﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Microsoft.VisualBasic;

namespace ECOOL_APP.com.ecool.util
{
    public class StringHelper
    {
        static public string SubStr(string a_SrcStr, int a_StartIndex, int a_Cnt)
        {
            Encoding l_Encoding = Encoding.GetEncoding("big5", new EncoderExceptionFallback(), new DecoderReplacementFallback(""));
            byte[] l_byte = l_Encoding.GetBytes(a_SrcStr);

            if (a_Cnt <= 0)
                return "";

            //例若長度10
            //若a_StartIndex傳入9 -> ok, 10 ->不行

            if (a_StartIndex + 1 > l_byte.Length)
                return "";
            else
            {
                //若a_StartIndex傳入9 , a_Cnt 傳入2 -> 不行 -> 改成 9,1
                if (a_StartIndex + a_Cnt > l_byte.Length)
                    a_Cnt = l_byte.Length - a_StartIndex;
            }

            return l_Encoding.GetString(l_byte, a_StartIndex, a_Cnt);
        }

        /// <summary>
        /// 過濾所有的HTML Tag
        /// </summary>
        /// <param name="text">要被過濾的文字</param>          
        /// <returns></returns>          
        static public string MakePlainText(string text)
        {
            //濾換行、空白
            text = Regex.Replace(text, "[\f\n\r\t\v]", "");
            text = Regex.Replace(text, " {2,}", " ");
            text = Regex.Replace(text, ">[ ]{1}", ">");
            text = Regex.Replace(text, "&nbsp;", "");

            //濾HTML Tag
            text = Regex.Replace(text, "<[^>]*>", "", RegexOptions.IgnoreCase);
            text = Regex.Replace(text, "<[^>]*", "", RegexOptions.IgnoreCase);

            // Regex regex = new System.Text.RegularExpressions.Regex(@"<(?!br|\/?p|b|\/?font|font color)[^>]*>");
            // text = regex.Replace(text, "");
            return text;
        }

        /// <summary>
        /// 取得左邊幾碼
        /// </summary>
        /// <param name="s"></param>
        /// <param name="length"></param>
        /// <returns></returns>
        public string StrLeft(string s, int length)
        {
            return s.Substring(0, length);
        }

        /// <summary>
        /// 取得右邊幾碼
        /// </summary>
        /// <param name="s"></param>
        /// <param name="length"></param>
        /// <returns></returns>
        public string StrRigth(string s, int length)
        {
            return s.Substring(s.Length - length);
        }

        public string StrMid(string s, int start, int length)
        {
            return s.Substring(start, length);
        }

        /// <summary>
        /// 顯示左邊幾碼 後面轉換「...」
        /// </summary>
        /// <param name="s"></param>
        /// <param name="length"></param>
        /// <param name="ReplaceStr"></param>
        /// <returns></returns>
        public static string LeftStringR(string s, int length, string ReplaceStr = "")
        {
            if (string.IsNullOrWhiteSpace(s))
            {
                return s;
            }

            if (s.Length <= length)
            {
                return s;
            }
            else
            {
                if (string.IsNullOrWhiteSpace(ReplaceStr))
                {
                    ReplaceStr = "...";
                }

                return new StringHelper().StrLeft(s, length) + ReplaceStr;
            }
        }

        public static string RightStringR(string s, int length, string ReplaceStr = "")
        {
            if (string.IsNullOrWhiteSpace(s))
            {
                return s;
            }

            if (s.Length <= length)
            {
                return s;
            }
            else
            {
                if (string.IsNullOrWhiteSpace(ReplaceStr))
                {
                    ReplaceStr = "...";
                }

                return ReplaceStr + new StringHelper().StrRigth(s, length);
            }
        }

        public static string UnHtml(string Temp)
        {
            return Regex.Replace(Temp, "(?is)<.+?>", "");
        }

        /// <summary>
        /// 數字轉國字數字
        /// </summary>
        /// <param name="number"></param>
        /// <returns></returns>
        public static string ConvertNumberToChineseNumber(int number)
        {
            string[] chineseNumber = { "零", "一", "二", "三", "四", "五", "六", "七", "八", "九" };
            string[] unit = { "", "十", "百", "千", "萬", "十萬", "百萬", "千萬", "億", "十億", "百億", "千億", "兆", "十兆", "百兆", "千兆" };
            StringBuilder ret = new StringBuilder();
            string inputNumber = number.ToString();
            int idx = inputNumber.Length;
            bool needAppendZero = false;
            foreach (char c in inputNumber)
            {
                idx--;
                if (c > '0')
                {
                    if (needAppendZero)
                    {
                        ret.Append(chineseNumber[0]);
                        needAppendZero = false;
                    }
                    ret.Append(chineseNumber[(int)(c - '0')] + unit[idx]);
                }
                else
                    needAppendZero = true;
            }
            return ret.Length == 0 ? chineseNumber[0] : ret.ToString();
        }

        /// <summary>
        /// 將國字數字轉換為阿拉伯數字 - 僅支援一億以下之數字; 若格式有誤, 可能出現 Exception
        /// </summary>
        /// <param name="cnum">請輸入正確格式之國字數字, 如 "八千四百二十一萬三千五百六十三"</param>
        public static int convChineseNumber(string cnum)
        {
            cnum = cnum.Replace("一十", "十");

            cnum = cnum.TrimEnd();
            char[] cnums = cnum.ToCharArray();
            int sum = 0, sectionUnit = 0, sectionsum = 0;
            foreach (char c in cnums)
            {
                int arab = mapCnumLetters(c);
                if (isMultiplier(c))
                {
                    if (isSegmentDelimeter(c)) // 萬/億
                    {
                        sectionsum = sum * arab;
                        sum = sectionsum;
                        if (sum < 0)
                            throw new Exception("輸入的字串無法解析!");
                        sectionsum = 0;
                    }
                    else // 十/百/千
                    {
                        if (sectionUnit == 0)
                            sectionsum = 10; // 特別處理 "十萬", "十一萬" 之類的狀況
                        else
                        {
                            sectionsum -= sectionUnit;
                            sum -= sectionUnit;
                            sectionsum = sectionUnit * arab;
                        }
                        sum += sectionsum;
                        if (sum < 0)
                            throw new Exception("輸入的字串無法解析!");
                    }
                }
                else
                {
                    sectionUnit = arab;
                    sum += arab;
                    if (sum < 0)
                        throw new Exception("輸入的字串無法解析!");
                    sectionsum += arab;
                }
            }
            return sum;
        }

        private static bool isSegmentDelimeter(char cnum)
        {
            switch (cnum)
            {
                case '萬':
                    return true;

                case '億':
                    return true;

                default:
                    return false;
            }
        }

        private static bool isMultiplier(char cnum)
        {
            switch (cnum)
            {
                case '十':
                    return true;

                case '拾':
                    return true;

                case '百':
                    return true;

                case '佰':
                    return true;

                case '千':
                    return true;

                case '仟':
                    return true;

                case '萬':
                    return true;

                case '億':
                    return true;

                default:
                    return false;
            }
        }

        private static int mapCnumLetters(char cnum)
        {
            switch (cnum)
            {
                case '零':
                    return 0;

                case '一':
                    return 1;

                case '壹':
                    return 1;

                case '二':
                    return 2;

                case '貳':
                    return 2;

                case '三':
                    return 3;

                case '參':
                    return 3;

                case '四':
                    return 4;

                case '肆':
                    return 4;

                case '五':
                    return 5;

                case '伍':
                    return 5;

                case '六':
                    return 6;

                case '陸':
                    return 6;

                case '七':
                    return 7;

                case '柒':
                    return 7;

                case '八':
                    return 8;

                case '捌':
                    return 8;

                case '九':
                    return 9;

                case '玖':
                    return 9;

                case '十':
                    return 10;

                case '拾':
                    return 10;

                case '廿':
                    return 20;

                case '丗':
                    return 30;

                case '百':
                    return 100;

                case '佰':
                    return 100;

                case '千':
                    return 1000;

                case '仟':
                    return 1000;

                case '萬':
                    return 10000;

                case '億':
                    return 100000000;

                default:
                    return 0;
            }
        }

        /// <summary>
        /// 姓名遮罩包含英文姓名 =>王O名
        /// </summary>
        /// <param name="val"></param>
        /// <returns></returns>
        public static string MaskName(string val)
        {
            if (!string.IsNullOrEmpty(val))
            {
                if (val.Length > 1)
                {
                    string maskstr, maskchar;
                    maskchar = null;
                    if (Regex.IsMatch(val, "[A-Za-z]"))
                    {
                        if (val.IndexOf("-") > 1)
                        {
                            maskstr = val.Split('-')[1];
                            val = val.Replace(maskstr, "*");
                        }
                        if (val.IndexOf(" ") > 1)
                        {
                            maskstr = val.Split(' ')[1];
                            val = val.Replace(maskstr, "*");
                        }
                    }
                    else
                    {
                        int End = (int)(val.Length / 2);
                        maskstr = val.Substring(1, End);
                        for (int i = 0; i < maskstr.Length; i++)
                        {
                            maskchar = maskchar + "O";
                        }
                        val = val.Replace(maskstr, maskchar);
                    }
                }
            }
            else
            {
                val = "";
            }

            return val;
        }
    }
}