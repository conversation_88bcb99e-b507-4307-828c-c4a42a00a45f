﻿@{
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
}

<div class="form-group">
    <a role="button" href='@Url.Action("Index", (string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys  @(ViewBag.NowAction == "Index" ? "active" : "")">
        藝廊說明
    </a>
    @if (user != null)
    {
        if (user.USER_TYPE == UserType.Student)
        {
            if (!AppMode)
            {

                <a role="button" href='@Url.Action("MenuIndex2", (string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys @(ViewBag.NowAction == "MenuIndex2" ? "active" : "")">
                    我要申請藝廊
                </a>
            }
            else
            {
                <a role="button" href='@Url.Action("Edit", (string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys  @(ViewBag.NowAction == "Edit" ? "active" : "")">
                        我要申請藝廊
                    </a>

            }

        }
        else if (user.USER_TYPE == UserType.Teacher || user.USER_TYPE == UserType.Admin && ViewBag.ISTASKLIST != "True")
        {
            if (!AppMode)
            {

            <a role="button" href='@Url.Action("MenuIndex", (string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys @(ViewBag.NowAction == "MenuIndex" ? "active" : "")">
                我要申請藝廊
            </a>
            }
            else
            {
            <a role="button" href='@Url.Action("Index2", (string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys @(ViewBag.NowAction == "Edit" ? "active" : "")">
                    批次線上藝廊
                </a>
            }
        }
    }
    @{ ZZZI34WorkIndexViewModel model = new ZZZI34WorkIndexViewModel();}
    <a role="button" href='@Url.Action("ArtGalleryList",(string)ViewBag.BRE_NO,new { model=model})' class="btn btn-sm btn-sys @(ViewBag.NowAction=="ArtGalleryList" ? "active":"")">
        全部藝廊
    </a>
    <a role="button" href='@Url.Action("ArtGalleryWorkList",(string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys @(ViewBag.NowAction=="ArtGalleryWorkList" ? "active":"")">
        所有作品
    </a>

    @if (user != null)
    {

        if (user.USER_TYPE == UserType.Student)
        {
            <a role="button" href='@Url.Action("ArtGalleryWorkList",(string)ViewBag.BRE_NO, new {WhereMyWork = true } )' class="btn btn-sm btn-sys @(ViewBag.NowAction=="MyWorkList" ? "active":"")">
                我的作品
            </a>
        }
        else if (user.USER_TYPE == UserType.Teacher || user.USER_TYPE == UserType.Admin && ViewBag.ISTASKLIST != "True")
        {
            <a role="button" href='@Url.Action("ArtGalleryList",(string)ViewBag.BRE_NO, new {WhereMyWork = true })' class="btn btn-sm btn-sys @(ViewBag.NowAction=="MyWorkList" ? "active":"")">
                我上傳的作品
            </a>
        }
    }
</div>