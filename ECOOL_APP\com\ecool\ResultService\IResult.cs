﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.com.ecool.ResultService
{
    public interface IResult
    {
        Exception Exception { get; set; }
        Guid ID { get; }
        object ModelItem { get; set; }
        List<IResult> InnerResults { get; }
        string Message { get; set; }
        bool Success { get; set; }
    }
}