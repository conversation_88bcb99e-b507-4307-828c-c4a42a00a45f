﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.com.ecool.Models.DTO
{
    public class APPT03Hrmt01ListViewModel
    {
        /// <summary>
        /// 學校
        /// </summary>
        [DisplayName("學校代碼")]
        public string SCHOOL_NO { get; set; }


        /// <summary>
        /// 學校
        /// </summary>
        [DisplayName("學校")]
        public string SHORT_NAME { get; set; }


        /// <summary>
        /// 系統角色
        /// </summary>
        [DisplayName("系統角色")]
        public string USER_TYPE { get; set; }


        /// <summary>
        ///  學號/帳號
        /// </summary>
        [DisplayName("學號/帳號")]
        public string USER_NO { get; set; }

        /// <summary>
        ///  學號/帳號
        /// </summary>
        [DisplayName("姓名")]
        public string NAME { get; set; }

        /// <summary>
        ///  班級
        /// </summary>
        [DisplayName("班級")]
        public string CLASS_NO { get; set; }


        /// <summary>
        ///  年級
        /// </summary>
        [DisplayName("年級")]
        public byte? GRADE { get; set; }

        /// <summary>
        ///  座號
        /// </summary>
        [DisplayName("座號")]
        public string SEAT_NO { get; set; }

        
    }
}
