﻿using Dapper;
using ECOOL_APP.com.ecool.util;
using ECOOL_APP.EF;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.Mvc;

namespace ECOOL_APP.com.ecool.service
{
    public class GAAT01Service
    {
        public List<GAAT01> GetGAAT01s(byte SYEAR, byte SEMESTER, ref ECOOL_DEVEntities db, DateTime? Date = null)
        {
            var Temp = db.GAAT01.Where(x => x.SYEAR == SYEAR && x.SEMESTER == SEMESTER);

            if (Date != null)
            {
                Temp = Temp.Where(x => x.ALARM_DATES <= Date);
            }

            Temp = Temp.OrderByDescending(x => x.ALARM_ID);

            return Temp.ToListNoLock();
        }

        /// <summary>
        /// 判斷這學期是否設定開學日
        /// </summary>
        /// <param name="SYEAR"></param>
        /// <param name="SEMESTER"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        public bool IsAlarmDateData(byte SYEAR, byte SEMESTER, ref ECOOL_DEVEntities db)
        {
            return db.GAAT01.Where(x => x.SYEAR == SYEAR && x.SEMESTER == SEMESTER).NoLock(x => x.Any());
        }

        /// <summary>
        /// 取得週期下拉式選單
        /// </summary>
        /// <param name="SYEAR"></param>
        /// <param name="SEMESTER"></param>
        /// <param name="db"></param>
        /// <param name="defaultSelectValue"></param>
        /// <returns></returns>
        public List<SelectListItem> GetAlarmCycleSelectItem(byte SYEAR, byte SEMESTER, ref ECOOL_DEVEntities db, string defaultSelectValue)
        {
            return db.GAAT01.Where(x => x.SYEAR == SYEAR && x.SEMESTER == SEMESTER).NoLock(x => x.AsEnumerable())
                  .Select(x => new SelectListItem
                  {
                      Text = DateHelper.ToSimpleTaiwanDate((DateTime)x.ALARM_DATES) + "~" + DateHelper.ToSimpleTaiwanDate((DateTime)x.ALARM_DATEE),
                      Value = x.ALARM_ID,
                      Selected = x.ALARM_ID == defaultSelectValue
                  })
                  .OrderBy(x => x.Value).ToList();
        }

        /// <summary>
        /// 取得設定年月下拉式選單
        /// </summary>
        /// <param name="db"></param>
        /// <param name="defaultSelectValue"></param>
        /// <returns></returns>
        public List<SelectListItem> GetYearMonthItem(ref ECOOL_DEVEntities db, string defaultSelectValue)
        {
            string sSQL = @" Select STUFF(CONVERT(VARCHAR(6), ALARM_DATES, 112), 1, 4,  YEAR(ALARM_DATES)-1911) as CYYMM,CONVERT(VARCHAR(6), ALARM_DATES, 112) YYMM
            from GAAT01 (nolock)
            group by STUFF(CONVERT(VARCHAR(6), ALARM_DATES, 112), 1, 4,  YEAR(ALARM_DATES)-1911),CONVERT(VARCHAR(6), ALARM_DATES, 112)";
            var temp = db.Database.Connection.Query<GAAI01YYMMViewModel>(sSQL).ToList();

            StringHelper stringHelper = new StringHelper();

            if (temp != null)
            {
                return temp.Select(x => new SelectListItem
                {
                    Text = stringHelper.StrLeft(x.CYYMM.ToString(), 3) + "年" + stringHelper.StrRigth(x.CYYMM.ToString(), 2) + "月",
                    Value = x.YYMM.ToString(),
                    Selected = x.YYMM.ToString() == defaultSelectValue
                }).OrderBy(x => x.Value).ToList();
            }

            return null;
        }
        public List<string> GetYearItem(ref ECOOL_DEVEntities db, string defaultSelectValue)
        {
            string sSQL = @" 	select distinct Year FROM   WeekSet ORDER BY Year DESC";
            var temp = db.Database.Connection.Query<string>(sSQL).ToList();
            List<string> YearTemp = new List<string>();
            if (temp != null)
            {
                YearTemp.AddRange(temp);

            }
            return YearTemp;
       }
            public List<SelectListItem> GetYear(ref ECOOL_DEVEntities db, string defaultSelectValue) {
            List<SelectListItem> Yearitems = new List<SelectListItem>();
            string sSQL = @" 	select distinct Year FROM   WeekSet ";
            var temp = db.Database.Connection.Query<string>(sSQL).ToList();
            List<SelectListItem> TempYear = new List<SelectListItem>();
            TempYear.Add(new SelectListItem() { Text = "請選擇..", Value = "", Selected = string.IsNullOrWhiteSpace(defaultSelectValue) });
            if (temp != null)
            {
                List<SelectListItem> tempy = new List<SelectListItem>();
                tempy = temp.Select(x => new SelectListItem
                {
                    Text = x,
                    Value = x,
                    Selected = x.ToString() == defaultSelectValue
                }).OrderBy(x => x.Value).ToList();
                TempYear.AddRange(tempy);
                return TempYear;
            }
            return null;
        }
        public List<SelectListItem> GetWeekItem(ref ECOOL_DEVEntities db, string defaultSelectValue, string Year)
        { 
            
            //取出學年度及學期
            int SYear;
            int Semesters;
            SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);
            GAAT01 gAAT = new GAAT01();
            gAAT = db.GAAT01.Where(x => x.SYEAR == SYear && x.SEMESTER == Semesters).OrderBy(x=>x.ALARM_DATES).FirstOrDefault();
            string sSQL = @" 	select weekNO ,    convert(varchar,weekStartDate ,111)+'-'+convert(varchar,weekEtartDate ,111)  as YYMMDD ,   ROW_NUMBER() OVER (ORDER BY weekStartDate) AS RowNum  FROM   WeekSet where Year='" + Year+ "' and  weekStartDate>= CAST('" + gAAT.ALARM_DATES.Value.ToShortDateString()+ "' as datetime)   ";
            sSQL = @"WITH NumberedWeeks AS (" + sSQL + " ) SELECT   YYMMDD,  weekNO,RowNum FROM NumberedWeeks ORDER BY RowNum";


            var temp = db.Database.Connection.Query<GAAI01WeekViewModel>(sSQL).ToList();

            StringHelper stringHelper = new StringHelper();

            if (temp != null)
            {
                return temp.Select(x => new SelectListItem
                {
                    Text = "第"+x.RowNum.ToString() + "週" +"( "+x.YYMMDD.ToString()+" )",
                    Value = x.weekNO.ToString(),
                    Selected = x.weekNO.ToString() == defaultSelectValue
                }).OrderBy(x => x.Value).ToList();
            }

            return null;
        }

        /// <summary>
        /// 取得有設定(GAAT01)的學年度+學期
        /// </summary>
        /// <param name="db"></param>
        /// <param name="defaultSelectValue"></param>
        /// <returns></returns>
        public List<SelectListItem> GetAlarmSyearSemester(ref ECOOL_DEVEntities db, string defaultSelectValue)
        {
            var Temp = db.GAAT01.Select(a => new { a.SYEAR, a.SEMESTER }).Distinct().OrderByDescending(a => a.SYEAR).ThenByDescending(a => a.SEMESTER).NoLock(x => x.AsEnumerable());

            if (Temp != null)
            {
                return Temp.Select(x => new SelectListItem
                {
                    Text = x.SYEAR.ToString() + "年度" + HRMT01.ParserSemester(x.SEMESTER),
                    Value = x.SYEAR.ToString() + "_" + x.SEMESTER,
                    Selected = x.SYEAR.ToString() + "_" + x.SEMESTER == defaultSelectValue
                })
                        .OrderBy(x => x.Value).ToList();
            }

            return null;
        }

        /// <summary>
        /// 取得這個日期的週期ID
        /// </summary>
        /// <param name="SYEAR"></param>
        /// <param name="SEMESTER"></param>
        /// <param name="db"></param>
        /// <param name="date"></param>
        /// <returns></returns>
        public string GetCycleforID(byte SYEAR, byte SEMESTER, ref ECOOL_DEVEntities db, DateTime date)
        {
            return db.GAAT01.Where(x => x.SYEAR == SYEAR && x.SEMESTER == SEMESTER && x.ALARM_DATES <= date && x.ALARM_DATEE >= date).OrderByDescending(x => x.ALARM_ID).Select(x => x.ALARM_ID).NoLock(x => x.FirstOrDefault());
        }

        /// <summary>
        /// 取得這個週期的時間
        /// </summary>
        /// <param name="ALARM_ID"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        public string GetCycleforData(string ALARM_ID, ref ECOOL_DEVEntities db)
        {
            var gAAt01 = db.GAAT01.Where(x => x.ALARM_ID == ALARM_ID).NoLock(x => x.FirstOrDefault());

            if (gAAt01 != null)
            {
                return gAAt01.ALARM_DATES.Value.ToString("yyyy/MM/dd") + "~" + gAAt01.ALARM_DATEE.Value.ToString("yyyy/MM/dd");
            }
            else
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// 取得這個id 資料
        /// </summary>
        /// <param name="ALARM_ID"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        public GAAT01 GetGAAt01forALARM_ID(string ALARM_ID, ref ECOOL_DEVEntities db)
        {
            return db.GAAT01.Where(x => x.ALARM_ID == ALARM_ID).NoLock(x => x.FirstOrDefault());
        }

        /// <summary>
        /// 取得這學期此班人數及班導資料
        /// </summary>
        /// <param name="SCHOOL_NO"></param>
        /// <param name="SYEAR"></param>
        /// <param name="SEMESTER"></param>
        /// <param name="CLASS_NO"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        public GAAI01SearchClassMainViewModel GetClassMain(string SCHOOL_NO, byte SYEAR, byte SEMESTER, string CLASS_NO, ref ECOOL_DEVEntities db)
        {
            return (from a in db.GAAT01_C
                    join b in db.BDMT01 on a.SCHOOL_NO equals b.SCHOOL_NO
                    where a.SCHOOL_NO == SCHOOL_NO && a.SYEAR == SYEAR && a.SEMESTER == SEMESTER && a.CLASS_NO == CLASS_NO
                    select new GAAI01SearchClassMainViewModel()
                    {
                        SCHOOL_NAME = b.SCHOOL_NAME,
                        GRADE = a.GRADE,
                        CLASS_NO = a.CLASS_NO,
                        STUDENT_NUMBER = a.STUDENT_NUMBER,
                        TEACHER_NAME = a.TEACHER_NAME
                    }).NoLock(x => x.FirstOrDefault());
        }
    }
}