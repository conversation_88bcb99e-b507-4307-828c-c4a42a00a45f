//BS4設定
$font-family-sans-serif: "微軟正黑體",
// Safari for OS X and iOS (San Francisco)
-apple-system,
// Chrome < 56 for OS X (San Francisco)
BlinkMacSystemFont,
// Windows
"Segoe UI",
// Android
"Roboto",
// Basic web fallback
"Helvetica Neue",
Arial,
sans-serif,
// Emoji fonts
"Apple Color Emoji",
"Segoe UI Emoji",
"Segoe UI Symbol";
$font-size-base: 0.825rem;
@import "newsite/bs4-custom";

//符號icon載入設定
// $fa-font-path: "../fonts";
// @import "font-awesome/font-awesome";

//輸入下拉套件
$color-red-error: rgb(185, 74, 72);
$color-green-success: #28a745;
$color-grey-arrow: rgba(204, 204, 204, 0.2);
$width-default: 220px;
$zindex-select-dropdown: 1060;
$input-color-placeholder: #999;
$input-alt-color-placeholder: rgba(255, 255, 255, 0.5);
$input-padding-y-sm: .25rem;
$input-padding-x-sm: .5rem;
$input-padding-y-lg: 0.5rem;
$input-padding-x-lg: 1rem;
@import "newsite/bootstrap-select";

//客製化
$menuColors:(default:(class: "default",
    bgcolor: #f8fbf3,
    btncolor: #e5f4c3,
    shadowcolor:#daf0ab,
    bordercolor:#e5f4c3,
    txtcolor:#000,
    feather:0,
    imgurl: "data:image/svg+xml,%3Csvg id='Layer_1' enable-background='new 0 0 50 50' height='512' viewBox='0 0 50 50' width='512' xmlns='http://www.w3.org/2000/svg'%3E%3Cg%3E%3Cg%3E%3Cg%3E%3Cg%3E%3Cg%3E%3Cg enable-background='new'%3E%3Cg%3E%3Cpath d='m2.20071 15.9276 24.1803-14.0521-1.1547-.6667-24.1803 14.0521z' fill='%23fe91ab'/%3E%3C/g%3E%3Cg%3E%3Cpath d='m28.26361 22.8806c-1.066-.6163-1.9302-2.1143-1.9259-3.3369l.0373-17.6641-8.2262 4.7801-15.9482 9.2669-.0435 17.6678c-.0021 1.2242.862 2.7227 1.9281 3.3369.5363.3108 1.0227.3387 1.3744.1349l24.1806-14.0529c-.3518.2039-.8403.176-1.3766-.1327z' fill='%23ed5d71'/%3E%3C/g%3E%3Cg%3E%3Cg%3E%3Cpath d='m3.16611 16.5965 3.2655 1.8847.2381.1377v18.7212c-.3153-.0188-.6635-.1266-1.0306-.3383-1.3874-.8033-2.5143-2.7532-2.51-4.3468z' fill='%23e3e7f0'/%3E%3C/g%3E%3C/g%3E%3Cg%3E%3Cpath d='m31.40261 4.7749-.0434 17.6674c-.0044.9706-.3474 1.6566-.90331 1.9779l-24.18049 14.0528c.5558-.3236.8988-1.0075.9032-1.9802l.0434-17.6674z' fill='%23ed5d71'/%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/g%3E%3Cg%3E%3Cpath d='m6.27541 18.3917 22.7005-13.1921-3.1093-1.7952-22.7005 13.1921z' fill='%23fff'/%3E%3C/g%3E%3Cg%3E%3Cpath d='m1.04601 15.2609-.0439 17.6671c-.0056 1.9561 1.3749 4.3479 3.0778 5.3311 1.7028.9831 3.0924.1906 3.0979-1.7655l.044-17.6671-1.1548-.6667-.0439 17.6671c-.0035 1.2245-.8733 1.7208-1.9394 1.1053-1.0662-.6156-1.9304-2.113-1.9269-3.3375l.0439-17.6671z' fill='%23d3374e'/%3E%3C/g%3E%3Cg%3E%3Cpath d='m7.22181 18.8265 24.1802-14.0521-1.1547-.6667-24.1803 14.0521z' fill='%23fe91ab'/%3E%3C/g%3E%3C/g%3E%3Cpath d='m31.40871 1.8773-5.7494 3.3416-2.4829 4.3361 8.2262-4.7801z' fill='%23ed5d71'/%3E%3Cpath d='m25.66001 5.2189-2.4827 4.3355-1.1548-.6667 2.4828-4.3354z' fill='%23d3374e'/%3E%3Cg%3E%3Cpath d='m25.66001 5.219 5.7486-3.3408-1.1547-.6667-5.7486 3.3408z' fill='%23fe91ab'/%3E%3C/g%3E%3C/g%3E%3Cg%3E%3Cg%3E%3Cg%3E%3Cg%3E%3Cg enable-background='new'%3E%3Cg%3E%3Cpath d='m10.99531 20.9825 24.1803-14.0521-1.1547-.6667-24.1803 14.0521z' fill='%23ffdd94'/%3E%3C/g%3E%3Cg%3E%3Cpath d='m37.05821 27.9355c-1.066-.6162-1.9302-2.1143-1.9259-3.3368l.0373-17.6642-8.2262 4.7802-15.9482 9.2669-.0434 17.6678c-.0022 1.2241.862 2.7227 1.928 3.3368.5363.3108 1.0227.3387 1.3744.1349l24.1806-14.0528c-.3517.2038-.8403.1759-1.3766-.1328z' fill='%23febb61'/%3E%3C/g%3E%3Cg%3E%3Cg%3E%3Cpath d='m11.96071 21.6514 3.2655 1.8847.2381.1377v18.7212c-.3153-.0187-.6635-.1266-1.0306-.3383-1.3874-.8033-2.5143-2.7531-2.51-4.3468z' fill='%23e3e7f0'/%3E%3C/g%3E%3C/g%3E%3Cg%3E%3Cpath d='m40.19721 9.8298-.0434 17.6674c-.0044.9706-.3474 1.6566-.9033 1.978l-24.1805 14.0527c.5558-.3235.8988-1.0074.9032-1.9802l.0434-17.6674z' fill='%23febb61'/%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/g%3E%3Cg%3E%3Cpath d='m15.07001 23.4466 22.7005-13.1921-3.1093-1.7952-22.7005 13.1921z' fill='%23fff'/%3E%3C/g%3E%3Cg%3E%3Cpath d='m9.84061 20.3158-.0439 17.6671c-.0056 1.9561 1.3749 4.3479 3.0778 5.3311 1.7029.9831 3.0924.1906 3.098-1.7655l.0439-17.6671-1.1548-.6667-.0439 17.6671c-.0035 1.2245-.8733 1.7208-1.9394 1.1053-1.0662-.6155-1.9304-2.113-1.9269-3.3375l.0439-17.6671z' fill='%23efa143'/%3E%3C/g%3E%3Cg%3E%3Cpath d='m16.01641 23.8814 24.1803-14.0521-1.1548-.6667-24.1803 14.0521z' fill='%23ffdd94'/%3E%3C/g%3E%3C/g%3E%3Cpath d='m40.20331 6.9322-5.7494 3.3417-2.4829 4.336 8.2262-4.7801z' fill='%23febb61'/%3E%3Cpath d='m34.45461 10.2739-2.4827 4.3354-1.1548-.6666 2.4828-4.3355z' fill='%23efa143'/%3E%3Cg%3E%3Cpath d='m34.45461 10.2739 5.7486-3.3407-1.1547-.6667-5.7486 3.3407z' fill='%23ffdd94'/%3E%3C/g%3E%3C/g%3E%3Cg%3E%3Cg%3E%3Cg%3E%3Cg%3E%3Cg enable-background='new'%3E%3Cg%3E%3Cpath d='m19.79001 26.0374 24.1802-14.052-1.1547-.6667-24.1803 14.052z' fill='%237bb1ff'/%3E%3C/g%3E%3Cg%3E%3Cpath d='m45.85281 32.9905c-1.066-.6163-1.9302-2.1144-1.9258-3.3369l.0372-17.6641-8.22621 4.7801-15.9482 9.2669-.0434 17.6678c-.0022 1.2241.862 2.7227 1.928 3.3369.5363.3107 1.0227.3387 1.3744.1348l24.1806-14.0528c-.35169.2038-.84029.1759-1.37659-.1327z' fill='%235793fb'/%3E%3C/g%3E%3Cg%3E%3Cg%3E%3Cpath d='m20.75531 26.7064 3.2655 1.8846.2381.1378v18.7212c-.3153-.0188-.6635-.1266-1.0306-.3383-1.3874-.8033-2.5143-2.7532-2.5099-4.3469z' fill='%23e3e7f0'/%3E%3C/g%3E%3C/g%3E%3Cg%3E%3Cpath d='m48.99181 14.8847-.0434 17.6674c-.0043.9706-.3474 1.6567-.90319 1.978l-24.1806 14.0528c.5558-.3236.8989-1.0075.9032-1.9802l.0434-17.6674z' fill='%235793fb'/%3E%3Cg fill='%23e3e7f0'%3E%3Cpath d='m41.17881 34.5208 5.8875-3.4211s-.0037 1.1592-.0028 1.2319l-5.89029 3.4235z'/%3E%3Cpath d='m41.17881 32.1931 5.8875-3.4212s-.0037 1.1592-.0028 1.232l-5.89029 3.4235z'/%3E%3Cpath d='m41.17881 29.8654 5.8875-3.4212s-.0037 1.1592-.0028 1.232l-5.89029 3.4235z'/%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/g%3E%3Cg%3E%3Cpath d='m23.86461 28.5015 22.7005-13.1921-3.1093-1.7951-22.7005 13.1921z' fill='%23fff'/%3E%3C/g%3E%3Cg%3E%3Cpath d='m18.63521 25.3707-.0439 17.6672c-.0056 1.956 1.375 4.3479 3.0778 5.331 1.7029.9832 3.0924.1906 3.098-1.7654l.0439-17.6672-1.1547-.6667-.044 17.6672c-.0034 1.2244-.8733 1.7207-1.9394 1.1052s-1.9303-2.113-1.9269-3.3374l.044-17.6672z' fill='%233a53d0'/%3E%3C/g%3E%3Cg%3E%3Cpath d='m24.81101 28.9363 24.1803-14.052-1.1548-.6667-24.1802 14.052z' fill='%237bb1ff'/%3E%3C/g%3E%3C/g%3E%3Cpath d='m48.99791 11.9872-5.7494 3.3416-2.4829 4.3361 8.2262-4.7802z' fill='%235793fb'/%3E%3Cpath d='m43.24931 15.3288-2.4828 4.3354-1.1547-.6666 2.4827-4.3354z' fill='%233a53d0'/%3E%3Cg%3E%3Cpath d='m43.24921 15.3288 5.7486-3.3407-1.1547-.6667-5.7486 3.3407z' fill='%237bb1ff'/%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E"
  ),
  secretary:(class: "secretary",
    bgcolor: #f6e1e1,
    btncolor: #f7c9c9,
    shadowcolor:#f6a5a5,
    bordercolor:#f3caca,
    txtcolor:#000,
    feather:0,
    imgurl: "../images/menu-img-secretary.png"
  ),
  bonus:(class: "bonus",
    bgcolor: #ffe527,
    btncolor: #edef5e,
    shadowcolor:rgba(255, 255, 255, 0.5),
    bordercolor:#ffe527,
    txtcolor:#000,
    feather:.4rem,
    imgurl: "../images/menu-img-bonus.png"
  ),
  market:(class: "market",
    bgcolor: #b4f0f6,
    btncolor: #66c6f3,
    shadowcolor:rgba(255, 255, 255, 0.5),
    bordercolor:#a0e1ff,
    txtcolor:#000,
    feather:.4rem,
    imgurl: "../images/menu-img-market.png"
  ),
  leaderboard:(class: "leaderboard",
    bgcolor: #efff71,
    btncolor: #d4f385,
    shadowcolor:rgba(255, 255, 255, 0.5),
    bordercolor:#d4f385,
    txtcolor:#000,
    feather:.4rem,
    imgurl: "../images/menu-img-leaderboard.png"
  ),
  passport:(class: "passport",
    bgcolor: #f8fbf3,
    btncolor: #61fad9,
    shadowcolor:#05c79d,
    bordercolor:#61fad9,
    txtcolor:#000,
    feather:0,
    imgurl: "../images/menu-img-passport.png"
  ),
  entity:(class: "entity",
    bgcolor: #f7f1e5,
    btncolor: #eadcbe,
    shadowcolor:#dcbc78,
    bordercolor:#eadcbe,
    txtcolor:#000,
    feather:0,
    imgurl: "../images/menu-img-entity.png"
  ),
  setting:(class: "setting",
    bgcolor: #f2f2ff,
    btncolor: #d2d2ff,
    shadowcolor:rgba(255, 255, 255, 0.8),
    bordercolor:#cacaf3,
    txtcolor:#344474,
    feather:.4rem,
    imgurl: "../images/menu-img-setting.png"
  ), member:(class: "member",
    bgcolor: #f8fbf3,
    btncolor: #e5f4c3,
    shadowcolor:#daf0ab,
    bordercolor:#e5f4c3,
    txtcolor:#4a7910,
    feather:0,
    imgurl: "../images/menu-img-member.png"
  ),
);
$ribbonColors:(default:(class:"default",
    bgcolor: #ebf1ff,
    txtcolor:#0036bf),
  ecool:(class:"ecool",
    bgcolor: #ccffff,
    txtcolor:#0036bf),
  news:(class:"news",
    bgcolor: #eced33,
    txtcolor:#0036bf),
  task:(class:"task",
    bgcolor: #adfbf2,
    txtcolor:#0036bf),
  readLeaderboard:(class:"readLeaderboard",
    bgcolor: #e2eaa2,
    txtcolor:#0036bf),
  read-books:(class:"read-books",
    bgcolor: #fceec1,
    txtcolor:#0036bf),
  carousel:(class:"carousel",
    bgcolor: #e2e1fe,
    txtcolor:#0036bf),
  articleLeaderboard:(class:"articleLeaderboard",
    bgcolor: #e8d1e6,
    txtcolor:#0036bf),
  hot-prize:(class:"hot-prize",
    bgcolor: #f3c8b0,
    txtcolor:#0036bf),
  coinLeaderboard:(class:"coinLeaderboard",
    bgcolor: #fde680,
    txtcolor:#0036bf),
);

@import "newsite/layout";
@import "newsite/typography";
@import "newsite/button";
@import "newsite/read-certification";
@import "newsite/read-books";
@import "newsite/sport-banker";
@import "newsite/carousel";