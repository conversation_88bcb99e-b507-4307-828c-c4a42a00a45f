﻿using com.ecool.sqlConnection;
using ECOOL_APP.com.ecool.Models.DTO;
using ECOOL_APP.EF;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.com.ecool.service
{
    public class ADDI03Service
    {
        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();

        #region 取得閱讀護照書單閱讀情況一覽表 PRINT

        public List<ADDI03PrintViewModel> GetListData(string SCHOOL_NO, int GRADE, string Class_No, string whereKeyword)
        {
            List<ADDI03PrintViewModel> list_data = new List<ADDI03PrintViewModel>();

            ADDI03PrintViewModel ReturnData = null;

            StringBuilder sSQL = new StringBuilder();
            StringBuilder sb = new StringBuilder();
            DataTable dt;
            DataTable dt2;
            try
            {
                sqlConnection SQLHelper = new sqlConnection();

                sSQL.AppendFormat(" select a.* from ADDT03 a (nolock) where a.SCHOOL_NO = '{0}' and a.GRA<PERSON>={1} order by a.BOOK_ID", SCHOOL_NO, GRADE);
                dt = new sqlConnection().executeQueryByDataTableList(sSQL.ToString());

                if (dt != null)
                {
                    sb.Append(" SELECT A.SCHOOL_NO,A.USER_NO,A.GRADE,A.CLASS_NO,A.SEAT_NO,A.NAME,A.SNAME,C.PASS_DATE ");

                    foreach (DataRow drB in dt.Rows)
                    {
                        sb.AppendFormat("  ,SUM(Case When b.BOOK_ID = '{0}' THEN 1 ELSE 0 END) AS [{0}] ", (string)drB["BOOK_ID"]);
                        sb.AppendFormat("  ,SUM(Case When b.BOOK_NAME = '{0}' THEN 1 ELSE 0 END) AS [{1}] ", SQLHelper.SQLString((string)drB["BOOK_NAME"]), (string)drB["BOOK_NAME"]);
                    }

                    sb.Append(" FROM HRMT01 A  (nolock)  ");
                    sb.AppendFormat(" LEFT OUTER JOIN ADDT05 B   (nolock)  ON A.SCHOOL_NO = B.SCHOOL_NO AND A.USER_NO = B.USER_NO AND B.GRADE = '{0}' ", GRADE);
                    sb.Append(" LEFT OUTER JOIN ADDT04 C  (nolock) ON A.SCHOOL_NO = C.SCHOOL_NO AND A.USER_NO = C.USER_NO AND B.GRADE = C.GRADE ");
                    sb.AppendFormat("  WHERE A.SCHOOL_NO = '{0}'  ", SCHOOL_NO);
                    sb.AppendFormat("  AND A.USER_TYPE = '{0}'  ", UserType.Student);
                    sb.AppendFormat("  AND A.USER_STATUS <> '{0}'  ", UserStaus.Invalid);

                    if (string.IsNullOrWhiteSpace(Class_No) == false && Class_No != "All")
                    {
                        sb.AppendFormat("  AND A.CLASS_NO = '{0}' ", Class_No);
                    }

                    if (string.IsNullOrWhiteSpace(whereKeyword) == false)
                    {
                        sb.AppendFormat("  AND (A.USER_NO like '%{0}%' or A.NAME like '%{0}%' ) ", whereKeyword);
                    }

                    sb.Append(" GROUP BY A.SCHOOL_NO,A.USER_NO,A.GRADE,A.CLASS_NO,A.SEAT_NO,A.NAME,A.SNAME,C.PASS_DATE ");
                    sb.Append(" ORDER BY A.GRADE,A.CLASS_NO,A.SEAT_NO");

                    dt2 = SQLHelper.executeQueryByDataTableList(sb.ToString());

                    if (dt2 != null)
                    {
                        foreach (DataRow dr in dt2.Rows)
                        {
                            ReturnData = new ADDI03PrintViewModel();
                            ReturnData.SCHOOL_NO = (dr["SCHOOL_NO"] == DBNull.Value ? "" : (string)dr["SCHOOL_NO"]);
                            ReturnData.GRADE = (dr["GRADE"] == DBNull.Value ? (short?)null : Convert.ToInt16(dr["GRADE"]));
                            ReturnData.CLASS_NO = (dr["CLASS_NO"] == DBNull.Value ? "" : (string)dr["CLASS_NO"]);
                            ReturnData.USER_NO = (dr["USER_NO"] == DBNull.Value ? "" : (string)dr["USER_NO"]);
                            ReturnData.NAME = (dr["NAME"] == DBNull.Value ? "" : (string)dr["NAME"]);
                            ReturnData.SNAME = (dr["SNAME"] == DBNull.Value ? "" : (string)dr["SNAME"]);
                            ReturnData.SEAT_NO = (dr["SEAT_NO"] == DBNull.Value ? "" : (string)dr["SEAT_NO"]);
                            ReturnData.PASS_DATE = (dr["PASS_DATE"] == DBNull.Value ? (DateTime?)null : (DateTime)dr["PASS_DATE"]);

                            Dictionary<string, int> IsReadBooK = new Dictionary<string, int>();
                            Dictionary<string, int> IsReadBooKName = new Dictionary<string, int>();

                            foreach (DataRow drB in dt.Rows)
                            {
                                string BOOK_ID = (string)drB["BOOK_ID"];
                                string BOOK_NAME = (string)drB["BOOK_NAME"];

                                int BookCount = (int)dr[BOOK_ID];
                                int BookNameCount = (int)dr[BOOK_NAME];
                                IsReadBooK.Add(BOOK_ID, BookCount);
                                IsReadBooKName.Add(BOOK_NAME, BookNameCount);
                            }

                            ReturnData.IsReadBooK = IsReadBooK;
                            ReturnData.IsReadBooKName = IsReadBooKName;

                            list_data.Add(ReturnData);
                        }
                    }

                    dt2.Clear();
                    dt2.Dispose();
                }

                dt.Clear();
                dt.Dispose();
            }
            catch (Exception exception)
            {
                throw exception;
            }
            return list_data;
        }

        #endregion 取得閱讀護照書單閱讀情況一覽表 PRINT

        /// <summary>
        /// 判斷此書是否閱讀過，閱讀是否閱讀護照，讀過但閱讀護照，此次只能讀閱讀護照
        /// </summary>
        /// <param name="SCHOOL_NO"></param>
        /// <param name="USER_NO"></param>
        /// <param name="BOOK_NAME"></param>
        /// <param name="APPLY_TYPE">申請方式 0:學生線上申請, 1:老師代申請 2:批次上傳</param>
        /// <returns>true 已閱讀 、false 未閱讀</returns>
        public bool isReadBooK(string SCHOOL_NO, string USER_NO, string BOOK_NAME, int APPLY_TYPE, out string StatusMemo)
        {
            bool ReturnBool = false;
            StatusMemo = string.Empty;

            //判斷 ADDT06小小讀書人申請閱讀認證是否有資料
            var ListT06 = db.ADDT06.Where(p => p.USER_NO == USER_NO && p.SCHOOL_NO == SCHOOL_NO && p.BOOK_NAME == BOOK_NAME && p.APPLY_STATUS != ADDT06.APPLY_STATUS_Val.APPLY_TYPE_DEL　&& p.BOOK_NAME!= "書名請見閱讀單");
            if (ListT06 == null || ListT06.Count() == 0)
            {
                //判斷 學生是否通過閱讀護照 for (舊資料)
                ReturnBool = isADDT05(SCHOOL_NO, USER_NO, BOOK_NAME);
            }
            else // 此書 ADDT06 有資料了
            {
                ADDT06 a6 = ListT06.First();
                if (a6.APPLY_STATUS == ADDT06.APPLY_STATUS_Val.APPLY_TYPE_WithVerify)
                {
                    StatusMemo = "(待審核)";
                }
                //判斷此書在 小小讀書人申請閱讀認證 是否申請閱讀護照
                ReturnBool = ListT06.Where(a => a.PASSPORT_YN == "Y").Any();
                if (ReturnBool == false) //沒有
                {
                    //判斷 學生是否通過閱讀護照 for (舊資料)
                    ReturnBool = isADDT05(SCHOOL_NO, USER_NO, BOOK_NAME);
                    if (ReturnBool == false)
                    {
                        if (APPLY_TYPE == ADDT06.APPLY_TYPE_Val.APPLY_TYPE_Student) //學生線上申請
                        {
                            ReturnBool = true; //學生重覆申請
                        }
                        else
                        {
                            if (isADDT03(SCHOOL_NO, BOOK_NAME) == false) //此書是否有閱讀護照設定 ，沒有
                            {
                                ReturnBool = true; //重覆申請
                            }
                        }
                    }
                }
            }

            return ReturnBool;
        }

        /// <summary>
        /// 是否 ADDT03閱讀護照書籍設定
        /// </summary>
        /// <param name="SCHOOL_NO"></param>
        /// <param name="BOOK_NAME"></param>
        /// <returns></returns>
        public bool isADDT03(string SCHOOL_NO, string BOOK_NAME)
        {
            return db.ADDT03.Where(A => A.SCHOOL_NO == SCHOOL_NO && A.BOOK_NAME == BOOK_NAME).Any();
        }

        /// <summary>
        /// 學生是否通過閱讀護照  PS.ADDT05學生閱讀護照紀錄表
        /// </summary>
        /// <param name="SCHOOL_NO"></param>
        /// <param name="USER_NO"></param>
        /// <param name="BOOK_NAME"></param>
        /// <returns></returns>
        private bool isADDT05(string SCHOOL_NO, string USER_NO, string BOOK_NAME)
        {
            return db.ADDT05.Where(A => A.SCHOOL_NO == SCHOOL_NO && A.USER_NO == USER_NO && A.BOOK_NAME == BOOK_NAME && A.BOOK_NAME != "書名請見閱讀單").Any();
        }
    }
}