﻿using System;
using System.Collections.Generic;
using System.Linq;
using ECOOL_APP.com.ecool.LogicCenter.RandomGame;
using ECOOL_APP.EF;
using Microsoft.VisualStudio.TestTools.UnitTesting;

namespace EcoolUnitTest
{
    [TestClass]
    public class TestRandomGame
    {
        ECOOL_DEVEntities db = new ECOOL_DEVEntities();
        List<HRMT01> gamers;

        public TestRandomGame()
        {
            gamers = db.HRMT01
                .Where(h => (h.SCHOOL_NO == "403605"
                && (h.CLASS_NO == "401"))
                && h.USER_STATUS == 1
                && h.USER_TYPE == UserType.Student).ToList();
        }

        string GroupingBy(HRMT01 h1)
        {
            return !string.IsNullOrEmpty(h1.CLASS_NO) ?
                      h1.SCHOOL_NO + "_" + h1.CLASS_NO :
                      h1.SCHOOL_NO + "_" + h1.USER_TYPE;
        }

        [TestMethod]
        public void Test_HRMT01_Draw()
        {
            RandomGame<HRMT01> rg = new RandomGame<HRMT01>(gamers,
                (h1) => { return GroupingBy(h1); });

            int drawCount = 100;
            int expected = drawCount;
            var result = rg.LuckyDraw(drawCount);

            if (gamers.Count < drawCount)
                expected = gamers.Count;

            Assert.AreEqual(expected, result.Count);

            int row = 0;
            result.ForEach((h) => {
                System.Diagnostics.Debug.WriteLine(++row + ".  Group: " + GroupingBy(h) + ", USER_NO: " + h.USER_NO);
            });
        }
    }
}
