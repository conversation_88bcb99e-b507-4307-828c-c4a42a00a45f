/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/General/Regular/Fraktur.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.STIXGeneral,{120068:[695,22,785,47,742],120069:[704,24,822,48,774],120071:[695,24,868,50,817],120072:[695,24,729,50,678],120073:[695,204,767,50,716],120074:[695,24,806,50,755],120077:[695,204,772,50,721],120078:[695,22,846,50,801],120079:[695,24,669,47,626],120080:[695,22,1083,50,1031],120081:[695,22,827,50,775],120082:[695,24,837,37,786],120083:[695,204,823,40,773],120084:[695,64,865,37,814],120086:[695,24,856,55,801],120087:[695,24,766,47,722],120088:[696,22,787,50,744],120089:[695,24,831,48,781],120090:[695,24,1075,48,1025],120091:[695,31,763,46,735],120092:[695,204,766,47,714],120094:[468,18,530,51,479],120095:[695,18,513,46,462],120096:[468,18,385,57,344],120097:[695,18,506,45,455],120098:[468,18,420,47,379],120099:[694,209,327,27,316],120100:[468,209,499,51,461],120101:[695,209,528,48,476],120102:[694,18,384,42,338],120103:[695,209,345,44,311],120104:[695,18,420,48,368],120105:[695,18,398,46,350],120106:[468,25,910,59,856],120107:[468,25,636,60,582],120108:[468,18,503,50,452],120109:[586,209,555,38,504],120110:[468,209,507,51,459],120111:[468,18,463,38,426],120112:[623,24,518,49,469],120113:[656,18,374,38,337],120114:[478,18,647,60,593],120115:[586,18,515,47,464],120116:[586,25,759,41,708],120117:[468,189,456,45,406],120118:[586,209,516,48,464],120119:[468,209,457,43,407]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/Regular/Fraktur.js");
