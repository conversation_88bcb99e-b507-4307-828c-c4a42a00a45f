/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX-Web/fontdata-extra.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

(function(aj){var B="2.7.5";var t=aj.FONTDATA.DELIMITERS;var s="H",f="V";var ag=[8722,E,0,0,0,-0.26,-0.26];var u="STIXMathJax_Alphabets-bold-italic",G="STIXMathJax_Alphabets-bold",z="STIXMathJax_Alphabets-italic",e="STIXMathJax_Alphabets",R="STIXMathJax_Arrows-bold",x="STIXMathJax_Arrows",g="STIXMathJax_DoubleStruck-bold-italic",i="STIXMathJax_DoubleStruck-bold",c="STIXMathJax_DoubleStruck-italic",b="STIXMathJax_DoubleStruck",q="STIXMathJax_Fraktur-bold",p="STIXMathJax_Fraktur",T="STIXMathJax_Latin-bold-italic",C="STIXMathJax_Latin-bold",D="STIXMathJax_Latin-italic",K="STIXMathJax_Latin",Q="STIXMathJax_Main-bold-italic",ak="STIXMathJax_Main-bold",O="STIXMathJax_Main-italic",E="STIXMathJax_Main",k="STIXMathJax_Marks-bold-italic",I="STIXMathJax_Marks-bold",m="STIXMathJax_Marks-italic",Z="STIXMathJax_Marks",ah="STIXMathJax_Misc-bold-italic",h="STIXMathJax_Misc-bold",ad="STIXMathJax_Misc-italic",y="STIXMathJax_Misc",al="STIXMathJax_Monospace",ab="STIXMathJax_Normal-bold-italic",P="STIXMathJax_Normal-bold",S="STIXMathJax_Normal-italic",j="STIXMathJax_Operators-bold",w="STIXMathJax_Operators",M="STIXMathJax_SansSerif-bold-italic",A="STIXMathJax_SansSerif-bold",am="STIXMathJax_SansSerif-italic",d="STIXMathJax_SansSerif",a="STIXMathJax_Script-bold-italic",N="STIXMathJax_Script-italic",W="STIXMathJax_Script",r="STIXMathJax_Shapes-bold-italic",J="STIXMathJax_Shapes-bold",l="STIXMathJax_Shapes",ae="STIXMathJax_Size1",ac="STIXMathJax_Size2",aa="STIXMathJax_Size3",Y="STIXMathJax_Size4",X="STIXMathJax_Size5",af="STIXMathJax_Symbols-bold",v="STIXMathJax_Symbols",F="STIXMathJax_Variants-bold-italic",o="STIXMathJax_Variants-bold",ai="STIXMathJax_Variants-italic",L="STIXMathJax_Variants";var n={61:{dir:s,HW:[[0.589,E]],stretch:{rep:[61,E]}},711:{dir:s,HW:[[0.311,E],[0.56,ae],[0.979,ac],[1.46,aa],[1.886,Y],[2.328,X]]},717:{dir:s,HW:[[0.312,Z]],stretch:{rep:[717,Z]}},759:{dir:s,HW:[[0.33,Z],[0.56,ae],[0.979,ac],[1.46,aa],[1.886,Y],[2.328,X]]},8400:{dir:s,HW:[[0.436,Z],[0.871,ae],[1.308,ac],[1.744,aa],[2.18,Y],[3,X]],stretch:{left:[57365,X],rep:[57366,X]}},8401:{dir:s,HW:[[0.436,Z],[0.871,ae],[1.308,ac],[1.744,aa],[2.18,Y],[3,X]],stretch:{rep:[57366,X],right:[57367,X]}},8406:{dir:s,HW:[[0.436,Z],[0.872,ae],[1.308,ac],[1.744,aa],[2.18,Y],[3,X]],stretch:{left:[57368,X],rep:[57366,X]}},8417:{dir:s,HW:[[0.478,Z]],stretch:{left:[57368,X],rep:[57366,X],right:[57369,X]}},8428:{dir:s,HW:[[0.436,Z],[0.871,ae],[1.308,ac],[1.744,aa],[2.18,Y],[3,X]],stretch:{rep:[57370,X],right:[57371,X]}},8429:{dir:s,HW:[[0.436,Z],[0.871,ae],[1.308,ac],[1.744,aa],[2.18,Y],[3,X]],stretch:{left:[57372,X],rep:[57370,X]}},8430:{dir:s,HW:[[0.436,Z],[0.872,ae],[1.308,ac],[1.744,aa],[2.18,Y],[3,X]],stretch:{left:[57373,X],rep:[57370,X]}},8431:{dir:s,HW:[[0.436,Z],[0.872,ae],[1.308,ac],[1.744,aa],[2.18,Y],[3,X]],stretch:{rep:[57370,X],right:[57374,X]}},8512:{dir:f,HW:[[1.022,b],[1.45,ae]]},8606:{dir:s,HW:[[0.786,E]],stretch:{left:[8606,E],rep:ag}},8607:{dir:f,HW:[[0.816,x]],stretch:{ext:[9168,E],top:[8607,x]}},8608:{dir:s,HW:[[0.786,E]],stretch:{right:[8608,E],rep:ag}},8609:{dir:f,HW:[[0.816,x]],stretch:{ext:[9168,E],bot:[8609,x]}},8612:{dir:s,HW:[[0.787,x]],stretch:{left:[8592,E],rep:[9135,v],right:[10206,v]}},8613:{dir:f,HW:[[0.816,x]],stretch:{bot:[95,E,0.05,-0.01,0.8],ext:[9168,E],top:[8593,E]}},8614:{dir:s,HW:[[0.787,E]],stretch:{left:[10205,v],rep:[9135,v],right:[8594,E]}},8615:{dir:f,HW:[[0.816,x]],stretch:{top:[8868,ak,0.04,0,0.6],ext:[9168,E],bot:[8595,E]}},8616:{dir:f,HW:[[0.816,x]],stretch:{top:[8593,E],ext:[9168,E],bot:[10515,x]}},8617:{dir:s,HW:[[0.786,E]],stretch:{left:[8592,E],rep:ag,right:[57525,x]}},8618:{dir:s,HW:[[0.786,E]],stretch:{left:[57524,x],rep:ag,right:[8594,E]}},8624:{dir:f,HW:[[0.818,E]],stretch:{top:[8624,E],ext:[9168,E,0.152]}},8625:{dir:f,HW:[[0.818,E]],stretch:{top:[8625,E],ext:[9168,E,-0.195]}},8626:{dir:f,HW:[[0.816,x]],stretch:{bot:[8626,x],ext:[9168,E,0.152]}},8627:{dir:f,HW:[[0.816,x]],stretch:{bot:[8627,x],ext:[9168,E,-0.195]}},8628:{dir:s,HW:[[0.786,x]],stretch:{rep:[8722,E,0,0.4],right:[8628,x]}},8629:{dir:f,HW:[[0.818,x]],stretch:{bot:[8629,x],ext:[9168,E,0.57]}},8636:{dir:s,HW:[[0.847,E]],stretch:{left:[8636,E],rep:[9135,v]}},8637:{dir:s,HW:[[0.847,E]],stretch:{left:[8637,E],rep:[9135,v]}},8638:{dir:f,HW:[[0.818,E]],stretch:{ext:[9168,E],top:[8638,E]}},8639:{dir:f,HW:[[0.818,E]],stretch:{ext:[9168,E],top:[8639,E]}},8640:{dir:s,HW:[[0.847,E]],stretch:{rep:[9135,v],right:[8640,E]}},8641:{dir:s,HW:[[0.847,E]],stretch:{right:[8641,E],rep:ag}},8642:{dir:f,HW:[[0.818,E]],stretch:{bot:[8642,E],ext:[9168,E]}},8643:{dir:f,HW:[[0.818,E]],stretch:{bot:[8643,E],ext:[9168,E]}},8651:{dir:s,HW:[[0.786,E]],stretch:{left:[10602,x],rep:[61,E],right:[10605,x]}},8652:{dir:s,HW:[[0.786,E]],stretch:{left:[10603,x],rep:[61,E],right:[10604,x]}},8666:{dir:s,HW:[[0.806,E]],stretch:{left:[8666,E],rep:[57377,X]}},8667:{dir:s,HW:[[0.806,E]],stretch:{rep:[57377,X],right:[8667,E]}},8672:{dir:s,HW:[[0.806,E]],stretch:{left:[8672,E],rep:[57633,x]}},8673:{dir:f,HW:[[0.818,x]],stretch:{ext:[57645,x],top:[8673,x]}},8674:{dir:s,HW:[[0.806,E]],stretch:{right:[8674,E],rep:[57646,x]}},8675:{dir:f,HW:[[0.818,x]],stretch:{ext:[57644,x],bot:[8675,x]}},8676:{dir:s,HW:[[0.806,x]],stretch:{left:[8676,x],rep:ag}},8677:{dir:s,HW:[[0.806,x]],stretch:{right:[8677,x],rep:ag}},8701:{dir:s,HW:[[0.806,x]],stretch:{left:[8701,x],rep:ag}},8702:{dir:s,HW:[[0.806,x]],stretch:{right:[8702,x],rep:ag}},8703:{dir:s,HW:[[0.886,x]],stretch:{left:[8701,x],rep:ag,right:[8702,x]}},8719:{dir:f,HW:[[1.022,w],[1.451,ae]]},8720:{dir:f,HW:[[1.022,w],[1.451,ae]]},8721:{dir:f,HW:[[1.022,w],[1.45,ae]]},8731:{dir:f,HW:[[1.232,w],[1.847,ae],[2.46,ac],[3.075,aa]],stretch:{bot:[57381,X],ext:[57379,X],top:[57380,X]}},8732:{dir:f,HW:[[1.232,w],[1.847,ae],[2.46,ac],[3.075,aa]],stretch:{bot:[57382,X],ext:[57379,X],top:[57380,X]}},8747:{dir:f,HW:[[0.607,E],[0.979,ae]],stretch:{top:[57404,X],ext:[57405,X],bot:[57406,X]}},8748:{dir:f,HW:[[1.144,w],[2.269,ae]]},8749:{dir:f,HW:[[1.144,w],[2.269,ae]]},8750:{dir:f,HW:[[1.144,w],[2.269,ae]]},8751:{dir:f,HW:[[1.144,w],[2.269,ae]]},8752:{dir:f,HW:[[1.144,w],[2.269,ae]]},8753:{dir:f,HW:[[1.144,w],[2.269,ae]]},8754:{dir:f,HW:[[1.144,w],[2.269,ae]]},8755:{dir:f,HW:[[1.144,w],[2.269,ae]]},8896:{dir:f,HW:[[1.022,w],[1.451,ae]]},8897:{dir:f,HW:[[1.022,w],[1.451,ae]]},8898:{dir:f,HW:[[1.032,w],[1.461,ae]]},8899:{dir:f,HW:[[1.032,w],[1.461,ae]]},9130:{dir:f,HW:[[1.01,X,null,57357]],stretch:{top:[57357,X],ext:[57357,X],bot:[57357,X]}},9140:{dir:s,HW:[[0.816,E],[0.925,ae],[1.458,ac],[1.991,aa],[2.524,Y],[3.057,X]],stretch:{left:[57383,X],rep:[57384,X],right:[57385,X]}},9141:{dir:s,HW:[[0.816,E],[0.925,ae],[1.458,ac],[1.991,aa],[2.524,Y],[3.057,X]],stretch:{left:[57386,X],rep:[57387,X],right:[57388,X]}},9168:{dir:f,HW:[[0.304,E],[0.69,ae],[0.879,ac],[1.35,ac,1.536],[1.827,ac,2.078],[2.303,ac,2.62],[2.78,ac,3.162]],stretch:{ext:[8739,E]}},9180:{dir:s,HW:[[1,E],[0.926,ae],[1.46,ac],[1.886,aa],[2.328,Y],[3.237,X]],stretch:{left:[57389,X],rep:[57384,X],right:[57390,X]}},9181:{dir:s,HW:[[1,E],[0.926,ae],[1.46,ac],[1.886,aa],[2.328,Y],[3.237,X]],stretch:{left:[57391,X],rep:[57387,X],right:[57392,X]}},9184:{dir:s,HW:[[1,E],[1.46,ae],[1.886,ac],[2.312,aa],[2.738,Y],[3.164,X]]},9185:{dir:s,HW:[[1,E],[1.46,ae],[1.886,ac],[2.312,aa],[2.738,Y],[3.164,X]]},10098:{dir:f,HW:[[0.932,y],[1.23,ae],[1.845,ac],[2.459,aa],[3.075,Y]]},10099:{dir:f,HW:[[0.932,y],[1.23,ae],[1.845,ac],[2.459,aa],[3.075,Y]]},10214:{dir:f,HW:[[0.93,v],[1.23,ae],[1.845,ac],[2.46,aa],[3.075,Y]],stretch:{top:[9555,l],ext:[9553,l],bot:[9561,l]}},10215:{dir:f,HW:[[0.93,v],[1.23,ae],[1.845,ac],[2.46,aa],[3.075,Y]],stretch:{top:[9558,l],ext:[9553,l],bot:[9564,l]}},10218:{dir:f,HW:[[0.932,v],[1.23,ae],[1.845,ac],[2.461,aa],[3.075,Y]]},10219:{dir:f,HW:[[0.932,v],[1.23,ae],[1.845,ac],[2.461,aa],[3.075,Y]]},10224:{dir:f,HW:[[0.818,x]],stretch:{ext:[57399,X],top:[10224,x]}},10225:{dir:f,HW:[[0.818,x]],stretch:{bot:[10225,x],ext:[57399,X]}},10502:{dir:s,HW:[[0.816,x]],stretch:{left:[8656,E],rep:[61,E],right:[10980,w,0,-0.09]}},10503:{dir:s,HW:[[0.816,x]],stretch:{left:[8872,E,0,-0.09],rep:[61,E],right:[8658,E]}},10506:{dir:f,HW:[[0.818,x]],stretch:{ext:[57400,X],top:[10506,x]}},10507:{dir:f,HW:[[0.818,x]],stretch:{bot:[10507,x],ext:[57400,X]}},10514:{dir:f,HW:[[0.818,x]],stretch:{top:[10514,x],ext:[9168,E]}},10515:{dir:f,HW:[[0.818,x]],stretch:{bot:[10515,x],ext:[9168,E]}},10574:{dir:s,HW:[[0.85,x]],stretch:{left:[8636,E],rep:ag,right:[8640,E]}},10575:{dir:f,HW:[[0.818,x]],stretch:{top:[8638,E],ext:[9168,E],bot:[8642,E]}},10576:{dir:s,HW:[[0.85,x]],stretch:{left:[8637,E],rep:ag,right:[8641,E]}},10577:{dir:f,HW:[[0.818,x]],stretch:{top:[8639,E],ext:[9168,E],bot:[8643,E]}},10578:{dir:s,HW:[[0.816,x]],stretch:{left:[10578,x],rep:ag}},10579:{dir:s,HW:[[0.816,x]],stretch:{right:[10579,x],rep:ag}},10580:{dir:f,HW:[[0.818,x]],stretch:{top:[10580,x],ext:[9168,E]}},10581:{dir:f,HW:[[0.818,x]],stretch:{bot:[10581,x],ext:[9168,E]}},10582:{dir:s,HW:[[0.816,x]],stretch:{left:[10582,x],rep:ag}},10583:{dir:s,HW:[[0.816,x]],stretch:{right:[10583,x],rep:ag}},10584:{dir:f,HW:[[0.818,x]],stretch:{top:[10584,x],ext:[9168,E]}},10585:{dir:f,HW:[[0.818,x]],stretch:{bot:[10585,x],ext:[9168,E]}},10586:{dir:s,HW:[[0.816,x]],stretch:{left:[8636,E],rep:ag,right:[8867,ak,0,0.1,0.6]}},10587:{dir:s,HW:[[0.816,x]],stretch:{left:[57526,x],rep:ag,right:[8640,E]}},10588:{dir:f,HW:[[0.818,x]],stretch:{bot:[95,E,0.05,-0.01,0.8],ext:[9168,E],top:[8638,E]}},10589:{dir:f,HW:[[0.818,x]],stretch:{top:[8868,ak,0.04,0,0.6],ext:[9168,E],bot:[8642,E]}},10590:{dir:s,HW:[[0.816,x]],stretch:{left:[8637,E],rep:ag,right:[8867,ak,0,0.1,0.6]}},10591:{dir:s,HW:[[0.816,x]],stretch:{left:[57526,x],rep:ag,right:[8641,E]}},10592:{dir:f,HW:[[0.818,x]],stretch:{bot:[95,E,0.05,-0.01,0.8],ext:[9168,E],top:[8639,E]}},10593:{dir:f,HW:[[0.818,x]],stretch:{top:[8868,ak,0.04,0,0.6],ext:[9168,E],bot:[8643,E]}},10624:{dir:f,HW:[[0.884,v]],stretch:{ext:[10624,v]}},10627:{dir:f,HW:[[0.932,v],[1.23,ae],[1.845,ac],[2.46,aa],[3.075,Y]]},10628:{dir:f,HW:[[0.932,v],[1.23,ae],[1.845,ac],[2.46,aa],[3.075,Y]]},10629:{dir:f,HW:[[0.932,v],[1.23,ae],[1.848,ac],[2.459,aa],[3.075,Y]]},10630:{dir:f,HW:[[0.932,v],[1.23,ae],[1.848,ac],[2.459,aa],[3.075,Y]]},10647:{dir:f,HW:[[0.932,E]],stretch:{top:[57613,l,0.1,0.05],ext:[9168,E,-0.1],bot:[57612,l,0.1]}},10648:{dir:f,HW:[[0.932,E]],stretch:{top:[57612,l,-0.1,0.05],ext:[9168,E],bot:[57613,l,-0.1]}},10752:{dir:f,HW:[[1.022,w],[1.451,ae]]},10753:{dir:f,HW:[[1.022,w],[1.451,ae]]},10754:{dir:f,HW:[[1.022,w],[1.451,ae]]},10755:{dir:f,HW:[[1.032,w],[1.461,ae]]},10756:{dir:f,HW:[[1.032,w],[1.461,ae]]},10757:{dir:f,HW:[[1.022,w],[1.451,ae]]},10758:{dir:f,HW:[[1.022,w],[1.451,ae]]},10759:{dir:f,HW:[[1.022,w],[1.451,ae]]},10760:{dir:f,HW:[[1.022,w],[1.451,ae]]},10761:{dir:f,HW:[[1.022,w],[1.451,ae]]},10762:{dir:f,HW:[[1.022,w],[1.45,ae]]},10763:{dir:f,HW:[[1.144,w],[2.269,ae]]},10764:{dir:f,HW:[[1.144,w],[2.269,ae]]},10765:{dir:f,HW:[[1.144,w],[2.269,ae]]},10766:{dir:f,HW:[[1.144,w],[2.269,ae]]},10767:{dir:f,HW:[[1.144,w],[2.269,ae]]},10768:{dir:f,HW:[[1.144,w],[2.269,ae]]},10769:{dir:f,HW:[[1.144,w],[2.269,ae]]},10770:{dir:f,HW:[[1.144,w],[2.269,ae]]},10771:{dir:f,HW:[[1.144,w],[2.269,ae]]},10772:{dir:f,HW:[[1.144,w],[2.269,ae]]},10773:{dir:f,HW:[[1.144,w],[2.269,ae]]},10774:{dir:f,HW:[[1.144,w],[2.269,ae]]},10775:{dir:f,HW:[[1.144,w],[2.269,ae]]},10776:{dir:f,HW:[[1.144,w],[2.269,ae]]},10777:{dir:f,HW:[[1.144,w],[2.269,ae]]},10778:{dir:f,HW:[[1.144,w],[2.269,ae]]},10779:{dir:f,HW:[[1.267,w],[2.426,ae]]},10780:{dir:f,HW:[[1.267,w],[2.426,ae]]},11004:{dir:f,HW:[[1.022,w],[1.23,ae],[1.875,ac]]},11007:{dir:f,HW:[[1.022,w],[1.23,ae],[1.875,ac]]},11077:{dir:s,HW:[[0.818,l]],stretch:{left:[11077,l],rep:[57401,X]}}};for(var U in n){if(n.hasOwnProperty(U)){t[U]=n[U]}}MathJax.Ajax.loadComplete(aj.fontDir+"/fontdata-extra.js")})(MathJax.OutputJax["HTML-CSS"]);
