﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using com.ecool.service;
using System.Web.Mvc;
using ECOOL_APP.com.ecool.Models.entity;
using ECOOL_APP.com.ecool.Models.DTO;
using ECOOL_APP;
using EcoolWeb.Models;

namespace EcoolWeb.Controllers
{
    public class BreadcrumbController : Controller
    {
        public ActionResult Index(string BRE_NO = "", string ThisActive="")
        {
            ViewBag.ThisActive = ThisActive;
            List<uZZT01> Data =new List<uZZT01>() ;

            if (BRE_NO!="")
            {
                Data = BreadcrumbService.GetBreadcrumbQUERY(BRE_NO);
              
            }

            return PartialView("../Shared/_Breadcrumb", Data);
        }


    }
}