﻿@model SECI02UseSituationIndexViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);

    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();


    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
    else
    {
        Layout = "~/Views/Shared/_LayoutSEO.cshtml";
    }


}


<link href='~/Content/css/EzCss.css' rel='stylesheet' />
<script src="~/Scripts/Highcharts-4.0.1/js/highcharts.js"></script>
<script src="~/Scripts/Highcharts-4.0.1/js/modules/exporting.js"></script>
<script src="~/Scripts/Blob.js"></script>
<script src="~/Scripts/FileSaver.js"></script>
<script src="~/Scripts/tableexport.js"></script>
<script src="~/Scripts/printThis/printThis.js"></script>
<br />


@using (Html.BeginForm("UseSituationIndex", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", id = "form1" }))
{

    @Html.Partial("_Title_Secondary")


    Html.RenderAction("_Menu", new { NowAction = "UseSituationIndex" });



    <div class="form-inline" role="form">
        <div class="form-group">
            <label class="control-label">學年</label>
        </div>
        <div class="form-group">
            @Html.DropDownListFor(m=>m.WhereSYEAR, (IEnumerable<SelectListItem>)ViewBag.SYearItems, new { @class = "form-control", @onchange = "form1.submit();" })

        </div>
        <div class="form-group">
            <label class="control-label">月份</label>
        </div>
        <div class="form-group">
            @Html.DropDownListFor(m => m.WhereMM, (IEnumerable<SelectListItem>)ViewBag.MonthItems, new { @class = "form-control", @onchange = "form1.submit();" })
        </div>
        <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="form1.submit();" />
        <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />
    </div>

            <br />

            <div class="form-inline">
                <div class="col-xs-12 text-right">
                    <button type="button" class="btn btn-sm btn-sys" onclick="PrintBooK()">我要列印</button>
                    <button class="btn btn-sm btn-sys" onclick='fn_save()'>另存新檔(Excel)</button>
                </div>
            </div>

}
<br />


<div id="tbData">
    <div style="height:25px"></div>
    <div class="col-sm-12">
        <div class="panel table-responsive">
            <table class="table table-striped table-hover">
                <caption><strong style="font-size:18px">使用情形表</strong></caption>
                <thead>
                    <tr>
                        @if (Model.IsClass)
                        {
                            <th style="text-align: center">
                                班級
                            </th>
                        }
                        else
                        {
                            <th style="text-align: center">
                                學校
                            </th>
                            <th style="text-align: center">
                                酷幣總發行量
                            </th>
                            <th style="text-align: center">
                                登入人次
                            </th>
                        }
                        <th style="text-align: center">
                            線上投稿投稿數
                        </th>
                        <th style="text-align: center">
                            閱讀認證投稿數
                        </th>
                        <th style="text-align: center">
                            線上藝廊作品數
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var item in Model.UseSituationTotal)
                    {
                        <tr>
                            @if (Model.IsClass)
                            {
                                <td style="text-align: center">
                                     @item.CLASS_NO
                                </td>
                            }
                            else
                            {
                                <td style="text-align: center">
                                    @item.SHORT_NAME
                                </td>
                                <td  align="center">
                                   @item.ADD_CASH_ALL.ToString("#,#0")
                                </td>
                                <td  align="center">
                                    @item.People_Count.ToString("#,#0")
                                </td>
                            }

                            <td align="center">
                                @item.ADDT01_ToTAL.ToString("#,#0")
                            </td>
                            <td align="center">
                                @item.ADDT06_ToTAL.ToString("#,#0")
                            </td>
                            <td align="center">
                                @item.ADDT22_ToTAL.ToString("#,#0")
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>


</div>




<script language="javascript">

    var targetFormID = '#form1';



    function PrintBooK()
    {
        $('#tbData').printThis();
    }


    function todoClear() {
        ////重設
        $("#form1").find(":input,:selected").each(function (i) {

            var type = $(this).attr('type')
            var InPreadonly = $(this).attr('readonly')
            var tag = this.tagName.toLowerCase(); // normalize case

            if (InPreadonly == false || InPreadonly == undefined) {

                if (type == 'radio' || type == 'checkbox') {
                    if ($(this).attr("title") == 'Default') {
                        this.checked = true;
                    }
                    else {
                        this.checked = false;
                    }
                }
                else if (tag == 'select') { //下拉式選單
                    this.selectedIndex = 0;
                }
                else if (type == 'text' || type == 'hidden' || type == 'password' || type == 'textarea') {
                    this.value = '';
                }
            }
        });

        form1.submit();
    }



    function fn_save() {
        var blob = new Blob([document.getElementById('tbData').innerHTML], {
            type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8"
        });
        var strFile = "Report.xls";
        saveAs(blob, strFile);
        return false;
    }

</script>