﻿using ECOOL_APP.EF;
using EcoolWeb.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using MvcPaging;
using ECOOL_APP.com.ecool.service;
using EntityFramework.Extensions;
using Dapper;
using ECOOL_APP;
using EcoolWeb.CustomAttribute;
using log4net;
namespace EcoolWeb.Controllers
{
    public class NoticeApiController : ApiBase
    {
        static ILog logger = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        private ECOOL_DEVEntities Db = new ECOOL_DEVEntities();

        /// <summary>
        /// 通知清單
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [SessionExpire]
        public NoticeApiIndexViewModel Index(NoticeApiIndexViewModel model)
        {
            string UserNO = null;
            UserProfile user = UserProfileHelper.Get();
            if (user != null) UserNO = user.USER_NO;
            string QueryUserNO = (UserNO != null) ? UserNO : model.USER_NO;

            if (model == null) model = new NoticeApiIndexViewModel();

            IQueryable<NoticeApiListViewModel> Data ;
            Data = (from a in Db.APPT02
                    join o in Db.APPT02_REF on new { a.REF_SOU_BRE_NO, a.REF_SOU_ITEM } equals new { o.REF_SOU_BRE_NO, o.REF_SOU_ITEM }
                    into ps
                    from o in ps.Where(o => o.OS_TYPE == model.OS_TYPE).DefaultIfEmpty()
                    where a.SCHOOL_NO == model.SCHOOL_NO && a.USER_NO == QueryUserNO
                    && a.STATUS != APPT02.StatusVal.Cre
                    && a.DEL_YN == "N"
                    select new NoticeApiListViewModel
                    {
                        RefAPPT02 = a,
                        AppControllerName = o.APP_CONTROLLER,
                        AppIdentifierName = o.APP_IDENTIFIER,
                        AppTitleName = o.APP_TITLE,
                        WebViewPath = o.APP_PATH
                    });

            if (string.IsNullOrWhiteSpace(model.SearchContents) == false)
            {
                Data = Data.Where(a => a.RefAPPT02.BODY_TXT.Contains(model.SearchContents.Trim()));
            }

            Data = Data.OrderByDescending(a => a.RefAPPT02.F_DATE).ThenByDescending(a => a.RefAPPT02.NOTIFICATION_ID);

            model.List = Data.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, model.PageSize);

            if (model.USER_NO != null && UserNO!=null)
            {
                if (model.USER_NO == "100020" || model.USER_NO == "102063") logger.Info("APP" + model.OS_TYPE + "查詢通知清單" + model.USER_NO + "/"+UserNO+"網址" + this.Request.RequestUri.ToString());
            }
            

            return model;
        }

        public static int? getBadgeNumber(string SCHOOL_NO ,string USER_NO, ref ECOOL_DEVEntities refDb)
        {

            if (string.IsNullOrWhiteSpace(SCHOOL_NO) || string.IsNullOrWhiteSpace(USER_NO))
            {
                return null;
            }

            string sSQL = @" Select Count(*) from APPT02  a (nolock) 
            where a.SCHOOL_NO = @SCHOOL_NO and a.USER_NO = @USER_NO 
            and a.STATUS = @StatusValCre and a.DEL_YN = 'N' ";

            var QTemp = refDb.Database.Connection.Query<int?>(sSQL, new {
                SCHOOL_NO = SCHOOL_NO,
                USER_NO = USER_NO,
                StatusValCre = APPT02.StatusVal.UnRead
            });

            return QTemp.FirstOrDefault().Value;

        }

        [HttpPost]
        public int? getBadgeNumber()
        {
            UserProfile user = UserProfileHelper.Get();

            if (user!=null)
            {
                return getBadgeNumber(user.SCHOOL_NO, user.USER_NO, ref Db);
            }
            else
            {
                return null;
            }


        }


        /// <summary>
        /// 更新已讀
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [SessionExpire]
        public int UpRead(NoticeApiIndexViewModel model)
        {

            if (model == null) model = new NoticeApiIndexViewModel();


            IQueryable<NoticeApiListViewModel> Data;


            Data = (from a in Db.APPT02
                    join o in Db.APPT02_REF on new { a.REF_SOU_BRE_NO, a.REF_SOU_ITEM } equals new { o.REF_SOU_BRE_NO, o.REF_SOU_ITEM }
                    into ps
                    from o in ps.Where(o => o.OS_TYPE == model.OS_TYPE).DefaultIfEmpty()
                    where a.SCHOOL_NO == model.SCHOOL_NO && a.USER_NO == model.USER_NO
                    && a.STATUS != APPT02.StatusVal.Cre
                    && a.DEL_YN == "N" 
                    select new NoticeApiListViewModel
                    {
                        RefAPPT02 = a,
                        AppControllerName = o.APP_CONTROLLER,
                        AppIdentifierName = o.APP_IDENTIFIER,
                        AppTitleName = o.APP_TITLE,
                        WebViewPath = o.APP_PATH
                    }).OrderByDescending(a => a.RefAPPT02.F_DATE).ThenByDescending(a=>a.RefAPPT02.NOTIFICATION_ID);

         
            model.List = Data.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, model.PageSize);

            int UpNum = 0;

            foreach (var item in model.List.Where(a=>a.RefAPPT02.STATUS== APPT02.StatusVal.UnRead))
            {
                var Up = (from a in Db.APPT02
                          where a.NOTIFICATION_ID == item.RefAPPT02.NOTIFICATION_ID && a.STATUS == APPT02.StatusVal.UnRead
                          select a).FirstOrDefault();

                if (Up != null)
                {
                    Up.STATUS = APPT02.StatusVal.Read;
                    UpNum = UpNum + 1;
                }
            }

            Db.SaveChanges();

            return UpNum;
        }

        /// <summary>
        /// del 通知
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [SessionExpire]
        public int DelNotice(NoticeApiIndexViewModel model)
        {
            string UserNO = null;
            UserProfile user = UserProfileHelper.Get();
            if (user != null) UserNO = user.USER_NO;
            string QueryUserNO = (UserNO != null) ? UserNO : model.USER_NO;

            using (var dbContextTransaction = Db.Database.BeginTransaction())
            {
                try
                {
                    var Updata = Db.APPT02.Where(u => u.SCHOOL_NO == model.SCHOOL_NO
                     && u.USER_NO == QueryUserNO && u.STATUS != APPT02.StatusVal.Cre)
                    .Update(u => new APPT02 { DEL_YN = "Y" });

                    dbContextTransaction.Commit();
                }
                catch (Exception ex)
                {
                    dbContextTransaction.Rollback();
                    throw ex;
                }
               
            }

            if (model.USER_NO != null && UserNO!=null)
            {
                if (model.USER_NO == "100020" || model.USER_NO == "102063")  logger.Info("APP" + model.OS_TYPE + "刪除通知清單" + model.USER_NO +"/"+UserNO+ "網址" + this.Request.RequestUri.ToString());
            }

            return 1;
        }


        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                Db.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
