﻿@model EcoolWeb.Models.SECI02BooksOrderViewModel
@{

    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    int DataCount = 0;
    int RowNumber = 0;
    string HidStyle = "";
    bool topmoth = false;
    if (Model.isCarousel || Model.IsPrint)
    {
        Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
        HidStyle = "display:none";
    }
    else if (ViewBag.from != null)
    {
        Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
    }
    else
    {
        if (AppMode)
        {
            Layout = "~/Views/Shared/_LayoutWebView.cshtml";
        }
    }

    int BookPageCount = 0;
}
@if (Model.isCarousel)
{
    <style type="text/css">
        .bigger {
            font-size: 30px;
        }

        .table-ecool thead > tr > th, .table-ecool thead > tr > td {
            font-size: 30px;
        }
    </style>
}
<style type="text/css" media="print">
    /* 列印時隱藏 */
    .cScreen {
        display: none;
    }

    a[href]:after {
        content: none !important;
    }
</style>

<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>
<script src="~/Scripts/Blob.js"></script>
<script src="~/Scripts/FileSaver.js"></script>
<script src="~/Scripts/tableexport.js"></script>
@if (AppMode == false)
{
    if (ViewBag.Title != null)
    {
        <div class="bigger Title_Secondary">@ViewBag.Title</div>
        <br>
    }
}
@Html.Partial("_Notice")

@if (Model.IsPrint == false && ViewBag.from == null)
{
    @Html.Partial("../SECI05/_SECI05Menu", 1)
}

@using (Html.BeginForm("BooksOrder", "SECI02", FormMethod.Post, new { id = "BooksOrder", NAME = "BooksOrder" }))
{
    @Html.HiddenFor(m => m.whereUserNo)
    @Html.HiddenFor(m => m.WhereUP_DATE_START)
    @Html.HiddenFor(m => m.WhereUP_DATE_END)
    @Html.HiddenFor(m => m.OrdercColumn)
    @Html.HiddenFor(m => m.Page)
    @Html.HiddenFor(m => m.WhereIsMonthTop)
    @Html.HiddenFor(m => m.IsPrint)
    @Html.HiddenFor(m => m.IsToExcel)
    @Html.HiddenFor(m => m.fromStr)
    <div class="form-inline" style="@HidStyle" id="Q_Div">
        <div class="form-inline" role="form">
            <div class="form-group">

                <label class="control-label">學年</label>
            </div>
            <div class="form-group">
                @Html.DropDownListFor(m => m.WhereSYEAR, (IEnumerable<SelectListItem>)ViewBag.SYearItems, new { @class = "form-control input-sm", @onchange = "FunPageProc(1)" })
            </div>
            <div class="form-group">
                <label class="control-label">月份</label>
            </div>
            <div class="form-group">
                @Html.DropDownListFor(m => m.WhereMM, (IEnumerable<SelectListItem>)ViewBag.MonthItems, new { @class = "form-control input-sm", @onchange = "FunPageProc(1)" })
            </div>
            <br />
            <div class="form-group ">
                <label class="control-label">學號/姓名</label>
            </div>
            <div class="form-group">
                @Html.EditorFor(m => m.whereKeyword, new { htmlAttributes = new { @class = "form-control input-sm" } })
            </div>
            <div class="form-group">
                <label class="control-label">年級</label>
            </div>
            <div class="form-group">
                @Html.DropDownListFor(m => m.whereGrade, (IEnumerable<SelectListItem>)ViewBag.GradeItem, new { @class = "form-control input-sm", onchange = "FunPageProc(1)" })
            </div>
            <div class="form-group">
                <label class="control-label">班級</label>
            </div>

            <div class="form-group">
                @Html.DropDownListFor(m => m.whereCLASS_NO, (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control input-sm", onchange = "FunPageProc(1)" })
            </div>  <br />
            <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1)" />
            <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />
            @if (user != null)
            {
                if (user.USER_TYPE != UserType.Student && user.USER_TYPE != UserType.Parents)
                {

                    if (!Model.IsPrint)
                    {
                        <button id="ButtonExcel" class="btn-yellow btn btn-sm cScreen" style="float:right" onclick="exportExcel()">匯出excel</button>
                        <button type="button" class="btn-yellow btn btn-sm" onclick="PrintBooK()" style="float:right;margin:5px">我要列印</button>
                    }
                    else
                    {
                        <button type="button" class="btn-yellow btn btn-sm" onclick="PrintBooK()" style="float:right">我要列印</button>
                    }

                }
            }
        </div>
    </div>

    <div class="form-inline cScreen" style="text-align:right;@HidStyle">
        <br />
        @if (!AppMode)
        {
            <button class="btn btn-xs btn-pink @(Model.WhereIsMonthTop == false ? "active" : "")" type="button" onclick="todoClear();doMonthTop('False');">全部 </button>
            <button title="月排行榜”只能查詢最近一個月內的" class="btn btn-xs btn-pink @(Model.WhereIsMonthTop == true ? "active" : "")" type="button" onclick="todoClear();doMonthTop('True');">月排行榜 </button>

        }
        else
        {
            <button class="btn btn-xs btn-pink @(Model.WhereIsMonthTop == false ? "active" : "")" type="button" onclick="doMonthTop('False');">全部</button>
            <button class="btn btn-xs btn-pink @(Model.WhereIsMonthTop == true ? "active" : "")" type="button" onclick="doMonthTop('True');">月排行榜</button>

        }
    </div>

    <div class="text-left" style="padding-left:30px">
        <strong>同步至 @ViewBag.RunStatusDate 前</strong>
        <strong> <font color="#FF0000">@ViewBag.RunStatus</font></strong>
    </div>
    <div class="@(Model.IsPrint ? "":"table-responsive")">
        <div class="text-center" id="tbData">
            <table class="@(Model.IsPrint ? "table table-bordered" : "bigger table-ecool table-92Per table-hover table-ecool-List")">
                <thead>
                    <tr>
                        <th style="text-align: center">序號</th>
                        <th style="text-align: center">
                            班級
                        </th>
                        <th style="text-align: center">
                            姓名
                        </th>
                        <th style="text-align: center;cursor:pointer;" onclick="doSort('SE_QTY');">
                            @if (!string.IsNullOrWhiteSpace(Model.WhereSYEAR))
                            {
                                <samp>@(Model.WhereSYEAR)學年</samp>
                            }
                            @if (!string.IsNullOrWhiteSpace(Model.WhereMM))
                            {
                                <samp>@(Model.WhereMM)月份</samp>
                            }
                            借書量
                            <img id="CRE_DATE" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var item in Model.BooksList)
                    {
                        BookPageCount += item.SE_QTY ?? 0;
                        DataCount++;
                        RowNumber = Model.BooksList.PageSize * (Model.BooksList.PageNumber - 1) + (DataCount);
                        if (Model.isCarousel && DataCount > 5) { break; }
                        <tr>
                            <td class="bigger">@RowNumber</td>
                            <td class="bigger">
                                @Html.DisplayFor(modelItem => item.CLASS_NO)
                            </td>
                            <td style="text-align: center;" class="bigger">
                                @if (user != null)
                                {
                                    if (user.USER_TYPE == UserType.Admin || user.USER_TYPE == UserType.Teacher)
                                    {
                                        if (Model.WhereUP_DATE_START != null && Model.WhereUP_DATE_END != null)
                                        {
                                            <button type="button" class="colorbox btn-link" href='@Url.Action("BorrowList", "SECI05", new { WhereSCHOOL_NO = item.SCHOOL_NO, WhereUSER_NO = item.USER_NO, WhereBORROW_DATES = Model.WhereUP_DATE_START, WhereBORROW_DATEE = Model.WhereUP_DATE_END })'>@item.SNAME</button>
                                        }
                                        else
                                        {
                                            <button type="button" class="colorbox btn-link" href='@Url.Action("BorrowList", "SECI05",new { WhereSCHOOL_NO = item.SCHOOL_NO, WhereUSER_NO =item.USER_NO})'>@item.SNAME</button>
                                        }

                                    }
                                    else
                                    {
                                        @item.SNAME
                                    }
                                }
                                else
                                {
                                    @item.SNAME
                                }
                            </td>
                            <td class="bigger">
                                @Html.DisplayFor(modelItem => item.SE_QTY)
                            </td>
                        </tr>
                    }
                    <tr>
                        <td></td>
                        <td></td>
                        <td></td>

                        <td style="border-top:2px solid #d56666">
                            <div class="text-danger">
                                本頁總計: @BookPageCount 本書　　
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    if (Model.isCarousel == false)
    {
        <div>
            @Html.Pager(Model.BooksList.PageSize, Model.BooksList.PageNumber, Model.BooksList.TotalItemCount).Options(o => o
              .DisplayTemplate("BootstrapPagination")
             .MaxNrOfPages(5)
             .SetPreviousPageText("上頁")
             .SetNextPageText("下頁")
         )
        </div>
    }

    @*<div style="text-align:center;">

            @if (Request["OrderList"] != null)
            {
                <a href='@Url.Action("Index", "ADDI01")' role="button" class="btn btn-default">
                    返回
                </a>
                <br />
            }
        </div>*@

}


@section scripts{
    <script>
        var targetFormID = '#BooksOrder';

           window.onload = function () {
                if ($('#@Html.IdFor(m=>m.IsPrint)').val() == "true" && $('#@Html.IdFor(m=>m.IsToExcel)').val() != "true"  ) {
                    window.print()
               }
               if ($('#WhereIsMonthTop').val() == "true") {
                   topmoth = true;

               }
               $('#fromStr').val("@ViewBag.from");
         }

         //$(function () {
         //       $('#ButtonExcel').click(function () {
         //           var blob = new Blob([document.getElementById('tbData').innerHTML], {
         //               type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8"
         //           });
         //           var strFile = "Report.xls";
         //           saveAs(blob, strFile);
         //           return false;
         //       });
         //   });

        function PrintBooK()
        {
            $('#@Html.IdFor(m=>m.IsPrint)').val(true)
            $(targetFormID).attr('action','@Url.Action("BooksOrder", "SECI02")')
            $(targetFormID).attr('target', '_blank').submit().removeAttr('target');
            $('#@Html.IdFor(m=>m.IsPrint)').val(false)
        }

         function doMonthTop(val) {
             $("#@Html.IdFor(m=>m.WhereIsMonthTop)").val(val);

                FunPageProc(1)
            }

            $(document).ready(function () {
                $(".colorbox").colorbox({ iframe: true, width: "80%", height: "80%", opacity: 0.82 });
            });

            function btnSend_onclick() {
                document.OrderList.enctype = "multipart/form-data";
                document.OrderList.action = "../SECI02/BooksOrder";
                document.OrderList.submit();
            }

            function FunPageProc(page) {
                if ($(targetFormID).size() > 0) {
                    $('#Page').val(page)
                    $(targetFormID).submit();
                }
            };

            function doSort(SortCol) {
                $("#OrdercColumn").val(SortCol);
                FunPageProc(1)
            }
            function exportExcel() {

                $("#BooksOrder").attr("enctype", "multipart/form-data");
                $("#BooksOrder").attr("action", "@Url.Action("ExcelExport", (string)ViewBag.BRE_NO)");
                $("#BooksOrder").submit();

        }
            function todoClear() {

                ////重設
                $("#Q_Div").find(":input,:selected").each(function (i) {

                    var type = $(this).attr('type')
                    var InPreadonly = $(this).attr('readonly')
                    var tag = this.tagName.toLowerCase(); // normalize case

                    if (InPreadonly == false || InPreadonly == undefined) {

                        if (type == 'radio' || type == 'checkbox') {
                            if ($(this).attr("title") == 'Default') {
                                this.checked = true;
                            }
                            else {
                                this.checked = false;
                            }
                        }
                        else if (tag == 'select') { //下拉式選單
                            this.selectedIndex = 0;
                        }
                        else if (type == 'text' || type == 'hidden' || type == 'password' || type == 'textarea') {
                            this.value = '';
                        }
                    }
                });

                $(targetFormID).submit();
            }
    </script>
}