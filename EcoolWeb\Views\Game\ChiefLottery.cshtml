﻿@model GameLotteryCreViewModel
@using ECOOL_APP.com.ecool.util
@{
    ViewBag.Title = ViewBag.Panel_Title;
    Layout = "~/Views/Shared/_LayoutGame.cshtml";
    EcoolWeb.Models.UserProfileHelper.SetLayoutCookieData(Layout);

    var index = 0;
    var grpOf10s = Model.People?.GroupBy(x => index++ / 10).ToList();

    int NO = 1;

    string ImgPath = string.Empty;
}

@Html.Partial("_Notice")

@if (SharedGlobal.HomeIndex == "ChildMonthIndex")
{
    ImgPath = "img/childrens_month_styleB";
    <link href="~/Content/css/childrens-month_styleB.css" rel="stylesheet" />
    <img src="~/Content/img/childrens_month_styleB/<EMAIL>" alt="" class="img-spaceship">
}
else
{
    ImgPath = "img";
    <link href="~/Content/css/childrens-month.min.css" rel="stylesheet" />
    <img src="" alt="" class="img-spaceship">
}

<!--↑ add bg-lottery-playing 動畫背景/bg-lottery-award 得獎背景 -->
<!--↓ add ready-box-playing 播放動畫 -->
<div class="ready-box">
    <img src="~/Content/@ImgPath/<EMAIL>" alt="">
    <button id="BtnStart" type="button" class="btn btn-start btn-lg btn-block">開始抽獎</button>
</div>

<div class="award-box" style="display:none">
    <strong class="award-head">@Model.Main.LOTTERY_DESC - 得獎名單</strong>
    @using (Html.BeginForm("ChiefLottery", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "formEdit", id = "formEdit", enctype = "multipart/form-data" }))
    {
        @Html.AntiForgeryToken()
        @Html.ValidationSummary(true, "", new { @class = "text-danger" })

        @Html.HiddenFor(m => m.Search.WhereGAME_NAME)
        @Html.HiddenFor(m => m.Search.WhereGAME_NO)
        @Html.HiddenFor(m => m.Search.WhereLOTTERY_NO)
        @Html.HiddenFor(m => m.Search.WhereSCHOOL_NO)
        @Html.HiddenFor(m => m.Search.SyntaxName)
        @Html.HiddenFor(m => m.Search.Page)
        @Html.HiddenFor(m => m.Search.OrdercColumn)

        @Html.HiddenFor(m => m.Main.GAME_NO)
        @Html.HiddenFor(m => m.Main.LOTTERY_NO)
        @Html.HiddenFor(m => m.Main.LEVEL)
        @Html.HiddenFor(m => m.Main.LEVEL_COUNT)
        @Html.HiddenFor(m => m.Main.PEOPLE_COUNT)
        @Html.HiddenFor(m => m.Main.UNLOTTERY)
        @Html.HiddenFor(m => m.Main.UNCUEST)
        @Html.HiddenFor(m => m.Main.STATUS)
        @Html.HiddenFor(m => m.Main.IS_GIVE_UP_LOTTERY)
        @Html.HiddenFor(m => m.Main.LOTTERY_DESC)

        @Html.HiddenFor(m => m.LotteryType)

        <div id="carousel-example-generic" class="carousel slide" data-ride="carousel">
            <!-- Wrapper for slides -->
            @if (grpOf10s != null)
            {

                <div class="carousel-inner" role="listbox">

                    @foreach (var groupIndex in grpOf10s)
                    {

                        <div class="item @(NO==1 ? "active":"")">
                            <ul class="list-award">
                                @foreach (var item in groupIndex)
                                {
                                    <li class="clearfix">
                                        <span>
                                            @if (item.GAME_USER_TYPE == UserType.Student)
                                            {
                                                @Html.DisplayFor(modelItem => item.SHORT_NAME)
                                            }
                                            else
                                            {
                                                <text>卡片</text>
                                            }
                                            @Html.Partial("_LotteryPeopleView", item)
                                        </span>
                                        <small class="pull-right">
                                            <span>
                                                @if (item.GAME_USER_TYPE == UserType.Student)
                                                {
                                                    @Html.DisplayFor(modelItem => item.USER_NO)
                                                }
                                                else
                                                {
                                                    @Html.DisplayFor(modelItem => item.GAME_USER_ID)
                                                }
                                            </span>
                                            <span style="padding-left:50px">
                                                @if (item.GAME_USER_TYPE == UserType.Student)
                                                {
                                                    @StringHelper.MaskName(item.NAME)
                                                }
                                                else
                                                {
                                                    @Html.DisplayFor(modelItem => item.NAME)

                                                }
                                            </span>
                                        </small>
                                    </li>
                                }
                            </ul>
                        </div>

                        NO++;
                    }
                </div>
            }

            <!-- Indicators -->
            <ol class="carousel-indicators">
                @for (int i = 0; i < NO - 1; i++)
                {
                    <li data-target="#carousel-example-generic" data-slide-to="@i" class=@(i==0 ? "active":"")></li>
                }
            </ol>
        </div>
    }

    @if (Model.Main.IS_GIVE_UP_LOTTERY == true)
    {
        <div class="award-foot">
            <button class="btn btn-default" onclick="onGiveUpLottery()">取消</button>
            <button class="btn btn-success" onclick="LotterySave()">確定</button>
        </div>
    }
    else
    {
        <div class="award-foot">
            <button class="btn btn-success" onclick="onBack()">回抽獎首頁</button>
        </div>
    }
</div>
<script src="~/Scripts/buzz/buzz.min.js"></script>
<script type="text/javascript">

    var targetFormID = "#formEdit"

       var game_LotterySound = new buzz.sound("@Url.Content("~/Content/mp3/game_Lottery.mp3")");

        function onGiveUpLottery()
        {
            $(targetFormID).attr("action", "@Url.Action("ChiefLottery", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        function LotterySave()
        {
            $('#@Html.IdFor(m=>m.LotteryType)').val('@( (byte)ADDT32.LotteryTypeVal.可重抽模式儲存抽獎結果)')
            $(targetFormID).attr("action", "@Url.Action("LotterySave", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        function onBack() {
            $(targetFormID).attr("action", "@Url.Action("Lottery", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

    $(document).ready(function () {

        $('body').addClass('bg-lottery')

        //本頁使用 bootstrap3 carousel 得獎名單輪播設定與秒數
        if ($(window).outerWidth() < 767) {
            $('.carousel').carousel({
                pause: true,
                interval: false
            });
        } else {
            $('.carousel').carousel({
                interval: 10000
            })
        }

        //抽獎開始
        $('#BtnStart').on('click', function () {
            $('.img-spaceship').show()
            $('.bg-lottery').addClass('bg-lottery-playing');
            $('.ready-box').addClass('ready-box-playing');
             $('.ready-box img').attr('src', '@Url.Content("~/Content/"+ ImgPath + "/laba-run.gif")');
            t = 8;//重製動畫秒數
            game_LotterySound.play();
            showTime();
        });

        //確定名單或取消
        $('#BtnSuccess').on('click', function () {
            $('.bg-lottery').removeClass('bg-lottery-award');
            $('.ready-box').show().removeClass('ready-box-playing');
            $('.award-box').hide();
        });

    });

    var t = 8; //預設動畫秒數
    //動畫後開獎倒數
    function showTime() {
        if (t == 0) {
            $('.ready-box').hide();
            $('.img-spaceship').hide()
            $('.award-box').show();
            $('.bg-lottery').removeClass('bg-lottery-playing');
            $('.bg-lottery').addClass('bg-lottery-award');

            $('.carousel').carousel(0);

            clearTimeout('showTime()');
        } else {
            t -= 1;
            setTimeout('showTime()', 1000);
        }
    }
</script>