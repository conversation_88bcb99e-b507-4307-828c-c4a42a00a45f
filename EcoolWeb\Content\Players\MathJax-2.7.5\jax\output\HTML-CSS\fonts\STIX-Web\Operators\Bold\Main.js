/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX-Web/Operators/Bold/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXMathJax_Operators-bold"]={directory:"Operators/Bold",family:"STIXMathJax_Operators",weight:"bold",testString:"\u00A0\u2206\u220A\u220C\u220F\u2210\u2211\u221F\u222C\u222D\u222E\u222F\u2230\u2231\u2232",32:[0,0,250,0,0],160:[0,0,250,0,0],8710:[676,0,681,23,658],8714:[499,-35,500,60,440],8716:[680,146,750,82,668],8719:[763,259,1000,37,963],8720:[763,259,982,28,954],8721:[763,259,914,40,873],8735:[584,0,685,50,634],8748:[824,320,863,32,1043],8749:[824,320,1174,32,1354],8750:[824,320,591,30,731],8751:[824,320,903,32,1043],8752:[824,320,1214,32,1354],8753:[824,320,593,32,733],8754:[824,320,593,32,733],8755:[824,320,593,32,733],8758:[575,41,554,190,364],8759:[575,41,750,68,683],8760:[543,-209,750,66,685],8761:[543,37,750,66,686],8762:[575,41,750,66,685],8763:[565,59,750,67,682],8766:[419,-85,750,68,683],8767:[484,-67,750,66,684],8772:[530,54,750,68,683],8775:[642,152,750,68,683],8777:[583,48,750,68,683],8779:[613,109,750,68,683],8780:[568,60,750,68,683],8788:[483,-50,932,68,864],8789:[483,-50,932,68,864],8792:[761,-107,750,68,682],8793:[836,-107,750,68,682],8794:[836,-107,750,68,682],8795:[841,-107,750,68,682],8797:[838,-107,750,55,735],8798:[721,-107,750,68,682],8799:[880,-107,750,68,682],8802:[688,156,750,68,682],8803:[592,57,750,68,682],8813:[591,87,750,68,683],8820:[780,282,750,67,682],8821:[780,282,750,67,682],8824:[824,316,750,80,670],8825:[824,316,750,80,670],8836:[680,146,750,82,668],8837:[680,146,750,82,668],8844:[541,33,650,66,584],8845:[541,33,650,66,584],8860:[634,130,864,50,814],8870:[676,0,555,91,464],8871:[676,0,555,91,464],8875:[676,0,944,91,856],8880:[543,38,750,98,652],8881:[543,38,750,98,652],8886:[436,-96,1216,50,1166],8887:[436,-96,1216,50,1166],8889:[563,57,750,65,685],8893:[697,28,640,52,588],8894:[630,0,750,60,690],8895:[662,158,910,45,865],8896:[763,259,977,54,923],8897:[763,259,977,54,923],8898:[768,264,961,94,867],8899:[768,264,961,94,867],8903:[595,63,750,66,685],8917:[690,189,685,48,637],8924:[627,120,750,80,670],8925:[627,120,750,80,670],8930:[792,241,750,87,663],8931:[792,241,750,87,663],8944:[579,75,977,162,815],8950:[735,13,750,82,668],8957:[735,13,750,82,668],10764:[824,320,1484,32,1664],10765:[824,320,593,32,733],10766:[824,320,593,32,733],10767:[824,320,593,32,733],10768:[824,320,593,32,733],10769:[824,320,593,32,733],10770:[824,320,613,32,733],10771:[824,320,593,32,733],10772:[824,320,675,32,735],10773:[824,320,593,32,733],10774:[824,320,623,32,733],10775:[824,320,791,32,871],10776:[824,320,633,32,733],10777:[824,320,653,32,733],10778:[824,320,653,32,733],10779:[959,320,557,32,737],10780:[824,455,557,32,737],10786:[894,57,750,65,685],10787:[736,57,750,65,685],10788:[746,57,750,65,685],10789:[563,287,750,65,685],10790:[563,240,750,65,685],10791:[563,247,780,65,778],10794:[297,37,750,66,685],10795:[543,37,750,66,685],10796:[543,37,750,66,685],10800:[745,33,702,66,636],10801:[538,191,702,66,636],10802:[538,59,702,66,636],10851:[536,379,640,52,588],10854:[399,161,750,68,682],10855:[775,-27,750,68,682],10858:[565,-132,750,67,682],10861:[759,60,750,68,683],10862:[884,-107,750,68,682],10863:[752,-26,750,68,683],10864:[680,176,750,68,683],10865:[665,159,750,65,685],10866:[665,159,750,65,685],10867:[568,60,750,67,682],10909:[689,183,750,67,682],10910:[689,183,750,67,682],10941:[547,13,750,82,668],10942:[547,13,750,82,668],57358:[819,339,750,80,670],57359:[742,235,750,80,670],57360:[742,235,750,80,670],57361:[819,339,750,80,670],57379:[742,235,750,68,683],57381:[852,345,750,67,683],57384:[672,166,1000,38,961],57385:[672,166,1000,38,961],57399:[672,166,750,67,682],57421:[553,47,750,68,683],57424:[672,166,750,87,663],57425:[672,166,750,87,663],57426:[574,69,750,68,683],57435:[574,-16,750,68,683],57436:[553,31,750,68,683],57453:[835,113,750,82,668],57454:[835,113,750,82,668],57455:[835,113,750,82,668],57456:[835,113,750,82,668],57470:[738,230,750,80,670],57471:[742,234,750,80,670],57472:[819,337,750,80,670],57473:[820,342,750,91,681],57474:[742,235,750,80,670],57475:[742,234,750,80,670],57476:[738,230,750,80,670],57477:[742,234,750,80,670],58307:[747,243,750,68,683],58308:[747,243,750,68,683]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"STIXMathJax_Operators-bold"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Operators/Bold/Main.js"]);
