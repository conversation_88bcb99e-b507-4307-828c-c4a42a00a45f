﻿using ECOOL_APP.EF;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Imaging;
using System.Linq;
using System.Web;
using ZXing;
using ZXing.Common;
using ZXing.QrCode;

namespace EcoolWeb.Util
{
    public class CreBarcodeimageService
    {
        public void GenerateCode39(string text, int? width, int? height, string savePath)
        {
            BarcodeWriter writer = new BarcodeWriter();
            writer.Format = BarcodeFormat.CODE_39;

            writer.Renderer = new ZXing.Rendering.BitmapRenderer
            {
                Background = Color.White,
            };

            EncodingOptions options = new EncodingOptions()
            {
                Margin = 0,
            };

            if (width != null)
            {
                options.Width = (int)width;
            }

            if (height != null)
            {
                options.Height = (int)height;
            }

            writer.Options = options;
            Bitmap map = writer.Write(text);

            map.Save(savePath, ImageFormat.Png);
            map.Dispose();
        }

        /// <summary>
        /// 抓取 上傳的實際路徑 =>FilePath
        /// </summary>
        /// <param name="SysPath"></param>
        /// <param name="KeyValue"></param>
        /// <returns></returns>
        public string GetSysPath(string SysPath, string KeyValue)
        {
            return HttpContext.Current.Server.MapPath(SharedGlobal.FilePath) + @"\" + SysPath.ToString() + @"\" + KeyValue.ToString();
        }

        /// <summary>
        /// 抓取檔案實際路徑  FilePath
        /// </summary>
        /// <param name="SysPath"></param>
        /// <param name="KeyValue"></param>
        /// <param name="FileName"></param>
        /// <param name="otherPath"></param>
        /// <returns></returns>
        public string GetSysPath(string SysPath, string KeyValue, string FileName, string otherPath = "")
        {
            string tempPath = this.GetSysPath(SysPath, KeyValue);

            if (!string.IsNullOrWhiteSpace(otherPath))
            {
                tempPath = tempPath + "\\" + otherPath;
            }

            tempPath = tempPath + "\\" + FileName;
            return tempPath;
        }
        public void CreateQRCode(string text, int? width, int? height, string savePath)
        {
            BarcodeWriter writer = new BarcodeWriter();
            writer.Format = BarcodeFormat.QR_CODE;

            writer.Renderer = new ZXing.Rendering.BitmapRenderer
            {
                Background = Color.White,
            };

            QrCodeEncodingOptions options = new QrCodeEncodingOptions
            {
                CharacterSet = "UTF-8",  //編碼格式 UTF-8  中文才不會出現亂
                Margin = 0,
            };

            if (width != null)
            {
                options.Width = (int)width;
            }

            if (height != null)
            {
                options.Height = (int)height;
            }

            writer.Options = options;
            Bitmap map = writer.Write(text);

            map.Save(savePath, ImageFormat.Png);
            map.Dispose();
        }
    }
}