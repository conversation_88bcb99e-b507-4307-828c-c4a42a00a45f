﻿@using (Html.BeginCollectionItem("AddPrizeExchangeItembtn"))
{
    var Index = Html.GetIndex("AddPrizeExchangeItembtn");

<div class="panel-footer">
    <div class="row">
        <div class="col-md-12 col-xs-12 text-right">
            <span class="input-group-btn">
                <button class="btn btn-info btn-sm" type="button" onclick="onAddDetailsPrizeLotteryItem()">
                    <i class="fa fa-plus-circle"></i>   增加兌換獎品
                </button>
            </span>
        </div>
    </div>
</div>
}