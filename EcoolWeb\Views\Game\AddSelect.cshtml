﻿@model GameAddSelectViewModel
@{
    ViewBag.Title = ViewBag.Panel_Title;
    Layout = "~/Views/Shared/_LayoutGame.cshtml";
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@using (Html.BeginForm("AddSelect", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1" }))
{
    @Html.AntiForgeryToken()

    <div class="panel panel-ZZZ" name="TOP">
        <div class="panel-heading text-center">
            @Html.BarTitle()
        </div>
        <div class="panel-body">
            <div class="form-horizontal">
                <h4><strong>選擇活動類行模式</strong></h4>
                <br /><br />
                <div class="form-group text-center">
                    <div class="col-md-offset-1 col-md-4">
                        <a role="button" href='@Url.Action("Edit",(string)ViewBag.BRE_NO)' class="btn btn-default btn-block btn-lg"><i class="fa fa-plus-circle"></i>  一般活動模式</a>
                    </div>
                    <div class="col-md-4 col-md-offset-1 ">
                        <a role="button" href='@Url.Action("EditQA",(string)ViewBag.BRE_NO)' class="btn btn-default btn-block btn-lg"><i class="fa fa-plus-circle"></i>  有獎徵答模式</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

}