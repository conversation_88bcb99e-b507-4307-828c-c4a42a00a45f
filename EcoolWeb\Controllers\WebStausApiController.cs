﻿using EcoolWeb.CustomAttribute;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using System.Web.Http.Cors;

namespace EcoolWeb.Controllers
{
    public class WebStausApiController : ApiBase
    {
        [AllowAnonymous]
        [EnableCors(origins: "*", headers: "*", methods: "*")]
        public IHttpActionResult Post()
        {
            return StatusCode(HttpStatusCode.OK);
        }
    }
}