﻿@model GAAI01CareFoUserViewModel

@using (Html.BeginCollectionItem("CareFoUser"))
{
    var Index = Html.GetIndex("CareFoUser");

    @Html.CheckBoxFor(m => m.Checked)

    @Html.HiddenFor(m => m.SCHOOL_NO)
    @Html.HiddenFor(m => m.CLASS_NO)

    @Html.HiddenFor(m => m.USER_NO)

    @Html.HiddenFor(m => m.NAME)

    @Html.HiddenFor(m => m.SNAME)
    @Html.HiddenFor(m => m.SEAT_NO)
    @Html.HiddenFor(m => m.UN_WEAR_COUNT)
}