﻿@model GAAI01WearUserMemoViewModel

@using (Html.BeginCollectionItem("Users"))
{
    var Index = Html.GetIndex("Users");

    <li class="list-group-item clearfix">
        <div class="row">
            <div class="col-sm-1">
                @Html.HiddenFor(m => m.USER_NO)

                @Html.HiddenFor(m => m.SEAT_NO)
                @Html.HiddenFor(m => m.NAME)
                @Html.HiddenFor(m => m.SNAME)
                @Html.HiddenFor(m => m.IS_WEAR)

                @Html.DisplayFor(m => m.SEAT_NO)
            </div>
            <div class="col-sm-3">
                @Html.DisplayFor(m => m.NAME)
            </div>
            <div class="col-sm-3">
                @if (Model.IS_WEAR)
                {
                    <strong>有</strong>
                }
                else
                {
                    <strong style="color:red">沒有</strong>
                }
            </div>

            <div class="col-sm-5">
                @if (Model.IS_WEAR == false)
                {
                    @Html.DropDownListFor(m => m.UN_WEAR_TYPE, GAAT02_U.GetUnWearTypeItems(Model.UN_WEAR_TYPE), new { @class = "form-control form-control-required" })
                }
            </div>
        </div>
    </li>

}