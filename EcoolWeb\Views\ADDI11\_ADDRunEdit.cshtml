﻿@model ADDI11MyRunLogDataViewModel
@{
    //Layout = _LayoutEmpty;
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@*@using (Html.BeginForm("_ADDRunEditSAVE", "ADDI11", FormMethod.Post, new { name = "form1", id= "editorRows", enctype = "multipart/form-data" }))
{*@

    @Html.HiddenFor(m => m.SCHOOL_NO)
    @Html.HiddenFor(m => m.USER_NO)
@Html.HiddenFor(m=>m.RUN_ID)
<div id="editorRows">
    <img src="~/Content/img/web-bar-Run.png" style="width:100%" class="img-responsive " alt="Responsive image" />

    <div class="Div-EZ-ADDI06">

        <div class="form-horizontal">
            <div class="form-group">
                @Html.LabelFor(model => model.CLASS_NO, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.EditorFor(model => model.CLASS_NO, new { htmlAttributes = new { @class = "form-control", disabled = "disabled" } })
                    @Html.HiddenFor(model => model.CLASS_NO)
                    @Html.ValidationMessageFor(model => model.CLASS_NO, "", new { @class = "text-danger" })
                </div>

            </div>
            <div class="form-group">
                @Html.LabelFor(model => model.SEAT_NO, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.EditorFor(model => model.SEAT_NO, new { htmlAttributes = new { @class = "form-control", disabled = "disabled" } })
                    @Html.HiddenFor(model => model.SEAT_NO)
                    @Html.ValidationMessageFor(model => model.SEAT_NO, "", new { @class = "text-danger" })
                </div>


            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.SNAME, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.EditorFor(model => model.SNAME, new { htmlAttributes = new { @class = "form-control", disabled = "disabled" } })
                    @Html.HiddenFor(model => model.SNAME)
                    @Html.ValidationMessageFor(model => model.SNAME, "", new { @class = "text-danger" })
                </div>


            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.LAP, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.EditorFor(model => model.LAP, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.HiddenFor(model => model.LAP)
                    @Html.ValidationMessageFor(model => model.LAP, "", new { @class = "text-danger" })
                </div>


            </div>
          
            <div class="form-group">
                @Html.LabelFor(model => model.RUN_DATE, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.EditorFor(model => model.RUN_DATE, new { htmlAttributes = new { @class = "form-control", disabled = "disabled" } })
                    @Html.HiddenFor(model => model.RUN_DATE)
                    @Html.ValidationMessageFor(model => model.RUN_DATE, "", new { @class = "text-danger" })
                </div>


            </div>
            <div class="form-group">
                <div class="col-md-offset-5 col-md-3">
                    <button value="Save" class="btn btn-default" onclick="OnclickCardNO();">
                        確定送出
                    </button>
                </div>

            </div>
        </div>
    </div>
</div>
@*}*@
<script>
    
     function OnclickCardNO() {
            $("#StatusMessageDiv").val('');
            $(window).unbind('beforeunload');

            var SCHOOL_NO1 = "";
         var RUN_DATE = $("#RUN_DATE").val();
         var RUN_ID = $("#RUN_ID").val();
            var USER_NO = $('#USER_NO').val();
            var LAP = $("#LAP").val();
           // var ONE_LAP_M = $("#ONE_LAP_M").val();
            SCHOOL_NO1 = $("#SCHOOL_NO").val();
           
            var data = {
                "USER_NO": USER_NO,
                "SCHOOL_NO": SCHOOL_NO1,
                "LAP": LAP,
                "RUN_DATE":RUN_DATE,
                "RUN_ID": RUN_ID
         };
         var data1 = {
             "USER_NO": USER_NO,
             "RunDtae": RUN_DATE,
             "LAP": LAP,
             "RUN_ID": RUN_ID
         }
           // $("div[id='loading']").attr("style", "width: 100%; height: 100%; background-color: black; z-index: 999; position: fixed; left: 0px; top: 0px;");
            if (LAP == 0) {
                alert("請輸入圈數");

            }

            else {
            
                $.ajax({

                                  url: '@Url.Action("_ADDRunEditSAVE", "ADDI11")',
                data: data,
                    cache: false,
                    contentType: 'json',
                    success: function (hdt) {
                        console.log(hdt);
                        var res = $.parseJSON(hdt);

                        if (res.Success == "flase") {
                            // $('#loading').hide();
                            $.ajax({

                                        url: '@Url.Action("_ADDRunEdit", (string)ViewBag.BRE_NO)',
                                        data: data1,
                                        cache: false,
                                        success: function (html) {
                                            $("#editorRows").html('');
                                            $("#editorRows").prepend(html);
                                        }


                                    });

                        }
                        else {
                          
                        }
                    }



                });

            }

          
            }


</script>