﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'table', 'eo', {
	border: 'Bordero',
	caption: 'Tabeltitolo',
	cell: {
		menu: 'Ĉelo',
		insertBefore: 'Enmeti Ĉelon Antaŭ',
		insertAfter: 'Enmeti Ĉelon Post',
		deleteCell: 'Forigi la Ĉelojn',
		merge: 'Kunfandi la Ĉelojn',
		mergeRight: 'Kunfandi dekstren',
		mergeDown: 'Kunfandi malsupren ',
		splitHorizontal: 'Horizontale dividi',
		splitVertical: 'Vertikale dividi',
		title: 'Ĉelatributoj',
		cellType: 'Ĉeltipo',
		rowSpan: 'Kunfando de linioj',
		colSpan: 'Kunfando de kolumnoj',
		wordWrap: 'Cezuro',
		hAlign: 'Horizontala ĝisrandigo',
		vAlign: 'Vertikala ĝisrandigo',
		alignBaseline: 'Malsupro de la teksto',
		bgColor: 'Fonkoloro',
		borderColor: 'Borderkoloro',
		data: 'Datenoj',
		header: 'Supra paĝotitolo',
		yes: 'Jes',
		no: 'No',
		invalidWidth: 'Ĉellarĝo devas esti nombro.',
		invalidHeight: 'Ĉelalto devas esti nombro.',
		invalidRowSpan: 'Kunfando de linioj devas esti entjera nombro.',
		invalidColSpan: 'Kunfando de kolumnoj devas esti entjera nombro.',
		chooseColor: 'Elektu'
	},
	cellPad: 'Interna Marĝeno de la ĉeloj',
	cellSpace: 'Spaco inter la Ĉeloj',
	column: {
		menu: 'Kolumno',
		insertBefore: 'Enmeti kolumnon antaŭ',
		insertAfter: 'Enmeti kolumnon post',
		deleteColumn: 'Forigi Kolumnojn'
	},
	columns: 'Kolumnoj',
	deleteTable: 'Forigi Tabelon',
	headers: 'Supraj Paĝotitoloj',
	headersBoth: 'Ambaŭ',
	headersColumn: 'Unua kolumno',
	headersNone: 'Neniu',
	headersRow: 'Unua linio',
	invalidBorder: 'La bordergrando devas esti nombro.',
	invalidCellPadding: 'La interna marĝeno en la ĉeloj devas esti pozitiva nombro.',
	invalidCellSpacing: 'La spaco inter la ĉeloj devas esti pozitiva nombro.',
	invalidCols: 'La nombro de la kolumnoj devas superi 0.',
	invalidHeight: 'La tabelalto devas esti nombro.',
	invalidRows: 'La nombro de la linioj devas superi 0.',
	invalidWidth: 'La tabellarĝo devas esti nombro.',
	menu: 'Atributoj de Tabelo',
	row: {
		menu: 'Linio',
		insertBefore: 'Enmeti linion antaŭ',
		insertAfter: 'Enmeti linion post',
		deleteRow: 'Forigi Liniojn'
	},
	rows: 'Linioj',
	summary: 'Resumo',
	title: 'Atributoj de Tabelo',
	toolbar: 'Tabelo',
	widthPc: 'elcentoj',
	widthPx: 'Rastrumeroj',
	widthUnit: 'unuo de larĝo'
} );
