﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Text;

namespace ECOOL_APP.EF
{
    public class GameLotteryPeopleViewModel
    {
        public string OrderBy { get; set; }

        /// <summary>
        ///抽獎id
        /// </summary>
        [DisplayName("抽獎id")]
        public string LOTTERY_NO { get; set; }

        /// <summary>
        ///項次
        /// </summary>
        [DisplayName("項次")]
        public string ITEM_NO { get; set; }

        /// <summary>
        ///暫存使用者帳號
        /// </summary>
        [DisplayName("暫存使用者帳號")]
        public string TEMP_USER_ID { get; set; }

        /// <summary>
        ///卡號
        /// </summary>
        [DisplayName("卡號")]
        public string GAME_USER_ID { get; set; }

        /// <summary>
        ///姓名
        /// </summary>
        [DisplayName("姓名")]
        public string NAME { get; set; }

        /// <summary>
        /// 身份
        /// </summary>
        [DisplayName("身份")]
        public string GAME_USER_TYPE { get; set; }

        /// <summary>
        /// 身份
        /// </summary>
        [DisplayName("身份")]
        public string GAME_USER_TYPE_DESC { get; set; }

        [DisplayName("電話")]
        public string PHONE { get; set; }

        /// <summary>
        ///帳號
        /// </summary>
        [DisplayName("學號")]
        public string USER_NO { get; set; }

        /// <summary>
        ///學校
        /// </summary>
        [DisplayName("學校")]
        public string SCHOOL_NO { get; set; }

        [DisplayName("學校")]
        public string SHORT_NAME { get; set; }

        /// <summary>
        ///簡稱
        /// </summary>
        [DisplayName("簡稱")]
        public string SNAME { get; set; }

        /// <summary>
        ///性別
        /// </summary>
        [DisplayName("性別")]
        public string SEX { get; set; }

        /// <summary>
        ///年級
        /// </summary>
        [DisplayName("年級")]
        public byte? GRADE { get; set; }

        /// <summary>
        ///班級
        /// </summary>
        [DisplayName("班級")]
        public string CLASS_NO { get; set; }

        /// <summary>
        ///座號
        /// </summary>
        [DisplayName("座號")]
        public string SEAT_NO { get; set; }

        /// <summary>
        /// 是否領獎
        /// </summary>
        [DisplayName("是否領獎")]
        public bool RECEIVE_AWARD { get; set; }
    }
}