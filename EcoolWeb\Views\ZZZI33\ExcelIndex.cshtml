﻿@model ZZZI33IndexViewModel
@{
    ViewBag.Title = ViewBag.Panel_Title;
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

<button class="btn btn-sm btn-sys" type="button" onclick="onAdd()">回「新增」</button>


@using (Html.BeginForm("ExcelSave", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", enctype = "multipart/form-data" }))
{
    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })
    @Html.HiddenFor(m => m.Search.OrdercColumn)
    @Html.HiddenFor(m => m.Search.SyntaxName)
    @Html.HiddenFor(m => m.Search.Page)
    @Html.HiddenFor(m => m.Search.whereQUESTIONNAIRE_ID)
    @Html.HiddenFor(m => m.Search.whereSearch)
    @Html.HiddenFor(m => m.Search.BackAction)
    @Html.HiddenFor(m => m.Search.BackController)
    @Html.HiddenFor(m => m.Search.whereIndex)


    <img src="~/Content/img/web-bar-vote.png" style="width:100%" class="img-responsive " alt="Responsive image" />
    <div class="Div-EZ-reader">
        <div class="form-horizontal">
            <label class="control-label">@ViewBag.Panel_Title</label>
            <br /><br />
            <div class="form-group">
                <label class="control-label col-md-3" for="QUESTIONS_TXT">上傳Excel檔</label>
                <div class="col-md-9">
                    <input class="btn btn-default" type="file" name="files" placeholder="必填" />
                    @Html.ValidationMessage("files", "", new { @class = "text-danger" })
                </div>
            </div>
           
            <div class="form-group text-center">
                <button class="btn btn-default">上 傳 Excel </button>
            </div>
            <div class="form-group">
                <div class="col-md-offset-1 col-md-9">
                    <label class="text-danger">
                        上傳說明:<br />
                        1.上傳之 Excel 檔, 請依規定格式填寫(<a href="~/Content/ExcelSample/VoteAddSample.xlsx" target="_blank" class="btn-table-link">下載 Sample</a>)<br/>
                        2.檔名不要是中文、特殊符號之類
                    </label>
                </div>
            </div>
        </div>
    </div>
    <script type="text/javascript">
        function onAdd() {
            form1.action = '@Url.Action("Edit", (string)ViewBag.BRE_NO)';
            form1.submit();
        }
    </script>

}


