﻿@model ECOOL_APP.com.ecool.Models.DTO.SECI05ReadingPartialViewModel

@{
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
}

<div class="container">
    <h4>@ViewBag.Panel_Title</h4>
    @using (Html.BeginForm("_ReadingPartialEclipseDiv", "SECI05", FormMethod.Post))
    {
        <p class="text-danger">※ 閱讀單一類別80%以上</p>

        <div>
            @Html.DropDownListFor(m => m.WhereCLASS_NO, (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control", onchange = "this.form.submit()" })
        </div>
        <br />
        if (Model.ReadingPartialList != null && Model.ReadingPartialList.Count > 0)
        {
            <table class="table table-bordered table-hover">
                <thead class="text-primary">
                    <tr>
                        <th>@Html.DisplayNameFor(m => m.ReadingPartialList.FirstOrDefault().NAME)</th>
                        <th>@Html.DisplayNameFor(m => m.ReadingPartialList.FirstOrDefault().USER_NO)</th>
                        <th>@Html.DisplayNameFor(m => m.ReadingPartialList.FirstOrDefault().CLASS_NO)</th>
                        <th>@Html.DisplayNameFor(m => m.ReadingPartialList.FirstOrDefault().TYPE_NAME)</th>

                        <th>@Html.DisplayNameFor(m => m.ReadingPartialList.FirstOrDefault().SubQty)</th>
                        <th>@Html.DisplayNameFor(m => m.ReadingPartialList.FirstOrDefault().RATE)</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var u in Model.ReadingPartialList)
                    {
                    <tr class="LinkTR" onclick="window.open('@Url.Action("Index","SECI05",new {  whereUser_NO = u.USER_NO, WhereCLASS_NO = u.CLASS_NO })','_blank')">
                        <td>@u.NAME</td>
                        <td>@u.USER_NO</td>
                        <td>@u.CLASS_NO</td>
                        <td>@u.TYPE_NAME</td>

                        <td>@u.SubQty</td>
                        <td>@( (u.RATE * 100).ToString("F2") + "%" )</td>
                    </tr>
                    }
                </tbody>
            </table>
        }
        else
        {
            <div>尚無偏食狀況的學生。</div>
        }

    }

</div>


@section css{
    <style>
        .LinkTR:hover {
            cursor: pointer;
        }
    </style>
}
