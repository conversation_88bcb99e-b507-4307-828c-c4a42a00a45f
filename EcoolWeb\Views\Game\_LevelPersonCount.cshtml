﻿@model GameListLevelPersonCountViewModel

<link href="~/Content/colorbox/example4/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>
<script src="~/Scripts/Highcharts-6.0.6/code/highcharts.js"></script>
<script src="~/Scripts/Highcharts-6.0.6/code/modules/data.js"></script>
<script src="~/Scripts//Highcharts-6.0.6/code/modules/drilldown.js"></script>

<script src="~/Scripts/Blob.js"></script>
<script src="~/Scripts/FileSaver.js"></script>
<script src="~/Scripts/tableexport.js"></script>
<script src="~/Scripts/printThis/printThis.js"></script>

<br />
<div class="form-inline">
    <div class="col-xs-12 text-right">
        <button type="button" class="btn btn-sm btn-sys" onclick="PrintBooK()">我要列印</button>
        <button class="btn btn-sm btn-sys" onclick='fn_save()'>另存新檔(Html)</button>
    </div>
</div>
<br />

<div id="tbData">
    <div class="panel panel-danger">
        <div class="panel-heading">
            <h3 class="panel-title">
                <strong>報名人數: 學生 @(Model.Student_Total_Count) 人，卡片 @(Model.Other_Total_Count) 人，合計 @(Model.Total_Count) 人</strong>
            </h3>
        </div>
    </div>

    @if (Model.IsStatistics)
    {
        <div class="panel panel-danger">
            <div class="panel-heading">
                <h3 class="panel-title">
                    <strong>本活動使用次數:關卡@(Model.UseTotal.Lv_Use_Total_Count) 人次，街頭藝人@(Model.UseTotal.Busker_Use_Total_Count) 人次，合計 @(Model.UseTotal.Use_Total_Count) 人次</strong>
                </h3>
            </div>
        </div>
        <div class="panel panel-danger">
            <div class="panel-heading">
                <h3 class="panel-title">
                    <strong>本活動使用點數: 關卡@(Model.UseTotal.Lv_Use_Total_Cash) 數，街頭藝人@(Model.UseTotal.Busker_Use_Total_Cash) 數，合計 @(Model.UseTotal.Use_Total_Cash) 數</strong>
                </h3>
            </div>
        </div>
    }

    <div class="panel panel-default">
        <div class="panel-heading">
            <h3 class="panel-title">完成關卡數量人數 (長條圖比例 = 完成關卡數量人數 ÷ 報名人數)，報名人數 = @(Model.Total_Count) 人</h3>
        </div>
        <div class="panel-body">
            @foreach (var item in Model.ListCountPassData)
            {
                <div class="form-group">
                    <strong>@item.Level_Count 關以上(含)</strong>
                    <br />
                    <div class="progress" href="@Url.Action("LevelPersonView", (string)ViewBag.BRE_NO,new {  GAME_NO= Model.GAME_NO,  LEVEL_COUNT=item.Level_Count})">
                        <div class="progress-bar progress-bar-info" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"
                             style="width: @(item.RATE)%;min-width: 6em;">
                            @(item.Level_Count_Person)
                            <text> 人</text>
                        </div>
                    </div>
                </div>
            }
        </div>
        @if (Model.IsStatistics)
        {
            <div class="col-sm-12">
                @if (Model.CountPassCharts != null)
                {
                    @Model.CountPassCharts
                }
            </div>
        }
    </div>

    <div class="panel panel-default">
        <div class="panel-heading">
            <h3 class="panel-title">完成某關卡人數 (長條圖比例 = 完成某關卡人數 ÷ 報名人數)，報名人數 = @(Model.Total_Count) 人</h3>
        </div>
        <div class="panel-body">
            @foreach (var item in Model.ListData)
            {
                <div class="form-group">
                    <strong>@item.LEVEL_NAME</strong>
                    <br />
                    <div class="progress" href="@Url.Action("LevelPersonView", (string)ViewBag.BRE_NO,new {  GAME_NO= Model.GAME_NO,  LEVEL_NO=item.LEVEL_NO,LEVEL_TYPE=item.LEVEL_TYPE })">
                        <div class="progress-bar progress-bar-info" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"
                             style="width: @(item.RATE)%;min-width: 6em;">
                            @(item.Person_Count)人
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>

    @if (SharedGlobal.HomeIndex != "ChildMonthIndex")
    {
        if (Model.IsStatistics)
        {
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title">完成某關卡次數/點數  (長條圖比例 = 完成某關卡次數 ÷ 本活動使用次數)，本活動使用次數 = @(Model.UseTotal.Use_Total_Count) 人次</h3>
                </div>
                <div class="panel-body">
                    @foreach (var item in Model.ListNumberCountData)
                    {
                        <div class="form-group">
                            <strong>@item.LEVEL_NAME</strong>
                            <br />
                            <div class="progress" href="@Url.Action("LevelPersonView", (string)ViewBag.BRE_NO,new {  GAME_NO= Model.GAME_NO,  LEVEL_NO=item.LEVEL_NO ,LEVEL_TYPE=item.LEVEL_TYPE})">
                                <div class="progress-bar progress-bar-info" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"
                                     style="width: @(item.RATE)%;min-width: 8em;">
                                    @(item.Number_Count)次/ @(item.Number_CASH)點
                                </div>
                            </div>
                        </div>
                    }
                </div>
            </div>
        }
    }
</div>

<script language="JavaScript">

    $(document).ready(function () {
        $(".progress").colorbox({ opacity: 0.82, width: "70%", innerHeight: "90%", maxHeight: "500px", scrolling: true, iframe: true, });
    });

    function PrintBooK() {
        $('#tbData').printThis();
    }

    function fn_save() {
        var blob = new Blob([document.getElementById('tbData').innerHTML], {
            type: "html/plain;charset=utf-8"
        });
        var strFile = "Report.html";
        saveAs(blob, strFile);
        return false;
    }
</script>