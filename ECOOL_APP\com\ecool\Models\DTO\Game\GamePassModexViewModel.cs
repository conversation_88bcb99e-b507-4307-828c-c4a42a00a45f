﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class GamePassModexViewModel
    {
        public string GAME_NO { get; set; }

        public string LEVEL_NO { get; set; }
        public bool IsQRCODE { get; set; }
        public bool UnApply { get; set; }
        public string Coupons_ITem { get; set; }
        public List<ADDT26_Q> aDDT26_Qs { get; set; }

        /// <summary>
        /// 主檔
        /// </summary>
        public GameEditMainViewModel Main { get; set; }

        /// <summary>
        /// 明細
        /// </summary>
        public List<GameEditDetailsViewModel> Details { get; set; }
    }
}