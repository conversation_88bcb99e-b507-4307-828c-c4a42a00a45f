﻿@{
    ViewBag.Title = "PortalIndex";
    //Layout = "~/Views/Shared/_LayoutPortal.cshtml";
    //EcoolWeb.Models.UserProfileHelper.SetLayoutCookieData(Layout);

    List<BET02> BET02List = ViewBag.BET02List;
}
<link href="~/Content/css/EzCss.css?@DateTime.Now.ToString("yyyy_MM_dd_H_mm_ss")" rel="stylesheet" />
<link href="~/Content/font-awesome/css/font-awesome.min.css" rel="stylesheet" />

@*@Html.Partial("_Notice")*@

<div>
    @*@Html.Partial("_BET02Partial")*@
</div>
<br />
@if (TempData["StatusMessage"] != null)
{
    string HtmlMsg = TempData["StatusMessage"].ToString().Replace("\r\n", "<br />");

    <div class="alert alert-dismissible alert-danger" id="StatusMessageDiv">
        @*<img src="~/Content/img/Warning.png" style="width:30px;height:30px" />*@
        <button class="close" type="button" data-dismiss="alert">×</button>
        <h1 id="StatusMessageHtmlMsg">@Html.Raw(HtmlMsg)</h1>
    </div>

}

@*<img src="~/Content/img/web-bar2-revise-03.png" class="img-responsive " alt="Responsive image" />
    <div class="row Div-EZ-purpose">
        <div class="col-md-12 Details">
            <div class="p-context">
               {string Explain = ViewBag.ECOOLEXPLAIN;
                    @Html.Raw(HttpUtility.HtmlDecode(@Explain))
                }
            </div>
        </div>
    </div>*@
<br />
<script>

    //$(document).ready(function () {
    //    if (window.location.protocol != 'https:') {
    //        location.href = location.href.replace("http://", "https://");
    //    }
    //});
</script>