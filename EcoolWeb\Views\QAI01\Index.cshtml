﻿@model EcoolWeb.Models.QAI01IndexViewModel
@{
    ViewBag.Title = "酷課雲影片作業一覽表";
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
}

<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>
    @if (AppMode)
    {
        @*<div class="Title_Secondary" style="font-size:20px;text-align:center">線上投稿一覽表</div>
            <br>*@
    }
    else
    { @Html.Partial("_Title_Secondary")
    }

    @using (Html.BeginForm("Index", "QAI01", FormMethod.Post, new { id = "QAI01", name = "form1" }))
    {

        if (ViewBag.VisibleADD == "Y")
        {
            <a role="button" href='@Url.Action("Create", "QAI01")' class="btn btn-sm btn-sys">
                指定影片作業
            </a>
        }

        <br />
        <div class="form-inline" role="form">
            <div class="form-group">
                <label class="control-label">影片名稱</label>
            </div>
            <div class="form-group">
                @Html.EditorFor(m => m.whereKeyword, new { htmlAttributes = new { @class = "form-control input-sm" } })
            </div>
            <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1)" />
            <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />
        </div>
        @Html.HiddenFor(m => m.OrdercColumn)
        @Html.HiddenFor(m => m.whereWritingStatus)
        @Html.HiddenFor(m => m.Page)

        <input type="hidden" id="doClear" value="false" />

        <div class="table-responsive">
            <div class="text-center">
                <table class="table-ecool table-92Per table-hover table-ecool-List">
                    <thead>
                        <tr>
                            <th class="AppGird" style="text-align: center;cursor:pointer;" onclick="doSort('CRE_DATE');">
                                建立日期

                            </th>
                            <th class="AppGird" style="text-align: center;cursor:pointer;" onclick="doSort('MEDIAS_NAME');">
                                影片名稱
                            </th>
                            <th class="AppGird" style="text-align: center;cursor:pointer;" onclick="doSort('MEMO');">
                                作業說明
                            </th>
                            <th class="AppGird" style="text-align: center;cursor:pointer;" onclick="doSort('END_DATE');">
                                完成日期
                            </th>
                            <th class="AppGird" style="text-align: center;cursor:pointer;" onclick="doSort('Q_USER_NAME');">
                                教師
                            </th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody>

                        @foreach (var item in Model.QAT01List)
                        {
                            <tr style="text-align: center;">
                                <td>
                                    @Html.DisplayFor(modelItem => item.CRE_DATE, "ShortDateTime")
                                </td>
                                <td style="text-align: left;white-space:normal">
                                    <button href='@Url.Action("PlasyVideo",new { QUESTIONS_ID =item.QUESTIONS_ID } )' class="btn-table-link">
                                        @item.MEDIAS_NAME
                                    </button>
                                </td>
                                <td style="text-align: center;cursor:pointer;" onclick="doSearch('whereUserNo','@item.MEMO');">
                                    @Html.DisplayFor(modelItem => item.MEMO)
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.END_DATE, "ShortDateTime")
                                </td>
                                <td style="text-align: center;cursor:pointer;" onclick="doSearch('whereUserNo','@item.Q_USER_NO');">
                                    @Html.DisplayFor(modelItem => item.Q_USER_NAME)
                                </td>
                                <td style="text-align: left;white-space:normal">
                                    @if (user.USER_TYPE == UserType.Student)
                                    {
                                        @Html.ActionLink("觀看完畢", "Finish",
                                        new
                                        {
                                            QUESTIONS_ID = item.QUESTIONS_ID
                                        }, new { @class = "btn btn-xs btn-Basic" })
                                    }
                                    else
                                    {
                                        @Html.ActionLink("檢視", "Details",
                                        new
                                        {
                                            QUESTIONS_ID = item.QUESTIONS_ID,
                                            BackAction = Model.BackAction,
                                            OrdercColumn = Model.OrdercColumn,
                                            whereKeyword = Model.whereKeyword,
                                            whereWritingStatus = Model.whereWritingStatus,
                                            whereCLASS_NO = Model.whereCLASS_NO,
                                            whereGrade = Model.whereGrade,
                                            Page = Model.Page
                                        }, new { @class = "btn btn-xs btn-Basic" })
                                    }


                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>

        </div>

        <div>
            @Html.Pager(Model.QAT01List.PageSize, Model.QAT01List.PageNumber, Model.QAT01List.TotalItemCount).Options(o => o
        .DisplayTemplate("BootstrapPagination")
        .MaxNrOfPages(5)
        .SetPreviousPageText("上頁")
        .SetNextPageText("下頁")
        )
        </div>
    }
    @section scripts{
        <script type="text/javascript">
            var targetFormID = '#QAI01';

            $(document).ready(function () {
                $(".btn-table-link").colorbox({ iframe: true, width: "100%", height: "100%", opacity: 0.82 });
            });



      

            function FunPageProc(page) {
                if ($(targetFormID).size() > 0) {
                    $('#Page').val(page)
                    $(targetFormID).submit();
                }
            };

            function doSort(SortCol) {
                $("#OrdercColumn").val(SortCol);
                FunPageProc(1)
            }

            function doSearch(ColName, whereValue) {
                $("#" + ColName).val(whereValue);
                FunPageProc(1)
            }

            function doSearchBool(ColName, whereValue) {
                $("#" + ColName).val(!whereValue);
                FunPageProc(1)
            }

            function todoClear() {
                $("#doClear").val(true);
                $("#OrdercColumn").val('');
                $("#whereKeyword").val('');
                $("#whereWritingStatus").val('');
                $("#Page").val(1);

                $(targetFormID).submit();
            }

        </script>
    }
