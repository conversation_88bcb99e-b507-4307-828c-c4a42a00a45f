﻿@model EcoolWeb.ViewModels.AWA003QueryViewModel
@{
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    ViewBag.Title = "教師酷幣總數排行榜";
    int RowNumber = 0;
    int ResetNumber = 0;
    string HidStyle = "";

    if (Model.IsPrint)
    {
        Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
        HidStyle = "display:none";
    }
}

<style type="text/css" media="print">
    /* 列印時隱藏 */
    .cScreen {
        display: none;
    }

    a[href]:after {
        content: none !important;
    }
</style>
<script src="~/Scripts/Blob.js"></script>
<script src="~/Scripts/FileSaver.js"></script>
<script src="~/Scripts/tableexport.js"></script>

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@using (Html.BeginForm("QueryTeacher", "AWA003", FormMethod.Post, new { name = "form1", id = "AWA003" }))
{
    <br />
    <div class="form-inline" style="@HidStyle">
        <div class="form-inline" role="form">
            <div class="form-group">
                <label class="control-label">學校</label>
            </div>
            <div class="form-group">
                @Html.DropDownList("whereSchoolNo", (IEnumerable<SelectListItem>)ViewBag.SchoolNoSelectItem, new { @class = "form-control input-sm", @onchange = "FunPageProc(1)" })
            </div>
            <div class="form-group">
                <label class="control-label">帳號/姓名</label>
            </div>
            <div class="form-group">
                @Html.EditorFor(m => m.whereKeyword, new { htmlAttributes = new { @class = "form-control input-sm" } })
                @Html.HiddenFor(m => m.OrdercColumn)
                @Html.HiddenFor(m => m.Page)
                @Html.HiddenFor(m => m.WhereIsMonthTop)
                @Html.HiddenFor(m => m.IsPrint)
                @Html.HiddenFor(m => m.IsToExcel)
            </div><br />
            <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1)" />
            <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />
            @if (user != null)
            {
                if (user.USER_TYPE != UserType.Student && user.USER_TYPE != UserType.Parents)
                {

                    if (!Model.IsPrint)
                    {
                        <button id="ButtonExcel" class="btn-yellow btn btn-sm cScreen" style="float:right" onclick="exportExcel()">匯出excel</button>
                        <button type="button" class="btn-yellow btn btn-sm" onclick="PrintBooK()" style="float:right;margin-right:5px;">我要列印</button>
                    }
                    else
                    {
                        <button type="button" class="btn-yellow btn btn-sm" onclick="PrintBooK()" style="float:right">我要列印</button>
                    }

                }
            }
        </div>
    </div>

}

<div class="form-inline cScreen" style="text-align:right;@HidStyle">
    <br />
    <button class="btn btn-xs btn-pink @(Model.WhereIsMonthTop == false ? "active":"")" type="button" onclick="todoClear();doMonthTop('false');">全部</button>
    <button class="btn btn-xs btn-pink @(Model.WhereIsMonthTop == true ? "active":"")" type="button" onclick="todoClear();doMonthTop('true');">月排行榜</button>
</div>

@if (!Model.IsPrint)
{
    <img src="~/Content/img/web-bar2-revise-24.png" style="width:100%" class="img-responsive " alt="Responsive image" />
}

<div class="@(Model.IsPrint ? "":"table-responsive")">
    <div class="text-center" id="tbData">
        <table class="@(Model.IsPrint ? "table table-bordered" : "table-ecool table-92Per table-hover table-ecool-AWA003")">
            <thead>
                <tr>
                    <th>序號</th>
                    <th>
                        學校
                    </th>
                    <th style="text-align: center;cursor:pointer;" onclick="doSort('NAME');">
                        姓名
                        <img id="NAME" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                    </th>
                    @if (Model.WhereIsMonthTop == true)
                    {
                    <th style="text-align: center;cursor:pointer;" onclick="doSort('CASH_ALL');">
                        本月累計
                        <img id="CASH_ALL" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                    </th>
                    <th style="text-align: center;cursor:pointer;" onclick="doSort('CASH_AVAILABLE');">
                        現有酷幣
                        <img id="CASH_AVAILABLE" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                    </th>
                    }
                    else
                    {
                    <th style="text-align: center;cursor:pointer;" onclick="doSort('CASH_AVAILABLE');">
                        現有酷幣
                        <img id="CASH_AVAILABLE" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                    </th>
                    <th style="text-align: center;cursor:pointer;" onclick="doSort('CASH_ALL');">
                        累計酷幣
                        <img id="CASH_ALL" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                    </th>

                    }
                </tr>
            </thead>
            <tbody>
                @foreach (var item in Model.VAWA003List)
                {
                    ResetNumber++;
                    RowNumber = Model.VAWA003List.PageSize * (Model.VAWA003List.PageNumber - 1) + (ResetNumber);
                    <tr>
                        <td>@RowNumber</td>
                        <td>
                            @Html.DisplayFor(modelItem => item.SHORT_NAME)
                        </td>
                        <td>
                            @Html.DisplayFor(modelItem => item.NAME)
                        </td>
                        @if (Model.WhereIsMonthTop == true)
                        {
                            <td class="bigger">
                                @Html.DisplayFor(modelItem => item.CASH_ALL)
                            </td>
                            <td class="bigger">
                               
                                @if (item.SUMCASH_AVAILABLE > item.CASH_ALL)
                                {
                                    @Html.DisplayFor(modelItem => item.SUMCASH_AVAILABLE)

                                }
                                else
                                {
                                    @Html.DisplayFor(modelItem => item.CASH_ALL)

                                }
                            </td>
                        }
                        else
                        {
                            <td class="bigger">
                       
                                @if (item.SUMCASH_AVAILABLE > item.CASH_AVAILABLE)
                                {
                                    @Html.DisplayFor(modelItem => item.SUMCASH_AVAILABLE)

                                }
                                else
                                {
                                    @Html.DisplayFor(modelItem => item.CASH_AVAILABLE)

                                }
                            </td>
                            <td class="bigger">
                                @Html.DisplayFor(modelItem => item.CASH_ALL)
                            </td>
                        }
                    </tr>
                }
            </tbody>
        </table>
    </div>
</div>
<div>
    @Html.Pager(Model.VAWA003List.PageSize, Model.VAWA003List.PageNumber, Model.VAWA003List.TotalItemCount).Options(o => o
              .DisplayTemplate("BootstrapPagination")
             .MaxNrOfPages(5)
             .SetPreviousPageText("上頁")
             .SetNextPageText("下頁")
         )
</div>

<div style="height:15px"></div>
<div style="text-align:center">
    @if (Request["Awat"] != null)
    {
        <a href='@Url.Action("Awat2Q02", "Awat2")' class="btn btn-default">
            返回
        </a>
        <br />
    }
</div>

@section scripts{
    <script type="text/javascript">

        var targetFormID = '#AWA003';

          window.onload = function () {
                if ($('#@Html.IdFor(m=>m.IsPrint)').val() == "true" && $('#@Html.IdFor(m=>m.IsToExcel)').val() != "true"  ) {
                    window.print()
                }
        }
          function exportExcel() {

                $("#AWA003").attr("enctype", "multipart/form-data");
                $("#AWA003").attr("action", "@Url.Action("TeacherExportExcel", (string)ViewBag.BRE_NO)");
                $("#AWA003").submit();

        }

        // $(function () {
        //        $('#ButtonExcel').click(function () {
        //            var blob = new Blob([document.getElementById('tbData').innerHTML], {
        //                type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8"
        //            });
        //            var strFile = "Report.xls";
        //            saveAs(blob, strFile);
        //            return false;
        //        });
        //});
  
        function PrintBooK()
        {
            $('#@Html.IdFor(m=>m.IsPrint)').val(true)
            $(targetFormID).attr('action','@Url.Action("QueryTeacher", "AWA003")')
            $(targetFormID).attr('target', '_blank').submit().removeAttr('target');
            $('#@Html.IdFor(m=>m.IsPrint)').val(false)
         }

           function doMonthTop(val) {
                $("#@Html.IdFor(m=>m.WhereIsMonthTop)").val(val);
                FunPageProc(1)
            }

        function FunPageProc(page) {
            if ($(targetFormID).size() > 0) {
                $('#Page').val(page)
                $(targetFormID).submit();
            }
        };

        function doSort(SortCol) {
            $("#OrdercColumn").val(SortCol);
            FunPageProc(1)
        }

        function todoClear() {
            ////重設
            $(targetFormID).find(":input,:selected").each(function (i) {

                var type = $(this).attr('type')
                var InPreadonly = $(this).attr('readonly')
                var tag = this.tagName.toLowerCase(); // normalize case

                if (InPreadonly == false || InPreadonly == undefined) {

                    if (type == 'radio' || type == 'checkbox') {
                        if ($(this).attr("title") == 'Default') {
                            this.checked = true;
                        }
                        else {
                            this.checked = false;
                        }
                    }
                    else if (tag == 'select') { //下拉式選單
                        this.selectedIndex = 0;
                    }
                    else if (type == 'text' || type == 'hidden' || type == 'password' || type == 'textarea') {
                        this.value = '';
                    }
                }
            });

            $(targetFormID).submit();
        }
    </script>
}