﻿using DotNet.Highcharts;
using MvcPaging;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Text;

namespace ECOOL_APP.EF
{
    [Serializable]
    public class ADDI11MyRunLogViewModel
    {
        /// <summary>
        /// 判斷從UserController Index 連結進來的
        /// </summary>
        public bool? WhereIsColorboxForUser { get; set; }

        public string WhereSCHOOL_NO { get; set; }

        public string WhereUSER_NO { get; set; }
    
        public string WhereCLASS_NO { get; set; }
        public string WhereKM { get; set; }
        public ADDI11MyRunLogRankDataViewModel MyRunRank { get; set; }

        public ADDI11MyClassRunLogRankDataViewModel MyClassRun { get; set; }

        public List<ADDI11MyFriendsViewModel> MyFriends { get; set; }
        public List<ADDI11RunCityCount> RunCity { get; set; }
        /// <summary>
        /// 排序欄位
        /// </summary>
        public string OrdercColumn { get; set; }
        /// <summary>
        /// Desc or ASC
        /// </summary>
        public string SyntaxName { get; set; }
        public int Page { get; set; }

        /// <summary>
        /// 一頁筆數 預設筆數
        /// </summary>
        public int PageSize { get; set; }

        public IPagedList<ADDI11MyRunLogDataViewModel> ListData;

        public Highcharts MyRunColumnChart;
        public Highcharts FriendsCompareColumnChart;

        public List<ADDI11MyRunLogDataViewModel> ColumnListData;

        public ADDI11MyRunLogViewModel()
        {
            PageSize = 7;
        }
    }
}