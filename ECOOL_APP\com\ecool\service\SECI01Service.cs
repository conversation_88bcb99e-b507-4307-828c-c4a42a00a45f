﻿using com.ecool.sqlConnection;
using ECOOL_APP.com.ecool.Models.DTO;
using ECOOL_APP.EF;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ECOOL_APP.com.ecool.util;
using System.Web;
using System.IO;
using System.Text.RegularExpressions;

namespace ECOOL_APP.EF

{
    public class SECI01Service
    {
        /// <summary>
        /// 虛擬 Path
        /// </summary>
        /// <param name="SCHOOL_NO"></param>
        /// <param name="ART_GALLERY_NO"></param>
        /// <param name="FileName"></param>
        /// <returns></returns>
        public string GetDirectorySysMyPhotoPath(string SCHOOL_NO, string USER_NO, string FileName)
        {
            string TempPath = GetSetMyPhotoPath(SCHOOL_NO, USER_NO) + @"\" + FileName;

            if (System.IO.File.Exists(HttpContext.Current.Server.MapPath(TempPath)))
            {
                return UrlCustomHelper.Url_Content(TempPath);
            }

            return string.Empty;
        }

        private static string GetSetMyPhotoPath()
        {
            string ReturnImgUrl = string.Empty;

            string UploadImageRoot = System.Web.Configuration.WebConfigurationManager.AppSettings["UploadImageRoot"];
            if (UploadImageRoot == null) UploadImageRoot = @"~\Content\";

            string Child = "MyPhoto";
            ReturnImgUrl = $@"{UploadImageRoot}{Child}\";

            return ReturnImgUrl;
        }

        public static string GetSetMyPhotoPath(string SCHOOL_NO, string USER_NO)
        {
            string ReturnImgUrl = string.Empty;

            ReturnImgUrl = GetSetMyPhotoPath() + $@"{SCHOOL_NO}\{USER_NO}";

            return ReturnImgUrl;
        }

        public string GetSysMyPhotoPath(string SCHOOL_NO, string USER_NO)
        {
            return HttpContext.Current.Server.MapPath(GetSetMyPhotoPath()) + @"\" + SCHOOL_NO + @"\" + USER_NO + @"\";
        }

        /// <summary>
        /// 虛擬 Path
        /// </summary>
        /// <param name="SCHOOL_NO"></param>
        /// <param name="ART_GALLERY_NO"></param>
        /// <param name="FileName"></param>
        /// <returns></returns>
        public string GetDirectorySysArtGalleryPath(string SCHOOL_NO, string USER_NO, string FileName)
        {
            string TempPath = GetSysMyPhotoPath(SCHOOL_NO, USER_NO) + @"\" + FileName;

            if (System.IO.File.Exists(HttpContext.Current.Server.MapPath(TempPath)))
            {
                return Uri.EscapeUriString(UrlCustomHelper.Url_Content(TempPath));
            }

            return string.Empty;
        }

        public bool UpLoadPhotoFile(string SCHOOL_NO, string USER_NO, HttpPostedFileBase file, ref ECOOL_DEVEntities db, ref string Message)
        {
            string fileName = string.Empty;

            try
            {
                string tempPath = GetSysMyPhotoPath(SCHOOL_NO, USER_NO);

                if (Directory.Exists(tempPath) == false)
                {
                    Directory.CreateDirectory(tempPath);
                }
                else
                {
                    Directory.Delete(tempPath, true);
                    Directory.CreateDirectory(tempPath);
                }

                if (file != null && file.ContentLength > 0)
                {
                    fileName = Path.GetFileName(file.FileName);

                    Regex regexCode = new Regex(@".*\.(jpg|jpeg|png|gif|bmp)");
                    if (regexCode.IsMatch(fileName.ToLower()) == false)
                    {
                        Message = "請上傳 jpg、jpeg、png、gif、bmp 等格式的圖片";
                        return false;
                    }

                    if (file.ContentLength / 1024 > (1024 * 20)) // 20MB
                    {
                        Message = "上傳檔案不能超過20MB";
                        return false;
                    }

                    string UpLoadFile = tempPath + "\\" + fileName;

                    if (System.IO.File.Exists(UpLoadFile))
                    {
                        System.IO.File.Delete(UpLoadFile);
                    }

                    file.SaveAs(UpLoadFile);

                    var T01 = db.HRMT01.Where(a => a.SCHOOL_NO == SCHOOL_NO && a.USER_NO == USER_NO).FirstOrDefault();

                    if (T01 == null)
                    {
                        Message = "異常HRMT01無此資料";
                        return false;
                    }

                    T01.PHOTO = fileName;
                    db.SaveChanges();

                    return true;
                }
            }
            catch (Exception ex)
            {
                Message = "系統發生錯誤;原因:" + ex.Message;
                return false;
            }

            return false;
        }

        public bool DelPhotoData(string SCHOOL_NO, string USER_NO, ref ECOOL_DEVEntities db, ref string Message)
        {
            try
            {
                string tempPath = GetSysMyPhotoPath(SCHOOL_NO, USER_NO);
                if (Directory.Exists(tempPath) == true)
                {
                    Directory.Delete(tempPath, true);
                }

                var T01 = db.HRMT01.Where(a => a.SCHOOL_NO == SCHOOL_NO && a.USER_NO == USER_NO).FirstOrDefault();

                if (T01 == null)
                {
                    Message = "異常HRMT01無此資料";
                    return false;
                }

                T01.PHOTO = null; ;
                db.SaveChanges();

                return true;
            }
            catch (Exception ex)
            {
                Message = "系統發生錯誤;原因:" + ex.Message;
                return false;
            }
        }
    }
}