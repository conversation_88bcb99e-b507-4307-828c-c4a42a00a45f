﻿using Dapper;
using ECOOL_APP.EF;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.com.ecool.service
{
    public class CER001Service
    {
        #region 列表資料

        public CER001IndexViewModel GetListData(CER001IndexViewModel model, ref ECOOL_DEVEntities db)
        {
            var Accreditations = (from a in db.CERT02
                                  join b in db.CERT02_D on a.ACCREDITATION_ID equals b.ACCREDITATION_ID
                                  join c in db.CERT01 on a.ACCREDITATION_TYPE equals c.TYPE_ID
                                  where a.DEL_YN == SharedGlobal.N && a.SCHOOL_NO == model.WhereSCHOOL_NO
                                  select new CER001AccreditationsViewModel()
                                  {
                                      ACCREDITATION_ID = a.ACCREDITATION_ID,
                                      ACCREDITATION_NAME = a.ACCREDITATION_NAME,
                                      ACCREDITATION_TYPE = a.ACCREDITATION_TYPE,
                                      ITEM_NO = b.ITEM_NO,
                                      SUBJECT = b.SUBJECT,
                                      CONTENT = b.CONTENT,
                                      TYPE_NAME = c.TYPE_NAME,
                                      IsText=a.IsText
                                  });

            if (!string.IsNullOrWhiteSpace(model.WhereACCREDITATION_TYPE))
            {
                Accreditations = Accreditations.Where(a => a.ACCREDITATION_TYPE == model.WhereACCREDITATION_TYPE);
            }

            if (!string.IsNullOrWhiteSpace(model.WhereACCREDITATION_NAME))
            {
                Accreditations = Accreditations.Where(a => a.ACCREDITATION_ID == model.WhereACCREDITATION_NAME);
            }
            if (!string.IsNullOrWhiteSpace(model.WhereSearch))
            {
                Accreditations = Accreditations.Where(a => a.SUBJECT.Contains(model.WhereSearch));
            }
            if (!string.IsNullOrWhiteSpace(model.WhereSUBJECT_Item))
            {
                
                Accreditations = Accreditations.Where(a => a.ITEM_NO == model.WhereSUBJECT_Item );
            }
            Accreditations = Accreditations.OrderBy(a => a.TYPE_NAME).ThenBy(a => a.ACCREDITATION_NAME).ThenBy(a => a.ITEM_NO);

            model.Accreditations = Accreditations.ToListNoLock();

            model.cERT03s = db.CERT03_G.Where(x => x.SCHOOL_NO == model.WhereSCHOOL_NO).ToListNoLock();

            if (model?.WhereGRADE_SEMESTERs?.Count > 0 && model.WhereGRADE!=null)
            {
                model.cERT03s = model.cERT03s.Where(x => model.WhereGRADE_SEMESTERs.Contains($"{x.GRADE}_{x.SEMESTER}")).ToList();

                model.Accreditations = (from a in model.Accreditations
                                        join b in model.cERT03s on new { a.ACCREDITATION_ID, a.ITEM_NO } equals new { b.ACCREDITATION_ID, b.ITEM_NO }
                                         select a).ToList();
            }

            var Students = db.HRMT01.Where(a => a.SCHOOL_NO == model.WhereSCHOOL_NO && a.USER_TYPE == UserType.Student && UserStaus.OkUserStausList.Contains(a.USER_STATUS));

            if (model.WhereGRADE != null)
            {
                Students = Students.Where(a => a.GRADE == model.WhereGRADE);
            }

            if (!string.IsNullOrWhiteSpace(model.WhereCLASS_NO))
            {
                Students = Students.Where(a => a.CLASS_NO == model.WhereCLASS_NO);
            }

            if (!string.IsNullOrWhiteSpace(model.WhereUser))
            {
                Students = Students.Where(a => a.NAME.Contains(model.WhereUser.Trim()) || a.USER_NO.Contains(model.WhereUser.Trim()));
            }
       

            model.Students = Students.OrderBy(a => a.CLASS_NO).ThenBy(a => a.SEAT_NO).ToList();
            model.ListData = model.Students.ToEzPagedList(model.Page > 0 ? model.Page : 0, model.PageSize);
            if (model.Accreditations?.Count > 0 && model.Students?.Count > 0)
            {
                var ACCREDITATION_IDs = model.Accreditations.Select(a => a.ACCREDITATION_ID).ToList();

                var USER_NOs = model.ListData.Select(a => a.USER_NO).ToList();
               
                model.PassList = (from a in db.CERT05
                                  where ACCREDITATION_IDs.Contains(a.ACCREDITATION_ID)
                                  && USER_NOs.Contains(a.USER_NO)
                                  && a.SCHOOL_NO == model.WhereSCHOOL_NO
                                  select new CER001PassListViewModel()
                                  {
                                      ACCREDITATION_ID = a.ACCREDITATION_ID,
                                      ITEM_NO = a.ITEM_NO,
                                      SCHOOL_NO = a.SCHOOL_NO,
                                      USER_NO = a.USER_NO,
                                      PersonText =a.PersonText
                                  }).ToListNoLock();
            }

            List<HRMT01> PASS = new List<HRMT01>();
            List<HRMT01> NOPASS = new List<HRMT01>();

            if (!string.IsNullOrEmpty(model.WhereStatus))
            {
                foreach (var item in model.ListData)
                {
                    //學生
                    foreach (var Accreditation in model.Accreditations)
                    {
                        if (model.PassList.Where(x => x.ACCREDITATION_ID == Accreditation.ACCREDITATION_ID && x.ITEM_NO == Accreditation.ITEM_NO && x.SCHOOL_NO == item.SCHOOL_NO && x.USER_NO == item.USER_NO).Any())
                        {
                            PASS.Add(item);

                        }
                        else {

                            NOPASS.Add(item);
                        }
                        //護照明細
                    }

                }
                List<string> passUser = new List<string>();
               
                if (model.WhereStatus == "1") {
                    passUser = PASS.Select(x => x.USER_NO).ToList();
                    model.Students= model.Students.Where(x => passUser.Contains(x.USER_NO)).ToList();
                }
                else
                {
                    passUser = NOPASS.Select(x => x.USER_NO).ToList();
                    model.Students = model.Students.Where(x => passUser.Contains(x.USER_NO)).ToList();
                  
                }
            }
            return model;
        }

        #endregion 列表資料


        #region 列表資料

        public CER001IndexViewModel GetListGraduateData(CER001IndexViewModel model, ref ECOOL_DEVEntities db)
        {
            var Accreditations = (from a in db.CERT02
                                  join b in db.CERT02_D on a.ACCREDITATION_ID equals b.ACCREDITATION_ID
                                  join c in db.CERT01 on a.ACCREDITATION_TYPE equals c.TYPE_ID
                                  where a.DEL_YN == SharedGlobal.N && a.SCHOOL_NO == model.WhereSCHOOL_NO
                                  select new CER001AccreditationsViewModel()
                                  {
                                      ACCREDITATION_ID = a.ACCREDITATION_ID,
                                      ACCREDITATION_NAME = a.ACCREDITATION_NAME,
                                      ACCREDITATION_TYPE = a.ACCREDITATION_TYPE,
                                      ITEM_NO = b.ITEM_NO,
                                      SUBJECT = b.SUBJECT,
                                      CONTENT = b.CONTENT,
                                      TYPE_NAME = c.TYPE_NAME,
                                      IsText = a.IsText
                                  });

            if (!string.IsNullOrWhiteSpace(model.WhereACCREDITATION_TYPE))
            {
                Accreditations = Accreditations.Where(a => a.ACCREDITATION_TYPE == model.WhereACCREDITATION_TYPE);
            }

            if (!string.IsNullOrWhiteSpace(model.WhereACCREDITATION_NAME))
            {
                Accreditations = Accreditations.Where(a => a.ACCREDITATION_ID == model.WhereACCREDITATION_NAME);
            }
            if (!string.IsNullOrWhiteSpace(model.WhereSearch))
            {
                Accreditations = Accreditations.Where(a => a.SUBJECT.Contains(model.WhereSearch));
            }
            if (!string.IsNullOrWhiteSpace(model.WhereSUBJECT_Item))
            {

                Accreditations = Accreditations.Where(a => a.ITEM_NO == model.WhereSUBJECT_Item);
            }
            Accreditations = Accreditations.OrderBy(a => a.TYPE_NAME).ThenBy(a => a.ACCREDITATION_NAME).ThenBy(a => a.ITEM_NO);

            model.Accreditations = Accreditations.ToListNoLock();

            model.cERT03s = db.CERT03_G.Where(x => x.SCHOOL_NO == model.WhereSCHOOL_NO).ToListNoLock();

            if (model?.WhereGRADE_SEMESTERs?.Count > 0 && model.WhereGRADE != null)
            {
                model.cERT03s = model.cERT03s.Where(x => model.WhereGRADE_SEMESTERs.Contains($"{x.GRADE}_{x.SEMESTER}")).ToList();

                model.Accreditations = (from a in model.Accreditations
                                        join b in model.cERT03s on new { a.ACCREDITATION_ID, a.ITEM_NO } equals new { b.ACCREDITATION_ID, b.ITEM_NO }
                                        select a).ToList();
            }

            int SYear;
            int Semesters;

            SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);
            int lastyear = SYear - 6;
            var Students = db.HRMT01.Where(a => a.SCHOOL_NO == model.WhereSCHOOL_NO && a.USER_TYPE == UserType.Student && a.USER_STATUS==9&& (a.SYEAR== lastyear || a.SYEAR == lastyear-1) 
            &&a.GRADE==6);
            if (model.WhereSyaer != null) {

                Students = Students.Where(a => a.SYEAR == model.WhereSyaer);

            }
            if (model.WhereSyaer == null)
            {

                Students = Students.Where(a => a.SYEAR == lastyear);

            }
            if (model.WhereGRADE != null)
            {
                Students = Students.Where(a => a.GRADE == model.WhereGRADE);
            }

            if (!string.IsNullOrWhiteSpace(model.WhereCLASS_NO))
            {
                Students = Students.Where(a => a.CLASS_NO == model.WhereCLASS_NO);
            }

            if (!string.IsNullOrWhiteSpace(model.WhereUser))
            {
                Students = Students.Where(a => a.NAME.Contains(model.WhereUser.Trim()) || a.USER_NO.Contains(model.WhereUser.Trim()));
            }


            model.Students = Students.OrderBy(a => a.CLASS_NO).ThenBy(a => a.SEAT_NO).ToList();
            model.ListData = model.Students.ToEzPagedList(model.Page > 0 ? model.Page : 0, model.PageSize);
            if (model.Accreditations?.Count > 0 && model.Students?.Count > 0)
            {
                var ACCREDITATION_IDs = model.Accreditations.Select(a => a.ACCREDITATION_ID).ToList();

                var USER_NOs = model.ListData.Select(a => a.USER_NO).ToList();

                model.PassList = (from a in db.CERT05
                                  where ACCREDITATION_IDs.Contains(a.ACCREDITATION_ID)
                                  && USER_NOs.Contains(a.USER_NO)
                                  && a.SCHOOL_NO == model.WhereSCHOOL_NO
                                  select new CER001PassListViewModel()
                                  {
                                      ACCREDITATION_ID = a.ACCREDITATION_ID,
                                      ITEM_NO = a.ITEM_NO,
                                      SCHOOL_NO = a.SCHOOL_NO,
                                      USER_NO = a.USER_NO,
                                      PersonText = a.PersonText
                                  }).ToListNoLock();
            }

            List<HRMT01> PASS = new List<HRMT01>();
            List<HRMT01> NOPASS = new List<HRMT01>();

            if (!string.IsNullOrEmpty(model.WhereStatus))
            {
                foreach (var item in model.ListData)
                {
                    //學生
                    foreach (var Accreditation in model.Accreditations)
                    {
                        if (model.PassList.Where(x => x.ACCREDITATION_ID == Accreditation.ACCREDITATION_ID && x.ITEM_NO == Accreditation.ITEM_NO && x.SCHOOL_NO == item.SCHOOL_NO && x.USER_NO == item.USER_NO).Any())
                        {
                            PASS.Add(item);

                        }
                        else
                        {

                            NOPASS.Add(item);
                        }
                        //護照明細
                    }

                }
                List<string> passUser = new List<string>();

                if (model.WhereStatus == "1")
                {
                    passUser = PASS.Select(x => x.USER_NO).ToList();
                    model.Students = model.Students.Where(x => passUser.Contains(x.USER_NO)).ToList();
                }
                else
                {
                    passUser = NOPASS.Select(x => x.USER_NO).ToList();
                    model.Students = model.Students.Where(x => passUser.Contains(x.USER_NO)).ToList();

                }
            }
            return model;
        }

        #endregion 列表資料
    }
}