﻿
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Text;

namespace ECOOL_APP.EF
{
    public class ZZZI34ShareViewModel
    {
        /// <summary>
        ///流水號
        /// </summary>
        [DisplayName("流水號")]
        public string COMMENT_NO { get; set; }

        /// <summary>
        ///留言/推薦
        /// </summary>
        [DisplayName("留言/推薦")]
        public byte? COMMENT_TYPE { get; set; }

        /// <summary>
        ///照片 NO.
        /// </summary>
        [DisplayName("照片 NO.")]
        public string PHOTO_NO { get; set; }

        /// <summary>
        ///學校代碼
        /// </summary>
        [DisplayName("學校代碼")]
        public string SCHOOL_NO { get; set; }

        [DisplayName("學校")]
        public string SHORT_NAME { get; set; }
        
        /// <summary>
        ///帳號
        /// </summary>
        [DisplayName("帳號")]
        public string USER_NO { get; set; }

        /// <summary>
        ///班級
        /// </summary>
        [DisplayName("班級")]
        public string CLASS_NO { get; set; }

        /// <summary>
        ///姓名
        /// </summary>
        [DisplayName("姓名")]
        public string NICK_NAME { get; set; }

        /// <summary>
        ///內容
        /// </summary>
        [DisplayName("內容")]
        public string COMMENT { get; set; }

        /// <summary>
        ///建立日期
        /// </summary>
        [DisplayName("建立日期")]
        public DateTime? CRE_DATE { get; set; }

        /// <summary>
        ///給點
        /// </summary>
        [DisplayName("給點")]
        public short CASH { get; set; }

        /// <summary>
        ///ip
        /// </summary>
        [DisplayName("ip")]
        public string IP_ADDRESS { get; set; }

        /// <summary>
        ///狀態
        /// </summary>
        [DisplayName("狀態")]
        public byte? COMMENT_STATUS { get; set; }


    }
}