﻿using MvcPaging;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
  public  class GameBuskerManagerViewModel
    {
        /// <summary>
        /// 查詢
        /// </summary>
        public GameSearchViewModel Search { get; set; }


       public string WhereKeyIn { get; set; }

        public BuskerManagerEditViewModel ThisEdit { get; set; }


        public List<BuskerManagerEditViewModel> EditPeople{ get; set; }
        /// <summary>
        /// 一頁筆數 預設筆數
        /// </summary>
        public int PageSize { get; set; }

        public IPagedList<BuskerManagerEditViewModel> Edit { get; set; }


        /// <summary>
        /// 建構式 預設值 
        /// </summary>
        public GameBuskerManagerViewModel()
        {
            PageSize = 300;
        }
    }
}
