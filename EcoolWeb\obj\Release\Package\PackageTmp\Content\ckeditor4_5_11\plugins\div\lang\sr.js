﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'div', 'sr', {
	IdInputLabel: 'Id', // MISSING
	advisoryTitleInputLabel: 'Advisory Title', // MISSING
	cssClassInputLabel: 'Stylesheet Classes', // MISSING
	edit: 'Edit Div', // MISSING
	inlineStyleInputLabel: 'Inline Style', // MISSING
	langDirLTRLabel: 'Left to Right (LTR)', // MISSING
	langDirLabel: 'Language Direction', // MISSING
	langDirRTLLabel: 'Right to Left (RTL)', // MISSING
	languageCodeInputLabel: ' Language Code', // MISSING
	remove: 'Remove Div', // MISSING
	styleSelectLabel: 'Style', // MISSING
	title: 'Create Div Container', // MISSING
	toolbar: 'Create Div Container' // MISSING
} );
