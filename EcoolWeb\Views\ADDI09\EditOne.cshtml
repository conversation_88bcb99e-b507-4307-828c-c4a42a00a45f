﻿@model ECOOL_APP.com.ecool.Models.DTO.ADDI09EditOoneViewModel
@using ECOOL_APP.com.ecool.Models.DTO;
@using EcoolWeb.Util;

<link href="~/Content/css/EzCss.css" rel="stylesheet" />
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/buzz/1.2.1/buzz.min.js">
</script>
@{
    ViewBag.Title = ViewBag.Panel_Title;

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")
@Html.ValidationMessage("ErrorMsg", new { @class = "text-danger" })

@using (Html.BeginForm((string)ViewBag.Action, (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", enctype = "multipart/form-data" }))
{

    @Html.AntiForgeryToken()
    @Html.Hidden("SYS_TABLE_TYPE", (string)ViewBag.SYS_TABLE_TYPE)
    @Html.HiddenFor(model => model.USER_NAME)
    @Html.HiddenFor(model => model.IsFix)
    @Html.HiddenFor(model => model.IsRandom)
    @Html.HiddenFor(model => model.IsRandomHighPoint)
    @Html.Hidden("CASH2", Model.CASH)

    @Html.ValidationSummary(true, "", new { @class = "text-danger" })

    <div class="Div-EZ-ADDI09">
        <div class="form-horizontal">

            @if (Model.IsRandom == false)
            {
                <div class="form-group">
                    @Html.LabelFor(model => model.CASH, htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-9" style="padding-top:7px;display:inline;white-space:nowrap;">

                        @foreach (short c in ViewBag.CashArray)
                        {
                            bool bind = false;
                            if (Model != null) { bind = (Model.CASH == c); }
                            if (bind)
                            {
                                @Html.RadioButtonFor(model => model.CASH, c, new { @checked = "checked" })
                            }
                            else
                            {
                                @Html.RadioButtonFor(model => model.CASH, c, new { @onclick = "CashChange(this.value)" })
                            }
                            @Html.Label(c.ToString())
                            @:&nbsp
                        }


                    </div>
                    <br />
                    <div class="col-md-8" style="display:inline;white-space:nowrap;">
                        <label class="control-label col-md-3"></label>

                        &nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp;&nbsp;&nbsp;

                        @if (user.USER_TYPE == "T" && ViewBag.uhRMT25YN != "Y")
                        {
                            <input id="CASH" name="CASH" type="number" style="width:60px" min="1" max="50" onchange="checkNum(this)">
                        }
                        else
                        {
                            <input id="CASH" name="CASH" type="number" style="width:60px" min="1" max="100" onchange="checkNumAdmin(this)">
                        }      <b>(自訂數字，老師50點以內;管理者100點以內)</b>
                    </div>
                </div>
            }
            <div class="form-group">
                @Html.LabelFor(model => model.SUBJECT, htmlAttributes: new { @class = "control-label col-md-3" })
                <div class="col-md-8">
                    @Html.DropDownListFor(model => model.SUBJECT, (IEnumerable<SelectListItem>)ViewBag.SubjectSelectItem, new { @class = "form-control", onchange = "$('#USER_NO').focus();" })
                    @Html.ValidationMessageFor(model => model.SUBJECT, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group" style="margin-bottom:0px;">
                @Html.Label("學號/班級座號/數位學生證", htmlAttributes: new { @class = "control-label col-md-3" })
                <div class="col-md-8">
                    @Html.EditorFor(model => model.USER_NO, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.USER_NO, "", new { @class = "text-danger" })
                    <div style="font-size:1rem">
                        @(AttributeHelper.GetDescriptionAttribute<ADDI09EditOoneViewModel>(nameof(Model.USER_NO)) )
                    </div>
                </div>
            </div>

            <div class="form-group">
                <div class="col-md-3">
                </div>
                <div class="col-md-8" style="text-align:center;vertical-align:middle;">
                    @Html.Label(" ", new { @id = "SHOWNAME", style = "margin-top:5px" })
                </div>
            </div>
        </div>
    </div>

    <div style="height:20px"></div>
    <div class="form-group text-center col-md-offset-3 ">
        @if (Model.IsFix)
        {
            <button type="button" id="myButton" data-loading-text="Loading..." class="btn btn-default" autocomplete="off">
                確定給點
            </button>

        }
        @if (Model.IsRandom)
        {
            <button type="button" id="BtnRandom" class="btn btn-default" onclick="GoRandom()">
                隨機給點
            </button>
        }

        @Html.ActionLink("回選擇模式", "EditOneIndex", new { SYS_TABLE_TYPE = ViewBag.SYS_TABLE_TYPE }, new { @class = "btn btn-default", @role = "button" })

        <button type="button" class="btn btn-default btn-danger" id="clean">
            清除
        </button>
    </div>

    <link href="~/Content/css/jquery-ui.min.css" rel="stylesheet" />
<script src = @Url.Content("~/Scripts/jquery-3.6.4.js") ></script >

    <script src="~/Scripts/jquery-ui.min.js"></script>
    <div id="dialog-confirm" style="display:none;" title="確認給點？">
        <span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span>您確定要給點嗎？
    </div>
    <div id="challenge-panel" class="challenge-panel-bg">
        <div id="challenge-ribbon" class="challenge-ribbon animated"></div>
        <div id="challenge-info" class="challenge-info animated"></div>
    </div>

    <div style="width:100%;height:100%;background-color:@SharedGlobal.Logo_loading_background_color;display:none;z-index:999;position:fixed;left:0;top:0" id="loading" class="challenge-loading">
        <div style="margin: 0px auto;text-align:center">
            <img src="@Url.Content(SharedGlobal.Logo_loading)" style="max-height:350px;" />
            <br />

           
                <h3 style="color:#80b4fb">寫入中…</h3>
         
        </div>
    </div>

    <script type="text/javascript">
         var game_falseSound = new buzz.sound("@Url.Content("~/Content/mp3/game_false.mp3")");
        $('#myButton').on('click', function () {
            PointSubmit();
        })

        $("#clean").on('click', function () {

            $('#@Html.IdFor(m => m.USER_NO)').val('');
            $('#@Html.IdFor(m => m.SUBJECT)').val('');
            $("input[type='number']").val('');
            $("input[type='radio']").removeAttr("checked")
        })
        function PointSubmit() {
            $('#loading').fadeIn(100);
             var UNAME = $('#@Html.IdFor(m => m.USER_NO)').val();
            if (UNAME == "" || UNAME == null)
            {
              
                window.alert('請輸入學號或班級座號');
              
                $('#loading').fadeOut(100);
                return;
            }
            var SUBJECT = $('#@Html.IdFor(m => m.SUBJECT)').val();
            if (SUBJECT == "" || SUBJECT == null) {
                

                window.alert('請選擇獎懲主旨');
                $('#loading').fadeOut(100);
                return;
            }
         
             form1.submit();

        }
        window.onload = function () {
            var StatusMessaglength = 0;
            StatusMessaglength = $("#StatusMessageDiv").length;
    
            if (StatusMessaglength > 0) {
              
                game_falseSound.play();

            }
        };
       
        $(document).ready(function () {
         
            var preValue ="@(Session["ADDI09_EditOne_SUBJECT"] != null&& Session["ADDI09_EditOne_CASH"] != null)";
            if (preValue=="True")
            {
                var CSAHin = Number("@Session["ADDI09_EditOne_CASH"]");

                if (CSAHin != 3 && CSAHin != 5 && CSAHin != 10 && CSAHin != 15 && CSAHin != 20 && CSAHin != 25 && CSAHin != 30) {

                    $("input[type='number']").val(@(Session["ADDI09_EditOne_CASH"]));

                }


            }
            $(document).on('keypress', function (e) {
                if (e.which == 13) {
                  PointSubmit();
                    //if ($('#USER_NO').focus().is(":focus") == false) {

                    //    $('#USER_NO').focus();

                    //}

                }

            })
            $('#USER_NO').focus();

            $('#@Html.IdFor(m => m.USER_NO),#@Html.IdFor(m => m.CLASS_SEAT)').change(function () {
                DbQuery();

            });
        })

        function checkNum(obj) {
            $("input:checked").removeAttr("checked");
            var num = 0;
            num = $(obj).val();
            if (num > 50) {
                if (@user.ROLE_LEVEL!= '@HRMT24_ENUM.QAdminLevel.ToString()' && @user.ROLE_LEVEL!= '@HRMT24_ENUM.QAdminLevel.ToString()') {

                    alert("老師給點最高50");
                }

                if ((@user.ROLE_LEVEL== '@HRMT24_ENUM.QAdminLevel.ToString()' || @user.ROLE_LEVEL== '@HRMT24_ENUM.QAdminLevel.ToString()')) {
                    $('#USER_NO').focus();

                }
              
            }
        }
        function checkNumAdmin(obj) {
            $("input:checked").removeAttr("checked");
            var num = 0;
            num = $(obj).val();
            if (num > 100) {
                alert("管理者給點最高100");
            }
        }
        function DbQuery() {

            $.ajax({
                url: "@(Url.Action("ToQueryUser", "ADDI09"))",     // url位置
                type: 'post',                   // post/get
                data: { "USER_NO": $('#@Html.IdFor(m => m.USER_NO)').val(), "CLASS_SEAT": $('#@Html.IdFor(m => m.CLASS_SEAT)').val() },
                dataType: 'json',               // xml/json/script/html
                cache: false,                   // 是否允許快取
                success: function (data) {
                    var res = jQuery.parseJSON(data);

                    if (res.USER_NO == '') {
                        alert(res.USER_NAME);
                        $("#USER_NO").val("");
                        game_falseSound.play();
                        $('#myButton').text("確定給點");
                        $('#BtnRandom').text("隨機給點");
                        $('#SHOWNAME').text(" ");
                    }
                    else {
                        $('#@Html.IdFor(m => m.USER_NAME)').val(res.USER_NAME);
                        $('#myButton').text("確定給點：" + res.USER_NAME);
                        $('#BtnRandom').text("隨機給點：" + res.USER_NAME);
                        $('#SHOWNAME').text(res.USER_NAME);
                    }
                },
                error: function (xhr, err) {
                    //alert("readyState: " + xhr.readyState + "\nstatus: " + xhr.status);
                    //alert("responseText: " + xhr.responseText);
                }

            });

        }

        function GoRandom() {
            $('#loading').fadeIn();
            form1.action = '@Url.Action("EditOneRandom", (string)ViewBag.BRE_NO,new { IsRandom = Model.IsRandom, IsFix = Model.IsFix})';
            form1.submit();

        }

        function CashChange(ans) {
            $('#@Html.Id("CASH2")').val(ans);
        }
    </script>
}