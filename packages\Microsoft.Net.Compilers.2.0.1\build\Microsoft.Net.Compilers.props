﻿<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (c)  Microsoft.  All Rights Reserved.  Licensed under the Apache License, Version 2.0.  See License.txt in the project root for license information. -->
<Project ToolsVersion="14.0" DefaultTargets="Build" InitialTargets="ValidateMSBuildToolsVersion" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <!-- The UsingTask, UseSharedCompilation, and ToolPath/Exe variables all interact to
       choose which compiler path to use and whether or not to use the compiler server.
       If UsingTask and UseSharedCompilation are set then the compiler server next to the
       task will be used (i.e., the one in this package).
       If UseSharedCompilation is false or ToolPath/Exe are set the compiler server will
       not be used and the compiler exe at the ToolPath, if set, will be executed, otherwise
       the executable in the MSBuild install path will be executed. -->

  <Target Name="ValidateMSBuildToolsVersion" Condition="'$(BuildingProject)' == 'true'">
    <Error Text="Microsoft.Net.Compilers is only supported on MSBuild v14.0 and above"
           Condition="'$(MSBuildToolsVersion)' == '2.0' OR
                      '$(MSBuildToolsVersion)' == '3.5' OR
                      '$(MSBuildToolsVersion)' == '4.0' OR
                      '$(MSBuildToolsVersion)' == '12.0'" />
  </Target>

  <!-- Always use the local build task, even if we just shell out to an exe in case there are
       new properties in the local build task. -->
  <UsingTask TaskName="Microsoft.CodeAnalysis.BuildTasks.Csc"
             AssemblyFile="$(MSBuildThisFileDirectory)..\tools\Microsoft.Build.Tasks.CodeAnalysis.dll" />
  <UsingTask TaskName="Microsoft.CodeAnalysis.BuildTasks.Vbc"
             AssemblyFile="$(MSBuildThisFileDirectory)..\tools\Microsoft.Build.Tasks.CodeAnalysis.dll" />
  <PropertyGroup>
    <!-- By default don't use the compiler server in Visual Studio. -->
    <UseSharedCompilation Condition="'$(UseSharedCompilation)' == ''">false</UseSharedCompilation>
    <CSharpCoreTargetsPath>$(MSBuildThisFileDirectory)..\tools\Microsoft.CSharp.Core.targets</CSharpCoreTargetsPath>
    <VisualBasicCoreTargetsPath>$(MSBuildThisFileDirectory)..\tools\Microsoft.VisualBasic.Core.targets</VisualBasicCoreTargetsPath>
  </PropertyGroup>

  <!-- If we're not using the compiler server, set ToolPath/Exe to direct to
       the exes in this package -->
  <PropertyGroup Condition="'$(UseSharedCompilation)' != 'true'">
    <CscToolPath>$(MSBuildThisFileDirectory)..\tools</CscToolPath>
    <CscToolExe>csc.exe</CscToolExe>
    <VbcToolPath>$(MSBuildThisFileDirectory)..\tools</VbcToolPath>
    <VbcToolExe>vbc.exe</VbcToolExe>
  </PropertyGroup>
</Project>