﻿
@model ECOOL_APP.com.ecool.Models.DTO.ADDI09EditViewModel
<style>
    optgroup[label]{
        color: indianred;
    }
    option{
    	color: black;
    }
</style>
<div class="form-group">
    <label class="control-label col-md-3">獎懲類別</label>
    <div class="col-md-9">
        @Html.DropDownListFor(model => model.SUBJECT, (IEnumerable<SelectListItem>)ViewBag.QuickItems, new { @class = "form-control" })
    </div>
</div>


<div class="form-group">
    <label class="control-label col-md-3"></label>
    　預設文字
    <select id="quickselector" onchange="$('#contentTxt').val(event.target.options[event.target.selectedIndex].value)">
        <option>請選擇</option>
        <option value="品德表現">品德表現</option>
        <option value="會自動自發幫助別人。">會自動自發幫助別人。</option>
        <option value="書包、課桌椅及置物櫃都整理得很整齊。">書包、課桌椅及置物櫃都整理得很整齊。</option>
        <option value="遇見師長會主動問好，是個有禮貌的孩子。">遇見師長會主動問好，是個有禮貌的孩子。</option>
        <option value="友愛同學，並且會主動幫助同學解決困難。">友愛同學，並且會主動幫助同學解決困難。</option>
        <option value="學習表現">學習表現</option>
        <option value="學習單優良">學習單優良</option>
        <option value="認真學習">認真學習</option>
        <option value="課堂表現積極">課堂表現積極</option>
        <option value="上課表現優良">上課表現優良</option>
    </select>
</div>

<div class="form-group">
    <label class="control-label col-md-3">具體事蹟</label>
    <div class="col-md-9">
        @Html.TextAreaFor(model => model.CONTENT_TXT, 5, 100, new { id="contentTxt", @class = "form-control" })
        @Html.ValidationMessageFor(model => model.CONTENT_TXT, "", new { @class = "text-danger" })
    </div>
</div>

@if (!Model.Individual_Give)
{
    <div class="form-group">
        @Html.LabelFor(model => model.CASH, htmlAttributes: new { @class = "control-label col-md-3" })
        <div class="col-md-9">
            @Html.EditorFor(model => model.CASH, new { htmlAttributes = new { @class = "form-control", @type = "text" } })
            @Html.ValidationMessageFor(model => model.CASH, "", new { @class = "text-danger" })
        </div>
    </div>
}
<div class="form-group">
    @Html.LabelFor(model => model.MEMO, htmlAttributes: new { @class = "control-label col-md-3" })
    <div class="col-md-9">
        @Html.EditorFor(model => model.MEMO, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.MEMO, "", new { @class = "text-danger" })
    </div>
</div>
<div class="form-group">
    @Html.LabelFor(model => model.IMG_FILE, htmlAttributes: new { @class = "control-label col-md-3" })
    <div class="col-md-9">
        <input class="btn btn-default" type="file" name="files" />
        @Html.ValidationMessage("files", "", new { @class = "text-danger" })
    </div>
</div>