/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX-Web/Main/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.STIXMathJax_Main={directory:"Main/Regular",family:"STIXMathJax_Main",testString:"\u00A0\u00A3\u00A5\u00A7\u00A8\u00AC\u00AE\u00AF\u00B0\u00B1\u00B4\u00B5\u00B7\u00D7\u00F0",32:[0,0,250,0,0],33:[676,9,333,130,236],34:[676,-431,408,77,331],35:[662,0,500,6,495],36:[727,87,500,44,458],37:[706,19,747,61,686],38:[676,13,778,42,750],39:[676,-431,180,48,133],40:[676,177,333,48,304],41:[676,177,333,29,285],42:[676,-265,500,68,433],43:[547,41,685,48,636],44:[102,141,250,55,195],45:[257,-194,333,39,285],46:[100,11,250,70,181],47:[676,14,278,-9,287],48:[676,14,500,24,476],49:[676,0,500,111,394],50:[676,0,500,29,474],51:[676,14,500,41,431],52:[676,0,500,12,473],53:[688,14,500,31,438],54:[684,14,500,34,468],55:[662,8,500,20,449],56:[676,14,500,56,445],57:[676,22,500,30,459],58:[459,11,278,81,192],59:[459,141,278,80,219],60:[534,24,685,56,621],61:[386,-120,685,48,637],62:[534,24,685,56,621],63:[676,8,444,68,414],64:[676,14,921,116,809],65:[674,0,722,15,707],66:[662,0,667,17,593],67:[676,14,667,28,633],68:[662,0,722,16,685],69:[662,0,611,12,597],70:[662,0,556,11,546],71:[676,14,722,32,709],72:[662,0,722,18,703],73:[662,0,333,18,315],74:[662,14,373,-6,354],75:[662,0,722,33,723],76:[662,0,611,12,598],77:[662,0,889,12,864],78:[662,11,722,12,707],79:[676,14,722,34,688],80:[662,0,557,16,542],81:[676,177,722,34,701],82:[662,0,667,17,660],83:[676,14,556,43,491],84:[662,0,611,17,593],85:[662,14,722,14,705],86:[662,11,722,16,697],87:[662,11,944,5,932],88:[662,0,722,10,704],89:[662,0,722,22,703],90:[662,0,612,10,598],91:[662,156,333,88,299],92:[676,14,278,-9,287],93:[662,156,333,34,245],94:[662,-297,469,24,446],95:[-75,125,500,0,500],96:[678,-507,333,18,242],97:[460,10,444,37,442],98:[683,10,500,3,468],99:[460,10,444,25,412],100:[683,10,500,27,491],101:[460,10,444,25,424],102:[683,0,333,20,383],103:[460,218,500,28,470],104:[683,0,500,9,487],105:[683,0,278,16,253],106:[683,218,278,-70,194],107:[683,0,500,7,505],108:[683,0,278,19,257],109:[460,0,778,16,775],110:[460,0,500,16,485],111:[460,10,500,29,470],112:[460,217,500,5,470],113:[460,217,500,24,488],114:[460,0,333,5,335],115:[459,10,389,51,348],116:[579,10,278,13,279],117:[450,10,500,9,480],118:[450,14,500,19,477],119:[450,14,722,21,694],120:[450,0,500,17,479],121:[450,218,500,14,475],122:[450,0,444,27,418],123:[680,181,480,100,350],124:[676,14,200,67,133],125:[680,181,480,130,380],126:[325,-183,541,40,502],160:[0,0,250,0,0],163:[676,8,500,12,490],165:[662,0,500,-53,512],167:[676,148,500,70,426],168:[622,-523,333,18,316],172:[393,-115,600,48,552],174:[676,14,760,38,722],175:[601,-547,333,11,322],176:[676,-390,400,57,343],177:[502,87,685,48,637],180:[678,-507,333,93,317],181:[450,218,500,36,512],183:[310,-199,250,70,181],215:[529,25,640,43,597],240:[686,10,500,29,471],247:[516,10,564,30,534],295:[683,0,500,8,487],305:[460,0,278,16,253],567:[460,218,278,-70,193],710:[674,-507,333,11,322],711:[674,-507,333,11,322],713:[601,-547,334,11,322],714:[679,-509,333,93,320],715:[679,-509,333,22,249],728:[664,-507,335,27,308],729:[622,-523,333,118,217],730:[711,-512,333,67,266],732:[638,-532,333,1,331],768:[678,-507,0,-371,-147],769:[678,-507,0,-371,-147],770:[674,-507,0,-386,-75],771:[638,-532,0,-395,-65],772:[601,-547,0,-385,-74],774:[664,-507,0,-373,-92],775:[622,-523,0,-280,-181],776:[622,-523,0,-379,-81],778:[711,-512,0,-329,-130],779:[678,-507,0,-401,-22],780:[674,-507,0,-385,-74],824:[662,156,0,-380,31],913:[674,0,722,15,707],914:[662,0,667,17,593],915:[662,0,587,11,577],916:[674,0,722,48,675],917:[662,0,611,12,597],918:[662,0,612,10,598],919:[662,0,722,18,703],920:[676,14,722,34,688],921:[662,0,333,18,315],922:[662,0,731,33,723],923:[674,0,702,15,687],924:[662,0,889,12,864],925:[662,11,722,12,707],926:[662,0,643,29,614],927:[676,14,722,34,688],928:[662,0,722,18,703],929:[662,0,557,16,542],931:[662,0,624,30,600],932:[662,0,611,17,593],933:[674,0,722,29,703],934:[662,0,763,35,728],935:[662,0,722,10,704],936:[690,0,746,22,724],937:[676,0,744,29,715],945:[460,10,543,29,529],946:[683,217,496,55,466],947:[457,218,474,10,444],948:[683,10,500,29,470],949:[460,10,439,25,407],950:[683,218,441,35,407],951:[460,217,512,10,452],952:[683,10,496,27,468],953:[460,10,275,20,267],954:[460,0,500,7,503],955:[683,11,497,12,492],956:[450,217,528,55,516],957:[460,14,455,20,443],958:[683,218,441,35,407],959:[460,10,505,35,473],960:[450,14,501,9,482],961:[460,217,496,55,466],962:[460,218,441,35,432],963:[450,10,548,29,518],964:[450,10,477,3,442],965:[460,10,524,16,494],966:[460,217,623,29,593],967:[460,220,500,11,486],968:[460,217,694,20,684],969:[460,10,625,29,595],976:[693,10,450,54,411],977:[683,10,554,0,544],978:[676,0,722,29,698],981:[683,217,623,29,593],982:[450,10,762,6,726],984:[676,217,722,34,688],985:[460,217,500,29,470],986:[676,218,667,28,622],987:[490,218,461,35,436],988:[662,0,556,11,546],989:[450,190,470,80,435],990:[797,14,703,13,678],991:[662,0,511,64,455],992:[676,218,801,11,767],993:[573,216,528,-6,487],1008:[460,10,551,42,515],1009:[460,215,500,29,470],1012:[676,14,722,34,688],1013:[460,10,439,25,407],1014:[460,10,444,32,414],8211:[250,-201,500,0,500],8212:[250,-201,1000,0,1000],8214:[690,189,523,129,394],8216:[676,-433,333,115,254],8217:[676,-433,333,79,218],8220:[676,-433,444,43,414],8221:[676,-433,444,30,401],8224:[676,149,500,59,442],8225:[676,153,500,58,442],8230:[100,11,1000,111,888],8242:[678,-402,289,75,214],8243:[678,-401,426,75,351],8244:[678,-401,563,75,488],8245:[678,-402,289,75,214],8254:[820,-770,500,0,500],8260:[676,14,167,-168,331],8279:[678,-401,710,75,635],8407:[760,-548,0,-453,-17],8463:[683,10,579,47,547],8465:[695,34,762,45,711],8467:[687,11,579,48,571],8472:[547,217,826,52,799],8476:[704,22,874,50,829],8487:[662,14,744,29,715],8498:[662,0,535,13,462],8501:[677,13,682,43,634],8502:[677,19,639,57,572],8503:[677,19,505,40,463],8504:[677,19,599,52,495],8513:[676,14,695,68,668],8592:[449,-58,926,71,857],8593:[662,156,511,60,451],8594:[448,-57,926,70,856],8595:[662,156,511,60,451],8596:[449,-57,926,38,888],8597:[730,224,511,60,451],8598:[662,156,926,70,856],8599:[662,156,926,70,856],8600:[662,156,926,70,856],8601:[662,156,926,70,856],8602:[450,-58,926,60,866],8603:[450,-58,926,60,866],8606:[449,-58,926,70,856],8608:[449,-58,926,70,856],8610:[449,-58,926,70,856],8611:[449,-58,926,70,856],8614:[450,-57,926,70,857],8617:[553,-57,926,70,856],8618:[553,-57,926,70,856],8619:[553,0,926,70,856],8620:[553,0,926,70,856],8621:[449,-58,1200,49,1151],8622:[450,-58,926,38,888],8624:[662,156,463,30,424],8625:[662,156,463,39,433],8630:[534,0,926,44,882],8631:[534,0,926,44,882],8634:[686,116,974,116,858],8635:[686,116,974,116,858],8636:[494,-220,955,54,901],8637:[286,-12,955,54,901],8638:[662,156,511,222,441],8639:[662,156,511,69,288],8640:[494,-220,955,54,901],8641:[286,-12,955,54,901],8642:[662,156,511,222,441],8643:[662,156,511,69,288],8644:[598,92,926,71,856],8646:[598,92,926,71,856],8647:[599,92,926,70,856],8648:[662,156,773,41,732],8649:[599,92,926,70,856],8650:[662,156,773,41,732],8651:[539,33,926,70,856],8652:[539,33,926,70,856],8653:[551,45,926,60,866],8654:[517,10,926,20,906],8655:[551,45,926,60,866],8656:[551,45,926,60,866],8657:[662,156,685,45,641],8658:[551,45,926,60,866],8659:[662,156,685,45,641],8660:[517,10,926,20,906],8661:[730,224,685,45,641],8666:[644,139,926,46,852],8667:[645,138,926,74,880],8669:[449,-58,926,60,866],8672:[449,-58,926,60,866],8674:[449,-58,926,60,866],8704:[662,0,560,2,558],8705:[760,15,463,59,404],8706:[668,11,471,40,471],8707:[662,0,560,73,487],8708:[775,122,560,71,487],8709:[583,79,762,50,712],8711:[662,12,731,63,667],8712:[531,27,685,60,625],8713:[662,157,685,60,625],8715:[531,27,685,60,625],8717:[459,-45,486,64,422],8722:[286,-220,685,64,621],8723:[502,87,685,48,637],8724:[741,41,685,48,636],8725:[710,222,523,46,478],8726:[411,-93,428,25,403],8727:[471,-33,523,67,457],8728:[387,-117,350,40,310],8729:[387,-117,350,40,310],8730:[973,259,928,112,963],8733:[430,0,685,41,643],8734:[430,0,926,70,854],8736:[547,0,685,23,643],8737:[547,72,685,22,642],8738:[519,11,685,56,653],8739:[690,189,266,100,166],8740:[690,189,404,23,381],8741:[690,189,523,129,394],8742:[690,189,609,23,586],8743:[536,29,620,31,589],8744:[536,29,620,31,589],8745:[536,31,620,48,572],8746:[536,31,620,48,572],8747:[824,320,459,32,639],8756:[521,16,620,38,582],8757:[521,16,620,38,582],8764:[362,-148,685,48,637],8765:[362,-148,685,48,637],8768:[547,42,286,35,249],8769:[424,-88,685,48,637],8770:[445,-55,685,48,637],8771:[445,-55,685,48,637],8773:[532,27,685,48,637],8774:[604,107,685,47,637],8776:[475,-25,685,48,637],8778:[552,45,685,48,637],8781:[498,-8,685,48,637],8782:[471,-35,685,48,637],8783:[471,-120,685,48,637],8784:[611,-120,685,48,637],8785:[611,106,685,48,637],8786:[611,105,685,48,637],8787:[611,106,685,48,637],8790:[416,-90,685,48,637],8791:[752,-120,685,48,637],8796:[853,-120,685,48,637],8800:[662,156,685,48,637],8801:[478,-28,685,48,637],8804:[609,103,685,64,629],8805:[609,103,685,64,629],8806:[718,211,685,57,622],8807:[718,211,685,57,622],8808:[746,260,685,56,621],8809:[746,260,685,56,621],8810:[532,26,933,25,908],8811:[532,26,933,25,908],8812:[730,224,466,85,381],8814:[662,156,685,56,621],8815:[662,156,685,56,621],8816:[730,229,685,56,621],8817:[730,229,685,56,622],8818:[664,164,685,48,637],8819:[664,164,685,48,637],8822:[705,204,685,56,621],8823:[705,204,685,56,621],8826:[532,26,685,64,621],8827:[532,26,685,64,621],8828:[628,120,685,64,621],8829:[629,119,685,64,621],8830:[664,164,685,48,637],8831:[664,164,685,48,637],8832:[662,156,685,64,621],8833:[662,156,685,64,621],8834:[531,25,685,64,621],8835:[531,25,685,64,621],8838:[607,103,685,64,621],8839:[607,103,685,64,621],8840:[730,229,685,64,621],8841:[730,229,685,64,621],8842:[627,216,685,64,621],8843:[627,216,685,64,621],8846:[536,31,620,48,572],8847:[531,25,685,64,621],8848:[531,25,685,64,621],8849:[607,103,685,64,621],8850:[607,103,685,64,621],8851:[536,31,620,48,572],8852:[536,31,620,48,572],8853:[623,119,842,50,792],8854:[623,119,842,50,792],8855:[623,119,842,50,792],8856:[623,119,842,50,792],8857:[583,79,762,50,712],8858:[623,119,842,50,792],8859:[623,119,842,50,792],8861:[623,119,842,50,792],8862:[662,158,910,45,865],8863:[662,158,910,45,865],8864:[662,158,910,45,865],8865:[662,157,910,45,865],8866:[662,0,685,64,621],8867:[662,0,685,64,621],8868:[662,0,685,48,637],8869:[662,0,685,48,637],8872:[662,0,685,64,621],8873:[662,0,860,57,814],8874:[662,0,860,45,815],8876:[662,0,786,9,723],8877:[662,0,786,9,723],8878:[662,0,968,9,922],8879:[662,0,968,9,922],8882:[531,25,685,24,631],8883:[531,25,685,54,661],8884:[607,103,685,24,631],8885:[607,103,685,54,661],8888:[403,-103,849,50,799],8890:[450,212,480,74,406],8891:[536,139,620,32,590],8892:[646,29,620,32,590],8900:[488,-16,523,26,497],8901:[313,-193,286,83,203],8902:[597,13,700,35,665],8904:[582,80,810,54,756],8905:[582,80,810,93,716],8906:[582,80,810,93,716],8907:[582,80,810,74,736],8908:[582,80,810,74,736],8909:[445,-55,685,48,637],8910:[532,25,580,31,549],8911:[532,25,580,31,549],8912:[531,25,685,64,621],8913:[531,25,685,64,621],8914:[536,31,620,48,572],8915:[536,31,620,48,572],8916:[631,31,620,48,572],8918:[534,24,685,56,621],8919:[534,24,685,56,621],8920:[534,24,1274,45,1229],8921:[534,24,1274,45,1229],8922:[830,324,685,56,621],8923:[830,324,685,56,621],8926:[627,121,685,64,621],8927:[627,121,685,64,621],8928:[730,229,685,64,621],8929:[730,229,685,64,621],8934:[669,279,685,48,637],8935:[669,279,685,48,637],8936:[670,279,685,48,637],8937:[670,279,685,48,637],8938:[662,156,635,24,581],8939:[662,156,635,54,611],8940:[730,229,635,24,581],8941:[730,229,635,54,611],8942:[606,104,511,192,319],8943:[316,-189,926,108,818],8945:[520,18,926,194,732],8968:[713,213,469,188,447],8969:[713,213,469,27,286],8970:[713,213,469,188,447],8971:[713,213,469,27,286],8994:[360,-147,1019,54,965],8995:[360,-147,1019,54,965],9140:[766,-574,926,55,871],9141:[109,83,926,55,871],9168:[405,-101,511,222,288],9180:[100,100,1000,0,1000],9181:[764,-564,1000,0,1000],9182:[214,114,1000,0,1000],9183:[892,-564,1000,0,1000],9184:[100,114,1000,0,1000],9185:[778,-564,1000,0,1000],9416:[676,14,684,0,684],9484:[340,303,708,317,720],9488:[340,303,708,-11,390],9492:[910,-267,708,317,720],9496:[910,-267,708,-11,390],9585:[910,303,708,-15,723],9586:[910,303,708,-15,723],9632:[662,158,910,45,865],9633:[662,158,910,45,865],9650:[811,127,1145,35,1110],9651:[811,127,1145,35,1110],9654:[790,285,1043,70,1008],9656:[556,49,660,80,605],9657:[555,50,660,80,605],9660:[811,127,1145,35,1110],9661:[811,127,1145,35,1110],9664:[790,285,1043,35,973],9666:[555,50,660,55,580],9667:[554,51,660,55,580],9674:[795,289,790,45,745],9711:[785,282,1207,70,1137],9824:[609,99,685,34,651],9825:[603,105,685,34,651],9826:[609,105,685,41,643],9827:[603,99,685,34,651],9837:[768,10,426,57,346],9838:[768,181,426,75,350],9839:[768,181,426,41,386],10003:[707,12,755,34,704],10016:[592,87,767,53,714],10216:[713,213,400,77,335],10217:[713,213,400,65,323],10222:[676,177,233,56,211],10223:[676,177,233,22,177],10229:[449,-58,1574,55,1519],10230:[449,-57,1574,55,1519],10231:[449,-57,1574,55,1519],10232:[551,45,1574,55,1519],10233:[551,45,1574,55,1519],10234:[517,10,1574,55,1519],10236:[450,-57,1574,55,1519],10647:[719,213,488,188,466],10648:[719,213,488,22,300],10731:[795,289,790,45,745],10741:[710,222,523,46,478],10744:[695,325,602,85,517],10745:[695,325,602,85,517],10815:[662,0,694,30,664],10846:[796,29,620,31,589],10877:[625,137,685,56,621],10878:[625,137,685,56,621],10885:[746,275,685,48,637],10886:[746,275,685,48,637],10887:[628,216,685,60,625],10888:[628,216,687,56,621],10889:[746,309,685,48,637],10890:[746,309,685,48,637],10891:[930,424,685,56,621],10892:[930,424,685,56,621],10901:[640,122,685,56,621],10902:[640,122,685,56,621],10927:[609,103,685,64,621],10928:[609,103,685,64,621],10933:[747,260,685,65,622],10934:[747,260,685,65,622],10935:[747,275,685,48,637],10936:[747,275,685,48,637],10937:[747,309,685,48,637],10938:[747,309,685,48,637],10949:[717,211,685,64,622],10950:[717,211,685,65,623],10955:[717,319,685,61,619],10956:[717,319,685,66,624],65533:[662,217,872,55,817]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"STIXMathJax_Main"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Main/Regular/Main.js"]);
