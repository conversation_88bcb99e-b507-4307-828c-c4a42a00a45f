﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'forms', 'nl', {
	button: {
		title: 'Eigenschappen knop',
		text: 'Tekst (waarde)',
		type: 'Soort',
		typeBtn: 'Knop',
		typeSbm: 'Versturen',
		typeRst: 'Leegmaken'
	},
	checkboxAndRadio: {
		checkboxTitle: 'Eigenschappen aanvinkvakje',
		radioTitle: 'Eigenschappen selectievakje',
		value: 'Waarde',
		selected: 'Geselecteerd',
		required: 'Vereist'
	},
	form: {
		title: 'Eigenschappen formulier',
		menu: 'Eigenschappen formulier',
		action: 'Actie',
		method: 'Methode',
		encoding: 'Codering'
	},
	hidden: {
		title: 'Eigenschappen verborgen veld',
		name: '<PERSON><PERSON>',
		value: '<PERSON>aar<PERSON>'
	},
	select: {
		title: 'Eigenschappen selectieveld',
		selectInfo: 'Informatie',
		opAvail: 'Beschikbare opties',
		value: 'Waarde',
		size: '<PERSON>root<PERSON>',
		lines: 'Regels',
		chkMulti: 'Gecombineerde selecties toestaan',
		required: 'Vereist',
		opText: 'Tekst',
		opValue: 'Waarde',
		btnAdd: 'Toevoegen',
		btnModify: 'Wijzigen',
		btnUp: 'Omhoog',
		btnDown: 'Omlaag',
		btnSetValue: 'Als geselecteerde waarde instellen',
		btnDelete: 'Verwijderen'
	},
	textarea: {
		title: 'Eigenschappen tekstvak',
		cols: 'Kolommen',
		rows: 'Rijen'
	},
	textfield: {
		title: 'Eigenschappen tekstveld',
		name: 'Naam',
		value: 'Waarde',
		charWidth: 'Breedte (tekens)',
		maxChars: 'Maximum aantal tekens',
		required: 'Vereist',
		type: 'Soort',
		typeText: 'Tekst',
		typePass: 'Wachtwoord',
		typeEmail: 'E-mail',
		typeSearch: 'Zoeken',
		typeTel: 'Telefoonnummer',
		typeUrl: 'URL'
	}
} );
