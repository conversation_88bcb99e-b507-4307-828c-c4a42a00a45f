﻿@model CERI04EditViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;
}
<style>
    .single-line {
        display: block;
        width: 100%;
        height: 34px;
        padding: 6px 12px;
        font-size: 14px;
        line-height: 1.428571429;
        color: #555555;
        max-width: 580px;
        max-height: 1000px;
        vertical-align: middle;
        background-color: #ffffff;
        border: 1px solid #cccccc;
        border-radius: 4px;
        -webkit-box-shadow: inset 0 1px 1px rgb(0 0 0 / 8%);
        box-shadow: inset 0 1px 1px rgb(0 0 0 / 8%);
        -webkit-transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
        transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
    }
</style>

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")
@{
    Html.RenderAction("_Ceri02Menu", "CERI02", new { NowAction = "CERI04" });
}
@using (Html.BeginForm("Edit", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1", @class = "form-horizontal", enctype = "multipart/form-data" }))
{

    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })

    @Html.HiddenFor(m => m.Keyword)
    @Html.HiddenFor(m => m.OrderByColumnName)
    @Html.HiddenFor(m => m.SortType)
    @Html.HiddenFor(m => m.Page)

    @Html.HiddenFor(m => m.WhereACCREDITATION_NAME)
    @Html.HiddenFor(m => m.WhereACCREDITATION_TYPE)
    @Html.HiddenFor(m => m.WhereGRADE)
    @Html.HiddenFor(m => m.WhereIsUnVerifier)
    @Html.HiddenFor(m => m.WhereCLASS_NO)
    @Html.HiddenFor(m => m.WhereSUBJECT)

    @Html.HiddenFor(m => m.ThisACCREDITATION_ID)
    @Html.HiddenFor(m => m.ThisITEM_NO)
    @Html.HiddenFor(m => m.ThisSCHOOL_NO)
    @Html.HiddenFor(m => m.ThisGRADE)
    @Html.HiddenFor(m => m.ThisCLASS_NO)

    <div class="panel panel-ZZZ" name="TOP">
        <div class="panel-heading text-center">
            @Html.BarTitle(null, (string)ViewBag.Title)
        </div>
        <div class="panel-body">
            <div class="form-horizontal">

                <div class="row">

                    <div class="col-md-6">
                        <samp class="dt">
                            @Html.LabelFor(m => m.QData.TYPE_NAME)
                        </samp>
                        <samp class="dd">
                            @Html.DisplayFor(m => m.QData.TYPE_NAME)
                        </samp>
                    </div>
                    <div class="col-md-6">
                        <samp class="dt">
                            @Html.LabelFor(m => m.QData.ACCREDITATION_NAME)
                        </samp>
                        <samp class="dd">
                            @Html.DisplayFor(m => m.QData.ACCREDITATION_NAME)
                        </samp>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12 ">
                        <samp class="dt">
                            @Html.LabelFor(m => m.QData.CLASS_NO)
                        </samp>
                        <samp class="dd">
                            @Html.DisplayFor(m => m.QData.CLASS_NO)
                        </samp>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12 ">
                        <samp class="dt">
                            @Html.LabelFor(m => m.QData.SUBJECT)
                        </samp>
                        <samp class="dd">
                            @Html.DisplayFor(m => m.QData.SUBJECT)
                        </samp>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <samp class="dt">
                            @Html.LabelFor(m => m.QData.CONTENT)
                        </samp>
                        <samp class="dd">
                            @Html.DisplayFor(m => m.QData.CONTENT)
                        </samp>
                    </div>
                </div>

                <hr class="hr-line-dashed" />
               
                <div class="panel panel-default">
                    <div class="panel-body">
                        <table class="table table-condensed table-ecool">
                            <thead>
                                <tr>
                                    <th>
                                        班級
                                    </th>
                                    <th>
                                        座號
                                    </th>
                                    <th>
                                        姓名
                                    </th>

                                    @if (Model.Details.FirstOrDefault().IsText == "Y")
                                    {
                                        <th>具體事蹟</th>
                                        <th colspan="2" class="col-xs-4 text-center">
                                            建議一個人一項，如果大於一項可以按＋

                                        </th>}

                                    else
                                    {
                                        <th></th>
                                        <th colspan="2" class="col-xs-4 text-center">
                                            全選
                                            <input type="checkbox" id="CheckAll" onclick="CheckAll(this)" title="全選" />


                                        </th>
                                    }
                                </tr>

                            </thead>
                            <tbody>
                                @if (Model.Details?.Count() > 0)
                                {
                                    List<string> USER_NOstr = new List<string>();

                                    USER_NOstr = Model.Details.Select(x => x.USER_NO).Distinct().ToList();

                                    foreach (var items in USER_NOstr)
                                    {
                                        var item = Model.Details.Where(x => x.USER_NO == items).ToList();

                                        @Html.Partial("_Detail", item)
                                    }
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="text-center">
        <hr />
        <button type="button" id="clearform" class="btn btn-default" onclick="onBack()">
            取消
        </button>

        <button type="button" class="btn btn-default" onclick="onSave()">
            <span class="fa fa-check-circle" aria-hidden="true"></span>存檔
        </button>
    </div>
}

@section Scripts {

    <script language="JavaScript">

        var targetFormID = '#form1';

        $(document).ready(function(){
            $("#CheckAll").click(function () {

                if($("#CheckAll").prop("checked")){
                  $(".IsPass").prop("checked",true);
                }else{
                  $(".IsPass").prop("checked",false);
                }
            })
        })

         //增加通過內容
        function AddItemD(str, user_NOItem, SCHOOL_NOItem) {
            var yes = confirm('避免跑版建議一人不要超過兩筆以上');
            if (yes) {
            var checkBoxsIsTextstr = "";
            checkBoxsIsTextstr = $("#checkBoxsIsText").prop("checked");
            if (checkBoxsIsTextstr == true) {

            }
             var data = {
                 //IsCopy: true,
                 //IsText: checkBoxsIsTextstr,
                 USER_NO: user_NOItem,
                 SCHOOL_NO: SCHOOL_NOItem,
                 //O_IS_PASS: O_IS_PASSItem
            };

              $.ajax({
                url: '@Url.Action("_ADDDetail")',
                data: data,
                type: 'post',
                async: false,
                cache: false,
                dataType: 'html',
                  success: function (data) {
                      $('#' + str).append(data);
                }
              });
            }
        }
        function onSave(StatusVal)
        {
            $(targetFormID).attr("action", "@Url.Action("EditSave", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }
        function CheckAll(obj) {
            var checkvalue = $(obj).is(":checked")
            console.log(checkvalue);
            if (checkvalue) {
                $("input[type='checkbox'][value='Y']").each(function () { this.checked = true; });

            }
            else {
                $("input[type='checkbox'][value='Y']").each(function () { this.checked = false; });
            }
        }



        function onBack() {
            $(targetFormID).attr("action", "@Url.Action("Index", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }
    </script>
}