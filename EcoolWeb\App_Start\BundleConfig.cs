﻿using System;
using System.Web;
using System.Web.Optimization;

namespace EcoolWeb
{
    public class BundleConfig
    {
        // 如需「搭配」的詳細資訊，請瀏覽 http://go.microsoft.com/fwlink/?LinkId=301862
        public static void RegisterBundles(BundleCollection bundles)
        {

            BundleTable.EnableOptimizations = false;

            //bundles.Add(new ScriptBundle("~/bundles/jquery").Include(
            //            "~/Scripts/jquery-{version}.js",
            //            "~/Scripts/thinkpower-jquery.validationEngine-zh-TW.js",
            //            "~/Scripts/thinkpower-jquery.validationEngine.js",
            //            "~/Scripts/ECoolBase.js",
            //            "~/Scripts/jquery-ui.min.js"));

            bundles.Add(new ScriptBundle("~/bundles/jquery").Include(
           "~/Scripts/jquery-{version}.js",
            "~/Scripts/ECoolBase.js",
            "~/Scripts/jquery-ui.min.js"));

            // 使用開發版本的 Modernizr 進行開發並學習。然後，當您
            // 準備好實際執行時，請使用 http://modernizr.com 上的建置工具，只選擇您需要的測試。
            bundles.Add(new ScriptBundle("~/bundles/modernizr").Include(
                        "~/Scripts/modernizr-*"));

            bundles.Add(new ScriptBundle("~/bundles/bootstrap").Include(
                      "~/Scripts/bootstrap.js ",
                      "~/Scripts/respond.js",
                      "~/Scripts/loaders.css.js",
                      "~/Scripts/bootstrap-select/bootstrap-select.js",
                      "~/Scripts/bootstrap-select/defaults-zh_TW.js")
                      );

            bundles.Add(new StyleBundle("~/Content/css").Include(
                      "~/Content/css/jquery.validationEngine.css",
                      "~/Content/css/bootstrap.css",
                      "~/Content/css/bootstrap-select.css",
                      "~/Content/css/Site.css",
                      "~/Content/css/loaders.css",
                      "~/Content/css/jquery-ui.min.css"));

            bundles.Add(new StyleBundle("~/Content/cssTwo").Include(
                     "~/Content/css/jquery.validationEngine.css",
                     "~/Content/css/bootstrap.css",
                     "~/Content/css/bootstrap-select.css",
                     "~/Content/css/jquery-ui.min.css"));
        }
    }
}