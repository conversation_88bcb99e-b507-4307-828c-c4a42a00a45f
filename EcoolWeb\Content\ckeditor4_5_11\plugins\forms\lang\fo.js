﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'forms', 'fo', {
	button: {
		title: '<PERSON>ginleikar fyri knøtt',
		text: 'Tekstur',
		type: 'Slag',
		typeBtn: 'Knøttur',
		typeSbm: 'Send',
		typeRst: 'Nullstilla'
	},
	checkboxAndRadio: {
		checkboxTitle: 'Eginleikar fyri flugubein',
		radioTitle: 'Eginleikar fyri radioknøtt',
		value: 'Virði',
		selected: 'Valt',
		required: 'Required' // MISSING
	},
	form: {
		title: 'Eginleikar fyri Form',
		menu: 'Eginleikar fyri Form',
		action: 'Hending',
		method: 'Háttur',
		encoding: 'Encoding'
	},
	hidden: {
		title: 'Eginleikar fyri fjaldan teig',
		name: 'Navn',
		value: 'Virði'
	},
	select: {
		title: 'Eginleikar fyri valskrá',
		selectInfo: 'Upplýsingar',
		opAvail: 'Tøkir møguleikar',
		value: 'Virði',
		size: 'Stødd',
		lines: 'Linjur',
		chkMulti: 'Loyv fleiri valmøguleikum samstundis',
		required: 'Required', // MISSING
		opText: 'Tekstur',
		opValue: 'Virði',
		btnAdd: 'Legg afturat',
		btnModify: 'Broyt',
		btnUp: 'Upp',
		btnDown: 'Niður',
		btnSetValue: 'Set sum valt virði',
		btnDelete: 'Strika'
	},
	textarea: {
		title: 'Eginleikar fyri tekstumráði',
		cols: 'kolonnur',
		rows: 'røðir'
	},
	textfield: {
		title: 'Eginleikar fyri tekstteig',
		name: 'Navn',
		value: 'Virði',
		charWidth: 'Breidd (sjónlig tekn)',
		maxChars: 'Mest loyvdu tekn',
		required: 'Required', // MISSING
		type: 'Slag',
		typeText: 'Tekstur',
		typePass: 'Loyniorð',
		typeEmail: 'Email', // MISSING
		typeSearch: 'Search', // MISSING
		typeTel: 'Telephone Number', // MISSING
		typeUrl: 'URL'
	}
} );
