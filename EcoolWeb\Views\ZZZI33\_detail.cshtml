﻿@model ZZZI33EditTopicViewModel
    @{

        var checkBoxsISPersonal = new List<CheckBoxListInfo>();
        CheckBoxListInfo cheboxISPersonalItem = new CheckBoxListInfo();
        cheboxISPersonalItem.DisplayText = "是";
        cheboxISPersonalItem.Value = "Y";


    }

@using (Html.BeginCollectionItem("Topic", Model.isCopy))
{

    var index = Html.GetIndex("Topic");

    <fieldset @(Model.ShowBtn == false ? "disabled" : "")>
        <div id="@index">
            @Html.HiddenFor(m => m.Html_ID, new { @Value = index, @autocomplete = "off" })
            @Html.HiddenFor(m => m.Q_TYPE)
            <br />
            @if (Model.Q_TYPE == SAQT02.Q_TYPEVal.IsText)
            {
                <div class="panel panel-info">
                    <div class="panel-heading">
                        <div class="form-group">
                            <label class="col-md-4 control-label">標題/描述</label>
                            <div class="col-md-8">
                                <div class="input-group">
                                    @Html.TextAreaFor(m => m.Q_SUBJECT, new { @class = "form-control", @rows = "1" })
                                    <span class="input-group-btn" style="vertical-align:top">
                                        <button class="btn btn-default" type="button" onclick="onInckeditorText('@Html.IdFor(m=>m.Q_SUBJECT)')" title="載入編輯器/移除編輯器"><i class="glyphicon glyphicon-edit"></i></button>
                                    </span>
                                </div>
                                @Html.ValidationMessageFor(m => m.Q_SUBJECT, "", new { @class = "text-danger" })
                            </div>
                        </div>
                    </div>
                </div>
            }
            else
            {
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <div class="form-group">
                            <label class="col-md-4 control-label">投票選項標題</label>
                            <div class="col-md-8">
                                <div class="input-group">
                                    @Html.TextAreaFor(m => m.Q_SUBJECT, new { @class = "form-control", @rows = "1" })
                                    <span class="input-group-btn" style="vertical-align:top">
                                        <button class="btn btn-default" type="button" onclick="onInckeditorText('@Html.IdFor(m=>m.Q_SUBJECT)')" title="載入編輯器/移除編輯器"><i class="glyphicon glyphicon-edit"></i></button>
                                    </span>
                                </div>
                                @Html.ValidationMessageFor(m => m.Q_SUBJECT, "", new { @class = "text-danger" })
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-4 control-label">投票選項類型   </label>
                            <div class="col-md-8">
                                <div class="input-group">
                                    @{
                                        ViewBag.InputType = SAQT03.Q_INPUT_TYPEVal.VoteSelectItem(Model.INPUT_TYPE);
                                        @Html.DropDownListFor(m => m.INPUT_TYPE, (IEnumerable<SelectListItem>)ViewBag.InputType, new { @class = "form-control", @onchange = "onSelectInput('" + @index + "',this.value,this.options[this.selectedIndex].text)" })
                                    }

                                    @Html.ValidationMessageFor(m => m.INPUT_TYPE, "", new { @class = "text-danger" })
                                  
                                    @*<span id="text@(index)" style="@((Model?.INPUT_TYPE ?? "") == SAQT03.Q_INPUT_TYPEVal.text ?"":"display:none")  ">

                                        <font color="FireBrick">簡答必填</font>

                                    </span>*@
                                    <span id="UPworld@(index)" style="@((Model?.INPUT_TYPE ?? "") == SAQT03.Q_INPUT_TYPEVal.UploadWorld ?"":"display:none")  ">

                                        <font color="FireBrick">讓使用者可上傳10M以下的Word 檔</font>

                                    </span>
                                    <span id="UPIMG@(index)" style="@((Model?.INPUT_TYPE ?? "") == SAQT03.Q_INPUT_TYPEVal.UploadImage ?"":"display:none")  ">


                                        <font color="FireBrick"> 讓使用者可上傳10M以下的圖檔</font>

                                    </span>
                                    <span id="UPPdf@(index)" style="@((Model?.INPUT_TYPE ?? "") ==SAQT03.Q_INPUT_TYPEVal.UploadPdf ?"":"display:none")  ">

                                        <font color="FireBrick">  讓使用者可上傳6M以下的Pdf檔</font>

                                    </span>

                                </div>
                            </div>

                        </div>
                     
                      
                      

                   
                        <div class="form-group" id="Q_MUTIPLE_CHOICES_OF_NUM@(index)" style="@((Model?.INPUT_TYPE ?? "") !=SAQT03.Q_INPUT_TYPEVal.checkbox ?"display:none":"")  ">
                            <label class="col-md-4 control-label">複選必需勾選的筆數</label>
                            <div class="col-md-8">
                                <div class="input-group">
                                    @Html.EditorFor(m => m.Q_MUTIPLE_CHOICES_OF_NUM, new { htmlAttributes = new { @class = "form-control input-md", @placeholder = "未填入不限制" } })
                                </div>
                            </div>
                            @Html.ValidationMessageFor(m => m.Q_MUTIPLE_CHOICES_OF_NUM, "", new { @class = "text-danger" })
                        </div>

                        <div class="form-group">
                            <label class="col-md-4 control-label">
                            </label>
                            <div class="col-md-8" id="Ipunt_@index">
                                @if (Model.Topic_D != null)
                                {
                                    if (Model.Topic_D.Count() > 0)
                                    {
                                        foreach (var D_item in Model.Topic_D)
                                        {
                                            D_item.index = index;

                                            @Html.Partial("_Input", D_item)
                                        }
                                    }
                                }
                            </div>
                        </div>
                        @if (Model.INPUT_TYPE == SAQT03.Q_INPUT_TYPEVal.radio || Model.INPUT_TYPE == SAQT03.Q_INPUT_TYPEVal.checkbox)
                        {
                            <div class="text-right" id="Btn_@index">
                                <button type="button" class="btn btn-default btn-sm" onclick="BtnOnAddInput('@index')">
                                    新增選項
                                </button>
                            </div>
                        }
                        else
                        {
                            <div class="text-right" style="display:none" id="Btn_@index">
                                <button type="button" class="btn btn-default btn-sm" onclick="BtnOnAddInput('@index')">
                                    新增選項
                                </button>
                            </div>
                        }
                    </div>
                        <div class="panel-footer">
                            <div class=" text-right">

                                @if (Model.ShowBtn)
                                {
                                    <a role="button" class="btn btn-info btn-sm glyphicon glyphicon-share-alt" onclick="onCopyItem('@index')" title="複製"></a>
                                    <a role="button" class="btn btn-danger btn-sm glyphicon glyphicon-trash" onclick="onDelItem('@index')" title="刪除"></a>
                                }

                                <label class="control-label" style="padding-left:10px;padding-right:10px;display:none">
                                    @Html.CheckBoxFor(m => m.MUST) 必填
                                </label>
                                &nbsp;&nbsp;&nbsp;
                            </div>
                        </div>
                    </div>
            }
        </div>
    </fieldset>

}