﻿@model CERI04IndexViewModel

@Html.AntiForgeryToken()
@Html.HiddenFor(m => m.Keyword)
@Html.HiddenFor(m => m.OrderByColumnName)
@Html.HiddenFor(m => m.SortType)
@Html.HiddenFor(m => m.Page)

@Html.HiddenFor(m => m.WhereSYEAR)
@Html.HiddenFor(m => m.ThisACCREDITATION_ID)
@Html.HiddenFor(m => m.ThisITEM_NO)
@Html.HiddenFor(m => m.ThisSCHOOL_NO)
@Html.HiddenFor(m => m.ThisGRADE)
@Html.HiddenFor(m => m.ThisCLASS_NO)

<div id="Q_Div">
    <div role="form">

        <div class="row">
            <div class="col-md-2">
                <label class="control-label">護照名稱</label>
            </div>
            <div class="col-md-3">
                @Html.DropDownListFor(m => m.WhereACCREDITATION_TYPE, (IEnumerable<SelectListItem>)ViewBag.AccreditationTypeItems, new { @class = "form-control input-sm" })
            </div>
            <div class="col-md-2">
                <label class="control-label">護照明細名稱</label>
            </div>
            <div class="col-md-5">
                <select class="selectpicker show-menu-arrow form-control" title="" date-style="input-sm" data-size="auto" data-live-search="true" id="@Html.IdFor(m=>m.WhereACCREDITATION_NAME)" name="@Html.NameFor(m=>m.WhereACCREDITATION_NAME)">
                    @if (ViewBag.AccreditationsItems != null)
                    {
                        foreach (var item in ViewBag.AccreditationsItems as IEnumerable<SelectListItem>)
                        {
                            <option data-tokens="@item.Text" value="@item.Value" @(item.Selected ? "selected" : "")>@item.Text</option>
                        }
                    }
                </select>
            </div>
        </div>
        <div class="row" style="padding-top:10px">
            <div class="col-md-2">
                <label class="control-label">通過主旨</label>
            </div>
            <div class="col-md-10">
                @Html.EditorFor(m => m.WhereSUBJECT, new { htmlAttributes = new { @class = "form-control input-sm" } })
            </div>
        </div>
        <div class="row" style="padding-top:10px">
            <div class="col-md-2">
                <label class="control-label">年級</label>
            </div>
            <div class="col-md-3">
                @Html.DropDownListFor(m => m.WhereGRADE, (IEnumerable<SelectListItem>)ViewBag.GradeItem, new { @class = "form-control input-sm", @onchange = "FunPageProc(1)" })
            </div>
            <div class="col-md-2">
                <label class="control-label">班級</label>
            </div>
            <div class="col-md-3">
                @Html.DropDownListFor(m => m.WhereCLASS_NO, (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control input-sm", @onchange = "FunPageProc(1)" })
            </div>
        </div>
        <div class="row" style="padding-top:10px">
            <div class="col-md-3">
                <label class="control-label">列出未認證資料</label>
                &nbsp  &nbsp
                <input type="checkbox" name="@Html.NameFor(m => m.WhereIsUnVerifier)" value="true" @(Model.WhereIsUnVerifier ? "checked" : "") />
            </div>
        </div>
        <br />
        <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1)" />
        <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear('true')" />
    </div>
</div>
<br />
<B style="color:red">現在進行批次補登，請選擇要補登的細項</B>
<div class="panel panel-ZZZ" name="TOP">
    <div class="panel-heading text-center">
        @Html.BarTitle(null, (string)ViewBag.Title)
    </div>
    <div>
        <table class="table-ecool table-92Per table-hover table-ecool-ZZZ">
            <thead>
                <tr class="text-center-Mobile-left">

                    <th class="text-nowrap">@Html.DisplaySortColumnFor(m => m.ListData.First().TYPE_NAME, Model?.OrderByColumnName, Model?.SortType, true)</th>
                    <th class="text-nowrap">@Html.DisplaySortColumnFor(m => m.ListData.First().ACCREDITATION_NAME, Model?.OrderByColumnName, Model?.SortType, true)</th>
                    <th class="text-nowrap">@Html.DisplaySortColumnFor(m => m.ListData.First().CLASS_NO, Model?.OrderByColumnName, Model?.SortType, true)</th>
                    <th class="text-nowrap">@Html.DisplaySortColumnFor(m => m.ListData.First().SUBJECT, Model?.OrderByColumnName, Model?.SortType, true)</th>
                    <th class="text-nowrap">認證老師/日期</th>
                    <th class="text-nowrap">全部通過</th>
                    <th style="width:60px">編輯</th>
                </tr>
            </thead>
            <tbody>
                @if (Model.ListData?.Count() > 0)
                {
                    foreach (var item in Model.ListData)
                    {

                        if (item.IsText != "Y")
                        {

                        <tr class="text-center-Mobile-left">
                            <td>@Html.DisplayFor(modelItem => item.TYPE_NAME)</td>
                            <td>@Html.DisplayFor(modelItem => item.ACCREDITATION_NAME)</td>
                            <td>@Html.DisplayFor(modelItem => item.CLASS_NO)</td>
                            <td>

                                <span data-html="true" data-toggle="tooltip" data-placement="right" title='@(item.CONTENT?.Replace("\n", "<br />"))'>
                                    @Html.DisplayFor(modelItem => item.SUBJECT)
                                </span>
                            </td>
                            <td>
                                @if (!string.IsNullOrWhiteSpace(item.VERIFIER))
                                {
                                    @Html.DisplayFor(modelItem => item.VERIFIER) @:， @Html.DisplayFor(modelItem => item.VERIFIED_DATE)
                                }
                            </td>
                            <td>
                                @if (item.CLASS_PASS_COUNT >= item.CLASS_COUNT)
                                {
                                    <i class="glyphicon glyphicon-ok"></i>
                                }
                            </td>
                            <td class="text-nowrap">
                                <button type="button" role="button" class="btn btn-xs btn-Basic" title="進行這項次批次補登"
                                        onclick="onBtnEdit('@item.ACCREDITATION_ID','@item.ITEM_NO','@item.SCHOOL_NO','@item.GRADE','@item.CLASS_NO')">
                                    <span class="fa fa-pencil" aria-hidden="true"></span> 補登
                                </button>
                            </td>
                        </tr>}
                    }
                }
            </tbody>
        </table>
        @if ((Model.ListData?.Count() ?? 0) == 0)
        {
            <div class="text-center" style="padding:10px"><strong>查無任何資料</strong></div>
        }
    </div>
</div>

@Html.Pager(Model.ListData.PageSize, Model.ListData.PageNumber, Model.ListData.TotalItemCount).Options(o => o.DisplayTemplate(PageGlobal.DfDisplayTemplate)
.MaxNrOfPages(PageGlobal.DfMaxNrOfPages)
.SetPreviousPageText(PageGlobal.DfSetPreviousPageText)
.SetNextPageText(PageGlobal.DfSetNextPageText)
)

<script>
    $(function () {
        $('[data-toggle="tooltip"]').tooltip()
        $('#@Html.IdFor(m=>m.WhereACCREDITATION_NAME)').selectpicker('refresh');
          $('.table-ecool').basictable({breakpoint: 767});
    })
</script>