﻿@model GameBuskerAddViewModel
@{
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
    EcoolWeb.Models.UserProfileHelper.SetLayoutCookieData(Layout);

}

<script src="~/Scripts/webcamjs/webcam.min.js"></script>

@using (Html.BeginForm("BuskerAddView", "Game", FormMethod.Post, new { id = "form1", name = "form1", enctype = "multipart/form-data", @AutoComplete = "Off" }))
{
    @Html.AntiForgeryToken()
    @Html.HiddenFor(m => m.GAME_NO)
    @Html.HiddenFor(m => m.WebCamBase64)

    <div id="MainView">
        <div class="row">
            <div class="col-md-8 col-md-offset-2">
                <div class="panel with-nav-tabs panel-info">
                    <div class="panel-heading">
                        <h1>街頭藝人報名</h1>
                    </div>
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-lg-12">
                                <div id="MainDiv">
                                    <div class="form-group">
                                        @Html.LabelFor(model => model.TITLE_SHOW_NAME, htmlAttributes: new { @class = "control-label col-md-2" })
                                        <div class="col-md-10">
                                            @Html.EditorFor(model => model.TITLE_SHOW_NAME, new { htmlAttributes = new { @class = "form-control", @placeholder = "請輸入表演名稱" } })
                                            @Html.ValidationMessageFor(model => model.TITLE_SHOW_NAME, "", new { @class = "text-danger" })
                                        </div>
                                    </div>
                                    <br />
                                    <div class="form-group" style="height:30px;"></div>
                                    <div class="form-group text-right">
                                        <button class="btn btn-default" type="button" onclick="OnNext()">下一步</button>
                                    </div>
                                    <img src="~/Content/img/show.svg" style="max-height:calc(100vh - 350px);" class="img-responsive" />
                                </div>
                                <div id="DetailsDiv" style="display:none">
                                    <div class="form-group">
                                        <div class="input-group input-group-lg">
                                            <span class="input-group-addon"><i class="fa fa-user"></i></span>
                                            @Html.EditorFor(model => model.GameUserID, new { htmlAttributes = new { @class = "form-control", @placeholder = "Username", @onKeyPress = "call(event,this);", @onblur = "GameUserIDOnblur()" } })
                                        </div>
                                        @Html.ValidationMessageFor(model => model.GameUserID, "", new { @class = "text-danger" })
                                    </div>
                                    <br />
                                    <div class="form-group text-center">
                                        請用感應數位學生證(臨時卡)或掃手機QRcode。
                                        <br />
                                        <br />
                                        <div>
                                            <img src="~/Content/img/Asset1.png" style="height:150px;padding-right:10px" />
                                            <img src="~/Content/img/Asset2.png" style="height:150px;" />
                                        </div>
                                    </div>
                                    <br />
                                    <div class="form-group">
                                        <div class="table-responsive">
                                            <div class="css-table" style="width:92%;">
                                                <div class="tr" style=" font-weight: bold;text-align:center;color:#004da0;text-overflow:ellipsis; ">
                                                    <div class="th" style="text-align:center">
                                                        刪除
                                                    </div>
                                                    <div class="th" style="text-align:center">
                                                        數位學生證卡號/<br />QR Code 申請的電話號碼
                                                    </div>
                                                    <div class="th" style="text-align:center">
                                                        姓名
                                                    </div>
                                                </div>
                                                <div id="editorRows" class="tbody">
                                                    @if (Model.Details != null)
                                                    {
                                                        foreach (var Item in Model.Details)
                                                        {
                                                            @Html.Partial("_Details", Item)
                                                        }
                                                    }
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <br />
                                    <div class="form-group" style="height:30px;"></div>
                                    <div class="form-group text-right">
                                        <button class="btn btn-default" type="button" onclick="OnNextTwo()">下一步 (使用webcam拍攝您們隊伍的照片)</button>
                                        <button class="btn btn-default" type="button" onclick="OnNextTwoUpload()">下一步 (上傳您們隊伍的照片)</button>
                                        <button class="btn btn-default" type="button" onclick="OnBuskerAddSave()">下一步 (使用系統預設的隊伍的照片)</button>
                                    </div>
                                </div>
                                <div id="webcamDiv" style="display:none">
                                    <div class="form-group text-center">
                                        <img src="~/Content/img/webcam.svg" style="height:150px;" />
                                        <br />
                                        <br />
                                        <font color="red">請對準 webcam 鏡頭</font>
                                    </div>
                                    <div class="form-group">

                                        <div class="form-group  text-center">
                                            <input type=button class="btn btn-warning" value="拍照" onClick="take_snapshot()">
                                        </div>
                                        <div id="my_camera" class="text-center" style="margin:0px auto;"></div>
                                        <br />
                                        <div id="results" class="text-center" style="margin:20px; padding:20px; border:1px solid; background:#ccc;">您拍攝的圖像將出現在這裡...</div>
                                        <br />
                                    </div>
                                    <div class="form-group" style="height:30px;"></div>
                                    <div class="form-group text-right">
                                        <button class="btn btn-default" type="button" onclick="OnBuskerAddSave()">確定報名</button>
                                    </div>
                                </div>
                                <div id="UploadDiv" style="display:none">

                                    <div class="form-group">
                                        <label class="control-label col-md-2">上傳隊伍的照片</label>
                                        <div class="col-md-10">
                                            @Html.TextBoxFor(m => m.UploadBuskerFile, new { @class = "form-control input-md", @type = "file" })
                                            @Html.ValidationMessageFor(m => m.UploadBuskerFile, "", new { @class = "text-danger" })
                                            <br />
                                            <label class="text-info">PS.請上傳 jpg、jpeg、png、gif、bmp 等格式的圖片</label>
                                        </div>
                                    </div>
                                    <div class="form-group" style="height:30px;"></div>
                                    <div class="form-group text-right">
                                        <button class="btn btn-default" type="button" onclick="OnBuskerAddSave()">確定報名</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div id="DivAddButton">
            <i id="title" class="fa fa-arrow-left fa-3x"></i>
            <button type="button" onclick="OnBack()" style="margin: -15px 0px;" class="btn btn-primary  btn-sm">回上一頁</button>
        </div>
    </div>
}
<div class="use-absolute" id="ErrorDiv">
    <div class="use-absoluteDiv">
        <div class="alert alert-danger" role="alert">
            <h1>
                <i class="fa fa-exclamation-circle"></i>
                <strong id="ErrorStr"></strong>
            </h1>
        </div>
    </div>
</div>

<div style="width:100%;height:100%;background-color:@SharedGlobal.Logo_loading_background_color;display:none;z-index:999;position:absolute" id="loading">
    <div style="margin: 0px auto;text-align:center">
        <img src="@Url.Content(SharedGlobal.Logo_loading)" style="max-height:350px;" />
        <br />
        <h3 style="color:#80b4fb">報名中…</h3>
    </div>
</div>

@section Scripts {
    <script language="JavaScript">
        var targetFormID = '#form1';

        function OnBack() {
            $(targetFormID).attr("action", "@Url.Action("PassMode", "Game")")

             $(targetFormID).submit();
        }

        function OnNext() {

            if ($('#@Html.IdFor(m=>m.TITLE_SHOW_NAME)').val() == '') {
                $('#ErrorStr').html('請輸入表演名稱')
                $('#ErrorDiv').show()
                setTimeout(function () { $('#ErrorDiv').fadeOut(2000) }, 3000);
                return false;
            }

            $('#MainDiv').hide()
            $('#DetailsDiv').show()
            $("#@Html.IdFor(m=>m.GameUserID)").focus();
        }

        function OnNextTwo() {
           if ($('#editorRows :input:hidden').not("input[name='Details.index']").length == 0) {
                $("#@Html.IdFor(m=>m.GameUserID)").focus();
                $('#ErrorStr').html('無表演人員，請感應加入表演人員')
                $('#ErrorDiv').show()
                setTimeout(function () { $('#ErrorDiv').fadeOut(2000) }, 3000);
                return false;
            }
            $('#MainDiv').hide()
            $('#DetailsDiv').hide()
            $('#webcamDiv').show()
            setTimeout(function () { OpenWebcam(); }, 1000);
        }

        function OnNextTwoUpload() {
            if ($('#editorRows :input:hidden').not("input[name='Details.index']").length == 0) {
                $("#@Html.IdFor(m=>m.GameUserID)").focus();
                $('#ErrorStr').html('無表演人員，請感應加入表演人員')
                $('#ErrorDiv').show()
                setTimeout(function () { $('#ErrorDiv').fadeOut(2000) }, 3000);
                return false;
            }
            $('#MainDiv').hide()
            $('#DetailsDiv').hide()
            $('#UploadDiv').show()
        }

        function OnBuskerAddSave() {
           if ($('#editorRows :input:hidden').not("input[name='Details.index']").length == 0) {
                $("#@Html.IdFor(m=>m.GameUserID)").focus();
                $('#ErrorStr').html('無表演人員，請感應加入表演人員')
                $('#ErrorDiv').show()
                setTimeout(function () { $('#ErrorDiv').fadeOut(2000) }, 3000);
                return false;
            }
            $('#MainView').hide()
            $('#loading').fadeIn(3000)
            $(targetFormID).attr("action", "@Url.Action("BuskerAddSave", "Game")")
            $(targetFormID).submit()
        }

        //del 項目
        function deleteRow(Id) {
            $('#' + Id).remove();
            $("#@Html.IdFor(m=>m.GameUserID)").focus();
        }

        function GameUserIDOnblur()
        {
            setTimeout(function () {

                if ($('#DetailsDiv').is(":visible") ) {
                       $("#@Html.IdFor(m=>m.GameUserID)").focus();
                }
            }, 10000);
        }

        function OpenWebcam() {
            Webcam.set({
                width: 320,
                height: 240,
                dest_width: 640,
                dest_height: 480,
                image_format: 'jpeg',
                jpeg_quality: 90
            });

            Webcam.on('error', function (err) {
                $('#ErrorStr').html(err)
                $('#ErrorDiv').show()
                setTimeout(function () { $('#ErrorDiv').fadeOut(2000) }, 3000);
            });

            Webcam.attach('#my_camera');
        }

        function take_snapshot() {
            // take snapshot and get image data
            Webcam.snap(function (data_uri) {
                // display results in page
                document.getElementById('results').innerHTML =
                    '<img src="' + data_uri + '" />';

                var base64 = data_uri//.replace(/^data\:image\/\w+\;base64\,/, '');
                $("#@Html.IdFor(m=>m.WebCamBase64)").val(base64)
            });
        }

        function call(e, input) {
            var code = (e.keyCode ? e.keyCode : e.which);

            if (code == 13) // 13 是 Enter 按鍵的值
            {
                $('#@Html.IdFor(m=>m.GameUserID)').prop('readonly', true);
                event.preventDefault();

                onAddItem()
            }
        }

          //增加明細
        function onAddItem() {

            $.ajax({
                url: '@Url.Action("_DetailsBusker")',
                data: $(targetFormID).serialize(),
                cache: false,
                success: function (html) {
                    if (html.indexOf("Error")!=-1) {
                        var res = jQuery.parseJSON(html);
                        $('#ErrorStr').html(res.Error)
                        $('#ErrorDiv').show()
                        setTimeout(function () { $('#ErrorDiv').fadeOut(2000) }, 3000);
                    }
                    else {
                        $("#editorRows").append(html);
                    }

                    $("#@Html.IdFor(m=>m.GameUserID)").val('');
                    $('#@Html.IdFor(m=>m.GameUserID)').prop('readonly', false);
                    $("#@Html.IdFor(m=>m.GameUserID)").focus();
                },
                 error: function (xhr, err) {
                    $("#@Html.IdFor(m=>m.GameUserID)").val('');
                    $('#@Html.IdFor(m=>m.GameUserID)').prop('readonly', false);
                    $("#@Html.IdFor(m=>m.GameUserID)").focus();
                    alert("readyState: " + xhr.readyState + "\nstatus: " + xhr.status);
                    alert("responseText: " + xhr.responseText);
                }
            });
        }
    </script>
}