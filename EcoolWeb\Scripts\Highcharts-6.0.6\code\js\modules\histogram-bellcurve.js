/*
  Highcharts JS v6.0.6 (2018-02-05)

 (c) 2010-2017 Highsoft AS
 Author: <PERSON>

 License: www.highcharts.com/license
*/
(function(d){"object"===typeof module&&module.exports?module.exports=d:d(Highcharts)})(function(d){var t=function(a){var d=a.each,k=a.Series,l=a.addEvent,n=a.fireEvent,e=a.wrap,h={init:function(){k.prototype.init.apply(this,arguments);this.initialised=!1;this.baseSeries=null;this.eventRemovers=[];this.addEvents()},setDerivedData:a.noop,setBaseSeries:function(){var m=this.chart,a=this.options.baseSeries;this.baseSeries=a&&(m.series[a]||m.get(a))||null},addEvents:function(){var a=this,g;g=l(this.chart,
"seriesLinked",function(){a.setBaseSeries();a.baseSeries&&!a.initialised&&(a.setDerivedData(),a.addBaseSeriesEvents(),a.initialised=!0)});this.eventRemovers.push(g)},addBaseSeriesEvents:function(){var a=this,g,d;g=l(a.baseSeries,"updatedData",function(){a.setDerivedData()});d=l(a.baseSeries,"destroy",function(){a.baseSeries=null;a.initialised=!1});a.eventRemovers.push(g,d)},destroy:function(){d(this.eventRemovers,function(a){a()});k.prototype.destroy.apply(this,arguments)}};e(a.Chart.prototype,"linkSeries",
function(a){a.call(this);n(this,"seriesLinked")});return h}(d);(function(a,d){function k(a){return function(b){return Math.floor(b/a)*a}}var l=a.each,n=a.objectEach,e=a.seriesType,h=a.correctFloat,m=a.isNumber,g=a.arrayMax,p=a.arrayMin;a=a.merge;var f={"square-root":function(a){return Math.round(Math.sqrt(a.options.data.length))},sturges:function(a){return Math.ceil(Math.log(a.options.data.length)*Math.LOG2E)},rice:function(a){return Math.ceil(2*Math.pow(a.options.data.length,1/3))}};e("histogram",
"column",{binsNumber:"square-root",binWidth:void 0,pointPadding:0,groupPadding:0,grouping:!1,pointPlacement:"between",tooltip:{headerFormat:"",pointFormat:'\x3cspan style\x3d"font-size:10px"\x3e{point.x} - {point.x2}\x3c/span\x3e\x3cbr/\x3e\x3cspan style\x3d"color:{point.color}"\x3e\u25cf\x3c/span\x3e {series.name} \x3cb\x3e{point.y}\x3c/b\x3e\x3cbr/\x3e'}},a(d,{setDerivedData:function(){var a=this.derivedData(this.baseSeries.yData,this.binsNumber(),this.options.binWidth);this.setData(a,!1)},derivedData:function(a,
c,f){var b=g(a),d=p(a),r={},e=[],q;f=this.binWidth=m(f)?f:(b-d)/c;q=k(f);for(c=q(d);c<=b;c+=f)r[h(q(c))]=0;l(a,function(a){a=h(q(a));r[a]++});n(r,function(a,b){e.push({x:Number(b),y:a,x2:h(Number(b)+f)})});e.sort(function(a,b){return a.x-b.x});return e},binsNumber:function(){var a=this.options.binsNumber,c=f[a]||"function"===typeof a&&a;return Math.ceil(c&&c(this.baseSeries)||(m(a)?a:f["square-root"](this.baseSeries)))}}))})(d,t);(function(a,d){function k(a){var b=a.length;a=p(a,function(a,b){return a+
b},0);return 0<b&&a/b}function l(a,b){var c=a.length;b=m(b)?b:k(a);a=p(a,function(a,c){c-=b;return a+c*c},0);return 1<c&&Math.sqrt(a/(c-1))}function n(a,b,c){a-=b;return Math.exp(-(a*a)/(2*c*c))/(c*Math.sqrt(2*Math.PI))}var e=a.seriesType,h=a.correctFloat,m=a.isNumber,g=a.merge,p=a.reduce;e("bellcurve","areaspline",{intervals:3,pointsInInterval:3,marker:{enabled:!1}},g(d,{setMean:function(){this.mean=h(k(this.baseSeries.yData))},setStandardDeviation:function(){this.standardDeviation=h(l(this.baseSeries.yData,
this.mean))},setDerivedData:function(){1<this.baseSeries.yData.length&&(this.setMean(),this.setStandardDeviation(),this.setData(this.derivedData(this.mean,this.standardDeviation),!1))},derivedData:function(a,b){var c=this.options.intervals,d=this.options.pointsInInterval,e=a-c*b,c=c*d*2+1,d=b/d,g=[],f;for(f=0;f<c;f++)g.push([e,n(e,a,b)]),e+=d;return g}}))})(d,t)});
