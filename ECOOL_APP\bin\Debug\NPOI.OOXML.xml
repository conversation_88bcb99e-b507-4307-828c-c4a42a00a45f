<?xml version="1.0"?>
<doc>
    <assembly>
        <name>NPOI.OOXML</name>
    </assembly>
    <members>
        <member name="T:NPOI.POIXMLDocumentPart">
             Represents an entry of a OOXML namespace.
            
             <p>
             Each POIXMLDocumentPart keeps a reference to the underlying a {@link org.apache.poi.openxml4j.opc.PackagePart}.
             </p>
            
             <AUTHOR>
        </member>
        <member name="F:NPOI.POIXMLDocumentPart.relationCounter">
            Counter that provides the amount of incoming relations from other parts
            to this part.
        </member>
        <member name="M:NPOI.POIXMLDocumentPart.#ctor(NPOI.OpenXml4Net.OPC.OPCPackage)">
            Construct POIXMLDocumentPart representing a "core document" namespace part.
        </member>
        <member name="M:NPOI.POIXMLDocumentPart.#ctor">
             Creates new POIXMLDocumentPart   - called by client code to create new parts from scratch.
            
             @see #CreateRelationship(POIXMLRelation, POIXMLFactory, int, bool)
        </member>
        <member name="M:NPOI.POIXMLDocumentPart.#ctor(NPOI.OpenXml4Net.OPC.PackagePart,NPOI.OpenXml4Net.OPC.PackageRelationship)">
             Creates an POIXMLDocumentPart representing the given namespace part and relationship.
             Called by {@link #read(POIXMLFactory, java.util.Map)} when Reading in an exisiting file.
            
             @param part - The namespace part that holds xml data represenring this sheet.
             @param rel - the relationship of the given namespace part
             @see #read(POIXMLFactory, java.util.Map) 
        </member>
        <member name="M:NPOI.POIXMLDocumentPart.#ctor(NPOI.POIXMLDocumentPart,NPOI.OpenXml4Net.OPC.PackagePart,NPOI.OpenXml4Net.OPC.PackageRelationship)">
             Creates an POIXMLDocumentPart representing the given namespace part, relationship and parent
             Called by {@link #read(POIXMLFactory, java.util.Map)} when Reading in an exisiting file.
            
             @param parent - Parent part
             @param part - The namespace part that holds xml data represenring this sheet.
             @param rel - the relationship of the given namespace part
             @see #read(POIXMLFactory, java.util.Map)
        </member>
        <member name="M:NPOI.POIXMLDocumentPart.Rebase(NPOI.OpenXml4Net.OPC.OPCPackage)">
            When you open something like a theme, call this to
             re-base the XML Document onto the core child of the
             current core document 
        </member>
        <member name="M:NPOI.POIXMLDocumentPart.GetPackagePart">
             Provides access to the underlying PackagePart
            
             @return the underlying PackagePart
        </member>
        <member name="M:NPOI.POIXMLDocumentPart.GetPackageRelationship">
             Provides access to the PackageRelationship that identifies this POIXMLDocumentPart
            
             @return the PackageRelationship that identifies this POIXMLDocumentPart
        </member>
        <member name="M:NPOI.POIXMLDocumentPart.GetRelations">
             Returns the list of child relations for this POIXMLDocumentPart
            
             @return child relations
        </member>
        <member name="M:NPOI.POIXMLDocumentPart.GetRelationById(System.String)">
            Returns the target {@link POIXMLDocumentPart}, where a
            {@link PackageRelationship} is set from the {@link PackagePart} of this
            {@link POIXMLDocumentPart} to the {@link PackagePart} of the target
            {@link POIXMLDocumentPart} with a {@link PackageRelationship#GetId()}
            matching the given parameter value.
            
            @param id
                       The relation id to look for
            @return the target part of the relation, or null, if none exists
        </member>
        <member name="M:NPOI.POIXMLDocumentPart.GetRelationId(NPOI.POIXMLDocumentPart)">
            Returns the {@link PackageRelationship#GetId()} of the
            {@link PackageRelationship}, that sources from the {@link PackagePart} of
            this {@link POIXMLDocumentPart} to the {@link PackagePart} of the given
            parameter value.
            
            @param part
                       The {@link POIXMLDocumentPart} for which the according
                       relation-id shall be found.
            @return The value of the {@link PackageRelationship#GetId()} or null, if
                    parts are not related.
        </member>
        <member name="M:NPOI.POIXMLDocumentPart.AddRelation(System.String,NPOI.POIXMLDocumentPart)">
             Add a new child POIXMLDocumentPart
            
             @param part the child to add
        </member>
        <member name="M:NPOI.POIXMLDocumentPart.RemoveRelation(NPOI.POIXMLDocumentPart)">
            Remove the relation to the specified part in this namespace and remove the
            part, if it is no longer needed.
        </member>
        <member name="M:NPOI.POIXMLDocumentPart.RemoveRelation(NPOI.POIXMLDocumentPart,System.Boolean)">
            Remove the relation to the specified part in this namespace and remove the
            part, if it is no longer needed and flag is set to true.
            
            @param part
                       The related part, to which the relation shall be Removed.
            @param RemoveUnusedParts
                       true, if the part shall be Removed from the namespace if not
                       needed any longer.
        </member>
        <member name="M:NPOI.POIXMLDocumentPart.GetParent">
             Returns the parent POIXMLDocumentPart. All parts except root have not-null parent.
            
             @return the parent POIXMLDocumentPart or <code>null</code> for the root element.
        </member>
        <member name="M:NPOI.POIXMLDocumentPart.Commit">
             Save the content in the underlying namespace part.
             Default implementation is empty meaning that the namespace part is left unmodified.
            
             Sub-classes should override and add logic to marshal the "model" into Ooxml4J.
            
             For example, the code saving a generic XML entry may look as follows:
             <pre><code>
             protected void commit()  {
               PackagePart part = GetPackagePart();
               Stream out = part.GetStream();
               XmlObject bean = GetXmlBean(); //the "model" which holds Changes in memory
               bean.save(out, DEFAULT_XML_OPTIONS);
               out.close();
             }
              </code></pre>
            
        </member>
        <member name="M:NPOI.POIXMLDocumentPart.OnSave(System.Collections.Generic.List{NPOI.OpenXml4Net.OPC.PackagePart})">
             Save Changes in the underlying OOXML namespace.
             Recursively fires {@link #commit()} for each namespace part
            
             @param alreadySaved    context set Containing already visited nodes
        </member>
        <member name="M:NPOI.POIXMLDocumentPart.CreateRelationship(NPOI.POIXMLRelation,NPOI.POIXMLFactory)">
             Create a new child POIXMLDocumentPart
            
             @param descriptor the part descriptor
             @param factory the factory that will create an instance of the requested relation
             @return the Created child POIXMLDocumentPart
        </member>
        <member name="M:NPOI.POIXMLDocumentPart.CreateRelationship(NPOI.POIXMLRelation,NPOI.POIXMLFactory,System.Int32,System.Boolean)">
             Create a new child POIXMLDocumentPart
            
             @param descriptor the part descriptor
             @param factory the factory that will create an instance of the requested relation
             @param idx part number
             @param noRelation if true, then no relationship is Added.
             @return the Created child POIXMLDocumentPart
        </member>
        <member name="M:NPOI.POIXMLDocumentPart.Read(NPOI.POIXMLFactory,System.Collections.Generic.Dictionary{NPOI.OpenXml4Net.OPC.PackagePart,NPOI.POIXMLDocumentPart})">
             Iterate through the underlying PackagePart and create child POIXMLFactory instances
             using the specified factory
            
             @param factory   the factory object that Creates POIXMLFactory instances
             @param context   context map Containing already visited noted keyed by tarGetURI
        </member>
        <member name="M:NPOI.POIXMLDocumentPart.GetTargetPart(NPOI.OpenXml4Net.OPC.PackageRelationship)">
             Get the PackagePart that is the target of a relationship.
            
             @param rel The relationship
             @return The target part
             @throws InvalidFormatException
        </member>
        <member name="M:NPOI.POIXMLDocumentPart.OnDocumentCreate">
            Fired when a new namespace part is Created
        </member>
        <member name="M:NPOI.POIXMLDocumentPart.OnDocumentRead">
            Fired when a namespace part is read
        </member>
        <member name="M:NPOI.POIXMLDocumentPart.onDocumentRemove">
            Fired when a namespace part is about to be Removed from the namespace
        </member>
        <member name="F:NPOI.POIXMLDocument.pkg">
            The OPC Package 
        </member>
        <member name="F:NPOI.POIXMLDocument.properties">
            The properties of the OPC namespace, opened as needed
        </member>
        <member name="M:NPOI.POIXMLDocument.OpenPackage(System.String)">
            Wrapper to open a namespace, returning an IOException
             in the event of a problem.
            Works around shortcomings in java's this() constructor calls
        </member>
        <member name="M:NPOI.POIXMLDocument.GetRelatedByType(System.String)">
            Retrieves all the PackageParts which are defined as
             relationships of the base document with the
             specified content type.
        </member>
        <member name="M:NPOI.POIXMLDocument.HasOOXMLHeader(System.IO.Stream)">
            Checks that the supplied Stream (which MUST
             support mark and reSet, or be a PushbackStream)
             has a OOXML (zip) header at the start of it.
            If your Stream does not support mark / reSet,
             then wrap it in a PushBackStream, then be
             sure to always use that, and not the original!
            @param inp An Stream which supports either mark/reSet, or is a PushbackStream
        </member>
        <member name="M:NPOI.POIXMLDocument.GetProperties">
            Get the document properties. This gives you access to the
             core ooxml properties, and the extended ooxml properties.
        </member>
        <member name="M:NPOI.POIXMLDocument.GetAllEmbedds">
            Get the document's embedded files.
        </member>
        <member name="M:NPOI.POIXMLDocument.Write(System.IO.Stream)">
             Write out this document to an Outputstream.
            
             @param stream - the java Stream you wish to write the file to
            
             @exception IOException if anything can't be written.
        </member>
        <member name="T:NPOI.POIXMLFactory">
             Defines a factory API that enables sub-classes to create instances of <code>POIXMLDocumentPart</code>
            
             <AUTHOR> Kozlov
        </member>
        <member name="M:NPOI.POIXMLFactory.CreateDocumentPart(NPOI.POIXMLDocumentPart,NPOI.OpenXml4Net.OPC.PackageRelationship,NPOI.OpenXml4Net.OPC.PackagePart)">
             Create a POIXMLDocumentPart from existing namespace part and relation. This method is called
             from {@link POIXMLDocument#load(POIXMLFactory)} when parsing a document
            
             @param parent parent part
             @param rel   the namespace part relationship
             @param part  the PackagePart representing the Created instance
             @return A new instance of a POIXMLDocumentPart.
        </member>
        <member name="M:NPOI.POIXMLFactory.CreateDocumentPart(NPOI.POIXMLRelation)">
             Create a new POIXMLDocumentPart using the supplied descriptor. This method is used when Adding new parts
             to a document, for example, when Adding a sheet to a workbook, slide to a presentation, etc.
            
             @param descriptor  describes the object to create
             @return A new instance of a POIXMLDocumentPart.
        </member>
        <member name="T:NPOI.CoreProperties">
            The core document properties
        </member>
        <member name="T:NPOI.ExtendedProperties">
            Extended document properties
        </member>
        <member name="T:NPOI.CustomProperties">
            Custom document properties
        </member>
        <member name="F:NPOI.CustomProperties.FORMAT_ID">
            Each custom property element Contains an fmtid attribute
            with the same GUID value ({D5CDD505-2E9C-101B-9397-08002B2CF9AE}).
        </member>
        <member name="M:NPOI.CustomProperties.Add(System.String)">
             Add a new property
            
             @param name the property name
             @throws IllegalArgumentException if a property with this name already exists
        </member>
        <member name="M:NPOI.CustomProperties.AddProperty(System.String,System.String)">
             Add a new string property
            
             @throws IllegalArgumentException if a property with this name already exists
        </member>
        <member name="M:NPOI.CustomProperties.AddProperty(System.String,System.Double)">
             Add a new double property
            
             @throws IllegalArgumentException if a property with this name already exists
        </member>
        <member name="M:NPOI.CustomProperties.AddProperty(System.String,System.Int32)">
             Add a new integer property
            
             @throws IllegalArgumentException if a property with this name already exists
        </member>
        <member name="M:NPOI.CustomProperties.AddProperty(System.String,System.Boolean)">
             Add a new bool property
            
             @throws IllegalArgumentException if a property with this name already exists
        </member>
        <member name="M:NPOI.CustomProperties.NextPid">
             Generate next id that uniquely relates a custom property
            
             @return next property id starting with 2
        </member>
        <member name="M:NPOI.CustomProperties.Contains(System.String)">
             Check if a property with this name already exists in the collection of custom properties
            
             @param name the name to check
             @return whether a property with the given name exists in the custom properties
        </member>
        <member name="T:NPOI.POIXMLProperties">
            Wrapper around the two different kinds of OOXML properties
             a document can have
        </member>
        <member name="M:NPOI.POIXMLProperties.Commit">
            Commit Changes to the underlying OPC namespace
        </member>
        <member name="P:NPOI.POIXMLProperties.CoreProperties">
            Returns the core document properties
        </member>
        <member name="P:NPOI.POIXMLProperties.ExtendedProperties">
            Returns the extended document properties
        </member>
        <member name="P:NPOI.POIXMLProperties.CustomProperties">
            Returns the custom document properties
        </member>
        <member name="T:NPOI.POIXMLPropertiesTextExtractor">
            A {@link POITextExtractor} for returning the textual
             content of the OOXML file properties, eg author
             and title.
        </member>
        <member name="F:NPOI.POIXMLTextExtractor._document">
            The POIXMLDocument that's open 
        </member>
        <member name="M:NPOI.POIXMLTextExtractor.#ctor(NPOI.POIXMLDocument)">
            Creates a new text extractor for the given document
        </member>
        <member name="M:NPOI.POIXMLTextExtractor.GetCoreProperties">
            Returns the core document properties
        </member>
        <member name="M:NPOI.POIXMLTextExtractor.GetExtendedProperties">
            Returns the extended document properties
        </member>
        <member name="M:NPOI.POIXMLTextExtractor.GetCustomProperties">
            Returns the custom document properties
        </member>
        <member name="P:NPOI.POIXMLTextExtractor.Document">
            Returns opened document
        </member>
        <member name="P:NPOI.POIXMLTextExtractor.Package">
            Returns the opened OPCPackage that Contains the document
        </member>
        <member name="P:NPOI.POIXMLTextExtractor.MetadataTextExtractor">
            Returns an OOXML properties text extractor for the
             document properties metadata, such as title and author.
        </member>
        <member name="M:NPOI.POIXMLPropertiesTextExtractor.#ctor(NPOI.POIXMLDocument)">
            Creates a new POIXMLPropertiesTextExtractor for the
             given open document.
        </member>
        <member name="M:NPOI.POIXMLPropertiesTextExtractor.#ctor(NPOI.POIXMLTextExtractor)">
            Creates a new POIXMLPropertiesTextExtractor, for the
             same file that another TextExtractor is already
             working on.
        </member>
        <member name="M:NPOI.POIXMLPropertiesTextExtractor.GetCorePropertiesText">
            Returns the core document properties, eg author
        </member>
        <member name="M:NPOI.POIXMLPropertiesTextExtractor.GetExtendedPropertiesText">
            Returns the extended document properties, eg
             application
        </member>
        <member name="M:NPOI.POIXMLPropertiesTextExtractor.GetCustomPropertiesText">
            Returns the custom document properties, if
             there are any
        </member>
        <member name="T:NPOI.POIXMLRelation">
             Represents a descriptor of a OOXML relation.
            
             <AUTHOR> Kozlov
        </member>
        <member name="F:NPOI.POIXMLRelation._type">
            Describes the content stored in a part.
        </member>
        <member name="F:NPOI.POIXMLRelation._relation">
            The kind of connection between a source part and a target part in a namespace.
        </member>
        <member name="F:NPOI.POIXMLRelation._defaultName">
            The path component of a pack URI.
        </member>
        <member name="F:NPOI.POIXMLRelation._cls">
            Defines what object is used to construct instances of this relationship
        </member>
        <member name="M:NPOI.POIXMLRelation.#ctor(System.String,System.String,System.String,System.Type)">
             Instantiates a POIXMLRelation.
            
             @param type content type
             @param rel  relationship
             @param defaultName default item name
             @param cls defines what object is used to construct instances of this relationship
        </member>
        <member name="M:NPOI.POIXMLRelation.#ctor(System.String,System.String,System.String)">
             Instantiates a POIXMLRelation.
            
             @param type content type
             @param rel  relationship
             @param defaultName default item name
        </member>
        <member name="M:NPOI.POIXMLRelation.GetFileName(System.Int32)">
            Returns the filename for the nth one of these,
             e.g. /xl/comments4.xml
        </member>
        <member name="P:NPOI.POIXMLRelation.ContentType">
             Return the content type. Content types define a media type, a subtype, and an
             optional set of parameters, as defined in RFC 2616.
            
             @return the content type
        </member>
        <member name="P:NPOI.POIXMLRelation.Relation">
             Return the relationship, the kind of connection between a source part and a target part in a namespace.
             Relationships make the connections between parts directly discoverable without looking at the content
             in the parts, and without altering the parts themselves.
            
             @return the relationship
        </member>
        <member name="P:NPOI.POIXMLRelation.DefaultFileName">
             Return the default part name. Part names are used to refer to a part in the context of a
             namespace, typically as part of a URI.
            
             @return the default part name
        </member>
        <member name="P:NPOI.POIXMLRelation.RelationClass">
             Return type of the obejct used to construct instances of this relationship
            
             @return the class of the object used to construct instances of this relation
        </member>
        <member name="M:NPOI.SS.Converter.ExcelToHtmlConverter.ProcessRow(NPOI.SS.Util.CellRangeAddress[][],NPOI.SS.UserModel.IRow,System.Xml.XmlElement)">
            @return maximum 1-base index of column that were rendered, zero if none
        </member>
        <member name="M:NPOI.SS.Converter.ExcelToHtmlConverter.ProcessColumnWidths(NPOI.SS.UserModel.ISheet,System.Int32,System.Xml.XmlElement)">
            Creates COLGROUP element with width specified for all columns. (Except
            first if <tt>{@link #isOutputRowNumbers()}==true</tt>)
        </member>
        <member name="P:NPOI.SS.Converter.ExcelToHtmlConverter.OutputColumnHeaders">
            <summary>
            是否输出列头
            </summary>
        </member>
        <member name="P:NPOI.SS.Converter.ExcelToHtmlConverter.OutputHiddenColumns">
            <summary>
            是否输出隐藏的列
            </summary>
        </member>
        <member name="P:NPOI.SS.Converter.ExcelToHtmlConverter.OutputHiddenRows">
            <summary>
            是否输出隐藏的行
            </summary>
        </member>
        <member name="P:NPOI.SS.Converter.ExcelToHtmlConverter.OutputLeadingSpacesAsNonBreaking">
            <summary>
            是否输出文本前的空格
            </summary>
        </member>
        <member name="P:NPOI.SS.Converter.ExcelToHtmlConverter.OutputRowNumbers">
            <summary>
            是否输出行号
            </summary>
        </member>
        <member name="P:NPOI.SS.Converter.ExcelToHtmlConverter.UseDivsToSpan">
            <summary>
            在跨列的单元格使用DIV标记
            </summary>
        </member>
        <member name="M:NPOI.SS.Converter.ExcelToHtmlUtils.BuildMergedRangesMap(NPOI.SS.UserModel.ISheet)">
            Creates a map (i.e. two-dimensional array) filled with ranges. Allow fast
            retrieving {@link CellRangeAddress} of any cell, if cell is contained in
            range.
            
            @see #getMergedRange(CellRangeAddress[][], int, int)
        </member>
        <member name="M:NPOI.SS.Converter.ExcelToHtmlUtils.GetColumnWidthInPx(System.Int32)">
            See <a href=
            "http://apache-poi.1045710.n5.nabble.com/Excel-Column-Width-Unit-Converter-pixels-excel-column-width-units-td2301481.html"
            >here</a> for Xio explanation and details
        </member>
        <member name="M:NPOI.SS.Converter.ExcelToHtmlUtils.GetMergedRange(NPOI.SS.Util.CellRangeAddress[][],System.Int32,System.Int32)">
            @param mergedRanges
                       map of sheet merged ranges built with
                       {@link #buildMergedRangesMap(HSSFSheet)}
            @return {@link CellRangeAddress} from map if cell with specified row and
                    column numbers contained in found range, <tt>null</tt> otherwise
        </member>
        <member name="T:NPOI.SS.UserModel.IndexedColors">
             A deprecated indexing scheme for colours that is still required for some records, and for backwards
              compatibility with OLE2 formats.
            
             <p>
             Each element corresponds to a color index (zero-based). When using the default indexed color palette,
             the values are not written out, but instead are implied. When the color palette has been modified from default,
             then the entire color palette is used.
             </p>
            
             <AUTHOR> Kozlov
        </member>
        <member name="P:NPOI.SS.UserModel.IndexedColors.Index">
             Returns index of this color
            
             @return index of this color
        </member>
        <member name="F:NPOI.SS.UserModel.ImportOption.SheetContentOnly">
            <summary>
            Only Text and Formulas are imported. Pictures, Drawing, Styles etc. are all ignored.
            </summary>
        </member>
        <member name="F:NPOI.SS.UserModel.ImportOption.TextOnly">
            <summary>
            Only Text, Comments and Formulas are imported. Pictures, Drawing, Styles etc. are all ignored.
            </summary>
        </member>
        <member name="F:NPOI.SS.UserModel.ImportOption.All">
            <summary>
            Everything is imported - this is the same as NONE.
            </summary>
        </member>
        <member name="T:NPOI.SS.UserModel.WorkbookFactory">
            <summary>
            Factory for creating the appropriate kind of Workbook
            (be it HSSFWorkbook or XSSFWorkbook), from the given input
            </summary>
        </member>
        <member name="M:NPOI.SS.UserModel.WorkbookFactory.Create(NPOI.POIFS.FileSystem.POIFSFileSystem)">
            <summary>
            Creates an HSSFWorkbook from the given POIFSFileSystem
            </summary>
        </member>
        <member name="M:NPOI.SS.UserModel.WorkbookFactory.Create(NPOI.POIFS.FileSystem.NPOIFSFileSystem)">
            Creates an HSSFWorkbook from the given NPOIFSFileSystem
        </member>
        <member name="M:NPOI.SS.UserModel.WorkbookFactory.Create(NPOI.OpenXml4Net.OPC.OPCPackage)">
            <summary>
            Creates an XSSFWorkbook from the given OOXML Package
            </summary>
        </member>
        <member name="M:NPOI.SS.UserModel.WorkbookFactory.Create(System.IO.Stream)">
            <summary>
            Creates the appropriate HSSFWorkbook / XSSFWorkbook from
            the given InputStream. The Stream is wraped inside a PushbackInputStream.
            </summary>
            <param name="inputStream">Input Stream of .xls or .xlsx file</param>
            <returns>IWorkbook depending on the input HSSFWorkbook or XSSFWorkbook is returned.</returns>
        </member>
        <member name="M:NPOI.SS.UserModel.WorkbookFactory.Create(System.String)">
            Creates the appropriate HSSFWorkbook / XSSFWorkbook from
             the given File, which must exist and be readable.
        </member>
        <member name="M:NPOI.SS.UserModel.WorkbookFactory.Create(System.IO.Stream,NPOI.SS.UserModel.ImportOption)">
            <summary>
            Creates the appropriate HSSFWorkbook / XSSFWorkbook from
            the given InputStream. The Stream is wraped inside a PushbackInputStream.
            </summary>
            <param name="inputStream">Input Stream of .xls or .xlsx file</param>
            <param name="importOption">Customize the elements that are processed on the next import</param>
            <returns>IWorkbook depending on the input HSSFWorkbook or XSSFWorkbook is returned.</returns>
        </member>
        <member name="M:NPOI.SS.UserModel.WorkbookFactory.CreateFormulaEvaluator(NPOI.SS.UserModel.IWorkbook)">
            <summary>
            Creates a specific FormulaEvaluator for the given workbook.
            </summary>
        </member>
        <member name="M:NPOI.SS.UserModel.WorkbookFactory.SetImportOption(NPOI.SS.UserModel.ImportOption)">
            <summary>
            Sets the import option when opening the next workbook.
            Works only for XSSF. For HSSF workbooks this option is ignored.
            </summary>
            <param name="importOption">Customize the elements that are processed on the next import</param>
        </member>
        <member name="T:NPOI.Util.IdentifierManager">
            <summary>
            24.08.2009 <AUTHOR> Stern
            </summary>
        </member>
        <member name="F:NPOI.Util.IdentifierManager.upperbound">
            
        </member>
        <member name="F:NPOI.Util.IdentifierManager.lowerbound">
            
        </member>
        <member name="F:NPOI.Util.IdentifierManager.segments">
            List of segments of available identifiers
        </member>
        <member name="M:NPOI.Util.IdentifierManager.#ctor(System.Int64,System.Int64)">
            @param lowerbound the lower limit of the id-range to manage. Must be greater than or equal to {@link #MIN_ID}.
            @param upperbound the upper limit of the id-range to manage. Must be less then or equal {@link #MAX_ID}.
        </member>
        <member name="M:NPOI.Util.IdentifierManager.ReserveNew">
            @return a new identifier. 
            @throws IllegalStateException if no more identifiers are available, then an Exception is raised.
        </member>
        <member name="M:NPOI.Util.IdentifierManager.Release(System.Int64)">
            @param id
            the identifier to release. Must be greater than or equal to
            {@link #lowerbound} and must be less than or equal to {@link #upperbound}
            @return true, if the identifier was reserved and has been successfully
            released, false, if the identifier was not reserved.
        </member>
        <member name="M:NPOI.Util.IdentifierManager.VerifyIdentifiersLeft">
            
        </member>
        <member name="T:NPOI.Util.PackageHelper">
             Provides handy methods to work with OOXML namespaces
            
             <AUTHOR> Kozlov
        </member>
        <member name="M:NPOI.Util.PackageHelper.Clone(NPOI.OpenXml4Net.OPC.OPCPackage,System.String)">
             Clone the specified namespace.
            
             @param   pkg   the namespace to clone
             @param   file  the destination file
             @return  the Cloned namespace
        </member>
        <member name="M:NPOI.Util.PackageHelper.CreateTempFile">
            Creates an empty file in the default temporary-file directory,
        </member>
        <member name="M:NPOI.Util.PackageHelper.Copy(NPOI.OpenXml4Net.OPC.OPCPackage,NPOI.OpenXml4Net.OPC.PackagePart,NPOI.OpenXml4Net.OPC.OPCPackage,NPOI.OpenXml4Net.OPC.PackagePart)">
            Recursively copy namespace parts to the destination namespace
        </member>
        <member name="M:NPOI.Util.PackageHelper.CopyProperties(NPOI.OpenXml4Net.OPC.PackageProperties,NPOI.OpenXml4Net.OPC.PackageProperties)">
             Copy core namespace properties
            
             @param src source properties
             @param tgt target properties
        </member>
        <member name="T:NPOI.Util.Units">
            <AUTHOR> Kozlov
        </member>
        <member name="T:NPOI.XSSF.Extractor.XSSFExcelExtractor">
            Helper class to extract text from an OOXML Excel file
        </member>
        <member name="M:NPOI.XSSF.Extractor.XSSFExcelExtractor.SetIncludeSheetNames(System.Boolean)">
            Should sheet names be included? Default is true
        </member>
        <member name="M:NPOI.XSSF.Extractor.XSSFExcelExtractor.SetFormulasNotResults(System.Boolean)">
            Should we return the formula itself, and not
             the result it produces? Default is false
        </member>
        <member name="M:NPOI.XSSF.Extractor.XSSFExcelExtractor.SetIncludeCellComments(System.Boolean)">
            Should cell comments be included? Default is true
        </member>
        <member name="M:NPOI.XSSF.Extractor.XSSFExcelExtractor.SetIncludeHeadersFooters(System.Boolean)">
            Should headers and footers be included? Default is true
        </member>
        <member name="P:NPOI.XSSF.Extractor.XSSFExcelExtractor.IncludeHeaderFooter">
            <summary>
             Should header and footer be included? Default is true
            </summary>
        </member>
        <member name="P:NPOI.XSSF.Extractor.XSSFExcelExtractor.IncludeSheetNames">
            <summary>
            Should sheet names be included? Default is true
            </summary>
            <value>if set to <c>true</c> [include sheet names].</value>
        </member>
        <member name="P:NPOI.XSSF.Extractor.XSSFExcelExtractor.FormulasNotResults">
            <summary>
            Should we return the formula itself, and not
            the result it produces? Default is false
            </summary>
            <value>if set to <c>true</c> [formulas not results].</value>
        </member>
        <member name="P:NPOI.XSSF.Extractor.XSSFExcelExtractor.IncludeCellComments">
            <summary>
            Should cell comments be included? Default is false
            </summary>
            <value>if set to <c>true</c> [include cell comments].</value>
        </member>
        <member name="P:NPOI.XSSF.Extractor.XSSFExcelExtractor.Text">
            Retreives the text contents of the file
        </member>
        <member name="T:NPOI.XSSF.Extractor.XSSFExportToXml">
            
             Maps an XLSX to an XML according to one of the mapping defined.
            
            
             The output XML Schema must respect this limitations:
            
             <ul>
             <li> all mandatory elements and attributes must be mapped (enable validation to check this)</li>
            
             <li> no &lt;any&gt; in complex type/element declaration </li>
             <li> no &lt;anyAttribute&gt; attributes declaration </li>
             <li> no recursive structures: recursive structures can't be nested more than one level </li>
             <li> no abstract elements: abstract complex types can be declared but must not be used in elements. </li>
             <li> no mixed content: an element can't contain simple text and child element(s) together </li>
             <li> no &lt;substitutionGroup&gt; in complex type/element declaration </li>
             </ul>
        </member>
        <member name="M:NPOI.XSSF.Extractor.XSSFExportToXml.#ctor(NPOI.XSSF.UserModel.XSSFMap)">
             Creates a new exporter and Sets the mapping to be used when generating the XML output document
            
             @param map the mapping rule to be used
        </member>
        <member name="M:NPOI.XSSF.Extractor.XSSFExportToXml.ExportToXML(System.IO.Stream,System.Boolean)">
            
             Exports the data in an XML stream
            
             @param os OutputStream in which will contain the output XML
             @param validate if true, validates the XML againts the XML Schema
             @throws SAXException
             @throws TransformerException  
             @throws ParserConfigurationException 
        </member>
        <member name="M:NPOI.XSSF.Extractor.XSSFExportToXml.ExportToXML(System.IO.Stream,System.String,System.Boolean)">
             Exports the data in an XML stream
            
             @param os OutputStream in which will contain the output XML
             @param encoding the output charset encoding
             @param validate if true, validates the XML againts the XML Schema
             @throws SAXException
             @throws ParserConfigurationException 
             @throws TransformerException 
             @throws InvalidFormatException
        </member>
        <member name="M:NPOI.XSSF.Extractor.XSSFExportToXml.IsValid(System.Xml.XmlDocument)">
             Validate the generated XML against the XML Schema associated with the XSSFMap
            
             @param xml the XML to validate
             @return
        </member>
        <member name="M:NPOI.XSSF.Extractor.XSSFExportToXml.Compare(System.String,System.String)">
             Compares two xpaths to define an ordering according to the XML Schema
            
        </member>
        <member name="T:NPOI.XSSF.Model.CalculationChain">
             The cells in a workbook can be calculated in different orders depending on various optimizations and
             dependencies. The calculation chain object specifies the order in which the cells in a workbook were last calculated.
            
             <AUTHOR> Kozlov
        </member>
        <member name="M:NPOI.XSSF.Model.CalculationChain.RemoveItem(System.Int32,System.String)">
            Remove a formula reference from the calculation chain
            
            @param sheetId  the sheet Id of a sheet the formula belongs to.
            @param ref  A1 style reference to the cell Containing the formula.
        </member>
        <member name="F:NPOI.XSSF.Model.CommentsTable.commentRefs">
            XML Beans uses a list, which is very slow
             to search, so we wrap things with our own
             map for fast Lookup.
        </member>
        <member name="M:NPOI.XSSF.Model.CommentsTable.ReferenceUpdated(System.String,NPOI.OpenXmlFormats.Spreadsheet.CT_Comment)">
            Called after the reference is updated, so that
             we can reflect that in our cache
        </member>
        <member name="M:NPOI.XSSF.Model.CommentsTable.FindAuthor(System.String)">
            <summary>
            Searches the author. If not found he is added to the list of authors.
            </summary>
            <param name="author">author to search</param>
            <returns>index of the author</returns>
        </member>
        <member name="T:NPOI.XSSF.Model.IndexedUDFFinder">
             A UDFFinder that can retrieve functions both by name and by fake index.
            
             <AUTHOR> Kozlov
        </member>
        <member name="T:NPOI.XSSF.Model.MapInfo">
            
            This class : the Custom XML Mapping Part (Open Office XML Part 1:
            chapter 12.3.6)
            
            An instance of this part type Contains a schema for an XML file, and
            information on the behavior that is used when allowing this custom XML schema
            to be mapped into the spreadsheet.
            
            <AUTHOR> Manicardi
        </member>
        <member name="M:NPOI.XSSF.Model.MapInfo.GetCTMapInfo">
            
            @return the internal data object
        </member>
        <member name="M:NPOI.XSSF.Model.MapInfo.GetCTSchemaById(System.String)">
            Gets the
            @param schemaId the schema ID
            @return CTSchema by it's ID
        </member>
        <member name="M:NPOI.XSSF.Model.MapInfo.GetAllXSSFMaps">
            
            @return all the mappings configured in this document
        </member>
        <member name="P:NPOI.XSSF.Model.MapInfo.Workbook">
             Returns the parent XSSFWorkbook
            
             @return the parent XSSFWorkbook
        </member>
        <member name="T:NPOI.XSSF.Model.SharedStringsTable">
             Table of strings shared across all sheets in a workbook.
             <p>
             A workbook may contain thousands of cells Containing string (non-numeric) data. Furthermore this data is very
             likely to be repeated across many rows or columns. The goal of implementing a single string table that is shared
             across the workbook is to improve performance in opening and saving the file by only Reading and writing the
             repetitive information once.
             </p>
             <p>
             Consider for example a workbook summarizing information for cities within various countries. There may be a
             column for the name of the country, a column for the name of each city in that country, and a column
             Containing the data for each city. In this case the country name is repetitive, being duplicated in many cells.
             In many cases the repetition is extensive, and a tremendous savings is realized by making use of a shared string
             table when saving the workbook. When displaying text in the spreadsheet, the cell table will just contain an
             index into the string table as the value of a cell, instead of the full string.
             </p>
             <p>
             The shared string table Contains all the necessary information for displaying the string: the text, formatting
             properties, and phonetic properties (for East Asian languages).
             </p>
            
             <AUTHOR> Birch
             <AUTHOR> Kozlov
        </member>
        <member name="F:NPOI.XSSF.Model.SharedStringsTable.strings">
            Array of individual string items in the Shared String table.
        </member>
        <member name="F:NPOI.XSSF.Model.SharedStringsTable.stmap">
            Maps strings and their indexes in the <code>strings</code> arrays
        </member>
        <member name="F:NPOI.XSSF.Model.SharedStringsTable.count">
            An integer representing the total count of strings in the workbook. This count does not
            include any numbers, it counts only the total of text strings in the workbook.
        </member>
        <member name="F:NPOI.XSSF.Model.SharedStringsTable.uniqueCount">
            An integer representing the total count of unique strings in the Shared String Table.
            A string is unique even if it is a copy of another string, but has different formatting applied
            at the character level.
        </member>
        <member name="M:NPOI.XSSF.Model.SharedStringsTable.GetEntryAt(System.Int32)">
             Return a string item by index
            
             @param idx index of item to return.
             @return the item at the specified position in this Shared String table.
        </member>
        <member name="M:NPOI.XSSF.Model.SharedStringsTable.AddEntry(NPOI.OpenXmlFormats.Spreadsheet.CT_Rst)">
             Add an entry to this Shared String table (a new value is appened to the end).
            
             <p>
             If the Shared String table already Contains this <code>CT_Rst</code> bean, its index is returned.
             Otherwise a new entry is aded.
             </p>
            
             @param st the entry to add
             @return index the index of Added entry
        </member>
        <member name="M:NPOI.XSSF.Model.SharedStringsTable.WriteTo(System.IO.Stream)">
            
            this table out as XML.
            
            @param out The stream to write to.
            @throws IOException if an error occurs while writing.
        </member>
        <member name="P:NPOI.XSSF.Model.SharedStringsTable.Count">
             Return an integer representing the total count of strings in the workbook. This count does not
             include any numbers, it counts only the total of text strings in the workbook.
            
             @return the total count of strings in the workbook
        </member>
        <member name="P:NPOI.XSSF.Model.SharedStringsTable.UniqueCount">
             Returns an integer representing the total count of unique strings in the Shared String Table.
             A string is unique even if it is a copy of another string, but has different formatting applied
             at the character level.
            
             @return the total count of unique strings in the workbook
        </member>
        <member name="P:NPOI.XSSF.Model.SharedStringsTable.Items">
             Provide low-level access to the underlying array of CT_Rst beans
            
             @return array of CT_Rst beans
        </member>
        <member name="T:NPOI.XSSF.Model.SingleXmlCells">
             
             This class : the Single Cell Tables Part (Open Office XML Part 4:
             chapter 3.5.2)
             
            
             <AUTHOR> Manicardi
        </member>
        <member name="M:NPOI.XSSF.Model.SingleXmlCells.GetAllSimpleXmlCell">
            
            @return all the SimpleXmlCell Contained in this SingleXmlCells element
        </member>
        <member name="T:NPOI.XSSF.Model.StylesTable">
             Table of styles shared across all sheets in a workbook.
            
             <AUTHOR>
        </member>
        <member name="F:NPOI.XSSF.Model.StylesTable.FIRST_CUSTOM_STYLE_ID">
            The first style id available for use as a custom style
        </member>
        <member name="M:NPOI.XSSF.Model.StylesTable.#ctor">
            Create a new, empty StylesTable
        </member>
        <member name="M:NPOI.XSSF.Model.StylesTable.ReadFrom(System.Xml.XmlDocument)">
             Read this shared styles table from an XML file.
            
             @param is The input stream Containing the XML document.
             @throws IOException if an error occurs while Reading.
        </member>
        <member name="M:NPOI.XSSF.Model.StylesTable.PutFont(NPOI.XSSF.UserModel.XSSFFont,System.Boolean)">
            Records the given font in the font table.
            Will re-use an existing font index if this
             font matches another, EXCEPT if forced
             registration is requested.
            This allows people to create several fonts
             then customise them later.
            Note - End Users probably want to call
             {@link XSSFFont#registerTo(StylesTable)}
        </member>
        <member name="M:NPOI.XSSF.Model.StylesTable.GetCTStylesheet">
            For unit testing only!
        </member>
        <member name="M:NPOI.XSSF.Model.StylesTable.WriteTo(System.IO.Stream)">
             Write this table out as XML.
            
             @param out The stream to write to.
             @throws IOException if an error occurs while writing.
        </member>
        <member name="M:NPOI.XSSF.Model.StylesTable.FindFont(System.Int16,System.Int16,System.Int16,System.String,System.Boolean,System.Boolean,NPOI.SS.UserModel.FontSuperScript,NPOI.SS.UserModel.FontUnderlineType)">
            Finds a font that matches the one with the supplied attributes
        </member>
        <member name="P:NPOI.XSSF.Model.StylesTable.NumCellStyles">
            get the size of cell styles
        </member>
        <member name="P:NPOI.XSSF.Model.StylesTable.NumberFormatSize">
            For unit testing only
        </member>
        <member name="P:NPOI.XSSF.Model.StylesTable.XfsSize">
            For unit testing only
        </member>
        <member name="P:NPOI.XSSF.Model.StylesTable.StyleXfsSize">
            For unit testing only
        </member>
        <member name="T:NPOI.XSSF.Model.ThemesTable">
            Class that represents theme of XLSX document. The theme includes specific
            colors and fonts.
            
            <AUTHOR> Udalau(Petr.Udalau at exigenservices.com) - theme colors
        </member>
        <member name="M:NPOI.XSSF.Model.ThemesTable.InheritFromThemeAsRequired(NPOI.XSSF.UserModel.XSSFColor)">
            If the colour is based on a theme, then inherit 
             information (currently just colours) from it as
             required.
        </member>
        <member name="T:NPOI.XSSF.UserModel.Charts.XSSFChartAxis">
             Base class for all axis types.
            
             <AUTHOR> Kashitsyn
        </member>
        <member name="T:NPOI.XSSF.UserModel.Charts.XSSFChartDataFactory">
            <AUTHOR> Kashitsyn
        </member>
        <member name="M:NPOI.XSSF.UserModel.Charts.XSSFChartDataFactory.CreateScatterChartData``2">
            @return new scatter chart data instance
        </member>
        <member name="M:NPOI.XSSF.UserModel.Charts.XSSFChartDataFactory.GetInstance">
            @return factory instance
        </member>
        <member name="T:NPOI.XSSF.UserModel.Charts.XSSFChartLegend">
            Represents a SpreadsheetML chart legend
            <AUTHOR> Kashitsyn
        </member>
        <member name="F:NPOI.XSSF.UserModel.Charts.XSSFChartLegend.legend">
            Underlaying CTLagend bean
        </member>
        <member name="M:NPOI.XSSF.UserModel.Charts.XSSFChartLegend.#ctor(NPOI.XSSF.UserModel.XSSFChart)">
            Create a new SpreadsheetML chart legend
        </member>
        <member name="M:NPOI.XSSF.UserModel.Charts.XSSFChartLegend.GetCTLegend">
             Return the underlying CTLegend bean.
            
             @return the underlying CTLegend bean
        </member>
        <member name="T:NPOI.XSSF.UserModel.Charts.XSSFChartUtil">
             Package private class with utility methods.
            
             <AUTHOR> Kashitsyn
        </member>
        <member name="M:NPOI.XSSF.UserModel.Charts.XSSFChartUtil.BuildAxDataSource``1(NPOI.OpenXmlFormats.Dml.Chart.CT_AxDataSource,NPOI.SS.UserModel.Charts.IChartDataSource{``0})">
            Builds CTAxDataSource object content from POI ChartDataSource.
            @param ctAxDataSource OOXML data source to build
            @param dataSource POI data source to use
        </member>
        <member name="M:NPOI.XSSF.UserModel.Charts.XSSFChartUtil.BuildNumDataSource``1(NPOI.OpenXmlFormats.Dml.Chart.CT_NumDataSource,NPOI.SS.UserModel.Charts.IChartDataSource{``0})">
            Builds CTNumDataSource object content from POI ChartDataSource
            @param ctNumDataSource OOXML data source to build
            @param dataSource POI data source to use
        </member>
        <member name="F:NPOI.XSSF.UserModel.Charts.XSSFLineChartData`2.series">
            List of all data series.
        </member>
        <member name="T:NPOI.XSSF.UserModel.Charts.XSSFManualLayout">
            Represents a SpreadsheetML manual layout.
            <AUTHOR> Kashitsyn
        </member>
        <member name="F:NPOI.XSSF.UserModel.Charts.XSSFManualLayout.layout">
            Underlaying CTManualLayout bean.
        </member>
        <member name="M:NPOI.XSSF.UserModel.Charts.XSSFManualLayout.#ctor(NPOI.OpenXmlFormats.Dml.Chart.CT_Layout)">
            Create a new SpreadsheetML manual layout.
            @param layout a Spreadsheet ML layout that should be used as base.
        </member>
        <member name="M:NPOI.XSSF.UserModel.Charts.XSSFManualLayout.#ctor(NPOI.XSSF.UserModel.XSSFChart)">
            Create a new SpreadsheetML manual layout for chart.
            @param chart a chart to create layout for.
        </member>
        <member name="M:NPOI.XSSF.UserModel.Charts.XSSFManualLayout.GetCTManualLayout">
             Return the underlying CTManualLayout bean.
            
             @return the underlying CTManualLayout bean.
        </member>
        <member name="T:NPOI.XSSF.UserModel.Charts.XSSFScatterChartData`2">
             Represents DrawingML scatter chart.
            
             <AUTHOR> Kashitsyn
        </member>
        <member name="F:NPOI.XSSF.UserModel.Charts.XSSFScatterChartData`2.series">
            List of all data series.
        </member>
        <member name="T:NPOI.XSSF.UserModel.Charts.XSSFScatterChartData`2.Serie">
            Package private ScatterChartSerie implementation.
        </member>
        <member name="M:NPOI.XSSF.UserModel.Charts.XSSFScatterChartData`2.Serie.GetXValues">
            Returns data source used for X axis values.
            @return data source used for X axis values
        </member>
        <member name="M:NPOI.XSSF.UserModel.Charts.XSSFScatterChartData`2.Serie.GetYValues">
            Returns data source used for Y axis values.
            @return data source used for Y axis values
        </member>
        <member name="M:NPOI.XSSF.UserModel.Charts.XSSFScatterChartData`2.Serie.SetUseCache(System.Boolean)">
            @param useCache if true, cached results will be Added on plot
        </member>
        <member name="T:NPOI.XSSF.UserModel.Charts.XSSFValueAxis">
             Value axis type.
            
             <AUTHOR> Kashitsyn
        </member>
        <member name="T:NPOI.XSSF.UserModel.Extensions.XSSFCellAlignment">
            Cell Settings avaiable in the Format/Alignment tab
        </member>
        <member name="M:NPOI.XSSF.UserModel.Extensions.XSSFCellAlignment.#ctor(NPOI.OpenXmlFormats.Spreadsheet.CT_CellAlignment)">
             Creates a Cell Alignment from the supplied XML defInition
            
             @param cellAlignment
        </member>
        <member name="M:NPOI.XSSF.UserModel.Extensions.XSSFCellAlignment.GetCTCellAlignment">
            Access to low-level data
        </member>
        <member name="P:NPOI.XSSF.UserModel.Extensions.XSSFCellAlignment.Vertical">
             Get the type of vertical alignment for the cell
            
             @return the type of aligment
             @see VerticalAlignment
        </member>
        <member name="P:NPOI.XSSF.UserModel.Extensions.XSSFCellAlignment.Horizontal">
             Get the type of horizontal alignment for the cell
            
             @return the type of aligment
             @see HorizontalAlignment
        </member>
        <member name="P:NPOI.XSSF.UserModel.Extensions.XSSFCellAlignment.Indent">
             Get the number of spaces to indent the text in the cell
            
             @return indent - number of spaces
        </member>
        <!-- 对于成员“P:NPOI.XSSF.UserModel.Extensions.XSSFCellAlignment.TextRotation”忽略有格式错误的 XML 注释 -->
        <member name="P:NPOI.XSSF.UserModel.Extensions.XSSFCellAlignment.WrapText">
             Whether the text should be wrapped
            
             @return a bool value indicating if the text in a cell should be line-wrapped within the cell.
        </member>
        <member name="T:NPOI.XSSF.UserModel.Extensions.BorderSide">
            The enumeration value indicating the side being used for a cell border.
        </member>
        <member name="T:NPOI.XSSF.UserModel.Extensions.XSSFCellBorder">
            This element Contains border formatting information, specifying border defInition formats (left, right, top, bottom, diagonal)
            for cells in the workbook.
            Color is optional.
        </member>
        <member name="M:NPOI.XSSF.UserModel.Extensions.XSSFCellBorder.#ctor(NPOI.OpenXmlFormats.Spreadsheet.CT_Border,NPOI.XSSF.Model.ThemesTable)">
            Creates a Cell Border from the supplied XML defInition
        </member>
        <member name="M:NPOI.XSSF.UserModel.Extensions.XSSFCellBorder.#ctor(NPOI.OpenXmlFormats.Spreadsheet.CT_Border)">
            Creates a Cell Border from the supplied XML defInition
        </member>
        <member name="M:NPOI.XSSF.UserModel.Extensions.XSSFCellBorder.#ctor">
            Creates a new, empty Cell Border.
            You need to attach this to the Styles Table
        </member>
        <member name="M:NPOI.XSSF.UserModel.Extensions.XSSFCellBorder.SetThemesTable(NPOI.XSSF.Model.ThemesTable)">
            Records the Themes Table that is associated with
             the current font, used when looking up theme
             based colours and properties.
        </member>
        <member name="M:NPOI.XSSF.UserModel.Extensions.XSSFCellBorder.GetCTBorder">
             Returns the underlying XML bean.
            
             @return CT_Border
        </member>
        <member name="M:NPOI.XSSF.UserModel.Extensions.XSSFCellBorder.GetBorderStyle(NPOI.XSSF.UserModel.Extensions.BorderSide)">
             Get the type of border to use for the selected border
            
             @param side -  - where to apply the color defInition
             @return borderstyle - the type of border to use. default value is NONE if border style is not Set.
             @see BorderStyle
        </member>
        <member name="M:NPOI.XSSF.UserModel.Extensions.XSSFCellBorder.SetBorderStyle(NPOI.XSSF.UserModel.Extensions.BorderSide,NPOI.SS.UserModel.BorderStyle)">
             Set the type of border to use for the selected border
            
             @param side  -  - where to apply the color defInition
             @param style - border style
             @see BorderStyle
        </member>
        <member name="M:NPOI.XSSF.UserModel.Extensions.XSSFCellBorder.GetBorderColor(NPOI.XSSF.UserModel.Extensions.BorderSide)">
             Get the color to use for the selected border
            
             @param side - where to apply the color defInition
             @return color - color to use as XSSFColor. null if color is not set
        </member>
        <member name="M:NPOI.XSSF.UserModel.Extensions.XSSFCellBorder.SetBorderColor(NPOI.XSSF.UserModel.Extensions.BorderSide,NPOI.XSSF.UserModel.XSSFColor)">
             Set the color to use for the selected border
            
             @param side  - where to apply the color defInition
             @param color - the color to use
        </member>
        <member name="T:NPOI.XSSF.UserModel.Extensions.XSSFCellFill">
            This element specifies fill formatting.
            A cell fill consists of a background color, foreground color, and pattern to be applied across the cell.
        </member>
        <member name="M:NPOI.XSSF.UserModel.Extensions.XSSFCellFill.#ctor(NPOI.OpenXmlFormats.Spreadsheet.CT_Fill)">
             Creates a CellFill from the supplied parts
            
             @param fill - fill
        </member>
        <member name="M:NPOI.XSSF.UserModel.Extensions.XSSFCellFill.#ctor">
            Creates an empty CellFill
        </member>
        <member name="M:NPOI.XSSF.UserModel.Extensions.XSSFCellFill.GetFillBackgroundColor">
             Get the background fill color.
            
             @return fill color, null if color is not set
        </member>
        <member name="M:NPOI.XSSF.UserModel.Extensions.XSSFCellFill.SetFillBackgroundColor(System.Int32)">
             Set the background fill color represented as a indexed color value.
            
             @param index
        </member>
        <member name="M:NPOI.XSSF.UserModel.Extensions.XSSFCellFill.SetFillBackgroundColor(NPOI.XSSF.UserModel.XSSFColor)">
             Set the background fill color represented as a {@link XSSFColor} value.
            
             @param color
        </member>
        <member name="M:NPOI.XSSF.UserModel.Extensions.XSSFCellFill.GetFillForegroundColor">
             Get the foreground fill color.
            
             @return XSSFColor - foreground color. null if color is not set
        </member>
        <member name="M:NPOI.XSSF.UserModel.Extensions.XSSFCellFill.SetFillForegroundColor(System.Int32)">
             Set the foreground fill color as a indexed color value
            
             @param index - the color to use
        </member>
        <member name="M:NPOI.XSSF.UserModel.Extensions.XSSFCellFill.SetFillForegroundColor(NPOI.XSSF.UserModel.XSSFColor)">
             Set the foreground fill color represented as a {@link XSSFColor} value.
            
             @param color - the color to use
        </member>
        <member name="M:NPOI.XSSF.UserModel.Extensions.XSSFCellFill.GetPatternType">
             get the fill pattern
            
             @return fill pattern type. null if fill pattern is not set
        </member>
        <member name="M:NPOI.XSSF.UserModel.Extensions.XSSFCellFill.SetPatternType(NPOI.OpenXmlFormats.Spreadsheet.ST_PatternType)">
             set the fill pattern
            
             @param patternType fill pattern to use
        </member>
        <member name="M:NPOI.XSSF.UserModel.Extensions.XSSFCellFill.GetCTFill">
             Returns the underlying XML bean.
            
             @return CT_Fill
        </member>
        <member name="T:NPOI.XSSF.UserModel.Extensions.XSSFHeaderFooter">
            <summary>
            Parent class of all XSSF headers and footers.
            </summary>
        </member>
        <member name="M:NPOI.XSSF.UserModel.Extensions.XSSFHeaderFooter.#ctor(NPOI.OpenXmlFormats.Spreadsheet.CT_HeaderFooter)">
             Create an instance of XSSFHeaderFooter from the supplied XML bean
            
             @param headerFooter
        </member>
        <member name="M:NPOI.XSSF.UserModel.Extensions.XSSFHeaderFooter.GetHeaderFooter">
             Returns the underlying CTHeaderFooter xml bean
            
             @return the underlying CTHeaderFooter xml bean
        </member>
        <member name="M:NPOI.XSSF.UserModel.Extensions.XSSFHeaderFooter.AreFieldsStripped">
            Are fields currently being stripped from the text that this
            {@link XSSFHeaderFooter} returns? Default is false, but can be Changed
        </member>
        <member name="M:NPOI.XSSF.UserModel.Extensions.XSSFHeaderFooter.SetAreFieldsStripped(System.Boolean)">
             Should fields (eg macros) be stripped from the text that this class
             returns? Default is not to strip.
            
             @param StripFields
        </member>
        <member name="M:NPOI.XSSF.UserModel.Extensions.XSSFHeaderFooter.StripFields(System.String)">
            Removes any fields (eg macros, page markers etc) from the string.
            Normally used to make some text suitable for showing to humans, and the
            resultant text should not normally be saved back into the document!
        </member>
        <member name="P:NPOI.XSSF.UserModel.Extensions.XSSFHeaderFooter.Center">
            get the text representing the center part of this element
        </member>
        <member name="P:NPOI.XSSF.UserModel.Extensions.XSSFHeaderFooter.Left">
            get the text representing the left part of this element
        </member>
        <member name="P:NPOI.XSSF.UserModel.Extensions.XSSFHeaderFooter.Right">
            get the text representing the right part of this element
        </member>
        <member name="T:NPOI.XSSF.UserModel.Helpers.ColumnHelper">
            Helper class for dealing with the Column Settings on
             a CT_Worksheet (the data part of a sheet).
            Note - within POI, we use 0 based column indexes, but
             the column defInitions in the XML are 1 based!
        </member>
        <member name="M:NPOI.XSSF.UserModel.Helpers.ColumnHelper.GetColumn(System.Int64,System.Boolean)">
            Returns the Column at the given 0 based index
        </member>
        <member name="M:NPOI.XSSF.UserModel.Helpers.ColumnHelper.GetColumn1Based(System.Int64,System.Boolean)">
            Returns the Column at the given 1 based index.
            POI default is 0 based, but the file stores
             as 1 based.
        </member>
        <member name="M:NPOI.XSSF.UserModel.Helpers.ColumnHelper.columnExists(NPOI.OpenXmlFormats.Spreadsheet.CT_Cols,System.Int64)">
            Does the column at the given 0 based index exist
             in the supplied list of column defInitions?
        </member>
        <member name="M:NPOI.XSSF.UserModel.Helpers.ColumnHelper.GetOrCreateColumn1Based(System.Int64,System.Boolean)">
            Return the CT_Col at the given (0 based) column index,
             creating it if required.
        </member>
        <member name="M:NPOI.XSSF.UserModel.Helpers.HeaderFooterHelper.GetParts(System.String)">
            Split into left, center, right
        </member>
        <member name="T:NPOI.XSSF.UserModel.Helpers.XSSFFormulaUtils">
             Utility to update formulas and named ranges when a sheet name was Changed
            
             <AUTHOR> Kozlov
        </member>
        <member name="M:NPOI.XSSF.UserModel.Helpers.XSSFFormulaUtils.UpdateSheetName(System.Int32,System.String)">
             Update sheet name in all formulas and named ranges.
             Called from {@link XSSFWorkbook#SetSheetName(int, String)}
             <p/>
             <p>
             The idea is to parse every formula and render it back to string
             with the updated sheet name. The IFormulaParsingWorkbook passed to the formula Parser
             is constructed from the old workbook (sheet name is not yet updated) and
             the FormulaRenderingWorkbook passed to FormulaRenderer#toFormulaString is a custom implementation that
             returns the new sheet name.
             </p>
            
             @param sheetIndex the 0-based index of the sheet being Changed
             @param name       the new sheet name
        </member>
        <member name="M:NPOI.XSSF.UserModel.Helpers.XSSFFormulaUtils.UpdateFormula(NPOI.XSSF.UserModel.XSSFCell,NPOI.SS.Formula.IFormulaRenderingWorkbook)">
             Parse cell formula and re-assemble it back using the specified FormulaRenderingWorkbook.
            
             @param cell the cell to update
             @param frwb the formula rendering workbbok that returns new sheet name
        </member>
        <member name="M:NPOI.XSSF.UserModel.Helpers.XSSFFormulaUtils.UpdateName(NPOI.SS.UserModel.IName,NPOI.SS.Formula.IFormulaRenderingWorkbook)">
             Parse formula in the named range and re-assemble it  back using the specified FormulaRenderingWorkbook.
            
             @param name the name to update
             @param frwb the formula rendering workbbok that returns new sheet name
        </member>
        <member name="T:NPOI.XSSF.UserModel.Helpers.XSSFRowShifter">
            <AUTHOR> Kozlov
        </member>
        <member name="M:NPOI.XSSF.UserModel.Helpers.XSSFRowShifter.ShiftMerged(System.Int32,System.Int32,System.Int32)">
             Shift merged regions
            
             @param startRow the row to start Shifting
             @param endRow   the row to end Shifting
             @param n        the number of rows to shift
             @return an array of affected cell regions
        </member>
        <member name="M:NPOI.XSSF.UserModel.Helpers.XSSFRowShifter.ContainsCell(NPOI.SS.Util.CellRangeAddress,System.Int32,System.Int32)">
             Check if the  row and column are in the specified cell range
            
             @param cr    the cell range to check in
             @param rowIx the row to check
             @param colIx the column to check
             @return true if the range Contains the cell [rowIx,colIx]
        </member>
        <member name="M:NPOI.XSSF.UserModel.Helpers.XSSFRowShifter.UpdateNamedRanges(NPOI.SS.Formula.FormulaShifter)">
            Updated named ranges
        </member>
        <member name="M:NPOI.XSSF.UserModel.Helpers.XSSFRowShifter.UpdateFormulas(NPOI.SS.Formula.FormulaShifter)">
            Update formulas.
        </member>
        <member name="M:NPOI.XSSF.UserModel.Helpers.XSSFRowShifter.ShiftFormula(NPOI.XSSF.UserModel.XSSFRow,System.String,NPOI.SS.Formula.FormulaShifter)">
             Shift a formula using the supplied FormulaShifter
            
             @param row     the row of the cell this formula belongs to. Used to get a reference to the parent workbook.
             @param formula the formula to shift
             @param Shifter the FormulaShifter object that operates on the Parsed formula tokens
             @return the Shifted formula if the formula was Changed,
                     <code>null</code> if the formula wasn't modified
        </member>
        <member name="T:NPOI.XSSF.UserModel.Helpers.XSSFSingleXmlCell">
                 * 
                 * This class is a wrapper around the CTSingleXmlCell  (Open Office XML Part 4:
                 * chapter 3.5.2.1) 
                 * 
            
                 * 
                 * <AUTHOR> Manicardi
                 *
        </member>
        <member name="M:NPOI.XSSF.UserModel.Helpers.XSSFSingleXmlCell.GetReferencedCell">
            Gets the XSSFCell referenced by the R attribute or Creates a new one if cell doesn't exists
            @return the referenced XSSFCell, null if the cell reference is invalid
        </member>
        <member name="T:NPOI.XSSF.UserModel.Helpers.XSSFXmlColumnPr">
             
             This class is a wrapper around the CT_XmlColumnPr (Open Office XML Part 4:
             chapter 3.5.1.7)
             
            
             <AUTHOR> Manicardi
        </member>
        <member name="M:NPOI.XSSF.UserModel.Helpers.XSSFXmlColumnPr.GetId">
            (see Open Office XML Part 4: chapter 3.5.1.3)
            @return An integer representing the unique identifier of this column. 
        </member>
        <member name="M:NPOI.XSSF.UserModel.Helpers.XSSFXmlColumnPr.GetLocalXPath">
            If the XPath is, for example, /Node1/Node2/Node3 and /Node1/Node2 is the common XPath for the table, the local XPath is /Node3
            	
            @return the local XPath 
        </member>
        <member name="T:NPOI.XSSF.UserModel.XSSFAnchor">
             An anchor is what specifics the position of a shape within a client object
             or within another containing shape.
            
             <AUTHOR> Kozlov
        </member>
        <member name="T:NPOI.XSSF.UserModel.XSSFAutoFilter">
             Represents autofiltering for the specified worksheet.
            
             <AUTHOR> Kozlov
        </member>
        <member name="T:NPOI.XSSF.UserModel.XSSFBorderFormatting">
            <AUTHOR> Kozlov
        </member>
        <member name="T:NPOI.XSSF.UserModel.XSSFCell">
            High level representation of a cell in a row of a spreadsheet.
            <p>
            Cells can be numeric, formula-based or string-based (text).  The cell type
            specifies this.  String cells cannot conatin numbers and numeric cells cannot
            contain strings (at least according to our model).  Client apps should do the
            conversions themselves.  Formula cells have the formula string, as well as
            the formula result, which can be numeric or string.
            </p>
            <p>
            Cells should have their number (0 based) before being Added to a row.  Only
            cells that have values should be Added.
            </p>
        </member>
        <member name="F:NPOI.XSSF.UserModel.XSSFCell._cell">
            the xml bean Containing information about the cell's location, value,
            data type, formatting, and formula
        </member>
        <member name="F:NPOI.XSSF.UserModel.XSSFCell._row">
            the XSSFRow this cell belongs to
        </member>
        <member name="F:NPOI.XSSF.UserModel.XSSFCell._cellNum">
            0-based column index
        </member>
        <member name="F:NPOI.XSSF.UserModel.XSSFCell._sharedStringSource">
            Table of strings shared across this workbook.
            If two cells contain the same string, then the cell value is the same index into SharedStringsTable
        </member>
        <member name="F:NPOI.XSSF.UserModel.XSSFCell._stylesSource">
            Table of cell styles shared across all cells in a workbook.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFCell.#ctor(NPOI.XSSF.UserModel.XSSFRow,NPOI.OpenXmlFormats.Spreadsheet.CT_Cell)">
             Construct a XSSFCell.
            
             @param row the parent row.
             @param cell the xml bean Containing information about the cell.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFCell.GetSharedStringSource">
            @return table of strings shared across this workbook
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFCell.GetStylesSource">
            @return table of cell styles shared across this workbook
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFCell.SetCellValue(System.Boolean)">
             Set a bool value for the cell
            
             @param value the bool value to Set this cell to.  For formulas we'll Set the
                    precalculated value, for bools we'll Set its value. For other types we
                    will change the cell to a bool cell and Set its value.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFCell.SetCellValue(System.Double)">
             Set a numeric value for the cell
            
             @param value  the numeric value to Set this cell to.  For formulas we'll Set the
                    precalculated value, for numerics we'll Set its value. For other types we
                    will change the cell to a numeric cell and Set its value.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFCell.SetCellValue(System.String)">
             Set a string value for the cell.
            
             @param str value to Set the cell to.  For formulas we'll Set the formula
             cached string result, for String cells we'll Set its value. For other types we will
             change the cell to a string cell and Set its value.
             If value is null then we will change the cell to a Blank cell.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFCell.SetCellValue(NPOI.SS.UserModel.IRichTextString)">
             Set a string value for the cell.
            
             @param str  value to Set the cell to.  For formulas we'll Set the 'pre-Evaluated result string,
             for String cells we'll Set its value.  For other types we will
             change the cell to a string cell and Set its value.
             If value is null then we will change the cell to a Blank cell.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFCell.ConvertSharedFormula(System.Int32)">
            <summary>
            Creates a non shared formula from the shared formula counterpart
            </summary>
            <param name="si">Shared Group Index</param>
            <returns>non shared formula created for the given shared formula and this cell</returns>
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFCell.SetCellFormula(System.String)">
             Sets formula for this cell.
             <p>
             Note, this method only Sets the formula string and does not calculate the formula value.
             To Set the precalculated value use {@link #setCellValue(double)} or {@link #setCellValue(String)}
             </p>
            
             @param formula the formula to Set, e.g. <code>"SUM(C4:E4)"</code>.
              If the argument is <code>null</code> then the current formula is Removed.
             @throws NPOI.ss.formula.FormulaParseException if the formula has incorrect syntax or is otherwise invalid
             @throws InvalidOperationException if the operation is not allowed, for example,
              when the cell is a part of a multi-cell array formula
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFCell.GetReference">
            <summary>
            Returns an A1 style reference to the location of this cell
            </summary>
            <returns>A1 style reference to the location of this cell</returns>
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFCell.GetBaseCellType(System.Boolean)">
            <summary>
            Detect cell type based on the "t" attribute of the CT_Cell bean
            </summary>
            <param name="blankCells"></param>
            <returns></returns>
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFCell.SetCellValue(System.DateTime)">
            <summary>
             Set a date value for the cell. Excel treats dates as numeric so you will need to format the cell as a date.
            </summary>
            <param name="value">the date value to Set this cell to.  For formulas we'll set the precalculated value, 
            for numerics we'll Set its value. For other types we will change the cell to a numeric cell and Set its value. </param>
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFCell.SetCellErrorValue(NPOI.SS.UserModel.FormulaError)">
            <summary>
            Set a error value for the cell
            </summary>
            <param name="error">the error value to Set this cell to. 
            For formulas we'll Set the precalculated value , for errors we'll set
            its value. For other types we will change the cell to an error cell and Set its value.
            </param>
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFCell.SetAsActiveCell">
            <summary>
            Sets this cell as the active cell for the worksheet.
            </summary>
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFCell.SetBlank">
            <summary>
            Blanks this cell. Blank cells have no formula or value but may have styling.
            This method erases all the data previously associated with this cell.
            </summary>
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFCell.SetCellNum(System.Int32)">
            <summary>
            Sets column index of this cell
            </summary>
            <param name="num"></param>
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFCell.SetCellType(NPOI.SS.UserModel.CellType)">
            <summary>
            Set the cells type (numeric, formula or string)
            </summary>
            <param name="cellType"></param>
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFCell.ToString">
            <summary>
            Returns a string representation of the cell
            </summary>
            <returns>Formula cells return the formula string, rather than the formula result.
            Dates are displayed in dd-MMM-yyyy format
            Errors are displayed as #ERR&lt;errIdx&gt;
            </returns>
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFCell.GetRawValue">
             Returns the raw, underlying ooxml value for the cell
             <p>
             If the cell Contains a string, then this value is an index into
             the shared string table, pointing to the actual string value. Otherwise,
             the value of the cell is expressed directly in this element. Cells Containing formulas express
             the last calculated result of the formula in this element.
             </p>
            
             @return the raw cell value as Contained in the underlying CT_Cell bean,
                 <code>null</code> for blank cells.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFCell.GetCellTypeName(NPOI.SS.UserModel.CellType)">
            <summary>
            Used to help format error messages
            </summary>
            <param name="cellTypeCode"></param>
            <returns></returns>
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFCell.TypeMismatch(NPOI.SS.UserModel.CellType,NPOI.SS.UserModel.CellType,System.Boolean)">
            Used to help format error messages
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFCell.CheckBounds(System.Int32)">
            @throws RuntimeException if the bounds are exceeded.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFCell.RemoveCellComment">
            <summary>
            Removes the comment for this cell, if there is one.
            </summary>
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFCell.GetCTCell">
             Returns the xml bean containing information about the cell's location (reference), value,
             data type, formatting, and formula
            
             @return the xml bean containing information about this cell
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFCell.ConvertCellValueToBoolean">
             Chooses a new bool value for the cell when its type is changing.<p/>
            
             Usually the caller is calling SetCellType() with the intention of calling
             SetCellValue(bool) straight afterwards.  This method only exists to give
             the cell a somewhat reasonable value until the SetCellValue() call (if at all).
             TODO - perhaps a method like SetCellTypeAndValue(int, Object) should be introduced to avoid this
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFCell.NotifyArrayFormulaChanging(System.String)">
             The purpose of this method is to validate the cell state prior to modification
            
             @see #NotifyArrayFormulaChanging()
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFCell.NotifyArrayFormulaChanging">
            <summary>
            Called when this cell is modified.The purpose of this method is to validate the cell state prior to modification.
            </summary>
            <exception cref="T:System.InvalidOperationException">if modification is not allowed</exception>
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFCell.Sheet">
             Returns the sheet this cell belongs to
            
             @return the sheet this cell belongs to
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFCell.Row">
             Returns the row this cell belongs to
            
             @return the row this cell belongs to
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFCell.BooleanCellValue">
            Get the value of the cell as a bool.
            <p>
            For strings, numbers, and errors, we throw an exception. For blank cells we return a false.
            </p>
            @return the value of the cell as a bool
            @throws InvalidOperationException if the cell type returned by {@link #CellType}
              is not CellType.Boolean, CellType.Blank or CellType.Formula
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFCell.NumericCellValue">
            Get the value of the cell as a number.
            <p>
            For strings we throw an exception. For blank cells we return a 0.
            For formulas or error cells we return the precalculated value;
            </p>
            @return the value of the cell as a number
            @throws InvalidOperationException if the cell type returned by {@link #CellType} is CellType.String
            @exception NumberFormatException if the cell value isn't a parsable <code>double</code>.
            @see DataFormatter for turning this number into a string similar to that which Excel would render this number as.
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFCell.StringCellValue">
            Get the value of the cell as a string
            <p>
            For numeric cells we throw an exception. For blank cells we return an empty string.
            For formulaCells that are not string Formulas, we throw an exception
            </p>
            @return the value of the cell as a string
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFCell.RichStringCellValue">
            Get the value of the cell as a XSSFRichTextString
            <p>
            For numeric cells we throw an exception. For blank cells we return an empty string.
            For formula cells we return the pre-calculated value if a string, otherwise an exception
            </p>
            @return the value of the cell as a XSSFRichTextString
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFCell.CellFormula">
            <summary>
            Return a formula for the cell,  for example, <code>SUM(C4:E4)</code>
            </summary>
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFCell.ColumnIndex">
            <summary>
            Returns zero-based column index of this cell
            </summary>
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFCell.RowIndex">
            <summary>
            Returns zero-based row index of a row in the sheet that contains this cell
            </summary>
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFCell.CellStyle">
            <summary>
            Return the cell's style.
            </summary>
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFCell.CellType">
            <summary>
            Return the cell type.
            </summary>
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFCell.CachedFormulaResultType">
            <summary>
            Only valid for formula cells
            </summary>
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFCell.DateCellValue">
            <summary>
            Get the value of the cell as a date.
            </summary>
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFCell.ErrorCellString">
            <summary>
            Returns the error message, such as #VALUE!
            </summary>
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFCell.ErrorCellValue">
            <summary>
            Get the value of the cell as an error code.
            For strings, numbers, and bools, we throw an exception.
            For blank cells we return a 0.
            </summary>
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFCell.CellComment">
            <summary>
             Returns cell comment associated with this cell
            </summary>
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFCell.Hyperlink">
            <summary>
            Returns hyperlink associated with this cell
            </summary>
        </member>
        <member name="T:NPOI.XSSF.UserModel.XSSFCellStyle">
            
             High level representation of the the possible formatting information for the contents of the cells on a sheet in a
             SpreadsheetML document.
            
             @see NPOI.xssf.usermodel.XSSFWorkbook#CreateCellStyle()
             @see NPOI.xssf.usermodel.XSSFWorkbook#getCellStyleAt(short)
             @see NPOI.xssf.usermodel.XSSFCell#setCellStyle(NPOI.ss.usermodel.CellStyle)
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFCellStyle.#ctor(System.Int32,System.Int32,NPOI.XSSF.Model.StylesTable,NPOI.XSSF.Model.ThemesTable)">
            Creates a Cell Style from the supplied parts
            @param cellXfId The main XF for the cell. Must be a valid 0-based index into the XF table
            @param cellStyleXfId Optional, style xf. A value of <code>-1</code> means no xf.
            @param stylesSource Styles Source to work off
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFCellStyle.GetCoreXf">
            Used so that StylesSource can figure out our location
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFCellStyle.GetStyleXf">
            Used so that StylesSource can figure out our location
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFCellStyle.#ctor(NPOI.XSSF.Model.StylesTable)">
            <summary>
            Creates an empty Cell Style
            </summary>
            <param name="stylesSource"></param>
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFCellStyle.VerifyBelongsToStylesSource(NPOI.XSSF.Model.StylesTable)">
            Verifies that this style belongs to the supplied Workbook
             Styles Source.
            Will throw an exception if it belongs to a different one.
            This is normally called when trying to assign a style to a
             cell, to ensure the cell and the style are from the same
             workbook (if they're not, it won't work)
            @throws ArgumentException if there's a workbook mis-match
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFCellStyle.CloneStyleFrom(NPOI.SS.UserModel.ICellStyle)">
             Clones all the style information from another
              XSSFCellStyle, onto this one. This
              XSSFCellStyle will then have all the same
              properties as the source, but the two may
              be edited independently.
             Any stylings on this XSSFCellStyle will be lost!
            
             The source XSSFCellStyle could be from another
              XSSFWorkbook if you like. This allows you to
              copy styles from one XSSFWorkbook to another.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFCellStyle.GetAlignmentEnum">
            <summary>
            Get the type of horizontal alignment for the cell
            </summary>
            <returns>the type of alignment</returns>
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFCellStyle.GetDataFormatString">
             Get the contents of the format string, by looking up
             the StylesSource
            
             @return the number format string
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFCellStyle.GetFont">
            Gets the font for this style
            @return Font - font
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFCellStyle.GetVerticalAlignmentEnum">
            <summary>
            Get the type of vertical alignment for the cell
            </summary>
            <returns></returns>
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFCellStyle.SetBottomBorderColor(NPOI.XSSF.UserModel.XSSFColor)">
             Set the color to use for the bottom border
            
             @param color the color to use, null means no color
        </member>
        <!-- 对于成员“M:NPOI.XSSF.UserModel.XSSFCellStyle.SetFillBackgroundColor(NPOI.XSSF.UserModel.XSSFColor)”忽略有格式错误的 XML 注释 -->
        <member name="M:NPOI.XSSF.UserModel.XSSFCellStyle.SetFillForegroundColor(NPOI.XSSF.UserModel.XSSFColor)">
                    * Set the foreground fill color represented as a {@link XSSFColor} value.
                     * <br/>
                    * <i>Note: Ensure Foreground color is Set prior to background color.</i>
                    * @param color the color to use
                    * @see #setFillBackgroundColor(NPOI.xssf.usermodel.XSSFColor) )
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFCellStyle.GetCTFill">
            Get a <b>copy</b> of the currently used CT_Fill, if none is used, return a new instance.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFCellStyle.GetCTBorder">
            Get a <b>copy</b> of the currently used CT_Border, if none is used, return a new instance.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFCellStyle.SetFont(NPOI.SS.UserModel.IFont)">
             Set the font for this style
            
             @param font  a font object Created or retreived from the XSSFWorkbook object
             @see NPOI.xssf.usermodel.XSSFWorkbook#CreateFont()
             @see NPOI.xssf.usermodel.XSSFWorkbook#getFontAt(short)
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFCellStyle.SetLeftBorderColor(NPOI.XSSF.UserModel.XSSFColor)">
             Set the color to use for the left border as a {@link XSSFColor} value
            
             @param color the color to use
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFCellStyle.SetRightBorderColor(NPOI.XSSF.UserModel.XSSFColor)">
             Set the color to use for the right border as a {@link XSSFColor} value
            
             @param color the color to use
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFCellStyle.SetTopBorderColor(NPOI.XSSF.UserModel.XSSFColor)">
             Set the color to use for the top border as a {@link XSSFColor} value
            
             @param color the color to use
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFCellStyle.SetVerticalAlignment(System.Int16)">
             Set the type of vertical alignment for the cell
            
             @param align - align the type of alignment
             @see NPOI.ss.usermodel.CellStyle#VERTICAL_TOP
             @see NPOI.ss.usermodel.CellStyle#VERTICAL_CENTER
             @see NPOI.ss.usermodel.CellStyle#VERTICAL_BOTTOM
             @see NPOI.ss.usermodel.CellStyle#VERTICAL_JUSTIFY
             @see NPOI.ss.usermodel.VerticalAlignment
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFCellStyle.GetBorderColor(NPOI.XSSF.UserModel.Extensions.BorderSide)">
             Gets border color
            
             @param side the border side
             @return the used color
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFCellStyle.SetBorderColor(NPOI.XSSF.UserModel.Extensions.BorderSide,NPOI.XSSF.UserModel.XSSFColor)">
             Set the color to use for the selected border
            
             @param side - where to apply the color defInition
             @param color - the color to use
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFCellStyle.GetCellAlignment">
            Get the cellAlignment object to use for manage alignment
            @return XSSFCellAlignment - cell alignment
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFCellStyle.GetCTCellAlignment">
             Return the CT_CellAlignment instance for alignment
            
             @return CT_CellAlignment
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFCellStyle.GetHashCode">
             Returns a hash code value for the object. The hash is derived from the underlying CT_Xf bean.
            
             @return the hash code value for this style
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFCellStyle.Equals(System.Object)">
             Checks is the supplied style is equal to this style
            
             @param o the style to check
             @return true if the supplied style is equal to this style
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFCellStyle.Clone">
             Make a copy of this style. The underlying CT_Xf bean is Cloned,
             the references to Fills and borders remain.
            
             @return a copy of this style
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFCellStyle.BorderRight">
            <summary>
            Get the type of border to use for the right border of the cell
            </summary>
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFCellStyle.BottomBorderColor">
            Get the color to use for the bottom border
            Color is optional. When missing, IndexedColors.Automatic is implied.
            @return the index of the color defInition, default value is {@link NPOI.ss.usermodel.IndexedColors#AUTOMATIC}
            @see NPOI.ss.usermodel.IndexedColors
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFCellStyle.BottomBorderXSSFColor">
             Get the color to use for the bottom border as a {@link XSSFColor}
            
             @return the used color or <code>null</code> if not Set
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFCellStyle.DataFormat">
             Get the index of the number format (numFmt) record used by this cell format.
            
             @return the index of the number format
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFCellStyle.FillBackgroundColor">
            <summary>
            Get the background fill color.
            Note - many cells are actually filled with a foreground fill, not a background fill
            </summary>
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFCellStyle.FillBackgroundColorColor">
            Get the background fill color.
            <p>
            Note - many cells are actually Filled with a foreground
             Fill, not a background fill - see {@link #getFillForegroundColor()}
            </p>
            @see NPOI.xssf.usermodel.XSSFColor#getRgb()
            @return XSSFColor - fill color or <code>null</code> if not Set
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFCellStyle.FillForegroundColor">
            Get the foreground fill color.
            <p>
            Many cells are Filled with this, instead of a
             background color ({@link #getFillBackgroundColor()})
            </p>
            @see IndexedColors
            @return fill color, default value is {@link NPOI.ss.usermodel.IndexedColors#AUTOMATIC}
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFCellStyle.FillForegroundColorColor">
            <summary>
            Get the foreground fill color.
            </summary>
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFCellStyle.FillForegroundXSSFColor">
            <summary>
            Get the foreground fill color.
            </summary>
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFCellStyle.FontIndex">
             Gets the index of the font for this style
            
             @return short - font index
             @see NPOI.xssf.usermodel.XSSFWorkbook#getFontAt(short)
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFCellStyle.IsHidden">
             Get whether the cell's using this style are to be hidden
            
             @return bool -  whether the cell using this style is hidden
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFCellStyle.Indention">
             Get the number of spaces to indent the text in the cell
            
             @return indent - number of spaces
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFCellStyle.Index">
             Get the index within the StylesTable (sequence within the collection of CT_Xf elements)
            
             @return unique index number of the underlying record this style represents
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFCellStyle.LeftBorderColor">
             Get the color to use for the left border
            
             @return the index of the color defInition, default value is {@link NPOI.ss.usermodel.IndexedColors#BLACK}
             @see NPOI.ss.usermodel.IndexedColors
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFCellStyle.LeftBorderXSSFColor">
             Get the color to use for the left border
            
             @return the index of the color defInition or <code>null</code> if not Set
             @see NPOI.ss.usermodel.IndexedColors
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFCellStyle.IsLocked">
            <summary>
            Get whether the cell's using this style are locked
            </summary>
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFCellStyle.RightBorderColor">
            <summary>
            Get the color to use for the right border
            </summary>
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFCellStyle.RightBorderXSSFColor">
            <summary>
            Get the color to use for the right border
            </summary>
            <returns></returns>
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFCellStyle.Rotation">
            <summary>
            Get the degree of rotation (between 0 and 180 degrees) for the text in the cell
            </summary>
            <example>
            Expressed in degrees. Values range from 0 to 180. The first letter of
            the text is considered the center-point of the arc.
            For 0 - 90, the value represents degrees above horizon. For 91-180 the degrees below the horizon is calculated as:
            <code>[degrees below horizon] = 90 - textRotation.</code>
            </example>
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFCellStyle.TopBorderColor">
             Get the color to use for the top border
            
             @return the index of the color defInition, default value is {@link NPOI.ss.usermodel.IndexedColors#BLACK}
             @see NPOI.ss.usermodel.IndexedColors
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFCellStyle.TopBorderXSSFColor">
            <summary>
            Get the color to use for the top border
            </summary>
            <returns></returns>
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFCellStyle.VerticalAlignment">
            <summary>
            Get the type of vertical alignment for the cell
            </summary>
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFCellStyle.WrapText">
            <summary>
            Whether the text in a cell should be line-wrapped within the cell.
            </summary>
        </member>
        <member name="T:NPOI.XSSF.UserModel.XSSFChart">
            Represents a SpreadsheetML Chart
            <AUTHOR> Burch
            <AUTHOR> Kashitsyn
        </member>
        <member name="F:NPOI.XSSF.UserModel.XSSFChart.frame">
            Parent graphic frame.
        </member>
        <member name="F:NPOI.XSSF.UserModel.XSSFChart.chartSpaceDocument">
            Root element of the SpreadsheetML Chart part
        </member>
        <member name="F:NPOI.XSSF.UserModel.XSSFChart.chart">
            The Chart within that
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFChart.#ctor">
            Create a new SpreadsheetML chart
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFChart.#ctor(NPOI.OpenXml4Net.OPC.PackagePart,NPOI.OpenXml4Net.OPC.PackageRelationship)">
             Construct a SpreadsheetML chart from a namespace part.
            
             @param part the namespace part holding the chart data,
             the content type must be <code>application/vnd.Openxmlformats-officedocument.Drawingml.chart+xml</code>
             @param rel  the namespace relationship holding this chart,
             the relationship type must be http://schemas.Openxmlformats.org/officeDocument/2006/relationships/chart
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFChart.CreateChart">
             Construct a new CTChartSpace bean.
             By default, it's just an empty placeholder for chart objects.
            
             @return a new CTChartSpace bean
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFChart.GetCTChartSpace">
             Return the underlying CTChartSpace bean, the root element of the SpreadsheetML Chart part.
            
             @return the underlying CTChartSpace bean
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFChart.GetCTChart">
             Return the underlying CTChart bean, within the Chart Space
            
             @return the underlying CTChart bean
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFChart.GetGraphicFrame">
            Returns the parent graphic frame.
            @return the graphic frame this chart belongs to
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFChart.SetGraphicFrame(NPOI.XSSF.UserModel.XSSFGraphicFrame)">
            Sets the parent graphic frame.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFChart.IsPlotOnlyVisibleCells">
            @return true if only visible cells will be present on the chart,
                    false otherwise
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFChart.SetPlotOnlyVisibleCells(System.Boolean)">
            @param plotVisOnly a flag specifying if only visible cells should be
                   present on the chart
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFChart.Title">
            Returns the title, or null if none is Set
        </member>
        <member name="T:NPOI.XSSF.UserModel.XSSFChartSheet">
             High level representation of Sheet Parts that are of type 'chartsheet'.
             <p>
              Chart sheet is a special kind of Sheet that Contains only chart and no data.
             </p>
            
             <AUTHOR> Kozlov
        </member>
        <member name="T:NPOI.XSSF.UserModel.XSSFSheet">
             High level representation of a SpreadsheetML worksheet.
            
             <p>
             Sheets are the central structures within a workbook, and are where a user does most of his spreadsheet work.
             The most common type of sheet is the worksheet, which is represented as a grid of cells. Worksheet cells can
             contain text, numbers, dates, and formulas. Cells can also be formatted.
             </p>
        </member>
        <member name="F:NPOI.XSSF.UserModel.XSSFSheet.sharedFormulas">
            cache of master shared formulas in this sheet.
            Master shared formula is the first formula in a group of shared formulas is saved in the f element.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.#ctor">
             Creates new XSSFSheet   - called by XSSFWorkbook to create a sheet from scratch.
            
             @see NPOI.XSSF.usermodel.XSSFWorkbook#CreateSheet()
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.#ctor(NPOI.OpenXml4Net.OPC.PackagePart,NPOI.OpenXml4Net.OPC.PackageRelationship)">
             Creates an XSSFSheet representing the given namespace part and relationship.
             Should only be called by XSSFWorkbook when Reading in an exisiting file.
            
             @param part - The namespace part that holds xml data represenring this sheet.
             @param rel - the relationship of the given namespace part in the underlying OPC namespace
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.OnDocumentRead">
            Initialize worksheet data when Reading in an exisiting file.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.OnDocumentCreate">
            Initialize worksheet data when creating a new sheet.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.InitHyperlinks">
            Read hyperlink relations, link them with CT_Hyperlink beans in this worksheet
            and Initialize the internal array of XSSFHyperlink objects
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.NewSheet">
             Create a new CT_Worksheet instance with all values set to defaults
            
             @return a new instance
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.GetCTWorksheet">
             Provide access to the CT_Worksheet bean holding this sheet's data
            
             @return the CT_Worksheet bean holding this sheet's data
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.AddMergedRegion(NPOI.SS.Util.CellRangeAddress)">
             Adds a merged region of cells (hence those cells form one).
            
             @param region (rowfrom/colfrom-rowto/colto) to merge
             @return index of this region
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.AutoSizeColumn(System.Int32)">
             Adjusts the column width to fit the contents.
            
             This process can be relatively slow on large sheets, so this should
              normally only be called once per column, at the end of your
              Processing.
            
             @param column the column index
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.AutoSizeColumn(System.Int32,System.Boolean)">
             Adjusts the column width to fit the contents.
             <p>
             This process can be relatively slow on large sheets, so this should
              normally only be called once per column, at the end of your
              Processing.
             </p>
             You can specify whether the content of merged cells should be considered or ignored.
              Default is to ignore merged cells.
            
             @param column the column index
             @param useMergedCells whether to use the contents of merged cells when calculating the width of the column
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.CreateDrawingPatriarch">
             Create a new SpreadsheetML drawing. If this sheet already Contains a drawing - return that.
            
             @return a SpreadsheetML drawing
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.GetVMLDrawing(System.Boolean)">
             Get VML drawing for this sheet (aka 'legacy' drawig)
            
             @param autoCreate if true, then a new VML drawing part is Created
            
             @return the VML drawing of <code>null</code> if the drawing was not found and autoCreate=false
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.CreateFreezePane(System.Int32,System.Int32)">
            Creates a split (freezepane). Any existing freezepane or split pane is overwritten.
            @param colSplit      Horizonatal position of split.
            @param rowSplit      Vertical position of split.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.CreateFreezePane(System.Int32,System.Int32,System.Int32,System.Int32)">
             Creates a split (freezepane). Any existing freezepane or split pane is overwritten.
            
             <p>
                 If both colSplit and rowSplit are zero then the existing freeze pane is Removed
             </p>
            
             @param colSplit      Horizonatal position of split.
             @param rowSplit      Vertical position of split.
             @param leftmostColumn   Left column visible in right pane.
             @param topRow        Top row visible in bottom pane
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.CreateComment">
             Creates a new comment for this sheet. You still
              need to assign it to a cell though
            
             @deprecated since Nov 2009 this method is not compatible with the common SS interfaces,
             use {@link NPOI.XSSF.usermodel.XSSFDrawing#CreateCellComment
              (NPOI.SS.usermodel.ClientAnchor)} instead
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.CreateRow(System.Int32)">
             Create a new row within the sheet and return the high level representation
            
             @param rownum  row number
             @return High level {@link XSSFRow} object representing a row in the sheet
             @see #RemoveRow(NPOI.SS.usermodel.Row)
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.CreateSplitPane(System.Int32,System.Int32,System.Int32,System.Int32,NPOI.SS.UserModel.PanePosition)">
            Creates a split pane. Any existing freezepane or split pane is overwritten.
            @param xSplitPos      Horizonatal position of split (in 1/20th of a point).
            @param ySplitPos      Vertical position of split (in 1/20th of a point).
            @param topRow        Top row visible in bottom pane
            @param leftmostColumn   Left column visible in right pane.
            @param activePane    Active pane.  One of: PANE_LOWER_RIGHT,
                                 PANE_UPPER_RIGHT, PANE_LOWER_LEFT, PANE_UPPER_LEFT
            @see NPOI.SS.usermodel.Sheet#PANE_LOWER_LEFT
            @see NPOI.SS.usermodel.Sheet#PANE_LOWER_RIGHT
            @see NPOI.SS.usermodel.Sheet#PANE_UPPER_LEFT
            @see NPOI.SS.usermodel.Sheet#PANE_UPPER_RIGHT
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.GetColumnWidth(System.Int32)">
             Get the actual column width (in units of 1/256th of a character width )
            
             <p>
             Note, the returned  value is always gerater that {@link #GetDefaultColumnWidth()} because the latter does not include margins.
             Actual column width measured as the number of characters of the maximum digit width of the
             numbers 0, 1, 2, ..., 9 as rendered in the normal style's font. There are 4 pixels of margin
             pAdding (two on each side), plus 1 pixel pAdding for the gridlines.
             </p>
            
             @param columnIndex - the column to set (0-based)
             @return width - the width in units of 1/256th of a character width
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.GetColumnStyle(System.Int32)">
            Returns the CellStyle that applies to the given
             (0 based) column, or null if no style has been
             set for that column
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.GetMargin(NPOI.SS.UserModel.MarginType)">
             Gets the size of the margin in inches.
            
             @param margin which margin to get
             @return the size of the margin
             @see Sheet#LeftMargin
             @see Sheet#RightMargin
             @see Sheet#TopMargin
             @see Sheet#BottomMargin
             @see Sheet#HeaderMargin
             @see Sheet#FooterMargin
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.SetMargin(NPOI.SS.UserModel.MarginType,System.Double)">
             Sets the size of the margin in inches.
            
             @param margin which margin to get
             @param size the size of the margin
             @see Sheet#LeftMargin
             @see Sheet#RightMargin
             @see Sheet#TopMargin
             @see Sheet#BottomMargin
             @see Sheet#HeaderMargin
             @see Sheet#FooterMargin
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.GetMergedRegion(System.Int32)">
            @return the merged region at the specified index
            @throws InvalidOperationException if this worksheet does not contain merged regions
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.ProtectSheet(System.String)">
            Enables sheet protection and Sets the password for the sheet.
            Also Sets some attributes on the {@link CT_SheetProtection} that correspond to
            the default values used by Excel
            
            @param password to set for protection. Pass <code>null</code> to remove protection
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.StringToExcelPassword(System.String)">
            Converts a String to a {@link STUnsignedshortHex} value that Contains the {@link PasswordRecord#hashPassword(String)}
            value in hexadecimal format
             
            @param password the password string you wish convert to an {@link STUnsignedshortHex}
            @return {@link STUnsignedshortHex} that Contains Excel hashed password in Hex format
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.GetRow(System.Int32)">
             Returns the logical row ( 0-based).  If you ask for a row that is not
             defined you get a null.  This is to say row 4 represents the fifth row on a sheet.
            
             @param rownum  row to get
             @return <code>XSSFRow</code> representing the rownumber or <code>null</code> if its not defined on the sheet
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.ensureOutlinePr">
            Ensure CT_Worksheet.CT_SheetPr.CT_OutlinePr
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.GroupColumn(System.Int32,System.Int32)">
            Group between (0 based) columns
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.GroupRow(System.Int32,System.Int32)">
             Tie a range of cell toGether so that they can be collapsed or expanded
            
             @param fromRow   start row (0-based)
             @param toRow     end row (0-based)
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.IsColumnBroken(System.Int32)">
            Determines if there is a page break at the indicated column
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.IsColumnHidden(System.Int32)">
             Get the hidden state for a given column.
            
             @param columnIndex - the column to set (0-based)
             @return hidden - <code>false</code> if the column is visible
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.IsRowBroken(System.Int32)">
             Tests if there is a page break at the indicated row
            
             @param row index of the row to test
             @return <code>true</code> if there is a page break at the indicated row
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.SetRowBreak(System.Int32)">
             Sets a page break at the indicated row
             Breaks occur above the specified row and left of the specified column inclusive.
            
             For example, <code>sheet.SetColumnBreak(2);</code> breaks the sheet into two parts
             with columns A,B,C in the first and D,E,... in the second. Simuilar, <code>sheet.SetRowBreak(2);</code>
             breaks the sheet into two parts with first three rows (rownum=1...3) in the first part
             and rows starting with rownum=4 in the second.
            
             @param row the row to break, inclusive
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.RemoveColumnBreak(System.Int32)">
            Removes a page break at the indicated column
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.RemoveMergedRegion(System.Int32)">
             Removes a merged region of cells (hence letting them free)
            
             @param index of the region to unmerge
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.RemoveRow(NPOI.SS.UserModel.IRow)">
             Remove a row from this sheet.  All cells Contained in the row are Removed as well
            
             @param row  the row to Remove.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.RemoveRowBreak(System.Int32)">
            Removes the page break at the indicated row
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.SetColumnBreak(System.Int32)">
             Sets a page break at the indicated column.
             Breaks occur above the specified row and left of the specified column inclusive.
            
             For example, <code>sheet.SetColumnBreak(2);</code> breaks the sheet into two parts
             with columns A,B,C in the first and D,E,... in the second. Simuilar, <code>sheet.SetRowBreak(2);</code>
             breaks the sheet into two parts with first three rows (rownum=1...3) in the first part
             and rows starting with rownum=4 in the second.
            
             @param column the column to break, inclusive
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.SetGroupHidden(System.Int32,System.Int32,System.Boolean)">
             Sets all adjacent columns of the same outline level to the specified
             hidden status.
            
             @param pIdx
                            the col info index of the start of the outline group
             @return the column index of the last column in the outline group
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.IsColumnGroupCollapsed(System.Int32)">
             'Collapsed' state is stored in a single column col info record
             immediately after the outline group
            
             @param idx
             @return a bool represented if the column is collapsed
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.SetColumnHidden(System.Int32,System.Boolean)">
             Get the visibility state for a given column.
            
             @param columnIndex - the column to get (0-based)
             @param hidden - the visiblity state of the column
        </member>
        <!-- 对于成员“M:NPOI.XSSF.UserModel.XSSFSheet.SetColumnWidth(System.Int32,System.Int32)”忽略有格式错误的 XML 注释 -->
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.SetRowGroupCollapsed(System.Int32,System.Boolean)">
             group the row It is possible for collapsed to be false and yet still have
             the rows in question hidden. This can be achieved by having a lower
             outline level collapsed, thus hiding all the child rows. Note that in
             this case, if the lowest level were expanded, the middle level would
             remain collapsed.
            
             @param rowIndex -
                            the row involved, 0 based
             @param collapse -
                            bool value for collapse
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.CollapseRow(System.Int32)">
            @param rowIndex the zero based row index to collapse
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.FindStartOfRowOutlineGroup(System.Int32)">
            @param rowIndex the zero based row index to find from
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.ExpandRow(System.Int32)">
            @param rowNumber the zero based row index to expand
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.FindEndOfRowOutlineGroup(System.Int32)">
            @param row the zero based row index to find from
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.IsRowGroupHiddenByParent(System.Int32)">
            @param row the zero based row index to find from
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.IsRowGroupCollapsed(System.Int32)">
            @param row the zero based row index to find from
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.SetZoom(System.Int32,System.Int32)">
             Sets the zoom magnication for the sheet.  The zoom is expressed as a
             fraction.  For example to express a zoom of 75% use 3 for the numerator
             and 4 for the denominator.
            
             @param numerator     The numerator for the zoom magnification.
             @param denominator   The denominator for the zoom magnification.
             @see #SetZoom(int)
        </member>
        <!-- 对于成员“M:NPOI.XSSF.UserModel.XSSFSheet.SetZoom(System.Int32)”忽略有格式错误的 XML 注释 -->
        <!-- 对于成员“M:NPOI.XSSF.UserModel.XSSFSheet.ShiftRows(System.Int32,System.Int32,System.Int32)”忽略有格式错误的 XML 注释 -->
        <!-- 对于成员“M:NPOI.XSSF.UserModel.XSSFSheet.ShiftRows(System.Int32,System.Int32,System.Int32,System.Boolean,System.Boolean)”忽略有格式错误的 XML 注释 -->
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.ShowInPane(System.Int16,System.Int16)">
             Location of the top left visible cell Location of the top left visible cell in the bottom right
             pane (when in Left-to-Right mode).
            
             @param toprow the top row to show in desktop window pane
             @param leftcol the left column to show in desktop window pane
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.UngroupRow(System.Int32,System.Int32)">
             Ungroup a range of rows that were previously groupped
            
             @param fromRow   start row (0-based)
             @param toRow     end row (0-based)
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.SetCellComment(System.String,NPOI.XSSF.UserModel.XSSFComment)">
             Assign a cell comment to a cell region in this worksheet
            
             @param cellRef cell region
             @param comment the comment to assign
             @deprecated since Nov 2009 use {@link XSSFCell#SetCellComment(NPOI.SS.usermodel.Comment)} instead
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.AddHyperlink(NPOI.XSSF.UserModel.XSSFHyperlink)">
             Register a hyperlink in the collection of hyperlinks on this sheet
            
             @param hyperlink the link to add
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.GetDefaultSheetView">
            Return the default sheet view. This is the last one if the sheet's views, according to sec. ********
            of the OOXML spec: "A single sheet view defInition. When more than 1 sheet view is defined in the file,
            it means that when opening the workbook, each sheet view corresponds to a separate window within the
            spreadsheet application, where each window is Showing the particular sheet. Containing the same
            workbookViewId value, the last sheetView defInition is loaded, and the others are discarded.
            When multiple windows are viewing the same sheet, multiple sheetView elements (with corresponding
            workbookView entries) are saved."
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.GetCommentsTable(System.Boolean)">
             Returns the sheet's comments object if there is one,
              or null if not
            
             @param create create a new comments table if it does not exist
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.GetSharedFormula(System.Int32)">
             Return a master shared formula by index
            
             @param sid shared group index
             @return a CT_CellFormula bean holding shared formula or <code>null</code> if not found
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.EnableLocking">
            Enable sheet protection
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.DisableLocking">
            Disable sheet protection
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.LockAutoFilter">
            Enable Autofilters locking.
            This does not modify sheet protection status.
            To enforce this locking, call {@link #enableLocking()}
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.LockDeleteColumns">
            Enable Deleting columns locking.
            This does not modify sheet protection status.
            To enforce this locking, call {@link #enableLocking()}
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.LockDeleteRows">
            Enable Deleting rows locking.
            This does not modify sheet protection status.
            To enforce this locking, call {@link #enableLocking()}
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.LockFormatCells">
            Enable Formatting cells locking.
            This does not modify sheet protection status.
            To enforce this locking, call {@link #enableLocking()}
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.LockFormatColumns">
            Enable Formatting columns locking.
            This does not modify sheet protection status.
            To enforce this locking, call {@link #enableLocking()}
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.LockFormatRows">
            Enable Formatting rows locking.
            This does not modify sheet protection status.
            To enforce this locking, call {@link #enableLocking()}
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.LockInsertColumns">
            Enable Inserting columns locking.
            This does not modify sheet protection status.
            To enforce this locking, call {@link #enableLocking()}
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.LockInsertHyperlinks">
            Enable Inserting hyperlinks locking.
            This does not modify sheet protection status.
            To enforce this locking, call {@link #enableLocking()}
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.LockInsertRows">
            Enable Inserting rows locking.
            This does not modify sheet protection status.
            To enforce this locking, call {@link #enableLocking()}
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.LockPivotTables">
            Enable Pivot Tables locking.
            This does not modify sheet protection status.
            To enforce this locking, call {@link #enableLocking()}
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.LockSort">
            Enable Sort locking.
            This does not modify sheet protection status.
            To enforce this locking, call {@link #enableLocking()}
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.LockObjects">
            Enable Objects locking.
            This does not modify sheet protection status.
            To enforce this locking, call {@link #enableLocking()}
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.LockScenarios">
            Enable Scenarios locking.
            This does not modify sheet protection status.
            To enforce this locking, call {@link #enableLocking()}
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.LockSelectLockedCells">
            Enable Selection of locked cells locking.
            This does not modify sheet protection status.
            To enforce this locking, call {@link #enableLocking()}
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.LockSelectUnlockedCells">
            Enable Selection of unlocked cells locking.
            This does not modify sheet protection status.
            To enforce this locking, call {@link #enableLocking()}
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.GetCellRange(NPOI.SS.Util.CellRangeAddress)">
            Also Creates cells if they don't exist
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.CreateTable">
            Creates a new Table, and associates it with this Sheet
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.GetTables">
            Returns any tables associated with this Sheet
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheet.SetTabColor(System.Int32)">
             Set background color of the sheet tab
            
             @param colorIndex  the indexed color to set, must be a constant from {@link IndexedColors}
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFSheet.Workbook">
             Returns the parent XSSFWorkbook
            
             @return the parent XSSFWorkbook
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFSheet.SheetName">
             Returns the name of this sheet
            
             @return the name of this sheet
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFSheet.ColumnBreaks">
             Vertical page break information used for print layout view, page layout view, drawing print breaks
             in normal view, and for printing the worksheet.
            
             @return column indexes of all the vertical page breaks, never <code>null</code>
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFSheet.DefaultColumnWidth">
            Get the default column width for the sheet (if the columns do not define their own width) in
            characters.
            <p>
            Note, this value is different from {@link #GetColumnWidth(int)}. The latter is always greater and includes
            4 pixels of margin pAdding (two on each side), plus 1 pixel pAdding for the gridlines.
            </p>
            @return column width, default value is 8
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFSheet.DefaultRowHeight">
             Get the default row height for the sheet (if the rows do not define their own height) in
             twips (1/20 of  a point)
            
             @return  default row height
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFSheet.DefaultRowHeightInPoints">
             Get the default row height for the sheet measued in point size (if the rows do not define their own height).
            
             @return  default row height in points
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFSheet.RightToLeft">
             Whether the text is displayed in right-to-left mode in the window
            
             @return whether the text is displayed in right-to-left mode in the window
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFSheet.DisplayGuts">
             Get whether to display the guts or not,
             default value is true
            
             @return bool - guts or no guts
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFSheet.DisplayZeros">
             Gets the flag indicating whether the window should show 0 (zero) in cells Containing zero value.
             When false, cells with zero value appear blank instead of Showing the number zero.
            
             @return whether all zero values on the worksheet are displayed
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFSheet.FirstRowNum">
             Gets the first row on the sheet
            
             @return the number of the first logical row on the sheet, zero based
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFSheet.FitToPage">
             Flag indicating whether the Fit to Page print option is enabled.
            
             @return <code>true</code>
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFSheet.Footer">
            Returns the default footer for the sheet,
             creating one as needed.
            You may also want to look at
             {@link #GetFirstFooter()},
             {@link #GetOddFooter()} and
             {@link #GetEvenFooter()}
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFSheet.Header">
            Returns the default header for the sheet,
             creating one as needed.
            You may also want to look at
             {@link #GetFirstHeader()},
             {@link #GetOddHeader()} and
             {@link #GetEvenHeader()}
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFSheet.OddFooter">
            Returns the odd footer. Used on all pages unless
             other footers also present, when used on only
             odd pages.
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFSheet.EvenFooter">
            Returns the even footer. Not there by default, but
             when Set, used on even pages.
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFSheet.FirstFooter">
            Returns the first page footer. Not there by
             default, but when Set, used on the first page.
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFSheet.OddHeader">
            Returns the odd header. Used on all pages unless
             other headers also present, when used on only
             odd pages.
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFSheet.EvenHeader">
            Returns the even header. Not there by default, but
             when Set, used on even pages.
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFSheet.FirstHeader">
            Returns the first page header. Not there by
             default, but when Set, used on the first page.
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFSheet.HorizontallyCenter">
            Determine whether printed output for this sheet will be horizontally centered.
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFSheet.NumMergedRegions">
             Returns the number of merged regions defined in this worksheet
            
             @return number of merged regions in this worksheet
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFSheet.PaneInformation">
             Returns the information regarding the currently configured pane (split or freeze).
            
             @return null if no pane configured, or the pane information.
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFSheet.PhysicalNumberOfRows">
             Returns the number of phsyically defined rows (NOT the number of rows in the sheet)
            
             @return the number of phsyically defined rows
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFSheet.PrintSetup">
             Gets the print Setup object.
            
             @return The user model for the print Setup object.
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFSheet.Protect">
             Answer whether protection is enabled or disabled
            
             @return true => protection enabled; false => protection disabled
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFSheet.RowBreaks">
             Horizontal page break information used for print layout view, page layout view, drawing print breaks in normal
              view, and for printing the worksheet.
            
             @return row indexes of all the horizontal page breaks, never <code>null</code>
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFSheet.RowSumsBelow">
             Flag indicating whether summary rows appear below detail in an outline, when Applying an outline.
            
             <p>
             When true a summary row is inserted below the detailed data being summarized and a
             new outline level is established on that row.
             </p>
             <p>
             When false a summary row is inserted above the detailed data being summarized and a new outline level
             is established on that row.
             </p>
             @return <code>true</code> if row summaries appear below detail in the outline
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFSheet.RowSumsRight">
             Flag indicating whether summary columns appear to the right of detail in an outline, when Applying an outline.
            
             <p>
             When true a summary column is inserted to the right of the detailed data being summarized
             and a new outline level is established on that column.
             </p>
             <p>
             When false a summary column is inserted to the left of the detailed data being
             summarized and a new outline level is established on that column.
             </p>
             @return <code>true</code> if col summaries appear right of the detail in the outline
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFSheet.ScenarioProtect">
            <summary>
            A flag indicating whether scenarios are locked when the sheet is protected.
            </summary>
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFSheet.TopRow">
            <summary>
            The top row in the visible view when the sheet is first viewed after opening it in a viewer
            </summary>
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFSheet.VerticallyCenter">
             Determine whether printed output for this sheet will be vertically centered.
            
             @return whether printed output for this sheet will be vertically centered.
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFSheet.DisplayFormulas">
             Gets the flag indicating whether this sheet should display formulas.
            
             @return <code>true</code> if this sheet should display formulas.
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFSheet.DisplayGridlines">
             Gets the flag indicating whether this sheet displays the lines
             between rows and columns to make editing and Reading easier.
            
             @return <code>true</code> if this sheet displays gridlines.
             @see #isPrintGridlines() to check if printing of gridlines is turned on or off
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFSheet.DisplayRowColHeadings">
             Gets the flag indicating whether this sheet should display row and column headings.
             <p>
             Row heading are the row numbers to the side of the sheet
             </p>
             <p>
             Column heading are the letters or numbers that appear above the columns of the sheet
             </p>
            
             @return <code>true</code> if this sheet should display row and column headings.
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFSheet.IsPrintGridlines">
             Returns whether gridlines are printed.
            
             @return whether gridlines are printed
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFSheet.ForceFormulaRecalculation">
            Whether Excel will be asked to recalculate all formulas when the
             workbook is opened.  
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFSheet.Autobreaks">
             Flag indicating whether the sheet displays Automatic Page Breaks.
            
             @return <code>true</code> if the sheet displays Automatic Page Breaks.
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFSheet.IsSelected">
             Returns a flag indicating whether this sheet is selected.
             <p>
             When only 1 sheet is selected and active, this value should be in synch with the activeTab value.
             In case of a conflict, the Start Part Setting wins and Sets the active sheet tab.
             </p>
             Note: multiple sheets can be selected, but only one sheet can be active at one time.
            
             @return <code>true</code> if this sheet is selected
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFSheet.ActiveCell">
             Return location of the active cell, e.g. <code>A1</code>.
            
             @return the location of the active cell.
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFSheet.HasComments">
            Does this sheet have any comments on it? We need to know,
             so we can decide about writing it to disk or not
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFSheet.IsAutoFilterLocked">
            @return true when Autofilters are locked and the sheet is protected.
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFSheet.IsDeleteColumnsLocked">
            @return true when Deleting columns is locked and the sheet is protected.
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFSheet.IsDeleteRowsLocked">
            @return true when Deleting rows is locked and the sheet is protected.
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFSheet.IsFormatCellsLocked">
            @return true when Formatting cells is locked and the sheet is protected.
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFSheet.IsFormatColumnsLocked">
            @return true when Formatting columns is locked and the sheet is protected.
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFSheet.IsFormatRowsLocked">
            @return true when Formatting rows is locked and the sheet is protected.
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFSheet.IsInsertColumnsLocked">
            @return true when Inserting columns is locked and the sheet is protected.
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFSheet.IsInsertHyperlinksLocked">
            @return true when Inserting hyperlinks is locked and the sheet is protected.
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFSheet.IsInsertRowsLocked">
            @return true when Inserting rows is locked and the sheet is protected.
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFSheet.IsPivotTablesLocked">
            @return true when Pivot tables are locked and the sheet is protected.
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFSheet.IsSortLocked">
            @return true when Sorting is locked and the sheet is protected.
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFSheet.IsObjectsLocked">
            @return true when Objects are locked and the sheet is protected.
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFSheet.IsScenariosLocked">
            @return true when Scenarios are locked and the sheet is protected.
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFSheet.IsSelectLockedCellsLocked">
            @return true when Selection of locked cells is locked and the sheet is protected.
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFSheet.IsSelectUnlockedCellsLocked">
            @return true when Selection of unlocked cells is locked and the sheet is protected.
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFSheet.IsSheetLocked">
            @return true when Sheet is Protected.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFChartSheet.GetCTChartsheet">
             Provide access to the CTChartsheet bean holding this sheet's data
            
             @return the CTChartsheet bean holding this sheet's data
        </member>
        <member name="T:NPOI.XSSF.UserModel.XSSFChildAnchor">
            <AUTHOR> Kozlov
        </member>
        <member name="T:NPOI.XSSF.UserModel.XSSFClientAnchor">
             A client anchor is attached to an excel worksheet.  It anchors against
             top-left and bottom-right cells.
            
             <AUTHOR> Kozlov
        </member>
        <member name="F:NPOI.XSSF.UserModel.XSSFClientAnchor.cell1">
            Starting anchor point
        </member>
        <member name="F:NPOI.XSSF.UserModel.XSSFClientAnchor.cell2">
            Ending anchor point
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFClientAnchor.#ctor">
            Creates a new client anchor and defaults all the anchor positions to 0.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFClientAnchor.#ctor(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)">
             Creates a new client anchor and Sets the top-left and bottom-right
             coordinates of the anchor.
            
             @param dx1  the x coordinate within the first cell.
             @param dy1  the y coordinate within the first cell.
             @param dx2  the x coordinate within the second cell.
             @param dy2  the y coordinate within the second cell.
             @param col1 the column (0 based) of the first cell.
             @param row1 the row (0 based) of the first cell.
             @param col2 the column (0 based) of the second cell.
             @param row2 the row (0 based) of the second cell.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFClientAnchor.#ctor(NPOI.OpenXmlFormats.Dml.Spreadsheet.CT_Marker,NPOI.OpenXmlFormats.Dml.Spreadsheet.CT_Marker)">
             Create XSSFClientAnchor from existing xml beans
            
             @param cell1 starting anchor point
             @param cell2 ending anchor point
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFClientAnchor.From">
             Return starting anchor point
            
             @return starting anchor point
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFClientAnchor.To">
             Return ending anchor point
            
             @return ending anchor point
        </member>
        <member name="T:NPOI.XSSF.UserModel.XSSFColor">
            Represents a color in SpreadsheetML
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFColor.#ctor(NPOI.OpenXmlFormats.Spreadsheet.CT_Color)">
            Create an instance of XSSFColor from the supplied XML bean
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFColor.#ctor">
            Create an new instance of XSSFColor
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFColor.CorrectRGB(System.Byte[])">
            For RGB colours, but not ARGB (we think...)
            Excel Gets black and white the wrong way around, so switch them 
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFColor.GetARgb">
            Standard Alpha Red Green Blue ctColor value (ARGB).
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFColor.GetRgbWithTint">
            Standard Red Green Blue ctColor value (RGB) with applied tint.
            Alpha values are ignored.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFColor.GetARGBHex">
            Return the ARGB value in hex format, eg FF00FF00.
            Works for both regular and indexed colours. 
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFColor.SetRgb(System.Byte[])">
            Standard Alpha Red Green Blue ctColor value (ARGB).
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFColor.GetCTColor">
             Returns the underlying XML bean
            
             @return the underlying XML bean
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFColor.IsAuto">
             <summary>
            A bool value indicating the ctColor is automatic and system ctColor dependent.
             </summary>
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFColor.Indexed">
            Indexed ctColor value. Only used for backwards compatibility. References a ctColor in indexedColors.
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFColor.RGB">
            Standard Red Green Blue ctColor value (RGB).
            If there was an A (Alpha) value, it will be stripped.
        </member>
        <!-- 对于成员“P:NPOI.XSSF.UserModel.XSSFColor.Theme”忽略有格式错误的 XML 注释 -->
        <member name="P:NPOI.XSSF.UserModel.XSSFColor.Tint">
             Specifies the tint value applied to the ctColor.
            
             <p>
             If tint is supplied, then it is applied to the RGB value of the ctColor to determine the final
             ctColor applied.
             </p>
             <p>
             The tint value is stored as a double from -1.0 .. 1.0, where -1.0 means 100% darken and
             1.0 means 100% lighten. Also, 0.0 means no Change.
             </p>
             <p>
             In loading the RGB value, it is Converted to HLS where HLS values are (0..HLSMAX), where
             HLSMAX is currently 255.
             </p>
             Here are some examples of how to apply tint to ctColor:
             <blockquote>
             <pre>
             If (tint &lt; 0)
             Lum' = Lum * (1.0 + tint)
            
             For example: Lum = 200; tint = -0.5; Darken 50%
             Lum' = 200 * (0.5) =&gt; 100
             For example: Lum = 200; tint = -1.0; Darken 100% (make black)
             Lum' = 200 * (1.0-1.0) =&gt; 0
             If (tint &gt; 0)
             Lum' = Lum * (1.0-tint) + (HLSMAX - HLSMAX * (1.0-tint))
             For example: Lum = 100; tint = 0.75; Lighten 75%
            
             Lum' = 100 * (1-.75) + (HLSMAX - HLSMAX*(1-.75))
             = 100 * .25 + (255 - 255 * .25)
             = 25 + (255 - 63) = 25 + 192 = 217
             For example: Lum = 100; tint = 1.0; Lighten 100% (make white)
             Lum' = 100 * (1-1) + (HLSMAX - HLSMAX*(1-1))
             = 100 * 0 + (255 - 255 * 0)
             = 0 + (255 - 0) = 255
             </pre>
             </blockquote>
            
             @return the tint value
        </member>
        <member name="F:NPOI.XSSF.UserModel.XSSFComment._str">
            cached reference to the string with the comment text
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFComment.#ctor(NPOI.XSSF.Model.CommentsTable,NPOI.OpenXmlFormats.Spreadsheet.CT_Comment,NPOI.OpenXmlFormats.Vml.CT_Shape)">
            Creates a new XSSFComment, associated with a given
             low level comment object.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFComment.SetString(System.String)">
             Sets the rich text string used by this comment.
            
             @param string  the XSSFRichTextString used by this object.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFComment.GetCTComment">
            @return the xml bean holding this comment's properties
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFComment.Author">
            
             @return Name of the original comment author. Default value is blank.
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFComment.Column">
            @return the 0-based column of the cell that the comment is associated with.
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFComment.Row">
            @return the 0-based row index of the cell that the comment is associated with.
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFComment.Visible">
            @return whether the comment is visible
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFComment.String">
            @return the rich text string of the comment
        </member>
        <member name="T:NPOI.XSSF.UserModel.XSSFConditionalFormatting">
            <AUTHOR> Kozlov
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFConditionalFormatting.GetFormattingRanges">
            @return array of <tt>CellRangeAddress</tt>s. Never <code>null</code>
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFConditionalFormatting.SetRule(System.Int32,NPOI.SS.UserModel.IConditionalFormattingRule)">
             Replaces an existing Conditional Formatting rule at position idx.
             Excel allows to create up to 3 Conditional Formatting rules.
             This method can be useful to modify existing  Conditional Formatting rules.
            
             @param idx position of the rule. Should be between 0 and 2.
             @param cfRule - Conditional Formatting rule
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFConditionalFormatting.AddRule(NPOI.SS.UserModel.IConditionalFormattingRule)">
             Add a Conditional Formatting rule.
             Excel allows to create up to 3 Conditional Formatting rules.
            
             @param cfRule - Conditional Formatting rule
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFConditionalFormatting.GetRule(System.Int32)">
            @return the Conditional Formatting rule at position idx.
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFConditionalFormatting.NumberOfRules">
            @return number of Conditional Formatting rules.
        </member>
        <member name="T:NPOI.XSSF.UserModel.XSSFConditionalFormattingRule">
            <AUTHOR> Kozlov
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFConditionalFormattingRule.CreateBorderFormatting">
             Create a new border formatting structure if it does not exist,
             otherwise just return existing object.
            
             @return - border formatting object, never returns <code>null</code>.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFConditionalFormattingRule.GetBorderFormatting">
            @return - border formatting object  if defined,  <code>null</code> otherwise
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFConditionalFormattingRule.CreateFontFormatting">
             Create a new font formatting structure if it does not exist,
             otherwise just return existing object.
            
             @return - font formatting object, never returns <code>null</code>.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFConditionalFormattingRule.GetFontFormatting">
            @return - font formatting object  if defined,  <code>null</code> otherwise
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFConditionalFormattingRule.CreatePatternFormatting">
             Create a new pattern formatting structure if it does not exist,
             otherwise just return existing object.
            
             @return - pattern formatting object, never returns <code>null</code>.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFConditionalFormattingRule.GetPatternFormatting">
            @return - pattern formatting object  if defined,  <code>null</code> otherwise
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFConditionalFormattingRule.ConditionType">
             Type of conditional formatting rule.
             <p>
             MUST be either {@link ConditionalFormattingRule#CONDITION_TYPE_CELL_VALUE_IS}
             or  {@link ConditionalFormattingRule#CONDITION_TYPE_FORMULA}
             </p>
            
             @return the type of condition
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFConditionalFormattingRule.ComparisonOperation">
             The comparison function used when the type of conditional formatting is Set to
             {@link ConditionalFormattingRule#CONDITION_TYPE_CELL_VALUE_IS}
             <p>
                 MUST be a constant from {@link NPOI.ss.usermodel.ComparisonOperator}
             </p>
            
             @return the conditional format operator
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFConditionalFormattingRule.Formula1">
             The formula used to Evaluate the first operand for the conditional formatting rule.
             <p>
             If the condition type is {@link ConditionalFormattingRule#CONDITION_TYPE_CELL_VALUE_IS},
             this field is the first operand of the comparison.
             If type is {@link ConditionalFormattingRule#CONDITION_TYPE_FORMULA}, this formula is used
             to determine if the conditional formatting is applied.
             </p>
             <p>
             If comparison type is {@link ConditionalFormattingRule#CONDITION_TYPE_FORMULA} the formula MUST be a Boolean function
             </p>
            
             @return  the first formula
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFConditionalFormattingRule.Formula2">
             The formula used to Evaluate the second operand of the comparison when
             comparison type is  {@link ConditionalFormattingRule#CONDITION_TYPE_CELL_VALUE_IS} and operator
             is either {@link NPOI.ss.usermodel.ComparisonOperator#BETWEEN} or {@link NPOI.ss.usermodel.ComparisonOperator#NOT_BETWEEN}
            
             @return  the second formula
        </member>
        <member name="T:NPOI.XSSF.UserModel.XSSFConnector">
             A connection shape Drawing element. A connection shape is a line, etc.
             that connects two other shapes in this Drawing.
            
             <AUTHOR> Kozlov
        </member>
        <member name="T:NPOI.XSSF.UserModel.XSSFShape">
             Represents a shape in a SpreadsheetML Drawing.
            
             <AUTHOR> Kozlov
        </member>
        <member name="F:NPOI.XSSF.UserModel.XSSFShape.drawing">
            Parent Drawing
        </member>
        <member name="F:NPOI.XSSF.UserModel.XSSFShape.parent">
            The parent shape, always not-null for shapes in groups
        </member>
        <member name="F:NPOI.XSSF.UserModel.XSSFShape.anchor">
            anchor that is used by this shape
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFShape.GetDrawing">
             Return the Drawing that owns this shape
            
             @return the parent Drawing that owns this shape
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFShape.GetAnchor">
            @return  the anchor that is used by this shape.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFShape.GetShapeProperties">
             Returns xml bean with shape properties.
            
             @return xml bean with shape properties.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFShape.SetFillColor(System.Int32,System.Int32,System.Int32)">
            Sets the color used to fill this shape using the solid fill pattern.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFShape.SetLineStyleColor(System.Int32,System.Int32,System.Int32)">
            The color applied to the lines of this shape.
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFShape.Parent">
            Gets the parent shape.
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFShape.IsNoFill">
             Whether this shape is not Filled with a color
            
             @return true if this shape is not Filled with a color.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFConnector.#ctor(NPOI.XSSF.UserModel.XSSFDrawing,NPOI.OpenXmlFormats.Dml.Spreadsheet.CT_Connector)">
             Construct a new XSSFConnector object.
            
             @param Drawing the XSSFDrawing that owns this shape
             @param ctShape the shape bean that holds all the shape properties
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFConnector.Prototype">
             Initialize default structure of a new auto-shape
            
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFConnector.ShapeType">
             Gets the shape type, one of the constants defined in {@link NPOI.ss.usermodel.ShapeTypes}.
            
             @return the shape type
             @see NPOI.ss.usermodel.ShapeTypes
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFCreationHelper.CreateRichTextString(System.String)">
            Creates a new XSSFRichTextString for you.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFCreationHelper.CreateFormulaEvaluator">
             Creates a XSSFFormulaEvaluator, the object that Evaluates formula cells.
            
             @return a XSSFFormulaEvaluator instance
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFCreationHelper.CreateClientAnchor">
             Creates a XSSFClientAnchor. Use this object to position Drawing object in
             a sheet
            
             @return a XSSFClientAnchor instance
             @see NPOI.ss.usermodel.Drawing
        </member>
        <member name="T:NPOI.XSSF.UserModel.XSSFDataFormat">
            Handles data formats for XSSF.
            
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFDataFormat.GetFormat(System.String)">
             Get the format index that matches the given format
              string, creating a new format entry if required.
             Aliases text to the proper format as required.
            
             @param format string matching a built in format
             @return index of format.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFDataFormat.GetFormat(System.Int16)">
            Get the format string that matches the given format index
            @param index of a format
            @return string represented at index of format or null if there is not a  format at that index
        </member>
        <member name="T:NPOI.XSSF.UserModel.XSSFDataValidation">
             <AUTHOR> href="<EMAIL>">Radhakrishnan J</a>
            
        </member>
        <member name="T:NPOI.XSSF.UserModel.XSSFDataValidationConstraint">
             <AUTHOR> href="<EMAIL>">Radhakrishnan J</a>
            
        </member>
        <member name="T:NPOI.XSSF.UserModel.XSSFDataValidationHelper">
             <AUTHOR> href="<EMAIL>">Radhakrishnan J</a>
            
        </member>
        <member name="T:NPOI.XSSF.UserModel.XSSFDrawing">
             Represents a SpreadsheetML Drawing
            
             <AUTHOR> Kozlov
        </member>
        <member name="F:NPOI.XSSF.UserModel.XSSFDrawing.drawing">
            Root element of the SpreadsheetML Drawing part
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFDrawing.#ctor">
             Create a new SpreadsheetML Drawing
            
             @see NPOI.xssf.usermodel.XSSFSheet#CreateDrawingPatriarch()
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFDrawing.#ctor(NPOI.OpenXml4Net.OPC.PackagePart,NPOI.OpenXml4Net.OPC.PackageRelationship)">
             Construct a SpreadsheetML Drawing from a namespace part
            
             @param part the namespace part holding the Drawing data,
             the content type must be <code>application/vnd.openxmlformats-officedocument.Drawing+xml</code>
             @param rel  the namespace relationship holding this Drawing,
             the relationship type must be http://schemas.openxmlformats.org/officeDocument/2006/relationships/drawing
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFDrawing.NewDrawing">
             Construct a new CT_Drawing bean. By default, it's just an empty placeholder for Drawing objects
            
             @return a new CT_Drawing bean
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFDrawing.GetCTDrawing">
             Return the underlying CT_Drawing bean, the root element of the SpreadsheetML Drawing part.
            
             @return the underlying CT_Drawing bean
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFDrawing.CreateTextbox(NPOI.SS.UserModel.IClientAnchor)">
             Constructs a textbox under the Drawing.
            
             @param anchor    the client anchor describes how this group is attached
                              to the sheet.
             @return      the newly Created textbox.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFDrawing.CreatePicture(NPOI.XSSF.UserModel.XSSFClientAnchor,System.Int32)">
             Creates a picture.
            
             @param anchor    the client anchor describes how this picture is attached to the sheet.
             @param pictureIndex the index of the picture in the workbook collection of pictures,
               {@link NPOI.xssf.usermodel.XSSFWorkbook#getAllPictures()} .
            
             @return  the newly Created picture shape.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFDrawing.CreateChart(NPOI.SS.UserModel.IClientAnchor)">
            <summary>
            Creates a chart.
            </summary>
            <param name="anchor">the client anchor describes how this chart is attached to</param>
            <returns>the newly created chart</returns>
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFDrawing.AddPictureReference(System.Int32)">
             Add the indexed picture to this Drawing relations
            
             @param pictureIndex the index of the picture in the workbook collection of pictures,
               {@link NPOI.xssf.usermodel.XSSFWorkbook#getAllPictures()} .
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFDrawing.CreateSimpleShape(NPOI.XSSF.UserModel.XSSFClientAnchor)">
             Creates a simple shape.  This includes such shapes as lines, rectangles,
             and ovals.
            
             @param anchor    the client anchor describes how this group is attached
                              to the sheet.
             @return  the newly Created shape.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFDrawing.CreateConnector(NPOI.XSSF.UserModel.XSSFClientAnchor)">
             Creates a simple shape.  This includes such shapes as lines, rectangles,
             and ovals.
            
             @param anchor    the client anchor describes how this group is attached
                              to the sheet.
             @return  the newly Created shape.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFDrawing.CreateGroup(NPOI.XSSF.UserModel.XSSFClientAnchor)">
             Creates a simple shape.  This includes such shapes as lines, rectangles,
             and ovals.
            
             @param anchor    the client anchor describes how this group is attached
                              to the sheet.
             @return  the newly Created shape.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFDrawing.CreateCellComment(NPOI.SS.UserModel.IClientAnchor)">
            Creates a comment.
            @param anchor the client anchor describes how this comment is attached
                          to the sheet.
            @return the newly Created comment.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFDrawing.CreateGraphicFrame(NPOI.XSSF.UserModel.XSSFClientAnchor)">
             Creates a new graphic frame.
            
             @param anchor    the client anchor describes how this frame is attached
                              to the sheet
             @return  the newly Created graphic frame
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFDrawing.GetCharts">
            Returns all charts in this Drawing.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFDrawing.CreateTwoCellAnchor(NPOI.SS.UserModel.IClientAnchor)">
             Create and Initialize a CT_TwoCellAnchor that anchors a shape against top-left and bottom-right cells.
            
             @return a new CT_TwoCellAnchor
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFDrawing.GetShapes">
            
             @return list of shapes in this drawing
        </member>
        <member name="T:NPOI.XSSF.UserModel.XSSFEvaluationCell">
            XSSF wrapper for a cell under Evaluation
            
            <AUTHOR> Micich
        </member>
        <member name="T:NPOI.XSSF.UserModel.XSSFEvaluationSheet">
            XSSF wrapper for a sheet under Evaluation
            
            <AUTHOR> Micich
        </member>
        <member name="T:NPOI.XSSF.UserModel.XSSFEvaluationWorkbook">
             Internal POI use only
            
             <AUTHOR> Micich
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFEvaluationWorkbook.ConvertFromExternSheetIndex(System.Int32)">
            @return the sheet index of the sheet with the given external index.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFEvaluationWorkbook.ConvertToExternalSheetIndex(System.Int32)">
            @return  the external sheet index of the sheet with the given internal
            index. Used by some of the more obscure formula and named range things.
            Fairly easy on XSSF (we think...) since the internal and external
            indicies are the same
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFEvaluationWorkbook.CleanXSSFFormulaText(System.String)">
            XSSF allows certain extra textual characters in the formula that
             HSSF does not. As these can't be composed down to HSSF-compatible
             Ptgs, this method strips them out for us.
        </member>
        <member name="T:NPOI.XSSF.UserModel.XSSFEvenFooter">
             
             Even page footer value. Corresponds to even printed pages. 
             Even page(s) in the sheet may not be printed, for example, if the print area is specified to be 
             a range such that it falls outside an even page's scope. 
             If no even footer is specified, then the odd footer's value is assumed for even page footers. 
            
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFEvenFooter.#ctor(NPOI.OpenXmlFormats.Spreadsheet.CT_HeaderFooter)">
            Create an instance of XSSFEvenFooter from the supplied XML bean
            @see XSSFSheet#GetEvenFooter()
            @param headerFooter
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFEvenFooter.Text">
            Get the content text representing the footer
            @return text
        </member>
        <member name="T:NPOI.XSSF.UserModel.XSSFEvenHeader">
             <p>
             Even page header value. Corresponds to even printed pages.
             Even page(s) in the sheet may not be printed, for example, if the print area is specified to be 
             a range such that it falls outside an even page's scope.
             If no even header is specified, then odd header value is assumed for even page headers.
            </p>
            
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFEvenHeader.#ctor(NPOI.OpenXmlFormats.Spreadsheet.CT_HeaderFooter)">
            Create an instance of XSSFEvenHeader from the supplied XML bean
            @see XSSFSheet#GetEvenHeader()
            @param headerFooter
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFEvenHeader.Text">
            Get the content text representing this header
            @return text
        </member>
        <member name="T:NPOI.XSSF.UserModel.XSSFFactory">
             Instantiates sub-classes of POIXMLDocumentPart depending on their relationship type
            
             <AUTHOR> Kozlov
        </member>
        <member name="T:NPOI.XSSF.UserModel.XSSFFirstFooter">
            
            First page footer content. Corresponds to first printed page.  
            The first logical page in the sheet may not be printed, for example, if the print area is specified to 
            be a range such that it falls outside the first page's scope.
            
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFFirstFooter.#ctor(NPOI.OpenXmlFormats.Spreadsheet.CT_HeaderFooter)">
            Create an instance of XSSFFirstFooter from the supplied XML bean
            @see XSSFSheet#getFirstFooter()
            @param headerFooter
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFFirstFooter.Text">
            Get the content text representing the footer
            @return text
        </member>
        <member name="T:NPOI.XSSF.UserModel.XSSFFirstHeader">
             
             First page header content. Corresponds to first printed page.
             The first logical page in the sheet may not be printed, for example, if the print area is specified to 
             be a range such that it falls outside the first page's scope.
            
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFFirstHeader.#ctor(NPOI.OpenXmlFormats.Spreadsheet.CT_HeaderFooter)">
            Create an instance of XSSFFirstHeader from the supplied XML bean
            @see XSSFSheet#getFirstHeader()
            @param headerFooter
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFFirstHeader.Text">
            Get the content text representing this header
            @return text
        </member>
        <member name="T:NPOI.XSSF.UserModel.XSSFFont">
             Represents a font used in a workbook.
            
             <AUTHOR> Bronzetti
        </member>
        <member name="F:NPOI.XSSF.UserModel.XSSFFont.DEFAULT_FONT_NAME">
            By default, Microsoft Office Excel 2007 uses the Calibry font in font size 11
        </member>
        <member name="F:NPOI.XSSF.UserModel.XSSFFont.DEFAULT_FONT_SIZE">
            By default, Microsoft Office Excel 2007 uses the Calibry font in font size 11
        </member>
        <member name="F:NPOI.XSSF.UserModel.XSSFFont.DEFAULT_FONT_COLOR">
            Default font color is black
            @see NPOI.SS.usermodel.IndexedColors#BLACK
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFFont.#ctor(NPOI.OpenXmlFormats.Spreadsheet.CT_Font)">
             Create a new XSSFFont
            
             @param font the underlying CT_Font bean
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFFont.#ctor">
            Create a new XSSFont. This method is protected to be used only by XSSFWorkbook
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFFont.GetCTFont">
            get the underlying CT_Font font
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFFont.GetXSSFColor">
             get the color value for the font
             References a color defined as  Standard Alpha Red Green Blue color value (ARGB).
            
             @return XSSFColor - rgb color to use
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFFont.GetThemeColor">
             get the color value for the font
             References a color defined in theme.
            
             @return short - theme defined to use
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFFont.SetCharSet(System.Byte)">
             set character-set to use.
            
             @param charset - charset
             @see FontCharset
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFFont.SetCharSet(System.Int32)">
             set character-set to use.
            
             @param charset - charset
             @see FontCharset
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFFont.SetCharSet(NPOI.SS.UserModel.FontCharset)">
             set character-set to use.
            
             @param charSet
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFFont.SetColor(NPOI.XSSF.UserModel.XSSFColor)">
             set the color for the font in Standard Alpha Red Green Blue color value
            
             @param color - color to use
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFFont.SetThemeColor(System.Int16)">
             set the theme color for the font to use
            
             @param theme - theme color to use
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFFont.SetUnderline(NPOI.SS.UserModel.FontUnderlineType)">
             set an enumeration representing the style of underlining that is used.
             The none style is equivalent to not using underlining at all.
             The possible values for this attribute are defined by the FontUnderline
            
             @param underline - FontUnderline enum value
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFFont.RegisterTo(NPOI.XSSF.Model.StylesTable)">
            **
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFFont.SetThemesTable(NPOI.XSSF.Model.ThemesTable)">
            Records the Themes Table that is associated with
             the current font, used when looking up theme
             based colours and properties.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFFont.GetScheme">
             get the font scheme property.
             is used only in StylesTable to create the default instance of font
            
             @return FontScheme
             @see NPOI.XSSF.model.StylesTable#CreateDefaultFont()
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFFont.SetScheme(NPOI.SS.UserModel.FontScheme)">
             set font scheme property
            
             @param scheme - FontScheme enum value
             @see FontScheme
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFFont.SetFamily(NPOI.SS.UserModel.FontFamily)">
             set an enumeration representing the font family this font belongs to.
             A font family is a set of fonts having common stroke width and serif characteristics.
            
             @param family font family
             @link #SetFamily(int value)
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFFont.IsBold">
             get a bool value for the boldness to use.
            
             @return bool - bold
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFFont.Charset">
             get character-set to use.
            
             @return int - character-set (0-255)
             @see NPOI.SS.usermodel.FontCharset
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFFont.Color">
             get the indexed color value for the font
             References a color defined in IndexedColors.
            
             @return short - indexed color to use
             @see IndexedColors
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFFont.FontHeight">
             get the font height in point.
            
             @return short - height in point
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFFont.FontHeightInPoints">
            @see #GetFontHeight()
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFFont.FontName">
             get the name of the font (i.e. Arial)
            
             @return String - a string representing the name of the font to use
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFFont.IsItalic">
             get a bool value that specify whether to use italics or not
            
             @return bool - value for italic
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFFont.IsStrikeout">
             get a bool value that specify whether to use a strikeout horizontal line through the text or not
            
             @return bool - value for strikeout
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFFont.TypeOffset">
             get normal,super or subscript.
            
             @return short - offset type to use (none,super,sub)
             @see Font#SS_NONE
             @see Font#SS_SUPER
             @see Font#SS_SUB
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFFont.Underline">
             get type of text underlining to use
            
             @return byte - underlining type
             @see NPOI.SS.usermodel.FontUnderline
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFFont.Boldweight">
            get the boldness to use
            @return boldweight
            @see #BOLDWEIGHT_NORMAL
            @see #BOLDWEIGHT_BOLD
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFFont.Family">
             get the font family to use.
            
             @return the font family to use
             @see NPOI.SS.usermodel.FontFamily
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFFont.Index">
            get the index within the XSSFWorkbook (sequence within the collection of Font objects)
            @return unique index number of the underlying record this Font represents (probably you don't care
             unless you're comparing which one is which)
        </member>
        <member name="T:NPOI.XSSF.UserModel.XSSFFontFormatting">
            <AUTHOR> Kozlov
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFFontFormatting.GetXSSFColor">
            
             @return xssf color wrapper or null if color info is missing
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFFontFormatting.SetFontStyle(System.Boolean,System.Boolean)">
             Set font style options.
            
             @param italic - if true, Set posture style to italic, otherwise to normal
             @param bold if true, Set font weight to bold, otherwise to normal
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFFontFormatting.ResetFontStyle">
            Set font style options to default values (non-italic, non-bold)
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFFontFormatting.EscapementType">
             Get the type of super or subscript for the font
            
             @return super or subscript option
             @see #SS_NONE
             @see #SS_SUPER
             @see #SS_SUB
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFFontFormatting.FontColorIndex">
            @return font color index
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFFontFormatting.FontHeight">
             Gets the height of the font in 1/20th point units
            
             @return fontheight (in points/20); or -1 if not modified
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFFontFormatting.UnderlineType">
             Get the type of underlining for the font
            
             @return font underlining type
            
             @see #U_NONE
             @see #U_SINGLE
             @see #U_DOUBLE
             @see #U_SINGLE_ACCOUNTING
             @see #U_DOUBLE_ACCOUNTING
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFFontFormatting.IsBold">
             Get whether the font weight is Set to bold or not
            
             @return bold - whether the font is bold or not
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFFontFormatting.IsItalic">
            @return true if font style was Set to <i>italic</i>
        </member>
        <member name="T:NPOI.XSSF.UserModel.XSSFFormulaEvaluator">
             Evaluates formula cells.<p/>
            
             For performance reasons, this class keeps a cache of all previously calculated intermediate
             cell values.  Be sure to call {@link #ClearAllCachedResultValues()} if any workbook cells are Changed between
             calls to Evaluate~ methods on this class.
            
             <AUTHOR> S. Deshmukh &lt; amolweb at ya hoo dot com &gt;
             <AUTHOR> Micich
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFFormulaEvaluator.#ctor(NPOI.XSSF.UserModel.XSSFWorkbook,NPOI.SS.Formula.IStabilityClassifier)">
            @param stabilityClassifier used to optimise caching performance. Pass <code>null</code>
            for the (conservative) assumption that any cell may have its defInition Changed After
            Evaluation begins.
            @deprecated (Sep 2009) (reduce overloading) use {@link #Create(XSSFWorkbook, NPOI.ss.formula.IStabilityClassifier, NPOI.ss.formula.udf.UDFFinder)}
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFFormulaEvaluator.Create(NPOI.XSSF.UserModel.XSSFWorkbook,NPOI.SS.Formula.IStabilityClassifier,NPOI.SS.Formula.Udf.UDFFinder)">
            @param stabilityClassifier used to optimise caching performance. Pass <code>null</code>
            for the (conservative) assumption that any cell may have its defInition Changed After
            Evaluation begins.
            @param udfFinder pass <code>null</code> for default (AnalysisToolPak only)
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFFormulaEvaluator.ClearAllCachedResultValues">
            Should be called whenever there are major Changes (e.g. moving sheets) to input cells
            in the Evaluated workbook.
            Failure to call this method After changing cell values will cause incorrect behaviour
            of the Evaluate~ methods of this class
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFFormulaEvaluator.Evaluate(NPOI.SS.UserModel.ICell)">
            If cell Contains a formula, the formula is Evaluated and returned,
            else the CellValue simply copies the appropriate cell value from
            the cell and also its cell type. This method should be preferred over
            EvaluateInCell() when the call should not modify the contents of the
            original cell.
            @param cell
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFFormulaEvaluator.EvaluateFormulaCell(NPOI.SS.UserModel.ICell)">
            If cell Contains formula, it Evaluates the formula,
             and saves the result of the formula. The cell
             remains as a formula cell.
            Else if cell does not contain formula, this method leaves
             the cell unChanged.
            Note that the type of the formula result is returned,
             so you know what kind of value is also stored with
             the formula.
            <pre>
            int EvaluatedCellType = Evaluator.EvaluateFormulaCell(cell);
            </pre>
            Be aware that your cell will hold both the formula,
             and the result. If you want the cell Replaced with
             the result of the formula, use {@link #Evaluate(NPOI.ss.usermodel.Cell)} }
            @param cell The cell to Evaluate
            @return The type of the formula result (the cell's type remains as HSSFCell.CELL_TYPE_FORMULA however)
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFFormulaEvaluator.EvaluateInCell(NPOI.SS.UserModel.ICell)">
            If cell Contains formula, it Evaluates the formula, and
             Puts the formula result back into the cell, in place
             of the old formula.
            Else if cell does not contain formula, this method leaves
             the cell unChanged.
            Note that the same instance of HSSFCell is returned to
            allow chained calls like:
            <pre>
            int EvaluatedCellType = Evaluator.EvaluateInCell(cell).CellType;
            </pre>
            Be aware that your cell value will be Changed to hold the
             result of the formula. If you simply want the formula
             value computed for you, use {@link #EvaluateFormulaCell(NPOI.ss.usermodel.Cell)} }
            @param cell
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFFormulaEvaluator.EvaluateAllFormulaCells(NPOI.SS.UserModel.IWorkbook)">
            Loops over all cells in all sheets of the supplied
             workbook.
            For cells that contain formulas, their formulas are
             Evaluated, and the results are saved. These cells
             remain as formula cells.
            For cells that do not contain formulas, no Changes
             are made.
            This is a helpful wrapper around looping over all
             cells, and calling EvaluateFormulaCell on each one.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFFormulaEvaluator.EvaluateAll">
            Loops over all cells in all sheets of the supplied
             workbook.
            For cells that contain formulas, their formulas are
             Evaluated, and the results are saved. These cells
             remain as formula cells.
            For cells that do not contain formulas, no Changes
             are made.
            This is a helpful wrapper around looping over all
             cells, and calling EvaluateFormulaCell on each one.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFFormulaEvaluator.EvaluateFormulaCellValue(NPOI.SS.UserModel.ICell)">
            Returns a CellValue wrapper around the supplied ValueEval instance.
        </member>
        <member name="T:NPOI.XSSF.UserModel.XSSFGraphicFrame">
             Represents DrawingML GraphicalObjectFrame.
            
             <AUTHOR> Kashitsyn
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFGraphicFrame.#ctor(NPOI.XSSF.UserModel.XSSFDrawing,NPOI.OpenXmlFormats.Dml.Spreadsheet.CT_GraphicalObjectFrame)">
             Construct a new XSSFGraphicFrame object.
            
             @param Drawing the XSSFDrawing that owns this frame
             @param ctGraphicFrame the XML bean that stores this frame content
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFGraphicFrame.Prototype">
            Initialize default structure of a new graphic frame
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFGraphicFrame.SetMacro(System.String)">
            Sets the frame macro.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFGraphicFrame.SetChart(NPOI.XSSF.UserModel.XSSFChart,System.String)">
            Assign a DrawingML chart to the graphic frame.
        </member>
        <!-- 对于成员“M:NPOI.XSSF.UserModel.XSSFGraphicFrame.AppendChartElement(NPOI.OpenXmlFormats.Dml.CT_GraphicalObjectData,System.String)”忽略有格式错误的 XML 注释 -->
        <member name="P:NPOI.XSSF.UserModel.XSSFGraphicFrame.Name">
            Returns the frame name.
            @return name of the frame
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFGraphicFrame.Anchor">
            Returns the frame anchor.
            @return the anchor this frame is attached to
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFGraphicFrame.Id">
            Gets the frame id.
        </member>
        <member name="T:NPOI.XSSF.UserModel.XSSFMap">
             This class : the Map element (Open Office XML Part 4:
             chapter 3.16.2)
             
             This element Contains all of the properties related to the XML map,
             and the behaviors expected during data refresh operations.
            
             <AUTHOR> Manicardi
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFMap.GetRelatedSingleXMLCell">
            @return the list of Single Xml Cells that provide a map rule to this mapping.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFMap.GetRelatedTables">
            @return the list of all Tables that provide a map rule to this mapping
        </member>
        <member name="T:NPOI.XSSF.UserModel.XSSFName">
             Represents a defined named range in a SpreadsheetML workbook.
             <p>
             Defined names are descriptive text that is used to represents a cell, range of cells, formula, or constant value.
             Use easy-to-understand names, such as Products, to refer to hard to understand ranges, such as <code>Sales!C20:C30</code>.
             </p>
             Example:
             <pre><blockquote>
               XSSFWorkbook wb = new XSSFWorkbook();
               XSSFSheet sh = wb.CreateSheet("Sheet1");
            
               //applies to the entire workbook
               XSSFName name1 = wb.CreateName();
               name1.SetNameName("FMLA");
               name1.SetRefersToFormula("Sheet1!$B$3");
            
               //applies to Sheet1
               XSSFName name2 = wb.CreateName();
               name2.SetNameName("SheetLevelName");
               name2.SetComment("This name is scoped to Sheet1");
               name2.SetLocalSheetId(0);
               name2.SetRefersToFormula("Sheet1!$B$3");
            
             </blockquote></pre>
            
             <AUTHOR> Burch
             <AUTHOR> Kozlov
        </member>
        <member name="F:NPOI.XSSF.UserModel.XSSFName.BUILTIN_PRINT_AREA">
            A built-in defined name that specifies the workbook's print area
        </member>
        <member name="F:NPOI.XSSF.UserModel.XSSFName.BUILTIN_PRINT_TITLE">
            A built-in defined name that specifies the row(s) or column(s) to repeat
            at the top of each printed page.
        </member>
        <member name="F:NPOI.XSSF.UserModel.XSSFName.BUILTIN_CRITERIA">
            A built-in defined name that refers to a range Containing the criteria values
            to be used in Applying an advanced filter to a range of data
        </member>
        <member name="F:NPOI.XSSF.UserModel.XSSFName.BUILTIN_EXTRACT">
            this defined name refers to the range Containing the filtered
            output values resulting from Applying an advanced filter criteria to a source
            range
        </member>
        <!-- 对于成员“F:NPOI.XSSF.UserModel.XSSFName.BUILTIN_FILTER_DB”忽略有格式错误的 XML 注释 -->
        <member name="F:NPOI.XSSF.UserModel.XSSFName.BUILTIN_CONSOLIDATE_AREA">
            A built-in defined name that refers to a consolidation area
        </member>
        <member name="F:NPOI.XSSF.UserModel.XSSFName.BUILTIN_DATABASE">
            A built-in defined name that specified that the range specified is from a database data source
        </member>
        <member name="F:NPOI.XSSF.UserModel.XSSFName.BUILTIN_SHEET_TITLE">
            A built-in defined name that refers to a sheet title.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFName.#ctor(NPOI.OpenXmlFormats.Spreadsheet.CT_DefinedName,NPOI.XSSF.UserModel.XSSFWorkbook)">
             Creates an XSSFName object - called internally by XSSFWorkbook.
            
             @param name - the xml bean that holds data represenring this defined name.
             @param workbook - the workbook object associated with the name
             @see NPOI.XSSF.usermodel.XSSFWorkbook#CreateName()
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFName.GetCTName">
            Returns the underlying named range object
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFName.Equals(System.Object)">
             Compares this name to the specified object.
             The result is <code>true</code> if the argument is XSSFName and the
             underlying CTDefinedName bean Equals to the CTDefinedName representing this name
            
             @param   o   the object to compare this <code>XSSFName</code> against.
             @return  <code>true</code> if the <code>XSSFName </code>are Equal;
                      <code>false</code> otherwise.
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFName.NameName">
             Returns the name that will appear in the user interface for the defined name.
            
             @return text name of this defined name
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFName.SheetIndex">
             Returns the sheet index this name applies to.
            
             @return the sheet index this name applies to, -1 if this name applies to the entire workbook
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFName.Function">
             Indicates that the defined name refers to a user-defined function.
             This attribute is used when there is an Add-in or other code project associated with the file.
            
             @return <code>true</code> indicates the name refers to a function.
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFName.FunctionGroupId">
             Returns the function group index if the defined name refers to a function. The function
             group defines the general category for the function. This attribute is used when there is
             an Add-in or other code project associated with the file.
            
             @return the function group index that defines the general category for the function
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFName.SheetName">
             Get the sheets name which this named range is referenced to
            
             @return sheet name, which this named range referred to.
             Empty string if the referenced sheet name weas not found.
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFName.IsFunctionName">
             Is the name refers to a user-defined function ?
            
             @return <code>true</code> if this name refers to a user-defined function
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFName.Comment">
             Returns the comment the user provided when the name was Created.
            
             @return the user comment for this named range
        </member>
        <member name="T:NPOI.XSSF.UserModel.XSSFHyperlink">
            XSSF Implementation of a Hyperlink.
            Note - unlike with HSSF, many kinds of hyperlink
            are largely stored as relations of the sheet
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFHyperlink.#ctor(NPOI.SS.UserModel.HyperlinkType)">
             Create a new XSSFHyperlink. This method is protected to be used only by XSSFCreationHelper
            
             @param type - the type of hyperlink to create
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFHyperlink.#ctor(NPOI.OpenXmlFormats.Spreadsheet.CT_Hyperlink,NPOI.OpenXml4Net.OPC.PackageRelationship)">
             Create a XSSFHyperlink amd Initialize it from the supplied CTHyperlink bean and namespace relationship
            
             @param ctHyperlink the xml bean Containing xml properties
             @param hyperlinkRel the relationship in the underlying OPC namespace which stores the actual link's Address
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFHyperlink.GetCTHyperlink">
            @return the underlying CTHyperlink object
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFHyperlink.NeedsRelationToo">
            Do we need to a relation too, to represent
            this hyperlink?
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFHyperlink.GenerateRelationIfNeeded(NPOI.OpenXml4Net.OPC.PackagePart)">
            Generates the relation if required
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFHyperlink.GetCellRef">
            Get the reference of the cell this applies to,
            es A55
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFHyperlink.SetCellReference(System.String)">
            Assigns this hyperlink to the given cell reference
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFHyperlink.Type">
             Return the type of this hyperlink
            
             @return the type of this hyperlink
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFHyperlink.Address">
             Hyperlink Address. Depending on the hyperlink type it can be URL, e-mail, path to a file
            
             @return the Address of this hyperlink
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFHyperlink.Label">
             Return text label for this hyperlink
            
             @return text to display
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFHyperlink.Location">
             Location within target. If target is a workbook (or this workbook) this shall refer to a
             sheet and cell or a defined name. Can also be an HTML anchor if target is HTML file.
            
             @return location
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFHyperlink.FirstColumn">
             Return the column of the first cell that Contains the hyperlink
            
             @return the 0-based column of the first cell that Contains the hyperlink
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFHyperlink.LastColumn">
             Return the column of the last cell that Contains the hyperlink
            
             @return the 0-based column of the last cell that Contains the hyperlink
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFHyperlink.FirstRow">
             Return the row of the first cell that Contains the hyperlink
            
             @return the 0-based row of the cell that Contains the hyperlink
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFHyperlink.LastRow">
             Return the row of the last cell that Contains the hyperlink
            
             @return the 0-based row of the last cell that Contains the hyperlink
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFHyperlink.Tooltip">
            <summary>
            get or set additional text to help the user understand more about the hyperlink
            </summary>
        </member>
        <member name="T:NPOI.XSSF.UserModel.XSSFOddFooter">
             Odd page footer value. Corresponds to odd printed pages.
             Odd page(s) in the sheet may not be printed, for example, if the print area is specified to be 
             a range such that it falls outside an odd page's scope.
            
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFOddFooter.#ctor(NPOI.OpenXmlFormats.Spreadsheet.CT_HeaderFooter)">
            Create an instance of XSSFOddFooter from the supplied XML bean
            @see XSSFSheet#GetOddFooter()
            @param headerFooter
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFOddFooter.Text">
            Get the content text representing the footer
            @return text
        </member>
        <member name="T:NPOI.XSSF.UserModel.XSSFOddHeader">
             Odd page header value. Corresponds to odd printed pages. 
             Odd page(s) in the sheet may not be printed, for example, if the print area is specified to be 
             a range such that it falls outside an odd page's scope.
            
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFOddHeader.#ctor(NPOI.OpenXmlFormats.Spreadsheet.CT_HeaderFooter)">
            Create an instance of XSSFOddHeader from the supplied XML bean
            @see XSSFSheet#GetOddHeader()
            @param headerFooter
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFOddHeader.Text">
            Get the content text representing this header
            @return text
        </member>
        <member name="T:NPOI.XSSF.UserModel.XSSFPatternFormatting">
            <AUTHOR> Kozlov
        </member>
        <member name="T:NPOI.XSSF.UserModel.XSSFPicture">
             Represents a picture shape in a SpreadsheetML Drawing.
            
             <AUTHOR> Kozlov
        </member>
        <member name="F:NPOI.XSSF.UserModel.XSSFPicture.DEFAULT_COLUMN_WIDTH">
             Column width measured as the number of characters of the maximum digit width of the
             numbers 0, 1, 2, ..., 9 as rendered in the normal style's font. There are 4 pixels of margin
             pAdding (two on each side), plus 1 pixel pAdding for the gridlines.
            
             This value is the same for default font in Office 2007 (Calibry) and Office 2003 and earlier (Arial)
        </member>
        <member name="F:NPOI.XSSF.UserModel.XSSFPicture.prototype">
            A default instance of CTShape used for creating new shapes.
        </member>
        <member name="F:NPOI.XSSF.UserModel.XSSFPicture.ctPicture">
            This object specifies a picture object and all its properties
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFPicture.#ctor(NPOI.XSSF.UserModel.XSSFDrawing,NPOI.OpenXmlFormats.Dml.Spreadsheet.CT_Picture)">
             Construct a new XSSFPicture object. This constructor is called from
              {@link XSSFDrawing#CreatePicture(XSSFClientAnchor, int)}
            
             @param Drawing the XSSFDrawing that owns this picture
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFPicture.#ctor(NPOI.XSSF.UserModel.XSSFDrawing,System.Xml.XmlNode)">
             Returns a prototype that is used to construct new shapes
            
             @return a prototype that is used to construct new shapes
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFPicture.SetPictureReference(NPOI.OpenXml4Net.OPC.PackageRelationship)">
             Link this shape with the picture data
            
             @param rel relationship referring the picture data
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFPicture.GetCTPicture">
             Return the underlying CT_Picture bean that holds all properties for this picture
            
             @return the underlying CT_Picture bean
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFPicture.Resize">
             Reset the image to the original size.
            
             <p>
             Please note, that this method works correctly only for workbooks
             with the default font size (Calibri 11pt for .xlsx).
             If the default font is Changed the resized image can be streched vertically or horizontally.
             </p>
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFPicture.Resize(System.Double)">
             Reset the image to the original size.
             <p>
             Please note, that this method works correctly only for workbooks
             with the default font size (Calibri 11pt for .xlsx).
             If the default font is Changed the resized image can be streched vertically or horizontally.
             </p>
            
             @param scale the amount by which image dimensions are multiplied relative to the original size.
             <code>resize(1.0)</code> Sets the original size, <code>resize(0.5)</code> resize to 50% of the original,
             <code>resize(2.0)</code> resizes to 200% of the original.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFPicture.GetPreferredSize">
             Calculate the preferred size for this picture.
            
             @return XSSFClientAnchor with the preferred size for this image
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFPicture.GetPreferredSize(System.Double)">
             Calculate the preferred size for this picture.
            
             @param scale the amount by which image dimensions are multiplied relative to the original size.
             @return XSSFClientAnchor with the preferred size for this image
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFPicture.GetImageDimension(NPOI.OpenXml4Net.OPC.PackagePart,System.Int32)">
             Return the dimension of this image
            
             @param part the namespace part holding raw picture data
             @param type type of the picture: {@link Workbook#PICTURE_TYPE_JPEG},
             {@link Workbook#PICTURE_TYPE_PNG} or {@link Workbook#PICTURE_TYPE_DIB}
            
             @return image dimension in pixels
        </member>
        <member name="T:NPOI.XSSF.UserModel.XSSFPictureData">
            Raw picture data, normally attached to a SpreadsheetML Drawing.
            As a rule, pictures are stored in the /xl/media/ part of a SpreadsheetML package.
        </member>
        <member name="F:NPOI.XSSF.UserModel.XSSFPictureData.RELATIONS">
            Relationships for each known picture type
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFPictureData.#ctor">
             Create a new XSSFPictureData node
            
             @see NPOI.xssf.usermodel.XSSFWorkbook#AddPicture(byte[], int)
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFPictureData.#ctor(NPOI.OpenXml4Net.OPC.PackagePart,NPOI.OpenXml4Net.OPC.PackageRelationship)">
             Construct XSSFPictureData from a namespace part
            
             @param part the namespace part holding the Drawing data,
             @param rel  the namespace relationship holding this Drawing,
             the relationship type must be http://schemas.Openxmlformats.org/officeDocument/2006/relationships/image
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFPictureData.SuggestFileExtension">
             Suggests a file extension for this image.
            
             @return the file extension.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFPictureData.GetPictureType">
             Return an integer constant that specifies type of this picture
            
             @return an integer constant that specifies type of this picture 
             @see NPOI.ss.usermodel.Workbook#PICTURE_TYPE_EMF
             @see NPOI.ss.usermodel.Workbook#PICTURE_TYPE_WMF
             @see NPOI.ss.usermodel.Workbook#PICTURE_TYPE_PICT
             @see NPOI.ss.usermodel.Workbook#PICTURE_TYPE_JPEG
             @see NPOI.ss.usermodel.Workbook#PICTURE_TYPE_PNG
             @see NPOI.ss.usermodel.Workbook#PICTURE_TYPE_DIB
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFPictureData.Data">
            <summary>
            Gets the picture data as a byte array.
            </summary>
        </member>
        <member name="T:NPOI.XSSF.UserModel.XSSFPrintSetup">
            Page Setup and page margins Settings for the worksheet.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFPrintSetup.SetPaperSize(NPOI.SS.UserModel.PaperSize)">
             Set the paper size as enum value.
            
             @param size value for the paper size.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFPrintSetup.GetPaperSizeEnum">
             Returns the paper size as enum.
            
             @return PaperSize paper size
             @see PaperSize
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFPrintSetup.Orientation">
             Orientation of the page: landscape - portrait.
            
             @return Orientation of the page
             @see PrintOrientation
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFPrintSetup.PageOrder">
             Get print page order.
            
             @return PageOrder
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFPrintSetup.PaperSize">
             Returns the paper size.
            
             @return short - paper size
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFPrintSetup.Scale">
             Returns the scale.
            
             @return short - scale
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFPrintSetup.PageStart">
             Set the page numbering start.
             Page number for first printed page. If no value is specified, then 'automatic' is assumed.
            
             @return page number for first printed page
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFPrintSetup.FitWidth">
             Returns the number of pages wide to fit sheet in.
            
             @return number of pages wide to fit sheet in
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFPrintSetup.FitHeight">
             Returns the number of pages high to fit the sheet in.
            
             @return number of pages high to fit the sheet in
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFPrintSetup.LeftToRight">
             Returns the left to right print order.
            
             @return left to right print order
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFPrintSetup.Landscape">
             Returns the landscape mode.
            
             @return landscape mode
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFPrintSetup.ValidSettings">
             Use the printer's defaults Settings for page Setup values and don't use the default values
             specified in the schema. For example, if dpi is not present or specified in the XML, the
             application shall not assume 600dpi as specified in the schema as a default and instead
             shall let the printer specify the default dpi.
            
             @return valid Settings
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFPrintSetup.NoColor">
             Returns the black and white Setting.
            
             @return black and white Setting
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFPrintSetup.Draft">
             Returns the draft mode.
            
             @return draft mode
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFPrintSetup.Notes">
             Returns the print notes.
            
             @return print notes
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFPrintSetup.NoOrientation">
             Returns the no orientation.
            
             @return no orientation
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFPrintSetup.UsePage">
             Returns the use page numbers.
            
             @return use page numbers
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFPrintSetup.HResolution">
             Returns the horizontal resolution.
            
             @return horizontal resolution
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFPrintSetup.VResolution">
             Returns the vertical resolution.
            
             @return vertical resolution
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFPrintSetup.HeaderMargin">
             Returns the header margin.
            
             @return header margin
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFPrintSetup.FooterMargin">
             Returns the footer margin.
            
             @return footer margin
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFPrintSetup.Copies">
             Returns the number of copies.
            
             @return number of copies
        </member>
        <member name="T:NPOI.XSSF.UserModel.XSSFRelation">
            
        </member>
        <member name="F:NPOI.XSSF.UserModel.XSSFRelation._table">
            A map to lookup POIXMLRelation by its relation type
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFRelation.GetContents(NPOI.OpenXml4Net.OPC.PackagePart)">
            Fetches the InputStream to read the contents, based
            of the specified core part, for which we are defined
            as a suitable relationship
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFRelation.GetInstance(System.String)">
             Get POIXMLRelation by relation type
            
             @param rel relation type, for example,
                <code>http://schemas.openxmlformats.org/officeDocument/2006/relationships/image</code>
             @return registered POIXMLRelation or null if not found
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFRelation.RemoveRelation(NPOI.XSSF.UserModel.XSSFRelation)">
            <summary>
            Removes the relation from the internal table.
            Following readings of files will ignoring the removed relation.
            </summary>
            <param name="relation">Relation to remove</param>
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFRelation.AddRelation(NPOI.XSSF.UserModel.XSSFRelation)">
            <summary>
            Adds the relation to the internal table.
            Following readings of files will process the given relation.
            </summary>
            <param name="relation">Relation to add</param>
        </member>
        <member name="T:NPOI.XSSF.UserModel.XSSFRichTextString">
             Rich text unicode string.  These strings can have fonts applied to arbitary parts of the string.
            
             <p>
             Most strings in a workbook have formatting applied at the cell level, that is, the entire string in the cell has the
             same formatting applied. In these cases, the formatting for the cell is stored in the styles part,
             and the string for the cell can be shared across the workbook. The following code illustrates the example.
             </p>
            
             <blockquote>
             <pre>
                 cell1.SetCellValue(new XSSFRichTextString("Apache POI"));
                 cell2.SetCellValue(new XSSFRichTextString("Apache POI"));
                 cell3.SetCellValue(new XSSFRichTextString("Apache POI"));
             </pre>
             </blockquote>
             In the above example all three cells will use the same string cached on workbook level.
            
             <p>
             Some strings in the workbook may have formatting applied at a level that is more granular than the cell level.
             For instance, specific characters within the string may be bolded, have coloring, italicizing, etc.
             In these cases, the formatting is stored along with the text in the string table, and is treated as
             a unique entry in the workbook. The following xml and code snippet illustrate this.
             </p>
            
             <blockquote>
             <pre>
                 XSSFRichTextString s1 = new XSSFRichTextString("Apache POI");
                 s1.ApplyFont(boldArial);
                 cell1.SetCellValue(s1);
            
                 XSSFRichTextString s2 = new XSSFRichTextString("Apache POI");
                 s2.ApplyFont(italicCourier);
                 cell2.SetCellValue(s2);
             </pre>
             </blockquote>
            
            
             <AUTHOR> Kozlov
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFRichTextString.#ctor(System.String)">
            Create a rich text string
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFRichTextString.#ctor">
            Create empty rich text string and Initialize it with empty string
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFRichTextString.#ctor(NPOI.OpenXmlFormats.Spreadsheet.CT_Rst)">
            Create a rich text string from the supplied XML bean
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFRichTextString.ApplyFont(System.Int32,System.Int32,System.Int16)">
             Applies a font to the specified characters of a string.
            
             @param startIndex    The start index to apply the font to (inclusive)
             @param endIndex      The end index to apply the font to (exclusive)
             @param fontIndex     The font to use.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFRichTextString.ApplyFont(System.Int32,System.Int32,NPOI.SS.UserModel.IFont)">
             Applies a font to the specified characters of a string.
            
             @param startIndex    The start index to apply the font to (inclusive)
             @param endIndex      The end index to apply to font to (exclusive)
             @param font          The index of the font to use.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFRichTextString.ApplyFont(NPOI.SS.UserModel.IFont)">
            Sets the font of the entire string.
            @param font          The font to use.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFRichTextString.ApplyFont(System.Int16)">
             Applies the specified font to the entire string.
            
             @param fontIndex  the font to Apply.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFRichTextString.Append(System.String,NPOI.XSSF.UserModel.XSSFFont)">
             Append new text to this text run and apply the specify font to it
            
             @param text  the text to append
             @param font  the font to apply to the Appended text or <code>null</code> if no formatting is required
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFRichTextString.Append(System.String)">
             Append new text to this text run
            
             @param text  the text to append
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFRichTextString.SetRunAttributes(NPOI.OpenXmlFormats.Spreadsheet.CT_Font,NPOI.OpenXmlFormats.Spreadsheet.CT_RPrElt)">
            Copy font attributes from CTFont bean into CTRPrElt bean
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFRichTextString.ClearFormatting">
            Removes any formatting that may have been applied to the string.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFRichTextString.GetIndexOfFormattingRun(System.Int32)">
             The index within the string to which the specified formatting run applies.
            
             @param index     the index of the formatting run
             @return  the index within the string.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFRichTextString.GetLengthOfFormattingRun(System.Int32)">
             Returns the number of characters this format run covers.
            
             @param index     the index of the formatting run
             @return  the number of characters this format run covers
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFRichTextString.ToString">
            Returns the plain string representation.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFRichTextString.GetFontOfFormattingRun(System.Int32)">
             Gets a copy of the font used in a particular formatting Run.
            
             @param index     the index of the formatting run
             @return  A copy of the  font used or null if no formatting is applied to the specified text Run.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFRichTextString.GetFontAtIndex(System.Int32)">
             Return a copy of the font in use at a particular index.
            
             @param index         The index.
             @return              A copy of the  font that's currently being applied at that
                                  index or null if no font is being applied or the
                                  index is out of range.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFRichTextString.GetCTRst">
            Return the underlying xml bean
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFRichTextString.ToCTFont(NPOI.OpenXmlFormats.Spreadsheet.CT_RPrElt)">
            
             CTRPrElt --> CTFont adapter
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFRichTextString.PreserveSpaces(System.String)">
            **
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFRichTextString.UtfDecode(System.String)">
             For all characters which cannot be represented in XML as defined by the XML 1.0 specification,
             the characters are escaped using the Unicode numerical character representation escape character
             format _xHHHH_, where H represents a hexadecimal character in the character's value.
             <p>
             Example: The Unicode character 0D is invalid in an XML 1.0 document,
             so it shall be escaped as <code>_x000D_</code>.
             </p>
             See section 3.18.9 in the OOXML spec.
            
             @param   value the string to decode
             @return  the decoded string
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFRichTextString.Length">
            Returns the number of characters in this string.
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFRichTextString.NumFormattingRuns">
            @return  The number of formatting Runs used.
        </member>
        <member name="T:NPOI.XSSF.UserModel.XSSFRow">
            High level representation of a row of a spreadsheet.
        </member>
        <member name="F:NPOI.XSSF.UserModel.XSSFRow._row">
            the xml bean Containing all cell defInitions for this row
        </member>
        <member name="F:NPOI.XSSF.UserModel.XSSFRow._cells">
            Cells of this row keyed by their column indexes.
            The TreeMap ensures that the cells are ordered by columnIndex in the ascending order.
        </member>
        <member name="F:NPOI.XSSF.UserModel.XSSFRow._sheet">
            the parent sheet
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFRow.#ctor(NPOI.OpenXmlFormats.Spreadsheet.CT_Row,NPOI.XSSF.UserModel.XSSFSheet)">
             Construct a XSSFRow.
            
             @param row the xml bean Containing all cell defInitions for this row.
             @param sheet the parent sheet.
        </member>
        <!-- 对于成员“M:NPOI.XSSF.UserModel.XSSFRow.CellIterator”忽略有格式错误的 XML 注释 -->
        <member name="M:NPOI.XSSF.UserModel.XSSFRow.GetEnumerator">
             Alias for {@link #cellIterator()} to allow  foreach loops:
             <blockquote><pre>
             for(Cell cell : row){
                 ...
             }
             </pre></blockquote>
            
             @return an iterator over cells in this row.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFRow.CompareTo(NPOI.XSSF.UserModel.XSSFRow)">
             Compares two <code>XSSFRow</code> objects.  Two rows are equal if they belong to the same worksheet and
             their row indexes are Equal.
            
             @param   row   the <code>XSSFRow</code> to be Compared.
             @return	the value <code>0</code> if the row number of this <code>XSSFRow</code> is
             		equal to the row number of the argument <code>XSSFRow</code>; a value less than
             		<code>0</code> if the row number of this this <code>XSSFRow</code> is numerically less
             		than the row number of the argument <code>XSSFRow</code>; and a value greater
             		than <code>0</code> if the row number of this this <code>XSSFRow</code> is numerically
             		 greater than the row number of the argument <code>XSSFRow</code>.
             @throws ArgumentException if the argument row belongs to a different worksheet
        </member>
        <!-- 对于成员“M:NPOI.XSSF.UserModel.XSSFRow.CreateCell(System.Int32)”忽略有格式错误的 XML 注释 -->
        <!-- 对于成员“M:NPOI.XSSF.UserModel.XSSFRow.CreateCell(System.Int32,NPOI.SS.UserModel.CellType)”忽略有格式错误的 XML 注释 -->
        <member name="M:NPOI.XSSF.UserModel.XSSFRow.GetCell(System.Int32)">
             Returns the cell at the given (0 based) index,
              with the {@link NPOI.SS.usermodel.Row.MissingCellPolicy} from the parent Workbook.
            
             @return the cell at the given (0 based) index
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFRow.RetrieveCell(System.Int32)">
            <summary>
            Get the hssfcell representing a given column (logical cell)
            0-based. If you ask for a cell that is not defined, then
            you Get a null.
            This is the basic call, with no policies applied
            </summary>
            <param name="cellnum">0 based column number</param>
            <returns>Cell representing that column or null if Undefined.</returns>
        </member>
        <!-- 对于成员“M:NPOI.XSSF.UserModel.XSSFRow.GetCell(System.Int32,NPOI.SS.UserModel.MissingCellPolicy)”忽略有格式错误的 XML 注释 -->
        <member name="M:NPOI.XSSF.UserModel.XSSFRow.SetRowStyle(NPOI.SS.UserModel.ICellStyle)">
            Applies a whole-row cell styling to the row.
            If the value is null then the style information is Removed,
             causing the cell to used the default workbook style.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFRow.RemoveCell(NPOI.SS.UserModel.ICell)">
             Remove the Cell from this row.
            
             @param cell the cell to remove
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFRow.GetCTRow">
             Returns the underlying CT_Row xml bean Containing all cell defInitions in this row
            
             @return the underlying CT_Row xml bean
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFRow.OnDocumentWrite">
             Fired when the document is written to an output stream.
            
             @see NPOI.XSSF.usermodel.XSSFSheet#Write(java.io.OutputStream) ()
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFRow.ToString">
            @return formatted xml representation of this row
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFRow.Shift(System.Int32)">
             update cell references when Shifting rows
            
             @param n the number of rows to move
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFRow.Sheet">
             Returns the XSSFSheet this row belongs to
            
             @return the XSSFSheet that owns this row
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFRow.FirstCellNum">
             Get the number of the first cell Contained in this row.
            
             @return short representing the first logical cell in the row,
              or -1 if the row does not contain any cells.
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFRow.LastCellNum">
             Gets the index of the last cell Contained in this row <b>PLUS ONE</b>. The result also
             happens to be the 1-based column number of the last cell.  This value can be used as a
             standard upper bound when iterating over cells:
             <pre>
             short minColIx = row.GetFirstCellNum();
             short maxColIx = row.GetLastCellNum();
             for(short colIx=minColIx; colIx&lt;maxColIx; colIx++) {
               XSSFCell cell = row.GetCell(colIx);
               if(cell == null) {
                 continue;
               }
               //... do something with cell
             }
             </pre>
            
             @return short representing the last logical cell in the row <b>PLUS ONE</b>,
               or -1 if the row does not contain any cells.
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFRow.Height">
             Get the row's height measured in twips (1/20th of a point). If the height is not Set, the default worksheet value is returned,
             See {@link NPOI.XSSF.usermodel.XSSFSheet#GetDefaultRowHeightInPoints()}
            
             @return row height measured in twips (1/20th of a point)
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFRow.HeightInPoints">
             Returns row height measured in point size. If the height is not Set, the default worksheet value is returned,
             See {@link NPOI.XSSF.usermodel.XSSFSheet#GetDefaultRowHeightInPoints()}
            
             @return row height measured in point size
             @see NPOI.XSSF.usermodel.XSSFSheet#GetDefaultRowHeightInPoints()
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFRow.PhysicalNumberOfCells">
             Gets the number of defined cells (NOT number of cells in the actual row!).
             That is to say if only columns 0,4,5 have values then there would be 3.
            
             @return int representing the number of defined cells in the row.
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFRow.RowNum">
             Get row number this row represents
            
             @return the row number (0 based)
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFRow.ZeroHeight">
             Get whether or not to display this row with 0 height
            
             @return - height is zero or not.
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFRow.IsFormatted">
            Is this row formatted? Most aren't, but some rows
             do have whole-row styles. For those that do, you
             can get the formatting from {@link #GetRowStyle()}
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFRow.RowStyle">
            Returns the whole-row cell style. Most rows won't
             have one of these, so will return null. Call
             {@link #isFormatted()} to check first.
        </member>
        <member name="T:NPOI.XSSF.UserModel.XSSFShapeGroup">
             This object specifies a group shape that represents many shapes grouped together. This shape is to be treated
             just as if it were a regular shape but instead of being described by a single geometry it is made up of all the
             shape geometries encompassed within it. Within a group shape each of the shapes that make up the group are
             specified just as they normally would.
            
             <AUTHOR> Kozlov
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFShapeGroup.#ctor(NPOI.XSSF.UserModel.XSSFDrawing,NPOI.OpenXmlFormats.Dml.Spreadsheet.CT_GroupShape)">
             Construct a new XSSFSimpleShape object.
            
             @param Drawing the XSSFDrawing that owns this shape
             @param ctGroup the XML bean that stores this group content
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFShapeGroup.Prototype">
            Initialize default structure of a new shape group
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFShapeGroup.CreateTextbox(NPOI.XSSF.UserModel.XSSFChildAnchor)">
             Constructs a textbox.
            
             @param anchor the child anchor describes how this shape is attached
                           to the group.
             @return      the newly Created textbox.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFShapeGroup.CreateSimpleShape(NPOI.XSSF.UserModel.XSSFChildAnchor)">
             Creates a simple shape.  This includes such shapes as lines, rectangles,
             and ovals.
            
             @param anchor the child anchor describes how this shape is attached
                           to the group.
             @return the newly Created shape.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFShapeGroup.CreateConnector(NPOI.XSSF.UserModel.XSSFChildAnchor)">
             Creates a simple shape.  This includes such shapes as lines, rectangles,
             and ovals.
            
             @param anchor the child anchor describes how this shape is attached
                           to the group.
             @return the newly Created shape.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFShapeGroup.CreatePicture(NPOI.XSSF.UserModel.XSSFClientAnchor,System.Int32)">
             Creates a picture.
            
             @param anchor       the client anchor describes how this picture is attached to the sheet.
             @param pictureIndex the index of the picture in the workbook collection of pictures,
                                 {@link XSSFWorkbook#getAllPictures()} .
             @return the newly Created picture shape.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFShapeGroup.SetCoordinates(System.Int32,System.Int32,System.Int32,System.Int32)">
            Sets the coordinate space of this group.  All children are constrained
            to these coordinates.
        </member>
        <member name="T:NPOI.XSSF.UserModel.XSSFSheetConditionalFormatting">
            <AUTHOR> Kozlov
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheetConditionalFormatting.CreateConditionalFormattingRule(NPOI.SS.UserModel.ComparisonOperator,System.String,System.String)">
             A factory method allowing to create a conditional formatting rule
             with a cell comparison operator<p/>
             TODO - formulas Containing cell references are currently not Parsed properly
            
             @param comparisonOperation - a constant value from
            		 <tt>{@link NPOI.hssf.record.CFRuleRecord.ComparisonOperator}</tt>: <p>
             <ul>
            		 <li>BETWEEN</li>
            		 <li>NOT_BETWEEN</li>
            		 <li>EQUAL</li>
            		 <li>NOT_EQUAL</li>
            		 <li>GT</li>
            		 <li>LT</li>
            		 <li>GE</li>
            		 <li>LE</li>
             </ul>
             </p>
             @param formula1 - formula for the valued, Compared with the cell
             @param formula2 - second formula (only used with
             {@link NPOI.ss.usermodel.ComparisonOperator#BETWEEN}) and
             {@link NPOI.ss.usermodel.ComparisonOperator#NOT_BETWEEN} operations)
        </member>
        <!-- 对于成员“M:NPOI.XSSF.UserModel.XSSFSheetConditionalFormatting.CreateConditionalFormattingRule(System.String)”忽略有格式错误的 XML 注释 -->
        <!-- 对于成员“M:NPOI.XSSF.UserModel.XSSFSheetConditionalFormatting.AddConditionalFormatting(NPOI.SS.UserModel.IConditionalFormatting)”忽略有格式错误的 XML 注释 -->
        <member name="M:NPOI.XSSF.UserModel.XSSFSheetConditionalFormatting.GetConditionalFormattingAt(System.Int32)">
             Gets Conditional Formatting object at a particular index
            
             @param index
            			of the Conditional Formatting object to fetch
             @return Conditional Formatting object
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSheetConditionalFormatting.RemoveConditionalFormatting(System.Int32)">
            Removes a Conditional Formatting object by index
            @param index of a Conditional Formatting object to remove
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFSheetConditionalFormatting.NumConditionalFormattings">
            @return number of Conditional Formatting objects of the sheet
        </member>
        <member name="T:NPOI.XSSF.UserModel.XSSFSimpleShape">
             Represents a shape with a predefined geometry in a SpreadsheetML Drawing.
             Possible shape types are defined in {@link NPOI.ss.usermodel.ShapeTypes}
            
             <AUTHOR> Kozlov
        </member>
        <member name="F:NPOI.XSSF.UserModel.XSSFSimpleShape.prototype">
            A default instance of CT_Shape used for creating new shapes.
        </member>
        <member name="F:NPOI.XSSF.UserModel.XSSFSimpleShape.ctShape">
            Xml bean that stores properties of this shape
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSimpleShape.Prototype">
            Prototype with the default structure of a new auto-shape.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFSimpleShape.ApplyAttributes(NPOI.OpenXmlFormats.Spreadsheet.CT_RPrElt,NPOI.OpenXmlFormats.Dml.CT_TextCharacterProperties)">
            
             org.openxmlformats.schemas.spreadsheetml.x2006.main.CTRPrElt to
             org.openxmlformats.schemas.drawingml.x2006.main.CTFont adapter
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFSimpleShape.ShapeType">
             Gets the shape type, one of the constants defined in {@link NPOI.ss.usermodel.ShapeTypes}.
            
             @return the shape type
             @see NPOI.ss.usermodel.ShapeTypes
        </member>
        <member name="T:NPOI.XSSF.UserModel.XSSFTable">
             
             This class : the Table Part (Open Office XML Part 4:
             chapter 3.5.1)
             
             This implementation works under the assumption that a table Contains mappings to a subtree of an XML.
             The root element of this subtree an occur multiple times (one for each row of the table). The child nodes
             of the root element can be only attributes or element with maxOccurs=1 property set
             
            
             <AUTHOR> Manicardi
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFTable.MapsTo(System.Int64)">
            Checks if this Table element Contains even a single mapping to the map identified by id
            @param id the XSSFMap ID
            @return true if the Table element contain mappings
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFTable.GetCommonXpath">
            
            Calculates the xpath of the root element for the table. This will be the common part
            of all the mapping's xpaths
            
            @return the xpath of the table's root element
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFTable.GetStartCellReference">
             @return The reference for the cell in the top-left part of the table
             (see Open Office XML Part 4: chapter 3.5.1.2, attribute ref) 
            
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFTable.GetEndCellReference">
             @return The reference for the cell in the bottom-right part of the table
             (see Open Office XML Part 4: chapter 3.5.1.2, attribute ref)
            
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFTable.UpdateHeaders">
            Synchronize table headers with cell values in the parent sheet.
            Headers <em>must</em> be in sync, otherwise Excel will display a
            "Found unreadable content" message on startup.
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFTable.Name">
            @return the name of the Table, if set
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFTable.DisplayName">
            @return the display name of the Table, if set
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFTable.NumberOfMappedColumns">
            @return  the number of mapped table columns (see Open Office XML Part 4: chapter 3.5.1.4)
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFTable.RowCount">
              @return the total number of rows in the selection. (Note: in this version autofiltering is ignored)
            
        </member>
        <member name="T:NPOI.XSSF.UserModel.XSSFTextBox">
             Represents a text box in a SpreadsheetML Drawing.
            
             <AUTHOR> Kozlov
        </member>
        <member name="T:NPOI.XSSF.UserModel.XSSFVMLDrawing">
             Represents a SpreadsheetML VML Drawing.
            
             <p>
             In Excel 2007 VML Drawings are used to describe properties of cell comments,
             although the spec says that VML is deprecated:
             </p>
             <p>
             The VML format is a legacy format originally introduced with Office 2000 and is included and fully defined
             in this Standard for backwards compatibility reasons. The DrawingML format is a newer and richer format
             Created with the goal of eventually replacing any uses of VML in the Office Open XML formats. VML should be
             considered a deprecated format included in Office Open XML for legacy reasons only and new applications that
             need a file format for Drawings are strongly encouraged to use preferentially DrawingML
             </p>
             
             <p>
             Warning - Excel is known to Put invalid XML into these files!
              For example, &gt;br&lt; without being closed or escaped crops up.
             </p>
            
             See 6.4 VML - SpreadsheetML Drawing in Office Open XML Part 4 - Markup Language Reference.pdf
            
             <AUTHOR> Kozlov
        </member>
        <member name="F:NPOI.XSSF.UserModel.XSSFVMLDrawing.ptrn_shapeId">
            regexp to parse shape ids, in VML they have weird form of id="_x0000_s1026"
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFVMLDrawing.#ctor">
             Create a new SpreadsheetML Drawing
            
             @see XSSFSheet#CreateDrawingPatriarch()
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFVMLDrawing.#ctor(NPOI.OpenXml4Net.OPC.PackagePart,NPOI.OpenXml4Net.OPC.PackageRelationship)">
             Construct a SpreadsheetML Drawing from a namespace part
            
             @param part the namespace part holding the Drawing data,
             the content type must be <code>application/vnd.Openxmlformats-officedocument.Drawing+xml</code>
             @param rel  the namespace relationship holding this Drawing,
             the relationship type must be http://schemas.Openxmlformats.org/officeDocument/2006/relationships/drawing
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFVMLDrawing.newDrawing">
            Initialize a new Speadsheet VML Drawing
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFVMLDrawing.FindCommentShape(System.Int32,System.Int32)">
             Find a shape with ClientData of type "NOTE" and the specified row and column
            
             @return the comment shape or <code>null</code>
        </member>
        <member name="T:NPOI.XSSF.UserModel.XSSFWorkbook">
            High level representation of a SpreadsheetML workbook.  This is the first object most users
            will construct whether they are Reading or writing a workbook.  It is also the
            top level object for creating new sheets/etc.
        </member>
        <member name="F:NPOI.XSSF.UserModel.XSSFWorkbook.DEFAULT_CHARACTER_WIDTH">
            Width of one character of the default font in pixels. Same for Calibry and Arial.
        </member>
        <member name="F:NPOI.XSSF.UserModel.XSSFWorkbook.Max_SENSITIVE_SHEET_NAME_LEN">
            Excel silently tRuncates long sheet names to 31 chars.
            This constant is used to ensure uniqueness in the first 31 chars
        </member>
        <member name="F:NPOI.XSSF.UserModel.XSSFWorkbook.PICTURE_TYPE_EMF">
            Extended windows meta file 
        </member>
        <member name="F:NPOI.XSSF.UserModel.XSSFWorkbook.PICTURE_TYPE_WMF">
            Windows Meta File 
        </member>
        <member name="F:NPOI.XSSF.UserModel.XSSFWorkbook.PICTURE_TYPE_PICT">
            Mac PICT format 
        </member>
        <member name="F:NPOI.XSSF.UserModel.XSSFWorkbook.PICTURE_TYPE_JPEG">
            JPEG format 
        </member>
        <member name="F:NPOI.XSSF.UserModel.XSSFWorkbook.PICTURE_TYPE_PNG">
            PNG format 
        </member>
        <member name="F:NPOI.XSSF.UserModel.XSSFWorkbook.PICTURE_TYPE_DIB">
            Device independent bitmap 
        </member>
        <member name="F:NPOI.XSSF.UserModel.XSSFWorkbook.PICTURE_TYPE_GIF">
            Images formats supported by XSSF but not by HSSF
        </member>
        <member name="F:NPOI.XSSF.UserModel.XSSFWorkbook.workbook">
            The underlying XML bean
        </member>
        <member name="F:NPOI.XSSF.UserModel.XSSFWorkbook.sheets">
            this holds the XSSFSheet objects attached to this workbook
        </member>
        <member name="F:NPOI.XSSF.UserModel.XSSFWorkbook.namedRanges">
            this holds the XSSFName objects attached to this workbook
        </member>
        <member name="F:NPOI.XSSF.UserModel.XSSFWorkbook.sharedStringSource">
            shared string table - a cache of strings in this workbook
        </member>
        <member name="F:NPOI.XSSF.UserModel.XSSFWorkbook.stylesSource">
            A collection of shared objects used for styling content,
            e.g. fonts, cell styles, colors, etc.
        </member>
        <member name="F:NPOI.XSSF.UserModel.XSSFWorkbook._udfFinder">
            The locator of user-defined functions.
            By default includes functions from the Excel Analysis Toolpack
        </member>
        <member name="F:NPOI.XSSF.UserModel.XSSFWorkbook.calcChain">
            TODO
        </member>
        <member name="F:NPOI.XSSF.UserModel.XSSFWorkbook.mapInfo">
            A collection of custom XML mappings
        </member>
        <member name="F:NPOI.XSSF.UserModel.XSSFWorkbook.formatter">
            Used to keep track of the data formatter so that all
            CreateDataFormatter calls return the same one for a given
            book.  This ensures that updates from one places is visible
            someplace else.
        </member>
        <member name="F:NPOI.XSSF.UserModel.XSSFWorkbook._missingCellPolicy">
            The policy to apply in the event of missing or
             blank cells when fetching from a row.
            See {@link NPOI.ss.usermodel.Row.MissingCellPolicy}
        </member>
        <member name="F:NPOI.XSSF.UserModel.XSSFWorkbook.pictures">
            array of pictures for this workbook
        </member>
        <member name="F:NPOI.XSSF.UserModel.XSSFWorkbook._creationHelper">
            cached instance of XSSFCreationHelper for this workbook
            @see {@link #getCreationHelper()}
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.#ctor">
            Create a new SpreadsheetML workbook.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.#ctor(NPOI.OpenXml4Net.OPC.OPCPackage)">
             Constructs a XSSFWorkbook object given a OpenXML4J <code>Package</code> object,
              see <a href="http://poi.apache.org/oxml4j/">http://poi.apache.org/oxml4j/</a>.
             
             Once you have finished working with the Workbook, you should close the package
             by calling pkg.close, to avoid leaving file handles open.
             
             Creating a XSSFWorkbook from a file-backed OPC Package has a lower memory
              footprint than an InputStream backed one.
            
             @param pkg the OpenXML4J <code>OPC Package</code> object.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.#ctor(System.IO.Stream)">
            Constructs a XSSFWorkbook object, by buffering the whole stream into memory
             and then opening an {@link OPCPackage} object for it.
            
            Using an {@link InputStream} requires more memory than using a File, so
             if a {@link File} is available then you should instead do something like
              <pre><code>
                  OPCPackage pkg = OPCPackage.open(path);
                  XSSFWorkbook wb = new XSSFWorkbook(pkg);
                  // work with the wb object
                  ......
                  pkg.close(); // gracefully closes the underlying zip file
              </code></pre>     
        </member>
        <!-- 对于成员“M:NPOI.XSSF.UserModel.XSSFWorkbook.#ctor(System.String)”忽略有格式错误的 XML 注释 -->
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.OnWorkbookCreate">
            Create a new CT_Workbook with all values Set to default
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.newPackage">
            Create a new SpreadsheetML namespace and Setup the default minimal content
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.GetCTWorkbook">
             Return the underlying XML bean
            
             @return the underlying CT_Workbook bean
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.AddPicture(System.Byte[],System.Int32)">
             Adds a picture to the workbook.
            
             @param pictureData       The bytes of the picture
             @param format            The format of the picture.
            
             @return the index to this picture (0 based), the Added picture can be obtained from {@link #getAllPictures()} .
             @see Workbook#PICTURE_TYPE_EMF
             @see Workbook#PICTURE_TYPE_WMF
             @see Workbook#PICTURE_TYPE_PICT
             @see Workbook#PICTURE_TYPE_JPEG
             @see Workbook#PICTURE_TYPE_PNG
             @see Workbook#PICTURE_TYPE_DIB
             @see #getAllPictures()
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.AddPicture(System.IO.Stream,System.Int32)">
             Adds a picture to the workbook.
            
             @param is                The sream to read image from
             @param format            The format of the picture.
            
             @return the index to this picture (0 based), the Added picture can be obtained from {@link #getAllPictures()} .
             @see Workbook#PICTURE_TYPE_EMF
             @see Workbook#PICTURE_TYPE_WMF
             @see Workbook#PICTURE_TYPE_PICT
             @see Workbook#PICTURE_TYPE_JPEG
             @see Workbook#PICTURE_TYPE_PNG
             @see Workbook#PICTURE_TYPE_DIB
             @see #getAllPictures()
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.CloneSheet(System.Int32)">
             Create an XSSFSheet from an existing sheet in the XSSFWorkbook.
              The Cloned sheet is a deep copy of the original.
            
             @return XSSFSheet representing the Cloned sheet.
             @throws ArgumentException if the sheet index in invalid
             @throws POIXMLException if there were errors when cloning
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.CreateCellStyle">
             Create a new XSSFCellStyle and add it to the workbook's style table
            
             @return the new XSSFCellStyle object
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.CreateDataFormat">
             Returns the instance of XSSFDataFormat for this workbook.
            
             @return the XSSFDataFormat object
             @see NPOI.ss.usermodel.DataFormat
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.CreateFont">
             Create a new Font and add it to the workbook's font table
            
             @return new font object
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.CreateSheet">
             Create an XSSFSheet for this workbook, Adds it to the sheets and returns
             the high level representation.  Use this to create new sheets.
            
             @return XSSFSheet representing the new sheet.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.CreateSheet(System.String)">
             Create a new sheet for this Workbook and return the high level representation.
             Use this to create new sheets.
            
             <p>
                 Note that Excel allows sheet names up to 31 chars in length but other applications
                 (such as OpenOffice) allow more. Some versions of Excel crash with names longer than 31 chars,
                 others - tRuncate such names to 31 character.
             </p>
             <p>
                 POI's SpreadsheetAPI silently tRuncates the input argument to 31 characters.
                 Example:
            
                 <pre><code>
                 Sheet sheet = workbook.CreateSheet("My very long sheet name which is longer than 31 chars"); // will be tRuncated
                 assert 31 == sheet.SheetName.Length;
                 assert "My very long sheet name which i" == sheet.SheetName;
                 </code></pre>
             </p>
            
             Except the 31-character constraint, Excel applies some other rules:
             <p>
             Sheet name MUST be unique in the workbook and MUST NOT contain the any of the following characters:
             <ul>
             <li> 0x0000 </li>
             <li> 0x0003 </li>
             <li> colon (:) </li>
             <li> backslash (\) </li>
             <li> asterisk (*) </li>
             <li> question mark (?) </li>
             <li> forward slash (/) </li>
             <li> opening square bracket ([) </li>
             <li> closing square bracket (]) </li>
             </ul>
             The string MUST NOT begin or end with the single quote (') character.
             </p>
            
             <p>
             See {@link org.apache.poi.ss.util.WorkbookUtil#createSafeSheetName(String nameProposal)}
                  for a safe way to create valid names
             </p>
             @param sheetname  sheetname to set for the sheet.
             @return Sheet representing the new sheet.
             @throws IllegalArgumentException if the name is null or invalid
              or workbook already contains a sheet with this name
             @see org.apache.poi.ss.util.WorkbookUtil#createSafeSheetName(String nameProposal)
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.FindFont(System.Int16,System.Int16,System.Int16,System.String,System.Boolean,System.Boolean,NPOI.SS.UserModel.FontSuperScript,NPOI.SS.UserModel.FontUnderlineType)">
            Finds a font that matches the one with the supplied attributes
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.GetAllPictures">
             Gets all pictures from the Workbook.
            
             @return the list of pictures (a list of {@link XSSFPictureData} objects.)
             @see #AddPicture(byte[], int)
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.GetCellStyleAt(System.Int16)">
             Get the cell style object at the given index
            
             @param idx  index within the Set of styles
             @return XSSFCellStyle object at the index
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.GetFontAt(System.Int16)">
             Get the font at the given index number
            
             @param idx  index number
             @return XSSFFont at the index
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.GetNameIndex(System.String)">
             Gets the named range index by his name
             <i>Note:</i>Excel named ranges are case-insensitive and
             this method performs a case-insensitive search.
            
             @param name named range name
             @return named range index
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.GetPrintArea(System.Int32)">
            Retrieves the reference for the printarea of the specified sheet, the sheet name is Appended to the reference even if it was not specified.
            @param sheetIndex Zero-based sheet index (0 Represents the first sheet to keep consistent with java)
            @return String Null if no print area has been defined
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.GetSheet(System.String)">
             Get sheet with the given name (case insensitive match)
            
             @param name of the sheet
             @return XSSFSheet with the name provided or <code>null</code> if it does not exist
        </member>
        <!-- 对于成员“M:NPOI.XSSF.UserModel.XSSFWorkbook.GetSheetAt(System.Int32)”忽略有格式错误的 XML 注释 -->
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.GetSheetIndex(System.String)">
            <summary>
            Returns the index of the sheet by his name (case insensitive match)
            </summary>
            <param name="name">the sheet name</param>
            <returns>index of the sheet (0 based) or -1 if not found</returns>
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.GetSheetIndex(NPOI.SS.UserModel.ISheet)">
             Returns the index of the given sheet
            
             @param sheet the sheet to look up
             @return index of the sheet (0 based). <tt>-1</tt> if not found
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.GetSheetName(System.Int32)">
             Get the sheet name
            
             @param sheetIx Number
             @return Sheet name
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.GetEnumerator">
             Allows foreach loops:
             <pre><code>
             XSSFWorkbook wb = new XSSFWorkbook(package);
             for(XSSFSheet sheet : wb){
            
             }
             </code></pre>
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.IsMacroEnabled">
            Are we a normal workbook (.xlsx), or a
             macro enabled workbook (.xlsm)?
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.RemoveName(NPOI.XSSF.UserModel.XSSFName)">
            As {@link #removeName(String)} is not necessarily unique 
            (name + sheet index is unique), this method is more accurate.
            
            @param name the name to remove.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.RemovePrintArea(System.Int32)">
             Delete the printarea for the sheet specified
            
             @param sheetIndex 0-based sheet index (0 = First Sheet)
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.RemoveSheetAt(System.Int32)">
             Removes sheet at the given index.<p/>
            
             Care must be taken if the Removed sheet is the currently active or only selected sheet in
             the workbook. There are a few situations when Excel must have a selection and/or active
             sheet. (For example when printing - see Bug 40414).<br/>
            
             This method Makes sure that if the Removed sheet was active, another sheet will become
             active in its place.  Furthermore, if the Removed sheet was the only selected sheet, another
             sheet will become selected.  The newly active/selected sheet will have the same index, or
             one less if the Removed sheet was the last in the workbook.
            
             @param index of the sheet  (0-based)
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.OnSheetDelete(System.Int32)">
             Gracefully remove references to the sheet being deleted
            
             @param index the 0-based index of the sheet to delete
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.ValidateSheetIndex(System.Int32)">
             Validate sheet index
            
             @param index the index to validate
             @throws ArgumentException if the index is out of range (index
                        &lt; 0 || index &gt;= NumberOfSheets).
        </member>
        <!-- 对于成员“M:NPOI.XSSF.UserModel.XSSFWorkbook.SetPrintArea(System.Int32,System.String)”忽略有格式错误的 XML 注释 -->
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.SetPrintArea(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)">
            For the Convenience of Java Programmers maintaining pointers.
            @see #setPrintArea(int, String)
            @param sheetIndex Zero-based sheet index (0 = First Sheet)
            @param startColumn Column to begin printarea
            @param endColumn Column to end the printarea
            @param startRow Row to begin the printarea
            @param endRow Row to end the printarea
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.SetRepeatingRowsAndColumns(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)">
             Sets the repeating rows and columns for a sheet.
             <p/>
             To Set just repeating columns:
             <pre>
              workbook.SetRepeatingRowsAndColumns(0,0,1,-1,-1);
             </pre>
             To Set just repeating rows:
             <pre>
              workbook.SetRepeatingRowsAndColumns(0,-1,-1,0,4);
             </pre>
             To remove all repeating rows and columns for a sheet.
             <pre>
              workbook.SetRepeatingRowsAndColumns(0,-1,-1,-1,-1);
             </pre>
            
             @param sheetIndex  0 based index to sheet.
             @param startColumn 0 based start of repeating columns.
             @param endColumn   0 based end of repeating columns.
             @param startRow    0 based start of repeating rows.
             @param endRow      0 based end of repeating rows.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.CreateBuiltInName(System.String,System.Int32)">
             Generates a NameRecord to represent a built-in region
            
             @return a new NameRecord
             @throws ArgumentException if sheetNumber is invalid
             @throws POIXMLException if such a name already exists in the workbook
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.SetSelectedTab(System.Int32)">
            We only Set one sheet as selected for compatibility with HSSF.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.SetSheetName(System.Int32,System.String)">
             Set the sheet name.
            
             @param sheetIndex sheet number (0 based)
             @param sheetname  the new sheet name
             @throws ArgumentException if the name is null or invalid
              or workbook already Contains a sheet with this name
             @see {@link #CreateSheet(String)}
             @see {@link NPOI.ss.util.WorkbookUtil#CreateSafeSheetName(String nameProposal)}
                  for a safe way to create valid names
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.SetSheetOrder(System.String,System.Int32)">
             Sets the order of appearance for a given sheet.
            
             @param sheetname the name of the sheet to reorder
             @param pos the position that we want to insert the sheet into (0 based)
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.SaveNamedRanges">
            marshal named ranges from the {@link #namedRanges} collection to the underlying CT_Workbook bean
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.GetSharedStringSource">
             Returns SharedStringsTable - tha cache of string for this workbook
            
             @return the shared string table
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.GetStylesSource">
            Return a object representing a collection of shared objects used for styling content,
            e.g. fonts, cell styles, colors, etc.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.GetTheme">
            Returns the Theme of current workbook.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.GetCreationHelper">
            Returns an object that handles instantiating concrete
             classes of the various instances for XSSF.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.ContainsSheet(System.String,System.Int32)">
             Determines whether a workbook Contains the provided sheet name.
             For the purpose of comparison, long names are tRuncated to 31 chars.
            
             @param name the name to Test (case insensitive match)
             @param excludeSheetIdx the sheet to exclude from the check or -1 to include all sheets in the Check.
             @return true if the sheet Contains the name, false otherwise.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.IsDate1904">
            Gets a bool value that indicates whether the date systems used in the workbook starts in 1904.
            <p>
            The default value is false, meaning that the workbook uses the 1900 date system,
            where 1/1/1900 is the first day in the system..
            </p>
            @return true if the date systems used in the workbook starts in 1904
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.GetAllEmbedds">
            Get the document's embedded files.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.IsSheetHidden(System.Int32)">
            Check whether a sheet is hidden.
            <p>
            Note that a sheet could instead be Set to be very hidden, which is different
             ({@link #isSheetVeryHidden(int)})
            </p>
            @param sheetIx Number
            @return <code>true</code> if sheet is hidden
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.IsSheetVeryHidden(System.Int32)">
            Check whether a sheet is very hidden.
            <p>
            This is different from the normal hidden status
             ({@link #isSheetHidden(int)})
            </p>
            @param sheetIx sheet index to check
            @return <code>true</code> if sheet is very hidden
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.SetSheetHidden(System.Int32,System.Boolean)">
             Sets the visible state of this sheet.
             <p>
               Calling <code>setSheetHidden(sheetIndex, true)</code> is equivalent to
               <code>setSheetHidden(sheetIndex, Workbook.SHEET_STATE_HIDDEN)</code>.
             <br/>
               Calling <code>setSheetHidden(sheetIndex, false)</code> is equivalent to
               <code>setSheetHidden(sheetIndex, Workbook.SHEET_STATE_VISIBLE)</code>.
             </p>
            
             @param sheetIx   the 0-based index of the sheet
             @param hidden whether this sheet is hidden
             @see #setSheetHidden(int, int)
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.SetSheetHidden(System.Int32,NPOI.SS.UserModel.SheetState)">
             Hide or unhide a sheet.
            
             <ul>
              <li>0 - visible. </li>
              <li>1 - hidden. </li>
              <li>2 - very hidden.</li>
             </ul>
             @param sheetIx the sheet index (0-based)
             @param state one of the following <code>Workbook</code> constants:
                    <code>Workbook.SHEET_STATE_VISIBLE</code>,
                    <code>Workbook.SHEET_STATE_HIDDEN</code>, or
                    <code>Workbook.SHEET_STATE_VERY_HIDDEN</code>.
             @throws ArgumentException if the supplied sheet index or state is invalid
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.SetSheetHidden(System.Int32,System.Int32)">
            <summary>
            Hide or unhide a sheet.
            </summary>
            <param name="sheetIx">The sheet number</param>
            <param name="hidden">0 for not hidden, 1 for hidden, 2 for very hidden</param>
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.OnDeleteFormula(NPOI.XSSF.UserModel.XSSFCell)">
             Fired when a formula is deleted from this workbook,
             for example when calling cell.SetCellFormula(null)
            
             @see XSSFCell#setCellFormula(String)
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.GetCalculationChain">
             Return the CalculationChain object for this workbook
             <p>
               The calculation chain object specifies the order in which the cells in a workbook were last calculated
             </p>
            
             @return the <code>CalculationChain</code> object or <code>null</code> if not defined
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.GetCustomXMLMappings">
            
             @return a collection of custom XML mappings defined in this workbook
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.GetMapInfo">
            
             @return the helper class used to query the custom XML mapping defined in this workbook
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.IsStructureLocked">
            Specifies a bool value that indicates whether structure of workbook is locked. <br/>
            A value true indicates the structure of the workbook is locked. Worksheets in the workbook can't be Moved,
            deleted, hidden, unhidden, or Renamed, and new worksheets can't be inserted.<br/>
            A value of false indicates the structure of the workbook is not locked.<br/>
            
            @return true if structure of workbook is locked
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.IsWindowsLocked">
            Specifies a bool value that indicates whether the windows that comprise the workbook are locked. <br/>
            A value of true indicates the workbook windows are locked. Windows are the same size and position each time the
            workbook is opened.<br/>
            A value of false indicates the workbook windows are not locked.
            
            @return true if windows that comprise the workbook are locked
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.IsRevisionLocked">
            Specifies a bool value that indicates whether the workbook is locked for revisions.
            
            @return true if the workbook is locked for revisions.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.LockStructure">
            Locks the structure of workbook.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.UnlockStructure">
            Unlocks the structure of workbook.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.LockWindows">
            Locks the windows that comprise the workbook. 
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.UnlockWindows">
            Unlocks the windows that comprise the workbook. 
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.LockRevision">
            Locks the workbook for revisions.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.UnlockRevision">
            Unlocks the workbook for revisions.
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.GetUDFFinder">
            
             Returns the locator of user-defined functions.
             <p>
             The default instance : the built-in functions with the Excel Analysis Tool Pack.
             To Set / Evaluate custom functions you need to register them as follows:
            
            
            
             </p>
             @return wrapped instance of UDFFinder that allows seeking functions both by index and name
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.AddToolPack(NPOI.SS.Formula.Udf.UDFFinder)">
             Register a new toolpack in this workbook.
            
             @param toopack the toolpack to register
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.SetForceFormulaRecalculation(System.Boolean)">
             Whether the application shall perform a full recalculation when the workbook is opened.
             <p>
             Typically you want to force formula recalculation when you modify cell formulas or values
             of a workbook previously Created by Excel. When Set to true, this flag will tell Excel
             that it needs to recalculate all formulas in the workbook the next time the file is opened.
             </p>
             <p>
             Note, that recalculation updates cached formula results and, thus, modifies the workbook.
             Depending on the version, Excel may prompt you with "Do you want to save the Changes in <em>filename</em>?"
             on close.
             </p>
            
             @param value true if the application will perform a full recalculation of
             workbook values when the workbook is opened
             @since 3.8
        </member>
        <member name="M:NPOI.XSSF.UserModel.XSSFWorkbook.GetForceFormulaRecalculation">
             Whether Excel will be asked to recalculate all formulas when the  workbook is opened.
            
             @since 3.8
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFWorkbook.ActiveSheetIndex">
            Convenience method to Get the active sheet.  The active sheet is is the sheet
            which is currently displayed when the workbook is viewed in Excel.
            'Selected' sheet(s) is a distinct concept.
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFWorkbook.NumCellStyles">
             Get the number of styles the workbook Contains
            
             @return count of cell styles
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFWorkbook.NumberOfFonts">
             Get the number of fonts in the this workbook
            
             @return number of fonts
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFWorkbook.NumberOfNames">
             Get the number of named ranges in the this workbook
            
             @return number of named ranges
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFWorkbook.NumberOfSheets">
             Get the number of worksheets in the this workbook
            
             @return number of worksheets
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFWorkbook.MissingCellPolicy">
            Retrieves the current policy on what to do when
             Getting missing or blank cells from a row.
            The default is to return blank and null cells.
             {@link MissingCellPolicy}
        </member>
        <member name="P:NPOI.XSSF.UserModel.XSSFWorkbook.FirstVisibleTab">
             Gets the first tab that is displayed in the list of tabs in excel.
            
             @return integer that Contains the index to the active sheet in this book view.
        </member>
        <member name="T:NPOI.XSSF.Util.EvilUnclosedBRFixingInputStream">
            This is a seriously sick fix for the fact that some .xlsx
             files contain raw bits of HTML, without being escaped
             or properly turned into XML.
            The result is that they contain things like &gt;br&lt;,
             which breaks the XML parsing.
            This very sick InputStream wrapper attempts to spot
             these go past, and fix them.
            Only works for UTF-8 and US-ASCII based streams!
            It should only be used where experience Shows the problem
             can occur...
        </member>
        <member name="M:NPOI.XSSF.Util.EvilUnclosedBRFixingInputStream.Read">
            Warning - doesn't fix!
        </member>
        <member name="M:NPOI.XSSF.Util.EvilUnclosedBRFixingInputStream.ReadFromSpare(System.Byte[],System.Int32,System.Int32)">
            Reads into the buffer from the spare bytes
        </member>
        <member name="T:NPOI.XWPF.Extractor.XWPFWordExtractor">
            Helper class to extract text from an OOXML Word file
        </member>
        <member name="M:NPOI.XWPF.Extractor.XWPFWordExtractor.SetFetchHyperlinks(System.Boolean)">
            Should we also fetch the hyperlinks, when fetching 
             the text content? Default is to only output the
             hyperlink label, and not the contents
        </member>
        <member name="T:NPOI.XWPF.Model.XMLParagraph">
             Base class for XWPF paragraphs
            
             <AUTHOR> Batrakov (batrakov at gmail.com)
             
        </member>
        <member name="T:NPOI.XWPF.Model.XWPFCommentsDecorator">
             Decorator class for XWPFParagraph allowing to add comments 
             found in paragraph to its text
            
             <AUTHOR> Batrakov (batrakov at gmail.com)
             
        </member>
        <member name="T:NPOI.XWPF.Model.XWPFParagraphDecorator">
            Base decorator class for XWPFParagraph
        </member>
        <member name="T:NPOI.XWPF.Model.XWPFHeaderFooterPolicy">
            A .docx file can have no headers/footers, the same header/footer
             on each page, odd/even page footers, and optionally also 
             a different header/footer on the first page.
            This class handles sorting out what there is, and giving you
             the right headers and footers for the document.
        </member>
        <member name="M:NPOI.XWPF.Model.XWPFHeaderFooterPolicy.#ctor(NPOI.XWPF.UserModel.XWPFDocument)">
            Figures out the policy for the given document,
             and Creates any header and footer objects
             as required.
        </member>
        <member name="M:NPOI.XWPF.Model.XWPFHeaderFooterPolicy.#ctor(NPOI.XWPF.UserModel.XWPFDocument,NPOI.OpenXmlFormats.Wordprocessing.CT_SectPr)">
            Figures out the policy for the given document,
             and Creates any header and footer objects
             as required.
        </member>
        <member name="M:NPOI.XWPF.Model.XWPFHeaderFooterPolicy.buildHdrFtr(System.String,NPOI.XWPF.UserModel.XWPFParagraph[],NPOI.XWPF.UserModel.XWPFHeaderFooter)">
            MB 24 May 2010. Created this overloaded buildHdrFtr() method because testing demonstrated
            that the XWPFFooter or XWPFHeader object returned by calls to the CreateHeader(int, XWPFParagraph[])
            and CreateFooter(int, XWPFParagraph[]) methods or the GetXXXXXHeader/Footer methods where
            headers or footers had been Added to a document since it had been Created/opened, returned
            an object that Contained no XWPFParagraph objects even if the header/footer itself did contain
            text. The reason was that this line of code; CTHdrFtr ftr = CTHdrFtr.Factory.NewInstance(); 
            Created a brand new instance of the CTHDRFtr class which was then populated with data when
            it should have recovered the CTHdrFtr object encapsulated within the XWPFHeaderFooter object
            that had previoulsy been instantiated in the CreateHeader(int, XWPFParagraph[]) or 
            CreateFooter(int, XWPFParagraph[]) methods.
        </member>
        <member name="M:NPOI.XWPF.Model.XWPFHeaderFooterPolicy.GetOddPageHeader">
            Returns the odd page header. This is
             also the same as the default one...
        </member>
        <member name="M:NPOI.XWPF.Model.XWPFHeaderFooterPolicy.GetOddPageFooter">
            Returns the odd page footer. This is
             also the same as the default one...
        </member>
        <member name="M:NPOI.XWPF.Model.XWPFHeaderFooterPolicy.GetHeader(System.Int32)">
            Get the header that applies to the given
             (1 based) page.
            @param pageNumber The one based page number
        </member>
        <member name="M:NPOI.XWPF.Model.XWPFHeaderFooterPolicy.GetFooter(System.Int32)">
            Get the footer that applies to the given
             (1 based) page.
            @param pageNumber The one based page number
        </member>
        <member name="T:NPOI.XWPF.Model.XWPFHyperlinkDecorator">
            Decorator class for XWPFParagraph allowing to add hyperlinks 
             found in paragraph to its text.
             
            Note - Adds the hyperlink at the end, not in the right place...
             
            @deprecated Use {@link XWPFHyperlinkRun} instead
        </member>
        <member name="M:NPOI.XWPF.Model.XWPFHyperlinkDecorator.#ctor(NPOI.XWPF.Model.XWPFParagraphDecorator,System.Boolean)">
            @param nextDecorator The next decorator to use
            @param outputHyperlinkUrls Should we output the links too, or just the link text?
        </member>
        <member name="M:NPOI.XWPF.Model.XWPFHyperlinkDecorator.#ctor(NPOI.XWPF.UserModel.XWPFParagraph,NPOI.XWPF.Model.XWPFParagraphDecorator,System.Boolean)">
            @param prgrph The paragraph of text to work on
            @param outputHyperlinkUrls Should we output the links too, or just the link text?
        </member>
        <member name="T:NPOI.XWPF.UserModel.BodyElementType">
             <p>
             9 Jan 2010
             </p>
             <p>
             // TODO insert Javadoc here!
             </p>
             <AUTHOR>
            
        </member>
        <member name="T:NPOI.XWPF.UserModel.BodyType">
             <p>
             9 Jan 2010
             </p>
             <p>
             // TODO insert Javadoc here!
             </p>
             <AUTHOR>
            
        </member>
        <member name="T:NPOI.XWPF.UserModel.Borders">
             Specifies all types of borders which can be specified for WordProcessingML
             objects which have a border. Borders can be Separated into two types:
             <ul>
             <li> Line borders: which specify a pattern to be used when Drawing a line around the
             specified object.
             </li>
             <li> Art borders: which specify a repeated image to be used
             when Drawing a border around the specified object. Line borders may be
             specified on any object which allows a border, however, art borders may only
             be used as a border at the page level - the borders under the pgBorders
             element
            </li>
             </ul>
             <AUTHOR> Bronzetti
        </member>
        <member name="F:NPOI.XWPF.UserModel.Borders.SINGLE">
            Specifies a line border consisting of a single line around the parent
            object.
        </member>
        <member name="F:NPOI.XWPF.UserModel.Borders.APPLES">
            Specifies an art border consisting of a repeated image of an apple
        </member>
        <member name="F:NPOI.XWPF.UserModel.Borders.ARCHED_SCALLOPS">
            Specifies an art border consisting of a repeated image of a shell pattern
        </member>
        <member name="F:NPOI.XWPF.UserModel.Borders.BABY_PACIFIER">
            Specifies an art border consisting of a repeated image of a baby pacifier
        </member>
        <member name="F:NPOI.XWPF.UserModel.Borders.BABY_RATTLE">
            Specifies an art border consisting of a repeated image of a baby rattle
        </member>
        <member name="F:NPOI.XWPF.UserModel.Borders.BALLOONS_3_COLORS">
            Specifies an art border consisting of a repeated image of a Set of
            balloons
        </member>
        <member name="F:NPOI.XWPF.UserModel.Borders.BALLOONS_HOT_AIR">
            Specifies an art border consisting of a repeated image of a hot air
            balloon
        </member>
        <member name="F:NPOI.XWPF.UserModel.Borders.BASIC_BLACK_DASHES">
            Specifies an art border consisting of a repeating image of a black and
            white background.
        </member>
        <member name="F:NPOI.XWPF.UserModel.Borders.BASIC_BLACK_DOTS">
            Specifies an art border consisting of a repeating image of a black dot on
            a white background.
        </member>
        <member name="F:NPOI.XWPF.UserModel.Borders.BASIC_BLACK_SQUARES">
            Specifies an art border consisting of a repeating image of a black and
            white background
        </member>
        <member name="F:NPOI.XWPF.UserModel.Borders.BASIC_THIN_LINES">
            Specifies an art border consisting of a repeating image of a black and
            white background.
        </member>
        <member name="F:NPOI.XWPF.UserModel.Borders.BASIC_WHITE_DASHES">
            Specifies an art border consisting of a repeating image of a black and
            white background.
        </member>
        <member name="F:NPOI.XWPF.UserModel.Borders.BASIC_WHITE_DOTS">
            Specifies an art border consisting of a repeating image of a white dot on
            a black background.
        </member>
        <member name="F:NPOI.XWPF.UserModel.Borders.BASIC_WHITE_SQUARES">
            Specifies an art border consisting of a repeating image of a black and
            white background.
        </member>
        <member name="F:NPOI.XWPF.UserModel.Borders.BASIC_WIDE_INLINE">
            Specifies an art border consisting of a repeating image of a black and
            white background.
        </member>
        <member name="F:NPOI.XWPF.UserModel.Borders.BASIC_WIDE_MIDLINE">
            Specifies an art border consisting of a repeating image of a black and
            white background
        </member>
        <member name="F:NPOI.XWPF.UserModel.Borders.BASIC_WIDE_OUTLINE">
            Specifies an art border consisting of a repeating image of a black and
            white background
        </member>
        <member name="F:NPOI.XWPF.UserModel.Borders.BATS">
            Specifies an art border consisting of a repeated image of bats
        </member>
        <member name="F:NPOI.XWPF.UserModel.Borders.BIRDS">
            Specifies an art border consisting of repeating images of birds
        </member>
        <member name="F:NPOI.XWPF.UserModel.Borders.BIRDS_FLIGHT">
            Specifies an art border consisting of a repeated image of birds flying
        </member>
        <member name="F:NPOI.XWPF.UserModel.Borders.CABINS">
            Specifies an art border consisting of a repeated image of a cabin
        </member>
        <member name="F:NPOI.XWPF.UserModel.Borders.CAKE_SLICE">
            Specifies an art border consisting of a repeated image of a piece of cake
        </member>
        <member name="F:NPOI.XWPF.UserModel.Borders.CANDY_CORN">
            Specifies an art border consisting of a repeated image of candy corn
        </member>
        <member name="F:NPOI.XWPF.UserModel.Borders.CELTIC_KNOTWORK">
            Specifies an art border consisting of a repeated image of a knot work
            pattern
        </member>
        <member name="F:NPOI.XWPF.UserModel.Borders.CERTIFICATE_BANNER">
            Specifies an art border consisting of a banner.
            <p>
            If the border is on the left or right, no border is displayed.
            </p>
        </member>
        <member name="F:NPOI.XWPF.UserModel.Borders.CHAIN_LINK">
            Specifies an art border consisting of a repeating image of a chain link
            pattern.
        </member>
        <member name="F:NPOI.XWPF.UserModel.Borders.CHAMPAGNE_BOTTLE">
            Specifies an art border consisting of a repeated image of a champagne
            bottle
        </member>
        <member name="F:NPOI.XWPF.UserModel.Borders.CHECKED_BAR_BLACK">
            Specifies an art border consisting of repeating images of a compass
        </member>
        <member name="F:NPOI.XWPF.UserModel.Borders.CHECKED_BAR_COLOR">
            Specifies an art border consisting of a repeating image of a colored
            pattern.
        </member>
        <member name="F:NPOI.XWPF.UserModel.Borders.CHECKERED">
            Specifies an art border consisting of a repeated image of a Checkerboard
        </member>
        <member name="F:NPOI.XWPF.UserModel.Borders.CHRISTMAS_TREE">
            Specifies an art border consisting of a repeated image of a Christmas
            tree
        </member>
        <member name="F:NPOI.XWPF.UserModel.Borders.CIRCLES_LINES">
            Specifies an art border consisting of repeating images of lines and
            circles
        </member>
        <member name="F:NPOI.XWPF.UserModel.Borders.CIRCLES_RECTANGLES">
            Specifies an art border consisting of a repeated image of a rectangular
            pattern
        </member>
        <member name="F:NPOI.XWPF.UserModel.Borders.CLASSICAL_WAVE">
            Specifies an art border consisting of a repeated image of a wave
        </member>
        <member name="F:NPOI.XWPF.UserModel.Borders.CLOCKS">
            Specifies an art border consisting of a repeated image of a clock
        </member>
        <member name="F:NPOI.XWPF.UserModel.Borders.COMPASS">
            Specifies an art border consisting of repeating images of a compass
        </member>
        <member name="F:NPOI.XWPF.UserModel.Borders.CONFETTI">
            Specifies an art border consisting of a repeated image of confetti
        </member>
        <member name="F:NPOI.XWPF.UserModel.Borders.CONFETTI_GRAYS">
            Specifies an art border consisting of a repeated image of confetti
        </member>
        <member name="F:NPOI.XWPF.UserModel.Borders.CONFETTI_OUTLINE">
            Specifies an art border consisting of a repeated image of confetti
        </member>
        <member name="F:NPOI.XWPF.UserModel.Borders.CONFETTI_STREAMERS">
            Specifies an art border consisting of a repeated image of confetti
            streamers
        </member>
        <member name="F:NPOI.XWPF.UserModel.Borders.CONFETTI_WHITE">
            Specifies an art border consisting of a repeated image of confetti
        </member>
        <member name="F:NPOI.XWPF.UserModel.Borders.CORNER_TRIANGLES">
            Specifies an art border consisting of a repeated image
        </member>
        <member name="F:NPOI.XWPF.UserModel.Borders.COUPON_CUTOUT_DASHES">
            Specifies an art border consisting of a dashed line
        </member>
        <member name="F:NPOI.XWPF.UserModel.Borders.COUPON_CUTOUT_DOTS">
            Specifies an art border consisting of a dotted line
        </member>
        <member name="F:NPOI.XWPF.UserModel.Borders.CRAZY_MAZE">
            Specifies an art border consisting of a repeated image of a maze-like
            pattern
        </member>
        <member name="F:NPOI.XWPF.UserModel.Borders.CREATURES_BUTTERFLY">
            Specifies an art border consisting of a repeated image of a butterfly
        </member>
        <member name="F:NPOI.XWPF.UserModel.Borders.CREATURES_FISH">
            Specifies an art border consisting of a repeated image of a fish
        </member>
        <member name="F:NPOI.XWPF.UserModel.Borders.CREATURES_INSECTS">
            Specifies an art border consisting of repeating images of insects.
        </member>
        <member name="F:NPOI.XWPF.UserModel.Borders.CREATURES_LADY_BUG">
            Specifies an art border consisting of a repeated image of a ladybug
        </member>
        <member name="F:NPOI.XWPF.UserModel.Borders.CROSS_STITCH">
            Specifies an art border consisting of repeating images of a cross-stitch
            pattern
        </member>
        <member name="F:NPOI.XWPF.UserModel.Borders.CUP">
            Specifies an art border consisting of a repeated image of Cupid
        </member>
        <member name="T:NPOI.XWPF.UserModel.BreakClear">
            Specifies the Set of possible restart locations which may be used as to
            determine the next available line when a break's type attribute has a value
            of textWrapping.
            
            <AUTHOR> Bronzetti
        </member>
        <member name="F:NPOI.XWPF.UserModel.BreakClear.NONE">
            Specifies that the text wrapping break shall advance the text to the next
            line in the WordProcessingML document, regardless of its position left to
            right or the presence of any floating objects which intersect with the
            line,
            
            This is the Setting for a typical line break in a document.
        </member>
        <member name="F:NPOI.XWPF.UserModel.BreakClear.LEFT">
            Specifies that the text wrapping break shall behave as follows:
            <ul>
            <li> If this line is broken into multiple regions (a floating object in
            the center of the page has text wrapping on both sides:
            <ul>
            <li> If this is the leftmost region of text flow on this line, advance
            the text to the next position on the line </li>
            <li>Otherwise, treat this as a text wrapping break of type all. </li>
            </ul>
            </li>
            <li> If this line is not broken into multiple regions, then treat this
            break as a text wrapping break of type none. </li>
            </ul>
            <li> If the parent paragraph is right to left, then these behaviors are
            also reversed. </li>
        </member>
        <!-- 对于成员“F:NPOI.XWPF.UserModel.BreakClear.RIGHT”忽略有格式错误的 XML 注释 -->
        <member name="F:NPOI.XWPF.UserModel.BreakClear.ALL">
            Specifies that the text wrapping break shall advance the text to the next
            line in the WordProcessingML document which spans the full width of the
            line.
        </member>
        <member name="T:NPOI.XWPF.UserModel.BreakType">
            Specifies the possible types of break characters in a WordProcessingML
            document.
            The break type determines the next location where text shall be
            placed After this manual break is applied to the text contents
            
            <AUTHOR> Bronzetti
        </member>
        <member name="F:NPOI.XWPF.UserModel.BreakType.PAGE">
            Specifies that the current break shall restart itself on the next page of
            the document when the document is displayed in page view.
        </member>
        <member name="F:NPOI.XWPF.UserModel.BreakType.COLUMN">
            Specifies that the current break shall restart itself on the next column
            available on the current page when the document is displayed in page
            view.
            <p>
            If the current section is not divided into columns, or the column break
            occurs in the last column on the current page when displayed, then the
            restart location for text shall be the next page in the document.
            </p>
        </member>
        <member name="F:NPOI.XWPF.UserModel.BreakType.TEXTWRAPPING">
            Specifies that the current break shall restart itself on the next line in
            the document when the document is displayed in page view.
            The determine of the next line shall be done subject to the value of the clear
            attribute on the specified break character.
        </member>
        <member name="F:NPOI.XWPF.UserModel.PictureType.EMF">
            Extended windows meta file 
        </member>
        <member name="F:NPOI.XWPF.UserModel.PictureType.WMF">
            Windows Meta File 
        </member>
        <member name="F:NPOI.XWPF.UserModel.PictureType.PICT">
            Mac PICT format 
        </member>
        <member name="F:NPOI.XWPF.UserModel.PictureType.JPEG">
            JPEG format 
        </member>
        <member name="F:NPOI.XWPF.UserModel.PictureType.PNG">
            PNG format 
        </member>
        <member name="F:NPOI.XWPF.UserModel.PictureType.DIB">
            Device independent bitmap 
        </member>
        <member name="F:NPOI.XWPF.UserModel.PictureType.GIF">
            GIF image format 
        </member>
        <member name="F:NPOI.XWPF.UserModel.PictureType.TIFF">
            Tag Image File (.tiff)
        </member>
        <member name="F:NPOI.XWPF.UserModel.PictureType.EPS">
            Encapsulated Postscript (.eps)
        </member>
        <member name="F:NPOI.XWPF.UserModel.PictureType.BMP">
            Windows Bitmap (.bmp)
        </member>
        <member name="F:NPOI.XWPF.UserModel.PictureType.WPG">
            WordPerfect graphics (.wpg)
        </member>
        <member name="T:NPOI.XWPF.UserModel.IBody">
             <p>
             8 Jan 2010
             </p>
             <p>
             // This Interface represents an object, which is able to have a collection of paragraphs and tables
            	this can be XWFPDocument, XWPFHeader, XWPFFooter, XWPFTableCell
             </p>
             <AUTHOR> Epp
            
        </member>
        <member name="M:NPOI.XWPF.UserModel.IBody.GetPart">
            returns the Part, to which the body belongs, which you need for Adding relationship to other parts
            Actually it is needed of the class XWPFTableCell. Because you have to know to which part the tableCell
            belongs.
            @return the Part, to which the body belongs
        </member>
        <member name="M:NPOI.XWPF.UserModel.IBody.GetParagraph(NPOI.OpenXmlFormats.Wordprocessing.CT_P)">
            if there is a corresponding {@link XWPFParagraph} of the parameter ctTable in the paragraphList of this header or footer
            the method will return this paragraph
            if there is no corresponding {@link XWPFParagraph} the method will return null 
            @param p is instance of CTP and is searching for an XWPFParagraph
            @return null if there is no XWPFParagraph with an corresponding CTPparagraph in the paragraphList of this header or footer
            		   XWPFParagraph with the correspondig CTP p
        </member>
        <member name="M:NPOI.XWPF.UserModel.IBody.GetTable(NPOI.OpenXmlFormats.Wordprocessing.CT_Tbl)">
            if there is a corresponding {@link XWPFTable} of the parameter ctTable in the tableList of this header
            the method will return this table
            if there is no corresponding {@link XWPFTable} the method will return null 
            @param ctTable
        </member>
        <member name="M:NPOI.XWPF.UserModel.IBody.GetParagraphArray(System.Int32)">
            Returns the paragraph that of position pos
        </member>
        <member name="M:NPOI.XWPF.UserModel.IBody.GetTableArray(System.Int32)">
            Returns the table at position pos
        </member>
        <member name="M:NPOI.XWPF.UserModel.IBody.InsertNewParagraph(System.Xml.XmlDocument)">
            inserts a new paragraph at position of the cursor
             @param cursor
        </member>
        <member name="M:NPOI.XWPF.UserModel.IBody.InsertNewTbl(System.Xml.XmlDocument)">
            inserts a new Table at the cursor position.
            @param cursor
        </member>
        <member name="M:NPOI.XWPF.UserModel.IBody.InsertTable(System.Int32,NPOI.XWPF.UserModel.XWPFTable)">
            inserts a new Table at position pos
            @param pos
            @param table
        </member>
        <member name="M:NPOI.XWPF.UserModel.IBody.GetTableCell(NPOI.OpenXmlFormats.Wordprocessing.CT_Tc)">
            returns the TableCell to which the Table belongs
            @param cell
        </member>
        <member name="M:NPOI.XWPF.UserModel.IBody.GetXWPFDocument">
            Return XWPFDocument
        </member>
        <member name="P:NPOI.XWPF.UserModel.IBody.PartType">
            Get the PartType of the body, for example
            DOCUMENT, HEADER, FOOTER,	FOOTNOTE, 
            @return the PartType of the body
        </member>
        <member name="P:NPOI.XWPF.UserModel.IBody.BodyElements">
            Returns an Iterator with paragraphs and tables, 
             in the order that they occur in the text.
        </member>
        <member name="P:NPOI.XWPF.UserModel.IBody.Paragraphs">
            Returns the paragraph(s) that holds
             the text of the header or footer.
        </member>
        <member name="P:NPOI.XWPF.UserModel.IBody.Tables">
            Return the table(s) that holds the text
             of the IBodyPart, for complex cases
             where a paragraph isn't used.
        </member>
        <member name="T:NPOI.XWPF.UserModel.IBodyElement">
             9 Jan 2010
             <AUTHOR> Epp
            
        </member>
        <member name="T:NPOI.XWPF.UserModel.LineSpacingRule">
            Specifies the logic which shall be used to calculate the line spacing of the
            parent object when it is displayed in the document.
            
            <AUTHOR> Bronzetti
        </member>
        <member name="F:NPOI.XWPF.UserModel.LineSpacingRule.AUTO">
            Specifies that the line spacing of the parent object shall be
            automatically determined by the size of its contents, with no
            predetermined minimum or maximum size.
        </member>
        <member name="F:NPOI.XWPF.UserModel.LineSpacingRule.EXACT">
            Specifies that the height of the line shall be exactly the value
            specified, regardless of the size of the contents If the contents are too
            large for the specified height, then they shall be clipped as necessary.
        </member>
        <member name="F:NPOI.XWPF.UserModel.LineSpacingRule.ATLEAST">
            Specifies that the height of the line shall be at least the value
            specified, but may be expanded to fit its content as needed.
        </member>
        <member name="T:NPOI.XWPF.UserModel.ParagraphAlignment">
             Specifies all types of alignment which are available to be applied to objects in a
             WordProcessingML document
            
             <AUTHOR> Kozlov
        </member>
        <member name="T:NPOI.XWPF.UserModel.PositionInParagraph">
                 * postion of a character in a paragrapho
                * 1st RunPositon
                * 2nd TextPosition
                * 3rd CharacterPosition 
                * 
                *
        </member>
        <member name="T:NPOI.XWPF.UserModel.TextAlignment">
            Specifies all types of vertical alignment which are available to be applied to of all text 
            on each line displayed within a paragraph.
            
            <AUTHOR> Bronzetti
        </member>
        <member name="F:NPOI.XWPF.UserModel.TextAlignment.TOP">
            Specifies that all text in the parent object shall be 
            aligned to the top of each character when displayed
        </member>
        <member name="F:NPOI.XWPF.UserModel.TextAlignment.CENTER">
            Specifies that all text in the parent object shall be 
            aligned to the center of each character when displayed.
        </member>
        <member name="F:NPOI.XWPF.UserModel.TextAlignment.BASELINE">
            Specifies that all text in the parent object shall be
            aligned to the baseline of each character when displayed.
        </member>
        <member name="F:NPOI.XWPF.UserModel.TextAlignment.BOTTOM">
            Specifies that all text in the parent object shall be
            aligned to the bottom of each character when displayed.
        </member>
        <member name="F:NPOI.XWPF.UserModel.TextAlignment.AUTO">
            Specifies that all text in the parent object shall be 
            aligned automatically when displayed.
        </member>
        <member name="T:NPOI.XWPF.UserModel.TextSegement">
            saves the begin and end position  of a text in a Paragraph
        </member>
        <member name="P:NPOI.XWPF.UserModel.TextSegement.BeginRun">
            <summary>
            The index of the start run
            </summary>
        </member>
        <member name="P:NPOI.XWPF.UserModel.TextSegement.BeginText">
            <summary>
            The index of the start CT_Text
            </summary>
        </member>
        <member name="P:NPOI.XWPF.UserModel.TextSegement.BeginChar">
            <summary>
            The index of the start text character
            </summary>
        </member>
        <member name="P:NPOI.XWPF.UserModel.TextSegement.EndText">
            <summary>
            the index of the end CT_Text 
            </summary>
        </member>
        <member name="P:NPOI.XWPF.UserModel.TextSegement.EndChar">
            <summary>
            the index of the end text character
            </summary>
        </member>
        <member name="T:NPOI.XWPF.UserModel.UnderlinePatterns">
            Specifies the types of patterns which may be used to create the underline
            applied beneath the text in a Run.
            
            <AUTHOR> Bronzetti
        </member>
        <member name="F:NPOI.XWPF.UserModel.UnderlinePatterns.Single">
            Specifies an underline consisting of a single line beneath all characters
            in this Run.
        </member>
        <member name="F:NPOI.XWPF.UserModel.UnderlinePatterns.Words">
            Specifies an underline consisting of a single line beneath all non-space
            characters in the Run. There shall be no underline beneath any space
            character (breaking or non-breaking).
        </member>
        <member name="F:NPOI.XWPF.UserModel.UnderlinePatterns.Double">
            Specifies an underline consisting of two lines beneath all characters in
            this run
        </member>
        <member name="F:NPOI.XWPF.UserModel.UnderlinePatterns.Thick">
            Specifies an underline consisting of a single thick line beneath all
            characters in this Run.
        </member>
        <member name="F:NPOI.XWPF.UserModel.UnderlinePatterns.Dotted">
            Specifies an underline consisting of a series of dot characters beneath
            all characters in this Run.
        </member>
        <member name="F:NPOI.XWPF.UserModel.UnderlinePatterns.DottedHeavy">
            Specifies an underline consisting of a series of thick dot characters
            beneath all characters in this Run.
        </member>
        <member name="F:NPOI.XWPF.UserModel.UnderlinePatterns.Dash">
            Specifies an underline consisting of a dashed line beneath all characters
            in this Run.
        </member>
        <member name="F:NPOI.XWPF.UserModel.UnderlinePatterns.DashedHeavy">
            Specifies an underline consisting of a series of thick dashes beneath all
            characters in this Run.
        </member>
        <member name="F:NPOI.XWPF.UserModel.UnderlinePatterns.DashLong">
            Specifies an underline consisting of long dashed characters beneath all
            characters in this Run.
        </member>
        <member name="F:NPOI.XWPF.UserModel.UnderlinePatterns.DashLongHeavy">
            Specifies an underline consisting of thick long dashed characters beneath
            all characters in this Run.
        </member>
        <member name="F:NPOI.XWPF.UserModel.UnderlinePatterns.DotDash">
            Specifies an underline consisting of a series of dash, dot characters
            beneath all characters in this Run.
        </member>
        <member name="F:NPOI.XWPF.UserModel.UnderlinePatterns.DashDotHeavy">
            Specifies an underline consisting of a series of thick dash, dot
            characters beneath all characters in this Run.
        </member>
        <member name="F:NPOI.XWPF.UserModel.UnderlinePatterns.DotDotDash">
            Specifies an underline consisting of a series of dash, dot, dot
            characters beneath all characters in this Run.
        </member>
        <member name="F:NPOI.XWPF.UserModel.UnderlinePatterns.DashDotDotHeavy">
            Specifies an underline consisting of a series of thick dash, dot, dot
            characters beneath all characters in this Run.
        </member>
        <member name="F:NPOI.XWPF.UserModel.UnderlinePatterns.Wave">
            Specifies an underline consisting of a single wavy line beneath all
            characters in this Run.
        </member>
        <member name="F:NPOI.XWPF.UserModel.UnderlinePatterns.WavyHeavy">
            Specifies an underline consisting of a single thick wavy line beneath all
            characters in this Run.
        </member>
        <member name="F:NPOI.XWPF.UserModel.UnderlinePatterns.WavyDouble">
            Specifies an underline consisting of a pair of wavy lines beneath all
            characters in this Run.
        </member>
        <member name="F:NPOI.XWPF.UserModel.UnderlinePatterns.None">
            Specifies no underline beneath this Run.
        </member>
        <member name="T:NPOI.XWPF.UserModel.VerticalAlign">
            Specifies possible values for the alignment of the contents of this run in
            relation to the default appearance of the Run's text. This allows the text to
            be repositioned as subscript or superscript without altering the font size of
            the run properties.
            
            <AUTHOR> Bronzetti
        </member>
        <member name="F:NPOI.XWPF.UserModel.VerticalAlign.BASELINE">
            Specifies that the text in the parent run shall be located at the
            baseline and presented in the same size as surrounding text.
        </member>
        <member name="F:NPOI.XWPF.UserModel.VerticalAlign.SUPERSCRIPT">
            Specifies that this text should be subscript. This Setting shall lower
            the text in this run below the baseline and change it to a smaller size,
            if a smaller size is available.
        </member>
        <member name="F:NPOI.XWPF.UserModel.VerticalAlign.SUBSCRIPT">
            Specifies that this text should be superscript. This Setting shall raise
            the text in this run above the baseline and change it to a smaller size,
            if a smaller size is available.
        </member>
        <member name="T:NPOI.XWPF.UserModel.XWPFAbstractNum">
             <AUTHOR> Epp
            
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFAbstractNum.MultiLevelType">
            <summary>
            Abstract Numbering Definition Type
            </summary>
        </member>
        <member name="T:NPOI.XWPF.UserModel.MultiLevelType">
            <summary>
            Numbering Definition Type
            </summary>
        </member>
        <member name="F:NPOI.XWPF.UserModel.MultiLevelType.SingleLevel">
            <summary>
            Single Level Numbering Definition
            </summary>
        </member>
        <member name="F:NPOI.XWPF.UserModel.MultiLevelType.Multilevel">
            <summary>
            Multilevel Numbering Definition
            </summary>
        </member>
        <member name="F:NPOI.XWPF.UserModel.MultiLevelType.HybridMultilevel">
            <summary>
            Hybrid Multilevel Numbering Definition
            </summary>
        </member>
        <member name="T:NPOI.XWPF.UserModel.NumberFormat">
            <summary>
            Numbering Format
            </summary>
        </member>
        <member name="F:NPOI.XWPF.UserModel.NumberFormat.Decimal">
            <summary>
            Decimal Numbers
            </summary>
        </member>
        <member name="F:NPOI.XWPF.UserModel.NumberFormat.UpperRoman">
            <summary>
            Uppercase Roman Numerals
            </summary>
        </member>
        <member name="F:NPOI.XWPF.UserModel.NumberFormat.LowerRoman">
            <summary>
            Lowercase Roman Numerals
            </summary>
        </member>
        <member name="F:NPOI.XWPF.UserModel.NumberFormat.UpperLetter">
            <summary>
            Uppercase Latin Alphabet
            </summary>
        </member>
        <member name="F:NPOI.XWPF.UserModel.NumberFormat.LowerLetter">
            <summary>
            Lowercase Latin Alphabet
            </summary>
        </member>
        <member name="F:NPOI.XWPF.UserModel.NumberFormat.Ordinal">
            <summary>
            Ordinal
            </summary>
        </member>
        <member name="F:NPOI.XWPF.UserModel.NumberFormat.CardinalText">
            <summary>
            Cardinal Text
            </summary>
        </member>
        <member name="F:NPOI.XWPF.UserModel.NumberFormat.OrdinalText">
            <summary>
            Ordinal Text
            </summary>
        </member>
        <member name="F:NPOI.XWPF.UserModel.NumberFormat.Hex">
            <summary>
            Hexadecimal Numbering
            </summary>
        </member>
        <member name="F:NPOI.XWPF.UserModel.NumberFormat.Chicago">
            <summary>
            Chicago Manual of Style
            </summary>
        </member>
        <member name="F:NPOI.XWPF.UserModel.NumberFormat.IdeographDigital">
            <summary>
            Ideographs
            </summary>
        </member>
        <member name="F:NPOI.XWPF.UserModel.NumberFormat.JapaneseCounting">
            <summary>
            Japanese Counting System
            </summary>
        </member>
        <member name="F:NPOI.XWPF.UserModel.NumberFormat.Aiueo">
            <summary>
            AIUEO Order Hiragana
            </summary>
        </member>
        <member name="F:NPOI.XWPF.UserModel.NumberFormat.Iroha">
            <summary>
            Iroha Ordered Katakana
            </summary>
        </member>
        <member name="F:NPOI.XWPF.UserModel.NumberFormat.DecimalFullWidth">
            <summary>
            Double Byte Arabic Numerals
            </summary>
        </member>
        <member name="F:NPOI.XWPF.UserModel.NumberFormat.DecimalHalfWidth">
            <summary>
            Single Byte Arabic Numerals
            </summary>
        </member>
        <member name="F:NPOI.XWPF.UserModel.NumberFormat.JapaneseLegal">
            <summary>
            Japanese Legal Numbering
            </summary>
        </member>
        <member name="F:NPOI.XWPF.UserModel.NumberFormat.JapaneseDigitalTenThousand">
            <summary>
            Japanese Digital Ten Thousand Counting System
            </summary>
        </member>
        <member name="F:NPOI.XWPF.UserModel.NumberFormat.DecimalEnclosedCircle">
            <summary>
            Decimal Numbers Enclosed in a Circle
            </summary>
        </member>
        <member name="F:NPOI.XWPF.UserModel.NumberFormat.DecimalFullWidth2">
            <summary>
            Double Byte Arabic Numerals Alternate
            </summary>
        </member>
        <member name="F:NPOI.XWPF.UserModel.NumberFormat.AiueoFullWidth">
            <summary>
            Full-Width AIUEO Order Hiragana
            </summary>
        </member>
        <member name="F:NPOI.XWPF.UserModel.NumberFormat.IrohaFullWidth">
            <summary>
            Full-Width Iroha Ordered Katakana
            </summary>
        </member>
        <member name="F:NPOI.XWPF.UserModel.NumberFormat.DecimalZero">
            <summary>
            Initial Zero Arabic Numerals
            </summary>
        </member>
        <member name="F:NPOI.XWPF.UserModel.NumberFormat.Bullet">
            <summary>
            Bullet
            </summary>
        </member>
        <member name="F:NPOI.XWPF.UserModel.NumberFormat.Ganada">
            <summary>
            Korean Ganada Numbering
            </summary>
        </member>
        <member name="F:NPOI.XWPF.UserModel.NumberFormat.Chosung">
            <summary>
            Korean Chosung Numbering
            </summary>
        </member>
        <member name="F:NPOI.XWPF.UserModel.NumberFormat.DecimalEnclosedFullstop">
            <summary>
            Decimal Numbers Followed by a Period
            </summary>
        </member>
        <member name="F:NPOI.XWPF.UserModel.NumberFormat.DecimalEnclosedParen">
            <summary>
            Decimal Numbers Enclosed in Parenthesis
            </summary>
        </member>
        <member name="F:NPOI.XWPF.UserModel.NumberFormat.DecimalEnclosedCircleChinese">
            <summary>
            Decimal Numbers Enclosed in a Circle
            </summary>
        </member>
        <member name="F:NPOI.XWPF.UserModel.NumberFormat.IdeographEnclosedCircle">
            <summary>
            Ideographs Enclosed in a Circle
            </summary>
        </member>
        <member name="F:NPOI.XWPF.UserModel.NumberFormat.IdeographTraditional">
            <summary>
            Traditional Ideograph Format
            </summary>
        </member>
        <member name="F:NPOI.XWPF.UserModel.NumberFormat.IdeographZodiac">
            <summary>
            Zodiac Ideograph Format
            </summary>
        </member>
        <member name="F:NPOI.XWPF.UserModel.NumberFormat.IdeographZodiacTraditional">
            <summary>
            Traditional Zodiac Ideograph Format
            </summary>
        </member>
        <member name="F:NPOI.XWPF.UserModel.NumberFormat.TaiwaneseCounting">
            <summary>
            Taiwanese Counting System
            </summary>
        </member>
        <member name="F:NPOI.XWPF.UserModel.NumberFormat.IdeographLegalTraditional">
            <summary>
            Traditional Legal Ideograph Format
            </summary>
        </member>
        <member name="F:NPOI.XWPF.UserModel.NumberFormat.TaiwaneseCountingThousand">
            <summary>
            Taiwanese Counting Thousand System
            </summary>
        </member>
        <member name="F:NPOI.XWPF.UserModel.NumberFormat.TaiwaneseDigital">
            <summary>
            Taiwanese Digital Counting System
            </summary>
        </member>
        <member name="F:NPOI.XWPF.UserModel.NumberFormat.ChineseCounting">
            <summary>
            Chinese Counting System
            </summary>
        </member>
        <member name="F:NPOI.XWPF.UserModel.NumberFormat.ChineseLegalSimplified">
            <summary>
            Chinese Legal Simplified Format
            </summary>
        </member>
        <member name="F:NPOI.XWPF.UserModel.NumberFormat.ChineseCountingThousand">
            <summary>
            Chinese Counting Thousand System
            </summary>
        </member>
        <member name="F:NPOI.XWPF.UserModel.NumberFormat.KoreanDigital">
            <summary>
            Korean Digital Counting System
            </summary>
        </member>
        <member name="F:NPOI.XWPF.UserModel.NumberFormat.KoreanCounting">
            <summary>
            Korean Counting System
            </summary>
        </member>
        <member name="F:NPOI.XWPF.UserModel.NumberFormat.KoreanLegal">
            <summary>
            Korean Legal Numbering
            </summary>
        </member>
        <member name="F:NPOI.XWPF.UserModel.NumberFormat.KoreanDigital2">
            <summary>
            Korean Digital Counting System Alternate
            </summary>
        </member>
        <member name="F:NPOI.XWPF.UserModel.NumberFormat.VietnameseCounting">
            <summary>
            Vietnamese Numerals
            </summary>
        </member>
        <member name="F:NPOI.XWPF.UserModel.NumberFormat.RussianLower">
            <summary>
            Lowercase Russian Alphabet
            </summary>
        </member>
        <member name="F:NPOI.XWPF.UserModel.NumberFormat.RussianUpper">
            <summary>
            Uppercase Russian Alphabet
            </summary>
        </member>
        <member name="F:NPOI.XWPF.UserModel.NumberFormat.None">
            <summary>
            No Numbering
            </summary>
        </member>
        <member name="F:NPOI.XWPF.UserModel.NumberFormat.NumberInDash">
            <summary>
            Number With Dashes
            </summary>
        </member>
        <member name="F:NPOI.XWPF.UserModel.NumberFormat.Hebrew1">
            <summary>
            Hebrew Numerals
            </summary>
        </member>
        <member name="F:NPOI.XWPF.UserModel.NumberFormat.Hebrew2">
            <summary>
            Hebrew Alphabet
            </summary>
        </member>
        <member name="F:NPOI.XWPF.UserModel.NumberFormat.ArabicAlpha">
            <summary>
            Arabic Alphabet
            </summary>
        </member>
        <member name="F:NPOI.XWPF.UserModel.NumberFormat.ArabicAbjad">
            <summary>
            Arabic Abjad Numerals
            </summary>
        </member>
        <member name="F:NPOI.XWPF.UserModel.NumberFormat.HindiVowels">
            <summary>
            Hindi Vowels
            </summary>
        </member>
        <member name="F:NPOI.XWPF.UserModel.NumberFormat.HindiConsonants">
            <summary>
            Hindi Consonants
            </summary>
        </member>
        <member name="F:NPOI.XWPF.UserModel.NumberFormat.HindiNumbers">
            <summary>
            Hindi Numbers
            </summary>
        </member>
        <member name="F:NPOI.XWPF.UserModel.NumberFormat.HindiCounting">
            <summary>
            Hindi Counting System
            </summary>
        </member>
        <member name="F:NPOI.XWPF.UserModel.NumberFormat.ThaiLetters">
            <summary>
            Thai Letters
            </summary>
        </member>
        <member name="F:NPOI.XWPF.UserModel.NumberFormat.ThaiNumbers">
            <summary>
            Thai Numerals
            </summary>
        </member>
        <member name="F:NPOI.XWPF.UserModel.NumberFormat.ThaiCounting">
            <summary>
            Thai Counting System
            </summary>
        </member>
        <member name="T:NPOI.XWPF.UserModel.XWPFComment">
                 * Sketch of XWPF comment class
                 * 
                * <AUTHOR> Batrakov (batrakov at gmail.com)
                 * 
        </member>
        <member name="T:NPOI.XWPF.UserModel.XWPFDocument">
             Experimental class to do low level Processing
              of docx files.
            
             If you're using these low level classes, then you
              will almost certainly need to refer to the OOXML
              specifications from
              http://www.ecma-international.org/publications/standards/Ecma-376.htm
            
             WARNING - APIs expected to change rapidly
        </member>
        <member name="F:NPOI.XWPF.UserModel.XWPFDocument.drawingIdManager">
            Keeps track on all id-values used in this document and included parts, like headers, footers, etc.
        </member>
        <member name="F:NPOI.XWPF.UserModel.XWPFDocument.headerFooterPolicy">
            Handles the joy of different headers/footers for different pages 
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFDocument.NewPackage">
            Create a new WordProcessingML package and Setup the default minimal content
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFDocument.OnDocumentCreate">
            Create a new CT_Document with all values Set to default
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFDocument.GetTableArray(System.Int32)">
            @see NPOI.XWPF.UserModel.IBody#getTableArray(int)
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFDocument.GetPartById(System.String)">
            Get the document part that's defined as the
             given relationship of the core document.
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFDocument.GetHeaderFooterPolicy">
            Returns the policy on headers and footers, which
             also provides a way to Get at them.
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFDocument.GetCTStyle">
            Returns the styles object used
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFDocument.GetAllEmbedds">
            Get the document's embedded files.
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFDocument.GetBodyElementSpecificPos(System.Int32,System.Collections.Generic.List{NPOI.XWPF.UserModel.IBodyElement})">
            Finds that for example the 2nd entry in the body list is the 1st paragraph
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFDocument.GetParagraphPos(System.Int32)">
            Look up the paragraph at the specified position in the body elemnts list
            and return this paragraphs position in the paragraphs list
            
            @param pos
                       The position of the relevant paragraph in the body elements
                       list
            @return the position of the paragraph in the paragraphs list, if there is
                    a paragraph at the position in the bodyelements list. Else it
                    will return -1
            
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFDocument.GetTablePos(System.Int32)">
            Get with the position of a table in the bodyelement array list 
            the position of this table in the table array list
            @param pos position of the table in the bodyelement array list
            @return if there is a table at the position in the bodyelement array list,
            		   else it will return null. 
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFDocument.InsertNewParagraph(System.Xml.XmlDocument)">
            Add a new paragraph at position of the cursor. The cursor must be on the
            {@link org.apache.xmlbeans.XmlCursor.TokenType#START} tag of an subelement
            of the documents body. When this method is done, the cursor passed as
            parameter points to the {@link org.apache.xmlbeans.XmlCursor.TokenType#END}
            of the newly inserted paragraph.
            
            @param cursor
            @return the {@link XWPFParagraph} object representing the newly inserted
                    CTP object
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFDocument.IsCursorInBody(System.Xml.XmlDocument)">
            verifies that cursor is on the right position
            @param cursor
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFDocument.GetPosOfParagraph(NPOI.XWPF.UserModel.XWPFParagraph)">
            Get the position of the paragraph, within the list
             of all the body elements.
            @param p The paragraph to find
            @return The location, or -1 if the paragraph couldn't be found 
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFDocument.GetPosOfTable(NPOI.XWPF.UserModel.XWPFTable)">
            Get the position of the table, within the list of
             all the body elements.
            @param t The table to find
            @return The location, or -1 if the table couldn't be found
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFDocument.Commit">
            Commit and saves the document
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFDocument.GetRelationIndex(NPOI.XWPF.UserModel.XWPFRelation)">
            Gets the index of the relation we're trying to create
            @param relation
            @return i
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFDocument.CreateParagraph">
            Appends a new paragraph to this document
            @return a new paragraph
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFDocument.CreateNumbering">
            Creates an empty numbering if one does not already exist and Sets the numbering member
            @return numbering
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFDocument.CreateStyles">
            Creates an empty styles for the document if one does not already exist
            @return styles
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFDocument.CreateFootnotes">
            Creates an empty footnotes element for the document if one does not already exist
            @return footnotes
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFDocument.RemoveBodyElement(System.Int32)">
            remove a BodyElement from bodyElements array list 
            @param pos
            @return true if removing was successfully, else return false
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFDocument.SetParagraph(NPOI.XWPF.UserModel.XWPFParagraph,System.Int32)">
            copies content of a paragraph to a existing paragraph in the list paragraphs at position pos
            @param paragraph
            @param pos
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFDocument.GetLastParagraph">
            @return the LastParagraph of the document
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFDocument.CreateTable">
            Create an empty table with one row and one column as default.
            @return a new table
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFDocument.CreateTable(System.Int32,System.Int32)">
            Create an empty table with a number of rows and cols specified
            @param rows
            @param cols
            @return table
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFDocument.CreateTOC">
            
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFDocument.SetTable(System.Int32,NPOI.XWPF.UserModel.XWPFTable)">
            Replace content of table in array tables at position pos with a
            @param pos
            @param table
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFDocument.IsEnforcedReadonlyProtection">
            Verifies that the documentProtection tag in Settings.xml file <br/>
            specifies that the protection is enforced (w:enforcement="1") <br/>
            and that the kind of protection is ReadOnly (w:edit="readOnly")<br/>
            <br/>
            sample snippet from Settings.xml
            <pre>
                &lt;w:settings  ... &gt;
                    &lt;w:documentProtection w:edit=&quot;readOnly&quot; w:enforcement=&quot;1&quot;/&gt;
            </pre>
            
            @return true if documentProtection is enforced with option ReadOnly
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFDocument.IsEnforcedFillingFormsProtection">
            Verifies that the documentProtection tag in Settings.xml file <br/>
            specifies that the protection is enforced (w:enforcement="1") <br/>
            and that the kind of protection is forms (w:edit="forms")<br/>
            <br/>
            sample snippet from Settings.xml
            <pre>
                &lt;w:settings  ... &gt;
                    &lt;w:documentProtection w:edit=&quot;forms&quot; w:enforcement=&quot;1&quot;/&gt;
            </pre>
            
            @return true if documentProtection is enforced with option forms
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFDocument.IsEnforcedCommentsProtection">
            Verifies that the documentProtection tag in Settings.xml file <br/>
            specifies that the protection is enforced (w:enforcement="1") <br/>
            and that the kind of protection is comments (w:edit="comments")<br/>
            <br/>
            sample snippet from Settings.xml
            <pre>
                &lt;w:settings  ... &gt;
                    &lt;w:documentProtection w:edit=&quot;comments&quot; w:enforcement=&quot;1&quot;/&gt;
            </pre>
            
            @return true if documentProtection is enforced with option comments
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFDocument.IsEnforcedTrackedChangesProtection">
            Verifies that the documentProtection tag in Settings.xml file <br/>
            specifies that the protection is enforced (w:enforcement="1") <br/>
            and that the kind of protection is trackedChanges (w:edit="trackedChanges")<br/>
            <br/>
            sample snippet from Settings.xml
            <pre>
                &lt;w:settings  ... &gt;
                    &lt;w:documentProtection w:edit=&quot;trackedChanges&quot; w:enforcement=&quot;1&quot;/&gt;
            </pre>
            
            @return true if documentProtection is enforced with option trackedChanges
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFDocument.EnforceReadonlyProtection">
            Enforces the ReadOnly protection.<br/>
            In the documentProtection tag inside Settings.xml file, <br/>
            it Sets the value of enforcement to "1" (w:enforcement="1") <br/>
            and the value of edit to ReadOnly (w:edit="readOnly")<br/>
            <br/>
            sample snippet from Settings.xml
            <pre>
                &lt;w:settings  ... &gt;
                    &lt;w:documentProtection w:edit=&quot;readOnly&quot; w:enforcement=&quot;1&quot;/&gt;
            </pre>
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFDocument.EnforceFillingFormsProtection">
            Enforce the Filling Forms protection.<br/>
            In the documentProtection tag inside Settings.xml file, <br/>
            it Sets the value of enforcement to "1" (w:enforcement="1") <br/>
            and the value of edit to forms (w:edit="forms")<br/>
            <br/>
            sample snippet from Settings.xml
            <pre>
                &lt;w:settings  ... &gt;
                    &lt;w:documentProtection w:edit=&quot;forms&quot; w:enforcement=&quot;1&quot;/&gt;
            </pre>
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFDocument.EnforceCommentsProtection">
            Enforce the Comments protection.<br/>
            In the documentProtection tag inside Settings.xml file,<br/>
            it Sets the value of enforcement to "1" (w:enforcement="1") <br/>
            and the value of edit to comments (w:edit="comments")<br/>
            <br/>
            sample snippet from Settings.xml
            <pre>
                &lt;w:settings  ... &gt;
                    &lt;w:documentProtection w:edit=&quot;comments&quot; w:enforcement=&quot;1&quot;/&gt;
            </pre>
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFDocument.EnforceTrackedChangesProtection">
            Enforce the Tracked Changes protection.<br/>
            In the documentProtection tag inside Settings.xml file, <br/>
            it Sets the value of enforcement to "1" (w:enforcement="1") <br/>
            and the value of edit to trackedChanges (w:edit="trackedChanges")<br/>
            <br/>
            sample snippet from Settings.xml
            <pre>
                &lt;w:settings  ... &gt;
                    &lt;w:documentProtection w:edit=&quot;trackedChanges&quot; w:enforcement=&quot;1&quot;/&gt;
            </pre>
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFDocument.RemoveProtectionEnforcement">
            Remove protection enforcement.<br/>
            In the documentProtection tag inside Settings.xml file <br/>
            it Sets the value of enforcement to "0" (w:enforcement="0") <br/>
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFDocument.EnforceUpdateFields">
            Enforces fields update on document open (in Word).
            In the settings.xml file <br/>
            sets the updateSettings value to true (w:updateSettings w:val="true")
            
             NOTICES:
             <ul>
             	<li>Causing Word to ask on open: "This document contains fields that may refer to other files. Do you want to update the fields in this document?"
                      (if "Update automatic links at open" is enabled)</li>
             	<li>Flag is removed after saving with changes in Word </li>
             </ul> 
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFDocument.InsertTable(System.Int32,NPOI.XWPF.UserModel.XWPFTable)">
            inserts an existing XWPFTable to the arrays bodyElements and tables
            @param pos
            @param table
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFDocument.GetNextPicNameNumber(System.Int32)">
            Get the next free ImageNumber
            @param format
            @return the next free ImageNumber
            @throws InvalidFormatException 
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFDocument.GetPictureDataByID(System.String)">
            returns the PictureData by blipID
            @param blipID
            @return XWPFPictureData of a specificID
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFDocument.GetNumbering">
            GetNumbering
            @return numbering
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFDocument.GetStyles">
            Get Styles
            @return styles for this document
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFDocument.GetParagraph(NPOI.OpenXmlFormats.Wordprocessing.CT_P)">
            Get the paragraph with the CTP class p
            
            @param p
            @return the paragraph with the CTP class p
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFDocument.GetTable(NPOI.OpenXmlFormats.Wordprocessing.CT_Tbl)">
            Get a table by its CTTbl-Object
            @param ctTbl
            @see NPOI.XWPF.UserModel.IBody#getTable(org.Openxmlformats.schemas.wordProcessingml.x2006.main.CTTbl)
            @return a table by its CTTbl-Object or null
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFDocument.GetParagraphArray(System.Int32)">
            Returns the paragraph that of position pos
            @see NPOI.XWPF.UserModel.IBody#getParagraphArray(int)
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFDocument.GetPart">
            returns the Part, to which the body belongs, which you need for Adding relationship to other parts
            Actually it is needed of the class XWPFTableCell. Because you have to know to which part the tableCell
            belongs.
            @see NPOI.XWPF.UserModel.IBody#getPart()
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFDocument.GetTableCell(NPOI.OpenXmlFormats.Wordprocessing.CT_Tc)">
            Get the TableCell which belongs to the TableCell
            @param cell
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFDocument.Document">
            Returns the low level document base object
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFDocument.BodyElements">
            returns an Iterator with paragraphs and tables
            @see NPOI.XWPF.UserModel.IBody#getBodyElements()
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFDocument.Paragraphs">
            @see NPOI.XWPF.UserModel.IBody#getParagraphs()
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFDocument.Tables">
            @see NPOI.XWPF.UserModel.IBody#getTables()
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFDocument.FooterList">
            
            @return  the list of footers
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFDocument.HeaderList">
            
            @return  the list of headers
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFDocument.AllPictures">
            Returns all Pictures, which are referenced from the document itself.
            @return a {@link List} of {@link XWPFPictureData}. The returned {@link List} is unmodifiable. Use #a
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFDocument.AllPackagePictures">
            @return all Pictures in this package
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFDocument.PartType">
             Get the PartType of the body, for example
             DOCUMENT, HEADER, FOOTER,	FOOTNOTE,
            
             @see NPOI.XWPF.UserModel.IBody#getPartType()
        </member>
        <member name="T:NPOI.XWPF.UserModel.XWPFFactory">
            <AUTHOR> Kozlov
        </member>
        <member name="T:NPOI.XWPF.UserModel.XWPFFooter">
            Sketch of XWPF footer class
        </member>
        <member name="T:NPOI.XWPF.UserModel.XWPFHeaderFooter">
            Parent of XWPF headers and footers
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFHeaderFooter.SetHeaderFooter(NPOI.OpenXmlFormats.Wordprocessing.CT_HdrFtr)">
            Set a new headerFooter
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFHeaderFooter.GetTable(NPOI.OpenXmlFormats.Wordprocessing.CT_Tbl)">
            if there is a corresponding {@link XWPFTable} of the parameter ctTable in the tableList of this header
            the method will return this table
            if there is no corresponding {@link XWPFTable} the method will return null 
            @param ctTable
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFHeaderFooter.GetParagraph(NPOI.OpenXmlFormats.Wordprocessing.CT_P)">
            if there is a corresponding {@link XWPFParagraph} of the parameter ctTable in the paragraphList of this header or footer
            the method will return this paragraph
            if there is no corresponding {@link XWPFParagraph} the method will return null 
            @param p is instance of CTP and is searching for an XWPFParagraph
            @return null if there is no XWPFParagraph with an corresponding CTPparagraph in the paragraphList of this header or footer
            		   XWPFParagraph with the correspondig CTP p
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFHeaderFooter.GetParagraphArray(System.Int32)">
            Returns the paragraph that holds
             the text of the header or footer.
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFHeaderFooter.GetListParagraph">
            Get a List of all Paragraphs
            @return a list of {@link XWPFParagraph} 
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFHeaderFooter.AddPictureData(System.Byte[],System.Int32)">
             Adds a picture to the document.
            
             @param pictureData       The picture data
             @param format            The format of the picture.
            
             @return the index to this picture (0 based), the Added picture can be obtained from {@link #getAllPictures()} .
             @throws InvalidFormatException 
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFHeaderFooter.AddPictureData(System.IO.Stream,System.Int32)">
             Adds a picture to the document.
            
             @param is                The stream to read image from
             @param format            The format of the picture.
            
             @return the index to this picture (0 based), the Added picture can be obtained from {@link #getAllPictures()} .
             @throws InvalidFormatException 
             @ 
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFHeaderFooter.GetPictureDataByID(System.String)">
            returns the PictureData by blipID
            @param blipID
            @return XWPFPictureData of a specificID
            @throws Exception 
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFHeaderFooter.GetTableArray(System.Int32)">
            Returns the table at position pos
            @see NPOI.XWPF.UserModel.IBody#getTableArray(int)
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFHeaderFooter.InsertTable(System.Int32,NPOI.XWPF.UserModel.XWPFTable)">
            inserts an existing XWPFTable to the arrays bodyElements and tables
            @param pos
            @param table
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFHeaderFooter.GetTableCell(NPOI.OpenXmlFormats.Wordprocessing.CT_Tc)">
            Get the TableCell which belongs to the TableCell
            @param cell
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFHeaderFooter.GetPart">
            returns the Part, to which the body belongs, which you need for Adding relationship to other parts
            @see NPOI.XWPF.UserModel.IBody#getPart()
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFHeaderFooter.Paragraphs">
            Returns the paragraph(s) that holds
             the text of the header or footer.
            Normally there is only the one paragraph, but
             there could be more in certain cases, or 
             a table.
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFHeaderFooter.Tables">
            Return the table(s) that holds the text
             of the header or footer, for complex cases
             where a paragraph isn't used.
            Normally there's just one paragraph, but some
             complex headers/footers have a table or two
             in Addition. 
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFHeaderFooter.Text">
            Returns the textual content of the header/footer,
             by flattening out the text of its paragraph(s)
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFHeaderFooter.AllPackagePictures">
            Get all Pictures in this package
            @return all Pictures in this package
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFHeaderFooter.Owner">
            verifies that cursor is on the right position
            @param cursor
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFFooter.Commit">
            save and Commit footer
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFFooter.PartType">
            Get the PartType of the body
            @see NPOI.XWPF.UserModel.IBody#getPartType()
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFFootnote.GetTableArray(System.Int32)">
            <summary>
            
            </summary>
            <param name="pos">position in table array</param>
            <returns>The table at position pos</returns>
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFFootnote.InsertTable(System.Int32,NPOI.XWPF.UserModel.XWPFTable)">
            <summary>
            inserts an existing XWPFTable to the arrays bodyElements and tables
            </summary>
            <param name="pos"></param>
            <param name="table"></param>
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFFootnote.GetTable(NPOI.OpenXmlFormats.Wordprocessing.CT_Tbl)">
            if there is a corresponding {@link XWPFTable} of the parameter ctTable in the tableList of this header
            the method will return this table
            if there is no corresponding {@link XWPFTable} the method will return null 
            @param ctTable
            @see NPOI.XWPF.UserModel.IBody#getTable(CTTbl ctTable)
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFFootnote.GetParagraph(NPOI.OpenXmlFormats.Wordprocessing.CT_P)">
            if there is a corresponding {@link XWPFParagraph} of the parameter ctTable in the paragraphList of this header or footer
            the method will return this paragraph
            if there is no corresponding {@link XWPFParagraph} the method will return null 
            @param p is instance of CTP and is searching for an XWPFParagraph
            @return null if there is no XWPFParagraph with an corresponding CTPparagraph in the paragraphList of this header or footer
            		   XWPFParagraph with the correspondig CTP p
            @see NPOI.XWPF.UserModel.IBody#getParagraph(CTP p)
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFFootnote.GetParagraphArray(System.Int32)">
            <summary>
            Returns the paragraph that holds the text of the header or footer.
            </summary>
            <param name="pos"></param>
            <returns></returns>
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFFootnote.GetTableCell(NPOI.OpenXmlFormats.Wordprocessing.CT_Tc)">
            <summary>
            Get the TableCell which belongs to the TableCell
            </summary>
            <param name="cell"></param>
            <returns></returns>
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFFootnote.IsCursorInFtn(System.Xml.XmlDocument)">
            verifies that cursor is on the right position
            @param cursor
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFFootnote.InsertNewTbl(System.Xml.XmlDocument)">
            
            @param cursor
            @return the inserted table
            @see NPOI.XWPF.UserModel.IBody#insertNewTbl(XmlCursor cursor)
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFFootnote.InsertNewParagraph(System.Xml.XmlDocument)">
            add a new paragraph at position of the cursor
            @param cursor
            @return the inserted paragraph
            @see NPOI.XWPF.UserModel.IBody#insertNewParagraph(XmlCursor cursor)
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFFootnote.AddNewTbl(NPOI.OpenXmlFormats.Wordprocessing.CT_Tbl)">
            add a new table to the end of the footnote
            @param table
            @return the Added XWPFTable
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFFootnote.AddNewParagraph(NPOI.OpenXmlFormats.Wordprocessing.CT_P)">
            add a new paragraph to the end of the footnote
            @param paragraph
            @return the Added XWPFParagraph
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFFootnote.GetXWPFDocument">
            @see NPOI.XWPF.UserModel.IBody#getXWPFDocument()
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFFootnote.GetPart">
            returns the Part, to which the body belongs, which you need for Adding relationship to other parts
            @see NPOI.XWPF.UserModel.IBody#getPart()
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFFootnote.PartType">
            Get the PartType of the body
            @see NPOI.XWPF.UserModel.IBody#getPartType()
        </member>
        <member name="T:NPOI.XWPF.UserModel.XWPFFootnotes">
            Looks After the collection of Footnotes for a document
             
            <AUTHOR> McEuen (<EMAIL>)
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFFootnotes.#ctor(NPOI.OpenXml4Net.OPC.PackagePart,NPOI.OpenXml4Net.OPC.PackageRelationship)">
             Construct XWPFFootnotes from a package part
            
             @param part the package part holding the data of the footnotes,
             @param rel  the package relationship of type "http://schemas.Openxmlformats.org/officeDocument/2006/relationships/footnotes"
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFFootnotes.#ctor">
            Construct XWPFFootnotes from scratch for a new document.
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFFootnotes.OnDocumentRead">
            Read document
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFFootnotes.SetFootnotes(NPOI.OpenXmlFormats.Wordprocessing.CT_Footnotes)">
            Sets the ctFootnotes
            @param footnotes
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFFootnotes.AddFootnote(NPOI.XWPF.UserModel.XWPFFootnote)">
            add an XWPFFootnote to the document
            @param footnote
            @throws IOException		 
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFFootnotes.AddFootnote(NPOI.OpenXmlFormats.Wordprocessing.CT_FtnEdn)">
            add a footnote to the document
            @param note
            @throws IOException		 
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFFootnotes.GetXWPFDocument">
            @see NPOI.XWPF.UserModel.IBody#getPart()
        </member>
        <member name="T:NPOI.XWPF.UserModel.XWPFHeader">
            Sketch of XWPF header class
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFHeader.Commit">
            <summary>
            Save and commit footer
            </summary>
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFHeader.OnDocumentRead">
            <summary>
            Read the document
            </summary>
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFHeader.PartType">
            <summary>
            Get the PartType of the body
            </summary>
        </member>
        <member name="T:NPOI.XWPF.UserModel.XWPFHyperlink">
                 * Sketch of XWPF hyperlink class
                 * 
                * <AUTHOR> Batrakov (batrakov at gmail.com)
                 * 
        </member>
        <member name="T:NPOI.XWPF.UserModel.XWPFHyperlinkRun">
            A run of text with a Hyperlink applied to it.
            Any given Hyperlink may be made up of multiple of these.
        </member>
        <member name="T:NPOI.XWPF.UserModel.XWPFRun">
             XWPFrun.object defines a region of text with a common Set of properties
            
             <AUTHOR> Kozlov
             <AUTHOR> Morris (gregg dot morris at gmail dot com) - added getColor(), setColor()
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFRun.#ctor(NPOI.OpenXmlFormats.Wordprocessing.CT_R,NPOI.XWPF.UserModel.XWPFParagraph)">
            @param r the CT_R bean which holds the run.attributes
            @param p the parent paragraph
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFRun.GetCTR">
            Get the currently used CT_R object
            @return CT_R object
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFRun.IsCTOnOff(NPOI.OpenXmlFormats.Wordprocessing.CT_OnOff)">
            For isBold, isItalic etc
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFRun.SetBold(System.Boolean)">
             Whether the bold property shall be applied to all non-complex script
             characters in the contents of this run.when displayed in a document. 
             <p>
             This formatting property is a toggle property, which specifies that its
             behavior differs between its use within a style defInition and its use as
             direct formatting. When used as part of a style defInition, Setting this
             property shall toggle the current state of that property as specified up
             to this point in the hierarchy (i.e. applied to not applied, and vice
             versa). Setting it to <code>false</code> (or an equivalent) shall
             result in the current Setting remaining unChanged. However, when used as
             direct formatting, Setting this property to true or false shall Set the
             absolute state of the resulting property.
             </p>
             <p>
             If this element is not present, the default value is to leave the
             formatting applied at previous level in the style hierarchy. If this
             element is never applied in the style hierarchy, then bold shall not be
             applied to non-complex script characters.
             </p>
            
             @param value <code>true</code> if the bold property is applied to
                          this run
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFRun.GetColor">
            Get text color. The returned value is a string in the hex form "RRGGBB".
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFRun.SetColor(System.String)">
            Set text color.
            @param rgbStr - the desired color, in the hex form "RRGGBB".
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFRun.GetText(System.Int32)">
             Return the string content of this text run
            
             @return the text of this text run.or <code>null</code> if not Set
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFRun.SetText(System.String)">
             Sets the text of this text run
            
             @param value the literal text which shall be displayed in the document
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFRun.SetText(System.String,System.Int32)">
             Sets the text of this text run.in the 
            
             @param value the literal text which shall be displayed in the document
             @param pos - position in the text array (NB: 0 based)
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFRun.InsertText(System.String,System.Int32)">
            <summary>
            insert text at start index in the run
            </summary>
            <param name="text">insert text</param>
            <param name="startIndex">start index of the insertion in the run text</param>
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFRun.SetUnderline(NPOI.XWPF.UserModel.UnderlinePatterns)">
             Specifies that the contents of this run.should be displayed along with an
             underline appearing directly below the character heigh
             If this element is not present, the default value is to leave the
             formatting applied at previous level in the style hierarchy. If this
             element is never applied in the style hierarchy, then an underline shall
             not be applied to the contents of this run.
            
             @param value -
                          underline type
             @see UnderlinePatterns : all possible patterns that could be applied
        </member>
        <!-- 对于成员“M:NPOI.XWPF.UserModel.XWPFRun.SetStrike(System.Boolean)”忽略有格式错误的 XML 注释 -->
        <member name="M:NPOI.XWPF.UserModel.XWPFRun.GetTextPosition">
             This element specifies the amount by which text shall be raised or
             lowered for this run.in relation to the default baseline of the
             surrounding non-positioned text. This allows the text to be repositioned
             without altering the font size of the contents.
            
             @return a big integer representing the amount of text shall be "moved"
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFRun.SetTextPosition(System.Int32)">
            This element specifies the amount by which text shall be raised or
            lowered for this run.in relation to the default baseline of the
            surrounding non-positioned text. This allows the text to be repositioned
            without altering the font size of the contents.
            
            If the val attribute is positive, then the parent run.shall be raised
            above the baseline of the surrounding text by the specified number of
            half-points. If the val attribute is negative, then the parent run.shall
            be lowered below the baseline of the surrounding text by the specified
            number of half-points.
                    * 
            If this element is not present, the default value is to leave the
            formatting applied at previous level in the style hierarchy. If this
            element is never applied in the style hierarchy, then the text shall not
            be raised or lowered relative to the default baseline location for the
            contents of this run.
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFRun.RemoveBreak">
            
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFRun.AddBreak">
            Specifies that a break shall be placed at the current location in the run
            content. 
            A break is a special character which is used to override the
            normal line breaking that would be performed based on the normal layout
            of the document's contents. 
            @see #AddCarriageReturn() 
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFRun.AddBreak(NPOI.XWPF.UserModel.BreakType)">
            Specifies that a break shall be placed at the current location in the run
            content.
            A break is a special character which is used to override the
            normal line breaking that would be performed based on the normal layout
            of the document's contents.
            <p>
            The behavior of this break character (the
            location where text shall be restarted After this break) shall be
            determined by its type values.
            </p>
            @see BreakType
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFRun.AddBreak(NPOI.XWPF.UserModel.BreakClear)">
            Specifies that a break shall be placed at the current location in the run
            content. A break is a special character which is used to override the
            normal line breaking that would be performed based on the normal layout
            of the document's contents.
            <p>
            The behavior of this break character (the
            location where text shall be restarted After this break) shall be
            determined by its type (in this case is BreakType.TEXT_WRAPPING as default) and clear attribute values.
            </p>
            @see BreakClear
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFRun.AddCarriageReturn">
            Specifies that a carriage return shall be placed at the
            current location in the run.content.
            A carriage return is used to end the current line of text in
            WordProcess.
            The behavior of a carriage return in run.content shall be
            identical to a break character with null type and clear attributes, which
            shall end the current line and find the next available line on which to
            continue.
            The carriage return character forced the following text to be
            restarted on the next available line in the document.
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFRun.AddPicture(System.IO.Stream,System.Int32,System.String,System.Int32,System.Int32)">
            Adds a picture to the run. This method handles
             attaching the picture data to the overall file.
             
            @see NPOI.XWPF.UserModel.Document#PICTURE_TYPE_EMF
            @see NPOI.XWPF.UserModel.Document#PICTURE_TYPE_WMF
            @see NPOI.XWPF.UserModel.Document#PICTURE_TYPE_PICT
            @see NPOI.XWPF.UserModel.Document#PICTURE_TYPE_JPEG
            @see NPOI.XWPF.UserModel.Document#PICTURE_TYPE_PNG
            @see NPOI.XWPF.UserModel.Document#PICTURE_TYPE_DIB
             
            @param pictureData The raw picture data
            @param pictureType The type of the picture, eg {@link Document#PICTURE_TYPE_JPEG}
            @param width width in EMUs. To convert to / from points use {@link org.apache.poi.util.Units}
            @param height height in EMUs. To convert to / from points use {@link org.apache.poi.util.Units}
            @throws NPOI.Openxml4j.exceptions.InvalidFormatException 
            @throws IOException 
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFRun.GetEmbeddedPictures">
            Returns the embedded pictures of the run. These
             are pictures which reference an external, 
             embedded picture image such as a .png or .jpg
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFRun.preserveSpaces(NPOI.OpenXmlFormats.Wordprocessing.CT_Text)">
             Add the xml:spaces="preserve" attribute if the string has leading or trailing white spaces
            
             @param xs    the string to check
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFRun.ToString">
            Returns the string version of the text, with tabs and
             carriage returns in place of their xml equivalents.
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFRun.Paragraph">
            Get the currenty referenced paragraph object
            @return current paragraph
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFRun.Document">
            @return The {@link XWPFDocument} instance, this run.belongs to, or
                    <code>null</code> if parent structure (paragraph > document) is not properly Set.
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFRun.IsBold">
             Whether the bold property shall be applied to all non-complex script
             characters in the contents of this run.when displayed in a document
            
             @return <code>true</code> if the bold property is applied
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFRun.PictureText">
            Returns text embedded in pictures
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFRun.IsItalic">
             Whether the italic property should be applied to all non-complex script
             characters in the contents of this run.when displayed in a document.
            
             @return <code>true</code> if the italic property is applied
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFRun.Underline">
             Specifies that the contents of this run.should be displayed along with an
             underline appearing directly below the character heigh
            
             @return the Underline pattern Applyed to this run
             @see UnderlinePatterns
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFRun.IsStrike">
             Specifies that the contents of this run.shall be displayed with a single
             horizontal line through the center of the line.
            
             @return <code>true</code> if the strike property is applied
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFRun.Subscript">
             Specifies the alignment which shall be applied to the contents of this
             run.in relation to the default appearance of the run.s text.
             This allows the text to be repositioned as subscript or superscript without
             altering the font size of the run.properties.
            
             @return VerticalAlign
             @see VerticalAlign all possible value that could be Applyed to this run
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFRun.FontFamily">
             Specifies the fonts which shall be used to display the text contents of
             this run. Specifies a font which shall be used to format all characters
             in the ASCII range (0 - 127) within the parent run
            
             @return a string representing the font family
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFRun.FontSize">
             Specifies the font size which shall be applied to all non complex script
             characters in the contents of this run.when displayed.
            
             @return value representing the font size
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFHyperlinkRun.GetHyperlinkId">
            Returns the ID of the hyperlink, if one is Set.
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFHyperlinkRun.GetHyperlink(NPOI.XWPF.UserModel.XWPFDocument)">
            If this Hyperlink is an external reference hyperlink,
             return the object for it.
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFLatentStyles.IsLatentStyle(System.String)">
            Checks wheter specific LatentStyleID is a latentStyle
        </member>
        <member name="T:NPOI.XWPF.UserModel.XWPFNum">
             <AUTHOR> Epp
            
        </member>
        <member name="T:NPOI.XWPF.UserModel.XWPFNumbering">
             <AUTHOR> Epp
            
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFNumbering.#ctor(NPOI.OpenXml4Net.OPC.PackagePart,NPOI.OpenXml4Net.OPC.PackageRelationship)">
            create a new styles object with an existing document 
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFNumbering.#ctor">
            create a new XWPFNumbering object for use in a new document
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFNumbering.OnDocumentRead">
            read numbering form an existing package
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFNumbering.Commit">
            save and Commit numbering
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFNumbering.SetNumbering(NPOI.OpenXmlFormats.Wordprocessing.CT_Numbering)">
            Sets the ctNumbering
            @param numbering
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFNumbering.NumExist(System.String)">
            Checks whether number with numID exists
            @param numID
            @return bool		true if num exist, false if num not exist
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFNumbering.AddNum(NPOI.XWPF.UserModel.XWPFNum)">
            add a new number to the numbering document
            @param num
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFNumbering.AddNum(System.String)">
            Add a new num with an abstractNumID
            @return return NumId of the Added num 
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFNumbering.AddNum(System.String,System.String)">
            Add a new num with an abstractNumID and a numID
            @param abstractNumID
            @param numID
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFNumbering.GetNum(System.String)">
            Get Num by NumID
            @param numID
            @return abstractNum with NumId if no Num exists with that NumID 
            			null will be returned
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFNumbering.GetAbstractNum(System.String)">
            Get AbstractNum by abstractNumID
            @param abstractNumID
            @return  abstractNum with abstractNumId if no abstractNum exists with that abstractNumID 
            			null will be returned
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFNumbering.GetIdOfAbstractNum(NPOI.XWPF.UserModel.XWPFAbstractNum)">
            Compare AbstractNum with abstractNums of this numbering document.
            If the content of abstractNum Equals with an abstractNum of the List in numbering
            the Bigint Value of it will be returned.
            If no equal abstractNum is existing null will be returned
            
            @param abstractNum
            @return 	Bigint
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFNumbering.AddAbstractNum(NPOI.XWPF.UserModel.XWPFAbstractNum)">
            add a new AbstractNum and return its AbstractNumID 
            @param abstractNum
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFNumbering.AddAbstractNum">
            <summary>
            Add a new AbstractNum
            </summary>
            <returns></returns>
            <AUTHOR> liu
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFNumbering.RemoveAbstractNum(System.String)">
            remove an existing abstractNum 
            @param abstractNumID
            @return true if abstractNum with abstractNumID exists in NumberingArray,
            		   false if abstractNum with abstractNumID not exists
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFNumbering.GetAbstractNumID(System.String)">
            return the abstractNumID
            If the AbstractNumID not exists
            return null
             @param 		numID
             @return 		abstractNumID
        </member>
        <member name="T:NPOI.XWPF.UserModel.XWPFParagraph">
            Sketch of XWPF paragraph class
        </member>
        <member name="F:NPOI.XWPF.UserModel.XWPFParagraph.document">
            For access to the document's hyperlink, comments, tables etc 
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFParagraph.BuildRunsInOrderFromXml(System.Collections.ArrayList)">
            Identifies (in order) the parts of the paragraph /
             sub-paragraph that correspond to character text
             runs, and builds the appropriate runs for these.
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFParagraph.GetNumID">
            If style exist for this paragraph
            NumId of the paragraph will be returned.
            If style not exist null will be returned     
            @return	NumID as Bigint
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFParagraph.SetNumID(System.String)">
            SetNumID of Paragraph
            @param numPos
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFParagraph.SetNumID(System.String,System.String)">
            <summary>
            Set NumID and level of Paragraph
            </summary>
            <param name="numId"></param>
            <param name="ilvl"></param>
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFParagraph.CreateRun">
            <summary>
            Appends a new run to this paragraph
            </summary>
            <returns>a new text run</returns>
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFParagraph.GetCTPBrd(System.Boolean)">
            Get a <b>copy</b> of the currently used CTPBrd, if none is used, return
            a new instance.
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFParagraph.GetCTSpacing(System.Boolean)">
            Get a <b>copy</b> of the currently used CTSpacing, if none is used,
            return a new instance.
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFParagraph.GetCTInd(System.Boolean)">
            Get a <b>copy</b> of the currently used CTPInd, if none is used, return
            a new instance.
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFParagraph.GetCTPPr">
            Get a <b>copy</b> of the currently used CTPPr, if none is used, return
            a new instance.
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFParagraph.AddRun(NPOI.OpenXmlFormats.Wordprocessing.CT_R)">
            add a new run at the end of the position of 
            the content of parameter run
            @param run
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFParagraph.ReplaceText(System.String,System.String)">
            <summary>
            Replace text inside each run (cross run is not supported yet)
            </summary>
            <param name="oldText">target text</param>
            <param name="newText">replacement text</param>
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFParagraph.SearchText(System.String,NPOI.XWPF.UserModel.PositionInParagraph)">
            <summary>
            this methods parse the paragraph and search for the string searched. 
            If it finds the string, it will return true and the position of the String will be saved in the parameter startPos.
            </summary>
            <param name="searched"></param>
            <param name="startPos"></param>
            <returns></returns>
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFParagraph.InsertNewRun(System.Int32)">
            <summary>
            insert a new Run in RunArray
            </summary>
            <param name="pos"></param>
            <returns>the inserted run</returns>
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFParagraph.GetText(NPOI.XWPF.UserModel.TextSegement)">
            Get a Text
            @param segment
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFParagraph.RemoveRun(System.Int32)">
            Removes a Run at the position pos in the paragraph
            @param pos
            @return true if the run was Removed
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFParagraph.GetPart">
            returns the part of the bodyElement
            @see NPOI.XWPF.UserModel.IBody#getPart()
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFParagraph.AddRun(NPOI.XWPF.UserModel.XWPFRun)">
            Adds a new Run to the Paragraph
            
            @param r
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFParagraph.GetRun(NPOI.OpenXmlFormats.Wordprocessing.CT_R)">
            return the XWPFRun-Element which owns the CTR Run-Element
            
            @param r
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFParagraph.Text">
            Return the textual content of the paragraph, including text from pictures
            in it.
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFParagraph.StyleID">
            Return styleID of the paragraph if style exist for this paragraph
            if not, null will be returned     
            @return		styleID as String
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFParagraph.ParagraphText">
            Returns the text of the paragraph, but not of any objects in the
            paragraph
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFParagraph.PictureText">
            Returns any text from any suitable pictures in the paragraph
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFParagraph.FootnoteText">
             Returns the footnote text of the paragraph
            
             @return  the footnote text or empty string if the paragraph does not have footnotes
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFParagraph.Alignment">
             Returns the paragraph alignment which shall be applied to text in this
             paragraph.
             <p>
             If this element is not Set on a given paragraph, its value is determined
             by the Setting previously Set at any level of the style hierarchy (i.e.
             that previous Setting remains unChanged). If this Setting is never
             specified in the style hierarchy, then no alignment is applied to the
             paragraph.
             </p>
            
             @return the paragraph alignment of this paragraph.
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFParagraph.VerticalAlignment">
             Returns the text vertical alignment which shall be applied to text in
             this paragraph.
             <p>
             If the line height (before any Added spacing) is larger than one or more
             characters on the line, all characters will be aligned to each other as
             specified by this element.
             </p>
             <p>
             If this element is omitted on a given paragraph, its value is determined
             by the Setting previously Set at any level of the style hierarchy (i.e.
             that previous Setting remains unChanged). If this Setting is never
             specified in the style hierarchy, then the vertical alignment of all
             characters on the line shall be automatically determined by the consumer.
             </p>
            
             @return the vertical alignment of this paragraph.
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFParagraph.BorderTop">
            <summary>
            the top border for the paragraph
            </summary>
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFParagraph.BorderBottom">
             <summary>
            Specifies the border which shall be displayed below a Set of
             paragraphs which have the same Set of paragraph border Settings.
             </summary>
             <returns>the bottom border for the paragraph</returns>
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFParagraph.BorderLeft">
            <summary>
            Specifies the border which shall be displayed on the left side of the
            page around the specified paragraph.
            </summary>
            <returns>the left border for the paragraph</returns>
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFParagraph.BorderRight">
             Specifies the border which shall be displayed on the right side of the
             page around the specified paragraph.
            
             @return ParagraphBorder - the right border for the paragraph
             @see #setBorderRight(Borders)
             @see Borders for a list of all possible borders
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFParagraph.BorderBetween">
             Specifies the border which shall be displayed between each paragraph in a
             Set of paragraphs which have the same Set of paragraph border Settings.
            
             @return ParagraphBorder - the between border for the paragraph
             @see #setBorderBetween(Borders)
             @see Borders for a list of all possible borders
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFParagraph.IsPageBreak">
             Specifies that when rendering this document in a paginated
             view, the contents of this paragraph are rendered on the start of a new
             page in the document.
             <p>
             If this element is omitted on a given paragraph,
             its value is determined by the Setting previously Set at any level of the
             style hierarchy (i.e. that previous Setting remains unChanged). If this
             Setting is never specified in the style hierarchy, then this property
             shall not be applied. Since the paragraph is specified to start on a new
             page, it begins page two even though it could have fit on page one.
             </p>
            
             @return bool - if page break is Set
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFParagraph.SpacingAfter">
             Specifies the spacing that should be Added After the last line in this
             paragraph in the document in absolute units.
            
             @return int - value representing the spacing After the paragraph
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFParagraph.SpacingAfterLines">
             Specifies the spacing that should be Added After the last line in this
             paragraph in the document in absolute units.
            
             @return bigint - value representing the spacing After the paragraph
             @see #setSpacingAfterLines(int)
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFParagraph.SpacingBefore">
             Specifies the spacing that should be Added above the first line in this
             paragraph in the document in absolute units.
            
             @return the spacing that should be Added above the first line
             @see #setSpacingBefore(int)
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFParagraph.SpacingBeforeLines">
             Specifies the spacing that should be Added before the first line in this paragraph in the
             document in line units.
             The value of this attribute is specified in one hundredths of a line.
            
             @return the spacing that should be Added before the first line in this paragraph
             @see #setSpacingBeforeLines(int)
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFParagraph.SpacingLineRule">
             Specifies how the spacing between lines is calculated as stored in the
             line attribute. If this attribute is omitted, then it shall be assumed to
             be of a value auto if a line attribute value is present.
            
             @return rule
             @see LineSpacingRule
             @see #setSpacingLineRule(LineSpacingRule)
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFParagraph.IndentationLeft">
             Specifies the indentation which shall be placed between the left text
             margin for this paragraph and the left edge of that paragraph's content
             in a left to right paragraph, and the right text margin and the right
             edge of that paragraph's text in a right to left paragraph
             <p>
             If this attribute is omitted, its value shall be assumed to be zero.
             Negative values are defined such that the text is Moved past the text margin,
             positive values Move the text inside the text margin.
             </p>
            
             @return indentation or null if indentation is not Set
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFParagraph.IndentationRight">
             Specifies the indentation which shall be placed between the right text
             margin for this paragraph and the right edge of that paragraph's content
             in a left to right paragraph, and the right text margin and the right
             edge of that paragraph's text in a right to left paragraph
             <p>
             If this attribute is omitted, its value shall be assumed to be zero.
             Negative values are defined such that the text is Moved past the text margin,
             positive values Move the text inside the text margin.
             </p>
            
             @return indentation or null if indentation is not Set
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFParagraph.IndentationHanging">
             Specifies the indentation which shall be Removed from the first line of
             the parent paragraph, by moving the indentation on the first line back
             towards the beginning of the direction of text flow.
             This indentation is
             specified relative to the paragraph indentation which is specified for
             all other lines in the parent paragraph.
             The firstLine and hanging
             attributes are mutually exclusive, if both are specified, then the
             firstLine value is ignored.
            
             @return indentation or null if indentation is not Set
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFParagraph.IndentationFirstLine">
             Specifies the Additional indentation which shall be applied to the first
             line of the parent paragraph. This Additional indentation is specified
             relative to the paragraph indentation which is specified for all other
             lines in the parent paragraph.
             The firstLine and hanging attributes are
             mutually exclusive, if both are specified, then the firstLine value is
             ignored.
             If the firstLineChars attribute is also specified, then this
             value is ignored.
             If this attribute is omitted, then its value shall be
             assumed to be zero (if needed).
            
             @return indentation or null if indentation is not Set
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFParagraph.IsWordWrap">
             This element specifies whether a consumer shall break Latin text which
             exceeds the text extents of a line by breaking the word across two lines
             (breaking on the character level) or by moving the word to the following
             line (breaking on the word level).
            
             @return bool
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFParagraph.Style">
            @return  the style of the paragraph
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFParagraph.ElementType">
            returns the type of the BodyElement Paragraph
            @see NPOI.XWPF.UserModel.IBodyElement#getElementType()
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFParagraph.PartType">
            returns the partType of the bodyPart which owns the bodyElement
            
            @see NPOI.XWPF.UserModel.IBody#getPartType()
        </member>
        <member name="T:NPOI.XWPF.UserModel.XWPFPicture">
            <AUTHOR> Epp
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFPicture.SetPictureReference(NPOI.OpenXml4Net.OPC.PackageRelationship)">
            Link Picture with PictureData
            @param rel
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFPicture.GetCTPicture">
             Return the underlying CTPicture bean that holds all properties for this picture
            
             @return the underlying CTPicture bean
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFPicture.GetPictureData">
            Get the PictureData of the Picture, if present.
            Note - not all kinds of picture have data
        </member>
        <member name="T:NPOI.XWPF.UserModel.XWPFPictureData">
            <summary>
            Raw picture data, normally attached to a WordProcessingML Drawing. As a rule, pictures are stored in the /word/media/ part of a WordProcessingML package.
            </summary>
            <remarks>
            <AUTHOR> Epp
            </remarks>
        </member>
        <member name="F:NPOI.XWPF.UserModel.XWPFPictureData.RELATIONS">
            Relationships for each known picture type
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFPictureData.#ctor">
             Create a new XWPFGraphicData node
            
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFPictureData.#ctor(NPOI.OpenXml4Net.OPC.PackagePart,NPOI.OpenXml4Net.OPC.PackageRelationship)">
             Construct XWPFPictureData from a package part
            
             @param part the package part holding the Drawing data,
             @param rel  the package relationship holding this Drawing,
             the relationship type must be http://schemas.Openxmlformats.org/officeDocument/2006/relationships/image
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFPictureData.SuggestFileExtension">
            Suggests a file extension for this image.
            @return the file extension.
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFPictureData.GetPictureType">
            Return an integer constant that specifies type of this picture
            
            @return an integer constant that specifies type of this picture
            @see NPOI.XWPF.UserModel.PictureTypeEMF
            @see NPOI.XWPF.UserModel.PictureTypeWMF
            @see NPOI.XWPF.UserModel.PictureTypePICT
            @see NPOI.XWPF.UserModel.PictureTypeJPEG
            @see NPOI.XWPF.UserModel.PictureTypePNG
            @see NPOI.XWPF.UserModel.PictureTypeDIB
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFPictureData.Data">
            Gets the picture data as a byte array.
            <p>
            Note, that this call might be expensive since all the picture data is copied into a temporary byte array.
            You can grab the picture data directly from the underlying package part as follows:
            <br/>
            <code>
            InputStream is1 = GetPackagePart().InputStream;
            </code>
            </p>
            @return the Picture data.
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFPictureData.FileName">
            Returns the file name of the image, eg image7.jpg . The original filename
            isn't always available, but if it can be found it's likely to be in the
            CTDrawing
        </member>
        <member name="T:NPOI.XWPF.UserModel.XWPFRelation">
            <AUTHOR> Kozlov
        </member>
        <member name="F:NPOI.XWPF.UserModel.XWPFRelation._table">
            A map to lookup POIXMLRelation by its relation type
        </member>
        <member name="F:NPOI.XWPF.UserModel.XWPFRelation.IMAGE_EMF">
            Supported image formats
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFRelation.GetInstance(System.String)">
             Get POIXMLRelation by relation type
            
             @param rel relation type, for example,
                        <code>http://schemas.openxmlformats.org/officeDocument/2006/relationships/image</code>
             @return registered POIXMLRelation or null if not found
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFSettings.GetZoomPercent">
            In the zoom tag inside Settings.xml file <br/>
            it Sets the value of zoom
            @return percentage as an integer of zoom level
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFSettings.SetZoomPercent(System.Int64)">
            <summary>
            Set zoom. In the zoom tag inside settings.xml file it sets the value of zoom
            </summary>
            <param name="zoomPercent"></param>
            <example>
            sample snippet from Settings.xml 
            
            &lt;w:zoom w:percent="50" /&gt;
            </example>
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFSettings.IsEnforcedWith(NPOI.OpenXmlFormats.Wordprocessing.ST_DocProtect)">
            Verifies the documentProtection tag inside Settings.xml file <br/>
            if the protection is enforced (w:enforcement="1") <br/>
            and if the kind of protection Equals to passed (STDocProtect.Enum editValue) <br/>
            
            <br/>
            sample snippet from Settings.xml
            <pre>
                &lt;w:settings  ... &gt;
                    &lt;w:documentProtection w:edit=&quot;readOnly&quot; w:enforcement=&quot;1&quot;/&gt;
            </pre>
            
            @return true if documentProtection is enforced with option ReadOnly
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFSettings.SetEnforcementEditValue(NPOI.OpenXmlFormats.Wordprocessing.ST_DocProtect)">
            Enforces the protection with the option specified by passed editValue.<br/>
            <br/>
            In the documentProtection tag inside Settings.xml file <br/>
            it Sets the value of enforcement to "1" (w:enforcement="1") <br/>
            and the value of edit to the passed editValue (w:edit="[passed editValue]")<br/>
            <br/>
            sample snippet from Settings.xml
            <pre>
                &lt;w:settings  ... &gt;
                    &lt;w:documentProtection w:edit=&quot;[passed editValue]&quot; w:enforcement=&quot;1&quot;/&gt;
            </pre>
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFSettings.RemoveEnforcement">
            Removes protection enforcement.<br/>
            In the documentProtection tag inside Settings.xml file <br/>
            it Sets the value of enforcement to "0" (w:enforcement="0") <br/>
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFSettings.SetUpdateFields">
            Enforces fields update on document open (in Word).
            In the settings.xml file <br/>
            sets the updateSettings value to true (w:updateSettings w:val="true")
            
             NOTICES:
             <ul>
             	<li>Causing Word to ask on open: "This document contains fields that may refer to other files. Do you want to update the fields in this document?"
                      (if "Update automatic links at open" is enabled)</li>
             	<li>Flag is removed after saving with changes in Word </li>
             </ul> 
        </member>
        <member name="T:NPOI.XWPF.UserModel.XWPFStyle">
             <AUTHOR> Epp
            
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFStyle.#ctor(NPOI.OpenXmlFormats.Wordprocessing.CT_Style)">
            constructor
            @param style
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFStyle.#ctor(NPOI.OpenXmlFormats.Wordprocessing.CT_Style,NPOI.XWPF.UserModel.XWPFStyles)">
            constructor
            @param style
            @param styles
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFStyle.SetStyle(NPOI.OpenXmlFormats.Wordprocessing.CT_Style)">
            Set style
            @param style		
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFStyle.GetCTStyle">
            Get ctStyle
            @return	ctStyle
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFStyle.GetStyles">
            Get styles
            @return styles		the styles to which this style belongs
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFStyle.HasSameName(NPOI.XWPF.UserModel.XWPFStyle)">
            Compares the names of the Styles 
            @param compStyle
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFStyle.StyleId">
            Get StyleID of the style
            @return styleID		StyleID of the style
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFStyle.StyleType">
            Get Type of the Style
            @return	ctType 
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFStyle.LinkStyleID">
            Get StyleID of the linked Style
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFStyle.NextStyleID">
            Get StyleID of the next style
        </member>
        <member name="T:NPOI.XWPF.UserModel.XWPFStyles">
             <AUTHOR> Epp
            
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFStyles.#ctor(NPOI.OpenXml4Net.OPC.PackagePart,NPOI.OpenXml4Net.OPC.PackageRelationship)">
             Construct XWPFStyles from a package part
            
             @param part the package part holding the data of the styles,
             @param rel  the package relationship of type "http://schemas.Openxmlformats.org/officeDocument/2006/relationships/styles"
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFStyles.#ctor">
            Construct XWPFStyles from scratch for a new document.
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFStyles.OnDocumentRead">
            Read document
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFStyles.SetStyles(NPOI.OpenXmlFormats.Wordprocessing.CT_Styles)">
            Sets the ctStyles
            @param styles
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFStyles.StyleExist(System.String)">
            Checks whether style with styleID exist
            @param styleID		styleID of the Style in the style-Document
            @return				true if style exist, false if style not exist
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFStyles.AddStyle(NPOI.XWPF.UserModel.XWPFStyle)">
            add a style to the document
            @param style				
            @throws IOException		 
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFStyles.GetStyle(System.String)">
            get style by a styleID 
             @param styleID	styleID of the searched style
             @return style
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFStyles.GetUsedStyleList(NPOI.XWPF.UserModel.XWPFStyle)">
            Get the styles which are related to the parameter style and their relatives
            this method can be used to copy all styles from one document to another document 
            @param style
            @return a list of all styles which were used by this method 
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFStyles.GetUsedStyleList(NPOI.XWPF.UserModel.XWPFStyle,System.Collections.Generic.List{NPOI.XWPF.UserModel.XWPFStyle})">
            Get the styles which are related to parameter style
            @param style
            @return all Styles of the parameterList
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFStyles.SetSpellingLanguage(System.String)">
            Sets the default spelling language on ctStyles DocDefaults parameter
            @param strSpellingLanguage
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFStyles.SetEastAsia(System.String)">
            Sets the default East Asia spelling language on ctStyles DocDefaults parameter
            @param strEastAsia
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFStyles.SetDefaultFonts(NPOI.OpenXmlFormats.Wordprocessing.CT_Fonts)">
            Sets the default font on ctStyles DocDefaults parameter
            @param fonts
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFStyles.GetLatentStyles">
            Get latentstyles
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFStyles.GetStyleWithSameName(NPOI.XWPF.UserModel.XWPFStyle)">
            Get the style with the same name
            if this style is not existing, return null
        </member>
        <member name="T:NPOI.XWPF.UserModel.XWPFTable">
             Sketch of XWPFTable class. Only table's text is being hold.
             <p/>
             Specifies the contents of a table present in the document. A table is a Set
             of paragraphs (and other block-level content) arranged in rows and columns.
            
             <AUTHOR> Batrakov (batrakov at gmail.com)
             <AUTHOR> Morris (gregg dot morris at gmail dot com) - added 
                     setStyleID()
                     getRowBandSize(), setRowBandSize()
                     getColBandSize(), setColBandSize()
                     getInsideHBorderType(), getInsideHBorderSize(), getInsideHBorderSpace(), getInsideHBorderColor()
                     getInsideVBorderType(), getInsideVBorderSize(), getInsideVBorderSpace(), getInsideVBorderColor()
                     setInsideHBorder(), setInsideVBorder()
                     getCellMarginTop(), getCellMarginLeft(), getCellMarginBottom(), getCellMarginRight()
                     setCellMargins()
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFTable.GetCTTbl">
            @return ctTbl object
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFTable.AddNewCol">
            add a new column for each row in this table
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFTable.CreateRow">
             create a new XWPFTableRow object with as many cells as the number of columns defined in that moment
            
             @return tableRow
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFTable.GetRow(System.Int32)">
            @param pos - index of the row
            @return the row at the position specified or null if no rows is defined or if the position is greather than the max size of rows array
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFTable.AddRow(NPOI.XWPF.UserModel.XWPFTableRow)">
            add a new Row to the table
            
            @param row	the row which should be Added
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFTable.AddRow(NPOI.XWPF.UserModel.XWPFTableRow,System.Int32)">
            add a new Row to the table
            at position pos
            @param row	the row which should be Added
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFTable.InsertNewTableRow(System.Int32)">
            inserts a new tablerow 
            @param pos
            @return  the inserted row
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFTable.RemoveRow(System.Int32)">
            Remove a row at position pos from the table
            @param pos	position the Row in the Table
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFTable.GetPart">
            returns the part of the bodyElement
            @see NPOI.XWPF.UserModel.IBody#getPart()
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFTable.GetRow(NPOI.OpenXmlFormats.Wordprocessing.CT_Row)">
            returns the XWPFRow which belongs to the CTRow row
            if this row is not existing in the table null will be returned
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFTable.Text">
            @return text
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFTable.Width">
            @return width value
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFTable.NumberOfRows">
            @return number of rows in table
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFTable.StyleID">
            Get the StyleID of the table
            @return	style-ID of the table
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFTable.ElementType">
            returns the type of the BodyElement Table
            @see NPOI.XWPF.UserModel.IBodyElement#getElementType()
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFTable.PartType">
            returns the partType of the bodyPart which owns the bodyElement
            @see NPOI.XWPF.UserModel.IBody#getPartType()
        </member>
        <member name="T:NPOI.XWPF.UserModel.XWPFTableCell">
             XWPFTableCell class.
            
             <AUTHOR> Morris (gregg dot morris at gmail dot com) - added XWPFVertAlign enum,
                     setColor(),
                     setVerticalAlignment()
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFTableCell.#ctor(NPOI.OpenXmlFormats.Wordprocessing.CT_Tc,NPOI.XWPF.UserModel.XWPFTableRow,NPOI.XWPF.UserModel.IBody)">
            If a table cell does not include at least one block-level element, then this document shall be considered corrupt
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFTableCell.AddParagraph">
            Add a Paragraph to this Table Cell
            @return The paragraph which was Added
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFTableCell.AddParagraph(NPOI.XWPF.UserModel.XWPFParagraph)">
            add a Paragraph to this TableCell
            @param p the paragaph which has to be Added
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFTableCell.RemoveParagraph(System.Int32)">
            Removes a paragraph of this tablecell
            @param pos
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFTableCell.GetParagraph(NPOI.OpenXmlFormats.Wordprocessing.CT_P)">
            if there is a corresponding {@link XWPFParagraph} of the parameter ctTable in the paragraphList of this table
            the method will return this paragraph
            if there is no corresponding {@link XWPFParagraph} the method will return null 
            @param p is instance of CTP and is searching for an XWPFParagraph
            @return null if there is no XWPFParagraph with an corresponding CTPparagraph in the paragraphList of this table
            		   XWPFParagraph with the correspondig CTP p
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFTableCell.SetColor(System.String)">
            Set cell color. This sets some associated values; for finer control
            you may want to access these elements individually.
            @param rgbStr - the desired cell color, in the hex form "RRGGBB".
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFTableCell.GetColor">
            Get cell color. Note that this method only returns the "fill" value.
            @return RGB string of cell color
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFTableCell.SetVerticalAlignment(NPOI.XWPF.UserModel.XWPFTableCell.XWPFVertAlign)">
            Set the vertical alignment of the cell.
            @param vAlign - the desired alignment enum value
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFTableCell.GetVerticalAlignment">
            Get the vertical alignment of the cell.
            @return the cell alignment enum value
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFTableCell.InsertNewParagraph(System.Xml.XmlDocument)">
            add a new paragraph at position of the cursor
            @param cursor
            @return the inserted paragraph
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFTableCell.IsCursorInTableCell(System.Xml.XmlDocument)">
            verifies that cursor is on the right position
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFTableCell.GetParagraphArray(System.Int32)">
            @see NPOI.XWPF.UserModel.IBody#getParagraphArray(int)
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFTableCell.GetPart">
            Get the to which the TableCell belongs
            
            @see NPOI.XWPF.UserModel.IBody#getPart()
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFTableCell.GetTable(NPOI.OpenXmlFormats.Wordprocessing.CT_Tbl)">
            Get a table by its CTTbl-Object
            @see NPOI.XWPF.UserModel.IBody#getTable(org.Openxmlformats.schemas.wordProcessingml.x2006.main.CTTbl)
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFTableCell.GetTableArray(System.Int32)">
            @see NPOI.XWPF.UserModel.IBody#getTableArray(int)
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFTableCell.InsertTable(System.Int32,NPOI.XWPF.UserModel.XWPFTable)">
            inserts an existing XWPFTable to the arrays bodyElements and tables
            @see NPOI.XWPF.UserModel.IBody#insertTable(int, NPOI.XWPF.UserModel.XWPFTable)
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFTableCell.GetTableCell(NPOI.OpenXmlFormats.Wordprocessing.CT_Tc)">
            Get the TableCell which belongs to the TableCell
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFTableCell.BodyElements">
            returns an Iterator with paragraphs and tables
            @see NPOI.XWPF.UserModel.IBody#getBodyElements()
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFTableCell.Paragraphs">
            returns a list of paragraphs
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFTableCell.PartType">
            @see NPOI.XWPF.UserModel.IBody#getPartType()
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFTableCell.Tables">
            @see NPOI.XWPF.UserModel.IBody#getTables()
        </member>
        <member name="T:NPOI.XWPF.UserModel.XWPFTableRow">
            <AUTHOR>
            <AUTHOR> morris - added removeCell(), setCantSplitRow(), setRepeatHeader()
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFTableRow.CreateCell">
            create a new XWPFTableCell and add it to the tableCell-list of this tableRow
            @return the newly Created XWPFTableCell
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFTableRow.AddNewTableCell">
            Adds a new TableCell at the end of this tableRow
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFTableRow.GetTableCells">
            create and return a list of all XWPFTableCell
            who belongs to this row
            @return a list of {@link XWPFTableCell} 
        </member>
        <member name="M:NPOI.XWPF.UserModel.XWPFTableRow.GetTableCell(NPOI.OpenXmlFormats.Wordprocessing.CT_Tc)">
            returns the XWPFTableCell which belongs to the CTTC cell
            if there is no XWPFTableCell which belongs to the parameter CTTc cell null will be returned
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFTableRow.Height">
             This element specifies the height of the current table row within the
             current table. This height shall be used to determine the resulting
             height of the table row, which may be absolute or relative (depending on
             its attribute values). If omitted, then the table row shall automatically
             resize its height to the height required by its contents (the equivalent
             of an hRule value of auto).
            
             @return height
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFTableRow.IsCantSplitRow">
            Return true if the "can't split row" value is true. The logic for this
            attribute is a little unusual: a TRUE value means DON'T allow rows to
            split, FALSE means allow rows to split.
            @return true if rows can't be split, false otherwise.
        </member>
        <member name="P:NPOI.XWPF.UserModel.XWPFTableRow.IsRepeatHeader">
            Return true if a table's header row should be repeated at the top of a
            table split across pages.
            @return true if table's header row should be repeated at the top of each
                    page of table, false otherwise.
        </member>
    </members>
</doc>
