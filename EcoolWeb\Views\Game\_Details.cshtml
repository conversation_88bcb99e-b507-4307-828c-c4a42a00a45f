﻿@model GameEditDetailsViewModel

@using (Html.BeginCollectionItem("Details"))
{
    var Index = Html.GetIndex("Details");

    string backgroundColor = "";



<div class="tr" id="Tr@(Index)" style="height:30px;vertical-align:middle;background-color:@(backgroundColor);">
    <div class="td" style="text-align:center;">
        @if ((Model.LEVEL_TYPE ?? ADDT26_D.LevelType.Pay) == ADDT26_D.LevelType.Pay || (Model.LEVEL_TYPE ?? ADDT26_D.LevelType.Pay) == ADDT26_D.LevelType.Prize)
        {
            <a role='button' style="cursor:pointer;" onclick="deleteRow('Tr@(Index)')"> <i class='glyphicon glyphicon-remove'></i></a>
        }
    </div>
    <div class="td" style="text-align:center">
        @if ((Model.LEVEL_TYPE ?? ADDT26_D.LevelType.Pay) == ADDT26_D.LevelType.Pay || (Model.LEVEL_TYPE ?? ADDT26_D.LevelType.Pay) == ADDT26_D.LevelType.Prize)
        {
            @Html.CheckBoxFor(m => m.Y_REPEAT)
        }
    </div>
    @*<div class="td" style="text-align:center">
        @if ((Model.LEVEL_TYPE ?? ADDT26_D.LevelType.Pay) == ADDT26_D.LevelType.Pay || (Model.LEVEL_TYPE ?? ADDT26_D.LevelType.Pay) == ADDT26_D.LevelType.Prize)
        {
            @Html.CheckBoxFor(m => m.Y_Photo)
        }
    </div>*@

    <div class="td">
        @Html.HiddenFor(m => m.IsApply)
        @Html.HiddenFor(m => m.LEVEL_NO)
        @Html.HiddenFor(m => m.LEVEL_TYPE)
        @Html.EditorFor(m => m.LEVEL_NAME, new { htmlAttributes = new { @class = "form-control input-md" } })
    </div>

    <div class="td">
        @if ((Model.LEVEL_TYPE ?? ADDT26_D.LevelType.Pay) == ADDT26_D.LevelType.Test)
        {
            @Html.EditorFor(m => m.CASH, new { htmlAttributes = new { @class = "form-control input-md", @readonly = "readonly" } })
        }
        else
        {
            @Html.EditorFor(m => m.CASH, new { htmlAttributes = new { @class = "form-control input-md" } })
        }
    </div>
    <div class="td">

        @if (string.IsNullOrWhiteSpace(Model.LEVEL_IMG_PATH))
        {
            <samp class="DivPHOTO_FILE">
                @Html.TextBoxFor(m => m.PhotoFiles, new { @class = "form-control input-md", @type = "file" })
                @Html.ValidationMessageFor(m => m.PhotoFiles, "", new { @class = "text-danger" })
            </samp>

        }
        else
        {
            <div id="DivPHOTO_FILE_URL_@(Index)" style="display:inline;">
                <a role='button' style="cursor:pointer;" onclick="DelFile('@Index')"> <i class='glyphicon glyphicon-remove'></i></a>

                <div class="colorboxPHOTO thumbnail" style="cursor:pointer;width:150px;height:150px;display:table-cell; vertical-align:middle; " href="@Model.LEVEL_IMG_PATH" title="@Model.LEVEL_IMG">
                    <img src="@Model.LEVEL_IMG_PATH" style="display:inline;max-width:140px;max-height:140px" alt="Responsive image" href="@Model.LEVEL_IMG_PATH" class="colorboxPHOTO" />
                </div>
            </div>

            <div style="display:none" class="DivPHOTO_FILE" id="DivPHOTO_FILE_@(Index)">
                @Html.EditorFor(m => m.LEVEL_IMG, new { htmlAttributes = new { @class = "form-control input-md" } })
                @Html.ValidationMessageFor(m => m.LEVEL_IMG, "", new { @class = "text-danger" })
            </div>

            <div style="display:none" class="DivUpPhotoFiles" id="DivUpPhotoFiles_@(Index)">
                @Html.TextBoxFor(m => m.PhotoFiles, new { @class = "form-control input-md", @type = "file" })
                @Html.ValidationMessageFor(m => m.PhotoFiles, "", new { @class = "text-danger" })
            </div>
        }
    </div>

    @if ((Model.LEVEL_TYPE ?? ADDT26_D.LevelType.Pay) == ADDT26_D.LevelType.Test)
    {
        <div class="td">
            @Html.EditorFor(m => m.LOADING_TIME, new { htmlAttributes = new { @class = "form-control input-md", @placeholder = "未填預設1秒" } })
        </div>
        <div class="td">
            @Html.EditorFor(m => m.PASSED_TIME, new { htmlAttributes = new { @class = "form-control input-md", @placeholder = "未填預設3秒" } })
        </div>
    }
    else if ((Model.LEVEL_TYPE ?? ADDT26_D.LevelType.Pay) == ADDT26_D.LevelType.Pay || (Model.LEVEL_TYPE ?? ADDT26_D.LevelType.Pay) == ADDT26_D.LevelType.Prize)
    {

        <div class="td">
            @Html.EditorFor(m => m.LOADING_TIME, new { htmlAttributes = new { @class = "form-control input-md", @placeholder = "未填預設1秒" } })
        </div>
        <div class="td">
            @Html.EditorFor(m => m.PASSED_TIME, new { htmlAttributes = new { @class = "form-control input-md", @placeholder = "未填預設3秒" } })
        </div>
    }
    else
    {
        <div class="td">
            @Html.EditorFor(m => m.PASSED_TIME, new { htmlAttributes = new { @class = "form-control input-md", @placeholder = "未填預設3秒" } })
        </div>
    }
</div>
}
