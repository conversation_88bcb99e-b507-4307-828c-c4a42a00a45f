﻿using MvcPaging;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Web.Mvc;

namespace ECOOL_APP.EF
{
    public enum OrderType
    {
        酷幣點數由大到小,
        兌換期限由小到大,
        酷幣點數由小到大,
        兌換期限由大到小
    }

    public class AWAI01IndexViewModel
    {
        /// <summary>
        /// 顯示二個區塊
        /// </summary>
        public bool ShowMultiple { get; set; }

        public byte MultipleLevel { get; set; }
        public string WhereSCHOOL_NO { get; set; }
        /// <summary>
        /// 新增權限
        /// </summary>
        public bool VisableInsert { get; set; }

        /// <summary>
        /// 修改權限
        /// </summary>
        public bool VisableModify { get; set; }

        /// <summary>
        /// 刪除權限
        /// </summary>
        public bool VisableDelete { get; set; }

        /// <summary>
        /// 行動支付兌換功能權限
        /// </summary>
        public bool VisableFullScreen { get; set; }

        /// <summary>
        /// 取得兌換獎品學生投稿說明檔
        /// </summary>
        public string AwatQ02SEXPLAIN { get; set; }

        /// <summary>
        ///取得兌換獎品老師投稿說明檔
        /// </summary>
        public string AwatQ02TEXPLAIN { get; set; }

        /// <summary>
        /// 獎品狀態 選單 for Query
        /// </summary>
        public List<SelectListItem> AwardTypeItemQuery { get; set; }

        /// <summary>
        /// 獎品類別
        /// </summary>
        public List<SelectListItem> AwardSchoolItemQuery { get; set; }

        public AWAI01SearchViewModel Search { get; set; }

        /// <summary>
        /// 一頁筆數 預設筆數
        /// </summary>
        public int PageSize { get; set; }

        /// <summary>
        /// 總召資料
        /// </summary>
        public IPagedList<AWAI01IndexListViewModel> SpecificListData;

        /// <summary>
        /// 查詢結果
        /// </summary>
        public IPagedList<AWAI01IndexListViewModel> ListData;

        /// <summary>
        /// 排序方式
        /// </summary>
        public OrderType OrderType { get; set; }

        public AWAI01IndexViewModel()
        {
            PageSize = int.MaxValue;
        }
    }
}