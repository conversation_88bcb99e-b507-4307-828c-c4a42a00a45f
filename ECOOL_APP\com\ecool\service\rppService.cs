﻿using com.ecool.service;
using ECOOL_APP.EF;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Text;

namespace com.ecool.service
{
    public class rppService : ServiceBase
    {
        public bool BookPreInser(string SCHOOL_NO)
        {
            bool InserPre = true;
            Dictionary<string, int[]> PreADDT03 = new Dictionary<string, int[]>();
            int[] Grade01 = new int[] { 101, 102, 103, 104, 105 };
            int[] Grade02 = new int[] { 201, 202, 203, 204, 205, 206, 207, 208 };
            int[] Grade03 = new int[] { 301, 302, 303, 304, 305, 306, 307, 308, 309, 310 };
            int[] Grade04 = new int[] { 401, 402, 403, 404, 405, 406, 407, 408, 409, 410 };
            int[] Grade05 = new int[] { 501, 502, 503, 504, 505, 506, 507, 508, 509, 510 };
            int[] Grade06 = new int[] { 601, 602, 603, 604, 605, 606, 607, 608, 609, 610 };
            PreADDT03.Add("一年級", Grade01);
            PreADDT03.Add("二年級", Grade02);
            PreADDT03.Add("三年級", Grade03);
            PreADDT03.Add("四年級", Grade04);
            PreADDT03.Add("五年級", Grade05);
            PreADDT03.Add("六年級", Grade06);
            int index = 0;
            foreach (var PreADDT03Item in PreADDT03.Keys)
            {
                index++;
                foreach (var GradeItem in PreADDT03[PreADDT03Item])
                {
                    bool GradeStatus = new rppService().GetBookStatus(GradeItem.ToString(), SCHOOL_NO);
                    if (!GradeStatus)
                    {
                        InserPre = new rppService().BookInsert("ADD", SCHOOL_NO, index.ToString(), GradeItem.ToString(), "");
                    }
                }
            }
            return InserPre;
        }

        public bool BookInsert(string Book_Status, string SCHOOL_NO, string bGrade, string BOOK_ID, string BOOK_NAME)
        {
            bool BookInsertStatus = true;
            ECOOL_DEVEntities db = new ECOOL_DEVEntities();
            ADDT03 a03 = new ADDT03();
            if (Book_Status == "ADD")
            {
                a03.SCHOOL_NO = SCHOOL_NO;

                a03.GRADE = byte.Parse(bGrade);
                a03.BOOK_ID = BOOK_ID;
                a03.BOOK_NAME = BOOK_NAME;
                if (db.ADDT03.Where(p => p.BOOK_ID == BOOK_ID && p.SCHOOL_NO == SCHOOL_NO).Count() == 0)
                {
                    db.ADDT03.Add(a03);
                }
                else
                {
                    BookInsertStatus = false;
                }
            }
            else if (Book_Status == "EDIT")
            {
                ADDT03 updateADDT03 = db.ADDT03.Where(p => p.SCHOOL_NO == SCHOOL_NO && p.BOOK_ID == BOOK_ID).FirstOrDefault();
                updateADDT03.SCHOOL_NO = SCHOOL_NO;

                updateADDT03.GRADE = Convert.ToByte(bGrade);
                //updateADDT03.BOOK_ID = BOOK_ID;
                updateADDT03.BOOK_NAME = BOOK_NAME;
                db.Entry(updateADDT03).State = EntityState.Modified;
            }
            try
            {
                db.SaveChanges();
            }
            catch
            {
                BookInsertStatus = false;
            }
            return BookInsertStatus;
        }

        public bool GetBookStatus(string BOOK_ID, string SCHOOL_NO)
        {
            ECOOL_DEVEntities db = new ECOOL_DEVEntities();
            bool BookStatus = false;

            if (db.ADDT03.Where(p => p.BOOK_ID == BOOK_ID && p.SCHOOL_NO == SCHOOL_NO).Count() != 0)
            {
                BookStatus = true;
            }
            return BookStatus;
        }

        public List<Hashtable> USP_RPP_BOOK_QUERY(string parSCHOOL_NO)
        {
            List<Hashtable> list_data = new List<Hashtable>();
            try
            {
                string sql = @" SELECT ROW_NUMBER() OVER(ORDER BY BOOK_ID ASC) AS NO, " +
                              " BOOK_ID, " +
                              " CASE GRADE " +
                              "     WHEN 1 THEN '一年級' " +
                              "     WHEN 2 THEN '二年級' " +
                              "     WHEN 3 THEN '三年級' " +
                              "     WHEN 4 THEN '四年級' " +
                              "     WHEN 5 THEN '五年級' " +
                              "     WHEN 6 THEN '六年級' " +
                              "     End as GRADE ,BOOK_NAME  " +
                              " FROM ADDT03 " +
                              //" WHERE SCHOOL_NO = 4142522";
                              " WHERE SCHOOL_NO = " + parSCHOOL_NO+ " and BOOK_NAME !='' ";
                list_data = new sqlConnection.sqlConnection().executeQueryBySQLToHashtableList(sql);
            }
            catch (Exception exception)
            {
                throw exception;
            }

            return list_data;
        }
    }
}