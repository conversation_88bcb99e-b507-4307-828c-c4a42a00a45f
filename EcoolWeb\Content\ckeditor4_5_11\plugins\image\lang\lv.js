﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'image', 'lv', {
	alt: 'Alternatīvais teksts',
	border: 'Rāmis',
	btnUpload: 'Nosūtīt serverim',
	button2Img: 'Vai vēlaties pārveidot izvēlēto attēla pogu uz attēla?',
	hSpace: 'Horizontālā telpa',
	img2Button: 'Vai vēlaties pārveidot izvēlēto attēlu uz attēla pogas?',
	infoTab: 'Informācija par attēlu',
	linkTab: 'Hipersaite',
	lockRatio: 'Nemainīga Augstuma/Platuma attiecība',
	menu: 'Attēla īpašības',
	resetSize: 'Atjaunot sākotnējo izmēru',
	title: 'Attēla <PERSON>',
	titleButton: 'Attēlpogas ī<PERSON>ī<PERSON>',
	upload: 'Augšupielādēt',
	urlMissing: 'Trūkst attēla atrašanās adrese.',
	vSpace: 'Vertikālā telpa',
	validateBorder: 'Apmalei jābūt veselam skaitlim',
	validateHSpace: 'HSpace jābūt veselam skaitlim',
	validateVSpace: 'VSpace jābūt veselam skaitlim'
} );
