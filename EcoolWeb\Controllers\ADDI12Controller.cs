﻿using com.ecool.service;
using ECOOL_APP;
using ECOOL_APP.com.ecool.Models.entity;
using ECOOL_APP.com.ecool.service;
using ECOOL_APP.com.ecool.util;
using ECOOL_APP.EF;
using EcoolWeb.CustomAttribute;
using EcoolWeb.Models;
using EcoolWeb.Util;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Transactions;
using System.Web;
using System.Web.Mvc;

namespace EcoolWeb.Controllers
{
    [SessionExpire]
    public class ADDI12Controller : Controller
    {
        /// <summary>
        /// 此Controller 對應的程式代碼
        /// </summary>
        private static string Bre_NO = "ADDI12";

        private string Bre_Name = BreadcrumbService.GetBRE_NAME_forBRE_NO(Bre_NO);

        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();
        protected string _ErrorRowCellExcel = string.Empty;

        private bool IsAdmin = false;
        private string[] MustArrayTwo; //必輸欄位
        private string[] ArraySheetNames;
        /// <summary>
        /// 必輸未輸 true(錯誤) ,false(沒錯誤)
        /// </summary>
        protected bool _CheckMustErr = false;
        private string SCHOOL_NO = string.Empty;
        /// <summary>
        /// 是否Error true(錯誤) ,false(沒錯誤)
        /// </summary>
        protected bool _CheckErr = false;

        /// <summary>
        /// 格式是否Error true(錯誤) ,false(沒錯誤)
        /// </summary>
        protected bool _CheckDataTypeErr = false;

        /// <summary>
        private ADDI12Service Service = new ADDI12Service();

        /// <summary>
        /// 會員相關資訊
        /// </summary>
        private UserProfile user;

        private string USER_NO = string.Empty;
        /// <summary>
        /// 必輸欄位陣列
        /// </summary>
        private string[] MustArray; //必輸欄位

        /// <summary>
        /// PartialView 隊伍清單
        /// </summary>
        /// <param name="Item"></param>
        /// <returns></returns>
        [CheckPermission(CheckACTION_ID = "Edit")] //檢查權限
        public ActionResult _EditDetails(ADDI12EditPeopleViewModel Item)
        {
            this.Shared();
            return PartialView(Item);
        }

        /// <summary>
        /// Menu
        /// </summary>
        /// <param name="NowAction"></param>
        /// <returns></returns>
        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult _Menu(string NowAction)
        {
            this.Shared();
            ViewBag.NowAction = NowAction;

            // 檢查按鈕權限
            ViewBag.Permission = PermissionService.GetActionPermissionForBreNO(UserProfileHelper.GetBRE_NO(), user?.SCHOOL_NO, user?.USER_NO)?.Where(a => a.BoolUse)?.ToList();

            //if (user != null)
            //{
            //    this.Service.CreADDI12forNotice(user.SCHOOL_NO, user.USER_NO, ref db);
            //}

            ViewBag.PremierCount = this.Service.GetADDI12forNoticeCount(user?.SCHOOL_NO, user?.USER_NO, true, ref db);
            ViewBag.AllVideoCount = this.Service.GetADDI12forNoticeCount(user?.SCHOOL_NO, user?.USER_NO, false, ref db);

            return PartialView();
        }

        /// <summary>
        ///  顯示 首播圖片PartialView
        /// </summary>
        /// <returns></returns>
        public ActionResult _PremierViewImage()
        {
            ZZZI25Service zZZI25Service = new ZZZI25Service();
            user = UserProfileHelper.Get();
            ViewBag.ISSHOW = "N";
            List<uADDT19> Data = zZZI25Service.GetGetNowDetailsData(ADDT19.IMG_TYPEVal.PremierViewImage);
            string[] personlist = new string[2];
            if (Data != null && Data.Count() != 0 && !string.IsNullOrEmpty(Data.FirstOrDefault().CRE_PERSON) && Data.FirstOrDefault().CRE_PERSON.IndexOf("_") > 0)
            {
                personlist = Data.FirstOrDefault().CRE_PERSON.Split('_');
                if (user != null)
                {
                    if (personlist[0] == user.SCHOOL_NO)
                    {
                        ViewBag.ISSHOW = "Y";
                    }
                }
            }
            else { Data = new List<uADDT19>(); }
            ViewBag.SYSUrl = zZZI25Service.GetSYSUrl(2);

            return PartialView(Data);
        }

        /// <summary>
        /// PartialView 帶入學生明細至 _EditDetails
        /// </summary>
        /// <param name="SCHOOL_NO"></param>
        /// <param name="USER_NO"></param>
        /// <returns></returns>
        [CheckPermission(CheckACTION_ID = "Edit")] //檢查權限
        public ActionResult _OpenPersonAddData(string SCHOOL_NO, string USER_NO)
        {
            this.Shared();
            var model = this.Service.GetAddPersonData(SCHOOL_NO, USER_NO, ref db);
            return Json(model, JsonRequestBehavior.AllowGet);
        }
        [CheckPermission(CheckACTION_ID = "Edit")] //檢查權限
        public ActionResult _OpenPersonStageView(ADDI12OpenPersonViewViewModel model)
        {
            this.Shared();

            model.PersonRoleType = "stage";
            var SchoolList=(from x in db.BDMT01
                            select new ADDI12EditPeopleViewModel()
                            {
                                SCHOOL_NO = x.SCHOOL_NO,
                                SHORT_NAME = x.SHORT_NAME
                               
                            });

            List<SelectListItem> SchoolNoSelectItem = new List<SelectListItem>();
            SchoolNoSelectItem.Add(new SelectListItem() { Text = "全部", Value = "", Selected = string.IsNullOrWhiteSpace(model.WhereSCHOOL_NO) });

            SchoolNoSelectItem.AddRange(SchoolList.Select(a => new { a.SCHOOL_NO, a.SHORT_NAME }).Distinct()
                .Select(x => new SelectListItem() { Text = x.SHORT_NAME, Value = x.SCHOOL_NO, Selected = x.SCHOOL_NO == model.WhereSCHOOL_NO })
                .OrderBy(a => a.Text).ToList());

            model.SchoolNoSelectItem = SchoolNoSelectItem;



            if (model.WhereSCHOOL_NO!=null) {

                model.Search.WhereSCHOOL_NO = model.WhereSCHOOL_NO;
                model = this.Service.GetOpenPersonData(model, ref db);

            }
            SchoolNoSelectItem.Add(new SelectListItem() { Text = "全部", Value = "", Selected = string.IsNullOrWhiteSpace(model.WhereSCHOOL_NO) });

            SchoolNoSelectItem.AddRange(SchoolList.Select(a => new { a.SCHOOL_NO, a.SHORT_NAME }).Distinct()
                .Select(x => new SelectListItem() { Text = x.SHORT_NAME, Value = x.SCHOOL_NO, Selected = x.SCHOOL_NO == model.WhereSCHOOL_NO })
                .OrderBy(a => a.Text).ToList());

            model.SchoolNoSelectItem = SchoolNoSelectItem;

            return PartialView(model);
        }

        /// <summary>
        /// PartialView 學生列表查詢
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [CheckPermission(CheckACTION_ID = "Edit")] //檢查權限
        public ActionResult _OpenPersonView(ADDI12OpenPersonViewViewModel model)
        {
            this.Shared();

            model = this.Service.GetOpenPersonData(model, ref db);
            return PartialView(model);
        }

        /// <summary>
        /// Youtube 畫面
        /// </summary>
        /// <param name="STAGE_ID"></param>
        /// <returns></returns>
        public ActionResult YoutubeView(string STAGE_ID, string ActionResultType = "")
        {
            this.Shared();
            this.Service.SaveReadCount(STAGE_ID, ref db);

            ViewBag.YOUTUBE_URL = db.ADDT34.Where(a => a.STAGE_ID == STAGE_ID).Select(a => a.YOUTUBE_URL).FirstOrDefault();
            ViewBag.WinOpenYoutubeUrlLink = UrlCustomHelper.GetOwnWebUri() + $@"/EcoolWeb//ADDI12/YoutubeView?STAGE_ID={STAGE_ID}";

            string TempActionResultType = ActionResultType;
            if (string.IsNullOrWhiteSpace(TempActionResultType))
            {
                TempActionResultType = ADDI12IndexListViewModel.ActionResultTypeVal.AllVideoView;
            }
            ViewBag.ActionResultType = TempActionResultType;

            return View();
        }

        /// <summary>
        /// Youtube 畫面 PartialView
        /// </summary>
        /// <param name="STAGE_ID"></param>
        /// <returns></returns>
        public ActionResult _PartialYoutubeView(string STAGE_ID, bool? ISCARD)
        {
            if (Session["isCAR"] != null)
            {
                ISCARD = false;
            }
            this.Shared();
            string IP_ADDRESS = string.Empty;

            if (!string.IsNullOrWhiteSpace(Request.ServerVariables["HTTP_VIA"]?.ToString()))
            {
                IP_ADDRESS = Request.ServerVariables["HTTP_X_FORWARDED_FOR"]?.ToString();
            }
            else
            {
                IP_ADDRESS = Request.ServerVariables["REMOTE_ADDR"]?.ToString();
            }

            var model = Service.GetYoutubeData(STAGE_ID, IP_ADDRESS, user, ref db);

            if (model == null)
            {
                TempData["StatusMessage"] = "發生異常無此影片";
            }
            if (ISCARD != null && ISCARD == true)
            {
                TempData["ISCARD"] = ISCARD;
            }
            else
            {
                TempData["ISCARD"] = false;
            }
            Session["isCAR"] = null;
            return PartialView(model);
        }

        /// <summary>
        /// 表演人員名單
        /// </summary>
        /// <param name="STAGE_ID"></param>
        /// <returns></returns>
        public ActionResult _PartialYoutubePersonListView(string STAGE_ID)
        {
            this.Shared();
            var model = Service.GetYoutubePeopleData(STAGE_ID, ref db);
            return PartialView(model);
        }

        /// <summary>
        /// 按讚人員清單
        /// </summary>
        /// <param name="STAGE_ID"></param>
        /// <returns></returns>
        public ActionResult _LinkPersonListView(string STAGE_ID)
        {
            this.Shared();
            var model = Service.GetLinkPersonData(STAGE_ID, ref db);
            return PartialView(model);
        }
        
        
        /// <summary>
         /// 新增、修改畫面
         /// </summary>
         /// <param name="model"></param>
         /// <returns></returns>
        [CheckPermission(CheckACTION_ID = "Edit")] //檢查權限
        public ActionResult Edit1(ADDI12EditViewModel model)
        {
            ViewBag.NowAction = "Edit";
            string str = "ALL";
            var SchoolNoSelectItem = new List<SelectListItem>();

        
            ViewBag.SchoolNoSelectItem = SchoolNoSelectItem;
            this.Shared();
            if (model.Search == null) {
                model.Search = new ADDI12SearchViewModel();
                model.Search.WhereSCHOOL_NO = "ALL";
            }
            if (user.ROLE_LEVEL != 0 && user.USER_NO != "stage")
            {
                SchoolNoSelectItem = BDMT01Service.GetSCHOOL_NO(model.Search.WhereSCHOOL_NO, null, user, false, null, null, false);
                SchoolNoSelectItem = SchoolNoSelectItem.Where(a => a.Value == user.SCHOOL_NO).ToList();
            }

            if (user.ROLE_LEVEL == 0 || user.USER_NO== "stage")
            {
                SchoolNoSelectItem = BDMT01Service.GetSCHOOL_NO(model.Search.WhereSCHOOL_NO, null, user, true, str, "ALL", true,true);


            }
            ViewBag.SchoolNoSelectItem = SchoolNoSelectItem;
            if (model == null) model = new ADDI12EditViewModel();
            if (model.Search == null) model.Search = new ADDI12SearchViewModel();
            ViewBag.PremierCount = this.Service.GetADDI12forNoticeCount(user?.SCHOOL_NO, user?.USER_NO, true, ref db);
            ViewBag.AllVideoCount = this.Service.GetADDI12forNoticeCount(user?.SCHOOL_NO, user?.USER_NO, false, ref db);
            if (string.IsNullOrWhiteSpace(model.Search.WhereSCHOOL_NO))
            {
                model.Search.WhereSCHOOL_NO = SCHOOL_NO;
            }

            if (!string.IsNullOrWhiteSpace(model.Search.WhereSTAGE_ID))
            {
                model = this.Service.GetEditData(model, ref db);
            }

            if (model.Main?.STAGE_ID == null)
            {
                this.Shared(Bre_Name + "-新增舞臺");
                model.Main = new ADDI12EditMainViewModel();
            }
            else
            {
                this.Shared(Bre_Name + "-修改舞臺");
            }

            ViewBag.Live = PermissionService.GetPermission_Use_YN(UserProfileHelper.GetBRE_NO(), "Live", user?.SCHOOL_NO, user?.USER_NO);

            return View(model);
        }


        /// <summary>
        /// 新增、修改畫面
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [CheckPermission(CheckACTION_ID = "Edit")] //檢查權限
        public ActionResult Edit(ADDI12EditViewModel model)
        {
            ViewBag.NowAction = "Edit";
            this.Shared();
            if (model == null) model = new ADDI12EditViewModel();
            if (model.Search == null) model.Search = new ADDI12SearchViewModel();
            ViewBag.PremierCount = this.Service.GetADDI12forNoticeCount(user?.SCHOOL_NO, user?.USER_NO, true, ref db);
            ViewBag.AllVideoCount = this.Service.GetADDI12forNoticeCount(user?.SCHOOL_NO, user?.USER_NO, false, ref db);
            if (string.IsNullOrWhiteSpace(model.Search.WhereSCHOOL_NO))
            {
                model.Search.WhereSCHOOL_NO = SCHOOL_NO;
            }

            if (!string.IsNullOrWhiteSpace(model.Search.WhereSTAGE_ID))
            {
                model = this.Service.GetEditData(model, ref db);
            }

            if (model.Main?.STAGE_ID == null)
            {
                this.Shared(Bre_Name + "-新增舞臺");
                model.Main = new ADDI12EditMainViewModel();
            }
            else
            {
                this.Shared(Bre_Name + "-修改舞臺");
            }

            ViewBag.Live = PermissionService.GetPermission_Use_YN(UserProfileHelper.GetBRE_NO(), "Live", user?.SCHOOL_NO, user?.USER_NO);

            return View(model);
        }

        /// <summary>
        ///  Save
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [CheckPermission(CheckACTION_ID = "Edit")] //檢查權限
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult EditSave(ADDI12EditViewModel model)
        {
            this.Shared(Bre_Name + " - 編輯");
            var SchoolNoSelectItem = new List<SelectListItem>();
            string str = "";
            if (user?.USER_NO == "stage") {
               
                str = "ALL";
                SchoolNoSelectItem = BDMT01Service.GetSCHOOL_NO(model.Search.WhereSCHOOL_NO, null, user, true, str, "ALL", true, true);

            }
            else
            {
                str =user?.SCHOOL_NO;
                SchoolNoSelectItem = BDMT01Service.GetSCHOOL_NO(model.Search.WhereSCHOOL_NO, null, user, true, str, "ALL", true, true);
            }

            ViewBag.SchoolNoSelectItem = SchoolNoSelectItem;

            ViewBag.GradeItem = HRMT01.GetGradeItems(model.Search.WhereGrade);

            ViewBag.ClassItems = HRMT01.GetClassListData(model.Search.WhereSCHOOL_NO, model.Search.WhereGrade, model.Search.WhereCLASS_NO, ref db)
                .Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.Search.WhereGrade });
            if (model == null) model = new ADDI12EditViewModel();
            if (model.Search == null) model.Search = new ADDI12SearchViewModel();

            string Message = string.Empty;

            if (model.Main?.IS_PREMIER ?? false)
            {
                if (model.Main?.STAGE_DATES == null || model.Main?.STAGE_DATEE == null)
                {
                    ModelState.AddModelError("Main.IS_PREMIER", "*勾選必需填寫首播開始日期及結束日期");
                }
            }

            if (model.Details == null || model.Details?.Count() == 0) {

                ModelState.AddModelError("Main.Details", "*必需填寫隊伍人員");

            }

            if (string.IsNullOrWhiteSpace(model.Main?.YOUTUBE_IMG) && model.Main?.UploadYoutubeFile == null)
            {
                ModelState.AddModelError("Main.UploadYoutubeFile", "*封面必需上傳");
            }

            if ((model.Details?.Count ?? 0) == 0)
            {
                ModelState.AddModelError("DetailsError", "*表演者隊伍未輸入");
            }

            if (ModelState.IsValid == false)
            {
                Message = "警告!!輸入的內容有錯誤";
            }
            else
            {
                bool OK = this.Service.SaveEditData(model, user, ref db, ref Message);

                if (OK)
                {
                    ModelState.Clear();
                    TempData["StatusMessage"] = "儲存完成";

                    if (string.IsNullOrWhiteSpace(model.ActionResultType))
                    {
                        if (model.Main.IS_PREMIER && user.USER_NO != "stage")
                        {
                            model.ActionResultType = ADDI12IndexListViewModel.ActionResultTypeVal.PremierView;
                        }
                        else if (user.USER_NO == "stage") {

                            model.ActionResultType = "StageYoutubeView";

                        }
                        else
                        {
                            model.ActionResultType = ADDI12IndexListViewModel.ActionResultTypeVal.AllVideoView;
                        }
                    }

                    return RedirectToAction(model.ActionResultType);
                }
            }

            TempData["StatusMessage"] = Message;

#if DEBUG
            var errorsaa = ModelState
                        .Where(x => x.Value.Errors.Count > 0)
                        .Select(x => new { x.Key, x.Value.Errors })
                        .ToArray();
#endif
            ViewBag.Live = PermissionService.GetPermission_Use_YN(UserProfileHelper.GetBRE_NO(), "Live", user?.SCHOOL_NO, user?.USER_NO);
            if (user.USER_NO == "stage")
            {
                return View("Edit1", model);
            }
            else {

                return View("Edit", model);
            }
       
            
        }

        /// <summary>
        ///  作廢
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [CheckPermission(CheckACTION_ID = "Edit")] //檢查權限
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult DelSave(ADDI12EditViewModel model)
        {
            this.Shared(Bre_Name + " - 編輯");

            if (model == null) model = new ADDI12EditViewModel();
            if (model.Search == null) model.Search = new ADDI12SearchViewModel();

            string Message = string.Empty;

            bool OK = this.Service.SaveDelData(model, user, ref db, ref Message);
            if (OK)
            {
                ModelState.Clear();
                TempData["StatusMessage"] = "作廢完成";

                if (string.IsNullOrWhiteSpace(model.ActionResultType))
                {
                    if (model.Main.IS_PREMIER)
                    {
                        model.ActionResultType = ADDI12IndexListViewModel.ActionResultTypeVal.PremierView;
                    }
                    else
                    {
                        model.ActionResultType = ADDI12IndexListViewModel.ActionResultTypeVal.AllVideoView;
                    }
                }

                return RedirectToAction(model.ActionResultType);
            }

            TempData["StatusMessage"] = Message;

#if DEBUG
            var errorsaa = ModelState
                        .Where(x => x.Value.Errors.Count > 0)
                        .Select(x => new { x.Key, x.Value.Errors })
                        .ToArray();
#endif

            return View("Edit", model);
        }
        [HttpPost]
        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        [ValidateAntiForgeryToken]
        public ActionResult ExcelView(HttpPostedFileBase files, string SYS_TABLE_TYPE, string ADDT14_STYLE)
        {
            string ErrorMsg = "";
            if (files == null || files.FileName == string.Empty) ModelState.AddModelError("files", "請上傳檔案");
            else if (files != null)
            {
                Regex regexCode = new Regex(@".*\.(xls|xlsx)");

                string fileNameExtension = Path.GetExtension(files.FileName);

                if (regexCode.IsMatch(fileNameExtension.ToLower()) == false)
                {
                    ModelState.AddModelError("files", "請上傳Excel格式為xls、xlsx");
                }
            }

            if (ModelState.IsValid == false)
            {
                ErrorMsg = "錯誤\r\n";
                return View();
            }
            else
            {
                string ThisBATCH_CASH_ID = string.Empty;
                var OK = this.ExcelData(files, SYS_TABLE_TYPE, ADDT14_STYLE, ref ThisBATCH_CASH_ID);
                if (OK)
                {
                    return RedirectToAction("StageYoutubeView");
                }
                else
                {
                    return View();
                }

            }
                return View();
           
        }
        #region 作用:NPOI EXCEL內容資料型態數誤，顯示的錯誤訊息內容

        protected void NPOI_DataTypeConflict(object sender, DataRowCellFilledArgs e)
        {
            _CheckErr = true;
            _CheckDataTypeErr = true;
            _ErrorRowCellExcel = _ErrorRowCellExcel + "Sheet名稱【" + e.SheetName + "】- EXCEL內容資料型態錯誤,欄位名稱【" + e.ColCaption + "】，第" + e.RowNo.ToString() + "列-儲存格內容為「" + e.CellToString + "」<br/>";
        }

        #endregion 作用:NPOI EXCEL內容資料型態數誤，顯示的錯誤訊息內容
        /// <summary>
        /// Excel 匯入處理
        /// </summary>
        /// <param name="files"></param>
        /// <returns></returns>
        private bool ExcelData(HttpPostedFileBase files, string SYS_TABLE_TYPE, string ADDT14_STYLE, ref string OutBATCH_CASH_ID)
        {
            ECOOL_DEVEntities Db = new ECOOL_DEVEntities();
            bool ReturnBool = true;

            user = UserProfileHelper.Get();
        
            NPOIHelper npoi = new NPOIHelper(); //NEW NPOIHelper
            npoi.onDataTypeConflict += new DataRowCellHandler(this.NPOI_DataTypeConflict); //宣告使用 輸入內容的型態 是否設定 及 Excel存儲格式一樣 ex. 日期 輸入[aaa] ，Excel存儲格式設日期，或欄位第一行有定義 System.DateTime
            npoi.onLineCheckValue += new DataRowCellHandler(this.NPOI_LineCheckValue);
            ArraySheetNames = new string[] { "主檔" }; //Excel Sheet名稱,因為是params,所以可以傳入多個(也會變成DataTable Name)
            MustArray = new string[] { "SCHOOL_NO", "STAGE_NAME", "YOUTUBE_URL"}; // Excel 必輸欄位
            string _Error;
            try
            {
                DataSet ds = npoi.Excel2Table(files.InputStream, 0, 2, 1, ArraySheetNames);
                if (ds == null)
                {
                    _Error = "上傳錯誤，上傳Excel不符合規定，上傳之 Excel 檔, 請依規定格式填寫，請先下載Sample Excel";
                    TempData["StatusMessage"] = _Error;
                    ReturnBool = false;
                    return ReturnBool;
                }

                ///讀取資料筆數為0
                if (ds.Tables.Count == 0)
                {
                    _Error = "上傳錯誤，錯誤原因如下:<br><br>讀取資料筆數為0，請確認 上傳Excel的 Sheet Name 是否有以下 ";
                    foreach (string ThisSheet in ArraySheetNames)
                    {
                        _Error = _Error + "【" + ThisSheet.ToString() + "】";
                    }
                    _Error = _Error + " Sheet Name ，請確認";

                    TempData["StatusMessage"] = _Error;

                    ReturnBool = false;
                    return ReturnBool;
                }
                ///EXCEL內容資料型態數誤
                if (_CheckDataTypeErr || _CheckMustErr)
                {
                    _Error = "上傳錯誤，錯誤原因如下:<br><br> " + _ErrorRowCellExcel;
                    TempData["StatusMessage"] = _Error;
                    ReturnBool = false;
                    return ReturnBool;
                }
                DataTable dtUSER_NO = ds.Tables[ArraySheetNames[0]];

                int SYear;
                int Semesters;
                SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);

                int NUM = 0;

        
                foreach (DataRow Row in dtUSER_NO.Rows)
                {
                    ADDT34 Cre = null;
                    string IS_PREMIER = "TRUE";
                    DateTime startDate = new DateTime();
                    DateTime EndDate = new DateTime();
                    if (Row.IsNull("IS_PREMIER")) {
                        IS_PREMIER = "FALSE";

                    }
                    if (Row.IsNull("STAGE_DATES")){

                        startDate = DateTime.Now;
                        startDate.ToString("yyyy/MM/dd HH:mm:ss.fff");
                    }
                    if (Row.IsNull("STAGE_DATEE"))
                    {

                        EndDate = DateTime.Now.AddMonths(3);
                    }
                    Cre = new ADDT34
                    {
                        STAGE_ID = Guid.NewGuid().ToString("N"),
                        SCHOOL_NO = Row["SCHOOL_NO"].ToString(),
                        STAGE_NAME = Row["STAGE_NAME"].ToString(),
                        CRE_PERSON = user.USER_KEY,
                        CRE_DATE = DateTime.Now,
                        CHG_PERSON = user.USER_KEY,
                        CHG_DATE = DateTime.Now,
                        YOUTUBE_URL = Row["YOUTUBE_URL"].ToString(),

                        IS_PREMIER = bool.Parse(IS_PREMIER),
                        STAGE_DATES = DateTime.Parse(startDate.ToString("yyyy/MM/dd HH:mm:ss.fff")),
                        STAGE_DATEE = DateTime.Parse(EndDate.ToString("yyyy/MM/dd HH:mm:ss.fff")),
                        STATUS = Convert.ToByte(TableStatus.OK),
                    };
                    Db.ADDT34.Add(Cre);
                }
                Db.SaveChanges();
                }
            catch (Exception e)
            {
                _Error = "上傳錯誤，錯誤原因如下:<br><br>" + e;
                TempData["StatusMessage"] = _Error;
                ReturnBool = false;
                return ReturnBool;

            }
            return ReturnBool;
        }
        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult ExcelView()
        {
      
            this.Shared(Bre_Name+"- Excel匯入");
            ViewBag.NowAction = "ExcelView";
            return View();
        }

        protected void NPOI_LineCheckValue(object sender, DataRowCellFilledArgs e)
        {
            if (e.SheetName == ArraySheetNames[0])
            {
                if (!Convert.IsDBNull(e.Row["SCHOOL_NO"]))
                {
                    foreach (var item in MustArray)
                    {
                        if (Convert.IsDBNull(e.Row[item]) || (e.Row[item] ?? "").ToString() == "")
                        {
                            _CheckErr = true;
                            _CheckMustErr = true;
                            _ErrorRowCellExcel = _ErrorRowCellExcel + "Sheet名稱【" + e.SheetName + "】-欄位名稱【" + e.ColCaption + "】，第" + e.RowNo.ToString() + "列-必需輸入<br/>";
                        }
                    }
                }
            }
         
        }
        /// <summary>
        /// 說明
        /// </summary>
        /// <returns></returns>
        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult Index()
        {
            user = UserProfileHelper.Get();
            //if (user == null)
            //{
            //    return RedirectToAction("PermissionError1999", "Error");
            //}
            this.Shared(Bre_Name + "-說明");
            ViewBag.NowAction = "Index";

            ViewBag.SEXPLAIN = db.BDMT01.Where(a => a.SCHOOL_NO == SCHOOL_NO).Select(a => a.ADDI12SEXPLAIN).FirstOrDefault();

            if (user != null)
            {
                this.Service.CreADDI12forNotice(user.SCHOOL_NO, user.USER_NO, ref db);
            }

            ViewBag.PremierCount = this.Service.GetADDI12forNoticeCount(user?.SCHOOL_NO, user?.USER_NO, true, ref db);
            ViewBag.AllVideoCount = this.Service.GetADDI12forNoticeCount(user?.SCHOOL_NO, user?.USER_NO, false, ref db);

            return View();
        }
        public ActionResult StageYoutubeView(ADDI12IndexListViewModel model) {
            this.Shared(Bre_Name + "- 所有影片");
            return View();

        }
        public ActionResult _StagePageContent(ADDI12IndexListViewModel model) {

            ADDI12Service aDDI12Service = new ADDI12Service();
            this.Shared();
            string str = "ALL";
            if (model == null) model = new ADDI12IndexListViewModel();
            if (model.Search == null) model.Search = new ADDI12SearchViewModel();
            model = Service.GetstageListData(model, user, ref db);
            var SchoolNoSelectItem = new List<SelectListItem>();
            SchoolNoSelectItem = BDMT01Service.GetSCHOOL_NO(model.Search.WhereSCHOOL_NO, null, user, true, str, "ALL", true, true);
            ViewBag.SchoolNoSelectItem = SchoolNoSelectItem;
            ViewBag.GradeItem = HRMT01.GetGradeItems(model.Search.WhereGrade);

            ViewBag.ClassItems = HRMT01.GetClassListData(model.Search.WhereSCHOOL_NO, model.Search.WhereGrade, model.Search.WhereCLASS_NO, ref db)
                .Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.Search.WhereGrade });
            foreach (var i in model.ListData.ToList())
            {
                string TITLE_IMG_Path = aDDI12Service.GetSysPath(i.SCHOOL_NO, i.STAGE_ID) + $@"\Small\";
                string UpLoadFile = TITLE_IMG_Path + "\\" + i.YOUTUBE_IMG;
                if (!System.IO.File.Exists(UpLoadFile))
                {
                    i.YOUTUBE_SmallURL = "";
                }
            }
            return PartialView(model);
        }
        /// <summary>
        /// 全部影片
        /// </summary>
        /// <returns></returns>
        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult AllVideoView(ADDI12IndexListViewModel model)
        {
            this.Shared(Bre_Name + " - 一般影片");
            ViewBag.NowAction = "AllVideoView";
            ViewBag.PremierCount = this.Service.GetADDI12forNoticeCount(user?.SCHOOL_NO, user?.USER_NO, true, ref db);
            ViewBag.AllVideoCount = this.Service.GetADDI12forNoticeCount(user?.SCHOOL_NO, user?.USER_NO, false, ref db);

            if ((ViewBag.AllVideoCount ?? 0) > 0)
            {
                this.Service.UpdateADDI12forNotice(user?.SCHOOL_NO, user?.USER_NO, false, ref db);
            }

            return View(model);
        }

        /// <summary>
        /// 我上傳的影片
        /// </summary>
        /// <returns></returns>
        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult MyUploadVideoView(ADDI12IndexListViewModel model)
        {
            this.Shared(Bre_Name + " - 我上傳的影片");
            return View(model);
        }

        /// <summary>
        /// 我的影片
        /// </summary>
        /// <returns></returns>
        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult MyVideoView(ADDI12IndexListViewModel model)
        {
            this.Shared(Bre_Name + " - 我的影片");

            return View(model);
        }

        /// <summary>
        /// 首播影片
        /// </summary>
        /// <returns></returns>
        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult PremierView(ADDI12IndexListViewModel model)
        {
            this.Shared(Bre_Name + " - 首播影片");
            ViewBag.NowAction = "PremierView";
            ViewBag.PremierCount = this.Service.GetADDI12forNoticeCount(user?.SCHOOL_NO, user?.USER_NO, true, ref db);
            ViewBag.AllVideoCount = this.Service.GetADDI12forNoticeCount(user?.SCHOOL_NO, user?.USER_NO, false, ref db);
            if ((ViewBag.PremierCount ?? 0) > 0)
            {
                this.Service.UpdateADDI12forNotice(user?.SCHOOL_NO, user?.USER_NO, true, ref db);
            }
            return View(model);
        }

        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult _ListBeginForm(ADDI12IndexListViewModel model)
        {
            this.Shared();
            return PartialView(model);
        }

        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult _StageListBeginForm(ADDI12IndexListViewModel model)
        {
            this.Shared(Bre_Name + "- 所有影片");
            return PartialView(model);
        }
        /// <summary>
        /// PartialView 查詢列表
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult _PageContent(ADDI12IndexListViewModel model)
        {
            ADDI12Service aDDI12Service = new ADDI12Service();
            this.Shared();
            if (model == null) model = new ADDI12IndexListViewModel();
            if (model.Search == null) model.Search = new ADDI12SearchViewModel();

            if (string.IsNullOrWhiteSpace(model.Search.WhereSCHOOL_NO))
            {
                model.Search.WhereSCHOOL_NO = SCHOOL_NO;
            }

            ViewBag.SYEARItems = HRMT01.GetSYearsItems(model.Search.WhereSYEAR.ToString(), ref db);

            ViewBag.GradeItem = HRMT01.GetGradeItems(model.Search.WhereGrade);

            ViewBag.ClassItems = HRMT01.GetClassListData(model.Search.WhereSCHOOL_NO, model.Search.WhereGrade, model.Search.WhereCLASS_NO, ref db)
                .Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.Search.WhereGrade });

            model = Service.GetListData(model, user, ref db);
            foreach (var i in model.ListData.ToList())
            {
                string TITLE_IMG_Path = aDDI12Service.GetSysPath(i.SCHOOL_NO, i.STAGE_ID) + $@"\Small\";
                string UpLoadFile = TITLE_IMG_Path + "\\" + i.YOUTUBE_IMG;
                if (!System.IO.File.Exists(UpLoadFile))
                {
                    i.YOUTUBE_SmallURL = "";
                }
            }
            return PartialView(model);
        }

        /// <summary>
        /// 首頁播放首播
        /// </summary>
        /// <returns></returns>
        public ActionResult _HomeIndexPartialView()
        {
            this.Shared();

            ADDI12IndexListViewModel TempModel = new ADDI12IndexListViewModel
            {
                ActionResultType = ADDI12IndexListViewModel.ActionResultTypeVal.PremierView,
                Search = new ADDI12SearchViewModel
                {
                    WhereSCHOOL_NO = SCHOOL_NO
                }
            };

            var model = Service.GetRandPremier(TempModel, user, db);

            return PartialView(model);
        }

        /// <summary>
        /// 按讚
        /// </summary>
        /// <param name="STAGE_ID"></param>
        /// <returns></returns>
        public JsonResult Share_Save(string STAGE_ID)
        {
            this.Shared();
            int SuccessType = 0;
            string Message = string.Empty;

            string IP_ADDRESS = string.Empty;

            if (!string.IsNullOrWhiteSpace(Request.ServerVariables["HTTP_VIA"]?.ToString()))
            {
                IP_ADDRESS = Request.ServerVariables["HTTP_X_FORWARDED_FOR"]?.ToString();
            }
            else
            {
                IP_ADDRESS = Request.ServerVariables["REMOTE_ADDR"]?.ToString();
            }
            List<Tuple<string, string, int>> valuesList = new List<Tuple<string, string, int>>();
            bool OK = Service.SaveLikeData(STAGE_ID, IP_ADDRESS, user, ref db, ref Message,ref valuesList);
            if (OK)
            {
                SuccessType = 1;
            }

            var data = "{ \"Success\" : \"" + SuccessType.ToString() + "\" , \"Error\" : \"" + Message + "\" }";
            Session["isCAR"] = false;
            return Json(@data, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// 按讚
        /// </summary>
        /// <param name="STAGE_ID"></param>
        /// <returns></returns>
        public JsonResult Share_Save2(string STAGE_ID, string CARD_ID)
        {
            TempData["isCAR"] = 1;

            this.Shared();
            int SuccessType = 0;
            string Message = string.Empty;
            List<Tuple<string, string, int>> valuesList = new List<Tuple<string, string, int>>();
            string IP_ADDRESS = string.Empty;

            if (!string.IsNullOrWhiteSpace(Request.ServerVariables["HTTP_VIA"]?.ToString()))
            {
                IP_ADDRESS = Request.ServerVariables["HTTP_X_FORWARDED_FOR"]?.ToString();
            }
            else
            {
                IP_ADDRESS = Request.ServerVariables["REMOTE_ADDR"]?.ToString();
            }

            IsAdmin = (user.USER_TYPE == UserType.Admin || user.USER_TYPE == UserType.Teacher);
            bool OK = false;
            if (IsAdmin)
            {
                HRMT01 FindUser = db.HRMT01.Include("HRMT25").Where(a => a.SCHOOL_NO == user.SCHOOL_NO && a.CARD_NO == CARD_ID).FirstOrDefault();
                if (FindUser == null)
                {
                    FindUser = db.HRMT01.Include("HRMT25").Where(a => a.SCHOOL_NO == user.SCHOOL_NO && a.USER_NO == CARD_ID).FirstOrDefault();
                }
                if (FindUser == null)
                {
                    Message = "此張數位學生證卡號無效，卡號:" + CARD_ID;
                    //TempData["StatusMessage"] = Message;
                    //TempData["Status"] = "error";
                }
                else
                {
                    UserProfile user2 = UserProfile.FillUserProfile(FindUser);
                    OK = Service.SaveLikeData(STAGE_ID, IP_ADDRESS, user2, ref db, ref Message,ref valuesList);
                }
            }
            else
            {
                OK = Service.SaveLikeData(STAGE_ID, IP_ADDRESS, user, ref db, ref Message,ref valuesList);
            }

            if (OK)
            {
                SuccessType = 1;
            }

            var data = "{ \"Success\" : \"" + SuccessType.ToString() + "\" , \"Error\" : \"" + Message + "\" }";
            return Json(@data, JsonRequestBehavior.AllowGet);
        }

        /// <summary>解析URL參數</summary>
        /// <param name="StrUrl"></param>
        /// <returns></returns>
        public JsonResult GetUrlArgument(string StrUrl)
        {
            int SuccessType = 0;
            string Message = string.Empty;

            var EmbedYouTubeUrl = YoutubeHelper.ConvertToEmbedYouTubeVideo(StrUrl, ref Message);

            if (string.IsNullOrWhiteSpace(Message))
            {
                SuccessType = 1;
            }

            var data = "{ \"Success\" : \"" + SuccessType.ToString() + "\" ,\"EmbedYouTubeUrl\" : \"" + EmbedYouTubeUrl + "\", \"Error\" : \"" + Message + "\" }";
            return Json(@data, JsonRequestBehavior.AllowGet);
        }

        #region Shared

        private void SetTitle(string Panel_Title = "")
        {
            if (!string.IsNullOrWhiteSpace(Panel_Title))
            {
                ViewBag.Panel_Title = Panel_Title;
            }
            else
            {
                ViewBag.Panel_Title = Bre_Name + " - 列表 ";
            }
        }

        private void Shared(string Panel_Title = "")
        {
            ViewBag.BRE_NO = Bre_NO;

            if (!string.IsNullOrWhiteSpace(Panel_Title))
            {
                ViewBag.Panel_Title = Panel_Title;
            }
            else
            {
                ViewBag.Panel_Title = Bre_Name + " - 列表 ";
            }

            user = UserProfileHelper.Get();
            SCHOOL_NO = UserProfileHelper.GetSchoolNo();

            if (user != null)
            {
                if (user.SCHOOL_NO != null)
                {
                    SCHOOL_NO = user.SCHOOL_NO;
                    USER_NO = user.USER_NO;
                }
            }
            IsAdmin = HRMT24_ENUM.CheckQAdmin(user);
            ViewBag.IsAdmin = IsAdmin;
        }

        #endregion Shared

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}