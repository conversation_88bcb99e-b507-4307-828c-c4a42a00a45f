﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Text;

namespace ECOOL_APP.EF
{
    public class GameScoreListPersonsViewModel
    {
        /// <summary>
        ///流水號
        /// </summary>
        [DisplayName("流水號")]
        public string ANSWER_ID { get; set; }

        /// <summary>
        ///學校簡稱
        /// </summary>
        [DisplayName("學校簡稱")]
        public string SHORT_NAME { get; set; }

        /// <summary>
        ///學校簡稱
        /// </summary>
        [DisplayName("學校")]
        public string SCHOOL_NO { get; set; }

        /// <summary>
        ///帳號
        /// </summary>
        [DisplayName("學號")]
        public string USER_NO { get; set; }

        /// <summary>
        ///班級
        /// </summary>
        [DisplayName("班級")]
        public string CLASS_NO { get; set; }

        /// <summary>
        ///座號
        /// </summary>
        [DisplayName("座號")]
        public string SEAT_NO { get; set; }

        /// <summary>
        ///姓名
        /// </summary>
        [DisplayName("姓名")]
        public string NAME { get; set; }

        /// <summary>
        ///分數
        /// </summary>
        [DisplayName("分數")]
        public int? SCORE { get; set; }

        [DisplayName("獲得點數")]
        public int? REWARD_CASH { get; set; }

        /// <summary>
        ///狀態 1.答作中 2 回答完畢
        /// </summary>
        [DisplayName("狀態 1.答作中 2 回答完畢")]
        public byte? STATUS { get; set; }

        /// <summary>
        ///是否為最後一次成積
        /// </summary>
        [DisplayName("是否為最後一次成積")]
        public bool? IS_LAST_SCORE { get; set; }

        /// <summary>
        ///建立日期
        /// </summary>
        [DisplayName("答題時間")]
        public DateTime? CRE_DATE { get; set; }

        /// <summary>
        /// 每題的成績
        /// </summary>
        public List<ADDT26_DAns> ScoreListData { get; set; }
    }
}