﻿using DotNet.Highcharts;
using ECOOL_APP.com.ecool.Models.DTO;
using ECOOL_APP.com.ecool.Models.entity;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class ADDI05TotalGraphViewModel
    {
        public ADDI05TotalGraphViewModel()
        {
            TrueCount = 0;
            FalseCount = 0;
            AllCount = 0;
        }

        public string DIALOG_ID { get; set; }
        public string SearchContents { get; set; }
        public string OrderByName { get; set; }
        public string SyntaxName { get; set; }
        public int? page { get; set; }

        public ADDI05DetailViewModel UADDT11 { get; set; }
        public List<uADDT12> UADDT12 { get; set; }

        public List<ADDI05TotalGraphDetailsListViewModel> AnsLists { get; set; }

        public List<ADDI05TotalGraphRightCountViewModel> RightCountData { get; set; }

        public double TrueCount { get; set; }

        public double TrueRate { get; set; }

        public double FalseCount { get; set; }

        public double FalseRate { get; set; }

        public double AllCount { get; set; }
    }

    public class ADDI05TotalGraphRightCountViewModel
    {
        public string DIALOG_ID { get; set; }

        /// <summary>
        ///題目序號
        /// </summary>
        [DisplayName("題目序號")]
        public int? Q_NUM { get; set; }

        public string RIGHT_YN { get; set; }

        public string ANSWER { get; set; }

        /// <summary>
        ///回答這個答案人數
        /// </summary>
        [DisplayName("回答這個答案人數")]
        public double QTY { get; set; }

        /// <summary>
        ///總回答人數
        /// </summary>
        [DisplayName("總回答人數")]
        public double TOT_COUNT { get; set; }

        /// <summary>
        /// 百分比
        /// </summary>
        public double RATE { get; set; }
    }
}