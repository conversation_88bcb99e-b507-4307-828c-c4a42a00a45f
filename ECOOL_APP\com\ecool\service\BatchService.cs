﻿using com.ecool.sqlConnection;
using ECOOL_APP.com.ecool.util;
using ECOOL_APP.EF;
using log4net;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;


namespace ECOOL_APP.com.ecool.service
{
    public static class BatchService
    {
        private static ILog logger = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        /// <summary>
        /// 批次塞資料，同時給點數
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="Data">T</param>
        /// <param name="SourceType">來源程式(Controller)</param>
        /// <param name="SourceNo">來源單號</param>
        /// <param name="TableName">來源Table</param>
        /// <param name="LogDesc">在Log紀錄上的補充說明</param>
        /// <param name="Msg">回傳訊息</param>
        public static bool BatchDatAddCasha<T>( this IEnumerable<T> Data, string SourceType, string SourceNo, string TableName, string LogDesc,string SessionID,string LOG_TABLE, UserProfile user, out string Msg, List<APPT02> PushList,HRMT01 hRMT01,string Barcode)
        {
            bool ReturnBool = false;

            logger.Info("紙本酷幣點數 ADDT14 系統操作 BatchDatAddCasha");

            using (SqlConnection conn = new sqlConnection().getConnection4Query())
            {

                try
                {
                    SqlTransaction trans = conn.BeginTransaction();

                    using (SqlBulkCopy sbCopy = new SqlBulkCopy(conn, SqlBulkCopyOptions.FireTriggers, trans))
                    {
                        try
                        {
                            sbCopy.DestinationTableName = TableName;
                            sbCopy.WriteToServer(Data.AsDataTable());
                            if (hRMT01 == null)
                            {
                                CashHelper.BatchAddCash(user, SessionID, Data.AsDataTable(), SourceType, SourceNo, LogDesc, LOG_TABLE, sbCopy, conn, trans);
                            }
                            else {
                                if (hRMT01.USER_TYPE != "S")
                                {

                                    string user_key = "系統操作";
                                    if (user != null)
                                    {
                                        user_key = user.USER_KEY;
                                    }
                                    else {
                                        if (SourceType == "ADDI13")
                                        {
                                            user = new UserProfile();
                                            user.USER_KEY = hRMT01.USER_KEY;
                                        }
                                    }
                                   
                                    CashHelper.TeachAddCashTolottery(hRMT01.SCHOOL_NO, hRMT01.USER_NO, hRMT01.USER_TYPE, user_key, SessionID, Data.AsDataTable(), SourceType, SourceNo, LogDesc, sbCopy, conn, trans,Barcode);


                                }
                                else {
                                    string user_key = "系統操作";
                                    if (user != null)
                                    {
                                        user_key = user.USER_KEY;
                                    }
                                    else
                                    {
                                        if (SourceType == "ADDI13" && hRMT01!=null) 
                                        {
                                            user = new UserProfile();
                                            user.USER_KEY = hRMT01.USER_KEY;
                                            logger.Info("紙本酷幣點數 ADDT14 系統操作 BatchDatAddCasha");
                                        }
                                    }

                                    logger.Info("紙本酷幣點數 ADDT14 系統操作 BatchDatAddCasha"+ LogDesc);

                                    CashHelper.BatchAddCashTolottery(hRMT01.SCHOOL_NO, hRMT01.USER_NO, hRMT01.USER_TYPE, user_key, SessionID, Data.AsDataTable(), SourceType, SourceNo, LogDesc, sbCopy, conn, trans, Barcode);
                                }
                            }
                            //Path
                            sbCopy.DestinationTableName = "APPT02";
                            sbCopy.WriteToServer(PushList.AsDataTable());


                            Msg = string.Empty;
                            trans.Commit();
                            ReturnBool = true;
                        }
                        catch (Exception ex)
                        {
                            logger.Info("紙本酷幣點數 ADDT14 系統操作 BatchDatAddCasha" + ex.InnerException);
                            Msg = "新增資料失敗;\r\n" + ex.Message;
                            trans.Rollback();
                        }
                        finally
                        {
                            trans.Dispose();
                        }
                    }
                }
                finally
                {
                    if (conn != null)
                    {
                        conn.Close();
                        conn.Dispose();
                    }
                }
            }

            return ReturnBool;
        }
    }

}
