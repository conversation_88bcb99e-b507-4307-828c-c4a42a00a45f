﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'table', 'nl', {
	border: 'Randdikte',
	caption: 'Titel',
	cell: {
		menu: 'Cel',
		insertBefore: 'Voeg cel in voor',
		insertAfter: 'Voeg cel in na',
		deleteCell: 'Cellen verwijderen',
		merge: 'Cellen samenvoegen',
		mergeRight: 'Voeg samen naar rechts',
		mergeDown: 'Voeg samen naar beneden',
		splitHorizontal: 'Splits cel horizontaal',
		splitVertical: 'Splits cel vertikaal',
		title: 'Celeigenschappen',
		cellType: 'Celtype',
		rowSpan: 'Rijen samenvoegen',
		colSpan: 'Kolommen samenvoegen',
		wordWrap: 'Automatische terugloop',
		hAlign: 'Horizontale uitlijning',
		vAlign: 'Verticale uitlijning',
		alignBaseline: '<PERSON><PERSON><PERSON><PERSON>',
		bgColor: 'Acht<PERSON>grondkleur',
		borderColor: 'Randkleur',
		data: 'Gegeven<PERSON>',
		header: 'Kop',
		yes: 'Ja',
		no: 'Nee',
		invalidWidth: 'De celbreedte moet een getal zijn.',
		invalidHeight: 'De celhoogte moet een getal zijn.',
		invalidRowSpan: 'Rijen samenvoegen moet een heel getal zijn.',
		invalidColSpan: 'Kolommen samenvoegen moet een heel getal zijn.',
		chooseColor: 'Kies'
	},
	cellPad: 'Celopvulling',
	cellSpace: 'Celafstand',
	column: {
		menu: 'Kolom',
		insertBefore: 'Voeg kolom in voor',
		insertAfter: 'Voeg kolom in na',
		deleteColumn: 'Kolommen verwijderen'
	},
	columns: 'Kolommen',
	deleteTable: 'Tabel verwijderen',
	headers: 'Koppen',
	headersBoth: 'Beide',
	headersColumn: 'Eerste kolom',
	headersNone: 'Geen',
	headersRow: 'Eerste rij',
	invalidBorder: 'De randdikte moet een getal zijn.',
	invalidCellPadding: 'Celopvulling moet een getal zijn.',
	invalidCellSpacing: 'Celafstand moet een getal zijn.',
	invalidCols: 'Het aantal kolommen moet een getal zijn groter dan 0.',
	invalidHeight: 'De tabelhoogte moet een getal zijn.',
	invalidRows: 'Het aantal rijen moet een getal zijn groter dan 0.',
	invalidWidth: 'De tabelbreedte moet een getal zijn.',
	menu: 'Tabeleigenschappen',
	row: {
		menu: 'Rij',
		insertBefore: 'Voeg rij in voor',
		insertAfter: 'Voeg rij in na',
		deleteRow: 'Rijen verwijderen'
	},
	rows: 'Rijen',
	summary: 'Samenvatting',
	title: 'Tabeleigenschappen',
	toolbar: 'Tabel',
	widthPc: 'procent',
	widthPx: 'pixels',
	widthUnit: 'eenheid breedte'
} );
