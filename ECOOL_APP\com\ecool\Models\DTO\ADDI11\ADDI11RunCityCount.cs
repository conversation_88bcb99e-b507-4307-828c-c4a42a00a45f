﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
   public class ADDI11RunCityCount
    {
        public string WhereSCHOOL_NO { get; set; }
        public string WhereKMS { get; set; }
        public string SCHOOL_NO { get; set; }
        public string USER_NO { get; set; }
        public string B<PERSON><PERSON>OR { get; set; }
        public string CLASS_NO { get; set; }
    
        public double? CLASSSUMKM { get; set; }
        public double? AVGCLASS { get; set; }
        public double? RUN_TOTAL_METER { get; set; }
        [DisplayName("公里數")]
        public decimal RUN_TOTAL_KM { get; set; }
        [DisplayName("跑到地區")]
        public string LocalName { get; set; }
        [DisplayName("人數")]
        public int LocalNameCount { get; set; }
        [DisplayName("班級人數")]
        public int CLASSCount { get; set; }
        public List<ADDI11PeopleEditList> ADDI11PeopleEditListInfo { get; set; }
    }
}
