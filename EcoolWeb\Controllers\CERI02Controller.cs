﻿using com.ecool.service;
using ECOOL_APP;
using ECOOL_APP.com.ecool.service;
using ECOOL_APP.EF;
using EcoolWeb.CustomAttribute;
using EcoolWeb.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace EcoolWeb.Controllers
{
    /// <summary>
    /// 護照明細設定
    /// </summary>
    [SessionExpire]
    [CheckPermissionSeeion(CheckACTION_ID = "Index")] //檢查權限
    public class CERI02Controller : Controller
    {
        /// <summary>
        /// 此Controller 對應的程式代碼
        /// </summary>
        private static string Bre_NO = "CERI02";

        private string Bre_Name = BreadcrumbService.GetBRE_NAME_forBRE_NO(Bre_NO);

        /// <summary>
        /// 會員相關資訊
        /// </summary>
        private UserProfile user;

        private string SCHOOL_NO = string.Empty;
        private string USER_NO = string.Empty;

        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();
        private CERI02Service Service = new CERI02Service();
        private CERI01Service eRI01Service = new CERI01Service();
        private UserOpenService userOpenService = new UserOpenService();

        #region 列表畫面

        public ActionResult Index()
        {
            this.Shared();

            if (eRI01Service.IsAccreditationTypeforSchool(SCHOOL_NO, ref db) == false)
            {
                TempData[SharedGlobal.StatusMessageName] = $"「{BreadcrumbService.GetBRE_NAME_forBRE_NO("CERI01")}」未設定，請先行設定，才執行此作業。";
            }

            return View();
        }

        public ActionResult _PageContent(AccreditationManageIndexViewModel model)
        {
            this.Shared();
            if (model == null) model = new AccreditationManageIndexViewModel();
            model = Service.GetListData(model, SCHOOL_NO, ref db);
            ViewBag.AccreditationTypeItems = eRI01Service.GetSelectAccreditationTypeItems(model?.WhereACCREDITATION_TYPE, SCHOOL_NO, ref db, "全部");
            ViewBag.AccreditationsItems = Service.GetSelectAccreditationsItems(model.WhereACCREDITATION_NAME, SCHOOL_NO, ref db, "全部");

            return PartialView(model);
        }

        #endregion 列表畫面

        #region 編輯畫面及處理

        public ActionResult Edit(AccreditationManageEditViewModel model)
        {
            this.Shared("編輯");

            if (model == null) model = new AccreditationManageEditViewModel();

            if (!string.IsNullOrWhiteSpace(model.Keyword))
            {
                model = this.Service.GetEditData(model, ref db);
            }

            ViewBag.AccreditationTypeItems = eRI01Service.GetSelectAccreditationTypeItems(model.Main?.ACCREDITATION_TYPE, SCHOOL_NO, ref db);

            return View(model);
        }
        public ActionResult _Ceri02Menu(string NowAction)
        {
            string ISTASKLIST = Request.QueryString["ISTASKLIST"];
            ViewBag.ISTASKLIST = ISTASKLIST;
            this.Shared();
            ViewBag.NowAction = NowAction;

         
         

            return PartialView();
        }
        /// <summary>
        /// 護照明細-通過條件
        /// </summary>
        /// <param name="Item"></param>
        /// <returns></returns>
        public ActionResult _Details(AccreditationManageEditDetailsViewModel Item)
        {
            return PartialView("_Details", Item);
        }

        //認證老師
        public ActionResult _DetailsV(HRMT01 Item)
        {
            return PartialView("_DetailsV", Item);
        }

        //認證老師
        public ActionResult _DetailsVData(string USER_KEY)
        {
            var Item = userOpenService.GetUserData(USER_KEY, ref db);

            return PartialView("_DetailsV", Item);
        }

        /// <summary>
        /// 編輯 Save
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [ValidateAntiForgeryToken]
        [HttpPost]
        public ActionResult EditSave(AccreditationManageEditViewModel model)
        {
            this.Shared("編輯");

            if (model == null) model = new AccreditationManageEditViewModel();

            string Message = string.Empty;

            if (model.Main == null)
            {
                return RedirectToAction("Edit");
            }

            if ((model.ContentData?.Count ?? 0) == 0)
            {
                ModelState.AddModelError("", "護照第二層設定未設定");
            }
            else
            {
                if (model.ContentData.Where(a => a.GRADE_SEMESTERs?.Count == 0).Any())
                {
                    ModelState.AddModelError("", "護照第二層設定-認證時間未設定完成");
                }
            }

            if (ModelState.IsValid == false)
            {
                Message = "警告!輸入的內容有錯誤";
            }
            else
            {
                var Result = this.Service.SaveEditData(model, SCHOOL_NO, user, ref db);

                if (Result.Success)
                {
                    TempData[SharedGlobal.StatusMessageName] = "儲存完成";

                    return RedirectToAction(nameof(CERI02Controller.Index));
                }

                Message += Result.Message;
            }

            TempData[SharedGlobal.StatusMessageName] = Message;

#if DEBUG
            var errorsaa = ModelState
                        .Where(x => x.Value.Errors.Count > 0)
                        .Select(x => new { x.Key, x.Value.Errors })
                        .ToArray();
#endif
            ViewBag.AccreditationTypeItems = eRI01Service.GetSelectAccreditationTypeItems(model.Main?.ACCREDITATION_TYPE, SCHOOL_NO, ref db);
            return View(nameof(CERI02Controller.Edit), model);
        }

        #endregion 編輯畫面及處理

        #region 作廢

        /// <summary>
        /// 編輯 Del
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [ValidateAntiForgeryToken]
        [HttpPost]
        public ActionResult Del(AccreditationManageEditViewModel model)
        {
            this.Shared("編輯");

            if (string.IsNullOrWhiteSpace(model.Keyword) && string.IsNullOrWhiteSpace(model.Main?.ACCREDITATION_ID))
            {
                return RedirectToAction(nameof(CERI02Controller.Index));
            }

            string Message = string.Empty;

            var Result = this.Service.DelDate(model, user, ref db);

            if (Result.Success)
            {
                TempData[SharedGlobal.StatusMessageName] = "作廢完成";
                return RedirectToAction(nameof(CERI02Controller.Index));
            }
            Message += Result.Message;

            TempData[SharedGlobal.StatusMessageName] = Message;

            return RedirectToAction(nameof(CERI02Controller.Index));
        }

        #endregion 作廢

        #region Open 護照小視窗

        public ActionResult _AccreditationModal(AccreditationModalViewModel Data)
        {
            return PartialView(Data);
        }

        public ActionResult _AccreditationModalPageContent(AccreditationModalViewModel Data)
        {
            this.Shared();
            if (Data == null) Data = new AccreditationModalViewModel();

            if (string.IsNullOrWhiteSpace(Data.SCHOOL_NO))
            {
                Data.SCHOOL_NO = SCHOOL_NO;
            }

            ModelState.Clear();
            Data.PageSize = 10;
            Data = Service.GetAccreditationModalListData(Data, ref db);
            return PartialView("_AccreditationModalPageContent", Data);
        }

        public ActionResult _DetailsAccreditation(AccreditationEditModalViewModel Item)
        {
            return PartialView("_DetailsAccreditation", Item);
        }

        public ActionResult _DetailsHideAccreditation(AccreditationEditModalViewModel Item)
        {
            return PartialView("_DetailsHideAccreditation", Item);
        }

        public ActionResult _DetailsGetDataAccreditation(string KEY)
        {
            var Item = Service.GetAccreditation(KEY, ref db);
            return PartialView("_DetailsAccreditation", Item);
        }

        #endregion Open 護照小視窗

        #region Shared

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }

        private void SetTitle(string Panel_Title = "")
        {
            if (!string.IsNullOrWhiteSpace(Panel_Title))
            {
                ViewBag.Panel_Title = Panel_Title;
            }
            else
            {
                ViewBag.Panel_Title = Bre_Name + " - 列表 ";
            }
        }

        private void Shared(string Panel_Title = "", string ThisBre_Name = "")
        {
            ViewBag.BRE_NO = Bre_NO;

            if (!string.IsNullOrWhiteSpace(Panel_Title))
            {
                if (!string.IsNullOrWhiteSpace(ThisBre_Name))
                {
                    ViewBag.Panel_Title = ThisBre_Name + "-" + Panel_Title;
                }
                else
                {
                    ViewBag.Panel_Title = Bre_Name + "-" + Panel_Title;
                }
            }
            else
            {
                if (!string.IsNullOrWhiteSpace(Panel_Title))
                {
                    ViewBag.Panel_Title = ThisBre_Name + " - 列表 ";
                }
                else
                {
                    ViewBag.Panel_Title = Bre_Name + " - 列表 ";
                }
            }

            user = UserProfileHelper.Get();
            SCHOOL_NO = UserProfileHelper.GetSchoolNo();

            if (user != null)
            {
                if (user.SCHOOL_NO != null)
                {
                    SCHOOL_NO = user.SCHOOL_NO;
                    USER_NO = user.USER_NO;
                }
            }
        }

        #endregion Shared
    }
}