﻿using AutoMapper;
using ECOOL_APP.com.ecool.ResultService;
using ECOOL_APP.EF;
using EntityFramework.Extensions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Transactions;
using System.Web.Mvc;

namespace ECOOL_APP.com.ecool.service
{
    public class CERI01Service
    {
        public List<SelectListItem> GetSelectAccreditationTypeItems(string defaultSelectValue, string SCHOOL_NO, ref ECOOL_DEVEntities db, string EmptyValueIsText = "")
        {
            List<SelectListItem> AccreditationTypeItems = new List<SelectListItem>();

            AccreditationTypeItems.Add(new SelectListItem() { Text = EmptyValueIsText, Value = "", Selected = string.IsNullOrWhiteSpace(defaultSelectValue) });

            var cERT01s = db.CERT01.Where(a => a.DEL_YN == SharedGlobal.N && a.SCHOOL_NO == SCHOOL_NO).ToListNoLock();

            if (cERT01s?.Count() > 0)
            {
                foreach (var item in cERT01s)
                {
                    AccreditationTypeItems.Add(new SelectListItem() { Text = item.TYPE_NAME, Value = item.TYPE_ID, Selected = item.TYPE_ID == defaultSelectValue });
                }
            }

            return AccreditationTypeItems;
        }

        /// <summary>
        /// 列表
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public AccreditationTypeIndexViewModel GetListData(AccreditationTypeIndexViewModel model, string SCHOOL_NO, ref ECOOL_DEVEntities db)
        {
            //// 排序 ...
            if (string.IsNullOrWhiteSpace(model.OrderByColumnName)) // 預設排序
            {
                model.OrderByColumnName = nameof(AccreditationTypeListViewModel.CRE_DATE);
                model.SortType = PageGlobal.SortType.DESC;
            }

            var temp = (from a in db.CERT01
                        join b in db.HRMT01 on a.CRE_PERSON equals b.USER_KEY into tmpCre
                        join c in db.HRMT01 on a.CHG_PERSON equals c.USER_KEY into tmpChg
                        from Cres in tmpCre.DefaultIfEmpty()
                        from Chgs in tmpChg.DefaultIfEmpty()
                        where a.DEL_YN == SharedGlobal.N && a.SCHOOL_NO == SCHOOL_NO
                        select new AccreditationTypeListViewModel()
                        {
                            TYPE_ID = a.TYPE_ID,
                            TYPE_NAME = a.TYPE_NAME,
                            CRE_PERSON = a.CRE_PERSON,
                            CRE_DATE = a.CRE_DATE,
                            CHG_PERSON = a.CHG_PERSON,
                            CHG_DATE = a.CHG_DATE,
                            CRE_PERSON_NAME = Cres.NAME,
                            CHG_PERSON_NAME = Chgs.NAME,
                            CASH = (short)(a.CASH ?? 0),
                        });

            if (!string.IsNullOrWhiteSpace(model.SearchContent))
            {
                temp = temp.Where(a => a.TYPE_NAME.Contains(model.SearchContent.Trim()));
            }

            temp = temp.OrderBy(model.OrderByColumnName, model.SortType);

            model.ListData = temp.ToEzPagedList(model.Page, model.PageSize);
            model.Page = model.ListData.PageNumber;

            return model;
        }

        /// <summary>
        /// 編輯
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public AccreditationTypeEditViewModel GetEditData(AccreditationTypeEditViewModel model, ref ECOOL_DEVEntities db)
        {
            using (var txn = Queries.GetNewReadUncommittedScope())
            {
                model.Main = (from a in db.CERT01
                              where a.TYPE_ID == model.Keyword
                              select new AccreditationTypeEditMainViewModel()
                              {
                                  TYPE_ID = a.TYPE_ID,
                                  TYPE_NAME = a.TYPE_NAME,
                                  CASH = (short)a.CASH,
                              }).FirstOrDefault();

                return model;
            }
        }

        /// <summary>
        /// 存檔
        /// </summary>
        /// <param name="model"></param>
        /// <param name="user"></param>
        /// <returns></returns>
        public IResult SaveEditData(AccreditationTypeEditViewModel model, UserProfile user, ref ECOOL_DEVEntities db)
        {
            IResult result = new Result(false);

            try
            {
                using (TransactionScope tx = new TransactionScope())
                {
                    //主檔
                    CERT01 SaveUp = null;

                    SaveUp = db.CERT01.Where(a => a.TYPE_ID == model.Main.TYPE_ID).FirstOrDefault();

                    if (SaveUp == null)
                    {
                        SaveUp = new CERT01();

                        SaveUp.TYPE_ID = Guid.NewGuid().ToString("N");
                        SaveUp.TYPE_NAME = model.Main.TYPE_NAME;
                        SaveUp.CASH = model.Main.CASH;
                        SaveUp.CHG_PERSON = user?.USER_KEY;
                        SaveUp.CHG_DATE = DateTime.Now;
                        SaveUp.CRE_PERSON = user?.USER_KEY;
                        SaveUp.CRE_DATE = DateTime.Now;
                        SaveUp.SCHOOL_NO = user?.SCHOOL_NO;
                        SaveUp.DEL_YN = SharedGlobal.N;

                        db.CERT01.Add(SaveUp);
                    }
                    else
                    {
                        SaveUp.TYPE_NAME = model.Main.TYPE_NAME;
                        SaveUp.CASH = model.Main.CASH;
                        SaveUp.CHG_DATE = DateTime.Now;
                        SaveUp.CRE_PERSON = user?.USER_KEY;
                        db.Entry(SaveUp).State = System.Data.Entity.EntityState.Modified;
                    }

                    try
                    {
                        db.SaveChanges();
                    }
                    catch (Exception ex)
                    {
                        result.Exception = ex;
                        result.Message += ex.Message;
                        return result;
                    }

                    tx.Complete();
                }

                result.Success = true;
            }
            catch (Exception ex)
            {
                result.Exception = ex;
                result.Message += ex.Message;
                return result;
            }

            return result;
        }

        /// <summary>
        /// 作廢
        /// </summary>
        /// <param name="model"></param>
        /// <param name="user"></param>
        /// <returns></returns>
        public IResult DelDate(AccreditationTypeEditViewModel model, UserProfile user, ref ECOOL_DEVEntities db)
        {
            IResult result = new Result(false);

            try
            {
                using (TransactionScope tx = new TransactionScope())
                {
                    model.Keyword = (!string.IsNullOrWhiteSpace(model.Keyword)) ? model.Keyword : model.Main?.TYPE_ID;

                    CERT01 SaveUp = db.CERT01.Where(a => a.TYPE_ID == model.Keyword).FirstOrDefault();

                    if (SaveUp == null)
                    {
                        result.Message += "查無此資料";
                        return result;
                    }

                    SaveUp.DEL_YN = SharedGlobal.Y;
                    SaveUp.DEL_DATE = DateTime.Now;
                    SaveUp.DEL_PERSON = user?.USER_KEY;

                    db.Entry(SaveUp).State = System.Data.Entity.EntityState.Modified;

                    db.CERT02.Where(a => a.ACCREDITATION_TYPE == model.Keyword).Update(x => new CERT02 { DEL_YN = SharedGlobal.Y, DEL_DATE = DateTime.Now, DEL_PERSON = (user.USER_KEY) });

                    try
                    {
                        db.SaveChanges();
                    }
                    catch (Exception ex)
                    {
                        result.Exception = ex;
                        result.Message += ex.Message;
                        return result;
                    }

                    tx.Complete();
                }

                CERI02Service cERI02Service = new CERI02Service();
                cERI02Service.SetSyearAccreditationData(db);

                result.Success = true;
            }
            catch (Exception ex)
            {
                result.Exception = ex;
                result.Message += ex.Message;
            }

            return result;
        }

        public IEnumerable<CERT01> GetACCREDITATION_TYPEs(string Value, string SCHOOL_NO, ref ECOOL_DEVEntities db)
        {
            var IQdata = db.CERT01.Where(x => x.DEL_YN == SharedGlobal.N && x.SCHOOL_NO == SCHOOL_NO);

            if (!string.IsNullOrEmpty(Value))
            {
                IQdata = IQdata.Where(x => x.TYPE_NAME.Contains(Value));
            }

            IQdata = IQdata.OrderBy(x => x.TYPE_NAME);

            return IQdata;
        }

        public bool IsAccreditationTypeforSchool(string SCHOOL_NO, ref ECOOL_DEVEntities db)
        {
            return db.CERT01.Where(x => x.DEL_YN == SharedGlobal.N && x.SCHOOL_NO == SCHOOL_NO).NoLock(x => x.Any());
        }
    }
}