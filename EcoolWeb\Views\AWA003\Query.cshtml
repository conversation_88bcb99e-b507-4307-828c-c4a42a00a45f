﻿@model EcoolWeb.ViewModels.AWA003QueryViewModel
@{
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    ViewBag.Title = "酷幣排行榜";

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    string HidStyle = "";
    int DataCount = 0;
    int RowNumber = 0;
    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";

    }

    if (Model.isCarousel || Model.IsPrint)
    {
        Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
        HidStyle = "display:none";
    }

}
@if (Model.isCarousel)
{
    <style type="text/css">
        .bigger {
            font-size: 30px;
        }

        .Carousel_hide {
            display: none;
        }

        .table-ecool thead > tr > th, .table-ecool thead > tr > td {
            font-size: 30px;
        }
    </style>
}
<style type="text/css" media="print">
    /* 列印時隱藏 */
    .cScreen {
        display: none;
    }

    a[href]:after {
        content: none !important;
    }
</style>
<script src="~/Scripts/Blob.js"></script>
<script src="~/Scripts/FileSaver.js"></script>
<script src="~/Scripts/tableexport.js"></script>
<link href="~/Scripts/timepicker/jquery-ui-timepicker-addon.css" rel="stylesheet" />
<script src="~/Scripts/timepicker/jquery-ui-timepicker-addon.js"></script>
<script src="~/Scripts/timepicker/jquery-ui-timepicker-zh-TW.js"></script>
<script src="~/Scripts/timepicker/jquery.ui.datepicker-zh-TW.js"></script>
@*<script src="~/Scripts/moment.js"></script>*@
<script src="~/Scripts/moment.min.js"></script>
@if (AppMode == false && Model.isCarousel == false)
{
    @Html.Partial("_Title_Secondary")
}
@Html.Partial("_Notice")

@using (Html.BeginForm("Query", "AWA003", FormMethod.Post, new { name = "form1", id = "AWA003" }))
{

    <br />

    if (ViewBag.Show)
    {
        <div class="form-inline" style="@HidStyle">
            <div class="form-group">
                <label class="control-label">學校</label>
            </div>
            <div class="form-group">
                @Html.DropDownList("whereSchoolNo", (IEnumerable<SelectListItem>)ViewBag.SchoolNoSelectItem, new { @class = "form-control input-sm", @onchange = "FunPageProc(1)" })
            </div>
        </div>
        <br />
    }

    <div class="form-inline" style="@HidStyle" id="Q_Div">
        <div class="form-inline" role="form">
            <div class="form-group">
                <label class="control-label">學號/姓名</label>
            </div>
            <div class="form-group">
                @Html.EditorFor(m => m.whereKeyword, new { htmlAttributes = new { @class = "form-control input-sm" } })
                @Html.HiddenFor(m => m.OrdercColumn)
                @Html.HiddenFor(m => m.Page)
                @Html.HiddenFor(m => m.WhereIsMonthTop)
                @Html.HiddenFor(m => m.IsPrint)
                @Html.HiddenFor(m => m.IsToExcel)
            </div>
            <div class="form-group">
                <label class="control-label">年級</label>
            </div>
            <div class="form-group">
                @Html.DropDownListFor(m => m.whereGrade, (IEnumerable<SelectListItem>)ViewBag.GradeItem, new { @class = "form-control input-sm", onchange = "FunPageProc(1)" })
            </div>
            <div class="form-group">
                <label class="control-label">班級</label>
            </div>
            <div class="form-group">
                @Html.DropDownListFor(m => m.whereCLASS_NO, (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control input-sm", onchange = "FunPageProc(1)" })
            </div>
            <br />
            <div id="Sdate">
                <div class="form-group">
                    <label class="control-label">日期(起)</label>
                </div>

                <div class="form-group">
                    @Html.EditorFor(m => m.whereSTART_CRE_DATE, new { htmlAttributes = new { @class = "form-control input-sm", autocomplete = "off" } })
                </div>
                <div class="form-group">
                    <label class="control-label">日期(迄)</label>
                </div>
                <div class="form-group">
                    @Html.EditorFor(m => m.whereEND_CRE_DATE, new { htmlAttributes = new { @class = "form-control input-sm", autocomplete = "off" } })
                </div>
            </div>
            <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1)" />
            <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />

            @if (user != null)
            {
                if (user.USER_TYPE != UserType.Student && user.USER_TYPE != UserType.Parents)
                {

                    if (!Model.IsPrint)
                    {
                        <button id="ButtonExcel" class="btn-yellow btn btn-sm cScreen" style="float:right" onclick="exportExcel()">匯出excel</button>
                        <button type="button" class="btn-yellow btn btn-sm" onclick="PrintBooK()" style="float:right;margin-right:5px;">我要列印</button>
                    }
                    else
                    {
                        <button type="button" class="btn-yellow btn btn-sm " onclick="PrintBooK()" style="float:right">我要列印</button>
                    }

                }
            }
        </div>
    </div>

}

<div class="form-inline cScreen" style="text-align:right;@HidStyle">
    <br />
    <button class="btn btn-xs btn-pink @(Model.WhereIsMonthTop == false ? "active":"")" type="button" onclick="todoClear();doMonthTop('false');" Mouth="false">全部</button>
    <button class="btn btn-xs btn-pink @(Model.WhereIsMonthTop == true ? "active":"")" type="button" onclick="todoClear();doMonthTop('true');" Mouth="true">月排行榜</button>
</div><br />
      <span style="color:red">
          說明：<br />
          1.定存+現有酷幣=現有總資產<br />
          2.累計酷幣是指使用者從一開始使用到現在總共賺了多少酷幣，包含已經花掉的點數。<br />
          例如：小明在酷幣平台總共賺了3000點(累計酷幣)，曾經換了500點的禮物，現在剩2500點(現有總資產)。
      </span>
@if (!Model.IsPrint)
{
    <img src="~/Content/img/web-bar2-revise-15.png" style="width:100%" class="img-responsive " alt="Responsive image" />
}

<div class="@(Model.IsPrint ? "":"table-responsive")">
    <div class="text-center" id="tbData">
        <table class="@(Model.IsPrint ? "table table-bordered" : "table-ecool table-92Per table-hover table-ecool-AWA003")">
            <thead>
                <tr>

                    @if (ViewBag.Show)
                    {
                        <th>序號</th>
                        <th style="text-align: center;">
                            學校
                        </th>
                    }
                    <th style="text-align: center;">
                        班級
                    </th>
                    <th class="Carousel_hide" style="text-align: center;cursor:pointer;" onclick="doSort('SEAT_NO');">
                        座號
                        <img id="SEAT_NO" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                    </th>
                    <th class="Carousel_hide" style="text-align: center;cursor:pointer;" onclick="doSort('USER_NO');">
                        學號
                        <img id="USER_NO" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                    </th>
                    <th style="text-align: center;cursor:pointer;" onclick="doSort('NAME');">
                        姓名
                        <img id="NAME" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                    </th>
                    @if (Model.WhereIsMonthTop == true)
                    {
                        <th style="text-align: center;cursor:pointer;" onclick="doSort('CASH_DEPOSIT');">
                            定存
                            <img id="CASH_DEPOSIT" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </th>
                        <th style="text-align: center;cursor:pointer;" onclick="doSort('CASH_ALL');">
                            本月累計
                            <img id="CASH_ALL" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </th>
                        <th style="text-align: center;cursor:pointer;" onclick="doSort('CASH_AVAILABLE');">
                            現有酷幣
                            <img id="CASH_AVAILABLE" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </th>
                    }
                    else
                    {
                        <th style="text-align: center;cursor:pointer;" onclick="doSort('CASH_DEPOSIT');">
                            定存
                            <img id="CASH_DEPOSIT" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </th>
                        <th style="text-align: center;cursor:pointer;" onclick="doSort('CASH_AVAILABLE');">
                            現有酷幣
                            <img id="CASH_AVAILABLE" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </th>
                        <th style="text-align: center;cursor:pointer;" onclick="doSort('SUMCASH_AVAILABLE');">
                            現有總資產

                            <img id="CASH_AVAILABLE" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </th>
                        <th style="text-align: center;cursor:pointer;" onclick="doSort('CASH_ALL');">
                            累計酷幣
                            <img id="CASH_ALL" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </th>
                        <th style="text-align: center;cursor:pointer;" onclick="doSort('BOOK_QTY');">
                            閱讀本數
                            <img id="BOOK_QTY" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </th>

                    }
                </tr>
            </thead>
            <tbody>
                @foreach (var item in Model.VAWA003List)
                {
                    DataCount++;
                    RowNumber = Model.VAWA003List.PageSize * (Model.VAWA003List.PageNumber - 1) + (DataCount);
                    if (Model.isCarousel && DataCount > 5) { break; }
                <tr>
                    @if (ViewBag.Show)
                    {
                        <td>@RowNumber</td>
                        <td>
                            @Html.DisplayFor(modelItem => item.SHORT_NAME)
                        </td>
                    }
                    <td class="bigger">
                        @Html.DisplayFor(modelItem => item.CLASS_NO)
                    </td>
                    <td class="Carousel_hide">
                        @Html.DisplayFor(modelItem => item.SEAT_NO)
                    </td>
                    <td class="Carousel_hide">
                        @Html.DisplayFor(modelItem => item.USER_NO)
                    </td>
                    <td class="bigger">
                        @if (user != null)
                        {
                            if (user.USER_TYPE == UserType.Admin)
                            {
                                <a href='@Url.Action("Query2", "AWA002", new { whereKeyword=item.USER_NO })' target="_blank">
                                    @Html.DisplayFor(modelItem => item.NAME)
                                </a>
                            }
                            else
                            {
                                @Html.DisplayFor(modelItem => item.NAME)
                            }
                        }
                        else
                        {
                            @Html.DisplayFor(modelItem => item.NAME)
                        }
                    </td>
                    @if (Model.WhereIsMonthTop == true)
                    {
                        <td class="bigger">
                            @Html.DisplayFor(modelItem => item.CASH_DEPOSIT)
                        </td>
                        <td class="bigger">
                            @Html.DisplayFor(modelItem => item.CASH_ALL)
                        </td>
                        <td class="bigger">
                            @if (item.SUMCASH_AVAILABLE > item.CASH_ALL)
                            {
                                @Html.DisplayFor(modelItem => item.SUMCASH_AVAILABLE)

                            }
                            else
                            {
                                @Html.DisplayFor(modelItem => item.CASH_ALL)

                            }
                        </td>
                    }
                    else
                    {
                        <td class="bigger">
                            @Html.DisplayFor(modelItem => item.CASH_DEPOSIT)
                        </td>
                        <td class="bigger">
                            @Html.DisplayFor(modelItem => item.CASH_AVAILABLE)
                        </td>
                        <td class="bigger">

                            @Html.DisplayFor(modelItem => item.SUMCASH_AVAILABLE)
                        </td>
                        <td class="bigger">
                            @if (item.SUMCASH_AVAILABLE > item.CASH_ALL)
                            {
                                @Html.DisplayFor(modelItem => item.SUMCASH_AVAILABLE)

                            }
                            else
                            {
                                @Html.DisplayFor(modelItem => item.CASH_ALL)

                            }

                        </td>
                        <td class="bigger">

                            @Html.DisplayFor(modelItem => item.BOOK_QTY)

                        </td>
                            }
                    </tr>
                }
            </tbody>
        </table>
    </div>
</div>
<div class="modal fade" id="myModal" role="dialog">
    <div class="modal-content">

        <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal">&times;</button>
            <h4 class="modal-title">檔案匯出中，須等待久一點，請靜待，感謝您。</h4>
        </div>
    </div>
    <div class="modal-body">
        <div class="progress progress-striped active">
            <div class="progress-bar progress-bar-success" id="barr" role="progressbar" aria-valuenow="50" aria-valuemin="50" aria-valuemax="100" style="width:50%;">

                <span class="sr-only">0% 完成</span>
            </div>
        </div>
    </div>
    <div class="modal-footer">

        <button type="button" class="btn btn-default" data-dismiss="modal">Close </button>
    </div>
</div>
@if (Model.isCarousel == false)
{
    <div>
        @Html.Pager(Model.VAWA003List.PageSize, Model.VAWA003List.PageNumber, Model.VAWA003List.TotalItemCount).Options(o => o
              .DisplayTemplate("BootstrapPagination")
             .MaxNrOfPages(5)
             .SetPreviousPageText("上頁")
             .SetNextPageText("下頁")
         )
    </div>
    <div style="height:15px"></div>
    <div style="text-align:center">
        @if (Request["Awat"] != null)
        {
            <a href='@Url.Action("AwatQ02", "Awat")' class="btn btn-default">
                返回
            </a>
            <br />
        }
    </div>
}

@section scripts{
    <script type="text/javascript">

      var targetFormID = '#AWA003';

           window.onload = function () {
                if ($('#@Html.IdFor(m=>m.IsPrint)').val() == "true" && $('#@Html.IdFor(m=>m.IsToExcel)').val() != "true"  ) {
                    window.print()
               }
               initDatepicker();
         }

        @*$(function () {
            var MonthDEF = "";
            MonthDEF = $(".active").attr("Mouth");
            if (MonthDEF == "true") {
                $("#Sdate").attr("hidden", "hidden");
            }
            $('#ButtonExcel').click(function () {
               $('#@Html.IdFor(m=>m.IsToExcel)').val(true)
            $(targetFormID).attr('action','@Url.Action("Query", "AWA003")')
            $(targetFormID).attr('target', '_blank').submit().removeAttr('target');
                    var blob = new Blob([document.getElementById('tbData').innerHTML], {
                        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8"
                    });
                    var strFile = "Report.xls";
                    saveAs(blob, strFile);
                    return false;
                });
              initDatepicker();
         });*@
            function exportExcel() {

                $("#myModal").modal('show');
                $("#AWA003").attr("enctype", "multipart/form-data");
                $("#AWA003").attr("action", "@Url.Action("ExportExcel", (string)ViewBag.BRE_NO)");
                $("#AWA003").submit();
                setTimeout(function () {
                    $("#barr").css("width",  "100%");
                    $("#myModal").modal('hide');
                      $("#AWA003").attr("enctype", "multipart/form-data");
                $("#AWA003").attr("action", "@Url.Action("Query", (string)ViewBag.BRE_NO)");
                }, 8000);
        }
        function initDatepicker() {
                   var opt = {
                        showMonthAfterYear: true,
                        format: moment().format('YYYY-MM-DD'),
                        showSecond: true,
                        showButtonPanel: true,
                        showTime: true,
                        beforeShow: function () {
                            setTimeout(
                                function () {
                                    $('#ui-datepicker-div').css("z-index", 15);
                                }, 100
                            );
                        },
                        onSelect: function (dateText, inst) {
                            $('#' + inst.id).attr('value', dateText);
                        }
                    };
                    $("#@Html.IdFor(m => m.whereSTART_CRE_DATE)").datetimepicker(opt);
                    $("#@Html.IdFor(m => m.whereEND_CRE_DATE)").datetimepicker(opt);
        }

        function PrintBooK()
        {
            $('#@Html.IdFor(m=>m.IsPrint)').val(true)
            $(targetFormID).attr('action','@Url.Action("Query", "AWA003")')
            $(targetFormID).attr('target', '_blank').submit().removeAttr('target');
            $('#@Html.IdFor(m=>m.IsPrint)').val(false)
        }
      function fn_save_Excel() {

          var blob = new Blob([document.getElementById('tbData').innerHTML], {
              type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8"
          });
          var strFile = "Report.xls";
          saveAs(blob, strFile);
          funAjax();
          return false;
      }

            function FunPageProc(page) {
                if ($(targetFormID).size() > 0) {
                    $('#Page').val(page)
                    $(targetFormID).submit();
                }
            };

            function doSort(SortCol) {
                $("#OrdercColumn").val(SortCol);
                FunPageProc(1)
            }

             function doMonthTop(val) {
                 $("#@Html.IdFor(m=>m.WhereIsMonthTop)").val(val);
                 $("#Sdate").attr("hidden", "hidden");
                FunPageProc(1)
            }

            function todoClear() {
                ////重設
                $(targetFormID).find(":input,:selected").each(function (i) {

                    var type = $(this).attr('type')
                    var InPreadonly = $(this).attr('readonly')
                    var tag = this.tagName.toLowerCase(); // normalize case

                    if (InPreadonly == false || InPreadonly == undefined) {

                        if (type == 'radio' || type == 'checkbox') {
                            if ($(this).attr("title") == 'Default') {
                                this.checked = true;
                            }
                            else {
                                this.checked = false;
                            }
                        }
                        else if (tag == 'select') { //下拉式選單
                            this.selectedIndex = 0;
                        }
                        else if (type == 'text' || type == 'hidden' || type == 'password' || type == 'textarea' || type == 'datetime') {
                            this.value = '';
                        }
                    }
                });

                $(targetFormID).submit();
            }
    </script>
}