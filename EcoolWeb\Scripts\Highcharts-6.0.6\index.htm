<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1" />
		<title>Highcharts Examples</title>
	</head>
	<body>
		<h1>Highcharts Examples</h1>
		<h4>Line charts</h4>
		<ul>
			<li><a href="examples/line-basic/index.htm">Basic line</a></li>
			<li><a href="examples/line-ajax/index.htm">Ajax loaded data, clickable points</a></li>
			<li><a href="examples/line-labels/index.htm">With data labels</a></li>
			<li><a href="examples/annotations/index.htm">With annotations</a></li>
			<li><a href="examples/line-time-series/index.htm">Time series, zoomable</a></li>
			<li><a href="examples/spline-inverted/index.htm">Spline with inverted axes</a></li>
			<li><a href="examples/spline-symbols/index.htm">Spline with symbols</a></li>
			<li><a href="examples/spline-plot-bands/index.htm">Spline with plot bands</a></li>
			<li><a href="examples/spline-irregular-time/index.htm">Time data with irregular intervals</a></li>
			<li><a href="examples/line-log-axis/index.htm">Logarithmic axis</a></li>
			<li><a href="examples/line-boost/index.htm">Line chart with 500k points</a></li>
		</ul>
		<h4>Area charts</h4>
		<ul>
			<li><a href="examples/area-basic/index.htm">Basic area</a></li>
			<li><a href="examples/area-negative/index.htm">Area with negative values</a></li>
			<li><a href="examples/area-stacked/index.htm">Stacked area</a></li>
			<li><a href="examples/area-stacked-percent/index.htm">Percentage area</a></li>
			<li><a href="examples/area-missing/index.htm">Area with missing points</a></li>
			<li><a href="examples/area-inverted/index.htm">Inverted axes</a></li>
			<li><a href="examples/areaspline/index.htm">Area-spline</a></li>
			<li><a href="examples/arearange/index.htm">Area range</a></li>
			<li><a href="examples/arearange-line/index.htm">Area range and line</a></li>
			<li><a href="examples/sparkline/index.htm">Sparkline charts</a></li>
			<li><a href="examples/streamgraph/index.htm">Streamgraph</a></li>
		</ul>
		<h4>Column and bar charts</h4>
		<ul>
			<li><a href="examples/bar-basic/index.htm">Basic bar</a></li>
			<li><a href="examples/bar-stacked/index.htm">Stacked bar</a></li>
			<li><a href="examples/bar-negative-stack/index.htm">Bar with negative stack</a></li>
			<li><a href="examples/column-basic/index.htm">Basic column</a></li>
			<li><a href="examples/column-negative/index.htm">Column with negative values</a></li>
			<li><a href="examples/column-stacked/index.htm">Stacked column</a></li>
			<li><a href="examples/column-stacked-and-grouped/index.htm">Stacked and grouped column</a></li>
			<li><a href="examples/column-stacked-percent/index.htm">Stacked percentage column</a></li>
			<li><a href="examples/column-rotated-labels/index.htm">Column with rotated labels</a></li>
			<li><a href="examples/column-drilldown/index.htm">Column with drilldown</a></li>
			<li><a href="examples/column-placement/index.htm">Fixed placement columns</a></li>
			<li><a href="examples/column-parsed/index.htm">Data defined in a HTML table</a></li>
			<li><a href="examples/columnrange/index.htm">Column range</a></li>
		</ul>
		<h4>Pie charts</h4>
		<ul>
			<li><a href="examples/pie-basic/index.htm">Pie chart</a></li>
			<li><a href="examples/pie-legend/index.htm">Pie with legend</a></li>
			<li><a href="examples/pie-donut/index.htm">Donut chart</a></li>
			<li><a href="examples/pie-semi-circle/index.htm">Semi circle donut</a></li>
			<li><a href="examples/pie-drilldown/index.htm">Pie with drilldown</a></li>
			<li><a href="examples/pie-gradient/index.htm">Pie with gradient fill</a></li>
			<li><a href="examples/pie-monochrome/index.htm">Pie with monochrome fill</a></li>
		</ul>
		<h4>Scatter and bubble charts</h4>
		<ul>
			<li><a href="examples/scatter/index.htm">Scatter plot</a></li>
			<li><a href="examples/scatter-boost/index.htm">Scatter plot with 1 million points</a></li>
			<li><a href="examples/bubble/index.htm">Bubble chart</a></li>
			<li><a href="examples/bubble-3d/index.htm">3D bubbles</a></li>
		</ul>
		<h4>Combinations</h4>
		<ul>
			<li><a href="examples/synchronized-charts/index.htm">Synchronized charts</a></li>
			<li><a href="examples/combo/index.htm">Column, line and pie</a></li>
			<li><a href="examples/combo-dual-axes/index.htm">Dual axes, line and column</a></li>
			<li><a href="examples/combo-multi-axes/index.htm">Multiple axes</a></li>
			<li><a href="examples/combo-regression/index.htm">Scatter with regression line</a></li>
			<li><a href="examples/combo-meteogram/index.htm">Meteogram</a></li>
			<li><a href="examples/combo-timeline/index.htm">Advanced timeline</a></li>
		</ul>
		<h4>Styled mode (CSS styling)</h4>
		<ul>
			<li><a href="examples/styled-mode-column/index.htm">Styled mode column</a></li>
			<li><a href="examples/styled-mode-pie/index.htm">Styled mode pie</a></li>
		</ul>
		<h4>Dynamic charts</h4>
		<ul>
			<li><a href="examples/dynamic-update/index.htm">Spline updating each second</a></li>
			<li><a href="examples/dynamic-click-to-add/index.htm">Click to add a point</a></li>
			<li><a href="examples/dynamic-master-detail/index.htm">Master-detail chart</a></li>
			<li><a href="examples/chart-update/index.htm">Update options after render</a></li>
			<li><a href="examples/responsive/index.htm">Responsive chart</a></li>
		</ul>
		<h4>3D charts</h4>
		<ul>
			<li><a href="examples/3d-column-interactive/index.htm">3D column</a></li>
			<li><a href="examples/3d-column-null-values/index.htm">3D column with null and 0 values</a></li>
			<li><a href="examples/3d-column-stacking-grouping/index.htm">3D column with stacking and grouping</a></li>
			<li><a href="examples/3d-pie/index.htm">3D pie</a></li>
			<li><a href="examples/3d-pie-donut/index.htm">3D donut</a></li>
			<li><a href="examples/3d-scatter-draggable/index.htm">3D scatter chart</a></li>
		</ul>
		<h4>Gauges</h4>
		<ul>
			<li><a href="examples/gauge-speedometer/index.htm">Gauge series</a></li>
			<li><a href="examples/gauge-solid/index.htm">Solid gauge</a></li>
			<li><a href="examples/gauge-activity/index.htm">Activity gauge</a></li>
			<li><a href="examples/gauge-clock/index.htm">Clock</a></li>
			<li><a href="examples/gauge-dual/index.htm">Gauge with dual axes</a></li>
			<li><a href="examples/gauge-vu-meter/index.htm">VU meter</a></li>
			<li><a href="examples/bullet-graph/index.htm">Bullet graph</a></li>
		</ul>
		<h4>Heat and tree maps</h4>
		<ul>
			<li><a href="examples/heatmap/index.htm">Heat map</a></li>
			<li><a href="examples/heatmap-canvas/index.htm">Large heat map</a></li>
			<li><a href="examples/honeycomb-usa/index.htm">Tile map, honeycomb</a></li>
			<li><a href="examples/treemap-coloraxis/index.htm">Tree map with color axis</a></li>
			<li><a href="examples/treemap-with-levels/index.htm">Tree map with levels</a></li>
			<li><a href="examples/treemap-large-dataset/index.htm">Large tree map</a></li>
		</ul>
		<h4>More chart types</h4>
		<ul>
			<li><a href="examples/polar/index.htm">Polar chart</a></li>
			<li><a href="examples/polar-spider/index.htm">Spiderweb</a></li>
			<li><a href="examples/sunburst/index.htm">Sunburst</a></li>
			<li><a href="examples/polar-wind-rose/index.htm">Wind rose</a></li>
			<li><a href="examples/parallel-coordinates/index.htm">Parallel coordinates</a></li>
			<li><a href="examples/windbarb-series/index.htm">Wind barb</a></li>
			<li><a href="examples/vector-plot/index.htm">Vector plot</a></li>
			<li><a href="examples/box-plot/index.htm">Box plot</a></li>
			<li><a href="examples/error-bar/index.htm">Error bar</a></li>
			<li><a href="examples/waterfall/index.htm">Waterfall</a></li>
			<li><a href="examples/variwide/index.htm">Variwide</a></li>
			<li><a href="examples/variable-radius-pie/index.htm">Variable radius pie</a></li>
			<li><a href="examples/histogram/index.htm">Histogram</a></li>
			<li><a href="examples/bellcurve/index.htm">Bell curve</a></li>
			<li><a href="examples/funnel/index.htm">Funnel chart</a></li>
			<li><a href="examples/pyramid/index.htm">Pyramid chart</a></li>
			<li><a href="examples/polygon/index.htm">Polygon series</a></li>
			<li><a href="examples/pareto/index.htm">Pareto chart</a></li>
			<li><a href="examples/sankey-diagram/index.htm">Sankey diagram</a></li>
			<li><a href="examples/x-range/index.htm">X-range series</a></li>
			<li><a href="examples/wordcloud/index.htm">Word cloud</a></li>
			<li><a href="examples/renderer/index.htm">General drawing</a></li>
		</ul>
	</body>
</html>
