﻿using com.ecool.service;
using ECOOL_APP;
using ECOOL_APP.com.ecool.service;
using ECOOL_APP.EF;
using EcoolWeb.CustomAttribute;
using EcoolWeb.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace EcoolWeb.Controllers
{
    /// <summary>
    /// 護照查詢
    /// </summary>
    [SessionExpire]
    [CheckPermissionSeeion(CheckACTION_ID = "GraduateIndex")]//檢查權限
    public class CERI013Controller : Controller
    {
        /// <summary>
        /// 此Controller 對應的程式代碼
        /// </summary>
        private static string Bre_NO = "CERI013";

        private string Bre_Name = BreadcrumbService.GetBRE_NAME_forBRE_NO(Bre_NO);

        /// <summary>
        /// 會員相關資訊
        /// </summary>
        private UserProfile user;

        private string SCHOOL_NO = string.Empty;
        private string USER_NO = string.Empty;

        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();
        private CER001Service Service = new CER001Service();
        private CERI01Service eRI01Service = new CERI01Service();
        private CERI02Service cERI02Service = new CERI02Service();



      
        public ActionResult GraduateIndex()
        {
            ViewBag.NowAction = "GraduateIndex";
            this.Shared();
            return View();
        }

        //檢查權限
        public ActionResult _GraduatePageContent(CER001IndexViewModel model)
        {
            this.Shared();
            ViewBag.NowAction = "GraduateIndex";
            if (model == null) model = new CER001IndexViewModel();

            if (string.IsNullOrWhiteSpace(model.WhereSCHOOL_NO))
            {
                model.WhereSCHOOL_NO = SCHOOL_NO;
            }

            if (model.WhereGRADE == null && string.IsNullOrWhiteSpace(model.WhereCLASS_NO))
            {
                HRMT03 hrmt03 = null;

                if (user != null)
                {
                    hrmt03 = db.HRMT03.Where(a => a.SCHOOL_NO == user.SCHOOL_NO && a.TEACHER_NO == user.USER_NO).FirstOrDefault();
                }

                if (hrmt03 != null)
                {
                    model.WhereGRADE = hrmt03.GRADE;
                    model.WhereCLASS_NO = hrmt03.CLASS_NO;
                }
                else
                {
                    //model.WhereGRADE = (byte)HRMT01.GradeVal.In1Grade;
                }

                var WhereGRADE_SEMESTERs = new List<string>();
                WhereGRADE_SEMESTERs.Add($"{model.WhereGRADE}_1");
                WhereGRADE_SEMESTERs.Add($"{model.WhereGRADE}_2");
                model.WhereGRADE_SEMESTERs = WhereGRADE_SEMESTERs;
            }
            int SYear;
            int Semesters;
            model = Service.GetListGraduateData(model, ref db);
            SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);
            List<SelectListItem> SyearItems = new List<SelectListItem>();


            SyearItems.Add(new SelectListItem() { Text = (SYear - 6).ToString(), Value = (SYear - 6).ToString(), Selected = true });
            SyearItems.Add(new SelectListItem() { Text = (SYear - 7).ToString(), Value = (SYear - 7).ToString(), Selected = false });
            model = Service.GetListGraduateData(model, ref db);
            ViewBag.SyearItems = SyearItems;
            ViewBag.AccreditationTypeItems = eRI01Service.GetSelectAccreditationTypeItems(model?.WhereACCREDITATION_TYPE, SCHOOL_NO, ref db, "全部");
            ViewBag.AccreditationsItems = cERI02Service.GetSelectAccreditationsItems(model.WhereACCREDITATION_NAME, SCHOOL_NO, ref db, "全部");

            List<SelectListItem> GradeItems = new List<SelectListItem>();
            GradeItems.Add(new SelectListItem() { Text = "畢業生", Value = "6", Selected = true });
            ViewBag.GradeItem = GradeItems;

          


             ViewBag.SUBJECTItem = cERI02Service.GetSelectSUBJECTItems("1", model.WhereACCREDITATION_NAME, ref db, "全部");
            ViewBag.ClassItems = HRMT01.GetGradeClassListData(SCHOOL_NO, model.WhereGRADE.HasValue ? model.WhereGRADE.Value.ToString() :
                string.Empty, model.WhereSyaer.HasValue ? model.WhereSyaer : (byte?)(SYear - 6), model.WhereCLASS_NO, ref db)
         .Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.WhereCLASS_NO });


            return PartialView(model);
        }

        public ActionResult _PageContent(CER001IndexViewModel model)
        {
            this.Shared();
            if (model == null) model = new CER001IndexViewModel();

            if (string.IsNullOrWhiteSpace(model.WhereSCHOOL_NO))
            {
                model.WhereSCHOOL_NO = SCHOOL_NO;
            }

            if (model.WhereGRADE == null && string.IsNullOrWhiteSpace(model.WhereCLASS_NO))
            {
                HRMT03 hrmt03 = null;

                if (user != null)
                {
                    hrmt03 = db.HRMT03.Where(a => a.SCHOOL_NO == user.SCHOOL_NO && a.TEACHER_NO == user.USER_NO).FirstOrDefault();
                }

                if (hrmt03 != null)
                {
                    model.WhereGRADE = hrmt03.GRADE;
                    model.WhereCLASS_NO = hrmt03.CLASS_NO;
                }
                else
                {
                    //model.WhereGRADE = (byte)HRMT01.GradeVal.In1Grade;
                }

                var WhereGRADE_SEMESTERs = new List<string>();
                WhereGRADE_SEMESTERs.Add($"{model.WhereGRADE}_1");
                WhereGRADE_SEMESTERs.Add($"{model.WhereGRADE}_2");
                model.WhereGRADE_SEMESTERs = WhereGRADE_SEMESTERs;
            }

            model = Service.GetListData(model, ref db);

            ViewBag.AccreditationTypeItems = eRI01Service.GetSelectAccreditationTypeItems(model?.WhereACCREDITATION_TYPE, SCHOOL_NO, ref db, "全部");
            ViewBag.AccreditationsItems = cERI02Service.GetSelectAccreditationsItems(model.WhereACCREDITATION_NAME, SCHOOL_NO, ref db, "全部");
            ViewBag.GradeItem = HRMT01.GetGradeItems(model.WhereGRADE.HasValue ? model.WhereGRADE.Value.ToString() : string.Empty, true);
            ViewBag.SUBJECTItem = cERI02Service.GetSelectSUBJECTItems("1", model.WhereACCREDITATION_NAME, ref db, "全部");
            ViewBag.ClassItems = HRMT01.GetClassListData(SCHOOL_NO, model.WhereGRADE.HasValue ? model.WhereGRADE.Value.ToString() : string.Empty, model.WhereCLASS_NO, ref db)
            .Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.WhereCLASS_NO });
        
         
            return PartialView(model);
        }

        public ActionResult _GetSubjectsItems_NODDLHtml(string tagId, string tagName, string ACCREDITATION_ID, string WhereACCREDITATION_NAME)
        {
            ViewBag.BRE_NO = Bre_NO;

            user = UserProfileHelper.Get();

            if (string.IsNullOrWhiteSpace(SCHOOL_NO))
            {
                SCHOOL_NO = user.SCHOOL_NO;
            }
            var SubjectItems = cERI02Service.GetSelectSUBJECTItems("", WhereACCREDITATION_NAME, ref db, "全部");
            string _html = Util.DropDownListHelper.GetDropdownList(tagId, tagName, SubjectItems, new { @class = "form-control" }, WhereACCREDITATION_NAME, true, null, true);
            return Content(_html);
        }
        public ActionResult _GetAccreditationsItems_NODDLHtml(string tagId, string tagName, string SCHOOL_NO, string WhereACCREDITATION_TYPE, string WhereACCREDITATION_NAME)
        {
            ViewBag.BRE_NO = Bre_NO;

            user = UserProfileHelper.Get();

            if (string.IsNullOrWhiteSpace(SCHOOL_NO))
            {
                SCHOOL_NO = user.SCHOOL_NO;
            }
            var AccreditationsItems = cERI02Service.GetSelectAccreditationsItems("", SCHOOL_NO, ref db, "全部", WhereACCREDITATION_TYPE);
            string _html = Util.DropDownListHelper.GetDropdownList(tagId, tagName, AccreditationsItems, new { @class = "form-control" }, WhereACCREDITATION_NAME, true, null, true);
            return Content(_html);
        }

        public ActionResult _GetCALSSITEMs_NODDLHtml(string tagId, string tagName, string SCHOOL_NO, string WhereGRADE, string WhereCLASS_NO)
        {
            ViewBag.BRE_NO = Bre_NO;

            user = UserProfileHelper.Get();

            if (string.IsNullOrWhiteSpace(SCHOOL_NO))
            {
                SCHOOL_NO = user.SCHOOL_NO;
            }
            var ClassListDataItems = HRMT01.GetClassListData(SCHOOL_NO, WhereGRADE, "", ref db);
            string _html = Util.DropDownListHelper.GetDropdownList(tagId, tagName, ClassListDataItems, new { @class = "form-control" }, WhereCLASS_NO, true, null, true);
            return Content(_html);
        }

        #region Shared

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }

        private void SetTitle(string Panel_Title = "")
        {
            if (!string.IsNullOrWhiteSpace(Panel_Title))
            {
                ViewBag.Panel_Title = Panel_Title;
            }
            else
            {
                ViewBag.Panel_Title = Bre_Name + " - 列表 ";
            }
        }

        private void Shared(string Panel_Title = "")
        {
            ViewBag.BRE_NO = Bre_NO;

            if (!string.IsNullOrWhiteSpace(Panel_Title))
            {
                ViewBag.Panel_Title = Bre_Name + "-" + Panel_Title;
            }
            else
            {
                ViewBag.Panel_Title = Bre_Name + " - 列表 ";
            }

            user = UserProfileHelper.Get();
            SCHOOL_NO = UserProfileHelper.GetSchoolNo();

            if (user != null)
            {
                if (user.SCHOOL_NO != null)
                {
                    SCHOOL_NO = user.SCHOOL_NO;
                    USER_NO = user.USER_NO;
                }
            }
        }

        #endregion Shared
    }
}