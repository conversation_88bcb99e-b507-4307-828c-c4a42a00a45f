/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/NonUnicode/Regular/PrivateUse.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.STIXNonUnicode,{57346:[1022,0,1192,30,1162],57347:[1450,0,1311,55,1256],57348:[450,-59,926,55,871],57349:[530,-57,926,55,871],57354:[538,-55,685,48,637],57355:[543,37,685,48,637],57358:[846,340,685,60,626],57359:[730,229,685,56,621],57360:[730,229,685,56,621],57361:[846,340,685,61,626],57366:[818,311,685,53,618],57368:[818,311,685,67,632],57374:[607,110,685,48,638],57379:[695,189,685,48,637],57381:[724,236,685,48,637],57382:[662,156,685,47,612],57383:[662,156,685,73,638],57384:[663,155,933,25,908],57385:[662,156,933,25,908],57386:[662,156,1240,43,1184],57387:[662,156,1240,56,1197],57391:[662,156,685,23,662],57397:[602,98,486,64,422],57399:[662,156,685,48,637],57400:[690,189,732,50,682],57401:[662,156,685,47,636],57402:[811,156,471,40,525],57403:[736,156,685,60,625],57404:[662,156,685,60,625],57405:[602,98,486,64,422],57408:[415,-92,685,48,637],57411:[818,311,685,60,625],57412:[818,311,685,60,625],57413:[818,311,685,60,625],57414:[818,311,685,60,625],57419:[745,242,685,60,625],57420:[845,341,685,60,625],57421:[534,19,685,48,637],57422:[845,341,685,60,625],57423:[745,242,685,60,625],57424:[662,156,685,65,620],57425:[662,156,685,65,620],57426:[561,55,685,48,637],57427:[450,0,632,26,604],57428:[516,10,688,37,679],57429:[475,14,571,20,563],57430:[459,11,632,10,624],57431:[459,12,624,29,595],57433:[730,224,685,48,637],57434:[730,224,685,48,637],57435:[561,-30,685,48,637],57436:[534,19,685,48,637],57437:[459,10,452,16,436],57438:[698,-547,0,95,406],57441:[-141,390,0,11,322],57442:[-141,486,0,11,322],57443:[734,-508,0,94,485],57444:[777,-547,0,95,425],57445:[-141,371,0,1,331],57446:[770,-547,0,101,412],57447:[-141,371,0,1,331],57448:[683,287,524,9,487],57451:[602,98,486,64,422],57452:[602,98,486,64,422],57453:[785,157,685,60,625],57454:[785,157,685,60,625],57455:[785,129,685,60,625],57456:[785,129,685,60,625],57461:[626,119,685,48,637],57462:[626,119,685,48,637],57463:[725,236,685,48,637],57468:[758,252,685,48,637],57469:[758,252,685,48,637],57470:[732,227,685,56,621],57471:[732,227,685,56,621],57472:[818,311,685,57,622],57473:[818,311,685,57,622],57474:[730,229,685,56,621],57475:[730,229,685,56,621],57476:[709,201,685,56,621],57477:[709,201,685,56,621],57478:[818,311,685,56,621],57479:[818,311,685,55,621],57480:[818,311,685,56,621],57481:[818,311,685,55,620],57490:[719,296,685,62,627],57491:[719,296,685,61,626],57492:[719,240,685,62,627],57493:[719,240,685,61,626],57494:[818,311,685,55,620],57495:[818,311,685,65,630],57496:[690,189,685,23,662],57497:[567,183,612,25,587],57498:[719,213,708,18,690],57499:[719,213,708,18,690],57506:[460,218,561,24,539],57508:[470,233,378,10,358],57509:[669,-426,397,75,338],57510:[216,144,444,38,429],57511:[702,-506,376,30,346],57512:[662,156,496,114,371],57513:[497,-167,647,49,619],57514:[702,-506,376,30,346],57515:[662,156,496,114,371],57516:[702,-506,470,30,440],57517:[662,156,638,35,513],57518:[662,0,423,55,345],57519:[662,0,423,55,345],57520:[735,-531,0,100,400],57521:[-50,254,0,0,300],57522:[-50,254,0,0,300],57523:[683,218,541,32,457],57524:[556,-220,313,55,258],57525:[556,-220,313,55,258],57526:[449,-57,0,30,124],57527:[324,-183,281,70,211],57528:[943,11,1344,67,1302],57529:[943,11,1344,67,1302],57531:[622,101,685,48,637],57532:[547,41,685,47,635],57533:[662,218,710,15,660],57534:[757,218,1102,15,1073],57535:[836,236,636,50,586],57536:[836,236,636,50,586],57537:[836,236,636,50,586],57538:[836,236,636,50,586],57539:[386,-120,750,50,700],57540:[478,-28,750,50,700],57541:[478,-28,750,50,700],57542:[286,-220,750,50,700],57543:[402,-120,750,50,700],57544:[386,-120,1000,50,950],57545:[478,-28,1000,50,950],57546:[544,38,1000,50,950],57547:[386,-120,750,50,700],57548:[478,-28,750,50,700],57549:[544,38,750,50,700],57550:[836,236,636,50,586],57551:[836,236,636,50,586],57552:[836,236,636,50,586],57553:[836,236,636,50,586],57554:[692,186,926,83,843],57555:[633,127,926,24,902],57556:[633,127,926,24,902],57557:[286,-220,1000,50,950],57558:[386,-120,750,50,700],57559:[583,79,762,50,712],57560:[584,0,400,57,343],57561:[665,0,255,56,199],57562:[665,0,388,56,332],57563:[610,104,1472,86,1402],57564:[354,-152,1134,65,1069],57565:[933,0,516,73,445],57566:[933,0,500,57,439],57567:[754,0,778,92,699],57568:[920,0,500,40,444],57569:[757,0,389,81,318],57570:[754,0,500,60,429],57571:[638,134,842,35,807],57573:[690,189,523,72,450],57574:[690,189,523,72,450],57575:[811,127,772,35,737],57576:[532,26,1077,55,1022],57577:[547,41,685,48,636],57578:[661,158,910,45,865],57579:[567,58,716,45,671],57580:[862,-120,685,48,637],57581:[819,312,511,192,319],57582:[751,156,926,85,841],57583:[547,41,686,49,637],57585:[66,0,390,48,342],57586:[936,157,1059,38,1033],57587:[662,156,1059,196,862],57588:[694,168,773,55,718],57589:[672,146,926,55,872],57590:[747,114,909,23,886],57591:[727,102,956,22,934],57592:[474,89,500,163,336],57593:[680,0,767,88,679],57594:[474,89,297,62,235],57595:[680,0,1750,88,1662],57596:[680,0,1625,88,1537],57597:[680,0,1625,88,1537],57598:[680,0,1625,88,1537],57599:[680,0,1625,88,1537],57600:[680,0,2032,88,1944],57601:[680,0,1625,88,1537],57602:[680,0,1608,88,1520],57603:[680,0,2296,88,2208],57604:[409,-253,100,-64,164],57605:[680,0,2032,88,1944],57606:[680,0,2032,88,1944],57607:[680,0,1625,88,1537],57608:[680,0,1625,88,1537],57609:[781,279,327,10,286],57610:[781,279,250,41,178],57612:[384,-122,400,69,330],57613:[384,-122,400,69,330],57614:[405,-101,652,193,459],57615:[386,-120,315,0,315],57616:[432,-28,652,124,528],57617:[432,-28,652,124,528],57618:[662,156,926,55,872],57619:[662,156,926,55,872],57620:[662,156,926,54,871],57621:[662,156,926,54,871],57622:[214,-107,511,223,289],57623:[286,-220,229,61,168],57624:[271,-134,277,70,207],57625:[271,-134,277,70,207],57626:[662,156,511,59,451],57627:[662,156,511,59,451],57628:[662,156,926,54,872],57629:[662,156,926,54,872],57630:[662,156,926,54,872],57631:[662,156,926,54,872],57632:[411,-94,511,220,293],57633:[290,-217,311,-3,314],57634:[382,-123,367,54,313],57635:[383,-124,367,54,313],57636:[662,156,511,59,451],57637:[662,156,511,59,451],57638:[449,-57,926,54,872],57639:[449,-57,926,54,872],57640:[662,155,926,54,872],57641:[662,156,926,55,872],57642:[662,156,926,54,871],57643:[661,156,926,54,872],57644:[404,-101,511,220,293],57645:[403,-100,511,220,293],57646:[290,-217,371,14,317],57647:[290,-217,371,54,357],57648:[373,-134,379,70,309],57649:[373,-134,379,70,309],57650:[373,-134,379,70,309],57651:[373,-134,379,70,309],57652:[486,-20,315,0,315],57653:[405,-101,926,230,696],57654:[541,35,315,0,315],57655:[405,-101,1033,229,805],57658:[943,11,735,67,1302],57663:[-126,261,325,-1,326],57666:[955,-342,1820,-25,1830],57667:[955,-342,1820,-10,1845],57668:[352,261,1820,-25,1830],57669:[352,261,1820,-10,1845],57670:[955,-554,1820,-25,1830],57671:[955,-554,1820,-10,1845],57672:[140,261,1820,-25,1830],57673:[140,261,1820,-10,1845],57676:[660,158,857,48,777],57677:[660,158,857,80,809],57678:[661,157,685,44,609],57679:[661,157,685,76,641],57680:[135,308,735,-25,746],57681:[135,308,735,-11,760],57682:[444,0,735,-25,746],57683:[444,0,735,-11,760],57724:[683,10,499,28,471],57725:[674,0,666,31,635],57726:[662,0,604,74,547],57727:[662,0,535,74,523],57728:[674,0,666,31,635],57729:[662,0,583,74,540],57730:[662,0,637,28,603],57731:[662,0,658,74,584],57732:[676,14,714,30,684],57733:[662,0,401,45,356],57734:[662,0,634,74,630],57735:[674,0,666,31,635],57736:[662,0,843,75,768],57737:[662,14,675,74,601],57738:[662,0,643,28,615],57739:[676,14,714,30,684],57740:[662,0,658,74,584],57741:[662,0,525,74,512],57742:[676,14,714,30,684],57743:[662,0,624,26,594],57744:[662,0,608,15,593],57745:[676,0,690,24,666],57746:[662,0,716,23,693],57747:[662,0,700,31,669],57748:[681,0,724,12,712],57749:[676,0,744,29,715],57750:[463,10,537,28,532],57751:[683,215,498,41,471],57752:[463,216,474,27,455],57753:[683,10,499,28,471],57754:[463,10,438,22,419],57755:[683,213,416,33,408],57756:[463,215,494,41,443],57757:[683,10,446,21,425],57758:[464,10,270,57,269],57759:[464,0,472,82,472],57760:[683,11,489,8,478],57761:[453,215,487,44,482],57762:[464,14,460,30,427],57763:[683,215,418,33,410],57764:[463,10,499,28,471],57765:[453,10,507,7,487],57766:[462,216,498,48,470],57767:[463,212,416,33,414],57768:[453,10,526,28,542],57769:[453,10,426,2,410],57770:[463,10,503,41,463],57771:[464,216,632,34,600],57772:[463,215,399,-20,440],57773:[461,216,654,12,642],57774:[454,10,624,29,595],57775:[463,10,456,23,432],57776:[683,12,489,42,491],57777:[684,216,622,28,594],57778:[463,216,491,28,463],57779:[453,10,762,7,739],57996:[474,-227,0,53,397],57997:[734,-484,0,94,460],57998:[622,101,685,48,637],58001:[955,-820,325,-1,326],58002:[662,0,1388,38,1350],58003:[763,260,1797,58,1739],58108:[756,218,722,15,707],58110:[756,217,667,17,593],58112:[756,217,587,11,577],58114:[756,218,722,48,675],58116:[756,217,611,12,597],58118:[756,217,612,10,598],58120:[756,217,722,18,703],58122:[756,218,722,34,688],58124:[756,218,333,-24,438],58126:[756,217,731,33,723],58128:[756,218,702,15,687],58130:[756,217,889,12,864],58132:[756,218,722,12,707],58134:[756,217,643,29,614],58136:[756,218,722,34,688],58138:[756,217,722,18,703],58140:[756,218,557,16,565],58142:[756,217,624,30,600],58144:[756,218,611,17,593],58146:[756,218,722,29,703],58148:[756,217,763,35,728],58150:[756,217,722,10,704],58152:[756,217,743,22,724],58154:[756,217,744,29,715],58212:[756,240,673,55,665],58216:[756,218,557,8,645],58220:[773,218,645,-72,675],58224:[756,218,708,7,668],58306:[662,156,685,48,637],58307:[627,135,685,48,637],58308:[627,135,685,48,637],58311:[662,156,902,0,863],58312:[662,156,902,0,863]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/NonUnicode/Regular/PrivateUse.js");
