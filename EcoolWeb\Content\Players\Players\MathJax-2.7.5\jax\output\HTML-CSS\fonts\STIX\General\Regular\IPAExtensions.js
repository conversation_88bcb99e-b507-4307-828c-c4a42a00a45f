/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/General/Regular/IPAExtensions.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.STIXGeneral,{592:[460,10,444,8,413],593:[460,10,500,27,491],594:[460,10,500,27,491],595:[683,10,500,69,468],596:[459,11,444,10,397],597:[460,160,444,25,417],598:[683,233,553,27,599],599:[683,10,587,27,602],600:[460,10,444,20,419],601:[460,10,444,14,413],602:[460,13,657,36,651],603:[475,14,438,20,389],604:[475,14,438,20,389],605:[475,14,623,20,603],606:[475,14,479,20,430],607:[460,218,315,-49,296],608:[683,212,594,32,634],609:[482,212,537,32,455],610:[450,11,570,30,539],611:[450,234,500,19,480],612:[450,10,500,13,486],613:[450,233,500,13,491],614:[683,0,500,9,487],615:[683,233,481,9,427],616:[683,0,278,16,253],617:[454,10,333,17,311],618:[450,0,258,21,231],619:[683,0,350,10,340],620:[683,0,375,12,362],621:[683,233,302,10,352],622:[683,233,549,19,538],623:[450,10,778,11,770],624:[450,233,803,11,785],625:[460,233,778,16,706],626:[460,233,529,-70,514],627:[460,233,533,16,603],628:[450,8,602,29,561],629:[460,10,500,29,470],630:[450,6,720,23,697],631:[475,4,667,37,629],632:[683,233,667,40,626],633:[450,10,370,30,360],634:[683,10,370,30,364],635:[450,233,418,30,468],636:[460,233,333,5,335],637:[460,233,370,7,339],638:[470,0,315,10,337],639:[470,0,350,5,332],640:[464,0,475,21,470],641:[464,0,475,21,470],642:[458,218,389,50,348],643:[683,233,322,-70,372],644:[683,218,304,-70,372],645:[470,233,400,15,457],646:[683,243,437,-23,422],647:[460,129,278,16,282],648:[579,233,270,13,283],649:[450,10,500,9,480],650:[450,10,537,46,490],651:[460,10,500,32,476],652:[464,0,500,-4,454],653:[464,0,722,21,694],654:[668,0,444,-2,459],655:[464,0,587,23,564],656:[450,218,528,27,569],657:[450,150,507,27,487],658:[450,233,413,12,392],659:[450,305,431,12,410],660:[683,0,450,47,400],661:[683,0,450,48,401],662:[662,14,450,47,400],663:[460,230,450,80,410],664:[679,17,723,33,690],665:[464,0,460,15,444],666:[475,14,479,20,430],667:[523,11,600,29,583],668:[464,0,572,21,560],669:[683,233,387,-23,412],670:[450,233,519,1,499],671:[464,0,470,21,441],672:[582,217,600,24,590],673:[683,0,450,48,401],674:[683,0,450,48,401],675:[683,10,802,27,775],676:[683,233,743,27,722],677:[683,160,864,27,844],678:[579,10,536,13,495],679:[683,233,483,13,540],680:[579,10,650,13,641],686:[469,232,619,15,612],687:[469,233,679,15,729]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/Regular/IPAExtensions.js");
