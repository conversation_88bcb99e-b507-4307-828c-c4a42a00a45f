﻿@model ZZZI34IndexViewModel
@{
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);

    if (AppMode)
    {
        Layout = "~/Views/QRCODEArtGallery/_LayoutWebView.cshtml";

    }
    else
    {
        Layout = "~/Views/Shared/_LayoutWebView1.cshtml";
    }
}
<style type="text/css">
    .App_hide {
        display: none;
    }

    .App_show {
        display: block;
    }

    .AppMode_hide {
        display: none;
    }

    .AppGird {
        font-size: 25px;
        color: red;
    }
</style>
<link href="~/Content/QRCODEArtGallery/eCoolArtGallery.css?@DateTime.Now.ToString("yyyy_MM_dd_H_mm_ss");" rel="stylesheet" />

@using (Html.BeginForm("Index", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1" }))
{
    @Html.HiddenFor(m => m.Search.WhereSCHOOL_NO)
    @Html.HiddenFor(m => m.Search.WhereUSER_NO)
    @Html.HiddenFor(m => m.Search.WhereSTATUS)
    <div id="check1"></div>
    @Html.AntiForgeryToken()
    <div id="OnePageContent" style="min-height:400px">
        @Html.Action("_WorkListPageContent", (string)ViewBag.BRE_NO)
    </div>

    @*if (!string.IsNullOrEmpty(Model?.ShareViewPHOTO_NO))
        {
            <!-- Modal -->
            <div class="modal fade" id="LikeNickNameModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                            <h4 class="modal-title" id="myModalLabel">推薦-帳號未登入請先登入或是訪客，請輸入訪客姓名</h4>
                        </div>
                        <div class="modal-body">
                            <div class="form-group">
                                @Html.Label("訪客姓名", htmlAttributes: new { @class = "col-md-3 control-label" })
                                <div class="col-md-9">
                                    @Html.Hidden("PHOTO_NO")
                                    @Html.Hidden("LikeCount")
                                    @Html.Editor("NICK_NAME", new { htmlAttributes = new { @class = "form-control input-md", @placeholder = "請輸訪客姓名" } })
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-primary" onclick="ModalSHARE_show()">確定</button>
                            <button type="button" class="btn btn-default" data-dismiss="modal">放棄</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Modal -->
            <div class="modal fade" id="LikeListModal" tabindex="-1" role="dialog" aria-labelledby="LikeListModal">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                            <h4 class="modal-title">按讚人員</h4>
                        </div>
                        <div class="modal-body modal-body-scroll" style="max-height:300px">
                            <div id="LikeListPageContent">
                                @Html.Action("_LikeListView", (string)ViewBag.BRE_NO)
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-default" data-dismiss="modal">關閉</button>
                        </div>
                    </div>
                </div>
            </div>
        }*@

}