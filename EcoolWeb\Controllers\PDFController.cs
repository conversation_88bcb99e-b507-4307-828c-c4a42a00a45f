﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace EcoolWeb.Controllers
{
    public class PDFController : Controller
    {

        [HttpPost]
        [ValidateInput(false)]
        [ValidateAntiForgeryToken]
        public ActionResult Index(string HtmlCode,string FileName="")
        {
            Uri contextUri = HttpContext.Request.Url;

            var baseUri = string.Format("{0}://{1}{2}", contextUri.Scheme,
            contextUri.Host, contextUri.Port == 80 ? string.Empty : ":" + contextUri.Port)+@"/"+ Request.Url.Segments[1];

            string oldStr = @""+@"/" + Request.Url.Segments[1];
            HtmlCode = HtmlCode.Replace(oldStr, baseUri);

            //說明 https://www.nrecosite.com/doc/NReco.PdfGenerator/
            var htmlToPdf = new NReco.PdfGenerator.HtmlToPdfConverter();
            htmlToPdf.PageHeaderHtml = "<div style='text-align:center'>臺北e酷幣</div>";
            htmlToPdf.PageFooterHtml = @"<div style='text-align:center'>Page: <span class='page'></span></div>";
            htmlToPdf.Size = NReco.PdfGenerator.PageSize.A4;
             var pdfBytes = htmlToPdf.GeneratePdf(HtmlCode);


            if (string.IsNullOrWhiteSpace(FileName))
            {
                FileName = DateTime.Now.ToString("yyyyMMdd") + ".pdf";
            }

            return File(pdfBytes, "application/unknow", FileName);
        }
    }
}