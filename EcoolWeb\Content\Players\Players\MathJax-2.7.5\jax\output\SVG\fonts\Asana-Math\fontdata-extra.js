/*
 *  /MathJax/jax/output/SVG/fonts/Asana-Math/fontdata-extra.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

(function(x){var z="2.7.5";var o=x.FONTDATA.DELIMITERS;var p="H",d="V";var c="AsanaMathJax_Alphabets",u="AsanaMathJax_Arrows",w="AsanaMathJax_DoubleStruck",A="AsanaMathJax_Fraktur",g="AsanaMathJax_Latin",t="AsanaMathJax_Main",l="AsanaMathJax_Marks",v="AsanaMathJax_Misc",D="AsanaMathJax_Monospace",y="AsanaMathJax_NonUnicode",q="AsanaMathJax_Normal",B="AsanaMathJax_Operators",a="AsanaMathJax_SansSerif",n="AsanaMathJax_Script",b="AsanaMathJax_Shapes",k="AsanaMathJax_Size1",j="AsanaMathJax_Size2",i="AsanaMathJax_Size3",h="AsanaMathJax_Size4",f="AsanaMathJax_Size5",e="AsanaMathJax_Size6",s="AsanaMathJax_Symbols",m="AsanaMathJax_Variants";var C={774:{dir:p,HW:[[282,t],[384,k],[542,j],[922,i],[1762,h]]},819:{dir:p,HW:[[433,l],[511,k],[675,j],[1127,i]],stretch:{rep:[57347,e],right:[57347,e]}},831:{dir:p,HW:[[433,l],[511,k],[675,j],[1127,i]],stretch:{rep:[57348,e],right:[57348,e]}},8261:{dir:d,HW:[[910,l],[1344,k],[1862,j],[2328,i]],stretch:{bot:[57350,e],ext:[57351,e],mid:[57352,e],top:[57353,e]}},8262:{dir:d,HW:[[910,l],[1344,k],[1862,j],[2328,i]],stretch:{bot:[57354,e],ext:[57355,e],mid:[57356,e],top:[57357,e]}},8400:{dir:p,HW:[[558,l]],stretch:{left:[8400,l],rep:[57358,e]}},8401:{dir:p,HW:[[558,l]],stretch:{rep:[57358,e],right:[8401,l]}},8406:{dir:p,HW:[[558,l],[807,k],[1127,j],[1878,i],[3579,h]],stretch:{left:[8406,l],rep:[57358,e]}},8407:{dir:p,HW:[[558,t],[807,k],[1127,j],[1878,i],[3579,h]],stretch:{rep:[57358,e],right:[8407,t]}},8417:{dir:p,HW:[[557,l]],stretch:{left:[8406,l],rep:[57358,e],right:[8407,t]}},8425:{dir:p,HW:[[630,l]],stretch:{left:[57359,e],rep:[57360,e],right:[57361,e]}},8430:{dir:p,HW:[[557,l]],stretch:{left:[8430,l],rep:[57362,e]}},8431:{dir:p,HW:[[557,l]],stretch:{rep:[57362,e],right:[8431,l]}},8617:{dir:p,HW:[[884,t]],stretch:{left:[57363,e],rep:[9135,s],right:[57370,e]}},8618:{dir:p,HW:[[884,t]],stretch:{left:[57371,e],rep:[9135,s],right:[57367,e]}},8720:{dir:d,HW:[[937,B],[1349,k],[1942,j],[2797,i]]},8721:{dir:d,HW:[[930,B],[1339,k],[1928,j],[2776,i]]},8745:{dir:d,HW:[[603,t],[1559,k],[2245,j],[2588,i]]},8747:{dir:d,HW:[[1327,t],[1964,k],[2711,j],[3470,i]],stretch:{bot:[8993,s],ext:[9134,s],top:[8992,s]}},8748:{dir:d,HW:[[1327,B],[1964,k],[2711,j],[3470,i]]},8749:{dir:d,HW:[[1327,B],[1964,k],[2711,j],[3470,i]]},8750:{dir:d,HW:[[1327,B],[1964,k],[2711,j],[3470,i]]},8751:{dir:d,HW:[[1327,B],[1964,k],[2711,j],[3470,i]]},8752:{dir:d,HW:[[1327,B],[1964,k],[2711,j],[3470,i]]},8753:{dir:d,HW:[[1327,B],[1964,k],[2711,j],[3470,i]]},8754:{dir:d,HW:[[1327,B],[1964,k],[2711,j],[3470,i]]},8755:{dir:d,HW:[[1327,B],[1964,k],[2711,j],[3470,i]]},8896:{dir:d,HW:[[939,B],[1559,k],[2588,j]]},8897:{dir:d,HW:[[939,B],[1559,k],[2588,j]]},8898:{dir:d,HW:[[939,B],[1559,k],[2588,j]]},8899:{dir:d,HW:[[939,B],[1559,k],[2245,j],[2588,i]]},9140:{dir:p,HW:[[602,t],[978,k],[1353,j],[1690,i]],stretch:{left:[57359,e],rep:[57360,e],right:[57361,e]}},9141:{dir:p,HW:[[602,t],[978,k],[1353,j],[1690,i]],stretch:{left:[57379,e],rep:[57380,e],right:[57381,e]}},9180:{dir:p,HW:[[942,t],[973,k],[1349,j],[1686,i]],stretch:{left:[57382,e],rep:[57383,e],right:[57384,e]}},9181:{dir:p,HW:[[942,t],[973,k],[1349,j],[1686,i]],stretch:{left:[57385,e],rep:[57386,e],right:[57387,e]}},9184:{dir:p,HW:[[900,t],[1360,k],[2056,j],[3108,i]]},9185:{dir:p,HW:[[900,t],[1360,k],[2056,j],[3108,i]]},10214:{dir:d,HW:[[910,s],[1025,k],[1535,j],[2045,i],[2556,h]]},10215:{dir:d,HW:[[910,s],[1025,k],[1535,j],[2045,i],[2556,h]]},10218:{dir:d,HW:[[885,s],[1020,k],[2041,j],[2552,i]]},10219:{dir:d,HW:[[885,s],[1020,k],[2041,j],[2552,i]]},10748:{dir:d,HW:[[953,s],[1372,k],[1893,j],[2366,i]]},10749:{dir:d,HW:[[953,s],[1372,k],[1893,j],[2366,i]]},10752:{dir:d,HW:[[1146,B],[1650,k],[2376,j]]},10753:{dir:d,HW:[[1149,B],[1650,k],[2376,j]]},10754:{dir:d,HW:[[1149,B],[1650,k],[2376,j]]},10755:{dir:d,HW:[[939,B],[1559,k],[2588,j]]},10756:{dir:d,HW:[[939,B],[1559,k],[2588,j]]},10757:{dir:d,HW:[[926,B],[1537,k],[2552,j]]},10758:{dir:d,HW:[[926,B],[1537,k],[2552,j]]},10759:{dir:d,HW:[[939,B],[1559,k],[2588,j]]},10760:{dir:d,HW:[[939,B],[1559,k],[2588,j]]},10761:{dir:d,HW:[[926,B],[1333,k],[1920,j]]},10764:{dir:d,HW:[[1327,B],[1964,k],[2711,j],[3470,i]]},10765:{dir:d,HW:[[1327,B],[1964,k],[2711,j],[3470,i]]},10766:{dir:d,HW:[[1327,B],[1964,k],[2711,j],[3470,i]]},10767:{dir:d,HW:[[1327,B],[1964,k],[2711,j],[3470,i]]},10768:{dir:d,HW:[[1327,B],[1964,k],[2711,j],[3470,i]]},10769:{dir:d,HW:[[1327,B],[1964,k],[2711,j],[3470,i]]},10770:{dir:d,HW:[[1327,B],[1964,k],[2711,j],[3470,i]]},10771:{dir:d,HW:[[1327,B],[1964,k],[2711,j],[3470,i]]},10772:{dir:d,HW:[[1327,B],[1964,k],[2711,j],[3470,i]]},10773:{dir:d,HW:[[1327,B],[1964,k],[2711,j],[3470,i]]},10774:{dir:d,HW:[[1327,B],[1964,k],[2711,j],[3470,i]]},10775:{dir:d,HW:[[1327,B],[1964,k],[2711,j],[3470,i]]},10776:{dir:d,HW:[[1327,B],[1964,k],[2711,j],[3470,i]]},10777:{dir:d,HW:[[1327,B],[1964,k],[2711,j],[3470,i]]},10778:{dir:d,HW:[[1327,B],[1964,k],[2711,j],[3470,i]]},10779:{dir:d,HW:[[1436,B],[2125,k],[2933,j],[3754,i]]},10780:{dir:d,HW:[[1436,B],[2125,k],[2933,j],[3754,i]]}};for(var r in C){if(C.hasOwnProperty(r)){o[r]=C[r]}}MathJax.Ajax.loadComplete(x.fontDir+"/fontdata-extra.js")})(MathJax.OutputJax.SVG);
