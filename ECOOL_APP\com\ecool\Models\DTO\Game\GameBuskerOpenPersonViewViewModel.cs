﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.Mvc;

namespace ECOOL_APP.EF
{
    public class GameBuskerOpenPersonViewViewModel
    {
        public string WhereTITLE_SHOW_ID { get; set; }


        public string WhereSCHOOL_NO { get; set; }


        public string WhereCLASS_NO { get; set; }


        public string WhereNAME { get; set; }


        public List<SelectListItem> SchoolNoSelectItem { get; set; }

        public List<SelectListItem> ClassItems { get; set; }


        /// <summary>
        /// 查詢
        /// </summary>
        public GameSearchViewModel Search { get; set; }

        public virtual ICollection<GameBuskerEditDetailsViewModel> Details { get; set; }

        public List<GameBuskerEditDetailsViewModel> PersonData { get; set; }

    }
}
