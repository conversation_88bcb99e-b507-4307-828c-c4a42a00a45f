/*
 *  /MathJax/jax/output/SVG/fonts/TeX/Fraktur/Regular/PUA.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax.SVG.FONTDATA.FONTS.MathJax_Fraktur,{58112:[683,32,497,75,431,"427 436Q427 434 427 425T429 398T430 362Q430 222 396 109L393 99L305 33Q218 -32 216 -32Q208 -29 142 22L91 68L78 81L77 94Q75 130 75 173Q75 245 87 347L135 385Q178 418 184 424L177 428Q174 429 170 431Q116 454 96 473T75 534Q79 608 154 683Q164 677 164 673Q164 670 157 662T144 637T137 598Q137 552 182 518T280 470T380 447T427 436ZM342 371L275 394L208 417Q203 417 192 399T168 334T156 229Q153 187 153 157Q153 141 156 135Q158 125 208 88T280 51Q306 51 326 120T346 297Q346 339 344 354T342 371"],58113:[616,30,498,35,432,"39 551L35 569L52 577Q72 586 98 595T140 610T158 616Q174 612 200 604T293 560T412 477Q414 475 417 472Q428 462 430 450T432 376Q432 223 401 124Q395 106 393 103T382 92Q351 68 281 20T206 -29Q201 -31 137 26L100 60L78 83L77 112Q76 132 76 170Q76 259 86 342L88 360L101 371Q116 386 163 422T215 459Q216 459 224 455T233 450L229 446Q225 442 218 434T203 419Q179 394 175 389T168 372Q156 334 156 262Q156 167 164 137Q168 125 196 102T252 62L278 45Q279 45 285 52T302 78T322 126T339 205T346 316Q346 367 344 389L343 406L326 423Q228 520 113 559L100 564L70 557L39 551"],58114:[680,215,333,29,339,"123 386L120 431Q116 476 116 511V520Q116 593 174 649Q207 680 236 680Q258 680 284 664T312 648Q318 648 327 656Q328 657 330 659Q337 664 337 661Q337 660 338 657Q338 652 339 648L268 566L260 574Q234 600 206 600Q182 600 164 585T145 541Q145 492 211 386L267 385H324L299 354H214V312Q214 86 193 -58L192 -69L116 -215H108Q92 -215 92 -212Q93 -211 100 -189T116 -135T128 -80Q134 -41 134 22Q134 54 130 185T125 349V354H29L59 385H91Q123 385 123 386"],58115:[679,224,329,28,318,"91 530Q91 564 116 600T164 656T194 678Q195 678 200 678T209 679Q268 679 316 639L293 593Q267 547 263 546H262Q260 546 256 553Q222 613 180 613Q160 613 146 599T132 564T170 474T210 388H318L296 356H206V322Q204 284 204 255Q202 221 202 161V99Q202 28 194 -22T160 -124Q148 -146 116 -199L101 -224L91 -220Q85 -218 84 -217T83 -215L101 -161Q116 -114 119 -73T122 108Q119 334 117 352V356H72L28 357L66 388H92Q118 388 118 389L109 433Q91 514 91 530"],58116:[471,214,503,52,449,"254 -150Q293 -150 328 -126T363 -54Q363 -38 352 29T339 98L250 34Q160 -30 159 -30L77 64V71Q74 95 74 174Q74 212 75 243T79 294T83 328T87 352T90 366L117 384Q206 446 238 464L250 471Q277 455 306 443T350 427L365 423Q367 423 405 443T443 465L449 455Q431 414 426 362T418 201Q418 135 420 121Q438 -4 438 -19Q438 -26 438 -31T434 -42T429 -51T420 -63T408 -77T391 -95T370 -119T346 -147T325 -170T309 -187T291 -200T274 -207T252 -213T225 -214Q175 -214 132 -196T70 -160L52 -143Q52 -138 90 -48Q90 -47 95 -47H101Q108 -81 146 -115T254 -150ZM341 136Q341 157 344 242T347 348V355L334 356Q299 359 262 367T203 383T179 391Q177 391 173 377T163 323T158 227Q158 164 161 128V121L174 106Q203 75 223 59L341 127V136"],58117:[686,20,333,26,315,"92 446Q92 603 82 664Q94 670 95 670L96 666Q98 661 101 651T108 633Q121 598 121 597L141 612Q247 686 250 686Q251 686 266 679Q261 674 243 659T213 632T190 597T173 546Q172 541 171 530T170 511T170 502Q171 502 222 542L273 582Q308 522 315 504L279 449L269 462Q231 506 215 506Q202 506 190 490Q164 458 164 395V390H279L266 373L254 355H167V306Q169 252 169 217Q170 195 170 147V117L200 92Q234 64 237 64Q243 64 277 81L311 99V75Q310 75 242 27L174 -20L156 -3Q88 60 81 60L79 62Q80 60 82 62Q87 67 87 290V355H57L26 356L73 390H92V446"],58118:[577,21,334,29,347,"117 531Q117 533 137 544T178 566L198 577Q200 577 204 575T208 572V570Q208 568 208 566T207 560Q197 496 197 397V392H321L295 353H199V260Q199 157 200 145V122L269 68Q271 67 274 67Q282 67 310 83T342 100Q343 100 345 92T346 83L211 -21L172 12Q117 59 117 63Q117 65 117 87T119 150T120 238V353H75L29 354L65 391H118V460Q117 498 117 531"],58119:[475,22,501,10,514,"337 91V78L324 71Q288 53 256 29T206 -8T180 -22Q174 -22 158 -9Q82 46 60 46H59L63 51Q67 56 73 68T85 96Q101 158 101 254Q101 300 95 330T83 370T66 394L53 388Q48 385 41 382T24 374Q22 376 20 378T16 381T13 383T10 385V386L119 475Q150 439 160 430L171 422V409Q173 377 173 300Q173 228 166 183T152 122T145 102Q207 81 242 58L337 104V111Q340 146 340 227Q340 320 339 351T338 408V423L422 469Q425 465 429 462L426 438Q413 354 413 251Q413 152 423 119Q426 110 435 96T452 82Q454 82 509 103Q514 98 514 89Q514 87 507 81T472 51T409 -7L395 -20Q393 -18 390 -17Q386 -14 382 -6Q380 -2 379 1Q369 24 361 40T348 62T341 73T338 84L337 91"]});MathJax.Ajax.loadComplete(MathJax.OutputJax.SVG.fontDir+"/Fraktur/Regular/PUA.js");
