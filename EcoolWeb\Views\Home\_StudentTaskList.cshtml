﻿@{
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
}
<img src="~/Content/img/web-bar2-revise-12.png" style="width:100%" class="imgEZ" alt="Responsive image" />
<div class="Div-EZ-task text-center">
    <div class="Details text-center">
        <div style="height:15px"></div>
        @*<div class="container" style="margin-top:10px">
                <div class="row" style="margin-top:10px">
                    <div class="col-md-4 col-md-offset-4">
                        <a class="group1 btn btn-block btn-social-lg btn-dropbox btn-social btn-lg " type="button" href="@Url.Action("VerifyList", "ADDI01",new { whereWritingStatus="0,2"})">
                            線上投稿任務
                        </a>
                        <a type="button" class="group1 btn btn-block btn-social-lg btn-dropbox btn-social btn-lg " href="@Url.Action("../ADDT/ADDTList_CheckPending", "ADDT")">
                            閱讀認證任務
                        </a>
                        <a type="button" class="group1 btn btn-block btn-social-lg btn-dropbox btn-social btn-lg " href="@Url.Action("ArtGalleryList", "ZZZI34",new { WhereVerify = true,ISTASKLIST=true})">
                            線上藝廊任務
                        </a>
                    </div>
                </div>
            </div>*@
        <div class="row">
            <div class="col-md-6" style="height:65px;">
                <a href='@Url.Action("Index", "ADDI05",new { FirstPage = true, SouBre_NO="ADDI05"})'>
                    <img src="~/Content/img/web-studenttask-01n.png" class="imgEZ" alt="Responsive image" style="max-width:200px" title="有獎徵答" />
                    &nbsp;<span class="lnkFont2">@ViewBag.ADDT11Temp </span>
                </a>
            </div>

            <div class="col-md-6" style="height:65px;">
                <a href='@Url.Action("Index", "ADDI10",new { FirstPage = true, SouBre_NO="ADDI10"})'>
                    <img src="~/Content/img/web-studenttasklist-02n.png" class="imgEZ" alt="Responsive image" style="max-width:200px;" title="投票任務" />
                    &nbsp;<span class="lnkFont2">@ViewBag.ADDI10IndexListViewModelTemp</span>
                </a>
            </div>

            <div class="col-md-6" style="height:65px;">
                <a href='@Url.Action("Index", "ADDI12",new { FirstPage = true, SouBre_NO="ADDI10"})'>
                    <img src="~/Content/img/studenttasklist-onLineArt.png" class="imgEZ" alt="Responsive image" style="max-width:200px" title="小小舞台任務" />
                    &nbsp;<span class="lnkFont2">@ViewBag.PremierCount</span>
                </a>
            </div>
            @if (ViewBag.NoteReadLevel > 0)
            {
                <div class="col-md-6" style="height:65px;">
                    <a href='@Url.Action("ReadLevelNote", "Home")'>
                        <img src="~/Content/img/web-studenttasklist-10n.png" class="imgEZ" alt="Responsive image" style="max-width:200px" title="小小舞台任務" />
                        &nbsp;<span class="lnkFont2">@ViewBag.NoteReadLevel</span>
                    </a>
                </div>
            }
            else
            {
                <div class="col-md-6" style="height:65px;">
                    <a href='#' onclick="NOMsg('閱讀認證升級')">
                        <img src="~/Content/img/web-studenttasklist-10n.png" class="imgEZ" alt="Responsive image" style="max-width:200px" title="小小舞台任務" />
                        &nbsp;<span class="lnkFont2">@ViewBag.NoteReadLevel</span>
                    </a>
                </div>

            }
            @*<div class="col-md-6" style="height:65px;">
                    <a href='@Url.Action("Index", "ZZZI11", new { STATUS = ECOOL_APP.com.ecool.Models.entity.uADDT16.STATUS_Val.STATUS_1 })'>
                        <img src="~/Content/img/web-studenttasklist-10n.png" class="imgEZ" alt="Responsive image" style="max-width:200px" title="升級訊息" />
                        &nbsp;<span class="lnkFont2">@ViewBag.NotePassGrade</span>
                    </a>
                </div>*@

            <div class="col-md-6" style="height:65px;">
                @if (ViewBag.Chance_BIRTHDAY == "1")
                {
                    <a href='@Url.Action("BirthdayGet", "Home", new { STATUS = ECOOL_APP.com.ecool.Models.entity.uADDT16.STATUS_Val.STATUS_2 })'>
                        <img src="~/Content/img/web-studenttasklist-18n.png" class="imgEZ" alt="Responsive image" style="max-width:200px" title="升級訊息" />
                        &nbsp;<span class="lnkFont2">@ViewBag.Chance_BIRTHDAY</span>
                    </a>

                }
                else
                {
                    <a href='#' onclick="NOMsg('生日禮')">
                        <img src="~/Content/img/web-studenttasklist-18n.png" class="imgEZ" alt="Responsive image" style="max-width:200px" title="升級訊息" />
                        &nbsp;<span class="lnkFont2">@ViewBag.Chance_BIRTHDAY</span>
                    </a>
                }
            </div>

            @*<div class="col-md-6" style="height:65px;">
                    <a href='@Url.Action("Index", "ZZZI31", new { STATUS = ECOOL_APP.com.ecool.Models.entity.uQAT16.STATUS_Val.STATUS_1 })'>
                        <img src="~/Content/img/web-teacher-18n.png" class="imgEZ" alt="Responsive image" style="max-width:200px" title="生日禮" />
                        &nbsp;<span class="lnkFont2">@ViewBag.Chance_BIRTHDAY</span>
                    </a>
                </div>*@
        </div>
    </div>
</div>
<script>
    function NOMsg(str)
    {

        alert("目前沒有「" + str+"」任務");

    }


</script>