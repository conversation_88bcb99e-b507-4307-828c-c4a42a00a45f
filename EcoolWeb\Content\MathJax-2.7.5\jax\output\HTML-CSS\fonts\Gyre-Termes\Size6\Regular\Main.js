/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Gyre-Termes/Size6/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.GyreTermesMathJax_Size6={directory:"Size6/Regular",family:"GyreTermesMathJax_Size6",testString:"\u00A0\u0302\u0303\u0306\u030C\u0311\u032C\u032D\u032E\u032F\u0330\u2016\u2044\u20E9\u221A",32:[0,0,250,0,0],40:[1452,952,624,143,528],41:[1452,952,624,96,481],47:[2272,1772,1536,80,1456],91:[1466,966,491,143,395],92:[2272,1772,1536,80,1456],93:[1466,966,491,96,348],123:[1454,954,547,96,451],124:[1444,944,219,80,139],125:[1454,954,547,96,451],160:[0,0,250,0,0],770:[700,-535,1496,0,1496],771:[689,-531,1491,0,1491],774:[695,-539,1515,0,1515],780:[695,-530,1496,0,1496],785:[710,-553,1515,0,1515],812:[-70,235,1496,0,1496],813:[-80,245,1496,0,1496],814:[-70,226,1515,0,1515],815:[-88,244,1515,0,1515],816:[-88,246,1491,0,1491],8214:[1444,944,378,80,298],8260:[2272,1772,1536,80,1456],8425:[758,-548,2625,0,2625],8730:[1768,1242,674,120,700],8739:[1444,944,219,80,139],8741:[1444,944,378,80,298],8968:[1466,944,491,143,395],8969:[1466,944,491,96,348],8970:[1444,966,491,143,395],8971:[1444,966,491,96,348],9001:[2274,1774,554,96,458],9002:[2274,1774,554,96,458],9140:[758,-548,2625,0,2625],9141:[-98,308,2625,0,2625],9180:[758,-541,3514,0,3514],9181:[-91,308,3514,0,3514],9182:[792,-525,3519,0,3519],9183:[-75,341,3519,0,3519],9184:[748,-538,3584,0,3584],9185:[-88,298,3584,0,3584],10214:[1466,966,495,143,399],10215:[1466,966,495,96,352],10216:[2274,1774,554,96,458],10217:[2274,1774,554,96,458],10218:[2274,1774,825,96,729],10219:[2274,1774,825,96,729],10222:[1451,951,418,143,322],10223:[1451,951,418,96,275],57344:[372,-128,167,0,167],57345:[372,-128,166,0,166],57346:[372,-128,167,0,167],57347:[596,0,547,230,314],57348:[796,0,219,80,139],57349:[796,0,219,80,139],57350:[796,0,219,80,139],57351:[596,0,547,233,317],57352:[710,-600,188,0,188],57353:[644,-600,125,0,125],57354:[644,-600,187,0,187],57355:[644,-600,187,0,187],57356:[644,-600,125,0,125],57357:[710,-600,188,0,188],57358:[710,-534,191,0,191],57359:[644,-600,128,0,128],57360:[644,-600,191,0,191],57361:[644,-600,191,0,191],57362:[644,-600,128,0,128],57363:[710,-534,191,0,191],57364:[710,-534,218,0,218],57365:[644,-600,146,0,146],57366:[710,-534,218,0,218],57367:[-150,194,188,0,188],57368:[-150,194,125,0,125],57369:[-150,260,187,0,187],57370:[-150,260,187,0,187],57371:[-150,194,125,0,125],57372:[-150,194,188,0,188],57373:[-84,260,191,0,191],57374:[-150,194,128,0,128],57375:[-150,194,191,0,191],57376:[-150,194,191,0,191],57377:[-150,194,128,0,128],57378:[-84,260,191,0,191],57379:[430,-70,379,0,379],57380:[276,-224,252,0,252],57381:[276,-224,379,0,379],57382:[276,-224,379,0,379],57383:[276,-224,252,0,252],57384:[430,-70,379,0,379],57385:[379,0,520,234,286],57386:[252,0,520,234,286],57387:[379,0,520,80,440],57388:[379,0,520,80,440],57389:[252,0,520,234,286],57390:[379,0,520,234,286],57391:[430,-70,289,0,289],57392:[276,-224,72,0,72],57393:[430,-70,288,0,288],57394:[276,-224,289,0,289],57395:[276,-224,289,0,289],57396:[276,-224,72,0,72],57397:[430,-70,288,0,288],57398:[430,-70,289,0,289],57399:[430,-70,450,0,450],57400:[276,-224,300,0,300],57401:[430,-70,450,0,450],57402:[450,0,520,80,440],57403:[300,0,520,234,286],57404:[450,0,520,80,440],57405:[430,-70,343,0,343],57406:[276,-224,86,0,86],57407:[430,-70,342,0,342],57408:[430,-70,343,0,343],57409:[430,-70,446,0,446],57410:[276,-224,298,0,298],57411:[276,-224,446,0,446],57412:[276,-224,446,0,446],57413:[276,-224,298,0,298],57414:[430,-70,446,0,446],57415:[446,0,520,234,286],57416:[298,0,520,234,286],57417:[446,0,520,80,440],57418:[446,0,520,80,440],57419:[298,0,520,234,286],57420:[446,0,520,234,286],57421:[430,-70,450,0,450],57422:[276,-224,300,0,300],57423:[430,-70,450,0,450],57424:[430,-70,450,0,450],57425:[276,-224,300,0,300],57426:[430,-70,450,0,450],57427:[430,-70,379,0,379],57428:[276,-224,252,0,252],57429:[430,-70,379,0,379],57430:[430,-70,379,0,379],57431:[276,-224,252,0,252],57432:[430,-70,379,0,379],57433:[379,0,520,80,440],57434:[252,0,520,234,286],57435:[379,0,520,80,440],57436:[379,0,520,80,440],57437:[252,0,520,234,286],57438:[379,0,520,80,440],57439:[508,-224,389,0,389],57440:[276,-224,259,0,259],57441:[430,-70,388,0,388],57442:[430,-70,388,0,388],57443:[276,-224,259,0,259],57444:[508,-224,389,0,389],57445:[508,-18,389,0,389],57446:[276,-224,259,0,259],57447:[430,-70,388,0,388],57448:[430,-70,388,0,388],57449:[276,-224,259,0,259],57450:[508,-18,389,0,389],57451:[430,-224,375,0,375],57452:[276,-224,250,0,250],57453:[276,-224,375,0,375],57454:[276,-224,375,0,375],57455:[276,-224,250,0,250],57456:[430,-224,375,0,375],57457:[276,-70,375,0,375],57458:[276,-224,250,0,250],57459:[276,-224,375,0,375],57460:[276,-224,375,0,375],57461:[276,-224,250,0,250],57462:[276,-70,375,0,375],57463:[375,0,366,80,132],57464:[250,0,366,80,132],57465:[375,0,366,80,286],57466:[375,0,366,80,286],57467:[250,0,366,80,132],57468:[375,0,366,80,132],57469:[375,0,366,234,286],57470:[250,0,366,234,286],57471:[375,0,366,80,286],57472:[375,0,366,80,286],57473:[250,0,366,234,286],57474:[375,0,366,234,286],57475:[476,130,383,0,383],57476:[476,-24,255,0,255],57477:[630,-24,382,0,382],57478:[630,-24,383,0,383],57479:[476,-24,255,0,255],57480:[476,130,382,0,382],57481:[383,0,920,234,840],57482:[255,0,920,234,686],57483:[382,0,920,80,686],57484:[382,0,920,80,686],57485:[255,0,920,234,686],57486:[383,0,920,234,840],57487:[630,130,379,0,379],57488:[476,-24,252,0,252],57489:[476,-24,379,0,379],57490:[476,-24,379,0,379],57491:[476,-24,252,0,252],57492:[630,130,379,0,379],57493:[379,0,920,234,686],57494:[252,0,920,234,686],57495:[379,0,920,80,840],57496:[379,0,920,80,840],57497:[252,0,920,234,686],57498:[379,0,920,234,686],57499:[676,176,379,0,379],57500:[676,176,252,0,252],57501:[830,330,379,0,379],57502:[830,330,379,0,379],57503:[676,176,252,0,252],57504:[676,176,379,0,379],57505:[526,-128,375,0,375],57506:[372,-128,250,0,250],57507:[372,26,375,0,375],57508:[372,26,375,0,375],57509:[372,-128,250,0,250],57510:[526,-128,375,0,375],57511:[470,-30,379,0,379],57512:[352,-148,252,0,252],57513:[352,-148,379,0,379],57514:[352,-148,379,0,379],57515:[352,-148,252,0,252],57516:[470,-30,379,0,379],57517:[379,0,600,198,402],57518:[252,0,600,198,402],57519:[379,0,600,80,520],57520:[379,0,600,80,520],57521:[252,0,600,198,402],57522:[379,0,600,198,402],57523:[470,-30,450,0,450],57524:[352,-148,300,0,300],57525:[470,-30,450,0,450],57526:[450,0,600,80,520],57527:[300,0,600,198,402],57528:[450,0,600,80,520],57529:[470,-30,289,0,289],57530:[352,-148,72,0,72],57531:[500,0,288,0,288],57532:[352,-148,289,0,289],57533:[352,-148,289,0,289],57534:[352,-148,72,0,72],57535:[500,0,288,0,288],57536:[470,-30,289,0,289],57537:[470,-30,343,0,343],57538:[352,-148,86,0,86],57539:[500,0,342,0,342],57540:[470,-30,343,0,343],57541:[470,-30,446,0,446],57542:[352,-148,298,0,298],57543:[470,-30,446,0,446],57544:[470,-30,446,0,446],57545:[352,-148,298,0,298],57546:[470,-30,446,0,446],57547:[572,72,446,0,446],57548:[428,-72,298,0,298],57549:[428,-72,446,0,446],57550:[428,-72,446,0,446],57551:[428,-72,298,0,298],57552:[572,72,446,0,446],57553:[470,-30,463,0,463],57554:[344,-156,308,0,308],57555:[344,-156,462,0,462],57556:[344,-156,463,0,463],57557:[344,-156,308,0,308],57558:[470,-30,462,0,462],57559:[463,0,600,206,394],57560:[308,0,600,206,394],57561:[462,0,600,80,520],57562:[462,0,600,80,520],57563:[308,0,600,206,394],57564:[463,0,600,206,394],57565:[469,0,600,80,520],57566:[312,0,600,206,394],57567:[469,0,600,80,520],57568:[470,-30,469,0,469],57569:[344,-156,313,0,313],57570:[470,-30,469,0,469],57571:[470,-30,446,0,446],57572:[326,-174,298,0,298],57573:[326,-174,446,0,446],57574:[326,-174,446,0,446],57575:[326,-174,298,0,298],57576:[470,-30,446,0,446],57577:[446,0,600,224,376],57578:[298,0,600,224,376],57579:[446,0,600,80,520],57580:[446,0,600,80,520],57581:[298,0,600,224,376],57582:[446,0,600,224,376],57583:[470,-30,450,0,450],57584:[326,-174,300,0,300],57585:[470,-30,450,0,450],57586:[450,0,600,80,520],57587:[300,0,600,224,376],57588:[450,0,600,80,520],57589:[-70,114,167,0,167],57590:[-70,114,166,0,166],57591:[-70,114,167,0,167],57592:[-70,228,167,0,167],57593:[-70,228,166,0,166],57594:[-70,228,167,0,167],57595:[632,-588,167,0,167],57596:[632,-588,166,0,166],57597:[632,-588,167,0,167],57598:[746,-588,167,0,167],57599:[746,-588,166,0,166],57600:[746,-588,167,0,167],57601:[1201,0,418,143,322],57602:[794,0,418,143,227],57603:[1201,0,418,143,322],57604:[1201,0,418,96,275],57605:[794,0,418,191,275],57606:[1201,0,418,96,275],57607:[811,0,495,143,399],57608:[810,0,495,143,315],57609:[811,0,495,143,399],57610:[811,0,495,96,352],57611:[810,0,495,180,352],57612:[811,0,495,96,352],57613:[702,-525,880,0,880],57614:[702,-618,874,0,874],57615:[792,-618,1759,0,1759],57616:[702,-525,880,0,880],57617:[-75,252,880,0,880],57618:[-168,252,874,0,874],57619:[-168,341,1759,0,1759],57620:[-75,252,880,0,880],57621:[758,-541,1757,0,1757],57622:[758,-674,874,0,874],57623:[758,-541,1757,0,1757],57624:[-91,308,1757,0,1757],57625:[-224,308,874,0,874],57626:[-91,308,1757,0,1757],57627:[758,-548,1313,0,1313],57628:[758,-674,875,0,875],57629:[758,-548,1312,0,1312],57630:[-98,308,1313,0,1313],57631:[-224,308,875,0,875],57632:[-98,308,1312,0,1312],57633:[748,-538,1792,0,1792],57634:[748,-664,1194,0,1194],57635:[748,-538,1792,0,1792],57636:[-88,298,1792,0,1792],57637:[-214,298,1194,0,1194],57638:[-88,298,1792,0,1792],57639:[276,-224,167,0,167],57640:[276,-224,166,0,166],57641:[276,-224,167,0,167],57642:[796,0,378,80,298],57643:[796,0,378,80,298],57644:[796,0,378,80,298],57645:[468,-32,167,0,167],57646:[468,-32,166,0,166],57647:[468,-32,167,0,167],57648:[564,64,167,0,167],57649:[564,64,166,0,166],57650:[564,64,167,0,167],57651:[1053,0,674,462,514],57652:[301,0,674,462,700],57653:[1161,0,800,334,720],57654:[1161,0,800,80,466],57655:[1161,0,1275,334,1195],57656:[580,0,1275,334,941],57657:[1161,0,1275,80,941],57658:[1161,0,1750,334,1670],57659:[580,0,1750,334,1416],57660:[1161,0,1750,80,1416]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"GyreTermesMathJax_Size6"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Size6/Regular/Main.js"]);
