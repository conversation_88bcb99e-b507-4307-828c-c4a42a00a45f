﻿using com.ecool.service;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Web;
using ECOOL_APP;
using ECOOL_APP.EF;
using System.Web.Mvc;
using log4net;
using System.Text.RegularExpressions;

namespace EcoolWeb.Models
{
    public class UserProfileHelper
    {
        public UserProfile MyEntity = new UserProfile();

        private static log4net.ILog logger = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        public UserProfileHelper()
        {
        }

        public static UserProfile Get()
        {
            System.Diagnostics.Debug.WriteLine(HttpContext.Current.Session.SessionID);
            UserProfile u = (UserProfile)HttpContext.Current.Session[CONSTANT.SESSION_USER_KEY];

          
             if (HttpContext.Current.Session == null && u==null) return null;
            string SchoolNo = GetSchoolNo();
           
            
            if (u != null)
            {
                LogHelper.AddLogToDB(u.SCHOOL_NO, u.USER_NO, System.Web.HttpContext.Current.Request.UserHostAddress, "GET_session" + u.SCHOOL_NO+"_"+u.USER_NO, "LoginSuccess");
               // LogHelper.AddLogToDB(u.SCHOOL_NO, u.USER_NO, System.Web.HttpContext.Current.Request.UserHostAddress, "GET_session" + HttpContext.Current.Session.SessionID, "LoginSuccess");
                //if (u.SCHOOL_NO != SchoolNo)
                //{
                //    u = null;
                //    HttpContext.Current.Session[CONSTANT.SESSION_USER_KEY] = null;
                //}
            }
            return u;
        }

        public static string GetUUID()
        {
            if (HttpContext.Current.Session == null) return null;
            string UUIDStr;
            object UUID = HttpContext.Current.Request.RequestContext.RouteData.Values["UUID"];
           
            if (UUID != null)
            {
                UUIDStr = UUID.ToString();
                if (UUIDStr != string.Empty) return UUIDStr;
            }

            UUID = HttpContext.Current.Request.QueryString["UUID"];
            if (UUID != null)
            {
                UUIDStr = UUID.ToString();
                if (UUIDStr != string.Empty)
                {
                    HttpContext.Current.Session[CONSTANT.SESSION_UUID_KEY] = UUID;
                    return UUIDStr;
                }
            }

            UUID = HttpContext.Current.Session[CONSTANT.SESSION_UUID_KEY];

            if (UUID == null)
            {
                UUID = GetUUIDCookie();
            }

            return (string)UUID;
        }

        public static void SetUUID(string UUID)
        {
            if (string.IsNullOrWhiteSpace(UUID) == false)
            {
                HttpContext.Current.Session[CONSTANT.SESSION_UUID_KEY] = UUID;
            }
        }

        public static string GetSchoolNo()
        {
            if (HttpContext.Current.Session == null) return null;

            object SchoolNo;
            if (HttpContext.Current.Session[CONSTANT.SESSION_SCHOOL_KEY] != null)
            {
                SchoolNo = HttpContext.Current.Session[CONSTANT.SESSION_SCHOOL_KEY];
            }
            else
            {
                SchoolNo = HttpContext.Current.Request.RequestContext.RouteData.Values["school"];
                if (SchoolNo != null) return SchoolNo.ToString();
            }
            return (string)SchoolNo;
        }

        public static string GetSchoolSName()
        {
            string SchoolNo = GetSchoolNo();
            if (string.IsNullOrEmpty(SchoolNo)) return string.Empty;
            Dictionary<string, BDMT01> BDMT01List = GetSchoolList();
            BDMT01 school = BDMT01List[SchoolNo];
            if (school != null)
                if (string.IsNullOrWhiteSpace(school.SHORT_NAMEDESC))
                {
                    school.SHORT_NAMEDESC = school.SHORT_NAME;
                }
                
                return school.SHORT_NAMEDESC;
            return string.Empty;
        }

        public static string GetSchoolSName(string SchoolNo)
        {
            if (string.IsNullOrEmpty(SchoolNo)) return string.Empty;
            Dictionary<string, BDMT01> BDMT01List = GetSchoolList();
            BDMT01 school = BDMT01List[SchoolNo];
            if (school != null) return school.SHORT_NAME;
            return string.Empty;
        }

        public static BDMT01 GetSchool(string SchoolNo)
        {
            if (string.IsNullOrEmpty(SchoolNo)) return null;
            Dictionary<string, BDMT01> BDMT01List = GetSchoolList();
            BDMT01 school = BDMT01List[SchoolNo];
            return school;
        }

        public static string GetSchoolType()
        {
            string SchoolNo = GetSchoolNo();
            if (string.IsNullOrEmpty(SchoolNo)) return BDMT01.SchoolType.ElementarySchool;

            using (ECOOL_DEVEntities db = new ECOOL_DEVEntities())
            {
                var B01 = db.BDMT01.Where(A => A.SCHOOL_NO == SchoolNo).FirstOrDefault();

                if (B01 == null)
                {
                    return BDMT01.SchoolType.ElementarySchool;
                }
                else
                {
                    if (string.IsNullOrWhiteSpace(B01.SCHOOL_TYPE)) return BDMT01.SchoolType.ElementarySchool;
                    else return B01.SCHOOL_TYPE;
                }
            }
        }

        public static string GetSchoolName()
        {
            string SchoolNo = GetSchoolNo();
            if (string.IsNullOrEmpty(SchoolNo)) return string.Empty;
            Dictionary<string, BDMT01> BDMT01List = GetSchoolList();
            BDMT01 school = BDMT01List[SchoolNo];
            if (school != null) return school.SCHOOL_NAME;
            return string.Empty;
        }

        public static void SetBRE_NO(string BRE_NO)
        {
            if (string.IsNullOrWhiteSpace(BRE_NO) == false)
            {
                HttpContext.Current.Session[CONSTANT.SESSION_BRE_NO_KEY] = BRE_NO;
            }
        }

        public static string GetBRE_NO()
        {
            if (HttpContext.Current.Session == null) return null;

            object SouBre_NO;

            if (HttpContext.Current.Session[CONSTANT.SESSION_BRE_NO_KEY] != null)
            {
                SouBre_NO = HttpContext.Current.Session[CONSTANT.SESSION_BRE_NO_KEY];
            }
            else
            {
                SouBre_NO = HttpContext.Current.Request.RequestContext.RouteData.Values["SouBre_NO"] ?? "";
            }
            return (string)SouBre_NO;
        }

        public static List<BDMT01> GetSchoolLists()
        {
            try
            {
                using (ECOOL_DEVEntities db = new ECOOL_DEVEntities())
                {
                    return db.BDMT01.OrderByDescending(a => a.CITY).ThenBy(a => a.ZONE_ID).ThenBy(a => a.SHORT_NAME).ToListNoLock();
                }

            }
            catch (Exception e) {
                logger.Info("USERProfile 取學校" + e.Message);
                logger.Info("USERProfile 取學校"+e.StackTrace);
                return null;

            }
         
        }

        public static Dictionary<string, BDMT01> GetSchoolList()
        {
            Dictionary<string, BDMT01> BDMT01List = null;

            using (ECOOL_DEVEntities db = new ECOOL_DEVEntities())
            {
                List<BDMT01> SchoolList = db.BDMT01.ToList();
                BDMT01List = new Dictionary<string, BDMT01>();
                foreach (BDMT01 school in SchoolList)
                {
                    BDMT01List.Add(school.SCHOOL_NO, school);
                }
            }

            //object cacheBDMT01 = HttpContext.Current.Cache["BDMT01"];
            //if ((cacheBDMT01 is Dictionary<string, BDMT01>))
            //{
            //    BDMT01List = (Dictionary<string, BDMT01>)cacheBDMT01;
            //}
            //else
            //{
            //    using (ECOOL_DEVEntities db = new ECOOL_DEVEntities())
            //    {
            //        List<BDMT01> SchoolList = db.BDMT01.ToList();
            //        BDMT01List = new Dictionary<string, BDMT01>();
            //        foreach (BDMT01 school in SchoolList)
            //        {
            //            BDMT01List.Add(school.SCHOOL_NO, school);
            //        }
            //    }
            //    HttpContext.Current.Cache.Insert("BDMT01", BDMT01List, null,
            //                                        System.Web.Caching.Cache.NoAbsoluteExpiration,
            //                                            new TimeSpan(4, 0, 0));

            //}
            return BDMT01List;
        }

        public static void Set(UserProfile LoginUser)
        {
         
            HttpContext.Current.Session[CONSTANT.SESSION_USER_KEY] = LoginUser;
            if (LoginUser != null)
            {
                HttpContext.Current.Session[CONSTANT.SESSION_SCHOOL_KEY] = LoginUser.SCHOOL_NO;
                SetCookieData(LoginUser.SCHOOL_NO, LoginUser.RoleID_Default);
            }
        }

        public static void Set(string SCHOOL_NO)
        {
            var SessionSCHOOL_NO = UserProfileHelper.GetSchoolNo();
            if (string.IsNullOrWhiteSpace(SCHOOL_NO) == false)
            {
                if (SessionSCHOOL_NO != null && SessionSCHOOL_NO != SCHOOL_NO)
                {
                    SessionDispose();
                }
                HttpContext.Current.Session[CONSTANT.SESSION_SCHOOL_KEY] = SCHOOL_NO;

                SetCookieData(SCHOOL_NO);
            }
            else if (string.IsNullOrWhiteSpace(SessionSCHOOL_NO) == false)
            {
                SessionDispose();
            }
        }

        public static void SessionDispose()
        {
            HttpContext.Current.Session[CONSTANT.SESSION_SCHOOL_KEY] = null;
            HttpContext.Current.Session[CONSTANT.SESSION_USER_KEY] = null;
            UserProfileHelper.ClearCookie();
        }

        public static void UserSessionDispose()
        {
            HttpContext.Current.Session[CONSTANT.SESSION_USER_KEY] = null;
        }

        /// <summary>
        /// 取得自已班級  --學生>
        /// </summary>
        /// <param name="user"></param>
        /// <returns></returns>
        public static int? GetMyGRADE(UserProfile user)
        {
            if (user != null)
            {
                if (user.GRADE != 0)
                {
                    return user.GRADE;
                }
                else if (string.IsNullOrWhiteSpace(user.TEACH_CLASS_NO) == false)
                {
                    return Convert.ToInt16(new ECOOL_APP.com.ecool.util.StringHelper().StrLeft(user.TEACH_CLASS_NO, 1));
                }
                else
                {
                    return null;
                }
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// 取得角色娃娃路徑
        /// </summary>
        /// <param name="user"></param>
        /// <returns></returns>
        public static string GetRoleDollUrl(UserProfile user)
        {
            string ImgSrc = string.Empty;

            if (string.IsNullOrWhiteSpace(user.PlayerUrl))
            {
                if (user.SEX == "1") //男預設值
                {
                    ImgSrc = "~/Content/img/web-student-allpage-33plus.png";
                }
                else //女預設值
                {
                    ImgSrc = "~/Content/img/web-student_allpage-17.png";
                }
            }
            else
            {
                ImgSrc = user.PlayerUrl;
            }

            return ImgSrc;
        }

        /// <summary>
        /// 閱讀認證等級 下拉式選單
        /// </summary>
        /// <param name="defaultSelectValue"></param>
        /// <returns></returns>
        static public List<SelectListItem> GetSelectListItemImgReadLEVEL(string defaultSelectValue = null)
        {
            Dictionary<byte, string> DictionaryImgReadLEVEL = SetSECImgReadLEVEL();

            List<SelectListItem> ReadLEVELItems = new List<SelectListItem>();
            ReadLEVELItems.Add(new SelectListItem() { Text = "無", Value = "", Selected = string.IsNullOrWhiteSpace(defaultSelectValue) });

            foreach (var item in DictionaryImgReadLEVEL)
            {
                ReadLEVELItems.Add(new SelectListItem() { Text = "滿" + item.Key + "級", Value = (item.Key).ToString(), Selected = (item.Key).ToString() == defaultSelectValue });
            }

            return ReadLEVELItems;
        }

        /// <summary>
        /// 取得閱讀認證等級圖示 for Dictionary  (舊)
        /// </summary>
        /// <param name="ReadLEVEL"></param>
        /// <returns></returns>
        public static Dictionary<byte, string> GetImgReadLEVEL(byte ReadLEVEL)
        {
            Dictionary<byte, string> DictionaryImgReadLEVEL = new Dictionary<byte, string>();
            DictionaryImgReadLEVEL = SetSECImgReadLEVEL();

            Dictionary<byte, string> ReturnValue = new Dictionary<byte, string>();

            foreach (var item in DictionaryImgReadLEVEL)
            {
                if (ReadLEVEL >= item.Key)
                {
                    ReturnValue.Add(item.Key, item.Value);
                }
            }

            return ReturnValue;
        }

        /// <summary>
        /// 取得閱讀認證等級單一圖示
        /// </summary>
        /// <param name="ReadLEVEL"></param>
        /// <returns></returns>
        public static string GetImgReadUrl(byte? ReadLEVEL)
        {
            if (ReadLEVEL == null) return null;

            Dictionary<byte, string> DictionaryImgReadLEVEL = SetSECImgReadLEVEL();

            if (DictionaryImgReadLEVEL.ContainsKey((byte)ReadLEVEL))
            {
                return DictionaryImgReadLEVEL[(byte)ReadLEVEL];
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// 我的秘書 取得閱讀認證等級圖示 for Dictionary
        /// </summary>
        /// <param name="ReadLEVEL"></param>
        /// <returns></returns>
        public static Dictionary<byte, string> GetSECImgReadLEVEL(byte ReadLEVEL)
        {
            Dictionary<byte, string> DictionaryImgReadLEVEL = new Dictionary<byte, string>();
            DictionaryImgReadLEVEL = SetSECImgReadLEVEL();

            Dictionary<byte, string> ReturnValue = new Dictionary<byte, string>();

            foreach (var item in DictionaryImgReadLEVEL)
            {
                if (ReadLEVEL >= item.Key)
                {
                    ReturnValue.Add(item.Key, item.Value);
                }
            }

            Dictionary<byte, string> HiedImg = new Dictionary<byte, string>();
            HiedImg = SetSECHiedImgReadLEVEL();

            foreach (var item in HiedImg)
            {
                if (ReadLEVEL < item.Key)
                {
                    ReturnValue.Add(item.Key, item.Value);
                }
            }

            return ReturnValue;
        }

        /// <summary>
        /// 取得跑步獎牌圖示 for Dictionary
        /// </summary>
        /// <param name="runTotal"></param>
        /// <returns></returns>
        public static string GetImgRunMedal(double runTotal)
        {
            Dictionary<double, string> medals = new Dictionary<double, string>();
            medals = SetSECHideImgRunMedals();

            foreach (var item in medals)
            {
                if (runTotal < item.Key)
                {
                    return item.Value;
                }
            }

            return medals.First().Value;
        }

        /// <summary>
        /// 護照等級 下拉式選單
        /// </summary>
        /// <param name="defaultSelectValue"></param>
        /// <returns></returns>
        static public List<SelectListItem> GetSelectListItemImgPassportLEVEL(string defaultSelectValue = null)
        {
            Dictionary<byte, string> DictionaryImgReadLEVEL = SetSECImgPassportLEVEL();

            List<SelectListItem> ReadLEVELItems = new List<SelectListItem>();
            ReadLEVELItems.Add(new SelectListItem() { Text = "無", Value = "", Selected = string.IsNullOrWhiteSpace(defaultSelectValue) });

            foreach (var item in DictionaryImgReadLEVEL)
            {
                ReadLEVELItems.Add(new SelectListItem() { Text = "滿" + item.Key + "級", Value = (item.Key).ToString(), Selected = (item.Key).ToString() == defaultSelectValue });
            }

            return ReadLEVELItems;
        }

        /// <summary>
        /// 取得「護照」等級圖示 for Dictionary
        /// </summary>
        /// <param name="MyA04"></param>
        /// <returns></returns>
        public static Dictionary<byte, string> GetImgPassportLEVEL(List<short> User_LisTBookPassport)
        {
            Dictionary<byte, string> DictionaryImg = new Dictionary<byte, string>();
            DictionaryImg = SetSECImgPassportLEVEL();

            Dictionary<byte, string> ReturnValue = new Dictionary<byte, string>();

            foreach (var item in DictionaryImg)
            {
                if (User_LisTBookPassport.Contains(item.Key))
                {
                    ReturnValue.Add(item.Key, item.Value);
                }
            }

            return ReturnValue;
        }

        /// <summary>
        /// 取得護照等級單一圖示
        /// </summary>
        /// <param name="ReadLEVEL"></param>
        /// <returns></returns>
        public static string GetImgPassportUrl(byte? Passport)
        {
            if (Passport == null) return null;

            Dictionary<byte, string> DictionaryImg = SetSECImgPassportLEVEL();

            if (DictionaryImg.ContainsKey((byte)Passport))
            {
                return DictionaryImg[(byte)Passport];
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// 我的秘書 取得「護照」等級圖示 for Dictionary
        /// </summary>
        /// <param name="MyA04"></param>
        /// <returns></returns>
        public static Dictionary<byte, string> GetSECImgPassportLEVEL(List<short> User_LisTBookPassport)
        {
            Dictionary<byte, string> DictionaryImg = SetSECImgPassportLEVEL();
            Dictionary<byte, string> HiedImg = SetSECHideImgPassportLEVEL();

            Dictionary<byte, string> ReturnValue = new Dictionary<byte, string>();

            foreach (var item in DictionaryImg)
            {
                if (User_LisTBookPassport.Contains(item.Key))
                {
                    ReturnValue.Add(item.Key, item.Value);
                }
                else
                {
                    ReturnValue.Add(item.Key, HiedImg[item.Key]);
                }
            }

            return ReturnValue;
        }

        public static UserProfile Get(string BRE_NO, string ActionResult_NAME)
        {
            return (UserProfile)HttpContext.Current.Session[CONSTANT.SESSION_USER_KEY];
        }

        public static void SetCookieData(string SchoolNo = "", string ROLE_ID = "")
        {
            if (HttpContext.Current.Request.Cookies[CONSTANT.COOKIES_INFO_KEY] == null)
            {
                HttpCookie CookieSchool = new HttpCookie(CONSTANT.COOKIES_INFO_KEY);
                DateTime dt = DateTime.Now;                                                     //定義時間對象
                TimeSpan ts = new TimeSpan(3, 0, 0, 0);                                         //cookie有效作用時間，具體查msdn
                CookieSchool.Expires = dt.Add(ts);                                              //增加作用時間
                CookieSchool.Values.Add(CONSTANT.COOKIES_SCHOOL_KEY, SchoolNo);                 //增加屬性
                CookieSchool.Values.Add(CONSTANT.COOKIES_ROLE_ID_KEY, ROLE_ID);
                HttpContext.Current.Response.AppendCookie(CookieSchool);                        //確定寫入cookie中
            }
            else
            {
                HttpContext.Current.Response.Cookies[CONSTANT.COOKIES_INFO_KEY][CONSTANT.COOKIES_SCHOOL_KEY] = SchoolNo;
                HttpContext.Current.Response.Cookies[CONSTANT.COOKIES_INFO_KEY].Expires = DateTime.Now.AddDays(1);
            }
        }
        public static void SetPointCookieData(string str) {

            HttpContext.Current.Session[CONSTANT.COOKIES_Point_NO_KEY] = str;
            if (HttpContext.Current.Request.Cookies[CONSTANT.COOKIES_Point_NO_KEY] == null)
            {
                HttpCookie CookiePointNO = new HttpCookie(CONSTANT.COOKIES_Point_NO_KEY);
                DateTime dt = DateTime.Now;
                TimeSpan ts = new TimeSpan(3, 0, 0, 0);
                CookiePointNO.Expires = dt.Add(ts);
                CookiePointNO.Values.Add(CONSTANT.COOKIES_Point_NO_KEY, str);
                HttpContext.Current.Response.AppendCookie(CookiePointNO);
            }
            else
            {
                HttpContext.Current.Response.Cookies[CONSTANT.COOKIES_SSO_NO_INFO_KEY][CONSTANT.COOKIES_SSO_NO_KEY] = str;
                HttpContext.Current.Response.Cookies[CONSTANT.COOKIES_Point_NO_INFO_KEY].Expires = DateTime.Now.AddHours(12);




            }
        }
        public static void SetATMNoCookieData(string ACESSNo)
        {
            HttpContext.Current.Session[CONSTANT.COOKIES_ATM_NO_KEY] = ACESSNo;
            if (HttpContext.Current.Request.Cookies[CONSTANT.COOKIES_ATM_NO_KEY] == null)
            {
                HttpCookie CookieATMNo = new HttpCookie(CONSTANT.COOKIES_ATM_NO_INFO_KEY);
                DateTime dt = DateTime.Now;                                                     //定義時間對象
                TimeSpan ts = new TimeSpan(3, 0, 0, 0);                                        //cookie有效作用時間，具體查msdn
                CookieATMNo.Expires = dt.Add(ts);                                              //增加作用時間
                CookieATMNo.Values.Add(CONSTANT.COOKIES_ATM_NO_KEY, ACESSNo);
                HttpContext.Current.Response.AppendCookie(CookieATMNo);
            }
            else
            {
                HttpContext.Current.Response.Cookies[CONSTANT.COOKIES_ATM_NO_INFO_KEY][CONSTANT.COOKIES_ATM_NO_KEY] = ACESSNo;

                HttpContext.Current.Response.Cookies[CONSTANT.COOKIES_ATM_NO_INFO_KEY].Expires = DateTime.Now.AddHours(12);
            }
        }
       public static void SetACESSNoCookieData(string ACESSNo)
        {
            HttpContext.Current.Session[CONSTANT.COOKIES_ACESS_NO_KEY] = ACESSNo;


            if (HttpContext.Current.Request.Cookies[CONSTANT.COOKIES_ACESS_NO_KEY] == null)
            {
                HttpCookie CookieACESSNo = new HttpCookie(CONSTANT.COOKIES_ACESS_NO_INFO_KEY);
                DateTime dt = DateTime.Now;                                                     //定義時間對象
                TimeSpan ts = new TimeSpan(3, 0, 0, 0);                                        //cookie有效作用時間，具體查msdn
                CookieACESSNo.Expires = dt.Add(ts);                                              //增加作用時間
                CookieACESSNo.Values.Add(CONSTANT.COOKIES_ACESS_NO_KEY, ACESSNo);                  //增加屬性

                HttpContext.Current.Response.AppendCookie(CookieACESSNo);                        //確定寫入cookie中
            }
            else
            {
                HttpContext.Current.Response.Cookies[CONSTANT.COOKIES_ACESS_NO_INFO_KEY][CONSTANT.COOKIES_ACESS_NO_KEY] = ACESSNo;

                HttpContext.Current.Response.Cookies[CONSTANT.COOKIES_ACESS_NO_INFO_KEY].Expires = DateTime.Now.AddHours(12);
            }
        }
        public static void SetSSONoCookieData(string SSONo)
        {
            HttpContext.Current.Session[CONSTANT.COOKIES_SSO_NO_KEY] = SSONo;
        

            if (HttpContext.Current.Request.Cookies[CONSTANT.COOKIES_SSO_NO_INFO_KEY] == null)
            {
                HttpCookie CookieSSONo = new HttpCookie(CONSTANT.COOKIES_SSO_NO_INFO_KEY);
                DateTime dt = DateTime.Now;                                                     //定義時間對象
                TimeSpan ts = new TimeSpan(3, 0, 0, 0);                                        //cookie有效作用時間，具體查msdn
                CookieSSONo.Expires = dt.Add(ts);                                              //增加作用時間
                CookieSSONo.Values.Add(CONSTANT.COOKIES_SSO_NO_KEY, SSONo);                  //增加屬性
                
                HttpContext.Current.Response.AppendCookie(CookieSSONo);                        //確定寫入cookie中
            }
            else
            {
                HttpContext.Current.Response.Cookies[CONSTANT.COOKIES_SSO_NO_INFO_KEY][CONSTANT.COOKIES_SSO_NO_KEY] = SSONo;
             
                HttpContext.Current.Response.Cookies[CONSTANT.COOKIES_SSO_NO_INFO_KEY].Expires = DateTime.Now.AddHours(12);
            }
        }
        public static void SetGameNoCookieData(string GameNo = "", bool IsBtnGoHide = true)
        {
            HttpContext.Current.Session[CONSTANT.COOKIES_GAME_NO_KEY] = GameNo;
            HttpContext.Current.Session[CONSTANT.COOKIES_Game_Btn_GO_KEY] = IsBtnGoHide;

            if (HttpContext.Current.Request.Cookies[CONSTANT.COOKIES_GAME_NO_INFO_KEY] == null)
            {
                HttpCookie CookieGameNo = new HttpCookie(CONSTANT.COOKIES_GAME_NO_INFO_KEY);
                DateTime dt = DateTime.Now;                                                     //定義時間對象
                TimeSpan ts = new TimeSpan(3, 0, 0, 0);                                        //cookie有效作用時間，具體查msdn
                CookieGameNo.Expires = dt.Add(ts);                                              //增加作用時間
                CookieGameNo.Values.Add(CONSTANT.COOKIES_GAME_NO_KEY, GameNo);                  //增加屬性
                CookieGameNo.Values.Add(CONSTANT.COOKIES_Game_Btn_GO_KEY, IsBtnGoHide.ToString());
                HttpContext.Current.Response.AppendCookie(CookieGameNo);                        //確定寫入cookie中
            }
            else
            {
                HttpContext.Current.Response.Cookies[CONSTANT.COOKIES_GAME_NO_INFO_KEY][CONSTANT.COOKIES_GAME_NO_KEY] = GameNo;
                HttpContext.Current.Response.Cookies[CONSTANT.COOKIES_GAME_NO_INFO_KEY][CONSTANT.COOKIES_Game_Btn_GO_KEY] = IsBtnGoHide.ToString();
                HttpContext.Current.Response.Cookies[CONSTANT.COOKIES_GAME_NO_INFO_KEY].Expires = DateTime.Now.AddHours(12);
            }
        }

        public static string GetGameNoCookie()
        {
            if (HttpContext.Current.Session[CONSTANT.COOKIES_GAME_NO_KEY] != null)
            {
                return (string)HttpContext.Current.Session[CONSTANT.COOKIES_GAME_NO_KEY];
            }

            if (HttpContext.Current.Request.Cookies[CONSTANT.COOKIES_GAME_NO_INFO_KEY] != null)
            {
                return (string)HttpContext.Current.Request.Cookies[CONSTANT.COOKIES_GAME_NO_INFO_KEY].Values[CONSTANT.COOKIES_GAME_NO_KEY];
            }
            else
            {
                return null;
            }
        }

        public static void ClearGameNoCookie()
        {
            HttpCookie CookieGame = new HttpCookie(CONSTANT.COOKIES_GAME_NO_KEY);

            if (CookieGame != null)
            {
                CookieGame.HttpOnly = true;
                CookieGame.Expires = DateTime.Now.AddYears(-1);
                HttpContext.Current.Response.SetCookie(CookieGame);
            }
        }

        public static void SetLevelNoCookieData(string LEVEL_NO = "")
        {
            HttpContext.Current.Session[CONSTANT.COOKIES_LEVEL_NO_KEY] = LEVEL_NO;

            if (HttpContext.Current.Request.Cookies[CONSTANT.COOKIES_LEVEL_NO_INFO_KEY] == null)
            {
                HttpCookie CookieLevelNo = new HttpCookie(CONSTANT.COOKIES_LEVEL_NO_INFO_KEY);
                DateTime dt = DateTime.Now;                                                     //定義時間對象
                TimeSpan ts = new TimeSpan(3, 0, 0, 0);                                        //cookie有效作用時間，具體查msdn
                CookieLevelNo.Expires = dt.Add(ts);       
                //增加作用時間
                CookieLevelNo.Values.Add(CONSTANT.COOKIES_LEVEL_NO_KEY, LEVEL_NO);                  //增加屬性
                HttpContext.Current.Response.AppendCookie(CookieLevelNo);                        //確定寫入cookie中
            }
            else
            {
                HttpContext.Current.Response.Cookies[CONSTANT.COOKIES_LEVEL_NO_INFO_KEY][CONSTANT.COOKIES_LEVEL_NO_KEY] = LEVEL_NO;
                HttpContext.Current.Response.Cookies[CONSTANT.COOKIES_LEVEL_NO_INFO_KEY].Expires = DateTime.Now.AddHours(12);
            }
        }

        public static string GetLevelNoCookie()
        {
            if (HttpContext.Current.Session[CONSTANT.COOKIES_LEVEL_NO_KEY] != null)
            {
                return (string)HttpContext.Current.Session[CONSTANT.COOKIES_LEVEL_NO_KEY];
            }

            if (HttpContext.Current.Request.Cookies[CONSTANT.COOKIES_LEVEL_NO_KEY] != null)
            {
                return (string)HttpContext.Current.Request.Cookies[CONSTANT.COOKIES_LEVEL_NO_KEY].Values[CONSTANT.COOKIES_LEVEL_NO_KEY];
            }
            else
            {
                return null;
            }
        }
       
        public static bool GetGameIsBtnGoHideCookie()
        {
            if (HttpContext.Current.Session[CONSTANT.COOKIES_Game_Btn_GO_KEY] != null)
            {
                bool IsBtnGoHide = true;

                Boolean.TryParse((HttpContext.Current.Session[CONSTANT.COOKIES_Game_Btn_GO_KEY] ?? true).ToString(), out IsBtnGoHide);

                return IsBtnGoHide;
            }

            if (HttpContext.Current.Request.Cookies[CONSTANT.COOKIES_GAME_NO_INFO_KEY] != null)
            {
                bool IsBtnGoHide = true;

                Boolean.TryParse(HttpContext.Current.Request.Cookies[CONSTANT.COOKIES_GAME_NO_INFO_KEY].Values[CONSTANT.COOKIES_Game_Btn_GO_KEY], out IsBtnGoHide);

                return IsBtnGoHide;
            }
            else
            {
                return true;
            }
        }

        public static void SetLayoutCookieData(string Layout = "")
        {
            HttpContext.Current.Session[CONSTANT.COOKIES_Layout_KEY] = Layout;

            if (HttpContext.Current.Request.Cookies[CONSTANT.COOKIES_Layout_INFO_KEY] == null)
            {
                HttpCookie CookieStr = new HttpCookie(CONSTANT.COOKIES_Layout_INFO_KEY);
                DateTime dt = DateTime.Now;                                                     //定義時間對象
                TimeSpan ts = new TimeSpan(3, 0, 0, 0);                                         //cookie有效作用時間，具體查msdn
                CookieStr.Expires = dt.Add(ts);                                              //增加作用時間
                CookieStr.Values.Add(CONSTANT.COOKIES_Layout_KEY, Layout);                 //增加屬性
                HttpContext.Current.Response.AppendCookie(CookieStr);                        //確定寫入cookie中
            }
            else
            {
                HttpContext.Current.Response.Cookies[CONSTANT.COOKIES_Layout_INFO_KEY][CONSTANT.COOKIES_Layout_KEY] = Layout;
                HttpContext.Current.Response.Cookies[CONSTANT.COOKIES_Layout_INFO_KEY].Expires = DateTime.Now.AddHours(12);
            }
        }

        public static string GetLayoutCookie()
        {
            if (HttpContext.Current.Session[CONSTANT.COOKIES_Layout_KEY] != null)
            {
                return (string)HttpContext.Current.Session[CONSTANT.COOKIES_Layout_KEY];
            }

            if (HttpContext.Current.Request.Cookies[CONSTANT.COOKIES_Layout_INFO_KEY] != null)
            {
                return (string)HttpContext.Current.Request.Cookies[CONSTANT.COOKIES_Layout_INFO_KEY].Values[CONSTANT.COOKIES_Layout_KEY];
            }
            else
            {
                return "";
            }
        }

        public static void SetUuidCookieData(string CheckId, string UUID, bool IsEncrypt)
        {
            if (string.IsNullOrWhiteSpace(CheckId) == false && string.IsNullOrWhiteSpace(UUID) == false)
            {
                string ECheckId = (IsEncrypt) ? AppHelper.GetEncryptCheckId(CheckId, UUID) : CheckId;

                if (HttpContext.Current.Request.Cookies[CONSTANT.COOKIES_APP_INFO_KEY] == null)
                {
                    HttpCookie CookieApp = new HttpCookie(CONSTANT.COOKIES_APP_INFO_KEY);
                    DateTime dt = DateTime.Now;                                                     //定義時間對象
                    TimeSpan ts = new TimeSpan(1, 0, 0, 0);                                         //cookie有效作用時間，具體查msdn
                    CookieApp.Expires = dt.Add(ts);
                    CookieApp.Values.Add(CONSTANT.COOKIES_CHID_KEY, ECheckId);                 //增加屬性
                    CookieApp.Values.Add(CONSTANT.COOKIES_UUID_KEY, UUID);
                    HttpContext.Current.Response.AppendCookie(CookieApp);                        //確定寫入cookie中
                }
                else
                {
                    HttpContext.Current.Response.Cookies[CONSTANT.COOKIES_APP_INFO_KEY][CONSTANT.COOKIES_CHID_KEY] = ECheckId;
                    HttpContext.Current.Response.Cookies[CONSTANT.COOKIES_APP_INFO_KEY][CONSTANT.COOKIES_UUID_KEY] = UUID;
                    HttpContext.Current.Response.Cookies[CONSTANT.COOKIES_APP_INFO_KEY].Expires = DateTime.Now.AddDays(1);
                }
            }
        }

        public static string GetCheckIdCookie()
        {
            if (HttpContext.Current.Request.Cookies[CONSTANT.COOKIES_APP_INFO_KEY] != null)
            {
                return (string)HttpContext.Current.Request.Cookies[CONSTANT.COOKIES_APP_INFO_KEY].Values[CONSTANT.COOKIES_CHID_KEY]; ;
            }
            else
            {
                return null;
            }
        }

        public static string GetUUIDCookie()
        {
            if (HttpContext.Current.Request.Cookies[CONSTANT.COOKIES_APP_INFO_KEY] != null)
            {
                return (string)HttpContext.Current.Request.Cookies[CONSTANT.COOKIES_APP_INFO_KEY].Values[CONSTANT.COOKIES_UUID_KEY];
            }
            else
            {
                return null;
            }
        }

        public static string GetSchoolNoCookie()
        {
            if (HttpContext.Current.Request.Cookies[CONSTANT.COOKIES_INFO_KEY] != null)
            {
                return (string)HttpContext.Current.Request.Cookies[CONSTANT.COOKIES_INFO_KEY].Values[CONSTANT.COOKIES_SCHOOL_KEY];
            }
            else
            {
                return null;
            }
        }
        public static string GetSSONoCookie()
        {

            if (HttpContext.Current.Session[CONSTANT.COOKIES_SSO_NO_KEY] != null)
            {
                return (string)HttpContext.Current.Session[CONSTANT.COOKIES_SSO_NO_KEY];
            }
            if (HttpContext.Current.Request.Cookies[CONSTANT.COOKIES_SSO_NO_INFO_KEY] != null && HttpContext.Current.Request.Cookies[CONSTANT.COOKIES_SSO_NO_INFO_KEY].Values[CONSTANT.COOKIES_SSO_NO_KEY] != null)
            {
                logger.Info("SSO getCOokie 不適空的");

                return (string)HttpContext.Current.Request.Cookies[CONSTANT.COOKIES_SSO_NO_INFO_KEY].Values[CONSTANT.COOKIES_SSO_NO_KEY];
            }
            else
            {
                return null;
            }
        }
        public static string GetPointNoCookie() {

            if (HttpContext.Current.Session[CONSTANT.COOKIES_Point_NO_KEY] != null)
            {
                return (string)HttpContext.Current.Session[CONSTANT.COOKIES_Point_NO_KEY];
            }
            if (HttpContext.Current.Request.Cookies[CONSTANT.COOKIES_Point_NO_INFO_KEY] != null && HttpContext.Current.Request.Cookies[CONSTANT.COOKIES_Point_NO_INFO_KEY].Values[CONSTANT.COOKIES_Point_NO_KEY] != null)
            {
                logger.Info("Point_NO_ 不適空的");

                return (string)HttpContext.Current.Request.Cookies[CONSTANT.COOKIES_Point_NO_INFO_KEY].Values[CONSTANT.COOKIES_Point_NO_KEY];
            }
            else
            {
                return null;
            }
        }
         public static string  GetATMNoCookie()
        {

            if (HttpContext.Current.Session[CONSTANT.COOKIES_ATM_NO_KEY] != null)
            {
                return (string)HttpContext.Current.Session[CONSTANT.COOKIES_ATM_NO_KEY];
            }
            if (HttpContext.Current.Request.Cookies[CONSTANT.COOKIES_ATM_NO_INFO_KEY] != null &&HttpContext.Current.Request.Cookies[CONSTANT.COOKIES_ATM_NO_INFO_KEY].Values[CONSTANT.COOKIES_ATM_NO_KEY] != null)
            {
                logger.Info("ATM getCOokie 不適空的");

                return (string)HttpContext.Current.Request.Cookies[CONSTANT.COOKIES_ATM_NO_INFO_KEY].Values[CONSTANT.COOKIES_ATM_NO_KEY];
            }
            else
            {
                return null;
            }
        }

        public static string GetACESSNoCookie()
        {

            if (HttpContext.Current.Session[CONSTANT.COOKIES_ACESS_NO_KEY] != null)
            {
                return (string)HttpContext.Current.Session[CONSTANT.COOKIES_ACESS_NO_KEY];
            }
            if (HttpContext.Current.Request.Cookies[CONSTANT.COOKIES_ACESS_NO_INFO_KEY] != null &&HttpContext.Current.Request.Cookies[CONSTANT.COOKIES_ACESS_NO_INFO_KEY].Values[CONSTANT.COOKIES_ACESS_NO_KEY] != null)
            {
                logger.Info("SSO getCOokie 不適空的");

                return (string)HttpContext.Current.Request.Cookies[CONSTANT.COOKIES_ACESS_NO_INFO_KEY].Values[CONSTANT.COOKIES_ACESS_NO_KEY];
            }
            else
            {
                return null;
            }
        }
        public static string GetROLE_IDCookie()
        {
            if (HttpContext.Current.Request.Cookies[CONSTANT.COOKIES_INFO_KEY] != null)
            {
                return (string)HttpContext.Current.Request.Cookies[CONSTANT.COOKIES_INFO_KEY].Values[CONSTANT.COOKIES_ROLE_ID_KEY];
            }
            else
            {
                return null;
            }
        }

        public static void ClearCookie()
        {
            HttpCookie CookieSchool = new HttpCookie(CONSTANT.COOKIES_INFO_KEY);
            if (CookieSchool != null)
            {
                CookieSchool.HttpOnly = true;
                CookieSchool.Expires = DateTime.Now.AddYears(-1);
                HttpContext.Current.Response.SetCookie(CookieSchool);
            }
        }

        /// <summary>
        /// 取得觀看角度
        /// </summary>
        /// <param name="SCHOOL_NO"></param>
        /// <param name="USER_NO"></param>
        /// <returns></returns>
        public static string GetDataAngleType(string SCHOOL_NO, string USER_NO)
        {
            using (ECOOL_DEVEntities db = new ECOOL_DEVEntities())
            {
                HRMT01 thisUser = db.HRMT01.Where(a => a.SCHOOL_NO == SCHOOL_NO && a.USER_NO == USER_NO).FirstOrDefault();

                if (thisUser == null) return null;

                if (thisUser.USER_TYPE == UserType.Student || thisUser.USER_TYPE == UserType.Parents)
                {
                    return AngleVal.OneData;
                }
                else
                {
                    HRMT03 aHRMT03 = db.HRMT03.Where(a => a.SCHOOL_NO == thisUser.SCHOOL_NO && a.TEACHER_NO == thisUser.USER_NO).FirstOrDefault();

                    if (aHRMT03 != null)
                    {
                        return AngleVal.ClassData;
                    }
                    else
                    {
                        return AngleVal.SchoolData;
                    }
                }
            }
        }

        /// <summary>
        /// 取得觀看角度
        /// </summary>
        /// <param name="user"></param>
        /// <returns></returns>
        public static string GetDataAngleType(UserProfile user)
        {
            if (user.USER_TYPE == UserType.Student)
            {
                return AngleVal.OneData;
            }
            else if (user.USER_TYPE == UserType.Parents)
            {
                return AngleVal.MyChildData;
            }
            else
            {
                if (user.TEACH_CLASS_NO != null)
                {
                    return AngleVal.ClassData;
                }
                else
                {
                    if (HRMT24_ENUM.QOutSchoolList.Contains((byte)user.ROLE_TYPE))
                    {
                        return AngleVal.AllSchoolData;
                    }
                    else
                    {
                        return AngleVal.SchoolData;
                    }
                }
            }
        }
        /// <summary>
        /// 檢查是否能看到這個學生資料
        /// </summary>
        /// <param name="user"></param>
        /// <param name="SeeSchool_No"></param>
        /// <param name="SeeUser_No"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        public static bool CheckSeeStudentIDNOData(UserProfile user, string SeeSchool_No, string SeeUser_No, string IDNO, ref ECOOL_DEVEntities db)
        {
            if (string.IsNullOrWhiteSpace(SeeSchool_No))
            {
                SeeSchool_No = string.Empty;
            }

            if (string.IsNullOrWhiteSpace(SeeUser_No))
            {
                SeeUser_No = string.Empty;
            }

            if (user == null)
            {
                return false;
            }
            else if (user.USER_TYPE == UserType.Student || user.USER_TYPE == UserType.Parents)
            {
                string IDNOstr = "";
                IDNOstr = db.HRMT01.Where(x => x.USER_NO == user.USER_NO && x.SCHOOL_NO == user.SCHOOL_NO).Select(x => x.IDNO).FirstOrDefault();
                if (user.SCHOOL_NO == SeeSchool_No && user.USER_NO == SeeUser_No)

                    return true;
                else if (IDNOstr == IDNO)
                    return true;
                else return false;
            }
            else if (user.USER_TYPE == UserType.Teacher)
            {
                //if (user.SCHOOL_NO == SeeSchool_No)
                //{
                var OK = CheckROLE_SCHOOL_ADMIN(user, db);

                if (OK)
                {
                    return true;
                }
                //}

                string TeachCLASS_NO = UserProfile.GetTeachCLASS_NO(user, ref db);
                string SeeCLASS_NO = UserProfile.GetStudentCLASS_NO(SeeSchool_No, SeeUser_No, ref db);
              
                if (TeachCLASS_NO == SeeCLASS_NO) return true;
                else {
                    Regex rx = new Regex(@"_");
                    if (SeeSchool_No != null && rx.Matches(SeeSchool_No).Count > 0)
                    {
                        string[] strSchool = new string[2];
                        strSchool = SeeSchool_No.Split('_');
                        SeeSchool_No = strSchool[0];

                    }
                    string IDNONow =
                    db.HRMT01.Where(x => x.SCHOOL_NO == user.SCHOOL_NO && x.USER_NO == SeeUser_No).Select(x => x.IDNO).FirstOrDefault();
                    string CLASSNONow = db.HRMT01.Where(x => x.SCHOOL_NO == user.SCHOOL_NO && x.IDNO == IDNONow).Select(x => x.CLASS_NO).FirstOrDefault();

                    if (TeachCLASS_NO == CLASSNONow)
                    {
                        return true;

                    }
                    else { return false; }
                }

                   
            }
            else if (user.USER_TYPE == UserType.Admin)
            {
                return true;
            }
            else
            {
                if (user.SCHOOL_NO == SeeSchool_No) return true;
                else return false;
            }
        }

        /// <summary>
        /// 檢查是否能看到這個學生資料
        /// </summary>
        /// <param name="user"></param>
        /// <param name="SeeSchool_No"></param>
        /// <param name="SeeUser_No"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        public static bool CheckSeeStudentData(UserProfile user, string SeeSchool_No, string SeeUser_No, ref ECOOL_DEVEntities db)
        {
            if (string.IsNullOrWhiteSpace(SeeSchool_No))
            {
                SeeSchool_No = string.Empty;
            }

            if (string.IsNullOrWhiteSpace(SeeUser_No))
            {
                SeeUser_No = string.Empty;
            }

            if (user == null)
            {
                return false;
            }
            else if (user.USER_TYPE == UserType.Student || user.USER_TYPE == UserType.Parents)
            {
              
                if (user.SCHOOL_NO == SeeSchool_No && user.USER_NO == SeeUser_No)

                    return true;
            
                else return false;
            }
            else if (user.USER_TYPE == UserType.Teacher)
            {
                //if (user.SCHOOL_NO == SeeSchool_No)
                //{
                    var OK = CheckROLE_SCHOOL_ADMIN(user, db);

                    if (OK)
                    {
                        return true;
                    }
                //}

                string TeachCLASS_NO = UserProfile.GetTeachCLASS_NO(user, ref db);
                string SeeCLASS_NO = UserProfile.GetStudentCLASS_NO(SeeSchool_No, SeeUser_No, ref db);

                if (TeachCLASS_NO == SeeCLASS_NO) return true;
                else
                {
                    Regex rx = new Regex(@"_");
                    if (SeeSchool_No != null && rx.Matches(SeeSchool_No).Count > 0)
                    {
                        string[] strSchool = new string[2];
                        strSchool = SeeSchool_No.Split('_');
                        SeeSchool_No = strSchool[0];

                    }
                    string IDNONow =
                    db.HRMT01.Where(x => x.SCHOOL_NO == SeeSchool_No && x.USER_NO == SeeUser_No).Select(x => x.IDNO).FirstOrDefault();
                    string CLASSNONow = db.HRMT01.Where(x => x.SCHOOL_NO == user.SCHOOL_NO && x.IDNO == IDNONow).Select(x => x.CLASS_NO).FirstOrDefault();

                    if (TeachCLASS_NO == CLASSNONow)
                    {
                        return true;

                    }
                    else { return false; }






                }


                 
            }
            else if (user.USER_TYPE == UserType.Admin)
            {
                return true;
            }
            else
            {
                if (user.SCHOOL_NO == SeeSchool_No) return true;
                else return false;
            }
        }

        //判斷權限是否大於各校管理者
        public static bool CheckROLE_SCHOOL_ADMIN(UserProfile user, ECOOL_DEVEntities db)
        {
            var dbNull = false;

            if (user == null)
            {
                return false;
            }

            if (user.USER_TYPE == UserType.Admin)
            {
                return true;
            }

            if (db == null)
            {
                db = new ECOOL_DEVEntities();
                dbNull = true;
            }

            var hr = (from it in db.HRMT25
                      where it.SCHOOL_NO == user.SCHOOL_NO && it.USER_NO == user.USER_NO
                      select it).ToList().OrderBy(o => decimal.Parse(o.ROLE_ID)).FirstOrDefault();

            if (dbNull)
            {
                db.Dispose();
            }

            if (hr != null)
            {
                if (decimal.Parse(hr.ROLE_ID) <= decimal.Parse(HRMT24_ENUM.ROLE_SCHOOL_ADMIN))
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 閱讀認證等級 圖片設定 (舊)
        /// </summary>
        /// <returns></returns>
        private static Dictionary<byte, string> SetImgReadLEVEL()
        {
            Dictionary<byte, string> ImageLinkList = new Dictionary<byte, string>();
            ImageLinkList.Add(1, "~/Content/img/web-student-allpage-27.png");
            ImageLinkList.Add(2, "~/Content/img/web-student-allpage-29.png");
            ImageLinkList.Add(3, "~/Content/img/web-student-allpage-30.png");
            ImageLinkList.Add(4, "~/Content/img/web-student-allpage-31.png");
            ImageLinkList.Add(5, "~/Content/img/web-student-allpage-32.png");
            ImageLinkList.Add(6, "~/Content/img/web-student-allpage-28.png");
            ImageLinkList.Add(7, "~/Content/img/web-student-allpage-33.png");
            ImageLinkList.Add(8, "~/Content/img/web-student-allpage-34.png");
            ImageLinkList.Add(9, "~/Content/img/web-student-allpage-35.png");
            ImageLinkList.Add(10, "~/Content/img/web-student-allpage-36.png");

            return ImageLinkList;
        }

        /// <summary>
        /// 我的秘書 閱讀認證等級 圖片設定
        /// </summary>
        /// <returns></returns>
        private static Dictionary<byte, string> SetSECImgReadLEVEL()
        {
            Dictionary<byte, string> ImageLinkList = new Dictionary<byte, string>();
            ImageLinkList.Add(1, "~/Content/img/web-revise-secretary-13.png");
            ImageLinkList.Add(2, "~/Content/img/web-revise-secretary-14.png");
            ImageLinkList.Add(3, "~/Content/img/web-revise-secretary-15.png");
            ImageLinkList.Add(4, "~/Content/img/web-revise-secretary-16.png");
            ImageLinkList.Add(5, "~/Content/img/web-revise-secretary-17.png");
            ImageLinkList.Add(6, "~/Content/img/web-revise-secretary-18.png");
            ImageLinkList.Add(7, "~/Content/img/web-revise-secretary-19.png");
            ImageLinkList.Add(8, "~/Content/img/web-revise-secretary-20.png");
            ImageLinkList.Add(9, "~/Content/img/web-revise-secretary-21.png");
            ImageLinkList.Add(10, "~/Content/img/web-revise-secretary-22.png");
            return ImageLinkList;
        }

        /// <summary>
        /// 我的秘書 閱讀認證等級 圖片設定 透明度 50%
        /// </summary>
        /// <returns></returns>
        private static Dictionary<byte, string> SetSECHiedImgReadLEVEL()
        {
            Dictionary<byte, string> ImageLinkList = new Dictionary<byte, string>();
            ImageLinkList.Add(1, "~/Content/img/web-revise-secretary-23.png");
            ImageLinkList.Add(2, "~/Content/img/web-revise-secretary-24.png");
            ImageLinkList.Add(3, "~/Content/img/web-revise-secretary-25.png");
            ImageLinkList.Add(4, "~/Content/img/web-revise-secretary-26.png");
            ImageLinkList.Add(5, "~/Content/img/web-revise-secretary-27.png");
            ImageLinkList.Add(6, "~/Content/img/web-revise-secretary-28.png");
            ImageLinkList.Add(7, "~/Content/img/web-revise-secretary-29.png");
            ImageLinkList.Add(8, "~/Content/img/web-revise-secretary-30.png");
            ImageLinkList.Add(9, "~/Content/img/web-revise-secretary-31.png");
            ImageLinkList.Add(10, "~/Content/img/web-revise-secretary-32.png");

            return ImageLinkList;
        }

        /// <summary>
        /// 「護照」等級 圖片設定  (舊)
        /// </summary>
        /// <returns></returns>
        private static Dictionary<byte, string> SetImgPassportLEVEL()
        {
            Dictionary<byte, string> ADDT04ImageLinkList = new Dictionary<byte, string>();

            ADDT04ImageLinkList.Add(1, "~/Content/img/web-student-allpage-37.png");
            ADDT04ImageLinkList.Add(2, "~/Content/img/web-student-allpage-38.png");
            ADDT04ImageLinkList.Add(3, "~/Content/img/web-student-allpage-39.png");
            ADDT04ImageLinkList.Add(4, "~/Content/img/web-student-allpage-40.png");
            ADDT04ImageLinkList.Add(5, "~/Content/img/web-student-allpage-41.png");
            ADDT04ImageLinkList.Add(6, "~/Content/img/web-student-allpage-42.png");

            return ADDT04ImageLinkList;
        }

        /// <summary>
        /// 我的秘書 「護照」等級 圖片設定
        /// </summary>
        /// <returns></returns>
        private static Dictionary<byte, string> SetSECImgPassportLEVEL()
        {
            Dictionary<byte, string> ADDT04ImageLinkList = new Dictionary<byte, string>();

            ADDT04ImageLinkList.Add(1, "~/Content/img/web-revise-secretary-33.png");
            ADDT04ImageLinkList.Add(2, "~/Content/img/web-revise-secretary-34.png");
            ADDT04ImageLinkList.Add(3, "~/Content/img/web-revise-secretary-35.png");
            ADDT04ImageLinkList.Add(4, "~/Content/img/web-revise-secretary-36.png");
            ADDT04ImageLinkList.Add(5, "~/Content/img/web-revise-secretary-37.png");
            ADDT04ImageLinkList.Add(6, "~/Content/img/web-revise-secretary-38.png");

            return ADDT04ImageLinkList;
        }

        /// <summary>
        /// 我的秘書 「護照」等級 圖片設定 透明度 50%
        /// </summary>
        /// <returns></returns>
        private static Dictionary<byte, string> SetSECHideImgPassportLEVEL()
        {
            Dictionary<byte, string> ADDT04ImageLinkList = new Dictionary<byte, string>();

            ADDT04ImageLinkList.Add(1, "~/Content/img/web-revise-secretary-39.png");
            ADDT04ImageLinkList.Add(2, "~/Content/img/web-revise-secretary-40.png");
            ADDT04ImageLinkList.Add(3, "~/Content/img/web-revise-secretary-41.png");
            ADDT04ImageLinkList.Add(4, "~/Content/img/web-revise-secretary-42.png");
            ADDT04ImageLinkList.Add(5, "~/Content/img/web-revise-secretary-43.png");
            ADDT04ImageLinkList.Add(6, "~/Content/img/web-revise-secretary-44.png");

            return ADDT04ImageLinkList;
        }

        /// <summary>
        /// 跑步地圖獎牌 Key: 里程數 (KM)
        /// </summary>
        /// <returns></returns>
        private static Dictionary<double, string> SetSECHideImgRunMedals()
        {
            Dictionary<double, string> ImageLinkList = new Dictionary<double, string>();

            ImageLinkList.Add(10, "~/Content/img/runMedals/runMedal0.png");
            ImageLinkList.Add(20, "~/Content/img/runMedals/runMedal1.png");
            ImageLinkList.Add(30, "~/Content/img/runMedals/runMedal2.png");
            ImageLinkList.Add(40, "~/Content/img/runMedals/runMedal3.png");
            ImageLinkList.Add(50, "~/Content/img/runMedals/runMedal4.png");
            ImageLinkList.Add(60, "~/Content/img/runMedals/runMedal5.png");
            ImageLinkList.Add(70, "~/Content/img/runMedals/runMedal6.png");
            ImageLinkList.Add(80, "~/Content/img/runMedals/runMedal7.png");
            ImageLinkList.Add(90, "~/Content/img/runMedals/runMedal8.png");
            ImageLinkList.Add(100, "~/Content/img/runMedals/runMedal9.png");
            ImageLinkList.Add(411.5, "~/Content/img/runMedals/runMedal10.png"); // 終點
            ImageLinkList.Add(int.MaxValue, "~/Content/img/runMedals/runMedal10.png");

            return ImageLinkList;
        }

        public static class AngleVal
        {
            /// <summary>
            /// 一個人的資料
            /// </summary>
            public static string OneData = "OneData";

            /// <summary>
            /// 我的小孩的資料
            /// </summary>
            public static string MyChildData = "MycChildData";

            /// <summary>
            /// 一個班級的資料
            /// </summary>
            public static string ClassData = "ClassData";

            /// <summary>
            /// 一個校的資料
            /// </summary>
            public static string SchoolData = "SchoolData";

            /// <summary>
            /// 全部學校的資料
            /// </summary>
            public static string AllSchoolData = "AllSchoolData";
        }
    }
}