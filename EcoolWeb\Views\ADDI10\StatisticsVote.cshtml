﻿@model ADDI10StatisticsVoteViewModel
@{
    ViewBag.Title = ViewBag.Panel_Title;
    int InputNum = 0;

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
    string backurl = Url.Action("Index", "ADDI10");
}
<link href="~/Content/colorbox/example4/colorbox.css" rel="stylesheet" />

<script src="~/Content/colorbox/jquery.colorbox.js"></script>
@*<script src="~/Scripts/printThis/printThis.js"></script>*@

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")
<button type="button" class="btn btn-sm btn-sys" onclick="onBack()" name="sub-btn">回投票系統列表</button>
<button type="button" class="btn btn-sm btn-sys" onclick="PrintBooK()">我要列印</button>
<a role="button" class="btn btn-sm btn-sys" href="@Url.Action("ExportStatisticsVoteExcel",new {QUESTIONNAIRE_ID=Model.Search.WhereQUESTIONNAIRE_ID })" target="_blank">匯出結果</a>
<button type="button" class="btn btn-sm btn-sys" onclick="ExportVote()">匯出一覽表</button>
@*<button type="button" class="btn btn-sm btn-sys" onclick="ExportVote1()">匯出一覽表1</button>*@
        @*@if (Model.VoteData.Where(x => x.IsIM > 0).Count() > 0) { <button type="button" class="btn btn-sm btn-sys" onclick="DownLoadJPG()">批次下載</button>
}*@

@using (Html.BeginForm("StatisticsVoteDetails", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "formEdit", name = "formEdit", enctype = "multipart/form-data", @AutoComplete = "Off" }))
{

    <div class="modal fade" id="myModal" role="dialog">
        <div class="modal-content">

            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">下載進度</h4>
            </div>
        </div>
        <div class="modal-body">
            <div class="progress progress-striped active">
                <div class="progress-bar progress-bar-success" id="barr" role="progressbar" aria-valuenow="60" aria-valuemin="0" aria-valuemax="100" style="width:0%;">

                    <span class="sr-only">40% 完成</span>
                </div>
            </div>
        </div>
        <div class="modal-footer">

            <button type="button" class="btn btn-default" data-dismiss="modal">Close </button>
        </div>
    </div>

    @Html.HiddenFor(m => m.Search.OrdercColumn)
    @Html.HiddenFor(m => m.Search.SyntaxName)
    @Html.HiddenFor(m => m.Search.Page)
    @Html.HiddenFor(m => m.Search.WhereSOU_KEY)
    @Html.HiddenFor(m => m.Search.WhereQUESTIONNAIRE_ID)
    @Html.HiddenFor(m => m.Search.BackAction)
    @Html.HiddenFor(m => m.Search.BackController)
    @Html.AntiForgeryToken()

    <img src="~/Content/img/web-bar-vote.png" class="img-responsive" alt="Responsive image" />
    <div class="Div-EZ-reader" id="tbData">
        <div class="form-horizontal">
            <div class="Details">
                <div class="dl-horizontal-EZ">

                    <samp class="dt">年級</samp>

                    <samp class="dd">
                        @Html.DropDownListFor(m => m.whereGrade, (IEnumerable<SelectListItem>)ViewBag.GradeItem, new { onchange = "Details_show()" })
                    </samp>

                    <samp class="dt">班級</samp>

                    <samp class="dd">
                        @Html.DropDownListFor(m => m.whereCLASS_NO, (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { onchange = "Details_show()" })
                    </samp>
                    <input type="button" class="btn-yellow btn btn-sm" id="searchMain" value="搜尋" onclick="Details_show()" />
                </div>  <div class="dl-horizontal-EZ">
                    <samp class="dt">
                        @Html.DisplayNameFor(m => m.Title.QUESTIONNAIRE_NAME)
                    </samp>
                    <samp class="dd">
                        @Html.DisplayFor(m => m.Title.QUESTIONNAIRE_NAME)
                    </samp>
                </div>
                <div class="dl-horizontal-EZ">
                    <samp class="dt">
                        @Html.DisplayNameFor(m => m.Title.QUESTIONNAIRE_SDATE)
                    </samp>
                    <samp class="dd">
                        @{
                            string STime = "";
                            STime = ((DateTime)Model.Title.QUESTIONNAIRE_SDATE).ToShortTimeString();

                        }
                        @Html.DisplayFor(m => m.Title.QUESTIONNAIRE_SDATE)    @STime
                    </samp>
                </div>
                <div class="dl-horizontal-EZ">
                    <samp class="dt">
                        @Html.DisplayNameFor(m => m.Title.QUESTIONNAIRE_EDATE)
                    </samp>
                    <samp class="dd">

                        @{
                            string ETime = "";
                            ETime = ((DateTime)Model.Title.QUESTIONNAIRE_EDATE).ToShortTimeString();

                        }



                        @Html.DisplayFor(m => m.Title.QUESTIONNAIRE_EDATE)    @ETime
                    </samp>
                </div>
                @if ((Model.Title.CASH ?? 0) > 0)
                {
                    <div class="dl-horizontal-EZ">
                        <samp class="dt">
                            @Html.DisplayNameFor(m => m.Title.CASH)
                        </samp>
                        <samp class="dd">
                            @Html.DisplayFor(m => m.Title.CASH)
                        </samp>
                    </div>
                }
                <div class="dl-horizontal-EZ">
                    <samp class="dt">
                        詳細活動內容
                    </samp>
                </div>
                <div style="height:15px"></div>
                <div class="p-context">
                    @Html.Raw(HttpUtility.HtmlDecode(Model.Title.QUESTIONNAIRE_DESC))
                </div>
                <div style="height:1px;background-color:#DDDDDD;width:99%"></div>
            </div>
            <div style="height:15px"></div>
            <div class="table-92Per" style="margin: 0px auto; ">

                @foreach (var item in Model.Topic)
                {
                    <div>
                        <strong class="label_dt_S">
                            Q@(InputNum + 1).
                            @Html.Raw(HttpUtility.HtmlDecode(item.Q_SUBJECT))
                        </strong>
                        <div style="height:15px"></div>
                        @foreach (var D_ITEM in Model.VoteData.Where(a => a.Q_NUM == item.Q_NUM))
                        {
                            if (!string.IsNullOrEmpty(D_ITEM.Q_VAL))
                            {
                                <label>@Html.Raw(HttpUtility.HtmlDecode(D_ITEM.Q_VAL))</label>
                                <br />
                            }

                            <div class="progress" href="@Url.Action("StatisticsVoteDetails", (string)ViewBag.BRE_NO,new {  QUESTIONNAIRE_ID= item.QUESTIONNAIRE_ID,  Q_NUM=item.Q_NUM,  ANSWER = D_ITEM.Q_VAL ,whereCLASS_NO=Model.whereCLASS_NO})">
                                <div class="progress-bar progress-bar-info" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"
                                     style="width: @(D_ITEM.RATE)%  ;min-width: 5em;">
                                    @(D_ITEM.RATE)% / @(D_ITEM.QTY)人
                                </div>
                            </div>
                        }
                    </div>

                    InputNum = InputNum + 1;
                }
                <div style="height:15px"></div>

            </div>
        </div>
    </div>

}

<script type="text/javascript">
        var targetFormID = '#formEdit';

    $(document).ready(function () {

            $("#searchMain").click(function () {
              $('#@Html.IdFor(m => m.Search.WhereQUESTIONNAIRE_ID)').val('@Model.Topic.FirstOrDefault().QUESTIONNAIRE_ID')
                $(targetFormID).attr("action", "@Url.Action("StatisticsVote", (string)ViewBag.BRE_NO)")

                $(targetFormID).submit();

            });
        $('[rel="tooltip"]').tooltip({
            animated: 'fade',
            placement: 'bottom',
        });
            $(".progress").colorbox({ opacity: 0.82, width: "70%", innerHeight: "500px", scrolling: true });

         });
         function Details_show() {
            $('#@Html.IdFor(m => m.Search.WhereQUESTIONNAIRE_ID)').val('@Model.Topic.FirstOrDefault().QUESTIONNAIRE_ID')
            $(targetFormID).attr("action", "@Url.Action("StatisticsVote", (string)ViewBag.BRE_NO)")
              $(targetFormID).submit();
    }
     function DownLoadJPG() {

         $("#myModal").modal('show');
         var id = setInterval(func, 10);
         var percent = 10;
         var percentW = "";
         percent = 10 * id;
         percentW = percent + "%";
         $("#barr").css("width", percentW);
        var postData = { "QUESTIONNAIRE_ID": "@Model.Title.QUESTIONNAIRE_ID","Q_NUM":""};

         var func =    $.ajax({
            type: "POST",
            url: "@Url.Action("DownloadFIMG1", "ADDI10")",



            data: JSON.stringify({
                postData
            }),
            contentType: "application/json; charset=utf-8",
            dataType: "json",


               }).done(function (data) {
                   console.log(data);

            // alert('File download a success!');
            //var response = JSON.parse(data);
            console.log(data.filename);
                   window.location = "@Url.Action("DownloadZIP", (string)ViewBag.BRE_NO)?file=" + data.filename;
                   $("#barr").css("width", "100%");
                   $("#myModal").modal('hide');
            //var blob = new Blob([data]);
            //var link = document.createElement('a');
            //link.href = window.URL.createObjectURL(blob);
            //link.download = "Sample.pdf";
            //link.click();
               });
         setTimeout(function () {
             $("#barr").css("width", "100%");
             $("#myModal").modal('hide');
         }, 3000);
    }
    function ExportVote() {

        var postData = { "QUESTIONNAIRE_ID":"@Model.Search.WhereQUESTIONNAIRE_ID"};
        $.ajax({
            type: "POST",
            url: "@Url.Action("ExportTOExcelADDI10", "ADDI10")",


            data: JSON.stringify({
                postData
            }),
            contentType: "application/json; charset=utf-8",
            dataType: "json",


        }).done(function (data) {
            // alert('File download a success!');
            //var response = JSON.parse(data);
            console.log(data.filename);
           window.location = "@Url.Action("Download", (string)ViewBag.BRE_NO)?file=" + data.filename;
            //var blob = new Blob([data]);
            //var link = document.createElement('a');
            //link.href = window.URL.createObjectURL(blob);
            //link.download = "Sample.pdf";
            //link.click();
        });
    }

    function ExportVote1() {

        var postData = { "QUESTIONNAIRE_ID":"@Model.Search.WhereQUESTIONNAIRE_ID"};
        $.ajax({
            type: "POST",
            url: "@Url.Action("ExportTOExcelADDI10ALL", "ADDI10")",


            data: JSON.stringify({
                postData
            }),
            contentType: "application/json; charset=utf-8",
            dataType: "json",


        }).done(function (data) {
            // alert('File download a success!');
            //var response = JSON.parse(data);
            console.log(data.filename);
           window.location = "@Url.Action("Download", (string)ViewBag.BRE_NO)?file=" + data.filename;
            //var blob = new Blob([data]);
            //var link = document.createElement('a');
            //link.href = window.URL.createObjectURL(blob);
            //link.download = "Sample.pdf";
            //link.click();
        });
        }
        function onBack() {

            $("#formEdit").attr("action", "@backurl")
            $("#formEdit").submit();

        }

        function onStatistics() {
            $(targetFormID).attr("action", "@Url.Action("Index", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        function PrintBooK() {
            $('#tbData').printThis();
        }

     @*function funAjax() {

            $.ajax({
                url: '@Url.Action("StatisticsVoteDetails", (string)ViewBag.BRE_NO)',
                data: $(targetFormID).serialize(),
                type: 'post',
                async: false,
                cache: false,
                dataType: 'html',
                success: function (data) {
                    $(".progress").html(data);
                }
            });
    }

        function doSort(SortCol) {
            alert("a");
        OrderByName = $('#OrdercColumn').val();
        SyntaxName = $('#SyntaxName').val();
        console.log(OrderByName);
        if (OrderByName == SortCol) {

            if (SyntaxName == "Desc") {
                $('#SyntaxName').val("ASC");
            }
            else {
                $('#SyntaxName').val("Desc");
            }
        }
        else {
            $('#OrdercColumn').val(SortCol);
            $('#SyntaxName').val("Desc");
        }
        funAjax()
    }*@
</script>