﻿using ECOOL_APP.com.ecool.Models.entity;
using ECOOL_APP.EF;
using MvcPaging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
namespace EcoolWeb.ViewModels
{
    public class ZZT17QueryListViewModel
    {
          /// <summary>
        /// 搜尋字串
        /// </summary>
        public string whereKeyword {get;set;}

        /// <summary>
        /// 只顯示某一位學生的學校別
        /// </summary>
        public string whereSchoolNo { get; set; }

        /// <summary>
        /// 只顯示某一位學生的文章
        /// </summary>
        public string whereUserNo { get; set; }

        /// <summary>
        /// 只顯示某一位學生的文章
        /// </summary>
        public string whereUserNo2 { get; set; }


        /// <summary>
        /// 只顯示操作時間
        /// </summary>
        public string whereACTIONTIME_S { get; set; }

        /// <summary>
        /// 只顯示操作時間
        /// </summary>
        public string whereACTIONTIME_E { get; set; }

        /// <summary>
        /// 只顯示某年級
        /// </summary>
        public string whereGrade { get; set; }

        /// <summary>
        /// 只顯示某一班
        /// </summary>
        public string whereCLASS_NO { get; set; }

        /// <summary>
        /// 排序欄位
        /// </summary>
        public string OrdercColumn { get; set; }

        public int Page { get; set; }



        /// <summary>
        /// 查詢結果
        /// </summary>
        public IPagedList<uZZT17_2> ZZT17List;

        public ZZT17QueryListViewModel()
        {
            Page = 0;
            OrdercColumn = "ACTIONTIME";
        }
    }
}