/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/IntegralsSm/Regular/All.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.STIXIntegralsSm,{32:[0,0,250,0,0],160:[0,0,250,0,0],8748:[690,189,726,41,782],8749:[690,189,956,41,1012],8751:[690,189,790,41,782],8752:[690,189,1020,41,1012],8753:[690,189,560,41,552],8754:[690,189,560,41,552],8755:[690,189,560,41,552],10763:[694,190,593,41,552],10764:[695,189,1152,41,1242],10765:[694,190,512,41,552],10766:[693,190,512,41,552],10767:[694,190,560,41,552],10768:[694,190,496,41,552],10769:[695,189,560,41,552],10770:[694,191,513,41,552],10771:[694,190,512,41,552],10772:[694,190,635,41,597],10773:[694,190,512,43,552],10774:[695,189,512,41,552],10775:[694,190,613,13,586],10776:[695,189,512,41,552],10777:[694,190,512,40,551],10778:[694,190,512,40,551],10779:[784,190,462,41,552],10780:[694,284,496,41,552]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/IntegralsSm/Regular/All.js");
