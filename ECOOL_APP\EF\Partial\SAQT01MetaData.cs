﻿
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.IO;
using System.Linq;
using System.Text;

namespace ECOOL_APP.EF
{
    [MetadataType(typeof(SAQT01MetaData))]
    public partial class SAQT01
    {
        public class SAQT01MetaData
        {
            /// <summary>
            ///投票ID
            /// </summary>
            [DisplayName("投票ID")]
            public string QUESTIONNAIRE_ID { get; set; }

            /// <summary>
            ///投票標題
            /// </summary>
            [DisplayName("活動名稱")]
            public string QUESTIONNAIRE_NAME { get; set; }

            /// <summary>
            ///投票敘述
            /// </summary>
            [DisplayName("投票內容")]
            public string QUESTIONNAIRE_DESC { get; set; }

            /// <summary>
            ///投票開始日
            /// </summary>
            [DisplayName("投票開始日")]
            [DataType(DataType.Date), DisplayFormat(DataFormatString = "{0:yyyy/MM/dd}", ApplyFormatInEditMode = true)]
            public DateTime? QUESTIONNAIRE_SDATE { get; set; }

            /// <summary>
            ///投票結束日
            /// </summary>
            [DisplayName("投票結束日")]
            [DataType(DataType.Date), DisplayFormat(DataFormatString = "{0:yyyy/MM/dd}", ApplyFormatInEditMode = true)]
            public DateTime? QUESTIONNAIRE_EDATE { get; set; }

            /// <summary>
            ///狀態
            /// </summary>
            [DisplayName("狀態")]
            public byte? STATUS { get; set; }

            /// <summary>
            ///修改人
            /// </summary>
            [DisplayName("修改人")]
            public string CHG_PERSON { get; set; }

            /// <summary>
            ///修改日
            /// </summary>
            [DisplayName("修改日")]
            public DateTime? CHG_DATE { get; set; }

            /// <summary>
            ///建立日
            /// </summary>
            [DisplayName("建立日")]
            public string CRE_PERSON { get; set; }

            /// <summary>
            ///建立日
            /// </summary>
            [DisplayName("建立日")]
            public DateTime? CRE_DATE { get; set; }

            /// <summary>
            ///來源key=>學校代碼
            /// </summary>
            [DisplayName("來源key=>學校代碼")]
            public string SOU_KEY { get; set; }

            /// <summary>
            ///類別 1.投票
            /// </summary>
            [DisplayName("類別 1.投票")]
            public byte? REPORT_TYPE { get; set; }

            /// <summary>
            ///結束語內容
            /// </summary>
            [DisplayName("結束語內容")]
            public string END_DESC { get; set; }

            /// <summary>
            ///給予點數
            /// </summary>
            [DisplayName("給予點數")]
            public short CASH { get; set; }

            /// <summary>
            ///否開放看結果
            /// </summary>
            [DisplayName("否開放看結果")]
            public bool? RESULT { get; set; }
   　　　　 [DisplayName("開放看結果人員")]
            public string RESULT_PERSON { get; set; }
            /// <summary>
            ///是否記名
            /// </summary>
            [DisplayName("是否記名")]
            public bool? REGISTERED_BALLOT { get; set; }
        }

    }
}