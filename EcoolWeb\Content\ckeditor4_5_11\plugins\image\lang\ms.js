﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'image', 'ms', {
	alt: 'Text Alternatif',
	border: 'Border',
	btnUpload: 'Hantar ke Server',
	button2Img: 'Do you want to transform the selected image button on a simple image?', // MISSING
	hSpace: '<PERSON><PERSON>',
	img2Button: 'Do you want to transform the selected image on a image button?', // MISSING
	infoTab: 'Info Imej',
	linkTab: 'Sambungan',
	lockRatio: 'Tetap<PERSON> Nisbah',
	menu: 'Ciri-ciri Imej',
	resetSize: 'Saiz Set Semula',
	title: 'Ciri-ciri Imej',
	titleButton: 'Ciri-ciri Butang <PERSON>am<PERSON>',
	upload: 'Muat Naik',
	urlMissing: 'Image source URL is missing.', // MISSING
	vSpace: '<PERSON><PERSON>',
	validateBorder: 'Border must be a whole number.', // MISSING
	validateHSpace: 'HSpace must be a whole number.', // MISSING
	validateVSpace: 'VSpace must be a whole number.' // MISSING
} );
