﻿@model EcoolWeb.ViewModels.ADDTBatchDeleteViewViewModel
@{
    ViewBag.Title = "閱讀認證-批次作廢資料成功";
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@using (Html.BeginForm("BatchDeleteView", "ADDT", FormMethod.Post, new { id = "form1", name = "form1" }))
{

    @Html.HiddenFor(m => m.StringDelApply)

    <h3 style="color:red">以下是作廢失敗的資料</h3>


    <div class="table-responsive">
        <div class="text-center">
            <table class="table-ecool table-92Per table-hover table-ecool-reader">
                <thead>
                    <tr>
                        <th style="text-align: center;">
                            申請日期
                        </th>
                        <th style="text-align: center;">
                            班級
                        </th>
                        <th style="text-align: center;">
                            座號
                        </th>
                        <th style="text-align: center;">
                            姓名
                        </th>
                        <th style="text-align: center;">
                            書名
                        </th>
                        <th style="text-align: center;">
                            狀態
                        </th>
                        <th style="text-align: center;">
                            失敗原因
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var item in Model.BatchDeleteList.Where(x => x.OK == false))
                    {
                        <tr>
                            <td style="text-align:left;white-space:nowrap;">
                                @Html.DisplayFor(modelItem => item.aDDT06.CRE_DATE, "ShortDateTime")
                            </td>
                            <td style="cursor:pointer;">
                                @Html.DisplayFor(modelItem => item.aDDT06.CLASS_NO)
                            </td>
                            <td>
                                @Html.DisplayFor(modelItem => item.aDDT06.SEAT_NO)
                            </td>
                            <td style="text-align:center;">
                                @Html.DisplayFor(modelItem => item.aDDT06.NAME)
                            </td>
                            <td align="left">
                                @Html.DisplayFor(modelItem => item.aDDT06.BOOK_NAME)

                                @if (item.aDDT06.SHARE_YN == "Y" || item.aDDT06.SHARE_YN == "y")
                                {
                                    <img src="~/Content/img/icons-like-05.png" />
                                }
                            </td>
                            <td style="text-align:center;">
                                @ECOOL_APP.EF.ADDStatus.GetADDT06StatusString(item.aDDT06.APPLY_STATUS)
                            </td>
                            <td>
                                <span style="color">@item.Error</span>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>
    <br />
    <h3 style="color:red">以下是作廢成功的資料</h3>

    <div class="table-responsive">
        <div class="text-center">
            <table class="table-ecool table-92Per table-hover table-ecool-reader">
                <thead>
                    <tr>
                        <th style="text-align: center;">
                            申請日期
                        </th>
                        <th style="text-align: center;">
                            班級
                        </th>
                        <th style="text-align: center;">
                            座號
                        </th>
                        <th style="text-align: center;">
                            姓名
                        </th>
                        <th style="text-align: center;">
                            書名
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var item in Model.BatchDeleteList.Where(x => x.OK == true))
                    {
                        <tr>
                            <td style="text-align:left;white-space:nowrap;">
                                @Html.DisplayFor(modelItem => item.aDDT06.CRE_DATE, "ShortDateTime")
                            </td>
                            <td style="cursor:pointer;">
                                @Html.DisplayFor(modelItem => item.aDDT06.CLASS_NO)
                            </td>
                            <td>
                                @Html.DisplayFor(modelItem => item.aDDT06.SEAT_NO)
                            </td>
                            <td style="text-align:center;">
                                @Html.DisplayFor(modelItem => item.aDDT06.NAME)
                            </td>
                            <td align="left">
                                @Html.DisplayFor(modelItem => item.aDDT06.BOOK_NAME)

                                @if (item.aDDT06.SHARE_YN == "Y" || item.aDDT06.SHARE_YN == "y")
                                {
                                    <img src="~/Content/img/icons-like-05.png" />
                                }
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>

    <br />
    <div class="row">
        <div class="text-center">
            <a href='@Url.Action("ADDTALLList", "ADDT")' role="button" class="btn btn-default">
                返回認證審核一覽表
            </a>
        </div>
    </div>
}