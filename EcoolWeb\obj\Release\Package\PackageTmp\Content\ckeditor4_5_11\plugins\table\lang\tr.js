﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'table', 'tr', {
	border: '<PERSON><PERSON>',
	caption: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
	cell: {
		menu: 'Hücre',
		insertBefore: 'Hücre E<PERSON> - <PERSON>',
		insertAfter: 'Hücre Ekle - Sonra',
		deleteCell: 'Hücre Sil',
		merge: 'Hücreleri Birleştir',
		mergeRight: 'Birleştir - Sağdaki İle ',
		mergeDown: 'Birleştir - Aşağıdaki İle ',
		splitHorizontal: 'Hücreyi Yatay Böl',
		splitVertical: 'Hücreyi Dikey Böl',
		title: 'Hücre Özellikleri',
		cellType: 'Hücre Tipi',
		rowSpan: 'Satırlar <PERSON> (Span)',
		colSpan: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Span)',
		wordWrap: '<PERSON><PERSON><PERSON>',
		hAlign: '<PERSON><PERSON><PERSON><PERSON>',
		vAlign: 'Ya<PERSON>ş Hizalama',
		alignBaseline: 'Tabana',
		bgColor: 'Arkaplan Rengi',
		borderColor: 'Çerçeve Rengi',
		data: 'Veri',
		header: 'Başlık',
		yes: 'Evet',
		no: 'Hayır',
		invalidWidth: 'Hücre genişliği sayı olmalıdır.',
		invalidHeight: 'Hücre yüksekliği sayı olmalıdır.',
		invalidRowSpan: 'Satırların mesafesi tam sayı olmalıdır.',
		invalidColSpan: 'Sütünların mesafesi tam sayı olmalıdır.',
		chooseColor: 'Seçiniz'
	},
	cellPad: 'Izgara yazı arası',
	cellSpace: 'Izgara kalınlığı',
	column: {
		menu: 'Sütun',
		insertBefore: 'Kolon Ekle - Önce',
		insertAfter: 'Kolon Ekle - Sonra',
		deleteColumn: 'Sütun Sil'
	},
	columns: 'Sütunlar',
	deleteTable: 'Tabloyu Sil',
	headers: 'Başlıklar',
	headersBoth: 'Her İkisi',
	headersColumn: 'İlk Sütun',
	headersNone: 'Yok',
	headersRow: 'İlk Satır',
	invalidBorder: 'Çerceve büyüklüklüğü sayı olmalıdır.',
	invalidCellPadding: 'Hücre aralığı (padding) sayı olmalıdır.',
	invalidCellSpacing: 'Hücre boşluğu (spacing) sayı olmalıdır.',
	invalidCols: 'Sütün sayısı 0 sayısından büyük olmalıdır.',
	invalidHeight: 'Tablo yüksekliği sayı olmalıdır.',
	invalidRows: 'Satır sayısı 0 sayısından büyük olmalıdır.',
	invalidWidth: 'Tablo genişliği sayı olmalıdır.',
	menu: 'Tablo Özellikleri',
	row: {
		menu: 'Satır',
		insertBefore: 'Satır Ekle - Önce',
		insertAfter: 'Satır Ekle - Sonra',
		deleteRow: 'Satır Sil'
	},
	rows: 'Satırlar',
	summary: 'Özet',
	title: 'Tablo Özellikleri',
	toolbar: 'Tablo',
	widthPc: 'yüzde',
	widthPx: 'piksel',
	widthUnit: 'genişlik birimi'
} );
