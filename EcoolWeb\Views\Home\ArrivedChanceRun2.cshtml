﻿
@{
    ViewBag.Title = "進步加值";
    string ImageUrl = Url.Content("~/Content/mp4/") + "ST01-5_Slot-" + ViewBag.ChanceResult.ToString() + "P_W2-600_HQ.mp4";

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
        ImageUrl = Url.Content("~/Content/mp4/") + "ST01-5_Slot-" + ViewBag.ChanceResult.ToString() + "P_W2-600_HQ.gif";
    }
}
<br />
<span style="font-size: 12pt;font-weight: bold; color:red">
    好運不常有，努力一定有！酷幣點數大放送來囉！
</span>
<br />

<span style="font-size: 12pt;font-weight: bold;color:blue">
    因為您的努力，積極學習參與，所以你有機會參加酷幣大放送活動！只要你的酷幣每增加<br />
    100點，你就能多玩一次拉霸，獲得更多酷幣！
</span>
<br />
<div style="height:41px"></div>
<div style="margin: 0px auto;width:100%">
@if (AppMode)
{
    <img style="width:400px ;height: auto" src='@ImageUrl' />
}
else
{
    <video id="SlotPlayer" style="width:600px ;height: auto">
        <source src='@ImageUrl' type="video/mp4">
    </video>
}


</div>
<script type="text/javascript">

    window.history.forward(1);

    $(document).ready(function () {
        var video = document.getElementById("SlotPlayer");
        video.load();
        video.play();
    });
</script>
