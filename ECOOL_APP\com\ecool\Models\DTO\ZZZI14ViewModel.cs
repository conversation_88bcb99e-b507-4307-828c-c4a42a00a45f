﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.com.ecool.Models.DTO
{
    public class ZZZI14ViewModel
    {
        ///Summary
        ///
        ///Summary
        [DisplayName("功能代碼")]
        [Required]
        public string BRE_NO { get; set; }

        ///Summary
        ///
        ///Summary
        [DisplayName("功能名稱")]
        [Required]
        public string BRE_NAME { get; set; }

        ///Summary
        ///
        ///Summary
        [DisplayName("控制項名稱")]
        [Required]
        public string CONTROLLER { get; set; }

        ///Summary
        ///
        ///Summary
        [DisplayName("預設動作代碼")]
        public string ACTION_ID { get; set; }

        ///Summary
        ///
        ///Summary
        [DisplayName("階層")]
        [Required]
        [Range(0, int.MaxValue, ErrorMessage = "請輸入數字")]
        public Nullable<decimal> LEVEL_ID { get; set; }

        ///Summary
        ///
        ///Summary
        [DisplayName("階層等級號碼")]
        public string LEVEL_NO { get; set; }

        ///Summary
        ///
        ///Summary
        [DisplayName("網址")]
        [DataType(DataType.Url, ErrorMessage = "請輸入正確的網址")]
        public string LINK_ADDR { get; set; }

        ///Summary
        ///
        ///Summary
        [DisplayName("父階功能代號")]
        public string BRE_NO_PRE { get; set; }

        ///Summary
        ///
        ///Summary
        [DisplayName("同階順序")]
        [Required]
        [Range(0, double.MaxValue, ErrorMessage = "請輸入數字")]
        public Nullable<decimal> ORDER_BY { get; set; }

        ///Summary
        ///
        ///Summary
        [DisplayName("程式類別(目標)")]
        [Required]
        public string TARGET { get; set; }

        /// <summary>
        /// 類別==>(1.全部(不卡權限)、2.依角色、3.標題)
        /// </summary>
        [DisplayName("類別")]
        [Required]
        public string BRE_TYPE { get; set; }

        [DisplayName("類別名稱")]
        public string BRE_TYPE_NAME { get; set; }

        [DisplayName("APP是否使用")]
        public string APP_USE_YN { get; set; }

        ///Summary
        ///
        ///Summary
        [DisplayName("說明")]
        public string FUN_DESC { get; set; }

        ///Summary
        ///
        ///Summary
        [DisplayName("群組說明")]
        public string GROUP_DESC { get; set; }

        ///Summary
        ///
        ///Summary
        [DisplayName("有效日期(起)")]
        [DataType(DataType.Date), DisplayFormat(DataFormatString = "{0:yyyy/MM/dd}", ApplyFormatInEditMode = true)]
        public Nullable<DateTime> WORK_DATES { get; set; }

        ///Summary
        ///
        ///Summary
        [DisplayName("有效日期(迄)")]
        [DataType(DataType.Date), DisplayFormat(DataFormatString = "{0:yyyy/MM/dd}", ApplyFormatInEditMode = true)]
        public Nullable<DateTime> WORK_DATEE { get; set; }

        [DisplayName("修改人員")]
        public string CHG_PERSON { get; set; }

        [DisplayName("修改日期")]
        public Nullable<DateTime> CHG_DATE { get; set; }

        [DisplayName("建檔人員")]
        public string CRE_PERSON { get; set; }

        [DisplayName("建檔日期")]
        public Nullable<DateTime> CRE_DATE { get; set; }

        public List<ZZZI14ViewModel_D> Details_List { get; set; }

        [DisplayName("啟用")]
        public bool ENABLE { get; set; }
    }

    public class ZZZI14ViewModel_D
    {
        [DisplayName("刪除")]
        public bool Del { get; set; }

        [DisplayName("序號")]
        public int ITEM { get; set; }

        [DisplayName("預設動作")]
        public string DF_ACTION_ID { get; set; }

        [DisplayName("預設動作")]
        public bool DF_ACTION_ID_Check { get; set; }

        [DisplayName("功能代碼")]
        public string BRE_NO { get; set; }

        [DisplayName("動作代碼")]
        public string ACTION_ID { get; set; }

        [DisplayName("動作名稱")]
        public string ACTION_NAME { get; set; }

        /// <summary>
        /// 動作類別 ==>ALL.全部(不卡權限) ,R 依角色限制權限
        /// </summary>
        [DisplayName("動作類別")]
        public string ACTION_TYPE { get; set; }

        [DisplayName("動作類別名稱")]
        public string ACTION_TYPE_NAME { get; set; }

        [DisplayName("說明")]
        public string FUN_DESC { get; set; }

        [DisplayName("修改人員")]
        public string CHG_PERSON { get; set; }

        [DisplayName("修改日期")]
        public Nullable<DateTime> CHG_DATE { get; set; }

        [DisplayName("建檔人員")]
        public string CRE_PERSON { get; set; }

        [DisplayName("建檔日期")]
        public Nullable<DateTime> CRE_DATE { get; set; }
    }
}