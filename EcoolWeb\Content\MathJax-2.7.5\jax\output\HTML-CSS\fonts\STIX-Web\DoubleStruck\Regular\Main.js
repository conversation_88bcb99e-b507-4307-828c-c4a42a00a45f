/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX-Web/DoubleStruck/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.STIXMathJax_DoubleStruck={directory:"DoubleStruck/Regular",family:"STIXMathJax_DoubleStruck",testString:"\u00A0\u2102\u210D\u2115\u2119\u211A\u211D\u2124\u213C\u213D\u213E\u213F\u2140\uD835\uDD38\uD835\uDD39",32:[0,0,250,0,0],160:[0,0,250,0,0],8450:[676,14,705,45,663],8461:[662,0,718,70,648],8469:[662,0,698,70,628],8473:[662,0,678,70,628],8474:[676,65,765,45,715],8477:[662,0,747,70,712],8484:[662,0,727,50,677],8508:[450,12,673,25,645],8509:[460,218,540,0,526],8510:[662,0,469,70,567],8511:[662,0,718,70,648],8512:[763,259,923,61,882],120120:[662,0,741,50,691],120121:[662,0,676,70,626],120123:[662,0,722,70,677],120124:[662,0,622,70,567],120125:[662,0,469,70,567],120126:[676,13,706,45,664],120128:[662,0,322,78,244],120129:[662,14,560,40,495],120130:[674,0,735,70,729],120131:[662,0,591,70,571],120132:[662,0,855,70,785],120134:[676,14,760,45,715],120138:[676,14,636,35,597],120139:[662,0,527,20,622],120140:[662,14,698,65,633],120141:[662,0,568,12,653],120142:[662,0,920,12,949],120143:[662,0,768,35,733],120144:[662,0,563,12,685],120146:[460,10,561,45,506],120147:[683,10,565,50,524],120148:[460,10,520,45,475],120149:[683,10,574,45,519],120150:[460,10,523,45,478],120151:[683,0,368,25,431],120152:[460,218,574,45,519],120153:[683,0,544,55,489],120154:[683,0,258,55,203],120155:[683,217,305,-15,250],120156:[683,0,551,50,539],120157:[683,0,258,55,203],120158:[460,0,830,55,775],120159:[460,0,544,55,489],120160:[458,12,553,45,508],120161:[460,218,574,55,529],120162:[460,218,574,45,519],120163:[462,0,301,55,407],120164:[460,10,519,36,483],120165:[633,10,329,20,297],120166:[450,10,544,55,489],120167:[450,0,443,20,479],120168:[450,0,676,20,695],120169:[450,0,560,30,530],120170:[450,218,468,20,510],120171:[450,0,519,43,476],120792:[676,14,540,28,512],120793:[693,0,540,91,355],120794:[676,0,547,48,514],120795:[676,14,540,49,478],120796:[676,0,540,20,524],120797:[662,14,540,35,489],120798:[676,14,540,28,512],120799:[662,0,540,24,511],120800:[676,14,540,28,512],120801:[676,12,540,28,512]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"STIXMathJax_DoubleStruck"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/DoubleStruck/Regular/Main.js"]);
