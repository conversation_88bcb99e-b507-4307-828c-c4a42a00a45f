﻿@model ZZZI09ShowADDT25_TREFViewViewModel

@{
    if (Model.Datalist != null && Model.Datalist.Count() != 0)
    {
        <br />
            <div class="col-sm-12 text-center">
                <h1><b>
    神秘任務
    
</b></h1>
            </div>
      
        foreach (var item in Model.Datalist)
        {
            <div class="col-sm-12">
                <b>@item.TASK_DESC</b>
            </div>
            <div class="col-sm-12">
                <b>答:</b>
            </div>
            <div class="col-md-12">
                <div class="p-context">
                    @item.ANSWERS

                    @foreach (var Name in item.FileName)
                    {
                        if (!string.IsNullOrWhiteSpace(Name))
                        {
                            string ImgPath = item.FilePaths + @"\" + Name;

                        <img src="@Url.Content(ImgPath)" href="@Url.Content(ImgPath)" class="img-responsive " />
                        }   }
                </div>
            </div>
           
        }
    }
}