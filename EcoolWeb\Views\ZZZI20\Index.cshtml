﻿@model List<ECOOL_APP.EF.HRMV01>
@{
    ViewBag.Title = "酷幣匯轉功能 - 匯入轉學生酷幣";
}

@Html.Partial("_Title_Secondary")

@Html.Partial("_Notice")
<a href="/EcoolWeb/Content/轉學生轉匯酷幣.docx">
    <img src="/EcoolWeb/Content/img/user_manual_icon.png" style="width:50px;height:50px;border: 0px" title="說明文件">
</a>
<div class="panel panel-ZZZ" name="TOP">
    <div class="panel-heading text-center">
        @Html.BarTitle()
    </div>
    <div class="table-responsive">
        <table class="table-ecool table-92Per table-hover table-ecool-ZZZ">
            <thead>
                <tr>
                    <th style="text-align: center">
                        班級
                    </th>
                    <th style="text-align: center">
                        學號
                    </th>
                    <th style="text-align: center">
                        座號
                    </th>
                    <th style="text-align: center">
                        姓名
                    </th>
                    <th style="text-align: center">
                        狀態
                    </th>
                </tr>
            </thead>
            <tbody>
                @foreach (var item in Model)
            {
                    <tr style="text-align: center">
                        <td>
                            @Html.DisplayFor(modelItem => item.CLASS_NO)
                        </td>
                        <td>
                            @Html.DisplayFor(modelItem => item.USER_NO)
                        </td>
                        <td>
                            @Html.DisplayFor(modelItem => item.SEAT_NO)
                        </td>
                        <td>
                            <a href='@Url.Action("Details", "ZZZI20", new { IDNO = item.IDNO })' class="btn-table-link">
                                @item.NAME
                            </a>
                        </td>
                        <td>
                            @UserStaus.GetDesc(item.USER_STATUS)
                        </td>
                    </tr>
                }
            </tbody>
        </table>
    </div>
</div>






