@font-face {
  font-family: "TaipeiSansTCBeta-Regular";
  src: local("TaipeiSansTCBeta-Regular"), url("../../Scripts/jspdf/TaipeiSansTCBeta-Regular.ttf") format("opentype");
}
body {
  font-family: "TaipeiSansTCBeta-Regular";
}

.container {
  padding: 0;
  margin: 0 auto;
  width: 600px;
  background-color: #fff;
  box-shadow: 0 0 6px rgba(0, 0, 0, 0.05);
}

* {
  -webkit-print-color-adjust: exact;
}

body {
  width: 100%;
  height: auto;
  margin: 0;
  float: none;
  clear: both;
  line-height: 1.3;
  display: block;
  background-color: #fff;
}

.container {
  width: 600px;
}

h1 {
  font-size: 30pt;
  font-weight: bolder;
  page-break-before: always;
}

h2 {
  font-size: 24pt;
  page-break-after: avoid;
}

p {
  font-size: 18pt;
}

img,
svg {
  display: inline-block;
  page-break-inside: avoid;
}

section {
  page-break-after: always;
}

.cover img {
  margin-left: 0;
  width: 100%;
  position: relative;
}

.cover,
.print-page {
  min-height: 20cm;
}

.break-avoid,
.table {
  page-break-inside: avoid;
}

.bg-custom-yellow {
  margin-left: 0.5cm;
  margin-right: 0.5cm;
  background-color: #fffef9 !important;
  border-color: #fdf9e2;
}

.profile {
  page-break-inside: avoid;
}

.profile > img:first-child {
  margin-top: 0rem;
}

.bgPosition {
  margin-top: 1.5cm;
}

.bgPosition-profile {
  margin-top: -3.5rem;
}

.record {
  margin-top: 20mm;
}

.record .bg-custom-yellow {
  background-color: #fffcda !important;
}

.record .profilePhoto {
  width: 180px;
  height: 180px;
}

.record .table th,
.record .table td {
  background-color: transparent !important;
}

.record .table-record th {
  background-color: #ffebb7 !important;
}

.record .table-record tr td {
  background-color: #fef3da !important;
  white-space: nowrap;
}

.record .table-record tr:last-child > td {
  background-color: #fef7e8 !important;
}

.ePassport .table thead th,
.readingBooksData .table thead th,
.healthData .table thead th {
  background-color: #ffebb7 !important;
}

.ePassport .table-striped tbody tr:nth-of-type(odd) > td,
.readingBooksData .table-striped tbody tr:nth-of-type(odd) > td,
.healthData .table-striped tbody tr:nth-of-type(odd) > td {
  background-color: #fef3da !important;
}

.ePassport .table-striped tbody tr:nth-of-type(even) > td,
.readingBooksData .table-striped tbody tr:nth-of-type(even) > td,
.healthData .table-striped tbody tr:nth-of-type(even) > td {
  background-color: #fef7e8 !important;
}

.honorArea {
  page-break-inside: avoid;
  padding-top: 0.5cm;
}

.onlineSubmit .break-avoid + .break-avoid,
.readingCertification .break-avoid + .break-avoid {
  padding-top: 8mm;
  page-break-inside: avoid !important;
}

.onlineSubmit .bg-custom-white,
.readingCertification .bg-custom-white {
  page-break-after: avoid !important;
}

.onlineSubmit .bg-custom-white + .content .grid,
.readingCertification .bg-custom-white + .content .grid {
  page-break-inside: avoid;
}

.onlineSubmit .bg-custom-white + .content p,
.readingCertification .bg-custom-white + .content p {
  page-break-inside: auto !important;
}

.onlineSubmit .bg-custom-white + .content hr,
.readingCertification .bg-custom-white + .content hr {
  border-top: 1pt dotted #7f5100;
}

.onlineSubmit .bg-custom-white + .content table tr,
.readingCertification .bg-custom-white + .content table tr {
  font-size: 1.45rem;
}

.onlineSubmit .break-avoid:last-child,
.readingCertification .break-avoid:last-child {
  padding-bottom: 10mm !important;
}

div[data-highcharts-chart] {
  transform: rotate(90deg);
  page-break-after: auto;
  page-break-inside: avoid;
  height: 290mm;
}

div[data-highcharts-chart] > div {
  max-height: 100%;
  page-break-inside: avoid;
  top: 50%;
  transform: translateY(-50%);
}

.ecoolStats .bgPosition {
  page-break-inside: avoid !important;
  page-break-after: auto;
}

.ecoolStats .heading-h2 + div[data-highcharts-chart] {
  page-break-inside: avoid;
}

.healthData .table,
.readingBooksData .table {
  page-break-inside: avoid;
}

.healthData .charts,
.readingBooksData .charts {
  page-break-before: always;
}

.healthData .charts .col-12,
.readingBooksData .charts .col-12 {
  display: inline-block;
  page-break-inside: avoid;
  page-break-before: always;
  page-break-after: always;
}

.onlineGallery .card {
  page-break-after: auto;
  page-break-inside: avoid;
}

.onlineGallery .card .card-body {
  border: 1px solid #eee;
  border-radius: 0 0 0.25rem 0.25rem;
  margin-top: -1px;
}

.charts {
  overflow: visible;
}/*# sourceMappingURL=learning-history-jsPDF.css.map */