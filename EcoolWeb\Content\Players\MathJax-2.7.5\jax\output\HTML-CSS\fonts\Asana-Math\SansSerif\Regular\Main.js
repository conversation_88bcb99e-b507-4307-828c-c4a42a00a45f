/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Asana-Math/SansSerif/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.AsanaMathJax_SansSerif={directory:"SansSerif/Regular",family:"AsanaMathJax_SansSerif",testString:"\uD835\uDDA0\uD835\uDDA1\uD835\uDDA2\uD835\uDDA3\uD835\uDDA4\uD835\uDDA5\uD835\uDDA6\uD835\uDDA7\uD835\uDDA8\uD835\uDDA9\uD835\uDDAA\uD835\uDDAB\uD835\uDDAC\uD835\uDDAD\uD835\uDDAE",32:[0,0,249,0,0],120224:[694,0,665,28,638],120225:[694,0,665,97,610],120226:[716,22,637,67,588],120227:[694,0,721,96,665],120228:[691,0,596,94,554],120229:[691,0,568,94,526],120230:[716,22,665,67,599],120231:[694,0,707,94,613],120232:[694,0,276,94,183],120233:[694,22,471,42,388],120234:[694,0,693,96,651],120235:[694,0,540,94,499],120236:[694,0,874,100,774],120237:[694,0,707,96,611],120238:[716,22,735,56,679],120239:[694,0,637,96,582],120240:[716,125,735,56,679],120241:[694,0,644,96,617],120242:[716,22,554,44,499],120243:[688,0,679,36,644],120244:[694,22,686,94,593],120245:[694,0,665,14,652],120246:[694,0,943,14,929],120247:[694,0,665,14,652],120248:[694,0,665,3,663],120249:[694,0,610,56,560],120250:[461,11,479,44,399],120251:[694,11,515,82,480],120252:[461,11,443,36,415],120253:[694,11,515,36,434],120254:[461,11,443,35,414],120255:[705,0,304,27,347],120256:[455,206,499,28,485],120257:[694,0,515,81,435],120258:[680,0,237,74,163],120259:[680,205,265,-61,184],120260:[694,0,487,84,471],120261:[694,0,237,81,156],120262:[455,0,793,81,713],120263:[455,0,515,81,435],120264:[461,11,499,30,469],120265:[455,194,515,82,480],120266:[455,194,515,36,434],120267:[455,0,340,82,327],120268:[461,11,382,28,360],120269:[571,11,360,19,332],120270:[444,11,515,81,435],120271:[444,0,460,14,446],120272:[444,0,682,14,668],120273:[444,0,460,0,460],120274:[444,205,460,14,446],120275:[444,0,433,28,402],120276:[694,0,732,42,690],120277:[694,0,732,91,671],120278:[716,22,701,61,647],120279:[694,0,793,91,732],120280:[691,0,640,91,595],120281:[691,0,610,91,564],120282:[716,22,732,61,659],120283:[694,0,793,91,702],120284:[694,0,329,92,239],120285:[694,22,518,46,427],120286:[694,0,762,91,701],120287:[694,0,579,91,534],120288:[694,0,976,91,886],120289:[694,0,793,91,702],120290:[716,22,793,61,732],120291:[694,0,701,91,641],120292:[716,106,793,61,732],120293:[694,0,701,91,653],120294:[716,22,610,48,549],120295:[688,0,732,40,692],120296:[694,22,762,91,672],120297:[694,0,732,27,705],120298:[694,0,1037,24,1014],120299:[694,0,732,37,694],120300:[694,0,732,24,708],120301:[694,0,671,61,616],120302:[475,11,524,31,464],120303:[694,11,560,61,523],120304:[475,11,487,37,457],120305:[694,11,560,37,499],120306:[475,11,510,31,479],120307:[705,0,335,30,381],120308:[469,206,549,25,534],120309:[694,0,560,60,500],120310:[695,0,254,54,201],120311:[695,205,285,-71,224],120312:[694,0,529,69,497],120313:[694,0,254,61,194],120314:[469,0,865,60,806],120315:[469,0,560,60,500],120316:[475,11,549,31,518],120317:[469,194,560,61,523],120318:[469,194,560,37,499],120319:[469,0,371,61,356],120320:[475,11,420,31,396],120321:[589,11,403,20,373],120322:[458,11,560,60,500],120323:[458,0,499,26,473],120324:[458,0,743,24,719],120325:[458,0,499,24,474],120326:[458,205,499,29,473],120327:[458,0,475,31,441],120328:[694,0,665,28,638],120329:[694,0,665,97,696],120330:[716,22,637,131,722],120331:[694,0,721,96,747],120332:[691,0,596,94,687],120333:[691,0,568,94,673],120334:[716,22,665,131,733],120335:[694,0,707,94,761],120336:[694,0,276,94,331],120337:[694,22,471,46,536],120338:[694,0,693,96,785],120339:[694,0,540,94,513],120340:[694,0,874,100,922],120341:[694,0,707,96,759],120342:[716,22,735,119,762],120343:[694,0,637,96,690],120344:[716,125,735,119,762],120345:[694,0,644,96,700],120346:[716,22,554,54,607],120347:[688,0,679,155,790],120348:[694,22,686,137,741],120349:[694,0,665,161,800],120350:[694,0,943,161,1077],120351:[694,0,665,14,758],120352:[694,0,665,150,811],120353:[694,0,610,56,701],120354:[461,11,479,65,465],120355:[694,11,515,82,535],120356:[461,11,443,77,499],120357:[694,11,515,76,582],120358:[461,11,443,77,471],120359:[705,0,304,101,494],120360:[455,206,499,11,571],120361:[694,0,515,81,505],120362:[680,0,237,81,307],120363:[680,205,265,-97,329],120364:[694,0,487,84,543],120365:[694,0,237,81,304],120366:[455,0,793,81,783],120367:[455,0,515,81,505],120368:[461,11,499,71,522],120369:[455,194,515,41,535],120370:[455,194,515,76,531],120371:[455,0,340,82,424],120372:[461,11,382,36,434],120373:[571,11,360,101,410],120374:[444,11,515,99,529],120375:[444,0,460,108,540],120376:[444,0,682,108,762],120377:[444,0,460,0,538],120378:[444,205,460,1,540],120379:[444,0,433,28,494],120380:[695,0,696,28,670],120381:[695,0,749,68,781],120382:[733,37,700,131,785],120383:[695,0,781,66,807],120384:[690,0,596,63,687],120385:[690,0,568,63,673],120386:[733,37,743,131,811],120387:[695,0,737,63,791],120388:[695,0,442,93,497],120389:[695,37,500,33,565],120390:[695,0,707,66,799],120391:[695,0,540,62,513],120392:[695,0,874,100,922],120393:[695,0,733,69,785],120394:[733,37,769,119,796],120395:[695,0,694,66,747],120396:[733,132,770,119,797],120397:[695,0,701,66,757],120398:[733,37,579,36,632],120399:[686,0,679,168,790],120400:[695,37,774,137,829],120401:[695,0,685,129,820],120402:[695,0,963,131,1097],120403:[695,0,683,-19,776],120404:[695,0,682,120,828],120405:[695,0,613,54,704],120406:[480,30,479,47,484],120407:[712,30,515,60,553],120408:[480,30,443,59,521],120409:[712,30,515,58,605],120410:[480,30,443,59,490],120411:[724,19,304,78,515],120412:[474,224,499,-8,590],120413:[712,19,515,59,523],120414:[698,19,237,59,330],120415:[698,223,265,-120,352],120416:[712,19,487,62,594],120417:[712,19,237,59,327],120418:[474,19,793,59,801],120419:[474,19,515,59,523],120420:[480,30,499,53,540],120421:[474,213,515,18,553],120422:[474,213,515,58,554],120423:[474,19,340,60,447],120424:[480,30,382,18,458],120425:[590,30,360,78,433],120426:[463,30,515,80,552],120427:[463,19,460,86,573],120428:[463,19,682,87,792],120429:[463,19,460,-47,585],120430:[463,223,460,-20,573],120431:[463,19,433,7,516],120662:[694,0,732,42,690],120663:[694,0,732,91,671],120664:[690,0,579,92,537],120665:[694,-8,915,60,855],120666:[691,0,640,91,595],120667:[694,0,671,61,616],120668:[694,0,793,91,702],120669:[716,22,854,62,792],120670:[694,0,329,92,239],120671:[694,0,762,91,701],120672:[694,0,671,41,630],120673:[694,0,976,91,886],120674:[694,0,793,91,702],120675:[687,0,732,45,687],120676:[716,22,793,61,732],120677:[690,2,793,92,700],120678:[694,0,701,91,641],120679:[716,22,854,62,792],120680:[693,-1,793,61,732],120681:[688,0,732,40,692],120682:[715,0,854,61,792],120683:[695,0,793,62,731],120684:[694,0,732,37,694],120685:[695,0,854,62,793],120686:[716,0,793,48,744],120687:[694,-8,915,60,855],120688:[469,13,742,47,720],120689:[733,90,549,46,503],120690:[469,201,610,32,577],120691:[719,10,518,46,475],120692:[470,11,472,33,456],120693:[734,265,518,46,472],120694:[481,200,564,30,514],120695:[733,11,549,46,503],120696:[471,11,304,34,290],120697:[471,12,531,62,547],120698:[734,1,549,19,530],120699:[470,204,610,35,618],120700:[458,0,518,-12,500],120701:[760,211,518,46,472],120702:[468,10,579,46,532],120703:[458,11,641,-24,642],120704:[469,192,518,46,471],120705:[458,172,488,46,458],120706:[458,10,625,46,594],120707:[458,11,503,-44,476],120708:[458,10,549,34,503],120709:[469,193,641,47,594],120710:[470,208,610,33,577],120711:[722,193,641,46,595],120712:[458,11,732,39,693],120713:[636,6,453,24,430],120714:[519,-2,534,59,483],120715:[712,22,627,62,609],120716:[518,8,574,19,538],120717:[603,192,565,33,536],120718:[444,199,463,27,431],120719:[514,11,834,33,800],120720:[694,0,732,-14,634],120721:[694,0,732,36,672],120722:[690,0,579,36,595],120723:[694,-8,915,2,798],120724:[691,0,640,36,634],120725:[694,0,671,6,664],120726:[694,0,793,36,756],120727:[716,22,854,51,801],120728:[694,0,329,37,293],120729:[694,0,762,36,753],120730:[694,0,671,-16,573],120731:[694,0,976,36,940],120732:[694,0,793,36,756],120733:[687,0,732,-10,734],120734:[716,22,793,49,742],120735:[690,2,793,35,757],120736:[694,0,701,36,668],120737:[716,22,854,51,801],120738:[693,-1,793,5,786],120739:[688,0,732,89,746],120740:[715,0,854,96,829],120741:[695,0,793,57,735],120742:[694,0,732,-20,706],120743:[695,0,854,102,834],120744:[716,0,793,-7,754],120745:[697,-8,915,117,912],120746:[469,13,695,40,707],120747:[733,90,549,-25,491],120748:[469,201,610,59,635],120749:[719,10,518,15,516],120750:[470,11,472,20,476],120751:[734,265,518,44,529],120752:[481,200,564,57,546],120753:[733,11,549,34,513],120754:[471,11,304,11,274],120755:[470,12,531,21,521],120756:[734,0,547,-37,478],120757:[470,206,610,-20,620],120758:[458,0,518,29,523],120759:[768,202,518,38,476],120760:[468,9,579,42,537],120761:[458,11,641,13,633],120762:[469,194,518,-6,491],120763:[460,165,488,53,502],120764:[458,10,625,42,634],120765:[458,11,503,-11,491],120766:[458,10,549,73,511],120767:[469,187,641,60,616],120768:[470,208,610,-11,621],120769:[722,193,641,43,604],120770:[458,11,732,31,693],120771:[636,6,453,0,411],120772:[519,-2,534,47,513],120773:[712,22,617,49,597],120774:[518,24,574,-3,578],120775:[603,192,565,30,541],120776:[444,199,463,-11,457],120777:[514,11,834,61,798],120778:[682,3,556,28,539],120779:[499,237,522,20,506],120802:[689,22,499,42,457],120803:[689,0,499,89,424],120804:[689,0,499,42,449],120805:[689,22,499,42,457],120806:[667,0,499,28,471],120807:[667,22,499,39,449],120808:[689,22,499,42,457],120809:[667,11,499,42,457],120810:[689,22,499,42,457],120811:[689,22,499,42,457],120812:[689,21,549,43,506],120813:[689,0,549,76,473],120814:[689,0,549,46,494],120815:[689,21,549,46,503],120816:[668,0,549,31,518],120817:[668,21,549,37,494],120818:[689,21,549,46,503],120819:[669,11,549,46,503],120820:[689,21,549,46,503],120821:[689,21,549,46,503]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"AsanaMathJax_SansSerif"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/SansSerif/Regular/Main.js"]);
