﻿@model ZZZI34WorkIndexViewModel
@using ECOOL_APP.com.ecool.service
@using ECOOL_APP.com.ecool.util
@{
    /**/

    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

    bool IsAdmin = ViewBag.IsAdmin;
    string WhereMyWorkSTr = "";
    if (Request["WhereMyWork"] == null)
    {

    }
    else
    {
        WhereMyWorkSTr = Request["WhereMyWork"];
    }
    string DivHotImg = ECOOL_APP.UrlCustomHelper.Url_Content("~/Content/img/GalleryBook_A2-1_Ass-11.png");
    string DivNormalImg = ECOOL_APP.UrlCustomHelper.Url_Content("~/Content/img/GalleryBook_A2-1_Ass-10.png");
    string IconHotImg = ECOOL_APP.UrlCustomHelper.Url_Content("~/Content/img/GalleryBook_A2-1_Ass-07.png");
}
<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
<style type="text/css">
    .element {
        float: left;
        padding: 3px;
        box-sizing: border-box;
    }
</style>
<script src="~/Scripts/grids.js"></script>
<script src="~/Content/colorbox/jquery.colorbox.js"></script>
@{

    if ((Model?.WhereIsColorboxForUser ?? false) == false)
    {
        if (Model.Search.WhereMyWork ?? false)
        {
            Html.RenderAction("_ArtGalleryMenu", new { NowAction = "MyWorkList" });
        }
        else
        {
            Html.RenderAction("_ArtGalleryMenu", new { NowAction = "ArtGalleryWorkList" });
        }
    }

}
@Html.HiddenFor(m => m.WhereIsColorboxForUser)
@Html.HiddenFor(m => m.WhereMyWork)
@Html.HiddenFor(m => m.Search.OrdercColumn)
@Html.HiddenFor(m => m.Search.SyntaxName)
@Html.HiddenFor(m => m.Search.Page)
@Html.HiddenFor(m => m.Search.WhereSCHOOL_NO)
@Html.HiddenFor(m => m.Search.WhereUSER_NO)
@Html.HiddenFor(m => m.Search.WhereART_GALLERY_NO)
@Html.HiddenFor(m => m.Search.WhereART_GALLERY_TYPE)
@Html.HiddenFor(m => m.Search.WhereSTATUS)
@Html.HiddenFor(m => m.Search.WhereMyWork)
@Html.HiddenFor(m => m.Search.WhereSearch)

@Html.HiddenFor(m => m.WorkSearch.OrdercColumn)
@Html.HiddenFor(m => m.WorkSearch.SyntaxName)
@Html.HiddenFor(m => m.WorkSearch.Page)
@Html.HiddenFor(m => m.IsPostBack, new { Value = "Y" })

@if ((Model?.WhereIsColorboxForUser ?? false) == false)
{
    if (string.IsNullOrWhiteSpace(Model.Search.WhereART_GALLERY_NO))
    {
        <div class="form-inline" role="form" id="Q_Div">
            <div class="form-group">
                <label class="control-label">學年</label>
            </div>
            <div class="form-group">

                @Html.DropDownListFor(m => m.Search.WhereSYEAR, (IEnumerable<SelectListItem>)ViewBag.SYEARItems, new { @class = "form-control input-sm", onchange = "FunPageProcTwo(1)" })
            </div>
            <br />
            <div class="form-group">
                <label class="control-label">學號/姓名</label>
            </div>
            <div class="form-group">
                @Html.EditorFor(m => m.Search.WhereKeyword, new { htmlAttributes = new { @class = "form-control input-sm" } })
            </div>

            <div class="form-group">
                <label class="control-label">年級</label>
            </div>
            <div class="form-group">
                @Html.DropDownListFor(m => m.Search.WhereGrade, (IEnumerable<SelectListItem>)ViewBag.GradeItem, new { @class = "form-control input-sm", onchange = "FunPageProcTwo(1)" })
            </div>
            <div class="form-group">
                <label class="control-label">班級</label>
            </div>
            <div class="form-group">
                @Html.DropDownListFor(m => m.Search.WhereCLASS_NO, (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control input-sm", onchange = "FunPageProcTwo(1)" })
            </div>
            <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProcTwo(1)" />
            <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear(true);" />
            @if (WhereMyWorkSTr == "True")
            {
                <input type="button" class="active btn btn-pink btn-sm float-right mr-5 mt-1" value="我的作品匯出" id="ExportFID" onclick="ExportFILE()" />
            }

        </div>
    }
}

@if (!string.IsNullOrWhiteSpace(Model.Search.WhereART_GALLERY_NO))
{
    <img src="~/Content/img/GalleryBook_A2-1_Ass-04.png" class="img-responsive" alt="Responsive image" style="min-width:300px;" />
}
else
{
    <img src="~/Content/img/GalleryBook_A2-1_Ass-04.png" class="img-responsive" alt="Responsive image" style="min-width:300px;" />

}
<div class="modal fade" id="myModal" role="dialog">
    <div class="modal-content">

        <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal">&times;</button>
            <h4 class="modal-title">上傳進度</h4>
        </div>
    </div>
    <div class="modal-body">
        <div class="progress progress-striped active">
            <div class="progress-bar progress-bar-success" id="barr" role="progressbar" aria-valuenow="60" aria-valuemin="0" aria-valuemax="100" style="width:0%;">

                <span class="sr-only">40% 完成</span>
            </div>
        </div>
    </div>
    <div class="modal-footer">

        <button type="button" class="btn btn-default" data-dismiss="modal">Close </button>
    </div>
</div>
<br />
<div class="Div-EZ-ArtGallery" style="min-width:288px;background-image:url('@ECOOL_APP.UrlCustomHelper.Url_Content(" ~/Content/img/GalleryBook_Asset-04.gif")');background-repeat:repeat;border-color:#a3bbdb; border-style:solid;">
    <div style="height:35px"></div>
    <div class="form-horizontal" style="display: table; margin: 0 auto;">
        <div class="row">
            @if (Model.WorkListData.Count() == 0)
            {
                <div class="col-xs-12 text-center">
                    <h3>目前尚未有作品</h3>
                </div>
            }
            @foreach (var item in Model.WorkListData)
            {
                <div class="col-md-4  col-xs-6 text-align:center;" style="height:294px">
                    <div class="element">
                        @{

                            var ImgPath = DivNormalImg;
                            if ((item.SHARE_COUNT) >= 30)
                            {
                                ImgPath = DivHotImg;
                            }

                            <div class="Div-EZ-ArtGallery-pointer-all">
                                <img src="@ImgPath" class="Div-EZ-ArtGallery-book-all" />

                                @if (item.WORK_TYPE == ADDT21.WORK_TYPE_VAL.photo)
                                {

                                    string NewImg = item.PHOTO_FILE.Replace(Path.GetExtension(item.PHOTO_FILE), "_S" + Path.GetExtension(item.PHOTO_FILE));

                                    string ImgUrl = new ZZZI34Service().GetDirectorySysArtGalleryPath(item.PHOTO_SCHOOL_NO, item.ART_GALLERY_NO, NewImg);
                                    <div class="colorboxPHOTO Div-EZ-ArtGallery-div-img-all" onclick="ShowColorbox('@item.ROWID')">
                                        <img src="@(ImgUrl+"?refreshCache=" + DateTime.Now.ToString("mmddss"))" class="Div-EZ-ArtGallery-img-all" title="@item.PHOTO_SUBJECT" alt="@item.PHOTO_SUBJECT" />
                                    </div>
                                }
                                @*else if (item.WORK_TYPE == ADDT21.WORK_TYPE_VAL.video)
                                    {
                                        string VideoUrl = new ZZZI34Service().GetDirectorySysArtGalleryPath(item.PHOTO_SCHOOL_NO, item.ART_GALLERY_NO, item.PHOTO_FILE);
                                        <div class="colorboxPHOTO Div-EZ-ArtGallery-div-img-all" style="cursor:pointer;top: 15px;left:20px; position: absolute; width: 130px;height:110px;z-index:2;text-align:center;display:table-cell;vertical-align:middle;line-height: 110px;" onclick="ShowColorbox('@item.ROWID')">
                                            <video class="Div-EZ-ArtGallery-img-all" controls>
                                                <source src="@VideoUrl" type="video/mp4" href="@Url" class="colorbox">
                                                Your browser does not support the video tag.
                                            </video>
                                        </div>
                                    }*@
                                else
                                {
                                    string ImgUrl = item.PHOTO_FILE.Replace("https://www.youtube.com/embed/", "http://img.youtube.com/vi/") + @"/mqdefault.jpg";

                                    <div class="colorboxPHOTO Div-EZ-ArtGallery-div-img-all" onclick="ShowColorbox('@item.ROWID')">
                                        <img src="@ImgUrl" class="Div-EZ-ArtGallery-img-all" title="@item.PHOTO_SUBJECT" alt="@item.PHOTO_SUBJECT" />
                                    </div>
                                }
                                @if ((item.SHARE_COUNT) >= 30)
                                {
                                    <img src="@IconHotImg" class="Div-EZ-ArtGallery-icon" />
                                }
                            </div>
                        }
                        <div class="caption">
                            <p style="text-align:center;padding-top:5px; ">
                                @if (item.STATUS == ADDT21.STATUSVal.Disabled || item.PHOTO_STATUS == ADDT22.STATUSVal.NG)
                                {
                                    if (item.STATUS == ADDT21.STATUSVal.Disabled)
                                    {
                                        <font color="red">藝廊已作廢</font>
                                    }
                                    else
                                    {
                                        <font color="red">作品已作廢</font>
                                    }
                                }
                                else
                                {
                                    if (user != null)
                                    {

                                        if ((user.USER_KEY == item.CRE_PERSON) || (user.USER_NO == item.PHOTO_USER_NO) || (item.STATUS != ADDT21.STATUSVal.NotStarted && IsAdmin))
                                        {
                                            <font color="red">(@ADDT21.STATUSVal.GetDesc(item.STATUS))</font>
                                            <samp>/</samp>
                                            <font color="red">(+@(item.PHOTO_CASH))</font>
                                            <br>
                                                    }
                                                    string str = com.ecool.service.PermissionService.GetPermission_Use_YN("ZZZI34", "Edit", user.SCHOOL_NO, user.USER_NO);
                                            
                                        if ((user.USER_KEY == item.CRE_PERSON && item.STATUS == ADDT21.STATUSVal.NotStarted)|| (user.USER_KEY == item.PHOTO_SCHOOL_NO+"_"+item.PHOTO_USER_NO && item.STATUS == ADDT21.STATUSVal.NotStarted  ) || (user.USER_KEY == item.CRE_PERSON && item.STATUS == ADDT21.STATUSVal.NotStarted) || (item.STATUS != ADDT21.STATUSVal.NotStarted && IsAdmin) || (str=="Y"&& user.USER_TYPE == "A" && user.USER_TYPE == "T"))
                                                    {

                                            <a class="btn btn-primary btn-xs" role="button" onclick="Edit_show('@item.ART_GALLERY_NO')">修改</a>
                                    }

                                }

                                    if (item.STATUS == ADDT21.STATUSVal.Pass)
                                    {
                                        if ((item.IsLikeCount) > 0)
                                        {
                                            <samp rel="tooltip" title="您已經按讚過" class="wrap poptooltip">
                                                <samp class="overlap"></samp>
                                                <a id="Link_@(item.PHOTO_NO)" class="btn btn-primary btn-xs" role="button" disabled>
                                                    <i class='glyphicon glyphicon-thumbs-up'></i> 按讚
                                                </a>
                                            </samp>
                                        }
                                        else
                                        {
                                            <a id="Link_@(item.PHOTO_NO)" class="btn btn-primary btn-xs" role="button" onclick="SHARE_show('@item.PHOTO_NO','','@item.SHARE_COUNT')">
                                                <i class='glyphicon glyphicon-thumbs-up'></i> 按讚
                                            </a>

                                        }
                                    }

                                    <strong>
                                        <a role="button" onclick="funAjaxGetLikeList('@(item.PHOTO_NO)')" style="cursor:pointer;">
                                            <font color="red" id="Font_@(item.PHOTO_NO)">

                                                @if (item.SHARE_COUNT > 0)
                                                {
                                                    <text>@(item.SHARE_COUNT) 人按讚</text>
                                                }
                                            </font>
                                        </a>
                                    </strong>

                                }
                            </p>

                            <strong style="color:#01b796">
                                @StringHelper.LeftStringR(item.ART_SUBJECT, 12)，@(item.PHOTO_CLASS_NO)

                                @if (item.AutherYN == false)
                                {

                                    @(item.PHOTO_SNAME)
                                }
                                else
                                {

                                    @:暫不顯示姓名
                                }

                            </strong>

                            <br />
                            <strong>
                                上架時間:@(item.CRE_DATE.Value.ToString("yyyy/MM/dd"))
                            </strong>
                            <br />
                            <strong>主題:<font color="#c01000">@item.PHOTO_SUBJECT</font></strong>
                            <br />
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>
</div>

<div>
    @Html.Pager(Model.WorkListData.PageSize, Model.WorkListData.PageNumber, Model.WorkListData.TotalItemCount).Options(o => o
    .DisplayTemplate(PageGlobal.BootstrapPaginationTwo)
    .MaxNrOfPages(PageGlobal.DfMaxNrOfPages)
    .SetPreviousPageText(PageGlobal.DfSetPreviousPageText)
    .SetNextPageText(PageGlobal.DfSetNextPageText)
    )
</div>
@if (!string.IsNullOrWhiteSpace(Model.Search.WhereART_GALLERY_NO))
{
    <div class="text-center">
        <a class="btn btn-default" role="button" onclick="GoBack()">返回</a>
    </div>

}

<script type="text/javascript">
    $(document).ready(function () {
      //  $(".colorboxPHOTO").colorbox({ photo: true, maxWidth: "80%", maxHeight: "80%", opacity: 0.82 });
       // $(".colorbox").colorbox({ iframe: true, width: "80%", height: "80%", opacity: 0.82 });
    });

    $('.element').responsiveEqualHeightGrid();

    var targetFormID = '#form1';

    function ShowColorbox(ROWID)
    {

        $('#@Html.IdFor(m=>m.WorkSearch.Page)').val(ROWID)

        $.ajax({
            type: 'POST',
            url: '@Url.Action("OneIndex", (string)ViewBag.BRE_NO)',
            data: $(targetFormID).serialize(),
            success: function (data) {
                $.colorbox({ html: data, width: "80%", height: "80%", opacity: 0.82 });
            }
        });
    }
</script>