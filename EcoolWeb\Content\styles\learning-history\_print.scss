@page {
  size: A4 portrait;
  padding: 0;
  margin: 0;
}

@media print {


  * {
    -webkit-print-color-adjust: exact;
  }

  body {
    width: 100%;
    height: auto;
    margin: 0;
    float: none;
    clear: both;
    line-height: 1.3;
    display: block;
    background-color: #fff;
  }

  h1 {
    font-size: 30pt;
    font-weight: bolder;
    page-break-before: always;
  }

  h2 {
    font-size: 24pt;
    page-break-after: avoid;
  }

  p {
    font-size: 18pt;
  }

  img,
  svg {
    display: inline-block;
    page-break-inside: avoid;
  }

  section {
    page-break-after: always;
  }

  // .container {
  //   width: auto;
  // }

  //封面
  .cover {
    img {
      margin-left: -1.1cm;
      width: 29.7cm;
      max-height: 40.4cm;
      position: relative;
    }
  }

  //封面及空白蝴蝶頁
  .cover,
  .print-page {
    min-height: 20cm;
  }

  //通用樣式

  .break-avoid,
  .table {
    page-break-inside: avoid;
  }

  .bg-custom-yellow {
    margin-left: 0.5cm;
    margin-right: 0.5cm;
    background-color: #fffef9 !important;
    border-color: #fdf9e2;
  }

  //---區塊樣式 

  // 自介區
  .profile {
    >img:first-child {
      margin-top: 0rem;
    }

    page-break-inside: avoid;
  }

  .bgPosition {
    margin-top: 1.5cm;

    &-profile {
      margin-top: -3.5rem;
    }
  }

  // 個人紀錄
  .record {
    .bg-custom-yellow {
      background-color: #fffcda !important;
    }

    margin-top: 20mm;

    .profilePhoto {
      width: 180px;
      height: 180px;
    }

    .table {

      th,
      td {
        background-color: transparent !important;
      }
    }

    .table-record {
      th {
        background-color: $th-bgcolor !important;
      }

      tr {
        td {
          background-color: $td-bgcolor1 !important;
          white-space: nowrap;
        }

        &:last-child>td {
          background-color: $td-bgcolor2 !important;
        }
      }
    }
  }

  // 護照、借閱圖書資料共同
  .ePassport,
  .readingBooksData,
  .healthData {
    .table {
      thead {
        th {
          background-color: $th-bgcolor !important;
        }
      }
    }

    .table-striped {
      tbody {
        tr {
          &:nth-of-type(odd)>td {
            background-color: $td-bgcolor1 !important;
          }

          &:nth-of-type(even)>td {
            background-color: $td-bgcolor2 !important;
          }
        }
      }
    }
  }

  .honorArea {
    page-break-inside: avoid;
    padding-top: 0.5cm;
  }

  // 線上投稿、閱讀認證共同
  .onlineSubmit,
  .readingCertification {
    .break-avoid+.break-avoid {
      padding-top: 8mm;
      page-break-inside: avoid !important;
    }

    .bg-custom-white {
      page-break-after: avoid !important;
    }

    .bg-custom-white+.content {

      // page-break-inside: avoid !important;
      .grid {
        page-break-inside: avoid;
      }

      p {
        page-break-inside: auto !important;
      }

      hr {
        border-top: 1pt dotted $th-color;
      }

      table {
        tr {
          font-size: 1.45rem;
        }
      }
    }

    .break-avoid:last-child {
      padding-bottom: 10mm !important;
    }
  }

  div[data-highcharts-chart] {
    transform: rotate(90deg);
    page-break-after: auto;
    page-break-inside: avoid;
    height: 290mm;

    >div {
      max-height: 100%;
      page-break-inside: avoid;
      top: 50%;
      transform: translateY(-50%);
    }
  }

  .ecoolStats {
    .bgPosition {
      page-break-inside: avoid !important;
      page-break-after: auto;
    }

    .heading-h2+div[data-highcharts-chart] {
      page-break-inside: avoid;
    }
  }

  //健康資訊
  .healthData,
  .readingBooksData {
    .table {
      page-break-inside: avoid;
    }

    .charts {
      page-break-before: always;

      .col-12 {
        display: inline-block;
        page-break-inside: avoid;
        page-break-before: always;
        page-break-after: always;
      }
    }
  }

  // // 閱讀偏食
  // .readingBooksData {
  // }

  // 線上藝廊
  .onlineGallery {
    .card {
      page-break-after: auto;
      page-break-inside: avoid;

      .card-body {
        border: 1px solid #eee;
        border-radius: 0 0 0.25rem 0.25rem;
        margin-top: -1px;
      }
    }
  }

  //圖表
  .charts {
    overflow: visible;
  }
}

//輔助不換頁
.page-break-before-auto {
  page-break-before: auto !important;
}