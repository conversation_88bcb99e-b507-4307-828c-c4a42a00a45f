﻿@using ECOOL_APP;
@{

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    var Permission = ViewBag.Permission as List<ControllerPermissionfile>;
    UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
}
<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>
<div class="form-group">


    <a href='@Url.Action("Index","AWAT14")' role="button" class="btn btn-sm btn-sys @(ViewBag.NowAction=="Index" ? "active":"")">
        說明
    </a>


    <a href='@Url.Action("SysSetinterval","AWAT14", new { ADDTList = "SysSetinterval" })' role="button" class="btn btn-sm btn-sys  @(ViewBag.NowAction=="SysSetinterval" ? "active":"")">
        參數設定
    </a>

    <a role="button" class="btn btn-sm btn-sys @(ViewBag.NowAction=="ADDTList" ? "active":"")" href="@Url.Action("ADDTList", "AWAT14")  ">
        酷幣點數排行榜
    </a>
    <a role="button" class="btn btn-sm btn-sys @(ViewBag.NowAction=="ADDTCountLog" ? "active":"")" href="@Url.Action("ADDTCountLog", "AWAT14")  ">
        各級數統計
    </a>
    <a role="button" class="btn btn-sm btn-sys @(ViewBag.NowAction=="ModifyQuery" ? "active":"")" href="@Url.Action("ModifyQuery", "AWAT14")  ">
        升級名單
    </a>




    @*<a href='@Url.Action("ADDTList","ADDT")' role="button" class="btn btn-sm btn-sys @(ViewBag.NowAction=="ADDTList" ? "active":"")">
            閱讀認證排行榜
        </a>*@
</div>

<script type="text/javascript">
    $(document).ready(function () {
        $("#btnExplain").colorbox({ width: "80%", height: "80%", opacity: 0.82 });
    });
</script>