﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace ECOOL_APP.com.ecool.Models.DTO
{
    public class ZZZI34DetailsViewModel
    {
        [DisplayName("刪除")]
        public bool Del { get; set; }

        ///Summary
        ///APPLY_NO
        ///Summary
        [DisplayName("APPLY_NO")]
        public int? APPLY_NO { get; set; }

        ///Summary
        ///SCHOOL_NO
        ///Summary
        [DisplayName("學校代碼")]
        public string SCHOOL_NO { get; set; }

        ///Summary
        ///USER_NO
        ///Summary
        [DisplayName("帳號")]
        [Required]
        public string USER_NO { get; set; }

        ///Summary
        ///CLASS_NO
        ///Summary
        [DisplayName("班級")]
        public string CLASS_NO { get; set; }

        ///Summary
        ///SYEAR
        ///Summary
        [DisplayName("學年度")]
        public byte? SYEAR { get; set; }

        ///Summary
        ///SEMESTER
        ///Summary
        [DisplayName("學期")]
        public byte? SEMESTER { get; set; }
        public bool? AutherYN { get; set; }
        ///Summary
        ///SEAT_NO
        ///Summary
        [DisplayName("座號")]
        public string SEAT_NO { get; set; }

        ///Summary
        ///NAME
        ///Summary
        [DisplayName("姓名")]
        public string NAME { get; set; }

        ///Summary
        ///SNAME
        ///Summary
        [DisplayName("姓名")]
        public string SNAME { get; set; }

        /// <summary>
        ///藝廊 NO.
        /// </summary>
        [DisplayName("藝廊 NO.")]
        public string ART_GALLERY_NO { get; set; }

        /// <summary>
        ///藝廊類別
        /// </summary>
        [DisplayName("藝廊類別")]
        public string ART_GALLERY_TYPE { get; set; }

        /// <summary>
        ///藝廊名稱
        /// </summary>
        [DisplayName("藝廊名稱")]
        public string ART_SUBJECT { get; set; }

        public string ART_DESC { get; set; }

        /// <summary>
        ///作品類別
        /// </summary>
        [DisplayName("作品類別")]
        public string WORK_TYPE { get; set; }

        /// <summary>
        ///封面
        /// </summary>
        [DisplayName("封面")]
        public string COVER_FILE { get; set; }

        public string COVER_FILE_PATH { get; set; }

        [DisplayName("CRE_DATE")]
        public DateTime? CRE_DATE { get; set; }

        ///Summary
        ///CHG_DATE
        ///Summary
        [DisplayName("CHG_DATE")]
        public DateTime? CHG_DATE { get; set; }

        ///Summary
        ///IMG_FILE
        ///Summary
        [DisplayName("IMG_FILE")]
        public string IMG_FILE { get; set; }

        public string SIMG_PATH { get; set; }
        public string MIMG_PATH { get; set; }
        public HttpPostedFileBase files;

        /// <summary>
        ///照片 NO.
        /// </summary>
        [DisplayName("照片 NO.")]
        public string PHOTO_NO { get; set; }

        /// <summary>
        ///照片作者學校代碼
        /// </summary>
        [DisplayName("照片作者學校代碼")]
        public string PHOTO_SCHOOL_NO { get; set; }

        /// <summary>
        ///照片作者帳號
        /// </summary>
        [DisplayName("照片作者帳號")]
        public string PHOTO_USER_NO { get; set; }

        /// <summary>
        ///照片作者班級
        /// </summary>
        [DisplayName("照片作者班級")]
        public string PHOTO_CLASS_NO { get; set; }

        /// <summary>
        ///照片檔名
        /// </summary>
        [DisplayName("照片檔名")]
        public string PHOTO_FILE { get; set; }

        /// <summary>
        ///照片描述
        /// </summary>
        [DisplayName("主題")]
        public string PHOTO_SUBJECT { get; set; }

        /// <summary>
        ///
        /// </summary>
        [DisplayName("內容")]
        public string PHOTO_DESC { get; set; }

        /// <summary>
        ///給予點數(照片作者)
        /// </summary>
        [DisplayName("給予點數(照片作者)")]
        public short? PHOTO_CASH { get; set; }

        public string filenames { get; set; }

        /// <summary>
        ///照片狀態
        /// </summary>
        [DisplayName("照片狀態")]
        public string PHOTO_STATUS { get; set; }

        /// <summary>
        ///照片排序
        /// </summary>
        [DisplayName("照片排序")]
        public int? PHOTO_ORDER_BY { get; set; }

        public HttpPostedFileBase PhotoFiles { get; set; }
    }
}