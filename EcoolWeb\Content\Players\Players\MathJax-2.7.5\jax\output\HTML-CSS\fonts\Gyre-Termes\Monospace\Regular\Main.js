/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Gyre-Termes/Monospace/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.GyreTermesMathJax_Monospace={directory:"Monospace/Regular",family:"GyreTermesMathJax_Monospace",testString:"\u00A0\uD835\uDE70\uD835\uDE71\uD835\uDE72\uD835\uDE73\uD835\uDE74\uD835\uDE75\uD835\uDE76\uD835\uDE77\uD835\uDE78\uD835\uDE79\uD835\uDE7A\uD835\uDE7B\uD835\uDE7C\uD835\uDE7D",32:[0,0,600,0,0],160:[0,0,600,0,0],120432:[563,0,600,9,591],120433:[563,0,600,43,541],120434:[576,16,600,63,534],120435:[563,0,600,43,520],120436:[563,0,600,43,520],120437:[563,0,600,43,520],120438:[576,16,600,63,562],120439:[563,0,600,53,551],120440:[563,0,600,113,487],120441:[563,16,600,84,583],120442:[563,0,600,43,572],120443:[563,0,600,63,541],120444:[563,0,600,11,593],120445:[563,0,600,22,562],120446:[576,16,600,51,549],120447:[563,0,600,43,499],120448:[576,115,600,51,549],120449:[563,0,600,43,589],120450:[576,16,600,92,508],120451:[563,0,600,72,528],120452:[563,16,600,40,560],120453:[563,0,600,9,591],120454:[563,0,600,20,580],120455:[563,0,600,40,560],120456:[563,0,600,51,549],120457:[563,0,600,103,497],120458:[431,16,600,72,541],120459:[604,16,600,22,541],120460:[431,16,600,84,535],120461:[604,16,600,63,583],120462:[431,16,600,63,520],120463:[604,0,600,105,541],120464:[431,186,600,63,562],120465:[604,0,600,43,551],120466:[610,0,600,92,508],120467:[610,186,600,147,458],120468:[604,0,600,63,541],120469:[604,0,600,92,508],120470:[431,0,600,11,593],120471:[431,0,600,53,541],120472:[431,16,600,72,528],120473:[431,186,600,22,541],120474:[431,186,600,63,583],120475:[427,0,600,84,541],120476:[431,16,600,103,497],120477:[563,16,600,43,499],120478:[417,16,600,43,541],120479:[417,0,600,30,570],120480:[417,0,600,30,570],120481:[417,0,600,51,549],120482:[417,186,600,51,549],120483:[417,0,600,115,489],120822:[618,15,600,113,487],120823:[612,0,600,113,487],120824:[618,0,600,84,478],120825:[618,15,600,96,499],120826:[604,0,600,105,478],120827:[604,15,600,96,499],120828:[618,15,600,136,510],120829:[604,1,600,105,478],120830:[618,15,600,113,487],120831:[618,15,600,136,510]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"GyreTermesMathJax_Monospace"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Monospace/Regular/Main.js"]);
