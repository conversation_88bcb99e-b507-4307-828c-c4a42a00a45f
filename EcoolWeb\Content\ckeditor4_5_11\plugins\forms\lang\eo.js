﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'forms', 'eo', {
	button: {
		title: 'Butonaj atributoj',
		text: '<PERSON><PERSON><PERSON> (Valoro)',
		type: 'Tipo',
		typeBtn: 'Butono',
		typeSbm: '<PERSON><PERSON><PERSON> (submit)',
		typeRst: 'Remet<PERSON> en la originstaton (Reset)'
	},
	checkboxAndRadio: {
		checkboxTitle: 'Markobutonaj Atributoj',
		radioTitle: 'Radiobutonaj Atributoj',
		value: '<PERSON><PERSON>',
		selected: 'Sele<PERSON><PERSON>',
		required: 'Postulata'
	},
	form: {
		title: 'Formularaj Atributoj',
		menu: 'Formularaj Atributoj',
		action: 'Ago',
		method: 'Metodo',
		encoding: 'Kodoprezento'
	},
	hidden: {
		title: 'Atributoj de Kaŝita Kampo',
		name: '<PERSON><PERSON>',
		value: '<PERSON><PERSON>'
	},
	select: {
		title: 'Atributoj de Elekta Kampo',
		selectInfo: 'Informoj pri la rulummenuo',
		opAvail: 'Elektoj Disponeblaj',
		value: '<PERSON><PERSON>',
		size: '<PERSON><PERSON>',
		lines: '<PERSON><PERSON>j',
		chkMulti: 'Permesi Plurajn Elektojn',
		required: 'Postulata',
		opText: 'Teksto',
		opValue: 'Valoro',
		btnAdd: 'Aldoni',
		btnModify: 'Modifi',
		btnUp: 'Supren',
		btnDown: 'Malsupren',
		btnSetValue: 'Agordi kiel Elektitan Valoron',
		btnDelete: 'Forigi'
	},
	textarea: {
		title: 'Atributoj de Teksta Areo',
		cols: 'Kolumnoj',
		rows: 'Linioj'
	},
	textfield: {
		title: 'Atributoj de Teksta Kampo',
		name: 'Nomo',
		value: 'Valoro',
		charWidth: 'Signolarĝo',
		maxChars: 'Maksimuma Nombro da Signoj',
		required: 'Postulata',
		type: 'Tipo',
		typeText: 'Teksto',
		typePass: 'Pasvorto',
		typeEmail: 'retpoŝtadreso',
		typeSearch: 'Serĉi',
		typeTel: 'Telefonnumero',
		typeUrl: 'URL'
	}
} );
