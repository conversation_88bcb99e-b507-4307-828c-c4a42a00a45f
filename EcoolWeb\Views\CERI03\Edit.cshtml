﻿@model CERI03EditViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;
}
<style type="text/css">
</style>
@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@using (Html.BeginForm("Edit", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1", @class = "form-horizontal", enctype = "multipart/form-data" }))
{

    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })
    @Html.HiddenFor(m => m.Keyword)
    @Html.HiddenFor(m => m.ACCREDITATION_ID)
    @Html.HiddenFor(m => m.ACCREDITATION_NAME)
    @Html.HiddenFor(m => m.Main.REQUIRED_ID)
    @Html.HiddenFor(m => m.Main.ACCREDITATION_ID)

    <div class="panel panel-ZZZ" name="TOP">
        <div class="panel-heading text-center">
            @Html.BarTitle(null, (string)ViewBag.Title)
        </div>
        <div class="panel-body">
            <div class="form-horizontal">
                <div class="form-group row">
                    @Html.LabelFor(m => m.Main.REQUIRED_TYPE, htmlAttributes: new { @class = "col-md-4 control-label control-label-required" })
                    <div class="col-md-8">
                        @Html.DropDownListFor(m => m.Main.REQUIRED_TYPE, (IEnumerable<SelectListItem>)ViewBag.RequiredTypeItems, new { @class = "form-control form-control-required", @onchange = "onREQUIRED_TYPE()" })
                    </div>
                    <div class="col-md-8">
                        @Html.ValidationMessageFor(m => m.Main.REQUIRED_TYPE, "", new { @class = "text-danger" })
                    </div>

                </div>

            </div>


        </div>
    </div>
    <div class="form-group row">
        <div class="col-md-12">
            說明:<br />
            <ol>
                <li>認證可以採年級、學期或不分年級。</li>
                <li>設定一年級認證，就是希望學生一年級達成這項能力。</li>
                <li>設定一年級上學期，就是希望學生一上時，達成這個能力。</li>
            </ol>
        </div>
    </div>
    <div style="margin-top:20px;margin-bottom:30px;text-align:center">
        <span class="glyphicon glyphicon-circle-arrow-down fa-2x" aria-hidden="true" style="color:#004da0"></span>
    </div>

    <div class="panel panel-ZZZ" name="TOP" id="DivGrade" style="display:none">
        <div class="panel-heading text-center">
            ※設定年級
        </div>
        <div class="panel-body">
            <div class="row">

                @if (ViewBag.SelectGradeItems != null)
                {
                    var SelectGradeItems = ViewBag.SelectGradeItems as List<SelectListItem>;

                    foreach (var item in SelectGradeItems)
                    {

                        if (string.IsNullOrWhiteSpace(item.Value))
                        {

                            <div class="col-md-12">
                                <div class="btn-group btn-group" data-toggle="buttons">
                                    <label class="btnGradeAll btn @(item.Selected ? "active":"") ">
                                        <input class="inputGradeAll" name="@Html.NameFor(m=>m.GRADEs)" id="@(Html.NameFor(m=>m.GRADEs))<EMAIL>" value="@item.Value" type="checkbox" autocomplete="off" @(item.Selected ? "checked" : "") onchange="onGradeCheckbox('DivGrade','@item.Value', this.checked)">
                                        <i class="fa fa-square-o fa-2x"></i><i class="fa fa-check-square-o fa-2x"></i>
                                        <span style="color:black"> @item.Text</span>
                                    </label>
                                </div>
                            </div>
                        }
                        else
                        {
                            <div class="col-md-4">
                                <div class="btn-group btn-group" data-toggle="buttons">
                                    <label class="btnGrade btn @(item.Selected ? "active":"") ">
                                        <input class="inputGrade" name="@Html.NameFor(m=>m.GRADEs)" id="@(Html.NameFor(m=>m.GRADEs))<EMAIL>" value="@item.Value" type="checkbox" autocomplete="off" @(item.Selected ? "checked" : "") onchange="onGradeCheckbox('DivGrade','@item.Value', this.checked)">
                                        <i class="fa fa-square-o fa-2x"></i><i class="fa fa-check-square-o fa-2x"></i>
                                        <span style="color:black"> @item.Text</span>
                                    </label>
                                </div>
                            </div>

                        }

                    }
                }
            </div>
        </div>
    </div>

    <div class="panel panel-ZZZ" name="TOP" id="DivGradeSemester" style="display:none">
        <div class="panel-heading text-center">
            ※設定年級+年級
        </div>
        <div class="panel-body">
            <div class="row">

                @if (ViewBag.SelectGradeItems != null)
                {
                    var SelectGradeSemesterItems = ViewBag.SelectGradeSemesterItems as List<SelectListItem>;

                    foreach (var item in SelectGradeSemesterItems)
                    {

                        if (string.IsNullOrWhiteSpace(item.Value))
                        {

                            <div class="col-md-12">
                                <div class="btn-group btn-group" data-toggle="buttons">
                                    <label class="btnGradeAll btn @(item.Selected ? "active":"") ">
                                        <input class="inputGradeAll" name="@Html.NameFor(m=>m.GRADE_SEMESTERs)" id="@(Html.NameFor(m=>m.GRADE_SEMESTERs))<EMAIL>" value="@item.Value" type="checkbox" autocomplete="off" @(item.Selected ? "checked" : "") onchange="onGradeCheckbox('DivGradeSemester','@item.Value', this.checked)">
                                        <i class="fa fa-square-o fa-2x"></i><i class="fa fa-check-square-o fa-2x"></i>
                                        <span style="color:black"> @item.Text</span>
                                    </label>
                                </div>
                            </div>
                        }
                        else
                        {
                            <div class="col-md-6">
                                <div class="btn-group btn-group" data-toggle="buttons">
                                    <label class="btnGrade btn @(item.Selected ? "active":"") ">
                                        <input class="inputGrade" name="@Html.NameFor(m=>m.GRADE_SEMESTERs)" id="@(Html.NameFor(m=>m.GRADE_SEMESTERs))<EMAIL>" value="@item.Value" type="checkbox" autocomplete="off" @(item.Selected ? "checked" : "") onchange="onGradeCheckbox('DivGradeSemester','@item.Value', this.checked)">
                                        <i class="fa fa-square-o fa-2x"></i><i class="fa fa-check-square-o fa-2x"></i>
                                        <span style="color:black"> @item.Text</span>
                                    </label>
                                </div>
                            </div>

                        }

                    }
                }
            </div>
        </div>
    </div>

    <div class="text-center">
        <hr />
        <button type="button" id="clearform" class="btn btn-default" onclick="onBack()">
            取消
        </button>
        <button type="button" class="btn btn-default" onclick="onSave()">
            <span class="fa fa-check-circle" aria-hidden="true"></span>儲存
        </button>
        @if (Model.Main?.REQUIRED_ID != null)
        {
            <button type="button" class="btn btn-default" onclick="onDel()">
                <span class="fa fa-trash" aria-hidden="true"></span>作廢
            </button>
        }
    </div>
}

@section Scripts {

    <script language="JavaScript">

        var targetFormID = '#form1';

        window.onload = function () {
            onREQUIRED_TYPE();
        }

        function onSave()
        {

            $(targetFormID).attr("action", "@Url.Action("EditSave", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        function onDel()
        {
            $(targetFormID).attr("action", "@Url.Action("Del", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        function onBack() {
            $(targetFormID).attr("action", "@Url.Action("Index", "CERI02")")
            $(targetFormID).submit();
        }

        function onREQUIRED_TYPE() {

            var REQUIRED_TYPE = $('#@Html.IdFor(m=>m.Main.REQUIRED_TYPE)').val();

            if (REQUIRED_TYPE=='@((byte)CERT03.RequiredTypeVal.GRADE_SEMESTER)') {
                $('#DivGradeSemester').show();
                $('#DivGrade').hide();
            }
            else {
                $('#DivGrade').show();
                $('#DivGradeSemester').hide();
            }
        }

        function onGradeCheckbox(DivId,Value,checked) {
            if (checked) {
                if (Value=='') {
                    $('#'+DivId+' .inputGrade').prop('checked', true);
                    $('#'+DivId+' .btnGrade').addClass("active")
                }
                else {
                    $('#'+DivId+' .inputGradeAll').prop('checked', false);
                    $('#'+DivId+' .btnGradeAll').removeClass("active");
                }
            }
            else {
                if (checked == false) {
                  if (Value=='') {
                        $('#'+DivId+' .inputGrade').prop('checked', false);
                        $('#'+DivId+' .btnGrade').removeClass("active")
                  }
                  else {
                     $('#'+DivId+' .inputGradeAll').prop('checked', false);
                     $('#'+DivId+' .btnGradeAll').removeClass("active");
                   }
                }
            }
        }
    </script>
}