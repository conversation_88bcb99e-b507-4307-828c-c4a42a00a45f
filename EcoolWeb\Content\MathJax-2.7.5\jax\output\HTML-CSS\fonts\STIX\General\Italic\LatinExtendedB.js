/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/General/Italic/LatinExtendedB.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXGeneral-italic"],{384:[683,11,500,23,473],392:[548,11,500,30,577],400:[684,6,667,66,671],402:[706,159,472,-62,494],405:[683,10,672,19,654],409:[683,11,500,14,490],410:[683,11,278,41,279],411:[668,0,490,30,478],414:[441,233,500,14,442],416:[691,18,722,60,783],417:[467,11,534,27,583],421:[669,205,504,-75,472],426:[685,233,340,31,319],427:[546,218,278,-54,296],429:[683,11,310,38,452],431:[765,18,754,102,881],432:[543,11,573,42,607],442:[450,234,500,8,462],443:[676,0,500,12,500],446:[539,12,500,47,453],448:[736,0,170,15,258],449:[736,0,290,15,379],450:[736,0,340,15,429],451:[667,11,333,39,304],496:[661,207,278,-124,397],506:[950,0,611,-51,564],507:[860,11,501,17,476],508:[876,0,889,-27,911],509:[664,11,667,23,640],510:[876,105,722,60,699],511:[664,135,500,28,469]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/Italic/LatinExtendedB.js");
