﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'table', 'gl', {
	border: 'Tamaño do bordo',
	caption: 'T<PERSON>tu<PERSON>',
	cell: {
		menu: 'Cela',
		insertBefore: 'Inserir a cela á esquerda',
		insertAfter: 'Inserir a cela á dereita',
		deleteCell: 'Eliminar celas',
		merge: 'Combinar celas',
		mergeRight: 'Combinar á dereita',
		mergeDown: 'Combinar cara abaixo',
		splitHorizontal: 'Dividir a cela en horizontal',
		splitVertical: 'Dividir a cela en vertical',
		title: 'Propiedades da cela',
		cellType: 'Tipo de cela',
		rowSpan: 'Expandir filas',
		colSpan: 'Expandir columnas',
		wordWrap: 'Axustar ao contido',
		hAlign: 'Aliñación horizontal',
		vAlign: 'Aliñación vertical',
		alignBaseline: 'Liña de base',
		bgColor: 'Cor do fondo',
		borderColor: 'Cor do bordo',
		data: 'Da<PERSON>',
		header: '<PERSON><PERSON><PERSON><PERSON>',
		yes: 'Si',
		no: 'Non',
		invalidWidth: 'O largo da cela debe ser un número.',
		invalidHeight: 'O alto da cela debe ser un número.',
		invalidRowSpan: 'A expansión de filas debe ser un número enteiro.',
		invalidColSpan: 'A expansión de columnas debe ser un número enteiro.',
		chooseColor: 'Escoller'
	},
	cellPad: 'Marxe interior da cela',
	cellSpace: 'Marxe entre celas',
	column: {
		menu: 'Columna',
		insertBefore: 'Inserir a columna á esquerda',
		insertAfter: 'Inserir a columna á dereita',
		deleteColumn: 'Borrar Columnas'
	},
	columns: 'Columnas',
	deleteTable: 'Borrar Táboa',
	headers: 'Cabeceiras',
	headersBoth: 'Ambas',
	headersColumn: 'Primeira columna',
	headersNone: 'Ningún',
	headersRow: 'Primeira fila',
	invalidBorder: 'O tamaño do bordo debe ser un número.',
	invalidCellPadding: 'A marxe interior debe ser un número positivo.',
	invalidCellSpacing: 'A marxe entre celas debe ser un número positivo.',
	invalidCols: 'O número de columnas debe ser un número maior que 0.',
	invalidHeight: 'O alto da táboa debe ser un número.',
	invalidRows: 'O número de filas debe ser un número maior que 0',
	invalidWidth: 'O largo da táboa debe ser un número.',
	menu: 'Propiedades da táboa',
	row: {
		menu: 'Fila',
		insertBefore: 'Inserir a fila por riba',
		insertAfter: 'Inserir a fila por baixo',
		deleteRow: 'Eliminar filas'
	},
	rows: 'Filas',
	summary: 'Resumo',
	title: 'Propiedades da táboa',
	toolbar: 'Taboa',
	widthPc: 'porcentaxe',
	widthPx: 'píxeles',
	widthUnit: 'unidade do largo'
} );
