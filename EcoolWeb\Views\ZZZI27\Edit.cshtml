﻿@model ZZZI27EditViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;
}
<link href="~/Content/css/EzCss.css" rel="stylesheet" />

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@using (Html.BeginForm("Edit", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", id = "form1", enctype = "multipart/form-data" }))
{
    @Html.AntiForgeryToken()

    @Html.HiddenFor(m => m.Search.wBRE_NO)
    @Html.HiddenFor(m => m.Search.wDATA_CODE)
    @Html.HiddenFor(m => m.IsLoadingDefault)

    <div class="form-inline" role="form">
        <div class="form-group">
            <label class="control-label">學校</label>
        </div>
        <div class="form-group">
            @Html.DropDownListFor(m => m.Search.wSCHOOL_NO, (IEnumerable<SelectListItem>)ViewBag.SchoolNoSelectItem, new { @class = "form-control", onchange = "form1.submit();" })
        </div>
    </div>
    <br />
    var checkBoxBDMT02 = new List<CheckBoxListInfo>();
    CheckBoxListInfo checkBoxBDMT02Item = new CheckBoxListInfo();
    checkBoxBDMT02Item.DisplayText = "關閉跳出公告";
    checkBoxBDMT02Item.IsChecked = false;
    if (Model.T_BDMT02.Close_YN != null && Model.T_BDMT02.Close_YN != "Y")
    {
        checkBoxBDMT02Item.Value = "Y";
    }
    else
    {
        checkBoxBDMT02Item.Value = "N";
    }
    checkBoxBDMT02Item.IsChecked = Model.T_BDMT02.Close_YN == "Y" ? true : false;
    checkBoxBDMT02.Add(checkBoxBDMT02Item);
    var htmlAttributeCHRIS = new Dictionary<string, object>();
    htmlAttributeCHRIS.Add("id", "T_BDMT02.Close_YN");
    <div class="form-horizontal">
        <div class="panel panel-ACC">
            <div class="panel-heading text-center">
                <h3 class="panel-title">@ViewBag.Panel_Title</h3>
            </div>
            <div class="panel-body">
                <div class="form-group">
                    @Html.ValidationMessage("DetailsError", new { @class = "text-danger" })
                  
                    @*<div style="height:15px"></div>*@
                    <div class="Caption_Div">
                        @Model.T_BDMT02.DATA_DESC

                        @if (Model.T_BDMT02.BRE_NO == "BarcCodeMyCash")
                        {

                            <br />

                            <B style="color:red">

                                說明:
                                <br />(1)當ATM網頁沒有操作的時候，網頁會自動轉到設定的排行榜。
                                <br />(2)0代表不設定此功能。
                                <br />(3)最小設定為0.1(分鐘)，建議至少設定3分鐘以上。
                            </B>
                        }
                        @if (!string.IsNullOrWhiteSpace(Model.T_BDMT02.MEMO))
                        {
                            @*<samp>(@Model.T_BDMT02.MEMO)</samp>*@
                            if (Model.T_BDMT02.DATA_CODE == "TeacherIndex")
                            {
                                <samp>
                                    @Html.CheckBoxList("T_BDMT02.Close_YN", (List<CheckBoxListInfo>)checkBoxBDMT02, htmlAttributeCHRIS, 1)
                                </samp>}
                        }
                    </div>
                    <div style="height:20px;width:100%;"></div>

                    @if ((Model.Details_List == null || Model.Details_List.Count() == 0) && Model.IsLoadingDefault == false && Model.Search.wSCHOOL_NO != SharedGlobal.ALL)
                    {
                        <div class="text-center" style="padding-left:20px">
                            <h3>貴學校未設定此參數</h3>
                        </div>
                        <div class="text-center">
                            <button class="btn btn-default" type="button" onclick="onLoadingVal()">開始設定</button>
                        </div>
                    }
                    else
                    {
                        <div class="table-responsive" style="width:100%;">
                            <div class="css-table" style="width:100%;">
                                <div class="tr" style=" font-weight: bold;text-align:center;color:#004da0;text-overflow:ellipsis; ">


                                    @if (Model.T_BDMT02.TXT_SCHOOL_SET_YN == SharedGlobal.Y)
                                    {
                                        if (Model.T_BDMT02.DATA_CODE == "TeacherIndex")
                                        {<div class="th" style="text-align:center">
                                            </div>
                                            <div class="th" style="text-align:left;padding-left:50px">
                                                標題
                                            </div>

                                        }
                                        else if (Model.T_BDMT02.BRE_NO == "BarcCodeMyCash")
                                        {
                                            <div class="th" style="text-align:center">

                                            </div>

                                        }
                                        else if (Model.T_BDMT02.ADD_MODE == BDMT02_ENUM.AddMode.Fixed || Model.T_BDMT02.ADD_MODE == BDMT02_ENUM.AddMode.Multi)
                                        {
                                            <div class="th" style="text-align:center">
                                                
                                            </div>
                                            <div class="th" style="text-align:center">
                                                內容
                                            </div>
                                        }
                                        else
                                        {
                                            <div class="th" style="text-align:center">
                                                內容
                                            </div>
                                            <div class="th" style="text-align:center">

                                            </div>
                                        }

                                    }
                                    @if (Model.T_BDMT02.VAL_SCHOOL_SET_YN == SharedGlobal.Y)
                                    {
                                        <div class="th" style="text-align:center">
                                            值
                                        </div>
                                    }

                                    @if (Model.T_BDMT02.ADD_MODE == BDMT02_ENUM.AddMode.Fixed)
                                    {
                                        <div class="th" style="text-align:left">
                                            說明
                                        </div>
                                    }
                                </div>
                                @if (Model.T_BDMT02.DATA_CODE == "TeacherIndex")
                                {
                                    <div id="editorRows1" class="tbody">
                                        @if (Model.Details_List.Count() > 0 && Model.Details_List != null)
                                        {

                                            @Html.Partial("_REF", Model.Details_List.FirstOrDefault())

                                        }
                                    </div>

                                    <div class="tr" style=" font-weight: bold;text-align:center;color:#004da0;text-overflow:ellipsis; ">

                                        <div class="th" style="text-align:center">

                                        </div>
                                        <div class="th" style="text-align:left ;padding-left:50px">
                                            內容
                                        </div>
                                    </div>

                                    <div id="editorRows" class="tbody">
                                        @if (Model.Details_List.Count() > 0 && Model.Details_List != null)
                                        {
                                            foreach (var Item in Model.Details_List.Skip(1))
                                            {

                                                @Html.Partial("_REF", Item)
                                            }
                                        }
                                    </div>
                                }
                                else if (Model.T_BDMT02.BRE_NO == "BarcCodeMyCash")
                                {
                                    <div id="editorRows1" class="tbody">
                                        @if (Model.Details_List != null)
                                        {

                                            @Html.Partial("_ATMREF", Model.Details_List)







                                        }
                                    </div>

                                }
                                else if (Model.T_BDMT02.ADD_MODE == BDMT02_ENUM.AddMode.Check)
                                {
                                    <div id="editorRows1" class="tbody">
                                        @if (Model.Details_List != null)
                                        {
                                            foreach (var Item in Model.Details_List)
                                            {

                                                @Html.Partial("_REF1", Item)
                                            }
                                        }
                                    </div>

                                }


                                else if (Model.T_BDMT02.ADD_MODE == BDMT02_ENUM.AddMode.Check)
                                {
                                    <div id="editorRows1" class="tbody">
                                        @if (Model.Details_List != null)
                                        {
                                            foreach (var Item in Model.Details_List)
                                            {

                                                @Html.Partial("_REF1", Item)
                                            }
                                        }
                                    </div>

                                }
                                else
                                {
                                    <div id="editorRows" class="tbody">
                                        @if (Model.Details_List != null)
                                        {
                                            foreach (var Item in Model.Details_List)
                                            {

                                                @Html.Partial("_REF", Item)
                                            }
                                        }
                                    </div>
                                }
                            </div>
                        </div>
                        if (Model.T_BDMT02.ADD_MODE == BDMT02_ENUM.AddMode.Multi && Model.T_BDMT02.BRE_NO != "BarcCodeMyCash")
                        {
                            <div style="height:25px" ></div>
                            <div class="row">
                                <div class="col-md-12 col-xs-12 text-right" style="padding-left:90px;padding-top:5px">
                                    <span class="input-group-btn">
                                        <button class="btn btn-default btn-sm" type="button" onclick="onAddItem()">
                                            增加明細
                                        </button>
                                    </span>
                                </div>
                            </div>
                        }
                        if (Model.T_BDMT02.ADD_MODE == BDMT02_ENUM.AddMode.Check && Model.T_BDMT02.BRE_NO != "BarcCodeMyCash")
                        {
                            if (Model.Details_List.Count() == 0)
                            {
                                <div style="height:25px"></div>
                                <div class="row">
                                    <div class="col-md-12 col-xs-12 text-right">
                                        <span class="input-group-btn">
                                            <button class="btn btn-default btn-sm" id="checkbutton" type="button" onclick="onAddItem1()">
                                                選項
                                            </button>
                                        </span>
                                    </div>
                                </div>}


                        }
                        if (Model.T_BDMT02.DATA_CODE == "TeacherIndex" && Model.T_BDMT02.BRE_NO != "BarcCodeMyCash")
                        {

                            <div style="padding:22px">
                                註明：<br />
                                1. 總召學校所設定的公告，只有總召學校可以關閉。<br />
                                2. 文字內容每行建議不要超過60個字<br />
                            </div>

                        }
                    }
                </div>
            </div>
        </div>
    </div>

    <div style="height:25px"></div>
    <div class="text-center" style="margin-left:10px">
        <button id="BtnSave" class="btn btn-default" type="button" onclick="onSave()">儲存</button>

        @*@if (Model.Details_List != null && Model.Details_List.Count() > 1 && Model.IsLoadingDefault == false && Model.Search.wSCHOOL_NO != SharedGlobal.ALL)
            {
                <button class="btn btn-default" type="button" onclick="onDel()">整筆刪除</button>
            }*@
        <button class="btn btn-default" type="button" onclick="onBack()">回【選擇功能】</button>
    </div>
}

@section Scripts {
    <script language="JavaScript">
        var targetFormID = '#form1';

        function onDel() {
            $(targetFormID).attr("action", "@Url.Action("EditDel", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        function onSave() {

            $(targetFormID).attr("action", "@Url.Action("EditSave", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        function onLoadingVal()
        {
            $('#@Html.IdFor(m=>m.IsLoadingDefault)').val(true)
           $(targetFormID).attr("action", "@Url.Action("Edit", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

       function onBack() {
            $(targetFormID).attr("action", "@Url.Action("Index", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }
        function onAddItem1() {
            $("#checkbutton").attr("style","display:none")
            var data = {
                "BRE_NO": '@Model.T_BDMT02.BRE_NO',
                "DATA_CODE": '@Model.T_BDMT02.DATA_CODE',
                "ADD_MODE": '@Model.T_BDMT02.ADD_MODE',
                "TXT_MODE": '@Model.T_BDMT02.TXT_MODE',
                "VAL_MODE": '@Model.T_BDMT02.VAL_MODE',
                "TXT_SCHOOL_SET_YN": '@Model.T_BDMT02.TXT_SCHOOL_SET_YN',
                "VAL_SCHOOL_SET_YN":'@Model.T_BDMT02.VAL_SCHOOL_SET_YN',
             };

            $.ajax({
                url: '@Url.Action("_REF1")',
                data: data,
                cache: false,
                success: function (html) {
                    $("#editorRows1").append(html);
                }
            });
        }
         function onAddItem() {

            var data = {
                "BRE_NO": '@Model.T_BDMT02.BRE_NO',
                "DATA_CODE": '@Model.T_BDMT02.DATA_CODE',
                "ADD_MODE": '@Model.T_BDMT02.ADD_MODE',
                "TXT_MODE": '@Model.T_BDMT02.TXT_MODE',
                "VAL_MODE": '@Model.T_BDMT02.VAL_MODE',
                "TXT_SCHOOL_SET_YN": '@Model.T_BDMT02.TXT_SCHOOL_SET_YN',
                "VAL_SCHOOL_SET_YN":'@Model.T_BDMT02.VAL_SCHOOL_SET_YN',
             };

            $.ajax({
                url: '@Url.Action("_REF")',
                data: data,
                cache: false,
                success: function (html) {
                    $("#editorRows").append(html);
                }
            });
        }

         //del 項目
         function deleteRow(Id) {
             $('#' + Id).remove();
         }
    </script>
}