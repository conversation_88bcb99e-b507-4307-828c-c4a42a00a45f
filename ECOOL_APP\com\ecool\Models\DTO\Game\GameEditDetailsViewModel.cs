﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace ECOOL_APP.EF
{
    public class GameEditDetailsViewModel
    {
        /// <summary>
        ///關卡id
        /// </summary>
        [DisplayName("關卡id")]
        public string LEVEL_NO { get; set; }

        /// <summary>
        /// 是否報名
        /// </summary>
        public bool IsApply { get; set; }

        /// <summary>
        /// 可對兌實體獎品關卡
        /// </summary>
        public bool IsAgainst { get; set; }

        /// <summary>
        /// 可抽獎獎品關卡
        /// </summary>
        public bool IsCouppons { get; set; }

        /// <summary>
        ///關卡類別
        /// </summary>
        [DisplayName("關卡類別")]
        public string LEVEL_TYPE { get; set; }

        /// <summary>
        ///關卡序順
        /// </summary>
        [DisplayName("關卡序順")]
        public string LEVEL_ITEM { get; set; }

        /// <summary>
        ///活動id
        /// </summary>
        [DisplayName("活動id")]
        public string GAME_NO { get; set; }

        /// <summary>
        ///關卡名稱
        /// </summary>
        [DisplayName("關卡名稱")]
        public string LEVEL_NAME { get; set; }

        /// <summary>
        ///關卡圖片
        /// </summary>
        [DisplayName("關卡圖片")]
        public string LEVEL_IMG { get; set; }

        public string LEVEL_IMG_PATH { get; set; }
        public string Coupons_ITem { get; set; }

        /// <summary>
        ///關卡說明
        /// </summary>
        [DisplayName("關卡說明")]
        public string LEVEL_DESC { get; set; }

        /// <summary>
        /// 可重複闖關
        /// </summary>
        [DisplayName("可重複闖關")]
        public bool Y_REPEAT { get; set; }
        public bool Y_CASH { get; set; }
        public bool Y_Photo { get; set; }
        /// <summary>
        ///酷幣點數
        /// </summary>
        [DisplayName("酷幣點數")]
        public int? CASH { get; set; }

        /// <summary>
        ///修改人
        /// </summary>
        [DisplayName("修改人")]
        public string CHG_PERSON { get; set; }

        /// <summary>
        /// 過場動畫秒數
        /// </summary>
        [DisplayName("過場動畫秒數")]
        public int? LOADING_TIME { get; set; }

        /// <summary>
        /// 結果停留時間(秒)
        /// </summary>
        [DisplayName("結果停留時間(秒)")]
        public int? PASSED_TIME { get; set; }

        /// <summary>
        ///修改日
        /// </summary>
        [DisplayName("修改日")]
        public DateTime? CHG_DATE { get; set; }

        public HttpPostedFileBase PhotoFiles { get; set; }

        /// <summary>
        ///題目序號
        /// </summary>
        [DisplayName("題目序號")]
        public string GROUP_ID { get; set; }

        /// <summary>
        ///回饋字串
        /// </summary>
        [DisplayName("回饋字串")]
        public string RETURN_DESC { get; set; }

        /// <summary>
        ///正確答案
        /// </summary>
        [DisplayName("正確答案")]
        public bool? TRUE_ANS { get; set; }

        /// <summary>
        /// 第幾題 for 有獎徵答用
        /// </summary>
        public int G_ORDER_BY { get; set; }

        /// <summary>
        /// 題目  for 有獎徵答用
        /// </summary>
        public string G_SUBJECT { get; set; }
    }
}