﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace com.ecool.service
{
    public class UserService : ServiceBase
    {
        /// <summary>
        /// 登入
        /// </summary>
        /// <param name="USERID">使用者帳號</param>
        /// <param name="USERPWD">使用者密碼</param>
        public List<Hashtable> USP_USER_LOGIN(string USERID, string USERPWD)
        {
            List<Hashtable> list_data = new List<Hashtable>();
            try
            {
                //list_data = USP_USER_LOGIN(USERID.Trim(), USERPWD.Trim());
            }
            catch (Exception exception)
            {
                throw exception;
            }
            return list_data;
        }
    }
}
