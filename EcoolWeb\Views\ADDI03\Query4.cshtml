﻿@using ECOOL_APP.com.ecool.Models.entity;
@using System.Collections;
@model  IEnumerable<uADDT03>
@{
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();


    ViewBag.Title = "閱讀護照-我的護照";

    if (user!=null)
    {
        if (user.USER_TYPE == UserType.Parents)
        {
            ViewBag.Title = "閱讀護照-寶貝護照";
        }
    }

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    if (string.IsNullOrWhiteSpace(UUID) == false)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
    string CGRADE = string.Empty;
    string sStatus = string.Empty;

    string ddlCLASS_NO = string.Empty;
    string ddlGrade = string.Empty;
    string whereKeyword = string.Empty;

    if (user != null)
    {
        ddlCLASS_NO = user.TEACH_CLASS_NO ?? user.CLASS_NO;
        ddlGrade = string.IsNullOrWhiteSpace(ddlCLASS_NO) == false ? new ECOOL_APP.com.ecool.util.StringHelper().StrLeft(ddlCLASS_NO, 1) : "";

        if (user.USER_TYPE == UserType.Student)
        {
            whereKeyword = user.USER_NO;
        }
    }

    int WhereGRADE = string.IsNullOrWhiteSpace(ddlGrade) == false ? Convert.ToInt32(ddlGrade) : 1;
}

@Html.Partial("_Title_Secondary")


<a role="button" href='@Url.Action("rpp", "rpp")' class="btn btn-sm btn-sys ">
    護照說明
</a>

<a href='@Url.Action("rpp_book", "rpp")' role="button" class="btn btn-sm btn-sys">
    閱讀書單
</a>


@Html.ActionLink("本學年護照", "Query3", "ADDI03", new { GRADE = WhereGRADE, QShowYN = "N" }, new { @role = "button", @class = "btn btn-sm btn-sys" })
@Html.ActionLink("護照現況一覽表", "Query3", "ADDI03", new { GRADE = WhereGRADE, whereKeyword = whereKeyword }, new { @role = "button", @class = "btn btn-sm btn-sys " })



<a href='@Url.Action("Query2", "ADDI03",new { ddlGrade= ddlGrade, ddlCLASS_NO= ddlCLASS_NO,whereKeyword=whereKeyword })' role="button" class="btn btn-sm btn-sys">
    護照完成一覽表
</a>

@if (user != null)
{
    if (user.USER_TYPE == UserType.Student)
    {
                <a role="button" href='@Url.Action("Query4", "ADDI03")' class="btn btn-sm btn-sys active">
                    我的護照
                </a>
    }
    else if (user.USER_TYPE == UserType.Parents)
    {
        <a role="button" href='@Url.Action("Query4", "ADDI03")' class="btn btn-sm btn-sys">
            寶貝護照
        </a>
    }
}

@using (Html.BeginForm("Query4", "ADDI03", FormMethod.Post, new { id = "ADDI01", name = "form1" }))
{
    if (user.USER_TYPE == UserType.Parents)
    {
        <br />
        <div class="form-inline" role="form">
            <div class="form-group">
                <label class="control-label">寶貝姓名</label>
            </div>
            <div class="form-group">
                @Html.DropDownList("USER_NO", (IEnumerable<SelectListItem>)ViewBag.MyPanyStudentItems, new { @class = "form-control", @onchange = "form1.submit();" })
            </div>
        </div>
        <br />
    }
}


<img src="~/Content/img/web-bar2-revise-30.png" style="width:100%" class="img-responsive App_hide" alt="Responsive image" />

<div class="table-responsive">
    <div class="text-center">

        <table class="table-ecool table-92Per table-hover table-ecool-rpp">
            <thead>
                <tr>
                    <td>閱讀年級</td>
                    <td>書本編號</td>
                    <td>書名</td>
                    <td>閱讀狀態</td>
                </tr>
            </thead>
    
            @foreach (var item in Model)
            {
                string bkColor = string.Empty;
                switch (item.GRADE)
                {
                    case "1":
                        CGRADE = "一年級";
                        bkColor = "#E6F1CC";
                        break;
                    case "2":
                        CGRADE = "二年級";
                        bkColor = "#FFF8E0";
                        break;
                    case "3":
                        CGRADE = "三年級";
                        bkColor = "#E8E2EF";
                        break;
                    case "4":
                        CGRADE = "四年級";
                        bkColor = "#ECFFFF";
                        break;
                    case "5":
                        CGRADE = "五年級";
                        bkColor = "#FFF2D5";
                        break;
                    case "6":
                        CGRADE = "六年級";
                        bkColor = "#FDEAF2";
                        break;
                }
                <tr bgcolor=@bkColor>
                    <td align="center" >
                            @CGRADE
                    </td>
                    <td align="center">
                            @item.BOOK_ID
                    </td>
                    <td  style="text-align: left;white-space:normal">
                            @item.BOOK_NAME
                    </td>

                  @if (item.Read_STATUS == "Y")
                  {
                        <td align="center" stye="font-weight bold;color:blue" bgcolor=@bkColor>
                            <font>
                                已讀
                            </font>
                        </td>
                    }
                    else
                    {
                        <td align="center" bgcolor=@bkColor>
                            <font>
                                @Html.ActionLink("未讀", "ADDTList_apply", new { Controller = "ADDT", BookID = item.BOOK_ID, BookStatus = "ALL", }, new { @style = "color:red;", @target = "view_window" })
                            </font>
                        </td>
                    }
                </tr>
            }
        </table>
    </div>
</div>



 

