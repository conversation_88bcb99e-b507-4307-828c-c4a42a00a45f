﻿using Dapper;
using ECOOL_APP.com.ecool.Models.DTO;
using ECOOL_APP.EF;
using MvcPaging;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;

namespace ECOOL_APP.com.ecool.service
{
    public class SECI03Service
    {
        public IEnumerable<SECI03Hrmt08ListViewModel> GetMyHrmt08Data(string IDNO, string schoolno, ref ECOOL_DEVEntities db)
        {
            string sSQL = @" select GRADE_SEMESTER = t.GRADE + CASE WHEN t.SEMESTER = 1 Then '上' Else '下' END
                                ,t.*
                                ,isnull(b.AVG_TALL,0) as AVG_TALL_M,isnull(b.AVG_WEIGHT,0) as AVG_WEIGHT_M,isnull(bs.SUM_AVG_TALL_M,0) as SUM_AVG_TALL_M,isnull(bs.SUM_AVG_WEIGHT_M,0) as SUM_AVG_WEIGHT_M
								,isnull(g.AVG_TALL,0) as AVG_TALL_W,isnull(g.AVG_WEIGHT,0) as AVG_WEIGHT_W,isnull(gs.SUM_AVG_TALL_W,0) as SUM_AVG_TALL_W,isnull(gs.SUM_AVG_WEIGHT_W,0) as SUM_AVG_WEIGHT_W
                                ,c.VISION_RIGHT_08,c.VISION_RIGHT_09,c.VISION_RIGHT_12
								,c.VISION_LEFT_08,c.VISION_LEFT_09,c.VISION_LEFT_12
                               ,round(Case When t.BMI is not null  Then t.BMI Else (t.WEIGHT)*1.0/((t.TALL/100)*(TALL/100))  End,1) as ShowBMI
                                from HRMT08 t (NOLOCK)
                                 left outer join (Select s.SCHOOL_NO,s.GRADE,s.SYEAR ,s.SEMESTER
	                                        ,round(AVG(s.TALL), 1) as AVG_TALL
	                                         ,round(AVG(s.WEIGHT), 1) as AVG_WEIGHT
                                             from HRMT08 s (NOLOCK)
                                             inner join (Select IDNO,SEX from DB2_STUDENT (nolock)  group by  IDNO,SEX ) a on s.IDNO = a.IDNO and a.SEX=1
                                             group by s.SCHOOL_NO,s.GRADE,s.SYEAR ,s.SEMESTER
                                ) as b on t.SCHOOL_NO = b.SCHOOL_NO and t.GRADE = b.GRADE and t.SYEAR = b.SYEAR and t.SEMESTER = b.SEMESTER
								left outer join  (Select s.SCHOOL_NO,s.GRADE,s.SYEAR ,s.SEMESTER
	                                        ,round(AVG(s.TALL), 1) as AVG_TALL
	                                         ,round(AVG(s.WEIGHT), 1) as AVG_WEIGHT
                                             from HRMT08 s (NOLOCK)
                                             inner join (Select IDNO,SEX from DB2_STUDENT (nolock)  group by  IDNO,SEX ) a  on s.IDNO = a.IDNO and a.SEX=0
                                             group by s.SCHOOL_NO,s.GRADE,s.SYEAR ,s.SEMESTER
                                ) as g on t.SCHOOL_NO = g.SCHOOL_NO and t.GRADE = g.GRADE and t.SYEAR = g.SYEAR and t.SEMESTER = g.SEMESTER
                                left outer join (Select s.SCHOOL_NO,s.GRADE ,s.SEMESTER
	                                             ,round(AVG(s.TALL), 1) as SUM_AVG_TALL_M
	                                             ,round(AVG(s.WEIGHT), 1) as SUM_AVG_WEIGHT_M
                                             from HRMT08 s (NOLOCK)
                                             inner join (Select IDNO,SEX from DB2_STUDENT (nolock)  group by  IDNO,SEX ) a  on s.IDNO = a.IDNO  and a.SEX=1
                                             group by s.SCHOOL_NO,s.GRADE,s.SEMESTER
                                ) as bs on t.SCHOOL_NO = bs.SCHOOL_NO and t.GRADE = bs.GRADE and t.SEMESTER = bs.SEMESTER
								 left outer join (Select s.SCHOOL_NO,s.GRADE ,s.SEMESTER
	                                             ,round(AVG(s.TALL), 1) as SUM_AVG_TALL_W
	                                             ,round(AVG(s.WEIGHT), 1) as SUM_AVG_WEIGHT_W
                                             from HRMT08 s (NOLOCK)
                                             inner join (Select IDNO,SEX from DB2_STUDENT (nolock)  group by  IDNO,SEX ) a  on s.IDNO = a.IDNO  and a.SEX=0
                                             group by s.SCHOOL_NO,s.GRADE,s.SEMESTER
                                ) as gs on t.SCHOOL_NO = gs.SCHOOL_NO and t.GRADE = gs.GRADE and t.SEMESTER = gs.SEMESTER
	                            inner  join (
										Select s.SCHOOL_NO,s.GRADE,s.SYEAR ,s.SEMESTER
                                        , sum( case when CAST(s.VISION_RIGHT as float) <= 0.8 Then 1 else 0 end ) as VISION_RIGHT_08
                                        , sum( case when CAST(s.VISION_RIGHT as float) >= 0.9 and CAST(s.VISION_RIGHT as float) <= 1.1 Then 1 else 0 end ) as VISION_RIGHT_09
									    , sum( case when CAST(s.VISION_RIGHT as float) >= 1.2 Then 1 else 0 end ) as VISION_RIGHT_12
										, sum( case when CAST(s.VISION_LEFT as float) <= 0.8 Then 1 else 0 end ) as VISION_LEFT_08
                                       , sum( case when CAST(s.VISION_LEFT as float) >= 0.9 and CAST(s.VISION_LEFT as float) <= 1.1 Then 1 else 0 end ) as VISION_LEFT_09
									    , sum( case when CAST(s.VISION_LEFT as float) >= 1.2 Then 1 else 0 end ) as VISION_LEFT_12
                                        from HRMT08 s (NOLOCK)
                                        where 1=1 and isNumeric(s.VISION_RIGHT) =1 and isNumeric(s.VISION_LEFT) =1
                                        and isnull(s.VISION_RIGHT, '') <> '' and isnull(s.VISION_LEFT, '') <> ''
                                        group by s.SCHOOL_NO,s.GRADE,s.SYEAR ,s.SEMESTER
								) as c on t.SCHOOL_NO = c.SCHOOL_NO and t.GRADE = c.GRADE and t.SYEAR = c.SYEAR and t.SEMESTER = c.SEMESTER
                            where t.IDNO = @IDNO  and t.SCHOOL_NO=@SCHOOL_NO
                            order by t.SYEAR ,t.SEMESTER ";

            var QTemp = db.Database.Connection.Query<SECI03Hrmt08ListViewModel>(sSQL, new { IDNO = IDNO, SCHOOL_NO = schoolno });

            return QTemp;
        }

        public IEnumerable<SECI03Hrmt09ListViewModel> GetMyHrmt09Data(string IDNO, ref ECOOL_DEVEntities db)
        {
            var Temp = from a in db.HRMT09
                       join h in db.HRMT01 on new { IDNO = a.ID_NO } equals new { h.IDNO }
                       join s in db.BDMT01 on new { h.SCHOOL_NO } equals new { s.SCHOOL_NO }
                       where h.USER_TYPE == "S"
                       orderby a.SYEAR, a.SEMESTER
                       select new SECI03Hrmt09ListViewModel
                       {
                           IDNO = h.IDNO,
                           SCHOOL_NO = h.SCHOOL_NO,
                           SCHOOL_NAME = s.SHORT_NAME,
                           SYEAR = a.SYEAR,
                           SEMESTER = a.SEMESTER,
                           GRADE = a.GRADE,
                           TDATE = a.TDATE,
                           NAME = a.SNAME,
                           V_SET_REACH_TEST = a.V_SET_REACH_TEST,
                           S_L_JUMP_TEST = a.S_L_JUMP_TEST,
                           SIT_UPS_TEST = a.SIT_UPS_TEST,
                           C_P_F_TEST = a.C_P_F_TEST,
                       };

            return Temp;
        }
    }
}