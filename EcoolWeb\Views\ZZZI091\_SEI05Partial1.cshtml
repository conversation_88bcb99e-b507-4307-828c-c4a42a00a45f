﻿@model SECI05IndexViewModel
@{
    Layout = "~/Views/Shared/_LayoutEmpty1.cshtml";
    string DateNowStr = DateTime.Now.ToString("yyyy_MM_dd_H_mm_ss");
}

@using (Html.BeginForm("Index", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1" }))
{
    if (Model != null && (Model.NowBorrowList != null ? Model.NowBorrowList.Count : 0) > 0)
    {<section class="row px-3 readingBooksData">
            <div class="col-lg-12 bgPosition">
                <h2 class="heading-h2">閱讀性向</h2>
                <div id="tbData">
                    <div class="row mb-3">
                        <div class="col-12 text-center">
                            <h3>目前借閱書籍</h3>
                        </div>
                        <div class="col-12">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>
                                            分類編號
                                        </th>
                                        <th>
                                            書名
                                        </th>
                                        <th>
                                            借閱時間
                                        </th>
                                        <th>
                                            備註
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @if ((Model.NowBorrowList != null ? Model.NowBorrowList.Count : 0) > 0)
                                    {
                                        foreach (var item in Model.NowBorrowList)
                                        {
                                            <tr class="text-center">
                                                <td title="分類編號">@item.BK_GRP</td>
                                                <td title="書名">@item.BKNAME</td>
                                                <td title="借閱時間">
                                                    @if (item.BORROW_DATE != null)
                                                    {
                                                        @item.BORROW_DATE.ToString("yyyy/MM/dd")
                                                    }
                                                </td>
                                                <td title="備註">
                                                    @if (item.EXPIRED_DAY >= 14)
                                                    {
                                                        <font>借閱天數超過14天</font>
                                                    }
                                                </td>
                                            </tr>
                                        }

                                    }
                                    else
                                    {
                                        <tr class="text-center">
                                            <td colspan="4">暫時無正在借書的數量</td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                    @if (Model.BorrowTypeQty != null)
                    {
                        SECI05BorrowTypeQtyViewModel Top1 = Model.BorrowTypeQty.OrderByDescending(a => a.Rate).FirstOrDefault();
                        SECI05BorrowTypeQtyViewModel Top2 = null;
                        if (Model.BorrowTypeQty.Count > 1)
                        {
                            Top2 = Model.BorrowTypeQty.OrderByDescending(a => a.Rate).Skip(1).FirstOrDefault();
                        }

                        if (Top1.Rate >= 0.85)
                        {
                            <div class="text-left text-danger mr-2 mb-3">
                                <strong class="h4">您的@(Top1.TYPE_NAME)：比例為@(Top1.Rate.ToString("P"))，<br />注意是否有閱讀偏食，建議多類別閱讀。</strong>
                            </div>
                        }
                        else
                        {
                            <div class="text-left text-danger mr-2 mb-3">
                                <strong class="h4">
                                    你的閱讀量最高類別為@(Top1.TYPE_NAME)，比例為@(Top1.Rate.ToString("P"))；<br />
                                    @if (Top2 != null)
                                    {
                                        <text> 第二高為  @(Top2.TYPE_NAME)：比例為@(Top2.Rate.ToString("P"))；</text> <br />
                                    }
                                    沒有閱讀偏食，請繼續多元閱讀，豐富人生。
                                </strong>
                            </div>
                        }
                        <div class="row">
                            <div class="col-sm-12 col-xs-12">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>
                                            </th>
                                            @foreach (var item in Model.BorrowTypeQty)
                                            {
                                                <th class="text-center">
                                                    @item.TYPE_NAME
                                                </th>
                                            }
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td class="text-center">
                                                <strong>數量</strong>
                                            </td>
                                            @foreach (var item in Model.BorrowTypeQty)
                                            {
                                                <td class="text-center">
                                                    @item.QTY
                                                </td>
                                            }
                                        </tr>
                                        <tr>
                                            <td class="text-center">
                                                <strong>比例</strong>
                                            </td>
                                            @foreach (var item in Model.BorrowTypeQty)
                                            {
                                                <td class="text-center">
                                                    @((item.Rate).ToString("P"))
                                                </td>
                                            }
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    }
                    @* 借閱類別統計表 *@
                    <div class="mt-3 charts table-responsive">
                        @if (Model.BorrowColumnChart != null)
                        {
                            @Model.BorrowColumnChart
                        }
                    </div>

                    <div class="row mt-3 charts table-responsive">
                        <div class="col-12">
                            @if (Model.GradeQtyCharts != null)
                            {
                                @Model.GradeQtyCharts
                            }
                        </div>
                    </div>
                </div>
            </div>
        </section>
    }
}
