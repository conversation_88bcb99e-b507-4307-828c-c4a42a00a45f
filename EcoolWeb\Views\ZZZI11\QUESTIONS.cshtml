﻿@model ECOOL_APP.com.ecool.Models.DTO.ZZZI11QUESTIONSViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@Html.PermissionButton("回查詢列表", "button", (string)ViewBag.BRE_NO, "Index", new { @class = "btn btn-sm btn-sys", onclick = "Index()" }, 1, ViewBag.BtnPermission as List<ECOOL_APP.ControllerPermissionfile>)


@using (Html.BeginForm("QUESTIONS", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", enctype = "multipart/form-data" }))
{

    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })

    <div>
        <div class="panel panel-ZZZ" name="TOP">
            <div class="panel-heading text-center">
                @Html.BarTitle()
            </div>
            <div class="panel-body">
                <div class="form-horizontal">
                    <div class="form-group">
                        @Html.LabelFor(model => model.uADDT16.SNAME, htmlAttributes: new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            @Html.EditorFor(model => model.uADDT16.SNAME, new { htmlAttributes = new { @class = "form-control", @placeholder = "必填" } })
                            @Html.ValidationMessageFor(model => model.uADDT16.SNAME, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div class="form-group">
                        @Html.LabelFor(model => model.uADDT16.E_MAIL, htmlAttributes: new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            @Html.EditorFor(model => model.uADDT16.E_MAIL, new { htmlAttributes = new { @class = "form-control" } })
                            @Html.ValidationMessageFor(model => model.uADDT16.E_MAIL, "", new { @class = "text-danger" })
                        </div>
                    </div>


                    <div class="form-group">
                        @Html.LabelFor(model => model.uADDT16.SUBJECT, htmlAttributes: new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            @Html.EditorFor(model => model.uADDT16.SUBJECT, new { htmlAttributes = new { @class = "form-control", @placeholder = "必填" } })
                            @Html.ValidationMessageFor(model => model.uADDT16.SUBJECT, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div class="form-group">
                        @Html.LabelFor(model => model.uADDT16.QUESTIONS_TXT, htmlAttributes: new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            @Html.TextAreaFor(model => model.uADDT16.QUESTIONS_TXT, new { @class = "form-control", @rows = "5", @cols = "20", @placeholder = "必填" })
                            @Html.ValidationMessageFor(model => model.uADDT16.QUESTIONS_TXT, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="control-label col-md-3" for="QUESTIONS_TXT">上傳附件</label>
                        <div class="col-md-9">
                            <input class="btn btn-default" type="file" name="files" />
                            @if (Model.uADDT16.FILE_NAME != null &&  Model.uADDT16.FILE_NAME != string.Empty)
                            {
                                @Html.PermissionButton("X", "button", (string)ViewBag.BRE_NO, "QUESTIONS", new { @class = "btn btn-info btn-xs", onclick = "DelFile('" + Model.uADDT16.QUESTIONS_ID + "', '', '" + Model.uADDT16.FILE_NAME + "')" }, 1, ViewBag.BtnPermission as List<ECOOL_APP.ControllerPermissionfile>)
                                @Html.DisplayFor(model => model.uADDT16.FILE_NAME)
                                @Html.HiddenFor(model => model.uADDT16.FILE_NAME)
                            }
                            @Html.ValidationMessageFor(model => model.uADDT16.FILE_NAME, "", new { @class = "text-danger" })
                        </div>
                    </div>
                </div>
                <div class="form-group text-center">
                    @if (Model.uADDT16.QUESTIONS_ID != string.Empty && Model.uADDT16.QUESTIONS_ID != null)
                    {
                        @Html.PermissionButton("送出", "button", (string)ViewBag.BRE_NO, "QUESTIONS", new { @class = "btn btn-default", onclick = "Save('" + EcoolWeb.Controllers.ZZZI11Controller.DATA_TYPE.DATA_TYPE_U + "')" }, 1, ViewBag.BtnPermission as List<ECOOL_APP.ControllerPermissionfile>)
                        @Html.PermissionButton("刪除", "button", (string)ViewBag.BRE_NO, "QUESTIONS", new { @class = "btn btn-default", onclick = "Save('" + EcoolWeb.Controllers.ZZZI11Controller.DATA_TYPE.DATA_TYPE_D + "')" }, 1, ViewBag.BtnPermission as List<ECOOL_APP.ControllerPermissionfile>)
                        @Html.PermissionButton("放棄", "button", (string)ViewBag.BRE_NO, "Index", new { @class = "btn btn-default", onclick = "Details()" }, 1, ViewBag.BtnPermission as List<ECOOL_APP.ControllerPermissionfile>)
                    }
                    else
                    {
                        @Html.PermissionButton("送出", "button", (string)ViewBag.BRE_NO, "QUESTIONS", new { @class = "btn btn-default", onclick = "Save('" + EcoolWeb.Controllers.ZZZI11Controller.DATA_TYPE.DATA_TYPE_A + "')" }, 1, ViewBag.BtnPermission as List<ECOOL_APP.ControllerPermissionfile>)
                    }

                   
                </div>
            </div>
        </div>
    </div>
    @Html.Hidden("DATA_TYPE")
    @Html.HiddenFor(model => model.uADDT16.QUESTIONS_ID)
    @Html.HiddenFor(model => model.Search.Page)
    @Html.HiddenFor(model => model.Search.OrderByName)
    @Html.HiddenFor(model => model.Search.Q_QUESTIONS_ID)
    @Html.HiddenFor(model => model.Search.SearchContents)
    @Html.HiddenFor(model => model.Search.SyntaxName)
    @Html.HiddenFor(model => model.Search.DetailsPage)
    @Html.HiddenFor(model => model.Search.STATUS)
}
@section Scripts {
    <script language="JavaScript">
        $(document).ready(function () {
            $('[data-toggle="tooltip"]').tooltip()
        });

        function Index() {
            form1.action = '@Html.Raw(@Url.Action("Index", (string)ViewBag.BRE_NO))'
            form1.submit();
        }

        function Details() {
            form1.action = '@Html.Raw(@Url.Action("Details", (string)ViewBag.BRE_NO))'
            form1.submit();
        }


        function DelFile(QUESTIONS_ID, ITEM_NO, FileName) {
            form1.action = '@Html.Raw(@Url.Action("DelFile", (string)ViewBag.BRE_NO))' + '?QUESTIONS_ID=' + QUESTIONS_ID + '&ITEM_NO=' + ITEM_NO + '&FileName=' + FileName
            form1.submit();
        }


        function Save(Val) {
            form1.DATA_TYPE.value = Val
            form1.submit();
        }
    </script>
}
