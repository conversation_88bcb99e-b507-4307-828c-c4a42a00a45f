﻿using ECOOL_APP.EF;
using MvcPaging;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using Dapper;
using System.Web;

using System.Text.RegularExpressions;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using System.Drawing;


namespace ECOOL_APP.com.ecool.service
{
    public class ADDI10Service
    {
        public ADDI10IndexViewModel GetVoteListData(ADDI10IndexViewModel model, UserProfile user, ref ECOOL_DEVEntities db)
        {
            string sSQL = @"Select QUESTIONNAIRE_ID = a.QUESTIONNAIRE_ID,
		                     QUESTIONNAIRE_NAME = a.QUESTIONNAIRE_NAME,
		                     QUESTIONNAIRE_DESC = a.QUESTIONNAIRE_DESC,
                             QUESTIONNAIRE_ANSWER_Count=a.ANSWER_COUNT,
		                     QUESTIONNAIRE_SDATE = a.QUESTIONNAIRE_SDATE,
		                     QUESTIONNAIRE_EDATE = a.QUESTIONNAIRE_EDATE,
		                     STATUS = a.STATUS,
		                     CHG_PERSON = a.CHG_PERSON,
		                     CHG_DATE = a.CHG_DATE,
		                     CRE_PERSON = a.CRE_PERSON,
		                     CRE_PERSON_NAME = b.NAME,
		                     CRE_PERSON_SNAME = b.SNAME,
		                     CRE_DATE = a.CRE_DATE,
		                     SOU_KEY = a.SOU_KEY,
		                     CASH = a.CASH,
		                     RESULT = a.RESULT,
                             RESULT_PERSON = a.RESULT_PERSON,
		                     REGISTERED_BALLOT = a.REGISTERED_BALLOT,
		                     VOTE_COUNT = (SELECT COUNT(*) FROM SAQT04_M M  (nolock) WHERE M.QUESTIONNAIRE_ID = a.QUESTIONNAIRE_ID and M.DEL_YN='N'  and ANSWER_ID in (  SELECT ANSWER_ID FROM SAQT04 where QUESTIONNAIRE_ID= a.QUESTIONNAIRE_ID )),
                             IsVote = Case When (SELECT COUNT(*) FROM SAQT04_M M  (nolock) WHERE M.QUESTIONNAIRE_ID = a.QUESTIONNAIRE_ID
                                                  and M.SCHOOL_NO=@SCHOOL_NO and M.USER_NO=@USER_NO and M.DEL_YN='N' )>0 Then 0 else  1 End,
                              ANSWERCount = Case When (SELECT COUNT(*) FROM SAQT04_M M  (nolock) WHERE M.QUESTIONNAIRE_ID = a.QUESTIONNAIRE_ID
                                                  and M.SCHOOL_NO=@SCHOOL_NO  and M.USER_NO=@USER_NO and M.DEL_YN='N' )>0 Then (SELECT COUNT(*) FROM SAQT04_M M  (nolock) WHERE M.QUESTIONNAIRE_ID = a.QUESTIONNAIRE_ID
                                                  and M.SCHOOL_NO=@SCHOOL_NO  and M.USER_NO=@USER_NO and M.DEL_YN='N' ) else 0  End,
                              ANSWER_PERSON_YN=Isnull(a.ANSWER_PERSON_YN,'N')
							 ,ANSWER_PERSON_COUNT = (Select COUNT(*) from REFT01 r (nolock) where r.REF_KEY= a.QUESTIONNAIRE_ID and r.REF_TABLE = 'SAQT01' and r.SCHOOL_NO=@SCHOOL_NO and r.USER_NO=@USER_NO )
                    from SAQT01 a (nolock)
                    left outer join HRMT01 b  (nolock) on a.CRE_PERSON=b.USER_KEY
                    Where a.SOU_KEY =@SOU_KEY  ";

            if (HRMT24_ENUM.CheckQAdmin(user) == false)
            {
                sSQL = sSQL + @" and ( (a.QUESTIONNAIRE_EDATE > @Now) or (a.QUESTIONNAIRE_EDATE <= @Now and a.RESULT=1) or (a.CRE_PERSON = @CRE_PERSON) )";
            }

            var temp = db.Database.Connection.Query<ADDI10IndexListViewModel>(sSQL
                   , new
                   {
                       SOU_KEY = model.Search.WhereSOU_KEY,

                       Now = DateTime.Now,
                       CRE_PERSON = user?.USER_KEY,
                       SCHOOL_NO = user?.SCHOOL_NO,
                       USER_NO = user?.USER_NO,
                   });

            if (!string.IsNullOrWhiteSpace(model.Search.WhereSearch))
            {
                temp = temp.Where(a => (a.QUESTIONNAIRE_NAME ?? "").Contains(model.Search.WhereSearch.Trim())
                || (a.QUESTIONNAIRE_DESC ?? "").Contains(model.Search.WhereSearch.Trim())
                );
            }
            if (!string.IsNullOrWhiteSpace(model.Search.whereADDI10Status))
            {
                DateTime dt = DateTime.Now;
                var arrWRITING_STATUS = model.Search.whereADDI10Status.Split(',');
                temp = temp.Where(X => arrWRITING_STATUS.Contains(X.STATUS.ToString()) && X.STATUS != null);
                if (model.Search.whereADDI10Status == ((int)SAQT01.STATUSVal.StartedOut).ToString())
                {
                    temp = temp.Where(x => x.QUESTIONNAIRE_SDATE <= dt && x.QUESTIONNAIRE_EDATE >= dt);
                }
            }
            else if (string.IsNullOrWhiteSpace(model.Search.whereADDI10Status))
            {
                temp = temp.Where(X => X.STATUS != null && X.STATUS != SAQT01.STATUSVal.Disabled && X.STATUS != SAQT01.STATUSVal.NotStarted && X.STATUS != SAQT01.STATUSVal.NotShow);
            }
            string CrePerosn = "";
            CrePerosn = user.SCHOOL_NO + "_" + user.USER_NO;
            if (HRMT24_ENUM.CheckQAdmin(user) == false)
            {
                temp = temp.Where(a => (a.ANSWER_PERSON_YN == SharedGlobal.Y && a.ANSWER_PERSON_COUNT > 0) || (a.ANSWER_PERSON_YN == SharedGlobal.N) || a.CRE_PERSON == CrePerosn);
            }

            temp = temp.OrderByDescending(a => a.QUESTIONNAIRE_SDATE);
            model.ListData = temp.ToPagedList(model.Search.Page > 0 ? model.Search.Page - 1 : 0, model.PageSize);

            return model;
        }
     
        public ADDI10EditViewModel GetVoteDetailsPreview(ADDI10EditViewModel model, ref ECOOL_DEVEntities db)
        {
            model.Title = (from c in db.SAQT01
                           where c.QUESTIONNAIRE_ID == model.Search.WhereQUESTIONNAIRE_ID
                           select c).FirstOrDefault();

            List<SAQT03> Temp_Topic_D = (from b in db.SAQT03
                                         where b.QUESTIONNAIRE_ID == model.Search.WhereQUESTIONNAIRE_ID
                                         select b).ToList();

            var Temp = (from a in db.SAQT02
                        where a.QUESTIONNAIRE_ID == model.Search.WhereQUESTIONNAIRE_ID
                        select new ADDI10TopicViewModel()
                        {
                            QUESTIONNAIRE_ID = a.QUESTIONNAIRE_ID,
                            Q_NUM = a.Q_NUM,
                            Q_SUBJECT = a.Q_SUBJECT,
                            Q_ORDER_BY = a.Q_ORDER_BY,
                            Q_MUST = a.Q_MUST,
                            Q_TYPE = a.Q_TYPE,
                            Q_MEMO = a.Q_MEMO,
                            Q_MUTIPLE_CHOICES_OF_NUM = a.Q_MUTIPLE_CHOICES_OF_NUM
                        }
             ).OrderBy(a => a.Q_ORDER_BY).ToList();

            model.Topic = Temp.Select(
                a =>
                {
                    a.Topic_D = (from b in Temp_Topic_D
                                 where b.QUESTIONNAIRE_ID == a.QUESTIONNAIRE_ID
                                        && b.Q_NUM == a.Q_NUM
                                 select b).ToList();
                    a.ANSWER = (model.Topic != null) ? model.Topic.Where(b => b.Q_NUM == a.Q_NUM).Select(b => b.ANSWER).FirstOrDefault() : string.Empty;
                    a.Index = model.Topic?.Where(b => b.Q_NUM == a.Q_NUM).Select(b => b.Index).FirstOrDefault();
                    return a;
                }).ToList();

            return model;
        }

        public string TOExcelADDI10(List<ADDISAQTModel> aDDISAQTs,List<SAQT02> SAQT02Items) {

            IWorkbook wb = new XSSFWorkbook();
            ISheet idpWorkSheet = wb.CreateSheet("有獎徵答總表");//建立sheet
                                                     //利用sheet建立列
            idpWorkSheet.CreateRow(0);//建立第一列檔標題
            idpWorkSheet.GetRow(0).CreateCell(0).SetCellValue("");
            List<string> ANSPEOPEL = new List<string>();
            ANSPEOPEL = aDDISAQTs.Select(x => x.NAME).Distinct().ToList();
            List<string> ANS = new List<string>();
            
            int i = 1;
            foreach (var Item in SAQT02Items) {
                idpWorkSheet.GetRow(0).CreateCell(i).SetCellValue(Item.Q_SUBJECT);
                ANS.Add(Item.QUESTIONNAIRE_ID);
               
                i++;
            }
            int j = 1;
            foreach (var item in ANSPEOPEL)
            {
                idpWorkSheet.CreateRow(j);
                idpWorkSheet.GetRow(j).CreateCell(0).SetCellValue(item);
                List<ADDISAQTModel> dDISAQTModels = new List<ADDISAQTModel>();
                dDISAQTModels = aDDISAQTs.Where(x => x.NAME == item).ToList();
                int k =1;
                foreach (var item2 in ANS)
                {
                    
                    ADDISAQTModel dDISAQTModelItem = new ADDISAQTModel();
                    dDISAQTModelItem = dDISAQTModels.Where(x => x.QUESTIONNAIRE_ID == item2 && x.Q_NUM==k).FirstOrDefault();
                    if (dDISAQTModelItem != null)
                    {
                        idpWorkSheet.GetRow(j).CreateCell(k).SetCellValue(dDISAQTModelItem.ANSWER??"");

                    }
                    else {


                        idpWorkSheet.GetRow(j).CreateCell(k).SetCellValue("");
                    }
                    k++;
                }
                j++;



            }

            //設定格式(可以設定多個ICellStyle，再選擇其中一個設定給儲存格)
            ICellStyle cellStyle = wb.CreateCellStyle();
            //文字水平置中
            cellStyle.Alignment = HorizontalAlignment.Center;
            //設定邊界線條為圓點
            cellStyle.BorderBottom = BorderStyle.Dotted;
            for (var jj = 0; jj < idpWorkSheet.GetRow(0).LastCellNum; jj++)
            {
                //標題設定cellStyle所設定的屬性，所以會水平置中且底線變為圓點
                idpWorkSheet.GetRow(0).GetCell(jj).CellStyle = cellStyle;
                //設定欄寬，單位為1/256個字元寬度，所以要乘上256
                idpWorkSheet.SetColumnWidth(j, 20 * 256);
            }
            string strTMPFile = System.Web.Configuration.WebConfigurationManager.AppSettings["FilePath"] + @"\TMP\" + DateTime.Now.Date.ToString("yyyyMMdd");

            if (Directory.Exists(strTMPFile) == false)
            {
                Directory.CreateDirectory(strTMPFile);
            }

            //FileStream file = new FileStream(@"D:\程式練習\NPOI\ExcelPractice\simple.xls", FileMode.Create);//產生檔案

            //wb.Write(file);

            //file.Close();
            string FileName = "";
            FileName = "申請者資料" + DateTime.Now.ToString("yyyyMMddHHmmss") + ".xlsx";
            strTMPFile = strTMPFile + @"\投票資料" + DateTime.Now.ToString("yyyyMMddHHmmss") + ".xlsx";

            System.IO.FileStream FS = new FileStream(strTMPFile, FileMode.Create);
            wb.Write(FS);
            FS.Close();

            return strTMPFile;

        }
        public string TOExcelADDI10ALL(List<ADDISAQTModel> aDDISAQTs, List<SAQT02> SAQT02Items)
        {

            IWorkbook wb = new XSSFWorkbook();
            ISheet idpWorkSheet = wb.CreateSheet("有獎徵答總表");//建立sheet
                                                           //利用sheet建立列
            idpWorkSheet.CreateRow(0);//建立第一列檔標題
            idpWorkSheet.GetRow(0).CreateCell(0).SetCellValue("");
            List<string> ANSPEOPEL = new List<string>();
            ANSPEOPEL = aDDISAQTs.OrderByDescending(x=>x.ANSWER_DATE).Select(x => x.NAME).ToList();
            List<string> ANS = new List<string>();

            int i = 1;
            foreach (var Item in SAQT02Items)
            {
                idpWorkSheet.GetRow(0).CreateCell(i).SetCellValue(Item.Q_SUBJECT);
                ANS.Add(Item.QUESTIONNAIRE_ID);

                i++;
            }
            idpWorkSheet.GetRow(0).CreateCell(i).SetCellValue("回答日期");
            int j = 1;
            foreach (var item in ANSPEOPEL.Distinct().ToList())
            {
                List<ADDISAQTModel> dDISAQTModels = new List<ADDISAQTModel>();
                dDISAQTModels = aDDISAQTs.Where(x => x.NAME == item).ToList();
                List<string> ANSWERIDs = new List<string>();
                
                    


                 
                    ANSWERIDs = dDISAQTModels.Where(x=>x.QUESTIONNAIRE_ID== ANS.FirstOrDefault()).OrderByDescending(x=>x.ANSWER_DATE).Select(x => x.ANSWER_ID).ToList();
                    foreach (var ASNID in ANSWERIDs.Distinct().ToList())
                    {
                        int k = 1;

                    idpWorkSheet.CreateRow(j);
                    idpWorkSheet.GetRow(j).CreateCell(0).SetCellValue(item);
                    foreach (var item2 in ANS)
                    {



                        ADDISAQTModel dDISAQTModelItem = new ADDISAQTModel();
                        dDISAQTModelItem = dDISAQTModels.Where(x => x.ANSWER_ID == ASNID && x.QUESTIONNAIRE_ID == item2 && x.Q_NUM == k).FirstOrDefault();
                        if (dDISAQTModelItem != null)
                        {
                            idpWorkSheet.GetRow(j).CreateCell(k).SetCellValue(dDISAQTModelItem.ANSWER ?? "");

                        }
                        else
                        {


                            idpWorkSheet.GetRow(j).CreateCell(k).SetCellValue("");
                        }

                       
                        k++;
                        }
             
                    ADDISAQTModel dDISAQTModelItem1 = new ADDISAQTModel();
                    dDISAQTModelItem1 = dDISAQTModels.Where(x => x.ANSWER_ID == ASNID && x.QUESTIONNAIRE_ID == ANS.FirstOrDefault() && x.ANSWER_DATE!=null).FirstOrDefault();
                  
                    if (dDISAQTModelItem1 != null)
                    {
                        idpWorkSheet.GetRow(j).CreateCell(k).SetCellValue(dDISAQTModelItem1.ANSWER_DATE.ToString("yyyy/MM/dd hh:mm:ss") ?? "");

                    }
                    else
                    {


                        idpWorkSheet.GetRow(j).CreateCell(k).SetCellValue("");
                    }
                    j++;
                }
               
             



            }

            //設定格式(可以設定多個ICellStyle，再選擇其中一個設定給儲存格)
            ICellStyle cellStyle = wb.CreateCellStyle();
            //文字水平置中
            cellStyle.Alignment = HorizontalAlignment.Center;
            //設定邊界線條為圓點
            cellStyle.BorderBottom = BorderStyle.Dotted;
            for (var jj = 0; jj < idpWorkSheet.GetRow(0).LastCellNum; jj++)
            {
                //標題設定cellStyle所設定的屬性，所以會水平置中且底線變為圓點
                idpWorkSheet.GetRow(0).GetCell(jj).CellStyle = cellStyle;
                //設定欄寬，單位為1/256個字元寬度，所以要乘上256
                idpWorkSheet.SetColumnWidth(j, 20 * 256);
            }
            string strTMPFile = System.Web.Configuration.WebConfigurationManager.AppSettings["FilePath"] + @"\TMP\" + DateTime.Now.Date.ToString("yyyyMMdd");

            if (Directory.Exists(strTMPFile) == false)
            {
                Directory.CreateDirectory(strTMPFile);
            }

            //FileStream file = new FileStream(@"D:\程式練習\NPOI\ExcelPractice\simple.xls", FileMode.Create);//產生檔案

            //wb.Write(file);

            //file.Close();
            string FileName = "";
            FileName = "申請者資料" + DateTime.Now.ToString("yyyyMMddHHmmss") + ".xlsx";
            strTMPFile = strTMPFile + @"\投票資料" + DateTime.Now.ToString("yyyyMMddHHmmss") + ".xlsx";

            System.IO.FileStream FS = new FileStream(strTMPFile, FileMode.Create);
            wb.Write(FS);
            FS.Close();

            return strTMPFile;

        }
        public List<ADDISAQTModel> GetSAQTModel(List<ADDISAQTModel> model,string QUESTIONNAIRE_ID, ref ECOOL_DEVEntities db) {

            string sSQL = @" SELECT a.Q_NUM,0 Q_T_NUM,'Q'+CONVERT(nvarchar(10),a.Q_NUM)+' '+ a.Q_SUBJECT as Q_VAL,c.NAME,k.ANSWER_ID,k.QUESTIONNAIRE_ID,(CASE WHEN  k.ANSWER is null then '' else k.ANSWER end ) AS ANSWER

                            ,c.ANSWER_DATE

                            FROM SAQT02 a (nolock)
							inner join SAQT03 b on a.QUESTIONNAIRE_ID=b.QUESTIONNAIRE_ID  and a.Q_NUM=b.Q_NUM
							inner join SAQT04_M c on b.QUESTIONNAIRE_ID=c.QUESTIONNAIRE_ID 
						   inner join SAQT04 k on b.QUESTIONNAIRE_ID=k.QUESTIONNAIRE_ID and c.ANSWER_ID=k.ANSWER_ID and b.Q_NUM=k.Q_NUM
                            where a.QUESTIONNAIRE_ID=@QUESTIONNAIRE_ID";
            model =db.Database.Connection.Query<ADDISAQTModel>(sSQL
                  , new
                  {
                      QUESTIONNAIRE_ID = QUESTIONNAIRE_ID
                  }).ToList();
            return model;
        }
        public ADDI10StatisticsVoteViewModel GetStatisticsVote(ADDI10StatisticsVoteViewModel model, ref ECOOL_DEVEntities db, string CLASS_NO)
        {
            model.Title = (from c in db.SAQT01
                           where c.QUESTIONNAIRE_ID == model.Search.WhereQUESTIONNAIRE_ID
                           select c).FirstOrDefault();

            string sSQL = @"SELECT a.*
                            FROM SAQT02 a (nolock)
                            where a.QUESTIONNAIRE_ID=@QUESTIONNAIRE_ID
                            order by a.Q_NUM";

            model.Topic = db.Database.Connection.Query<SAQT02>(sSQL
                  , new
                  {
                      QUESTIONNAIRE_ID = model.Search.WhereQUESTIONNAIRE_ID,
                      radio = SAQT03.Q_INPUT_TYPEVal.radio,
                      checkbox = SAQT03.Q_INPUT_TYPEVal.checkbox,
                  }).ToList();
            string strSQLSeatNo = "select  *　from  SAQT04_M where QUESTIONNAIRE_ID =@QUESTIONNAIRE_ID";
            List<SAQT04_M> sAQT04_Ms = new List<SAQT04_M>();
            sAQT04_Ms = db.Database.Connection.Query<SAQT04_M>(sSQL
                  , new
                  {
                      QUESTIONNAIRE_ID = model.Search.WhereQUESTIONNAIRE_ID
                  }).ToList();

            sSQL = $@"select a.Q_NUM,a.Q_T_NUM,a.Q_VAL
                    , Sum(Case When a.Q_VAL = d.ANSWER and a.Q_INPUT_TYPE in ('{SAQT03.Q_INPUT_TYPEVal.radio}','{SAQT03.Q_INPUT_TYPEVal.checkbox}') Then 1
								 When isnull(d.ANSWER,'')<>'' and a.Q_INPUT_TYPE not in ('{SAQT03.Q_INPUT_TYPEVal.radio}','{SAQT03.Q_INPUT_TYPEVal.checkbox}') Then 1
								else 0 end) QTY
                    ,T.TOT_QTY
                    , Sum(Case When  a.Q_INPUT_TYPE in ('{SAQT03.Q_INPUT_TYPEVal.UploadImage}','{SAQT03.Q_INPUT_TYPEVal.UploadWorld}') Then 1
								
								else 0 end) IsIM
                    ,RATE =Round( Sum(Case When a.Q_VAL = d.ANSWER and a.Q_INPUT_TYPE in ('{SAQT03.Q_INPUT_TYPEVal.radio}','{SAQT03.Q_INPUT_TYPEVal.checkbox}') Then 1
								 When isnull(d.ANSWER,'')<>'' and a.Q_INPUT_TYPE not in ('{SAQT03.Q_INPUT_TYPEVal.radio}','{SAQT03.Q_INPUT_TYPEVal.checkbox}') Then 1
								else 0 end)* 100.0 / T.TOT_QTY*1.0,2)
                    from SAQT03 a  (nolock)";

            string sSQLstr = "";
            if (!string.IsNullOrWhiteSpace(CLASS_NO))
            {
                sSQLstr = $@"join (Select sa.QUESTIONNAIRE_ID,sa.Q_NUM,Case when sb.word<>'' Then sb.word Else sa.ANSWER  end as  ANSWER
						 from SAQT04 sa  (nolock)
						 OUTER apply dbo.udf_Split(case when (select count(*) from SAQT03 s3 where s3.QUESTIONNAIRE_ID=sa.QUESTIONNAIRE_ID  and  s3.Q_NUM=sa.Q_NUM  and s3.Q_INPUT_TYPE in ('{SAQT03.Q_INPUT_TYPEVal.radio}','{SAQT03.Q_INPUT_TYPEVal.checkbox}') ) > 0 Then	 sa.ANSWER Else '' End,',') sb
					     where sa.QUESTIONNAIRE_ID=@QUESTIONNAIRE_ID and sa.ANSWER_ID in(select ANSWER_ID from  SAQT04_M where QUESTIONNAIRE_ID=@QUESTIONNAIRE_ID and CLASS_NO=@CLASS_NO)) d on a.QUESTIONNAIRE_ID=d.QUESTIONNAIRE_ID and a.Q_NUM =d.Q_NUM
                    join (Select  s.QUESTIONNAIRE_ID,s.Q_NUM,Count(*) TOT_QTY from SAQT04 s (nolock)
	                      where s.QUESTIONNAIRE_ID=@QUESTIONNAIRE_ID and s.ANSWER_ID in(select ANSWER_ID from  SAQT04_M where QUESTIONNAIRE_ID=@QUESTIONNAIRE_ID and CLASS_NO=@CLASS_NO)
	                      group by s.QUESTIONNAIRE_ID,s.Q_NUM) as T on a.QUESTIONNAIRE_ID=T.QUESTIONNAIRE_ID and a.Q_NUM =T.Q_NUM
                    where a.QUESTIONNAIRE_ID=@QUESTIONNAIRE_ID
                    group by  a.Q_NUM,a.Q_T_NUM,a.Q_VAL,T.TOT_QTY
                    order by a.Q_NUM,a.Q_T_NUM";
                sSQL = sSQL + sSQLstr;
                model.VoteData = db.Database.Connection.Query<ADDI10StatisticsVoteDataViewModel>(sSQL
            , new
            {
                CLASS_NO = CLASS_NO,
                QUESTIONNAIRE_ID = model.Search.WhereQUESTIONNAIRE_ID,
            }).ToList();
            }
            else if (!string.IsNullOrWhiteSpace(model.whereGrade))
            {
                sSQLstr = $@"join (Select sa.QUESTIONNAIRE_ID,sa.Q_NUM,Case when sb.word<>'' Then sb.word Else sa.ANSWER  end as  ANSWER
						 from SAQT04 sa  (nolock)
						 OUTER apply dbo.udf_Split(case when (select count(*) from SAQT03 s3 where s3.QUESTIONNAIRE_ID=sa.QUESTIONNAIRE_ID  and  s3.Q_NUM=sa.Q_NUM  and s3.Q_INPUT_TYPE in ('{SAQT03.Q_INPUT_TYPEVal.radio}','{SAQT03.Q_INPUT_TYPEVal.checkbox}') ) > 0 Then	 sa.ANSWER Else '' End,',') sb
					     where sa.QUESTIONNAIRE_ID=@QUESTIONNAIRE_ID and sa.ANSWER_ID in(select ANSWER_ID from  SAQT04_M where QUESTIONNAIRE_ID=@QUESTIONNAIRE_ID and LEFT([CLASS_NO],1)=@Grade)) d on a.QUESTIONNAIRE_ID=d.QUESTIONNAIRE_ID and a.Q_NUM =d.Q_NUM
                    join (Select  s.QUESTIONNAIRE_ID,s.Q_NUM,Count(*) TOT_QTY from SAQT04 s (nolock)
	                      where s.QUESTIONNAIRE_ID=@QUESTIONNAIRE_ID and s.ANSWER_ID in(select ANSWER_ID from  SAQT04_M where QUESTIONNAIRE_ID=@QUESTIONNAIRE_ID and LEFT([CLASS_NO],1)=@Grade)
	                      group by s.QUESTIONNAIRE_ID,s.Q_NUM) as T on a.QUESTIONNAIRE_ID=T.QUESTIONNAIRE_ID and a.Q_NUM =T.Q_NUM
                    where a.QUESTIONNAIRE_ID=@QUESTIONNAIRE_ID
                    group by  a.Q_NUM,a.Q_T_NUM,a.Q_VAL,T.TOT_QTY
                    order by a.Q_NUM,a.Q_T_NUM";
                sSQL = sSQL + sSQLstr;
                model.VoteData = db.Database.Connection.Query<ADDI10StatisticsVoteDataViewModel>(sSQL
            , new
            {
                Grade = model.whereGrade,
                QUESTIONNAIRE_ID = model.Search.WhereQUESTIONNAIRE_ID,
            }).ToList();
            }
            else
            {
                sSQLstr = $@"join (Select sa.QUESTIONNAIRE_ID,sa.Q_NUM,Case when sb.word<>'' Then sb.word Else sa.ANSWER  end as  ANSWER
						 from SAQT04 sa  (nolock)
						 OUTER apply dbo.udf_Split(case when (select count(*) from SAQT03 s3 where s3.QUESTIONNAIRE_ID=sa.QUESTIONNAIRE_ID  and  s3.Q_NUM=sa.Q_NUM  and s3.Q_INPUT_TYPE in ('{SAQT03.Q_INPUT_TYPEVal.radio}','{SAQT03.Q_INPUT_TYPEVal.checkbox}') ) > 0 Then	 sa.ANSWER Else '' End,',') sb
					     where sa.QUESTIONNAIRE_ID=@QUESTIONNAIRE_ID) d on a.QUESTIONNAIRE_ID=d.QUESTIONNAIRE_ID and a.Q_NUM =d.Q_NUM
                    join (Select  s.QUESTIONNAIRE_ID,s.Q_NUM,Count(*) TOT_QTY from SAQT04 s (nolock)
	                      where s.QUESTIONNAIRE_ID=@QUESTIONNAIRE_ID
	                      group by s.QUESTIONNAIRE_ID,s.Q_NUM) as T on a.QUESTIONNAIRE_ID=T.QUESTIONNAIRE_ID and a.Q_NUM =T.Q_NUM
                    where a.QUESTIONNAIRE_ID=@QUESTIONNAIRE_ID
                    group by  a.Q_NUM,a.Q_T_NUM,a.Q_VAL,T.TOT_QTY
                    order by a.Q_NUM,a.Q_T_NUM";
                sSQL = sSQL + sSQLstr;
                model.VoteData = db.Database.Connection.Query<ADDI10StatisticsVoteDataViewModel>(sSQL
        , new
        {
            QUESTIONNAIRE_ID = model.Search.WhereQUESTIONNAIRE_ID,
        }).ToList();
            }
            return model;
        }

        public List<ADDI10ExportStatisticsVoteExcelViewModel> GetExportStatisticsVoteExcel(string QUESTIONNAIRE_ID, ref ECOOL_DEVEntities db)
        {
            string sSQL = @"SELECT a.Q_NUM,0 Q_T_NUM,'Q'+CONVERT(nvarchar(10),a.Q_NUM)+' '+ a.Q_SUBJECT as Q_VAL,CONVERT(money,null)   QTY , CONVERT(money,null) RATE
                            FROM SAQT02 a (nolock)
                            where a.QUESTIONNAIRE_ID=@QUESTIONNAIRE_ID

                            union all

                            select a.Q_NUM,a.Q_T_NUM,CONVERT(nvarchar(10),a.Q_T_NUM)+' '+a.Q_VAL
                            , Sum(Case When a.Q_VAL = d.ANSWER and a.Q_INPUT_TYPE in ('radio','checkbox') Then 1
                            When isnull(d.ANSWER,'')<>'' and a.Q_INPUT_TYPE not in ('radio','checkbox') Then 1
                            else 0 end) QTY
                            ,RATE =Round( Sum(Case When a.Q_VAL = d.ANSWER and a.Q_INPUT_TYPE in ('radio','checkbox') Then 1
                            When isnull(d.ANSWER,'')<>'' and a.Q_INPUT_TYPE not in ('radio','checkbox') Then 1
                            else 0 end)* 100.0 / T.TOT_QTY*1.0,2)
                            from SAQT03 a  (nolock)
                            join (Select sa.QUESTIONNAIRE_ID,sa.Q_NUM,Case when sb.word<>'' Then sb.word Else sa.ANSWER  end as  ANSWER
                                  from SAQT04 sa  (nolock)
                                  OUTER apply dbo.udf_Split(case when (select count(*) from SAQT03 s3 where s3.QUESTIONNAIRE_ID=sa.QUESTIONNAIRE_ID  and  s3.Q_NUM=sa.Q_NUM  and s3.Q_INPUT_TYPE in ('radio','checkbox') ) > 0 Then	 sa.ANSWER Else '' End,',') sb
                                  where sa.QUESTIONNAIRE_ID=@QUESTIONNAIRE_ID) d on a.QUESTIONNAIRE_ID=d.QUESTIONNAIRE_ID and a.Q_NUM =d.Q_NUM
                            join (Select  s.QUESTIONNAIRE_ID,s.Q_NUM,Count(*) TOT_QTY from SAQT04 s (nolock)
                                  where s.QUESTIONNAIRE_ID=@QUESTIONNAIRE_ID
                                  group by s.QUESTIONNAIRE_ID,s.Q_NUM) as T on a.QUESTIONNAIRE_ID=T.QUESTIONNAIRE_ID and a.Q_NUM =T.Q_NUM
                            where a.QUESTIONNAIRE_ID=@QUESTIONNAIRE_ID
                            group by  a.Q_NUM,a.Q_T_NUM,a.Q_VAL,T.TOT_QTY

                            order by a.Q_NUM,Q_T_NUM";
            var Temp = db.Database.Connection.Query<ADDI10ExportStatisticsVoteExcelViewModel>(sSQL
              , new
              {
                  QUESTIONNAIRE_ID
              }).ToList();

            return Temp;
        }

        public ADDI10StatisticsVoteDetailsViewModel GetALLVotePeople(string QUESTIONNAIRE_ID, string OrdercColumn, string SyntaxName, string whereGrade, string whereCLASS_NO, string whereSearch, ref ECOOL_DEVEntities db)
        {
            ADDI10StatisticsVoteDetailsViewModel model = new ADDI10StatisticsVoteDetailsViewModel();

            model.Title = db.SAQT01.Where(a => a.QUESTIONNAIRE_ID == QUESTIONNAIRE_ID).FirstOrDefault();

            model.Topic = db.SAQT02.Where(a => a.QUESTIONNAIRE_ID == QUESTIONNAIRE_ID).FirstOrDefault();

            string sSQL = $@"select a.* ,c.SHORT_NAME
                            from SAQT04_M a (nolock)
                            join SAQT04 b (nolock) on a.QUESTIONNAIRE_ID =b.QUESTIONNAIRE_ID and a.ANSWER_ID=b.ANSWER_ID
                                OUTER apply dbo.udf_Split(case when (select count(*) from SAQT03 s3 where s3.QUESTIONNAIRE_ID=b.QUESTIONNAIRE_ID  and  s3.Q_NUM=b.Q_NUM  and s3.Q_INPUT_TYPE in ('{SAQT03.Q_INPUT_TYPEVal.radio}','{SAQT03.Q_INPUT_TYPEVal.checkbox}') ) > 0 Then	 b.ANSWER Else '' End,',') sb
                            left outer join BDMT01 c (nolock) on a.SCHOOL_NO = c.SCHOOL_NO
                            where a.QUESTIONNAIRE_ID=@QUESTIONNAIRE_ID ";
            if (!string.IsNullOrWhiteSpace(whereCLASS_NO))
            {
                sSQL = sSQL + "and a.CLASS_NO='" + whereCLASS_NO + "'";
            }

            if (!string.IsNullOrWhiteSpace(whereSearch))
            {
                sSQL = sSQL + "and a.NAME like'%" + whereSearch + "%'";
            }
            if (!string.IsNullOrWhiteSpace(whereGrade))
            {
                sSQL = sSQL + "and  LEFT(a.CLASS_NO,1)='" + whereGrade + "'";
            }

            sSQL = sSQL + "  group by a.ANSWER_ID,a.QUESTIONNAIRE_ID,a.SCHOOL_NO,a.USER_NO,a.NAME,a.SNAME,a.ANSWER_DATE,a.STATUS,a.DEL_YN,a.CLASS_NO,a.SYEAR,a.SEMESTER,a.SEAT_NO,c.SHORT_NAME order by a.ANSWER_DATE desc";

            var Temp = db.Database.Connection.Query<ADDI10VotePeopleViewModel>(sSQL
          , new
          {
              QUESTIONNAIRE_ID = QUESTIONNAIRE_ID,
          });
            if (OrdercColumn == "SEAT_NO")
            {
                if (SyntaxName == "ASC")
                {
                    Temp = Temp.OrderBy(X => X.SEAT_NO);
                }
                else
                {
                    Temp = Temp.OrderByDescending(a => a.SEAT_NO);
                }
            }
            else if (OrdercColumn == "ANSWER_DATE")
            {
                if (SyntaxName == "ASC")
                {
                    Temp = Temp.OrderBy(X => X.ANSWER_DATE);
                }
                else
                {
                    Temp = Temp.OrderByDescending(X => X.ANSWER_DATE);
                }
            }
            else if (OrdercColumn == "CLASS_NO")
            {
                if (SyntaxName == "ASC")
                {
                    Temp = Temp.OrderBy(X => X.CLASS_NO);
                }
                else
                {
                    Temp = Temp.OrderByDescending(X => X.CLASS_NO);
                }
            }
            model.VotePeople = Temp.ToList();

            return model;
        }

        public ADDI10StatisticsVoteDetailsViewModel GetVotePeople(string QUESTIONNAIRE_ID, int Q_NUM, string ANSWER, string OrdercColumn, string SyntaxName, string whereGrade, string whereCLASS_NO, string WhereSearch, ref ECOOL_DEVEntities db)
        {
            ADDI10StatisticsVoteDetailsViewModel model = new ADDI10StatisticsVoteDetailsViewModel();

            model.Title = db.SAQT01.Where(a => a.QUESTIONNAIRE_ID == QUESTIONNAIRE_ID).FirstOrDefault();

            model.Topic = db.SAQT02.Where(a => a.QUESTIONNAIRE_ID == QUESTIONNAIRE_ID && a.Q_NUM == Q_NUM).FirstOrDefault();

            string sSQL = $@"select a.* ,c.SHORT_NAME,Case when sb.word<>'' Then sb.word Else b.ANSWER  end as  ANSWER
                            from SAQT04_M a (nolock)
                            join SAQT04 b (nolock) on a.QUESTIONNAIRE_ID =b.QUESTIONNAIRE_ID and a.ANSWER_ID=b.ANSWER_ID
                          OUTER apply dbo.udf_Split(case when (select count(*) from SAQT03 s3
                           where s3.QUESTIONNAIRE_ID=b.QUESTIONNAIRE_ID  and  s3.Q_NUM=b.Q_NUM
                        and s3.Q_INPUT_TYPE in ('{SAQT03.Q_INPUT_TYPEVal.radio}','{SAQT03.Q_INPUT_TYPEVal.checkbox}') ) > 0
                         Then	 b.ANSWER Else '' End,',') sb
                            left outer join BDMT01 c (nolock) on a.SCHOOL_NO = c.SCHOOL_NO
                            where a.QUESTIONNAIRE_ID=@QUESTIONNAIRE_ID
                            and b.Q_NUM=@Q_NUM  ";

            if (!string.IsNullOrWhiteSpace(ANSWER))
            {
                sSQL = sSQL + " and Case when sb.word<>'' Then sb.word Else b.ANSWER  end = @ANSWER ";
            }
            if (!string.IsNullOrWhiteSpace(whereCLASS_NO))
            {
                sSQL = sSQL + "and a.CLASS_NO='" + whereCLASS_NO + "'";
            }

            if (!string.IsNullOrWhiteSpace(WhereSearch))
            {
                sSQL = sSQL + "and a.NAME like'%" + WhereSearch + "%'";
            }
            if (!string.IsNullOrWhiteSpace(whereGrade))
            {
                sSQL = sSQL + "and  LEFT(a.CLASS_NO,1)='" + whereGrade + "'";
            }
            sSQL = sSQL + " order by a.ANSWER_DATE desc";
            var Temp = db.Database.Connection.Query<ADDI10VotePeopleViewModel>(sSQL
            , new
            {
                QUESTIONNAIRE_ID = QUESTIONNAIRE_ID,
                Q_NUM = Q_NUM,
                ANSWER = ANSWER,
            });
            if (OrdercColumn == "SEAT_NO")
            {
                if (SyntaxName == "ASC")
                {
                    Temp = Temp.OrderBy(X => X.SEAT_NO);
                }
                else
                {
                    Temp = Temp.OrderByDescending(a => a.SEAT_NO);
                }
            }
            else if (OrdercColumn == "ANSWER_DATE")
            {
                if (SyntaxName == "ASC")
                {
                    Temp = Temp.OrderBy(X => X.ANSWER_DATE);
                }
                else
                {
                    Temp = Temp.OrderByDescending(X => X.ANSWER_DATE);
                }
            }
            else if (OrdercColumn == "CLASS_NO")
            {
                if (SyntaxName == "ASC")
                {
                    Temp = Temp.OrderBy(X => X.CLASS_NO);
                }
                else
                {
                    Temp = Temp.OrderByDescending(X => X.CLASS_NO);
                }
            }
            model.VotePeople = Temp.ToList();

            model.ANSWER = ANSWER;
            model.GetSAQT03 = db.SAQT03.Where(x => x.QUESTIONNAIRE_ID == QUESTIONNAIRE_ID && x.Q_NUM== Q_NUM).FirstOrDefault();
            return model;
        }

        public bool SaveVote(ADDI10EditViewModel model, UserProfile user, ref ECOOL_DEVEntities db, ref string Message, HRMT01 hRMT01)
        {


            List<Tuple<string, string, int>> valuesList = new List<Tuple<string, string, int>>();
            SAQT04_M SaveUp = null;

            //預設值
            int SYear;
            int Semesters;
            SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);
          
            SaveUp = new SAQT04_M()
            {
                ANSWER_ID = Guid.NewGuid().ToString("N"),
                QUESTIONNAIRE_ID = model.Search.WhereQUESTIONNAIRE_ID,
                SCHOOL_NO = hRMT01.SCHOOL_NO,
                USER_NO = hRMT01.USER_NO,
                NAME = hRMT01.NAME,
                SNAME = hRMT01.SNAME,
                ANSWER_DATE = DateTime.Now,
                STATUS = SAQT04_M.STATUS_Val.OK,
                DEL_YN = SharedGlobal.N,
                CLASS_NO = hRMT01.CLASS_NO,
                SEAT_NO = hRMT01.SEAT_NO,
                SEMESTER = Convert.ToByte(Semesters),
                SYEAR = Convert.ToByte(SYear)
            };
            db.SAQT04_M.Add(SaveUp);

            foreach (var item in model.Topic)
            {
                SAQT04 Cre = new SAQT04();
                 Cre.ANSWER_ID = SaveUp.ANSWER_ID;
                Cre.QUESTIONNAIRE_ID = SaveUp.QUESTIONNAIRE_ID;
                Cre.Q_NUM = (int)item.Q_NUM;
                string Q_INPUT_TYPE = "";
                Q_INPUT_TYPE = db.SAQT03.Where(x => x.QUESTIONNAIRE_ID == SaveUp.QUESTIONNAIRE_ID && x.Q_NUM == (int)item.Q_NUM).Select(x => x.Q_INPUT_TYPE).FirstOrDefault();
                string FileNAME = "";
                if (item.ANSWER == "System.Web.HttpPostedFileWrapper")
                {
                    FileNAME = UpLoadFile(hRMT01.SCHOOL_NO, SaveUp.ANSWER_ID, SaveUp.QUESTIONNAIRE_ID, item.UploadAnswerFile, ref Message, Q_INPUT_TYPE);
             
                    Cre.ANSWER = FileNAME;
                    if (string.IsNullOrWhiteSpace(Message) == false)
                    {
                        return false;
                    }
                }
                else {


                    Cre.ANSWER = item.ANSWER;
              
                }
                db.SAQT04.Add(Cre);
            }

            if (model.Title.CASH != null)
            {
                if (hRMT01.USER_TYPE == UserType.Student)
                {

                    bool ISvotes = false;
                    //判斷是否有投過票
                    ISvotes= db.SAQT04_M.Where(x => x.QUESTIONNAIRE_ID == SaveUp.QUESTIONNAIRE_ID && x.SCHOOL_NO == hRMT01.SCHOOL_NO && x.USER_NO == hRMT01.USER_NO).Any();
                    if (!ISvotes) { 
                    CashHelper.AddCash(user, (int)model.Title.CASH, hRMT01.SCHOOL_NO, hRMT01.USER_NO, "ADDI10", SaveUp.ANSWER_ID, "投票系統投票獲得點教", true, ref db,"","",ref valuesList);
                    }
                }
                else if (hRMT01.USER_TYPE == UserType.Teacher || hRMT01.USER_TYPE == UserType.Admin)
                { bool ISvotes = false;
                    //判斷是否有投過票
                    ISvotes = db.SAQT04_M.Where(x => x.QUESTIONNAIRE_ID == SaveUp.QUESTIONNAIRE_ID && x.SCHOOL_NO == hRMT01.SCHOOL_NO && x.USER_NO == hRMT01.USER_NO).Any();
                    if (!ISvotes)
                    {
                        CashHelper.TeachAddCash(user, (int)model.Title.CASH, hRMT01.SCHOOL_NO, hRMT01.USER_NO, "ADDI10", SaveUp.ANSWER_ID, "投票系統投票獲得點教", true, null, ref db);
                    }
                }
            }

            try
            {
                db.SaveChanges();
                return true;
            }
            catch (Exception ex)
            {
                Message = "系統發生錯誤;原因:" + ex.Message;
                return false;
            }
        }
        public string UpLoadFile(string SCHOOL_NO, string ANSWER_ID,string QUESTIONNAIRE_ID, HttpPostedFileBase file, ref string Message, string WORK_TYPE = null)
        {

            string tempPath = GetSysVotePath(SCHOOL_NO, QUESTIONNAIRE_ID, ANSWER_ID);

            if (Directory.Exists(tempPath) == false)
            {
                Directory.CreateDirectory(tempPath);
            }
            if (file != null && file.ContentLength > 0)
            {
                if (!string.IsNullOrWhiteSpace(WORK_TYPE) && WORK_TYPE == SAQT03.Q_INPUT_TYPEVal.UploadWorld)
                {
                    Regex reg = new Regex(@".*\.(doc|docx)");
                    if (reg.IsMatch(file.FileName.ToLower()) == false)
                    {
                        Message = "請上傳 doc、 docx 等格式檔案";
                        return file.FileName;
                    }
                }
                else if(!string.IsNullOrWhiteSpace(WORK_TYPE) && WORK_TYPE == SAQT03.Q_INPUT_TYPEVal.UploadImage)
                {
                    Regex regexcode = new Regex(@".*\.(jpg|jpeg|png|gif|bmp|jfif|webp)");
                    if (regexcode.IsMatch(file.FileName.ToLower()) == false)
                    {
                        Message = "請上傳 jpg、jpeg、png、gif、bmp、jfif、webp等格式的圖片";
                        return file.FileName;

                    }

                }
            }
            
            if (file.ContentLength / 1024 > (1024 * 10)) // 20MB
            {
                Message = "上傳檔案不能超過10MB";
                return file.FileName;
            }
            string UpLoadFile = tempPath + "\\" + file.FileName;

            if (System.IO.File.Exists(UpLoadFile))
            {
                System.IO.File.Delete(UpLoadFile);
            }
            file.SaveAs(UpLoadFile);
            if (!string.IsNullOrWhiteSpace(WORK_TYPE) && WORK_TYPE == SAQT03.Q_INPUT_TYPEVal.UploadImage)
            {
                SaveImageM(file, UpLoadFile);
                SaveImageS(file, UpLoadFile);
              
            }

           
             
            return file.FileName;
        }
        private void SaveImageM(HttpPostedFileBase file, string UpLoadFile)
        {
            string Extension = Path.GetExtension(UpLoadFile);
            string NewPath = UpLoadFile.Replace(Extension, "_M" + Extension);

            SaveThumbnailImage(file, 1600, 1600, NewPath);
        }
        private void SaveImageS(HttpPostedFileBase file, string UpLoadFile)
        {
            string Extension = Path.GetExtension(UpLoadFile);
            string NewPath = UpLoadFile.Replace(Extension, "_S" + Extension);

            SaveThumbnailImage(file, 200, 150, NewPath);
        }
        public void SaveThumbnailImage(HttpPostedFileBase file, double FixWidth, double FixHeight, string NewImagePath)
        {
            System.Drawing.Image image = System.Drawing.Image.FromStream(file.InputStream);

            double rate = 1;
            if (image.Width > FixWidth || image.Height > FixHeight)
            {
                if (image.Width > FixWidth) rate = FixWidth / image.Width;
                else if (image.Height * rate > FixHeight) rate = FixHeight / image.Height;

                int w = Convert.ToInt32(image.Width * rate);
                int h = Convert.ToInt32(image.Height * rate);
                Bitmap imageOutput = new Bitmap(image, w, h);
                imageOutput.Save(NewImagePath, image.RawFormat);
                imageOutput.Dispose();
            }
            else
            {
                //直接儲存
                file.SaveAs(NewImagePath);
            }

            image.Dispose();
        }
        public void CopySingleFiles(string remotePath, string localPath, string tempPath3tempVirtual, string Nickname, string Q_INPUT_TYPE,List<string>fileName)
        {
            //FileInfo[] file = new FileInfo[fileName.Count()];
            //List<FileInfo> tempList = new List<FileInfo>();

            //foreach (string item in fileName)
            //{
            //    FileInfo myInfo = new FileInfo(remotePath + item);
            //    if (myInfo.Exists)
            //    {
                   

            //        tempList.Add(item);
            //    }
            //}
            //file = tempList.ToArray();
            for (int i = 0; i < fileName.Count(); i++)
            {
                string fileNames = remotePath + @"\" + fileName[i].ToString();
                string desFileName = localPath + @"\" + fileName[i].ToString();
                string pattern = @"_M";
                if (Q_INPUT_TYPE == "UploadImage")
                {

                    pattern = @"_M";
                }
                else
                {

                    pattern = "";
                }
                Match match = Regex.Match(fileName[i], pattern);
                if (match.Success)
                {
                    string VirtualPath = "";
                    string newFZIPNAME = "";
                    int j = 0;
                    VirtualPath = tempPath3tempVirtual + @"\" + Nickname + Path.GetExtension(fileName[i].ToString());
                    if (System.IO.File.Exists(System.Web.HttpContext.Current.Server.MapPath(VirtualPath)))
                    {
                        string VirtualPath1 = "";
                        VirtualPath1 = tempPath3tempVirtual + @"\" + Nickname + @"_" + j.ToString() + Path.GetExtension(fileName[i].ToString());
                        if (System.IO.File.Exists(System.Web.HttpContext.Current.Server.MapPath(VirtualPath1)))
                        {
                            while (System.IO.File.Exists(System.Web.HttpContext.Current.Server.MapPath(VirtualPath1)))

                            {
                                j = j + 1;
                                VirtualPath1 = tempPath3tempVirtual + @"\" + Nickname + @"_" + j.ToString() + Path.GetExtension(fileName[i].ToString());
                            }
                            string PhisicalPayh = "";
                            PhisicalPayh = localPath + @"\" + Nickname + @"_" + j.ToString() + Path.GetExtension(fileName[i].ToString());
                            FileInfo f1 = new FileInfo(fileNames);
                            f1.CopyTo(PhisicalPayh);

                            System.Threading.Thread.Sleep(500);
                        }

                        else
                        {

                            VirtualPath1 = localPath + @"\" + Nickname + @"_" + j.ToString() + Path.GetExtension(fileName[i].ToString());
                            FileInfo f1 = new FileInfo(fileNames);
                            f1.CopyTo(VirtualPath1);
                            System.Threading.Thread.Sleep(500);

                        }




                    }
                    else
                    {

                        desFileName = localPath + @"\" + Nickname + Path.GetExtension(fileName[i].ToString());

                        File.Copy(fileNames, desFileName, false);
                        System.Threading.Thread.Sleep(500);
                    }
                }
                else
                {

                    string VirtualPath = "";
                    string NewPath = "";
                    string VirtualPath1 = "";
                    int j = 0;
                   
                    //查看檔案_M是否存在，不存在拿原本的


                    string strFileName = Path.GetFileNameWithoutExtension(fileName[i].ToString());
                    string strFileExtension = Path.GetExtension(fileName[i].ToString());
                    //改成拿_M
                    NewPath = tempPath3tempVirtual + @"\" + strFileName + "_M" + strFileExtension;

                    if (System.IO.File.Exists(System.Web.HttpContext.Current.Server.MapPath(NewPath)))
                    {
                        fileNames = remotePath + @"\" + strFileName + "_M" + strFileExtension;
                    }
                    //改名
                    VirtualPath = tempPath3tempVirtual + @"\" + Nickname + @"_" + j.ToString() + Path.GetExtension(fileName[i].ToString());
                        //查看要copy 的檔案是否有重複
                        if (System.IO.File.Exists(System.Web.HttpContext.Current.Server.MapPath(VirtualPath)))
                        {
                            while (System.IO.File.Exists(System.Web.HttpContext.Current.Server.MapPath(VirtualPath)))

                            {
                                j = j + 1;
                                VirtualPath = tempPath3tempVirtual + @"\" + Nickname + @"_" + j.ToString() + Path.GetExtension(fileName[i].ToString());
                            }
                            string PhisicalPayh = "";
                            PhisicalPayh = localPath + @"\" + Nickname + @"_" + j.ToString() + Path.GetExtension(fileName[i].ToString());

                            FileInfo f1 = new FileInfo(fileNames);
                            f1.CopyTo(PhisicalPayh);

                            System.Threading.Thread.Sleep(500);
                        }

                        else
                        {

                            VirtualPath1 = localPath + @"\" + Nickname + @"_" + j.ToString() + Path.GetExtension(fileName[i].ToString());
                            FileInfo f1 = new FileInfo(fileNames);
                            f1.CopyTo(VirtualPath1);
                            System.Threading.Thread.Sleep(500);

                        }
                  
                   

                }
            }
        }


       public void CopyFiles(string remotePath, string localPath, string tempPath3tempVirtual,string Nickname,string Q_INPUT_TYPE)
        {
            FileInfo[] file = GetFileList(remotePath);
            for (int i = 0; i < file.Length; i++)
            {
                string fileName = remotePath + @"\" + file.GetValue(i).ToString();
                string desFileName = localPath + @"\" + file.GetValue(i).ToString();
                string pattern = @"_M";
                if (Q_INPUT_TYPE == "UploadImage")
                {

                    pattern = @"_M";
                }
                else {

                    pattern = "";
                }
                Match match = Regex.Match(file.GetValue(i).ToString(), pattern);
                if (match.Success)
                { 
                string VirtualPath = "";
                    string newFZIPNAME = "";
                    int j = 0;
                VirtualPath = tempPath3tempVirtual + @"\"+ Nickname + Path.GetExtension(file.GetValue(i).ToString()); 
                if (System.IO.File.Exists(System.Web.HttpContext.Current.Server.MapPath(VirtualPath)))
                {
                        string VirtualPath1 = "";
                        VirtualPath1 = tempPath3tempVirtual + @"\" + Nickname+ @"_" + j.ToString() + Path.GetExtension(file.GetValue(i).ToString());
                        if (System.IO.File.Exists(System.Web.HttpContext.Current.Server.MapPath(VirtualPath1)))
                        {
                            while (System.IO.File.Exists(System.Web.HttpContext.Current.Server.MapPath(VirtualPath1)))

                            {
                                j = j + 1;
                                VirtualPath1 = tempPath3tempVirtual + @"\" + Nickname + @"_" + j.ToString() + Path.GetExtension(file.GetValue(i).ToString());
                            }
                            string PhisicalPayh = "";
                            PhisicalPayh = localPath + @"\" + Nickname + @"_" + j.ToString() + Path.GetExtension(file.GetValue(i).ToString());
                            FileInfo f1 = new FileInfo(fileName);
                            f1.CopyTo(PhisicalPayh);
                            
                            System.Threading.Thread.Sleep(500);
                        }

                        else {

                            VirtualPath1 = localPath + @"\" + Nickname + @"_" + j.ToString() + Path.GetExtension(file.GetValue(i).ToString());
                            FileInfo f1 = new FileInfo(fileName);
                            f1.CopyTo(VirtualPath1);
                            System.Threading.Thread.Sleep(500);

                        }




                    }
                else {

                    desFileName = localPath + @"\"+Nickname + Path.GetExtension(file.GetValue(i).ToString());

                    File.Copy(fileName, desFileName, false);
                    System.Threading.Thread.Sleep(500);
                }
                }

            }
        }
        private FileInfo[] GetFileList(string path)
        {
            FileInfo[] fileList = null;
            if (Directory.Exists(path))
            {
                DirectoryInfo di = new DirectoryInfo(path);
                fileList = di.GetFiles();
            }

            return fileList;
        }
        public string GetSetArtGalleryPath()
        {
            string ReturnImgUrl = string.Empty;

            string UploadImageRoot = System.Web.Configuration.WebConfigurationManager.AppSettings["UploadImageRoot"];
            if (UploadImageRoot == null) UploadImageRoot = @"~\Content\";

            string Vote = "Vote";
            ReturnImgUrl = $@"{UploadImageRoot}{Vote}\";

            return ReturnImgUrl;
        }

        public string GetVirtalSysVotePath(string SCHOOL_NO, string QUESTIONNAIRE_ID, string ANSWER_ID)
        {

            if (string.IsNullOrWhiteSpace(ANSWER_ID))
            {
                return GetSetArtGalleryPath() + SCHOOL_NO + @"\" + QUESTIONNAIRE_ID ;
            }
            else {
                return GetSetArtGalleryPath() + SCHOOL_NO + @"\" + QUESTIONNAIRE_ID + @"\" + ANSWER_ID + @"\";

            }
           
        }
        public string GetSysVotePath(string SCHOOL_NO, string QUESTIONNAIRE_ID,string ANSWER_ID)
        {
            if (string.IsNullOrWhiteSpace(ANSWER_ID))
            {

                return HttpContext.Current.Server.MapPath(GetSetArtGalleryPath()) + SCHOOL_NO + @"\" + QUESTIONNAIRE_ID ;

            }
            else {

                return HttpContext.Current.Server.MapPath(GetSetArtGalleryPath()) + SCHOOL_NO + @"\" + QUESTIONNAIRE_ID + @"\" + ANSWER_ID + @"\";
            }
              
        }

        public string GetSysVirtauiVotePath(string SCHOOL_NO, string QUESTIONNAIRE_ID, string ANSWER_ID)

        {
            if (string.IsNullOrWhiteSpace(ANSWER_ID))
            {

                return GetSetArtGalleryPath() + SCHOOL_NO + @"\" + QUESTIONNAIRE_ID;

            }
            else
            {

                return GetSetArtGalleryPath() + SCHOOL_NO + @"\" + QUESTIONNAIRE_ID + @"\" + ANSWER_ID + @"\";
            }
        }
        public string GetDirectorySysArtGalleryPath(string SCHOOL_NO, string QUESTIONNAIRE_ID, string ANSWER_ID, string FileName)
        {
            string TempPath = GetSetArtGalleryPath() + SCHOOL_NO + @"\" + QUESTIONNAIRE_ID + @"\" + ANSWER_ID + @"\"+ FileName;

            if (System.IO.File.Exists(HttpContext.Current.Server.MapPath(TempPath)))
            {
                return Uri.EscapeUriString(UrlCustomHelper.Url_Content(TempPath));
            }

            return string.Empty;
        }

        
        public bool IsCheckVote(string QUESTIONNAIRE_ID, UserProfile user, ref ECOOL_DEVEntities db)
        {
            int? ANSWER_COUNT = 0;
            ANSWER_COUNT = db.SAQT01.Where(x => x.QUESTIONNAIRE_ID == QUESTIONNAIRE_ID).Select(x => x.ANSWER_COUNT).FirstOrDefault();
            if (ANSWER_COUNT > 1)
            {
                int SAQT04_MANSCount = 0;
                SAQT04_MANSCount = db.SAQT04_M.Where(a => a.QUESTIONNAIRE_ID == QUESTIONNAIRE_ID && a.SCHOOL_NO == user.SCHOOL_NO && a.USER_NO == user.USER_NO).Count();
                if (SAQT04_MANSCount < ANSWER_COUNT)
                {
                    return false;
                }
                else {
                    return true;
                }
            }
            return db.SAQT04_M.Where(a => a.QUESTIONNAIRE_ID == QUESTIONNAIRE_ID && a.SCHOOL_NO == user.SCHOOL_NO && a.USER_NO == user.USER_NO).Any();
        }

        public bool IsCheckVote(string QUESTIONNAIRE_ID, string SCHOOL_NO, string USER_NO, ref ECOOL_DEVEntities db)
        {
            int? ANSWER_COUNT = 0;
            ANSWER_COUNT = db.SAQT01.Where(x => x.QUESTIONNAIRE_ID == QUESTIONNAIRE_ID).Select(x => x.ANSWER_COUNT).FirstOrDefault();
            if (ANSWER_COUNT > 1)
            {
                int SAQT04_MANSCount = 0;
                SAQT04_MANSCount = db.SAQT04_M.Where(a => a.QUESTIONNAIRE_ID == QUESTIONNAIRE_ID && a.SCHOOL_NO == SCHOOL_NO && a.USER_NO ==USER_NO).Count();
                if (SAQT04_MANSCount < ANSWER_COUNT)
                {
                    return false;
                }
                else
                {
                    return true;
                }
            }
            return db.SAQT04_M.Where(a => a.QUESTIONNAIRE_ID == QUESTIONNAIRE_ID && a.SCHOOL_NO == SCHOOL_NO && a.USER_NO == USER_NO).Any();
        }
    }
}