﻿
using DotNet.Highcharts;
using MvcPaging;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;

namespace ECOOL_APP.EF
{
    public class AWAI07IndexViewModel
    {

        public AWAI07SearchViewModel Search { get; set; }

        /// <summary>
        /// 一頁筆數 預設筆數
        /// </summary>
        public int PageSize { get; set; }

        /// <summary>
        /// 查詢結果
        /// </summary>
        public IPagedList<AWAI07ListViewModel> ListData;

        /// <summary>
        /// 活存
        /// </summary>
        public decimal? Demand_Deposit { get; set; }

        /// <summary>
        /// 定存
        /// </summary>
        public decimal? Time_Deposit { get; set; }


        public Highcharts PieChart;

        public AWAI07IndexViewModel()
        {
            PageSize = PageGlobal.DfTeamPageSize;
        }
    }
}