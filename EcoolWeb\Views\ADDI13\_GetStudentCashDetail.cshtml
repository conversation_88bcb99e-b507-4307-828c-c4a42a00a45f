﻿@model ADDI13IndexViewModel
@{
    ViewBag.Title = "點數領取-列表";
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";

    string SCHOOL_NO = EcoolWeb.Models.UserProfileHelper.GetSchoolNo();

    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    string LogoAct = "GuestIndex";

}
@Html.Partial("_Notice")
@*<center style="white-space: nowrap;font-size: 30pt;font-weight: bold;">
        程式修正中，請12月20日再兌換，造成不便，請包涵
    </center>

    <img src="~/Content/images/Sorry.PNG"  style="width:50%" class="img-responsive " alt="Responsive image" />*@

<div class="row" id="PersonalDivContent">
    <div class="col-sm-2 col-xs-12">
        <div>
            @if (!string.IsNullOrEmpty(Model.PlayerUrl))
            {
                <img src="@(Url.Content(Model.PlayerUrl)+ "?refreshCache=" + DateTime.Now.ToString("mmddss"))" class="imgEZ" style="margin-top:30px;max-width:90%;" />
            }
        </div>
    </div>
    <div class="col-sm-10 col-xs-12">


        <div>
            @if ("ADDI13" == "ADDI13")
            {
                <div style="height:25px"></div>
                if (!AppMode)
                {
                    <button type="button" class="btn btn-sm btn-bold btn-pink" onclick="printScreenH()">我要列印</button>
                }
                <button type="button" class="btn btn-sm btn-bold btn-pink" title="個人化編輯" id="MyPHOTO" href="@Url.Action("MyPhoto", (string)ViewBag.BRE_NO)">個人化編輯</button>
                <button type="button" class="btn btn-sm btn-bold btn-pink" title="點數分析" id="PieChartbtn" href="@Url.Action("_PieChartDiv", (string)ViewBag.BRE_NO)">點數分析</button>
                <button type="button" class="btn btn-sm btn-bold btn-pink" title="加值應用統計" id="Statisticalbtn" href="@Url.Action("_StatisticalDiv", (string)ViewBag.BRE_NO)">加值應用統計</button>
                if (!AppMode)
                {
                    <button type="button" class="btn btn-sm btn-bold btn-pink" onclick="GameCash()">取得點數</button>
                }
                else
                {

                    <button type="button" class="btn btn-sm btn-bold btn-pink" title="取得點數" id="GameCash" href="@(Url.Action("GetStudentCashIndex", "ADDI13") + "?PRINT=Y&SCHOOL_NO1="+user.SCHOOL_NO)">取得點數</button>
                }
                if (Model.wIsQhisSchool && ViewBag.IsUseZZZI09 == true)
                {
                    <button type="button" class="btn btn-sm btn-bold btn-pink" title="學習成果匯出(過去學校)" id="ExportResult" onclick="ExportResultWinOpen()">學習成果匯出(過去學校)</button>
                }

                if (Model.DATA_ANGLE_TYPE != EcoolWeb.Models.UserProfileHelper.AngleVal.OneData)
                {
                    if (ViewBag.IsUseZZZI09 == true)
                    {
                        <button type="button" class="btn btn-sm btn-bold btn-pink" title="學習成果匯出" id="ExportResult" onclick="ExportResultWinOpen()">學習成果匯出</button>
                    }
                }
                else
                {
                    <button type="button" class="btn btn-sm btn-bold btn-pink" title="我的線上投稿" onclick="funBookW()">我的線上投稿</button>


                    <a role="button" href='@Url.Action("BOOK_APPLY", (string)ViewBag.BRE_NO)' class="btn btn-sm btn-bold btn-pink">我的閱讀認證</a>
                    <a role="button" href='@Url.Action("Index3", (string)ViewBag.BRE_NO)' id="MyMOMO" class="btn btn-sm btn-bold btn-pink">我的默默等級</a>
                    <div style="display:none">
                        @{
                            int No = 1;
                            string IdName = string.Empty;

                            <div class="arrWRITING_NO">
                                @foreach (var item in Model.arrWRITING_NO)
                                {
                                    IdName = "W" + No.ToString();

                                    <a id="@IdName" class="groupWRITING_NO" href='@Url.Action("BOOK_Details", "ADDI01" , new {  WRITING_NO = item})'>
                                        @item
                                    </a>
                                    No++;
                                }
                            </div>

                            No = 1;

                            <div class="arrAPPLY_NO">
                                @foreach (var item in Model.arrAPPLY_NO)
                                {
                                    IdName = "A" + No.ToString();

                                    <a id="@IdName" class="groupAPPLY_NO" href='@Url.Action("BOOK_Details", "ADDI01" , new {  WRITING_NO = item})'>
                                        @item
                                    </a>

                                    No++;
                                }
                            </div>

                        }
                    </div>
                }
            }
        </div>

    </div>



</div>
@using (Html.BeginForm("Index1", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", id = "form1", @AutoComplete = "Off", @target = "_blank" }))
{
    @Html.AntiForgeryToken()
    //@Html.HiddenFor(m => m.ROLL_CALL_ID)
    @Html.HiddenFor(m => m.SCHOOL_NO1)
    <div class="row">
        <div class="col-md-8 col-md-offset-2">
            <div class="panel with-nav-tabs panel-info" id="panel">
                <div class="panel-heading">
                    <h1>點數領取</h1>
                </div>
                <div class="panel-body">
                    <div>
                        <div class="input-group input-group-lg">
                            <span class="input-group-addon"><i class="fa fa-user"></i></span>

                            @Html.EditorFor(m => m.CARD_NO, new { htmlAttributes = new { @class = "form-control", @placeholder = "請輸入紙張裡的+66+66" +
                           "12碼數字", @onKeyPress = "call(event,this);" } })


                        </div>
                        @Html.ValidationMessage("CARD_NO", "", new { @class = "text-danger" })
                    </div>
                    <input type="button" value="送出" onclick="OnclickCardNO()" />
                </div>
            </div>

        </div>
    </div>
    <div id="DetailsView">

        <div id="editorRows" class="tbody">
            @if (Model != null && Model.Details != null && Model.Details.Count() > 0)
            {
                @Html.Action("_EditDetailsList", (string)ViewBag.BRE_NO, new { model = Model.Details.ToList() })
            }
        </div>
    </div>
    <div style="width:100%;height:100%;background-color:@SharedGlobal.Logo_loading_background_color;display:none;z-index:999;position:fixed;left:0;top:0" id="loading" class="challenge-loading">
        <div style="margin: 0px auto;text-align:center">
            <img src="@Url.Content(SharedGlobal.Logo_loading)" style="max-height:350px;" />
            <br />


            <h3 style="color:#80b4fb">讀取中…</h3>

        </div>
    </div>
    <div class="row">
        <div class="col-lg-6 col-lg-offset-3">
            <div class="use-absolute" id="ErrorDiv">
                <div class="use-absoluteDiv">
                    <div class="alert alert-danger" role="alert">
                        <h1>
                            <i class="fa fa-exclamation-circle"></i>
                            <strong id="ErrorStr"></strong>
                        </h1>
                    </div>
                </div>
            </div>
        </div>
    </div>
}

