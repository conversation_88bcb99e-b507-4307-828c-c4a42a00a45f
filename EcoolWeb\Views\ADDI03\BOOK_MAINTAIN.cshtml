﻿@model ADDT03
@{
    ViewBag.Title = "閱讀書籍-維護";
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    if (string.IsNullOrWhiteSpace(UUID) == false)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

<div class="table-92Per">
    <label class="label_dt">說明</label>
    <br />
    <label class="label_dt">1.若有修正共讀本書目，注意原本書目的標號不能更動，以免造成混亂。 例如</label><br />
    <label class="label_dt">
        不要將書目小王子編號由105改成102。
    </label><br />
    <label class="label_dt">2.書目可以異動，建議開學前(或正式使用前)完成。</label><br />
    <label class="label_dt">3.流水編號很重要，系統以此編排位置和紀錄歷程。一年級請設定 101、102、</label><br />
     <label class="label_dt">103.....，二年級請設定 101、102、103.....</label>
</div>

<br />

@using (Html.BeginForm(null, null, FormMethod.Post, new { name = "contentForm", id = "contentForm" }))
{
    @Html.Hidden("BOOK_Status", ViewData["BookStatus"])
    <img src="~/Content/img/web-bar2-revise-05.png" style="width:100%" class="img-responsive App_hide" alt="Responsive image" />
    <div class="Div-EZ-rpp">
        <div class="form-horizontal">
            <div class="form-group">
                @Html.Label("年級", htmlAttributes: new { @class = "control-label label_dt col-md-3 col-sm-3 col-lg-2" })
                <div class="ccol-md-9 col-sm-9 col-lg-10">
                    @if (ViewData["BookStatus"].ToString() == "DEL")
                    {
                        @Html.DropDownList("GRADE", (IEnumerable<SelectListItem>)ViewBag.GRADEItem, new { @class = "form-control", @disabled = "disabled" })
                    }
                    else if (ViewData["BookStatus"].ToString() != "ADD" && ViewData["BookStatus"].ToString() != "DEL")
                    {


                        @Html.DropDownList("GRADE", (IEnumerable<SelectListItem>)ViewBag.GRADEItem, new { @class = "form-control" })

                    }
                    else
                    {
                        @Html.DropDownList("GRADE", (IEnumerable<SelectListItem>)ViewBag.GRADEItem, new { @class = "form-control" ,@onchange="GetMax();"})
                    }
                </div>
            </div>
            <div class="form-group">
                @Html.Label("流水編號", htmlAttributes: new { @class = "control-label label_dt col-md-3 col-sm-3 col-lg-2" })
                <div class="ccol-md-9 col-sm-9 col-lg-10">
                    @if (ViewData["BookStatus"].ToString() != "ADD")
                    {
                        @Html.TextBoxFor(model => model.BOOK_ID, new { @disabled = "disabled", @class = "form-control" })
                    }
                    else
                    {
                        @Html.TextBoxFor(model => model.BOOK_ID, new { @class = "form-control" })
                    }

                    @Html.Hidden("hidBOOK_ID", Model.BOOK_ID)
                </div>
            </div>
            <div class="form-group">
                @Html.Label("書名", htmlAttributes: new { @class = "control-label label_dt col-md-3 col-sm-3 col-lg-2" })
                <div class="ccol-md-9 col-sm-9 col-lg-10">
                    @if (ViewData["BookStatus"].ToString() == "DEL")
                    {
                        @Html.TextBoxFor(model => model.BOOK_NAME, new { @disabled = "disabled", @class = "form-control" })
                    }
                    else
                    {
                        @Html.TextBoxFor(model => model.BOOK_NAME, new { @class = "form-control" })
                    }
                </div>
            </div>
            <div class="form-group text-center">
                <button class="btn btn-default" type="button" id="btnSend" name="btnSend" onclick="btnSend_onclick();">
                    確定送出

                </button>
                <button class="btn btn-default" type="button" id="btnCancel" onclick="btnCancel_onclick();">
                    放棄編輯
                </button>
            </div>
        </div>
    </div>

}
@section Scripts {
    <script nonce="cmlvaw">
        // 設置全局配置
        window.ADDI03_CONFIG = {
            maxBookUrl: '@Url.Action("MaxBook")',
            checkBookIdUrl: '@Url.Action("GetBookStatus")',
            getBookIsModifyUrl: '@Url.Action("GetBookisMODIFY")',
            checkBookCountUrl: '@Url.Action("CheckBookCount")'
        };
    </script>
    <script src="~/Scripts/ADDI03/book-maintain.js" nonce="cmlvaw"></script>
}
