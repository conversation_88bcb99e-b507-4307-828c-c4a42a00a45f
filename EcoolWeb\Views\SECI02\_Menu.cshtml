﻿@{
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
}
<div class="form-group">

    <a role="button" href='@Url.Action("Index",(string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys @(ViewBag.NowAction=="Index" ? "active":"")">
        點數統計
    </a>

    <a href='@Url.Action("ReadIndex",(string)ViewBag.BRE_NO)' role="button" class="btn btn-sm btn-sys @(ViewBag.NowAction=="ReadIndex" ? "active":"")">
        投稿統計
    </a>

    <a href="@Url.Action("UseIndex",(string)ViewBag.BRE_NO)" role="button" class="btn btn-sm btn-sys @(ViewBag.NowAction=="UseIndex" ? "active":"")">
        使用統計
    </a>

    @if ((user != null ? (byte)user.ROLE_TYPE : HRMT24_ENUM.RoleTypeVal.VisitorsLevel) <= HRMT24_ENUM.RoleTypeVal.AdminLevel)
    {
        <a href="@Url.Action("UseSituationIndex",(string)ViewBag.BRE_NO)" role="button" class="btn btn-sm btn-sys @(ViewBag.NowAction=="UseSituationIndex" ? "active":"")">
            各校使用情形
        </a>
    }
    else
    {
        <a href="@Url.Action("UseSituationIndex",(string)ViewBag.BRE_NO)" role="button" class="btn btn-sm btn-sys @(ViewBag.NowAction=="UseSituationIndex" ? "active":"")">
            各班使用情形
        </a>
    }

    <a href="@Url.Action("CareIndex",(string)ViewBag.BRE_NO)" role="button" class="btn btn-sm btn-sys @(ViewBag.NowAction=="CareIndex" ? "active":"")">
        關懷清單(登入)
    </a>
</div>