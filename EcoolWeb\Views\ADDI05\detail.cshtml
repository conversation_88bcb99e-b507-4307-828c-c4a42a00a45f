@model ECOOL_APP.com.ecool.Models.DTO.ADDI05DetailViewModel
@using System.Text.RegularExpressions;
@{
    ViewBag.Title = ViewBag.Panel_Title;
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }

    if (ViewBag.PreviewY == "Y")
    {
        Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
    }

    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
}

<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>

@if (ViewBag.PreviewY != "Y")
{
    @Html.Partial("_Title_Secondary")
    @Html.Partial("_Notice")
    <a class="btn btn-sm btn-sys" role="button" onclick="onGo('Index')">
        回有獎徵答首頁
    </a>
}

<img src="~/Content/img/web-bar2-revise-07.png" class="img-responsive App_hide" alt="Responsive image" />
<div class="Div-EZ-ADDI05">
    <div class="Details">
        <div class="dl-horizontal-EZ">
            <samp class="dt">
                @Html.DisplayNameFor(model => model.uADDT11.DIALOG_NAME)
            </samp>
            <samp class="dd">
                @Html.DisplayFor(model => model.uADDT11.DIALOG_NAME)
            </samp>
        </div>
        <div class="dl-horizontal-EZ">
            <samp class="dt">
                @Html.DisplayNameFor(model => model.uADDT11.SNAME)
            </samp>
            <samp class="dd">
                @Html.DisplayFor(model => model.uADDT11.SNAME)
            </samp>
        </div>
        @if (Model.uADDT11.ANSWER_COUNT != null)
        {
            <div class="dl-horizontal-EZ">
                <samp class="dt">
                    @Html.DisplayNameFor(model => model.uADDT11.ANSWER_COUNT)
                </samp>
                <samp class="dd">
                    @Html.DisplayFor(model => model.uADDT11.ANSWER_COUNT)
                </samp>
            </div>
        }
        @if ((Model.uADDT11.ANSWER_PERSON_YN ?? "N") == "Y")
        {
            <div class="dl-horizontal-EZ">
                <samp class="dt">
                    @Html.DisplayNameFor(model => model.uADDT11.ANSWER_PERSON_YN)
                </samp>
                <samp class="dd">
                    @Html.DisplayFor(model => model.uADDT11.ANSWER_PERSON_YN)
                    (@ECOOL_APP.com.ecool.util.StringHelper.LeftStringR(Model.uADDT11.ANSWER_PERSON, 50))
                </samp>
            </div>
        }
        <div class="dl-horizontal-EZ">
            <samp class="dt">
                詳細活動內容
            </samp>
        </div>
        <div style="height:15px"></div>
        <div class="p-context">
            @Html.DisplayFor(model => model.uADDT11.DIALOG_EXPRESS)

            @if (Model.FILE != null)
            {
                if (Model.FILE.Count > 0)
                {
                    Regex regexCode = new Regex(@".*\.(jpg|jpeg|png|gif|bmp)");
                    <p>
                        <samp>附件下載:</samp>
                        @foreach (var FILEItem in Model.FILE)
                        {
                            if (regexCode.IsMatch(FILEItem.FILE_NAME.ToLower()))
                            {
                                string ImgSrc = Url.Action("DownLoad", "ADDI05", new { DIALOG_ID = FILEItem.DIALOG_ID, name = FILEItem.FILE_NAME });

                                <img src='@ImgSrc' style="max-width:300px" class="img-responsive" href="@ImgSrc" />
                            }
                            else
                            {
                                @Html.ActionLink(FILEItem.FILE_NAME, "DownLoad", new { controller = (string)ViewBag.BRE_NO, DIALOG_ID = FILEItem.DIALOG_ID, name = FILEItem.FILE_NAME }, new { @class = "btn btn-link" })
                            }
                        }
                    </p>
                }

            }
            @*<p>
                    識別碼： @Model.uADDT11.DIALOG_ID
                </p>*@
        </div>
        <div class="Div-btn-center">

            <a class="btn btn-default btn-info" role="button" onclick="onGo('AnswerList')" @((ViewBag.PreviewY == "Y" || (ViewBag.userType == ECOOL_APP.EF.UserType.Student)) ? "disabled" : "")>
                所有作答名單
            </a>
            <a class="btn btn-default" role="button" onclick="onGo('PassList')" @(ViewBag.PreviewY == "Y" ? "disabled" : "")>
                全對名單<span class="badge">@ViewBag.PassCount</span>
            </a>
            <a class="@ViewBag.BtnClass" role="button" onclick="onGo('Answer')" @(ViewBag.PreviewY == "Y" ? "disabled" : "")>
                @ViewBag.BtnName
            </a>
            <a class="@ViewBag.BtnClassAnsShow" role="button" onclick="onGo('QusList')" @(ViewBag.PreviewY == "Y" ? "disabled" : "")>
                @ViewBag.BtnNameAnsShow
            </a>

            @if (user != null)
            {
                if (user.USER_TYPE == UserType.Admin || user.USER_TYPE == UserType.Teacher)
                {
                    <a class="btn btn-default" role="button" onclick="onGo('TotalGraph')" @(ViewBag.PreviewY == "Y" ? "disabled" : "")>
                        統計分析
                    </a>
                }
            }
        </div>
    </div>
</div>

@using (Html.BeginForm("detail", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", enctype = "multipart/form-data" }))
{
    @Html.Hidden("SearchContents", (string)TempData["SearchContents"])
    @Html.Hidden("OrderByName", (string)TempData["OrderByName"])
    @Html.Hidden("SyntaxName", (string)TempData["SyntaxName"])
    @Html.Hidden("page", (int?)TempData["page"])
    @Html.Hidden("DIALOG_ID", (string)TempData["DIALOG_ID"])
}
@section Scripts {
    <script language="JavaScript">

        @*$(document).ready(function () {

            var SysUrl = 'http://' + '@Request.Url.Authority' + '@Request.ApplicationPath'

            $(".p-context img").each(function () {

                var ThisSrc = $(this).attr("src")

                if (ThisSrc.indexOf("http") != 0) {
                    $(this).attr("href", ThisSrc);
                }
                else if (ThisSrc.indexOf("data:image") != 0) {
                    $(this).attr("href", ThisSrc);
                }
                else {
                    $(this).attr("href", SysUrl + ThisSrc);
                }
            });

            $(".p-context img").colorbox({ photo: true });
        });

        function onGo(ActionVal) {

            if (ActionVal == "Index") {
                form1.action = '@Url.Action("Index", (string)ViewBag.BRE_NO)';
            }
            else if (ActionVal == "QusList") {
                form1.action = '@Url.Action("QusList", (string)ViewBag.BRE_NO)';
            }
            else if (ActionVal == "PassList") {
                form1.action = '@Url.Action("PassList", (string)ViewBag.BRE_NO)';

            }
            else if (ActionVal == "Answer") {
                form1.action = '@Url.Action("Answer", (string)ViewBag.BRE_NO)';
            }
            else if (ActionVal == "TotalGraph") {
                form1.action = '@Url.Action("TotalGraph", (string)ViewBag.BRE_NO)';
            }
            else if (ActionVal == "AnswerList") {
                $("#page").val('1');
                form1.action = '@Url.Action("AnswerList", (string)ViewBag.BRE_NO)';
            }
            form1.submit();
        }*@
        window.ADDI05_detail_LIST_URLS = {
            Anserdetail: "detail",
             index: '@Url.Action("Index", (string)ViewBag.BRE_NO)',
            QusList: '@Url.Action("QusList", (string)ViewBag.BRE_NO)',
            PassList: '@Url.Action("PassList", (string)ViewBag.BRE_NO)',
            Answer: '@Url.Action("Answer",(string)ViewBag.BRE_NO)',
            TotalGraph: '@Url.Action("TotalGraph", (string)ViewBag.BRE_NO)',
            AnserList : '@Url.Action("AnswerList", (string)ViewBag.BRE_NO)',
           sysUrl: 'http://' + '@Request.Url.Authority' + '@Request.ApplicationPath'
        }
    </script>
    <script src="~/Scripts/ADDI05/common.js" nonce="cmlvaw"></script>
    <script src="~/Scripts/ADDI05/detail.js" nonce="cmlvaw"></script>
}