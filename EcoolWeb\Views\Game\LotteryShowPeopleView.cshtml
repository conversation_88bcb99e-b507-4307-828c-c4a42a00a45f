﻿@model GameLotteryCreViewModel
@{
    ViewBag.Title = ViewBag.Panel_Title;
    Layout = "~/Views/Shared/_LayoutGame.cshtml";
    EcoolWeb.Models.UserProfileHelper.SetLayoutCookieData(Layout);
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@{
    if (!string.IsNullOrWhiteSpace(Model?.Search?.WhereGAME_NO))
    {
        Html.RenderAction("_ByOneGameMenu", new { NowAction = "Lottery" });
    }
}

@using (Html.BeginForm("LotteryCreView", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "formEdit", id = "formEdit", enctype = "multipart/form-data" }))
{

    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })

    @Html.HiddenFor(m => m.Search.WhereGAME_NAME)
    @Html.HiddenFor(m => m.Search.WhereGAME_NO)
    @Html.HiddenFor(m => m.Search.WhereLOTTERY_NO)
    @Html.HiddenFor(m => m.Search.WhereSCHOOL_NO)
    @Html.HiddenFor(m => m.Search.SyntaxName)
    @Html.HiddenFor(m => m.Search.Page)
    @Html.HiddenFor(m => m.Search.OrdercColumn)

    @Html.HiddenFor(m => m.Main.GAME_NO)
    @Html.HiddenFor(m => m.Main.LOTTERY_NO)
    @Html.HiddenFor(m => m.Main.LEVEL)
    @Html.HiddenFor(m => m.Main.LEVEL_COUNT)
    @Html.HiddenFor(m => m.Main.PEOPLE_COUNT)
    @Html.HiddenFor(m => m.Main.UNLOTTERY)
    @Html.HiddenFor(m => m.Main.UNCUEST)
    @Html.HiddenFor(m => m.Main.STATUS)

    <div class="panel panel-ZZZ" name="TOP">
        <div class="panel-heading text-center">
            中獎名單
        </div>
        <div class="table-responsive">

            <table class="table-ecool table-92Per table-hover table-ecool-ZZZ">
                <thead>
                    <tr>
                        <th>
                            <samp class="form-group" style="cursor:pointer;" onclick="FunSort('SHORT_NAME')">
                                @Html.DisplayNameFor(model => model.People.First().SHORT_NAME)
                            </samp>
                        </th>
                        <th>
                            <samp class="form-group" style="cursor:pointer;" onclick="FunSort('NAME')">
                                @Html.DisplayNameFor(model => model.People.First().NAME)
                            </samp>
                        </th>

                        <th>
                            <samp class="form-group" style="cursor:pointer;" onclick="FunSort('PHONE')">
                                @Html.DisplayNameFor(model => model.People.First().PHONE)
                            </samp>
                        </th>

                        <th>
                            <samp class="form-group" style="cursor:pointer;" onclick="FunSort('GAME_USER_TYPE')">
                                @Html.DisplayNameFor(model => model.People.First().GAME_USER_TYPE)
                            </samp>
                        </th>

                        <th>
                            <samp class="form-group" style="cursor:pointer;" onclick="FunSort('USER_NO')">
                                @Html.DisplayNameFor(model => model.People.First().USER_NO)
                            </samp>
                        </th>
                        <th>
                            <samp class="form-group" style="cursor:pointer;" onclick="FunSort('CLASS_NO')">
                                @Html.DisplayNameFor(model => model.People.First().CLASS_NO)
                            </samp>
                        </th>
                        <th>
                            <samp class="form-group" style="cursor:pointer;" onclick="FunSort('SEAT_NO')">
                                @Html.DisplayNameFor(model => model.People.First().SEAT_NO)
                            </samp>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @if (Model.People.Count() > 0)
                    {
                        foreach (var item in Model.People)
                        {
                            <tr>
                                <td align="center">
                                    @if (item.GAME_USER_TYPE == UserType.Student)
                                    {
                                        @Html.DisplayFor(modelItem => item.SHORT_NAME)
                                    }
                                    else
                                    {
                                        <text>卡片</text>
                                    }
                                </td>
                                <td align="center">
                                    @Html.Partial("_LotteryPeopleView", item)
                                    @Html.DisplayFor(modelItem => item.NAME)
                                </td>
                                <td align="center">
                                    @Html.DisplayFor(modelItem => item.PHONE)
                                </td>
                                <td align="center">
                                    @if (string.IsNullOrWhiteSpace(item.GAME_USER_TYPE_DESC))
                                    {
                                        @UserType.GetDesc(item.GAME_USER_TYPE)
                                    }
                                    else
                                    {
                                        @Html.DisplayFor(modelItem => item.GAME_USER_TYPE_DESC)
                                    }
                                </td>
                                <td align="center">
                                    @if (item.GAME_USER_TYPE == UserType.Student)
                                    {
                                        @Html.DisplayFor(modelItem => item.USER_NO)
                                    }
                                </td>
                                <td align="center">
                                    @Html.DisplayFor(modelItem => item.CLASS_NO)
                                </td>
                                <td align="center">
                                    @Html.DisplayFor(modelItem => item.SEAT_NO)
                                </td>
                            </tr>
                        }
                    }
                </tbody>
            </table>
        </div>
    </div>
    <BR />
    <div class="form-group">
        @Html.LabelFor(model => model.Main.LOTTERY_DESC, htmlAttributes: new { @class = "control-label-left col-md-1" })
        <div class="col-md-11">
            @Html.EditorFor(model => model.Main.LOTTERY_DESC, new { htmlAttributes = new { @class = "form-control", @placeholder = "非必填", @maxlength = "30" } })
            @Html.ValidationMessageFor(model => model.Main.LOTTERY_DESC, "", new { @class = "text-danger" })
        </div>
    </div>
    <div style="height:20px"></div>
    <div class="form-group" style="padding-top:20px">
        <div class="col-xs-12 text-center">
            <div class="form-inline">
                <input type="button" value="確定" class="btn btn-default" onclick="LotterySave()" />
                <button class="btn btn-default" type="button" onclick="onBack()">放棄</button>
            </div>
        </div>
    </div>
}

@section Scripts {
    <script language="JavaScript">
        var targetFormID = '#formEdit';

        function LotterySave()
        {
            $(targetFormID).attr("action", "@Url.Action("LotterySave", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        function onBack() {
            $(targetFormID).attr("action", "@Url.Action("LotteryCreView", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }
    </script>
}