﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'image', 'fo', {
	alt: 'Alternativur tekstur',
	border: '<PERSON>rdi',
	btnUpload: 'Send til ambætaran',
	button2Img: 'Skal valdi myndaknøttur gerast til vanliga mynd?',
	hSpace: 'Høgri breddi',
	img2Button: 'Skal valda mynd gerast til myndaknøtt?',
	infoTab: 'Myndaupplýsingar',
	linkTab: 'Tilknýti',
	lockRatio: '<PERSON><PERSON>s lutfallið',
	menu: 'Myndaeginleikar',
	resetSize: 'Upprunastødd',
	title: 'Mynda<PERSON>inleikar',
	titleButton: 'Eginleikar fyri myndaknøtt',
	upload: 'Send',
	urlMissing: 'URL til mynd manglar.',
	vSpace: '<PERSON><PERSON><PERSON> breddi',
	validateBorder: 'Bordi má vera eitt heiltal.',
	validateHSpace: 'HSpace má vera eitt heiltal.',
	validateVSpace: 'VSpace má vera eitt heiltal.'
} );
