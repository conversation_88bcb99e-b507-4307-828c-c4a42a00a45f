/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/General/Italic/SpacingModLetters.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXGeneral-italic"],{688:[838,-326,378,7,391],689:[838,-326,378,7,414],690:[851,-199,300,44,350],691:[690,-345,320,2,320],692:[690,-345,320,0,318],693:[690,-163,320,0,335],694:[684,-345,390,6,462],695:[690,-327,500,15,515],696:[693,-202,330,16,357],699:[686,-443,333,79,236],704:[690,-295,326,30,307],705:[690,-295,326,23,343],710:[661,-492,333,91,385],711:[661,-492,333,121,426],728:[650,-492,333,117,418],729:[606,-508,333,207,305],730:[707,-508,333,155,355],731:[40,169,333,-20,200],732:[624,-517,333,100,427],733:[664,-494,333,93,486],736:[684,-218,315,23,335],737:[837,-333,220,41,214],738:[691,-335,300,16,290],739:[691,-333,380,4,379],740:[847,-333,318,8,345],748:[70,147,320,15,305],749:[665,-507,405,10,395],759:[-113,220,333,-94,233]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/Italic/SpacingModLetters.js");
