﻿using ECOOL_APP;
using ECOOL_APP.EF;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Web;

namespace EcoolWeb
{
    public class LogHelper
    {
        private static string infoLogPath = AppDomain.CurrentDomain.BaseDirectory + "log/info/";
        private static string errorLogPath = AppDomain.CurrentDomain.BaseDirectory + "log/error/";

        /// <summary>
        /// 寫入日誌String
        /// </summary>
        /// <param name="input"></param>
        public static void LogToTxt(String input)
        {
            if (!Directory.Exists(infoLogPath))
            {
                Directory.CreateDirectory(infoLogPath);
            }
            StringBuilder logInfo = new StringBuilder("");
            string currentTime = System.DateTime.Now.ToString("[yyyy-MM-dd HH:mm:ss]");
            if (input != null)
            {
                logInfo.Append("\n");
                logInfo.Append(currentTime + "\n");
                logInfo.Append(input);
            }
            System.IO.File.AppendAllText(infoLogPath + DateTime.Now.ToString("yyyy-MM-dd") + ".txt", logInfo.ToString() + Environment.NewLine);
        }

        /// <summary>
        /// 寫入日誌Exception
        /// </summary>
        /// <param name="ex"></param>
        public static void LogToTxt(Exception ex)
        {
            if (!Directory.Exists(errorLogPath))
            {
                Directory.CreateDirectory(errorLogPath);
            }
            StringBuilder logInfo = new StringBuilder("");
            string currentTime = System.DateTime.Now.ToString("[yyyy-MM-dd HH:mm:ss]");
            if (ex != null)
            {
                logInfo.Append("\n");
                logInfo.Append(currentTime + "\n");
                logInfo.Append(ex.Message + "\n");
                logInfo.Append(ex.GetType() + "\n");
                logInfo.Append(ex.Source + "\n");
                logInfo.Append(ex.TargetSite + "\n");
                logInfo.Append(ex.StackTrace + "\n");
            }
            System.IO.File.AppendAllText(errorLogPath + DateTime.Now.ToString("yyyy-MM-dd") + ".txt", logInfo.ToString() + Environment.NewLine);
        }

        /// <summary>
        ///
        /// </summary>
        /// <param name="SCHOOL_NO">學校帳號</param>
        /// <param name="USER_NO">使用者登入帳號</param>
        /// <param name="IP_ADRESS">使用者IP位置</param>
        /// <param name="ACTION_ID">讀取ACTION位置</param>
        /// <param name="LOGMSG">LOG訊息</param>
        public static void AddLogToDB(string SCHOOL_NO, string USER_NO, string IP_ADRESS, string ACTION_ID, string LOGMSG)
        {
            //新增登入紀錄
            using (ECOOL_DEVEntities db = new ECOOL_DEVEntities())
            {
                ZZT17 zzt17 = new ZZT17();
                zzt17.SCHOOL_NO = SCHOOL_NO;
                zzt17.USER_NO = USER_NO;

                zzt17.IP_ADRESS = IP_ADRESS??"無IP";
                //或
                //zzt17.IP_ADRESS = Request.UserHostAddress;

                //zzt17.IP_ADRESS = System.Web.HttpContext.Current.Request.UserHostAddress;
                zzt17.ACTION_ID = ACTION_ID?? "ACTION_ID錯誤";
                zzt17.LOG_STATUS = LOGMSG;
                zzt17.ACTIONTIME = DateTime.Now;

                //儲存資料
                db.ZZT17.Add(zzt17);
                db.SaveChanges();
            }
        }

        internal static void LogToTxt()
        {
            throw new NotImplementedException();
        }
    }
}