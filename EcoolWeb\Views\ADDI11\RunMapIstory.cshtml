﻿@model ADDI11RunMapViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
   Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
}
@if (AppMode == false && Model.WhereIsColorboxForUser != "True")
{
    @Html.Partial("_Title_Secondary")
}
@Html.Partial("_Notice")

@{
    Html.RenderAction("_RunMenuIstory", new { NowAction = "RunMapIstory" });
}
@if (Model.RUN_TOTAL_METER >= 411.5)
{

    <p style="font-size:150%;font-weight:bold;">
        @ViewBag.UName

        小朋友

        ,到達 <b style="color:red">壽山動物園</b>,實在太棒了!!
    </p>
}
else
{
    if (Model.RUN_TOTAL_METER < 0.8)
    {

        <p style="font-size:150%;font-weight:bold;">
            @ViewBag.UName @ViewBag.USERNOTitle,你目前跑 <b style="color:red">@Model.RUN_TOTAL_METER KM </b>
            ,再  <b style="color:red">@Model.Range KM </b>就可以跑到  <b style="color:red">@Model.NextStop</b>,請繼續加油!
        </p>

    }
    else
    {
        <p style="font-size:150%;font-weight:bold;">
            @ViewBag.UName @ViewBag.USERNOTitle,你目前跑 <b style="color:red">@Model.RUN_TOTAL_METER KM </b>

            ,已經超過  <b style="color:red">@Model.LOCATION_NAME</b>,再  <b style="color:red">@Model.Range KM </b>就可以跑到  <b style="color:red">@Model.NextStop</b>,請繼續加油!
        </p>
    }

}
<img src="@Model.RUN_IMG_PATH" class="img-responsive" alt="Responsive image" />