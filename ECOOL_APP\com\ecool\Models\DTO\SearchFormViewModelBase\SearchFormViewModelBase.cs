﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static ECOOL_APP.EF.PageGlobal;

namespace ECOOL_APP.EF
{
    /// <summary>
    /// 搜索 Form 的 ViewModel base。定義搜索必須要有的相關欄位。
    /// </summary>
    public abstract class SearchFormViewModelBase : ISearchFormViewModelBase
    {
        /// <summary>
        /// 目前頁數的值
        /// </summary>
        private int page;

        /// <summary>
        /// 每頁筆數的值
        /// </summary>
        private int pageSize;

        /// <summary>
        ///  取得或設定目前頁數。最小值是1。
        /// </summary>
        /// <value>
        /// 目前頁數
        /// </value>
        public virtual int Page
        {
            get
            {
                if (this.page <= 0)
                {
                    this.page = 1;
                }

                return this.page;
            }

            set { this.page = value; }
        }

        /// <summary>
        /// 取得或設定每頁筆數。最小值是20。
        /// </summary>
        /// <value>
        /// 每頁筆數
        /// </value>
        public virtual int PageSize
        {
            get
            {
                if (this.pageSize < 1)
                {
                    this.pageSize = PageGlobal.DfPageSize;
                }

                return this.pageSize;
            }

            set { this.pageSize = value; }
        }

        /// <summary>
        /// /
        /// </summary>
        public string OrderByColumnName { get; set; }

        /// <summary>
        ///
        /// </summary>
        public SortType SortType { get; set; }

        /// <summary>
        /// 是否匯出Excel
        /// </summary>
        public bool IsToExcel { get; set; }

        /// <summary>
        /// KEY
        /// </summary>
        public string Keyword { get; set; }

        /// <summary>
        /// Search 內容
        /// </summary>
        public string SearchContent { get; set; }
    }
}