body {
    background-image: url(../images/site-body-bg.png);
    min-height: 100vh;
}

.navbar-header {
    background-color: #fff;
    flex-flow: row wrap;
    min-height: 54px;
    padding: 0;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;

    @include media-breakpoint-up(md) {
        min-height: 78px;
        padding: 0 1rem .5rem 1rem;
        background-image: url(../images/site-header-bg.png), linear-gradient(to bottom, rgba(240, 238, 0, 1) 0%, rgba(180, 218, 7, 1) 100%);
        background-repeat: repeat-x;
        background-position: bottom center;
        background-size: 27px 5px, auto 100%;
    }

    .navbar-toggler {
        padding: 0 0.5rem;
        height: 3rem;
        display: block;
        border-radius: 0;
        border: 0;
        border-right: 1px solid #ccc;

        @include media-breakpoint-up(md) {
            display: none;
        }
    }

    .school-brand {
        padding: 0 .3rem;
        flex: 0 0 35%;
        color: #004DA0;
        min-height: 3rem;
        display: flex;
        align-items: center;
        font-size: 1.125rem;
        font-weight: bold;
        font-family: DFKai-SB;
        border-left: 1px solid #ccc;

        @include media-breakpoint-up(md) {
            border-left-width: 0;
            flex: 0 0 auto;
            font-size: 1.325rem;
        }

        img {
            display: none;
            height: 100%;
            width: auto;
            max-width: 4.6rem;

            @include media-breakpoint-up(md) {
                display: inline-block;
            }
        }


    }

    .user-name {
        padding: 0 .3rem;
        font-size: 0.75rem;
        font-weight: normal;
        order: 1;
        flex: 1 1 100%;
        background-color: #f3f3f3;
        position: relative;

        @include media-breakpoint-up(md) {
            order: inherit;
            font-size: 1.225rem;
            font-weight: bold;
            flex: 1 1 auto;
            background-color: transparent;
        }
    }

    //酷幣點數
    .my-points {
        display: block;

        @include media-breakpoint-up(md) {
            display: inline-flex;
            background-image: url(../images/my-points-bg.png);
            background-size: auto 100%;
            background-repeat: no-repeat;
            min-width: 181px;
            min-height: 61px;
            line-height: 1;
            vertical-align: middle;
            color: inherit;

            .txt {
                display: block;
                padding: 1rem;
                margin-left: 0.3rem;
                font-size: 0.85rem;
            }

            .number {
                flex: 1 1 auto;
                display: flex;
                justify-content: center;
                align-items: center;
                font-size: 1.225rem;
            }
        }
    }

    //抽獎連結
    .my-lottery {
        display: inline-flex;
        background-image: url(../images/monkey-lucky-null.png);
        background-size: auto 100%;
        background-repeat: no-repeat;
        min-width: 178px;
        min-height: 61px;
        line-height: 1;
        color: #0033ed;
        vertical-align: middle;
        transform: scale(0.758);
        position: absolute;
        top: -11px;
        right: -21px;

        @include media-breakpoint-up(md) {
            transform: scale(1);
            position: static;
        }

        &-total {
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: center;
            margin-left: 2.7rem;
            font-size: 0.5rem;
            transform: scale(0.7);

            .number {
                display: inline-block;
                padding: 0.3rem;
                font-size: 1.325rem;
                font-weight: 600;
                font-family: sans-serif;
            }

            .txt {
                display: inline-block;
                padding: 0.8rem 0 0 0.2rem;
            }
        }

        &-hint {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            font-size: 0.5rem;
            padding-top: 0.5rem;
            line-height: 1.3;
            color: #000;

            .number {
                font-weight: 600;
                font-family: sans-serif;
                font-size: 1rem;
                padding: 0.2rem;
                color: #ff0505;
            }
        }

        &:hover {
            text-decoration: none;

            .my-lottery-total .number {
                text-decoration: underline;
            }
        }
    }

    .btn-manage {

        //MENU 管理(老師)提示
        &::before {
            content: "\f013 ";
            margin-right: .3rem;
            font-family: FontAwesome;
            opacity: .1;
        }
    }

    //小鈴鐺任務按鈕
    .btn-task {
        padding: 0;
        background-color: #fff;
        border: rgba(0, 0, 0, 0.3);
        border-radius: 3rem;
        font-weight: 600;
        font-size: 0;
        color: #0260c5;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.25);

        @include media-breakpoint-up(lg) {
            padding: 0 1.5rem 0 .8rem;
            font-size: 1.256rem;
        }
    }

    .btn-task img {
        height: 100%;
        width: auto;
    }

    //登入的header
    &-login {
        background: #ffffff;
        pointer-events: none;

        @include media-breakpoint-up(md) {
            background: transparent;
        }

        >* {
            pointer-events: visible;
        }
    }
}

.site-logo {
    display: block;
    width: 3.825rem;
    max-width: 3.825rem;
    height: 3.25rem;
    background-image: url(../images/site-logo.png);
    background-repeat: no-repeat;
    background-color: transparent;
    background-position: center;
    background-size: 80% auto;
    border-color: transparent;
    font-size: 0;
    // border-left: 1px solid #ccc;
    // border-right: 1px solid #ccc;

    @include media-breakpoint-up(md) {
        border: 0;
        width: 4.6rem;
        height: 3.5rem;
        background-size: auto 100%;
    }
}

.btn-logout {
    padding: 0 .5rem;
    height: 3.25rem;
    font-size: 0;

    @include media-breakpoint-up(md) {
        background: linear-gradient(to bottom, #fffb89 0%, #fecb04 100%);
        font-size: 1.125rem;
        letter-spacing: .3rem;
        padding: 0.3rem 0.5rem 0.3rem 0.7rem;
        height: auto;
        font-weight: bold;
        border: 2px solid #e8e8e8;
    }

    &::before {
        content: "";
        display: inline-block;
        font-size: 2rem;
        opacity: 0.7;
        width: 1em;
        height: 1em;
        background-image: url("data:image/svg+xml,%3Csvg id='Capa_1' enable-background='new 0 0 512 512' height='512' viewBox='0 0 512 512' width='512' xmlns='http://www.w3.org/2000/svg'%3E%3Cg%3E%3Cg id='layer4' transform='translate(-34.926 -.529)'%3E%3Cpath id='LINE-3' d='m112.525.529c-48.127.176-76.564 39.454-76.337 76.586v248.618c0 44.159 22.717 85.329 60.163 108.733l112.266 70.316c34.655 21.659 82.209-4.816 82.209-45.683v-51.007h50.958c41.913 0 76.586-34.424 76.586-76.337v-25.379c.055-14.071-11.307-25.523-25.379-25.579-.083 0-.166 0-.249 0-14.071.055-25.434 11.507-25.379 25.578v25.379c0 14.374-11.205 25.379-25.578 25.379h-50.958v-178.302c0-41.037-24.535-78.236-62.254-94.401l-76.735-32.894h189.947c14.369 0 25.578 11.01 25.578 25.379v25.578c.051 14.003 11.376 25.331 25.379 25.379 14.076.056 25.58-11.303 25.628-25.379v-25.577c0-41.908-34.678-76.386-76.586-76.387h-229.259zm-25.379 78.626 121.323 52.102c19.11 8.19 31.401 26.782 31.401 47.574v203.88 76.387c0 3.499-1.162 4.293-4.13 2.438l-112.515-70.365c-22.606-14.129-36.078-38.781-36.078-65.439v-246.577zm381.237 48.719c-6.452.245-12.571 2.931-17.119 7.514-9.976 9.924-10.02 26.056-.096 36.032.032.032.064.064.096.096l7.315 7.315h-91.216c-14.071-.055-25.523 11.307-25.578 25.379v.199c.055 14.071 11.507 25.434 25.578 25.379h91.465l-7.564 7.564c-9.882 9.938-9.882 25.991 0 35.929 9.938 9.881 25.991 9.881 35.929 0l49.614-49.664c5.596-4.817 8.828-11.825 8.858-19.209.026-7.244-3.032-14.157-8.41-19.01-.084-.084-.169-.167-.255-.249l-49.813-49.763c-4.96-4.993-11.767-7.713-18.804-7.512z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        background-repeat: repeat;
        background-size: auto 100%;

        @include media-breakpoint-up(md) {
            display: none;
        }
    }
}

.btn-login {
    padding: 0 .5rem;
    height: 3.25rem;
    font-size: 0;

    &::before {
        content: "";
        display: inline-block;
        font-size: 1.526rem;
        opacity: 0.7;
        width: 1em;
        height: 1em;
        background-image: url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='iso-8859-1'%3F%3E%3C!-- Generator: Adobe Illustrator 19.0.0, SVG Export Plug-In . SVG Version: 6.00 Build 0) --%3E%3Csvg version='1.1' id='Capa_1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' viewBox='0 0 460.8 460.8' style='enable-background:new 0 0 460.8 460.8;' xml:space='preserve'%3E%3Cpath d='M230.432,239.282c65.829,0,119.641-53.812,119.641-119.641C350.073,53.812,296.261,0,230.432,0 S110.792,53.812,110.792,119.641S164.604,239.282,230.432,239.282z'/%3E%3Cpath d='M435.755,334.89c-3.135-7.837-7.314-15.151-12.016-21.943c-24.033-35.527-61.126-59.037-102.922-64.784 c-5.224-0.522-10.971,0.522-15.151,3.657c-21.943,16.196-48.065,24.555-75.233,24.555s-53.29-8.359-75.233-24.555 c-4.18-3.135-9.927-4.702-15.151-3.657c-41.796,5.747-79.412,29.257-102.922,64.784c-4.702,6.792-8.882,14.629-12.016,21.943 c-1.567,3.135-1.045,6.792,0.522,9.927c4.18,7.314,9.404,14.629,14.106,20.898c7.314,9.927,15.151,18.808,24.033,27.167 c7.314,7.314,15.673,14.106,24.033,20.898c41.273,30.825,90.906,47.02,142.106,47.02s100.833-16.196,142.106-47.02 c8.359-6.269,16.718-13.584,24.033-20.898c8.359-8.359,16.718-17.241,24.033-27.167c5.224-6.792,9.927-13.584,14.106-20.898 C436.8,341.682,437.322,338.024,435.755,334.89z'/%3E%3C/svg%3E%0A");
        background-repeat: repeat;
        background-size: auto 100%;
    }
}

.menu-collapse {
    position: fixed;
    top: 77px;
    right: 0;
    left: 0;
    bottom: 0;
    z-index: 999;
    padding: 0;
    overflow: auto;

    @include media-breakpoint-up(md) {
        position: static;
        z-index: 0;
        margin-top: -1rem;
    }

    &.collapse:not(.show) {
        display: none;

        @include media-breakpoint-up(md) {
            display: block;
        }
    }
}

.menulist {
    padding: 0;
    margin: 0;
    display: block;
    width: 100%;

    @include media-breakpoint-up(md) {
        padding: 0 1rem;
    }

    .menulist-item {
        padding: 0 0 .5rem 0;
        margin: 0;
        display: flex;
        flex-direction: column;
        border-radius: 0;
        background-color: #E6EB42;

        @include media-breakpoint-up(md) {
            margin: 1rem 0;
            border-radius: .4rem;
            background-color: #f2f2ff;
            box-shadow: 0 0 0.3rem rgba(0, 0, 0, 0.3);
        }

        &-main {
            display: block;
            font-size: 0.93rem;
            font-weight: bold;
            letter-spacing: .2rem;
            text-align: left;
            color: #000;
            background-color: transparent;
            border: 0;
            position: relative;

            @include media-breakpoint-up(md) {
                text-align: center;
                border-style: solid;
                border-width: 0 0 1px 0;
                border-radius: .4rem .4rem 0 0;
            }

            &::before {
                content: "";
                display: inline-block;
                width: 2.5em;
                height: 2.5em;
                margin: -.9em 0.3rem -.5em -2.5em;
                background-repeat: no-repeat;
                background-position: center center;
                background-size: 100% auto;
            }

            &:after {
                content: "";
                display: inline-block;
                width: 0;
                height: 0;
                margin-left: 2px;
                vertical-align: middle;
                border-style: solid;
                border-color: #000 transparent transparent transparent;
                border-width: 4px 4px 0 4px;

                @include media-breakpoint-up(md) {
                    display: none;
                }
            }

            .fa {
                min-width: 1.5em;
                text-align: center;
            }

            &[data-child="true"] {
                background-color: #f3f3f3;
            }
        }

        &-child {
            display: block;
        }

    }

    &-link {
        padding: .2rem .5rem .2rem 2.8rem;
        display: block;
        font-size: 0.85rem;
        text-align: left;
        color: #000;

        @include media-breakpoint-up(md) {
            padding: .1rem .3rem;
            text-align: center;
        }
    }
}

//主題變色
@mixin menulist-main-style($btncolor, $shadowcolor, $bordercolor, $txtcolor, $feather, $imgurl) {
    @include media-breakpoint-up(md) {
        padding-left: 1rem;
        color: $txtcolor;
        background-color: $btncolor;
        box-shadow: inset 0 -.3rem $feather $shadowcolor, inset 0 -.75rem $feather darken($btncolor, 5%);
        border-color: $bordercolor;

        &::before {
            background-image: url($imgurl);
        }
    }
}

@each $name,
$value in $menuColors {
    $class: map-get($value, class);
    $bgcolor: map-get($value, bgcolor);
    $btncolor: map-get($value, btncolor);
    $shadowcolor: map-get($value, shadowcolor);
    $bordercolor: map-get($value, bordercolor);
    $txtcolor: map-get($value, txtcolor);
    $feather: map-get($value, feather);
    $imgurl: map-get($value, imgurl);

    .menulist .menulist-item-#{$class} {
        @include media-breakpoint-up(md) {
            background-color: $bgcolor;
        }

        .menulist-item-main {
            @include menulist-main-style($btncolor, $shadowcolor, $bordercolor, $txtcolor, $feather, $imgurl);
        }
    }

}

//js開關
.menulist-item-main[data-child="false"] {
    &+.menulist-item-child {
        display: none;

        @include media-breakpoint-up(md) {
            display: block;
        }
    }
}

//主板上距 js參數用值
@for $i from 78 to 200 {
    body[data-top="#{$i}"] {
        padding-top: #{$i}px;
    }

    .menu-collapse[data-top="#{$i}"] {
        top: #{$i}px;
    }
}

.shortcut-links {
    display: inline;

    @include media-breakpoint-up(md) {
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
    }

    &-item {
        flex: 0 0 auto;
        margin: 0 .3rem;
        font-size: 1rem;
        display: inline-block;

        @include media-breakpoint-up(md) {
            flex: 0 0 3.125rem;
            display: block;
            margin: .3rem;
            font-size: 0;
            height: 3.125rem;
            max-height: 3.125rem;
            width: 3.125rem;
            max-width: 3.125rem;
            background-color: #ededed;
            border-radius: 3rem;
            box-shadow: 0 0.1rem 0.3rem rgba(0, 0, 0, 0.18);
        }

        @include media-breakpoint-up(lg) {
            margin: 0 0.3rem;
        }

        img {
            display: none;
            width: 100%;
            height: 100%;

            @include media-breakpoint-up(md) {
                display: block;
            }
        }
    }
}

.shortcut-qrcode {
    display: inline-block;
    margin: 0 .3rem;
    padding: 0;
    font-size: 1rem;

    @include media-breakpoint-up(md) {
        margin: 0;
        padding: 1rem 1rem 0 1rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        color: #000;
    }

    img {
        display: none;
        margin: 0.3rem;
        height: auto;
        width: 100%;

        @include media-breakpoint-up(md) {
            display: block;
            max-width: 100%;
        }

        @include media-breakpoint-up(lg) {
            max-width: 100px;
        }
    }
}

.footer {
    text-align: center;
    padding: 0.2rem;
    margin: 1rem 4.5em;
    background-color: #744624;
    color: #fff;
    border-top: 2px solid #c77966;
    border-bottom: 2px solid #c77966;
    position: relative;

    &::before {
        content: "";
        width: 4.5em;
        height: 2.35em;
        position: absolute;
        top: -0.15em;
        left: -4.5em;
        background-image: url(../images/site-footer-bg.png);
        background-repeat: no-repeat;
        background-position: left top;
        background-size: auto 100%;
    }

    &::after {
        content: "";
        width: 4.5em;
        height: 2.35em;
        position: absolute;
        top: -0.15em;
        right: -4.5em;
        background-image: url(../images/site-footer-bg.png);
        background-repeat: no-repeat;
        background-position: right top;
        background-size: auto 100%;
    }
}

//角色娃娃
.user-players {
    display: inline-block;
    width: 100%;
    max-width: 7rem;
    height: auto;
    vertical-align: text-bottom;
}

//登入頁歡迎圖
.login-head-img {
    padding-top: 53%;
    background-image: url(../images/login-head-img-mb.png);
    background-repeat: no-repeat;
    background-position: center center;
    background-size: auto 100%;

    @include media-breakpoint-up(md) {
        padding-top: 220px;
        background-image: url(../images/login-head-img-pc.png);
    }
}

//登入圖上距 js參數用值
@for $i from 40 to 100 {
    .login-head-img[data-top="#{$i}"] {
        margin-top: #{$i}px;

        @include media-breakpoint-up(md) {
            margin-top: 0;
        }
    }
}

//橘色登入框
.login-box {
    position: relative;
    padding: 0;
    margin: 1.3rem auto 0 auto;
    border-radius: 0.3rem;
    max-width: 290px;
    font-size: 1rem;

    @include media-breakpoint-up(md) {
        padding: 2.5rem 0 1rem 0;
        margin: 1.3rem auto;
        font-size: inherit;
        max-width: 180px;
        background-color: #ff8e24;
        box-shadow: 0 0 0 1px #fff, 0 0 0 5px #ff8e24, 0 0 6px 4px #000;
    }

    &-title {
        position: absolute;
        top: 0;
        right: 0;
        left: 0;
        padding: 0.6rem 0 0.3rem 0;
        margin: -5px -5px 0 -5px;
        display: block;
        border-radius: 0.3rem 0.3rem 0 0;
        font-size: 1.125rem;
        font-weight: 600;
        text-align: center;
        color: #004aa2;

        @include media-breakpoint-up(md) {
            background-color: rgba(255, 238, 212, .4);
            color: #603813;
        }
    }


    .input-group>* {
        padding: 0 .5rem;
    }

    .form-control,
    .btn-light {
        border-color: #f8b4af;

        @include media-breakpoint-up(md) {
            border-color: #dee2e6;
        }
    }

    .btn-light {
        background-color: #ffe5e3;

        @include media-breakpoint-up(md) {
            background-color: #f8f9fa;
        }
    }

    .form-control-sm {
        font-size: 1rem;

        @include media-breakpoint-up(md) {
            font-size: inherit;
        }
    }

    &-label {
        display: block;
        text-align: right;
        font-weight: bold;
        font-size: 1rem;
        padding: 0.3rem;

        @include media-breakpoint-up(md) {
            padding: 0;
            font-size: inherit;
            color: #fff;
        }
    }

    .btn {
        font-size: 1rem;

        @include media-breakpoint-up(md) {
            font-size: inherit;
        }
    }

    .btn-link {
        @include media-breakpoint-up(md) {
            color: #fff;
        }
    }

    &::after {
        content: "";
        background-image: url(../images/login-box-bg.png);
        background-repeat: no-repeat;
        background-position: center bottom;
        background-size: auto 100%;
        height: 26px;
        width: 26px;
        display: none;
        position: absolute;
        bottom: -23px;
        right: auto;
        left: calc(50% - 13px);

        @include media-breakpoint-up(md) {
            display: block;
        }
    }
}

//臺北市單一身分驗證
.btn-openid {
    font-size: 0;
    display: block;
    width: 100%;
    max-width: 180px;
    margin: 0 auto .5rem auto;
    padding-top: 86px;
    background-image: url(../images/oidc-v.png);
    background-repeat: no-repeat;
    background-size: 100% auto;

    @include media-breakpoint-up(md) {
        padding-top: 50%;
    }
}

//登入頁 選擇學校
.school-menu {
    position: relative;
    padding: .5em;
    margin-bottom: 1em;
    overflow: auto;
    // position: fixed;
    // top: 54px;
    // right: 0;
    // bottom: -1rem;
    // left: 0;
    // z-index: 2;
    background-image: url(../images/site-body-bg.png);
    background-color: #fff;
    border-radius: 0;
    font-size: 1rem;

    @include media-breakpoint-up(md) {
        position: static;
        background-image: none;
        background-color: #ffc100;
        box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.4);
        border-radius: 0.3em;
    }

    &-none {
        display: none;

        @include media-breakpoint-up(md) {
            display: block;
        }
    }

    &-title {
        margin: -0.5em -0.5em 0.4em -0.5em;
        padding: 0.3rem 0;
        display: block;
        border-radius: 0.3em 0.3em 0 0;
        font-size: 1.125em;
        font-weight: 600;
        text-align: center;
        color: #004aa2;

        @include media-breakpoint-up(md) {
            background-image: linear-gradient(45deg, rgba(255, 233, 136, 1) 0%, rgba(255, 220, 50, 1) 100%);
            color: #603813;
        }
    }

    &-list {
        padding: 0;
        margin: 1em 0 0 0;
        list-style-type: none;

        strong {
            padding: 0.3em;
            display: block;
            text-align: left;
            color: #3a87ad;
            background-color: #d9edf7;
            border-color: #bce8f1;
            border-radius: .3em .3em 0 0;

            @include media-breakpoint-up(md) {
                text-align: center;
                color: #000;
                background-color: transparent;
                border-color: transparent;
            }
        }

        >li {
            display: block;
            padding: 0;
            margin: 0;
            list-style-type: none;
            background-color: #ffffff;
            border: 1px solid #bce8f1;
            border-radius: 0.3em;

            @include media-breakpoint-up(md) {
                background-color: transparent;
                border-color: transparent;
            }
        }
    }

    &-child {
        padding: .5em;
        margin: 0;
        list-style-type: none;
        display: flex;
        flex-wrap: wrap;
        font-size: 0.725em;

        @include media-breakpoint-up(md) {
            padding: 0;
        }

        li {
            flex: 0 0 50%;
            display: inline-block;
            padding: 0;
            margin: 0;

            a {
                display: block;
                font-size: .925em;
                font-weight: bold;
                text-align: center;
                padding: 0.3em;
                margin: 0.1em 0.5em;
                letter-spacing: 2px;
                color: #000;
                background-color: #FFF;
                border: 1.5px solid #c3e4f6;
                border-radius: 0.3em;

                @include media-breakpoint-up(md) {
                    padding: 0;
                    margin: 0;
                    font-size: 0.725em;
                    font-weight: normal;
                    letter-spacing: 0;
                    background-color: transparent;
                    border-color: transparent;
                }
            }
        }
    }

}