﻿@using ECOOL_APP.com.ecool.Models.entity;
@using System.Collections;
@model  IEnumerable<uADDT04Q02>
@{
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    ViewBag.Title = "閱讀護照完成一覽表";

    string ddlCLASS_NO = string.Empty;
    string ddlGrade = string.Empty;
    string whereKeyword = string.Empty;

    if (user!=null)
    {
        ddlCLASS_NO = user.TEACH_CLASS_NO ?? user.CLASS_NO;
        ddlGrade = string.IsNullOrWhiteSpace(ddlCLASS_NO) == false ? new ECOOL_APP.com.ecool.util.StringHelper().StrLeft(ddlCLASS_NO, 1) : "";

        if (user.USER_TYPE==UserType.Student)
        {
            //whereKeyword = user.USER_NO;
        }
    }

    int WhereGRADE = string.IsNullOrWhiteSpace(ddlGrade) == false ? Convert.ToInt32(ddlGrade) : 1;


}
@helper  buttonFun()
{
string BooKGradeEmpty = string.IsNullOrWhiteSpace(ViewBag.BooKGrade) ? "active" : "";
//string BooKGrade1 = ViewBag.BooKGrade=="1" ? "active" : "";
//string BooKGrade2 = ViewBag.BooKGrade == "2" ? "active" : "";
//string BooKGrade3 = ViewBag.BooKGrade == "3" ? "active" : "";
//string BooKGrade4 = ViewBag.BooKGrade == "4" ? "active" : "";
//string BooKGrade5 = ViewBag.BooKGrade == "5" ? "active" : "";
//string BooKGrade6 = ViewBag.BooKGrade == "6" ? "active" : "";
string BooKGradeALL = ViewBag.BooKGrade == "ALL" ? "active" : "";

    <br/>
    <div class="form-inline" >
        <div class="col-xs-12 text-right">
            顯示：
            <button class="btn btn-xs btn-pink @BooKGradeEmpty" type="button" data-grade="">全部</button>
            @*<button class="btn btn-xs btn-pink @BooKGrade1" data-grade="1" type="button">一年級</button>
            <button class="btn btn-xs btn-pink @BooKGrade2" data-grade="2" type="button">二年級</button>
            <button class="btn btn-xs btn-pink @BooKGrade3" data-grade="3" type="button">三年級</button>
            <button class="btn btn-xs btn-pink @BooKGrade4" data-grade="4" type="button">四年級</button>
            <button class="btn btn-xs btn-pink @BooKGrade5" data-grade="5" type="button">五年級</button>
            <button class="btn btn-xs btn-pink @BooKGrade6" data-grade="6" type="button">六年級</button>*@
            <button class="btn btn-xs btn-pink @BooKGradeALL" type="button" data-grade="ALL">全部完成護照學生</button>
            @Html.Hidden("ddlBooKGrade",(string) ViewBag.BooKGrade)
        </div>
    </div>
}

@if (AppMode == false)
{
    @Html.Partial("_Title_Secondary")
}

@using (Html.BeginForm("QUERY2", "ADDI03", FormMethod.Post, new { id = "ADDI03", name = "ADDI03" }))
{

    <div class="form-inline">
        <a role="button" href='@Url.Action("rpp", "rpp")' class="btn btn-sm btn-sys">
            護照說明
        </a>

        <a href='@Url.Action("rpp_book", "rpp")' role="button" class="btn btn-sm btn-sys">
            閱讀書單
        </a>

        @if (string.IsNullOrWhiteSpace(ddlCLASS_NO) == false)
        {
            @Html.ActionLink("本學年護照", "Query3", "ADDI03", new { GRADE = WhereGRADE, QShowYN = "N" }, new { @role = "button", @class = "btn btn-sm btn-sys" })
        }

        @Html.ActionLink("護照現況一覽表", "Query3", "ADDI03", new { GRADE = WhereGRADE, whereKeyword= whereKeyword }, new { @role = "button", @class = "btn btn-sm btn-sys" })

        <a href='@Url.Action("Query2", "ADDI03",new { ddlGrade= ddlGrade, ddlCLASS_NO= ddlCLASS_NO, whereKeyword= whereKeyword })' role="button" class="btn btn-sm btn-sys active" >
            護照完成一覽表
        </a>
        @if (user != null)
        {
            if (user.USER_TYPE == UserType.Student)
            {


                <a role="button" href='@Url.Action("Query4", "ADDI03")' class="btn btn-sm btn-sys">
                    我的護照
                </a>


            }
            else if (user.USER_TYPE == UserType.Parents)
            {
                <a role="button" href='@Url.Action("Query4", "ADDI03")' class="btn btn-sm btn-sys">
                    寶貝護照
                </a>
            }
        }


        <div class="form-inline" role="form">

            <div class="form-group">
                <label class="control-label">學號/姓名</label>
            </div>
            <div class="form-group">
                @Html.Editor("whereKeyword",(string)ViewBag.whereKeyword, new { htmlAttributes = new { @class = "form-control input-sm" } })
            </div>

            <div class="form-group">
                <label class="control-label">年級：</label>
            </div>
            <div class="form-group">
                @Html.DropDownList("ddlGrade", (IEnumerable<SelectListItem>)ViewBag.GradeItem, new { @class = "form-control input-sm" })
            </div>
            <div class="form-group">
                <label class="control-label">班級</label>
            </div>
            <div class="form-group">
                @Html.DropDownList("ddlCLASS_NO", (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control input-sm"})
            </div>


            <input type="submit" class="btn-yellow btn btn-sm" value="搜尋"  />
            <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" />
        </div>
    </div>

    @buttonFun()
    <img src="~/Content/img/web-bar2-revise-30.png" style="width:100%" class="img-responsive App_hide" alt="Responsive image" />
    <div class="table-eZwhitespacinitial">
        <div  class="text-center">
            <table  class="table-ecool table-92Per table-hover table-ecool-rpp-sm">
                <thead>
                    <tr>
                        <th>班級</th>
                        <th>姓名</th>
                        <th>一年級<br/>護照</th>
                        <th>二年級<br />護照</th>
                        <th>三年級<br />護照</th>
                        <th>四年級<br />護照</th>
                        <th>五年級<br />護照</th>
                        <th>六年級<br />護照</th>
                        <th>全部<br />完成</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var item in Model)
                {
                        <tr>
                            <td align="center">@item.CLASS_NO</td>
                            <td align="center" style="white-space:nowrap">@item.SONAME</td>
                            <td align="center" style="font-size:10px">@item.UserStatus_NO01</td>
                            <td align="center" style="font-size:10px">@item.UserStatus_NO02</td>
                            <td align="center" style="font-size:10px">@item.UserStatus_NO03</td>
                            <td align="center" style="font-size:10px">@item.UserStatus_NO04</td>
                            <td align="center" style="font-size:10px">@item.UserStatus_NO05</td>
                            <td align="center" style="font-size:10px">@item.UserStatus_NO06</td>
                            <td align="center" style="font-size:10px">@item.FinishStatus</td>
                        </tr>
                    }
                </tbody>
            </table>
            @if (Model.Count() == 0)
            {
                <h3>查無資料!!</h3>
            }
            <div style="height:15px"></div>
            <div class="btn-group btn-group-justified" role="group">
                共 @Model.Count() 人
            </div>
        </div>
    </div>
}
@section css {
    <style>
        .form-group.focused {
            background-color: #f8f9fa;
            border-radius: 4px;
            padding: 5px;
            transition: all 0.3s ease;
        }

        .btn-hover {
            background-color: #e91e63 !important;
            border-color: #e91e63 !important;
            color: white !important;
            opacity: 0.8;
        }

        .table-row-hover {
            background-color: #f5f5f5 !important;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .alert-message {
            animation: slideDown 0.3s ease;
        }

        @keyframes slideDown {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .form-control:focus {
            border-color: #80bdff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        .btn-pink.active {
            background-color: #e91e63 !important;
            border-color: #e91e63 !important;
            color: white !important;
        }
    </style>
}

@section scripts {
    <script nonce="cmlvaw">
        // 設置全局 URL 配置
        window.ADDI03_QUERY2_URLS = {
            query2: "Query2"
        };
    </script>
    <script src="~/Scripts/ADDI03/query2.js" nonce="cmlvaw"></script>
}
