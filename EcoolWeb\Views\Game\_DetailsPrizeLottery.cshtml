﻿@model GameLotteryPrizeViewModel

@using (Html.BeginCollectionItem("PrizeDetails"))
{
    var Index = Html.GetIndex("PrizeDetails");

<div class="tr" id="Tr@(Index)" style="height:30px;vertical-align:middle;">
    <div class="td" style="text-align:center;">
        <a role='button' style="cursor:pointer;" onclick="deleteRow('Tr@(Index)')"> <i class='glyphicon glyphicon-remove'></i></a>
    </div>

    <div class="td" style="text-align:center">
       @Html.CheckBoxFor(m => m.Y_REPEAT)
    </div>
    <div class="td">

        @*@Html.EditorFor(m => m.PrizeQty, new { htmlAttributes = new { @class = "form-control input-md", @placeholder = "未填預設3秒" } })*@
    </div>
    <div class="td">   </div>
    <div class="td">
        @Html.HiddenFor(m => m.IsAgainst)
        @Html.HiddenFor(m => m.IsApply)
        @Html.HiddenFor(m => m.LEVEL_NO)
        @Html.HiddenFor(m => m.LEVEL_TYPE)
        @Html.EditorFor(m => m.PrizeName, new { htmlAttributes = new { @class = "form-control input-md" } })
        @*@Html.EditorFor(m => m.LEVEL_NAME, new { htmlAttributes = new { @class = "form-control input-md" } })*@
    </div>

    <div class="td">
        @Html.EditorFor(m => m.PrizeQty, new { htmlAttributes = new { @class = "form-control input-md" } })
    </div>

    <div class="td">
        @Html.HiddenFor(m => m.PrizeRate, new { htmlAttributes = new { @class = "form-control input-md", @placeholder = "未填預設獎品數" } })
    </div>
    @*<div class="panel-footer">
        <div class="row">
            <div class="col-md-12 col-xs-12 text-right">
                <span class="input-group-btn">
                    <button class="btn btn-info btn-sm" type="button" onclick="onAddDetailsPrizeLotteryItem()">
                        <i class="fa fa-plus-circle"></i>   增加兌換獎品
                    </button>
                </span>
            </div>
        </div>
    </div>*@
</div>
}