/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/General/BoldItalic/LetterlikeSymbols.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXGeneral-bold-italic"],{8450:[685,14,713,35,704],8453:[683,14,847,52,795],8458:[462,224,819,27,771],8459:[699,21,1171,65,1154],8461:[669,0,773,21,808],8464:[699,21,997,47,977],8466:[699,21,1036,40,1015],8467:[699,14,500,43,632],8469:[669,0,760,27,783],8470:[675,15,1055,24,1031],8473:[669,0,497,18,715],8474:[685,74,754,35,734],8475:[699,21,1048,55,973],8477:[669,0,727,18,718],8482:[676,-271,1000,24,977],8484:[669,0,807,23,837],8492:[699,21,1060,55,985],8495:[462,14,726,35,648],8496:[699,21,826,95,791],8497:[699,21,1042,65,1025],8499:[699,21,1300,60,1245],8500:[462,14,848,35,780],8508:[449,13,730,32,715],8511:[669,0,796,35,821],8517:[669,0,748,18,733],8518:[699,13,633,45,698],8519:[462,13,575,45,540],8520:[669,0,379,40,413],8521:[669,205,421,-93,455]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/BoldItalic/LetterlikeSymbols.js");
