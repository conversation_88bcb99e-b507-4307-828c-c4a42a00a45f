﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Text;

namespace ECOOL_APP.EF
{
    public class ADDI10SearchViewModel
    {
        public string WhereSOU_KEY { get; set; }
        public string WhereQUESTIONNAIRE_ID { get; set; }
        public string LoginYN { get; set; }

        [DisplayName("活動名稱")]
        public string WhereSearch { get; set; }

        public string WhereIndex { get; set; }
        public string whereADDI10Status { get; set; }

        /// <summary>
        /// 排序欄位
        /// </summary>
        public string OrdercColumn { get; set; }

        /// <summary>
        /// Desc or ASC
        /// </summary>
        public string SyntaxName { get; set; }

        /// <summary>
        /// 頁次
        /// </summary>
        public int Page { get; set; }

        public string BackAction { get; set; }

        public string BackController { get; set; }
    }
}