﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class GameLotteryPrizeViewModel
    { /// <summary>
      ///關卡id
      /// </summary>
        [DisplayName("關卡id")]
        public string LEVEL_NO { get; set; }

        /// <summary>
        /// 是否報名
        /// </summary>
        public bool IsApply { get; set; }

        /// <summary>
        /// 可對兌實體獎品關卡
        /// </summary>
        public bool IsAgainst { get; set; }

        /// <summary>
        ///關卡名稱
        /// </summary>
        [DisplayName("關卡名稱")]
        public string LEVEL_NAME { get; set; }
        [DisplayName("兌換點數")]
        public int CASH { get; set; }
        /// <summary>
        /// 可重複闖關
        /// </summary>
        [DisplayName("可重複闖關")]
        public bool Y_REPEAT { get; set; }
        [DisplayName("是否為點數")]
        public bool Y_CASH { get; set; }
        public string SUBJECT { get; set; }
        /// <summary>
        ///關卡類別
        /// </summary>
        [DisplayName("關卡類別")]
        public string LEVEL_TYPE { get; set; }

        /// <summary>
        ///關卡序順
        /// </summary>
        [DisplayName("關卡序順")]
        public string LEVEL_ITEM { get; set; }

        /// <summary>
        ///獎品 ID
        /// </summary>
        [DisplayName("獎品 ID")]
        public string PrizeId { get; set; }

        /// <summary>
        ///獎品品名
        /// </summary>
        [DisplayName("獎品品名")]
        public string PrizeName { get; set; }

        /// <summary>
        ///獎品數量
        /// </summary>
        [DisplayName("獎品數量")]
        public int? PrizeQty { get; set; }

        /// <summary>
        ///中獎率
        /// </summary>
        [DisplayName("中獎率")]
        public decimal? PrizeRate { get; set; }

        /// <summary>
        ///修改人
        /// </summary>
        [DisplayName("修改人")]
        public string UpdateUserID { get; set; }

        /// <summary>
        ///修改日期
        /// </summary>
        [DisplayName("修改日期")]
        public DateTime? UpdateDate { get; set; }
        public int? FixQty { get; set; }
        public int? OrderRank { get; set; }
    }
}