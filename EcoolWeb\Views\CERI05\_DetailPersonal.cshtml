﻿@model CERI05PersonalEditViewModel
@using ECOOL_APP.EF;
@{
    ECOOL_DEVEntities db = new ECOOL_DEVEntities();
    string SCHOOL_NO =
    Model.Accreditationts.Select(x => x.SCHOOL_NO).FirstOrDefault();
    List<string> ACCREDITATION_IDITEM = new List<string>();
    List<string> ACCREDITATION_IDITEMtemp = new List<string>();
    ACCREDITATION_IDITEM = Model.Accreditationts.Where(x => x.SCHOOL_NO == SCHOOL_NO).Select(x => x.ACCREDITATION_ID).ToList();
    var ACCREDITATION_IDITEMCERT03_G = db.CERT03_G.Where(x => ACCREDITATION_IDITEM.Contains(x.ACCREDITATION_ID) && x.SCHOOL_NO == SCHOOL_NO ).OrderBy(x => x.GRADE).Select(x => x.ACCREDITATION_ID).ToList();
    var temp = db.CERT03_G.Where(x => ACCREDITATION_IDITEM.Contains(x.ACCREDITATION_ID) && x.SCHOOL_NO == SCHOOL_NO).OrderBy(x => x.GRADE).ToList();
}
@foreach (var item2 in ACCREDITATION_IDITEMCERT03_G)
{

    if (!ACCREDITATION_IDITEMtemp.Contains(item2)) {
        ACCREDITATION_IDITEMtemp.Add(item2);


    }

}
 @foreach (var item2 in ACCREDITATION_IDITEMtemp)
    {
        foreach (var item in Model.Accreditationts.Where(x => x.ACCREDITATION_ID == item2).OrderBy(x=>x.ITEM_NO).ToList())
        {
            if (item != null)
            {
                using (Html.BeginCollectionItem("Accreditationts", true))
                {
                    var Index = Html.GetIndex("Accreditationts");


                    CERT02 CERT02 = new CERT02();
                    CERT02 = db.CERT02.Where(x => x.ACCREDITATION_ID == item.ACCREDITATION_ID && x.SCHOOL_NO == Model.ThisSCHOOL_NO).FirstOrDefault();

                    if (CERT02.IsText != "Y")
                    {

                        <tr>
                            <td>
                                @Html.HiddenFor(m => item.ACCREDITATION_ID)
                                @Html.HiddenFor(m => item.ITEM_NO)
                                @Html.HiddenFor(m => item.TYPE_NAME)
                                @Html.HiddenFor(m => item.ACCREDITATION_NAME)
                                @Html.HiddenFor(m => item.SUBJECT)
                                @Html.HiddenFor(m => item.CONTENT)
                                @Html.HiddenFor(m => item.O_IS_PASS)

                                @Html.DisplayFor(m => item.TYPE_NAME)
                                
                            </td>
                            <td>
                                @Html.DisplayFor(m => item.ACCREDITATION_NAME)
                            </td>
                            <td>
                                <span title='@item.CONTENT'>
                                    @(item.SUBJECT)
                                </span>
                            </td>

                            <td>

                                @if (item.states == "NO")
                                {

                                    <label>尚未開放補登</label>
                                }
                                else
                                {

                                    @*if (CERT02.IsText == "Y")
                                        {
                                            List<CERI05EditAccreditationtViewModel> cERI05Edits = new List<CERI05EditAccreditationtViewModel>();
                                            cERI05Edits = Model.Accreditationt1s.Where(x => x.ACCREDITATION_ID == item.ACCREDITATION_ID && x.SCHOOL_NO == item.SCHOOL_NO && x.ITEM_NO == item.ITEM_NO).ToList();
                                            if (cERI05Edits.Count() > 0)
                                            {
                                                foreach (var ii in cERI05Edits)
                                                {

                                                    @Html.EditorFor(m => ii.PersonText, new { @class = "form-control" })

                                                    <input name="@Html.NameFor(m => item.PersonText)" type="hidden" value="@ii.PersonText" />
                                                }
                                            }
                                            else
                                            {
                                                @Html.EditorFor(m => item.PersonText, new { @class = "form-control" })

                                            }
                                            <div id="ADD_@item.ACCREDITATION_ID"></div>


                                        }*@
                                    if (CERT02.IsText != "Y")
                                    {

                                        if (item.IS_PASS != null)
                                        {
                                            <input type="checkbox" class="IsPass" name="@Html.NameFor(m => item.IS_PASS)" id="@Html.IdFor(m => item.IS_PASS)" @((item.IS_PASS ?? false) ? "checked" : "") value="true" />
                                            <input name="@Html.NameFor(m => item.IS_PASS)" type="hidden" value="false" />
                                            //value = "@(Model.IS_PASS ?? false)"

                                        }
                                        else
                                        { <input type="checkbox" class="IsPass" name="@Html.NameFor(m => item.IS_PASS)" id="@Html.IdFor(m => item.IS_PASS)" value="true" />
                                            <input name="@Html.NameFor(m => item.IS_PASS)" type="hidden" value="false" />
                                            @*<input type="checkbox" class="IsPass" name="@Html.NameFor(m => m.IS_PASS)" id="@Html.IdFor(m => m.IS_PASS)" @((Model.IS_PASS ?? false) ? "checked" : "") value="true" />*@

                                        }
                                    }
                                }

                            </td>
                            @*@if (CERT02.IsText == "Y")
                                {
                                    <td>

                                        <button class="btn btn-block btn-default-buy" type="button" onclick="AddItemD('ADD_@item.ACCREDITATION_ID','@item.ACCREDITATION_ID','@item.SCHOOL_NO')">
                                            <span class="fa fa-plus" aria-hidden="true" title="增加一個特殊表現"></span>
                                        </button>

                                    </td>}*@
                            @if (CERT02.IsText != "Y")
                            {
                                <td></td>
                            }
                            <td>
                                @{


                                    List<CERT03_G> cERT03_Gs = new List<CERT03_G>();
                                    cERT03_Gs = db.CERT03_G.Where(x => x.ACCREDITATION_ID == item.ACCREDITATION_ID && x.ITEM_NO == item.ITEM_NO && x.SCHOOL_NO == item.SCHOOL_NO).ToList();

                                    string allSTR = "";
                                    List<CERT03_G> tmeg = new List<CERT03_G>();
                                }
                               
                                @foreach (var g3i in cERT03_Gs.GroupBy(x => x.GRADE))
                                {
                                    foreach (var GradeItem in cERT03_Gs.Where(x => x.GRADE == g3i.Key))
                                    {

                                        string str = GradeItem.GRADE.ToString();

                                        if (GradeItem.SEMESTER == 1)
                                        {

                                            str = str + "上";
                                        }
                                        else
                                        {
                                            str = str + "下";

                                        }
                                        allSTR = allSTR + str;
                                    }





                                }
                                @allSTR




                            </td>
                        </tr>
                    }

                }
            }
        }
    }
