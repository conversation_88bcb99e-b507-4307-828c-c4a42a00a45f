/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX-Web/Normal/Bold/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXMathJax_Normal-bold"]={directory:"Normal/Bold",family:"STIXMathJax_Normal",weight:"bold",testString:"\u00A0\u210E\uD835\uDC00\uD835\uDC01\uD835\uDC02\uD835\uDC03\uD835\uDC04\uD835\uDC05\uD835\uDC06\uD835\uDC07\uD835\uDC08\uD835\uDC09\uD835\uDC0A\uD835\uDC0B\uD835\uDC0C",32:[0,0,250,0,0],160:[0,0,250,0,0],8462:[685,10,576,50,543],119808:[690,0,722,9,689],119809:[676,0,667,16,619],119810:[691,19,722,49,687],119811:[676,0,722,14,690],119812:[676,0,667,16,641],119813:[676,0,611,16,583],119814:[691,19,778,37,755],119815:[676,0,778,21,759],119816:[676,0,389,20,370],119817:[676,96,500,3,478],119818:[676,0,778,30,769],119819:[676,0,667,19,638],119820:[676,0,944,14,921],119821:[676,18,722,16,701],119822:[691,19,778,35,743],119823:[676,0,611,16,600],119824:[691,176,778,35,743],119825:[676,0,722,26,716],119826:[692,19,556,35,513],119827:[676,0,667,31,636],119828:[676,19,722,16,701],119829:[676,18,722,16,701],119830:[676,15,1000,19,981],119831:[676,0,722,16,699],119832:[676,0,722,15,699],119833:[676,0,667,28,634],119834:[473,14,500,25,488],119835:[676,14,556,17,521],119836:[473,14,444,25,430],119837:[676,14,556,25,534],119838:[473,14,444,25,427],119839:[691,0,333,14,389],119840:[473,206,500,28,483],119841:[676,0,556,15,534],119842:[691,0,278,15,256],119843:[691,203,333,-57,263],119844:[676,0,556,22,543],119845:[676,0,278,15,256],119846:[473,0,833,15,814],119847:[473,0,556,21,539],119848:[473,14,500,25,476],119849:[473,205,556,19,524],119850:[473,205,556,34,536],119851:[473,0,444,28,434],119852:[473,14,389,25,361],119853:[630,12,333,19,332],119854:[461,14,556,16,538],119855:[461,14,500,21,485],119856:[461,14,722,23,707],119857:[461,0,500,12,484],119858:[461,205,500,16,482],119859:[461,0,444,21,420],120488:[690,0,735,9,689],120489:[676,0,667,16,619],120490:[676,0,620,16,593],120491:[690,0,691,16,656],120492:[676,0,679,16,641],120493:[676,0,693,28,634],120494:[676,0,810,21,759],120495:[692,18,778,35,743],120496:[676,0,421,20,370],120497:[676,0,820,30,769],120498:[690,0,707,9,674],120499:[676,0,972,14,921],120500:[676,18,722,16,701],120501:[676,0,623,28,595],120502:[691,19,778,35,743],120503:[676,0,780,21,759],120504:[676,0,611,16,600],120505:[692,18,778,35,743],120506:[676,0,665,14,627],120507:[676,0,667,31,636],120508:[692,0,722,3,699],120509:[676,0,836,18,818],120510:[676,0,747,16,699],120511:[692,0,800,3,785],120512:[692,0,778,35,723],120513:[676,14,691,16,656],120514:[473,14,644,25,618],120515:[692,205,556,45,524],120516:[473,205,518,12,501],120517:[692,14,502,26,477],120518:[473,14,444,28,429],120519:[692,205,459,23,437],120520:[473,205,580,12,545],120521:[692,14,501,25,476],120522:[461,14,326,15,304],120523:[473,0,581,21,559],120524:[692,18,546,19,527],120525:[461,205,610,45,588],120526:[473,14,518,15,495],120527:[692,205,465,23,439],120528:[473,14,500,25,476],120529:[461,18,631,20,609],120530:[473,205,547,45,515],120531:[473,203,464,23,444],120532:[461,14,568,25,529],120533:[461,14,492,18,457],120534:[473,14,576,12,551],120535:[473,205,653,24,629],120536:[473,205,612,21,586],120537:[473,205,763,12,751],120538:[473,14,734,26,708],120539:[707,14,515,25,491],120540:[473,14,444,25,430],120541:[692,14,647,12,620],120542:[473,19,563,12,546],120543:[676,205,653,24,629],120544:[473,205,511,25,486],120545:[461,14,864,9,851],120782:[688,13,500,24,476],120783:[688,0,500,65,441],120784:[688,0,500,17,478],120785:[688,14,500,16,468],120786:[688,0,500,19,476],120787:[676,8,500,22,470],120788:[688,13,500,28,475],120789:[676,0,500,17,477],120790:[688,13,500,28,472],120791:[688,13,500,26,473]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"STIXMathJax_Normal-bold"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Normal/Bold/Main.js"]);
