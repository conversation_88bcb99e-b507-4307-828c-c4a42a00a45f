﻿using MvcPaging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class ADDI12IndexListViewModel
    {
        /// <summary>
        /// 判斷從UserController Index 連結進來的
        /// </summary>
        public bool? WhereIsColorboxForUser { get; set; }

        public ADDI12SearchViewModel Search { get; set; }

        public string ActionResultType { get; set; }

        /// <summary>
        /// 一頁筆數 預設筆數
        /// </summary>
        public int PageSize { get; set; }

        /// <summary>
        /// 查詢結果
        /// </summary>
        public IPagedList<ADDI12IndexListDataViewModel> ListData;

        public ADDI12IndexListViewModel()
        {
            PageSize = 9;
        }

        public class ActionResultTypeVal
        {
            /// <summary>
            /// 全部影片
            /// </summary>
            static public string AllVideoView = "AllVideoView";

            /// <summary>
            ///首播影片
            /// </summary>
            static public string PremierView = "PremierView";

            /// <summary>
            ///我的影片
            /// </summary>
            static public string MyVideoView = "MyVideoView";

            /// <summary>
            ///我上傳的影片
            /// </summary>
            static public string MyUploadVideoView = "MyUploadVideoView";

            static public string PremierViewImage = "PremierViewImage";
        }
    }
}