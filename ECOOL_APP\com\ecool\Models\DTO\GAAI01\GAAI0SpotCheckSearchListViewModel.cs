﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class GAAI0SpotCheckSearchListViewModel
    {
        /// <summary>
        ///抽查ID
        /// </summary>
        [DisplayName("抽查ID")]
        public string SPOT_CHECK_ID { get; set; }

        /// <summary>
        ///抽查時間
        /// </summary>
        [DisplayName("抽查時間")]
        [DisplayFormat(DataFormatString = "{0:yyyy/MM/dd HH:mm}", ApplyFormatInEditMode = true)]
        public DateTime? ALARM_DATE { get; set; }

        /// <summary>
        ///學校ID
        /// </summary>
        [DisplayName("學校")]
        public string SCHOOL_NO { get; set; }

        /// <summary>
        ///班級ID
        /// </summary>
        [DisplayName("班級")]
        public string CLASS_NO { get; set; }

        /// <summary>
        ///年級
        /// </summary>
        [DisplayName("年級")]
        public byte? GRADE { get; set; }

        /// <summary>
        ///學年度
        /// </summary>
        [DisplayName("學年度")]
        public byte? SYEAR { get; set; }

        /// <summary>
        ///學期
        /// </summary>
        [DisplayName("學期")]
        public byte? SEMESTER { get; set; }

        /// <summary>
        ///學生ID
        /// </summary>
        [DisplayName("學號")]
        public string USER_NO { get; set; }

        /// <summary>
        ///姓名
        /// </summary>
        [DisplayName("姓名")]
        public string NAME { get; set; }

        /// <summary>
        ///座號
        /// </summary>
        [DisplayName("座號")]
        public string SEAT_NO { get; set; }

        /// <summary>
        ///是否配載
        /// </summary>
        [DisplayName("是否配載")]
        public bool? IS_WEAR { get; set; }

        /// <summary>
        ///未配載原因
        /// </summary>
        [DisplayName("未配載原因")]
        public byte? UN_WEAR_TYPE { get; set; }

        /// <summary>
        ///配載獲得的點數
        /// </summary>
        [DisplayName("配載獲得的點數")]
        public short CASH { get; set; }
    }
}