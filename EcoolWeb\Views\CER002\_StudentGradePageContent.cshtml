﻿@model CER002IndexViewModel
@using ECOOL_APP.com.ecool.util;

@Html.AntiForgeryToken()

@Html.HiddenFor(m => m.OrderByColumnName)
@Html.HiddenFor(m => m.SortType)
@Html.HiddenFor(m => m.WhereUser)
@Html.HiddenFor(m => m.Page)
@Html.HiddenFor(m => m.ListDataHRMT01.whereUserNo)
<div id="Q_Div">
    <div role="form">

        <div class="row">
            <div class="col-md-2">
                <label class="control-label">護照名稱</label>
            </div>
            <div class="col-md-3">
                @Html.DropDownListFor(m => m.WhereACCREDITATION_TYPE, (IEnumerable<SelectListItem>)ViewBag.AccreditationTypeItems, new { @class = "form-control input-sm" })
            </div>
            <div class="col-md-2">
                <label class="control-label">護照明細名稱</label>
            </div>
            <div class="col-md-5">
                <select class="selectpicker show-menu-arrow form-control" title="" date-style="input-sm" data-size="auto" data-live-search="true" id="@Html.IdFor(m=>m.WhereACCREDITATION_NAME)" name="@Html.NameFor(m=>m.WhereACCREDITATION_NAME)">
                    @if (ViewBag.AccreditationsItems != null)
                    {
                        foreach (var item in ViewBag.AccreditationsItems as IEnumerable<SelectListItem>)
                        {
                            <option data-tokens="@item.Text" value="@item.Value" @(item.Selected ? "selected" : "")>@item.Text</option>
                        }
                    }
                </select>
            </div>
        </div>
        <div class="row" style="padding-top:10px">
            <div class="col-md-12">
                <label class="radio-inline">
                    <input type="radio" name="@Html.NameFor(m=>m.WhereGRADE_SEMESTER_TYPE)" id="@Html.IdFor(m=>m.WhereGRADE_SEMESTER_TYPE)" value="0" @(Model.WhereGRADE_SEMESTER_TYPE == 0 ? "checked" : "")>全部護照

                </label>
                <label class="radio-inline">
                    <input type="radio" name="@Html.NameFor(m=>m.WhereGRADE_SEMESTER_TYPE)" id="@Html.IdFor(m=>m.WhereGRADE_SEMESTER_TYPE)" value="1" @(Model.WhereGRADE_SEMESTER_TYPE == 1 ? "checked" : "")>本學期護照
                </label>
            </div>
        </div>
        <br />
        <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="funAjax()" />
        <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear('true')" />

    </div>
</div>

<div class="panel panel-ZZZ" name="TOP">
    <div class="panel-heading text-center">
        @Html.BarTitle(null, (string)ViewBag.Title)
    </div>
    <div>
        <table class="table-ecool table-92Per table-hover table-ecool-ZZZ" style="width:100%">
            <thead>
                <tr class="text-center-Mobile-left">
                    <th class="text-nowrap">@Html.DisplaySortColumnFor(m => m.ListData.First().TYPE_NAME, Model?.OrderByColumnName, Model?.SortType, true)</th>
                    <th class="text-nowrap">@Html.DisplaySortColumnFor(m => m.ListData.First().ACCREDITATION_NAME, Model?.OrderByColumnName, Model?.SortType, true)</th>
                    <th class="text-nowrap">@Html.DisplaySortColumnFor(m => m.ListData.First().SUBJECT, Model?.OrderByColumnName, Model?.SortType, true)</th>
                    <th class="text-nowrap">預計完成時間</th>
                    <th class="text-nowrap">@Html.DisplaySortColumnFor(m => m.ListData.First().VERIFIER, Model?.OrderByColumnName, Model?.SortType, true)</th>

                    <th class="text-nowrap">通過</th>
                    <th style="width:140px">專業護照</th>
                </tr>
            </thead>
            <tbody>
                @if (Model.ListData?.Count() > 0)
                {
                    foreach (var item in Model.ListData)
                    {
                        <tr class="text-center-Mobile-left">
                            <td>@Html.DisplayFor(modelItem => item.TYPE_NAME)</td>
                            <td>@Html.DisplayFor(modelItem => item.ACCREDITATION_NAME)</td>

                            <td>
                                <span data-html="true" data-toggle="tooltip" data-placement="right" title='@(item.CONTENT?.Replace("\n","<br />"))'>
                                    @Html.DisplayFor(modelItem => item.SUBJECT)
                                </span>
                            </td>
                            <td>
                                @if (item.GRADE_SEMESTERs?.Count > 0)
                                {
                                    if (item.GRADE_SEMESTERs.Count() >= 12)
                                    {
                                        <span>不分年級</span>
                                    }
                                    else
                                    {
                                        var GRADE_SEMESTERs = string.Join(",", item.GRADE_SEMESTERs.ToList());

                                        <span data-html="true" data-toggle="tooltip" data-placement="right" title="@(GRADE_SEMESTERs.Replace(",","<br />"))">
                                            @StringHelper.LeftStringR(GRADE_SEMESTERs, 7)
                                        </span>
                                    }
                                }
                            </td>
                            <td>
                                @if (item.IS_PASS ?? false)
                                {
                                    @Html.DisplayFor(modelItem => item.VERIFIER)
                                }
                            </td>
                            <td>
                                @if (item.IS_PASS ?? false)
                                {
                                    <i class="glyphicon glyphicon-ok"></i>
                                }
                            </td>
                            <td style="width:30px">
                                @if (item.IS_PASS ?? false)
                                {
                                    if (!string.IsNullOrEmpty(item.IsText) && item.IsText != "N")
                                    {

                                        @Html.DisplayFor(modelItem => item.PersonText)

                                    }

                                }

                            </td>
                        </tr>
                    }
                }
            </tbody>
        </table>
        @if ((Model.ListData?.Count() ?? 0) == 0)
        {
            <div class="text-center" style="padding:10px"><strong>目前無資料</strong></div>
        }
    </div>
</div>
<div class="row Div-btn-center">
    <div class="col-md-12">
        <a href='@Url.Action("IndexTecher", "CER002")' role="button" class="btn btn-info">
            返回
        </a>
    </div>
</div>
@Html.Pager(Model.ListData.PageSize, Model.ListData.PageNumber, Model.ListData.TotalItemCount).Options(o => o.DisplayTemplate(PageGlobal.DfDisplayTemplate)
.MaxNrOfPages(PageGlobal.DfMaxNrOfPages)
.SetPreviousPageText(PageGlobal.DfSetPreviousPageText)
.SetNextPageText(PageGlobal.DfSetNextPageText)
)

<script>
    $(function () {
        $('[data-toggle="tooltip"]').tooltip()
        $('#@Html.IdFor(m=>m.WhereACCREDITATION_NAME)').selectpicker('refresh');
        $('.table-ecool').basictable({breakpoint: 767});
    })
</script>
