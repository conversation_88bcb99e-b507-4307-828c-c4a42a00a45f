﻿@model CERI05PersonalEditViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")
@{
    Html.RenderAction("_Ceri02Menu", "CERI02", new { NowAction = "CERI05" });
}
@using (Html.BeginForm("PersonalEdit", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1", @class = "form-horizontal", enctype = "multipart/form-data" }))
{

    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })

    @Html.HiddenFor(m => m.Keyword)
    @Html.HiddenFor(m => m.OrderByColumnName)
    @Html.HiddenFor(m => m.SortType)
    @Html.HiddenFor(m => m.Page)

    @Html.HiddenFor(m => m.WhereGRADE)
    @Html.HiddenFor(m => m.WhereCLASS_NO)
    @Html.HiddenFor(m => m.WhereSCHOOL_NO)
    @Html.HiddenFor(m => m.WhereUser)
    @Html.HiddenFor(m => m.ThisSCHOOL_NO)
    @Html.HiddenFor(m => m.ThisUSER_NO)

    @Html.HiddenFor(m => m.MyData.NAME)

    <div class="panel panel-ZZZ" name="TOP">
        <div class="panel-heading text-center">
            @Html.BarTitle(null, (string)ViewBag.Title)
        </div>
        <div class="panel panel-default">
            <div class="panel-body">
                <table class="table table-condensed table-ecool">
                    <thead>
                        <tr>
                            <th>
                                護照名稱
                            </th>
                            <th>
                                細項名稱
                            </th>
                            <th>
                                通過主旨
                            </th>
                            <th>
                                <input type="checkbox" id="CheckAll" title="全部通過/全部取消" />
                                全部通過/全部取消
                            </th>
                            <th></th>
                            <th class="col-sm-2">
                                設定認證
                                時間
                            <th />
                        </tr>
                    </thead>

                    <tbody>
                        @if (Model.Accreditationts?.Count() > 0)
                        {


                            //foreach (var item in Model.Accreditationt1s)
                            //{
                            @Html.Partial("_DetailPersonal", Model);
                            //}
                        }
                    </tbody>
                </table>
            </div>

        </div>
    </div>
    <div class="text-center">
        <hr />
        <button type="button" id="clearform" class="btn btn-default" onclick="onBack()">
            回上一頁
        </button>

        <button type="button" class="btn btn-default" onclick="onSave()">
            <span class="fa fa-check-circle" aria-hidden="true"></span>確定
        </button>
    </div>

}

@section Scripts {

    <script language="JavaScript">

        var targetFormID = '#form1';

        $(document).ready(function(){
            $("#CheckAll").click(function () {

                if($("#CheckAll").prop("checked")){
                  $(".IsPass").prop("checked",true);
                }else{
                  $(".IsPass").prop("checked",false);
                }
            })
        })

        function onSave(StatusVal)
        {
            $(targetFormID).attr("action", "@Url.Action("PersonalEditSave", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        function onBack() {
            $(targetFormID).attr("action", "@Url.Action("PersonalIndex", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }
        
          //增加通過內容
        function AddItemD(str, user_NOItem, SCHOOL_NOItem) {
            var yes = confirm('避免跑版建議一人不要超過兩筆以上');
            if (yes) {
            //var checkBoxsIsTextstr = "";
            //checkBoxsIsTextstr = $("#checkBoxsIsText").prop("checked");
            //if (checkBoxsIsTextstr == true) {

            //}
             var data = {
                 //IsCopy: true,
                 //IsText: checkBoxsIsTextstr,
                 ACCREDITATION_ID: user_NOItem,
                 SCHOOL_NO: SCHOOL_NOItem,
                 //O_IS_PASS: O_IS_PASSItem
            };

              $.ajax({
                url: '@Url.Action("_ADDDetail")',
                data: data,
                type: 'post',
                async: false,
                cache: false,
                dataType: 'html',
                  success: function (data) {
                      $('#' + str).append(data);
                }
              });
            }
        }
    </script>
}
