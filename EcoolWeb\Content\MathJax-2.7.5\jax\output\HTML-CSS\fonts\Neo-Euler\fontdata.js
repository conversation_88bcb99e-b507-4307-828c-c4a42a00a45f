/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Neo-Euler/fontdata.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

(function(o,e,A){var x="2.7.5";var b="NeoEulerMathJax_Alphabets",u="NeoEulerMathJax_Arrows",y="NeoEulerMathJax_Fraktur",t="NeoEulerMathJax_Main",k="NeoEulerMathJax_Marks",w="NeoEulerMathJax_NonUnicode",q="NeoEulerMathJax_Normal",z="NeoEulerMathJax_Operators",n="NeoEulerMathJax_Script",c="NeoEulerMathJax_Shapes",l="NeoEulerMathJax_Size1",j="NeoEulerMathJax_Size2",i="NeoEulerMathJax_Size3",g="NeoEulerMathJax_Size4",f="NeoEulerMathJax_Size5",s="NeoEulerMathJax_Symbols",m="NeoEulerMathJax_Variants",v="NeoEulerMathJax_Normal",a="NeoEulerMathJax_Normal",B="NeoEulerMathJax_Normal";var p="H",d="V",r={load:"extra",dir:p},h={load:"extra",dir:d};o.Augment({FONTDATA:{version:x,TeX_factor:0.958,baselineskip:1.2,lineH:0.8,lineD:0.2,hasStyleChar:true,FONTS:{NeoEulerMathJax_Alphabets:"Alphabets/Regular/Main.js",NeoEulerMathJax_Arrows:"Arrows/Regular/Main.js",NeoEulerMathJax_Fraktur:"Fraktur/Regular/Main.js",NeoEulerMathJax_Main:"Main/Regular/Main.js",NeoEulerMathJax_Marks:"Marks/Regular/Main.js",NeoEulerMathJax_NonUnicode:"NonUnicode/Regular/Main.js",NeoEulerMathJax_Normal:"Normal/Regular/Main.js",NeoEulerMathJax_Operators:"Operators/Regular/Main.js",NeoEulerMathJax_Script:"Script/Regular/Main.js",NeoEulerMathJax_Shapes:"Shapes/Regular/Main.js",NeoEulerMathJax_Size1:"Size1/Regular/Main.js",NeoEulerMathJax_Size2:"Size2/Regular/Main.js",NeoEulerMathJax_Size3:"Size3/Regular/Main.js",NeoEulerMathJax_Size4:"Size4/Regular/Main.js",NeoEulerMathJax_Size5:"Size5/Regular/Main.js",NeoEulerMathJax_Symbols:"Symbols/Regular/Main.js",NeoEulerMathJax_Variants:"Variants/Regular/Main.js"},VARIANT:{normal:{fonts:[t,q,b,k,u,z,s,c,m,w,l]},bold:{fonts:[t,q,b,k,u,z,s,c,m,w,l],bold:true,offsetA:119808,offsetG:120488,offsetN:120782},italic:{fonts:[t,q,b,k,u,z,s,c,m,w,l],italic:true},"bold-italic":{fonts:[t,q,b,k,u,z,s,c,m,w,l],bold:true,italic:true},"double-struck":{fonts:[v],offsetA:120120,offsetN:120792,remap:{120122:8450,120127:8461,120133:8469,120135:8473,120136:8474,120137:8477,120145:8484}},fraktur:{fonts:[y],offsetA:120068,remap:{120070:8493,120075:8460,120076:8465,120085:8476,120093:8488}},"bold-fraktur":{fonts:[y],bold:true,offsetA:120172},script:{fonts:[n],italic:true,offsetA:119964,remap:{119965:8492,119968:8496,119969:8497,119971:8459,119972:8464,119975:8466,119976:8499,119981:8475,119994:8495,119996:8458,120004:8500}},"bold-script":{fonts:[n],bold:true,italic:true,offsetA:120016},"sans-serif":{fonts:[a],offsetA:120224,offsetN:120802},"bold-sans-serif":{fonts:[a],bold:true,offsetA:120276,offsetN:120812,offsetG:120662},"sans-serif-italic":{fonts:[a],italic:true,offsetA:120328},"sans-serif-bold-italic":{fonts:[a],bold:true,italic:true,offsetA:120380,offsetG:120720},monospace:{fonts:[B],offsetA:120432,offsetN:120822},"-Neo-Euler-variant":{fonts:[m,t,q,b,k,u,z,s,c,w,l]},"-tex-caligraphic":{fonts:[t,q,b,k,u,z,s,c,m,w,l],italic:true},"-tex-oldstyle":{offsetN:57856,fonts:[m,t,q,b,k,u,z,s,c,w,l]},"-tex-caligraphic-bold":{fonts:[t,q,b,k,u,z,s,c,m,w,l],italic:true,bold:true},"-tex-oldstyle-bold":{fonts:[t,q,b,k,u,z,s,c,m,w,l],bold:true},"-tex-mathit":{fonts:[t,q,b,k,u,z,s,c,m,w,l],italic:true,noIC:true},"-largeOp":{fonts:[l,t]},"-smallOp":{}},RANGES:[{name:"alpha",low:97,high:122,offset:"A",add:26},{name:"Alpha",low:65,high:90,offset:"A"},{name:"number",low:48,high:57,offset:"N"},{name:"greek",low:945,high:969,offset:"G",add:26},{name:"Greek",low:913,high:1014,offset:"G",remap:{1013:52,977:53,1008:54,981:55,1009:56,982:57,1012:17}}],RULECHAR:8722,REMAP:{10:32,8432:42,8226:8729,8931:"\u2292\u0338",8930:"\u2291\u0338",12296:10216,713:175,8215:95,8428:8641,8429:8637,10799:215,8400:8636,8401:8640,978:933,8212:175,8213:175,12297:10217,65079:9182,65080:9183,697:8242,10072:8739,8254:175},REMAPACCENT:{"\u007E":"\u0303","\u2192":"\u20D7","\u0060":"\u0300","\u005E":"\u0302","\u00B4":"\u0301","\u2032":"\u0301","\u2035":"\u0300"},REMAPACCENTUNDER:{},DELIMITERS:{40:{dir:d,HW:[[0.925,t],[1.198,l],[1.798,j],[1.961,j,1.091],[2.398,i],[2.998,g]],stretch:{bot:[9117,s],ext:[9116,s],top:[9115,s]}},41:{dir:d,HW:[[0.925,t],[1.198,l],[1.798,j],[1.961,j,1.091],[2.398,i],[2.998,g]],stretch:{bot:[9120,s],ext:[9119,s],top:[9118,s]}},45:{alias:175,dir:p},47:{dir:d,HW:[[0.912,t],[1.199,l],[1.799,j],[1.961,j,1.09],[2.399,i],[2.999,g]]},61:{dir:p,HW:[[0.668,t]],stretch:{rep:[61,t]}},91:{dir:d,HW:[[0.866,t],[1.199,l],[1.799,j],[1.961,j,1.09],[2.399,i],[2.999,g]],stretch:{bot:[9123,s],ext:[9122,s],top:[9121,s]}},92:{dir:d,HW:[[0.914,t],[1.199,l],[1.799,j],[1.961,j,1.09],[2.399,i],[2.999,g]]},93:{dir:d,HW:[[0.866,t],[1.199,l],[1.799,j],[1.961,j,1.09],[2.399,i],[2.999,g]],stretch:{bot:[9126,s],ext:[9125,s],top:[9124,s]}},95:{alias:175,dir:p},123:{dir:d,HW:[[0.908,t],[1.199,l],[1.799,j],[1.961,j,1.09],[2.399,i],[2.999,g]],stretch:{bot:[9129,s],ext:[9130,s],mid:[9128,s],top:[9127,s]}},124:{dir:d,HW:[[0.905,t],[1.505,l],[2.105,j],[2.706,i],[3.306,g]],stretch:{bot:[57344,f],ext:[57345,f]}},125:{dir:d,HW:[[0.908,t],[1.199,l],[1.799,j],[1.961,j,1.09],[2.399,i],[2.999,g]],stretch:{bot:[9133,s],ext:[9130,s],mid:[9132,s],top:[9131,s]}},175:{dir:p,HW:[[0.312,t]],stretch:{rep:[175,t]}},201:{alias:175,dir:p},818:{alias:175,dir:p},8213:{alias:175,dir:p},8214:{dir:d,HW:[[0.905,t],[1.505,l],[2.105,j],[2.706,i],[3.306,g]],stretch:{bot:[57346,f],ext:[57347,f]}},8215:{alias:175,dir:p},8254:{alias:175,dir:p},8260:h,8406:{dir:p,HW:[[0.418,k]],stretch:{left:[8406,k],rep:[57348,f]}},8407:{dir:p,HW:[[0.418,t]],stretch:{rep:[57348,f],right:[8407,t]}},8417:r,8430:r,8431:r,8592:{alias:8406,dir:p},8593:{dir:d,HW:[[0.887,t]],stretch:{top:[8593,t],ext:[124,t]}},8594:{alias:8407,dir:p},8595:{dir:d,HW:[[0.867,t]],stretch:{ext:[124,t],bot:[8595,t]}},8596:{alias:8417,dir:p},8597:{dir:d,HW:[[1.042,t]],stretch:{top:[8593,t],ext:[124,t],bot:[8595,t]}},8656:{dir:p,HW:[[0.867,t],[1.567,l]]},8657:{dir:p,HW:[[0.64,t]],stretch:{top:[8657,t],ext:[8214,t]}},8658:{dir:p,HW:[[0.867,t],[1.567,l]]},8659:{dir:p,HW:[[0.64,t]],stretch:{ext:[8214,t],bot:[8659,t]}},8660:{dir:p,HW:[[0.867,t,null,8656],[1.632,l]]},8661:{dir:p,HW:[[0.64,t]],stretch:{top:[8657,t],ext:[8214,t],bot:[8659,t]}},8719:h,8720:h,8721:h,8722:{dir:p,HW:[],stretch:{rep:[8722,t,0,0,0,-0.31,-0.31]}},8725:{dir:d,HW:[[0.912,t],[1.199,l],[1.799,j],[2.399,i],[2.999,g]]},8730:{dir:d,HW:[[0.989,t],[1.209,l],[1.801,j],[2.403,i],[3.003,g]],stretch:{bot:[57350,f],ext:[57351,f],top:[57352,f]}},8739:{dir:d,HW:[[0.795,t],[1.505,l],[2.105,j],[2.706,i],[3.306,g]]},8741:{dir:d,HW:[[0.905,t],[0.905,l],[1.505,j],[2.105,i],[2.706,g],[3.306,f]],stretch:{bot:[57346,f],ext:[57347,f]}},8743:h,8744:h,8745:h,8746:h,8747:h,8748:h,8749:h,8750:h,8846:h,8896:h,8897:h,8898:h,8899:h,8968:{dir:d,HW:[[0.98,t],[1.199,l],[1.799,j],[1.961,j,1.09],[2.399,i],[2.999,g]],stretch:{ext:[9122,s],top:[9121,s]}},8969:{dir:d,HW:[[0.98,t],[1.199,l],[1.799,j],[1.961,j,1.09],[2.399,i],[2.999,g]],stretch:{ext:[9125,s],top:[9124,s]}},8970:{dir:d,HW:[[0.98,t],[1.199,l],[1.799,j],[1.961,j,1.09],[2.399,i],[2.999,g]],stretch:{bot:[9123,s],ext:[9122,s]}},8971:{dir:d,HW:[[0.98,t],[1.199,l],[1.799,j],[1.961,j,1.09],[2.399,i],[2.999,g]],stretch:{bot:[9126,s],ext:[9125,s]}},8978:{alias:9180,dir:p},8994:{alias:9180,dir:p},8995:{alias:9181,dir:p},9001:{dir:d,HW:[[0.974,s],[1.176,l],[1.77,j],[2.366,i],[2.958,g]]},9002:{dir:d,HW:[[0.974,s],[1.176,l],[1.77,j],[2.366,i],[2.958,g]]},9130:{dir:d,HW:[[0.32,s]],stretch:{ext:[9130,s]}},9135:{alias:175,dir:p},9136:{dir:d,HW:[[0.909,s,null,9127]],stretch:{top:[9127,s],ext:[9130,s],bot:[9133,s]}},9137:{dir:d,HW:[[0.909,s,null,9131]],stretch:{top:[9131,s],ext:[9130,s],bot:[9129,s]}},9168:{dir:d,HW:[[0.905,t,null,124],[1.15,t,1.271,124],[1.556,t,1.719,124],[1.961,t,2.167,124],[2.367,t,2.615,124]],stretch:{ext:[124,t]}},9180:r,9181:r,9182:{dir:p,HW:[[0.908,t],[1.199,l],[1.799,j],[2.399,i],[2.999,g]],stretch:{left:[57359,f],rep:[57360,f],mid:[57361,f],right:[57362,f]}},9183:{dir:p,HW:[[0.908,t],[1.199,l],[1.799,j],[2.399,i],[2.999,g]],stretch:{left:[57363,f],rep:[57364,f],mid:[57365,f],right:[57366,f]}},9472:{alias:175,dir:p},10072:{alias:9168,dir:d},10216:{dir:d,HW:[[0.974,t],[0.974,l],[1.176,j],[1.77,i],[2.366,g],[2.958,f]]},10217:{dir:d,HW:[[0.974,t],[0.974,l],[1.176,j],[1.77,i],[2.366,g],[2.958,f]]},10222:{alias:40,dir:d},10223:{alias:41,dir:d},10229:{alias:8406,dir:p},10230:{alias:8407,dir:p},10231:{alias:8417,dir:p},10232:{alias:8656,dir:p},10233:{alias:8658,dir:p},10234:{alias:8660,dir:p},10235:{alias:8406,dir:p},10236:{alias:8407,dir:p},10237:{alias:8656,dir:p},10238:{alias:8658,dir:p},10764:h,12296:{alias:10216,dir:d},12297:{alias:10217,dir:d},65079:{alias:9182,dir:p},65080:{alias:9183,dir:p}}}});MathJax.Hub.Register.LoadHook(o.fontDir+"/Main/Regular/Main.js",function(){o.FONTDATA.FONTS[t][8722][0]=o.FONTDATA.FONTS[t][43][0];o.FONTDATA.FONTS[t][8722][1]=o.FONTDATA.FONTS[t][43][1]});MathJax.Hub.Register.LoadHook(o.fontDir+"/Size5/Regular/Main.js",function(){var C;C=o.FONTDATA.DELIMITERS[9182].stretch.rep[0];o.FONTDATA.FONTS[f][C][0]+=200;o.FONTDATA.FONTS[f][C][1]+=200;C=o.FONTDATA.DELIMITERS[9183].stretch.rep[0];o.FONTDATA.FONTS[f][C][0]+=200;o.FONTDATA.FONTS[f][C][1]+=200});A.loadComplete(o.fontDir+"/fontdata.js")})(MathJax.OutputJax["HTML-CSS"],MathJax.ElementJax.mml,MathJax.Ajax);
