<?xml version="1.0"?>
<doc>
    <assembly>
        <name>MiniProfiler</name>
    </assembly>
    <members>
        <member name="T:StackExchange.Profiling.BaseProfilerProvider">
            <summary>
            BaseProfilerProvider.  This providers some helper methods which provide access to
            internals not otherwise available.
            To use, override the <see cref="M:StackExchange.Profiling.BaseProfilerProvider.Start(System.String)"/>, <see cref="M:StackExchange.Profiling.BaseProfilerProvider.Stop(System.Boolean)"/> and <see cref="M:StackExchange.Profiling.BaseProfilerProvider.GetCurrentProfiler"/>
            methods.
            </summary>
        </member>
        <member name="T:StackExchange.Profiling.IProfilerProvider">
            <summary>
            A provider used to create <see cref="T:StackExchange.Profiling.MiniProfiler"/> instances and maintain the current instance.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.IProfilerProvider.Start(StackExchange.Profiling.ProfileLevel,System.String)">
            <summary>
            Starts a new MiniProfiler and sets it to be current.  By the end of this method
            <see cref="M:StackExchange.Profiling.IProfilerProvider.GetCurrentProfiler"/> should return the new MiniProfiler.
            </summary>
            <param name="level">The <see cref="T:StackExchange.Profiling.ProfileLevel"/> to use.</param>
            <param name="sessionName">
            Allows explicit naming of the new profiling session; when null, an appropriate default will be used, e.g. for
            a web request, the url will be used for the overall session name.
            </param>
        </member>
        <member name="M:StackExchange.Profiling.IProfilerProvider.Stop(System.Boolean)">
            <summary>
            Ends the current profiling session, if one exists.
            </summary>
            <param name="discardResults">
            When true, clears the <see cref="P:StackExchange.Profiling.MiniProfiler.Current"/> for this HttpContext, allowing profiling to 
            be prematurely stopped and discarded. Useful for when a specific route does not need to be profiled.
            </param>
        </member>
        <member name="M:StackExchange.Profiling.IProfilerProvider.GetCurrentProfiler">
            <summary>
            Returns the current MiniProfiler.  This is used by <see cref="P:StackExchange.Profiling.MiniProfiler.Current"/>.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.BaseProfilerProvider.Start(StackExchange.Profiling.ProfileLevel,System.String)">
            <summary>
            Starts a new MiniProfiler and sets it to be current.  By the end of this method
            <see cref="M:StackExchange.Profiling.BaseProfilerProvider.GetCurrentProfiler"/> should return the new MiniProfiler.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.BaseProfilerProvider.Start(System.String)">
            <summary>
            Starts a new MiniProfiler and sets it to be current.  By the end of this method
            <see cref="M:StackExchange.Profiling.BaseProfilerProvider.GetCurrentProfiler"/> should return the new MiniProfiler.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.BaseProfilerProvider.Stop(System.Boolean)">
            <summary>
            Stops the current MiniProfiler (if any is currently running).
            <see cref="M:StackExchange.Profiling.BaseProfilerProvider.SaveProfiler(StackExchange.Profiling.MiniProfiler)"/> should be called if <paramref name="discardResults"/> is false
            </summary>
            <param name="discardResults">If true, any current results will be thrown away and nothing saved</param>
        </member>
        <member name="M:StackExchange.Profiling.BaseProfilerProvider.GetCurrentProfiler">
            <summary>
            Returns the current MiniProfiler.  This is used by <see cref="P:StackExchange.Profiling.MiniProfiler.Current"/>.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.BaseProfilerProvider.SetProfilerActive(StackExchange.Profiling.MiniProfiler)">
            <summary>
            Sets <paramref name="profiler"/> to be active (read to start profiling)
            This should be called once a new MiniProfiler has been created.
            </summary>
            <exception cref="T:System.ArgumentNullException">If <paramref name="profiler"/> is null</exception>
        </member>
        <member name="M:StackExchange.Profiling.BaseProfilerProvider.StopProfiler(StackExchange.Profiling.MiniProfiler)">
            <summary>
            Stops the profiler and marks it as inactive.
            </summary>
            <returns>True if successful, false if Stop had previously been called on this profiler</returns>
            <exception cref="T:System.ArgumentNullException">If <paramref name="profiler"/> is null</exception>
        </member>
        <member name="M:StackExchange.Profiling.BaseProfilerProvider.SaveProfiler(StackExchange.Profiling.MiniProfiler)">
            <summary>
            Calls <see cref="M:StackExchange.Profiling.MiniProfiler.Settings.EnsureStorageStrategy"/> to save the current
            profiler using the current storage settings. 
            If <see cref="P:StackExchange.Profiling.MiniProfiler.Storage"/> is set, this will be used.
            </summary>
        </member>
        <member name="T:StackExchange.Profiling.ClientTimings">
            <summary>
            Times collected from the client
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.ClientTimings.FromRequest(System.Web.HttpRequest)">
            <summary>
            Returns null if there is not client timing stuff
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.ClientTimings.Timings">
            <summary>
            Gets or sets the list of client side timings
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.ClientTimings.RedirectCount">
            <summary>
            Gets or sets the redirect count.
            </summary>
        </member>
        <member name="T:StackExchange.Profiling.ClientTimings.ClientTiming">
            <summary>
            A client timing probe
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.ClientTimings.ClientTiming.Name">
            <summary>
            Gets or sets the name.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.ClientTimings.ClientTiming.Start">
            <summary>
            Gets or sets the start.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.ClientTimings.ClientTiming.Duration">
            <summary>
            Gets or sets the duration.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.ClientTimings.ClientTiming.Id">
            <summary>
            Unique Identifier used for sql storage. 
            </summary>
            <remarks>Not set unless storing in Sql</remarks>
        </member>
        <member name="P:StackExchange.Profiling.ClientTimings.ClientTiming.MiniProfilerId">
            <summary>
            Used for sql storage
            </summary>
            <remarks>Not set unless storing in Sql</remarks>
        </member>
        <member name="T:StackExchange.Profiling.CustomTiming">
            <summary>
            A custom timing that usually represents a Remote Procedure Call, allowing better
            visibility into these longer-running calls.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.CustomTiming.#ctor">
            <summary>
            Don't use this.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.CustomTiming.#ctor(StackExchange.Profiling.MiniProfiler,System.String,System.Nullable{System.Decimal})">
            <summary>
            Returns a new CustomTiming, also initializing its <see cref="P:StackExchange.Profiling.CustomTiming.Id"/> and, optionally, its <see cref="P:StackExchange.Profiling.CustomTiming.StackTraceSnippet"/>.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.CustomTiming.FirstFetchCompleted">
            <summary>
            OPTIONAL - call after receiving the first response from your Remote Procedure Call to 
            properly set <see cref="P:StackExchange.Profiling.CustomTiming.FirstFetchDurationMilliseconds"/>.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.CustomTiming.Stop">
            <summary>
            Stops this timing, setting <see cref="P:StackExchange.Profiling.CustomTiming.DurationMilliseconds"/>.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.CustomTiming.ToString">
            <summary>
            Returns <see cref="P:StackExchange.Profiling.CustomTiming.CommandString"/> for debugging.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.CustomTiming.Id">
            <summary>
            Unique identifier for this <see cref="T:StackExchange.Profiling.CustomTiming"/>.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.CustomTiming.CommandString">
            <summary>
            Gets or sets the command that was executed, e.g. "select * from Table" or "INCR my:redis:key"
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.CustomTiming.ExecuteType">
            <summary>
            Short name describing what kind of custom timing this is, e.g. "Get", "Query", "Fetch".
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.CustomTiming.StackTraceSnippet">
            <summary>
            Gets or sets roughly where in the calling code that this custom timing was executed.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.CustomTiming.StartMilliseconds">
            <summary>
            Gets or sets the offset from main <c>MiniProfiler</c> start that this custom command began.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.CustomTiming.DurationMilliseconds">
            <summary>
            Gets or sets how long this custom command statement took to execute.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.CustomTiming.FirstFetchDurationMilliseconds">
            <summary>
            OPTIONAL - how long this tim took to come back initially from the remote server, 
            before all data is fetched and command is completed.
            </summary>
        </member>
        <member name="T:StackExchange.Profiling.MiniProfilerHandler">
            <summary>
            Understands how to route and respond to MiniProfiler UI URLS.
            </summary>
        </member>
        <member name="F:StackExchange.Profiling.MiniProfilerHandler.ResourceCache">
            <summary>
            Embedded resource contents keyed by filename.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.MiniProfilerHandler.RegisterRoutes">
            <summary>
            Usually called internally, sometimes you may clear the routes during the apps lifecycle, 
            if you do that call this to bring back mini profiler.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.MiniProfilerHandler.System#Web#Routing#IRouteHandler#GetHttpHandler(System.Web.Routing.RequestContext)">
            <summary>
            Returns this <see cref="T:StackExchange.Profiling.MiniProfilerHandler"/> to handle <paramref name="requestContext"/>.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.MiniProfilerHandler.ProcessRequest(System.Web.HttpContext)">
            <summary>
            Returns either includes' <c>css/javascript</c> or results' html.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.MiniProfilerHandler.RenderIncludes(StackExchange.Profiling.MiniProfiler,System.Nullable{StackExchange.Profiling.RenderPosition},System.Nullable{System.Boolean},System.Nullable{System.Boolean},System.Nullable{System.Int32},System.Nullable{System.Boolean},System.Nullable{System.Boolean})">
            <summary>
            Renders script tag found in "include.partial.html".
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.MiniProfilerHandler.Includes(System.Web.HttpContext,System.String)">
            <summary>
            Handles rendering static content files.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.MiniProfilerHandler.AuthorizeRequest(System.Web.HttpContext,System.Boolean,System.String@)">
            <summary>
            Returns true if the current request is allowed to see the profiler response.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.MiniProfilerHandler.GetSingleProfilerResult(System.Web.HttpContext)">
            <summary>
            Returns either json or full page html of a previous <c>MiniProfiler</c> session, 
            identified by its <c>"?id=GUID"</c> on the query.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.MiniProfilerHandler.IsReusable">
            <summary>
            Gets a value indicating whether to keep things static and reusable.
            </summary>
        </member>
        <member name="T:StackExchange.Profiling.SqlFormatters.IAdvancedSqlFormatter">
            <summary>
            Takes a <c>SqlTiming</c> and returns a formatted SQL string, for parameter replacement, etc.
            </summary>
        </member>
        <member name="T:StackExchange.Profiling.SqlFormatters.ISqlFormatter">
            <summary>
            Takes a <c>SqlTiming</c> and returns a formatted SQL string, for parameter replacement, etc.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.SqlFormatters.ISqlFormatter.FormatSql(System.String,System.Collections.Generic.List{StackExchange.Profiling.SqlTimingParameter})">
            <summary>
            Return SQL the way you want it to look on the in the trace. Usually used to format parameters.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.SqlFormatters.IAdvancedSqlFormatter.FormatSql(System.String,System.Collections.Generic.List{StackExchange.Profiling.SqlTimingParameter},System.Data.IDbCommand)">
            <summary>
            Return SQL the way you want it to look on the in the trace. Usually used to format parameters.
            </summary>
        </member>
        <member name="T:StackExchange.Profiling.SqlFormatters.SqlFormatterExtensions">
            <summary>
            Extensions for ISqlFormatter instances
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.SqlFormatters.SqlFormatterExtensions.GetFormattedSql(StackExchange.Profiling.SqlFormatters.ISqlFormatter,System.String,System.Collections.Generic.List{StackExchange.Profiling.SqlTimingParameter},System.Data.IDbCommand)">
            <summary>
            Format sql using the FormatSql method available in the current <see cref="P:StackExchange.Profiling.MiniProfiler.Settings.SqlFormatter"/>. 
            </summary>
            <remarks>It is preferable to use this rather than accessing <see cref="M:StackExchange.Profiling.SqlFormatters.ISqlFormatter.FormatSql(System.String,System.Collections.Generic.List{StackExchange.Profiling.SqlTimingParameter})"/> directly, 
            as this method will detect whether an <see cref="T:StackExchange.Profiling.SqlFormatters.IAdvancedSqlFormatter"/> is being used, and will access it properly. 
            This may be removed in a future major version when <see cref="T:StackExchange.Profiling.SqlFormatters.IAdvancedSqlFormatter"/> can be consolidated back
            into <see cref="T:StackExchange.Profiling.SqlFormatters.ISqlFormatter"/>.
            </remarks>
            <returns></returns>
        </member>
        <member name="T:StackExchange.Profiling.SqlFormatters.VerboseSqlServerFormatter">
            <summary>
            Formats SQL server queries with a DECLARE up top for parameter values
            </summary>
            
        </member>
        <member name="T:StackExchange.Profiling.SqlFormatters.SqlServerFormatter">
            <summary>
            Formats SQL server queries with a DECLARE up top for parameter values
            </summary>
        </member>
        <member name="F:StackExchange.Profiling.SqlFormatters.SqlServerFormatter.ParamTranslator">
            <summary>
            Lookup a function for translating a parameter by parameter type
            </summary>
        </member>
        <member name="F:StackExchange.Profiling.SqlFormatters.SqlServerFormatter.DontQuote">
            <summary>
            What data types should not be quoted when used in parameters
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.SqlFormatters.SqlServerFormatter.#cctor">
            <summary>
            Initialises static members of the <see cref="T:StackExchange.Profiling.SqlFormatters.SqlServerFormatter"/> class.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.SqlFormatters.SqlServerFormatter.FormatSql(System.String,System.Collections.Generic.List{StackExchange.Profiling.SqlTimingParameter})">
            <summary>
            Formats the SQL in a SQL-Server friendly way, with DECLARE statements for the parameters up top.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.SqlFormatters.SqlServerFormatter.FormatSql(System.String,System.Collections.Generic.List{StackExchange.Profiling.SqlTimingParameter},System.Data.IDbCommand)">
            <summary>
            Formats the SQL in a SQL-Server friendly way, with DECLARE statements for the parameters up top.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.SqlFormatters.SqlServerFormatter.TerminateSqlStatement(System.String)">
            <summary>
            This function is necessary to always return the sql statement terminated with a semicolon.
            Since we're using semicolons, we should also add it to the end.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.SqlFormatters.SqlServerFormatter.GenerateParamText(System.Text.StringBuilder,System.Collections.Generic.List{StackExchange.Profiling.SqlTimingParameter})">
            <summary>
            Generate formatter output text for all <paramref name="parameters"/>.
            </summary>
            <param name="buffer"><see cref="T:System.Text.StringBuilder"/> to use</param>
            <param name="parameters">Parameters to evaluate</param>
            <returns></returns>
        </member>
        <member name="M:StackExchange.Profiling.SqlFormatters.SqlServerFormatter.PrepareValue(StackExchange.Profiling.SqlTimingParameter)">
            <summary>
            Prepare the parameter value for use in SqlFormatter output
            </summary>
            <param name="parameter"></param>
            <returns></returns>
        </member>
        <member name="M:StackExchange.Profiling.SqlFormatters.VerboseSqlServerFormatter.#ctor(System.Boolean)">
            <summary>
            The constructor
            </summary>
            <param name="includeMetaData"></param>
        </member>
        <member name="M:StackExchange.Profiling.SqlFormatters.VerboseSqlServerFormatter.FormatSql(System.String,System.Collections.Generic.List{StackExchange.Profiling.SqlTimingParameter},System.Data.IDbCommand)">
            <summary>
            Formats the SQL in a SQL-Server friendly way, with DECLARE statements for the parameters up top.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.SqlFormatters.VerboseSqlServerFormatter.IncludeMetaData">
            <summary>
            Should meta data relating to the command type, database and transaction be included in sql output
            </summary>
        </member>
        <member name="T:StackExchange.Profiling.SqlTiming">
            <summary>
            Profiles a single SQL execution.
            </summary>
        </member>
        <member name="F:StackExchange.Profiling.SqlTiming.MaxByteParameterSize">
            <summary>
            Holds the maximum size that will be stored for byte[] parameters
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.SqlTiming.#ctor(System.Data.IDbCommand,StackExchange.Profiling.Data.SqlExecuteType,StackExchange.Profiling.MiniProfiler)">
            <summary>
            Initialises a new instance of the <see cref="T:StackExchange.Profiling.SqlTiming"/> class. 
            Creates a new <c>SqlTiming</c> to profile 'command'.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.SqlTiming.ToString">
            <summary>
            Returns a snippet of the SQL command and the duration.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.SqlTiming.Equals(System.Object)">
            <summary>
            Returns true if Ids match.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.SqlTiming.GetHashCode">
            <summary>
            Returns hash code of Id.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.SqlTiming.ExecutionComplete(System.Boolean)">
            <summary>
            Called when command execution is finished to determine this <c>SqlTiming's</c> duration.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.SqlTiming.ReaderFetchComplete">
            <summary>
            Called when database reader is closed, ending profiling for 
            <see cref="F:StackExchange.Profiling.Data.SqlExecuteType.Reader"/> <c>SqlTimings</c>.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.SqlTiming.GetValue(System.Data.IDataParameter)">
            <summary>
            Returns the value of <paramref name="parameter"/> suitable for storage/display.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.SqlTiming.AddSpacesToParameters(System.String)">
            <summary>
            To help with display, put some space around crowded commas.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.SqlTiming.GetCommandParameters(System.Data.IDbCommand)">
            <summary>
            Returns better parameter information for <paramref name="command"/>.  Returns null if no parameters are present.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.SqlTiming.StartMilliseconds">
            <summary>
            Gets or sets the offset from main <c>MiniProfiler</c> start that this custom command began.
            </summary>
        </member>
        <member name="T:StackExchange.Profiling.SqlTimingParameter">
            <summary>
            Information about a DbParameter used in the sql statement profiled by SqlTiming.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.SqlTimingParameter.Equals(System.Object)">
            <summary>
            Returns true if this has the same parent  
            <see cref="P:StackExchange.Profiling.SqlTimingParameter.Name"/> and <see cref="P:StackExchange.Profiling.SqlTimingParameter.Value"/> as <paramref name="obj"/>.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.SqlTimingParameter.GetHashCode">
            <summary>
            Returns the XOR of certain properties.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.SqlTimingParameter.ToString">
            <summary>
            Returns name and value for debugging.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.SqlTimingParameter.Name">
            <summary>
            Parameter name, e.g. "routeName"
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.SqlTimingParameter.Value">
            <summary>
            The value submitted to the database.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.SqlTimingParameter.DbType">
            <summary>
            System.Data.DbType, e.g. "String", "Bit"
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.SqlTimingParameter.Size">
            <summary>
            How large the type is, e.g. for string, size could be 4000
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.SqlTimingParameter.Direction">
            <summary>
            System.Data.ParameterDirection: "Input", "Output", "InputOutput", "ReturnValue"
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.SqlTimingParameter.IsNullable">
            <summary>
            Gets or sets a value that indicates whether the parameter accepts null values.
            </summary>
        </member>
        <member name="T:StackExchange.Profiling.Storage.DatabaseStorageBase">
            <summary>
            Understands how to save MiniProfiler results to a MSSQL database, allowing more permanent storage and querying of slow results.
            </summary>
        </member>
        <member name="T:StackExchange.Profiling.Storage.IStorage">
            <summary>
            Provides saving and loading <see cref="T:StackExchange.Profiling.MiniProfiler"/>s to a storage medium.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Storage.IStorage.List(System.Int32,System.Nullable{System.DateTime},System.Nullable{System.DateTime},StackExchange.Profiling.Storage.ListResultsOrder)">
            <summary>
            List the latest profiling results.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Storage.IStorage.Save(StackExchange.Profiling.MiniProfiler)">
            <summary>
            Stores <paramref name="profiler"/> under its <see cref="P:StackExchange.Profiling.MiniProfiler.Id"/>.
            </summary>
            <param name="profiler">The results of a profiling session.</param>
            <remarks>
            Should also ensure the profiler is stored as being un-viewed by its profiling <see cref="P:StackExchange.Profiling.MiniProfiler.User"/>.
            </remarks>
        </member>
        <member name="M:StackExchange.Profiling.Storage.IStorage.Load(System.Guid)">
            <summary>
            Returns a <see cref="T:StackExchange.Profiling.MiniProfiler"/> from storage based on <paramref name="id"/>, 
            which should map to <see cref="P:StackExchange.Profiling.MiniProfiler.Id"/>.
            </summary>
            <remarks>
            Should also update that the resulting profiler has been marked as viewed by its 
            profiling <see cref="P:StackExchange.Profiling.MiniProfiler.User"/>.
            </remarks>
        </member>
        <member name="M:StackExchange.Profiling.Storage.IStorage.SetUnviewed(System.String,System.Guid)">
            <summary>
            Sets a particular profiler session so it is considered "un-viewed"  
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Storage.IStorage.SetViewed(System.String,System.Guid)">
            <summary>
            Sets a particular profiler session to "viewed"
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Storage.IStorage.GetUnviewedIds(System.String)">
            <summary>
            Returns a list of <see cref="P:StackExchange.Profiling.MiniProfiler.Id"/>s that haven't been seen by <paramref name="user"/>.
            </summary>
            <param name="user">
            User identified by the current <c>MiniProfiler.Settings.UserProvider</c>
            </param>
        </member>
        <member name="M:StackExchange.Profiling.Storage.DatabaseStorageBase.#ctor(System.String)">
            <summary>
            Initialises a new instance of the <see cref="T:StackExchange.Profiling.Storage.DatabaseStorageBase"/> class. 
            Returns a new <c>SqlServerDatabaseStorage</c> object that will insert into the database identified by connectionString.
            </summary>
            <param name="connectionString">The connection String.</param>
        </member>
        <member name="M:StackExchange.Profiling.Storage.DatabaseStorageBase.#ctor(System.Configuration.ConnectionStringSettings)">
            <summary>
            Initialises a new instance of the <see cref="T:StackExchange.Profiling.Storage.DatabaseStorageBase"/> class. 
            Returns a new <c>SqlServerDatabaseStorage</c> object that will insert into the database identified by connection string settings.
            </summary>
            <param name="connectionStringSettings">
            The connection string settings read from ConfigurationManager.ConnectionStrings["connection"]
            </param>
        </member>
        <member name="M:StackExchange.Profiling.Storage.DatabaseStorageBase.Save(StackExchange.Profiling.MiniProfiler)">
            <summary>
            Saves 'profiler' to a database under its <see cref="P:StackExchange.Profiling.MiniProfiler.Id"/>.
            </summary>
            <param name="profiler">The profiler.</param>
        </member>
        <member name="M:StackExchange.Profiling.Storage.DatabaseStorageBase.Load(System.Guid)">
            <summary>
            Returns the MiniProfiler identified by 'id' from the database or null when no MiniProfiler exists under that 'id'.
            </summary>
            <param name="id">
            The id.
            </param>
            <returns>the mini profiler</returns>
        </member>
        <member name="M:StackExchange.Profiling.Storage.DatabaseStorageBase.SetUnviewed(System.String,System.Guid)">
            <summary>
            Sets a particular profiler session so it is considered "un-viewed"  
            </summary>
            <param name="user">The user.</param>
            <param name="id">The id.</param>
        </member>
        <member name="M:StackExchange.Profiling.Storage.DatabaseStorageBase.SetViewed(System.String,System.Guid)">
            <summary>
            Sets a particular profiler session to "viewed"
            </summary>
            <param name="user">The user.</param>
            <param name="id">The id.</param>
        </member>
        <member name="M:StackExchange.Profiling.Storage.DatabaseStorageBase.GetUnviewedIds(System.String)">
            <summary>
            Returns a list of <see cref="P:StackExchange.Profiling.MiniProfiler.Id"/>s that haven't been seen by <paramref name="user"/>.
            </summary>
            <param name="user">
            User identified by the current <c>MiniProfiler.Settings.UserProvider</c>.
            </param>
            <returns>the list of keys for the supplied user</returns>
        </member>
        <member name="M:StackExchange.Profiling.Storage.DatabaseStorageBase.List(System.Int32,System.Nullable{System.DateTime},System.Nullable{System.DateTime},StackExchange.Profiling.Storage.ListResultsOrder)">
            <summary>
            Implement a basic list search here
            </summary>
            <param name="maxResults">The max number of results.</param>
            <param name="start">The start.</param>
            <param name="finish">The finish.</param>
            <param name="orderBy">order By.</param>
            <returns>the list of GUID keys</returns>
        </member>
        <member name="P:StackExchange.Profiling.Storage.DatabaseStorageBase.ConnectionString">
            <summary>
            Gets or sets how we connect to the database used to save/load MiniProfiler results.
            </summary>
        </member>
        <member name="T:StackExchange.Profiling.Storage.MultiStorageProvider">
            <summary>
            Allow for results to be stored in and retrieved from multiple IStorage stores.
            When reading Loading a MiniProfiler, will load from the first Store that returns a record for the Guid.
            When saving, will save in all Stores.
            </summary>
            <example>Ideal usage scenario - you want to store requests in Cache and Sql Server, but only want to retrieve from Cache if it is available</example>
        </member>
        <member name="M:StackExchange.Profiling.Storage.MultiStorageProvider.#ctor(StackExchange.Profiling.Storage.IStorage[])">
            <summary>
            Create the <see cref="T:StackExchange.Profiling.Storage.MultiStorageProvider"/> with the given collection of <see cref="T:StackExchange.Profiling.Storage.IStorage"/> objects (order is important!)
            </summary>
            <param name="stores">The <see cref="T:StackExchange.Profiling.Storage.IStorage"/> objects to use for storage (order is important!)</param>
        </member>
        <member name="M:StackExchange.Profiling.Storage.MultiStorageProvider.List(System.Int32,System.Nullable{System.DateTime},System.Nullable{System.DateTime},StackExchange.Profiling.Storage.ListResultsOrder)">
            <summary>
            Run the List command on the first Store from <see cref="P:StackExchange.Profiling.Storage.MultiStorageProvider.Stores"/> that returns a result with any values. 
            Will NOT return a superset of results from all <see cref="P:StackExchange.Profiling.Storage.MultiStorageProvider.Stores"/>.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Storage.MultiStorageProvider.Save(StackExchange.Profiling.MiniProfiler)">
            <summary>
            Stores <paramref name="profiler"/> under its <see cref="P:StackExchange.Profiling.MiniProfiler.Id"/> in all of the <see cref="P:StackExchange.Profiling.Storage.MultiStorageProvider.Stores"/>.
            </summary>
            <param name="profiler">The results of a profiling session.</param>
            <remarks>
            Should also ensure the profiler is stored as being un-viewed by its profiling <see cref="P:StackExchange.Profiling.MiniProfiler.User"/>.
            </remarks>
        </member>
        <member name="M:StackExchange.Profiling.Storage.MultiStorageProvider.Load(System.Guid)">
            <summary>
            Returns a <see cref="T:StackExchange.Profiling.MiniProfiler"/> from storage based on <paramref name="id"/>, 
            which should map to <see cref="P:StackExchange.Profiling.MiniProfiler.Id"/>. Will check in all of the <see cref="T:StackExchange.Profiling.Storage.IStorage"/>
            classes in <see cref="P:StackExchange.Profiling.Storage.MultiStorageProvider.Stores"/>, and will return the first <see cref="T:StackExchange.Profiling.MiniProfiler"/> that it finds.
            </summary>
            <remarks>
            Should also update that the resulting profiler has been marked as viewed by its 
            profiling <see cref="P:StackExchange.Profiling.MiniProfiler.User"/>.
            </remarks>
        </member>
        <member name="M:StackExchange.Profiling.Storage.MultiStorageProvider.SetUnviewed(System.String,System.Guid)">
            <summary>
            Sets a particular profiler session so it is considered "un-viewed".
            Will set this to all <see cref="T:StackExchange.Profiling.Storage.IStorage"/> items in <see cref="P:StackExchange.Profiling.Storage.MultiStorageProvider.Stores"/>
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Storage.MultiStorageProvider.SetViewed(System.String,System.Guid)">
            <summary>
            Sets a particular profiler session to "viewed".
            Will set this to all <see cref="T:StackExchange.Profiling.Storage.IStorage"/> items in <see cref="P:StackExchange.Profiling.Storage.MultiStorageProvider.Stores"/>
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Storage.MultiStorageProvider.GetUnviewedIds(System.String)">
            <summary>
            Runs <see cref="M:StackExchange.Profiling.Storage.IStorage.GetUnviewedIds(System.String)"/> on each <see cref="T:StackExchange.Profiling.Storage.IStorage"/> object in <see cref="P:StackExchange.Profiling.Storage.MultiStorageProvider.Stores"/> and returns the Union of results.
            Will run on multiple stores in parallel if <see cref="P:StackExchange.Profiling.Storage.MultiStorageProvider.AllowParallelOps"/> = true.
            </summary>
            <param name="user"></param>
            <returns></returns>
        </member>
        <member name="P:StackExchange.Profiling.Storage.MultiStorageProvider.Stores">
            <summary>
            The stores that are exposed by this <see cref="T:StackExchange.Profiling.Storage.MultiStorageProvider"/>
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Storage.MultiStorageProvider.AllowParallelOps">
            <summary>
            Should operations use Parallel.ForEach when it makes sense to do so (all save operations, and data retrieval where all items in <see cref="P:StackExchange.Profiling.Storage.MultiStorageProvider.Stores"/> are hit? 
            If False, all operations will run synchronously, in order. Defaults to False.
            </summary>
        </member>
        <member name="T:StackExchange.Profiling.Storage.SqlServerStorage">
            <summary>
            Understands how to store a <see cref="T:StackExchange.Profiling.MiniProfiler"/> to a MSSQL database.
            </summary>
        </member>
        <member name="F:StackExchange.Profiling.Storage.SqlServerStorage.SqlStatements">
            <summary>
            Load the SQL statements (using Dapper Multiple Results)
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Storage.SqlServerStorage.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:StackExchange.Profiling.Storage.SqlServerStorage"/> class with the specified connection string.
            </summary>
            <param name="connectionString">
            The connection string to use.
            </param>
        </member>
        <member name="M:StackExchange.Profiling.Storage.SqlServerStorage.#ctor(System.Configuration.ConnectionStringSettings)">
            <summary>
            Initializes a new instance of the <see cref="T:StackExchange.Profiling.Storage.SqlServerStorage"/> class with the specified connection string settings.
            </summary>
            <param name="connectionStringSettings">
            The connection string settings read from ConfigurationManager.ConnectionStrings["connection"]
            </param>
        </member>
        <member name="M:StackExchange.Profiling.Storage.SqlServerStorage.Save(StackExchange.Profiling.MiniProfiler)">
            <summary>
            Stores to <c>dbo.MiniProfilers</c> under its <see cref="P:StackExchange.Profiling.MiniProfiler.Id"/>;
            </summary>
            <param name="profiler">The Mini Profiler</param>
        </member>
        <member name="M:StackExchange.Profiling.Storage.SqlServerStorage.Load(System.Guid)">
            <summary>
            Loads the <c>MiniProfiler</c> identified by 'id' from the database.
            </summary>
            <param name="id">The id.</param>
            <returns>the mini profiler.</returns>
        </member>
        <member name="M:StackExchange.Profiling.Storage.SqlServerStorage.SetUnviewed(System.String,System.Guid)">
            <summary>
            Sets the session to un-viewed 
            </summary>
            <param name="user">The user.</param>
            <param name="id">The id.</param>
        </member>
        <member name="M:StackExchange.Profiling.Storage.SqlServerStorage.SetViewed(System.String,System.Guid)">
            <summary>
            sets the session to viewed
            </summary>
            <param name="user">The user.</param>
            <param name="id">The id.</param>
        </member>
        <member name="M:StackExchange.Profiling.Storage.SqlServerStorage.GetUnviewedIds(System.String)">
            <summary>
            Returns a list of <see cref="P:StackExchange.Profiling.MiniProfiler.Id"/>s that haven't been seen by <paramref name="user"/>.
            </summary>
            <param name="user">User identified by the current UserProvider"/&gt;.</param>
            <returns>the list of keys.</returns>
        </member>
        <member name="M:StackExchange.Profiling.Storage.SqlServerStorage.List(System.Int32,System.Nullable{System.DateTime},System.Nullable{System.DateTime},StackExchange.Profiling.Storage.ListResultsOrder)">
            <summary>
            List the Results.
            </summary>
            <param name="maxResults">The max results.</param>
            <param name="start">The start.</param>
            <param name="finish">The finish.</param>
            <param name="orderBy">order by.</param>
            <returns>the list of key values.</returns>
        </member>
        <member name="M:StackExchange.Profiling.Storage.SqlServerStorage.LoadProfilerRecord(System.Data.Common.DbConnection,System.Object)">
            <summary>
            Load individual MiniProfiler
            </summary>
            <param name="connection">The connection.</param>
            <param name="keyParameter">The id Parameter.</param>
            <returns>Related MiniProfiler object</returns>
        </member>
        <member name="M:StackExchange.Profiling.Storage.SqlServerStorage.PopulateChildTimings(StackExchange.Profiling.Timing,System.Linq.ILookup{System.Guid,StackExchange.Profiling.Timing})">
            <summary>
            Build the subtree of <see cref="T:StackExchange.Profiling.Timing"/> objects with <paramref name="parent"/> at the top.
            Used recursively.
            </summary>
            <param name="parent">Parent <see cref="T:StackExchange.Profiling.Timing"/> to be evaluated.</param>
            <param name="timingsLookupByParent">Key: parent timing Id; Value: collection of all <see cref="T:StackExchange.Profiling.Timing"/> objects under the given parent.</param>
        </member>
        <member name="M:StackExchange.Profiling.Storage.SqlServerStorage.GetConnection">
            <summary>
            Returns a connection to Sql Server.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Storage.SqlServerStorage.GetOpenConnection">
            <summary>
            Returns a DbConnection already opened for execution.
            </summary>
        </member>
        <member name="F:StackExchange.Profiling.Storage.SqlServerStorage.TableCreationScript">
            <summary>
            Creates needed tables. Run this once on your database.
            </summary>
            <remarks>
            Works in SQL server and <c>sqlite</c> (with documented removals).
            </remarks>
        </member>
        <member name="T:StackExchange.Profiling.Suppression">
            <summary>
            An individual suppression block that deactivates profiling temporarily
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Suppression.#ctor">
            <summary>
            Initialises a new instance of the <see cref="T:StackExchange.Profiling.Suppression"/> class. 
            Obsolete - used for serialization.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Suppression.#ctor(StackExchange.Profiling.MiniProfiler)">
            <summary>
            Initialises a new instance of the <see cref="T:StackExchange.Profiling.Suppression"/> class. 
            Creates a new Suppression to deactive profiling while alive
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Suppression.Profiler">
            <summary>
            Gets a reference to the containing profiler, allowing this Suppression to affect profiler activity.
            </summary>
        </member>
        <member name="T:StackExchange.Profiling.Data.SqlExecuteType">
            <summary>
            Categories of SQL statements.
            </summary>
        </member>
        <member name="F:StackExchange.Profiling.Data.SqlExecuteType.None">
            <summary>
            Unknown type
            </summary>
        </member>
        <member name="F:StackExchange.Profiling.Data.SqlExecuteType.NonQuery">
            <summary>
            DML statements that alter database state, e.g. INSERT, UPDATE
            </summary>
        </member>
        <member name="F:StackExchange.Profiling.Data.SqlExecuteType.Scalar">
            <summary>
            Statements that return a single record
            </summary>
        </member>
        <member name="F:StackExchange.Profiling.Data.SqlExecuteType.Reader">
            <summary>
            Statements that iterate over a result set
            </summary>
        </member>
        <member name="T:StackExchange.Profiling.Data.IDbProfiler">
            <summary>
            A call back for <c>ProfiledDbConnection</c> and family
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Data.IDbProfiler.ExecuteStart(System.Data.IDbCommand,StackExchange.Profiling.Data.SqlExecuteType)">
            <summary>
            Called when a command starts executing
            </summary>
            <param name="profiledDbCommand">
            The profiled dB Command.
            </param>
            <param name="executeType">
            The execute Type.
            </param>
        </member>
        <member name="M:StackExchange.Profiling.Data.IDbProfiler.ExecuteFinish(System.Data.IDbCommand,StackExchange.Profiling.Data.SqlExecuteType,System.Data.Common.DbDataReader)">
            <summary>
            Called when a reader finishes executing
            </summary>
            <param name="profiledDbCommand">The profiled DB Command.</param>
            <param name="executeType">The execute Type.</param>
            <param name="reader">The reader.</param>
        </member>
        <member name="M:StackExchange.Profiling.Data.IDbProfiler.ReaderFinish(System.Data.IDataReader)">
            <summary>
            Called when a reader is done iterating through the data 
            </summary>
            <param name="reader">The reader.</param>
        </member>
        <member name="M:StackExchange.Profiling.Data.IDbProfiler.OnError(System.Data.IDbCommand,StackExchange.Profiling.Data.SqlExecuteType,System.Exception)">
            <summary>
            Called when an error happens during execution of a command 
            </summary>
            <param name="profiledDbCommand">The profiled DB Command.</param>
            <param name="executeType">The execute Type.</param>
            <param name="exception">The exception.</param>
        </member>
        <member name="P:StackExchange.Profiling.Data.IDbProfiler.IsActive">
            <summary>
            Gets a value indicating whether or not the profiler instance is active
            </summary>
        </member>
        <member name="T:StackExchange.Profiling.Data.SimpleProfiledCommand">
            <summary>
            A general implementation of <see cref="T:System.Data.IDbCommand"/> that uses an <see cref="T:StackExchange.Profiling.Data.IDbProfiler"/>
            to collect profiling information.
            </summary>
        </member>
        <member name="F:StackExchange.Profiling.Data.SimpleProfiledCommand._command">
            <summary>
            The command.
            </summary>
        </member>
        <member name="F:StackExchange.Profiling.Data.SimpleProfiledCommand._connection">
            <summary>
            The connection.
            </summary>
        </member>
        <member name="F:StackExchange.Profiling.Data.SimpleProfiledCommand._profiler">
            <summary>
            The profiler.
            </summary>
        </member>
        <member name="F:StackExchange.Profiling.Data.SimpleProfiledCommand._transaction">
            <summary>
            The transaction.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Data.SimpleProfiledCommand.#ctor(System.Data.IDbCommand,System.Data.IDbConnection,StackExchange.Profiling.Data.IDbProfiler)">
            <summary>
            Initialises a new instance of the <see cref="T:StackExchange.Profiling.Data.SimpleProfiledCommand"/> class. 
            Creates a new wrapped command
            </summary>
            <param name="command">The wrapped command</param>
            <param name="connection">The wrapped connection the command is attached to</param>
            <param name="profiler">The profiler to use</param>
        </member>
        <member name="M:StackExchange.Profiling.Data.SimpleProfiledCommand.Prepare">
            <summary>
            prepare the command.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Data.SimpleProfiledCommand.Cancel">
            <summary>
            cancel the command.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Data.SimpleProfiledCommand.CreateParameter">
            <summary>
            create a new parameter.
            </summary>
            <returns>The <see cref="T:System.Data.IDbDataParameter"/>.</returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.SimpleProfiledCommand.ExecuteNonQuery">
            <summary>
            execute a non query.
            </summary>
            <returns>The <see cref="T:System.Int32"/>.</returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.SimpleProfiledCommand.ExecuteReader">
            <summary>
            execute the reader.
            </summary>
            <returns>The <see cref="T:System.Data.IDataReader"/>.</returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.SimpleProfiledCommand.ExecuteReader(System.Data.CommandBehavior)">
            <summary>
            execute the reader.
            </summary>
            <param name="behavior">The <c>behavior</c>.</param>
            <returns>the active reader.</returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.SimpleProfiledCommand.ExecuteScalar">
            <summary>
            execute and return a scalar.
            </summary>
            <returns>the scalar value.</returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.SimpleProfiledCommand.ProfileWith``1(StackExchange.Profiling.Data.SqlExecuteType,System.Func{``0})">
            <summary>
            profile with results.
            </summary>
            <param name="type">The type of execution.</param>
            <param name="func">a function to execute against against the profile result</param>
            <typeparam name="TResult">the type of result to return.</typeparam>
        </member>
        <member name="M:StackExchange.Profiling.Data.SimpleProfiledCommand.Dispose">
            <summary>
            dispose the command / connection and profiler.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Data.SimpleProfiledCommand.Dispose(System.Boolean)">
            <summary>
            dispose the command / connection and profiler.
            </summary>
            <param name="disposing">false if the dispose is called from a <c>finalizer</c></param>
        </member>
        <member name="M:StackExchange.Profiling.Data.SimpleProfiledCommand.Clone">
            <summary>
            clone the command.
            </summary>
            <returns>The <see cref="T:System.Object"/>.</returns>
        </member>
        <member name="P:StackExchange.Profiling.Data.SimpleProfiledCommand.Connection">
            <summary>
            Gets or sets the connection.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Data.SimpleProfiledCommand.Transaction">
            <summary>
            Gets or sets the transaction.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Data.SimpleProfiledCommand.CommandText">
            <summary>
            Gets or sets the command text.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Data.SimpleProfiledCommand.CommandTimeout">
            <summary>
            Gets or sets the command timeout.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Data.SimpleProfiledCommand.CommandType">
            <summary>
            Gets or sets the command type.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Data.SimpleProfiledCommand.Parameters">
            <summary>
            Gets the parameters.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Data.SimpleProfiledCommand.UpdatedRowSource">
            <summary>
            Gets or sets the updated row source.
            </summary>
        </member>
        <member name="T:StackExchange.Profiling.Data.SimpleProfiledConnection">
            <summary>
            A general implementation of <c>IDbConnection</c> that uses an <see cref="T:StackExchange.Profiling.Data.IDbProfiler"/>
            to collect profiling information.
            </summary>
        </member>
        <member name="F:StackExchange.Profiling.Data.SimpleProfiledConnection._profiler">
            <summary>
            The profiler.
            </summary>
        </member>
        <member name="F:StackExchange.Profiling.Data.SimpleProfiledConnection._connection">
            <summary>
            The connection.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Data.SimpleProfiledConnection.#ctor(System.Data.IDbConnection,StackExchange.Profiling.Data.IDbProfiler)">
            <summary>
            Initialises a new instance of the <see cref="T:StackExchange.Profiling.Data.SimpleProfiledConnection"/> class. 
            Creates a simple profiled connection instance.
            </summary>
            <param name="connection">
            The database connection to wrap
            </param>
            <param name="profiler">
            The profiler to use
            </param>
        </member>
        <member name="M:StackExchange.Profiling.Data.SimpleProfiledConnection.BeginTransaction">
            <summary>
            begin the transaction.
            </summary>
            <returns>The <see cref="T:System.Data.IDbTransaction"/>.</returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.SimpleProfiledConnection.BeginTransaction(System.Data.IsolationLevel)">
            <summary>
            begin a transaction.
            </summary>
            <param name="isolationLevel">The isolation Level.</param>
            <returns>the wrapped transaction</returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.SimpleProfiledConnection.ChangeDatabase(System.String)">
            <summary>
            change the database.
            </summary>
            <param name="databaseName">The database name.</param>
        </member>
        <member name="M:StackExchange.Profiling.Data.SimpleProfiledConnection.CreateCommand">
            <summary>
            create a new command.
            </summary>
            <returns>The <see cref="T:System.Data.IDbCommand"/>.</returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.SimpleProfiledConnection.Close">
            <summary>
            close the connection
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Data.SimpleProfiledConnection.Open">
            <summary>
            open the connection
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Data.SimpleProfiledConnection.Dispose">
            <summary>
            dispose the command / connection and profiler.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Data.SimpleProfiledConnection.Dispose(System.Boolean)">
            <summary>
            dispose the command / connection and profiler.
            </summary>
            <param name="disposing">false if the dispose is called from a <c>finalizer</c></param>
        </member>
        <member name="P:StackExchange.Profiling.Data.SimpleProfiledConnection.ConnectionString">
            <summary>
            Gets or sets the connection string.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Data.SimpleProfiledConnection.ConnectionTimeout">
            <summary>
            Gets the connection timeout.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Data.SimpleProfiledConnection.Database">
            <summary>
            Gets the database.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Data.SimpleProfiledConnection.State">
            <summary>
            Gets the state.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Data.SimpleProfiledConnection.WrappedConnection">
            <summary>
            Gets the internally wrapped <see cref="T:System.Data.IDbConnection"/>
            </summary>
        </member>
        <member name="T:StackExchange.Profiling.Data.SimpleProfiledDataReader">
            <summary>
            A simple profiled data reader.
            </summary>
        </member>
        <member name="F:StackExchange.Profiling.Data.SimpleProfiledDataReader._reader">
            <summary>
            The reader.
            </summary>
        </member>
        <member name="F:StackExchange.Profiling.Data.SimpleProfiledDataReader._profiler">
            <summary>
            The profiler.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Data.SimpleProfiledDataReader.#ctor(System.Data.IDataReader,StackExchange.Profiling.Data.IDbProfiler)">
            <summary>
            Initialises a new instance of the <see cref="T:StackExchange.Profiling.Data.SimpleProfiledDataReader"/> class.
            </summary>
            <param name="reader">The reader.</param>
            <param name="profiler">The profiler.</param>
        </member>
        <member name="M:StackExchange.Profiling.Data.SimpleProfiledDataReader.GetName(System.Int32)">
            <summary>
            get the name.
            </summary>
            <param name="index">The index.</param>
            <returns>a string containing the name</returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.SimpleProfiledDataReader.GetDataTypeName(System.Int32)">
            <summary>
            get the data type name.
            </summary>
            <param name="index">The index.</param>
            <returns>The <see cref="T:System.String"/>.</returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.SimpleProfiledDataReader.GetFieldType(System.Int32)">
            <summary>
            get the field type.
            </summary>
            <param name="index">The index.</param>
            <returns>The <see cref="T:System.Type"/>.</returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.SimpleProfiledDataReader.GetValue(System.Int32)">
            <summary>
            get the value.
            </summary>
            <param name="index">The index.</param>
            <returns>The <see cref="T:System.Object"/>.</returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.SimpleProfiledDataReader.GetValues(System.Object[])">
            <summary>
            get the values.
            </summary>
            <param name="values">The values.</param>
            <returns>The <see cref="T:System.Int32"/>.</returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.SimpleProfiledDataReader.GetOrdinal(System.String)">
            <summary>
            get the ordinal.
            </summary>
            <param name="name">The name.</param>
            <returns>The <see cref="T:System.Int32"/>.</returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.SimpleProfiledDataReader.GetBoolean(System.Int32)">
            <summary>
            The get boolean.
            </summary>
            <param name="index">
            The index.
            </param>
            <returns>
            The <see cref="T:System.Boolean"/>.
            </returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.SimpleProfiledDataReader.GetByte(System.Int32)">
            <summary>
            The get byte.
            </summary>
            <param name="index">
            The index.
            </param>
            <returns>
            The <see cref="T:System.Byte"/>.
            </returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.SimpleProfiledDataReader.GetBytes(System.Int32,System.Int64,System.Byte[],System.Int32,System.Int32)">
            <summary>
            Gets the value for the column specified by the ordinal as an array of <see cref="T:System.Byte"/> objects.
            </summary>
            <param name="index">The index.</param>
            <param name="fieldOffset">The field Offset.</param>
            <param name="buffer">The buffer.</param>
            <param name="bufferoffset">The buffer offset.</param>
            <param name="length">The length.</param>
            <returns>The number of bytes copied.</returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.SimpleProfiledDataReader.GetChar(System.Int32)">
            <summary>
            The get char.
            </summary>
            <param name="index">
            The index.
            </param>
            <returns>
            The <see cref="T:System.Char"/>.
            </returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.SimpleProfiledDataReader.GetChars(System.Int32,System.Int64,System.Char[],System.Int32,System.Int32)">
            <summary>
            Gets the value for the column specified by the ordinal as an array of <see cref="T:System.Char"/> objects.
            </summary>
            <param name="index">The index.</param>
            <param name="fieldoffset">The field offset.</param>
            <param name="buffer">The buffer.</param>
            <param name="bufferoffset">The buffer offset.</param>
            <param name="length">The length.</param>
            <returns>The <see cref="T:System.Int64"/>.</returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.SimpleProfiledDataReader.GetGuid(System.Int32)">
            <summary>
            get the GUID.
            </summary>
            <param name="index">The index.</param>
            <returns>The <see cref="T:System.Guid"/>.</returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.SimpleProfiledDataReader.GetInt16(System.Int32)">
            <summary>
            Gets the value for the column specified by the ordinal as a <see cref="T:System.Int16"/>.
            </summary>
            <param name="index">
            The index.
            </param>
            <returns>
            The <see cref="T:System.Int16"/>.
            </returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.SimpleProfiledDataReader.GetInt32(System.Int32)">
            <summary>
            Gets the value for the column specified by the ordinal as a <see cref="T:System.Int32"/>.
            </summary>
            <param name="index">
            The index.
            </param>
            <returns>
            The <see cref="T:System.Int32"/>.
            </returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.SimpleProfiledDataReader.GetInt64(System.Int32)">
            <summary>
            Gets the value for the column specified by the ordinal as a <see cref="T:System.Int64"/>.
            </summary>
            <param name="index">
            The index.
            </param>
            <returns>
            The <see cref="T:System.Int64"/>.
            </returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.SimpleProfiledDataReader.GetFloat(System.Int32)">
            <summary>
            The get float.
            </summary>
            <param name="index">
            The index.
            </param>
            <returns>
            The <see cref="T:System.Single"/>.
            </returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.SimpleProfiledDataReader.GetDouble(System.Int32)">
            <summary>
            The get double.
            </summary>
            <param name="index">
            The index.
            </param>
            <returns>
            The <see cref="T:System.Double"/>.
            </returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.SimpleProfiledDataReader.GetString(System.Int32)">
            <summary>
            The get string.
            </summary>
            <param name="index">
            The index.
            </param>
            <returns>
            The <see cref="T:System.String"/>.
            </returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.SimpleProfiledDataReader.GetDecimal(System.Int32)">
            <summary>
            The get decimal.
            </summary>
            <param name="index">
            The index.
            </param>
            <returns>
            The <see cref="T:System.Decimal"/>.
            </returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.SimpleProfiledDataReader.GetDateTime(System.Int32)">
            <summary>
            The get date time.
            </summary>
            <param name="index">
            The index.
            </param>
            <returns>
            The <see cref="T:System.DateTime"/>.
            </returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.SimpleProfiledDataReader.GetData(System.Int32)">
            <summary>
            The get data.
            </summary>
            <param name="index">
            The index.
            </param>
            <returns>
            The <see cref="T:System.Data.IDataReader"/>.
            </returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.SimpleProfiledDataReader.IsDBNull(System.Int32)">
            <summary>
            Returns true if the column specified by the column ordinal parameter is null.
            </summary>
            <param name="index">
            The index.
            </param>
            <returns>
            The <see cref="T:System.Boolean"/>.
            </returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.SimpleProfiledDataReader.Close">
            <summary>
            The close.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Data.SimpleProfiledDataReader.GetSchemaTable">
            <summary>
            The get schema table.
            </summary>
            <returns>
            The <see cref="T:System.Data.DataTable"/>.
            </returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.SimpleProfiledDataReader.NextResult">
            <summary>
            The next result.
            </summary>
            <returns>
            The <see cref="T:System.Boolean"/>.
            </returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.SimpleProfiledDataReader.Read">
            <summary>
            read from the underlying reader.
            </summary>
            <returns>
            The <see cref="T:System.Boolean"/>.</returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.SimpleProfiledDataReader.Dispose">
            <summary>
            dispose the command / connection and profiler.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Data.SimpleProfiledDataReader.Dispose(System.Boolean)">
            <summary>
            dispose the command / connection and profiler.
            </summary>
            <param name="disposing">false if the dispose is called from a <c>finalizer</c></param>
        </member>
        <member name="P:StackExchange.Profiling.Data.SimpleProfiledDataReader.FieldCount">
            <summary>
            Gets the field count.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Data.SimpleProfiledDataReader.Item(System.Int32)">
            <summary>
            The 
            </summary>
            <param name="index">
            The index.
            </param>
            <returns>
            The <see cref="T:System.Object"/>.
            </returns>
        </member>
        <member name="P:StackExchange.Profiling.Data.SimpleProfiledDataReader.Item(System.String)">
            <summary>
            The 
            </summary>
            <param name="name">
            The name.
            </param>
            <returns>
            The <see cref="T:System.Object"/>.
            </returns>
        </member>
        <member name="P:StackExchange.Profiling.Data.SimpleProfiledDataReader.Depth">
            <summary>
            Gets the depth.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Data.SimpleProfiledDataReader.IsClosed">
            <summary>
            Gets a value indicating whether is closed.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Data.SimpleProfiledDataReader.RecordsAffected">
            <summary>
            Gets the number of records affected.
            </summary>
        </member>
        <member name="T:StackExchange.Profiling.Data.ProfiledDbDataAdapter">
            <summary>
            Provides a wrapper around a native <c>DbDataAdapter</c>, allowing a profiled Fill operation.
            </summary>
        </member>
        <member name="F:StackExchange.Profiling.Data.ProfiledDbDataAdapter.TokenReader">
            <summary>
            This static variable is simply used as a non-null placeholder in the MiniProfiler.ExecuteFinish method
            </summary>
        </member>
        <member name="F:StackExchange.Profiling.Data.ProfiledDbDataAdapter._profiler">
            <summary>
            The profiler.
            </summary>
        </member>
        <member name="F:StackExchange.Profiling.Data.ProfiledDbDataAdapter._adapter">
            <summary>
            The adapter.
            </summary>
        </member>
        <member name="F:StackExchange.Profiling.Data.ProfiledDbDataAdapter._selectCommand">
            <summary>
            The select command.
            </summary>
        </member>
        <member name="F:StackExchange.Profiling.Data.ProfiledDbDataAdapter._insertCommand">
            <summary>
            The insert command.
            </summary>
        </member>
        <member name="F:StackExchange.Profiling.Data.ProfiledDbDataAdapter._updateCommand">
            <summary>
            The update command.
            </summary>
        </member>
        <member name="F:StackExchange.Profiling.Data.ProfiledDbDataAdapter._deleteCommand">
            <summary>
            The delete command.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbDataAdapter.#ctor(System.Data.IDbDataAdapter,StackExchange.Profiling.Data.IDbProfiler)">
            <summary>
            Initialises a new instance of the <see cref="T:StackExchange.Profiling.Data.ProfiledDbDataAdapter"/> class.
            </summary>
            <param name="wrappedAdapter">The wrapped adapter.</param>
            <param name="profiler">The profiler.</param>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbDataAdapter.FillSchema(System.Data.DataSet,System.Data.SchemaType)">
            <summary>
            Adds a <see cref="T:System.Data.DataTable"/> named "Table" to the specified <see cref="T:System.Data.DataSet"/> and configures the schema to match that in the data source based on the specified <see cref="T:System.Data.SchemaType"/>.
            </summary>
            <param name="dataSet">The <see cref="T:System.Data.DataSet"/> to be filled with the schema from the data source.</param>
            <param name="schemaType">One of the <see cref="T:System.Data.SchemaType"/> values.</param>
            <returns>
            An array of <see cref="T:System.Data.DataTable"/> objects that contain schema information returned from the data source.
            </returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbDataAdapter.Fill(System.Data.DataSet)">
            <summary>
            Adds or updates rows in the <see cref="T:System.Data.DataSet"/> to match those in the data source using the <see cref="T:System.Data.DataSet"/> name, and creates a <see cref="T:System.Data.DataTable"/> named "Table".
            </summary>
            <param name="dataSet">A <see cref="T:System.Data.DataSet"/> to fill with records and, if necessary, schema.</param>
            <returns>
            The number of rows successfully added to or refreshed in the <see cref="T:System.Data.DataSet"/>. This does not include rows affected by statements that do not return rows.
            </returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbDataAdapter.Fill(System.Data.DataTable)">
            <summary>
            Adds or refreshes rows in a specified range in the <see cref="T:System.Data.DataTable"/>. 
            </summary>
            <remarks>
            This function will only work if you are using an <see cref="T:IDbDataAdapter"/> implementation that inherits from <see cref="T:DbDataAdapter"/>. 
            This includes <see cref="T:SqlDataAdapter"/> and most other similar classes. 
            </remarks> 
            <returns>
            The number of rows successfully added to or refreshed in the <see cref="T:System.Data.DataSet"/>. This does not include rows affected by statements that do not return rows.
            </returns>
            <param name="dataTable">The <see cref="T:System.Data.DataTable"/> to use for table mapping.</param>
            <exception cref="T:System.InvalidOperationException">The source table is invalid or being used with an <see cref="T:IDbDataAdapter"/> implementation that does not inherit from <see cref="T:DbDataAdapter"/>.</exception>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbDataAdapter.GetFillParameters">
            <summary>
            Gets the parameters set by the user when executing an SQL SELECT statement.
            </summary>
            <returns>
            An array of <see cref="T:System.Data.IDataParameter"/> objects that contains the parameters set by the user.
            </returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbDataAdapter.Update(System.Data.DataSet)">
            <summary>
            Calls the respective INSERT, UPDATE, or DELETE statements for each inserted, updated, or deleted row in the specified <see cref="T:System.Data.DataSet"/> from a <see cref="T:System.Data.DataTable"/> named "Table".
            </summary>
            <param name="dataSet">The <see cref="T:System.Data.DataSet"/> used to update the data source.</param>
            <returns>
            The number of rows successfully updated from the <see cref="T:System.Data.DataSet"/>.
            </returns>
            <exception cref="T:System.Data.DBConcurrencyException">An attempt to execute an INSERT, UPDATE, or DELETE statement resulted in zero records affected. </exception>
        </member>
        <member name="P:StackExchange.Profiling.Data.ProfiledDbDataAdapter.InternalAdapter">
            <summary>
            Gets the underlying adapter.  Useful for when APIs can't handle the wrapped adapter (e.g. CommandBuilder).
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Data.ProfiledDbDataAdapter.MissingMappingAction">
            <summary>
            Gets or sets whether unmapped source tables or columns are passed with their source names in order to be filtered or to raise an error.
            </summary>
            <returns>One of the <see cref="T:System.Data.MissingMappingAction"/> values. The default is Passthrough.</returns>
        </member>
        <member name="P:StackExchange.Profiling.Data.ProfiledDbDataAdapter.MissingSchemaAction">
            <summary>
            Gets or sets whether missing source tables, columns, and their relationships are added to the dataset schema, ignored, or cause an error to be raised.
            </summary>
            <returns>One of the <see cref="T:System.Data.MissingSchemaAction"/> values. The default is Add.</returns>
            <exception cref="T:System.ArgumentException">The value set is not one of the <see cref="T:System.Data.MissingSchemaAction"/> values. </exception>
        </member>
        <member name="P:StackExchange.Profiling.Data.ProfiledDbDataAdapter.TableMappings">
            <summary>
            Gets how a source table is mapped to a dataset table.
            </summary>
            <returns>A collection that provides the master mapping between the returned records and the <see cref="T:System.Data.DataSet"/>. The default value is an empty collection.</returns>
        </member>
        <member name="P:StackExchange.Profiling.Data.ProfiledDbDataAdapter.SelectCommand">
            <summary>
            Gets or sets an SQL statement used to select records in the data source.
            </summary>
            <returns>An <see cref="T:System.Data.IDbCommand"/> that is used during <see cref="M:System.Data.Common.DbDataAdapter.Update(System.Data.DataSet)"/> to select records from data source for placement in the data set.</returns>
        </member>
        <member name="P:StackExchange.Profiling.Data.ProfiledDbDataAdapter.InsertCommand">
            <summary>
            Gets or sets an SQL statement used to insert new records into the data source.
            </summary>
            <returns>An <see cref="T:System.Data.IDbCommand"/> used during <see cref="M:System.Data.Common.DbDataAdapter.Update(System.Data.DataSet)"/> to insert records in the data source for new rows in the data set.</returns>
        </member>
        <member name="P:StackExchange.Profiling.Data.ProfiledDbDataAdapter.UpdateCommand">
            <summary>
            Gets or sets an SQL statement used to update records in the data source.
            </summary>
            <returns>An <see cref="T:System.Data.IDbCommand"/> used during <see cref="M:System.Data.Common.DbDataAdapter.Update(System.Data.DataSet)"/> to update records in the data source for modified rows in the data set.</returns>
        </member>
        <member name="P:StackExchange.Profiling.Data.ProfiledDbDataAdapter.DeleteCommand">
            <summary>
            Gets or sets an SQL statement for deleting records from the data set.
            </summary>
            <returns>An <see cref="T:System.Data.IDbCommand"/> used during <see cref="M:System.Data.Common.DbDataAdapter.Update(System.Data.DataSet)"/> to delete records in the data source for deleted rows in the data set.</returns>
        </member>
        <member name="T:StackExchange.Profiling.Data.ProfiledDbProviderFactory">
            <summary>
            Wrapper for a database provider factory to enable profiling
            </summary>
        </member>
        <member name="F:StackExchange.Profiling.Data.ProfiledDbProviderFactory.Instance">
            <summary>
            Every provider factory must have an Instance public field
            </summary>
        </member>
        <member name="F:StackExchange.Profiling.Data.ProfiledDbProviderFactory._tail">
            <summary>
            The tail.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbProviderFactory.#ctor(System.Data.Common.DbProviderFactory)">
            <summary>
            Initialises a new instance of the <see cref="T:StackExchange.Profiling.Data.ProfiledDbProviderFactory"/> class.
            proxy provider factory
            </summary>
            <param name="tail">The tail.</param>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbProviderFactory.#ctor">
            <summary>
            Prevents a default instance of the <see cref="T:StackExchange.Profiling.Data.ProfiledDbProviderFactory"/> class from being created.
            Used for database provider APIS internally
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbProviderFactory.CreateCommand">
            <summary>
            create the command.
            </summary>
            <returns>The <see cref="T:System.Data.Common.DbCommand"/>.</returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbProviderFactory.CreateCommandBuilder">
            <summary>
            create the command builder.
            </summary>
            <returns>
            The <see cref="T:System.Data.Common.DbCommandBuilder"/>.
            </returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbProviderFactory.CreateConnection">
            <summary>
            create the connection.
            </summary>
            <returns>The <see cref="T:System.Data.Common.DbConnection"/>.</returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbProviderFactory.CreateConnectionStringBuilder">
            <summary>
            create the connection string builder.
            </summary>
            <returns>
            The <see cref="T:System.Data.Common.DbConnectionStringBuilder"/>.
            </returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbProviderFactory.CreateDataAdapter">
            <summary>
            create the data adapter.
            </summary>
            <returns>
            The <see cref="T:System.Data.Common.DbDataAdapter"/>.
            </returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbProviderFactory.CreateDataSourceEnumerator">
            <summary>
            create the data source enumerator.
            </summary>
            <returns>The <see cref="T:System.Data.Common.DbDataSourceEnumerator"/>.</returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbProviderFactory.CreateParameter">
            <summary>
            create the parameter.
            </summary>
            <returns>The <see cref="T:System.Data.Common.DbParameter"/>.</returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbProviderFactory.CreatePermission(System.Security.Permissions.PermissionState)">
            <summary>
            create the permission.
            </summary>
            <param name="state">The state.</param>
            <returns>The <see cref="T:System.Security.CodeAccessPermission"/>.</returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbProviderFactory.InitProfiledDbProviderFactory(System.Data.Common.DbProviderFactory)">
            <summary>
            Allow to re-initialise the provider factory.
            </summary>
            <param name="tail">The tail.</param>
        </member>
        <member name="P:StackExchange.Profiling.Data.ProfiledDbProviderFactory.CanCreateDataSourceEnumerator">
            <summary>
            Gets a value indicating whether a data source enumerator can be created.
            </summary>
        </member>
        <member name="T:StackExchange.Profiling.Data.SimpleProfiledTransaction">
            <summary>
            A general implementation of <see cref="T:System.Data.IDbTransaction"/> that is used to
            wrap profiling information around calls to it.
            </summary>
        </member>
        <member name="F:StackExchange.Profiling.Data.SimpleProfiledTransaction._transaction">
            <summary>
            The transaction.
            </summary>
        </member>
        <member name="F:StackExchange.Profiling.Data.SimpleProfiledTransaction._connection">
            <summary>
            The connection.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Data.SimpleProfiledTransaction.#ctor(System.Data.IDbTransaction,StackExchange.Profiling.Data.SimpleProfiledConnection)">
            <summary>
            Creates a new wrapped <see cref="T:System.Data.IDbTransaction"/>
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Data.SimpleProfiledTransaction.Commit">
            <summary>
            commit the transaction.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Data.SimpleProfiledTransaction.Rollback">
            <summary>
            rollback the transaction
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Data.SimpleProfiledTransaction.Dispose">
            <summary>
            dispose the command / connection and profiler.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Data.SimpleProfiledTransaction.WrappedTransaction">
            <summary>
            Gets the internal wrapped transaction
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Data.SimpleProfiledTransaction.Connection">
            <summary>
            Gets the connection.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Data.SimpleProfiledTransaction.IsolationLevel">
            <summary>
            Gets the isolation level.
            </summary>
        </member>
        <member name="T:StackExchange.Profiling.Helpers.ExtensionMethods">
            <summary>
            Common extension methods to use only in this project
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.ExtensionMethods.IsNullOrWhiteSpace(System.String)">
            <summary>
            Answers true if this String is either null or empty.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.ExtensionMethods.HasValue(System.String)">
            <summary>
            Answers true if this String is neither null or empty.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.ExtensionMethods.Truncate(System.String,System.Int32)">
            <summary>
            Chops off a string at the specified length and accounts for smaller length
            </summary>
            <param name="s"></param>
            <param name="maxLength"></param>
            <returns></returns>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.ExtensionMethods.EnsureTrailingSlash(System.String)">
            <summary>
            Removes trailing / characters from a path and leaves just one
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.ExtensionMethods.RemoveLeadingSlash(System.String)">
            <summary>
            Removes any leading / characters from a path
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.ExtensionMethods.RemoveTrailingSlash(System.String)">
            <summary>
            Removes any leading / characters from a path
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.ExtensionMethods.ToJson(System.Object)">
            <summary>
            Serializes <paramref name="o"/> to a JSON string.
            </summary>
            <param name="o">the instance to serialise</param>
            <returns>the resulting JSON object as a string</returns>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.ExtensionMethods.FromJson``1(System.String)">
            <summary>
            Deserializes <paramref name="s"/> to an object of type <typeparamref name="T"/>.
            </summary>
            <param name="s">The string to deserialize</param>
            <returns>The object resulting from the given string</returns>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.ExtensionMethods.ToJs(System.Boolean)">
            <summary>
            Returns a lowercase string of <paramref name="b"/> suitable for use in javascript.
            </summary>
        </member>
        <member name="T:StackExchange.Profiling.Data.Link`2">
            <summary>
            This is a micro-cache; suitable when the number of terms is controllable (a few hundred, for example),
            and strictly append-only; you cannot change existing values. All key matches are on **REFERENCE**
            equality. The type is fully thread-safe.
            </summary>
            <typeparam name="TKey">the key type</typeparam>
            <typeparam name="TValue">the value type</typeparam>
        </member>
        <member name="M:StackExchange.Profiling.Data.Link`2.#ctor(`0,`1,StackExchange.Profiling.Data.Link{`0,`1})">
            <summary>
            Initialises a new instance of the <see cref="T:StackExchange.Profiling.Data.Link`2"/> class.
            </summary>
            <param name="key">
            The key.
            </param>
            <param name="value">
            The value.
            </param>
            <param name="tail">
            The tail.
            </param>
        </member>
        <member name="M:StackExchange.Profiling.Data.Link`2.TryGet(StackExchange.Profiling.Data.Link{`0,`1},`0,`1@)">
            <summary>
            try and return a value from the cache based on the key.
            the default value is returned if no match is found.
            An exception is not thrown.
            </summary>
            <param name="link">The link.</param>
            <param name="key">The key.</param>
            <param name="value">The value.</param>
            <returns>return true if a value is located.</returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.Link`2.TryAdd(StackExchange.Profiling.Data.Link{`0,`1}@,`0,`1@)">
            <summary>
            try and return a value from the cache based on the key.
            the default value is returned if no match is found.
            An exception is not thrown.
            </summary>
            <param name="head">The head.</param>
            <param name="key">The key.</param>
            <param name="value">The value.</param>
            <returns>return true if a value is located</returns>
        </member>
        <member name="P:StackExchange.Profiling.Data.Link`2.Key">
            <summary>
            Gets the key.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Data.Link`2.Value">
            <summary>
            Gets the value.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Data.Link`2.Tail">
            <summary>
            Gets the tail.
            </summary>
        </member>
        <member name="T:StackExchange.Profiling.Data.ProfiledDbCommand">
            <summary>
            The profiled database command.
            </summary>
        </member>
        <member name="F:StackExchange.Profiling.Data.ProfiledDbCommand.bindByNameCache">
            <summary>
            The bind by name cache.
            </summary>
        </member>
        <member name="F:StackExchange.Profiling.Data.ProfiledDbCommand._command">
            <summary>
            The command.
            </summary>
        </member>
        <member name="F:StackExchange.Profiling.Data.ProfiledDbCommand._connection">
            <summary>
            The connection.
            </summary>
        </member>
        <member name="F:StackExchange.Profiling.Data.ProfiledDbCommand._transaction">
            <summary>
            The transaction.
            </summary>
        </member>
        <member name="F:StackExchange.Profiling.Data.ProfiledDbCommand._profiler">
            <summary>
            The profiler.
            </summary>
        </member>
        <member name="F:StackExchange.Profiling.Data.ProfiledDbCommand._bindByName">
            <summary>
            bind by name.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbCommand.#ctor(System.Data.Common.DbCommand,System.Data.Common.DbConnection,StackExchange.Profiling.Data.IDbProfiler)">
            <summary>
            Initialises a new instance of the <see cref="T:StackExchange.Profiling.Data.ProfiledDbCommand"/> class.
            </summary>
            <param name="command">The command.</param>
            <param name="connection">The connection.</param>
            <param name="profiler">The profiler.</param>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbCommand.GetBindByName(System.Type)">
            <summary>
            get the binding name.
            </summary>
            <param name="commandType">The command type.</param>
            <returns>The <see cref="T:System.Action"/>.</returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbCommand.ExecuteDbDataReader(System.Data.CommandBehavior)">
            <summary>
            The execute database data reader.
            </summary>
            <param name="behavior">The behaviour.</param>
            <returns>the resulting <see cref="T:System.Data.Common.DbDataReader"/>.</returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbCommand.ExecuteNonQuery">
            <summary>
            execute a non query.
            </summary>
            <returns>the number of affected records.</returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbCommand.ExecuteScalar">
            <summary>
            execute the scalar.
            </summary>
            <returns>
            The <see cref="T:System.Object"/>.
            </returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbCommand.Cancel">
            <summary>
            cancel the command.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbCommand.Prepare">
            <summary>
            prepare the command.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbCommand.CreateDbParameter">
            <summary>
            create a database parameter.
            </summary>
            <returns>The <see cref="T:System.Data.Common.DbParameter"/>.</returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbCommand.Dispose(System.Boolean)">
            <summary>
            dispose the command.
            </summary>
            <param name="disposing">false if this is being disposed in a <c>finalizer</c>.</param>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbCommand.Clone">
            <summary>
            clone the command, entity framework expects this behaviour.
            </summary>
            <returns>The <see cref="T:StackExchange.Profiling.Data.ProfiledDbCommand"/>.</returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbCommand.System#ICloneable#Clone">
            <summary>
            The clone.
            </summary>
            <returns>
            The <see cref="T:System.Object"/>.
            </returns>
        </member>
        <member name="P:StackExchange.Profiling.Data.ProfiledDbCommand.BindByName">
            <summary>
            Gets or sets a value indicating whether or not to bind by name.
            If the underlying command supports BindByName, this sets/clears the underlying
            implementation accordingly. This is required to support OracleCommand from dapper-dot-net
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Data.ProfiledDbCommand.CommandText">
            <summary>
            Gets or sets the command text.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Data.ProfiledDbCommand.CommandTimeout">
            <summary>
            Gets or sets the command timeout.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Data.ProfiledDbCommand.CommandType">
            <summary>
            Gets or sets the command type.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Data.ProfiledDbCommand.DbConnection">
            <summary>
            Gets or sets the database connection.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Data.ProfiledDbCommand.DbParameterCollection">
            <summary>
            Gets the database parameter collection.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Data.ProfiledDbCommand.DbTransaction">
            <summary>
            Gets or sets the database transaction.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Data.ProfiledDbCommand.DesignTimeVisible">
            <summary>
            Gets or sets a value indicating whether the command is design time visible.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Data.ProfiledDbCommand.UpdatedRowSource">
            <summary>
            Gets or sets the updated row source.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Data.ProfiledDbCommand.InternalCommand">
            <summary>
            Gets the internal command.
            </summary>
        </member>
        <member name="T:StackExchange.Profiling.Data.ProfiledDbConnection">
            <summary>
            Wraps a database connection, allowing SQL execution timings to be collected when a <see cref="T:StackExchange.Profiling.MiniProfiler"/> session is started.
            </summary>
        </member>
        <member name="F:StackExchange.Profiling.Data.ProfiledDbConnection._connection">
            <summary>
            This will be made private; use <see cref="P:StackExchange.Profiling.Data.ProfiledDbConnection.InnerConnection"/>
            </summary>
        </member>
        <member name="F:StackExchange.Profiling.Data.ProfiledDbConnection._profiler">
            <summary>
            This will be made private; use <see cref="P:StackExchange.Profiling.Data.ProfiledDbConnection.Profiler"/>
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbConnection.#ctor(System.Data.Common.DbConnection,StackExchange.Profiling.Data.IDbProfiler)">
            <summary>
            Initialises a new instance of the <see cref="T:StackExchange.Profiling.Data.ProfiledDbConnection"/> class. 
            Returns a new <see cref="T:StackExchange.Profiling.Data.ProfiledDbConnection"/> that wraps <paramref name="connection"/>, 
            providing query execution profiling. If profiler is null, no profiling will occur.
            </summary>
            <param name="connection">
            <c>Your provider-specific flavour of connection, e.g. SqlConnection, OracleConnection</c>
            </param>
            <param name="profiler">
            The currently started <see cref="T:StackExchange.Profiling.MiniProfiler"/> or null.
            </param>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbConnection.ChangeDatabase(System.String)">
            <summary>
            change the database.
            </summary>
            <param name="databaseName">The new database name.</param>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbConnection.Close">
            <summary>
            close the connection.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbConnection.EnlistTransaction(System.Transactions.Transaction)">
            <summary>
            enlist the transaction.
            </summary>
            <param name="transaction">The transaction.</param>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbConnection.GetSchema">
            <summary>
            get the schema.
            </summary>
            <returns>The <see cref="T:System.Data.DataTable"/>.</returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbConnection.GetSchema(System.String)">
            <summary>
            get the schema.
            </summary>
            <param name="collectionName">The collection name.</param>
            <returns>The <see cref="T:System.Data.DataTable"/>.</returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbConnection.GetSchema(System.String,System.String[])">
            <summary>
            get the schema.
            </summary>
            <param name="collectionName">The collection name.</param>
            <param name="restrictionValues">The restriction values.</param>
            <returns>The <see cref="T:System.Data.DataTable"/>.</returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbConnection.Open">
            <summary>
            open the connection
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbConnection.BeginDbTransaction(System.Data.IsolationLevel)">
            <summary>
            begin the database transaction.
            </summary>
            <param name="isolationLevel">The isolation level.</param>
            <returns>The <see cref="T:System.Data.Common.DbTransaction"/>.</returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbConnection.CreateDbCommand">
            <summary>
            create the database command.
            </summary>
            <returns>The <see cref="T:System.Data.Common.DbCommand"/>.</returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbConnection.Dispose(System.Boolean)">
            <summary>
            dispose the underlying connection.
            </summary>
            <param name="disposing">false if pre-empted from a <c>finalizer</c></param>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbConnection.StateChangeHandler(System.Object,System.Data.StateChangeEventArgs)">
            <summary>
            The state change handler.
            </summary>
            <param name="sender">The sender.</param>
            <param name="stateChangeEventArguments">The state change event arguments.</param>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbConnection.Clone">
            <summary>
            create a clone.
            </summary>
            <returns>The <see cref="T:StackExchange.Profiling.Data.ProfiledDbConnection"/>.</returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbConnection.System#ICloneable#Clone">
            <summary>
            create a clone.
            </summary>
            <returns>The <see cref="T:System.Object"/>.</returns>
        </member>
        <member name="P:StackExchange.Profiling.Data.ProfiledDbConnection.InnerConnection">
            <summary>
            Gets the underlying, real database connection to your database provider.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Data.ProfiledDbConnection.Profiler">
            <summary>
            Gets the current profiler instance; could be null.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Data.ProfiledDbConnection.WrappedConnection">
            <summary>
            Gets the wrapped connection.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Data.ProfiledDbConnection.CanRaiseEvents">
            <summary>
            Gets a value indicating whether events can be raised.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Data.ProfiledDbConnection.ConnectionString">
            <summary>
            Gets or sets the connection string.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Data.ProfiledDbConnection.ConnectionTimeout">
            <summary>
            Gets the connection timeout.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Data.ProfiledDbConnection.Database">
            <summary>
            Gets the database.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Data.ProfiledDbConnection.DataSource">
            <summary>
            Gets the data source.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Data.ProfiledDbConnection.ServerVersion">
            <summary>
            Gets the server version.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Data.ProfiledDbConnection.State">
            <summary>
            Gets the state.
            </summary>
        </member>
        <member name="T:StackExchange.Profiling.Data.ProfiledDbDataReader">
            <summary>
            The profiled database data reader.
            </summary>
        </member>
        <member name="F:StackExchange.Profiling.Data.ProfiledDbDataReader._reader">
            <summary>
            The _reader.
            </summary>
        </member>
        <member name="F:StackExchange.Profiling.Data.ProfiledDbDataReader._profiler">
            <summary>
            The _profiler.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbDataReader.#ctor(System.Data.Common.DbDataReader,System.Data.Common.DbConnection,StackExchange.Profiling.Data.IDbProfiler)">
            <summary>
            Initialises a new instance of the <see cref="T:StackExchange.Profiling.Data.ProfiledDbDataReader"/> class.
            </summary>
            <param name="reader">The reader.</param>
            <param name="connection">The connection.</param>
            <param name="profiler">The profiler.</param>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbDataReader.Close">
            <summary>
            The close.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbDataReader.GetBoolean(System.Int32)">
            <summary>
            The get boolean.
            </summary>
            <param name="ordinal">
            The ordinal.
            </param>
            <returns>
            The <see cref="T:System.Boolean"/>.
            </returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbDataReader.GetByte(System.Int32)">
            <summary>
            The get byte.
            </summary>
            <param name="ordinal">
            The ordinal.
            </param>
            <returns>
            The <see cref="T:System.Byte"/>.
            </returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbDataReader.GetBytes(System.Int32,System.Int64,System.Byte[],System.Int32,System.Int32)">
            <summary>
            The get bytes.
            </summary>
            <param name="ordinal">
            The ordinal.
            </param>
            <param name="dataOffset">
            The data offset.
            </param>
            <param name="buffer">
            The buffer.
            </param>
            <param name="bufferOffset">
            The buffer offset.
            </param>
            <param name="length">
            The length.
            </param>
            <returns>
            The <see cref="T:System.Int64"/>.
            </returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbDataReader.GetChar(System.Int32)">
            <summary>
            The get char.
            </summary>
            <param name="ordinal">
            The ordinal.
            </param>
            <returns>
            The <see cref="T:System.Char"/>.
            </returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbDataReader.GetChars(System.Int32,System.Int64,System.Char[],System.Int32,System.Int32)">
            <summary>
            The get chars.
            </summary>
            <param name="ordinal">
            The ordinal.
            </param>
            <param name="dataOffset">
            The data offset.
            </param>
            <param name="buffer">
            The buffer.
            </param>
            <param name="bufferOffset">
            The buffer offset.
            </param>
            <param name="length">
            The length.
            </param>
            <returns>
            The <see cref="T:System.Int64"/>.
            </returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbDataReader.GetDataTypeName(System.Int32)">
            <summary>
            The get data type name.
            </summary>
            <param name="ordinal">
            The ordinal.
            </param>
            <returns>
            The <see cref="T:System.String"/>.
            </returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbDataReader.GetDateTime(System.Int32)">
            <summary>
            The get date time.
            </summary>
            <param name="ordinal">
            The ordinal.
            </param>
            <returns>
            The <see cref="T:System.DateTime"/>.
            </returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbDataReader.GetDecimal(System.Int32)">
            <summary>
            The get decimal.
            </summary>
            <param name="ordinal">
            The ordinal.
            </param>
            <returns>
            The <see cref="T:System.Decimal"/>.
            </returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbDataReader.GetDouble(System.Int32)">
            <summary>
            The get double.
            </summary>
            <param name="ordinal">
            The ordinal.
            </param>
            <returns>
            The <see cref="T:System.Double"/>.
            </returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbDataReader.GetEnumerator">
            <summary>
            The get enumerator.
            </summary>
            <returns>
            The <see cref="T:System.Collections.Generic.IEnumerator`1"/>.
            </returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbDataReader.GetFieldType(System.Int32)">
            <summary>
            The get field type.
            </summary>
            <param name="ordinal">
            The ordinal.
            </param>
            <returns>
            The <see cref="T:System.Type"/>.
            </returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbDataReader.GetFloat(System.Int32)">
            <summary>
            The get float.
            </summary>
            <param name="ordinal">
            The ordinal.
            </param>
            <returns>
            The <see cref="T:System.Single"/>.
            </returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbDataReader.GetGuid(System.Int32)">
            <summary>
            get the GUID.
            </summary>
            <param name="ordinal">
            The ordinal.
            </param>
            <returns>
            The <see cref="T:System.Guid"/>.
            </returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbDataReader.GetInt16(System.Int32)">
            <summary>
            The get integer.
            </summary>
            <param name="ordinal">The ordinal.</param>
            <returns>The <see cref="T:System.Int16"/>.</returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbDataReader.GetInt32(System.Int32)">
            <summary>
            get a 32 bit integer
            </summary>
            <param name="ordinal">The ordinal.</param>
            <returns>
            The <see cref="T:System.Int32"/>.
            </returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbDataReader.GetInt64(System.Int32)">
            <summary>
            get a 64 bit integer (long)
            </summary>
            <param name="ordinal">
            The ordinal.
            </param>
            <returns>
            The <see cref="T:System.Int64"/>.
            </returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbDataReader.GetName(System.Int32)">
            <summary>
            The get name.
            </summary>
            <param name="ordinal">
            The ordinal.
            </param>
            <returns>
            The <see cref="T:System.String"/>.
            </returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbDataReader.GetOrdinal(System.String)">
            <summary>
            The get ordinal.
            </summary>
            <param name="name">
            The name.
            </param>
            <returns>
            The <see cref="T:System.Int32"/>.
            </returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbDataReader.GetSchemaTable">
            <summary>
            The get schema table.
            </summary>
            <returns>
            The <see cref="T:System.Data.DataTable"/>.
            </returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbDataReader.GetString(System.Int32)">
            <summary>
            The get string.
            </summary>
            <param name="ordinal">
            The ordinal.
            </param>
            <returns>
            The <see cref="T:System.String"/>.
            </returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbDataReader.GetValue(System.Int32)">
            <summary>
            The get value.
            </summary>
            <param name="ordinal">
            The ordinal.
            </param>
            <returns>
            The <see cref="T:System.Object"/>.
            </returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbDataReader.GetValues(System.Object[])">
            <summary>
            The get values.
            </summary>
            <param name="values">
            The values.
            </param>
            <returns>
            The <see cref="T:System.Int32"/>.
            </returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbDataReader.IsDBNull(System.Int32)">
            <summary>
            the database value null.
            </summary>
            <param name="ordinal">
            The ordinal.
            </param>
            <returns>
            The <see cref="T:System.Boolean"/>.
            </returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbDataReader.NextResult">
            <summary>
            The next result.
            </summary>
            <returns>
            The <see cref="T:System.Boolean"/>.
            </returns>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbDataReader.Read">
            <summary>
            The read.
            </summary>
            <returns>
            The <see cref="T:System.Boolean"/>.
            </returns>
        </member>
        <member name="P:StackExchange.Profiling.Data.ProfiledDbDataReader.Depth">
            <summary>
            Gets the depth.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Data.ProfiledDbDataReader.FieldCount">
            <summary>
            Gets the field count.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Data.ProfiledDbDataReader.HasRows">
            <summary>
            Gets a value indicating whether has rows.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Data.ProfiledDbDataReader.IsClosed">
            <summary>
            Gets a value indicating whether is closed.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Data.ProfiledDbDataReader.RecordsAffected">
            <summary>
            Gets the records affected.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Data.ProfiledDbDataReader.WrappedReader">
            <summary>
            The <see cref="T:System.Data.Common.DbDataReader"/> that is being used
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Data.ProfiledDbDataReader.Item(System.String)">
            <summary>
            The 
            </summary>
            <param name="name">
            The name.
            </param>
            <returns>
            The <see cref="T:System.Object"/>.
            </returns>
        </member>
        <member name="P:StackExchange.Profiling.Data.ProfiledDbDataReader.Item(System.Int32)">
            <summary>
            The 
            </summary>
            <param name="ordinal">
            The ordinal.
            </param>
            <returns>
            The <see cref="T:System.Object"/>.
            </returns>
        </member>
        <member name="T:StackExchange.Profiling.Data.ProfiledDbTransaction">
            <summary>
            The profiled database transaction.
            </summary>
        </member>
        <member name="F:StackExchange.Profiling.Data.ProfiledDbTransaction._connection">
            <summary>
            The connection.
            </summary>
        </member>
        <member name="F:StackExchange.Profiling.Data.ProfiledDbTransaction._transaction">
            <summary>
            The transaction.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbTransaction.#ctor(System.Data.Common.DbTransaction,StackExchange.Profiling.Data.ProfiledDbConnection)">
            <summary>
            Initialises a new instance of the <see cref="T:StackExchange.Profiling.Data.ProfiledDbTransaction"/> class.
            </summary>
            <param name="transaction">The transaction.</param>
            <param name="connection">The connection.</param>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbTransaction.Commit">
            <summary>
            commit the transaction.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbTransaction.Rollback">
            <summary>
            rollback the transaction
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Data.ProfiledDbTransaction.Dispose(System.Boolean)">
            <summary>
            dispose the transaction and connection.
            </summary>
            <param name="disposing">false if being called from a <c>finalizer</c></param>
        </member>
        <member name="P:StackExchange.Profiling.Data.ProfiledDbTransaction.DbConnection">
            <summary>
            Gets the database connection.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Data.ProfiledDbTransaction.WrappedTransaction">
            <summary>
            Gets the wrapped transaction.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Data.ProfiledDbTransaction.IsolationLevel">
            <summary>
            Gets the isolation level.
            </summary>
        </member>
        <member name="T:StackExchange.Profiling.Helpers.IStopwatch">
            <summary>
            The Stopwatch interface.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.IStopwatch.Stop">
            <summary>
            stop the timer.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Helpers.IStopwatch.ElapsedTicks">
            <summary>
            Gets the elapsed ticks.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Helpers.IStopwatch.Frequency">
            <summary>
            Gets the frequency.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Helpers.IStopwatch.IsRunning">
            <summary>
            Gets a value indicating whether is running.
            </summary>
        </member>
        <member name="T:StackExchange.Profiling.Helpers.StopwatchWrapper">
            <summary>
            The stopwatch wrapper.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.StopwatchWrapper.StartNew">
            <summary>
            start a new timer.
            </summary>
            <returns>
            The <see cref="T:StackExchange.Profiling.Helpers.IStopwatch"/>.
            </returns>
        </member>
        <member name="F:StackExchange.Profiling.Helpers.StopwatchWrapper._stopwatch">
            <summary>
            The _stopwatch.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.StopwatchWrapper.#ctor">
            <summary>
            Prevents a default instance of the <see cref="T:StackExchange.Profiling.Helpers.StopwatchWrapper"/> class from being created.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.StopwatchWrapper.Stop">
            <summary>
            stop the timer.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Helpers.StopwatchWrapper.ElapsedTicks">
            <summary>
            Gets the elapsed ticks.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Helpers.StopwatchWrapper.Frequency">
            <summary>
            Gets the frequency.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Helpers.StopwatchWrapper.IsRunning">
            <summary>
            Gets a value indicating whether is running.
            </summary>
        </member>
        <member name="T:StackExchange.Profiling.Helpers.Dapper.SqlBuilder">
            <summary>
            Help to build out sql
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.Dapper.SqlBuilder.AddTemplate(System.String,System.Object)">
            <summary>
            Add a template to the SqlBuilder
            </summary>
            <param name="sql"></param>
            <param name="parameters"></param>
            <returns></returns>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.Dapper.SqlBuilder.LeftJoin(System.String,System.Object)">
            <summary>
            Add a Left Join
            </summary>
            <returns>itself</returns>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.Dapper.SqlBuilder.Where(System.String,System.Object)">
            <summary>
            Add a Where Clause
            </summary>
            <returns>itself</returns>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.Dapper.SqlBuilder.OrderBy(System.String,System.Object)">
            <summary>
            Add an OrderBy Clause
            </summary>
            <returns>itself</returns>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.Dapper.SqlBuilder.Select(System.String,System.Object)">
            <summary>
            Add the Select section
            </summary>
            <returns>itself</returns>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.Dapper.SqlBuilder.AddParameters(System.Object)">
            <summary>
            Add a parameters to the query
            </summary>
            <returns>itself</returns>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.Dapper.SqlBuilder.Join(System.String,System.Object)">
            <summary>
            Add a Join statement
            </summary>
            <returns>itself</returns>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.Dapper.SqlBuilder.GroupBy(System.String,System.Object)">
            <summary>
            Add a Group By section
            </summary>
            <returns>itself</returns>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.Dapper.SqlBuilder.Having(System.String,System.Object)">
            <summary>
            Add a Having section
            </summary>
            <returns>itself</returns>
        </member>
        <member name="T:StackExchange.Profiling.Helpers.Dapper.SqlBuilder.Template">
            <summary>
            Template helper class for SqlBuilder
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.Dapper.SqlBuilder.Template.#ctor(StackExchange.Profiling.Helpers.Dapper.SqlBuilder,System.String,System.Object)">
            <summary>
            Template constructor
            </summary>
            <param name="builder"></param>
            <param name="sql"></param>
            <param name="parameters"></param>
        </member>
        <member name="P:StackExchange.Profiling.Helpers.Dapper.SqlBuilder.Template.RawSql">
            <summary>
            Raw Sql returns by the <see cref="T:StackExchange.Profiling.Helpers.Dapper.SqlBuilder"/>
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Helpers.Dapper.SqlBuilder.Template.Parameters">
            <summary>
            Parameters being used
            </summary>
        </member>
        <member name="T:StackExchange.Profiling.Helpers.Dapper.SqlMapper">
            <summary>
            Dapper, a light weight object mapper for ADO.NET
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.Dapper.SqlMapper.PurgeQueryCache">
            <summary>
            Purge the query cache 
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.Dapper.SqlMapper.GetCachedSQLCount">
            <summary>
            Return a count of all the cached queries by dapper
            </summary>
            <returns></returns>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.Dapper.SqlMapper.GetCachedSQL(System.Int32)">
            <summary>
            Return a list of all the queries cached by dapper
            </summary>
            <param name="ignoreHitCountAbove"></param>
            <returns></returns>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.Dapper.SqlMapper.GetHashCollissions">
            <summary>
            Deep diagnostics only: find any hash collisions in the cache
            </summary>
            <returns></returns>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.Dapper.SqlMapper.Execute(System.Data.IDbConnection,System.String,System.Object,System.Data.IDbTransaction,System.Nullable{System.Int32},System.Nullable{System.Data.CommandType})">
            <summary>
            Execute parameterized SQL  
            </summary>
            <returns>Number of rows affected</returns>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.Dapper.SqlMapper.Query(System.Data.IDbConnection,System.String,System.Object,System.Data.IDbTransaction,System.Boolean,System.Nullable{System.Int32},System.Nullable{System.Data.CommandType})">
            <summary>
            Return a list of dynamic objects, reader is closed after the call
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.Dapper.SqlMapper.Query``1(System.Data.IDbConnection,System.String,System.Object,System.Data.IDbTransaction,System.Boolean,System.Nullable{System.Int32},System.Nullable{System.Data.CommandType})">
            <summary>
            Executes a query, returning the data typed as per T
            </summary>
            <remarks>the dynamic param may seem a bit odd, but this works around a major usability issue in vs, if it is Object vs completion gets annoying. Eg type new [space] get new object</remarks>
            <returns>A sequence of data of the supplied type; if a basic type (int, string, etc) is queried then the data from the first column in assumed, otherwise an instance is
            created per row, and a direct column-name===member-name mapping is assumed (case insensitive).
            </returns>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.Dapper.SqlMapper.QueryMultiple(System.Data.IDbConnection,System.String,System.Object,System.Data.IDbTransaction,System.Nullable{System.Int32},System.Nullable{System.Data.CommandType})">
            <summary>
            Execute a command that returns multiple result sets, and access each in turn
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.Dapper.SqlMapper.QueryInternal``1(System.Data.IDbConnection,System.String,System.Object,System.Data.IDbTransaction,System.Nullable{System.Int32},System.Nullable{System.Data.CommandType})">
            <summary>
            Return a typed list of objects, reader is closed after the call
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.Dapper.SqlMapper.Query``3(System.Data.IDbConnection,System.String,System.Func{``0,``1,``2},System.Object,System.Data.IDbTransaction,System.Boolean,System.String,System.Nullable{System.Int32},System.Nullable{System.Data.CommandType})">
            <summary>
            Maps a query to objects
            </summary>
            <typeparam name="TFirst">The first type in the recordset</typeparam>
            <typeparam name="TSecond">The second type in the recordset</typeparam>
            <typeparam name="TReturn">The return type</typeparam>
            <param name="cnn"></param>
            <param name="sql"></param>
            <param name="map"></param>
            <param name="param"></param>
            <param name="transaction"></param>
            <param name="buffered"></param>
            <param name="splitOn">The Field we should split and read the second object from (default: id)</param>
            <param name="commandTimeout">Number of seconds before command execution timeout</param>
            <param name="commandType">Is it a stored proc or a batch?</param>
            <returns></returns>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.Dapper.SqlMapper.Query``4(System.Data.IDbConnection,System.String,System.Func{``0,``1,``2,``3},System.Object,System.Data.IDbTransaction,System.Boolean,System.String,System.Nullable{System.Int32},System.Nullable{System.Data.CommandType})">
            <summary>
            Maps a query to objects
            </summary>
            <typeparam name="TFirst"></typeparam>
            <typeparam name="TSecond"></typeparam>
            <typeparam name="TThird"></typeparam>
            <typeparam name="TReturn"></typeparam>
            <param name="cnn"></param>
            <param name="sql"></param>
            <param name="map"></param>
            <param name="param"></param>
            <param name="transaction"></param>
            <param name="buffered"></param>
            <param name="splitOn">The Field we should split and read the second object from (default: id)</param>
            <param name="commandTimeout">Number of seconds before command execution timeout</param>
            <param name="commandType"></param>
            <returns></returns>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.Dapper.SqlMapper.Query``5(System.Data.IDbConnection,System.String,System.Func{``0,``1,``2,``3,``4},System.Object,System.Data.IDbTransaction,System.Boolean,System.String,System.Nullable{System.Int32},System.Nullable{System.Data.CommandType})">
            <summary>
            Perform a multi mapping query with 4 input parameters
            </summary>
            <typeparam name="TFirst"></typeparam>
            <typeparam name="TSecond"></typeparam>
            <typeparam name="TThird"></typeparam>
            <typeparam name="TFourth"></typeparam>
            <typeparam name="TReturn"></typeparam>
            <param name="cnn"></param>
            <param name="sql"></param>
            <param name="map"></param>
            <param name="param"></param>
            <param name="transaction"></param>
            <param name="buffered"></param>
            <param name="splitOn"></param>
            <param name="commandTimeout"></param>
            <param name="commandType"></param>
            <returns></returns>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.Dapper.SqlMapper.Query``6(System.Data.IDbConnection,System.String,System.Func{``0,``1,``2,``3,``4,``5},System.Object,System.Data.IDbTransaction,System.Boolean,System.String,System.Nullable{System.Int32},System.Nullable{System.Data.CommandType})">
            <summary>
            Perform a multi mapping query with 5 input parameters
            </summary>
            <typeparam name="TFirst"></typeparam>
            <typeparam name="TSecond"></typeparam>
            <typeparam name="TThird"></typeparam>
            <typeparam name="TFourth"></typeparam>
            <typeparam name="TFifth"></typeparam>
            <typeparam name="TReturn"></typeparam>
            <param name="cnn"></param>
            <param name="sql"></param>
            <param name="map"></param>
            <param name="param"></param>
            <param name="transaction"></param>
            <param name="buffered"></param>
            <param name="splitOn"></param>
            <param name="commandTimeout"></param>
            <param name="commandType"></param>
            <returns></returns>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.Dapper.SqlMapper.ReadChar(System.Object)">
            <summary>
            Internal use only
            </summary>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.Dapper.SqlMapper.ReadNullableChar(System.Object)">
            <summary>
            Internal use only
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.Dapper.SqlMapper.PackListParameters(System.Data.IDbCommand,System.String,System.Object)">
            <summary>
            Internal use only
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.Dapper.SqlMapper.CreateParamInfoGenerator(StackExchange.Profiling.Helpers.Dapper.SqlMapper.Identity)">
            <summary>
            Internal use only
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.Dapper.SqlMapper.GetClassDeserializer(System.Type,System.Data.IDataReader,System.Int32,System.Int32,System.Boolean)">
            <summary>
            Internal use only
            </summary>
            <param name="type"></param>
            <param name="reader"></param>
            <param name="startBound"></param>
            <param name="length"></param>
            <param name="returnNullIfFirstMissing"></param>
            <returns></returns>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.Dapper.SqlMapper.ThrowDataException(System.Exception,System.Int32,System.Data.IDataReader)">
            <summary>
            Throws a data exception, only used internally
            </summary>
            <param name="ex"></param>
            <param name="index"></param>
            <param name="reader"></param>
        </member>
        <member name="E:StackExchange.Profiling.Helpers.Dapper.SqlMapper.QueryCachePurged">
            <summary>
            Called if the query cache is purged via PurgeQueryCache
            </summary>
        </member>
        <member name="T:StackExchange.Profiling.Helpers.Dapper.SqlMapper.IDynamicParameters">
            <summary>
            Implement this interface to pass an arbitrary db specific set of parameters to Dapper
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.Dapper.SqlMapper.IDynamicParameters.AddParameters(System.Data.IDbCommand,StackExchange.Profiling.Helpers.Dapper.SqlMapper.Identity)">
            <summary>
            Add all the parameters needed to the command just before it executes
            </summary>
            <param name="command">The raw command prior to execution</param>
            <param name="identity">Information about the query</param>
        </member>
        <member name="T:StackExchange.Profiling.Helpers.Dapper.SqlMapper.Link`2">
            <summary>
            This is a micro-cache; suitable when the number of terms is controllable (a few hundred, for example),
            and strictly append-only; you cannot change existing values. All key matches are on **REFERENCE**
            equality. The type is fully thread-safe.
            </summary>
        </member>
        <member name="T:StackExchange.Profiling.Helpers.Dapper.SqlMapper.Identity">
            <summary>
            Identity of a cached query in Dapper, used for extensability
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.Dapper.SqlMapper.Identity.ForDynamicParameters(System.Type)">
            <summary>
            Create an identity for use with DynamicParameters, internal use only
            </summary>
            <param name="type"></param>
            <returns></returns>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.Dapper.SqlMapper.Identity.Equals(System.Object)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="F:StackExchange.Profiling.Helpers.Dapper.SqlMapper.Identity.sql">
            <summary>
            The sql
            </summary>
        </member>
        <member name="F:StackExchange.Profiling.Helpers.Dapper.SqlMapper.Identity.commandType">
            <summary>
            The command type 
            </summary>
        </member>
        <member name="F:StackExchange.Profiling.Helpers.Dapper.SqlMapper.Identity.hashCode">
            <summary>
            
            </summary>
        </member>
        <member name="F:StackExchange.Profiling.Helpers.Dapper.SqlMapper.Identity.gridIndex">
            <summary>
            
            </summary>
        </member>
        <member name="F:StackExchange.Profiling.Helpers.Dapper.SqlMapper.Identity.connectionString">
            <summary>
            
            </summary>
        </member>
        <member name="F:StackExchange.Profiling.Helpers.Dapper.SqlMapper.Identity.parametersType">
            <summary>
            
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.Dapper.SqlMapper.Identity.GetHashCode">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.Dapper.SqlMapper.Identity.Equals(StackExchange.Profiling.Helpers.Dapper.SqlMapper.Identity)">
            <summary>
            Compare 2 Identity objects
            </summary>
            <param name="other"></param>
            <returns></returns>
        </member>
        <member name="T:StackExchange.Profiling.Helpers.Dapper.SqlMapper.GridReader">
            <summary>
            The grid reader provides interfaces for reading multiple result sets from a Dapper query 
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.Dapper.SqlMapper.GridReader.Read``1">
            <summary>
            Read the next grid of results
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.Dapper.SqlMapper.GridReader.Read``3(System.Func{``0,``1,``2},System.String)">
            <summary>
            Read multiple objects from a single recordset on the grid
            </summary>
            <typeparam name="TFirst"></typeparam>
            <typeparam name="TSecond"></typeparam>
            <typeparam name="TReturn"></typeparam>
            <param name="func"></param>
            <param name="splitOn"></param>
            <returns></returns>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.Dapper.SqlMapper.GridReader.Read``4(System.Func{``0,``1,``2,``3},System.String)">
            <summary>
            Read multiple objects from a single recordset on the grid
            </summary>
            <typeparam name="TFirst"></typeparam>
            <typeparam name="TSecond"></typeparam>
            <typeparam name="TThird"></typeparam>
            <typeparam name="TReturn"></typeparam>
            <param name="func"></param>
            <param name="splitOn"></param>
            <returns></returns>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.Dapper.SqlMapper.GridReader.Read``5(System.Func{``0,``1,``2,``3,``4},System.String)">
            <summary>
            Read multiple objects from a single record set on the grid
            </summary>
            <typeparam name="TFirst"></typeparam>
            <typeparam name="TSecond"></typeparam>
            <typeparam name="TThird"></typeparam>
            <typeparam name="TFourth"></typeparam>
            <typeparam name="TReturn"></typeparam>
            <param name="func"></param>
            <param name="splitOn"></param>
            <returns></returns>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.Dapper.SqlMapper.GridReader.Read``6(System.Func{``0,``1,``2,``3,``4,``5},System.String)">
            <summary>
            Read multiple objects from a single record set on the grid
            </summary>
            <typeparam name="TFirst"></typeparam>
            <typeparam name="TSecond"></typeparam>
            <typeparam name="TThird"></typeparam>
            <typeparam name="TFourth"></typeparam>
            <typeparam name="TFifth"></typeparam>
            <typeparam name="TReturn"></typeparam>
            <param name="func"></param>
            <param name="splitOn"></param>
            <returns></returns>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.Dapper.SqlMapper.GridReader.Dispose">
            <summary>
            Dispose the grid, closing and disposing both the underlying reader and command.
            </summary>
        </member>
        <member name="T:StackExchange.Profiling.Helpers.Dapper.DynamicParameters">
            <summary>
            A bag of parameters that can be passed to the Dapper Query and Execute methods
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.Dapper.DynamicParameters.#ctor">
            <summary>
            construct a dynamic parameter bag
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.Dapper.DynamicParameters.#ctor(System.Object)">
            <summary>
            construct a dynamic parameter bag
            </summary>
            <param name="template">can be an anonymous type of a DynamicParameters bag</param>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.Dapper.DynamicParameters.AddDynamicParams(System.Object)">
            <summary>
            Append a whole object full of params to the dynamic
            EG: AddParams(new {A = 1, B = 2}) // will add property A and B to the dynamic
            </summary>
            <param name="param"></param>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.Dapper.DynamicParameters.Add(System.String,System.Object,System.Nullable{System.Data.DbType},System.Nullable{System.Data.ParameterDirection},System.Nullable{System.Int32})">
            <summary>
            Add a parameter to this dynamic parameter list
            </summary>
            <param name="name"></param>
            <param name="value"></param>
            <param name="dbType"></param>
            <param name="direction"></param>
            <param name="size"></param>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.Dapper.DynamicParameters.Get``1(System.String)">
            <summary>
            Get the value of a parameter
            </summary>
            <typeparam name="T"></typeparam>
            <param name="name"></param>
            <returns>The value, note DBNull.Value is not returned, instead the value is returned as null</returns>
        </member>
        <member name="T:StackExchange.Profiling.Helpers.Dapper.DbString">
            <summary>
            This class represents a SQL string, it can be used if you need to denote your parameter is a Char vs VarChar vs nVarChar vs nChar
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.Dapper.DbString.#ctor">
            <summary>
            Create a new DbString
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.Dapper.DbString.AddParameter(System.Data.IDbCommand,System.String)">
            <summary>
            Add the parameter to the command... internal use only
            </summary>
            <param name="command"></param>
            <param name="name"></param>
        </member>
        <member name="P:StackExchange.Profiling.Helpers.Dapper.DbString.IsAnsi">
            <summary>
            Ansi vs Unicode 
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Helpers.Dapper.DbString.IsFixedLength">
            <summary>
            Fixed length 
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Helpers.Dapper.DbString.Length">
            <summary>
            Length of the string -1 for max
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Helpers.Dapper.DbString.Value">
            <summary>
            The value of the string
            </summary>
        </member>
        <member name="T:StackExchange.Profiling.Helpers.StackTraceSnippet">
            <summary>
                Gets part of a stack trace containing only methods we care about.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.StackTraceSnippet.Get">
            <summary>
                Gets the current formatted and filtered stack trace.
            </summary>
            <returns>Space separated list of methods</returns>
        </member>
        <member name="T:StackExchange.Profiling.Helpers.HaackFormatter">
            <summary>
            The <c>haack</c> formatter.
            <c>http://haacked.com/archive/2009/01/04/fun-with-named-formats-string-parsing-and-edge-cases.aspx</c>.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.HaackFormatter.Format(System.String,System.Object)">
            <summary>
            format the supplied string.
            </summary>
            <param name="format">The format.</param>
            <param name="source">The source.</param>
            <returns>The <see cref="T:System.String"/>.</returns>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.HaackFormatter.SplitFormat(System.String)">
            <summary>
            split and format the supplied string.
            </summary>
            <param name="format">The format.</param>
            <returns>the set of text expressions</returns>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.HaackFormatter.IndexOfExpressionStart(System.String,System.Int32)">
            <summary>
            index of the expression start.
            </summary>
            <param name="format">The format.</param>
            <param name="startIndex">The start index.</param>
            <returns>the start index</returns>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.HaackFormatter.IndexOfExpressionEnd(System.String,System.Int32)">
            <summary>
            index of the expression end.
            </summary>
            <param name="format">The format.</param>
            <param name="startIndex">The start index.</param>
            <returns>the expression end index.</returns>
        </member>
        <member name="T:StackExchange.Profiling.Helpers.HaackFormatter.FormatExpression">
            <summary>
            The format expression.
            </summary>
        </member>
        <member name="T:StackExchange.Profiling.Helpers.HaackFormatter.ITextExpression">
            <summary>
            The TextExpression interface.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.HaackFormatter.ITextExpression.Eval(System.Object)">
            <summary>
            evaluate the supplied object.
            </summary>
            <param name="o">The o.</param>
            <returns>a string containing the substituted text.</returns>
        </member>
        <member name="F:StackExchange.Profiling.Helpers.HaackFormatter.FormatExpression._invalidExpression">
            <summary>
            The _invalid expression.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.HaackFormatter.FormatExpression.#ctor(System.String)">
            <summary>
            Initialises a new instance of the <see cref="T:StackExchange.Profiling.Helpers.HaackFormatter.FormatExpression"/> class.
            </summary>
            <param name="expression">
            The expression.
            </param>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.HaackFormatter.FormatExpression.Eval(System.Object)">
            <summary>
            evaluate the expression
            </summary>
            <param name="o">The o.</param>
            <returns>The <see cref="T:System.String"/>.</returns>
        </member>
        <member name="P:StackExchange.Profiling.Helpers.HaackFormatter.FormatExpression.Expression">
            <summary>
            Gets the expression.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Helpers.HaackFormatter.FormatExpression.Format">
            <summary>
            Gets the format.
            </summary>
        </member>
        <member name="T:StackExchange.Profiling.Helpers.HaackFormatter.LiteralFormat">
            <summary>
            The literal format.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.HaackFormatter.LiteralFormat.#ctor(System.String)">
            <summary>
            Initialises a new instance of the <see cref="T:StackExchange.Profiling.Helpers.HaackFormatter.LiteralFormat"/> class.
            </summary>
            <param name="literalText">
            The literal text.
            </param>
        </member>
        <member name="M:StackExchange.Profiling.Helpers.HaackFormatter.LiteralFormat.Eval(System.Object)">
            <summary>
            evaluate the object
            </summary>
            <param name="o">The object.</param>
            <returns>The <see cref="T:System.String"/>.</returns>
        </member>
        <member name="P:StackExchange.Profiling.Helpers.HaackFormatter.LiteralFormat.LiteralText">
            <summary>
            Gets the literal text.
            </summary>
        </member>
        <member name="T:StackExchange.Profiling.IpAddressIdentity">
            <summary>
            Identifies users based on ip address.
            </summary>
        </member>
        <member name="T:StackExchange.Profiling.IUserProvider">
            <summary>
            Provides functionality to identify which user is profiling a request.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.IUserProvider.GetUser(System.Web.HttpRequest)">
            <summary>
            Returns a string to identify the user profiling the current 'request'.
            </summary>
            <param name="request">The current HttpRequest being profiled.</param>
        </member>
        <member name="M:StackExchange.Profiling.IpAddressIdentity.GetUser(System.Web.HttpRequest)">
            <summary>
            Returns the paramter HttpRequest's client ip address.
            We combine both the REMOTE_ADDR header (which is the connecting device's IP address),
            plus the HTTP_X_FORWARDED_FOR header if present (which is set by some proxy
            servers and load balancers). This allows us to have a unique per-user view, even
            when behind a proxy or load balancer.
            </summary>
        </member>
        <member name="T:StackExchange.Profiling.MiniProfiler">
            <summary>
            A single MiniProfiler can be used to represent any number of steps/levels in a call-graph, via Step()
            </summary>
            <remarks>Totally baller.</remarks>
        </member>
        <member name="M:StackExchange.Profiling.MiniProfiler.#ctor">
            <summary>
            Initialises a new instance of the <see cref="T:StackExchange.Profiling.MiniProfiler"/> class. 
            Obsolete - used for serialization.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.MiniProfiler.#ctor(System.String)">
            <summary>
            Initialises a new instance of the <see cref="T:StackExchange.Profiling.MiniProfiler"/> class.  Creates and starts a new MiniProfiler 
            for the root <paramref name="url"/>.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.MiniProfiler.#ctor(System.String,StackExchange.Profiling.ProfileLevel)">
            <summary>
            Initialises a new instance of the <see cref="T:StackExchange.Profiling.MiniProfiler"/> class.  Creates and starts a new MiniProfiler 
            for the root <paramref name="url"/>, filtering <see cref="T:StackExchange.Profiling.Timing"/> steps to <paramref name="level"/>.
            </summary>
        </member>
        <member name="F:StackExchange.Profiling.MiniProfiler._sw">
            <summary>
            Starts when this profiler is instantiated. Each <see cref="T:StackExchange.Profiling.Timing"/> step will use this Stopwatch's current ticks as
            their starting time.
            </summary>
        </member>
        <member name="F:StackExchange.Profiling.MiniProfiler._root">
            <summary>
            The root.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.MiniProfiler.Start">
            <summary>
            Starts a new MiniProfiler based on the current <see cref="T:StackExchange.Profiling.IProfilerProvider"/>. This new profiler can be accessed by
            <see cref="P:StackExchange.Profiling.MiniProfiler.Current"/>.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.MiniProfiler.Start(System.String)">
            <summary>
            Starts a new MiniProfiler based on the current <see cref="T:StackExchange.Profiling.IProfilerProvider"/>. This new profiler can be accessed by
            <see cref="P:StackExchange.Profiling.MiniProfiler.Current"/>.
            </summary>
            <param name="sessionName">
            Allows explicit naming of the new profiling session; when null, an appropriate default will be used, e.g. for
            a web request, the url will be used for the overall session name.
            </param>
        </member>
        <member name="M:StackExchange.Profiling.MiniProfiler.Start(StackExchange.Profiling.ProfileLevel,System.String)">
            <summary>
            Starts a new MiniProfiler based on the current <see cref="T:StackExchange.Profiling.IProfilerProvider"/>. This new profiler can be accessed by
            <see cref="P:StackExchange.Profiling.MiniProfiler.Current"/>.
            </summary>
            <param name="level">Profiling level. Default to Info.</param>
            <param name="sessionName">
            Allows explicit naming of the new profiling session; when null, an appropriate default will be used, e.g. for
            a web request, the url will be used for the overall session name.
            </param>
        </member>
        <member name="M:StackExchange.Profiling.MiniProfiler.Stop(System.Boolean)">
            <summary>
            Ends the current profiling session, if one exists.
            </summary>
            <param name="discardResults">
            When true, clears the <see cref="P:StackExchange.Profiling.MiniProfiler.Current"/> for this HttpContext, allowing profiling to 
            be prematurely stopped and discarded. Useful for when a specific route does not need to be profiled.
            </param>
        </member>
        <member name="M:StackExchange.Profiling.MiniProfiler.StepStatic(System.String)">
            <summary>
            Returns an <see cref="T:System.IDisposable"/> that will time the code between its creation and disposal. Use this method when you
            do not wish to include the StackExchange.Profiling namespace for the <see cref="M:StackExchange.Profiling.MiniProfilerExtensions.Step(StackExchange.Profiling.MiniProfiler,System.String)"/> extension method.
            </summary>
            <param name="name">A descriptive name for the code that is encapsulated by the resulting IDisposable's lifetime.</param>
            <returns>the static step.</returns>
        </member>
        <member name="M:StackExchange.Profiling.MiniProfiler.StepStatic(System.String,StackExchange.Profiling.ProfileLevel)">
            <summary>
            Returns an <see cref="T:System.IDisposable"/> that will time the code between its creation and disposal. Use this method when you
            do not wish to include the StackExchange.Profiling namespace for the <see cref="M:StackExchange.Profiling.MiniProfilerExtensions.Step(StackExchange.Profiling.MiniProfiler,System.String)"/> extension method.
            </summary>
            <param name="name">A descriptive name for the code that is encapsulated by the resulting IDisposable's lifetime.</param>
            <param name="level">This step's visibility level; allows filtering when <see cref="M:StackExchange.Profiling.MiniProfiler.Start(System.String)"/> is called.</param>
            <returns>the static step.</returns>
        </member>
        <member name="M:StackExchange.Profiling.MiniProfiler.ToJson">
            <summary>
            Renders the current <see cref="T:StackExchange.Profiling.MiniProfiler"/> to JSON.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.MiniProfiler.ToJson(StackExchange.Profiling.MiniProfiler)">
            <summary>
            Renders the parameter <see cref="T:StackExchange.Profiling.MiniProfiler"/> to JSON.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.MiniProfiler.FromJson(System.String)">
            <summary>
            Deserializes the JSON string parameter to a <see cref="T:StackExchange.Profiling.MiniProfiler"/>.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.MiniProfiler.RenderIncludes(System.Nullable{StackExchange.Profiling.RenderPosition},System.Nullable{System.Boolean},System.Nullable{System.Boolean},System.Nullable{System.Int32},System.Nullable{System.Boolean},System.Nullable{System.Boolean},System.Boolean,System.Nullable{System.Boolean})">
            <summary>
            Returns the <c>css</c> and <c>javascript</c> includes needed to display the MiniProfiler results UI.
            </summary>
            <param name="position">Which side of the page the profiler popup button should be displayed on (defaults to left)</param>
            <param name="showTrivial">Whether to show trivial timings by default (defaults to false)</param>
            <param name="showTimeWithChildren">Whether to show time the time with children column by default (defaults to false)</param>
            <param name="maxTracesToShow">The maximum number of trace popups to show before removing the oldest (defaults to 15)</param>
            <param name="showControls">when true, shows buttons to minimize and clear MiniProfiler results</param>
            <param name="useExistingjQuery">
            Should MiniProfiler attempt to load its own version of jQuery, or rely on a version previously loaded on the page?
            </param>
            <param name="samplingOnly">The sampling Only.</param>
            <param name="startHidden">Should the profiler start as hidden. Default to null.</param>
            <returns>Script and link elements normally; an empty string when there is no active profiling session.</returns>
        </member>
        <member name="M:StackExchange.Profiling.MiniProfiler.ToString">
            <summary>
            Returns the <see cref="P:StackExchange.Profiling.MiniProfiler.Root"/>'s <see cref="P:StackExchange.Profiling.Timing.Name"/> and <see cref="P:StackExchange.Profiling.MiniProfiler.DurationMilliseconds"/> this profiler recorded.
            </summary>
            <returns>a string containing the recording information</returns>
        </member>
        <member name="M:StackExchange.Profiling.MiniProfiler.Equals(System.Object)">
            <summary>
            Returns true if Ids match.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.MiniProfiler.GetHashCode">
            <summary>
            Returns hash code of Id.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.MiniProfiler.GetTimingHierarchy">
            <summary>
            Walks the <see cref="T:StackExchange.Profiling.Timing"/> hierarchy contained in this profiler, starting with <see cref="P:StackExchange.Profiling.MiniProfiler.Root"/>, and returns each Timing found.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.MiniProfiler.Clone">
            <summary>
            Create a DEEP clone of this MiniProfiler.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.MiniProfiler.GetRoundedMilliseconds(System.Int64)">
            <summary>
            Returns milliseconds based on Stopwatch's Frequency, rounded to one decimal place.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.MiniProfiler.GetDurationMilliseconds(System.Int64)">
            <summary>
            Returns how many milliseconds have elapsed since <paramref name="startTicks"/> was recorded.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.MiniProfiler.OnDeserialized(System.Runtime.Serialization.StreamingContext)">
            <summary>
            Called immediately after deserialization.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.MiniProfiler.SqlProfiler">
            <summary>
            Contains information about queries executed during this profiling session.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.MiniProfiler.Id">
            <summary>
            Gets or sets the profiler id.
            Identifies this Profiler so it may be stored/cached.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.MiniProfiler.Name">
            <summary>
            Gets or sets a display name for this profiling session.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.MiniProfiler.Started">
            <summary>
            Gets or sets when this profiler was instantiated, in UTC time.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.MiniProfiler.DurationMilliseconds">
            <summary>
            Gets the milliseconds, to one decimal place, that this MiniProfiler ran.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.MiniProfiler.MachineName">
            <summary>
            Gets or sets where this profiler was run.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.MiniProfiler.CustomLinks">
            <summary>
            Keys are names, values are URLs, allowing additional links to be added to a profiler result, e.g. perhaps a deeper
            diagnostic page for the current request.
            </summary>
            <remarks>
            Use <see cref="M:StackExchange.Profiling.MiniProfilerExtensions.AddCustomLink(StackExchange.Profiling.MiniProfiler,System.String,System.String)"/> to easily add a name/url pair to this dictionary.
            </remarks>
        </member>
        <member name="P:StackExchange.Profiling.MiniProfiler.CustomLinksJson">
            <summary>
            Json used to store Custom Links. Do not touch.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.MiniProfiler.Root">
            <summary>
            Gets or sets the root timing.
            The first <see cref="T:StackExchange.Profiling.Timing"/> that is created and started when this profiler is instantiated.
            All other <see cref="T:StackExchange.Profiling.Timing"/>s will be children of this one.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.MiniProfiler.RootTimingId">
            <summary>
            Id of Root Timing. Used for Sql Storage purposes.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.MiniProfiler.ClientTimings">
            <summary>
            Gets or sets timings collected from the client
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.MiniProfiler.ClientTimingsRedirectCount">
            <summary>
            RedirectCount in ClientTimings. Used for sql storage.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.MiniProfiler.User">
            <summary>
            Gets or sets a string identifying the user/client that is profiling this request.
            </summary>
            <remarks>
            If this is not set manually at some point, the <see cref="T:StackExchange.Profiling.IUserProvider"/> implementation will be used;
            by default, this will be the current request's IP address.
            </remarks>
        </member>
        <member name="P:StackExchange.Profiling.MiniProfiler.HasUserViewed">
            <summary>
            Returns true when this MiniProfiler has been viewed by the <see cref="P:StackExchange.Profiling.MiniProfiler.User"/> that recorded it.
            </summary>
            <remarks>
            Allows POSTs that result in a redirect to be profiled. <see cref="P:StackExchange.Profiling.MiniProfiler.Settings.Storage"/> implementation
            will keep a list of all profilers that haven't been fetched down.
            </remarks>
        </member>
        <member name="P:StackExchange.Profiling.MiniProfiler.Level">
            <summary>
            Gets or sets whether or not filtering is allowed of <see cref="T:StackExchange.Profiling.Timing"/> steps based on what <see cref="T:StackExchange.Profiling.ProfileLevel"/> 
            the steps are created with.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.MiniProfiler.Head">
            <summary>
            Gets or sets points to the currently executing Timing. 
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.MiniProfiler.ElapsedTicks">
            <summary>
            Gets the ticks since this MiniProfiler was started.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.MiniProfiler.Stopwatch">
            <summary>
            Gets the timer, for unit testing, returns the timer.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.MiniProfiler.Current">
            <summary>
            Gets the currently running MiniProfiler for the current HttpContext; null if no MiniProfiler was <see cref="M:StackExchange.Profiling.MiniProfiler.Start(System.String)"/>ed.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.MiniProfiler.Storage">
            <summary>
            A <see cref="T:StackExchange.Profiling.Storage.IStorage"/> strategy to use for the current profiler. 
            If null, then the <see cref="T:StackExchange.Profiling.Storage.IStorage"/> set in <see cref="P:StackExchange.Profiling.MiniProfiler.Settings.Storage"/> will be used.
            </summary>
            <remarks>Used to set custom storage for an individual request</remarks>
        </member>
        <member name="T:StackExchange.Profiling.MiniProfiler.Settings">
            <summary>
            Various configuration properties.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.MiniProfiler.Settings.ExcludeAssembly(System.String)">
            <summary>
            Excludes the specified assembly from the stack trace output.
            </summary>
            <param name="assemblyName">The short name of the assembly. AssemblyName.Name</param>
        </member>
        <member name="M:StackExchange.Profiling.MiniProfiler.Settings.ExcludeType(System.String)">
            <summary>
            Excludes the specified type from the stack trace output.
            </summary>
            <param name="typeToExclude">The System.Type name to exclude</param>
        </member>
        <member name="M:StackExchange.Profiling.MiniProfiler.Settings.ExcludeMethod(System.String)">
            <summary>
            Excludes the specified method name from the stack trace output.
            </summary>
            <param name="methodName">The name of the method</param>
        </member>
        <member name="M:StackExchange.Profiling.MiniProfiler.Settings.EnsureStorageStrategy">
            <summary>
            Make sure we can at least store profiler results to the http runtime cache.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.MiniProfiler.Settings.AssembliesToExclude">
            <summary>
            Assemblies to exclude from the stack trace report.
            Add to this using the <see cref="M:StackExchange.Profiling.MiniProfiler.Settings.ExcludeAssembly(System.String)"/> method.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.MiniProfiler.Settings.TypesToExclude">
            <summary>
            Types to exclude from the stack trace report.
            Add to this using the <see cref="M:StackExchange.Profiling.MiniProfiler.Settings.ExcludeType(System.String)"/> method.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.MiniProfiler.Settings.MethodsToExclude">
            <summary>
            Methods to exclude from the stack trace report.
            Add to this using the <see cref="M:StackExchange.Profiling.MiniProfiler.Settings.ExcludeMethod(System.String)"/> method.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.MiniProfiler.Settings.MaxUnviewedProfiles">
            <summary>
            The maximum number of unviewed profiler sessions (set this low cause we don't want to blow up headers)
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.MiniProfiler.Settings.StackMaxLength">
            <summary>
            The max length of the stack string to report back; defaults to 120 chars.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.MiniProfiler.Settings.TrivialDurationThresholdMilliseconds">
            <summary>
            Any Timing step with a duration less than or equal to this will be hidden by default in the UI; defaults to 2.0 ms.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.MiniProfiler.Settings.PopupShowTimeWithChildren">
            <summary>
            Dictates if the "time with children" column is displayed by default, defaults to false.
            For a per-page override you can use .RenderIncludes(showTimeWithChildren: true/false)
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.MiniProfiler.Settings.PopupShowTrivial">
            <summary>
            Dictates if trivial timings are displayed by default, defaults to false.
            For a per-page override you can use .RenderIncludes(showTrivial: true/false)
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.MiniProfiler.Settings.PopupMaxTracesToShow">
            <summary>
            Determines how many traces to show before removing the oldest; defaults to 15.
            For a per-page override you can use .RenderIncludes(maxTracesToShow: 10)
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.MiniProfiler.Settings.PopupRenderPosition">
            <summary>
            Dictates on which side of the page the profiler popup button is displayed; defaults to left.
            For a per-page override you can use .RenderIncludes(position: RenderPosition.Left/Right)
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.MiniProfiler.Settings.PopupToggleKeyboardShortcut">
            <summary>
            Allows showing/hiding of popup results buttons via keyboard.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.MiniProfiler.Settings.PopupStartHidden">
            <summary>
            When true, results buttons will not initially be shown, requiring keyboard activation via <see cref="P:StackExchange.Profiling.MiniProfiler.Settings.PopupToggleKeyboardShortcut"/>.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.MiniProfiler.Settings.ShowControls">
            <summary>
            Determines if min-max, clear, etc are rendered; defaults to false.
            For a per-page override you can use .RenderIncludes(showControls: true/false)
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.MiniProfiler.Settings.UseExistingjQuery">
            <summary>
            Determines if Miniprofiler relies on jQuery already loaded on the page; defaults to false.
            For a per-page override you can use .RenderIncludes(useExistingjQuery: true/false)
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.MiniProfiler.Settings.ExcludeStackTraceSnippetFromSqlTimings">
            <summary>
            By default, SqlTimings will grab a stack trace to help locate where queries are being executed.
            When this setting is true, no stack trace will be collected, possibly improving profiler performance.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.MiniProfiler.Settings.ExcludeStackTraceSnippetFromCustomTimings">
            <summary>
            By default, <see cref="T:StackExchange.Profiling.CustomTiming"/>s created by this assmebly will grab a stack trace to help 
            locate where Remote Procedure Calls are being executed.  When this setting is true, no stack trace 
            will be collected, possibly improving profiler performance.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.MiniProfiler.Settings.IgnoredPaths">
            <summary>
            When <see cref="M:StackExchange.Profiling.MiniProfiler.Start(System.String)"/> is called, if the current request url contains any items in this property,
            no profiler will be instantiated and no results will be displayed.
            Default value is { "/content/", "/scripts/", "/favicon.ico" }.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.MiniProfiler.Settings.RouteBasePath">
            <summary>
            The path under which ALL routes are registered in, defaults to the application root.  For example, "~/myDirectory/" would yield
            "/myDirectory/includes.js" rather than just "/mini-profiler-resources/includes.js"
            Any setting here should be in APP RELATIVE FORM, e.g. "~/myDirectory/"
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.MiniProfiler.Settings.CustomUITemplates">
            <summary>
            The path where custom ui elements are stored.
            If the custom file doesn't exist, the standard resource is used.
            This setting should be in APP RELATIVE FORM, e.g. "~/App_Data/MiniProfilerUI"
            </summary>
            <remarks>A web server restart is required to reload new files.</remarks>
        </member>
        <member name="P:StackExchange.Profiling.MiniProfiler.Settings.MaxJsonResponseSize">
            <summary>
            Maximum payload size for json responses in bytes defaults to 2097152 characters, which is equivalent to 4 MB of Unicode string data.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.MiniProfiler.Settings.Storage">
            <summary>
            Understands how to save and load MiniProfilers. Used for caching between when
            a profiling session ends and results can be fetched to the client, and for showing shared, full-page results.
            </summary>
            <remarks>
            The normal profiling session life-cycle is as follows:
            1) request begins
            2) profiler is started
            3) normal page/controller/request execution
            4) profiler is stopped
            5) profiler is cached with <see cref="P:StackExchange.Profiling.MiniProfiler.Settings.Storage"/>'s implementation of <see cref="M:StackExchange.Profiling.Storage.IStorage.Save(StackExchange.Profiling.MiniProfiler)"/>
            6) request ends
            7) page is displayed and profiling results are ajax-fetched down, pulling cached results from 
               <see cref="P:StackExchange.Profiling.MiniProfiler.Settings.Storage"/>'s implementation of <see cref="M:StackExchange.Profiling.Storage.IStorage.Load(System.Guid)"/>
            </remarks>
        </member>
        <member name="P:StackExchange.Profiling.MiniProfiler.Settings.SqlFormatter">
            <summary>
            The formatter applied to any SQL before being set in a <see cref="P:StackExchange.Profiling.CustomTiming.CommandString"/>.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.MiniProfiler.Settings.Version">
            <summary>
            Assembly version of this dank MiniProfiler.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.MiniProfiler.Settings.ProfilerProvider">
            <summary>
            The <see cref="T:StackExchange.Profiling.IProfilerProvider"/> class that is used to run MiniProfiler
            </summary>
            <remarks>
            If not set explicitly, will default to <see cref="T:StackExchange.Profiling.WebRequestProfilerProvider"/>
            </remarks>
        </member>
        <member name="P:StackExchange.Profiling.MiniProfiler.Settings.Results_Authorize">
            <summary>
            A function that determines who can access the MiniProfiler results url and list url.  It should return true when
            the request client has access to results, false for a 401 to be returned. HttpRequest parameter is the current request and
            </summary>
            <remarks>
            The HttpRequest parameter that will be passed into this function should never be null.
            </remarks>
        </member>
        <member name="P:StackExchange.Profiling.MiniProfiler.Settings.Results_List_Authorize">
            <summary>
            Special authorization function that is called for the list results (listing all the profiling sessions), 
            we also test for results authorize always. This must be set and return true, to enable the listing feature.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.MiniProfiler.Settings.StopwatchProvider">
            <summary>
            Allows switching out stopwatches for unit testing.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.MiniProfiler.Settings.EnableCompression">
            <summary>
            By default, the output of the MiniProfilerHandler is compressed, if the request supports that.
            If this setting is false, the output won't be compressed. (Only do this when you take care of compression yourself)
            </summary>
        </member>
        <member name="T:StackExchange.Profiling.ClientTimingHelper">
            <summary>
            Pro
            </summary>
        </member>
        <member name="F:StackExchange.Profiling.ClientTimingHelper.InitScript">
            <summary>
            This code needs to be inserted in the page before client timings work
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.ClientTimingHelper.TimeScript(System.String,System.String)">
            <summary>
            You can wrap an html block with timing wrappers using this helper
            </summary>
        </member>
        <member name="T:StackExchange.Profiling.MiniProfilerExtensions">
            <summary>
            Contains helper methods that ease working with null <see cref="T:StackExchange.Profiling.MiniProfiler"/>s.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.MiniProfilerExtensions.Inline``1(StackExchange.Profiling.MiniProfiler,System.Func{``0},System.String)">
            <summary>
            Wraps <paramref name="selector"/> in a <see cref="M:StackExchange.Profiling.MiniProfilerExtensions.Step(StackExchange.Profiling.MiniProfiler,System.String)"/> call and executes it, returning its result.
            </summary>
            <typeparam name="T">the type of result.</typeparam>
            <param name="profiler">The current profiling session or null.</param>
            <param name="selector">Method to execute and profile.</param>
            <param name="name">The <see cref="T:StackExchange.Profiling.Timing"/> step name used to label the profiler results.</param>
            <returns>the profiled result.</returns>
        </member>
        <member name="M:StackExchange.Profiling.MiniProfilerExtensions.Inline``1(StackExchange.Profiling.MiniProfiler,System.Func{``0},System.String,StackExchange.Profiling.ProfileLevel)">
            <summary>
            Wraps <paramref name="selector"/> in a <see cref="M:StackExchange.Profiling.MiniProfilerExtensions.Step(StackExchange.Profiling.MiniProfiler,System.String)"/> call and executes it, returning its result.
            </summary>
            <typeparam name="T">the type of result.</typeparam>
            <param name="profiler">The current profiling session or null.</param>
            <param name="selector">Method to execute and profile.</param>
            <param name="name">The <see cref="T:StackExchange.Profiling.Timing"/> step name used to label the profiler results.</param>
            <param name="level">This step's visibility level; allows filtering when <see cref="M:StackExchange.Profiling.MiniProfiler.Start(System.String)"/> is called.</param>
            <returns>the profiled result.</returns>
        </member>
        <member name="M:StackExchange.Profiling.MiniProfilerExtensions.Step(StackExchange.Profiling.MiniProfiler,System.String)">
            <summary>
            Returns an <see cref="T:System.IDisposable"/> that will time the code between its creation and disposal.
            </summary>
            <param name="profiler">The current profiling session or null.</param>
            <param name="name">A descriptive name for the code that is encapsulated by the resulting IDisposable's lifetime.</param>
            <returns>the profile step</returns>
        </member>
        <member name="M:StackExchange.Profiling.MiniProfilerExtensions.Step(StackExchange.Profiling.MiniProfiler,System.String,StackExchange.Profiling.ProfileLevel)">
            <summary>
            Returns an <see cref="T:System.IDisposable"/> that will time the code between its creation and disposal.
            </summary>
            <param name="profiler">The current profiling session or null.</param>
            <param name="name">A descriptive name for the code that is encapsulated by the resulting IDisposable's lifetime.</param>
            <param name="level">This step's visibility level; allows filtering when <see cref="M:StackExchange.Profiling.MiniProfiler.Start(System.String)"/> is called.</param>
            <returns>the profile step</returns>
        </member>
        <member name="M:StackExchange.Profiling.MiniProfilerExtensions.StepIf(StackExchange.Profiling.MiniProfiler,System.String,System.Decimal,System.Boolean)">
            <summary>
            Returns an <see cref="T:System.IDisposable"/> that will time the code between its creation and disposal.
            Will only save the <see cref="T:StackExchange.Profiling.Timing"/> if total time taken exceeds <paramref name="minSaveMs"/>.
            </summary>
            <param name="profiler">The current profiling session or null.</param>
            <param name="name">A descriptive name for the code that is encapsulated by the resulting IDisposable's lifetime.</param>
            <param name="minSaveMs">The minimum amount of time that needs to elapse in order for this result to be recorded.</param>
            <param name="includeChildren">Should the amount of time spent in child timings be included when comparing total time
            profiled with <paramref name="minSaveMs"/>? If true, will include children. If false will ignore children.</param>
            <returns></returns>
            <remarks>If <paramref name="includeChildren"/> is set to true and a child is removed due to its use of StepIf, then the 
            time spent in that time will also not count for the current StepIf calculation.</remarks>
        </member>
        <member name="M:StackExchange.Profiling.MiniProfilerExtensions.CustomTiming(StackExchange.Profiling.MiniProfiler,System.String,System.String,System.String)">
            <summary>
            Returns a new <see cref="M:StackExchange.Profiling.MiniProfilerExtensions.CustomTiming(StackExchange.Profiling.MiniProfiler,System.String,System.String,System.String)"/> that will automatically set its <see cref="P:StackExchange.Profiling.CustomTiming.StartMilliseconds"/>
            and <see cref="P:StackExchange.Profiling.CustomTiming.DurationMilliseconds"/>
            </summary>
            <param name="profiler">The current profiling session or null.</param>
            <param name="category">The category under which this timing will be recorded.</param>
            <param name="commandString">The command string that will be recorded along with this timing, for display in the MiniProfiler results.</param>
            <param name="executeType">Execute Type to be associated with the Custom Timing. Example: Get, Set, Insert, Delete</param>
            <remarks>
            Should be used like the <see cref="M:StackExchange.Profiling.MiniProfilerExtensions.Step(StackExchange.Profiling.MiniProfiler,System.String)"/> extension method
            </remarks>
        </member>
        <member name="M:StackExchange.Profiling.MiniProfilerExtensions.CustomTimingIf(StackExchange.Profiling.MiniProfiler,System.String,System.String,System.Decimal,System.String)">
            <summary>
            Returns a new <see cref="M:StackExchange.Profiling.MiniProfilerExtensions.CustomTiming(StackExchange.Profiling.MiniProfiler,System.String,System.String,System.String)"/> that will automatically set its <see cref="P:StackExchange.Profiling.CustomTiming.StartMilliseconds"/>
            and <see cref="P:StackExchange.Profiling.CustomTiming.DurationMilliseconds"/>. Will only save the new <see cref="T:StackExchange.Profiling.Timing"/> if the total elapsed time
            takes more than <paramef name="minSaveMs"/>.
            </summary>
            <param name="profiler">The current profiling session or null.</param>
            <param name="category">The category under which this timing will be recorded.</param>
            <param name="commandString">The command string that will be recorded along with this timing, for display in the MiniProfiler results.</param>
            <param name="executeType">Execute Type to be associated with the Custom Timing. Example: Get, Set, Insert, Delete</param>
            <param name="minSaveMs">The minimum amount of time that needs to elapse in order for this result to be recorded.</param>
            <remarks>
            Should be used like the <see cref="M:StackExchange.Profiling.MiniProfilerExtensions.Step(StackExchange.Profiling.MiniProfiler,System.String)"/> extension method 
            </remarks>
        </member>
        <member name="M:StackExchange.Profiling.MiniProfilerExtensions.Ignore(StackExchange.Profiling.MiniProfiler)">
            <summary>
            Returns an <see cref="T:System.IDisposable"/> that will ignore profiling between its creation and disposal.
            </summary>
            <remarks>
            This is mainly useful in situations where you want to ignore database profiling for known hot spots,
            but it is safe to use in a nested step such that you can ignore sub-sections of a profiled step.
            </remarks>
            <param name="profiler">The current profiling session or null.</param>
            <returns>the profile step</returns>
        </member>
        <member name="M:StackExchange.Profiling.MiniProfilerExtensions.AddProfilerResults(StackExchange.Profiling.MiniProfiler,StackExchange.Profiling.MiniProfiler)">
            <summary>
            Adds <paramref name="externalProfiler"/>'s <see cref="T:StackExchange.Profiling.Timing"/> hierarchy to this profiler's current Timing step,
            allowing other threads, remote calls, etc. to be profiled and joined into this profiling session.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.MiniProfilerExtensions.AddCustomLink(StackExchange.Profiling.MiniProfiler,System.String,System.String)">
            <summary>
            Adds the <paramref name="text"/> and <paramref name="url"/> pair to <paramref name="profiler"/>'s 
            <see cref="P:StackExchange.Profiling.MiniProfiler.CustomLinks"/> dictionary; will be displayed on the client in the bottom of the profiler popup.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.MiniProfilerExtensions.Render(StackExchange.Profiling.MiniProfiler)">
            <summary>
            Returns an html-encoded string with a text-representation of <paramref name="profiler"/>; returns "" when profiler is null.
            </summary>
            <param name="profiler">The current profiling session or null.</param>
        </member>
        <member name="M:StackExchange.Profiling.MiniProfilerExtensions.RenderPlainText(StackExchange.Profiling.MiniProfiler)">
            <summary>
            Returns a plain-text representation of <paramref name="profiler"/>, suitable for viewing from 
            <see cref="T:System.Console"/>, log, or unit test output.
            </summary>
            <param name="profiler">A profiling session with child <see cref="T:StackExchange.Profiling.Timing"/> instances.</param>
        </member>
        <member name="T:StackExchange.Profiling.ProfileLevel">
            <summary>
            Categorizes individual <see cref="T:StackExchange.Profiling.Timing"/> steps to allow filtering.
            </summary>
        </member>
        <member name="F:StackExchange.Profiling.ProfileLevel.Info">
            <summary>
            Default level given to Timings.
            </summary>
        </member>
        <member name="F:StackExchange.Profiling.ProfileLevel.Verbose">
            <summary>
            Useful when profiling many items in a loop, but you don't wish to always see this detail.
            </summary>
        </member>
        <member name="T:StackExchange.Profiling.RenderPosition">
            <summary>
            Dictates on which side of the page the profiler popup button is displayed; defaults to top left.
            </summary>
        </member>
        <member name="F:StackExchange.Profiling.RenderPosition.Left">
            <summary>
            Profiler popup button is displayed on the top left.
            </summary>
        </member>
        <member name="F:StackExchange.Profiling.RenderPosition.Right">
            <summary>
            Profiler popup button is displayed on the top right.
            </summary>
        </member>
        <member name="F:StackExchange.Profiling.RenderPosition.BottomLeft">
            <summary>
            Profiler popup button is displayed on the bottom left.
            </summary>
        </member>
        <member name="F:StackExchange.Profiling.RenderPosition.BottomRight">
            <summary>
            Profiler popup button is displayed on the bottom right.
            </summary>
        </member>
        <member name="T:StackExchange.Profiling.SqlFormatters.InlineFormatter">
            <summary>
            Formats any SQL query with inline parameters, optionally including the value type
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.SqlFormatters.InlineFormatter.#ctor(System.Boolean)">
            <summary>
            Creates a new Inline SQL Formatter, optionally including the parameter type info 
            in comments beside the replaced value
            </summary>
            <param name="includeTypeInfo">
            whether to include a comment after the value, indicating the type, e.g. <c>/* @myParam DbType.Int32 */</c>
            </param>
        </member>
        <member name="M:StackExchange.Profiling.SqlFormatters.InlineFormatter.FormatSql(System.String,System.Collections.Generic.List{StackExchange.Profiling.SqlTimingParameter})">
            <summary>
            Formats the SQL in a generic friendly format, including the parameter type information 
            in a comment if it was specified in the InlineFormatter constructor
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.SqlFormatters.InlineFormatter.GetParameterValue(StackExchange.Profiling.SqlTimingParameter)">
            <summary>
            Returns a string representation of the parameter's value, including the type
            </summary>
        </member>
        <member name="T:StackExchange.Profiling.SingletonProfilerProvider">
            <summary>
            Mostly for unit testing and single-threaded apps, only allows one 
            instance of a <see cref="T:StackExchange.Profiling.MiniProfiler"/> to be the <see cref="P:StackExchange.Profiling.MiniProfiler.Current"/> one.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.SingletonProfilerProvider.GetCurrentProfiler">
            <summary>
            The name says it all
            </summary>
            <returns></returns>
        </member>
        <member name="M:StackExchange.Profiling.SingletonProfilerProvider.Start(System.String)">
            <summary>
            Starts a new profiling session.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.SingletonProfilerProvider.Start(StackExchange.Profiling.ProfileLevel,System.String)">
            <summary>
            Starts a new profiling session.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.SingletonProfilerProvider.Stop(System.Boolean)">
            <summary>
            Stops the current profiling session.
            </summary>
        </member>
        <member name="T:StackExchange.Profiling.Storage.HttpRuntimeCacheStorage">
            <summary>
            Understands how to store a <see cref="T:StackExchange.Profiling.MiniProfiler"/> to the <see cref="P:System.Web.HttpRuntime.Cache"/> 
            with absolute expiration.
            </summary>
        </member>
        <member name="F:StackExchange.Profiling.Storage.HttpRuntimeCacheStorage.CacheKeyPrefix">
            <summary>
            The string that prefixes all keys that MiniProfilers are saved under, e.g.
            <c>"mini-profiler-ecfb0050-7ce8-4bf1-bf82-2cb38e90e31e".</c>
            </summary>
        </member>
        <member name="F:StackExchange.Profiling.Storage.HttpRuntimeCacheStorage.AddPerUserUnviewedIdsLock">
            <summary>
            Syncs access to runtime cache when adding a new list of ids for a user.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Storage.HttpRuntimeCacheStorage.#ctor(System.TimeSpan)">
            <summary>
            Initialises a new instance of the <see cref="T:StackExchange.Profiling.Storage.HttpRuntimeCacheStorage"/> class. 
            Returns a new HttpRuntimeCacheStorage class that will cache MiniProfilers for the specified duration.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Storage.HttpRuntimeCacheStorage.Save(StackExchange.Profiling.MiniProfiler)">
            <summary>
            Saves <paramref name="profiler"/> to the HttpRuntime.Cache under a key concatenated with <see cref="F:StackExchange.Profiling.Storage.HttpRuntimeCacheStorage.CacheKeyPrefix"/>
            and the parameter's <see cref="P:StackExchange.Profiling.MiniProfiler.Id"/>.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Storage.HttpRuntimeCacheStorage.SetUnviewed(System.String,System.Guid)">
            <summary>
            remembers we did not view the profile
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Storage.HttpRuntimeCacheStorage.SetViewed(System.String,System.Guid)">
            <summary>
            Set the profile to viewed for this user
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Storage.HttpRuntimeCacheStorage.Load(System.Guid)">
            <summary>
            Returns the saved <see cref="T:StackExchange.Profiling.MiniProfiler"/> identified by <paramref name="id"/>. Also marks the resulting
            profiler <see cref="P:StackExchange.Profiling.MiniProfiler.HasUserViewed"/> to true.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Storage.HttpRuntimeCacheStorage.GetUnviewedIds(System.String)">
            <summary>
            Returns a list of <see cref="P:StackExchange.Profiling.MiniProfiler.Id"/>s that haven't been seen by <paramref name="user"/>.
            </summary>
            <param name="user">
            User identified by the current <c>MiniProfiler.Settings.UserProvider</c>.
            </param>
        </member>
        <member name="M:StackExchange.Profiling.Storage.HttpRuntimeCacheStorage.List(System.Int32,System.Nullable{System.DateTime},System.Nullable{System.DateTime},StackExchange.Profiling.Storage.ListResultsOrder)">
            <summary>
            List the latest profiling results.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Storage.HttpRuntimeCacheStorage.CacheDuration">
            <summary>
            Gets or sets how long to cache each <see cref="T:StackExchange.Profiling.MiniProfiler"/> for, in absolute terms.
            </summary>
        </member>
        <member name="T:StackExchange.Profiling.Storage.HttpRuntimeCacheStorage.ProfileInfo">
            <summary>
            Identifies a MiniProfiler result and only contains the needed info for sorting a list of profiling sessions.
            </summary>
            <remarks>SortedList on uses the comparer for both key lookups and insertion</remarks>
        </member>
        <member name="T:StackExchange.Profiling.Storage.ListResultsOrder">
            <summary>
            How lists should be sorted.
            </summary>
        </member>
        <member name="F:StackExchange.Profiling.Storage.ListResultsOrder.Ascending">
            <summary>
            Ascending Order
            </summary>
        </member>
        <member name="F:StackExchange.Profiling.Storage.ListResultsOrder.Descending">
            <summary>
            Descending Order
            </summary>
        </member>
        <member name="T:StackExchange.Profiling.SqlProfiler">
            <summary>
            Contains helper code to time SQL statements.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.SqlProfiler.#ctor(StackExchange.Profiling.MiniProfiler)">
            <summary>
            Returns a new <c>SqlProfiler</c> to be used in the <paramref name="profiler"/> session.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.SqlProfiler.ExecuteStartImpl(System.Data.IDbCommand,StackExchange.Profiling.Data.SqlExecuteType)">
            <summary>
            Tracks when 'command' is started.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.SqlProfiler.ExecuteFinishImpl(System.Data.IDbCommand,StackExchange.Profiling.Data.SqlExecuteType,System.Data.Common.DbDataReader)">
            <summary>
            Finishes profiling for 'command', recording durations.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.SqlProfiler.ReaderFinishedImpl(System.Data.IDataReader)">
            <summary>
            Called when 'reader' finishes its iterations and is closed.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.SqlProfiler.GetInProgressCommands">
            <summary>
            Returns all currently open commands on this connection
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.SqlProfiler.Profiler">
            <summary>
            Gets the profiling session this <c>SqlProfiler</c> is part of.
            </summary>
        </member>
        <member name="T:StackExchange.Profiling.SqlProfilerExtensions">
            <summary>
            Helper methods that allow operation on <c>SqlProfilers</c>, regardless of their instantiation.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.SqlProfilerExtensions.ExecuteStart(StackExchange.Profiling.SqlProfiler,System.Data.IDbCommand,StackExchange.Profiling.Data.SqlExecuteType)">
            <summary>
            Tracks when 'command' is started.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.SqlProfilerExtensions.ExecuteFinish(StackExchange.Profiling.SqlProfiler,System.Data.IDbCommand,StackExchange.Profiling.Data.SqlExecuteType,System.Data.Common.DbDataReader)">
            <summary>
            Finishes profiling for 'command', recording durations.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.SqlProfilerExtensions.ReaderFinish(StackExchange.Profiling.SqlProfiler,System.Data.IDataReader)">
            <summary>
            Called when 'reader' finishes its iterations and is closed.
            </summary>
        </member>
        <member name="T:StackExchange.Profiling.Timing">
            <summary>
            An individual profiling step that can contain child steps.
            </summary>
        </member>
        <member name="F:StackExchange.Profiling.Timing._startTicks">
            <summary>
            Offset from parent MiniProfiler's creation that this Timing was created.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Timing.#ctor">
            <summary>
            Initialises a new instance of the <see cref="T:StackExchange.Profiling.Timing"/> class. 
            Obsolete - used for serialization.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Timing.#ctor(StackExchange.Profiling.MiniProfiler,StackExchange.Profiling.Timing,System.String,System.Nullable{System.Decimal},System.Nullable{System.Boolean})">
            <summary>
            Creates a new Timing named 'name' in the 'profiler's session, with 'parent' as this Timing's immediate ancestor.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Timing.ToString">
            <summary>
            Returns this Timing's Name.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Timing.Equals(System.Object)">
            <summary>
            Returns true if Ids match.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Timing.GetHashCode">
            <summary>
            Returns hash code of Id.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Timing.Stop">
            <summary>
            Completes this Timing's duration and sets the MiniProfiler's Head up one level.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Timing.System#IDisposable#Dispose">
            <summary>
            Stops profiling, allowing the <c>using</c> construct to neatly encapsulate a region to be profiled.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.Timing.AddChild(StackExchange.Profiling.Timing)">
            <summary>
            Add the parameter 'timing' to this Timing's Children collection.
            </summary>
            <remarks>
            Used outside this assembly for custom deserialization when creating an <see cref="T:StackExchange.Profiling.Storage.IStorage"/> implementation.
            </remarks>
        </member>
        <member name="M:StackExchange.Profiling.Timing.AddCustomTiming(System.String,StackExchange.Profiling.CustomTiming)">
            <summary>
            Adds <paramref name="customTiming"/> to this <see cref="T:StackExchange.Profiling.Timing"/> step's dictionary of 
            custom timings, <see cref="P:StackExchange.Profiling.Timing.CustomTimings"/>.  Ensures that <see cref="P:StackExchange.Profiling.Timing.CustomTimings"/> is created, 
            as well as the <paramref name="category"/>'s list.
            </summary>
            <param name="category">The kind of custom timing, e.g. "http", "redis", "memcache"</param>
            <param name="customTiming">Duration and command information</param>
        </member>
        <member name="M:StackExchange.Profiling.Timing.GetCustomTimingList(System.String)">
            <summary>
            Returns the <see cref="T:StackExchange.Profiling.CustomTiming"/> list keyed to the <paramref name="category"/>, creating any collections when null.
            </summary>
            <param name="category">The kind of custom timings, e.g. "sql", "redis", "memcache"</param>
        </member>
        <member name="P:StackExchange.Profiling.Timing.Id">
            <summary>
            Gets or sets Unique identifier for this timing; set during construction.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Timing.Name">
            <summary>
            Gets or sets Text displayed when this Timing is rendered.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Timing.DurationMilliseconds">
            <summary>
            Gets or sets How long this Timing step took in ms; includes any <see cref="P:StackExchange.Profiling.Timing.Children"/> Timings' durations.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Timing.StartMilliseconds">
            <summary>
            Gets or sets The offset from the start of profiling.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Timing.Children">
            <summary>
            Gets or sets All sub-steps that occur within this Timing step. Add new children through <see cref="M:StackExchange.Profiling.Timing.AddChild(StackExchange.Profiling.Timing)"/>
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Timing.CustomTimings">
            <summary>
            <see cref="T:StackExchange.Profiling.CustomTiming"/> lists keyed by their type, e.g. "sql", "memcache", "redis", "http".
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Timing.CustomTimingsJson">
            <summary>
            JSON representing the Custom Timings associated with this timing.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Timing.HasCustomTimings">
            <summary>
            Returns true when there exists any <see cref="T:StackExchange.Profiling.CustomTiming"/> objects in this <see cref="P:StackExchange.Profiling.Timing.CustomTimings"/>.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Timing.ParentTiming">
            <summary>
            Gets or sets Which Timing this Timing is under - the duration that this step takes will be added to its parent's duration.
            </summary>
            <remarks>This will be null for the root (initial) Timing.</remarks>
        </member>
        <member name="P:StackExchange.Profiling.Timing.ParentTimingId">
            <summary>
            The Unique Identifier identifying the parent timing of this Timing. Used for sql server storage.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Timing.DurationWithoutChildrenMilliseconds">
            <summary>
            Gets the elapsed milliseconds in this step without any children's durations.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Timing.IsTrivial">
            <summary>
            Gets a value indicating whether this <see cref="P:StackExchange.Profiling.Timing.DurationWithoutChildrenMilliseconds"/> is less than the configured
            <see cref="P:StackExchange.Profiling.MiniProfiler.Settings.TrivialDurationThresholdMilliseconds"/>, by default 2.0 ms.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Timing.HasChildren">
            <summary>
            Gets a value indicating whether this Timing has inner Timing steps.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Timing.IsRoot">
            <summary>
            Gets a value indicating whether this Timing is the first one created in a MiniProfiler session.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Timing.Depth">
            <summary>
            Gets a value indicating whether how far away this Timing is from the Profiler's Root.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Timing.Profiler">
            <summary>
            Gets a reference to the containing profiler, allowing this Timing to affect the Head and get Stopwatch readings.
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.Timing.MiniProfilerId">
            <summary>
            The unique identifier used to identify the Profiler with which this Timing is associated. Used for sql storage.
            </summary>
        </member>
        <member name="T:StackExchange.Profiling.WebRequestProfilerProvider">
            <summary>
            HttpContext based profiler provider.  This is the default provider to use in a web context.
            The current profiler is associated with a HttpContext.Current ensuring that profilers are 
            specific to a individual HttpRequest.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.WebRequestProfilerProvider.#ctor">
            <summary>
            Initialises a new instance of the <see cref="T:StackExchange.Profiling.WebRequestProfilerProvider"/> class. 
            Public constructor.  This also registers any UI routes needed to display results
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.WebRequestProfilerProvider.Start(System.String)">
            <summary>
            Starts a new MiniProfiler and associates it with the current <see cref="P:System.Web.HttpContext.Current"/>.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.WebRequestProfilerProvider.Start(StackExchange.Profiling.ProfileLevel,System.String)">
            <summary>
            Starts a new MiniProfiler and associates it with the current <see cref="P:System.Web.HttpContext.Current"/>.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.WebRequestProfilerProvider.Stop(System.Boolean)">
            <summary>
            Ends the current profiling session, if one exists.
            </summary>
            <param name="discardResults">
            When true, clears the <see cref="P:StackExchange.Profiling.MiniProfiler.Current"/> for this HttpContext, allowing profiling to 
            be prematurely stopped and discarded. Useful for when a specific route does not need to be profiled.
            </param>
        </member>
        <member name="M:StackExchange.Profiling.WebRequestProfilerProvider.EnsureName(StackExchange.Profiling.MiniProfiler,System.Web.HttpRequest)">
            <summary>
            Makes sure 'profiler' has a Name, pulling it from route data or url.
            </summary>
        </member>
        <member name="M:StackExchange.Profiling.WebRequestProfilerProvider.GetCurrentProfiler">
            <summary>
            Returns the current profiler
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.WebRequestProfilerProvider.Current">
            <summary>
            Gets the currently running MiniProfiler for the current HttpContext; null if no MiniProfiler was <see cref="M:StackExchange.Profiling.WebRequestProfilerProvider.Start(System.String)"/>ed.
            </summary>
        </member>
        <member name="T:StackExchange.Profiling.WebRequestProfilerProvider.Settings">
            <summary>
            WebRequestProfilerProvider specific configurations
            </summary>
        </member>
        <member name="P:StackExchange.Profiling.WebRequestProfilerProvider.Settings.UserProvider">
            <summary>
            Provides user identification for a given profiling request.
            </summary>
        </member>
    </members>
</doc>
