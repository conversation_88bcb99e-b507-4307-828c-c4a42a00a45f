﻿/**
 * @license Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
 * For licensing, see LICENSE.md or http://ckeditor.com/license
 */

CKEDITOR.plugins.setLang( 'specialchar', 'vi', {
	euro: '<PERSON>ý hiệu Euro',
	lsquo: 'Dấu ngoặc đơn trái',
	rsquo: 'Dấu ngoặc đơn phải',
	ldquo: 'Dấu ngoặc đôi trái',
	rdquo: 'Dấu ngoặc đôi phải',
	ndash: 'Gạch ngang tiếng anh',
	mdash: 'Gạch ngang Em',
	iexcl: 'Chuyển đổi dấu chấm than',
	cent: '<PERSON>ý tự tiền Mỹ',
	pound: '<PERSON><PERSON> tự tiền Anh',
	curren: 'Ký tự tiền tệ',
	yen: '<PERSON><PERSON> tự tiền Yên Nhật',
	brvbar: 'Thanh hỏng',
	sect: '<PERSON><PERSON> tự khu vực',
	uml: '<PERSON><PERSON><PERSON> tách đôi',
	copy: '<PERSON><PERSON> tự bản quyền',
	ordf: '<PERSON>ần chỉ thị giống cái',
	laquo: '<PERSON>ọn dấu ngoặc đôi trái',
	not: 'Không có ký tự',
	reg: 'Ký tự đăng ký',
	macr: 'Dấu nguyên âm dài',
	deg: 'Ký tự độ',
	sup2: 'Chữ trồi lên trên dạng 2',
	sup3: 'Chữ trồi lên trên dạng 3',
	acute: 'Dấu trọng âm',
	micro: 'Ký tự micro',
	para: 'Ký tự đoạn văn',
	middot: 'Dấu chấm tròn',
	cedil: 'Dấu móc lưới',
	sup1: 'Ký tự trồi lên cấp 1',
	ordm: 'Ký tự biểu hiện giống đực',
	raquo: 'Chọn dấu ngoặc đôi phải',
	frac14: 'Tỉ lệ một phần tư',
	frac12: 'Tỉ lệ một nửa',
	frac34: 'Tỉ lệ ba phần tư',
	iquest: 'Chuyển đổi dấu chấm hỏi',
	Agrave: 'Ký tự la-tinh viết hoa A với dấu huyền',
	Aacute: 'Ký tự la-tinh viết hoa A với dấu sắc',
	Acirc: 'Ký tự la-tinh viết hoa A với dấu mũ',
	Atilde: 'Ký tự la-tinh viết hoa A với dấu ngã',
	Auml: 'Ký tự la-tinh viết hoa A với dấu hai chấm trên đầu',
	Aring: 'Ký tự la-tinh viết hoa A với biểu tượng vòng tròn trên đầu',
	AElig: 'Ký tự la-tinh viết hoa của Æ',
	Ccedil: 'Ký tự la-tinh viết hoa C với dấu móc bên dưới',
	Egrave: 'Ký tự la-tinh viết hoa E với dấu huyền',
	Eacute: 'Ký tự la-tinh viết hoa E với dấu sắc',
	Ecirc: 'Ký tự la-tinh viết hoa E với dấu mũ',
	Euml: 'Ký tự la-tinh viết hoa E với dấu hai chấm trên đầu',
	Igrave: 'Ký tự la-tinh viết hoa I với dấu huyền',
	Iacute: 'Ký tự la-tinh viết hoa I với dấu sắc',
	Icirc: 'Ký tự la-tinh viết hoa I với dấu mũ',
	Iuml: 'Ký tự la-tinh viết hoa I với dấu hai chấm trên đầu',
	ETH: 'Viết hoa của ký tự Eth',
	Ntilde: 'Ký tự la-tinh viết hoa N với dấu ngã',
	Ograve: 'Ký tự la-tinh viết hoa O với dấu huyền',
	Oacute: 'Ký tự la-tinh viết hoa O với dấu sắc',
	Ocirc: 'Ký tự la-tinh viết hoa O với dấu mũ',
	Otilde: 'Ký tự la-tinh viết hoa O với dấu ngã',
	Ouml: 'Ký tự la-tinh viết hoa O với dấu hai chấm trên đầu',
	times: 'Ký tự phép toán nhân',
	Oslash: 'Ký tự la-tinh viết hoa A với dấu ngã xuống',
	Ugrave: 'Ký tự la-tinh viết hoa U với dấu huyền',
	Uacute: 'Ký tự la-tinh viết hoa U với dấu sắc',
	Ucirc: 'Ký tự la-tinh viết hoa U với dấu mũ',
	Uuml: 'Ký tự la-tinh viết hoa U với dấu hai chấm trên đầu',
	Yacute: 'Ký tự la-tinh viết hoa Y với dấu sắc',
	THORN: 'Phần viết hoa của ký tự Thorn',
	szlig: 'Ký tự viết nhỏ la-tinh của chữ s',
	agrave: 'Ký tự la-tinh thường với dấu huyền',
	aacute: 'Ký tự la-tinh thường với dấu sắc',
	acirc: 'Ký tự la-tinh thường với dấu mũ',
	atilde: 'Ký tự la-tinh thường với dấu ngã',
	auml: 'Ký tự la-tinh thường với dấu hai chấm trên đầu',
	aring: 'Ký tự la-tinh viết thường với biểu tượng vòng tròn trên đầu',
	aelig: 'Ký tự la-tinh viết thường của æ',
	ccedil: 'Ký tự la-tinh viết thường của c với dấu móc bên dưới',
	egrave: 'Ký tự la-tinh viết thường e với dấu huyền',
	eacute: 'Ký tự la-tinh viết thường e với dấu sắc',
	ecirc: 'Ký tự la-tinh viết thường e với dấu mũ',
	euml: 'Ký tự la-tinh viết thường e với dấu hai chấm trên đầu',
	igrave: 'Ký tự la-tinh viết thường i với dấu huyền',
	iacute: 'Ký tự la-tinh viết thường i với dấu sắc',
	icirc: 'Ký tự la-tinh viết thường i với dấu mũ',
	iuml: 'Ký tự la-tinh viết thường i với dấu hai chấm trên đầu',
	eth: 'Ký tự la-tinh viết thường của eth',
	ntilde: 'Ký tự la-tinh viết thường n với dấu ngã',
	ograve: 'Ký tự la-tinh viết thường o với dấu huyền',
	oacute: 'Ký tự la-tinh viết thường o với dấu sắc',
	ocirc: 'Ký tự la-tinh viết thường o với dấu mũ',
	otilde: 'Ký tự la-tinh viết thường o với dấu ngã',
	ouml: 'Ký tự la-tinh viết thường o với dấu hai chấm trên đầu',
	divide: 'Ký hiệu phép tính chia',
	oslash: 'Ký tự la-tinh viết thường o với dấu ngã',
	ugrave: 'Ký tự la-tinh viết thường u với dấu huyền',
	uacute: 'Ký tự la-tinh viết thường u với dấu sắc',
	ucirc: 'Ký tự la-tinh viết thường u với dấu mũ',
	uuml: 'Ký tự la-tinh viết thường u với dấu hai chấm trên đầu',
	yacute: 'Ký tự la-tinh viết thường y với dấu sắc',
	thorn: 'Ký tự la-tinh viết thường của chữ thorn',
	yuml: 'Ký tự la-tinh viết thường y với dấu hai chấm trên đầu',
	OElig: 'Ký tự la-tinh viết hoa gạch nối OE',
	oelig: 'Ký tự la-tinh viết thường gạch nối OE',
	'372': 'Ký tự la-tinh viết hoa W với dấu mũ',
	'374': 'Ký tự la-tinh viết hoa Y với dấu mũ',
	'373': 'Ký tự la-tinh viết thường w với dấu mũ',
	'375': 'Ký tự la-tinh viết thường y với dấu mũ',
	sbquo: 'Dấu ngoặc đơn thấp số-9',
	'8219': 'Dấu ngoặc đơn đảo ngược số-9',
	bdquo: 'Gấp đôi dấu ngoặc đơn số-9',
	hellip: 'Tĩnh dược chiều ngang',
	trade: 'Ký tự thương hiệu',
	'9658': 'Ký tự trỏ về hướng bên phải màu đen',
	bull: 'Ký hiệu',
	rarr: 'Mũi tên hướng bên phải',
	rArr: 'Mũi tên hướng bên phải dạng đôi',
	hArr: 'Mũi tên hướng bên trái dạng đôi',
	diams: 'Ký hiệu hình thoi',
	asymp: 'Gần bằng với'
} );
