/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/IntegralsD/Bold/All.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXIntegralsD-bold"],{32:[0,0,250,0,0],160:[0,0,250,0,0],8747:[2000,269,686,56,1136],8748:[2000,269,1084,56,1534],8749:[2000,269,1482,56,1932],8750:[2000,269,736,56,1136],8751:[2000,269,1134,56,1534],8752:[2000,269,1532,56,1932],8753:[2000,269,736,56,1136],8754:[2000,269,736,56,1136],8755:[2000,269,736,56,1136],10764:[2000,269,1880,56,2330],10765:[2000,269,736,56,1136],10766:[2000,269,736,56,1136],10767:[2000,269,736,56,1136],10768:[2000,269,736,56,1136],10769:[2000,269,736,56,1136],10770:[2000,269,836,56,1136],10771:[2000,269,736,56,1136],10772:[2000,269,926,56,1136],10773:[2000,269,736,56,1136],10774:[2000,269,836,56,1136],10775:[2000,269,911,24,1131],10776:[2000,269,736,56,1136],10777:[2000,269,836,56,1136],10778:[2000,269,836,56,1136],10779:[2182,269,746,56,1146],10780:[2000,451,696,56,1146]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/IntegralsD/Bold/All.js");
