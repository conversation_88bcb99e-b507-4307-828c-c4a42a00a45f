﻿@{
    ViewBag.Title = "設定結果";
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
}

<link href='~/Content/css/EzCss.css' rel='stylesheet' />
<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>

<table style="width:100%">
    <tr>
        <td align="center" valign="middle">
            <h4>@ViewBag.Msg</h4>
            <input type="button" id="btnColorboxClose" value="關閉" class="btn-primary btn btn-sm" onclick="ColorboxClose();" />
        </td>
    </tr>
</table>
<script type="text/javascript">
    function ColorboxClose() {
        window.parent.$.colorbox.close();
        window.parent.FunPageProc(1);
    }
</script>