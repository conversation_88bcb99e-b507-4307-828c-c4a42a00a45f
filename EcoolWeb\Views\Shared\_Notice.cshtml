﻿@if (TempData["StatusMessage"] != null)
{
    string HtmlMsg = TempData["StatusMessage"].ToString().Replace("\r\n", "<br />");

    <div class="alert alert-dismissible alert-danger" id="StatusMessageDiv">
        @*<img src="~/Content/img/Warning.png" style="width:30px;height:30px" />*@
        <button class="close" type="button" data-dismiss="alert">×</button>
        <strong class="h4" id="StatusMessageHtmlMsg">@Html.Raw(HtmlMsg)</strong>
    </div>

  

}
