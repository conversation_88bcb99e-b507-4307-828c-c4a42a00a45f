﻿@model ZZZI09ShowCERT05ViewModel
@using ECOOL_APP.com.ecool.util;

@if (TempData["StatusMessage"] != null)
{
    @Html.Partial("_Notice")
}
else
{
    string ZZZIcss = "";
    if ((!string.IsNullOrEmpty(Model.fromWhere) && Model.ListData?.Count() == 0) || !string.IsNullOrEmpty(Model.fromWhere) && Model.ListData == null)
    {
        ZZZIcss = "hidden";
    }


    if (Model.ListData != null && Model.ListData.Count() != 0)
    {
        if (Model.ListData.Count() > 0)
        {

            <section class="row px-3 ePassport">
                <div class="col-lg-12 ">
                    <h2 class="heading-h2">我的護照</h2>
                    <div class="row mt-3">

                        <div class="col-lg-12">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th scope="col" width=15%>@Html.DisplayNameFor(m => m.ListData.First().TYPE_NAME)</th>
                                        <th scope="col" width=24%>@Html.DisplayNameFor(m => m.ListData.First().ACCREDITATION_NAME)</th>
                                        <th scope="col" width=24%>@Html.DisplayNameFor(m => m.ListData.First().SUBJECT)</th>
                                        <th scope="col">預計完成時間</th>
                                        <th scope="col">@Html.DisplayNameFor(m => m.ListData.First().VERIFIER)</th>
                                        <th scope="col">通過</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @if (Model.ListData?.Count() > 0)
                                    {
                                        foreach (var item in Model.ListData)
                                        {
                                            <tr>
                                                <td>@Html.DisplayFor(modelItem => item.TYPE_NAME)</td>
                                                <td>@Html.DisplayFor(modelItem => item.ACCREDITATION_NAME)</td>
                                                <td>
                                                    <span data-html="true" data-toggle="tooltip" data-placement="right" title='@(item.CONTENT?.Replace("\n", "<br />"))'>
                                                        @Html.DisplayFor(modelItem => item.SUBJECT)
                                                    </span>
                                                </td>
                                                <td>
                                                    @if (item.GRADE_SEMESTERs?.Count > 0)
                                                    {
                                                        if (item.GRADE_SEMESTERs.Count() >= 12)
                                                        {
                                                            <span>不分年級</span>
                                                        }
                                                        else
                                                        {
                                                            var GRADE_SEMESTERs = string.Join(",", item.GRADE_SEMESTERs.OrderBy(a => a).ToList());

                                                            <span data-html="true" data-toggle="tooltip" data-placement="right" title="@(GRADE_SEMESTERs.Replace(",", "<br />"))">
                                                                @StringHelper.LeftStringR(GRADE_SEMESTERs, 7)
                                                            </span>
                                                        }
                                                    }
                                                </td>
                                                <td>
                                                    @if (item.IS_PASS ?? false)
                                                    {
                                                        @Html.DisplayFor(modelItem => item.VERIFIER)
                                                    }
                                                </td>
                                                <td>
                                                    @if (item.IS_PASS ?? false)
                                                    {
                                                        <i class="glyphicon glyphicon-ok"></i>
                                                    }
                                                </td>
                                            </tr>
                                        }
                                    }
                                </tbody>

                            </table>
                            @if ((Model.ListData?.Count() ?? 0) == 0)
                            {
                                <div class="text-center" style="padding:10px">
                                    <strong>目前無資料</strong>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </section>

        }
    }
    @*<div class="panel panel-ZZZ" name="TOP" @ZZZIcss>
            <div class="panel-heading text-center">
                我的護照
            </div>
            <div class="table-responsive">
                <table class="table-ecool table-92Per table-hover table-ecool-ZZZ">
                    <thead class="bg-primary-dark text-white">
                        <tr class="thead-primary">
                            <th class="text-nowrap">@Html.DisplayNameFor(m => m.ListData.First().TYPE_NAME)</th>
                            <th class="text-nowrap">@Html.DisplayNameFor(m => m.ListData.First().ACCREDITATION_NAME)</th>
                            <th class="text-nowrap">@Html.DisplayNameFor(m => m.ListData.First().SUBJECT)</th>
                            <th class="text-nowrap">預計完成時間</th>
                            <th class="text-nowrap">@Html.DisplayNameFor(m => m.ListData.First().VERIFIER)</th>
                            <th class="text-nowrap">通過</th>
                        </tr>
                    </thead>
                    <tbody>
                        @if (Model.ListData?.Count() > 0)
                        {
                            foreach (var item in Model.ListData)
                            {
                                <tr class="text-center">
                                    <td>@Html.DisplayFor(modelItem => item.TYPE_NAME)</td>
                                    <td>@Html.DisplayFor(modelItem => item.ACCREDITATION_NAME)</td>

                                    <td>
                                        <span data-html="true" data-toggle="tooltip" data-placement="right" title='@(item.CONTENT?.Replace("\n","<br />"))'>
                                            @Html.DisplayFor(modelItem => item.SUBJECT)
                                        </span>
                                    </td>
                                    <td>
                                        @if (item.GRADE_SEMESTERs?.Count > 0)
                                        {
                                            if (item.GRADE_SEMESTERs.Count() >= 12)
                                            {
                                                <span>不分年級</span>
                                            }
                                            else
                                            {
                                                var GRADE_SEMESTERs = string.Join(",", item.GRADE_SEMESTERs.OrderBy(a => a).ToList());

                                                <span data-html="true" data-toggle="tooltip" data-placement="right" title="@(GRADE_SEMESTERs.Replace(",","<br />"))">
                                                    @StringHelper.LeftStringR(GRADE_SEMESTERs, 7)
                                                </span>
                                            }
                                        }
                                    </td>
                                    <td>
                                        @if (item.IS_PASS ?? false)
                                        {
                                            @Html.DisplayFor(modelItem => item.VERIFIER)
                                        }
                                    </td>
                                    <td>
                                        @if (item.IS_PASS ?? false)
                                        {
                                            <i class="glyphicon glyphicon-ok"></i>
                                        }
                                    </td>
                                </tr>
                            }
                        }
                    </tbody>
                </table>
                @if ((Model.ListData?.Count() ?? 0) == 0)
                {
                    <div class="text-center" style="padding:10px"><strong>目前無資料</strong></div>
                }
            </div>
        </div>*@

    @*if (Model.ListData?.Count() > 0)
        {
            foreach (var item in Model.ListData)
            {
                <div class="Div-EZ-Pink" style="margin-bottom:15px">
                    <div class="Details">
                        <div class="row">
                            <div class="col-md-12 col-sm-12  dl-horizontal">
                                <samp class="dt" style="font-size:0.6cm;">
                                    護照名稱
                                </samp>
                                <samp class="dd" style="font-size:0.5cm;">

                                    <span class="control-label">@item.TYPE_NAME</span>
                                </samp>
                            </div>
                            <div class="col-md-12 col-sm-12  dl-horizontal">
                                <samp class="dt" style="font-size:0.6cm;">
                                    護照細項名稱
                                </samp>
                                <samp class="dd" style="font-size:0.5cm;">
                                    @item.ACCREDITATION_NAME
                                </samp>
                            </div>
                            <div class="col-md-12 col-sm-12  dl-horizontal">
                                <samp class="dt" style="font-size:0.6cm;">
                                    通過主旨
                                </samp>
                                <samp class="dd" style="font-size:0.5cm;">
                                    @item.SUBJECT
                                </samp>
                            </div>
                            <div class="col-md-12 col-sm-12  dl-horizontal">
                                <samp class="dt" style="font-size:0.6cm;">
                                    通過內容
                                </samp>
                                <samp class="dd" style="font-size:0.5cm;">
                                    @item.CONTENT
                                </samp>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 col-sm-6  dl-horizontal">
                                <samp class="dt" style="font-size:0.6cm;">
                                    是否通過
                                </samp>

                                <samp class="dd" style="font-size:0.5cm;">
                                    @if ((item.IS_PASS ?? false))
                                    {
                                        <span>通過</span>
                                    }
                                    else
                                    {
                                        if (string.IsNullOrWhiteSpace(item.VERIFIER))
                                        {
                                            <span>未認證</span>
                                        }
                                        else
                                        {
                                            <span>未通過</span>
                                        }

                                    }
                                </samp>

                                @if (item.IS_PASS != null)
                                {
                                    <samp class="dt" style="font-size:0.6cm;">
                                        認證老師/認證時間
                                    </samp>

                                    <samp class="dd" style="font-size:0.5cm;">
                                        @item.VERIFIER
                                    </samp>
                                }
                            </div>
                        </div>
                        <div style="height:15px"></div>
                    </div>
                    <div class="col-md-12">
                        <div class="p-context">
                        </div>
                    </div>
                    <div style="height:1px;background-color:palevioletred" ;=""></div>
                </div>
            }
        }*@

    <script>
                                                        //$(function () {
                                                        //    $('[data-toggle="tooltip"]').tooltip()
                                                        //})
    </script>
}
