﻿@{
    ViewBag.Title = "閱讀護照-說明";
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

    string ddlCLASS_NO = string.Empty;
    string ddlGrade = string.Empty;
    string whereKeyword = string.Empty;

    if (user != null)
    {
        ddlCLASS_NO = user.TEACH_CLASS_NO ?? user.CLASS_NO;
        ddlGrade = string.IsNullOrWhiteSpace(ddlCLASS_NO) == false ? new ECOOL_APP.com.ecool.util.StringHelper().StrLeft(ddlCLASS_NO, 1) : "";

        if (user.USER_TYPE == UserType.Student)
        {
            whereKeyword = user.USER_NO;
        }
    }

    int WhereGRADE = string.IsNullOrWhiteSpace(ddlGrade) == false ? Convert.ToInt32(ddlGrade) : 1;

}

@if (AppMode == false)
{
    @Html.Partial("_Title_Secondary")
}

<div class="form-group">

    <a role="button" href='@Url.Action("rpp", "rpp")' class="btn btn-sm btn-sys active">
        護照說明
    </a>

    <a href='@Url.Action("rpp_book", "rpp")' role="button" class="btn btn-sm btn-sys">
        閱讀書單
    </a>

    @if (string.IsNullOrWhiteSpace(ddlCLASS_NO) == false)
    {
        @Html.ActionLink("本學年護照", "Query3", "ADDI03", new { GRADE = WhereGRADE, QShowYN = "N" }, new { @role = "button", @class = "btn btn-sm btn-sys" })
    }
        @Html.ActionLink("護照現況一覽表", "Query3", "ADDI03", new { GRADE = WhereGRADE, whereKeyword = whereKeyword }, new { @role = "button", @class = "btn btn-sm btn-sys" })
   

    <a href='@Url.Action("Query2", "ADDI03",new { ddlGrade= ddlGrade, ddlCLASS_NO= ddlCLASS_NO, whereKeyword= whereKeyword  })' role="button" class="btn btn-sm btn-sys">
        護照完成一覽表
    </a>
    @if (user != null)
    {
        if (user.USER_TYPE == UserType.Student)
        {
            <a role="button" href='@Url.Action("Query4", "ADDI03")' class="btn btn-sm btn-sys">
                我的護照
            </a>
        }
        else if (user.USER_TYPE == UserType.Parents)
        {
            <a role="button" href='@Url.Action("Query4", "ADDI03")' class="btn btn-sm btn-sys">
                寶貝護照
            </a>
        }
    }
</div>
<img src="~/Content/img/web-bar2-revise-30.png" style="width:100%" class="img-responsive App_hide" alt="Responsive image" />
<div style="width:100%;max-width:600px">
    @{string Explain = ViewBag.BookExplain;}
    @Html.Raw(HttpUtility.HtmlDecode(@Explain))

</div>


