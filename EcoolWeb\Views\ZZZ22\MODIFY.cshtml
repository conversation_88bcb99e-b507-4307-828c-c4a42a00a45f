﻿@model ECOOL_APP.EF.HRMT04
@{
    ViewBag.Title = "總召基本資料維護-維護內容";
}
<script src="~/Content/ckeditor/ckeditor.js"></script>

@section Scripts
{
    <script type="text/javascript">
        function btnSend_onclick() {
            document.ZZZ22.enctype = "multipart/form-data";
            document.ZZZ22.action = "MODIFY";
            document.ZZZ22.submit();
        }
    </script>
}

@Html.Partial("_Title_Secondary")

@Html.Partial("_Notice")

@using (Html.BeginForm("MODIFY", "ZZZ22", FormMethod.Post, new { id = "ZZZ22", name = "<PERSON><PERSON>Z22" }))
{
    <div class="panel panel-ZZZ" name="TOP">
        <div class="panel-heading text-center">
            @Html.BarTitle()
        </div>
        <div class="panel-body">
            <div class="form-horizontal">
                <div class="form-group">
                    @Html.Label("總召名稱", htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-9">
                        @Html.HiddenFor(model => model.USER_ID)
                        @Html.EditorFor(model => model.USERNAME, new { htmlAttributes = new { @class = "form-control", @placeholder = "必填" } })
                    </div>
                </div>
                <div class="form-group">
                    @Html.Label("總召Mail", htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-9">
                        @Html.EditorFor(model => model.EMAIL, new { htmlAttributes = new { @class = "form-control" } })
                    </div>
                </div>
                <div class="form-group">
                    @Html.Label("總召電話", htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-9">
                        @Html.EditorFor(model => model.TEL, new { htmlAttributes = new { @class = "form-control" } })
                    </div>
                </div>
                <div class="form-group">
                    @Html.Label("酷幣宗旨", htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-9">
                        @Html.TextAreaFor(model => model.ECOOLEXPLAIN, 10, 100, new { @class = "ckeditor" })
                    </div>
                </div>
                <div class="text-center">
                    <button class="btn btn-default" type="button" id="btnSend" name="btnSend" onclick="btnSend_onclick();">
                        確定送出
                    </button>
                </div>
            </div>
        </div>
    </div>
  
}
