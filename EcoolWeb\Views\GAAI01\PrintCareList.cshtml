﻿@model GAAI0CareListIndexViewModel
@{
    ViewBag.Title = ViewBag.Panel_Title;

    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
}

<div style="width:90%;max-width:1024px;margin: 0px auto;">

    <div class="form-group row no-print">
        @Html.LabelFor(m => m.ALARM_NOTICE, htmlAttributes: new { @class = "col-md-3 control-label" })
        <div class="col-md-9">
            @Html.TextAreaFor(m => m.ALARM_NOTICE, new { cols = "200", rows = "15", @class = "form-control", @placeholder = "請勿超過250個字" })
        </div>
    </div>
    <div class="text-center no-print">
        <hr />
        <button type="button" class="btn btn-default" onclick="onUpNotice()">
            <span class="fa fa-check-circle" aria-hidden="true"></span>更新
        </button>
        <button type="button" class="btn btn-default" onclick="onPrint('#DivPrint')">
            <span class="fa fa-check-circle" aria-hidden="true"></span>列印
        </button>
    </div>

    <div id="DivPrint">
        <style>
            @@media print {
                html, body {
                    height: 100%;
                }
            }

            div.editable {
                border: 3px dashed #e3e6e9;
                margin-bottom: 5px;
                page-break-inside: avoid;
            }

                div.editable ul {
                    margin: 20px;
                    list-style: none;
                }

                    div.editable ul li {
                        padding: 0px 10px;
                        font-family: Roboto,Arial,sans-serif;
                        font-size: 15px;
                        line-height: 32px;
                    }

                        div.editable ul li:nth-child(1n+2) {
                            border-top: 1px solid #e3e6e9;
                        }

                        div.editable ul li:focus {
                            background-color: #ffffcc;
                        }

            div.status {
                margin: 0px 0px 10px 0px;
                padding: 10px 0px 10px 10px;
                font-family: Roboto,Arial,sans-serif;
            }

                div.status span {
                    width: 50%;
                    text-align: center;
                    display: inline-block;
                }

                    div.status span.status-event {
                        color: #0095dd;
                    }

                    div.status span.status-editor {
                        color: red;
                    }

            div.status-content {
                margin: 20px 0px 50px 0px;
                padding: 20px 0px;
                font-family: Roboto,Arial,sans-serif;
                font-size: 15px;
                color: #dd3300;
                text-align: center;
                display: none;
                background-color: #ffffcc;
                border: 1px solid #dd3300;
            }

            div.rwd {
                margin: 3px 0px 0px 3px;
                width: 100%;
                max-width: 850px;
                box-sizing: border-box;
            }

            div.editable ul {
                margin: 10px;
            }
        </style>
        @if (Model.DivId == "CareFoDateClassTable")
        {

            foreach (var User in Model.WearDetailList)
            {

                <div class="row">
                    <div class="col-xl-12 no-print">
                        @(User.CLASS_NO)班 @User.NAME @User.SEAT_NO
                    </div>
                    <div class="col-xl-12 editable">
                        <ul>
                            <li contenteditable="true">
                                親愛的 @User.NAME 家長您好，貴子弟
                                <text class="textareaText">@Model.ALARM_NOTICE</text>
                            </li>
                        </ul>
                    </div>
                </div>
            }

        }
        else if (Model.DivId == "CareFoUserTable")
        {
            foreach (var User in Model.CareFoUser.Where(x => x.Checked == true).OrderBy(x => x.CLASS_NO).ThenBy(x => x.SEAT_NO))
            {
                <div class="row">
                    <div class="col-xl-12 no-print">
                        @(User.CLASS_NO)班 @User.NAME @User.SEAT_NO
                    </div>
                    <div class="col-xl-12 editable">
                        <ul>
                            <li contenteditable="true">
                                親愛的 @User.NAME 家長您好，貴子弟
                                <text class="textareaText">@Model.ALARM_NOTICE</text>
                            </li>
                        </ul>
                    </div>
                </div>
            }
        }
    </div>
</div>
@section Scripts {
    <script src="~/Content/ckeditor/ckeditor.js"></script>
    <script src="~/Scripts/printThis/printThis.js"></script>
    <script type="text/javascript">

    CKEDITOR.disableAutoInline = true;
    CKEDITOR.replace("@Html.NameFor(m=>m.ALARM_NOTICE)");

    function onUpNotice() {

        var editorText = CKEDITOR.instances['@Html.IdFor(m=>m.ALARM_NOTICE)'].getData();

        $(".textareaText").each(function () {
                 $(this).html(editorText)
         });
     }

        function onPrint(DivId) {

         //   var DivHeight = ($(".editable").first().outerHeight()) + 5
         //   $(".page-break-before").remove();

         //var nowheight = 0;

         //   $(".editable").each(function (index) {

         //    nowheight = nowheight + DivHeight

         //   if (nowheight >= 845) {
         //       $(this).after('<p class="page-break-before" style="page-break-before: always "></p>');
         //       $(this).after('<br>');
         //       nowheight = 0;
         //       }

         //});

         $(DivId).printThis();
    }
    </script>
}