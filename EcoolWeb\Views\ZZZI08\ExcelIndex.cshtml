﻿@model ECOOL_APP.com.ecool.Models.DTO.ZZZI08ListViewModel
@{
    ViewBag.Title = ViewBag.Panel_Title;
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@Html.PermissionButton("回「新增」", "button", (string)ViewBag.BRE_NO, "Save", new { @class = "btn btn-sm btn-sys", @onclick = "onAdd()" }, 2, ViewBag.BtnPermission as List<ECOOL_APP.ControllerPermissionfile>)

@using (Html.BeginForm("ExcelIndex", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", enctype = "multipart/form-data" }))
{
    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })
    @Html.HiddenFor(model => model.SearchType)
    @Html.HiddenFor(model => model.VIEW_DATA_TYPE)
    @Html.HiddenFor(model => model.OrderByName)
    @Html.HiddenFor(model => model.SyntaxName)
    @Html.HiddenFor(model => model.Page)
    @Html.HiddenFor(model => model.Q_COPY_YN)
    @Html.HiddenFor(model => model.Q_SCHOOL_NO)
    @Html.HiddenFor(model => model.SearchContents)


    <img src="~/Content/img/web-bar2-revise-07.png" style="width:100%" class="img-responsive " alt="Responsive image" />
    <div class="Div-EZ-ADDI05">
        <div class="form-horizontal">
            <label class="control-label">@ViewBag.Panel_Title</label>
            <br /><br />
            <div class="form-group">
                <label class="control-label col-md-3" for="QUESTIONS_TXT">上傳Excel檔</label>
                <div class="col-md-9">
                    <input class="btn btn-default" type="file" name="files" placeholder="必填" />
                    @Html.ValidationMessage("files", "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="form-group text-center">
                <button class="btn btn-default">上 傳 Excel </button>
            </div>
            <div class="form-group">
                <div class="col-md-offset-1 col-md-9">
                    <label class="text-danger">
                        上傳說明:<br />
                        1.上傳之 Excel 檔, 請依規定格式填寫(<a href="~/Content/ExcelSample/ZZZI08AddSample.xlsx" target="_blank" class="btn-table-link">下載 Sample</a>)<br/>
                        2.檔名不要是中文、特殊符號之類
                    </label>
                </div>
            </div>

        </div>
    </div>
    <script type="text/javascript">
        function onAdd() {

            $('#VIEW_DATA_TYPE').val("@ViewBag.VIEW_A")
            form1.action = '@Url.Action("Edit", (string)ViewBag.BRE_NO)';
            form1.submit();

        }
    </script>

}


