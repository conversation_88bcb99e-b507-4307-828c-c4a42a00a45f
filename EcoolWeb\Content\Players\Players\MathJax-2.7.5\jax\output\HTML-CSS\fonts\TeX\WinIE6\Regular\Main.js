/*
 *  /MathJax/jax/output/HTML-CSS/fonts/TeX/WinIE6/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.MathJax_WinIE6={directory:"WinIE6/Regular",family:"MathJax_WinIE6",testString:"\uE247 \uE257 \uE2CF",skew:{57869:0.0833,57933:0.0958},Ranges:[[57920,57983,"Bold"],[58048,58079,"AMS"]],32:[0,0,250,0,0],160:[0,0,250,0,0],57856:[511,11,1000,55,944],57857:[694,193,500,17,483],57858:[511,11,1000,56,944],57859:[694,194,500,17,483],57860:[511,11,1000,55,944],57861:[772,272,500,17,483],57862:[720,195,1000,29,944],57863:[720,195,1000,55,970],57864:[695,220,1000,55,970],57865:[695,220,1000,29,944],57866:[525,24,1000,56,944],57867:[526,25,1000,34,966],57868:[694,22,556,0,556],57869:[715,22,531,42,566],57870:[694,0,556,56,500],57871:[683,33,833,46,786],57872:[540,40,667,84,583],57873:[540,40,667,83,582],57874:[750,250,500,56,445],57875:[800,200,833,72,853],57876:[442,11,778,56,722],57877:[442,11,1000,55,944],57878:[694,0,722,55,666],57879:[750,249,278,119,159],57880:[750,250,500,132,367],57881:[598,22,667,55,611],57882:[598,22,667,55,611],57883:[598,22,667,55,611],57884:[598,22,667,55,611],57885:[716,216,417,55,472],57886:[367,-133,778,55,722],57887:[483,-55,778,55,722],57888:[716,215,778,56,722],57889:[464,-36,778,56,722],57890:[636,138,778,83,694],57891:[636,138,778,83,694],57892:[568,67,1000,56,944],57893:[567,67,1000,55,944],57894:[540,40,778,84,694],57895:[540,40,778,83,693],57896:[636,138,778,84,694],57897:[636,138,778,83,693],57898:[583,83,778,56,722],57899:[583,83,778,56,722],57900:[668,0,778,55,723],57901:[716,0,889,59,828],57902:[500,215,889,59,828],57903:[715,215,1000,56,944],57904:[727,130,778,55,723],57905:[716,33,778,55,723],57906:[727,162,778,55,723],57907:[726,130,778,28,750],57908:[750,22,389,55,332],57909:[734,223,389,65,324],57910:[723,223,389,55,333],57984:[0,1000,944,55,888],57985:[0,1000,1056,56,999],57986:[40,1160,1000,111,1020],57987:[21,621,333,145,188],57988:[21,621,556,145,410],57989:[0,1111,472,55,610],57990:[0,1111,472,55,610],57991:[0,600,667,112,555],57992:[0,600,667,112,555],57993:[1,601,667,312,355],58000:[0,1400,1278,56,1221],58001:[0,1400,1444,55,1388],58002:[40,1760,1000,111,1020],58005:[0,2222,556,55,944],58006:[0,2222,556,55,944],58018:[40,2360,1000,111,1020],58034:[40,2960,1000,111,1020]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"MathJax_WinIE6"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/WinIE6/Regular/Main.js"]);
