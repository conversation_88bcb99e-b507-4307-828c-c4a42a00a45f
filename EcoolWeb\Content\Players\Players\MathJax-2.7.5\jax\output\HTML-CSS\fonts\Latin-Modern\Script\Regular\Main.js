/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Latin-Modern/Script/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.LatinModernMathJax_Script={directory:"Script/Regular",family:"LatinModernMathJax_Script",testString:"\u00A0\u210B\u2110\u2112\u211B\u212C\u2130\u2131\u2133\uD835\uDC9C\uD835\uDC9E\uD835\uDC9F\uD835\uDCA2\uD835\uDCA5\uD835\uDCA6",32:[0,0,332,0,0],160:[0,0,332,0,0],8459:[690,8,1065,40,1024],8464:[685,14,620,40,580],8466:[685,7,770,40,730],8475:[712,6,818,40,778],8492:[712,1,778,40,737],8496:[702,12,613,40,573],8497:[699,15,904,40,865],8499:[699,13,1149,40,1109],119964:[694,15,857,39,817],119966:[697,15,654,39,614],119967:[716,-4,871,40,831],119970:[697,130,685,39,644],119973:[692,129,698,40,658],119974:[690,12,989,40,949],119977:[706,7,1007,40,967],119978:[686,18,699,40,659],119979:[710,11,763,40,723],119980:[694,24,716,40,676],119982:[702,12,625,40,585],119983:[693,6,776,39,736],119984:[699,16,744,41,704],119985:[709,9,710,40,669],119986:[702,5,1028,40,988],119987:[706,9,870,39,829],119988:[702,136,628,40,588],119989:[696,11,726,41,687],120016:[711,17,969,40,930],120017:[727,1,916,40,876],120018:[709,15,745,40,705],120019:[727,1,1007,40,966],120020:[708,12,705,40,665],120021:[731,14,1005,40,966],120022:[705,138,790,39,749],120023:[699,12,1191,39,1150],120024:[703,18,715,39,675],120025:[701,137,771,40,732],120026:[709,9,1099,39,1058],120027:[710,12,861,40,822],120028:[710,17,1284,40,1244],120029:[712,13,1095,40,1054],120030:[707,20,822,40,781],120031:[726,13,880,40,840],120032:[705,42,839,41,799],120033:[732,12,923,40,882],120034:[715,18,722,40,682],120035:[697,11,910,39,870],120036:[709,13,853,39,813],120037:[702,16,784,40,744],120038:[710,8,1150,40,1110],120039:[712,11,970,40,930],120040:[709,135,738,39,698],120041:[705,14,800,40,760]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"LatinModernMathJax_Script"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Script/Regular/Main.js"]);
