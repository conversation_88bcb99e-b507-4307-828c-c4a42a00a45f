.list-icons-box {
  position: relative;
  padding: 2rem 1rem 1rem 1rem;
  border: 1px solid #eee;
}
.list-icons-box li {
  padding: 0.5rem 0 0.5rem 35px;
}
.list-icons-box li img {
  margin-left: -35px;
  margin-right: 5px;
}
.list-icons-box::before {
  content: attr(data-title);
  position: absolute;
  top: -1rem;
  display: block;
  padding: 0 1rem;
  background-color: #fff;
  font-size: 1.7rem;
}

.redeem-block {
  padding: 1rem 2rem;
  display: block;
  width: 90%;
  background-color: rgba(243, 200, 176, 0.2);
  margin: 0px auto;
}
.redeem-block-havetitle {
  margin-top: -30px;
  padding-top: calc(30px + 1rem);
}

.redeem-box {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  margin: 2rem 0 1rem 0;
  background-color: #fff;
}
.redeem-box img {
  display: block;
  width: 100%;
  max-width: 200px;
  max-height: 200px;
  margin: 0 auto;
}
.redeem-box-bidding {
  position: relative;
  background-color: #ffe0e0;
  border: 4px #f1c2a8 double;
  box-shadow: 0 0 6px #ab8a76;
}
.redeem-box-bidding::after {
  position: absolute;
  content: url("../images/icon-redeem-bidding.png");
  top: 3px;
  right: 3px;
  opacity: 0.5;
}

.redeem-row {
  display: flex;
  flex-wrap: wrap;
}
.redeem-row > div {
  flex: 0 1 50%;
}
@media screen and (max-width: 768px) {
  .redeem-row > div {
    flex: 1 1 100%;
  }
}/*# sourceMappingURL=redeem.css.map */