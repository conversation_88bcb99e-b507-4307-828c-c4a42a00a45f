﻿@model AWAI01IndexViewModel

@using ECOOL_APP.com.ecool.util;

@{
    ViewBag.Title = "ProductListForMobile";
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";

    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
}

<div class="pageBackGround">
    <div class="row titleRow" style="background-color:#F5C8AD">
        <label class="pull-left actionbarItem titleLable"><span class="glyphicon glyphicon-shopping-cart"></span> 兌換獎品 - 獎品一覽表</label>

        <button type="button" id="closefullBtn" class="btn btn-danger pull-right actionbarItem mobileHidden-xs"
                onclick="exitFullscreen();$(this).toggle(); $('#fullBtn').toggle();">
            <span class="glyphicon glyphicon-resize-small"></span>
        </button>
        <button type="button" id="fullBtn" class="btn btn-danger pull-right actionbarItem mobileHidden-xs"
                onclick="fullscreen(document.documentElement); $(this).toggle();$('#closefullBtn').toggle(); ">
            <span class="glyphicon glyphicon-resize-full" aria-hidden="true"></span>
        </button>

        @Html.DropDownListFor(m=>m.OrderType, new List<SelectListItem>()
           {
               new SelectListItem(){ Text ="酷幣點數由大到小", Value ="0" },

                new SelectListItem(){ Text ="酷幣點數由小到大", Value ="2" },
                       new SelectListItem(){ Text ="兌換期限由大到小", Value ="3" },
                           new SelectListItem(){ Text ="兌換期限由小到大", Value ="1" },
           }, new { id="orderSel", @class="orderSel pull-right", onchange="FormControl.changeOrder()" , @style = "width:230px" })
    </div>
    @{
        int pageSize = 6;
        foreach (var award in Model.ListData)
        {
            string aImgUrl = ViewBag.ImgUrl + award.SCHOOL_NO + @"/" + award.IMG_FILE; // 學生Img檔案目錄

            <div class="col-lg-4 col-md-4 col-xs-6 col-sm-6 prizebox">
                <div class="card">
                    <div class="mybox" style='@if (award.AWARD_TYPE == Awa.AWARD_TYPE_Val.AWARD_TYPE_B) { <TEXT>background-color:lightpink</TEXT> }'>
                        <img src="@aImgUrl" href="@aImgUrl" title="@award.AWARD_NAME" alt="商品圖片" class="center img-responsive">
                    </div>
                    <div>
                        <div class="center">
                            <div class="">
                                <label class="label_dd_font18" title="@award.AWARD_NAME">@StringHelper.LeftStringR(award.AWARD_NAME, 13)</label>
                            </div>
                            <div class="" style="margin-bottom:0px;">
                                <div class="prod-text">

                                    @if (award.AWARD_TYPE == Awa.AWARD_TYPE_Val.AWARD_TYPE_B)
                                    {
                                        <span>目前出價：@award.COST_CASH</span>

                                    }
                                    else
                                    {
                                        <span>兌換點數：<span style="color:red"><b>@award.COST_CASH</b></span></span>
                                    }
                                </div>

                                @if (award.AWARD_TYPE == Awa.AWARD_TYPE_Val.AWARD_TYPE_P || award.AWARD_TYPE == Awa.AWARD_TYPE_Val.AWARD_TYPE_C)
                                {
                                    <div class="prod-text">已募資點數：@(award.COST_CASH * award.QTY_TRANS)</div>
                                }
                                else
                                {
                                    <div class="prod-text">
                                        剩餘數量：@award.QTY_STORAGE
                                    </div>
                                }

                                @if (award.AWARD_TYPE == Awa.AWARD_TYPE_Val.AWARD_TYPE_B && (award.BID_BUY_PRICE ?? 0) > 0)
                                {
                                    <br />
                                    <div class="prod-text">
                                        直購點數：@award.BID_BUY_PRICE
                                    </div>
                                }

                                <br />
                                開始日期：@(award.SDATETIME.HasValue ? award.SDATETIME.Value.ToString("yyyy/MM/dd HH:mm") : "NA" )
                                <br />
                                兌換期限：@(award.EDATETIME.HasValue ? award.EDATETIME.Value.ToString("yyyy/MM/dd HH:mm") : "NA" )
                                <br />
                                <div class="text-center text-primary" data-toggle="tooltip" title="@award.DESCRIPTION">
                                    @if (award.SHOW_DESCRIPTION_YN == SharedGlobal.Y)
                                    {
                                        if (!string.IsNullOrWhiteSpace(award.DESCRIPTION))
                                        {
                                            int maxLen = 40;
                                            @(award.DESCRIPTION.Length > maxLen ? award.DESCRIPTION.Substring(0, maxLen) + "..." : award.DESCRIPTION);
                                        }

                                    }
                                    else
                                    {
                                        @Html.Raw("　<br>")
                                    }
                                </div>
                            </div>
                            <div class="prod-icon">
                                @if (award.SCHOOL_NO == SharedGlobal.ALL)
                                {
                                    <img src='~/Content/img/web-revise-prize-03s.png' style="max-height:30px;" />
                                }

                                @if (award.HOT_YN == SharedGlobal.Y)
                                {
                                    <img src='~/Content/img/icons-07.png' style="height:30px;width:30px;max-height:30px;max-width:30px">
                                }
                                @if (award.AWARD_TYPE == Awa.AWARD_TYPE_Val.AWARD_TYPE_B)
                                {
                                    <img src='~/Content/img/bid2.png' style="height:30px;width:30px;max-height:30px;max-width:30px">
                                }
                                @if (Convert.ToDateTime(award.SDATETIME).AddMonths(+1) >= DateTime.Now)
                                {
                                    <img src='~/Content/img/web-student-prize-05.png' style="height:30px;width:30px;max-height:30px;max-width:30px" />
                                }
                                @if (award.BUY_PERSON_YN == "Y")
                                {
                                    <img src="~/Content/img/web-student-prize-20.png" title="有限制對象" style="height:30px;width:30px;max-height:30px;max-width:30px" />
                                }
                                @if (award.READ_LEVEL != null)
                                {
                                    <img src="@Url.Content(EcoolWeb.Models.UserProfileHelper.GetImgReadUrl(award.READ_LEVEL))" style="max-height:30px;margin-right:5px">
                                }

                                @if (award.PASSPORT_LEVEL != null)
                                {
                                    <img src="@Url.Content(EcoolWeb.Models.UserProfileHelper.GetImgPassportUrl(award.PASSPORT_LEVEL))" style="max-height:30px;margin-right:5px">
                                }

                                <a class="btn-default btn btn-xs btn-prod" onclick="FormControl.exchangeList('@award.AWARD_NO')">
                                    @if (award.AWARD_TYPE == Awa.AWARD_TYPE_Val.AWARD_TYPE_B)
                                    {
                                        <span>得標名單</span>
                                    }
                                    else
                                    {
                                        <span>兌獎名單</span>
                                    }
                                </a>

                                <br />
                            </div>
                            <div>
                                @if (award.AWARD_TYPE != Awa.AWARD_TYPE_Val.AWARD_TYPE_B)
                                {
                                    if (award.QTY_STORAGE < 1)
                                    {<a class="btn-default btn btn-danger btn-block btn-prod" disabled role="button">庫存不足</a> }
                                    else
                                    { <a class="btn-default btn btn-success btn-block btn-prod" role="button" onclick="FormControl.funCARD('@award.AWARD_NO')">數位學生證感應兌換</a>}
                                }
                                else
                                {
                                    @Html.Raw("<br>");
                                }
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            pageSize--;
        }
        if (pageSize > 0)
        {
            for (int i = 0; i < pageSize; i++)
            {
                <div class="col-lg-4 col-md-4 col-xs-6 col-sm-6 prizebox">
                    <div class="card">
                        <div class="mybox">
                            <img src="~/Content/img/present-question-image-marks.jpg" href="~/Content/img/present-question-image-marks.jpg" alt="Avatar" class="center img-responsive">
                        </div>
                        <div>
                            <div class="center">
                                <div class="form-group">
                                    <label class="label_dd_font18" title="即將上架的神秘禮品">即將上架的神秘禮品</label>
                                </div>
                                <div class="form-group" style="margin-bottom:0px;">
                                    <div class="prod-text">
                                        <span>兌換點數：?</span>
                                    </div>
                                    <div class="prod-text">
                                        剩餘數量：?
                                    </div>
                                    <br>
                                    開始日期：?
                                    <br>
                                    兌換期限：?
                                    <br />
                                    <div>　</div>
                                </div>
                                <div class="prod-icon">
                                    <a class="btn-default btn btn-xs btn-prod" disabled>
                                        <span>兌獎名單</span>
                                    </a>
                                    <br>
                                </div>
                                <div>
                                    <a class="btn btn-danger btn-block btn-prod" role="button" disabled>即將開放</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            }
        }
    }

    @using (Html.BeginForm("", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "exchangeCardForm", name = "exchangeCardForm", @AutoComplete = "Off" }))
    {
        @Html.AntiForgeryToken()
        @Html.ValidationSummary(true, "", new { @class = "text-danger" })

        @Html.HiddenFor(m => m.OrderType)
        @Html.HiddenFor(m => m.Search.BackAction)
        @Html.HiddenFor(m => m.Search.BackController)

        @Html.HiddenFor(m => m.Search.WhereAWARD_NO)
        @Html.HiddenFor(m => m.Search.WhereSCHOOL_NO)
        @Html.HiddenFor(m => m.Search.WhereSouTable)
        @Html.Hidden("AfterClose", true)

    }
</div>

@section css{
    <link href="~/Content/colorbox/example4/colorbox.css" rel="stylesheet" />

    <style>
        @@media (min-width: 1367px) {
            html {
                font-size: 24px !important;
            }
        }

        @@media (max-width: 1366px) {
            .mobileHidden-xs {
                display: none !important;
            }

            .card {
                height: 480px !important;
            }
        }

        @@media (max-width: 1000px) {
            .card {
                height: 420px !important;
            }
        }

        @@media (max-width: 600px) {
            .prod-icon {
                display: none;
            }

            img {
                height: 70px !important;
            }

            .titleLable {
                font-size: 10px;
            }

            .actionbarItem {
                display: none;
            }

            .card {
                height: 230px !important;
            }

            label {
                font-size: 12px;
                margin-bottom: 0px;
            }

            .form-group {
                margin-bottom: 1px;
            }
        }

        html, body, pageBackGround {
            background-color: rgba(243, 200, 176, 0.2);
        }

        .titleRow {
            margin: 0px;
            box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2);
        }

        .actionbarItem {
            margin: 4px 1%;
        }

        .btn {
            border-radius: 0;
        }

        .btn-prod {
            margin-top: 2px;
        }

        .titleLable {
            font-size: 30px;
            color: white;
            text-shadow: 0px 0px 5px black;
        }

        .orderSel {
            height: 35px;
            margin-top: 3px;
            width: 140px;
        }

        .center {
            display: table;
            margin: auto;
        }

        img.center {
            height: 20vh;
        }

        .card {
            box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2);
            transition: 0.3s;
            width: 100%;
            background-color: white;
            padding: 2%;
            height: 45vh;
            position: relative;
        }

            .card:hover {
                box-shadow: 0 0 8px 0 #ea8a66;
                transform: scale(1.04);
                z-index: 2;
            }

        .prizebox {
            margin-top: 3px;
            padding: 5px;
        }

        #cboxOverlay {
            background-color: black
        }
    </style>
}

@section scripts{
    <script src="~/Content/colorbox/jquery.colorbox.js"></script>

    <script>
        $(document).ready(function () {
            // Hover ToolTips
            $('[data-toggle="tooltip"]').tooltip();

            $(".mybox img").colorbox({ opacity: 0.82 });

            if ((window.fullScreen) ||
                (window.innerWidth == screen.width && window.innerHeight == screen.height)) {
                $('#fullBtn').hide();
            } else {
                $('#closefullBtn').hide();
            }

            setTimeout(function () {
                location.reload();

            }, 300000);
        });

        var FormControl = {
            targetFormID: "#exchangeCardForm",
            funCARD: function (AWARD_NO) {
                $('#@Html.IdFor(m=>m.Search.WhereAWARD_NO)').val(AWARD_NO);
                $(this.targetFormID).attr("action", "@Url.Action("AwatExchangeCARD", (string) ViewBag.BRE_NO)");
                $(this.targetFormID).attr("target", "_blank");
                $(this.targetFormID).submit();
                $(this.targetFormID).attr("target", "_self");
            },
            exchangeList: function (AWARD_NO) {
                $.colorbox({ href: '@(Url.Action("Query", "AWA004"))?whereAWARD_NO=' + AWARD_NO + '&Awat=Awat_Key', iframe: true, width: "75%", height: "75%" });
            },
            changeOrder: function () {
                 $('#@Html.IdFor(m=>m.OrderType)').val($("#orderSel").val());
                 $(this.targetFormID).attr("action", "@Url.Action("ProductListForFullScreen", (string) ViewBag.BRE_NO)")
                 $(this.targetFormID).attr("target", "_self")
                 $(this.targetFormID).submit();
            },
        };

        // 開啟全屏
        function fullscreen(element) {
            if (element.requestFullscreen) {
                element.requestFullscreen();
            } else if (element.mozRequestFullScreen) {
                element.mozRequestFullScreen();
            } else if (element.msRequestFullscreen) {
                element.msRequestFullscreen();
            } else if (element.webkitRequestFullscreen) {
                element.webkitRequestFullScreen();
            }
        }
        // 關閉全屏
        function exitFullscreen() {
            if (document.exitFullscreen) {
                document.exitFullscreen();
            } else if (document.msExitFullscreen) {
                document.msExitFullscreen();
            } else if (document.mozCancelFullScreen) {
                document.mozCancelFullScreen();
            } else if (document.webkitExitFullscreen) {
                document.webkitExitFullscreen();
            }
        }
    </script>

}