/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Latin-Modern/Size7/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.LatinModernMathJax_Size7={directory:"Size7/Regular",family:"LatinModernMathJax_Size7",testString:"\u00A0\u0302\u0303\u0306\u030C\u0311\u032C\u032D\u032E\u032F\u0330\u2016\u2044\u20E9\u2223",32:[0,0,332,0,0],40:[1745,1245,875,277,823],41:[1745,1245,875,52,598],47:[3560,3060,2572,57,2517],91:[1750,1250,667,321,647],92:[3560,3060,2572,55,2515],93:[1750,1250,667,20,346],123:[1750,1250,902,147,755],124:[2053,1553,278,100,179],125:[1750,1250,902,147,755],160:[0,0,332,0,0],770:[749,-569,1896,0,1896],771:[773,-527,1915,0,1915],774:[743,-573,1920,0,1920],780:[743,-563,1896,0,1896],785:[761,-591,1920,0,1920],812:[-96,276,1896,0,1896],813:[-108,288,1896,0,1896],814:[-96,266,1920,0,1920],815:[-118,288,1920,0,1920],816:[-118,364,1915,0,1915],8214:[2053,1553,424,57,369],8260:[3560,3060,2572,57,2517],8425:[772,-504,2985,0,2985],8739:[2053,1553,278,100,179],8741:[2053,1553,424,57,369],8968:[1750,1250,623,221,595],8969:[1750,1250,623,28,402],8970:[1750,1250,623,221,595],8971:[1750,1250,623,28,402],9001:[1750,1250,908,204,852],9002:[1750,1250,908,56,704],9140:[772,-504,2985,0,2985],9141:[-74,342,2985,0,2985],9180:[796,-502,4032,0,4032],9181:[-72,366,4032,0,4032],9182:[854,-493,4006,0,4006],9183:[-62,423,4006,0,4006],9184:[873,-605,4082,0,4082],9185:[-175,443,4082,0,4082],10214:[1750,1250,1007,353,985],10215:[1750,1250,1007,22,654],10216:[1750,1250,908,204,852],10217:[1750,1250,908,56,704],10218:[1750,1250,1362,204,1306],10219:[1750,1250,1362,56,1158],10222:[1776,1276,647,294,591],10223:[1776,1276,647,56,353],57344:[367,-133,222,0,222],57345:[367,-133,222,0,222],57346:[367,-133,222,0,222],57347:[748,0,902,400,502],57348:[1202,0,278,100,179],57349:[1202,0,278,100,179],57350:[1202,0,278,100,179],57351:[748,0,902,400,502],57352:[711,-601,208,0,208],57353:[631,-601,139,0,139],57354:[631,-601,208,0,208],57355:[631,-601,208,0,208],57356:[631,-601,139,0,139],57357:[711,-601,208,0,208],57358:[711,-521,205,0,205],57359:[631,-601,137,0,137],57360:[631,-601,205,0,205],57361:[631,-601,205,0,205],57362:[631,-601,137,0,137],57363:[711,-521,205,0,205],57364:[711,-521,226,0,226],57365:[631,-601,151,0,151],57366:[711,-521,226,0,226],57367:[-171,201,208,0,208],57368:[-171,201,139,0,139],57369:[-171,281,208,0,208],57370:[-171,281,208,0,208],57371:[-171,201,139,0,139],57372:[-171,201,208,0,208],57373:[-91,281,205,0,205],57374:[-171,201,137,0,137],57375:[-171,201,205,0,205],57376:[-171,201,205,0,205],57377:[-171,201,137,0,137],57378:[-91,281,205,0,205],57379:[510,10,507,0,507],57380:[270,-230,337,0,337],57381:[270,-230,507,0,507],57382:[270,-230,507,0,507],57383:[270,-230,337,0,337],57384:[510,10,507,0,507],57385:[506,0,500,230,270],57386:[337,0,500,230,270],57387:[505,0,500,20,480],57388:[505,0,500,20,480],57389:[337,0,500,230,270],57390:[506,0,500,230,270],57391:[510,10,386,0,386],57392:[270,-230,97,0,97],57393:[510,10,386,0,386],57394:[270,-230,386,0,386],57395:[270,-230,386,0,386],57396:[270,-230,96,0,96],57397:[510,10,386,0,386],57398:[510,10,386,0,386],57399:[510,10,499,0,499],57400:[270,-230,332,0,332],57401:[510,10,499,0,499],57402:[380,0,572,56,516],57403:[254,0,572,266,306],57404:[380,0,572,56,516],57405:[510,10,380,0,380],57406:[270,-230,95,0,95],57407:[510,10,380,0,380],57408:[510,10,380,0,380],57409:[510,10,507,0,507],57410:[270,-230,337,0,337],57411:[270,-230,507,0,507],57412:[270,-230,507,0,507],57413:[270,-230,337,0,337],57414:[510,10,507,0,507],57415:[506,0,572,266,306],57416:[337,0,572,266,306],57417:[505,0,572,56,516],57418:[505,0,572,56,516],57419:[337,0,572,266,306],57420:[506,0,572,266,306],57421:[510,10,580,0,580],57422:[270,-230,386,0,386],57423:[510,10,580,0,580],57424:[510,10,580,0,580],57425:[270,-230,386,0,386],57426:[510,10,580,0,580],57427:[510,10,499,0,499],57428:[270,-230,333,0,333],57429:[510,10,499,0,499],57430:[510,10,499,0,499],57431:[270,-230,333,0,333],57432:[510,10,499,0,499],57433:[498,0,632,55,576],57434:[332,0,632,296,336],57435:[498,0,632,86,546],57436:[498,0,632,86,546],57437:[332,0,632,296,336],57438:[498,0,632,55,576],57439:[550,-230,507,0,507],57440:[270,-230,337,0,337],57441:[510,10,507,0,507],57442:[510,10,507,0,507],57443:[270,-230,337,0,337],57444:[550,-230,507,0,507],57445:[550,50,507,0,507],57446:[270,-230,337,0,337],57447:[510,10,507,0,507],57448:[510,10,507,0,507],57449:[270,-230,337,0,337],57450:[550,50,507,0,507],57451:[503,-230,513,0,513],57452:[270,-230,341,0,341],57453:[270,-230,512,0,512],57454:[270,-230,512,0,512],57455:[270,-230,341,0,341],57456:[503,-230,513,0,513],57457:[270,3,512,0,512],57458:[270,-230,341,0,341],57459:[270,-230,513,0,513],57460:[270,-230,512,0,512],57461:[270,-230,341,0,341],57462:[270,3,513,0,513],57463:[512,0,441,112,152],57464:[341,0,441,112,152],57465:[513,0,441,112,385],57466:[513,0,441,112,385],57467:[341,0,441,112,152],57468:[512,0,441,112,152],57469:[512,0,441,289,329],57470:[341,0,441,289,329],57471:[513,0,441,56,329],57472:[513,0,441,56,329],57473:[341,0,441,289,329],57474:[512,0,441,289,329],57475:[432,172,515,0,515],57476:[432,-68,343,0,343],57477:[672,-68,514,0,514],57478:[672,-68,514,0,514],57479:[432,-68,343,0,343],57480:[432,172,515,0,515],57481:[515,0,896,266,840],57482:[343,0,896,266,630],57483:[514,0,896,56,630],57484:[514,0,896,56,630],57485:[343,0,896,266,630],57486:[515,0,896,266,840],57487:[750,250,507,0,507],57488:[510,10,337,0,337],57489:[510,10,507,0,507],57490:[510,10,507,0,507],57491:[510,10,337,0,337],57492:[750,250,507,0,507],57493:[506,0,992,266,726],57494:[337,0,992,266,726],57495:[505,0,992,56,936],57496:[505,0,992,56,936],57497:[337,0,992,266,726],57498:[506,0,992,266,726],57499:[750,250,507,0,507],57500:[750,250,337,0,337],57501:[990,490,507,0,507],57502:[990,490,507,0,507],57503:[750,250,337,0,337],57504:[750,250,507,0,507],57505:[600,-133,515,0,515],57506:[367,-133,343,0,343],57507:[367,100,514,0,514],57508:[367,100,514,0,514],57509:[367,-133,343,0,343],57510:[600,-133,515,0,515],57511:[520,20,504,0,504],57512:[367,-133,336,0,336],57513:[367,-133,505,0,505],57514:[367,-133,505,0,505],57515:[367,-133,336,0,336],57516:[520,20,504,0,504],57517:[505,0,652,209,443],57518:[336,0,652,209,443],57519:[504,0,652,56,596],57520:[504,0,652,56,596],57521:[336,0,652,209,443],57522:[505,0,652,209,443],57523:[520,20,533,0,533],57524:[367,-133,356,0,356],57525:[520,20,533,0,533],57526:[533,0,652,56,596],57527:[356,0,652,209,443],57528:[533,0,652,56,596],57529:[520,20,384,0,384],57530:[367,-133,97,0,97],57531:[520,20,384,0,384],57532:[367,-133,384,0,384],57533:[367,-133,384,0,384],57534:[367,-133,97,0,97],57535:[520,20,384,0,384],57536:[520,20,384,0,384],57537:[520,20,406,0,406],57538:[367,-133,102,0,102],57539:[520,20,406,0,406],57540:[520,20,406,0,406],57541:[520,20,497,0,497],57542:[367,-133,331,0,331],57543:[520,20,497,0,497],57544:[520,20,497,0,497],57545:[367,-133,331,0,331],57546:[520,20,497,0,497],57547:[617,117,506,0,506],57548:[464,-36,337,0,337],57549:[464,-36,506,0,506],57550:[464,-36,506,0,506],57551:[464,-36,337,0,337],57552:[617,117,506,0,506],57553:[520,20,519,0,519],57554:[367,-133,346,0,346],57555:[367,-133,519,0,519],57556:[367,-133,519,0,519],57557:[367,-133,346,0,346],57558:[520,20,519,0,519],57559:[519,0,652,209,443],57560:[346,0,652,209,443],57561:[519,0,652,56,596],57562:[519,0,652,56,596],57563:[346,0,652,209,443],57564:[519,0,652,209,443],57565:[524,0,652,56,596],57566:[349,0,652,209,443],57567:[523,0,652,56,596],57568:[520,20,524,0,524],57569:[367,-133,349,0,349],57570:[520,20,523,0,523],57571:[468,-31,492,0,492],57572:[347,-153,327,0,327],57573:[347,-153,492,0,492],57574:[347,-153,492,0,492],57575:[347,-153,327,0,327],57576:[468,-31,492,0,492],57577:[492,0,612,209,403],57578:[327,0,612,209,403],57579:[492,0,612,87,524],57580:[492,0,612,87,524],57581:[327,0,612,209,403],57582:[492,0,612,209,403],57583:[468,-31,484,0,484],57584:[347,-153,322,0,322],57585:[468,-31,484,0,484],57586:[484,0,549,56,492],57587:[322,0,549,177,371],57588:[484,0,549,56,492],57589:[-103,143,189,0,189],57590:[-103,143,190,0,190],57591:[-103,143,189,0,189],57592:[-103,293,189,0,189],57593:[-103,293,190,0,190],57594:[-103,293,189,0,189],57595:[670,-630,189,0,189],57596:[670,-630,190,0,190],57597:[670,-630,189,0,189],57598:[820,-630,189,0,189],57599:[820,-630,190,0,190],57600:[820,-630,189,0,189],57601:[1526,0,647,294,591],57602:[998,0,647,294,396],57603:[1526,0,647,294,591],57604:[1526,0,647,56,353],57605:[998,0,647,251,353],57606:[1526,0,647,56,353],57607:[1000,0,1007,353,985],57608:[1000,0,1007,353,714],57609:[1000,0,1007,353,985],57610:[1000,0,1007,22,654],57611:[1000,0,1007,293,654],57612:[1000,0,1007,22,654],57613:[724,-493,1002,0,1002],57614:[724,-622,994,0,994],57615:[854,-622,2003,0,2003],57616:[724,-493,1001,0,1001],57617:[-62,294,1002,0,1002],57618:[-192,294,994,0,994],57619:[-192,423,2003,0,2003],57620:[-62,294,1001,0,1001],57621:[796,-502,2016,0,2016],57622:[796,-694,994,0,994],57623:[796,-502,2016,0,2016],57624:[-72,366,2016,0,2016],57625:[-264,366,994,0,994],57626:[-72,366,2016,0,2016],57627:[772,-504,1493,0,1493],57628:[772,-712,995,0,995],57629:[772,-504,1492,0,1492],57630:[-74,342,1493,0,1493],57631:[-282,342,995,0,995],57632:[-74,342,1492,0,1492],57633:[873,-605,2041,0,2041],57634:[873,-771,1360,0,1360],57635:[873,-605,2041,0,2041],57636:[-175,443,2041,0,2041],57637:[-341,443,1360,0,1360],57638:[-175,443,2041,0,2041],57639:[270,-230,222,0,222],57640:[270,-230,222,0,222],57641:[270,-230,222,0,222],57642:[1202,0,424,57,369],57643:[1202,0,424,57,369],57644:[1202,0,424,57,369],57645:[464,-36,222,0,222],57646:[464,-36,222,0,222],57647:[464,-36,222,0,222],57648:[561,61,222,0,222],57649:[561,61,222,0,222],57650:[561,61,222,0,222],57651:[640,0,1056,702,742],57652:[620,0,1056,702,1076]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"LatinModernMathJax_Size7"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Size7/Regular/Main.js"]);
