/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/General/Italic/GreekItalic.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXGeneral-italic"],{120546:[667,0,717,35,685],120547:[653,0,696,38,686],120548:[653,0,616,38,721],120549:[667,0,596,30,556],120550:[653,0,714,38,734],120551:[653,0,772,60,802],120552:[653,0,873,38,923],120553:[669,11,737,50,712],120554:[653,0,480,38,530],120555:[653,0,762,38,802],120556:[667,0,718,35,686],120557:[653,0,1005,38,1055],120558:[653,0,851,38,901],120559:[653,0,706,52,741],120560:[669,11,732,50,712],120561:[653,0,873,38,923],120562:[653,0,594,38,704],120563:[669,11,737,50,712],120564:[653,0,735,58,760],120565:[653,0,550,25,670],120566:[668,0,613,28,743],120567:[653,0,772,25,747],120568:[653,0,790,25,810],120569:[667,0,670,28,743],120570:[666,0,800,32,777],120571:[653,15,627,42,600],120572:[441,10,524,40,529],120573:[668,183,493,25,518],120574:[441,187,428,35,458],120575:[668,11,463,40,451],120576:[441,11,484,25,444],120577:[668,183,435,40,480],120578:[441,183,460,30,455],120579:[668,11,484,40,474],120580:[441,11,267,50,227],120581:[441,0,534,50,549],120582:[668,16,541,50,511],120583:[428,183,579,30,549],120584:[446,9,452,50,462],120585:[668,183,433,25,443],120586:[441,11,458,40,438],120587:[428,13,558,35,568],120588:[441,183,502,30,472],120589:[490,183,439,35,464],120590:[428,11,537,40,547],120591:[428,5,442,30,472],120592:[439,11,460,30,445],120593:[441,183,666,50,631],120594:[441,202,595,30,645],120595:[441,183,661,30,711],120596:[441,11,681,20,661],120597:[668,11,471,40,471],120598:[441,11,430,40,430],120599:[678,10,554,20,507],120600:[441,13,561,12,587],120601:[668,183,645,40,620],120602:[441,187,509,40,489],120603:[428,11,856,30,866]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/Italic/GreekItalic.js");
