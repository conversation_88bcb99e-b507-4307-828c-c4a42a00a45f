﻿@model ADDI12YoutubeViewViewModel
@{
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);

    //APP 需改成這樣
    ViewBag.YOUTUBE_URL = ViewBag.YOUTUBE_URL.Replace("watch?v=", "embed/");

}

<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
<link href="~/Content/styles/likeBtn.css?v=202011062" rel="stylesheet" />
<script src="~/Scripts/grids.js"></script>
<script src="~/Content/colorbox/jquery.colorbox.js"></script>
<!-- clipboard.js v1.7.1 -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/clipboard.js/1.7.1/clipboard.min.js"></script>

<style type="text/css">
    .videoWrapperOuter {
        max-width: 1024px;
        margin-left: auto;
        margin-right: auto;
    }

    .videoWrapperInner {
        float: none;
        clear: both;
        width: 80%;
        position: relative;
        padding-bottom: 40%;
        padding-top: 25px;
        height: 0;
        margin: auto;
    }

        .videoWrapperInner iframe {
            position: absolute;
            padding-top: 3px;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }

    .box-loader {
        background-color: red;
    }
</style>

<div class="row box-loader">
    <div class="col-xl-12 text-center">
        <div class="loader-inner ball-pulse"></div>
    </div>
</div>

<div class="videoWrapperOuter">
    <div class="videoWrapperInner">

        <iframe id="ytubeIFrame" src=""
                frameborder="0" allow="autoplay; encrypted-media" allowfullscreen></iframe>
    </div>

    <div id="PartialYoutubeView" style="padding-top: 25px;padding-bottom: 25px">
        @Html.Action("_PartialYoutubeView", (string)ViewBag.BRE_NO)
    </div>
</div>

@if (AppMode)
{
    <div style="text-align:center">
        <a class="btn btn-sm btn-sys" href="@Url.Action((string)ViewBag.ActionResultType,"ADDI12")">回前頁</a>
    </div>

}

<!-- Modal -->
<div class="modal fade" id="myModalLinkPerson" tabindex="-1" role="dialog" aria-labelledby="myModalLinkPersonLabel" style="z-index:9999">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel">按讚人員清單</h4>
            </div>
            <div class="modal-body">
                <div id="LinkPersonListView"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal -->
<div class="modal fade" id="myModalPerson" tabindex="-1" role="dialog" aria-labelledby="myModalPersonLabel" style="z-index:9999">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalPersonLabel">表演人員名單</h4>
            </div>
            <div class="modal-body">
                <div id="PartialYoutubePersonListViewOen"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade bs-example-modal-lg" id="mySmallModal" tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel" style="z-index:9999">
    <div class="modal-dialog modal-sm" role="document">
        <div class="modal-content">

            <div class="input-group">
                <span class="input-group-btn">
                    <button type="button" id="id_copy"
                            data-clipboard-target="#id_text"
                            data-clipboard-action="copy" onclick="OnCopy()">
                        點擊複製
                    </button>
                </span>
                <div id="id_text">@ViewBag.WinOpenYoutubeUrlLink</div>
                <div id="success" style="display:none">已複製</div>
                <input id="copyStr" type="hidden" value="@ViewBag.WinOpenYoutubeUrlLink">
            </div><!-- /input-group -->
        </div>
    </div>
</div>

<div class="modal fade bs-example-modal-lg" id="myQrCodeModal" tabindex="-1" role="dialog" aria-labelledby="myQrCodeModalLabel" style="z-index:9999">
    <div class="modal-dialog modal-sm" role="document">
        <div class="modal-content">
            <div class="input-group">
                @if (ViewBag.WinOpenYoutubeUrlLink != null)
                {
                    <img src="@Url.Action("Cre","Barcode", new {Value=ViewBag.WinOpenYoutubeUrlLink })" style="max-width:150px" />
                }
            </div><!-- /input-group -->
        </div>
    </div>
</div>

@*<div class="placement">*@
<div class="heart" id="heartloading"></div>
<div class="heart1" id="heart1loading"></div>
<div class="heart2" id="heart2loading"></div>
<div class="thumb" id="thumbsloading"></div>

@*</div>*@

@*<img src="~/Content/img/thumbs-up.svg" style="width:500px;height:500px;top:40%;right:50%;z-index:9999; display:none;position:fixed;" id="Likeloading" />*@
<img src="~/Content/img/heart.svg" style="width:500px;height:500px;top:40%;right:50%;z-index:9999; display:none;position:fixed;" id="heartLikeloading" />
<div id="LikeADD">
</div>
<div id="LikeADD1">
</div>
<div id="LikeADD2">
</div>
<div id="LikeADD3">
</div>
<script type="text/javascript">

   $('.loader-inner').loaders()

    $(function () {

        var $modal = $('#mySmallModal');

        $modal.on('show.bs.modal', function () {
            var $this = $(this);
            var $modal_dialog = $this.find('.modal-dialog');
            $this.css('display', 'block');
            $modal_dialog.css({ 'margin-top': Math.max(0, ($(window).height() - $modal_dialog.height())-90) });
        });

        var $QrCodeModal = $('#myQrCodeModal');

        $QrCodeModal.on('show.bs.modal', function () {
            var $this = $(this);
            var $modal_dialog = $this.find('.modal-dialog');
            $this.css('display', 'block');
            $modal_dialog.css({ 'margin-top': Math.max(0, ($(window).height() - $modal_dialog.height()) - 10) });
        });

        $('#ytubeIFrame').attr('src', '@(ViewBag.YOUTUBE_URL)?rel=0&autoplay=1');

        $('#ytubeIFrame').on('load', function () {
           $('.box-loader').hide();
        });

    });

    function OnCopy() {
        var clipboard = new Clipboard("#id_copy");
        clipboard.on("success", function (element) {//複製成功的回調
            console.info("複製成功，複製內容：    " + element.text);
            $('#success').show()
            $('#id_text').hide()

            setTimeout('HidemySmallModal()', 2000);

        });
        clipboard.on("error", function (element) {//複製失敗的回調
            console.info(element);

            var $input = $('#copyStr');
            $input.val();
            if (navigator.userAgent.match(/ipad|ipod|iphone/i)) {
                clipboard.on('success', function (e) {
                    e.clearSelection();
                    $.sDialog({
                        skin: "red",
                        content: 'copy success!',
                        okBtn: false,
                        cancelBtn: false,
                        lock: true
                    });
                    console.log('copy success!');

                    $('#success').show()
                    $('#id_text').hide()

                    setTimeout('HidemySmallModal()', 2000);

                });
            } else {
                $input.select();
            }
            //document.execCommand('copy');
            $input.blur();
        });
    }

    function HidemySmallModal() {
        $('#mySmallModal').modal('hide');
    }

    function onLinkPersonList(STAGE_ID) {
          var data = {
            "STAGE_ID": STAGE_ID
        };

             $.ajax({
                url: '@Url.Action("_LinkPersonListView", (string)ViewBag.BRE_NO)',
                 data: data,
                type: 'post',
                async: false,
                cache: false,
                dataType: 'html',
                success: function (data) {
                    $('#LinkPersonListView').html(data);
                }
            });

        if ($('#myModalLinkPerson').is(':visible') == false) {
            $('#myModalLinkPerson').modal('show');
          }
    }

    function onYoutubePersonListOne(STAGE_ID) {

        var data = {
            "STAGE_ID": STAGE_ID
        };

             $.ajax({
                url: '@Url.Action("_PartialYoutubePersonListView", (string)ViewBag.BRE_NO)',
                 data: data,
                type: 'post',
                async: false,
                cache: false,
                dataType: 'html',
                 success: function (data) {
                     $('#PartialYoutubePersonListViewOen').html(data);
                }
            });

        if ($('#myModalPerson').is(':visible') == false) {
            $('#myModalPerson').modal('show');
          }
    }

    function onWinOpenYoutubeUrlLink() {

        $('#success').hide()
        $('#id_text').show()

        if ($('#mySmallModal').is(':visible') == false) {
            $('#mySmallModal').modal('show');
        }
    }

    function onWinOpenYoutubeQRCODE() {
        if ($('#myQrCodeModal').is(':visible') == false) {
            $('#myQrCodeModal').modal('show');
        }
    }

    function random(max, min) {
        var Value = Math.floor(Math.random() * (max - min + 1)) + min;
        return Value;
    }
    function ShowLinkImg1() {
        alert("a");
        alert($(".heart").html());
        $(".heart").toggleClass("active");
    }
    var oId = 1;
    function ShowLinkImg() {

        var WidthHeight = random(300, 50) + "px"
        var Top = random(47, 10) + "%"
        var Right = random(80, 10) + "%"

        var img = random(1, 0)

        if (img == 1) {
         //  $(".heart").removeAttr('style');
         //$(".heart1").removeAttr('style');
         // $(".heart2").removeAttr('style');
         // $('#heartLikeloading').removeAttr('style');
        $(".heart").toggleClass("active");
        $('.heart').css({ 'top': '' + Top + '', 'left': '' + Right + '' })
        $('.heart').clone(true).attr('id', 'heartloading' + oId).appendTo('#LikeADD').css({ 'width': '' + WidthHeight + '', 'height': '' + WidthHeight + '', 'top': '' + Top + '', 'left': '' + Right + '' }).show(1000).fadeOut(2000);
        $(".heart1").toggleClass("active");
       $('.heart1').css({ 'top': '' + (Top + 5) + '', 'left': '' + (Right + 5) + '' })
       $('.heart1').clone(true).attr('id', 'heart1loading' + oId).appendTo('#LikeADD1').css({ 'width': '' + WidthHeight + '', 'height': '' + WidthHeight + '', 'top': '' + (Top + 5) + '', 'left': '' + (Right + 5) + '' }).show(1000).fadeOut(2000);
        $(".heart2").toggleClass("active");
       $('.heart2').css({ 'top': '' + (Top + 10) + '', 'left': '' + (Right + 10) + '' })
       $('.heart2').clone(true).attr('id', 'heart2loading' + oId).appendTo('#LikeADD2').css({ 'width': '' + WidthHeight + '', 'height': '' + WidthHeight + '', 'top': '' + (Top + 10) + '', 'left': '' + (Right + 10) + '' }).show(1000).fadeOut(2000);
        $('#heartLikeloading').css({ 'top': '' + (Top + 15) + '', 'right': '' + (Right) + '' })
          $('#heartLikeloading').clone(true).attr('id', 'heartLikeloading' + oId).appendTo('#LikeADD3').css({ 'width': '' + WidthHeight + '', 'height': '' + WidthHeight + '', 'top': '' + (Top + 15) + '', 'right': '' + (Right) + '' }).show(1000).fadeOut(2000);
           // alert("aaa");
            setTimeout(function () {
                $("#heartloading").attr("class", "heart")
                $("#heart1loading").attr("class", "heart1")
                $("#heart2loading").attr("class", "heart2")
                $("#thumbsloading").attr("class", "thumb")
              //  alert("a1");
             // $('#heartloading' + oId).remove();
              //$('#heart1loading' + oId).remove();
                //$('#heart2loading' + oId).remove();
                //$('#heartloading' + oId).remove();
                //$('#heart1loading').attr('style', 'display:none');
              //  $('#heartloading').attr('style', 'display:none');
            },1000);

        }

     else {
            $(".thumb").toggleClass("active");
            $('.thumb').css({ 'top': '' + Top + '', 'left': '' + Right + '' })
            $('.thumb').clone(true).attr('id', 'thumbsloading' + oId).appendTo('#LikeADD').css({ 'width': '' + WidthHeight + '', 'height': '' + WidthHeight + '', 'top': '' + Top + '', 'left': '' + Right + '' }).show(1000).fadeOut(2000);
          setTimeout(function () { $('#thumbsloading' + oId).remove(); }, 1000);
        }
        oId += 1;

    }

    function Share_Save(STAGE_ID) {
         $.ajax({
                url: "@(Url.Action("Share_Save", (string)ViewBag.BRE_NO))",     // url位置
                type: 'post',                   // post/get
                data: {
                    STAGE_ID: STAGE_ID,
                },     // data
                dataType: 'json',               // xml/json/script/html
                cache: false,                   // 是否允許快取
                success: function (data) {
                    var res = jQuery.parseJSON(data);

                    if (res.Success == 0) { //失敗
                        alert(res.Error);
                    }
                    else if (res.Success == 1) //成功
                    {
                        ShowLinkImg()
                        PageYoutubeView(STAGE_ID, false) //Call PageYoutubeView
                    }
                },
                error: function (xhr, err) {
                    alert("readyState: " + xhr.readyState + "\nstatus: " + xhr.status);
                    alert("responseText: " + xhr.responseText);
                }

            });
    }
    function Share_Save2(STAGE_ID, CARD_ID) {

         $.ajax({
                url: "@(Url.Action("Share_Save2", (string)ViewBag.BRE_NO))",     // url位置
                type: 'post',                   // post/get
                data: {
                    STAGE_ID: STAGE_ID,
                    CARD_ID: CARD_ID,
                },     // data
                dataType: 'json',               // xml/json/script/html
                cache: false,                   // 是否允許快取
                success: function (data) {
                    var res = jQuery.parseJSON(data);

                    if (res.Success == 0) { //失敗
                        alert(res.Error);
                        PageYoutubeView(STAGE_ID,true) //Call PageYoutubeView
                    }
                    else if (res.Success == 1) //成功
                    {
                        ShowLinkImg()
                        PageYoutubeView(STAGE_ID, true) //Call PageYoutubeView
                    }
                },
                error: function (xhr, err) {
                    alert("readyState: " + xhr.readyState + "\nstatus: " + xhr.status);
                    alert("responseText: " + xhr.responseText);
                }

            });
    }

    function PageYoutubeView(STAGE_ID,ISCARD) {
         $.ajax({
                url: '@Url.Action("_PartialYoutubeView", (string)ViewBag.BRE_NO)',
                 data: {
                     STAGE_ID: STAGE_ID,
                     ISCARD: true
                 },     // data
                type: 'post',
                async: false,
                cache: false,
                dataType: 'html',
             success: function (data) {

                    $('#PartialYoutubeView').html(data);
                },
                error: function (xhr, ajaxOptions, thrownError) {
                    alert(xhr.status);
                    alert(thrownError);
                }
            });
    }
</script>