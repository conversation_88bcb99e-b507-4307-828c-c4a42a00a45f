/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Latin-Modern/Normal/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.LatinModernMathJax_Normal={directory:"Normal/Regular",family:"LatinModernMathJax_Normal",testString:"\u00A0\u210E\uD835\uDC00\uD835\uDC01\uD835\uDC02\uD835\uDC03\uD835\uDC04\uD835\uDC05\uD835\uDC06\uD835\uDC07\uD835\uDC08\uD835\uDC09\uD835\uDC0A\uD835\uDC0B\uD835\uDC0C",32:[0,0,332,0,0],160:[0,0,332,0,0],8462:[694,11,576,55,546],119808:[698,0,869,41,827],119809:[686,0,818,39,753],119810:[697,11,831,64,766],119811:[686,0,882,39,817],119812:[680,0,756,39,723],119813:[680,0,724,39,675],119814:[697,11,904,64,845],119815:[686,0,900,39,860],119816:[686,0,436,33,403],119817:[686,11,594,23,527],119818:[686,0,901,39,852],119819:[686,0,692,39,643],119820:[686,0,1092,40,1051],119821:[686,0,900,39,860],119822:[697,11,864,64,799],119823:[686,0,786,39,721],119824:[697,194,864,64,805],119825:[686,11,863,39,858],119826:[697,11,639,64,574],119827:[675,0,800,42,757],119828:[686,11,885,39,845],119829:[686,8,869,26,842],119830:[686,8,1189,24,1164],119831:[686,0,869,34,835],119832:[686,0,869,19,849],119833:[686,0,703,64,645],119834:[453,6,559,27,553],119835:[694,6,639,37,600],119836:[453,6,511,38,478],119837:[694,6,639,38,601],119838:[453,6,527,32,494],119839:[700,0,351,42,437],119840:[455,201,575,32,558],119841:[694,0,639,45,615],119842:[685,0,319,47,286],119843:[685,200,351,-56,267],119844:[694,0,607,37,587],119845:[694,0,319,47,293],119846:[450,0,958,45,935],119847:[450,0,639,45,615],119848:[453,6,575,32,542],119849:[450,194,639,37,600],119850:[450,194,607,38,601],119851:[450,0,474,37,442],119852:[453,6,454,38,415],119853:[635,6,447,21,382],119854:[450,6,639,45,615],119855:[444,4,607,26,580],119856:[444,4,831,25,805],119857:[444,0,607,22,585],119858:[444,200,607,22,580],119859:[444,0,511,32,462],119860:[716,0,750,35,721],119861:[683,0,759,42,756],119862:[705,22,715,50,760],119863:[683,0,828,40,804],119864:[680,0,738,38,763],119865:[680,0,643,38,748],119866:[705,22,786,50,760],119867:[683,0,831,39,881],119868:[683,0,440,34,497],119869:[683,22,555,71,633],119870:[683,0,849,38,889],119871:[683,0,681,39,643],119872:[683,0,970,42,1044],119873:[683,0,803,39,881],119874:[705,22,763,49,740],119875:[683,0,642,40,754],119876:[705,194,791,49,740],119877:[683,22,759,41,755],119878:[705,22,613,52,645],119879:[677,0,584,24,704],119880:[683,22,683,67,760],119881:[683,22,583,56,769],119882:[683,22,944,55,1048],119883:[683,0,828,27,851],119884:[683,0,581,35,762],119885:[683,0,683,58,723],119886:[442,11,529,40,498],119887:[694,11,429,47,415],119888:[442,11,433,41,430],119889:[694,11,520,40,516],119890:[442,11,466,46,430],119891:[705,205,490,53,552],119892:[442,205,477,15,474],119894:[661,11,345,29,293],119895:[661,205,412,-13,397],119896:[694,11,521,55,508],119897:[694,11,298,44,258],119898:[442,11,878,29,848],119899:[442,11,600,29,571],119900:[442,11,485,41,469],119901:[442,194,503,-32,490],119902:[442,194,446,40,452],119903:[442,11,451,29,436],119904:[442,11,469,52,420],119905:[626,11,361,23,330],119906:[442,11,572,29,543],119907:[442,11,485,29,468],119908:[442,11,716,29,691],119909:[442,11,572,29,527],119910:[442,205,490,29,490],119911:[442,11,465,43,467],119912:[711,0,869,45,834],119913:[686,0,866,43,854],119914:[703,17,817,55,855],119915:[686,0,938,43,915],119916:[680,0,810,43,825],119917:[680,0,689,43,809],119918:[703,17,887,55,854],119919:[686,0,982,43,1027],119920:[686,0,511,38,566],119921:[686,17,631,56,694],119922:[686,0,971,43,1003],119923:[686,0,756,43,707],119924:[686,0,1142,46,1216],119925:[686,0,950,43,1027],119926:[703,17,837,53,815],119927:[686,0,723,43,849],119928:[703,194,869,53,815],119929:[686,17,872,43,881],119930:[703,17,693,64,714],119931:[675,0,637,25,772],119932:[686,17,800,63,877],119933:[686,17,678,66,885],119934:[686,17,1093,65,1207],119935:[686,0,947,39,953],119936:[686,0,675,44,875],119937:[686,0,773,68,805],119938:[452,8,633,45,599],119939:[694,8,521,52,506],119940:[452,8,513,46,511],119941:[694,8,610,45,604],119942:[452,8,554,49,511],119943:[702,202,568,62,626],119944:[452,202,545,6,534],119945:[694,8,668,50,634],119946:[694,8,405,32,359],119947:[694,202,471,-14,450],119948:[694,8,604,50,570],119949:[694,8,348,33,288],119950:[452,8,1032,32,998],119951:[452,8,713,32,679],119952:[452,8,585,46,569],119953:[452,194,601,-15,586],119954:[452,194,542,45,543],119955:[452,8,529,32,505],119956:[452,8,531,57,476],119957:[643,8,415,25,379],119958:[452,8,681,32,647],119959:[453,8,567,32,541],119960:[453,8,831,32,797],119961:[452,8,659,38,604],119962:[452,202,590,32,582],119963:[452,8,555,42,538],120484:[442,11,322,29,293],120485:[442,205,384,-13,361],120488:[698,0,869,41,827],120489:[686,0,818,39,753],120490:[680,0,692,39,643],120491:[698,0,958,57,900],120492:[680,0,756,39,723],120493:[686,0,703,64,645],120494:[686,0,900,39,860],120495:[697,11,894,64,829],120496:[686,0,436,33,403],120497:[686,0,901,39,852],120498:[698,0,806,40,765],120499:[686,0,1092,40,1051],120500:[686,0,900,39,860],120501:[675,0,767,48,718],120502:[697,11,864,64,799],120503:[680,0,900,39,860],120504:[686,0,786,39,721],120505:[697,11,894,64,829],120506:[686,0,831,64,766],120507:[675,0,800,42,757],120508:[697,0,894,64,829],120509:[686,0,831,64,766],120510:[686,0,869,34,835],120511:[686,0,894,64,829],120512:[697,0,831,51,779],120513:[686,25,958,57,900],120514:[452,8,743,52,744],120515:[703,194,647,92,562],120516:[452,211,679,26,594],120517:[718,8,551,53,466],120518:[461,17,597,57,471],120519:[703,202,615,83,559],120520:[452,211,615,-3,555],120521:[702,8,567,52,513],120522:[452,8,359,43,361],120523:[452,8,615,59,617],120524:[694,12,618,21,663],120525:[452,211,615,59,617],120526:[452,0,583,-16,522],120527:[703,202,615,83,559],120528:[453,6,575,32,542],120529:[444,9,679,-3,610],120530:[452,211,567,59,513],120531:[452,105,551,84,499],120532:[444,8,695,52,626],120533:[444,12,615,-3,546],120534:[453,8,615,-3,563],120535:[452,216,743,51,690],120536:[452,202,743,-3,744],120537:[694,202,743,-3,690],120538:[453,8,743,51,689],120539:[711,17,679,67,610],120540:[444,8,506,78,505],120541:[702,8,615,-3,608],120542:[452,10,724,35,689],120543:[694,202,743,61,681],120544:[452,194,567,59,514],120545:[444,8,966,-3,897],120546:[716,0,750,35,721],120547:[683,0,759,42,756],120548:[680,0,615,39,720],120549:[716,0,833,49,787],120550:[680,0,738,38,763],120551:[683,0,683,58,723],120552:[683,0,831,39,881],120553:[705,22,763,49,740],120554:[683,0,440,34,497],120555:[683,0,849,38,889],120556:[716,0,694,35,666],120557:[683,0,970,42,1044],120558:[683,0,803,39,881],120559:[677,0,742,53,777],120560:[705,22,763,49,740],120561:[680,0,831,39,880],120562:[683,0,642,40,754],120563:[705,22,763,49,740],120564:[683,0,780,58,806],120565:[677,0,584,24,704],120566:[705,0,583,28,701],120567:[683,0,667,23,643],120568:[683,0,828,27,851],120569:[683,0,612,27,693],120570:[705,0,772,80,786],120571:[683,33,833,50,789],120572:[442,11,640,41,602],120573:[706,194,566,30,574],120574:[442,215,518,18,543],120575:[712,11,444,43,452],120576:[453,22,466,26,429],120577:[697,205,438,46,474],120578:[442,216,497,29,496],120579:[705,11,469,42,455],120580:[442,11,354,56,324],120581:[442,11,576,55,546],120582:[694,13,583,53,548],120583:[442,216,603,30,572],120584:[442,0,494,53,524],120585:[697,205,438,23,446],120586:[442,11,485,41,469],120587:[431,11,570,27,567],120588:[442,216,517,30,502],120589:[442,108,363,31,409],120590:[431,11,571,38,567],120591:[431,12,437,27,511],120592:[442,11,540,29,524],120593:[442,218,654,50,619],120594:[442,205,626,31,594],120595:[694,205,651,29,635],120596:[442,11,622,13,604],120597:[716,22,531,40,566],120598:[431,11,406,47,376],120599:[705,11,591,29,561],120600:[442,12,624,70,582],120601:[694,205,596,49,573],120602:[442,194,517,74,502],120603:[431,11,828,27,817],120604:[711,0,869,45,834],120605:[686,0,866,43,854],120606:[680,0,657,43,777],120607:[711,0,958,60,904],120608:[680,0,810,43,825],120609:[686,0,773,68,805],120610:[686,0,982,43,1027],120611:[703,17,867,53,844],120612:[686,0,511,38,566],120613:[686,0,971,43,1003],120614:[711,0,806,45,772],120615:[686,0,1142,46,1216],120616:[686,0,950,43,1027],120617:[675,0,841,62,867],120618:[703,17,837,53,815],120619:[680,0,982,43,1026],120620:[686,0,723,43,849],120621:[703,17,867,53,844],120622:[686,0,885,69,901],120623:[675,0,637,25,772],120624:[703,0,671,32,802],120625:[686,0,767,28,738],120626:[686,0,947,39,953],120627:[686,0,714,27,792],120628:[703,0,879,93,886],120629:[686,25,958,60,904],120630:[452,8,761,46,704],120631:[703,194,660,35,638],120632:[452,211,590,12,617],120633:[718,8,522,47,513],120634:[461,17,529,35,483],120635:[703,202,508,48,522],120636:[452,211,600,32,594],120637:[702,8,562,46,547],120638:[452,8,412,46,378],120639:[452,8,668,50,634],120640:[694,12,671,47,644],120641:[452,211,708,40,674],120642:[452,0,577,45,603],120643:[703,202,508,24,491],120644:[452,8,585,46,569],120645:[444,9,682,31,668],120646:[452,211,612,40,597],120647:[452,105,424,35,462],120648:[444,8,686,42,671],120649:[444,12,521,31,604],120650:[453,8,631,32,605],120651:[452,216,747,52,704],120652:[452,202,718,37,679],120653:[694,202,758,32,733],120654:[453,8,718,22,691],120655:[711,17,628,59,657],120656:[444,8,483,50,444],120657:[702,8,692,32,655],120658:[452,10,724,72,681],120659:[694,202,713,56,687],120660:[452,194,612,81,597],120661:[444,8,975,31,955],120782:[655,11,575,45,529],120783:[655,0,575,85,494],120784:[655,0,575,57,517],120785:[655,11,575,48,526],120786:[656,0,575,32,542],120787:[655,11,575,57,517],120788:[655,11,575,48,526],120789:[676,11,575,64,558],120790:[655,11,575,48,526],120791:[655,11,575,48,526]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"LatinModernMathJax_Normal"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Normal/Regular/Main.js"]);
