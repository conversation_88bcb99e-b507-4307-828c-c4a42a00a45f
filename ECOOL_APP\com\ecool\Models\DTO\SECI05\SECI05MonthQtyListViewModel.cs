﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class SECI05MonthQtyListViewModel
    {
        [Display(Name = "年月")]
        public string RET_YYMM { get; set; }

        [Display(Name = "全校借書量(本)")]
        public int SCHOOL_SUM { get; set; }

        [Display(Name = "班借書量(本)")]
        public int CLASS_SUM { get; set; }

        /// <summary>
        /// 班級數
        /// </summary>
        public int CLASS_COUNT { get; set; }

        [Display(Name = "平均借書量(本)(班級)")]
        public double BORROW_BOOK_AVG { get; set; }
    }
}