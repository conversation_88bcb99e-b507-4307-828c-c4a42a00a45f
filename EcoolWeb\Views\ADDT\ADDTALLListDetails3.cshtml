﻿@using ECOOL_APP.com.ecool.Models.entity;
@model IEnumerable<ECOOL_APP.EF.ADDT06>

@{
    ViewBag.Title = ViewBag.UserName + "-閱讀護照明細";
    int i = 0;

    List<uADDT04Q02> liADDT04Q02 = (List<uADDT04Q02>)ViewBag.ListA04Q2;

}
@if (Model!=null&&Model.Count() > 0) {
<section class="row px-3 readingCertification">
    <div class="col-lg-12 bg-custom-yellow bgPosition">
        <h2 class="heading-h2 mb-3">閱讀認證</h2>
        @foreach (var item in Model)
        {
            List<Image_File> ImageModel = (List<Image_File>)ViewBag.ImageUrl;

            <div class="break-avoid">
                <div class="bg-custom-white">
                    <table class="table">
                        <tr>
                            <td width=70% class="pt-3 pb-1 pl-4">
                                <table>
                                    <tr>
                                        <th scope="row" class="pr-3">申請日</th>
                                        <td>@Html.DisplayFor(model => item.CRE_DATE, "ShortDateTime")</td>
                                    </tr>
                                </table>
                            </td>
                            <td class="pt-3 pb-1 pr-4">
                                <table>
                                    <tr>
                                        <th scope="row" class="pr-3">班級</th>
                                        <td>@Html.DisplayFor(model => item.CLASS_NO)</td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td class="pb-3 pt-1 pl-4">
                                <table>
                                    <tr>
                                        <th scope="row" class="pr-3">書名</th>
                                        <td>@Html.DisplayFor(model => item.BOOK_NAME)</td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                </div>
                <div class="row content">

                    @if (ImageModel[i].APPLY_NO == item.APPLY_NO && ImageModel[i].ImageUrl != null)
                    {
                        if (string.IsNullOrWhiteSpace(item.REVIEW))
                        {
                            <div class="col-12">
                                <div class="readingPaper">
                                    <img src="@ImageModel[i].ImageUrl" id="imgArticle" href="@ImageModel[i].ImageUrl" />
                                </div>
                            </div>
                        }
                        else
                        {
                            <div class="col-12">
                                @{

                                    int REVIEW_VERIFYCount = 0;
                                    int REVIEWYCount = 0;
                                    if (!string.IsNullOrWhiteSpace(item.REVIEW_VERIFY))
                                    {

                                        REVIEW_VERIFYCount = item.REVIEW_VERIFY.Replace("\r\n", "<br />").Count();

                                    }
                                    if (!string.IsNullOrWhiteSpace(item.REVIEW))
                                    {
                                        REVIEWYCount = item.REVIEW.Replace("\r\n", "<br />").Count();
                                    }
                                }
                                @if (REVIEW_VERIFYCount > 10 || REVIEWYCount > 10)
                                {

                                    <p>
                                        @if (ViewBag.ShowOriginalArticle == "V")
                                        {



                                            @Html.Raw(HttpUtility.HtmlDecode(item.REVIEW_VERIFY.Replace("\r\n", "<br />")))



                                        }
                                        else
                                        {

                                            @Html.Raw(HttpUtility.HtmlDecode(item.REVIEW.Replace("\r\n", "<br />")))
                                        }
                                    </p>
                                }
                                else
                                {
                                    if (ViewBag.ShowOriginalArticle == "V")
                                    {



                                        @Html.Raw(HttpUtility.HtmlDecode(item.REVIEW_VERIFY.Replace("\r\n", "<br />")))



                                    }
                                    else
                                    {

                                        @Html.Raw(HttpUtility.HtmlDecode(item.REVIEW.Replace("\r\n", "<br />")))
                                    }

                                }

                                <div class="readingPaper">
                                    <img src="@ImageModel[i].ImageUrl" id="imgArticle" href="@ImageModel[i].ImageUrl" />
                                </div>
                            </div>
                        }
                    }
                    @if (string.IsNullOrWhiteSpace(item.VERIFY_COMMENT) == false)
                    {
                        <hr>
                        <table>
                            <tr>
                                <th width=100 class="text-nowrap">教師評語：</th>
                                <td>@Html.Raw(HttpUtility.HtmlDecode(item.VERIFY_COMMENT))</td>
                            </tr>
                        </table>

                    }
                </div>
            </div>

            i++;
        }
    </div>
</section> }