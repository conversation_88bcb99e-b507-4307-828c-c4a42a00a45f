﻿@model GameEditViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;
    Layout = "~/Views/Shared/_LayoutGame.cshtml";
    EcoolWeb.Models.UserProfileHelper.SetLayoutCookieData(Layout);
}

<link href="~/Content/colorbox/example2/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>
<!-- clipboard.js v1.7.1 複製內容到剪貼簿的小工具 -->
<script src="~/Scripts/clipboard.min.js"></script>

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@{
    if (!string.IsNullOrWhiteSpace(Model?.Search?.WhereGAME_NO))
    {
        Html.RenderAction("_ByOneGameMenu", new { NowAction = "Edit" });
    }
}

@using (Html.BeginForm((string)ViewBag.Action, (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "formEdit", id = "formEdit", enctype = "multipart/form-data" }))
{

    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })
    @Html.HiddenFor(model => model.Main.GAME_NO)
    @Html.HiddenFor(model => model.Main.SCHOOL_NO)

    @Html.HiddenFor(m => m.Search.WhereGAME_NAME)
    @Html.HiddenFor(m => m.Search.WhereGAME_NO)
    @Html.HiddenFor(m => m.Search.WhereLOTTERY_NO)
    @Html.HiddenFor(m => m.Search.WhereSCHOOL_NO)
    @Html.HiddenFor(m => m.Search.SyntaxName)
    @Html.HiddenFor(m => m.Search.Page)
    @Html.HiddenFor(m => m.Search.OrdercColumn)
    @Html.HiddenFor(m => m.GAME_TYPE)

    <div class="panel panel-ZZZ" name="TOP">
        <div class="panel-heading text-center">
            @ViewBag.Panel_Title
        </div>
        <div class="panel-body">
            <div class="form-horizontal">
                <div class="form-group">
                    @Html.LabelFor(model => model.Main.GAME_NAME, htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-9">
                        @Html.EditorFor(model => model.Main.GAME_NAME, new { htmlAttributes = new { @class = "form-control", @placeholder = "必填" } })
                        @Html.ValidationMessageFor(model => model.Main.GAME_NAME, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group">
                    @Html.LabelFor(model => model.Main.GAME_DESC, htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-9">
                        @Html.TextAreaFor(m => m.Main.GAME_DESC, 2, 100, new { @class = "form-control", @placeholder = Html.DisplayNameFor(n => n.Main.GAME_DESC) })
                        @Html.ValidationMessageFor(model => model.Main.GAME_DESC, "", new { @class = "text-danger" })
                    </div>
                </div>

                @if (Model.GAME_TYPE == (byte)ADDT26.GameType.一般)
                {
                    <div class="form-group">
                        @Html.LabelFor(model => model.Main.LIKE_COUNT, htmlAttributes: new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            @Html.EditorFor(model => model.Main.LIKE_COUNT, new { htmlAttributes = new { @class = "form-control", @placeholder = "必填" } })
                            @Html.ValidationMessageFor(model => model.Main.LIKE_COUNT, "", new { @class = "text-danger" })
                            <br />
                            <font color="red">*未設定預設30</font>
                        </div>
                    </div>
                }
                <div class="form-group">
                    @Html.LabelFor(model => model.Main.GAME_DATES, htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-9">
                        @Html.EditorFor(model => model.Main.GAME_DATES, new { htmlAttributes = new { @class = "form-control", @readonly = "readonly", @type = "text" } })
                        @Html.ValidationMessageFor(model => model.Main.GAME_DATES, "", new { @class = "text-danger" })
                    </div>
                </div>

                <div class="form-group">
                    @Html.LabelFor(model => model.Main.GAME_DATEE, htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-9">
                        @Html.EditorFor(model => model.Main.GAME_DATEE, new { htmlAttributes = new { @class = "form-control", @readonly = "readonly", @type = "text" } })
                        @Html.ValidationMessageFor(model => model.Main.GAME_DATEE, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group">
                    @Html.LabelFor(model => model.Main.GAME_IMG, htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-9">
                        @if (Model.Main != null)
                        {
                            if (!string.IsNullOrWhiteSpace(Model.Main.GAME_IMG_PATH))
                            {
                                <img src="@Model.Main.GAME_IMG_PATH" class="img-responsive colorbox" alt="Responsive image" href="@Model.Main.GAME_IMG_PATH" style="max-width:300px" />
                                <br />
                            }
                        }
                        <div>
                            @Html.TextBoxFor(m => m.Main.UploadGamerFile, new { @class = "form-control input-md", @type = "file" })
                            @Html.ValidationMessageFor(m => m.Main.UploadGamerFile, "", new { @class = "text-danger" })
                            <br />
                            <label class="text-info">
                                PS:<br />
                                1.請上傳 jpg、jpeg、png、gif、bmp 等格式的圖片<br />
                                2.上傳的圖片，同時也是各關卡的預設圖片<br />
                                3.未上傳，則自動使用系統預設圖片
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="ApplyDetailsView">
        <div style="margin-top:20px;margin-bottom:30px;text-align:center">
            <span class="glyphicon glyphicon-circle-arrow-down fa-2x" aria-hidden="true" style="color:#004da0"></span>
        </div>
        @if (Model.Main?.GAME_NO != null)
        {

            <div class="col-xs-12 text-right">
                <button role="button" class="btn btn-sm btn-sys cScreen" onclick="onBackupLink()">備援機網址輸入</button>
                <a role="button" class="btn btn-sm btn-sys cScreen" target="_blank" href="@Url.Action("ExportFastLink","Game",new {GAME_NO=Model.Main.GAME_NO})">匯出快速連結excel</a>
            </div>

            <div class="col-xs-12 text-right">
            </div>
        }
        <div class="panel panel-ZZZ">
            <div class="panel-heading text-center">
                報名關卡
            </div>
            <div class="panel-body">
                @Html.ValidationMessage("ApplyError", new { @class = "text-danger" })

                @if (Html.ValidationMessage("ApplyError") != null)
                {
                    <br /> <br />
                }
                <div class="table-responsive">
                    <div class="css-table" style="width:92%;">
                        <div class="tr" style=" font-weight: bold;text-align:center;color:#004da0;text-overflow:ellipsis; ">
                            <div class="th" style="text-align:center">
                            </div>
                            <div class="th" style="text-align:center">
                            </div>
                            <div class="th" style="text-align:center">
                                關卡名稱
                            </div>
                            <div class="th" style="text-align:center">
                                酷幣點數
                            </div>
                            <div class="th" style="text-align:center">
                                關卡圖片(未上傳，則自動使用系統預設圖片)
                            </div>
                            <div class="th" style="text-align:center">
                                結果停留時間(秒)
                            </div>
                        </div>
                        <div class="tbody">
                            @if (Model.Details == null || Model.Details?.Where(a => a.LEVEL_TYPE == ADDT26_D.LevelType.Apply).Count() == 0)
                            {

                                if (Model.Details == null)
                                {
                                    Model.Details = new List<GameEditDetailsViewModel>();
                                }

                                Model.Details.Add(new GameEditDetailsViewModel()
                                {
                                    IsApply = true,
                                    LEVEL_TYPE = ADDT26_D.LevelType.Apply,
                                    LEVEL_NAME = "報名",
                                    Y_REPEAT = false,
                                    CASH = 0,
                                });
                            }

                            @foreach (var Item in Model.Details.Where(a => a.LEVEL_TYPE == ADDT26_D.LevelType.Apply).ToList())
                            {
                                Html.RenderPartial("_Details", Item);
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="testDetailsView">
        <div style="margin-top:20px;margin-bottom:30px;text-align:center">
            <span class="glyphicon glyphicon-circle-arrow-down fa-2x" aria-hidden="true" style="color:#004da0"></span>
        </div>

        <div class="panel panel-ZZZ">
            <div class="panel-heading text-center">
                測試及開卡關卡
            </div>
            <div class="panel-body">
                <div class="table-responsive">
                    <div class="css-table" style="width:92%;">
                        <div class="tr" style=" font-weight: bold;text-align:center;color:#004da0;text-overflow:ellipsis; ">
                            <div class="th" style="text-align:center">
                            </div>
                            <div class="th" style="text-align:center">
                            </div>
                            <div class="th" style="text-align:center">
                                關卡名稱
                            </div>
                            <div class="th" style="text-align:center">
                                酷幣點數
                                <br />(如果是扣點，必須寫(-)，例如:-10)
                            </div>
                            <div class="th" style="text-align:center">
                                關卡圖片(未上傳，則自動使用系統預設圖片)
                            </div>
                            <div class="th" style="text-align:center">
                                過場動畫秒數(秒)
                            </div>
                            <div class="th" style="text-align:center">
                                結果停留時間(秒)
                            </div>
                        </div>
                        <div class="tbody">
                            @if (Model.Details == null || Model.Details?.Where(a => a.LEVEL_TYPE == ADDT26_D.LevelType.Test).Count() == 0)
                            {
                                if (Model.Details == null)
                                {
                                    Model.Details = new List<GameEditDetailsViewModel>();
                                }

                                Model.Details.Add(new GameEditDetailsViewModel()
                                {
                                    IsApply = false,
                                    LEVEL_TYPE = ADDT26_D.LevelType.Test,
                                    LEVEL_NAME = "測試及開卡",
                                    Y_REPEAT = true,
                                    CASH = 0,
                                });
                            }

                            @foreach (var Item in Model.Details.Where(a => a.LEVEL_TYPE == ADDT26_D.LevelType.Test).ToList())
                            {
                                Html.RenderPartial("_Details", Item);
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="PrizeDetailsView">
        <div style="margin-top:20px;margin-bottom:30px;text-align:center">
            <span class="glyphicon glyphicon-circle-arrow-down fa-2x" aria-hidden="true" style="color:#004da0"></span>
        </div>

        <div class="panel panel-ZZZ">
            <div class="panel-heading text-center" style="background-color:antiquewhite">
                兌換關卡(模式1，達到點數可以兌換參加獎模式)
            </div>
            <div class="panel-body">
                <div class="table-responsive">
                    <div class="css-table" style="width:92%;">
                        <div class="tr" style=" font-weight: bold;text-align:center;color:#004da0;text-overflow:ellipsis; ">
                            <div class="th" style="text-align:center">
                            </div>
                            <div class="th" style="text-align:center;width:50px">
                                可重複<br>兌換
                            </div>
                            
                            <div class="th" style="text-align:center">
                                關卡名稱
                            </div>
                            <div class="th" style="text-align:center">
                                酷幣點數
                                <br>(如果是扣點，必須寫(-)，例如:-10)
                            </div>
                            <div class="th" style="text-align:center">
                                關卡圖片(未上傳，則自動使用系統預設圖片)
                            </div>
                            <div class="th" style="text-align:center">
                                過場動畫秒數(秒)
                            </div>
                            <div class="th" style="text-align:center">
                                結果停留時間(秒)
                            </div>

                        </div>
                        <div class="tbody" id="PrizeEditorRows">
                            @if (Model.Details == null || Model.Details?.Where(a => a.LEVEL_TYPE == ADDT26_D.LevelType.Prize).Count() == 0)
                            {
                                if (Model.Details == null)
                                {
                                    Model.Details = new List<GameEditDetailsViewModel>();
                                }

                                Model.Details.Add(new GameEditDetailsViewModel()
                                {
                                    IsApply = false,
                                    IsAgainst = true,
                                    LEVEL_TYPE = ADDT26_D.LevelType.Prize,
                                    LEVEL_NAME = "兌換關卡",
                                    Y_REPEAT = false,
                                   
                                    CASH = 0,
                                });
                            }

                            @foreach (var Item in Model.Details.Where(a => a.LEVEL_TYPE == ADDT26_D.LevelType.Prize).ToList())
                            {
                                Html.RenderPartial("_DetailsPrize", Item);
                            }
                        </div>
                    </div>
                </div>
            </div>
            <div class="panel-footer">
                <div class="row">
                    <div class="col-md-12 col-xs-12 text-right">
                        <span class="input-group-btn">
                            <button class="btn btn-info btn-sm" type="button" onclick="onAddPrizeItem()">
                                <i class="fa fa-plus-circle"></i>   增加兌換關卡
                            </button>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div>
        <div style="margin-top:20px;margin-bottom:30px;text-align:center">
            <span class="glyphicon glyphicon-circle-arrow-down fa-2x" aria-hidden="true" style="color:#004da0"></span>
        </div>
        <div class="panel panel-ZZZ">
            <div class="panel-heading text-center" style="background-color:antiquewhite">
                兌換關卡(模式2，達到點數可以兌換抽獎獎模式)
            </div>
            <div class="panel-body">
                @Html.ValidationMessage("PrizeDetailsError", new { @class = "text-danger" })
                @{

                    var Prizevalue = Html.ValidationMessage("PrizeDetailsError").ToString();

                    if (Prizevalue.Contains("field-validation-error"))
                    {
                        <br /> <br />
                    }
                }
                <div class="table-responsive">
                    <div class="css-table" style="width:92%;">
                        <div class="tr" style=" font-weight: bold;text-align:center;color:#004da0;text-overflow:ellipsis; ">
                            <div class="th" style="text-align:center">
                            </div>
                            <div class="th" style="text-align:center;width:50px">
                                可重複
                                兌換
                            </div>
                            <div class="th" style="text-align:center;">
                                關卡名稱
                            </div>
                            <div class="th" style="text-align:center">
                                酷幣點數<br>(如果是扣點，必須寫(-)，例如:-10)
                            </div>
                            <div class="th" style="text-align:center">
                                獎品名稱
                            </div>
                            <div class="th" style="text-align:center">
                                獎品數量
                            </div>
                            <div class="th" style="text-align:center">
                                @*獎品機率*@
                            </div>
                            @*<div class="th" style="text-align:center">
                                    獎品機率
                                </div>*@
                        </div>
                        @*<div id="PrizeItemEditorRows">*@
                        <div class="tbody" style=" font-weight: bold;text-align:center;color:#004da0;text-overflow:ellipsis; ">
                            @if (Model.Details == null || Model.Details?.Where(a => a.LEVEL_TYPE == ADDT26_D.LevelType.ItemPrize && a.Coupons_ITem == "1").Count() == 0)
                            {
                                if (Model.Details == null)
                                {
                                    Model.Details = new List<GameEditDetailsViewModel>();
                                }

                                Model.Details.Add(new GameEditDetailsViewModel()
                                {
                                    IsApply = false,
                                    IsAgainst = false,
                                    IsCouppons = true,
                                    LEVEL_ITEM = "004",
                                    Coupons_ITem = "1",
                                    //PrizeName = "1",
                                    //PrizeQty = 1,
                                    //PrizeRate = 1,
                                    LEVEL_TYPE = ADDT26_D.LevelType.ItemPrize,
                                    LEVEL_NAME = "我要兌換抽獎",
                                    Y_REPEAT = false,
                                    CASH = 0,

                                });
                            }

                            @foreach (var Item in Model.Details.Where(a => a.LEVEL_TYPE == ADDT26_D.LevelType.ItemPrize && a.Coupons_ITem == "1").ToList())
                            {
                                Html.RenderPartial("_DetailsPrize2", Item);
                            }
                        </div>
                        <div class="tbody" id="PrizeDetailsEditorRows">

                            @if (Model.PrizeDetails == null || Model.PrizeDetails.Count() == 0)
                            {
                                if (Model.PrizeDetails == null)
                                {
                                    Model.PrizeDetails = new List<GameLotteryPrizeViewModel>();
                                }

                                Model.PrizeDetails.Add(new GameLotteryPrizeViewModel()
                                {
                                    IsApply = false,
                                    IsAgainst = true,

                                    PrizeName = "獎品1",
                                    PrizeQty = 1,
                                    LEVEL_NO = "1",
                                    PrizeRate = 1,
                                    LEVEL_TYPE = ADDT26_D.LevelType.Prize,
                                    LEVEL_NAME = "我要兌換抽獎",
                                    Y_REPEAT = false,

                                });
                                Model.PrizeDetails.Add(new GameLotteryPrizeViewModel()
                                {
                                    IsApply = false,
                                    IsAgainst = true,

                                    PrizeName = "獎品2",
                                    PrizeQty = 1,
                                    LEVEL_NO = "1",
                                    PrizeRate = 1,
                                    LEVEL_TYPE = ADDT26_D.LevelType.Prize,
                                    LEVEL_NAME = "我要兌換抽獎",
                                    Y_REPEAT = false,

                                }); Model.PrizeDetails.Add(new GameLotteryPrizeViewModel()
                                {
                                    IsApply = false,
                                    IsAgainst = true,

                                    PrizeName = "獎品3",
                                    PrizeQty = 1,
                                    LEVEL_NO = "1",
                                    PrizeRate = 1,
                                    LEVEL_TYPE = ADDT26_D.LevelType.Prize,
                                    LEVEL_NAME = "我要兌換抽獎",
                                    Y_REPEAT = false,

                                });
                            }

                            @foreach (var Item in Model.PrizeDetails.Where(x => x.LEVEL_NO == "1").OrderBy(x=>x.PrizeName).ToList())
                            {
                                Html.RenderPartial("_DetailsPrizeLottery", Item);
                            }
                        </div>
                        @*</div>*@
                    </div>
                </div>
                <div class="panel-footer">
                    <div class="row">
                        <div class="col-md-12 col-xs-12 text-right">
                            <span class="input-group-btn">
                                <button class="btn btn-info btn-sm" type="button" onclick="onAddDetailsPrizeLotteryItem()">
                                    <i class="fa fa-plus-circle"></i>   增加兌換獎品
                                </button>
                            </span>
                        </div>
                    </div>
                </div>
                @*<div class="panel-footer">
                       <div class="row">
                         <div class="col-md-12 col-xs-12 text-right">
                         <span class="input-group-btn">
                             <button class="btn btn-info btn-sm" type="button" onclick="onAddPrizeExchangeItem()">
                                 <i class="fa fa-plus-circle"></i>   增加兌換關卡
                             </button>
                         </span>
                        </div>
                       </div>
                    </div>*@
            </div>
        </div>
        <div class="panel panel-ZZZ">
         
            <div class="panel-heading text-center" style="background-color:antiquewhite">
                兌換關卡(模式2，達到點數可以兌換抽獎獎模式)

            </div>
            <div class="panel-body">
                <b style="font-size:20px">說明：如果第一個抽獎不夠，可以再使用下面第二個抽獎；如果沒使用，不用刪除，直接可以忽略即可</b>
                @Html.ValidationMessage("PrizeDetailsError", new { @class = "text-danger" })
                @{

                    var Prizevalue1 = Html.ValidationMessage("PrizeDetailsError").ToString();

                    if (Prizevalue1.Contains("field-validation-error"))
                    {
                        <br /> <br />
                    }
                }
                <div class="table-responsive">
                    <div class="css-table" style="width:92%;">
                        <div class="tr" style=" font-weight: bold;text-align:center;color:#004da0;text-overflow:ellipsis; ">
                            <div class="th" style="text-align:center">
                            </div>
                            <div class="th" style="text-align:center;width:50px">
                                可重複
                                兌換
                            </div>
                            <div class="th" style="text-align:center;">
                                關卡名稱
                            </div>
                            <div class="th" style="text-align:center">
                                酷幣點數<br>(如果是扣點，必須寫(-)，例如:-10)
                            </div>
                            <div class="th" style="text-align:center">
                                獎品名稱
                            </div>
                            <div class="th" style="text-align:center">
                                獎品數量
                            </div>
                            <div class="th" style="text-align:center">
                                @*獎品機率*@
                            </div>
                            @*<div class="th" style="text-align:center">
                            獎品機率
                        </div>*@
                        </div>
                        @*<div id="PrizeItemEditorRows">*@
                        <div class="tbody" style=" font-weight: bold;text-align:center;color:#004da0;text-overflow:ellipsis; ">
                            @if (Model.Details == null || Model.Details?.Where(a => a.LEVEL_TYPE == ADDT26_D.LevelType.ItemPrize && a.Coupons_ITem == "2").Count() == 0)
                            {
                                if (Model.Details == null)
                                {
                                    Model.Details = new List<GameEditDetailsViewModel>();
                                }

                                Model.Details.Add(new GameEditDetailsViewModel()
                                {
                                    IsApply = false,
                                    IsAgainst = false,
                                    IsCouppons = true,
                                    LEVEL_ITEM = "005",
                                    Coupons_ITem = "2",
                                    //PrizeName = "1",
                                    //PrizeQty = 1,
                                    //PrizeRate = 1,
                                    LEVEL_TYPE = ADDT26_D.LevelType.ItemPrize,
                                    LEVEL_NAME = "我要兌換抽獎2",
                                    Y_REPEAT = false,
                                    CASH = 0,

                                });
                            }

                            @foreach (var Item in Model.Details.Where(a => a.LEVEL_TYPE == ADDT26_D.LevelType.ItemPrize && a.Coupons_ITem == "2").ToList())
                            {
                                Html.RenderPartial("_DetailsPrize2", Item);
                            }
                        </div>
                        <div class="tbody" id="PrizeDetailsEditorRows1">

                            @if (Model.PrizeDetails == null || Model.PrizeDetails.Where(x => x.LEVEL_NO == "2").Count() == 0)
                            {
                                if (Model.PrizeDetails == null)
                                {
                                    Model.PrizeDetails = new List<GameLotteryPrizeViewModel>();
                                }

                                Model.PrizeDetails.Add(new GameLotteryPrizeViewModel()
                                {
                                    IsApply = false,
                                    IsAgainst = true,

                                    PrizeName = "獎品4",
                                    PrizeQty = 1,
                                    LEVEL_NO = "2",
                                    PrizeRate = 1,
                                    LEVEL_TYPE = ADDT26_D.LevelType.Prize,
                                    LEVEL_NAME = "我要兌換抽獎",
                                    Y_REPEAT = false,

                                });
                                Model.PrizeDetails.Add(new GameLotteryPrizeViewModel()
                                {
                                    IsApply = false,
                                    IsAgainst = true,

                                    PrizeName = "獎品5",
                                    PrizeQty = 1,
                                    LEVEL_NO = "2",
                                    PrizeRate = 1,
                                    LEVEL_TYPE = ADDT26_D.LevelType.Prize,
                                    LEVEL_NAME = "我要兌換抽獎",
                                    Y_REPEAT = false,

                                }); Model.PrizeDetails.Add(new GameLotteryPrizeViewModel()
                                {
                                    IsApply = false,
                                    IsAgainst = true,

                                    PrizeName = "獎品6",
                                    PrizeQty = 1,
                                    LEVEL_NO = "2",
                                    PrizeRate = 1,
                                    LEVEL_TYPE = ADDT26_D.LevelType.Prize,
                                    LEVEL_NAME = "我要兌換抽獎",
                                    Y_REPEAT = false,

                                });
                            }

                            @foreach (var Item in Model.PrizeDetails.Where(a => a.LEVEL_NO == "2").OrderBy(x => x.PrizeName).ToList())
                            {
                                Html.RenderPartial("_DetailsPrizeLottery", Item);
                            }
                        </div>
                        @*</div>*@
                    </div>
                </div>
                <div class="panel-footer">
                    <div class="row">
                        <div class="col-md-12 col-xs-12 text-right">
                            <span class="input-group-btn">
                                <button class="btn btn-info btn-sm" type="button" onclick="onAddDetailsPrizeLotteryItem1()">
                                    <i class="fa fa-plus-circle"></i>   增加兌換獎品
                                </button>
                            </span>
                        </div>
                    </div>
                </div>
                @*<div class="panel-footer">
               <div class="row">
                 <div class="col-md-12 col-xs-12 text-right">
                 <span class="input-group-btn">
                     <button class="btn btn-info btn-sm" type="button" onclick="onAddPrizeExchangeItem()">
                         <i class="fa fa-plus-circle"></i>   增加兌換關卡
                     </button>
                 </span>
                </div>
               </div>
            </div>*@
            </div>
        </div>
    </div>

    if (Model.GAME_TYPE == (byte)ADDT26.GameType.一般)
    {
        <div id="DetailsView">
            <div style="margin-top:20px;margin-bottom:30px;text-align:center">
                <span class="glyphicon glyphicon-circle-arrow-down fa-2x" aria-hidden="true" style="color:#004da0"></span>
            </div>
            <div class="panel panel-ZZZ">
                <div class="panel-heading text-center">
                    關卡資料
                </div>
                <div class="panel-body">
                    @Html.ValidationMessage("DetailsError", new { @class = "text-danger" })
                    @{

                        var value = Html.ValidationMessage("DetailsError").ToString();

                        if (value.Contains("field-validation-error"))
                        {
                            <br /> <br />
                        }
                    }

                    <br />
                    <div class="table-responsive">
                        <div class="css-table" style="width:100%;">
                            <div class="tr" style=" font-weight: bold;text-align:center;color:#004da0;text-overflow:ellipsis; ">
                                <div class="th" style="text-align:center">
                                    刪除
                                </div>
                                <div class="th" style="text-align:center;width:50px">
                                    可重複<br>闖關
                                </div>
                                @*<div class="th" style="text-align:center;width:50px">
                                    上傳圖片闖關
                                </div>*@
                                <div class="th" style="text-align:center">
                                    關卡名稱
                                </div>
                                <div class="th" style="text-align:center;">
                                    酷幣點數<br>(如果是扣點，必須寫(-)，例如:-10)
                                </div>
                                <div class="th" style="text-align:center">
                                    關卡圖片<br>(未上傳，則自動使用系統預設圖片)
                                </div>
                                <div class="th" style="text-align:center">
                                    過場動畫<br>秒數(秒)
                                </div>
                                <div class="th" style="text-align:center">
                                    結果停留<br>時間(秒)
                                </div>
                            </div>
                            <div id="editorRows" class="tbody">

                                @if (Model.Details == null || Model.Details?.Where(a => a.LEVEL_TYPE == ADDT26_D.LevelType.Pay).Count() == 0)
                                {
                                    if (Model.Details == null)
                                    {
                                        Model.Details = new List<GameEditDetailsViewModel>();
                                    }

                                    Model.Details.Add(new GameEditDetailsViewModel()
                                    {
                                        IsApply = false,

                                        LEVEL_TYPE = ADDT26_D.LevelType.Pay,
                                        LEVEL_NAME = "1.",
                                        Y_REPEAT = false,
                                        Y_Photo = false,
                                        CASH = 0,
                                    });
                                }
                                @{
                                    foreach (var Item in Model.Details?.Where(a => a.LEVEL_TYPE == ADDT26_D.LevelType.Pay).ToList())
                                    {
                                        Html.RenderPartial("_Details", Item);
                                    }
                                }
                            </div>
                        </div>
                    </div>
                </div>
                <div class="panel-footer">
                    <div class="row">
                        <div class="col-md-12 col-xs-12 text-right">
                            <span class="input-group-btn">
                                <button class="btn btn-info btn-sm" type="button" onclick="onAddItem()">
                                    <i class="fa fa-plus-circle"></i>   增加關卡
                                </button>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
    else if (Model.GAME_TYPE == (byte)ADDT26.GameType.有獎徵答)
    {
        <div id="DetailsView">
            <div style="margin-top:20px;margin-bottom:30px;text-align:center">
                <span class="glyphicon glyphicon-circle-arrow-down fa-2x" aria-hidden="true" style="color:#004da0"></span>
            </div>
            <div class="panel panel-ZZZ">
                <div class="panel-heading text-center">
                    有獎徵答-關卡資料
                </div>
                <div class="panel-body">
                    @Html.ValidationMessage("DetailsQAError", new { @class = "text-danger" })
                    @if (Html.ValidationMessage("DetailsQAError") != null)
                    {
                        <br /> <br />
                    }
                    <div id="editorRows" class="col-md-12">

                        @if (Model.DetailsQA != null)
                        {
                            foreach (var item in Model.DetailsQA)
                            {
                                Html.RenderPartial("_DetailsQA", item);
                            }
                        }
                    </div>
                </div>
                <div class="panel-footer">
                    <div class="row">
                        <div class="col-md-12 col-xs-12 text-right">
                            <span class="input-group-btn">
                                <button class="btn btn-info btn-sm" type="button" onclick="onAddItemOX()">
                                    <i class="fa fa-plus-circle"></i>    增加「是非題」
                                </button>
                                &nbsp;&nbsp;&nbsp;&nbsp;
                                <button class="btn btn-info btn-sm" type="button" onclick="onAddItemSelect()">
                                    <i class="fa fa-plus-circle"></i>   增加「選擇題」
                                </button>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }

    <div class="form-group">
        <div class="col-xs-12 text-center">
            <div class="form-inline">
                <input type="button" value="存檔" class="btn btn-default" onclick="Save(this)" />
                @if (!string.IsNullOrWhiteSpace(Model?.Main?.GAME_NO) && (!string.IsNullOrWhiteSpace(Model.Search.WhereGAME_NO)))
                {
                    <button class="btn btn-default" type="button" onclick="onDel()">整筆刪除</button>
                }
                <button class="btn btn-default" type="button" onclick="onBack()">放棄</button>
            </div>
        </div>
    </div>

}

<div style="width:100%;height:100%;display:none;z-index:999;position:absolute" id="loading">
    <div style="margin: 0px auto;text-align:center">
        <img src="@Url.Content(SharedGlobal.Logo_loading)" style="max-height:350px;" />
        <br />
        <h3>處理中…</h3>
    </div>
</div>

@section Scripts {
    <script language="JavaScript">

        var targetFormID = '#formEdit';

        $(".colorboxPHOTO").colorbox({ photo: true, maxWidth: "90%", maxHeight: "90%", opacity: 0.82 });
        $(".colorbox").colorbox({ iframe: true, width: "90%", height: "90%", opacity: 0.82 });

   $("#@Html.IdFor(m => m.Main.GAME_DATES) ,#@Html.IdFor(m => m.Main.GAME_DATEE)").datepicker({
                 dateFormat: "yy/mm/dd",
                 changeMonth: true,
                 changeYear: true,
                 showOn: "button",
                 buttonImage: "../Content/img/icon/calendar.gif",
                 buttonImageOnly: true,
             });

             var Today = new Date();

             if ($("#@Html.IdFor(m => m.Main.GAME_DATES)").val() == "") {
                 $("#@Html.IdFor(m => m.Main.GAME_DATES)").datepicker("setDate", Today);
             }

             if ($("#@Html.IdFor(m => m.Main.GAME_DATEE)").val() == "") {
                 Today.setMonth(Today.getMonth() + 1);
                 $("#@Html.IdFor(m => m.Main.GAME_DATEE)").datepicker('setDate', Today);
             }

        //存檔
        function Save(ButtonName)
        {
            $(targetFormID).attr("action", "@Url.Action("EditSave", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();

            ButtonName.disabled = true;
        }

        //放棄回上一頁
        function onBack() {
            $(targetFormID).attr("action", "@Url.Action("GameIndex", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        //備援
        function onBackupLink() {
             $(targetFormID).attr("action", "@Url.Action("BackupLink", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        //刪除這筆活動資料
        function onDel() {
            var OK = confirm("您確定要刪除這筆活動資料，按確定後，活動資料、報名著資料及報名著參與的活動內容，將全部被刪除!!，刪除後無法還原!!!!")

            if (OK) {
               // $(targetFormID).validate().resetForm();
                $('html, body').scrollTop(0);
                $(targetFormID).hide()
                $('#loading').fadeIn(3000)
                setTimeout(function () {
                     $(targetFormID).attr("action", "@Url.Action("EditDel", (string)ViewBag.BRE_NO)")
                     $(targetFormID).submit();
                }, 3000);
            }
         }

       //增加明細(一般模式)
       function onAddItem() {

           var trLength = $("#editorRows .tr").length
           trLength = trLength + 1;

         var data = {
               "LEVEL_TYPE": '@ADDT26_D.LevelType.Pay',
               "LEVEL_NAME": trLength+"."
            };

            $.ajax({
                url: '@Url.Action("_Details")',
                data: data,
                cache: false,
                success: function (html) {
                    $("#editorRows").append(html);
                }
            });
        }
        //新增抽獎兌換
        function onAddPrizeExchangeItem() {
            var trLength = $("#PrizeItemEditorRows .tr").length
            trLength = trLength + 1;
            var data = {
               "IsApply": false,
                "IsAgainst": false,
                "IsCouppons": true,
               "LEVEL_TYPE": '@ADDT26_D.LevelType.Prize',
                "LEVEL_NAME": "我要兌換抽獎",
                "Y_REPEAT": false,
               "CASH": 0,
            };
             var data1 = {
               "IsApply": false,
                 "IsAgainst": true,
                 "PrizeName": "獎品1",
                 "PrizeQty": 1,
                 "PrizeRate": 1,

               "LEVEL_TYPE": '@ADDT26_D.LevelType.Prize',
                "LEVEL_NAME": "我要兌換抽獎",
                "Y_REPEAT": false,
               "CASH": 0,
            };
            var data2 = {
               "IsApply": false,
                 "IsAgainst": true,
                 "PrizeName": "獎品2",
                 "PrizeQty": 1,
                 "PrizeRate": 1,

               "LEVEL_TYPE": '@ADDT26_D.LevelType.Prize',
                "LEVEL_NAME": "我要兌換抽獎",
                "Y_REPEAT": false,
               "CASH": 0,
            };
             var data3 = {
               "IsApply": false,
                 "IsAgainst": true,
                 "PrizeName": "獎品3",
                 "PrizeQty": 1,
                 "PrizeRate": 1,

               "LEVEL_TYPE": '@ADDT26_D.LevelType.Prize',
                "LEVEL_NAME": "我要兌換抽獎",
                "Y_REPEAT": false,
               "CASH": 0,
            };

            $.ajax({
                url: '@Url.Action("_DetailsPrize2")',
                data: data,
                cache: false,
                success: function (html) {
                    $("#PrizeItemEditorRows").append(html);
                }
            });
               $.ajax({
                url: '@Url.Action("_DetailsPrizeLottery")',
                   data: data1,
                cache: false,
                success: function (html) {
                    $("#PrizeItemEditorRows").append(html);
                }
               });
              $.ajax({
                url: '@Url.Action("_DetailsPrizeLottery")',
                   data: data2,
                cache: false,
                success: function (html) {
                    $("#PrizeItemEditorRows").append(html);
                }
              });
              $.ajax({
                url: '@Url.Action("_DetailsPrizeLottery")',
                   data: data3,
                cache: false,
                success: function (html) {
                    $("#PrizeItemEditorRows").append(html);
                }
              });
            $("#PrizeItemEditorRows").append("");
        }
          //增加兌換
        function onAddPrizeItem() {

            var trLength = $("#PrizeEditorRows .tr").length
            trLength = trLength + 1;

             var data = {
               "IsApply": false,
               "IsAgainst": true,
               "LEVEL_TYPE": '@ADDT26_D.LevelType.Prize',
               "LEVEL_NAME": "兌換關卡",
               "Y_REPEAT": true,
               "CASH": 0,
             };

            $.ajax({
                url: '@Url.Action("_DetailsPrize")',
                data: data,
                cache: false,
                success: function (html) {
                    $("#PrizeEditorRows").append(html);
                }
            });
        }   //增加兌換
        function onAddDetailsPrizeLotteryItem() {

            var trLength = $("#PrizeDetailsEditorRows .tr").length
            trLength = trLength + 1;

             var data = {
               "IsApply": false,
               "IsAgainst": true,
               "LEVEL_TYPE": '@ADDT26_D.LevelType.ItemPrize',
                 "LEVEL_NAME": "兌換關卡抽獎",
                 "Y_REPEAT": false,
                 "PrizeName": "Sample",
                 "PrizeQty": "1",
                 "PrizeRate": "1",
                 "LEVEL_NO":"1"
             };

            $.ajax({
                url: '@Url.Action("_DetailsPrizeLottery")',
                data: data,
                cache: false,
                success: function (html) {
                    $("#PrizeDetailsEditorRows").append(html);
                }
            });
        }
          function onAddDetailsPrizeLotteryItem1() {

            var trLength = $("#PrizeDetailsEditorRows1 .tr").length
            trLength = trLength + 1;

             var data = {
               "IsApply": false,
               "IsAgainst": true,
               "LEVEL_TYPE": '@ADDT26_D.LevelType.ItemPrize',
                 "LEVEL_NAME": "兌換關卡抽獎",
                 "Y_REPEAT": false,
                 "PrizeName": "Sample",
                 "PrizeQty": "1",
                 "PrizeRate": "1",
                 "LEVEL_NO":"2"
             };

            $.ajax({
                url: '@Url.Action("_DetailsPrizeLottery")',
                data: data,
                cache: false,
                success: function (html) {
                    $("#PrizeDetailsEditorRows1").append(html);
                }
            });
        }
        //增加是非題
        function onAddItemOX() {

            var data = {
                "InputType": "OX",
                "Y_REPEAT": "true",
            };

             $.ajax({
                 url: '@Url.Action("_DetailsQA")',
                 data: data,
                cache: false,
                success: function (html) {
                    $("#editorRows").append(html);
                }
            });
        }

        //增加選擇題
        function onAddItemSelect() {

            var data = {
                "InputType": "Select",
                "Y_REPEAT": "true",
            };

             $.ajax({
                 url: '@Url.Action("_DetailsQA")',
                 data: data,
                cache: false,
                success: function (html) {
                    $("#editorRows").append(html);
                }
            });
        }

        function onIsAgainstCheckBox(This, TrId) {

            if (This.checked) {
                $('#' + TrId).css("background-color", "#B0E0E6");
            }
            else {
                $('#' + TrId).css("background-color", "");
            }
        }

        function onCheckBoxTrue(ThisCheckBox, ClassStr, ClassRETURN_DESC) {

            var ThisCheckBoxId = ('#' + ThisCheckBox.id);

            var RETURN_DESC_ID = ThisCheckBoxId.replace("TRUE_ANS", "RETURN_DESC")

            if (ThisCheckBox.checked) {

                $("." + ClassRETURN_DESC).each(function () {
                    $(this).val('請看清楚題目')
                })

                $("." + ClassStr).each(function () {
                    $(this).prop("checked", false);
                })

                $(ThisCheckBoxId).prop("checked", true);
                $(RETURN_DESC_ID).val('')
            }
            else {
                if ($("." + ClassStr).is(':checked') == false) {
                    alert('需勾選一個選項為正確答案')
                    $(ThisCheckBoxId).prop("checked", true);
                }
            }
        }

        //del 項目
        function deleteRow(Id) {
            $('#' + Id).remove();
        }

        //del 圖片
        function DelFile(Index) {

             $("#DivPHOTO_FILE_URL_" + Index).remove();
             $('#DivUpPhotoFiles_' + Index).show();
             $('#Details_' + Index +'__LEVEL_IMG').val('')

        }
    </script>
}