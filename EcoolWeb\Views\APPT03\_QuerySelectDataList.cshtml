﻿@model ECOOL_APP.com.ecool.Models.DTO.APPT03EditListViewModel

<div style="text-align: center; margin-top: 20px; margin-bottom: 30px;">
    <span class="glyphicon glyphicon-circle-arrow-down fa-2x" aria-hidden="true" style="color:#004da0"></span>
</div>

<div class="panel panel-ACC">
    <div class="form-horizontal">
        <label class="text-danger">
            <samp>* 已選取清單，點選「刪除」 </samp>
            <samp>
                總共已選 <span class="badge">@Model.SelectDataCount</span> 筆
            </samp>
        </label>
        <br />
        <div class="btn-group btn-group-xs" role="group">
            @Html.HiddenFor(model => model.DivHeight)
            <button type="button" class="btn btn-default" id="DivHeight_plus" onclick="setDivHeight('+')"><i class="glyphicon glyphicon-plus"></i></button>
            <button type="button" class="btn btn-default" id="DivHeight_minus" onclick="setDivHeight('-')"><i class="glyphicon glyphicon-minus"></i></button>
        </div>

        <div class="table-responsive" id="DivSelectData">
            <table class="table-ecool  table-hover table-ecool-ACC">
                <thead>
                    <tr>
                        <th>
                            <samp class="form-group" style="cursor:pointer;" onclick="FunSort('SHORT_NAME')">
                                @Html.DisplayNameFor(model => model.DataList.First().SHORT_NAME)
                            </samp>
                        </th>
                        <th>
                            <samp class="form-group">
                                @Html.DisplayNameFor(model => model.DataList.First().BTN_TYPE)
                            </samp>
                        </th>
                        <th>
                            <samp class="form-group" style="cursor:pointer;" onclick="FunSort('CONTENT_TXT')">
                                @Html.DisplayNameFor(model => model.DataList.First().CONTENT_TXT)
                            </samp>
                        </th>
                    
                    </tr>
                </thead>
                <tbody>
                    @foreach (var item in Model.DataList)
                    {
                        <tr onclick="onBtnLinkDel('@item.SCHOOL_NO', '@item.BTN_TYPE','@item.ITEM_VAL')" style="cursor:pointer">
                            <td align="center">
                                @Html.DisplayFor(modelItem => item.SHORT_NAME)
                            </td>
                            <td align="center">
                                @APPT03_Q.BTN_TYPE_VAL.GetDesc(item.BTN_TYPE)
                            </td>
                            <td align="center">
                                @Html.DisplayFor(modelItem => item.CONTENT_TXT)
                            </td>
                        </tr>
                    }

                </tbody>
            </table>
        </div>
    </div>
</div>

<script type="text/javascript">
    DivHeight()


    function DivHeight()
    {
        var FixedHeight = 200;
        var Height = $('#DivSelectData').innerHeight();

        var DivHeight = $('#DivHeight').val();
        if (DivHeight == '') DivHeight = '@ECOOL_APP.com.ecool.Models.DTO.ADDI09EditViewModel.DivHeightVal.DivHeightM'


        $('#DivHeight_plus').hide();
        $('#DivHeight_minus').hide();
        if (DivHeight == "-") {
            $('#DivHeight_plus').show();
        }
        else {
            $('#DivHeight_minus').show();
        }

        if (Height > FixedHeight && DivHeight == "-") {
            $('#DivSelectData').css("height", FixedHeight).css("overflow", "auto")
        }
        else {
            $('#DivSelectData').removeClass();
        }
    }

    function setDivHeight(Value) {

        $('#DivHeight').val(Value);
        onBtnLinkDel('','','')
    }

    function onBtnLinkDel(SCHOOL_NO,BTN_TYPE,ITEM_VAL)
    {

        var data = {
            "REF_TABLE": $('#REF_TABLE').val(),
            "REF_KEY": $('#REF_KEY').val(),
            "STATUS": $('#STATUS').val(),
            "BTN_TYPE": BTN_TYPE,
            "ITEM_VAL": ITEM_VAL,
            "whereSCHOOL_NO": SCHOOL_NO,
            "DivHeight": $('#DivHeight').val(),
            "DataType": '@ECOOL_APP.com.ecool.Models.DTO.APPT03EditListViewModel.DataTypeVal.DataTypeDel'
        };


        $.ajax({
            url: '@Url.Action("_QuerySelectDataList", (string)ViewBag.BRE_NO)',
            data: data,
            type: 'post',
            async: false,
            cache: false,
            dataType: 'html',
            success: function (data) {
                $('#QuerySelectDataList').html(data);

                funAjax()
            }
        });
    }
</script>