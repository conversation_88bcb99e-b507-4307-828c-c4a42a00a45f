﻿using EcoolWeb.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using ECOOL_APP;
using log4net;

namespace EcoolWeb.Mvc.Filter
{
    public class ElapsedTimeLogAttribute : System.Web.Mvc.ActionFilterAttribute
    {
        private log4net.ILog logger = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        private long START;
        private long END;

        public override void OnActionExecuting(ActionExecutingContext ctx)
        {
            START = Now;
        }

        public override void OnActionExecuted(ActionExecutedContext ctx)
        {
            END = Now;
            double sec = new TimeSpan(END - START).TotalSeconds;
            sec = sec * 1000;
            int elapsedTime = Int32.Parse(sec.ToString("0"));
            UserProfile user = UserProfileHelper.Get();
            if (user == null)
            {
                //導引未登入頁面

            }
            /*
            logger.Info(" -----------  Elapsed Time for " + user.PGMNM + "(" + user.PGMID + ") : " + sec + " milliseconds------------- ");
            */
        }

        private long Now
        {
            get
            {
                return DateTime.Now.Ticks;
            }
        }

    }
}