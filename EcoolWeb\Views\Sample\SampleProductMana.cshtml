﻿@using System.Collections;
@{
    ViewBag.Title = "SampleProductMana";
    Layout = "~/Views/Shared/_Layout.cshtml";
    List<Hashtable> htbPRODUCT = ViewData["PRODUCT_HTB"] == null ? new List<Hashtable>() : (List<Hashtable>)ViewData["PRODUCT_HTB"];
}
<h2>獎品管理</h2>
<form action="#" name="contentForm" id="contentForm" method="post">
    <div class="table-responsive">
        <table class="table">
            <tr>
                <td colspan="2" class="text-center">@(Request["AWARD_NO"] == null ? "新增獎品" : Request["MANA_STS"] == "U" ? "修改獎品" : "刪除獎品")</td>
            </tr>
            <tr>
                <th>獎品名稱</th>
                <td>
                    <input type="text" id="ParamAWARD_NAME" name="ParamAWARD_NAME" class="validate[required]" maxlength="50" value="@(null != htbPRODUCT && htbPRODUCT.Count != 0 ? htbPRODUCT[0]["AWARD_NAME"] : "")" />
                </td>
            </tr>
            <tr>
                <th>兌換點數</th>
                <td>
                    <input type="text" id="ParamCOST_CASH" name="ParamCOST_CASH" class="validate[required,number]" maxlength="6" value="@(null != htbPRODUCT && htbPRODUCT.Count != 0 ? htbPRODUCT[0]["COST_CASH"] : "")" />
                </td>
            </tr>
            <tr>
                <th>獎品數量</th>
                <td>
                    <input type="text" id="ParamCOUNT_TOTAL" name="ParamQTY_STORAGE" class="validate[required,number]" maxlength="6" value="@(null != htbPRODUCT && htbPRODUCT.Count != 0 ? htbPRODUCT[0]["QTY_STORAGE"] : "")" />
                </td>
            </tr>
            <tr>
                <th>開始兌換</th>
                <td>
                    <input type="text" id="ParamSDATETIME" name="ParamSDATETIME" class="validate[required]" readonly="readonly" value="@(null != htbPRODUCT && htbPRODUCT.Count != 0 ? htbPRODUCT[0]["SDATETIME"] : "")" />
                </td>
            </tr>
            <tr>
                <th>兌換期限</th>
                <td>
                    <input type="text" id="ParamEDATETIME" name="ParamEDATETIME" class="validate[required]" readonly="readonly" value="@(null != htbPRODUCT && htbPRODUCT.Count != 0 ? htbPRODUCT[0]["EDATETIME"] : "")" />
                    
                </td>
            </tr>
            <tr>
                <th>備註說明</th>
                <td>
                    <input type="text" id="ParamDESCRIPTION" name="ParamDESCRIPTION" value="@(null != htbPRODUCT && htbPRODUCT.Count != 0 ? htbPRODUCT[0]["DESCRIPTION"] : "")" />
                </td>
            </tr>
            <tr>
                <th>上傳圖片</th>
                <td>
                    @Html.Action("Upload", "Comm")
                </td>
            </tr>
            <tr>
                <td colspan="2" class="text-center">
                    <form action="#" name="contentForm" id="contentForm" method="post">
                        @{
                            if (Request["AWARD_NO"] == null)
                            {
                                <button class="btn btn-default" type="button" id="btnINSERT" onclick="ToDoInsert()">@Resources.Resource.INSERT</button>
                            }
                            else if (Request["AWARD_NO"] != null && Request["MANA_STS"] == "U")
                            {
                                <button class="btn btn-default" type="button" id="btnMODIFY" onclick="ToDoModify()">@Resources.Resource.MODIFY</button>
                            }
                            else if (Request["AWARD_NO"] != null && Request["MANA_STS"] == "D")
                            {
                                <button class="btn btn-default" type="button" id="btnDELETE" onclick="ToDoDelete()">@Resources.Resource.DELETE</button>
                            }
                        }
                    </form>
                    <button class="btn btn-default" type="button" id="btnCancel" onclick="window.location.href='SampleProduct'">@Resources.Resource.Cancel</button>
                </td>
            </tr>
            @{

                if (Request["AWARD_NO"] != null)
                {
                    <tr>
                        <td colspan="2">
                            <img src="@(null != htbPRODUCT && htbPRODUCT.Count != 0 ? htbPRODUCT[0]["IMG_FILE"] : "")" class="img-responsive" alt="Responsive image" />
                        </td>
                    </tr>
                }
            }
        </table>
    </div>
    <input type="hidden" id="ParamAWARD_NO" name="ParamAWARD_NO" value="@(null != htbPRODUCT && htbPRODUCT.Count != 0 ? htbPRODUCT[0]["AWARD_NO"] : "")" />
</form>

@section Scripts
    {
    <script type="text/javascript">
        function doInsert() {
            try {
                if ($("#ParamAWARD_NO").val() != "" && $("#ParamAWARD_NO").val() != "0") {
                    alert('新增程序不正確');
                    return;
                }
                if ($("#file").val() != "") {
                    if (false == FileUpload_click()) {
                        return;
                    }
                }
            }
            catch (err) {
            }
            document.contentForm.enctype = "multipart/form-data";
            document.contentForm.action = "SampleProductInsert";
            document.contentForm.submit();
        }

        function doModify() {
            try {
                if ($("#ParamAWARD_NO").val() == "") {
                    alert('修改程序不正確');
                    return;
                }
                if ($("#file").val() != "") {
                    if (false == FileUpload_click()) {
                        return;
                    }
                }
            }
            catch (err) {
            }
            document.contentForm.enctype = "multipart/form-data";
            document.contentForm.action = "SampleProductModify";
            document.contentForm.submit();
        }

        function doDelete() {
            if ($("#ParamAWARD_NO").val() == "") {
                alert('刪除程序不正確');
                return;
            }

            document.contentForm.action = "SampleProductDelete";
            document.contentForm.submit();
        }

        $(function () {

            $("#ParamSDATETIME").datepicker({
                dateFormat: "yy/mm/dd",
                changeMonth: true,
                changeYear: true,
                showOn: "button",
                buttonImage: "../Content/img/icon/calendar.gif",
                buttonImageOnly: true,

            });

            $("#ParamEDATETIME").datepicker({
                dateFormat: "yy/mm/dd",
                showSecond: true,
                showMillisec: true,
                timeFormat: 'HH:mm:ss',
                changeMonth: true,
                changeYear: true,
                showOn: "button",
                buttonImage: "../Content/img/icon/calendar.gif",
                buttonImageOnly: true
            });

            @if (Request["AWARD_NO"] == null)
            {
                <text>
            var Today = new Date();
            $("#ParamSDATETIME").datepicker("setDate", Today);
            Today.setMonth(Today.getMonth() + 12);
            $("#ParamEDATETIME").datepicker('setDate', Today);
            </text>
            }
        });
    </script>
}