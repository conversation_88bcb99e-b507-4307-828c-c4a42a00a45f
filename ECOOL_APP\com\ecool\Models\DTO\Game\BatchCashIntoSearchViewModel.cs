﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class BatchCashIntoSearchViewModel
    {
        /// <summary>
        ///卡號
        /// </summary>
        [DisplayName("卡號")]
        public string WhereCARD_NO { get; set; }

        /// <summary>
        ///學校
        /// </summary>
        [DisplayName("學校")]
        public string WhereSCHOOL_NO { get; set; }

        /// <summary>
        ///帳號
        /// </summary>
        [DisplayName("學號/帳號")]
        public string WhereUSER_NO { get; set; }

        /// <summary>
        ///姓名
        /// </summary>
        [DisplayName("姓名")]
        public string WhereNAME { get; set; }

        /// <summary>
        ///年級
        /// </summary>
        [DisplayName("年級")]
        public byte? WhereGRADE { get; set; }

        /// <summary>
        ///班級
        /// </summary>
        [DisplayName("班級")]
        public string WhereCLASS_NO { get; set; }

        /// <summary>
        ///座號
        /// </summary>
        [DisplayName("座號")]
        public string WhereSEAT_NO { get; set; }

        /// <summary>
        ///電話
        /// </summary>
        [DisplayName("電話")]
        public string WherePHONE { get; set; }

        /// <summary>
        /// 身份
        /// </summary>
        [DisplayName("身份")]
        public string WhereGAME_USER_TYPE { get; set; }

        /// <summary>
        /// 排序欄位
        /// </summary>
        public string OrdercColumn { get; set; }

        /// <summary>
        /// Desc or ASC
        /// </summary>
        public string SyntaxName { get; set; }

        /// <summary>
        /// 頁次
        /// </summary>
        public int Page { get; set; }

        /// <summary>
        /// 過濾活動酷幣餘額大於 0
        /// </summary>
        public bool WhereUnCashZero { get; set; }
    }
}