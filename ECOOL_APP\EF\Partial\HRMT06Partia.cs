﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.Mvc;

namespace ECOOL_APP.EF
{
    public partial class HRMT06
    {

        /// <summary>
        /// 判斷家長是否綁定任何學生
        /// </summary>
        /// <param name="SchoolNo">學校代碼</param>
        /// <param name="UserNo">家長帳號</param>
        /// <returns>true 綁定, false 未綁定</returns>
        static public bool CkPanyStudent(string SchoolNo,string UserNo, ECOOL_DEVEntities db=null)
        {

            bool dbNull = false;
            bool ReturnVal=false;

            if (db == null) { db = new ECOOL_DEVEntities(); dbNull = true; }

            if (db.HRMT06.Where(a => a.SCHOOL_NO == SchoolNo && a.PARENTS_USER_NO == UserNo).Any())
            {
                ReturnVal = true;
            }

            if (dbNull)
            {
                db.Dispose();
            }

            return ReturnVal;
        }

        static public List<HRMT06> GetMyPanyStudent(string SCHOOL_NO,string USER_NO, ECOOL_DEVEntities db = null)
        {
            if (string.IsNullOrWhiteSpace(SCHOOL_NO))
            {
                return null;
            }

            if (string.IsNullOrWhiteSpace(USER_NO))
            {
                return null;
            }

            bool dbNull = false;

            if (db == null) { db = new ECOOL_DEVEntities(); dbNull = true; }

            List<HRMT06> T06 = db.HRMT06.Where(a => a.SCHOOL_NO == SCHOOL_NO && a.PARENTS_USER_NO == USER_NO).ToList();

            if (dbNull) db.Dispose();

            if (T06 == null || T06.Count() == 0)
            {
                return null;
            }
            else
            {
                return T06;
            }

        }

        static public List<HRMT06> GetMyPanyStudent(UserProfile user, ECOOL_DEVEntities db = null)
        {
            if (user==null)
            {
                return null;
            }

            if (user.USER_TYPE!=UserType.Parents)
            {
                return null;
            }

            bool dbNull = false;

            if (db == null) { db = new ECOOL_DEVEntities(); dbNull = true; }

            List<HRMT06> T06 = db.HRMT06.Where(a => a.SCHOOL_NO == user.SCHOOL_NO && a.PARENTS_USER_NO == user.USER_NO).ToList();

            if (dbNull) db.Dispose();

            if (T06==null || T06.Count()==0)
            {
                return null;
            }
            else
            {
                return T06;
            }

        }


        static public List<SelectListItem> GetMyPanyStudentItems(string defaultSelectValue, UserProfile user, ECOOL_DEVEntities db = null)
        {

            List<SelectListItem> GradeItems = new List<SelectListItem>();


            List<SelectListItem> TempSelect = (from a in db.HRMT06
                             join b in db.HRMT01 on new { SCHOOL_NO = a.SCHOOL_NO ,USER_NO= a.STUDENT_USER_NO} equals new { SCHOOL_NO = b.SCHOOL_NO, USER_NO = b.USER_NO }
                             where a.SCHOOL_NO == user.SCHOOL_NO && a.PARENTS_USER_NO == user.USER_NO
                             select new SelectListItem { Text =b.NAME ,Value=a.STUDENT_USER_NO,Selected = a.STUDENT_USER_NO == defaultSelectValue })
                             .Distinct().OrderBy(x => x.Value).ToList();

            return TempSelect;
        }

        static public string GetStringMyPanyStudent(UserProfile user, ECOOL_DEVEntities db = null)
        {

            var Data = GetMyPanyStudent(user, db);

            if (Data==null || Data.Count()==0)
            {
                return "NA";
            }
            else
            {
                string StringMyStudent = string.Join(",", Data.Select(a=>a.STUDENT_USER_NO));

                return StringMyStudent;
            }
        }

        static public string [] GetArrMyPanyStudent(UserProfile user, ECOOL_DEVEntities db = null)
        {

            var Data = GetMyPanyStudent(user, db);

            if (Data == null || Data.Count() == 0)
            {
                return null;
            }
            else
            {
                string[] ArrMyStudent = Data.Select(a => a.STUDENT_USER_NO).ToArray();
                return ArrMyStudent;
            }
        }


    }
 }
