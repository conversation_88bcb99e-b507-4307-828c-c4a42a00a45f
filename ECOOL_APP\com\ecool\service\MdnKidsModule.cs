﻿using AutoMapper;
using ECOOL_APP.com.ecool.LogicCenter;
using ECOOL_APP.com.ecool.util;
using ECOOL_APP.EF;
using log4net;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Web;
using System.Xml.Linq;

namespace com.ecool.service
{
    public class MdnKidsModule
    {
        protected readonly ILog logger = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        private static Dictionary<string, string> mailAddresssDict;

        public MdnKidsField MdnKids { get; set; }

        public MdnKidsModule()
        {
            string xmlpath = Path.Combine(Path.GetDirectoryName(Assembly.GetAssembly(typeof(MdnKidsModule)).CodeBase), "MailAddress.xml");
            mailAddresssDict = LogicCenter.LoadXML(xmlpath, "addresses", "address");

            if (MdnKids == null) MdnKids = new MdnKidsField();
            Mapper.Initialize(cfg =>
            {
                cfg.CreateMap<HRMT01, MdnKidsField>()
                .ForMember(x => x.Email, opt => opt.MapFrom(y => MdnKidsField.IsValidEmailAddress(y.E_MAIL) ? y.E_MAIL : ""))
                .ForMember(x => x.ResidenceAddress, opt => opt.MapFrom(y => MdnKidsField.IsValidSize(y.RESIDENCE_ADDRESS, 100) ? y.RESIDENCE_ADDRESS : ""))
                .ForMember(x => x.Address, opt => opt.MapFrom(y => MdnKidsField.IsValidSize(y.ADDRESS, 100) ? y.ADDRESS : ""))
                .ForMember(x => x.Phone, opt => opt.MapFrom(y => MdnKidsField.IsValidSize(y.PHONE, 10) ? y.PHONE : ""))
                .ForMember(x => x.Name, opt => opt.Ignore())  // 不Mapping的屬於學生的資料
                .ForMember(x => x.Grade, opt => opt.Ignore())
                .ForMember(x => x.Class_No, opt => opt.Ignore())
                .ForMember(x => x.IDNO, opt => opt.Ignore());
            });
        }

        public MdnKidsModule(MdnKidsField mdnKids) : this()
        {
            this.MdnKids = mdnKids;
        }

        /// <summary>
        /// HRMT01 Table 轉 國語日報Model
        /// </summary>
        /// <param name="student">學生Table</param>
        /// <param name="teacher">老師Table</param>
        /// <param name="schoolName">學校名稱</param>
        /// <returns></returns>
        public MdnKidsField Mapping(HRMT01 student, HRMT01 teacher, string schoolName, string articleTitle, string articleContent)
        {
            //老師資料
            MdnKids = Mapper.Map<HRMT01, MdnKidsField>(teacher);
            //學生資料
            MdnKids.Name = student.NAME;
            MdnKids.Grade = Convert.ToInt16(student.GRADE);
            MdnKids.Class_No = student.CLASS_NO;
            MdnKids.IDNO = student.IDNO;
            MdnKids.SchoolName = schoolName;
            MdnKids.ArticleTitle = articleTitle;
            MdnKids.ArticleContent = articleContent;
            return MdnKids;
        }

        /// <summary>
        /// 更新EF Teacher儲存欄位
        /// </summary>
        /// <param name="teacher">老師Table</param>
        public void UpdateEntity(HRMT01 teacher)
        {
            if (teacher != null)
            {
                teacher.ADDRESS = MdnKids.Address;
                teacher.RESIDENCE_ADDRESS = MdnKids.ResidenceAddress;
                teacher.PHONE = MdnKids.Phone;
                teacher.E_MAIL = MdnKids.Email;
            }
        }

        public string BuildMail()
        {
            StringBuilder sb = new StringBuilder();
            sb.Append($"<div>");
            sb.Append($"    <div>學校名稱: {MdnKids.SchoolName} </div>");
            sb.Append($"    <div>文章作者: {MdnKids.Name} </div>");
            sb.Append($"    <div>年級: {MdnKids.Grade} </div>");
            sb.Append($"    <div>班級: {MdnKids.Class_No} </div>");
            sb.Append($"    <div>身分證: {MdnKids.IDNO} </div>");
            sb.Append($"    <div>住家地址: {MdnKids.Address} </div>");
            sb.Append($"    <div>戶籍地址: {MdnKids.ResidenceAddress} </div>");
            sb.Append($"    <div>電話: {MdnKids.Phone} </div>");
            sb.Append($"    <div>郵件: {MdnKids.Email} </div>");
            sb.Append($"    <br /><br />");
            sb.Append($"    <div>文章標題: {MdnKids.ArticleTitle}</div>");
            sb.Append($"    <hr />");
            sb.Append($"    {MdnKids.ArticleContent}");
            sb.Append($"</div>");

            return sb.ToString();
        }

        public string BuildMailForSendEmail()
        {
            StringBuilder sb = new StringBuilder();
            sb.Append($"<div>");
            sb.Append($" <div>※作品經採用刊登將同時刊於國語日報,國語日報網站及相關行動載具及臉書,並收錄國語日報 </div>");
            sb.Append($"<div>※請勿抄襲(投稿圖文需原創, 如有侵權行為, 由創作者自負法律責任), 及一稿多投, 本報 得對來稿有刪修權, 投稿三週內若未通知採用, 請自行處理。(文字引自於國語日報網站)</div>");
            sb.Append($"    <br /><br />");
            sb.Append($"<div> 需填寫資料如下:</div> ");

            sb.Append($"    <div>學校名稱: {MdnKids.SchoolName} </div>");
            sb.Append($"    <div>文章作者: {MdnKids.Name} </div>");
            sb.Append($"    <div>年級: {MdnKids.Grade} </div>");
            sb.Append($"    <div>班級: {MdnKids.Class_No} </div>");
            sb.Append($"    <div>身分證: {MdnKids.IDNO} </div>");
            sb.Append($"    <div>住家地址: {MdnKids.Address} </div>");
            sb.Append($"    <div>戶籍地址: {MdnKids.ResidenceAddress} </div>");
            sb.Append($"    <div>電話: {MdnKids.Phone} </div>");
            sb.Append($"    <div>郵件: {MdnKids.Email} </div>");
            sb.Append($"    <br /><br />");
            sb.Append($"   <div>文章內容如下:</div>");
            sb.Append($"    <div>文章標題: {MdnKids.ArticleTitle}</div>");
            sb.Append($"    <hr />");

            sb.Append($"    {MdnKids.ArticleContent}");

            sb.Append($"</div>");
            return sb.ToString();
        }

        /// <summary>
        /// 寄件送出
        /// </summary>
        /// <returns></returns>
        public Tuple<bool, string> SendMailPerson(string Email2)
        {
            try
            {
                MailHelper mailHelper = new MailHelper();
                List<string> mailTo = new List<string>();
                mailTo.Add(Email2);
                bool state = mailHelper.SendMailByGmail(mailTo, "國語日報文章投稿寄到電子郵件", BuildMailForSendEmail());
                return Tuple.Create(state, state ? "成功發送至電子郵件。" : "信件發送失敗。");
            }
            catch (Exception ex)
            {
                logger.Error($"國語日報模組寄到電子郵件{nameof(MdnKidsModule)}，發送郵件時發生例外錯誤: {ex.Message}");
                return Tuple.Create(false, "信件發送失敗。");
            }
        }

        /// <summary>
        /// 寄件送出
        /// </summary>
        /// <returns></returns>
        public Tuple<bool, string> SendMail()
        {
            try
            {
                MailHelper mailHelper = new MailHelper();
                List<string> mailTo = new List<string>();
#if !DEBUG
                if (MdnKids.Grade > 4) // 高年級
                {
                    mailTo.Add(mailAddresssDict.SingleOrDefault(a => a.Key == "MdnKid9").Value);
                }
                else // 低年級
                {
                    mailTo.Add(mailAddresssDict.SingleOrDefault(a => a.Key == "MdnKid8").Value);
                }
                mailTo.Add(MdnKids.Email);
                mailTo.Add(mailAddresssDict.SingleOrDefault(a => a.Key == "joy").Value);
#else
                mailTo.Add(mailAddresssDict.SingleOrDefault(a => a.Key == "Test").Value);
#endif
                bool state = mailHelper.SendMailByGmail(mailTo, "國語日報文章投稿", BuildMail());

                return Tuple.Create(state, state ? "成功發送至國語日報。" : "信件發送失敗。");
            }
            catch (Exception ex)
            {
                logger.Error($"國語日報模組{nameof(MdnKidsModule)}，發送郵件時發生例外錯誤: {ex.Message}");
                return Tuple.Create(false, "信件發送失敗。");
            }
        }
    }

    /// <summary>
    /// 國語日報投稿所需欄位
    /// </summary>
    public class MdnKidsField
    {
        /// <summary>
        /// 文章作者名稱
        /// </summary>
        [DisplayName("文章作者")]
        public string Name { get; set; }

        /// <summary>
        /// 學校名稱
        /// </summary>
        [DisplayName("學校名稱")]
        public string SchoolName { get; set; }

        /// <summary>
        /// 年級
        /// </summary>
        [DisplayName("年級")]
        public int Grade { get; set; }

        /// <summary>
        /// 班級
        /// </summary>
        [DisplayName("班級")]
        public string Class_No { get; set; }

        /// <summary>
        /// 身分證字號 可由發布者填寫
        /// </summary>
        [DisplayName("身分證")]
        [Required]
        [StringLength(10)]
        public string IDNO { get; set; }

        /// <summary>
        /// 住家地址 可由發布者填寫(會記錄)
        /// </summary>
        [DisplayName("住家地址")]
        [Required]
        public string Address { get; set; }

        /// <summary>
        /// 戶籍地址 可由發布者填寫(會記錄)
        /// </summary>
        [DisplayName("戶籍地址")]
        [Required]
        public string ResidenceAddress { get; set; }

        /// <summary>
        /// 電話 可由發布者填寫(會記錄)
        /// </summary>
        [DisplayName("電話")]
        [Required]
        [StringLength(10)]
        public string Phone { get; set; }

        /// <summary>
        /// 郵件 可由發布者填寫(會記錄)
        /// </summary>
        [DisplayName("郵件")]
        [Required]
        [EmailAddress]
        public string Email { get; set; }

        /// <summary>
        /// 郵件 可由發布者填寫(會記錄)
        /// </summary>
        [DisplayName("寄到電子信箱")]
        [Required]
        [EmailAddress]
        public string Email2 { get; set; }

        /// <summary>
        /// 推薦人
        /// </summary>
        [DisplayName("推薦人")]
        public string IntroPerson { get; set; }

        /// <summary>
        /// 文章標題
        /// </summary>
        public string ArticleTitle { get; set; }

        /// <summary>
        /// 文章內容
        /// </summary>
        public string ArticleContent { get; set; }

        public static bool IsValidEmailAddress(string emailAddress)
        {
            return new System.ComponentModel.DataAnnotations
                                .EmailAddressAttribute()
                                .IsValid(emailAddress);
        }

        public static bool IsValidSize(string input, int size)
        {
            return new System.ComponentModel.DataAnnotations
                                .StringLengthAttribute(size)
                                .IsValid(input);
        }
    }
}