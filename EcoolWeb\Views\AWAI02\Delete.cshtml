﻿@model ECOOL_APP.EF.AWAT06

@{
    ViewBag.Title = "Delete";
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    if (string.IsNullOrWhiteSpace(UUID) == false)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
}

<h2>Delete</h2>

<h3>Are you sure you want to delete this?</h3>
<div>
    <h4>AWAT06</h4>
    <hr />
    <dl class="dl-horizontal">
        <dt>
            @Html.DisplayNameFor(model => model.SCHOOL_NO)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.SCHOOL_NO)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.PLAYER_SEX)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.PLAYER_SEX)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.PLAYER_NAME)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.PLAYER_NAME)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.COST_CASH)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.COST_CASH)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.TREASURE_TYPE)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.TREASURE_TYPE)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.SDATETIME)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.SDATETIME)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.EDATETIME)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.EDATETIME)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.DESCRIPTION)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.DESCRIPTION)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.IMG_FILE)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.IMG_FILE)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.IMG2_FILE)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.IMG2_FILE)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.AWARD_STATUS)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.AWARD_STATUS)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.HOT_YN)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.HOT_YN)
        </dd>

    </dl>

    @using (Html.BeginForm()) {
        @Html.AntiForgeryToken()

        <div class="form-actions no-color">
            <input type="submit" value="Delete" class="btn btn-default" /> |
            @Html.ActionLink("Back to List", "Index")
        </div>
    }
</div>
