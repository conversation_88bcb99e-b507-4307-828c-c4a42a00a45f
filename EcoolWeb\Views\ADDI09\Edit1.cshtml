﻿@model ECOOL_APP.com.ecool.Models.DTO.ADDI09EditViewModel
@using ECOOL_APP.com.ecool.Models.DTO;
@using ECOOL_APP;
@using EcoolWeb.Models;
<link href="~/Content/css/EzCss.css" rel="stylesheet" />
@{
    ViewBag.Title = ViewBag.Panel_Title;
    UserProfile user = UserProfileHelper.Get();
}

@Html.Partial("_Title_Secondary")

@Html.Partial("_Notice")
@Html.ValidationMessage("ErrorMsg", new { @class = "text-danger" })

@Html.ActionLink("回「新增批次給點/扣點」首頁", (string)ViewBag.IndexActionName, null, new { @class = "btn btn-sm btn-sys", @role = "button" })

@using (Html.BeginForm((string)ViewBag.Action, (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", enctype = "multipart/form-data" }))
{
    if (!ViewData.ModelState.IsValid)
    {
        <div class="alert alert-danger">
            @Html.ValidationSummary()
        </div>
    }

    @Html.AntiForgeryToken()
    @Html.Hidden("SYS_TABLE_TYPE", (string)ViewBag.SYS_TABLE_TYPE)
    @Html.Hidden("ADDT14_STYLE", (string)ViewBag.ADDT14_STYLE)
    @Html.Hidden("Individual_Give", (bool)ViewBag.Individual_Give)

    @Html.ValidationSummary(true, "", new { @class = "text-danger" })
    <img src="~/Content/img/web-bar2-revise-22.png" style="width:100%" class="img-responsive " alt="Responsive image" />
   
            <div id="QuerySelectDataList">
                @Html.ValidationMessage("DataList", new { @class = "text-danger" })
                @Html.Action("_QuerySelectDataList1", (string)ViewBag.BRE_NO, new { ShowType = ViewContext.RouteData.Values["action"] })
            </div>
            <div style="height:20px"></div>
            <div class="text-center" align="center">
                @*@Html.ActionLink("回選擇「學號」輸入模式", "Index", new { SYS_TABLE_TYPE = ViewBag.ADDT14_STYLE , ADDT14_STYLE = ViewBag.ADDT14_STYLE }, new { @class = "btn btn-default", @role = "button" })*@

                @Html.ActionLink("重選人員", "QuerySelectView1", new { SYS_TABLE_TYPE = ViewBag.SYS_TABLE_TYPE, ADDT14_STYLE = ViewBag.ADDT14_STYLE, @DataType = "reelect" }, new { @class = "btn btn-default", @role = "button" })

                <button type="button" id="myButton" data-loading-text="Loading..." class="btn btn-default" autocomplete="off" align="center">
                    確定給點
                </button>
            </div>
            <div id="areapoint"></div>
            }

            @{

                ADDI09SelectListViewModel temp = new ADDI09SelectListViewModel();

            }
            <script type="text/javascript">
        @*$(document).ready(function () {
     $("#DivSelectData tbody tr").each(function (index) {
            var str0 = "";
            var str = "";
            var str1 = "";
            var str2 = "";
            var str3 = "";
            var str4= "";
            if ($(this).find('td').eq(0).text() != null) {
                str0 = "<input type=\"hidden\" name=\"Data.CHECKDataList[" + index + "].Chk\" value=\"" + "false" + "\" /><input type=\"hidden\" name=\"Data.CHECKDataList[" + index + "].SCHOOL_NO\" value=\"" + @user.SCHOOL_NO + "\" />";
                str = "<input type=\"hidden\" name=\"Data.CHECKDataList[" + index + "].USER_NO\" value=\"" + $(this).find('td').eq(0).text().trim() + "\" />";
                str1 = "<input type=\"hidden\" name=\"Data.CHECKDataList[" + index + "].SNAME\" value=\"" + $(this).find('td').eq(1).text().trim() + "\" />";
                str2 = "<input type=\"hidden\" name=\"Data.CHECKDataList[" + index + "].NAME\" value=\"" + $(this).find('td').eq(1).text().trim() + "\" />";
                str3 = "<input type=\"hidden\" name=\"Data.CHECKDataList[" + index + "].CLASS_NO\" value=\"" + $(this).find('td').eq(2).text().trim() + "\" />";
                str4 = "<input type=\"hidden\" name=\"Data.CHECKDataList[" + index + "].SEAT_NO\" value=\"" + $(this).find('td').eq(3).text().trim() + "\" />";

                $("#areapoint").append(str0 + str + str1 + str2 + str3 + str4);


                }

                });
        });*@
    $('#myButton').on('click', function () {

        var $btn = $(this).button('loading....')

        form1.submit();
    });
            </script>
