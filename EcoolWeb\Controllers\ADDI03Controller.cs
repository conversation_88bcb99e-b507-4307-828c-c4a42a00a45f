﻿using ECOOL_APP.EF;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using EcoolWeb.Models;
using ECOOL_APP.com.ecool.Models.entity;
using com.ecool.service;
using System.Collections;
using System.Data.Entity.Validation;
using System.Data.Entity;
using EcoolWeb.CustomAttribute;
using ECOOL_APP.com.ecool.service;
using Dapper;
using System.Data;
using System.Data.SqlClient;
using com.ecool.sqlConnection;
using System.Text;
using ECOOL_APP;
using ECOOL_APP.com.ecool.LogicCenter;

namespace EcoolWeb.Controllers
{
    [SessionExpire]
    public class ADDI03Controller : Controller
    {
        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();
        private ECOOL_APP.UserProfile user = UserProfileHelper.Get();
        private string SchoolNo = UserProfileHelper.GetSchoolNo();

        public ActionResult MaintainOneApple(string USER_NO, short GRADE, string TWO_BOOK_ID)
        {
            //權限
            string UseYN = (user != null) ? PermissionService.GetPermission_Use_YN("ADDI03", "MaintainOneApple", user.SCHOOL_NO, user.USER_NO) : "N";
            if (UseYN == "N")
            {
                return RedirectToAction("PermissionError", "Error");
            }

            ADDV02 AppleData = db.ADDV02.Where(v2 => v2.SCHOOL_NO == SchoolNo && v2.USER_NO == USER_NO && v2.GRADE == GRADE).FirstOrDefault();

            //如果該生此年級已閱讀完畢，不能再修改
            ADDT04 A4 = db.ADDT04.Where(a4 => a4.SCHOOL_NO == SchoolNo && a4.USER_NO == USER_NO && a4.GRADE == GRADE).FirstOrDefault();
            if (A4 != null)
            {
                if (A4.PASS_YN == "Y")
                {
                    //不能再修改
                    return RedirectToAction("Query3", new { GRADE = GRADE });
                }
            }

            ADDI03BookMaintainViewModel model = new ADDI03BookMaintainViewModel();
            //儲存查詢條件
            model.whereKeyword = Request["whereKeyword"];
            model.Class_No = Request["Class_No"];

            model.USER_NO = USER_NO;
            //學生姓名
            model.USER_NAME = db.HRMT01.Where(a => a.SCHOOL_NO == SchoolNo && a.USER_NO == USER_NO && a.USER_STATUS != UserStaus.Invalid).Select(a => a.NAME).FirstOrDefault();

            //護照年級
            model.GRADE = GRADE;

            //原書號
            model.BOOK_ID = GRADE.ToString() + TWO_BOOK_ID;

            //學生已經閱讀的清單
            List<ADDT05> ReadList = db.ADDT05.Where(a => a.SCHOOL_NO == SchoolNo && a.USER_NO == USER_NO && a.GRADE == GRADE).ToList();
            //該年級規定的清單
            List<ADDT03> PassPortList = db.ADDT03.Where(a => a.SCHOOL_NO == SchoolNo && a.GRADE == GRADE).ToList();

            //找出可以調整的書號
            List<string> RList = ReadList.Select(r => r.BOOK_ID).ToList();
            var theList = PassPortList.Where(a => RList.Contains(a.BOOK_ID) == false)
             .Select(x => new SelectListItem { Text = x.BOOK_ID + "-" + x.BOOK_NAME, Value = x.BOOK_ID }).ToList();
            theList.Add(new SelectListItem() { Text = "刪除書號", Value = "del" });
            model.NewBookIDList = theList;

            //原書名
            ADDT05 a5 = ReadList.Where(a => a.BOOK_ID == GRADE.ToString() + TWO_BOOK_ID).FirstOrDefault();
            if (a5 != null)
            {
                model.BOOK_NAME = a5.BOOK_NAME;
            }

            return View(model);
        }

        public ActionResult MaintainOneAppleOK(ADDI03BookMaintainViewModel model)
        {
            //取得原護照紀錄
            ADDT05 a5 = db.ADDT05.Where(a => a.SCHOOL_NO == SchoolNo && a.USER_NO == model.USER_NO && a.GRADE == model.GRADE && a.BOOK_ID == model.BOOK_ID).FirstOrDefault();

            //刪除
            if (model.NewBookID == "del")
            {
                db.ADDT05.Remove(a5);
            }
            //改變書號
            else
            {
                ADDT05 NewA5 = db.ADDT05.Create();
                NewA5.BOOK_ID = model.NewBookID;
                NewA5.APPLY_NO = a5.APPLY_NO;
                NewA5.BOOK_NAME = a5.BOOK_NAME;
                NewA5.GRADE = a5.GRADE;
                NewA5.SCHOOL_NO = a5.SCHOOL_NO;
                NewA5.USER_NO = a5.USER_NO;
                NewA5.VERIFIED_DATE = a5.VERIFIED_DATE;

                db.ADDT05.Add(NewA5);
                db.ADDT05.Remove(a5);
            }
            db.SaveChanges();

            return RedirectToAction("Query3", new { GRADE = model.GRADE, whereKeyword = model.whereKeyword, Class_No = model.Class_No });
        }

        //閱讀護照完成一覽表
        public ActionResult Query2(FormCollection viewADDI04, string ddlGrade = null, string ddlCLASS_NO = null, string whereKeyword = null)
        {
            //取出該校所有的小朋友閱讀護照通過認證暫存表
            List<uADDT04> litmpADDI04 = new List<uADDT04>();

            //待回傳閱讀護照通過認證
            List<uADDT04Q02> liADDT04Q02 = new List<uADDT04Q02>();

            //護照年級
            string BooKGrade = viewADDI04["ddlBooKGrade"];
            ViewBag.BooKGrade = BooKGrade;

            //人員年級
            ddlGrade = (string.IsNullOrWhiteSpace(viewADDI04["ddlGrade"]) == false) ? viewADDI04["ddlGrade"] : ddlGrade;
            ViewBag.GradeItem = HRMT01.GetGradeItems(ddlGrade);

            //班級
            ViewBag.ClassItems = HRMT01.GetClassListData(SchoolNo, viewADDI04["ddlGrade"], viewADDI04["ddlCLASS_NO"], ref db)
             .Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == viewADDI04["ddlCLASS_NO"] });

            //學號/姓名
            whereKeyword = (string.IsNullOrWhiteSpace(viewADDI04["whereKeyword"]) == false) ? viewADDI04["whereKeyword"] : whereKeyword;
            ViewBag.whereKeyword = whereKeyword;

            var Temp = (from h01 in db.HRMT01
                        join a04 in db.ADDT04
                         on new { h01.SCHOOL_NO, h01.USER_NO }
                            equals new { a04.SCHOOL_NO, a04.USER_NO } into a04_join
                        from a04 in a04_join.DefaultIfEmpty()
                        where h01.SCHOOL_NO == SchoolNo
                                    && (!UserStaus.NGUserStausList.Contains(h01.USER_STATUS))
                                    && h01.USER_TYPE == UserType.Student
                        orderby
                          a04.SCHOOL_NO,
                          h01.CLASS_NO,
                          h01.SEAT_NO
                        select new uADDT04
                        {
                            HrGRADE = h01.GRADE,
                            CLASS_NO = h01.CLASS_NO,
                            NAME = h01.NAME,
                            SONAME = (h01.SEAT_NO + h01.SNAME),
                            GRADE = a04.GRADE,
                            PASS_DATE = a04.PASS_DATE,
                            USER_NO = h01.USER_NO,
                            SCHOOL_NO = h01.SCHOOL_NO
                        });

            //人員年級
            if (string.IsNullOrWhiteSpace(ddlGrade) == false)
            {
                short iGRADE = (string.IsNullOrWhiteSpace(ddlGrade) == false) ? Convert.ToInt16(ddlGrade) : Convert.ToInt16(0);
                Temp = Temp.Where(a => a.HrGRADE == iGRADE);
            }

            //人員班級
            if (string.IsNullOrWhiteSpace(ddlCLASS_NO) == false)
            {
                Temp = Temp.Where(a => a.CLASS_NO == ddlCLASS_NO);
            }

            //學號/姓名
            if (string.IsNullOrWhiteSpace(whereKeyword) == false)
            {
                Temp = Temp.Where(a => a.USER_NO.Contains(whereKeyword.Trim()) || a.NAME.Contains(whereKeyword.Trim()));
            }

            //護照年級
            if (string.IsNullOrWhiteSpace(BooKGrade) == false)
            {
                if (BooKGrade == "ALL")
                {
                    Temp = (from a in Temp
                            join b in (from o in db.ADDT04 group o by new { o.SCHOOL_NO, o.USER_NO } into o where o.Count() >= 6 select o.Key)
                            on new { a.SCHOOL_NO, a.USER_NO } equals new { b.SCHOOL_NO, b.USER_NO }
                            select a);
                }
                else
                {
                    short IntBooKGrade = Convert.ToInt16(BooKGrade);

                    Temp = Temp.Where(a => a.GRADE == IntBooKGrade);
                }
            }

            litmpADDI04 = Temp.ToList();

            for (int i = 0; i < litmpADDI04.Count(); i++)
            {
                uADDT04 u4 = litmpADDI04[i];
                uADDT04Q02 u4Q = liADDT04Q02.Where(q => q.USER_NO == u4.USER_NO).FirstOrDefault();

                if (u4Q == null)
                {
                    //取得該學生該年度的學習護照閱讀總筆數
                    int iADDT04Count = litmpADDI04.Where(p => p.USER_NO == u4.USER_NO).Count();

                    liADDT04Q02.Add(new uADDT04Q02
                    {
                        CLASS_NO = u4.CLASS_NO,
                        SONAME = u4.SONAME,
                        USER_NO = u4.USER_NO,
                        UserDate_NO01 = (u4.GRADE == 1) ? "一年級" : "",
                        UserDate_NO02 = (u4.GRADE == 2) ? "二年級" : "",
                        UserDate_NO03 = (u4.GRADE == 3) ? "三年級" : "",
                        UserDate_NO04 = (u4.GRADE == 4) ? "四年級" : "",
                        UserDate_NO05 = (u4.GRADE == 5) ? "五年級" : "",
                        UserDate_NO06 = (u4.GRADE == 6) ? "六年級" : "",
                        UserStatus_NO01 = (u4.GRADE == 1) ? Convert.ToDateTime(u4.PASS_DATE).ToString("yyyy/MM/dd") : "",
                        UserStatus_NO02 = (u4.GRADE == 2) ? Convert.ToDateTime(u4.PASS_DATE).ToString("yyyy/MM/dd") : "",
                        UserStatus_NO03 = (u4.GRADE == 3) ? Convert.ToDateTime(u4.PASS_DATE).ToString("yyyy/MM/dd") : "",
                        UserStatus_NO04 = (u4.GRADE == 4) ? Convert.ToDateTime(u4.PASS_DATE).ToString("yyyy/MM/dd") : "",
                        UserStatus_NO05 = (u4.GRADE == 5) ? Convert.ToDateTime(u4.PASS_DATE).ToString("yyyy/MM/dd") : "",
                        UserStatus_NO06 = (u4.GRADE == 6) ? Convert.ToDateTime(u4.PASS_DATE).ToString("yyyy/MM/dd") : "",
                        FinishStatus = (iADDT04Count == 6) ? "Y" : ""
                    });
                }
                else
                {
                    u4Q.UserDate_NO01 = (u4.GRADE == 1) ? "一年級" : u4Q.UserDate_NO01;
                    u4Q.UserDate_NO02 = (u4.GRADE == 2) ? "二年級" : u4Q.UserDate_NO02;
                    u4Q.UserDate_NO03 = (u4.GRADE == 3) ? "三年級" : u4Q.UserDate_NO03;
                    u4Q.UserDate_NO04 = (u4.GRADE == 4) ? "四年級" : u4Q.UserDate_NO04;
                    u4Q.UserDate_NO05 = (u4.GRADE == 5) ? "五年級" : u4Q.UserDate_NO05;
                    u4Q.UserDate_NO06 = (u4.GRADE == 6) ? "六年級" : u4Q.UserDate_NO06;

                    u4Q.UserStatus_NO01 = (u4.GRADE == 1) ? Convert.ToDateTime(u4.PASS_DATE).ToString("yyyy/MM/dd") : u4Q.UserStatus_NO01;
                    u4Q.UserStatus_NO02 = (u4.GRADE == 2) ? Convert.ToDateTime(u4.PASS_DATE).ToString("yyyy/MM/dd") : u4Q.UserStatus_NO02;
                    u4Q.UserStatus_NO03 = (u4.GRADE == 3) ? Convert.ToDateTime(u4.PASS_DATE).ToString("yyyy/MM/dd") : u4Q.UserStatus_NO03;
                    u4Q.UserStatus_NO04 = (u4.GRADE == 4) ? Convert.ToDateTime(u4.PASS_DATE).ToString("yyyy/MM/dd") : u4Q.UserStatus_NO04;
                    u4Q.UserStatus_NO05 = (u4.GRADE == 5) ? Convert.ToDateTime(u4.PASS_DATE).ToString("yyyy/MM/dd") : u4Q.UserStatus_NO05;
                    u4Q.UserStatus_NO06 = (u4.GRADE == 6) ? Convert.ToDateTime(u4.PASS_DATE).ToString("yyyy/MM/dd") : u4Q.UserStatus_NO06;
                }
            }

            return View(liADDT04Q02.OrderBy(a => a.GRADE).ThenBy(a => a.CLASS_NO).ThenBy(a => a.SONAME));
        }

        //書單閱讀情況一覽表
        public ActionResult _Query3(int GRADE)
        {
            //取出該校特定年級所有的閱讀資料
            List<uADDT05Q03TMP> liAD05Q03TMP =
            (from ad05 in db.ADDT05
             join h01 in db.HRMT01 on new { ad05.USER_NO, ad05.SCHOOL_NO } equals new { h01.USER_NO, h01.SCHOOL_NO } into h01_join
             from h01 in h01_join.DefaultIfEmpty()
             where ad05.BOOK_ID.Substring(0, 1) == GRADE.ToString()
             orderby h01.CLASS_NO, h01.SEAT_NO
             select new uADDT05Q03TMP
             {
                 USER_NO = ad05.USER_NO,
                 CLASS_NO = h01.CLASS_NO,
                 SEAT_NO = h01.SEAT_NO,
                 NAME = h01.SNAME,
                 BOOK_ID = ad05.BOOK_ID
             }).ToList();

            //待回傳的閱讀護照情況一覽表
            List<uADDT05Q03> liADDT05Q03 = new List<uADDT05Q03>();

            //取出各年級閱讀護照本數
            List<ADDT03BookCount> liADDT03 = new List<ADDT03BookCount>();

            liADDT03 = (from addt03 in db.ADDT03
                        where addt03.SCHOOL_NO == SchoolNo  && addt03.BOOK_NAME!=""
                        group addt03 by new
                        {
                            addt03.GRADE
                        } into g
                        select new ADDT03BookCount
                        {
                            SumGrade = (int)g.Count(p => p.GRADE != null),
                            Grade = (int)g.Key.GRADE
                        }).ToList();

            int iStuSeat = -1;    //資料位置
            string Name = string.Empty;
            for (int i = 0; i < liAD05Q03TMP.Count(); i++)
            {
                if (Name != liAD05Q03TMP.ToList()[i].NAME)
                {
                    //取得該年級總筆數
                    int iYearCount = liADDT03.Where(p => p.Grade == Convert.ToInt16(GRADE)).FirstOrDefault().SumGrade;

                    //取得該學生該年度的學習護照閱讀總筆數
                    int iADDT06Count = liAD05Q03TMP.Where(p => p.USER_NO == liAD05Q03TMP.ToList()[i].USER_NO).Count();

                    //取得讀取百分比
                    int iReadPercent = Convert.ToInt32(Math.Round((decimal)iADDT06Count / iYearCount, 2) * 100);
                    iReadPercent = (iReadPercent > 100) ? 100 : iReadPercent;
                    //取得完成日期
                    string sPASS_DATE = string.Empty;

                    //取得酷幣
                    string sCoolCash = string.Empty;

                    if (iReadPercent == 100)
                    {
                        string sTMPUSER_NO = liAD05Q03TMP.ToList()[i].USER_NO;
                        ADDT04 a4 = db.ADDT04.Where(p => p.USER_NO == sTMPUSER_NO && p.GRADE == GRADE).FirstOrDefault();
                        if (a4 != null)
                        {
                            sPASS_DATE = Convert.ToDateTime(a4.PASS_DATE).ToString("yyyy/MM/dd");
                        }
                        sCoolCash = db.ADDT10.Where(p => p.GRADE == GRADE).FirstOrDefault().TO_CASH.ToString();
                    }

                    //塞資料進回傳集合
                    liADDT05Q03.Add(new uADDT05Q03
                    {
                        CLASS_NO = liAD05Q03TMP.ToList()[i].CLASS_NO,
                        USER_NO = liAD05Q03TMP.ToList()[i].SEAT_NO + liAD05Q03TMP.ToList()[i].NAME,
                        Book_NO01 = (liAD05Q03TMP.ToList()[i].BOOK_ID.Substring(2, 1) == "1") ? "Y" : "N",
                        Book_NO02 = (liAD05Q03TMP.ToList()[i].BOOK_ID.Substring(2, 1) == "2") ? "Y" : "N",
                        Book_NO03 = (liAD05Q03TMP.ToList()[i].BOOK_ID.Substring(2, 1) == "3") ? "Y" : "N",
                        Book_NO04 = (liAD05Q03TMP.ToList()[i].BOOK_ID.Substring(2, 1) == "4") ? "Y" : "N",
                        Book_NO05 = (liAD05Q03TMP.ToList()[i].BOOK_ID.Substring(2, 1) == "5") ? "Y" : "N",
                        Book_NO06 = (liAD05Q03TMP.ToList()[i].BOOK_ID.Substring(2, 1) == "6") ? "Y" : "N",
                        Book_NO07 = (liAD05Q03TMP.ToList()[i].BOOK_ID.Substring(2, 1) == "7") ? "Y" : "N",
                        Book_NO08 = (liAD05Q03TMP.ToList()[i].BOOK_ID.Substring(2, 1) == "8") ? "Y" : "N",
                        Book_NO09 = (liAD05Q03TMP.ToList()[i].BOOK_ID.Substring(2, 1) == "9") ? "Y" : "N",
                        Book_NO10 = (liAD05Q03TMP.ToList()[i].BOOK_ID.Substring(1, 2) == "10") ? "Y" : "N",
                        Book_NO11 = (liAD05Q03TMP.ToList()[i].BOOK_ID.Substring(1, 2) == "11") ? "Y" : "N",
                        CoolCash = sCoolCash,
                        FinishStatus = (iReadPercent == 100) ? sPASS_DATE : iReadPercent.ToString() + "%"
                    });
                    iStuSeat++;
                    Name = liAD05Q03TMP.ToList()[i].NAME;
                }
                else
                {
                    liADDT05Q03.ToList()[iStuSeat].Book_NO01 = (liAD05Q03TMP.ToList()[i].BOOK_ID.Substring(2, 1) == "1") ? "Y" : liADDT05Q03.ToList()[iStuSeat].Book_NO01;
                    liADDT05Q03.ToList()[iStuSeat].Book_NO02 = (liAD05Q03TMP.ToList()[i].BOOK_ID.Substring(2, 1) == "2") ? "Y" : liADDT05Q03.ToList()[iStuSeat].Book_NO02;
                    liADDT05Q03.ToList()[iStuSeat].Book_NO03 = (liAD05Q03TMP.ToList()[i].BOOK_ID.Substring(2, 1) == "3") ? "Y" : liADDT05Q03.ToList()[iStuSeat].Book_NO03;
                    liADDT05Q03.ToList()[iStuSeat].Book_NO04 = (liAD05Q03TMP.ToList()[i].BOOK_ID.Substring(2, 1) == "4") ? "Y" : liADDT05Q03.ToList()[iStuSeat].Book_NO04;
                    liADDT05Q03.ToList()[iStuSeat].Book_NO05 = (liAD05Q03TMP.ToList()[i].BOOK_ID.Substring(2, 1) == "5") ? "Y" : liADDT05Q03.ToList()[iStuSeat].Book_NO05;
                    liADDT05Q03.ToList()[iStuSeat].Book_NO06 = (liAD05Q03TMP.ToList()[i].BOOK_ID.Substring(2, 1) == "6") ? "Y" : liADDT05Q03.ToList()[iStuSeat].Book_NO06;
                    liADDT05Q03.ToList()[iStuSeat].Book_NO07 = (liAD05Q03TMP.ToList()[i].BOOK_ID.Substring(2, 1) == "7") ? "Y" : liADDT05Q03.ToList()[iStuSeat].Book_NO07;
                    liADDT05Q03.ToList()[iStuSeat].Book_NO08 = (liAD05Q03TMP.ToList()[i].BOOK_ID.Substring(2, 1) == "8") ? "Y" : liADDT05Q03.ToList()[iStuSeat].Book_NO08;
                    liADDT05Q03.ToList()[iStuSeat].Book_NO09 = (liAD05Q03TMP.ToList()[i].BOOK_ID.Substring(2, 1) == "9") ? "Y" : liADDT05Q03.ToList()[iStuSeat].Book_NO09;
                    liADDT05Q03.ToList()[iStuSeat].Book_NO10 = (liAD05Q03TMP.ToList()[i].BOOK_ID.Substring(1, 2) == "10") ? "Y" : liADDT05Q03.ToList()[iStuSeat].Book_NO10;
                    liADDT05Q03.ToList()[iStuSeat].Book_NO11 = (liAD05Q03TMP.ToList()[i].BOOK_ID.Substring(1, 2) == "11") ? "Y" : liADDT05Q03.ToList()[iStuSeat].Book_NO11;
                }
            }

            //取出書本數
            ADDT18 ltA18 = db.ADDT18.Where(p => p.GRADE == GRADE).FirstOrDefault();
            List<ADDT03> ADDT03Items = new List<ADDT03>();
            ADDT03Items = db.ADDT03.Where(p => p.GRADE == GRADE && p.SCHOOL_NO==SchoolNo).ToList();
            ViewBag.BookCount = ltA18.BOOK_SCCOUNT + ltA18.BOOK_SECOUNT;
            ViewBag.BookDetail = ADDT03Items;
            return View(liADDT05Q03);
        }

        public ActionResult Query4(string USER_NO)
        {
            if (user == null)
            {
                return RedirectToAction("SessionTimeOutError", "Error");
            }

            if (string.IsNullOrWhiteSpace(USER_NO))
            {
                if (user.USER_TYPE == UserType.Parents)
                {
                    var H06 = db.HRMT06.Where(a => a.SCHOOL_NO == SchoolNo && a.PARENTS_USER_NO == user.USER_NO).FirstOrDefault();
                    if (H06 != null)
                    {
                        USER_NO = H06.STUDENT_USER_NO;
                    }
                    else
                    {
                        USER_NO = string.Empty;
                    }
                }
                else
                {
                    USER_NO = user.USER_NO;
                }
            }

            List<uADDT03> liADDI03 = new List<uADDT03>();
            liADDI03 = ((from addt03 in db.ADDT03
                         where
                            addt03.SCHOOL_NO == SchoolNo &&
                             !(from addt05 in db.ADDT05
                               where
                                     addt05.SCHOOL_NO == SchoolNo &&
                                     addt05.USER_NO == USER_NO
                               select new
                               {
                                   addt05.BOOK_ID
                               }).Contains(new { addt03.BOOK_ID })
                         select new uADDT03
                         {
                             BOOK_ID = addt03.BOOK_ID,
                             GRADE = addt03.GRADE.ToString(),
                             BOOK_NAME = addt03.BOOK_NAME,
                             Read_STATUS = "N"
                         }
                      )
                      .Union
                      (from addt03 in db.ADDT03
                       where
                        addt03.SCHOOL_NO == SchoolNo &&
                          (from addt05 in db.ADDT05
                           where
                                  addt05.SCHOOL_NO == SchoolNo &&
                                  addt05.USER_NO == USER_NO
                           select new
                           {
                               addt05.BOOK_ID
                           }).Contains(new { addt03.BOOK_ID })
                       select new uADDT03
                       {
                           BOOK_ID = addt03.BOOK_ID,
                           GRADE = addt03.GRADE.ToString(),
                           BOOK_NAME = addt03.BOOK_NAME,
                           Read_STATUS = "Y"
                       }
                      )).OrderBy(a => a.GRADE).ThenBy(a => a.BOOK_ID).ToList();

            ViewBag.MyPanyStudentItems = HRMT06.GetMyPanyStudentItems(USER_NO, user, db);

            return View(liADDI03);
        }

        public ActionResult BOOK_MAINTAIN()
        {
            ADDT03 A03 = new ADDT03();
            string Book_ID = string.Empty;
            string SCHOOL_NO = UserProfileHelper.Get().SCHOOL_NO.ToString();

            if (Request["Book_Status"] != null)
            {
                ViewData["BookStatus"] = Request["Book_Status"];
                if (Request["Book_Status"] == "EDIT" || Request["Book_Status"] == "DEL")
                {
                    if (Request["Book_ID"] != string.Empty)
                    {
                        Book_ID = Request["Book_ID"];
                        A03 = db.ADDT03.Where(p => p.BOOK_ID == Book_ID && p.SCHOOL_NO == SCHOOL_NO).FirstOrDefault();
                    }
                }
            }

            List<SelectListItem> items = new List<SelectListItem>();
            for (int i = 0; i <= 6; i++)
            {
                string GRADE_Text = string.Empty, GRADE_Value = string.Empty;
                switch (i)
                {
                    case 0:
                        GRADE_Text = "請選擇";
                        GRADE_Value = "";
                        break;

                    case 1:
                        GRADE_Text = "一年級";
                        GRADE_Value = i.ToString();
                        break;

                    case 2:
                        GRADE_Text = "二年級";
                        GRADE_Value = i.ToString();
                        break;

                    case 3:
                        GRADE_Text = "三年級";
                        GRADE_Value = i.ToString();
                        break;

                    case 4:
                        GRADE_Text = "四年級";
                        GRADE_Value = i.ToString();
                        break;

                    case 5:
                        GRADE_Text = "五年級";
                        GRADE_Value = i.ToString();
                        break;

                    case 6:
                        GRADE_Text = "六年級";
                        GRADE_Value = i.ToString();
                        break;
                }
                items.Add(new SelectListItem()
                {
                    Text = GRADE_Text,
                    Value = GRADE_Value,
                    Selected = (Book_ID == GRADE_Value) ? true : false
                 });
            }
            ViewBag.GRADEItem = items;

            return View(A03);
        }
        public ActionResult MaxBook(string Grade) {
            string SCHOOL_NO = UserProfileHelper.Get().SCHOOL_NO.ToString();
            byte Gradetobyte = 0;
            Gradetobyte = byte.Parse(Grade);
            string BookStr = "";
            int BookStrInt = 0;
          BookStr = db.ADDT03.Where(x => x.GRADE == Gradetobyte && x.SCHOOL_NO == SchoolNo&& x.BOOK_NAME!="" && x.BOOK_NAME != null).Select(x => x.BOOK_ID).OrderByDescending(x => x).FirstOrDefault();
            if (BookStr == null)
            {
                BookStr = db.ADDT03.Where(x => x.GRADE == Gradetobyte && x.SCHOOL_NO == SchoolNo && x.BOOK_NAME == "").Select(x => x.BOOK_ID).OrderBy(x => x).FirstOrDefault();
                if (BookStr == null)
                {
                    BookStr = Gradetobyte.ToString() + "01";
                    BookStrInt = int.Parse(BookStr);
                }
                else {
                    BookStrInt = int.Parse(BookStr);

                }
           }
            else {
                BookStrInt = int.Parse(BookStr);
                BookStrInt = BookStrInt + 1;
            }
     

          
            BookStr = BookStrInt.ToString();
            return Json(BookStr);
        }
        //書單維護
        public ActionResult BOOK_MAINTAINQuery()
        {
            List<Hashtable> Hashtablelist = new List<Hashtable>();
            try
            {
                bool IserOK = true;
                Hashtablelist = new rppService().USP_RPP_BOOK_QUERY(SchoolNo);
                if (Hashtablelist != null && Hashtablelist.Count == 0)
                {
                    IserOK = new rppService().BookPreInser(SchoolNo);
                }
                else if (Hashtablelist == null)
                {
                    IserOK = new rppService().BookPreInser(SchoolNo);
                }
                ViewData["BOOK_HTB"] = new rppService().USP_RPP_BOOK_QUERY(SchoolNo);
            }
            catch (Exception e)
            {
                //this.HandleMessage(e.ToString());
            }
            return View();
        }

        [HttpPost]
        public ActionResult GetBookStatus(string BOOK_ID)
        {
            bool BookStatus = false;
            string SCHOOL_NO = UserProfileHelper.Get().SCHOOL_NO.ToString();
            if (db.ADDT03.Where(p => p.BOOK_ID == BOOK_ID && p.SCHOOL_NO == SCHOOL_NO && p.BOOK_NAME!="").Count() != 0  )
            {
               
                BookStatus = true;
            }
            return Json(BookStatus);
        }
        [HttpPost]
        public ActionResult GetBookisMODIFY(string BOOK_ID)
        {
            bool BookStatus = false;
            string SCHOOL_NO = UserProfileHelper.Get().SCHOOL_NO.ToString();
            if (db.ADDT03.Where(p => p.BOOK_ID == BOOK_ID && p.SCHOOL_NO == SCHOOL_NO && p.BOOK_NAME == "").Count() > 0) {
                BookStatus = true;

            }
            return Json(BookStatus);
        }
        //確認是否書本本數超過該年級上限數
        [HttpPost]
        public ActionResult CheckBookCount(byte bGRADE)
        {
            string SchoolNO = UserProfileHelper.GetSchoolNo();
            bool BookStatus = false;
            try
            {
                ADDT18 ltA18 = db.ADDT18.Where(p => p.GRADE == bGRADE).FirstOrDefault();
                //List<ADDT18> test = (from addt03 in db.ADDT03 select new ADDT18 { GRADE =  }).ToList();
                //List<ADDT18> ltReadyBook = (from addt03 in db.ADDT03
                //                            group addt03 by new
                //                            {
                //                                addt03.GRADE
                //                            } into g
                //                            select new
                //                            {
                //                                BOOK_SCCOUNT = g.Count(p => p.GRADE),
                //                                GRADE = g.Key.GRADE
                //                            }).ToList();

                int A03Count = db.ADDT03.Where(p => p.GRADE == bGRADE && p.SCHOOL_NO == SchoolNO).Count();
                if (A03Count >= (ltA18.BOOK_SCCOUNT + ltA18.BOOK_SECOUNT))
                {
                    BookStatus = true;
                }
            }
            catch (DbEntityValidationException ex)
            {
                var entityError = ex.EntityValidationErrors.SelectMany(x => x.ValidationErrors).Select(x => x.ErrorMessage);
                var getFullMessage = string.Join("; ", entityError);
                var exceptionMessage = string.Concat(ex.Message, "errors are: ", getFullMessage);
            }
            return Json(BookStatus);
        }

        //書單新增/修改
        [HttpPost]
        public ActionResult BooKMAInsert()
        {
            ADDT03 a03 = new ADDT03();
            if (Request["Book_Status"] != null)
            {
                byte? bGrade = 0;
                string BOOK_ID = string.Empty, hidBOOK_ID = string.Empty;

                hidBOOK_ID = Request["hidBOOK_ID"].ToString();
                //BOOK_ID = Request["BOOK_ID"].ToString();
                if (Request["Book_Status"] == "ADD")
                {
                    a03.SCHOOL_NO = user.SCHOOL_NO;
                    bGrade = Convert.ToByte(Request["GRADE"]);
                    a03.GRADE = bGrade;
                    a03.BOOK_ID = Request["BOOK_ID"].ToString();
                    a03.BOOK_NAME = Request["BOOK_NAME"].ToString();
                    if (db.ADDT03.Where(p => p.BOOK_ID == BOOK_ID && p.SCHOOL_NO == user.SCHOOL_NO).Count() == 0)
                    {
                        db.ADDT03.Add(a03);
                        TempData["StatusMessage"] = "新增成功";
                    }
                }
                else if (Request["Book_Status"] == "EDIT")
                {
                    ADDT03 updateADDT03 = db.ADDT03.Where(p => p.SCHOOL_NO == user.SCHOOL_NO && p.BOOK_ID == hidBOOK_ID).FirstOrDefault();
                    updateADDT03.SCHOOL_NO = user.SCHOOL_NO;
                    bGrade = Convert.ToByte(Request["GRADE"]);
                    updateADDT03.GRADE = bGrade;
                    //updateADDT03.BOOK_ID = BOOK_ID;
                    updateADDT03.BOOK_NAME = Request["BOOK_NAME"].ToString();
                    db.Entry(updateADDT03).State = System.Data.Entity.EntityState.Modified;
                    TempData["StatusMessage"] = "異動成功";
                }

                try
                {
                    db.SaveChanges();
                }
                catch (DbEntityValidationException ex)
                {
                    var entityError = ex.EntityValidationErrors.SelectMany(x => x.ValidationErrors).Select(x => x.ErrorMessage);
                    var getFullMessage = string.Join("; ", entityError);
                    var exceptionMessage = string.Concat(ex.Message, "errors are: ", getFullMessage);
                    TempData["StatusMessage"] = "異動失敗, 系統錯誤原因:" + exceptionMessage;
                }
                if (Request["Book_Status"] == "DEL")
                {
                    ECOOL_DEVEntities dbef = new ECOOL_DEVEntities();
                    SqlConnection Conn = null;
                    SqlTransaction Transaction = null;
                    bool boolConn = true;
                    if (Conn == null)
                    {
                        boolConn = false;
                        Conn = new sqlConnection().getConnection4Query();
                    }
                    if (Transaction == null) Transaction = Conn.BeginTransaction();
                    string sSQL = $@"select * FROM  ADDT03 Where SCHOOL_NO=@SCHOOL_NO and BOOK_ID=@BOOK_ID";
                    // var delADDT03 = db.ADDT03.Where(p => p.SCHOOL_NO == user.SCHOOL_NO && p.BOOK_ID == hidBOOK_ID).ToList();
                    var temp = dbef.Database.Connection.Query<ADDT03>(sSQL, new
                    {
                        SCHOOL_NO = user.SCHOOL_NO,
                        BOOK_ID = hidBOOK_ID,
                    });
                    if (temp.Count() > 0)
                    {
                        ProductService productService = new ProductService();
                        productService.USP_ADDI03_DELETE(user.SCHOOL_NO, hidBOOK_ID);
                        //IDbCommand cmd = new SqlCommand(@" DELETE ADDT03 Where SCHOOL_NO=@SCHOOL_NO and BOOK_ID=@BOOK_ID", Conn, Transaction);
                        //cmd.Parameters.Add(new SqlParameter("@SCHOOL_NO", user.SCHOOL_NO));
                        //cmd.Parameters.Add(new SqlParameter("@BOOK_ID", hidBOOK_ID));
                        //cmd.ExecuteNonQuery();
                        //if (boolConn == false)
                        //{
                        //Transaction.Commit();

                        //if (Conn != null)
                        //{
                        //    Conn.Close();
                        //    Conn.Dispose();
                        //}
                        //}
                    }
                    // db.ADDT03.RemoveRange(delADDT03);
                    TempData["StatusMessage"] = "刪除成功";
                }
            }

            return RedirectToAction("../ADDI03/BOOK_MAINTAINQuery", "ADDI03");
        }        //書單閱讀情況一覽表

        public ActionResult Query3(int GRADE = 1, string Class_No = null, string whereKeyword = null, string QShowYN = null,string Page=null,string whereGrade=null)
        {
            ViewBag.QShowYN = QShowYN;
            List<SelectListItem> GradeList = HRMT01.GetGradeItems("");
            if (!string.IsNullOrEmpty(whereGrade))
            {
                GradeList = HRMT01.GetGradeItems(whereGrade);

            }
            else { ViewBag.ClassNoItem = HRMT01.GetClassListData(SchoolNo, "", Class_No, ref db); }

            if (Class_No == "All") {

                Class_No = "";
            }
            //if (string.IsNullOrEmpty(whereGrade))
            //{


            //}
            ViewBag.ClassItems = HRMT01.GetClassListData(SchoolNo, GRADE.ToString(), Class_No, ref db);
            List<ADDT03> ADDT03ItemsISnull = new List<ADDT03>();
            List<ADDT03> ADDT03Items = new List<ADDT03>();
            ADDT03Items = db.ADDT03.Where(p => p.GRADE == GRADE && p.SCHOOL_NO == SchoolNo && p.BOOK_NAME != "" &&p.BOOK_NAME != null).ToList();
            ADDT03ItemsISnull = db.ADDT03.Where(p => p.GRADE == GRADE && p.SCHOOL_NO == SchoolNo &&( p.BOOK_NAME == "" || p.BOOK_NAME == null)).ToList();
            ViewBag.BookDetail = ADDT03Items;
            var PassList = db.ADDV02.Where(v2 => v2.SCHOOL_NO == SchoolNo && v2.GRADE == GRADE).ToList();
        
            PassList = PassList.OrderBy(v2 => v2.CLASS_NO).ThenBy(v2 => v2.SEAT_NO).ToList();
            string TeachCLASS_NO = UserProfile.GetTeachCLASS_NO(user, ref db);
            if (user.USER_TYPE == "T" && TeachCLASS_NO != null)
            {
                Class_No = TeachCLASS_NO;



            }
            int PageInt = 0;
            PageInt = Int32.Parse(Page??"0");
            List<ADDT03> ADDT03SItems = new List<ADDT03>();
            List<ADDV02> aDDV02sDetail= new List<ADDV02>();

            List<ADDV02> aDDV02sItme = new List<ADDV02>();
            ADDT03SItems = ADDT03Items.Skip(PageInt).Take(10).ToList();
            var conn = new com.ecool.sqlConnection.sqlConnection().getConnection4System();
            int i = 1;
            string combinedSubstrings = string.Join(", [", ADDT03SItems.Select(x => x.BOOK_ID.Substring(1, 2)),"]");
           // int BOOK_IDInt = 0;
           // BOOK_IDInt = ADDT03SItems.Select(x => x.BOOK_ID.Substring(1, 2)).Count() + 7;
            int baseInttemp = 0;
            List<string> str = new List<string>();
            string USER_NO = "";
            string BKID = "";
            var strST = "";
            string BKID2 = "";
            StringBuilder sb = new StringBuilder();
            StringBuilder sb1 = new StringBuilder();
            BKID = @"SELECT  a5.SCHOOL_NO, a5.GRADE, h1.CLASS_NO, a5.USER_NO, h1.NAME, h1.SNAME, h1.SEAT_NO
                              ,{2} as SkipPage
                   FROM[ADDT05] a5 INNER JOIN
                               HRMT01 h1 ON a5.SCHOOL_NO = h1.SCHOOL_NO AND a5.USER_NO = h1.USER_NO AND h1.USER_STATUS <> 9 where a5.SCHOOL_NO = {0}  and a5.GRADE={1}

                                      group by a5.SCHOOL_NO, a5.GRADE, h1.CLASS_NO, a5.USER_NO, h1.NAME, h1.SNAME, h1.SEAT_NO";

            if (!string.IsNullOrEmpty(Class_No))
            {
                BKID = @"SELECT  a5.SCHOOL_NO, a5.GRADE, h1.CLASS_NO, a5.USER_NO, h1.NAME, h1.SNAME, h1.SEAT_NO
                              ,{2} as SkipPage
                   FROM[ADDT05] a5 INNER JOIN
                               HRMT01 h1 ON a5.SCHOOL_NO = h1.SCHOOL_NO AND a5.USER_NO = h1.USER_NO AND h1.USER_STATUS <> 9 where a5.SCHOOL_NO = {0}  and a5.GRADE={1} and  h1.CLASS_NO='{3}'

                                      group by a5.SCHOOL_NO, a5.GRADE, h1.CLASS_NO, a5.USER_NO, h1.NAME, h1.SNAME, h1.SEAT_NO";
                sb.AppendFormat(BKID, SchoolNo, GRADE, PageInt, Class_No);
            }
            else { sb.AppendFormat(BKID, SchoolNo, GRADE, PageInt); }

            sb.Append(" order by CLASS_NO,SEAT_NO");
            aDDV02sItme = conn.Query<ADDV02>(sb.ToString()).ToList();
            // }
            BKID2 = @"SELECT  a5.SCHOOL_NO, a5.GRADE, h1.CLASS_NO, a5.USER_NO, h1.NAME, h1.SNAME, h1.SEAT_NO, RIGHT(BOOK_ID, 2) 
                                      AS BOOK_ID, 'Y' AS [READ],{2} as SkipPage
                   FROM       [ADDT05] a5 INNER JOIN
                                      HRMT01 h1 ON a5.SCHOOL_NO = h1.SCHOOL_NO AND a5.USER_NO = h1.USER_NO AND h1.USER_STATUS<>9 where a5.SCHOOL_NO={0}  and a5.GRADE={1}";

            if (!string.IsNullOrEmpty(Class_No))
            {
                BKID2 = BKID2 + " and h1.CLASS_NO='" + Class_No + "'";


            }

            sb1.AppendFormat(BKID2, SchoolNo, GRADE, PageInt);
            sb1.Append(" order by CLASS_NO,SEAT_NO");
  
            aDDV02sDetail = conn.Query<ADDV02>(sb1.ToString()).ToList();
            
            //取出書本數
            ADDT18 ltA18 = db.ADDT18.Where(p => p.GRADE == GRADE).FirstOrDefault();
            ViewBag.BookCount = ltA18.BOOK_SCCOUNT + ltA18.BOOK_SECOUNT;
            ViewBag.aDDV02sDetail = aDDV02sDetail;
            var ClassStringList = db.HRMT01.Where(h1 => h1.SCHOOL_NO == SchoolNo && string.IsNullOrEmpty(h1.CLASS_NO) == false).
                Select(h1 => h1.CLASS_NO).Distinct().OrderBy(a => a).ToList();
            ClassStringList.Insert(0, "All");

            List<SelectListItem> ClassList = ClassStringList.Select(x => new SelectListItem { Text = x, Value = x }).ToList();

            //if (string.IsNullOrEmpty(Class_No))
            //{
            //    if (user != null)
            //    {
            //        if (string.IsNullOrEmpty(user.TEACH_CLASS_NO) == false) Class_No = user.TEACH_CLASS_NO;
            //        if (string.IsNullOrEmpty(user.CLASS_NO) == false) Class_No = user.CLASS_NO;
            //    }
            //}
            if ((user.ROLE_LEVEL <= (decimal)4.00) && string.IsNullOrEmpty(Class_No) == false) // 各校管理者權限以下才隱藏
            {
                ClassList.Where(a => a.Value == Class_No).First().Selected = true;

                PassList = PassList.Where(v2 => v2.CLASS_NO == Class_No).ToList();

                ViewBag.hClass_No = Class_No;
                ViewBag.ClassNoItem = ClassList;
            }
            else { 

                if (string.IsNullOrEmpty(Class_No) == false && Class_No != "All" )
            {

                ClassList = new List<SelectListItem>();
                ClassList = ClassStringList.Where (x=>x== Class_No)
                    .Select(x => new SelectListItem { Text = x, Value = x }).ToList();
                ClassList.Where(a => a.Value == Class_No).First().Selected = true;

                PassList = PassList.Where(v2 => v2.CLASS_NO == Class_No).ToList();

                ViewBag.hClass_No = Class_No;
            }
                if (string.IsNullOrEmpty(whereGrade) == false)
                {
                    ClassList = new List<SelectListItem>();
                    ClassList = HRMT01.GetClassListData(SchoolNo, GRADE.ToString(), Class_No, ref db);
                    List<string> TempSelect = db.HRMT01.Where(u => string.IsNullOrEmpty(u.CLASS_NO) == false
                                && u.USER_TYPE == UserType.Student
                               && u.USER_STATUS == UserStaus.Enabled).Where(a => a.SCHOOL_NO == SchoolNo && a.GRADE == GRADE).Select(a => a.CLASS_NO).ToList();

                    PassList = PassList.Where(v2 => TempSelect.Contains(v2.CLASS_NO)).ToList();
                    ViewBag.ClassNoItem = ViewBag.ClassItems;
                    //ViewBag.hClass_No = ViewBag.ClassItems;
                }
                else if (string.IsNullOrEmpty(whereGrade)) {


                }
            }
            //學號/姓名
            ViewBag.whereKeyword = whereKeyword;

            if (string.IsNullOrEmpty(whereKeyword) == false)
            {
                PassList = PassList.Where(a => a.USER_NO.Contains(whereKeyword.Trim()) || a.NAME.Contains(whereKeyword.Trim())).ToList();
            }
          
            //SelectListItem listItem = new SelectListItem();
            //listItem = GradeList.Where(x => x.Value == "").FirstOrDefault();
            //GradeList.Remove(listItem);
            ViewBag.GradeItem = GradeList;
            return View(aDDV02sItme);
        }

        public ActionResult PrintBooK(int GRADE, string Class_No, string whereKeyword)
        {
            ViewBag.ADDT03List = db.ADDT03.Where(A => A.SCHOOL_NO == SchoolNo && A.GRADE == GRADE).OrderBy(A => A.BOOK_ID).ToList();

            IQueryable<SelectListItem> ClassStringList = HRMT01.GetClassListData(SchoolNo, ref db);

            if (string.IsNullOrWhiteSpace(Class_No) == false && Class_No != "All")
            {
                ClassStringList = ClassStringList.Where(a => a.Value.ToString() == Class_No);
            }

            ViewBag.ClassStringList = ClassStringList.ToList();

            var Data = new ADDI03Service().GetListData(SchoolNo, GRADE, Class_No, whereKeyword);

            return View(Data);
        }

        [HttpPost]
        public JsonResult GetBOOK_NAME(string SCHOOL_NO, string USER_NO, string GRADE, string TWO_BOOK_ID)
        {
            string Success = string.Empty;
            string ErrorMsg = string.Empty;
            string BOOK_NAME = string.Empty;

            ADDT05 ADDT05 = db.ADDT05.Where(a => a.SCHOOL_NO == SCHOOL_NO && a.USER_NO == USER_NO && a.BOOK_ID == GRADE + TWO_BOOK_ID).FirstOrDefault();
            if (ADDT05 != null)
            {
                BOOK_NAME = ADDT05.BOOK_NAME;
            }

            var data = "{\"BOOK_NAME\" : \"" + BOOK_NAME + "\" }";
            return Json(@data, JsonRequestBehavior.AllowGet);
        }

        static object GetPropertyValue(dynamic obj, string propertyName)
        {
            var type = obj.GetType();
            var property = type.GetProperty(propertyName);
            if (property != null)
            {
                return property.GetValue(obj, null);
            }
            return null;
        }
    }
}