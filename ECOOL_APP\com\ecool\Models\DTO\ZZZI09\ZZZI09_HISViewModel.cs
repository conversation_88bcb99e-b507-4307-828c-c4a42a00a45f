﻿using ECOOL_APP.EF;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace ECOOL_APP.com.ecool.Models.DTO
{
    public class ZZZI09_HISViewModel
    {
        /// <summary>
        /// 管理者下拉可選各校學生
        /// </summary>
        public string Where_SCHOOLNO_FORADMIN { get; set; }

        [Required(ErrorMessage = "畢業學年必選")]
        public int Where_SYEAR { get; set; }

        public string Where_CLASSNO { get; set; }

        [Required(ErrorMessage = "姓名座號必選")]
        public string Where_USERNO { get; set; }

        public string IDNO { get; set; }
        public string ReadYN { get; set; }
        [Required(ErrorMessage = "學校必選")]
        public string Where_SCHOOLNO { get; set; }

        [Required(ErrorMessage = "選擇匯出的模式")]
        public string Where_EXPORTTYPE { get; set; }

        public string School_No { get; set; }

        public string User_No { get; set; }
        public string SName { get; set; }
        public string Class_NO { get; set; }

        public string COVERJPG { get; set; }
        public string COVERJPGFileName { get; set; }
        public string UploadCoverFileName { get; set; }
        public HttpPostedFileBase UploadCoverFile { get; set; }
        public List<HRMT01> VotePeople { get; set; }
    }
}