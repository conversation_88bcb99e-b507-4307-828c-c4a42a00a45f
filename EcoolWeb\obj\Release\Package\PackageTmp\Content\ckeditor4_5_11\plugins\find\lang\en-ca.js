﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'find', 'en-ca', {
	find: 'Find',
	findOptions: 'Find Options',
	findWhat: 'Find what:',
	matchCase: 'Match case',
	matchCyclic: 'Match cyclic',
	matchWord: 'Match whole word',
	notFoundMsg: 'The specified text was not found.',
	replace: 'Replace',
	replaceAll: 'Replace All',
	replaceSuccessMsg: '%1 occurrence(s) replaced.',
	replaceWith: 'Replace with:',
	title: 'Find and Replace'
} );
