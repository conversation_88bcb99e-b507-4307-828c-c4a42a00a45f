/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Asana-Math/Alphabets/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.AsanaMathJax_Alphabets={directory:"Alphabets/Regular",family:"AsanaMathJax_Alphabets",testString:"\u0384\u0385\u0386\u0387\u0388\u0389\u038A\u038C\u038E\u038F\u0390\u03AA\u03AB\u03AC\u03AD",32:[0,0,249,0,0],900:[685,-476,374,134,241],901:[685,-476,332,-6,339],902:[700,3,777,15,756],903:[453,-327,216,45,172],904:[692,3,755,22,717],905:[692,3,987,22,966],906:[692,3,476,22,455],908:[709,20,863,22,842],910:[691,4,854,3,855],911:[709,6,904,34,871],912:[730,16,318,15,360],938:[819,3,388,22,367],939:[819,4,733,3,735],940:[730,16,594,44,571],941:[730,13,480,69,436],942:[730,275,531,-11,464],943:[730,16,310,94,305],944:[730,12,547,13,490],970:[645,16,310,15,360],971:[645,12,547,13,490],972:[730,20,545,32,514],973:[730,12,547,13,490],974:[730,12,715,36,659],5024:[692,4,793,22,751],5025:[692,3,667,22,669],5026:[692,3,612,18,595],5027:[699,-3,813,26,776],5028:[689,20,729,29,702],5029:[687,3,290,21,271],5030:[709,20,835,26,809],5031:[748,-49,647,27,617],5032:[692,3,577,22,559],5033:[692,0,709,50,675],5034:[700,3,777,15,756],5035:[692,196,509,22,487],5036:[692,3,610,22,572],5037:[697,3,785,27,758],5038:[689,3,519,22,499],5039:[693,0,602,20,577],5040:[692,3,505,22,483],5041:[692,3,555,22,536],5042:[687,1,806,14,781],5043:[692,9,967,8,956],5044:[674,20,878,32,846],5045:[692,3,555,22,580],5046:[710,20,730,22,692],5047:[692,13,1004,16,985],5048:[725,195,572,23,538],5049:[692,-4,920,11,908],5050:[709,20,1105,22,1073],5051:[692,3,831,22,810],5052:[692,19,525,27,493],5053:[692,-2,759,27,730],5054:[709,20,786,22,764],5055:[691,210,579,14,550],5056:[710,20,839,22,805],5057:[697,11,662,19,634],5058:[790,3,581,6,572],5059:[692,3,666,15,638],5060:[700,-2,616,24,583],5061:[708,21,1018,23,991],5062:[689,-4,466,25,441],5063:[691,12,715,41,671],5064:[692,4,525,29,509],5065:[705,6,1186,22,1168],5066:[652,12,715,38,671],5067:[710,20,547,30,524],5068:[692,20,778,12,759],5069:[698,20,839,19,814],5070:[698,3,563,2,534],5071:[692,3,603,22,580],5072:[692,3,526,14,507],5073:[693,11,549,10,514],5074:[692,3,696,22,674],5075:[692,195,509,22,487],5076:[693,6,869,9,859],5077:[709,20,578,24,544],5078:[692,3,665,30,633],5079:[692,11,466,10,446],5080:[691,0,447,15,427],5081:[693,6,681,8,661],5082:[709,19,525,24,503],5083:[692,36,725,18,716],5084:[719,33,674,25,649],5085:[693,0,500,12,478],5086:[692,3,611,22,586],5087:[733,20,709,22,664],5088:[692,196,647,26,625],5089:[722,13,902,32,884],5090:[692,3,604,22,580],5091:[710,20,764,22,742],5092:[692,0,636,10,606],5093:[692,3,680,22,658],5094:[692,3,726,22,692],5095:[692,-6,491,14,469],5096:[688,16,875,22,854],5097:[719,11,712,21,684],5098:[692,9,981,10,959],5099:[709,20,786,22,764],5100:[694,22,922,4,908],5101:[693,0,577,20,552],5102:[690,20,496,28,467],5103:[693,20,785,20,755],5104:[677,158,512,7,486],5105:[691,-2,596,27,565],5106:[728,3,590,23,567],5107:[759,9,840,22,814],5108:[692,3,610,26,576],8448:[436,72,719,29,691],8449:[436,72,719,34,686],8451:[709,20,899,27,873],8453:[436,72,719,24,696],8454:[436,72,729,18,712],8455:[719,5,549,23,527],8457:[707,3,755,22,734],8470:[692,20,1108,17,1076],8471:[705,164,906,18,889],8478:[692,15,667,22,669],8480:[668,-273,834,23,823],8481:[692,3,1099,33,1067],8482:[659,-282,929,15,917],8486:[704,6,824,34,791],8489:[473,16,310,94,304],8490:[692,3,725,22,719],8491:[943,3,777,15,756],8494:[535,17,599,44,561],8505:[706,3,332,34,298],8506:[638,104,919,18,902],8507:[692,3,1099,28,1072],8514:[694,0,540,68,473],8515:[694,0,540,68,473],8516:[694,0,665,3,663],8523:[689,21,777,43,753],8525:[663,56,777,39,739],8526:[456,4,418,33,390],65859:[700,0,671,55,630],65860:[700,0,671,55,630],65861:[700,0,671,55,630],65862:[700,0,671,55,630],65863:[700,0,671,55,630]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"AsanaMathJax_Alphabets"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Alphabets/Regular/Main.js"]);
