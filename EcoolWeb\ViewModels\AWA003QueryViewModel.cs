﻿using ECOOL_APP.EF;
using MvcPaging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace EcoolWeb.ViewModels
{
    public class AWA003QueryViewModel
    {
        /// <summary>
        /// 只顯示某一所學校
        /// </summary>
        public string whereSchoolNo { get; set; }


        /// <summary>
        /// 搜尋字串
        /// </summary>
        public string whereKeyword {get;set;}

        /// <summary>
        /// 只顯示某一位學生的文章
        /// </summary>
        public string whereUserNo { get; set; }

        /// <summary>
        /// 只顯示某一位學生的姓名
        /// </summary>
        public string whereName { get; set; }

        /// <summary>
        /// 只顯示某班級
        /// </summary>
        public string whereCLASS_NO { get; set; }

        /// <summary>
        /// 查詢起日
        /// </summary>
        public DateTime? whereSTART_CRE_DATE { get; set; }

        /// <summary>
        /// 查詢迄日
        /// </summary>
        public DateTime? whereEND_CRE_DATE { get; set; }

        /// <summary>
        /// 只顯示某年級
        /// </summary>
        public string whereGrade { get; set; }
        /// <summary>
        /// 只顯示某座號
        /// </summary>
        public string whereSeat_NO { get; set; }

        /// <summary>
        /// 月排行榜
        /// </summary>
        public bool WhereIsMonthTop { get; set; }

        public bool IsPrint { get; set; }

        public bool IsToExcel { get; set; }

        public string whereSOURCETABLE { get; set; }
       
        /// <summary>
        /// 排序欄位
        /// </summary>
        public string OrdercColumn { get; set; }

        public int Page { get; set; }


        /// <summary>
        /// 是否輪播
        /// </summary>
        public bool isCarousel { get; set; }
        public int PageInt { get; set; }

        /// <summary>
        /// 查詢結果
        /// </summary>
        public IPagedList<HRMT01QTY> VAWA003List;

        public AWA003QueryViewModel()
        {
            Page = 0;
            OrdercColumn = "CASH_ALL";
            WhereIsMonthTop = false;
        }
    }
}