﻿using ECOOL_APP.com.ecool.Models.DTO;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;

namespace ECOOL_APP.EF
{
    public class ZZZI34EditViewModel
    {
        public string SaveType { get; set; }
        public string ZZZI34ImgType { get; set; }
        public bool IsTempSave { get; set; }
        public byte? ModeValue { get; set; }
        public string radFileType { get; set; }

        public ZZZI34SearchViewModel Search { get; set; }

        /// <summary>
        /// 增加明細筆數
        /// </summary>
        public int? ADDNUM;

        public ZZZI34EditMainViewModel Main { get; set; }

        public virtual ICollection<ZZZI34EditPeopleViewModel> People { get; set; }
        public List<ZZZI34DetailsViewModel> Details_List { get; set; }

        public class SaveTypeVal
        {
            /// <summary>
            /// 暫存
            /// </summary>
            static public string TempSave = "TempSave";

            /// <summary>
            /// 存檔
            /// </summary>
            static public string Save = "Save";

            /// <summary>
            /// 發佈
            /// </summary>
            static public string Release = "Release";

            /// <summary>
            /// 審核/公開
            /// </summary>
            static public string Verify = "Verify";

            /// <summary>
            /// 作廢
            /// </summary>
            static public string Disabled = "Disabled";
        }

        public class ShowBtnVal
        {
            /// <summary>
            /// 無法異動
            /// </summary>
            static public int False = 0;

            /// <summary>
            /// 可異動
            /// </summary>
            static public int True = 1;

            /// <summary>
            /// 審核
            /// </summary>
            static public int Verify = 2;

            /// <summary>
            /// 不可異動酷幣
            /// </summary>
            static public int NotCash = 3;
        }
    }
}