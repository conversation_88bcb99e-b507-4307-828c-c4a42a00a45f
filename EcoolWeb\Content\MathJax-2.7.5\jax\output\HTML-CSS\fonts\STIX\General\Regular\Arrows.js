/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/General/Regular/Arrows.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.STIXGeneral,{8602:[450,-58,926,60,866],8603:[450,-58,926,60,866],8604:[411,-102,926,70,856],8605:[411,-102,926,70,856],8606:[449,-58,926,70,856],8607:[662,154,511,60,451],8608:[449,-58,926,70,856],8609:[662,154,511,60,451],8610:[449,-58,926,70,856],8611:[449,-58,926,70,856],8612:[450,-57,926,70,857],8613:[662,154,511,60,451],8615:[662,154,511,59,451],8616:[662,154,511,59,451],8619:[553,0,926,70,856],8620:[553,0,926,70,856],8621:[449,-58,1200,49,1151],8622:[450,-58,926,38,888],8623:[662,154,511,60,451],8624:[662,156,463,30,424],8625:[662,156,463,39,433],8626:[662,154,463,25,419],8627:[662,154,463,39,433],8628:[662,154,926,70,856],8629:[662,156,926,70,856],8630:[534,0,926,44,882],8631:[534,0,926,44,882],8632:[732,156,926,55,872],8633:[598,92,926,60,866],8634:[686,116,974,116,858],8635:[686,116,974,116,858],8638:[662,156,511,222,441],8639:[662,156,511,69,288],8642:[662,156,511,222,441],8643:[662,156,511,69,288],8644:[598,92,926,71,856],8645:[662,156,773,31,742],8646:[598,92,926,71,856],8647:[599,92,926,70,856],8648:[662,156,773,41,732],8649:[599,92,926,70,856],8650:[662,156,773,41,732],8651:[539,33,926,70,856],8653:[551,45,926,60,866],8654:[517,10,926,20,906],8655:[551,45,926,60,866],8662:[662,156,926,55,874],8663:[662,156,926,55,874],8664:[662,156,926,55,874],8665:[662,156,926,55,874],8666:[644,139,926,46,852],8667:[645,138,926,74,880],8668:[449,-58,926,60,866],8669:[449,-58,926,60,866],8670:[662,156,511,60,451],8671:[662,156,511,60,451],8672:[449,-58,926,60,866],8673:[662,156,511,60,451],8674:[449,-58,926,60,866],8675:[662,156,511,60,451],8676:[450,-58,926,60,866],8677:[450,-58,926,60,866],8678:[551,45,926,60,866],8679:[662,156,685,45,641],8680:[551,45,926,60,866],8681:[662,156,685,45,641],8682:[690,184,685,45,641],8692:[448,-57,926,70,856],8693:[662,156,773,31,742],8694:[739,232,926,60,866],8695:[450,-58,926,60,866],8696:[450,-58,926,55,861],8697:[450,-58,926,48,878],8698:[450,-58,926,60,866],8699:[450,-58,926,60,866],8700:[450,-58,926,38,888],8701:[449,-57,926,60,866],8702:[449,-57,926,60,866],8703:[449,-57,926,20,906]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/Regular/Arrows.js");
