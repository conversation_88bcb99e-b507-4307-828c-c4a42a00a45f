﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.com.ecool.Models.DTO
{
    public class ZZZI13ViewModel
    {
        public string SCHOOL_NO { get; set; }

        public string USER_NO { get; set; }

        public string  CLASS_NO { get; set; }
        public string NAME { get; set; }

        public List<ZZZI13ViewModel_D> Details_List { get; set; }

    }

    public class ZZZI13ViewModel_D
    {
        public string ROLE_ID { get; set; }

        public string ROLE_NAME { get; set; }

        public bool Checked { get; set; }


    }

}
