﻿

<table border="0" cellspacing="0" cellpadding="0" style="font-size:12pt; color:black" align="center">

    <tr>
        <td>
            <img src="~/Content/img/web-student_allpage-04-top.png" />
        </td>
    </tr>
    <tr>
        <td align="center" style="background-image:url('@Url.Content("~/Content/img/web-student_allpage-04-body.png")');background-repeat:repeat-y">
            @Html.ActionLink("線上投稿", "Index", "ADDI01")
            <br />  @Html.ActionLink("閱讀認證", "ADDTList", "ADDT")
            <br />  @Html.ActionLink("閱讀認證批次", "UPLOAD", "ADDI02")
            <br />  @Html.ActionLink("閱讀護照", "rpp", "rpp")
            <br /> @Html.ActionLink("有獎徵答", "", "")
            <br />@Html.ActionLink("校內表現", "", "")
        </td>
    </tr>
    <tr>
        <td>
            <img src="~/Content/img/web-student_allpage-04-footer.png" style="vertical-align:top" />
        </td>
    </tr>

    <tr>
        <td height="5px"></td>
    </tr>
    <tr>
        <td>
            <img src="~/Content/img/web-student_allpage-05-top.png" />
        </td>
    </tr>
    <tr>
        <td align="center" style="background-image:url('@Url.Content("~/Content/img/web-student_allpage-05-body.png")');background-repeat:repeat-y">
            @Html.ActionLink("兌換獎品", "AwatQ02", "Awat")
            @*@Html.ActionLink("兌換獎品", "SampleProduct", "Sample")*@
            @*<br />  @Html.ActionLink("獎品管理", "SampleProductMana", "Sample")*@
            <br />  @Html.ActionLink("角色娃娃", "AwatQ02", "Awat")
        </td>
    </tr>
    <tr>
        <td><img src="~/Content/img/web-student_allpage-05-footer.png" style="vertical-align:top" /></td>

    </tr>


    <tr>
        <td height="5px"></td>
    </tr>
    <tr>
        <td>
            <img src="~/Content/img/web-student_allpage-06-top.png" />
        </td>
    </tr>
    <tr>
        <td align="center" style="background-image:url('@Url.Content("~/Content/img/web-student_allpage-06-body.png")');background-repeat:repeat-y">
            @Html.ActionLink("酷幣總數排行榜", "", "")
            <br />@Html.ActionLink("學生兌獎情形", "", "")
            <br />  @Html.ActionLink("線上投稿排行榜", "", "")
            <br />  @Html.ActionLink("閱讀認證排行榜", "", "")

        </td>
    </tr>
    <tr>
        <td>
            <img src="~/Content/img/web-student_allpage-06-footer.png" style="vertical-align:top" />
        </td>
    </tr>
</table>