﻿using ECOOL_APP;
using ECOOL_APP.EF;
using EcoolWeb.Models;
using EcoolWeb.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using MvcPaging;
using EcoolWeb.CustomAttribute;
using System.Net;
using System.IO;
using System.Data.Entity.Validation;
using System.Data.Entity;
using com.ecool.service;
using System.ComponentModel.DataAnnotations;
using Dapper;
using ECOOL_APP.com.ecool.service;
using System.Globalization;
using System.Data.SqlClient;
using System.Data;
using com.ecool.sqlConnection;

namespace EcoolWeb.Controllers
{
    [SessionExpire]
    public class ZZZI19Controller : Controller
    {
        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();
        private string SchoolNO = UserProfileHelper.GetSchoolNo();
        private UserProfile user = UserProfileHelper.Get();
        private string SCHOOL_NO = string.Empty;
        private string USER_NO = string.Empty;
        private static string Bre_NO = "ZZZI19";

        private string Bre_Name = BreadcrumbService.GetBRE_NAME(Bre_NO);

        // GET: ZZZI19
        [CheckPermission] //檢查權限
        public ActionResult Query(ZZZI19IndexViewModel model)
        {
            ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
            if (model == null) model = new ZZZI19IndexViewModel();

            string sSQL = $@"select * from ufnGetApLog(@SCHOOL_NO)";
            IQueryable<ZZZI19Hrmt01ViewModel> HRMT01List = db.Database.Connection.Query<ZZZI19Hrmt01ViewModel>(sSQL
            , new
            {
                SCHOOL_NO = SchoolNO,
            }).AsQueryable();
            //IQueryable<VAWA007> VAWA007List = db.VAWA007;
            //VAWA007List = db.VAWA007.Where(x=>x.SCHOOL_NO== SchoolNO);
            if (string.IsNullOrWhiteSpace(model.whereKeyword) == false)
            {
                HRMT01List = HRMT01List.Where(a => a.USER_NO.Contains(model.whereKeyword.Trim()) || a.NAME.Contains(model.whereKeyword.Trim()));
            }

            if (string.IsNullOrWhiteSpace(user.TEACH_CLASS_NO) == false)
            {
                model.whereClass_No = user.TEACH_CLASS_NO;
            }

            if (string.IsNullOrWhiteSpace(model.whereClass_No) == false)
            {
                HRMT01List = HRMT01List.Where(a => a.CLASS_NO == model.whereClass_No);
            }

            if (string.IsNullOrWhiteSpace(model.whereGrade) == false)
            {
                byte bGrade = Convert.ToByte(model.whereGrade);
                string GradeString = "";
                 GradeString = bGrade.ToString();
                HRMT01List = HRMT01List.Where(a => a.GRADE == GradeString);
            }

            if (string.IsNullOrWhiteSpace(model.whereStatus) == false)
            {
                byte bUSER_STATUS = Convert.ToByte(model.whereStatus);
                HRMT01List = HRMT01List.Where(a => a.USER_STATUS == bUSER_STATUS);
            }

            switch (model.OrdercColumn)
            {
                case "USER_NO":
                    HRMT01List = HRMT01List.OrderBy(a => a.USER_NO);
                    break;
                case "CLASS_NO":
                    HRMT01List = HRMT01List.OrderBy(a => a.CLASS_NO);
                    break;
                case "GRADE":
                    HRMT01List = HRMT01List.OrderBy(a => a.GRADE);
                    break;
                case "SEAT_NO":
                    HRMT01List = HRMT01List.OrderBy(a => a.SEAT_NO);
                    break;

                case "USER_STATUS":
                    HRMT01List = HRMT01List.OrderBy(a => a.USER_STATUS).ThenBy(a => a.CLASS_NO).ThenBy(a => a.NO);
                    break;

                case "LoginCount":
                    HRMT01List = HRMT01List.OrderByDescending(a => a.LoginCount).ThenBy(a => a.CLASS_NO).ThenBy(a => a.NO);
                    break;

                default:
                    HRMT01List = HRMT01List.OrderBy(a => a.GRADE).ThenBy(a => a.CLASS_NO).ThenBy(a => a.SEAT_NO);
                    break;
            }

            int iPageCount = (model.ShowPageCount != null) ? Convert.ToInt32(model.ShowPageCount) : 100;
            model.HRMT01List = HRMT01List.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, iPageCount);

            model.DataList = null;

            model.DataList = new List<ZZZI19CheckBoxListViewModel>();

            ZZZI19CheckBoxListViewModel ReturnData = null;
            foreach (var item in model.HRMT01List)
            {
                ReturnData = new ZZZI19CheckBoxListViewModel();
                ReturnData.Chk = false;
                ReturnData.SCHOOL_NO = item.SCHOOL_NO;
                ReturnData.USER_NO = item.USER_NO;
                model.DataList.Add(ReturnData);
            }
            int SCHOOLCount = 0;
            SCHOOLCount = db.BDMT01.Where(x => x.CITY != "臺北市" && x.SCHOOL_NO== user.SCHOOL_NO).Count();
            ViewBag.IsADDOne = false;
            if (SCHOOLCount > 0) {
                ViewBag.IsADDOne = true;

            }
            //班級
            var ClassNoItem = HRMT01.GetClassListData(user.SCHOOL_NO, model.whereGrade, model.whereClass_No, ref db);

            if (!string.IsNullOrWhiteSpace(user.TEACH_CLASS_NO) && user.ROLE_TYPE == HRMT24_ENUM.RoleTypeVal.SchoolLevel)
            {
                ClassNoItem = ClassNoItem.Where(a => a.Value == user.TEACH_CLASS_NO).ToList();
            }
            ViewBag.ClassNoItem = ClassNoItem;

            //年級
            ViewBag.GradeItem = HRMT01.GetGradeItems(model.whereGrade);

            //狀態
            ViewBag.StatusItem = UserStaus.GetUserStausItemsALL(model.whereStatus);

            List<SelectListItem> PageCountItem = new List<SelectListItem>();
            PageCountItem.Add(new SelectListItem() { Text = "100", Value = "100", Selected = iPageCount.ToString() == "100" });
            PageCountItem.Add(new SelectListItem() { Text = "200", Value = "200", Selected = iPageCount.ToString() == "200" });
            PageCountItem.Add(new SelectListItem() { Text = "500", Value = "500", Selected = iPageCount.ToString() == "500" });
            ViewBag.PageCount = PageCountItem;

            //轉學生酷幣轉匯

            var PermissionBtn = PermissionService.GetActionPermissionForBreNO("ZZZI20", SchoolNO, user.USER_NO);
            //判斷是否有[我要投稿]權限
            ViewBag.VisibleTurnIn = (PermissionBtn.Where(a => a.ActionName == "Index").Select(a => a.BoolUse).FirstOrDefault() == true ? "Y" : "N");

            ViewBag.ShowTurnInCount = 0;

            List<HRMV01> TurnInList = new List<HRMV01>();
            //先找出該校有多少轉入學生
            var Students = db.HRMV01.Where(a => a.SCHOOL_NO == SchoolNO && a.USER_STATUS== UserStaus.Disable && a.INIT_STATUS==0);
            //篩選還沒處理的
            foreach (HRMV01 s in Students)
            {
                if (s.USER_STATUS != UserStaus.Disable) continue;
                if (s.INIT_STATUS != 0) continue;

                AWAT01 UserCash = db.AWAT01.Where(uu => uu.SCHOOL_NO == s.SCHOOL_NO && uu.USER_NO == s.USER_NO).FirstOrDefault();
                if (UserCash != null)
                {
                    if (UserCash.CASH_ALL > 0) continue;
                }

                TurnInList.Add(s);
            }
            ViewBag.ShowTurnInCount = TurnInList.Count;

            return View(model);
        }
        public ActionResult AddHrt01Single(ZZZI19Hrmt01ViewModel hrnt) {
            int IDNOCount = 0;
            IDNOCount = db.Temp_STUDENT.Where(x => x.STDNO == hrnt.USER_NO && x.IDNO != hrnt.IDNO).Count();
            if (IDNOCount > 0) {
                Temp_STUDENT temp_STUDENT = new Temp_STUDENT();
                //刪除資料
                temp_STUDENT = db.Temp_STUDENT.Where(x => x.STDNO == hrnt.USER_NO && x.IDNO != hrnt.IDNO).FirstOrDefault();
                db.Temp_STUDENT.Remove(temp_STUDENT);
            }
            int IDNOCount1 = 0;
            IDNOCount1 = db.Temp_STUDENT.Where(x => x.STDNO != hrnt.USER_NO && x.IDNO == hrnt.IDNO).Count();
            if (IDNOCount1 > 0)
            {
                Temp_STUDENT temp_STUDENT = new Temp_STUDENT();
                //刪除資料
                temp_STUDENT = db.Temp_STUDENT.Where(x => x.STDNO != hrnt.USER_NO && x.IDNO == hrnt.IDNO).FirstOrDefault();
                db.Temp_STUDENT.Remove(temp_STUDENT);
            }

            int IDNOHrmt01Count = 0;
            IDNOHrmt01Count = db.HRMT01.Where(x => x.USER_NO == hrnt.USER_NO && x.IDNO != hrnt.IDNO).Count();
            if (IDNOHrmt01Count > 0)
            {
                HRMT01 rMT01 = new HRMT01();
                //刪除資料
                rMT01 = db.HRMT01.Where(x => x.USER_NO == hrnt.USER_NO && x.IDNO != hrnt.IDNO).FirstOrDefault();
                db.HRMT01.Remove(rMT01);
            }
            int IDNOHrmt01Count1 = 0;
            IDNOHrmt01Count1 = db.HRMT01.Where(x => x.USER_NO != hrnt.USER_NO && x.IDNO == hrnt.IDNO).Count();
            if (IDNOHrmt01Count1 > 0)
            {
                HRMT01 rMT01 = new HRMT01();
                //刪除資料
                rMT01 = db.HRMT01.Where(x => x.USER_NO != hrnt.USER_NO && x.IDNO == hrnt.IDNO).FirstOrDefault();
                db.HRMT01.Remove(rMT01);

            }
            int SameCount = 0;
            SameCount = db.Temp_STUDENT.Where(x => x.STDNO == hrnt.USER_NO && x.IDNO == hrnt.IDNO).Count();
            if (SameCount > 0)
            {
                Temp_STUDENT temp_STUDENT = new Temp_STUDENT();
                //updet 資料

                temp_STUDENT = db.Temp_STUDENT.Where(x => x.STDNO == hrnt.USER_NO && x.IDNO == hrnt.IDNO).FirstOrDefault();
                temp_STUDENT.NAME = hrnt.NAME;
                temp_STUDENT.SEX =hrnt.SEX;
                temp_STUDENT.SEYEAR = hrnt.SYEAR;
                temp_STUDENT.CLASSNO = hrnt.CLASS_NO;
                temp_STUDENT.NO = hrnt.SEAT_NO;
                temp_STUDENT.BIRTHDAY = hrnt.BIRTHDAY;
                temp_STUDENT.STATUS = "0";
            }

            int SamHrmt01eCount = 0;
            SameCount = db.HRMT01.Where(x => x.USER_NO == hrnt.USER_NO && x.IDNO == hrnt.IDNO).Count();
            if (SamHrmt01eCount > 0)
            {
                //updet 資料
                HRMT01 rMT01 = new HRMT01();
                rMT01 = db.HRMT01.Where(x => x.USER_NO == hrnt.USER_NO && x.IDNO == hrnt.IDNO && x.USER_STATUS == 1).FirstOrDefault();
                rMT01.NAME = hrnt.NAME;
                rMT01.SEX = hrnt.SEX;
                rMT01.SYEAR = byte.Parse(hrnt.SYEAR);
                rMT01.GRADE = byte.Parse(hrnt.GRADE);
                rMT01.CLASS_NO = hrnt.CLASS_NO;
                rMT01.SEAT_NO = hrnt.SEAT_NO;
                rMT01.BIRTHDAY = DateTime.Parse(hrnt.BIRTHDAY);
                rMT01.USER_STATUS = 1;
            }
            db.SaveChanges();

            ECOOL_DEVEntities db1 = new ECOOL_DEVEntities();
            if (SameCount == 0 || SameCount == null) {
                Temp_STUDENT temp_STUDENT = new Temp_STUDENT();
                int IDM = 0;
                IDM = db.Temp_STUDENT.Max(x => x.ID);
                //新增資料
                temp_STUDENT.SCHOOL_NO = user.SCHOOL_NO;
                temp_STUDENT.STDNO = hrnt.USER_NO;
                temp_STUDENT.ID = IDM + 1;
                temp_STUDENT.NAME = hrnt.NAME;
                temp_STUDENT.SEX = hrnt.SEX;
                temp_STUDENT.YEAR= hrnt.CLASS_NO.Substring(0,1);
                temp_STUDENT.SEYEAR = hrnt.SYEAR;
                temp_STUDENT.CLASSNO = hrnt.CLASS_NO.Substring(1, 3);
                temp_STUDENT.NO = hrnt.SEAT_NO;

                temp_STUDENT.IDNO = hrnt.IDNO;
                temp_STUDENT.BIRTHDAY = DateTime.Parse(hrnt.BIRTHDAY).Year.ToString() + DateTime.Parse(hrnt.BIRTHDAY).Month.ToString() + DateTime.Parse(hrnt.BIRTHDAY).Day.ToString();
                temp_STUDENT.STATUS = "0";
                db1.Temp_STUDENT.Add(temp_STUDENT);
            }


          
            if (SamHrmt01eCount == 0 || SamHrmt01eCount == null)
            {
                HRMT01 rMT01 = new HRMT01();
                rMT01.SCHOOL_NO = user.SCHOOL_NO;
                rMT01.USER_NO = hrnt.USER_NO;
                rMT01.USER_KEY = user.SCHOOL_NO+"_"+hrnt.USER_NO;
                //新增資料
                rMT01.NAME = hrnt.NAME;
                rMT01.SNAME = hrnt.NAME.Substring(1, hrnt.NAME.Length-1);
                rMT01.SEX = hrnt.SEX;
                rMT01.SYEAR = byte.Parse(hrnt.SYEAR);
                rMT01.GRADE = byte.Parse(hrnt.GRADE);
                rMT01.CLASS_NO = hrnt.CLASS_NO;
                rMT01.SEAT_NO = hrnt.SEAT_NO;
                rMT01.BIRTHDAY = DateTime.Parse(hrnt.BIRTHDAY);
                rMT01.USER_STATUS = 1;
                rMT01.USER_TYPE = "S";
                rMT01.IDNO = hrnt.IDNO;
                rMT01.INIT_STATUS = 0;
                db1.HRMT01.Add(rMT01);

            }
            db1.SaveChanges();
            return View();
        }
        /// <summary>
        /// 啟用
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        public ActionResult StartUser(ZZZI19IndexViewModel model)
        {
            UpDataStatus(model.DataList, UserStaus.Enabled);
            return RedirectToAction("Query", model);
        }

        /// <summary>
        /// 停用
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        public ActionResult CancelUser(ZZZI19IndexViewModel model)
        {
            UpDataStatus(model.DataList, UserStaus.Disable);
            return RedirectToAction("Query", model);
        }
        public ActionResult Edit(ZZZI19Hrmt01ViewModel model)
        {
            this.Shared();
            if (model == null) model = new ZZZI19Hrmt01ViewModel();
            ZZZI19Hrmt01ViewModel zZZI19Hrmtreseponse = new ZZZI19Hrmt01ViewModel();
            int SCHOOLCount = 0;
            SCHOOLCount = db.BDMT01.Where(x => x.CITY != "臺北市" && x.SCHOOL_NO == user.SCHOOL_NO).Count();
            ViewBag.IsADDOne = false;
            if (SCHOOLCount > 0)
            {
                ViewBag.IsADDOne = true;

            }
            model.USER_NO = "";
            model.NAME = "";
            model.GRADE = "00";
            ViewBag.GradeItem = HRMT01.GetGradeItems(model.whereGrade);

            ViewBag.ClassItems = HRMT01.GetClassListData(SchoolNO, model.whereGrade, "", ref db);
            ViewBag.YearItems = HRMT01.GetYearListData(SchoolNO, "", ref db);
            return View(model);
        }
        public ActionResult Edit1(ZZZI19IndexViewModel model)
        {
            this.Shared();
            if (model == null) model = new ZZZI19IndexViewModel();
            HRMT01 rMT01 = new HRMT01();
            rMT01 = db.HRMT01.Where(x => x.USER_NO == model.whereUserNo && x.SCHOOL_NO == model.whereKeyword &&( x.USER_STATUS == UserStaus.Enabled || x.USER_STATUS == UserStaus.Disable)).FirstOrDefault();

           
          ZZZI19Hrmt01ViewModel zZZI19Hrmtreseponse = new ZZZI19Hrmt01ViewModel();
            model.zZZI19Hrmt = new ZZZI19Hrmt01ViewModel();
           zZZI19Hrmtreseponse.USER_NO = rMT01.USER_NO;
            zZZI19Hrmtreseponse.NAME = rMT01.NAME;
            zZZI19Hrmtreseponse.IDNO = rMT01.IDNO;
            zZZI19Hrmtreseponse.USER_STATUS = rMT01.USER_STATUS;
            model.zZZI19Hrmt = zZZI19Hrmtreseponse;
            //zZZI19Hrmtreseponse.NAME = "";
            //zZZI19Hrmtreseponse.GRADE = "00";
            //ViewBag.GradeItem = HRMT01.GetGradeItems(model.whereGrade);
            //model.zZZI19Hrmt.NAME = "輸入名稱";
            //ViewBag.ClassItems = HRMT01.GetClassListData(SchoolNO, model.whereGrade, model.zZZI19Hrmt.whereCLASS_NO, ref db);
            //ViewBag.YearItems = HRMT01.GetYearListData(SchoolNO, "", ref db);
            return View(model);
        }
        private void Shared(string Panel_Title = "")
        {
            ViewBag.BRE_NO = Bre_NO;

            if (!string.IsNullOrWhiteSpace(Panel_Title))
            {
                ViewBag.Panel_Title = Panel_Title;
            }
            else
            {
                ViewBag.Panel_Title = Bre_Name + " - 列表 ";
            }

            user = UserProfileHelper.Get();
            SCHOOL_NO = UserProfileHelper.GetSchoolNo();

            if (user != null)
            {
                if (user.SCHOOL_NO != null)
                {
                    SCHOOL_NO = user.SCHOOL_NO;
                    USER_NO = user.USER_NO;
                }
            }

            ViewBag.Permission = PermissionService.GetActionPermissionForBreNO(UserProfileHelper.GetBRE_NO(), user?.SCHOOL_NO, user?.USER_NO)?.Where(a => a.BoolUse)?.ToList();
        }

        [CheckPermission(CheckACTION_ID = "Query")] //檢查權限
        public ActionResult EditSave1(FormCollection fc)
        {
            ZZZI19Hrmt01ViewModel model = new ZZZI19Hrmt01ViewModel();
            if (!string.IsNullOrWhiteSpace(fc["zZZI19Hrmt.USER_STATUS"]))
            {
                var TEMPSTUDENTCount= 0;
                var Hrmt01Count = 0;
                model.USER_NO = fc["zZZI19Hrmt.USER_NO"];
                model.IDNO = fc["zZZI19Hrmt.IDNO"];
                string USERSTATUS = "";
                string[] split2 = fc["zZZI19Hrmt.USER_STATUS"].Split(',');

                USERSTATUS = split2[0];
               model.USER_STATUS = byte.Parse(USERSTATUS);
                model.SCHOOL_NO = SchoolNO;
                ZZZI19IndexViewModel model1 = new ZZZI19IndexViewModel();
                //相同學號
                TEMPSTUDENTCount = db.Temp_STUDENT.Where(x => x.SCHOOL_NO == SchoolNO && x.STDNO == model.USER_NO).Count();
                Hrmt01Count = db.HRMT01.Where(x=>x.SCHOOL_NO == SchoolNO && x.USER_NO == model.USER_NO).Count();
                var ClassNoItem = HRMT01.GetClassListData(user.SCHOOL_NO, model.whereGrade, model1.whereClass_No, ref db);

                if (!string.IsNullOrWhiteSpace(user.TEACH_CLASS_NO) && user.ROLE_TYPE == HRMT24_ENUM.RoleTypeVal.SchoolLevel)
                {
                    ClassNoItem = ClassNoItem.Where(a => a.Value == user.TEACH_CLASS_NO).ToList();
                }
                ViewBag.ClassNoItem = ClassNoItem;

                //年級
                ViewBag.GradeItem = HRMT01.GetGradeItems(model.whereGrade);

                //狀態
                ViewBag.StatusItem = UserStaus.GetUserStausItemsALL(model1.whereStatus);
                //在校狀態(0:在校, 2:離校)
                if (TEMPSTUDENTCount > 0 || Hrmt01Count > 0) {
                    Temp_STUDENT _STUDENT = new Temp_STUDENT();
                    HRMT01 rMT01 = new HRMT01();
                    rMT01 = db.HRMT01.Where(x=>x.USER_NO == model.USER_NO && x.SCHOOL_NO== SchoolNO && x.IDNO == model.IDNO).FirstOrDefault();
                    _STUDENT = db.Temp_STUDENT.Where(x => x.SCHOOL_NO == SchoolNO && x.STDNO == model.USER_NO && x.IDNO == model.IDNO).FirstOrDefault();
                    //班級
         
                    if (_STUDENT != null) {
                        if (model.USER_STATUS == 9)
                        {
                            //rMT01.USER_STATUS = model.USER_STATUS;
                            _STUDENT.STATUS = "2";
                        }
                        if (model.USER_STATUS == 1)
                        {
                          //  rMT01.USER_STATUS = model.USER_STATUS;

                            _STUDENT.STATUS = "0";
                        }
                    }
                    if (rMT01 != null) {

                        if (model.USER_STATUS == 9)
                        {
                            rMT01.USER_STATUS = model.USER_STATUS;
                            //_STUDENT.STATUS = "2";
                        }
                        if (model.USER_STATUS == 1)
                        {
                            rMT01.USER_STATUS = model.USER_STATUS;

                           // _STUDENT.STATUS = "0";
                        }
                    }
                    string sSQL = $@"select * from ufnGetApLog(@SCHOOL_NO)";
                    IQueryable<ZZZI19Hrmt01ViewModel> HRMT01List = db.Database.Connection.Query<ZZZI19Hrmt01ViewModel>(sSQL
                    , new
                    {
                        SCHOOL_NO = SchoolNO,
                    }).AsQueryable();
                    int iPageCount = (model1.ShowPageCount != null) ? Convert.ToInt32(model1.ShowPageCount) : 100;
                    model1.HRMT01List = HRMT01List.ToPagedList(model1.Page > 0 ? model1.Page - 1 : 0, iPageCount);

                    model1.DataList = null;

                    model1.DataList = new List<ZZZI19CheckBoxListViewModel>();

                    ZZZI19CheckBoxListViewModel ReturnData = null;
                    foreach (var item in model1.HRMT01List)
                    {
                        ReturnData = new ZZZI19CheckBoxListViewModel();
                        ReturnData.Chk = false;
                        ReturnData.SCHOOL_NO = item.SCHOOL_NO;
                        ReturnData.USER_NO = item.USER_NO;
                        model1.DataList.Add(ReturnData);
                    }

                    List<SelectListItem> PageCountItem = new List<SelectListItem>();
                    PageCountItem.Add(new SelectListItem() { Text = "100", Value = "100", Selected = iPageCount.ToString() == "100" });
                    PageCountItem.Add(new SelectListItem() { Text = "200", Value = "200", Selected = iPageCount.ToString() == "200" });
                    PageCountItem.Add(new SelectListItem() { Text = "500", Value = "500", Selected = iPageCount.ToString() == "500" });
                    ViewBag.PageCount = PageCountItem;
                    try
                    {
                      
                        db.SaveChanges();
                        TempData["StatusMessage"] = "轉出成功";
                     
                        //轉學生酷幣轉匯

                        var PermissionBtn = PermissionService.GetActionPermissionForBreNO("ZZZI20", SchoolNO, user.USER_NO);
                        //判斷是否有[我要投稿]權限
                        ViewBag.VisibleTurnIn = (PermissionBtn.Where(a => a.ActionName == "Index").Select(a => a.BoolUse).FirstOrDefault() == true ? "Y" : "N");

                        ViewBag.ShowTurnInCount = 0;

                        List<HRMV01> TurnInList = new List<HRMV01>();
                        //先找出該校有多少轉入學生
                        var Students = db.HRMV01.Where(a => a.SCHOOL_NO == SchoolNO && a.USER_STATUS == UserStaus.Disable && a.INIT_STATUS == 0);
                        //篩選還沒處理的
                        foreach (HRMV01 s in Students)
                        {
                            if (s.USER_STATUS != UserStaus.Disable) continue;
                            if (s.INIT_STATUS != 0) continue;

                            AWAT01 UserCash = db.AWAT01.Where(uu => uu.SCHOOL_NO == s.SCHOOL_NO && uu.USER_NO == s.USER_NO).FirstOrDefault();
                            if (UserCash != null)
                            {
                                if (UserCash.CASH_ALL > 0) continue;
                            }

                            TurnInList.Add(s);
                        }
                        ViewBag.ShowTurnInCount = TurnInList.Count;
                        ViewBag.Msg = "轉出成功";
                        return RedirectToAction("Query", model);
                        
                    
                    }
                    catch (Exception e) {

                        TempData["StatusMessage"] = "出現錯誤，已轉出，再請您檢查一下，謝謝。";
                        return View("Edit1", model);
                    }
                }

                return RedirectToAction("Query", model);

                ////班級
                //var ClassNoItem = HRMT01.GetClassListData(user.SCHOOL_NO, model1.whereGrade, model1.whereClass_No, ref db);

                //if (!string.IsNullOrWhiteSpace(user.TEACH_CLASS_NO) && user.ROLE_TYPE == HRMT24_ENUM.RoleTypeVal.SchoolLevel)
                //{
                //    ClassNoItem = ClassNoItem.Where(a => a.Value == user.TEACH_CLASS_NO).ToList();
                //}
                //ViewBag.ClassNoItem = ClassNoItem;

                ////年級
                //ViewBag.GradeItem = HRMT01.GetGradeItems(model1.whereGrade);
                ////狀態
                //ViewBag.StatusItem = UserStaus.GetUserStausItemsALL(model1.whereStatus);


            }
            else {


                return View();
            }
        }
        [CheckPermission(CheckACTION_ID = "Query")] //檢查權限
        public ActionResult EditSave(FormCollection fc)
        {
            ZZZI19Hrmt01ViewModel model = new ZZZI19Hrmt01ViewModel();
            model.USER_NO= fc["USER_NO"];
            model.NAME = fc["NAME"];

            model.SYEAR = fc["SYEAR"];
            model.GRADE = fc["GRADE"];
            model.CLASS_NO = fc["CLASS_NO"];
            model.BIRTHDAY = fc["BIRTHDAY"];
            model.IDNO = fc["IDNO"];
            model.SEX= fc["SEX"];
            model.NO = fc["SEAT_NO"];
            model.SEAT_NO= fc["SEAT_NO"];
            ViewBag.IsADDOne = false;
          
            //model.USER_NO = "";
            //model.NAME = "";
            //model.GRADE = "00";
            ViewBag.GradeItem = HRMT01.GetGradeItems(model.whereGrade);

            ViewBag.ClassItems = HRMT01.GetClassListData(SchoolNO, model.whereGrade, "", ref db);
            ViewBag.YearItems = HRMT01.GetYearListData(SchoolNO, "", ref db);
            if (string.IsNullOrEmpty(fc["USER_NO"]) || string.IsNullOrEmpty(fc["NAME"]) || string.IsNullOrEmpty(fc["SYEAR"])
                || string.IsNullOrEmpty(fc["GRADE"]) || string.IsNullOrEmpty(fc["CLASS_NO"]) || string.IsNullOrEmpty(fc["BIRTHDAY"])
                || string.IsNullOrEmpty(fc["IDNO"]) || string.IsNullOrEmpty(fc["SEX"]) || string.IsNullOrEmpty(fc["SEAT_NO"])

                ) {
                TempData["StatusMessage"] = "所有欄位皆必填";
                return View("Edit", model);

            }
            model.SCHOOL_NO =SchoolNO;
            var TEMPSTUDENTCount = 0;
            var HRMTSTUDENTCount = 0;
            var TEMPSTUDENTCount1 = 0;
            var HRMT01STUDENTCount1 = 0;
            //相同學號
            TEMPSTUDENTCount = db.Temp_STUDENT.Where(x => x.SCHOOL_NO == SchoolNO && x.STDNO == model.USER_NO).Count();
            //相同學號
            HRMTSTUDENTCount = db.HRMT01.Where(x => x.SCHOOL_NO == SchoolNO && x.USER_NO == model.USER_NO).Count();
            //相同IDNO
            TEMPSTUDENTCount1 = db.Temp_STUDENT.Where(x => x.SCHOOL_NO == SchoolNO && x.IDNO == model.IDNO).Count();
            //相同IDNO
            HRMT01STUDENTCount1 = db.HRMT01.Where(x => x.SCHOOL_NO == SchoolNO && x.IDNO == model.IDNO).Count();
            int SCHOOLCount = 0;
            SCHOOLCount = db.BDMT01.Where(x => x.CITY != "臺北市" && x.SCHOOL_NO == SchoolNO).Count();
            if (SCHOOLCount > 0)
            {
                ViewBag.IsADDOne = true;

            }
            if (TEMPSTUDENTCount > 0 || TEMPSTUDENTCount1 > 0 || HRMTSTUDENTCount>0 || HRMT01STUDENTCount1>0)
            {
                TempData["StatusMessage"] = "出現錯誤，可能身分證字號有錯、學號重複或其他原因，請您檢查一下，謝謝。";
                return View("Edit",model);
            }
            else
            {

                int tempCount = 0;
                tempCount = db.Temp_STUDENT.ToList().Count();

                int i = 0;
                if (tempCount > 0)
                {
                    i = db.Temp_STUDENT.Select(X => X.ID).Max();
                }
                CultureInfo provider = CultureInfo.InvariantCulture;
                //新增到Temp_STUDENT 
                //執行 MERGE_TempHRMT_BySchool
                DateTime dateNow = new DateTime();
                dateNow = DateTime.Parse(model.BIRTHDAY);
                string BIRTHDAYstr = string.Format("{0:yyyyMMdd}", dateNow);
                string BIRTHDAYstr2 = string.Format("{0:yyyy-MM-dd}", dateNow);
                Temp_STUDENT temp_STUDENT = new Temp_STUDENT();
                temp_STUDENT.SCHOOL_NO = model.SCHOOL_NO;
                temp_STUDENT.ID = i + 1;
                temp_STUDENT.STDNO = model.USER_NO;
                temp_STUDENT.IDNO = model.IDNO;
                temp_STUDENT.NAME = model.NAME;
              
                temp_STUDENT.SEYEAR = model.SYEAR.ToString();
                temp_STUDENT.SEX = model.SEX;
                temp_STUDENT.BIRTHDAY = BIRTHDAYstr;
                temp_STUDENT.STATUS = "0";
                temp_STUDENT.CLASSNO = model.CLASS_NO.Substring(1,2).ToString(); 
                temp_STUDENT.YEAR = model.GRADE.ToString();
                temp_STUDENT.NO = model.NO;
                db.Entry(temp_STUDENT).State = System.Data.Entity.EntityState.Added;
                HRMT01 rMT01Temp = new HRMT01();
                rMT01Temp.SCHOOL_NO= model.SCHOOL_NO;
                rMT01Temp.USER_NO = model.USER_NO;
                rMT01Temp.NAME = model.NAME;

                rMT01Temp.SNAME = SubString(2, model.NAME);
                rMT01Temp.SYEAR = byte.Parse(model.SYEAR);
                rMT01Temp.SEX = model.SEX;
                rMT01Temp.GRADE = byte.Parse(model.GRADE);
                rMT01Temp.CLASS_NO = model.CLASS_NO;
                rMT01Temp.SEAT_NO = model.SEAT_NO;
                rMT01Temp.IDNO = model.IDNO;
                rMT01Temp.BIRTHDAY = DateTime.ParseExact(BIRTHDAYstr2, "yyyy-MM-dd", provider);
                rMT01Temp.USER_TYPE = "S";
                rMT01Temp.USER_STATUS = 1;
                rMT01Temp.INIT_STATUS = 0;
                db.Entry(rMT01Temp).State = System.Data.Entity.EntityState.Added;
                ZZT08 zZT08Info = new ZZT08();
                zZT08Info.SCHOOL_NO= model.SCHOOL_NO;
                zZT08Info.USER_NO = model.USER_NO;
                zZT08Info.PASSWORD = model.IDNO.Substring(model.IDNO.Length-4, 4);
                db.Entry(zZT08Info).State = System.Data.Entity.EntityState.Added;
                AWAT07 aWAT07temp = new AWAT07();
                aWAT07temp.SCHOOL_NO = model.SCHOOL_NO;
                try {
                    db.SaveChanges();
                    CreateDateAWAT07(aWAT07temp);
                }
                catch (Exception e)
                {
                    TempData["StatusMessage"] = "出現錯誤，可能身分證字號有錯、學號重複或其他原因，請您檢查一下，謝謝。";
                    return View("Edit", model);


                }
             
                //using (HrmtImportService importService = new HrmtImportService())
                //{


                //    importService.MERGETEMPHRMTBySchool(model.SCHOOL_NO);
                //}
            }

            ZZZI19IndexViewModel model1 = new ZZZI19IndexViewModel();
            string sSQL = $@"select * from ufnGetApLog(@SCHOOL_NO)";
            IQueryable<ZZZI19Hrmt01ViewModel> HRMT01List = db.Database.Connection.Query<ZZZI19Hrmt01ViewModel>(sSQL
            , new
            {
                SCHOOL_NO = SchoolNO,
            }).AsQueryable();
            int iPageCount = (model1.ShowPageCount != null) ? Convert.ToInt32(model1.ShowPageCount) : 100;
            model1.HRMT01List = HRMT01List.ToPagedList(model1.Page > 0 ? model1.Page - 1 : 0, iPageCount);

            model1.DataList = null;

            model1.DataList = new List<ZZZI19CheckBoxListViewModel>();

            ZZZI19CheckBoxListViewModel ReturnData = null;
            foreach (var item in model1.HRMT01List)
            {
                ReturnData = new ZZZI19CheckBoxListViewModel();
                ReturnData.Chk = false;
                ReturnData.SCHOOL_NO = item.SCHOOL_NO;
                ReturnData.USER_NO = item.USER_NO;
                model1.DataList.Add(ReturnData);
            }

            //班級
            var ClassNoItem = HRMT01.GetClassListData(user.SCHOOL_NO, model1.whereGrade, model1.whereClass_No, ref db);

            if (!string.IsNullOrWhiteSpace(user.TEACH_CLASS_NO) && user.ROLE_TYPE == HRMT24_ENUM.RoleTypeVal.SchoolLevel)
            {
                ClassNoItem = ClassNoItem.Where(a => a.Value == user.TEACH_CLASS_NO).ToList();
            }
            ViewBag.ClassNoItem = ClassNoItem;

            //年級
            ViewBag.GradeItem = HRMT01.GetGradeItems(model1.whereGrade);
            //狀態
            ViewBag.StatusItem = UserStaus.GetUserStausItemsALL(model1.whereStatus);

            List<SelectListItem> PageCountItem = new List<SelectListItem>();
            PageCountItem.Add(new SelectListItem() { Text = "100", Value = "100", Selected = iPageCount.ToString() == "100" });
            PageCountItem.Add(new SelectListItem() { Text = "200", Value = "200", Selected = iPageCount.ToString() == "200" });
            PageCountItem.Add(new SelectListItem() { Text = "500", Value = "500", Selected = iPageCount.ToString() == "500" });
            ViewBag.PageCount = PageCountItem;
            //轉學生酷幣轉匯

            var PermissionBtn = PermissionService.GetActionPermissionForBreNO("ZZZI20", SchoolNO, user.USER_NO);
            //判斷是否有[我要投稿]權限
            ViewBag.VisibleTurnIn = (PermissionBtn.Where(a => a.ActionName == "Index").Select(a => a.BoolUse).FirstOrDefault() == true ? "Y" : "N");

            ViewBag.ShowTurnInCount = 0;

            List<HRMV01> TurnInList = new List<HRMV01>();
            //先找出該校有多少轉入學生
            var Students = db.HRMV01.Where(a => a.SCHOOL_NO == SchoolNO && a.USER_STATUS == UserStaus.Disable && a.INIT_STATUS == 0);
            //篩選還沒處理的
            foreach (HRMV01 s in Students)
            {
                if (s.USER_STATUS != UserStaus.Disable) continue;
                if (s.INIT_STATUS != 0) continue;

                AWAT01 UserCash = db.AWAT01.Where(uu => uu.SCHOOL_NO == s.SCHOOL_NO && uu.USER_NO == s.USER_NO).FirstOrDefault();
                if (UserCash != null)
                {
                    if (UserCash.CASH_ALL > 0) continue;
                }

                TurnInList.Add(s);
            }
            ViewBag.ShowTurnInCount = TurnInList.Count;

            return RedirectToAction("Query", model);
        }
        public string SubString(int len, string Str) {
            if (Str.Length >= len) {
                int stratIndex = 0;
                if ((Str.Length - len) >= 1) {

                    stratIndex = Str.Length - len ;
                }
                Str = Str.Substring(stratIndex, len);


            }
            return Str;
        }
        public void CreateDateAWAT07(AWAT07 Date)
        {

            string ErrorMsg="";
             int ErrorInt = 0;
            using (SqlConnection conn = new sqlConnection().getConnection4Query())
            {
                SqlCommand cmd = conn.CreateCommand();
                SqlTransaction transaction = conn.BeginTransaction();
                cmd.Transaction = transaction;
                try
                {
                  

                    this.INSERT_NTO_AWAT07(conn, transaction, Date);

                    if (ErrorInt == 0)
                    {
                        transaction.Commit();
                    }
                    else
                    {
                        Date.SCHOOL_NO = null;
                        transaction.Rollback();
                        ErrorMsg = "新增資料失敗;\r\n" + ErrorMsg;
                    }
                }
                catch (Exception ex)
                {
                    Date.SCHOOL_NO = null;
                    transaction.Rollback();
                    ErrorMsg = "新增資料失敗;\r\n" + ex.Message;
                }
                finally
                {
                    if (cmd != null)
                    {
                        cmd.Dispose();
                    }

                    if (conn != null)
                    {
                        conn.Close();
                        conn.Dispose();
                    }
                }
            }
        }

        private void INSERT_NTO_AWAT07(SqlConnection conn, SqlTransaction transaction, AWAT07 Date)
        {
            IDbCommand cmd = new SqlCommand(@" insert into[AWAT07]
(

        [SCHOOL_NO]
      ,[USER_NO]
      ,[PLAYER_NO]
      ,[TRANS_DATE]
      ,[TRANS_CASH]
      ,[DEFAULT_YN]
)
select SCHOOL_NO, USER_NO
, ABS(CHECKSUM(NewId())) % 11 + 1 as PLAYER_NO,
GETDATE() as TRANS_DATE,
0 as TRANS_CASH,
1 as DEFAULT_YN
from HRMT01 h1
where SCHOOL_NO = @SCHOOL_NO  and not exists(select  SCHOOL_NO, USER_NO from AWAT07 a7 where h1.SCHOOL_NO = a7.SCHOOL_NO and h1.USER_NO = a7.USER_NO)
and h1.USER_TYPE = 'S'");
            cmd.Connection = conn;
            cmd.Transaction = transaction;

            cmd.Parameters.Add(
            (Date.SCHOOL_NO == null)

            ? new SqlParameter("@SCHOOL_NO", DBNull.Value)

            : new SqlParameter("@SCHOOL_NO", Date.SCHOOL_NO)); try
            {
                cmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
             
            }
            finally
            {
                cmd.Dispose();
            }

        }
        [CheckPermission(CheckACTION_ID = "Query")] //檢查權限
        public ActionResult CheckInfo(ZZZI19Hrmt01ViewModel model)
        {
            if (model == null) model = new ZZZI19Hrmt01ViewModel();
            //ZZZI19Hrmt01ViewModel model = new ZZZI19Hrmt01ViewModel();
            //model.USER_NO = fc.USER_NO;
            //model.NAME = fc["NAME"];

            //model.SYEAR = fc["SYEAR"];
            //model.GRADE = fc["GRADE"];
            //model.CLASS_NO = fc["CLASS_NO"];
            //model.BIRTHDAY = fc["BIRTHDAY"];
            //model.IDNO = fc["IDNO"];
            //model.SEX = fc["SEX"];
            //model.NO = fc["NO"];
            ViewBag.GradeItem = HRMT01.GetGradeItems(model.whereGrade);

            ViewBag.ClassItems = HRMT01.GetClassListData(SchoolNO, model.whereGrade, "", ref db);
            ViewBag.YearItems = HRMT01.GetYearListData(SchoolNO, "", ref db);
            var TEMPSTUDENTCount = 0;

            var TEMPSTUDENTCount1 = 0;
            string messege="";
            if (string.IsNullOrEmpty(model.USER_NO) || string.IsNullOrEmpty(model.NAME) || string.IsNullOrEmpty(model.SYEAR )|| string.IsNullOrEmpty(model.GRADE ) || string.IsNullOrEmpty(model.CLASS_NO) || string.IsNullOrEmpty(model.BIRTHDAY) || string.IsNullOrEmpty(model.IDNO) || string.IsNullOrEmpty(model.SEAT_NO )) {


                messege = "不可為空";
                TempData["StatusMessage"] = messege;
                return View("Edit", model);
            }
            //TEMPSTUDENTCount = db.Temp_STUDENT.Where(x => x.SCHOOL_NO == SchoolNO && x.STDNO == model.USER_NO).Count();
            //if (TEMPSTUDENTCount > 0) {

            //    messege = "相同學號學生";
            //    TempData["StatusMessage"] = messege;
            //    return View("Edit", model);
            //}
            //TEMPSTUDENTCount1 = db.Temp_STUDENT.Where(x => x.SCHOOL_NO == SchoolNO && x.IDNO == model.IDNO).Count();
            //if (TEMPSTUDENTCount1 > 0)
            //{
            //    messege = "相同身份證";
            //    TempData["StatusMessage"] = messege;
            //    return View("Edit", model);
            //}
            //班級
            var ClassNoItem = HRMT01.GetClassListData(user.SCHOOL_NO, model.whereGrade, "", ref db);

            if (!string.IsNullOrWhiteSpace(user.TEACH_CLASS_NO) && user.ROLE_TYPE == HRMT24_ENUM.RoleTypeVal.SchoolLevel)
            {
                ClassNoItem = ClassNoItem.Where(a => a.Value == user.TEACH_CLASS_NO).ToList();
            }
            ZZZI19IndexViewModel model1 = new ZZZI19IndexViewModel();
            string sSQL = $@"select * from ufnGetApLog(@SCHOOL_NO)";
            IQueryable<ZZZI19Hrmt01ViewModel> HRMT01List = db.Database.Connection.Query<ZZZI19Hrmt01ViewModel>(sSQL
            , new
            {
                SCHOOL_NO = SchoolNO,
            }).AsQueryable();
            int iPageCount = (model1.ShowPageCount != null) ? Convert.ToInt32(model1.ShowPageCount) : 100;
            model1.HRMT01List = HRMT01List.ToPagedList(model1.Page > 0 ? model1.Page - 1 : 0, iPageCount);
            ViewBag.ClassNoItem = ClassNoItem;

            //年級
            ViewBag.GradeItem = HRMT01.GetGradeItems(model.whereGrade);

            //狀態
            ViewBag.StatusItem = UserStaus.GetUserStausItemsALL("");
            List<SelectListItem> PageCountItem = new List<SelectListItem>();
            PageCountItem.Add(new SelectListItem() { Text = "100", Value = "100", Selected = iPageCount.ToString() == "100" });
            PageCountItem.Add(new SelectListItem() { Text = "200", Value = "200", Selected = iPageCount.ToString() == "200" });
            PageCountItem.Add(new SelectListItem() { Text = "500", Value = "500", Selected = iPageCount.ToString() == "500" });
            ViewBag.PageCount = PageCountItem;

            //轉學生酷幣轉匯

            var PermissionBtn = PermissionService.GetActionPermissionForBreNO("ZZZI20", SchoolNO, user.USER_NO);
            //判斷是否有[我要投稿]權限
            ViewBag.VisibleTurnIn = (PermissionBtn.Where(a => a.ActionName == "Index").Select(a => a.BoolUse).FirstOrDefault() == true ? "Y" : "N");

            ViewBag.ShowTurnInCount = 0;

            List<HRMV01> TurnInList = new List<HRMV01>();
            //先找出該校有多少轉入學生
            var Students = db.HRMV01.Where(a => a.SCHOOL_NO == SchoolNO && a.USER_STATUS == UserStaus.Disable && a.INIT_STATUS == 0);
            //篩選還沒處理的
            foreach (HRMV01 s in Students)
            {
                if (s.USER_STATUS != UserStaus.Disable) continue;
                if (s.INIT_STATUS != 0) continue;

                AWAT01 UserCash = db.AWAT01.Where(uu => uu.SCHOOL_NO == s.SCHOOL_NO && uu.USER_NO == s.USER_NO).FirstOrDefault();
                if (UserCash != null)
                {
                    if (UserCash.CASH_ALL > 0) continue;
                }

                TurnInList.Add(s);
            }
            ViewBag.ShowTurnInCount = TurnInList.Count;
            int SCHOOLCount = 0;
            SCHOOLCount = db.BDMT01.Where(x => x.CITY != "臺北市" && x.SCHOOL_NO == user.SCHOOL_NO).Count();
            ViewBag.IsADDOne = false;
            if (SCHOOLCount > 0)
            {
                ViewBag.IsADDOne = true;

            }
            int IDNOCount = 0;
            IDNOCount = db.Temp_STUDENT.Where(x => x.SCHOOL_NO == SchoolNO && x.STDNO == model.USER_NO && x.IDNO != model.IDNO).Count();
            if (IDNOCount > 0)
            {
                Temp_STUDENT temp_STUDENT = new Temp_STUDENT();
                //刪除資料
                temp_STUDENT = db.Temp_STUDENT.Where(x => x.SCHOOL_NO == SchoolNO && x.STDNO == model.USER_NO && x.IDNO != model.IDNO).FirstOrDefault();
                db.Temp_STUDENT.Remove(temp_STUDENT);
            }
            int IDNOCount1 = 0;
            IDNOCount1 = db.Temp_STUDENT.Where(x => x.SCHOOL_NO == SchoolNO && x.STDNO != model.USER_NO && x.IDNO == model.IDNO).Count();
            if (IDNOCount1 > 0)
            {
                Temp_STUDENT temp_STUDENT = new Temp_STUDENT();
                //刪除資料
                temp_STUDENT = db.Temp_STUDENT.Where(x => x.SCHOOL_NO == SchoolNO && x.STDNO != model.USER_NO && x.IDNO == model.IDNO).FirstOrDefault();
                db.Temp_STUDENT.Remove(temp_STUDENT);
            }

            int IDNOHrmt01Count = 0;
            IDNOHrmt01Count = db.HRMT01.Where(x => x.SCHOOL_NO == SchoolNO && x.USER_NO == model.USER_NO && x.IDNO != model.IDNO).Count();
            if (IDNOHrmt01Count > 0)
            {
                HRMT01 rMT01 = new HRMT01();
                //刪除資料
                rMT01 = db.HRMT01.Where(x => x.SCHOOL_NO == SchoolNO &&  x.USER_NO == model.USER_NO && x.IDNO != model.IDNO).FirstOrDefault();
                db.HRMT01.Remove(rMT01);
            }
            int IDNOHrmt01Count1 = 0;
            IDNOHrmt01Count1 = db.HRMT01.Where(x => x.SCHOOL_NO == SchoolNO && x.USER_NO != model.USER_NO && x.IDNO == model.IDNO).Count();
            if (IDNOHrmt01Count1 > 0)
            {
                HRMT01 rMT01 = new HRMT01();
                //刪除資料
                rMT01 = db.HRMT01.Where(x => x.SCHOOL_NO == SchoolNO && x.USER_NO != model.USER_NO && x.IDNO == model.IDNO).FirstOrDefault();
                db.HRMT01.Remove(rMT01);

            }
            int SameCount = 0;
            SameCount = db.Temp_STUDENT.Where(x => x.SCHOOL_NO == SchoolNO && x.STDNO == model.USER_NO && x.IDNO == model.IDNO).Count();
            if (SameCount > 0)
            {
                Temp_STUDENT temp_STUDENT = new Temp_STUDENT();
                //updet 資料

                temp_STUDENT = db.Temp_STUDENT.Where(x => x.SCHOOL_NO == SchoolNO && x.STDNO == model.USER_NO && x.IDNO == model.IDNO).FirstOrDefault();
                temp_STUDENT.NAME = model.NAME;
                temp_STUDENT.SEX = model.SEX;
                temp_STUDENT.SEYEAR = model.SYEAR;
                temp_STUDENT.CLASSNO = model.CLASS_NO;
                temp_STUDENT.NO = model.SEAT_NO;
                temp_STUDENT.BIRTHDAY = model.BIRTHDAY;
                temp_STUDENT.STATUS = "0";
            }

            int SamHrmt01eCount = 0;
            SameCount = db.HRMT01.Where(x => x.SCHOOL_NO == SchoolNO && x.USER_NO == model.USER_NO && x.IDNO == model.IDNO).Count();
            if (SamHrmt01eCount > 0)
            {
                //updet 資料
                HRMT01 rMT01 = new HRMT01();
                rMT01 = db.HRMT01.Where(x => x.SCHOOL_NO == SchoolNO && x.USER_NO == model.USER_NO && x.IDNO == model.IDNO && x.USER_STATUS == 1).FirstOrDefault();
                rMT01.NAME = model.NAME;
                rMT01.SEX = model.SEX;
                rMT01.SYEAR = byte.Parse(model.SYEAR);
                rMT01.GRADE = byte.Parse(model.GRADE);
                rMT01.CLASS_NO = model.CLASS_NO;
                rMT01.SEAT_NO = model.SEAT_NO;
                rMT01.BIRTHDAY = DateTime.Parse(model.BIRTHDAY);
                rMT01.USER_STATUS = 1;
            }
            db.SaveChanges();

            ECOOL_DEVEntities db1 = new ECOOL_DEVEntities();
            if (SameCount == 0 || SameCount == null)
            {
                Temp_STUDENT temp_STUDENT = new Temp_STUDENT();
                int IDM = 0;
                IDM = db.Temp_STUDENT.Max(x => x.ID);
                //新增資料
                temp_STUDENT.SCHOOL_NO = user.SCHOOL_NO;
                temp_STUDENT.STDNO = model.USER_NO;
                temp_STUDENT.ID = IDM + 1;
                temp_STUDENT.NAME = model.NAME;
                temp_STUDENT.SEX = model.SEX;
                temp_STUDENT.YEAR = model.CLASS_NO.Substring(0, 1);
                temp_STUDENT.SEYEAR = model.SYEAR;
                temp_STUDENT.CLASSNO = model.CLASS_NO.Substring(1, 2);
                temp_STUDENT.NO = model.SEAT_NO;

                temp_STUDENT.IDNO = model.IDNO;
                temp_STUDENT.BIRTHDAY = DateTime.Parse(model.BIRTHDAY).Year.ToString() + DateTime.Parse(model.BIRTHDAY).Month.ToString() + DateTime.Parse(model.BIRTHDAY).Day.ToString();
                temp_STUDENT.STATUS = "0";
                db1.Temp_STUDENT.Add(temp_STUDENT);
            }



            if (SamHrmt01eCount == 0 || SamHrmt01eCount == null)
            {
                HRMT01 rMT01 = new HRMT01();
                rMT01.SCHOOL_NO = user.SCHOOL_NO;
                rMT01.USER_NO = model.USER_NO;
                rMT01.USER_KEY = user.SCHOOL_NO + "_" + model.USER_NO;
                //新增資料
                rMT01.NAME = model.NAME;
                rMT01.SNAME = model.NAME.Substring(1, model.NAME.Length - 1);
                rMT01.SEX = model.SEX;
                rMT01.SYEAR = byte.Parse(model.SYEAR);
                rMT01.GRADE = byte.Parse(model.GRADE);
                rMT01.CLASS_NO = model.CLASS_NO;
                rMT01.SEAT_NO = model.SEAT_NO;
                rMT01.BIRTHDAY = DateTime.Parse(model.BIRTHDAY);
                rMT01.USER_STATUS = 1;
                rMT01.USER_TYPE = "S";
                rMT01.IDNO = model.IDNO;
                rMT01.INIT_STATUS = 0;
                db1.HRMT01.Add(rMT01);

            }
            db1.SaveChanges();
            ZZZI19IndexViewModel ZZZI19model = new ZZZI19IndexViewModel();
            string sSQL1 = $@"select * from ufnGetApLog(@SCHOOL_NO)";
            IQueryable<ZZZI19Hrmt01ViewModel> HRMT01Lists = db.Database.Connection.Query<ZZZI19Hrmt01ViewModel>(sSQL1
            , new
            {
                SCHOOL_NO = SchoolNO,
            }).AsQueryable();
            ZZZI19model.HRMT01List = HRMT01List.ToPagedList(ZZZI19model.Page > 0 ? ZZZI19model.Page - 1 : 0, iPageCount);
            ZZZI19model.DataList = null;

            ZZZI19model.DataList = new List<ZZZI19CheckBoxListViewModel>();

            ZZZI19CheckBoxListViewModel ReturnData = null;
            foreach (var item in ZZZI19model.HRMT01List)
            {
                ReturnData = new ZZZI19CheckBoxListViewModel();
                ReturnData.Chk = false;
                ReturnData.SCHOOL_NO = item.SCHOOL_NO;
                ReturnData.USER_NO = item.USER_NO;
                ZZZI19model.DataList.Add(ReturnData);
            }
            int SCHOOLCount1 = 0;
            SCHOOLCount = db.BDMT01.Where(x => x.CITY != "臺北市" && x.SCHOOL_NO == user.SCHOOL_NO).Count();
            ViewBag.IsADDOne = false;
            if (SCHOOLCount > 0)
            {
                ViewBag.IsADDOne = true;

            }
     
            return View("Query", ZZZI19model);

        }
        public ActionResult ADDUser()
        {
            ZZZI19Hrmt01ViewModel model = new ZZZI19Hrmt01ViewModel();
          
          
            return RedirectToAction("Edit", model);
        }
        /// <summary>
        /// 更新資料
        /// </summary>
        /// <param name="DataList"></param>
        /// <param name="StatusVal"></param>
        public void UpDataStatus(List<ZZZI19CheckBoxListViewModel> DataList, byte StatusVal)
        {
            try
            {
                DataList.RemoveAll(a => a.Chk == false);

                foreach (var item in DataList)
                {
                    if (item.Chk)
                    {
                        HRMT01 HT01 = db.HRMT01.Where(p => p.SCHOOL_NO == item.SCHOOL_NO && p.USER_NO == item.USER_NO).FirstOrDefault();
                        if (HT01 != null)
                        {
                            HT01.USER_STATUS = StatusVal;
                            db.Entry(HT01).State = System.Data.Entity.EntityState.Modified;
                            db.SaveChanges();
                        }
                    }
                }

                TempData["StatusMessage"] = "異動成功";
            }
            catch (Exception ex)
            {
                TempData["StatusMessage"] = "異動失敗，系統錯誤原因:" + ex.Message;
            }
        }
    }
}