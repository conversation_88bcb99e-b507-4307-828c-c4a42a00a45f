﻿@model AWAT14RankCount
@{
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

 
}


@Html.HiddenFor(m => m.WhereSCHOOL_NO)
@Html.HiddenFor(m => m.WhereRANKNO)







<img src="~/Content/img/web-bar2-revise-80.png" class="img-responsive App_hide  m-auto" alt="Responsive image" />
<div class="table-responsive">
    <div class="text-center" id="tbData">
        <table class="table-ecool table-92Per table-hover table-ecool-reader">
            <thead>
                <tr>

                    <th onclick="doSort('CLASS_NO')">
                        班級
                        <img id="CLASS_NO" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                    </th>
                    <th onclick="doSort('SEAT_NO')">
                        座號

                        <img id="SEAT_NO" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                    </th>
                    <th>姓名</th>
                    <th onclick="doSort('CASH_ALL')">
                        累積點數


                        <img id="CASH_ALL" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                    </th>

                </tr>
            </thead>
            <tbody>
                @foreach (var item in Model.AWAT14PeopleEditList)
                {

                <tr>
                    <td style="text-align: center">@Html.DisplayFor(modelItem => item.CLASS_NO)</td>
                    <td style="text-align: center">@Html.DisplayFor(modelItem => item.SEAT_NO)</td>
                    <td style="text-align: center">@Html.DisplayFor(modelItem => item.NAME)</td>

                    <td style="text-align: center">@Html.DisplayFor(modelItem => item.CASH_ALL)</td>

                </tr>
                }
            </tbody>
        </table>
    </div>
</div>
<script>
    function funAjax() {
        
       
        $.ajax({
            type: "GET",
                url: '@(Url.Action("_PageRunDetailLog", (string)ViewBag.BRE_NO))',
            data: {
             
                OrdercColumn: $('#OrdercColumn').val(),
                SyntaxName: $('#SyntaxName').val(),
                WhereRANKNO: '@Model.WhereRANKNO',
                WhereSCHOOL_NO:$('#WhereSCHOOL_NO').val(),
            },
                async: false,
                cache: false,
                dataType: 'html',
            success: function (data) {
              
                $("#cboxLoadedContent").html('');
                console.log(data);
                $("#cboxLoadedContent").html(data);
                }
            });
    }
    function doSort(SortCol) {
        var sort = "";
        sort = SortCol;
        OrderByName = $('#OrdercColumn').val();
        SyntaxName = $('#SyntaxName').val();
        $('#OrdercColumn').val(SortCol);
        console.log("SortCol");
        if (OrderByName == SortCol) {

            if (SyntaxName == "Desc") {

                $('#OrdercColumn').val(sort);
                $('#SyntaxName').val("ASC");
            }
            else {

                $('#OrdercColumn').val(sort);
                $('#SyntaxName').val("Desc");
            }
        }
        else {

            $('#OrdercColumn').val(sort);
            $('#SyntaxName').val("Desc");
        }
        funAjax()
    }
</script>
@*@if (Model.RunCity.Count() == 0)
{
    <div class="text-center">
        <h3>無任何跑步記錄</h3>
    </div>

}
else
{
    <div style="height:25px"></div>
    <div class="col-sm-12">
        @if (Model.MyRunColumnChart != null)
        {
            @Model.MyRunColumnChart
        }
    </div>
}*@