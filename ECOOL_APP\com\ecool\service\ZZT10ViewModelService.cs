﻿using ECOOL_APP.com.ecool.Models.entity;
using ECOOL_APP.com.ecool.Models.DTO;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Data.SqlClient;

namespace com.ecool.service
{
    public class ZZT10ViewModelService
    {
        public string ErrorMsg;

        #region 取得此角色權限清單

        /// <summary>
        /// 取得此角色權限清單
        /// </summary>
        /// <param name="ROLE_ID">角色代碼</param>
        /// <returns>List<ZZT10ViewModel></returns>
        public List<ZZT10ViewModel> USP_ZZT10ViewModel_QUERY(string ROLE_ID, ECOOL_APP.UserProfile User)
        {
            List<ZZT10ViewModel> list_data = new List<ZZT10ViewModel>();

            ZZT10ViewModel ReturnData = null;

            StringBuilder sb = new StringBuilder();
            DataTable dt;
            try
            {
                sb.Append("  SELECT R.ROLE_ID,R<PERSON>_NAME,B.BRE_NO,B.BRE_NAME,B.BRE_TYPE,B.LEVEL_NO,isnull(A.ACTION_ID,'') ACTION_ID,isnull(A.ACTION_NAME,'') ACTION_NAME,isnull(A.ACTION_TYPE,'') ACTION_TYPE");
                sb.Append(" ,CASE WHEN ISNULL(C.ACTION_ID,'')<>'' or isnull(A.ACTION_TYPE,'')='ALL' or R.ROLE_ID='0' Then 'true' Else 'false' End as Checked ");
                sb.AppendFormat(" , Case When dbo.fn_Permission_Use(B.BRE_NO,A.ACTION_ID,'{0}','{1}')='Y' Then   'true' Else 'false' End as MY_Permission_Checked", User.SCHOOL_NO, User.USER_NO);
                sb.Append(" FROM HRMT24 R (NOLOCK) ");
                sb.Append(" CROSS JOIN ZZT01 B  (NOLOCK)  ");
                sb.Append(" FULL JOIN ZZT34 A  (NOLOCK)  ON A.BRE_NO=B.BRE_NO ");
                sb.Append(" LEFT OUTER JOIN ZZT10 C (NOLOCK) ON R.ROLE_ID=C.ROLE_ID AND A.BRE_NO=C.BRE_NO AND A.ACTION_ID=C.ACTION_ID ");
                sb.Append(" Where  1 = 1  and  isnull(B.ENABLE,1)=1 ");
                sb.AppendFormat(" AND R.ROLE_LEVEL >={0} ", User.ROLE_LEVEL);

                if (ROLE_ID != string.Empty)
                {
                    sb.AppendFormat(" AND R.ROLE_ID ='{0}' ", ROLE_ID);
                }

                sb.Append(" ORDER BY R.ROLE_ID,B.LEVEL_NO,B.BRE_NO,A.ACTION_ID ");

                dt = new sqlConnection.sqlConnection().executeQueryByDataTableList(sb.ToString());

                string BRE_NO = string.Empty;

                foreach (DataRow dr in dt.Rows)
                {
                    if (BRE_NO != dr["BRE_NO"].ToString())
                    {
                        ReturnData = new ZZT10ViewModel();
                        ReturnData.Details_List = new List<ZZT10_D_ViewModel>();
                        ReturnData.ROLE_ID = dr["ROLE_ID"].ToString();
                        ReturnData.BRE_NO = dr["BRE_NO"].ToString();
                        ReturnData.BRE_NAME = dr["BRE_NAME"].ToString();
                        ReturnData.BRE_TYPE = dr["BRE_TYPE"].ToString();
                        ReturnData.LEVEL_NO = dr["LEVEL_NO"].ToString();
                        list_data.Add(ReturnData);
                    }

                    ZZT10_D_ViewModel Details = new ZZT10_D_ViewModel();

                    Details.ROLE_ID = dr["ROLE_ID"].ToString();
                    Details.BRE_NO = dr["BRE_NO"].ToString();
                    Details.ACTION_ID = dr["ACTION_ID"].ToString();
                    Details.ACTION_NAME = dr["ACTION_NAME"].ToString();
                    Details.ACTION_TYPE = dr["ACTION_TYPE"].ToString();
                    Details.Checked = Convert.ToBoolean(dr["Checked"]);
                    Details.MY_Permission_Checked = Convert.ToBoolean(dr["MY_Permission_Checked"]);

                    ReturnData.Details_List.Add(Details);

                    BRE_NO = dr["BRE_NO"].ToString();
                }

                dt.Clear();
                dt.Dispose();
                sb.Clear();
            }
            catch (Exception exception)
            {
                throw exception;
            }
            return list_data;
        }

        #endregion 取得此角色權限清單

        #region 新增角色權限

        /// <summary>
        /// 新增角色權限
        /// </summary>
        /// <param name="Date">uZZT10</param>
        public void AddDate(uZZT10 Date)
        {
            using (SqlConnection conn = new sqlConnection.sqlConnection().getConnection4Query())
            {
                IDbCommand cmd = new SqlCommand(@"INSERT INTO ZZT10 ( ROLE_ID,BRE_NO,ACTION_ID,CRE_PERSON,CRE_DATE
                    ) VALUES (@ROLE_ID,@BRE_NO,@ACTION_ID,@CRE_PERSON,@CRE_DATE)");

                cmd.Connection = conn;
                SqlTransaction transaction = conn.BeginTransaction();
                cmd.Transaction = transaction;

                cmd.Parameters.Add(
                (Date.ROLE_ID == null)
                ? new SqlParameter("@ROLE_ID", DBNull.Value)
                : new SqlParameter("@ROLE_ID", Date.ROLE_ID));

                cmd.Parameters.Add(
                (Date.BRE_NO == null)
                ? new SqlParameter("@BRE_NO", DBNull.Value)
                : new SqlParameter("@BRE_NO", Date.BRE_NO));

                cmd.Parameters.Add(
                (Date.ACTION_ID == null)
                ? new SqlParameter("@ACTION_ID", DBNull.Value)
                : new SqlParameter("@ACTION_ID", Date.ACTION_ID));

                cmd.Parameters.Add(
                (Date.CRE_PERSON == null)
                ? new SqlParameter("@CRE_PERSON", DBNull.Value)
                : new SqlParameter("@CRE_PERSON", Date.CRE_PERSON));

                cmd.Parameters.Add(
                (Date.CRE_DATE == null)
                ? new SqlParameter("@CRE_DATE", DBNull.Value)
                : new SqlParameter("@CRE_DATE", Date.CRE_DATE));

                try
                {
                    cmd.ExecuteNonQuery();
                    transaction.Commit();
                }
                catch (Exception ex)
                {
                    transaction.Rollback();
                    ErrorMsg = "異動資料失敗;" + ex.Message;
                }
                finally
                {
                    if (cmd != null)
                    {
                        cmd.Dispose();
                    }

                    if (conn != null)
                    {
                        conn.Close();
                        conn.Dispose();
                    }
                }
            }
        }

        #endregion 新增角色權限

        #region 刪除角色權限

        public void DelDate(uZZT10 Date)
        {
            using (SqlConnection conn = new sqlConnection.sqlConnection().getConnection4Query())
            {
                IDbCommand cmd = new SqlCommand(@"DELETE ZZT10 Where ROLE_ID=@ROLE_ID and BRE_NO=@BRE_NO and ACTION_ID=@ACTION_ID");

                cmd.Connection = conn;
                SqlTransaction transaction = conn.BeginTransaction();
                cmd.Transaction = transaction;

                cmd.Parameters.Add(
                (Date.ROLE_ID == null)
                ? new SqlParameter("@ROLE_ID", DBNull.Value)
                : new SqlParameter("@ROLE_ID", Date.ROLE_ID));

                cmd.Parameters.Add(
                (Date.BRE_NO == null)
                ? new SqlParameter("@BRE_NO", DBNull.Value)
                : new SqlParameter("@BRE_NO", Date.BRE_NO));

                cmd.Parameters.Add(
                (Date.ACTION_ID == null)
                ? new SqlParameter("@ACTION_ID", DBNull.Value)
                : new SqlParameter("@ACTION_ID", Date.ACTION_ID));

                try
                {
                    cmd.ExecuteNonQuery();
                    transaction.Commit();
                }
                catch (Exception ex)
                {
                    transaction.Rollback();
                    ErrorMsg = "異動資料失敗;" + ex.Message;
                }
                finally
                {
                    if (cmd != null)
                    {
                        cmd.Dispose();
                    }

                    if (conn != null)
                    {
                        conn.Close();
                        conn.Dispose();
                    }
                }
            }
        }

        #endregion 刪除角色權限
    }
}