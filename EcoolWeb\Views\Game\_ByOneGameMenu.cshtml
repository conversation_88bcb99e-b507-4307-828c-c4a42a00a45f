﻿@using ECOOL_APP;
@{

    var Permission = ViewBag.Permission as List<ControllerPermissionfile>;

    byte? GAME_TYPE = ViewBag.GAME_TYPE ?? (byte)ADDT26.GameType.一般;

}

    <div class="form-group">

        <button type="button" class="btn btn-sm btn-sys  @(ViewBag.NowAction=="GameIndex" ? "active":"")" onclick="OnGameIndex()"> <i class="fa fa-edit"></i> 活動一覽表</button>

        @if (Permission.Where(a => a.ActionName == "Edit").Any())
        {
            if (GAME_TYPE == (byte)ADDT26.GameType.一般)
            {
                <button type="button" class="btn btn-sm btn-sys  @(ViewBag.NowAction=="Edit" ? "active":"")" onclick="OnOneEdit('Edit')"> <i class="fa fa-edit"></i> 關卡管理</button>
            }
            else if (GAME_TYPE == (byte)ADDT26.GameType.有獎徵答)
            {
                <button type="button" class="btn btn-sm btn-sys  @(ViewBag.NowAction=="Edit" ? "active":"")" onclick="OnOneEdit('EditQA')"> <i class="fa fa-edit"></i> 關卡管理</button>
                <button type="button" class="btn btn-sm btn-sys  @(ViewBag.NowAction=="Reward" ? "active":"")" onclick="OnOneReward()"> <i class="fa fa-edit"></i> 鼓勵與獎勵</button>
            }
        }

        @if (Permission.Where(a => a.ActionName == "BatchWork").Any())
        {
            <button type="button" class="btn btn-sm btn-sys  @(ViewBag.NowAction=="BatchWork" ? "active":"")" onclick="OnOneBatcWork()"> <i class="fa fa-rocket"></i> 人員管理</button>
        }

        <button type="button" class="btn btn-sm btn-sys  @(ViewBag.NowAction=="PassMod" ? "active":"")" onclick="OnOnePassMode()"> <i class="fa fa-truck"></i> 過關模式</button>

        @if (GAME_TYPE == (byte)ADDT26.GameType.一般)
        {

            if (Permission.Where(a => a.ActionName == "Statistics").Any())
            {
                <button type="button" class="btn btn-sm btn-sys  @(ViewBag.NowAction=="Statistics" ? "active":"")" onclick="OnOneStatistics()"> <i class="fa fa-bar-chart-o"></i> 統計</button>
            }

            if (Permission.Where(a => a.ActionName == "Lottery").Any())
            {
                <button type="button" class="btn btn-sm btn-sys  @(ViewBag.NowAction=="Lottery" ? "active":"")" onclick="OnOneLottery()"> <i class="fa fa-money"></i> 抽獎</button>
            }

            if (Permission.Where(a => a.ActionName == "BuskerManager").Any())
            {
                <button type="button" class="btn btn-sm btn-sys  @(ViewBag.NowAction=="BuskerManager" ? "active":"")" onclick="OnOneBuskerManager()"> <i class="fa fa-group"></i> 街頭藝人</button>
            }
        }
        else if (GAME_TYPE == (byte)ADDT26.GameType.有獎徵答)
        {
            if (Permission.Where(a => a.ActionName == "StatisticsAns").Any())
            {
                <button type="button" class="btn btn-sm btn-sys  @(ViewBag.NowAction=="StatisticsAns" ? "active":"")" onclick="OnStatisticsAns()"> <i class="fa fa-bar-chart-o"></i> 統計分析</button>
            }

            if (Permission.Where(a => a.ActionName == "ScoreList").Any())
            {
                <button type="button" class="btn btn-sm btn-sys  @(ViewBag.NowAction=="ScoreLists" ? "active":"")" onclick="OnScoreList()"> <i class="fa fa-bar-chart-o"></i> 成績一覽表</button>
            }

            if (Permission.Where(a => a.ActionName == "Lottery").Any())
            {
                <button type="button" class="btn btn-sm btn-sys  @(ViewBag.NowAction=="Lottery" ? "active":"")" onclick="OnOneLottery()"> <i class="fa fa-money"></i> 抽獎</button>
            }

        }

        @if (Permission.Where(a => a.ActionName == "CashIntoView").Any())
        {
            <button type="button" class="btn btn-sm btn-sys  @(ViewBag.NowAction=="CashIntoView" ? "active":"")" onclick="OnCashIntoView()"> <i class="fa fa-group"></i> 點數處理</button>
        }

        <button type="button" class="btn btn-sm btn-sys" onclick="onWinOpenYoutubeUrlLink()"> <i class="fa fa-copy"></i> 複製過關模式連結</button>
        <button type="button" class="btn btn-sm btn-sys @(ViewBag.NowAction=="QueryTeamSetView" ? "active":"")" onclick="onQueryTeamSetView()"> <i class="fa fa-copy"></i> 小組闖關</button>
    </div>

<div class="modal fade bs-example-modal-lg" id="mySmallModal" tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel" style="z-index:9999">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">

            <div class="input-group">
                <span class="input-group-btn">
                    <button type="button" id="id_copy"
                            data-clipboard-target="#id_text"
                            data-clipboard-action="copy" onclick="OnCopy()">
                        點擊複製
                    </button>
                </span>
                <div id="id_text" style="word-break: break-all;"></div>
                <div id="success" style="display:none">已複製</div>
                <input id="copyStr" type="hidden" value="">
            </div><!-- /input-group -->
        </div>
    </div>
</div>
<!-- clipboard.js v1.7.1 複製內容到剪貼簿的小工具 -->
<script src="~/Scripts/clipboard.min.js"></script>
<script type="text/javascript">
    function OnOneEdit(val) {

        if (val.toUpperCase()=="EDIT") {
             $('form').attr("action", "@Url.Action("Edit", "Game")");
        }
        else {
            $('form').attr("action", "@Url.Action("EditQA", "Game")");
        }

        $('form').submit();
    }

    function onWinOpenYoutubeUrlLink() {
        var LevelUrl = '@ECOOL_APP.UrlCustomHelper.GetOwnWebUri()/@Url.Action("FastLevel", (string)ViewBag.BRE_NO)?ActionName=PassMode&GAME_NO=' + $('#Search_WhereGAME_NO').val() + '&Title=闖關地圖'

            $('#id_text').text(LevelUrl)
            $('#copyStr').val(LevelUrl)

            $('#success').hide()
            $('#id_text').show()

            if ($('#mySmallModal').is(':visible') == false) {
                $('#mySmallModal').modal('show');
            }
    }

      //複製快速連結
        function OnCopy() {
            var clipboard = new Clipboard("#id_copy");
            clipboard.on("success", function (element) {//複製成功的回調
                console.info("複製成功，複製內容：    " + element.text);
                $('#success').show()
                $('#id_text').hide()

                setTimeout('HidemySmallModal()', 2000);

            });
            clipboard.on("error", function (element) {//複製失敗的回調
                console.info(element);

                var $input = $('#copyStr');
                $input.val();
                if (navigator.userAgent.match(/ipad|ipod|iphone/i)) {
                    clipboard.on('success', function (e) {
                        e.clearSelection();
                        $.sDialog({
                            skin: "red",
                            content: 'copy success!',
                            okBtn: false,
                            cancelBtn: false,
                            lock: true
                        });
                        console.log('copy success!');

                        $('#success').show()
                        $('#id_text').hide()

                        setTimeout('HidemySmallModal()', 2000);

                    });
                } else {
                    $input.select();
                }
                //document.execCommand('copy');
                $input.blur();
            });
        }

    function OnGameIndex() {
        location.href = "@Url.Action("GameIndex", (string)ViewBag.BRE_NO)";
    }

    function onQueryTeamSetView() {
        $('form').attr("action", "@Url.Action("QueryTeamSetView", "Game")?GAME_NO=" + $('#Search_WhereGAME_NO').val()+"");
        $('form').submit();
    }

    function OnCashIntoView() {
        $('form').attr("action", "@Url.Action("CashIntoView", "Game")");
        $('form').submit();
    }

    function OnOneReward() {
        $('form').attr("action", "@Url.Action("Reward", "Game")");
        $('form').submit();
    }

    function OnOneStatistics() {
         $('form').attr("action", "@Url.Action("Statistics", "Game")");
        $('form').submit();
    }

    function OnStatisticsAns() {
         $('form').attr("action", "@Url.Action("StatisticsAns", "Game")");
         $('form').submit();
    }

    function OnScoreList() {
         $('form').attr("action", "@Url.Action("ScoreLists", "Game")");
         $('form').submit();
    }

    function OnOneLottery() {
         $('form').attr("action", "@Url.Action("Lottery", "Game")");
        $('form').submit();
    }

    function OnOnePassMode() {
        $('form').attr("action", "@Url.Action("PassMode", "Game")");
        $('form').submit();
    }

    function OnOnePassMode() {
        location.href = "@Url.Action("FastLevel", (string)ViewBag.BRE_NO)?ActionName=PassMode&Title=闖關地圖&GAME_NO=" + $('#Search_WhereGAME_NO').val();
    }

    function OnOneBuskerManager() {
        $('form').attr("action", "@Url.Action("BuskerManager", "Game")");
        $('form').submit();
    }

    function OnOneBatcWork() {
         $('form').attr("action", "@Url.Action("BatchWork", "Game")");
         $('form').submit();
    }
</script>