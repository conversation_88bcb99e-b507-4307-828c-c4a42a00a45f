﻿<?xml version="1.0" encoding="utf-8"?>
<configurationSnapshot xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="urn:schemas-microsoft-com:xml-wcfconfigurationsnapshot">
  <behaviors />
  <bindings>
    <binding digest="System.ServiceModel.Configuration.BasicHttpBindingElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089:&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data name=&quot;BasicHttpBinding_IEzCodeStudMap&quot; /&gt;" bindingType="basicHttpBinding" name="BasicHttpBinding_IEzCodeStudMap" />
    <binding digest="System.ServiceModel.Configuration.BasicHttpBindingElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089:&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data name=&quot;BasicHttpsBinding_IEzCodeStudMap&quot;&gt;&lt;security mode=&quot;Transport&quot; /&gt;&lt;/Data&gt;" bindingType="basicHttpBinding" name="BasicHttpsBinding_IEzCodeStudMap" />
  </bindings>
  <endpoints>

    <endpoint normalizedDigest="&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data address=&quot;https://ecard38:9443/EzCodeStudMap/EzCodeStudMap.svc&quot; binding=&quot;basicHttpBinding&quot; bindingConfiguration=&quot;BasicHttpsBinding_IEzCodeStudMap&quot; contract=&quot;EzCodeStudMap.IEzCodeStudMap&quot; name=&quot;BasicHttpsBinding_IEzCodeStudMap&quot; /&gt;" digest="&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data address=&quot;https://ecard38:9443/EzCodeStudMap/EzCodeStudMap.svc&quot; binding=&quot;basicHttpBinding&quot; bindingConfiguration=&quot;BasicHttpsBinding_IEzCodeStudMap&quot; contract=&quot;EzCodeStudMap.IEzCodeStudMap&quot; name=&quot;BasicHttpsBinding_IEzCodeStudMap&quot; /&gt;" contractName="EzCodeStudMap.IEzCodeStudMap" name="BasicHttpsBinding_IEzCodeStudMap" />
  </endpoints>
</configurationSnapshot>