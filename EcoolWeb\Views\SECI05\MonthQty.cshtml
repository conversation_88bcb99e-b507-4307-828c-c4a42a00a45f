﻿@model SECI05MonthQtyIndexViewModel
@{
    ViewBag.Title = ViewBag.Panel_Title;

    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
    else if (ViewBag.from != null)
    {
        Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
    }
    else
    {
        Layout = "~/Views/Shared/_LayoutSEO.cshtml";
    }
}
<script src="~/Scripts/Pring.js"></script>
<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>
<script src="~/Scripts/Highcharts-4.0.1/js/highcharts.js"></script>
<script src="~/Scripts/Highcharts-4.0.1/js/modules/exporting.js"></script>
<script src="~/Scripts/Blob.js"></script>
<script src="~/Scripts/FileSaver.js"></script>
<script src="~/Scripts/tableexport.js"></script>
<script src="~/Scripts/printThis/printThis.js"></script>

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")
@if (ViewBag.from != null) { }

else
{
    @Html.Partial("_SECI05Menu", 7)
}

@using (Html.BeginForm("MonthQty", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1" }))
{
    @Html.HiddenFor(m => m.fromStr)
    <div class="form-inline" role="form" style="padding-left:40px" >

        <div class="row" >
            @Html.LabelFor(m => m.Where_SYEAR, new { @class = "col-md-12" })
            <div class="col-md-3">
                @Html.DropDownListFor(m => m.Where_SYEAR, (IEnumerable<SelectListItem>)ViewBag.SYearItems, new { @class = "form-control input-sm", @onchange = "this.form.submit()" })
            </div>
        </div>
        <div class="row" >
            @Html.LabelFor(m => m.Where_CLASS_NO, new { @class = "col-md-12" })
            <div class="col-md-3" >
                @Html.DropDownListFor(m => m.Where_CLASS_NO, (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control input-sm", @onchange = "this.form.submit()" })
            </div>
        </div>
        <input  type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="form1.submit();" />
        <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />
    </div>

    <div class="form-inline">
        <div class="col-xs-12 text-right">
            <button type="button" class="btn btn-sm btn-sys" onclick="PrintBooK()">我要列印</button>
            <button class="btn btn-sm btn-sys" onclick='fn_save()'>另存新檔(Excel)</button>
        </div>
    </div>

    <div id="tbData" style="padding-left:40px;padding-right:40px" >
        <div class="row">
            <div class="col-sm-12 col-xs-12">
                <table class="table-ecool table-ecool-Bule-SEC">
                    <thead>
                        <tr>
                            <th>
                                年月
                            </th>
                            <th>
                                @(Model.Where_CLASS_NO)班借書量(本)
                            </th>
                            <th>
                                全校借書量(本)
                            </th>
                            <th>
                                平均借書量(本)<br>
                                全校借書量(本)/全校班級數
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        @if (Model.MonthQtyList?.Count > 0)
                        {
                            foreach (var item in Model.MonthQtyList)
                            {
                                <tr class="text-center">
                                    <td>@item.RET_YYMM</td>
                                    <td>@item.CLASS_SUM</td>
                                    <td>@item.SCHOOL_SUM</td>
                                    <td>@item.BORROW_BOOK_AVG</td>
                                </tr>
                            }

                        }
                        else
                        {
                            <tr class="text-center">
                                <td colspan="4">此年度無借書的數量</td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
        <div style="height:25px"></div>
        <div class="row">
            <div class="col-sm-12">
                @if (Model.MonthQtycharts != null)
                {
                    @Model.MonthQtycharts
                }
            </div>
        </div>
    </div>
}

<script language="javascript">

    $(document).ready(function () {
        $(".colorbox").colorbox({ iframe: true, width: "80%", height: "80%", opacity: 0.82 });
           $('#fromStr').val("@ViewBag.from");
    });

    function PrintBooK() {
        $('#tbData').printThis();
    }

    function fn_save() {
        var blob = new Blob([document.getElementById('tbData').innerHTML], {
            type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8"
        });
        var strFile = "Report.xls";
        saveAs(blob, strFile);
        return false;
    }

    function todoClear() {
        ////重設
        $("#form1").find(":input,:selected").each(function (i) {

            var type = $(this).attr('type')
            var InPreadonly = $(this).attr('readonly')
            var tag = this.tagName.toLowerCase(); // normalize case

            if (InPreadonly == false || InPreadonly == undefined) {

                if (type == 'radio' || type == 'checkbox') {
                    if ($(this).attr("title") == 'Default') {
                        this.checked = true;
                    }
                    else {
                        this.checked = false;
                    }
                }
                else if (tag == 'select') { //下拉式選單
                    this.selectedIndex = 0;
                }
                else if (type == 'text' || type == 'hidden' || type == 'password' || type == 'textarea') {
                    this.value = '';
                }
            }
        });

        form1.submit();
    }
</script>