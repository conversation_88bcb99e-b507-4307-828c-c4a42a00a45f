﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.com.ecool.Models.DTO
{
    public class ADDI09SearchViewModel
    {
        /// <summary>
        /// 排序欄位
        /// </summary>
        public string OrderByName { get; set; }

        /// <summary>
        /// Desc or ASC
        /// </summary>
        public string SyntaxName { get; set; }

        /// <summary>
        /// 頁次
        /// </summary>
        public int Page { get; set; }

        [DisplayName("搜尋條件")]
        public string SearchContents { get; set; }

        public string BATCH_CASH_ID { get; set; }

        public string SYS_TABLE_TYPE { get; set; }

        public string ADDT14_STYLE { get; set; }
        public string Search_SUBJECT { get; set; }
        public string SOURCE_NO { get; set; }
        public string ListSchool { get; set; }
        public string ListUSERNO { get; set; }
        public string fromSOurce { get; set; }
        public ADDI09SearchViewModel()
        {
            Page = 1;
            OrderByName = "";
            SyntaxName = "Desc";
        }
    }
}