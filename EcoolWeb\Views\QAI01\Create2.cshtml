﻿@model EcoolWeb.Models.QAI01CreateViewModel
@{
    ViewBag.Title = "新增觀看影片作業";
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
}

<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>酷課雲影片作業</title>
</head>
<body>
    <script src="~/Content/ckeditor/ckeditor.js"></script>
    <link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
    <script src="~/Content/colorbox/jquery.colorbox.js"></script>

    @Html.Partial("_Title_Secondary")
    @Html.Partial("_Notice")

    @using (Html.BeginForm("Create3", "QAI01", FormMethod.Post, new {id="QAI01", name = "form1", enctype = "multipart/form-data" }))
    {
        @Html.AntiForgeryToken()

        @Html.HiddenFor(m => m.QUESTIONS_ID)
        @Html.HiddenFor(m => m.SelectMedias_id)
        @Html.HiddenFor(m => m.SelectMedias_name)
        @Html.HiddenFor(m => m.SelectMedias_contentLink)
        @Html.HiddenFor(m => m.SelectMedias_thumbnail)

        <div class="Div-EZ-ZZZI04">
            <div class="form-horizontal">

                <div class="form-group">
                    @Html.Label("影片名稱", htmlAttributes: new { @class = "control-label label_dt col-md-3col-sm-3 col-lg-2" })
                    <div class="col-md-12 col-sm-12 col-lg-10">
                        @Html.EditorFor(model => model.SelectMedias_name, new { htmlAttributes = new { @class = "form-control" } })
                        @Html.ValidationMessageFor(model => model.SelectMedias_name, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group">
                    @Html.Label("作業說明", htmlAttributes: new { @class = "control-label label_dt col-md-3col-sm-3 col-lg-2" })
                    <div class="col-md-12 col-sm-12 col-lg-10">
                        @Html.EditorFor(model => model.MEMO, new { htmlAttributes = new { @class = "form-control" } })
                        @Html.ValidationMessageFor(model => model.MEMO, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group">
                    @Html.Label("完成日期", htmlAttributes: new { @class = "control-label col-md-2" })
                    <div class="col-md-10">
                        @Html.EditorFor(model => model.END_DATE, new { htmlAttributes = new { @class = "form-control", @readonly = "readonly", @type = "text" } })
                        @Html.ValidationMessageFor(model => model.END_DATE, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div>
                    <button type="button" class="btn btn-default" title="選取觀看人員" id="BTN_PUSH" href="@Url.Action("Index", "APPT03", new { BTN_ID = "#BTN_PUSH",REF_TABLE = "QAT01" ,REF_KEY= Model.QUESTIONS_ID})">選取觀看人員</button>
                </div>
               <div>
                   <a class="btn-default btn btn-xs btn-prod" href='@Model.SelectMedias_contentLink' target="_blank">
                       <img src='@Model.SelectMedias_thumbnail' href="@Model.SelectMedias_thumbnail" style="max-height:200px;">
                   </a>
               </div>
            </div>

            <div class="form-group">
                <div class="col-md-offset-3 col-sm-offset-2 col-md-3 col-sm-3">
                    <button value="Create" class="btn btn-default " >
                        確定送出
                    </button>
                </div>
                <div class="col-md-offset-1 col-sm-offset-2 col-md-3 col-sm-3">
                    <a href='@Url.Action("Index", "QAI01")' role="button" class="btn btn-default" >
                        放棄編輯
                    </a>
                </div>
            </div>
         </div>
    }



    <script>
        $(document).ready(function () {
            $("#BTN_PUSH").colorbox({
                iframe: true, width: "80%", height: "80%", opacity: 0.82
            });
        });



        $(document).ready(function () {


            $("#END_DATE").datepicker({
                dateFormat: "yy/mm/dd",
                changeMonth: true,
                changeYear: true,
                showOn: "button",
                buttonImage: "../Content/img/icon/calendar.gif",
                buttonImageOnly: true,
            });


        });
    </script>
</body>
</html>
