/*
 *  /MathJax/jax/output/HTML-CSS/fonts/TeX/Main/Italic/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["MathJax_Main-italic"]={directory:"Main/Italic",family:"MathJax_Main",style:"italic",testString:"MathJax Main ^ \u210F \u2223",Ranges:[[160,255,"Latin1Supplement"],[768,879,"CombDiacritMarks"],[8192,8303,"GeneralPunctuation"],[8448,8527,"LetterlikeSymbols"]],32:[0,0,250,0,0],33:[716,0,307,107,380],34:[694,-379,514,176,538],35:[694,194,818,115,828],37:[750,56,818,145,847],38:[716,22,767,127,802],39:[694,-379,307,213,377],40:[750,250,409,144,517],41:[750,250,409,17,390],42:[750,-320,511,195,584],43:[557,57,767,139,753],44:[121,194,307,69,232],45:[251,-180,358,84,341],46:[121,0,307,107,231],47:[750,250,511,19,617],48:[665,21,511,110,562],49:[666,0,511,110,468],50:[666,22,511,76,551],51:[666,22,511,96,562],52:[666,194,511,46,478],53:[666,22,511,106,567],54:[665,22,511,120,565],55:[666,22,511,136,634],56:[666,21,511,99,553],57:[666,22,511,107,553],58:[431,0,307,107,308],59:[431,194,307,70,308],61:[367,-133,767,116,776],63:[716,0,511,195,551],64:[705,11,767,152,789],65:[716,0,743,58,696],66:[683,0,704,57,732],67:[705,21,716,150,812],68:[683,0,755,56,775],69:[680,0,678,54,743],70:[680,-1,653,54,731],71:[705,22,774,150,812],72:[683,0,743,54,860],73:[683,0,386,49,508],74:[683,21,525,78,622],75:[683,0,769,54,859],76:[683,0,627,54,628],77:[683,0,897,58,1010],78:[683,0,743,54,860],79:[704,22,767,149,788],80:[683,0,678,55,729],81:[704,194,767,149,788],82:[683,22,729,55,723],83:[705,22,562,74,633],84:[677,0,716,171,806],85:[683,22,743,194,860],86:[683,22,743,205,868],87:[683,22,999,205,1124],88:[683,0,743,50,825],89:[683,0,743,198,875],90:[683,0,613,80,704],91:[750,250,307,73,446],93:[750,250,307,-14,359],94:[694,-527,511,260,528],95:[-25,62,511,91,554],97:[442,11,511,101,543],98:[694,11,460,108,467],99:[441,10,460,103,469],100:[694,11,511,101,567],101:[442,10,460,107,470],102:[705,204,307,-23,450],103:[442,205,460,46,494],104:[694,11,511,69,544],105:[656,10,307,75,340],106:[656,204,307,-32,364],107:[694,11,460,69,498],108:[694,11,256,87,312],109:[442,11,818,75,851],110:[442,11,562,75,595],111:[442,11,511,103,517],112:[442,194,511,6,518],113:[442,194,460,101,504],114:[442,11,422,75,484],115:[442,11,409,76,418],116:[626,11,332,87,373],117:[441,11,537,75,570],118:[443,10,460,75,492],119:[443,11,664,75,696],120:[442,11,464,58,513],121:[441,205,486,75,522],122:[442,11,409,54,466],126:[318,-208,511,246,571],163:[714,11,769,88,699],305:[441,10,307,75,340],567:[442,204,332,-32,327],915:[680,0,627,54,705],916:[716,0,818,70,751],920:[704,22,767,149,788],923:[716,0,692,58,646],926:[677,0,664,74,754],928:[680,0,743,54,859],931:[683,0,716,80,782],933:[705,0,767,213,832],934:[683,0,716,159,728],936:[683,0,767,207,824],937:[705,0,716,100,759]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"MathJax_Main-italic"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Main/Italic/Main.js"]);
