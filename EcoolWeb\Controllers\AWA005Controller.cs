﻿using ECOOL_APP;
using ECOOL_APP.EF;
using EcoolWeb.Models;
using EcoolWeb.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using MvcPaging;
using EcoolWeb.CustomAttribute;

namespace EcoolWeb.Controllers
{
    [SessionExpire]
    public class AWA005Controller : Controller
    {
        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();
        // GET: AWA005
        public ActionResult Query(AWA005QueryViewModel model)
        {
            if (model == null) model = new AWA005QueryViewModel();

            UserProfile user = UserProfileHelper.Get();
            string SchoolNO = UserProfileHelper.GetSchoolNo();

            var RankCash = from w1 in db.AWAT08
                           join h1 in db.HRMT01
                           on new { w1.SCHOOL_NO, w1.USER_NO } equals new { h1.SCHOOL_NO, h1.USER_NO }
                           into h1Table
                           from hh1 in h1Table.DefaultIfEmpty()
                           //where hh1.SCHOOL_NO == SchoolNO
                           select new HRMT01QTY
                           {
                               SYEAR = hh1.SYEAR,
                               CLASS_NO = hh1.CLASS_NO,
                               NAME = hh1.SNAME,
                               USER_NO = hh1.USER_NO,
                               SEAT_NO = hh1.SEAT_NO,
                               QTY = w1.CASH_ALL,
                               CASH_ALL = w1.CASH_ALL,
                               CASH_AVAILABLE = w1.CASH_AVAILABLE,
                           };
            if (string.IsNullOrWhiteSpace(model.whereKeyword) == false)
            {
                RankCash = RankCash.Where(a => a.USER_NO.Contains(model.whereKeyword) || a.NAME.Contains(model.whereKeyword));
            }
            model.VAWA005List = RankCash.OrderByDescending(a => a.CASH_ALL).ToPagedList(model.Page > 0 ? model.Page - 1 : 0, 20);

            return View(model);
        }
    }
}