/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Gyre-Pagella/Normal/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.GyrePagellaMathJax_Normal={directory:"Normal/Regular",family:"GyrePagellaMathJax_Normal",testString:"\u00A0\u210E\uD835\uDC00\uD835\uDC01\uD835\uDC02\uD835\uDC03\uD835\uDC04\uD835\uDC05\uD835\uDC06\uD835\uDC07\uD835\uDC08\uD835\uDC09\uD835\uDC0A\uD835\uDC0B\uD835\uDC0C",32:[0,0,250,0,0],160:[0,0,250,0,0],8462:[733,9,500,10,471],119808:[686,3,778,24,757],119809:[681,3,667,39,611],119810:[695,17,722,44,695],119811:[681,3,833,35,786],119812:[681,3,611,39,577],119813:[681,3,556,28,539],119814:[695,17,833,47,776],119815:[681,3,833,36,796],119816:[681,3,389,39,350],119817:[681,213,389,-11,350],119818:[681,3,778,39,763],119819:[681,3,611,39,577],119820:[681,10,1000,32,968],119821:[681,16,833,35,798],119822:[695,17,833,47,787],119823:[681,3,611,39,594],119824:[695,184,833,47,787],119825:[681,3,722,39,708],119826:[695,17,611,57,559],119827:[681,3,667,17,650],119828:[681,17,778,26,760],119829:[681,3,778,20,763],119830:[686,3,1000,17,988],119831:[695,3,667,17,650],119832:[695,3,667,15,660],119833:[681,3,667,24,627],119834:[471,17,500,40,478],119835:[720,17,611,10,556],119836:[471,17,444,37,414],119837:[720,17,611,42,577],119838:[471,17,500,42,461],119839:[720,3,389,34,381],119840:[471,266,556,26,535],119841:[720,3,611,24,587],119842:[666,3,333,34,298],119843:[666,266,333,3,233],119844:[720,3,611,21,597],119845:[720,3,333,24,296],119846:[471,3,889,24,864],119847:[471,3,611,24,587],119848:[471,17,556,40,517],119849:[471,258,611,29,567],119850:[471,258,611,52,589],119851:[471,3,389,30,389],119852:[471,17,444,39,405],119853:[632,17,333,22,324],119854:[471,17,611,25,583],119855:[459,3,556,11,545],119856:[471,3,833,13,820],119857:[471,3,500,20,483],119858:[459,266,556,10,546],119859:[457,3,500,16,464],119860:[705,3,722,-19,677],119861:[692,3,611,26,559],119862:[706,18,667,45,651],119863:[692,3,778,28,741],119864:[692,3,611,30,570],119865:[692,3,556,0,548],119866:[706,18,722,50,694],119867:[692,3,778,-3,800],119868:[692,3,333,7,354],119869:[692,206,333,-35,358],119870:[692,3,667,13,683],119871:[692,3,556,16,523],119872:[692,18,944,-19,940],119873:[692,11,778,2,804],119874:[706,18,778,53,748],119875:[692,3,611,9,594],119876:[706,201,778,53,748],119877:[692,3,667,9,639],119878:[706,18,556,42,506],119879:[692,3,611,53,635],119880:[692,19,778,88,798],119881:[692,8,722,75,754],119882:[700,8,944,71,980],119883:[692,3,722,20,734],119884:[705,3,667,52,675],119885:[692,3,667,20,637],119886:[482,11,444,4,406],119887:[733,11,463,37,433],119888:[482,11,407,25,389],119889:[733,11,500,17,483],119890:[482,11,389,15,374],119891:[733,276,278,-162,413],119892:[482,276,500,-37,498],119894:[670,9,278,34,266],119895:[670,276,278,-70,273],119896:[733,9,444,8,449],119897:[733,9,278,36,251],119898:[482,9,778,24,740],119899:[482,9,556,24,514],119900:[482,11,444,17,411],119901:[482,276,500,-7,465],119902:[482,276,463,24,432],119903:[482,9,389,26,384],119904:[482,11,389,9,345],119905:[646,9,333,41,310],119906:[482,11,556,32,512],119907:[482,11,500,21,477],119908:[482,11,722,21,699],119909:[482,11,500,9,484],119910:[482,276,500,-8,490],119911:[482,11,444,-1,416],119912:[683,3,722,-35,685],119913:[682,3,667,8,629],119914:[695,17,685,69,695],119915:[682,3,778,0,747],119916:[681,3,611,11,606],119917:[681,3,556,-6,593],119918:[695,17,778,72,750],119919:[681,3,778,-12,826],119920:[681,3,389,-1,412],119921:[681,207,389,-29,417],119922:[681,3,722,-10,746],119923:[681,3,611,26,578],119924:[681,17,944,-23,985],119925:[681,3,778,-2,829],119926:[695,17,833,76,794],119927:[681,3,667,11,673],119928:[695,222,833,76,794],119929:[681,3,722,4,697],119930:[695,17,556,50,517],119931:[681,3,611,56,674],119932:[681,17,778,83,825],119933:[681,3,667,67,745],119934:[689,3,1000,67,1073],119935:[681,3,722,-9,772],119936:[695,3,611,54,675],119937:[681,3,667,1,676],119938:[470,17,556,44,519],119939:[726,17,537,44,494],119940:[469,17,444,32,436],119941:[726,17,556,38,550],119942:[469,17,444,28,418],119943:[726,271,333,-130,449],119944:[469,271,500,-50,529],119945:[726,17,556,22,522],119946:[675,17,333,26,301],119947:[675,271,333,-64,311],119948:[726,17,556,34,528],119949:[726,17,333,64,318],119950:[469,17,833,19,803],119951:[469,17,556,17,521],119952:[469,17,556,48,502],119953:[469,271,556,-21,516],119954:[469,271,537,32,513],119955:[469,17,389,20,411],119956:[469,17,444,25,406],119957:[636,17,389,42,409],119958:[469,17,556,22,521],119959:[469,17,556,19,513],119960:[469,17,833,27,802],119961:[469,17,500,-8,500],119962:[469,271,556,13,541],119963:[469,17,500,31,470],120484:[482,9,278,34,241],120485:[482,276,278,-70,228],120488:[686,3,748,6,739],120489:[681,3,659,31,603],120490:[681,3,562,31,542],120491:[686,3,662,25,637],120492:[681,3,606,31,569],120493:[681,3,670,25,628],120494:[681,3,822,31,791],120495:[695,17,831,47,787],120496:[681,3,389,40,351],120497:[681,3,761,31,755],120498:[686,3,748,6,739],120499:[681,10,1009,38,974],120500:[681,16,822,31,794],120501:[681,3,719,42,676],120502:[695,17,832,46,786],120503:[681,3,822,31,791],120504:[681,3,611,31,586],120505:[695,17,831,47,787],120506:[681,3,669,25,628],120507:[681,3,673,20,653],120508:[695,3,675,15,660],120509:[681,3,833,47,787],120510:[695,3,620,-8,625],120511:[681,3,742,4,738],120512:[695,3,827,27,804],120513:[676,13,662,25,637],120514:[469,17,563,43,563],120515:[718,272,617,71,576],120516:[469,232,571,-14,572],120517:[718,17,482,41,440],120518:[471,17,491,41,467],120519:[718,232,491,45,468],120520:[469,271,569,5,499],120521:[695,17,550,49,502],120522:[469,17,359,79,349],120523:[469,17,623,22,601],120524:[718,19,613,10,603],120525:[469,271,608,16,601],120526:[469,17,533,-9,494],120527:[718,232,476,54,477],120528:[469,17,539,41,496],120529:[493,17,777,55,754],120530:[469,271,570,69,529],120531:[469,232,486,48,464],120532:[482,17,660,54,637],120533:[493,17,618,32,594],120534:[469,17,538,-5,495],120535:[469,271,727,41,684],120536:[469,232,654,22,656],120537:[636,271,728,-5,687],120538:[469,17,802,41,759],120539:[740,17,571,47,512],120540:[471,17,576,69,536],120541:[695,17,602,22,580],120542:[469,17,693,39,654],120543:[633,268,722,41,680],120544:[469,271,561,70,519],120545:[559,17,803,41,760],120546:[700,3,744,-35,697],120547:[692,3,624,33,601],120548:[692,3,539,-17,609],120549:[700,3,616,-33,572],120550:[692,3,615,7,640],120551:[692,3,661,-23,705],120552:[692,3,819,-17,878],120553:[709,20,833,67,813],120554:[692,3,334,-17,393],120555:[692,3,698,-18,761],120556:[700,3,720,-46,685],120557:[692,13,934,-22,987],120558:[692,20,836,-18,885],120559:[692,3,693,16,683],120560:[709,20,833,66,811],120561:[692,3,819,-17,878],120562:[692,3,592,-17,627],120563:[709,20,833,67,813],120564:[692,3,696,4,672],120565:[692,3,602,79,666],120566:[705,3,634,78,717],120567:[692,3,833,71,806],120568:[700,3,643,-31,704],120569:[692,3,767,94,832],120570:[709,3,822,4,799],120571:[690,13,616,80,684],120572:[482,11,537,22,496],120573:[711,277,582,7,534],120574:[482,226,571,14,589],120575:[711,11,458,48,450],120576:[484,11,444,39,401],120577:[711,226,454,47,539],120578:[482,276,526,46,506],120579:[711,11,492,71,493],120580:[482,9,285,54,264],120581:[482,9,518,61,526],120582:[711,12,569,-32,543],120583:[482,276,596,32,549],120584:[482,12,499,41,517],120585:[711,226,456,48,540],120586:[482,11,484,53,454],120587:[493,11,677,68,705],120588:[482,276,524,-6,495],120589:[482,226,472,38,454],120590:[494,11,582,52,639],120591:[493,11,559,68,594],120592:[482,11,528,56,510],120593:[482,276,638,50,610],120594:[482,226,557,-44,588],120595:[646,276,646,48,640],120596:[482,11,765,42,759],120597:[733,9,545,64,526],120598:[482,11,489,54,491],120599:[711,11,553,57,581],120600:[483,17,660,72,609],120601:[644,274,637,54,605],120602:[482,276,535,55,492],120603:[548,11,765,42,759],120604:[686,3,733,-35,690],120605:[681,3,646,22,627],120606:[681,3,551,-10,609],120607:[686,3,649,-13,593],120608:[681,3,594,22,625],120609:[681,3,657,-16,692],120610:[681,3,806,-10,855],120611:[695,17,814,56,799],120612:[681,3,381,-1,424],120613:[681,3,746,-10,795],120614:[686,3,733,-35,690],120615:[681,10,989,-3,1035],120616:[681,16,806,-10,858],120617:[681,3,705,22,693],120618:[695,17,815,55,798],120619:[681,3,806,-10,855],120620:[681,3,599,-10,630],120621:[695,17,814,56,799],120622:[681,3,656,-16,640],120623:[681,3,660,84,719],120624:[695,3,661,84,726],120625:[681,3,816,61,795],120626:[695,3,608,-48,691],120627:[681,3,727,77,803],120628:[695,3,810,-4,806],120629:[676,13,649,95,701],120630:[469,17,536,20,514],120631:[718,272,588,-21,551],120632:[469,232,544,12,585],120633:[718,17,459,34,483],120634:[471,17,468,24,444],120635:[718,232,468,40,525],120636:[469,271,542,41,523],120637:[695,17,524,61,526],120638:[469,17,342,48,313],120639:[469,17,593,55,570],120640:[718,19,584,-29,552],120641:[469,271,579,3,551],120642:[469,17,508,27,527],120643:[718,232,453,49,534],120644:[469,17,513,33,495],120645:[493,17,740,61,778],120646:[469,271,543,-13,526],120647:[469,232,463,36,451],120648:[482,17,629,46,664],120649:[493,17,589,65,626],120650:[469,17,512,33,507],120651:[469,271,692,31,675],120652:[469,232,623,-42,582],120653:[636,271,693,33,690],120654:[469,17,764,37,759],120655:[740,17,544,70,529],120656:[471,17,549,64,538],120657:[695,17,573,59,618],120658:[469,17,660,67,609],120659:[633,268,688,36,667],120660:[469,271,534,54,517],120661:[559,17,765,37,760],120782:[660,17,500,33,468],120783:[670,3,500,35,455],120784:[660,3,500,25,472],120785:[660,17,500,22,458],120786:[672,3,500,12,473],120787:[656,17,500,42,472],120788:[660,17,500,37,469],120789:[656,3,500,46,493],120790:[660,17,500,34,467],120791:[660,17,500,31,463]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"GyrePagellaMathJax_Normal"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Normal/Regular/Main.js"]);
