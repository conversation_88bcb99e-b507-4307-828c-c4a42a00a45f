/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Neo-Euler/Script/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.NeoEulerMathJax_Script={directory:"Script/Regular",family:"NeoEulerMathJax_Script",testString:"\u00A0\u210B\u2110\u2112\u211B\u212C\u2130\u2131\u2133\uD835\uDC9C\uD835\uDC9E\uD835\uDC9F\uD835\uDCA2\uD835\uDCA5\uD835\uDCA6",32:[0,0,333,0,0],160:[0,0,333,0,0],8459:[690,8,884,33,872],8464:[685,14,430,27,412],8466:[685,7,715,26,613],8475:[712,6,722,33,711],8492:[712,1,761,42,683],8496:[702,12,608,69,528],8497:[699,15,649,38,710],8499:[699,13,982,27,961],119964:[694,15,770,-8,736],119966:[697,15,602,70,561],119967:[716,-4,830,52,764],119970:[697,130,604,72,524],119973:[692,129,506,28,468],119974:[690,12,824,29,794],119977:[706,7,774,28,802],119978:[686,18,707,71,633],119979:[710,11,660,44,638],119980:[694,24,662,72,616],119982:[702,12,542,28,481],119983:[693,6,586,26,660],119984:[699,16,714,25,691],119985:[709,9,664,27,638],119986:[702,5,989,30,963],119987:[706,9,716,50,693],119988:[702,136,595,25,502],119989:[696,11,656,58,632],120016:[711,17,887,10,860],120017:[727,1,864,38,794],120018:[709,15,680,59,644],120019:[727,1,938,28,881],120020:[708,12,687,61,628],120021:[731,14,755,31,802],120022:[705,138,683,52,601],120023:[699,12,999,29,990],120024:[703,18,563,28,506],120025:[701,137,571,31,540],120026:[709,9,931,27,903],120027:[710,12,808,26,741],120028:[710,17,1111,27,1081],120029:[712,13,874,25,889],120030:[707,20,799,45,738],120031:[726,13,746,29,712],120032:[705,42,765,42,725],120033:[732,12,816,29,842],120034:[715,18,598,7,554],120035:[697,11,654,26,735],120036:[709,13,799,9,797],120037:[702,16,751,27,714],120038:[710,8,1119,27,1081],120039:[712,11,811,27,774],120040:[709,135,680,-4,599],120041:[705,14,742,57,714]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"NeoEulerMathJax_Script"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Script/Regular/Main.js"]);
