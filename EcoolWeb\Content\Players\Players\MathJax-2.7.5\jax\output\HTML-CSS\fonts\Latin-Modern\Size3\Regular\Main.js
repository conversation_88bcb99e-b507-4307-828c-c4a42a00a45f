/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Latin-Modern/Size3/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.LatinModernMathJax_Size3={directory:"Size3/Regular",family:"LatinModernMathJax_Size3",testString:"\u00A0\u0302\u0303\u0306\u030C\u0311\u032C\u032D\u032E\u032F\u0330\u2016\u2044\u20E9\u221A",32:[0,0,332,0,0],40:[972,472,523,156,461],41:[972,472,523,62,367],47:[1374,874,964,57,909],91:[975,475,444,226,414],92:[1374,874,964,56,908],93:[975,475,444,30,218],123:[975,475,624,100,524],124:[1117,617,278,113,166],125:[975,475,624,100,524],160:[0,0,332,0,0],770:[747,-572,919,0,919],771:[757,-543,931,0,931],774:[742,-577,937,0,937],780:[740,-565,919,0,919],785:[758,-592,937,0,937],812:[-96,271,919,0,919],813:[-108,283,919,0,919],814:[-96,262,937,0,937],815:[-118,284,937,0,937],816:[-118,332,931,0,931],8214:[1117,617,372,57,317],8260:[1374,874,964,57,909],8425:[742,-535,1485,0,1485],8730:[1450,950,1000,111,1020],8739:[1117,617,278,113,166],8741:[1117,617,372,57,317],8968:[975,475,499,189,471],8969:[975,475,499,28,310],8970:[975,475,499,189,471],8971:[975,475,499,28,310],9001:[975,475,537,154,476],9002:[975,475,537,61,383],9140:[742,-535,1485,0,1485],9141:[-105,312,1485,0,1485],9180:[767,-509,2012,0,2012],9181:[-79,337,2012,0,2012],9182:[825,-506,1996,0,1996],9183:[-75,394,1996,0,1996],9184:[858,-610,2056,0,2056],9185:[-180,428,2056,0,2056],10214:[975,475,555,170,532],10215:[975,475,555,23,385],10216:[975,475,537,154,476],10217:[975,475,537,61,383],10218:[975,475,781,154,720],10219:[975,475,781,61,627],10222:[991,491,370,142,314],10223:[991,491,370,56,228]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"LatinModernMathJax_Size3"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Size3/Regular/Main.js"]);
