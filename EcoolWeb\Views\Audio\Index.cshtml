﻿
@if (Request.Browser.Browser == "InternetExplorer" || Request.Browser.Browser == "IE")
{
    <div class="form-group">
        <span class="control-label label_dt col-md-3 col-sm-3 col-lg-2">線上錄音</span>
        <div class="col-md-9 col-sm-9 col-lg-10" >
            <input type="hidden" id="TempVoicefile" name="TempVoicefile" class="form-control" />
            <button class="btn btn-default btn-sm" type="button" onclick="window.open('@Url.Action("FlashWavRecorder", "Audio")','Audio','scrollbars=yes,top=40,width=400,height=250,left=150','replace=true'); return false;">開起錄音程式</button>
        </div>
    </div>
}
else
{
    <script type="text/javascript" src="~/Scripts/HZRecorder.js"></script>
    <script type="text/javascript" src="~/Scripts/main.js"></script>
    <style>
        canvas {
            display: inline-block;
            background: #202020;
            width: 95%;
            height: 45%;
            box-shadow: 0px 0px 10px blue;
        }

        #viz {
            height: 80%;
            width: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-around;
            align-items: center;
        }
    </style>
    <input  id="TempVoicefile" name="TempVoicefile" class="form-control"  type="hidden"/>
      <!-- Button trigger modal -->
    @*<button type="button" class="btn btn-default btn-sm" data-toggle="modal" data-target="#myModal" onclick="Audio()">
        開啟錄音
    </button>*@
        @*<input type="text" id="hidVoicePath" name="hidVoicePath">*@
        <!-- Modal -->
        <div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="false">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                        <h4 class="modal-title" id="myModalLabel">錄音程式</h4>
                    </div>
                    <div class="modal-body">
                        <div id="viz">
                            <canvas id="analyser" width="512" height="250"></canvas>
                        </div>
                        <p>
                            <audio controls autoplay="" id="audio"></audio>
                        </p>
                    </div>
                    <div class="modal-footer">

                        <div class="btn-group" role="group">
                            <button id="start" type="button" class="btn btn-primary btn-xs" onclick="startRecording()">開始錄音</button>
                            <button id="stop" type="button" class="btn btn-primary btn-xs" onclick="stopRecording()">停止錄音</button>
                            <button id="play" type="button" class="btn btn-primary btn-xs" onclick="playRecording()">播放錄音</button>
                            <button id="upload" type="button" class="btn btn-primary btn-xs" onclick="uploadAudio()">上傳</button>
                            <button type="button" class="btn btn-default btn-xs" data-dismiss="modal">關閉</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>


  <script type="text/javascript">

    $("#stop").prop('disabled', true);
    $("#play").prop('disabled', true);
    $("#upload").prop('disabled', true);

    // window.addEventListener('load', initAudio);

    var recorder;
    var audio = document.querySelector('audio');

    $('#audio').hide();

    //錄音
    function startRecording() {
        HZRecorder.get(function (rec) {
            recorder = rec;
            recorder.start();

            $("#start").prop('disabled', true);
            $("#stop").prop('disabled', false);
        });
    }

    //停止
    function stopRecording() {

        recorder.stop();

        $("#stop").prop('disabled', true);
        $("#play").prop('disabled', false);
        $("#upload").prop('disabled', false);
    }

    function playRecording() {
        $('#analyser').hide();
        $('#audio').show()
        recorder.play(audio);
    }

    function uploadAudio() {
        recorder.upload("@(Url.Action("UploadAction", "Audio"))", function (state, e) {

            switch (state) {
                case 'uploading':
                    //var percentComplete = Math.round(e.loaded * 100 / e.total) + '%';
                    break;
                case 'ok':
                    //alert(e.target.responseText);
                    var arryData = e.srcElement.response.split(",")
                    $('#TempVoicefile').val(arryData[1]);
                    alert("上傳成功");
                    break;
                case 'error':
                    alert("上傳失敗");
                    break;
                case 'cancel':
                    alert("上傳被取消");
                    break;
            }
        });
    }
        </script>

}


      
