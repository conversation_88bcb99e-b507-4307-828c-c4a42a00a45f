﻿@using ECOOL_APP;
@{

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    var Permission = ViewBag.Permission as List<ControllerPermissionfile>;
    UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
}
<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>
<div class="form-group">

    @if (Permission.Where(a => a.ActionName == "ADDTList_apply").Any() || user?.USER_TYPE == UserType.Student || (user?.USER_TYPE == UserType.Teacher && user?.CLASS_NO!=null)|| (user?.USER_TYPE == UserType.Admin))
                            {
        <a href='@Url.Action("ADDTList_apply","ADDT")' role="button" class="btn btn-sm btn-sys @(ViewBag.NowAction=="ADDTList_apply" ? "active":"")">
            我要申請閱讀認證
        </a>
        }

    <a href='@Url.Action("PushList","ADDT", new { ADDTList = "ADDTList" })' role="button" class="btn btn-sm btn-sys  @(ViewBag.NowAction=="PushList" ? "active":"")">
        推薦清單
    </a>

    <button name="btnExplain" target="_blank" role="button" class="btn btn-sm btn-sys" id="btnExplain" href="@Url.Action("ADDT08", "ADDT")">
        等級說明
    </button>

    @if (user != null)
        {
            if (user.USER_TYPE == UserType.Student)
            {
            <a href='@Url.Action("QUERY","ADDI02")' role="button" class="btn btn-sm btn-sys @(ViewBag.NowAction=="QUERY" ? "active":"")">
                我的閱讀認證清單
            </a>
    }
        else if (user.USER_TYPE == UserType.Parents)
        {
            <a href='@Url.Action("QUERY","ADDI02")' role="button" class="btn btn-sm btn-sys @(ViewBag.NowAction=="QUERY" ? "active":"")">
                寶貝的清單
            </a>
        }
        else
        {

            if (Permission.Where(a => a.ActionName == "ADDTList_apply").Any())
            {
                if (!string.IsNullOrWhiteSpace(user.TEACH_CLASS_NO))
                {
                    <a href='@Url.Action("QUERY","ADDI02")' role="button" class="btn btn-sm btn-sys @(ViewBag.NowAction=="QUERY" ? "active":"")">
                        我的班級閱讀認證清單-草稿、退回資料
                    </a>
                }
                else
                {
                    <a href='@Url.Action("QUERY","ADDI02")' role="button" class="btn btn-sm btn-sys @(ViewBag.NowAction=="QUERY" ? "active":"")">
                        學生閱讀認證清單-草稿、退回資料
                    </a>
                }

            }

            <a href='@Url.Action("ADDTALLList","ADDT")' role="button" class="btn btn-sm btn-sys @(ViewBag.NowAction=="ADDTALLList" ? "active":"")">
                認證審核一覽表
            </a>

            if (Permission.Where(a => a.ActionName == "BATCH_DEL").Any())
            {
                <a href='@Url.Action("DEL_LOG_LIST","ADDT")' role="button" class="btn btn-sm btn-sys @(ViewBag.NowAction=="BATCH_DEL" ? "active":"")">
                    作廢失敗記錄一覽表
                </a>
            }
        }
    }

    @*<a href='@Url.Action("ADDTList","ADDT")' role="button" class="btn btn-sm btn-sys @(ViewBag.NowAction=="ADDTList" ? "active":"")">
            閱讀認證排行榜
        </a>*@
</div>

<script type="text/javascript">
    $(document).ready(function () {
        $("#btnExplain").colorbox({ width: "80%", height: "80%", opacity: 0.82 });
    });
</script>