/*
 *  /MathJax/jax/output/HTML-CSS/fonts/TeX/Main/Bold/Latin1Supplement.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["MathJax_Main-bold"],{160:[0,0,250,0,0],168:[695,-535,575,96,478],172:[371,-61,767,64,702],175:[607,-540,575,80,494],176:[702,-536,575,160,414],177:[728,35,894,64,829],180:[706,-503,575,236,460],215:[530,28,894,168,726],247:[597,96,894,64,828]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/Main/Bold/Latin1Supplement.js");
