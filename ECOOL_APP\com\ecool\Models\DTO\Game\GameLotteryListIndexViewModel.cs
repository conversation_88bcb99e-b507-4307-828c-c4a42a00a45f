﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class GameLotteryListIndexViewModel
    {
        public ADDT26 GameInfo { get; set; }

        public string IsPostBack { get; set; }

        /// <summary>
        /// 查詢ID URL 傳入
        /// </summary>
        public string GAME_NO { get; set; }

        [DisplayName("最後領獎日")]
        [DataType(DataType.Date), DisplayFormat(DataFormatString = "{0:yyyy/MM/dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? RECEIVE_DATE { get; set; }

        public GameSearchViewModel Search { get; set; }

        public BatchCashIntoSearchViewModel CashSearch { get; set; }

        public List<GameLotteryListViewModel> Main { get; set; }

        public List<GameLotteryPeopleViewModel> People { get; set; }
    }
}