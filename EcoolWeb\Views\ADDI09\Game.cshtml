﻿@model ECOOL_APP.com.ecool.Models.DTO.ADDI09EditOoneViewModel
@using ECOOL_APP.com.ecool.Models.DTO;

<link href="~/Content/css/EzCss.css" rel="stylesheet" />
@{
    ViewBag.Title = ViewBag.Panel_Title;

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")
@Html.ValidationMessage("ErrorMsg", new { @class = "text-danger" })

@{
    Html.RenderAction("_EGameMenu", "BarcCodeMyCash", new { NowAction = "Game" });
}


@using (Html.BeginForm((string)ViewBag.Action, (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", enctype = "multipart/form-data" }))
{

    @Html.AntiForgeryToken()
    @Html.Hidden("SYS_TABLE_TYPE", (string)ViewBag.SYS_TABLE_TYPE)
    @Html.HiddenFor(model => model.USER_NAME)
    @Html.HiddenFor(model => model.IsFix)
    @Html.HiddenFor(model => model.IsRandom)
    @Html.HiddenFor(model => model.IsRandomHighPoint)
    @Html.Hidden("CASH2", Model.CASH)

    @Html.ValidationSummary(true, "", new { @class = "text-danger" })

    <div class="Div-EZ-ADDI09">
        <div class="form-horizontal">
            <div class="form-group">
                @Html.Label("請刷學生卡或QR Code：", htmlAttributes: new { @class = "control-label col-md-3" })
                <div class="col-md-8">
                    @Html.EditorFor(model => model.USER_NO, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.USER_NO, "", new { @class = "text-danger" })
                </div>
            </div>
            @*<div class="form-group">
                    @Html.LabelFor(model => model.CLASS_SEAT, htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-8" style="text-align:center;vertical-align:middle;">
                        @Html.EditorFor(model => model.CLASS_SEAT, new { htmlAttributes = new { @class = "form-control" } })
                        @Html.ValidationMessageFor(model => model.CLASS_SEAT, "", new { @class = "text-danger" })
                        @Html.Label(" ", new { @id = "SHOWNAME", style = "margin-top:5px" })
                    </div>
                </div>*@
            <div class="form-group">
                @Html.Label("市集關卡", htmlAttributes: new { @class = "control-label col-md-3" })
                <div class="col-md-8">
                    @Html.DropDownListFor(model => model.SUBJECT, (IEnumerable<SelectListItem>)ViewBag.SubjectSelectItem, new { @class = "form-control" })
                    @*@Html.EditorFor(model => model.SUBJECT, new { htmlAttributes = new { @class = "form-control" } })*@
                    @Html.ValidationMessageFor(model => model.SUBJECT, "", new { @class = "text-danger" })
                </div>
            </div>
            @*<div class="form-group">
                    @Html.LabelFor(model => model.CONTENT_TXT, htmlAttributes: new { @class = "control-label col-md-4" })
                    <div class="col-md-8">
                        @Html.TextAreaFor(model => model.CONTENT_TXT, 5, 100, new { @class = "form-control" })
                        @Html.ValidationMessageFor(model => model.CONTENT_TXT, "", new { @class = "text-danger" })
                    </div>
                </div>*@
            @if (Model.IsRandom == false)
            {
                <div class="form-group">
                    @Html.Label("加點/消費點數", htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-9" style="padding-top:7px;">

                        @foreach (short c in ViewBag.CashArray)
                        {
                            bool bind = false;
                            if (Model != null) { bind = (Model.CASH == c); }
                            if (bind)
                            {
                                @Html.RadioButtonFor(model => model.CASH, c, new { @checked = "checked" })
                            }
                            else
                            {
                                @Html.RadioButtonFor(model => model.CASH, c, new { @onclick = "CashChange(this.value)" })
                            }
                            @Html.Label(c.ToString())
                            @:&nbsp
                        }

                        @*@Html.EditorFor(model => model.CASH, new { htmlAttributes = new { @class = "form-control", @type = "text" } })
                            @Html.ValidationMessageFor(model => model.CASH, "", new { @class = "text-danger" })*@
                    </div>
                </div>
            }
            <div class="form-group">
                <div class="col-md-12" style="text-align:center">
                    <img src="~/Content/Game/Game0.jpg" class="img-responsive" alt="Responsive image" />
                </div>
            </div>
        </div>
    </div>

    <div style="height:20px"></div>
    <div class="form-group text-center col-md-offset-3 ">
        @if (Model.IsFix)
        {
            <div class="col-md-3 ">
                <button type="button" id="myButton" data-loading-text="Loading..." class="btn btn-default" autocomplete="off">
                    確定
                </button>
            </div>
        }
        @if (Model.IsRandom)
        {
            <div class="col-md-3">
                <div id="BtnRandom" class="btn btn-default" onclick="GoRandom()">
                    隨機給點
                </div>
            </div>
        }
        <div class="col-md-offset-1 col-md-3 ">
            @Html.ActionLink("回選擇模式", "EditOneIndex", new { SYS_TABLE_TYPE = ViewBag.SYS_TABLE_TYPE }, new { @class = "btn btn-default", @role = "button" })
        </div>
    </div>

    <link href="~/Content/css/jquery-ui.min.css" rel="stylesheet" />
    <script src=@Url.Content("~/Scripts/jquery-3.6.4.min.js")></script>
    <script src="~/Scripts/jquery-ui.min.js"></script>
    <div id="dialog-confirm" style="display:none;" title="確認給點？">
        <span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span>您確定要給點嗎？
    </div>

    <script type="text/javascript">

        $(document).ready(function () {
            var SIn = window.document.getElementById("USER_NO");
            if (SIn != null) {
                SIn.focus();
            }
            $("#USER_NO").val('');

        });
        $(function () {
            $("form input").keypress(function (e) {
                if ((e.which && e.which == 13) || (e.keyCode && e.keyCode == 13)) {
                    $('#myButton').click();
                    return false;
                } else {
                    return true;
                }
            });
        });

        $("#dialog-confirm").dialog({
            resizable: true,
            autoOpen: false,
            height: 200,
            width: 200,
            modal: true,
            buttons: {
                '確認': function () {
                    $('#myButton').hide();
                    form1.submit();
                    $(this).dialog('close');
                },
                '取消': function () {
                    $(this).dialog('close');
                }
            }
        });

        $('#myButton').on('click', function () {
            @*var UNAME = $('#@Html.IdFor(m => m.USER_NAME)').val();
            if (UNAME == "" || UNAME == null) {
                window.alert('請輸入學號或班級座號');
                return;
            }*@
            var SUBJECT = $('#@Html.IdFor(m => m.SUBJECT)').val();
            if (SUBJECT == "" || SUBJECT == null) {
                window.alert('請選擇獎懲主旨');
                return;
            }
            form1.submit();

            @*$('#dialog-confirm').text("您確定要給" + UNAME + "酷幣點數" + $('#@Html.Id("CASH2")').val() + "點嗎 ?");
            $('#dialog-confirm').dialog('open');*@
        })


        @*$('#@Html.IdFor(m => m.USER_NO),#@Html.IdFor(m => m.CLASS_SEAT)').change(function () {

            DbQuery();
        });*@

        function DbQuery() {

            $.ajax({
                url: "@(Url.Action("ToQueryUser", "ADDI09"))",     // url位置
                type: 'post',                   // post/get
                data: { "USER_NO": $('#@Html.IdFor(m => m.USER_NO)').val(), "CLASS_SEAT": $('#@Html.IdFor(m => m.CLASS_SEAT)').val() },
                dataType: 'json',               // xml/json/script/html
                cache: false,                   // 是否允許快取
                success: function (data) {
                    var res = jQuery.parseJSON(data);

                    if (res.USER_NO == '') {
                        alert(res.USER_NAME);
                        $('#myButton').text("確定給點");
                        $('#BtnRandom').text("隨機給點");
                        $('#SHOWNAME').text(" ");
                    }
                    else {
                        $('#@Html.IdFor(m => m.USER_NAME)').val(res.USER_NAME);
                        $('#myButton').text("確定給點：" + res.USER_NAME);
                        $('#BtnRandom').text("隨機給點：" + res.USER_NAME);
                        $('#SHOWNAME').text(res.USER_NAME);
                    }
                },
                error: function (xhr, err) {
                    //alert("readyState: " + xhr.readyState + "\nstatus: " + xhr.status);
                    //alert("responseText: " + xhr.responseText);
                }

            });

        }

        function GoRandom() {

            form1.action = '@Url.Action("EditOneRandom", (string)ViewBag.BRE_NO,new { IsRandom = Model.IsRandom, IsFix = Model.IsFix})';
            form1.submit();

        }

        function CashChange(ans) {
            $('#@Html.Id("CASH2")').val(ans);
        }

    </script>
}


