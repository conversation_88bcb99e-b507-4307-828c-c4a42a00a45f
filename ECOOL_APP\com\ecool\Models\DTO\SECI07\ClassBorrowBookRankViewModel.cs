﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.com.ecool.Models.DTO
{
    public class ClassBorrowBookRankViewModel
    {
        public string Where_SCHOOLNO { get; set; }

        [Display(Name = "學年度")]
        public string Where_SYEAR { get; set; }

        [Display(Name = "月份")]
        public string Where_MONTH { get; set; }

        public List<ClassBorrowBookRank> ClassRankList { get; set; }

        /// <summary>
        /// 排序欄位
        /// </summary>
        public string OrdercColumn { get; set; }
    }

    public class ClassBorrowBookRank
    {
        [Display(Name = "學校代號")]
        public string SCHOOL_NO { get; set; }

        [Display(Name = "班級")]
        public string CLASS_NO { get; set; }

        [Display(Name = "班級人數")]
        public int CLASSQTY { get; set; }

        [Display(Name = "借書量(本)")]
        public int BORROW_BOOK_COUNT { get; set; }

        [Display(Name = "平均借書量(本)")]
        public double BORROW_BOOK_AVG { get; set; }

        [Display(Name = "排名")]
        public int RANK { get; set; }

        [Display(Name = "平均排名")]
        public int AVG_RANK { get; set; }
    }
}