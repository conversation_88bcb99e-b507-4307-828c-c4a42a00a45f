﻿using MvcPaging;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;

namespace ECOOL_APP.EF
{
    public  class ZZZI19IndexViewModel
    {
        public string REF_KEY { get; set; }
        public string SaveType { get; set; }
        /// <summary>
        /// 搜尋字串
        /// </summary>
        public string whereKeyword { get; set; }
        public byte? WhereSyaer { get; set; }
        /// <summary>
        /// 搜尋帳號
        /// </summary>
        public string whereUserNo { get; set; }

        /// <summary>
        /// 搜尋狀態
        /// </summary>
        public string whereStatus { get; set; }


        /// <summary>
        /// 搜尋年級
        /// </summary>
        public string whereGrade { get; set; }

        /// <summary>
        /// 搜尋班級
        /// </summary>
        public string whereClass_No { get; set; }


        /// <summary>
        /// 一次顯示幾筆
        /// </summary>
        public string ShowPageCount { get; set; }


        /// <summary>
        /// 排序欄位
        /// </summary>
        public string OrdercColumn { get; set; }

        public int Page { get; set; }


        /// <summary>
        /// 查詢結果
        /// </summary>
        public IPagedList<ZZZI19Hrmt01ViewModel> HRMT01List;
        public ZZZI19Hrmt01ViewModel zZZI19Hrmt { get; set; }

        public List<ZZZI19CheckBoxListViewModel> DataList { get; set; }

        public ZZZI19IndexViewModel()
        {
            Page = 0;
            OrdercColumn = "";
        }

    }
}