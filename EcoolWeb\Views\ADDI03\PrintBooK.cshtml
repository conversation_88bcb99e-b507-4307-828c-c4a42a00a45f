﻿@model IEnumerable<ECOOL_APP.com.ecool.Models.DTO.ADDI03PrintViewModel>
@{
    ViewBag.Title = "取得閱讀護照書單閱讀情況一覽表 PRINT";
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
    int i = 1;

}
<script src="~/Scripts/Blob.js"></script>
<script src="~/Scripts/FileSaver.js"></script>
<div class="Div-EZ-Right">
    <button id="ButtonExcel" class="btn btn-sm btn-sys">匯出excel</button>
</div>

<div class="print table-92Per" style="margin: 0px auto; " id="tbData">
    @foreach (var ClassItem in ViewBag.ClassStringList as List<SelectListItem>)
    {

        if (i > 1)
        {
            <div style="page-break-before:always;"></div>
        }
                    <samp>出現「<samp style="color:red">*</samp>」代表當初讀的閱讀護照書籍名稱與現在不一致</samp>
                    <table class="table-ecool table-ecool-pink-SEC" border="1">
                        <thead>
                            <tr>
                            </tr>
                            <tr class="text-center">
                                <th>
                                    班級
                                </th>
                                <th>
                                    座號
                                </th>
                                <th>
                                    姓名
                                </th>
                                @foreach (var At03 in ViewBag.ADDT03List as List<ADDT03>)
                                {
                                    <th>
                                        @At03.BOOK_NAME
                                    </th>
                                }
                                <th>
                                    備註
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var item in Model.Where(a => a.CLASS_NO == ClassItem.Value))
                            {
                                <tr align="center">
                                    <td style="white-space:nowrap;">@Html.DisplayFor(modelItem => item.CLASS_NO)</td>
                                    <td style="white-space:nowrap;">@Html.DisplayFor(modelItem => item.SEAT_NO)</td>
                                    <td style="white-space:nowrap;">@Html.DisplayFor(modelItem => item.NAME)</td>

                                    @foreach (var At03 in ViewBag.ADDT03List as List<ADDT03>)
                                    {
                                        <td align="center">
                                            @if (item.IsReadBooK.ContainsKey(At03.BOOK_ID))
                                            {
                                                if (Convert.ToInt32(item.IsReadBooK[At03.BOOK_ID]) > 0)
                                                {


                                                    if (item.IsReadBooKName.ContainsKey(At03.BOOK_NAME))
                                                    {
                                                        if (Convert.ToInt32(item.IsReadBooKName[At03.BOOK_NAME]) == 0)
                                                        {
                                                            <samp style="color:red">*</samp>
                                                        }
                                                        else
                                                        {
                                                            <samp>Y</samp>
                                                        }
                                                    }
                                                }
                                            }
                                        </td>
                                    }
                                    <td>@Html.DisplayFor(modelItem => item.PASS_DATE, "ShortDateTime")</td>
                                </tr>
                            }
                        </tbody>
                    </table>
            i++;
        }
</div>

<script type="text/javascript">
    window.onload =function()
    {
        window.print()
    }

    $(function () {
        $('#ButtonExcel').click(function () {
            var blob = new Blob([document.getElementById('tbData').innerHTML], {
                type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8"
            });
            var strFile = "Report.xls";
            saveAs(blob, strFile);
            return false;
        });
    });
</script>

