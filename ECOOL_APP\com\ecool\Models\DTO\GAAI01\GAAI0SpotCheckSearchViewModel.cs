﻿using MvcPaging;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class GAAI0SpotCheckSearchViewModel : SearchFormViewModelBase
    {
        /// <summary>
        /// 查詢結果
        /// </summary>
        public IPagedList<GAAI0SpotCheckSearchListViewModel> ListData;

        public string WhereSCHOOL_NO { get; set; }

        /// <summary>
        /// 年級
        /// </summary>
        public byte? WhereGRADE { get; set; }

        /// <summary>
        /// 班級
        /// </summary>
        [DisplayName("班級")]
        public string WhereCLASS_NO { get; set; }

        [DisplayName("學號/姓名")]
        public string WhereUser { get; set; }

        [DisplayFormat(DataFormatString = "{0:yyyy/MM/dd}", ApplyFormatInEditMode = true)]
        public DateTime? WhereALARM_DATEs { get; set; }

        [DisplayFormat(DataFormatString = "{0:yyyy/MM/dd}", ApplyFormatInEditMode = true)]
        public DateTime? WhereALARM_DATEe { get; set; }
    }
}