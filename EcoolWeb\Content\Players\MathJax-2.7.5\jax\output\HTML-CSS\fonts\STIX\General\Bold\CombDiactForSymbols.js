/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/General/Bold/CombDiactForSymbols.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXGeneral-bold"],{8400:[846,-637,0,-470,14],8401:[846,-637,0,-470,14],8402:[662,156,0,-298,-223],8406:[846,-508,0,-500,-16],8407:[846,-508,0,-470,14],8411:[666,-537,0,-512,37],8412:[666,-537,0,-627,132],8413:[760,254,0,-753,256],8417:[846,-508,0,-515,79],8420:[1055,169,0,-998,519],8421:[662,155,0,-470,12],8422:[662,156,0,-390,-111],8423:[760,172,0,-643,200],8424:[-109,238,0,-512,37],8425:[717,-544,0,-510,54],8426:[441,-65,0,-688,148],8427:[775,235,0,-505,208],8428:[-166,375,0,-470,14],8429:[-166,375,0,-470,14],8430:[-35,373,0,-490,-6],8431:[-35,373,0,-470,14],8432:[845,-543,0,-385,-115]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/Bold/CombDiactForSymbols.js");
