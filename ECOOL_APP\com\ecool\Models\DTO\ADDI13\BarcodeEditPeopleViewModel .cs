﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class BarcodeEditPeopleViewModel
    {
        /// <summary>
        ///學校
        /// </summary>
        [DisplayName("學校")]
        public string SCHOOL_NO { get; set; }
        public short? CASH { get; set; }
        [DisplayName("活動名稱")]
   
        public string ROLL_CALL_NAME { get; set; }
        public string ROLL_CALL_ID { get; set; }
        public string BarCode { get; set; }
        public string txtUSER_NO { get; set; }
        public byte  SYear { get; set; }
        public byte Semesters { get; set; }
        public string txtPASSWORD { get; set; }
        /// <summary>
        ///學校簡稱
        /// </summary>
        [DisplayName("學校")]
        public string SHORT_NAME { get; set; }

        /// <summary>
        ///學號
        /// </summary>
        [DisplayName("學號")]
        public string USER_NO { get; set; }

        /// <summary>
        ///全名
        /// </summary>
        [DisplayName("全名")]
        public string NAME { get; set; }

        /// <summary>
        ///年級
        /// </summary>
        [DisplayName("年級")]
        public byte? GRADE { get; set; }

        /// <summary>
        ///班級
        /// </summary>
        [DisplayName("班級")]
        public string CLASS_NO { get; set; }

        /// <summary>
        ///座號
        /// </summary>
        [DisplayName("座號")]
        public string SEAT_NO { get; set; }

        /// <summary>
        ///卡號
        /// </summary>
        [DisplayName("卡號")]
        public string CARD_NO { get; set; }
        /// <summary>
        /// 角色娃娃 圖 路徑
        /// </summary>
        public string PlayerUrl { get; set; }
        public string wREF_BRE_NO { get; set; }
        /// <summary>
        /// 可看舊學校 Hmto1.USER_STATUS ='9'
        /// </summary>
        public bool wIsQhisSchool { get; set; }
        /// <summary>
        /// 資料角度
        /// </summary>
        public string DATA_ANGLE_TYPE { get; set; }
        /// <summary>
        /// 我的 線上投稿 全部單號  for 電子書用
        /// </summary>
        public List<int> arrWRITING_NO;
        /// <summary>
        /// 我的 線上投稿 TOP 5 筆
        /// </summary>
        public List<ADDT01> ADDT01List;

        /// <summary>
        /// 我的 閱讀認證 全部單號  for 電子書用
        /// </summary>
        public List<int> arrAPPLY_NO;

        /// <summary>
        /// 我的 閱讀認證 TOP 5筆
        /// </summary>
        ///         /// <summary>
        /// 線上投稿.投稿數
        /// </summary>
        public Int32 WritingCount { get; set; }

        /// <summary>
        /// 線上投稿.推薦數
        /// </summary>
        public Int32 WritingShareCount { get; set; }

        /// <summary>
        /// 閱讀認證.投稿數
        /// </summary>
        public Int32 BookCount { get; set; }

        /// <summary>
        /// 閱讀認證.推薦數
        /// </summary>
        public Int32 BookShareCount { get; set; }
        /// <summary>
        /// 我的 閱讀認證 TOP 5筆
        /// </summary>
        public List<ADDT06> ADDT06List;
        public Nullable<int> SumCash { get; set; }
    }
}