﻿@model ADDI12YoutubeViewViewModel
@{
    bool IsAdmin = false;
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    if (user != null)
    {
        IsAdmin = (user.USER_TYPE == UserType.Admin || user.USER_TYPE == UserType.Teacher);
    }
    string ISCARD = "false";
}

@if (TempData["ISCARD"] != null)
{

    ISCARD = TempData["ISCARD"].ToString();
}
@if (TempData["StatusMessage"] != null)
{
    string HtmlMsg = TempData["StatusMessage"].ToString().Replace("\r\n", "<br />");

    <div class="alert alert-dismissible alert-danger">
        <img src="~/Content/img/Warning.png" style="width:30px;height:30px" />
        <button class="close" type="button" data-dismiss="alert">×</button>
        <strong>@Html.Raw(HtmlMsg)</strong>
    </div>
}
else
{
    <div class="row">
        <div class="col-xs-1"></div>
        <div class="col-xs-10">
            <h5>
                <strong>主題:</strong>
                <b style="color:midnightblue">@Model.STAGE_NAME</b>
            </h5>
            <h5>
                <strong>瀏覽人數:</strong>
                <font color="red">@(Model.READ_COUNT) </font>

                @if (Model.IS_PREMIER == true)
                {
                    <strong>首播時段按讚數:  </strong>
                    <font color="red">+ @Model.PremierLikeCount  </font>

                    if (Model.STAGE_DATEE < DateTime.Now)
                    {
                        <strong>&nbsp;&nbsp;一般時段按讚數:</strong>
                        <font color="red">+ @Model.LikeCount </font>
                    }

                }
                else
                {
                    <strong>&nbsp;&nbsp;一般時段按讚數:</strong>
                    <font color="red">+ @Model.LikeCount </font>
                }
            </h5>
            @if (Model.BtnLike)
            {
                <a class="btn btn-primary btn-xs" role="button" onclick="Share_Save('@Model.STAGE_ID')">
                    <i class='glyphicon glyphicon-thumbs-up'></i> 按讚
                </a>
            }
            else
            {
                <a class="btn btn-primary btn-xs" role="button" disabled>
                    <i class='glyphicon glyphicon-thumbs-up'></i> 按讚
                </a>
            }
            <a class="btn btn-primary btn-xs" role="button" onclick="onYoutubePersonListOne('@Model.STAGE_ID')">
                表演人員
            </a>
            <a class="btn btn-primary btn-xs" role="button" onclick="onWinOpenYoutubeUrlLink()">
                分享網址
            </a>

            <a class="btn btn-primary btn-xs" role="button" onclick="onWinOpenYoutubeQRCODE()">
                分享 QR CODE
            </a>
            @if (IsAdmin)
            {
                <a class="btn btn-primary btn-xs" role="button" onclick="onShowCard()">
                    進行數位學生證打賞
                </a>
            }
            <a class="btn btn-primary btn-xs" role="button" onclick="onLinkPersonList('@Model.STAGE_ID')">
                按讚人員清單
            </a>
            @*@if (IsAdmin)
                {
                    <a class="btn btn-primary btn-xs" style="background-color:darkred" role="button" onclick='javascript:$("#MainDiv").css("visibility", "visible");'>
                        學生卡按讚
                    </a>
                }*@
        </div>
        <div class="col-xs-1"></div>
    </div>

    <div class="row">
        <div class="col-xs-1"></div>
        <div class="col-xs-10">
            <div id="MainDiv" style="visibility:hidden">
                @*style="visibility:hidden"*@
                <br />
                @if (IsAdmin)
                {
                    <div class="form-group">
                        <div class="input-group input-group-lg">
                            <span class="input-group-addon"><i class="fa fa-user"></i></span>
                            @Html.EditorFor(model => model.CARD_ID, new { htmlAttributes = new { @class = "form-control", @placeholder = "Username", @onKeyPress = "call(event,this);" } })
                        </div>
                        @Html.ValidationMessageFor(model => model.CARD_ID, "", new { @class = "text-danger" })
                    </div>
                    <div class="form-group text-center">
                        <h4>數位學生證感應按讚區</h4>
                        <br />
                        <div>
                            <img src="~/Content/img/Asset1.png" style="height:140px;padding-right:10px" />
                            <img src="~/Content/img/Asset2.png" style="height:140px;" />
                        </div>
                    </div>
                }
            </div>
        </div>
        <div class="col-xs-1"></div>
    </div>

    <script src="~/Scripts/buzz/buzz.min.js"></script>
    <!--資料顯示區END -->
    <script type="text/javascript">

    $(document).ready(function () {
        $("#@Html.IdFor(m=>m.CARD_ID)").val('');
        $("#@Html.IdFor(m=>m.CARD_ID)").focus();
        if ("@ISCARD" == "True")
        {
            onShowCard();
        }
    });

        @*function funExchangeCard() {

            Share_Save2('@Model.STAGE_ID', $("#@Html.IdFor(m=>m.CARD_ID)").val());  }*@
        function onShowCard() {
            $("#MainDiv").attr("style", "");
            document.getElementById('CARD_ID').focus();
        }
        function call(e, input) {
            var code = (e.keyCode ? e.keyCode : e.which);

            if (code == 13) // 13 是 Enter 按鍵的值
            {

                event.preventDefault();

                if ($('#@Html.IdFor(m=>m.CARD_ID)').val() != '') {
                    $('#@Html.IdFor(m=>m.CARD_ID)').prop('readonly', true);

                    Share_Save2('@Model.STAGE_ID', $("#@Html.IdFor(m=>m.CARD_ID)").val());

                    //setTimeout(function () {
                    //        //funExchangeCard()
                    //    });
                    $('#@Html.IdFor(m=>m.CARD_ID)').prop('readonly', false);
                }
            }
        }
    </script>

}