

@media screen and (min-width: 1190px)  {

    .dg-container nav{
    margin-left: -10%;
}

    .dg-container{
	width: 80%;
	height: 580px;
	position: relative;
     left:30%
    }

    .dg-wrapper{
	    max-width: 250px;
        width:100%;
        height:auto;
	    margin: 0 auto;
	    position: relative;
	    -webkit-transform-style: preserve-3d;
	    -moz-transform-style: preserve-3d;
	    -o-transform-style: preserve-3d;
	    -ms-transform-style: preserve-3d;
	    transform-style: preserve-3d;
	    -webkit-perspective: 600px;
	    -moz-perspective: 600px;
	    -o-perspective: 600px;
	    -ms-perspective: 600px;
	    perspective: 600px;
    }
    .dg-wrapper a{
	    max-width: 200px;
        width:100%;
        height:auto;
	    display: block;
	    position: absolute;
	    left: 0;
	    top: 0;
	    box-shadow: 0px 10px 20px rgba(0,0,0,0.3);
    }

     .rightItm{
         transform: translateX(250px) translateZ(-200px) rotateY(-45deg);
    }
  }

@media screen and (min-width: 850px)  and (max-width: 1189px)   {

        .dg-container nav{
    margin-left: -15%;
}

    .dg-container{
	width: 80%;
	height: 510px;
	position: relative;
     left:30%
    }

    .dg-wrapper{
	    max-width: 250px;
        width:100%;
        height:auto;
	    margin: 0 auto;
	    position: relative;
	    -webkit-transform-style: preserve-3d;
	    -moz-transform-style: preserve-3d;
	    -o-transform-style: preserve-3d;
	    -ms-transform-style: preserve-3d;
	    transform-style: preserve-3d;
	    -webkit-perspective: 450px;
	    -moz-perspective: 450px;
	    -o-perspective: 450px;
	    -ms-perspective: 450px;
	    perspective: 450px;
    }
    .dg-wrapper a{
	    max-width: 160px;
        width:100%;
        height:auto;
	    display: block;
	    position: absolute;
	    left: 0;
	    top: 0;
	    box-shadow: 0px 10px 20px rgba(0,0,0,0.3);
    }

     .rightItm{
         transform: translateX(250px) translateZ(-200px) rotateY(-45deg);
    }
  }


@media screen and (max-width: 849px)  {

            .dg-container nav{
    margin-left: -20%;
}
        .dg-container{
	width: 80%;
	height: 470px;
	position: relative;
     left:35%
    }

   .dg-wrapper{
	    max-width: 250px;
	    margin: 0 auto;
	    position: relative;
	    -webkit-transform-style: preserve-3d;
	    -moz-transform-style: preserve-3d;
	    -o-transform-style: preserve-3d;
	    -ms-transform-style: preserve-3d;
	    transform-style: preserve-3d;
	    -webkit-perspective: 300px;
	    -moz-perspective: 300px;
	    -o-perspective: 300px;
	    -ms-perspective: 300px;
	    perspective: 300px;
    }
    .dg-wrapper a{
	    max-width: 150px;
	    display: block;
	    position: absolute;
	    left: 0;
	    top: 0;
	    box-shadow: 0px 10px 20px rgba(0,0,0,0.3);
    }

    .rightItm{
         transform: translateX(180px) translateZ(-200px) rotateY(-45deg);
    }
  }

@media screen and (max-width: 480px)  {

            .dg-container nav{
    margin-left: -35%;
}

    .dg-container{
	width: 80%;
	height: 380px;
	position: relative;
    left:45%
    }


   .dg-wrapper{
	    max-width: 250px;
	    margin: 0 auto;
	    position: relative;
	    -webkit-transform-style: preserve-3d;
	    -moz-transform-style: preserve-3d;
	    -o-transform-style: preserve-3d;
	    -ms-transform-style: preserve-3d;
	    transform-style: preserve-3d;
	    -webkit-perspective: 250px;
	    -moz-perspective: 250px;
	    -o-perspective: 250px;
	    -ms-perspective: 250px;
	    perspective: 250px;
    }
    .dg-wrapper a{
	    max-width: 110px;
	    display: block;
	    position: absolute;
	    left: 0;
	    top: 0;
	    box-shadow: 0px 10px 20px rgba(0,0,0,0.3);
    }

    .rightItm{
         transform: translateX(120px) translateZ(-200px) rotateY(-45deg);
    }
  }

@media screen and (max-width: 380px)  {

            .dg-container nav{
    margin-left: -38%;
}

    .dg-container{
	width: 80%;
	height: 300px;
	position: relative;
    left:45%
    }


   .dg-wrapper{
	    max-width: 250px;
	    margin: 0 auto;
	    position: relative;
	    -webkit-transform-style: preserve-3d;
	    -moz-transform-style: preserve-3d;
	    -o-transform-style: preserve-3d;
	    -ms-transform-style: preserve-3d;
	    transform-style: preserve-3d;
	    -webkit-perspective: 150px;
	    -moz-perspective: 150px;
	    -o-perspective: 150px;
	    -ms-perspective: 150px;
	    perspective: 150px;
    }
    .dg-wrapper a{
	    max-width: 80px;
	    display: block;
	    position: absolute;
	    left: 0;
	    top: 0;
	    box-shadow: 0px 10px 20px rgba(0,0,0,0.3);
    }

    .rightItm{
         transform: translateX(120px) translateZ(-200px) rotateY(-45deg);
    }
  }


.dg-wrapper a.dg-transition{
	-webkit-transition: all 0.5s ease-in-out;
	-moz-transition: all 0.5s ease-in-out;
	-o-transition: all 0.5s ease-in-out;
	-ms-transition: all 0.5s ease-in-out;
	transition: all 0.5s ease-in-out;
}
.dg-wrapper a img{

	padding: 0px 0px 0px 5px;
}
.dg-wrapper a div{
	font-style: italic;
	text-align: center;
	line-height: 50px;
	text-shadow: 1px 1px 1px rgba(255,255,255,0.5);
	color: #333;
	width: 100%;
	bottom: -55px;
	display: none;
	position: absolute;
}
.dg-wrapper a.dg-center div{
	display: block;
}
.dg-container nav{
	width: 58px;
	position: absolute;
	z-index: 1000;
	bottom: 40px;
	left: 45%;
}

    .dg-container nav span {
        text-indent: -9000px;
        float: left;
        cursor: pointer;
        width: 24px;
        height: 25px;
        opacity: 0.8;
        background: transparent  url('../img/Galleryarrows.png')no-repeat top left;
        /*background-image: url('../img/Galleryarrows.png');*/
    }
.dg-container nav span:hover{
	opacity: 1;
}
.dg-container nav span.dg-next{
	background-position: top right;
	margin-left: 10px;
}