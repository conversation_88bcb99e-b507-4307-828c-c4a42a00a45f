﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'find', 'de-ch', {
	find: 'Suchen',
	findOptions: 'Suchoptionen',
	findWhat: 'Suche nach:',
	matchCase: 'Gross-/Kleinschreibung beachten',
	matchCyclic: 'Zyklische Suche',
	matchWord: 'Nur ganze Worte suchen',
	notFoundMsg: 'Der angegebene Text wurde nicht gefunden.',
	replace: 'Ersetzen',
	replaceAll: 'Alle ersetzen',
	replaceSuccessMsg: '%1 Vorkommen ersetzt.',
	replaceWith: 'Ersetze mit:',
	title: 'Suchen und Ersetzen'
} );
