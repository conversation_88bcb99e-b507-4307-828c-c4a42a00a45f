﻿// JavaScript for ADDI05 Answer page - 有獎徵答答題頁面
$(document).ready(function() {
    // 有獎徵答答題模組
    const answerModule = {
        init: function() {
            this.bindEvents();
            this.setupGlobalFunctions();
            this.handleImages();
            this.enhanceUserExperience();
            this.addFormValidation();
        },

        bindEvents: function() {
            // 綁定導航按鈕事件
            $('a[onclick*="onGo"]').on('click', this.handleNavigation.bind(this));

            // 綁定表單提交事件
            $('form[name="form1"]').on('submit', this.handleFormSubmit.bind(this));

            // 綁定單選按鈕變更事件
            $('input[type="radio"]').on('change', this.handleAnswerChange.bind(this));
        },

        setupGlobalFunctions: function() {
            // 設置全局函數以保持向後兼容
            window.onGo = this.onGo.bind(this);
        },

        handleNavigation: function(event) {
            event.preventDefault();

            try {
                const $button = $(event.target);
                const onclick = $button.attr('onclick');

                if (onclick) {
                    // 解析onclick中的參數
                    const match = onclick.match(/onGo\('([^']*)'\)/);
                    if (match) {
                        const action = match[1];
                        this.onGo(action);
                    }
                }
            } catch (error) {
                console.error('導航處理時發生錯誤:', error);
                this.showMessage('導航時發生錯誤，請稍後再試', 'error');
            }
        },

        onGo: function(actionVal) {
            try {
                const form = document.form1;
                if (!form) {
                    console.error('找不到表單元素');
                    this.showMessage('系統錯誤：找不到表單元素', 'error');
                    return;
                }

                console.log('導航到:', actionVal);

                // 根據動作設置表單action
                if (actionVal === "Index") {
                    form.action = window.ADDI05_ANSWER_URLS.index;
                } else if (actionVal === "detail") {
                    form.action = window.ADDI05_ANSWER_URLS.detail;
                } else if (actionVal === "Answer") {
                    // 檢查是否為提交答案
                    if (this.validateAnswers()) {
                        form.action = window.ADDI05_ANSWER_URLS.answer;
                    } else {
                        return; // 驗證失敗，不提交
                    }
                }

                // 顯示載入狀態
                this.showLoadingState(true);

                form.submit();
            } catch (error) {
                console.error('導航時發生錯誤:', error);
                this.showLoadingState(false);
                this.showMessage('導航時發生錯誤，請稍後再試', 'error');
            }
        },

        handleImages: function() {
            try {
                $(".p-context img").each(function() {
                    const $img = $(this);
                    let thisSrc = $img.attr("src");

                    if (!thisSrc) return;

                    // 處理圖片URL
                    if (thisSrc.indexOf("http") === 0) {
                        // 已經是完整URL
                        $img.attr("href", thisSrc);
                    } else if (thisSrc.indexOf("data:image") === 0) {
                        // Base64圖片
                        $img.attr("href", thisSrc);
                    } else {
                        // 相對路徑，需要加上系統URL
                        const fullUrl = window.ADDI05_ANSWER_URLS.sysUrl + thisSrc;
                        $img.attr("href", fullUrl);
                    }
                });

                // 初始化colorbox
                if (typeof $.colorbox === 'function') {
                    $(".p-context img").colorbox({
                        photo: true,
                        maxWidth: '90%',
                        maxHeight: '90%'
                    });
                }
            } catch (error) {
                console.error('處理圖片時發生錯誤:', error);
            }
        },

        validateAnswers: function() {
            try {
                const $radioGroups = {};
                let hasUnanswered = false;

                // 收集所有單選按鈕組
                $('input[type="radio"]').each(function() {
                    const name = $(this).attr('name');
                    if (!$radioGroups[name]) {
                        $radioGroups[name] = [];
                    }
                    $radioGroups[name].push(this);
                });

                // 檢查每個問題是否已回答
                Object.keys($radioGroups).forEach(groupName => {
                    const $group = $($radioGroups[groupName]);
                    const isAnswered = $group.is(':checked');

                    if (!isAnswered) {
                        hasUnanswered = true;
                        // 高亮未回答的問題
                        $group.closest('.p-context').addClass('unanswered-question');
                    } else {
                        $group.closest('.p-context').removeClass('unanswered-question');
                    }
                });

                if (hasUnanswered) {
                    this.showMessage('請回答所有問題後再提交', 'warning');

                    // 滾動到第一個未回答的問題
                    const $firstUnanswered = $('.unanswered-question').first();
                    if ($firstUnanswered.length > 0) {
                        $('html, body').animate({
                            scrollTop: $firstUnanswered.offset().top - 100
                        }, 500);
                    }

                    return false;
                }

                return true;
            } catch (error) {
                console.error('驗證答案時發生錯誤:', error);
                return true; // 發生錯誤時允許提交
            }
        },

        handleAnswerChange: function(event) {
            try {
                const $radio = $(event.target);
                const $question = $radio.closest('.p-context');

                // 移除未回答標記
                $question.removeClass('unanswered-question');

                // 添加已回答標記
                $question.addClass('answered-question');

                // 記錄答案變更
                console.log('答案變更:', {
                    question: $radio.attr('name'),
                    answer: $radio.val()
                });

                // 更新進度
                this.updateProgress();
            } catch (error) {
                console.error('處理答案變更時發生錯誤:', error);
            }
        },

        updateProgress: function() {
            try {
                const totalQuestions = $('input[type="radio"]').attr('name') ?
                    [...new Set($('input[type="radio"]').map(function() { return $(this).attr('name'); }).get())].length : 0;
                const answeredQuestions = $('.answered-question').length;

                if (totalQuestions > 0) {
                    const progress = Math.round((answeredQuestions / totalQuestions) * 100);

                    // 更新或創建進度條
                    let $progressBar = $('.answer-progress');
                    if ($progressBar.length === 0) {
                        $progressBar = $(`
                            <div class="answer-progress" style="position: fixed; top: 0; left: 0; right: 0; z-index: 1000; background: #f8f9fa; padding: 5px; border-bottom: 1px solid #dee2e6;">
                                <div class="progress" style="height: 10px; margin: 0;">
                                    <div class="progress-bar" role="progressbar" style="width: 0%; background-color: #28a745;"></div>
                                </div>
                                <small class="progress-text">答題進度：0%</small>
                            </div>
                        `);
                        $('body').prepend($progressBar);
                        $('body').css('padding-top', '60px');
                    }

                    $progressBar.find('.progress-bar').css('width', progress + '%');
                    $progressBar.find('.progress-text').text(`答題進度：${progress}% (${answeredQuestions}/${totalQuestions})`);
                }
            } catch (error) {
                console.error('更新進度時發生錯誤:', error);
            }
        },

        handleFormSubmit: function(event) {
            try {
                // 記錄表單提交
                console.log('表單提交');

                // 顯示載入狀態
                this.showLoadingState(true);

                return true;
            } catch (error) {
                console.error('表單提交時發生錯誤:', error);
                event.preventDefault();
                this.showLoadingState(false);
                this.showMessage('提交時發生錯誤，請稍後再試', 'error');
                return false;
            }
        },

        enhanceUserExperience: function() {
            // 添加鍵盤支援
            $(document).on('keydown', function(e) {
                // 數字鍵1-4選擇答案
                if (e.key >= '1' && e.key <= '4') {
                    const $focusedQuestion = $('.p-context:hover, .p-context:focus-within').first();
                    if ($focusedQuestion.length > 0) {
                        const $radio = $focusedQuestion.find(`input[value="${e.key}"]`);
                        if ($radio.length > 0) {
                            $radio.prop('checked', true).trigger('change');
                        }
                    }
                }

                // Enter鍵提交
                if (e.key === 'Enter' && e.ctrlKey) {
                    e.preventDefault();
                    $('a:contains("送出")').click();
                }
            });

            // 添加問題hover效果
            $('.p-context').on('mouseenter', function() {
                $(this).addClass('question-hover');
            }).on('mouseleave', function() {
                $(this).removeClass('question-hover');
            });

            // 添加單選按鈕樣式增強
            $('input[type="radio"]').each(function() {
                const $radio = $(this);
                const $label = $radio.closest('label, div');

                $label.on('click', function() {
                    $label.addClass('radio-selected');
                    $radio.siblings('input[type="radio"]').closest('label, div').removeClass('radio-selected');
                });
            });
        },

        addFormValidation: function() {
            // 添加實時驗證樣式
            const style = `
                <style>
                .unanswered-question {
                    border-left: 4px solid #dc3545;
                    padding-left: 10px;
                    background-color: #fff5f5;
                    animation: shake 0.5s ease-in-out;
                }

                .answered-question {
                    border-left: 4px solid #28a745;
                    padding-left: 10px;
                }

                .question-hover {
                    background-color: #f8f9fa;
                    border-radius: 5px;
                    transition: all 0.2s ease;
                }

                .radio-selected {
                    background-color: #e3f2fd;
                    border-radius: 3px;
                    font-weight: bold;
                }

                @keyframes shake {
                    0%, 100% { transform: translateX(0); }
                    25% { transform: translateX(-5px); }
                    75% { transform: translateX(5px); }
                }

                .btn:disabled {
                    opacity: 0.6;
                    cursor: not-allowed;
                }
                </style>
            `;

            $('head').append(style);
        },

        showLoadingState: function(isLoading) {
            const $buttons = $('.btn');

            if (isLoading) {
                $buttons.prop('disabled', true);
                $buttons.filter(':contains("送出")').text('提交中...');
            } else {
                $buttons.prop('disabled', false);
                $buttons.filter(':contains("提交中")').text('送出');
            }
        },

        showMessage: function(message, type = 'info') {
            // 移除現有訊息
            $('.alert-message').remove();

            // 添加新訊息
            const alertClass = type === 'success' ? 'alert-success' :
                              type === 'error' ? 'alert-danger' :
                              type === 'warning' ? 'alert-warning' : 'alert-info';

            const messageHtml = `
                <div class="alert ${alertClass} alert-message" style="position: fixed; top: 70px; left: 50%; transform: translateX(-50%); z-index: 1001; min-width: 300px;">
                    <button type="button" class="close" data-dismiss="alert">&times;</button>
                    ${message}
                </div>
            `;

            $('body').append(messageHtml);

            // 3秒後自動隱藏
            setTimeout(() => {
                $('.alert-message').fadeOut();
            }, 3000);
        }
    };

    // 初始化模組
    answerModule.init();

    // 設置全局錯誤處理
    window.addEventListener('error', function(e) {
        console.error('頁面錯誤:', e);
    });
});