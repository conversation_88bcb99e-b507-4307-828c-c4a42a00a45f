// JavaScript for ADDI05 Answer page - 有獎徵答答題頁面
$(document).ready(function() {
    // 有獎徵答答題模組
    const answerModule = {
        init: function() {
            this.bindEvents();
            this.setupGlobalFunctions();
            this.handleImages();
        },

        bindEvents: function() {
            // 綁定導航按鈕事件
            $('a[onclick*="onGo"]').on('click', this.handleNavigation.bind(this));

            // 綁定表單提交事件
            $('form[name="form1"]').on('submit', this.handleFormSubmit.bind(this));

            // 綁定單選按鈕變更事件
            $('input[type="radio"]').on('change', this.handleAnswerChange.bind(this));
        },

        setupGlobalFunctions: function() {
            // 設置全局函數以保持向後兼容
            window.onGo = this.onGo.bind(this);
        },

        handleNavigation: function(event) {
            event.preventDefault();

            try {
                const $button = $(event.target);
                const onclick = $button.attr('onclick');

                if (onclick) {
                    // 解析onclick中的參數
                    const match = onclick.match(/onGo\('([^']*)'\)/);
                    if (match) {
                        const action = match[1];
                        this.onGo(action);
                    }
                }
            } catch (error) {
                console.error('導航處理時發生錯誤:', error);
                this.showMessage('導航時發生錯誤，請稍後再試', 'error');
            }
        },

        onGo: function(actionVal) {
            try {
                const form = document.form1;
                if (!form) {
                    console.error('找不到表單元素');
                    this.showMessage('系統錯誤：找不到表單元素', 'error');
                    return;
                }

                console.log('導航到:', actionVal);

                // 根據動作設置表單action
                if (actionVal === "Index") {
                    form.action = window.ADDI05_ANSWER_URLS.index;
                } else if (actionVal === "detail") {
                    form.action = window.ADDI05_ANSWER_URLS.detail;
                } else if (actionVal === "Answer") {
                    // 檢查是否為提交答案
                    if (this.validateAnswers()) {
                        form.action = window.ADDI05_ANSWER_URLS.answer;
                    } else {
                        return; // 驗證失敗，不提交
                    }
                }

                form.submit();
            } catch (error) {
                console.error('導航時發生錯誤:', error);
                this.showLoadingState(false);
                this.showMessage('導航時發生錯誤，請稍後再試', 'error');
            }
        },

        handleImages: function() {
            try {
                $(".p-context img").each(function() {
                    const $img = $(this);
                    let thisSrc = $img.attr("src");

                    if (!thisSrc) return;

                    // 處理圖片URL
                    if (thisSrc.indexOf("http") === 0) {
                        // 已經是完整URL
                        $img.attr("href", thisSrc);
                    } else if (thisSrc.indexOf("data:image") === 0) {
                        // Base64圖片
                        $img.attr("href", thisSrc);
                    } else {
                        // 相對路徑，需要加上系統URL
                        const fullUrl = window.ADDI05_ANSWER_URLS.sysUrl + thisSrc;
                        $img.attr("href", fullUrl);
                    }
                });

                // 初始化colorbox
                if (typeof $.colorbox === 'function') {
                    $(".p-context img").colorbox({
                        photo: true,
                        maxWidth: '90%',
                        maxHeight: '90%'
                    });
                }
            } catch (error) {
                console.error('處理圖片時發生錯誤:', error);
            }
        },

        validateAnswers: function() {
            try {
                const $radioGroups = {};
                let hasUnanswered = false;

                // 收集所有單選按鈕組
                $('input[type="radio"]').each(function() {
                    const name = $(this).attr('name');
                    if (!$radioGroups[name]) {
                        $radioGroups[name] = [];
                    }
                    $radioGroups[name].push(this);
                });

                // 檢查每個問題是否已回答
                Object.keys($radioGroups).forEach(groupName => {
                    const $group = $($radioGroups[groupName]);
                    const isAnswered = $group.is(':checked');

                    if (!isAnswered) {
                        hasUnanswered = true;
                    }
                });

                if (hasUnanswered) {
                    alert('請回答所有問題後再提交');
                    return false;
                }

                return true;
            } catch (error) {
                console.error('驗證答案時發生錯誤:', error);
                return true; // 發生錯誤時允許提交
            }
        },

        handleAnswerChange: function(event) {
            try {
                const $radio = $(event.target);

                // 記錄答案變更
                console.log('答案變更:', {
                    question: $radio.attr('name'),
                    answer: $radio.val()
                });
            } catch (error) {
                console.error('處理答案變更時發生錯誤:', error);
            }
        },



        handleFormSubmit: function(event) {
            try {
                // 記錄表單提交
                console.log('表單提交');

                // 顯示載入狀態
                this.showLoadingState(true);

                return true;
            } catch (error) {
                console.error('表單提交時發生錯誤:', error);
                event.preventDefault();
                this.showLoadingState(false);
                this.showMessage('提交時發生錯誤，請稍後再試', 'error');
                return false;
            }
        },



        showMessage: function(message, type = 'info') {
            // 使用簡單的alert，不改變頁面外觀
            alert(message);
        }
    };

    // 初始化模組
    answerModule.init();

    // 設置全局錯誤處理
    window.addEventListener('error', function(e) {
        console.error('頁面錯誤:', e);
    });
});