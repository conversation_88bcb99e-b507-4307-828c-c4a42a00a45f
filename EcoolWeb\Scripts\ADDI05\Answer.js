// JavaScript for ADDI05 Answer page - 有獎徵答答題頁面
$(document).ready(function() {
    // 有獎徵答答題模組
    const answerModule = {
        init: function() {
            this.bindEvents();
            this.setupGlobalFunctions();
            this.handleImages();
        },

        bindEvents: function() {
            // 綁定導航按鈕事件
            $('a[onclick*="onGo"]').on('click', this.handleNavigation.bind(this));

            // 綁定表單提交事件
            $('form[name="form1"]').on('submit', this.handleFormSubmit.bind(this));

            // 綁定單選按鈕變更事件
            $('input[type="radio"]').on('change', this.handleAnswerChange.bind(this));
        },

        setupGlobalFunctions: function() {
            // 設置全局函數以保持向後兼容
            window.onGo = this.onGo.bind(this);
        },

        handleNavigation: function(event) {
            event.preventDefault();

            try {
                const $button = $(event.target);
                const onclick = $button.attr('onclick');

                if (onclick) {
                    // 解析onclick中的參數
                    const match = onclick.match(/onGo\('([^']*)'\)/);
                    if (match) {
                        const action = match[1];
                        this.onGo(action);
                    }
                }
            } catch (error) {
                console.error('導航處理時發生錯誤:', error);
                this.showMessage('導航時發生錯誤，請稍後再試', 'error');
            }
        },

        onGo: function(actionVal) {
            try {
                const form = document.form1;
                if (!form) {
                    console.error('找不到表單元素');
                    this.showMessage('系統錯誤：找不到表單元素', 'error');
                    return;
                }

                console.log('導航到:', actionVal);

                // 根據動作設置表單action
                if (actionVal === "Index") {
                    form.action = window.ADDI05_ANSWER_URLS.index;
                } else if (actionVal === "detail") {
                    form.action = window.ADDI05_ANSWER_URLS.detail;
                } else if (actionVal === "Answer") {
                    // 檢查是否為提交答案
                    if (this.validateAnswers()) {
                        form.action = window.ADDI05_ANSWER_URLS.answer;
                    } else {
                        return; // 驗證失敗，不提交
                    }
                }

                form.submit();
            } catch (error) {
                console.error('導航時發生錯誤:', error);
                this.showLoadingState(false);
                this.showMessage('導航時發生錯誤，請稍後再試', 'error');
            }
        },

        handleImages: function() {
            try {
                $(".p-context img").each(function() {
                    const $img = $(this);
                    let thisSrc = $img.attr("src");

                    if (!thisSrc) return;

                    // 處理圖片URL
                    if (thisSrc.indexOf("http") === 0) {
                        // 已經是完整URL
                        $img.attr("href", thisSrc);
                    } else if (thisSrc.indexOf("data:image") === 0) {
                        // Base64圖片
                        $img.attr("href", thisSrc);
                    } else {
                        // 相對路徑，需要加上系統URL
                        const fullUrl = window.ADDI05_ANSWER_URLS.sysUrl + thisSrc;
                        $img.attr("href", fullUrl);
                    }
                });

                // 初始化colorbox
                if (typeof $.colorbox === 'function') {
                    $(".p-context img").colorbox({
                        photo: true,
                        maxWidth: '90%',
                        maxHeight: '90%'
                    });
                }
            } catch (error) {
                console.error('處理圖片時發生錯誤:', error);
            }
        },

        validateAnswers: function() {
            try {
                const $radioGroups = {};
                let hasUnanswered = false;
                let unansweredQuestions = [];

                // 先清除所有錯誤訊息
                $('.error-message').remove();

                // 收集所有單選按鈕組
                $('input[type="radio"]').each(function() {
                    const name = $(this).attr('name');
                    if (!$radioGroups[name]) {
                        $radioGroups[name] = [];
                    }
                    $radioGroups[name].push(this);
                });

                // 檢查每個問題是否已回答
                Object.keys($radioGroups).forEach(groupName => {
                    const $group = $($radioGroups[groupName]);
                    const isAnswered = $group.is(':checked');

                    if (!isAnswered) {
                        hasUnanswered = true;
                        unansweredQuestions.push(groupName);

                        // 在未回答的問題下方顯示錯誤訊息
                        const $questionContainer = $group.first().closest('.p-context');
                        if ($questionContainer.length > 0) {
                            const errorHtml = '<div class="error-message" style="color: red; font-size: 14px; margin-top: 5px;">請選擇一個答案</div>';
                            $questionContainer.append(errorHtml);
                        }
                    }
                });

                if (hasUnanswered) {
                    // 滾動到第一個未回答的問題
                    const $firstErrorMessage = $('.error-message').first();
                    if ($firstErrorMessage.length > 0) {
                        $('html, body').animate({
                            scrollTop: $firstErrorMessage.closest('.p-context').offset().top - 100
                        }, 500);
                    }

                    return false;
                }

                return true;
            } catch (error) {
                console.error('驗證答案時發生錯誤:', error);
                return true; // 發生錯誤時允許提交
            }
        },

        handleAnswerChange: function(event) {
            try {
                const $radio = $(event.target);
                const $questionContainer = $radio.closest('.p-context');

                // 移除該問題的錯誤訊息
                $questionContainer.find('.error-message').remove();

                // 記錄答案變更
                console.log('答案變更:', {
                    question: $radio.attr('name'),
                    answer: $radio.val()
                });
            } catch (error) {
                console.error('處理答案變更時發生錯誤:', error);
            }
        },



        handleFormSubmit: function(event) {
            try {
                // 記錄表單提交
                console.log('表單提交');

                // 顯示載入狀態
                this.showLoadingState(true);

                return true;
            } catch (error) {
                console.error('表單提交時發生錯誤:', error);
                event.preventDefault();
                this.showLoadingState(false);
                this.showMessage('提交時發生錯誤，請稍後再試', 'error');
                return false;
            }
        },



        showMessage: function(message, type = 'info') {
            // 移除現有的一般訊息
            $('.general-message').remove();

            // 在頁面頂部顯示一般訊息
            const messageColor = type === 'error' ? 'red' : type === 'warning' ? 'orange' : 'blue';
            const messageHtml = `<div class="general-message" style="color: ${messageColor}; background-color: #f9f9f9; padding: 10px; margin: 10px 0; border: 1px solid ${messageColor}; border-radius: 4px;">${message}</div>`;

            // 在表單頂部插入訊息
            $('form[name="form1"]').prepend(messageHtml);

            // 滾動到訊息位置
            $('html, body').animate({
                scrollTop: $('.general-message').offset().top - 50
            }, 300);

            // 5秒後自動移除
            setTimeout(() => {
                $('.general-message').fadeOut(function() {
                    $(this).remove();
                });
            }, 5000);
        }
    };

    // 初始化模組
    answerModule.init();

    // 設置全局錯誤處理
    window.addEventListener('error', function(e) {
        console.error('頁面錯誤:', e);
    });
});