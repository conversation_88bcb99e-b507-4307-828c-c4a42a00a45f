﻿$(document).ready(function () {
    const AnswerModule = {
        init: function () {
            this.bindEvents();
            this.setupGlobalFunctions();
            this.handleimg();
        },
        bindEvents: function () { },

        setupGlobalFunctions: function () {
            window.onGO = this.onGO.bind(this);

        },
        onGO: function (ActionVal) {
            if (ActionVal == "Index") {
                form1.action = '@Url.Action("Index", (string)ViewBag.BRE_NO)';
            }
            else if (ActionVal == "detail") {
                form1.action = '@Url.Action("detail", (string)ViewBag.BRE_NO)';
            }
            else if (ActionVal == "Answer") {
                form1.action = '@Url.Action("Answer", (string)ViewBag.BRE_NO)';
            }
            form1.submit();
        },
        handleimg: function () {
            $(".p-context img").each(function () {

                var ThisSrc = $(this).attr("src")

                if (ThisSrc.indexOf("http") != 0) {
                    $(this).attr("href", ThisSrc);
                }
                else if (ThisSrc.indexOf("data:image") != 0) {
                    $(this).attr("href", ThisSrc);
                }
                else {
                    $(this).attr("href", SysUrl + ThisSrc);
                }
            });

            $(".p-context img").colorbox({ photo: true });
        }

    }
    AnswerModule.init();
    // 設置全局錯誤處理
    window.addEventListener('error', function (e) {
        console.error('頁面錯誤:', e);
    });
});