﻿using com.ecool.service;
using ECOOL_APP.com.ecool.Models.DTO;
using ECOOL_APP.com.ecool.Models.entity;
using ECOOL_APP.EF;
using EcoolWeb.CustomAttribute;
using EcoolWeb.Models;
using EcoolWeb.ViewModels;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace EcoolWeb.Controllers
{
    [SessionExpire]
    public class ZZZI12Controller : Controller
    {
        private string Bre_NO = "ZZZI12";
        private string ErrorMsg = string.Empty;
        PeopleViewModelServuce PeopleDb = new PeopleViewModelServuce();
        ZZZI12ViewModelService Db = new ZZZI12ViewModelService();
        ECOOL_APP.UserProfile user;


        // GET
        [HttpGet]
        [CheckPermission] //檢查權限
        public ActionResult index()
        {
            user = UserProfileHelper.Get();
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "帳號權限維護";

            //角色清單
            ViewBag.HRMT24ListItems = HRMT24Service.USP_HRMT24_QUERY("", "", "6", "2",null, user);
            //ViewBag.HRMT24ListItems = (from a in TemPuHRMT24
            //                            where a.ROLE_ID != "6"
            //                            select a).ToList();

            ViewBag.Q_ROLE_ID = user.RoleID_Default;
            ViewBag.MY_RoleID = user.RoleID_Default;

            //學校清單
            List<SelectListItem> SCHOOL_NO_ITEMS = BDMT01Service.GetSCHOOL_NO("", "", user);
            ViewBag.SchoolNoItems = SCHOOL_NO_ITEMS;
            ViewBag.Q_SCHOOL_NO = user.SCHOOL_NO;

            //人員清單
            List<PeopleViewModel> PeopleList = PeopleDb.USP_PeopleViewModel_QUERY(user.SCHOOL_NO, "", "", user.RoleID_Default);
            ViewBag.PeopleList = PeopleList;

            //記錄選取的帳號 預設自已
            ViewBag.SCHOOL_NO = user.SCHOOL_NO;
            ViewBag.USER_NO = user.USER_NO;

            ViewBag.MY_SCHOOL_NO = user.SCHOOL_NO;
            ViewBag.MY_USER_NO = user.USER_NO;

      

            //此帳號權限清單
            var model = Db.USP_ZZZI12ViewModel_QUERY(user.SCHOOL_NO, user.USER_NO, user.RoleID_Default, user);


            //取得最大角色名稱
            string MaxROLEName = HRMT24Service.USP_HRMT24_QUERY("", "", "", "",0, user).First().ROLE_NAME;
            ViewBag.MaxROLEName = MaxROLEName;


            TempData["StatusMessage"] =  "【勾選/取消】核取方塊後立即【增加權限/取消權限】<br/>";

            if (ViewBag.MY_RoleID == "0" && ViewBag.Q_ROLE_ID == "0")
            {
                TempData["StatusMessage"] = TempData["StatusMessage"] +"您是" + ViewBag.MaxROLEName + "，擁有最大權限。<br/>";
            }
            else if (ViewBag.SCHOOL_NO == ViewBag.MY_SCHOOL_NO && ViewBag.USER_NO == ViewBag.MY_USER_NO)
            {
                TempData["StatusMessage"] = TempData["StatusMessage"] +"自已無法異動自已權限。需要比你更高權限者，才能異動你的權限。<br/>";
            }


            return View(model);
        }


        [HttpPost]
        [CheckPermission] //檢查權限
        public ActionResult index(string SCHOOL_NO, string USER_NO, string Q_SCHOOL_NO, string Q_USER_NO, string Q_NAME, string Q_ROLE_ID)
        {
            user = UserProfileHelper.Get();
            ViewBag.BRE_NO = Bre_NO;


            //角色清單
            ViewBag.HRMT24ListItems = HRMT24Service.USP_HRMT24_QUERY("", "", "6", "2",null, user);
    
            ViewBag.Q_ROLE_ID = Q_ROLE_ID;
            ViewBag.MY_RoleID = user.RoleID_Default;

            //學校清單
            List<SelectListItem> SCHOOL_NO_ITEMS = BDMT01Service.GetSCHOOL_NO("", "", user);
            ViewBag.SchoolNoItems = SCHOOL_NO_ITEMS;
            ViewBag.Q_SCHOOL_NO = user.SCHOOL_NO;

            //人員清單
            List<PeopleViewModel> PeopleList = PeopleDb.USP_PeopleViewModel_QUERY(Q_SCHOOL_NO, Q_USER_NO, Q_NAME, Q_ROLE_ID);
            ViewBag.PeopleList = PeopleList;


            var date = from u in PeopleList
                       where u.USER_NO == USER_NO && u.SCHOOL_NO == SCHOOL_NO
                       select u;

            if (date.Count()==0)
            {
                if (PeopleList.Count>0)
                {
                    //記錄選取的帳號 預設第一筆
                    SCHOOL_NO = PeopleList.First().SCHOOL_NO;
                    USER_NO = PeopleList.First().USER_NO; 
                }
            }


            ViewBag.SCHOOL_NO = SCHOOL_NO;
            ViewBag.USER_NO = USER_NO;

            ViewBag.MY_SCHOOL_NO = user.SCHOOL_NO;
            ViewBag.MY_USER_NO = user.USER_NO;

            //此帳號權限清單
            var model = Db.USP_ZZZI12ViewModel_QUERY(SCHOOL_NO, USER_NO, Q_ROLE_ID, user);

            //取得最大角色名稱
            string MaxROLEName = HRMT24Service.USP_HRMT24_QUERY("0", "", "", "",null, user).First().ROLE_NAME;
            ViewBag.MaxROLEName = MaxROLEName;

            if (ViewBag.MY_RoleID == "0" && ViewBag.Q_ROLE_ID == "0")
            {
                ViewBag.StatusMessage = "您是" + ViewBag.MaxROLEName + "，擁有最大權限。";
            }
            else if (ViewBag.SCHOOL_NO == ViewBag.MY_SCHOOL_NO && ViewBag.USER_NO == ViewBag.MY_USER_NO)
            {
                ViewBag.StatusMessage = "自已無法異動自已權限。需要比你更高權限者，才能異動你的權限。";
            }

            return View(model);
        }

        /// <summary>
        /// 批次新增
        /// </summary>
        /// <returns></returns>
#if !DEBUG
        [CheckPermission(CheckACTION_ID = "BatchIndex")] //檢查權限
#endif
        public ActionResult BatchIndex()
        {
            user = UserProfileHelper.Get();
            //學校清單
            List<SelectListItem> SCHOOL_NO_ITEMS = BDMT01Service.GetSCHOOL_NO("", "",  user);
            ViewBag.SchoolNoItems = SCHOOL_NO_ITEMS;
            ViewBag.Q_SCHOOL_NO = user.SCHOOL_NO;

            //角色清單
            ViewBag.HRMT24ListItems = HRMT24Service.USP_HRMT24_QUERY("", "", "6", "2", null, user);
            ViewBag.Q_ROLE_ID = user.RoleID_Default;

            ViewBag.MyRoleID = user.RoleID_Default;

            return View();
        }
#if !DEBUG
        [CheckPermission(CheckACTION_ID = "BatchIndex")] //檢查權限
#endif
        public JsonResult GetPeopleList(string schoolNo, string roleID)
        {
            List<PeopleViewModel> PeopleList = PeopleDb.USP_PeopleViewModel_QUERY(schoolNo, "", "", roleID);
            return Json(PeopleList, JsonRequestBehavior.AllowGet);
        }

#if !DEBUG
        [CheckPermission(CheckACTION_ID = "BatchIndex")] //檢查權限
#endif
        [HttpPost]
        public JsonResult GetPeopleAuthList(ZZZI12GetPeopleAuthListViewModel model)
        {
            string schoolNo = model.SchoolNo;
            string roleID = model.RoleID;
            string[] user_Nos = model.User_Nos;
            user = UserProfileHelper.Get();
            //此帳號權限清單
            var q = Db.USP_ZZZI12ViewModel_QUERY(schoolNo, user_Nos[0], roleID, user);
            return Json(q);
        }

#if !DEBUG
        [CheckPermission(CheckACTION_ID = "BatchIndex")] //檢查權限
#endif
        [HttpPost]
        public JsonResult BatchSave(ZZZI12BatchAuthInsertViewModel model)
        {
            ECOOL_DEVEntities db = new ECOOL_DEVEntities();
            string Success = string.Empty;
            user = UserProfileHelper.Get();
            string UseYN = PermissionService.GetPermission_Use_YN(model.BRE_NO, model.ACTION_ID, user.SCHOOL_NO, user.USER_NO);
            if (UseYN == "N")
            {
                ErrorMsg = "您無權異動資料";
                Success = "false";
            }

            if (Success != "false")
            {
                try
                {
                    foreach(var userno in model.USER_NO)
                    {
                        // 已有權限就跳過不用再新增 沒有權限不用再刪除
                        bool find = db.ZZT03.Any(z => z.USER_NO == userno
                             && z.SCHOOL_NO == model.SCHOOL_NO
                             && z.BRE_NO == model.BRE_NO
                             && z.ACTION_ID == model.ACTION_ID);

                        uZZT03 Date = new uZZT03();

                        Date.SCHOOL_NO = model.SCHOOL_NO;
                        Date.USER_NO = userno;
                        Date.BRE_NO = model.BRE_NO;
                        Date.ACTION_ID = model.ACTION_ID;
                        Date.CRE_DATE = DateTime.Now;
                        Date.CRE_PERSON = user.USER_KEY;

                        if (model.Checked && !find)
                        {
                            Db.AddDate(Date);
                        }
                        if(!model.Checked && find)
                        {
                            Db.DelDate(Date);
                        }

                        if (Db.ErrorMsg != null && Db.ErrorMsg != string.Empty)
                        {
                            Success = "false";
                            ErrorMsg = ErrorMsg + Db.ErrorMsg;
                        }
                        else
                        {
                            Success = "true";
                        }
                    }
                }
                catch (Exception ex)
                {
                    Success = "false";
                    ErrorMsg = ErrorMsg + ex;
                }
                finally
                {
                    db.Dispose();
                }
            }

            var data = "{ \"Success\" : \"" + Success + "\" , \"Error\" : \"" + ErrorMsg + "\" }";
            return Json(data);
        }


       [HttpPost]
       [CheckPermission(ResultType = "Json")] //檢查權限
       public JsonResult Save(string SCHOOL_NO, string USER_NO, string BRE_NO, string ACTION_ID, string Checked)
       {

           string Success = string.Empty;
           user = UserProfileHelper.Get();

           string UseYN = PermissionService.GetPermission_Use_YN(BRE_NO, ACTION_ID, user.SCHOOL_NO, user.USER_NO);
           if (UseYN == "N")
           {
               ErrorMsg = "您無權異動資料";
               Success = "false";
           }

           if (Success != "false")
           {
               try
               {
                   uZZT03 Date = new uZZT03();

                   Date.SCHOOL_NO = SCHOOL_NO;
                   Date.USER_NO = USER_NO;
                   Date.BRE_NO = BRE_NO;
                   Date.ACTION_ID = ACTION_ID;
                   Date.CRE_DATE = DateTime.Now;
                   Date.CRE_PERSON = user.USER_KEY;

                   if (Checked == "true")
                   {
                       Db.AddDate(Date);
                   }
                   else
                   {
                       Db.DelDate(Date);
                   }


                   if (Db.ErrorMsg != null && Db.ErrorMsg != string.Empty)
                   {
                       Success = "false";
                       ErrorMsg = ErrorMsg + Db.ErrorMsg;
                   }
                   else
                   {
                       Success = "true";
                   }

               }
               catch (Exception ex)
               {
                   Success = "false";
                   ErrorMsg = ErrorMsg + ex;
               }
           }

 
           var data = "{ \"Success\" : \"" + Success + "\" , \"Error\" : \"" + ErrorMsg + "\" }";
           return Json(data, JsonRequestBehavior.AllowGet);
       }



    }
}