﻿/**
 * @license Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
 * For licensing, see LICENSE.md or http://ckeditor.com/license
 */

CKEDITOR.plugins.setLang( 'specialchar', 'si', {
	euro: 'යුරෝ සලකුණ',
	lsquo: 'වමේ තනි උපුටා දක්වීම ',
	rsquo: 'දකුණේ තනි උපුටා දක්වීම ',
	ldquo: 'වමේ දිත්ව  උපුටා දක්වීම ',
	rdquo: 'දකුණේ දිත්ව  උපුටා දක්වීම ',
	ndash: 'En dash', // MISSING
	mdash: 'Em dash', // MISSING
	iexcl: 'යටිකුරු හර්ෂදී ',
	cent: 'Cent sign', // MISSING
	pound: 'Pound sign', // MISSING
	curren: 'මුල්‍යමය ',
	yen: 'යෙන් ',
	brvbar: 'Broken bar', // MISSING
	sect: 'තෙරේම් ',
	uml: 'Diaeresis', // MISSING
	copy: 'පිටපත් අයිතිය ',
	ordf: 'දර්ශකය',
	laquo: 'Left-pointing double angle quotation mark', // MISSING
	not: 'සලකුණක් නොවේ',
	reg: 'සලකුණක් ලියාපදිංචි කිරීම',
	macr: 'මුද්‍රිත ',
	deg: 'සලකුණේ ',
	sup2: 'උඩු ලකුණු දෙක',
	sup3: 'Superscript three', // MISSING
	acute: 'Acute accent', // MISSING
	micro: 'Micro sign', // MISSING
	para: 'Pilcrow sign', // MISSING
	middot: 'Middle dot', // MISSING
	cedil: 'Cedilla', // MISSING
	sup1: 'Superscript one', // MISSING
	ordm: 'Masculine ordinal indicator', // MISSING
	raquo: 'Right-pointing double angle quotation mark', // MISSING
	frac14: 'Vulgar fraction one quarter', // MISSING
	frac12: 'Vulgar fraction one half', // MISSING
	frac34: 'Vulgar fraction three quarters', // MISSING
	iquest: 'Inverted question mark', // MISSING
	Agrave: 'Latin capital letter A with grave accent', // MISSING
	Aacute: 'Latin capital letter A with acute accent', // MISSING
	Acirc: 'Latin capital letter A with circumflex', // MISSING
	Atilde: 'Latin capital letter A with tilde', // MISSING
	Auml: 'Latin capital letter A with diaeresis', // MISSING
	Aring: 'Latin capital letter A with ring above', // MISSING
	AElig: 'Latin Capital letter Æ', // MISSING
	Ccedil: 'Latin capital letter C with cedilla', // MISSING
	Egrave: 'Latin capital letter E with grave accent', // MISSING
	Eacute: 'Latin capital letter E with acute accent', // MISSING
	Ecirc: 'Latin capital letter E with circumflex', // MISSING
	Euml: 'Latin capital letter E with diaeresis', // MISSING
	Igrave: 'Latin capital letter I with grave accent', // MISSING
	Iacute: 'Latin capital letter I with acute accent', // MISSING
	Icirc: 'Latin capital letter I with circumflex', // MISSING
	Iuml: 'Latin capital letter I with diaeresis', // MISSING
	ETH: 'Latin capital letter Eth', // MISSING
	Ntilde: 'Latin capital letter N with tilde', // MISSING
	Ograve: 'Latin capital letter O with grave accent', // MISSING
	Oacute: 'Latin capital letter O with acute accent', // MISSING
	Ocirc: 'Latin capital letter O with circumflex', // MISSING
	Otilde: 'Latin capital letter O with tilde', // MISSING
	Ouml: 'Latin capital letter O with diaeresis', // MISSING
	times: 'Multiplication sign', // MISSING
	Oslash: 'Latin capital letter O with stroke', // MISSING
	Ugrave: 'Latin capital letter U with grave accent', // MISSING
	Uacute: 'Latin capital letter U with acute accent', // MISSING
	Ucirc: 'Latin capital letter U with circumflex', // MISSING
	Uuml: 'Latin capital letter U with diaeresis', // MISSING
	Yacute: 'Latin capital letter Y with acute accent', // MISSING
	THORN: 'Latin capital letter Thorn', // MISSING
	szlig: 'Latin small letter sharp s', // MISSING
	agrave: 'Latin small letter a with grave accent', // MISSING
	aacute: 'Latin small letter a with acute accent', // MISSING
	acirc: 'Latin small letter a with circumflex', // MISSING
	atilde: 'Latin small letter a with tilde', // MISSING
	auml: 'Latin small letter a with diaeresis', // MISSING
	aring: 'Latin small letter a with ring above', // MISSING
	aelig: 'Latin small letter æ', // MISSING
	ccedil: 'Latin small letter c with cedilla', // MISSING
	egrave: 'Latin small letter e with grave accent', // MISSING
	eacute: 'Latin small letter e with acute accent', // MISSING
	ecirc: 'Latin small letter e with circumflex', // MISSING
	euml: 'Latin small letter e with diaeresis', // MISSING
	igrave: 'Latin small letter i with grave accent', // MISSING
	iacute: 'Latin small letter i with acute accent', // MISSING
	icirc: 'Latin small letter i with circumflex', // MISSING
	iuml: 'Latin small letter i with diaeresis', // MISSING
	eth: 'Latin small letter eth', // MISSING
	ntilde: 'Latin small letter n with tilde', // MISSING
	ograve: 'Latin small letter o with grave accent', // MISSING
	oacute: 'Latin small letter o with acute accent', // MISSING
	ocirc: 'Latin small letter o with circumflex', // MISSING
	otilde: 'Latin small letter o with tilde', // MISSING
	ouml: 'Latin small letter o with diaeresis', // MISSING
	divide: 'Division sign', // MISSING
	oslash: 'Latin small letter o with stroke', // MISSING
	ugrave: 'Latin small letter u with grave accent', // MISSING
	uacute: 'Latin small letter u with acute accent', // MISSING
	ucirc: 'Latin small letter u with circumflex', // MISSING
	uuml: 'Latin small letter u with diaeresis', // MISSING
	yacute: 'Latin small letter y with acute accent', // MISSING
	thorn: 'Latin small letter thorn', // MISSING
	yuml: 'Latin small letter y with diaeresis', // MISSING
	OElig: 'Latin capital ligature OE', // MISSING
	oelig: 'Latin small ligature oe', // MISSING
	'372': 'Latin capital letter W with circumflex', // MISSING
	'374': 'Latin capital letter Y with circumflex', // MISSING
	'373': 'Latin small letter w with circumflex', // MISSING
	'375': 'Latin small letter y with circumflex', // MISSING
	sbquo: 'Single low-9 quotation mark', // MISSING
	'8219': 'Single high-reversed-9 quotation mark', // MISSING
	bdquo: 'Double low-9 quotation mark', // MISSING
	hellip: 'Horizontal ellipsis', // MISSING
	trade: 'Trade mark sign', // MISSING
	'9658': 'Black right-pointing pointer', // MISSING
	bull: 'Bullet', // MISSING
	rarr: 'Rightwards arrow', // MISSING
	rArr: 'Rightwards double arrow', // MISSING
	hArr: 'Left right double arrow', // MISSING
	diams: 'Black diamond suit', // MISSING
	asymp: 'Almost equal to' // MISSING
} );
