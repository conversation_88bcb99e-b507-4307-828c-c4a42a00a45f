﻿@using System.Collections;
@using ECOOL_APP.com.ecool.Models.entity;
@{
    ViewBag.Title = "批次閱讀認證-檔案上傳";
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    if (string.IsNullOrWhiteSpace(UUID) == false)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }

    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    string BookID = ViewBag.BookID;
    bool BookStatus = (ViewBag.BookStatus == "ALL") ? true : false;
    string ReadrppBookCheckF = (BookID == null) ? "selected" : "";
    string ReadrppBookCheckT = (BookID != null) ? "selected" : "";
    IEnumerable<SelectListItem> BookList = Model.Select(x => new SelectListItem { Text = x.BOOK_ID + " " + x.BOOK_NAME, Value = x.BOOK_ID, Selected = x.BOOK_ID == BookID });

    var Book_Info = from Book in this.Model
                    where Book.BOOK_ID == BookID && Book.SCHOOL_NO == user.SCHOOL_NO
                    select Book;
    string Book_Name = (Book_Info.FirstOrDefault() != null) ? Book_Info.FirstOrDefault().BOOK_NAME : string.Empty;
}
@model IEnumerable<ADDT03>

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@using (Html.BeginForm(null, null, FormMethod.Post, new { name = "contentForm", id = "contentForm" }))
{
    <br />
    <img src="~/Content/img/web-bar2-revise-20.png" style="width:100%" class="img-responsive " alt="Responsive image" />
    <div class="Div-EZ-reader">
        <div class="form-horizontal">
            <div class="form-group">
                @Html.Label("是否為閱讀護照書籍", htmlAttributes: new { @class = "control-label-left label_dt col-md-4 col-sm-4 col-lg-4" })
                <div class="col-md-8 col-sm-8 col-lg-8">
                    <select id="ddlReadrppBook" name="ddlReadrppBook" class="form-control input-sm">
                        <option value="N" @ReadrppBookCheckF>否</option>
                        <option value="Y" @ReadrppBookCheckT>是</option>
                    </select>
                </div>
            </div>

            <div class="form-group">
                @Html.Label("閱讀書名", htmlAttributes: new { @class = "control-label-left label_dt col-md-4 col-sm-4 col-lg-4" })
                <div class="col-md-8 col-sm-8 col-lg-8">
                    <samp class="dd">
                        <input type="text" id="txtBOOK_NAME" name="txtBOOK_NAME" value="@Book_Name" class="form-control input-sm" />
                        @Html.DropDownList("BOOK_ID", BookList.OrderBy(a => a.Text), "選擇書名", new { @class = "form-control input-sm" })
                        @Html.Hidden("BOOK_NAME", Book_Name)
                        <input type="hidden" id="hidBOOK_NAME" name="hidBOOK_NAME">
                    </samp>
                </div>
            </div>
            <div class="form-group">
                @Html.Label("檔名上傳類型", htmlAttributes: new { @class = "control-label-left label_dt col-md-4 col-sm-4 col-lg-4" })
                <div class="col-md-8 col-sm-8 col-lg-8">
                    <input type="radio" id="radUserNo" name="radFileType" value="U" checked />學號
                    <input type="radio" id="radSeatNo" name="radFileType" value="S" />座號
                </div>
            </div>
            <div class="form-group" id="TRsClassNo" style="display: none;">
                @Html.Label("班級", htmlAttributes: new { @class = "control-label-left label_dt col-md-4 col-sm-4 col-lg-4" })
                <div class="col-md-8 col-sm-8 col-lg-8">
                    @Html.DropDownList("Class_No", (IEnumerable<SelectListItem>)ViewBag.ClassNoItem, new { @class = "form-control input-sm" })
                </div>
            </div>
            <div class="form-group">
                <span class="control-label-left label_dt col-md-4 col-sm-4 col-lg-4">上傳檔案</span>
                <div class="col-md-8 col-sm-8 col-lg-8">
                    <input type="file" name="file" id="file" accept="zip/*" class="form-control" style="height:45px" />
                    <label class="text-info">
                        <br />
                        1.批次上傳學生心得(請用ZIP檔)<br />
                        2.圖檔格式限制jpg，並需小於6MB<br />
                        3.壓縮檔限用ZIP檔，並需小於100MB<br />
                        4.檔名上傳類型選【學號】，檔名一定是要【學號】<br />
                        5.檔名上傳類型選【座號】，檔名一定是要【座號】<br />
                    </label>
                </div>
            </div>
            <div class="text-center">

                <button id="btnSend" class="btn btn-default">
                    確定送出
                </button>
            </div>
        </div>
    </div>

}

@section css {
    <style>
        .form-group.focused {
            background-color: #f8f9fa;
            border-radius: 4px;
            padding: 10px;
            transition: all 0.3s ease;
        }

        .is-invalid {
            border-color: #dc3545 !important;
            box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
        }

        .file-feedback {
            margin-top: 5px;
            font-size: 0.875em;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .form-control:focus {
            border-color: #80bdff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        .Div-EZ-reader {
            transition: all 0.3s ease;
        }
    </style>
}

@section Scripts {
    <script nonce="cmlvaw">
        // 設置全局 URL 配置
        window.ADDI02_UPLOAD_URLS = {
            setUpload: "SetUpload",
            checkPending: "../ADDT02/ADDTList_CheckPending"
        };
    </script>
    <script src="~/Scripts/ADDI02/upload.js" nonce="cmlvaw"></script>
}