/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Neo-Euler/Size5/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.NeoEulerMathJax_Size5={directory:"Size5/Regular",family:"NeoEulerMathJax_Size5",testString:"\u00A0\u2225\u27E8\u27E9\uE000\uE001\uE002\uE003\uE004\uE005\uE006\uE007\uE008\uE009\uE00A",32:[0,0,333,0,0],160:[0,0,333,0,0],8741:[3098,208,403,86,316],10216:[2730,228,803,137,694],10217:[2730,228,859,109,666],57344:[3098,208,213,86,126],57345:[138,167,213,86,126],57346:[3098,208,403,86,316],57347:[138,167,403,86,316],57348:[635,-595,150,0,150],57349:[-65,105,150,0,150],57350:[1820,0,1055,111,742],57351:[572,-2,1055,702,742],57352:[583,2,1055,702,1076],57353:[827,-276,1799,0,1809],57354:[828,-718,600,-10,610],57355:[828,-277,1799,-10,1799],57356:[280,271,1799,0,1809],57357:[-160,271,600,-10,610],57358:[281,270,1799,-10,1799],57359:[758,-436,450,-24,460],57360:[758,-660,300,-10,310],57361:[983,-661,1800,-10,1810],57362:[758,-436,450,-10,474],57363:[120,202,450,-24,460],57364:[-106,202,300,-10,310],57365:[-106,428,1800,-10,1810],57366:[120,202,450,-10,474]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"NeoEulerMathJax_Size5"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Size5/Regular/Main.js"]);
