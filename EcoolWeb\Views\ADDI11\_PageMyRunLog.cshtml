﻿@model ADDI11MyRunLogViewModel
@{
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

}
<script src="~/Scripts/Highcharts-4.0.1/js/highcharts.js"></script>
<script src="~/Scripts/Highcharts-4.0.1/js/modules/exporting.js"></script>

@Html.HiddenFor(m => m.WhereIsColorboxForUser)
@Html.HiddenFor(m => m.WhereSCHOOL_NO)
@Html.HiddenFor(m => m.WhereCLASS_NO)
@Html.HiddenFor(m => m.WhereUSER_NO)
@Html.HiddenFor(m => m.OrdercColumn)
@Html.HiddenFor(m => m.Page)

@if (user.USER_TYPE == UserType.Teacher)
{
    <div class="row">
        <div class="col-xs-12">
            我的跑步累積里程 @((Convert.ToDouble(Model.MyRunRank.RUN_TOTAL_METER ?? 0) / 1000.00).ToString("#,0.000")) 公里。
        </div>
    </div>
}
else if (user.USER_TYPE == UserType.Student)
{
    <div class="row">
        <div class="col-xs-12">
            我的跑步累積里程 @((Convert.ToDouble(Model.MyRunRank.RUN_TOTAL_METER ?? 0) / 1000.00).ToString("#,0.000")) 公里，目前位居全校第 @(Model.MyRunRank.Ranking) 名。
        </div>
        <div class="col-xs-12">
            全班的跑步平均累積里程 @((Convert.ToDouble(Model.MyClassRun.AVG_Total_M ?? 0) / 1000.00).ToString("#,0.000")) 公里。
        </div>
    </div>
}

<img src="~/Content/img/web-bar-Run.png" class="img-responsive App_hide" alt="Responsive image" />
<div class="table-responsive">
    <div class="text-center" id="tbData">
        <table class="table-ecool table-92Per table-hover table-ecool-reader">
            <thead>
                <tr>
                    <th>@Html.DisplayNameFor(m => m.ListData.First().NAME)</th>
                    <th>@Html.DisplayNameFor(m => m.ListData.First().RUN_DATE)</th>
                    <th>@Html.DisplayNameFor(m => m.ListData.First().LAP)</th>
                    <th>@Html.DisplayNameFor(m => m.ListData.First().LAP_M)</th>
                    <th>@Html.DisplayNameFor(m => m.ListData.First().Total_M)</th>
                </tr>
            </thead>
            <tbody>
                @foreach (var item in Model.ListData)
                {

                    <tr>
                        <td style="text-align: center">@Html.DisplayFor(modelItem => item.NAME)</td>
                        <td style="text-align: center">@Html.DisplayFor(modelItem => item.RUN_DATE)</td>
                        <td style="text-align: center">@Html.DisplayFor(modelItem => item.LAP)</td>
                        <td style="text-align: center">@Html.DisplayFor(modelItem => item.LAP_M)</td>
                        <td>@((Convert.ToDouble(item.Total_M) / 1000.00).ToString("#,0.000"))</td>
                    </tr>
                }
            </tbody>
        </table>
    </div>
</div>
@if (Model.ListData.Count() == 0)
{
    <div class="text-center">
        <h3>無任何跑步記錄</h3>
    </div>

}
else
{
    <div style="height:25px"></div>
    <div class="col-sm-12">
        @if (Model.MyRunColumnChart != null)
        {
            @Model.MyRunColumnChart
        }
    </div>
}