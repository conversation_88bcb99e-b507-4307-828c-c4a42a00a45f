﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'flash', 'sr-latn', {
	access: 'Script Access', // MISSING
	accessAlways: 'Always', // MISSING
	accessNever: 'Never', // MISSING
	accessSameDomain: 'Same domain', // MISSING
	alignAbsBottom: 'Abs dole',
	alignAbsMiddle: 'Abs sredina',
	alignBaseline: 'Bazno',
	alignTextTop: 'Vrh teksta',
	bgcolor: '<PERSON><PERSON> pozadine',
	chkFull: 'Allow Fullscreen', // MISSING
	chkLoop: 'Ponavljaj',
	chkMenu: 'Uklju<PERSON>i fleš meni',
	chkPlay: 'Automatski start',
	flashvars: 'Variables for Flash', // MISSING
	hSpace: 'HSpace',
	properties: 'Osobine fleša',
	propertiesTab: 'Properties', // MISSING
	quality: 'Quality', // MISSING
	qualityAutoHigh: 'Auto High', // MISSING
	qualityAutoLow: 'Auto Low', // MISSING
	qualityBest: 'Best', // MISSING
	qualityHigh: 'High', // MISSING
	qualityLow: 'Low', // MISSING
	qualityMedium: 'Medium', // MISSING
	scale: 'Skaliraj',
	scaleAll: 'Prikaži sve',
	scaleFit: 'Popuni površinu',
	scaleNoBorder: 'Bez ivice',
	title: 'Osobine fleša',
	vSpace: 'VSpace',
	validateHSpace: 'HSpace must be a number.', // MISSING
	validateSrc: 'Unesite URL linka',
	validateVSpace: 'VSpace must be a number.', // MISSING
	windowMode: 'Window mode', // MISSING
	windowModeOpaque: 'Opaque', // MISSING
	windowModeTransparent: 'Transparent', // MISSING
	windowModeWindow: 'Window' // MISSING
} );
