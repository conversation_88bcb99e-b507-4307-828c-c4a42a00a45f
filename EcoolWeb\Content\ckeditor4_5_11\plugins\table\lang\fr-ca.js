﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'table', 'fr-ca', {
	border: 'Taille de la bordure',
	caption: 'Titre',
	cell: {
		menu: 'Cellule',
		insertBefore: 'Insérer une cellule avant',
		insertAfter: 'Insérer une cellule après',
		deleteCell: 'Supprimer des cellules',
		merge: 'Fusionner les cellules',
		mergeRight: 'Fusionner à droite',
		mergeDown: 'Fusionner en bas',
		splitHorizontal: 'Scinder la cellule horizontalement',
		splitVertical: 'Scinder la cellule verticalement',
		title: 'Propriétés de la cellule',
		cellType: 'Type de cellule',
		rowSpan: 'Fusion de lignes',
		colSpan: 'Fusion de colonnes',
		wordWrap: 'Retour à la ligne',
		hAlign: 'Alignement horizontal',
		vAlign: 'Alignement vertical',
		alignBaseline: 'Bas du texte',
		bgColor: 'Couleur d\'arrière plan',
		borderColor: 'Couleur de bordure',
		data: 'Données',
		header: 'En-tête',
		yes: 'Oui',
		no: 'Non',
		invalidWidth: 'La largeur de cellule doit être un nombre.',
		invalidHeight: 'La hauteur de cellule doit être un nombre.',
		invalidRowSpan: 'La fusion de lignes doit être un nombre entier.',
		invalidColSpan: 'La fusion de colonnes doit être un nombre entier.',
		chooseColor: 'Sélectionner'
	},
	cellPad: 'Marge interne des cellules',
	cellSpace: 'Espacement des cellules',
	column: {
		menu: 'Colonne',
		insertBefore: 'Insérer une colonne avant',
		insertAfter: 'Insérer une colonne après',
		deleteColumn: 'Supprimer des colonnes'
	},
	columns: 'Colonnes',
	deleteTable: 'Supprimer le tableau',
	headers: 'En-têtes',
	headersBoth: 'Les deux.',
	headersColumn: 'Première colonne',
	headersNone: 'Aucun',
	headersRow: 'Première ligne',
	invalidBorder: 'La taille de bordure doit être un nombre.',
	invalidCellPadding: 'La marge interne des cellules doit être un nombre positif.',
	invalidCellSpacing: 'L\'espacement des cellules doit être un nombre positif.',
	invalidCols: 'Le nombre de colonnes doit être supérieur à 0.',
	invalidHeight: 'La hauteur du tableau doit être un nombre.',
	invalidRows: 'Le nombre de lignes doit être supérieur à 0.',
	invalidWidth: 'La largeur du tableau doit être un nombre.',
	menu: 'Propriétés du tableau',
	row: {
		menu: 'Ligne',
		insertBefore: 'Insérer une ligne avant',
		insertAfter: 'Insérer une ligne après',
		deleteRow: 'Supprimer des lignes'
	},
	rows: 'Lignes',
	summary: 'Résumé',
	title: 'Propriétés du tableau',
	toolbar: 'Tableau',
	widthPc: 'pourcentage',
	widthPx: 'pixels',
	widthUnit: 'unité de largeur'
} );
