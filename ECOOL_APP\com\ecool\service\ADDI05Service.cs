﻿using ECOOL_APP.com.ecool.Models.entity;
using ECOOL_APP.com.ecool.Models.DTO;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Data.SqlClient;
using ECOOL_APP;
using System.Transactions;
using Dapper;
using ECOOL_APP.EF;
using DotNet.Highcharts;
using DotNet.Highcharts.Enums;
using DotNet.Highcharts.Helpers;
using DotNet.Highcharts.Options;

namespace com.ecool.service
{
    public class ADDI05Service
    {
        public string ErrorMsg;

        public List<ADDI05IndexViewModel> GetListData(string SCHOOL_NO, string SearchContents, string OrderByName, string SyntaxName
            , byte? DialogType, byte? whereShowDataVal, int page, int pageSize, ref int Count, UserProfile user)
        {
            List<ADDI05IndexViewModel> list_data = new List<ADDI05IndexViewModel>();

            ADDI05IndexViewModel ReturnData = null;

            StringBuilder sb = new StringBuilder();
            DataTable dt;
            try
            {
                sb.Append("  SELECT A.DIALOG_ID ,A.SCHOOL_NO,A.SYEAR,A.SEMESTER,A.DIALOG_NAME,A.DIALOG_EXPRESS,A.DIALOG_SDATE,A.DIALOG_EDATE ");
                sb.Append("  ,A.USER_NO,A.NAME,A.SNAME,A.JOB_NAME,A.CHG_PERSON,A.CHG_DATE ");
                sb.Append("  ,MEMO=CASE WHEN A.STATUS='Z' Then '活動已提早結束' ");
                sb.Append("             WHEN CONVERT(nvarchar(10),GETDATE(),111)>CONVERT(nvarchar(10),A.DIALOG_EDATE,111)  THEN '活動已結束' ");
                sb.Append("             WHEN CONVERT(nvarchar(10),GETDATE(),111)<CONVERT(nvarchar(10), A.DIALOG_SDATE,111) THEN '活動未開始' ");
                sb.Append("             ELSE '' END ");
                sb.Append(",A.STATUS,A.DIALOG_TYPE");
                sb.Append("  FROM ADDT11 A  (NOLOCK)   WHERE 1=1");
                if (whereShowDataVal != (byte)ADDT11.ShowDateType.隱藏刪除)
                {
                    sb.Append(" AND A.STATUS IN ('R','Z')");
                }

                sb.AppendFormat(" AND (A.SCHOOL_NO = '{0}' or A.SCHOOL_NO = '{1}') ", SCHOOL_NO, SharedGlobal.ALL);

                if (HRMT24_ENUM.CheckQAdmin(user) == false)
                {
                    sb.AppendFormat(" and (isnull(A.ANSWER_PERSON_YN,'N')='N' or (isnull(A.ANSWER_PERSON_YN,'N')='Y') and (Select COUNT(*) from REFT01 r (nolock) where r.REF_KEY = a.DIALOG_ID and r.REF_TABLE = 'ADDT11' and r.SCHOOL_NO ='{0}' and r.USER_NO = '{1}')>0 or A.USER_NO='{1}')", user?.SCHOOL_NO, user?.USER_NO);
                }

                if (DialogType != null)
                {
                    sb.AppendFormat(" AND A.DIALOG_TYPE = {0} ", DialogType);
                }

                if (whereShowDataVal != null)
                {
                    if (whereShowDataVal == (byte)ADDT11.ShowDateType.實施中)
                    {
                        sb.AppendFormat(" AND CONVERT(nvarchar(10), A.DIALOG_SDATE,111)  <= CONVERT(nvarchar(10),GETDATE(),111) ");
                        sb.AppendFormat(" AND CONVERT(nvarchar(10), A.DIALOG_EDATE,111)  > CONVERT(nvarchar(10),GETDATE(),111)  ");
                        sb.AppendFormat(" AND A.STATUS='R'  ");
                    }
                    else if (whereShowDataVal == (byte)ADDT11.ShowDateType.已截止)
                    {
                        sb.AppendFormat(" AND (CONVERT(nvarchar(10), A.DIALOG_EDATE,111)  <= CONVERT(nvarchar(10),GETDATE(),111)  OR A.STATUS='Z')");
                    }
                    else if (whereShowDataVal == (byte)ADDT11.ShowDateType.尚未開始)
                    {
                        sb.AppendFormat(" AND CONVERT(nvarchar(10), A.DIALOG_SDATE,111) > CONVERT(nvarchar(10),GETDATE(),111) ");
                        sb.AppendFormat(" AND A.STATUS='R'  ");
                    }
                    else if (whereShowDataVal == (byte)ADDT11.ShowDateType.隱藏刪除)
                    {
                        //sb.AppendFormat(" AND CONVERT(nvarchar(10), A.DIALOG_SDATE,111) > CONVERT(nvarchar(10),GETDATE(),111) ");
                        sb.AppendFormat(" AND (A.STATUS='D' OR A.STATUS='H') ");
                    }
                    else //全部
                    {
                        sb.AppendFormat(" AND CONVERT(nvarchar(10), A.DIALOG_SDATE,111) <= CONVERT(nvarchar(10),GETDATE(),111) ");
                    }
                }

                if (SearchContents != string.Empty)
                {
                    sb.Append(" AND (");

                    sb.AppendFormat("  A.DIALOG_NAME LIKE '%{0}%' ", SearchContents.Trim());
                    sb.Append(" OR ");
                    sb.AppendFormat("  A.DIALOG_EXPRESS LIKE '%{0}%' ", SearchContents.Trim());
                    sb.Append(" OR ");
                    sb.AppendFormat("  A.NAME LIKE '%{0}%' ", SearchContents.Trim());
                    sb.Append(" OR ");
                    sb.AppendFormat("  A.SNAME LIKE '%{0}%' ", SearchContents.Trim());

                    int intDate;
                    if (int.TryParse(SearchContents, out intDate))
                    {
                        sb.Append(" OR ");
                        sb.AppendFormat("  A.SYEAR LIKE '%{0}%' ", SearchContents.Trim());
                    }

                    byte byteDate;
                    if (byte.TryParse(SearchContents, out byteDate))
                    {
                        sb.Append(" OR ");
                        sb.AppendFormat("  A.SEMESTER LIKE '%{0}%' ", SearchContents.Trim());
                    }

                    DateTime dtDate;
                    if (DateTime.TryParse(SearchContents, out dtDate))
                    {
                        sb.Append(" OR ");
                        sb.AppendFormat("  A.DIALOG_SDATE = '{0}' ", SearchContents.Trim());
                        sb.Append(" OR ");
                        sb.AppendFormat("  A.DIALOG_EDATE = '{0}' ", SearchContents.Trim());
                    }

                    sb.Append(" )");
                }

                string OrderBy = "A.DIALOG_SDATE DESC";

                if (OrderByName != string.Empty)
                {
                    OrderBy = "A." + OrderByName + " " + SyntaxName;
                }

                string ThisError = "";
                dt = new sqlConnection.sqlConnection().executeQueryBSqlDataReaderListPage(page, pageSize, sb.ToString(), sb.ToString(), OrderBy, ref Count, ref ThisError);
                if (dt != null)
                {
                    foreach (DataRow dr in dt.Rows)
                    {
                        ReturnData = new ADDI05IndexViewModel();
                        ReturnData.uADDT11 = new uADDT11();

                        ReturnData.uADDT11.DIALOG_ID = (dr["DIALOG_ID"] == DBNull.Value ? "" : (string)dr["DIALOG_ID"]);
                        ReturnData.uADDT11.SCHOOL_NO = (dr["SCHOOL_NO"] == DBNull.Value ? "" : (string)dr["SCHOOL_NO"]);
                        ReturnData.uADDT11.SYEAR = (dr["SYEAR"] == DBNull.Value ? (int?)null : (int)dr["SYEAR"]);
                        ReturnData.uADDT11.SEMESTER = (dr["SEMESTER"] == DBNull.Value ? (byte?)null : (byte)dr["SEMESTER"]);
                        ReturnData.uADDT11.DIALOG_NAME = (dr["DIALOG_NAME"] == DBNull.Value ? "" : (string)dr["DIALOG_NAME"]);
                        ReturnData.uADDT11.DIALOG_EXPRESS = (dr["DIALOG_EXPRESS"] == DBNull.Value ? "" : (string)dr["DIALOG_EXPRESS"]);
                        ReturnData.uADDT11.DIALOG_SDATE = (dr["DIALOG_SDATE"] == DBNull.Value ? (DateTime?)null : (DateTime)dr["DIALOG_SDATE"]);
                        ReturnData.uADDT11.DIALOG_EDATE = (dr["DIALOG_EDATE"] == DBNull.Value ? (DateTime?)null : (DateTime)dr["DIALOG_EDATE"]);
                        ReturnData.uADDT11.USER_NO = (dr["USER_NO"] == DBNull.Value ? "" : (string)dr["USER_NO"]);
                        ReturnData.uADDT11.NAME = (dr["NAME"] == DBNull.Value ? "" : (string)dr["NAME"]);
                        ReturnData.uADDT11.SNAME = (dr["SNAME"] == DBNull.Value ? "" : (string)dr["SNAME"]);
                        ReturnData.uADDT11.JOB_NAME = (dr["JOB_NAME"] == DBNull.Value ? "" : (string)dr["JOB_NAME"]);
                        ReturnData.uADDT11.CHG_PERSON = (dr["CHG_PERSON"] == DBNull.Value ? "" : (string)dr["CHG_PERSON"]);
                        ReturnData.uADDT11.CHG_DATE = (dr["CHG_DATE"] == DBNull.Value ? (DateTime?)null : (DateTime)dr["CHG_DATE"]);
                        ReturnData.uADDT11.STATUS = (dr["STATUS"] == DBNull.Value ? "" : (string)dr["STATUS"]);
                        ReturnData.MEMO = (dr["MEMO"] == DBNull.Value ? "" : (string)dr["MEMO"]);

                        ReturnData.uADDT11.DIALOG_TYPE = (dr["DIALOG_TYPE"] == DBNull.Value ? (byte?)null : (byte)dr["DIALOG_TYPE"]);

                        list_data.Add(ReturnData);
                    }
                }

                dt.Clear();
                dt.Dispose();
            }
            catch (Exception exception)
            {
                throw exception;
            }
            return list_data;
        }

        public ADDI05DetailViewModel GetMainData(string DIALOG_ID)
        {
            ADDI05DetailViewModel ReturnData = new ADDI05DetailViewModel();
            ReturnData.uADDT11 = new uADDT11();

            StringBuilder sb = new StringBuilder();
            try
            {
                sb.Append("  SELECT A.DIALOG_ID ,A.SCHOOL_NO,A.SYEAR,A.SEMESTER,A.DIALOG_NAME,A.DIALOG_EXPRESS,A.DIALOG_SDATE,A.DIALOG_EDATE ");
                sb.Append("  ,A.USER_NO,A.NAME,A.SNAME,A.JOB_NAME,A.CHG_PERSON,A.CHG_DATE,A.STATUS ");
                sb.Append("  ,ANSWER_COUNT,A.COPY_YN,A.ANSWER_PERSON_YN,A.RANDOM,A.CASH");
                sb.Append("  FROM ADDT11 A  (NOLOCK) ");
                sb.Append("  WHERE 1=1 ");
                //sb.Append("  AND A.STATUS IN ('R','Z')");
                sb.AppendFormat(" and  A.DIALOG_ID = '{0} '  ", DIALOG_ID);
                var conn = new sqlConnection.sqlConnection().getConnection4System();
                ReturnData.uADDT11 = conn.Query<uADDT11>(sb.ToString()).FirstOrDefault();

                if ((ReturnData.uADDT11.ANSWER_PERSON_YN ?? "N") == "Y")
                {
                    var Q_ANSWER_PERSON = conn.Query<string>(@"select CONTENT_TXT=case when a.BTN_TYPE='person'  then Replace(a.CONTENT_TXT,a.USER_NO,'') else a.CONTENT_TXT end
                                                                 from REFT01_Q a(NOLOCK)
                                                                 where a.REF_TABLE='ADDT11'
                                                                 and a.REF_KEY=@REF_KEY"
                                                                 , new { REF_KEY = ReturnData.uADDT11.DIALOG_ID }
                                                                 ).ToList();

                    ReturnData.uADDT11.ANSWER_PERSON = string.Join(",", Q_ANSWER_PERSON.ToArray());
                }

                sb.Clear();

                ReturnData.FILE = new List<ADDI05FILEViewModel>();

                sb.Append("  Select B.DIALOG_ID,B.FILE_NAME");
                sb.Append("  from ADDT11_FILE B (nolock) ");
                sb.Append("  Where 1=1 ");
                sb.AppendFormat(" AND B.DIALOG_ID= '{0}' ", DIALOG_ID);
                ReturnData.FILE = conn.Query<ADDI05FILEViewModel>(sb.ToString()).ToList();

                sb.Clear();
            }
            catch (Exception exception)
            {
                throw exception;
            }

            return ReturnData;
        }

        /// <summary>
        /// 查ADDT12
        /// </summary>
        /// <param name="DIALOG_ID"></param>
        /// <param name="OrderByType"></param>
        /// <param name="RANDOM"></param>
        /// <returns></returns>
        public List<uADDT12> GetQusListData(string DIALOG_ID, string OrderByType, bool? RANDOM)
        {
            List<uADDT12> list_data = new List<uADDT12>();

            uADDT12 ReturnData = null;

            StringBuilder sb = new StringBuilder();
            DataTable dt;
            try
            {
                sb.Append("  Select B.DIALOG_ID,B.Q_NUM,B.Q_TYPE,B.TRUE_ANS,B.Q_TEXT");
                sb.Append("  ,B.Q_ANS1, B.Q_ANS2, B.Q_ANS3,B.Q_ANS4");
                sb.Append("  from ADDT12 B (nolock) ");
                sb.Append("  Where 1=1 ");
                sb.AppendFormat(" AND B.DIALOG_ID= '{0}' ", DIALOG_ID);

                if (RANDOM ?? false)
                {
                    if (OrderByType == "default")
                    {
                        sb.Append("  ORDER BY B.Q_TYPE,B.Q_NUM");
                    }
                    else if (OrderByType == "NEWID")
                    {
                        sb.Append("  Order by B.Q_TYPE,NEWID()");
                    }
                }
                else
                {
                    sb.Append("  ORDER BY B.Q_TYPE,B.Q_NUM");
                }

                dt = new sqlConnection.sqlConnection().executeQueryByDataTableList(sb.ToString());
                if (dt != null)
                {
                    foreach (DataRow dr in dt.Rows)
                    {
                        ReturnData = new uADDT12();

                        ReturnData.DIALOG_ID = (dr["DIALOG_ID"] == DBNull.Value ? "" : (string)dr["DIALOG_ID"]);
                        ReturnData.Q_NUM = (dr["Q_NUM"] == DBNull.Value ? (int?)null : (int)dr["Q_NUM"]);
                        ReturnData.Q_TYPE = (dr["Q_TYPE"] == DBNull.Value ? (int?)null : (int)dr["Q_TYPE"]);

                        if (ReturnData.Q_TYPE != null)
                        {
                            ReturnData.Q_TYPE_NAME = uADDT12.ParserQ_TYPE_NAME((int)dr["Q_TYPE"]);
                        }

                        ReturnData.TRUE_ANS = (dr["TRUE_ANS"] == DBNull.Value ? "" : (string)dr["TRUE_ANS"]);
                        ReturnData.Q_TEXT = (dr["Q_TEXT"] == DBNull.Value ? "" : (string)dr["Q_TEXT"]);
                        ReturnData.Q_ANS1 = (dr["Q_ANS1"] == DBNull.Value ? "" : (string)dr["Q_ANS1"]);
                        ReturnData.Q_ANS2 = (dr["Q_ANS2"] == DBNull.Value ? "" : (string)dr["Q_ANS2"]);
                        ReturnData.Q_ANS3 = (dr["Q_ANS3"] == DBNull.Value ? "" : (string)dr["Q_ANS3"]);
                        ReturnData.Q_ANS4 = (dr["Q_ANS4"] == DBNull.Value ? "" : (string)dr["Q_ANS4"]);
                        list_data.Add(ReturnData);
                    }
                }

                dt.Clear();
                dt.Dispose();
            }
            catch (Exception exception)
            {
                throw exception;
            }
            var items = Enumerable.Range(0, 50).OrderBy(d => Guid.NewGuid());

            return list_data;
        }

        /// <summary>
        /// 所有學生回答名單
        /// </summary>
        public List<uADDT13> AnswerListData(string dialogID, string orderByName, string searchContents)
        {
            using (ECOOL_DEVEntities db = new ECOOL_DEVEntities())
            {
                var answerList = db.ADDT13_HIS.Where(ah => ah.DIALOG_ID == dialogID)
                    .Join(db.BDMT01, ah => ah.SCHOOL_NO, bd => bd.SCHOOL_NO,
                    (ah, bd) => new
                    {
                        CLASS_NO = ah.CLASS_NO,
                        DIALOG_ID = ah.DIALOG_ID,
                        NAME = ah.NAME,
                        SCHOOL_NO = ah.SCHOOL_NO,
                        SEAT_NO = ah.SEAT_NO,
                        SEMESTER = ah.SEMESTER,
                        SHORT_NAME = bd.SHORT_NAME,
                        SNAME = ah.SNAME,
                        SYEAR = ah.SYEAR,
                        USER_NO = ah.USER_NO,
                        CRE_DATE = ah.CRE_DATE,
                        CRE_PERSON = ah.CRE_PERSON,
                    }).
                    GroupBy(g => new
                    {
                        g.CLASS_NO,
                        g.DIALOG_ID,
                        g.NAME,
                        g.SCHOOL_NO,
                        g.SEAT_NO,
                        g.SEMESTER,
                        g.SHORT_NAME,
                        g.SNAME,
                        g.SYEAR,
                        g.USER_NO
                    })
                        .Select(g => new uADDT13
                        {
                            CLASS_NO = g.Key.CLASS_NO,
                            DIALOG_ID = g.Key.DIALOG_ID,
                            NAME = g.Key.NAME,
                            SCHOOL_NO = g.Key.SCHOOL_NO,
                            SEAT_NO = g.Key.SEAT_NO,
                            SEMESTER = g.Key.SEMESTER,
                            SHORT_NAME = g.Key.SHORT_NAME,
                            SNAME = g.Key.SNAME,
                            SYEAR = g.Key.SYEAR,
                            USER_NO = g.Key.USER_NO,
                            Count = g.Count(),
                            CRE_DATE = g.Min(d => d.CRE_DATE)
                        })
                        .AsEnumerable();

                if (!string.IsNullOrEmpty(searchContents))
                {
                    answerList = answerList.Where(a => a.NAME.Contains(searchContents)
                    || a.CLASS_NO == searchContents
                    || a.SEAT_NO == searchContents);
                }

                // 排序
                var propertyInfo = typeof(uADDT13).GetProperty(orderByName);
                answerList = answerList.OrderByDescending(x => propertyInfo.GetValue(x, null));

                return answerList.ToList();
            }
        }

        /// <summary>
        /// 每位學生的作答紀錄 (包含分數)
        /// </summary>
        public List<uADDT13_Each> AnswerListDataForEachUser(string dialogID, string user_no, string school_no, string orderByName)
        {
            List<uADDT13_Each> resultuADDT13Each = new List<uADDT13_Each>();
            using (ECOOL_DEVEntities db = new ECOOL_DEVEntities())
            {
                var answerList = db.ADDT13_HIS.Where(ah => ah.DIALOG_ID == dialogID)
                    .Join(db.BDMT01, ah => ah.SCHOOL_NO, bd => bd.SCHOOL_NO,
                    (ah, bd) => new uADDT13_Each
                    {
                        CLASS_NO = ah.CLASS_NO,
                        DIALOG_ID = ah.DIALOG_ID,
                        NAME = ah.NAME,
                        SCHOOL_NO = ah.SCHOOL_NO,
                        SEAT_NO = ah.SEAT_NO,
                        SEMESTER = ah.SEMESTER,
                        SHORT_NAME = bd.SHORT_NAME,
                        SNAME = ah.SNAME,
                        SYEAR = ah.SYEAR,
                        USER_NO = ah.USER_NO,
                        CRE_DATE = ah.CRE_DATE,
                        CRE_PERSON = ah.CRE_PERSON,
                        RIGHT_YN = ah.RIGHT_YN,
                        HIS_NO = ah.HIS_NO
                    }).Where(al => al.USER_NO == user_no
                    && al.SCHOOL_NO == school_no)
                    .AsEnumerable();

                List<ADDT13_HIS_D> eachTimeAns;
                int questionCount;
                int correctCount;
                int score;

                resultuADDT13Each = answerList.ToList();
                // 分數計算
                foreach (var item in resultuADDT13Each)
                {
                    eachTimeAns = db.ADDT13_HIS_D.Where(ahd => ahd.HIS_NO == item.HIS_NO).ToList();
                    questionCount = eachTimeAns.Count();
                    correctCount = eachTimeAns.Where(ahd => ahd.RIGHT_YN == "Y").Count();
                    score = (int)Math.Round(((double)correctCount / questionCount) * 100);
                    item.Grade = score;
                }
                // 排序
                var propertyInfo = typeof(uADDT13_Each).GetProperty(orderByName);
                resultuADDT13Each = resultuADDT13Each.OrderByDescending(x => propertyInfo.GetValue(x, null)).ToList();

                return resultuADDT13Each;
            }
        }

        public List<uADDT13> PassListData(string DIALOG_ID, int page, int pageSize, string PassOrderByName, string PassSearchContents, ref int Count,string PassOrderBy2Name)
        {
            List<uADDT13> list_data = new List<uADDT13>();

            uADDT13 ReturnData = null;

            StringBuilder sb = new StringBuilder();
            DataTable dt;
            try
            {
                ECOOL_DEVEntities context = new ECOOL_DEVEntities();
                var query = from a in context.ADDT13
                            join b in context.BDMT01 on a.SCHOOL_NO equals b.SCHOOL_NO
                            where a.DIALOG_ID == DIALOG_ID
                            select new
                            {
                                a.DIALOG_ID,
                                a.SCHOOL_NO,
                                SHORT_NAME = b.SHORT_NAME,
                                a.CLASS_NO,
                                a.SYEAR,
                                a.SEMESTER,
                                a.SEAT_NO,
                                a.NAME,
                                a.SNAME,
                                a.CRE_DATE,
                                a.LOTTO_ORDER,
                                a.USER_NO,
                                counts = context.ADDT13_HIS
                                    .Count(hh => hh.DIALOG_ID == a.DIALOG_ID && hh.USER_NO == a.USER_NO)
                            };

                // 加入模糊搜尋條件
                if (!string.IsNullOrEmpty(PassSearchContents))
                {
                    var keyword = PassSearchContents.Trim();
                    query = query.Where(q =>
                        q.NAME.Contains(keyword) ||
                        q.SNAME.Contains(keyword) ||
                        q.CLASS_NO.Contains(keyword) ||
                        q.SEAT_NO.Contains(keyword));
                }
                Count=query.Count();
                // 分頁處理
                var pagedResult = query
                    .OrderBy(q => q.SEAT_NO)  // 可依需求更改排序欄位
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToList();
                string ThisError = "";
                if (PassOrderByName == "A.CRE_DATE DESC") {



                }
          //   dt = new sqlConnection.sqlConnection().executeQueryBSqlDataReaderOrderListPage(page, pageSize, sb.ToString(), sb.ToString(), PassOrderByName, ref Count, ref ThisError, PassOrderBy2Name);
             //  dt= new sqlConnection.sqlConnection().execute()
              
                    foreach (var dr in pagedResult)
                    {
                        ReturnData = new uADDT13();

                        ReturnData.DIALOG_ID = dr.DIALOG_ID;
                        ReturnData.SCHOOL_NO = dr.SCHOOL_NO;
                        ReturnData.SHORT_NAME = dr.SHORT_NAME;
                        ReturnData.USER_NO = dr.USER_NO;
                        ReturnData.CLASS_NO = dr.CLASS_NO;
                        ReturnData.SYEAR = dr.SYEAR;
                        ReturnData.SEMESTER = dr.SEMESTER;
                        ReturnData.SEAT_NO = dr.SEAT_NO;
                        ReturnData.NAME = dr.NAME;
                        ReturnData.SNAME = dr.SNAME;
                        ReturnData.CRE_DATE = dr.CRE_DATE;
                        ReturnData.LOTTO_ORDER = dr.LOTTO_ORDER??0;
                        ReturnData.Count = dr.counts;
                        // ReturnData.Grade = 100 - 10 * (ReturnData.Count - 1); =>2018/11/15 改計算方法
                        list_data.Add(ReturnData);
                    }


         
            }
            catch (Exception exception)
            {
                throw exception;
            }
            return list_data;
        }

        public ADDI05TotalGraphViewModel GetTotalGraph(ADDI05TotalGraphViewModel model, ref ECOOL_DEVEntities db)
        {
            if (model == null) model = new ADDI05TotalGraphViewModel();

            string sSQL = @"SELECT A.DIALOG_ID, A.Q_NUM ,A.RIGHT_YN,A.ANSWER,COUNT(*) QTY,S.TOT_COUNT
                            ,RATE =round(COUNT(*) *100 /S.TOT_COUNT* 1.00,2)
                            FROM ADDT13_HIS_D A (nolock)
                            JOIN (SELECT T.DIALOG_ID, COUNT(DISTINCT T.HIS_NO) TOT_COUNT FROM ADDT13_HIS T (nolock)
                            WHERE T.DIALOG_ID=@DIALOG_ID GROUP BY  T.DIALOG_ID ) AS S ON A.DIALOG_ID=S.DIALOG_ID
                            WHERE A.DIALOG_ID=@DIALOG_ID
                            GROUP BY A.DIALOG_ID, A.Q_NUM ,A.RIGHT_YN,A.ANSWER,S.TOT_COUNT
                            ORDER BY A.DIALOG_ID, A.Q_NUM,A.ANSWER";

            model.RightCountData = db.Database.Connection.Query<ADDI05TotalGraphRightCountViewModel>(sSQL
                , new { DIALOG_ID = model.DIALOG_ID }).ToList();

            var HisList = db.ADDT13_HIS.Where(a => a.DIALOG_ID == model.DIALOG_ID).OrderBy(x => x.SCHOOL_NO)
                .ThenBy(x => x.CLASS_NO).ThenBy(x => x.SEAT_NO).ThenByDescending(x => x.CRE_DATE).ToList();

            if (HisList.Count() > 0)
            {
                model.AnsLists = (from a in db.ADDT13_HIS
                                  join b in db.ADDT13_HIS_D on a.HIS_NO equals b.HIS_NO
                                  where a.DIALOG_ID == model.DIALOG_ID
                                  select new ADDI05TotalGraphDetailsListViewModel()
                                  {
                                      HIS_NO = a.HIS_NO,
                                      DIALOG_ID = a.DIALOG_ID,
                                      SCHOOL_NO = a.SCHOOL_NO,
                                      USER_NO = a.USER_NO,
                                      CLASS_NO = a.CLASS_NO,
                                      SYEAR = a.SYEAR,
                                      SEMESTER = a.SEMESTER,
                                      SEAT_NO = a.SEAT_NO,
                                      NAME = a.NAME,
                                      SNAME = a.SNAME,
                                      CRE_PERSON = a.CRE_PERSON,
                                      CRE_DATE = a.CRE_DATE,
                                      ALL_RIGHT_YN = a.RIGHT_YN,
                                      Q_NUM = b.Q_NUM,
                                      RIGHT_YN = b.RIGHT_YN,
                                      ANSWER = b.ANSWER,
                                  }).ToListNoLock();

                model.AllCount = HisList.Count();

                model.TrueCount = HisList.Where(a => a.RIGHT_YN == SharedGlobal.Y).Count();

                model.TrueRate = Math.Round((model.TrueCount / model.AllCount * 100));

                model.FalseCount = HisList.Where(a => a.RIGHT_YN == SharedGlobal.N).Count();

                model.FalseRate = Math.Round((model.FalseCount / model.AllCount * 100));
            }

            return model;
        }

        public ADDI05TotalGraphDetailsViewModel GetTotalGraphAnsList(string DIALOG_ID, int? Q_NUM, string ANSWER, string CLASS_NO, ref ECOOL_DEVEntities db)
        {
            ADDI05TotalGraphDetailsViewModel model = new ADDI05TotalGraphDetailsViewModel();

            model.MADDT11 = db.ADDT11.Where(a => a.DIALOG_ID == DIALOG_ID).FirstOrDefault();

            model.DADDT12 = db.ADDT12.Where(a => a.DIALOG_ID == DIALOG_ID && a.Q_NUM == Q_NUM).FirstOrDefault();

            string sSQL = string.Empty;
            if (Q_NUM != null)
            {
                sSQL = @"SELECT a.* ,c.SHORT_NAME
                            FROM ADDT13_HIS a (nolock)
                            inner join ADDT13_HIS_D b (nolock) on a.HIS_NO=b.HIS_NO
                            inner join BDMT01 c  (nolock) on a.SCHOOL_NO =c.SCHOOL_NO
                            WHERE a.DIALOG_ID=@DIALOG_ID
                            and b.Q_NUM =@Q_NUM
                            and b.ANSWER=@ANSWER
                            order by a.SCHOOL_NO,a.CLASS_NO,a.SEAT_NO,a.CRE_DATE desc";
            }
            else
            {
                sSQL = @"SELECT a.* ,c.SHORT_NAME
                            FROM ADDT13_HIS a (nolock)
                            inner join BDMT01 c  (nolock) on a.SCHOOL_NO =c.SCHOOL_NO
                            WHERE a.DIALOG_ID=@DIALOG_ID
                            order by a.RIGHT_YN desc,a.SCHOOL_NO,a.CLASS_NO,a.SEAT_NO,a.CRE_DATE desc";
            }

            var AnsList = db.Database.Connection.Query<ADDI05TotalGraphDetailsListViewModel>(sSQL
                  , new
                  {
                      DIALOG_ID = DIALOG_ID,
                      Q_NUM = Q_NUM,
                      ANSWER = ANSWER,
                  }).AsEnumerable();

            if (!string.IsNullOrWhiteSpace(CLASS_NO))
            {
                AnsList = AnsList.Where(a => a.CLASS_NO == CLASS_NO);
            }

            model.AnsList = AnsList.ToList();

            model.ANSWER = ANSWER;

            return model;
        }
    }
}