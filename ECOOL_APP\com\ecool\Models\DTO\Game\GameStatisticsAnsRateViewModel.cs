﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class GameStatisticsAnsRateViewModel
    {
        /// <summary>
        ///活動id
        /// </summary>
        [DisplayName("活動id")]
        public string GAME_NO { get; set; }

        /// <summary>
        ///題目序號
        /// </summary>
        [DisplayName("題目序號")]
        public string GROUP_ID { get; set; }

        /// <summary>
        ///題目描述
        /// </summary>
        [DisplayName("題目描述")]
        public string G_SUBJECT { get; set; }

        /// <summary>
        ///題目排序
        /// </summary>
        [DisplayName("題目排序")]
        public int? G_ORDER_BY { get; set; }

        public List<GameStatisticsAnsThisRateViewModel> RateData { get; set; }
    }
}