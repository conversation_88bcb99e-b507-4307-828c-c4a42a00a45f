/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/General/Italic/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXGeneral-italic"]={directory:"General/Italic",family:"STIXGeneral",style:"italic",Ranges:[[160,255,"Latin1Supplement"],[256,383,"LatinExtendedA"],[384,591,"LatinExtendedB"],[592,687,"IPAExtensions"],[688,767,"SpacingModLetters"],[880,1023,"GreekAndCoptic"],[1024,1279,"Cyrillic"],[7680,7935,"LatinExtendedAdditional"],[8192,8303,"GeneralPunctuation"],[8352,8399,"CurrencySymbols"],[8400,8447,"CombDiactForSymbols"],[8448,8527,"LetterlikeSymbols"],[8704,8959,"MathOperators"],[9216,9279,"ControlPictures"],[9312,9471,"EnclosedAlphanum"],[9472,9599,"BoxDrawing"],[64256,64335,"AlphaPresentForms"],[119860,119911,"MathItalic"],[119964,120015,"MathScript"],[120328,120379,"MathSSItalic"],[120484,120485,"ij"],[120546,120603,"GreekItalic"]],32:[0,0,250,0,0],33:[667,11,333,39,304],34:[666,-421,420,144,432],35:[676,0,501,2,540],36:[731,89,500,32,497],37:[706,19,755,80,705],38:[666,18,778,76,723],39:[666,-421,214,132,241],40:[669,181,333,42,315],41:[669,180,333,16,289],42:[666,-255,500,128,492],43:[506,0,675,86,590],44:[101,129,250,-5,135],45:[255,-192,333,49,282],46:[100,11,250,27,138],47:[666,18,278,-65,386],48:[676,7,500,32,497],49:[676,0,500,50,409],50:[676,0,500,12,452],51:[676,7,500,16,465],52:[676,0,500,1,479],53:[666,7,500,15,491],54:[686,7,500,30,521],55:[666,8,500,75,537],56:[676,7,500,30,493],57:[676,17,500,23,492],58:[441,11,333,50,261],59:[441,129,333,26,261],60:[516,10,675,84,592],61:[386,-120,675,86,590],62:[516,10,675,84,592],63:[664,12,500,132,472],64:[666,18,920,118,806],65:[668,0,611,-51,564],66:[653,0,611,-8,588],67:[666,18,667,66,689],68:[653,0,722,-8,700],69:[653,0,611,-1,634],70:[653,0,611,8,645],71:[666,18,722,52,722],72:[653,0,722,-8,769],73:[653,0,333,-8,384],74:[653,18,444,-6,491],75:[653,0,667,7,722],76:[653,0,556,-8,559],77:[653,0,833,-18,872],78:[653,15,667,-20,727],79:[667,18,722,60,699],80:[653,0,611,0,605],81:[666,182,722,59,699],82:[653,0,611,-13,588],83:[667,18,500,17,508],84:[653,0,556,59,633],85:[653,18,722,102,765],86:[653,18,611,76,688],87:[653,18,833,71,906],88:[653,0,611,-29,655],89:[653,0,556,78,633],90:[653,0,556,-6,606],91:[663,153,389,21,391],92:[666,18,278,-41,319],93:[663,153,389,12,382],94:[666,-301,422,0,422],95:[-75,125,500,0,500],96:[664,-492,333,120,311],97:[441,11,501,17,476],98:[683,11,500,23,473],99:[441,11,444,30,425],100:[683,13,500,15,527],101:[441,11,444,31,412],102:[678,207,278,-147,424],103:[441,206,500,8,471],104:[683,9,500,19,478],105:[654,11,278,49,264],106:[652,207,278,-124,279],107:[683,11,444,14,461],108:[683,11,278,41,279],109:[441,9,722,12,704],110:[441,9,500,14,474],111:[441,11,500,27,468],112:[441,205,504,-75,472],113:[441,209,500,25,484],114:[441,0,389,45,412],115:[442,13,389,16,366],116:[546,11,278,38,296],117:[441,11,500,42,475],118:[441,18,444,20,426],119:[441,18,667,15,648],120:[441,11,444,-27,447],121:[441,206,444,-24,426],122:[428,81,389,-2,380],123:[687,177,400,51,407],124:[666,18,275,105,171],125:[687,177,400,-7,349],126:[323,-183,541,40,502],305:[441,11,278,47,235],567:[441,207,278,-124,246],915:[653,0,611,8,645],916:[668,0,611,-32,526],920:[667,18,722,60,699],923:[668,0,611,-51,564],926:[653,0,651,-6,680],928:[653,0,722,-8,769],931:[653,0,620,-6,659],933:[668,0,556,78,648],934:[653,0,741,50,731],936:[667,0,675,77,778],937:[666,0,762,-6,739],945:[441,11,552,27,549],946:[678,205,506,-40,514],947:[435,206,410,19,438],948:[668,11,460,24,460],949:[441,11,444,30,425],950:[683,185,454,30,475],951:[441,205,474,14,442],952:[678,11,480,27,494],953:[441,11,278,49,235],954:[441,13,444,14,465],955:[678,16,458,-12,431],956:[428,205,526,-33,483],957:[441,18,470,20,459],958:[683,185,454,30,446],959:[441,11,500,27,468],960:[428,18,504,19,536],961:[441,205,504,-40,471],962:[441,185,454,30,453],963:[428,11,498,27,531],964:[428,11,410,12,426],965:[441,10,478,19,446],966:[441,205,622,27,590],967:[441,207,457,-108,498],968:[441,205,584,15,668],969:[439,11,686,27,654],977:[678,10,556,19,526],981:[683,205,627,27,595],982:[428,11,792,17,832],1009:[441,205,516,27,484],1013:[441,11,444,30,420],8467:[687,11,579,48,571]};MathJax.OutputJax["HTML-CSS"].initFont("STIXGeneral-italic");MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/Italic/Main.js");
