﻿using ECOOL_APP.EF;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace EcoolWeb.Models
{
    public class ADDI01ApiDetailsViewModel
    {

        /// <summary>
        /// * (條件必傳) 線上投稿單號
        /// </summary>
        public int? WRITING_NO { get; set; }

        /// <summary>
        /// (條件) 顯示批閱前文章(false)，還是批閱後 (true) 
        /// </summary>
        public bool? ShowOriginal { get; set; }

 
        /// <summary>
        /// 線上投稿資料
        /// </summary>
        public ADDT01 aDDT01 { get; set; }
    }
}