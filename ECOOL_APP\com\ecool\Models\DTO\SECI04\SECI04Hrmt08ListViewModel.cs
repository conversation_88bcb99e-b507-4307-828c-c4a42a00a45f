﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.com.ecool.Models
{
    public class SECI04Hrmt08ListViewModel
    {

        ///Summary
        ///學校代碼
        ///Summary
        [DisplayName("學校代碼")]
        public string SCHOOL_NO { get; set; }

        ///Summary
        ///學年
        ///Summary
        [DisplayName("學年")]
        public byte? SYEAR { get; set; }

        ///Summary
        ///學期
        ///Summary
        [DisplayName("學期")]
        public byte? SEMESTER { get; set; }

        ///Summary
        ///年級
        ///Summary
        [DisplayName("年級")]
        public string GRADE { get; set; }

        ///Summary
        ///年級+學期 =>Y
        ///Summary
        [DisplayName("年級+學期")]
        public string GRADE_SEMESTER { get; set; }

        ///Summary
        ///歷屆身高平均(男)
        ///Summary
        [DisplayName("歷屆身高平均(男)")]
        public decimal? AVG_TALL_M { get; set; }

        ///Summary
        ///歷屆身高平均(女)
        ///Summary
        [DisplayName("歷屆身高平均(女)")]
        public decimal? AVG_TALL_W { get; set; }

        ///Summary
        ///歷屆平均體重(男)
        ///Summary
        [DisplayName("歷屆平均體重(男)")]
        public decimal? AVG_WEIGHT_M { get; set; }

        ///Summary
        ///歷屆平均體重(女)
        ///Summary
        [DisplayName("歷屆平均體重(女)")]
        public decimal? AVG_WEIGHT_W { get; set; }



        ////

        ///Summary
        ///身高平均(男)
        ///Summary
        [DisplayName("身高平均(男)")]
        public decimal? This_AVG_TALL_M { get; set; }

        ///Summary
        ///身高平均(女)
        ///Summary
        [DisplayName("身高平均(女)")]
        public decimal? This_AVG_TALL_W { get; set; }

        ///Summary
        ///平均體重(男)
        ///Summary
        [DisplayName("平均體重(男)")]
        public decimal? This_AVG_WEIGHT_M { get; set; }

        ///Summary
        ///平均體重(女)
        ///Summary
        [DisplayName("平均體重(女)")]
        public decimal? This_AVG_WEIGHT_W { get; set; }




        /// <summary>
        /// 歷屆視力人數 Left 小於 0.8
        /// </summary>

        [DisplayName("歷屆平均-0.8以下")]
        public double VisionLeft_08 { get; set; }

        /// <summary>
        /// 歷屆視力人數 Left 0.9以上
        /// </summary>
        [DisplayName("歷屆平均-0.9以上")]
        public double VisionLeft_09 { get; set; }



        /// <summary>
        /// 歷屆視力人數 Right 小於 0.8
        /// </summary>
        [DisplayName("歷屆平均-0.8以下")]
        public double VisionRight_08 { get; set; }

        /// <summary>
        /// 歷屆視力人數 Right 0.9以上
        /// </summary>
        [DisplayName("歷屆平均-0.9以上")]
        public double VisionRight_09 { get; set; }


        /// <summary>
        /// (條件下)視力人數 Left 小於 0.8
        /// </summary>
        [DisplayName("條件平均-0.8以下")]
        public double This_VisionLeft_08 { get; set; }

        /// <summary>
        /// (條件下)視力人數 Left 0.9以上
        /// </summary>
        [DisplayName("條件平均-0.9以上")]
        public double This_VisionLeft_09 { get; set; }


        /// <summary>
        /// (條件下)視力人數 Right 小於 0.8
        /// </summary>
        [DisplayName("條件平均-0.8以下")]
        public double This_VisionRight_08 { get; set; }

        /// <summary>
        /// (條件下)視力人數 Right 0.9以上
        /// </summary>
        [DisplayName("條件平均-0.9以上")]
        public double This_VisionRight_09 { get; set; }



    }
}
