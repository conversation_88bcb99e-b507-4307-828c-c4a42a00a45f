(function(factory){if (typeof define === 'function' && define.amd){define(['jquery'], factory);} else if (typeof module === 'object' && typeof module.exports === 'object'){module.exports = factory(require('jquery'));} else {factory(jQuery);}}(function(jQuery){

/*! Widget: math - updated 11/20/2018 (v2.31.1) */
!function(v){"use strict";var _=v.tablesorter,x={error:{0:"Infinity result: Divide by zero",1:"Need more than one element to make this calculation",undef:"No elements found"},invalid:function(t,e,n){return console.warn(e,x.error[n]),t&&t.widgetOptions.math_none||""},events:"tablesorter-initialized update updateAll updateRows addRows updateCell filterReset ".split(" ").join(".tsmath "),processText:function(t,e){var n,a=t.widgetOptions,r=_.getElementText(t,e,x.getCellIndex(e)),i=t.widgetOptions.math_prefix;return a.math_textAttr&&(r=e.attr(a.math_textAttr)||r),/</.test(i)&&(n=v("<div>"+i+"</div>").text().replace(/\{content\}/g,"").trim(),r=r.replace(n,"")),r=_.formatFloat(r.replace(/[^\w,. \-()]/g,""),t.table)||0,isNaN(r)?0:r},getRow:function(t,e,n){var a,r=t.widgetOptions,i=[],l=e.closest("tr"),o=l.hasClass(r.filter_filteredRow||"filtered");return n&&(l=l.filter(n)),!n&&o||(a=l.children().not("["+r.math_dataAttrib+"=ignore]"),r.math_ignore.length&&(a=a.filter(function(){return-1===v.inArray(x.getCellIndex(v(this)),r.math_ignore)})),i=a.not(e).map(function(){return x.processText(t,v(this))}).get()),i},getColumn:function(t,e,n,a){var r,i,l,o,h,s,d=t.widgetOptions,u=[],c=e.closest("tr"),g=d.math_dataAttrib,f="["+g+"=ignore]",m=d.filter_filteredRow||"filtered",p=x.getCellIndex(e),_=t.$table.children("tbody").children(),b=["["+g+"^=above]","["+g+"^=below]","["+g+"^=col]","["+g+"^=all]"];if("above"===n)for(r=o=_.index(c);0<=r;)s=(l=_.eq(r)).children().filter(b[0]).length,a&&(l=l.filter(a)),i=l.children().filter(function(){return x.getCellIndex(v(this))===p}),((a||!l.hasClass(m))&&l.not(f).length&&r!==o||s&&r!==o)&&(s?r=0:i.length&&(u[u.length]=x.processText(t,i))),r--;else if("below"===n)for(o=_.length,r=_.index(c)+1;r<o&&!(l=_.eq(r)).children().filter(b[1]).length;r++)a&&(l=l.filter(a)),i=l.children().filter(function(){return x.getCellIndex(v(this))===p}),(a||!l.hasClass(m))&&l.not(f).length&&i.length&&(u[u.length]=x.processText(t,i));else for(o=(h=_.not(f)).length,r=0;r<o;r++)l=h.eq(r),a&&(l=l.filter(a)),i=l.children().filter(function(){return x.getCellIndex(v(this))===p}),!a&&l.hasClass(m)||!i.not(b.join(",")).length||i.is(e)||(u[u.length]=x.processText(t,i));return u},getAll:function(t,e){var n,a,r,i,l,o,h,s,d=[],u=t.widgetOptions,c=u.math_dataAttrib,g="["+c+"=ignore]",f=u.filter_filteredRow||"filtered",m=t.$table.children("tbody").children().not(g);for(l=m.length,i=0;i<l;i++)if(r=m.eq(i),e&&(r=r.filter(e)),e||!r.hasClass(f))for(s=(o=r.children().not(g)).length,h=0;h<s;h++)n=o.eq(h),a=x.getCellIndex(n),!n.filter("["+c+"]").length&&v.inArray(a,u.math_ignore)<0&&(d[d.length]=x.processText(t,n));return d},setColumnIndexes:function(t){var e=t.$table,r=1,n=e.children("tbody").children().filter(function(){var t,e,n=v(this),a=0<n.children("[colspan]").length;if(1<r?(r--,a=!0):r<1&&(r=1),0<n.children("[rowspan]").length)for(t=this.cells,e=0;e<t.length;e++)r=Math.max(t[e].rowSpan,r);return a});_.computeColumnIndex(n,t)},getCellIndex:function(t){var e=t.attr("data-column");return void 0===e?t[0].cellIndex:parseInt(e,10)},recalculate:function(n,t,e){if(n&&(!t.math_isUpdating||e)){var a,r,i,l,o,h=!1,s={};for((n.debug||t.math_debug)&&(a=new Date),e&&x.setColumnIndexes(n),t.math_dataAttrib="data-"+(t.math_data||"math"),r=t.math_dataAttrib,i=n.$tbodies.children("tr").children("["+r+"]"),h=x.mathType(n,i,t.math_priority)||h,i=n.$table.children("."+n.cssInfoBlock+", tfoot").children("tr").children("["+r+"]"),x.mathType(n,i,t.math_priority),o=(i=n.$table.children().children("tr").children("["+r+"^=all]")).length,l=0;l<o;l++){var d=i.eq(l),u=d.attr(r+"-filter")||t.math_rowFilter;s[u]=s[u]?s[u].add(d):d}v.each(s,function(t,e){h=x.mathType(n,e,["all"],t)||h}),h?(t.math_isUpdating=!0,(n.debug||t.math_debug)&&console[console.group?"group":"log"]("Math widget updating the cache after recalculation"),_.updateCache(n,function(){x.updateComplete(n),e||"function"!=typeof t.math_completed||t.math_completed(n),(n.debug||t.math_debug)&&console.log("Math widget update completed"+_.benchmark(a))})):(e||"function"!=typeof t.math_completed||t.math_completed(n),(n.debug||t.math_debug)&&console.log("Math widget found no changes in data"+_.benchmark(a)))}},updateComplete:function(t){var e=t.widgetOptions;e.math_isUpdating&&(t.debug||e.math_debug)&&console.groupEnd&&console.groupEnd(),e.math_isUpdating=!1},mathType:function(s,d,t,u){if(d.length){var c,g=!1,f=s.widgetOptions,m=f.math_dataAttrib,p=_.equations;return"all"===t[0]&&(c=x.getAll(s,u)),(s.debug||f.math_debug)&&console[console.group?"group":"log"]("Tablesorter Math widget recalculation"),v.each(t,function(t,e){var n,a,r,i,l,o=d.filter("["+m+"^="+e+"]"),h=o.length;if(h){for((s.debug||f.math_debug)&&console[console.group?"group":"log"](e),n=0;n<h;n++)(l=o.eq(n)).parent().hasClass(f.filter_filteredRow||"filtered")||(u=l.attr(m+"-filter")||f.math_rowFilter,r=(l.attr(m)||"").replace(e+"-",""),a="row"===e?x.getRow(s,l,u):"all"===e?c:x.getColumn(s,l,e,u),p[r]&&(a.length?(i=p[r](a,s),(s.debug||f.math_debug)&&console.log(l.attr(m),u?'("'+u+'")':"",a,"=",i)):i=x.invalid(s,r,"mean"===r?0:"undef"),g=x.output(l,s,i,a)||g));(s.debug||f.math_debug)&&console.groupEnd&&console.groupEnd()}}),(s.debug||f.math_debug)&&console.groupEnd&&console.groupEnd(),g}return!1},output:function(t,e,n,a){var r,i=e.widgetOptions,l=!1,o=t.html(),h=t.attr("data-"+i.math_data+"-mask")||i.math_mask,s=t.attr("data-"+i.math_data+"-target")||"",d=_.formatMask(h,n,i.math_prefix,i.math_suffix);return s&&(r=t.find(s)).length&&(t=r),"function"==typeof i.math_complete&&(d=i.math_complete(t,i,d,n,a)),!1!==d&&(l=o!==d,t.html(d)),!(l&&(!(r=t.closest("tbody")).length||r.hasClass(e.cssInfoBlock)||r.parent()[0]!==e.table))&&l}};_.formatMask=function(t,e,n,a){if(!t||isNaN(+e))return e;var r,i,l,o,h,s,d,u,c,g,f,m,p,_,b="",v=t.length,x=t.search(/[0-9\-\+#]/),w=0<x?t.substring(0,x):"",C=w;if(n&&(C=/\{content\}/.test(n||"")?(n||"").replace(/\{content\}/g,w||""):(n||"")+w),p=v-(_=t.split("").reverse().join("").search(/[0-9\-\+#]/)),p+="."===t.substring(p,p+1)?1:0,b=w=0<_?t.substring(p,v):"",a&&(b=/\{content\}/.test(a||"")?(a||"").replace(/\{content\}/g,w||""):w+(a||"")),r=(e="-"===(t=t.substring(x,p)).charAt(0)?-e:+e)<0?e=-e:0,l=(i=t.match(/[^\d\-\+#]/g))&&i[i.length-1]||".",o=i&&i[1]&&i[0]||",",t=t.split(l),e=+(e=e.toFixed(t[1]&&t[1].length))+"",s=t[1]&&t[1].lastIndexOf("0"),(!(u=e.split("."))[1]||u[1]&&u[1].length<=s)&&(e=(+e).toFixed(s+1)),c=t[0].split(o),t[0]=c.join(""),-1<(h=t[0]&&t[0].indexOf("0")))for(;u[0].length<t[0].length-h;)u[0]="0"+u[0];else 0==+u[0]&&(u[0]="");if((e=e.split("."))[0]=u[0],d=c[1]&&c[c.length-1].length){for(f="",m=(g=e[0]).length%d,v=g.length,p=0;p<v;p++)f+=g.charAt(p),!((p-m+1)%d)&&p<v-d&&(f+=o);e[0]=f}return e[1]=t[1]&&e[1]?l+e[1]:"",C+(r?"-":"")+e[0]+e[1]+b},_.equations={count:function(t){return t.length},sum:function(t){var e,n=t.length,a=0;for(e=0;e<n;e++)a+=t[e];return a},mean:function(t){return _.equations.sum(t)/t.length},median:function(t,e){var n,a=t.length;return 1<a?(t.sort(function(t,e){return t-e}),n=Math.floor(a/2),a%2?t[n]:(t[n-1]+t[n])/2):x.invalid(e,"median",1)},mode:function(t){var e,n,a,r={},i=1,l=[t[0]];for(e=0;e<t.length;e++)r[n=t[e]]=r[n]?r[n]+1:1,i<(a=r[n])?(l=[n],i=a):a===i&&(l[l.length]=n,i=a);return l.sort(function(t,e){return t-e})},max:function(t){return Math.max.apply(Math,t)},min:function(t){return Math.min.apply(Math,t)},range:function(t){var e=t.sort(function(t,e){return t-e});return e[t.length-1]-e[0]},variance:function(t,e,n){for(var a,r=_.equations.mean(t),i=0,l=t.length;l--;)i+=Math.pow(t[l]-r,2);return 0===(a=t.length-(e?0:1))?x.invalid(n,"variance",0):i/=a},varp:function(t,e){return _.equations.variance(t,!0,e)},vars:function(t,e){return _.equations.variance(t,!1,e)},stdevs:function(t,e){var n=_.equations.variance(t,!1,e);return Math.sqrt(n)},stdevp:function(t,e){var n=_.equations.variance(t,!0,e);return Math.sqrt(n)}},_.addWidget({id:"math",priority:100,options:{math_data:"math",math_debug:!1,math_ignore:[],math_mask:"#,##0.00",math_complete:null,math_completed:function(){},math_priority:["row","above","below","col"],math_prefix:"",math_suffix:"",math_textAttr:"",math_none:"N/A",math_event:"recalculate",math_rowFilter:""},init:function(t,e,n,a){var r=(_.hasWidget(t,"filter")?"filterEnd":"updateComplete")+".tsmath";x.events+=(_.hasWidget(t,"pager")?"pagerComplete":"filterEnd")+".tsmath ",n.$table.off((x.events+"updateComplete.tsmath "+a.math_event).replace(/\s+/g," ")).on(x.events+a.math_event,function(t){if(this.hasInitialized){var e="tablesorter-initialized"===t.type;a.math_isUpdating&&!e||(/filter/.test(t.type)||e||x.setColumnIndexes(n),x.recalculate(n,a,e))}}).on(r,function(){setTimeout(function(){x.updateComplete(n)},40)}),a.math_isUpdating=!1,t.hasInitialized&&x.recalculate(n,a,!0)},remove:function(t,e,n,a){a||e.$table.off((x.events+" updateComplete.tsmath "+n.math_event).replace(/\s+/g," ")).children().children("tr").children("[data-"+n.math_data+"]").empty()}})}(jQuery);return jQuery;}));
