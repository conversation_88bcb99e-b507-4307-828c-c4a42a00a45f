﻿
using MvcPaging;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.IO;
using System.Linq;
using System.Text;

namespace ECOOL_APP.EF
{
    public class ADDI11EditViewModel
    {
        public string SearchCLASS_NO { get; set; }

        public string SearchName { get; set; }

       [ Required(ErrorMessage = "此欄位必輸")]
       [ DisplayFormat(DataFormatString = "{0:yyyy/MM/dd}", ApplyFormatInEditMode = true)]
        public DateTime? RunDate { get; set; }

        public int Page { get; set; }
        public int ONE_LAP_M { get; set; }
        public double? LAP { get; set; }
        public int PageSize { get; set; }
        public virtual ICollection<ADDI11EditPeopleViewModel> People { get; set; }
        public virtual IPagedList<ADDI11EditPeopleViewModel> Peoples { get; set; }
        public ADDI11EditViewModel() {

            PageSize =7;
        }
    }
}