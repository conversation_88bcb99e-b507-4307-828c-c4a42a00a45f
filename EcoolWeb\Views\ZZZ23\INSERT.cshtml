﻿@model ECOOL_APP.EF.HRMT24
@{
    ViewBag.Title = "新增角色";
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@using (Html.BeginForm())
{
    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })


    <div class="panel panel-ACC">
        <div class="panel-heading text-center">
            @Html.BarTitle()
        </div>
        <div class="panel-body">
            <div class="form-horizontal">
                <div class="form-group">
                    <div class="control-label col-md-2">
                        角色名稱
                    </div>
                    <div class="col-md-10">
                        @Html.EditorFor(model => model.ROLE_NAME, new { htmlAttributes = new { @class = "form-control" } })
                        @Html.ValidationMessageFor(model => model.ROLE_NAME, "", new { @class = "text-danger" })
                    </div>
                </div>

                <div class="form-group text-center">
                    <button id="Add" name="Add" value="新增" class="btn btn-default">
                        確定送出
                    </button>

                    <a href='@Url.Action("QUERY", "ZZZ23")' class="btn btn-default" role="button">
                        放棄編輯
                    </a>
                </div>
            </div>
        </div>
    </div>

   
}

@section Scripts
{
    <script type="text/javascript">
        $(function () {
            $("#Add").click(function () { return Valid(); });
        });

        function Valid() {

            var msg = '';
            var blStatus = false;

            if ($("#ROLE_NAME").val().length <= 0) {
                msg += '角色名稱不能為空\r\n';
            }

            if (msg != '') {
                alert(msg);
                blStatus = false;
            }
            else {
                blStatus = true;
            }

            return blStatus;
        }
    </script>
}