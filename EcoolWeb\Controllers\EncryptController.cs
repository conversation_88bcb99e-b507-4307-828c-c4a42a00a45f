﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace EcoolWeb.Controllers
{
    public class EncryptController : Controller
    {
        // GET: Encrypt
        public ActionResult Index()
        {
            return View();
        }

        [HttpPost]
        public ActionResult Index(string StrA,string StrB,string NewPageKey)
        {
            ViewBag.StrA = ECOOL_APP.AppHelper.MD5Encrypt32(StrA);

            if (string.IsNullOrWhiteSpace(StrB)==false && string.IsNullOrWhiteSpace(NewPageKey)==false)
            {
                ViewBag.StrB = ECOOL_APP.AppHelper.Encrypt(StrB, NewPageKey);
            }
           

            return View();
        }
    }
}