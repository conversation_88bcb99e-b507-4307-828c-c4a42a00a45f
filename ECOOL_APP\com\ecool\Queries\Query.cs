﻿using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    internal class Query<TContext, TResult> : IQuery<TResult> where TContext : DbContext
    {
        public Query(IConnection connection, Func<TContext, TResult> selector)
        {
            Connection = connection;
            Selector = selector;
        }

        public TResult Execute() => Selector(Connection.ToContext<TContext>());

        private IConnection Connection { get; }
        private Func<TContext, TResult> Selector { get; }
    }
}