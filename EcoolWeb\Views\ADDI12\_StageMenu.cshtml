﻿@using ECOOL_APP;
@{
    /**/

    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

    var Permission = ViewBag.Permission as List<ControllerPermissionfile>;
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);

}

<div class="form-group">



    @if (Permission.Where(a => a.ActionName == "Index").Any())
    {
        <a role="button" href='@Url.Action("Index",(string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys  @(ViewBag.NowAction=="Index" ? "active":"")">
            小小舞臺說明
        </a>

    }
    @if (user?.USER_NO == "stage")
    {
        <a role="button" href='@Url.Action("Edit1",(string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys @(ViewBag.NowAction=="Edit" ? "active":"")">
            我要申請小小舞臺(小小舞台幫手)
        </a>

    }
    @if (Permission.Where(a => a.ActionName == "Edit").Any())
    {
        <a role="button" href='@Url.Action("Edit",(string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys @(ViewBag.NowAction=="Edit" ? "active":"")">
            我要申請小小舞臺
        </a>
    }

    @if (Permission.Where(a => a.ActionName == "Index").Any())
    {
        <a role="button" href='@Url.Action("PremierView",(string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys @(ViewBag.NowAction==ADDI12IndexListViewModel.ActionResultTypeVal.PremierView ? "active":"")">
            首播影片 <span class="badge">@ViewBag.PremierCount</span>
        </a>
        <a role="button" href='@Url.Action("AllVideoView",(string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys @(ViewBag.NowAction==ADDI12IndexListViewModel.ActionResultTypeVal.AllVideoView ? "active":"")">
            一般影片 <span class="badge">@ViewBag.AllVideoCount</span>
        </a>
    }

    @if (user != null)
    {
        if (user.USER_TYPE == UserType.Student)
        {
            <a role="button" href='@Url.Action("MyVideoView",(string)ViewBag.BRE_NO )' class="btn btn-sm btn-sys @(ViewBag.NowAction==ADDI12IndexListViewModel.ActionResultTypeVal.MyVideoView ? "active":"")">
                我的影片
            </a>
        }
        else if ((user.USER_TYPE == UserType.Teacher || user.USER_TYPE == UserType.Admin) && Permission.Where(a => a.ActionName == "Edit").Any())
        {
            <a role="button" href='@Url.Action("MyUploadVideoView",(string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys @(ViewBag.NowAction==ADDI12IndexListViewModel.ActionResultTypeVal.MyUploadVideoView ? "active":"")">
                我上傳的影片
            </a>
        }
    }

    @if (Permission.Where(a => a.ActionName == "LiveEditSET").Any())
    {
        <a role="button" href='@Url.Action("Index","ZZZI25",new { IMG_TYPE=ADDT19.IMG_TYPEVal.PremierViewImage})' class="btn btn-sm btn-sys @(ViewBag.NowAction==ADDI12IndexListViewModel.ActionResultTypeVal.PremierViewImage ? "active":"")">
            首播圖片輪播設定
        </a>
    }
</div>