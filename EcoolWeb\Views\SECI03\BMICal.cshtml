﻿@using ECOOL_APP.com.ecool.Models.DTO;
@model ECOOL_APP.com.ecool.Models.DTO.SECI03BMICalViewModel

<link href="~/Content/css/EzCss.css" rel="stylesheet" />
@{
    ViewBag.Title = ViewBag.Panel_Title;

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode || (Model?.WhereIsColorboxForUser ?? false))
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")
@Html.ValidationMessage("ErrorMsg", new { @class = "text-danger" })

@using (Html.BeginForm("BMICal2", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", enctype = "multipart/form-data" }))
{

    @Html.AntiForgeryToken()
    @Html.Hidden("SYS_TABLE_TYPE", (string)ViewBag.SYS_TABLE_TYPE)
    @Html.HiddenFor(m => m.WhereIsColorboxForUser)
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })

    <div class="Div-EZ-ADDI09">
        <div class="form-horizontal">
            @*<div class="form-group">
                    @Html.LabelFor(model => model.Sex, htmlAttributes: new { @class = "control-label col-md-4" })
                    <div class="col-md-8">
                        @Html.DropDownListFor(model => model.Sex, (IEnumerable<SelectListItem>)ViewBag.SexSelectItem, new { @class = "form-control" })
                        @Html.ValidationMessageFor(model => model.Sex, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group">
                    @Html.LabelFor(model => model.Old, htmlAttributes: new { @class = "control-label col-md-4" })
                    <div class="col-md-8">
                        @Html.EditorFor(model => model.Old, new { htmlAttributes = new { @class = "form-control", @type = "text" } })
                        @Html.ValidationMessageFor(model => model.Old, "", new { @class = "text-danger" })
                    </div>
                </div>*@
            <div class="form-group">
                @Html.LabelFor(model => model.Height, htmlAttributes: new { @class = "control-label col-md-4" })
                <div class="col-md-8">
                    @Html.EditorFor(model => model.Height, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.Height, "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="form-group">
                @Html.LabelFor(model => model.Weight, htmlAttributes: new { @class = "control-label col-md-4" })
                <div class="col-md-8">
                    @Html.EditorFor(model => model.Weight, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.Weight, "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="text-center">
                <button type="button" id="myButton" data-loading-text="Loading..." class="btn btn-default btn-sm" autocomplete="off" onclick="DbQuery();">
                    計算BMI
                </button>
            </div>
        </div>
    </div>

    <div style="height:30px; "></div>

    <div id="BMI_V" class="Div-EZ-ADDI09" style="background-color:aquamarine">
    </div>

    <div style="width:90%;margin: 0px auto;padding-top:20px">
        說明：<br />世界衛生組織建議以身體質量指數（BMI）來衡量肥胖程度，其計算公式是以體重（公斤）除以身高（公尺）的平方。
        國民健康署建議我國成人BMI應維持在18.5-24之間，太瘦、過重或太胖皆有礙健康。
        研究顯示，體重過重或是肥胖為糖尿病、心血管疾病、惡性腫瘤等慢性疾病的主要風險因素；而過瘦的健康問題，
        則會有營養不良、骨質疏鬆、猝死等健康問題。
        <div style="height:30px; "></div>
        <div class="table-responsive">
            <table class="table table-striped">
                <tr class="warning">
                    <td>
                        成人肥胖定義
                    </td>
                    <td>
                        身體質量指數(BMI)(kg/m^2)
                    </td>
                </tr>
                <tr class="active">
                    <td>
                        體重過輕
                    </td>
                    <td>
                        BMI<18.5
                    </td>
                </tr>
                <tr>
                    <td>
                        健康體位
                    </td>
                    <td>
                        18.5<=BMI<24
                    </td>
                </tr>
                <tr class="active">
                    <td>
                        體位異常
                    </td>
                    <td>
                        過重：24<=BMI<27<br />輕度肥胖：27 <= BMI < 30<br />中度肥胖：30 <= BMI < 35<br />重度肥胖：BMI >= 35<br />
                    </td>
                </tr>
                <tr>
                    <td></td>
                    <td style="text-align:right">
                        資料來源：健康99網站
                    </td>
                </tr>
            </table>
        </div>
    </div>

}

<script type="text/javascript">

    $('#@Html.IdFor(m => m.Height),#@Html.IdFor(m => m.Weight)').change(function () {

        if ( $('#@Html.IdFor(m => m.Height)').val()!='' && $('#@Html.IdFor(m => m.Weight)').val()!='') {
            DbQuery();
        }

    });

    function DbQuery() {

        $.ajax({
            url: "@(Url.Action("BMICal2", "SECI03"))",     // url位置
            type: 'post',                   // post/get
        data: {  "Height": $('#@Html.IdFor(m => m.Height)').val(), "Weight": $('#@Html.IdFor(m => m.Weight)').val() },
        dataType: 'json',               // xml/json/script/html
        cache: false,                   // 是否允許快取
        success: function (data) {
            var res = jQuery.parseJSON(data);

            $('#BMI_V').text(res.BMI);
        },
        error: function (xhr, err) {
            alert("readyState: " + xhr.readyState + "\nstatus: " + xhr.status);
            alert("responseText: " + xhr.responseText);
        }
    });

    }
</script>