﻿using com.ecool.service;
using ECOOL_APP;
using ECOOL_APP.com.ecool.Models.DTO;
using ECOOL_APP.com.ecool.service;
using ECOOL_APP.com.ecool.util;
using ECOOL_APP.EF;
using EcoolWeb.CustomAttribute;
using EcoolWeb.Models;
using EcoolWeb.Util;
using Ionic.Zip;
using Newtonsoft.Json;
using NPOI.SS.UserModel;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Web;
using System.Web.Mvc;

namespace EcoolWeb.Controllers
{
    [SessionExpire]
    public class ZZZI34Controller : Controller
    {
        /// <summary>
        /// 此Controller 對應的程式代碼
        /// </summary>
        private static string Bre_NO = "ZZZI34";

        private string Bre_Name = BreadcrumbService.GetBRE_NAME_forBRE_NO(Bre_NO);

        /// <summary>
        /// 是否批閱權限，有可上傳檔案
        /// </summary>
        private string VerifyUseYN = string.Empty;

        /// <summary>
        /// 會員相關資訊
        /// </summary>
        private UserProfile user;

        private string ErrorMsg;
        private string SCHOOL_NO = string.Empty;
        private string USER_NO = string.Empty;
        private bool IsAdmin = false;

        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();
        private ZZZI34Service Service = new ZZZI34Service();

        public ActionResult Index()
        {
            this.Shared(Bre_Name + "-說明");
            user = UserProfileHelper.Get();
            ViewBag.ZZZI34SEXPLAIN = db.BDMT01.Where(a => a.SCHOOL_NO == SCHOOL_NO).Select(a => a.ZZZI34SEXPLAIN).FirstOrDefault();
            if (user == null)
            {
                return RedirectToAction("PermissionError1999", "Error");
            }
            return View();
        }
        public ActionResult ExportFileZZI34(ZZZI34IndexViewModel model) {
            this.Shared();
            ViewBag.VisibleVerify = (PermissionService.GetPermission_Use_YN(Bre_NO, "Verify", user?.SCHOOL_NO, user?.USER_NO) == "Y" ? true : false);
          //  model = Service.GetArtGalleryListData(model, user, ref db, ViewBag.VisibleVerify);
            SCHOOL_NO = user.SCHOOL_NO;
            string dicnewpath = "";
            string dicnewZippath = "";
            string realdicnewZippath = "";
            string USER_N = "";
            string realdicnewpath = "";
            string UploadImageRoot = System.Web.Configuration.WebConfigurationManager.AppSettings["UploadImageRoot"];
            if (UploadImageRoot == null) UploadImageRoot = @"~\Content\";
            string RealUploadImageRoot = Request.MapPath(UploadImageRoot);
            USER_N = user.USER_NO;
          
            dicnewZippath = UploadImageRoot + $@"ArtGallery\{SCHOOL_NO}";
            dicnewpath = UploadImageRoot + $@"ArtGallery\{SCHOOL_NO}\{USER_N}";
            realdicnewpath = RealUploadImageRoot + $@"ArtGallery\{SCHOOL_NO}\{USER_N}";
            realdicnewZippath = RealUploadImageRoot + $@"ArtGallery\{SCHOOL_NO}";
            ZZZI34Service zZZI34 = new ZZZI34Service();
            ZZZI34WorkIndexViewModel modeltemp = new ZZZI34WorkIndexViewModel();
            modeltemp.Search = new ZZZI34SearchViewModel();
            modeltemp.Search.WhereMyWork = true;
            modeltemp.Search.WhereSCHOOL_NO = user.SCHOOL_NO;
            modeltemp.Search.WhereUSER_NO = user.USER_NO;
            List<string> ART_GALLERY_NOItem = new List<string>();
            //ART_GALLERY_NOItem = db.ADDT21.Where(x => x.SCHOOL_NO == user.SCHOOL_NO && x.USER_NO == user.USER_NO && x.STATUS != ADDT21.STATUSVal.Disabled).Select(x => x.ART_GALLERY_NO).ToList();
            var data = db.ADDT22.Where(x => x.PHOTO_SCHOOL_NO == user.SCHOOL_NO && x.PHOTO_USER_NO == user.USER_NO && x.PHOTO_STATUS != ADDT21.STATUSVal.Disabled).Select(x => x.ART_GALLERY_NO).ToList();

            //var ART_GALLERY_NOItemlist = db.ADDT22.Where(x => x.PHOTO_SCHOOL_NO == user.SCHOOL_NO && x.PHOTO_USER_NO == user.USER_NO && x.PHOTO_STATUS != ADDT21.STATUSVal.Disabled)


            //       .Join(db.ADDT21, ah => ah.ART_GALLERY_NO, bd => bd.ART_GALLERY_NO, (ah, bd) => new
            //       {
            //           ART_GALLERY_NO = ah.ART_GALLERY_NO,
            //           USER_NO = ah.PHOTO_USER_NO,
            //           SCHOOL_NO = ah.PHOTO_SCHOOL_NO

            //       }).GroupBy(g => new
            //       { g.ART_GALLERY_NO }).Select(g => new ADDT22
            //       { ART_GALLERY_NO = g.Key.ART_GALLERY_NO }).AsEnumerable();

                    // modeltemp = zZZI34.GetArtGalleryWorkListData(modeltemp, user, ref db);
                    List <ADDT22> aDDT22s = new List<ADDT22>();
            string SNAME = "";
            SNAME = db.HRMT01.Where(x => x.SCHOOL_NO == user.SCHOOL_NO && x.USER_NO == user.USER_NO && x.USER_STATUS != UserStaus.Invalid).Select(x => x.NAME).FirstOrDefault();
            SNAME = SNAME + ".zip";
            aDDT22s = db.ADDT22.ToList();
            int i = 0;
            List<ADDT22> ADDT22Item = new List<ADDT22>();
            //ADDT22Item = ART_GALLERY_NOItemlist.ToList();
            ART_GALLERY_NOItem = data.Distinct().ToList();
            foreach (var item in ART_GALLERY_NOItem) {
                ArrayList filelistItem = new ArrayList();
                

               string ART_GALLERY_NO = item;
           
               
                string PathI = RealUploadImageRoot + $@"ArtGallery\{SCHOOL_NO}\{ART_GALLERY_NO}";
                //filelistItem = GetFiles(PathI);
                // CreateDirectory(tempPath);
            
               
                foreach (var FItem in aDDT22s.Where(x=>x.ART_GALLERY_NO== ART_GALLERY_NO).ToList()) {
                    i++;
                    DateTime COVER_FILETime = new DateTime();
                    string ReturnImgUrl = string.Empty;
                    ReturnImgUrl = UploadImageRoot + $@"ArtGallery\{SCHOOL_NO}\{ART_GALLERY_NO}";
                    string RealReturnImgUrl = string.Empty;

                    RealReturnImgUrl = RealUploadImageRoot + $@"ArtGallery\{SCHOOL_NO}\{ART_GALLERY_NO}";
                    COVER_FILETime =
                    (DateTime)FItem.CRE_DATE;
                    string COVER_FILETimestr = "";
                    FItem.PHOTO_CLASS_NO.Substring(0, 1);
                    if (FItem.PHOTO_SEMESTER == 1)
                    {
                        COVER_FILETimestr = FItem.PHOTO_CLASS_NO.Substring(0, 1).ToString() + "上";

                    }
                    else {
                        COVER_FILETimestr = FItem.PHOTO_CLASS_NO.Substring(0, 1).ToString() + "下";

                    }
                    COVER_FILETimestr = COVER_FILETimestr+ "_" + FItem.PHOTO_SUBJECT+"_"+COVER_FILETime.Year.ToString();
                    string NewImg = FItem.PHOTO_FILE;
                    ReturnImgUrl= ReturnImgUrl+ @"\"+NewImg;
                    RealReturnImgUrl= RealReturnImgUrl + @"\" + NewImg;
                    string newpath = "";
                    string newpathReal = "";

                    newpath = UploadImageRoot + $@"ArtGallery\{SCHOOL_NO}\{USER_N}";
                    newpathReal = RealUploadImageRoot + $@"ArtGallery\{SCHOOL_NO}\{USER_N}";
                    if (!Directory.Exists(realdicnewpath)) {
                        Directory.CreateDirectory(realdicnewpath);
                    }
                    if (!Directory.Exists(newpathReal))
                    {
                        Directory.CreateDirectory(newpathReal);
                    }
                    if (System.IO.File.Exists(System.Web.HttpContext.Current.Server.MapPath(ReturnImgUrl)))
                    {
                        string FilENAME = Path.GetFileName(ReturnImgUrl);
                        Uri.EscapeUriString(UrlCustomHelper.Url_Content(ReturnImgUrl));
                        FileInfo file = new FileInfo(RealReturnImgUrl);
                      string newFZIPNAME= COVER_FILETimestr + Path.GetExtension(FItem.PHOTO_FILE);
                        if (System.IO.File.Exists(System.Web.HttpContext.Current.Server.MapPath(newpath + @"\" + newFZIPNAME)))
                        {
                            newFZIPNAME = newFZIPNAME.Replace(Path.GetExtension(newFZIPNAME) ,@"_" + i.ToString() + Path.GetExtension(newFZIPNAME));
                           // newFZIPNAME = newFZIPNAME + @"_" + i.ToString();

                            if (!System.IO.File.Exists(System.Web.HttpContext.Current.Server.MapPath(newpath + @"\" + newFZIPNAME)))
                            {
                                for (int j = 50; j < 50; j++)
                                {

                                    newFZIPNAME = newFZIPNAME.Replace(Path.GetExtension(newFZIPNAME), @"_" + i.ToString() + Path.GetExtension(newFZIPNAME));
                                    if (!System.IO.File.Exists(System.Web.HttpContext.Current.Server.MapPath(newpath + @"\" + newFZIPNAME)))
                                    {


                                        break;
                                    }
                                    file.CopyTo(newpathReal + @"\" + newFZIPNAME);
                                }
                            }
                               
                                
                            else {

                                file.CopyTo(newpathReal + @"\" + newFZIPNAME);
                            }
                           
                        }
                        else {
                            file.CopyTo(newpathReal + @"\" + newFZIPNAME);
                        }
                      
                    }
                 

                }
            }
            string startPath = realdicnewpath;
            string strPATH = "";

            strPATH = Guid.NewGuid().ToString("N") + ".zip";
            string strTMPFile = dicnewZippath + @"\" + strPATH;
           
            string zipPath = realdicnewZippath + @"\" + strPATH;
            if (Directory.Exists(zipPath))
            {
                System.IO.File.Delete(zipPath);
            }
          
            System.IO.Compression.ZipFile.CreateFromDirectory(startPath, zipPath);
            if (Directory.Exists(startPath))
            {

                foreach (string d in Directory.GetFileSystemEntries(startPath))
                {
                    if (System.IO.File.Exists(d))
                    {
                        FileInfo fi = new FileInfo(d);
                        if (fi.Attributes.ToString().IndexOf("ReadOnly") != -1)
                            fi.Attributes = FileAttributes.Normal;
                        System.IO.File.Delete(d);//直接删除其中的文件   
                    }
                  
                }
                Directory.Delete(startPath);//删除已空文件夹   
               
              //  System.IO.File.Delete(dicnewpath);
            }
            var file2= new System.IO.FileStream(
      System.Web.HttpContext.Current.Server.MapPath(strTMPFile),
       System.IO.FileMode.Open,
       System.IO.FileAccess.Read,
       System.IO.FileShare.Read,
       4096,
       System.IO.FileOptions.DeleteOnClose);
            return File(file2, "application/zip", SNAME);
          //  return File(strTMPFile, "application/vnd.ms-excel", strPATH);
            //  ZipDirectory(dicnewpath, dicnewZippath, "線上藝廊", false);
        }
        /// <summary>
        /// ZIP：压缩文件夹
        /// add yuangang by 2016-06-13
        /// </summary>
        /// <param name="DirectoryToZip">需要压缩的文件夹（绝对路径）</param>
        /// <param name="ZipedPath">压缩后的文件路径（绝对路径）</param>
        /// <param name="ZipedFileName">压缩后的文件名称（文件名，默认 同源文件夹同名）</param>
        /// <param name="IsEncrypt">是否加密（默认 加密）</param>
        public void ZipDirectory(string DirectoryToZip, string ZipedPath, string ZipedFileName = "", bool IsEncrypt = false)
        {
            //如果目录不存在，则报错
            if (!System.IO.Directory.Exists(DirectoryToZip))
            {
                throw new System.IO.FileNotFoundException("指定的目录: " + DirectoryToZip + " 不存在!");
            }

            //文件名称（默认同源文件名称相同）
            string ZipFileName =
                 string.IsNullOrEmpty(ZipedFileName) ? ZipedPath + "\\" + new DirectoryInfo(DirectoryToZip).Name + ".zip" : ZipedPath + "\\" + ZipedFileName + ".zip";

            using (System.IO.FileStream ZipFile = System.IO.File.Create(ZipFileName))
            {
                using (ICSharpCode.SharpZipLib.Zip.ZipOutputStream s = new ICSharpCode.SharpZipLib.Zip.ZipOutputStream(ZipFile))
                {
                    if (IsEncrypt)
                    {
                        //压缩文件加密
                        s.Password = "123";
                    }
                    ZipSetp(DirectoryToZip, s, "");
                }
            }
        }
        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult ArtGalleryListMid(ZZZI34IndexViewModel model)
        {


            ZZZI34WorkIndexViewModel modelMId = new ZZZI34WorkIndexViewModel();

            if (model == null) {

                model = new ZZZI34IndexViewModel();

            }
            modelMId.Search= model.Search;
           
            this.Shared(Bre_Name + "-全部藝廊");
            return View("ArtGalleryList",modelMId);
        }

        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult ArtGalleryList(ZZZI34IndexViewModel model)
        {
            ZZZI34IndexViewModel model2 = new ZZZI34IndexViewModel();
            this.Shared(Bre_Name + "-全部藝廊");
          
            return View(model);
        }
       
        //[CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult QRCODEUrl(string WhereART_GALLERY_NO,string PHOTO_USER_NO, string PHOTO_SCHOOL_NO)
        {
            this.Shared();
            ECOOL_APP.EF.ZZZI34IndexViewModel model = new ZZZI34IndexViewModel();
           
            model.Search = new ZZZI34SearchViewModel();
            if (!string.IsNullOrEmpty(PHOTO_USER_NO) && !string.IsNullOrEmpty(PHOTO_SCHOOL_NO))
            {

                model.Search.WhereSTATUS = "1";

            }
            model.Search.WhereART_GALLERY_NO = WhereART_GALLERY_NO;
            model.Search.WhereUSER_NO = PHOTO_USER_NO;
            model.Search.WhereSCHOOL_NO = PHOTO_SCHOOL_NO;
            
           return View(model);
        }
         /// <summary>
        /// 递归遍历目录
        /// add yuangang by 2016-06-13
        /// </summary>
        private static void ZipSetp(string strDirectory, ICSharpCode.SharpZipLib.Zip.ZipOutputStream s, string parentPath)
        {
            if (strDirectory[strDirectory.Length - 1] != Path.DirectorySeparatorChar)
            {
                strDirectory += Path.DirectorySeparatorChar;
            }
            ICSharpCode.SharpZipLib.Checksums.Crc32 crc = new ICSharpCode.SharpZipLib.Checksums.Crc32();

            string[] filenames = Directory.GetFileSystemEntries(strDirectory);

            foreach (string file in filenames)// 遍历所有的文件和目录
            {

                if (Directory.Exists(file))// 先当作目录处理如果存在这个目录就递归Copy该目录下面的文件
                {
                    string pPath = parentPath;
                    pPath += file.Substring(file.LastIndexOf("\\") + 1);
                    pPath += "\\";
                    ZipSetp(file, s, pPath);
                }

                else // 否则直接压缩文件
                {
                    //打开压缩文件
                    //using (FileStream fs = File.OpenRead(file))
                    using (FileStream fs = new FileStream(file,
                        FileMode.Open, FileAccess.Read))
                    {

                        byte[] buffer = new byte[fs.Length];
                        fs.Read(buffer, 0, buffer.Length);

                        string fileName = parentPath + file.Substring(file.LastIndexOf("\\") + 1);
                        var entry = new ICSharpCode.SharpZipLib.Zip.ZipEntry(fileName);

                        entry.DateTime = DateTime.Now;
                        entry.Size = fs.Length;

                        fs.Close();

                        crc.Reset();
                        crc.Update(buffer);

                        entry.Crc = crc.Value;
                        s.PutNextEntry(entry);

                        s.Write(buffer, 0, buffer.Length);
                    }
                }
            }
        }
        public ActionResult OneIndex(ZZZI34WorkIndexViewModel model)
        {
            this.Shared();
            return View(model);
        }
        public ActionResult OneIndexForArGallery(ZZZI34WorkIndexViewModel model)
        {
            this.Shared();
            ZZZI34WorkIndexViewModel temp = new ZZZI34WorkIndexViewModel();
            
            if (!string.IsNullOrWhiteSpace(model.WhereART_GALLERY_NOGallaryN1))
            {
                model.Search = new ZZZI34SearchViewModel();
                model.Search.WhereART_GALLERY_NO = model.WhereART_GALLERY_NOGallaryN1;
                if (TempData["model"] != null)
                {


                    temp = (ZZZI34WorkIndexViewModel)TempData["model"];
                    if (temp.Search != null) {

                        model.Search.WhereUSER_NO = temp.Search?.WhereUSER_NO;
                        model.Search.WhereSCHOOL_NO = temp.Search?.WhereSCHOOL_NO;
                    }
         
                }
            }

            return View(model);
        }
       
        public ActionResult _WorkListPageContent(ZZZI34IndexViewModel model)
        {
            ViewBag.WinOpenShareUrlLink = UrlCustomHelper.GetOwnWebUri() + $@"/EcoolWeb//{Bre_NO}/QRCODEUrl";
            string WhereFrom = Request.QueryString["WhereART_GALLERY_NO"];
            string WhereUSER_NO = Request.QueryString["PHOTO_USER_NO"];
            string WhereSCHOOL_NO = Request.QueryString["PHOTO_SCHOOL_NO"];
       
            this.Shared();
            if (model == null) model = new ZZZI34IndexViewModel();
            if (model.Search == null) model.Search = new ZZZI34SearchViewModel();
            if (WhereSCHOOL_NO != null) {
                model.Search.WhereSCHOOL_NO = WhereSCHOOL_NO;

            }
         
            //批閱
            ViewBag.VisibleVerify = (PermissionService.GetPermission_Use_YN(Bre_NO, "Verify", user?.SCHOOL_NO, user?.USER_NO) == "Y" ? true : false);

            if (string.IsNullOrWhiteSpace(model.IsPostBack))
            {
                if (model.Search.WhereSYEAR == null && string.IsNullOrWhiteSpace(WhereFrom))
                {
                    model.Search.WhereSYEAR = SysHelper.GetNowSYear(DateTime.Now);
                }
            }
            if (!string.IsNullOrWhiteSpace(WhereFrom))
            {
                model.Search.WhereART_GALLERY_NO = WhereFrom;
            }
            if (!string.IsNullOrWhiteSpace(WhereUSER_NO) )
            {
                model.Search.WhereUSER_NO = WhereUSER_NO;
            }
            //if (!string.IsNullOrWhiteSpace(model.Search.WhereUSER_NO)) {


            //}
                if (WhereUSER_NO != null)
            {
                model.Search.WhereMyWork = true;
            }
            if (!string.IsNullOrWhiteSpace(model.Search.WhereSCHOOL_NO))
            {
                if (!string.IsNullOrWhiteSpace(model.Search.WhereART_GALLERY_NO))
                {
                    ADDT21 add21TEMP = new ADDT21();
                    add21TEMP = db.ADDT21.Where(x => x.ART_GALLERY_NO == model.Search.WhereART_GALLERY_NO && x.STATUS != ADDT21.STATUSVal.Disabled).FirstOrDefault();
                
                    if (add21TEMP.SCHOOL_NO != model.Search.WhereSCHOOL_NO)
                    {

                        model.Search.WhereSCHOOL_NO = add21TEMP.SCHOOL_NO;
                        SCHOOL_NO = add21TEMP.SCHOOL_NO;


                    }
                }

            }
            if (string.IsNullOrWhiteSpace(model.Search.WhereSCHOOL_NO))
            {

                if (string.IsNullOrWhiteSpace(SCHOOL_NO))
                {
                    if (!string.IsNullOrWhiteSpace(model.Search.WhereART_GALLERY_NO))
                    {
                        ADDT21 add21TEMP = new ADDT21();
                        add21TEMP = db.ADDT21.Where(x => x.ART_GALLERY_NO == model.Search.WhereART_GALLERY_NO && x.STATUS != ADDT21.STATUSVal.Disabled).FirstOrDefault();
                        model.Search.WhereSCHOOL_NO = add21TEMP.SCHOOL_NO;
                        SCHOOL_NO= add21TEMP.SCHOOL_NO;
                    }
                    

                }
                else {
                    if (!string.IsNullOrWhiteSpace(model.Search.WhereART_GALLERY_NO))
                    {
                        ADDT21 add21TEMP = new ADDT21();
                        add21TEMP = db.ADDT21.Where(x => x.ART_GALLERY_NO == model.Search.WhereART_GALLERY_NO && x.STATUS != ADDT21.STATUSVal.Disabled).FirstOrDefault();
                      
                        if (add21TEMP.SCHOOL_NO != model.Search.WhereSCHOOL_NO)
                        {

                            model.Search.WhereSCHOOL_NO = add21TEMP.SCHOOL_NO;
                            SCHOOL_NO = add21TEMP.SCHOOL_NO;


                        }
                        else
                        {
                            model.Search.WhereSCHOOL_NO = SCHOOL_NO;
                        }

                    }
                    else
                    {
                        model.Search.WhereSCHOOL_NO = SCHOOL_NO;
                    }
                  
                }
            }

            if (IsAdmin == false && ViewBag.VisibleVerify == false && string.IsNullOrWhiteSpace(WhereUSER_NO)==true && model.Search!=null&& model.Search.WhereUSER_NO == null)
            {
                model.Search.WhereUSER_NO = USER_NO;
            }

          

            if ((model.WhereVerify ?? false))
            {
                model.Search.WhereSTATUS = ADDT21.STATUSVal.Verification;
            }

            ViewBag.SYEARItems = HRMT01.GetSYearsItems(model.Search.WhereSYEAR.ToString(), ref db);

            ViewBag.GradeItem = HRMT01.GetGradeItems(model.Search.WhereGrade);

            ViewBag.ClassItems = HRMT01.GetClassListData(model.Search.WhereSCHOOL_NO, model.Search.WhereGrade, model.Search.WhereCLASS_NO, ref db)
                .Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.Search.WhereGrade });

            ViewBag.ART_GALLERY_TYPE = ADDT21.ART_GALLERY_TYPE_VAL.SelectItem(model.Search.WhereART_GALLERY_TYPE);
            var StatusItem = ADDT21.STATUSVal.SelectItem(model.Search.WhereSTATUS, true);

            if (!IsAdmin)
            {
                StatusItem.Remove(StatusItem.Where(c => c.Value == ADDT21.STATUSVal.Disabled).Single());
            }
            ViewBag.StatusItem = StatusItem;

            model = Service.GetArtGalleryListData(model, user, ref db, ViewBag.VisibleVerify);
            return PartialView(model);
        }
        public ActionResult _PageContent(ZZZI34IndexViewModel model)
        {
            ViewBag.WinOpenShareUrlLink = UrlCustomHelper.GetOwnWebUri() + $@"/EcoolWeb//{Bre_NO}/QRCODEUrl";
            string WhereFrom = Request.QueryString["WhereFrom"];

            this.Shared();
            if (model == null) model = new ZZZI34IndexViewModel();
            if (model.Search == null) model.Search = new ZZZI34SearchViewModel();

            //批閱
            ViewBag.VisibleVerify = (PermissionService.GetPermission_Use_YN(Bre_NO, "Verify", user?.SCHOOL_NO, user?.USER_NO) == "Y" ? true : false);

            if (string.IsNullOrWhiteSpace(model.IsPostBack))
            {
                if (model.Search.WhereSYEAR == null && string.IsNullOrWhiteSpace(WhereFrom))
                {
                    model.Search.WhereSYEAR = SysHelper.GetNowSYear(DateTime.Now);
                }
            }
            if (!string.IsNullOrWhiteSpace(WhereFrom))
            {
                if (model.WhereFrom == "Tasklist")
                {
                    string str = PermissionService.GetPermission_Use_YN("ZZZI34", "Verify", user.SCHOOL_NO, user.USER_NO);

                    if (str == "Y")
                    {
                        model.Search.WhereUSER_NO = "";
                    }
                    else
                    {
                        model.Search.WhereUSER_NO = user.USER_NO;

                    }
                }
                else {
                    model.Search.WhereUSER_NO = user.USER_NO;

                }
              
            }
            if (model.WhereMyWork != null)
            {
                model.Search.WhereMyWork = model.WhereMyWork;
            }

            if (string.IsNullOrWhiteSpace(model.Search.WhereSCHOOL_NO))
            {
                model.Search.WhereSCHOOL_NO = SCHOOL_NO;
            }

            //if (IsAdmin == false && ViewBag.VisibleVerify == false)
            //{
            //    model.Search.WhereUSER_NO = USER_NO;
            //}

            //if ((model.Search.WhereMyWork ?? false))
            //{
            //    model.Search.WhereUSER_NO = USER_NO;
            //}

            if ((model.WhereVerify ?? false))
            {
                model.Search.WhereSTATUS = ADDT21.STATUSVal.Verification;
            }

            ViewBag.SYEARItems = HRMT01.GetSYearsItems(model.Search.WhereSYEAR.ToString(), ref db);

            ViewBag.GradeItem = HRMT01.GetGradeItems(model.Search.WhereGrade);

            ViewBag.ClassItems = HRMT01.GetClassListData(model.Search.WhereSCHOOL_NO, model.Search.WhereGrade, model.Search.WhereCLASS_NO, ref db)
                .Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.Search.WhereGrade });

            ViewBag.ART_GALLERY_TYPE = ADDT21.ART_GALLERY_TYPE_VAL.SelectItem(model.Search.WhereART_GALLERY_TYPE);
            var StatusItem = ADDT21.STATUSVal.SelectItem(model.Search.WhereSTATUS, true);

            if (!IsAdmin)
            {
                StatusItem.Remove(StatusItem.Where(c => c.Value == ADDT21.STATUSVal.Disabled).Single());
            }
            ViewBag.StatusItem = StatusItem;

            model = Service.GetArtGalleryListData(model, user, ref db, ViewBag.VisibleVerify);
            return PartialView(model);
        }
        [CheckPermission(CheckACTION_ID = "Edit")] //檢查權限
        public ActionResult StudentUploadZip(ZZZI34WorkIndexViewModel model) {


            UserProfile user = UserProfileHelper.Get();
            string Class_No = (Request["Class_No"] != null) ? Request["Class_No"] : string.Empty;
            if (user.USER_TYPE == "S") {

                var items = db.HRMT01.Where(u => u.SCHOOL_NO == user.SCHOOL_NO && u.CLASS_NO ==user.CLASS_NO&& u.USER_STATUS == UserStaus.Enabled).
               Select(x => new SelectListItem { Text = x.CLASS_NO, Value = x.CLASS_NO }).Distinct().OrderBy(o => o.Value);
                IEnumerable<SelectListItem> USER_NOItems = null;
                USER_NOItems = HRMT01.GetUserNoListData(user.SCHOOL_NO, user.CLASS_NO,user.USER_NO, ref db);
                ViewBag.USER_NOItems = USER_NOItems;
                ViewBag.ClassNoItem = items;
            }
            if (model != null && model.ISPersonal_YN == "Y")
            {
                return View(model);
            }
            else
            {
                return View();
            }
        }
        [CheckPermission(CheckACTION_ID = "UPLOAD", CheckBRE_NO = "ZZZI341")] //檢查權限
        public ActionResult UPLOAD2(ZZZI34WorkIndexViewModel model)
        {
            UserProfile user = UserProfileHelper.Get();
            return View(model);
        }

        [CheckPermission(CheckACTION_ID = "UPLOAD", CheckBRE_NO = "ZZZI341")] //檢查權限
        public ActionResult UPLOAD1(ZZZI34WorkIndexViewModel model)
        {
            UserProfile user = UserProfileHelper.Get();
            string Class_No = (Request["Class_No"] != null) ? Request["Class_No"] : string.Empty;
            if (string.IsNullOrEmpty(user.TEACH_CLASS_NO)&&string.IsNullOrEmpty(Class_No))
            {
                var items = db.HRMT01.Where(u => u.SCHOOL_NO == user.SCHOOL_NO && string.IsNullOrEmpty(u.CLASS_NO) == false && u.USER_STATUS == UserStaus.Enabled).
                Select(x => new SelectListItem { Text = x.CLASS_NO, Value = x.CLASS_NO }).Distinct().OrderBy(o => o.Value);
                //SelectListItem item = new SelectListItem();
                //item.Text = "請選擇班級";
                //items.
                IEnumerable<SelectListItem> USER_NOItems = null;
                USER_NOItems = HRMT01.GetUserNoListData(user.SCHOOL_NO, user.TEACH_CLASS_NO, "", ref db);
                ViewBag.USER_NOItems = USER_NOItems;
                ViewBag.ClassNoItem = items;
            }
            else
            {
                List<SelectListItem> ClassList = new List<SelectListItem>();
                
                SelectListItem it = new SelectListItem() { Text = user.TEACH_CLASS_NO, Value = user.TEACH_CLASS_NO };
                ClassList.Add(it);
                ViewBag.ClassNoItem = ClassList;
                IEnumerable<SelectListItem> USER_NOItems = null;
                USER_NOItems = HRMT01.GetUserNoListData(user.SCHOOL_NO, user.TEACH_CLASS_NO, "", ref db);
                ViewBag.USER_NOItems = USER_NOItems;
            }
            if (model != null && model.ISPersonal_YN == "Y")
            {
                return View(model);
            }
            else
            {
                return View();
            }
        }
        public ActionResult Index2() {
            ViewBag.Panel_Title = "批次線上藝廊";
            return View();
        }
        public ActionResult Index3()
        {
            ViewBag.Panel_Title = "學生批次線上藝廊";
            return View();
        }
        [CheckPermission(CheckACTION_ID = "UPLOAD", CheckBRE_NO = "ZZZI341")] //檢查權限
        public ActionResult UPLOAD(ZZZI34WorkIndexViewModel model)
        {
            UserProfile user = UserProfileHelper.Get();

            if (string.IsNullOrEmpty(user.TEACH_CLASS_NO))
            {
                var items = db.HRMT01.Where(u => u.SCHOOL_NO == user.SCHOOL_NO && string.IsNullOrEmpty(u.CLASS_NO) == false && u.USER_STATUS == UserStaus.Enabled).
                Select(x => new SelectListItem { Text = x.CLASS_NO, Value = x.CLASS_NO }).Distinct().OrderBy(o => o.Value);
                //SelectListItem item = new SelectListItem();
                //item.Text = "請選擇班級";
                //items.
                IEnumerable<SelectListItem> USER_NOItems = null;
                USER_NOItems = HRMT01.GetUserNoListData(user.SCHOOL_NO, user.TEACH_CLASS_NO, "", ref db);
                ViewBag.USER_NOItems = USER_NOItems;
                ViewBag.ClassNoItem = items;
            }
            else
            {
                List<SelectListItem> ClassList = new List<SelectListItem>();
                SelectListItem it = new SelectListItem() { Text = user.TEACH_CLASS_NO, Value = user.TEACH_CLASS_NO };
                ClassList.Add(it);
                ViewBag.ClassNoItem = ClassList;
                IEnumerable<SelectListItem> USER_NOItems = null;
                USER_NOItems = HRMT01.GetUserNoListData(user.SCHOOL_NO, user.TEACH_CLASS_NO, "", ref db);
                ViewBag.USER_NOItems = USER_NOItems;
            }
            if (model != null && model.ISPersonal_YN == "Y")
            {
                var items = db.HRMT01.Where(u => u.SCHOOL_NO == user.SCHOOL_NO && string.IsNullOrEmpty(u.CLASS_NO) == false && u.USER_STATUS == UserStaus.Enabled).
     Select(x => new SelectListItem { Text = x.CLASS_NO, Value = x.CLASS_NO }).Distinct().OrderBy(o => o.Value);
                //List<SelectListItem> ClassList = new List<SelectListItem>();
                //ViewBag.ClassNoItem = ClassList;
                IEnumerable<SelectListItem> USER_NOItems = null;
                if (model != null && model.Search != null && model.Search.WhereCLASS_NO != null) {
                    SelectListItem selectItem = new SelectListItem();
                    //foreach (var i in items.Where(x => x.Text == model.Search.WhereCLASS_NO).ToList()) {


                    //    i.Selected = true;
                    //    selectItem = i;
                    //}
                   items.First(x => x.Value == model.Search.WhereCLASS_NO).Selected = true;
                 
                    USER_NOItems = HRMT01.GetUserNoListData(user.SCHOOL_NO, model.Search.WhereCLASS_NO, "", ref db);

                }
              
             
                ViewBag.USER_NOItems = USER_NOItems;
                ViewBag.ClassNoItem = items;
                ViewBag.ISPersonal_YN = "Y";
                return View(model);
            }
            else
            {
                return View();
            }
        }
        public ActionResult StudentUPLOADCHECK(ZZZI34EditViewModel model)
        {
            UserProfile user = UserProfileHelper.Get();
            ADDT21 aDDT21 = new ADDT21();
            aDDT21 = db.ADDT21.Where(x => x.ART_GALLERY_NO == model.Main.ART_GALLERY_NO).FirstOrDefault();
            db.ADDT21.Remove(aDDT21);
            if (string.IsNullOrEmpty(user.TEACH_CLASS_NO))
            {
                var items = db.HRMT01.Where(u => u.SCHOOL_NO == user.SCHOOL_NO && string.IsNullOrEmpty(u.CLASS_NO) == false && u.USER_STATUS == UserStaus.Enabled && u.USER_NO==user.USER_NO).
                Select(x => new SelectListItem { Text = x.CLASS_NO, Value = x.CLASS_NO }).Distinct().OrderBy(o => o.Value);
                //SelectListItem item = new SelectListItem();
                //item.Text = "請選擇班級";
                //items.

                IEnumerable<SelectListItem> USER_NOItems = null;
                USER_NOItems = HRMT01.GetUserNoListData(user.SCHOOL_NO, user.CLASS_NO, user.USER_NO, ref db);
                ViewBag.USER_NOItems = USER_NOItems;
                ViewBag.ClassNoItem = items;
            }
            else
            {
                List<SelectListItem> ClassList = new List<SelectListItem>();
                SelectListItem it = new SelectListItem() { Text = user.TEACH_CLASS_NO, Value = user.TEACH_CLASS_NO };
                IEnumerable<SelectListItem> USER_NOItems = null;
                USER_NOItems = HRMT01.GetUserNoListData(user.SCHOOL_NO, user.CLASS_NO, user.USER_NO, ref db);
                ViewBag.USER_NOItems = USER_NOItems;
                ClassList.Add(it);
                ViewBag.ClassNoItem = ClassList;
            }
            try
            {
                db.SaveChanges();

                return View("StudentUploadZip");
            }
            catch (Exception e)
            {
                return View("StudentUploadZip");
            }
        }
        public ActionResult UPLOADCHECK(ZZZI34EditViewModel model)
        {
            UserProfile user = UserProfileHelper.Get();
            ADDT21 aDDT21 = new ADDT21();
            aDDT21 = db.ADDT21.Where(x => x.ART_GALLERY_NO == model.Main.ART_GALLERY_NO).FirstOrDefault();
            db.ADDT21.Remove(aDDT21);
            if (string.IsNullOrEmpty(user.TEACH_CLASS_NO))
            {
                var items = db.HRMT01.Where(u => u.SCHOOL_NO == user.SCHOOL_NO && string.IsNullOrEmpty(u.CLASS_NO) == false && u.USER_STATUS == UserStaus.Enabled).
                Select(x => new SelectListItem { Text = x.CLASS_NO, Value = x.CLASS_NO }).Distinct().OrderBy(o => o.Value);
                //SelectListItem item = new SelectListItem();
                //item.Text = "請選擇班級";
                //items.
                ViewBag.ClassNoItem = items;
            }
            else
            {
                List<SelectListItem> ClassList = new List<SelectListItem>();
                SelectListItem it = new SelectListItem() { Text = user.TEACH_CLASS_NO, Value = user.TEACH_CLASS_NO };
                ClassList.Add(it);
                ViewBag.ClassNoItem = ClassList;
            }
            try
            {
                db.SaveChanges();

                return View("UPLOAD");
            }
            catch (Exception e)
            {
                return View("UPLOAD");
            }
        }

        [HttpPost]
        [CheckPermission(CheckACTION_ID = "Edit")] //檢查權限
        public ActionResult StudentUploadZip(ZZZI34EditViewModel model, HttpPostedFileBase file, HttpPostedFileBase file2)
        {

            ViewBag.BRE_NO = Bre_NO;
            UserProfile User = UserProfileHelper.Get();
            this.Shared();
            int SYear;
            int Semesters;
            SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);
            string DefaultSCHOOL_NO = UserProfileHelper.GetSchoolNo();
            string SCHOOL_NOUSER = "";
            SCHOOL_NOUSER = user.SCHOOL_NO + "_" + user.USER_NO;
            bool IsOkForArt = true;
            //檢查是否本學期超過
            if (User.USER_TYPE == UserType.Student) {
                int SemeterCount = 0;
                SemeterCount = db.ADDT21.Where(x => x.SCHOOL_NO == user.SCHOOL_NO && x.CRE_PERSON == SCHOOL_NOUSER && x.SEMESTER == Semesters && x.SYEAR == SYear && x.WORK_TYPE == ADDT21.ART_GALLERY_TYPE_VAL.Personal).Count();
                if (SemeterCount > 3) {
                    IsOkForArt = false;
                    ModelState.AddModelError("相簿首新增錯誤", "「每學期半年」最多3次畫展");
                }

            }
            byte? ModeVal = 0;
            string stdUserNo = "";
            string ART_SUBJECT = (Request["ART_SUBJECT"] != null) ? Request["ART_SUBJECT"] : string.Empty;
            string Class_No = (Request["Class_No"] != null) ? Request["Class_No"] : string.Empty;
            stdUserNo = (Request["USER_NO"] != null) ? Request["USER_NO"] : string.Empty;
            string Main_ART_DESC = (Request["Main_ART_DESC"] != null) ? Request["Main_ART_DESC"].ToString() : string.Empty;
            string radFileType = (Request["radFileType"] != null) ? Request["radFileType"].ToString() : string.Empty;
            ViewBag.Panel_Title = " 批次線上藝廊上傳- " + GetModeName(ModeVal) + ".新增(步驟1)";
            model.Search = new ZZZI34SearchViewModel();

            model.Search.WhereCLASS_NO = Class_No;
            
            ViewBag.ClassItems = HRMT01.GetClassListData(DefaultSCHOOL_NO, "", model.Search.WhereCLASS_NO, ref db);
            if (file == null)
            {
                ModelState.AddModelError("file", "*請上傳檔案");
            }
            else
            {
                if (file.ContentLength == 0)
                {
                    ModelState.AddModelError("file", "*請上傳檔案");
                }
                else
                {
                    if (file.ContentType != "application/x-zip-compressed" && file.ContentType != "application/octet-stream" && file.ContentType != "application/zip")
                    {
                        ModelState.AddModelError("file", "*請上傳檔案[ZIP]格式");
                    }
                }
            }
            ZZZI34Service zZZI34Service = new ZZZI34Service();
          
            string Message = "";
           

            ADDT21 SaveUp = null;
            SaveUp = new ADDT21();
            model.Main = new ZZZI34EditMainViewModel();
            if (model.Main?.ART_GALLERY_NO == null)
            {
                this.Shared(Bre_Name + "-新增封面");

                model.Main = new ZZZI34EditMainViewModel();
                model.Main.ART_GALLERY_TYPE = user?.USER_TYPE == UserType.Student ? ADDT21.ART_GALLERY_TYPE_VAL.Personal : ADDT21.ART_GALLERY_TYPE_VAL.Campus;
            }
            model.Main.ART_SUBJECT = ART_SUBJECT;
            model.Main.ART_DESC = Main_ART_DESC;

            model.Main.WORK_TYPE = "P";
            if ((SaveUp == null || SaveUp.ART_GALLERY_NO == null)&& IsOkForArt)
            {

                SaveUp.ART_GALLERY_NO = Guid.NewGuid().ToString("N");
                SaveUp.ART_GALLERY_TYPE = model.Main.ART_GALLERY_TYPE;
                SaveUp.ART_SUBJECT = model.Main.ART_SUBJECT;
                SaveUp.ART_DESC = model.Main.ART_DESC;
                SaveUp.WORK_TYPE = model.Main.WORK_TYPE;

                if (SaveUp.ART_GALLERY_TYPE == ADDT21.ART_GALLERY_TYPE_VAL.Campus)
                {
                    SaveUp.STATUS = ADDT21.STATUSVal.NotStarted;
                }
                else
                {
                    SaveUp.STATUS = ADDT21.STATUSVal.Verification;
                }

                SaveUp.SCHOOL_NO = User.SCHOOL_NO;
                SaveUp.USER_NO = User.USER_NO;
                SaveUp.NAME = User.NAME;
                SaveUp.SNAME = User.SNAME;
                SaveUp.CLASS_NO = User.CLASS_NO;
                SaveUp.SYEAR = Convert.ToByte(SYear);
                SaveUp.SEMESTER = Convert.ToByte(Semesters);
                SaveUp.CRE_PERSON = User.USER_KEY;
                SaveUp.CRE_DATE = DateTime.Now;
                SaveUp.CHG_PERSON = User.USER_KEY;
                SaveUp.CHG_DATE = DateTime.Now;

                SaveUp.COVER_FILE = zZZI34Service.UpLoadFile(SaveUp.SCHOOL_NO, SaveUp.ART_GALLERY_NO, file2, ref Message);
                if (string.IsNullOrWhiteSpace(Message) == false)
                {
                    ModelState.AddModelError("相簿首頁圖", Message);
                }

                //找老師
                HRMT03 TeachInfo =
                    db.HRMT03.Where(a => a.SCHOOL_NO == SaveUp.SCHOOL_NO && a.CLASS_NO == SaveUp.CLASS_NO).FirstOrDefault();
                if (TeachInfo != null)
                {
                    SaveUp.VERIFIER = TeachInfo.TEACHER_NO;
                }

                db.ADDT21.Add(SaveUp);
            }
            try
            {
                db.SaveChanges();
            }
            catch (Exception ex)
            {
                Message = "系統發生錯誤;原因:" + ex.Message;
                ModelState.AddModelError("相簿首頁儲存錯誤", Message);
            }
            if (ModelState.IsValid) //沒有錯誤
            {

                if (db.Database.Connection.State != ConnectionState.Open) db.Database.Connection.Open();
                //ZIP 檔上傳
                string fileName = Path.GetFileName(file.FileName);

                //建立上傳暫存路徑

                string NowFile = DateTime.Now.ToString("yyyyMMdd");

                model.Search.TEMP_BATCH_KEY = Session.SessionID;

                string UploadImageRoot = System.Web.Configuration.WebConfigurationManager.AppSettings["UploadImageRoot"];
                if (UploadImageRoot == null) UploadImageRoot = @"~\Content\";
                string RealUploadImageRoot = Request.MapPath(UploadImageRoot);

                string SCHOOL_NOPath = RealUploadImageRoot + @"\" + Bre_NO + @"\" + DefaultSCHOOL_NO + @"\";
                string ALLPath = SCHOOL_NOPath + @"\" + NowFile + @"\";
                if (!Directory.Exists(ALLPath))
                {
                    if (Directory.Exists(SCHOOL_NOPath))
                    {
                        Directory.Delete(SCHOOL_NOPath, true);
                    }

                    Directory.CreateDirectory(ALLPath);
                }

                string path = ALLPath + fileName;

                file.SaveAs(path);
                ReadOptions options = new ReadOptions();
                options.Encoding = Encoding.Default;
                Ionic.Zip.ZipFile unzip = Ionic.Zip.ZipFile.Read(path, options);
                List<string> SIMAGE = new List<string>();
                List<string> MIMAGE = new List<string>();
                string unZipPath = ALLPath + @"\Temp_" + DateTime.Now.ToString("HH_mm_ss") + @"\";
                byte[] bufferArray = new byte[4096];

                foreach (ZipEntry e in unzip)
                {
                    e.Extract(unZipPath, ExtractExistingFileAction.OverwriteSilently);
                }

                unzip.Dispose();

                ArrayList ArrImg = GetFiles(unZipPath);

                Dictionary<string, string> ErrMSG = new Dictionary<string, string>();
                Regex regexCode = new Regex(@".*\.(jpg|jpeg|png|gif|bmp)");

                bool OK = true;
                List<HRMT01> H01List = db.HRMT01.Where(a => a.SCHOOL_NO == DefaultSCHOOL_NO
                  && a.CLASS_NO == model.Search.WhereCLASS_NO).ToList();
                model.Details_List = new List<ZZZI34DetailsViewModel>();
                int i = 0;
                List<SelectListItem> listItemSInfo = new List<SelectListItem>();
                foreach (var File in ArrImg)
                {
                    string FilePAth1 = "";
                    FilePAth1 = (string)File;
                    string Extension = Path.GetExtension(FilePAth1);
                    string NewPath = FilePAth1.Replace(Extension, "_S" + Extension);
                    string NewPath1 = FilePAth1.Replace(Extension, "_M" + Extension);

                    Service.SaveThumbnailImage(FilePAth1, 200, 150, NewPath);
                    Service.SaveThumbnailImage(FilePAth1, 1600, 1600, NewPath1);
                    string FileName = Path.GetFileName(FilePAth1); //取得檔名: "test.jpg"
                    string SEAT_NO = "";
                    string UserNO = "";
                    //以學號上傳的




                    HRMT01 H02 = db.HRMT01.Where(a => a.SCHOOL_NO == DefaultSCHOOL_NO
              && a.USER_NO == stdUserNo && a.USER_STATUS != UserStaus.Invalid).FirstOrDefault();
                    SEAT_NO = H02.SEAT_NO;
                    model.Search.WhereCLASS_NO = H02.CLASS_NO;

                    if (regexCode.IsMatch(FileName.ToLower()) == false)
                    {
                        ErrMSG.Add(FileName, "非有效圖片格式");
                        OK = false;
                        continue;
                    }
                    ////並確認學生清單
                    HRMT01 H01 = db.HRMT01.Where(a => a.SCHOOL_NO == DefaultSCHOOL_NO
                    && a.CLASS_NO == model.Search.WhereCLASS_NO && a.SEAT_NO == SEAT_NO && a.USER_STATUS != UserStaus.Invalid).FirstOrDefault();

                    if (H01 == null)
                    {
                        ErrMSG.Add(FileName, "無此座號");
                        OK = false;
                        continue;
                    }
                    ZZZI34DetailsViewModel Details = new ZZZI34DetailsViewModel();

                    Details.ART_GALLERY_NO = SaveUp.ART_GALLERY_NO;
                    Details.SCHOOL_NO = H01.SCHOOL_NO;
                    Details.USER_NO = H01.USER_NO;
                    Details.CLASS_NO = H01.CLASS_NO;
                    Details.SEAT_NO = H01.SEAT_NO;
                    Details.NAME = H01.NAME;
                    Details.SNAME = H01.SNAME;
                    Details.ART_DESC = SaveUp.ART_DESC;
                    Details.files = file;
                    Details.SIMG_PATH = NewPath;
                    Details.MIMG_PATH = NewPath1;
                    Details.IMG_FILE = UploadImageRoot + GetRelativePath(RealUploadImageRoot, File.ToString());
                    Details.CRE_DATE = DateTime.Now;
                    Details.COVER_FILE = SaveUp.COVER_FILE;
                    Details.ART_GALLERY_TYPE = SaveUp.ART_GALLERY_TYPE;
                    Details.WORK_TYPE = SaveUp.WORK_TYPE;
                    Details.ART_SUBJECT = SaveUp.ART_SUBJECT;
                    Details.filenames = FileName;
                    model.Details_List.Add(Details);
                    i++;


                    SelectListItem listItem = new SelectListItem();
                    listItem = new SelectListItem { Text = H01.NAME + "/" + H01.CLASS_NO + "/" + H01.SEAT_NO, Value = H01.USER_NO };
                    listItemSInfo.Add(listItem);
                    //Details.ART_GALLERY_NO=
                }
                if (OK)
                {
                    model.ZZZI34ImgType = "G";

                    // var USER_NOItems = HRMT01.GetUserNoClassListData(DefaultSCHOOL_NO, model.Search.WhereCLASS_NO, ref db);
                    if (listItemSInfo.Count() != 0)
                    {
                        ViewBag.USER_NOItems = listItemSInfo;

                    }
                    //else
                    //{
                    //    ViewBag.USER_NOItems = USER_NOItems;
                    //}

                    ViewBag.Panel_Title = "批次申請藝廊 -" + GetModeName(0) + ".新增(步驟2)";
                    TempData["TOLTAL"] = model.Details_List.Count();

                    //model.Search.WhereART_GALLERY_NO = SaveUp.ART_GALLERY_NO;
                    return View("StudentUploadZipEdite", model);
                }
                else
                {
                    if (Directory.Exists(ALLPath))
                    {
                        Directory.Delete(ALLPath, true);
                    }
                    ZZZI34WorkIndexViewModel viewModel = new ZZZI34WorkIndexViewModel();

                    TempData["StatusMessage"] = "上傳失敗!!。<br/>";
                    ViewBag.FailList = ErrMSG;
                    ViewBag.ErrCount = ErrMSG.Count;

                    UserProfile user = UserProfileHelper.Get();
                    ADDT21 aDDT21 = new ADDT21();
                    aDDT21 = db.ADDT21.Where(X => X.ART_GALLERY_NO == SaveUp.ART_GALLERY_NO).FirstOrDefault();
                    db.ADDT21.Remove(aDDT21);
                    db.SaveChanges();
                    if (!string.IsNullOrEmpty(user.CLASS_NO))
                    {
                        List<SelectListItem> ClassList = new List<SelectListItem>();
                        SelectListItem it = new SelectListItem() { Text = user.CLASS_NO, Value = user.CLASS_NO };
                        ClassList.Add(it);
                        ViewBag.ClassNoItem = ClassList;
                    }
                    return View("StudentUploadZip");
                }

            }
            else {


                if (user.USER_TYPE == "S")
                {

                    var items = db.HRMT01.Where(u => u.SCHOOL_NO == user.SCHOOL_NO && u.CLASS_NO == user.CLASS_NO && u.USER_STATUS == UserStaus.Enabled).
                   Select(x => new SelectListItem { Text = x.CLASS_NO, Value = x.CLASS_NO }).Distinct().OrderBy(o => o.Value);
                    IEnumerable<SelectListItem> USER_NOItems = null;
                    USER_NOItems = HRMT01.GetUserNoListData(user.SCHOOL_NO, user.CLASS_NO, user.USER_NO, ref db);
                    ViewBag.USER_NOItems = USER_NOItems;
                    ViewBag.ClassNoItem = items;
                }

                string errstr = "";
                foreach (var str in ModelState) {
                    if (str.Value.Errors != null && str.Value.Errors.Count()!=0) {
                        if (str.Value.Errors[0] != null && str.Value.Errors[0].ErrorMessage != null) {

                            errstr += str.Value.Errors[0].ErrorMessage;

                        }
                       

                    }
                 


                }
                TempData["StatusMessage"] = "上傳失敗!!。<br/>"+ errstr;
                //ViewBag.FailList = errstr;
                //ViewBag.ErrCount = ModelState.Count;
                return View();
            }
            return View();
        }
        [HttpPost]
        [CheckPermission(CheckACTION_ID = "UPLOAD", CheckBRE_NO = "ZZZI341")] //檢查權限
        public ActionResult SetUpload1(ZZZI34EditViewModel model, HttpPostedFileBase file, HttpPostedFileBase file2)
        {
            ViewBag.BRE_NO = Bre_NO;
            UserProfile User = UserProfileHelper.Get();
            this.Shared();
            string DefaultSCHOOL_NO = UserProfileHelper.GetSchoolNo();
            byte? ModeVal = 0;
            string stdUserNo = "";
            string ART_SUBJECT = (Request["ART_SUBJECT"] != null) ? Request["ART_SUBJECT"] : string.Empty;
            string Class_No = (Request["Class_No"] != null) ? Request["Class_No"] : string.Empty;
            stdUserNo = (Request["USER_NO"] != null) ? Request["USER_NO"] : string.Empty;
            string Main_ART_DESC = (Request["Main_ART_DESC"] != null) ? Request["Main_ART_DESC"].ToString() : string.Empty;
            string radFileType = (Request["radFileType"] != null) ? Request["radFileType"].ToString() : string.Empty;
            ViewBag.Panel_Title = " 批次線上藝廊上傳- " + GetModeName(ModeVal) + ".新增(步驟1)";
            model.Search = new ZZZI34SearchViewModel();

            model.Search.WhereCLASS_NO = Class_No;
            ViewBag.ClassItems = HRMT01.GetClassListData(DefaultSCHOOL_NO, "", model.Search.WhereCLASS_NO, ref db);

            if (string.IsNullOrWhiteSpace(model.Search.WhereCLASS_NO))
            {
                ModelState.AddModelError(model.Search.WhereCLASS_NO, "*請輸入班級");
            }
            if (file == null)
            {
                ModelState.AddModelError("file", "*請上傳檔案");
            }
            else
            {
                if (file.ContentLength == 0)
                {
                    ModelState.AddModelError("file", "*請上傳檔案");
                }
                else
                {
                    if (file.ContentType != "application/x-zip-compressed" && file.ContentType != "application/octet-stream" && file.ContentType != "application/zip")
                    {
                        ModelState.AddModelError("file", "*請上傳檔案[ZIP]格式");
                    }
                }
            }



            ZZZI34Service zZZI34Service = new ZZZI34Service();
            int SYear;
            int Semesters;
            string Message = "";
            SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);
            if (model.Details_List == null) { model.Details_List = new List<ZZZI34DetailsViewModel>(); }
            ADDT21 SaveUp = null;
            SaveUp = new ADDT21();
            model.Main = new ZZZI34EditMainViewModel();
            model.Main.ART_GALLERY_TYPE = ADDT21.ART_GALLERY_TYPE_VAL.Campus;
            model.Main.ART_SUBJECT = ART_SUBJECT;
            model.Main.ART_DESC = Main_ART_DESC;

            model.Main.WORK_TYPE = "P";
            if (SaveUp == null || SaveUp.ART_GALLERY_NO == null)
            {
                SaveUp.ART_GALLERY_NO = Guid.NewGuid().ToString("N");
                SaveUp.ART_GALLERY_TYPE = model.Main.ART_GALLERY_TYPE;
                SaveUp.ART_SUBJECT = model.Main.ART_SUBJECT;
                SaveUp.ART_DESC = model.Main.ART_DESC;
                SaveUp.WORK_TYPE = model.Main.WORK_TYPE;

                if (SaveUp.ART_GALLERY_TYPE == ADDT21.ART_GALLERY_TYPE_VAL.Campus)
                {
                    SaveUp.STATUS = ADDT21.STATUSVal.NotStarted;
                }
                else
                {
                    SaveUp.STATUS = ADDT21.STATUSVal.Verification;
                }

                SaveUp.SCHOOL_NO = User.SCHOOL_NO;
                SaveUp.USER_NO = User.USER_NO;
                SaveUp.NAME = User.NAME;
                SaveUp.SNAME = User.SNAME;
                SaveUp.CLASS_NO = User.CLASS_NO;
                SaveUp.SYEAR = Convert.ToByte(SYear);
                SaveUp.SEMESTER = Convert.ToByte(Semesters);
                SaveUp.CRE_PERSON = User.USER_KEY;
                SaveUp.CRE_DATE = DateTime.Now;
                SaveUp.CHG_PERSON = User.USER_KEY;
                SaveUp.CHG_DATE = DateTime.Now;

                SaveUp.COVER_FILE = zZZI34Service.UpLoadFile(SaveUp.SCHOOL_NO, SaveUp.ART_GALLERY_NO, file2, ref Message);

                if (string.IsNullOrWhiteSpace(Message) == false)
                {
                    ModelState.AddModelError("相簿首頁圖", Message);
                }

                //找老師
                HRMT03 TeachInfo =
                    db.HRMT03.Where(a => a.SCHOOL_NO == SaveUp.SCHOOL_NO && a.CLASS_NO == SaveUp.CLASS_NO).FirstOrDefault();
                if (TeachInfo != null)
                {
                    SaveUp.VERIFIER = TeachInfo.TEACHER_NO;
                }

                db.ADDT21.Add(SaveUp);
            }
            try
            {
                db.SaveChanges();
            }
            catch (Exception ex)
            {
                Message = "系統發生錯誤;原因:" + ex.Message;
                ModelState.AddModelError("相簿首頁儲存錯誤", Message);
            }
            if (ModelState.IsValid) //沒有錯誤
            {
                if (db.Database.Connection.State != ConnectionState.Open) db.Database.Connection.Open();
                //ZIP 檔上傳
                string fileName = Path.GetFileName(file.FileName);

                //建立上傳暫存路徑

                string NowFile = DateTime.Now.ToString("yyyyMMdd");

                model.Search.TEMP_BATCH_KEY = Session.SessionID;

                string UploadImageRoot = System.Web.Configuration.WebConfigurationManager.AppSettings["UploadImageRoot"];
                if (UploadImageRoot == null) UploadImageRoot = @"~\Content\";
                string RealUploadImageRoot = Request.MapPath(UploadImageRoot);

                string SCHOOL_NOPath = RealUploadImageRoot + @"\" + Bre_NO + @"\" + DefaultSCHOOL_NO + @"\";
                string ALLPath = SCHOOL_NOPath + @"\" + NowFile + @"\";
                if (!Directory.Exists(ALLPath))
                {
                    if (Directory.Exists(SCHOOL_NOPath))
                    {
                        Directory.Delete(SCHOOL_NOPath, true);
                    }

                    Directory.CreateDirectory(ALLPath);
                }

                string path = ALLPath + fileName;

                file.SaveAs(path);
                ReadOptions options = new ReadOptions();
                options.Encoding = Encoding.Default;
                Ionic.Zip.ZipFile unzip = Ionic.Zip.ZipFile.Read(path, options);
                List<string> SIMAGE = new List<string>();
                List<string> MIMAGE = new List<string>();
                string unZipPath = ALLPath + @"\Temp_" + DateTime.Now.ToString("HH_mm_ss") + @"\";
                byte[] bufferArray = new byte[4096];

                foreach (ZipEntry e in unzip)
                {
                    e.Extract(unZipPath, ExtractExistingFileAction.OverwriteSilently);
                }

                unzip.Dispose();

                ArrayList ArrImg = GetFiles(unZipPath);

                Dictionary<string, string> ErrMSG = new Dictionary<string, string>();
                Regex regexCode = new Regex(@".*\.(jpg|jpeg|png|gif|bmp)");

                bool OK = true;

                int i = 0;
                List<SelectListItem> listItemSInfo = new List<SelectListItem>();
                foreach (var File in ArrImg)
                {
                    string FilePAth1 = "";
                    FilePAth1 = (string)File;
                    string Extension = Path.GetExtension(FilePAth1);
                    string NewPath = FilePAth1.Replace(Extension, "_S" + Extension);
                    string NewPath1 = FilePAth1.Replace(Extension, "_M" + Extension);

                    Service.SaveThumbnailImage(FilePAth1, 200, 150, NewPath);
                    Service.SaveThumbnailImage(FilePAth1, 1600, 1600, NewPath1);
                    string FileName = Path.GetFileName(FilePAth1); //取得檔名: "test.jpg"
                    string SEAT_NO = "";
                    string UserNO = "";
                    //以學號上傳的
             



                         HRMT01 H02 = db.HRMT01.Where(a => a.SCHOOL_NO == DefaultSCHOOL_NO
                   && a.USER_NO == stdUserNo && a.USER_STATUS != UserStaus.Invalid).FirstOrDefault();
                    SEAT_NO = H02.SEAT_NO;
                    model.Search.WhereCLASS_NO = H02.CLASS_NO;

                    if (regexCode.IsMatch(FileName.ToLower()) == false)
                    {
                        ErrMSG.Add(FileName, "非有效圖片格式");
                        OK = false;
                        continue;
                    }
                    ////並確認學生清單
                    HRMT01 H01 = db.HRMT01.Where(a => a.SCHOOL_NO == DefaultSCHOOL_NO
                    && a.CLASS_NO == model.Search.WhereCLASS_NO && a.SEAT_NO == SEAT_NO && a.USER_STATUS != UserStaus.Invalid).FirstOrDefault();

                    if (H01 == null)
                    {
                        ErrMSG.Add(FileName, "無此座號");
                        OK = false;
                        continue;
                    }
                    ZZZI34DetailsViewModel Details = new ZZZI34DetailsViewModel();

                    Details.ART_GALLERY_NO = SaveUp.ART_GALLERY_NO;
                    Details.SCHOOL_NO = H01.SCHOOL_NO;
                    Details.USER_NO = H01.USER_NO;
                    Details.CLASS_NO = H01.CLASS_NO;
                    Details.SEAT_NO = H01.SEAT_NO;
                    Details.NAME = H01.NAME;
                    Details.SNAME = H01.SNAME;
                    Details.ART_DESC = SaveUp.ART_DESC;
                    Details.files = file;
                    Details.SIMG_PATH = NewPath;
                    Details.MIMG_PATH = NewPath1;
                    Details.IMG_FILE = UploadImageRoot + GetRelativePath(RealUploadImageRoot, File.ToString());
                    Details.CRE_DATE = DateTime.Now;
                    Details.COVER_FILE = SaveUp.COVER_FILE;
                    Details.ART_GALLERY_TYPE = SaveUp.ART_GALLERY_TYPE;
                    Details.WORK_TYPE = SaveUp.WORK_TYPE;
                    Details.ART_SUBJECT = SaveUp.ART_SUBJECT;
                    Details.filenames = FileName;
                    model.Details_List.Add(Details);
                    i++;


                    SelectListItem listItem = new SelectListItem();
                    listItem = new SelectListItem { Text = H01.NAME + "/" + H01.CLASS_NO + "/" + H01.SEAT_NO, Value = H01.USER_NO };
                    listItemSInfo.Add(listItem);
                    //Details.ART_GALLERY_NO=
                }

                if (OK)
                {
                    model.ZZZI34ImgType = "G";

                    var USER_NOItems = HRMT01.GetUserNoClassListData(DefaultSCHOOL_NO, model.Search.WhereCLASS_NO, ref db);
                    if (listItemSInfo.Count() != 0)
                    {
                        ViewBag.USER_NOItems = listItemSInfo;

                    }
                    else
                    {
                        ViewBag.USER_NOItems = USER_NOItems;
                    }

                    ViewBag.Panel_Title = "批次申請藝廊 -" + GetModeName(0) + ".新增(步驟2)";
                    TempData["TOLTAL"] = model.Details_List.Count();

                    //model.Search.WhereART_GALLERY_NO = SaveUp.ART_GALLERY_NO;
                    return View("UploadEdite", model);
                }
                else
                {
                    if (Directory.Exists(ALLPath))
                    {
                        Directory.Delete(ALLPath, true);
                    }
                    ZZZI34WorkIndexViewModel viewModel = new ZZZI34WorkIndexViewModel();

                    TempData["StatusMessage"] = "上傳失敗!!。<br/>";
                    ViewBag.FailList = ErrMSG;
                    ViewBag.ErrCount = ErrMSG.Count;

                    UserProfile user = UserProfileHelper.Get();
                    ADDT21 aDDT21 = new ADDT21();
                    aDDT21 = db.ADDT21.Where(X => X.ART_GALLERY_NO == SaveUp.ART_GALLERY_NO).FirstOrDefault();
                    db.ADDT21.Remove(aDDT21);
                    db.SaveChanges();
                    if (string.IsNullOrEmpty(user.TEACH_CLASS_NO))
                    {
                        var items = db.HRMT01.Where(u => u.SCHOOL_NO == user.SCHOOL_NO && string.IsNullOrEmpty(u.CLASS_NO) == false && u.USER_STATUS == UserStaus.Enabled).
                        Select(x => new SelectListItem { Text = x.CLASS_NO, Value = x.CLASS_NO }).Distinct().OrderBy(o => o.Value);
                        //SelectListItem item = new SelectListItem();
                        //item.Text = "請選擇班級";
                        //items.
                        ViewBag.ClassNoItem = items;
                    }
                    else
                    {
                        List<SelectListItem> ClassList = new List<SelectListItem>();
                        SelectListItem it = new SelectListItem() { Text = user.TEACH_CLASS_NO, Value = user.TEACH_CLASS_NO };
                        ClassList.Add(it);
                        ViewBag.ClassNoItem = ClassList;
                    }
                    return View("UPLOAD1");
                }
            }
            return View();
        }
        [CheckPermission(CheckACTION_ID = "Edit", CheckBRE_NO = "ZZZI34")] //檢查權限
        public ActionResult MenuIndex2()
        {


            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "線上藝廊 - 選擇模式";
            this.Shared();
            return View();
        }
        [CheckPermission(CheckACTION_ID = "MenuIndex", CheckBRE_NO = "ZZZI34")] //檢查權限
        public ActionResult MenuIndex() {

             
                ViewBag.BRE_NO = Bre_NO;
                ViewBag.Panel_Title =  "線上藝廊 - 選擇模式";
                this.Shared();
                return View();
            }
        [HttpPost]
        [CheckPermission(CheckACTION_ID = "UPLOAD", CheckBRE_NO = "ZZZI341")] //檢查權限
        public ActionResult SetUpload2(ZZZI34EditViewModel model, HttpPostedFileBase file, HttpPostedFileBase file2) {


            ViewBag.BRE_NO = Bre_NO;
            UserProfile User = UserProfileHelper.Get();
            this.Shared();
            string DefaultSCHOOL_NO = UserProfileHelper.GetSchoolNo();
            byte? ModeVal = 0;

            string ART_SUBJECT = (Request["ART_SUBJECT"] != null) ? Request["ART_SUBJECT"] : string.Empty;
            string Class_No = (Request["Class_No"] != null) ? Request["Class_No"] : string.Empty;
            string Main_ART_DESC = (Request["Main_ART_DESC"] != null) ? Request["Main_ART_DESC"].ToString() : string.Empty;
            string radFileType = (Request["radFileType"] != null) ? Request["radFileType"].ToString() : string.Empty;
            ViewBag.Panel_Title = " 批次線上藝廊上傳- " + GetModeName(ModeVal) + ".新增(步驟1)";
            model.Search = new ZZZI34SearchViewModel();
            ViewBag.ClassItems = HRMT01.GetClassListData(DefaultSCHOOL_NO, "", "", ref db);
            if (file == null)
            {
                ModelState.AddModelError("file", "*請上傳檔案");
            }
            else
            {
                if (file.ContentLength == 0)
                {
                    ModelState.AddModelError("file", "*請上傳檔案");
                }
                else
                {
                    if (file.ContentType != "application/x-zip-compressed" && file.ContentType != "application/octet-stream" && file.ContentType != "application/zip")
                    {
                        ModelState.AddModelError("file", "*請上傳檔案[ZIP]格式");
                    }
                }
            }

            ZZZI34Service zZZI34Service = new ZZZI34Service();
            int SYear;
            int Semesters;
            string Message = "";
            SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);

            ADDT21 SaveUp = null;
            SaveUp = new ADDT21();
            model.Main = new ZZZI34EditMainViewModel();
            model.Main.ART_GALLERY_TYPE = ADDT21.ART_GALLERY_TYPE_VAL.Campus;
            model.Main.ART_SUBJECT = ART_SUBJECT;
            model.Main.ART_DESC = Main_ART_DESC;

            model.Main.WORK_TYPE = "P";
            if (SaveUp == null || SaveUp.ART_GALLERY_NO == null)
            {


                SaveUp.ART_GALLERY_NO = Guid.NewGuid().ToString("N");
                SaveUp.ART_GALLERY_TYPE = model.Main.ART_GALLERY_TYPE;
                SaveUp.ART_SUBJECT = model.Main.ART_SUBJECT;
                SaveUp.ART_DESC = model.Main.ART_DESC;
                SaveUp.WORK_TYPE = model.Main.WORK_TYPE;

                if (SaveUp.ART_GALLERY_TYPE == ADDT21.ART_GALLERY_TYPE_VAL.Campus)
                {
                    SaveUp.STATUS = ADDT21.STATUSVal.NotStarted;
                }
                SaveUp.SCHOOL_NO = User.SCHOOL_NO;
                SaveUp.USER_NO = User.USER_NO;
                SaveUp.NAME = User.NAME;
                SaveUp.SNAME = User.SNAME;
                SaveUp.CLASS_NO = "";
                SaveUp.SYEAR = Convert.ToByte(SYear);
                SaveUp.SEMESTER = Convert.ToByte(Semesters);
                SaveUp.CRE_PERSON = User.USER_KEY;
                SaveUp.CRE_DATE = DateTime.Now;
                SaveUp.CHG_PERSON = User.USER_KEY;
                SaveUp.CHG_DATE = DateTime.Now;
                SaveUp.COVER_FILE = zZZI34Service.UpLoadFile(SaveUp.SCHOOL_NO, SaveUp.ART_GALLERY_NO, file2, ref Message);

                if (string.IsNullOrWhiteSpace(Message) == false)
                {
                    ModelState.AddModelError("相簿首頁圖", Message);
                }
             
                db.ADDT21.Add(SaveUp);
            }
            try
            {
                db.SaveChanges();
            }
            catch (Exception ex)
            {
                Message = "系統發生錯誤;原因:" + ex.Message;
                ModelState.AddModelError("相簿首頁儲存錯誤", Message);
            }

            if (ModelState.IsValid) //沒有錯誤
            {

                if (db.Database.Connection.State != ConnectionState.Open) db.Database.Connection.Open();
                //ZIP 檔上傳
                string fileName = Path.GetFileName(file.FileName);
                if (file.ContentLength / 1024 > (1024 * 100)) // 5MB
                {
                    TempData["StatusMessage"] = "上傳檔案不能超過100MB";
                }
                //建立上傳暫存路徑


                string NowFile = DateTime.Now.ToString("yyyyMMdd");
                model.Search.TEMP_BATCH_KEY = Session.SessionID;

                string UploadImageRoot = System.Web.Configuration.WebConfigurationManager.AppSettings["UploadImageRoot"];
                if (UploadImageRoot == null) UploadImageRoot = @"~\Content\";
                string RealUploadImageRoot = Request.MapPath(UploadImageRoot);

                string SCHOOL_NOPath = RealUploadImageRoot + @"\" + Bre_NO + @"\" + DefaultSCHOOL_NO + @"\";
                string ALLPath = SCHOOL_NOPath + @"\" + NowFile + @"\";
                if (!Directory.Exists(ALLPath))
                {
                    if (Directory.Exists(SCHOOL_NOPath))
                    {
                        Directory.Delete(SCHOOL_NOPath, true);
                    }

                    Directory.CreateDirectory(ALLPath);
                }

                string path = ALLPath + fileName;

                file.SaveAs(path);
                ReadOptions options = new ReadOptions();
                options.Encoding = Encoding.Default;
                Ionic.Zip.ZipFile unzip = Ionic.Zip.ZipFile.Read(path, options);
                List<string> SIMAGE = new List<string>();
                List<string> MIMAGE = new List<string>();
                string unZipPath = ALLPath + @"\Temp_" + DateTime.Now.ToString("HH_mm_ss") + @"\";
                byte[] bufferArray = new byte[4096];


                bool OK = true;
                Dictionary<string, string> ErrMSG = new Dictionary<string, string>();
                using (ZipArchive archive = System.IO.Compression.ZipFile.OpenRead(path))
                {
                    foreach (ZipArchiveEntry entry in archive.Entries)
                    {
                        if (entry.Length / 1024 > 6 * 1024)
                        {
                            ErrMSG.Add(entry.Name, "上傳一個檔案不能超過6MB");
                            OK = false;
                            continue;

                        }
                        else
                        {
                            float compressedRatio = (float)entry.CompressedLength / entry.Length;
                            float reductionPercentage = 100 - (compressedRatio * 100);



                        }



                    }
                }
                foreach (ZipEntry e in unzip)
                {

                    e.Extract(unZipPath, ExtractExistingFileAction.OverwriteSilently);
                }

                unzip.Dispose();
                ArrayList ArrImg = new ArrayList();
                ArrImg = GetFiles(unZipPath);
                ArrayList myAL = new ArrayList();
               
              
                          Regex regexCode = new Regex(@".*\.(jpg|jpeg|png|gif|bmp)");
                model.Details_List = new List<ZZZI34DetailsViewModel>();
                int i = 0;
                List<SelectListItem> listItemSInfo = new List<SelectListItem>();
                foreach (var FileItem in ArrImg)
                {
                    string FilePAth1 = "";
                    FilePAth1 =FileItem.ToString();
                    string Extension = Path.GetExtension(FilePAth1);
                    string NewPath = FilePAth1.Replace(Extension, "_S" + Extension);
                    string NewPath1 = FilePAth1.Replace(Extension, "_M" + Extension);
                    Service.SaveThumbnailImage(FilePAth1, 200, 150, NewPath);
                    Service.SaveThumbnailImage(FilePAth1, 1600, 1600, NewPath1);
                    string FileName = Path.GetFileName(FilePAth1); //取得檔名: "test.jpg"
                    ZZZI34DetailsViewModel Details = new ZZZI34DetailsViewModel();

                    Details.ART_GALLERY_NO = SaveUp.ART_GALLERY_NO;
                    Details.SCHOOL_NO = DefaultSCHOOL_NO;
                    Details.USER_NO = "";
                    Details.CLASS_NO = "";
                    Details.SEAT_NO = "";
                    Details.NAME = "";
                    Details.SNAME = "";
                    Details.ART_DESC = SaveUp.ART_DESC;
                    Details.files = file;
                    Details.SIMG_PATH = NewPath;
                    Details.MIMG_PATH = NewPath1;
                    Details.IMG_FILE = UploadImageRoot + GetRelativePath(RealUploadImageRoot, FileItem.ToString());
                    Details.CRE_DATE = DateTime.Now;
                    Details.COVER_FILE = SaveUp.COVER_FILE;
                    Details.ART_GALLERY_TYPE = SaveUp.ART_GALLERY_TYPE;
                    Details.WORK_TYPE = SaveUp.WORK_TYPE;
                    Details.ART_SUBJECT = SaveUp.ART_SUBJECT;
                    Details.filenames = FileName;
                    model.Details_List.Add(Details);
                    i++;


                }
                if (OK)
                {
                    model.ZZZI34ImgType = "G";
                    ViewBag.ClassItems = HRMT01.GetClassListData(DefaultSCHOOL_NO, null, ref db);
                    ViewBag.UserNoItems = HRMT01.GetUserNoListDataMust(DefaultSCHOOL_NO, null, "", ref db);

                    ViewBag.Panel_Title = "批次申請藝廊 -" + GetModeName(0) + ".新增(步驟2)";
                    TempData["TOLTAL"] = model.Details_List.Count();

                    //model.Search.WhereART_GALLERY_NO = SaveUp.ART_GALLERY_NO;
                    return View("UploadEdite1", model);
                }
                else
                {
                    if (Directory.Exists(ALLPath))
                    {
                        Directory.Delete(ALLPath, true);
                    }
                    ZZZI34WorkIndexViewModel viewModel = new ZZZI34WorkIndexViewModel();
                    string errSTRING = "";
                    foreach (var Itrme in ErrMSG)
                    {
                        errSTRING += Itrme;

                    }
                    TempData["StatusMessage"] = "上傳失敗!!。<br/>" + errSTRING;
                    ViewBag.FailList = ErrMSG;
                    ViewBag.ErrCount = ErrMSG.Count;

                    UserProfile user = UserProfileHelper.Get();
                    ADDT21 aDDT21 = new ADDT21();
                    aDDT21 = db.ADDT21.Where(X => X.ART_GALLERY_NO == SaveUp.ART_GALLERY_NO).FirstOrDefault();
                    db.ADDT21.Remove(aDDT21);
                    db.SaveChanges();

                    return View("UPLOAD2");
                }
            }
            return View();

        }
        public ArrayList PrintValues(IEnumerable myList)
        {
            ArrayList myAL = new ArrayList();
            foreach (Object obj in myList) {
                myAL.Add(obj);

            }
            return myAL;
          }
        [HttpPost]
        [CheckPermission(CheckACTION_ID = "UPLOAD", CheckBRE_NO = "ZZZI341")] //檢查權限
        public ActionResult SetUpload(ZZZI34EditViewModel model, HttpPostedFileBase file, HttpPostedFileBase file2)
        {
            ViewBag.BRE_NO = Bre_NO;
            UserProfile User = UserProfileHelper.Get();
            this.Shared();
            string DefaultSCHOOL_NO = UserProfileHelper.GetSchoolNo();
            byte? ModeVal = 0;

            string ART_SUBJECT = (Request["ART_SUBJECT"] != null) ? Request["ART_SUBJECT"] : string.Empty;
            string Class_No = (Request["Class_No"] != null) ? Request["Class_No"] : string.Empty;
            string Main_ART_DESC = (Request["Main_ART_DESC"] != null) ? Request["Main_ART_DESC"].ToString() : string.Empty;
            string radFileType = (Request["radFileType"] != null) ? Request["radFileType"].ToString() : string.Empty;
            ViewBag.Panel_Title = " 批次線上藝廊上傳- " + GetModeName(ModeVal) + ".新增(步驟1)";
            model.Search = new ZZZI34SearchViewModel();
          
            model.Search.WhereCLASS_NO = Class_No;
            ViewBag.ClassItems = HRMT01.GetClassListData(DefaultSCHOOL_NO, "", model.Search.WhereCLASS_NO, ref db);

            if (string.IsNullOrWhiteSpace(model.Search.WhereCLASS_NO))
            {
                ModelState.AddModelError(model.Search.WhereCLASS_NO, "*請輸入班級");
            }
            if (file == null)
            {
                ModelState.AddModelError("file", "*請上傳檔案");
            }
            else
            {
                if (file.ContentLength == 0)
                {
                    ModelState.AddModelError("file", "*請上傳檔案");
                }
                else
                {
                    if (file.ContentType != "application/x-zip-compressed" && file.ContentType != "application/octet-stream" && file.ContentType != "application/zip")
                    {
                        ModelState.AddModelError("file", "*請上傳檔案[ZIP]格式");
                    }
                }
            }
            string stdUserNo = "";

            
                ZZZI34Service zZZI34Service = new ZZZI34Service();
            int SYear;
            int Semesters;
            string Message = "";
            SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);

            ADDT21 SaveUp = null;
            SaveUp = new ADDT21();
            model.Main = new ZZZI34EditMainViewModel();
            model.Main.ART_GALLERY_TYPE = ADDT21.ART_GALLERY_TYPE_VAL.Campus;
            model.Main.ART_SUBJECT = ART_SUBJECT;
            model.Main.ART_DESC = Main_ART_DESC;

            model.Main.WORK_TYPE = "P";
            if (SaveUp == null || SaveUp.ART_GALLERY_NO == null)
            {
                SaveUp.ART_GALLERY_NO = Guid.NewGuid().ToString("N");
                SaveUp.ART_GALLERY_TYPE = model.Main.ART_GALLERY_TYPE;
                SaveUp.ART_SUBJECT = model.Main.ART_SUBJECT;
                SaveUp.ART_DESC = model.Main.ART_DESC;
                SaveUp.WORK_TYPE = model.Main.WORK_TYPE;

                if (SaveUp.ART_GALLERY_TYPE == ADDT21.ART_GALLERY_TYPE_VAL.Campus)
                {
                    SaveUp.STATUS = ADDT21.STATUSVal.NotStarted;
                }
                else
                {
                    SaveUp.STATUS = ADDT21.STATUSVal.Verification;
                }

                SaveUp.SCHOOL_NO = User.SCHOOL_NO;
                SaveUp.USER_NO = User.USER_NO;
                SaveUp.NAME = User.NAME;
                SaveUp.SNAME = User.SNAME;
                SaveUp.CLASS_NO = User.CLASS_NO;
                SaveUp.SYEAR = Convert.ToByte(SYear);
                SaveUp.SEMESTER = Convert.ToByte(Semesters);
                SaveUp.CRE_PERSON = User.USER_KEY;
                SaveUp.CRE_DATE = DateTime.Now;
                SaveUp.CHG_PERSON = User.USER_KEY;
                SaveUp.CHG_DATE = DateTime.Now;

                SaveUp.COVER_FILE = zZZI34Service.UpLoadFile(SaveUp.SCHOOL_NO, SaveUp.ART_GALLERY_NO, file2, ref Message);

                if (string.IsNullOrWhiteSpace(Message) == false)
                {
                    ModelState.AddModelError("相簿首頁圖", Message);
                }

                //找老師
                HRMT03 TeachInfo =
                    db.HRMT03.Where(a => a.SCHOOL_NO == SaveUp.SCHOOL_NO && a.CLASS_NO == SaveUp.CLASS_NO).FirstOrDefault();
                if (TeachInfo != null)
                {
                    SaveUp.VERIFIER = TeachInfo.TEACHER_NO;
                }

                db.ADDT21.Add(SaveUp);
            }
            try
            {
                db.SaveChanges();
            }
            catch (Exception ex)
            {
                Message = "系統發生錯誤;原因:" + ex.Message;
                ModelState.AddModelError("相簿首頁儲存錯誤", Message);
            }
            if (ModelState.IsValid) //沒有錯誤
            {
                if (db.Database.Connection.State != ConnectionState.Open) db.Database.Connection.Open();
                //ZIP 檔上傳
                string fileName = Path.GetFileName(file.FileName);
                if (file.ContentLength / 1024 > (1024 * 100)) // 5MB
                {
                    TempData["StatusMessage"] = "上傳檔案不能超過100MB";
                }
                    //建立上傳暫存路徑

                    string NowFile = DateTime.Now.ToString("yyyyMMdd");

                model.Search.TEMP_BATCH_KEY = Session.SessionID;

                string UploadImageRoot = System.Web.Configuration.WebConfigurationManager.AppSettings["UploadImageRoot"];
                if (UploadImageRoot == null) UploadImageRoot = @"~\Content\";
                string RealUploadImageRoot = Request.MapPath(UploadImageRoot);

                string SCHOOL_NOPath = RealUploadImageRoot + @"\" + Bre_NO + @"\" + DefaultSCHOOL_NO + @"\";
                string ALLPath = SCHOOL_NOPath + @"\" + NowFile + @"\";
                if (!Directory.Exists(ALLPath))
                {
                    if (Directory.Exists(SCHOOL_NOPath))
                    {
                        Directory.Delete(SCHOOL_NOPath, true);
                    }

                    Directory.CreateDirectory(ALLPath);
                }

                string path = ALLPath + fileName;

                file.SaveAs(path);
                ReadOptions options = new ReadOptions();
                options.Encoding = Encoding.Default;
                Ionic.Zip.ZipFile unzip = Ionic.Zip.ZipFile.Read(path, options);
                List<string> SIMAGE = new List<string>();
                List<string> MIMAGE = new List<string>();
                string unZipPath = ALLPath + @"\Temp_" + DateTime.Now.ToString("HH_mm_ss") + @"\";
                byte[] bufferArray = new byte[4096];

               
                bool OK = true;
                Dictionary<string, string> ErrMSG = new Dictionary<string, string>();
                using (ZipArchive archive = System.IO.Compression.ZipFile.OpenRead(path))
                {
                    foreach (ZipArchiveEntry entry in archive.Entries)
                    {
                        if (entry.Length / 1024 > 6 * 1024) {
                            ErrMSG.Add(entry.Name, "上傳一個檔案不能超過6MB");
                            OK = false;
                            continue;
                      
                        }
                        else
                        {
                            float compressedRatio = (float)entry.CompressedLength / entry.Length;
                            float reductionPercentage = 100 - (compressedRatio * 100);



                        }
                        

                       
                    }
                }
                foreach (ZipEntry e in unzip)
                {
                
                    e.Extract(unZipPath, ExtractExistingFileAction.OverwriteSilently);
                }

                unzip.Dispose();
                ArrayList ArrImg = GetFiles(unZipPath);

            
                Regex regexCode = new Regex(@".*\.(jpg|jpeg|png|gif|bmp)");

             

                List<HRMT01> H01List = db.HRMT01.Where(a => a.SCHOOL_NO == DefaultSCHOOL_NO
                   && a.CLASS_NO == model.Search.WhereCLASS_NO).ToList();
                model.Details_List = new List<ZZZI34DetailsViewModel>();
                int i = 0;
                List<SelectListItem> listItemSInfo = new List<SelectListItem>();
                foreach (var FileItem in ArrImg)
                {
                    string FilePAth1 = "";
                    FilePAth1 = (string)FileItem;
                    string Extension = Path.GetExtension(FilePAth1);
                    string NewPath = FilePAth1.Replace(Extension, "_S" + Extension);
                    string NewPath1 = FilePAth1.Replace(Extension, "_M" + Extension);

                    Service.SaveThumbnailImage(FilePAth1, 200, 150, NewPath);
                    Service.SaveThumbnailImage(FilePAth1, 1600, 1600, NewPath1);
                    string FileName = Path.GetFileName(FilePAth1); //取得檔名: "test.jpg"
                    string SEAT_NO = "";
                    string UserNO = "";
                    //以學號上傳的

                    if (model.radFileType == "U")
                    {
                        
                        stdUserNo = Path.GetFileNameWithoutExtension(FileItem.ToString());
                        int found = 0;

                        bool telcheck = Regex.IsMatch(stdUserNo, @"-");
                      
                        if (telcheck)
                        {

                            found = stdUserNo.IndexOf("-");

                            stdUserNo = stdUserNo.Substring(0, found);

                        }
                        HRMT01 H02 = db.HRMT01.Where(a => a.SCHOOL_NO == DefaultSCHOOL_NO
                  && a.USER_NO == stdUserNo && a.USER_STATUS != UserStaus.Invalid).FirstOrDefault();
                        if (H02 != null)
                        {


                            SEAT_NO = H02.SEAT_NO;
                            model.Search.WhereCLASS_NO = H02.CLASS_NO;
                        }
                        else {
                            ErrMSG.Add(FileName, "無此學號");
                          
                            OK = false;
                            continue;

                        }
                    }
                    else { 
             
                       SEAT_NO = Path.GetFileNameWithoutExtension(FileItem.ToString());
                        int found = 0;

                        bool telcheck = Regex.IsMatch(SEAT_NO, @"-");
                        if (telcheck) {

                            found = SEAT_NO.IndexOf("-");

                            SEAT_NO = SEAT_NO.Substring(0, found);
                         
                        }
                    }
                    if (regexCode.IsMatch(FileName.ToLower()) == false)
                    {
                        ErrMSG.Add(FileName, "非有效圖片格式");
                        OK = false;
                        continue;
                    }

                    //if (((HttpPostedFileBase)FileItem).ContentLength / 1024 > (1024 * 6)) // 5MB
                    //{
                    //    ErrMSG.Add(FileName, "上傳檔案不能超過6MB");
                    //    OK = false;
                    //    continue;
                    //}
                    ////並確認學生清單
                    HRMT01 H01 = db.HRMT01.Where(a => a.SCHOOL_NO == DefaultSCHOOL_NO
                    && a.CLASS_NO == model.Search.WhereCLASS_NO && a.SEAT_NO == SEAT_NO && a.USER_STATUS != UserStaus.Invalid).FirstOrDefault();

                    if (H01 == null)
                    {
                        ErrMSG.Add(FileName, "無此座號");
                        OK = false;
                        continue;
                    }
                    ZZZI34DetailsViewModel Details = new ZZZI34DetailsViewModel();

                    Details.ART_GALLERY_NO = SaveUp.ART_GALLERY_NO;
                    Details.SCHOOL_NO = H01.SCHOOL_NO;
                    Details.USER_NO = H01.USER_NO;
                    Details.CLASS_NO = H01.CLASS_NO;
                    Details.SEAT_NO = H01.SEAT_NO;
                    Details.NAME = H01.NAME;
                    Details.SNAME = H01.SNAME;
                    Details.ART_DESC = SaveUp.ART_DESC;
                    Details.files = file;
                    Details.SIMG_PATH = NewPath;
                    Details.MIMG_PATH = NewPath1;
                    Details.IMG_FILE = UploadImageRoot + GetRelativePath(RealUploadImageRoot, FileItem.ToString());
                    Details.CRE_DATE = DateTime.Now;
                    Details.COVER_FILE = SaveUp.COVER_FILE;
                    Details.ART_GALLERY_TYPE = SaveUp.ART_GALLERY_TYPE;
                    Details.WORK_TYPE = SaveUp.WORK_TYPE;
                    Details.ART_SUBJECT = SaveUp.ART_SUBJECT;
                    Details.filenames = FileName;
                    model.Details_List.Add(Details);
                    i++;


                    SelectListItem listItem = new SelectListItem();
                    listItem = new SelectListItem { Text = H01.NAME + "/" + H01.CLASS_NO + "/" + H01.SEAT_NO, Value = H01.USER_NO };
                    listItemSInfo.Add(listItem);
                    //Details.ART_GALLERY_NO=
                }

                if (OK)
                {
                    model.ZZZI34ImgType = "G";
                 
                    var USER_NOItems = HRMT01.GetUserNoClassListData(DefaultSCHOOL_NO, model.Search.WhereCLASS_NO, ref db);
                    if (listItemSInfo.Count() != 0)
                    {
                        ViewBag.USER_NOItems = listItemSInfo;

                    }
                    else {
                        ViewBag.USER_NOItems = USER_NOItems;
                    }
                    
                    ViewBag.Panel_Title = "批次申請藝廊 -" + GetModeName(0) + ".新增(步驟2)";
                    TempData["TOLTAL"] = model.Details_List.Count();

                    //model.Search.WhereART_GALLERY_NO = SaveUp.ART_GALLERY_NO;
                    return View("UploadEdite", model);
                }
                else
                {
                    if (Directory.Exists(ALLPath))
                    {
                        Directory.Delete(ALLPath, true);
                    }
                    ZZZI34WorkIndexViewModel viewModel = new ZZZI34WorkIndexViewModel();
                    string errSTRING = "";
                    foreach (var Itrme in ErrMSG) {
                        errSTRING+= Itrme;

                    }
                    TempData["StatusMessage"] ="上傳失敗!!。<br/>"+errSTRING;
                    ViewBag.FailList = ErrMSG;
                    ViewBag.ErrCount = ErrMSG.Count;

                    UserProfile user = UserProfileHelper.Get();
                    ADDT21 aDDT21 = new ADDT21();
                    aDDT21 = db.ADDT21.Where(X => X.ART_GALLERY_NO == SaveUp.ART_GALLERY_NO).FirstOrDefault();
                    db.ADDT21.Remove(aDDT21);
                    db.SaveChanges();
                    if (string.IsNullOrEmpty(user.TEACH_CLASS_NO))
                    {
                        var items = db.HRMT01.Where(u => u.SCHOOL_NO == user.SCHOOL_NO && string.IsNullOrEmpty(u.CLASS_NO) == false && u.USER_STATUS == UserStaus.Enabled).
                        Select(x => new SelectListItem { Text = x.CLASS_NO, Value = x.CLASS_NO }).Distinct().OrderBy(o => o.Value);
                        //SelectListItem item = new SelectListItem();
                        //item.Text = "請選擇班級";
                        //items.
                        ViewBag.ClassNoItem = items;
                    }
                    else
                    {
                        List<SelectListItem> ClassList = new List<SelectListItem>();
                        SelectListItem it = new SelectListItem() { Text = user.TEACH_CLASS_NO, Value = user.TEACH_CLASS_NO };
                        ClassList.Add(it);
                        ViewBag.ClassNoItem = ClassList;
                    }
                    return View("UPLOAD");
                }
            }
            return View();
        }
        public ActionResult UploadEdite(ZZZI34EditViewModel Data, IEnumerable<HttpPostedFileBase> files, string DATA_TYPE, string ADDNUM)
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "代申請線上藝廊 -" + GetModeName(1) + ".新增(步驟2)";
            this.Shared();
            if (Data.Details_List == null) Data.Details_List = new List<ZZZI34DetailsViewModel>();

            if (string.IsNullOrEmpty(ADDNUM) == false) Data.ADDNUM = Convert.ToInt32(ADDNUM);

            if (string.IsNullOrEmpty(DATA_TYPE) == false)
            {
                if (DATA_TYPE == ZZZI26Controller.DATA_TYPE.DATA_TYPE_A) //新增明細不驗正
                {
                    ModelState.Clear(); //清除驗正

                    if (Data.ADDNUM == null)
                    {
                        ModelState.AddModelError("additem", "【增加明細】請輸入筆數");
                    }
                }
                else if (DATA_TYPE == ZZZI26Controller.DATA_TYPE.DATA_TYPE_S) // 存檔 / 批閱
                {
                    if (string.IsNullOrWhiteSpace(Data.Search.TEMP_BATCH_KEY))
                    {
                        if (VerifyUseYN == "Y")
                        {
                            int num = 0;
                            foreach (var file in files)
                            {
                                if (file != null && file.ContentLength > 0)
                                {
                                    Data.Details_List[num].IMG_FILE = Path.GetFileName(file.FileName);
                                    Data.Details_List[num].files = file;
                                }
                                num++;
                            }
                        }
                    }

                    var OK = this.Save(Data);
                    if (OK)
                    {
                        if (string.IsNullOrEmpty(ErrorMsg) == false)
                        {
                            TempData["StatusMessage"] = "部分申請認証失敗。";
                        }
                        else
                        {
                            TempData["StatusMessage"] = "代申請認証全部成功";
                        }
                        return View("ShowList", Data);
                    }
                    else
                    {
                        TempData["StatusMessage"] = "錯誤!!請修正。<br/>" + ErrorMsg;
                    }
                }
            }
            //else //新增預帶值
            //{
            //    if (Data.Search.ModeVal == (byte)Mode.ManyStudentIndex)
            //    {
            //        this.AddItem(Data, Data.Search.NumType);
            //    }
            //    else if (Data.Search.ModeVal == (byte)Mode.ManyBookIndex)
            //    {
            //        this.AddItem(Data, Data.Search.NumBook.ToString());
            //    }
            //}

            //計算明細筆數
            int Count = 0;
            int Total = 0;
            if (Data.Details_List != null)
            {
                Count = Data.Details_List.Count();
            }

            Total = (Data.ADDNUM == null) ? Count : (int)Data.ADDNUM + Count;
            TempData["TOLTAL"] = Total;
            if (Data.ADDNUM != null)
            {
                this.AddItem(Data, Data.ADDNUM.ToString());
                Data.ADDNUM = null;
            }

            //姓名 下拉
            IEnumerable<SelectListItem> USER_NOItems = null;

            USER_NOItems = HRMT01.GetUserNoListData(Data.Search.WhereSCHOOL_NO, Data.Search.WhereCLASS_NO, "", ref db);

            ViewBag.USER_NOItems = USER_NOItems;

            Data.Details_List.RemoveAll(a => a.Del == true);
            return View(Data);
        }
        public ActionResult UploadEdite1(ZZZI34EditViewModel Data, IEnumerable<HttpPostedFileBase> files, string DATA_TYPE, string ADDNUM)
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "代申請線上藝廊 -" + GetModeName(1) + ".新增(步驟2)";
            this.Shared();
            if (Data.Details_List == null) Data.Details_List = new List<ZZZI34DetailsViewModel>();

            if (string.IsNullOrEmpty(ADDNUM) == false) Data.ADDNUM = Convert.ToInt32(ADDNUM);

            if (string.IsNullOrEmpty(DATA_TYPE) == false)
            {
                if (DATA_TYPE == ZZZI26Controller.DATA_TYPE.DATA_TYPE_A) //新增明細不驗正
                {
                    ModelState.Clear(); //清除驗正

                    if (Data.ADDNUM == null)
                    {
                        ModelState.AddModelError("additem", "【增加明細】請輸入筆數");
                    }
                }
                else if (DATA_TYPE == ZZZI26Controller.DATA_TYPE.DATA_TYPE_S) // 存檔 / 批閱
                {
                    if (string.IsNullOrWhiteSpace(Data.Search.TEMP_BATCH_KEY))
                    {
                        if (VerifyUseYN == "Y")
                        {
                            int num = 0;
                            foreach (var file in files)
                            {
                                if (file != null && file.ContentLength > 0)
                                {
                                    Data.Details_List[num].IMG_FILE = Path.GetFileName(file.FileName);
                                    Data.Details_List[num].files = file;
                                }
                                num++;
                            }
                        }
                    }

                    var OK = this.Save(Data);
                    if (OK)
                    {
                        if (string.IsNullOrEmpty(ErrorMsg) == false)
                        {
                            TempData["StatusMessage"] = "部分申請認証失敗。";
                        }
                        else
                        {
                            TempData["StatusMessage"] = "代申請認証全部成功";
                        }
                        return View("ShowList", Data);
                    }
                    else
                    {
                        TempData["StatusMessage"] = "錯誤!!請修正。<br/>" + ErrorMsg;
                    }
                }
            }
            //else //新增預帶值
            //{
            //    if (Data.Search.ModeVal == (byte)Mode.ManyStudentIndex)
            //    {
            //        this.AddItem(Data, Data.Search.NumType);
            //    }
            //    else if (Data.Search.ModeVal == (byte)Mode.ManyBookIndex)
            //    {
            //        this.AddItem(Data, Data.Search.NumBook.ToString());
            //    }
            //}

            //計算明細筆數
            int Count = 0;
            int Total = 0;
            if (Data.Details_List != null)
            {
                Count = Data.Details_List.Count();
            }

            Total = (Data.ADDNUM == null) ? Count : (int)Data.ADDNUM + Count;
            TempData["TOLTAL"] = Total;
            if (Data.ADDNUM != null)
            {
                this.AddItem(Data, Data.ADDNUM.ToString());
                Data.ADDNUM = null;
            }

            //姓名 下拉
            IEnumerable<SelectListItem> USER_NOItems = null;
            ViewBag.ClassItems = HRMT01.GetClassListData(user.SCHOOL_NO, "", ref db).Where(a => a.Value == "");
            ViewBag.UserNoItems = HRMT01.GetUserNoListDataMust(user.SCHOOL_NO, "", "", ref db).Where(a => a.Value == "");


            ViewBag.USER_NOItems = USER_NOItems;

            Data.Details_List.RemoveAll(a => a.Del == true);
            return View(Data);
        }

        public ActionResult StudentUploadZipEdite(ZZZI34EditViewModel Data, IEnumerable<HttpPostedFileBase> files, string DATA_TYPE, string ADDNUM)
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "代申請線上藝廊 -" + GetModeName(1) + ".新增(步驟2)";
            this.Shared();
            if (Data.Details_List == null) Data.Details_List = new List<ZZZI34DetailsViewModel>();

            if (string.IsNullOrEmpty(ADDNUM) == false) Data.ADDNUM = Convert.ToInt32(ADDNUM);

            if (string.IsNullOrEmpty(DATA_TYPE) == false)
            {
                if (DATA_TYPE == ZZZI26Controller.DATA_TYPE.DATA_TYPE_A) //新增明細不驗正
                {
                    ModelState.Clear(); //清除驗正

                    if (Data.ADDNUM == null)
                    {
                        ModelState.AddModelError("additem", "【增加明細】請輸入筆數");
                    }
                }
                else if (DATA_TYPE == ZZZI26Controller.DATA_TYPE.DATA_TYPE_S) // 存檔 / 批閱
                {
                    if (string.IsNullOrWhiteSpace(Data.Search.TEMP_BATCH_KEY))
                    {
                        if (VerifyUseYN == "Y")
                        {
                            int num = 0;
                            foreach (var file in files)
                            {
                                if (file != null && file.ContentLength > 0)
                                {
                                    Data.Details_List[num].IMG_FILE = Path.GetFileName(file.FileName);
                                    Data.Details_List[num].files = file;
                                }
                                num++;
                            }
                        }
                    }

                    var OK = this.PersonSave(Data);
                    if (OK)
                    {
                        if (string.IsNullOrEmpty(ErrorMsg) == false)
                        {
                            TempData["StatusMessage"] = "部分申請認証失敗。";
                        }
                        else
                        {
                            TempData["StatusMessage"] = "代申請認証全部成功";
                        }
                        return View("ShowList", Data);
                    }
                    else
                    {
                        TempData["StatusMessage"] = "錯誤!!請修正。<br/>" + ErrorMsg;
                    }
                }
            }
            //else //新增預帶值
            //{
            //    if (Data.Search.ModeVal == (byte)Mode.ManyStudentIndex)
            //    {
            //        this.AddItem(Data, Data.Search.NumType);
            //    }
            //    else if (Data.Search.ModeVal == (byte)Mode.ManyBookIndex)
            //    {
            //        this.AddItem(Data, Data.Search.NumBook.ToString());
            //    }
            //}

            //計算明細筆數
            int Count = 0;
            int Total = 0;
            if (Data.Details_List != null)
            {
                Count = Data.Details_List.Count();
            }

            Total = (Data.ADDNUM == null) ? Count : (int)Data.ADDNUM + Count;
            TempData["TOLTAL"] = Total;
            if (Data.ADDNUM != null)
            {
                this.AddItem(Data, Data.ADDNUM.ToString());
                Data.ADDNUM = null;
            }

            //姓名 下拉
            IEnumerable<SelectListItem> USER_NOItems = null;

            USER_NOItems = HRMT01.GetUserNoListData(Data.Search.WhereSCHOOL_NO, Data.Search.WhereCLASS_NO, "", ref db);

            ViewBag.USER_NOItems = USER_NOItems;

            Data.Details_List.RemoveAll(a => a.Del == true);
            return View(Data);
        }

        /// <summary>
        /// 增加明細
        /// </summary>
        /// <param name="Data"></param>
        /// <param name="Num"></param>
        private void AddItem(ZZZI34EditViewModel Data, string Num)
        {
            if (Num == "ALL")
            {
                var HRMT01List = db.HRMT01.Where(a => a.SCHOOL_NO == Data.Search.WhereSCHOOL_NO && a.CLASS_NO == Data.Search.WhereCLASS_NO && a.USER_TYPE == UserType.Student
                && (!UserStaus.NGKeyinUserStausList.Contains(a.USER_STATUS))).ToList().OrderBy(o => o.SEAT_NO);

                if (HRMT01List != null)
                {
                    foreach (var item in HRMT01List)
                    {
                        ZZZI34DetailsViewModel T34 = new ZZZI34DetailsViewModel();
                        T34.USER_NO = item.USER_NO;
                        Data.Details_List.Add(T34);
                    }
                }
            }
            else
            {
                for (int i = 1; i <= Convert.ToInt16(Num); i++)
                {
                    ZZZI34DetailsViewModel T34 = new ZZZI34DetailsViewModel();

                    //if (Data.Search.ModeVal == (byte)Mode.ManyStudentIndex)
                    //{
                    //    T06.USER_NO = "";
                    //}
                    //else if (Data.Search.ModeVal == (byte)Mode.ManyBookIndex)
                    //{
                    //    T06.USER_NO = Data.Search.USER_NO;
                    //}

                    Data.Details_List.Add(T34);
                }
            }
        }
        public bool PersonSave(ZZZI34EditViewModel Data)
        {
            bool ReturnBool = false;

            try
            {

                //移除DEL有勾選的資料
                Data.Details_List.RemoveAll(a => a.Del == true);
                var errorsaa = ModelState
                                             .Where(x => x.Value.Errors.Count > 0)
                                             .Select(x => new { x.Key, x.Value.Errors })
                                             .ToArray();

                if (ModelState.IsValid) //沒有錯誤
                {
                    int CountOK = 0;

                    int SYear;
                    int Semesters;
                    SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);
                    List<HRMT01> H01ListData = db.HRMT01.Where(p => p.SCHOOL_NO == Data.Search.WhereSCHOOL_NO && (!UserStaus.NGKeyinUserStausList.Contains(p.USER_STATUS))).ToList();

                    List<ADDT22> SaveADDT22_List = new List<ADDT22>();
                    Data.People = new List<ZZZI34EditPeopleViewModel>();
                    bool OK = false;
                    string CheckNo = string.Empty;
                    if (Data.Details_List != null)
                    {
                        if (Data.Details_List.Count() > 0)
                        {
                            foreach (var Peitem in Data.Details_List.GroupBy(X => X.USER_NO).Select(x => x.Key))
                            {
                                ZZZI34EditPeopleViewModel peopels = new ZZZI34EditPeopleViewModel();
                                ZZZI34DetailsViewModel viewM = new ZZZI34DetailsViewModel();
                                List<ZZZI34EditPhotoViewModel> Photos = new List<ZZZI34EditPhotoViewModel>();
                                viewM = Data.Details_List.Where(x => x.USER_NO == Peitem).FirstOrDefault();
                                peopels.PHOTO_CLASS_NO = viewM.CLASS_NO;
                                peopels.PHOTO_USER_NO = viewM.USER_NO;
                                peopels.ART_GALLERY_TYPE = viewM.ART_GALLERY_TYPE;
                                peopels.ShowBtn = 1;
                                foreach (var item in Data.Details_List.Where(x => x.USER_NO == Peitem).ToList())

                                {
                                    ZZZI34EditPhotoViewModel PhotoItem = new ZZZI34EditPhotoViewModel();
                                    PhotoItem.ART_GALLERY_NO = item.ART_GALLERY_NO;
                                    PhotoItem.ART_GALLERY_TYPE = item.ART_GALLERY_TYPE;
                                  //  PhotoItem.PHOTO_CASH = item.PHOTO_CASH;
                                    PhotoItem.PhotoFiles = item.PhotoFiles;
                                    PhotoItem.PHOTO_SUBJECT = item.PHOTO_SUBJECT;
                                    PhotoItem.ShowBtn = 1;
                                    PhotoItem.WORK_TYPE = item.WORK_TYPE;
                                    Photos.Add(PhotoItem);
                                }
                                peopels.Photo = new List<ZZZI34EditPhotoViewModel>();
                                peopels.Photo = Photos;
                                Data.People.Add(peopels);
                            }
                            }
                        OK = Service.IsCheck(Data, user, ref db, ref ErrorMsg);

                    }
                    int ORDER_BY_NUM = 0;
                    if (OK)
                    {

                        if (Data.People != null && Data.People.Count()>0)
                        {
                            string GallaryNO = "";
                            string ART_GALLERY_TYPE = "";
                            GallaryNO = Data.Details_List.FirstOrDefault().ART_GALLERY_NO;
                            Data.Main.WORK_TYPE = Data.Details_List.FirstOrDefault().WORK_TYPE;
                            ART_GALLERY_TYPE = Data.Details_List.FirstOrDefault().ART_GALLERY_TYPE;
                            foreach (var Peitem1 in Data.People)
                            {
                                if (Peitem1.Photo?.Count() > 0)
                                {
                                    foreach (var PhotoItem in Peitem1.Photo)
                                    {


                                        ORDER_BY_NUM = ORDER_BY_NUM + 1;

                                        bool IsCreSavePhotoUp = false;

                                        ADDT22 SavePhotoUp = null;

                                        var Hr = db.HRMT01.Where(a => a.SCHOOL_NO == SCHOOL_NO && a.USER_NO == Peitem1.PHOTO_USER_NO).FirstOrDefault();

                                        if (Hr == null)
                                        {
                                            ErrorMsg += $"找不到此帳號{Peitem1.PHOTO_USER_NO}";
                                            return false;
                                        }

                                        if (string.IsNullOrWhiteSpace(PhotoItem.PHOTO_NO))
                                        {
                                            SavePhotoUp = new ADDT22();
                                            SavePhotoUp.PHOTO_NO = Guid.NewGuid().ToString("N");
                                            SavePhotoUp.ART_GALLERY_NO = GallaryNO;
                                            SavePhotoUp.PHOTO_STATUS = ADDT22.STATUSVal.OK;
                                            SavePhotoUp.PHOTO_SYEAR = Convert.ToByte(SYear);
                                            SavePhotoUp.AutherYN = false;
                                            SavePhotoUp.PHOTO_SEMESTER = Convert.ToByte(Semesters);
                                            SavePhotoUp.CRE_PERSON = user?.USER_KEY;
                                            SavePhotoUp.CRE_DATE = DateTime.Now;

                                            IsCreSavePhotoUp = true;
                                        }
                                        else
                                        {
                                            SavePhotoUp = db.ADDT22.Where(a => a.ART_GALLERY_NO == GallaryNO && a.PHOTO_NO == PhotoItem.PHOTO_NO).FirstOrDefault();

                                            if (SavePhotoUp == null)
                                            {
                                                SavePhotoUp = new ADDT22();
                                                SavePhotoUp.PHOTO_NO = PhotoItem.PHOTO_NO;
                                                SavePhotoUp.ART_GALLERY_NO = GallaryNO;
                                                SavePhotoUp.PHOTO_STATUS = ADDT22.STATUSVal.OK;
                                                SavePhotoUp.PHOTO_SYEAR = Convert.ToByte(SYear);
                                                SavePhotoUp.AutherYN = false;
                                                SavePhotoUp.PHOTO_SEMESTER = Convert.ToByte(Semesters);
                                                SavePhotoUp.CRE_PERSON = user?.USER_KEY;
                                                SavePhotoUp.CRE_DATE = DateTime.Now;

                                                IsCreSavePhotoUp = true;
                                            }

                                            if (SavePhotoUp.PHOTO_STATUS == ADDT22.STATUSVal.NG)
                                            {
                                                ErrorMsg = "此狀態不能異動資料(已作廢)";
                                                return false;
                                            }
                                        }
                                        if (PhotoItem.PhotoFiles?.ContentLength > 0)
                                        {
                                            SavePhotoUp.PHOTO_FILE = Service.UpLoadFile(SCHOOL_NO, GallaryNO, PhotoItem.PhotoFiles, ref ErrorMsg, Data.Main.WORK_TYPE);
                                            if (string.IsNullOrWhiteSpace(ErrorMsg) == false)
                                            {
                                                ErrorMsg = $"主題「{PhotoItem.PHOTO_SUBJECT}」，" + ErrorMsg;
                                                return false;
                                            }
                                        }
                                        SavePhotoUp.PHOTO_SUBJECT = PhotoItem.PHOTO_SUBJECT;
                                        SavePhotoUp.PHOTO_DESC = PhotoItem.PHOTO_DESC;
                                        SavePhotoUp.PHOTO_SCHOOL_NO = SCHOOL_NO;
                                        SavePhotoUp.PHOTO_USER_NO = Peitem1.PHOTO_USER_NO;
                                        SavePhotoUp.PHOTO_NAME = Hr?.NAME;
                                        SavePhotoUp.PHOTO_SNAME = Hr?.SNAME;
                                        SavePhotoUp.PHOTO_CLASS_NO = Hr?.CLASS_NO;
                                        SavePhotoUp.AutherYN = Peitem1.AutherYN;
                                        SavePhotoUp.CHG_PERSON = user?.USER_KEY;
                                        SavePhotoUp.CHG_DATE = DateTime.Now;

                                        SavePhotoUp.PHOTO_ORDER_BY = ORDER_BY_NUM;

                                        if (IsCreSavePhotoUp)
                                        {
                                            db.ADDT22.Add(SavePhotoUp);
                                        }

                                        SaveADDT22_List.Add(SavePhotoUp);
                                    }
                                    }
                                }
                            if (SaveADDT22_List.Count() > 0)
                            {
                                var OrtGallery = db.ADDT22.Where(o => o.ART_GALLERY_NO == GallaryNO).ToList();

                                if (OrtGallery.Count > 0)
                                {
                                    var Difference = from o in OrtGallery
                                                     where !(
                                                         from N in SaveADDT22_List
                                                         select N.PHOTO_NO
                                                       ).Contains(o.PHOTO_NO)
                                                     select o;
                                    if (Data.Main.WORK_TYPE == ADDT21.WORK_TYPE_VAL.Youtube)
                                    {
                                        db.ADDT22.RemoveRange(Difference);
                                    }
                                    else
                                    {
                                        foreach (var item in Difference)
                                        {
                                            Service.DelFile(item.PHOTO_SCHOOL_NO, item.ART_GALLERY_NO, item.PHOTO_FILE, ref ErrorMsg);

                                            if (string.IsNullOrWhiteSpace(ErrorMsg) == false)
                                            {
                                                ErrorMsg = $"主題「{item.PHOTO_SUBJECT}」，" + ErrorMsg;
                                                return false;
                                            }
                                        }

                                        db.ADDT22.RemoveRange(Difference);
                                    }
                                }

                            }
                            try
                            {
                                ReturnBool = true;
                                db.SaveChanges();
                            }
                            catch (Exception ex)
                            {
                                ErrorMsg = "系統發生錯誤;原因:" + ex.Message;
                                return false;
                            }
                        }
                    }
                }
               
                }
            catch (Exception e) {
                ErrorMsg = e.Message;

            }
            return ReturnBool;

        }
            /// <summary>
            /// 資料處理
            /// </summary>
            /// <param name="Data"></param>
            /// <returns></returns>
            public bool Save(ZZZI34EditViewModel Data)
        {
            bool ReturnBool = false;

            try
            {
                //移除DEL有勾選的資料
                Data.Details_List.RemoveAll(a => a.Del == true);

#if DEBUG
                var errorsaa = ModelState
                                                .Where(x => x.Value.Errors.Count > 0)
                                                .Select(x => new { x.Key, x.Value.Errors })
                                                .ToArray();
#endif
                if (ModelState.IsValid) //沒有錯誤
                {
                    int CountOK = 0;

                    int SYear;
                    int Semesters;
                    SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);
                    List<HRMT01> H01ListData = db.HRMT01.Where(p => p.SCHOOL_NO == Data.Search.WhereSCHOOL_NO && (!UserStaus.NGKeyinUserStausList.Contains(p.USER_STATUS))).ToList();

                    List<ADDT22> SaveADDT22_List = new List<ADDT22>();

                    Data.People = new List<ZZZI34EditPeopleViewModel>();
                    bool OK = false;
                    string CheckNo = string.Empty;
                    if (Data.Details_List != null)
                    {
                        if (Data.Details_List.Count() > 0)
                        {
                            foreach (var Peitem in Data.Details_List.GroupBy(X => X.USER_NO).Select(x => x.Key))
                            {
                                ZZZI34EditPeopleViewModel peopels = new ZZZI34EditPeopleViewModel();
                                ZZZI34DetailsViewModel viewM = new ZZZI34DetailsViewModel();
                                List<ZZZI34EditPhotoViewModel> Photos = new List<ZZZI34EditPhotoViewModel>();
                                viewM = Data.Details_List.Where(x => x.USER_NO == Peitem).FirstOrDefault();
                                peopels.PHOTO_CLASS_NO = viewM.CLASS_NO;
                                peopels.PHOTO_USER_NO = viewM.USER_NO;
                                peopels.ART_GALLERY_TYPE = viewM.ART_GALLERY_TYPE;
                                peopels.ShowBtn = 1;
                                foreach (var item in Data.Details_List.Where(x => x.USER_NO == Peitem).ToList())

                                {
                                    ZZZI34EditPhotoViewModel PhotoItem = new ZZZI34EditPhotoViewModel();
                                    PhotoItem.ART_GALLERY_NO = item.ART_GALLERY_NO;
                                    PhotoItem.ART_GALLERY_TYPE = item.ART_GALLERY_TYPE;
                                    PhotoItem.PHOTO_CASH = item.PHOTO_CASH;
                                    PhotoItem.PhotoFiles = item.PhotoFiles;
                                    PhotoItem.PHOTO_SUBJECT = item.PHOTO_SUBJECT;
                                    PhotoItem.ShowBtn = 1;
                                    PhotoItem.WORK_TYPE = item.WORK_TYPE;
                                    Photos.Add(PhotoItem);
                                }
                                peopels.Photo = new List<ZZZI34EditPhotoViewModel>();
                                peopels.Photo = Photos;
                                Data.People.Add(peopels);
                            }
                        }

                        OK = Service.IsCheck(Data, user, ref db, ref ErrorMsg);
                    }
                    int ORDER_BY_NUM = 0;
                    if (OK)
                    {
                        if (Data.People != null)
                        {
                            if (Data.People.Count() > 0)
                            {
                                string GallaryNO = "";
                                string ART_GALLERY_TYPE = "";
                                GallaryNO = Data.Details_List.FirstOrDefault().ART_GALLERY_NO;
                                Data.Main.WORK_TYPE = Data.Details_List.FirstOrDefault().WORK_TYPE;
                                ART_GALLERY_TYPE = Data.Details_List.FirstOrDefault().ART_GALLERY_TYPE;
                                foreach (var Peitem1 in Data.People)
                                {
                                    if (Peitem1.Photo?.Count() > 0)
                                    {
                                        foreach (var PhotoItem in Peitem1.Photo)
                                        {
                                            ORDER_BY_NUM = ORDER_BY_NUM + 1;

                                            bool IsCreSavePhotoUp = false;

                                            ADDT22 SavePhotoUp = null;

                                            var Hr = db.HRMT01.Where(a => a.SCHOOL_NO == SCHOOL_NO && a.USER_NO == Peitem1.PHOTO_USER_NO).FirstOrDefault();

                                            if (Hr == null)
                                            {
                                                ErrorMsg += $"找不到此帳號{Peitem1.PHOTO_USER_NO}";
                                                return false;
                                            }

                                            if (string.IsNullOrWhiteSpace(PhotoItem.PHOTO_NO))
                                            {
                                                SavePhotoUp = new ADDT22();
                                                SavePhotoUp.PHOTO_NO = Guid.NewGuid().ToString("N");
                                                SavePhotoUp.ART_GALLERY_NO = GallaryNO;
                                                SavePhotoUp.PHOTO_STATUS = ADDT22.STATUSVal.OK;
                                                SavePhotoUp.PHOTO_SYEAR = Convert.ToByte(SYear);
                                                SavePhotoUp.AutherYN = false;
                                                SavePhotoUp.PHOTO_SEMESTER = Convert.ToByte(Semesters);
                                                SavePhotoUp.CRE_PERSON = user?.USER_KEY;
                                                SavePhotoUp.CRE_DATE = DateTime.Now;

                                                IsCreSavePhotoUp = true;
                                            }
                                            else
                                            {
                                                SavePhotoUp = db.ADDT22.Where(a => a.ART_GALLERY_NO == GallaryNO && a.PHOTO_NO == PhotoItem.PHOTO_NO).FirstOrDefault();

                                                if (SavePhotoUp == null)
                                                {
                                                    SavePhotoUp = new ADDT22();
                                                    SavePhotoUp.PHOTO_NO = PhotoItem.PHOTO_NO;
                                                    SavePhotoUp.ART_GALLERY_NO = GallaryNO;
                                                    SavePhotoUp.PHOTO_STATUS = ADDT22.STATUSVal.OK;
                                                    SavePhotoUp.PHOTO_SYEAR = Convert.ToByte(SYear);
                                                    SavePhotoUp.AutherYN = false;
                                                    SavePhotoUp.PHOTO_SEMESTER = Convert.ToByte(Semesters);
                                                    SavePhotoUp.CRE_PERSON = user?.USER_KEY;
                                                    SavePhotoUp.CRE_DATE = DateTime.Now;

                                                    IsCreSavePhotoUp = true;
                                                }

                                                if (SavePhotoUp.PHOTO_STATUS == ADDT22.STATUSVal.NG)
                                                {
                                                    ErrorMsg = "此狀態不能異動資料(已作廢)";
                                                    return false;
                                                }
                                            }
                                            if (PhotoItem.PhotoFiles?.ContentLength > 0)
                                            {
                                                SavePhotoUp.PHOTO_FILE = Service.UpLoadFile(SCHOOL_NO, GallaryNO, PhotoItem.PhotoFiles, ref ErrorMsg, Data.Main.WORK_TYPE);
                                                if (string.IsNullOrWhiteSpace(ErrorMsg) == false)
                                                {
                                                    ErrorMsg = $"主題「{PhotoItem.PHOTO_SUBJECT}」，" + ErrorMsg;
                                                    return false;
                                                }
                                            }
                                            SavePhotoUp.PHOTO_SUBJECT = PhotoItem.PHOTO_SUBJECT;
                                            SavePhotoUp.PHOTO_DESC = PhotoItem.PHOTO_DESC;

                                            if (ART_GALLERY_TYPE == ADDT21.ART_GALLERY_TYPE_VAL.Campus
                                                //&& SaveUp.STATUS == ADDT21.STATUSVal.NotStarted
                                                )
                                            {
                                                SavePhotoUp.PHOTO_CASH = PhotoItem.PHOTO_CASH;
                                            }

                                            SavePhotoUp.PHOTO_SCHOOL_NO = SCHOOL_NO;
                                            SavePhotoUp.PHOTO_USER_NO = Peitem1.PHOTO_USER_NO;
                                            SavePhotoUp.PHOTO_NAME = Hr?.NAME;
                                            SavePhotoUp.PHOTO_SNAME = Hr?.SNAME;
                                            SavePhotoUp.PHOTO_CLASS_NO = Hr?.CLASS_NO;
                                            SavePhotoUp.AutherYN = Peitem1.AutherYN;
                                            SavePhotoUp.CHG_PERSON = user?.USER_KEY;
                                            SavePhotoUp.CHG_DATE = DateTime.Now;

                                            SavePhotoUp.PHOTO_ORDER_BY = ORDER_BY_NUM;

                                            if (IsCreSavePhotoUp)
                                            {
                                                db.ADDT22.Add(SavePhotoUp);
                                            }

                                            SaveADDT22_List.Add(SavePhotoUp);
                                        }
                                    }
                                }

                                if (SaveADDT22_List.Count() > 0)
                                {
                                    var OrtGallery = db.ADDT22.Where(o => o.ART_GALLERY_NO == GallaryNO).ToList();

                                    if (OrtGallery.Count > 0)
                                    {
                                        var Difference = from o in OrtGallery
                                                         where !(
                                                             from N in SaveADDT22_List
                                                             select N.PHOTO_NO
                                                           ).Contains(o.PHOTO_NO)
                                                         select o;
                                        if (Data.Main.WORK_TYPE == ADDT21.WORK_TYPE_VAL.Youtube)
                                        {
                                            db.ADDT22.RemoveRange(Difference);
                                        }
                                        else
                                        {
                                            foreach (var item in Difference)
                                            {
                                                Service.DelFile(item.PHOTO_SCHOOL_NO, item.ART_GALLERY_NO, item.PHOTO_FILE, ref ErrorMsg);

                                                if (string.IsNullOrWhiteSpace(ErrorMsg) == false)
                                                {
                                                    ErrorMsg = $"主題「{item.PHOTO_SUBJECT}」，" + ErrorMsg;
                                                    return false;
                                                }
                                            }

                                            db.ADDT22.RemoveRange(Difference);
                                        }
                                    }
                                }

                                try
                                {
                                    ReturnBool = true;
                                    db.SaveChanges();
                                }
                                catch (Exception ex)
                                {
                                    ErrorMsg = "系統發生錯誤;原因:" + ex.Message;
                                    return false;
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorMsg = ex.Message;
            }

            return ReturnBool;
        }

        private static String GetRelativePath(String basePath, String targetPath)
        {
            Uri baseUri = new Uri(basePath);
            Uri targetUri = new Uri(targetPath);
            return baseUri.MakeRelativeUri(targetUri).ToString().Replace(@"/", @"\");
        }

        //讀取目錄下所有檔案
        private static ArrayList GetFiles(string path)
        {
            ArrayList files = new ArrayList();
            ECOOL_APP.com.ecool.service.SortByFilleName.NaturalFileInfoNameComparer SortByFilleName = new ECOOL_APP.com.ecool.service.SortByFilleName.NaturalFileInfoNameComparer();
            
            if (Directory.Exists(path))
            {
                string[] s = Directory.GetFiles(path, "*", SearchOption.AllDirectories);
                DirectoryInfo directoryInfo = new DirectoryInfo(path);
                FileInfo[] fils2=directoryInfo.GetFiles("*", SearchOption.AllDirectories);
                Array.Sort(fils2, (FileInfo x, FileInfo y) => SortByFilleName.Compare(x, y));
                foreach (var  items in fils2) {

                    files.Add(items.FullName);
                }
           
             
            }
            
            
            return files;
        }

        public static string GetModeName(byte? ModeValue)
        {
            if (ModeValue == 0)
                return "批次壓縮上傳";
            else
                return "逐筆上傳";
        }

        //[CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult ArtGalleryWorkList(ZZZI34WorkIndexViewModel model)
        {
            this.Shared();

            if (!string.IsNullOrWhiteSpace(model.Search?.WhereART_GALLERY_NO))
            {
                var ART_SUBJECT = db.ADDT21.Where(a => a.ART_GALLERY_NO == model.Search.WhereART_GALLERY_NO).Select(a => a.ART_SUBJECT).FirstOrDefault();

                if (!string.IsNullOrWhiteSpace(ART_SUBJECT))
                {
                    SetTitle(Bre_Name + "-" + ART_SUBJECT);
                }
            }
            else
            {
                if (model.WhereMyWork != null)
                {
                    SetTitle(Bre_Name + "-我的作品");
                }
                else
                {
                    SetTitle(Bre_Name + "-全部作品");
                }
            }

            return View(model);
        }
        
        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult _WorkPageContent(ZZZI34WorkIndexViewModel model)
        {
            this.Shared();
            if (model == null) model = new ZZZI34WorkIndexViewModel();
            if (model.Search == null) model.Search = new ZZZI34SearchViewModel();
            if (model.WorkSearch == null) model.WorkSearch = new ZZZI34WorkSearchViewModel();

            if (string.IsNullOrWhiteSpace(model.Search.WhereSCHOOL_NO))
            {
                model.Search.WhereSCHOOL_NO = SCHOOL_NO;
            }

            if (model.WhereMyWork != null)
            {
                model.Search.WhereMyWork = model.WhereMyWork;
            }

            if ((model.Search.WhereMyWork ?? false))
            {
                model.Search.WhereUSER_NO = USER_NO;
            }

            if (!IsAdmin)
            {
                model.Search.WhereUSER_NO = USER_NO;
            }

            if (!string.IsNullOrWhiteSpace(Request.ServerVariables["HTTP_VIA"]?.ToString()))
            {
                model.IP_ADDRESS = Request.ServerVariables["HTTP_X_FORWARDED_FOR"]?.ToString();
            }
            else
            {
                model.IP_ADDRESS = Request.ServerVariables["REMOTE_ADDR"]?.ToString();
            }

            if (string.IsNullOrWhiteSpace(model.Search.WhereART_GALLERY_NO))
            {
                if (string.IsNullOrWhiteSpace(model.IsPostBack))
                {
                    if (model.Search.WhereSYEAR == null && model.WhereMyWork==false)
                    {
                        model.Search.WhereSYEAR = SysHelper.GetNowSYear(DateTime.Now);
                    }
                }

                ViewBag.SYEARItems = HRMT01.GetSYearsItems(model.Search.WhereSYEAR.ToString(), ref db);

                ViewBag.GradeItem = HRMT01.GetGradeItems(model.Search.WhereGrade);

                ViewBag.ClassItems = HRMT01.GetClassListData(model.Search.WhereSCHOOL_NO, model.Search.WhereGrade, model.Search.WhereCLASS_NO, ref db)
                    .Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.Search.WhereGrade });
            }

            model = Service.GetArtGalleryWorkListData(model, user, ref db);
            return PartialView(model);
        }
        public ActionResult ArtGalleryDateMid(ZZZI34WorkIndexViewModel modelitem)
        {
            ZZZI34WorkIndexViewModel model1 = new ZZZI34WorkIndexViewModel();
            this.Shared();
            modelitem = Service.GetArtGalleryWorkListData(modelitem, user, ref db);
            model1.Search = new ZZZI34SearchViewModel();
            model1.WorkListData = modelitem.WorkListData;
            model1.WorkSearch = new ZZZI34WorkSearchViewModel();
            model1.WorkSearch.Page = 1;
            model1.Search.WhereART_GALLERY_NO = modelitem.Search.WhereART_GALLERY_NO;

            model1.Search = modelitem.Search;
            TempData["model"] = model1;
            //model1.ShareViewPHOTO_NO = modelitem.WorkListData.Select(x=>x.PHOTO_NO).FirstOrDefault();
            return RedirectToAction("OneIndexForArGallery", new ZZZI34WorkIndexViewModel(){
                WhereART_GALLERY_NOGallaryN1 =  modelitem.Search.WhereART_GALLERY_NO
              

            });
        }
        //[CheckPermission(CheckACTION_ID = "Index")] //檢查權限


        //[CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult _OnePageContent(ZZZI34WorkIndexViewModel model)
        {
            this.Shared();
            if (model == null) model = new ZZZI34WorkIndexViewModel();
            if (model.Search == null) model.Search = new ZZZI34SearchViewModel();
            if (model.WorkSearch == null) model.WorkSearch = new ZZZI34WorkSearchViewModel();
            if (!string.IsNullOrWhiteSpace(model.WhereART_GALLERY_NOGallaryN1))
            {
                ZZZI34WorkIndexViewModel temp = new ZZZI34WorkIndexViewModel();
                model.Search.WhereART_GALLERY_NO = model.WhereART_GALLERY_NOGallaryN1;
                if (TempData["model"] != null)
                {


                    temp = (ZZZI34WorkIndexViewModel)TempData["model"];
                    if (temp.Search != null)
                    {

                        model.Search.WhereUSER_NO = temp.Search?.WhereUSER_NO;
                        model.Search.WhereSCHOOL_NO = temp.Search?.WhereSCHOOL_NO;
                    }

                }
            }
            if (!string.IsNullOrWhiteSpace(model.ShareViewPHOTO_NO))
            {
                model.Search.WherePHOTO_NO = model.ShareViewPHOTO_NO;
            }

            if (model.WhereMyWork != null)
            {
                model.Search.WhereMyWork = model.WhereMyWork;
            }

            if (!string.IsNullOrWhiteSpace(model.Search.WhereSCHOOL_NO)) {
                if (!string.IsNullOrWhiteSpace(model.WhereART_GALLERY_NOGallaryN1))
                {
                    ADDT21 add21TEMP = new ADDT21();
                    add21TEMP = db.ADDT21.Where(x => x.ART_GALLERY_NO == model.WhereART_GALLERY_NOGallaryN1 && x.STATUS != ADDT21.STATUSVal.Disabled).FirstOrDefault();
             
                    if (add21TEMP.SCHOOL_NO != model.Search.WhereSCHOOL_NO)
                    {

                        model.Search.WhereSCHOOL_NO = add21TEMP.SCHOOL_NO;
                        SCHOOL_NO = add21TEMP.SCHOOL_NO;


                    }
                }
               
            }
            if (string.IsNullOrWhiteSpace(model.Search.WhereSCHOOL_NO))
            {

                if (string.IsNullOrWhiteSpace(SCHOOL_NO))
                {
                    if (!string.IsNullOrWhiteSpace(model.WhereART_GALLERY_NOGallaryN1))
                    {
                        ADDT21 add21TEMP = new ADDT21();
                        add21TEMP = db.ADDT21.Where(x => x.ART_GALLERY_NO == model.WhereART_GALLERY_NOGallaryN1 && x.STATUS != ADDT21.STATUSVal.Disabled).FirstOrDefault();
                        model.Search.WhereSCHOOL_NO = add21TEMP.SCHOOL_NO;
                        SCHOOL_NO = add21TEMP.SCHOOL_NO;
                    }
                  

                }
                else
                {
                    if (!string.IsNullOrWhiteSpace(model.WhereART_GALLERY_NOGallaryN1))
                    {
                        ADDT21 add21TEMP = new ADDT21();
                        add21TEMP = db.ADDT21.Where(x => x.ART_GALLERY_NO == model.WhereART_GALLERY_NOGallaryN1 && x.STATUS != ADDT21.STATUSVal.Disabled).FirstOrDefault();
                    
                        if (add21TEMP.SCHOOL_NO != model.Search.WhereSCHOOL_NO)
                        {

                            model.Search.WhereSCHOOL_NO = add21TEMP.SCHOOL_NO;
                            SCHOOL_NO = add21TEMP.SCHOOL_NO;


                        }
                        else
                        {
                            model.Search.WhereSCHOOL_NO = SCHOOL_NO;
                        }
                    }
                    else
                    {
                        model.Search.WhereSCHOOL_NO = SCHOOL_NO;
                    }
                }
            }
            if (!string.IsNullOrWhiteSpace(model.WhereART_GALLERY_NOGallaryN1))
            {
                model.Search.WhereART_GALLERY_NO = model.WhereART_GALLERY_NOGallaryN1;
            }

            if (!IsAdmin)
            {
                model.Search.WhereUSER_NO = USER_NO;
            }

            if (!string.IsNullOrWhiteSpace(Request.ServerVariables["HTTP_VIA"]?.ToString()))
            {
                model.IP_ADDRESS = Request.ServerVariables["HTTP_X_FORWARDED_FOR"]?.ToString();
            }
            else
            {
                model.IP_ADDRESS = Request.ServerVariables["REMOTE_ADDR"]?.ToString();
            }

            model.PageSize = 1;

            model = Service.GetArtGalleryWorkListData(model, user, ref db);

            if (model.WorkListData != null && model.WorkListData.Count() != 0)
            {
                ViewBag.WinOpenShareUrlLink = UrlCustomHelper.GetOwnWebUri() + $@"/EcoolWeb//{Bre_NO}/OneIndex?ShareViewPHOTO_NO={model.WorkListData.FirstOrDefault().PHOTO_NO}";
            }

            return PartialView(model);
        }

        [CheckPermission(CheckACTION_ID = "Edit")] //檢查權限
        public ActionResult Edit(ZZZI34EditViewModel model)
        {
            this.Shared();
            if (model == null) model = new ZZZI34EditViewModel();
            if (model.Search == null) model.Search = new ZZZI34SearchViewModel();

            model = Service.GetEditArtGallery(model, user, ref db);
         
            if (model.Main?.ART_GALLERY_NO == null)
            {
                this.Shared(Bre_Name + "-新增封面");

                model.Main = new ZZZI34EditMainViewModel();
                model.Main.ART_GALLERY_TYPE = user?.USER_TYPE == UserType.Student ? ADDT21.ART_GALLERY_TYPE_VAL.Personal : ADDT21.ART_GALLERY_TYPE_VAL.Campus;
            }
           
            else
            {
                if (model.SaveType == ZZZI34EditViewModel.SaveTypeVal.Verify)
                {
                    this.Shared(Bre_Name + "-批閱線上藝廊內容");
                    ViewBag.VisibleVerify = (PermissionService.GetPermission_Use_YN(Bre_NO, "Verify", user?.SCHOOL_NO, user?.USER_NO) == "Y" ? true : false);
                }
                else
                {
                    this.Shared(Bre_Name + "-" + model.Main.ART_SUBJECT);
                    ViewBag.VisibleVerify = false;
                }
            }

            return View(model);
        }

        public JsonResult Checkbatch(ZZZI34EditViewModel model)
        {
            this.Shared();
            if (model == null) model = new ZZZI34EditViewModel();
            if (model.Search == null) model.Search = new ZZZI34SearchViewModel();
            string Message = string.Empty;
            bool OK = false;
            if (model.Details_List != null && model.Details_List.Count() > 0)
            {
            }
            try
            {
                OK = Service.IsCheck(model, user, ref db, ref Message);
            }
            catch (Exception ex)
            {
                throw ex;
            }

            string output = JsonConvert.SerializeObject(
            new
            {
                Success = OK.ToString(),
                Error = Message,
            });

            return Json(output, JsonRequestBehavior.AllowGet);
        }
        [CheckPermission(CheckACTION_ID = "Edit")] //檢查權限
        public JsonResult PersonCheckJosn(ZZZI34EditViewModel model)
        {

            this.Shared();
            if (model == null) model = new ZZZI34EditViewModel();
            if (model.Search == null) model.Search = new ZZZI34SearchViewModel();
            string Message = string.Empty;
            bool OK = false;
            bool ISAdmin = HRMT24_ENUM.CheckQAdmin(user);


            try
            {
                OK = Service.IsCheck(model, user, ref db, ref Message);


            }
            catch (Exception ex)
            {
                throw ex;
            }

            string output = JsonConvert.SerializeObject(
            new
            {
                Success = OK.ToString(),
                Error = Message,
            });

            return Json(output, JsonRequestBehavior.AllowGet);
        }
        [CheckPermission(CheckACTION_ID = "Edit")] //檢查權限
        public JsonResult CheckJosn(ZZZI34EditViewModel model)
        {
            this.Shared();
            if (model == null) model = new ZZZI34EditViewModel();
            if (model.Search == null) model.Search = new ZZZI34SearchViewModel();
            string Message = string.Empty;
            bool OK = false;
            bool ISAdmin = HRMT24_ENUM.CheckQAdmin(user);
         

            try
            {
                OK = Service.IsCheck( model, user, ref db, ref Message);
              
             
            }
            catch (Exception ex)
            {
                throw ex;
            }

            string output = JsonConvert.SerializeObject(
            new
            {
                Success = OK.ToString(),
                Error = Message,
            });

            return Json(output, JsonRequestBehavior.AllowGet);
        }

        [CheckPermission(CheckACTION_ID = "Edit")] //檢查權限
        public ActionResult EditSave(ZZZI34EditViewModel model)
        {
            if (model == null) model = new ZZZI34EditViewModel();
            if (model.Search == null) model.Search = new ZZZI34SearchViewModel();

            int SYear;
            int Semesters;
            SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);
            ViewBag.VisibleVerify = (PermissionService.GetPermission_Use_YN(Bre_NO, "Verify", user?.SCHOOL_NO, user?.USER_NO) == "Y" ? true : false);

            if (model.Main?.ART_GALLERY_NO == null)
            {
                this.Shared(Bre_Name + "-新增封面");
            }
            else
            {
                if (model.SaveType == ZZZI34EditViewModel.SaveTypeVal.Verify)
                {
                    this.Shared(Bre_Name + "-批閱線上藝廊內容");
                }
                else
                {
                    this.Shared(Bre_Name + "-" + model.Main.ART_SUBJECT);
                }
            }

            string Message = string.Empty;

            if (ModelState.IsValid == false)
            {
                Message = "警告!!輸入的內容有錯誤";
            }
            else
            {
                bool OK = false;
                string SCHOOL_NOUSER = "";
                SCHOOL_NOUSER = user.SCHOOL_NO + "_" + user.USER_NO;
                bool IsOkForArt = true;
                //檢查是否本學期超過
                if (user.USER_TYPE == UserType.Student)
                {
                    int SemeterCount = 0;
                    SemeterCount = db.ADDT21.Where(x => x.SCHOOL_NO == user.SCHOOL_NO && x.CRE_PERSON == SCHOOL_NOUSER && x.SEMESTER == Semesters && x.SYEAR == SYear &&x.WORK_TYPE == ADDT21.ART_GALLERY_TYPE_VAL.Personal).Count();
                    if (SemeterCount >= 3)
                    {

                        Message = "「每學期半年」最多3次畫展";
                        IsOkForArt = false;
                    }

                }
                // else {
                if (IsOkForArt) { 
                    OK = Service.IsCheck(model, user, ref db, ref Message);
                    bool ISAdmin = HRMT24_ENUM.CheckQAdmin(user);
                    if (OK)
                    {
                        ZZZI34ViewBackModel zZZI34ViewBackModel = new ZZZI34ViewBackModel();
                        zZZI34ViewBackModel = Service.SaveArtGallery(model, user, ref db, ref Message, model.IsTempSave);

                        if (zZZI34ViewBackModel.status)
                        {
                            ADDT21 SaveUp = db.ADDT21.Where(a => a.ART_GALLERY_NO == zZZI34ViewBackModel.WhereART_GALLERY_NO).FirstOrDefault();
                            if (SaveUp.CRE_PERSON == user.USER_KEY && (ISAdmin || user.USER_TYPE == "T"))
                            {
                                model.Search.WhereART_GALLERY_NO = zZZI34ViewBackModel.WhereART_GALLERY_NO;
                                model.SaveType = ZZZI34EditViewModel.SaveTypeVal.Verify;
                                List<Tuple<string, string, int>> valuesList = new List<Tuple<string, string, int>>();
                                Service.UpdateStatus(model, user, ref db, ref Message, true, ref valuesList);

                            }
                            if (model.Main.ART_GALLERY_TYPE == ADDT21.ART_GALLERY_TYPE_VAL.Campus)
                            {
                                if ((model.Main?.STATUS ?? ADDT21.STATUSVal.NotStarted) == ADDT21.STATUSVal.NotStarted)
                                {
                                    if (model.Details_List != null && model.Details_List.Count() > 0)
                                    {
                                        model.Main.ART_GALLERY_TYPE = ADDT21.ART_GALLERY_TYPE_VAL.Campus;
                                        model.SaveType = ZZZI34EditViewModel.SaveTypeVal.Verify;
                                        model.Search.WhereART_GALLERY_NO = model.Main.ART_GALLERY_NO;

                                        List<Tuple<string, string, int>> valuesList = new List<Tuple<string, string, int>>();
                                        Service.UpdateStatus(model, user, ref db, ref Message, true, ref valuesList);
                                    }
                                    if (model.ZZZI34ImgType == "G")
                                    {
                                        TempData["StatusMessage"] = "完成藝廊申請";
                                    }
                                    else
                                    {
                                        TempData["StatusMessage"] = "儲存完成，提醒您需「發佈」";
                                    }
                                }
                                else
                                {
                                    TempData["StatusMessage"] = "儲存完成";
                                }
                            }
                            else
                            {
                                if ((model.Main?.STATUS ?? ADDT21.STATUSVal.NotStarted) == ADDT21.STATUSVal.NotStarted)
                                {
                                    TempData["StatusMessage"] = "儲存完成<br/>※個人藝廊必須經過審核給酷幣點數後，才能公開分享。<br/>※老師審核前，可自行<a role=\"button\" href=\" javascript:Edit_show('" + zZZI34ViewBackModel.WhereART_GALLERY_NO + "')\">修改 </a>內容";
                                }
                                else
                                {
                                    TempData["StatusMessage"] = "儲存完成";
                                }
                            }

                            ZZZI34IndexViewModel Qmodel = new ZZZI34IndexViewModel()
                            {
                                Search = new ZZZI34SearchViewModel()
                            };
                            Qmodel.Search = model.Search;
                            Qmodel.WhereMyWork = true;
                            return View("ArtGalleryList", Qmodel);
                        }
                    }

                    //  }
                }

                }

#if DEBUG
                var errorsaa = ModelState
                        .Where(x => x.Value.Errors.Count > 0)
                        .Select(x => new { x.Key, x.Value.Errors })
                        .ToArray();
#endif

            TempData["StatusMessage"] = Message;
            if (model.Details_List != null && model.Details_List.Count() > 0)
            {
                return View("UploadEdit", model);
            }
            else
            {
                return View("Edit", model);
            }
        }

        public ActionResult ExportExcel(ZZZI34OrderListViewModel model)
        {
            SCHOOL_NO = UserProfileHelper.GetSchoolNo();
            string Message = string.Empty;
            if (model == null) model = new ZZZI34OrderListViewModel();

            if (string.IsNullOrWhiteSpace(model.WhereSCHOOL_NO))
            {
                model.WhereSCHOOL_NO = SCHOOL_NO;
            }
            model.IsToExcel = true;
            model = Service.GetOrderListData(model, ref db, ref Message);
            var DataList = model.ListData.ToList();
            DataTable DataTableExcel = DataList?.AsDataTable();
            DataTableExcel.Columns.Add("RowNum", typeof(int));
            int i = 0;
            foreach (DataRow row in DataTableExcel.Rows)
            { row["RowNum"] = ++i; }
            NPOIHelper npoi = new NPOIHelper();
            string TempleteFileFullName = Server.MapPath(@"~/Content/ExcelSample/OrderListExcelFORZZZ034.xlsx");

            FileStream xlsfile = new FileStream(TempleteFileFullName, FileMode.Open, FileAccess.Read);
            IWorkbook aBook = WorkbookFactory.Create(xlsfile);

            npoi.ExportExcelFromTemplete(DataTableExcel, aBook, "線上藝廊排行榜", false, 2);

            string strTMPFile = System.Web.Configuration.WebConfigurationManager.AppSettings["FilePath"] + @"\TMP\" + DateTime.Now.Date.ToString("yyyyMMdd");

            if (Directory.Exists(strTMPFile) == false)
            {
                Directory.CreateDirectory(strTMPFile);
            }

            strTMPFile = strTMPFile + @"\線上藝廊排行榜" + Session.SessionID + ".xlsx";

            System.IO.FileStream FS = new FileStream(strTMPFile, FileMode.Create);
            aBook.Write(FS);
            FS.Close();

            return File(strTMPFile, "application/vnd.ms-excel", "線上藝廊排行榜.xlsx");//輸出檔案給Client端
        }

        [CheckPermission(CheckACTION_ID = "Edit")] //檢查權限
        public ActionResult _People(ZZZI34EditPeopleViewModel Item)
        {
            this.Shared();
            if (Item == null) Item = new ZZZI34EditPeopleViewModel();
            if (Item.PHOTO_SCHOOL_NO == null) Item.PHOTO_SCHOOL_NO = SCHOOL_NO;
            if (Item.AutherYN == null)
            {
                Item.AutherYN = false;
            }

            if (Item.ART_GALLERY_TYPE == ADDT21.ART_GALLERY_TYPE_VAL.Personal && Item.PHOTO_USER_NO == null)
            {
                Item.PHOTO_CLASS_NO = user.CLASS_NO;
                Item.PHOTO_USER_NO = user.USER_NO;
                Item.PHOTO_SNAME = user.SNAME;
            }

            if (Item.ShowBtn == ZZZI34EditViewModel.ShowBtnVal.NotCash)
            {
                ViewBag.ClassItems = HRMT01.GetClassListData(Item.PHOTO_SCHOOL_NO, Item.PHOTO_CLASS_NO, ref db).Where(a => a.Value == Item.PHOTO_CLASS_NO);
                ViewBag.UserNoItems = HRMT01.GetUserNoListDataMust(Item.PHOTO_SCHOOL_NO, Item.PHOTO_CLASS_NO, Item.PHOTO_USER_NO, ref db).Where(a => a.Value == Item.PHOTO_USER_NO); 
            }
            else
            {
                ViewBag.ClassItems = HRMT01.GetClassListData(Item.PHOTO_SCHOOL_NO, Item.PHOTO_CLASS_NO, ref db);
                ViewBag.UserNoItems = HRMT01.GetUserNoListDataMust(Item.PHOTO_SCHOOL_NO, Item.PHOTO_CLASS_NO, Item.PHOTO_USER_NO, ref db);
            }

            return PartialView(Item);
        }

        [CheckPermission(CheckACTION_ID = "Edit")] //檢查權限
        public ActionResult _Photo(ZZZI34EditPhotoViewModel ItemPhoto)
        {
            this.Shared();
            return PartialView(ItemPhoto);
        }

        [HttpPost]
        [CheckPermission(CheckACTION_ID = "Edit")] //檢查權限
        public ActionResult _GetUSER_NODDLHtml(string tagId, string tagName, string CLASS_NO, string USER_NO)
        {
            this.Shared();
            var UserNoListData = HRMT01.GetUserNoListDataMust(SCHOOL_NO, CLASS_NO, USER_NO, ref db);

            string _html = Util.DropDownListHelper.GetDropdownList(tagId, tagName, UserNoListData, new { @class = "form-control input-sm rounded" }, "", false, null, false);

            return Content(_html);
        }

        [CheckPermission(CheckACTION_ID = "Edit")] //檢查權限
        public ActionResult UpdateStatus(ZZZI34EditViewModel model)
        {
            this.Shared();
            if (model == null) model = new ZZZI34EditViewModel();
            if (model.Search == null) model.Search = new ZZZI34SearchViewModel();
            ViewBag.VisibleVerify = (PermissionService.GetPermission_Use_YN(Bre_NO, "Verify", user?.SCHOOL_NO, user?.USER_NO) == "Y" ? true : false);

            string Message = string.Empty;

            if (ModelState.IsValid == false)
            {
                Message = "警告!!輸入的內容有錯誤";
            }
            else
            {
                bool OK = false;
                List<Tuple<string, string, int>> valuesList = new List<Tuple<string, string, int>>();
                OK = Service.UpdateStatus(model, user, ref db, ref Message, ViewBag.VisibleVerify ,ref valuesList);


                if (OK)
                {

                    
                    TempData["StatusMessage"] = Message;

                    ZZZI34IndexViewModel Qmodel = new ZZZI34IndexViewModel()
                    {
                        Search = new ZZZI34SearchViewModel()
                    };
                    Qmodel.Search = model.Search;

                    if (model.SaveType != ZZZI34EditViewModel.SaveTypeVal.Verify)
                    {
                        Qmodel.WhereMyWork = true;

                    }
                    else {


                        Qmodel.Search.WhereART_GALLERY_NO = "";

                    }

                    return View("ArtGalleryList", Qmodel);
                }
            }

            if (model.SaveType == ZZZI34EditViewModel.SaveTypeVal.Verify)
            {
                this.Shared(Bre_Name + "-批閱線上藝廊內容");
            }
            else
            {
                this.Shared(Bre_Name + "-" + model.Main.ART_SUBJECT);
            }

            TempData["StatusMessage"] = Message;

            return View("Edit", model);
        }

        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public JsonResult ShareSave(ZZZI34ShareViewModel model, int LikeCount = 0)
        {
            this.Shared();
            if (model == null) model = new ZZZI34ShareViewModel();

            if (!string.IsNullOrWhiteSpace(Request.ServerVariables["HTTP_VIA"]?.ToString()))
            {
                model.IP_ADDRESS = Request.ServerVariables["HTTP_X_FORWARDED_FOR"]?.ToString();
            }
            else
            {
                model.IP_ADDRESS = Request.ServerVariables["REMOTE_ADDR"]?.ToString();
            }

            int SuccessType = 0;

            string Message = string.Empty;

            if (user == null && string.IsNullOrWhiteSpace(model.NICK_NAME))
            {
                SuccessType = 2;
            }
            else
            {
                List<Tuple<string, string, int>> valuesList = new List<Tuple<string, string, int>>();
                bool OK = Service.ShareSaveData(model, user, ref db, ref Message, ref LikeCount, ref valuesList);
                if (OK)
                {
                    SuccessType = 1;
                }
            }

            var data = "{ \"Success\" : \"" + SuccessType.ToString() + "\" , \"LikeCount\" : \"" + LikeCount.ToString() + "\" , \"Error\" : \"" + Message + "\" }";
            return Json(@data, JsonRequestBehavior.AllowGet);
        }

        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult _ArtGalleryMenu(string NowAction)
        {
            string ISTASKLIST = Request.QueryString["ISTASKLIST"];
            ViewBag.ISTASKLIST = ISTASKLIST;
            this.Shared();
            ViewBag.NowAction = NowAction;
            return PartialView();
        }

        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult _VideoView(string Url)
        {
            ViewBag.Url = Url;
            return PartialView();
        }

        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult _LikeListView(string PHOTO_NO)
        {
            this.Shared();
            ZZZI34LikeListViewModel model = new ZZZI34LikeListViewModel();
            model.PHOTO_NO = PHOTO_NO;
            model = Service.GetLikeListData(model, ref db);
            return PartialView(model);
        }

        [CheckPermission(CheckBRE_NO = "ZZZ034", CheckACTION_ID = "OrderList")] //檢查權限
        public ActionResult OrderList(ZZZI34OrderListViewModel model)
        {
            this.Shared(BreadcrumbService.GetBRE_NAME("ZZZ034"));
            return View(model);
        }

        [CheckPermission(CheckBRE_NO = "ZZZ034", CheckACTION_ID = "OrderList")] //檢查權限
        public ActionResult _PageOrderList(ZZZI34OrderListViewModel model)
        {
            this.Shared(BreadcrumbService.GetBRE_NAME("ZZZ034"));
            string Message = string.Empty;
            if (model == null) model = new ZZZI34OrderListViewModel();

            if (string.IsNullOrWhiteSpace(model.WhereSCHOOL_NO))
            {
                model.WhereSCHOOL_NO = SCHOOL_NO;
            }

            model = Service.GetOrderListData(model, ref db, ref Message);

            if (!string.IsNullOrWhiteSpace(Message))
            {
                TempData["StatusMessage"] = Message;
            }

            ViewBag.GradeItem = HRMT01.GetGradeItems(model.WhereGrade);

            ViewBag.ClassItems = HRMT01.GetClassListData(SCHOOL_NO, model.WhereGrade, model.WhereCLASS_NO, ref db)
               .Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.WhereGrade });

            return PartialView(model);
        }

        #region Shared

        private void SetTitle(string Panel_Title = "")
        {
            if (!string.IsNullOrWhiteSpace(Panel_Title))
            {
                ViewBag.Panel_Title = Panel_Title;
            }
            else
            {
                ViewBag.Panel_Title = Bre_Name + " - 列表 ";
            }
        }

        private void Shared(string Panel_Title = "")
        {
            ViewBag.BRE_NO = Bre_NO;

            if (!string.IsNullOrWhiteSpace(Panel_Title))
            {
                ViewBag.Panel_Title = Panel_Title;
            }
            else
            {
                ViewBag.Panel_Title = Bre_Name + " - 列表 ";
            }

            user = UserProfileHelper.Get();
            SCHOOL_NO = UserProfileHelper.GetSchoolNo();

            if (user != null)
            {
                if (user.SCHOOL_NO != null)
                {
                    SCHOOL_NO = user.SCHOOL_NO;
                    USER_NO = user.USER_NO;
                }
            }
            IsAdmin = HRMT24_ENUM.CheckQAdmin(user);
            VerifyUseYN = PermissionService.GetPermission_Use_YN(Bre_NO, "Verify", SCHOOL_NO, USER_NO);
            ViewBag.VerifyUseYN = VerifyUseYN;
            ViewBag.IsAdmin = IsAdmin;
        }

        #endregion Shared

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}