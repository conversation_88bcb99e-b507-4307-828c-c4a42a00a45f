﻿using com.ecool.service;
using ECOOL_APP;
using ECOOL_APP.EF;
using EcoolWeb.Models;
using System.Collections.Generic;
using System.Web.Mvc;
using EcoolWeb.Util;
using ECOOL_APP.com.ecool.service;
using DotNet.Highcharts;
using DotNet.Highcharts.Enums;
using DotNet.Highcharts.Helpers;
using DotNet.Highcharts.Options;
using static ECOOL_APP.EF.HRMT01;
using System;
using System.Net;
using ECOOL_APP.com.ecool.util;
using System.IO;
using iTextSharp.text;
using iTextSharp.text.pdf;
using iTextSharp.tool.xml;
using System.Web;
using EcoolWeb.CustomAttribute;
using System.Linq;

using Dapper;

namespace EcoolWeb.Controllers
{
    [SessionExpire]
    public class SECI06Controller : Controller
    {
        /// <summary>
        /// 此Controller 對應的程式代碼
        /// </summary>
        private static string Bre_NO = "SECI06";

        private string Bre_Name = BreadcrumbService.GetBRE_NAME(Bre_NO);

        /// <summary>
        /// 會員相關資訊
        /// </summary>
        private UserProfile user;

        private string DefaultSCHOOL_NO = string.Empty;
        private string USER_NO = string.Empty;

        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();

        private SECI06Service Service = new SECI06Service();

        // GET: SECI06
        public ActionResult Index(SECI06IndexViewModel model)
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "我的健康秘書";
            this.Shared(ViewBag.Panel_Title);

            List<string> MyPanyStudent = new List<string>();

            if (user == null)
            {
                return RedirectToAction("SessionTimeOutError", "Error");
            }

            if (string.IsNullOrWhiteSpace(model.WhereSCHOOL_NO))
            {
                model.WhereSCHOOL_NO = user.SCHOOL_NO;
            }

            if (string.IsNullOrWhiteSpace(model.WhereCLASS_NO))
            {
                model.WhereCLASS_NO = user.CLASS_NO;
            }

            if (user.USER_TYPE == UserType.Student)
            {
                model.WhereCLASS_NO = user.CLASS_NO;
                model.WhereUSER_NO = user.USER_NO;
            }
            else if (user.USER_TYPE == UserType.Parents)
            {
                var Hr06 = HRMT06.GetMyPanyStudent(user, db);

                if (Hr06 != null)
                {
                    if (Hr06.Count() > 0)
                    {
                        MyPanyStudent = Hr06.Select(a => a.STUDENT_USER_NO).ToList();
                    }
                }

                if (MyPanyStudent.Count >= 1 && string.IsNullOrWhiteSpace(model.WhereUSER_NO))
                {
                    model.WhereUSER_NO = MyPanyStudent.FirstOrDefault();
                }
            }

            List<SelectListItem> SchoolNoSelectItem = BDMT01Service.GetSCHOOL_NO(model.WhereSCHOOL_NO, null, user, true, null, null, false);

            if (!HRMT24_ENUM.CheckQQutSchool(user))
            {
                SchoolNoSelectItem = SchoolNoSelectItem.Where(a => a.Value == model.WhereSCHOOL_NO).ToList();
            }
            ViewBag.SchoolNoSelectItem = SchoolNoSelectItem;

            var ClassItems = HRMT01.GetClassListData(model.WhereSCHOOL_NO, null, model.WhereCLASS_NO, ref db);
            if (!HRMT24_ENUM.CheckQAdmin(user))
            {
                if (user.TEACH_CLASS_NO == null)
                {
                    if (HRMT24_ENUM.IsOtherTeacher(user) == false)
                    {
                        ClassItems = ClassItems.Where(a => a.Value == (user.TEACH_CLASS_NO ?? "")).ToList();
                    }
                }
                else
                {
                    ClassItems = ClassItems.Where(a => a.Value == (user.TEACH_CLASS_NO ?? "")).ToList();
                }
            }
            ViewBag.ClassItems = ClassItems;

            var USER_NOItems = HRMT01.GetUserNoListData(model.WhereSCHOOL_NO, model.WhereCLASS_NO, model.WhereUSER_NO, ref db);
            if (user.USER_TYPE == UserType.Parents)
            {
                USER_NOItems = HRMT01.GetUserNoListDataForP(model.WhereUSER_NO, user, db).ToListNoLock();
            }
            else
            {
                USER_NOItems = HRMT01.GetUserNoListData(model.WhereSCHOOL_NO, model.WhereCLASS_NO, model.WhereUSER_NO, ref db);
            }
            ViewBag.USER_NOItems = USER_NOItems;

            if (!string.IsNullOrWhiteSpace(model.WhereUSER_NO))
            {
                model = Service.GetHealthData(model, user, ref db);
                SECI03Controller sECI03Controller = new SECI03Controller();
                model.TALLchart = sECI03Controller.GetTALLchart(model.Hrmt08List, 600, 700, "TALLchart");
                model.WEIGHTchart = sECI03Controller.GetWEIGHTchart(model.Hrmt08List, 600, 700, "WEIGHTchart");
            }

            return View(model);
        }

        public ActionResult _SECI06UserSportsInfo()
        {
            return PartialView();
        }

        public ActionResult _SECI06UserSportsInfoforprint1(string wSCHOOL_NO, string wUSER_NO)
        {
            SECI01Service Service = new SECI01Service();
            HRMT01 thisUser = db.HRMT01.Where(a => a.SCHOOL_NO == wSCHOOL_NO && a.USER_NO == wUSER_NO && a.USER_STATUS == UserStaus.Enabled).FirstOrDefault();
            List<ADDT01> aDDT01sDetail = new List<ADDT01>();
            aDDT01sDetail = db.ADDT01.Where(x => x.SCHOOL_NO == wSCHOOL_NO && x.CLASS_NO == thisUser.CLASS_NO && x.USER_NO == wUSER_NO).ToList();
            string sSQL = string.Empty;
            HRMT01 thisTeacher = db.HRMT01.Where(a => a.SCHOOL_NO == wSCHOOL_NO && a.CLASS_NO == thisUser.CLASS_NO && a.USER_TYPE == UserType.Teacher).FirstOrDefault();
            if (aDDT01sDetail.Count() > 0)
            { 
                List<string> aDDT01sDetailtostring = new List<string>();
                aDDT01sDetailtostring = aDDT01sDetail.Select(x => x.VERIFIER).Distinct().ToList();
                sSQL = "SELECT VERIFIER ,count(VERIFIER)  as VERIFIERCount  FROM dbo.ADDT01 where SCHOOL_NO = @SCHOO_NO and USER_NO = @USER_NO and CLASS_NO = @CLASS_NO GROUP by VERIFIER";
                var temp = db.Database.Connection.Query<SportInfoViewModels>(sSQL, new {
                    SCHOO_NO = wSCHOOL_NO,
                    USER_NO= wUSER_NO,
                    CLASS_NO= thisUser.CLASS_NO
                });
             string TeacherV=   temp.OrderByDescending(x => x.VERIFIERCount).Select(x => x.VERIFIER).FirstOrDefault();
                thisTeacher = db.HRMT01.Where(a => a.SCHOOL_NO == wSCHOOL_NO && a.IDNO == TeacherV).FirstOrDefault();
            }
           
     
            SECI06IndexViewModel model = new SECI06IndexViewModel();
            //角色娃娃
            if (!string.IsNullOrWhiteSpace(thisUser.PHOTO))
            {
                model.PlayerUrl = Service.GetDirectorySysMyPhotoPath(thisUser.SCHOOL_NO, thisUser.USER_NO, thisUser.PHOTO);
            }
            else
            {
                model.PlayerUrl = GetPlayerUrl(ref db, thisUser.SCHOOL_NO, thisUser.USER_NO, thisUser.SEX, thisUser.USER_TYPE);
            }
            if (thisTeacher == null)
            {
                thisTeacher = db.HRMT01.Where(a => a.SCHOOL_NO == wSCHOOL_NO && a.CLASS_NO == thisUser.CLASS_NO && a.USER_TYPE == UserType.Teacher).FirstOrDefault();

            }
           
            model.TeacherSTR = thisTeacher.NAME;
            return PartialView();
        }
        public static string GetPlayerUrl(ref ECOOL_DEVEntities db, string SCHOOL_NO, string USER_NO, string SEX, string USER_TYPE)
        {
            if (USER_TYPE == UserType.Parents)
            {
                return "~/Content/img/web-parent.png";
            }
            //角色娃娃
            List<AWAT07> MyPlayerList =
                db.AWAT07.Where(a => a.SCHOOL_NO == SCHOOL_NO && a.USER_NO == USER_NO).ToList();
            if (MyPlayerList.Any())
            {
                AWAT07 MyPlayer = MyPlayerList.Where(a => a.DEFAULT_YN == true).FirstOrDefault();
                if (MyPlayer == null) MyPlayer = MyPlayerList.First();

                string ImageUrl = @"~/Content/Players/";
                return ImageUrl + db.AWAT06.Find(MyPlayer.PLAYER_NO).IMG_FILE;
            }
            else
            {
                if (string.IsNullOrWhiteSpace(SEX))
                {
                    HRMT01 tHRMT01 = db.HRMT01.Where(a => a.SCHOOL_NO == SCHOOL_NO && a.USER_NO == USER_NO).FirstOrDefault();

                    if (tHRMT01 != null)
                    {
                        SEX = tHRMT01.SEX;
                    }
                }

                if (SEX == "1") //男預設值
                {
                    return "~/Content/img/web-student-allpage-33plus.png";
                }
                else            //女預設值
                {
                    return "~/Content/img/web-student_allpage-17.png";
                }
            }
        }

        public ActionResult _SECI06UserSportsInfoforprint()

        {
            return PartialView();
        }

        public ActionResult BMI_MEMO()
        {
            return View();
        }

        [HttpPost]
        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult _GetUSER_NODDLHtml(string tagId, string tagName, string SCHOOL_NO, string CLASS_NO, string USER_NO)
        {
            ViewBag.BRE_NO = Bre_NO;

            user = UserProfileHelper.Get();

            if (string.IsNullOrWhiteSpace(SCHOOL_NO))
            {
                SCHOOL_NO = user.SCHOOL_NO;
            }

            var UserNoListData = HRMT01.GetUserNoListData(SCHOOL_NO, CLASS_NO, ref db);
            string _html = Util.DropDownListHelper.GetDropdownList(tagId, tagName, UserNoListData, new { @class = "form-control" }, USER_NO, true, null);

            return Content(_html);
        }

        #region Shared

        private void Shared(string Panel_Title = "")
        {
            ViewBag.BRE_NO = Bre_NO;

            string Temp_Panel_Title = Bre_Name;

            if (!string.IsNullOrWhiteSpace(Panel_Title))
            {
                Temp_Panel_Title = Temp_Panel_Title + " - " + Panel_Title;
            }
            ViewBag.Panel_Title = Temp_Panel_Title;

            user = UserProfileHelper.Get();
            DefaultSCHOOL_NO = UserProfileHelper.GetSchoolNo();

            if (user != null)
            {
                if (user.SCHOOL_NO != null)
                {
                    DefaultSCHOOL_NO = user.SCHOOL_NO;
                    USER_NO = user.USER_NO;
                }
            }
        }

        #endregion Shared

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}