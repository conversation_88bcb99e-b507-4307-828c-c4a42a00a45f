﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Reflection;
using System.Web;

namespace EcoolWeb.Util
{
    public class AttributeHelper
    {
        public static string GetDescriptionAttribute<T>(string fieldName)
        {
            string result;
            PropertyInfo pi = typeof(T).GetProperty(fieldName);
            if (pi != null)
            {
                try
                {
                    object[] descriptionAttrs = pi.GetCustomAttributes(typeof(DescriptionAttribute), false);
                    DescriptionAttribute description = (DescriptionAttribute)descriptionAttrs[0];
                    result = (description.Description);
                }
                catch
                {
                    result = null;
                }
            }
            else
            {
                result = null;
            }

            return result;
        }
    }
}