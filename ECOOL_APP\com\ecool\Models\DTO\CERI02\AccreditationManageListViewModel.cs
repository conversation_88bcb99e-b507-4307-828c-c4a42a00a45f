﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class AccreditationManageListViewModel
    {
        /// <summary>
        ///護照細項ID
        /// </summary>
        [DisplayName("護照細項ID")]
        public string ACCREDITATION_ID { get; set; }

        /// <summary>
        ///護照ID
        /// </summary>
        [DisplayName("護照ID")]
        public string ACCREDITATION_TYPE { get; set; }

        [DisplayName("隸屬護照")]
        public string TYPE_NAME { get; set; }

        /// <summary>
        ///護照細項名稱
        /// </summary>
        [DisplayName("護照細項名稱")]
        public string ACCREDITATION_NAME { get; set; }

        /// <summary>
        ///學校代碼
        /// </summary>
        [DisplayName("學校代碼")]
        public string SCHOOL_NO { get; set; }

        /// <summary>
        ///修改人
        /// </summary>
        [DisplayName("修改人")]
        public string CHG_PERSON { get; set; }

        [DisplayName("修改人")]
        public string CHG_PERSON_NAME { get; set; }

        /// <summary>
        ///修改時間
        /// </summary>
        [DisplayName("修改時間")]
        [DisplayFormat(DataFormatString = "{0:yyyy/MM/dd}", ApplyFormatInEditMode = true)]
        public DateTime? CHG_DATE { get; set; }

        /// <summary>
        /// 認證老師
        /// </summary>
        [DisplayName("認證老師")]
        public List<string> VERIFIERs { get; set; }
    }
}