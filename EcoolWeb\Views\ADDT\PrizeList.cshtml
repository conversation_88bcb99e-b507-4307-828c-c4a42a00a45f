﻿@model IEnumerable<ECOOL_APP.com.ecool.Models.DTO.PrizeListViewModel>
@using ECOOL_APP
@using EcoolWeb.Models

@{
    ViewBag.Title = "閱讀認證-獎狀清單";
    string ImagePath = Url.Content("~/Content/img/coolreader-02.png");
    string SchoolName = EcoolWeb.Models.UserProfileHelper.GetSchoolName();
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();

    UserProfile user = UserProfileHelper.Get();

    if (string.IsNullOrWhiteSpace(UUID) == false)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }

    var Search = TempData["Search"] as EcoolWeb.ViewModels.ADDTListViewModel;
}

@Html.Partial("_Title_Secondary")


@foreach (var item in Model)
{

    <div style="position:relative">
        <div style="width:480px;position:absolute; font-size:16pt;font-weight:bold ;color:#333333;top:150px;left:180px">閱讀認證榮譽獎(第@(item.LEVEL_ID)級)</div>
        <div style="width:480px;position:absolute;top:200px;left:50px;font-size: 11pt; color:#333333;white-space: normal;">

            恭喜&nbsp;@SchoolName &nbsp;
            <span style="color:blue">
                @if (user != null)
                {
                    if (user.USER_TYPE == UserType.Admin || user.USER_TYPE == UserType.Teacher || user.USER_NO == item.USER_NO)
                    {
                        @item.NAME
                    }
                    else
                    {
                        @item.SNAME
                    }
                }
                else
                {
                    @item.SNAME
                }
            </span> &nbsp;小朋友閱讀好書並撰寫
            <br />
            閱讀心得報告 @item.BOOK_QTY  &nbsp;篇， 於
            @if (item.UP_DATE != null)
            {
                <b>
                    @item.UP_DATE.Value.Date.ToShortDateString()
                </b>
            }
            晉升到
            <span style="color:blue">  @item.LEVEL_DESC 獎！</span>
            <br />

            <br />
            閱讀各種不同領域的書籍，不但可以充實知識，還能將知識融會貫通，希望愛看書的你繼續保持閱讀的好習慣，為自己加油喔！
        </div>
        <img src="@Url.Content(ImagePath)">
    </div>



}
<a href='@Url.Action("ADDTList", "ADDT"
            ,new {
    whereKeyword = Search.whereKeyword,
    OrdercColumn = Search.OrdercColumn,
    whereCLASS_NO= Search.whereCLASS_NO,
    whereGrade= Search.whereGrade,
    Page = Search.Page
})' role="button" class="btn btn-default">
    返回
</a>

