/*
 *  /MathJax/jax/output/HTML-CSS/fonts/TeX/SansSerif/Bold/Other.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["MathJax_SansSerif-bold"],{160:[0,0,250,0,0],305:[458,0,256,54,201],567:[458,205,286,-71,232],915:[691,0,581,92,534],916:[694,0,917,60,856],920:[716,22,856,62,793],923:[694,0,672,41,630],926:[688,0,733,46,686],928:[691,0,794,92,702],931:[694,0,794,61,732],933:[715,0,856,62,793],934:[694,0,794,62,732],936:[694,0,856,61,794],937:[716,0,794,49,744],8211:[327,-240,550,0,549],8212:[327,-240,1100,0,1099],8216:[694,-443,306,81,226],8217:[694,-442,306,80,226],8220:[694,-443,558,138,520],8221:[694,-442,558,37,420]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/SansSerif/Bold/Other.js");
