﻿@model ZZZI09ShowADDT14ViewViewModel

@if (TempData["StatusMessage"] != null)
{
    @Html.Partial("_Notice")
}

else
{
    if (Model!=null&&Model.ADDT14List != null && Model.ADDT14List.Count()>0)
    {
    <section class="row px-3 honorRollInSchool">
        <div class="col-lg-12 bg-custom-yellow bgPosition">
            <h2 class="heading-h2">榮譽表現（校內）</h2>


            @foreach (var item in Model.ADDT14List)
            {
            <div class="row honorArea">
                <div class="col-lg-12">
                    <div class="bg-custom-white">
                        <div class="row">
                            <div class="col-8">
                                <table class="table">
                                    <tr>
                                        <th scope="row" width=120>日期</th>
                                        <td>@Html.DisplayFor(model => item.CREATEDATE, "ShortDateTime")</td>
                                    </tr>
                                    <tr>
                                        <th scope="row">班級</th>
                                        <td>@Html.DisplayFor(model => item.CLASS_NO)</td>
                                    </tr>
                                    <tr>
                                        <th scope="row">姓名</th>
                                        <td>@Html.DisplayFor(model => item.SNAME)</td>
                                    </tr>
                                    <tr>
                                        <th scope="row">獎懲類別</th>
                                        <td>@Html.DisplayFor(model => item.IAWARD_KIND)</td>
                                    </tr>
                                    <tr>
                                        <th scope="row">具體事蹟</th>
                                        <td>@Html.DisplayFor(model => item.IAWARD_ITEM)</td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-4 d-flex justify-content-center align-items-center">
                                @{

                                    string stylestr = "";
                                }
                                @if (!string.IsNullOrWhiteSpace(ViewBag.ImageUrl) && !string.IsNullOrWhiteSpace(item.IMG_FILE))
                                {
                                    string ImgPath = ViewBag.ImageUrl + Path.Combine(item.IAWARD_ID.ToString(), item.IMG_FILE);

                                    string SysTempPath = HttpContext.Current.Server.MapPath(ImgPath);
                                    System.Drawing.Image image = System.Drawing.Image.FromFile(SysTempPath);
                                    double FixWidth = 618;
                                    double FixHeight = 450;
                                    double rate = 1;
                                    if (image.Width > FixWidth || image.Height > FixHeight)
                                    {
                                        if (image.Width > FixWidth) { rate = FixWidth / image.Width; }
                                        if (image.Height * rate > FixHeight)
                                        {
                                            rate = FixHeight / image.Height;

                                        }

                                        int w = Convert.ToInt32(image.Width * rate);
                                        int h = Convert.ToInt32(image.Height * rate);

                                        stylestr = "width:" + w + "px;height:" + h + "px;padding-right:10px;";
                                    }
                                    else
                                    {
                                        stylestr = "width:" + image.Width + "px;height:" + image.Height + "px;padding-right:10px;";
                                    }
                                    <div class="honorPaper">
                                        <img src="@Url.Content(ImgPath)" href="@Url.Content(ImgPath)" />
                                    </div>
                                }
                            </div>

                        </div>
                    </div>
                </div>
            </div>
            }
        </div>
    </section>

                                        }
            }

@*@else
    {
    <div class="Div-EZ-Pink" style="margin-bottom:15px">
        <div class="Details text-danger">
            <h4><label style="font-size:0.5cm;">&nbsp;校內榮譽</label></h4>
        </div>
    </div>

    foreach (var item in Model.ADDT14List)
    {
    <div class="Div-EZ-Pink" style="margin-bottom:15px">
        <div class="Details">
            <div class="row">

                <div class="col-md-5 col-sm-6 dl-horizontal-EZ">
                    <samp class="dt" style="font-size:0.6cm;">
                        日期
                    </samp>
                    <samp class="dd" style="font-size:0.5cm;">
                        @Html.DisplayFor(model => item.CREATEDATE, "ShortDateTime")
                    </samp><br />  <samp class="dt" style="font-size:0.6cm;">
                        班級
                    </samp>
                    <samp class="dd" style="font-size:0.5cm;">
                        @Html.DisplayFor(model => item.CLASS_NO)
                    </samp><br />
                    <samp class="dt" style="font-size:0.6cm;">
                        姓名
                    </samp>
                    <samp class="dd" style="font-size:0.5cm;">
                        @Html.DisplayFor(model => item.SNAME)
                    </samp> <br />
                    <samp class="dt" style="font-size:0.6cm;">
                        獎懲類別
                    </samp>

                    <samp class="dd" style="font-size:0.5cm;">
                        @Html.DisplayFor(model => item.IAWARD_KIND)
                    </samp><br />
                    <samp class="dt" style="font-size:0.6cm;">
                        具體事蹟
                    </samp>
                    <samp class="dd" style="font-size:0.5cm;">
                        @Html.DisplayFor(model => item.IAWARD_ITEM)
                    </samp>
                </div>
                <div class="col-md-5  col-sm-6 offset-md-4">
                    @{

                        string stylestr = "";
                    }
                    @if (!string.IsNullOrWhiteSpace(ViewBag.ImageUrl) && !string.IsNullOrWhiteSpace(item.IMG_FILE))
                    {
                        string ImgPath = ViewBag.ImageUrl + Path.Combine(item.IAWARD_ID.ToString(), item.IMG_FILE);

                        string SysTempPath = HttpContext.Current.Server.MapPath(ImgPath);
                        System.Drawing.Image image = System.Drawing.Image.FromFile(SysTempPath);
                        double FixWidth = 618;
                        double FixHeight = 450;
                        double rate = 1;
                        if (image.Width > FixWidth || image.Height > FixHeight)
                        {
                            if (image.Width > FixWidth) { rate = FixWidth / image.Width; }
                            if (image.Height * rate > FixHeight)
                            {
                                rate = FixHeight / image.Height;

                            }

                            int w = Convert.ToInt32(image.Width * rate);
                            int h = Convert.ToInt32(image.Height * rate);

                            stylestr = "width:" + w + "px;height:" + h + "px;padding-right:10px;";
                        }
                        else
                        {
                            stylestr = "width:" + image.Width + "px;height:" + image.Height + "px;padding-right:10px;";
                        }
                        <img src="@Url.Content(ImgPath)" href="@Url.Content(ImgPath)" class="img-responsive " style="@stylestr" />
                    }
                </div>
            </div>

            <div style="height:15px"></div>
        </div>*@
@*<div class="col-md-12">
        <div class="p-context">
            @if (!string.IsNullOrWhiteSpace(ViewBag.ImageUrl) && !string.IsNullOrWhiteSpace(item.IMG_FILE))
            {
                string ImgPath = ViewBag.ImageUrl + Path.Combine(item.IAWARD_ID.ToString(), item.IMG_FILE);

                <img src="@Url.Content(ImgPath)" href="@Url.Content(ImgPath)" class="img-responsive " />
            }
        </div>
    </div>*@
@*<div style="height:1px;background-color:palevioletred" ;></div>
    </div>
            }

            }*@
