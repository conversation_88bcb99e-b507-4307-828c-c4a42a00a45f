﻿@model EcoolWeb.Models.ADDT06ViewModel

@{
    /**/

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode || (Model?.WhereIsColorboxForUser ?? false))
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
    else
    {
        Layout = null;
    }

}

<div id="check1"></div>
@Html.AntiForgeryToken()
@*<div id="OnePageContent" style="min-height:400px">
        @Html.Action("PushListDetail", (string)ViewBag.BRE_NO, new { APPLY_NO = Model.OrdercColumn, ListType = Model.whereKeyword })' class="btn btn-xs btn-default")
    </div>*@
<div id="OnePageContent" style="min-height:400px">
    @Html.Action("_OnePageContent", (string)ViewBag.BRE_NO)
</div>
