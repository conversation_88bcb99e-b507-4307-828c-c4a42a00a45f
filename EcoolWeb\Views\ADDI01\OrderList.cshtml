﻿@model EcoolWeb.Models.ADDI01OrderListViewModel
@{
    ViewBag.Title = "線上投稿排行榜";

    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    string HidStyle = "";
    int DataCount = 0;
    //序號
    int RowNumber = 0;

    if (Model!=null &&(Model.isCarousel ||( Model.IsPrint!=null&& Model.IsPrint==true)))
    {
        Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
        HidStyle = "display:none";
    }
    else
    {
        if (AppMode)
        {
            Layout = "~/Views/Shared/_LayoutWebView.cshtml";
        }
    }
}
<script src="~/Scripts/Blob.js" nonce="cmlvaw"></script>
<script src="~/Scripts/FileSaver.js" nonce="cmlvaw"></script>
<script src="~/Scripts/tableexport.js" nonce="cmlvaw"></script>
<link href="~/Scripts/timepicker/jquery-ui-timepicker-addon.css" rel="stylesheet" />
<script src="~/Scripts/timepicker/jquery-ui-timepicker-addon.js" nonce="cmlvaw"></script>
<script src="~/Scripts/timepicker/jquery-ui-timepicker-zh-TW.js" nonce="cmlvaw"></script>
<script src="~/Scripts/timepicker/jquery.ui.datepicker-zh-TW.js" nonce="cmlvaw"></script>
@*<script src="~/Scripts/moment.js" nonce="cmlvaw"></script>*@
<script src="~/Scripts/moment.min.js" nonce="cmlvaw"></script>
@if (Model != null && Model.isCarousel)
{
    <style type="text/css">
        .bigger {
            font-size: 30px;
        }

        .Carousel_hide {
            display: none;
        }

        .table-ecool thead > tr > th, .table-ecool thead > tr > td {
            font-size: 30px;
        }
    </style>
}

<style type="text/css" media="print">
    /* 列印時隱藏 */
    .cScreen {
        display: none;
    }
</style>

@if (AppMode == false &&(Model!=null&& Model.isCarousel == false))
{
    @Html.Partial("_Title_Secondary")
}
@Html.Partial("_Notice")

@using (Html.BeginForm("OrderList", "ADDI01", FormMethod.Post, new { id = "OrderList", NAME = "OrderList" }))
{
    @Html.HiddenFor(m => m.whereUserNo)
    @Html.HiddenFor(m => m.OrdercColumn)
    @Html.HiddenFor(m => m.Page)
    @Html.HiddenFor(m => m.IsPrint)
    @Html.HiddenFor(m => m.IsToExcel)
    @Html.HiddenFor(m => m.WhereIsMonthTop)
    @Html.AntiForgeryToken()
    <div class="form-inline" style="@HidStyle">
        <div class="form-inline" role="form">

            <div class="form-group">
                <label class="control-label">學號/姓名</label>
            </div>
            <div class="form-group">
                @Html.EditorFor(m => m.whereKeyword, new { htmlAttributes = new { @class = "form-control input-sm" } })
            </div>
            <div class="form-group">
                <label class="control-label">年級</label>
            </div>
            <div class="form-group">
                @Html.DropDownListFor(m => m.whereGrade, (IEnumerable<SelectListItem>)ViewBag.GradeItem, new { @class = "form-control input-sm", onchange = "FunPageProc(1)" })
            </div>
            <div class="form-group">
                <label class="control-label">班級</label>
            </div>
            <div class="form-group">
                @Html.DropDownListFor(m => m.whereCLASS_NO, (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control input-sm", onchange = "FunPageProc(1)" })
            </div>

            <br />
            <div id="Sdate">
                <div class="form-group">
                    <label class="control-label">投稿日期(起)</label>
                </div>

                <div class="form-group">
                    @Html.EditorFor(m => m.whereSTART_CRE_DATE, new { htmlAttributes = new { @class = "form-control input-sm", autocomplete = "off" } })
                </div>
                <div class="form-group">
                    <label class="control-label">投稿日期(迄)</label>
                </div>
                <div class="form-group">
                    @Html.EditorFor(m => m.whereEND_CRE_DATE, new { htmlAttributes = new { @class = "form-control input-sm", autocomplete = "off" } })
                </div>
            </div>
            <input type="button" class="btn-yellow btn btn-sm " value="搜尋" onclick="FunPageProc(1)" />
            <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />

            @if (user != null)
            {
                if (user.USER_TYPE != UserType.Student && user.USER_TYPE != UserType.Parents)
                {

                    if (!(Model.IsPrint!=null && Model.IsPrint==false))
                    {

                        <button id="ButtonExcel" class="btn-yellow btn btn-sm " onclick="exportExcel()" style="float:right">匯出excel</button>

                        <button type="button" class="btn-yellow btn btn-sm cScreen" onclick="PrintBooK()" style="float:right;margin-right:5px">我要列印</button>
                    }
                    else
                    {

                        <button type="button" class="btn-yellow btn btn-sm cScreen" onclick="PrintBooK()" style="float:right;margin-right:5px">我要列印</button>
                    }

                }
            }
        </div>
    </div>

    <div class="form-inline cScreen" style="text-align:right">
        <br />
        <button class="btn btn-xs btn-pink @(Model.WhereIsMonthTop == false ? "active":"")" type="button" onclick="todoClear();doMonthTop('false');">全部</button>
        <button class="btn btn-xs btn-pink @(Model.WhereIsMonthTop == true ? "active":"")" type="button" onclick="todoClear();doMonthTop('true');">月排行榜</button>
    </div>

    <img src="~/Content/img/web-Bar-17.png" style="width:100%;@HidStyle" class="img-responsive App_hide " alt="Responsive image" />
    <div class="@((Model.IsPrint!=null && Model.IsPrint==true) ? "":"table-responsive")">
        <div class="text-center" id="tbData">
            <table class="@((Model.IsPrint!=null && Model.IsPrint==true) ? "table table-bordered":"table-ecool table-92Per table-hover table-ecool-List")">
                <thead>
                    <tr>
                        <th style="text-align: center">
                            序號
                        </th>
                        <th style="text-align: center">
                            班級
                        </th>
                        <th class="Carousel_hide" style="text-align: center">
                            座號
                        </th>
                        <th style="text-align: center">
                            學號
                        </th>
                        <th style="text-align: center">
                            姓名
                        </th>
                        <th style="text-align: center;cursor:pointer;" onclick="doSort('WRITING_QTY');">
                            投稿篇數
                            <img id="WRITING_QTY" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </th>
                        <th class="Carousel_hide" style="text-align: center;cursor:pointer;" onclick="doSort('SHARE_QTY');">
                            推薦數
                            <img id="SHARE_QTY" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </th>
                        <th class="Carousel_hide" style="text-align: center;cursor:pointer;" onclick="doSort('CASH_QTY');">
                            投稿所得點數
                            <img id="CASH_QTY" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </th>
                        <th class="Carousel_hide" style="text-align: center;cursor:pointer;" onclick="doSort('READ_COUNT');">
                            點閱數
                            <img id="READ_COUNT" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </th>
                        <th class="Carousel_hide" style="text-align: center;cursor:pointer;" onclick="doSort('COMMENT_COUNT');">
                            鼓勵與建議數
                            <img id="COMMENT_COUNT" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var item in Model.ADDV01List)
                    {
                        DataCount++;
                        RowNumber = Model.ADDV01List.PageSize * (Model.ADDV01List.PageNumber - 1) + (DataCount);
                        if (Model.isCarousel && DataCount > 5) { break; }
                        <tr>
                            <td class="bigger">

                                @RowNumber
                            </td>
                            <td class="bigger">
                                @Html.DisplayFor(modelItem => item.CLASS_NO)
                            </td>
                            <td class="Carousel_hide">
                                @Html.DisplayFor(modelItem => item.SEAT_NO)
                            </td>
                            <td class="Carousel_hide">
                                @Html.DisplayFor(modelItem => item.USER_NO)
                            </td>
                            <td class="bigger" style="text-align: center;">
                                @if ((Model.IsPrint != null && Model.IsPrint == true))
                                {
                                    @Html.DisplayFor(modelItem => item.SNAME)
                                }
                                else
                                {
                                    if (user?.USER_TYPE == UserType.Admin || user?.USER_TYPE == UserType.Teacher)
                                    {
                                        @Html.ActionLink(item.SNAME, "Index", new { whereUserNo = item.USER_NO })

                                    }
                                    else
                                    {
                                        @Html.DisplayFor(modelItem => item.SNAME)
                                    }
                                }
                            </td>
                            <td class="bigger">
                                @Html.DisplayFor(modelItem => item.WRITING_QTY)
                            </td>
                            <td class="Carousel_hide">
                                @Html.DisplayFor(modelItem => item.SHARE_QTY)
                            </td>
                            <td class="Carousel_hide">
                                @Html.DisplayFor(modelItem => item.CASH_QTY)
                            </td>
                            <td class="Carousel_hide">
                                @Html.DisplayFor(modelItem => item.READ_COUNT)
                            </td>
                            <td class="Carousel_hide">
                                @(item.COMMENT_COUNT ?? 0)
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>
    if (Model?.isCarousel == false)
    {
        <div>
            @Html.Pager(Model.ADDV01List.PageSize, Model.ADDV01List.PageNumber, Model.ADDV01List.TotalItemCount).Options(o => o
              .DisplayTemplate("BootstrapPagination")
             .MaxNrOfPages(5)
             .SetPreviousPageText("上頁")
             .SetNextPageText("下頁")
         )
        </div>
    }
    <div style="text-align:center;" class="cScreen">

        @if (Request["OrderList"] != null)
        {
            <a href='@Url.Action("Index", "ADDI01")' role="button" class="btn btn-default">
                返回
            </a>
            <br />
        }
    </div>

}
@section Scripts {
    <script nonce="cmlvaw">
        // 設置全局 URL 配置
        window.ADDI01_URLS = {
            exportExcel: "@Url.Action("ExportExcel", (string)ViewBag.BRE_NO)",
            orderList: "@Html.Raw(@Url.Action("OrderList", "ADDI01"))"
        };

        // 設置全局欄位ID配置
        window.ADDI01_FIELD_IDS = {
            whereSTART_CRE_DATE: "@Html.IdFor(m => m.whereSTART_CRE_DATE)",
            whereEND_CRE_DATE: "@Html.IdFor(m => m.whereEND_CRE_DATE)",
            IsPrint: "@Html.IdFor(m => m.IsPrint)",
            IsToExcel: "@Html.IdFor(m => m.IsToExcel)",
            WhereIsMonthTop: "@Html.IdFor(m => m.WhereIsMonthTop)"
        };
    </script>
    <script src="~/Scripts/ADDI01/orderlist.js" nonce="cmlvaw"></script>
}