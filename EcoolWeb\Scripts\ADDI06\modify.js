﻿// JavaScript for ADDI06 MODIFY page - 修改獎勵記錄
$(document).ready(function () {
    const modifyModule = {
        init: function () {
            this.setupGlobalFunctions();
            this.bindEvents();
            this.initializeColorbox();
        },

        bindEvents: function () {
            // 綁定事蹟類型選擇事件
            $('#IAWARD_KIND').on('change', this.handleAwardKindChange.bind(this));

            // 綁定班級選擇事件（如果有的話）
            $('#Class_No').on('change', this.handleClassChange.bind(this));

            // 綁定表單提交事件
            $('form').on('submit', this.handleFormSubmit.bind(this));

            // 綁定修改按鈕事件
            $('input[onclick*="Modify"], button[onclick*="Modify"]').on('click', this.handleModify.bind(this));
        },

        setupGlobalFunctions: function () {
            // 設置全局函數以保持向後兼容
            window.IAWARD_KIND_onchange = this.awardKind_onChange.bind(this);
            window.ChangeClass_No = this.changeClass_No.bind(this);
            window.Modify = this.validateAndModify.bind(this);
        },

        awardKind_onChange: function () {
            // 使用共用的事蹟類型變更函數
            return ADDI06Common.awardKindChange('#IAWARD_KIND', '#CASH', '#lbIAWARD_KIND');
        },

        changeClass_No: function () {
            try {
                const selectedClass_No = $.trim($('#Class_No option:selected').val());

                // 使用共用的學生載入函數
                ADDI06Common.loadStudentsByClass(selectedClass_No, '#USER_NO', window.ADDI06_MODIFY_URLS);

                console.log('MODIFY 班級變更:', selectedClass_No);
            } catch (error) {
                console.error('MODIFY 班級變更時發生錯誤:', error);
                ADDI06Common.showMessage('班級變更時發生錯誤，請稍後再試', 'error');
            }
        },

        validateAndModify: function () {
            try {
                let msg = '';
                let isValid = false;

                // 檢查學生選擇
                if ($("#USER_NO").val() === '' || $("#USER_NO").val() === null) {
                    msg += '請選擇學生\n';
                }

                // 檢查獎勵點數
                const cashValue = $("#CASH").val();
                const selectedAwardKind = $("#IAWARD_KIND option:selected").text();

                // 使用共用的點數驗證函數
                const cashErrors = ADDI06Common.validateCash(cashValue, selectedAwardKind);
                if (cashErrors.length > 0) {
                    msg += cashErrors.join('\n') + '\n';
                }

                // 檢查成績選擇（如果有的話）
                if ($("#OAWARD_SCORE").length > 0 && ($("#OAWARD_SCORE").val() === '' || $("#OAWARD_SCORE").val() === null)) {
                    msg += '請選擇成績\n';
                }

                // 檢查事蹟類型
                if (selectedAwardKind === "請選擇事蹟") {
                    msg += '請選擇事蹟\n';
                }

                if (msg !== '') {
                    ADDI06Common.showMessage(msg, 'error');
                    isValid = false;
                } else {
                    isValid = true;
                }

                console.log('MODIFY 表單驗證結果:', isValid ? '通過' : '失敗');
                return isValid;
            } catch (error) {
                console.error('MODIFY 表單驗證時發生錯誤:', error);
                ADDI06Common.showMessage('表單驗證時發生錯誤，請稍後再試', 'error');
                return false;
            }
        },

        initializeColorbox: function () {
            try {
                if (typeof $.colorbox === "function") {
                    $(".colorbox").colorbox({
                        opacity: 0.82,
                        width: "70%",
                        innerHeight: "500px",
                        scrolling: true
                    });
                    console.log('MODIFY Colorbox 已初始化');
                } else {
                    console.warn('MODIFY Colorbox 插件未載入');
                }
            } catch (error) {
                console.error('MODIFY 初始化 Colorbox 時發生錯誤:', error);
            }
        },

        // 事件處理器
        handleAwardKindChange: function (event) {
            this.awardKind_onChange();
        },

        handleClassChange: function (event) {
            this.changeClass_No();
        },

        handleFormSubmit: function (event) {
            try {
                console.log('MODIFY 表單提交');

                // 如果驗證失敗，阻止表單提交
                if (!this.validateAndModify()) {
                    event.preventDefault();
                    return false;
                }

                return true;
            } catch (error) {
                console.error('MODIFY 表單提交時發生錯誤:', error);
                event.preventDefault();
                ADDI06Common.showMessage('表單提交時發生錯誤，請稍後再試', 'error');
                return false;
            }
        },

        handleModify: function (event) {
            event.preventDefault();

            // 執行驗證
            if (this.validateAndModify()) {
                // 如果驗證通過，提交表單
                const form = ADDI06Common.getForm();
                if (form) {
                    form.submit();
                }
            }
        }
    };

    // 初始化模組
    modifyModule.init();

    // 設置全局錯誤處理
    window.addEventListener('error', function (e) {
        console.error('MODIFY 頁面錯誤:', e);
    });
});
