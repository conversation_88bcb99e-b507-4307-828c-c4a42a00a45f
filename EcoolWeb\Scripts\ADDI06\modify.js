﻿$(document).ready(function () {
    const modifyModule = {
        init: function () { this.initializeColorbox(); },
        bindEvents: function () {
            $('select[onchange*="IAWARD_KIND_onchange"]').on('change', this.IAWARD_KIND_onchange.bind(this));


        },
        setupGlobalFunction: function () {

        },
        IAWARD_KIND_onchange: function () {

            return ADDI06Common.IAWARD_KIND_onchange();
        },
        initializeColorbox: function () {
            if (typeof $.colorbox === "function") { 
            $(".colorbox").colorbox({
                opacity: 0.82,
                width: "70%",
                innerHeight: "500px",
                scrolling: true
            });
            }
        }
    }
})
