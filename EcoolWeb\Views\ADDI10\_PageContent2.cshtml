﻿@model ADDI10EditViewModel
    @{ string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();

    }
    <script type="text/javascript">
    </script>
    @if (Model.Search.LoginYN == "N")
    {
        <div class="table-responsive">

            <text>
                請先登入E酷幣
            </text>
        </div>
    }
    else
    {
        Layout = null;
        @Html.AntiForgeryToken()
        <div id="PageContent">
            @Html.Action("Edit", (string)ViewBag.BRE_NO, new { model = Model, HttpMethod = "POST" })
        </div>
    }