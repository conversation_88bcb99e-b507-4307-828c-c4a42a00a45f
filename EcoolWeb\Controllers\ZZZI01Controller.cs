﻿using ECOOL_APP;
using ECOOL_APP.EF;
using EcoolWeb.Models;
using EcoolWeb.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using MvcPaging;
using EcoolWeb.CustomAttribute;
using System.Data.Entity.Validation;
using System.Collections;
using System.Data.Entity;
using System.Net;
using com.ecool.service;
using System.IO;
using System.Text.RegularExpressions;

using com.ecool.sqlConnection;
using System.Data.SqlClient;
using ECOOL_APP.com.ecool.util;

namespace EcoolWeb.Controllers
{
    [SessionExpire]
    public class ZZZI01Controller : Controller
    {
        /// <summary>
        /// 此Controller 對應的程式代碼
        /// </summary>
        private string Bre_NO = "ZZZI01";

        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();
        private ECOOL_APP.UserProfile user = UserProfileHelper.Get();
        private string SchoolNO = UserProfileHelper.GetSchoolNo();

        public ActionResult TOMERGE()
        {
            string SchoolNo = UserProfileHelper.GetSchoolNo();

            string sp_name = "MERGE_HRMT_1";
            sqlConnection getConn = new sqlConnection();
            SqlConnection conn = getConn.getConnection4Query();
            SqlCommand cmd = new SqlCommand("dbo." + sp_name, conn);
            cmd.CommandType = System.Data.CommandType.StoredProcedure;
            cmd.Parameters.Add("SCHOOL_NO", System.Data.SqlDbType.VarChar, 50).Value = SchoolNo;
            try
            {
                cmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                throw ex.GetBaseException();
            }
            finally
            {
                getConn.closeConnection4Query(conn);
            }

            return RedirectToAction("QUERY");
        }

        public ActionResult QUERY(BDMT01IndexViewModel model)
        {
            if (user == null)
            {
                return RedirectToAction("SessionTimeOutError", "Error");
            }

            if (model == null) model = new BDMT01IndexViewModel();
            ViewBag.Show = HRMT24_ENUM.CheckQQutSchool(user);

            IQueryable<BDMT01> BDMT01List = db.BDMT01.AsQueryable();

            //如果為各校管理員只能修改自己學校的資訊
            if (user.ROLE_TYPE == 3)
            {
                ViewBag.ROLE_TYPE = 3;
                BDMT01List = BDMT01List.Where(a => a.SCHOOL_NO == SchoolNO);
            }

            if (string.IsNullOrWhiteSpace(model.whereKeyword) == false)
            {
                BDMT01List = BDMT01List.Where(a => a.SCHOOL_NO.Contains(model.whereKeyword.Trim()) || a.SCHOOL_NAME.Contains(model.whereKeyword.Trim()) || a.SHORT_NAME.Contains(model.whereKeyword.Trim()));
            }

            switch (model.OrdercColumn)
            {
                case "CRE_DATE":
                    BDMT01List = BDMT01List.OrderByDescending(a => a.CRE_DATE);
                    break;

                case "CITY":
                    BDMT01List = BDMT01List.OrderByDescending(a => a.CITY);
                    break;

                default:
                    BDMT01List = BDMT01List.OrderByDescending(a => a.CRE_DATE);
                    break;
            }

            model.BDMT01List = BDMT01List.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, 20);
            string UseYN = "N";
            //判斷是否有權限
            UseYN = (user != null) ? PermissionService.GetPermission_Use_YN(Bre_NO, "ADD", SchoolNO, user.USER_NO) : "N";
            if (UseYN == "N")
            {
                ViewBag.VisableInsert = false;
            }
            else
            {
                ViewBag.VisableInsert = true;
            }
            //判斷是否有權限
            UseYN = (user != null) ? PermissionService.GetPermission_Use_YN(Bre_NO, "Delete", SchoolNO, user.USER_NO) : "N";
            if (UseYN == "N")
            {
                ViewBag.VisableDelete = false;
            }
            else
            {
                ViewBag.VisableDelete = true;
            }
            UseYN = (user != null) ? PermissionService.GetPermission_Use_YN(Bre_NO, "MODIFY", SchoolNO, user.USER_NO) : "N";
            if (UseYN == "N")
            {
                ViewBag.VisableModify = false;
            }
            else
            {
                ViewBag.VisableModify = true;
            }
            return View(model);
        }

        [CheckPermission] //檢查權限
        public ActionResult ADD()
        {
            BDMT01 bdmt01 = new BDMT01();
            ViewBag.SchoolTypeItems = BDMT01.SchoolType.GetItems();
            return View(bdmt01);
        }

        [HttpPost]
        [CheckPermission] //檢查權限
        [ValidateAntiForgeryToken]
        public ActionResult ADD([Bind(Include = "SCHOOL_NO,SCHOOL_NAME,SHORT_NAME,CITY,ADDRESS,TEL,Manager_No,Manager_Name,Manager_Phone,E_MAIL,SCHOOL_TYPE")] BDMT01 bDMT01)
        {
            ViewBag.SchoolTypeItems = BDMT01.SchoolType.GetItems(bDMT01.SCHOOL_TYPE);

            if (ModelState.IsValid)
            {
                bDMT01.Manager_No = "Coolman";
                bDMT01.CRE_PERSON = user.USER_NO;
                bDMT01.SYS_ID = "001";
                bDMT01.CRE_DATE = DateTime.Now;
                bDMT01.CRE_PERSON = user.USER_NO;
                db.BDMT01.Add(bDMT01);

                //新增系統管理員coolman
                HRMT01 H01 = new HRMT01();
                H01.SCHOOL_NO = bDMT01.SCHOOL_NO;
                H01.USER_NO = "coolman";
                H01.NAME = "各校系管";
                H01.SNAME = "系管";
                H01.SEX = "1";
                H01.GRADE = 0;
                H01.USER_STATUS = UserStaus.Enabled;
                H01.USER_TYPE = UserType.Admin;
                db.HRMT01.Add(H01);

                //新增系統管理員預設密碼
                ZZT08 Z08 = new ZZT08();
                Z08.SCHOOL_NO = bDMT01.SCHOOL_NO;
                Z08.USER_NO = "coolman";
                Z08.PASSWORD = "0000";
                db.ZZT08.Add(Z08);

                //帳號(非學生)與系統角色對照檔
                HRMT25 H25 = new HRMT25();
                H25.SCHOOL_NO = bDMT01.SCHOOL_NO;
                H25.USER_NO = "coolman";
                H25.ROLE_ID = "4";
                H25.CHG_PERSON = bDMT01.SCHOOL_NO + "_0000";
                H25.CHG_DATE = DateTime.Now;
                H25.DEFAULT_YN = "Y";
                db.HRMT25.Add(H25);

                //新增志工helper
                HRMT01 helper = new HRMT01();
                helper.SCHOOL_NO = bDMT01.SCHOOL_NO;
                helper.USER_NO = "helper";
                helper.NAME = "志工";
                helper.SNAME = "志工";
                helper.SEX = "1";
                helper.GRADE = 0;
                helper.USER_STATUS = UserStaus.Enabled;
                helper.USER_TYPE = UserType.Parents;
                db.HRMT01.Add(helper);

                //新增志工預設密碼
                ZZT08 helperZ08 = new ZZT08();
                helperZ08.SCHOOL_NO = bDMT01.SCHOOL_NO;
                helperZ08.USER_NO = "helper";
                helperZ08.PASSWORD = "0000";
                db.ZZT08.Add(helperZ08);

                //帳號(非學生)與系統角色對照檔
                HRMT25 helperH25 = new HRMT25();
                helperH25.SCHOOL_NO = bDMT01.SCHOOL_NO;
                helperH25.USER_NO = "helper";
                helperH25.ROLE_ID = "14";
                helperH25.CHG_PERSON = bDMT01.SCHOOL_NO + "_0000";
                helperH25.CHG_DATE = DateTime.Now;
                helperH25.DEFAULT_YN = "Y";
                db.HRMT25.Add(helperH25);

                try
                {
                    db.SaveChanges();
                    TempData["StatusMessage"] = "異動成功";
                }
                catch (DbEntityValidationException ex)
                {
                    var entityError = ex.EntityValidationErrors.SelectMany(x => x.ValidationErrors).Select(x => x.ErrorMessage);
                    var getFullMessage = string.Join("; ", entityError);
                    var exceptionMessage = string.Concat(ex.Message, "errors are: ", getFullMessage);
                    TempData["StatusMessage"] = "異動失敗, 系統錯誤原因:" + exceptionMessage;
                }
                return RedirectToAction("QUERY");
            }
            return View(bDMT01);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [ValidateInput(false)]
        public ActionResult MODIFY([Bind(Include = "SCHOOL_NO,SCHOOL_NAME,SHORT_NAME,CITY,ADDRESS,EngCity,EngADDR,Manager_No,TEL,FAX,Manager_No,Manager_Name,Manager_Phone,WEBADDRESS,E_MAIL,LOGO_FILE,EXPLAIN,BOOKEXPLAIN,AwatQ02SEXPLAIN,AwatQ02TEXPLAIN,ADDI01SEXPLAIN,ADDT06SEXPLAIN,ADDT06TEXPLAIN,MaintanceSDate,MaintanceEDate,SCHOOL_TYPE,ZZZI09_USE_S_MMDD,ZZZI09_USE_E_MMDD,ONE_LAP_M,RUN_PEOPLE_STANDARD,CHECK_CASH_LIMIT,ADDI01Context,SHORT_NAMEDESC,SendMailPostToTecher,SendMailReadToTecher")] BDMT01 bDMT01, HttpPostedFileBase Imgfile)
        {
            ViewBag.SchoolTypeItems = BDMT01.SchoolType.GetItems(bDMT01.SCHOOL_TYPE);
            if (ModelState.IsValid == false) return View(bDMT01);

            BDMT01 oldbDMT01 = db.BDMT01.Find(bDMT01.SCHOOL_NO);
            if (bDMT01 == null)
            {
                return HttpNotFound();
            }
            oldbDMT01.SCHOOL_NAME = bDMT01.SCHOOL_NAME;
            oldbDMT01.SHORT_NAME = bDMT01.SHORT_NAME;
            oldbDMT01.CITY = bDMT01.CITY;
            oldbDMT01.ADDRESS = bDMT01.ADDRESS;
            oldbDMT01.TEL = bDMT01.TEL;
            oldbDMT01.FAX = bDMT01.FAX;
            oldbDMT01.Manager_Name = bDMT01.Manager_Name;
            oldbDMT01.Manager_Phone = bDMT01.Manager_Phone;
            oldbDMT01.E_MAIL = bDMT01.E_MAIL;
            oldbDMT01.EngCity = bDMT01.EngCity;
            oldbDMT01.EngADDR = bDMT01.EngADDR;
            oldbDMT01.WEBADDRESS = bDMT01.WEBADDRESS;
            oldbDMT01.LOGO_FILE = bDMT01.LOGO_FILE;
            oldbDMT01.EXPLAIN = HtmlUtility.SanitizeHtml(bDMT01.EXPLAIN);
            oldbDMT01.BOOKEXPLAIN = HtmlUtility.SanitizeHtml(bDMT01.BOOKEXPLAIN);
            oldbDMT01.AwatQ02SEXPLAIN = HtmlUtility.SanitizeHtml(bDMT01.AwatQ02SEXPLAIN);
            oldbDMT01.AwatQ02TEXPLAIN = HtmlUtility.SanitizeHtml(bDMT01.AwatQ02TEXPLAIN);
            oldbDMT01.ADDI01SEXPLAIN = HtmlUtility.SanitizeHtml(bDMT01.ADDI01SEXPLAIN);
            oldbDMT01.ADDT06SEXPLAIN = HtmlUtility.SanitizeHtml(bDMT01.ADDT06SEXPLAIN);
            oldbDMT01.ADDT06TEXPLAIN = HtmlUtility.SanitizeHtml(bDMT01.ADDT06TEXPLAIN);
            oldbDMT01.CHG_PERSON = user.USER_NO;
            oldbDMT01.CHG_DATE = DateTime.Now;
            oldbDMT01.MaintanceSDate = bDMT01.MaintanceSDate;
            oldbDMT01.MaintanceEDate = bDMT01.MaintanceEDate;
            oldbDMT01.SCHOOL_TYPE = bDMT01.SCHOOL_TYPE;
            oldbDMT01.ZZZI09_USE_S_MMDD = bDMT01.ZZZI09_USE_S_MMDD;
            oldbDMT01.ZZZI09_USE_E_MMDD = bDMT01.ZZZI09_USE_E_MMDD;
            oldbDMT01.ONE_LAP_M = bDMT01.ONE_LAP_M;
            oldbDMT01.RUN_PEOPLE_STANDARD = bDMT01.RUN_PEOPLE_STANDARD;
            oldbDMT01.CHECK_CASH_LIMIT = bDMT01.CHECK_CASH_LIMIT;
            oldbDMT01.SendMailPostToTecher = bDMT01.SendMailPostToTecher;
            oldbDMT01.SendMailReadToTecher = bDMT01.SendMailReadToTecher;
            if (string.IsNullOrWhiteSpace(bDMT01.SHORT_NAMEDESC)) {

                bDMT01.SHORT_NAMEDESC = bDMT01.SHORT_NAME;
            }
            oldbDMT01.SHORT_NAMEDESC = bDMT01.SHORT_NAMEDESC;
            oldbDMT01.ADDI01Context = HtmlUtility.SanitizeHtml(bDMT01.ADDI01Context);
            try
            {
                //處理上傳檔案
                //bool ans = doImage(Convert.ToInt32(bDMT01.SCHOOL_NO), Imgfile);
                bool ans = doImage(bDMT01.SCHOOL_NO.ToString(), Imgfile);

                db.SaveChanges();

                TempData["StatusMessage"] = "異動成功";

                List<string> cacheKeys = new List<string>();
                var cacheEnum = HttpRuntime.Cache.GetEnumerator();

                while (cacheEnum.MoveNext())
                {
                    cacheKeys.Add(cacheEnum.Key.ToString());
                }
                foreach (string cacheKey in cacheKeys)
                {
                    HttpRuntime.Cache.Remove(cacheKey);
                }
            }
            catch (DbEntityValidationException ex)
            {
                var entityError = ex.EntityValidationErrors.SelectMany(x => x.ValidationErrors).Select(x => x.ErrorMessage);
                var getFullMessage = string.Join("; ", entityError);
                var exceptionMessage = string.Concat(ex.Message, "errors are: ", getFullMessage);

                TempData["StatusMessage"] = "異動失敗, 系統錯誤原因:" + exceptionMessage;
            }
            return RedirectToAction("QUERY");
        }

        // GET: ADDI15/MODIFY/5
        public ActionResult MODIFY(string SCHOOL_NO)
        {
            if (SCHOOL_NO == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }
            BDMT01 bDMT01 = db.BDMT01.Where(p => p.SCHOOL_NO == SCHOOL_NO.ToString()).FirstOrDefault();
            if (bDMT01 == null)
            {
                return HttpNotFound();
            }
            if (string.IsNullOrWhiteSpace(bDMT01.SHORT_NAMEDESC))
            {

                bDMT01.SHORT_NAMEDESC = bDMT01.SHORT_NAME;
            }
            if (string.IsNullOrWhiteSpace(bDMT01.ZZZI09_USE_S_MMDD)) {

                bDMT01.ZZZI09_USE_S_MMDD = "01/01";
            }

            if (string.IsNullOrWhiteSpace(bDMT01.ZZZI09_USE_S_MMDD))
            {

                bDMT01.ZZZI09_USE_S_MMDD = "01/01";
            }

            ViewBag.SchoolTypeItems = BDMT01.SchoolType.GetItems(bDMT01.SCHOOL_TYPE);
            ViewBag.ImageUrl = GetImageUrl(SCHOOL_NO);

            return View(bDMT01);
        }

        /// <summary>
        /// 啟用
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        public ActionResult StartUser(BDMT01IndexViewModel model, string SCHOOL_NO)
        {
            if (HRMT24_ENUM.CheckQQutSchool(user))
            {
                HRMT01 HT01 = db.HRMT01.Where(p => p.SCHOOL_NO == SCHOOL_NO && p.USER_NO == "coolman").FirstOrDefault();
                if (HT01 != null)
                {
                    HT01.USER_STATUS = UserStaus.Enabled;
                    db.Entry(HT01).State = EntityState.Modified;
                    db.SaveChanges();
                    TempData["StatusMessage"] = "已解鎖";
                }
            }

            return RedirectToAction("Query", model);
        }

        public ActionResult DoResetPassword(BDMT01IndexViewModel model, string SCHOOL_NO)
        {
            if (HRMT24_ENUM.CheckQQutSchool(user))
            {
                ZZT08 zzt08 = db.ZZT08.Where(p => p.SCHOOL_NO == SCHOOL_NO && p.USER_NO == "coolman").FirstOrDefault();
                if (zzt08 != null)
                {
                    zzt08.PASSWORD = "0000";
                    db.SaveChanges();
                    TempData["StatusMessage"] = "已重設密碼";
                }

                //11/6 Helper的密碼要可以還原0000
                ZZT08 zzt08h = db.ZZT08.Where(p => p.SCHOOL_NO == SCHOOL_NO && p.USER_NO == "helper").FirstOrDefault();
                if (zzt08h != null)
                {
                    zzt08h.PASSWORD = "0000";
                    db.SaveChanges();
                }
            }

            return RedirectToAction("Query", model);
        }

        private bool doImage(string SCHOOL_NO, HttpPostedFileBase Imgfile)
        {
            if (Imgfile == null) return false;
            //a.組檔案名稱
            string IMG_FILE = SCHOOL_NO + ".png";
            Regex regexCode = new Regex(@".*\.(jpg|jpeg|png|gif|bmp)");
            if (regexCode.IsMatch(IMG_FILE) == false)
            {
                return false;
            }

            //b.組上傳資料夾路徑
            string UploadImageRoot =
                System.Web.Configuration.WebConfigurationManager.AppSettings["UploadImageRoot"];
            if (UploadImageRoot == null) UploadImageRoot = @"~\Content\";
            if (UploadImageRoot.EndsWith(@"\") == false) UploadImageRoot += @"\";
            string imgPath = string.Format(@"{0}BDMT01IMG\", Request.MapPath(UploadImageRoot));
            if (Directory.Exists(imgPath) == false) Directory.CreateDirectory(imgPath);

            //c.縮圖
            //System.Drawing.Image image = System.Drawing.Image.FromStream(Imgfile.InputStream);
            //double FixWidth = 300;
            //double FixHeight = 300;
            //double rate = 1;
            //if (image.Width > FixWidth || image.Height > FixHeight)
            //{
            //    if (image.Width > FixWidth) rate = FixWidth / image.Width;
            //    if (image.Height * rate > FixHeight) rate = FixHeight / image.Height;

            //    int w = Convert.ToInt32(image.Width * rate);
            //    int h = Convert.ToInt32(image.Height * rate);
            //    Bitmap imageOutput = new Bitmap(image, w, h);
            //    imageOutput.Save(Path.Combine(imgPath, IMG_FILE), image.RawFormat);
            //}
            //else
            //{
            //直接儲存
            Imgfile.SaveAs(Path.Combine(imgPath, IMG_FILE));
            //}
            //image.Dispose();
            return true;
        }

        private string GetImageUrl(string SCHOOL_NO)
        {
            string UploadImageRoot =
               System.Web.Configuration.WebConfigurationManager.AppSettings["UploadImageRoot"];
            if (UploadImageRoot == null) UploadImageRoot = @"~\Content\";
            if (UploadImageRoot.EndsWith(@"\") == false) UploadImageRoot += @"\";
            string imgPath = string.Format(@"{0}BDMT01IMG\", Request.MapPath(UploadImageRoot));
            if (System.IO.File.Exists(Path.Combine(imgPath, SCHOOL_NO.ToString())))
            {
                string imgUrl = string.Format(@"{0}BDMT01IMG\{1}.png", UploadImageRoot, SCHOOL_NO);
                return Url.Content(imgUrl);
            }

            return string.Format(Url.Content("~/Content/BDMT01IMG/{0}.png"), SCHOOL_NO);
        }
    }
}