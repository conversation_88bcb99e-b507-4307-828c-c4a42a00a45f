﻿using ECOOL_APP.EF;
using EcoolWeb.CustomAttribute;
using EcoolWeb.Models;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace EcoolWeb.Controllers
{
    [SessionExpire]
    public class ZZZ21Controller : Controller
    {
        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();
        private ECOOL_APP.UserProfile user = UserProfileHelper.Get();
        // GET: ZZZ21
        public ActionResult MODIFY()
        {
            int SchoolCount = db.ADDT08.Where(p => p.SCHOOL_NO == user.SCHOOL_NO).Count();
            List<ADDT08> ltADDT08 = new List<ADDT08>();
            if (SchoolCount==0)
            {
                ltADDT08 = db.ADDT08.Where(p => p.SCHOOL_NO == "403605").ToList();
            }
            else
            {
                ltADDT08 = db.ADDT08.Where(p => p.SCHOOL_NO == user.SCHOOL_NO).ToList();
            }

            return View(ltADDT08);
        }

        [HttpPost]
        public ActionResult MODIFY(FormCollection vADDT08)
        {

            int SchoolCount = db.ADDT08.Where(p => p.SCHOOL_NO == user.SCHOOL_NO).Count();

            try
            {
                if (SchoolCount > 0)
                {
                    for (int i = 0; i < 10; i++)
                    {
                        ADDT08 A08 = new ADDT08();
                        int tmpLeveL_ID = Convert.ToInt32(vADDT08["hidLeveL_ID_" + (i + 1)].Split(',')[0]);
                        int iLeveL_ID = Convert.ToInt32(tmpLeveL_ID);
                        A08 = db.ADDT08.Where(p => p.LEVEL_ID == tmpLeveL_ID && p.SCHOOL_NO == user.SCHOOL_NO).FirstOrDefault();
                        A08.SCHOOL_NO = user.SCHOOL_NO;
                        A08.LEVEL_ID = (byte)(i + 1);
                        A08.LEVEL_DESC = vADDT08["txtLEVEL_DESC_" + (i + 1)];
                        A08.CHG_PERSON = user.NAME;
                        A08.CHG_DATE = DateTime.Now;
                        db.Entry(A08).State = EntityState.Modified;
                        db.SaveChanges();
                    }
                }
                else
                {
                    for (int i = 0; i < 11; i++)
                    {
                        int tmpLeveL_ID = Convert.ToInt32(vADDT08["hidLeveL_ID_" + (i + 1)].Split(',')[0]);
                        int iLeveL_ID = Convert.ToInt32(tmpLeveL_ID);
                        if (tmpLeveL_ID != 0 && user.SCHOOL_NO != null)
                        {
                            ADDT08 A08 = new ADDT08();
                            A08.SCHOOL_NO = user.SCHOOL_NO;
                            A08.LEVEL_ID = (byte)(i + 1);
                            A08.LEVEL_DESC = vADDT08["txtLEVEL_DESC_" + (i + 1)];
                            A08.CHG_PERSON = user.NAME;
                            A08.CHG_DATE = DateTime.Now;
                            db.ADDT08.Add(A08);
                            db.SaveChanges();
                        }
                    }
                }

                TempData["StatusMessage"] = "異動成功";
            }
            catch (Exception ex)
            {

                TempData["StatusMessage"] = "異動失敗, 系統錯誤原因:" + ex.Message;
            }

            List<ADDT08> ltADDT08 = db.ADDT08.Where(p => p.SCHOOL_NO == user.SCHOOL_NO).ToList();
            return RedirectToAction("../ZZZ21/MODIFY", ltADDT08);
        }
    }
}