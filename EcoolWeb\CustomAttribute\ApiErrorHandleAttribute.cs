﻿using EcoolWeb.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web;
using System.Web.Http.Filters;

namespace EcoolWeb.CustomAttribute
{
    public class ApiErrorHandleAttribute : System.Web.Http.Filters.ExceptionFilterAttribute
    {
   

        public override void OnException(System.Web.Http.Filters.HttpActionExecutedContext actionExecutedContext)
        {
            base.OnException(actionExecutedContext);

            // 取得發生例外時的錯誤訊息
            var errorMessage = actionExecutedContext.Exception.Message;

            var result = new ApiResultModel()
            {
                Status = HttpStatusCode.BadRequest,
                ErrorMessage = errorMessage
            };

            // 重新打包回傳的訊息
            actionExecutedContext.Response = actionExecutedContext.Request
                .CreateResponse(result.Status, result);
        }

    }
}