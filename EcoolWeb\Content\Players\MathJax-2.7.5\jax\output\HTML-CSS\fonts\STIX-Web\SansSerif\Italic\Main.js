/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX-Web/SansSerif/Italic/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXMathJax_SansSerif-italic"]={directory:"SansSerif/Italic",family:"STIXMathJax_SansSerif",style:"italic",testString:"\u00A0\uE1B4\uE1B5\uE1B6\uE1B7\uE1B8\uE1B9\uE1BA\uE1BB\uE1BC\uE1BD\uE1BE\uE1BF\uE1C0\uE1C1",32:[0,0,250,0,0],160:[0,0,250,0,0],57780:[676,14,500,86,578],57781:[677,0,500,223,469],57782:[676,0,500,35,574],57783:[676,14,500,44,544],57784:[676,0,500,52,547],57785:[676,14,500,49,626],57786:[684,14,500,83,617],57787:[662,8,500,146,616],57788:[676,14,500,81,560],57789:[676,21,500,51,579],57790:[683,10,536,45,527],57791:[674,0,660,28,632],57792:[662,0,662,60,627],57793:[662,0,562,60,665],57794:[674,0,660,28,632],57795:[662,0,639,60,664],57796:[662,0,698,25,760],57797:[662,0,700,60,735],57798:[676,14,780,75,755],57799:[662,0,433,50,503],57800:[662,0,631,60,715],57801:[674,0,664,20,624],57802:[662,0,890,60,918],57803:[662,14,724,60,752],57804:[662,0,722,47,754],57805:[676,14,780,75,755],57806:[662,0,700,60,735],57807:[662,0,538,60,624],57808:[676,14,780,75,755],57809:[662,0,654,21,706],57810:[662,0,585,72,659],57811:[676,0,593,83,725],57812:[662,0,736,52,736],57813:[662,0,722,20,795],57814:[681,0,712,105,805],57815:[676,0,795,39,795],57816:[463,10,586,47,616],57817:[683,215,535,-12,559],57818:[463,216,503,84,527],57819:[683,10,497,30,537],57820:[463,10,494,35,484],57821:[683,213,429,32,454],57822:[463,215,493,38,486],57823:[683,10,518,65,511],57824:[464,10,296,56,268],57825:[464,0,472,38,517],57826:[683,11,536,18,502],57827:[453,215,561,-9,536],57828:[464,14,376,41,416],57829:[683,215,434,43,464],57830:[463,10,533,45,505],57831:[453,10,565,45,589],57832:[462,216,534,-33,510],57833:[463,212,436,52,500],57834:[453,10,607,45,625],57835:[453,10,468,42,486],57836:[463,10,514,61,490],57837:[464,216,665,55,641],57838:[463,215,514,-72,552],57839:[461,216,654,75,705],57840:[454,10,630,50,636],57841:[463,10,462,45,467],57842:[683,12,534,45,525],57843:[684,216,648,48,630],57844:[463,216,536,38,518],57845:[453,10,795,40,811],120328:[674,0,666,31,635],120329:[662,0,604,74,641],120330:[676,14,671,96,755],120331:[662,0,692,74,751],120332:[662,0,583,74,678],120333:[662,0,535,74,679],120334:[676,14,695,97,755],120335:[662,0,658,74,749],120336:[662,0,401,59,512],120337:[662,14,398,22,470],120338:[662,0,634,74,729],120339:[662,0,559,74,564],120340:[662,0,843,75,933],120341:[662,14,675,74,766],120342:[676,14,714,99,779],120343:[662,0,525,74,638],120344:[676,175,716,99,779],120345:[662,0,589,74,639],120346:[676,14,541,62,597],120347:[662,0,608,161,748],120348:[662,14,661,117,757],120349:[662,11,654,196,788],120350:[662,11,921,194,1057],120351:[662,0,700,31,806],120352:[662,0,630,186,774],120353:[662,0,637,28,763],120354:[463,10,448,55,467],120355:[684,10,496,74,535],120356:[463,10,456,67,503],120357:[684,11,494,72,600],120358:[463,10,444,69,487],120359:[683,0,336,101,526],120360:[463,216,496,-7,575],120361:[684,0,487,63,510],120362:[679,0,220,69,325],120363:[679,216,254,-118,354],120364:[684,0,453,63,556],120365:[684,0,205,61,313],120366:[464,0,756,65,775],120367:[464,0,487,63,510],120368:[463,10,499,76,536],120369:[464,216,498,14,538],120370:[464,216,498,72,549],120371:[464,0,336,63,439],120372:[463,10,389,61,432],120373:[580,10,291,96,376],120374:[453,11,491,89,536],120375:[453,14,474,143,555],120376:[453,14,702,140,787],120377:[453,0,482,30,544],120378:[453,216,484,-19,565],120379:[453,0,447,25,517]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"STIXMathJax_SansSerif-italic"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/SansSerif/Italic/Main.js"]);
