﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;

namespace ECOOL_APP.EF
{
    public static class PropertyInfoExtensions
    {
        public static object GetPropertyValue(object obj, string property)
        {
            System.Reflection.PropertyInfo propertyInfo = obj.GetType().GetProperty(property);
            return propertyInfo.GetValue(obj, null);
        }

        public static IQueryable<TEntity> OrderBy<TEntity>(this IQueryable<TEntity> source, string orderByProperty,
                          PageGlobal.SortType SortType)
        {
            string command = SortType == PageGlobal.SortType.DESC ? "OrderByDescending" : "OrderBy";
            var type = typeof(TEntity);
            var property = type.GetProperty(orderByProperty);

            if (property != null)
            {
                var parameter = Expression.Parameter(type, "p");
                var propertyAccess = Expression.MakeMemberAccess(parameter, property);
                var orderByExpression = Expression.Lambda(propertyAccess, parameter);
                var resultExpression = Expression.Call(typeof(Queryable), command, new Type[] { type, property.PropertyType },
                                              source.Expression, Expression.Quote(orderByExpression));
                return source.Provider.CreateQuery<TEntity>(resultExpression);
            }
            else
            {
                throw new ApplicationException($"非合法參數或欄位 Value :{orderByProperty}"); ;
            }
        }

        /// <summary>
        /// 1. The sortExpressions is a list of Tuples, the first item of the
        /// tuples is the field name,
        /// the second item of the tuples is the sorting order (asc/desc) case sensitive.
        /// 2. If the field name (case sensitive) provided for sorting does not exist
        /// in the object,
        /// exception is thrown
        /// 3. If a property name shows up more than once in the "sortExpressions",
        /// only the first takes effect.
        /// </summary>
        public static IEnumerable<T> MultipleSort<T>(this IQueryable<T> data,
            List<GridSort> gridsorts)
        {
            var sortExpressions = new List<Tuple<string,
                string>>();
            for (int i = 0; i < gridsorts.Count(); i++)
            {
                var fieldName = gridsorts[i].Field.Trim();
                var sortOrder = (gridsorts[i].Dir.Length > 1) ?
                    gridsorts[i].Dir.Trim().ToLower() : "asc";
                sortExpressions.Add(new Tuple<string, string>(fieldName, sortOrder));
            }
            // No sorting needed
            if ((sortExpressions == null) || (sortExpressions.Count <= 0))
            {
                return data;
            }
            // Let us sort it
            IEnumerable<T> query = from item in data select item;
            IOrderedEnumerable<T> orderedQuery = null;
            for (int i = 0; i < sortExpressions.Count; i++)
            {
                // We need to keep the loop index, not sure why it is altered by the Linq.
                var index = i;
                Func<T, object> expression = item => item.GetType()
                 .GetProperty(sortExpressions[index].Item1)
                 .GetValue(item, null);
                if (sortExpressions[index].Item2 == "asc")
                {
                    orderedQuery = (index == 0) ? query.OrderBy(expression) :
                        orderedQuery.ThenBy(expression);
                }
                else
                {
                    orderedQuery = (index == 0) ? query.OrderByDescending(expression) :
                        orderedQuery.ThenByDescending(expression);
                }
            }
            query = orderedQuery;
            return query;
        }
    }
}