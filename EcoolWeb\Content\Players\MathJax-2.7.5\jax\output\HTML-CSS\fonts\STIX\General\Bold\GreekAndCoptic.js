/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/General/Bold/GreekAndCoptic.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXGeneral-bold"],{894:[472,180,333,82,266],900:[692,-528,300,118,263],901:[692,-528,390,-2,392],902:[700,0,722,9,689],903:[472,-303,333,82,251],904:[700,0,800,10,791],905:[700,0,920,10,904],906:[700,0,530,10,516],908:[700,19,778,10,743],910:[700,0,860,10,846],911:[700,0,780,10,761],912:[692,14,390,-2,392],913:[690,0,722,9,689],914:[676,0,667,16,619],917:[676,0,667,16,641],918:[676,0,667,28,634],919:[676,0,778,21,759],921:[676,0,389,20,370],922:[676,0,778,30,769],924:[676,0,944,14,921],925:[676,18,722,16,701],927:[691,19,778,35,743],929:[676,0,611,16,600],932:[676,0,667,31,636],935:[676,0,722,16,699],938:[915,0,389,20,370],939:[915,0,703,7,693],940:[692,14,644,25,618],941:[692,14,444,28,429],942:[692,205,585,12,545],943:[692,14,326,15,304],944:[692,14,576,12,551],945:[473,14,644,25,618],946:[692,205,556,45,524],947:[473,205,518,12,501],948:[692,14,502,26,477],949:[473,14,444,28,429],950:[692,205,459,23,437],951:[473,205,585,12,545],952:[692,14,501,25,476],953:[461,14,326,15,304],954:[473,0,581,21,559],955:[692,18,547,19,527],956:[461,205,610,45,588],957:[473,14,518,15,495],958:[692,205,468,23,439],959:[473,14,500,25,476],960:[461,18,631,20,609],961:[473,205,547,45,515],962:[473,203,464,23,444],963:[461,14,568,25,529],964:[461,14,492,18,457],965:[473,14,576,12,551],966:[473,205,653,24,629],967:[473,205,612,21,586],968:[473,205,763,12,751],969:[473,14,733,26,708],970:[666,14,335,-2,337],971:[666,14,576,12,551],972:[692,14,500,25,476],973:[692,14,576,12,551],974:[692,14,733,26,708],976:[697,10,500,54,462],977:[692,14,647,12,620],978:[692,0,743,7,733],981:[676,205,653,24,629],982:[461,14,864,9,851],984:[691,205,778,35,743],985:[473,205,500,25,476],986:[691,211,680,45,645],987:[503,203,504,23,483],988:[676,0,620,16,593],989:[461,205,491,45,458],990:[797,14,757,35,715],991:[692,0,485,29,453],992:[692,205,839,33,801],993:[639,205,611,29,583],1008:[473,19,563,12,546],1009:[473,205,511,25,486],1012:[691,19,778,35,743],1013:[473,14,444,25,430],1014:[473,14,444,14,419]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/Bold/GreekAndCoptic.js");
