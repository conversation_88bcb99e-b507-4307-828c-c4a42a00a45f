/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Gyre-Pagella/Size5/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.GyrePagellaMathJax_Size5={directory:"Size5/Regular",family:"GyrePagellaMathJax_Size5",testString:"\u00A0\u0302\u0303\u0306\u030C\u0311\u032C\u032D\u032E\u032F\u0330\u2016\u2044\u20E9\u221A",32:[0,0,250,0,0],40:[1259,759,624,139,531],41:[1259,759,624,93,485],47:[1793,1293,1288,80,1208],91:[1265,765,500,139,407],92:[1793,1293,1288,80,1208],93:[1265,765,500,93,361],123:[1264,764,555,93,462],124:[1245,745,221,80,141],125:[1264,764,555,93,462],160:[0,0,250,0,0],770:[714,-541,1251,0,1251],771:[707,-537,1247,0,1247],774:[709,-549,1279,0,1279],780:[713,-540,1251,0,1251],785:[723,-563,1279,0,1279],812:[-60,233,1251,0,1251],813:[-70,243,1251,0,1251],814:[-60,220,1279,0,1279],815:[-78,238,1279,0,1279],816:[-78,249,1247,0,1247],8214:[1245,745,402,80,322],8260:[1793,1293,1288,80,1208],8425:[782,-652,2226,0,2226],8730:[1560,1030,757,120,787],8739:[1245,745,221,80,141],8741:[1245,745,402,80,322],8968:[1265,745,500,139,407],8969:[1265,745,500,93,361],8970:[1245,765,500,139,407],8971:[1245,765,500,93,361],9001:[1797,1298,571,93,478],9002:[1797,1298,571,93,478],9140:[782,-652,2226,0,2226],9141:[-182,312,2226,0,2226],9180:[792,-570,3028,0,3028],9181:[-100,322,3028,0,3028],9182:[808,-587,3038,0,3038],9183:[-117,338,3038,0,3038],9184:[739,-525,3076,0,3076],9185:[-55,269,3076,0,3076],10214:[1265,765,504,139,411],10215:[1265,765,504,93,365],10216:[1797,1298,571,93,478],10217:[1797,1298,571,93,478],10218:[1797,1298,872,93,779],10219:[1797,1298,872,93,779],10222:[1259,759,418,139,325],10223:[1259,759,418,93,279]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"GyrePagellaMathJax_Size5"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Size5/Regular/Main.js"]);
