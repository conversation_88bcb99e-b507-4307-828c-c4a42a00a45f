<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.VisualStudio.TestPlatform.TestFramework</name>
    </assembly>
    <members>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod">
            <summary>
            Atributo TestMethod para la ejecución.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.TestMethodName">
            <summary>
            Obtiene el nombre del método de prueba.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.TestClassName">
            <summary>
            Obtiene el nombre de la clase de prueba.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.ReturnType">
            <summary>
            Obtiene el tipo de valor devuelto del método de prueba.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.ParameterTypes">
            <summary>
            Obtiene los parámetros del método de prueba.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.MethodInfo">
            <summary>
            Obtiene el valor de methodInfo para el método de prueba.
            </summary>
            <remarks>
            This is just to retrieve additional information about the method.
            Do not directly invoke the method using MethodInfo. Use ITestMethod.Invoke instead.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.Invoke(System.Object[])">
            <summary>
            Invoca el método de prueba.
            </summary>
            <param name="arguments">
            Argumentos que se pasan al método de prueba (por ejemplo, controlada por datos)
            </param>
            <returns>
            Resultado de la invocación del método de prueba.
            </returns>
            <remarks>
            This call handles asynchronous test methods as well.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.GetAllAttributes(System.Boolean)">
            <summary>
            Obtiene todos los atributos del método de prueba.
            </summary>
            <param name="inherit">
            Indica si el atributo definido en la clase primaria es válido.
            </param>
            <returns>
            Todos los atributos.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.GetAttributes``1(System.Boolean)">
            <summary>
            Obtiene un atributo de un tipo específico.
            </summary>
            <typeparam name="AttributeType"> System.Attribute type. </typeparam>
            <param name="inherit">
            Indica si el atributo definido en la clase primaria es válido.
            </param>
            <returns>
            Atributos del tipo especificado.
            </returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.Helper">
            <summary>
            Elemento auxiliar.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Helper.CheckParameterNotNull(System.Object,System.String,System.String)">
            <summary>
            Parámetro de comprobación no NULL.
            </summary>
            <param name="param">
            El parámetro.
            </param>
            <param name="parameterName">
            El nombre del parámetro.
            </param>
            <param name="message">
            El mensaje.
            </param>
            <exception cref="T:System.ArgumentNullException"> Throws argument null exception when parameter is null. </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Helper.CheckParameterNotNullOrEmpty(System.String,System.String,System.String)">
            <summary>
            Parámetro de comprobación no NULL o vacío.
            </summary>
            <param name="param">
            El parámetro.
            </param>
            <param name="parameterName">
            El nombre del parámetro.
            </param>
            <param name="message">
            El mensaje.
            </param>
            <exception cref="T:System.ArgumentException"> Throws ArgumentException when parameter is null. </exception>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod">
            <summary>
            Enumeración de cómo se accede a las filas de datos en las pruebas controladas por datos.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod.Sequential">
            <summary>
            Las filas se devuelven en orden secuencial.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod.Random">
            <summary>
            Las filas se devuelven en orden aleatorio.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute">
            <summary>
            Atributo para definir los datos insertados de un método de prueba.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute.#ctor(System.Object)">
            <summary>
            Inicializa una nueva instancia de la clase <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute"/>.
            </summary>
            <param name="data1"> Objeto de datos. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute.#ctor(System.Object,System.Object[])">
            <summary>
            Inicializa una nueva instancia de la clase <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute"/>, que toma una matriz de argumentos.
            </summary>
            <param name="data1"> Objeto de datos. </param>
            <param name="moreData"> Más datos. </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute.Data">
            <summary>
            Obtiene datos para llamar al método de prueba.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute.DisplayName">
            <summary>
            Obtiene o establece el nombre para mostrar en los resultados de pruebas para personalizarlo.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException">
            <summary>
            Excepción de aserción no concluyente.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException.#ctor(System.String,System.Exception)">
            <summary>
            Inicializa una nueva instancia de la clase <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException"/>.
            </summary>
            <param name="msg"> El mensaje. </param>
            <param name="ex"> La excepción. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException.#ctor(System.String)">
            <summary>
            Inicializa una nueva instancia de la clase <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException"/>.
            </summary>
            <param name="msg"> El mensaje. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException.#ctor">
            <summary>
            Inicializa una nueva instancia de la clase <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException"/>.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException">
            <summary>
            Clase InternalTestFailureException. Se usa para indicar un error interno de un caso de prueba.
            </summary>
            <remarks>
            This class is only added to preserve source compatibility with the V1 framework.
            For all practical purposes either use AssertFailedException/AssertInconclusiveException.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException.#ctor(System.String,System.Exception)">
            <summary>
            Inicializa una nueva instancia de la clase <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException"/>.
            </summary>
            <param name="msg"> Mensaje de la excepción. </param>
            <param name="ex"> La excepción. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException.#ctor(System.String)">
            <summary>
            Inicializa una nueva instancia de la clase <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException"/>.
            </summary>
            <param name="msg"> Mensaje de la excepción. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException.#ctor">
            <summary>
            Inicializa una nueva instancia de la clase <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException"/>.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute">
            <summary>
            Atributo que indica que debe esperarse una excepción del tipo especificado.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.#ctor(System.Type)">
            <summary>
            Inicializa una nueva instancia de la clase <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute"/> con el tipo esperado.
            </summary>
            <param name="exceptionType">Tipo de la excepción esperada</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.#ctor(System.Type,System.String)">
            <summary>
            Inicializa una nueva instancia de la clase <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute"/>
            con el tipo esperado y el mensaje para incluir cuando la prueba no produce una excepción.
            </summary>
            <param name="exceptionType">Tipo de la excepción esperada</param>
            <param name="noExceptionMessage">
            Mensaje que se incluye en el resultado de la prueba si esta no se supera debido a que no se inicia una excepción
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.ExceptionType">
            <summary>
            Obtiene un valor que indica el tipo de la excepción esperada.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.AllowDerivedTypes">
            <summary>
            Obtiene o establece un valor que indica si se permite que los tipos derivados del tipo de la excepción esperada
            se consideren también como esperados.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.NoExceptionMessage">
            <summary>
            Obtiene el mensaje que debe incluirse en el resultado de la prueba si esta no acaba correctamente porque no se produce una excepción.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.Verify(System.Exception)">
            <summary>
            Comprueba que el tipo de la excepción producida por la prueba unitaria es el esperado.
            </summary>
            <param name="exception">Excepción que inicia la prueba unitaria</param>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute">
            <summary>
            Clase base para atributos que especifican que se espere una excepción de una prueba unitaria.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.#ctor">
            <summary>
            Inicializa una nueva instancia de la clase <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute"/> con un mensaje de ausencia de excepción predeterminado.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.#ctor(System.String)">
            <summary>
            Inicializa una nueva instancia de la clase <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute"/> con un mensaje de ausencia de excepción.
            </summary>
            <param name="noExceptionMessage">
            Mensaje para incluir en el resultado de la prueba si esta no se supera debido a que no se inicia una
            excepción
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.NoExceptionMessage">
            <summary>
            Obtiene el mensaje que debe incluirse en el resultado de la prueba si esta no acaba correctamente porque no se produce una excepción.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.SpecifiedNoExceptionMessage">
            <summary>
            Obtiene el mensaje que debe incluirse en el resultado de la prueba si esta no acaba correctamente porque no se produce una excepción.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.GetDefaultNoExceptionMessage(System.String)">
            <summary>
            Obtiene el mensaje de ausencia de excepción predeterminado.
            </summary>
            <param name="expectedExceptionAttributeTypeName">Nombre del tipo de atributo ExpectedException</param>
            <returns>Mensaje de ausencia de excepción predeterminado</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.Verify(System.Exception)">
            <summary>
            Determina si se espera la excepción. Si el método devuelve un valor, se entiende
            que se esperaba la excepción. Si el método produce una excepción,
            se entiende que no se esperaba la excepción y se incluye el mensaje
            de la misma en el resultado de la prueba. Se puede usar <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.Assert"/> para mayor
            comodidad. Si se utiliza <see cref="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Inconclusive"/> y la aserción no funciona,
            el resultado de la prueba se establece como No concluyente.
            </summary>
            <param name="exception">Excepción que inicia la prueba unitaria</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.RethrowIfAssertException(System.Exception)">
            <summary>
            Produce de nuevo la excepción si es de tipo AssertFailedException o AssertInconclusiveException.
            </summary>
            <param name="exception">La excepción que se va a reiniciar si es una excepción de aserción</param>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper">
            <summary>
            Esta clase está diseñada para ayudar al usuario a realizar pruebas unitarias para tipos con tipos genéricos.
            GenericParameterHelper satisface algunas de las restricciones de tipo genérico comunes,
            como:
            1. Constructor predeterminado público.
            2. Implementa una interfaz común: IComparable, IEnumerable.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.#ctor">
            <summary>
            Inicializa una nueva instancia de la clase <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper"/> que
            satisface la restricción "renovable" en genéricos de C#.
            </summary>
            <remarks>
            This constructor initializes the Data property to a random value.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.#ctor(System.Int32)">
            <summary>
            Inicializa una nueva instancia de la clase <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper"/> que
            inicializa la propiedad Data con un valor proporcionado por el usuario.
            </summary>
            <param name="data">Cualquier valor entero</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.Data">
            <summary>
            Obtiene o establece los datos.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.Equals(System.Object)">
            <summary>
            Compara el valor de dos objetos GenericParameterHelper.
            </summary>
            <param name="obj">objeto con el que hacer la comparación</param>
            <returns>Es true si el objeto tiene el mismo valor que el objeto GenericParameterHelper "this".
            De lo contrario, false.</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.GetHashCode">
            <summary>
            Devuelve un código hash para este objeto.
            </summary>
            <returns>El código hash.</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.CompareTo(System.Object)">
            <summary>
            Compara los datos de los dos objetos <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper"/>.
            </summary>
            <param name="obj">Objeto con el que se va a comparar.</param>
            <returns>
            Número con signo que indica los valores relativos de esta instancia y valor.
            </returns>
            <exception cref="T:System.NotSupportedException">
            Thrown when the object passed in is not an instance of <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.GetEnumerator">
            <summary>
            Devuelve un objeto IEnumerator cuya longitud se deriva de
            la propiedad Data.
            </summary>
            <returns>El objeto IEnumerator</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.Clone">
            <summary>
            Devuelve un objeto GenericParameterHelper que es igual al
            objeto actual.
            </summary>
            <returns>El objeto clonado.</returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.Logging.Logger">
            <summary>
            Permite a los usuarios registrar o escribir el seguimiento de las pruebas unitarias con fines de diagnóstico.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.Logging.Logger.LogMessageHandler">
            <summary>
            Controlador para LogMessage.
            </summary>
            <param name="message">Mensaje para registrar.</param>
        </member>
        <member name="E:Microsoft.VisualStudio.TestTools.UnitTesting.Logging.Logger.OnLogMessage">
            <summary>
            Evento que se debe escuchar. Se genera cuando el autor de las pruebas unitarias escribe algún mensaje.
            Lo consume principalmente el adaptador.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Logging.Logger.LogMessage(System.String,System.Object[])">
            <summary>
            API del escritor de la prueba para llamar a los mensajes de registro.
            </summary>
            <param name="format">Formato de cadena con marcadores de posición.</param>
            <param name="args">Parámetros para los marcadores de posición.</param>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryAttribute">
            <summary>
            Atributo TestCategory. Se usa para especificar la categoría de una prueba unitaria.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryAttribute.#ctor(System.String)">
            <summary>
            Inicializa una nueva instancia de la clase <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryAttribute"/> y le aplica la categoría a la prueba.
            </summary>
            <param name="testCategory">
            Categoría de prueba.
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryAttribute.TestCategories">
            <summary>
            Obtiene las categorías que se le han aplicado a la prueba.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryBaseAttribute">
            <summary>
            Clase base del atributo "Category".
            </summary>
            <remarks>
            The reason for this attribute is to let the users create their own implementation of test categories.
            - test framework (discovery, etc) deals with TestCategoryBaseAttribute.
            - The reason that TestCategories property is a collection rather than a string,
              is to give more flexibility to the user. For instance the implementation may be based on enums for which the values can be OR'ed
              in which case it makes sense to have single attribute rather than multiple ones on the same test.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryBaseAttribute.#ctor">
            <summary>
            Inicializa una nueva instancia de la clase <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryBaseAttribute"/>.
            Aplica la categoría a la prueba. Las cadenas que devuelve TestCategories
            se usan con el comando /category para filtrar las pruebas.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryBaseAttribute.TestCategories">
            <summary>
            Obtiene la categoría que se le ha aplicado a la prueba.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            <summary>
            Clase AssertFailedException. Se usa para indicar el error de un caso de prueba.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException.#ctor(System.String,System.Exception)">
            <summary>
            Inicializa una nueva instancia de la clase <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException"/>.
            </summary>
            <param name="msg"> El mensaje. </param>
            <param name="ex"> La excepción. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException.#ctor(System.String)">
            <summary>
            Inicializa una nueva instancia de la clase <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException"/>.
            </summary>
            <param name="msg"> El mensaje. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException.#ctor">
            <summary>
            Inicializa una nueva instancia de la clase <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException"/>.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.Assert">
            <summary>
            Colección de clases auxiliares para probar varias condiciones en las
            pruebas unitarias. Si la condición que se está probando no se cumple, se produce
            una excepción.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.That">
            <summary>
            Obtiene la instancia de singleton de la funcionalidad de Assert.
            </summary>
            <remarks>
            Users can use this to plug-in custom assertions through C# extension methods.
            For instance, the signature of a custom assertion provider could be "public static void IsOfType&lt;T&gt;(this Assert assert, object obj)"
            Users could then use a syntax similar to the default assertions which in this case is "Assert.That.IsOfType&lt;Dog&gt;(animal);"
            More documentation is at "https://github.com/Microsoft/testfx-docs".
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsTrue(System.Boolean)">
            <summary>
            Comprueba si la condición especificada es true y produce una excepción
            si la condición es false.
            </summary>
            <param name="condition">
            Condición que la prueba espera que sea true.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is false.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsTrue(System.Boolean,System.String)">
            <summary>
            Comprueba si la condición especificada es true y produce una excepción
            si la condición es false.
            </summary>
            <param name="condition">
            Condición que la prueba espera que sea true.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="condition"/>
            es false. El mensaje se muestra en los resultados de las pruebas.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is false.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsTrue(System.Boolean,System.String,System.Object[])">
            <summary>
            Comprueba si la condición especificada es true y produce una excepción
            si la condición es false.
            </summary>
            <param name="condition">
            Condición que la prueba espera que sea true.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="condition"/>
            es false. El mensaje se muestra en los resultados de las pruebas.
            </param>
            <param name="parameters">
            Matriz de parámetros que se usa al formatear <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is false.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsFalse(System.Boolean)">
            <summary>
            Comprueba si la condición especificada es false y produce una excepción
            si la condición es true.
            </summary>
            <param name="condition">
            Condición que la prueba espera que sea false.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is true.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsFalse(System.Boolean,System.String)">
            <summary>
            Comprueba si la condición especificada es false y produce una excepción
            si la condición es true.
            </summary>
            <param name="condition">
            Condición que la prueba espera que sea false.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="condition"/>
            es true. El mensaje se muestra en los resultados de las pruebas.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is true.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsFalse(System.Boolean,System.String,System.Object[])">
            <summary>
            Comprueba si la condición especificada es false y produce una excepción
            si la condición es true.
            </summary>
            <param name="condition">
            Condición que la prueba espera que sea false.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="condition"/>
            es true. El mensaje se muestra en los resultados de las pruebas.
            </param>
            <param name="parameters">
            Matriz de parámetros que se usa al formatear <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is true.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNull(System.Object)">
            <summary>
            Comprueba si el objeto especificado es NULL y produce una excepción
            si no lo es.
            </summary>
            <param name="value">
            El objeto que la prueba espera que sea NULL.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNull(System.Object,System.String)">
            <summary>
            Comprueba si el objeto especificado es NULL y produce una excepción
            si no lo es.
            </summary>
            <param name="value">
            El objeto que la prueba espera que sea NULL.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="value"/>
            no es NULL. El mensaje se muestra en los resultados de las pruebas.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNull(System.Object,System.String,System.Object[])">
            <summary>
            Comprueba si el objeto especificado es NULL y produce una excepción
            si no lo es.
            </summary>
            <param name="value">
            El objeto que la prueba espera que sea NULL.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="value"/>
            no es NULL. El mensaje se muestra en los resultados de las pruebas.
            </param>
            <param name="parameters">
            Matriz de parámetros que se usa al formatear <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotNull(System.Object)">
            <summary>
            Comprueba si el objeto especificado no es NULL y produce una excepción
            si lo es.
            </summary>
            <param name="value">
            El objeto que la prueba espera que no sea NULL.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotNull(System.Object,System.String)">
            <summary>
            Comprueba si el objeto especificado no es NULL y produce una excepción
            si lo es.
            </summary>
            <param name="value">
            El objeto que la prueba espera que no sea NULL.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="value"/>
            es NULL. El mensaje se muestra en los resultados de las pruebas.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotNull(System.Object,System.String,System.Object[])">
            <summary>
            Comprueba si el objeto especificado no es NULL y produce una excepción
            si lo es.
            </summary>
            <param name="value">
            El objeto que la prueba espera que no sea NULL.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="value"/>
            es NULL. El mensaje se muestra en los resultados de las pruebas.
            </param>
            <param name="parameters">
            Matriz de parámetros que se usa al formatear <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreSame(System.Object,System.Object)">
            <summary>
            Comprueba si dos objetos especificados hacen referencia al mismo objeto
            y produce una excepción si ambas entradas no hacen referencia al mismo objeto.
            </summary>
            <param name="expected">
            Primer objeto para comparar. Este es el valor que la prueba espera.
            </param>
            <param name="actual">
            Segundo objeto para comparar. Este es el valor generado por el código sometido a prueba.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> does not refer to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreSame(System.Object,System.Object,System.String)">
            <summary>
            Comprueba si dos objetos especificados hacen referencia al mismo objeto
            y produce una excepción si ambas entradas no hacen referencia al mismo objeto.
            </summary>
            <param name="expected">
            Primer objeto para comparar. Este es el valor que la prueba espera.
            </param>
            <param name="actual">
            Segundo objeto para comparar. Este es el valor generado por el código sometido a prueba.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="actual"/>
            no es igual que <paramref name="expected"/>. El mensaje se muestra
            en los resultados de las pruebas.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> does not refer to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreSame(System.Object,System.Object,System.String,System.Object[])">
            <summary>
            Comprueba si dos objetos especificados hacen referencia al mismo objeto
            y produce una excepción si ambas entradas no hacen referencia al mismo objeto.
            </summary>
            <param name="expected">
            Primer objeto para comparar. Este es el valor que la prueba espera.
            </param>
            <param name="actual">
            Segundo objeto para comparar. Este es el valor generado por el código sometido a prueba.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="actual"/>
            no es igual que <paramref name="expected"/>. El mensaje se muestra
            en los resultados de las pruebas.
            </param>
            <param name="parameters">
            Matriz de parámetros que se usa al formatear <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> does not refer to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotSame(System.Object,System.Object)">
            <summary>
            Comprueba si dos objetos especificados hacen referencia a objetos diferentes
            y produce una excepción si ambas entradas hacen referencia al mismo objeto.
            </summary>
            <param name="notExpected">
            Primer objeto para comparar. Este es el valor que la prueba espera que no
            coincida con <paramref name="actual"/>.
            </param>
            <param name="actual">
            Segundo objeto para comparar. Este es el valor generado por el código sometido a prueba.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> refers to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotSame(System.Object,System.Object,System.String)">
            <summary>
            Comprueba si dos objetos especificados hacen referencia a objetos diferentes
            y produce una excepción si ambas entradas hacen referencia al mismo objeto.
            </summary>
            <param name="notExpected">
            Primer objeto para comparar. Este es el valor que la prueba espera que no
            coincida con <paramref name="actual"/>.
            </param>
            <param name="actual">
            Segundo objeto para comparar. Este es el valor generado por el código sometido a prueba.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="actual"/>
            es igual que <paramref name="notExpected"/>. El mensaje se muestra en
            los resultados de las pruebas.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> refers to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotSame(System.Object,System.Object,System.String,System.Object[])">
            <summary>
            Comprueba si dos objetos especificados hacen referencia a objetos diferentes
            y produce una excepción si ambas entradas hacen referencia al mismo objeto.
            </summary>
            <param name="notExpected">
            Primer objeto para comparar. Este es el valor que la prueba espera que no
            coincida con <paramref name="actual"/>.
            </param>
            <param name="actual">
            Segundo objeto para comparar. Este es el valor generado por el código sometido a prueba.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="actual"/>
            es igual que <paramref name="notExpected"/>. El mensaje se muestra en
            los resultados de las pruebas.
            </param>
            <param name="parameters">
            Matriz de parámetros que se usa al formatear <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> refers to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual``1(``0,``0)">
            <summary>
            Comprueba si dos valores especificados son iguales y produce una excepción
            si no lo son. Los tipos numéricos distintos se tratan
            como diferentes aunque sus valores lógicos sean iguales. 42L no es igual que 42.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="expected">
            Primer valor para comparar. Este es el valor que la prueba espera.
            </param>
            <param name="actual">
            Segundo valor para comparar. Este es el valor generado por el código sometido a prueba.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual``1(``0,``0,System.String)">
            <summary>
            Comprueba si dos valores especificados son iguales y produce una excepción
            si no lo son. Los tipos numéricos distintos se tratan
            como diferentes aunque sus valores lógicos sean iguales. 42L no es igual que 42.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="expected">
            Primer valor para comparar. Este es el valor que la prueba espera.
            </param>
            <param name="actual">
            Segundo valor para comparar. Este es el valor generado por el código sometido a prueba.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="actual"/>
            no es igual a <paramref name="expected"/>. El mensaje se muestra en
            los resultados de las pruebas.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual``1(``0,``0,System.String,System.Object[])">
            <summary>
            Comprueba si dos valores especificados son iguales y produce una excepción
            si no lo son. Los tipos numéricos distintos se tratan
            como diferentes aunque sus valores lógicos sean iguales. 42L no es igual que 42.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="expected">
            Primer valor para comparar. Este es el valor que la prueba espera.
            </param>
            <param name="actual">
            Segundo valor para comparar. Este es el valor generado por el código sometido a prueba.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="actual"/>
            no es igual a <paramref name="expected"/>. El mensaje se muestra en
            los resultados de las pruebas.
            </param>
            <param name="parameters">
            Matriz de parámetros que se usa al formatear <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual``1(``0,``0)">
            <summary>
            Comprueba si dos valores especificados son distintos y produce una excepción
            si son iguales. Los tipos numéricos distintos se tratan
            como diferentes aunque sus valores lógicos sean iguales. 42L no es igual que 42.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="notExpected">
            Primer valor para comparar. Este es el valor que la prueba espera que no
            coincida con <paramref name="actual"/>.
            </param>
            <param name="actual">
            Segundo valor para comparar. Este es el valor generado por el código sometido a prueba.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual``1(``0,``0,System.String)">
            <summary>
            Comprueba si dos valores especificados son distintos y produce una excepción
            si son iguales. Los tipos numéricos distintos se tratan
            como diferentes aunque sus valores lógicos sean iguales. 42L no es igual que 42.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="notExpected">
            Primer valor para comparar. Este es el valor que la prueba espera que no
            coincida con <paramref name="actual"/>.
            </param>
            <param name="actual">
            Segundo valor para comparar. Este es el valor generado por el código sometido a prueba.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="actual"/>
            es igual a <paramref name="notExpected"/>. El mensaje se muestra en
            los resultados de las pruebas.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual``1(``0,``0,System.String,System.Object[])">
            <summary>
            Comprueba si dos valores especificados son distintos y produce una excepción
            si son iguales. Los tipos numéricos distintos se tratan
            como diferentes aunque sus valores lógicos sean iguales. 42L no es igual que 42.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="notExpected">
            Primer valor para comparar. Este es el valor que la prueba espera que no
            coincida con <paramref name="actual"/>.
            </param>
            <param name="actual">
            Segundo valor para comparar. Este es el valor generado por el código sometido a prueba.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="actual"/>
            es igual a <paramref name="notExpected"/>. El mensaje se muestra en
            los resultados de las pruebas.
            </param>
            <param name="parameters">
            Matriz de parámetros que se usa al formatear <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Object,System.Object)">
            <summary>
            Comprueba si dos objetos especificados son iguales y produce una excepción
            si no lo son. Los tipos numéricos distintos se tratan
            como tipos diferentes aunque sus valores lógicos sean iguales. 42L no es igual que 42.
            </summary>
            <param name="expected">
            Primer objeto para comparar. Este es el objeto que la prueba espera.
            </param>
            <param name="actual">
            Segundo objeto para comparar. Este es el objeto generado por el código sometido a prueba.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Object,System.Object,System.String)">
            <summary>
            Comprueba si dos objetos especificados son iguales y produce una excepción
            si no lo son. Los tipos numéricos distintos se tratan
            como tipos diferentes aunque sus valores lógicos sean iguales. 42L no es igual que 42.
            </summary>
            <param name="expected">
            Primer objeto para comparar. Este es el objeto que la prueba espera.
            </param>
            <param name="actual">
            Segundo objeto para comparar. Este es el objeto generado por el código sometido a prueba.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="actual"/>
            no es igual a <paramref name="expected"/>. El mensaje se muestra en
            los resultados de las pruebas.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Object,System.Object,System.String,System.Object[])">
            <summary>
            Comprueba si dos objetos especificados son iguales y produce una excepción
            si no lo son. Los tipos numéricos distintos se tratan
            como tipos diferentes aunque sus valores lógicos sean iguales. 42L no es igual que 42.
            </summary>
            <param name="expected">
            Primer objeto para comparar. Este es el objeto que la prueba espera.
            </param>
            <param name="actual">
            Segundo objeto para comparar. Este es el objeto generado por el código sometido a prueba.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="actual"/>
            no es igual a <paramref name="expected"/>. El mensaje se muestra en
            los resultados de las pruebas.
            </param>
            <param name="parameters">
            Matriz de parámetros que se usa al formatear <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Object,System.Object)">
            <summary>
            Comprueba si dos objetos especificados son distintos y produce una excepción
            si lo son. Los tipos numéricos distintos se tratan
            como tipos diferentes aunque sus valores lógicos sean iguales. 42L no es igual que 42.
            </summary>
            <param name="notExpected">
            Primer objeto para comparar. Este es el valor que la prueba espera que no
            coincida con <paramref name="actual"/>.
            </param>
            <param name="actual">
            Segundo objeto para comparar. Este es el objeto generado por el código sometido a prueba.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Object,System.Object,System.String)">
            <summary>
            Comprueba si dos objetos especificados son distintos y produce una excepción
            si lo son. Los tipos numéricos distintos se tratan
            como tipos diferentes aunque sus valores lógicos sean iguales. 42L no es igual que 42.
            </summary>
            <param name="notExpected">
            Primer objeto para comparar. Este es el valor que la prueba espera que no
            coincida con <paramref name="actual"/>.
            </param>
            <param name="actual">
            Segundo objeto para comparar. Este es el objeto generado por el código sometido a prueba.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="actual"/>
            es igual a <paramref name="notExpected"/>. El mensaje se muestra en
            los resultados de las pruebas.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Object,System.Object,System.String,System.Object[])">
            <summary>
            Comprueba si dos objetos especificados son distintos y produce una excepción
            si lo son. Los tipos numéricos distintos se tratan
            como tipos diferentes aunque sus valores lógicos sean iguales. 42L no es igual que 42.
            </summary>
            <param name="notExpected">
            Primer objeto para comparar. Este es el valor que la prueba espera que no
            coincida con <paramref name="actual"/>.
            </param>
            <param name="actual">
            Segundo objeto para comparar. Este es el objeto generado por el código sometido a prueba.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="actual"/>
            es igual a <paramref name="notExpected"/>. El mensaje se muestra en
            los resultados de las pruebas.
            </param>
            <param name="parameters">
            Matriz de parámetros que se usa al formatear <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Single,System.Single,System.Single)">
            <summary>
            Comprueba si los valores float especificados son iguales y produce una excepción
            si no lo son.
            </summary>
            <param name="expected">
            Primer valor float para comparar. Este es el valor float que la prueba espera.
            </param>
            <param name="actual">
            Segundo valor float para comparar. Este es el valor float generado por el código sometido a prueba.
            </param>
            <param name="delta">
            Precisión requerida. Se iniciará una excepción solamente si
            <paramref name="actual"/> difiere de <paramref name="expected"/>
            por más de <paramref name="delta"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Single,System.Single,System.Single,System.String)">
            <summary>
            Comprueba si los valores float especificados son iguales y produce una excepción
            si no lo son.
            </summary>
            <param name="expected">
            Primer valor float para comparar. Este es el valor float que la prueba espera.
            </param>
            <param name="actual">
            Segundo valor float para comparar. Este es el valor float generado por el código sometido a prueba.
            </param>
            <param name="delta">
            Precisión requerida. Se iniciará una excepción solamente si
            <paramref name="actual"/> difiere de <paramref name="expected"/>
            por más de <paramref name="delta"/>.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="actual"/>
            difiere de <paramref name="expected"/> por más de
            <paramref name="delta"/>. El mensaje se muestra en los resultados de las pruebas.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Single,System.Single,System.Single,System.String,System.Object[])">
            <summary>
            Comprueba si los valores float especificados son iguales y produce una excepción
            si no lo son.
            </summary>
            <param name="expected">
            Primer valor float para comparar. Este es el valor float que la prueba espera.
            </param>
            <param name="actual">
            Segundo valor float para comparar. Este es el valor float generado por el código sometido a prueba.
            </param>
            <param name="delta">
            Precisión requerida. Se iniciará una excepción solamente si
            <paramref name="actual"/> difiere de <paramref name="expected"/>
            por más de <paramref name="delta"/>.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="actual"/>
            difiere de <paramref name="expected"/> por más de
            <paramref name="delta"/>. El mensaje se muestra en los resultados de las pruebas.
            </param>
            <param name="parameters">
            Matriz de parámetros que se usa al formatear <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Single,System.Single,System.Single)">
            <summary>
            Comprueba si los valores float especificados son distintos y produce una excepción
            si son iguales.
            </summary>
            <param name="notExpected">
            Primer valor float para comparar. Este es el valor float que la prueba espera que no
            coincida con <paramref name="actual"/>.
            </param>
            <param name="actual">
            Segundo valor float para comparar. Este es el valor float generado por el código sometido a prueba.
            </param>
            <param name="delta">
            Precisión requerida. Se iniciará una excepción solamente si
            <paramref name="actual"/> difiere de <paramref name="notExpected"/>
            por un máximo de <paramref name="delta"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Single,System.Single,System.Single,System.String)">
            <summary>
            Comprueba si los valores float especificados son distintos y produce una excepción
            si son iguales.
            </summary>
            <param name="notExpected">
            Primer valor float para comparar. Este es el valor float que la prueba espera que no
            coincida con <paramref name="actual"/>.
            </param>
            <param name="actual">
            Segundo valor float para comparar. Este es el valor float generado por el código sometido a prueba.
            </param>
            <param name="delta">
            Precisión requerida. Se iniciará una excepción solamente si
            <paramref name="actual"/> difiere de <paramref name="notExpected"/>
            por un máximo de <paramref name="delta"/>.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="actual"/>
            es igual a <paramref name="notExpected"/> o difiere por menos de
            <paramref name="delta"/>. El mensaje se muestra en los resultados de las pruebas.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Single,System.Single,System.Single,System.String,System.Object[])">
            <summary>
            Comprueba si los valores float especificados son distintos y produce una excepción
            si son iguales.
            </summary>
            <param name="notExpected">
            Primer valor float para comparar. Este es el valor float que la prueba espera que no
            coincida con <paramref name="actual"/>.
            </param>
            <param name="actual">
            Segundo valor float para comparar. Este es el valor float generado por el código sometido a prueba.
            </param>
            <param name="delta">
            Precisión requerida. Se iniciará una excepción solamente si
            <paramref name="actual"/> difiere de <paramref name="notExpected"/>
            por un máximo de <paramref name="delta"/>.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="actual"/>
            es igual a <paramref name="notExpected"/> o difiere por menos de
            <paramref name="delta"/>. El mensaje se muestra en los resultados de las pruebas.
            </param>
            <param name="parameters">
            Matriz de parámetros que se usa al formatear <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Double,System.Double,System.Double)">
            <summary>
            Comprueba si los valores double especificados son iguales y produce una excepción
            si no lo son.
            </summary>
            <param name="expected">
            Primer valor double para comparar. Este es el valor double que la prueba espera.
            </param>
            <param name="actual">
            Segundo valor double para comparar. Este es el valor double generado por el código sometido a prueba.
            </param>
            <param name="delta">
            Precisión requerida. Se iniciará una excepción solamente si
            <paramref name="actual"/> difiere de <paramref name="expected"/>
            por más de <paramref name="delta"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Double,System.Double,System.Double,System.String)">
            <summary>
            Comprueba si los valores double especificados son iguales y produce una excepción
            si no lo son.
            </summary>
            <param name="expected">
            Primer valor double para comparar. Este es el valor double que la prueba espera.
            </param>
            <param name="actual">
            Segundo valor double para comparar. Este es el valor double generado por el código sometido a prueba.
            </param>
            <param name="delta">
            Precisión requerida. Se iniciará una excepción solamente si
            <paramref name="actual"/> difiere de <paramref name="expected"/>
            por más de <paramref name="delta"/>.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="actual"/>
            difiere de <paramref name="expected"/> por más de
            <paramref name="delta"/>. El mensaje se muestra en los resultados de las pruebas.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Double,System.Double,System.Double,System.String,System.Object[])">
            <summary>
            Comprueba si los valores double especificados son iguales y produce una excepción
            si no lo son.
            </summary>
            <param name="expected">
            Primer valor double para comparar. Este es el valor double que la prueba espera.
            </param>
            <param name="actual">
            Segundo valor double para comparar. Este es el valor double generado por el código sometido a prueba.
            </param>
            <param name="delta">
            Precisión requerida. Se iniciará una excepción solamente si
            <paramref name="actual"/> difiere de <paramref name="expected"/>
            por más de <paramref name="delta"/>.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="actual"/>
            difiere de <paramref name="expected"/> por más de
            <paramref name="delta"/>. El mensaje se muestra en los resultados de las pruebas.
            </param>
            <param name="parameters">
            Matriz de parámetros que se usa al formatear <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Double,System.Double,System.Double)">
            <summary>
            Comprueba si los valores double especificados son distintos y produce una excepción
            si son iguales.
            </summary>
            <param name="notExpected">
            Primer valor double para comparar. Este es el valor double que la prueba espera que no
            coincida con <paramref name="actual"/>.
            </param>
            <param name="actual">
            Segundo valor double para comparar. Este es el valor double generado por el código sometido a prueba.
            </param>
            <param name="delta">
            Precisión requerida. Se iniciará una excepción solamente si
            <paramref name="actual"/> difiere de <paramref name="notExpected"/>
            por un máximo de <paramref name="delta"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Double,System.Double,System.Double,System.String)">
            <summary>
            Comprueba si los valores double especificados son distintos y produce una excepción
            si son iguales.
            </summary>
            <param name="notExpected">
            Primer valor double para comparar. Este es el valor double que la prueba espera que no
            coincida con <paramref name="actual"/>.
            </param>
            <param name="actual">
            Segundo valor double para comparar. Este es el valor double generado por el código sometido a prueba.
            </param>
            <param name="delta">
            Precisión requerida. Se iniciará una excepción solamente si
            <paramref name="actual"/> difiere de <paramref name="notExpected"/>
            por un máximo de <paramref name="delta"/>.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="actual"/>
            es igual a <paramref name="notExpected"/> o difiere por menos de
            <paramref name="delta"/>. El mensaje se muestra en los resultados de las pruebas.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Double,System.Double,System.Double,System.String,System.Object[])">
            <summary>
            Comprueba si los valores double especificados son distintos y produce una excepción
            si son iguales.
            </summary>
            <param name="notExpected">
            Primer valor double para comparar. Este es el valor double que la prueba espera que no
            coincida con <paramref name="actual"/>.
            </param>
            <param name="actual">
            Segundo valor double para comparar. Este es el valor double generado por el código sometido a prueba.
            </param>
            <param name="delta">
            Precisión requerida. Se iniciará una excepción solamente si
            <paramref name="actual"/> difiere de <paramref name="notExpected"/>
            por un máximo de <paramref name="delta"/>.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="actual"/>
            es igual a <paramref name="notExpected"/> o difiere por menos de
            <paramref name="delta"/>. El mensaje se muestra en los resultados de las pruebas.
            </param>
            <param name="parameters">
            Matriz de parámetros que se usa al formatear <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean)">
            <summary>
            Comprueba si las cadenas especificadas son iguales y produce una excepción
            si no lo son. Se usa la referencia cultural invariable para la comparación.
            </summary>
            <param name="expected">
            Primera cadena para comparar. Esta es la cadena que la prueba espera.
            </param>
            <param name="actual">
            Segunda cadena para comparar. Esta es la cadena generada por el código sometido a prueba.
            </param>
            <param name="ignoreCase">
            Valor booleano que indica una comparación donde se distingue o no mayúsculas de minúsculas. (true
            indica una comparación que no distingue mayúsculas de minúsculas).
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.String)">
            <summary>
            Comprueba si las cadenas especificadas son iguales y produce una excepción
            si no lo son. Se usa la referencia cultural invariable para la comparación.
            </summary>
            <param name="expected">
            Primera cadena para comparar. Esta es la cadena que la prueba espera.
            </param>
            <param name="actual">
            Segunda cadena para comparar. Esta es la cadena generada por el código sometido a prueba.
            </param>
            <param name="ignoreCase">
            Valor booleano que indica una comparación donde se distingue o no mayúsculas de minúsculas. (true
            indica una comparación que no distingue mayúsculas de minúsculas).
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="actual"/>
            no es igual a <paramref name="expected"/>. El mensaje se muestra en
            los resultados de las pruebas.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.String,System.Object[])">
            <summary>
            Comprueba si las cadenas especificadas son iguales y produce una excepción
            si no lo son. Se usa la referencia cultural invariable para la comparación.
            </summary>
            <param name="expected">
            Primera cadena para comparar. Esta es la cadena que la prueba espera.
            </param>
            <param name="actual">
            Segunda cadena para comparar. Esta es la cadena generada por el código sometido a prueba.
            </param>
            <param name="ignoreCase">
            Valor booleano que indica una comparación donde se distingue o no mayúsculas de minúsculas. (true
            indica una comparación que no distingue mayúsculas de minúsculas).
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="actual"/>
            no es igual a <paramref name="expected"/>. El mensaje se muestra en
            los resultados de las pruebas.
            </param>
            <param name="parameters">
            Matriz de parámetros que se usa al formatear <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo)">
            <summary>
            Comprueba si las cadenas especificadas son iguales y produce una excepción
            si no lo son.
            </summary>
            <param name="expected">
            Primera cadena para comparar. Esta es la cadena que la prueba espera.
            </param>
            <param name="actual">
            Segunda cadena para comparar. Esta es la cadena generada por el código sometido a prueba.
            </param>
            <param name="ignoreCase">
            Valor booleano que indica una comparación donde se distingue o no mayúsculas de minúsculas. (true
            indica una comparación que no distingue mayúsculas de minúsculas).
            </param>
            <param name="culture">
            Objeto CultureInfo que proporciona información de comparación específica de la referencia cultural.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo,System.String)">
            <summary>
            Comprueba si las cadenas especificadas son iguales y produce una excepción
            si no lo son.
            </summary>
            <param name="expected">
            Primera cadena para comparar. Esta es la cadena que la prueba espera.
            </param>
            <param name="actual">
            Segunda cadena para comparar. Esta es la cadena generada por el código sometido a prueba.
            </param>
            <param name="ignoreCase">
            Valor booleano que indica una comparación donde se distingue o no mayúsculas de minúsculas. (true
            indica una comparación que no distingue mayúsculas de minúsculas).
            </param>
            <param name="culture">
            Objeto CultureInfo que proporciona información de comparación específica de la referencia cultural.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="actual"/>
            no es igual a <paramref name="expected"/>. El mensaje se muestra en
            los resultados de las pruebas.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo,System.String,System.Object[])">
            <summary>
            Comprueba si las cadenas especificadas son iguales y produce una excepción
            si no lo son.
            </summary>
            <param name="expected">
            Primera cadena para comparar. Esta es la cadena que la prueba espera.
            </param>
            <param name="actual">
            Segunda cadena para comparar. Esta es la cadena generada por el código sometido a prueba.
            </param>
            <param name="ignoreCase">
            Valor booleano que indica una comparación donde se distingue o no mayúsculas de minúsculas. (true
            indica una comparación que no distingue mayúsculas de minúsculas).
            </param>
            <param name="culture">
            Objeto CultureInfo que proporciona información de comparación específica de la referencia cultural.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="actual"/>
            no es igual a <paramref name="expected"/>. El mensaje se muestra en
            los resultados de las pruebas.
            </param>
            <param name="parameters">
            Matriz de parámetros que se usa al formatear <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean)">
            <summary>
            Comprueba si las cadenas especificadas son distintas y produce una excepción
            si son iguales. Para la comparación, se usa la referencia cultural invariable.
            </summary>
            <param name="notExpected">
            Primera cadena para comparar. Esta es la cadena que la prueba espera que no
            coincida con <paramref name="actual"/>.
            </param>
            <param name="actual">
            Segunda cadena para comparar. Esta es la cadena generada por el código sometido a prueba.
            </param>
            <param name="ignoreCase">
            Valor booleano que indica una comparación donde se distingue o no mayúsculas de minúsculas. (true
            indica una comparación que no distingue mayúsculas de minúsculas).
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.String)">
            <summary>
            Comprueba si las cadenas especificadas son distintas y produce una excepción
            si son iguales. Para la comparación, se usa la referencia cultural invariable.
            </summary>
            <param name="notExpected">
            Primera cadena para comparar. Esta es la cadena que la prueba espera que no
            coincida con <paramref name="actual"/>.
            </param>
            <param name="actual">
            Segunda cadena para comparar. Esta es la cadena generada por el código sometido a prueba.
            </param>
            <param name="ignoreCase">
            Valor booleano que indica una comparación donde se distingue o no mayúsculas de minúsculas. (true
            indica una comparación que no distingue mayúsculas de minúsculas).
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="actual"/>
            es igual a <paramref name="notExpected"/>. El mensaje se muestra en
            los resultados de las pruebas.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.String,System.Object[])">
            <summary>
            Comprueba si las cadenas especificadas son distintas y produce una excepción
            si son iguales. Para la comparación, se usa la referencia cultural invariable.
            </summary>
            <param name="notExpected">
            Primera cadena para comparar. Esta es la cadena que la prueba espera que no
            coincida con <paramref name="actual"/>.
            </param>
            <param name="actual">
            Segunda cadena para comparar. Esta es la cadena generada por el código sometido a prueba.
            </param>
            <param name="ignoreCase">
            Valor booleano que indica una comparación donde se distingue o no mayúsculas de minúsculas. (true
            indica una comparación que no distingue mayúsculas de minúsculas).
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="actual"/>
            es igual a <paramref name="notExpected"/>. El mensaje se muestra en
            los resultados de las pruebas.
            </param>
            <param name="parameters">
            Matriz de parámetros que se usa al formatear <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo)">
            <summary>
            Comprueba si las cadenas especificadas son distintas y produce una excepción
            si son iguales.
            </summary>
            <param name="notExpected">
            Primera cadena para comparar. Esta es la cadena que la prueba espera que no
            coincida con <paramref name="actual"/>.
            </param>
            <param name="actual">
            Segunda cadena para comparar. Esta es la cadena generada por el código sometido a prueba.
            </param>
            <param name="ignoreCase">
            Valor booleano que indica una comparación donde se distingue o no mayúsculas de minúsculas. (true
            indica una comparación que no distingue mayúsculas de minúsculas).
            </param>
            <param name="culture">
            Objeto CultureInfo que proporciona información de comparación específica de la referencia cultural.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo,System.String)">
            <summary>
            Comprueba si las cadenas especificadas son distintas y produce una excepción
            si son iguales.
            </summary>
            <param name="notExpected">
            Primera cadena para comparar. Esta es la cadena que la prueba espera que no
            coincida con <paramref name="actual"/>.
            </param>
            <param name="actual">
            Segunda cadena para comparar. Esta es la cadena generada por el código sometido a prueba.
            </param>
            <param name="ignoreCase">
            Valor booleano que indica una comparación donde se distingue o no mayúsculas de minúsculas. (true
            indica una comparación que no distingue mayúsculas de minúsculas).
            </param>
            <param name="culture">
            Objeto CultureInfo que proporciona información de comparación específica de la referencia cultural.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="actual"/>
            es igual a <paramref name="notExpected"/>. El mensaje se muestra en
            los resultados de las pruebas.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo,System.String,System.Object[])">
            <summary>
            Comprueba si las cadenas especificadas son distintas y produce una excepción
            si son iguales.
            </summary>
            <param name="notExpected">
            Primera cadena para comparar. Esta es la cadena que la prueba espera que no
            coincida con <paramref name="actual"/>.
            </param>
            <param name="actual">
            Segunda cadena para comparar. Esta es la cadena generada por el código sometido a prueba.
            </param>
            <param name="ignoreCase">
            Valor booleano que indica una comparación donde se distingue o no mayúsculas de minúsculas. (true
            indica una comparación que no distingue mayúsculas de minúsculas).
            </param>
            <param name="culture">
            Objeto CultureInfo que proporciona información de comparación específica de la referencia cultural.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="actual"/>
            es igual a <paramref name="notExpected"/>. El mensaje se muestra en
            los resultados de las pruebas.
            </param>
            <param name="parameters">
            Matriz de parámetros que se usa al formatear <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsInstanceOfType(System.Object,System.Type)">
            <summary>
            Comprueba si el objeto especificado es una instancia del tipo
            esperado y produce una excepción si el tipo esperado no se encuentra en
            la jerarquía de herencia del objeto.
            </summary>
            <param name="value">
            El objeto que la prueba espera que sea del tipo especificado.
            </param>
            <param name="expectedType">
            Tipo esperado de <paramref name="value"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsInstanceOfType(System.Object,System.Type,System.String)">
            <summary>
            Comprueba si el objeto especificado es una instancia del tipo
            esperado y produce una excepción si el tipo esperado no se encuentra en
            la jerarquía de herencia del objeto.
            </summary>
            <param name="value">
            El objeto que la prueba espera que sea del tipo especificado.
            </param>
            <param name="expectedType">
            Tipo esperado de <paramref name="value"/>.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="value"/>
            no es una instancia de <paramref name="expectedType"/>. El mensaje se
            muestra en los resultados de las pruebas.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsInstanceOfType(System.Object,System.Type,System.String,System.Object[])">
            <summary>
            Comprueba si el objeto especificado es una instancia del tipo
            esperado y produce una excepción si el tipo esperado no se encuentra en
            la jerarquía de herencia del objeto.
            </summary>
            <param name="value">
            El objeto que la prueba espera que sea del tipo especificado.
            </param>
            <param name="expectedType">
            Tipo esperado de <paramref name="value"/>.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="value"/>
            no es una instancia de <paramref name="expectedType"/>. El mensaje se
            muestra en los resultados de las pruebas.
            </param>
            <param name="parameters">
            Matriz de parámetros que se usa al formatear <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotInstanceOfType(System.Object,System.Type)">
            <summary>
            Comprueba si el objeto especificado no es una instancia del tipo
            incorrecto y produce una excepción si el tipo especificado se encuentra en la
            jerarquía de herencia del objeto.
            </summary>
            <param name="value">
            El objeto que la prueba espera que no sea del tipo especificado.
            </param>
            <param name="wrongType">
            El tipo que <paramref name="value"/> no debe tener.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null and
            <paramref name="wrongType"/> is in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotInstanceOfType(System.Object,System.Type,System.String)">
            <summary>
            Comprueba si el objeto especificado no es una instancia del tipo
            incorrecto y produce una excepción si el tipo especificado se encuentra en la
            jerarquía de herencia del objeto.
            </summary>
            <param name="value">
            El objeto que la prueba espera que no sea del tipo especificado.
            </param>
            <param name="wrongType">
            El tipo que <paramref name="value"/> no debe tener.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="value"/>
            es una instancia de <paramref name="wrongType"/>. El mensaje se muestra
            en los resultados de las pruebas.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null and
            <paramref name="wrongType"/> is in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotInstanceOfType(System.Object,System.Type,System.String,System.Object[])">
            <summary>
            Comprueba si el objeto especificado no es una instancia del tipo
            incorrecto y produce una excepción si el tipo especificado se encuentra en la
            jerarquía de herencia del objeto.
            </summary>
            <param name="value">
            El objeto que la prueba espera que no sea del tipo especificado.
            </param>
            <param name="wrongType">
            El tipo que <paramref name="value"/> no debe tener.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="value"/>
            es una instancia de <paramref name="wrongType"/>. El mensaje se muestra
            en los resultados de las pruebas.
            </param>
            <param name="parameters">
            Matriz de parámetros que se usa al formatear <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null and
            <paramref name="wrongType"/> is in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Fail">
            <summary>
            Produce una excepción AssertFailedException.
            </summary>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Fail(System.String)">
            <summary>
            Produce una excepción AssertFailedException.
            </summary>
            <param name="message">
            Mensaje que se va a incluir en la excepción. El mensaje se muestra en los
            resultados de las pruebas.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Fail(System.String,System.Object[])">
            <summary>
            Produce una excepción AssertFailedException.
            </summary>
            <param name="message">
            Mensaje que se va a incluir en la excepción. El mensaje se muestra en los
            resultados de las pruebas.
            </param>
            <param name="parameters">
            Matriz de parámetros que se usa al formatear <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Inconclusive">
            <summary>
            Produce una excepción AssertInconclusiveException.
            </summary>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Inconclusive(System.String)">
            <summary>
            Produce una excepción AssertInconclusiveException.
            </summary>
            <param name="message">
            Mensaje que se va a incluir en la excepción. El mensaje se muestra en los
            resultados de las pruebas.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Inconclusive(System.String,System.Object[])">
            <summary>
            Produce una excepción AssertInconclusiveException.
            </summary>
            <param name="message">
            Mensaje que se va a incluir en la excepción. El mensaje se muestra en los
            resultados de las pruebas.
            </param>
            <param name="parameters">
            Matriz de parámetros que se usa al formatear <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Equals(System.Object,System.Object)">
            <summary>
            Las sobrecargas de igualdad estáticas se usan para comparar la igualdad de referencia de
            instancias de dos tipos. Este método <b>no</b> debe usarse para comparar la igualdad de dos instancias.
            Este objeto se devolverá <b>siempre</b> con Assert.Fail. Utilice 
            Assert.AreEqual y las sobrecargas asociadas en pruebas unitarias.
            </summary>
            <param name="objA"> Objeto A </param>
            <param name="objB"> Objeto B </param>
            <returns> False, siempre. </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Action)">
            <summary>
            Comprueba si el código especificado por el delegado <paramref name="action"/> produce exactamente la excepción dada de tipo <typeparamref name="T"/> (y no de un tipo derivado)
            y devuelve una excepción
            <code>
            AssertFailedException
            </code>
            si el código no produce la excepción dada o produce otra de un tipo que no sea <typeparamref name="T"/>.
            </summary>
            <param name="action">
            Delegado para el código que se va a probar y que se espera que inicie una excepción.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            El tipo de excepción que se espera que se inicie.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Action,System.String)">
            <summary>
            Comprueba si el código especificado por el delegado <paramref name="action"/> produce exactamente la excepción dada de tipo <typeparamref name="T"/> (y no de un tipo derivado)
            y devuelve una excepción
            <code>
            AssertFailedException
            </code>
            si el código no produce la excepción dada o produce otra de un tipo que no sea <typeparamref name="T"/>.
            </summary>
            <param name="action">
            Delegado a código que se va a probar y que se espera que inicie una excepción.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="action"/>
            no inicia una excepción de tipo <typeparamref name="T"/>.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            El tipo de excepción que se espera que se inicie.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Func{System.Object})">
            <summary>
            Comprueba si el código especificado por el delegado <paramref name="action"/> produce exactamente la excepción dada de tipo <typeparamref name="T"/> (y no de un tipo derivado)
            y devuelve una excepción
            <code>
            AssertFailedException
            </code>
            si el código no produce la excepción dada o produce otra de un tipo que no sea <typeparamref name="T"/>.
            </summary>
            <param name="action">
            Delegado a código que se va a probar y que se espera que inicie una excepción.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            El tipo de excepción que se espera que se inicie.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Func{System.Object},System.String)">
            <summary>
            Comprueba si el código especificado por el delegado <paramref name="action"/> produce exactamente la excepción dada de tipo <typeparamref name="T"/> (y no de un tipo derivado)
            y devuelve una excepción
            <code>
            AssertFailedException
            </code>
            si el código no produce la excepción dada o produce otra de un tipo que no sea <typeparamref name="T"/>.
            </summary>
            <param name="action">
            Delegado a código que se va a probar y que se espera que inicie una excepción.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="action"/>
            no inicia una excepción de tipo <typeparamref name="T"/>.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            El tipo de excepción que se espera que se inicie.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Func{System.Object},System.String,System.Object[])">
            <summary>
            Comprueba si el código especificado por el delegado <paramref name="action"/> produce exactamente la excepción dada de tipo <typeparamref name="T"/> (y no de un tipo derivado)
            y devuelve una excepción
            <code>
            AssertFailedException
            </code>
            si el código no produce la excepción dada o produce otra de un tipo que no sea <typeparamref name="T"/>.
            </summary>
            <param name="action">
            Delegado a código que se va a probar y que se espera que inicie una excepción.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="action"/>
            no inicia una excepción de tipo <typeparamref name="T"/>.
            </param>
            <param name="parameters">
            Matriz de parámetros que se usa al formatear <paramref name="message"/>.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throw exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            El tipo de excepción que se espera que se inicie.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Action,System.String,System.Object[])">
            <summary>
            Comprueba si el código especificado por el delegado <paramref name="action"/> produce exactamente la excepción dada de tipo <typeparamref name="T"/> (y no de un tipo derivado)
            y devuelve una excepción
            <code>
            AssertFailedException
            </code>
            si el código no produce la excepción dada o produce otra de un tipo que no sea <typeparamref name="T"/>.
            </summary>
            <param name="action">
            Delegado a código que se va a probar y que se espera que inicie una excepción.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="action"/>
            no inicia una excepción de tipo <typeparamref name="T"/>.
            </param>
            <param name="parameters">
            Matriz de parámetros que se usa al formatear <paramref name="message"/>.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            El tipo de excepción que se espera que se inicie.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsExceptionAsync``1(System.Func{System.Threading.Tasks.Task})">
            <summary>
            Comprueba si el código especificado por el delegado <paramref name="action"/> produce exactamente la excepción dada de tipo <typeparamref name="T"/> (y no de un tipo derivado)
            y devuelve una excepción
            <code>
            AssertFailedException
            </code>
            si el código no produce la excepción dada o produce otra de un tipo que no sea <typeparamref name="T"/>.
            </summary>
            <param name="action">
            Delegado para el código que se va a probar y que se espera que inicie una excepción.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
 <see cref="T:System.Threading.Tasks.Task"/> que ejecuta el delegado.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsExceptionAsync``1(System.Func{System.Threading.Tasks.Task},System.String)">
            <summary>
            Comprueba si el código especificado por el delegado <paramref name="action"/> produce exactamente la excepción dada de tipo <typeparamref name="T"/> (y no de un tipo derivado)
            y devuelve una excepción <code>AssertFailedException</code> si el código no produce la excepción dada o produce otra de un tipo que no sea <typeparamref name="T"/>.
            </summary>
            <param name="action">Delegado para el código que se va a probar y que se espera que inicie una excepción.</param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="action"/>
            no inicia una excepción de tipo <typeparamref name="T"/>.
            </param>
            <typeparam name="T">Type of exception expected to be thrown.</typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
 <see cref="T:System.Threading.Tasks.Task"/> que ejecuta el delegado.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsExceptionAsync``1(System.Func{System.Threading.Tasks.Task},System.String,System.Object[])">
            <summary>
            Comprueba si el código especificado por el delegado <paramref name="action"/> produce exactamente la excepción dada de tipo <typeparamref name="T"/> (y no de un tipo derivado)
            y devuelve una excepción <code>AssertFailedException</code> si el código no produce la excepción dada o produce otra de un tipo que no sea <typeparamref name="T"/>.
            </summary>
            <param name="action">Delegado para el código que se va a probar y que se espera que inicie una excepción.</param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="action"/>
            no inicia una excepción de tipo <typeparamref name="T"/>.
            </param>
            <param name="parameters">
            Matriz de parámetros que se usa al formatear <paramref name="message"/>.
            </param>
            <typeparam name="T">Type of exception expected to be thrown.</typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
 <see cref="T:System.Threading.Tasks.Task"/> que ejecuta el delegado.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ReplaceNullChars(System.String)">
            <summary>
            Reemplaza los caracteres NULL "\0" por "\\0".
            </summary>
            <param name="input">
            Cadena para buscar.
            </param>
            <returns>
            La cadena convertida con los caracteres NULL reemplazados por "\\0".
            </returns>
            <remarks>
            This is only public and still present to preserve compatibility with the V1 framework.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.HandleFail(System.String,System.String,System.Object[])">
            <summary>
            Función auxiliar que produce una excepción AssertionFailedException.
            </summary>
            <param name="assertionName">
            nombre de la aserción que inicia una excepción
            </param>
            <param name="message">
            mensaje que describe las condiciones del error de aserción
            </param>
            <param name="parameters">
            Los parámetros.
            </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.CheckParameterNotNull(System.Object,System.String,System.String,System.String,System.Object[])">
            <summary>
            Comprueba el parámetro para las condiciones válidas.
            </summary>
            <param name="param">
            El parámetro.
            </param>
            <param name="assertionName">
            Nombre de la aserción.
            </param>
            <param name="parameterName">
            nombre de parámetro
            </param>
            <param name="message">
            mensaje de la excepción de parámetro no válido
            </param>
            <param name="parameters">
            Los parámetros.
            </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ReplaceNulls(System.Object)">
            <summary>
            Convierte un objeto en cadena de forma segura, con control de los valores y caracteres NULL.
            Los valores NULL se convierten en "NULL". Los caracteres NULL se convierten en "\\0".
            </summary>
            <param name="input">
            Objeto que se va a convertir en cadena.
            </param>
            <returns>
            La cadena convertida.
            </returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert">
            <summary>
            Aserción de cadena.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.That">
            <summary>
            Obtiene la instancia de singleton de la funcionalidad CollectionAssert.
            </summary>
            <remarks>
            Users can use this to plug-in custom assertions through C# extension methods.
            For instance, the signature of a custom assertion provider could be "public static void ContainsWords(this StringAssert cusomtAssert, string value, ICollection substrings)"
            Users could then use a syntax similar to the default assertions which in this case is "StringAssert.That.ContainsWords(value, substrings);"
            More documentation is at "https://github.com/Microsoft/testfx-docs".
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Contains(System.String,System.String)">
            <summary>
            Comprueba si la cadena especificada contiene la subcadena indicada
            y produce una excepción si la subcadena no está en la
            cadena de prueba.
            </summary>
            <param name="value">
            La cadena que se espera que contenga <paramref name="substring"/>.
            </param>
            <param name="substring">
            La cadena que se espera que aparezca en <paramref name="value"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="substring"/> is not found in
            <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Contains(System.String,System.String,System.String)">
            <summary>
            Comprueba si la cadena especificada contiene la subcadena indicada
            y produce una excepción si la subcadena no está en la
            cadena de prueba.
            </summary>
            <param name="value">
            La cadena que se espera que contenga <paramref name="substring"/>.
            </param>
            <param name="substring">
            La cadena que se espera que aparezca en <paramref name="value"/>.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="substring"/>
            no se encuentra en <paramref name="value"/>. El mensaje se muestra en
            los resultados de las pruebas.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="substring"/> is not found in
            <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Contains(System.String,System.String,System.String,System.Object[])">
            <summary>
            Comprueba si la cadena especificada contiene la subcadena indicada
            y produce una excepción si la subcadena no está en la
            cadena de prueba.
            </summary>
            <param name="value">
            La cadena que se espera que contenga <paramref name="substring"/>.
            </param>
            <param name="substring">
            La cadena que se espera que aparezca en <paramref name="value"/>.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="substring"/>
            no se encuentra en <paramref name="value"/>. El mensaje se muestra en
            los resultados de las pruebas.
            </param>
            <param name="parameters">
            Matriz de parámetros que se usa al formatear <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="substring"/> is not found in
            <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.StartsWith(System.String,System.String)">
            <summary>
            Comprueba si la cadena especificada empieza por la subcadena indicada
            y produce una excepción si la cadena de prueba no empieza por la
            subcadena.
            </summary>
            <param name="value">
            Cadena que se espera que empiece por <paramref name="substring"/>.
            </param>
            <param name="substring">
            Cadena que se espera que sea un prefijo de <paramref name="value"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not begin with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.StartsWith(System.String,System.String,System.String)">
            <summary>
            Comprueba si la cadena especificada empieza por la subcadena indicada
            y produce una excepción si la cadena de prueba no empieza por la
            subcadena.
            </summary>
            <param name="value">
            Cadena que se espera que empiece por <paramref name="substring"/>.
            </param>
            <param name="substring">
            Cadena que se espera que sea un prefijo de <paramref name="value"/>.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="value"/>
            no empieza por <paramref name="substring"/>. El mensaje se
            muestra en los resultados de las pruebas.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not begin with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.StartsWith(System.String,System.String,System.String,System.Object[])">
            <summary>
            Comprueba si la cadena especificada empieza por la subcadena indicada
            y produce una excepción si la cadena de prueba no empieza por la
            subcadena.
            </summary>
            <param name="value">
            Cadena que se espera que empiece por <paramref name="substring"/>.
            </param>
            <param name="substring">
            Cadena que se espera que sea un prefijo de <paramref name="value"/>.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="value"/>
            no empieza por <paramref name="substring"/>. El mensaje se
            muestra en los resultados de las pruebas.
            </param>
            <param name="parameters">
            Matriz de parámetros que se usa al formatear <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not begin with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.EndsWith(System.String,System.String)">
            <summary>
            Comprueba si la cadena especificada termina con la subcadena indicada
            y produce una excepción si la cadena de prueba no termina con la
            subcadena.
            </summary>
            <param name="value">
            Cadena que se espera que termine con <paramref name="substring"/>.
            </param>
            <param name="substring">
            Cadena que se espera que sea un sufijo de <paramref name="value"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not end with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.EndsWith(System.String,System.String,System.String)">
            <summary>
            Comprueba si la cadena especificada termina con la subcadena indicada
            y produce una excepción si la cadena de prueba no termina con la
            subcadena.
            </summary>
            <param name="value">
            Cadena que se espera que termine con <paramref name="substring"/>.
            </param>
            <param name="substring">
            Cadena que se espera que sea un sufijo de <paramref name="value"/>.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="value"/>
            no termina con <paramref name="substring"/>. El mensaje se
            muestra en los resultados de las pruebas.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not end with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.EndsWith(System.String,System.String,System.String,System.Object[])">
            <summary>
            Comprueba si la cadena especificada termina con la subcadena indicada
            y produce una excepción si la cadena de prueba no termina con la
            subcadena.
            </summary>
            <param name="value">
            Cadena que se espera que termine con <paramref name="substring"/>.
            </param>
            <param name="substring">
            Cadena que se espera que sea un sufijo de <paramref name="value"/>.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="value"/>
            no termina con <paramref name="substring"/>. El mensaje se
            muestra en los resultados de las pruebas.
            </param>
            <param name="parameters">
            Matriz de parámetros que se usa al formatear <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not end with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Matches(System.String,System.Text.RegularExpressions.Regex)">
            <summary>
            Comprueba si la cadena especificada coincide con una expresión regular
            y produce una excepción si la cadena no coincide con la expresión.
            </summary>
            <param name="value">
            La cadena que se espera que coincida con <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            Expresión regular con la que se espera que <paramref name="value"/>
            coincida.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not match
            <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Matches(System.String,System.Text.RegularExpressions.Regex,System.String)">
            <summary>
            Comprueba si la cadena especificada coincide con una expresión regular
            y produce una excepción si la cadena no coincide con la expresión.
            </summary>
            <param name="value">
            La cadena que se espera que coincida con <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            Expresión regular con la que se espera que <paramref name="value"/>
            coincida.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="value"/>
            no coincide con <paramref name="pattern"/>. El mensaje se muestra en
            los resultados de las pruebas.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not match
            <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Matches(System.String,System.Text.RegularExpressions.Regex,System.String,System.Object[])">
            <summary>
            Comprueba si la cadena especificada coincide con una expresión regular
            y produce una excepción si la cadena no coincide con la expresión.
            </summary>
            <param name="value">
            La cadena que se espera que coincida con <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            Expresión regular con la que se espera que <paramref name="value"/>
            coincida.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="value"/>
            no coincide con <paramref name="pattern"/>. El mensaje se muestra en
            los resultados de las pruebas.
            </param>
            <param name="parameters">
            Matriz de parámetros que se usa al formatear <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not match
            <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.DoesNotMatch(System.String,System.Text.RegularExpressions.Regex)">
            <summary>
            Comprueba si la cadena especificada no coincide con una expresión regular
            y produce una excepción si la cadena coincide con la expresión.
            </summary>
            <param name="value">
            Cadena que se espera que no coincida con <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            Expresión regular con la que se espera que <paramref name="value"/> no
            coincida.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> matches <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.DoesNotMatch(System.String,System.Text.RegularExpressions.Regex,System.String)">
            <summary>
            Comprueba si la cadena especificada no coincide con una expresión regular
            y produce una excepción si la cadena coincide con la expresión.
            </summary>
            <param name="value">
            Cadena que se espera que no coincida con <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            Expresión regular con la que se espera que <paramref name="value"/> no
            coincida.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="value"/>
            coincide con <paramref name="pattern"/>. El mensaje se muestra en los resultados de las
            pruebas.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> matches <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.DoesNotMatch(System.String,System.Text.RegularExpressions.Regex,System.String,System.Object[])">
            <summary>
            Comprueba si la cadena especificada no coincide con una expresión regular
            y produce una excepción si la cadena coincide con la expresión.
            </summary>
            <param name="value">
            Cadena que se espera que no coincida con <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            Expresión regular con la que se espera que <paramref name="value"/> no
            coincida.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="value"/>
            coincide con <paramref name="pattern"/>. El mensaje se muestra en los resultados de las
            pruebas.
            </param>
            <param name="parameters">
            Matriz de parámetros que se usa al formatear <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> matches <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert">
            <summary>
            Colección de clases auxiliares para probar varias condiciones asociadas
            a las colecciones en las pruebas unitarias. Si la condición que se está probando no se
            cumple, se produce una excepción.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.That">
            <summary>
            Obtiene la instancia de singleton de la funcionalidad CollectionAssert.
            </summary>
            <remarks>
            Users can use this to plug-in custom assertions through C# extension methods.
            For instance, the signature of a custom assertion provider could be "public static void AreEqualUnordered(this CollectionAssert cusomtAssert, ICollection expected, ICollection actual)"
            Users could then use a syntax similar to the default assertions which in this case is "CollectionAssert.That.AreEqualUnordered(list1, list2);"
            More documentation is at "https://github.com/Microsoft/testfx-docs".
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.Contains(System.Collections.ICollection,System.Object)">
            <summary>
            Comprueba si la colección especificada contiene el elemento indicado
            y produce una excepción si el elemento no está en la colección.
            </summary>
            <param name="collection">
            Colección donde buscar el elemento.
            </param>
            <param name="element">
            El elemento que se espera que esté en la colección.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is not found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.Contains(System.Collections.ICollection,System.Object,System.String)">
            <summary>
            Comprueba si la colección especificada contiene el elemento indicado
            y produce una excepción si el elemento no está en la colección.
            </summary>
            <param name="collection">
            Colección donde buscar el elemento.
            </param>
            <param name="element">
            El elemento que se espera que esté en la colección.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="element"/>
            no se encuentra en <paramref name="collection"/>. El mensaje se muestra en
            los resultados de las pruebas.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is not found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.Contains(System.Collections.ICollection,System.Object,System.String,System.Object[])">
            <summary>
            Comprueba si la colección especificada contiene el elemento indicado
            y produce una excepción si el elemento no está en la colección.
            </summary>
            <param name="collection">
            Colección donde buscar el elemento.
            </param>
            <param name="element">
            El elemento que se espera que esté en la colección.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="element"/>
            no se encuentra en <paramref name="collection"/>. El mensaje se muestra en
            los resultados de las pruebas.
            </param>
            <param name="parameters">
            Matriz de parámetros que se usa al formatear <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is not found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.DoesNotContain(System.Collections.ICollection,System.Object)">
            <summary>
            Comprueba si la colección especificada no contiene el elemento indicado
            y produce una excepción si el elemento se encuentra en la colección.
            </summary>
            <param name="collection">
            Colección donde buscar el elemento.
            </param>
            <param name="element">
            El elemento que se espera que no esté en la colección.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.DoesNotContain(System.Collections.ICollection,System.Object,System.String)">
            <summary>
            Comprueba si la colección especificada no contiene el elemento indicado
            y produce una excepción si el elemento se encuentra en la colección.
            </summary>
            <param name="collection">
            Colección donde buscar el elemento.
            </param>
            <param name="element">
            El elemento que se espera que no esté en la colección.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="element"/>
            se encuentra en <paramref name="collection"/>. El mensaje se muestra en los resultados de las
            pruebas.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.DoesNotContain(System.Collections.ICollection,System.Object,System.String,System.Object[])">
            <summary>
            Comprueba si la colección especificada no contiene el elemento indicado
            y produce una excepción si el elemento se encuentra en la colección.
            </summary>
            <param name="collection">
            Colección donde buscar el elemento.
            </param>
            <param name="element">
            El elemento que se espera que no esté en la colección.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="element"/>
            se encuentra en <paramref name="collection"/>. El mensaje se muestra en los resultados de las
            pruebas.
            </param>
            <param name="parameters">
            Matriz de parámetros que se usa al formatear <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreNotNull(System.Collections.ICollection)">
            <summary>
            Comprueba que todos los elementos de la colección especificada no sean NULL
            y produce una excepción si alguno lo es.
            </summary>
            <param name="collection">
            Colección donde buscar elementos NULL.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a null element is found in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreNotNull(System.Collections.ICollection,System.String)">
            <summary>
            Comprueba que todos los elementos de la colección especificada no sean NULL
            y produce una excepción si alguno lo es.
            </summary>
            <param name="collection">
            Colección donde buscar elementos NULL.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="collection"/>
            contiene un elemento NULL. El mensaje se muestra en los resultados de las pruebas.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a null element is found in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreNotNull(System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Comprueba que todos los elementos de la colección especificada no sean NULL
            y produce una excepción si alguno lo es.
            </summary>
            <param name="collection">
            Colección donde buscar elementos NULL.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="collection"/>
            contiene un elemento NULL. El mensaje se muestra en los resultados de las pruebas.
            </param>
            <param name="parameters">
            Matriz de parámetros que se usa al formatear <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a null element is found in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreUnique(System.Collections.ICollection)">
            <summary>
            Comprueba si todos los elementos de la colección especificada son únicos o no
            y produce una excepción si dos elementos de la colección son iguales.
            </summary>
            <param name="collection">
            Colección donde buscar elementos duplicados.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a two or more equal elements are found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreUnique(System.Collections.ICollection,System.String)">
            <summary>
            Comprueba si todos los elementos de la colección especificada son únicos o no
            y produce una excepción si dos elementos de la colección son iguales.
            </summary>
            <param name="collection">
            Colección donde buscar elementos duplicados.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="collection"/>
            contiene al menos un elemento duplicado. El mensaje se muestra en los
            resultados de las pruebas.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a two or more equal elements are found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreUnique(System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Comprueba si todos los elementos de la colección especificada son únicos o no
            y produce una excepción si dos elementos de la colección son iguales.
            </summary>
            <param name="collection">
            Colección donde buscar elementos duplicados.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="collection"/>
            contiene al menos un elemento duplicado. El mensaje se muestra en los
            resultados de las pruebas.
            </param>
            <param name="parameters">
            Matriz de parámetros que se usa al formatear <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a two or more equal elements are found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsSubsetOf(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Comprueba si una colección es un subconjunto de otra y produce
            una excepción si algún elemento del subconjunto no se encuentra también en el
            superconjunto.
            </summary>
            <param name="subset">
            Se esperaba que la colección fuera un subconjunto de <paramref name="superset"/>.
            </param>
            <param name="superset">
            Se esperaba que la colección fuera un superconjunto de <paramref name="subset"/>
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="subset"/> is not found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsSubsetOf(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            Comprueba si una colección es un subconjunto de otra y produce
            una excepción si algún elemento del subconjunto no se encuentra también en el
            superconjunto.
            </summary>
            <param name="subset">
            Se esperaba que la colección fuera un subconjunto de <paramref name="superset"/>.
            </param>
            <param name="superset">
            Se esperaba que la colección fuera un superconjunto de <paramref name="subset"/>
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando un elemento de
            <paramref name="subset"/> no se encuentra en <paramref name="superset"/>.
            El mensaje se muestra en los resultados de las pruebas.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="subset"/> is not found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsSubsetOf(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Comprueba si una colección es un subconjunto de otra y produce
            una excepción si algún elemento del subconjunto no se encuentra también en el
            superconjunto.
            </summary>
            <param name="subset">
            Se esperaba que la colección fuera un subconjunto de <paramref name="superset"/>.
            </param>
            <param name="superset">
            Se esperaba que la colección fuera un superconjunto de <paramref name="subset"/>
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando un elemento de
            <paramref name="subset"/> no se encuentra en <paramref name="superset"/>.
            El mensaje se muestra en los resultados de las pruebas.
            </param>
            <param name="parameters">
            Matriz de parámetros que se usa al formatear <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="subset"/> is not found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsNotSubsetOf(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Comprueba si una colección no es un subconjunto de otra y produce
            una excepción si todos los elementos del subconjunto se encuentran también en el
            superconjunto.
            </summary>
            <param name="subset">
            Se esperaba que la colección no fuera un subconjunto de <paramref name="superset"/>.
            </param>
            <param name="superset">
            Se esperaba que la colección no fuera un superconjunto de <paramref name="subset"/>
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if every element in <paramref name="subset"/> is also found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsNotSubsetOf(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            Comprueba si una colección no es un subconjunto de otra y produce
            una excepción si todos los elementos del subconjunto se encuentran también en el
            superconjunto.
            </summary>
            <param name="subset">
            Se esperaba que la colección no fuera un subconjunto de <paramref name="superset"/>.
            </param>
            <param name="superset">
            Se esperaba que la colección no fuera un superconjunto de <paramref name="subset"/>
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando cada elemento de
            <paramref name="subset"/> también se encuentra en <paramref name="superset"/>.
            El mensaje se muestra en los resultados de las pruebas.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if every element in <paramref name="subset"/> is also found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsNotSubsetOf(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Comprueba si una colección no es un subconjunto de otra y produce
            una excepción si todos los elementos del subconjunto se encuentran también en el
            superconjunto.
            </summary>
            <param name="subset">
            Se esperaba que la colección no fuera un subconjunto de <paramref name="superset"/>.
            </param>
            <param name="superset">
            Se esperaba que la colección no fuera un superconjunto de <paramref name="subset"/>
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando cada elemento de
            <paramref name="subset"/> también se encuentra en <paramref name="superset"/>.
            El mensaje se muestra en los resultados de las pruebas.
            </param>
            <param name="parameters">
            Matriz de parámetros que se usa al formatear <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if every element in <paramref name="subset"/> is also found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEquivalent(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Comprueba si dos colecciones contienen los mismos elementos y produce
            una excepción si alguna de ellas contiene un elemento que
            no está en la otra.
            </summary>
            <param name="expected">
            Primera colección para comparar. Contiene los elementos que la prueba
            espera.
            </param>
            <param name="actual">
            Segunda colección para comparar. Esta es la colección generada por
            el código sometido a prueba.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element was found in one of the collections but not
            the other.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEquivalent(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            Comprueba si dos colecciones contienen los mismos elementos y produce
            una excepción si alguna de ellas contiene un elemento que
            no está en la otra.
            </summary>
            <param name="expected">
            Primera colección para comparar. Contiene los elementos que la prueba
            espera.
            </param>
            <param name="actual">
            Segunda colección para comparar. Esta es la colección generada por
            el código sometido a prueba.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando un elemento se encontró
            en una de las colecciones pero no en la otra. El mensaje se muestra
            en los resultados de las pruebas.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element was found in one of the collections but not
            the other.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEquivalent(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Comprueba si dos colecciones contienen los mismos elementos y produce
            una excepción si alguna de ellas contiene un elemento que
            no está en la otra.
            </summary>
            <param name="expected">
            Primera colección para comparar. Contiene los elementos que la prueba
            espera.
            </param>
            <param name="actual">
            Segunda colección para comparar. Esta es la colección generada por
            el código sometido a prueba.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando un elemento se encontró
            en una de las colecciones pero no en la otra. El mensaje se muestra
            en los resultados de las pruebas.
            </param>
            <param name="parameters">
            Matriz de parámetros que se usa al formatear <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element was found in one of the collections but not
            the other.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEquivalent(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Comprueba si dos colecciones contienen elementos distintos y produce una
            excepción si las colecciones contienen elementos idénticos, independientemente
            del orden.
            </summary>
            <param name="expected">
            Primera colección para comparar. Contiene los elementos que la prueba
            espera que sean distintos a los de la colección real.
            </param>
            <param name="actual">
            Segunda colección para comparar. Esta es la colección generada por
            el código sometido a prueba.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if the two collections contained the same elements, including
            the same number of duplicate occurrences of each element.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEquivalent(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            Comprueba si dos colecciones contienen elementos distintos y produce una
            excepción si las colecciones contienen elementos idénticos, independientemente
            del orden.
            </summary>
            <param name="expected">
            Primera colección para comparar. Contiene los elementos que la prueba
            espera que sean distintos a los de la colección real.
            </param>
            <param name="actual">
            Segunda colección para comparar. Esta es la colección generada por
            el código sometido a prueba.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="actual"/>
            contiene los mismos elementos que <paramref name="expected"/>. El mensaje
            se muestra en los resultados de las pruebas.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if the two collections contained the same elements, including
            the same number of duplicate occurrences of each element.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEquivalent(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Comprueba si dos colecciones contienen elementos distintos y produce una
            excepción si las colecciones contienen elementos idénticos, independientemente
            del orden.
            </summary>
            <param name="expected">
            Primera colección para comparar. Contiene los elementos que la prueba
            espera que sean distintos a los de la colección real.
            </param>
            <param name="actual">
            Segunda colección para comparar. Esta es la colección generada por
            el código sometido a prueba.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="actual"/>
            contiene los mismos elementos que <paramref name="expected"/>. El mensaje
            se muestra en los resultados de las pruebas.
            </param>
            <param name="parameters">
            Matriz de parámetros que se usa al formatear <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if the two collections contained the same elements, including
            the same number of duplicate occurrences of each element.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreInstancesOfType(System.Collections.ICollection,System.Type)">
            <summary>
            Comprueba si todos los elementos de la colección especificada son instancias
            del tipo esperado y produce una excepción si el tipo esperado no
            se encuentra en la jerarquía de herencia de uno o más de los elementos.
            </summary>
            <param name="collection">
            Colección que contiene los elementos que la prueba espera que sean del
            tipo especificado.
            </param>
            <param name="expectedType">
            El tipo esperado de cada elemento de <paramref name="collection"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="collection"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of an element in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreInstancesOfType(System.Collections.ICollection,System.Type,System.String)">
            <summary>
            Comprueba si todos los elementos de la colección especificada son instancias
            del tipo esperado y produce una excepción si el tipo esperado no
            se encuentra en la jerarquía de herencia de uno o más de los elementos.
            </summary>
            <param name="collection">
            Colección que contiene los elementos que la prueba espera que sean del
            tipo especificado.
            </param>
            <param name="expectedType">
            El tipo esperado de cada elemento de <paramref name="collection"/>.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando un elemento de
            <paramref name="collection"/> no es una instancia de
            <paramref name="expectedType"/>. El mensaje se muestra en los resultados de las pruebas.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="collection"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of an element in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreInstancesOfType(System.Collections.ICollection,System.Type,System.String,System.Object[])">
            <summary>
            Comprueba si todos los elementos de la colección especificada son instancias
            del tipo esperado y produce una excepción si el tipo esperado no
            se encuentra en la jerarquía de herencia de uno o más de los elementos.
            </summary>
            <param name="collection">
            Colección que contiene los elementos que la prueba espera que sean del
            tipo especificado.
            </param>
            <param name="expectedType">
            El tipo esperado de cada elemento de <paramref name="collection"/>.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando un elemento de
            <paramref name="collection"/> no es una instancia de
            <paramref name="expectedType"/>. El mensaje se muestra en los resultados de las pruebas.
            </param>
            <param name="parameters">
            Matriz de parámetros que se usa al formatear <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="collection"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of an element in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Comprueba si dos colecciones especificadas son iguales y produce una excepción
            si las colecciones no son iguales. La igualdad equivale a tener los mismos
            elementos en el mismo orden y la misma cantidad. Las distintas referencias al mismo
            valor se consideran iguales.
            </summary>
            <param name="expected">
            Primera colección para comparar. Esta es la colección que la prueba espera.
            </param>
            <param name="actual">
            Segunda colección para comparar. Esta es la colección generada por el
            código sometido a prueba.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            Comprueba si dos colecciones especificadas son iguales y produce una excepción
            si las colecciones no son iguales. La igualdad equivale a tener los mismos
            elementos en el mismo orden y la misma cantidad. Las distintas referencias al mismo
            valor se consideran iguales.
            </summary>
            <param name="expected">
            Primera colección para comparar. Esta es la colección que la prueba espera.
            </param>
            <param name="actual">
            Segunda colección para comparar. Esta es la colección generada por el
            código sometido a prueba.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="actual"/>
            no es igual a <paramref name="expected"/>. El mensaje se muestra en
            los resultados de las pruebas.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Comprueba si dos colecciones especificadas son iguales y produce una excepción
            si las colecciones no son iguales. La igualdad equivale a tener los mismos
            elementos en el mismo orden y la misma cantidad. Las distintas referencias al mismo
            valor se consideran iguales.
            </summary>
            <param name="expected">
            Primera colección para comparar. Esta es la colección que la prueba espera.
            </param>
            <param name="actual">
            Segunda colección para comparar. Esta es la colección generada por el
            código sometido a prueba.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="actual"/>
            no es igual a <paramref name="expected"/>. El mensaje se muestra en
            los resultados de las pruebas.
            </param>
            <param name="parameters">
            Matriz de parámetros que se usa al formatear <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Comprueba si dos colecciones especificadas son distintas y produce una excepción
            si las colecciones son iguales. La igualdad equivale a tener los mismos
            elementos en el mismo orden y la misma cantidad. Las distintas referencias al mismo
            valor se consideran iguales.
            </summary>
            <param name="notExpected">
            Primera colección para comparar. Esta es la colección que la prueba espera que
            no coincida con <paramref name="actual"/>.
            </param>
            <param name="actual">
            Segunda colección para comparar. Esta es la colección generada por el
            código sometido a prueba.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            Comprueba si dos colecciones especificadas son distintas y produce una excepción
            si las colecciones son iguales. La igualdad equivale a tener los mismos
            elementos en el mismo orden y la misma cantidad. Las distintas referencias al mismo
            valor se consideran iguales.
            </summary>
            <param name="notExpected">
            Primera colección para comparar. Esta es la colección que la prueba espera que
            no coincida con <paramref name="actual"/>.
            </param>
            <param name="actual">
            Segunda colección para comparar. Esta es la colección generada por el
            código sometido a prueba.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="actual"/>
            es igual a <paramref name="notExpected"/>. El mensaje se muestra en
            los resultados de las pruebas.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Comprueba si dos colecciones especificadas son distintas y produce una excepción
            si las colecciones son iguales. La igualdad equivale a tener los mismos
            elementos en el mismo orden y la misma cantidad. Las distintas referencias al mismo
            valor se consideran iguales.
            </summary>
            <param name="notExpected">
            Primera colección para comparar. Esta es la colección que la prueba espera que
            no coincida con <paramref name="actual"/>.
            </param>
            <param name="actual">
            Segunda colección para comparar. Esta es la colección generada por el
            código sometido a prueba.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="actual"/>
            es igual a <paramref name="notExpected"/>. El mensaje se muestra en
            los resultados de las pruebas.
            </param>
            <param name="parameters">
            Matriz de parámetros que se usa al formatear <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer)">
            <summary>
            Comprueba si dos colecciones especificadas son iguales y produce una excepción
            si las colecciones no son iguales. La igualdad equivale a tener los mismos
            elementos en el mismo orden y la misma cantidad. Las distintas referencias al mismo
            valor se consideran iguales.
            </summary>
            <param name="expected">
            Primera colección para comparar. Esta es la colección que la prueba espera.
            </param>
            <param name="actual">
            Segunda colección para comparar. Esta es la colección generada por el
            código sometido a prueba.
            </param>
            <param name="comparer">
            Implementación de comparación que se va a usar al comparar elementos de la colección.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer,System.String)">
            <summary>
            Comprueba si dos colecciones especificadas son iguales y produce una excepción
            si las colecciones no son iguales. La igualdad equivale a tener los mismos
            elementos en el mismo orden y la misma cantidad. Las distintas referencias al mismo
            valor se consideran iguales.
            </summary>
            <param name="expected">
            Primera colección para comparar. Esta es la colección que la prueba espera.
            </param>
            <param name="actual">
            Segunda colección para comparar. Esta es la colección generada por el
            código sometido a prueba.
            </param>
            <param name="comparer">
            Implementación de comparación que se va a usar al comparar elementos de la colección.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="actual"/>
            no es igual a <paramref name="expected"/>. El mensaje se muestra en
            los resultados de las pruebas.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer,System.String,System.Object[])">
            <summary>
            Comprueba si dos colecciones especificadas son iguales y produce una excepción
            si las colecciones no son iguales. La igualdad equivale a tener los mismos
            elementos en el mismo orden y la misma cantidad. Las distintas referencias al mismo
            valor se consideran iguales.
            </summary>
            <param name="expected">
            Primera colección para comparar. Esta es la colección que la prueba espera.
            </param>
            <param name="actual">
            Segunda colección para comparar. Esta es la colección generada por el
            código sometido a prueba.
            </param>
            <param name="comparer">
            Implementación de comparación que se va a usar al comparar elementos de la colección.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="actual"/>
            no es igual a <paramref name="expected"/>. El mensaje se muestra en
            los resultados de las pruebas.
            </param>
            <param name="parameters">
            Matriz de parámetros que se usa al formatear <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer)">
            <summary>
            Comprueba si dos colecciones especificadas son distintas y produce una excepción
            si las colecciones son iguales. La igualdad equivale a tener los mismos
            elementos en el mismo orden y la misma cantidad. Las distintas referencias al mismo
            valor se consideran iguales.
            </summary>
            <param name="notExpected">
            Primera colección para comparar. Esta es la colección que la prueba espera que
            no coincida con <paramref name="actual"/>.
            </param>
            <param name="actual">
            Segunda colección para comparar. Esta es la colección generada por el
            código sometido a prueba.
            </param>
            <param name="comparer">
            Implementación de comparación que se va a usar al comparar elementos de la colección.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer,System.String)">
            <summary>
            Comprueba si dos colecciones especificadas son distintas y produce una excepción
            si las colecciones son iguales. La igualdad equivale a tener los mismos
            elementos en el mismo orden y la misma cantidad. Las distintas referencias al mismo
            valor se consideran iguales.
            </summary>
            <param name="notExpected">
            Primera colección para comparar. Esta es la colección que la prueba espera que
            no coincida con <paramref name="actual"/>.
            </param>
            <param name="actual">
            Segunda colección para comparar. Esta es la colección generada por el
            código sometido a prueba.
            </param>
            <param name="comparer">
            Implementación de comparación que se va a usar al comparar elementos de la colección.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="actual"/>
            es igual a <paramref name="notExpected"/>. El mensaje se muestra en
            los resultados de las pruebas.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer,System.String,System.Object[])">
            <summary>
            Comprueba si dos colecciones especificadas son distintas y produce una excepción
            si las colecciones son iguales. La igualdad equivale a tener los mismos
            elementos en el mismo orden y la misma cantidad. Las distintas referencias al mismo
            valor se consideran iguales.
            </summary>
            <param name="notExpected">
            Primera colección para comparar. Esta es la colección que la prueba espera que
            no coincida con <paramref name="actual"/>.
            </param>
            <param name="actual">
            Segunda colección para comparar. Esta es la colección generada por el
            código sometido a prueba.
            </param>
            <param name="comparer">
            Implementación de comparación que se va a usar al comparar elementos de la colección.
            </param>
            <param name="message">
            Mensaje que se va a incluir en la excepción cuando <paramref name="actual"/>
            es igual a <paramref name="notExpected"/>. El mensaje se muestra en
            los resultados de las pruebas.
            </param>
            <param name="parameters">
            Matriz de parámetros que se usa al formatear <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsSubsetOfHelper(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Determina si la primera colección es un subconjunto de la
            segunda. Si cualquiera de los conjuntos contiene elementos duplicados, el número
            de repeticiones del elemento en el subconjunto debe ser inferior o
            igual al número de repeticiones en el superconjunto.
            </summary>
            <param name="subset">
            Colección que la prueba espera que esté incluida en <paramref name="superset"/>.
            </param>
            <param name="superset">
            Colección que la prueba espera que contenga <paramref name="subset"/>.
            </param>
            <returns>
            True si <paramref name="subset"/> es un subconjunto de
            <paramref name="superset"/>, de lo contrario false.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.GetElementCounts(System.Collections.ICollection,System.Int32@)">
            <summary>
            Construye un diccionario que contiene el número de repeticiones de cada
            elemento en la colección especificada.
            </summary>
            <param name="collection">
            Colección que se va a procesar.
            </param>
            <param name="nullCount">
            Número de elementos NULL de la colección.
            </param>
            <returns>
            Diccionario que contiene el número de repeticiones de cada elemento
            en la colección especificada.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.FindMismatchedElement(System.Collections.ICollection,System.Collections.ICollection,System.Int32@,System.Int32@,System.Object@)">
            <summary>
            Encuentra un elemento no coincidente entre ambas colecciones. Un elemento
            no coincidente es aquel que aparece un número distinto de veces en la
            colección esperada de lo que aparece en la colección real. Se
            supone que las colecciones son referencias no NULL diferentes con el
            mismo número de elementos. El autor de la llamada es el responsable de
            este nivel de comprobación. Si no hay ningún elemento no coincidente,
            la función devuelve false y no deben usarse parámetros out.
            </summary>
            <param name="expected">
            La primera colección para comparar.
            </param>
            <param name="actual">
            La segunda colección para comparar.
            </param>
            <param name="expectedCount">
            Número esperado de repeticiones de
            <paramref name="mismatchedElement"/> o 0 si no hay ningún elemento no
            coincidente.
            </param>
            <param name="actualCount">
            El número real de repeticiones de
            <paramref name="mismatchedElement"/> o 0 si no hay ningún elemento no
            coincidente.
            </param>
            <param name="mismatchedElement">
            El elemento no coincidente (puede ser nulo) o NULL si no hay ningún
            elemento no coincidente.
            </param>
            <returns>
            Es true si se encontró un elemento no coincidente. De lo contrario, false.
            </returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.ObjectComparer">
            <summary>
            compara los objetos con object.Equals.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException">
            <summary>
            Clase base para las excepciones de marco.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException.#ctor">
            <summary>
            Inicializa una nueva instancia de la clase <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException"/>.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException.#ctor(System.String,System.Exception)">
            <summary>
            Inicializa una nueva instancia de la clase <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException"/>.
            </summary>
            <param name="msg"> El mensaje. </param>
            <param name="ex"> La excepción. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException.#ctor(System.String)">
            <summary>
            Inicializa una nueva instancia de la clase <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException"/>.
            </summary>
            <param name="msg"> El mensaje. </param>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages">
            <summary>
              Clase de recurso fuertemente tipado para buscar cadenas traducidas, etc.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ResourceManager">
            <summary>
              Devuelve la instancia de ResourceManager almacenada en caché que usa esta clase.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.Culture">
            <summary>
              Invalida la propiedad CurrentUICulture del subproceso actual para todas
              las búsquedas de recursos que usan esta clase de recursos fuertemente tipados.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AccessStringInvalidSyntax">
            <summary>
              Busca una cadena traducida similar a "La cadena de acceso tiene una sintaxis no válida".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ActualHasMismatchedElements">
            <summary>
              Busca una cadena traducida similar a "La colección esperada contiene {1} repeticiones de &lt;{2}&gt;. La colección actual contiene {3} repeticiones. {0}".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AllItemsAreUniqueFailMsg">
            <summary>
              Busca una cadena traducida similar a "Se encontró un elemento duplicado: &lt;{1}&gt;. {0}".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreEqualCaseFailMsg">
            <summary>
              Busca una cadena traducida similar a "Se esperaba: &lt;{1}&gt;. El caso es distinto para el valor real: &lt;{2}&gt;. {0}".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreEqualDeltaFailMsg">
            <summary>
              Busca una cadena traducida similar a "Se esperaba una diferencia no superior a &lt;{3}&gt; entre el valor esperado &lt;{1}&gt; y el valor real &lt;{2}&gt;. {0}".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreEqualDifferentTypesFailMsg">
            <summary>
              Busca una cadena traducida similar a "Se esperaba: &lt;{1} ({2})&gt;, pero es: &lt;{3} ({4})&gt;. {0}".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreEqualFailMsg">
            <summary>
              Busca una cadena traducida similar a "Se esperaba: &lt;{1}&gt;, pero es: &lt;{2}&gt;. {0}".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreNotEqualDeltaFailMsg">
            <summary>
              Busca una cadena traducida similar a "Se esperaba una diferencia mayor que &lt;{3}&gt; entre el valor esperado &lt;{1}&gt; y el valor real &lt;{2}&gt;. {0}".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreNotEqualFailMsg">
            <summary>
              Busca una cadena traducida similar a "Se esperaba cualquier valor excepto: &lt;{1}&gt;, pero es: &lt;{2}&gt;. {0}".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreSameGivenValues">
            <summary>
              Busca una cadena traducida similar a "No pase tipos de valor a AreSame(). Los valores convertidos a Object no serán nunca iguales. Considere el uso de AreEqual(). {0}".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AssertionFailed">
            <summary>
              Busca una cadena traducida similar a "Error de {0}. {1}".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AsyncUITestMethodNotSupported">
            <summary>
              Busca una cadena traducida similar a "No se admite un método de prueba asincrónico con UITestMethodAttribute. Quite el método asincrónico o use TestMethodAttribute.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.BothCollectionsEmpty">
            <summary>
              Busca una cadena traducida similar a "Ambas colecciones están vacías". {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.BothCollectionsSameElements">
            <summary>
              Busca una cadena traducida similar a "Ambas colecciones tienen los mismos elementos".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.BothCollectionsSameReference">
            <summary>
              Busca una cadena traducida similar a "Las referencias de ambas colecciones apuntan al mismo objeto de colección. {0}".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.BothSameElements">
            <summary>
              Busca una cadena traducida similar a "Ambas colecciones tienen los mismos elementos. {0}".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.CollectionEqualReason">
            <summary>
              Busca una cadena traducida similar a "{0}({1})".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.Common_NullInMessages">
            <summary>
              Busca una cadena traducida similar a "(NULL)".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.Common_ObjectString">
            <summary>
              Busca una cadena traducida similar a "(objeto)".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ContainsFail">
            <summary>
              Busca una cadena traducida similar a "La cadena "{0}" no contiene la cadena "{1}". {2}".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.DataDrivenResultDisplayName">
            <summary>
              Busca una cadena traducida similar a "{0} ({1})".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.DoNotUseAssertEquals">
            <summary>
              Busca una cadena traducida similar a "No se debe usar Assert.Equals para aserciones. Use Assert.AreEqual y Overloads en su lugar".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ElementNumbersDontMatch">
            <summary>
              Busca una cadena traducida similar a "El número de elementos de las colecciones no coincide. Se esperaba: &lt;{1}&gt;, pero es: &lt;{2}&gt;. {0}".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ElementsAtIndexDontMatch">
            <summary>
              Busca una cadena traducida similar a "El elemento del índice {0} no coincide".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ElementTypesAtIndexDontMatch">
            <summary>
              Busca una cadena traducida similar a "El elemento del índice {1} no es del tipo esperado. Tipo esperado: &lt;{2}&gt;, tipo real: &lt;{3}&gt;. {0}".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ElementTypesAtIndexDontMatch2">
            <summary>
              Busca una cadena traducida similar a "El elemento del índice {1} es (NULL). Se esperaba el tipo: &lt;{2}&gt;. {0}".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.EndsWithFail">
            <summary>
              Busca una cadena traducida similar a "La cadena "{0}" no termina con la cadena "{1}". {2}".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.EqualsTesterInvalidArgs">
            <summary>
              Busca una cadena traducida similar a "Argumento no válido: EqualsTester no puede utilizar valores NULL".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ErrorInvalidCast">
            <summary>
              Busca una cadena traducida similar a "El objeto de tipo {0} no se puede convertir en {1}".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.InternalObjectNotValid">
            <summary>
              Busca una cadena traducida similar a "El objeto interno al que se hace referencia ya no es válido".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.InvalidParameterToAssert">
            <summary>
              Busca una cadena traducida similar a "El parámetro "{0}" no es válido. {1}".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.InvalidPropertyType">
            <summary>
              Busca una cadena traducida similar a "La propiedad {0} tiene el tipo {1}; se esperaba el tipo {2}".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.IsInstanceOfFailMsg">
            <summary>
              Busca una cadena traducida similar a "{0} Tipo esperado: &lt;{1}&gt;. Tipo real: &lt;{2}&gt;".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.IsMatchFail">
            <summary>
              Busca una cadena traducida similar a "La cadena "{0}" no coincide con el patrón "{1}". {2}".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.IsNotInstanceOfFailMsg">
            <summary>
              Busca una cadena traducida similar a "Tipo incorrecto: &lt;{1}&gt;. Tipo real: &lt;{2}&gt;. {0}".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.IsNotMatchFail">
            <summary>
              Busca una cadena traducida similar a "La cadena "{0}" coincide con el patrón "{1}". {2}".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.NoDataRow">
            <summary>
              Busca una cadena traducida similar a "No se especificó ningún atributo DataRowAttribute. Se requiere al menos un elemento DataRowAttribute con DataTestMethodAttribute".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.NoExceptionThrown">
            <summary>
              Busca una cadena traducida similar a "No se produjo ninguna excepción. Se esperaba la excepción {1}. {0}".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.NullParameterToAssert">
            <summary>
              Busca una cadena traducida similar a "El parámetro "{0}" no es válido. El valor no puede ser NULL. {1}".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.NumberOfElementsDiff">
            <summary>
              Busca una cadena traducida similar a "Número diferente de elementos".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.PrivateAccessorConstructorNotFound">
            <summary>
              Busca una cadena traducida similar a 
                 "No se encontró el constructor con la signatura especificada. Es posible que tenga que regenerar el descriptor de acceso privado,
                 o que el miembro sea privado y esté definido en una clase base. Si se trata de esto último, debe pasar el tipo
                 que define el miembro al constructor de PrivateObject".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.PrivateAccessorMemberNotFound">
            <summary>
              Busca una cadena traducida similar a 
                 "No se encontró el miembro especificado ({0}). Es posible que tenga que regenerar el descriptor de acceso privado,
                 o que el miembro sea privado y esté definido en una clase base. Si se trata de esto último, debe pasar el tipo
                 que define el miembro al constructor de PrivateObject".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.StartsWithFail">
            <summary>
              Busca una cadena traducida similar a "La cadena "{0}" no empieza con la cadena "{1}". {2}".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_ExpectedExceptionTypeMustDeriveFromException">
            <summary>
              Busca una cadena traducida similar a "El tipo de excepción esperado debe ser System.Exception o un tipo derivado de System.Exception".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_FailedToGetExceptionMessage">
            <summary>
              Busca una cadena traducida similar a "No se pudo obtener el mensaje para una excepción del tipo {0} debido a una excepción".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_TestMethodNoException">
            <summary>
              Busca una cadena traducida similar a "El método de prueba no inició la excepción esperada {0}. {1}".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_TestMethodNoExceptionDefault">
            <summary>
              Busca una cadena traducida similar a "El método de prueba no inició una excepción. El atributo {0} definido en el método de prueba esperaba una excepción".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_TestMethodWrongException">
            <summary>
              Busca una cadena traducida similar a "El método de prueba inició la excepción {0}, pero se esperaba la excepción {1}. Mensaje de la excepción: {2}".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_TestMethodWrongExceptionDerivedAllowed">
            <summary>
              Busca una cadena traducida similar a "El método de prueba inició la excepción {0}, pero se esperaba la excepción {1} o un tipo derivado de ella. Mensaje de la excepción: {2}".
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.WrongExceptionThrown">
             <summary>
               Busca una cadena traducida similar a "Se produjo la excepción {2}, pero se esperaba la excepción {1}. {0}
            Mensaje de excepción: {3}
            Seguimiento de la pila: {4}".
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome">
            <summary>
            Resultados de la prueba unitaria.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Failed">
            <summary>
            La prueba se ejecutó, pero hubo problemas.
            Entre estos, puede haber excepciones o aserciones con errores.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Inconclusive">
            <summary>
            La prueba se completó, pero no podemos determinar si el resultado fue correcto o no.
            Se puede usar para pruebas anuladas.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Passed">
            <summary>
            La prueba se ejecutó sin problemas.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.InProgress">
            <summary>
            La prueba se está ejecutando.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Error">
            <summary>
            Error del sistema al intentar ejecutar una prueba.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Timeout">
            <summary>
            Se agotó el tiempo de espera de la prueba.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Aborted">
            <summary>
            El usuario anuló la prueba.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Unknown">
            <summary>
            La prueba tiene un estado desconocido
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.UtfHelper">
            <summary>
            Proporciona funcionalidad auxiliar para el marco de pruebas unitarias.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.UtfHelper.GetExceptionMsg(System.Exception)">
            <summary>
            Obtiene los mensajes de excepción, incluidos los mensajes de todas las excepciones internas,
            de forma recursiva.
            </summary>
            <param name="ex">Excepción para la que se obtienen los mensajes</param>
            <returns>la cadena con información del mensaje de error</returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestTimeout">
            <summary>
            Enumeración para cuando se agota el tiempo de espera que se puede usar con el atributo <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute"/>.
            El tipo de la enumeración debe coincidir.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.TestTimeout.Infinite">
            <summary>
            Infinito.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestClassAttribute">
            <summary>
            Atributo de la clase de prueba.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestClassAttribute.GetTestMethodAttribute(Microsoft.VisualStudio.TestTools.UnitTesting.TestMethodAttribute)">
            <summary>
            Obtiene un atributo de método de prueba que habilita la ejecución de esta prueba.
            </summary>
            <param name="testMethodAttribute">La instancia de atributo de método de prueba definida en este método.</param>
            <returns>Tipo <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestMethodAttribute"/> que se utilizará para ejecutar esta prueba.</returns>
            <remarks>Extensions can override this method to customize how all methods in a class are run.</remarks>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestMethodAttribute">
            <summary>
            Atributo del método de prueba.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestMethodAttribute.Execute(Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod)">
            <summary>
            Ejecuta un método de prueba.
            </summary>
            <param name="testMethod">El método de prueba para ejecutar.</param>
            <returns>Una matriz de objetos de TestResult que representan los resultados de la prueba.</returns>
            <remarks>Extensions can override this method to customize running a TestMethod.</remarks>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestInitializeAttribute">
            <summary>
            Atributo para inicializar la prueba.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCleanupAttribute">
            <summary>
            Atributo de limpieza de la prueba.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.IgnoreAttribute">
            <summary>
            Atributo de omisión.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute">
            <summary>
            Atributo de propiedad de la prueba.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute.#ctor(System.String,System.String)">
            <summary>
            Inicializa una nueva instancia de la clase <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute"/>.
            </summary>
            <param name="name">
            El nombre.
            </param>
            <param name="value">
            El valor.
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute.Name">
            <summary>
            Obtiene el nombre.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute.Value">
            <summary>
            Obtiene el valor.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ClassInitializeAttribute">
            <summary>
            Atributo de inicialización de la clase.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ClassCleanupAttribute">
            <summary>
            Atributo de limpieza de la clase.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssemblyInitializeAttribute">
            <summary>
            Atributo de inicialización del ensamblado.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssemblyCleanupAttribute">
            <summary>
            Atributo de limpieza del ensamblado.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.OwnerAttribute">
            <summary>
            Propietario de la prueba.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.OwnerAttribute.#ctor(System.String)">
            <summary>
            Inicializa una nueva instancia de la clase <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.OwnerAttribute"/>.
            </summary>
            <param name="owner">
            El propietario.
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.OwnerAttribute.Owner">
            <summary>
            Obtiene el propietario.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.PriorityAttribute">
            <summary>
            Atributo de prioridad. Se usa para especificar la prioridad de una prueba unitaria.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PriorityAttribute.#ctor(System.Int32)">
            <summary>
            Inicializa una nueva instancia de la clase <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.PriorityAttribute"/>.
            </summary>
            <param name="priority">
            La prioridad.
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.PriorityAttribute.Priority">
            <summary>
            Obtiene la prioridad.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DescriptionAttribute">
            <summary>
            Descripción de la prueba.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DescriptionAttribute.#ctor(System.String)">
            <summary>
            Inicializa una nueva instancia de la clase <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DescriptionAttribute"/> para describir una prueba.
            </summary>
            <param name="description">La descripción.</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DescriptionAttribute.Description">
            <summary>
            Obtiene la descripción de una prueba.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.CssProjectStructureAttribute">
            <summary>
            URI de estructura de proyectos de CSS.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CssProjectStructureAttribute.#ctor(System.String)">
            <summary>
            Inicializa una nueva instancia de la clase <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.CssProjectStructureAttribute"/> para el URI de estructura de proyecto de CSS.
            </summary>
            <param name="cssProjectStructure">URI de estructura de proyectos de CSS.</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.CssProjectStructureAttribute.CssProjectStructure">
            <summary>
            Obtiene el URI de estructura de proyectos de CSS.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.CssIterationAttribute">
            <summary>
            URI de iteración de CSS.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CssIterationAttribute.#ctor(System.String)">
            <summary>
            Inicializa una nueva instancia de la clase <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.CssIterationAttribute"/> para el URI de iteración de CSS.
            </summary>
            <param name="cssIteration">URI de iteración de CSS.</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.CssIterationAttribute.CssIteration">
            <summary>
            Obtiene el URI de iteración de CSS.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.WorkItemAttribute">
            <summary>
            Atributo WorkItem. Se usa para especificar un elemento de trabajo asociado a esta prueba.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.WorkItemAttribute.#ctor(System.Int32)">
            <summary>
            Inicializa una nueva instancia de la clase <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.WorkItemAttribute"/> para el atributo WorkItem.
            </summary>
            <param name="id">Identificador de un elemento de trabajo.</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.WorkItemAttribute.Id">
            <summary>
            Obtiene el identificador de un elemento de trabajo asociado.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute">
            <summary>
            Atributo de tiempo de espera. Se usa para especificar el tiempo de espera de una prueba unitaria.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute.#ctor(System.Int32)">
            <summary>
            Inicializa una nueva instancia de la clase <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute"/>.
            </summary>
            <param name="timeout">
            Tiempo de espera.
            </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute.#ctor(Microsoft.VisualStudio.TestTools.UnitTesting.TestTimeout)">
            <summary>
            Inicializa una nueva instancia de la clase <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute"/> con un tiempo de espera preestablecido.
            </summary>
            <param name="timeout">
            Tiempo de espera
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute.Timeout">
            <summary>
            Obtiene el tiempo de espera.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult">
            <summary>
            Objeto TestResult que debe devolverse al adaptador.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.#ctor">
            <summary>
            Inicializa una nueva instancia de la clase <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult"/>.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.DisplayName">
            <summary>
            Obtiene o establece el nombre para mostrar del resultado. Es útil cuando se devuelven varios resultados.
            Si es NULL, se utiliza el nombre del método como nombre para mostrar.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.Outcome">
            <summary>
            Obtiene o establece el resultado de la ejecución de pruebas.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.TestFailureException">
            <summary>
            Obtiene o establece la excepción que se inicia cuando la prueba da error.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.LogOutput">
            <summary>
            Obtiene o establece la salida del mensaje registrado por el código de la prueba.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.LogError">
            <summary>
            Obtiene o establece la salida del mensaje registrado por el código de la prueba.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.DebugTrace">
            <summary>
            Obtiene o establece el seguimiento de depuración que realiza el código de la prueba.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.TestContextMessages">
            <summary>
            Gets or sets the debug traces by test code.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.Duration">
            <summary>
            Obtiene o establece la duración de la ejecución de la prueba.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.DatarowIndex">
            <summary>
            Obtiene o establece el índice de la fila de datos en el origen de datos. Se establece solo para resultados
            de ejecuciones individuales de filas de datos de una prueba controlada por datos.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.ReturnValue">
            <summary>
            Obtiene o establece el valor devuelto del método de prueba. Actualmente es siempre NULL.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.ResultFiles">
            <summary>
            Obtiene o establece los archivos de resultados que adjunta la prueba.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute">
            <summary>
            Especifica la cadena de conexión, el nombre de tabla y el método de acceso a fila para las pruebas controladas por datos.
            </summary>
            <example>
            [DataSource("Provider=SQLOLEDB.1;Data Source=source;Integrated Security=SSPI;Initial Catalog=EqtCoverage;Persist Security Info=False", "MyTable")]
            [DataSource("dataSourceNameFromConfigFile")]
            </example>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.DefaultProviderName">
            <summary>
            Nombre de proveedor predeterminado del origen de datos.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.DefaultDataAccessMethod">
            <summary>
            Método de acceso a datos predeterminado.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.#ctor(System.String,System.String,System.String,Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod)">
            <summary>
            Inicializa una nueva instancia de la clase <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute"/>. Esta instancia se inicializará con un proveedor de datos, una cadena de conexión, una tabla de datos y un método de acceso a datos para acceder al origen de datos.
            </summary>
            <param name="providerInvariantName">Nombre invariable del proveedor de datos, como System.Data.SqlClient</param>
            <param name="connectionString">
            Cadena de conexión específica del proveedor de datos. 
            ADVERTENCIA: La cadena de conexión puede contener información confidencial (por ejemplo, una contraseña).
            La cadena de conexión se almacena en texto sin formato en el código fuente y en el ensamblado compilado. 
            Restrinja el acceso al código fuente y al ensamblado para proteger esta información confidencial.
            </param>
            <param name="tableName">Nombre de la tabla de datos.</param>
            <param name="dataAccessMethod">Especifica el orden de acceso a los datos.</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.#ctor(System.String,System.String)">
            <summary>
            Inicializa una nueva instancia de la clase <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute"/>. Esta instancia se inicializará con una cadena de conexión y un nombre de tabla.
            Especifique la cadena de conexión y la tabla de datos para acceder al origen de datos OLEDB.
            </summary>
            <param name="connectionString">
            Cadena de conexión específica del proveedor de datos. 
            ADVERTENCIA: La cadena de conexión puede contener información confidencial (por ejemplo, una contraseña).
            La cadena de conexión se almacena en texto sin formato en el código fuente y en el ensamblado compilado. 
            Restrinja el acceso al código fuente y al ensamblado para proteger esta información confidencial.
            </param>
            <param name="tableName">Nombre de la tabla de datos.</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.#ctor(System.String)">
            <summary>
            Inicializa una nueva instancia de la clase <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute"/>. Esta instancia se inicializará con un proveedor de datos y una cadena de conexión asociada al nombre del valor de configuración.
            </summary>
            <param name="dataSourceSettingName">El nombre de un origen de datos que se encuentra en la sección &lt;microsoft.visualstudio.qualitytools&gt; del archivo app.config.</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.ProviderInvariantName">
            <summary>
            Obtiene un valor que representa el proveedor de datos del origen de datos.
            </summary>
            <returns>
            Nombre del proveedor de datos. Si no se designó un proveedor de datos al inicializar el objeto, se devolverá el proveedor predeterminado de System.Data.OleDb.
            </returns>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.ConnectionString">
            <summary>
            Obtiene un valor que representa la cadena de conexión para el origen de datos.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.TableName">
            <summary>
            Obtiene un valor que indica el nombre de la tabla que proporciona los datos.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.DataAccessMethod">
             <summary>
             Obtiene el método usado para tener acceso al origen de datos.
            </summary>
            
             <returns>
             Uno de los<see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod"/> . Si <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute"/> no se ha inicializado, devolverá el valor predeterminado <see cref="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod.Random"/>.
             </returns>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.DataSourceSettingName">
            <summary>
            Obtiene el nombre del origen de datos que se encuentra en la sección &lt;microsoft.visualstudio.qualitytools&gt; del archivo app.config.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataTestMethodAttribute">
            <summary>
            Atributo para una prueba controlada por datos donde los datos pueden especificarse insertados.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataTestMethodAttribute.Execute(Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod)">
            <summary>
            Busca todas las filas de datos y las ejecuta.
            </summary>
            <param name="testMethod">
            El método de prueba.
            </param>
            <returns>
            Una matriz de <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult"/>.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataTestMethodAttribute.RunDataDrivenTest(Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod,Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute[])">
            <summary>
            Ejecuta el método de prueba controlada por datos.
            </summary>
            <param name="testMethod"> Método de prueba para ejecutar. </param>
            <param name="dataRows"> Fila de datos. </param>
            <returns> Resultados de la ejecución. </returns>
        </member>
    </members>
</doc>
