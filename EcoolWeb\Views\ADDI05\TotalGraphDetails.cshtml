﻿@model ADDI05TotalGraphDetailsViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
}
<link href="~/Scripts/tablesorter/dist/css/theme.blue.css" rel="stylesheet" />

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

<div class="form-inline no-print" role="form">
    <div class="form-group">
        <label class="control-label">
            @Html.DisplayNameFor(model => model.AnsList.First().CLASS_NO)
        </label>
    </div>
    <div class="form-group">
        @Html.EditorFor(m => m.CLASS_NO, new { htmlAttributes = new { @class = "form-control input-sm" } })
    </div>
    <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="onSearch()" />
</div>

<div class="row no-print">
    <div class="col-md-offset-1 col-md-6 text-left">
    </div>
    <div class="col-md-4 col-md-offset-1 text-right">
        <button type="button" class="btn btn-default btn-xs no-print" id="download">
            匯出Excel
        </button>
        <button type="button" class="btn btn-default btn-xs no-print" onclick="onprint('#DIALOG_DIV')">
            列印
        </button>
    </div>
</div>

<div class="table-responsive">
    <div class="text-center" id="DIALOG_DIV">
        <div class="Caption_Div_Left">
            活動名稱：@Model.MADDT11.DIALOG_NAME <br />

            @if (string.IsNullOrWhiteSpace(Model.ANSWER) == false)
            {
                <span>
                    題目：@Html.Raw(HttpUtility.HtmlDecode(Model.DADDT12.Q_TEXT))
                </span><br />
                <span>回答：@(Model.ANSWER)</span>
            }
        </div>
        <table class="tablesorter-blue" id="TableData">

            <thead>
                <tr>
                    <td>
                        @Html.DisplayNameFor(model => model.AnsList.First().SHORT_NAME)
                    </td>
                    <td>
                        @Html.DisplayNameFor(model => model.AnsList.First().SYEAR)
                    </td>
                    <td>
                        @Html.DisplayNameFor(model => model.AnsList.First().SEMESTER)
                    </td>
                    <td>
                        @Html.DisplayNameFor(model => model.AnsList.First().CLASS_NO)
                    </td>
                    <td>
                        @Html.DisplayNameFor(model => model.AnsList.First().SEAT_NO)
                    </td>
                    <td>
                        姓名
                    </td>
                    <td>
                        @Html.DisplayNameFor(model => model.AnsList.First().CRE_DATE)
                    </td>
                    @if (string.IsNullOrWhiteSpace(Model.ANSWER))
                    {
                        <td>答對/答錯</td>
                    }
                </tr>
            </thead>
            <tbody>
                @foreach (var item in Model.AnsList)
                {
                    <tr class="@item.CLASS_NO CLASS_NO">
                        <td align="center">
                            @Html.DisplayFor(modelItem => item.SHORT_NAME)
                        </td>
                        <td align="center">
                            @Html.DisplayFor(modelItem => item.SYEAR)
                        </td>
                        <td align="center">
                            @Html.DisplayFor(modelItem => item.SEMESTER)
                        </td>
                        <td align="center">
                            @if (string.IsNullOrWhiteSpace(item.CLASS_NO))
                            {
                                <samp>-</samp>
                            }
                            else
                            {
                                @Html.DisplayFor(modelItem => item.CLASS_NO)
                            }
                        </td>
                        <td align="center">
                            @if (string.IsNullOrWhiteSpace(item.SEAT_NO))
                            {
                                <samp>-</samp>
                            }
                            else
                            {
                                @Html.DisplayFor(modelItem => item.SEAT_NO)
                            }
                        </td>
                        <td align="center">
                            @Html.DisplayFor(modelItem => item.SNAME)
                        </td>
                        <td align="center">
                            @Html.DisplayFor(modelItem => item.CRE_DATE)
                        </td>
                        @if (string.IsNullOrWhiteSpace(Model.ANSWER))
                        {
                            <td align="center">
                                @if (item.RIGHT_YN == "Y")
                                {
                                    <span>O</span>
                                }
                                else
                                {
                                    <span>X</span>
                                }
                            </td>
                        }
                    </tr>
                }
            </tbody>
        </table>
        <h3> 共 @(Model.AnsList.Count()) 人</h3>
    </div>
</div>

<script src="~/Scripts/printThis/printThis.js"></script>
<script src="~/Scripts/tablesorter/dist/js/jquery.tablesorter.js"></script>
<script src="~/Scripts/tablesorter/dist/js/jquery.tablesorter.widgets.js"></script>
<script src="~/Scripts/Blob.js"></script>
<script src="~/Scripts/FileSaver.js"></script>
<script src="~/Scripts/tableexport.js"></script>

<script language="JavaScript">

    $(function () {
        var $table = $('#TableData');

        $('#download').click(function () {
            fn_save()
        });

        $table.tablesorter();
    });

    function onprint(DivId) {
        $(DivId).printThis();
    }

    function onSearch() {
      var CLASS_NO =  $('#@Html.IdFor(m=>m.CLASS_NO)').val();

        if (CLASS_NO!='') {
            $('.CLASS_NO').hide();
            $('.'+CLASS_NO).show();
        }
        else {
              $('.CLASS_NO').show();
        }

    }

    function fn_save() {
        var blob = new Blob([document.getElementById('DIALOG_DIV').innerHTML], {
            type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8"
        });
        var strFile = "Report.xls";

        saveAs(blob, strFile);
        return false;
    }
</script>