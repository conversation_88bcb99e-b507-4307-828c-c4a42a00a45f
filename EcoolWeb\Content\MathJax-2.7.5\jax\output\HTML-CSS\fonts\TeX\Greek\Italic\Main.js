/*
 *  /MathJax/jax/output/HTML-CSS/fonts/TeX/Greek/Italic/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["MathJax_Greek-italic"]={directory:"Greek/Italic",family:"MathJax_Greek",style:"italic",testString:"\u0393 \u03A5 \u039B",skew:{915:0.0833,916:0.167,920:0.0833,923:0.167,926:0.0833,928:0.0556,931:0.0833,933:0.0556,934:0.0833,936:0.0556,937:0.0833,945:0.0278,946:0.0833,948:0.0556,949:0.0833,950:0.0833,951:0.0556,952:0.0833,953:0.0556,956:0.0278,957:0.0278,958:0.111,959:0.0556,961:0.0833,962:0.0833,964:0.0278,965:0.0278,966:0.0833,967:0.0556,968:0.111,977:0.0833,981:0.0833,1009:0.0833,1013:0.0556},32:[0,0,250,0,0],160:[0,0,250,0,0],915:[680,-1,615,31,721],916:[716,0,833,48,788],920:[704,22,763,50,740],923:[716,0,694,35,670],926:[678,0,742,53,777],928:[681,0,831,31,887],931:[683,0,780,58,806],933:[705,0,583,28,700],934:[683,0,667,24,642],936:[683,0,612,21,692],937:[704,0,772,80,786],945:[442,11,640,34,603],946:[705,194,566,23,573],947:[441,216,518,11,543],948:[717,10,444,36,451],949:[452,22,466,27,428],950:[704,204,438,44,471],951:[442,216,497,21,503],952:[705,10,469,35,462],953:[442,10,354,48,332],954:[442,11,576,49,554],955:[694,12,583,47,556],956:[442,216,603,23,580],957:[442,2,494,45,530],958:[704,205,438,21,443],959:[441,11,485,34,476],960:[431,11,570,19,573],961:[442,216,517,23,510],962:[442,107,363,31,405],963:[431,11,571,31,572],964:[431,13,437,18,517],965:[443,10,540,21,523],966:[442,218,654,50,618],967:[442,204,626,25,600],968:[694,205,651,21,634],969:[443,11,622,15,604],977:[705,11,591,21,563],981:[694,205,596,43,579],982:[431,10,828,19,823],1009:[442,194,517,67,510],1013:[431,11,406,40,382]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"MathJax_Greek-italic"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Greek/Italic/Main.js"]);
