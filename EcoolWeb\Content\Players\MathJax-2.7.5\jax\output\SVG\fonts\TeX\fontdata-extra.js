/*
 *  /MathJax/jax/output/SVG/fonts/TeX/fontdata-extra.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

(function(a){var n="2.7.5";var m=a.FONTDATA.DELIMITERS;var h="MathJax_Main",i="MathJax_Main-bold",k="MathJax_AMS",f="MathJax_Size1",b="MathJax_Size4";var l="H",g="V";var j=[8722,h,0,0,0,-0.31,-0.31];var d=[61,h,0,0,0,0,0.1];var e={61:{dir:l,HW:[[767,h]],stretch:{rep:[61,h]}},8606:{dir:l,HW:[[1000,k]],stretch:{left:[8606,k],rep:j}},8608:{dir:l,HW:[[1000,k]],stretch:{right:[8608,k],rep:j}},8612:{dir:l,HW:[],stretch:{min:1,left:[8592,h],rep:j,right:[8739,f,0,-0.05,0.9]}},8613:{dir:g,HW:[],stretch:{min:0.6,bot:[8869,i,0,0,0.75],ext:[9168,f],top:[8593,f]}},8614:{dir:l,HW:[[1000,h]],stretch:{left:[8739,f,-0.09,-0.05,0.9],rep:j,right:[8594,h]}},8615:{dir:g,HW:[],stretch:{min:0.6,top:[8868,i,0,0,0.75],ext:[9168,f],bot:[8595,f]}},8624:{dir:g,HW:[[722,k]],stretch:{top:[8624,k],ext:[9168,f,0.097]}},8625:{dir:g,HW:[[722,k]],stretch:{top:[8625,k,0.27],ext:[9168,f]}},8636:{dir:l,HW:[[1000,h]],stretch:{left:[8636,h],rep:j}},8637:{dir:l,HW:[[1000,h]],stretch:{left:[8637,h],rep:j}},8638:{dir:g,HW:[[888,k]],stretch:{top:[8638,k,0.12,0,1.1],ext:[9168,f]}},8639:{dir:g,HW:[[888,k]],stretch:{top:[8639,k,0.12,0,1.1],ext:[9168,f]}},8640:{dir:l,HW:[[1000,h]],stretch:{right:[8640,h],rep:j}},8641:{dir:l,HW:[[1000,h]],stretch:{right:[8641,h],rep:j}},8642:{dir:g,HW:[[888,k]],stretch:{bot:[8642,k,0.12,0,1.1],ext:[9168,f]}},8643:{dir:g,HW:[[888,k]],stretch:{bot:[8643,k,0.12,0,1.1],ext:[9168,f]}},8666:{dir:l,HW:[[1000,k]],stretch:{left:[8666,k],rep:[8801,h]}},8667:{dir:l,HW:[[1000,k]],stretch:{right:[8667,k],rep:[8801,h]}},9140:{dir:l,HW:[],stretch:{min:0.5,left:[9484,k,0,-0.1],rep:[8722,h,0,0.325],right:[9488,k,0,-0.1]}},9141:{dir:l,HW:[],stretch:{min:0.5,left:[9492,k,0,0.26],rep:[8722,h,0,0,0,0.25],right:[9496,k,0,0.26]}},9180:{dir:l,HW:[[778,k,0,8994],[100,h,0,8994]],stretch:{left:[57680,b],rep:[57684,b],right:[57681,b]}},9181:{dir:l,HW:[[778,k,0,8995],[100,h,0,8995]],stretch:{left:[57682,b],rep:[57684,b],right:[57683,b]}},9184:{dir:l,HW:[],stretch:{min:1.25,left:[714,h,-0.1],rep:[713,h,-0.05,0.13],right:[715,h],fullExtenders:true}},9185:{dir:l,HW:[],stretch:{min:1.5,left:[715,h,-0.1,0.1],rep:[713,h,-0.1],right:[714,h,-0.1,0.1],fullExtenders:true}},10502:{dir:l,HW:[],stretch:{min:1,left:[8656,h],rep:d,right:[8739,f,0,-0.1]}},10503:{dir:l,HW:[],stretch:{min:0.7,left:[8872,k,0,-0.12],rep:d,right:[8658,h]}},10574:{dir:l,HW:[],stretch:{min:0.5,left:[8636,h],rep:j,right:[8640,h]}},10575:{dir:g,HW:[],stretch:{min:0.5,top:[8638,k,0.12,0,1.1],ext:[9168,f],bot:[8642,k,0.12,0,1.1]}},10576:{dir:l,HW:[],stretch:{min:0.5,left:[8637,h],rep:j,right:[8641,h]}},10577:{dir:g,HW:[],stretch:{min:0.5,top:[8639,k,0.12,0,1.1],ext:[9168,f],bot:[8643,k,0.12,0,1.1]}},10586:{dir:l,HW:[],stretch:{min:1,left:[8636,h],rep:j,right:[8739,f,0,-0.05,0.9]}},10587:{dir:l,HW:[],stretch:{min:1,left:[8739,f,-0.05,-0.05,0.9],rep:j,right:[8640,h]}},10588:{dir:g,HW:[],stretch:{min:0.7,bot:[8869,i,0,0,0.75],ext:[9168,f],top:[8638,k,0.12,0,1.1]}},10589:{dir:g,HW:[],stretch:{min:0.7,top:[8868,i,0,0,0.75],ext:[9168,f],bot:[8642,k,0.12,0,1.1]}},10590:{dir:l,HW:[],stretch:{min:1,left:[8637,h],rep:j,right:[8739,f,0,-0.05,0.9]}},10591:{dir:l,HW:[],stretch:{min:1,left:[8739,f,-0.05,-0.05,0.9],rep:j,right:[8641,h]}},10592:{dir:g,HW:[],stretch:{min:0.7,bot:[8869,i,0,0,0.75],ext:[9168,f],top:[8639,k,0.12,0,1.1]}},10593:{dir:g,HW:[],stretch:{min:0.7,top:[8868,i,0,0,0.75],ext:[9168,f],bot:[8643,k,0.12,0,1.1]}}};for(var c in e){if(e.hasOwnProperty(c)){m[c]=e[c]}}MathJax.Ajax.loadComplete(a.fontDir+"/fontdata-extra.js")})(MathJax.OutputJax.SVG);
