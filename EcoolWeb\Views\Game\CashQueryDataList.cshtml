﻿@model GameCashQueryViewModel
@{
    /**/

    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
    EcoolWeb.Models.UserProfileHelper.SetLayoutCookieData(Layout);
}
<link href="~/Content/css/childrens-month.css?v=2" rel="stylesheet" />
<script src="~/Scripts/jquery.basictable.js"></script>
<div class="timer"></div>

@using (Html.BeginForm("CashQuery", "Game", FormMethod.Post, new { id = "form1", name = "form1" }))
{
    @Html.AntiForgeryToken()
    @Html.HiddenFor(m => m.GAME_NO)
    @Html.HiddenFor(m => m.GameUserID)
    @Html.HiddenFor(m => m.FromSourcePage)

    @Html.Hidden("StatusMessage", TempData["StatusMessage"])

    <div id="MainView">
        <div class="row">
            <div class="col-md-8 col-md-offset-2">
                <div class="panel with-nav-tabs panel-info">
                    <div class="panel-heading">

                        @if (Model.FromSourcePage != GameCashQueryViewModel.SourcePage.QueryUserGameData)
                        {

                            if (Model.FromSourcePage != GameCashQueryViewModel.SourcePage.QueryUserGameData)
                            {
                                if (string.IsNullOrWhiteSpace((string)(TempData["StatusMessage"] ?? "")))
                                {



                                    <h1> 過關(點數)查詢                                      <a style="padding:5px 5px 5px 5px" role="button" class="btn btn-default" onclick="funGetExchange()"><i class="fa fa-search" aria-hidden="true"></i> 回查詢首頁</a></h1>
                                    @*<div style="height:25px"></div>
                    <div class="row">
                        <div class="col-md-12  text-center">


                        </div>
                    </div>
                    <br />*@
                                }
                            }
                            else
                            {


                                <h1> 過關(點數)查詢</h1>

                            }
                        }
                        else
                        {
                            if (Model.FromSourcePage != GameCashQueryViewModel.SourcePage.QueryUserGameData)
                            {
                                if (string.IsNullOrWhiteSpace((string)(TempData["StatusMessage"] ?? "")))
                                {



                                <h1> 現況查詢                                     <a style="padding:5px 5px 5px 5px" role="button" class="btn btn-default" onclick="funGetExchange()"><i class="fa fa-search" aria-hidden="true"></i> 回查詢首頁</a></h1>
                                    @*<div style="height:25px"></div>
                    <div class="row">
                        <div class="col-md-12  text-center">


                        </div>
                    </div>
                    <br />*@
                                }
                            }
                            else
                            {


                                    <h1> 現況查詢</h1>

                            }
                           
                        }
                    </div>
                    <div class="panel-body">
                        @if (!string.IsNullOrWhiteSpace((string)(TempData["StatusMessage"] ?? "")))
                        {
                            <h1 style="color:red">@Html.Raw(HttpUtility.HtmlDecode((string)TempData["StatusMessage"]))</h1>
                        }
                        else
                        {
                            if (Model.User != null)
                            {
                                <div class="row">
                                    <div class="col-md-6">
                                        <strong class="childrens-user" style="font-size:26px">
                                            @Model.User.NAME

                                            @if (Model.User.STATUS == (byte)ADDT27.StatusVal.停用)
                                            {
                                                <font color="red">(停用)</font>
                                            }
                                        </strong>
                                    </div>
                                    <div class="col-md-6">
                                        <strong class="childrens-val danger text-right" style="font-size:26px">
                                            <font style="color:blue">
                                                點數：@Model.CASH_AVAILABLE &nbsp;&nbsp;&nbsp;&nbsp;
                                                @if (Model.GameInfo.GAME_TYPE == (byte)ADDT26.GameType.有獎徵答)
                                                {
                                                    @:分數：
                                                    if (Model.MeMAns != null && Model.MeMAns.Count() != 0)
                                                    {
                                                        @Model.MeMAns.FirstOrDefault().SCORE
                                                    }
                                                }
                                                else
                                                {
                                                    @:已完成 @(Model.LevelPassCount) 關
                                                }
                                            </font>
                                        </strong>
                                    </div>
                                </div>
                                <hr>
                               
                                <div class="childrens-awards-block">
                                    <div class="title-bar" style="width:320px">
                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 343.47 253.48" class="svg-award">
                                            <title>award</title>
                                            <path d="M150.46,153.53c8.67,5.51,9.42,39.5,19.59,39.95,9.85.44,12.55-33.21,21.85-38.14s38.66,11.74,43.83,3.33c5.33-8.66-22.37-28.37-22.06-38.63.29-9.94,29.23-27.13,24.22-36.59S202.41,88.29,194,83c-8.67-5.51-9.42-39.49-19.59-39.95-9.85-.44-12.55,33.21-21.85,38.14s-38.66-11.73-43.83-3.33c-5.33,8.66,22.37,28.37,22.06,38.63-.29,10-29.23,27.14-24.22,36.6S142.07,148.2,150.46,153.53Zm14-49.88A16.51,16.51,0,1,1,157.65,126,16.51,16.51,0,0,1,164.5,103.65Z" />
                                            <path d="M67.09,151.71l-5.64-16.2a29.59,29.59,0,0,0-12.81,35.17L56,191.77c.78.19,1.53.44,2.29.67a42.1,42.1,0,0,1-.7-10.12A41,41,0,0,1,68.32,156.8,30.42,30.42,0,0,0,67.09,151.71Z" />
                                            <path d="M59.77,118.31l1-17.14a29.64,29.64,0,0,0-25.29,27.59l-1.28,22.32c.64.47,1.25,1,1.85,1.5a41.77,41.77,0,0,1,3.24-9.64A41.09,41.09,0,0,1,59,123.47,30.25,30.25,0,0,0,59.77,118.31Z" />
                                            <path d="M43,60.89c.12.79.19,1.58.26,2.37a41.58,41.58,0,0,1,9.08-4.51A41,41,0,0,1,80,58.9a30.26,30.26,0,0,0,4.23-3.08L97.07,44.4A29.61,29.61,0,0,0,59.68,46Z" />
                                            <path d="M20.28,144.35c.81.22,1.61.49,2.4.76l1-17a29.4,29.4,0,0,0-7.45-21.37c-.57-.64-1.21-1.18-1.82-1.76-1.12-.84-2.2-1.72-3.22-2.65a29,29,0,0,0-9.48-4.51l-1,17.13A29.56,29.56,0,0,0,20.28,144.35Z" />
                                            <path d="M43.06,190.66l-5.6-16.08a29.56,29.56,0,0,0-17.08-17.77,41.68,41.68,0,0,1-4.73-1.43A29.1,29.1,0,0,0,5.57,155l5.65,16.2a29.53,29.53,0,0,0,28.84,19.72C41.05,190.79,42.06,190.72,43.06,190.66Z" />
                                            <path d="M47.62,202.41a28.27,28.27,0,0,0-3.19,0c-.74.1-1.5.13-2.24.19a29.33,29.33,0,0,0-11.14,3.73l11.43,12.81a29.61,29.61,0,0,0,36.94,5.88L68,212.26A29.32,29.32,0,0,0,47.62,202.41Z" />
                                            <path d="M56.25,69.92A29.39,29.39,0,0,0,39.35,85l-9.72,20.12c.42.69.78,1.41,1.16,2.13a41.45,41.45,0,0,1,32.3-18.17,28.93,28.93,0,0,0,2.73-4.46l7.46-15.44A29.56,29.56,0,0,0,56.25,69.92Z" />
                                            <path d="M21.28,95.19l7.41-15.35a29.5,29.5,0,0,0,.47-24.66,41.51,41.51,0,0,1-2.28-4.26A29.29,29.29,0,0,0,20,43.44L12.53,58.88a29.56,29.56,0,0,0,6.78,34.66C20,94.07,20.63,94.63,21.28,95.19Z" />
                                            <path d="M39.1,48.53,51.81,37.2a29.41,29.41,0,0,0,9.84-20.39A29.41,29.41,0,0,0,57.7.24L44.89,11.66a29.43,29.43,0,0,0-7.38,33.92C38.08,46.54,38.6,47.52,39.1,48.53Z" />
                                            <path d="M88.27,217.18a29.61,29.61,0,0,0-1.63-37.39L75.22,167a29.59,29.59,0,0,0,1.62,37.39Z" />
                                            <path d="M308.43,152.58c.61-.51,1.21-1,1.85-1.51L309,128.76a29.64,29.64,0,0,0-25.28-27.59l1,17.13a29.92,29.92,0,0,0,.81,5.17,41,41,0,0,1,22.92,29.11Z" />
                                            <path d="M314.84,105.11,305.12,85s0,0,0,0a29.58,29.58,0,0,0-33.9-15.77l7.46,15.45a29.73,29.73,0,0,0,2.73,4.46,41.38,41.38,0,0,1,32.31,18.14C314.07,106.52,314.43,105.8,314.84,105.11Z" />
                                            <path d="M330.08,104.94c-.61.58-1.25,1.13-1.81,1.77a29.36,29.36,0,0,0-7.46,21.37l1,17c.78-.26,1.56-.53,2.36-.74a29.56,29.56,0,0,0,19.62-29.45l-1-17.13a29,29,0,0,0-9.48,4.5A39.07,39.07,0,0,1,330.08,104.94Z" />
                                            <path d="M288.48,191.77l7.35-21.08A29.59,29.59,0,0,0,283,135.52l-5.64,16.2a30,30,0,0,0-1.23,5.09,41.12,41.12,0,0,1,10.74,25.52,42.1,42.1,0,0,1-.7,10.12C287,192.21,287.71,192,288.48,191.77Z" />
                                            <path d="M315.78,79.84l6.42,13.31,1,2c.64-.56,1.29-1.14,2-1.66a29.55,29.55,0,0,0,6.77-34.65l-7.46-15.44a29.45,29.45,0,0,0-6.89,7.48,41.89,41.89,0,0,1-2.29,4.28,29.5,29.5,0,0,0,.46,24.61A0,0,0,0,1,315.78,79.84Z" />
                                            <path d="M264.44,58.9a41.16,41.16,0,0,1,36.79,4.38c.07-.79.13-1.59.25-2.38L284.79,46A29.61,29.61,0,0,0,247.4,44.4l12.81,11.42A29.68,29.68,0,0,0,264.44,58.9Z" />
                                            <path d="M257.83,179.8a29.6,29.6,0,0,0-1.63,37.38l11.43-12.8A29.59,29.59,0,0,0,269.25,167Z" />
                                            <path d="M300.15,202.45a27.93,27.93,0,0,0-3.3,0,29.37,29.37,0,0,0-20.39,9.84l-11.41,12.8a29.65,29.65,0,0,0,37-5.88l11.42-12.81a29.4,29.4,0,0,0-11.26-3.74C301.49,202.57,300.82,202.54,300.15,202.45Z" />
                                            <path d="M328.29,155.54a38.73,38.73,0,0,1-4.05,1.23A29.57,29.57,0,0,0,307,174.58l-5.61,16.08c1,.06,2,.13,3,.27a29.52,29.52,0,0,0,28.83-19.73L338.9,155A29.27,29.27,0,0,0,328.29,155.54Z" />
                                            <path d="M305.39,48.54c.46-.91.92-1.82,1.44-2.7a29.45,29.45,0,0,0-7.25-34.19L286.77.23a29.26,29.26,0,0,0-3.95,16.58,29.31,29.31,0,0,0,9.84,20.38Z" />
                                            <path d="M248,223.54a174.1,174.1,0,0,1-151.57,0,47.54,47.54,0,0,1-9.26,8.6c13.11,7,43.8,20.59,85.05,20.59s71.93-13.58,85.05-20.59A47.31,47.31,0,0,1,248,223.54Z" />
                                        </svg>

                                        <span style="font-size:26px">我的提醒</span>
                                    </div>
                                    @if (Model.GameInfo.GAME_TYPE == (byte)ADDT26.GameType.一般)
                                    {
                                        if (Model.LotteryDetails?.Count > 0)
                                        {
                                            foreach (var item in Model.LotteryDetails.OrderBy(a => a.ITEM_NO))
                                            {

                                                @:恭喜獲得  <span style="color:red">@item.LOTTERY_DESC</span>

                                                if (item.RECEIVE_AWARD)
                                                {
                                                    <span style="color:blue">(已領獎)</span>
                                                    @:已領獎
                                                }
                                                else
                                                {
                                                    <span style="color:blue">(尚未領獎)</span>
                                                }
                                                <br />
                                            }
                                        }
                                        else
                                        {
                                            @:你尚未有中獎紀錄
                                            <br />
                                        }

                                        if (Model.PrizeLevel != null)
                                        {
                                            foreach (var Prize in Model.PrizeLevel)
                                            {

                                                if (Prize.IsPrize)
                                                {
                                                    @:你已經達到領 <span style="color:red">@Prize.LEVEL_NAME</span>  的資格
                                                    <span style="color:blue">(已領取)</span>
                                                    <br />
                                                }
                                                else
                                                {
                                                    if (Prize.CASH < 0)
                                                    {

                                                        if (Model.CASH_AVAILABLE >= -Prize.CASH)
                                                        {
                                                            @:你已經達到領 <span style="color:red">@Prize.LEVEL_NAME</span>  的資格
                                                            <span style="color:blue">(尚未領取)</span>

                                                            <br />
                                                        }
                                                        else
                                                        {
                                                            @:你還未達 <span style="color:red">@Prize.LEVEL_NAME</span> 的資格
                                                            <br />
                                                        }
                                                    }
                                                    else
                                                    {
                                                        if (Model.CASH_AVAILABLE >= Prize.CASH)
                                                        {
                                                            @:你已經達到領 <span style="color:red">@Prize.LEVEL_NAME</span>  的資格
                                                            <span style="color:blue">(尚未領取)</span>

                                                            <br />
                                                        }
                                                        else
                                                        {
                                                            @:你還未達 <span style="color:red">@Prize.LEVEL_NAME</span> 的資格
                                                            <br />
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else if (Model.GameInfo.GAME_TYPE == (byte)ADDT26.GameType.有獎徵答)
                                    {
                                        if (!string.IsNullOrEmpty(Model.REWARD_DESC))
                                        {
                                            <font>@Model.REWARD_DESC</font>
                                        }
                                        else
                                        {
                                            <font>答題未完成</font>
                                        }
                                    }
                                </div>

                                <hr>

                                <div class="childrens-info-block">
                                    <div class="title-bar" style="font-size:22px">
                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 343.47 253.48" class="svg-map">
                                            <title>path</title>
                                            <path d="M305.94,168.21a34.34,34.34,0,0,0-28.22-14.79h-78.6a20.66,20.66,0,0,0-39.85,0H74.13a23.19,23.19,0,0,1-15-5.52,20.66,20.66,0,0,0,.8-36.33,23.29,23.29,0,0,1,14.24-4.86h85.14a20.66,20.66,0,0,0,39.85,0h78.6a34.41,34.41,0,0,0,33.47-26.62,20.67,20.67,0,0,0-5.41-38.21,34.64,34.64,0,0,0-3.86-4.56,34,34,0,0,0-24.44-10c-28.32.21-54.46.37-78.18.49A20.67,20.67,0,0,0,159,28c-41.07.14-72,.11-91-.09a20.67,20.67,0,1,0-.13,11l5.25,0c15.2.13,36.71.17,63.94.12L159.52,39a20.69,20.69,0,0,0,39.41-.17c31.07-.16,60-.36,78.63-.49h.18a23.15,23.15,0,0,1,14.46,5,20.66,20.66,0,0,0,6.53,39.17,23.36,23.36,0,0,1-21,13.16h-78.6a20.67,20.67,0,0,0-39.85,0H74.13A34.31,34.31,0,0,0,47,109a20.66,20.66,0,0,0-.91,40.86,34.32,34.32,0,0,0,28,14.54h85.14a20.67,20.67,0,0,0,39.85,0h78.6a23.24,23.24,0,0,1,14.64,5.17,20.67,20.67,0,0,0,.83,37.82,23.23,23.23,0,0,1-15.47,5.89H198.79a20.67,20.67,0,0,0-39.19,0H68.14a20.67,20.67,0,1,0,.61,11H159a20.68,20.68,0,0,0,40.41,0h78.32a34.38,34.38,0,0,0,29.09-16.11,20.67,20.67,0,0,0-.87-40ZM179.19,41.92a8.71,8.71,0,1,1,8.71-8.71A8.71,8.71,0,0,1,179.19,41.92Zm0,50.58a8.71,8.71,0,1,1-8.71,8.71A8.71,8.71,0,0,1,179.19,92.5ZM49.63,138.23a8.71,8.71,0,1,1,8.71-8.71A8.71,8.71,0,0,1,49.63,138.23Zm129.56,29.4a8.71,8.71,0,1,1,8.71-8.71A8.71,8.71,0,0,1,179.19,167.63Zm0,61a8.71,8.71,0,1,1,8.71-8.71A8.71,8.71,0,0,1,179.19,228.59Z" />
                                        </svg>
                                        @if (Model.GameInfo.GAME_TYPE == (byte)ADDT26.GameType.一般)
                                        {
                                            <span>
                                                關卡資訊 ， 已完成 <span class="label label-success">@(Model.LevelPassCount)</span> 關
                                                ，未完成  <span class="label label-noplay"> @(Model.UnLevelPassCount)</span> 關
                                            </span>
                                            if (Model.UnLevelPassCount > 0)
                                            {

                                            }
                                            else
                                            {
                                                <font style="color:red">，恭喜你，全部的關卡都通過</font>
                                            }
                                        }
                                        else if (Model.GameInfo.GAME_TYPE == (byte)ADDT26.GameType.有獎徵答)
                                        {
                                            <text> 本次答題資訊 ， 已完成 <span class="label label-success">@(Model.LevelPassCount)</span> 題
                                            ，未完成  <span class="label label-noplay"> @(Model.UnLevelPassCount)</span> 題</text>
                                        }
                                    </div>

                                    <div class="row">

                                        @if (Model.GameInfo.GAME_TYPE == (byte)ADDT26.GameType.一般)
                                        {
                                            foreach (var item in Model.MeLevelDetails)
                                            {

                                                <div class="element col-md-12" style="color:red">
                                                    @if (item.MeIsLevelPass)
                                                    {
                                                        <div class="childrens-level childrens-level-success" style="height:95%;font-size:20px">
                                                            @item.LEVEL_NAME <span class="glyphicon glyphicon-ok" aria-hidden="true"></span>
                                                        </div>
                                                    }
                                                    else
                                                    {
                                                        <div class="childrens-level childrens-level-noplay" style="height:95%;font-size:20px">
                                                            @item.LEVEL_NAME
                                                        </div>
                                                    }
                                                </div>
                                            }
                                        }
                                        else if (Model.GameInfo.GAME_TYPE == (byte)ADDT26.GameType.有獎徵答)
                                        {
                                            if (Model.MeQALevel?.Count > 0)
                                            {
                                                foreach (var item in Model.MeQALevel)
                                                {
                                                    <div class="element col-md-12">
                                                        @if (item.MeIsLevelPass)
                                                        {
                                                            <div class="childrens-level childrens-level-success" style="height:95%;font-size:20px">
                                                                第@(item.G_ORDER_BY)題，@item.G_SUBJECT &nbsp;&nbsp;我回答:@(item.LEVEL_NAME)

                                                                @if (Model.UnLevelPassCount == 0)
                                                                {
                                                                    if (item.TRUE_ANS == false)
                                                                    {

                                                                        <font color="red">
                                                                            &nbsp;&nbsp; &nbsp;&nbsp; @item.RETURN_DESC
                                                                            <span class="glyphicon glyphicon-remove" style="float:right;"></span>
                                                                        </font>
                                                                    }
                                                                    else
                                                                    {
                                                                        <font color="#2200AA">
                                                                            <span class="glyphicon glyphicon-ok" style="float:right;"></span>
                                                                        </font>
                                                                    }
                                                                }
                                                            </div>
                                                        }
                                                        else
                                                        {
                                                            <div class="childrens-level childrens-level-noplay" style="height:95%;font-size:20px">
                                                                第@(item.G_ORDER_BY)題，@item.G_SUBJECT

                                                                @if (item.IsRecommendLevel)
                                                                {
                                                                    <span class="glyphicon glyphicon-random" style="float:right;"></span>
                                                                }
                                                            </div>
                                                        }
                                                    </div>
                                                }
                                            }
                                        }
                                    </div>
                                </div>

                                <hr>
                                if (Model.GameInfo.GAME_TYPE == (byte)ADDT26.GameType.有獎徵答)
                                {
                                    <div class="childrens-info-block">
                                        <div class="title-bar" style="width:320px">
                                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 343.47 253.48" class="svg-map">
                                                <title>path</title>
                                                <path d="M305.94,168.21a34.34,34.34,0,0,0-28.22-14.79h-78.6a20.66,20.66,0,0,0-39.85,0H74.13a23.19,23.19,0,0,1-15-5.52,20.66,20.66,0,0,0,.8-36.33,23.29,23.29,0,0,1,14.24-4.86h85.14a20.66,20.66,0,0,0,39.85,0h78.6a34.41,34.41,0,0,0,33.47-26.62,20.67,20.67,0,0,0-5.41-38.21,34.64,34.64,0,0,0-3.86-4.56,34,34,0,0,0-24.44-10c-28.32.21-54.46.37-78.18.49A20.67,20.67,0,0,0,159,28c-41.07.14-72,.11-91-.09a20.67,20.67,0,1,0-.13,11l5.25,0c15.2.13,36.71.17,63.94.12L159.52,39a20.69,20.69,0,0,0,39.41-.17c31.07-.16,60-.36,78.63-.49h.18a23.15,23.15,0,0,1,14.46,5,20.66,20.66,0,0,0,6.53,39.17,23.36,23.36,0,0,1-21,13.16h-78.6a20.67,20.67,0,0,0-39.85,0H74.13A34.31,34.31,0,0,0,47,109a20.66,20.66,0,0,0-.91,40.86,34.32,34.32,0,0,0,28,14.54h85.14a20.67,20.67,0,0,0,39.85,0h78.6a23.24,23.24,0,0,1,14.64,5.17,20.67,20.67,0,0,0,.83,37.82,23.23,23.23,0,0,1-15.47,5.89H198.79a20.67,20.67,0,0,0-39.19,0H68.14a20.67,20.67,0,1,0,.61,11H159a20.68,20.68,0,0,0,40.41,0h78.32a34.38,34.38,0,0,0,29.09-16.11,20.67,20.67,0,0,0-.87-40ZM179.19,41.92a8.71,8.71,0,1,1,8.71-8.71A8.71,8.71,0,0,1,179.19,41.92Zm0,50.58a8.71,8.71,0,1,1-8.71,8.71A8.71,8.71,0,0,1,179.19,92.5ZM49.63,138.23a8.71,8.71,0,1,1,8.71-8.71A8.71,8.71,0,0,1,49.63,138.23Zm129.56,29.4a8.71,8.71,0,1,1,8.71-8.71A8.71,8.71,0,0,1,179.19,167.63Zm0,61a8.71,8.71,0,1,1,8.71-8.71A8.71,8.71,0,0,1,179.19,228.59Z" />
                                            </svg>
                                            作答歷史記錄
                                        </div>
                                        <div>
                                            <table id="table-breakpoint" class="table table-striped">
                                                <thead>
                                                    <tr>
                                                        <th>
                                                            作答時間
                                                        </th>

                                                        <th>
                                                            成績
                                                        </th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach (var item in Model.MeMAns)
                                                    {
                                                        <tr>

                                                            <td data-th="@Html.DisplayNameFor(model => model.MeMAns.First().CRE_DATE)">
                                                                <span class="bt-content">
                                                                    @Html.DisplayFor(modelItem => item.CRE_DATE)
                                                                </span>
                                                            </td>
                                                            <td data-th="@Html.DisplayNameFor(model => model.MeMAns.First().SCORE)">
                                                                <span class="bt-content">
                                                                    @Html.DisplayFor(modelItem => item.SCORE)
                                                                </span>
                                                            </td>
                                                        </tr>
                                                    }
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                    <hr>
                                }

                                <div id="ALL" class="childrens-info-block">
                                    <div class="title-bar" style="width:320px">
                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 343.47 253.48" class="svg-map">
                                            <title>path</title>
                                            <path d="M305.94,168.21a34.34,34.34,0,0,0-28.22-14.79h-78.6a20.66,20.66,0,0,0-39.85,0H74.13a23.19,23.19,0,0,1-15-5.52,20.66,20.66,0,0,0,.8-36.33,23.29,23.29,0,0,1,14.24-4.86h85.14a20.66,20.66,0,0,0,39.85,0h78.6a34.41,34.41,0,0,0,33.47-26.62,20.67,20.67,0,0,0-5.41-38.21,34.64,34.64,0,0,0-3.86-4.56,34,34,0,0,0-24.44-10c-28.32.21-54.46.37-78.18.49A20.67,20.67,0,0,0,159,28c-41.07.14-72,.11-91-.09a20.67,20.67,0,1,0-.13,11l5.25,0c15.2.13,36.71.17,63.94.12L159.52,39a20.69,20.69,0,0,0,39.41-.17c31.07-.16,60-.36,78.63-.49h.18a23.15,23.15,0,0,1,14.46,5,20.66,20.66,0,0,0,6.53,39.17,23.36,23.36,0,0,1-21,13.16h-78.6a20.67,20.67,0,0,0-39.85,0H74.13A34.31,34.31,0,0,0,47,109a20.66,20.66,0,0,0-.91,40.86,34.32,34.32,0,0,0,28,14.54h85.14a20.67,20.67,0,0,0,39.85,0h78.6a23.24,23.24,0,0,1,14.64,5.17,20.67,20.67,0,0,0,.83,37.82,23.23,23.23,0,0,1-15.47,5.89H198.79a20.67,20.67,0,0,0-39.19,0H68.14a20.67,20.67,0,1,0,.61,11H159a20.68,20.68,0,0,0,40.41,0h78.32a34.38,34.38,0,0,0,29.09-16.11,20.67,20.67,0,0,0-.87-40ZM179.19,41.92a8.71,8.71,0,1,1,8.71-8.71A8.71,8.71,0,0,1,179.19,41.92Zm0,50.58a8.71,8.71,0,1,1-8.71,8.71A8.71,8.71,0,0,1,179.19,92.5ZM49.63,138.23a8.71,8.71,0,1,1,8.71-8.71A8.71,8.71,0,0,1,49.63,138.23Zm129.56,29.4a8.71,8.71,0,1,1,8.71-8.71A8.71,8.71,0,0,1,179.19,167.63Zm0,61a8.71,8.71,0,1,1,8.71-8.71A8.71,8.71,0,0,1,179.19,228.59Z" />
                                        </svg>
                                        點數歷史記錄
                                    </div>
                                    <div>
                                        <table id="table-breakpoint" class="table table-striped">
                                            <thead>
                                                <tr>
                                                    <th>
                                                        @Html.DisplayNameFor(model => model.CashDetails.First().LOG_TIME)
                                                    </th>

                                                    <th>
                                                        @Html.DisplayNameFor(model => model.CashDetails.First().LOG_DESC)
                                                    </th>
                                                    <th>
                                                        @Html.DisplayNameFor(model => model.CashDetails.First().CASH_IN)
                                                    </th>
                                                    <th>
                                                        @Html.DisplayNameFor(model => model.CashDetails.First().LOG_CASH_AVAILABLE)
                                                    </th>
                                                </tr>
                                            </thead>
                                            <tbody id="ShowTop3">
                                                @foreach (var item in Model.CashDetails.Take(3))
                                                {
                                                    <tr>

                                                        <td data-th="@Html.DisplayNameFor(model => model.CashDetails.First().LOG_TIME)">
                                                            <span class="bt-content">
                                                                @Html.DisplayFor(modelItem => item.LOG_TIME)
                                                            </span>
                                                        </td>
                                                        <td data-th="@Html.DisplayNameFor(model => model.CashDetails.First().LOG_DESC)">
                                                            <span class="bt-content">
                                                                @Html.DisplayFor(modelItem => item.LOG_DESC)
                                                            </span>
                                                        </td>
                                                        <td data-th="@Html.DisplayNameFor(model => model.CashDetails.First().CASH_IN)">
                                                            <span class="bt-content">
                                                                @Html.DisplayFor(modelItem => item.CASH_IN)
                                                            </span>
                                                        </td>
                                                        <td data-th="@Html.DisplayNameFor(model => model.CashDetails.First().LOG_CASH_AVAILABLE)">
                                                            <span class="bt-content">
                                                                @Html.DisplayFor(modelItem => item.LOG_CASH_AVAILABLE)
                                                            </span>
                                                        </td>
                                                    </tr>
                                                }
                                                @if (Model.CashDetails.Count() > 3)
                                                {
                                                    <tr>
                                                        <td colspan="4" class="text-right">
                                                            <button type="button" class="btn btn-xs btn-Basic" onclick="OnMore()"> <i class="fa fa-search-plus"></i> more</button>
                                                        </td>
                                                    </tr>
                                                }
                                            </tbody>
                                            <tbody id="ShowALL" style="display:none">
                                                @foreach (var item in Model.CashDetails)
                                                {
                                                    <tr>

                                                        <td data-th="@Html.DisplayNameFor(model => model.CashDetails.First().LOG_TIME)">
                                                            <span class="bt-content">
                                                                @Html.DisplayFor(modelItem => item.LOG_TIME)
                                                            </span>
                                                        </td>
                                                        <td data-th="@Html.DisplayNameFor(model => model.CashDetails.First().LOG_DESC)">
                                                            <span class="bt-content">
                                                                @Html.DisplayFor(modelItem => item.LOG_DESC)
                                                            </span>
                                                        </td>
                                                        <td data-th="@Html.DisplayNameFor(model => model.CashDetails.First().CASH_IN)">
                                                            <span class="bt-content">
                                                                @Html.DisplayFor(modelItem => item.CASH_IN)
                                                            </span>
                                                        </td>
                                                        <td data-th="@Html.DisplayNameFor(model => model.CashDetails.First().LOG_CASH_AVAILABLE)">
                                                            <span class="bt-content">
                                                                @Html.DisplayFor(modelItem => item.LOG_CASH_AVAILABLE)
                                                            </span>
                                                        </td>
                                                    </tr>
                                                }
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            }

                        }
                    </div>
                </div>
            </div>
        </div>
    </div>

}

@if (Model.FromSourcePage != GameCashQueryViewModel.SourcePage.QueryUserGameData)
{
    if (string.IsNullOrWhiteSpace((string)(TempData["StatusMessage"] ?? "")))
    {
        <div style="height:25px"></div>
        <div class="row">
            <div class="col-md-12  text-center">
                <a style="padding:5px 5px 5px 5px" role="button" class="btn btn-primary" onclick="onShowMail()"> <i class="fa fa-envelope" aria-hidden="true"></i>寄回自己信箱</a>
                <a style="padding:5px 5px 5px 5px" role="button" class="btn btn-default" onclick="funGetExchange()"><i class="fa fa-search" aria-hidden="true"></i> 回查詢首頁</a>
            </div>
        </div>
        <br />
    }
}
else
{
    <div style="height:25px"></div>
    <div class="row">
        <div class="col-md-12  text-center">
            <a role="button" class="btn btn-default" href="@Url.Action("QueryUserGameData","Game", new { GAME_NO= Model.GAME_NO })"><i class="fa fa-search" aria-hidden="true"></i> 回闖關查詢首頁</a>
            <a role="button" class="btn btn-default" href="@Url.Action("QueryUserDataList","Game", new {GAME_NO = Model.GAME_NO })"><i class="fa fa-search" aria-hidden="true"></i> 中獎查詢</a>
        </div>
    </div>
    <br />

}

<!-- Modal -->
<div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel">將此活動記錄寄Mail到自已信箱</h4>
            </div>
            <div class="modal-body">
                @Html.EditorFor(m => m.E_MAIL, new { htmlAttributes = new { @class = "form-control", @placeholder = "E_MAIL" } })
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" onclick="OnMailData()">確定</button>
                <button type="button" class="btn btn-default" data-dismiss="modal" id="close">關閉</button>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-6 col-lg-offset-3">
        <div class="use-absolute" id="ErrorDiv">
            <div class="use-absoluteDiv">
                <div class="alert alert-danger" role="alert">
                    <h1>
                        <i class="fa fa-exclamation-circle"></i>
                        <strong id="ErrorStr"></strong>
                    </h1>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script language="JavaScript">
        var targetFormID = '#form1'

        var keyin13 = 0;

        $(document).ready(function () {
            $('#table-breakpoint').basictable({
                breakpoint: 768
            });

            if ($('.element').is()) {
                $('.element').responsiveEqualHeightGrid();
            }

              if ($('#@Html.IdFor(m=>m.FromSourcePage)').val() != '@GameCashQueryViewModel.SourcePage.QueryUserGameData') {
                  $(document).on('keypress', function (e) {
                      if (e.which == 13) {

                          keyin13++;

                          if (keyin13>=2) {
                              $('#ErrorStr').html('轉頁中，請稍候…轉頁後請重新感應。')
                              $('#ErrorDiv').show()
                              setTimeout(function () {
                                  console.log('轉頁中，請稍候…轉頁後請重新感應。');
                                  $('#ErrorDiv').fadeOut(2000)
                                  funGetExchange()
                              }, 3000);
                          }
                      }
                  });
             }

        });

        function funGetExchange() {
            console.log('轉頁中，funGetExchange');
            $('#@Html.IdFor(m=>m.GameUserID)').val('')
            $(targetFormID).submit();
        }

        function onShowMail() {
           $('#myModal').modal('show');
        }

        function OnMailData() {

            if ($('#@Html.IdFor(m=>m.E_MAIL)').val() == '') {
                alert('請輸入E-MAIL')
                return false;
            }

            if (validateEmail($('#@Html.IdFor(m=>m.E_MAIL)').val()) == false) {
                alert('E-MAIL格式不正確');
                return false;
            }

            $('#myModal').modal('hide');

             $.ajax({
                url: "@(Url.Action("GameMailData", "Game"))",     // url位置
                type: 'post',                   // post/get
                data: $(targetFormID).serialize(),
                dataType: 'json',               // xml/json/script/html
                cache: false,                   // 是否允許快取
                success: function (data) {
                    var res = jQuery.parseJSON(data);

                    if (res.Success == 'false') {
                        $('#ErrorStr').html(res.Error)
                        $('#ErrorDiv').show()
                        setTimeout(function () { $('#ErrorDiv').fadeOut(2000) }, 3000);
                    }
                    else {
                        $('#ErrorStr').html('發送成功')
                        $('#ErrorDiv').show()
                        FunPageProc(1)
                        setTimeout(function () {
                            $('#ErrorDiv').fadeOut(1000)
                        }, 2000);
                    }
                },
                error: function (xhr, err) {
                    alert("readyState: " + xhr.readyState + "\nstatus: " + xhr.status);
                    alert("responseText: " + xhr.responseText);
                }
            });
        }

        function validateEmail(sEmail) {
            var re = /\S+@@\S+\.\S+/;
            return re.test(sEmail);
        }

        function OnMore() {
            $('#ShowTop3').hide()
            $('#ShowALL').show()
        }

        //指定多少毫秒無動作將跳轉
        var timeout = 4000;

        if ($('#StatusMessage').val()!='') {
            timeout = 2000;
        }

        //記錄當前時間
        var occurtime = new Date().getTime();

        //更新最新動作時間(滑鼠)
        document.onmousemove = function () {
            occurtime = new Date().getTime();
        }

        //更新最新動作時間(鍵盤)
        document.onkeydown = function () {
            occurtime = new Date().getTime();
        }

        //判斷無動作時間並跳轉
        function goUrl() {

            @if (Model.FromSourcePage == GameCashQueryViewModel.SourcePage.QueryUserGameData ) {
                <text> return; </text>
            }
            var a = parseInt(new Date().getTime() - occurtime);

            if (a > timeout) {
                funGetExchange();
            }
        }
        console.log('goUrl()，funGetExchange');
        if ($('#@Html.IdFor(m=>m.FromSourcePage)').val() != '@GameCashQueryViewModel.SourcePage.QueryUserGameData') {
            window.setInterval("goUrl()",100);
        }
    </script>
}