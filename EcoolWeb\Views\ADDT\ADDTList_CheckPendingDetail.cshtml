﻿@using ECOOL_APP.com.ecool.Models.entity;
@model IEnumerable<uADDT06>

@{
    //string ReadOnly = (Request["Mode"].ToString() == "Del") ? "READONLY" : "";
    string Rule = (Request["Rule"] != null) ? Request["Rule"].ToString() : "";
    string PASSPORT_YN = (Model.FirstOrDefault().PASSPORT_YN == "N") ? "否" : "是";
    bool SHARE_Y = (Model.FirstOrDefault().SHARE_YN == "y") ? true : false;
    bool SHARE_N = (Model.FirstOrDefault().SHARE_YN.ToLower() == "n" || string.IsNullOrWhiteSpace(Model.FirstOrDefault().SHARE_YN) ) ? true : false;
    ViewBag.Title = "閱讀認證-待批閱認證明細";
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    if (string.IsNullOrWhiteSpace(UUID) == false)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
    var Search = TempData["Search"] as EcoolWeb.Models.ADDT06ViewModel;
}
<script src="~/Content/ckeditor/ckeditor.js"></script>
<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
<script src="~/Content/ckeditor/ckeditor.js"></script>
<script src="~/Content/colorbox/jquery.colorbox.js"></script>

@Html.Partial("_Title_Secondary")

<div style="width:600px">
    @{string Explain = ViewBag.ADDT06TEXPLAIN;}
    @Html.Raw(HttpUtility.HtmlDecode(@Explain))
</div>



<img src="~/Content/img/web-Bar-09.png" style="width:100%" class="img-responsive App_hide" alt="Responsive image" />
@using (Html.BeginForm(null, null, FormMethod.Post, new { name = "contentForm", id = "contentForm" }))
{

    @Html.Hidden("BackAction", Search.BackAction)
    @Html.Hidden("whereKeyword", Search.whereKeyword)
    @Html.Hidden("OrdercColumn", Search.OrdercColumn)
    @Html.Hidden("whereUserNo", Search.whereUserNo)
    @Html.Hidden("whereAPPLY_STATUS", Search.whereAPPLY_STATUS)
    @Html.Hidden("whereCLASS_NO", Search.whereCLASS_NO)
    @Html.Hidden("whereGrade", Search.whereGrade)
    @Html.Hidden("whereBOOK_NAME", Search.whereBOOK_NAME)
    @Html.Hidden("Page", Search.Page)
    <input type="hidden" id="Mode" name="Mode" value="@Request["Mode"]" />
    <input type="hidden" id="APPLY_NO" name="APPLY_NO" value="@Request["APPLY_NO"]" />
    <input type="hidden" id="Rule" name="Rule" value="@Rule" />

    <div class="Div-EZ-reader">
        <div class="form-horizontal">
            <div class="form-group">
                @Html.Label("是否為閱讀護照書籍", htmlAttributes: new { @class = "control-label-left label_dt col-md-4 col-sm-4 col-lg-4" })
                <div class="col-md-8 col-sm-8 col-lg-8">
                    <samp class="dd">
                        @PASSPORT_YN
                    </samp>
                </div>
            </div>
            <div class="form-group">
                @Html.Label("班級座號", htmlAttributes: new { @class = "control-label-left label_dt col-md-4 col-sm-4 col-lg-4" })
                <div class="col-md-8 col-sm-8 col-lg-8">
                    <samp class="dd">
                        @Model.FirstOrDefault().<EMAIL>().SEAT_NO
                    </samp>
                </div>
            </div>
            <div class="form-group">
                @Html.Label("姓名", htmlAttributes: new { @class = "control-label-left label_dt col-md-4 col-sm-4 col-lg-4" })
                <div class="col-md-8 col-sm-8 col-lg-8">
                    <samp class="dd">
                        @Model.FirstOrDefault().NAME
                    </samp>
                </div>
            </div>
            <div class="form-group">
                @Html.Label("閱讀書名", htmlAttributes: new { @class = "control-label-left label_dt col-md-4 col-sm-4 col-lg-4" })
                <div class="col-md-8 col-sm-8 col-lg-8">
                    <samp class="dd">
                        @Model.FirstOrDefault().BOOK_NAME
                    </samp>
                </div>
            </div>
            <div >
                @Html.Label("詳細內容:限制6000字(如果沒有上傳圖片，心得必須20個字以上)", htmlAttributes: new { @class = "control-label-left label_dt " })
            </div>
            <div >
                @Html.TextArea("txtARTICLE", @Model.FirstOrDefault().REVIEW, 15, 200, new { @class = "ckeditor" })
            </div>
            <div class="form-group">
                @Html.Label("原上傳圖檔", htmlAttributes: new { @class = "control-label-left label_dt col-md-4 col-sm-4 col-lg-4" })
            </div>
            <div>
                @if (string.IsNullOrWhiteSpace(ViewBag.ImageUrl) == false)
                {
                    if (ViewBag.ShowImg)
                    {
                        @Html.Action("_imagesRotateBtnView", "Comm", new { ImgURL = ViewBag.ImageUrl, ImgID = "group1" })
                    }

                    <img  src='@ViewBag.ImageUrl'  class="img-responsive " alt="Responsive image" id="group1" href="@ViewBag.ImageUrl" />
                }
               
            </div>

            @if (ViewBag.ShowImg)
            {
                <div class="form-group">
                    @Html.Label("上傳圖檔", htmlAttributes: new { @class = "control-label-left label_dt col-md-4 col-sm-4 col-lg-4" })
                    <div class="col-md-8 col-sm-8 col-lg-8">
                        @Html.Action("Upload", "Comm")
                    </div>
                </div>
            }
            @if (Rule.ToUpper() == "T" || Rule.ToUpper() == "A")
            {
                <div class="form-group">
                    @Html.Label("是否推薦", htmlAttributes: new { @class = "control-label-left label_dt col-md-4 col-sm-4 col-lg-4" })
                    <div class="col-md-8 col-sm-8 col-lg-8">
                        @if (ViewBag.ShowShare)
                        {
                            @Html.RadioButton("SHARE_YN", "y", SHARE_Y, new { htmlAttributes = new { @class = "form-control" } })
                            @Html.Label("推薦(酷幣10點)")
                            @Html.RadioButton("SHARE_YN", "n", SHARE_N, new { htmlAttributes = new { @class = "form-control" } })
                            @Html.Label("不推薦(酷幣5點)")
                        }
                        else
                        {
                            if (SHARE_Y)
                            {
                                <div>推薦</div>

                            }
                            @Html.Hidden("SHARE_YN", Model.FirstOrDefault().SHARE_YN)
                        }
                    </div>
                </div>
            }

            <div class="Div-btn-center">
                @if (Rule.ToUpper() == "T" || Rule.ToUpper() == "A")
                {
                    <button class="btn2 btn-default" type="button" id="btnSend" name="btnSend" onclick="btnSend_onclick();">@Resources.Resource.BookSuccess</button>

                    <samp>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</samp>
                }


                <a href='@Url.Action(Search.BackAction, "ADDT"
                                , new {
                                        whereKeyword = Search.whereKeyword,
                                        whereUserNo=Search.whereUserNo,
                                        whereBOOK_NAME=Search.whereBOOK_NAME,
                                        whereAPPLY_STATUS=Search.whereAPPLY_STATUS,
                                        OrdercColumn = Search.OrdercColumn,
                                        whereCLASS_NO= Search.whereCLASS_NO,
                                        whereGrade= Search.whereGrade,
                                        Page = Search.Page
                                })' class="btn2 btn-default" role="button">
                    @Resources.Resource.Back
                </a>
            </div>


          
        </div>
        <div class="Div-BK">
            <div class="Div-btn-center">
                @if (ViewBag.VisibleBACK == true || ViewBag.VisibleDEL == true)
                {
                    <div class="form-group text-left">
                        &nbsp;&nbsp;
                        @Html.Label("退回/作廢原因", htmlAttributes: new { @class = "control-label-left label_dt col-md-12 col-sm-12 col-lg-12" })
                        <div class="col-md-12 col-sm-12 col-lg-12">

                            @Html.DropDownList("BACK_MEMO_DropDownList", (IEnumerable<SelectListItem>)ViewBag.BackSelectItem, new { @class = "form-control", @onchange = "BackDropDownList(this.value)" })
                            @Html.Hidden("BACK_MEMO")
                        </div>
                    </div>
                        <div >
                            <div>
                                <br />
                                @if (ViewBag.VisibleBACK == true)
                                {
                                    <input type=button class="btn2 btn-default" name=DisableUpSetDraft value="退回再修" onclick="DisableGO(this, '@ADDT06.APPLY_STATUS_Val.APPLY_TYPE_Back')">
                                    <samp>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</samp>
                                }

                                @if (ViewBag.VisibleDEL == true)
                                {
                                    <input type=button class="btn2 btn-default" name=DisableUpSetDraft value="直接作廢" onclick="DisableGO(this,'@ADDT06.APPLY_STATUS_Val.APPLY_TYPE_DEL')">
                                }
                            </div>
                        </div>
                }
            </div>
        </div>

    </div>
}

@section Scripts
{
    <script type="text/javascript">

        $(document).ready(function () {
            $("#group1").colorbox({ rel: 'group1', opacity: 0.82 });
        });


        function btnSend_onclick() {
            document.contentForm.enctype = "multipart/form-data";
            document.contentForm.action = "ADDTList_CheckPendingDetailEDIT";
            document.contentForm.submit();
        }

        function btnSendRollBack_onclick() {
            $("#Mode").val('Del');
            btnSend_onclick();
        }

        function BackDropDownList(Val) {
            if (Val == '@ECOOL_APP.com.ecool.service.BDMT02Service.OtherVal') {
                $('#BACK_MEMO').val("")
                $('#BACK_MEMO').attr("type", "text").attr("placeholder", "請輸入原因").addClass("form-control");
            }
            else {
                $('#BACK_MEMO').attr("type", "hidden").removeClass();
                $('#BACK_MEMO').val(Val)
            }
        }

        function DisableGO(BtnThis, Mode) {
            var ErrorMsg = ''
            BtnThis.disabled = true;


            if ($('#BACK_MEMO').val() == '') {
                ErrorMsg = ErrorMsg + '請輸入退回/作廢原因'
            }

            if (ErrorMsg != '') {
                BtnThis.disabled = false;
                alert(ErrorMsg)
                return false;
            }
            else {
                var YN = confirm("你確定要「退回/作廢」這篇文章?")
                if (YN) {
                    $("#Mode").val(Mode);
                    btnSend_onclick();
                }
                else {
                    BtnThis.disabled = false;
                    return false;
                }
            }
        }


    </script>
}