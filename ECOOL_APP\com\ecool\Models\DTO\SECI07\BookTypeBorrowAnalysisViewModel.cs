﻿using ECOOL_APP.com.ecool.LogicCenter;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.com.ecool.Models.DTO
{
    public enum BorrowAnalysisType
    {
        <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
        <PERSON><PERSON><PERSON><PERSON>List,
        Grade<PERSON><PERSON>rowList,
        MonthBorrowList
    }

    public class BookTypeBorrowAnalysisViewModel
    {
        public string Where_SCHOOLNO { get; set; }
        [Display(Name ="學年度")]
        public string Where_SYEAR { get; set; }

        public Dictionary<BorrowAnalysisType, List<BookTypeBorrowAnalysis>> AnalysisListDictionary { get; set; }

        public BookTypeBorrowAnalysisViewModel()
        {
            AnalysisListDictionary = new Dictionary<BorrowAnalysisType, List<BookTypeBorrowAnalysis>>();
        }
    }

    public class BookTypeBorrowAnalysis
    {
        public int TYPE_ID { get; set; }
        public string TYPE_NAME { get; set; }
        public int SEX { get; set; }
        public int BORROW_MONTH { get; set; }
        public int QTY { get; set; }
        public int GRADE { get; set; }
        public double RATE { get; set; }
    }
}
