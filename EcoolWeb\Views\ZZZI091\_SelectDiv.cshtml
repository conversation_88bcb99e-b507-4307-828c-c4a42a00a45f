﻿<style>
    #progress-container {
        width: 100%;
        background-color: #ccc;
    }

    #progress-bar {
        width: 0%;
        height: 30px;
        background-color: #4caf50;
        text-align: center;
        line-height: 30px;
        color: white;
    }
</style>
@if (TempData["StatusMessage"] != null)
{
    @Html.Partial("_Notice")
}
else
{
    using (Html.BeginForm("Index", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "formEdit", name = "formEdit", enctype = "multipart/form-data" }))
    {
        {

            @Html.Hidden("wIsQhisSchool", (bool)ViewBag.wIsQhisSchool)
            <link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
            <script src="~/Content/colorbox/jquery.colorbox.js"></script>
            <label class="control-label"> * @(ViewBag.Panel_Title)</label>
            <br /><br />
            if (ViewBag.wIsQhisSchool == false)
            {
                //@Html.Hidden("SCHOOL_NO", (string)ViewBag.SCHOOL_NO)
                @Html.Hidden("IDNO", "")
                <div class="form-group">
                    @Html.Label("班級", htmlAttributes: new { @class = "control-label col-md-3", @for = "CLASS_NO" })
                    <div class="col-md-9">
                        @Html.DropDownList("CLASS_NO", (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control", @onchange = "ChangeUSER_NOUseReplaceWith()" })
                        @Html.ValidationMessage("CLASS_NO", "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group">
                    @Html.Label("姓名", htmlAttributes: new { @class = "control-label col-md-3", @for = "USER_NO" })
                    <div class="col-md-9">
                        @Html.DropDownList("USER_NO", (IEnumerable<SelectListItem>)ViewBag.USER_NOItems, new { @class = "form-control", onchange = "ChangeSCHOOL_NOUseReplaceWith()" })
                        @Html.ValidationMessage("USER_NO", "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group">
                    @{
                        var labelContent = "學校" + Environment.NewLine + "(轉學生可以選學校)";
                    }
                    <label class="control-label col-md-3" for="Where_SCHOOLNO">學校<br />(轉學生可以選學校)</label>

                    <div class="col-md-9">
                        @Html.DropDownList("SCHOOL_NO", (IEnumerable<SelectListItem>)ViewBag.SchoolItems, new { @class = "form-control", onchange = "$('#CLASS_NO').val('');this.form.submit();" })
                        @Html.ValidationMessage("SCHOOL_NO", "", new { @class = "text-danger" })
                    </div>
                </div>

                <div class="form-group">
                    @Html.Label("是否匯出閱讀認證", htmlAttributes: new { @class = "control-label col-md-3", @for = "CLASS_NO" })
                    <div class="col-md-9">
                        否<input type="checkbox" id="ReadYN" name="ReadYN" />
                    </div>

                </div>

                <div class="form-group">
                    @Html.Label("封面", htmlAttributes: new { @class = "control-label col-md-3", @for = "COVERJPG" })
                    <div class="col-md-9">
                        <input id="COVERJPG" name="COVERJPG" type="radio" value="coverA.jpg" checked>
                        <img style="Max-width:70px" class="cboxElement " id="cover" src="~/Content/img/coverA.jpg" href="~/Content/img/coverA.jpg" />
                        <input id="COVERJPG" name="COVERJPG" type="radio" value="coverB.jpg">
                        <img style="Max-width:70px" class="cboxElement  " id="cover" src="~/Content/img/coverB.jpg" href="~/Content/img/coverB.jpg" />
                        <input id="COVERJPG" name="COVERJPG" type="radio" value="coverC.jpg">
                        <img style="Max-width:70px" class="cboxElement " id="cover" src="~/Content/img/coverC.jpg" href="~/Content/img/coverC.jpg" />

                        <input id="COVERJPG" name="COVERJPG" type="radio" value="coverD.jpg">
                        <img style="Max-width:70px" class="cboxElement  " id="cover" src="~/Content/img/coverD.jpg" href="~/Content/img/coverD.jpg" />
                        <br />
                        <input id="COVERJPG" name="COVERJPG" type="radio" value="coverE.jpg">
                        <img style="Max-width:70px" class="cboxElement  " id="cover" src="~/Content/img/coverE.jpg" href="~/Content/img/coverE.jpg" />
                        <br />
                        <input id="COVERJPG" name="COVERJPG" type="radio" value="coverF.jpg"> <b>自行上傳封面(直式圖片)</b>
                        @*<input class="form-control input-md" id="COVERJPG" name="COVERJPG" type="file" value="">*@
                        <input class="form-control input-md" id="UploadCoverFile" name="UploadCoverFile" type="file" value="" onclick="checkRadio(this)">

                        <label class="text-info"><b style="font-size:20px">PS.請上傳 jpg、jpeg、png、gif、bmp 等格式的圖片;(長:1569px;寬1110px)</b></label>
                    </div>
                </div>
                <div class="form-group text-left">
                    @Html.Label("開始日期", htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-9">
                        @Html.Editor("S_DATE", new { htmlAttributes = new { @class = "form-control", @readonly = "readonly", @type = "text" } })
                    </div>
                </div>
                <div class="form-group text-left">
                    @Html.Label("結束日期", htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-9">
                        @Html.Editor("E_DATE", new { htmlAttributes = new { @class = "form-control", @readonly = "readonly", @type = "text" } })
                    </div>
                </div>
            }
            else
            {
                <div class="form-group">
                    @Html.Label("學校", htmlAttributes: new { @class = "control-label col-md-3", @for = "SCHOOL_NO" })
                    <div class="col-md-9">
                        @Html.DropDownList("SCHOOL_NO", (List<SelectListItem>)ViewBag.SCHOOL_NOItems, new { @class = "form-control" })
                        @Html.ValidationMessage("SCHOOL_NO", "", new { @class = "text-danger" })
                    </div>
                </div>


                @Html.Hidden("CLASS_NO", "")
                @Html.Hidden("USER_NO", "")
                @Html.Hidden("S_DATE", "")
                @Html.Hidden("E_DATE", "")
                @Html.Hidden("IDNO", (string)ViewBag.IDNO)
            }

            <div class="form-group">
                <div class="col-md-offset-3 col-md-9 text-center">
                    <button type="button" class="btn btn-default btn-block" id="BtnSave" onclick="CK1()">匯出</button>
                </div>
            </div>
            <div class="loader">
                <center>
                    <h1 class="loading-image">
                        <span class="glyphicon glyphicon-search"></span>查詢中，請稍後 ...
                    </h1>
                </center>
            </div>
        }
    }
    <div class="modal fade" id="myModal" role="dialog">
        <div class="modal-content">

            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">正在備份中，這步驟需要一段時間，會先跑完程式，才開始下載，請靜待。</h4>
            </div>
            <div id="counter"></div>
        </div>
        <div class="modal-body">
            <div class="progress progress-striped active">
                <div class="progress-bar progress-bar-success" id="barr" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width:0%;">

                    <span class="sr-only">0% 完成</span>
                </div>
            </div>
        </div>
        <div class="modal-footer">

            <button type="button" class="btn " data-dismiss="modal">Close </button>
        </div>
    </div>
    <script type="text/javascript">
        var targetFormID = '#formEdit';
        $("#S_DATE,#E_DATE").datepicker({
            dateFormat: "yy/mm/dd",
            changeMonth: true,
            changeYear: true,
            showOn: "button",
            buttonImage: "../Content/img/icon/calendar.gif",
            buttonImageOnly: true,
        });
        $(document).ready(function () {
            $('.loader').hide();
            $("#cover").colorbox({ opacity: 0.82 });
        });
        window.onload = function () {
            ChangeUSER_NOUseReplaceWith()
            $("#IDNO").val("@ViewBag.IDNO")
        }
        function checkRadio(obj) {

            $("input[value='coverF.jpg']").attr("checked", "checked")

                }
        function CK1() {
            $("#myModal").modal('show');
                    $(".modal-title").html("正在備份中，這步驟需要一段時間，會先跑完程式，才開始下載，請靜待。預估費時約5分鐘");
                    
            setTimeout(CK, 500);
            setTimeout(function () {
                $("#barr").css("width", "50%");
            
            }, 1500);
            setTimeout(function () { $("#myModal").modal('hide'); }, 3000);
                }
                async function CK() {
            var Msg = '';
            $('button').html("製作中，請稍後");
            $('button').attr("disabled", "disabled");
            if ($('#USER_NO').val() == '' && $('#IDNO').val() == '') {
                Msg = Msg + '請選擇「' + $("label[for='USER_NO']").text() + '」\n';
            }

            if (Msg != '') {
                alert(Msg);
            }
            else {

                var COVERJPGValue = "";
                COVERJPGValue = $('input[name*=COVERJPG]:checked').val();
                var formdata = new FormData($('form').get(0));
              //  $('#BtnSave').attr('disabled', 'disabled').html("Loading...資料驗証中…請勿其他動作");
                  $.ajax({
                url: '@Url.Action("ExportResultView", (string)ViewBag.BRE_NO)',
                data: formdata,
                processData: false,
                contentType: false,
                type: 'POST',
                async: false,
                dataType: 'json',               // xml/json/script/html
                cache: false,                   // 是否允許快取
                      success: function (data) {
                          $('.loader').hide();
                          var res = $.parseJSON(data);
                          console.log(res);
                      },
                      beforeSend: function () {
                          $('.loader').show()
                      },
                      complete: function () {
                          setTimeout(function () { $('.loader').hide(); }, 1000);
                      }

                  });
                var file = $("#UploadCoverFile").val();
                var fileName = getFileName(file);
                var StrWhere = '?School_No=' + $('#SCHOOL_NO').val() + '&USER_NO=' + $('#USER_NO').val() + '&S_DATE=' + $('#S_DATE').val() + '&E_DATE=' + $('#E_DATE').val() + '&IDNO=' + $('#IDNO').val() + '&COVERJPG=' + COVERJPGValue + '&UploadCoverFileName=' + fileName + '&redirect=s&ReadYN=' + $("#ReadYN").prop("checked")
                //var StrWhere = formdata;
                $('button').removeAttr('disabled');
                $('button').html("匯出");
                window.open('@Url.Action("ExportResultView")' + StrWhere + '', '_blank');
            }
        }
        function getFileName(o) {
            var pos = o.lastIndexOf("\\");
            return o.substring(pos + 1);
        }
        function SetUSER_NODDLEmpty() {
            $('#USER_NO').empty();
            $('#USER_NO').append($('<option></option>').val(' ').text('請選擇' + $("label[for='CLASS_NO']").text() + '...').prop('selected', true));
        }
           function ChangeSCHOOL_NOUseReplaceWith()

        {
               var selectedCLASS_NO = $.trim($('#CALSSNO option:selected').val());


            //   if (selectedCLASS_NO.length == 0) {
            //    SetUSER_NODDLEmpty();
            //}
               //else {
               var USER_NO = $.trim($('#USER_NO option:selected').val());
                $.ajax(
                {
                    url: '@Url.Action("_GetSCHOOL_NODDLHtml", (string)ViewBag.BRE_NO)',
                    data: {
                        tagId: 'SCHOOL_NO',
                        tagName: 'SCHOOL_NO',
                        wSCHOOL_NO: '@ViewBag.SCHOOL_NO',
                        wUSER_NO: USER_NO,
                        wCLASS_NO: selectedCLASS_NO,
                        wDATA_ANGLE_TYPE: '@ViewBag.DATA_ANGLE_TYPE'
                    },
                    type: 'post',
                    cache: false,
                    async: false,
                    dataType: 'html',
                    success: function (data) {
                        if (data.length > 0) {
                            $('#SCHOOL_NO').replaceWith(data);
                        }
                    }
                });
            //}
        }
        function ChangeUSER_NOUseReplaceWith() {

            var selectedCLASS_NO = $.trim($('#CLASS_NO option:selected').val());
            if (selectedCLASS_NO.length == 0) {
                SetUSER_NODDLEmpty();
            }
            else {

                $.ajax(
                {
                    url: '@Url.Action("_GetUSER_NODDLHtmlForALL", (string)ViewBag.BRE_NO)',
                    data: {
                        tagId: 'USER_NO',
                        tagName: 'USER_NO',
                        wSCHOOL_NO: '@ViewBag.SCHOOL_NO',
                        wUSER_NO: '@ViewBag.USER_NO',
                        wCLASS_NO: selectedCLASS_NO,
                        wIDNO : '@ViewBag.IDNO',
                        wDATA_ANGLE_TYPE: '@ViewBag.DATA_ANGLE_TYPE'
                    },
                    type: 'post',
                    cache: false,
                    async: false,
                    dataType: 'html',
                    success: function (data) {
                        if (data.length > 0) {
                            $("#IDNO").val('@ViewBag.slectIDNO');
                            $('#USER_NO').replaceWith(data);
                        }
                    }
                });
            }
        }
    </script>

}