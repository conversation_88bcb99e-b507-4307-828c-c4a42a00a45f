/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Latin-Modern/Size4/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.LatinModernMathJax_Size4={directory:"Size4/Regular",family:"LatinModernMathJax_Size4",testString:"\u00A0\u0302\u0303\u0306\u030C\u0311\u032C\u032D\u032E\u032F\u0330\u2016\u2044\u20E9\u221A",32:[0,0,332,0,0],40:[1146,646,597,179,539],41:[1146,646,597,58,418],47:[1722,1222,1222,56,1166],91:[1150,650,472,226,448],92:[1722,1222,1222,56,1166],93:[1150,650,472,24,246],123:[1150,650,667,97,569],124:[1292,792,278,109,169],125:[1150,650,667,98,570],160:[0,0,332,0,0],770:[747,-571,1100,0,1100],771:[762,-539,1115,0,1115],774:[742,-576,1120,0,1120],780:[741,-565,1100,0,1100],785:[759,-592,1120,0,1120],812:[-96,273,1100,0,1100],813:[-108,285,1100,0,1100],814:[-96,263,1120,0,1120],815:[-118,285,1120,0,1120],816:[-118,341,1115,0,1115],8214:[1292,792,386,56,330],8260:[1722,1222,1222,56,1166],8425:[750,-527,1860,0,1860],8730:[1750,1250,1000,111,1020],8739:[1292,792,278,109,169],8741:[1292,792,386,56,330],8968:[1150,650,528,196,502],8969:[1150,650,528,26,332],8970:[1150,650,528,196,502],8971:[1150,650,528,26,332],9001:[1150,650,611,159,555],9002:[1150,650,611,56,452],9140:[750,-527,1860,0,1860],9141:[-97,320,1860,0,1860],9180:[774,-506,2516,0,2516],9181:[-76,344,2516,0,2516],9182:[833,-502,2498,0,2498],9183:[-71,402,2498,0,2498],9184:[863,-607,2564,0,2564],9185:[-177,433,2564,0,2564],10214:[1150,650,660,211,637],10215:[1150,650,660,23,449],10216:[1150,650,611,159,555],10217:[1150,650,611,56,452],10218:[1150,650,905,159,849],10219:[1150,650,905,56,746],10222:[1168,668,432,176,376],10223:[1168,668,432,56,256]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"LatinModernMathJax_Size4"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Size4/Regular/Main.js"]);
