﻿using Dapper;
using ECOOL_APP.com.ecool.Models.DTO;
using ECOOL_APP.EF;
using MvcPaging;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;

namespace ECOOL_APP.com.ecool.service
{
    public class SECI05Service
    {


        public SECI05IndexViewModel GetBorrowDataATM(SECI05IndexViewModel model, UserProfile User, ref ECOOL_DEVEntities db)
        {
            if (model == null) model = new SECI05IndexViewModel();

            //預設值
            int SYear;
            int Semesters;
            SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);

            var Hr = db.HRMT01.Where(a => a.SCHOOL_NO == model.WhereSCHOOL_NO && a.USER_NO == model.WhereUSER_NO).FirstOrDefault();

            if (Hr != null)
            {
                string sSQL = $@" Select * from DB2_LWorkQuery where SCHOOL_NO=@SCHOOL_NO and USER_NO=@USER_NO";

                model.MyBorrow = db.Database.Connection.Query<SECI05MyBorrowViewModel>(sSQL
                  , new
                  {
                      SCHOOL_NO = Hr.SCHOOL_NO,
                      USER_NO = Hr.USER_NO,
                  }).FirstOrDefault();

                if (model.MyBorrow != null)
                {
                    //角色娃娃
                    if (!string.IsNullOrWhiteSpace(model.MyBorrow.PHOTO))
                    {
                        model.MyBorrow.PlayerUrl = new SECI01Service().GetDirectorySysMyPhotoPath(Hr.SCHOOL_NO, Hr.USER_NO, model.MyBorrow.PHOTO);
                    }
                    else
                    {
                        model.MyBorrow.PlayerUrl = UserProfile.GetPlayerUrl(ref db, Hr.SCHOOL_NO, Hr.USER_NO, Hr.SEX, Hr.USER_TYPE);
                    }

                    sSQL = @" select left(a.BK_GRP,3) BK_GRP,a.BKNAME,a.BORROW_DATE,DATEDIFF(day, a.BORROW_DATE, getdate()) EXPIRED_DAY
                        from  DB2_L_WORK a(nolock)
                        where 1 = 1
                        and isnull(a.DATE_RET,'')= ''
                        and a.NO_READ = @IDNO
                        order by a.BORROW_DATE desc ";
                    model.NowBorrowList = db.Database.Connection.Query<SECI05NowBorrowViewModel>(sSQL
                  , new
                  {
                      IDNO = model.MyBorrow.IDNO,
                  }).ToList();

                 
                }
            }

            return model;
        }
        public SECI05IndexViewModel GetBorrowData(SECI05IndexViewModel model, UserProfile User, ref ECOOL_DEVEntities db)
        {
            if (model == null) model = new SECI05IndexViewModel();

            //預設值
            int SYear;
            int Semesters;
            SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);

            var Hr = db.HRMT01.Where(a => a.SCHOOL_NO == model.WhereSCHOOL_NO && a.USER_NO == model.WhereUSER_NO).FirstOrDefault();

            if (Hr != null)
            {
                string sSQL = $@" Select * from DB2_LWorkQuery where SCHOOL_NO=@SCHOOL_NO and USER_NO=@USER_NO";

                model.MyBorrow = db.Database.Connection.Query<SECI05MyBorrowViewModel>(sSQL
                  , new
                  {
                      SCHOOL_NO = Hr.SCHOOL_NO,
                      USER_NO = Hr.USER_NO,
                  }).FirstOrDefault();

                if (model.MyBorrow != null)
                {
                    //角色娃娃
                    if (!string.IsNullOrWhiteSpace(model.MyBorrow.PHOTO))
                    {
                        model.MyBorrow.PlayerUrl = new SECI01Service().GetDirectorySysMyPhotoPath(Hr.SCHOOL_NO, Hr.USER_NO, model.MyBorrow.PHOTO);
                    }
                    else
                    {
                        model.MyBorrow.PlayerUrl = UserProfile.GetPlayerUrl(ref db, Hr.SCHOOL_NO, Hr.USER_NO, Hr.SEX, Hr.USER_TYPE);
                    }

                    sSQL = @" select left(a.BK_GRP,3) BK_GRP,a.BKNAME,a.BORROW_DATE,DATEDIFF(day, a.BORROW_DATE, getdate()) EXPIRED_DAY
                        from  DB2_L_WORK a(nolock)
                        where 1 = 1
                        and isnull(a.DATE_RET,'')= ''
                        and a.NO_READ = @IDNO
                        order by a.BORROW_DATE desc ";
                    model.NowBorrowList = db.Database.Connection.Query<SECI05NowBorrowViewModel>(sSQL
                  , new
                  {
                      IDNO = model.MyBorrow.IDNO,
                  }).ToList();

                    string sSQLSEYEAR = string.Empty;

                    if (!string.IsNullOrWhiteSpace(model.WhereSEYEAR))
                    {
                        sSQLSEYEAR = " and a.SEYEAR = @SEYEAR ";
                    }
                    DateTime dateTime2 = new DateTime();
                    var now = DateTime.Now;
                    dateTime2 = new DateTime(now.Year, now.Month, now.Day, 0, 0, 0);
                    string sSQLs = "";
                    sSQLs = $@"SELECT COUNT(*) FROM DB2_LWorkQuery2 where CRE_DATE>=@CRE_DATE and NO_READ=@NO_READ ";
                    int TEMPCOUNT = 0;
             TEMPCOUNT   = db.Database.Connection.Query<int>(sSQLs
                   , new
                   {
                       CRE_DATE = dateTime2,
                       NO_READ = model.MyBorrow.IDNO,
                   }).FirstOrDefault();
                    if (TEMPCOUNT == 0)
                    {
                        sSQL = $@"  insert DB2_LWorkQuery2 select b.TYPE_ID as TYPE_ID ,b.TYPE_NAME as 	TYPE_NAME	
                            ,Sum(Case When isnull(a.NO_READ,'')=@IDNO {sSQLSEYEAR} Then 1 else 0 end) QTY
                            ,(Case When isnull(a.NO_READ,'')=@IDNO  Then a.NO_READ else '' end) NO_READ
                             ,GETDATE() AS CRE_DATE
                            ,Case When  (select count(*) from DB2_L_WORK s (nolock) where 1=1 and isnull(s.NO_READ,'')=@IDNO {sSQLSEYEAR.Replace("a.", "s.")}) >0 Then
                            isnull(CONVERT(money,Sum(Case When isnull(a.NO_READ,'')=@IDNO {sSQLSEYEAR}  Then 1 else 0 end) ) / (select count(*) from DB2_L_WORK s (nolock) where 1=1 and isnull(s.NO_READ,'')=@IDNO {sSQLSEYEAR.Replace("a.", "s.")})  ,0)
                            Else 0 end Rate
                            from BDMT04 b (nolock)
                            FULL OUTER JOIN  DB2_L_WORK a  (nolock)  on left(a.BK_GRP,3) BETWEEN b.VAL_S and b.VAL_E and a.NO_READ=@IDNO {sSQLSEYEAR}
                            Where 1=1 and b.MAIN_YN='Y'
                            group by b.TYPE_ID,b.TYPE_NAME,NO_READ

	                        union

							select b.TYPE_ID as TYPE_ID ,b.TYPE_NAME as 	TYPE_NAME	
                            ,Sum(Case When isnull(a.NO_READ,'')=@IDNO Then 1 else 0 end) QTY
                            ,(Case When isnull(a.NO_READ,'')=@IDNO  Then a.NO_READ else '' end) NO_READ
                            ,GETDATE() AS CRE_DATE
                            ,Case When  (select count(*) from DB2_L_WORK s (nolock) where 1=1 and isnull(s.NO_READ,'')=@IDNO {sSQLSEYEAR.Replace("a.", "s.")}) >0 Then
                            isnull(CONVERT(money,Sum(Case When isnull(a.NO_READ,'')=@IDNO {sSQLSEYEAR}  Then 1 else 0 end) ) / (select count(*) from DB2_L_WORK s (nolock) where 1=1 and isnull(s.NO_READ,'')=@IDNO {sSQLSEYEAR.Replace("a.", "s.")})  ,0)
                            Else 0 end Rate
							from (select 99 TYPE_ID,'其他類' TYPE_NAME ) as b
                            left JOIN  DB2_L_WORK a  (nolock)  on a.NO_READ=@IDNO {sSQLSEYEAR} and not exists (Select t.VAL_S,t.VAL_E from BDMT04 t  (nolock) where left(a.BK_GRP,3) BETWEEN t.VAL_S and t.VAL_E and t.MAIN_YN='Y')
							group by b.TYPE_ID,b.TYPE_NAME,NO_READ
                            order by TYPE_ID ";
                       var BorrowTypeQtyInsertt= db.Database.Connection.Query<string>(sSQL
                       , new
                       {
                           IDNO = model.MyBorrow.IDNO,
                           SEYEAR = model.WhereSEYEAR,
                       }).ToList();
                    }


                    string sSQLQeury1 = $@" select *　FROM DB2_LWorkQuery2 where  NO_READ =@IDNO  and CRE_DATE>=@CRE_DATE";
                    model.BorrowTypeQty = db.Database.Connection.Query<SECI05BorrowTypeQtyViewModel>(sSQLQeury1
                      , new
                      {
                          IDNO = model.MyBorrow.IDNO,
                          CRE_DATE = dateTime2,
                      }).ToList();
                }
            }

            return model;
        }

        /// <summary>
        /// 每月借書變化表
        /// </summary>
        /// <param name="model"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        public SECI05MonthQtyIndexViewModel GetMonthQty(SECI05MonthQtyIndexViewModel model, ref ECOOL_DEVEntities db)
        {
            string sSQL = @" select T1.RET_YYMM,sum(T1.QTY) as SCHOOL_SUM ";

            if (!string.IsNullOrWhiteSpace(model.Where_CLASS_NO))
            {
                sSQL = sSQL + @",Sum(Case when T1.CLASS_NO=@CLASS_NO Then T1.QTY else 0 end ) as CLASS_SUM ";
            }

            sSQL = sSQL + @",count( distinct T1.CLASS_NO) CLASS_COUNT
							from (
									select a.CLASS_NO,b.RET_YYMM,sum(b.QTY) QTY
									from HRMT01 a (nolock)
									join DB2_L_WORK2 b (nolock) on a.IDNO=b.NO_READ
									where  b.SEYEAR=@SEYEAR and a.USER_TYPE='S' and a.SCHOOL_NO=@SCHOOL_NO
									group by a.CLASS_NO,b.RET_YYMM
							) T1
							group by T1.RET_YYMM ";
            model.MonthQtyList = db.Database.Connection.Query<SECI05MonthQtyListViewModel>(sSQL
                 , new
                 {
                     SCHOOL_NO = model.Where_SCHOOLNO,
                     SEYEAR = model.Where_SYEAR,
                     CLASS_NO = model.Where_CLASS_NO
                 }).ToList();

            if (model.MonthQtyList != null)
            {
                var MaxClassCount = model.MonthQtyList.Max(x => x.CLASS_COUNT);

                model.MonthQtyList.Select(a =>
                {
                    a.BORROW_BOOK_AVG = Math.Round((double)a.SCHOOL_SUM / MaxClassCount);
                    return a;
                }).ToList();
            }

            return model;
        }

        public SECI05BorrowListIndexViewModel GetBorrowListData(SECI05BorrowListIndexViewModel model, UserProfile User, ref ECOOL_DEVEntities db)
        {
            HRMT01 H01 = null;

            if (!string.IsNullOrWhiteSpace(model.WhereSCHOOL_NO) && !string.IsNullOrWhiteSpace(model.WhereUSER_NO))
            {
                H01 = db.HRMT01.Where(a => a.SCHOOL_NO == model.WhereSCHOOL_NO && a.USER_NO == model.WhereUSER_NO).FirstOrDefault();
            }
            else
            {
                H01 = db.HRMT01.Where(a => a.SCHOOL_NO == User.SCHOOL_NO && a.USER_NO == User.USER_NO).FirstOrDefault();
            }

            if (H01 != null)
            {
                string sSQL = $@" select left(a.BK_GRP,3) BK_GRP,a.BKNAME,isnull(b.TYPE_NAME,'其他') TYPE_NAME,Case When isnull(a.DATE_RET,'')<>'' Then 'Y' else 'N' end BORROW_YN
                                ,a.BORROW_DATE
                                ,Case when ISDATE(left(a.DATE_RET,8) +' '+SUBSTRING(a.DATE_RET,9,2)+':'+SUBSTRING(a.DATE_RET,11,2)+':'+Case When SUBSTRING(a.DATE_RET,13,2)<>'' Then SUBSTRING(a.DATE_RET,13,2) Else '00' End)=1 Then
                                 convert(datetime, left(a.DATE_RET,8) +' '+SUBSTRING(a.DATE_RET,9,2)+':'+SUBSTRING(a.DATE_RET,11,2)+':'+Case When SUBSTRING(a.DATE_RET,13,2)<>'' Then SUBSTRING(a.DATE_RET,13,2) Else '00' End , 112)
                                Else null End DATE_RET
                                ,Case When  isnull(b.VAL_S+'~'+b.VAL_E,'')<>'' Then isnull(b.VAL_S+'~'+b.VAL_E,'') Else '999999' End as OrderBy
                                from  DB2_L_WORK a(nolock)
                                FULL OUTER JOIN BDMT04 b  (nolock)  on left(a.BK_GRP,3) BETWEEN b.VAL_S and b.VAL_E
                                where 1 = 1 and a.NO_READ='{H01.IDNO}' ";

                if (!string.IsNullOrWhiteSpace(model.WhereBORROW_DATES))
                {
                    sSQL = sSQL + " and  a.BORROW_DATE >=@BORROW_DATES ";
                }

                if (!string.IsNullOrWhiteSpace(model.WhereBORROW_DATEE))
                {
                    sSQL = sSQL + " and  a.BORROW_DATE <=@BORROW_DATEE ";
                }
              

                if (!string.IsNullOrWhiteSpace(model.WhereBK_GRP))
                {
                    sSQL = sSQL + " and  Case When  isnull(b.VAL_S+'~'+b.VAL_E,'')<>'' Then isnull(b.VAL_S+'~'+b.VAL_E,'') Else '999999' End =@BK_GRP ";
                }

                if (!string.IsNullOrWhiteSpace(model.WhereSEYEAR))
                {
                    sSQL = sSQL + " and  a.SEYEAR=@SEYEAR ";
                }

                if (model.IsRepeatBook)
                {
                    sSQL = sSQL + $@" and a.BKNAME in (select m.BKNAME from DB2_L_WORK m (nolock) where  m.NO_READ='{H01.IDNO}'  group by m.BKNAME HAVING count(*)>1)";
                }

                sSQL = sSQL + " order by a.BORROW_DATE desc ";

                var Temp = db.Database.Connection.Query<SECI05BorrowListViewModel>(sSQL
                    , new
                    {
                        BORROW_DATES = model.WhereBORROW_DATES,
                        BORROW_DATEE = model.WhereBORROW_DATEE,
                        BK_GRP = model.WhereBK_GRP,
                        SEYEAR = model.WhereSEYEAR,
                    });

                if (!string.IsNullOrWhiteSpace(model.OrdercColumn))
                {
                    if (model.OrdercColumn == "BK_GRP")
                    {
                        if (model.SyntaxName == "ASC")
                        {
                            Temp = Temp.OrderBy(a => a.BK_GRP);
                        }
                        else
                        {
                            Temp = Temp.OrderByDescending(a => a.BK_GRP);
                        }
                    }
                    else if (model.OrdercColumn == "BKNAME")
                    {
                        if (model.SyntaxName == "ASC")
                        {
                            Temp = Temp.OrderBy(a => a.BKNAME);
                        }
                        else
                        {
                            Temp = Temp.OrderByDescending(a => a.BKNAME);
                        }
                    }
                    else if (model.OrdercColumn == "TYPE_NAME")
                    {
                        if (model.SyntaxName == "ASC")
                        {
                            Temp = Temp.OrderBy(a => a.TYPE_NAME);
                        }
                        else
                        {
                            Temp = Temp.OrderByDescending(a => a.TYPE_NAME);
                        }
                    }
                    else if (model.OrdercColumn == "BORROW_YN")
                    {
                        if (model.SyntaxName == "ASC")
                        {
                            Temp = Temp.OrderBy(a => a.BORROW_YN);
                        }
                        else
                        {
                            Temp = Temp.OrderByDescending(a => a.BORROW_YN);
                        }
                    }
                    else if (model.OrdercColumn == "DATE_RET")
                    {
                        if (model.SyntaxName == "ASC")
                        {
                            Temp = Temp.OrderBy(a => a.DATE_RET);
                        }
                        else
                        {
                            Temp = Temp.OrderByDescending(a => a.DATE_RET);
                        }
                    }
                    else
                    {
                        if (model.SyntaxName == "ASC")
                        {
                            Temp = Temp.OrderBy(a => a.BORROW_DATE);
                        }
                        else
                        {
                            Temp = Temp.OrderByDescending(a => a.BORROW_DATE);
                        }
                    }
                }

                model.ListData = Temp.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, model.PageSize);
            }

            return model;
        }

        public SECI05TypeListIndexViewModel GetTypeListData(SECI05TypeListIndexViewModel model, UserProfile User, ref ECOOL_DEVEntities db)
        {
            HRMT01 H01 = null;

            if (!string.IsNullOrWhiteSpace(model.WhereSCHOOL_NO) && !string.IsNullOrWhiteSpace(model.WhereUSER_NO))
            {
                H01 = db.HRMT01.Where(a => a.SCHOOL_NO == model.WhereSCHOOL_NO && a.USER_NO == model.WhereUSER_NO).FirstOrDefault();
            }
            else
            {
                H01 = db.HRMT01.Where(a => a.SCHOOL_NO == User.SCHOOL_NO && a.USER_NO == User.USER_NO).FirstOrDefault();
            }

            if (H01 != null)
            {
                string sSQLSEYEAR = string.Empty;

                if (!string.IsNullOrWhiteSpace(model.WhereSEYEAR))
                {
                    sSQLSEYEAR = " and a.SEYEAR = @SEYEAR ";
                }

                string sSQL = $@"Select isnull(B04.TYPE_ID,99) TYPE_ID
                                        ,Case When isnull(B04.TYPE_ID,99)<>99  Then B04.VAL_S+'~'+B04.VAL_E Else '~' End  BK_GRP
                                        ,Case When isnull(B04.TYPE_ID,99)<>99  Then B04.VAL_S+'~'+B04.VAL_E Else '999999' End  OrderBy
                                        ,Case When isnull(B04.TYPE_ID,99)<>99  Then B04.TYPE_NAME Else '其他類' End TYPE_NAME
                                         ,isnull(T1.QTY,0) QTY, isnull(T2.Total,0) Total
                                        ,isnull(T1.QTY,0) *1.00 /isnull(T2.Total,0) Rate
                                        from BDMT04 B04
                                        full outer join  (
				                                        select isnull(b.TYPE_ID,99) TYPE_ID
				                                        ,count(*) QTY
				                                        from DB2_L_WORK a (nolock)
			                                            left outer join BDMT04 b (nolock)  on left(a.BK_GRP,3) BETWEEN b.VAL_S and b.VAL_E
				                                        Where 1=1 and a.NO_READ=@IDNO  {sSQLSEYEAR}
				                                        group by b.TYPE_ID
                                        ) as T1 on B04.TYPE_ID =T1.TYPE_ID
                                         cross join (select count(*) Total from  DB2_L_WORK a  (nolock) Where 1=1 and a.NO_READ=@IDNO {sSQLSEYEAR}  ) as T2
                                         order by OrderBy";

                var Temp = db.Database.Connection.Query<SECI05BTypeListViewModel>(sSQL
               , new
               {
                   IDNO = H01.IDNO,
                   SEYEAR = model.WhereSEYEAR,
               });

                if (!string.IsNullOrWhiteSpace(model.OrdercColumn))
                {
                    if (model.OrdercColumn == "TYPE_NAME")
                    {
                        if (model.SyntaxName == "ASC")
                        {
                            Temp = Temp.OrderBy(a => a.TYPE_NAME).ThenBy(a => a.OrderBy);
                        }
                        else
                        {
                            Temp = Temp.OrderByDescending(a => a.TYPE_NAME).ThenByDescending(a => a.OrderBy);
                        }
                    }
                    else if (model.OrdercColumn == "QTY")
                    {
                        if (model.SyntaxName == "ASC")
                        {
                            Temp = Temp.OrderBy(a => a.QTY).ThenBy(a => a.OrderBy);
                        }
                        else
                        {
                            Temp = Temp.OrderByDescending(a => a.QTY).ThenByDescending(a => a.OrderBy);
                        }
                    }
                    else if (model.OrdercColumn == "RATE")
                    {
                        if (model.SyntaxName == "ASC")
                        {
                            Temp = Temp.OrderBy(a => a.RATE).ThenBy(a => a.OrderBy);
                        }
                        else
                        {
                            Temp = Temp.OrderByDescending(a => a.RATE).ThenByDescending(a => a.OrderBy);
                        }
                    }
                    else
                    {
                        if (model.SyntaxName == "ASC")
                        {
                            Temp = Temp.OrderBy(a => a.OrderBy);
                        }
                        else
                        {
                            Temp = Temp.OrderByDescending(a => a.OrderBy);
                        }
                    }
                }

                model.ListData = Temp.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, model.PageSize);
            }

            return model;
        }

        /// <summary>
        /// 本學期借書量後百分比n的學生
        /// </summary>
        /// <returns></returns>
        public SECI05EncourageViewModel GetBorrowEncourageList(SECI05EncourageViewModel model, int percentNum, ref ECOOL_DEVEntities db)
        {
            string whereSql = "";
            SysHelper.SemestersInfo(DateTime.Now, out int syear, out int semester);
            if (!string.IsNullOrEmpty(model.WhereSEYEAR))
            {
                syear = Convert.ToInt16(model.WhereSEYEAR);
            }
            if (!string.IsNullOrEmpty(model.WhereCLASS_NO))
            {
                whereSql += " and a.CLASS_NO =@CLASS_NO ";
            }
            string sSQL = $@"Select top {percentNum} PERCENT a.USER_NO,a.IDNO,a.CLASS_NO,a.PHOTO
                            ,a.NAME
                            ,( select count(*)  from
                                DB2_L_WORK (nolock) where NO_READ=a.IDNO  )as SumQTY
                            ,Sum(case when b.SEYEAR= @SYEAR and b.SESEM= @SESEM Then 1 else 0 end ) as THIS_SESEM_QTY
                            ,max(b.BORROW_DATE) LAST_BORROW_DATE
                            from HRMT01 a (nolock)
                            left outer join
                            (
                                select * from
                                DB2_L_WORK (nolock)
                                where SEYEAR=@SYEAR and SESEM=@SESEM
                            ) b on a.IDNO=b.NO_READ
                            where a.SCHOOL_NO=@SCHOOL_NO and a.USER_TYPE='S' and a.USER_STATUS='1'
                            {whereSql}
                            group by a.USER_NO,a.IDNO,a.CLASS_NO,a.NAME ,a.PHOTO
                            order by THIS_SESEM_QTY,CLASS_NO, LAST_BORROW_DATE desc";

            var list = db.Database.Connection.Query<SECI05MyBorrowViewModel>(sSQL, new
            {
                SCHOOL_NO = model.WhereSCHOOL_NO,
                CLASS_NO = model.WhereCLASS_NO,
                SYEAR = syear,
                SESEM = semester
            }).ToList();

            model.EncourageUserList = list;

            return model;
        }

        /// <summary>
        /// 疑似偏食: 單一類別n%以上
        /// </summary>
        /// <param name="model"></param>
        /// <param name="percentNum">n</param>
        /// <param name="db"></param>
        /// <returns></returns>
        public SECI05ReadingPartialViewModel GetReadingPartialEclipseList(SECI05ReadingPartialViewModel model, int percentNum, ref ECOOL_DEVEntities db)
        {
            string whereSql = "";

            if (!string.IsNullOrEmpty(model.WhereCLASS_NO))
            {
                whereSql += " and CLASS_NO =@CLASS_NO ";
            }
            string sSQL = $@"   SELECT *
                                FROM [ECOOL_DEV].[dbo].[READING_PARTIAL_ECLIPSE]
                                WHERE SCHOOL_NO = @SCHOOL_NO and RATE >={(double)percentNum / 100}
                                {whereSql}
                                ORDER BY RATE DESC, CLASS_NO
                            ";

            var list = db.Database.Connection.Query<SECI05ReadingPartial>(sSQL, new
            {
                SCHOOL_NO = model.WhereSCHOOL_NO,
                CLASS_NO = model.WhereCLASS_NO
            }).ToList();

            model.ReadingPartialList = list;

            return model;
        }

        /// <summary>
        /// n個月未借書及環書學生
        /// </summary>
        /// <param name="model"></param>
        /// <param name="diffmonth">n</param>
        /// <param name="db"></param>
        /// <returns></returns>
        public SECI05UnusualViewModel GetUnusualList(SECI05UnusualViewModel model, int diffmonth, ref ECOOL_DEVEntities db)
        {
            string whereSql = "";

            if (!string.IsNullOrEmpty(model.WhereCLASS_NO))
            {
                whereSql += " and a.CLASS_NO =@CLASS_NO ";
            }

            // 借書異常 => n個月未借書的學生 (算大約就好30天一個月)
            string sSQL = $@"Select *, DATEDIFF(d, LAST_BORROW_DATE, GETDATE())/30 DiffMonth from
                            (
	                             Select  a.USER_NO,a.IDNO,a.CLASS_NO,a.PHOTO,a.SYEAR
	                             ,a.NAME
	                             ,max(b.BORROW_DATE) LAST_BORROW_DATE
                                  ,( select count(*)  from DB2_L_WORK (nolock) where NO_READ=a.IDNO  ) as SumQTY
	                             from HRMT01 a (nolock)
	                             inner join DB2_L_WORK b
	                             on b.NO_READ  = (
                                    select top 1 NO_READ
                                     from DB2_L_WORK (nolock)
                                     where NO_READ = a.IDNO
                                     order by BORROW_DATE desc
                                 )
	                             where a.SCHOOL_NO=@SCHOOL_NO and a.USER_TYPE='S'and  a.USER_STATUS=1
                                 {whereSql}
	                             group by  a.USER_NO,a.IDNO,a.CLASS_NO,a.NAME ,a.PHOTO,　a.SYEAR
                             ) t
                            order by LAST_BORROW_DATE, DiffMonth desc  ";

            var brrowUnusual = db.Database.Connection.Query<SECI05MyBorrowViewModel>(sSQL, new
            {
                SCHOOL_NO = model.WhereSCHOOL_NO,
                CLASS_NO = model.WhereCLASS_NO
            }).ToList();

            model.BrrowUnusualList = brrowUnusual.Where(l => l.DiffMonth >= diffmonth).ToList();

            // 歸還異常 => 未環書的學生
            string sSQL2 = $@"
                             Select *, (CASE WHEN DATE_RET is null THEN DATEDIFF(d, LAST_BORROW_DATE,GETDATE())/30 ELSE DATEDIFF(d, LAST_BORROW_DATE, DATE_RET)/30 END) DiffMonth

                              from
                             (
	                             Select  a.USER_NO,a.IDNO,a.CLASS_NO,a.SYEAR

	                             ,a.NAME
	                             ,b.BORROW_DATE LAST_BORROW_DATE

								 ,b.BKNAME
								 , max(Case when ISDATE(left(b.DATE_RET,8) +' '+SUBSTRING(b.DATE_RET,9,2)+':'+SUBSTRING(b.DATE_RET,11,2)+':'+Case When SUBSTRING(b.DATE_RET,13,2)<>'' Then SUBSTRING(b.DATE_RET,13,2) Else '00' End)=1 Then
                                   convert(datetime, left(b.DATE_RET,8) +' '+SUBSTRING(b.DATE_RET,9,2)+':'+SUBSTRING(b.DATE_RET,11,2)+':'+Case When SUBSTRING(b.DATE_RET,13,2)<>'' Then SUBSTRING(b.DATE_RET,13,2) Else '00' End , 112)
									Else null End ) DATE_RET
                                 ,( select count(*)  from DB2_L_WORK (nolock) where NO_READ=a.IDNO  ) as SumQTY
	                             from HRMT01 a (nolock)
	                             inner join DB2_L_WORK b
	                             on b.NO_READ  = (
                                    select top 1 NO_READ
                                     from DB2_L_WORK (nolock)
                                     where NO_READ = a.IDNO
                                     order by BORROW_DATE desc
                                 )
	                             where a.SCHOOL_NO=@SCHOOL_NO and a.USER_TYPE='S' and a.USER_STATUS='1' and DATE_RET = ''
                                 {whereSql}
	                             group by  a.USER_NO,a.IDNO,a.CLASS_NO,a.NAME ,b.BKNAME,　a.SYEAR ,b.BORROW_DATE
                             ) t
                             order by LAST_BORROW_DATE, DiffMonth desc
                            ";
            var retUnusual = db.Database.Connection.Query<SECI05MyBorrowViewModel>(sSQL2, new
            {
                SCHOOL_NO = model.WhereSCHOOL_NO,
                CLASS_NO = model.WhereCLASS_NO
            }).ToList();

            model.ReturnUnusualList = retUnusual.Where(l => l.DiffMonth >= 1).ToList();
            return model;
        }
    }
}