/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/SizeOneSym/Regular/All.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.STIXSizeOneSym,{710:[767,-554,560,0,560],711:[767,-554,560,0,560],732:[750,-598,558,-2,558],759:[-117,269,558,-2,558],773:[820,-770,0,-1000,0],780:[767,-554,0,-720,-160],816:[-117,269,0,-722,-162],818:[-127,177,0,-1000,0],824:[532,21,0,-720,-157],8254:[820,-770,1000,0,1000],8400:[749,-584,0,-892,-21],8401:[749,-584,0,-893,-22],8406:[735,-482,0,-893,-21],8407:[736,-482,0,-893,-21],8428:[-123,288,0,-893,-22],8429:[-123,288,0,-892,-21],8430:[-26,279,0,-893,-21],8431:[-25,279,0,-893,-21],8512:[1500,-50,1259,55,1204],8731:[1552,295,1057,112,1089],8732:[1552,295,1057,112,1089],8992:[1066,79,688,294,574],8993:[1086,59,688,115,394],9138:[1500,5,1482,92,1292],9139:[1500,5,1482,92,1366],9140:[766,-544,1063,69,994],9141:[139,83,1063,68,993],9180:[60,153,926,0,926],9181:[777,-564,926,0,926],9184:[66,212,1460,0,1460],9185:[842,-564,1460,0,1460],10098:[1066,164,566,205,539],10099:[1066,164,566,27,361],10214:[1066,164,515,180,486],10215:[1066,164,515,29,335],10218:[1066,164,798,116,670],10219:[1066,164,798,128,682],10627:[1066,164,712,114,587],10628:[1066,164,712,114,587],10629:[1066,164,632,135,546],10630:[1066,164,632,86,497],10744:[1566,279,806,25,781],10745:[1566,279,806,25,781],10755:[1500,-39,1265,118,1147],10759:[1500,-49,1530,60,1470],10760:[1500,-49,1530,60,1470],10761:[1500,-49,1482,60,1422],10762:[1500,-50,1292,90,1202],11004:[867,363,690,133,557],11007:[867,363,410,100,310]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/SizeOneSym/Regular/All.js");
