﻿using MvcPaging;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;

namespace ECOOL_APP.EF
{
    public class ADDI10IndexViewModel
    {
        /// <summary>
        /// 查詢結果
        /// </summary>
        public IPagedList<ADDI10IndexListViewModel> ListData;

        public ADDI10IndexViewModel()
        {
            PageSize = PageGlobal.DfTeamPageSize;
        }

        public bool? StudentCardVote { get; set; }

        public ADDI10SearchViewModel Search { get; set; }

        /// <summary>
        /// 一頁筆數 預設筆數
        /// </summary>
        public int PageSize { get; set; }
    }
}