/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/General/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.STIXGeneral={directory:"General/Regular",family:"STIXGeneral",Ranges:[[160,255,"Latin1Supplement"],[256,383,"LatinExtendedA"],[384,591,"LatinExtendedB"],[592,687,"IPAExtensions"],[688,767,"SpacingModLetters"],[768,879,"CombDiacritMarks"],[880,1023,"GreekAndCoptic"],[1024,1279,"Cyrillic"],[7424,7615,"PhoneticExtensions"],[7680,7935,"LatinExtendedAdditional"],[8192,8303,"GeneralPunctuation"],[8304,8351,"SuperAndSubscripts"],[8352,8399,"CurrencySymbols"],[8400,8447,"CombDiactForSymbols"],[8448,8527,"LetterlikeSymbols"],[8528,8591,"NumberForms"],[8592,8703,"Arrows"],[8704,8959,"MathOperators"],[8960,9215,"MiscTechnical"],[9216,9279,"ControlPictures"],[9312,9471,"EnclosedAlphanum"],[9472,9599,"BoxDrawing"],[9600,9631,"BlockElements"],[9632,9727,"GeometricShapes"],[9728,9983,"MiscSymbols"],[9984,10175,"Dingbats"],[10176,10223,"MiscMathSymbolsA"],[10224,10239,"SupplementalArrowsA"],[10496,10623,"SupplementalArrowsB"],[10624,10751,"MiscMathSymbolsB"],[10752,11007,"SuppMathOperators"],[11008,11263,"MiscSymbolsAndArrows"],[12288,12351,"CJK"],[12352,12447,"Hiragana"],[42784,43007,"LatinExtendedD"],[64256,64335,"AlphaPresentForms"],[65520,65535,"Specials"],[119808,119859,"MathBold"],[119860,119911,"MathItalic"],[119912,119963,"MathBoldItalic"],[119964,120015,"MathScript"],[120016,120067,"MathBoldScript"],[120068,120119,"Fraktur"],[120120,120171,"BBBold"],[120172,120223,"BoldFraktur"],[120224,120275,"MathSS"],[120276,120327,"MathSSBold"],[120328,120379,"MathSSItalic"],[120380,120431,"MathSSItalicBold"],[120432,120483,"MathTT"],[120484,120485,"ij"],[120488,120545,"GreekBold"],[120546,120603,"GreekItalic"],[120604,120661,"GreekBoldItalic"],[120662,120719,"GreekSSBold"],[120720,120777,"GreekSSBoldItalic"],[120782,120791,"MathBold"],[120792,120801,"BBBold"],[120802,120811,"MathSS"],[120812,120822,"MathSSBold"],[120822,120831,"MathTT"]],32:[0,0,250,0,0],33:[676,9,333,130,236],34:[676,-431,408,77,331],35:[662,0,500,6,495],36:[727,87,500,44,458],37:[706,19,747,61,686],38:[676,13,778,42,750],39:[676,-431,180,48,133],40:[676,177,333,48,304],41:[676,177,333,29,285],42:[676,-265,500,68,433],43:[547,41,685,48,636],44:[102,141,250,55,195],45:[257,-194,333,39,285],46:[100,11,250,70,181],47:[676,14,278,-9,287],48:[676,14,500,24,476],49:[676,0,500,111,394],50:[676,0,500,29,474],51:[676,14,500,41,431],52:[676,0,500,12,473],53:[688,14,500,31,438],54:[684,14,500,34,468],55:[662,8,500,20,449],56:[676,14,500,56,445],57:[676,22,500,30,459],58:[459,11,278,81,192],59:[459,141,278,80,219],60:[534,24,685,56,621],61:[386,-120,685,48,637],62:[534,24,685,56,621],63:[676,8,444,68,414],64:[676,14,921,116,809],65:[674,0,722,15,707],66:[662,0,667,17,593],67:[676,14,667,28,633],68:[662,0,722,16,685],69:[662,0,611,12,597],70:[662,0,556,11,546],71:[676,14,722,32,709],72:[662,0,722,18,703],73:[662,0,333,18,315],74:[662,14,373,-6,354],75:[662,0,722,33,723],76:[662,0,611,12,598],77:[662,0,889,12,864],78:[662,11,722,12,707],79:[676,14,722,34,688],80:[662,0,557,16,542],81:[676,177,722,34,701],82:[662,0,667,17,660],83:[676,14,556,43,491],84:[662,0,611,17,593],85:[662,14,722,14,705],86:[662,11,722,16,697],87:[662,11,944,5,932],88:[662,0,722,10,704],89:[662,0,722,22,703],90:[662,0,612,10,598],91:[662,156,333,88,299],92:[676,14,278,-9,287],93:[662,156,333,34,245],94:[662,-297,469,24,446],95:[-75,125,500,0,500],96:[678,-507,333,18,242],97:[460,10,444,37,442],98:[683,10,500,3,468],99:[460,10,444,25,412],100:[683,10,500,27,491],101:[460,10,444,25,424],102:[683,0,333,20,383],103:[460,218,500,28,470],104:[683,0,500,9,487],105:[683,0,278,16,253],106:[683,218,278,-70,194],107:[683,0,500,7,505],108:[683,0,278,19,257],109:[460,0,778,16,775],110:[460,0,500,16,485],111:[460,10,500,29,470],112:[460,217,500,5,470],113:[460,217,500,24,488],114:[460,0,333,5,335],115:[459,10,389,51,348],116:[579,10,278,13,279],117:[450,10,500,9,480],118:[450,14,500,19,477],119:[450,14,722,21,694],120:[450,0,500,17,479],121:[450,218,500,14,475],122:[450,0,444,27,418],123:[680,181,480,100,350],124:[676,14,200,67,133],125:[680,181,480,130,380],126:[325,-183,541,40,502],160:[0,0,250,0,0],168:[622,-523,333,18,316],172:[393,-115,600,48,552],175:[601,-547,333,11,322],177:[502,87,685,48,637],183:[310,-199,250,70,181],215:[529,25,640,43,597],247:[516,10,564,30,534],305:[460,0,278,16,253],567:[460,218,278,-70,193],710:[674,-507,333,11,322],711:[674,-507,333,11,322],713:[601,-547,334,11,322],714:[679,-509,333,93,320],715:[679,-509,333,22,249],728:[664,-507,335,27,308],729:[622,-523,333,118,217],732:[638,-532,333,1,331],768:[678,-507,0,-371,-147],769:[678,-507,0,-371,-147],770:[674,-507,0,-386,-75],771:[638,-532,0,-395,-65],772:[601,-547,0,-385,-74],774:[664,-507,0,-373,-92],775:[622,-523,0,-280,-181],776:[622,-523,0,-379,-81],778:[711,-512,0,-329,-130],779:[678,-507,0,-401,-22],780:[674,-507,0,-385,-74],824:[662,156,0,-380,31],915:[662,0,587,11,577],916:[674,0,722,48,675],920:[676,14,722,34,688],923:[674,0,702,15,687],926:[662,0,643,29,614],928:[662,0,722,18,703],931:[662,0,624,30,600],933:[674,0,722,29,703],934:[662,0,763,35,728],936:[690,0,746,22,724],937:[676,0,744,29,715],8224:[676,149,500,59,442],8225:[676,153,500,58,442],8230:[100,11,1000,111,888],8242:[678,-402,289,75,214],8254:[820,-770,500,0,500],8407:[760,-548,0,-453,-17],8465:[695,34,762,45,711],8472:[547,217,826,52,799],8476:[704,22,874,50,829],8501:[677,13,682,43,634],8592:[449,-58,926,71,857],8593:[662,156,511,60,451],8594:[448,-57,926,70,856],8595:[662,156,511,60,451],8596:[449,-57,926,38,888],8597:[730,224,511,60,451],8598:[662,156,926,70,856],8599:[662,156,926,70,856],8600:[662,156,926,70,856],8601:[662,156,926,70,856],8614:[450,-57,926,70,857],8617:[553,-57,926,70,856],8618:[553,-57,926,70,856],8636:[494,-220,955,54,901],8637:[286,-12,955,54,901],8640:[494,-220,955,54,901],8641:[286,-12,955,54,901],8652:[539,33,926,70,856],8656:[551,45,926,60,866],8657:[662,156,685,45,641],8658:[551,45,926,60,866],8659:[662,156,685,45,641],8660:[517,10,926,20,906],8661:[730,224,685,45,641],8704:[662,0,560,2,558],8706:[668,11,471,40,471],8707:[662,0,560,73,487],8709:[583,79,762,50,712],8711:[662,12,731,63,667],8712:[531,27,685,60,625],8713:[662,157,685,60,625],8715:[531,27,685,60,625],8719:[763,259,1000,52,948],8720:[763,259,1000,52,948],8721:[763,259,914,58,856],8722:[286,-220,685,64,621],8723:[502,87,685,48,637],8725:[710,222,523,46,478],8726:[411,-93,428,25,403],8727:[471,-33,523,67,457],8728:[387,-117,350,40,310],8729:[387,-117,350,40,310],8730:[973,259,928,112,963],8733:[430,0,685,41,643],8734:[430,0,926,70,854],8736:[547,0,685,23,643],8739:[690,189,266,100,166],8741:[690,189,523,129,394],8743:[536,29,620,31,589],8744:[536,29,620,31,589],8745:[536,31,620,48,572],8746:[536,31,620,48,572],8747:[824,320,459,32,639],8764:[362,-148,685,48,637],8768:[547,42,286,35,249],8771:[445,-55,685,48,637],8773:[532,27,685,48,637],8776:[475,-25,685,48,637],8781:[498,-8,685,48,637],8784:[611,-120,685,48,637],8800:[662,156,685,48,637],8801:[478,-28,685,48,637],8804:[609,103,685,64,629],8805:[609,103,685,64,629],8810:[532,26,933,25,908],8811:[532,26,933,25,908],8826:[532,26,685,64,621],8827:[532,26,685,64,621],8828:[628,120,685,64,621],8829:[629,119,685,64,621],8834:[531,25,685,64,621],8835:[531,25,685,64,621],8838:[607,103,685,64,621],8839:[607,103,685,64,621],8846:[536,31,620,48,572],8849:[607,103,685,64,621],8850:[607,103,685,64,621],8851:[536,31,620,48,572],8852:[536,31,620,48,572],8853:[623,119,842,50,792],8854:[623,119,842,50,792],8855:[623,119,842,50,792],8856:[623,119,842,50,792],8857:[583,79,762,50,712],8866:[662,0,685,64,621],8867:[662,0,685,64,621],8868:[662,0,685,48,637],8869:[662,0,685,48,637],8872:[662,0,685,64,621],8896:[763,259,924,54,870],8897:[763,259,924,54,870],8898:[778,254,924,94,830],8899:[768,264,924,94,830],8900:[488,-16,523,26,497],8901:[313,-193,286,83,203],8902:[597,13,700,35,665],8904:[582,80,810,54,756],8942:[606,104,511,192,319],8943:[316,-189,926,108,818],8945:[520,18,926,194,732],8968:[713,213,469,188,447],8969:[713,213,469,27,286],8970:[713,213,469,188,447],8971:[713,213,469,27,286],8994:[360,-147,1019,54,965],8995:[360,-147,1019,54,965],9135:[286,-220,315,0,315],9168:[405,-101,511,222,288],9651:[811,127,1145,35,1110],9657:[555,50,660,80,605],9661:[811,127,1145,35,1110],9667:[554,51,660,55,580],9711:[785,282,1207,70,1137],9824:[609,99,685,34,651],9825:[603,105,685,34,651],9826:[609,105,685,41,643],9827:[603,99,685,34,651],9837:[768,10,426,57,346],9838:[768,181,426,75,350],9839:[768,181,426,41,386],10216:[713,213,400,77,335],10217:[713,213,400,65,323],10222:[676,177,233,56,211],10223:[676,177,233,22,177],10229:[449,-58,1574,55,1519],10230:[449,-57,1574,55,1519],10231:[449,-57,1574,55,1519],10232:[551,45,1574,55,1519],10233:[551,45,1574,55,1519],10234:[517,10,1574,55,1519],10235:[450,-57,1574,55,1519],10236:[450,-57,1574,55,1519],10741:[710,222,523,46,478],10752:[763,259,1126,53,1073],10753:[763,259,1126,53,1073],10754:[763,259,1126,53,1073],10755:[768,264,924,94,830],10756:[768,264,924,94,830],10757:[763,259,924,94,830],10758:[763,259,924,94,830],10815:[662,0,694,30,664],10927:[609,103,685,64,621],10928:[609,103,685,64,621]};MathJax.OutputJax["HTML-CSS"].initFont("STIXGeneral");MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/Regular/Main.js");
