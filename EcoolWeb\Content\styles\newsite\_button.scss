.btn-arrow {
    display: flex;
    align-items: center;
    text-decoration: none;
    line-height: 1.2;
    padding: 0.5rem 0 .5rem 1.4rem;
    margin: 0.5rem 3em 0.5rem 0;
    font-size: 0.725rem;
    position: relative;
    color: #fff;
    background-image: linear-gradient(to bottom, #3b7bc9 0%, #2e619e 100%);
    border: 0;
    box-shadow: 0 0.1rem 0.2rem rgba(0, 0, 0, 0.3);
    transform: scale(0.9);

    @include media-breakpoint-up(md) {
        padding: .5rem .5rem .5rem 2rem;
        font-size: 1rem;
    }

    &::after {
        content: "";
        display: block;
        position: absolute;
        width: 1em;
        height: 100%;
        right: -1em;
        background-image: url(../images/btn-arrow-bg.png);
        background-repeat: no-repeat;
        background-position: right center;
        background-size: auto 160%;

        @include media-breakpoint-up(md) {
            width: 1.725em;
            height: 100%;
            right: -1.4em;
        }
    }

    img {
        height: 1.5em;
        line-height: 1;
        width: auto;
        position: absolute;
        left: 0.3em;
        top: auto;
        bottom: auto;
    }

    .badge {
        min-width: 1em;
        padding: 0;
        position: absolute;
        top: .5em;
        right: -1.9em;
        font-size: 1rem;
        font-weight: 600;
        color: #0061CA;
        background-color: transparent;

        @include media-breakpoint-up(md) {
            min-width: 2em;
            padding: 0 .3rem;
            right: -3em;
            font-size: 1.256rem;
        }
    }

    &:hover,
    &:focus {
        color: #fff;
        filter: brightness(1.2);
    }

    &-red {
        //藍綠色
        filter: hue-rotate(156deg);

        img {
            filter: hue-rotate(204deg);
        }

        &:hover,
        &:focus {
            color: #fff;
            filter: hue-rotate(156deg) brightness(1.2);

            img {
                filter: hue-rotate(204deg);
            }
        }
    }

    &-lake {
        filter: hue-rotate(335deg);

        img {
            filter: hue-rotate(25deg);
        }

        &:hover,
        &:focus {
            color: #fff;
            filter: hue-rotate(335deg) brightness(1.2);

            img {
                filter: hue-rotate(25deg);
            }
        }
    }

    &-purple {
        filter: hue-rotate(50deg);

        img {
            filter: hue-rotate(310deg);
        }

        &:hover,
        &:focus {
            color: #fff;
            filter: hue-rotate(50deg) brightness(1.2);

            img {
                filter: hue-rotate(310deg);
            }
        }
    }

    &-purple-lite {
        filter: hue-rotate(224deg) invert(1) brightness(1.3) saturate(0.7);

        img,
        .badge {
            filter: hue-rotate(136deg) invert(1) brightness(1.5);
        }

        &:hover,
        &:focus {
            color: #fff;
            filter: hue-rotate(224deg) invert(1) brightness(1.5) saturate(0.7);

            img,
            .badge {
                filter: hue-rotate(136deg) invert(1) brightness(1.5);
            }
        }
    }
}