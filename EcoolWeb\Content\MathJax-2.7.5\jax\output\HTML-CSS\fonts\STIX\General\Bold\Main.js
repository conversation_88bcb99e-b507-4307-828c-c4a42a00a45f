/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/General/Bold/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXGeneral-bold"]={directory:"General/Bold",family:"STIXGeneral",weight:"bold",Ranges:[[160,255,"Latin1Supplement"],[256,383,"LatinExtendedA"],[384,591,"LatinExtendedB"],[592,687,"IPAExtensions"],[688,767,"SpacingModLetters"],[768,879,"CombDiacritMarks"],[880,1023,"GreekAndCoptic"],[1024,1279,"Cyrillic"],[7424,7615,"PhoneticExtensions"],[7680,7935,"LatinExtendedAdditional"],[8192,8303,"GeneralPunctuation"],[8304,8351,"SuperAndSubscripts"],[8352,8399,"CurrencySymbols"],[8400,8447,"CombDiactForSymbols"],[8448,8527,"LetterlikeSymbols"],[8528,8591,"NumberForms"],[8592,8703,"Arrows"],[8704,8959,"MathOperators"],[8960,9215,"MiscTechnical"],[9216,9279,"ControlPictures"],[9312,9471,"EnclosedAlphanum"],[9472,9599,"BoxDrawing"],[9632,9727,"GeometricShapes"],[9728,9983,"MiscSymbols"],[10176,10223,"MiscMathSymbolsA"],[10624,10751,"MiscMathSymbolsB"],[10752,11007,"SuppMathOperators"],[42784,43007,"LatinExtendedD"],[64256,64335,"AlphaPresentForms"],[119808,119859,"MathBold"],[120120,120171,"BBBold"],[120172,120223,"BoldFraktur"],[120276,120327,"MathSSBold"],[120488,120545,"GreekBold"],[120662,120719,"GreekSSBold"],[120782,120791,"MathBold"],[120812,120822,"MathSSBold"]],32:[0,0,250,0,0],33:[691,13,333,81,251],34:[691,-404,555,83,472],35:[700,0,500,5,495],36:[750,99,500,29,472],37:[706,29,749,61,688],38:[691,16,833,62,789],39:[691,-404,278,75,204],40:[694,168,333,46,306],41:[694,168,333,27,287],42:[691,-255,500,56,448],43:[563,57,750,65,685],44:[155,180,250,39,223],45:[287,-171,333,44,287],46:[156,13,250,41,210],47:[691,19,278,-24,302],48:[688,13,500,24,476],49:[688,0,500,65,441],50:[688,0,500,17,478],51:[688,14,500,16,468],52:[688,0,500,19,476],53:[676,8,500,22,470],54:[688,13,500,28,475],55:[676,0,500,17,477],56:[688,13,500,28,472],57:[688,13,500,26,473],58:[472,13,333,82,251],59:[472,180,333,82,266],60:[534,24,750,80,670],61:[399,-107,750,68,682],62:[534,24,750,80,670],63:[689,13,500,57,445],64:[691,19,930,108,822],65:[690,0,722,9,689],66:[676,0,667,16,619],67:[691,19,722,49,687],68:[676,0,722,14,690],69:[676,0,667,16,641],70:[676,0,611,16,583],71:[691,19,778,37,755],72:[676,0,778,21,759],73:[676,0,389,20,370],74:[676,96,500,3,478],75:[676,0,778,30,769],76:[677,0,667,19,638],77:[676,0,944,14,921],78:[676,18,722,16,701],79:[691,19,778,35,743],80:[676,0,611,16,600],81:[691,176,778,35,743],82:[676,0,722,26,716],83:[692,19,556,35,513],84:[676,0,667,31,636],85:[676,19,722,16,701],86:[676,18,722,16,701],87:[676,15,1000,19,981],88:[676,0,722,16,699],89:[676,0,722,15,699],90:[676,0,667,28,634],91:[678,149,333,67,301],92:[691,19,278,-25,303],93:[678,149,333,32,266],94:[676,-311,581,73,509],95:[-75,125,500,0,500],96:[713,-528,333,8,246],97:[473,14,500,25,488],98:[676,14,556,17,521],99:[473,14,444,25,430],100:[676,14,556,25,534],101:[473,14,444,25,427],102:[691,0,333,14,389],103:[473,206,500,28,483],104:[676,0,556,15,534],105:[691,0,278,15,256],106:[691,203,333,-57,263],107:[676,0,556,22,543],108:[676,0,278,15,256],109:[473,0,833,15,814],110:[473,0,556,21,539],111:[473,14,500,25,476],112:[473,205,556,19,524],113:[473,205,556,34,536],114:[473,0,444,28,434],115:[473,14,389,25,361],116:[630,12,333,19,332],117:[461,14,556,16,538],118:[461,14,500,21,485],119:[461,14,722,23,707],120:[461,0,500,12,484],121:[461,205,500,16,482],122:[461,0,444,21,420],123:[698,175,394,22,340],124:[691,19,220,66,154],125:[698,175,394,54,372],126:[333,-173,520,29,491],915:[676,0,620,16,593],916:[690,0,722,33,673],920:[692,18,778,35,743],923:[690,0,707,9,674],926:[676,0,647,40,607],928:[676,0,778,21,759],931:[676,0,671,28,641],933:[692,0,703,7,693],934:[676,0,836,18,818],936:[692,0,808,15,797],937:[692,0,768,28,740]};MathJax.OutputJax["HTML-CSS"].initFont("STIXGeneral-bold");MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/Bold/Main.js");
