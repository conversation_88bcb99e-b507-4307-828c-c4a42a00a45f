﻿@model EcoolWeb.ViewModels.ZZT17QueryListViewModel
@using ECOOL_APP.com.ecool.util
@{
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
}
@{
    ViewBag.Title = "系統操作紀錄-系統操作紀錄一覽表";
}
@Html.Partial("_Title_Secondary")

@Html.Partial("_Notice")


@using (Html.BeginForm("QUERY", "ZZT17", FormMethod.Post, new { id = "ZZT17" }))
{

    @Html.ActionLink("登入次數統計", "CountLoginQuery", "ZZT17", null, new { @class = "btn btn-sm btn-sys" })

    <br />
    <div class="form-inline" role="form">

        <div class="form-group">
            <label class="control-label">姓名/學生代號</label>
        </div>
        <div class="form-group">
            @Html.TextBoxFor(m => m.whereKeyword, new { htmlAttributes = new { @class = "form-control input-sm" } })
        </div>
        <div class="form-group">
            <label class="control-label">年級</label>
        </div>
        <div class="form-group">
            @Html.DropDownListFor(m => m.whereGrade, (IEnumerable<SelectListItem>)ViewBag.GradeItem, new { @class = "form-control input-sm", onchange = "FunPageProc(1)" })
        </div>
        <div class="form-group">
            <label class="control-label">班級</label>
        </div>
        <div class="form-group">
            @Html.DropDownListFor(m => m.whereCLASS_NO, (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control input-sm", onchange = "FunPageProc(1)" })
        </div>
        <br />
        <div class="form-group">
            <label class="control-label">登入時間</label>
        </div>
        <div class="form-group">
            <div class="form-inline">
                @Html.TextBoxFor(m => m.whereACTIONTIME_S, new { htmlAttributes = new { @class = "form-control input-sm", @readonly = "readonly" } })
                ~
                @Html.TextBoxFor(m => m.whereACTIONTIME_E, new { htmlAttributes = new { @class = "form-control input-sm", @readonly = "readonly" } })
            </div>

            @Html.HiddenFor(m => m.OrdercColumn)
            @Html.HiddenFor(m => m.whereSchoolNo)
            @Html.HiddenFor(m => m.whereUserNo)
            @Html.HiddenFor(m => m.Page)

        </div>
        <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1)" />
        <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />
    </div>

        <br />


    <div class="panel panel-ZZZ" name="TOP">
        <div class="panel-heading text-center">
            @Html.BarTitle()
        </div>
        <div class="table-responsive">
            <table class="table-ecool table-92Per table-hover table-ecool-ZZZ">
                <thead>
                    <tr>
                        <th style="text-align: center">
                            使用者代號
                        </th>
                        <th style="text-align: center">
                            使用者姓名
                        </th>
                        <th style="text-align: center;">
                            操作頁面
                        </th>
                        <th style="text-align: center;">
                            IP位置
                        </th>
                        <th style="text-align: center;">
                            操作時間
                        </th>
                        <th style="text-align: center">
                            操作狀態
                        </th>
                    </tr>
                </thead>
                 <tbody>
                     @foreach (var item in Model.ZZT17List)
                    {
                         <tr>
                             <td onclick="doSearch('whereSchoolNo','@item.SCHOOL_NO','whereUserNo','@item.USER_NO');">
                                 @if (item.USER_TYPE == UserType.Student || item.USER_TYPE == UserType.Parents)
                                 {
                                     @Html.DisplayFor(modelItem => item.USER_NO)
                                 }
                                 else
                                 {
                                     if (user.RoleID_Default == HRMT24_ENUM.SuperAdminROLE)
                                     {
                                         @Html.DisplayFor(modelItem => item.USER_NO)
                                     }
                                     else
                                     {
                                         @StringHelper.LeftStringR(item.USER_NO, 5, "*****")
                                     }
                                 }
                             </td>
                             <td>
                                 @Html.DisplayFor(modelItem => item.USERNAME)
                             </td>
                             <td>
                                 @Html.DisplayFor(modelItem => item.ACTION_ID)
                             </td>
                             <td>
                                 @Html.DisplayFor(modelItem => item.IP_ADRESS)
                             </td>
                             <td>
                                 @Html.DisplayFor(modelItem => item.ACTIONTIME)
                             </td>
                             <td style="text-align: left;white-space:normal">
                                 @Html.DisplayFor(modelItem => item.LOG_STATUS)
                             </td>
                         </tr>
                     }
                 </tbody>
            </table>
        </div>
    </div>


        <div>
            @Html.Pager(Model.ZZT17List.PageSize, Model.ZZT17List.PageNumber, Model.ZZT17List.TotalItemCount).Options(o => o
              .DisplayTemplate("BootstrapPagination")
             .MaxNrOfPages(5)
             .SetPreviousPageText("上頁")
             .SetNextPageText("下頁")
         )
        </div>
}

@section scripts{
    <script>
        var targetFormID = '#ZZT17';

        $(function () {

            $("#whereACTIONTIME_S,#whereACTIONTIME_E").datepicker({
                dateFormat: "yy/mm/dd",
                changeMonth: true,
                changeYear: true,
                showOn: "button",
                buttonImage: "../Content/img/icon/calendar.gif",
                buttonImageOnly: true
            });
        });

        function FunPageProc(page) {
            if ($(targetFormID).size() > 0) {
                $('#Page').val(page)
                $(targetFormID).submit();
            }
        };


        function doSort(SortCol) {
            $("#OrdercColumn").val(SortCol);
            FunPageProc(1)
        }
        function doSearch(ColName, whereValue) {
            $("#" + ColName).val(whereValue);
            FunPageProc(1)
        }
        function doSearch(ColName, whereValue, ColName2, whereValue2) {
            $("#" + ColName).val(whereValue);
            $("#" + ColName2).val(whereValue2);
            FunPageProc(1)
        }

        function todoClear() {
            ////重設

            $(targetFormID).find(":input,:selected").each(function (i) {

                var type = $(this).attr('type')
                var InPreadonly = $(this).attr('readonly')
                var tag = this.tagName.toLowerCase(); // normalize case

                if (InPreadonly == false || InPreadonly == undefined) {

                    if (type == 'radio' || type == 'checkbox') {
                        if ($(this).attr("title") == 'Default') {
                            this.checked = true;
                        }
                        else {
                            this.checked = false;
                        }
                    }
                    else if (tag == 'select') { //下拉式選單
                        this.selectedIndex = 0;
                    }
                    else if (type == 'text' || type == 'hidden' || type == 'password' || type == 'textarea') {
                        this.value = '';
                    }
                }
            });

            $(targetFormID).submit();
        }
    </script>
}