@model EcoolWeb.ViewModels.ADDI06IndexViewModel

@{
    if ((Model?.WhereIsColorboxForUser ?? false))
    {
        ViewBag.Title = "我的校內表現";
    }
    else
    {
        ViewBag.Title = "校內表現-校內表現一覽表";
    }
    ECOOL_DEVEntities db = new ECOOL_DEVEntities();
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode || (Model?.WhereIsColorboxForUser ?? false))
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
    if (string.IsNullOrWhiteSpace(Model.NoLayout) == false)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
}
<link href="~/Content/colorbox/example4/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>

@helper  buttonFun(ECOOL_APP.UserProfile user)
{
    <br />
    <div class="form-inline">
        <div class="col-xs-12 text-right">
            @{
                string ActiveSchool = (Model.ClassKind == string.Empty || Model.ClassKind == null) ? "active" : "";
                string ActiveClass = (string.IsNullOrWhiteSpace(Model.ClassKind) == false) ? "active" : "";

                <button class="btn btn-xs btn-pink  @ActiveSchool" type="button" onclick="doSearch('ClassKind', '');">學校表現</button>
                <button class="btn btn-xs btn-pink  @ActiveClass" onclick="doSearch('ClassKind','class');" type="button">班級表現</button>
            }

            @if (user != null)
            {
                if (user.USER_TYPE == UserType.Student || user.USER_TYPE == UserType.Parents)
                {

                    string MyBtnName = string.Empty;
                    string whereUserNoValue = string.Empty;

                    if (user.USER_TYPE == UserType.Student)
                    {
                        MyBtnName = "我的校內表現";
                        whereUserNoValue = user.USER_NO;
                    }
                    else
                    {
                        MyBtnName = "寶貝校內表現";
                        whereUserNoValue = HRMT06.GetStringMyPanyStudent(user);
                    }

                    string ActiveAll = (Model.whereUserNo == string.Empty || Model.whereUserNo == null) ? "active" : "";
                    string ActiveMe = (Model.whereUserNo == whereUserNoValue) ? "active" : "";

                    <button class="btn btn-xs btn-pink  @ActiveAll" type="button" onclick="doSearch('whereUserNo', '');">全部</button>
                    <button class="btn btn-xs btn-pink  @ActiveMe" onclick="doSearch('whereUserNo','@whereUserNoValue');" type="button">@MyBtnName</button>

                }
                else
                {
                    <button type="button" class="btn btn-sm btn-sys" onclick="PrintBooK()">我要列印</button>
                }
            }
        </div>
    </div>
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@using (Html.BeginForm("QUERY", "ADDI06", FormMethod.Post, new { id = "ADDI06", name = "form1" }))
{
    @Html.HiddenFor(m => m.OrdercColumn)
    @Html.HiddenFor(m => m.whereUserNo)
    @Html.HiddenFor(m => m.Page)
    @Html.HiddenFor(m => m.NoLayout)
    @Html.HiddenFor(m => m.ClassKind)
    @Html.HiddenFor(m => m.WhereIsColorboxForUser)
    
    if ((Model?.WhereIsColorboxForUser ?? false) == false)
    {
        if (ViewBag.VisableInsert == true)
        {
            <div class="form-inline">
                <a role="button" href='@Url.Action("ADD", "ADDI06")' class="btn btn-sm btn-sys">
                    新增校內表現
                </a>
                <br />
                <br />
            </div>

        }
        <div class="form-inline" role="form">
            <div class="form-group">
                <label class="control-label">搜尋</label>
            </div>
            <div class="form-group" title="學號/姓名/公告人/類型/事蹟">

                @Html.EditorFor(m => m.whereKeyword, new { htmlAttributes = new { @class = "form-control input-sm" } })
            </div>

            <div class="form-group">
                <label class="control-label">年級</label>
            </div>
            <div class="form-group">
                @Html.DropDownListFor(m => m.whereGrade, (IEnumerable<SelectListItem>)ViewBag.GradeItem, new { @class = "form-control input-sm", onchange = "FunPageProc(1)" })
            </div>
            <div class="form-group">
                <label class="control-label">班級</label>
            </div>
            <div class="form-group">
                @Html.DropDownListFor(m => m.whereCLASS_NO, (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control input-sm", onchange = "FunPageProc(1)" })
            </div>
            <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1)" />
            <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />
        </div>
        <br />
        @buttonFun(user)
    }

    <img src="~/Content/img/web-bar2-revise-10.png" style="width:100%" class="img-responsive " alt="Responsive image" />
    <div class="table-responsive">
        <div class="text-center">
            <table class="table-ecool table-92Per table-hover table-ecool-ADDI06">
                <thead>
                    <tr>
                        <th style="text-align: center;cursor:pointer;" onclick="doSort('CREATEDATE');">
                            日期
                            <img id="CREATEDATE" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </th>
                        @if (AppMode == false)
                        {
                            <th style="text-align: center">
                                公告人
                            </th>
                        }

                        <th style="text-align: center;cursor:pointer;" onclick="doSort('CLASS_NO');">
                            班級
                            <img id="CLASS_NO" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </th>
                        @if (AppMode == false)
                        {
                            <th style="text-align: center;cursor:pointer;" onclick="doSort('USERNAME');">
                                姓名
                                <img id="USERNAME" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                            </th>
                        }
                        <th style="text-align: center">
                            獎懲類別
                        </th>
                        <th style="text-align: center;width:200px">
                            具體事蹟
                        </th>
                        <th style="text-align: center">
                        </th>
                        <th style="text-align: center">
                            獎勵點數
                        </th>
                       
                        @*<th style="text-align: center">
            變更<br />圖片<br />人員
        </th>
        <th style="text-align: center">
            變更<br />圖片<br />日期
        </th>*@
                        <th style="text-align: center">
                        </th>
                    </tr>
                </thead>
                <tbody>
                    
                    @foreach (var item in Model.ADDT14List)
                    {
                        string ModifyUser = "";
                        string ModifySCHOOLNO = "";
                        string USERNAME = "";
                        string ModifyDateInfo = "";
                        DateTime ModifyDate = new DateTime();

                        if (item.ImgSourceNO != null) {
                            ModifyUser = db.ImageModifyTemp.Where(x => x.Source_ID == item.ImgSourceNO.Trim()).Select(x=>x.USER_NO).FirstOrDefault();
                            ModifySCHOOLNO  = db.ImageModifyTemp.Where(x => x.Source_ID == item.ImgSourceNO.Trim()).Select(x => x.SCHOOL_NO).FirstOrDefault();
                       
                            USERNAME = db.HRMT01.Where(x => x.USER_NO == ModifyUser && x.SCHOOL_NO == ModifySCHOOLNO).Select(x=>x.NAME).FirstOrDefault();
                            ModifyDate = db.ImageModifyTemp.Where(x => x.Source_ID == item.ImgSourceNO.Trim()).Select(x =>(DateTime) x.CHG_DATE).FirstOrDefault();
                            ModifyDateInfo = ModifyDate.ToShortDateString();
                        }
                    <tr>
                        <td>
                            @Html.DisplayFor(modelItem => item.CREATEDATE, "ShortDateTime")
                        </td>
                        @if (AppMode == false)
                        {
                            <td>
                                @Html.DisplayFor(modelItem => item.TNAME)
                            </td>
                        }

                        <td>
                            @Html.DisplayFor(modelItem => item.CLASS_NO)
                        </td>
                        @if (AppMode == false)
                        {
                            <td>
                                @Html.DisplayFor(modelItem => item.SNAME)
                            </td>}

                        @if (item.IAWARD_From != null && item.IAWARD_From == "LotteryPrize")
                        {

                            <td title="@item.IAWARD_KIND">
                                參加抽獎活動
                            </td>
                        }
                        else
                        {

                            <td>
                                @Html.DisplayFor(modelItem => item.IAWARD_KIND)
                            </td>
                        }

                        <td style="text-align: left;white-space:normal">
                            @Html.DisplayFor(modelItem => item.IAWARD_ITEM)
                        </td>
                        <td style="text-align: center">
                            @if (!string.IsNullOrWhiteSpace(item.IMG_FILE))
                            {

                                <span data-toggle="tooltip" title="變更圖片人員:@USERNAME ,變更圖片日期: @ModifyDateInfo">
                                    @{ string Img_Path = new ECOOL_APP.FileHelper().GetDirectorySysPathImg(item.SCHOOL_NO, EcoolWeb.Controllers.ADDI06Controller.ImgPath, item.IAWARD_ID.ToString(), item.IMG_FILE);
                                    }
                                    <img class="Img_Path" src="@Img_Path" style="max-height:30px" href="@Img_Path" />
                                </span>
                            }
                        </td>
                        <td>

                            @Html.DisplayFor(modelItem => item.CASH)

                        </td>
                        @*<td>
            @USERNAME
        </td>
        <td>
            @ModifyDateInfo
        </td>*@
                        <td style="text-align: left;white-space:nowrap">
                            @if (ViewBag.VisableMODIFY == true)
                            {
                                if (user.ROLE_LEVEL == (decimal)HRMT24_ENUM.QAdminLevel || user.USER_TYPE == UserType.Admin || (user.USER_TYPE == UserType.Teacher && user.NAME == item.TNAME))
                                {
                                    @Html.ActionLink("維護", "MODIFY", new { IAWARD_ID = item.IAWARD_ID }, new { @class = "btn btn-xs btn-Basic" })
                                }
                            }
                            @if (ViewBag.StudentUploadIMG == true)
                            {
                                if (user.USER_TYPE == UserType.Student && item.USER_NO == user?.USER_NO)
                                {
                                    @Html.ActionLink("維護", "MODIFY1", new { IAWARD_ID = item.IAWARD_ID }, new { @class = "btn btn-xs btn-Basic" })

                                }

                            }
                            <br />
                            @if (ViewBag.VisableDelete == true)
                            {
                                if (user.USER_TYPE == UserType.Admin || (user.USER_TYPE == UserType.Teacher && user.NAME == item.TNAME))
                                {
                                    @Html.ActionLink("作廢", "Delete", new { IAWARD_ID = item.IAWARD_ID }, new { @class = "btn btn-xs btn-Basic" });
                                }
                            }
                        </td>
                    </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>
    <div>
        @Html.Pager(Model.ADDT14List.PageSize, Model.ADDT14List.PageNumber, Model.ADDT14List.TotalItemCount).Options(o => o
              .DisplayTemplate("BootstrapPagination")
             .MaxNrOfPages(5)
             .SetPreviousPageText("上頁")
             .SetNextPageText("下頁")
         )
    </div>

}
@section Scripts {
    <script  nonce="cmlvaw">
        window.ADDI06_Query_URLS = {
            query: "QUERY"

        };


    </script>
   <script src="~/Scripts/ADDI06/qeury.js" nonce="cmlvaw">



    </script>
    <script type="text/javascript">
        $(function () {
            $(".Img_Path").colorbox({ opacity: 0.82, width: "70%", innerHeight: "500px", scrolling: true });
            $('[data-toggle="tooltip"]').tooltip();
        });

        function doSearch(ColName, whereValue) {
            $("#" + ColName).val(whereValue);
            FunPageProc(1);
        }

        function doSort(SortCol) {
            $("#OrdercColumn").val(SortCol);
            FunPageProc(1);
        }

        function FunPageProc(pageno) {
            $("#Page").val(pageno);
            form1.submit();
        }

        function todoClear() {
            $(form1).find(":input,:selected").each(function () {
                var type = $(this).attr('type');
                var InPreadonly = $(this).attr('readonly');
                var tag = this.tagName.toLowerCase();

                if (InPreadonly == false || InPreadonly == undefined) {
                    if (type == 'radio' || type == 'checkbox') {
                        if ($(this).attr("title") == 'Default') {
                            this.checked = true;
                        }
                        else {
                            this.checked = false;
                        }
                    }
                    else if (tag == 'select') {
                        this.selectedIndex = 0;
                    }
                    else if (type == 'text' || type == 'hidden' || type == 'password' || type == 'textarea') {
                        this.value = '';
                    }
                }
            });

            FunPageProc(1);
        }

        function PrintBooK() {
            var url = '@Url.Action("PrintQuery", "ADDI06").ToString()';
            window.open(url, '_blank');
        }
    </script>
}