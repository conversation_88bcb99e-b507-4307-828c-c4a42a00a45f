﻿@model ZZZI34OrderListViewModel
@{
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    string HidStyle = Model.IsPrint == true ? "display:none" : "";
    int RowNumber = 0;
    int ResetNumber = 0;
}
<script src="~/Scripts/Blob.js"></script>
<script src="~/Scripts/FileSaver.js"></script>
<script src="~/Scripts/tableexport.js"></script>
<link href="~/Scripts/timepicker/jquery-ui-timepicker-addon.css" rel="stylesheet" />
<script src="~/Scripts/timepicker/jquery-ui-timepicker-addon.js"></script>
<script src="~/Scripts/timepicker/jquery-ui-timepicker-zh-TW.js"></script>
<script src="~/Scripts/timepicker/jquery.ui.datepicker-zh-TW.js"></script>
@*<script src="~/Scripts/moment.js"></script>*@
<script src="~/Scripts/moment.min.js"></script>
<style type="text/css" media="print">

    /* 列印時隱藏 */
    .cScreen {
        display: none;
    }
</style>

@Html.HiddenFor(m => m.WhereUserNo)
@Html.HiddenFor(m => m.OrdercColumn)
@Html.HiddenFor(m => m.Page)
@Html.HiddenFor(m => m.IsPrint)
@Html.HiddenFor(m => m.IsToExcel)
@Html.HiddenFor(m => m.WhereIsMonthTop)

<div class="form-inline" role="form" id="Q_Div" style="@HidStyle">
    <div class="form-group">
        <label class="control-label">學號/姓名</label>
    </div>
    <div class="form-group">
        @Html.EditorFor(m => m.WhereKeyword, new { htmlAttributes = new { @class = "form-control" } })
    </div>
    <div class="form-group">
        <label class="control-label">年級</label>
    </div>
    <div class="form-group">
        @Html.DropDownListFor(m => m.WhereGrade, (IEnumerable<SelectListItem>)ViewBag.GradeItem, new { @class = "form-control", onchange = "FunPageProc(1)" })
    </div>
    <div class="form-group">
        <label class="control-label">班級</label>
    </div>
    <div class="form-group">
        @Html.DropDownListFor(m => m.WhereCLASS_NO, (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control", onchange = "FunPageProc(1)" })
    </div>
    <div id="Sdate">
        <div class="form-group">
            <label class="control-label">投稿日期(起)</label>
        </div>

        <div class="form-group">
            @Html.EditorFor(m => m.whereSTART_CRE_DATE, new { htmlAttributes = new { @class = "form-control input-sm", autocomplete = "off" } })
        </div>
        <div class="form-group">
            <label class="control-label">投稿日期(迄)</label>
        </div>
        <div class="form-group">
            @Html.EditorFor(m => m.whereEND_CRE_DATE, new { htmlAttributes = new { @class = "form-control input-sm", autocomplete = "off" } })
        </div>
    </div>
    <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1)" />
    <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />

    @if (user != null)
    {
        if (user.USER_TYPE != UserType.Student && user.USER_TYPE != UserType.Parents)
        {

            if (!Model.IsPrint)
            {
                <button id="ButtonExcel" class="btn-yellow btn btn-sm cScreen" onclick="exportExcel()" style="float:right">匯出excel</button>
                <button type="button" class="btn-yellow btn btn-sm" onclick="PrintBooK()" style="float:right;margin-right:5px">我要列印</button>
            }
            else
            {
                <button type="button" class="btn-yellow btn btn-sm" onclick="PrintBooK()" style="float:right;margin-right:5px">我要列印</button>
            }

        }
    }
</div>

<div class="form-inline cScreen" style="text-align:right;@HidStyle">
    <br />
    <button class="btn btn-xs btn-pink @(Model.WhereIsMonthTop == false ? "active":"")" type="button" onclick="todoClear();doMonthTop('false');">全部</button>
    <button class="btn btn-xs btn-pink @(Model.WhereIsMonthTop == true ? "active":"")" type="button" onclick="todoClear();doMonthTop('true');">月排行榜</button>
</div>

<img src="~/Content/img/web-bar-Art-gallery.png" style="width:100%;@HidStyle" class="img-responsive App_hide " alt="Responsive image" />
<div class="@(Model.IsPrint ? "":"table-responsive")">
    <div class="text-center" id="tbData">
        <table class="@(Model.IsPrint ? "table table-bordered" : "table-ecool table-92Per table-hover table-ecool-reader")">
            <thead>
                <tr>
                    <th>序號</th>
                    <th>@Html.DisplayNameFor(m => m.ListData.First().CLASS_NO)</th>
                    <th>@Html.DisplayNameFor(m => m.ListData.First().SEAT_NO)</th>
                    <th>@Html.DisplayNameFor(m => m.ListData.First().SNAME)</th>
                    <th style="text-align: center;cursor:pointer;" onclick="doSort('WorkCount');">
                        @Html.DisplayNameFor(m => m.ListData.First().WorkCount)
                        <img id="WorkCount" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                    </th>
                    <th style="text-align: center;cursor:pointer;" onclick="doSort('SumCash');">
                        @Html.DisplayNameFor(m => m.ListData.First().SumCash)
                        <img id="SumCash" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                    </th>
                    <th style="text-align: center;cursor:pointer;" onclick="doSort('LikeCount');">
                        @Html.DisplayNameFor(m => m.ListData.First().LikeCount)
                        <img id="LikeCount" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                    </th>
                </tr>
            </thead>
            <tbody>
                @foreach (var item in Model.ListData)
                {
                    ResetNumber++;
                    RowNumber = Model.ListData.PageSize * (Model.ListData.PageNumber - 1) + (ResetNumber);
                    <tr>
                        <td>@RowNumber</td>
                        <td style="text-align: center">@Html.DisplayFor(modelItem => item.CLASS_NO)</td>
                        <td style="text-align: center">@Html.DisplayFor(modelItem => item.SEAT_NO)</td>
                        <td>@Html.DisplayFor(modelItem => item.SNAME)</td>
                        <td style="text-align: center;">@Html.DisplayFor(modelItem => item.WorkCount)</td>
                        <td style="text-align: center;">@Html.DisplayFor(modelItem => item.SumCash)</td>
                        <td style="text-align: center;">@Html.DisplayFor(modelItem => item.LikeCount)</td>
                    </tr>
                }
            </tbody>
        </table>
    </div>
</div>

<div>
    @Html.Pager(Model.ListData.PageSize, Model.ListData.PageNumber, Model.ListData.TotalItemCount).Options(o => o
                            .DisplayTemplate(PageGlobal.DfDisplayTemplate)
                            .MaxNrOfPages(PageGlobal.DfMaxNrOfPages)
                            .SetPreviousPageText(PageGlobal.DfSetPreviousPageText)
                            .SetNextPageText(PageGlobal.DfSetNextPageText)
                            )
</div>
<script>
    $(function () {

        var MonthDEF = "";
        MonthDEF = $(".active").attr("Mouth");
        if (MonthDEF == "true") {
            $("#Sdate").attr("hidden", "hidden");
        }

        initDatepicker();
    });
         function initDatepicker() {
                   var opt = {
                        showMonthAfterYear: true,
                        format: moment().format('YYYY-MM-DD'),
                        showSecond: true,
                        showButtonPanel: true,
                        showTime: true,
                        beforeShow: function () {
                            setTimeout(
                                function () {
                                    $('#ui-datepicker-div').css("z-index", 15);
                                }, 100
                            );
                        },
                        onSelect: function (dateText, inst) {
                            $('#' + inst.id).attr('value', dateText);
                        }
                    };
                    $("#@Html.IdFor(m => m.whereSTART_CRE_DATE)").datetimepicker(opt);
                    $("#@Html.IdFor(m => m.whereEND_CRE_DATE)").datetimepicker(opt);
            }
</script>