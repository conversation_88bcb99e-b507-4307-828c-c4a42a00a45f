﻿using com.ecool.service;
using ECOOL_APP;
using ECOOL_APP.com.ecool.service;
using ECOOL_APP.com.ecool.util;
using ECOOL_APP.EF;
using EcoolWeb.CustomAttribute;
using EcoolWeb.Models;
using System.IO.Compression;
using log4net;
using NPOI.SS.UserModel;
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using System.Text.RegularExpressions;

namespace EcoolWeb.Controllers
{
    [SessionExpire]
    public class ADDI10Controller : Controller
    {
        /// <summary>
        /// 此Controller 對應的程式代碼
        /// </summary>
        private static string Bre_NO = "ADDI10";

        private string Bre_Name = BreadcrumbService.GetBRE_NAME(Bre_NO);

        /// <summary>
        /// 會員相關資訊
        /// </summary>
        private UserProfile user;

        private string SCHOOL_NO = string.Empty;
        private string USER_NO = string.Empty;

        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();
        private ADDI10Service Service = new ADDI10Service();

      [CheckPermission] //檢查權限
        public ActionResult Index()
        {
            this.Shared();
            //UserProfile user = UserProfileHelper.Get();

            return View();
        }

        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult _PageContent(ADDI10IndexViewModel model)
        {
            this.Shared();
            if (model == null) model = new ADDI10IndexViewModel();
            if (model.Search == null) model.Search = new ADDI10SearchViewModel();

            if (model.Search.WhereSOU_KEY == null)
            {
                model.Search.WhereSOU_KEY = SCHOOL_NO;
            }

            model.Search.BackAction = "Index";
            model.Search.BackController = Bre_NO;
            model = Service.GetVoteListData(model, user, ref db);

            ViewBag.WinOpenShareUrlLink = UrlCustomHelper.GetOwnWebUri() + $@"/EcoolWeb//{Bre_NO}/_PageContent2";
            ViewBag.USER_KEY = user?.USER_KEY ?? string.Empty;

            return PartialView(model);
        }

        /// <summary>
        /// 分享網址 ==>不知在寫什麼，問 P妹
        /// </summary>
        /// <param name="Mode"></param>
        /// <param name="EditNum"></param>
        /// <returns></returns>
        public ActionResult _PageContent2(string Mode, string EditNum,string SCHOOLNO)
        {
            System.Diagnostics.Debug.WriteLine(System.Web.HttpContext.Current.Session.SessionID);
            UserProfile u = (UserProfile)System.Web.HttpContext.Current.Session[CONSTANT.SESSION_USER_KEY];
            if (System.Web.HttpContext.Current.Session != null && u != null)

            {
                this.Shared();
            }
               
            ADDI10EditViewModel model = new ADDI10EditViewModel();
            model.Search = new ADDI10SearchViewModel();
            model.Search.LoginYN = "N";
            if (user != null)
            {
                model.Search.LoginYN = "Y";
            }
            else
            {

                if (this.Request.RequestContext.HttpContext.Session[CONSTANT.SESSION_SCHOOL_KEY] != null) { 
                    this.Request.RequestContext.HttpContext.Session[CONSTANT.SESSION_SCHOOL_KEY] = null;
                }
                return RedirectToAction("LoginPage", "Home", new { returnURL = UrlCustomHelper.GetOwnWebUri() + $@"/EcoolWeb/{Bre_NO}/_PageContent2?Mode=Edit&Rule=A&EditNum=" + EditNum, SCSCHOOLNO = SCHOOLNO });
            }

            model.Search.WhereQUESTIONNAIRE_ID = EditNum;

            return View(model);
        }

        public ActionResult Edit(ADDI10EditViewModel model)
        {
            this.Shared();
            if (model == null) model = new ADDI10EditViewModel();
            if (model.Search == null) model.Search = new ADDI10SearchViewModel();

            if (model.StudentCardVote == null)
            {
                model.StudentCardVote = false;
            }

            if (model.StudentCardVote == false)
            {
                UserProfileHelper.ClearGameNoCookie();

                var Use_YN = PermissionService.GetPermission_Use_YN(Bre_NO, "Index", SCHOOL_NO, USER_NO);

                if (Use_YN == SharedGlobal.N)
                {
                    return RedirectToAction(ErrorHelper.ErrorVal.PermissionError, "Error");
                }

                if (user == null)
                {
                    return RedirectToAction(ErrorHelper.ErrorVal.PermissionError, "Error");
                }

                if (Service.IsCheckVote(model.Search.WhereQUESTIONNAIRE_ID, user, ref db))
                {
                    TempData["StatusMessage"] = "您已投票過了";
                    ADDI10IndexViewModel QModel = new ADDI10IndexViewModel()
                    {
                        Search = model.Search
                    };
                    return View("Index", QModel);
                }
            }
            else
            {
                if (user != null) //數位學生證投票 管理者第一次進來 ，把key 塞進 Cookie，之後要用 Cookie 驗証，是否有權使用 數位學生證投票
                {
                    if (HRMT24_ENUM.CheckQAdmin(user))
                    {
                        UserProfileHelper.SetGameNoCookieData(model.Search.WhereQUESTIONNAIRE_ID, false);
                    }

                    LogHelper.AddLogToDB(user.SCHOOL_NO, null, System.Web.HttpContext.Current.Request.UserHostAddress, "HOME", "LoginOUT");
                    this.Request.RequestContext.HttpContext.Session[CONSTANT.SESSION_USER_KEY] = null;
                }

                if (string.IsNullOrWhiteSpace(UserProfileHelper.GetGameNoCookie()))
                {
                    return RedirectToAction(ErrorHelper.ErrorVal.PermissionError, "Error");
                }
                else
                {
                    UserProfileHelper.SetGameNoCookieData(model.Search.WhereQUESTIONNAIRE_ID, false);
                }
            }

            if (string.IsNullOrWhiteSpace(model.Search.WhereQUESTIONNAIRE_ID))
            {
                return RedirectToAction(ErrorHelper.ErrorVal.NotParameterError, "Error");
            }

            model = Service.GetVoteDetailsPreview(model, ref db);

            if (model.Title == null)
            {
                return RedirectToAction(ErrorHelper.ErrorVal.NotFindError, "Error");
            }
            string Creperson = "";
            Creperson = user.SCHOOL_NO + "_" + user.USER_NO;
            if ((model.Title.ANSWER_PERSON_YN ?? "N") == "Y" && model.StudentCardVote == false && db.SAQT01.Where(x => x.CRE_PERSON == Creperson).Any() == false)
            {
                if (db.REFT01.Where(A => (A.REF_KEY == model.Title.QUESTIONNAIRE_ID
                                && A.REF_TABLE == "SAQT01"
                                && A.SCHOOL_NO == SCHOOL_NO
                                && A.USER_NO == USER_NO)).Any() == false)
                {
                    if (db.SAQT01.Where(x => x.CRE_PERSON == Creperson).Any() == false)
                    {
                        TempData["StatusMessage"] = "您無權投票";
                    }

                    ADDI10IndexViewModel QModel = new ADDI10IndexViewModel()
                    {
                        Search = model.Search
                    };
                    return View("Index", QModel);
                }
                if (model.Title.QUESTIONNAIRE_EDATE < DateTime.Now || model.Title.QUESTIONNAIRE_SDATE > DateTime.Now)
                {
                    ADDI10IndexViewModel QModel = new ADDI10IndexViewModel();
                    return View("Index", QModel);
                }
            }
            if (model.Title.QUESTIONNAIRE_EDATE < DateTime.Now || model.Title.QUESTIONNAIRE_SDATE > DateTime.Now)
            {
                ADDI10IndexViewModel QModel = new ADDI10IndexViewModel();
                return View("Index", QModel);
            }
            this.SetTitle(Bre_Name + "-" + model.Title.QUESTIONNAIRE_NAME);

            Session.Timeout = 3000;
            return View(model);
        }

        public ActionResult OneIndex(ADDI10IndexViewModel model)
        {
            this.Shared();
            return View(model);
        }

        public JsonResult CheckCardNo(string CARD_NO)
        {
            this.Shared();
            string Success = string.Empty;
            string ErrorMsg = string.Empty;

            var h01 = HRMT01.CheckCardNoToHrmt01(SCHOOL_NO, CARD_NO, ref db, ref ErrorMsg);

            if (!string.IsNullOrWhiteSpace(ErrorMsg))
            {
                Success = false.ToString();
            }
            else
            {
                var QUESTIONNAIRE_ID = UserProfileHelper.GetGameNoCookie();

                if (Service.IsCheckVote(QUESTIONNAIRE_ID, h01.SCHOOL_NO, h01.USER_NO, ref db))
                {
                    ErrorMsg = " 您已投票過了";
                    Success = false.ToString();
                }
                else
                {
                    Success = true.ToString();
                }
            }

            var data = "{ \"Success\" : \"" + Success + "\" , \"Error\" : \"" + ErrorMsg + "\" }";
            return Json(data, JsonRequestBehavior.AllowGet);
        }

        public ActionResult EditSave(ADDI10EditViewModel model, string CARD_NO)
        {
            this.Shared();
            if (model == null) model = new ADDI10EditViewModel();
            if (model.Search == null) model.Search = new ADDI10SearchViewModel();

            if (model.StudentCardVote == false)
            {
                UserProfileHelper.ClearGameNoCookie();

                var Use_YN = PermissionService.GetPermission_Use_YN(Bre_NO, "Index", SCHOOL_NO, USER_NO);

                if (Use_YN == SharedGlobal.N)
                {
                    return RedirectToAction(ErrorHelper.ErrorVal.PermissionError, "Error");
                }

                if (user == null)
                {
                    return RedirectToAction(ErrorHelper.ErrorVal.PermissionError, "Error");
                }

                if (Service.IsCheckVote(model.Search.WhereQUESTIONNAIRE_ID, user, ref db))
                {
                    TempData["StatusMessage"] = "您已投票過了";
                    ADDI10IndexViewModel QModel = new ADDI10IndexViewModel()
                    {
                        Search = model.Search
                    };

                    return RedirectToAction("Index", new { model = QModel });
                }
            }
            else
            {
                if (string.IsNullOrWhiteSpace(UserProfileHelper.GetGameNoCookie()))
                {
                    return RedirectToAction(ErrorHelper.ErrorVal.PermissionError, "Error");
                }
                else
                {
                    UserProfileHelper.SetGameNoCookieData(model.Search.WhereQUESTIONNAIRE_ID, false);
                }
            }

            model = Service.GetVoteDetailsPreview(model, ref db);

            foreach (var item in model.Topic)
            {
                var D_item = item.Topic_D.FirstOrDefault();

                if (Convert.ToBoolean(item.Q_MUST))
                {
                    var temp = item.ANSWER;
                    if (string.IsNullOrWhiteSpace(item.ANSWER))
                    {
                        ModelState.AddModelError(string.Format("Topic[{0}].ANSWER", item.Index), SAQT03.Validate.requiredToStr(D_item.Q_REQUIRED_MSG));
                    }
                    else
                    {
                        var temp1 = item.ANSWER;
                        if (item.ANSWER == "System.Web.HttpPostedFileWrapper")
                        {
                            var uploadFile = Request.Files[$"Topic[{item.Index}].ANSWER"];
                            item.UploadAnswerFile = uploadFile;

                            if (uploadFile.ContentLength == 0)
                            {
                                ModelState.AddModelError(string.Format("Topic[{0}].ANSWER", item.Index), SAQT03.Validate.requiredToStr(D_item.Q_REQUIRED_MSG));
                            }
                            else
                            {

                                if (D_item.Q_INPUT_TYPE == "UploadImage")
                                {

                                    Array[] validExts = new Array[5];
                                    List<string> validExtstrs = new List<string>();
                                    validExtstrs.Add(".jpg");
                                    validExtstrs.Add(".jpeg");
                                    validExtstrs.Add(".png");
                                    validExtstrs.Add(".gif");
                                    validExtstrs.Add(".bmp");
                                    var fileExt = uploadFile.FileName.Substring(uploadFile.FileName.LastIndexOf("."));
                                    if (validExtstrs.IndexOf(fileExt)<0)

                                    {

                                        ModelState.AddModelError(string.Format("Topic[{0}].ANSWER", item.Index), "請上傳 jpg、jpeg、png、gif、bmp 等格式的圖片");
                                    }


                                }
                                else if (D_item.Q_INPUT_TYPE == "UploadPdf")
                                {
                                    List<string> validExtstrs = new List<string>();
                                    validExtstrs.Add(".pdf");
                                    validExtstrs.Add(".jpg");
                                    validExtstrs.Add(".jpeg");
                                    validExtstrs.Add(".png");
                                    validExtstrs.Add(".gif");
                                    validExtstrs.Add(".bmp");
                                    var fileExt = uploadFile.FileName.Substring(uploadFile.FileName.LastIndexOf("."));
                                    if (validExtstrs.IndexOf(fileExt) < 0)

                                    
                                        {

                                        ModelState.AddModelError(string.Format("Topic[{0}].ANSWER", item.Index), "請上傳 pdf、jpg、jpeg、png、gif、bmp 等格式的");
                                    }

                                }
                                else if (D_item.Q_INPUT_TYPE == "UploadWorld")
                                {
                                    List<string> validExtstrs = new List<string>();
                                    validExtstrs.Add(".doc");
                                    validExtstrs.Add(".docx");

                                    var fileExt = uploadFile.FileName.Substring(uploadFile.FileName.LastIndexOf("."));
                                    if (validExtstrs.IndexOf(fileExt) < 0)


                                    {

                                        ModelState.AddModelError(string.Format("Topic[{0}].ANSWER", item.Index), "請上傳 doc、docx 等格式的");
                                    }

                                }
                            }
                        }
                    }

                    if (item.Q_MUTIPLE_CHOICES_OF_NUM != null)
                    {
                        var ANSWER = (item.ANSWER ?? "").Split(',');

                        if (ANSWER.Length != item.Q_MUTIPLE_CHOICES_OF_NUM)
                        {
                            ModelState.AddModelError(string.Format("Topic[{0}].ANSWER", item.Index), $"*此題必需勾選{item.Q_MUTIPLE_CHOICES_OF_NUM}筆");
                        }
                    }
                }
                else if (!Convert.ToBoolean(item.Q_MUST) && item.ANSWER == "System.Web.HttpPostedFileWrapper") {

                    var uploadFile = Request.Files[$"Topic[{item.Index}].ANSWER"];
                    item.UploadAnswerFile = uploadFile;
                }
            }

            string Message = string.Empty;

            if (ModelState.IsValid == false)
            {
                Message = "警告!!輸入的內容有錯誤<br/>";
            }
            else
            {
                HRMT01 hRMT01 = null;

                if (model.StudentCardVote == false)
                {
                    hRMT01 = db.HRMT01.Where(x => x.SCHOOL_NO == user.SCHOOL_NO && x.USER_NO == user.USER_NO).FirstOrDefault();

                    if (hRMT01 == null)
                    {
                        Message = "系統發生錯誤;原因:對應不到學生身份，你重新執行!!";
                    }
                }
                else
                {
                    hRMT01 = HRMT01.CheckCardNoToHrmt01(SCHOOL_NO, CARD_NO, ref db, ref Message);
                }
             
                if (string.IsNullOrWhiteSpace(Message))
                {
                    bool OK = Service.SaveVote(model, user, ref db, ref Message, hRMT01);

                    if (OK)
                    {
                        if (model.StudentCardVote == false)
                        {
                            if (model.Title.CASH != null)
                            {
                                if (user.USER_TYPE == UserType.Student || user.USER_TYPE == UserType.Teacher || user.USER_TYPE == UserType.Admin)
                                {
                                    //更新顯示
                                    UserProfile.RefreshCashInfo(user, ref db);
                                    UserProfileHelper.Set(user);
                                    TempData["StatusMessage"] = string.Format("您針對{0}已投票完成，獲得酷幣點數{1}點!!", model.Title.QUESTIONNAIRE_NAME, model.Title.CASH);
                                }
                                else
                                {
                                    TempData["StatusMessage"] = string.Format("您針對{0}已投票完成!!", model.Title.QUESTIONNAIRE_NAME);
                                }
                            }
                            else
                            {
                                TempData["StatusMessage"] = string.Format("您針對{0}已投票完成!!", model.Title.QUESTIONNAIRE_NAME);
                            }

                            ADDI10IndexViewModel QModel = new ADDI10IndexViewModel()
                            {
                                Search = model.Search
                            };
                            return View("Index", QModel);
                        }
                        else
                        {
                            TempData["StatusMessage"] = string.Format("您針對{0}已投票完成，獲得酷幣點數{1}點!!", model.Title.QUESTIONNAIRE_NAME, model.Title.CASH);

                            ModelState.Clear();

                            ADDI10EditViewModel QModel = new ADDI10EditViewModel()
                            {
                                StudentCardVote = true,
                                Search = model.Search,
                            };

                            QModel = Service.GetVoteDetailsPreview(QModel, ref db);

                            return View("Edit", QModel);
                        }
                    }
                }
            }

            TempData["StatusMessage"] = Message;

            this.SetTitle(Bre_Name + "-" + model.Title.QUESTIONNAIRE_NAME);

#if DEBUG
            var errorsaa = ModelState
                        .Where(x => x.Value.Errors.Count > 0)
                        .Select(x => new { x.Key, x.Value.Errors })
                        .ToArray();
#endif

            return View("Edit", model);
        }

        public JsonResult GetVoteDetails(ADDI10EditViewModel model)
        {
            string Message = "";
            string Success = string.Empty;
            model = Service.GetVoteDetailsPreview(model, ref db);
            foreach (var item in model.Topic)
            {
                var D_item = item.Topic_D.FirstOrDefault();

                if (Convert.ToBoolean(item.Q_MUST))
                {
                    var temp = "";
                    if (item.Topic_D != null) {

                        temp = item.Topic_D.Select(x => x.Q_INPUT_TYPE).FirstOrDefault();
                    }

                    
                    if (string.IsNullOrWhiteSpace(item.ANSWER) && temp != "UploadImage" && temp != "UploadWorld" && temp != "UploadPdf")
                    {
                        ModelState.AddModelError(string.Format("Topic[{0}].ANSWER", item.Index), SAQT03.Validate.requiredToStr(D_item.Q_REQUIRED_MSG));
                    }
                    //else
                    //{
                       
                    //    if (item.ANSWER == "System.Web.HttpPostedFileWrapper" || temp == "UploadImage" || temp == "UploadWorld" || temp == "UploadPdf")
                    //    {
                    //        var uploadFile = Request.Files[$"Topic[{item.Index-1}].ANSWER"];

                    //        if (uploadFile.ContentLength == 0)
                    //        {
                    //            ModelState.AddModelError(string.Format("Topic[{0}].ANSWER", item.Index), SAQT03.Validate.requiredToStr(D_item.Q_REQUIRED_MSG));
                    //        }
                    //    }
                    //}

                    if (item.Q_MUTIPLE_CHOICES_OF_NUM != null)
                    {
                        var ANSWER = (item.ANSWER ?? "").Split(',');

                        if (ANSWER.Length != item.Q_MUTIPLE_CHOICES_OF_NUM)
                        {
                            ModelState.AddModelError(string.Format("Topic[{0}].ANSWER", item.Index), $"*此題必需勾選{item.Q_MUTIPLE_CHOICES_OF_NUM}筆");
                        }
                    }
                }
            }
            TempData["StatusMessage"] = Message;

            this.SetTitle(Bre_Name + "-" + model.Title.QUESTIONNAIRE_NAME);

            //#if DEBUG
            var errorsaa = ModelState
                        .Where(x => x.Value.Errors.Count > 0)
                        .Select(x => new { x.Value.Errors })
                        .ToArray();
            //#endif
            var data = "{ \"Success\" : \"" + Success + "\" , \"Error\" : \"\" }";
            if (errorsaa.Count() > 0)
            {
                Success = false.ToString();
                foreach (var item in errorsaa)
                {
                    data = "{ \"Success\" : \"" + Success + "\" , \"Error\" : \"" + item.Errors.Select(x => x.ErrorMessage).FirstOrDefault() + "\" }";
                    return Json(data, JsonRequestBehavior.AllowGet);
                }
            }
            else
            {
                Success = true.ToString();
            }
            return Json(data, JsonRequestBehavior.AllowGet);
        }
        private string SysPath = System.Web.Configuration.WebConfigurationManager.AppSettings["UploadImageRoot"] + @"\\Vote\\";
        public ActionResult DownLoadJPG(string SCHOOL_NO, string QUESTIONNAIRE_ID, string ANSWERID, string ANSWER)
        {
            string tempPath = System.Web.HttpContext.Current.Request.MapPath(SysPath)+ SCHOOL_NO + "\\" + QUESTIONNAIRE_ID + "\\"+ ANSWERID+"\\"+ ANSWER;

            if (System.IO.File.Exists(tempPath))
            {
                return File(tempPath, "text/plain", Server.HtmlEncode(ANSWER));
            }
            else
            {
                return Content("No file name provided");
            }
        }

       
      //  [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult StatisticsVote(ADDI10StatisticsVoteViewModel model, string QUESTIONNAIRE_ID)
        {
            try
            {
                string SchoolNO = UserProfileHelper.GetSchoolNo();
                ViewBag.GradeItem = HRMT01.GetGradeItems(model.whereGrade);
                if (model.Search == null)
                {

                    model.Search = new ADDI10SearchViewModel();
                }
                ViewBag.ClassItems = HRMT01.GetClassListData(SchoolNO, model.whereGrade, model.whereCLASS_NO, ref db)
                    .Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.whereGrade });
                this.Shared(Bre_Name + " - 投票結果");
                if (!string.IsNullOrWhiteSpace(QUESTIONNAIRE_ID))
                {
                   
                    model.Search.WhereQUESTIONNAIRE_ID = QUESTIONNAIRE_ID;
                }
                    if (string.IsNullOrWhiteSpace(model.Search.WhereQUESTIONNAIRE_ID))
                {
                    return RedirectToAction(ErrorHelper.ErrorVal.NotParameterError, "Error");
                }

                model = Service.GetStatisticsVote(model, ref db, model.whereCLASS_NO);
                

                if (HRMT24_ENUM.CheckQAdmin(user) == false)
                {
                    if ((model.Title.RESULT ?? false) || model.Title.CRE_PERSON == user?.USER_KEY)
                    {
                        return View(model);
                    }
                    else
                    {
                        return RedirectToAction(ErrorHelper.ErrorVal.PermissionError, "Error");
                    }
                }
            }
            catch (Exception ex)
            {
                log4net.ILog logger = LogManager.GetLogger(typeof(MvcApplication));
                logger.Error(ex.ToString());
            }

            return View(model);
        }
        [HttpPost]
        public ActionResult ExportTOExcelADDI10(ADDISAQTModel postdata)
        {
            List<ADDISAQTModel> model = new List<ADDISAQTModel>();
            List<SAQT02> SAQT02Items = new List<SAQT02>();
            ADDI10Service dDI10Service = new ADDI10Service();
            SAQT02Items = db.SAQT02.Where(x => x.QUESTIONNAIRE_ID == postdata.QUESTIONNAIRE_ID).ToList();
            model = dDI10Service.GetSAQTModel(model, postdata.QUESTIONNAIRE_ID, ref db);
           string filepath= dDI10Service.TOExcelADDI10ALL(model, SAQT02Items);
            return Json(new { filename = filepath });
        }
        [HttpPost]
        public ActionResult ExportTOExcelADDI10ALL(ADDISAQTModel postdata)
        {
            List<ADDISAQTModel> model = new List<ADDISAQTModel>();
            List<SAQT02> SAQT02Items = new List<SAQT02>();
            ADDI10Service dDI10Service = new ADDI10Service();
            SAQT02Items = db.SAQT02.Where(x => x.QUESTIONNAIRE_ID == postdata.QUESTIONNAIRE_ID).ToList();
            model = dDI10Service.GetSAQTModel(model, postdata.QUESTIONNAIRE_ID, ref db);
            string filepath = dDI10Service.TOExcelADDI10ALL(model, SAQT02Items);
            return Json(new { filename = filepath });
        }
        [HttpPost]
        public ActionResult ExportTOExcelADDI102(ADDISAQTModel postdata)
        {
            List<ADDISAQTModel> model = new List<ADDISAQTModel>();
            List<SAQT02> SAQT02Items = new List<SAQT02>();
            ADDI10Service dDI10Service = new ADDI10Service();
            SAQT02Items = db.SAQT02.Where(x => x.QUESTIONNAIRE_ID == postdata.QUESTIONNAIRE_ID).ToList();
            model = dDI10Service.GetSAQTModel(model, postdata.QUESTIONNAIRE_ID, ref db);
            string filepath = dDI10Service.TOExcelADDI10ALL(model, SAQT02Items);
            return Json(new { filename = filepath });
        }
        [HttpGet]

        public ActionResult DownloadZIP(string file)
        {
            //到伺服器臨時檔案目錄下載相應的檔案
            string fullPath = file;
            string dt = new DateTime().ToShortDateString();
            //返回檔案物件，這裡用的是Excel，所以檔案頭使用了 "application/vnd.ms-excel"
            dt=DateTime.Now.ToShortDateString();
            return File(System.IO.File.ReadAllBytes(file), "application/zip", dt+"投票問卷.zip");//輸出檔案給Client端
            //return File(fullPath, "application/vnd.ms-excel", file);
        }
        [HttpGet]

        public ActionResult Download(string file)
        {
            //到伺服器臨時檔案目錄下載相應的檔案
            string fullPath = file;
           
            //返回檔案物件，這裡用的是Excel，所以檔案頭使用了 "application/vnd.ms-excel"
          
            return File(System.IO.File.ReadAllBytes(file), "application/vnd.ms-excel", $"投票總表.xlsx");//輸出檔案給Client端
            //return File(fullPath, "application/vnd.ms-excel", file);
        }
        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult ExportStatisticsVoteExcel(string QUESTIONNAIRE_ID)
        {
            this.Shared();

            var Temp = Service.GetExportStatisticsVoteExcel(QUESTIONNAIRE_ID, ref db);

            DataTable DataTableExcel = Temp.AsDataTable();

            NPOIHelper npoi = new NPOIHelper();
            string TempleteFileFullName = Server.MapPath(@"~/Content/ExcelSample/StatisticsVoteExcel.xlsx");

            FileStream xlsfile = new FileStream(TempleteFileFullName, FileMode.Open, FileAccess.Read);
            IWorkbook aBook = WorkbookFactory.Create(xlsfile);

            npoi.ExportExcelFromTemplete(DataTableExcel, aBook, "data", false, 2);

            string strTMPFile = System.Web.Configuration.WebConfigurationManager.AppSettings["FilePath"] + @"\TMP\" + DateTime.Now.Date.ToString("yyyyMMdd");

            if (Directory.Exists(strTMPFile) == false)
            {
                Directory.CreateDirectory(strTMPFile);
            }

            strTMPFile = strTMPFile + @"\StatisticsVoteExcel_" + Session.SessionID + ".xlsx";

            System.IO.FileStream FS = new FileStream(strTMPFile, FileMode.Create);
            aBook.Write(FS);
            FS.Close();

            return File(strTMPFile, "application/vnd.ms-excel", "StatisticsVoteExcel.xlsx");//輸出檔案給Client端
        }

        public ActionResult StatisticsVotePeople(string QUESTIONNAIRE_ID, string OrdercColumn, string SyntaxName, string whereGrade, string whereCLASS_NO, string whereSearch)
        {
            this.Shared(Bre_Name + " - 投票人員清單");
            if (user == null)
            {
                return RedirectToAction(ErrorHelper.ErrorVal.PermissionError, "Error");
            }

            var model = Service.GetALLVotePeople(QUESTIONNAIRE_ID, OrdercColumn, SyntaxName, whereGrade, whereCLASS_NO, whereSearch, ref db);
            if (!string.IsNullOrWhiteSpace(whereCLASS_NO))
            {
                model.whereCLASS_NO = whereCLASS_NO;
            }
            if (!string.IsNullOrWhiteSpace(whereGrade))
            {
                model.whereGrade = whereGrade;
            }
            if (model.Title == null || model.Topic == null)
            {
                return RedirectToAction(ErrorHelper.ErrorVal.NotFindError, "Error");
            }

            if (Convert.ToBoolean(model.Title.REGISTERED_BALLOT ?? false) == false)
            {
                if (user.USER_KEY != model.Title.CRE_PERSON && HRMT24_ENUM.CheckQAdmin(user) == false)
                {
                    return RedirectToAction(ErrorHelper.ErrorVal.PermissionError, "Error", new { error = "不記名投票" });
                }
            }
            ViewBag.GradeItem = HRMT01.GetGradeItems(model.whereGrade);

            ViewBag.ClassItems = HRMT01.GetClassListData(user.SCHOOL_NO, model.whereGrade, model.whereCLASS_NO, ref db)
                .Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.whereGrade });
            return PartialView(model);
        }
        [HttpPost]
        public ActionResult DownloadFIMG1(ADDISAQTModel postdata)

        {
            this.Shared();
            ADDI10StatisticsVoteDetailsViewModel model = new ADDI10StatisticsVoteDetailsViewModel();
            if (postdata.Q_NUM == 0)
            {
                List<int> Q_NUMSstring = new List<int>();
                Q_NUMSstring = db.SAQT03.Where(x => x.QUESTIONNAIRE_ID == postdata.QUESTIONNAIRE_ID && (x.Q_INPUT_TYPE == SAQT03.Q_INPUT_TYPEVal.UploadWorld || x.Q_INPUT_TYPE == SAQT03.Q_INPUT_TYPEVal.UploadImage || x.Q_INPUT_TYPE == SAQT03.Q_INPUT_TYPEVal.UploadPdf)).Select(x => x.Q_NUM).ToList();

                List<ADDI10VotePeopleViewModel> votePeopleViewModelsItem = new List<ADDI10VotePeopleViewModel>();

                foreach (var item in Q_NUMSstring)
                {

                    ADDI10StatisticsVoteDetailsViewModel votePeoplemodelTemp = new ADDI10StatisticsVoteDetailsViewModel();
                    votePeoplemodelTemp = Service.GetVotePeople(postdata.QUESTIONNAIRE_ID, item, null, null, null, null, null, null, ref db);
                    votePeopleViewModelsItem.AddRange(votePeoplemodelTemp.VotePeople);
                }
                model.GetSAQT03 = new SAQT03();
                model.GetSAQT03.Q_INPUT_TYPE = "UploadImage";
                model.VotePeople = votePeopleViewModelsItem;
            }
            else
            {
                model = Service.GetVotePeople(postdata.QUESTIONNAIRE_ID, postdata.Q_NUM, null, null, null, null, null, null, ref db);

            }


            ADDI10Service aDDI10Ser = new ADDI10Service();
            string DateTimeNowstring = "";
            DateTimeNowstring = DateTime.Now.ToString("yyyymmddhhmmss");
            string TempPath1 = aDDI10Ser.GetVirtalSysVotePath(SCHOOL_NO, postdata.QUESTIONNAIRE_ID, DateTimeNowstring);
            string TempPath2 = aDDI10Ser.GetSysVotePath(user?.SCHOOL_NO, DateTimeNowstring, "");

            if (Directory.Exists(TempPath2))
            {
                //資料夾存在
            }
            else
            {
                //新增資料夾
                Directory.CreateDirectory(TempPath2);
            }
            string tempPath3temp = aDDI10Ser.GetSysVotePath(SCHOOL_NO, postdata.QUESTIONNAIRE_ID, "temp") + @"\" + DateTimeNowstring;
            string tempPath3tempVirtual = aDDI10Ser.GetSysVirtauiVotePath(SCHOOL_NO, postdata.QUESTIONNAIRE_ID, "temp") + DateTimeNowstring;

            if (Directory.Exists(tempPath3temp))
            {
                //資料夾存在
                Directory.Delete(tempPath3temp, true);


            }

            List<string> ANSWER_IDlistString = new List<string>();
            int VotePeopleCount = 0;

            if (postdata.Q_NUM == 0)
            {
                ANSWER_IDlistString = db.SAQT04.Where(x => x.QUESTIONNAIRE_ID == postdata.QUESTIONNAIRE_ID).Select(x => x.ANSWER_ID).Distinct().ToList();

            }
            else
            {

                ANSWER_IDlistString = db.SAQT04.Where(x => x.Q_NUM == postdata.Q_NUM && x.QUESTIONNAIRE_ID == postdata.QUESTIONNAIRE_ID).Select(x => x.ANSWER_ID).Distinct().ToList();
            }
            VotePeopleCount = model.VotePeople.Where(x => ANSWER_IDlistString.Contains(x.ANSWER_ID)).Count();
            if (VotePeopleCount > 0)
            {
                foreach (var item in model.VotePeople.Where(x => ANSWER_IDlistString.Contains(x.ANSWER_ID)))
                {
                    //string ii = "";
                    string temp3 = tempPath3temp;
                    //Regex regexCode = new Regex("_M.");
                    //if (!string.IsNullOrWhiteSpace(item.ANSWER) && regexCode.IsMatch(item.ANSWER)) {
                    //    ii = item.ANSWER.Replace(".", "_M.");
                    //}
                   

                    if (model.GetSAQT03.Q_INPUT_TYPE == "UploadImage" || model.GetSAQT03.Q_INPUT_TYPE == "UploadWorld" || model.GetSAQT03.Q_INPUT_TYPE == "UploadPdf")
                    {


                        string tempPath = aDDI10Ser.GetSysVotePath(item.SCHOOL_NO, item.QUESTIONNAIRE_ID, item.ANSWER_ID);


                        //temp3 = temp3 + @"\" + item.SNAME + @"\";
                        if (Directory.Exists(tempPath))
                        {
                            if (postdata.Q_NUM != 0)
                            {
                                string SNAME = "";
                                List<string> aQT04s = new List<string>();
                                aQT04s = db.SAQT04.Where(x => x.ANSWER_ID == item.ANSWER_ID && x.Q_NUM == postdata.Q_NUM).Select(x => x.ANSWER).ToList();
                                if (Directory.Exists(temp3))
                                {
                                    if (!string.IsNullOrEmpty(item.CLASS_NO))
                                    {
                                        SNAME += item.CLASS_NO+"班_";
                                    }
                                    if (!string.IsNullOrEmpty(item.SEAT_NO))
                                    {
                                        SNAME += item.SEAT_NO+"號_";
                                    }
                                    SNAME += item.SNAME;
                                    aDDI10Ser.CopySingleFiles(tempPath, temp3, tempPath3tempVirtual, SNAME, model.GetSAQT03.Q_INPUT_TYPE, aQT04s);
                                }
                                else
                                {
                                    if (!string.IsNullOrEmpty(item.CLASS_NO))
                                    {
                                        SNAME += item.CLASS_NO + "班_";
                                    }
                                    if (!string.IsNullOrEmpty(item.SEAT_NO))
                                    {
                                        SNAME += item.SEAT_NO + "號_";
                                    }
                                    SNAME += item.SNAME;

                                    //新增資料夾
                                    Directory.CreateDirectory(temp3);
                                    aDDI10Ser.CopySingleFiles(tempPath, temp3, tempPath3tempVirtual, SNAME, model.GetSAQT03.Q_INPUT_TYPE, aQT04s);
                                }
                            }

                            else {

                            

                            if (Directory.Exists(temp3))
                            {
                                aDDI10Ser.CopyFiles(tempPath, temp3, tempPath3tempVirtual, item.SNAME, model.GetSAQT03.Q_INPUT_TYPE);
                            }
                            else
                            {
                                //新增資料夾
                                Directory.CreateDirectory(temp3);
                                aDDI10Ser.CopyFiles(tempPath, temp3, tempPath3tempVirtual,item.SNAME, model.GetSAQT03.Q_INPUT_TYPE);
                            }
                            }
                        }


                    }
                }
                string ZiipFile = "";
                ZiipFile = TempPath2 + DateTimeNowstring + ".zip";

                //if (!System.IO.File.Exists(ZiipFile))
                //{


                //    System.IO.File.Create(ZiipFile).Close();

                //}

                ZipFile.CreateFromDirectory(tempPath3temp, ZiipFile);
                if (Directory.Exists(tempPath3temp))
                {
                    //資料夾存在
                    Directory.Delete(tempPath3temp, true);


                }

                return Json(new { filename = ZiipFile });
            }
            else
            {

                return Json(new { filename = "無資料" });
            }
        }
        [HttpPost]
        public ActionResult DownloadFIMG(ADDISAQTModel postdata)

        {
            this.Shared();
            ADDI10StatisticsVoteDetailsViewModel model = new ADDI10StatisticsVoteDetailsViewModel();
            string UploadImageRoot = System.Web.Configuration.WebConfigurationManager.AppSettings["UploadImageRoot"];
            if (UploadImageRoot == null) UploadImageRoot = @"~\Content\";
            string RealUploadImageRoot = Request.MapPath(UploadImageRoot);
            if (postdata.Q_NUM == 0)
            {
                List<int> Q_NUMSstring = new List<int>();
                Q_NUMSstring = db.SAQT03.Where(x => x.QUESTIONNAIRE_ID == postdata.QUESTIONNAIRE_ID && (x.Q_INPUT_TYPE == SAQT03.Q_INPUT_TYPEVal.UploadWorld || x.Q_INPUT_TYPE == SAQT03.Q_INPUT_TYPEVal.UploadImage || x.Q_INPUT_TYPE == SAQT03.Q_INPUT_TYPEVal.UploadPdf)).Select(x => x.Q_NUM).ToList();

                List<ADDI10VotePeopleViewModel> votePeopleViewModelsItem = new List<ADDI10VotePeopleViewModel>();
                
                foreach (var item in Q_NUMSstring) {
          
                    ADDI10StatisticsVoteDetailsViewModel votePeoplemodelTemp = new ADDI10StatisticsVoteDetailsViewModel();
                    votePeoplemodelTemp = Service.GetVotePeople(postdata.QUESTIONNAIRE_ID, item, null, null, null, null, null, null, ref db);
                    votePeopleViewModelsItem .AddRange(votePeoplemodelTemp.VotePeople);
                }
                model.GetSAQT03 = new SAQT03();
                model.GetSAQT03.Q_INPUT_TYPE = "UploadImage";
                model.VotePeople = votePeopleViewModelsItem;
            }
            else {
             model = Service.GetVotePeople(postdata.QUESTIONNAIRE_ID, postdata.Q_NUM, null, null, null, null, null, null, ref db);

            }
               
           
            ADDI10Service aDDI10Ser = new ADDI10Service();
            string DateTimeNowstring = "";
            DateTimeNowstring = DateTime.Now.ToString("yyyymmddhhmmss");
            string TempPath1 = aDDI10Ser.GetVirtalSysVotePath(SCHOOL_NO, postdata.QUESTIONNAIRE_ID, DateTimeNowstring);
            string TempPath2 = aDDI10Ser.GetSysVotePath(user?.SCHOOL_NO, DateTimeNowstring, "");

            if (Directory.Exists(TempPath2))
            {
                //資料夾存在
            }
            else
            {
                //新增資料夾
                Directory.CreateDirectory(TempPath2);
            }
            string tempPath3tempVirtual = aDDI10Ser.GetSysVirtauiVotePath(SCHOOL_NO, postdata.QUESTIONNAIRE_ID, "temp") + @"\" + DateTimeNowstring;
            string tempPath3temp = aDDI10Ser.GetSysVotePath(SCHOOL_NO, postdata.QUESTIONNAIRE_ID, "temp") + @"\" + DateTimeNowstring;
            if (Directory.Exists(tempPath3temp))
            {
                //資料夾存在
                Directory.Delete(tempPath3temp,true);

               
            }
          
            List<string> ANSWER_IDlistString = new List<string>();
            int VotePeopleCount = 0;
          
            if (postdata.Q_NUM == 0)
            {
                ANSWER_IDlistString = db.SAQT04.Where(x => x.QUESTIONNAIRE_ID == postdata.QUESTIONNAIRE_ID).Select(x => x.ANSWER_ID).ToList();

            }
            else {

                ANSWER_IDlistString = db.SAQT04.Where(x => x.Q_NUM == postdata.Q_NUM && x.QUESTIONNAIRE_ID == postdata.QUESTIONNAIRE_ID).Select(x => x.ANSWER_ID).ToList();
            }
            VotePeopleCount = model.VotePeople.Where(x => ANSWER_IDlistString.Contains(x.ANSWER_ID)).Count();

   
            if (VotePeopleCount > 0)
            {
                foreach (var item in model.VotePeople.Where(x => ANSWER_IDlistString.Contains(x.ANSWER_ID)))
                {
                    string ii = "";
                    string temp3 = tempPath3temp;
                    ii = item.ANSWER.Replace(".", "_M.");
                 
                    if (model.GetSAQT03.Q_INPUT_TYPE == "UploadImage" || model.GetSAQT03.Q_INPUT_TYPE == "UploadWorld")
                    {


                        string tempPath = aDDI10Ser.GetSysVotePath(item.SCHOOL_NO, item.QUESTIONNAIRE_ID, item.ANSWER_ID);


                       // temp3 = temp3 + @"\" + item.SNAME;
                       // FileInfo file = new FileInfo(tempPath);
                        if (Directory.Exists(tempPath))
                        {




                            if (Directory.Exists(temp3))
                            {
                                
                                aDDI10Ser.CopyFiles(tempPath, temp3, tempPath3tempVirtual, "", model.GetSAQT03.Q_INPUT_TYPE);
                            }
                            else
                            {
                                //新增資料夾
                                Directory.CreateDirectory(temp3);
                                aDDI10Ser.CopyFiles(tempPath, temp3, tempPath3tempVirtual, "", model.GetSAQT03.Q_INPUT_TYPE);
                            }

                        }


                    }
                }
                string ZiipFile = "";
                ZiipFile = TempPath2 + DateTimeNowstring + ".zip";

                //if (!System.IO.File.Exists(ZiipFile))
                //{


                //    System.IO.File.Create(ZiipFile).Close();

                //}

                ZipFile.CreateFromDirectory(tempPath3temp, ZiipFile);
                if (Directory.Exists(tempPath3temp))
                {
                    //資料夾存在
                    Directory.Delete(tempPath3temp, true);


                }

                return Json(new { filename = ZiipFile });
            }
            else {

                return Json(new { filename ="無資料" });
            }
        }
        
        //讀取目錄下所有檔案
        private static ArrayList GetFiles(string path)
        {
            ArrayList files = new ArrayList();

            if (Directory.Exists(path))
            {
                files.AddRange(Directory.GetFiles(path, "*", SearchOption.AllDirectories));
            }

            return files;
        }
        [ValidateInput(false)]
        public ActionResult StatisticsVoteDetails(string QUESTIONNAIRE_ID, int Q_NUM, string ANSWER, string OrdercColumn, string SyntaxName, string whereGrade, string whereCLASS_NO, string whereSearch)
        {
            this.Shared(Bre_Name + " - 投票結果清單");

            if (user == null)
            {
                return RedirectToAction(ErrorHelper.ErrorVal.PermissionError, "Error");
            }
            
            var model = Service.GetVotePeople(QUESTIONNAIRE_ID, Q_NUM, ANSWER, OrdercColumn, SyntaxName, whereGrade, whereCLASS_NO, whereSearch, ref db);
            if (!string.IsNullOrWhiteSpace(whereCLASS_NO))
            {
                model.whereCLASS_NO = whereCLASS_NO;
            }
            if (!string.IsNullOrWhiteSpace(whereGrade))
            {
                model.whereGrade = whereGrade;
            }
            if (model.Title == null || model.Topic == null)
            {
                return RedirectToAction(ErrorHelper.ErrorVal.NotFindError, "Error");
            }

            if (Convert.ToBoolean(model.Title.REGISTERED_BALLOT ?? false) == false)
            {
                if (user.USER_KEY != model.Title.CRE_PERSON && HRMT24_ENUM.CheckQAdmin(user) == false)
                {
                    return RedirectToAction(ErrorHelper.ErrorVal.PermissionError, "Error", new { error = "不記名投票" });
                }
            }

            ViewBag.GradeItem = HRMT01.GetGradeItems(model.whereGrade);
            ViewBag.GradeItem1 = HRMT01.GetGradeItems(model.whereGrade);
            ViewBag.ClassItems = HRMT01.GetClassListData(user.SCHOOL_NO, model.whereGrade, model.whereCLASS_NO, ref db)
                .Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.whereGrade });
            ViewBag.ClassItems1 = HRMT01.GetClassListData(user.SCHOOL_NO, model.whereGrade1, model.whereCLASS_NO1, ref db)
                .Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.whereGrade1 });

            return PartialView(model);
        }

        #region Shared

        private void SetTitle(string Panel_Title = "")
        {
            if (!string.IsNullOrWhiteSpace(Panel_Title))
            {
                ViewBag.Panel_Title = Panel_Title;
            }
            else
            {
                ViewBag.Panel_Title = Bre_Name + " - 列表 ";
            }
        }

        private void Shared(string Panel_Title = "")
        {
            ViewBag.BRE_NO = Bre_NO;

            if (!string.IsNullOrWhiteSpace(Panel_Title))
            {
                ViewBag.Panel_Title = Panel_Title;
            }
            else
            {
                ViewBag.Panel_Title = Bre_Name + " - 列表 ";
            }

            user = UserProfileHelper.Get();
            SCHOOL_NO = UserProfileHelper.GetSchoolNo();

            if (user != null)
            {
                if (user.SCHOOL_NO != null)
                {
                    SCHOOL_NO = user.SCHOOL_NO;
                    USER_NO = user.USER_NO;
                }
            }
        }

        #endregion Shared

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}