﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class GameStatisticsAnsViewModel
    {
        /// <summary>
        /// 查詢
        /// </summary>
        public GameSearchViewModel Search { get; set; }

        /// <summary>
        /// 活動資訊
        /// </summary>
        public ADDT26 GameInfo { get; set; }

        /// <summary>
        /// 全部回答人次
        /// </summary>
        public double? AnsTotalNum { get; set; }

        /// <summary>
        /// 答對人次
        /// </summary>
        public double? AnsTrueNum { get; set; }

        /// <summary>
        /// 答對占比
        /// </summary>
        public double? AnsTruerRate { get; set; }

        /// <summary>
        /// 未全部答對人次
        /// </summary>
        public double? AnsFalseNum { get; set; }

        /// <summary>
        /// 未全部占比
        /// </summary>
        public double? AnsFalseNRate { get; set; }

        /// <summary>
        /// 各題目佔比
        /// </summary>
        public List<GameStatisticsAnsRateViewModel> Rate { get; set; }
    }
}