﻿@model EcoolWeb.ViewModels.ADDTListViewModel
@{
    ViewBag.Title = "獎狀處理清單";
    int i = 0;

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    if (string.IsNullOrWhiteSpace(UUID) == false)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
}

<style>
    .table thead > tr > th, .table tbody > tr > th, .table tfoot > tr > th, .table thead > tr > td, .table tbody > tr > td, .table tfoot > tr > td {
        border-top: none;
    }
</style>
<script type="text/javascript">
    function exportExcel() {

        var sHtml = htmlEncode($("#OutList")[0].outerHTML);//做html編碼

        $("input[name='hHtml']").val(sHtml);

        //表單提交
        document.ADDT.enctype = "multipart/form-data";
        document.ADDT.action = "ExportExcel";
        document.ADDT.submit();
        //$("form[name='AWA004']").submit();
    }

    function htmlEncode(value) {
        //create a in-memory div, set it's inner text(which jQuery automatically encodes)
        //then grab the encoded contents back out.  The div never exists on the page.
        return $('<div/>').text(value).html();
    }
</script>
@using (Html.BeginForm("HandleBookCreditList", "ADDT", FormMethod.Post, new { name = "ADDT", id = "ADDT" }))
{
    @section scripts{
        <script>

            function btnSend_onclick() {
                document.ADDT.enctype = "multipart/form-data";
                document.ADDT.action = "HandleBookCreditEDIT";
                document.ADDT.submit();
            }

            function btnSearch_onclick() {
                document.ADDT.enctype = "multipart/form-data";
                document.ADDT.action = "HandleBookCreditList";
                document.ADDT.submit();
            }

            $("#chkALL").click(function () {

                if ($("#chkALL").prop("checked")) {
                    $("input:checkbox").each(function () {
                        $(this).prop("checked", true);
                    });
                }
                else {
                    $("input:checkbox").each(function () {
                        $(this).prop("checked", false);
                    });
                }
            });

            var targetFormID = '#ADDT';
            $(function () {

                // Fields
                var _pageLinkers = $(".pager> a");

                // Binding click event
                _pageLinkers.each(function (i, item) {
                    var page = getParameterByName($(item).attr('href'), 'page')
                    $(item).attr('href', '#').click(function () { postPage(page); });
                });

            });

            function getParameterByName(url, name) {
                name = name.replace(/[\[]/, "\\[").replace(/[\]]/, "\\]");
                var regex = new RegExp("[\\?&]" + name + "=([^&#]*)"),
                    results = regex.exec(url);
                return results === null ? "" : decodeURIComponent(results[1].replace(/\+/g, " "));
            }

            function postPage(page) {
                if ($(targetFormID).size() > 0) {
                    $('<input>')
                        .attr({ type: 'hidden', id: 'page', name: 'page', value: page })
                        .appendTo($(targetFormID));
                    $(targetFormID).submit();
                }
            };

            function doSort(SortCol) {
                $("#OrdercColumn").val(SortCol);
                $(targetFormID).submit();
            }

            function doSearch(ColName, whereValue) {
                $("#" + ColName).val(whereValue);
                $(targetFormID).submit();
            }
        </script>

    }

    <div style=" position: relative;left:15px;">
        <br />
        <span>&nbsp;搜尋瀏覽請輸入相關字串[學號/姓名]:</span>
        @Html.EditorFor(m => m.whereKeyword, new { htmlAttributes = new { @style = "width:100px" } })
        @Html.HiddenFor(m => m.OrdercColumn)
        @Html.HiddenFor(m => m.whereCLASS_NO)
        <input id="btnSearch" name="btnSearch" onclick="btnSearch_onclick();" type="button" value="搜尋" />
    </div>

    <div class="pager">

        @Html.Pager(Model.ADDT0809List.PageSize, Model.ADDT0809List.PageNumber, Model.ADDT0809List.TotalItemCount)
        @*&nbsp;共&nbsp; @Model.ADDT0809List.TotalItemCount &nbsp;筆*@
        @Html.Hidden("hHtml")
    </div>

    <img src="~/Content/img/web-Bar-09.png" style="width:100%" class="img-responsive App_hide" alt="Responsive image"/>

    <table class="table" style="white-space: nowrap;" id="OutList">
            <tr class="ListColName">
                <th style="text-align: center;">
                    處理
                    <input type="checkbox" id="chkALL" name="chkALL" />
                </th>
                <th style="text-align: center;cursor:pointer;">
                    學年
                </th>
                <th style="text-align: center;cursor:pointer;">
                    學期
                </th>
                <th style="text-align: center;cursor:pointer;">
                    班級
                </th>
                <th style="text-align: center;cursor:pointer;">
                    座號
                </th>
                <th style="text-align: center;cursor:pointer;">
                    姓名
                </th>
                <th style="text-align: center;cursor:pointer;">
                    閱讀冊數
                </th>
                <th style="text-align: center;cursor:pointer;">
                    認證等級
                </th>
                <th style="text-align: center;cursor:pointer;">
                    升級日期
                </th>
                <th style="text-align: center;">
                    備註
                </th>
            </tr>

            @foreach (var item in Model.ADDT0809List)
            {
                <tr class="ListRow">
                    <td>
                        <input type="checkbox" id='[@i].chkUPNO' name='[@i].chkUPNO'>
                        <input type="hidden" id='[@i].UPNO' name='[@i].UPNO' value=@item.UPNO />
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.SYEAR)
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.SEMESTER)
                    </td>
                    <td style="text-align: center;cursor:pointer;" onclick="doSearch('whereCLASS_NO','@item.CLASS_NO');">
                        @Html.DisplayFor(modelItem => item.CLASS_NO)
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.SEAT_NO)
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.SNAME)
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.BOOK_QTY)
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.LEVEL_DESC)
                        @*@Html.ActionLink(item.LEVEL_DESC, "PrizeList", new { USER_NO = item.USER_NO }, new { @style = "color:blue;" })*@
                    </td>
                    <td style="text-align: left;white-space:normal">
                        @Html.DisplayFor(modelItem => item.UP_DATE, "ShortDateTime")
                    </td>
                    <td style="text-align: left;white-space:normal"></td>
                </tr>
                i++;
            }

        </table>

    <div class="col-md-2">
        <input type="button" id="btnSend" value="確定送出" class="btn-primary LinkButton" onclick="btnSend_onclick();" />
        <input type="button" id="btnExcel" value="匯出Excel" class="btn-primary LinkButton" onclick="exportExcel();" />
    </div>
}