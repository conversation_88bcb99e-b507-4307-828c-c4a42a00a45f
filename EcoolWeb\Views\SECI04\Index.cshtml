﻿@model ECOOL_APP.com.ecool.Models.SECI04IndexViewModel
@{
    ViewBag.Title = ViewBag.Panel_Title;

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);

    if (!Model.IsPrint)
    {
        if (AppMode)
        {
            Layout = "~/Views/Shared/_LayoutWebView.cshtml";
        }
        else
        {
            Layout = "~/Views/Shared/_LayoutSEO.cshtml";
        }
    }
    else
    {
        Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
    }


}

<style type="text/css" media="print">
    /* 列印時隱藏 */
    .cScreen {
        display: none;
    }
</style>

<link href='~/Content/css/EzCss.css' rel='stylesheet' />
<script src="~/Scripts/Highcharts-4.0.1/js/highcharts.js"></script>
<script src="~/Scripts/Highcharts-4.0.1/js/modules/exporting.js"></script>
<script src="~/Scripts/Blob.js"></script>
<script src="~/Scripts/FileSaver.js"></script>
<script src="~/Scripts/tableexport.js"></script>
<script type="text/javascript">
        var targetFormID = '#form1';


        function PrintBooK()
          {
            $(targetFormID).attr('action', '@Html.Raw(@Url.Action("Index", (string)ViewBag.BRE_NO))')
            $("#@Html.IdFor(m=>m.IsPrint)").val(true)
            $(targetFormID).attr('target', '_blank').submit().removeAttr('target');
        }

        function onSubmit()
        {
            $("#@Html.IdFor(m=>m.IsPrint)").val(false)
            $(targetFormID).submit()
        }

        window.onload = function () {
            if ( $("#@Html.IdFor(m=>m.IsPrint)").val()=="true") {
                window.print()
            }
        }


        function fn_save() {
            var blob = new Blob([document.getElementById('tbData').innerHTML], {
                type: "html/plain;charset=utf-8"
            });
            var strFile = "Report.html";
            saveAs(blob, strFile);
            return false;
        }
</script>
<br />

@if (!Model.IsPrint)
{
    @Html.Partial("_Title_Secondary")

    <div class="form-group">
        <a role="button" href='@Url.Action("Index",  (string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys active">
            我要看每一學年
        </a>

        <a role="button" href='@Url.Action("Index2",  (string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys">
            我要看每一年級 
        </a>

        <a role="button" href='@Url.Action("Index3",  (string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys">
            我要看一屆
        </a>
    </div>
}

    <div class="cScreen">
        @using (Html.BeginForm("Index", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", id = "form1" }))
        {
           @Html.HiddenFor(m => m.IsPrint)

            if (!Model.IsPrint)
            {
                <div class="form-inline" role="form">
                    <div class="form-group">
                        <label class="control-label">學校</label>
                    </div>
                    <div class="form-group">
                        @Html.DropDownListFor(m => m.whereSCHOOL_NO, (IEnumerable<SelectListItem>)ViewBag.SchoolNoSelectItem, new { @class = "form-control input-sm", @onchange = "form1.submit();" })
                    </div>
                    <label class="control-label">我要看</label>
                    <div class="form-group">
                        @Html.EditorFor(m => m.whereSYEAR, new { htmlAttributes = new { @class = "form-control input-sm", @style = "max-width:60px" } })
                    </div>
                    <label class="control-label">學年 的健康資料</label>
             
                    <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="onSubmit();" />

                </div>

                <br />
                <div class="form-inline">
                    <div class="col-xs-12 text-right">
                        <button type="button" class="btn btn-sm btn-sys" onclick="PrintBooK()">我要列印</button>
                    </div>
                </div>
            }

        }
    </div>

    <br />

@if (Model.IsPrint)
{
    <div class="Div-EZ-Right cScreen">
        <button class="btn btn-sm btn-sys" onclick='fn_save()'>另存新檔</button>
    </div>
}
 



<div id="tbData">
    @if (Model.TALLchart != null || Model.WEIGHTchart != null || Model.RIGHT_VisionChart != null || Model.LEFT_VisionChart != null)
    {
        <div class="panel table-responsive">
            <table class="table-ecool table-92Per table-hover table-ecool-ACC ">
                <caption><strong style="font-size:18px">@(Model.whereSYEAR)學年，各年級-身高狀況表</strong></caption>
                <thead>
                    <tr>
                        <th style="text-align: center;">
                            @Html.DisplayNameFor(model => model.Hrmt08List.First().GRADE_SEMESTER)
                        </th>
                        <th style="text-align: center">
                            @Html.DisplayNameFor(model => model.Hrmt08List.First().This_AVG_TALL_M)
                        </th>
                        <th style="text-align: center">
                            @Html.DisplayNameFor(model => model.Hrmt08List.First().This_AVG_TALL_W)
                        </th>
                        <th style="text-align: center">
                            @Html.DisplayNameFor(model => model.Hrmt08List.First().AVG_TALL_M)
                        </th>
                        <th style="text-align: center">
                            @Html.DisplayNameFor(model => model.Hrmt08List.First().AVG_TALL_W)
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var item in Model.Hrmt08List)
                {
                        <tr align="center">
                            <td>
                                @Html.Raw(HttpUtility.HtmlDecode(item.GRADE_SEMESTER))
                            </td>

                            <td>
                                @if (item.This_AVG_TALL_M == 0)
                                {
		                            <font color="red">
                                        @Html.DisplayFor(modelItem => item.This_AVG_TALL_M)
                                    </font>
                                }
                                else
                                {
                                    @Html.DisplayFor(modelItem => item.This_AVG_TALL_M)
                                }
                            </td>
                            <td>
                                @if (item.This_AVG_TALL_W == 0)
                                {
                                    <font color="red">
                                        @Html.DisplayFor(modelItem => item.This_AVG_TALL_W)
                                    </font>
                                }
                                else
                                {
                                    @Html.DisplayFor(modelItem => item.This_AVG_TALL_W)
                                }


                              
                            </td>
                            <td>
                                @if (item.AVG_TALL_M == 0)
                                {
                                    <font color="red">
                                        @Html.DisplayFor(modelItem => item.AVG_TALL_M)
                                    </font>
                                }
                                else
                                {
                                    @Html.DisplayFor(modelItem => item.AVG_TALL_M)
                                }


                              
                            </td>
                            <td>
                                @if (item.AVG_TALL_W == 0)
                                {
                                    <font color="red">
                                        @Html.DisplayFor(modelItem => item.AVG_TALL_W)
                                    </font>
                                }
                                else
                                {
                                    @Html.DisplayFor(modelItem => item.AVG_TALL_W)
                                }

                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
        <div class="col-sm-12  text-center">
            @if (Model.TALLchart != null)
            {
                @Model.TALLchart
                <br />
                <font style="font-size:90%;color:red">*可點選Y軸標題，可移除此圖比較</font>
            }
        </div>
        <div style="height:50px"></div>
        <div class="panel table-responsive">
            <table class="table-ecool table-92Per table-hover table-ecool-ACC ">
                <caption><strong style="font-size:18px">@(Model.whereSYEAR)學年，各年級-體重狀況表</strong></caption>
                <thead>
                    <tr>
                        <th style="text-align: center;">
                            @Html.DisplayNameFor(model => model.Hrmt08List.First().GRADE_SEMESTER)
                        </th>
                        <th style="text-align: center">
                            @Html.DisplayNameFor(model => model.Hrmt08List.First().This_AVG_WEIGHT_M)
                        </th>
                        <th style="text-align: center">
                            @Html.DisplayNameFor(model => model.Hrmt08List.First().This_AVG_WEIGHT_W)
                        </th>
                        <th style="text-align: center">
                            @Html.DisplayNameFor(model => model.Hrmt08List.First().AVG_WEIGHT_M)
                        </th>
                        <th style="text-align: center">
                            @Html.DisplayNameFor(model => model.Hrmt08List.First().AVG_WEIGHT_W)
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var item in Model.Hrmt08List)
                {
                        <tr align="center">
                            <td>
                                @Html.Raw(HttpUtility.HtmlDecode(item.GRADE_SEMESTER))
                            </td>

                            <td>
                                @if (item.This_AVG_WEIGHT_M == 0)
                                {
                                    <font color="red">
                                        @Html.DisplayFor(modelItem => item.This_AVG_WEIGHT_M)
                                    </font>
                                }
                                else
                                {
                                    @Html.DisplayFor(modelItem => item.This_AVG_WEIGHT_M)
                                }


                            
                            </td>
                            <td>
                                @if (item.This_AVG_WEIGHT_W == 0)
                                {
                                    <font color="red">
                                        @Html.DisplayFor(modelItem => item.This_AVG_WEIGHT_W)
                                    </font>
                                }
                                else
                                {
                                    @Html.DisplayFor(modelItem => item.This_AVG_WEIGHT_W)
                                }


                               
                            </td>
                            <td>
                                @if (item.AVG_WEIGHT_M == 0)
                                {
                                    <font color="red">
                                        @Html.DisplayFor(modelItem => item.AVG_WEIGHT_M)
                                    </font>
                                }
                                else
                                {
                                    @Html.DisplayFor(modelItem => item.AVG_WEIGHT_M)
                                }



                              
                            </td>
                            <td>

                                @if (item.AVG_WEIGHT_W == 0)
                                {
                                    <font color="red">
                                        @Html.DisplayFor(modelItem => item.AVG_WEIGHT_W)
                                    </font>
                                }
                                else
                                {
                                    @Html.DisplayFor(modelItem => item.AVG_WEIGHT_W)
                                }


                             
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
        <div class="col-sm-12  text-center">
            @if (Model.WEIGHTchart != null)
            {
                @Model.WEIGHTchart
                <br />
                <font style="font-size:90%;color:red">*可點選Y軸標題，可移除此圖比較</font>
            }
        </div>
        <div style="height:50px"></div>
        <div class="panel table-responsive">
            <table class="table-ecool table-92Per table-hover table-ecool-ACC ">
                <caption><strong style="font-size:18px">@(Model.whereSYEAR)學年，各年級-視力分布狀況表(右眼)</strong></caption>
                <thead>
                    <tr>
                        <th style="text-align: center;">
                            @Html.DisplayNameFor(model => model.VisionHrmt08List.First().GRADE_SEMESTER)
                        </th>
                        <th style="text-align: center">
                            @Html.DisplayNameFor(model => model.VisionHrmt08List.First().This_VisionRight_08)
                        </th>
                        <th style="text-align: center">
                            @Html.DisplayNameFor(model => model.VisionHrmt08List.First().This_VisionRight_09)
                        </th>
          
                        <th style="text-align: center">
                            @Html.DisplayNameFor(model => model.VisionHrmt08List.First().VisionRight_08)
                        </th>
                        <th style="text-align: center">
                            @Html.DisplayNameFor(model => model.VisionHrmt08List.First().VisionRight_09)
                        </th>
         
                    </tr>
                </thead>
                <tbody>
                    @foreach (var item in Model.VisionHrmt08List)
                {
                        <tr align="center">
                            <td>
                                @Html.Raw(HttpUtility.HtmlDecode(item.GRADE_SEMESTER))
                            </td>

                            <td>
                                @Html.DisplayFor(modelItem => item.This_VisionRight_08) <span>%</span>
                            </td>
                            <td>
                                @Html.DisplayFor(modelItem => item.This_VisionRight_09) <span>%</span>
                            </td>
                     
                            <td>
                                @Html.DisplayFor(modelItem => item.VisionRight_08) <span>%</span>
                            </td>
                            <td>
                                @Html.DisplayFor(modelItem => item.VisionRight_09) <span>%</span>
                            </td>
                   
                        </tr>
                    }
                </tbody>
            </table>
        </div>
        <div class="col-sm-12  text-center">
            @if (Model.RIGHT_VisionChart != null)
            {
                @Model.RIGHT_VisionChart
                <br />
                <font style="font-size:90%;color:red">*可點選Y軸各長條圖標題，可移除此長條圖比較</font>
            }

        </div>
        <div style="height:50px"></div>
        <div class="panel table-responsive">
            <table class="table-ecool table-92Per table-hover table-ecool-ACC ">
                <caption><strong style="font-size:18px">@(Model.whereSYEAR)學年，各年級-視力分布狀況表(左眼)</strong></caption>
                <thead>
                    <tr>
                        <th style="text-align: center;">
                            @Html.DisplayNameFor(model => model.VisionHrmt08List.First().GRADE_SEMESTER)
                        </th>
                        <th style="text-align: center">
                            @Html.DisplayNameFor(model => model.VisionHrmt08List.First().This_VisionLeft_08)
                        </th>
                        <th style="text-align: center">
                            @Html.DisplayNameFor(model => model.VisionHrmt08List.First().This_VisionLeft_09)
                        </th>
                     
                        <th style="text-align: center">
                            @Html.DisplayNameFor(model => model.VisionHrmt08List.First().VisionLeft_08)
                        </th>
                        <th style="text-align: center">
                            @Html.DisplayNameFor(model => model.VisionHrmt08List.First().VisionLeft_09)
                        </th>
                    
                    </tr>
                </thead>
                <tbody>
                    @foreach (var item in Model.VisionHrmt08List)
                {
                        <tr align="center">
                            <td>
                                @Html.Raw(HttpUtility.HtmlDecode(item.GRADE_SEMESTER))
                            </td>

                            <td>
                                @Html.DisplayFor(modelItem => item.This_VisionLeft_08) <span>%</span>
                            </td>
                            <td>
                                @Html.DisplayFor(modelItem => item.This_VisionLeft_09) <span>%</span>
                            </td>
                           
                            <td>
                                @Html.DisplayFor(modelItem => item.VisionLeft_08) <span>%</span>
                            </td>
                            <td>
                                @Html.DisplayFor(modelItem => item.VisionLeft_09) <span>%</span>
                            </td>
                          
                        </tr>
                    }
                </tbody>
            </table>
        </div>
        <div class="col-sm-12 text-center">
            @if (Model.LEFT_VisionChart != null)
            {
                @Model.LEFT_VisionChart
                <br />
                <font style="font-size:90%;color:red">*可點選Y軸各長條圖標題，可移除此長條圖比較</font>
            }
        </div>
    }
    else
    {
        <h3>此學年度查無資料</h3>
    }
</div>