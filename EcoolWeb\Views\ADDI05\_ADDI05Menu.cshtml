﻿@model int

@{
    int activeIDNumber = Model;
}

<div class="form-group">
    <a class="btn btn-sm btn-sys" role="button" onclick="onGo('Index')">
        回有獎徵答首頁
    </a>
    <a class="btn btn-sm btn-sys" role="button" onclick="onGo('detail')">
        回詳細活動內容
    </a>

    @*<a href="@Url.Action("Index","SECI05")" class="btn btn-sm btn-bold btn-pink @(activeIDNumber==0? "active":"")">學生閱讀狀況</a>*@

</div>