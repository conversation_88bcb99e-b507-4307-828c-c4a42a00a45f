﻿@model  ECOOL_APP.EF.ZZZI34EditViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;
}
<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>
@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")
<div id="Msg" class="alert alert-dismissible alert-danger" style="display:none">
    <img src="~/Content/img/Warning.png" style="width:30px;height:30px" />
    <button class="close" type="button" data-dismiss="alert">×</button>
    @Html.ValidationMessage("Error", "", new { @class = "text-danger" })
</div>

@*
        <div class="alert alert-dismissible alert-danger">
             <button class="close" type="button" data-dismiss="alert">×</button>
                <strong>
                        @if(ViewBag.SumCount != null) {
                         if (Model.Details_List.Count() == ViewBag.SumCount)
                         {
                             <samp> 批次代閱讀認證作業 - 全部成功</samp>
                         }
                         else if (ViewBag.SumCount == ViewBag.ErrCount)
                         {
                             <samp>批次代閱讀認證作業 - 全部失敗</samp>
                         }
                         else
                         {
                             <samp>批次代閱讀認證作業 - 有一部份失敗</samp>
                         }
                        }
                     </strong>
    </div>*@
@using (Html.BeginForm("UploadEdite", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", id = "form1", enctype = "multipart/form-data" }))
{
    @Html.AntiForgeryToken()
    @Html.HiddenFor(model => model.ZZZI34ImgType)
    <img src="~/Content/img/web-bar3-revise-21.png" style="width:100%" class="img-responsive " alt="Responsive image" />
    if (ViewBag.FailList != null)
    {
        <div class="Div-EZ-reader">
            <div class="Details">
                <div class="table-responsive">
                    <div class="text-center">
                        <table class="table-ecool table-92Per table-hover">
                            <caption class="Caption_Div_Left">
                                失敗清單：
                            </caption>
                            <thead>
                                <tr>
                                    <th>檔名</th>
                                    <th>訊息</th>
                                </tr>
                            </thead>
                            <tbody>

                                @foreach (var item in (Dictionary<string, string>)ViewBag.FailList)
                                {
                                    if (item.Value != string.Empty && item.Value != null)
                                    {
                                        <tr>
                                            <td>@item.Key</td>
                                            <td>@item.Value</td>
                                        </tr>
                                    }
                                }
                            </tbody>
                        </table>
                        <div style="height:15px"></div>
                        <div class="btn-group btn-group-justified" role="group">
                            共 @ViewBag.ErrCount 人
                        </div>
                    </div>
                </div>
            </div>
        </div> }
    <div class="Div-EZ-ZZZI26">
        <div class="form-horizontal">
            <label class="control-label">@ViewBag.Panel_Title</label>

            <br /><br />
            <div class="table-responsive">
                <table class="table-ecool table-hover">
                    <tr style="background-color:#FFE2E7;">
                        <td colspan="3">批次給分的貼心工具</td>
                        <td>                            @Html.Editor("CASH", new { htmlAttributes = new { @class = "form-control input-sm", @placeholder = "0~10" } })</td>
                        <td colspan="5">                                <button class="btn2 btn-default btn-sm" type="button" onclick="AutoCash($('#CASH').val())">全部自動帶入這個酷幣值</button></td>
                    </tr>
                    <tr style="background-color:#D9F6FF;">
                        <td colspan="3">批次給主題的貼心工具</td>
                        <td>                            @Html.Editor("SUBJECT", new { htmlAttributes = new { @class = "form-control input-sm" } })</td>
                        <td colspan="5">                                <button class="btn2 btn-default btn-sm" type="button" onclick="AutoCashSubject($('#SUBJECT').val())">全部自動帶入這個主題</button></td>
                    </tr>
                </table>
                @Html.ValidationMessage("DetailsError", new { @class = "text-danger" })
                <table class="table-ecool table-hover">

                    <thead>

                        <tr class="text-center">
                            <th>
                                序
                            </th>
                            <th>
                                刪
                            </th>
                            <th>
                                姓名 / 座號
                            </th>
                            @*<th>
                                    班級
                                </th>*@
                            <th>
                                <font color="red">*</font>酷幣
                            </th>
                            <th>
                                <font color="red">*</font>主題
                            </th>
                            <th>
                                內容
                            </th>
                            @if (ViewBag.VerifyUseYN == "Y")
                            {
                                <th>
                                    上傳
                                </th>
                            }
                        </tr>
                    </thead>
                    <tbody>
                        @{
                            int Num = 0;
                            Model.Main = new ZZZI34EditMainViewModel();

                            foreach (var Data in Model.Details_List.OrderBy(x=>x.filenames).ToList())
                            {
                                Model.Main.ART_GALLERY_NO = Data.ART_GALLERY_NO;
                                Model.Main.ART_GALLERY_TYPE = Data.ART_GALLERY_TYPE;
                                Model.Main.ART_SUBJECT = Data.ART_SUBJECT;
                                Model.Main.WORK_TYPE = Data.WORK_TYPE;
                                Model.Main.ART_DESC = Data.ART_DESC;
                                Model.Main.COVER_FILE = Data.COVER_FILE;
                                Model.Main.SCHOOL_NO = Data.SCHOOL_NO;

                                <tr>
                                    <td class="text-center">
                                        @(Num + 1)
                                    </td>
                                    <td class="text-center">
                                        <input id="Details_List_Del_@Num" name="Details_List[@Num].Del" type="checkbox" value="true" @(Data.Del ? "checked=\"checked\"" : "") />
                                    </td>
                                    <td>
                                        <select class="form-control input-sm" name="Details_List[@Num].USER_NO" style="min-width:120px;">
                                            @{
                                                string SelectedVal = string.Empty;

                                                if (ViewBag.USER_NOItems != null)
                                                {
                                                    foreach (var item in ViewBag.USER_NOItems as IEnumerable<SelectListItem>)
                                                    {
                                                        SelectedVal = Data.USER_NO == item.Value ? "selected" : "";
                                                        <option value="@item.Value" @SelectedVal>@item.Text</option>
                                                    }
                                                }
                                            }
                                        </select>
                                        @Html.ValidationMessageFor(model => model.Details_List[Num].USER_NO, "", new { @class = "text-danger" })
                                        @Html.HiddenFor(model => model.Details_List[Num].SCHOOL_NO)
                                        @Html.HiddenFor(model => model.Details_List[Num].CLASS_NO)
                                        @Html.HiddenFor(model => model.Details_List[Num].SIMG_PATH)
                                        @Html.HiddenFor(model => model.Details_List[Num].MIMG_PATH)
                                        @Html.HiddenFor(model => model.Details_List[Num].filenames)
                                    </td>
                                    @*<td>
                                            @Html.EditorFor(model => model.Details_List[Num].CLASS_NO, new { htmlAttributes = new { @class = "form-control  input-sm", @style = "min-width:80px;max-width:97pt" } })
                                            @Html.ValidationMessageFor(model => model.Details_List[Num].CLASS_NO, "", new { @class = "text-danger" })
                                        </td>*@

                                    <td>
                                        @Html.EditorFor(model => model.Details_List[Num].PHOTO_CASH, new { htmlAttributes = new { @class = "form-control CASH  input-sm", @style = "min-width:80px;max-width:97pt" } })
                                        @Html.ValidationMessageFor(model => model.Details_List[Num].PHOTO_CASH, "", new { @class = "text-danger" })
                                    </td>
                                    <td>
                                        @Html.EditorFor(model => model.Details_List[Num].PHOTO_SUBJECT, new { htmlAttributes = new { @class = "form-control SUBJECT input-sm", @style = "min-width:80px;max-width:97pt" } })
                                        @Html.ValidationMessageFor(model => model.Details_List[Num].PHOTO_SUBJECT, "", new { @class = "text-danger" })
                                    </td>
                                    <td>
                                        @Html.EditorFor(model => model.Details_List[Num].PHOTO_DESC, new { htmlAttributes = new { @class = "form-control  input-sm", @style = "min-width:80px;max-width:97pt" } })
                                        @Html.ValidationMessageFor(model => model.Details_List[Num].PHOTO_DESC, "", new { @class = "text-danger" })
                                    </td>
                                    @if (string.IsNullOrWhiteSpace(Model.Search.TEMP_BATCH_KEY) == false)
                                    {
                                        <td>
                                            <img class="img-responsive" src="@Url.Content(Data.IMG_FILE)" href="@Url.Content(Data.IMG_FILE)" style="max-height:150pt" oncontextmenu="javascript: window.open('@Url.Content(Data.IMG_FILE)')" />
                                            @Html.HiddenFor(model => model.Details_List[Num].IMG_FILE)
                                        </td>
                                    }
                                    else
                                    {
                                        if (ViewBag.VerifyUseYN == "Y")
                                        {
                                            <td>
                                                <input class="form-control input-sm" type="file" name="files" id="files_@Num" value="瀏覽" style="min-width:100px" />
                                                @Html.ValidationMessageFor(model => model.Details_List[Num].files, "", new { @class = "text-danger" })
                                            </td>
                                        }
                                    }
                                    <td class="text-center">
                                        @*<input name="Details_List[@Num].SHARE_YN" type="checkbox" value="Y" @(Data.SHARE_YN == "Y" ? "checked=\"checked\"" : "") />*@
                                    </td>
                                </tr>
                                Num++;
                            }
                        }
                    </tbody>
                </table>
            </div>
            <div>
                <div>

                    @Html.HiddenFor(model => model.Search.TEMP_BATCH_KEY)
                    @Html.HiddenFor(model => model.ModeValue)
                    @Html.HiddenFor(model => model.Main.SCHOOL_NO)

                    @Html.HiddenFor(model => model.Search.NumType)
                    @Html.HiddenFor(model => model.Search.WhereCLASS_NO)
                    @Html.HiddenFor(model => model.Main.ART_GALLERY_NO)
                    @Html.HiddenFor(model => model.Main.ART_GALLERY_TYPE)
                    @Html.HiddenFor(model => model.Main.ART_SUBJECT)
                    @Html.HiddenFor(model => model.Main.ART_DESC)
                    @Html.HiddenFor(model => model.Main.WORK_TYPE)
                    @Html.HiddenFor(model => model.Main.COVER_FILE)

                    @Html.Hidden("TOLTAL", TempData["TOLTAL"])
                    @Html.Hidden("DATA_TYPE")
                    @Html.Hidden("Num", Num)
                    @Html.Hidden("VerifyUseYN", (string)ViewBag.VerifyUseYN)

                    @Html.ValidationMessage("additem", new { @class = "text-danger" })
                    @if (string.IsNullOrWhiteSpace(Model.Search.TEMP_BATCH_KEY))
                    {
                        <div class="col-md-6 col-xs-8">
                            <div class="input-group">
                                @Html.TextBox("ADDNUM", TempData["ADDNUM"], new { @class = "form-control", @placeholder = "請填筆數", onchange = "if (isNaN(this.value) || this.value<=0) {alert('請填數字!');this.value=''};" })
                                <span class="input-group-btn">
                                    <input type=button class="btn btn-default" name=additem value="增加筆數" onclick="AddItem()">
                                </span>
                            </div><!-- /input-group -->
                        </div>
                        <div class="col-md-6 col-xs-4">
                            <input type="button" value="存檔" class="btn btn-default" onclick="onSave('Save')" id="BtnSave" />
                        </div>
                    }
                    else
                    {
                        <div class="col-md-12 col-xs-12 text-center">
                            <input type="button" value="存檔" class="btn btn-default" onclick="onSave('Save')" id="BtnSave" />
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>

    <div style="height:20px"></div>
}
<div class="text-center">
    <input type="button" value="回選擇模式" class="btn btn-default" onclick="RedSaveAction()" id="RedSave" />
    @*@Html.ActionLink("回選擇模式", "UPLOADCHECK", new { controller = (string)ViewBag.BRE_NO }, new { @class = "btn btn-default" })*@
</div>

@section Scripts {
    <script language="JavaScript">
        var targetFormID = '#form1';
        $(document).ready(function () {
            $(".img-responsive").colorbox({ opacity: 0.82, width: "90%" });
        });

        function AddItem() {
            form1.DATA_TYPE.value = 'AddItem'
            form1.action = '@Url.Action("Edit", (string)ViewBag.BRE_NO)';
            form1.submit();
        }
        function AutoCash(Val) {
            var re = /^[0-9]+$/;
            if (!re.test(Val)) {
                alert("只能輸入數字");
                $("#CASH").val('')
                return false;
            }

            if (parseInt(Val) > parseInt(10) || parseInt(Val) < parseInt(0)) {
                alert("輸入0~10的數字");
                $("#CASH").val('')
                return false;
            }

            if ($(".CASH").length == 0) {
                alert("請新增作者/主題");
                $("#CASH").val('')
                return false;
            }

            $(".CASH").each(function (i) {
                this.value = Val;
            });
        }
        function AutoCashSubject(Val) {
            //var re = /^[0-9]+$/;
            //if (!re.test(Val)) {
            //    alert("只能輸入數字");
            //    $("#CASH").val('')
            //    return false;
            //}

            //if (parseInt(Val) > parseInt(10) || parseInt(Val) < parseInt(0)) {
            //    alert("輸入0~10的數字");
            //    $("#CASH").val('')
            //    return false;
            //}

            //if ($(".CASH").length == 0) {
            //    alert("請新增作者/主題");
            //    $("#CASH").val('')
            //    return false;
            //}

            $(".SUBJECT").each(function (i) {
                this.value = Val;
            });
        }
     function onSave(Val) {

             if ($('#@Html.IdFor(m=>m.Main.ART_GALLERY_NO)').val() != '')
            {
                $('#fieldset_WORK_TYPE').removeProp('disabled');
            }

         var formdata = new FormData($('form').get(0));
         console.log(formdata);
            var OBtnSave = $('#BtnSave').html();

            $('#Msg').hide();
            $('span[data-valmsg-for="Error"]').html('');
            $('#BtnSave').attr('disabled', 'disabled').html("Loading...資料驗証中…請勿其他動作");

             $.ajax({
                url: '@Url.Action("CheckJosn", (string)ViewBag.BRE_NO)',
                data: formdata,
                processData: false,
                contentType: false,
                type: 'POST',
                async: false,
                dataType: 'json',               // xml/json/script/html
                cache: false,                   // 是否允許快取
                success: function (data) {

                    var res = $.parseJSON(data);

                    if (res.Success.toLowerCase() == 'true') {
                        SaveAction(Val)
                    }
                    else {
                             if ($('#@Html.IdFor(m=>m.Main.ART_GALLERY_NO)').val() != '')
                             {
                                 $('#fieldset_WORK_TYPE').attr("disabled", "disabled");
                              }

                        $('#Msg').show()
                        $('span[data-valmsg-for="Error"]').html('發生錯誤;原因:<br />' + res.Error);
                        $('#BtnSave').removeProp('disabled').html(OBtnSave);

                        $('html, body').animate({
                            scrollTop: $('#LayoutTOP').offset().top
                        }, 'show');
                    }

                },
                error: function (xhr, ajaxOptions, thrownError) {
                    alert(xhr.status);
                    alert(thrownError);
                }
            });
        }

        function SaveAction(Val)
        {
                    $('#@Html.IdFor(m=>m.IsTempSave)').val(Val)

                    $(":disabled", $(targetFormID)).removeAttr("disabled");
                    $('#BtnSave').attr('disabled', 'disabled').html("Loading...資料處理中…請勿其他動作");

                    $(targetFormID).attr("action", "@Url.Action("EditSave", (string)ViewBag.BRE_NO)")
                    $(targetFormID).submit();
        }
        function RedSaveAction()
        {

                    $(":disabled", $(targetFormID)).removeAttr("disabled");
            $('#RedSave').attr('disabled', 'disabled').html("Loading...資料處理中…請勿其他動作");

                    $(targetFormID).attr("action", "@Url.Action("UPLOADCHECK", (string)ViewBag.BRE_NO)")
                    $(targetFormID).submit();
        }
        function Save(Val)
        {

            var Msg = '';

            if ($('#VerifyUseYN').val() == 'Y') {
                for (var i = 0; i < form1.Num.value; i++) {
                    if ($("#Details_List_Del_" + i).prop('checked') == false) {
                        if ($('#files_' + i).val() == '') {
                            Msg = Msg + '第' + (i + 1) + '筆未上傳檔案\n';
                        }
                    }
                }
            }

            var BoolOK = false;

            if (Msg!='') {
                BoolOK = confirm(Msg +'\n 請問是否繼續')
            }
            else {
                BoolOK = true;
            }

            if (BoolOK) {
                form1.DATA_TYPE.value = Val
                form1.action = '@Url.Action("UploadEdite", (string)ViewBag.BRE_NO)';
                form1.submit();
            }
        }
    </script>
}