﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;

namespace ECOOL_APP.com.ecool.Models.entity
{
    public class ADDTViewModel
    {
        ///Summary
        ///學年度
        ///Summary
        [Display(Name = "學年度")]
        public string SYEAR { get; set; }

        ///Summary
        ///學期
        ///Summary
        [Display(Name = "學期")]
        public string SEME { get; set; }

        ///Summary
        ///班級
        ///Summary
        [Display(Name = "班級")]
        [StringLength(100)]
        public string CLASS_NO { get; set; }

        ///Summary
        ///座號
        ///Summary
        [Display(Name = "座號")]
        [StringLength(2)]
        public string SEAT_NO { get; set; }

        ///Summary
        ///姓名
        ///Summary
        [Display(Name = "姓名")]
         [StringLength(20)]
        public string USERNAME { get; set; }

        ///Summary
        ///閱讀本數
        ///Summary
        [Display(Name = "閱讀本數")]
        [StringLength(255)]
        public int BOOK_QTY { get; set; }

        ///Summary
        ///等級名稱
        ///Summary
        [Display(Name = "等級名稱")]
        [StringLength(50)]
        public string LEVEL_DESC { get; set; }

        ///Summary
        ///授權日期
        ///Summary
        [Display(Name = "授權日期")]
        [DataType(DataType.Date), DisplayFormat(DataFormatString = "{0:yyyy/MM/dd}", ApplyFormatInEditMode = true)]
        public DateTime UP_DATE { get; set; }

    }
}
