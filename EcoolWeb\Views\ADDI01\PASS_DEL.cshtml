﻿@model ECOOL_APP.EF.ADDT01
@using EcoolWeb.Util;
@using com.ecool.service
@{
    /**/

    ViewBag.Title = "線上投稿-批閱後投稿內容";

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    if (string.IsNullOrWhiteSpace(UUID) == false)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    ViewBag.VisibleSendChrisMail = PermissionService.GetPermission_Use_YN("ADDI01", "SendChrisMail", user?.SCHOOL_NO ?? "", user?.USER_NO ?? "");
}
@*<script src="~/js/bootstrap.min.js"></script>*@
<script src=@Url.Content("~/Scripts/jquery-ui.js") nonce="cmlvaw"></script>
@*<script src="http://code.jquery.com/ui/1.10.3/jquery-ui.js"></script>*@
<script src="~/Content/ckeditor/ckeditor.js" nonce="cmlvaw"></script>
<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" nonce="cmlvaw" />
<script src="~/Content/colorbox/jquery.colorbox.js" nonce="cmlvaw"></script>

@Html.Partial("_Title_Secondary")

@using (Html.BeginForm("PASS_DEL", "ADDI01", FormMethod.Post, new { name = "form1", enctype = "multipart/form-data" }))
{
    @Html.AntiForgeryToken()

    var Search = TempData["Search"] as EcoolWeb.Models.ADDI01IndexViewModel;

    @Html.Hidden("BackAction", Search.BackAction)
    @Html.Hidden("OrdercColumn", Search.OrdercColumn)
    @Html.Hidden("whereKeyword", Search.whereKeyword)
    @Html.Hidden("whereUserNo", Search.whereUserNo)
    @Html.Hidden("whereWritingStatus", Search.whereWritingStatus)
    @Html.Hidden("whereShareYN", Search.whereShareYN)
    @Html.Hidden("whereComment", Search.whereComment)
    @Html.Hidden("whereCommentCash", Search.whereCommentCash)
    @Html.Hidden("whereCLASS_NO", Search.whereCLASS_NO)
    @Html.Hidden("whereGrade", Search.whereGrade)
    @Html.Hidden("Page", Search.Page)
    @Html.Hidden("DeleteImage", "")
    <img src="~/Content/img/web-revise-submit-04.png" style="width:100%" class="img-responsive App_hide" alt="Responsive image" />
    <div class="Div-EZ-Pink">
        <div class="form-horizontal">
            @Html.ValidationSummary(true, "", new { @class = "text-danger" })

            @Html.HiddenFor(model => model.WRITING_NO)

            <div class="form-group">
                @Html.LabelFor(model => model.CRE_DATE, htmlAttributes: new { @class = "control-label col-md-2 col-sm-3" })
                <div class="col-md-2  col-sm-2" style="padding-top:7px">
                    @Html.DisplayFor(model => model.CRE_DATE, "ShortDateTime", new { htmlAttributes = new { @class = "form-control" } })
                </div>

                @Html.LabelFor(model => model.CLASS_NO, htmlAttributes: new { @class = "control-label col-md-2 col-sm-3" })
                <div class="col-md-2  col-sm-2" style="padding-top:7px">
                    @Html.DisplayFor(model => model.CLASS_NO, new { htmlAttributes = new { @class = "form-control" } })
                </div>

                @Html.LabelFor(model => model.NAME, htmlAttributes: new { @class = "control-label col-md-2 col-sm-3" })
                <div class="col-md-2  col-sm-2" style="padding-top:7px">
                    @Html.DisplayFor(model => model.NAME, new { htmlAttributes = new { @class = "form-control" } })
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.SUBJECT, htmlAttributes: new { @class = "control-label col-md-2 col-sm-3" })
                <div class="col-md-6  col-sm-6" style="padding-top:7px">
                    @Html.DisplayFor(model => model.SUBJECT, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.HiddenFor(model => model.SUBJECT)
                </div>
                @Html.LabelFor(model => model.SEAT_NO, htmlAttributes: new { @class = "control-label col-md-2 col-sm-3" })
                <div class="col-md-2  col-sm-2" style="padding-top:7px">
                    @Html.DisplayFor(model => model.SEAT_NO, new { htmlAttributes = new { @class = "form-control" } })
                </div>

                @Html.Label("是否出現作者姓名", htmlAttributes: new { @class = "control-label col-md-2 col-sm-3" })
                <div class="col-md-10  col-sm-9" style="padding-top:12px">
                    @Html.RadioButtonFor(model => model.AutherYN, false, new { @id = "AutherYN" })
                    @Html.Label("出現作者姓名")
                    @Html.RadioButtonFor(model => model.AutherYN, true, new { @id = "AutherYN" })
                    @Html.Label("不出現作者姓名")
                </div>

                @*@Html.LabelFor(model => model.CASH, htmlAttributes: new { @class = "control-label col-md-2 col-sm-3" })
                    <div class="col-md-2  col-sm-2" style="padding-top:7px">
                        @Html.DisplayFor(model => model.CASH, new { htmlAttributes = new { @class = "form-control" } })
                    </div>*@
            </div>

            <div class="form-group" disabled="disabled">
                @Html.Label("批閱前文章內容(要修改請修改下面表格)", htmlAttributes: new { @class = "control-label col-md-6 col-sm-6", @style = "padding-left:8px" })


                <div class="col-md-12">
                    <div class="p-context">
                        <label class="control-label-left label_dt col-md-12 col-sm-12 col-lg-12">
                            詳細內容:限制1500字，目前字數為<span id="ShowFontLen" style="color:red">@(Model.ARTICLE_VERIFY.Length) </span>字
                        </label>
                        <div id="ARTICLE">
                            @if (ViewBag.ShowOriginalArticle == "V")
                            {
                                @Html.Raw(HttpUtility.HtmlDecode(Model.ARTICLE_VERIFY))
                            }
                            else
                            {
                                @Html.Raw(HttpUtility.HtmlDecode(Model.ARTICLE))
                            }
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-group">
                @Html.LabelFor(model => model.ARTICLE_VERIFY, htmlAttributes: new { @class = "control-label col-md-3 col-sm-3", @style = "padding-left:8px" })

                <div class="col-md-11 col-sm-11" style="margin:10px">
                    @Html.TextAreaFor(model => model.ARTICLE_VERIFY, new { cols = "200", rows = "25", @class = "ckeditor", style = "white-space: pre-line" })
                    @Html.ValidationMessageFor(model => model.ARTICLE_VERIFY, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                <span class="control-label label_dt col-md-3 col-sm-3 col-lg-2">上傳圖檔</span>
                <div class="col-md-9 col-sm-9 col-lg-10">
                    @Html.Action("MultiFileUpload", "Comm")
                </div>
            </div>
            @if (string.IsNullOrWhiteSpace(Model.IMG_FILE) == false)
            {
                <div class="form-group">
                    <span class="control-label label_dt col-md-3 col-sm-3 col-lg-2">原圖檔</span>
                    <div class="text-center">
                        <br />
                        <br />
                        <br />
                        @if (ViewBag.ImageUrl != null && Enumerable.Any(ViewBag.ImageUrl))
                        {

                            int Num = 1;
                            if (ViewBag.ImageOrders != null)
                            {
                                var images = ViewBag.Images as List<string> ?? new List<string>();
                                var orders = ViewBag.ImageOrders as List<string> ?? new List<string>();
                                foreach (var Img in ViewBag.ImageUrl as List<string>)
                                {
                                    string ext = Path.GetExtension(Img);

                                    <div style="position:relative">
                                        @Html.Action("_imagesRotateBtnView", "Comm", new { ImgURL = Img, ImgID = "ImageX" + Num })

                                        <img id="@("ImageX" + Num)" src='@(Img + "?refreshCache=" + DateTime.Now.ToString("hhmmss"))' style="max-height:250px;width:auto;margin-left:auto;margin-right:auto;padding-bottom:10px" href="@Img" class="img-responsive " />
                                        @if (images.Count() > 0)
                                        {
                                            <button type="button" class="btn btn-danger" style="position:absolute; right:35px; top:40px"
                                                    onclick="DeleteMyImage($(this), '@images[Num - 1]')">
                                                ✖
                                            </button>
                                            <input type="number" name="Search.ImageOrders" value="@(images.Count == orders.Count&&orders[Num - 1]!="" ? orders[Num - 1] : Num.ToString())" placeholder="排序" class="form-inline form-control" style="position:absolute; right:20px; top:75px;width:70px" />
                                        }
                                        @*<input type="number" name="Search.ImageOrders" value="@(images.Count == orders.Count&&orders[Num - 1]!="" ? orders[Num - 1] : Num.ToString())" placeholder="排序" class="form-inline form-control" style="position:absolute; right:20px; top:75px;width:70px" />*@
                                    </div>


                                    <br />
                                    <br />

                                    Num = Num + 1;
                                }
                            }
                        }
                    </div>
                </div>
            }
            <div class="form-group">

                @Html.LabelFor(model => model.YOUTUBE_URL, htmlAttributes: new { @class = "control-label col-md-2 col-sm-3" })
                <div class="col-md-12 " style="padding-top:7px">
                    <div class="col-md-10">

                        <div class="input-group">
                            @Html.EditorFor(model => model.YOUTUBE_URL, new { htmlAttributes = new { @class = "form-control", @placeholder = Html.DisplayNameFor(n => n.YOUTUBE_URL) } })
                            <span class="input-group-btn">
                                <button class="btn btn-info" type="button" id="CheckYoutube">檢查網址</button>
                            </span>
                        </div><!-- /input-group -->
                        @*@Html.ValidationMessageFor(model => model.YOUTUBE_URL, "", new { @class = "text-danger" })*@
                    </div>
                    <br />
                    @*<a href="@Html.DisplayFor(model => model.YOUTUBE_URL)" target="_blank">@Html.DisplayFor(model => model.YOUTUBE_URL)</a>*@
                </div>
            </div>
            <div style="height:15px"></div>



            @if (string.IsNullOrEmpty(ViewBag.VoiceUrl) == false)
            {
                <div class="form-group">
                    <span class="control-label col-md-2 col-sm-3">上傳錄音</span>
                    <div class="col-md-9  col-sm-6" style="padding-top:7px">
                        @if (Request.Browser.Browser == "InternetExplorer")
                        {
                            <OBJECT ID="Player"
                                    CLASSID="CLSID:6BF52A52-394A-11d3-B153-00C04F79FAA6" width="300" height="45">
                                <PARAM name="autoStart" value="false">
                                <param name="URL" value="@ViewBag.VoiceUrl">
                            </OBJECT>
                        }
                        else
                        {
                            <audio width="300" height="48" controls="controls">
                                <source src="@ViewBag.VoiceUrl" />
                            </audio>
                        }
                    </div>
                </div>
            }
            <div class="form-group">
                <span class="control-label label_dt col-md-3col-sm-3 col-lg-2">上傳PPT或pdf(大小限制6MB)</span>
                <div class="col-md-12 col-sm-12 col-lg-10">
                    @Html.Action("MultiFiles2Upload", "Comm")
                </div>

                @if (string.IsNullOrWhiteSpace(Model.Upload_FILE) == false)
                {


                    <div class="text-center">
                        <br />
                        <br />
                        <br />
                        @if (ViewBag.OtherFilesUrl != null && Enumerable.Any(ViewBag.OtherFilesUrl))
                        {
                            int Num = 1;
                            foreach (var OtherFiles in ViewBag.OtherFilesUrl as List<string>)
                            {
                                string name = Path.GetFileName(OtherFiles);
                                <div class="col-md-12 col-sm-12 col-xs-12 " style="text-align:center">
                                    <br />
                                    <a href="@OtherFiles" class="btn btn-link btn-lg" target="_blank"><span class="glyphicon glyphicon-download-alt"></span> @name</a>
                                    <br /><br />
                                </div>
                                Num = Num + 1;
                            }
                        }
                    </div>

                }
            </div>
            <div style="height:15px"></div>


            <div class="form-group">
                &nbsp;&nbsp;
                @Html.LabelFor(model => model.VERIFY_COMMENT, htmlAttributes: new { @class = "control-label col-md-2 col-sm-3", @style = "padding-top:30px" })
                <div class="col-md-10  col-sm-9">
                    @Html.DropDownList("VERIFY_COMMENT_DropDownList", (IEnumerable<SelectListItem>)ViewBag.VCommentSelectItem, new { @class = "form-control", @onchange = "VCommentDropDownList(this.value)" })
                    @Html.HiddenFor(model => model.VERIFY_COMMENT)
                </div>
            </div>

            <div class="form-group">
                <div class="control-label col-md-2 col-sm-3">
                    @Html.LabelFor(model => model.CASH)
                    <span style="color:red">*</span>
                </div>
                <div class="col-md-10  col-sm-9" style="padding-top:7px">
                    @foreach (short c in ViewBag.CashArray)
                    {
                        if (c == 0)
                        {
                            @:
                            <br />
                        }
                        @Html.RadioButtonFor(model => model.CASH, c)
                        @Html.Label(c.ToString())
                        @:&nbsp
                    }
                </div>
                <div class="text-danger" style="padding-top:7px;padding-left:15px">
                    @if (ViewBag.CashAlert != null)
                    {@ViewBag.CashAlert}
                </div>
            </div>
            <div class="form-group">
                @Html.LabelFor(model => model.SHARE_YN, htmlAttributes: new { @class = "control-label col-md-2 col-sm-3" })
                <div class="col-md-9  col-sm-6" style="padding-top:7px">
                    @Html.RadioButtonFor(model => model.SHARE_YN, "y", new { @id = "SHARE_Y" })
                    @Html.Label("推薦")

                    @Html.RadioButtonFor(model => model.SHARE_YN, "n", new { @id = "SHARE_N" })
                    @Html.Label("取消")
                </div>
            </div>

            @{
                var checkBoxs = new List<CheckBoxListInfo>();
                CheckBoxListInfo chebox = new CheckBoxListInfo();
                chebox.DisplayText = "是 <br />五六年級學生文章會直接投稿到edit10 @mdnkids.com。<br />一到四年級學生文章會直接投稿到*****************。";
                chebox.Value = "Y";
                chebox.IsChecked = Model.PUBLISH_MDNKIDS_YN == "Y" ? true : false;
                checkBoxs.Add(chebox);
                var htmlAttribute = new Dictionary<string, object>();
                htmlAttribute.Add("id", "PUBLISH_MDNKIDS_YN");
            }
            @*@{
                    var checkBoxs = new List<CheckBoxListInfo>();
                    CheckBoxListInfo chebox = new CheckBoxListInfo();
                    chebox.DisplayText = $"是 { ((bool)ViewBag.IsFirstPublish ? "(尚未完成投稿)" : "(已投稿完成)") }　<br />※備註: 勾選並送出後會同時投稿國語日報，投稿過的文章則不會再重複發送。<br />五六年級學生文章會系統直接投稿到edit10 @mdnkids.com。<br />一到四年級學生文章會系統直接投稿到*****************。";
                    chebox.Value = "Y";
                    chebox.IsChecked = Model.PUBLISH_MDNKIDS_YN == "Y" ? true : false;
                    checkBoxs.Add(chebox);
                    var htmlAttribute = new Dictionary<string, object>();
                    htmlAttribute.Add("id", "PUBLISH_MDNKIDS_YN");
                }
                @if (chebox.IsChecked == false)
                {
                    <div class="form-group">
                        @Html.LabelFor(model => model.PUBLISH_MDNKIDS_YN, htmlAttributes: new { @class = "control-label col-md-2 col-sm-3" })
                        <div class="col-md-9 col-sm-6" style="padding-top:7px; @(chebox.IsChecked ? "" : "")  ">
                            @Html.CheckBoxList("PUBLISH_MDNKIDS_YN", (List<CheckBoxListInfo>)checkBoxs, htmlAttribute, 1)
                        </div>
                    </div>
                }*@
            @if (ViewBag.VisibleSendChrisMail == SharedGlobal.Y)
            {

                var checkBoxsCHRIS = new List<CheckBoxListInfo>();
                CheckBoxListInfo cheboxCHRISItem = new CheckBoxListInfo();
                cheboxCHRISItem.DisplayText = " ※<font color=\"#FF0000\">不會</font>真的寫電子信給聖誕老公公，只會將本文標誌和聖誕老公公有關，方便學校辦理活動匯出。(這是學校聖誕活動專用選項)";
                cheboxCHRISItem.Value = "Y";
                cheboxCHRISItem.IsChecked = Model.PUBLISH_CHRIS_YN == "Y" ? true : false;
                checkBoxsCHRIS.Add(cheboxCHRISItem);
                var htmlAttributeCHRIS = new Dictionary<string, object>();
                htmlAttributeCHRIS.Add("id", "PUBLISH_CHRIS_YN");


                <div class="form-group">
                    @Html.LabelFor(model => model.PUBLISH_CHRIS_YN, htmlAttributes: new { @class = "control-label label_dt col-md-3col-sm-3 col-lg-2" })

                    <div class="col-md-9 col-sm-6" style="padding-top:7px">
                        @Html.CheckBoxList("PUBLISH_CHRIS_YN", (List<CheckBoxListInfo>)checkBoxsCHRIS, htmlAttributeCHRIS, 1)
                    </div>
                </div>
            }
            @*<div id="mdnInfoDiv" style="@(chebox.IsChecked? "":"display:none;")">
                    @Html.Partial("__MdnKidsModal", Model)
                </div>*@

            <div class="form-group">
                @Html.Label("異動原因", htmlAttributes: new { @class = "control-label col-md-2 col-sm-3" })
                <div class="col-md-9  col-sm-6" style="padding-top:7px">
                    @Html.Editor("HIS_MEMO", new { htmlAttributes = new { @class = "form-control" } })
                </div>
            </div>
            <div>
                <div class="text-center">
                    <button onclick="UpdateVerify(this,'@Url.Action("PASS_DEL")')" class="btn btn-default"> 異動批閱後內容</button>
                    @*<a href='javascript:;'  role="button" onclick="UpdateVerify(this,'@Url.Action("PASS_DEL")')" class="btn btn-default">
                            異動批閱後內容
                        </a>*@
                </div>
            </div>

            <div style="height:15px"></div>
            <div class="form-group">
                &nbsp;&nbsp;
                @Html.Label("作廢原因", htmlAttributes: new { @class = "control-label col-md-2 col-sm-3", @style = "padding-top:30px" })
                <div class="col-xs-12">
                    @Html.DropDownList("BACK_MEMO_DropDownList", (IEnumerable<SelectListItem>)ViewBag.BackSelectItem, new { @class = "form-control", @onchange = "BackDropDownList(this.value)" })
                    @Html.HiddenFor(model => model.BACK_MEMO)
                </div>
            </div>
            <div>
                <div class="text-center">

                    <a href='javascript:;' value="DisableUpSetDraft" onclick="DisableGO(this,'@Url.Action("PASS_DEL")')" class="btn btn-default">
                        作廢
                    </a>



                    <a href='javascript:;' onclick="DoGO('@Url.Action(Search.BackAction)')" role="button" class="btn btn-default">
                        返回
                    </a>
                </div>
            </div>
        </div>
    </div>

}
<div hidden="hidden" id="notice">
    <span>作品經採用刊登將同時刊於國語日報,國語日報網站及相關行動載具及臉書,並收錄於國語日報知識庫。</span></br>

    <span style="color:red;">
        請勿抄襲(投稿圖文需原創,如有侵權行為,由創作者自負法律責任),及一稿多投,
    </span>
    <span>
        本報
        得對來稿有刪修權,投稿三週內若未通知採用,請自行處理。(文字引自於國語日報網站)
    </span>
</div>
@section Scripts {
    <script nonce="cmlvaw">
        // 設置全局配置
        window.ADDI01_CONFIG = {
            otherValue: "@ECOOL_APP.com.ecool.service.BDMT02Service.OtherVal",
            userName: "@ViewBag.userName"
        };
    </script>
    <script src="~/Scripts/ADDI01/pass-del.js" nonce="cmlvaw"></script>
}