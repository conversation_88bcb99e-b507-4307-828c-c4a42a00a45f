//icon

%iconHover {
  // cursor: pointer;
  &:hover {
    filter: brightness(0.8);
  }
  &:active {
    filter: brightness(1.1);
  }
}

// icon 引入
@mixin icon($iconName, $bgSize) {
  background-image: url(/Content/images/#{$iconName});
  background-size: $bgSize;
}

// 設定 icon 各種尺寸
@mixin iconSize($iconSize) {
  height: $iconSize;
  width: $iconSize;
}

.icon {
  &-size-xs {
    &::before {
      display: inline-block;
      @include iconSize(1.2rem);
      vertical-align: text-bottom;
    }
  }
  &-size-s {
    &::before {
      display: inline-block;
      @include iconSize(1.4rem);
      vertical-align: text-bottom;
    }
  }
  &-size-m {
    &::before {
      display: inline-block;
      @include iconSize(2rem);
      vertical-align: text-bottom;
    }
  }
  &-size-lg {
    &::before {
      display: inline-block;
      @include iconSize(3rem);
      vertical-align: text-bottom;
    }
  }
  &-add {
    &::before {
      content: "";
      @include icon("icon-add.svg", 100%) // background-image: url(/Content/images/icon-news.svg);
        // background-size: 100%;;;;
    }
  }
  &-back {
    &::before {
      content: "";
      @include icon("icon-back.svg", 100%);
    }
  }
 
  
 
  
  
  
  &-user {
    &::before {
      content: "";
      @include icon("icon-user.svg", 100%);
    }
  }
  // &-info {
  //   &::before {
  //     content: "";
  //     @include icon("icon-info.svg", 100%);
  //     @extend %iconHover;
  //   }
  // }
  
  
  // &-ban {
  //   height: 1rem;
  //   width: 1rem;
  //   @extend %iconHover;
  //   &::before {
  //     content: "";
  //     @include icon("icon-ban.svg", 100%);
  //   }
  // }
}



