﻿using ECOOL_APP.com.ecool.LogicCenter;
using ECOOL_APP.com.ecool.LogicCenter.Interfaces;
using ECOOL_APP.com.ecool.util;
using ECOOL_APP.EF;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Data.SqlClient;
using com.ecool.sqlConnection;
using System.IO;
using Dapper;
using log4net;
using System.Text.RegularExpressions;

namespace ECOOL_APP.com.ecool.service
{
    public class HrmtImportService : AbstractExcelAccess, IResult, IDisposable
    {
        private NPOIHelper npoi;
        private ECOOL_DEVEntities db;
        private static ILog logger = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        #region Results 結果

        public string R_Message { get; set; }

        public List<string> R_CreHt01 { get; set; }
        public List<string> R_ModifyHt01 { get; set; }

        #endregion Results 結果

        #region 建構子

        public HrmtImportService() : base()
        {
            npoi = new NPOIHelper();
            R_CreHt01 = new List<string>();
            R_ModifyHt01 = new List<string>();
            db = new ECOOL_DEVEntities();
            R_Message = @"";
        }

        private SqlConnection conn = null;
        private string sp_name = string.Empty;

        public string MERGETEMPHRMTBySchool(string SCHOOL_NO)
        {
            sp_name = "MERGE_TEMPHRMT_BySchool";
            sqlConnection getConn = new sqlConnection();
            conn = getConn.getConnection4Query();
            SqlCommand cmd = new SqlCommand("dbo." + sp_name, conn);
            cmd.CommandType = System.Data.CommandType.StoredProcedure;
            cmd.Parameters.Add("@SCHOOL_NO", System.Data.SqlDbType.VarChar, 50).Value = SCHOOL_NO;
            cmd.CommandTimeout = 300;
            SqlParameter retValParam = new SqlParameter("@return_value", System.Data.SqlDbType.Int);
            retValParam.Direction = System.Data.ParameterDirection.Output;

            try
            {
                cmd.ExecuteNonQuery();

                handleException(sp_name, (string)retValParam.Value);
                return (string)retValParam.Value;
            }
            catch (Exception ex)
            {
                throw ex.GetBaseException();
            }
            finally
            {
                getConn.closeConnection4Query(conn);
            }
        }

        //檢核錯誤
        public void handleException(string sp_name, string DBMsg)
        {
            //if (null != DBMsg && !"".Equals(DBMsg))
            //{
            //    Console.WriteLine("預儲程式" + sp_name + " 執行時發生錯誤:" + DBMsg.ToString());

            //    if (!"".Equals(DBMsg.ToString().Trim()))
            //    {
            //        throw new Exception("Stored Procedure名稱:" + sp_name + "執行時發生錯誤:" + DBMsg.ToString());
            //        //throw new Exception("請洽系統管理員");
            //    }

            //}
        }

        #endregion 建構子

        /// <summary>
        /// 匯入/變更學生內碼
        /// </summary>
        /// <param name="excelFiles">Excel檔案</param>
        /// <returns></returns>
        public bool ImportCardNoFromExcel(HttpPostedFileBase excelFiles, string schoolNo)
        {
            DataSet excelDs;
            npoi.onDataTypeConflict += (s, e) => NPOI_DataTypeConflict(s, e);
            npoi.onLineCheckValue += (s, e) =>
            {
                int index = Array.IndexOf(SheetNames, e.SheetName);
                if (!IsDBNullOrNullEmpty(e.Row["學號"]))
                    foreach (string col in SheetMustColumnNames[index])
                        if (IsDBNullOrNullEmpty(e.Row[col]))
                        {
                            HasErr = true;
                            ErrorRowCellExcel = ErrorRowCellExcel + "Sheet名稱【" + e.SheetName + "】-欄位名稱【" + col + "】，第" + e.RowNo.ToString() + "列-必需輸入<br/>";
                        }
            };

            SheetNames = new string[1] { "373610" };
            SheetMustColumnNames = new string[1][] {
                new [] {
                    "學號",  "卡片內碼"
                }
            };

            if (excelFiles == null)
            {
                R_Message = "未選取Excel檔案";
                return false;
            }
            excelDs = npoi.Excel2Table(excelFiles.InputStream, 0, 1, 0, SheetNames);
            if (IsExcelErrorExist(excelDs)) return false;

            #region 變更內碼 func

            Action<DataRow, int> changeCard_No = (dr, index) =>
            {
                if (IsDBNullOrNullEmpty(dr["學號"], true))
                    return;
                string thisUserNo = Convert.ToString(dr["學號"]);

                HRMT01 thisHt01 = db.HRMT01.Where(a => a.SCHOOL_NO == schoolNo && a.USER_NO == thisUserNo).FirstOrDefault();

                // Modify
                if (thisHt01 != null)
                {
                    thisHt01.CARD_NO = dr["卡片內碼"].ToType<string>();
                    R_ModifyHt01.Add($"列{index}，{thisUserNo} - 匯入成功<br />");
                    db.Entry(thisHt01).State = System.Data.Entity.EntityState.Modified;
                }
                else
                {
                    R_ModifyHt01.Add($"列{index}，{thisUserNo} - 匯入失敗，原因: 找不到該學號。<br />");
                }
            };

            #endregion 變更內碼 func

            try
            {
                int index = 1;

                DataTable dtCardNo = excelDs.Tables[0];
                if (dtCardNo != null)
                    foreach (DataRow dr in dtCardNo.Rows)
                        changeCard_No(dr, index++);
                db.SaveChanges();
            }
            catch (Exception ex)
            {
                R_Message = ex.Message;
                return false;
            }

            return true;
        }

        public void ClearErrors()
        {
            R_Message = @"";
            CheckDataTypeErr = false;
            HasErr = false;
        }

        public Dictionary<bool, string> ImportTeacherClassInfo(HttpPostedFileBase excelFiles)
        {
            byte[] buffer = new byte[excelFiles.InputStream.Length];
            MemoryStream ms = new MemoryStream(buffer);
            ms.Read(buffer, 0, (int)ms.Length);

            ms.Close();

            Dictionary<bool, string> dic = new Dictionary<bool, string>();
            //dic.Add(true, "");
            DataSet excelDs;
            npoi.onDataTypeConflict += (s, e) => NPOI_DataTypeConflict(s, e);
            npoi.onLineCheckValue += (s, e) =>
            {
                int index = Array.IndexOf(SheetNames, e.SheetName);
                if (!IsDBNullOrNullEmpty(e.Row["學校代號"]))
                    foreach (string col in SheetMustColumnNames[index])
                        if (IsDBNullOrNullEmpty(e.Row[col]))
                        {
                            HasErr = true;
                            ErrorRowCellExcel = ErrorRowCellExcel + "Sheet名稱【" + e.SheetName + "】-欄位名稱【" + col + "】，第" + e.RowNo.ToString() + "列-必需輸入<br/>";
                        }
            };
            SheetNames = new string[1] { "教師帶班表" };
            SheetMustColumnNames = new string[1][] {
                new [] {
                    "學校代號",  "帳號","教師姓名","身分證字號","班級代號","班級名稱"
                }
            };

            if (excelFiles == null)
            {
                dic.Add(false, "");
                R_Message = "未選取Excel檔案";
                return dic;
            }
            excelDs = npoi.Excel2Table(excelFiles.InputStream, 0, 1, 0, SheetNames);
            if (IsExcelErrorExist(excelDs))
            {
                dic.Add(false, "");
                return dic;
            }

            #region 變更內碼 func

            int Year = DateTime.Now.Year;
            int month = DateTime.Now.Month;
            int stryear = Year - 1911;
            int stream = 1;
            if (month > 7)
            {
                stream = 1;
            }
            else if (month < 2)
            {
                stream = 1;
                stryear = stryear - 1;
            }
            else
            {
                stream = 2;
                stryear = stryear - 1;
            }
            string SCH = "";
            Action<DataRow, int> changeCard_No = (dr, index) =>
            {
                if (IsDBNullOrNullEmpty(dr["學校代號"], true))
                    return;
                string schoolNo = Convert.ToString(dr["學校代號"]);
                SCH = schoolNo;
                if (IsDBNullOrNullEmpty(dr["帳號"], true))
                    return;
                string USERNO = Convert.ToString(dr["帳號"]);
                if (IsDBNullOrNullEmpty(dr["教師姓名"], true))
                    return;
                string TeacherName = Convert.ToString(dr["教師姓名"]);
                if (IsDBNullOrNullEmpty(dr["身分證字號"], true))
                    return;
                string IDNO = Convert.ToString(dr["身分證字號"]);
                if (IsDBNullOrNullEmpty(dr["班級代號"], true))
                    return;
                string CLASS_NO = Convert.ToString(dr["班級代號"]);
                if (IsDBNullOrNullEmpty(dr["班級名稱"], true))
                    return;
                string CLASSNAME = Convert.ToString(dr["班級名稱"]);

                HRMT01 thisHt01 = db.HRMT01.Where(a => a.SCHOOL_NO == schoolNo && a.USER_NO == USERNO).FirstOrDefault();
                Temp_TEABAS thisTEABAS = db.Temp_TEABAS.Where(x => x.SCHOOL_NO == schoolNo && x.IDNO == IDNO).FirstOrDefault();
                    //存在在temp 資料表
                if (thisTEABAS != null)
                {
                    Temp_BASCLS thisBASCLS = db.Temp_BASCLS.Where(X => X.SCHOOL_NO == schoolNo && X.TEAID == thisTEABAS.TEAID).FirstOrDefault();
                    if (thisBASCLS != null)
                    {
                        thisBASCLS.SCHOOL_NO = schoolNo;
                        thisBASCLS.SEYEAR = stryear.ToString();
                        thisBASCLS.SESEM = stream.ToString();
                        thisBASCLS.CLASSID = CLASS_NO;
                        thisBASCLS.CLASSNAME = CLASSNAME;
                        //thisBASCLS.TEAID = thisTEABAS.TEAID;
                        db.Entry(thisBASCLS).State = System.Data.Entity.EntityState.Modified;
                    }
                    else
                    {
                        Temp_BASCLS temp_BASCLS = new Temp_BASCLS();

                        temp_BASCLS.SCHOOL_NO = schoolNo;
                        temp_BASCLS.SEYEAR = stryear.ToString();
                        temp_BASCLS.SESEM = stream.ToString();
                        temp_BASCLS.CLASSID = CLASS_NO;
                        temp_BASCLS.CLASSNAME = CLASSNAME;
                        temp_BASCLS.TEAID = thisTEABAS.TEAID;
                        db.Entry(temp_BASCLS).State = System.Data.Entity.EntityState.Added;
                    }
                    db.SaveChanges();
                }
                else if (thisHt01 != null && thisTEABAS==null)
                {
                    int tempCount = 0;
                    tempCount = db.Temp_TEABAS.ToList().Count();
                    int i = 0;
                    if (tempCount > 0)
                    {
                        i = db.Temp_TEABAS.Select(X => X.TEAID).Max();
                    }
                    string bthday = "";
                    if (thisHt01.BIRTHDAY != null)
                    {
                        bthday = ((DateTime)thisHt01.BIRTHDAY).ToShortDateString();
                    }
                    Temp_TEABAS temp_TEABAS = new Temp_TEABAS();
                    temp_TEABAS.TEAID = i + 1;
                    temp_TEABAS.SCHOOL_NO = schoolNo;
                    temp_TEABAS.IDNO = IDNO;
                    temp_TEABAS.TEANAME = TeacherName;
                    temp_TEABAS.TEAMAIL = thisHt01.E_MAIL;
                    temp_TEABAS.TEASEX = thisHt01.SEX;
                    temp_TEABAS.BIRTHDAY = bthday;
                    temp_TEABAS.ATSCHOOL = thisHt01.USER_STATUS.ToString();
                    temp_TEABAS.TEAMOBIL = thisHt01.TEL_NO;
                    db.Entry(temp_TEABAS).State = System.Data.Entity.EntityState.Added;
                    db.SaveChanges();
                    Temp_BASCLS temp_BASCLS = new Temp_BASCLS();

                    temp_BASCLS.SCHOOL_NO = schoolNo;
                    temp_BASCLS.SEYEAR = stryear.ToString();
                    temp_BASCLS.SESEM = stream.ToString();
                    temp_BASCLS.CLASSID = CLASS_NO;
                    temp_BASCLS.CLASSNAME = CLASSNAME;
                    temp_BASCLS.TEAID = temp_TEABAS.TEAID;
                    db.Entry(temp_BASCLS).State = System.Data.Entity.EntityState.Added;
                    db.SaveChanges();
                }
                else
                {
                    dic.Add(false, "");
                    R_Message = "請先新增此老師資料";
                }
            };

            #endregion 變更內碼 func

            try
            {
                int index = 1;

                DataTable dtCardNo = excelDs.Tables[0];
                if (dtCardNo != null)
                    foreach (DataRow dr in dtCardNo.Rows)
                        changeCard_No(dr, index++);
            }
            catch (Exception ex)
            {
                dic.Add(false, "");
                logger.Info("帶班資訊" + ex.Message);
                if (ex.InnerException != null) { 
                logger.Info("帶班資訊"+ex.InnerException);
                }
                R_Message = ex.Message;
                return dic;
            }
            dic.Add(true, SCH);
            // excelFiles.InputStream.Flush();
            // excelFiles.InputStream.Dispose();
            return dic;
        }

        public List<ADDI10IndexListViewModel> CountADDI10List(string USER_KEY, string SCHOOL_NO, string USERNO)
        {
            List<ADDI10IndexListViewModel> ADDI10IndexListViewModelTemp;
            string sSQL = $@"select *from (
                             Select Status as Status1 ,QUESTIONNAIRE_ID = a.QUESTIONNAIRE_ID,
		                     QUESTIONNAIRE_NAME = a.QUESTIONNAIRE_NAME,
		                     QUESTIONNAIRE_DESC = a.QUESTIONNAIRE_DESC,
		                     QUESTIONNAIRE_SDATE = a.QUESTIONNAIRE_SDATE,
		                     QUESTIONNAIRE_EDATE = a.QUESTIONNAIRE_EDATE,
		                     STATUS = a.STATUS,
		                     CHG_PERSON = a.CHG_PERSON,
		                     CHG_DATE = a.CHG_DATE,
		                     CRE_PERSON = a.CRE_PERSON,
		                     CRE_PERSON_NAME = b.NAME,
		                     CRE_PERSON_SNAME = b.SNAME,
		                     CRE_DATE = a.CRE_DATE,
		                     SOU_KEY = a.SOU_KEY,
		                     CASH = a.CASH,
		                     RESULT = a.RESULT,
		                     REGISTERED_BALLOT = a.REGISTERED_BALLOT,
                             VoteUSERCount=(SELECT COUNT(*) FROM SAQT04_M M  (nolock) WHERE M.QUESTIONNAIRE_ID = a.QUESTIONNAIRE_ID
                                                  and M.SCHOOL_NO=@SCHOOL_NO  and M.USER_NO=@USER_NO and M.DEL_YN='N' ),
		                     VOTE_COUNT = (SELECT COUNT(*) FROM SAQT04_M M  (nolock) WHERE M.QUESTIONNAIRE_ID = a.QUESTIONNAIRE_ID and M.DEL_YN='N' ),
                             IsVote = Case When (SELECT COUNT(*) FROM SAQT04_M M  (nolock) WHERE M.QUESTIONNAIRE_ID = a.QUESTIONNAIRE_ID
                                                  and M.SCHOOL_NO=@SCHOOL_NO and M.USER_NO=@USER_NO and M.DEL_YN='N' )>=a.ANSWER_COUNT Then 0 else  1 End,
                              ANSWER_PERSON_YN=Isnull(a.ANSWER_PERSON_YN,'N')
							 ,ANSWER_PERSON_COUNT = (Select COUNT(*) from REFT01 r (nolock) where r.REF_KEY= a.QUESTIONNAIRE_ID and r.REF_TABLE = 'SAQT01' and r.SCHOOL_NO=@SCHOOL_NO  and r.USER_NO=@USER_NO )
                    from SAQT01 a (nolock)
                    left outer join HRMT01 b  (nolock) on a.CRE_PERSON=b.USER_KEY
                    Where a.SOU_KEY =@SOU_KEY   and ( (a.QUESTIONNAIRE_EDATE > @Now) or (a.CRE_PERSON = @CRE_PERSON ) )
					) as kk
					where kk.Status1 is not null and kk.IsVote=1 and kk.Status1 !=3  and  kk.Status1 !=0 and  kk.Status1  !=9 and ((ANSWER_PERSON_YN='Y' and  ANSWER_PERSON_COUNT>0 ) or ANSWER_PERSON_YN='N'  or CRE_PERSON=@CRE_PERSON)";

            var Temp = db.Database.Connection.Query<ADDI10IndexListViewModel>(sSQL
                     , new
                     {
                         SOU_KEY = SCHOOL_NO,
                         Now = DateTime.Now,
                         CRE_PERSON = USER_KEY,
                         SCHOOL_NO = SCHOOL_NO,
                         USER_NO = USERNO,
                     });

            ADDI10IndexListViewModelTemp = (List<ADDI10IndexListViewModel>)Temp;
            return ADDI10IndexListViewModelTemp;
        }

        public List<ADDT11> CountADDT11Student(string SCHOOL_NO, string USERNO)
        {
            List<ADDT11> ADDT11Temp;
            string sSQL = $@"
                              select *from (
                                (SELECT A.DIALOG_ID ,A.SCHOOL_NO,A.SYEAR,A.SEMESTER,A.DIALOG_NAME,A.DIALOG_EXPRESS,A.DIALOG_SDATE,A.DIALOG_EDATE
                                 ,A.USER_NO,A.NAME,A.SNAME,A.JOB_NAME,A.CHG_PERSON,A.CHG_DATE   ,MEMO=CASE WHEN A.STATUS='Z' Then '活動已提早結束'
                               WHEN CONVERT(nvarchar(10),GETDATE(),111)>CONVERT(nvarchar(10),A.DIALOG_EDATE,111)  THEN '活動已結束'
	                           WHEN CONVERT(nvarchar(10),GETDATE(),111)<CONVERT(nvarchar(10), A.DIALOG_SDATE,111) THEN '活動未開始'
	                           ELSE '' END ,A.STATUS,A.DIALOG_TYPE,CASE WHEN  A.ANSWER_COUNT is  null then 'Y' when  (  A.ANSWER_COUNT is  null  and A.ANSWER_COUNT=(select count(*) from ADDT13_HIS where DIALOG_ID=A.DIALOG_ID and SCHOOL_NO=@SCHOOLNO and USER_NO=@USERNO)) then 'N' else 'Y' end  as ANSWERCOUNTYN

		                       FROM ADDT11 A  (NOLOCK)
		                       inner join   REFT01 B on A.DIALOG_ID = B.REF_KEY and B.SCHOOL_NO =@SCHOOLNO  and B.USER_NO=@USERNO
		                         WHERE 1=1 AND A.STATUS IN ('R','Z') AND (A.SCHOOL_NO = @SCHOOLNO or A.SCHOOL_NO = 'ALL')
		                     AND CONVERT(nvarchar(10), A.DIALOG_SDATE,111) <= CONVERT(nvarchar(10),GETDATE(),111) AND CONVERT(nvarchar(10), A.DIALOG_EDATE,111) >=CONVERT(nvarchar(10),GETDATE(),111)
		                        AND A.ANSWER_PERSON_YN='Y' and A.DIALOG_ID not in (select DIALOG_ID from  ADDT13 where SCHOOL_NO=@SCHOOLNO and USER_NO=@USERNO))

		                        UNION
		                        --無限制人
		                        (SELECT A.DIALOG_ID ,A.SCHOOL_NO,A.SYEAR,A.SEMESTER,A.DIALOG_NAME,A.DIALOG_EXPRESS,A.DIALOG_SDATE,A.DIALOG_EDATE
                                ,A.USER_NO,A.NAME,A.SNAME,A.JOB_NAME,A.CHG_PERSON,A.CHG_DATE   ,MEMO=CASE WHEN A.STATUS='Z' Then '活動已提早結束'
                                  WHEN CONVERT(nvarchar(10),GETDATE(),111)>CONVERT(nvarchar(10),A.DIALOG_EDATE,111)  THEN '活動已結束'
	                              WHEN CONVERT(nvarchar(10),GETDATE(),111)<CONVERT(nvarchar(10), A.DIALOG_SDATE,111) THEN '活動未開始'
	                               ELSE '' END ,A.STATUS,A.DIALOG_TYPE ,CASE WHEN  A.ANSWER_COUNT is  null then 'Y' when  (  A.ANSWER_COUNT is  null  and A.ANSWER_COUNT=(select count(*) from ADDT13_HIS where DIALOG_ID=A.DIALOG_ID and SCHOOL_NO=@SCHOOLNO and USER_NO=@USERNO)) then 'N' else 'Y' end  as ANSWERCOUNTYN
		                           FROM ADDT11 A  (NOLOCK)
		                          --inner join   REFT01 B on A.DIALOG_ID = B.REF_KEY and B.SCHOOL_NO =@SCHOOLNO  and B.USER_NO=@USERNO
		                            WHERE 1=1 AND A.STATUS IN ('R','Z') AND (A.SCHOOL_NO = @SCHOOLNO or A.SCHOOL_NO = 'ALL')
		                             AND CONVERT(nvarchar(10), A.DIALOG_SDATE,111) <= CONVERT(nvarchar(10),GETDATE(),111) AND CONVERT(nvarchar(10), A.DIALOG_EDATE,111) >=CONVERT(nvarchar(10),GETDATE(),111)
		                             AND A.ANSWER_PERSON_YN is null and A.DIALOG_ID not in (select DIALOG_ID from  ADDT13 where SCHOOL_NO=@SCHOOLNO and USER_NO=@USERNO) ) ) A where A.ANSWERCOUNTYN='Y'";

            var Temp = db.Database.Connection.Query<ADDT11>(sSQL
                     , new
                     {
                         SCHOOLNO = SCHOOL_NO,
                         USERNO = USERNO,
                     });

            ADDT11Temp = (List<ADDT11>)Temp;
            return ADDT11Temp;
        }

        public bool CheckStudentTasklist(UserProfile LoginUser)
        {
            ECOOL_APP.EF.ECOOL_DEVEntities db = new ECOOL_APP.EF.ECOOL_DEVEntities();
            HrmtImportService hrmtImportService = new HrmtImportService();
            List<ADDT11> ADDT11Temp = new List<ADDT11>();

            ADDI12Service ADDI12Service = new ADDI12Service();
            List<ADDI10IndexListViewModel> ADDI10IndexListViewModelTemp = new List<ADDI10IndexListViewModel>();
            int? checkcount = 0;
            //有獎徵答
            ADDT11Temp = hrmtImportService.CountADDT11Student(LoginUser.SCHOOL_NO, LoginUser.USER_NO);
            checkcount += ADDT11Temp?.Count();
            //投票
            ADDI10IndexListViewModelTemp = hrmtImportService.CountADDI10List(LoginUser.USER_KEY, LoginUser.SCHOOL_NO, LoginUser.USER_NO);
            checkcount += ADDI10IndexListViewModelTemp?.Count();
            checkcount += ADDI12Service.GetADDI12forNoticeCount(LoginUser.SCHOOL_NO, LoginUser.USER_NO, true, ref db) ?? 0;
            checkcount += ADDI12Service.GetADDI12forNoticeCount(LoginUser.SCHOOL_NO, LoginUser.USER_NO, false, ref db) ?? 0;
            checkcount += LoginUser.NoteReadLevel;
            checkcount += LoginUser.Chance_BIRTHDAY ? 1 : 0;
            if (checkcount > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        public Dictionary<bool, string> ImportStudentInfo(HttpPostedFileBase excelFiles)
        {
            byte[] buffer = new byte[excelFiles.InputStream.Length];
            MemoryStream ms = new MemoryStream(buffer);
            ms.Read(buffer, 0, (int)ms.Length);

            ms.Close();
            Dictionary<bool, string> dic = new Dictionary<bool, string>();
            DataSet excelDs;
            npoi.onDataTypeConflict += (s, e) => NPOI_DataTypeConflict(s, e);
            npoi.onLineCheckValue += (s, e) =>
            {
                int index = Array.IndexOf(SheetNames, e.SheetName);
                if (!IsDBNullOrNullEmpty(e.Row["學校代號"]))
                    foreach (string col in SheetMustColumnNames[index])
                        if (IsDBNullOrNullEmpty(e.Row[col]))
                        {
                            HasErr = true;
                            ErrorRowCellExcel = ErrorRowCellExcel + "Sheet名稱【" + e.SheetName + "】-欄位名稱【" + col + "】，第" + e.RowNo.ToString() + "列-必需輸入<br/>";
                        }
            };
            SheetNames = new string[1] { "學生" };
            SheetMustColumnNames = new string[1][] {
                new [] {
                    "學校代號", "學號","姓名","身分證字號","性別","入學年度","年級","班級","座號","生日","在校狀態(0:在校, 2:離校)"
                }
            };

            if (excelFiles == null)
            {
                dic.Add(false, "");
                R_Message = "未選取Excel檔案";
                return dic;
            }
            excelDs = npoi.Excel2Table(excelFiles.InputStream, 0, 2, 0, SheetNames);
            if (IsExcelErrorExist(excelDs))
            {
                dic.Add(false, "");

                return dic;
            }

            #region 變更內碼 func

            string SCH = "";
            int stryear;
            int strMonth;
            int stream;
            stryear = DateTime.Now.Year - 1911;
            strMonth = DateTime.Now.Month;
            if (strMonth > 7)

            {
                stream = 1;
            }
            else if (strMonth <= 2)
            {
                stream = 1;
                stryear = stryear - 1;
            }
            else
            {
                stream = 2;
                stryear = stryear - 1;
            }
            Action<DataRow, int> changeCard_No = (dr, index) =>
        {
            if (IsDBNullOrNullEmpty(dr["學校代號"], true))
                return;
            string schoolNo = Convert.ToString(dr["學校代號"]);
            SCH = schoolNo;

            if (IsDBNullOrNullEmpty(dr["學號"], true))
                return;
            string USERNO = Convert.ToString(dr["學號"]);

            if (IsDBNullOrNullEmpty(dr["姓名"], true))
                return;
            string TeacherName = Convert.ToString(dr["姓名"]);
            if (IsDBNullOrNullEmpty(dr["身分證字號"], true))
                return;
            string IDNO = Convert.ToString(dr["身分證字號"]);
            if (IsDBNullOrNullEmpty(dr["性別"], true))
                return;
            string SEX = Convert.ToString(dr["性別"]);
            if (IsDBNullOrNullEmpty(dr["入學年度"], true))
                return;
            string SYEAR = Convert.ToString(dr["入學年度"]);
            if (IsDBNullOrNullEmpty(dr["年級"], true))
                return;
            string GRADE = Convert.ToString(dr["年級"]);
            if (IsDBNullOrNullEmpty(dr["班級"], true))
                return;
            string CLASS_NO = Convert.ToString(dr["班級"]);
            if (IsDBNullOrNullEmpty(dr["座號"], true))
                return;
            string SEAT_NO = Convert.ToString(dr["座號"]);
            if (IsDBNullOrNullEmpty(dr["生日"], true))
                return;
            DateTime BIRTHDAY = new DateTime();
            string pattern = @"日";
            string pattern1 = @"日";
            Match match = Regex.Match(dr["生日"].ToString(), pattern);
            if (match.Success) {

                string s = dr["生日"].ToString();
                char[] separators = new char[] { '日', ' ' };
                char[] separators1 = new char[] { '月', ' ' };
                char[] separators2 = new char[] { '年', ' ' };
                string[] subs = s.Split(separators, StringSplitOptions.RemoveEmptyEntries);
                string separatorsa = "";

                string[] subs1 = subs[0].Split(separators1, StringSplitOptions.RemoveEmptyEntries);
                string[] subs2 = subs1[0].Split(separators2, StringSplitOptions.RemoveEmptyEntries);

                separatorsa = subs1[1];
                separatorsa = subs2[0] + "/" + subs2[1] + "/" + subs1[1];
               

    
}
            else
            {
                BIRTHDAY = DateTime.Parse(dr["生日"].ToString());
                //  string dt = DateTime.FromOADate(Convert.ToInt32(s)).ToString("d");
                //    BIRTHDAY = DateTime.Parse(dt);
            }
            //string BIRTHDAY = Convert.ToString(dr["生日"]);
            //   DateTime BIRTHDAY= Convert.ToDateTime(dr["生日"]);
            if (IsDBNullOrNullEmpty(dr["在校狀態(0:在校, 2:離校)"], true))
                return;
            string USER_STATUS = Convert.ToString(dr["在校狀態(0:在校, 2:離校)"]);
            HRMT01 thisHt01 = db.HRMT01.Where(a => a.SCHOOL_NO == schoolNo && a.USER_NO == USERNO).FirstOrDefault();
            Temp_STUDENT thisSTUDENT = db.Temp_STUDENT.Where(x => x.SCHOOL_NO == schoolNo && x.IDNO == IDNO).FirstOrDefault();

            int tempCount = 0;
            tempCount = db.Temp_STUDENT.ToList().Count();

            int i = 0;
            if (tempCount > 0)
            {
                i = db.Temp_STUDENT.Select(X => X.ID).Max();
            }
            if (thisSTUDENT != null)
            {
                thisSTUDENT.SCHOOL_NO = schoolNo;
                thisSTUDENT.IDNO = IDNO;

                thisSTUDENT.STDNO = USERNO;
                thisSTUDENT.NAME = TeacherName;
                thisSTUDENT.SEYEAR = stryear.ToString();
                thisSTUDENT.SEX = SEX;
                thisSTUDENT.BIRTHDAY = BIRTHDAY.ToString("yyyyMMdd");
                //thisSTUDENT.BIRTHDAY = DateTime.Parse(BIRTHDAY).ToString("yyyyMMdd");
                thisSTUDENT.STATUS = USER_STATUS;
                thisSTUDENT.CLASSNO = CLASS_NO;
                thisSTUDENT.YEAR = GRADE;
                thisSTUDENT.NO = SEAT_NO;
                db.Entry(thisSTUDENT).State = System.Data.Entity.EntityState.Modified;
            }
            else
            {
                Temp_STUDENT temp_STUDENT = new Temp_STUDENT();
                temp_STUDENT.SCHOOL_NO = schoolNo;
                temp_STUDENT.ID = i + 1;
                temp_STUDENT.STDNO = USERNO;
                temp_STUDENT.IDNO = IDNO;
                temp_STUDENT.NAME = TeacherName;
                temp_STUDENT.SEYEAR = stryear.ToString();
                temp_STUDENT.SEX = SEX;
                temp_STUDENT.BIRTHDAY = BIRTHDAY.ToString("yyyyMMdd");
              //  temp_STUDENT.BIRTHDAY = DateTime.Parse(BIRTHDAY).ToString("yyyyMMdd");
                temp_STUDENT.STATUS = USER_STATUS;
                temp_STUDENT.CLASSNO = CLASS_NO;
                temp_STUDENT.YEAR = GRADE;
                temp_STUDENT.NO = SEAT_NO;
                db.Entry(temp_STUDENT).State = System.Data.Entity.EntityState.Added;
            }
            db.SaveChanges();
        };

            #endregion 變更內碼 func

            try
            {
                int index = 1;

                DataTable dtCardNo = excelDs.Tables[0];
                if (dtCardNo != null)
                    foreach (DataRow dr in dtCardNo.Rows)
                        changeCard_No(dr, index++);
            }
            catch (Exception ex)
            {
                dic.Add(false, "");
                R_Message = ex.Message;
                return dic;
            }

            dic.Add(true, SCH);
            return dic;
        }

        public Dictionary<bool, string> ImportTeacherInfo(HttpPostedFileBase excelFiles)
        {
            Dictionary<bool, string> dic = new Dictionary<bool, string>();
            DataSet excelDs;
            npoi.onDataTypeConflict += (s, e) => NPOI_DataTypeConflict(s, e);
            npoi.onLineCheckValue += (s, e) =>
            {
                int index = Array.IndexOf(SheetNames, e.SheetName);
                if (!IsDBNullOrNullEmpty(e.Row["學校代號"]))
                    foreach (string col in SheetMustColumnNames[index])
                        if (IsDBNullOrNullEmpty(e.Row[col]))
                        {
                            HasErr = true;
                            ErrorRowCellExcel = ErrorRowCellExcel + "Sheet名稱【" + e.SheetName + "】-欄位名稱【" + col + "】，第" + e.RowNo.ToString() + "列-必需輸入<br/>";
                        }
            };
            SheetNames = new string[1] { "教師" };
            SheetMustColumnNames = new string[1][] {
                new [] {
                    "學校代號", "帳號(一般同身分證字號也可以不一樣)","教師姓名","身分證字號","性別","在校狀態(1:在校, 2:離校)"
                }
            };

            if (excelFiles == null)
            {
                dic.Add(false, "");
                R_Message = "未選取Excel檔案";
                return dic;
            }
            excelDs = npoi.Excel2Table(excelFiles.InputStream, 0, 1, 0, SheetNames);
            if (IsExcelErrorExist(excelDs))
            {
                dic.Add(false, "");

                return dic;
            }

            #region 變更內碼 func

            string SCH = "";
            Action<DataRow, int> changeCard_No = (dr, index) =>
            {
                if (IsDBNullOrNullEmpty(dr["學校代號"], true))
                    return;
                string schoolNo = Convert.ToString(dr["學校代號"]);
                SCH = schoolNo;

                if (IsDBNullOrNullEmpty(dr["帳號(一般同身分證字號也可以不一樣)"], true))
                    return;
                string USERNO = Convert.ToString(dr["帳號(一般同身分證字號也可以不一樣)"]);
                if (IsDBNullOrNullEmpty(dr["教師姓名"], true))
                    return;
                string TeacherName = Convert.ToString(dr["教師姓名"]);
                if (IsDBNullOrNullEmpty(dr["身分證字號"], true))
                    return;
                string IDNO = Convert.ToString(dr["身分證字號"]);
                if (IsDBNullOrNullEmpty(dr["性別"], true))
                    return;
                string SEX = Convert.ToString(dr["性別"]);

                if (IsDBNullOrNullEmpty(dr["在校狀態(1:在校, 2:離校)"], true))
                    return;
                string USER_STATUS = Convert.ToString(dr["在校狀態(1:在校, 2:離校)"]);
                string Email = "";
                string BIRTHDAY = "";
                string TEL_NO = "";
                if (IsDBNullOrNullEmpty(dr["登入帳號(單一登入帳號) 這格不用寫"], false))
                {
                    Email = Convert.ToString(dr["登入帳號(單一登入帳號) 這格不用寫"]);
                }
                if (IsDBNullOrNullEmpty(dr["生日 這格不用寫"], false))
                {
                    BIRTHDAY = Convert.ToString(dr["生日 這格不用寫"]);
                }
                if (IsDBNullOrNullEmpty(dr["電話   這格不用寫"], false))
                {
                    TEL_NO = Convert.ToString(dr["電話   這格不用寫"]);
                }
                HRMT01 thisHt01 = db.HRMT01.Where(a => a.SCHOOL_NO == schoolNo && a.USER_NO == USERNO).FirstOrDefault();
                Temp_TEABAS thisTEABAS = db.Temp_TEABAS.Where(x => x.SCHOOL_NO == schoolNo && x.IDNO == IDNO).FirstOrDefault();
                int tempCount = 0;
                tempCount = db.Temp_TEABAS.ToList().Count();
                int i = 0;
                if (tempCount > 0)
                {
                    i = db.Temp_TEABAS.Select(X => X.TEAID).Max();
                }

                if (thisTEABAS != null)
                {
                    thisTEABAS.SCHOOL_NO = schoolNo;
                    thisTEABAS.IDNO = IDNO;
                    thisTEABAS.TEANAME = TeacherName;
                    thisTEABAS.TEAMAIL = Email;
                    thisTEABAS.TEASEX = SEX;
                    thisTEABAS.BIRTHDAY = BIRTHDAY;
                    thisTEABAS.ATSCHOOL = USER_STATUS;
                    thisTEABAS.TEAMOBIL = TEL_NO;
                    db.Entry(thisTEABAS).State = System.Data.Entity.EntityState.Modified;
                }
                else
                {
                    Temp_TEABAS temp_TEABAS = new Temp_TEABAS();
                    temp_TEABAS.TEAID = i + 1;
                    temp_TEABAS.SCHOOL_NO = schoolNo;
                    temp_TEABAS.IDNO = IDNO;
                    temp_TEABAS.TEANAME = TeacherName;
                    temp_TEABAS.TEAMAIL = Email;
                    temp_TEABAS.TEASEX = SEX;
                    temp_TEABAS.BIRTHDAY = BIRTHDAY;
                    temp_TEABAS.ATSCHOOL = USER_STATUS;
                    temp_TEABAS.TEAMOBIL = TEL_NO;
                    db.Entry(temp_TEABAS).State = System.Data.Entity.EntityState.Added;
                }
                db.SaveChanges();
            };

            #endregion 變更內碼 func

            try
            {
                int index = 1;

                DataTable dtCardNo = excelDs.Tables[0];
                if (dtCardNo != null)
                    foreach (DataRow dr in dtCardNo.Rows)
                    {
                        changeCard_No(dr, index++);
                    }
                        
            }
            catch (Exception ex)
            {
                dic.Add(false, "");
                R_Message = ex.Message;
                return dic;
            }

            dic.Add(true, SCH);

            return dic;
        }

        /// <summary>
        /// 匯入/變更老師內碼
        /// </summary>
        /// <param name="excelFiles">Excel檔案</param>
        /// <returns></returns>
        ///

        public bool ImportCardNoFromExcelTeacher(HttpPostedFileBase excelFiles, string schoolNo)
        {
            DataSet excelDs;
            npoi.onDataTypeConflict += (s, e) => NPOI_DataTypeConflict(s, e);
            npoi.onLineCheckValue += (s, e) =>
            {
                int index = Array.IndexOf(SheetNames, e.SheetName);
                if (!IsDBNullOrNullEmpty(e.Row["身分證字號"]))
                    foreach (string col in SheetMustColumnNames[index])
                        if (IsDBNullOrNullEmpty(e.Row[col]))
                        {
                            HasErr = true;
                            ErrorRowCellExcel = ErrorRowCellExcel + "Sheet名稱【" + e.SheetName + "】-欄位名稱【" + col + "】，第" + e.RowNo.ToString() + "列-必需輸入<br/>";
                        }
            };

            SheetNames = new string[1] { "373610" };
            SheetMustColumnNames = new string[1][] {
                new [] {
                    "身分證字號",  "卡片內碼"
                }
            };

            if (excelFiles == null)
            {
                R_Message = "未選取Excel檔案";
                return false;
            }
            excelDs = npoi.Excel2Table(excelFiles.InputStream, 0, 1, 0, SheetNames);
            if (IsExcelErrorExist(excelDs)) return false;

            #region 變更內碼 func

            Action<DataRow, int> changeCard_No = (dr, index) =>
            {
                if (IsDBNullOrNullEmpty(dr["身分證字號"], true))
                    return;
                string thisUserNo = Convert.ToString(dr["身分證字號"]);

                HRMT01 thisHt01 = db.HRMT01.Where(a => a.SCHOOL_NO == schoolNo && a.USER_NO == thisUserNo).FirstOrDefault();

                // Modify
                if (thisHt01 != null)
                {
                    thisHt01.CARD_NO = dr["卡片內碼"].ToType<string>();
                    R_ModifyHt01.Add($"列{index}，{thisUserNo} - 匯入成功<br />");
                    db.Entry(thisHt01).State = System.Data.Entity.EntityState.Modified;
                }
                else
                {
                    R_ModifyHt01.Add($"列{index}，{thisUserNo} - 匯入失敗，原因: 找不到該帳號。<br />");
                }
            };

            #endregion 變更內碼 func

            try
            {
                int index = 1;

                DataTable dtCardNo = excelDs.Tables[0];
                if (dtCardNo != null)
                    foreach (DataRow dr in dtCardNo.Rows)
                        changeCard_No(dr, index++);
                db.SaveChanges();
            }
            catch (Exception ex)
            {
                R_Message = ex.Message;
                return false;
            }

            return true;
        }

        #region Shared

        private bool IsExcelErrorExist(DataTable excelDt)
        {
            if (excelDt == null)
            {
                R_Message = @"上傳錯誤，上傳Excel不符合規定，上傳之 Excel 檔, 請依規定格式填寫，請先下載Sample Excel";
                return true;
            }
            if (HasErr)
            {
                R_Message = ErrorRowCellExcel;
                return true;
            }
            return false;
        }

        private bool IsExcelErrorExist(DataSet excelDs)
        {
            if (excelDs == null)
            {
                R_Message = @"上傳錯誤，上傳Excel不符合規定，上傳之 Excel 檔, 請依規定格式填寫，請先下載Sample Excel";
                return true;
            }
            if (excelDs.Tables.Count == 0) /*讀取資料筆數為0*/
            {
                R_Message = @"上傳錯誤，錯誤原因如下:<br><br>讀取資料筆數為0，請確認 上傳Excel的 Sheet Name 是否有以下 ";
                foreach (string ThisSheet in SheetNames)
                {
                    R_Message += "【" + ThisSheet.ToString() + "】";
                }
                R_Message += @" Sheet Name ，請確認";
                return true;
            }
            if (HasErr)
            {
                R_Message = ErrorRowCellExcel;
                return true;
            }
            return false;
        }

        private void NPOI_DataTypeConflict(object s, DataRowCellFilledArgs e)
        {
            CheckDataTypeErr = true;
            ErrorRowCellExcel = ErrorRowCellExcel + "Sheet名稱【" + e.SheetName + "】- EXCEL內容資料型態錯誤,欄位名稱【" + e.ColCaption + "】，第" + e.RowNo.ToString() + "列-儲存格內容為「" + e.CellToString + "」<br/>";
        }

        public static bool IsDBNullOrNullEmpty(object value, bool NoWhiteSpace = false)
        => Convert.IsDBNull(value) ||
            NoWhiteSpace ? string.IsNullOrWhiteSpace(Convert.ToString(value)) : string.IsNullOrEmpty(Convert.ToString(value));

        #endregion Shared

        #region IDisposable 實作

        private bool disposed = false;

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            // Check to see if Dispose has already been called.
            if (!this.disposed)
            {
                if (disposing)
                {
                    db.Dispose();
                    db = null;
                }
                disposed = true;
            }
        }

        ~HrmtImportService()
        {
            Dispose(false);
        }

        #endregion IDisposable 實作
    }
}