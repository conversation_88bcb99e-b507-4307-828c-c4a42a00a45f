/*
 *  /MathJax/jax/output/CommonHTML/fonts/TeX/Main-Bold.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

(function(b){var a="MathJax_Main-Bold";MathJax.Hub.Insert(b.FONTDATA.FONTS[a],{160:[0,0,250,0,0],168:[695,-535,575,96,478],172:[371,-61,767,64,702],175:[607,-540,575,80,494],176:[702,-536,575,160,414],177:[728,35,894,64,829],180:[706,-503,575,236,460],215:[530,28,894,168,726],247:[597,96,894,64,828],305:[452,8,394,24,367],567:[451,201,439,-12,420],710:[694,-520,575,126,448],711:[660,-515,575,130,444],713:[607,-540,575,80,494],714:[706,-503,575,236,460],715:[706,-503,575,114,338],728:[694,-500,575,102,472],729:[695,-525,575,202,372],730:[702,-536,575,160,414],732:[694,-552,575,96,478],768:[706,-503,0,-461,-237],769:[706,-503,0,-339,-115],770:[694,-520,0,-449,-127],771:[694,-552,0,-479,-97],772:[607,-540,0,-495,-81],774:[694,-500,0,-473,-103],775:[695,-525,0,-373,-203],776:[695,-535,0,-479,-97],778:[702,-536,0,-415,-161],779:[714,-511,0,-442,-82],780:[660,-515,0,-445,-131],824:[711,210,0,-734,-161],8194:[0,0,500,0,0],8195:[0,0,999,0,0],8196:[0,0,333,0,0],8197:[0,0,250,0,0],8198:[0,0,167,0,0],8201:[0,0,167,0,0],8202:[0,0,83,0,0],8211:[300,-249,575,0,574],8212:[300,-249,1150,0,1149],8216:[694,-329,319,58,245],8217:[694,-329,319,74,261],8220:[694,-329,603,110,564],8221:[694,-329,603,38,492],8224:[702,211,511,64,446],8225:[702,202,511,64,446],8230:[171,-1,1295,74,1221],8242:[563,-33,344,35,331],8407:[723,-513,0,-542,-33],8463:[694,8,668,45,642],8465:[702,8,831,64,798],8467:[702,19,474,-1,446],8472:[461,210,740,72,726],8476:[711,16,831,42,824],8501:[694,0,703,64,638],8592:[518,17,1150,64,1084],8593:[694,193,575,14,561],8594:[518,17,1150,65,1085],8595:[694,194,575,14,561],8596:[518,17,1150,64,1085],8597:[767,267,575,14,561],8598:[724,194,1150,64,1084],8599:[724,193,1150,64,1085],8600:[694,224,1150,65,1085],8601:[694,224,1150,64,1085],8614:[518,17,1150,65,1085],8617:[518,17,1282,64,1218],8618:[518,17,1282,65,1217],8636:[518,-220,1150,64,1084],8637:[281,17,1150,64,1084],8640:[518,-220,1150,65,1085],8641:[281,17,1150,64,1085],8652:[718,17,1150,64,1085],8656:[547,46,1150,64,1085],8657:[694,193,703,30,672],8658:[547,46,1150,64,1084],8659:[694,194,703,30,672],8660:[547,46,1150,47,1102],8661:[767,267,703,30,672],8704:[694,16,639,1,640],8706:[710,17,628,60,657],8707:[694,-1,639,64,574],8709:[767,73,575,46,528],8711:[686,24,958,56,901],8712:[587,86,767,97,670],8713:[711,210,767,97,670],8715:[587,86,767,96,670],8722:[281,-221,894,96,797],8723:[537,227,894,64,829],8725:[750,250,575,63,511],8726:[750,250,575,63,511],8727:[472,-28,575,73,501],8728:[474,-28,575,64,510],8729:[474,-28,575,64,510],8730:[820,180,958,78,988],8733:[451,8,894,65,830],8734:[452,8,1150,65,1084],8736:[714,0,722,55,676],8739:[750,249,319,129,190],8741:[750,248,575,145,430],8743:[604,17,767,64,702],8744:[604,16,767,64,702],8745:[603,16,767,64,702],8746:[604,16,767,64,702],8747:[711,211,569,64,632],8764:[391,-109,894,64,828],8768:[583,82,319,64,254],8771:[502,3,894,64,829],8773:[638,27,1000,64,829],8776:[524,-32,894,64,829],8781:[533,32,894,64,829],8784:[721,-109,894,64,829],8800:[711,210,894,64,829],8801:[505,3,894,64,829],8804:[697,199,894,96,797],8805:[697,199,894,96,797],8810:[617,116,1150,64,1085],8811:[618,116,1150,64,1085],8826:[585,86,894,96,797],8827:[586,86,894,96,797],8834:[587,85,894,96,797],8835:[587,86,894,96,796],8838:[697,199,894,96,797],8839:[697,199,894,96,796],8846:[604,16,767,64,702],8849:[697,199,894,96,828],8850:[697,199,894,66,797],8851:[604,-1,767,70,696],8852:[604,-1,767,70,696],8853:[632,132,894,64,828],8854:[632,132,894,64,828],8855:[632,132,894,64,828],8856:[632,132,894,64,828],8857:[632,132,894,64,828],8866:[693,-1,703,65,637],8867:[693,-1,703,64,638],8868:[694,-1,894,64,829],8869:[693,-1,894,65,829],8872:[750,249,974,129,918],8900:[523,21,575,15,560],8901:[336,-166,319,74,245],8902:[502,0,575,24,550],8904:[540,39,1000,33,967],8942:[951,29,319,74,245],8943:[336,-166,1295,74,1221],8945:[871,-101,1323,129,1194],8968:[750,248,511,194,493],8969:[750,248,511,17,317],8970:[749,248,511,194,493],8971:[749,248,511,17,317],8994:[405,-108,1150,65,1084],8995:[392,-126,1150,64,1085],9651:[711,-1,1022,69,953],9657:[540,39,575,33,542],9661:[500,210,1022,68,953],9667:[539,38,575,33,542],9711:[711,211,1150,65,1084],9824:[719,129,894,64,829],9825:[711,24,894,65,828],9826:[719,154,894,64,828],9827:[719,129,894,32,861],9837:[750,17,447,64,381],9838:[741,223,447,57,389],9839:[724,224,447,63,382],10216:[750,249,447,127,382],10217:[750,249,447,64,319],10229:[518,17,1805,64,1741],10230:[518,17,1833,96,1773],10231:[518,17,2126,64,2061],10232:[547,46,1868,64,1804],10233:[547,46,1870,64,1804],10234:[547,46,2126,64,2060],10236:[518,17,1833,65,1773],10815:[686,0,900,39,860],10927:[696,199,894,96,797],10928:[697,199,894,96,797]});b.fontLoaded("TeX/"+a.substr(8))})(MathJax.OutputJax.CommonHTML);
