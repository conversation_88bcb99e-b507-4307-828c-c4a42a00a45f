/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/SizeFiveSym/Regular/All.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.STIXSizeFiveSym,{710:[816,-572,2328,0,2328],711:[816,-572,2328,0,2328],732:[780,-617,2328,0,2328],759:[-117,280,2328,0,2328],773:[820,-770,0,-3000,0],780:[816,-572,0,-2485,-157],816:[-117,280,0,-2485,-157],818:[-127,177,0,-3000,0],824:[960,454,0,-561,-123],8254:[820,-770,3000,0,3000],8400:[749,-584,0,-3000,0],8401:[749,-584,0,-3000,0],8406:[735,-482,0,-3000,0],8407:[735,-482,0,-3000,0],8428:[-123,288,0,-3000,0],8429:[-123,288,0,-3000,0],8430:[-26,279,0,-3000,0],8431:[-26,279,0,-3000,0],9140:[766,-544,3237,90,3147],9141:[139,83,3237,90,3147],9180:[80,189,3237,0,3237],9181:[842,-573,3237,0,3237],9184:[66,212,3164,0,3164],9185:[842,-564,3164,0,3164]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/SizeFiveSym/Regular/All.js");
