﻿@{
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    string SchoolNO = EcoolWeb.Models.UserProfileHelper.GetSchoolNo();
    string SchoolName = EcoolWeb.Models.UserProfileHelper.GetSchoolSName();

    string LogoAct = "GuestIndex";
    if (user != null)
    {
        if (user.USER_TYPE == UserType.Student)
        { LogoAct = "StudentIndex"; }
        else
        { LogoAct = "TeacherIndex"; }

    }

    EcoolWeb.Models.UserProfileHelper.SetLayoutCookieData("~/Views/Shared/_LayoutSEO.cshtml");

    string DateNowStr = DateTime.Now.ToString("yyyy_MM_dd_H_mm_ss");

}
<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@ViewBag.Title</title>
    @Styles.Render("~/Content/css")
    <link href="~/Content/css/EzCss.css?@DateNowStr" rel="stylesheet" />
    <link href="~/Content/font-awesome/css/font-awesome.min.css" rel="stylesheet" />
    @Scripts.Render("~/bundles/modernizr")
    @Scripts.Render("~/bundles/jquery")
    @Scripts.Render("~/bundles/bootstrap")
    @RenderSection("css", required: false)

    @{ Html.RenderPartial("_GoogleAnalytics"); }
</head>
@Html.Partial("../Shared/_CheckBrowser")
<body>
    <div id="LayoutTOP"></div>
    <div class="visible-xs">
        <!---手機 -->
        <nav class="navbar navbar-phone navbar-fixed-top" role="navigation">

            <div class="navbar-header pull-left">
                <button type="button" class="btn btn-navbar" data-toggle="collapse" data-target=".navbar-ex1-collapse" title="Menu">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>

                <a class="line-left"></a>

                <a href='@Url.Action("PortalIndex", "Home")' class="btn-logo-layout">
                    <img src="~/Content/img/web-student_png-16.png" class="img-responsive " alt="Responsive image" title="回台北e酷幣首頁" />
                </a>

                <a class="line-left"></a>

                @if (string.IsNullOrWhiteSpace(SchoolNO) == false)
                {
                    <a class="btn-logo-school" href='@Url.Action(LogoAct, "Home", new { school = SchoolNO })' title="回學校首頁">
                        <img src='@Url.Action("BDMT01IMG", "Content", new { school = SchoolNO + ".png" })' />
                    </a>

                    <a class="btn-font-school" href='@Url.Action("GuestIndex", "Home", new {school= SchoolNO })' title="回學校首頁">
                        @SchoolName
                    </a>
                }
            </div>

            @if (user == null)
            {
                <a role="button" class="btn-User" href="@Url.Action("LoginPage","Home")">
                    <i class="fa fa-user" style="font-size:2.5em" title="會員登入"></i>
                </a>
            }
            else
            {
                <a role="button" class="btn-User" href="@Url.Action("Logout","Home")">
                    <i class="fa fa-power-off" style="font-size:2.5em" title="登出"></i>
                </a>
            }
            <a class="line-right"></a>
        </nav>
    </div>

    <div class="hidden-xs">
        <!---PC -->
        <nav class="navbar navbar-inverse navbar-fixed-top" role="navigation">
            <!-- Brand and toggle get grouped for better mobile display -->
            <div class="navbar-header" style="height:87px;width: 100%; background-image: url('@Url.Content("~/Content/img/web-16.png")');background-repeat:repeat-x;">
                <span class="navbar-brandEZ">@Html.Partial("../Shared/_Navbar")</span>
            </div>
        </nav>
    </div>

    <div style="width:auto;height:80px"></div>

    <div class="containerEZ">
        <br>
        <div class="row">
            <div class="col-lg-2 col-md-2 col-sm-3 col-xs-12  Div-Menu" id="sidebar">
                @Html.Action("PermissionMenu", "Menu")
            </div>
            <div class="col-lg-10 col-md-10 col-sm-9 col-xs-12" id="container">
                @RenderBody()
            </div>
        </div>

        @if (System.Web.Configuration.WebConfigurationManager.AppSettings["TestMode"] != "true")
        {
            <div style="width:auto;height:25px"></div>
            <FOOTER>
                @Html.Action("_Footer", "Home")
            </FOOTER>
        }
    </div>

    @RenderSection("scripts", required: false)

    @if (TempData["ErrMessage"] != null)
    {
        <script>
            window.alert('@TempData["ErrMessage"]');
        </script>
    }
    <script type="text/javascript">
        window.history.forward(1);
    </script>
</body>
</html>