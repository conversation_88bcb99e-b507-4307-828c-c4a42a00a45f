﻿  body	{
        margin: 0 10px;
        }

    body	{
        background-image: url('../img/web-01.png');
	    background-repeat:repeat;
	    margin-top: 0px;
        }



/* 中螢幕設備（一般桌面，992px（含）以上） */
@media (max-width: 1200px) { 

      #container	{
        max-width: 100%;
	    margin: auto;
        }

        #header {
            width: 100%;
            height: 100px;
            background-image: url('../img/web-16.png');
            background-repeat: repeat-x ;

            
        }

}

/* 大螢幕設備（大型桌面，1200px（含）以上） */
@media (min-width: 1200px) {

          #container	{
        max-width: 1200px;
	    margin: auto;
        }

        #header {
            max-width: 1200px;
            height: 100px;
            background-image: url('../img/web-16.png');
            background-repeat: repeat-x ;
        }

}


    #site	{
        padding: 5px 10px 10px;
        position:relative;
        display: inline-block;
    }

     #student_img	{
        padding: 5px 10px 10px;
            display: inline-block;
          
      
    }

     #student_name	{
            font-family: '<PERSON><PERSON>', Helvetica, Arial, sans-serif;
            padding: 5px 10px 10px;
            display: inline-block;
            margin: 0;
            line-height: 1;
            font-weight: normal;
            font-size: 16pt;
            font-weight: bold;
            height: 100px;
            margin: 1em;
            color: #004DA0;
          
    }

      #student_name a {
            text-decoration: none;
      }

      * {
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
}