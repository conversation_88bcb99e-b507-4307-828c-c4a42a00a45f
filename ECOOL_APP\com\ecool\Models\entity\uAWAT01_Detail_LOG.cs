﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.com.ecool.Models.entity
{
    public class uAWAT01_Detail_LOG
    {
        public Nullable<int> SYEAR { get; set; }
        public Nullable<byte> SEMESTER { get; set; }
        public string SCHOOL_NO { get; set; }
        public string CLASS_NO { get; set; }
        public string SEAT_NO { get; set; }
        public string USERNAME { get; set; }
        public string SNAME { get; set; }
        public string USER_NO { get; set; }
        public string ORGINALSOUCETYPE { get; set; }
        public string USER_TYPE { get; set; }
        public string SOURCE_TYPE { get; set; }
        public string SOURCE_NO { get; set; }
        public Nullable<short> CASH_IN { get; set; }
        public System.DateTime LOG_TIME { get; set; }
        public string LOG_DESC { get; set; }
        public string CHART_DESC { get; set; }
        public string SOURCE_TYPEEdite { get; set; }
        public string LOG_DESCDetail { get; set; }
        public string LOG_PERSON { get; set; }

        public string LOG_PERSON_NAME { get; set; }
        public string LOG_TABLE { get; set; }
        public Nullable<int> AWAT01_CASH_AVAILABLE { get; set; }
    }
}