﻿using DotNet.Highcharts;
using DotNet.Highcharts.Enums;
using DotNet.Highcharts.Helpers;
using DotNet.Highcharts.Options;
using ECOOL_APP;
using ECOOL_APP.com.ecool.Models.DTO;
using ECOOL_APP.com.ecool.util;
using ECOOL_APP.EF;
using EcoolWeb.CustomAttribute;
using EcoolWeb.Models;
using EcoolWeb.Util;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using ECOOL_APP.com.ecool.service;
using MvcPaging;
using Dapper;
using ECOOL_APP.com.ecool.LogicCenter;

namespace EcoolWeb.Controllers
{
    [SessionExpire]
    public class SECI01Controller : Controller
    {
        /// <summary>
        /// 此Controller 對應的程式代碼
        /// </summary>
        private string Bre_NO = "SECI01";
        private string Bre_Name = "";
        /// <summary>
        /// 會員相關資訊
        /// </summary>
        private UserProfile user;

        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();

        private SECI01Service Service = new SECI01Service();
        private SECSharedService SharedService = new SECSharedService();

        /// <summary>
        /// 定義要取幾月份資料(前6個月)
        /// </summary>
        private int SetMonth = -6;
        public ActionResult Index2() {

            Bre_Name = "查詢學生秘書";
            user = UserProfileHelper.Get();
            return View();
        }
        public ActionResult _PageContent(CER002IndexViewModel model)
        {

            user = UserProfileHelper.Get();
            Bre_Name = "全校護照";
            if (model == null) {
                model = new CER002IndexViewModel();
    }

            if (model.ListDataHRMT01 == null)
            {
                model.ListDataHRMT01 = new ZZZI19IndexViewModel();


}
             
            if (!string.IsNullOrEmpty(model.ListDataHRMT01.whereKeyword)) {
                model.ListDataHRMT01.whereKeyword = model.ListDataHRMT01.whereKeyword;

            }
            if (!string.IsNullOrEmpty(model.ListDataHRMT01.whereGrade))
            {
                model.ListDataHRMT01.whereGrade = model.ListDataHRMT01.whereGrade;

            }
            if (!string.IsNullOrEmpty(model.ListDataHRMT01.whereClass_No))
            {
                model.ListDataHRMT01.whereClass_No = model.ListDataHRMT01.whereClass_No;

            }
            if (!string.IsNullOrEmpty(model.ListDataHRMT01.OrdercColumn))
            {
                model.ListDataHRMT01.OrdercColumn = model.ListDataHRMT01.OrdercColumn;

            }
            ECOOL_DEVEntities db2 = new ECOOL_DEVEntities();
string sSQL = $@"select * from ufnGetApLog(@SCHOOL_NO)";
IQueryable<ZZZI19Hrmt01ViewModel> HRMT01List = db2.Database.Connection.Query<ZZZI19Hrmt01ViewModel>(sSQL
, new
{
    SCHOOL_NO = user.SCHOOL_NO,
}).AsQueryable();
        
          // ViewBag.AccreditationTypeItems = eRI01Service.GetSelectAccreditationTypeItems(model?.WhereACCREDITATION_TYPE, SCHOOL_NO, ref db, "全部");
           // ViewBag.AccreditationsItems = cERI02Service.GetSelectAccreditationsItems(model.WhereACCREDITATION_NAME, SCHOOL_NO, ref db, "全部");
            if (!string.IsNullOrEmpty(user?.TEACH_CLASS_NO)) {

                model.ListDataHRMT01.whereClass_No = user?.TEACH_CLASS_NO;

            }
            if (string.IsNullOrWhiteSpace(model.ListDataHRMT01.whereKeyword) == false)
            {
                HRMT01List = HRMT01List.Where(a => a.USER_NO.Contains(model.ListDataHRMT01.whereKeyword.Trim()) || a.NAME.Contains(model.ListDataHRMT01.whereKeyword.Trim()));
            }

            //if (string.IsNullOrWhiteSpace(user.TEACH_CLASS_NO) == false)
            //{
            //    model.whereClass_No = user.TEACH_CLASS_NO;
            //}

            if (string.IsNullOrWhiteSpace(model.ListDataHRMT01.whereClass_No) == false)
            {
                HRMT01List = HRMT01List.Where(a => a.CLASS_NO == model.ListDataHRMT01.whereClass_No);
            }

            if (string.IsNullOrWhiteSpace(model.ListDataHRMT01.whereGrade) == false)
            {
                byte bGrade = Convert.ToByte(model.ListDataHRMT01.whereGrade);
string GradeString = "";
GradeString = bGrade.ToString();
                HRMT01List = HRMT01List.Where(a => a.GRADE == GradeString);
            }
            switch (model.OrderByColumnName)
            {
                case "USER_NO":
                    HRMT01List = HRMT01List.OrderBy(a => a.USER_NO);
                    break;
                case "GRADE":
                    HRMT01List = HRMT01List.OrderBy(a => a.GRADE);
                    break;
                case "CLASS_NO":
                    HRMT01List = HRMT01List.OrderBy(a => a.CLASS_NO);
                    break;
                case "SEAT_NO":
                    HRMT01List = HRMT01List.OrderBy(a => a.SEAT_NO);
                    break;
                default:
                    HRMT01List = HRMT01List.OrderBy(a => a.GRADE).ThenBy(a => a.CLASS_NO).ThenBy(a => a.SEAT_NO);
                    break;
            }
           
            ViewBag.GradeItem = HRMT01.GetGradeItems(model.ListDataHRMT01.whereGrade);
            ViewBag.ClassItems = HRMT01.GetClassListData(user.SCHOOL_NO, model.ListDataHRMT01.whereGrade, ref db2)
         .Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.ListDataHRMT01.whereClass_No });
            int iPageCount = (model.ListDataHRMT01.ShowPageCount != null) ? Convert.ToInt32(model.ListDataHRMT01.ShowPageCount) : 100;
model.ListDataHRMT01.HRMT01List = HRMT01List.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, iPageCount);
var ClassNoItem = HRMT01.GetClassListData(user.SCHOOL_NO, model.ListDataHRMT01.whereGrade, model.ListDataHRMT01.whereClass_No, ref db2);

            if (!string.IsNullOrWhiteSpace(user.TEACH_CLASS_NO) && user.ROLE_TYPE == HRMT24_ENUM.RoleTypeVal.SchoolLevel)
            {
                ClassNoItem = ClassNoItem.Where(a => a.Value == user.TEACH_CLASS_NO).ToList();
            }
            ViewBag.ClassNoItem = ClassNoItem;

            //年級
            ViewBag.GradeItem = HRMT01.GetGradeItems(model.ListDataHRMT01.whereGrade);

            //狀態
         //   ViewBag.StatusItem = UserStaus.GetUserStausItemsALL(model.whereStatus);

            List<SelectListItem> PageCountItem = new List<SelectListItem>();
PageCountItem.Add(new SelectListItem() { Text = "100", Value = "100", Selected = iPageCount.ToString() == "100" });
            PageCountItem.Add(new SelectListItem() { Text = "200", Value = "200", Selected = iPageCount.ToString() == "200" });
            PageCountItem.Add(new SelectListItem() { Text = "500", Value = "500", Selected = iPageCount.ToString() == "500" });
            ViewBag.PageCount = PageCountItem;
            return PartialView(model);
        
      }
        [CheckPermission] //檢查權限
        public ActionResult Index(string PRINT)
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "我的秘書";

            user = UserProfileHelper.Get();

            if (user == null)
            {
                return RedirectToAction("SessionTimeOutError", "Error");
            }

            SECSharedSearchViewModel Data = new SECSharedSearchViewModel();
            Data.SCHOOL_NO = user.SCHOOL_NO;
            Data.USER_NO = user.USER_NO;
            Data.CLASS_NO = user.TEACH_CLASS_NO;
            Data.DATA_ANGLE_TYPE = UserProfileHelper.GetDataAngleType(user);
            Data.PRINT = PRINT;

            return View(Data);
        }

        public string GetDATA_TYPE_NAME(string wDATA_ANGLE_TYPE, string wCLASS_NO)
        {
            if (wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.ClassData)
            {
                return "(" + wCLASS_NO + "班)";
            }
            else if (wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.SchoolData)
            {
                return "(本校)";
            }
            else if (wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.MyChildData)
            {
                return "(我的寶貝)";
            }
            else
            {
                return "";
            }
        }

        /// <summary>
        ///  個人資訊
        /// </summary>
        /// <param name="wSCHOOL_NO"></param>
        /// <param name="wUSER_NO"></param>
        /// <param name="ShowType"></param>
        /// <returns></returns>
        public ActionResult _PersonalDiv(string wSCHOOL_NO, string wUSER_NO, string wCLASS_NO, string wDATA_ANGLE_TYPE, string wREF_BRE_NO, string wPRINT)
        {
            if (string.IsNullOrWhiteSpace(wSCHOOL_NO) || string.IsNullOrWhiteSpace(wUSER_NO))
            {
                return RedirectToAction("NotParameterError", "Error");
            }

            SECI01IndexViewModel Data = new SECI01IndexViewModel();

            HRMT01 thisUser = db.HRMT01.Where(a => a.SCHOOL_NO == wSCHOOL_NO && a.USER_NO == wUSER_NO).FirstOrDefault();

            if (thisUser == null)
            {
                return RedirectToAction("NotFindError", "Error", new { error = "無此帳號資料，請確認(SCHOOL_NO:" + wSCHOOL_NO + "、USER_NO:" + wUSER_NO + ")" });
            }

            Data.DATA_ANGLE_TYPE = wDATA_ANGLE_TYPE;
            Data.DATA_TYPE_NAME = GetDATA_TYPE_NAME(wDATA_ANGLE_TYPE, wCLASS_NO);
            Data.wREF_BRE_NO = wREF_BRE_NO;
            Data.wUSER_NO = wUSER_NO;
            Data.wPRINT = wPRINT;
            Data.NAME = thisUser.NAME;

            Data.wIsQhisSchool = (db.HRMT01.Where(a => a.IDNO == thisUser.IDNO && a.USER_STATUS == UserStaus.Invalid).Count() > 0) ? true : false;

            user = EcoolWeb.Models.UserProfileHelper.Get();

            string Message = string.Empty;
            ViewBag.IsUseZZZI09 = new SECSharedService().IsUseZZZI09(user, ref db, ref Message);

            //個人酷幣點數
            if (thisUser.USER_TYPE == UserType.Student)
            {
                //學生
                AWAT01 tCASH = db.AWAT01.Where(a => a.SCHOOL_NO == thisUser.SCHOOL_NO && a.USER_NO == thisUser.USER_NO).FirstOrDefault();

                if (tCASH != null)
                {
                    Data.CASH_ALL = (tCASH.CASH_ALL != null) ? tCASH.CASH_ALL : 0;
                    Data.CASH_AVAILABLE = (tCASH.CASH_AVAILABLE != null) ? tCASH.CASH_AVAILABLE : 0;
                }
                else
                {
                    Data.CASH_ALL = 0;
                    Data.CASH_AVAILABLE = 0;
                }

                int SYear;
                int Semesters;
                SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);
                List<DB2_L_WORK2> BookWorks = db.DB2_L_WORK2.Where(a => a.SCHOOL_NO == thisUser.SCHOOL_NO && a.NO_READ == thisUser.IDNO && a.SEYEAR == SYear.ToString() && a.SESEM == Semesters.ToString()).ToList();
                if (BookWorks != null)
                {
                    Data.BOOKS = BookWorks.Sum(a => a.QTY);
                    string YYYYMM = DateTime.Today.ToString("yyyyMM");
                    DB2_L_WORK2 mm = BookWorks.Where(a => a.RET_YYMM == YYYYMM).FirstOrDefault();
                    if (mm != null)
                        Data.BOOKS_MONTH = mm.QTY;
                    else
                        Data.BOOKS_MONTH = 0;
                }
                //DB2_SCH_STATIC SCH_STATIC = db.DB2_SCH_STATIC.Where(a => a.SCHOOL_NO == wSCHOOL_NO && a.USER_NO == wUSER_NO && a.SYEAR == SYear && a.SEMESTER == Semesters).FirstOrDefault();

                //if (SCH_STATIC!=null)
                //{
                //    Data.BOOKS = SCH_STATIC.BOOKS;
                //}
                // CustomerName == null ? n.CustomerName == n.CustomerName : n.CustomerName == CustomerName.ToString())

                //閱讀等級
                ADDT09 a9 = db.ADDT09.Where(user => user.SCHOOL_NO == wSCHOOL_NO && user.USER_NO == wUSER_NO).FirstOrDefault();
                if (a9 != null)
                {
                    Data.ReadLEVEL = Convert.ToInt16(a9.LEVEL_ID);
                }
                else
                {
                    Data.ReadLEVEL = 0;
                }

                //閱讀認證.等級
                Data.PassportLEVEL = db.ADDT04.Where(p => p.USER_NO == wUSER_NO && p.SCHOOL_NO == wSCHOOL_NO && p.PASS_YN == "Y").Count();

                //閱讀等級圖示
                Data.ReadImgURL = UserProfileHelper.GetSECImgReadLEVEL((Byte)Data.ReadLEVEL);

                //「認證」等級圖示
                Data.PassportImgURL = UserProfileHelper.GetSECImgPassportLEVEL(UserProfile.GetOenUseADDT04toShort(wUSER_NO, wSCHOOL_NO, ref db));

                //我可兌換獎品 TOP 3 筆
                Data.AWAT02List = db.AWAT02.Where(a => (a.SCHOOL_NO == thisUser.SCHOOL_NO || a.SCHOOL_NO == "ALL") && a.AWARD_STATUS != "N" && a.QTY_STORAGE > 0
                                 && a.SDATETIME <= DateTime.Today && a.EDATETIME >= DateTime.Today && a.COST_CASH <= Data.CASH_AVAILABLE
                                 && (a.READ_LEVEL == null || a.READ_LEVEL <= Data.ReadLEVEL)
                                 && (a.PASSPORT_LEVEL == null || a.PASSPORT_LEVEL <= Data.PassportLEVEL) && a.FULLSCREEN_YN !="Y"
                                 )
                                .OrderByDescending(a => a.COST_CASH).Take(3).ToList();

                string sSQL = @"SELECT TOP 3 A.*
                FROM AWAT02 A (NOLOCK)
                WHERE (A.SCHOOL_NO=@SCHOOL_NO OR A.SCHOOL_NO='ALL')
                AND A.AWARD_STATUS<>'N' AND A.QTY_STORAGE>0 AND A.FULLSCREEN_YN <>'Y'
                AND A.SDATETIME <= GETDATE()
                AND A.EDATETIME >= GETDATE()
                AND A.COST_CASH <= @CASH_AVAILABLE
                AND (A.READ_LEVEL IS  NULL OR A.READ_LEVEL<=@LEVEL_ID)
                AND (A.PASSPORT_LEVEL IS  NULL OR A.PASSPORT_LEVEL<=@PassportLEVEL)
                AND (ISNULL(A.BUY_PERSON_YN,'0')='0' OR (ISNULL(A.BUY_PERSON_YN,'0')='1'
                AND (SELECT COUNT(*) FROM REFT01 B (NOLOCK) WHERE B.REF_KEY=A.AWARD_NO AND B.REF_TABLE='AWAT02' AND B.SCHOOL_NO=@SCHOOL_NO AND B.USER_NO=@USER_NO)>0
                ))
                ORDER BY A.COST_CASH DESC";
                var QTemp = db.Database.Connection.Query<AWAT02>(sSQL
                    , new
                    {
                        SCHOOL_NO = thisUser.SCHOOL_NO,
                        CASH_AVAILABLE = Data.CASH_AVAILABLE,
                        LEVEL_ID = Data.ReadLEVEL,
                        PassportLEVEL = Data.PassportLEVEL,
                        USER_NO = wUSER_NO
                    });

                //我可兌換獎品 TOP 3 筆
                Data.AWAT02List = QTemp.ToList();

                //兌換獎品系統路徑
                ViewBag.SysAwardPath = AWAI01Controller.GetImagePathUrl(AWAI01SearchViewModel.SouTableVal.Student);

                //QAT02Qty
                ViewBag.QAT02Qty = db.QAT02.Where(q2 => q2.A_USER_NO == wUSER_NO && q2.A_STATUS != "2").Count();
            }
            else
            {
                //老師
                AWAT08 tCASH = db.AWAT08.Where(a => a.SCHOOL_NO == thisUser.SCHOOL_NO && a.USER_NO == thisUser.USER_NO).FirstOrDefault();

                if (tCASH != null)
                {
                    Data.CASH_ALL = (tCASH.CASH_ALL != null) ? tCASH.CASH_ALL : 0;
                    Data.CASH_AVAILABLE = (tCASH.CASH_AVAILABLE != null) ? tCASH.CASH_AVAILABLE : 0;
                }
                else
                {
                    Data.CASH_ALL = 0;
                    Data.CASH_AVAILABLE = 0;
                }

                //我可兌換獎品 TOP 3 筆
                Data.AWAT09List = db.AWAT09.Where(a => a.SCHOOL_NO == thisUser.SCHOOL_NO && a.QTY_STORAGE > 0
                                 && a.SDATETIME <= DateTime.Today.Date && a.EDATETIME >= DateTime.Today.Date && a.COST_CASH <= Data.CASH_AVAILABLE)
                                .OrderByDescending(a => a.COST_CASH).Take(3).ToList();

                //兌換獎品系統路徑
                ViewBag.SysAwardPath = AWAI01Controller.GetImagePathUrl(AWAI01SearchViewModel.SouTableVal.Teacher);

                //QAT02Qty
                ViewBag.QAT02Qty = 0;

                //老師本月已發放特殊點數
                string ErrMsg;
                short ThisMonthCash;
                string LimitShow = string.Empty;

                int CashLimit = ECOOL_APP.UserProfile.GetCashLimit(user.SCHOOL_NO, user.USER_NO, user.USER_TYPE, ref db, out ThisMonthCash, out ErrMsg);
                if (CashLimit != short.MaxValue && CashLimit != 0)
                {
                    LimitShow = "您本月有" + CashLimit + "點酷幣可以發給特殊表現的孩子，你已經發放了" + ThisMonthCash.ToString() + "點。";
                }
                Data.Special__Cash_Limit = LimitShow;

                //老師本月已發放酷幣點數
                int nowYear = DateTime.Now.Year;
                int nowMonth = DateTime.Now.Month;
                var groupAWAT01_LOG = from al in db.AWAT01_LOG
                                      join h01 in db.HRMT01 on new { al.USER_NO, al.SCHOOL_NO } equals new { h01.USER_NO, h01.SCHOOL_NO } into alh
                                      from h01 in alh
                                      where al.CASH_IN > 0
                                        && al.LOG_TIME.Year == nowYear
                                        && al.LOG_TIME.Month == nowMonth
                                        && h01.USER_TYPE == UserType.Student
                                      group al by new { al.LOG_PERSON } into g
                                      select new { LOG_PERSON = g.Key.LOG_PERSON, ADD_CASH_ALL = g.Sum(d => d.ADD_CASH_ALL) };
                var HRMT01List = from h in db.HRMT01
                                 where h.SCHOOL_NO == wSCHOOL_NO && h.USER_NO == user.USER_NO
                                 && (!UserStaus.NGKeyinUserStausList.Contains(h.USER_STATUS))
                                 join a in groupAWAT01_LOG on h.USER_KEY equals a.LOG_PERSON into ha
                                 from a in ha.DefaultIfEmpty()
                                 select new
                                 {
                                     USE_CASH = a.ADD_CASH_ALL ?? 0
                                 };
                Data.Month_Given_Cash = HRMT01List.FirstOrDefault().USE_CASH;
            }

            //角色娃娃
            if (!string.IsNullOrWhiteSpace(thisUser.PHOTO))
            {
                Data.PlayerUrl = Service.GetDirectorySysMyPhotoPath(thisUser.SCHOOL_NO, thisUser.USER_NO, thisUser.PHOTO);
            }
            else
            {
                Data.PlayerUrl = UserProfile.GetPlayerUrl(ref db, thisUser.SCHOOL_NO, thisUser.USER_NO, thisUser.SEX, thisUser.USER_TYPE);
            }

            SetStatisticalData(wSCHOOL_NO, wUSER_NO, wCLASS_NO, wDATA_ANGLE_TYPE, ref Data);

            return PartialView(Data);
        }
        public ActionResult StudentIndex(string wSCHOOL_NO, string wUSER_NO, string wCLASS_NO, string wDATA_ANGLE_TYPE, string wREF_BRE_NO, string wPRINT)
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "我的秘書";

            user = UserProfileHelper.Get();

            if (user == null)
            {
                return RedirectToAction("SessionTimeOutError", "Error");
            }

            SECSharedSearchViewModel Data = new SECSharedSearchViewModel();
            Data.SCHOOL_NO = wSCHOOL_NO;
            Data.USER_NO = wUSER_NO;
            HRMT01 hrt = db.HRMT01.Where(x => x.SCHOOL_NO == wSCHOOL_NO && x.USER_NO == wUSER_NO && x.USER_STATUS == UserStaus.Enabled).FirstOrDefault();
            Data.CLASS_NO = hrt.CLASS_NO;
            Data.DATA_ANGLE_TYPE = "OneData";
            Data.PRINT = wPRINT;

            return View(Data);
        }

            /// <summary>
            ///  個人資訊
            /// </summary>
            /// <param name="wSCHOOL_NO"></param>
            /// <param name="wUSER_NO"></param>
            /// <param name="ShowType"></param>
            /// <returns></returns>
            public ActionResult _PersonalDiv1(string wSCHOOL_NO, string wUSER_NO, string wCLASS_NO, string wDATA_ANGLE_TYPE, string wREF_BRE_NO, string wPRINT)
        {
            if (string.IsNullOrWhiteSpace(wSCHOOL_NO) || string.IsNullOrWhiteSpace(wUSER_NO))
            {
                return RedirectToAction("NotParameterError", "Error");
            }
            string TeachTecherName = "";
            SECI01IndexViewModel Data = new SECI01IndexViewModel();
            
            HRMT01 thisUser = db.HRMT01.Where(a => a.SCHOOL_NO == wSCHOOL_NO && a.USER_NO == wUSER_NO).FirstOrDefault();
       
            if (thisUser == null)
            {
                return RedirectToAction("NotFindError", "Error", new { error = "無此帳號資料，請確認(SCHOOL_NO:" + wSCHOOL_NO + "、USER_NO:" + wUSER_NO + ")" });
            }

            Data.DATA_ANGLE_TYPE = wDATA_ANGLE_TYPE;
            Data.DATA_TYPE_NAME = GetDATA_TYPE_NAME(wDATA_ANGLE_TYPE, wCLASS_NO);
            Data.wREF_BRE_NO = wREF_BRE_NO;
            Data.wUSER_NO = wUSER_NO;
            Data.wPRINT = wPRINT;
            Data.NAME = thisUser.NAME;

            Data.wIsQhisSchool = (db.HRMT01.Where(a => a.IDNO == thisUser.IDNO ).Count() > 0) ? true : false;

            user = EcoolWeb.Models.UserProfileHelper.Get();

            string Message = string.Empty;
            ViewBag.IsUseZZZI09 = new SECSharedService().IsUseZZZI09(user, ref db, ref Message);

            //個人酷幣點數
            if (thisUser.USER_TYPE == UserType.Student)
            {
                //學生
                AWAT01 tCASH = db.AWAT01.Where(a => a.SCHOOL_NO == thisUser.SCHOOL_NO && a.USER_NO == thisUser.USER_NO).FirstOrDefault();

                if (tCASH != null)
                {
                    Data.CASH_ALL = (tCASH.CASH_ALL != null) ? tCASH.CASH_ALL : 0;
                    Data.CASH_AVAILABLE = (tCASH.CASH_AVAILABLE != null) ? tCASH.CASH_AVAILABLE : 0;
                }
                else
                {
                    Data.CASH_ALL = 0;
                    Data.CASH_AVAILABLE = 0;
                }

                int SYear;
                int Semesters;
                SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);
                List<DB2_L_WORK2> BookWorks = db.DB2_L_WORK2.Where(a => a.SCHOOL_NO == thisUser.SCHOOL_NO && a.NO_READ == thisUser.IDNO && a.SEYEAR == SYear.ToString() && a.SESEM == Semesters.ToString()).ToList();
                if (BookWorks != null)
                {
                    Data.BOOKS = BookWorks.Sum(a => a.QTY);
                    string YYYYMM = DateTime.Today.ToString("yyyyMM");
                    DB2_L_WORK2 mm = BookWorks.Where(a => a.RET_YYMM == YYYYMM).FirstOrDefault();
                    if (mm != null)
                        Data.BOOKS_MONTH = mm.QTY;
                    else
                        Data.BOOKS_MONTH = 0;
                }
                //DB2_SCH_STATIC SCH_STATIC = db.DB2_SCH_STATIC.Where(a => a.SCHOOL_NO == wSCHOOL_NO && a.USER_NO == wUSER_NO && a.SYEAR == SYear && a.SEMESTER == Semesters).FirstOrDefault();

                //if (SCH_STATIC!=null)
                //{
                //    Data.BOOKS = SCH_STATIC.BOOKS;
                //}
                // CustomerName == null ? n.CustomerName == n.CustomerName : n.CustomerName == CustomerName.ToString())

                //閱讀等級
                ADDT09 a9 = db.ADDT09.Where(user => user.SCHOOL_NO == wSCHOOL_NO && user.USER_NO == wUSER_NO).FirstOrDefault();
                if (a9 != null)
                {
                    Data.ReadLEVEL = Convert.ToInt16(a9.LEVEL_ID);
                }
                else
                {
                    Data.ReadLEVEL = 0;
                }

                //閱讀認證.等級
                Data.PassportLEVEL = db.ADDT04.Where(p => p.USER_NO == wUSER_NO && p.SCHOOL_NO == wSCHOOL_NO && p.PASS_YN == "Y").Count();

                //閱讀等級圖示
                Data.ReadImgURL = UserProfileHelper.GetSECImgReadLEVEL((Byte)Data.ReadLEVEL);

                //「認證」等級圖示
                Data.PassportImgURL = UserProfileHelper.GetSECImgPassportLEVEL(UserProfile.GetOenUseADDT04toShort(wUSER_NO, wSCHOOL_NO, ref db));

                //我可兌換獎品 TOP 3 筆
                Data.AWAT02List = db.AWAT02.Where(a => (a.SCHOOL_NO == thisUser.SCHOOL_NO || a.SCHOOL_NO == "ALL") && a.AWARD_STATUS != "N" && a.QTY_STORAGE > 0
                                 && a.SDATETIME <= DateTime.Today && a.EDATETIME >= DateTime.Today && a.COST_CASH <= Data.CASH_AVAILABLE
                                 && (a.READ_LEVEL == null || a.READ_LEVEL <= Data.ReadLEVEL)
                                 && (a.PASSPORT_LEVEL == null || a.PASSPORT_LEVEL <= Data.PassportLEVEL)
                                 )
                                .OrderByDescending(a => a.COST_CASH).Take(3).ToList();

                string sSQL = @"SELECT TOP 3 A.*
                FROM AWAT02 A (NOLOCK)
                WHERE (A.SCHOOL_NO=@SCHOOL_NO OR A.SCHOOL_NO='ALL')
                AND A.AWARD_STATUS<>'N' AND A.QTY_STORAGE>0
                AND A.SDATETIME <= GETDATE()
                AND A.EDATETIME >= GETDATE()
                AND A.COST_CASH <= @CASH_AVAILABLE
                AND (A.READ_LEVEL IS  NULL OR A.READ_LEVEL<=@LEVEL_ID)
                AND (A.PASSPORT_LEVEL IS  NULL OR A.PASSPORT_LEVEL<=@PassportLEVEL)
                AND (ISNULL(A.BUY_PERSON_YN,'0')='0' OR (ISNULL(A.BUY_PERSON_YN,'0')='1'
                AND (SELECT COUNT(*) FROM REFT01 B (NOLOCK) WHERE B.REF_KEY=A.AWARD_NO AND B.REF_TABLE='AWAT02' AND B.SCHOOL_NO=@SCHOOL_NO AND B.USER_NO=@USER_NO)>0
                ))
                ORDER BY A.COST_CASH DESC";
                var QTemp = db.Database.Connection.Query<AWAT02>(sSQL
                    , new
                    {
                        SCHOOL_NO = thisUser.SCHOOL_NO,
                        CASH_AVAILABLE = Data.CASH_AVAILABLE,
                        LEVEL_ID = Data.ReadLEVEL,
                        PassportLEVEL = Data.PassportLEVEL,
                        USER_NO = wUSER_NO
                    });

                //我可兌換獎品 TOP 3 筆
                Data.AWAT02List = QTemp.ToList();

                //兌換獎品系統路徑
                ViewBag.SysAwardPath = AWAI01Controller.GetImagePathUrl(AWAI01SearchViewModel.SouTableVal.Student);

                //QAT02Qty
                ViewBag.QAT02Qty = db.QAT02.Where(q2 => q2.A_USER_NO == wUSER_NO && q2.A_STATUS != "2").Count();
            }
            else
            {
                //老師
                AWAT08 tCASH = db.AWAT08.Where(a => a.SCHOOL_NO == thisUser.SCHOOL_NO && a.USER_NO == thisUser.USER_NO).FirstOrDefault();

                if (tCASH != null)
                {
                    Data.CASH_ALL = (tCASH.CASH_ALL != null) ? tCASH.CASH_ALL : 0;
                    Data.CASH_AVAILABLE = (tCASH.CASH_AVAILABLE != null) ? tCASH.CASH_AVAILABLE : 0;
                }
                else
                {
                    Data.CASH_ALL = 0;
                    Data.CASH_AVAILABLE = 0;
                }

                //我可兌換獎品 TOP 3 筆
                Data.AWAT09List = db.AWAT09.Where(a => a.SCHOOL_NO == thisUser.SCHOOL_NO && a.QTY_STORAGE > 0
                                 && a.SDATETIME <= DateTime.Today.Date && a.EDATETIME >= DateTime.Today.Date && a.COST_CASH <= Data.CASH_AVAILABLE)
                                .OrderByDescending(a => a.COST_CASH).Take(3).ToList();

                //兌換獎品系統路徑
                ViewBag.SysAwardPath = AWAI01Controller.GetImagePathUrl(AWAI01SearchViewModel.SouTableVal.Teacher);

                //QAT02Qty
                ViewBag.QAT02Qty = 0;

                //老師本月已發放特殊點數
                string ErrMsg;
                short ThisMonthCash;
                string LimitShow = string.Empty;

                int CashLimit = ECOOL_APP.UserProfile.GetCashLimit(user.SCHOOL_NO, user.USER_NO, user.USER_TYPE, ref db, out ThisMonthCash, out ErrMsg);
                if (CashLimit != short.MaxValue && CashLimit != 0)
                {
                    LimitShow = "您本月有" + CashLimit + "點酷幣可以發給特殊表現的孩子，你已經發放了" + ThisMonthCash.ToString() + "點。";
                }
                Data.Special__Cash_Limit = LimitShow;

                //老師本月已發放酷幣點數
                int nowYear = DateTime.Now.Year;
                int nowMonth = DateTime.Now.Month;
                var groupAWAT01_LOG = from al in db.AWAT01_LOG
                                      join h01 in db.HRMT01 on new { al.USER_NO, al.SCHOOL_NO } equals new { h01.USER_NO, h01.SCHOOL_NO } into alh
                                      from h01 in alh
                                      where al.CASH_IN > 0
                                        && al.LOG_TIME.Year == nowYear
                                        && al.LOG_TIME.Month == nowMonth
                                        && h01.USER_TYPE == UserType.Student
                                      group al by new { al.LOG_PERSON } into g
                                      select new { LOG_PERSON = g.Key.LOG_PERSON, ADD_CASH_ALL = g.Sum(d => d.ADD_CASH_ALL) };
                var HRMT01List = from h in db.HRMT01
                                 where h.SCHOOL_NO == wSCHOOL_NO && h.USER_NO == user.USER_NO
                                 && (!UserStaus.NGKeyinUserStausList.Contains(h.USER_STATUS))
                                 join a in groupAWAT01_LOG on h.USER_KEY equals a.LOG_PERSON into ha
                                 from a in ha.DefaultIfEmpty()
                                 select new
                                 {
                                     USE_CASH = a.ADD_CASH_ALL ?? 0
                                 };
                Data.Month_Given_Cash = HRMT01List.FirstOrDefault().USE_CASH;
            }

            //角色娃娃
            if (!string.IsNullOrWhiteSpace(thisUser.PHOTO))
            {
                Data.PlayerUrl = Service.GetDirectorySysMyPhotoPath(thisUser.SCHOOL_NO, thisUser.USER_NO, thisUser.PHOTO);
            }
            else
            {
                Data.PlayerUrl = UserProfile.GetPlayerUrl(ref db, thisUser.SCHOOL_NO, thisUser.USER_NO, thisUser.SEX, thisUser.USER_TYPE);
            }

            SetStatisticalData1(wSCHOOL_NO, wUSER_NO, wCLASS_NO, wDATA_ANGLE_TYPE, ref Data);

            return PartialView(Data);
        }

        /// <summary>
        /// 以各角度看 線上投稿/閱讀認證 資料
        /// </summary>
        /// <param name="wSCHOOL_NO"></param>
        /// <param name="wUSER_NO"></param>
        /// <param name="wCLASS_NO"></param>
        /// <param name="Data"></param>
        /// <param name="wDATA_ANGLE_TYPE"></param>
        public void SetStatisticalData(string wSCHOOL_NO, string wUSER_NO, string wCLASS_NO, string wDATA_ANGLE_TYPE, ref SECI01IndexViewModel Data)
        {

            // 標頭
            int SYear; int Semesters;
            SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters); // 取得目前的Semester
            //線上投稿資料
            var MyADDT01List = from a01 in db.ADDT01
                               join h01 in db.HRMT01 on new { a01.USER_NO, a01.SCHOOL_NO } equals new { h01.USER_NO, h01.SCHOOL_NO } into h01_join
                               from h01 in h01_join.DefaultIfEmpty()
                               where a01.SCHOOL_NO == wSCHOOL_NO 
                               && (!UserStaus.NGUserStausList.Contains(h01.USER_STATUS))
                               && h01.USER_STATUS == UserStaus.Enabled
                                && a01.WRITING_STATUS != (byte)ADDStatus.eADDT01Status.Disable
                               select a01;

            List<string> MyChild = new List<string>();
            if (wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.MyChildData)
            {
                var Ht06 = HRMT06.GetMyPanyStudent(wSCHOOL_NO, wUSER_NO, db);

                if (Ht06 != null)
                {
                    if (Ht06.Count() > 0)
                    {
                        MyChild = Ht06.Select(a => a.STUDENT_USER_NO).ToList();
                    }
                }
            }
            if (!string.IsNullOrEmpty(wCLASS_NO)) {
              
                Data.wCLASS_NO = wCLASS_NO;
                Data.wGrade_NO = wCLASS_NO.Substring(0, 1);
            }
            if (MyChild.Count == 0)
            {
                MyChild.Add("");
            }

            //看單一學生
            if (wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.OneData)
            {
                MyADDT01List = MyADDT01List.Where(a => a.USER_NO == wUSER_NO);

                Data.arrWRITING_NO = MyADDT01List.OrderByDescending(a => a.WRITING_NO).Select(a => a.WRITING_NO).ToList();
            }

            //看寶貝
            if (wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.MyChildData)
            {
                MyADDT01List = MyADDT01List.Where(a => MyChild.Contains(a.USER_NO));
            }

            //看單一班級
            if (wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.ClassData)
            {
                string IDNO = "";
               IDNO = db.HRMT01.Where(x => x.SCHOOL_NO == wSCHOOL_NO && x.USER_NO == wUSER_NO).Select(x => x.IDNO).FirstOrDefault();
             
                var HrList = db.HRMT01.Where(a => a.SCHOOL_NO == wSCHOOL_NO && a.CLASS_NO == wCLASS_NO).Select(a => a.USER_NO).ToList();
                string GRADE = "";
                string CLASSNUM = "";
                if (!string.IsNullOrEmpty(wCLASS_NO))
                {

                    GRADE = wCLASS_NO.Substring(0, 1);
                    if (GRADE != "1")
                    {
                        int GRADENum = 0;
                        GRADENum = Int32.Parse(GRADE) - 1;
                        GRADE = GRADENum.ToString();
                    }

                    CLASSNUM = wCLASS_NO.Substring(1, 2);
                    CLASSNUM = GRADE + CLASSNUM;
                    MyADDT01List = MyADDT01List.Where(a => (HrList.Contains(a.USER_NO) && (a.CLASS_NO == wCLASS_NO || a.CLASS_NO == CLASSNUM)));
                }

                else {



                    MyADDT01List = MyADDT01List.Where(a => (HrList.Contains(a.USER_NO) && (a.CLASS_NO == wCLASS_NO)));

                }
            }

            Data.WritingCount = MyADDT01List.Count();
            Data.WritingShareCount = MyADDT01List.Where(a => a.SHARE_YN == "Y").Count();
            Data.ADDT01List = MyADDT01List.OrderByDescending(a => a.WRITING_NO).Take(5).ToList();

            //閱讀認證
            var MyADDT06List = from a06 in db.ADDT06
                               join h01 in db.HRMT01
                                   on new { a06.SCHOOL_NO, a06.USER_NO }
                                   equals new { h01.SCHOOL_NO, h01.USER_NO } into h01_join
                               from h01 in h01_join.DefaultIfEmpty()
                               where a06.SCHOOL_NO == wSCHOOL_NO 
                               && (!UserStaus.NGKeyinUserStausList.Contains(h01.USER_STATUS))
                                && a06.APPLY_STATUS != ADDT06.APPLY_STATUS_Val.APPLY_TYPE_DEL
                               select a06;

            //看單一學生
            if (wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.OneData)
            {
                MyADDT06List = MyADDT06List.Where(a => a.USER_NO == wUSER_NO);

                Data.arrAPPLY_NO = MyADDT06List.OrderByDescending(a => a.APPLY_NO).Select(a => a.APPLY_NO).ToList();
            }

            //看寶貝
            if (wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.MyChildData)
            {
                MyADDT06List = MyADDT06List.Where(a => MyChild.Contains(a.USER_NO));
            }

            //看單一班級
            if (wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.ClassData)
            {
                var HrList = db.HRMT01.Where(a => a.SCHOOL_NO == wSCHOOL_NO && a.CLASS_NO == wCLASS_NO).Select(a => a.USER_NO).ToList();
              //  MyADDT06List = MyADDT06List.Where(a => HrList.Contains(a.USER_NO));


                string IDNO = "";
                IDNO = db.HRMT01.Where(x => x.SCHOOL_NO == wSCHOOL_NO && x.USER_NO == wUSER_NO).Select(x => x.IDNO).FirstOrDefault();

               // var HrList = db.HRMT01.Where(a => a.SCHOOL_NO == wSCHOOL_NO && a.CLASS_NO == wCLASS_NO).Select(a => a.USER_NO).ToList();
                string GRADE = "";
                string CLASSNUM = "";
                if (!string.IsNullOrEmpty(wCLASS_NO))
                {

                    GRADE = wCLASS_NO.Substring(0, 1);
                    if (GRADE != "1")
                    {
                        int GRADENum = 0;
                        GRADENum = Int32.Parse(GRADE) - 1;
                        GRADE = GRADENum.ToString();
                    }

                    CLASSNUM = wCLASS_NO.Substring(1, 2);
                    CLASSNUM = GRADE + CLASSNUM;
                    MyADDT06List = MyADDT06List.Where(a => (HrList.Contains(a.USER_NO) && (a.CLASS_NO == wCLASS_NO || a.CLASS_NO == CLASSNUM)));
                }

                else
                {



                    MyADDT06List = MyADDT06List.Where(a => (HrList.Contains(a.USER_NO) && (a.CLASS_NO == wCLASS_NO)));

                }
            }
            MyADDT06List = MyADDT06List.Where(x => x.APPLY_STATUS != ADDT06.APPLY_STATUS_Val.APPLY_TYPE_Back && x.APPLY_STATUS != ADDT06.APPLY_STATUS_Val.APPLY_TYPE_TempSave);
            Data.BookCount = MyADDT06List.Count();
            Data.BookShareCount = MyADDT06List.Where(a => a.SHARE_YN == "y").Count();
            Data.ADDT06List = MyADDT06List.OrderByDescending(a => a.APPLY_NO).Take(5).ToList();

            //建議與鼓勵 top 5
            Data.ADDT02List = (from a in db.ADDT02
                               join b in MyADDT01List on new { a.SCHOOL_NO, a.WRITING_NO } equals new { b.SCHOOL_NO, b.WRITING_NO }
                               where a.COMMENT_STATUS != 9
                               select a).OrderByDescending(a => a.CRE_DATE).Take(5).ToList();

            // 校內外表現
            var SchoolDataList = (from a in db.ADDT14
                                  join h01 in db.HRMT01 on new { a.USER_NO, a.SCHOOL_NO } equals new { h01.USER_NO, h01.SCHOOL_NO } into h01_join
                                  from h01 in h01_join.DefaultIfEmpty()
                                  where a.SCHOOL_NO == wSCHOOL_NO && a.APPLY_STATUS != "9"
                                  && (!UserStaus.NGUserStausList.Contains(h01.USER_STATUS))
                                  select new SECI01SchoolDataViewModel
                                  {
                                      SYS_TABLE = ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_IN,
                                      USER_NO = a.USER_NO,
                                      CLASS_NO = a.CLASS_NO,
                                      NO = a.IAWARD_ID,
                                      CRE_DATE = a.CREATEDATE,
                                      CONTENT_TXT = a.IAWARD_ITEM,
                                      CASH = a.CASH
                                  })
                              .Union(from a in db.ADDT15
                                     join h01 in db.HRMT01 on new { a.USER_NO, a.SCHOOL_NO } equals new { h01.USER_NO, h01.SCHOOL_NO } into h01_join
                                     from h01 in h01_join.DefaultIfEmpty()
                                     where a.SCHOOL_NO == wSCHOOL_NO && a.APPLY_STATUS != "9" && (!UserStaus.NGUserStausList.Contains(h01.USER_STATUS))
                                     select new SECI01SchoolDataViewModel
                                     {
                                         SYS_TABLE = ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OBC,
                                         USER_NO = a.USER_NO,
                                         CLASS_NO = a.CLASS_NO,
                                         NO = a.OAWARD_ID,
                                         CRE_DATE = a.CREATEDATE,
                                         CONTENT_TXT = a.OAWARD_ITEM,
                                         CASH = a.CASH
                                     }
                                 );

            //看單一學生
            if (wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.OneData)
            {
                SchoolDataList = SchoolDataList.Where(a => a.USER_NO == wUSER_NO);
            }

            //看寶貝
            if (wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.MyChildData)
            {
                SchoolDataList = SchoolDataList.Where(a => MyChild.Contains(a.USER_NO));
            }

            //看單一班級
            if (wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.ClassData)
            {
                var HrList = db.HRMT01.Where(a => a.SCHOOL_NO == wSCHOOL_NO && a.CLASS_NO == wCLASS_NO).Select(a => a.USER_NO).ToList();
          
                string IDNO = "";
                IDNO = db.HRMT01.Where(x => x.SCHOOL_NO == wSCHOOL_NO && x.USER_NO == wUSER_NO).Select(x => x.IDNO).FirstOrDefault();

                // var HrList = db.HRMT01.Where(a => a.SCHOOL_NO == wSCHOOL_NO && a.CLASS_NO == wCLASS_NO).Select(a => a.USER_NO).ToList();
                string GRADE = "";
                string CLASSNUM = "";
                if (!string.IsNullOrEmpty(wCLASS_NO))
                {

                    GRADE = wCLASS_NO.Substring(0, 1);
                    if (GRADE != "1")
                    {
                        int GRADENum = 0;
                        GRADENum = Int32.Parse(GRADE) - 1;
                        GRADE = GRADENum.ToString();
                    }

                    CLASSNUM = wCLASS_NO.Substring(1, 2);
                    CLASSNUM = GRADE + CLASSNUM;
                    SchoolDataList = SchoolDataList.Where(a => (HrList.Contains(a.USER_NO) && (a.CLASS_NO == wCLASS_NO || a.CLASS_NO == CLASSNUM)));
                }

                else
                {



                    SchoolDataList = SchoolDataList.Where(a => (HrList.Contains(a.USER_NO) && (a.CLASS_NO == wCLASS_NO)));

                }
            }

            Data.SchoolInCount = SchoolDataList.Where(A => A.SYS_TABLE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_IN).Count();
            Data.SchoolObcCount = SchoolDataList.Where(A => A.SYS_TABLE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OBC).Count();

            // 校內外表現top 5
            Data.SchoolDataList = SchoolDataList.OrderByDescending(a => a.CRE_DATE).Take(5).ToList();

            // 最後投稿日 備註

            //取出比較的天數
            string LASH_DAY_VAL = BDMT02Service.GetRefFirstVal("SECI01", "LAST_DAY_MEMO", "DAY", wSCHOOL_NO, ref db);

            if (string.IsNullOrWhiteSpace(LASH_DAY_VAL))
            {
                LASH_DAY_VAL = "7";
            }

            //比較的日期
            var CompareDdays = DateTime.Now.AddDays(-1 * Convert.ToDouble(LASH_DAY_VAL));

            //最後一筆文章的日期
            var LastDdays = (
                         from c in Data.ADDT01List
                         select c.CRE_DATE
                        ).Union(
                         from e in Data.ADDT06List
                         select e.CRE_DATE
                    ).Max();

            var MemoList = BDMT02Service.GetRefDataList("SECI01", "LAST_DAY_MEMO", null, wSCHOOL_NO, ref db);

            string MoreDataString = "非常棒!!";
            string LessDataString = "很久沒寫文章了喔!!，再努力點喔!!";
            string NotDataString = "無任何文章，再努力再努力點喔!!";

            if (MemoList.Count > 0)
            {
                //大於等於
                MoreDataString = MemoList.Where(a => a.DATA_TYPE == "1").Select(A => A.CONTENT_TXT).FirstOrDefault() ?? MoreDataString;

                //小於
                LessDataString = MemoList.Where(a => a.DATA_TYPE == "2").Select(A => A.CONTENT_TXT).FirstOrDefault() ?? LessDataString;

                //完全沒資料
                NotDataString = MemoList.Where(a => a.DATA_TYPE == "3").Select(A => A.CONTENT_TXT).FirstOrDefault() ?? NotDataString;
            }

            string NAME = string.Empty;

            //看單一學生
            if (wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.OneData)
            {
                NAME = Data.NAME;
            }

            //看寶貝
            if (wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.MyChildData)
            {
                NAME = "您的寶貝";
            }

            //看單一班級
            if (wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.ClassData)
            {
                NAME = "您的班級";
            }

            if (wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.SchoolData)
            {
                NAME = "您的學校";
            }

            //沒文章
            if (LastDdays == null)
            {
                Data.Last_Day_MEMO = NAME + " " + NotDataString;
            }
            else if (LastDdays < CompareDdays) //最後一筆文章的日期 小於 比較的日期
            {
                Data.Last_Day_MEMO = NAME + " 最後投稿日(投稿, 閱讀): " + Convert.ToDateTime(LastDdays).ToString("yyyy/MM/dd") + @"，" + LessDataString;
            }
            else
            {
                Data.Last_Day_MEMO = NAME + " 最後投稿日(投稿, 閱讀): " + Convert.ToDateTime(LastDdays).ToString("yyyy/MM/dd") + @"，" + MoreDataString;
            }
        }
        public void SetStatisticalData1(string wSCHOOL_NO, string wUSER_NO, string wCLASS_NO, string wDATA_ANGLE_TYPE, ref SECI01IndexViewModel Data)
        {
            //線上投稿資料
            var MyADDT01List = from a01 in db.ADDT01
                               join h01 in db.HRMT01 on new { a01.USER_NO, a01.SCHOOL_NO } equals new { h01.USER_NO, h01.SCHOOL_NO } into h01_join
                               from h01 in h01_join.DefaultIfEmpty()
                               where a01.SCHOOL_NO == wSCHOOL_NO
                               //&& (!UserStaus.NGUserStausList.Contains(h01.USER_STATUS))
                                && a01.WRITING_STATUS != (byte)ADDStatus.eADDT01Status.Disable
                               select a01;

            List<string> MyChild = new List<string>();
            if (wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.MyChildData)
            {
                var Ht06 = HRMT06.GetMyPanyStudent(wSCHOOL_NO, wUSER_NO, db);

                if (Ht06 != null)
                {
                    if (Ht06.Count() > 0)
                    {
                        MyChild = Ht06.Select(a => a.STUDENT_USER_NO).ToList();
                    }
                }
            }

            if (MyChild.Count == 0)
            {
                MyChild.Add("");
            }

            //看單一學生
            if (wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.OneData)
            {
                MyADDT01List = MyADDT01List.Where(a => a.USER_NO == wUSER_NO);

                Data.arrWRITING_NO = MyADDT01List.OrderByDescending(a => a.WRITING_NO).Select(a => a.WRITING_NO).ToList();
            }

            //看寶貝
            if (wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.MyChildData)
            {
                MyADDT01List = MyADDT01List.Where(a => MyChild.Contains(a.USER_NO));
            }

            //看單一班級
            if (wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.ClassData)
            {
                MyADDT01List = MyADDT01List.Where(a => a.CLASS_NO == wCLASS_NO);
            }

            Data.WritingCount = MyADDT01List.Count();
            Data.WritingShareCount = MyADDT01List.Where(a => a.SHARE_YN == "Y").Count();
            Data.ADDT01List = MyADDT01List.OrderByDescending(a => a.WRITING_NO).Take(5).ToList();

            //閱讀認證
            var MyADDT06List = from a06 in db.ADDT06
                               join h01 in db.HRMT01
                                   on new { a06.SCHOOL_NO, a06.USER_NO }
                                   equals new { h01.SCHOOL_NO, h01.USER_NO } into h01_join
                               from h01 in h01_join.DefaultIfEmpty()
                               where a06.SCHOOL_NO == wSCHOOL_NO
                               //&& (!UserStaus.NGKeyinUserStausList.Contains(h01.USER_STATUS))
                                && a06.APPLY_STATUS != ADDT06.APPLY_STATUS_Val.APPLY_TYPE_DEL
                               select a06;

            //看單一學生
            if (wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.OneData)
            {
                MyADDT06List = MyADDT06List.Where(a => a.USER_NO == wUSER_NO);

                Data.arrAPPLY_NO = MyADDT06List.OrderByDescending(a => a.APPLY_NO).Select(a => a.APPLY_NO).ToList();
            }

            //看寶貝
            if (wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.MyChildData)
            {
                MyADDT06List = MyADDT06List.Where(a => MyChild.Contains(a.USER_NO));
            }

            //看單一班級
            if (wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.ClassData)
            {
                MyADDT06List = MyADDT06List.Where(a => a.CLASS_NO == wCLASS_NO);
            }
            MyADDT06List = MyADDT06List.Where(x => x.APPLY_STATUS != ADDT06.APPLY_STATUS_Val.APPLY_TYPE_Back && x.APPLY_STATUS != ADDT06.APPLY_STATUS_Val.APPLY_TYPE_TempSave);
            Data.BookCount = MyADDT06List.Count();
            Data.BookShareCount = MyADDT06List.Where(a => a.SHARE_YN == "y").Count();
            Data.ADDT06List = MyADDT06List.OrderByDescending(a => a.APPLY_NO).Take(5).ToList();

            //建議與鼓勵 top 5
            Data.ADDT02List = (from a in db.ADDT02
                               join b in MyADDT01List on new { a.SCHOOL_NO, a.WRITING_NO } equals new { b.SCHOOL_NO, b.WRITING_NO }
                               where a.COMMENT_STATUS != 9
                               select a).OrderByDescending(a => a.CRE_DATE).Take(5).ToList();

            // 校內外表現
            var SchoolDataList = (from a in db.ADDT14
                                  join h01 in db.HRMT01 on new { a.USER_NO, a.SCHOOL_NO } equals new { h01.USER_NO, h01.SCHOOL_NO } into h01_join
                                  from h01 in h01_join.DefaultIfEmpty()
                                  where a.SCHOOL_NO == wSCHOOL_NO && a.APPLY_STATUS != "9"
                                  //&& (!UserStaus.NGUserStausList.Contains(h01.USER_STATUS))
                                  select new SECI01SchoolDataViewModel
                                  {
                                      SYS_TABLE = ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_IN,
                                      USER_NO = a.USER_NO,
                                      CLASS_NO = a.CLASS_NO,
                                      NO = a.IAWARD_ID,
                                      CRE_DATE = a.CREATEDATE,
                                      CONTENT_TXT = a.IAWARD_ITEM,
                                      CASH = a.CASH
                                  })
                              .Union(from a in db.ADDT15
                                     join h01 in db.HRMT01 on new { a.USER_NO, a.SCHOOL_NO } equals new { h01.USER_NO, h01.SCHOOL_NO } into h01_join
                                     from h01 in h01_join.DefaultIfEmpty()
                                     where a.SCHOOL_NO == wSCHOOL_NO && a.APPLY_STATUS != "9" 
                                     //&& (!UserStaus.NGUserStausList.Contains(h01.USER_STATUS))
                                     select new SECI01SchoolDataViewModel
                                     {
                                         SYS_TABLE = ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OBC,
                                         USER_NO = a.USER_NO,
                                         CLASS_NO = a.CLASS_NO,
                                         NO = a.OAWARD_ID,
                                         CRE_DATE = a.CREATEDATE,
                                         CONTENT_TXT = a.OAWARD_ITEM,
                                         CASH = a.CASH
                                     }
                                 );

            //看單一學生
            if (wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.OneData)
            {
                SchoolDataList = SchoolDataList.Where(a => a.USER_NO == wUSER_NO);
            }

            //看寶貝
            if (wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.MyChildData)
            {
                SchoolDataList = SchoolDataList.Where(a => MyChild.Contains(a.USER_NO));
            }

            //看單一班級
            if (wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.ClassData)
            {
                SchoolDataList = SchoolDataList.Where(a => a.CLASS_NO == wCLASS_NO);
            }

            Data.SchoolInCount = SchoolDataList.Where(A => A.SYS_TABLE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_IN).Count();
            Data.SchoolObcCount = SchoolDataList.Where(A => A.SYS_TABLE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OBC).Count();

            // 校內外表現top 5
            Data.SchoolDataList = SchoolDataList.OrderByDescending(a => a.CRE_DATE).Take(5).ToList();

            // 最後投稿日 備註

            //取出比較的天數
            string LASH_DAY_VAL = BDMT02Service.GetRefFirstVal("SECI01", "LAST_DAY_MEMO", "DAY", wSCHOOL_NO, ref db);

            if (string.IsNullOrWhiteSpace(LASH_DAY_VAL))
            {
                LASH_DAY_VAL = "7";
            }

            //比較的日期
            var CompareDdays = DateTime.Now.AddDays(-1 * Convert.ToDouble(LASH_DAY_VAL));

            //最後一筆文章的日期
            var LastDdays = (
                         from c in Data.ADDT01List
                         select c.CRE_DATE
                        ).Union(
                         from e in Data.ADDT06List
                         select e.CRE_DATE
                    ).Max();

            var MemoList = BDMT02Service.GetRefDataList("SECI01", "LAST_DAY_MEMO", null, wSCHOOL_NO, ref db);

            string MoreDataString = "非常棒!!";
            string LessDataString = "很久沒寫文章了喔!!，再努力點喔!!";
            string NotDataString = "無任何文章，再努力再努力點喔!!";

            if (MemoList.Count > 0)
            {
                //大於等於
                MoreDataString = MemoList.Where(a => a.DATA_TYPE == "1").Select(A => A.CONTENT_TXT).FirstOrDefault() ?? MoreDataString;

                //小於
                LessDataString = MemoList.Where(a => a.DATA_TYPE == "2").Select(A => A.CONTENT_TXT).FirstOrDefault() ?? LessDataString;

                //完全沒資料
                NotDataString = MemoList.Where(a => a.DATA_TYPE == "3").Select(A => A.CONTENT_TXT).FirstOrDefault() ?? NotDataString;
            }

            string NAME = string.Empty;

            //看單一學生
            if (wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.OneData)
            {
                NAME = Data.NAME;
            }

            //看寶貝
            if (wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.MyChildData)
            {
                NAME = "您的寶貝";
            }

            //看單一班級
            if (wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.ClassData)
            {
                NAME = "您的班級";
            }

            if (wDATA_ANGLE_TYPE == UserProfileHelper.AngleVal.SchoolData)
            {
                NAME = "您的學校";
            }

            //沒文章
            if (LastDdays == null)
            {
                Data.Last_Day_MEMO = NAME + " " + NotDataString;
            }
            else if (LastDdays < CompareDdays) //最後一筆文章的日期 小於 比較的日期
            {
                Data.Last_Day_MEMO = NAME + " 最後投稿日(投稿, 閱讀): " + Convert.ToDateTime(LastDdays).ToString("yyyy/MM/dd") + @"，" + LessDataString;
            }
            else
            {
                Data.Last_Day_MEMO = NAME + " 最後投稿日(投稿, 閱讀): " + Convert.ToDateTime(LastDdays).ToString("yyyy/MM/dd") + @"，" + MoreDataString;
            }
        }
        /// <summary>
        /// 點數分析 PartialView
        /// </summary>
        /// <returns></returns>
        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult _PieChartDiv()
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "";

            user = UserProfileHelper.Get();

            if (user == null)
            {
                return RedirectToAction("SessionTimeOutError", "Error");
            }

            SECSharedSearchViewModel wData = new SECSharedSearchViewModel();
            wData.SCHOOL_NO = user.SCHOOL_NO;
            wData.USER_NO = user.USER_NO;

            SECI01IndexViewModel Data = new SECI01IndexViewModel();

            //總酷幣點數
            SECSharedTotalCashDataViewModel TotalData = SharedService.GetTotalCash(ref db, wData);
            Data.CASH_ALL = TotalData.ToTAL_CASH_ALL;
            Data.CASH_AVAILABLE = TotalData.ToTAL_CASH_AVAILABLE;

            //各類別 酷幣點數，及比例
            if (TotalData.ToTAL_CASH_ALL > 0)
            {
                Data.SumItemDescCASHList = SharedService.GetCashPre(ref db, wData, (int)TotalData.ToTAL_CASH_ALL);
                Data.PreCashPieChart = GetPreCashPieChart(Data.SumItemDescCASHList);
                Data.CashPreColumnChart = GetCashPreColumnChart(Data.SumItemDescCASHList);
            }

            return PartialView(Data);
        }

        /// <summary>
        /// 酷幣占比 圓餅圖
        /// </summary>
        /// <param name="PerData"></param>
        /// <returns></returns>
        private Highcharts GetPreCashPieChart(List<SECSharedCashPreViewModel> PerData)
        {
            var returnObject = new List<object>();

            foreach (var item in PerData)
            {
                returnObject.Add(new object[] { item.LOG_DESC, item.PRE });
            }

            Highcharts TempCashPieChart = new Highcharts("TempCashPieChart")
               .InitChart(new Chart
               {
                   DefaultSeriesType = ChartTypes.Pie,
                   //BackgroundColor = new BackColorOrGradient(
                   //                             new Gradient
                   //                             {
                   //                                 LinearGradient = new[] { 0, 0, 0, 100 },
                   //                                 Stops = new object[,]
                   //                                            {
                   //                                                 {   0, System.Drawing.Color.White }
                   //                                                 , {1, "#C7DFF5" }
                   //                                            }
                   //                             }),
                   BorderWidth = null,
                   Shadow = false
               })
              .SetPlotOptions(new PlotOptions
              {
                  Pie = new PlotOptionsPie
                  {
                      AllowPointSelect = true,
                      Cursor = Cursors.Pointer,
                      //ShowInLegend = true,
                      DataLabels = new PlotOptionsPieDataLabels
                      {
                          Enabled = true,
                          Format = "<b>{point.name}</b>: {point.percentage:.1f} %",
                          Style = "color: (Highcharts.theme && Highcharts.theme.contrastTextColor) || 'black'"
                      }
                  }
              })
             .SetTooltip(new Tooltip { PointFormat = "{series.name}: <b>{point.percentage:.1f}%</b>" })
             .SetTitle(new Title { Text = "酷幣點數占比圖" })
              .SetSeries(new Series
              {
                  Data = new Data(returnObject.ToArray())
              });

            chartsHelper.SetCopyright(TempCashPieChart);

            return TempCashPieChart;
        }

        /// <summary>
        /// 各類別點數 長條圖
        /// </summary>
        /// <param name="PerData"></param>
        /// <returns></returns>
        private Highcharts GetCashPreColumnChart(List<SECSharedCashPreViewModel> PerData)
        {
            var returnPoint = new List<Point>();

            foreach (var item in PerData)
            {
                returnPoint.Add(new Point { Y = item.SUM_ADD_CASH_ALL, Name = item.LOG_DESC });
            }

            Data data = new Data(returnPoint.ToArray());

            Highcharts TempPreColumnChart = new Highcharts("TempPreColumnChart")
           .InitChart(new Chart
           {
               DefaultSeriesType = ChartTypes.Column,
               //BackgroundColor = new BackColorOrGradient(
               //                                 new Gradient
               //                                 {
               //                                     LinearGradient = new[] { 0, 0, 0, 400 },
               //                                     Stops = new object[,]
               //                                                {
               //                                                     {   0, System.Drawing.Color.White }
               //                                                     , {1, "#C7DFF5" }
               //                                                }
               //                                 })
           })
           .SetTitle(new Title { Text = "各類別酷幣點數" })
           .SetXAxis(new XAxis
           {
               Type = AxisTypes.Category
                        ,
               Labels = new XAxisLabels
               {
                   Rotation = -45,
                   Style = "fontSize: '12px', fontFamily: 'Verdana, sans-serif'"
               }
           })
           //.SetYAxis(new YAxis { Title = new YAxisTitle { Text = "酷幣數量" }, Min = 0 })
           .SetYAxis(new YAxis { Title = new YAxisTitle { Text = "酷幣數量" }, AllowDecimals = false })
           .SetSeries(new[]
                   {
                        new Series {
                            Data = data,
                            Name = "酷幣數量",
                            PlotOptionsBar = new PlotOptionsBar {
                                ColorByPoint = true,
                            },
                        }
                   })
           .SetPlotOptions(new PlotOptions
           {
               Column = new PlotOptionsColumn
               {
                   DataLabels = new PlotOptionsColumnDataLabels
                   {
                       Enabled = true,
                       Rotation = 0,
                       Color = System.Drawing.Color.Black,
                       Format = "{point.y:.1f}",
                       Style = "fontSize: '9px',fontFamily: 'Verdana, sans-serif'"
                   }
               }
           })
           .SetTooltip(new Tooltip { PointFormat = "{point.y:.1f} 點</b>" })
           .SetLegend(new Legend { Enabled = false });

            chartsHelper.SetCopyright(TempPreColumnChart);

            return TempPreColumnChart;
        }

        /// <summary>
        /// 加值應用統計
        /// </summary>
        /// <returns></returns>
        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult _StatisticalDiv()
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "";
            user = UserProfileHelper.Get();

            SECSharedSearchViewModel wData = new SECSharedSearchViewModel();
            wData.DATA_ANGLE_TYPE = UserProfileHelper.GetDataAngleType(user);
            wData.SCHOOL_NO = user.SCHOOL_NO;

            if (wData.DATA_ANGLE_TYPE == UserProfileHelper.AngleVal.OneData)
            {
                wData.USER_NO = user.USER_NO;
            }

            if (wData.DATA_ANGLE_TYPE == UserProfileHelper.AngleVal.ClassData)
            {
                wData.CLASS_NO = user.TEACH_CLASS_NO;
            }

            SECI01StatisticalViewModel Data = new SECI01StatisticalViewModel();
            Data.DATA_TYPE_NAME = GetDATA_TYPE_NAME(wData.DATA_ANGLE_TYPE, wData.CLASS_NO);
            Data.TEachMonth = DateHelper.GetArrMonth(DateTime.Now, SetMonth);
            Data.MonthList = SharedService.GetMonthData(Data.TEachMonth, wData);

            Data.TEachWeekToDate = DateHelper.GetArrWeekToDate(DateTime.Now, SetMonth);
            Data.WeekList = SharedService.GetWeekData(Data.TEachWeekToDate, wData);

            Data.Monthcharts = GetMonthcharts(Data);
            Data.Weekcharts = GetWeekcharts(Data);

            return PartialView(Data);
        }

        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult ExportResult()
        {
            ViewBag.Panel_Title = "我的秘書-學習成果匯出";
            user = UserProfileHelper.Get();

            if (user == null)
            {
                return RedirectToAction("SessionTimeOutError", "Error");
            }

            user = UserProfileHelper.Get();

            SECSharedSearchViewModel Data = new SECSharedSearchViewModel();
            Data.SCHOOL_NO = user.SCHOOL_NO;
            Data.USER_NO = user.USER_NO;

            Data.DATA_ANGLE_TYPE = UserProfileHelper.GetDataAngleType(user);

            Data.CLASS_NO = user.TEACH_CLASS_NO ?? user.CLASS_NO;

            return View(Data);
        }

        /// <summary>
        /// 產生 月趨示圖
        /// </summary>
        /// <param name="Data"></param>
        /// <returns></returns>
        private Highcharts GetMonthcharts(SECI01StatisticalViewModel Data)
        {
            if (Data.MonthList.Count == 0)
            {
                return null;
            }

            var aADDT01 = (from a in Data.MonthList
                           where a.TYPE_NAME == ADDT01.GetWritingName
                           select a.DATA_COUNT).FirstOrDefault();

            object[] ArrWRITING = new object[] { };

            if (aADDT01 != null)
            {
                ArrWRITING = aADDT01.Cast<object>().ToArray();
            }
            else
            {
                ArrWRITING = new int[Data.TEachMonth.ToArray().Length - 1].Cast<object>().ToArray();
            }

            var aADDT06 = (from a in Data.MonthList
                           where a.TYPE_NAME == ADDT06.GetPassportName
                           select a.DATA_COUNT).FirstOrDefault();

            object[] ArrPASSPORT = new object[] { };

            if (aADDT06 != null)
            {
                ArrPASSPORT = aADDT06.Cast<object>().ToArray();
            }
            else
            {
                ArrPASSPORT = new int[Data.TEachMonth.ToArray().Length - 1].Cast<object>().ToArray();
            }

            Highcharts Monthchart = new Highcharts("Monthchart");

            Monthchart
            .InitChart(new DotNet.Highcharts.Options.Chart { DefaultSeriesType = ChartTypes.Line })
            .SetTitle(new Title { Text = Data.DATA_TYPE_NAME + "月統計", X = 0 })
            .SetXAxis(new XAxis { Title = new XAxisTitle { Text = "月份" }, Categories = Data.TEachMonth.ToArray() })
            .SetYAxis(new YAxis { Title = new YAxisTitle { Text = "筆數" }, Min = 0 })
            .SetPlotOptions(new PlotOptions
            {
                Line = new PlotOptionsLine
                {
                    DataLabels = new PlotOptionsLineDataLabels
                    {
                        Enabled = true
                    },
                    EnableMouseTracking = false
                }
            })
            .SetTooltip(new Tooltip { ValueSuffix = "筆" })
            .SetSeries(new Series[]
                        {
                                 new Series
                                 { Name =  ADDT01.GetWritingName,
                                   Data = new DotNet.Highcharts.Helpers.Data(ArrWRITING)
                                 },
                                 new Series
                                 { Name = ADDT06.GetPassportName,
                                   Data = new DotNet.Highcharts.Helpers.Data(ArrPASSPORT)
                                 }
                        }
            );

            chartsHelper.SetCopyright(Monthchart);

            return Monthchart;
        }

        /// <summary>
        /// 產生 週趨示圖
        /// </summary>
        /// <param name="Data"></param>
        /// <returns></returns>
        private Highcharts GetWeekcharts(SECI01StatisticalViewModel Data)
        {
            if (Data.WeekList.Count == 0)
            {
                return null;
            }

            var ArrEachWeek = DateHelper.GetArrWeekToDate(DateTime.Now, SetMonth).ToArray();

            object[] ArrWRITING = new object[] { };

            try
            {
                ArrWRITING = (from a in Data.WeekList
                              where a.TYPE_NAME == ADDT01.GetWritingName
                              select a.DATA_COUNT).FirstOrDefault().Cast<object>().ToArray();
            }
            catch (Exception)
            {
                ArrWRITING = new int[ArrEachWeek.Length - 1].Cast<object>().ToArray();
            }

            object[] ArrPASSPORT = new object[] { };

            try
            {
                ArrPASSPORT = (from a in Data.WeekList
                               where a.TYPE_NAME == ADDT06.GetPassportName
                               select a.DATA_COUNT).FirstOrDefault().Cast<object>().ToArray();
            }
            catch (Exception)
            {
                ArrPASSPORT = new int[ArrEachWeek.Length - 1].Cast<object>().ToArray();
            }

            Highcharts Weekchart = new Highcharts("Weekchart");

            Weekchart
            .InitChart(new DotNet.Highcharts.Options.Chart { DefaultSeriesType = ChartTypes.Line })
            .SetTitle(new Title { Text = Data.DATA_TYPE_NAME + "週統計", X = 0 })
            .SetXAxis(new XAxis
            {
                Title = new XAxisTitle { Text = "時間" },
                Categories = ArrEachWeek,
                Labels = new XAxisLabels
                {
                    Rotation = -90,
                    Style = "fontSize: '12px'"
                }
            })
            .SetYAxis(new YAxis { Title = new YAxisTitle { Text = "筆數" }, Min = 0 })
            .SetPlotOptions(new PlotOptions
            {
                Line = new PlotOptionsLine
                {
                    DataLabels = new PlotOptionsLineDataLabels
                    {
                        Enabled = true
                    },
                    EnableMouseTracking = false
                }
            })
            .SetTooltip(new Tooltip { ValueSuffix = "筆" })
            .SetSeries(new Series[]
                        {
                                 new Series
                                 { Name = ADDT01.GetWritingName,
                                   Data = new DotNet.Highcharts.Helpers.Data(ArrWRITING)
                                 },
                                 new Series
                                 { Name = ADDT06.GetPassportName,
                                   Data = new DotNet.Highcharts.Helpers.Data(ArrPASSPORT)
                                 }
                        }
            );

            chartsHelper.SetCopyright(Weekchart);

            return Weekchart;
        }

        public ActionResult Index3()
        {
            user = UserProfileHelper.Get();
            BarcCodeMyCashIndexViewModel model = new BarcCodeMyCashIndexViewModel();

            model.WhereSchoolNo = user.SCHOOL_NO;

            //酷幣排行榜
            var RankCashSort = from w1 in db.AWAT01
                               join h1 in db.HRMT01
                               on new { w1.SCHOOL_NO, w1.USER_NO } equals new { h1.SCHOOL_NO, h1.USER_NO }
                               into h1Table
                               from hh1 in h1Table.DefaultIfEmpty()
                               where w1.SCHOOL_NO == model.WhereSchoolNo && w1.CASH_ALL > 0 && (!UserStaus.NGUserStausList.Contains(hh1.USER_STATUS))
                               select new HRMT01QTY { CLASS_NO = hh1.CLASS_NO, NAME = hh1.SNAME, USER_NO = hh1.USER_NO, QTY = w1.CASH_ALL, CASH_ALL = w1.CASH_ALL, CASH_AVAILABLE = w1.CASH_AVAILABLE, };

            var AWAT01List = RankCashSort.OrderByDescending(a => a.CASH_ALL).Take(10).ToList();
            string SortBoard = string.Empty;
            string SortBoard2 = string.Empty;
            short i = 1;
            foreach (var t01 in AWAT01List)
            {
                int deAMT = 0;
                List<AWAT10> AT10list = new List<AWAT10>();
                AT10list = db.AWAT10.Where(x => x.STATUS == "1" && x.SCHOOL_NO == model.WhereSchoolNo && x.USER_NO == t01.USER_NO).ToList();
                if (AT10list.Count() > 0)
                {
                    foreach (var item in AT10list)
                    {
                        if (item.AMT != null)

                        {
                            deAMT += (int)item.AMT;
                        }
                    }
                }
                else
                {
                    int? CASH_AVAILABLE = 0;
                    CASH_AVAILABLE = db.AWAT01.Where(x => x.SCHOOL_NO == model.WhereSchoolNo && x.USER_NO == t01.USER_NO).Select(x => x.CASH_AVAILABLE).FirstOrDefault();
                    deAMT = (int)CASH_AVAILABLE;
                }
                if (i <= 3) SortBoard += string.Format("   第{0}名：{1} {2}  ", i, t01.CLASS_NO, t01.NAME);
                SortBoard2 += string.Format("第{0}名：{1}({2}點)　", i, t01.NAME, deAMT);
                i++;
            }
            ViewBag.SortBoard = SortBoard;
            ViewBag.SortBoard2 = SortBoard2;

            HRMT01 FindUser = db.HRMT01.Include("HRMT25").Where(a => a.USER_NO == user.USER_NO && a.SCHOOL_NO == model.WhereSchoolNo).FirstOrDefault();
            ADDI11Service ServiceA11 = new ADDI11Service();

            #region PageContentB

            string Classno = ServiceA11.Numtostring(FindUser.CLASS_NO);
            model.ShowStep2 = true;
            model.UserNAME = FindUser.SNAME;
            model.CLASS_NO = Classno;
            model.SEAT_NO = FindUser.SEAT_NO;
            //借書量
            SECI05Service ServiceS5 = new SECI05Service();
            SECI05IndexViewModel BookModel = new SECI05IndexViewModel();
            BookModel.WhereSCHOOL_NO = FindUser.SCHOOL_NO;
            BookModel.WhereUSER_NO = FindUser.USER_NO;
            BookModel = ServiceS5.GetBorrowData(BookModel, null, ref db);
            if (BookModel.MyBorrow != null)
            {
                model.UserBookQty = BookModel.MyBorrow.THIS_SESEM_QTY;
                model.AllBookQty = BookModel.MyBorrow.ALL_QTY;
                model.MonthBookQty = BookModel.MyBorrow.THIS_MM_QTY;
            }

            //學生酷幣
            AWAT01 tCASH = db.AWAT01.Where(a => a.SCHOOL_NO == FindUser.SCHOOL_NO && a.USER_NO == FindUser.USER_NO).FirstOrDefault();
            if (tCASH != null)
            {
                if (tCASH.CASH_AVAILABLE.HasValue) model.UserCash = tCASH.CASH_AVAILABLE.Value;
            }

            //定存
            AWAI07Service Service = new AWAI07Service();
            AWAI07IndexViewModel modelA7 = new AWAI07IndexViewModel();
            modelA7.Search = new AWAI07SearchViewModel();
            modelA7.Search.WhereSCHOOL_NO = FindUser.SCHOOL_NO;
            modelA7.Search.WhereUSER_NO = FindUser.USER_NO;
            modelA7.Search.WhereSTATUS = AWAT10.StatusVal.SetUp;
            Service.GetListData(modelA7, ref db);
            if (modelA7.ListData != null)
            {
                var SumAMT = modelA7.ListData.Sum(a => a.AMT);
                if (SumAMT.HasValue) model.UserAWAI07 = SumAMT.Value;
            }

            //運動

            ADDI11MyRunLogViewModel modelA11 = new ADDI11MyRunLogViewModel();
            modelA11.WhereSCHOOL_NO = FindUser.SCHOOL_NO;
            modelA11.WhereUSER_NO = FindUser.USER_NO;
            modelA11 = ServiceA11.GetMyRunLog(modelA11, user, ref db);

            model.RUN_TOTAL_METER = (modelA11.MyRunRank != null) ? modelA11.MyRunRank.RUN_TOTAL_METER ?? 0 : 0;
            // 下次升級
            string upgraderesultStr;
            model.RUN_UPGRADE_METER = ServiceA11.NextUpgradeRunningMiles(model.RUN_UPGRADE_METER, ref db, out upgraderesultStr);
            ViewBag.RUN_UPGRADE_RESULTSTR = upgraderesultStr;

            //取出可對兌獎品

            //閱讀等級
            ADDT09 a9 = db.ADDT09.Where(a => a.SCHOOL_NO == FindUser.SCHOOL_NO && a.USER_NO == FindUser.USER_NO).FirstOrDefault();
            byte L_ID = 0;
            if (a9 != null) L_ID = (a9.LEVEL_ID ?? 0);

            //閱讀認證.等級
            var PassportLEVEL = db.ADDT04.Where(p => p.USER_NO == FindUser.USER_NO && p.SCHOOL_NO == FindUser.SCHOOL_NO && p.PASS_YN == "Y").Count();

            string sSQL = @"SELECT  A.*
            FROM AWAT02 A (NOLOCK)
            WHERE (A.SCHOOL_NO=@SCHOOL_NO OR A.SCHOOL_NO='ALL')
            AND A.AWARD_STATUS<>'N' AND A.QTY_STORAGE>0
            AND A.SDATETIME <= GETDATE()
            AND A.EDATETIME >= GETDATE()
            AND A.COST_CASH <= @CASH_AVAILABLE
            AND (A.READ_LEVEL IS  NULL OR A.READ_LEVEL<=@LEVEL_ID)
            AND (A.PASSPORT_LEVEL IS  NULL OR A.PASSPORT_LEVEL<=@PassportLEVEL)
            AND (ISNULL(A.BUY_PERSON_YN,'0')='0' OR (ISNULL(A.BUY_PERSON_YN,'0')='1'
            AND (SELECT COUNT(*) FROM REFT01 B (NOLOCK) WHERE B.REF_KEY=A.AWARD_NO AND B.REF_TABLE='AWAT02' AND B.SCHOOL_NO=@SCHOOL_NO AND B.USER_NO=@USER_NO)>0
            ))
            ORDER BY A.COST_CASH DESC";
            var QTemp = db.Database.Connection.Query<AWAT02>(sSQL
                , new
                {
                    SCHOOL_NO = FindUser.SCHOOL_NO,
                    CASH_AVAILABLE = (tCASH?.CASH_AVAILABLE ?? 0),
                    LEVEL_ID = L_ID,
                    PassportLEVEL = PassportLEVEL,
                    USER_NO = FindUser.USER_NO
                });

            model.AWAT02List = QTemp.ToList();

            sSQL = @"select a.BOOK_QTY,b.LEVEL_DESC ,b.LEVEL_ID
						 ,Case when a.LEVEL_ID<10 Then (select Top 1 L.LEVEL_QTY from ADDT07 L (nolock) Where L.LEVEL_ID>a.LEVEL_ID order by L.LEVEL_ID)-a.BOOK_QTY
						       else 0 end as UNLEVEL_QTY
						 from ADDT09 a (nolock)
						 left outer join ADDT08 b (nolock) on a.SCHOOL_NO=b.SCHOOL_NO and a.LEVEL_ID=b.LEVEL_ID
						 where a.SCHOOL_NO=@SCHOOL_NO  and a.USER_NO=@USER_NO ";
            model.MyData = db.Database.Connection.Query<ZZZI09MyDataViewModel>(sSQL
                , new
                {
                    SCHOOL_NO = FindUser.SCHOOL_NO,
                    USER_NO = FindUser.USER_NO,
                }).FirstOrDefault();

            // 等級分數計算
            // 公式: 目前酷幣值 + 定存酷幣值 + 每月的借書量 x 100 + 跑步里程 x 20
            model.point = LogicCenter.CaculateMyATM_point(model.UserCash,
                Convert.ToInt32(model.UserAWAI07),
                model.MonthBookQty,
                Convert.ToDouble(model.RUN_TOTAL_METER ?? 0) / 1000.000);
            model.Level = LogicCenter.CaculateMyATM_Level(model.point);
            //  }
            if (model.MyData == null) model.MyData = new ZZZI09MyDataViewModel();
            string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
            ViewBag.ATMUID = FindUser.USER_TYPE;
            bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
            //if (AppMode)
            //{
            //    return PartialView("_PageContentForAPP", model);
            //}
            //else
            //{
            //    return PartialView("_PageContentB", model);
            //}

            #endregion PageContentB

            return View(model);
        }

        public ActionResult BOOK_APPLY(ADDT06ViewModel Data, bool? ShowOriginal, int pageSize = 20)
        {
            ViewBag.Panel_Title = "我的閱讀認證";
            user = UserProfileHelper.Get();

            if (user == null)
            {
                return RedirectToAction("SessionTimeOutError", "Error");
            }

            var MyADDT06List = db.ADDT06.Where(a => a.SCHOOL_NO == user.SCHOOL_NO && a.USER_NO == user.USER_NO
            && a.APPLY_STATUS != ADDT06.APPLY_STATUS_Val.APPLY_TYPE_DEL && a.APPLY_STATUS != ADDT06.APPLY_STATUS_Val.APPLY_TYPE_Back).OrderByDescending(a => a.APPLY_NO);

            Data.ADDT06List = MyADDT06List.ToPagedList(Data.Page > 0 ? Data.Page - 1 : 0, 1);

            var Img = Data.ADDT06List.ToList();

            var ImgList = new EcoolWeb.Controllers.ADDTController().GetImageDictionaryUrl(Img);

            ViewBag.ImageUrl = ImgList;

            //預設顯示老師批閱的文章
            if (Data.ADDT06List.Count() != 0 && string.IsNullOrWhiteSpace(Data.ADDT06List.FirstOrDefault().REVIEW_VERIFY) == false)
                ViewBag.ShowOriginalArticle = (ShowOriginal == true) ? "O" : "V";

            return View(Data);
        }

        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult BorrowIndex(SECI01BorrowIndexViewModel model)
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "我的借閱資料";
            return View();
        }

        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult _PageBorrow(SECI01BorrowIndexViewModel model)
        {
            ViewBag.BRE_NO = Bre_NO;
            user = UserProfileHelper.Get();

            if (model.Search == null) model.Search = new SECI01BorrowSearchViewModel();

            if (user == null)
            {
                return RedirectToAction("SessionTimeOutError", "Error");
            }

            if (string.IsNullOrWhiteSpace(model.SCHOOL_NO))
            {
                model.SCHOOL_NO = user.SCHOOL_NO;
            }

            if (string.IsNullOrWhiteSpace(model.USER_NO))
            {
                model.USER_NO = user.USER_NO;
            }

            string IDNO = db.HRMT01.Where(a => a.USER_NO == model.USER_NO && a.SCHOOL_NO == model.SCHOOL_NO).Select(a => a.IDNO).FirstOrDefault();

            if (string.IsNullOrWhiteSpace(IDNO))
            {
                return Content("<h2 style='color:darkred'>找不到您的閱讀紀錄。</h2>");
                //return RedirectToAction(ErrorHelper.ErrorVal.NotFindError, "Error");
            }

            string sSQL = @" select a.*
            from DB2_L_WORK a (nolock)
            where a.NO_READ=@NO_READ
            order by a.BORROW_DATE desc";
            var QTemp = db.Database.Connection.Query<SECI01BorrowDataViewModel>(sSQL, new { NO_READ = IDNO });

            model.PageSize = 20;
            model.ListData = QTemp.ToPagedList(model.Search.Page > 0 ? model.Search.Page - 1 : 0, model.PageSize);

            return PartialView(model);
        }

        public ActionResult MyPhoto2(SECI01MyPhotoViewModel model)
        {
            ViewBag.BRE_NO = Bre_NO;
            //user = UserProfileHelper.Get();

            //if (user == null)
            //{
            //    return RedirectToAction("SessionTimeOutError", "Error");
            //}
            if (model == null) model = new SECI01MyPhotoViewModel();

            var userModel = db.HRMT01.Where(a => a.SCHOOL_NO == model.SCHOOL_NO && a.USER_NO == model.USER_NO && a.USER_STATUS == UserStaus.Enabled).FirstOrDefault();
            model.PHOTO = userModel.PHOTO;
            var classModel = db.HRMT03.SingleOrDefault(c => c.SCHOOL_NO == model.SCHOOL_NO && c.CLASS_NO == userModel.CLASS_NO);
            model.SYEAR_START = Convert.ToString(userModel.SYEAR) + "/09/01";
            model.SYEAR_END = (Convert.ToInt16(userModel.USER_NO.Substring(0, 3)) + 6) + "/07/01";
            model.CLASSNAME = classModel.CLASSNAME;
            model.SEAT_NO = userModel.SEAT_NO;
            model.CurrentClassTeacherName = db.HRMT01.Where(h => h.SCHOOL_NO == model.SCHOOL_NO && h.USER_NO == classModel.TEACHER_NO).FirstOrDefault().NAME;
            model.USER_TYPE = userModel.USER_TYPE;
            if (!string.IsNullOrWhiteSpace(model.PHOTO))
            {
                model.PhotoPath = Service.GetDirectorySysMyPhotoPath(model.SCHOOL_NO, model.USER_NO, model.PHOTO);
            }

            return View(model);
        }

        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult MyPhoto(SECI01MyPhotoViewModel model)
        {
            ViewBag.BRE_NO = Bre_NO;
            user = UserProfileHelper.Get();

            if (user == null)
            {
                return RedirectToAction("SessionTimeOutError", "Error");
            }
            if (model == null) model = new SECI01MyPhotoViewModel();

            var userModel = db.HRMT01.Where(a => a.SCHOOL_NO == user.SCHOOL_NO && a.USER_NO == user.USER_NO && a.USER_STATUS == UserStaus.Enabled).FirstOrDefault();
            if (user.USER_TYPE == "P") {
               var ParsentModel= db.HRMT06.Where(a => a.SCHOOL_NO == user.SCHOOL_NO && a.PARENTS_USER_NO == user.USER_NO).FirstOrDefault();

              userModel = db.HRMT01.Where(a => a.SCHOOL_NO == user.SCHOOL_NO && a.USER_NO == ParsentModel.STUDENT_USER_NO && a.USER_STATUS == UserStaus.Enabled).FirstOrDefault();
            }
         

            model.PHOTO = userModel.PHOTO;
            var classModel = db.HRMT03.SingleOrDefault(c => c.SCHOOL_NO == user.SCHOOL_NO && c.CLASS_NO == userModel.CLASS_NO);
            model.SYEAR_START = Convert.ToString(userModel.SYEAR) + "/09/01";
            if (user.USER_TYPE == "P")
            {
                model.SYEAR_END = (Convert.ToInt16(userModel.USER_NO.Substring(1, 4)) + 6) + "/07/01";

            }
            else {
                model.SYEAR_END = (Convert.ToInt16(userModel.USER_NO.Substring(0, 3)) + 6) + "/07/01";
            }
        
            model.CLASSNAME = classModel.CLASSNAME;
            model.SEAT_NO = userModel.SEAT_NO;
            model.CurrentClassTeacherName = db.HRMT01.Where(h => h.SCHOOL_NO == user.SCHOOL_NO && h.USER_NO == classModel.TEACHER_NO).FirstOrDefault().NAME;

            if (!string.IsNullOrWhiteSpace(model.PHOTO))
            {
                model.PhotoPath = Service.GetDirectorySysMyPhotoPath(user.SCHOOL_NO, userModel.USER_NO, model.PHOTO);
            }

            return View(model);
        }

        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        [ValidateAntiForgeryToken]
        public ActionResult SavePhoto(SECI01MyPhotoViewModel model)
        {
            ViewBag.BRE_NO = Bre_NO;
            user = UserProfileHelper.Get();

            if (user == null)
            {
                return RedirectToAction("SessionTimeOutError", "Error");
            }
            if (model == null) model = new SECI01MyPhotoViewModel();
            string Message = string.Empty;

            bool OK = Service.UpLoadPhotoFile(user.SCHOOL_NO, user.USER_NO, model.File, ref db, ref Message);

            if (OK)
            {
                TempData["StatusMessage"] = "上傳完成";
                return RedirectToAction("MyPhoto");
            }
            else
            {
                TempData["StatusMessage"] = Message;
                return View("MyPhoto", model);
            }
        }

        public ActionResult DelPhoto()
        {
            ViewBag.BRE_NO = Bre_NO;
            user = UserProfileHelper.Get();

            if (user == null)
            {
                return RedirectToAction("SessionTimeOutError", "Error");
            }
            string Message = string.Empty;

            bool OK = Service.DelPhotoData(user.SCHOOL_NO, user.USER_NO, ref db, ref Message);

            if (OK)
            {
                TempData["StatusMessage"] = "刪除完成";
                return RedirectToAction("MyPhoto");
            }
            else
            {
                TempData["StatusMessage"] = Message;
                return RedirectToAction("MyPhoto");
            }
        }

        public static class REF_BRE_NO_VAL
        {
            /// <summary>
            /// 我的秘書
            /// </summary>
            public static string C_SECI01 = "SECI01";

            /// <summary>
            /// 學習成果匯出
            /// </summary>
            public static string C_ZZI09 = "ZZI09";

            /// <summary>
            /// MobileHome
            /// </summary>
            public static string C_MobileHome = "MobileHome";
        }
    }
}