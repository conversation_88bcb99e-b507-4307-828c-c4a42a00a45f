﻿body {
    /*margin:10px 15px 10px 10px;
    padding-top: 10px;*/
    padding-bottom: 10px;
}

/* Override the default bootstrap behavior where horizontal description lists 
   will truncate terms that are too long to fit in the left column 
*/
/*.dl-horizontal dt {
    white-space: normal;
}*/

/* Set width on the form input elements since they're 100% wide by default */
input,
select,
textarea {
    max-width: 580px;
    max-height:1000px;
}

.navbar-inverse {
    /*width: 1024px;*/
    height: 72px;
    background-repeat: no-repeat;
    background-size: 100% 100%;
}

.content-Horn{
    width: 72px;
    height: 55px;
    margin-top: 16px;
    right: 388px;
    z-index:-1;
    position:absolute;
    background-image: url('../Content/img/web-student_png-09.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
}

.content-HornYellow{
    width: 55px;
    height: 97px;
    margin-top: 26px;
    right: 154px;
    position:absolute;
    background-image: url('../Content/img/web-student_png-10.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
}

#lnk_btn a{
    color:Blue;
    font-weight:bold;
    height:25px;
    width:120px;
}

.lnk_btn{
    color:Blue;
    font-weight:bold;
    height:25px;
    width:120px;
}

.spanFont{
    font-weight:bold;
    font-family:Microsoft JhengHei;
}

.btnSend {
    background-image: url('../img/web-student-submit-08.png');
    background-repeat: no-repeat;
    border:0;
    height:41px;
    width:100px;
}

.btnCancel {
    background-image: url('../img/web-student-submit-09.png');
    background-repeat: no-repeat;
    border:0;
    height:41px;
    width:100px;
}

.pager
{
    margin: 8px 3px;
    padding: 3px;
}
 

.pager .current
{
    background-color: #6ea9bf;
    border: 1px solid #6e99aa;
    color: #fff;
    font-weight: bold;
    margin-top: 4px;
    padding: 3px 5px;
    text-align: center;
}
 
.pager span, .pager a
{
    margin: 4px 3px;
}
 
.pager a
{
    border: 1px solid #aaa;
    padding: 3px 5px;
    text-align: center;
    text-decoration: none;
}
/* 線上投稿等類似畫面共用 */
    .TitleBarDiv {
        width: 570px;
        height: 10px;
        background-color: #FFFFEC;
        position: relative;
        top: -20px;
        left: 15px;
        z-index: -1;
    }

    .ListDiv {
        width: 570px;
        top: -20px;
        left: 15px;
        position: relative;
    }

    .ListColName {
        color: #004DA0;
        font-family: microsoft jhenghei;
        font-size: 12pt;
        font-weight: bold;
        border-top-style: none;
        background-color: #FFFFEC;
    }

    .ListRow {
        text-align: center;
    }

    .ListRow:nth-child(even) {
        background-color: #EBEBC8;
    }

    .ListRow:nth-child(odd) {
        background-color: #FFFFEC;
    }

    .ListRow:hover {
        color:#e9a00b;
    }
    .ListRow_Link:hover {
        color:#e9a00b;
    }

    .DetailTitleBarDiv {
        width: 570px;
        height: 30px;
        background-color: #F1EDE3;
        position: relative;
        top: -20px;
        left: 15px;
        z-index: -1;
    }
    .DetailDiv {
        width: 570px;
        top: -45px;
        left: 15px;
        position: relative;
        background-color: #F1EDE3;
        text-align:left;
        margin:15px;
    }

    .LinkButton
    {
        margin-top:5px;
        padding:3px;
        border-radius: 6px;
    }