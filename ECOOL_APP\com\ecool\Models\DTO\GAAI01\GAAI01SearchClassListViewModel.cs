﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class GAAI01SearchClassListViewModel
    {
        /// <summary>
        ///防身周期ID
        /// </summary>
        [DisplayName("防身周期ID")]
        public string ALARM_ID { get; set; }

        /// <summary>
        ///週期
        /// </summary>
        [DisplayName("週期")]
        public byte? CYCLE { get; set; }

        /// <summary>
        ///登記日期_開始
        /// </summary>
        [DisplayName("週期日期_開始")]
        [DataType(DataType.Date), DisplayFormat(DataFormatString = "{0:yyyy/MM/dd}", ApplyFormatInEditMode = true)]
        public DateTime? ALARM_DATES { get; set; }

        /// <summary>
        ///登記日期_結束
        /// </summary>
        [DisplayName("週期日期_結束")]
        [DataType(DataType.Date), DisplayFormat(DataFormatString = "{0:yyyy/MM/dd}", ApplyFormatInEditMode = true)]
        public DateTime? ALARM_DATEE { get; set; }

        public string CLASS_NO { get; set; }

        /// <summary>
        ///當記當下總學生人數
        /// </summary>
        [DisplayName("學生人數")]
        public int? STUDENT_NUMBER { get; set; }

        /// <summary>
        ///配戴人數
        /// </summary>
        [DisplayName("配戴人數")]
        public int? WEAR_NUMBER { get; set; }

        [DisplayName("未配戴人數")]
        public int? UN_WEAR_NUMBER { get; set; }

        /// <summary>
        ///配戴率
        /// </summary>
        [DisplayName("配戴率")]
        public decimal? WEAR_RATE { get; set; }

        /// <summary>
        /// 未配載原因備註
        /// </summary>
        [DisplayName("未配載原因備註")]
        public string UN_WEAR_MEMO { get; set; }
    }
}