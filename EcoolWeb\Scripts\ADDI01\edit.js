// Modern jQuery code for ADDI01 Edit page
$(document).ready(function() {
    // YouTube URL 檢查功能
    const youtubeChecker = {
        init: function() {
            this.bindEvents();
        },

        bindEvents: function() {
            $('#CheckYoutube').on('click', this.checkYoutubeUrl.bind(this));
        },

        checkYoutubeUrl: function() {
            const youtubeUrl = $('#YOUTUBE_URL').val();
            
            $.ajax({
                url: window.ADDI01_URLS.getUrlArgument,
                type: 'post',
                data: { StrUrl: youtubeUrl },
                dataType: 'json',
                cache: false,
                success: this.handleYoutubeResponse.bind(this),
                error: this.handleYoutubeError.bind(this)
            });
        },

        handleYoutubeResponse: function(data) {
            try {
                const res = jQuery.parseJSON(data);
                
                if (res.Success == 0) {
                    $('#YOUTUBE_URL').val('');
                    alert(res.Error);
                } else if (res.Success == 1) {
                    alert('正確');
                    $('#YOUTUBE_URL').val(res.EmbedYouTubeUrl);
                }
            } catch (e) {
                console.error('YouTube response parsing error:', e);
                alert('處理YouTube回應時發生錯誤');
            }
        },

        handleYoutubeError: function(xhr, err) {
            console.error('YouTube check error:', xhr, err);
            alert("readyState: " + xhr.readyState + "\nstatus: " + xhr.status);
            alert("responseText: " + xhr.responseText);
        }
    };

    // 文字計數功能
    const textCounter = {
        init: function() {
            this.bindEvents();
            this.updateCount();
            // 將函數暴露到全局作用域供 HTML 調用
            window.KeyIn = this.updateCount.bind(this);
        },

        bindEvents: function() {
            $('#ARTICLE').on("keyup change", this.updateCount.bind(this));
        },

        updateCount: function() {
            // 注意：這裡使用 .val() 而不是 .text()，因為是 textarea
            const text = $("#ARTICLE").val();
            const textWithoutWhitespace = text.replace(/[\n\s]/g, "");
            const length = textWithoutWhitespace.length;
            $("#ShowFontLen").text(length);
        }
    };

    // 文章作廢功能
    const articleDisabler = {
        init: function() {
            window.DisableGO = this.disableArticle.bind(this);
        },

        disableArticle: function(buttonElement, actionUrl) {
            const errorMsg = '';
            buttonElement.disabled = true;

            if (errorMsg !== '') {
                buttonElement.disabled = false;
                alert(errorMsg);
                return false;
            } else {
                const confirmed = confirm("你確定要「作廢」這篇文章?");
                if (confirmed) {
                    const form = document.getElementById('ADDI01Form');
                    form.action = actionUrl;
                    form.submit();
                } else {
                    buttonElement.disabled = false;
                    return false;
                }
            }
        }
    };

    // 表單提交功能
    const formSubmission = {
        init: function() {
            window.TempSave = this.tempSave.bind(this);
        },

        tempSave: function() {
            const form = $("#ADDI01Form");
            $('<input>').attr({
                type: 'hidden',
                name: 'tempSave',
                value: true
            }).appendTo(form);

            form.submit();
        }
    };

    // 文件刪除功能
    const fileDeleter = {
        init: function() {
            window.DeleteMyImage = this.deleteImage.bind(this);
            window.DeleteMyOtherFile = this.deleteOtherFile.bind(this);
        },

        deleteImage: function(element, imageName) {
            const oldText = $("#DeleteImage").val();
            $("#DeleteImage").val(oldText + imageName + "|");
            element.closest('div').remove();
            console.log("Deleted images:", $("#DeleteImage").val());
        },

        deleteOtherFile: function(element, fileName) {
            const oldText = $("#DeleteOtherFile").val();
            $("#DeleteOtherFile").val(oldText + fileName + "|");
            element.closest('div').remove();
            console.log("Deleted files:", $("#DeleteOtherFile").val());
        }
    };

    // 初始化所有功能
    youtubeChecker.init();
    textCounter.init();
    articleDisabler.init();
    formSubmission.init();
    fileDeleter.init();

    // 設置全局 URL 配置（需要在 CSHTML 中定義）
    window.ADDI01_URLS = window.ADDI01_URLS || {};

    // 頁面加載完成後的額外初始化
    $(window).on('load', function() {
        // 確保文字計數功能在頁面完全加載後執行
        textCounter.updateCount();
    });
});
