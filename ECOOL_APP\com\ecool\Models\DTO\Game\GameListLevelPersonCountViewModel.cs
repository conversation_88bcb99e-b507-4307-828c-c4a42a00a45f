﻿using DotNet.Highcharts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class GameListLevelPersonCountViewModel
    {

        public string GAME_NO { get; set; }

        /// <summary>
        /// 是否統計報表
        /// </summary>
        public bool IsStatistics { get; set; }

        /// <summary>
        /// 報名人數
        /// </summary>
        public double Total_Count { get; set; }


        public double Student_Total_Count { get; set; }

        public double Other_Total_Count { get; set; }

        public GameUseTotalViewModel UseTotal { get; set; }

        /// <summary>
        /// 此關卡闖關人數
        /// </summary>
        public List<GameLevelPersonCountViewModel> ListData { get; set; }


        /// <summary>
        /// 此關卡闖關次數
        /// </summary>
        public List<GameLevelPersonCountViewModel> ListNumberCountData { get; set; }


        /// <summary>
        /// 過關數 完成1關...2..3關
        /// </summary>
        public List<GameLevelCountPassPersonViewModel> ListCountPassData { get; set; }


        /// <summary>
        /// 過關數 完成1關...2..3關 趨示圖
        /// </summary>
        public Highcharts CountPassCharts;






    }
}
