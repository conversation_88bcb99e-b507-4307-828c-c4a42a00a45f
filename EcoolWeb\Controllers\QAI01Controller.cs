﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Web;
using System.Web.Mvc;
using System.Web.Script.Serialization;
using EcoolWeb.Models;
using ECOOL_APP;
using ECOOL_APP.EF;
using com.ecool.service;
using MvcPaging;
using EcoolWeb.CustomAttribute;

namespace EcoolWeb.Controllers
{
    [SessionExpire]
    public class QAI01Controller : Controller
    {
        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();
        private bool AppMode = string.IsNullOrEmpty(EcoolWeb.Models.UserProfileHelper.GetUUID()) == false;

        // GET: QAI01
        public ActionResult Index(QAI01IndexViewModel model)
        {
            if (model == null) model = new QAI01IndexViewModel();
            if (AppMode) model.PageSize = 15;
            model.BackAction = "Index";

            string SchoolNO = UserProfileHelper.GetSchoolNo();
            UserProfile user = UserProfileHelper.Get();
            string UserNO = string.Empty;
            if (user != null) UserNO = user.USER_NO;

            var PermissionBtn = PermissionService.GetActionPermissionForBreNO("QAI01", SchoolNO, UserNO);
            ViewBag.VisibleADD = (PermissionBtn.Where(a => a.ActionName == "Create").Select(a => a.BoolUse).FirstOrDefault() == true ? "Y" : "N");

            var list = db.QAT01.Include("QAT02").Where(a => a.SCHOOL_NO == SchoolNO && a.Q_STATUS != "9");
            if (string.IsNullOrWhiteSpace(model.whereKeyword) == false)
            {
                list = list.Where(a => a.MEDIAS_NAME.Contains(model.whereKeyword) || a.MEMO.Contains(model.whereKeyword));
            }

            if (user != null)
            {
                if (user.USER_TYPE == UserType.Student)
                {
                    list = list.Where(q1 => q1.QAT02.Any(q2 => q2.A_STATUS != "2" && q2.A_USER_NO == user.USER_NO));
                }
            }

            list = list.OrderByDescending(a => a.CRE_DATE);

            model.QAT01List = list.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, model.PageSize); ;

            return View(model);
        }

        public ActionResult Finish(string QUESTIONS_ID)
        {
            string SchoolNO = UserProfileHelper.GetSchoolNo();
            UserProfile user = UserProfileHelper.Get();
            string UserNO = string.Empty;
            if (user != null) UserNO = user.USER_NO;

            QAT02 Q2 = db.QAT02.Where(q2 => q2.QUESTIONS_ID == QUESTIONS_ID && q2.A_USER_NO == UserNO).FirstOrDefault();
            Q2.A_STATUS = "2";
            db.SaveChanges();

            return RedirectToAction("Index");
        }

        public ActionResult Details(string QUESTIONS_ID, QAI01IndexViewModel model)
        {
            QAT01 Q1 = db.QAT01.Include("QAT02").Where(a => a.QUESTIONS_ID == QUESTIONS_ID).FirstOrDefault();
            if (Q1 == null)
            {
                return HttpNotFound();
            }

            if (model == null) model = new QAI01IndexViewModel();
            TempData["Search"] = model;
            ViewBag.BackAction = model.BackAction;
            ViewBag.OrdercColumn = model.OrdercColumn;
            ViewBag.whereKeyword = model.whereKeyword;
            ViewBag.whereWritingStatus = model.whereWritingStatus;
            ViewBag.whereCLASS_NO = model.whereCLASS_NO;
            ViewBag.whereGrade = model.whereGrade;
            ViewBag.Page = model.Page;

            return View(Q1);
        }

        public ActionResult PlasyVideo(string QUESTIONS_ID)
        {
            QAT01 Q1 = db.QAT01.Include("QAT02").Where(a => a.QUESTIONS_ID == QUESTIONS_ID).FirstOrDefault();
            if (Q1 == null)
            {
                return HttpNotFound();
            }

            return View(Q1);
        }

        public ActionResult Create(QAI01CreateViewModel model)
        {
            if (model == null)
            {
                model = new QAI01CreateViewModel();
                model.Page = 1;
            }
            if (model.Page == 0) model.Page = 1;

            string url = @"http://*************/coocLearning/coocAPI/shareVideos/";

            url += model.Page.ToString();
            if (string.IsNullOrWhiteSpace(model.whereKeyword) == false)
            {
                url += "?keyword=" + Server.HtmlEncode(model.whereKeyword);
            }

            WebClient webClient1 = new WebClient();
            string JsonString = webClient1.DownloadString(url);

            //JavaScriptSerializer serializer = new JavaScriptSerializer();
            //List<COOC_Videos> VideosInfo = serializer.Deserialize(JsonString, typeof(List<COOC_Videos>)) as List<COOC_Videos>;

            COOC_Videos VideosInfo = JsonConvert.DeserializeObject<COOC_Videos>(JsonString);

            model.MediasList = VideosInfo.medias;
            model.total_pages = VideosInfo.total_pages;

            return View(model);
        }

        public ActionResult Create2(QAI01CreateViewModel model)
        {
            if (string.IsNullOrEmpty(model.QUESTIONS_ID))
                model.QUESTIONS_ID = DateTime.Now.ToString("yyyyMMddhhmmss");

            return View(model);
        }

        public ActionResult Create3(QAI01CreateViewModel model)
        {
            UserProfile user = UserProfileHelper.Get();

            List<APPT03_Q> Members = db.APPT03_Q.Where(q => q.REF_TABLE == "QAT01" && q.REF_KEY == model.QUESTIONS_ID).ToList();

            //主檔
            QAT01 Q1 = db.QAT01.Create();
            Q1.QUESTIONS_ID = model.QUESTIONS_ID;
            Q1.MEMO = model.MEMO;
            Q1.END_DATE = model.END_DATE;
            Q1.CRE_DATE = DateTime.Now;
            Q1.SCHOOL_NO = user.SCHOOL_NO;
            Q1.Q_USER_NO = user.USER_NO;
            Q1.Q_USER_NAME = user.NAME;

            Q1.MEDIAS_CONTENTLINK = model.SelectMedias_contentLink;
            Q1.MEDIAS_ID = model.SelectMedias_id;
            Q1.MEDIAS_NAME = model.SelectMedias_name;
            Q1.MEDIAS_THUMBNAIL = model.SelectMedias_thumbnail;

            //細項
            string BODY_TXT = "影片作業:" + model.SelectMedias_name + "，作業說明:" + model.MEMO;

            string ErrorMsg;
            List<APPT03> APPT03List = PushService.QAppt03ToAPPT03("QAT01", model.QUESTIONS_ID, model.QUESTIONS_ID, ref db, user, out ErrorMsg, BODY_TXT);

            foreach (APPT03 a3 in APPT03List)
            {
                QAT02 q2 = db.QAT02.Create();
                q2.SCHOOL_NO = a3.SCHOOL_NO;
                q2.A_USER_NO = a3.USER_NO;
                q2.A_STATUS = "0";

                HRMT01 h1 = db.HRMT01.Where(h => h.SCHOOL_NO == a3.SCHOOL_NO && h.USER_NO == a3.USER_NO).FirstOrDefault();
                if (h1 != null)
                {
                    if (h1.USER_TYPE != UserType.Student) continue;

                    q2.CLASS_NO = h1.CLASS_NO;
                    q2.SEAT_NO = h1.SEAT_NO;
                    q2.SNAME = h1.SNAME;
                    q2.NAME = h1.NAME;
                }

                Q1.QAT02.Add(q2);
            }
            db.QAT01.Add(Q1);
            db.SaveChanges();

            if (APPT03List.Count() > 0)
            {
                string NewREF_KEY = APPT03List.FirstOrDefault().REF_KEY;
                PushService.APPT03ToPushData(APPT03List, out ErrorMsg, BODY_TXT, "QAI01", "Index", NewREF_KEY, "", "QAI01/Index");
            }

            return RedirectToAction("Index");
        }
    }
}