﻿
(function ($) {
    $.fn.validationEngineLanguage = function () { };
    $.validationEngineLanguage = {
        newLang: function () {
            $.validationEngineLanguage.allRules = {
                "custID": {
                    "regex": "none",
                    "alertText": "* 身份證字號輸入錯誤"
                },
                "copID": {
                    "regex": "none",
                    "alertText": "* 統一編號輸入錯誤"
                },
                "custIDorCopID": {
                    "regex": "none",
                    "alertText": "* 身份證字號或統一編號輸入長度有誤"
                },
                "ValidateDoubleSecialChar": {
                    "regex": "none",
                    "alertText": "* 請勿輸入特殊字元 [ /* ],[ */ ],[ -- ]"
                },
                "IsNumberInRange": { //正規表示式  正數最大3位，小數最大2位
                    "regex": "/^[-]?[0-9]{1,3}(\.[0-9]{0,2})?$/",
                    "alertText": "* 限制輸入正數最大3位，小數最大2位 ex:[ 123.12 ]"
                },
                "time": {
                    "regex": "/^(([0-9])|([0-1][0-9])|([2][0-3])):(([0-9])|([0-5][0-9]))$/",
                    "alertText": "* 時間格式錯誤！ex:[12:34]"
                },
                "dateCheck": {              // 驗証輸入日期是否存在
                    "regex": "none",
                    "alertText": "* 日期格式為",
                    "alertTextTarget": "* 所比對的日期不存在",
                    "alertTextGreater": "* 日期「年」過於大 "
                },
                "dateDiff": {
                    "regex": "none",
                    "alertText": "* 日期格式不對",
                    "alertTextTarget": "* 所比對的日期格式不對",
                    "alertTextGreater": "* 日期必須大於 ",
                    "alertTextLess": "* 日期必須小於 ",
                    "alertTextGreaterEqual": "* 日期必須大或等於 ",
                    "alertTextLessEqual": "* 日期必須小或等於 ",
                    "alertTextDay": " 天內",
                    "alertTextMonth": " 個月內",
                    "alertTextYear": " 年內"
                },
                "required": { // Add your regex rules here, you can take telephone as an example
                    "regex": "none",
                    "alertText": "* 這欄位是必填", //This field is required
                    "alertTextCheckboxMultiple": "* 請選擇一個選項", //Please select an option
                    "alertTextCheckboxe": "* 這選項是必需的"
                }, //This checkbox is required
                "length": {
                    "regex": "none",
                    "alertText": "* 字元長度介於 ", //Between
                    "alertText2": " 至 ", //and
                    "alertText3": " 之間", //characters allowed
                    "alertText4": "* 限定 ",
                    "alertText5": " 個字元",
                    "alertText6": "* 最少 ",
                    "alertText7": "* 最多 "
                },
                "maxCheckbox": {
                    "regex": "none",
                    "alertText": "* 選擇已超出所允許"
                }, //Checks allowed Exceeded	
                "minCheckbox": {
                    "regex": "none",
                    "alertText": "* 請選", //Please select
                    "alertText2": " 個選項"
                }, //options
                "confirm": {
                    "regex": "none",
                    "alertText": "* 你的欄位資料不吻合"
                }, //Your field is not matching	
                "telephone": {
                    "regex": "/^[0-9\-\(\)\ ]+$/",
                    "alertText": "* 無效的電話號碼"
                }, //Invalid phone number
                "telandacd": {
                    "regex": "none",
                    "alertText": "* 無效的電話號碼"
                }, //Invalid phone number
                "cellphone": {
                    "regex": "/[0][9][0-9]{8}/",
                    "alertText": "* 無效的手機號碼"
                }, //Invalid cellphone number
                "email": {
                    "regex": "/^[a-zA-Z0-9_\.\-]+\@([a-zA-Z0-9\-]+\.)+[a-zA-Z0-9]{2,4}$/",
                    "alertText": "* 錯誤的 E-mail 格式"
                }, //Invalid email address
                "date": {
                    "regex": "/^[0-9]{4}\-\[0-9]{1,2}\-\[0-9]{1,2}$/",
                    "alertText": "* 日期格式必需是 YYYY-MM-DD"
                }, //Invalid date, must be in YYYY-MM-DD format
                "onlyNumber": {
                    "regex": "/^[0-9\ ]+$/",
                    "alertText": "* 只能是數字"
                }, //Numbers only
                "number": {
                    "regex": "none",
                    "alertText": "* 只能是數值"
                },
                "noSpecialCaracters": {
                    "regex": "/^[0-9a-zA-Z]+$/",
                    "alertText": "* 不允許特殊字元"
                }, //No special characters allowed
                "NumMinusABC": {
                    "regex": "/^[0-9a-zA-Z\-]+$/",
                    "alertText": "請輸入[0~9]或[A~Z]或[-]!!"
                },
                "EGNOCaracters": {
                    "regex": "/^[0-9a-zA-Z\s \*]+$/",
                    "alertText": "不允EGNO以外的許特殊字元"
                },
                "ajaxUser": {
                    "file": "validateUser.php",
                    "extraData": "name=eric",
                    "alertTextOk": "* 這帳號可用", //This user is available
                    "alertTextLoad": "* 載入中請稍後", //Loading, please wait
                    "alertText": "* 這帳號已使用"
                }, //This user is already taken
                "ajaxName": {
                    "file": "validateUser.php",
                    "alertText": "* 這名稱已使用", //This name is already taken
                    "alertTextOk": "* 這名稱可用", //This name is available
                    "alertTextLoad": "* 載入中請稍後"
                }, //Loading, please wait
                "onlyLetter": {
                    "regex": "/^[a-zA-Z\ \']+$/",
                    "alertText": "* 只能是字母"
                }, //Letters only
                "seqnoDiff": {// 流水號比對
                    "regex": "none",
                    "alertText": "* 流水號格式不正確",
                    "alertTextTarget": "* 所比對的流水號格式不正確",
                    "alertTextGreater": "* 流水號不得大於 ",
                    "alertTextLess": "* 流水號必須小於 ",
                    "alertTextGreaterEqual": "* 流水號必須大或等於 ",
                    "alertTextLessEqual": "* 流水號必須小或等於 ",
                    "alertTextMiddle": " ",
                    "alertTextInterval": "號之內"
                },
                "intLimit": {// 限制輸入數字的大小
                    "regex": "none",
                    "alertText": "* 只能為數值格式",
                    "alertTextTarget": "* 所比對數值的格式不正確",
                    "alertTextGreater": "* 數值必須大於 ",
                    "alertTextLess": "* 數值必須小於 ",
                    "alertTextGreaterEqual": "* 數值必須大或等於 ",
                    "alertTextLessEqual": "* 數值必須小或等於 "
                },
                "checkCreditCARD": {// 檢查信用卡卡號
                    "regex": "none",
                    "alertText": "* 卡號格式不符合，<br/>國際編碼原則",
                    "alertTextTarget": "* 所比對卡號的格式不符合，<br/>國際編碼原則"
                },
                "stringDiff": {
                    "regex": "none",
                    "alertTextGreater": "* 必須大於 ",
                    "alertTextLess": "* 必須小於 ",
                    "alertTextGreaterEqual": "* 必須大或等於 ",
                    "alertTextLessEqual": "* 必須小或等於 "
                },
                "validate2fields": {
                    "nname": "validate2fields",
                    "alertText": "* 你必需有姓氏和名字"
                } //You must have a firstname and a lastname
            }
        }
    }
})(jQuery);

$(document).ready(function () {
    $.validationEngineLanguage.newLang();
});
