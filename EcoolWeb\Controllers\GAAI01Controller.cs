﻿using com.ecool.service;
using ECOOL_APP;
using ECOOL_APP.com.ecool.service;
using ECOOL_APP.com.ecool.util;
using ECOOL_APP.EF;
using EcoolWeb.CustomAttribute;
using EcoolWeb.Models;
using EcoolWeb.Util;
using log4net;
using NPOI.SS.UserModel;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace EcoolWeb.Controllers
{
    /// <summary>
    /// 防身警報器
    /// </summary>
    [SessionExpire]
    public class GAAI01Controller : Controller
    {
        /// <summary>
        /// 此Controller 對應的程式代碼
        /// </summary>
        private static string Bre_NO = "GAAI01";

        private string Bre_Name = BreadcrumbService.GetBRE_NAME_forBRE_NO(Bre_NO);

        /// <summary>
        /// 會員相關資訊
        /// </summary>
        private UserProfile user;

        private string SCHOOL_NO = string.Empty;
        private string USER_NO = string.Empty;

        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();
        private GAAT01Service gAAT01Service = new GAAT01Service();
        private GAAT02Service gAAT02Service = new GAAT02Service();
        private GAAT03Service gAAT03Service = new GAAT03Service();

        public ActionResult Index()
        {
            this.Shared();
            return View();
        }

        /// <summary>
        /// Menu
        /// </summary>
        /// <param name="NowAction"></param>
        /// <returns></returns>
        [CheckPermissionSeeion(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult _PageMenu(string NowAction)
        {
            this.Shared();

            ViewBag.Permission = PermissionService.GetActionPermissionForBreNO(UserProfileHelper.GetBRE_NO(), user?.SCHOOL_NO, user?.USER_NO)?.Where(a => a.BoolUse)?.ToList();
            ViewBag.NowAction = NowAction;
            return PartialView();
        }

        #region 管理者參數設定

        /// <summary>
        /// 管理者參數設定
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [CheckPermissionSeeion(CheckACTION_ID = "SysSetWearIndex")] //檢查權限
        public ActionResult SysSetWearIndex(GAAI01SysSetWearIndexViewModel model)
        {
            this.Shared("管理者參數設定");
            if (model == null) model = new GAAI01SysSetWearIndexViewModel();
            ModelState.Clear();
            model = gAAT03Service.GetSysSetWearData(model, SCHOOL_NO, ref db);

            return View(model);
        }

        [CheckPermissionSeeion(CheckACTION_ID = "SysSetWearIndex")] //檢查權限
        [ValidateAntiForgeryToken]
        [HttpPost]
        public ActionResult SysSetWearIndexSave(GAAI01SysSetWearIndexViewModel model)
        {
            this.Shared("管理者參數設定");

            string Message = string.Empty;

            if (model == null)
            {
                return RedirectToAction("SysSetWearIndex");
            }

            if (user.ROLE_TYPE <= HRMT24_ENUM.RoleTypeVal.ExceedLevel && model.FIRST_DAY == null)
            {
                ModelState.AddModelError(nameof(GAAI01SysSetWearIndexViewModel.FIRST_DAY), "開學日期未填入");
            }

            if (ModelState.IsValid == false)
            {
                Message = "警告!輸入的內容有錯誤";
            }
            else
            {
                var Result = this.gAAT03Service.SaveSysSetWearData(model, user, ref db);

                if (Result.Success)
                {
                    TempData[SharedGlobal.StatusMessageName] = "設定完成";

                    return RedirectToAction(nameof(GAAI01Controller.Index));
                }

                Message += Result.Message;
            }

            TempData[SharedGlobal.StatusMessageName] = Message;

#if DEBUG
            var errorsaa = ModelState
                        .Where(x => x.Value.Errors.Count > 0)
                        .Select(x => new { x.Key, x.Value.Errors })
                        .ToArray();
#endif

            return View(nameof(GAAI01Controller.SysSetWearIndex), model);
        }

        #endregion 管理者參數設定

        #region 有配戴感應登記

        /// <summary>
        /// 有配戴感應登記
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ActionResult TagWearIndex(GAAI01WearIndexViewModel model)
        {
            model.WhereWearModelType = GAAI01WearIndexViewModel.WearModelTypeVal.有配戴感應登記;

            var Title = Enum.GetName(typeof(GAAI01WearIndexViewModel.WearModelTypeVal), model.WhereWearModelType);
            this.Shared(Title);
            GetWearShowShared(model);
            return View(model);
        }

        public ActionResult TagWearDetails(GAAI01WearIndexViewModel model)
        {
            model.WhereWearModelType = GAAI01WearIndexViewModel.WearModelTypeVal.有配戴感應登記;

            var Title = Enum.GetName(typeof(GAAI01WearIndexViewModel.WearModelTypeVal), model.WhereWearModelType);
            this.Shared(Title);

            if (string.IsNullOrWhiteSpace(model.WhereCLASS_NO) || string.IsNullOrWhiteSpace(model.WhereALARM_ID))
            {
                return RedirectToAction("TagWearIndex");
            }

            GetWearShowShared(model);

            model.ALARM_DATE = gAAT01Service.GetCycleforData(model.WhereALARM_ID, ref db);
            return View(model);
        }

        public ActionResult _TagWear(GAAI01TagWearDetailsViewModel Item)
        {
            return PartialView(Item);
        }

        public ActionResult GetTagWearDetailsData(string CARD_NO, string CLASS_NO)
        {
            this.Shared();

            string Message = string.Empty;
            var model = gAAT02Service.GetHrmt01forCard(SCHOOL_NO, CLASS_NO, CARD_NO, ref db, ref Message);
            ViewBag.Message = Message;
            return PartialView("_TagWear", model);
        }

        #endregion 有配戴感應登記

        #region 未配戴點選登記

        [CheckPermissionSeeion(CheckACTION_ID = "WearIndex")] //檢查權限
        public ActionResult WearIndex(GAAI01WearIndexViewModel model)
        {
            model.WhereWearModelType = GAAI01WearIndexViewModel.WearModelTypeVal.未配戴點選登記;

            var Title = Enum.GetName(typeof(GAAI01WearIndexViewModel.WearModelTypeVal), model.WhereWearModelType);
            this.Shared(Title);

            GetWearShowShared(model);

            return View(model);
        }

        private void GetWearShowShared(GAAI01WearIndexViewModel model)
        {
            //取出學年度及學期
            int SYear;
            int Semesters;
            SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);

            if (gAAT01Service.IsAlarmDateData((byte)SYear, (byte)Semesters, ref db) == false)
            {
                TempData[SharedGlobal.StatusMessageName] = "這學期開學日未設定，請連絡「維護管理者」。";
            }

            if (string.IsNullOrWhiteSpace(model.WhereCLASS_NO))
            {
                model.WhereCLASS_NO = user?.TEACH_CLASS_NO;
            }

            if (string.IsNullOrWhiteSpace(model.WhereALARM_ID))
            {
                model.WhereALARM_ID = gAAT01Service.GetCycleforID((byte)SYear, (byte)Semesters, ref db, DateTime.Now);
            }

            var ClassItems = HRMT01.GetClassListData(SCHOOL_NO, model.WhereCLASS_NO, ref db)
            .Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.WhereCLASS_NO });

            if (!string.IsNullOrWhiteSpace(user?.TEACH_CLASS_NO))
            {
                ClassItems = ClassItems.Where(a => a.Value == user.TEACH_CLASS_NO).ToList();
            }

            ViewBag.ClassItems = ClassItems;

            ViewBag.AlarmCycleSelectItems = gAAT01Service.GetAlarmCycleSelectItem((byte)SYear, (byte)Semesters, ref db, model.WhereALARM_ID);

            if (model.IsSearch == 1)
            {
                if (string.IsNullOrWhiteSpace(model.WhereCLASS_NO))
                {
                    ModelState.AddModelError(nameof(GAAI01WearIndexViewModel.WhereCLASS_NO), "*請選擇班級…");
                    ModelState.Remove(nameof(GAAI01WearIndexViewModel.IsSearch));
                    model.IsSearch = 0;
                }

                if (string.IsNullOrWhiteSpace(model.WhereALARM_ID))
                {
                    model.IsSearch = 0;
                }

                if (model.IsSearch == 1)
                {
                    if (model.WhereWearModelType == GAAI01WearIndexViewModel.WearModelTypeVal.未配戴點選登記)
                    {
                        var UserNoItems = HRMT01.GetUserNoSelectItems(SCHOOL_NO, model.WhereCLASS_NO, model?.USER_NOs, ref db);

                        ViewBag.UserNoItems = UserNoItems;
                        ModelState.Remove(nameof(GAAI01WearIndexViewModel.STUDENT_NUMBER));
                        model.STUDENT_NUMBER = UserNoItems.Count();
                    }

                    ModelState.Remove(nameof(GAAI01WearIndexViewModel.IsWearData));
                    model.IsWearData = gAAT02Service.IsWearData(SCHOOL_NO, model.WhereCLASS_NO, model.WhereALARM_ID, ref db);

                    model.gAAT01 = gAAT01Service.GetGAAt01forALARM_ID(model.WhereALARM_ID, ref db);
                }
                else
                {
                    model.IsWearData = false;
                }
            }
        }

        #endregion 未配戴點選登記

        public ActionResult AllWearIndex(GAAI01WearIndexViewModel model)
        {
            model.WhereWearModelType = GAAI01WearIndexViewModel.WearModelTypeVal.全部都配戴登記;
            var Title = Enum.GetName(typeof(GAAI01WearIndexViewModel.WearModelTypeVal), model.WhereWearModelType);
            this.Shared(Title);
            GetWearShowShared(model);
            return View(model);
        }

        #region 確認未配戴原因畫面 及 資料處理

        [CheckPermissionSeeion(CheckACTION_ID = "WearIndex,TagWearIndex,AllWearIndex")] //檢查權限
        public ActionResult UnWearMemo(GAAI01WearIndexViewModel model)
        {
            var Title = Enum.GetName(typeof(GAAI01WearIndexViewModel.WearModelTypeVal), model.WhereWearModelType);
            this.Shared(Title);

            model.ALARM_DATE = gAAT01Service.GetCycleforData(model.WhereALARM_ID, ref db);

            model = gAAT02Service.ConvertToWearUserMemo(model, SCHOOL_NO, ref db);

            return View(model);
        }

        [CheckPermissionSeeion(CheckACTION_ID = "WearIndex")] //檢查權限
        public ActionResult _WearDetail(GAAI01WearUserMemoViewModel Item)
        {
            return PartialView("_WearDetail", Item);
        }

        [CheckPermissionSeeion(CheckACTION_ID = "WearIndex")] //檢查權限
        [ValidateAntiForgeryToken]
        [HttpPost]
        public ActionResult UnWearMemoSave(GAAI01WearIndexViewModel model)
        {
            var Title = Enum.GetName(typeof(GAAI01WearIndexViewModel.WearModelTypeVal), model.WhereWearModelType);
            this.Shared(Title);
            List<Tuple<string, string, int>> valuesList = new List<Tuple<string, string, int>>();
            string Message = string.Empty;

            if (model.Users == null || (string.IsNullOrWhiteSpace(model.WhereALARM_ID) || string.IsNullOrWhiteSpace(model.WhereCLASS_NO)))
            {
                if (model.WhereWearModelType == GAAI01WearIndexViewModel.WearModelTypeVal.未配戴點選登記
                      || model.WhereWearModelType == GAAI01WearIndexViewModel.WearModelTypeVal.有配戴點選登記)
                {
                    return RedirectToAction("WearIndex");
                }
            }

            if (ModelState.IsValid == false)
            {
                Message = "警告!輸入的內容有錯誤";
            }
            else
            {
                var Result = this.gAAT02Service.SaveUnWearMemoData(model, user, ref db,ref valuesList);

                if (Result.Success)
                {
                    TempData[SharedGlobal.StatusMessageName] = $@"{model.WhereCLASS_NO} 班，登錄 {model.ALARM_DATE}成功，詳細明細請至「查詢班級登記」查詢";

                    return RedirectToAction(nameof(GAAI01Controller.SearchClassIndex)
                        , new
                        {
                            IsSearch = 1,
                            model.WhereCLASS_NO,
                            model.WhereSYEARSEMESTER
                        });
                }

                Message += Result.Message;
            }

            TempData[SharedGlobal.StatusMessageName] = Message;

#if DEBUG
            var errorsaa = ModelState
                        .Where(x => x.Value.Errors.Count > 0)
                        .Select(x => new { x.Key, x.Value.Errors })
                        .ToArray();
#endif

            return View(nameof(GAAI01Controller.UnWearMemo), model);
        }

        #endregion 確認未配戴原因畫面 及 資料處理

        #region 查詢班級登記

        /// <summary>
        /// 查詢班級登記
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [CheckPermissionSeeion(CheckACTION_ID = "SearchClassIndex")] //檢查權限
        public ActionResult SearchClassIndex(GAAI01SearchClassIndexViewModel model)
        {
            this.Shared("查詢班級登記");
            if (model == null) model = new GAAI01SearchClassIndexViewModel();

            //取出學年度及學期
            int SYear;
            int Semesters;
            SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);

            if (gAAT01Service.IsAlarmDateData((byte)SYear, (byte)Semesters, ref db) == false)
            {
                TempData[SharedGlobal.StatusMessageName] = "這學期開學日未設定，請連絡「維護管理者」。";
            }

            if (string.IsNullOrWhiteSpace(model.WhereSCHOOL_NO))
            {
                model.WhereSCHOOL_NO = SCHOOL_NO;
            }

            if (string.IsNullOrWhiteSpace(model.WhereSYEARSEMESTER))
            {
                model.WhereSYEARSEMESTER = $"{SYear}_{Semesters}";
            }

            var AlarmSyearSemesterItem = gAAT01Service.GetAlarmSyearSemester(ref db, model.WhereSYEARSEMESTER);

            if (AlarmSyearSemesterItem?.Count > 0)
            {
                if (!AlarmSyearSemesterItem.Where(a => a.Selected == true).Any())
                {
                    model.WhereSYEARSEMESTER = AlarmSyearSemesterItem.FirstOrDefault().Value;

                    AlarmSyearSemesterItem = AlarmSyearSemesterItem.Select(a =>
                    {
                        a.Selected = model.WhereSYEARSEMESTER == a.Value;
                        return a;
                    }).ToList();
                }

                string[] arrSyearSemester = model.WhereSYEARSEMESTER.Split('_');

                model.WhereSYEAR = Convert.ToByte(arrSyearSemester[0]);
                model.WhereSEMESTER = Convert.ToByte(arrSyearSemester[1]);
            }
            else
            {
                model.IsSearch = 0;
            }

            ViewBag.AlarmSyearSemesterItem = AlarmSyearSemesterItem;

            if (string.IsNullOrWhiteSpace(model.WhereCLASS_NO))
            {
                if (user?.TEACH_CLASS_NO != null)
                {
                    model.WhereCLASS_NO = user.TEACH_CLASS_NO;
                }
            }

            if (model.IsSearch == 1)
            {
                if (string.IsNullOrWhiteSpace(model.WhereCLASS_NO))
                {
                    ModelState.AddModelError(nameof(GAAI01SearchClassIndexViewModel.WhereCLASS_NO), "*請選擇班級…");
                    ModelState.Remove(nameof(GAAI01WearIndexViewModel.IsSearch));
                    model.IsSearch = 0;
                }

                if (!string.IsNullOrWhiteSpace(model.WhereCLASS_NO))
                {
                    model.gAAT01s = gAAT01Service.GetGAAT01s((byte)model.WhereSYEAR, (byte)model.WhereSEMESTER, ref db, DateTime.Now);

                    model.ClassMain = gAAT01Service.GetClassMain(model.WhereSCHOOL_NO, (byte)model.WhereSYEAR, (byte)model.WhereSEMESTER, model.WhereCLASS_NO, ref db);

                    model.ClassList = gAAT02Service.GetClassList((byte)model.WhereSYEAR, (byte)model.WhereSEMESTER, model.WhereSCHOOL_NO, model.WhereCLASS_NO, ref db);

                    model.WearDetailList = gAAT02Service.GetWearDetailList((byte)model.WhereSYEAR, (byte)model.WhereSEMESTER, model.WhereSCHOOL_NO, model.WhereCLASS_NO, false, ref db);
                }
            }

            var ClassItems = HRMT01.GetClassListData(model.WhereSCHOOL_NO, model.WhereCLASS_NO, ref db)
             .Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.WhereCLASS_NO });

            if (user?.TEACH_CLASS_NO != null)
            {
                ClassItems = ClassItems.Where(a => a.Value == user.TEACH_CLASS_NO).ToList();
            }

            ViewBag.ClassItems = ClassItems;

            return View(model);
        }

        [CheckPermissionSeeion(CheckACTION_ID = "SearchClassIndex")] //檢查權限
        public ActionResult _ShowClassIsWearList(string SCHOOL_NO, string ALARM_ID, byte IsWear)
        {
            var model = gAAT02Service.GetShowClassIsWearList(SCHOOL_NO, ALARM_ID, ref db).Where(a => a.IsWear == IsWear).ToList();

            return PartialView(model);
        }

        #endregion 查詢班級登記

        #region 督學月統計表

        [CheckPermissionSeeion(CheckACTION_ID = "MonthWearIndex")] //檢查權限
        /// <summary>
        /// 督學月統計表
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ActionResult MonthWearIndex(GAAI01MonthWearIndexViewModel model)
        {
            this.Shared("督學月統計表");
            if (model == null) model = new GAAI01MonthWearIndexViewModel();

            if (string.IsNullOrWhiteSpace(model.WhereSCHOOL_NO))
            {
                model.WhereSCHOOL_NO = SCHOOL_NO;
            }

            if (string.IsNullOrWhiteSpace(model.WhereYearMonth))
            {
                model.WhereYearMonth = DateTime.Now.ToString("yyyyMM");
            }

            var YearMonthItem = gAAT01Service.GetYearMonthItem(ref db, model.WhereYearMonth);

            if (YearMonthItem?.Count > 0)
            {
                if (!YearMonthItem.Where(a => a.Selected == true).Any())
                {
                    model.WhereYearMonth = YearMonthItem.OrderByDescending(a => a.Value).FirstOrDefault().Value;

                    YearMonthItem = YearMonthItem.Select(a =>
                    {
                        a.Selected = model.WhereYearMonth == a.Value;
                        return a;
                    }).ToList();
                }
            }

            ViewBag.YearMonthItem = YearMonthItem;

            model.dMT01 = db.BDMT01.Where(a => a.SCHOOL_NO == model.WhereSCHOOL_NO).FirstOrDefault();

            model.MonthMaxGradeWearrRate = gAAT02Service.GetMonthMaxGradeWearrRate(model.WhereYearMonth, model.WhereSCHOOL_NO, ref db);
            model.MonthMaxClassWearrRate = gAAT02Service.GetMonthMaxClassWearrRate(model.WhereYearMonth, model.WhereSCHOOL_NO, ref db);
            return View(model);
        }
        [CheckPermissionSeeion(CheckACTION_ID = "WeekWearIndex")]
        /// <summary>
        /// 督學月統計表
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ActionResult WeekWearIndex(GAAI01MonthWearIndexViewModel model)
        {
            this.Shared("督學週統計表");
            if (model == null) model = new GAAI01MonthWearIndexViewModel();

            if (string.IsNullOrWhiteSpace(model.WhereSCHOOL_NO))
            {
                model.WhereSCHOOL_NO = SCHOOL_NO;
            }
            if (string.IsNullOrWhiteSpace(model.Year))
            {
                var YearItem1 = gAAT01Service.GetYearItem(ref db, model.Year);

                if (YearItem1.Count() > 0)
                {
                    model.Year = YearItem1.FirstOrDefault();

                }
                else {

                    model.Year = (DateTime.Now.Year - 1911).ToString();
                }
              

            }
                if (string.IsNullOrWhiteSpace(model.WhereWeekNO))
            {
                model.WhereWeekNO = "1";
            }
            var YearItem =gAAT01Service.GetYear(ref db, model.Year);
           
            var WeekItem = gAAT01Service.GetWeekItem(ref db, model.WhereWeekNO,model.Year);

            if (WeekItem?.Count > 0)
            {
                if (!WeekItem.Where(a => a.Selected == true).Any())
                {
                    model.WhereWeekNO = WeekItem.OrderBy(a => a.Value).FirstOrDefault().Value;

                    WeekItem = WeekItem.Select(a =>
                    {
                        a.Selected = model.WhereWeekNO == a.Value;
                        return a;
                    }).OrderBy(a => a.Value).ToList();
                }
            }

            ViewBag.WeekItem = WeekItem.OrderBy(x=>Int32.Parse(x.Value));
            ViewBag.YearItem = YearItem;
            model.dMT01 = db.BDMT01.Where(a => a.SCHOOL_NO == model.WhereSCHOOL_NO).FirstOrDefault();

            model.MonthMaxGradeWearrRate = gAAT02Service.GetWeekMaxGradeWearrRate(model.WhereWeekNO, model.WhereSCHOOL_NO, model.Year, ref db);
            model.MonthMaxClassWearrRate = gAAT02Service.GetWeekMaxClassWearrRate(model.WhereWeekNO, model.WhereSCHOOL_NO, model.Year, ref db);
            return View(model);
        }

        [CheckPermissionSeeion(CheckACTION_ID = "MonthWearIndex")] //檢查權限
        /// <summary>
        /// 官方報表
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ActionResult Official(GAAI01MonthWearIndexViewModel model)
        {
            this.Shared("督學月統計表");
            if (model == null) model = new GAAI01MonthWearIndexViewModel();

            if (string.IsNullOrWhiteSpace(model.WhereSCHOOL_NO) || string.IsNullOrWhiteSpace(model.WhereYearMonth))
            {
                model.WhereSCHOOL_NO = SCHOOL_NO;
                return RedirectToAction(nameof(GAAI01Controller.MonthWearIndex));
            }
            model.dMT01 = db.BDMT01.Where(a => a.SCHOOL_NO == model.WhereSCHOOL_NO).FirstOrDefault();
            model.MonthMaxGradeWearrRate = gAAT02Service.GetMonthMaxGradeWearrRate(model.WhereYearMonth, model.WhereSCHOOL_NO, ref db);
            return View(model);
        }
        [CheckPermissionSeeion(CheckACTION_ID = "MonthWearIndex")] //檢查權限
        /// <summary>
        /// 官方報表
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ActionResult Official1(GAAI01MonthWearIndexViewModel model)
        {
            this.Shared("督學週統計表");
            if (model == null) model = new GAAI01MonthWearIndexViewModel();

            if (string.IsNullOrWhiteSpace(model.WhereSCHOOL_NO) || string.IsNullOrWhiteSpace(model.WhereYearMonth))
            {
                model.WhereSCHOOL_NO = SCHOOL_NO;
                return RedirectToAction(nameof(GAAI01Controller.MonthWearIndex));
            }
            if (model.WhereWeekNO == null) {

                model.WhereWeekNO = model.WhereYearMonth;
            }
            model.dMT01 = db.BDMT01.Where(a => a.SCHOOL_NO == model.WhereSCHOOL_NO).FirstOrDefault();
            model.MonthMaxGradeWearrRate = gAAT02Service.GetWeekMaxGradeWearrRate(model.WhereWeekNO, model.WhereSCHOOL_NO,model.Year, ref db);
            var WeekItem = gAAT01Service.GetWeekItem(ref db, model.WhereWeekNO, model.Year);

            if (WeekItem?.Count > 0)
            {
                if (!WeekItem.Where(a => a.Selected == true).Any())
                {
                    model.WhereWeekNO = WeekItem.OrderBy(a => a.Value).FirstOrDefault().Value;

                    WeekItem = WeekItem.Select(a =>
                    {
                        a.Selected = model.WhereWeekNO == a.Value;
                        return a;
                    }).OrderBy(a => a.Value).ToList();
                }
            }
         model.ShowONWeek= WeekItem.OrderBy(x => Int32.Parse(x.Value)).Select(x=>x.Text).FirstOrDefault();
            return View(model);
        }
        #endregion 督學月統計表

        #region 班級統計表

        [CheckPermissionSeeion(CheckACTION_ID = "ClassWearIndex")] //檢查權限
        /// <summary>
        /// 班級統計表
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ActionResult ClassWearIndex(GAAI01ClassWearIndexViewModel model)
        {
            this.Shared("班級統計表");
            if (model == null) model = new GAAI01ClassWearIndexViewModel();

            //取出學年度及學期
            int SYear;
            int Semesters;
            SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);

            if (gAAT01Service.IsAlarmDateData((byte)SYear, (byte)Semesters, ref db) == false)
            {
                TempData[SharedGlobal.StatusMessageName] = "這學期開學日未設定，請連絡「維護管理者」。";
            }

            if (string.IsNullOrWhiteSpace(model.WhereSCHOOL_NO))
            {
                model.WhereSCHOOL_NO = SCHOOL_NO;
            }

            if (string.IsNullOrWhiteSpace(model.WhereSYEARSEMESTER))
            {
                model.WhereSYEARSEMESTER = $"{SYear}_{Semesters}";
            }

            var AlarmSyearSemesterItem = gAAT01Service.GetAlarmSyearSemester(ref db, model.WhereSYEARSEMESTER);

            if (AlarmSyearSemesterItem?.Count > 0)
            {
                if (!AlarmSyearSemesterItem.Where(a => a.Selected == true).Any())
                {
                    model.WhereSYEARSEMESTER = AlarmSyearSemesterItem.FirstOrDefault().Value;

                    AlarmSyearSemesterItem = AlarmSyearSemesterItem.Select(a =>
                    {
                        a.Selected = model.WhereSYEARSEMESTER == a.Value;
                        return a;
                    }).ToList();
                }

                string[] arrSyearSemester = model.WhereSYEARSEMESTER.Split('_');

                model.WhereSYEAR = Convert.ToByte(arrSyearSemester[0]);
                model.WhereSEMESTER = Convert.ToByte(arrSyearSemester[1]);
            }
            else
            {
                model.IsSearch = 0;
            }

            ViewBag.AlarmSyearSemesterItem = AlarmSyearSemesterItem;

            if (model.IsSearch == 1)
            {
                model.dMT01 = db.BDMT01.Where(a => a.SCHOOL_NO == model.WhereSCHOOL_NO).FirstOrDefault();

                model.gAAT01s = gAAT01Service.GetGAAT01s((byte)model.WhereSYEAR, (byte)model.WhereSEMESTER, ref db, DateTime.Now);

                model.AvgClassData = gAAT02Service.GetAvgClassWearrRate((byte)model.WhereSYEAR, (byte)model.WhereSEMESTER, model.WhereSCHOOL_NO, ref db);

                model.ClassReportedData = gAAT02Service.GetClassReportedData((byte)model.WhereSYEAR, (byte)model.WhereSEMESTER, model.WhereSCHOOL_NO, ref db);

                model.SchoolRate = gAAT02Service.GetSchoolRateData((byte)model.WhereSYEAR, (byte)model.WhereSEMESTER, model.WhereSCHOOL_NO, ref db);

                model.UnWearMemoCount = gAAT02Service.GetUnWearMemoCount((byte)model.WhereSYEAR, (byte)model.WhereSEMESTER, model.WhereSCHOOL_NO, ref db);
            }

            return View(model);
        }

        #endregion 班級統計表

        #region 防身警報器-學校隨機抽查登記

        [CheckPermissionSeeion(CheckACTION_ID = "SpotCheckIndex")] //檢查權限
        /// <summary>
        ///防身警報器-學校隨機抽查登記
        /// </summary>
        /// <returns></returns>
        public ActionResult SpotCheckIndex()
        {
            this.Shared("學校隨機抽查登記");
            GAAI01SpotCheckIndexViewModel model = new GAAI01SpotCheckIndexViewModel();
            Session.Timeout = 600;
            var gAAT03 = gAAT03Service.GetGAAT03(SCHOOL_NO, ref db);

            if (gAAT03 != null)
            {
                model.SetCASH = (short)gAAT03.ALARM_CASH;
            }

            model.SetIs_WEAR = true;
            model.SetUN_WEAR_TYPE = GAAT02_U.UnWearType.其它;

            ViewBag.IsWearItgem = HtnlHelperService.YNSelectItem(model.SetIs_WEAR);

            return View(model);
        }

        [CheckPermissionSeeion(CheckACTION_ID = "SpotCheckIndex")] //檢查權限
        public ActionResult _SpotCheckDetails(GAAI01SpotCheckPeopleViewModel Item)
        {
            return PartialView(Item);
        }

        [CheckPermissionSeeion(CheckACTION_ID = "SpotCheckIndex")] //檢查權限
        public ActionResult SaveSpotCheckData(string CARD_NO, bool SetIs_WEAR, short SetCASH, GAAT02_U.UnWearType SetUN_WEAR_TYPE)
        {
            this.Shared();
            List<Tuple<string, string, int>> valuesList = new List<Tuple<string, string, int>>();

            string Message = string.Empty;
            var model = gAAT02Service.SaveSpotCheckData(SCHOOL_NO, CARD_NO, SetIs_WEAR, SetCASH, SetUN_WEAR_TYPE, user, ref db, ref Message,ref valuesList);
            ViewBag.Message = Message;
            return PartialView("_SpotCheckDetails", model);
        }

        #endregion 防身警報器-學校隨機抽查登記

        #region 學校隨機抽查查詢

        [CheckPermissionSeeion(CheckACTION_ID = "SpotCheckSearch")] //檢查權限
        public ActionResult SpotCheckSearch(GAAI0SpotCheckSearchViewModel model)
        {
            this.Shared("學校隨機抽查查詢");
            return View(model);
        }

        [CheckPermissionSeeion(CheckACTION_ID = "SpotCheckSearch")] //檢查權限
        public ActionResult SpotCheckSearchPrintExcel(GAAI0SpotCheckSearchViewModel model)
        {
            this.Shared("學校隨機抽查查詢");
            model.PageSize = int.MaxValue;
            model.Page = 1;
            model = gAAT02Service.GetSpotCheckSearch(model, ref db);
            DataTable DataTableExcel = model.ListData.ToList().AsDataTable();

            NPOIHelper npoi = new NPOIHelper();
            string TempleteFileFullName = Server.MapPath(@"~/Content/ExcelSample/SpotCheckSearchExportExcel.xlsx");

            FileStream xlsfile = new FileStream(TempleteFileFullName, FileMode.Open, FileAccess.Read);
            IWorkbook aBook = WorkbookFactory.Create(xlsfile);

            npoi.ExportExcelFromTemplete(DataTableExcel, aBook, "學校隨機抽查清單", false, 2);

            string strTMPFile = System.Web.Configuration.WebConfigurationManager.AppSettings["FilePath"] + @"\TMP\" + DateTime.Now.Date.ToString("yyyyMMdd");

            if (Directory.Exists(strTMPFile) == false)
            {
                Directory.CreateDirectory(strTMPFile);
            }

            strTMPFile = strTMPFile + @"\學校隨機抽查清單_" + Session.SessionID + ".xlsx";

            System.IO.FileStream FS = new FileStream(strTMPFile, FileMode.Create);
            aBook.Write(FS);
            FS.Close();

            return File(strTMPFile, "application/vnd.ms-excel", "學校隨機抽查清單.xlsx");//輸出檔案給Client端
        }

        [CheckPermissionSeeion(CheckACTION_ID = "SpotCheckSearch")] //檢查權限
        public ActionResult _PageSpotCheckSearch(GAAI0SpotCheckSearchViewModel model)
        {
            this.Shared();
            if (model == null) model = new GAAI0SpotCheckSearchViewModel();

            if (string.IsNullOrWhiteSpace(model.WhereSCHOOL_NO))
            {
                model.WhereSCHOOL_NO = SCHOOL_NO;
            }

            if (HtnlHelperService.IsPostBack() == false)
            {
                model.WhereALARM_DATEs = Convert.ToDateTime(DateTime.Now.AddMonths(-1).ToString("yyyy-MM-dd"));
                model.WhereALARM_DATEe = Convert.ToDateTime(DateTime.Now.ToString("yyyy-MM-dd"));
            }

            ViewBag.GradeItem = HRMT01.GetGradeItems(model.WhereGRADE.HasValue ? model.WhereGRADE.Value.ToString() : string.Empty);

            ViewBag.ClassItems = HRMT01.GetClassListData(SCHOOL_NO, model.WhereGRADE.HasValue ? model.WhereGRADE.Value.ToString() : string.Empty, model.WhereCLASS_NO, ref db)
            .Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.WhereCLASS_NO });

            model = gAAT02Service.GetSpotCheckSearch(model, ref db);
            return PartialView(model);
        }

        #endregion 學校隨機抽查查詢

        /// <summary>
        /// 關懷名單
        /// </summary>
        /// <returns></returns>
        [CheckPermissionSeeion(CheckACTION_ID = "CareList")] //檢查權限
        public ActionResult CareList(GAAI0CareListIndexViewModel model)
        {
            this.Shared("關懷名單");
            if (model == null) model = new GAAI0CareListIndexViewModel();

            //取出學年度及學期
            int SYear;
            int Semesters;
            SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);

            if (gAAT01Service.IsAlarmDateData((byte)SYear, (byte)Semesters, ref db) == false)
            {
                TempData[SharedGlobal.StatusMessageName] = "這學期開學日未設定，請連絡「維護管理者」。";
            }

            if (string.IsNullOrWhiteSpace(model.WhereSCHOOL_NO))
            {
                model.WhereSCHOOL_NO = SCHOOL_NO;
            }

            if (string.IsNullOrWhiteSpace(model.WhereSYEARSEMESTER))
            {
                model.WhereSYEARSEMESTER = $"{SYear}_{Semesters}";
            }

            var AlarmSyearSemesterItem = gAAT01Service.GetAlarmSyearSemester(ref db, model.WhereSYEARSEMESTER);

            if (AlarmSyearSemesterItem?.Count > 0)
            {
                if (!AlarmSyearSemesterItem.Where(a => a.Selected == true).Any())
                {
                    model.WhereSYEARSEMESTER = AlarmSyearSemesterItem.FirstOrDefault().Value;

                    AlarmSyearSemesterItem = AlarmSyearSemesterItem.Select(a =>
                    {
                        a.Selected = model.WhereSYEARSEMESTER == a.Value;
                        return a;
                    }).ToList();
                }

                string[] arrSyearSemester = model.WhereSYEARSEMESTER.Split('_');

                model.WhereSYEAR = Convert.ToByte(arrSyearSemester[0]);
                model.WhereSEMESTER = Convert.ToByte(arrSyearSemester[1]);
            }
            else
            {
                model.IsSearch = 0;
            }

            ViewBag.AlarmSyearSemesterItem = AlarmSyearSemesterItem;

            if (string.IsNullOrWhiteSpace(model.WhereCLASS_NO))
            {
                if (user?.TEACH_CLASS_NO != null)
                {
                    model.WhereCLASS_NO = user.TEACH_CLASS_NO;
                }
            }

            var ClassItems = HRMT01.GetClassListData(model.WhereSCHOOL_NO, null, model.WhereCLASS_NO, ref db)
            ?.Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.WhereCLASS_NO });

            ViewBag.ClassItems = ClassItems;

            if (model.IsSearch == 1)
            {
                model = gAAT02Service.GetCareListData(model, ref db);
            }

            return View(model);
        }

        [CheckPermissionSeeion(CheckACTION_ID = "CareList")] //檢查權限
        public ActionResult _CareFoDateClass(GAAI01CareFoDateClassViewModel Item)
        {
            return PartialView(Item);
        }

        [CheckPermissionSeeion(CheckACTION_ID = "CareList")] //檢查權限
        public ActionResult _CareFoUser(GAAI01CareFoUserViewModel Item)
        {
            return PartialView(Item);
        }

        [CheckPermissionSeeion(CheckACTION_ID = "CareList")] //檢查權限
        public ActionResult PrintCareList(GAAI0CareListIndexViewModel model)
        {
            this.Shared("列印關懷通知書設定");
            model.ALARM_NOTICE = db.GAAT03.Where(a => a.SCHOOL_NO == model.WhereSCHOOL_NO).FirstOrDefault()?.ALARM_NOTICE ?? "";

            if (model.DivId == "CareFoDateClassTable")
            {
                var WearDetailList = gAAT02Service.GetWearDetailList((byte)model.WhereSYEAR, (byte)model.WhereSEMESTER, model.WhereSCHOOL_NO, model.WhereCLASS_NO, false, ref db);

                var ALARM_IDs = model.CareFoDateClass.Where(x => x.Checked == true).Select(a => a.ALARM_ID).ToList();

                model.WearDetailList =
                 (from a in WearDetailList
                  where ALARM_IDs.Contains(a.ALARM_ID)
                  group a by new
                  {
                      a.SCHOOL_NO,
                      a.CLASS_NO,
                      a.USER_NO,
                      a.NAME,
                      a.SNAME,
                      a.SEAT_NO
                  } into gcs
                  select new GAAI01SearchWearDetailListViewModel()
                  {
                      SCHOOL_NO = gcs.Key.SCHOOL_NO,
                      CLASS_NO = gcs.Key.CLASS_NO,
                      USER_NO = gcs.Key.USER_NO,
                      NAME = gcs.Key.NAME,
                      SNAME = gcs.Key.SNAME,
                      SEAT_NO = gcs.Key.SEAT_NO,
                  }).OrderBy(x => x.CLASS_NO).ThenBy(x => x.SEAT_NO).ToList();
            }

            return View(model);
        }

        #region Shared

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }

        private void SetTitle(string Panel_Title = "")
        {
            if (!string.IsNullOrWhiteSpace(Panel_Title))
            {
                ViewBag.Panel_Title = Bre_Name + "-" + Panel_Title;
            }
            else
            {
                ViewBag.Panel_Title = Bre_Name;
            }
        }

        private void Shared(string Panel_Title = "")
        {
            ViewBag.BRE_NO = Bre_NO;

            if (!string.IsNullOrWhiteSpace(Panel_Title))
            {
                ViewBag.Panel_Title = Bre_Name + "-" + Panel_Title;
            }
            else
            {
                ViewBag.Panel_Title = Bre_Name;
            }

            user = UserProfileHelper.Get();
            SCHOOL_NO = UserProfileHelper.GetSchoolNo();

            if (user != null)
            {
                if (user.SCHOOL_NO != null)
                {
                    SCHOOL_NO = user.SCHOOL_NO;
                    USER_NO = user.USER_NO;
                }
            }
        }

        #endregion Shared
    }
}