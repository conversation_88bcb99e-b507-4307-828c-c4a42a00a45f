﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;

namespace ECOOL_APP.EF
{
    public class GamePersonIntoViewModel
    {
        public string TEMP_USER_ID { get; set; }

        public string GAME_NO { get; set; }

        public ADDT27 User { get; set; }

        public List<GameCashDetailsViewModel> CashDetails { get; set; }

        /// <summary>
        /// 中獎內容
        /// </summary>
        public List<GameLotteryListViewModel> LotteryDetails { get; set; }

        /// <summary>
        /// 關卡現況
        /// </summary>
        public List<GameMeLevelViewModel> MeLevelDetails { get; set; }

        /// <summary>
        /// 已完成關卡
        /// </summary>
        public int LevelPassCount { get; set; }

        /// <summary>
        /// 未完成關卡
        /// </summary>
        public int UnLevelPassCount { get; set; }
    }
}