﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'forms', 'sl', {
	button: {
		title: 'Lastnosti gumba',
		text: '<PERSON><PERSON><PERSON> (Vrednost)',
		type: 'Vrsta',
		typeBtn: 'Gumb',
		typeSbm: 'Potrdi',
		typeRst: 'Ponastavi'
	},
	checkboxAndRadio: {
		checkboxTitle: 'Lastnosti potrditvenega polja',
		radioTitle: 'Lastnosti izbirnega polja',
		value: 'Vrednost',
		selected: 'I<PERSON><PERSON><PERSON>',
		required: '<PERSON>ah<PERSON><PERSON>o'
	},
	form: {
		title: 'Lastnosti obrazca',
		menu: 'Lastnosti obrazca',
		action: 'Dejanje',
		method: 'Metoda',
		encoding: '<PERSON>dira<PERSON><PERSON> znakov'
	},
	hidden: {
		title: 'Lastnosti skritega polja',
		name: '<PERSON><PERSON>',
		value: 'Vrednost'
	},
	select: {
		title: 'Lastnosti spustnega seznama',
		selectInfo: '<PERSON>da<PERSON>',
		opAvail: 'Razpoložljive izbire',
		value: 'Vrednost',
		size: 'Velikost',
		lines: 'vrstic',
		chkMulti: 'Dovoli izbor več vrednosti',
		required: 'Zahtevano',
		opText: 'Besedilo',
		opValue: 'Vrednost',
		btnAdd: 'Dodaj',
		btnModify: 'Spremeni',
		btnUp: 'Gor',
		btnDown: 'Dol',
		btnSetValue: 'Določi kot privzeto izbiro',
		btnDelete: 'Izbriši'
	},
	textarea: {
		title: 'Lastnosti besedilnega območja',
		cols: 'Stolpcev',
		rows: 'Vrstic'
	},
	textfield: {
		title: 'Lastnosti besedilnega polja',
		name: 'Ime',
		value: 'Vrednost',
		charWidth: 'Širina',
		maxChars: 'Največje število znakov',
		required: 'Zahtevano',
		type: 'Vrsta',
		typeText: 'Besedilo',
		typePass: 'Geslo',
		typeEmail: 'E-pošta',
		typeSearch: 'Iskanje',
		typeTel: 'Telefonska številka',
		typeUrl: 'URL'
	}
} );
