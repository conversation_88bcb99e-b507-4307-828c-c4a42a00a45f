﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Data.Entity.Core.Objects;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace ECOOL_APP.com.ecool.util
{
    public delegate void CellBindHandler(object sender, CellBindArgs e);

    public delegate void DataRowCellHandler(object sender, DataRowCellFilledArgs e);

    public delegate void TableCreatedHandler(object sender, DataTable dt);

    public delegate void HSSFCellStylelHandler(object sender, HSSFCellStyle e);

    public class CellBindArgs : System.EventArgs
    {
        public string ColName;
        public DbDataRecord record;
        public DataRow theRow;
        public object value;
        public bool ChangeValue = false;
        public NPOI.SS.UserModel.ICell theCell;
    }

    public class DataRowCellFilledArgs : System.EventArgs
    {
        public string SheetName;
        public string ColName;
        public string ColCaption;
        public DataRow Row;
        public int RowNo;
        public Type ColumnDataType;
        public string CellDataType;

        public string CellFormula;
        public string CellToString;

        public string StringCellValue;
        public double NumericCellValue;
        public DateTime DateCellValue;
    }

    public class NPOIHelper
    {
        public CellBindHandler onCellBinding;
        public TableCreatedHandler onTableCreaded;
        public DataRowCellHandler onDataTypeConflict;
        public DataRowCellHandler onRowCheckValue;
        public DataRowCellHandler onLineCheckValue;
        public HSSFCellStylelHandler onCellStyle;
        public bool FillSpace_ColumnNotFind = false;

        #region 將一個DataTable匯出為一個Excel Sheet

        /// <summary>
        /// 將一個DataTable匯出為一個Excel Sheet
        /// </summary>
        /// <param name="dtSource">DataTable</param>
        /// <param name="TempleteFileFullName">Excel</param>
        /// <param name="SheetName">Excel Sheet Name</param>
        /// <param name="WriteColName">是否要覆寫欄位名稱</param>
        /// <param name="DataRowNum">資料起始列</param>
        /// <returns></returns>
        public System.IO.MemoryStream ExportExcelFromTemplete(DataTable dtSource, string TempleteFileFullName, string SheetName, bool WriteColName, int DataRowNum)
        {
            FileStream xlsfile = new FileStream(TempleteFileFullName, FileMode.Open, FileAccess.Read);
            System.IO.MemoryStream MSOK = new System.IO.MemoryStream();

            //辯別是2007還是2003
            IWorkbook aBook = WorkbookFactory.Create(xlsfile);
            ExportExcelFromTemplete(dtSource, aBook, SheetName, WriteColName, DataRowNum);
            aBook.Write(MSOK);

            return MSOK;
        }

        #endregion 將一個DataTable匯出為一個Excel Sheet

        #region 從DataSet匯出Excel(多個Sheet)

        /// <summary>
        /// 從DataSet匯出Excel(多個Sheet)
        /// </summary>
        /// <param name="dsSource">可多個Table,TableName=Sheet Name</param>
        /// <param name="TempleteFileFullName">樣板Excel檔案</param>
        /// <param name="WriteColName">是否要覆寫欄位名稱</param>
        /// <param name="DataRowNum">資料起始列</param>
        /// <returns></returns>
        public System.IO.MemoryStream ExportExcelFromTemplete(DataSet dsSource, string TempleteFileFullName, bool WriteColName, int DataRowNum, string TempleteSheetName, bool HiddenTempleteSheet)
        {
            FileStream xlsfile = new FileStream(TempleteFileFullName, FileMode.Open, FileAccess.Read);
            System.IO.MemoryStream MSOK = new System.IO.MemoryStream();

            //辯別是2007還是2003
            IWorkbook aBook = WorkbookFactory.Create(xlsfile);

            int? TempleteSheetIndex = null;
            TempleteSheetIndex = CheckSheetName(TempleteSheetName, aBook);

            foreach (DataTable dt in dsSource.Tables)
            {
                if (TempleteSheetIndex.HasValue)
                {
                    bool HasSheetName = CheckSheetName(dt.TableName, aBook) >= 0;
                    if (HasSheetName == false)
                    {
                        ISheet NewSheet = aBook.CloneSheet(TempleteSheetIndex.Value);
                        aBook.SetSheetName(aBook.NumberOfSheets - 1, dt.TableName);
                    }
                }

                ExportExcelFromTemplete(dt, aBook, dt.TableName, WriteColName, DataRowNum);
            }
            if (HiddenTempleteSheet)
            {
                if (TempleteSheetIndex.HasValue) aBook.SetSheetHidden(TempleteSheetIndex.Value, 1);
            }

            aBook.Write(MSOK);

            return MSOK;
        }

        #endregion 從DataSet匯出Excel(多個Sheet)

        #region 共用 private  for ==>  匯出為一個Excel

        /// <summary>
        ///  DataTable 匯出為一個Excel
        /// </summary>
        /// <param name="dtSource"></param>
        /// <param name="aBook"></param>
        /// <param name="SheetName"></param>
        /// <param name="WriteColName"></param>
        /// <param name="DataRowNum"></param>
        public void ExportExcelFromTemplete(DataTable dtSource, IWorkbook aBook, string SheetName, bool WriteColName, int DataRowNum)
        {
            bool HasSheetName = CheckSheetName(SheetName, aBook) >= 0;
            if (HasSheetName == false) return;

            ISheet aSheet = aBook.GetSheet(SheetName);
            NPOI.SS.UserModel.IRow FirstRow = null;
            NPOI.SS.UserModel.IRow FirstDataRow = aSheet.GetRow(DataRowNum);

            int r = DataRowNum;
            foreach (DataRow dr in dtSource.Rows)
            {
                if (FirstRow == null && WriteColName)
                {
                    if (aSheet.LastRowNum >= 1)
                    {
                        FirstRow = aSheet.GetRow(0);
                    }
                    else
                    {
                        FirstRow = aSheet.CreateRow(0);
                    }

                    for (int i = 0; i < dtSource.Columns.Count; i++)
                    {
                        NPOI.SS.UserModel.ICell theCell = FirstRow.CreateCell(i);

                        theCell.SetCellValue(dtSource.Columns[i].ColumnName);
                        
                    }
                }
                
                if (FirstRow == null) FirstRow = aSheet.GetRow(0);

                NPOI.SS.UserModel.IRow aRow;
                if (r <= aSheet.LastRowNum)
                    aRow = aSheet.GetRow(r);
                else
                    aRow = aSheet.CreateRow(r);

                for (int c = 0; c < FirstRow.LastCellNum; c++)
                {
                    NPOI.SS.UserModel.ICell aCell;
                    if (aRow == null)
                    {
                        aRow = aSheet.CreateRow(r);
                    }
                    if (c >= (aRow.LastCellNum))
                        aCell = aRow.CreateCell(c);
               
                   
                    else
                        aCell = aRow.GetCell(c);

                    if (aCell == null) aCell = aRow.CreateCell(c);

                    if (FirstDataRow.GetCell(c) != null)
                    {
                        if (FirstDataRow.GetCell(c).CellStyle != null)
                        {
                            aCell.CellStyle = FirstDataRow.GetCell(c).CellStyle;
                        }
                    }

                    object oValue = null;
                    if (FirstRow.GetCell(c) != null)
                    {
                        string ColName = FirstRow.GetCell(c).StringCellValue;

                        string[] ColNameList = ColName.Trim().Split('/');

                        ColName = ColNameList[0];

                        if (dtSource.Columns.Contains(ColName) == false) continue;
                        oValue = dr[ColName];
                        if (this.onCellBinding != null)
                        {
                            CellBindArgs e = new CellBindArgs();
                            e.ColName = ColName;
                            e.theRow = dr;
                            e.value = oValue;

                            e.theCell = aCell;
                            if (e.record != null)
                            {
                                this.onCellBinding(this, e);
                                if (e.ChangeValue) oValue = e.value;
                            }
                        }
                       
                        ICellStyle cellStyle = aBook.CreateCellStyle();
                        cellStyle.DataFormat = aBook.CreateDataFormat().GetFormat("yyyy/m/d hh:mm;@");
                        SetCellValue(aCell, oValue);
                        if (oValue is DateTime)
                        { aCell.CellStyle = cellStyle; }
                    }
                }

                r++;
            }
        }

        #endregion 共用 private  for ==>  匯出為一個Excel

        #region 匯出一個EXCEL 裡面有「公式的」for office 2003，需要一個樣本公式是有公式的

        /// <summary>
        /// 匯出EXCEL 裡面有公式的 for office 2003
        /// </summary>
        /// <param name="dtSource">DataTable</param>
        /// <param name="aBook">串流</param>
        /// <param name="SheetName">SheetName</param>
        /// <param name="WriteColName"></param>
        /// <param name="InsertRow">使用插入還是覆蓋原資料</param>
        /// <param name="FirstRow">資料庫欄位名稱在在樣式EXCEL 第Row</param>
        /// <param name="DataRowNum">第幾Row開始寫入資料</param>
        /// <param name="EachFORMULA">每一欄要套用的公式 在樣式EXCEL 第Row</param>
        /// <param name="FinallyFORMULA">資料的最後一Row要套用的公式 在樣式EXCEL 第Row  EX.總計 Sum(A1:A10)</param>
        /// <param name="FinallySNum">公式加總的第1列  ex .Sum(A1:A10) =>1</param>
        /// <param name="FinallyENum">公式加總的套最後一列  ex .Sum(A1:A10) =>10</param>
        public void ExportExcelFromTempleteFORMULA(DataTable dtSource, HSSFWorkbook aBook, string SheetName, bool WriteColName, bool InsertRow, int FirstRowNum, int DataRowNum, int? EachFORMULA, int? FinallyFORMULA, int? FinallySNum, int? FinallyENum)
        {
            bool HasSheetName = CheckSheetName(SheetName, aBook) >= 0;
            if (HasSheetName == false) return;

            ISheet aSheet = aBook.GetSheet(SheetName);

            ExportExcelFromTempleteFORMULA(dtSource, aSheet, SheetName, WriteColName, InsertRow, FirstRowNum, DataRowNum, EachFORMULA, FinallyFORMULA, FinallySNum, FinallyENum);

            aBook.ForceFormulaRecalculation = true;
        }

        #endregion 匯出一個EXCEL 裡面有「公式的」for office 2003，需要一個樣本公式是有公式的

        #region 共用 private  for ==> 匯出一個EXCEL 裡面有「公式的」，需要一個樣本公式是有公式的

        private void ExportExcelFromTempleteFORMULA(DataTable dtSource, ISheet aSheet, string SheetName, bool WriteColName, bool InsertRow, int FirstRowNum, int DataRowNum, int? EachFORMULA, int? FinallyFORMULA, int? FinallySNum, int? FinallyENum)
        {
            // NPOI從 0 開始 所以第3列 要減1
            DataRowNum = DataRowNum - 1;
            if (EachFORMULA != null) EachFORMULA = EachFORMULA - 1;
            if (FinallyFORMULA != null) FinallyFORMULA = FinallyFORMULA - 1;

            FirstRowNum = FirstRowNum - 1;

            NPOI.SS.UserModel.IRow FirstRow = null;

            //公式樣式列
            NPOI.SS.UserModel.IRow EachFormulaRow = EachFORMULA != null ? aSheet.GetRow((int)EachFORMULA) : null;

            //最後一欄加總樣式列
            NPOI.SS.UserModel.IRow FinallyFormulaRow = FinallyFORMULA != null ? aSheet.GetRow((int)FinallyFORMULA) : null;

            NPOI.SS.UserModel.IRow FirstDataRow = aSheet.GetRow(DataRowNum);

            if (this.onCellBinding != null)
            {
                //CellBindArgs e = new CellBindArgs();
                //e.ColName = ColName;
                //e.theRow = dr;
                //e.value = oValue;
                //this.onCellBinding(this, e);
                //if (e.ChangeValue) oValue = e.value;
            }

            int r = DataRowNum;
            foreach (DataRow dr in dtSource.Rows)
            {
                if (FirstRow == null && WriteColName)
                {
                    if (aSheet.LastRowNum >= 1)
                        FirstRow = aSheet.GetRow(0);
                    else
                        FirstRow = aSheet.CreateRow(0);

                    for (int i = 0; i < dtSource.Columns.Count; i++)
                    {
                        NPOI.SS.UserModel.ICell theCell = FirstRow.CreateCell(i);
                        theCell.SetCellValue(dtSource.Columns[i].ColumnName);
                    }
                }
                if (FirstRow == null) FirstRow = aSheet.GetRow(FirstRowNum);

                NPOI.SS.UserModel.IRow aRow;

                if (r <= aSheet.LastRowNum)
                {
                    if (InsertRow == false)
                    {
                        aRow = aSheet.GetRow(r);
                    }
                    else
                    {
                        aSheet.ShiftRows(r, aSheet.LastRowNum + 1, 1); //搬移
                        aRow = aSheet.CreateRow(r);
                    }
                }
                else
                {
                    aRow = aSheet.CreateRow(r);
                }

                for (int c = 0; c < FirstRow.LastCellNum; c++)
                {
                    NPOI.SS.UserModel.ICell aCell;
                    if (c >= aRow.LastCellNum)
                        aCell = aRow.CreateCell(c);
                    else
                        aCell = aRow.GetCell(c);

                    if (EachFormulaRow.GetCell(c) != null)
                    {
                        aCell.CellStyle = EachFormulaRow.GetCell(c).CellStyle;
                    }
                    else if (FirstDataRow.GetCell(c) != null)
                    {
                        aCell.CellStyle = FirstDataRow.GetCell(c).CellStyle;
                    }

                    if (EachFormulaRow.GetCell(c).CellType == NPOI.SS.UserModel.CellType.Formula)
                    {
                        aCell.SetCellFormula(NewFormula(EachFormulaRow.GetCell(c).CellFormula, r - EachFORMULA));
                    }

                    object oValue = null;

                    if (FirstRow.GetCell(c) == null || FirstRow.GetCell(c).CellType != NPOI.SS.UserModel.CellType.String) continue;

                    string ColName = FirstRow.GetCell(c).StringCellValue;

                    string CellFormula = string.Empty;

                    string[] ColNameList = ColName.Trim().Split('/');

                    ColName = ColNameList[0];

                    if (dtSource.Columns.Contains(ColName) == false) continue;
                    oValue = dr[ColName];

                    if (this.onCellBinding != null)
                    {
                        CellBindArgs e = new CellBindArgs();
                        e.ColName = ColName;
                        e.theRow = dr;
                        e.value = oValue;
                        e.theCell = aCell;
                        this.onCellBinding(this, e);
                        if (e.ChangeValue) oValue = e.value;
                    }

                    SetCellValue(aCell, oValue);
                }
                r++;
            }

            //處理最後一列 加總列
            if (FinallyFORMULA != null)
            {
                NPOI.SS.UserModel.IRow aRow;

                if (r <= aSheet.LastRowNum)
                {
                    if (InsertRow == false)
                    {
                        aRow = aSheet.GetRow(r);
                    }
                    else
                    {
                        aSheet.ShiftRows(r, aSheet.LastRowNum + 1, 1); //搬移
                        aRow = aSheet.CreateRow(r);
                    }
                }
                else
                {
                    aRow = aSheet.CreateRow(r);
                }

                for (int c = 0; c < FirstRow.LastCellNum; c++)
                {
                    NPOI.SS.UserModel.ICell aCell;
                    if (c >= aRow.LastCellNum)
                        aCell = aRow.CreateCell(c);
                    else
                        aCell = aRow.GetCell(c);

                    if (FinallyFormulaRow.GetCell(c) != null)
                    {
                        aCell.CellStyle = FinallyFormulaRow.GetCell(c).CellStyle;
                    }

                    if (FinallyFormulaRow.GetCell(c).CellType == NPOI.SS.UserModel.CellType.Formula)
                    {
                        aCell.SetCellFormula(NewSumFormula(FinallyFormulaRow.GetCell(c).CellFormula, FinallySNum, FinallyENum, DataRowNum + 1, r, r - FinallyFORMULA));
                    }
                    else
                    {
                        SetCellValue(aCell, FinallyFormulaRow.GetCell(c));
                    }
                }
            }
        }

        #endregion 共用 private  for ==> 匯出一個EXCEL 裡面有「公式的」，需要一個樣本公式是有公式的

        #region 取代第 Row 至 Row 某個字串 for 2003

        /// <summary>
        /// 取代第 Row 至 Row 某個字串 for 2003
        /// </summary>
        /// <param name="aBook">HSSFWorkbook</param>
        /// <param name="SheetName"></param>
        /// <param name="SNumRow">開始</param>
        /// <param name="ENumRow">結束</param>
        /// <param name="Pattern">被取代字串</param>
        /// <param name="ReplaceStr">變更後字串</param>
        public void AllRowReplace(HSSFWorkbook aBook, string SheetName, int SNumRow, int ENumRow, string Pattern, string ReplaceStr)
        {
            ISheet aSheet = aBook.GetSheet(SheetName);
            AllRowReplace(aSheet, SNumRow, ENumRow, Pattern, ReplaceStr);
        }

        #endregion 取代第 Row 至 Row 某個字串 for 2003

        #region 取代第 Row 至 Row 某個字串 for 2007

        /// <summary>
        /// 取代第 Row 至 Row 某個字串 for 2007
        /// </summary>
        /// <param name="aBook">XSSFWorkbook</param>
        /// <param name="SheetName"></param>
        /// <param name="SNumRow">開始</param>
        /// <param name="ENumRow">結束</param>
        /// <param name="Pattern">被取代字串</param>
        /// <param name="ReplaceStr">變更後字串</param>
        public void AllRowReplace(XSSFWorkbook aBook, string SheetName, int SNumRow, int ENumRow, string Pattern, string ReplaceStr)
        {
            ISheet aSheet = aBook.GetSheet(SheetName);
            AllRowReplace(aSheet, SNumRow, ENumRow, Pattern, ReplaceStr);
        }

        #endregion 取代第 Row 至 Row 某個字串 for 2007

        #region 共用 private ==> 取代第 Row 至 Row 某個字串

        private void AllRowReplace(ISheet aSheet, int SNumRow, int ENumRow, string Pattern, string ReplaceStr)
        {
            for (int i = SNumRow - 1; i <= ENumRow - 1; i++)
            {
                NPOI.SS.UserModel.IRow aRow = aSheet.GetRow(i);

                if (aRow != null)
                {
                    for (int c = 0; c < aRow.LastCellNum; c++)
                    {
                        NPOI.SS.UserModel.ICell aCell;
                        aCell = aRow.GetCell(c);

                        if (aCell != null)
                        {
                            if (aCell.CellType == CellType.String || aCell.CellType == CellType.Numeric)
                            {
                                aCell.SetCellValue(Regex.Replace(aCell.ToString(), Pattern, ReplaceStr));
                            }
                        }
                    }
                }
            }
        }

        #endregion 共用 private ==> 取代第 Row 至 Row 某個字串

        #region 變更公式的數字欄位(PS.公式裡不能是英文數字公式名稱) ex. A1+B1+C1 =>A2+B2+C2

        /// <summary>
        /// 變更公式的數字欄位 ex. A1+B1+C1 =>A2+B2+C2
        /// </summary>
        /// <param name="oFormula">原公式(A1+B1+C1)</param>
        /// <param name="RowNum">後面數字需加多少</param>
        /// <returns></returns>
        public string NewFormula(string oFormula, int? RowNum)
        {
            string ReturnVal = string.Empty;
            string Pattern = @"[A-Z]+[0-9]+";

            if (oFormula == null || oFormula == string.Empty) return ReturnVal;
            if (RowNum == null) return oFormula;

            ReturnVal = Regex.Replace(oFormula, Pattern,
             delegate (Match m)
             {
                 string EStr = Regex.Match(m.Value, @"[A-Z]+").ToString();
                 string NumStr = Regex.Match(m.Value, @"[0-9]+").ToString();

                 return EStr + (Convert.ToInt32(NumStr) + RowNum).ToString();
             });

            return ReturnVal;
        }

        #endregion 變更公式的數字欄位(PS.公式裡不能是英文數字公式名稱) ex. A1+B1+C1 =>A2+B2+C2

        #region 變更公式為Sum的數字欄位 Sum(A1:A3) ==> Sum(A4:A50)

        /// <summary>
        /// 變更公式為Sum的數字欄位 Sum(A1:A3) ==> Sum(A4:A50)
        /// </summary>
        /// <param name="oFormula">原公式</param>
        /// <param name="oSNum">原公式的第一欄的數字 ex 1</param>
        /// <param name="oENum">原公式的第二欄的數字 ex 3</param>
        /// <param name="nSNum">新公式第一欄的數字 ex 4</param>
        /// <param name="nEnum">新公式第二欄的數字 ex 50</param>
        /// <param name="NowRowNum">當不是Sum 需加多少數字</param>
        /// <returns></returns>
        public string NewSumFormula(string oFormula, int? oSNum, int? oENum, int? nSNum, int? nEnum, int? NowRowNum)
        {
            string ReturnVal = string.Empty;
            if (oFormula == null || oFormula == string.Empty) return ReturnVal;
            if (oSNum == null || oENum == null || nSNum == null || nEnum == null || NowRowNum == null) return oFormula;

            string Pattern = @"[A-Z]+[0-9]+";
            string Pattern2 = @"SUM(";

            string Num = string.Empty;

            if (oFormula.IndexOf(Pattern2, StringComparison.OrdinalIgnoreCase) >= 0)
            {
                ReturnVal = Regex.Replace(oFormula, Pattern,
                    delegate (Match m)
                    {
                        string EStr = Regex.Match(m.Value, @"[A-Z]+").ToString();

                        if (Regex.IsMatch(m.Value, @"[" + oENum + "]+"))
                        {
                            Num = nEnum.ToString();
                        }
                        else if (Regex.IsMatch(m.Value, @"[" + oSNum + "]+"))
                        {
                            Num = nSNum.ToString();
                        }

                        return EStr + Num;
                    });
            }
            else
            {
                ReturnVal = NewFormula(oFormula, NowRowNum);
            }

            return ReturnVal;
        }

        #endregion 變更公式為Sum的數字欄位 Sum(A1:A3) ==> Sum(A4:A50)

        #region protected ==>共用 SetCellValue

        protected void SetCellValue(NPOI.SS.UserModel.ICell aCell, object oValue)
        {
            if (oValue == null || Convert.IsDBNull(oValue)) return;
           
            if (oValue is DateTime)
            {
                
                aCell.SetCellValue((DateTime)oValue);

           
            }
            else if (oValue is double)
            {
                aCell.SetCellValue((double)oValue);
            }
            else if (oValue is decimal || oValue is int || oValue is float || oValue is short)
            {
                aCell.SetCellValue(Convert.ToDouble(oValue));
            }
            else if (oValue is bool)
            {
                aCell.SetCellValue((bool)oValue);
            }
            else
            {
                aCell.SetCellValue(oValue.ToString());
            }
        }

        #endregion protected ==>共用 SetCellValue

        #region ObjectQuery 匯出

        /// <summary>
        /// ObjectQuery 匯出 Excel
        /// Col Row Num=0
        /// </summary>
        /// <param name="DataRecordQuery"></param>
        /// <param name="TempleteFileFullName"></param>
        /// <param name="SheetName"></param>
        /// <param name="WriteColName"></param>
        /// <param name="DataRowNum"></param>
        /// <returns></returns>
        public System.IO.MemoryStream ExportExcelFromTemplete(ObjectQuery<DbDataRecord> DataRecordQuery, string TempleteFileFullName, string SheetName, bool WriteColName, int DataRowNum)
        {
            FileStream xlsfile = new FileStream(TempleteFileFullName, FileMode.Open, FileAccess.Read);

            IWorkbook aBook = WorkbookFactory.Create(xlsfile);

            bool HasSheetName = CheckSheetName(SheetName, aBook) >= 0;
            if (HasSheetName == false)
            {
                System.IO.MemoryStream MS = new System.IO.MemoryStream();
                aBook.Write(MS);
                return MS;
            }

            ISheet aSheet = aBook.GetSheet(SheetName);

            NPOI.SS.UserModel.IRow FirstRow = null;
            NPOI.SS.UserModel.IRow FirstDataRow = aSheet.GetRow(DataRowNum);

            int r = DataRowNum;
            foreach (DbDataRecord ddr in DataRecordQuery)
            {
                if (FirstRow == null && WriteColName)
                {
                    if (aSheet.LastRowNum >= 1)
                        FirstRow = aSheet.GetRow(0);
                    else
                        FirstRow = aSheet.CreateRow(0);

                    for (int i = 0; i < ddr.FieldCount; i++)
                    {
                        NPOI.SS.UserModel.ICell theCell = FirstRow.CreateCell(i);
                        theCell.SetCellValue(ddr.GetName(i));
                    }
                }
                if (FirstRow == null) FirstRow = aSheet.GetRow(0);

                NPOI.SS.UserModel.IRow aRow;
                if (r <= aSheet.LastRowNum)
                    aRow = aSheet.GetRow(r);
                else
                    aRow = aSheet.CreateRow(r);

                for (int c = 0; c < FirstRow.LastCellNum; c++)
                {
                    NPOI.SS.UserModel.ICell aCell;
                    if (c >= aRow.LastCellNum)
                        aCell = aRow.CreateCell(c);
                    else
                        aCell = aRow.GetCell(c);

                    aCell.CellStyle = FirstDataRow.GetCell(c).CellStyle;

                    object oValue = null;
                    string ColName = string.Empty;
                    for (int i = 0; i < ddr.FieldCount; i++)
                    {
                        if (ddr.GetName(i) != FirstRow.GetCell(c).StringCellValue) continue;
                        ColName = ddr.GetName(i);
                        oValue = ddr.GetValue(c);
                        if (this.onCellBinding != null)
                        {
                            CellBindArgs e = new CellBindArgs();
                            e.ColName = ColName;
                            e.record = ddr;
                            e.value = oValue;
                            e.theCell = aCell;
                            this.onCellBinding(this, e);
                            if (e.ChangeValue) oValue = e.value;
                        }
                    }

                    SetCellValue(aCell, oValue);
                }

                r++;
            }

            System.IO.MemoryStream MSOK = new System.IO.MemoryStream();
            aBook.Write(MSOK);

            return MSOK;
        }

        public System.IO.MemoryStream ExportExcelFromTemplete(ObjectQuery<DbDataRecord> DataRecordQuery, string TempleteFileFullName, string SheetName, Dictionary<string, int> CNdict, int ColNameRowNum, int DataRowNum)
        {
            FileStream xlsfile = new FileStream(TempleteFileFullName, FileMode.Open, FileAccess.Read);

            IWorkbook aBook = WorkbookFactory.Create(xlsfile);

            bool HasSheetName = CheckSheetName(SheetName, aBook) >= 0;
            if (HasSheetName == false)
            {
                System.IO.MemoryStream MS = new System.IO.MemoryStream();
                aBook.Write(MS);
                return MS;
            }

            ISheet aSheet = aBook.GetSheet(SheetName);
            IRow ColNameRow = aSheet.GetRow(ColNameRowNum);

            IRow FirstDataRow = null;
            int r = DataRowNum - 1;
            foreach (DbDataRecord ddr in DataRecordQuery)
            {
                r++;

                if (FirstDataRow == null) FirstDataRow = aSheet.GetRow(DataRowNum);
                NPOI.SS.UserModel.IRow aRow;
                if (r <= aSheet.LastRowNum)
                    aRow = aSheet.GetRow(r);
                else
                    aRow = aSheet.CreateRow(r);

                for (int c = 0; c < ColNameRow.LastCellNum; c++)
                {
                    NPOI.SS.UserModel.ICell aCell;
                    if (c >= aRow.LastCellNum)
                        aCell = aRow.CreateCell(c);
                    else
                        aCell = aRow.GetCell(c);
                    aCell.CellStyle = FirstDataRow.GetCell(c).CellStyle;

                    string ColName = ColNameRow.GetCell(c).StringCellValue;
                    string[] ColNameList = ColName.Trim().Split('/');

                    bool NotFound = true;

                    for (int f = 0; f < ddr.FieldCount; f++)
                    {
                        if (ddr.GetName(f) != ColNameList[0]) continue;

                        NotFound = false;

                        break;
                    }

                    if (NotFound)
                    {
                        if (FillSpace_ColumnNotFind) aCell.SetCellValue(" ");
                        continue;
                    }

                    object oValue = ddr[ColNameList[0]];

                    //object oValue = ddr.GetValue(CNdict[ColNameList[0]]);

                    if (this.onCellBinding != null)
                    {
                        CellBindArgs e = new CellBindArgs();
                        e.ColName = ColName;
                        e.record = ddr;
                        e.value = oValue;
                        this.onCellBinding(this, e);
                        if (e.ChangeValue) oValue = e.value;
                    }

                    SetCellValue(aCell, oValue);
                }
            }

            System.IO.MemoryStream MSOK = new System.IO.MemoryStream();
            aBook.Write(MSOK);

            return MSOK;
        }

        #endregion ObjectQuery 匯出

        #region 共用 private for Check Sheet Name

        /// <summary>
        /// Check Sheet Name for 2003
        /// </summary>
        /// <param name="SheetName"></param>
        /// <param name="aBook"></param>
        /// <returns>sheet index , if can not find, return -1</returns>
        private static int CheckSheetName(string SheetName, IWorkbook aBook)
        {
            for (int s = 0; s < aBook.NumberOfSheets; s++)
            {
                if (aBook.GetSheetName(s) == SheetName)
                {
                    return s;
                }
            }
            return -1;
        }

        private static string FirstSheetName(IWorkbook aBook)
        {
            return aBook.GetSheetName(0);
        }

        /// <summary>
        ///  Check Sheet Name for 2007
        /// </summary>
        /// <param name="SheetName"></param>
        /// <param name="aBook"></param>
        /// <returns></returns>
        private static int CheckSheetName(string SheetName, XSSFWorkbook aBook)
        {
            for (int s = 0; s < aBook.NumberOfSheets; s++)
            {
                if (aBook.GetSheetName(s) == SheetName)
                {
                    return s;
                }
            }
            return -1;
        }

        #endregion 共用 private for Check Sheet Name

        #region Excel檔轉換成DataTable

        public bool AllowFirstSheet = false;

        public DataTable Excel2Table(Stream inputStream, string SheetName, int ColNameRowNum, int DataRowStartNum, bool HasCaption)
        {
            //10.判斷輸入資料
            if (inputStream == null) return null;
            HSSFWorkbook aBook = new HSSFWorkbook(inputStream);

            bool HasSheetName = CheckSheetName(SheetName, aBook) >= 0;
            if (HasSheetName == false)
            {
                if (AllowFirstSheet == false) return null;
                SheetName = FirstSheetName(aBook);
            }

            ISheet aSheet = aBook.GetSheet(SheetName);
            if (aSheet == null) return null;

            DataTable dt = this.innerExcel2Table(aSheet, SheetName, ColNameRowNum, DataRowStartNum, HasCaption);

            return dt;
        }

        #endregion Excel檔轉換成DataTable

        #region 透過對應表來產生DataTable

        /// <summary>
        /// 透過對應表來產生DataTable
        /// </summary>
        /// <param name="MapFilePath"></param>
        /// <param name="inputStream"></param>
        /// <param name="SheetName"></param>
        /// <param name="ColNameRowNum"></param>
        /// <param name="DataRowStartNum"></param>
        /// <returns></returns>
        public DataTable Excel2Table(string MapXLSPath, Stream inputStream, string SheetName, int ColNameRowNum, int DataRowStartNum)
        {
            //10.判斷輸入資料
            if (inputStream == null) return null;
            if (System.IO.File.Exists(MapXLSPath) == false) return null;

            //20.將Map Excel File 轉成 DataTable
            FileStream fs = File.Open(MapXLSPath, FileMode.Open);
            DataTable dtMap = this.Excel2Table(fs, SheetName, 0, 1, false);

            //30.建立DataTable
            DataTable dt = new DataTable(SheetName);
            foreach (DataRow drMap in dtMap.Rows)
            {
                if (string.IsNullOrWhiteSpace((string)drMap["DB_COL_NAME"])) continue;
                DataColumn dc = dt.Columns.Add((string)drMap["DB_COL_NAME"]);
                if (Convert.IsDBNull(drMap["DATA_TYPE"]) == false)
                {
                    string DataType = (string)drMap["DATA_TYPE"];
                    if (string.IsNullOrEmpty(DataType) == false)
                        dc.DataType = System.Type.GetType(DataType);
                }
                if (Convert.IsDBNull(drMap["DEFAULT_VALUE"]) == false)
                {
                    string DefaultValue = (string)drMap["DEFAULT_VALUE"];
                    if (string.IsNullOrEmpty(DefaultValue) == false)
                        dc.DefaultValue = Convert.ChangeType(DefaultValue, dc.DataType);
                }
                //string Expression = (string)drMap["EXPRESSION"];
                //if ((string.IsNullOrEmpty(Expression) == false))
                //                        dc.Expression=Expression;
            }

            if (this.onTableCreaded != null)
            {
                this.onTableCreaded(this, dt);
            }

            //40.讀取來源字串
            IWorkbook aBook = WorkbookFactory.Create(inputStream);

            bool HasSheetName = CheckSheetName(SheetName, aBook) >= 0;
            if (HasSheetName == false)
            {
                if (this.AllowFirstSheet == false) return null;
                SheetName = FirstSheetName(aBook);
            }

            ISheet aSheet = aBook.GetSheet(SheetName);
            if (aSheet == null) return null;

            int LastRowNum = aSheet.LastRowNum;
            if (LastRowNum < ColNameRowNum) return null;
            if (LastRowNum < DataRowStartNum) return null;

            IRow ColNameRow = aSheet.GetRow(ColNameRowNum);
            if (ColNameRow == null) return null;

            //xian
            //檢查該Sheet的欄位是否與MAP有對應到
            int ErrorCount = 0;
            for (int c = 0; c < ColNameRow.LastCellNum; c++)
            {
                try
                {
                    string XLS_COL_NAME = ColNameRow.Cells[c].StringCellValue;
                    DataRow[] xlsRows = dtMap.Select(string.Format(" XLS_COL_NAME='{0}'", XLS_COL_NAME));

                    if (xlsRows == null)
                    {
                        ErrorCount++;
                    }
                    else if (xlsRows.Length == 0)
                    {
                        ErrorCount++;
                    }
                }
                catch
                {
                    ErrorCount++;
                }
            }
            if (ErrorCount >= ColNameRow.LastCellNum - 1)
            {
                throw new System.Exception("Excel格式錯誤，請下載範本比對！");
            }

            //50.填入資料
            for (int r = DataRowStartNum; r <= LastRowNum; r++)
            {
                IRow row = aSheet.GetRow(r);
                if (row == null) continue;

                DataRow dr = dt.NewRow();
                for (int c = 0; c < ColNameRow.LastCellNum; c++)
                {
                    string XLS_COL_NAME = ColNameRow.Cells[c].StringCellValue;
                    DataRow[] xlsRows = dtMap.Select(string.Format(" XLS_COL_NAME='{0}'", XLS_COL_NAME));
                    if (xlsRows.Length == 0) continue;
                    string DB_COL_NAME = xlsRows[0]["DB_COL_NAME"].ToString();

                    ICell aCell = row.GetCell(c);
                    if (aCell == null) continue;

                    DataColumn dc = dt.Columns[DB_COL_NAME];
                    try
                    {
                        if (dc.DataType == typeof(string))
                        {
                            dr[dc] = aCell.ToString();
                        }
                        else if (dc.DataType == typeof(DateTime))
                        {
                            dr[dc] = aCell.DateCellValue;
                            if (aCell.DateCellValue == DateTime.MaxValue)
                                dr[dc] = DateTime.MinValue;
                        }
                        else if (dc.DataType == typeof(decimal))
                        {
                            dr[dc] = aCell.NumericCellValue;
                        }
                        else if (dc.DataType == typeof(double))
                        {
                            dr[dc] = aCell.NumericCellValue;
                        }
                        else if (dc.DataType == typeof(int))
                        {
                            dr[dc] = aCell.NumericCellValue;
                        }
                    }
                    catch { }
                }

                dt.Rows.Add(dr);
            }

            return dt;
        }

        #endregion 透過對應表來產生DataTable

        #region Excel(多個Sheet)檔轉換成DataSet

        /// <summary>
        /// Excel(多個Sheet)檔轉換成DataSet (是否有中文說明欄位) 從欄位名稱所在的列數的下一列讀入
        /// </summary>
        /// <param name="inputStream">傳入上傳的Excel檔(ex:透過FileUpload)</param>
        /// <param name="ColNameRowNum">欄位名稱所在的列數</param>
        /// <param name="DataRowStartNum">資料列起始列數</param>
        /// <param name="HasCaption">是否有中文說明欄位(ColNameRowNum的下一列)</param>
        /// <param name="SheetNames">Excel Sheet名稱,因為是params,所以可以傳入多個(也會變成DataTable Name)</param>
        /// <returns></returns>
        public DataSet Excel2Table(Stream inputStream, int ColNameRowNum, int DataRowStartNum, bool HasCaption, params string[] SheetNames)
        {
            //10.判斷輸入資料
            if (inputStream == null) return null;

            IWorkbook aBook = WorkbookFactory.Create(inputStream);

            DataSet ds = new DataSet();
            foreach (string SheetName in SheetNames)
            {
                bool HasSheetName = CheckSheetName(SheetName, aBook) >= 0;
                if (HasSheetName == false) return null;

                ISheet aSheet = aBook.GetSheet(SheetName);
                if (aSheet == null) return null;

                DataTable dt = this.innerExcel2Table(aSheet, SheetName, ColNameRowNum, DataRowStartNum, HasCaption);
                ds.Tables.Add(dt);
            }

            return ds;
        }

        /// <summary>
        ///  Excel(多個Sheet)檔轉換成DataSet (指定>中文說明欄位所在的列數)
        /// </summary>
        /// <param name="inputStream">傳入上傳的Excel檔(ex:透過FileUpload)</param>
        /// <param name="ColNameRowNum">欄位名稱所在的列數</param>
        /// <param name="DataRowStartNum">資料列起始列數</param>
        /// <param name="HasCaption">中文說明欄位所在的列數</param>
        /// <param name="SheetNames">Excel Sheet名稱,因為是params,所以可以傳入多個(也會變成DataTable Name)</param>
        /// <returns></returns>
        public DataSet Excel2Table(Stream inputStream, int ColNameRowNum, int DataRowStartNum, int HasCaption, params string[] SheetNames)
        {
            //10.判斷輸入資料
            if (inputStream == null) return null;
            IWorkbook aBook = WorkbookFactory.Create(inputStream);

            DataSet ds = new DataSet();
            foreach (string SheetName in SheetNames)
            {
                bool HasSheetName = CheckSheetName(SheetName, aBook) >= 0;
                if (HasSheetName == false) return null;

                ISheet aSheet = aBook.GetSheet(SheetName);
                if (aSheet == null) return null;

                DataTable dt = this.innerExcel2Table(aSheet, SheetName, ColNameRowNum, DataRowStartNum, HasCaption);
                if (dt != null)
                {
                    ds.Tables.Add(dt);
                }
            }
            //inputStream.Flush();
            //inputStream.Dispose();
            
return ds;
        }

        /// <summary>
        /// 共用 for Excel(多個Sheet)檔轉換成DataSet (是否有中文說明欄位) 從欄位名稱所在的列數的下一列讀入
        /// </summary>
        /// <param name="aSheet"></param>
        /// <param name="SheetName"></param>
        /// <param name="ColNameRowNum"></param>
        /// <param name="DataRowStartNum"></param>
        /// <param name="HasCaption"></param>
        /// <returns></returns>
        private DataTable innerExcel2Table(ISheet aSheet, string SheetName, int ColNameRowNum, int DataRowStartNum, bool HasCaption)
        {
            int LastRowNum = aSheet.LastRowNum;
            if (LastRowNum < ColNameRowNum) return null;
            if (LastRowNum < DataRowStartNum) return null;

            IRow ColNameRow = aSheet.GetRow(ColNameRowNum);
            if (ColNameRow == null) return null;

            IRow ColCaptionRow = null;
            if (HasCaption) ColCaptionRow = aSheet.GetRow(ColNameRowNum + 1);

            //20.建立DataTable
            DataTable dt = new DataTable(SheetName);
            SortedList<int, string> ColNameIndex = new System.Collections.Generic.SortedList<int, string>();
            for (int c = 0; c < ColNameRow.LastCellNum; c++)
            {
                //21判斷欄位名稱
                ICell ColNameCell = ColNameRow.GetCell(c);
                if (ColNameCell == null)
                    continue;

                string[] ColNameList = ColNameCell.ToString().Trim().Split('/');
                string ColName = ColNameList[0];
                if (ColName == string.Empty)
                    continue;

                //22判斷欄位Data Type
                Type ColDataType = typeof(string);
                if (ColNameList.Length == 2)
                {
                    string TypeName = ColNameList[1].Trim().ToLower();
                    if (TypeName == typeof(DateTime).ToString().ToLower())
                    {
                        ColDataType = typeof(DateTime);
                    }
                    else if (TypeName == typeof(decimal).ToString().ToLower())
                    {
                        ColDataType = typeof(decimal);
                    }
                    else if (TypeName == typeof(double).ToString().ToLower())
                    {
                        ColDataType = typeof(double);
                    }
                    else if (TypeName == typeof(int).ToString().ToLower())
                    {
                        ColDataType = typeof(int);
                    }
                }

                //23加入欄位
                if (dt.Columns.Contains(ColName) == false)
                {
                    DataColumn dc = dt.Columns.Add(ColName, ColDataType);
                    ColNameIndex.Add(c, ColName);
                    if (ColCaptionRow != null)
                    {
                        ICell CellCaption = ColCaptionRow.GetCell(c);

                        if (CellCaption != null)
                        {
                            dc.Caption = CellCaption.ToString();
                        }
                    }
                }
            }
            if (this.onTableCreaded != null)
            {
                this.onTableCreaded(this, dt);
            }

            //30.填入資料
            for (int r = DataRowStartNum; r <= LastRowNum; r++)
            {
                IRow row = aSheet.GetRow(r);
                if (row == null) continue;

                DataRow dr = dt.NewRow();
                foreach (KeyValuePair<int, string> pair in ColNameIndex)
                {
                    ICell aCell = row.GetCell(pair.Key);
                    if (aCell == null) continue;

                    DataColumn dc = dt.Columns[pair.Value];
                    if (dc == null) continue;

                    bool TypeConflict = (aCell.CellType == CellType.Error);
                    try
                    {
                        if (dc.DataType == typeof(string))
                        {
                            if (aCell.CellType == CellType.Formula)
                            {
                                dr[dc] = aCell.StringCellValue;
                            }
                            else
                            {
                                dr[dc] = aCell.ToString();
                            }
                        }
                        else if (dc.DataType == typeof(DateTime))
                        {
                            dr[dc] = aCell.DateCellValue;

                            if (aCell.DateCellValue == DateTime.MaxValue || aCell.DateCellValue == DateTime.MinValue)
                                dr[dc] = System.DBNull.Value;
                        }
                        else if (dc.DataType == typeof(decimal) || dc.DataType == typeof(double) || dc.DataType == typeof(int))
                        {
                            if (aCell.CellType != CellType.Numeric && aCell.CellType != CellType.Formula && aCell.CellType != CellType.Blank)
                                TypeConflict = true;
                            else
                                dr[dc] = aCell.NumericCellValue;
                        }
                    }
                    catch { }

                    if (TypeConflict && this.onDataTypeConflict != null)
                    {
                        DataRowCellFilledArgs eFill = new DataRowCellFilledArgs();
                        eFill.ColName = dc.ColumnName;
                        eFill.ColCaption = dc.Caption;
                        eFill.Row = dr;
                        eFill.RowNo = r + 1;
                        eFill.ColumnDataType = dc.DataType;
                        eFill.CellDataType = aCell.CellType.ToString();
                        eFill.CellToString = aCell.ToString();

                        if (aCell.CellType == CellType.Numeric) eFill.NumericCellValue = aCell.NumericCellValue;
                        else if (aCell.CellType == CellType.String) eFill.StringCellValue = aCell.StringCellValue;
                        else if (aCell.CellType == CellType.Formula) eFill.CellFormula = aCell.CellFormula;

                        this.onDataTypeConflict(this, eFill);
                    }
                }
                dt.Rows.Add(dr);
            }
            return dt;
        }

        /// <summary>
        /// 共用 private  for Excel(多個Sheet)檔轉換成DataSet (指定>中文說明欄位所在的列數)
        /// </summary>
        /// <param name="aSheet"></param>
        /// <param name="SheetName"></param>
        /// <param name="ColNameRowNum"></param>
        /// <param name="DataRowStartNum"></param>
        /// <param name="HasCaption"></param>
        /// <returns></returns>
        private DataTable innerExcel2Table(ISheet aSheet, string SheetName, int ColNameRowNum, int DataRowStartNum, int HasCaption)
        {
            int LastRowNum = aSheet.LastRowNum;
            if (LastRowNum < ColNameRowNum) return null;
            if (LastRowNum < DataRowStartNum) return null;

            IRow ColNameRow = aSheet.GetRow(ColNameRowNum);
            if (ColNameRow == null) return null;

            IRow ColCaptionRow = null;
            ColCaptionRow = aSheet.GetRow(HasCaption);

            //20.建立DataTable
            DataTable dt = new DataTable(SheetName);
            SortedList<int, string> ColNameIndex = new System.Collections.Generic.SortedList<int, string>();
            for (int c = 0; c < ColNameRow.LastCellNum; c++)
            {
                //21判斷欄位名稱
                ICell ColNameCell = ColNameRow.GetCell(c);
                if (ColNameCell == null)
                    continue;

                string[] ColNameList = ColNameCell.ToString().Trim().Split('/');
                string ColName = ColNameList[0];
                if (ColName == string.Empty)
                    continue;

                //22判斷欄位Data Type
                Type ColDataType = typeof(string);
                if (ColNameList.Length == 2)
                {
                    string TypeName = ColNameList[1].Trim().ToLower();
                    if (TypeName == typeof(DateTime).ToString().ToLower())
                    {
                        ColDataType = typeof(DateTime);
                    }
                    else if (TypeName == typeof(decimal).ToString().ToLower())
                    {
                        ColDataType = typeof(decimal);
                    }
                    else if (TypeName == typeof(double).ToString().ToLower())
                    {
                        ColDataType = typeof(double);
                    }
                    else if (TypeName == typeof(int).ToString().ToLower())
                    {
                        ColDataType = typeof(int);
                    }
                }

                //23加入欄位
                if (dt.Columns.Contains(ColName) == false)
                {
                    DataColumn dc = dt.Columns.Add(ColName, ColDataType);
                    ColNameIndex.Add(c, ColName);
                    if (ColCaptionRow != null)
                    {
                        ICell CellCaption = ColCaptionRow.GetCell(c);
                        if (CellCaption != null) dc.Caption = CellCaption.ToString();
                    }
                }
            }
            if (this.onTableCreaded != null)
            {
                this.onTableCreaded(this, dt);
            }

            //30.填入資料
            for (int r = DataRowStartNum; r <= LastRowNum; r++)
            {
                IRow row = aSheet.GetRow(r);
                if (row == null) continue;

                DataRow dr = dt.NewRow();
                foreach (KeyValuePair<int, string> pair in ColNameIndex)
                {
                    DataColumn dc = dt.Columns[pair.Value];
                    if (dc == null)
                        continue;

                    ICell aCell = row.GetCell(pair.Key);
                    if (aCell == null)
                    {
                        if (this.onRowCheckValue != null)
                        {
                            DataRowCellFilledArgs eFilln = new DataRowCellFilledArgs();
                            eFilln.SheetName = SheetName;
                            eFilln.ColName = dc.ColumnName;
                            eFilln.ColCaption = dc.Caption;
                            eFilln.Row = dr;
                            eFilln.RowNo = r + 1;
                            eFilln.ColumnDataType = dc.DataType;

                            this.onRowCheckValue(this, eFilln);
                        }
                        continue;
                    }

                    //System.Diagnostics.Debug.WriteLine(dc.ColumnName);

                    bool TypeConflict = (aCell.CellType == CellType.Error);
                    try
                    {
                        if (dc.DataType == typeof(string))
                        {
                            if (aCell.CellType == CellType.Formula)
                            {
                                if (aCell.CachedFormulaResultType == CellType.Numeric)
                                {
                                    dr[dc] = aCell.NumericCellValue;
                                }
                                else
                                {
                                    dr[dc] = aCell.StringCellValue;
                                }
                            }
                            else
                            {
                                dr[dc] = aCell.ToString();
                            }
                        }
                        else if (dc.DataType == typeof(DateTime))
                        {
                            if (aCell.CellType == CellType.Numeric)
                            {
                                dr[dc] = aCell.DateCellValue;

                                if (aCell.DateCellValue == DateTime.MaxValue || aCell.DateCellValue == DateTime.MinValue)
                                    dr[dc] = System.DBNull.Value;
                            }
                            else
                            {
                                DateTime sd;
                                if ((DateTime.TryParse(aCell.ToString(), out sd)) == true)
                                {
                                    dr[dc] = sd;
                                }
                                else
                                {
                                    if (aCell.CellType == CellType.Blank) //空白，塞Null
                                    {
                                        dr[dc] = System.DBNull.Value;
                                    }
                                    else
                                    {
                                        TypeConflict = true;
                                    }
                                }
                            }
                        }
                        else if (dc.DataType == typeof(decimal) || dc.DataType == typeof(double) || dc.DataType == typeof(int))
                        {
                            if (aCell.CellType != CellType.Numeric && aCell.CellType != CellType.Formula && aCell.CellType != CellType.Blank)
                                TypeConflict = true;
                            else
                                dr[dc] = aCell.NumericCellValue;
                        }
                    }
                    catch (Exception e)
                    {

                        string exce = "";
                        exce = e.InnerException.Message;
                    }
                    try {
                        if (TypeConflict && this.onDataTypeConflict != null)
                        {
                            DataRowCellFilledArgs eFill = new DataRowCellFilledArgs();
                            eFill.SheetName = SheetName;
                            eFill.ColName = dc.ColumnName;
                            eFill.ColCaption = dc.Caption;
                            eFill.Row = dr;
                            eFill.RowNo = r + 1;
                            eFill.ColumnDataType = dc.DataType;
                            eFill.CellDataType = aCell.CellType.ToString();
                            eFill.CellToString = aCell.ToString();

                            if (aCell.CellType == CellType.Numeric) eFill.NumericCellValue = aCell.NumericCellValue;
                            else if (aCell.CellType == CellType.String) eFill.StringCellValue = aCell.StringCellValue;
                            else if (aCell.CellType == CellType.Formula) eFill.CellFormula = aCell.CellFormula;

                            this.onDataTypeConflict(this, eFill);
                        }

                        if (this.onRowCheckValue != null)
                        {
                            DataRowCellFilledArgs eFill2 = new DataRowCellFilledArgs();
                            eFill2.SheetName = SheetName;
                            eFill2.ColName = dc.ColumnName;
                            eFill2.ColCaption = dc.Caption;
                            eFill2.Row = dr;
                            eFill2.RowNo = r + 1;
                            eFill2.ColumnDataType = dc.DataType;
                            eFill2.CellDataType = aCell.CellType.ToString();
                            eFill2.CellToString = aCell.ToString();

                            if (aCell.CellType == CellType.Numeric) eFill2.NumericCellValue = aCell.NumericCellValue;
                            else if (aCell.CellType == CellType.String) eFill2.StringCellValue = aCell.StringCellValue;
                            else if (aCell.CellType == CellType.Formula) eFill2.CellFormula = aCell.CellFormula;

                            this.onRowCheckValue(this, eFill2);
                        }



                    } catch (Exception e)

                    {
                        string exce = "";
                        exce = e.InnerException.Message;



                    }
                   
                }
                try {
                    if (this.onLineCheckValue != null)
                    {
                        DataRowCellFilledArgs eFill3 = new DataRowCellFilledArgs();

                        eFill3.SheetName = SheetName;
                        eFill3.Row = dr;
                        eFill3.RowNo = r + 1;

                        this.onLineCheckValue(this, eFill3);
                    }

                    dt.Rows.Add(dr);

                }
                catch (Exception e)

                {
                    string exce = "";
                    exce = e.InnerException.Message;



                }
              
            }
            return dt;
        }

        #endregion Excel(多個Sheet)檔轉換成DataSet

        #region 檢查使用者 上傳EXCEL 與 Server上的Sample Excel 是否一樣

        /// <summary>
        /// 檢查使用者 上傳EXCEL 與 Server上的Sample Excel 是否一樣
        /// </summary>
        /// <param name="ExcelPath">Server上的Sample Excel路徑</param>
        /// <param name="ArraySheetNames">Sample Excel SheetNames 可以傳入多個 ex.{"Sheet1","Sheet2"} </param>
        /// <param name="DsData">使用者上傳EXCEL產生的DataSet Name</param>
        /// <param name="ColNameRowNum">Server上的Sample Excel 英文欄位 在第幾列</param>
        /// <param name="ChiColNameRowNum">Server上的Sample Excel 中文欄位 在第幾列</param>
        /// <returns></returns>
        public string NPOI_RowCheckCol(string ExcelPath, string[] ArraySheetNames, DataSet DsData, int ColNameRowNum, int ChiColNameRowNum)
        {
            string Err = string.Empty;

            //Check Sample 表頭是否跟User上傳 Execl 是否一樣
            //抓取要 Check Execl 格式
            string CheckSampleExcel = System.Web.HttpContext.Current.Server.MapPath(ExcelPath);
            FileStream xlsfile = new FileStream(CheckSampleExcel, FileMode.Open, FileAccess.Read);

            IWorkbook CheckBook = WorkbookFactory.Create(xlsfile);

            foreach (string ThisSheet in ArraySheetNames) //從第一個Sheet開始檢查
            {
                DataTable dt = DsData.Tables[ThisSheet]; //把USER 上傳的 Sheet存到dt裡

                if (dt == null)
                {
                    Err = Err + "上傳錯誤，錯誤原因如下:<br><br>上傳Excel的Sheet Name，沒有【" + ThisSheet.ToString() + " 】，請確認<br/>";
                }

                ISheet CheckSheet = CheckBook.GetSheet(ThisSheet); //取得Sample Execl

                IRow ColNameRow = CheckSheet.GetRow(ColNameRowNum);     //取得Sample Execl第1列 欄位名稱
                IRow ColChiNameRow = CheckSheet.GetRow(ChiColNameRowNum);  //取得Sample Execl第3列 中文名稱

                for (int c = 0; c < ColNameRow.LastCellNum; c++) //從Sample Execl 第一欄開始跑,ColNameRow.LastCellNum  第1列 總共欄位數
                {
                    ICell ColNameCell = ColNameRow.GetCell(c);

                    string[] ColNameList = ColNameCell.ToString().Trim().Split('/');
                    string ColName = ColNameList[0];  //取得欄位名稱

                    //取得中文欄位名稱
                    ICell ColChiNameCell = ColChiNameRow.GetCell(c);

                    if (ColName.ToString() != "")
                    {
                        if (DsData.Tables[ThisSheet].Columns.Contains(ColName.ToString()) == false) //檢查 上傳Excel 塞至ds 裡 有沒有這個欄位名稱
                        {
                            Err = Err + "Sheet名稱【" + ThisSheet + "】- Excel的標題，沒有【" + ColName.ToString() + " ." + ColChiNameCell.ToString() + " 】，請確認<br/>";
                        }
                    }
                }
            } //foreach (string ThisSheet in ThisSheetNames) //從第一個Sheet開始檢查

            return Err;
        }

        #endregion 檢查使用者 上傳EXCEL 與 Server上的Sample Excel 是否一樣
    }
}