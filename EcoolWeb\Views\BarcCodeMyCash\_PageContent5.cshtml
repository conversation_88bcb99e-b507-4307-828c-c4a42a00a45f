﻿@model BarcCodeMyCashIndexViewModel
@using ECOOL_APP.com.ecool.util
@{
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

    //兌換獎品系統路徑
    ViewBag.SysAwardPath = EcoolWeb.Controllers.AWAI01Controller.GetImagePathUrl(AWAI01SearchViewModel.SouTableVal.Student);
}

@Html.HiddenFor(m => m.OrdercColumn)
@Html.HiddenFor(m => m.SyntaxName)
@Html.HiddenFor(m => m.Page)
@Html.HiddenFor(m => m.WhereSchoolNo)
@Html.HiddenFor(m => m.TimeoutSeconds)
@Html.HiddenFor(m => m.NoBook)
@Html.HiddenFor(m => m.ShowStep2)

@Html.Partial("_Notice")

<header>
    <h1 class="page-title"><i class="icon-size-lg icon-search mr-2"></i>酷幣查詢</h1>
</header>

<section class="bg-tertiary rounded atm-honorRoll">
    <!-- 累積榮譽榜 -->
    <div class="row">
        <div class="col-sm-6 col-md-3 d-flex justify-content-center justify-content-md-end align-items-center flex-row atm-honorRoll-box">
            <h2 class="atm-honorRoll-title text-center d-inline-block text-orange">累計點數<br class="d-none d-md-block" />榮譽榜</h2>
            <i class="icon icon-bigMedal"></i>
        </div>
        <div class="col-sm-6 col-md-9 d-flex align-items-center">
            <div class="atm-honorRoll-bg my-2">
                <ol class="atm-honorRoll-list">
                    @{short i = 1; }
                    @foreach (var t01 in ViewBag.AWAT01List as List<HRMT01QTY>)
                    {
                        if (i <= 3)
                        {
                            <li>
                                第 @i 名：
                                <span> @t01.CLASS_NO  @t01.NAME        </span>
                            </li>
                            i++;
                        }
                    }
                </ol>
            </div>
        </div>
    </div>
</section>
<section class="row align-items-stretch">
    <div class="col-md-8 col-lg-9 d-flex bg-tertiary rounded">
        <div class="flex-fill">
            <!-- 我要查詢 -->
            <div class="p-2 mt-1">
                <div class="row searchArea">
                    <div class="col-md-4 col-lg-3 text-center">
                        <h2 class="searchArea-title mb-2 mb-md-2 mb-lg-0 text-nowrap d-none d-md-block">我要查詢</h2>
                    </div>
                    <div class="col-md-8 col-lg-9">
                        <div class="input-group">
                            <span class="placeholderIcon"><i class="fa fa-2x fa-user" aria-hidden="true"></i></span>
                            @Html.EditorFor(m => m.WhereKeyword, new { htmlAttributes = new { @class = "form-control", @onKeyPress = "call(event,this);", @placeholder = "請感應數位學生證或輸入學號", @ariaLabel = "請感應數位學生證或輸入學號，輸入後按下搜尋鈕" } })
                            <div class="input-group-append">
                                <button class="btn btn-primary text-white" type="submit" id="button-addon2">
                                    <i class="fa fa-2x fa-search" aria-hidden="true"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 跑馬燈 -->
            <div class="p-2 mt-1 mb-3">
                <div class="marquee">

                    <i class="icon-size-m icon-announce"></i>
                    <span class="text-danger text-nowrap">現有資產排名:</span>
                    <div class="js-conveyor-1">
                        <ul>
                            <li>

                                <span>@ViewBag.SortBoard2</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4 col-lg-3 px-0 pl-md-3 mt-3 mt-md-0 sideArea flex-fill d-flex flex-column flex-fill justify-content-between">
        <!-- 簡易版只秀圖片 -->
        <img class="img-fluid mb-auto w-100" src="../Content/images/082819_RFID_TagCard-網頁.gif" alt="卡片感應示意圖">
    </div>
</section>
<input type="text" id="displayBox" name="displayBox" value="0" style="display:none">
@*<div class="jumbotron justify-content-center align-items-center align-content-center" id="atm_bg_a1" style="padding-top:15px;">
        <div class="col d-flex justify-content-center" id="atm_head">
            <div id="atm_logo" style="background-image:url('@Url.Content("~/assets/img/atm_logo_02.png")');background-position:center;background-size:contain;background-repeat:no-repeat;"></div>
        </div>
    </div>*@
@*<div class="row d-flex align-items-center align-content-center" id="atm_contain_a1">

        <div class="row" id="atm_con_l_row_a1">
        </div>
    </div>*@
<input type="text" id="displayBox" name="displayBox" value="0" style="display:none">
<script src="~/Scripts/jquery.simple.timer.js?ver=2"></script>
<script src="~/Content/colorbox/jquery.colorbox.js"></script>
<script type="text/javascript">
     var oTimerId;

     function Timeout() {
        var SCHOOL_NO = '';
        SCHOOL_NO = $("#@Html.IdFor(m=>m.WhereSchoolNo)").val();
        if ('@Model.ChangeMode' == "(1)酷幣點數排行榜+現有點數排行榜") {
            window.location.href = "@Url.Action("LeaderIndex", "BarcCodeMyCash")?" + "WhereSchoolNo=" +@Model.WhereSchoolNo+"&FROMACTION=Index5&TimeoutSeconds=" + '@Model.TimeoutSeconds';

        }
        else if ('@Model.ChangeMode' == "(2)閱讀認證排行榜+運動撲滿排行榜") {

            window.location.href = "@Url.Action("LeaderIndex2", "BarcCodeMyCash")?" + "WhereSchoolNo=" +@Model.WhereSchoolNo+"&FROMACTION=Index5&TimeoutSeconds=" + '@Model.TimeoutSeconds';
        }
        else {

            window.location.href = "@Url.Action("LeaderIndex3", "BarcCodeMyCash")?" + "WhereSchoolNo = " +@Model.WhereSchoolNo+"&FROMACTION=Index5&TimeoutSeconds=" + '@Model.TimeoutSeconds';
        }

        x = 0;

        return true;
    }

     x = 0
    function countSecond() {
        var Detime = 0;

        Detime =  '@Model.DelayTime'* 60;
        if (x < Detime) {
            x = x + 1
            document.getElementById("displayBox").value = x
            setTimeout("countSecond()", 1000)
        }
        else {
            Timeout();

        }
    }
    document.onmouseup = function () {
        x = 0;
    }
   if ('@Model.DelayTime' != 0) {


        countSecond()

    }

    $(document).ready(function () {
          $("#@Html.IdFor(m=>m.WhereKeyword)").val('');
          $("#@Html.IdFor(m=>m.WhereKeyword)").focus();

           if ($('#@Html.IdFor(m => m.TimeoutSeconds)').val() != "") {
              var DivATM2 = document.getElementById("atm_btn_img_1");
              DivATM2.style.display = 'none';

              var DivATM2_2 = document.getElementById("atm_btn_img_2");
              DivATM2_2.style.display = 'none';

            }
        if ('@Model.DelayTime' != '0.00'){
            document.onmousedown = ReCalculate();
            document.onmousemove = ReCalculate();
        }
        });

     function call(e, input) {
        var code = (e.keyCode ? e.keyCode : e.which);

        if (code == 13) // 13 是 Enter 按鍵的值
        {
            event.preventDefault();
            if ($('#@Html.IdFor(m => m.WhereKeyword)').val() != "") {

                   $('#@Html.IdFor(m => m.WhereKeyword)').prop('readonly', true);

                    setTimeout(function () {
                        funAjax();
                    });
            }
        }
    }

    // 點選查詢Div Row也submit
    $("#board_text_1-3_a1").on('click', function (e) {
        // 點選的是input則返回
        if (e.target.id == "WhereKeyword") return;
        $('#@Html.IdFor(m => m.WhereKeyword)').focus();
        if ($('#@Html.IdFor(m => m.WhereKeyword)').val() != "") {

                   $('#@Html.IdFor(m => m.WhereKeyword)').prop('readonly', true);

                    setTimeout(function () {
                        funAjax();
                    });
            }
    });

   function BT1_CLICK()
   {
       alert('未輸入學號/掃描條碼')
   }
    function BT3_CLICK() {
        var wkey = $("#WhereKeyword").val();
        if (wkey == '' || wkey == undefined) {
            alert('未輸入學號/掃描條碼');
        }
        else {
            $('#form1').attr("action", "@Url.Action("IstoryUserIndex", "BarcCodeMyCash",new { WhereSchoolNo =Model.WhereSchoolNo })",)
            $('#form1').attr('target', '_blank').submit().removeAttr('target');

        }
    }

</script>