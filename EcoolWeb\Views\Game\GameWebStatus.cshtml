﻿@model List<GameWebStatusIntoViewModel>
@{
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";

}
<link href="~/Content/css/childrens-month.css?v=2" rel="stylesheet" />
<div class="panel with-nav-tabs panel-info">
    <div class="panel-heading">
        <h1 style="font-size: 3.31rem;">連線狀態</h1>
    </div>
    <div class="panel-body" style="background-color:#eef6fa;">
        @if ((Model?.Count() ?? 0) > 0)
        {
            foreach (var item in Model)
            {
                <div class="col-md-4">
                    <div class="childrens-level" id="Div@(item.BACKUP_ID)" url="@(item.BACKUP_URL)@(Url.Action("Post","WebStausApi", new { httproute = "DefaultApi" }))">
                        @item.BACKUP_NAME
                    </div>
                </div>
            }
        }
    </div>
</div>

<script>

    window.onload = function () {
        DivCheck()
    }

    function DivCheck() {
        $(".childrens-level").each(function () {
            var Url = $('#' + this.id).attr('url');
            CheckApi(this.id, Url)
        });

        setTimeout(function () { DivCheck(); }, 6000);
    }

    function CheckApi(Id, nowRoot) {

        var hasError = false;

        $.ajax({
            type: "POST",
            url: nowRoot,
            contentType: 'application/json',
            data: null, // serializes the form's elements.
            async: false, // 非同步
            timeout: 1000,
            success: function (data) {
                console.log(data);
                if (data.Status == "200") {
                    $('#' + Id).removeClass("childrens-level-noplay").addClass("childrens-level-success")
                } else {
                    $('#' + Id).removeClass("childrens-level-success").addClass("childrens-level-noplay")
                }
            },
            error: function (jqXHR, exception) {
                hasError = true;
            },
            complete: function (data) {
                //發生連線錯誤
                if (hasError) {
                    console.log(hasError);
                    $('#' + Id).removeClass("childrens-level-success").addClass("childrens-level-noplay")
                    return; // 不繼續往下執行ajax作業
                }
            }
        });
    }
</script>