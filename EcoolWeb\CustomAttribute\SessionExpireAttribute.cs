﻿using com.ecool.service;
using ECOOL_APP;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using System.Web.Routing;
using EcoolWeb.Models;
using EcoolWeb.Util;
using ECOOL_APP.EF;
using log4net;
using UAParser;

namespace EcoolWeb.CustomAttribute
{
    public class SessionExpireAttribute : ActionFilterAttribute
    {
        private static ILog logger = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        private bool HasAttribute(ActionExecutingContext context, Type attribute)
        {
            var actionDesc = context.ActionDescriptor;
            var controllerDesc = actionDesc.ControllerDescriptor;

            bool allowAnon =
                actionDesc.IsDefined(attribute, true) ||
                controllerDesc.IsDefined(attribute, true);

            return allowAnon;
        }

        public override void OnActionExecuting(ActionExecutingContext filterContext)
        {
            var userAgent = HttpContext.Current.Request.UserAgent;
            var uaParser = Parser.GetDefault();
            ClientInfo c = uaParser.Parse(userAgent);

            string SCHOOL_NO = UserProfileHelper.GetSchoolNo();

            var SouBre_NO = filterContext.HttpContext.Request.QueryString["SouBre_NO"];

            if (!string.IsNullOrWhiteSpace(SouBre_NO))
            {
                UserProfileHelper.SetBRE_NO(SouBre_NO);
            }

            if (SCHOOL_NO == null)
            {
                //HttpRequestMessage
                var CheckId = filterContext.HttpContext.Request.QueryString["CheckId"];
                string UUID = UserProfileHelper.GetUUIDCookie();

                if (string.IsNullOrWhiteSpace(CheckId) == false) //手機版 URL 有傳 CheckId
                {
                    //判斷傳入的驗証碼是否正確，當正確時會 把相關資訊寫入 Session
                    var Ok = Util.WebApiHelper.GetCKSetSession(filterContext.HttpContext.Request);
                    //logger.Info("Ok=" + Ok.ToString());

                    //再取一次 Session 來做判斷，當沒取得 Session ，代表上面那段他是驗証失敗
                    SCHOOL_NO = UserProfileHelper.GetSchoolNo();
                    //UserProfile user = UserProfileHelper.Get();
                    //logger.Info("SCHOOL_NO=" + SCHOOL_NO);
                    //logger.Info("user.USER_NO=" + user?.USER_NO ?? "");

                    if (SCHOOL_NO == null)
                    {
                        //出取Cookie 裡的學校代碼
                        SCHOOL_NO = UserProfileHelper.GetSchoolNoCookie();

                        HttpContext.Current.Session[CONSTANT.SESSION_SCHOOL_KEY] = SCHOOL_NO;

                        if (SCHOOL_NO == null)
                        {
                            filterContext.Result = new RedirectToRouteResult(
                                                             new RouteValueDictionary
                                                            {
                           { "controller", "Error" },
                           { "action", "Error403" }
                                                            });
                        }
                    }
                }
                //手機版 URL 沒傳 CheckId ，但 Cookie 可取出 UUID ，WebView內頁的連結就是這種狀態
                else if (string.IsNullOrWhiteSpace(CheckId) && string.IsNullOrWhiteSpace(UUID) == false)
                {
                    if (userAgent.IndexOf("IsEcoolAppOpen") > 0 || (c.OS.Family == "IOS" && c.UA.Family.IndexOf("UIWebView") > 0) || (c.OS.Family.ToUpper() == "ANDROID"))
                    {
                        //取得 CheckId
                        CheckId = UserProfileHelper.GetCheckIdCookie();

                        ApiBaseModel CkDb = new ApiBaseModel();

                        CkDb.CheckId = CheckId;
                        CkDb.UUID = UUID;
                        string RealCheckId;

                        //驗証 CheckId 與 UUID 是否正確
                        bool Ok = ECOOL_APP.AppHelper.checkCheckId(CkDb.CheckId, CkDb.UUID, out RealCheckId);
                        if (Ok)
                        {
                            CkDb.RealCheckId = RealCheckId;
                            WebApiHelper.ApiSetSession(CkDb);
                        }
                        else
                        {
                            filterContext.Result = new RedirectToRouteResult(
                                                 new RouteValueDictionary
                                                {
                           { "controller", "Error" },
                           { "action", "Error403" }
                                                });
                        }
                    }
                }
                else if (string.IsNullOrWhiteSpace(CheckId) == false && string.IsNullOrWhiteSpace(UUID) == true)  //手機版 理論上不進跑進這，因為跑到第一個判斷式
                {
                    if (string.IsNullOrWhiteSpace(UUID)) UUID = filterContext.HttpContext.Request.QueryString["UUID"];

                    ApiBaseModel CkDb = new ApiBaseModel();

                    CkDb.CheckId = CheckId;
                    CkDb.UUID = UUID;
                    WebApiHelper.ApiSetSession(CkDb);
                }
                //網頁版  或 手機版 (使用者把app 裡的Cookie清空)，瀏覽器的Cookie 跟 app 的Cookie 是存不同地方，除非有專門清掉 Cookie app ，app Cookie 才有可能被清空
                //，但每次 app 打開有 有登入帳號時，都會寫進去 Cookie 裡
                else
                {
                    //出取Cookie 裡的學校代碼
                    SCHOOL_NO = UserProfileHelper.GetSchoolNoCookie();

                    HttpContext.Current.Session[CONSTANT.SESSION_SCHOOL_KEY] = SCHOOL_NO;

                    Controller control = filterContext.Controller as Controller;

                    var controllerName = control.RouteData.Values["Controller"].ToString();

                    bool allowAnon = (filterContext.ActionDescriptor.IsDefined(typeof(AllowAnonymousAttribute), true)
                        || filterContext.ActionDescriptor.ControllerDescriptor.IsDefined(typeof(AllowAnonymousAttribute), true));

                    if (allowAnon)
                    {
                        //....
                    }
                    else
                    {
                        if (controllerName != "Game" && controllerName != "ADDI10")
                        {
                            if (UserProfileHelper.GetROLE_IDCookie() != null) //取去Cookie是否有存 ROLE_ID，有代表他之前登入，但之前取不到 SCHOOL_NO Session 所以代表他 SessionTimeOutError
                            {
                                filterContext.Result = new RedirectToRouteResult(
                                new RouteValueDictionary
                               {
                                 { "controller", "Error" },
                                 { "action", "SessionTimeOutError" }
                               });
                            }
                        }
                    }
                }
            }

            //理論上不用跑這斷 ，Eric寫的
            if (c.OS.Family.ToUpper() == "ANDROID" && string.IsNullOrWhiteSpace(SCHOOL_NO) == true)
            {
                //Eric for android
                string urlCheckId = filterContext.HttpContext.Request.QueryString["CheckId"];
                string urlUUID = filterContext.HttpContext.Request.QueryString["UUID"];

                if (urlCheckId == null) urlCheckId = string.Empty;
                if (urlUUID == null) urlUUID = string.Empty;
                //if (urlUUID == "APA91bHiD3XBC27xqmS-YWfVKR-BaxLUU2Uy1pZnMegJ-7SjtUGgUpTeCjiL9kUJFzrYzB1XbQS54h1500tiVDBRzvdsVMlwc_8-vYfXo2k1I5uC-OFlB6AN-QT4QR7Rr0JnxM8ulGW3")
                logger.Info("android ChangeSession:urlCheckId=" + urlCheckId);
                logger.Info("android ChangeSession:urlUUID=" + urlUUID);

                if (string.IsNullOrWhiteSpace(urlCheckId) && string.IsNullOrWhiteSpace(urlUUID) == false)
                {
                    using (ECOOL_DEVEntities db = new ECOOL_DEVEntities())
                    {
                        HRMT05_CHECK dbCk = db.HRMT05_CHECK.Where(a => a.UUID == urlUUID).FirstOrDefault();
                        urlCheckId = dbCk.CHECK_ID;
                        //logger.Info("android ChangeSession:CheckId Empty,Find=" + urlCheckId);
                    }
                }
                if (string.IsNullOrEmpty(urlCheckId) == false && string.IsNullOrEmpty(urlUUID) == false)
                {
                    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
                    bool ChangeSession = (user == null);
                    if (ChangeSession == false)
                    {
                        if (urlCheckId.Length <= 4) ChangeSession = true;//android
                    }
                    if (ChangeSession)
                    {
                        using (ECOOL_DEVEntities db = new ECOOL_DEVEntities())
                        {
                            HRMT05_CHECK ckDb = db.HRMT05_CHECK.Where(a => a.UUID == urlUUID).FirstOrDefault();
                            if (ckDb != null)
                            {
                                if (string.IsNullOrWhiteSpace(ckDb.USER_NO) == false)
                                {
                                    HRMT01 FindUser = db.HRMT01.Where(a => a.SCHOOL_NO == ckDb.SCHOOL_NO && a.USER_NO == ckDb.USER_NO).FirstOrDefault();
                                    if (FindUser != null)
                                    {
                                        //填入UserProfile
                                        UserProfile LoginUser = UserProfile.FillUserProfile(FindUser);
                                        UserProfileHelper.Set(LoginUser);
                                        //if (FindUser.USER_NO == "100019" || FindUser.USER_NO == "A100019") logger.Info("android ChangeSession:USER_NO=" + FindUser.USER_NO);
                                    }
                                }
                            }
                        }
                    }
                }
            }

            base.OnActionExecuting(filterContext);
        }
    }
}