﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class GameListDataViewModel
    {
        public string SCHOOL_NO { get; set; }
        public string LEVEL_NO { get; set; }
        public string Coupons_ITem { get; set; }
        /// <summary>
        ///活動id
        /// </summary>
        [DisplayName("活動id")]
        public string GAME_NO { get; set; }

        /// <summary>
        ///活動名稱
        /// </summary>
        [DisplayName("活動名稱")]
        public string GAME_NAME { get; set; }

        /// <summary>
        ///活動開始日
        /// </summary>
        [DisplayName("活動開始日")]
        [DataType(DataType.Date), DisplayFormat(DataFormatString = "{0:yyyy/MM/dd}", ApplyFormatInEditMode = true)]
        public DateTime? GAME_DATES { get; set; }

        /// <summary>
        ///活動結束日
        /// </summary>
        [DisplayName("活動結束日")]
        [DataType(DataType.Date), DisplayFormat(DataFormatString = "{0:yyyy/MM/dd}", ApplyFormatInEditMode = true)]
        public DateTime? GAME_DATEE { get; set; }

        /// <summary>
        ///建立人
        /// </summary>
        [DisplayName("建立人")]
        public string CRE_PERSON { get; set; }

        /// <summary>
        ///建立日
        /// </summary>
        [DisplayName("建立日")]
        public DateTime? CRE_DATE { get; set; }

        /// <summary>
        ///修改人
        /// </summary>
        [DisplayName("修改人")]
        public string CHG_PERSON { get; set; }

        /// <summary>
        ///修改日
        /// </summary>
        [DisplayName("修改日")]
        public DateTime? CHG_DATE { get; set; }

        public int GAME_TYPE { get; set; }
    }
}