﻿
using DotNet.Highcharts;
using ECOOL_APP.com.ecool.Models.DTO;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;

namespace ECOOL_APP.EF
{
    public class SECI02ReadIndexViewModel
    {
        /// <summary>
        /// 月份 字串
        /// </summary>
        public List<string> TEachMonth { get; set; }

        /// <summary>
        /// 每週 週六日期
        /// </summary>
        public List<string> TEachWeekToDate { get; set; }

        /// <summary>
        /// 月統計表資料
        /// </summary>
        public List<SECSharedChartLineViewModel> MonthList;


        public string[] ArrEachWeek;

        public object[] MonthArrWRITING = new object[] { };
        public object[] MonthArrPASSPORT = new object[] { };

        public object[] WeekArrWRITING = new object[] { };
        public object[] WeekArrPASSPORT = new object[] { };

        /// <summary>
        /// 月趨示圖
        /// </summary>
        public Highcharts Monthcharts;


        /// <summary>
        /// 週統計表
        /// </summary>
        public List<SECSharedChartLineViewModel> WeekList;

        /// <summary>
        /// 週趨示圖
        /// </summary>
        public Highcharts Weekcharts;
    }
}