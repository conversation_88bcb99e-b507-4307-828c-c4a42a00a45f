﻿@model EcoolWeb.ViewModels.AWA002Query2ViewModel
@using ECOOL_APP.com.ecool.Models.DTO
@using ECOOL_APP.com.ecool.util
@{

    if (Model.WhereIsPassbook)
    {
        ViewBag.Title = "酷幣給點紀錄-我的數位存摺";
    }
    else
    {
        ViewBag.Title = "酷幣給點紀錄-酷幣紀錄明細一覽表";
    }

    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
    ECOOL_DEVEntities db = new ECOOL_DEVEntities();
    int TrNum = 1;
}
@if (AppMode == false)
{
    @Html.Partial("_Title_Secondary")
}

@Html.Partial("_Notice")
<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>
<div class="table-responsive">

    @using (Html.BeginForm("Query2", "AWA002", FormMethod.Post, new { id = "AWA002" }))
    {

        if (user != null)
        {
            if (user.USER_TYPE != UserType.Student && user.USER_TYPE != UserType.Parents)
            {
                @Html.ActionLink("各類酷幣給點統計", "Query", null, new { @class = "btn btn-sm btn-sys" })
                @Html.ActionLink("學生e酷幣給點紀錄", "Query2", null, new { @class = "btn btn-sm btn-sys active" })
                @Html.ActionLink("學生數位存摺", "Query5", null, new { @class = "btn btn-sm btn-sys" })

            }
            else
            {
                if (user.USER_TYPE == UserType.Student)
                {

                    if (Model.WhereIsPassbook)
                    {
                        @Html.ActionLink("我的數位存摺", "Query2", new { WhereIsPassbook = true }, new { @class = "btn btn-sm btn-sys active" })
                        @Html.ActionLink("e酷幣給點紀錄", "Query2", null, new { @class = "btn btn-sm btn-sys" })

                    }

                    else
                    {
                        @Html.ActionLink("我的數位存摺", "Query2", new { WhereIsPassbook = true }, new { @class = "btn btn-sm btn-sys" })
                        @Html.ActionLink("e酷幣給點紀錄", "Query2", null, new { @class = "btn btn-sm btn-sys active" })

                    }

                    @Html.ActionLink("我的酷幣給點統計", "Query", null, new { @class = "btn btn-sm btn-sys" })

                }
                else
                {
                    @Html.ActionLink("寶貝酷幣給點統計", "Query", null, new { @class = "btn btn-sm btn-sys" })
                }

            }
        }

        <br />
        <br />

        <div class="form-inline" role="form">
            <div class="form-group">
                <label class="control-label">日期區間</label>
            </div>
            <div class="form-group">
                <div class="row">
                    <div class="col-xs-3">
                        @Html.EditorFor(m => m.whereLOG_TIME_S, new { htmlAttributes = new { @class = "form-control input-sm", @type = "text" } })
                    </div>
                    <div class="col-xs-1">
                        至
                    </div>
                    <div class="col-xs-3">
                        @Html.EditorFor(m => m.whereLOG_TIME_E, new { htmlAttributes = new { @class = "form-control input-sm", @type = "text" } })
                    </div>
                    <div class="col-xs-1">
                    </div>
                </div>
            </div>
        </div>
        <br />

        if (!Model.WhereIsPassbook)
        {
            <div class="form-inline" role="form">
                <div class="form-group">
                    <label class="control-label">類別&nbsp;&nbsp;</label>
                    @Html.CheckBoxList("whereArrCHART_DESC", (List<CheckBoxListInfo>)ViewBag.ArrCHART_DESC, null, 5)
                </div>
            </div>
            <br />
            if (user != null)
            {
                if (user.USER_TYPE != UserType.Student)
                {
                    <div class="form-inline" role="form">
                        <div class="form-group">
                            <label class="control-label">學號/姓名/異動說明/給點人員</label>
                        </div>
                        <div class="form-group">
                            @Html.EditorFor(m => m.whereKeyword, new { htmlAttributes = new { @class = "form-control input-sm" } })
                        </div>
                        <div class="form-group">
                            <label class="control-label">年級</label>
                        </div>
                        <div class="form-group">
                            @Html.DropDownListFor(m => m.whereGrade, (IEnumerable<SelectListItem>)ViewBag.GradeItem, new { @class = "form-control input-sm", onchange = "FunPageProc(1)" })
                        </div>
                        <div class="form-group">
                            <label class="control-label">班級</label>
                        </div>
                        <div class="form-group">
                            @Html.DropDownListFor(m => m.whereCLASS_NO, (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control input-sm", onchange = "FunPageProc(1)" })
                        </div>
                    </div>
                }
                else
                {
                    <div class="form-inline" role="form">
                        <div class="form-group">
                            <label class="control-label">異動說明</label>
                        </div>
                        <div class="form-group">
                            @Html.EditorFor(m => m.whereKeyword, new { htmlAttributes = new { @class = "form-control input-sm" } })
                        </div>
                    </div>
                }
            }

        }

        @Html.HiddenFor(m => m.OrdercColumn)
        @Html.HiddenFor(m => m.Page)
        @Html.HiddenFor(m => m.WhereIsPassbook)
        @Html.HiddenFor(m => m.whereFromType)
        @Html.HiddenFor(m => m.whereUserNo)
        @Html.HiddenFor(m => m.whereSOURCETABLE)
        <br />
        <div class="form-inline" role="form">
            <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1)" />
            <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />
        </div>
        <br />
    }
    <span style="color:red">說明：如果要看到具體事蹟，可以將滑鼠停在異動說明列，會顯示提示文字。</span>
    @if (AppMode == false)
    {
        <div class="row">
            <div class="col-xs-12 text-right">
                <button type="button" class="btn btn-sm btn-sys" onclick="PrintBooK()">我要列印</button>
                @if (!Model.WhereIsPassbook)
                {
                    <button type="button" class="btn btn-sm btn-sys" onclick="ToExcel()">匯出excel</button>
                }
            </div>
        </div>
    }
    @if (ViewBag.Title == "酷幣給點紀錄-我的數位存摺")
    {

        <img src="~/Content/img/web-bar2-revise-bk.png" style="width:100%" class="img-responsive " alt="Responsive image" />
    }
    else if (ViewBag.Title == "酷幣給點紀錄-我的各類酷幣給點統計一覽表")
    {
        <img src="~/Content/img/web-bar2-revise-bk2.png" style="width:100%" class="img-responsive " alt="Responsive image" />
    }
    else
    {

        <img src="~/Content/img/web-bar2-revise-16.png" style="width:100%" class="img-responsive " alt="Responsive image" />
    }

    <table class="table-ecool table-92Per table-hover table-ecool-AWA003">
        <thead>
            <tr>
                <td align="center">
                    日期
                </td>
                @if (Model.WhereIsPassbook)
                {
                    if (user != null)
                    {
                        if (user.USER_TYPE != UserType.Student && user.USER_TYPE != UserType.Parents)
                        {
                            <td align="center">
                                學號
                            </td>
                            <td align="center">
                                姓名
                            </td>
                        }
                    }

                    <td align="center">
                        支出
                    </td>
                    <td align="center">
                        存入
                    </td>
                    <td align="center">
                        給扣點人員
                    </td>
                    <td align="center">
                        異動說明
                    </td>
                    <td align="center">
                        目前點數
                    </td>
                }
                else
                {
                    <td align="center">
                        學號
                    </td>
                    <td align="center">
                        班級
                    </td>
                    <td align="center">
                        座號
                    </td>
                    <td align="center">
                        姓名
                    </td>
                    <td align="center">
                        點數
                    </td>
                    <td align="center">
                        異動說明
                    </td>
                    <td align="center">
                        給點人員
                    </td>
                }
            </tr>
        </thead>
        <tbody>
            @foreach (var item in Model.VAWAT01_LOG.OrderByDescending(x=>x.LOG_TIME))
            {
                string SYS_TABLE_TYPE = item.SOURCE_TYPEEdite;
                string controllerName = "";
                bool isButn = false;
                string urlParameters = "";

                if (SYS_TABLE_TYPE == "ADDI09" || SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OTHER || SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OTHER_CLASS || SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_ADDT20_CLASS_BATCH || SYS_TABLE_TYPE == "ADDI13")
                {
                    isButn = true;
                    controllerName = "ADDI09";
                    if (item.LOG_DESC == "批次校內表現" || item.LOG_DESC.Contains("批次校內表現") || item.LOG_DESC.Contains("校內表現"))
                    {
                        ADDT14 aDDT14Item = new ADDT14();
                        aDDT14Item = db.ADDT14.Where(x => x.BATCH_CASH_ID == item.SOURCE_NO).FirstOrDefault();
                        if (aDDT14Item != null)
                        {

                            item.LOG_DESC = "批次校內表現-" + aDDT14Item.IAWARD_ITEM;
                        }
                    }

                    else if (item.LOG_DESC == "批次校外榮礜")
                    {
                        ADDT15 aDDT15Item = new ADDT15();
                        aDDT15Item = db.ADDT15.Where(x => x.BATCH_CASH_ID == item.SOURCE_NO).FirstOrDefault();
                        if (aDDT15Item != null)
                        {

                            item.LOG_DESC = "批次校外榮礜-" + aDDT15Item.OAWARD_ITEM;
                        }
                    }
                    //  urlParameters = $"?BATCH_CASH_ID={item.BATCH_CASH_ID}&NUM={item.NUM}";
                }
                else if (SYS_TABLE_TYPE == "ZZZI20" && item.LOG_DESC == "酷幣匯轉區域管理")
                {
                    isButn = true;

                    controllerName = "ZZZI20";

                }
                else if (SYS_TABLE_TYPE == "ADDI06" || SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_IN || SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_ADDT14_CLASS_BATCH || SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_QUICKBULK)
                {
                    isButn = true;
                    controllerName = "ADDI06";
                    //urlParameters = $"?IAWARD_ID={item.IAWARD_ID}";
                }
                else if (SYS_TABLE_TYPE == "ADDI07" || SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OBC)
                {
                    isButn = true;
                    controllerName = "ADDI07";
                    // urlParameters = $"?OAWARD_ID={item.OAWARD_ID}";
                }

            <tr onclick="OnDetails('@item.SOURCE_TYPE', '@item.SOURCE_NO')" style="@(Model.WhereIsPassbook && TrNum % 2 ==0 ? "cursor:pointer;background-color:#D3FF93" : "cursor:pointer;")">
                <td align="center">
                    @Html.DisplayFor(modelItem => item.LOG_TIME, "ShortDateTime")
                </td>

                @if (Model.WhereIsPassbook)
                {
                    string showdesc = "轉學生";
                    if (user != null)
                    {
                        if (user.USER_TYPE != UserType.Student && user.USER_TYPE != UserType.Parents)
                        {
                            <td align="center">
                                @if (item.USER_TYPE == UserType.Student)
                                {
                                    @Html.DisplayFor(modelItem => item.USER_NO)
                                }
                                else
                                {
                                    @ECOOL_APP.com.ecool.util.StringHelper.RightStringR(item.USER_NO, 4, "O")
                                }
                            </td>
                            <td align="center">
                                @Html.DisplayFor(modelItem => item.SNAME)
                            </td>
                        }
                    }

                    <td align="center">
                        @if (item.CASH_IN < 0)
                        {
                            @(item.CASH_IN * -1);
                        }
                    </td>
                    <td align="center">
                        @if (item.CASH_IN >= 0)
                        {
                            @Html.DisplayFor(modelItem => item.CASH_IN)
                        }
                    </td>
                    <td align="center">
                        @Html.DisplayFor(modelItem => item.LOG_PERSON_NAME)
                    </td>
                    string ISEDItStatus = "";

                    if (item.LOG_DESC.Contains("校內表現") || item.LOG_DESC.Contains("班級幫手和榮譽") || item.SOURCE_TYPEEdite == "ADDT14")
                    {
                        int ADDT14Count = 0;

                        ADDT14Count = db.ADDT14.Where(x => x.BATCH_CASH_ID == item.SOURCE_NO && x.SCHOOL_NO == item.SCHOOL_NO).Count();
                        if (ADDT14Count > 0)
                        {
                            string IAWARD_KIND = "";
                            ISEDItStatus = db.ADDT14.Where(x => x.BATCH_CASH_ID == item.SOURCE_NO && x.SCHOOL_NO == item.SCHOOL_NO && x.CASH == item.CASH_IN && x.USER_NO == item.USER_NO).Select(x => x.APPLY_STATUS).FirstOrDefault();
                            IAWARD_KIND = db.ADDT14.Where(x => x.BATCH_CASH_ID == item.SOURCE_NO && x.SCHOOL_NO == item.SCHOOL_NO && x.CASH == item.CASH_IN && x.USER_NO == item.USER_NO).Select(x => x.IAWARD_ITEM).FirstOrDefault();

                            if (item.CHART_DESC.Contains("參加抽獎活動"))
                            {
                                if (item.LOG_PERSON != "系統給扣點")
                                {

                                    <td style="text-align: left;white-space:normal" title="@IAWARD_KIND">

                                        參加抽獎活動
                                    </td>
                                }
                                else
                                {
                                    <td style="text-align: left;white-space:normal" title="@IAWARD_KIND">

                                        @Html.DisplayFor(modelItem => item.SOURCE_TYPE)
                                    </td> }
                            }
                            else
                            {


                                <td style="text-align: left;white-space:normal" title="@IAWARD_KIND">

                                    @Html.DisplayFor(modelItem => item.SOURCE_TYPE)
                                </td>
                            }


                        }
                        else
                        {

                            <td style="text-align:center" title="@item.LOG_DESC">

                                @Html.DisplayFor(modelItem => item.SOURCE_TYPE)
                            </td>
                        }
                    }
                    else if (item.LOG_DESC.Contains("批次特殊加扣點") || item.SOURCE_TYPEEdite == "ADDT20")
                    {
                        int ADDT20Count = 0;
                        ADDT20Count = db.ADDT20.Where(x => x.BATCH_CASH_ID == item.SOURCE_NO && x.SCHOOL_NO == item.SCHOOL_NO).Count();

                        if (ADDT20Count > 0)
                        {

                            string IAWARD_KIND = "";
                            IAWARD_KIND = db.ADDT20.Where(x => x.BATCH_CASH_ID == item.SOURCE_NO && x.SCHOOL_NO == item.SCHOOL_NO && x.CASH == item.CASH_IN && x.USER_NO == item.USER_NO).Select(x => x.CONTENT_TXT).FirstOrDefault();
                            ISEDItStatus = db.ADDT20.Where(x => x.BATCH_CASH_ID == item.SOURCE_NO && x.SCHOOL_NO == item.SCHOOL_NO && x.CASH == item.CASH_IN && x.USER_NO == item.USER_NO).Select(x => x.APPLY_STATUS).FirstOrDefault();
                            if (item.SOURCE_TYPE.Contains("參加抽獎活動"))
                            {
                                if (item.LOG_PERSON != "系統給扣點")
                                {

                                    <td style="text-align: left;white-space:normal" title="@IAWARD_KIND">

                                        參加抽獎活動(獎品)
                                    </td>
                                }
                            }
                            else
                            {

                                <td style="text-align: left;white-space:normal" title="@IAWARD_KIND">

                                    @Html.DisplayFor(modelItem => item.SOURCE_TYPE)
                                </td>}

                        }
                        else
                        {

                            <td style="text-align: left;white-space:normal" title="@item.LOG_DESC">

                                @Html.DisplayFor(modelItem => item.SOURCE_TYPE)
                            </td>


                        }
                    }
                    else
                    {

                        if (item.LOG_DESC.Contains("校外榮譽") || item.SOURCE_TYPEEdite == "ADDT15" ||item.CHART_DESC.Contains("校外榮譽"))
                        {



                            int ADDT15Count = 0;
                            ADDT15Count = db.ADDT15.Where(x => x.BATCH_CASH_ID == item.SOURCE_NO && x.SCHOOL_NO == item.SCHOOL_NO).Count();

                            if (ADDT15Count > 0)
                            {
                                string IAWARD_KIND = "";

                                IAWARD_KIND = db.ADDT15.Where(x => x.BATCH_CASH_ID == item.SOURCE_NO && x.SCHOOL_NO == item.SCHOOL_NO && x.CASH == item.CASH_IN && x.USER_NO == item.USER_NO).Select(x => x.OAWARD_ITEM).FirstOrDefault();
                                ISEDItStatus = db.ADDT15.Where(x => x.BATCH_CASH_ID == item.SOURCE_NO && x.SCHOOL_NO == item.SCHOOL_NO && x.CASH == item.CASH_IN && x.USER_NO == item.USER_NO).Select(x => x.APPLY_STATUS).FirstOrDefault();
                                <td style="text-align: left;white-space:normal" title="@IAWARD_KIND">

                                    @Html.DisplayFor(modelItem => item.SOURCE_TYPE)
                                </td>

                            }
                            else
                            {

                                <td style="text-align:center" title="@item.LOG_DESC">
                                    @if (item.SOURCE_TYPE == "酷幣匯轉區域管理")
                                    {
                                        @showdesc

                                    }
                                    else
                                    {

                                        @StringHelper.LeftStringR(item.SOURCE_TYPE, 15)
                                    }
                                </td>
                            }



                        }
                        else
                        {
                            <td style="text-align: left;white-space:normal" title="@item.LOG_DESC">

                                @if (item.SOURCE_TYPE == "酷幣匯轉區域管理")
                                {
                                    @showdesc

                                }
                                else
                                {
                                    if (item.CHART_DESC.Contains("參加抽獎活動"))
                                    {
                                        if (item.LOG_PERSON != "系統給扣點")
                                        {
                                            @:參加抽獎活動
                                        }
                                        else
                                        { @StringHelper.LeftStringR(item.SOURCE_TYPE, 15)}
                                }
                                else
                                {
                                    @StringHelper.LeftStringR(item.SOURCE_TYPE, 15)
                                }
                            }
                            </td>}
                    }
                    @*<td align="center" title="@item.LOG_DESC">
                @if (item.SOURCE_TYPE == "酷幣匯轉區域管理")
                {
                @showdesc

                }
                else
                {

                @StringHelper.LeftStringR(item.SOURCE_TYPE, 15)
                }

            </td>*@
                    <td align="center">
                        @Html.DisplayFor(modelItem => item.AWAT01_CASH_AVAILABLE)
                    </td>
                }
                else
                {
                    string showdesc = "轉學生";
                    <td align="center">
                        @if (item.USER_TYPE == UserType.Student)
                        {
                            @Html.DisplayFor(modelItem => item.USER_NO)
                        }
                        else
                        {
                            @ECOOL_APP.com.ecool.util.StringHelper.RightStringR(item.USER_NO, 4, "O")
                        }
                    </td>
                    <td align="center">
                        @Html.DisplayFor(modelItem => item.CLASS_NO)
                    </td>
                    <td align="center">
                        @Html.DisplayFor(modelItem => item.SEAT_NO)
                    </td>
                    <td align="center">
                        @Html.DisplayFor(modelItem => item.SNAME)
                    </td>
                    <td align="center">
                        @Html.DisplayFor(modelItem => item.CASH_IN)
                    </td>
                    string ISEDItStatus = "";

                    if (item.LOG_DESC.Contains("校內表現") || item.LOG_DESC.Contains("班級幫手和榮譽") || item.SOURCE_TYPEEdite == "ADDT14")
                    {
                        int ADDT14Count = 0;

                        ADDT14Count = db.ADDT14.Where(x => x.BATCH_CASH_ID == item.SOURCE_NO && x.SCHOOL_NO == item.SCHOOL_NO).Count();
                        if (ADDT14Count > 0)
                        {
                            string IAWARD_KIND = "";
                            ISEDItStatus = db.ADDT14.Where(x => x.BATCH_CASH_ID == item.SOURCE_NO && x.SCHOOL_NO == item.SCHOOL_NO && x.CASH == item.CASH_IN && x.USER_NO == item.USER_NO).Select(x => x.APPLY_STATUS).FirstOrDefault();
                            IAWARD_KIND = db.ADDT14.Where(x => x.BATCH_CASH_ID == item.SOURCE_NO && x.SCHOOL_NO == item.SCHOOL_NO && x.CASH == item.CASH_IN && x.USER_NO == item.USER_NO).Select(x => x.IAWARD_ITEM).FirstOrDefault();

                            if (item.CHART_DESC.Contains("參加抽獎活動"))
                            {
                                if (item.LOG_PERSON != "系統給扣點")
                                {

                                    <td style="text-align: left;white-space:normal" title="@IAWARD_KIND">

                                        參加抽獎活動
                                    </td>
                                }
                                else
                                {
                                    <td style="text-align: left;white-space:normal" title="@IAWARD_KIND">

                                        @Html.DisplayFor(modelItem => item.SOURCE_TYPE)
                                    </td> }
                            }
                            else
                            {


                                <td style="text-align: left;white-space:normal" title="@IAWARD_KIND">

                                    @Html.DisplayFor(modelItem => item.SOURCE_TYPE)
                                </td>
                            }


                        }
                        else
                        {

                            <td style="text-align:center" title="@item.LOG_DESC">

                                @Html.DisplayFor(modelItem => item.SOURCE_TYPE)
                            </td>
                        }
                    }
                    else if (item.LOG_DESC.Contains("批次特殊加扣點") || item.SOURCE_TYPEEdite == "ADDT20")
                    {
                        int ADDT20Count = 0;
                        ADDT20Count = db.ADDT20.Where(x => x.BATCH_CASH_ID == item.SOURCE_NO && x.SCHOOL_NO == item.SCHOOL_NO).Count();

                        if (ADDT20Count > 0)
                        {

                            string IAWARD_KIND = "";
                            IAWARD_KIND = db.ADDT20.Where(x => x.BATCH_CASH_ID == item.SOURCE_NO && x.SCHOOL_NO == item.SCHOOL_NO && x.CASH == item.CASH_IN && x.USER_NO == item.USER_NO).Select(x => x.CONTENT_TXT).FirstOrDefault();
                            ISEDItStatus = db.ADDT20.Where(x => x.BATCH_CASH_ID == item.SOURCE_NO && x.SCHOOL_NO == item.SCHOOL_NO && x.CASH == item.CASH_IN && x.USER_NO == item.USER_NO).Select(x => x.APPLY_STATUS).FirstOrDefault();
                            if (item.SOURCE_TYPE.Contains("參加抽獎活動"))
                            {
                                if (item.LOG_PERSON != "系統給扣點")
                                {

                                    <td style="text-align: left;white-space:normal" title="@IAWARD_KIND">

                                        參加抽獎活動(獎品)
                                    </td>
                                }
                            }
                            else
                            {

                                <td style="text-align: left;white-space:normal" title="@IAWARD_KIND">

                                    @Html.DisplayFor(modelItem => item.SOURCE_TYPE)
                                </td>}

                        }
                        else
                        {

                            <td style="text-align: left;white-space:normal" title="@item.LOG_DESC">

                                @Html.DisplayFor(modelItem => item.SOURCE_TYPE)
                            </td>


                        }
                    }
                    else
                    {

                        if (item.LOG_DESC.Contains("校外榮譽") || item.SOURCE_TYPEEdite == "ADDT15" || item.CHART_DESC.Contains("校外榮譽"))
                        {



                            int ADDT15Count = 0;
                            ADDT15Count = db.ADDT15.Where(x => x.BATCH_CASH_ID == item.SOURCE_NO && x.SCHOOL_NO == item.SCHOOL_NO).Count();

                            if (ADDT15Count > 0)
                            {
                                string IAWARD_KIND = "";

                                IAWARD_KIND = db.ADDT15.Where(x => x.BATCH_CASH_ID == item.SOURCE_NO && x.SCHOOL_NO == item.SCHOOL_NO && x.CASH == item.CASH_IN && x.USER_NO == item.USER_NO).Select(x => x.OAWARD_ITEM).FirstOrDefault();
                                ISEDItStatus = db.ADDT15.Where(x => x.BATCH_CASH_ID == item.SOURCE_NO && x.SCHOOL_NO == item.SCHOOL_NO && x.CASH == item.CASH_IN && x.USER_NO == item.USER_NO).Select(x => x.APPLY_STATUS).FirstOrDefault();
                                <td style="text-align: left;white-space:normal" title="@IAWARD_KIND">

                                    @Html.DisplayFor(modelItem => item.SOURCE_TYPE)
                                </td>

                            }
                            else
                            {

                                <td style="text-align:center" title="@item.LOG_DESC">
                                    @if (item.SOURCE_TYPE == "酷幣匯轉區域管理")
                                    {
                                        @showdesc

                                    }
                                    else
                                    {
                                        
                                        @StringHelper.LeftStringR(item.SOURCE_TYPE, 15)
                                    }
                                </td>
                            }



                        }
                        else
                        {
                            <td style="text-align: left;white-space:normal" title="@item.LOG_DESC">

                                @if (item.SOURCE_TYPE == "酷幣匯轉區域管理")
                                {
                                    @showdesc

                                }
                                else
                                {
                                    if (item.CHART_DESC.Contains("參加抽獎活動"))
                                    {
                                        if (item.LOG_PERSON != "系統給扣點")
                                        {
                                            @:參加抽獎活動
                                        }
                                        else
                                        { @StringHelper.LeftStringR(item.SOURCE_TYPE, 15)}
                                }
                                else
                                {
                                    @StringHelper.LeftStringR(item.SOURCE_TYPE, 15)
                                }
                            }
                            </td>}
                    }

                    <td align="center">
                        @Html.DisplayFor(modelItem => item.LOG_PERSON_NAME)
                    </td>
                    if (isButn)
                    {
                        <td style="text-align: left;white-space:nowrap">
                            @if (!(SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OTHER || SYS_TABLE_TYPE == "ADDI09" || SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OTHER_CLASS))
                            {
                                if (user.ROLE_LEVEL == (decimal)4.00 || user.USER_TYPE == "A" || user.NAME == item.LOG_PERSON)
                                {
                                    if (ISEDItStatus != "9")
                                    {



                                        <a href="@Url.Action("MODIFY", "AWA002", item)" class="btn btn-sm btn-bold btn-pink colorbox">維護</a> }
                                    @*<a class="btn btn-xs btn-Basic" href="@Url.Action("MODIFY", "AWA002", item)">維護</a><br />*@
                                }
                            }
                            else if (user?.USER_TYPE != UserType.Student && user?.USER_TYPE != UserType.Parents)
                            {

                                if (user.ROLE_LEVEL == (decimal)4.00 || user.USER_TYPE == "A" || user.NAME == item.LOG_PERSON)
                                {
                                    if (ISEDItStatus != "9")
                                    {
                                        @*<a class="btn btn-xs btn-Basic" href="@Url.Action("MODIFY", "AWA002", item)">作廢</a>*@
                                        <a href="@Url.Action("MODIFY", "AWA002", item)" class="btn btn-sm btn-bold btn-pink colorbox">編輯</a>}
                                }

                            }
                        </td>
                    }
                    else
                    {
                        <td style="text-align: left;white-space:nowrap">   </td>
                        <td style="text-align: left;white-space:nowrap">   </td>

                    }
                }
            </tr>

                TrNum = TrNum + 1;
            }
        </tbody>
    </table>
    <div>
        @Html.Pager(Model.VAWAT01_LOG.PageSize, Model.VAWAT01_LOG.PageNumber, Model.VAWAT01_LOG.TotalItemCount).Options(o => o
              .DisplayTemplate("BootstrapPagination")
             .MaxNrOfPages(5)
             .SetPreviousPageText("上頁")
             .SetNextPageText("下頁")
         )
    </div>
    <div style="height:15px"></div>
    <div style="text-align:center;">
        @if (Request["QUERY"] != null)
        {
            <a href='@Url.Action("QUERY", "AWA002")' role="button" class="btn btn-default">
                返回
            </a>
        }
    </div>
</div>

@section scripts{
    <script type="text/javascript">
            var targetFormID = '#AWA002';

        $(document).ready(function () {
            $(".colorbox").colorbox({ iframe: true, width: "80%", height: "80%", opacity: 0.82 });
        });

            function FunPageProc(page) {
                if ($(targetFormID).size() > 0) {

                    $('#Page').val(page)
                    $(targetFormID).attr('action', '@Url.Action("Query2", "AWA002")').attr('target', '_self');
                    $(targetFormID).submit();
                }

            }

            function PrintBooK() {
                $(targetFormID).attr('action', '@Url.Action("PrintQuery2", "AWA002")').attr('target','_blank');
                $(targetFormID).submit();
            };

        function ToExcel() {
            $(targetFormID).attr('action', '@Url.Action("PrintExcel", "AWA002")').attr('target', '_blank');
            $(targetFormID).submit();
         };

            function todoClear() {

                ////重設
                $("#OrderList").find(":input,:selected").each(function (i) {

                    var type = $(this).attr('type')
                    var InPreadonly = $(this).attr('readonly')
                    var tag = this.tagName.toLowerCase(); // normalize case

                    if (InPreadonly == false || InPreadonly == undefined) {

                        if (type == 'radio' || type == 'checkbox') {
                            if ($(this).attr("title") == 'Default') {
                                this.checked = true;
                            }
                            else {
                                this.checked = false;
                            }
                        }
                        else if (tag == 'select') { //下拉式選單
                            this.selectedIndex = 0;
                        }
                        else if (type == 'text' || type == 'hidden' || type == 'password' || type == 'textarea') {
                            this.value = '';
                        }
                    }
                });
                $(targetFormID).attr('action', '@Url.Action("Query2", "AWA002")').attr('target', '_self');
                $(targetFormID).submit();
            }

            function OnDetails(vSOURCE_TYPE, vSOURCE_NO)
            {
                if (vSOURCE_TYPE == 'ADDI01' || vSOURCE_TYPE == 'ADDT02') {
                    window.open('@Url.Action("Details", "ADDI01")' + '?WRITING_NO=' + vSOURCE_NO +'', '_blank');
                }
                else if (vSOURCE_TYPE == 'ADDT')
                {
                    window.open('@Url.Action("ADDTALLListDetails", "ADDT")' + '?APPLY_NO=' + vSOURCE_NO + '', '_blank');
                }
                else if (vSOURCE_TYPE == 'ADDT13') {
                    window.open('@Url.Action("detail", "ADDI05")' + '?DIALOG_ID=' + vSOURCE_NO + '', '_blank');
                }
            }

        $(document).ready(function () {

            $("#whereLOG_TIME_S,#whereLOG_TIME_E").datepicker({
                dateFormat: "yy/mm/dd",
                changeMonth: true,
                changeYear: true,
                showOn: "both",
                buttonImage: "../Content/img/icon/calendar.gif",
                buttonImageOnly: true,
            });
        });
    </script>
}