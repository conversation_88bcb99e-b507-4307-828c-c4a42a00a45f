/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX-Web/Latin/BoldItalic/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXMathJax_Latin-bold-italic"]={directory:"Latin/BoldItalic",family:"STIXMathJax_Latin",weight:"bold",style:"italic",testString:"\u00A0\u00A1\u00A2\u00A4\u00A6\u00A9\u00AA\u00AB\u00AD\u00B2\u00B3\u00B6\u00B8\u00B9\u00BA",32:[0,0,250,0,0],160:[0,0,250,0,0],161:[494,205,389,19,320],162:[576,143,500,42,439],164:[542,10,500,-26,526],166:[685,18,220,66,154],169:[685,18,747,30,718],170:[685,-399,266,16,330],171:[415,-32,500,12,468],173:[282,-166,333,2,271],178:[683,-274,300,2,313],179:[683,-265,300,17,321],182:[669,193,617,60,679],184:[5,218,333,-80,156],185:[683,-274,300,30,301],186:[685,-400,300,56,347],187:[415,-32,500,12,468],188:[683,14,750,7,721],189:[683,14,750,-9,723],190:[683,14,750,7,726],191:[492,205,500,30,421],192:[947,0,667,-68,593],193:[947,0,667,-68,593],194:[940,0,667,-68,593],195:[905,0,667,-68,612],196:[905,0,667,-68,599],197:[1004,0,667,-68,593],198:[669,0,944,-64,918],199:[685,218,667,32,677],200:[947,0,667,-27,653],201:[947,0,667,-27,653],202:[940,0,667,-27,653],203:[905,0,667,-27,653],204:[947,0,389,-32,406],205:[947,0,389,-32,440],206:[940,0,389,-32,469],207:[905,0,389,-32,480],208:[669,0,722,-31,700],209:[905,15,722,-27,748],210:[947,18,722,27,691],211:[947,18,722,27,691],212:[940,18,722,27,691],213:[905,18,722,27,691],214:[905,18,722,27,691],216:[764,125,722,27,691],217:[947,18,722,67,744],218:[947,18,722,67,744],219:[940,18,722,67,744],220:[905,18,722,67,744],221:[947,0,611,71,659],222:[669,0,611,-27,573],223:[705,200,500,-200,473],224:[697,14,500,-21,456],225:[697,14,500,-21,456],226:[690,14,500,-21,475],227:[655,14,500,-21,497],228:[655,14,500,-21,485],229:[756,14,500,-21,456],230:[462,13,722,-5,673],231:[462,218,444,-24,392],232:[697,13,444,5,398],233:[697,13,444,5,419],234:[690,13,444,5,462],235:[655,13,444,5,470],236:[697,9,278,2,294],237:[697,9,278,2,310],238:[690,9,278,2,353],239:[655,9,278,2,362],241:[655,9,556,-6,507],242:[697,13,500,-3,441],243:[697,13,500,-3,441],244:[690,13,500,-3,462],245:[655,13,500,-3,485],246:[655,13,500,-3,470],248:[560,119,500,-3,441],249:[697,9,556,15,493],250:[697,9,556,15,493],251:[690,9,556,15,493],252:[655,9,556,15,493],253:[697,205,444,-94,401],254:[699,205,500,-120,446],255:[655,205,444,-94,460],256:[793,0,667,-68,593],257:[586,14,500,-21,486],258:[885,0,667,-68,593],259:[678,14,500,-21,483],260:[683,173,667,-68,640],261:[462,173,500,-21,507],262:[904,18,667,32,677],263:[697,13,444,-5,392],264:[897,18,667,32,677],265:[690,13,444,-5,415],266:[862,18,667,32,677],267:[655,13,444,-5,392],268:[897,18,667,32,677],269:[690,13,444,-5,437],270:[897,0,722,-46,685],271:[710,13,658,-21,726],272:[669,0,722,-31,700],273:[699,13,500,-21,541],274:[793,0,667,-27,653],275:[586,13,444,5,431],276:[885,0,667,-27,653],277:[678,13,444,5,478],278:[862,0,667,-27,653],279:[655,13,444,5,398],280:[669,182,667,-27,653],281:[462,182,444,5,398],282:[897,0,667,-27,653],283:[690,13,444,5,486],284:[897,18,722,21,705],285:[690,203,500,-52,477],286:[885,18,722,21,705],287:[678,203,500,-52,477],288:[862,18,722,21,705],289:[655,203,500,-52,477],290:[685,359,722,21,705],291:[832,203,500,-52,477],292:[897,0,778,-24,799],293:[897,9,556,-13,498],294:[669,0,778,-24,800],296:[862,0,389,-32,470],297:[655,9,278,-9,350],298:[793,0,389,-32,451],299:[586,9,278,-11,331],300:[885,0,389,-32,458],301:[678,9,278,2,328],302:[669,173,389,-32,406],303:[684,173,278,2,262],304:[862,0,389,-32,406],306:[669,99,823,-32,913],307:[685,207,552,2,544],308:[897,99,500,-46,554],309:[690,207,278,-189,314],310:[669,359,667,-21,702],311:[699,359,500,-23,483],312:[470,0,600,6,689],313:[904,0,611,-22,590],314:[904,9,278,2,344],315:[669,359,611,-22,590],316:[699,359,278,-62,290],317:[685,0,611,-22,667],318:[710,9,451,2,499],319:[669,0,611,-22,590],320:[699,9,375,2,382],321:[669,0,611,-22,590],322:[699,9,278,-13,301],323:[904,15,722,-27,748],324:[697,9,556,-6,494],325:[669,359,722,-27,748],326:[462,359,556,-6,494],327:[897,15,722,-27,748],328:[690,9,556,-6,506],329:[710,9,700,42,657],330:[669,203,722,-46,685],331:[462,207,543,-6,474],332:[793,18,722,27,691],333:[586,13,500,-3,461],334:[885,18,722,27,691],335:[678,13,500,-3,488],336:[904,18,722,27,700],337:[697,13,500,-3,519],338:[677,8,944,23,946],339:[462,13,722,6,674],340:[904,0,667,-28,623],341:[697,0,389,-21,389],342:[669,359,667,-28,623],343:[462,359,389,-102,389],344:[897,0,667,-28,623],345:[690,0,389,-21,411],346:[904,18,556,2,526],347:[697,13,389,-19,379],348:[897,18,556,2,526],349:[690,13,389,-19,367],350:[685,218,556,2,526],351:[462,218,389,-19,333],352:[897,18,556,2,526],353:[690,13,389,-19,411],354:[669,218,611,49,650],355:[594,218,278,-75,289],356:[897,0,611,49,650],357:[710,9,411,-11,499],358:[669,0,611,49,650],359:[594,9,278,-30,281],360:[841,18,722,67,744],361:[655,9,556,15,493],362:[793,18,722,67,744],363:[586,9,556,15,493],364:[885,18,722,67,744],365:[678,9,556,15,493],366:[921,18,722,67,744],367:[729,9,556,15,493],368:[889,18,722,67,744],369:[697,9,556,15,527],370:[669,173,722,67,744],371:[462,173,556,15,531],372:[897,18,889,64,940],373:[690,13,667,15,614],374:[897,0,611,71,659],375:[690,205,444,-94,393],376:[862,0,611,71,659],377:[904,0,611,-12,589],378:[697,78,389,-43,379],379:[862,0,611,-12,589],380:[655,78,389,-43,368],381:[897,0,611,-12,589],382:[690,78,389,-43,411],383:[691,0,333,14,536],384:[699,13,500,-14,444],392:[576,13,560,-5,627],400:[686,4,512,54,676],402:[707,156,500,-87,537],405:[699,10,735,-13,692],409:[691,8,500,-23,483],410:[699,9,278,2,290],411:[666,0,480,16,452],414:[462,205,536,-6,474],416:[716,18,722,27,806],417:[507,13,537,24,595],421:[673,205,520,-100,466],426:[684,233,400,46,380],427:[594,218,286,-49,289],429:[691,9,360,-3,450],431:[803,18,775,67,893],432:[583,9,556,15,656],442:[450,237,496,-52,458],443:[683,0,500,-27,469],446:[541,10,500,37,463],448:[740,0,208,14,278],449:[740,0,345,14,415],450:[740,0,368,14,438],451:[684,13,300,45,355],496:[690,207,350,-104,474],506:[972,0,667,-68,593],507:[909,14,500,-21,456],508:[904,0,944,-64,918],509:[697,13,722,-5,673],510:[904,125,722,27,691],511:[697,119,500,-3,441],7808:[904,18,889,64,940],7809:[697,13,667,15,614],7810:[904,18,889,64,940],7811:[697,13,667,15,614],7812:[862,18,889,64,940],7813:[655,13,667,15,614],7922:[904,0,611,71,659],7923:[697,205,444,-94,392],64256:[698,205,613,-169,726],64257:[703,205,556,-188,514],64258:[704,205,556,-186,553],64259:[703,205,856,-169,814],64260:[704,205,854,-169,851]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"STIXMathJax_Latin-bold-italic"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Latin/BoldItalic/Main.js"]);
