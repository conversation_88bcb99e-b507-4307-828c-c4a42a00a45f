﻿@{
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    if (string.IsNullOrWhiteSpace(UUID) == false)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }

    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

    var ADDTList_apply_YN = com.ecool.service.PermissionService.GetPermission_Use_YN("ADDT", "ADDTList_apply", user?.SCHOOL_NO, user?.USER_NO);

    if (user != null)
    {
        if (user.USER_TYPE == UserType.Student)
        {
            ViewBag.Title = "閱讀認證-我的閱讀認證";
        }
        else
        {
            if (ADDTList_apply_YN == "Y")
            {
                ViewBag.Title = "閱讀認證-閱讀認證清單-草稿、退回資料";
            }
            else
            {
                ViewBag.Title = "閱讀認證-寶貝的閱讀認證清單";
            }

        }
    }

}

@Html.Partial("_Title_Secondary")

@Html.Partial("_Notice")

@{
    Html.RenderAction("_PageMenu", "ADDT", new { NowAction = "QUERY" });
}

@using (Html.BeginForm("QUERY", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", enctype = "multipart/form-data" }))
{
    @Html.AntiForgeryToken()
    <br />
    <div id="PageContent">
        @Html.Action("_PageContent", (string)ViewBag.BRE_NO)
    </div>

    if (user != null)
    {
        @Html.Hidden("USER_TYPE", user.USER_TYPE)
    }

}

@section Scripts
    {
    <script nonce="cmlvaw">
        // 設置全局配置
        window.ADDI02_QUERY_CONFIG = {
            userTypeStudent: "@UserType.Student",
            addtListApplyYN: "@ADDTList_apply_YN",
            applyTypeWithVerify: "@ADDT06.APPLY_STATUS_Val.APPLY_TYPE_WithVerify",
            applyTypeBack: "@ADDT06.APPLY_STATUS_Val.APPLY_TYPE_Back",
            applyTypeTempSave: "@ADDT06.APPLY_STATUS_Val.APPLY_TYPE_TempSave"
        };

        // 設置全局 URL 配置
        window.ADDI02_QUERY_URLS = {
            checkPendingDetail2: "@Html.Raw(@Url.Action("ADDTList_CheckPendingDetail2", "ADDT"))",
            addtAllListDetails: "@Html.Raw(@Url.Action("ADDTALLListDetails", "ADDT"))",
            pageContent: "@Url.Action("_PageContent", (string)ViewBag.BRE_NO)"
        };
    </script>
    <script src="~/Scripts/ADDI02/query.js" nonce="cmlvaw"></script>
}