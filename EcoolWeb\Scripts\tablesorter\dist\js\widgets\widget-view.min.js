(function(factory){if (typeof define === 'function' && define.amd){define(['jquery'], factory);} else if (typeof module === 'object' && typeof module.exports === 'object'){module.exports = factory(require('jquery'));} else {factory(jQuery);}}(function(jQuery){

!function(r){"use strict";var i,a,o,n=r.tablesorter,l=!1,c=n.view={copyCaption:function(e,t){c.removeCaption(e,t),0<e.$table.find("caption").length&&r(t.view_caption).text(e.$table.find("caption").text())},removeCaption:function(e,t){r(t.view_caption).empty()},buildToolBar:function(t,o){c.removeToolBar(t,o),c.copyCaption(t,o);var n=r(o.view_toolbar);r.each(o.view_layouts,function(e,t){var i=o.view_switcher_class;e===o.view_layout&&(i+=" active");var a=r("<a>",{href:"#","class":i,"data-view-type":e,title:t.title});a.append(r("<i>",{"class":t.icon})),n.append(a)}),n.find("."+o.view_switcher_class).on("click",function(e){if(e.preventDefault(),r(this).hasClass("active"))return!1;n.find("."+o.view_switcher_class).removeClass("active"),r(this).addClass("active"),o.view_layout=r(this).attr("data-view-type"),!0===o.view_layouts[o.view_layout].raw?(c.remove(t,o),c.buildToolBar(t,o)):(!1===l&&c.hideTable(t,o),c.buildView(t,o))})},removeToolBar:function(e,t){r(t.view_toolbar).empty(),c.removeCaption(e,t)},buildView:function(e,t){c.removeView(e,t);var a=t.view_layouts[t.view_layout],o=r(a.container,{"class":t.view_layout});n.getColumnText(e.$table,0,function(e){var l=a.tmpl;r.each(r(e.$row).find("td"),function(e,t){var i={},a="{col"+e+"}";r.each(t.attributes,function(e,t){i[t.nodeName]=t.nodeValue});var o=r(t).html(),n=r("<span />").append(r("<span/>",i).append(o));l=l.replace(new RegExp(a,"g"),n.html()),a="{col"+e+":raw}",l=l.replace(new RegExp(a,"g"),r(t).text())});var i=r(l);r.each(e.$row[0].attributes,function(e,t){"class"===t.nodeName?i.attr(t.nodeName,i.attr(t.nodeName)+" "+t.nodeValue):i.attr(t.nodeName,t.nodeValue)}),o.append(i)}),r(t.view_container).append(o),e.$table.triggerHandler("viewComplete")},removeView:function(e,t){r(t.view_container).empty()},hideTable:function(e){i=e.$table.css("position"),a=e.$table.css("bottom"),o=e.$table.css("left"),e.$table.css({position:"absolute",top:"-10000px",left:"-10000px"}),l=!0},init:function(e,t){!1!==t.view_layout&&void 0!==t.view_layouts[t.view_layout]&&(!1===l&&c.hideTable(e,t),e.$table.on("tablesorter-ready",function(){c.buildToolBar(e,t),c.buildView(e,t)}))},remove:function(e,t){c.removeToolBar(e,t),c.removeView(e,t),e.$table.css({position:i,top:a,left:o}),l=!1}};n.addWidget({id:"view",options:{view_toolbar:"#ts-view-toolbar",view_container:"#ts-view",view_caption:"#ts-view-caption",view_switcher_class:"ts-view-switcher",view_layout:!1,view_layouts:{}},init:function(e,t,i,a){c.init(i,a)},remove:function(e,t,i){c.remove(t,i)}})}(jQuery);return jQuery;}));
