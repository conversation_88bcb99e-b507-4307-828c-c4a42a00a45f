﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'flash', 'tr', {
	access: 'Kod İzni',
	accessAlways: '<PERSON><PERSON><PERSON>',
	accessNever: 'Asla',
	accessSameDomain: 'Aynı domain',
	alignAbsBottom: 'Tam Altı',
	alignAbsMiddle: 'Tam Ortası',
	alignBaseline: '<PERSON>ban <PERSON>iz<PERSON>',
	alignTextTop: 'Yazı Tepeye',
	bgcolor: 'Arka Renk',
	chkFull: 'Tam ekrana İzinver',
	chkLoop: '<PERSON>öng<PERSON>',
	chkMenu: '<PERSON> Menüsünü Kullan',
	chkPlay: 'Otomatik Oynat',
	flashvars: 'Flash Değerleri',
	hSpace: 'Yatay <PERSON>',
	properties: 'Flash Özellikleri',
	propertiesTab: 'Özellikler',
	quality: 'Kalite',
	qualityAutoHigh: 'Otomatik Yükseklik',
	qualityAutoLow: 'Otomatik Düşüklük',
	qualityBest: 'En iyi',
	qualityHigh: 'Yüksek',
	qualityLow: 'Düşük',
	qualityMedium: 'Orta',
	scale: 'Boyutlandır',
	scaleAll: 'Hepsini Göster',
	scaleFit: 'Tam Sığdır',
	scaleNoBorder: 'Kenar Yok',
	title: 'Flash Özellikleri',
	vSpace: 'Dikey Boşluk',
	validateHSpace: 'HSpace sayı olmalıdır.',
	validateSrc: 'Lütfen köprü URL\'sini yazın',
	validateVSpace: 'VSpace sayı olmalıdır.',
	windowMode: 'Pencere modu',
	windowModeOpaque: 'Opak',
	windowModeTransparent: 'Şeffaf',
	windowModeWindow: 'Pencere'
} );
