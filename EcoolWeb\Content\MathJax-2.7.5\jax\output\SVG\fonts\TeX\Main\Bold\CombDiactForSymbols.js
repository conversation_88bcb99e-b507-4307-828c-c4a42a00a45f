/*
 *  /MathJax/jax/output/SVG/fonts/TeX/Main/Bold/CombDiactForSymbols.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax.SVG.FONTDATA.FONTS["MathJax_Main-bold"],{8407:[723,-513,0,-542,-33,"-169 694Q-169 707 -160 715T-142 723Q-127 723 -119 716T-107 698T-90 673T-53 648Q-33 637 -33 619Q-33 602 -45 595T-87 573T-144 532Q-165 513 -176 513Q-189 513 -197 522T-206 543Q-206 556 -188 574L-175 588H-347L-519 589Q-542 597 -542 618Q-542 623 -541 627T-537 635T-532 640T-527 644T-522 648L-519 649H-149Q-169 676 -169 694"]});MathJax.Ajax.loadComplete(MathJax.OutputJax.SVG.fontDir+"/Main/Bold/CombDiactForSymbols.js");
