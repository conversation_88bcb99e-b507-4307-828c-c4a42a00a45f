﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class GAAI01SearchSchoolRateViewModel
    {
        /// <summary>
        ///防身周期ID
        /// </summary>
        [DisplayName("防身周期ID")]
        public string ALARM_ID { get; set; }

        /// <summary>
        ///週期
        /// </summary>
        [DisplayName("週期")]
        public byte? CYCLE { get; set; }

        /// <summary>
        ///登記日期_開始
        /// </summary>
        [DisplayName("週期日期_開始")]
        [DataType(DataType.Date), DisplayFormat(DataFormatString = "{0:yyyy/MM/dd}", ApplyFormatInEditMode = true)]
        public DateTime? ALARM_DATES { get; set; }

        /// <summary>
        ///登記日期_結束
        /// </summary>
        [DisplayName("週期日期_結束")]
        [DataType(DataType.Date), DisplayFormat(DataFormatString = "{0:yyyy/MM/dd}", ApplyFormatInEditMode = true)]
        public DateTime? ALARM_DATEE { get; set; }

        /// <summary>
        /// 全校學生數
        /// </summary>
        public int STUDENT_NUMBER { get; set; }

        /// <summary>
        /// 未填報人數
        /// </summary>
        public int NotReported_NUMBER { get; set; }

        /// <summary>
        /// 配載人數
        /// </summary>
        public int WEAR_NUMBER { get; set; }

        /// <summary>
        /// 未配載人數
        /// </summary>
        public int UN_WEAR_NUMBER { get; set; }

        /// <summary>
        /// 配載率
        /// </summary>
        public decimal? WEAR_RATE { get; set; }
    }
}