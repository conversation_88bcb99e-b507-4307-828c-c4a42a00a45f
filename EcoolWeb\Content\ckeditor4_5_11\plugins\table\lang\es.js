﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'table', 'es', {
	border: 'Tamaño de Borde',
	caption: '<PERSON><PERSON><PERSON><PERSON>',
	cell: {
		menu: 'Celda',
		insertBefore: 'Insertar celda a la izquierda',
		insertAfter: 'Insertar celda a la derecha',
		deleteCell: 'Eliminar Celdas',
		merge: 'Combinar Celdas',
		mergeRight: 'Combinar a la derecha',
		mergeDown: 'Combinar hacia abajo',
		splitHorizontal: 'Dividir la celda horizontalmente',
		splitVertical: 'Dividir la celda verticalmente',
		title: 'Propiedades de celda',
		cellType: 'Tipo de Celda',
		rowSpan: 'Expandir filas',
		colSpan: 'Expandir columnas',
		wordWrap: 'Ajustar al contenido',
		hAlign: 'Alineación Horizontal',
		vAlign: 'Alineación Vertical',
		alignBaseline: 'Linea de base',
		bgColor: 'Color de fondo',
		borderColor: 'Color de borde',
		data: 'Datos',
		header: 'Encabezado',
		yes: 'Sí',
		no: 'No',
		invalidWidth: 'La anchura de celda debe ser un número.',
		invalidHeight: 'La altura de celda debe ser un número.',
		invalidRowSpan: 'La expansión de filas debe ser un número entero.',
		invalidColSpan: 'La expansión de columnas debe ser un número entero.',
		chooseColor: 'Elegir'
	},
	cellPad: 'Esp. interior',
	cellSpace: 'Esp. e/celdas',
	column: {
		menu: 'Columna',
		insertBefore: 'Insertar columna a la izquierda',
		insertAfter: 'Insertar columna a la derecha',
		deleteColumn: 'Eliminar Columnas'
	},
	columns: 'Columnas',
	deleteTable: 'Eliminar Tabla',
	headers: 'Encabezados',
	headersBoth: 'Ambas',
	headersColumn: 'Primera columna',
	headersNone: 'Ninguno',
	headersRow: 'Primera fila',
	invalidBorder: 'El tamaño del borde debe ser un número.',
	invalidCellPadding: 'El espaciado interior debe ser un número.',
	invalidCellSpacing: 'El espaciado entre celdas debe ser un número.',
	invalidCols: 'El número de columnas debe ser un número mayor que 0.',
	invalidHeight: 'La altura de tabla debe ser un número.',
	invalidRows: 'El número de filas debe ser un número mayor que 0.',
	invalidWidth: 'La anchura de tabla debe ser un número.',
	menu: 'Propiedades de Tabla',
	row: {
		menu: 'Fila',
		insertBefore: 'Insertar fila en la parte superior',
		insertAfter: 'Insertar fila en la parte inferior',
		deleteRow: 'Eliminar Filas'
	},
	rows: 'Filas',
	summary: 'Síntesis',
	title: 'Propiedades de Tabla',
	toolbar: 'Tabla',
	widthPc: 'porcentaje',
	widthPx: 'pixeles',
	widthUnit: 'unidad de la anchura'
} );
