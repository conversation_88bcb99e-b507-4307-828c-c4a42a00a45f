/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX-Web/Size5/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.STIXMathJax_Size5={directory:"Size5/Regular",family:"STIXMathJax_Size5",testString:"\u00A0\u02C6\u02C7\u02DC\u02F7\u0302\u0303\u0305\u030C\u0330\u0332\u0338\u203E\u20D0\u20D1",32:[0,0,250,0,0],160:[0,0,250,0,0],710:[816,-572,2328,0,2328],711:[816,-572,2328,0,2328],732:[780,-617,2328,0,2328],759:[-117,280,2328,0,2328],770:[816,-572,2328,0,2328],771:[780,-617,2328,0,2328],773:[820,-770,3000,0,3000],780:[816,-572,2328,0,2328],816:[-117,280,2328,0,2328],818:[-127,177,3000,0,3000],824:[960,454,0,-561,-123],8254:[820,-770,3000,0,3000],8400:[749,-584,3000,0,3000],8401:[749,-584,3000,0,3000],8406:[735,-482,3000,0,3000],8407:[735,-482,3000,0,3000],8428:[-123,288,3000,0,3000],8429:[-123,288,3000,0,3000],8430:[-26,279,3000,0,3000],8431:[-26,279,3000,0,3000],9140:[766,-544,3237,90,3147],9141:[139,83,3237,90,3147],9180:[80,189,3237,0,3237],9181:[842,-573,3237,0,3237],9182:[181,90,3238,0,3238],9183:[844,-573,3238,0,3238],9184:[66,212,3164,0,3164],9185:[842,-564,3164,0,3164],57344:[705,300,450,50,400],57345:[705,305,450,50,174],57346:[700,305,450,50,400],57347:[705,300,450,50,400],57348:[705,305,450,276,400],57349:[700,305,450,50,400],57350:[687,318,450,50,415],57351:[687,323,450,50,150],57352:[682,323,450,50,415],57353:[687,318,450,35,400],57354:[687,323,450,300,400],57355:[682,323,450,35,400],57356:[705,300,640,260,600],57357:[705,305,640,260,380],57358:[705,305,640,40,380],57359:[700,305,640,260,600],57360:[705,300,640,40,380],57361:[705,305,640,260,600],57362:[700,305,640,40,380],57363:[820,-770,1000,0,1000],57364:[-127,177,1000,0,1000],57365:[749,-584,870,0,871],57366:[634,-584,480,-10,490],57367:[749,-584,871,0,871],57368:[735,-482,871,0,872],57369:[736,-482,871,0,872],57370:[-127,177,480,-10,490],57371:[-123,288,871,0,871],57372:[-123,288,871,0,871],57373:[-26,279,871,0,872],57374:[-25,279,871,0,872],57375:[386,-120,315,0,315],57376:[405,-101,686,210,476],57377:[486,-20,315,0,315],57378:[1855,0,1184,112,895],57379:[635,0,1184,829,895],57380:[626,0,1184,829,1211],57381:[2140,0,1184,112,895],57382:[2135,0,1184,112,895],57383:[955,-554,1820,-25,1830],57384:[955,-820,633,-1,634],57385:[955,-554,1820,-10,1845],57386:[140,261,1820,-25,1830],57387:[-126,261,633,-1,634],57388:[140,261,1820,-10,1845],57389:[955,-342,1820,-25,1830],57390:[955,-342,1820,-10,1845],57391:[352,261,1820,-25,1830],57392:[352,261,1820,-10,1845],57393:[955,-512,897,-25,908],57394:[1218,-820,1844,-10,1854],57395:[955,-512,897,-11,922],57396:[182,261,897,-25,908],57397:[-126,524,1844,-10,1854],57398:[182,261,897,-11,922],57399:[405,-101,1033,229,805],57400:[405,-101,926,230,696],57401:[541,35,315,0,315],57402:[700,301,600,35,566],57403:[700,301,600,35,566],57404:[1066,79,688,294,574],57405:[610,25,688,294,394],57406:[1086,59,688,115,394]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"STIXMathJax_Size5"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Size5/Regular/Main.js"]);
