﻿using MvcPaging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class TeacherModalViewModel : SearchFormViewModelBase
    {
        //列表資料
        public IPagedList<HRMT01> Teachers;

        //原來的資料
        public ICollection<HRMT01> oTeachers { get; set; }

        public string SCHOOL_NO { get; set; }

        //加入時，母視窗 的  PartialView 畫面內容
        public string UrlAction { get; set; }

        /// <summary>
        /// //加入時「畫面」寫入 ，母視窗 的 要加入 Div 位置
        /// </summary>
        public string DivEditorRowsID { get; set; }
    }
}