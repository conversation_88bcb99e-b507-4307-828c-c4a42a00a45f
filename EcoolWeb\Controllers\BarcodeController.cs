﻿using System.IO;
using System.Web.Mvc;
using ZXing;
using ZXing.QrCode;
using ZXing.QrCode.Internal;
using System.Drawing.Imaging;
using ZXing.Rendering;
using System.Drawing;
using System.Drawing.Drawing2D;
using System;
using System.Net;
using ZXing.Common;

namespace EcoolWeb.Controllers
{
    public class BarcodeController : Controller
    {

      
        // GET: Barcode
        public ActionResult Index()
        {
            return View();
        }


        public ActionResult Cre(string Value)
        {
            var qrCodeWriter = new BarcodeWriter
            {
                Format = BarcodeFormat.QR_CODE,
                Options = new QrCodeEncodingOptions
                {
                    Margin = 1,
                    Height = 300,
                    Width = 300,
                    ErrorCorrection = ErrorCorrectionLevel.Q,
                    CharacterSet = "UTF-8",
                },
            };


            Bitmap writeableBitmap = qrCodeWriter.Write(Value);

            var memoryStream = new MemoryStream();
            writeableBitmap.Save(memoryStream, ImageFormat.Jpeg);
            return File(memoryStream.GetBuffer(), "image/jpeg");
           
        }
        public ActionResult GenerateCode39(string text)
        {
            BarcodeWriter writer = new BarcodeWriter();
            writer.Format = BarcodeFormat.CODE_39;

            writer.Renderer = new ZXing.Rendering.BitmapRenderer
            {
                Background = Color.White,
            };

            EncodingOptions options = new EncodingOptions()
            {
                Margin = 0,
            };
            options.Height = 100;
            options.Width = 400;
            //if (width != null)
            //{
            //    options.Width = (int)width;
            //}

            //if (height != null)
            //{
            //    options.Height = (int)height;
            //}

            writer.Options = options;
            Bitmap map = writer.Write(text);
            var memoryStream = new MemoryStream();
            map.Save(memoryStream, ImageFormat.Jpeg);
            return File(memoryStream.GetBuffer(), "image/jpeg");
            //map.Save(savePath, ImageFormat.Png);
            //map.Dispose();
        }

        /// <summary>
        /// 產生 QRCod 圖中 帶自已加入的圖 .ex LOGO
        /// </summary>
        /// <param name="width"></param>
        /// <param name="height"></param>
        /// <param name="Value"></param>
        /// <param name="ImgPath"></param>
        /// <returns></returns>
        public ActionResult GenerateQR(int width, int height, string Value,string ImgPath)
        {
            var bw = new ZXing.BarcodeWriter();

            var encOptions = new ZXing.Common.EncodingOptions
            {
                Margin = 1,
                Width = width,
                Height = height,
                PureBarcode = false,
            };

            encOptions.Hints.Add(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);
            encOptions.Hints.Add(EncodeHintType.CHARACTER_SET, "UTF-8");


          

            bw.Renderer = new BitmapRenderer();
            bw.Options = encOptions;
            bw.Format = ZXing.BarcodeFormat.QR_CODE;
            Bitmap bm = bw.Write(Value);

            Uri uri = new Uri(ImgPath);
            WebRequest webRequest = WebRequest.Create(uri);
            Stream stream = webRequest.GetResponse().GetResponseStream();

            Bitmap overlay = new Bitmap(stream);

            if ((overlay.Width) > (width * 0.4) || (overlay.Height ) > (height * 0.4))
            {
                int NewW = Convert.ToInt16(width * 0.4);
                int NewH = Convert.ToInt16(height * 0.4);

                overlay = ScaleImage(overlay, NewW, NewH);
            }

           



            int deltaHeigth = bm.Height - overlay.Height;
            int deltaWidth = bm.Width - overlay.Width;

            Graphics g = Graphics.FromImage(bm);
            g.DrawImage(overlay, new Point(deltaWidth / 2, deltaHeigth / 2));

            var memoryStream = new MemoryStream();
            bm.Save(memoryStream, ImageFormat.Jpeg);
            return File(memoryStream.GetBuffer(), "image/jpeg");

        }

        public Bitmap ScaleImage(Bitmap image, int maxWidth, int maxHeight)
        {
            var ratioX = (double)maxWidth / image.Width;
            var ratioY = (double)maxHeight / image.Height;
            var ratio = Math.Min(ratioX, ratioY);

            var newWidth = (int)(image.Width * ratio);
            var newHeight = (int)(image.Height * ratio);

            var newImage = new Bitmap(maxWidth, maxWidth);
            using (var graphics = Graphics.FromImage(newImage))
            {
                // Calculate x and y which center the image
                int y = (maxHeight / 2) - newHeight / 2;
                int x = (maxWidth / 2) - newWidth / 2;

                // Draw image on x and y with newWidth and newHeight
                graphics.DrawImage(image, x, y, newWidth, newHeight);
            }

            return newImage;
        }


        /// <summary>
        /// 產生 QRCod 圖中加入 文字
        /// </summary>
        /// <param name="width">QRCod width</param>
        /// <param name="height">QRCod height</param>
        /// <param name="Value">QRCod Value</param>
        /// <param name="TxtValue">圖的文字</param>
        /// <param name="x">圖的文字 位置</param>
        /// <param name="y">圖的文字 位置</param>
        /// <param name="TxtWidth">圖的文字 Width</param>
        /// <param name="TxtHeight">圖的文字 TxtHeight</param>
        /// <returns></returns>
        public ActionResult CreTxtQRCode(int width, int height, string Value, string TxtValue,int x,int y ,int  TxtWidth, int TxtHeight)
        {

            var qrCodeWriter = new BarcodeWriter
            {
                Format = BarcodeFormat.QR_CODE,
                Options = new QrCodeEncodingOptions
                {
                    Margin = 1,
                    Height = height,
                    Width = width,
                    ErrorCorrection = ErrorCorrectionLevel.Q,
                    CharacterSet = "UTF-8",
                },
            };


            Bitmap writeableBitmap = qrCodeWriter.Write(Value);


            RectangleF rectf = new RectangleF(x, y, TxtWidth, TxtHeight);

            Graphics g = Graphics.FromImage(writeableBitmap);

            g.SmoothingMode = SmoothingMode.AntiAlias;
            g.InterpolationMode = InterpolationMode.HighQualityBicubic;
            g.PixelOffsetMode = PixelOffsetMode.HighQuality;
            g.DrawString(TxtValue, new Font("Tahoma", 8), Brushes.Red, rectf);

            g.Flush();

            var memoryStream = new MemoryStream();
            writeableBitmap.Save(memoryStream, ImageFormat.Jpeg);
            return File(memoryStream.GetBuffer(), "image/jpeg");
        }




    }
}