//bootstrap v4.1.3 設置
//BS4參數調整
$font-family-sans-serif: "微軟正黑體",
-apple-system,
BlinkMacSystemFont,
"Segoe UI",
"Roboto",
"Helvetica Neue",
Arial,
sans-serif,
"Apple Color Emoji",
"Segoe UI Emoji",
"Segoe UI Symbol";
$font-size-base: 1rem;
@import "eccATM/bs4-variables";
@import "eccATM/bs4-custom";

//符號icon載入設定
$fa-font-path: "../fonts";
@import "font-awesome/font-awesome";

//----客製化---//
//元件模組
@import "eccATM/_typography.scss";
@import "eccATM/_icon.scss";
@import "eccATM/_table.scss";
@import "eccATM/_jquery.jConveyorTicker.min.scss"; //跑馬燈 jQuery 樣式

//標籤修改
body {
  background-color: #b5e2e3;
  padding-bottom: 1rem;

  @include media-breakpoint-up(lg) {
    padding-bottom: 0;
  }
}

//頁面標題
.page-title {
  padding: .525rem 0;
  font-size: 2rem;
  letter-spacing: 0.5rem;
  text-align: center;
  position: relative;
  color: $green;

  @include media-breakpoint-up(md) {
    padding: 2.325rem 0;
    font-size: 3rem;
  }

  &:before,
  &:after {
    content: "";
    display: block;
    position: absolute;
    top: 50%;
    transform: translateY(-25%);
    width: calc((100% - 250px) / 2);
    height: 3px;
    background-color: $primary;

    @include media-breakpoint-up(md) {
      width: calc((100% - 320px) / 2);
    }
  }

  &:before {
    display: block;
    left: 0;
  }

  &:after {
    display: block;
    right: 0;
  }

  .icon-size-lg::before {
    height: 1.1em;
    width: 1em;
    background-repeat: no-repeat;

    @include media-breakpoint-up(md) {
      height: 3.5rem;
      width: 3.5rem;
    }
  }
}

//區塊(章節)
section {
  padding: 0 1rem;

  &+& {
    margin-top: 1rem;
  }
}

//累計點數排行榜
.atm-honorRoll {
  &-box{
    margin-top: -1rem;
    margin-bottom: -1.725rem;
    z-index: 1;
    @include media-breakpoint-up(md) {
      margin-top:0;
      margin-bottom: 0;
    }
  }
  &-title {
    font-size: 1.225rem;

    @include media-breakpoint-up(lg) {
      font-size: 1.8rem;
    }
  }

  .icon-bigMedal {
    font-size: 3rem;
    @include media-breakpoint-up(lg) {
      font-size: 5rem;
      vertical-align: text-bottom;
    }
  }

}

.atm-honorRoll-bg {
  border: 4px dashed darken($warning, 10%);
  width: 100%;
  padding: .5rem;
  background-color: $warning;
  border-radius: 0.5rem;

  @include media-breakpoint-up(lg) {
    padding: .725rem 1.5rem;
  }
}

.atm-honorRoll-list {
  margin: 0;
  padding: 0;
  list-style: none;
  font-size: 1rem;
  font-weight: bolder;
  color: #8a4000;

  li {
    display: block;
    text-align: center;

    @include media-breakpoint-up(md) {
      display: inline-block;
      margin-right: 1.5rem;
    }

    @include media-breakpoint-up(lg) {
      font-size: 1.5rem;
    }

    span {
      color: #000;
    }
  }
}

.atm-info {

  .fa,
  .icon {
    font-size: 1.125rem;

    @include media-breakpoint-up(lg) {
      font-size: 2rem;
    }
  }
}

//我要查詢區塊
.searchArea {

  .form-control {
    height: auto;
  }

  &-title {
    color: $gray-800;
    padding: 0.25rem 0;
    margin-bottom: 0;
  }

  .btn {
    padding: 0.375rem 1rem;
  }

  input {
    &.form-control {
      text-indent: 2rem;
    }
  }

  .input-group {
    position: relative;

    .placeholderIcon {
      position: absolute;
      top: 6px;
      left: 12px;
      color: $primary;
      z-index: 100;
    }

    .placeholderIcon+input {
      padding-top: 0.6rem;

      &::placeholder {
        text-indent: 2.25rem;
        font-size: 1.35rem;
        ;
      }
    }
  }
}

//跑馬燈區塊
.marquee {
  display: flex;
  background-color: lighten($info, 4%);
  border: 2px dashed lighten($primary, 6%);
  padding: .325rem 1rem;
  border-radius: 0.5rem;
  align-items: center;
  justify-content: space-around;

  //custom
  .js-conveyor-1 {
    width: calc(100% - 3.5rem);

    li {
      padding-right: 10rem;
      line-height: 1.5;
      font-size: 1.325rem;
    }
  }

}

//右側區塊
.sideArea {

  &-btnGroup {
    .col-6:first-child {
      padding-right: 8px;
    }

    .col-6:last-child {
      padding-left: 8px;
    }
  }

  .btn {
    font-size: 1.25rem;
    font-weight: bold;
    line-height: 1.8;
    border-radius: 1rem;
  }

  //兌換獎品
  .btn-light-orange {
    border: 2px solid lighten($orange, 15%);
  }

  //返回
  .btn-light {
    color: $gray-700;
    border: 2px solid $gray-500;
  }

  //存款
  .btn-deposit {
    color: #8f009f;
    background-color: #ebd2ff;
    border: 2px solid #da80fb;
  }

  //iStory
  .btn-info {
    border: 2px solid darken($info, 15%);
    color: darken($info, 40%);
  }

  .img-box {
    text-align: center;
    background-color: #81caf7;

    img {
      max-width: 240px;
      width: 100%;

      @include media-breakpoint-up(lg) {
        max-width: 100%;
      }
    }
  }
}

//查詢結果
.resultDetail {
  padding: .5rem 0 0 0;
  font-size: .925rem;

  @include media-breakpoint-up(md) {
    font-size: 1rem;
  }

  p {
    margin-bottom: .3rem;
  }

  strong {
    display: block;
    color: #237d80;

    @include media-breakpoint-up(md) {
      display: inline;
    }
  }
}