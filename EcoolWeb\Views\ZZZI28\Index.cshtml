﻿@model ECOOL_APP.com.ecool.Models.DTO.ZZZI28QueryViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@if (user != null)
{
    if (user.USER_TYPE == UserType.Parents)
    {
        <a role="button" href='@Url.Action("Edit_last", (string)ViewBag.BRE_NO ,new { REF_BRE_NO=(string)ViewBag.BRE_NO})' class="btn btn-sm btn-sys">
            新增綁定
        </a>
    }
}


<div class="panel panel-ACC">
    <div class="panel-heading text-center">
        @Html.BarTitle()
    </div>
    <div class="table-responsive">
        <table class="table-ecool table-92Per table-hover table-ecool-ACC">
            <thead>
                <tr>

                    <th style="text-align: center;">
                        年級
                    </th>
                    <th style="text-align: center">
                        班級
                    </th>
                    <th style="text-align: center">
                        學號
                    </th>
                    <th style="text-align: center">
                        座號
                    </th>
                    <th style="text-align: center">
                        姓名
                    </th>
                    <th style="text-align: center">
                        學生帳號狀態
                    </th>
                    <th style="text-align: center">

                    </th>
                </tr>
            </thead>
            <tbody>
                @foreach (var item in Model.HRMT01List)
                {
                    <tr align="center">
                        <td>
                            @HRMT01.ParserGrade(item.GRADE)
                        </td>
                        <td>
                            @Html.DisplayFor(modelItem => item.CLASS_NO)
                        </td>
                        <td>
                            @Html.DisplayFor(modelItem => item.USER_NO)
                        </td>
                        <td>
                            @Html.DisplayFor(modelItem => item.SEAT_NO)
                        </td>
                        <td>
                            @Html.DisplayFor(modelItem => item.NAME)
                        </td>
                        <td>
                            @UserStaus.GetDesc(item.USER_STATUS)
                        </td>
                        <td>
                            <a class="btn btn-xs btn-Basic" role="button" onclick="gogo = '@Url.Action("DEL", (string)ViewBag.BRE_NO, new { STUDENT_USER_NO = item.USER_NO})'; currentForm = this; $('#dialog-confirm').dialog('open');">刪除</a>

                        </td>
                    </tr>
                }
            </tbody>
        </table>
    </div>
</div>
<div class="p-context">
    功能說明：當您有兩位以上的子女就讀同一間學校時，若想同時查詢所有子女的文章，可透過此功能來完成；若您不想綁定，也可以透過App設定功能中的[切換身分]來切換不同子女之間的帳號。
</div>
<link href="~/Content/css/jquery-ui.min.css" rel="stylesheet" />
<script src="~/Scripts/jquery-3.6.4.min.js"></script>
@*<script src="~/Scripts/jquery-1.10.2.min.js"></script>*@
<script src="~/Scripts/jquery-ui.min.js"></script>

<div id="dialog-confirm" style="display:none;" title="確認刪除？">
    <span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span>您確定要刪除嗎？
</div>
<script language="javascript">
    var currentForm;
    var gogo;
    $("#dialog-confirm").dialog({
        resizable: false,
        autoOpen: false,
        height: 180,
        modal: true,
        buttons: {
            '確認': function () {
                document.location.href = gogo;
            },
            '取消': function () {
                $(this).dialog('close');
            }
        }
    });
    $('#checkin').submit(function () {
        currentForm = this; $('#dialog-confirm').dialog('open');
        return false;
    });

</script>