
                       @echo off
chcp 65001 > nul
setlocal enabledelayedexpansion

rem 设置变量
set "url=https://ecc.tp.edu.tw:443/EcoolWeb/ZZZI091/ExportResultView3?School_No=403605&USER_NO=112006&S_DATE=&E_DATE=&IDNO=&COVERJPG=coverA.jpg&UploadCoverFileName=&redirect=&ReadYN=&&LogimUser=Y"
set "output_file=D:\Website\EcoolWeb\Content\403605\101\臺北市內湖區西湖國民小學徐梓騫的國小回憶.pdf"

rem 启动 Chrome 以打开网址
start "Chrome" "C:\Program Files\Google\Chrome\Application\chrome.exe" --headless --disable-gpu --remote-debugging-port=9222 --print-to-pdf="!output_file!" -- "!url!"

rem 等待10秒
timeout /t 10

rem 关闭 Chrome
taskkill /IM chrome.exe /F

endlocal