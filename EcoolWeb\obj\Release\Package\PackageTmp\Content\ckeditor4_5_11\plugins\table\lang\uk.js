﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'table', 'uk', {
	border: 'Розмір рамки',
	caption: 'Заголовок таблиці',
	cell: {
		menu: 'Комірки',
		insertBefore: 'Вставити комірку перед',
		insertAfter: 'Вставити комірку після',
		deleteCell: 'Видалити комірки',
		merge: 'Об\'єднати комірки',
		mergeRight: 'Об\'єднати справа',
		mergeDown: 'Об\'єднати донизу',
		splitHorizontal: 'Розділити комірку по горизонталі',
		splitVertical: 'Розділити комірку по вертикалі',
		title: 'Властиво<PERSON>ті комірки',
		cellType: 'Тип комірки',
		rowSpan: 'Об\'єднання рядків',
		colSpan: 'Об\'єднання стовпців',
		wordWrap: 'Автоперенесення тексту',
		hAlign: 'Гориз. вирівнювання',
		vAlign: 'Верт. вирівнювання',
		alignBaseline: 'По базовій лінії',
		bgColor: 'Колір фону',
		borderColor: 'Колір рамки',
		data: 'Дані',
		header: 'Заголовок',
		yes: 'Так',
		no: 'Ні',
		invalidWidth: 'Ширина комірки повинна бути цілим числом.',
		invalidHeight: 'Висота комірки повинна бути цілим числом.',
		invalidRowSpan: 'Кількість об\'єднуваних рядків повинна бути цілим числом.',
		invalidColSpan: 'Кількість об\'єднуваних стовбців повинна бути цілим числом.',
		chooseColor: 'Обрати'
	},
	cellPad: 'Внутр. відступ',
	cellSpace: 'Проміжок',
	column: {
		menu: 'Стовбці',
		insertBefore: 'Вставити стовбець перед',
		insertAfter: 'Вставити стовбець після',
		deleteColumn: 'Видалити стовбці'
	},
	columns: 'Стовбці',
	deleteTable: 'Видалити таблицю',
	headers: 'Заголовки стовбців/рядків',
	headersBoth: 'Стовбці і рядки',
	headersColumn: 'Стовбці',
	headersNone: 'Без заголовків',
	headersRow: 'Рядки',
	invalidBorder: 'Розмір рамки повинен бути цілим числом.',
	invalidCellPadding: 'Внутр. відступ комірки повинен бути цілим числом.',
	invalidCellSpacing: 'Проміжок між комірками повинен бути цілим числом.',
	invalidCols: 'Кількість стовбців повинна бути більшою 0.',
	invalidHeight: 'Висота таблиці повинна бути цілим числом.',
	invalidRows: 'Кількість рядків повинна бути більшою 0.',
	invalidWidth: 'Ширина таблиці повинна бути цілим числом.',
	menu: 'Властивості таблиці',
	row: {
		menu: 'Рядки',
		insertBefore: 'Вставити рядок перед',
		insertAfter: 'Вставити рядок після',
		deleteRow: 'Видалити рядки'
	},
	rows: 'Рядки',
	summary: 'Детальний опис заголовку таблиці',
	title: 'Властивості таблиці',
	toolbar: 'Таблиця',
	widthPc: 'відсотків',
	widthPx: 'пікселів',
	widthUnit: 'Одиниці вимір.'
} );
