﻿/**
 * @license Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
 * For licensing, see LICENSE.md or http://ckeditor.com/license
 */

CKEDITOR.editorConfig = function( config ) {
   

	// %REMOVE_START%
	// The configuration options below are needed when running CKEditor from source files.
    config.plugins = 'dialogui,dialog,about,a11yhelp,dialogadvtab,basicstyles,bidi,blockquote,clipboard,button,panelbutton,panel,floatpanel,colorbutton,colordialog,templates,menu,contextmenu,div,resize,toolbar,elementspath,enterkey,entities,popup,filebrowser,find,fakeobjects,flash,floatingspace,listblock,richcombo,font,forms,format,horizontalrule,htmlwriter,iframe,wysiwygarea,image,indent,indentblock,indentlist,smiley,justify,menubutton,language,link,list,liststyle,magicline,maximize,newpage,pagebreak,pastetext,pastefromword,preview,print,removeformat,save,selectall,showblocks,showborders,sourcearea,specialchar,scayt,stylescombo,tab,table,tabletools,undo,wsc,video,audio';
	config.skin = 'moonocolor';
    // %REMOVE_END%

	//config.extraPlugins = 'video';

	// Define changes to default configuration here. For example:
	config.language = 'zh';
	config.uiColor = '#FFFFFF';

	config.allowedContent = true;


    //////////////////////////////////////////////////// 工具列
	config.toolbar_Basic =
    [
       ['Bold', 'Italic', '-', 'NumberedList', 'BulletedList', '-', 'Link', 'Unlink'],
       ['FontSize', 'TextColor', 'BGColor'],
       ['ShowBlock', 'Source'],
    ];


	config.toolbar = [
           { name: 'clipboard', items: ['Cut', 'Copy', 'Paste', 'PasteFromWord', '-', 'Undo', 'Redo'] },
            { name: 'colors', items: ['FontSize', 'TextColor', 'BGColor'] },
             '/',
           { name: 'basicstyles', items: ['SpecialChar', '-', 'Bold', 'Italic', 'Underline', 'Strike', 'Subscript', 'Superscript', '-', 'RemoveFormat'] },
            '/',
           { name: 'tools', items: ['Maximize', 'ShowBlocks'] },
           { name: 'document', items: ['Source'] }

	];

	config.toolbar_Image =
    [
          { name: 'document', items: ['Source'] },
          { name: 'clipboard', items: ['Cut', 'Copy', 'Paste', 'PasteText', 'PasteFromWord', '-', 'Undo', 'Redo'] },
          { name: 'insert', items: ['Image', 'HorizontalRule', 'Smiley', 'SpecialChar', 'Video', 'Audio', 'Table'] },
         '/',
          { name: 'basicstyles', items: ['Bold', 'Italic', 'Underline', 'Strike', 'Subscript', 'Superscript', '-', 'RemoveFormat'] },
          {
              name: 'paragraph', items: ['NumberedList', 'BulletedList', '-', 'Outdent', 'Indent', '-', 'Blockquote', 'CreateDiv',
             '-', 'JustifyLeft', 'JustifyCenter', 'JustifyRight', 'JustifyBlock', '-', 'BidiLtr', 'BidiRtl']
          },
          { name: 'links', items: ['Link', 'Unlink', 'Anchor'] },

         '/',
          { name: 'styles', items: ['Format', 'Font', 'FontSize'] },
          { name: 'colors', items: ['TextColor', 'BGColor'] },
          { name: 'tools', items: ['Maximize', 'ShowBlocks', '-', 'About'] }
    ];

	config.toolbar_Full =
    [
     { name: 'document', items: ['Source', '-', 'Save', 'NewPage', 'DocProps', 'Preview', 'Print', '-', 'Templates'] },
     { name: 'clipboard', items: ['Cut', 'Copy', 'Paste', 'PasteText', 'PasteFromWord', '-', 'Undo', 'Redo'] },
     { name: 'editing', items: ['Find', 'Replace', '-', 'SelectAll', '-', 'SpellChecker', 'Scayt'] },
     {
         name: 'forms', items: ['Form', 'Checkbox', 'Radio', 'TextField', 'Textarea', 'Select', 'Button', 'ImageButton',
          'HiddenField']
     },
     '/',
     { name: 'basicstyles', items: ['Bold', 'Italic', 'Underline', 'Strike', 'Subscript', 'Superscript', '-', 'RemoveFormat'] },
     {
         name: 'paragraph', items: ['NumberedList', 'BulletedList', '-', 'Outdent', 'Indent', '-', 'Blockquote', 'CreateDiv',
         '-', 'JustifyLeft', 'JustifyCenter', 'JustifyRight', 'JustifyBlock', '-', 'BidiLtr', 'BidiRtl']
     },
     { name: 'links', items: ['Link', 'Unlink', 'Anchor'] },
     { name: 'insert', items: ['Image', 'Flash', 'Table', 'HorizontalRule', 'Smiley', 'SpecialChar', 'PageBreak', 'Iframe'] },
     '/',
     { name: 'styles', items: ['Styles', 'Format', 'Font', 'FontSize'] },
     { name: 'colors', items: ['TextColor', 'BGColor'] },
     { name: 'tools', items: ['Maximize', 'ShowBlocks', '-', 'About'] }
    ];

	//config.extraPlugins = 'video';

	//CKEDITOR.plugins.add('Video', CKEDITOR.plugins.add('Video', '/plugins/video/'))
	//config.extraPlugins = 'video'
  


    // 使用Shift+ Enter組合鍵的行為。
    // CKEDITOR.ENTER_P   = <p>;
    // CKEDITOR.ENTER_BR  = <br/>
    // CKEDITOR.ENTER_DIV = <div>
    // Defaults to: CKEDITOR.ENTER_BR
	config.shiftEnterMode = CKEDITOR.ENTER_BR;

    // 使用ENTER鍵產生的標籤
    // CKEDITOR.ENTER_P   = <p>;
    // CKEDITOR.ENTER_BR  = <br/>
    // CKEDITOR.ENTER_DIV = <div>
    // Defaults to: CKEDITOR.ENTER_BR
	config.enterMode = CKEDITOR.ENTER_BR;

	CKEDITOR.config.fontSize_sizes = '8/8px;9/9px;10/10px;11/11px;12/12px;14/14px;16/16px;18/18px;20/20px;22/22px;24/24px;26/26px;28/28px;36/36px;48/48px;72/72px';

    // 字型選單 (名稱/字體)
	CKEDITOR.config.font_names = 'Arial/Arial, Helvetica, sans-serif;' +
    'Comic Sans MS/Comic Sans MS, cursive;' +
    'Courier New/Courier New, Courier, monospace;' +
    'Georgia/Georgia, serif;' +
    'Lucida Sans Unicode/Lucida Sans Unicode, Lucida Grande, sans-serif;' +
    'Tahoma/Tahoma, Geneva, sans-serif;' +
    'Times New Roman/Times New Roman, Times, serif;' +
    'Trebuchet MS/Trebuchet MS, Helvetica, sans-serif;' +
    'Verdana/Verdana, Geneva, sans-serif';

    // 設定表情符號icon
	config.smiley_descriptions = ['smiley', 'sad', 'wink', 'laugh', 'frown', 'cheeky', 'blush', 'surprise', 'indecision', 'angry', 'angel', 'cool', 'devil', 'crying', 'enlightened', 'no', 'yes', 'heart', 'broken heart', 'kiss', 'mail'];

    // 設定表情符號對應圖片
	config.smiley_images = ['regular_smile.gif', 'sad_smile.gif', 'wink_smile.gif', 'teeth_smile.gif', 'confused_smile.gif', 'tongue_smile.gif', 'embarrassed_smile.gif', 'omg_smile.gif', 'whatchutalkingabout_smile.gif', 'angry_smile.gif', 'angel_smile.gif', 'shades_smile.gif', 'devil_smile.gif', 'cry_smile.gif', 'lightbulb.gif', 'thumbs_down.gif', 'thumbs_up.gif', 'heart.gif', 'broken_heart.gif', 'kiss.gif', 'envelope.gif']

    // 表情符號路徑
	config.smiley_path = CKEDITOR.basePath + 'plugins/smiley/images/';

	config.fontSize_defaultLabel = '12px';
};


