﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class CERI05EditAccreditationtViewModel
    {
        /// <summary>
        ///護照細項ID
        /// </summary>
        [DisplayName("護照細項ID")]
        public string ACCREDITATION_ID { get; set; }

        public string ITEM_NO { get; set; }

        [DisplayName("護照名稱")]
        public string TYPE_NAME { get; set; }

        /// <summary>
        ///護照細項名稱
        /// </summary>
        [DisplayName("護照細項名稱")]
        public string ACCREDITATION_NAME { get; set; }

        [DisplayName("通過主旨")]
        public string SUBJECT { get; set; }

        [DisplayName("通過內容")]
        public string CONTENT { get; set; }

        /// <summary>
        /// 原來狀態
        /// </summary>
        [DisplayName("是否完成")]
        public bool? O_IS_PASS { get; set; }

        /// <summary>
        /// 這次
        /// </summary>
        public bool? IS_PASS { get; set; }
        public string states { get; set; }
        public string SCHOOL_NO { get; set; }
        public string PersonText { get; set; }
        public virtual CERI05EditAccreditationtViewModel item { get; set; }
    }
}