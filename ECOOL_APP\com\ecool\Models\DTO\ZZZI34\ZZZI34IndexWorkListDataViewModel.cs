﻿
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Text;

namespace ECOOL_APP.EF
{
    public class ZZZI34IndexWorkListDataViewModel
    {

        public int ROWID { get; set; }


        /// <summary>
        ///藝廊名稱
        /// </summary>
        [DisplayName("藝廊名稱")]
        public string ART_SUBJECT { get; set; }

        /// <summary>
        ///作品類別
        /// </summary>
        [DisplayName("作品類別")]
        public string WORK_TYPE { get; set; }

        public string STATUS { get; set; }
        

        /// <summary>
        ///推薦數
        /// </summary>
        [DisplayName("推薦")]
        public int  SHARE_COUNT { get; set; }

        /// <summary>
        ///照片 NO.
        /// </summary>
        [DisplayName("照片 NO.")]
        public string PHOTO_NO { get; set; }

        /// <summary>
        ///藝廊 NO.
        /// </summary>
        [DisplayName("藝廊 NO.")]
        public string ART_GALLERY_NO { get; set; }

        /// <summary>
        ///照片檔名
        /// </summary>
        [DisplayName("照片檔名")]
        public string PHOTO_FILE { get; set; }

        /// <summary>
        ///照片描述
        /// </summary>
        [DisplayName("主題")]
        public string PHOTO_SUBJECT { get; set; }

        /// <summary>
        ///
        /// </summary>
        [DisplayName("內容簡介")]
        public string PHOTO_DESC { get; set; }

        /// <summary>
        ///給予點數(照片作者)
        /// </summary>
        [DisplayName("給予點數(照片作者)")]
        public short PHOTO_CASH { get; set; }
        public bool? AutherYN { get; set; }
   
        /// <summary>
        ///照片作者學校代碼
        /// </summary>
        [DisplayName("照片作者學校代碼")]
        public string PHOTO_SCHOOL_NO { get; set; }

        /// <summary>
        ///照片作者帳號
        /// </summary>
        [DisplayName("照片作者帳號")]
        public string PHOTO_USER_NO { get; set; }

        /// <summary>
        ///照片作者姓名
        /// </summary>
        [DisplayName("照片作者姓名")]
        public string PHOTO_NAME { get; set; }

        /// <summary>
        ///照片作者簡稱
        /// </summary>
        [DisplayName("照片作者簡稱")]
        public string PHOTO_SNAME { get; set; }

        /// <summary>
        ///照片作者班級
        /// </summary>
        [DisplayName("照片作者班級")]
        public string PHOTO_CLASS_NO { get; set; }

        /// <summary>
        ///照片作者學年度
        /// </summary>
        [DisplayName("照片作者學年度")]
        public byte? PHOTO_SYEAR { get; set; }

        /// <summary>
        ///照片作者學期
        /// </summary>
        [DisplayName("照片作者學期")]
        public byte? PHOTO_SEMESTER { get; set; }

        /// <summary>
        ///建立者
        /// </summary>
        [DisplayName("建立者")]
        public string CRE_PERSON { get; set; }

        /// <summary>
        ///建立日期
        /// </summary>
        [DisplayName("建立日期")]
        public DateTime? CRE_DATE { get; set; }

        /// <summary>
        ///修改人
        /// </summary>
        [DisplayName("修改人")]
        public string CHG_PERSON { get; set; }

        /// <summary>
        ///修改日期
        /// </summary>
        [DisplayName("修改日期")]
        public DateTime? CHG_DATE { get; set; }

        /// <summary>
        ///照片狀態
        /// </summary>
        [DisplayName("照片狀態")]
        public string PHOTO_STATUS { get; set; }

        /// <summary>
        ///照片排序
        /// </summary>
        [DisplayName("照片排序")]
        public int? PHOTO_ORDER_BY { get; set; }

        /// <summary>
        ///使用者是否推薦過
        /// </summary>
        [DisplayName("是否推薦過")]
        public int? IsLikeCount { get; set; }


    }
}