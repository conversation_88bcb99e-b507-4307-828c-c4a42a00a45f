﻿@model ADDI10IndexViewModel
@{
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
}
<style>

    .wrap {
        display: inline-block;
        position: relative;
    }

    .overlap {
        display: none
    }

    .poptooltip .overlap {
        display: block;
        position: absolute;
        height: 100%;
        width: 100%;
        z-index: 1000;
    }

    .btn-Basic.disabled,
    .btn-Basic[disabled],
    fieldset[disabled] .btn-Basic,
    .btn-Basic.disabled:hover,
    .btn-Basic[disabled]:hover,
    fieldset[disabled] .btn-Basic:hover,
    .btn-Basic.disabled:focus,
    .btn-Basic[disabled]:focus,
    fieldset[disabled] .btn-Basic:focus,
    .btn-Basic.disabled:active,
    .btn-Basic[disabled]:active,
    fieldset[disabled] .btn-Basic:active,
    .btn-Basic.disabled.active,
    .btn-Basic[disabled].active,
    fieldset[disabled] .btn-Basic.active {
        background-color: rgba(0, 0, 0, 0);
    }
</style>
@Html.PermissionButton("新增投票活動", "button", "ZZZI33", "Index", new { @class = "btn btn-sm btn-sys", @onclick = "onAdd()" }, 2)

@Html.HiddenFor(m => m.Search.OrdercColumn)
@Html.HiddenFor(m => m.Search.SyntaxName)
@Html.HiddenFor(m => m.Search.Page)
@Html.HiddenFor(m => m.Search.whereADDI10Status)
@Html.HiddenFor(m => m.Search.WhereSOU_KEY)
@Html.HiddenFor(m => m.Search.WhereQUESTIONNAIRE_ID)
@Html.HiddenFor(m => m.Search.BackAction)
@Html.HiddenFor(m => m.Search.BackController)
@Html.HiddenFor(m => m.StudentCardVote)
<script src="https://cdnjs.cloudflare.com/ajax/libs/clipboard.js/1.7.1/clipboard.min.js"></script>
<link href="~/Content/colorbox/example4/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>
<script src="~/Scripts/printThis/printThis.js"></script>
<div class="form-inline" role="form" id="Q_Div">
    <div class="form-group">
        <label class="control-label">
            @Html.DisplayNameFor(model => model.Search.WhereSearch)
        </label>
    </div>
    <div class="form-group">
        @Html.EditorFor(m => m.Search.WhereSearch, new { htmlAttributes = new { @class = "form-control" } })
    </div>

    <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1)" />
    <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />
</div>
@{
    string btnActiveAll = AppMode ? "全部" : "全部(不含作廢)";

    string ActiveAll = (Model.Search.whereADDI10Status == string.Empty || Model.Search.whereADDI10Status == null) ? "active" : "";

    string ActiveIN = (Model.Search.whereADDI10Status == Convert.ToSByte(SAQT01.STATUSVal.NotStarted).ToString()) ? "active" : "";
    string ActiveDel = (Model.Search.whereADDI10Status == Convert.ToSByte(SAQT01.STATUSVal.NotShow).ToString() || Model.Search.whereADDI10Status == Convert.ToSByte(SAQT01.STATUSVal.Disabled).ToString()) ? "active" : "";
}
<button class="btn btn-xs btn-pink  @ActiveDel" onclick="DoADDI10_STATUS('@Convert.ToSByte(SAQT01.STATUSVal.NotShow),@Convert.ToSByte(SAQT01.STATUSVal.Disabled)')" type="button" style="float:right">已作廢</button>
@*<button class="btn btn-xs btn-pink  @ActiveUN" onclick="DoWRITING_STATUS('@Convert.ToSByte(ADDStatus.eADDT01Status.Draft),@Convert.ToSByte(ADDStatus.eADDT01Status.TurnVerify)')" type="button">未開始</button>*@
<button class="btn btn-xs btn-pink  @ActiveIN" onclick="DoADDI10_STATUS('@Convert.ToSByte(SAQT01.STATUSVal.StartedOut)')" type="button" style="float:right">正常(投票中)</button>

<button class="btn btn-xs btn-pink  @ActiveAll" type="button" style="float:right" onclick="DoADDI10_STATUS('')">@btnActiveAll</button>
<img src="~/Content/img/web-bar-vote.png" class="img-responsive" alt="Responsive image" />
<div class="modal fade bs-example-modal-lg" id="myShareUrlModal" tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel" style="z-index:9999">
    <div class="modal-dialog modal-sm" role="document" style="top:200px">
        <div class="modal-content">

            <div class="input-group">
                <span class="input-group-btn">
                    <button type="button" id="id_copy"
                            data-clipboard-target="#id_text"
                            data-clipboard-action="copy" onclick="OnCopy()">
                        點擊複製
                    </button>
                </span>
                <div id="id_text">@ViewBag.WinOpenShareUrlLink</div>
                <div id="success" style="display:none">已複製</div>
                <input id="copyStr" type="hidden" value="@ViewBag.WinOpenYoutubeUrlLink">
            </div><!-- /input-group -->
        </div>
    </div>
</div>
<div class="modal fade bs-example-modal-lg" id="myQrCodeModal" tabindex="-1" role="dialog" aria-labelledby="myQrCodeModalLabel" style="z-index:9999">
    <div class="modal-dialog modal-sm" role="document">
        <div class="modal-content">
            <div class="input-group">
                @if (ViewBag.WinOpenShareUrlLink != null)
                {
                    <img src="@Url.Action("Cre","Barcode", new {Value=ViewBag.WinOpenShareUrlLink })" style="max-width:150px" />
                }
            </div><!-- /input-group -->
        </div>
    </div>
</div>
<div class="table-responsive">
    <table class="table-ecool table-92Per table-hover table-ecool-reader">
        <thead>
            <tr>
                <th></th>
                <th style="width:32%;">
                    投票標題
                </th>
                <th>
                    酷幣<br />點數
                </th>
                <th>承辦<br />人員</th>
                <th>@Html.DisplayNameFor(m => m.ListData.First().QUESTIONNAIRE_SDATE)~<br />@Html.DisplayNameFor(m => m.ListData.First().QUESTIONNAIRE_EDATE)</th>
                <th>投票<br />人數</th>
            </tr>
        </thead>
        <tbody>
            @if (Model.ListData != null)
            {
                foreach (var item in Model.ListData)
                {
                    var show = "disabled=\"disabled\"";
                    var showtitle = "活動已經結束";
                    var showclass = "wrap poptooltip";
                    var showShare = "disabled=\"disabled\"";
                    var showSharetitle = "活動已經結束";
                    var showShareclass = "wrap poptooltip";
                    <tr>
                        <td nowrap="nowrap">
                            @if (item.QUESTIONNAIRE_SDATE > DateTime.Now && user.USER_KEY != item.CRE_PERSON)
                            {

                                showtitle = "活動還未開始";
                                showSharetitle = "活動還未開始";
                            }
                            @if (item.QUESTIONNAIRE_EDATE >= DateTime.Now && item.QUESTIONNAIRE_SDATE <= DateTime.Now && user != null)
                            {
                                showShare = "";
                                showSharetitle = "";
                                showShareclass = "wrap";
                                if (item.QUESTIONNAIRE_ANSWER_Count > 0 && item.ANSWERCount != 0)
                                {
                                    if (item.ANSWERCount != null && item.ANSWERCount != 0)
                                    {

                                        if (item.ANSWERCount >= item.QUESTIONNAIRE_ANSWER_Count)

                                        {

                                            showtitle = string.Format("此活動限制只能回答{0}次，您已超過此次數", item.QUESTIONNAIRE_ANSWER_Count);
                                        }
                                        else
                                        {


                                            show = "";
                                            showtitle = "";
                                            showclass = "wrap";
                                        }
                                    }

                                }
                                else if (user.USER_KEY == item.CRE_PERSON) {

                                    show = "";
                                    showtitle = "";
                                    showclass = "wrap";
                                }
                                else
                                {
                                    if (item.IsVote)
                                    {

                                        show = "";
                                        showtitle = "";
                                        showclass = "wrap";

                                    }
                                    else if (!item.IsVote)
                                    {
                                        showtitle = "已經投過票";
                                    }
                                }
                            }
                            @{
                                var show1 = "disabled=\"disabled\"";
                                var show1title = "活動已經結束";
                                var show1class = "wrap poptooltip";
                            }
                            @if (item.QUESTIONNAIRE_SDATE > DateTime.Now && user.USER_KEY != item.CRE_PERSON)
                            {

                                show1title = "活動還未開始";

                            }
                            @if (HRMT24_ENUM.CheckQAdmin(user) && AppMode == false)
                            {
                                if (item.QUESTIONNAIRE_EDATE >= DateTime.Now && item.QUESTIONNAIRE_SDATE <= DateTime.Now && user != null)
                                {
                                    show1 = "";
                                    show1title = "";
                                    show1class = "wrap";
                                }

                            }

                            @{
                                string str = ViewBag.WinOpenShareUrlLink + "?Mode=Edit&EditNum=" + item.QUESTIONNAIRE_ID+ "&SCHOOLNO="+item.CHG_PERSON.Substring(0,6);
                            }

                            @{
                                var IsResualt = "disabled=\"disabled\"";
                                var IsResualt1title = "活動已經結束";
                                var IsResualtclass = "wrap poptooltip";
                            }

                            @if ((item.RESULT ?? false))
                            {
                                if (!string.IsNullOrWhiteSpace(item.RESULT_PERSON))
                                {
                                    if (item.RESULT_PERSON == "A" && user.USER_KEY != item.CRE_PERSON) {

                                        IsResualt1title = "結果只有管理者可以看";
                                    }

                                    else if (item.RESULT_PERSON == "T" && user.USER_KEY != item.CRE_PERSON)
                                    {

                                        IsResualt1title = "結果只有管理者，老師可以看";
                                    }
                                    if (item.RESULT_PERSON == "A" && HRMT24_ENUM.CheckQAdmin(user) == true)
                                    {
                                        IsResualt = "";
                                        IsResualt1title = "";
                                        IsResualtclass = "wrap";

                                    }

                                    else if (user.USER_KEY == item.CRE_PERSON)
                                    {

                                        IsResualt = "";
                                        IsResualt1title = "";
                                        IsResualtclass = "wrap";
                                    }
                                    else if (item.RESULT_PERSON == "T" && (user.USER_TYPE == "T" || HRMT24_ENUM.CheckQAdmin(user) == true))
                                    {
                                        IsResualt = "";
                                        IsResualt1title = "";
                                        IsResualtclass = "wrap";

                                    }
                                    else if (item.RESULT_PERSON == "N")
                                    {
                                        if (user.USER_TYPE != "A" && user.USER_TYPE != "T")
                                        {
                                            if (item.QUESTIONNAIRE_EDATE < DateTime.Now && item.QUESTIONNAIRE_SDATE >= DateTime.Now && user != null)
                                            {
                                                show1 = "";
                                                show1title = "";
                                                show1class = "wrap";
                                                IsResualt1title = "";
                                            }
                                            else {

                                                IsResualt1title = "學生要活動結束才可以看結果";
                                            }
                                        }
                                        else {
                                            IsResualt = "";
                                            IsResualt1title = "";
                                            IsResualtclass = "wrap";

                                        }




                                    }
                                    else {
                                        if (item.RESULT_PERSON == "A")
                                        {

                                            IsResualt1title = "結果只有管理者可以看";
                                        }
                                        if (item.RESULT_PERSON == "T")
                                        {

                                            IsResualt1title = "結果只有管理者，老師可以看";
                                        }
                                        else {
                                            IsResualt1title = "投票尚未開放看結果";
                                        }


                                    }
                                }
                                else {

                                    if (item.QUESTIONNAIRE_EDATE <= DateTime.Now || ViewBag.USER_KEY == item.CRE_PERSON || HRMT24_ENUM.CheckQAdmin(user) == true)
                                    {
                                        IsResualt = "";
                                        IsResualt1title = "";
                                        IsResualtclass = "wrap";
                                    }
                                    else if (item.QUESTIONNAIRE_EDATE > DateTime.Now)
                                    {
                                        if (item.RESULT_PERSON == "A")
                                        {

                                            IsResualt1title = "結果只有管理者可以看";
                                        }
                                        if (item.RESULT_PERSON == "T")
                                        {

                                            IsResualt1title = "結果只有管理者，老師可以看";
                                        }
                                        else {
                                            IsResualt1title = "投票尚未開放看結果";
                                        }

                                    }
                                    else if (item.QUESTIONNAIRE_SDATE < DateTime.Now)
                                    {
                                        if (item.RESULT_PERSON == "A")
                                        {

                                            IsResualt1title = "結果只有管理者可以看";
                                        }
                                        if (item.RESULT_PERSON == "T")
                                        {

                                            IsResualt1title = "結果只有管理者，老師可以看";
                                        }
                                        else {
                                            IsResualt1title = "投票尚未開放看結果";
                                        }
                                    }
                                }
                            }

                            else
                            {
                                if (ViewBag.USER_KEY == item.CRE_PERSON || HRMT24_ENUM.CheckQAdmin(user) == true)
                                {
                                    IsResualt = "";
                                    IsResualt1title = "";
                                    IsResualtclass = "wrap";
                                }
                                else
                                {

                                    if (item.RESULT_PERSON == "A")
                                    {

                                        IsResualt1title = "結果只有管理者可以看";
                                    }
                                    if (item.RESULT_PERSON == "T")
                                    {

                                        IsResualt1title = "結果只有管理者，老師可以看";
                                    }
                                    else
                                    {
                                        IsResualt1title = "投票尚未開放看結果";
                                    }

                                }
                            }

                            <div rel="tooltip" title="@showtitle" class="@showclass">
                                <div class="overlap"></div>
                                <button class="btn btn-xs btn-Basic" type="button" @show onclick="Edit_show('@item.QUESTIONNAIRE_ID', false)">

                                    投票
                                </button>
                            </div>

                            @if (user.USER_TYPE == "T" || user.USER_TYPE == "A")
                            {
                                <div rel="tooltip" title="@show1title" class="@show1class">
                                    <div class="overlap"></div>
                                    <button class="btn btn-xs btn-Basic" type="button" @show1 onclick="Edit_show('@item.QUESTIONNAIRE_ID', true)">數位學生證投票</button>
                                </div>
                                <br />

                            }
                            <div rel="tooltip" title="@showSharetitle" class="@showShareclass">
                                <div class="overlap"></div>
                                <a class="btn btn-xs btn-Basic" role="button" @showShare onclick="onWinOpenShareUrlLink('@str')">
                                    分享網址
                                </a>
                            </div>
                            @if (user.USER_TYPE == "T" || user.USER_TYPE == "A")
                            {
                                <div rel="tooltip" title="@showSharetitle" class="@showShareclass">
                                    <div class="overlap"></div>
                                    <a class="btn btn-xs btn-Basic" role="button" @showShare onclick="onWinOpenShareQRCODE('@item.QUESTIONNAIRE_ID')">
                                        分享二維碼
                                    </a>
                                </div>
                                <div class="modal fade bs-example-modal-lg" id="@item.QUESTIONNAIRE_ID" tabindex="-1" role="dialog" aria-labelledby="myQrCodeModalLabel" style="z-index:9999">
                                    <div class="modal-dialog modal-sm" role="document">
                                        <div class="modal-content">
                                            <div class="input-group">
                                                @if (ViewBag.WinOpenShareUrlLink != null)
                                                {
                                                    <img src="@Url.Action("Cre", "Barcode", new { Value = str })" style="max-width:150px" />
                                                }
                                            </div><!-- /input-group -->
                                        </div>
                                    </div>
                                </div>
                            }

                                <div rel="tooltip" title="@IsResualt1title" class="@IsResualtclass">
                                    <div class="overlap"></div>
                                    @*<button type="button" class="btn btn-xs btn-Basic" @IsResualt onclick="@Url.Action("StatisticsVote",(string)ViewBag.BRE_NO,new {QUESTIONNAIRE_ID= item.QUESTIONNAIRE_ID })">結果</button>*@
                                    <a role="button" href='@Url.Action("StatisticsVote",(string)ViewBag.BRE_NO,new {QUESTIONNAIRE_ID= item.QUESTIONNAIRE_ID })' class="btn btn-xs btn-Basic">
                                        結果
                                    </a>
                                </div>
                            @if (Model.Search == null) {
                                Model.Search = new ADDI10SearchViewModel();
                                Model.Search.WhereQUESTIONNAIRE_ID= item.QUESTIONNAIRE_ID;
                            }
                            @**@

                        </td>

                        <td style="text-align: left;">@Html.DisplayFor(modelItem => item.QUESTIONNAIRE_NAME)</td>
                        <td>@Html.DisplayFor(modelItem => item.CASH)</td>
                        <td>@Html.DisplayFor(modelItem => item.CRE_PERSON_SNAME)</td>
                        <td>@Html.DisplayFor(modelItem => item.QUESTIONNAIRE_SDATE)~<br />@Html.DisplayFor(modelItem => item.QUESTIONNAIRE_EDATE)</td>
                        <td style="text-align: left;">
                            <a class="votepeopel" href="@Url.Action("StatisticsVotePeople", (string)ViewBag.BRE_NO,new {QUESTIONNAIRE_ID= item.QUESTIONNAIRE_ID })">@Html.DisplayFor(modelItem => item.VOTE_COUNT) </a>
                            <text>人</text>
                        </td>
                    </tr>
                }
            }
        </tbody>
    </table>
</div>

<div>
    @Html.Pager(Model.ListData.PageSize, Model.ListData.PageNumber, Model.ListData.TotalItemCount).Options(o => o
                            .DisplayTemplate(PageGlobal.DfDisplayTemplate)
                            .MaxNrOfPages(PageGlobal.DfMaxNrOfPages)
                            .SetPreviousPageText(PageGlobal.DfSetPreviousPageText)
                            .SetNextPageText(PageGlobal.DfSetNextPageText)
                            )
</div>
<script type="text/javascript">

    $(document).ready(function () {
        $(".votepeopel").colorbox({ opacity: 0.82, width: "70%", innerHeight: "500px", scrolling: true });
    });

    function OnCopy() {
        var clipboard = new Clipboard("#id_copy");
        clipboard.on("success", function (element) {//複製成功的回調
            console.info("複製成功，複製內容：    " + element.text);
            $('#success').show()
            $('#id_text').hide()

            setTimeout('HidemySmallModal()', 2000);
        });
        clipboard.on("error", function (element) {//複製失敗的回調
            console.info(element);
        });
    }

    function onWinOpenShareUrlLink(obj) {
        $(".modal-content").attr("style", "width:500px");
        $('#id_text').html(obj);
        $('#copyStr').html(obj);
        $('#success').hide()
        $('#id_text').show()

        if ($('#myShareUrlModal').is(':visible') == false) {
            $('#myShareUrlModal').modal('show');
        }
    }

    function onWinOpenShareQRCODE(obj) {

        if ($('#' + obj).is(':visible') == false) {
            $(".modal-content").attr("style","width:50px");
            $('#' + obj).modal('show');
        }

        @*$.ajax({
            type: 'GET',
            url: '@Url.Action("Cre", "Barcode")',
            data: obj ,

            dataType: 'html',
            success: function (data) {

                $("#myQrCodeModal div[class='input-group']").html(" <img src=\"" + data + "\" style=\"max-width:150px\" />");

            },

            error: function (xhr, ajaxOptions, thrownError) {
                alert(xhr.status);
                alert(thrownError);
            }
        });

        if ($('#myQrCodeModal').is(':visible') == false) {
            $('#myQrCodeModal').modal('show');
        }*@
    }
    function DoADDI10_STATUS(Value) {
        $('#Search_whereADDI10Status').val(Value)
        FunPageProc(1)
    }
</script>