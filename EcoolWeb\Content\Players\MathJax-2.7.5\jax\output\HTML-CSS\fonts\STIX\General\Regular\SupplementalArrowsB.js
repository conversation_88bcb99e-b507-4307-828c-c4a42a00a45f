/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/General/Regular/SupplementalArrowsB.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.STIXGeneral,{10496:[450,-57,926,56,871],10497:[450,-57,926,55,871],10498:[551,45,926,55,871],10499:[551,45,926,55,871],10500:[551,45,926,20,906],10501:[450,-57,926,55,871],10502:[551,45,926,55,871],10503:[551,45,926,55,871],10504:[662,156,511,59,452],10505:[662,156,511,59,452],10506:[662,156,926,71,854],10507:[662,156,926,72,855],10508:[449,-57,926,55,871],10509:[449,-57,926,55,871],10510:[449,-57,926,55,871],10511:[449,-57,926,55,871],10512:[449,-57,1412,55,1357],10513:[449,-57,926,55,873],10514:[662,156,511,59,452],10515:[662,156,511,59,452],10516:[450,-57,926,55,871],10517:[450,-57,926,55,871],10518:[449,-57,926,55,871],10519:[450,-57,926,55,871],10520:[450,-57,926,50,876],10521:[449,-57,926,55,871],10522:[449,-57,926,55,871],10523:[449,-57,926,55,871],10524:[449,-57,926,55,871],10525:[449,-57,926,55,871],10526:[449,-57,926,55,871],10527:[450,-57,926,55,871],10528:[450,-57,926,55,871],10529:[662,156,926,55,871],10530:[660,156,926,55,873],10531:[662,156,926,55,871],10532:[662,156,926,55,871],10533:[662,156,926,55,871],10534:[662,156,926,55,871],10535:[662,156,926,55,873],10536:[662,156,926,53,871],10537:[662,156,926,53,871],10538:[662,156,926,55,873],10539:[662,156,926,55,871],10540:[662,156,926,55,871],10541:[662,156,926,55,871],10542:[662,156,926,55,871],10543:[662,156,926,55,871],10544:[662,154,926,55,873],10545:[662,156,926,54,870],10546:[662,156,926,55,871],10547:[449,-57,926,55,871],10548:[562,0,926,141,797],10549:[562,0,926,141,797],10550:[493,163,784,87,649],10551:[493,163,784,135,697],10552:[657,153,511,70,415],10553:[657,153,511,96,441],10554:[423,-78,926,69,866],10555:[423,-78,926,60,857],10556:[423,-64,926,59,856],10557:[423,29,926,69,866],10558:[563,116,926,69,856],10559:[563,116,926,69,856],10560:[788,116,926,92,834],10561:[788,116,926,92,834],10562:[598,92,926,55,871],10563:[598,92,926,55,871],10564:[598,92,926,55,871],10565:[449,69,926,55,871],10566:[449,69,926,55,871],10567:[449,-57,926,55,871],10568:[449,-57,926,38,888],10569:[662,154,511,60,451],10570:[439,-67,926,38,888],10571:[439,-67,926,38,888],10572:[662,156,511,69,441],10573:[662,156,511,69,441],10574:[439,-220,926,38,888],10575:[662,156,511,222,441],10576:[286,-67,926,38,888],10577:[662,156,511,69,288],10578:[448,-58,926,55,871],10579:[448,-58,926,55,871],10580:[662,156,511,60,451],10581:[662,156,511,60,451],10582:[448,-58,926,55,871],10583:[448,-58,926,55,871],10584:[662,156,511,60,451],10585:[662,156,511,60,451],10586:[448,-58,926,55,871],10587:[448,-58,926,55,871],10588:[662,156,511,60,451],10589:[662,156,511,60,451],10590:[448,-58,926,55,871],10591:[448,-58,926,55,871],10592:[662,156,511,59,450],10593:[662,156,511,59,450],10594:[539,33,926,55,871],10595:[662,156,685,57,629],10596:[539,33,926,55,871],10597:[662,156,685,57,629],10598:[539,-120,926,55,871],10599:[386,33,926,55,871],10600:[539,-120,926,55,871],10601:[386,33,926,55,871],10602:[539,-120,926,55,871],10603:[386,33,926,55,871],10604:[539,-120,926,55,871],10605:[386,33,926,55,871],10606:[662,156,685,57,629],10607:[662,156,685,57,629],10608:[386,-120,926,55,871],10609:[565,-57,926,55,871],10610:[508,-57,926,55,871],10611:[449,2,926,55,871],10612:[449,2,926,55,871],10613:[449,141,926,55,871],10614:[607,283,685,64,621],10615:[532,26,926,45,871],10616:[608,282,685,64,621],10617:[627,262,685,64,621],10618:[532,26,926,45,871],10619:[627,262,685,63,620],10620:[511,5,926,135,791],10621:[511,5,926,135,791],10622:[581,75,685,84,600],10623:[581,75,685,84,600]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/Regular/SupplementalArrowsB.js");
