﻿@model EcoolWeb.Models.ADDT06ViewModel
@using ECOOL_APP.com.ecool.Models.entity;
@using System.Text.RegularExpressions;
@{
    ViewBag.Title = "閱讀認證-認證審核一覽表";
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    string Rule = "";
    if (user != null)
    {
        if (user.USER_TYPE == UserType.Teacher) { Rule = "T"; }
        if (user.USER_TYPE == UserType.Admin) { Rule = "A"; }
    }

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    string strPASS_DEL = "已批閱後修改/作廢";
    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
        strPASS_DEL = "批閱後修改/作廢";
    }
    else
    {
        <link href="~/Content/css/EzCss.css" rel="stylesheet" />
    }
}

@if (AppMode == false)
{
    @Html.Partial("_Title_Secondary")
}

@Html.Partial("_Notice")

@{
    Html.RenderAction("_PageMenu", new { NowAction = "ADDTALLList" });
}

@functions
{
    int bookNameMaxStr = 10;
    string AdjustmentBookName(string bookName)
    {
        if (bookName == null)
            return "";
        if (bookName.Length > bookNameMaxStr)
        {
            return bookName.Substring(0, bookNameMaxStr) + "...";
        }

        return bookName;
    }
}

@helper  buttonFun()
{

    string ActiveAll = (Model.whereAPPLY_STATUS == string.Empty || Model.whereAPPLY_STATUS == null) ? "active" : "";
    string ActiveUN = (Model.whereAPPLY_STATUS == ADDT06.APPLY_STATUS_Val.APPLY_TYPE_WithVerify) ? "active" : "";
    string ActiveIN = (Model.whereAPPLY_STATUS == ADDT06.APPLY_STATUS_Val.APPLY_TYPE_Pass) ? "active" : "";
    string ActiveDel = (Model.whereAPPLY_STATUS == ADDT06.APPLY_STATUS_Val.APPLY_TYPE_DEL) ? "active" : "";

    if (ViewBag.VisableVerify)
    {
        <br />
        <div class="form-inline">
            <div class="col-xs-6 text-left" onclick="PicClick();">
                <div class="btn btn-default" onclick="PicClick();">
                    @Html.CheckBoxFor(m => m.PictureMode, new { @onchange = "this.form.submit();" }) 圖片模式
                </div>
            </div>
            <div class="col-xs-6 text-right">
                <button type="button" class="btn btn-sm btn-sys" onclick="PrintBooK()">我要列印</button>
            </div>
        </div>
        <br />
        <div class="row">
            <div class="col-xs-12 text-right">
                <button class="btn btn-xs btn-pink @ActiveAll" type="button" onclick="DoWRITING_STATUS('')">全部(不含作廢)</button>
                <button class="btn btn-xs btn-pink @ActiveUN" onclick="DoWRITING_STATUS('@ADDT06.APPLY_STATUS_Val.APPLY_TYPE_WithVerify')" type="button">待批閱</button>
                <button class="btn btn-xs btn-pink @ActiveIN" onclick="DoWRITING_STATUS('@ADDT06.APPLY_STATUS_Val.APPLY_TYPE_Pass')" type="button">已批閱</button>
                @if (ViewBag.VisibleSearchDelList == "Y")
                {
                    <button class="btn btn-xs btn-pink @ActiveDel" onclick="DoWRITING_STATUS('@ADDT06.APPLY_STATUS_Val.APPLY_TYPE_DEL')" type="button">已作廢</button>
                }
            </div>
        </div>

    }
}

@helper  buttonFunD(ADDT06 item, ECOOL_APP.UserProfile user, string Rule, string strPASS_DEL)
{
    @Html.ActionLink("檢視", "ADDTALLListDetails"
                                   , new
                                   {
                                       APPLY_NO = item.APPLY_NO,
                                       whereKeyword = Model.whereKeyword,
                                       whereUserNo = Model.whereUserNo,
                                       whereBOOK_NAME = Model.whereBOOK_NAME,
                                       whereAPPLY_STATUS = Model.whereAPPLY_STATUS,
                                       OrdercColumn = Model.OrdercColumn,
                                       whereCLASS_NO = Model.whereCLASS_NO,
                                       whereGrade = Model.whereGrade,
                                       Page = Model.Page,
                                       PictureMode = Model.PictureMode
                                   }, new { @class = "btn btn-xs btn-Basic" })

    if (ViewBag.VisableVerify && (item.APPLY_STATUS == ADDT06.APPLY_STATUS_Val.APPLY_TYPE_WithVerify))
    {
        if (com.ecool.service.PermissionService.isMyClassNO(item.CLASS_NO, user, item.USER_NO) == true || ViewBag.isShow == "true")
        {
            <text>
                @Html.ActionLink("批閱", "ADDTList_CheckPendingDetail"
                                               , new
                                               {
                                                   Mode = "Edit",
                                                   Rule = Rule,
                                                   APPLY_NO = item.APPLY_NO,
                                                   BackAction = "ADDTALLList",
                                                   whereKeyword = Model.whereKeyword,
                                                   whereUserNo = Model.whereUserNo,
                                                   whereBOOK_NAME = Model.whereBOOK_NAME,
                                                   whereAPPLY_STATUS = Model.whereAPPLY_STATUS,
                                                   OrdercColumn = Model.OrdercColumn,
                                                   whereCLASS_NO = Model.whereCLASS_NO,
                                                   whereGrade = Model.whereGrade,
                                                   Page = Model.Page,
                                                   PictureMode = Model.PictureMode
                                               }, new { @class = "btn btn-xs btn-Basic" })
            </text>
        }
    }

    if (ViewBag.PASS_DEL_ALL_YN == "Y" && item.APPLY_STATUS == ADDT06.APPLY_STATUS_Val.APPLY_TYPE_Pass)
    {

        if (com.ecool.service.PermissionService.isMyClassNO(item.CLASS_NO, user, item.USER_NO) == true || ViewBag.isShow == "true")
        {
            @Html.ActionLink(strPASS_DEL, "PASS_DEL"
                                               , new
                                               {
                                                   APPLY_NO = item.APPLY_NO,
                                                   whereKeyword = Model.whereKeyword,
                                                   whereUserNo = Model.whereUserNo,
                                                   whereBOOK_NAME = Model.whereBOOK_NAME,
                                                   whereAPPLY_STATUS = Model.whereAPPLY_STATUS,
                                                   OrdercColumn = Model.OrdercColumn,
                                                   whereCLASS_NO = Model.whereCLASS_NO,
                                                   whereGrade = Model.whereGrade,
                                                   Page = Model.Page,
                                                   PictureMode = Model.PictureMode
                                               }, new { @class = "btn btn-xs btn-Basic" })
        }
    }

}

@using (Html.BeginForm("ADDTALLList", "ADDT", FormMethod.Post, new { id = "ADDTALLList", name = "form1" }))
{

    List<Image_File> ImageModel = (List<Image_File>)ViewBag.ImageUrl;

    <div class="form-inline">
        <button type="button" class="btn-default btn-sm" onclick="showSearch();" id="glyphicon-search">
            <span class="glyphicon glyphicon-search fa-1x" aria-hidden="true" id="search-img">顯示搜尋條件</span>
        </button>
        <div class="form-inline" role="form" id="search">
            <div class="form-group">
                <label class="control-label">書名/學號/姓名</label>
            </div>
            <div class="form-group">
                @Html.EditorFor(m => m.whereKeyword, new { htmlAttributes = new { @class = "form-control input-sm" } })
                @Html.HiddenFor(m => m.OrdercColumn)
                @Html.HiddenFor(m => m.whereUserNo)
                @Html.HiddenFor(m => m.whereAPPLY_STATUS)
                @Html.HiddenFor(m => m.whereBOOK_NAME)
                @Html.HiddenFor(m => m.Page)
                @Html.HiddenFor(m => m.StringDelApply)
            </div>
            <div class="form-group">
                <label class="control-label">年級</label>
            </div>
            <div class="form-group">
                @Html.DropDownListFor(m => m.whereGrade, (IEnumerable<SelectListItem>)ViewBag.GradeItem, new { @class = "form-control input-sm", onchange = "FunPageProc(1)" })
            </div>
            <div class="form-group">
                <label class="control-label">班級</label>
            </div>
            <div class="form-group">
                @Html.DropDownListFor(m => m.whereCLASS_NO, (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control input-sm", onchange = "FunPageProc(1)" })
            </div>
            <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1)" />
            <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />
        </div>
    </div>

    @buttonFun()
    <img src="~/Content/img/web-Bar-09.png" class="img-responsive App_hide" alt="Responsive image" />
    <div class="table-responsive">
        <div class="text-center">
            @if (Model.PictureMode)
            {
                int i = 0;
                <div class="row">
                    @foreach (var item in Model.ADDT06List)
                    {
                        <div class="col-md-3" style="height:310px">
                            <div>
                                @if (  /* 已篩選只看圖片的(無文字) */
                                            ImageModel[i].APPLY_NO == item.APPLY_NO
                                            && !string.IsNullOrEmpty(ImageModel[i].ImageUrl)
                                            )
                                {
                                    DateTime dateTime = new DateTime().AddSeconds(1);
                                    Regex rgx = new Regex(@"/Small/");
                                    string Oring_B = "";
                                    string NOOring_B = "";
                                    if (rgx.IsMatch(ImageModel[i].ImageUrl))
                                    {
                                        Oring_B = ImageModel[i].ImageUrl.Replace("/Small", "") + '?' + dateTime;
                                        NOOring_B = ImageModel[i].ImageUrl.Replace("/Small", "");
                                    }
                                    else
                                    {
                                        NOOring_B = ImageModel[i].ImageUrl;
                                        Oring_B = ImageModel[i].ImageUrl + '?' + dateTime;
                                    }

                                    <img src="@(ImageModel[i].ImageUrl+"?"+dateTime)" id="imgArticle" class="img-thumbnail img-responsive" style="margin:10px;height:150px;width:200px" href="@ImageModel[i].ImageUrl" onclick="ShowColorbox('@item.APPLY_NO', '')" />
                                    if (!string.IsNullOrWhiteSpace(Oring_B) && rgx.IsMatch(ImageModel[i].ImageUrl))

                                    {
                                        <button onclick="imagesRotateJS('@ImageModel[i].ImageUrl','90','@i'); "><i class="fa fa-repeat" aria-hidden="true"></i></button>

                                    }
                                    else

                                    {
                                        <button onclick="imagesRotateJS('@NOOring_B','90','@i');"><i class="fa fa-repeat" aria-hidden="true"></i></button>
                                    }

                                    <span>@Html.DisplayFor(modelItem => item.CRE_DATE, "ShortDateTime")</span><br />
                                    <span class="text-primary">姓名: @Html.DisplayFor(modelItem => item.NAME)</span> <br />
                                    <span class="text-success">書名: @AdjustmentBookName(item.BOOK_NAME)</span><br />
                                    <span>@buttonFunD(item, user, Rule, strPASS_DEL)</span>
                                }
                                else
                                {
                                    <div class="text-danger pull-left" style="margin:10px;height:150px;width:200px;"></div>
                                    <span>@Html.DisplayFor(modelItem => item.CRE_DATE, "ShortDateTime")</span><br />
                                    <span class="text-primary">姓名: @Html.DisplayFor(modelItem => item.NAME)</span> <br />
                                    <span class="text-success">書名: @AdjustmentBookName(item.BOOK_NAME)</span><br />
                                    <span>@buttonFunD(item, user, Rule, strPASS_DEL)</span>
                                }
                            </div>
                        </div>
                        i++;
                    }
                </div>
            }
            else
            {
                <table class="table-ecool table-92Per table-hover table-ecool-reader">
                    <thead>

                        @if (AppMode)
                        {
                            <tr>
                                <th style="text-align: center">
                                </th>
                                <th style="text-align: center;cursor:pointer;white-space:nowrap" onclick="doSort('CRE_DATE');">
                                    申請日期
                                    <img id="CRE_DATE" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                                </th>
                                <th style="text-align: center;cursor:pointer;white-space:nowrap" onclick="doSort('CLASS_NO');">
                                    班級
                                    <img id="CLASS_NO" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                                </th>
                                <th style="text-align: center;cursor:pointer;white-space:nowrap" onclick="doSort('SEAT_NO');">
                                    座號
                                    <img id="SEAT_NO" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                                </th>
                                <th style="text-align: center;cursor:pointer;white-space:nowrap" onclick="doSort('SNAME');">
                                    姓名
                                    <img id="SNAME" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                                </th>
                                <th style="text-align: center;white-space:nowrap">
                                    書名
                                </th>
                                <th style="text-align: center;white-space:nowrap">
                                    狀態
                                </th>
                            </tr>

                        }

                        @if (AppMode == false)
                        {
                            <tr>
                                @if (ViewBag.VisibleBATCH_DEL as string == SharedGlobal.Y)
                                {
                                    <th style="text-align: center;white-space:nowrap">
                                        <input id="CheckAll" type="checkbox" />
                                        批次作廢
                                    </th>
                                }

                                <th style="text-align: center;cursor:pointer;white-space:nowrap" onclick="doSort('CRE_DATE');">
                                    申請日期
                                    <img id="CRE_DATE" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                                </th>
                                <th style="text-align: center;cursor:pointer;white-space:nowrap" onclick="doSort('CLASS_NO');">
                                    班級
                                    <img id="CLASS_NO" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                                </th>
                                <th style="text-align: center;cursor:pointer;white-space:nowrap" onclick="doSort('SEAT_NO');">
                                    座號
                                    <img id="SEAT_NO" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                                </th>
                                <th style="text-align: center;cursor:pointer;white-space:nowrap" onclick="doSort('SNAME');">
                                    姓名
                                    <img id="SNAME" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                                </th>
                                <th style="text-align: center;white-space:nowrap">
                                    書名
                                </th>
                                <th style="text-align: center;white-space:nowrap">
                                    狀態
                                </th>
                                <th style="text-align: center">
                                </th>
                            </tr>
                        }
                    </thead>
                    <tbody>
                        @foreach (var item in Model.ADDT06List)
                        {

                            if (AppMode)
                            {
                                <tr>
                                    <td align="left">
                                        @buttonFunD(item, user, Rule, strPASS_DEL)
                                    </td>
                                    <td style="text-align:left;white-space:nowrap;">
                                        @Html.DisplayFor(modelItem => item.CRE_DATE, "ShortDateTime")
                                    </td>
                                    <td style="cursor:pointer;" onclick="doSearch('whereCLASS_NO','@item.CLASS_NO');">
                                        @Html.DisplayFor(modelItem => item.CLASS_NO)
                                    </td>
                                    <td>
                                        @Html.DisplayFor(modelItem => item.SEAT_NO)
                                    </td>
                                    <td style="text-align:center;cursor:pointer;white-space:nowrap" onclick="doSearch('whereUserNo','@item.USER_NO');">
                                        @Html.DisplayFor(modelItem => item.NAME)
                                    </td>
                                    <td align="left" style="cursor:pointer;white-space:normal;width:150px" onclick="doSearch('whereBOOK_NAME','@HttpUtility.JavaScriptStringEncode(item.BOOK_NAME)');">
                                        @Html.DisplayFor(modelItem => item.BOOK_NAME)

                                        @if (item.SHARE_YN == "Y" || item.SHARE_YN == "y")
                                        {
                                            <img src="~/Content/img/icons-like-05.png" />
                                        }
                                    </td>
                                    <td style="text-align: center;cursor:pointer;white-space:nowrap" onclick="doSearch('whereAPPLY_STATUS','@item.APPLY_STATUS');">
                                        @ECOOL_APP.EF.ADDStatus.GetADDT06StatusString(item.APPLY_STATUS)
                                    </td>
                                </tr>
                            }
                            if (AppMode == false)
                            {
                                <tr>
                                    @if (ViewBag.VisibleBATCH_DEL as string == SharedGlobal.Y)
                                    {
                                        var strchecked = "";

                                        if (!string.IsNullOrWhiteSpace(Model.StringDelApply))
                                        {
                                            var ArrDelApply = Model.StringDelApply.Split(',');

                                            if (ArrDelApply.Length > 0)
                                            {

                                                int index = Array.IndexOf(ArrDelApply, item.APPLY_NO.ToString());

                                                if (index != -1)
                                                {
                                                    strchecked = "checked";
                                                }
                                            }
                                        }

                                        <th style="text-align: center;white-space:nowrap">
                                            @if (item.APPLY_STATUS != ADDT06.APPLY_STATUS_Val.APPLY_TYPE_DEL)
                                            {
                                                <input class="BATCH_DEL_CSS" type="checkbox" @strchecked name="BATCH_DEL_INPUT" id="BATCH_DEL_INPUT_@item.APPLY_NO" value="@item.APPLY_NO" onclick="funBatchDel(this)" />
                                            }
                                        </th>
                                    }
                                    <td style="text-align:left;white-space:nowrap;">
                                        @Html.DisplayFor(modelItem => item.CRE_DATE, "ShortDateTime")
                                    </td>
                                    <td style="cursor:pointer;" onclick="doSearch('whereCLASS_NO','@item.CLASS_NO');">
                                        @Html.DisplayFor(modelItem => item.CLASS_NO)
                                    </td>
                                    <td>
                                        @Html.DisplayFor(modelItem => item.SEAT_NO)
                                    </td>
                                    <td style="text-align:center;cursor:pointer;white-space:nowrap" onclick="doSearch('whereUserNo','@item.USER_NO');">
                                        @Html.DisplayFor(modelItem => item.NAME)
                                    </td>
                                    <td align="left" style="cursor:pointer;white-space:normal;width:150px" onclick="doSearch('whereBOOK_NAME','@HttpUtility.JavaScriptStringEncode(item.BOOK_NAME)');">
                                        @Html.DisplayFor(modelItem => item.BOOK_NAME)

                                        @if (item.SHARE_YN == "Y" || item.SHARE_YN == "y")
                                        {
                                            <img src="~/Content/img/icons-like-05.png" />
                                        }
                                    </td>
                                    <td style="text-align: center;cursor:pointer;white-space:nowrap" onclick="doSearch('whereAPPLY_STATUS','@item.APPLY_STATUS');">
                                        @ECOOL_APP.EF.ADDStatus.GetADDT06StatusString(item.APPLY_STATUS)
                                    </td>
                                    <td align="left">
                                        @buttonFunD(item, user, Rule, strPASS_DEL)
                                    </td>
                                </tr>
                            }

                        }
                    </tbody>
                </table>
            }
        </div>
    </div>

    <div>
        @Html.Pager(Model.ADDT06List.PageSize, Model.ADDT06List.PageNumber, Model.ADDT06List.TotalItemCount).Options(o => o
          .DisplayTemplate("BootstrapPagination")
         .MaxNrOfPages(5)
         .SetPreviousPageText("上頁")
         .SetNextPageText("下頁")
     )
    </div>
    <br />
    <div class="row">
        <div class="text-center">
            <a href='@Url.Action("ADDTList", "ADDT")' role="button" class="btn btn-default">
                返回
            </a>
            @if (ViewBag.VisibleBATCH_DEL as string == SharedGlobal.Y && Model.PictureMode == false)
            {
                <button type="button" class="btn btn-default" onclick="btnBatchDelSubmit();">
                    批次作廢
                </button>
            }
        </div>
    </div>
}
@section scripts{
    <script type="text/javascript">
            var targetFormID = '#ADDTALLList';

             jQuery( document ).ready(function( $ ) {
                 if ("@Model.whereKeyword" == "推薦清單圖") {

                     $('#@Html.IdFor(m=>m.whereKeyword)').val("");
                     FunPageProc(1);
                 }
                    if ($('#AppMode').val()=="True") {
                        $('#search').hide()
                    }
                    else
                    {
                      $('#glyphicon-search').hide()
                 }

                   $("#CheckAll").click(function(){
                       if($("#CheckAll").prop("checked")){//如果全選按鈕有被選擇的話（被選擇是true）
                            $(".BATCH_DEL_CSS").each(function(){
                                $(this).prop("checked", true);//把所有的核取方框的property都變成勾選
                                funBatchDel(this)
                        })
                       }else{
                            $(".BATCH_DEL_CSS").each(function(){
                                $(this).prop("checked", false);//把所有的核方框的property都取消勾選
                                 funBatchDel(this)
                            })
                       }
                    })
             });

        function btnBatchDelSubmit() {

            if ($('#@Html.IdFor(m=>m.StringDelApply)').val() == '') {
                alert('未勾選要作廢資料')
                return false;
            }

            $(targetFormID).attr("action", "@Url.Action("BatchDeleteView", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();

        }

        function funBatchDel(This) {
             var StringDelApply = $('#@Html.IdFor(m=>m.StringDelApply)').val();

             var ArrDelApply = StringDelApply.split(",");

                if (This.checked) {
                    var index = ArrDelApply.indexOf(This.value);
                    if (index==-1) {
                          ArrDelApply.push(This.value)
                    }
                }
                else {
                    var index = ArrDelApply.indexOf(This.value);
                    if (index!=-1) {
                          ArrDelApply.splice(index, 1);
                    }
            }

            if (ArrDelApply.length>0) {
              $('#@Html.IdFor(m=>m.StringDelApply)').val(ArrDelApply.join(','))
            }
            else {
                $('#@Html.IdFor(m=>m.StringDelApply)').val('')
            }
         }
        function ShowColorbox(APPLY_NO,ListType)
        {

            $("input[name='PictureMode']").val("@Model.PictureMode");
            @*$("input[name='whereKeyword']").val("@Model.whereKeyword");
            $("input[name='whereGrade']").val("@Model.whereGrade");
            $("input[name='whereCLASS_NO']").val("@Model.whereCLASS_NO");*@
                $('#@Html.IdFor(m=>m.OrdercColumn)').val(APPLY_NO)
                console.log("ListType" + APPLY_NO);
                $('#@Html.IdFor(m=>m.whereKeyword)').val(ListType)
        $.ajax({
            type: 'POST',
            url: '@Url.Action("OneIndex", (string)ViewBag.BRE_NO)',
            data: $(targetFormID).serialize(),
            success: function (data) {
                $.colorbox({ html: data, width: "80%", height: "80%", opacity: 0.82 });
            }
        });
    }
            function showSearch()
            {
                if ($('#search').is(':hidden') ) {
                    $('#search').show(500)
                    $('#search-img').attr("class", "glyphicon glyphicon-minus fa-1x");
                    $('#search-img').text("隱藏搜尋條件")
                }
                else {
                     $('#search').hide(500)
                     $('#search-img').attr("class", "glyphicon glyphicon-search fa-1x");

                     $('#search-img').text("顯示搜尋條件")

                }
        }
        function PicClick()
        {
            if ("@Model.PictureMode"== "True")
            {
                $("input[name='PictureMode']").val("False");
               
                FunPageProc(1);

            }
            if ("@Model.PictureMode" == "False")
            {
                $("input[name='PictureMode']").val("True");
              
              
                FunPageProc(1);

            }
        }
        function imagesRotateJS(ImgURL_Val, RotateAngle_Val, IDX_Val) {
          $(this).attr('disabled', 'disabled').html("Loading...資料驗証中…請勿其他動作");
        if (!ImgURL_Val) {
            return;
        }
        $.ajax({
            url: "@Url.Action("imagesRotate", "Comm")",     // url位置
            type: 'post',                   // post/get
        data: {
            ImgURL: ImgURL_Val
            , RotateAngle: RotateAngle_Val
        },     // data
        async: false,
        dataType: 'json',               // xml/json/script/html
        cache: false,                   // 是否允許快取
        success: function (data) {

            $('#' + IDX_Val).attr("src", "");
           $('#'+IDX_Val).attr("src",data+'?'+new Date());

        },
        error: function (xhr, err) {
            alert("readyState: " + xhr.readyState + "\nstatus: " + xhr.status);
            alert("responseText: " + xhr.responseText);
        }
    });
  }
            function FunPageProc(page) {
                if ($(targetFormID).size() > 0) {
                    $('#Page').val(page)
                    $(targetFormID).attr('action','@Html.Raw(@Url.Action("ADDTALLList", "ADDT"))')
                    $(targetFormID).submit();
                }
            };

            function doSort(SortCol) {
                $("#OrdercColumn").val(SortCol);
                FunPageProc(1)
            }

            function doSearch(ColName, whereValue) {
                $("#" + ColName).val(whereValue);
                FunPageProc(1)
            }

            function DoWRITING_STATUS(Value) {
                $('#whereAPPLY_STATUS').val(Value)
                FunPageProc(1)
            }

             function PrintBooK()
          {
              $(targetFormID).attr('action','@Html.Raw(@Url.Action("PrintQuery", "ADDT"))')
                $(targetFormID).attr('target', '_blank').submit().removeAttr('target');
            }

            function todoClear() {
                ////重設

                $(targetFormID).find(":input,:selected").each(function (i) {

                    var type = $(this).attr('type')
                    var InPreadonly = $(this).attr('readonly')
                    var tag = this.tagName.toLowerCase(); // normalize case

                    if (InPreadonly == false || InPreadonly == undefined) {

                        if (type == 'radio' || type == 'checkbox') {
                            if ($(this).attr("title") == 'Default') {
                                this.checked = true;
                            }
                            else {
                                this.checked = false;
                            }
                        }
                        else if (tag == 'select') { //下拉式選單
                            this.selectedIndex = 0;
                        }
                        else if (type == 'text' || type == 'hidden' || type == 'password' || type == 'textarea') {
                            this.value = '';
                        }
                    }
                });

                $(targetFormID).submit();
            }
    </script>
}