/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/General/Regular/LatinExtendedB.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.STIXGeneral,{384:[683,10,500,-19,472],392:[559,10,500,25,511],400:[684,6,580,33,562],402:[706,159,434,6,426],405:[683,10,735,9,710],409:[683,0,500,7,505],410:[683,0,278,19,257],411:[668,0,520,55,516],414:[460,233,500,16,485],416:[754,14,722,34,688],417:[474,10,545,29,531],421:[669,217,500,5,470],426:[684,233,432,20,412],427:[579,218,290,13,279],429:[683,10,310,14,333],431:[774,14,766,14,810],432:[561,10,500,9,539],437:[662,0,612,10,598],442:[450,234,381,4,360],443:[676,0,500,22,482],446:[539,12,500,73,427],448:[736,0,160,54,105],449:[736,0,280,54,225],450:[736,0,435,34,400],451:[676,9,333,130,236],496:[674,218,278,-70,294],506:[938,0,722,15,707],507:[890,10,444,37,442],508:[890,0,889,0,863],509:[678,7,667,38,632],510:[890,80,722,34,688],511:[678,112,500,29,470],545:[683,150,671,27,652],564:[683,150,429,19,410],565:[460,150,672,16,653],566:[580,150,401,13,382]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/Regular/LatinExtendedB.js");
