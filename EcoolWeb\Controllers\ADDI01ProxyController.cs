﻿using com.ecool.service;
using ECOOL_APP;
using ECOOL_APP.com.ecool.LogicCenter;
using ECOOL_APP.com.ecool.LogicCenter.Interfaces;
using ECOOL_APP.EF;
using EcoolWeb.CustomAttribute;
using EcoolWeb.Models;
using EcoolWeb.ViewModels;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Web;
using System.Web.Mvc;

namespace EcoolWeb.Controllers
{
    /// <summary>
    ///  代申請線上投稿
    /// ( 類似ZZZI26 代申請閱讀認證)
    /// </summary>
    [SessionExpire]
#if !DEBUG
    [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
#endif
    public class ADDI01ProxyController : Controller, IProxyMode<ADDI01ProxyEditViewModel>
    {
        /// <summary>
        /// 此Controller 對應的程式代碼
        /// </summary>
        private string Bre_NO = "ADDI01Proxy";

        private string ActionLeftTitle = "代申請線上投稿";

        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();

        /// <summary>
        /// 是否批閱權限，有可上傳檔案
        /// </summary>
        private string VerifyUseYN = string.Empty;

        /// <summary>
        /// 會員相關資訊
        /// </summary>
        private UserProfile user;

        private string DefaultSCHOOL_NO = string.Empty;
        private string USER_KEY = string.Empty;
        private string USER_NO = string.Empty;

        /// <summary>
        /// 錯誤訊息
        /// </summary>
        public string ErrorMsg { get; set; }

        public ADDI01ProxyController()
        {
        }

        public ActionResult Index()
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = ActionLeftTitle + " - 選擇模式";
            this.Shared();
            ViewBag.tModeList = GetModeList();
            return View();
        }

        /// <summary>
        /// 多學生多文章
        /// </summary>
        /// <param name="mode"></param>
        /// <returns></returns>
        // GET: ZZZI26
        public ActionResult ManyStudent(Mode mode)
        {
            ViewBag.Panel_Title = ActionLeftTitle + " - " + LogicCenter.GetEnumDescription(mode) + ".新增(步驟1)";
            this.Shared();

            ADDI01ProxyEditViewModel Data = new ADDI01ProxyEditViewModel();

            if (Data.Search == null) Data.Search = new ADDI01ProxySearchViewModel();
            Data.Search.ModeVal = (byte)mode;
            Data.Search.SCHOOL_NO = DefaultSCHOOL_NO;

            if (user != null)
            {
                HRMT03 HRMT03 = db.HRMT03.Where(A => A.SCHOOL_NO == DefaultSCHOOL_NO && A.TEACHER_NO == USER_NO).FirstOrDefault();
                if (HRMT03 != null)
                {
                    Data.Search.CLASS_NO = HRMT03.CLASS_NO;
                }
            }

            ViewBag.ClassItems = HRMT01.GetClassListData(DefaultSCHOOL_NO, ref db);

            List<SelectListItem> NumTypeItems = new List<SelectListItem>();

            NumTypeItems.Add(new SelectListItem() { Text = "全班", Value = "ALL" });
            for (int i = 1; i <= 50; i++)
            {
                NumTypeItems.Add(new SelectListItem() { Text = i.ToString(), Value = i.ToString() });
            }
            ViewBag.NumTypeItems = NumTypeItems;

            return View(Data);
        }

        /// <summary>
        /// 單一學生多文章
        /// </summary>
        /// <param name="mode"></param>
        /// <returns></returns>
        public ActionResult ManyArticle(Mode mode)
        {
            ViewBag.Panel_Title = ActionLeftTitle + " - " + LogicCenter.GetEnumDescription(mode) + ".新增(步驟1)";
            this.Shared();

            ADDI01ProxyEditViewModel Data = new ADDI01ProxyEditViewModel();

            if (Data.Search == null) Data.Search = new ADDI01ProxySearchViewModel();
            Data.Search.ModeVal = (byte)mode;
            Data.Search.SCHOOL_NO = DefaultSCHOOL_NO;
            Data.Search.NumArticle = 1;
            List<SelectListItem> ClassItem = new List<SelectListItem>();
            ClassItem.Add(new SelectListItem() { Text = "請選擇..", Value = "" });
            ClassItem.AddRange(HRMT01.GetClassListData(DefaultSCHOOL_NO, ref db).ToList());
            ViewBag.ClassItems = ClassItem;
            ViewBag.USER_NOItems = HRMT01.GetUserNoListData(DefaultSCHOOL_NO, "", ref db);

            if (user != null)
            {
                HRMT03 HRMT03 = db.HRMT03.Where(A => A.SCHOOL_NO == DefaultSCHOOL_NO && A.TEACHER_NO == USER_NO).FirstOrDefault();
                if (HRMT03 != null)
                {
                    Data.Search.CLASS_NO = HRMT03.CLASS_NO;
                }
            }

            return View(Data);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit(ADDI01ProxyEditViewModel Data, string DATA_TYPE, string ADDNUM)
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = ActionLeftTitle + " - " + LogicCenter.GetEnumDescription((Mode)Data.Search.ModeVal) + ".新增(步驟2)";
            this.Shared();

            if (Data.Details_List == null) Data.Details_List = new List<ADDI01ProxyDetailsViewModel>();

            if (string.IsNullOrEmpty(ADDNUM) == false) Data.ADDNUM = Convert.ToInt32(ADDNUM);

            if (string.IsNullOrEmpty(DATA_TYPE) == false)
            {
                if (DATA_TYPE == DATA_TYPE_STRUCT.DATA_TYPE_A) //新增明細不驗正
                {
                    ModelState.Clear(); //清除驗正

                    if (Data.ADDNUM == null)
                    {
                        ModelState.AddModelError("additem", "【增加明細】請輸入筆數");
                    }
                }
                else if (DATA_TYPE == DATA_TYPE_STRUCT.DATA_TYPE_S) // 存檔 / 批閱
                {
                    if (VerifyUseYN == "Y")
                    {
                        int num = 0;
                        foreach (var data in Data.Details_List) // 圖片名稱
                        {
                            if (data.files != null && data.files.ContentLength > 0)
                            {
                                Data.Details_List[num].IMG_FILE = Path.GetFileName(data.files.FileName);
                            }
                            num++;
                        }
                    }

                    var OK = this.Save(Data);
                    if (OK)
                    {
                        if (string.IsNullOrEmpty(ErrorMsg) == false)
                        {
                            TempData["StatusMessage"] = ActionLeftTitle + "部分失敗。";
                        }
                        else
                        {
                            TempData["StatusMessage"] = ActionLeftTitle + "全部成功";
                        }
                        return View("ShowList", Data);
                    }
                    else
                    {
                        TempData["StatusMessage"] = "錯誤!!請修正。<br/>" + ErrorMsg;
                    }
                }
            }
            else //新增預帶值
            {
                if (Data.Search.ModeVal == (byte)Mode.ManyStudent)
                {
                    this.AddItem(Data, Data.Search.NumType);
                }
                else if (Data.Search.ModeVal == (byte)Mode.ManyArticle)
                {
                    this.AddItem(Data, Data.Search.NumArticle.ToString());
                }
            }

            //計算明細筆數
            int Count = 0;
            int Total = 0;
            if (Data.Details_List != null)
            {
                Count = Data.Details_List.Count();
            }

            Total = (Data.ADDNUM == null) ? Count : (int)Data.ADDNUM + Count;
            TempData["TOLTAL"] = Total;
            if (Data.ADDNUM != null)
            {
                this.AddItem(Data, Data.ADDNUM.ToString());
                Data.ADDNUM = null;
            }

            //姓名 下拉
            IEnumerable<SelectListItem> USER_NOItems = null;

            if (Data.Search.ModeVal == (byte)Mode.ManyStudent)
            {
                USER_NOItems = HRMT01.GetUserNoListData(Data.Search.SCHOOL_NO, Data.Search.CLASS_NO, ref db);
            }
            else if (Data.Search.ModeVal == (byte)Mode.ManyArticle)
            {
                USER_NOItems = HRMT01.GetUserNoListData(Data.Search.SCHOOL_NO, Data.Search.CLASS_NO, Data.Search.USER_NO, ref db);
            }

            ViewBag.USER_NOItems = USER_NOItems;

            Data.Details_List.RemoveAll(a => a.Del == true);
            return View(Data);
        }

        public ActionResult ShowList(ADDI01ProxyEditViewModel Data)
        {
            if (Data == null) Data = new ADDI01ProxyEditViewModel();
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = ActionLeftTitle + " -" + LogicCenter.GetEnumDescription((Mode)Data.Search.ModeVal) + ".處理清單";
            this.Shared();
            return View(Data);
        }

        /// <summary>
        /// 增加明細
        /// </summary>
        /// <param name="Data"></param>
        /// <param name="Num"></param>
        public void AddItem(ADDI01ProxyEditViewModel Data, string Num)
        {
            ADDI01ProxyDetailsViewModel detail_vm;
            if (Num == "ALL")
            {
                var HRMT01List = db.HRMT01.Where(a => a.SCHOOL_NO == Data.Search.SCHOOL_NO && a.CLASS_NO == Data.Search.CLASS_NO && a.USER_TYPE == UserType.Student
                && (!UserStaus.NGKeyinUserStausList.Contains(a.USER_STATUS))).ToList().OrderBy(o => o.SEAT_NO);

                if (HRMT01List != null)
                {
                    foreach (var item in HRMT01List)
                    {
                        detail_vm = new ADDI01ProxyDetailsViewModel();
                        detail_vm.USER_NO = item.USER_NO;
                        Data.Details_List.Add(detail_vm);
                    }
                }
            }
            else
            {
                for (int i = 1; i <= Convert.ToInt16(Num); i++)
                {
                    detail_vm = new ADDI01ProxyDetailsViewModel();

                    if (Data.Search.ModeVal == (byte)Mode.ManyArticle)
                    {
                        detail_vm.USER_NO = Data.Search.USER_NO;
                    }

                    Data.Details_List.Add(detail_vm);
                }
            }
        }

        /// <summary>
        /// 資料處理
        /// </summary>
        /// <param name="Data"></param>
        /// <returns></returns>
        public bool Save(ADDI01ProxyEditViewModel Data)
        {
            bool ReturnBool = false;
            List<Tuple<string, string, int>> valuesList = new List<Tuple<string, string, int>>();
            if (!ModelState.IsValid) return false;

            try
            {
                //移除DEL有勾選的資料
                Data.Details_List.RemoveAll(a => a.Del == true);

                int CountOK = 0;

                int SYear;
                int Semesters;
                SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);
                List<HRMT01> H01ListData = db.HRMT01.Where(p => p.SCHOOL_NO == Data.Search.SCHOOL_NO && (!UserStaus.NGKeyinUserStausList.Contains(p.USER_STATUS))).ToList();

                bool errorOccur;
                DateTime timeNow = DateTime.Now;
   
                foreach (var item in Data.Details_List)
                {
                    errorOccur = false;

                    //取得學生資料
                    HRMT01 H01 = H01ListData.Where(p => p.USER_NO == item.USER_NO && p.SCHOOL_NO == Data.Search.SCHOOL_NO).FirstOrDefault();

                    if (H01 == null)
                    {
                        errorOccur = true;
                        item.MEMO = "此學生帳號已失效";
                        item.OK_YN = "N";
                        ErrorMsg = ErrorMsg + "學號:" + item.USER_NO + "-帳號已失效。<br/>";
                    }

                    // 匯入ADDT01資料表
                    if (H01 != null)
                    {
                        item.SCHOOL_NO = H01.SCHOOL_NO;
                        item.USER_NO = H01.USER_NO;
                        item.CLASS_NO = H01.CLASS_NO;
                        item.SYEAR = (byte)SYear;
                        item.SEMESTER = (byte)Semesters;
                        item.SEAT_NO = H01.SEAT_NO;
                        item.NAME = H01.NAME;
                        item.SNAME = H01.SNAME;

                        //找老師
                        string verifier =
                            db.HRMT03
                            .Where(a => a.SCHOOL_NO == item.SCHOOL_NO && a.CLASS_NO == item.CLASS_NO)
                            .FirstOrDefault()?
                            .TEACHER_NO;

                        // 圖片名稱先快速處理
                        var aDDT01 = new ADDT01()
                        {
                            SCHOOL_NO = item.SCHOOL_NO,
                            USER_NO = item.USER_NO,
                            CLASS_NO = item.CLASS_NO,
                            SYEAR = item.SYEAR,
                            SEMESTER = item.SEMESTER,
                            SEAT_NO = item.SEAT_NO,
                            NAME = item.NAME,
                            SNAME = item.SNAME,
                            SUBJECT = item.SUBECT,
                            ARTICLE = item.ARTICLE,
                            ARTICLE_VERIFY = item.ARTICLE,
                             AutherYN = false,
                        IMG_FILE = item.IMG_FILE,
                            VERIFIER = verifier,
                            CASH = item.GIVE_POINT,
                            READ_COUNT = 0,
                            WRITING_STATUS = (byte)ADDStatus.eADDT01Status.Verified,
                            CRE_DATE = timeNow,
                            VERIFIED_DATE = timeNow
                        };
                        db.ADDT01.Add(aDDT01);
                        
                        try
                        {
                            db.SaveChanges();
                            //線上投稿批閱通過給予的點數
                            int iSetCoolCash = 1;
                            CashHelper.TeachAddCash(user, iSetCoolCash, user.SCHOOL_NO, user.USER_NO, "ADDI01", aDDT01.WRITING_NO.ToString(), "線上投稿批閱通過", true, null, ref db);
                            // 給點數
                            CashHelper.AddCash(user, Convert.ToInt32(item.GIVE_POINT), item.SCHOOL_NO, item.USER_NO, "ADDI01", aDDT01.WRITING_NO.ToString(), "線上投稿批閱通過", true, ref db, "", "",ref valuesList);

                            //處理上傳檔案
                            bool ans = (item.files == null) ? true : doImage(aDDT01, item.files);
                            if (ans)
                                db.SaveChanges();
                            else
                                throw new Exception("儲存圖檔發生錯誤");
                        }
                        catch (Exception ex)
                        {
                            errorOccur = true;
                            item.MEMO = "儲存資料-發生錯誤。錯誤原因如下:" + ex.Message;
                            item.OK_YN = "N";
                            ErrorMsg = ErrorMsg + "班級:" + H01.CLASS_NO + "、姓名:" + H01.NAME + "、座號:" + H01.SEAT_NO + " - 「" + item.SUBECT + "」發生錯誤(A.儲存資料-發生錯誤)。錯誤原因如下:<br/>" + ex.Message + "<br/><br/>";
                        }
                    }
                    if (!errorOccur) CountOK++;
                }

                if (CountOK > 0) ReturnBool = true;
            }
            catch (Exception ex)
            {
                ErrorMsg = ex.Message;
            }
            UserProfile.RefreshCashInfo(user, ref db);
            UserProfileHelper.Set(user);
            return ReturnBool;
        }

        /// <summary>
        /// ADDT01文章存檔處理圖片 => 批次這裡的無多個檔案
        /// </summary>
        /// <param name="aDDT01"></param>
        /// <param name="Imgfile"></param>
        /// <returns></returns>
        private bool doImage(ADDT01 aDDT01, HttpPostedFileBase file)
        {
            if (file == null) return false;

            //組上傳資料夾路徑
            string UploadImageRoot =
                System.Web.Configuration.WebConfigurationManager.AppSettings["UploadImageRoot"];
            if (UploadImageRoot == null) UploadImageRoot = @"~\Content\";
            if (UploadImageRoot.EndsWith(@"\") == false) UploadImageRoot += @"\";
            string imgPath = string.Format(@"{0}ADDI01IMG\{1}\", Request.MapPath(UploadImageRoot), aDDT01.SCHOOL_NO.ToString());
            if (Directory.Exists(imgPath) == false) Directory.CreateDirectory(imgPath);

            if (file != null && file.ContentLength > 0)
            {
                string FileName = aDDT01.WRITING_NO.ToString() + "_" + Path.GetFileName(file.FileName);

                //更新圖名
                aDDT01.IMG_FILE = FileName;

                Regex regexCode = new Regex(@".*\.(jpg|jpeg|png|gif|bmp)");
                if (regexCode.IsMatch(FileName.ToLower()) == false)
                {
                    return false;
                }

                //縮圖
                System.Drawing.Image image = System.Drawing.Image.FromStream(file.InputStream);
                double FixWidth = 1000;
                double FixHeight = 1000;
                double rate = 1;
                if (image.Width > FixWidth || image.Height > FixHeight)
                {
                    if (image.Width > FixWidth) rate = FixWidth / image.Width;
                    else if (image.Height * rate > FixHeight) rate = FixHeight / image.Height;

                    int w = Convert.ToInt32(image.Width * rate);
                    int h = Convert.ToInt32(image.Height * rate);
                    Bitmap imageOutput = new Bitmap(image, w, h);
                    imageOutput.Save(Path.Combine(imgPath, FileName), image.RawFormat);
                    imageOutput.Dispose();
                }
                else
                {
                    //直接儲存
                    file.SaveAs(Path.Combine(imgPath, FileName));
                }
                image.Dispose();
            }

            return true;
        }

        /// <summary>
        /// UserNo下拉選單
        /// </summary>
        /// <param name="tagId"></param>
        /// <param name="tagName"></param>
        /// <param name="CLASS_NO"></param>
        /// <returns></returns>
        public ActionResult _GetUSER_NODDLHtml(string tagId, string tagName, string CLASS_NO)
        {
            ViewBag.BRE_NO = Bre_NO;
            this.Shared();

            var UserNoListData = HRMT01.GetUserNoListData(DefaultSCHOOL_NO, CLASS_NO, ref db);
            string _html = Util.DropDownListHelper.GetDropdownList(tagId, tagName, UserNoListData, new { @class = "form-control" }, "", true, null);

            return Content(_html);
        }

        #region View Mode 模組

        /// <summary>
        /// 模式類型清單
        /// </summary>
        /// <returns></returns>
        public List<SelectListItem> GetModeList(byte? SelectedValue = null)
        {
            List<SelectListItem> ItemS = new List<SelectListItem>();

            foreach (var it in Enum.GetValues(typeof(Mode)))
            {
                SelectListItem Item = new SelectListItem();
                Item.Text = LogicCenter.GetEnumDescription((Mode)it);
                Item.Value = it.ToString();

                if (SelectedValue != null)
                {
                    Item.Selected = (byte)it == SelectedValue ? true : false;
                }

                ItemS.Add(Item);
            }

            return ItemS;
        }

        /// <summary>
        /// 模式 單一學生多文章
        /// </summary>
        public enum Mode : byte
        {
            [Description("多學生多文章")]
            ManyStudent,

            [Description("單一學生多文章")]
            ManyArticle = 1,
        }

        public struct DATA_TYPE_STRUCT
        {
            /// <summary>
            /// 存檔(新增)
            /// </summary>
            public static string DATA_TYPE_S = "Save";

            /// <summary>
            /// (新增明細)
            /// </summary>
            public static string DATA_TYPE_A = "AddItem";
        }

        #endregion View Mode 模組

        #region Shared

        private void Shared()
        {
            ViewBag.BRE_NO = Bre_NO;

            user = UserProfileHelper.Get();
            DefaultSCHOOL_NO = UserProfileHelper.GetSchoolNo();

            if (user != null)
            {
                if (user.SCHOOL_NO != null)
                {
                    DefaultSCHOOL_NO = user.SCHOOL_NO;
                    USER_KEY = user.USER_KEY;
                    USER_NO = user.USER_NO;
                }
            }
#if !DEBUG
            VerifyUseYN = PermissionService.GetPermission_Use_YN(Bre_NO, "Verify", DefaultSCHOOL_NO, USER_NO);
            ViewBag.VerifyUseYN = VerifyUseYN;
#else
            // Debug Test
            VerifyUseYN = "Y";
            ViewBag.VerifyUseYN = VerifyUseYN;
            ViewBag.BtnPermission = new List<ControllerPermissionfile>() {
                new ControllerPermissionfile(){ ActionName="Index", BreNoName = Bre_NO, BoolUse = true },
                new ControllerPermissionfile(){ ActionName="ManyArticle", BreNoName = Bre_NO, BoolUse = true },
                new ControllerPermissionfile(){ ActionName="Edit", BreNoName = Bre_NO, BoolUse = true }
            };
#endif
        }

        #endregion Shared
    }
}