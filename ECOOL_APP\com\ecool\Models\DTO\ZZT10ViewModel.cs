﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.com.ecool.Models.DTO
{
    public class ZZT10ViewModel
    {
        [Display(Name = "ROLE_ID")]
        public string ROLE_ID { get; set; }

        [Display(Name = "BRE_NO")]
        public string BRE_NO { get; set; }

        [Display(Name = "BRE_NAME")]
        public string BRE_NAME { get; set; }

        [Display(Name = "BRE_TYPE")]
        public string BRE_TYPE { get; set; }

        [Display(Name = "LEVEL_NO")]
        public string LEVEL_NO { get; set; }

        //public Dictionary<string, ZZT10_D_ViewModel> Details { get; set; }

        public List<ZZT10_D_ViewModel> Details_List { get; set; }
    }



    public class ZZT10_D_ViewModel
    {

        [Display(Name = "ROLE_ID")]
        public string ROLE_ID { get; set; }
        
        [Display(Name = "BRE_NO")]
        public string BRE_NO { get; set; }

        [Display(Name = "ACTION_ID")]
        public string ACTION_ID { get; set; }

        [Display(Name = "ACTION_NAME")]
        public string ACTION_NAME { get; set; }

        [Display(Name = "ACTION_TYPE")]
        public string ACTION_TYPE { get; set; }

        public bool Checked { get; set; }

        public bool MY_Permission_Checked { get; set; }
        

    }


   







}
