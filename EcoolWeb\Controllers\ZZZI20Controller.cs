﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

using ECOOL_APP;
using ECOOL_APP.EF;
using EcoolWeb.Models;
using EcoolWeb.ViewModels;
using EcoolWeb.CustomAttribute;
using ECOOL_APP.com.ecool.service;
using log4net;

namespace EcoolWeb.Controllers
{
    [SessionExpire]
    public class ZZZI20Controller : Controller
    {
        private ECOOL_DEVEntities EntitiesDb = new ECOOL_DEVEntities();
        public static ILog logger = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        [CheckPermission] //檢查權限
        public ActionResult TurnIn(string IDNO)
        {
            if (string.IsNullOrWhiteSpace(IDNO)) RedirectToAction("Index");

            string SchoolNO = UserProfileHelper.GetSchoolNo();
            UserProfile LoginUser = UserProfileHelper.Get();

            ECOOL_DEVEntities db = new ECOOL_DEVEntities();
            List<Tuple<string, string, int>> valuesList = new List<Tuple<string, string, int>>();
            var History = db.HRMV01.Where(a => a.IDNO == IDNO);
            HRMV01 HrV01 = new HRMV01();
            int HRMT01Int32 = 0;
            HrV01 = db.HRMV01.Where(a => a.IDNO == IDNO && a.USER_STATUS != UserStaus.Enabled).FirstOrDefault();
            HRMT01Int32 = db.AWAT01_LOG.Where(a => a.SCHOOL_NO == HrV01.SCHOOL_NO && a.USER_NO == HrV01.USER_NO && a.LOG_DESC.Contains("酷幣匯轉區域管理")).Count();
            if (HRMT01Int32 == 0) {



                HRMV01 TurnIn = History.Where(a => a.SCHOOL_NO == SchoolNO && a.USER_STATUS != UserStaus.Invalid).OrderByDescending(a => a.GRADE).FirstOrDefault();

                HRMV01 TurnOut = History.Where(a => a.SCHOOL_NO != SchoolNO).OrderByDescending(a => a.GRADE).FirstOrDefault();

                AWAT01 UserCash_Out =
                    db.AWAT01.Where(user => user.SCHOOL_NO == TurnOut.SCHOOL_NO && user.USER_NO == TurnOut.USER_NO).FirstOrDefault();

                int TurnOutCash = 0;
                if (UserCash_Out != null)
                {
                    if (UserCash_Out.CASH_AVAILABLE.HasValue)
                        TurnOutCash = UserCash_Out.CASH_AVAILABLE.Value;
                }

                HRMT01 student = db.HRMT01.Where(a => a.SCHOOL_NO == TurnIn.SCHOOL_NO && a.USER_NO == TurnIn.USER_NO).FirstOrDefault();
                student.USER_STATUS = UserStaus.Enabled;

                string BATCH_ID = PushService.CreBATCH_ID();

                ECOOL_APP.CashHelper.AddCash(LoginUser, TurnOutCash, TurnIn.SCHOOL_NO, TurnIn.USER_NO, "ZZZI20", TurnIn.USER_NO, "酷幣匯轉區域管理", true, ref db, "", "",ref valuesList);

                string BODY_TXT = "酷幣匯轉區域管理，獲得酷幣點數" + (TurnOutCash).ToString() + "數";

                PushService.InsertPushDataMe(BATCH_ID, TurnIn.SCHOOL_NO, TurnIn.USER_NO, "", BODY_TXT, "", "Home", "ArrivedChanceRun", TurnIn.USER_NO, "", false, ref db);

                try
                {
                    db.SaveChanges();
                    TempData["StatusMessage"] = "異動成功";
                }
                catch (Exception ex)
                {
                    TempData["StatusMessage"] = "異動失敗, 系統錯誤原因:" + ex.Message;
                }

                //Push
                PushHelper.ToPushServer(BATCH_ID);
            }
            return RedirectToAction("Index");
        }
        [HttpGet]
        public ActionResult SycTurnInAll() {
            ZZZI20Service zzzI20 = new ZZZI20Service();
            ECOOL_DEVEntities db = new ECOOL_DEVEntities();
            List<BDMT01> bDMT01s = new List<BDMT01>();
            bDMT01s = db.BDMT01.Where(x => x.CITY == "臺北市").ToList();
            foreach (var item in bDMT01s) {
                SycTurnInSingle(item.SCHOOL_NO);

            }
            return Json("ok");
        }
        [HttpGet]
        public ActionResult SycTurnInSingle(string SchoolNO)
        {
            ZZZI20Service zzzI20 = new ZZZI20Service();
            List<Tuple<string, string, int>> valuesList = new List<Tuple<string, string, int>>();
          //  string SchoolNO = UserProfileHelper.GetSchoolNo();
          //   UserProfile LoginUser = UserProfileHelper.Get();
            string msg = "";
            ECOOL_DEVEntities db = new ECOOL_DEVEntities();
            List<HRMV01> TurnInList = new List<HRMV01>();
            //先找出該校有多少轉入學生
            var Students = db.HRMV01.Where(a => a.SCHOOL_NO == SchoolNO);

            foreach (HRMV01 s in Students)
            {
                if (s.USER_STATUS != UserStaus.Disable) continue;
                if (s.INIT_STATUS != 0) continue;
                int StudentStatuInCount = 0;
                StudentStatuInCount = db.HRMT01.Where(x => x.SCHOOL_NO == SchoolNO  && x.USER_TYPE == UserType.Student && x.IDNO == s.IDNO).Count();
                if (StudentStatuInCount > 0)
                {
                    int StudentStatuInTempCount = 0;
                    StudentStatuInTempCount = db.HRMT01.Where(x => x.SCHOOL_NO != SchoolNO && x.USER_TYPE == UserType.Student && x.IDNO == s.IDNO).Count();
                    if (StudentStatuInTempCount > 0) {


                    }
                    bool tempSTatus = true;
                    tempSTatus = zzzI20.TurnIn(s.IDNO, SchoolNO, ref msg,ref valuesList);
                    if (tempSTatus == false)
                    {
                        logger.Info("執行轉學生 錯誤" + msg);

                    }

                }


            }
            return Json("ok");
        }

        [HttpGet]
        public ActionResult SycTurnInEnivalid()
        {
           
            string SchoolNO = UserProfileHelper.GetSchoolNo();
            UserProfile LoginUser = UserProfileHelper.Get();
            ECOOL_DEVEntities db = new ECOOL_DEVEntities();
            List<HRMT01> hRMT01sList = new List<HRMT01>();
            hRMT01sList = db.HRMT01.Where(x => x.SCHOOL_NO == SchoolNO && x.USER_STATUS == UserStaus.Disable && x.INIT_STATUS == 0 ).ToList();
            foreach (HRMT01 s in hRMT01sList)
            {

                s.USER_STATUS = UserStaus.Enabled;
            }
            db.SaveChanges();
            return Json("ok");
        }
        [HttpGet]
        public ActionResult SycTurnIn()
        {
            ZZZI20Service zzzI20 = new ZZZI20Service();
            string SchoolNO = UserProfileHelper.GetSchoolNo();
            UserProfile LoginUser = UserProfileHelper.Get();
            string msg = "";
            string insertAtmmsg = "";
            ECOOL_DEVEntities db = new ECOOL_DEVEntities();
            List<HRMV01> TurnInList = new List<HRMV01>();
            //先找出該校有多少轉入學生
            var Students = db.HRMV01.Where(a => a.SCHOOL_NO == SchoolNO);
            List<Tuple<string, string, int>> valuesList = new List<Tuple<string, string, int>>();
            foreach (HRMV01 s in Students)
            {
                if (s.IDNO == "A131869932") {

                    bool T = true;
                }
                if (s.USER_STATUS != UserStaus.Disable) continue;
                if (s.INIT_STATUS != 0) continue;
                int StudentStatuInCount = 0;
                var temp =db.HRMV01.Where(a => a.IDNO == s.IDNO && a.USER_STATUS == UserStaus.Invalid).FirstOrDefault();
                StudentStatuInCount = db.HRMT01.Where(x => x.SCHOOL_NO == SchoolNO && x.USER_STATUS == UserStaus.Disable && x.USER_TYPE == UserType.Student &&x.IDNO == s.IDNO).Count();
                if (StudentStatuInCount > 0)
                {
                    //if (s.IDNO == "A231990078") { 
                    bool insertATM = true;
                    if (temp != null) { 
                    insertATM = zzzI20.insertATMCash(temp.SCHOOL_NO, temp.USER_NO, ref insertAtmmsg,ref valuesList);
                    if (insertATM == false)
                    {
                        logger.Info("執行轉學生ATM 存入定存 錯誤" + insertAtmmsg);

                    }

                    bool tempSTatus = true;

                    tempSTatus = zzzI20.TurnIn(s.IDNO,SchoolNO, ref msg, ref valuesList);
                    if (tempSTatus == false) {
                        logger.Info("執行轉學生 錯誤" + msg);

                    }
                    }
                    //}
                }
                

            }
            return Json("ok");
         }
       
        [CheckPermission] //檢查權限
        public ActionResult Details(string IDNO)
        {
            if (string.IsNullOrWhiteSpace(IDNO)) RedirectToAction("Index");

            string SchoolNO = UserProfileHelper.GetSchoolNo();

            ZZZI20DetailsViewModel model = new ZZZI20DetailsViewModel();

            using (ECOOL_DEVEntities db = new ECOOL_DEVEntities())
            {
                var History = db.HRMV01.Where(a => a.IDNO == IDNO);

                model.TurnIn = History.Where(a => a.SCHOOL_NO == SchoolNO && a.USER_STATUS != UserStaus.Invalid).OrderByDescending(a => a.GRADE).FirstOrDefault();

                model.TurnOut = History.Where(a => a.SCHOOL_NO != SchoolNO).OrderByDescending(a => a.GRADE).FirstOrDefault();
                //where SCHOOL_NO='323607' 臺北市信義區福德國民小學 位加入酷幣

                model.TurnOutSchool = db.BDMT01.Find(model.TurnOut.SCHOOL_NO) == null ? model.TurnOut.SCHOOL_NO : db.BDMT01.Find(model.TurnOut.SCHOOL_NO).SCHOOL_NAME;
                if (model.TurnOut.SCHOOL_NO == "323607")
                {
                    model.TurnOutSchool = "臺北市信義區福德國民小學";
                }
                AWAT01 UserCash_Out =
                    db.AWAT01.Where(user => user.SCHOOL_NO == model.TurnOut.SCHOOL_NO && user.USER_NO == model.TurnOut.USER_NO).FirstOrDefault();

                if (UserCash_Out != null)
                {
                    if (UserCash_Out.CASH_AVAILABLE.HasValue)
                        model.TurnOutCash = UserCash_Out.CASH_AVAILABLE.Value;
                }
            }

            return View(model);
        }

        // GET: ZZZI20
        [CheckPermission] //檢查權限
        public ActionResult Index()
        {
            string SchoolNO = UserProfileHelper.GetSchoolNo();
            List<HRMV01> TurnInList = new List<HRMV01>();

            using (ECOOL_DEVEntities db = new ECOOL_DEVEntities())
            {
                //先找出該校有多少轉入學生
                var Students = db.HRMV01.Where(a => a.SCHOOL_NO == SchoolNO);

                //篩選還沒處理的
                foreach (HRMV01 s in Students)
                {
                    if (s.USER_STATUS != UserStaus.Disable) continue;
                    if (s.INIT_STATUS != 0) continue;

                    AWAT01 UserCash = db.AWAT01.Where(user => user.SCHOOL_NO == s.SCHOOL_NO && user.USER_NO == s.USER_NO).FirstOrDefault();
                    if (UserCash != null)
                    {
                        if (UserCash.CASH_ALL > 0) continue;
                    }

                    TurnInList.Add(s);
                }
            }

            return View(TurnInList);
        }
    }
}