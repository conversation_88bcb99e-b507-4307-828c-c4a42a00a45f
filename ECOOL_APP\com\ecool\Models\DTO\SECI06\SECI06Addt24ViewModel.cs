﻿
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Text;

namespace ECOOL_APP.EF
{
    public class SECI06Addt24ViewModel
    {
        /// <summary>
        /// 當天
        /// </summary>
        [DisplayName("當天日期")]
        public int The_day_M { get; set; }

        /// <summary>
        /// 昨天
        /// </summary>
        [DisplayName("昨天日期")]
        public int Yesterday_M { get; set; }

        /// <summary>
        /// 前天
        /// </summary>
       [DisplayName("前天日期")]
        public int The_day_before_yesterday_M { get; set; }

        /// <summary>
        ///累計里程
        /// </summary>
        [DisplayName("累計里程")]
        public double Total_M { get; set; }
    }
}