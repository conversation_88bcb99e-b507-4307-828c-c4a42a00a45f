﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class RollCallBarcodeEditMainViewModel
    {
        [Key]
        public string ROLL_CALL_ID { get; set; }

        /// <summary>
        ///活動名稱
        /// </summary>
        [DisplayName("活動名稱")]
        [Required]
        public string ROLL_CALL_NAME { get; set; }

        /// <summary>
        ///活動簡介
        /// </summary>
        [DisplayName("活動簡介")]
        public string ROLL_CALL_DESC { get; set; }

        /// <summary>
        ///活動開始日
        /// </summary>
        [DisplayName("點數兌換開始日期")]
        [Required]
        public DateTime? ROLL_CALL_DATES { get; set; }

        /// <summary>
        ///活動結束日
        /// </summary>
        [DisplayName("點數兌換結束日期")]
        [Required]
        public DateTime? ROLL_CALL_DATEE { get; set; }
        public DateTime? CRE_DATE { get; set; }
        /// <summary>
        ///點名類別 1.一般點名,2 對照點名
        /// </summary>
        [DisplayName("點名類別 1.一般點名,2 對照點名")]
        public byte? ROLL_CALL_TYPE { get; set; }
        public string CRE_PERSON { get; set; }
        public string CRE_PERSON_NAME { get; set; }
        // <summary>
        //預設獲得點數
        // </summary>
        [DisplayName("預設獲得點數")]

        public short? CASH { get; set; }
        [DisplayName("預設發幾份")]

        public short? ROLL_CASH_NUM { get; set; }

        public short? ROLL_CASH_SUM { get; set; }
        /// <summary>
        ///1.點名前 ,2 點名後,3.己給點
        /// </summary>
        [DisplayName("1.點名前 ,2 點名後,3.己給點")]
        public byte? STATUS { get; set; }
        public bool IS_SHOW { get; set; }
        public bool IS_RemarkY { get; set; }
        public bool IS_RemarkF { get; set; }

        public bool IS_Remark
        {
            get
            {
                if (IS_RemarkY == true)
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }

            set
            {


                if (value == true)
                {
                    IS_RemarkY = true;
                    IS_RemarkF = false;
                }
                else
                {
                    IS_RemarkY = false;
                    IS_RemarkF = true;
                }
            }
        }
    }
}
