﻿using com.ecool.service;
using ECOOL_APP.com.ecool.Models.DTO;
using ECOOL_APP.com.ecool.Models.entity;
using ECOOL_APP.EF;
using EcoolWeb.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using MvcPaging;
using ECOOL_APP;
using EcoolWeb.CustomAttribute;
using System.Transactions;
using System.Data.Entity.Core.EntityClient;
using System.Data.SqlClient;
using System.IO;
using System.Drawing.Imaging;
using ECOOL_APP.com.ecool.Models.DTO.ZZZI08;
using ECOOL_APP.com.ecool.util;
using System.Data;
using NPOI.SS.UserModel;
using EcoolWeb.Util;

namespace EcoolWeb.Controllers
{
    [SessionExpire]
    public class ADDI05Controller : Controller
    {
        /// <summary>
        /// 此Controller 對應的程式代碼
        /// </summary>
        private string Bre_NO = "ADDI05";

        /// <summary>
        /// Error 訊息
        /// </summary>
        private string ErrorMsg = string.Empty;

        /// <summary>
        /// 資料庫相關處理
        /// </summary>
        private ADDI05Service Db = new ADDI05Service();

        /// <summary>
        /// 會員相關資訊
        /// </summary>
        private UserProfile user;

        [CheckPermission] //檢查權限
        public ActionResult Index()
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "有獎徵答-有獎徵答一覽表";

            return View();
        }

        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult PageContent(string SearchContents = "", string OrderByName = "", string SyntaxName = "", int page = 1, int pageSize = 10, byte? DialogType = null
            , byte? whereShowData = null)
        {
            ViewBag.BRE_NO = Bre_NO;
            string SchoolNO = UserProfileHelper.GetSchoolNo();
            user = UserProfileHelper.Get();

            string AddDialog = "N";

            if (user != null)
            {
                //新增有獎徵答權限
                AddDialog = PermissionService.GetPermission_Use_YN("ZZZI08", "Save", user.SCHOOL_NO, user.USER_NO);
            }

            ViewBag.DialogTypeItems = ADDT11.GetDialogTypeItems(DialogType, "全部");
            ViewBag.AddDialog = AddDialog;

            if (whereShowData == null)
            {
                whereShowData = (byte)ADDT11.ShowDateType.全部;
            }

            int count = int.MinValue;
            List<ADDI05IndexViewModel> model = Db.GetListData(SchoolNO, SearchContents, OrderByName, SyntaxName, DialogType, whereShowData, page, pageSize, ref count, user);
            ViewData.Model = model.ToPagedList(page - 1, pageSize, count);

            TempData["SearchContents"] = SearchContents;
            TempData["OrderByName"] = OrderByName;
            TempData["SyntaxName"] = SyntaxName;
            TempData["page"] = page;
            TempData["whereShowData"] = whereShowData;

            return PartialView();
        }

        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult detail(string DIALOG_ID, string SearchContents, string OrderByName, string SyntaxName, int? page, string PreviewY = null)
        {
            string SchoolNO = UserProfileHelper.GetSchoolNo();
            user = UserProfileHelper.Get();
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "有獎徵答-活動內容";
            ViewBag.PreviewY = PreviewY;

            TempData["DIALOG_ID"] = DIALOG_ID;
            TempData["SearchContents"] = SearchContents;
            TempData["OrderByName"] = OrderByName;
            TempData["SyntaxName"] = SyntaxName;
            TempData["page"] = page;
            if (user != null)
            {
                ViewBag.userType = user.USER_TYPE;
            }
            else
            {
                ViewBag.userType = "C";
            }
            ViewBag.PassCount = new CommService().GetTotalCount("ADDT13", "DIALOG_ID='" + DIALOG_ID + "'");

            var model = Db.GetMainData(DIALOG_ID);

            this.IsAns(model.uADDT11);

            ViewBag.AnsShow = GetIsAnsShow(model.uADDT11.DIALOG_EDATE, model.uADDT11.STATUS);

            if (ViewBag.AnsShow == false)
            {
                //暫時先開放
                //ViewBag.BtnClassAnsShow = "btn btn-default active disabled";
                ViewBag.BtnClassAnsShow = "btn btn-default";
                ViewBag.BtnNameAnsShow = "查看題目 (活動未結束)";
            }
            else
            {
                ViewBag.BtnClassAnsShow = "btn btn-default";
                ViewBag.BtnNameAnsShow = "查看題目";
            }

            return View(model);
        }

        #region 下載(全部人)

        private string SysPath = System.Web.Configuration.WebConfigurationManager.AppSettings["FilePath"] + @"\\ADDT11_FILE\\";

        public ActionResult DownLoad(String DIALOG_ID, String name)
        {
            string tempPath = SysPath + DIALOG_ID + @"\\" + name;

            if (System.IO.File.Exists(tempPath))
            {
                return File(tempPath, "text/plain", Server.HtmlEncode(name));
            }
            else
            {
                return Content("No file name provided");
            }
        }

        #endregion 下載(全部人)

        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult PassList(ADDI05PassListViewModel model)
        {
            if (model == null) model = new ADDI05PassListViewModel();

            string SchoolNO = UserProfileHelper.GetSchoolNo();
            user = UserProfileHelper.Get();
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "有獎徵答-全對學生名單";
            string PassOrderBy2Name = "";
            if (string.IsNullOrWhiteSpace(model.PassOrderByName))
            {
                model.PassOrderByName = "A.CRE_DATE "+model.OrderRank;
                PassOrderBy2Name = "__RowNumberTable.CRE_DATE " + model.OrderRank;
            }
            else if (model.PassOrderByName == "CRE_DATE")
            {


                model.PassOrderByName = "A.CRE_DATE " + model.OrderRank;
                PassOrderBy2Name = "__RowNumberTable.CRE_DATE " + model.OrderRank;
            }
            else if (model.PassOrderByName == "A.CRE_DATE ")
            {


                model.PassOrderByName = "A.CRE_DATE " + model.OrderRank;
                PassOrderBy2Name = "__RowNumberTable.CRE_DATE " + model.OrderRank;
            }
            else if (model.PassOrderByName == "counts") {


                model.PassOrderByName = "A.CRE_DATE " + model.OrderRank;
                PassOrderBy2Name = "__RowNumberTable.counts " + model.OrderRank;
            }
            else
            {
                PassOrderBy2Name = model.PassOrderByName+" "+model.OrderRank;


            }

            var data = Db.GetMainData(model.DIALOG_ID);
            uADDT11 uADDT11 = data.uADDT11;
            ViewBag.DIALOG_NAME = uADDT11.DIALOG_NAME;

            if (uADDT11.SCHOOL_NO != SharedGlobal.ALL)
            {
                ViewBag.Panel_Title = "有獎徵答-" + UserProfileHelper.GetSchoolSName(uADDT11.SCHOOL_NO) + "全對學生名單";
            }
            else
            {
                ViewBag.Panel_Title = "有獎徵答-全對學生名單";
            }

            List<SelectListItem> CountNumItem = new List<SelectListItem>();
            CountNumItem.Add(new SelectListItem() { Text = "100筆", Value = "100", Selected = model.PassPageSize == 100 });
            CountNumItem.Add(new SelectListItem() { Text = "300筆", Value = "300", Selected = model.PassPageSize == 300 });
            CountNumItem.Add(new SelectListItem() { Text = "500筆", Value = "500", Selected = model.PassPageSize == 500 });
            ViewBag.CountNumItem = CountNumItem;

            ViewBag.VisibleLotto = false;
            if (user != null)
            {
                if (user.USER_TYPE == UserType.Admin)
                {
                    ViewBag.VisibleLotto = true;
                }
                else if (uADDT11.USER_NO == user.USER_NO)
                {
                    ViewBag.VisibleLotto = true;
                }
            }

            ViewBag.PassCount = new CommService().GetTotalCount("ADDT13", "DIALOG_ID='" + model.DIALOG_ID + "'");

            int PassCount = int.MinValue;
            if (model.LotteryCount > 0)
            {
                model.PassPageSize = 500;
            }

            List<uADDT13> uADDT13List = Db.PassListData(model.DIALOG_ID, model.PassPage, model.PassPageSize, model.PassOrderByName, model.PassSearchContents, ref PassCount, PassOrderBy2Name);
            model.uADDT13 = uADDT13List.ToPagedList(model.PassPage - 1, model.PassPageSize, PassCount);

            if (model.LotteryCount == 0)
            {
                return View(model);
            }

            ECOOL_DEVEntities db13 = new ECOOL_DEVEntities();
            List<ADDT13> UserList = db13.ADDT13.Where(a => a.DIALOG_ID == model.DIALOG_ID).ToList();
            foreach (ADDT13 a13 in UserList)
            {
                a13.LOTTO_ORDER = 0;
            }

            //抽獎
            var items = Enumerable.Range(1, model.LotteryCount).OrderBy(d => Guid.NewGuid()).ToList();

            int Max = uADDT13List.Count - 1;
            int seed = Guid.NewGuid().GetHashCode();
            Random Chance = new Random(seed);

            List<int> LotteryNumberList = new List<int>();
            List<uADDT13> LotteryList = new List<uADDT13>();
            while (LotteryList.Count < model.LotteryCount)
            {
                int n = Chance.Next(0, Max);
                if (LotteryNumberList.Contains(n)) continue;

                LotteryNumberList.Add(n);
                LotteryList.Add(uADDT13List[n]);
                var uu = UserList.Where(a => a.SCHOOL_NO == uADDT13List[n].SCHOOL_NO && a.USER_NO == uADDT13List[n].USER_NO).FirstOrDefault();
                if (uu != null)
                {
                    uu.LOTTO_ORDER = Convert.ToInt16(items[LotteryList.Count - 1]);
                    uADDT13List[n].LOTTO_ORDER = uu.LOTTO_ORDER.Value;
                }
            }
            db13.SaveChanges();
            model.uADDT13 = LotteryList.OrderBy(a => a.LOTTO_ORDER).ToPagedList(model.PassPage - 1, model.PassPageSize, PassCount); ;

            return View("PassListLotto", model);
        }

        /// <summary>
        /// 回答名單
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
#if !DEBUG
        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
#endif

        public ActionResult AnswerList(ADDI05AnswerListViewModel model)
        {
            if (model == null) model = new ADDI05AnswerListViewModel();

            string SchoolNO = UserProfileHelper.GetSchoolNo();
            user = UserProfileHelper.Get();

            var data = Db.GetMainData(model.DIALOG_ID);
            uADDT11 uADDT11 = data.uADDT11;
            model.DIALOG_NAME = uADDT11.DIALOG_NAME;
            string PassOrderBy2Name = "";
            //if (string.IsNullOrWhiteSpace(model.OrderByName))
            //{
            //    model.OrderByName = "A.CRE_DATE " + model.OrderRank;
            //    PassOrderBy2Name = "__RowNumberTable.CRE_DATE " + model.OrderRank;
            //}
            //else if (model.OrderByName == "CRE_DATE")
            //{


            //    model.OrderByName = "A.CRE_DATE " + model.OrderRank;
            //    PassOrderBy2Name = "__RowNumberTable.CRE_DATE " + model.OrderRank;
            //}
            //else if (model.OrderByName == "counts")
            //{


            //    model.OrderByName = "A.CRE_DATE " + model.OrderRank;
            //    PassOrderBy2Name = "__RowNumberTable.counts " + model.OrderRank;
            //}
            //else
            //{
            //    PassOrderBy2Name = model.OrderByName + " " + model.OrderRank;


            //}
            ViewBag.BRE_NO = Bre_NO;
            if (uADDT11.SCHOOL_NO != SharedGlobal.ALL)
                ViewBag.Panel_Title = "有獎徵答-" + UserProfileHelper.GetSchoolSName(uADDT11.SCHOOL_NO) + "所有答題學生名單";
            else
                ViewBag.Panel_Title = "有獎徵答-所有答題學生名單";

            if (string.IsNullOrWhiteSpace(model.OrderByName))
            {
                model.OrderByName = nameof(uADDT11.CRE_DATE);
            }
            List<uADDT13> uADDT13List = Db.AnswerListData(model.DIALOG_ID, model.OrderByName, model.SearchContents);
            model.uADDT13 = uADDT13List.ToPagedList(model.Page - 1, model.PageSize);

            return View(model);
        }

        /// <summary>
        /// 學生每次作答的得分
        /// </summary>
        /// <returns></returns>
#if !DEBUG
                [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
#endif

        public ActionResult EachAnswerList(ADDI05EachAnswerListViewModel model)
        {
            if (model == null) model = new ADDI05EachAnswerListViewModel();

            string SchoolNO = UserProfileHelper.GetSchoolNo();
            user = UserProfileHelper.Get();

            var data = Db.GetMainData(model.DIALOG_ID);
            uADDT11 uADDT11 = data.uADDT11;
            model.DIALOG_NAME = uADDT11.DIALOG_NAME;

            ViewBag.BRE_NO = Bre_NO;
            if (uADDT11.SCHOOL_NO != SharedGlobal.ALL)
                ViewBag.Panel_Title = "有獎徵答-" + UserProfileHelper.GetSchoolSName(uADDT11.SCHOOL_NO) + "所有答題學生名單";
            else
                ViewBag.Panel_Title = "有獎徵答-所有答題學生名單";

            if (string.IsNullOrWhiteSpace(model.OrderByName))
            {
                model.OrderByName = nameof(uADDT13_Each.Grade);
            }

            List<uADDT13_Each> uADDT13List = Db.AnswerListDataForEachUser(model.DIALOG_ID, model.Where_USERNO, model.Where_SCHOOLNO, model.OrderByName);
            model.uADDT13_Each = uADDT13List;

            return View(model);
        }

        public ActionResult PrintExcel(ADDI05PassListViewModel model)
        {
            string PassOrderBy2Name = "";
            if (string.IsNullOrWhiteSpace(model.PassOrderByName))
            {
                model.PassOrderByName = "A.CRE_DATE DESC";
                PassOrderBy2Name = "__RowNumberTable.CRE_DATE";
            }
            else if (model.PassOrderByName == "A.CRE_DATE")
            {


                model.PassOrderByName = "A.CRE_DATE DESC";
                PassOrderBy2Name = "__RowNumberTable.CRE_DATE ";
            }
            else
            {
                PassOrderBy2Name = model.PassOrderByName;


            }

            var data = Db.GetMainData(model.DIALOG_ID);
            uADDT11 uADDT11 = data.uADDT11;
            string FileName = "有獎徵答-" + uADDT11.DIALOG_NAME;
            if (model.LotteryCount > 0)
            {
                FileName += "抽獎名單";
            }
            else
            {
                FileName += "答對清單";
            }

            int PassCount = short.MinValue;
            model.PassPageSize = short.MaxValue;

            List<uADDT13> uADDT13List = Db.PassListData(model.DIALOG_ID, model.PassPage, model.PassPageSize, model.PassOrderByName, model.PassSearchContents, ref PassCount, PassOrderBy2Name); 

            DataTable DataTableExcel = null;
            if (model.LotteryCount > 0)
            {
                //int Max = uADDT13List.Count-1;
                //int seed = Guid.NewGuid().GetHashCode();
                //Random Chance = new Random(seed);

                //List<int> LotteryNumberList = new List<int>();
                //List<uADDT13> LotteryList = new List<uADDT13>();
                //while (LotteryList.Count< model.LotteryCount)
                //{
                //    int n = Chance.Next(0, Max);
                //    if (LotteryNumberList.Contains(n)) continue;
                //    LotteryNumberList.Add(n);
                //    LotteryList.Add(uADDT13List[n]);
                //}
                DataTableExcel = uADDT13List.Where(a => a.LOTTO_ORDER > 0).ToList().OrderBy(a => a.LOTTO_ORDER).AsDataTable();
            }
            else
            {
                DataTableExcel = uADDT13List.ToList().AsDataTable();
            }

            NPOIHelper npoi = new NPOIHelper();
            string TempleteFileFullName = Server.MapPath(@"~/Content/ExcelSample/ADDI05_Log_ExportExcel.xlsx");

            FileStream xlsfile = new FileStream(TempleteFileFullName, FileMode.Open, FileAccess.Read);
            IWorkbook aBook = WorkbookFactory.Create(xlsfile);

            npoi.ExportExcelFromTemplete(DataTableExcel, aBook, "工作表1", false, 2);

            string strTMPFile = System.Web.Configuration.WebConfigurationManager.AppSettings["FilePath"] + @"\TMP\" + DateTime.Now.Date.ToString("yyyyMMdd");

            if (Directory.Exists(strTMPFile) == false)
            {
                Directory.CreateDirectory(strTMPFile);
            }

            strTMPFile = strTMPFile + @"\" + FileName + "_" + Session.SessionID + ".xlsx";

            System.IO.FileStream FS = new FileStream(strTMPFile, FileMode.Create);
            aBook.Write(FS);
            FS.Close();

            return File(strTMPFile, "application/vnd.ms-excel", "" + FileName + ".xlsx");//輸出檔案給Client端
        }

        //判斷活動結束與否
        public bool GetIsAnsShow(DateTime? DIALOG_EDATE, string STATUS)
        {
            bool AnsShow = false;

            if (STATUS == ZZZI08EditViewModel.DATA_TYPE_Z)
            {
                AnsShow = true;
            }
            else
            {
                if (DIALOG_EDATE != null)
                {
                    //活動結束才能顯示答案
                    if (DIALOG_EDATE < DateTime.Today)
                    {
                        AnsShow = true;
                    }
                }
            }

            return AnsShow;
        }

        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult TotalGraph(ADDI05TotalGraphViewModel model)
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "有獎徵答-統計分析";
            ECOOL_DEVEntities db = new ECOOL_DEVEntities();

            model.UADDT11 = Db.GetMainData(model.DIALOG_ID);

            ViewBag.IsShowAns = (model.UADDT11.uADDT11.STATUS == uADDT11.STATUS_Z || model.UADDT11.uADDT11.DIALOG_EDATE < DateTime.Now) ? true : false;

            ViewBag.DIALOG_NAME = model.UADDT11?.uADDT11?.DIALOG_NAME;

            model.UADDT12 = Db.GetQusListData(model.DIALOG_ID, "default", false);

            model = Db.GetTotalGraph(model, ref db);

            return View(model);
        }

        public ActionResult TotalGraphDetails(ADDI05TotalGraphDetailsViewModel model)
        {
            ECOOL_DEVEntities db = new ECOOL_DEVEntities();
            string SchoolNO = UserProfileHelper.GetSchoolNo();
            user = UserProfileHelper.Get();

            if (user == null)
            {
                return RedirectToAction("PermissionError", "Error");
            }

            model = Db.GetTotalGraphAnsList(model.DIALOG_ID, model.Q_NUM, model.ANSWER, model.CLASS_NO, ref db);

            if (model.MADDT11 == null)
            {
                return RedirectToAction(ErrorHelper.ErrorVal.NotFindError, "Error");
            }

            if (user.USER_KEY != model.MADDT11.CRE_PERSON && HRMT24_ENUM.CheckQAdmin(user) == false)
            {
                return RedirectToAction("PermissionError", "Error");
            }

            return PartialView(model);
        }

        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult QusList(string DIALOG_ID, string SearchContents, string OrderByName, string SyntaxName, int? page)
        {
            string SchoolNO = UserProfileHelper.GetSchoolNo();
            user = UserProfileHelper.Get();
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "有獎徵答-活動題目";

            TempData["DIALOG_ID"] = DIALOG_ID;
            TempData["SearchContents"] = SearchContents;
            TempData["OrderByName"] = OrderByName;
            TempData["SyntaxName"] = SyntaxName;
            TempData["page"] = page;

            var data = Db.GetMainData(DIALOG_ID);
            uADDT11 uADDT11 = data.uADDT11;
            ViewBag.DIALOG_NAME = uADDT11.DIALOG_NAME;

            ViewBag.AnsShow = GetIsAnsShow(uADDT11.DIALOG_EDATE, uADDT11.STATUS);

            if (ViewBag.AnsShow == false)
            {
                TempData["StatusMessage"] = "<h4>活動結束才會公佈答案</h4>";
            }

            List<uADDT12> model = Db.GetQusListData(DIALOG_ID, "default", uADDT11.RANDOM);

            return View(model);
        }

        /// <summary>
        /// 回答
        /// </summary>
        /// <param name="Data"></param>
        /// <param name="DIALOG_ID"></param>
        /// <param name="SearchContents"></param>
        /// <param name="OrderByName"></param>
        /// <param name="SyntaxName"></param>
        /// <param name="page"></param>
        /// <param name="mode"></param>
        /// <param name="PreviewY"></param>
        /// <returns></returns>
       // [CheckPermission(CheckACTION_ID = "Answer")] //檢查權限
        public ActionResult Answer(IEnumerable<ADDI05AnswerViewModel> Data, string DIALOG_ID, string SearchContents, string OrderByName, string SyntaxName, int? page, string mode, string PreviewY = null)
        {
            ECOOL_DEVEntities db = new ECOOL_DEVEntities();
            string SchoolNO = UserProfileHelper.GetSchoolNo();
            user = UserProfileHelper.Get();

            if (mode == "again") //重新作答
            {
                return RedirectToAction("Answer", Bre_NO, new { DIALOG_ID = DIALOG_ID, SearchContents = SearchContents, OrderByName = OrderByName, SyntaxName = SyntaxName, page = page });
            }

            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "有獎徵答-我要作答";

            ViewBag.PreviewY = PreviewY;

            TempData["DIALOG_ID"] = DIALOG_ID;
            TempData["SearchContents"] = SearchContents;
            TempData["OrderByName"] = OrderByName;
            TempData["SyntaxName"] = SyntaxName;
            TempData["page"] = page;

            var data = Db.GetMainData(DIALOG_ID);
            uADDT11 uADDT11 = data.uADDT11;
            ViewBag.DIALOG_NAME = uADDT11.DIALOG_NAME;

            if (ViewBag.PreviewY == "Y")
            {
                if (user == null)
                {
                    return RedirectToAction("PermissionError", "Error");
                }

                if (PermissionService.GetPermission_Use_YN("ZZZI08", "Index", SchoolNO, user.USER_NO) == "N")
                {
                    return RedirectToAction("PermissionError", "Error");
                }
            }

            //判斷是否有權作答
            this.IsAns(uADDT11);
            if (PreviewY != "Y")
            {
                if (ViewBag.Btnbool == false)
                {
                    ViewBag.StatusMessage = ViewBag.BtnName;
                    return RedirectToAction("detail", Bre_NO, new { DIALOG_ID = DIALOG_ID, SearchContents = SearchContents, OrderByName = OrderByName, SyntaxName = SyntaxName, page = page });
                }
            }

            List<uADDT12> uADDT12 = Db.GetQusListData(DIALOG_ID, "NEWID", uADDT11.RANDOM);
            List<ADDI05AnswerViewModel> model = uADDT12.Select(a => new ADDI05AnswerViewModel() { uADDT12 = a }).ToList();

            Boolean DataBool = false;

            //已作答
            if (Data != null)
            {
                DataBool = true;

                var updated = (from x in Data
                               join y in model on x.uADDT12.Q_NUM equals y.uADDT12.Q_NUM
                               select new ADDI05AnswerViewModel()
                               {
                                   uADDT12 = y.uADDT12,
                                   Ans = x.Ans
                               }).ToList();

                var NG = from a in updated
                         where a.uADDT12.TRUE_ANS != a.Ans
                         select a;

                if (uADDT11.USER_NO != user?.USER_NO)
                {
                    ADDT13_HIS Cre = new ADDT13_HIS();

                    Cre.HIS_NO = Guid.NewGuid().ToString("N");
                    Cre.DIALOG_ID = uADDT11.DIALOG_ID;
                    Cre.SCHOOL_NO = user.SCHOOL_NO;
                    Cre.USER_NO = user.USER_NO;
                    Cre.CLASS_NO = user.CLASS_NO;

                    int SYear;
                    int Semesters;
                    SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);
                    Cre.SYEAR = SYear;
                    Cre.SEMESTER = (byte)Semesters;
                    Cre.SEAT_NO = user.SEAT_NO;
                    Cre.NAME = user.NAME;
                    Cre.SNAME = user.SNAME;
                    Cre.CRE_PERSON = user.USER_KEY;
                    Cre.CRE_DATE = DateTime.Now;
                    Cre.RIGHT_YN = NG.Count() == 0 ? "Y" : "N";

                    db.ADDT13_HIS.Add(Cre);

                    var CreList = (from a in updated
                                   select new ADDT13_HIS_D()
                                   {
                                       HIS_NO = Cre.HIS_NO,
                                       DIALOG_ID = a.uADDT12.DIALOG_ID,
                                       Q_NUM = (int)a.uADDT12.Q_NUM,
                                       ANSWER = a.Ans,
                                       RIGHT_YN = a.uADDT12.TRUE_ANS == a.Ans ? "Y" : "N"
                                   }).ToList();

                    db.ADDT13_HIS_D.AddRange(CreList);

                    try
                    {
                        db.SaveChanges();
                    }
                    catch (Exception ex)
                    {
                        throw ex;
                    }
                }

                if (NG.Count() == 0)
                {
                    if (user != null)
                    {
                        int? CASG = (uADDT11.USER_NO != user?.USER_NO) ? this.Save(DIALOG_ID, uADDT11) : null;

                        if (user?.USER_TYPE == UserType.Student)
                        {
                            TempData["StatusMessage"] = "恭喜您全答對了，獲得" + CASG.ToString() + "酷幣";
                        }
                        else
                        {
                            if (uADDT11.USER_NO != user?.USER_NO)
                            {
                                TempData["StatusMessage"] = "恭喜您全答對了";
                            }
                            else
                            {
                                TempData["StatusMessage"] = "恭喜你全對，但是建立者答對，名單不會出現在名單裡。";
                            }
                        }

                        return RedirectToAction("detail", Bre_NO, new { DIALOG_ID = DIALOG_ID, SearchContents = SearchContents, OrderByName = OrderByName, SyntaxName = SyntaxName, page = page });
                    }
                }
                else
                {
                    var Orderdated = (from x in Data
                                      join y in model on x.uADDT12.Q_NUM equals y.uADDT12.Q_NUM
                                      select new ADDI05AnswerViewModel()
                                      {
                                          uADDT12 = y.uADDT12,
                                          Ans = x.Ans,
                                          O_Orderby = x.O_Orderby
                                      }).ToList().OrderBy(a => a.O_Orderby);

                    foreach (var item in NG)
                    {
                        ModelState.AddModelError(item.uADDT12.Q_NUM.ToString(), "這題答錯");
                    }
                    TempData["StatusMessage"] = "您有" + NG.Count().ToString() + "答錯。";

                    ViewBag.DataBool = DataBool;
                    return View(Orderdated);
                }
            } //已作答

            ViewBag.DataBool = DataBool;
            return View(model);
        }

        public int? Save(string DIALOG_ID, uADDT11 dtADDT11)
        {
            //using (TransactionScope ts = new TransactionScope())
            //{
            ECOOL_DEVEntities db = new ECOOL_DEVEntities();
            List<Tuple<string, string, int>> valuesList = new List<Tuple<string, string, int>>();
            int? TO_CASG = null;

            var find1 = db.ADDT13.Where(u => u.SCHOOL_NO == user.SCHOOL_NO && u.USER_NO == user.USER_NO && u.DIALOG_ID == DIALOG_ID).Any();
            if (!find1) //防呆
            {
                ADDT13 ADDT13Data = db.ADDT13.Create();

                ADDT13Data.SCHOOL_NO = user.SCHOOL_NO;
                ADDT13Data.USER_NO = user.USER_NO;
                ADDT13Data.CLASS_NO = user.CLASS_NO;

                ADDT13Data.DIALOG_ID = DIALOG_ID;
                ADDT13Data.NAME = user.NAME;

                ADDT13Data.SEAT_NO = user.SEAT_NO;

                int SYear;
                int Semesters;
                SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);

                ADDT13Data.SEMESTER = (byte)Semesters;
                ADDT13Data.SYEAR = (short)SYear;
                ADDT13Data.SNAME = user.SNAME;
                ADDT13Data.CRE_DATE = DateTime.Now;
                ADDT13Data.CRE_PERSON = user.USER_KEY;

                db.ADDT13.Add(ADDT13Data);

                if (dtADDT11.CASH != null && dtADDT11.CASH > 0 && user?.USER_TYPE != UserType.Teacher)
                {
                    TO_CASG = dtADDT11.CASH;
                }
                else if (dtADDT11.GetCASH != null && dtADDT11.GetCASH > 0 && user?.USER_TYPE == UserType.Teacher)
                {
                    TO_CASG = dtADDT11.GetCASH;
                }
                else
                {
                    uAWAT05 SouData = new AwatService().GetSouData("ADDT13", 1);
                    TO_CASG = SouData.TO_CASG;
                }

                if (user?.USER_TYPE == UserType.Student)
                {
                    ECOOL_APP.CashHelper.AddCash(user, Convert.ToInt32(TO_CASG), user.SCHOOL_NO, user.USER_NO, "ADDT13", DIALOG_ID, "有獎徵答", true, ref db, "", "",ref valuesList);
                }
                else if (user?.USER_TYPE == UserType.Teacher)
                {
                    ECOOL_APP.CashHelper.TeachAddCash(user, Convert.ToInt32(TO_CASG), user.SCHOOL_NO, user.USER_NO, "ADDT13", DIALOG_ID, "有獎徵答", true, null, ref db);
                }

                string BATCH_ID = PushService.CreBATCH_ID();
                string BODY_TXT = "有獎徵答，全部答對、獲得酷幣點數" + (TO_CASG).ToString() + "數";

                PushService.InsertPushDataParents(BATCH_ID, user.SCHOOL_NO, user.USER_NO, "", BODY_TXT, "", "ADDI05", "Save", DIALOG_ID, "", false, ref db);
                PushService.InsertPushDataMe(BATCH_ID, user.SCHOOL_NO, user.USER_NO, "", BODY_TXT, "", "ADDI05", "Save", DIALOG_ID, "", false, ref db);

                try
                {
                    db.SaveChanges();
                }
                catch (Exception ex)
                {
                    throw ex;
                }

                //更新顯示
                UserProfile.RefreshCashInfo(user, ref db);
                UserProfileHelper.Set(user);
            }

            return TO_CASG;
            //    ts.Complete();
            //}
        }

        public void IsAns(uADDT11 Main)
        {
            bool Btnbool = true;
            string BtnName = "我要作答";
            string BtnClass = "btn btn-default";

            if (Main.STATUS == "Z")
            {
                Btnbool = false;
                BtnName = "作答已提早結束";
                BtnClass = BtnClass + " active disabled ";
            }
            else if (Main.DIALOG_EDATE < DateTime.Today)
            {
                Btnbool = false;
                BtnName = "作答已結束";
                BtnClass = BtnClass + " active disabled ";
            }
            else if (Main.DIALOG_SDATE > DateTime.Today)
            {
                Btnbool = false;
                BtnName = "活動未開始";
                BtnClass = BtnClass + " active disabled ";
            }
            else
            {
                string SchoolNO = UserProfileHelper.GetSchoolNo();
                user = UserProfileHelper.Get();
                string USER_NO = user == null ? "" : user.USER_NO;

                //判斷是否有權作答
                string UseYN = PermissionService.GetPermission_Use_YN(Bre_NO, "Answer", SchoolNO, USER_NO);
                if (UseYN == "N")
                {
                    Btnbool = false;
                    BtnName = "您無權作答(A)";
                    BtnClass = BtnClass + " active disabled ";
                }
                else
                {
                    //判斷是否作答過
                    bool Pass = false;
                    bool isUse = true;
                    bool isCrePerson = true;
                    using (ECOOL_DEVEntities Edb = new ECOOL_DEVEntities())
                    {
                        if (Main.ANSWER_PERSON_YN == "Y")
                        {
                            isUse = Edb.REFT01.Where(A => A.REF_KEY == Main.DIALOG_ID
                                    && A.REF_TABLE == "ADDT11"
                                    && A.SCHOOL_NO == SchoolNO
                                    && A.USER_NO == USER_NO).Any();
                            isCrePerson = Edb.ADDT11.Where(x => x.CRE_PERSON == user.SCHOOL_NO + "_" + user.USER_NO).Any();
                            if (isUse == false && isCrePerson == false)
                            {
                                Btnbool = false;
                                BtnName = "此活動限制回答對象-您無權作答(B)";
                                BtnClass = BtnClass + " active disabled ";
                            }
                        }

                        if (isUse)
                        {
                            Pass = Edb.ADDT13.Where(a => a.DIALOG_ID == Main.DIALOG_ID && a.USER_NO == USER_NO && a.SCHOOL_NO == SchoolNO).Any();

                            if (Pass)
                            {
                                Btnbool = false;
                                BtnName = "您已通過";
                                BtnClass = BtnClass + " active disabled ";
                            }
                            else
                            {
                                if (Main.ANSWER_COUNT != null)
                                {
                                    var HisAns = Edb.ADDT13_HIS.Where(A => A.DIALOG_ID == Main.DIALOG_ID
                                     && A.SCHOOL_NO == SchoolNO
                                     && A.USER_NO == USER_NO).ToList();

                                    if (HisAns.Count() >= Main.ANSWER_COUNT)
                                    {
                                        Btnbool = false;
                                        BtnName = string.Format("此活動限制只能回答{0}次，您已超過此次數", Main.ANSWER_COUNT); ;
                                        BtnClass = BtnClass + " active disabled ";
                                    }
                                }
                            }
                        }
                    }
                }
            }

            ViewBag.Btnbool = Btnbool;
            ViewBag.BtnName = BtnName;
            ViewBag.BtnClass = BtnClass;
        }
    }
}