﻿@model global::ECOOL_APP.com.ecool.Models.DTO.ZZZI09_HISViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;
}
<br />

@Html.Partial("_Notice")
@*<img src="~/Content/img/web-bar2-revise-19.png" style="width:100%" class="img-responsive " alt="Responsive image" />*@
@*@using (Html.BeginForm("ExportResultView", "ZZZI091", FormMethod.Post, new { id = "formEdit", name = "formEdit", enctype = "multipart/form-data" }))
    {*@
@using (Html.BeginForm("Index3", "ZZZI09_HIS", FormMethod.Post, new { id = "formEdit", name = "formEdit", enctype = "multipart/form-data", @AutoComplete = "Off" }))
{
    @Html.Hidden("IDNO", "")


    <div class="Div-EZ-reader" id="tbData">
         <div class="panel-heading text-center">
       
    </div>
        <div class="form-horizontal">
            <div class="Details">
                <div class="dl-horizontal-EZ">
                    <samp class="dt">選擇匯出轉學或畢業生</samp>

                    <samp class="dd">
                        @Html.DropDownListFor(m => m.Where_EXPORTTYPE, (IEnumerable<SelectListItem>)ViewBag.ExportTypeItems, new { @class = "form-control", onchange = "$('#Where_SYEAR').val('');this.form.submit();" })
                        @Html.ValidationMessageFor(m => m.Where_EXPORTTYPE, "", new { @class = "text-danger" })
                    </samp>
                    <samp class="dt">學校</samp>

                    <samp class="dd">
                        @if (ViewBag.CanChangeSchool)
                        {
                            @Html.DropDownListFor(m => m.Where_SCHOOLNO_FORADMIN, (IEnumerable<SelectListItem>)ViewBag.AllSchoolItems, new { @class = "form-control", onchange = "$('#Where_USERNO').val('');this.form.submit();" })
                        }
                        else
                        {
                            @Html.DropDownListFor(m => m.Where_SCHOOLNO_FORADMIN, (IEnumerable<SelectListItem>)ViewBag.AllSchoolItems, new { @class = "form-control", onchange = "$('#Where_USERNO').val('');this.form.submit();", disabled = "disabled" })
                        }
                        @Html.ValidationMessageFor(m => m.Where_SCHOOLNO_FORADMIN, "", new { @class = "text-danger" })
                    </samp>
                    <samp class="dt">用名字搜尋</samp>
                    <samp class="dd">     @Html.EditorFor(m => m.SName, new { @class = "control-label col-md-3", @for = "SName" })</samp>
                    <input type="button" class="btn-yellow btn btn-sm" id="searchMain" value="搜尋" onclick="Details_show()" />
                </div>
            </div>
        </div>
    </div>
    <div class="table-responsive">
        <table class="table-ecool table-92Per table-hover">
            <thead>
                <tr>
                    <th>
                        姓名
                    </th>
                    <th>
                        學號
                    </th>
                    <th>
                        班級
                    </th>
                    <th>
                        座號
                    </th>
                    <th>學年</th>
                </tr>
            </thead>
            <tbody>
                @if (Model.VotePeople != null)
                {
                    foreach (var item in Model.VotePeople)
                    {

                        <tr>
                            <td>  @Html.DisplayFor(modelItem => item.SNAME)</td>
                            <td ><button type="button" id="btn" onclick="linktoDIF('@item.SCHOOL_NO','@item.USER_NO','@item.IDNO','@item.SYEAR','@item.CLASS_NO')"> @Html.DisplayFor(modelItem => item.USER_NO)</button></td>
                            <td> @Html.DisplayFor(modelItem => item.CLASS_NO)</td>
                            <td> @Html.DisplayFor(modelItem => item.SEAT_NO)</td>
                            <td > @Html.DisplayFor(modelItem => item.SYEAR)</td>
                        </tr>}
                }
            </tbody>
            </table></div>
            }

            @section scripts{
                <script>
                    var targetFormID = '#formEdit';
                            function Details_show() {

            $(targetFormID).attr("action", "@Url.Action("Index3", (string)ViewBag.BRE_NO)")
              $(targetFormID).submit();

                    }

                    function linktoDIF(SCHOOL_NO, USER_NO, IDNO, SYEAR, CLASS_NO) {
                        var COVERJPGValue = "";
                        COVERJPGValue = $('input[name*=COVERJPG1]:checked').val();
                         var file = $("#UploadCoverFile").val();
                        //var fileName = getFileName(file);
                        var data = {
                            "School_No": $('#Where_SCHOOLNO').val(),
                          


                        }
                              @*$.ajax({
                url: '@Url.Action("Index2", "ZZZI09_HIS")',
                data: formdata,
                processData: false,
                contentType: false,
                type: 'POST',
                async: false,
                dataType: 'json',               // xml/json/script/html
                cache: false,                   // 是否允許快取
                      success: function (data) {

                          var res = $.parseJSON(data);
                          console.log(res);
                      }

                  });*@

                        var Where_EXPORTTYPE = $("#Where_EXPORTTYPE").val();
                        var StrWhere = '?School_No=' + SCHOOL_NO + '&USER_NO=' + USER_NO + '&Where_USERNO=' + USER_NO+'&IDNO=' + IDNO + '&Where_EXPORTTYPE=' + Where_EXPORTTYPE + '&Where_SYEAR=' + SYEAR + '&CLASS_NO=' + CLASS_NO + '&Where_SCHOOLNO_FORADMIN=' + SCHOOL_NO + '&Where_CLASSNO=' + CLASS_NO
                //var StrWhere = '?School_No=' + $('#Where_SCHOOLNO').val() + '&USER_NO=' + $('#Where_USERNO').val() + '&IDNO=' + $('#IDNO').val()
                window.open('@Url.Action("Index2", "ZZZI09_HIS")' + StrWhere+'', '_blank');
                    }
        function CK() {
            var Msg = '';
            var COVERJPGValue = "";
            COVERJPGValue = $('input[name*=COVERJPG]:checked').val();
            var formdata = new FormData($('form').get(0));
            if ($('#Where_USERNO').val() == '' && $('#SName').val().length=='0') {
                Msg = Msg + '請選擇「' + $("label[for='Where_USERNO']").text() + '」\n';
            }

            if (Msg != '') {
                alert(Msg);
            }
            else {
                 var COVERJPGValue = "";
                COVERJPGValue = $('input[name*=COVERJPG1]:checked').val();
                var Where_SCHOOLNO = $('#Where_SCHOOLNO').val();
                var Where_USERNO = $('#Where_USERNO').val();
                var Where_CLASSNO = $('#Where_CLASSNO').val();

                $("#User_No").val(Where_USERNO);
                $("#School_No").val(Where_SCHOOLNO);
                $("#Class_NO").val(Where_CLASSNO);
                $("#COVERJPG").val(COVERJPGValue);
                var formdata = new FormData($('form').get(0));
                $('#BtnSave').attr('disabled', 'disabled').html("Loading...資料驗証中…請勿其他動作");
                  $.ajax({
                url: '@Url.Action("ExportResultView", "ZZZI091")',
                data: formdata,
                processData: false,
                contentType: false,
                type: 'POST',
                async: false,
                dataType: 'json',               // xml/json/script/html
                cache: false,                   // 是否允許快取
                      success: function (data) {

                          var res = $.parseJSON(data);
                          console.log(res);
                      }

                  });
                var file = $("#UploadCoverFile").val();
                var fileName = getFileName(file);
                var StrWhere = '?School_No=' + $('#Where_SCHOOLNO').val() + '&USER_NO=' + $('#Where_USERNO').val() + '&IDNO=' + $('#IDNO').val() + '&COVERJPG=' + COVERJPGValue + '&UploadCoverFileName=' + fileName + '&redirect=s' + '&ReadYN=' + $("#ReadYN").prop("checked")
                //var StrWhere = '?School_No=' + $('#Where_SCHOOLNO').val() + '&USER_NO=' + $('#Where_USERNO').val() + '&IDNO=' + $('#IDNO').val()
                window.open('@Url.Action("ExportResultView", "ZZZI091")' + StrWhere+'', '_blank');
            }
        }

        function getFileName(o) {
            var pos = o.lastIndexOf("\\");
            return o.substring(pos + 1);
        }
                </script>
            }
