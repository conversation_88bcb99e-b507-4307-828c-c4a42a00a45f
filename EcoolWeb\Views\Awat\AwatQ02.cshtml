﻿@model List<ECOOL_APP.EF.AWAT02>
@using System.Collections;
@using ECOOL_APP.com.ecool.util;
@{

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    string ButtonAlign = "text-right";
    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
        ButtonAlign = "text-left";
    }
    string SchoolNO = EcoolWeb.Models.UserProfileHelper.GetSchoolNo();
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    string unProduct = (Request["unProduct"] != null) ? Request["unProduct"] : "";

    if (string.IsNullOrWhiteSpace(unProduct) == false)
    {
        ViewBag.Title = "兌換獎品-兌換獎品一覽表(下架商品)";
    }
    else
    {
        ViewBag.Title = "兌換獎品-兌換獎品一覽表";
    }
}
<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>
@section Scripts
    {
    <script type="text/javascript">
        var width = jQuery(window).width();

     

        function ShowColorbox(ROWID) {
            $.colorbox({ html: data, width: "80%", height: "80%", opacity: 0.82 });
            $("a.gallery").colorbox({
                ROWID
                }
            });
        }

        function doSearch(ColName, whereValue) {
            $("#" + ColName).val(whereValue);
            document.form1.enctype = "multipart/form-data";
            document.form1.action = "AwatQ02";
            document.form1.submit();
        }

        function funGetModify(AWARD_NO, MODE, unProduct) {
            $('#hidAWARD_NO').val(AWARD_NO);
            $('#hidAWARD_STS').val('M');
            document.form1.enctype = "multipart/form-data";
            document.form1.action = "AwatMana02?MODE=" + MODE + "&unProduct=" + unProduct;
            document.form1.submit();
        }
        function funGetDelete(AWARD_NO) {
            $('#hidAWARD_NO').val(AWARD_NO);
            $('#hidAWARD_STS').val('D');
            document.form1.action = "AwatMana02?MODE=DEL";
            document.form1.submit();
        }
        function funGetExchange(AWARD_NO) {
            var btnId='#btn_'+AWARD_NO
            $('#hidAWARD_NO').val(AWARD_NO);
            document.form1.action = "AwatExchange02";
            document.form1.submit();
            $(btnId).text('我要兌換...讀取中...')
        }


        function todoClear() {
            ////重設

            $('#form1').find(":input,:selected").each(function (i) {

                var type = $(this).attr('type')
                var InPreadonly = $(this).attr('readonly')
                var tag = this.tagName.toLowerCase(); // normalize case

                if (InPreadonly == false || InPreadonly == undefined) {

                    if (type == 'radio' || type == 'checkbox') {
                        if ($(this).attr("title") == 'Default') {
                            this.checked = true;
                        }
                        else {
                            this.checked = false;
                        }
                    }
                    else if (tag == 'select') { //下拉式選單
                        this.selectedIndex = 0;
                    }
                    else if (type == 'text' || type == 'hidden' || type == 'password' || type == 'textarea') {
                        this.value = '';
                    }
                }
            });

            $('#form1').submit();
        }
    </script>
}





    



@helper btnFun(string SchoolNO)
{
    string AWARD_SCHOOL_NO_EMPTY = (string.IsNullOrWhiteSpace(ViewBag.whereAWARD_SCHOOL_NO)) ? "active" : "";
    string AWARD_SCHOOL_NO_ALL = (ViewBag.whereAWARD_SCHOOL_NO == "ALL") ? "active" : "";
    string AWARD_SCHOOL_NO_Me = (ViewBag.whereAWARD_SCHOOL_NO == SchoolNO && string.IsNullOrWhiteSpace(ViewBag.whereAWARD_SCHOOL_NO) == false) ? "active" : "";


    string AWARD_TYPE_All = (string.IsNullOrWhiteSpace(ViewBag.AWARD_TYPE)) ? "active" : "";
    string AWARD_TYPE_A = (ViewBag.AWARD_TYPE == "A") ? "active" : "";
    string AWARD_TYPE_T = (ViewBag.AWARD_TYPE == "T") ? "active" : "";
    string AWARD_TYPE_S = (ViewBag.AWARD_TYPE == "S") ? "active" : "";
    string AWARD_TYPE_PC = (ViewBag.AWARD_TYPE == "P,C") ? "active" : "";

    <br />
    <div class="row">
        <div class="col-md-6 col-xs-12 text-left">
            <samp>獎品類別：</samp>
            <button class="btn btn-xs btn-pink  @AWARD_SCHOOL_NO_EMPTY" type="button" onclick="doSearch('whereAWARD_SCHOOL_NO', '');">全部</button>
            <button class="btn btn-xs btn-pink  @AWARD_SCHOOL_NO_ALL" type="button" onclick="doSearch('whereAWARD_SCHOOL_NO', 'ALL');">總召學校獎品</button>
            <button class="btn btn-xs btn-pink  @AWARD_SCHOOL_NO_Me" type="button" onclick="doSearch('whereAWARD_SCHOOL_NO', '@SchoolNO');">本校獎品</button>
            @Html.Hidden("whereAWARD_SCHOOL_NO", (string)ViewBag.whereAWARD_SCHOOL_NO)
        </div>
        <div class="col-md-6 col-xs-12 text-left">
            <samp>獎品狀態：</samp>
            <button class="btn btn-xs btn-pink  @AWARD_TYPE_All" type="button" onclick="doSearch('AWARD_TYPE', '')">全部</button>
            <button class="btn btn-xs btn-pink  @AWARD_TYPE_A" type="button" onclick="doSearch('AWARD_TYPE', 'A')">活動</button>
            <button class="btn btn-xs btn-pink  @AWARD_TYPE_T" type="button" onclick="doSearch('AWARD_TYPE', 'T')">票券</button>
            <button class="btn btn-xs btn-pink  @AWARD_TYPE_S" type="button" onclick="doSearch('AWARD_TYPE', 'S')">實體</button>
            <button class="btn btn-xs btn-pink  @AWARD_TYPE_PC" type="button" onclick="doSearch('AWARD_TYPE', 'P,C')">募資</button>

            @Html.Hidden("AWARD_TYPE", (string)ViewBag.AWARD_TYPE)
        </div>
    </div>
    <br />
}



@if (AppMode == false)
{
    @Html.Partial("_Title_Secondary")
}
@Html.Partial("_Notice")

@if (AppMode == false)
{
    @Html.ActionLink("酷幣點數排行榜", "QUERY", "AWA003", new { Awat = "Awat" }, new { @class="btn btn-sm btn-sys" })
    @Html.ActionLink("兌獎名單","QUERY", "AWA004", new { Awat = "Awat" }, new { @class = "btn btn-sm btn-sys" })
}

@if (user != null)
{
    if (user.USER_TYPE == UserType.Student)
    {
        @Html.ActionLink("我的獎品紀錄", "QUERY", "AWA004", new { Awat = "Awat", whereUserNo= user.USER_NO }, new { @class = "btn btn-sm btn-sys" })
    }
    else if (user.USER_TYPE == UserType.Parents)
    {
        @Html.ActionLink("寶貝獎品紀錄", "QUERY", "AWA004", new { Awat = "Awat", whereUserNo = HRMT06.GetStringMyPanyStudent(user) }, new { @class = "btn btn-sm btn-sys" })
    }
}


@if (user != null)
{
    if (ViewBag.VisableModify == true)
    {
        if (string.IsNullOrWhiteSpace(unProduct))
        {
            @Html.ActionLink("搜尋已下架商品", "AwatQ02", "Awat", new { unProduct = "unProduct" }, new { @class = "btn btn-sm btn-sys" }) 
        }
        else
        {
            @Html.ActionLink("搜尋上架中商品", "AwatQ02", "Awat", new { unProduct = "" }, new { @class = "btn btn-sm btn-sys" })
        }

    }
}

@if (ViewBag.VisableInsert == true && AppMode==false)
{
    <a class="btn btn-sm btn-sys" href='@Url.Action("Awat02", "Awat", new { MODE = "ADD" })'>
        新增獎品
    </a>
}

@if (AppMode == false)
{
    <br />
    <label class="label_dt"><img src='~/Content/img/web-revise-prize-03s.png' style="max-height:30px;margin-right:5px">   此獎品圖示：代表總召學校獎品</label>
    <br />
    <label class="label_dt"><img src='~/Content/img/icons-07.png' style="max-height:30px;margin-right:5px">   此獎品圖示：代表熱門獎品</label>
    <br />
    <label class="label_dt"><img src='~/Content/img/web-student-prize-05.png' style="max-height:30px;margin-right:5px">   此獎品圖示：新上架獎品</label>
    <br />
    <label class="label_dt"><img src="~/Content/img/web-revise-secretary-22.png" style="max-height:30px;margin-right:5px">   此獎品圖示：擁有 閱讀認證滿 1-10級，才可兌換該獎品</label>
    <br />
    <label class="label_dt"> <img src="~/Content/img/web-revise-secretary-38.png" style="max-height:30px;margin-right:5px">   此獎品圖示：擁有 閱讀護照滿 1-6級，才可兌換該獎品</label>
    <br />
}
    
<div style="width:95%">
    @if (user != null && AppMode==false)
    {
        //取得兌換獎品學生投稿說明檔
        if (user.USER_TYPE == "S")
        {
            string Explain = ViewBag.AwatQ02SEXPLAIN;
            @Html.Raw(HttpUtility.HtmlDecode(@Explain))
        }
        else if (user.USER_TYPE == "T")
        {
            string Explain = ViewBag.AwatQ02SEXPLAIN;
            @Html.Raw(HttpUtility.HtmlDecode(@Explain))
        }
        <br />
    }
</div>

<form action="#" name="form1" id="form1" method="post">
    <div class="form-inline" role="form">
        <div class="form-group">
            <label class="control-label">品名</label>
        </div>
        <div class="form-group">
            @Html.Editor("whereKeyword", (string)ViewBag.whereKeyword, new { htmlAttributes = new { @class = "form-control input-sm" } })
        </div>
        <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="form1.submit();" />
        <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />
    </div>



    @btnFun(SchoolNO)
    <img src="~/Content/img/web-bar2-revise-09.png" style="width:100%" class="img-responsive App_hide" alt="Responsive image" />
    <div class="Div-EZ-Awat">
        @if (AppMode)
        {
            <div style="height:5px"></div>
            <br />
        }
        else
        {
            <div style="height:35px"></div>
        }
        
        <div class="p-context">

            @if (Model.Count == 0)
            {
                <div class="text-center">
                    <label class="label_dd_font18">無任何商品</label>
                </div>
            }
           
            <div class="row">
                     @{
                         string divrowSt = "<div class='row-hidden'>";
                         string divrowEd = "</div> <!--row-hidden End-->";
                         int num = 0;

                         foreach (AWAT02 award in Model)
                         {

                             string aImgUrl = ViewBag.ImgUrl + award.SCHOOL_NO + @"/" + award.IMG_FILE;
                             num++;

                             if (num % 2 !=0)
                             {
                                 @Html.Raw(HttpUtility.HtmlDecode(divrowSt))
                             }



                            <div class="col-md-6 col-sm-6 col-xs-12 col-height">
                                <div class="box">
                                    <div>
                                        <img src='@aImgUrl' title="@award.AWARD_NAME" href="@aImgUrl" onclick="ShowColorbox('@aImgUrl')">
                                    </div>
                                </div>
                                <div class="prod-caption">
                                    <div class="form-group">
                                        <label class="label_dd_font18" title="@award.AWARD_NAME">@StringHelper.LeftStringR(award.AWARD_NAME, 13)</label>
                                    </div>
                                    <div class="form-group">
                                        <div class="prod-text">
                                            兌換點數：@award.COST_CASH
                                        </div>

                                        @if (award.AWARD_TYPE == "P" || award.AWARD_TYPE == "C")
                                        {
                                            <div class="prod-text">已募資點數：@(award.COST_CASH * award.QTY_TRANS)</div>


                                        }
                                        else
                                        {
                                            <div class="prod-text">
                                                剩餘數量：@award.QTY_STORAGE
                                            </div>
                                        }
                                        <br />
                                        開始日期：@award.SDATETIME.Value.ToShortDateString()<br />
                                        兌換期限：@award.EDATETIME.Value.ToShortDateString()<br />
                                        @if (award.SHOW_DESCRIPTION_YN == "Y")
                                        {
                                            @award.DESCRIPTION
                                        }
                                    </div>
                                    <div class="prod-icon">
                                        @if (award.SCHOOL_NO == "ALL")
                                        {
                                            <img src='~/Content/img/web-revise-prize-03s.png' style="max-height:30px;" />
                                        }

                                        @if (award.HOT_YN == "Y")
                                        {
                                            <img src='~/Content/img/icons-07.png' style="height:30px;width:30px;max-height:30px;max-width:30px">
                                        }

                                        @if (Convert.ToDateTime(award.SDATETIME).AddMonths(+1) >= DateTime.Now)
                                        {
                                            <img src='~/Content/img/web-student-prize-05.png' style="height:30px;width:30px;max-height:30px;max-width:30px" />
                                        }

                                        @if (award.READ_LEVEL != null)
                                        {
                                            <img src="@Url.Content(EcoolWeb.Models.UserProfileHelper.GetImgReadUrl(award.READ_LEVEL))" style="max-height:30px;margin-right:5px">
                                        }

                                        @if (award.PASSPORT_LEVEL != null)
                                        {
                                            <img src="@Url.Content(EcoolWeb.Models.UserProfileHelper.GetImgPassportUrl(award.PASSPORT_LEVEL))" style="max-height:30px;margin-right:5px">
                                        }

                                        @if (null != user && user.USER_TYPE == "S")
                                                    {
                                                        string NG = string.Empty; //EcoolWeb.Controllers.AwatController.CheckCxchange(award, user);


                                                        if (string.IsNullOrWhiteSpace(NG) == false)
                                                        {

                                                <button role="button" class="btn-default btn btn-xs btn-prod" title="@Html.Raw(StringHelper.UnHtml(HttpUtility.HtmlDecode(NG)))">
                                                    無法兌換
                                                </button>
                                            }
                                            else
                                            {
                                                string BtnId = "btn_" + award.AWARD_NO;

                                            @Html.ActionLink("我要兌換", "AwatExchange02", "Awat", new { hidAWARD_NO = award.AWARD_NO }, new { @class = "btn-default btn btn-xs btn-prod" })
                                            }
                                        }

                                        <a class="btn-default btn btn-xs btn-prod" href='@Url.Action("Query", "AWA004", new { whereAWARD_NO = award.AWARD_NO, Awat = "Awat_Key" })'>兌獎名單</a>

                                        @if (ViewBag.VisableModify == true || ViewBag.VisableDelete == true)
                                        {


                                            if (ViewBag.VisableModify == true)
                                            {
                                                if (((award.SCHOOL_NO != "ALL") || (award.SCHOOL_NO == "ALL" && ECOOL_APP.EF.HRMT24_ENUM.QOutSchoolList.Contains((byte)user.ROLE_TYPE))))
                                                {
                                                    <a class="btn-default btn btn-xs btn-prod" style="" role="button" onclick="funGetModify(@award.AWARD_NO,'EDIT','@unProduct')">修改</a>
                                                }
                                            }
                                            if (ViewBag.VisableDelete == true)
                                            {
                                                if (unProduct == "" && ((award.SCHOOL_NO != "ALL") || (award.SCHOOL_NO == "ALL" && ECOOL_APP.EF.HRMT24_ENUM.QOutSchoolList.Contains((byte)user.ROLE_TYPE))))
                                                {
                                                    <a class="btn-default btn btn-xs btn-prod" role="button" onclick="funGetDelete(@award.AWARD_NO)">下架/刪除</a>
                                                }
                                            }

                                        }
                                    </div>
                                </div>

                            </div>





                             if (num % 2 == 0)
                             {
                                @Html.Raw(HttpUtility.HtmlDecode(divrowEd))
                             }
                         }

                         if (num % 2 != 0)
                         {
                            @Html.Raw(HttpUtility.HtmlDecode(divrowEd))
                         }
                    }
            </div><!--/row-->

            

        </div>
    </div>
    <input type="hidden" id="hidunProduct" name="unProduct" value="@unProduct" />
    <input type="hidden" id="hidAWARD_NO" name="hidAWARD_NO" />
    <input type="hidden" id="hidAWARD_STS" name="hidAWARD_STS" />
</form>



<script>
    $(document).ready(function () {
        // $(".box img").colorbox({ opacity: 0.82 });
        $('div.redeem-box>img').each(function () {
            // Iterate through individual images...
            // because we need access to their 'src' attribute
            $(this).colorbox({
                rel: 'images',
                transition: "fade",
                opacity: 0.5,
                rel: 'group1',
                href: $(this).attr('src')
            });
        });

    });

</script>