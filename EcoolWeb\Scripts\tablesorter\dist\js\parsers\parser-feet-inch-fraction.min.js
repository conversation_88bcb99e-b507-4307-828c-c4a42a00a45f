(function(factory){if (typeof define === 'function' && define.amd){define(['jquery'], factory);} else if (typeof module === 'object' && typeof module.exports === 'object'){module.exports = factory(require('jquery'));} else {factory(jQuery);}}(function(jQuery){

/*! Parser: distance */
!function(a){"use strict";var n=a.tablesorter;n.symbolRegex=/[\u215b\u215c\u215d\u215e\u00bc\u00bd\u00be]/g,n.processFractions=function(t,e){if(t){var r,s=0;t=a.trim(t.replace(/\"/,"")),/\s/.test(t)&&(s=n.formatFloat(t.split(" ")[0],e),t=a.trim(t.substring(t.indexOf(" "),t.length))),/\//g.test(t)?(r=t.split("/"),t=s+parseInt(r[0],10)/parseInt(r[1]||1,10)):n.symbolRegex.test(t)&&(t=s+t.replace(n.symbolRegex,function(t){return{"⅛":".125","⅜":".375","⅝":".625","⅞":".875","¼":".25","½":".5","¾":".75"}[t]}))}return t||0},a.tablesorter.addParser({id:"distance",is:function(){return!1},format:function(t,e){if(""===t)return"";var r=/^\s*\S*(\s+\S+)?\s*\'/.test(t)?t.split(/\'/):[0,t],s=n.processFractions(r[0],e),a=n.processFractions(r[1],e);return/[\'\"]/.test(t)?parseFloat(s)+(parseFloat(a)/12||0):parseFloat(s)+parseFloat(a)},type:"numeric"})}(jQuery);return jQuery;}));
