﻿@model GameBuskerManagerViewModel
@{
    ViewBag.Title = ViewBag.Panel_Title;
    Layout = "~/Views/Shared/_LayoutGame.cshtml";
    EcoolWeb.Models.UserProfileHelper.SetLayoutCookieData(Layout);

    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@{
    if (!string.IsNullOrWhiteSpace(Model?.Search?.WhereGAME_NO))
    {
        Html.RenderAction("_ByOneGameMenu", new { NowAction = "BuskerManager" });
    }
}

@if (user?.RoleID_Default == HRMT24_ENUM.SuperAdminROLE)
{
    <div class="row">
        <div class="col-sm-offset-3 col-sm-9">

            <div class="form-group" style="padding-left:50px">
                <button type="button" class="btn btn-sm btn-sys-busker  active" onclick="OnBuskerManager()"> <i class="fa fa-edit"></i> 街頭藝人管理</button>
                <button type="button" class="btn btn-sm btn-sys-busker" onclick="OnBuskerAddView()"> <i class="fa fa-edit"></i> 街頭藝人現場報名</button>
                <button type="button" class="btn btn-sm btn-sys-busker" onclick="OnBuskerLikeView()"> <i class="fa fa-thumbs-o-up"></i> 街頭藝人打賞畫面</button>
                <button type="button" class="btn btn-sm btn-sys-busker" onclick="OnBuskerGuestLikeView()"> <i class="fa fa-thumbs-o-up"></i> 家長打賞網址</button>
            </div>
        </div>
    </div>

}

@using (Html.BeginForm("BuskerManager", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1", enctype = "multipart/form-data" }))
{
    @Html.AntiForgeryToken()
    <div id="PageContent">
        @Html.Action("_BuskerPageContent", (string)ViewBag.BRE_NO)
    </div>
    <div class="text-center">
        <font color="red">*注意，當已給隊伍點數時，要刪除隊伍名單時，請先把給於點數設成0</font>
    </div>

    @Html.HiddenFor(m => m.ThisEdit.TITLE_SHOW_ID)
    @Html.HiddenFor(m => m.ThisEdit.TITLE_SHOW_NAME)
    @Html.HiddenFor(m => m.ThisEdit.ORDER_BY)
    <!-- Modal -->
    <div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="myModalLabel">給於此街頭藝人小組活動酷幣點數</h4>
                </div>
                <div class="modal-body">
                    @Html.EditorFor(m => m.ThisEdit.CASH, new { htmlAttributes = new { @class = "form-control", @placeholder = "活動點數" } })
                    <br /><font color="red"> *當已給過點數，會先扣回上次給的點數，再給這次修改的點數</font>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" onclick="OnCashData()">確定</button>
                    <button type="button" class="btn btn-default" data-dismiss="modal" id="close">關閉</button>
                </div>
            </div>
        </div>
    </div>
}

<div class="row">
    <div class="col-lg-6 col-lg-offset-3">
        <div class="use-absolute" id="ErrorDiv">
            <div class="use-absoluteDiv">
                <div class="alert alert-danger" role="alert">
                    <h1>
                        <i class="fa fa-exclamation-circle"></i>
                        <strong id="ErrorStr"></strong>
                    </h1>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="form-group">
    <div class="col-xs-12 text-center">
        <div class="form-inline">
            <button class="btn btn-default" type="button" onclick="onBack()">回上一頁</button>
        </div>
    </div>
</div>

@section Scripts {
    <script language="JavaScript">
    var targetFormID = '#form1'

        function OnBuskerManager() {
            $(targetFormID).attr("action", "@Url.Action("BuskerManager", (string)ViewBag.BRE_NO)")
            $(targetFormID).attr("target", "_self")
            $(targetFormID).submit();
        }

        function OnBuskerAddView() {

            var NewUrl ="@Url.Action("BuskerAddView", (string)ViewBag.BRE_NO)?GAME_NO=@Model.Search.WhereGAME_NO"

            $(targetFormID).attr("action", NewUrl)
            $(targetFormID).attr("target", "_blank")
            $(targetFormID).submit();
            $(targetFormID).attr("target", "_self")
        }

        function OnBuskerLikeView() {
             var NewUrl ="@Url.Action("BuskerLikeView", (string)ViewBag.BRE_NO)?GAME_NO=@Model.Search.WhereGAME_NO"

            $(targetFormID).attr("action", NewUrl)
            $(targetFormID).attr("target", "_blank")
            $(targetFormID).submit();
            $(targetFormID).attr("target", "_self")
        }

        function OnBuskerGuestLikeView() {
             var NewUrl ="@Url.Action("BuskerGuestLikeView", (string)ViewBag.BRE_NO)?GAME_NO=@Model.Search.WhereGAME_NO"

            $(targetFormID).attr("action", NewUrl)
            $(targetFormID).attr("target", "_blank")
            $(targetFormID).submit();
            $(targetFormID).attr("target", "_self")
        }

        function onBuskerEdit(Val) {
            $('#WhereTITLE_SHOW_ID').val(Val);
            $(targetFormID).attr("action", "@Url.Action("BuskerEdit", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        function onBack() {
            $(targetFormID).attr("action", "@Url.Action("GameIndex", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        function OnCashData() {

            var CASH = $('#@Html.IdFor(m=>m.ThisEdit.CASH)').val();

            if (CASH=='') {
                alert('請輸入點數')
                return false;
            }
            else if ($.isNumeric(CASH) == false || CASH < 0 ) {
                alert('點數請輸入大於0的數字')
                return false;
            }

            $("#close").click()

             $.ajax({
                url: "@(Url.Action("BuskerCashSave", "Game"))",     // url位置
                type: 'post',                   // post/get
                data: {
                    GAME_NO: $('#@Html.IdFor(m=>m.Search.WhereGAME_NO)').val(),
                    TITLE_SHOW_ID: $('#@Html.IdFor(m=>m.ThisEdit.TITLE_SHOW_ID)').val() ,
                    CASH: CASH,
                },     // data
                dataType: 'json',               // xml/json/script/html
                cache: false,                   // 是否允許快取
                success: function (data) {
                    var res = jQuery.parseJSON(data);

                    if (res.Success == 'false') {
                        $('#ErrorStr').html(res.Error)
                        $('#ErrorDiv').show()
                        setTimeout(function () { $('#ErrorDiv').fadeOut(2000) }, 3000);
                    }
                    else {
                        $('#ErrorStr').html('異動完成')
                        $('#ErrorDiv').show()
                        FunPageProc(1)
                        setTimeout(function () {
                            $('#ErrorDiv').fadeOut(1500)
                        }, 2000);
                    }
                },
                error: function (xhr, err) {
                    alert("readyState: " + xhr.readyState + "\nstatus: " + xhr.status);
                    alert("responseText: " + xhr.responseText);
                }
            });

        }

        function OnShowCash(TITLE_SHOW_ID,Like_Count) {
            $('#@Html.IdFor(m=>m.ThisEdit.TITLE_SHOW_ID)').val(TITLE_SHOW_ID)
            $('#@Html.IdFor(m=>m.ThisEdit.CASH)').val(Like_Count)
            $('#myModal').modal('show');
        }

        function OnSave(TITLE_SHOW_ID, Type) {
            var item_TITLE_SHOW_ID, item_TITLE_SHOW_NAME, item_ORDER_BY

            $('#Tr' + TITLE_SHOW_ID + ' :input').not(':button').each(function (index) {
                console.log(index + ': ' + $(this).attr("id"));
                if ($(this).attr("id") == 'item_TITLE_SHOW_ID') {
                    item_TITLE_SHOW_ID = $(this).val()
                }
                else if ($(this).attr("id") == 'item_TITLE_SHOW_NAME')
                {
                    item_TITLE_SHOW_NAME = $(this).val()
                }
                else if ($(this).attr("id") == 'item_ORDER_BY') {
                    item_ORDER_BY = $(this).val()
                }
            });

            if (Type == 'Save') {
                AjaxSave(item_TITLE_SHOW_ID, item_TITLE_SHOW_NAME, item_ORDER_BY, false, false, false)
            }
            else if (Type == 'Live')
            {
                AjaxSave(item_TITLE_SHOW_ID, item_TITLE_SHOW_NAME, item_ORDER_BY, true, false, false)
            }
            else if (Type == 'UnLive') {
                AjaxSave(item_TITLE_SHOW_ID, item_TITLE_SHOW_NAME, item_ORDER_BY, false, false, true)
            }
            else if (Type == 'Delete') {
                AjaxSave(item_TITLE_SHOW_ID, item_TITLE_SHOW_NAME, item_ORDER_BY, false, true, false)
            }
        }

        function AjaxSave(TITLE_SHOW_ID, TITLE_SHOW_NAME, ORDER_BY, IsLive, IsDelete, IsUnLive) {
             //LIVE_STREAM
            $.ajax({
                url: "@(Url.Action("BuskerManagerSave", "Game"))",     // url位置
                type: 'post',                   // post/get
                data: {
                    GAME_NO: $('#@Html.IdFor(m=>m.Search.WhereGAME_NO)').val(),
                    TITLE_SHOW_ID: TITLE_SHOW_ID ,
                    TITLE_SHOW_NAME: TITLE_SHOW_NAME,
                    ORDER_BY: ORDER_BY,
                    IsLive: IsLive,
                    IsDelete: IsDelete,
                    IsUnLive: IsUnLive,
                },     // data
                dataType: 'json',               // xml/json/script/html
                cache: false,                   // 是否允許快取
                success: function (data) {
                    var res = jQuery.parseJSON(data);

                    if (res.Success == 'false') {
                        $('#ErrorStr').html(res.Error)
                        $('#ErrorDiv').show()
                        setTimeout(function () { $('#ErrorDiv').fadeOut(2000) }, 3000);
                    }
                    else {
                        $('#ErrorStr').html('異動完成')
                        $('#ErrorDiv').show()
                        FunPageProc(1)
                        setTimeout(function () {
                            $('#ErrorDiv').fadeOut(1500)
                        }, 2000);

                    }

                },
                error: function (xhr, err) {
                    alert("readyState: " + xhr.readyState + "\nstatus: " + xhr.status);
                    alert("responseText: " + xhr.responseText);
                }
            });
        }

        //分頁
         function FunPageProc(page) {
            if ($(targetFormID).size() > 0) {
                $('#@Html.IdFor(m=>m.Search.Page)').val(page)
                funAjax()
            }
        };

         //查詢
        function funAjax() {

            $.ajax({
                url: '@Url.Action("_BuskerPageContent", (string)ViewBag.BRE_NO)',
                data: $(targetFormID).serialize(),
                type: 'post',
                async: false,
                cache: false,
                dataType: 'html',
                success: function (data) {
                    $('#PageContent').html(data);
                }
            });
        }

        function todoClear() {
            ////重設

            $('#Q_Div').find(":input,:selected").each(function (i) {

                var type = $(this).attr('type')
                var InPreadonly = $(this).attr('readonly')
                var tag = this.tagName.toLowerCase(); // normalize case

                if (InPreadonly == false || InPreadonly == undefined) {

                    if (type == 'radio' || type == 'checkbox') {
                        if ($(this).attr("title") == 'Default') {
                            this.checked = true;
                        }
                        else {
                            this.checked = false;
                        }
                    }
                    else if (tag == 'select') { //下拉式選單
                        this.selectedIndex = 0;
                    }
                    else if (type == 'text' || type == 'hidden' || type == 'password' || type == 'textarea') {
                        this.value = '';
                    }
                }
            });

            $(targetFormID).submit();
        }
    </script>
}