(function(factory){if (typeof define === 'function' && define.amd){define(['jquery'], factory);} else if (typeof module === 'object' && typeof module.exports === 'object'){module.exports = factory(require('jquery'));} else {factory(jQuery);}}(function(jQuery){

/*! Widget: alignChar - updated 2/7/2015 (v2.19.0) */
!function(_){"use strict";var e=_.tablesorter;e.alignChar={init:function(i,t,r){t.$headers.filter("["+r.alignChar_charAttrib+"]").each(function(){var n=_(this),a={column:this.column,align:n.attr(r.alignChar_charAttrib),alignIndex:parseInt(n.attr(r.alignChar_indexAttrib)||0,10),adjust:parseFloat(n.attr(r.alignChar_adjustAttrib))||0};a.regex=new RegExp("\\"+a.align,"g"),void 0!==a.align&&(r.alignChar_savedVars[this.column]=a,e.alignChar.setup(i,t,r,a))})},setup:function(n,a,i,t){if(!_.isEmptyObject(a.cache)){var r,e,l,h,g,o,s,d,c,u,f,m,p,C,w=[],b=[];for(r=0;r<a.$tbodies.length;r++)for(u=(s=a.cache[r]).normalized.length,e=0;e<u;e++){if(0<(c=((d=(C=s.row?s.row[e]:s.normalized[e][a.columns].$row).find("td").eq(t.column).text().replace(/[ ]/g," ")).match(t.regex)||[]).length)&&0<t.alignIndex)for(h=Math.min(t.alignIndex,c),g=o=l=0;l++<h;)o=(g=d.indexOf(t.align,g+1))<0?o:g;else o=d.indexOf(t.align);0<=o?(w.push(d.substring(0,o)||""),b.push(d.substring(o,d.length)||"")):(w.push(1<=c&&t.alignIndex>=c?"":d||""),b.push(1<=c&&t.alignIndex>=c&&d||""))}for(f=_.extend([],w).sort(function(n,a){return a.length-n.length})[0],m=_.extend([],b).sort(function(n,a){return a.length-n.length})[0],t.width=t.width||Math.floor(f.length/(f.length+m.length)*100)+t.adjust,f="min-width:"+t.width+"%",m="min-width:"+(100-t.width)+"%",r=0;r<a.$tbodies.length;r++)for(u=(s=a.cache[r]).normalized.length,e=0;e<u;e++)p=_(i.alignChar_wrap).length?_(i.alignChar_wrap).html(t.align)[0].outerHTML:t.align,C=s.row?s.row[e]:s.normalized[e][a.columns].$row,g=b[e].slice(t.align.length),C.find("td").eq(t.column).html('<span class="ts-align-wrap"><span class="ts-align-left" style="'+f+'">'+w[e]+'</span><span class="ts-align-right" style="'+m+'">'+(g.length?p+g:"")+"</span></span>");i.alignChar_initialized=!0}},remove:function(n,a,i){var t,r,e,l,h;if(!_.isEmptyObject(a.cache))for(t=0;t<a.$tbodies.length;t++)for(e=(l=a.cache[t]).normalized.length,r=0;r<e;r++)(h=(l.row?l.row[r]:l.normalized[r][a.columns].$row).find("td").eq(i)).html(h.text().replace(/\s/g," "))}},e.addWidget({id:"alignChar",priority:100,options:{alignChar_wrap:"",alignChar_charAttrib:"data-align-char",alignChar_indexAttrib:"data-align-index",alignChar_adjustAttrib:"data-align-adjust"},init:function(n,a,i,t){t.alignChar_initialized=!1,t.alignChar_savedVars=[],e.alignChar.init(n,i,t),i.$table.on("pagerEnd refreshAlign",function(){i.$headers.filter("["+t.alignChar_charAttrib+"]").each(function(){e.alignChar.remove(n,i,this.column)}),e.alignChar.init(n,i,t)})},format:function(n,a,i){i.alignChar_initialized||a.$table.triggerHandler("refreshAlign")},remove:function(n,a,i,t){t||(a.$headers.filter("["+i.alignChar_charAttrib+"]").each(function(){e.alignChar.remove(n,a,this.column)}),i.alignChar_initialized=!1)}})}(jQuery);return jQuery;}));
