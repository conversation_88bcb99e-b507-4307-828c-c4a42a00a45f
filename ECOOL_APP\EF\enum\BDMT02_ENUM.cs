﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    /// <summary>
    /// BDMT02 欄位參數
    /// </summary>
    public class BDMT02_ENUM
    {




        /// <summary>
        /// 類別
        /// </summary>
        public static class DataType
        {
            /// <summary>
            /// 參數設定的btn =>大標題
            /// </summary>
            public static string DataTypeMenu = "ALL";


        }


        /// <summary>
        /// 模式
        /// </summary>
        public static class AddMode
        {

            /// <summary>
            /// M=>由 User 自由 新增 筆數 ,ex 退回修改原因選單 內容
            /// </summary>
            public static string Multi = "M";

            /// <summary>
            /// O=>固定模式 讀取 BDMT02 內容去設定 1筆設定 設定1個
            /// </summary>
            public static string Fixed = "O";
            public static string Check = "C";
        }

        public static class Mode
        {

            /// <summary>
            /// 字串
            /// </summary>
            public static string STRING = "STRING";

            /// <summary>
            /// 整數
            /// </summary>
            public static string INT = "INT";


            public static string GetInputType(string Val)
            {

                if (Val== STRING)
                {
                    return "text";
                }
                else if (Val == INT)
                {
                    return "number";
                }
                else
                {
                    return "text";
                }
            }

        }




    }
}
