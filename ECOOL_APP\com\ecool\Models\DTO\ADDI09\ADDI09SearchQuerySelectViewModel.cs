﻿using ECOOL_APP.EF;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.com.ecool.Models.DTO
{
    public class ADDI09SearchQuerySelectViewModel
    {
        /// <summary>
        /// 排序欄位
        /// </summary>
        public string OrderByName { get; set; }

        /// <summary>
        /// Desc or ASC
        /// </summary>
        public string SyntaxName { get; set; }

        /// <summary>
        /// 頁次
        /// </summary>
        public int Page { get; set; }

        [DisplayName("學號")]
        public string USER_NO { get; set; }

        [DisplayName("班級")]
        public string CLASS_NO { get; set; }

        [DisplayName("年級")]
        public string SYEAR { get; set; }

        [DisplayName("姓名")]
        public string NAME { get; set; }

        public ADDI09SearchQuerySelectViewModel()
        {
            Page = 1;
            OrderByName = "";
            SyntaxName = "ASC";
        }
    }
}
