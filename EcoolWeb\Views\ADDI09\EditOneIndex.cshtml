﻿@{
    ViewBag.Title = ViewBag.Panel_Title;

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }

    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    string ErrMsg;
    short ThisMonthCash;
    string LimitShow = string.Empty;
    ECOOL_DEVEntities db = null;
    int CashLimit = 0;
    CashLimit = ECOOL_APP.UserProfile.
         GetCashLimit(user.SCHOOL_NO, user.USER_NO, user.USER_TYPE, ref db, out ThisMonthCash, out ErrMsg);
    if (CashLimit != short.MaxValue && CashLimit != 0)
    {
        LimitShow = "您本月有" + CashLimit + "點酷幣可以發給特殊表現的孩子，你已經發放了" + ThisMonthCash.ToString() + "點。";
    }

}
@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")
<div class="form-group ">
    <label class="text-danger col-md-12">
        @LimitShow
    </label>
</div>
@using (Html.BeginForm((string)ViewBag.Action, (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", enctype = "multipart/form-data" }))
{
    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })

    <img src="~/Content/img/web-bar2-revise-2200R.png" style="width:100%" class="img-responsive " alt="Responsive image" />
    <div class="Div-EZ-ADDI09">
        <div class="form-horizontal">
            <label class="control-label">@ViewBag.Panel_Title</label>
            <br /><br />
                  <div class="form-group text-center">

                      <div class="col-xs-6">
                          @*@Html.ActionLink("特殊加點-固定", "EditOne", new { IsFix = "True" }, new { @class = "btn-block btn btn-warning", @role = "button" })*@
                          <a class="btn-block btn btn-warning" href="@Url.Action("EditOne", "ADDI09",new{IsFix="True"})" role="button">特殊加點-固定</a>
                          <br />


                          @*@Html.ActionLink("特殊加點-隨機(大點)", "EditOne", new { IsRandom = "True", IsRandomHighPoint = "True" }, new { @class = "btn-block btn btn-warning", @role = "button" })*@
                          <a class="btn-block btn btn-warning" href="@Url.Action("EditOne", "ADDI09", new { IsRandom = "True", IsRandomHighPoint = "True" })" role="button">特殊加點<br class="hidden-md hidden-sm hidden-lg" />-隨機(大點)</a>
                          <br />
                          <a class="btn-block btn btn-warning" href="@Url.Action("EditOne", "ADDI09", new { IsRandom = "True" })" role="button">特殊加點<br class="hidden-md hidden-sm hidden-lg" />-隨機(小點)</a>
                          @*@Html.ActionLink("特殊加點-隨機(小點)", "EditOne", new { IsRandom = "True" }, new { @class = "btn-block btn btn-warning", @role = "button" })*@
                      </div>
                     
                      <div class="col-xs-6">
                          @*@Html.ActionLink("校內表現-感應", "EditOne14", null, new { @class = "btn-block btn btn-default", @role = "button" })*@
                          <a class="btn-block btn btn-default" role="button" href="@Url.Action("EditOne14", "ADDI09")">校內表現<br class=" hidden-md hidden-sm hidden-lg" />-感應</a>
                          <input type="button" class="btn-block btn btn-default" style="margin-top:20px" value="查詢歷史記錄" onclick="$('#listViewForm').submit();" />
                      </div>
                   

                  </div>
            <div class="form-group">
                <label class="text-danger">
                    1.「特殊」開頭的加點紀錄不會顯示於校內表現、校外榮譽裡，也不會在e本書。
                </label>
                <label>
                    2.<font style="color:#f0ad4e">橘色按鈕</font>是被控管的點數，每個月發出去的點數會被限制，<a  href="https://youtu.be/CoNHkV9Z-Pw">若有疑問，可參考這裡影片說明。</a>
                </label>
            </div>
        </div>
    </div>
}

@using (Html.BeginForm("ListView", (string)ViewBag.BRE_NO, FormMethod.Post, new { @id="listViewForm" })) // 導向 特殊加扣點作廢 Form
{
    @Html.Hidden("Search.SYS_TABLE_TYPE", "ADDT20")
}
