﻿@{
    /**/

    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    string SchoolName = ViewBag.SCHOOL_NAME;
    if (SchoolName == null) { if (user != null) { SchoolName = user.SCHOOL_NAME; } else { SchoolName = "  "; } }

    ViewBag.Title = SchoolName + "e酷幣首頁";
    Layout = "~/Views/Shared/_Layout.cshtml";
    EcoolWeb.Models.UserProfileHelper.SetLayoutCookieData(Layout);
}

@Html.Partial("_Notice")

<div class="row">
    <div class="col-md-12">
        <div class="p-context">
            @{string Explain = (ViewBag.SchoolExplain != null) ? ViewBag.SchoolExplain : "";}
            @Html.Raw(HttpUtility.HtmlDecode(@Explain))
        </div>
    </div>
</div>
<br />
@Html.Partial("_BET02Partial")
<br />
@Html.Action("_ImgPlay", "ZZZI25")

<br />

<div class="row text-center">
    @{
        List<ADDV01> ADDV01List = ViewBag.ADDV01List;
        List<HRMT01QTY> ADDT09List = ViewBag.ADDT09List;
        List<HRMT01QTY> AWAT01List = ViewBag.AWAT01List;
    }

    <div class="col-md-4 text-center" style="margin: 0px auto;max-width:250px ">
        <div class="text-center">
            <img src="~/Content/img/web-bar2-revise-27.png" class="img-responsive " alt="Responsive image" />
            <table class="table-ecool table-hover table-ecool-reader">
                <thead>
                    <tr>
                        <th>
                            @Html.DisplayNameFor(m => ADDT09List.First().CLASS_NO)
                        </th>
                        <th>
                            @Html.DisplayNameFor(m => ADDT09List.First().NAME)
                        </th>
                        <th>
                            閱讀冊數
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @if (ADDT09List.Count > 0)
                    {
                        foreach (var item in ADDT09List)
                        {
                            <tr>
                                <td style="text-align:center">
                                    @Html.DisplayFor(modelItem => item.CLASS_NO)
                                </td>
                                <td style="text-align:center">
                                    @Html.DisplayFor(modelItem => item.NAME)
                                </td>
                                <td style="text-align:center">
                                    @Html.DisplayFor(modelItem => item.QTY)
                                </td>
                            </tr>
                        }
                    }
                    <tr>
                        <td align="right" colspan="3">
                            <u>
                                @Html.ActionLink("more", "../ADDT/ADDTList", "ADDT", new { @class = "btn btn-link-ez btn-xs" })
                            </u>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <div class="col-md-4 text-center" style="margin: 0px auto;max-width:250px ">
        <div class="text-center">
            <img src="~/Content/img/web-bar2-revise-28.png" class="img-responsive " alt="Responsive image" />
            <table class="table-ecool table-hover table-ecool-List">
                <thead>
                    <tr>
                        <th>
                            @Html.DisplayNameFor(m => ADDV01List.First().CLASS_NO)
                        </th>
                        <th>
                            @Html.DisplayNameFor(m => ADDV01List.First().NAME)
                        </th>
                        <th>
                            投稿數
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @if (ADDV01List.Count > 0)
                    {
                        foreach (var item in ADDV01List)
                        {
                            <tr>
                                <td style="text-align:center">
                                    @Html.DisplayFor(modelItem => item.CLASS_NO)
                                </td>
                                <td style="text-align:center">
                                    @Html.DisplayFor(modelItem => item.SNAME)
                                </td>
                                <td style="text-align:center">
                                    @Html.DisplayFor(modelItem => item.WRITING_QTY)
                                </td>
                            </tr>
                        }
                    }
                    <tr>
                        <td align="right" colspan="3">
                            <u>
                                @Html.ActionLink("more", "../ADDI01/OrderList", "ADDI01", new { @class = "btn btn-link-ez btn-xs" })
                            </u>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <div class="col-md-4 text-center" style="margin: 0px auto;max-width:250px ">
        <div class="text-center">
            <img src="~/Content/img/web-bar2-revise-29.png" class="img-responsive " alt="Responsive image" />
            <table class="table-ecool table-hover table-ecool-AWA003">
                <thead>
                    <tr>
                        <th>
                            @Html.DisplayNameFor(m => AWAT01List.First().CLASS_NO)
                        </th>
                        <th>
                            @Html.DisplayNameFor(m => AWAT01List.First().NAME)
                        </th>
                        <th>
                            酷幣點數
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @if (AWAT01List.Count > 0)
                    {
                        foreach (var item in AWAT01List)
                        {
                            <tr>
                                <td style="text-align:center">
                                    @Html.DisplayFor(modelItem => item.CLASS_NO)
                                </td>
                                <td style="text-align:center">
                                    @Html.DisplayFor(modelItem => item.NAME)
                                </td>
                                <td style="text-align:center">
                                    @Html.DisplayFor(modelItem => item.CASH_ALL)
                                </td>
                            </tr>
                        }
                    }
                    <tr>
                        <td align="right" colspan="3">
                            <u>
                                @Html.ActionLink("more", "../AWA003/QUERY", "AWA003", new { @class = "btn btn-link-ez btn-xs" })
                            </u>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<script>
    //$(document).ready(function () {
    //    if (window.location.protocol != 'https:') {
    //        location.href = location.href.replace("http://", "https://");
    //    }
    //});
</script>