﻿@model ECOOL_APP.com.ecool.Models.DTO.MonthBestSellerViewModel
@{
    ViewBag.Title = ViewBag.Panel_Title;

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);

    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
    else
    {
        Layout = "~/Views/Shared/_LayoutSEO.cshtml";
    }

}

@Html.Partial("_Title_Secondary")
@{
    Html.RenderAction("_Menu", new { NowAction = "MonthBestSeller" });
}

@using (Html.BeginForm("MonthBestSeller", "SECI07", FormMethod.Post, new { id = "form1", name = "form1" }))
{
    <div class="row">
        @Html.LabelFor(m => m.Where_YEAR, new { @class = "col-md-12" })
        <div class="col-md-3">
            @Html.DropDownListFor(m => m.Where_YEAR, (IEnumerable<SelectListItem>)ViewBag.YearItems, new { @class = "form-control input-sm", @onchange = "this.form.submit()" })
        </div>
    </div>
    <div class="row">
        @Html.LabelFor(m => m.Where_Month, new { @class = "col-md-12" })
        <div class="col-md-3">
            @Html.DropDownListFor(m => m.Where_Month, (IEnumerable<SelectListItem>)ViewBag.MonthItems, new { @class = "form-control input-sm", @onchange = "this.form.submit()" })
        </div>
    </div>
    <div class="row">
        @Html.LabelFor(m => m.Where_Sex, new { @class = "col-md-12" })
        <div class="col-md-3">
            @Html.DropDownListFor(m => m.Where_Sex, (IEnumerable<SelectListItem>)ViewBag.GenderItems, new { @class = "form-control input-sm", @onchange = "this.form.submit()" })
        </div>
    </div>
}
<br />

@if (Model.MonthOfBookSellList != null && Model.MonthOfBookSellList.Count > 0)
{
    <table class="table table-responsive table-striped table-hover">
        <thead>
            <tr>
                <th class="text-center">
                    @Html.DisplayNameFor(m => m.MonthOfBookSellList.FirstOrDefault().RANK)
                </th>
                <th class="text-center">
                    @Html.DisplayNameFor(m => m.MonthOfBookSellList.FirstOrDefault().BKNAME)
                </th>
                <th class="text-center">
                    @Html.DisplayNameFor(m => m.MonthOfBookSellList.FirstOrDefault().BORROW_COUNT)
                </th>
            </tr>
        </thead>
        <tbody class="text-center">
            @foreach (var item in Model.MonthOfBookSellList)
            {
                <tr>
                    <td>@item.RANK</td>
                    <td>@item.BKNAME</td>
                    <td>@item.BORROW_COUNT</td>
                </tr>
            }
                <tr>
                    <td></td>

                    <td></td>
                    <td style="border-top:2px solid #d56666">
                        <div class="text-danger text-center">
                            本頁總計: @Model.BorrowBookTotal 本書　　
                        </div>
                    </td>
                </tr>
        </tbody>
    </table>
}
else
{
    <div>資料量不足</div>
}
