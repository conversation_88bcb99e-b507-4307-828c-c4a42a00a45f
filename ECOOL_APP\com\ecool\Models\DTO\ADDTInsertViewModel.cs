﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.com.ecool.Models.entity
{
    public class ADDTInsertViewModel
    {

        ///Summary
        ///學校代碼
        ///Summary
        public string SCHOOL_NO { get; set; }

        ///Summary
        ///帳號
        ///Summary
        public string USER_NO { get; set; }

        ///Summary
        ///班級
        ///Summary
        public string CLASS_NO { get; set; }

        ///Summary
        ///學年度
        ///Summary
        public int SYEAR { get; set; }

        ///Summary
        ///學期
        ///Summary
        public int SEMESTER { get; set; }

        ///Summary
        ///座號
        ///Summary
        public string SEAT_NO { get; set; }

        ///Summary
        ///姓名
        ///Summary
        public string NAME { get; set; }

        ///Summary
        ///簡稱
        ///Summary
        public string SNAME { get; set; }

        ///Summary
        ///是否為閱讀護照
        ///Summary
        public string PASSPORT_YN { get; set; }

        ///Summary
        ///閱讀護照代碼
        ///Summary
        public string BOOK_ID { get; set; }

        ///Summary
        ///書名
        ///Summary
        public string BOOK_NAME { get; set; }

        ///Summary
        ///心得
        ///Summary
        public string REVIEW { get; set; }

        ///Summary
        ///圖檔儲存路徑
        ///Summary
        public string IMG_FILE { get; set; }

        ///Summary
        ///參考 ADDT06MetaData
        ///Summary
        public byte APPLY_TYPE { get; set; }

        ///Summary
        ///參考 ADDT06MetaData
        ///Summary
        public string APPLY_STATUS { get; set; }

        ///Summary
        ///申請日
        ///Summary
        public DateTime CRE_DATE { get; set; }

    }
}
