2.1.3.1 (Feb., 2015)
Bug Fixes
a. fix word serialization issues
b. fix CloneStyleFrom issue
c. Fix vertical alignment default value for XSSFCell
d. fix setAutoFilter change range issue
e. fix EDate function
f. fix FormatException in FormulaParser
g. fix Comment random Shape issue (comment serialization issue)
h. fix not be able to unlock the cell issue 

POI BUG FIXES
55729 - DataFormatter should format Error cells, returning the Excel error string
49237 - HSSF Row Style XfIndex is 12 not 16 bits of data
54607 - NullPointerException in XSSFSheet.getTopRow() when the top row is 1
55745 - fix handling of tables in XSSF if there are comments as well
54673 - Simple wildcard support in HLOOKUP, VOOLKUP, MATCH, COUNTIF
55047 - REPT formula support
55042 - COMPLEX formula support
55041 - CODE formula support
54508 - EDATE formula support
53966 - IfError support (from Analysis Toolpak)
54402 - IfError handling of indirect references

New feature
a. Add ReplaceText to XWPFParagraph and XWPFRun
b. adjust ExcelExtractor interface
c. Improving comment missing handling in HSSFSheet
d. copy hyperlink in CopySheet and fix some bugs
e. Implement ChartSheet in OpenXmlFormats
f. Implement shrinktofit for XSSF

2.1.3 (Dec., 2014)
Bug Fixes
a. fix 2 charts insert in the same sheet issue
b. fix a lot of Excel 2007 serialization issues
c. fix some Word 2007 serialiation issues
d. fix RemoteSheetAt bug
e. support changing background in XWPF
f. fix Uri-related issues on Mono

New examples
SetIsRightToLeftInXlsx - show how to use IsRightToLeft property
ExtractPicturesFromXlsx - show how to extract pictures from Excel 2007 file
SetRowStyle - show how to set whole row style with simple code

2.1.1 (Jun., 2014) 
New Features
a. XSSFSheet.CopySheet
b. Excel2Html for XSSF and HSSF
c. insert picture in word 2007
d. Implement IfError function in formula engine

Bug Fixes
a. fix conditional formatting issue
b. fix ctFont order issue
c. fix vertical alignment issue in XSSF
d. add IndexedColors to NPOI.SS.UserModel
e. fix decimal point issue in non-English culture
f. fix SetMargin issue in XSSF
g.fix multiple images insert issue in XSSF
h.fix rich text style missing issue in XSSF
i. fix cell comment shape (big arrow) in XSSF
j. WorkbookFactory for Excel 2007 doesn't occupy file.
k. fix XSSFCell.IsMergedCell
l. fix incorrect page margin value due to different culture
m. fix HSSFSheet.CopyTo doesn't copy rich text in cells
n. fix scroll bar and tabs missing in previous 2.1 release

New examples
a. XSSF.DownloadXlsx
b. XSSF.ConditionFormats
c. XSSF.LineChart
d. XWPF.InsertPicturesInWord

2.0 [v2.0.6] (Jan., 2014)
a. fix a lot of xml serialization issue for OOXML (2.0.5 will corrupt some xlsx and docx files)
b. implement XSSFCell.IsMergedCell
c. IWorkbook implements IList<ISheet>

2.0 Beta 2 [v2.0.5] (Dec, 2013)
New features
a. Support Scatter chart in XSSF (xlsx) (other chart types are not supported yet)
b. Extract pictures from Excel (xlsx)
c. XWPF becomes stabler than before
d. Able to support xml:space="preserve" attribute
e. Add mono assembly in the release package
f. file generated by NPOI will contain NPOI tags in custom properties to identify the generator
g. Adjust some XWPF interfaces

Sample changes
a. add new samples like BigGridTest, WritePerformanceTest to test performance
b. add ScatterChart to show how to create Scatter chart
c. add LinkedDropDownList for both XSSF and HSSF
d. add MonthlySalaryReport to show how to use formula in XSSF
e. add CreateCustomProperties to show how to use custom props in XSSF and XWPF

Bug fixes
a. fix shift row issue in XSSFSheet
b. fix performance issue due to XmlSerializer. NPOI is getting rid of XmlSerializer.
c. reading/writing CT_Drawing
d. fix ddd pattern issue in CellDateFormatter
e. Change some common interfaces in NPOI.SS
f. fix OutOfMemory issue in MemoryPackagePart
g. Able to read AbsoluteAnchor, OneCellAnchor, TwoCellAnchor in drawing.xml
h. Formula will be calculated automatically after generation
i. improve performance for XSSF while creating new rows
For detail, please read https://npoi.codeplex.com/discussions/443655
j. fix name encoding issue of custom properties in HSSF
For detail, please read https://npoi.codeplex.com/workitem/12296
k. fix exception in HSSFRows.RemoveAllCells
l. fix CellStyle Hashtable comparison issue in class HSSFCellUtil. This can help prevent over 4000 styles issue from code.
m. Fix globalization issue in ExpandedDouble

=========================================================================

2.0 Beta 1 [v2.0.1] (Feb, 2013)
New features
a. Copy rows, columns inside a sheet
b. Copy sheet between workbooks (contributed by Paul Kratt)
c. insert rows and column inside a sheet
d. OpenXml4Net is stable and ready for use
e. Support new Excel functions such as RATE, RANK, ISERR
f. Support converting from Excel to Html
g. POIFS Browser supports Chart records

Example changes
a. Use MemoryStream.WriteTo instead in ExportXlsToDownload in order to avoid out of memory exception
b. add new examples like CalendarDemo, BusinessPlan, CopySheet

NPOI Bug Fixes
Issue with 2.0 Beta: Get an non-Closing Element error
http://npoi.codeplex.com/workitem/11085
Npoi 2 error in NumericCellValue (XSSFCell)
http://npoi.codeplex.com/workitem/11083
[HSSF]Comment is not saved correctly while using a xls template with comment
http://npoi.codeplex.com/workitem/11169
Access issue creating worksheet
http://npoi.codeplex.com/workitem/11383
Error when running NPOI with Mono C# compiler version *******
http://npoi.codeplex.com/workitem/4547

POI Bug Fixes
53282 - Avoid exception when parsing OPC relationships with non-breaking spaces(poi-developers) 
54016 - Avoid exception when parsing workbooks with DConRefRecord in row aggregate(poi-developers)
53404 - Fixed compatibility bug with modifying xls files created by POI-3.6 and earlier(poi-developers)
53763 - avoid style mess when using HSSFOptimiser (poi-developers) 
53974 - Avoid NPE when constructing HSSFWorbook on Google App Engine(poi-developers) 
53950 - fixed setForceFormulaRecalculation to reset workbook-level "manual" flag(poi-developers) 
52211 - avoid unnessary re-coverting content types to US-ASCII, it can cause exceptions on ibm mainframes(poi-developers) 
HSSFOptimiser will now also tidy away un-used cell styles, in addition to duplicate styles(poi-developers)
53434 - Subtotal is not return correct value. (poi-developers) 
53144 - First comment not cloned after cloneSheet() (poi-developers) 
53028 - Broken auto fit row height in the cells with word wrap (poi-developers)
53010 - GSoC2012? Improve drawing support in HSSF (poi-developers)
52764 - Unmodified cell comments disappear after HSSFWorkbook.write (poi-developers) 
51676 - Using drawingPatriarch.createCellComment(anchor) leads to File error: data may have been lost (poi-developers) 
51455 - It would be really nice to be able to set the background picture of a comment (poi-developers) 
48989 - If we have a comment but the row is not created we will not be able to get it. (poi-developers) 
48873 - Comments not saving in XLS files with collapsible columns (poi-developers) 
46143 - setLineStyleColor for comments donot work (poi-developers) 
53699 - Patch to correct BorderStyle? enum positions (poi-developers)
53644 - XLS formula bugfix (CalFieldFunc?) + WeekDay? addon (poi-developers) 
53446 - Fixed some problems extracting PNGs (poi-developers)
53204 - Improved performanceof PageSettingsBlock? in HSSF (poi-developers)
53500 - Getter for repeating rows and columns(poi-developers)
53476 - Support Complex Name in formulas (poi-developers) 
53414 - properly update sheet dimensions when adding column (poi-developers)
Add File based constructor to OPCPackage, alongside existing String one (which constructed a File from the string internally)(poi-developers) 
53389 - Handle formatting General and @ formats even if a locale is prefixed to them(poi-developers) 
53058 - Utility for representing drawings contained in a binary Excel file as a XML tree(poi-developers)
48528 - support negative arguments to the DATE() function(poi-developers) 
53101 - fixed evaluation of SUM over cell range > 255(poi-developers)~
52928 - DateFormatConverter?: an utility to convert instances of java.text.DateFormat? to Excel format patterns(poi-developers)
52895 - show SSTIndex instead of XFIndex in LabelSSTRecord.toString()(poi-developers)
52818 - Added implementation for RANK()(poi-developers)
51564 - support for enforcing fields update in XWPF(poi-developers) 51673 - support grouping rows in SXSSF(poi-developers)
51780 - support replacement of content types in OPC packages (poi-developers)
52057 - updated formula test framework to be aware of recently added Functions (poi-developers)
52574 - support setting header / footer page margins in HSSF(poi-developers)
52583 - fixed WorkbookUtil#createSafeSheetName? to escape colon (poi-developers)
52708 - misc improvements in CellFormat? (poi-developers)
52690 - added a getter for length of encrypted data in Ecma and Agile decryptors(poi-developers)
allow runtime registration of functions in FormulaEvaluator?(poi-developers)
52665 - When reading from a ZipFileZipEntrySource? that has already been closed, give IllegalArgumentException? rather than NPE(poi-developers)
52385 - avoid trancated array and vector data when reading OLE properties(poi-developers)
51498 - fixed evaluation of blank cells in COUNTIF(poi-developers)
52576 - support changing external file references in HSSFWorkbook(poi-developers)
49896 - support external references in FormulaRenderer?(poi-developers)
52527 - avoid exception when matching shared formula records in HSSF(poi-developers)
52568 - Added methods to set/get an XWPFRun's text color(poi-developers)
52566 - Added methods to set/get vertical alignment and color in XWPFTableCell(poi-developers)
52562 - Added methods to get/set a table row's Can't Split and Repeat Header attributes in XWPF(poi-developers)
52561 - Added methods to set table inside borders and cell margins in XWPF(poi-developers)
52569 - Support DConRefRecord in HSSF(poi-developers)
52575 - added an option to ignore missing workbook references in formula evaluator(poi-developers)
52540 - Relax the M4.1 constraint on reading OOXML files, as some Office produced ones do have 2 Core Properties, despite the specification explicitly forbidding this(poi-developers)
52462 - Added implementation for SUMIFS()(poi-developers)
52449 - Support writing XWPF documents with glossaries (Glossaries are not yet supported, but can now be written out again without changes)(poi-developers)
52438 - Update CellDateFormatter? to handle times without seconds(poi-developers)
52389 - Support ?/? as well as #/# fractions, and tighten DataFormatter? rules for fraction matching(poi-developers)
52378 - Support for WORKDAY and NETWORKDAYS functions(poi-developers)
52349 - Merge the logic between the TEXT function and DataFormatter?(poi-developers)
52349 - Correctly support excel style date format strings in the TEXT function(poi-developers)
52369 - XSSFExcelExtractor should format numeric cells based on the format strings applied to them(poi-developers) 
52369 - Event based XSSF parsing should handle formatting of formula values in XSSFSheetXMLHandler(poi-developers)
52348 - Avoid exception when creating cell style in a workbook that has an empty xf table(poi-developers)
52314 - enhanced SheetUtil?.getColumnWidth(poi-developers)
51875 - More XSSF new-line in formula support(poi-developers)
POIFS EntryUtils?.copyNodes(POFS,POIFS) now uses FilteringDirectoryNode?, so can exclude from copying nodes not just directly under the root(poi-developers)
POIFS Helper FilteringDirectoryNode?, which wraps a DirectoryEntry? and allows certain parts to be ignored(poi-developers)
52190 - null check on XWPF setFontFamily(poi-developers)
52050 - Support for the Excel RATE function(poi-developers)
51949 - Avoid NPE on double close of ZipFileZipEntrySource?(poi-developers)
51950 - XWPF fix for footnotes not always being present in a document(poi-developers)
51963 - Correct AreaReference? handling of references containing a sheet name which includes a comma(poi-developers)
51834 - Opening and Writing .doc file results in corrupt document(poi-developers)
Allow the passing of a File object to WorkbookFactory?.create, which permits lower memory processing than the InputStream? version(poi-developers)
51850 - support creating comments in XSSF on an earlier slide when later ones already have them(poi-developers)
New PackagePart? method getRelatedPart(PackageRelationship?) to simplify navigation of relations between OPC Parts(poi-developers)
51832 - handle XLS files where the WRITEPROTECT record preceeds the FILEPASS one, rather than following as normal(poi-developers)
51809 - correct GTE handling in COUNTIF(poi-developers)
51670 - avoid LeftoverDataException? when reading .xls files with invalid LabelRecords?(poi-developers)
51196 - prevent NPE in XWPFPicture.getPictureData() (poi-developers)
51196 - more progress with Chart APi in XSSF(poi-developers)
51785 - Allow XSSF setForceFormulaRecalculation to work with the minimal ooxml-schemas jar(poi-developers)

=========================================================================
 
2.0 Alpha [v2.0.0] (August, 2012)
New features
a. Implement OpenXml4Net (same as System.Packaging from Microsoft). It supports both .NET 2.0 and .NET 4.0
b. Excel 2007 read/write library (NPOI.XSSF)
c. Word 2007 read/write library(NPOI.XWPF)
d. NPOI.SS namespace becomes the interface shared between XSSF and HSSF
e. Load xlsx template and save as new xlsx file (partially supported)
f. Diagonal line in cell both in xls and xlsx
g. Support isRightToLeft and setRightToLeft on the common spreadsheet Sheet interface, as per existing HSSF support(poi-developers)
h. New examples for NPOI.OpenXml4Net(2 examples), NPOI.XSSF (15 examples) and NPOI.XWPF (5 examples)

=========================================================================

1.2.5 (April,2012)
In this release, we fixed most of the bugs found in POI 3.8 beta 4. 

POI Bug Fixes
51535 - correct signed vs unsigned short reading in NDocumentInputStream(poi-developers)
50209 - Fixed evaluation of Subtotals to ignore nested subtotals(poi-developers)
50401 - fixed EscherProperty to return property name instead of 'unknown' for complex properties (poi-developers)
51481 - Fixed autofilters in HSSF to avoid warnings in Excel 2007(poi-developers)
51533 - Avoid exception when changing name of a sheet containing shared formulas(poi-developers)
46250 - Fixed cloning worksheets with images(poi-developers)
51514 - allow HSSFObjectData to work with both POIFS and NPOIFS(poi-developers)
51514 - avoid NPE when copying nodes from one HSSF workbook to a new one, when opened from NPOIFS(poi-developers)
51504 - avoid NPE when DefaultRowHeight or DefaultColumnWidth records are missing(poi-developers)
48294 - Fixed HSSFWorkbook.setSheetOrder() to respect inter-sheet references (poi-developers)
51448 - Avoid exception when evaluating workbooks with more than 256 sheets (poi-developers)
51458 - Correct BitField wrapping when setting large values(poi-developers)
51460 - Improve HSSF performance when loading very long rows, by switching the CellValue array to an iterator(poi-developers)
51415 - Fixed Workbook.createSheet(sheetName) to truncate names longer than 31 characters(poi-developers)
51332 - Fixed internal IDs of shapes generated by HSSFPatriarch when there are more than 1023 drawing objects (poi-developers)
48408 - Improved documentation for Sheet.setColumnWidth (poi-developers)
50681 - Avoid exceptions in HSSFDataFormat.getDataFormatString() (poi-developers)
50681 - Fixed autosizing columns beyond 255 character limit (poi-developers)
51339 - Fixed arithmetic rounding in formula evaluation (poi-developers)
51098 - Correctly calculate image width/height, if image fits into one cell(poi-developers)
51273 - Formula Value Cache fix for repeated evaluations(poi-developers)
51171 - Improved performance of SharedValueManager (poi-developers)
51171 - Improved performance of opening large .xls files(poi-developers)
51153 - Correct sizing of LbsDataSubRecord with unused padding fields(poi-developers)
51143 - NameCommentRecord correction for writing non ASCII strings(poi-developers)
51115 - Handle DataFormatter escaping of "." in the same way as "-" and "/"(poi-developers)
51100 - Fix IOUtils issue for NPOIFS reading from an InputStream where every block is full(poi-developers)
50841 - Improved SpreadSheet DataFormatter to handle scientific notation, invalid dates and format spacers(poi-developers)
50939 - ChartEndObjectRecord is supposed to have 6 bytes at the end, but handle it not(poi-developers)
50912 - fixed setting named styles to HSSFCells(poi-developers)
50779 - fixed RecordFormatException when reading unicode strings with photenic data(poi-developers)
50718 - More helpful error message when you try to create a CellReference with #REF!(poi-developers)
50786 - Speed up calls to HSSFColor.getIndexHash() by returning a cached, unmodifiable Map. HSSFColor.getModifiableIndexHash() provides access to the old (slow but modifiable) functionality(poi-developers)
32903 - Correct XBAT chaining explanation in /poifs/fileformat.html(poi-developers)
46664 - When creating HSSF Print Areas, ensure the named range is reference based not value based(poi-developers)
50756 - When formatting numbers based on their Cell Style, treat GENERAL the same as the more typical General(poi-developers)
fixed HSSFWorkbook.createCellStyle to throw exception if the maximum number of cell styles was exceeded(poi-developers)
49928 - allow overridden built-in formats in HSSFCellStyle(poi-developers)
50587 - Improved documentation on user-defined functions(poi-developers)
50416 - Correct shifting of the first or last row in a sheet by multiple rows(POI-DEVELOPERS)
50246 - Properly position GutsRecord when reading HSSF workbooks(POI-DEVELOPERS)
50437 - Support passing ranges to NPV()(POI-DEVELOPERS)
47405 - Improved performance of RowRecordsAggregate.getStartRowNumberForBlock / getEndRowNumberForBlock(poi-developers)
50113 - Remove cell from Calculation Chain after setting cell type to blank (poi-developers)
50096 - Fixed evaluation of cell references with column index greater than 255 (poi-developers)
49761 - Tolerate Double.NaN when reading .xls files(poi-developers)
50211 - Use cached formula result when auto-sizing formula cells(poi-developers)
50118 - OLE2 does allow a directory with an empty name, so support this in POIFS(poi-developers)

NPOI Bug fixes
a. CloneSheet with images throws exception
b. Comments are still visible even set Visible property to false
c. AutoSizeColumn doesn't work as expected
d. Reading sheet protected workbook throws exception

New Features
a. Added NPOIFS constructors to most POIDocument classes and their extractors, and more widely deprecated the Document(DirectoryNode, POIFSFileSystem) constructor in      favour of the more general Document(DirectoryNode) one
b. Added implementation for CLEAN(), CHAR(), ADDRESS(),MROUND(), VAR(), VARP(), IRR()
c. Added Support for HOUR, MINUTE and SECOND date formulas
d. Support for continued NameRecords, continued ExtSSTRecords
e. Support using RecalcIdRecord to trigger a full formula recalculation on load (poi-developers)
f. ExternalNameRecord support for DDE Link entries without an operation(poi-developers)
g. POIFS Browser: add ability to parse EscherContainer and sub nodes


=========================================================================

1.2.4 (Nov,2011)
In this release, we fixed most of the bugs found in POI 3.6 and POI 3.7. 

NPOI Bug Fixes
5157 - HSSFSheet.FitToPage property is added. It doesn't work previously.
7271 - Cell formula that has been "dragged" down cannot be read. Patch is applied
xxx - Bad padding calculation 
3804 - NPOI doesn't work with a Excel template with macro 

POI Bug Fixes
46547 - ClassCastException in HSSFSheet.shiftRows(...)
47363 - Fixed HSSFSheet to allow addition of data validations after sheet protection
45066 - sheet encoding size mismatch problems
49026 - added implementation for text() (poi-developers)
46654 - HSSFRow/RowRecord to properly update cell boundary indexes(POI-DEVELOPERS)
46385 - (also patch 46362) fix serialization of StyleRecord with unicode name(POI-DEVELOPERS)
47069 - Fixed HSSFSheet#getFirstRowNum and HSSFSheet#getLastRowNum to return correct values after removal of all rows(POI-DEVELOPERS)
48325 - bad text 'Page &P of &N' and similar errors when reading in spreadsheets
48485 - add extra paper size constans to printsetup, such as a3, b4 and b5(poi-developers)
48425 - improved performance of dateutil.iscelldateformatted() (poi-developers)
49524 - add vertical text orientation method
47001 - Fixed WriteAccessRecord and LinkTable to handle unusual format written by Google Docs(POI-DEVELOPERS)
46368 - Fix HSSFRichTextRun and strings longer than 32768 characters(POI-DEVELOPERS)
48292 - Support of array formulas
49820 - ParagraphProperties.getLvl() returns 0 for both Level 1 and Body text
      - fixed HSSFWorkbook.createCellStyle to throw exception if the maximum number of cell styles was exceeded(poi-developers)
47405 - Improved performance of RowRecordsAggregate.getStartRowNumberForBlock / getEndRowNumberForBlock(poi-developers)
46250 - Workbook cloneSheet() - clone images
48026 - duplicate footer and header
46664 - Print Area does not save in HSSF worksheets
49761 - Double.NaN can be written but not read with POI
47309 - Number of Cell Comments in a sheet limited to 65536 with HSSF 
46776 - POI does not work when run the method "cloneSheet()" 
47250 - Fixed FontRecord to expect unicode flags even when name length is zero(POI-DEVELOPERS)
47198 - Fixed formula evaluator comparison of -0.0 and 0.0(POI-DEVELOPERS)
46287 - Control of header and footer extraction in ExcelExtractor / XSSFExcelExtractor(POI-DEVELOPERS)
47154 - Handle the cell format @ as the same as General(POI-DEVELOPERS)
40520 - Fixed HSSFFont.applyFont() to properly apply font to overlapping regions(POI-DEVELOPERS)
45720 - cloneSheet breaks autofilters 
46643 - Formula parser should encode explicit range operator with tMemFunc 
51481 - Office 2007 warning if using autofilter 
50681 - autoSizeColumn sets column width beyond 255 character limit for XSSF sheets and HSSF Sheets
50912 - Applying an HSSFCellStyle on an HSSFCell has no effect
51143 - NameCommentRecord correction for writing non ASCII strings(poi-developers)

New Features
a. Add NameCommentRecord, HeaderFooterRecord
b. AutoFilter Phrase II - it's able to create autofilter with any cell range
c. Add the method to determine if the cell is merged or not
d. Support compilation with MonoDeveloper
e. Change all interface name starting with 'I'

=========================================================================

1.2.3 (Nov. 2010)
NPOI Bug fixes
5010 - Unable to read xls file with pivot table 
5139 - SheetExtRecord DataSize is 40 
6177 - LeftoverDataException: Intermitend Bug 
6341 - System.NullReferenceException on Workbook.Dispose (+Bugfix) 
Change NPOI.HSSF.Model.Sheet to NPOI.HSSF.Model.InternalSheet
Change NPOI.HSSF.Model.Workbook to NPOI.HSSF.Model.InternalWorkbook
6984 - Cannot manually edit/add dates in the xls created by NPOI

Sync POI bug fixes
46776 - Added clone() method to MulBlankRecord to fix crash in Sheet.cloneSheet()(POI-DEVELOPERS)
46547 - Allow addition of conditional formatting after data validation(POI-DEVELOPERS)
45290 - Support odd files where the POIFS header block comes after the data blocks, and is on the data blocks list(POI-DEVELOPERS)
46904 - Fix POIFS issue with duplicate block 0 references on very old BIFF5/BIFF7 files(POI-DEVELOPERS)
45376
47970 - added a method to set arabic mode in HSSFSheet(POI-DEVELOPERS)
47048 - Fixed evaluation of defined names with the 'complex' flag set(POI-DEVELOPERS)
44916 - Allow access to the HSSFPatriarch from HSSFSheet once created(POI-DEVELOPERS)
45672 - improve handling by MissingRecordAwareHSSFListener of records that cover multiple cells (MulBlankRecord and MulRKRecord)(POI-DEVELOPERS)
45698 - Fix LinkTable to tolerate multiple EXTERNSHEET records(POI-DEVELOPERS)
45784 - More fixes to SeriesTextRecord(POI-DEVELOPERS) 
46065 - added implementation for VALUE function(POI-DEVELOPERS) 
45966 - added implementation for FIND function(POI-DEVELOPERS) 
45784 - More fixes to SeriesTextRecord(POI-DEVELOPERS) 
46065 - added implementation for VALUE function(POI-DEVELOPERS) 
45966 - added implementation for FIND function(POI-DEVELOPERS) 
47721 - Added implementation for INDIRECT()
	Added implementation for ISNA()(
48332 - fixed ColumnInfoRecord to tolerate missing reserved field
45778 - fixed ObjRecord to read ftLbsData properly(POI-DEVELOPERS) 
46206 - Fixed Sheet to tolerate missing DIMENSION records(POI-DEVELOPERS) 
47384 - Fixed ExternalNameRecord to handle unicode names(POI-DEVELOPERS) 
47479 - Fix BoolErrRecord to tolerate incorrect format written by OOO
46199 - More tweaks to EmbeddedObjectRefSubRecord(POI-DEVELOPERS) 
47751 - Do not allow HSSF's cell text longer than 32,767 characters
46213 - Fixed FormulaRecordAggregate to gracefully ignore extra StringRecords(POI-DEVELOPERS) 
46301 - added pivot table records: SXDI, SXVDEX, SXPI, SXIDSTM, SXVIEW, SXVD, SXVS, et al(POI-DEVELOPERS) 
48180 - be more forgiving of short chart records, which skip some unused fields(POI-DEVELOPERS) 
46280 - Fixed RowRecordsAggregate etc to properly skip PivotTable records(POI-DEVELOPERS) 
46174 - Fixed HSSFName to handle general formulas (not just area references)(POI-DEVELOPERS) 
47768 - Implementation of Excel "Npv" functions
47771 - Added method setFunction(boolean) for defined names
47770 - built-in positive formats don't need starting '
47737 - adjust sheet indices of named ranges when deleting sheets
47448 - Allow HSSFEventFactory to handle non-zero padding at the end of the workbook stream
47143 - Fixed OOM in HSSFWorkbook#getAllPictures when reading .xls files containing metafiles
47415 - Fixed PageSettingsBlock to allow multiple PLS records
46269 - Improved error message when attempting to read BIFF2 file(POI-DEVELOPERS) 
46189 - added chart records: CHARTFRTINFO, STARTBLOCK, ENDBLOCK, STARTOBJECT, ENDOBJECT, and CATLAB(POI-DEVELOPERS) 
45290 - Support odd files where the POIFS header block comes after the data blocks, and is on the data blocks list(POI-DEVELOPERS) 
46137 - Handle odd files with a ContinueRecord after EOFRecord(POI-DEVELOPERS) 

=========================================================================

NPOI 1.2.2 (2009-12-5)
a. ability to identify more Chart record
b. ColumnAutoSize bug is fixed (bug 3754 )
c. DefaultRowHeight bug is fixed (bug 3880)
e. Sheet Tab Formatting (bug 3772)
f. ShrinkToFit property is exposed to user (bug 4103)
g. Active selection area (bug 4527)
h. HSSFSheet.RemoveRow will remove CellRecord as well as RowRecord (bug 3493)
i. Auto filter feature (alpha)
j. read xls template with macro (bug 3804)

=========================================================================

NPOI 1.2.1 (2009-6-1)
a. Conditional Formating doesn't work as expected
b. HSSFDataFormat.GetFormat return different index for the same format string
c. Incorrect namespace spelling of NPOI.SS.Formula
d. HSSFCell.ToString() method supports DataFormat now
e. add strong name for all the assembiles
f. HSSFColor.index static variable isn't accessible in VB.NET due to the HSSFColor Index property

=========================================================================

NPOI 1.2 (2009-5)
implement features in POI 3.2 final