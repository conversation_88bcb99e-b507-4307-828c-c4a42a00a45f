/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/Variants/Bold/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXVariants-bold"]={directory:"Variants/Bold",family:"STIXVariants",weight:"bold",Ranges:[[32,32,"All"],[119,124,"All"],[160,160,"All"],[411,411,"All"],[8242,8279,"All"],[8512,8512,"All"],[8592,8595,"All"],[8657,8674,"All"],[8709,8941,"All"]],8242:[586,-12,394,44,350],8709:[729,74,584,36,548],8726:[732,193,518,45,473],8730:[943,-28,800,112,844]};MathJax.OutputJax["HTML-CSS"].initFont("STIXVariants-bold");MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/Variants/Bold/Main.js");
