/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/General/Bold/SuppMathOperators.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXGeneral-bold"],{10764:[824,320,1484,32,1664],10765:[824,320,593,32,733],10766:[824,320,593,32,733],10767:[824,320,593,32,733],10768:[824,320,593,32,733],10769:[824,320,593,32,733],10770:[824,320,613,32,733],10771:[824,320,593,32,733],10772:[824,320,675,32,735],10773:[824,320,593,32,733],10774:[824,320,623,32,733],10775:[824,320,791,32,871],10776:[824,320,633,32,733],10777:[824,320,653,32,733],10778:[824,320,653,32,733],10779:[959,320,557,32,737],10780:[824,455,557,32,737],10786:[894,57,750,65,685],10787:[736,57,750,65,685],10788:[746,57,750,65,685],10789:[563,287,750,65,685],10790:[563,240,750,65,685],10791:[563,247,780,65,778],10794:[297,37,750,66,685],10795:[543,37,750,66,685],10796:[543,37,750,66,685],10800:[745,33,702,66,636],10801:[538,191,702,66,636],10802:[538,59,702,66,636],10815:[676,0,734,27,707],10846:[887,28,640,52,588],10851:[536,379,640,52,588],10854:[399,161,750,68,682],10855:[775,-27,750,68,682],10858:[565,-132,750,67,682],10861:[759,60,750,68,683],10862:[884,-107,750,68,682],10863:[752,-26,750,68,683],10864:[680,176,750,68,683],10865:[665,159,750,65,685],10866:[665,159,750,65,685],10867:[568,60,750,67,682],10877:[648,140,750,80,670],10878:[648,140,750,80,670],10887:[646,213,750,80,670],10888:[646,213,750,80,670],10889:[792,305,750,67,682],10890:[792,305,750,67,682],10901:[648,140,750,80,670],10902:[648,140,750,80,670],10909:[689,183,750,67,682],10910:[689,183,750,67,682],10927:[619,111,750,80,670],10928:[619,111,750,80,670],10941:[547,13,750,82,668],10942:[547,13,750,82,668],10949:[730,222,750,80,670],10950:[730,222,750,80,670]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/Bold/SuppMathOperators.js");
