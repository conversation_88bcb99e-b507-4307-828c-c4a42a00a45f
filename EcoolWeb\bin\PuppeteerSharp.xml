<?xml version="1.0"?>
<doc>
    <assembly>
        <name>PuppeteerSharp</name>
    </assembly>
    <members>
        <member name="T:PuppeteerSharp.AddTagOptions">
            <summary>
            Options used by <see cref="M:PuppeteerSharp.Page.AddScriptTagAsync(PuppeteerSharp.AddTagOptions)"/> &amp; <see cref="M:PuppeteerSharp.Page.AddStyleTagAsync(PuppeteerSharp.AddTagOptions)"/>
            </summary>
        </member>
        <member name="P:PuppeteerSharp.AddTagOptions.Url">
            <summary>
            Url of a script to be added
            </summary>
        </member>
        <member name="P:PuppeteerSharp.AddTagOptions.Path">
            <summary>
            Path to the JavaScript file to be injected into frame. If its a relative path, then it is resolved relative to <see cref="M:System.IO.Directory.GetCurrentDirectory"/>
            </summary>
        </member>
        <member name="P:PuppeteerSharp.AddTagOptions.Content">
            <summary>
            Raw JavaScript content to be injected into frame
            </summary>
        </member>
        <member name="P:PuppeteerSharp.AddTagOptions.Type">
            <summary>
            Script type. Use <c>module</c> in order to load a Javascript ES6 module
            </summary>
            <seealso href="https://developer.mozilla.org/en-US/docs/Web/HTML/Element/script"/>
        </member>
        <member name="T:PuppeteerSharp.BoundingBox">
            <summary>
            Bounding box data returned by <see cref="M:PuppeteerSharp.ElementHandle.BoundingBoxAsync"/>.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.BoundingBox.X">
            <summary>
            The x coordinate of the element in pixels.
            </summary>
            <value>The x.</value>
        </member>
        <member name="P:PuppeteerSharp.BoundingBox.Y">
            <summary>
            The y coordinate of the element in pixels.
            </summary>
            <value>The y.</value>
        </member>
        <member name="P:PuppeteerSharp.BoundingBox.Width">
            <summary>
            The width of the element in pixels.
            </summary>
            <value>The width.</value>
        </member>
        <member name="P:PuppeteerSharp.BoundingBox.Height">
            <summary>
            The height of the element in pixels.
            </summary>
            <value>The height.</value>
        </member>
        <member name="M:PuppeteerSharp.BoundingBox.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:PuppeteerSharp.BoundingBox"/> class.
            </summary>
        </member>
        <member name="M:PuppeteerSharp.BoundingBox.#ctor(System.Decimal,System.Decimal,System.Decimal,System.Decimal)">
            <summary>
            Initializes a new instance of the <see cref="T:PuppeteerSharp.BoundingBox"/> class.
            </summary>
            <param name="x">The x coordinate.</param>
            <param name="y">The y coordinate.</param>
            <param name="width">Width.</param>
            <param name="height">Height.</param>
        </member>
        <member name="M:PuppeteerSharp.BoundingBox.Equals(System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:PuppeteerSharp.BoundingBox.Equals(PuppeteerSharp.BoundingBox)">
            <summary>
            Determines whether the specified <see cref="T:PuppeteerSharp.BoundingBox"/> is equal to the current <see cref="T:PuppeteerSharp.BoundingBox"/>.
            </summary>
            <param name="obj">The <see cref="T:PuppeteerSharp.BoundingBox"/> to compare with the current <see cref="T:PuppeteerSharp.BoundingBox"/>.</param>
            <returns><c>true</c> if the specified <see cref="T:PuppeteerSharp.BoundingBox"/> is equal to the current
            <see cref="T:PuppeteerSharp.BoundingBox"/>; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:PuppeteerSharp.BoundingBox.GetHashCode">
            <inheritdoc/>
        </member>
        <member name="T:PuppeteerSharp.BoxModel">
            <summary>
            Represents boxes of the element.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.BoxModel.Content">
            <summary>
            Gets the Content box
            </summary>
        </member>
        <member name="P:PuppeteerSharp.BoxModel.Padding">
            <summary>
            Gets the Padding box
            </summary>
        </member>
        <member name="P:PuppeteerSharp.BoxModel.Border">
            <summary>
            Gets the Border box
            </summary>
        </member>
        <member name="P:PuppeteerSharp.BoxModel.Margin">
            <summary>
            Gets the Margin box
            </summary>
        </member>
        <member name="P:PuppeteerSharp.BoxModel.Width">
            <summary>
            Gets the element's width
            </summary>
        </member>
        <member name="P:PuppeteerSharp.BoxModel.Height">
            <summary>
            Gets the element's height
            </summary>
        </member>
        <member name="T:PuppeteerSharp.BoxModelPoint">
            <summary>
            Represents a <see cref="T:PuppeteerSharp.BoxModel"/> point
            </summary>
        </member>
        <member name="P:PuppeteerSharp.BoxModelPoint.X">
            <summary>
            Gets the X point
            </summary>
        </member>
        <member name="P:PuppeteerSharp.BoxModelPoint.Y">
            <summary>
            Gets the y point
            </summary>
        </member>
        <member name="T:PuppeteerSharp.Browser">
            <summary>
            Provides methods to interact with a browser in Chromium.
            </summary>
            <example>
            An example of using a <see cref="T:PuppeteerSharp.Browser"/> to create a <see cref="T:PuppeteerSharp.Page"/>:
            <code>
            <![CDATA[
            var browser = await Puppeteer.LaunchAsync(new LaunchOptions());
            var page = await browser.NewPageAsync();
            await page.GoToAsync("https://example.com");
            await browser.CloseAsync();
            ]]>
            </code>
            An example of disconnecting from and reconnecting to a <see cref="T:PuppeteerSharp.Browser"/>:
            <code>
            <![CDATA[
            var browser = await Puppeteer.LaunchAsync(new LaunchOptions());
            var browserWSEndpoint = browser.WebSocketEndpoint;
            browser.Disconnect();
            var browser2 = await Puppeteer.ConnectAsync(new ConnectOptions { BrowserWSEndpoint = browserWSEndpoint });
            await browser2.CloseAsync();
            ]]>
            </code>
            </example>
        </member>
        <member name="F:PuppeteerSharp.Browser.CloseTimeout">
            <summary>
            Time in milliseconds for chromium process to exit gracefully.
            </summary>
        </member>
        <member name="M:PuppeteerSharp.Browser.#ctor(PuppeteerSharp.Connection,System.String[],System.Boolean,PuppeteerSharp.ViewPortOptions,PuppeteerSharp.ChromiumProcess)">
            <summary>
            Initializes a new instance of the <see cref="T:PuppeteerSharp.Browser"/> class.
            </summary>
            <param name="connection">The connection</param>
            <param name="contextIds">The context ids></param>
            <param name="ignoreHTTPSErrors">The option to ignoreHTTPSErrors</param>
            <param name="defaultViewport">Default viewport</param>
            <param name="chromiumProcess">The Chromium process</param>
        </member>
        <member name="E:PuppeteerSharp.Browser.Closed">
            <summary>
            
            </summary>
        </member>
        <member name="E:PuppeteerSharp.Browser.Disconnected">
            <summary>
            Raised when puppeteer gets disconnected from the Chromium instance. This might happen because one of the following
            - Chromium is closed or crashed
            - <see cref="M:PuppeteerSharp.Browser.Disconnect"/> method was called
            </summary>
        </member>
        <member name="E:PuppeteerSharp.Browser.TargetChanged">
            <summary>
            Raised when the url of a target changes
            </summary>
        </member>
        <member name="E:PuppeteerSharp.Browser.TargetCreated">
            <summary>
            Raised when a target is created, for example when a new page is opened by <c>window.open</c> <see href="https://developer.mozilla.org/en-US/docs/Web/API/Window/open"/> or <see cref="M:PuppeteerSharp.Browser.NewPageAsync"/>.
            </summary>
        </member>
        <member name="E:PuppeteerSharp.Browser.TargetDestroyed">
            <summary>
            Raised when a target is destroyed, for example when a page is closed
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Browser.WebSocketEndpoint">
            <summary>
            Gets the Browser websocket url
            </summary>
            <remarks>
            Browser websocket endpoint which can be used as an argument to <see cref="M:PuppeteerSharp.Puppeteer.ConnectAsync(PuppeteerSharp.ConnectOptions,Microsoft.Extensions.Logging.ILoggerFactory)"/>.
            The format is <c>ws://${host}:${port}/devtools/browser/[id]</c>
            You can find the <c>webSocketDebuggerUrl</c> from <c>http://${host}:${port}/json/version</c>.
            Learn more about the devtools protocol <see href="https://chromedevtools.github.io/devtools-protocol"/> 
            and the browser endpoint <see href="https://chromedevtools.github.io/devtools-protocol/#how-do-i-access-the-browser-target"/>
            </remarks>
        </member>
        <member name="P:PuppeteerSharp.Browser.Process">
            <summary>
            Gets the spawned browser process. Returns <c>null</c> if the browser instance was created with <see cref="M:PuppeteerSharp.Puppeteer.ConnectAsync(PuppeteerSharp.ConnectOptions,Microsoft.Extensions.Logging.ILoggerFactory)"/> method.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Browser.IgnoreHTTPSErrors">
            <summary>
            Gets or Sets whether to ignore HTTPS errors during navigation
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Browser.IsClosed">
            <summary>
            Gets a value indicating if the browser is closed
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Browser.DefaultContext">
            <summary>
            Returns the default browser context. The default browser context can not be closed.
            </summary>
            <value>The default context.</value>
        </member>
        <member name="P:PuppeteerSharp.Browser.DefaultWaitForTimeout">
            <summary>
            Dafault wait time in milliseconds. Defaults to 30 seconds.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Browser.IsConnected">
            <summary>
            Indicates that the browser is connected.
            </summary>
        </member>
        <member name="M:PuppeteerSharp.Browser.NewPageAsync">
            <summary>
            Creates a new page
            </summary>
            <returns>Task which resolves to a new <see cref="T:PuppeteerSharp.Page"/> object</returns>
        </member>
        <member name="M:PuppeteerSharp.Browser.Targets">
            <summary>
            Returns An Array of all active targets
            </summary>
            <returns>An Array of all active targets</returns>
        </member>
        <member name="P:PuppeteerSharp.Browser.Target">
            <summary>
            A target associated with the browser.
            </summary>
        </member>
        <member name="M:PuppeteerSharp.Browser.CreateIncognitoBrowserContextAsync">
            <summary>
            Creates a new incognito browser context. This won't share cookies/cache with other browser contexts.
            </summary>
            <returns>Task which resolves to a new <see cref="T:PuppeteerSharp.BrowserContext"/> object</returns>
            <example>
            <code>
            <![CDATA[
            using(var browser = await Puppeteer.LaunchAsync(new LaunchOptions()))
            {
                // Create a new incognito browser context.
                var context = await browser.CreateIncognitoBrowserContextAsync();
                // Create a new page in a pristine context.
                var page = await context.NewPageAsync();
                // Do stuff
                await page.GoToAsync("https://example.com");
            }
            ]]>
            </code>
            </example>
        </member>
        <member name="M:PuppeteerSharp.Browser.BrowserContexts">
            <summary>
            Returns an array of all open <see cref="T:PuppeteerSharp.BrowserContext"/>. In a newly created browser, this will return a single instance of <see cref="T:PuppeteerSharp.BrowserContext"/>
            </summary>
            <returns>An array of <see cref="T:PuppeteerSharp.BrowserContext"/> objects</returns>
        </member>
        <member name="M:PuppeteerSharp.Browser.PagesAsync">
            <summary>
            Returns a Task which resolves to an array of all open pages.
            Non visible pages, such as <c>"background_page"</c>, will not be listed here. You can find them using <see cref="M:PuppeteerSharp.Target.PageAsync"/>
            </summary>
            <returns>Task which resolves to an array of all open pages inside the Browser. 
            In case of multiple browser contexts, the method will return an array with all the pages in all browser contexts.
            </returns>
        </member>
        <member name="M:PuppeteerSharp.Browser.GetVersionAsync">
            <summary>
            Gets the browser's version
            </summary>
            <returns>For headless Chromium, this is similar to <c>HeadlessChrome/61.0.3153.0</c>. For non-headless, this is similar to <c>Chrome/61.0.3153.0</c></returns>
            <remarks>
            the format of <see cref="M:PuppeteerSharp.Browser.GetVersionAsync"/> might change with future releases of Chromium
            </remarks>
        </member>
        <member name="M:PuppeteerSharp.Browser.GetUserAgentAsync">
            <summary>
            Gets the browser's original user agent
            </summary>
            <returns>Task which resolves to the browser's original user agent</returns>
            <remarks>
            Pages can override browser user agent with <see cref="M:PuppeteerSharp.Page.SetUserAgentAsync(System.String)"/>
            </remarks>
        </member>
        <member name="M:PuppeteerSharp.Browser.Disconnect">
            <summary>
            Disconnects Puppeteer from the browser, but leaves the Chromium process running. After calling <see cref="M:PuppeteerSharp.Browser.Disconnect"/>, the browser object is considered disposed and cannot be used anymore
            </summary>
        </member>
        <member name="M:PuppeteerSharp.Browser.CloseAsync">
            <summary>
            Closes Chromium and all of its pages (if any were opened). The browser object itself is considered disposed and cannot be used anymore
            </summary>
            <returns>Task</returns>
        </member>
        <member name="M:PuppeteerSharp.Browser.WaitForTargetAsync(System.Func{PuppeteerSharp.Target,System.Boolean},PuppeteerSharp.WaitForOptions)">
            <summary>
            This searches for a target in this specific browser context.
            <example>
            <code>
            <![CDATA[
            await page.EvaluateAsync("() => window.open('https://www.example.com/')");
            var newWindowTarget = await browserContext.WaitForTargetAsync((target) => target.Url == "https://www.example.com/");
            ]]>
            </code>
            </example>
            </summary>
            <param name="predicate">A function to be run for every target</param>
            <param name="options">options</param>
            <returns>Resolves to the first target found that matches the predicate function.</returns>
        </member>
        <member name="M:PuppeteerSharp.Browser.Dispose">
            <summary>
            Closes <see cref="P:PuppeteerSharp.Browser.Connection"/> and any Chromium <see cref="P:PuppeteerSharp.Browser.Process"/> that was
            created by Puppeteer.
            </summary>
        </member>
        <member name="T:PuppeteerSharp.BrowserContext">
            <summary>
            BrowserContexts provide a way to operate multiple independent browser sessions. When a browser is launched, it has
            a single <see cref="T:PuppeteerSharp.BrowserContext"/> used by default. The method <see cref="M:PuppeteerSharp.Browser.NewPageAsync"/> creates a <see cref="T:PuppeteerSharp.Page"/> in the default <see cref="T:PuppeteerSharp.BrowserContext"/>
            </summary>
        </member>
        <member name="E:PuppeteerSharp.BrowserContext.TargetChanged">
            <summary>
            Raised when the url of a target changes
            </summary>
        </member>
        <member name="E:PuppeteerSharp.BrowserContext.TargetCreated">
            <summary>
            Raised when a target is created, for example when a new page is opened by <c>window.open</c> <see href="https://developer.mozilla.org/en-US/docs/Web/API/Window/open"/> or <see cref="M:PuppeteerSharp.BrowserContext.NewPageAsync"/>.
            </summary>
        </member>
        <member name="E:PuppeteerSharp.BrowserContext.TargetDestroyed">
            <summary>
            Raised when a target is destroyed, for example when a page is closed
            </summary>
        </member>
        <member name="P:PuppeteerSharp.BrowserContext.IsIncognito">
            <summary>
            Returns whether BrowserContext is incognito
            The default browser context is the only non-incognito browser context
            </summary>
            <remarks>
            The default browser context cannot be closed
            </remarks>
        </member>
        <member name="P:PuppeteerSharp.BrowserContext.Browser">
            <summary>
            Gets the browser this browser context belongs to
            </summary>
        </member>
        <member name="M:PuppeteerSharp.BrowserContext.Targets">
            <summary>
            Gets an array of all active targets inside the browser context 
            </summary>
            <returns>An array of all active targets inside the browser context</returns>
        </member>
        <member name="M:PuppeteerSharp.BrowserContext.WaitForTargetAsync(System.Func{PuppeteerSharp.Target,System.Boolean},PuppeteerSharp.WaitForOptions)">
            <summary>
            This searches for a target in this specific browser context.
            <example>
            <code>
            <![CDATA[
            await page.EvaluateAsync("() => window.open('https://www.example.com/')");
            var newWindowTarget = await browserContext.WaitForTargetAsync((target) => target.Url == "https://www.example.com/");
            ]]>
            </code>
            </example>
            </summary>
            <param name="predicate">A function to be run for every target</param>
            <param name="options">options</param>
            <returns>Resolves to the first target found that matches the predicate function.</returns>
        </member>
        <member name="M:PuppeteerSharp.BrowserContext.PagesAsync">
            <summary>
            An array of all pages inside the browser context.
            </summary>
            <returns>Task which resolves to an array of all open pages. 
            Non visible pages, such as <c>"background_page"</c>, will not be listed here. 
            You can find them using <see cref="M:PuppeteerSharp.Target.PageAsync"/>.</returns>
        </member>
        <member name="M:PuppeteerSharp.BrowserContext.NewPageAsync">
            <summary>
            Creates a new page
            </summary>
            <returns>Task which resolves to a new <see cref="T:PuppeteerSharp.Page"/> object</returns>
        </member>
        <member name="M:PuppeteerSharp.BrowserContext.CloseAsync">
            <summary>
            Closes the browser context. All the targets that belong to the browser context will be closed
            </summary>
            <returns>Task</returns>
        </member>
        <member name="M:PuppeteerSharp.BrowserContext.OverridePermissionsAsync(System.String,System.Collections.Generic.IEnumerable{PuppeteerSharp.OverridePermission})">
            <summary>
            Overrides the browser context permissions.
            </summary>
            <returns>The task.</returns>
            <param name="origin">The origin to grant permissions to, e.g. "https://example.com"</param>
            <param name="permissions">
            An array of permissions to grant. All permissions that are not listed here will be automatically denied.
            </param>
            <example>
            <![CDATA[
            var context = browser.DefaultBrowserContext;
            await context.OverridePermissionsAsync("https://html5demos.com", new List<string> {"geolocation"});
            ]]>
            </example>
            <seealso href="https://developer.mozilla.org/en-US/docs/Glossary/Origin"/>
        </member>
        <member name="M:PuppeteerSharp.BrowserContext.ClearPermissionOverridesAsync">
            <summary>
            Clears all permission overrides for the browser context.
            </summary>
            <returns>The task.</returns>
        </member>
        <member name="T:PuppeteerSharp.BrowserFetcher">
            <summary>
            BrowserFetcher can download and manage different versions of Chromium.
            BrowserFetcher operates on revision strings that specify a precise version of Chromium, e.g. 533271. Revision strings can be obtained from omahaproxy.appspot.com.
            </summary>
            <example>
            Example on how to use BrowserFetcher to download a specific version of Chromium and run Puppeteer against it:
            <code>
            var browserFetcher = Puppeteer.CreateBrowserFetcher();
            var revisionInfo = await browserFetcher.DownloadAsync(533271);
            var browser = await await Puppeteer.LaunchAsync(new LaunchOptions { ExecutablePath = revisionInfo.ExecutablePath});
            </code>
            </example>
        </member>
        <member name="F:PuppeteerSharp.BrowserFetcher.DefaultRevision">
            <summary>
            Default Chromium revision.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.BrowserFetcher.DownloadsFolder">
            <summary>
            Gets the downloads folder.
            </summary>
            <value>The downloads folder.</value>
        </member>
        <member name="P:PuppeteerSharp.BrowserFetcher.DownloadHost">
            <summary>
            A download host to be used. Defaults to https://storage.googleapis.com.
            </summary>
            <value>The download host.</value>
        </member>
        <member name="P:PuppeteerSharp.BrowserFetcher.Platform">
            <summary>
            Gets the platform.
            </summary>
            <value>The platform.</value>
        </member>
        <member name="E:PuppeteerSharp.BrowserFetcher.DownloadProgressChanged">
            <summary>
            Occurs when download progress in <see cref="M:PuppeteerSharp.BrowserFetcher.DownloadAsync(System.Int32)"/> changes.
            </summary>
        </member>
        <member name="M:PuppeteerSharp.BrowserFetcher.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:PuppeteerSharp.BrowserFetcher"/> class.
            </summary>
        </member>
        <member name="M:PuppeteerSharp.BrowserFetcher.#ctor(PuppeteerSharp.BrowserFetcherOptions)">
            <summary>
            Initializes a new instance of the <see cref="T:PuppeteerSharp.BrowserFetcher"/> class.
            </summary>
            <param name="options">Fetch options.</param>
        </member>
        <member name="M:PuppeteerSharp.BrowserFetcher.CanDownloadAsync(System.Int32)">
            <summary>
            The method initiates a HEAD request to check if the revision is available.
            </summary>
            <returns>Whether the version is available or not.</returns>
            <param name="revision">A revision to check availability.</param>
        </member>
        <member name="M:PuppeteerSharp.BrowserFetcher.LocalRevisions">
            <summary>
            A list of all revisions available locally on disk.
            </summary>
            <returns>The available revisions.</returns>
        </member>
        <member name="M:PuppeteerSharp.BrowserFetcher.Remove(System.Int32)">
            <summary>
            Removes a downloaded revision.
            </summary>
            <param name="revision">Revision to remove.</param>
        </member>
        <member name="M:PuppeteerSharp.BrowserFetcher.RevisionInfo(System.Int32)">
            <summary>
            Gets the revision info.
            </summary>
            <returns>Revision info.</returns>
            <param name="revision">A revision to get info for.</param>
        </member>
        <member name="M:PuppeteerSharp.BrowserFetcher.DownloadAsync(System.Int32)">
            <summary>
            Downloads the revision.
            </summary>
            <returns>Task which resolves to the completed download.</returns>
            <param name="revision">Revision.</param>
        </member>
        <member name="M:PuppeteerSharp.BrowserFetcher.GetExecutablePath(System.Int32)">
            <summary>
            Gets the executable path for a revision.
            </summary>
            <returns>The executable path.</returns>
            <param name="revision">Revision.</param>
        </member>
        <member name="M:PuppeteerSharp.BrowserFetcher.GetExecutablePath(PuppeteerSharp.Platform,System.Int32,System.String)">
            <summary>
            Gets the executable path.
            </summary>
            <returns>The executable path.</returns>
            <param name="platform">Platform.</param>
            <param name="revision">Revision.</param>
            <param name="folderPath">Folder path.</param>
        </member>
        <member name="T:PuppeteerSharp.BrowserFetcherOptions">
            <summary>
            Browser fetcher options used to construct a <see cref="T:PuppeteerSharp.BrowserFetcher"/>
            </summary>
        </member>
        <member name="P:PuppeteerSharp.BrowserFetcherOptions.Platform">
            <summary>
            Platform, defaults to currenct platform.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.BrowserFetcherOptions.Path">
            <summary>
            A path for the downloads folder. Defaults to [root]/.local-chromium, where [root] is where the project binaries are located.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.BrowserFetcherOptions.Host">
            <summary>
            A download host to be used. Defaults to https://storage.googleapis.com.
            </summary>
        </member>
        <member name="T:PuppeteerSharp.CDPSession">
            <summary>
            The CDPSession instances are used to talk raw Chrome Devtools Protocol:
             * Protocol methods can be called with <see cref="M:PuppeteerSharp.CDPSession.SendAsync(System.String,System.Object,System.Boolean)"/> method.
             * Protocol events, using the <see cref="E:PuppeteerSharp.CDPSession.MessageReceived"/> event.
            
            Documentation on DevTools Protocol can be found here: <see href="https://chromedevtools.github.io/devtools-protocol/"/>.
            
            <code>
            <![CDATA[
            var client = await Page.Target.CreateCDPSessionAsync();
            await client.SendAsync("Animation.enable");
            client.MessageReceived += (sender, e) =>
            {
                 if (e.MessageID == "Animation.animationCreated")
                 {
                     Console.WriteLine("Animation created!");
                 }
            };
            JObject response = await client.SendAsync("Animation.getPlaybackRate");
            Console.WriteLine("playback rate is " + response.playbackRate);
            await client.SendAsync("Animation.setPlaybackRate", new
            {
                playbackRate = Convert.ToInt32(response.playbackRate / 2)
            });
            ]]></code>
            </summary>
        </member>
        <member name="P:PuppeteerSharp.CDPSession.TargetType">
            <summary>
            Gets the target type.
            </summary>
            <value>The target type.</value>
        </member>
        <member name="P:PuppeteerSharp.CDPSession.SessionId">
            <summary>
            Gets the session identifier.
            </summary>
            <value>The session identifier.</value>
        </member>
        <member name="P:PuppeteerSharp.CDPSession.Connection">
            <summary>
            Gets the connection.
            </summary>
            <value>The connection.</value>
        </member>
        <member name="E:PuppeteerSharp.CDPSession.MessageReceived">
            <summary>
            Occurs when message received from Chromium.
            </summary>
        </member>
        <member name="E:PuppeteerSharp.CDPSession.Disconnected">
            <summary>
            Occurs when the connection is closed.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.CDPSession.IsClosed">
            <summary>
            Gets or sets a value indicating whether this <see cref="T:PuppeteerSharp.CDPSession"/> is closed.
            </summary>
            <value><c>true</c> if is closed; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:PuppeteerSharp.CDPSession.CloseReason">
            <summary>
            Connection close reason.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.CDPSession.LoggerFactory">
            <summary>
            Gets the logger factory.
            </summary>
            <value>The logger factory.</value>
        </member>
        <member name="M:PuppeteerSharp.CDPSession.SendAsync``1(System.String,System.Object)">
            <summary>
            Protocol methods can be called with this method.
            </summary>
            <param name="method">The method name</param>
            <param name="args">The method args</param>
            <returns>The task.</returns>
        </member>
        <member name="M:PuppeteerSharp.CDPSession.SendAsync(System.String,System.Object,System.Boolean)">
            <summary>
            Protocol methods can be called with this method.
            </summary>
            <param name="method">The method name</param>
            <param name="args">The method args</param>
            <param name="waitForCallback">
            If <c>true</c> the method will return a task to be completed when the message is confirmed by Chromium.
            If <c>false</c> the task will be considered complete after sending the message to Chromium.
            </param>
            <returns>The task.</returns>
            <exception cref="T:PuppeteerSharp.PuppeteerException"></exception>
        </member>
        <member name="M:PuppeteerSharp.CDPSession.DetachAsync">
            <summary>
            Detaches session from target. Once detached, session won't emit any events and can't be used to send messages.
            </summary>
            <returns></returns>
            <exception cref="T:PuppeteerSharp.PuppeteerException"></exception>
        </member>
        <member name="T:PuppeteerSharp.ChromiumProcess">
            <summary>
            Represents a Chromium process and any associated temporary user data directory that have created
            by Puppeteer and therefore must be cleaned up when no longer needed.
            </summary>
        </member>
        <member name="M:PuppeteerSharp.ChromiumProcess.#ctor(System.String,PuppeteerSharp.LaunchOptions,Microsoft.Extensions.Logging.ILoggerFactory)">
            <summary>
            Creates a new <see cref="T:PuppeteerSharp.ChromiumProcess"/> instance.
            </summary>
            <param name="chromiumExecutable">Full path of Chromium executable.</param>
            <param name="options">Options for launching Chromium.</param>
            <param name="loggerFactory">Logger factory</param>
        </member>
        <member name="M:PuppeteerSharp.ChromiumProcess.Finalize">
            <summary>
            Finalizer.
            </summary>
        </member>
        <member name="M:PuppeteerSharp.ChromiumProcess.Dispose">
            <inheritdoc />
        </member>
        <member name="M:PuppeteerSharp.ChromiumProcess.Dispose(System.Boolean)">
            <summary>
            Disposes Chromium process and any temporary user directory.
            </summary>
            <param name="disposing">Indicates whether disposal was initiated by <see cref="M:PuppeteerSharp.ChromiumProcess.Dispose"/> operation.</param>
        </member>
        <member name="P:PuppeteerSharp.ChromiumProcess.Process">
            <summary>
            Gets Chromium process details.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.ChromiumProcess.EndPoint">
            <summary>
            Gets Chromium endpoint.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.ChromiumProcess.IsExiting">
            <summary>
            Indicates whether Chromium process is exiting. 
            </summary>
        </member>
        <member name="P:PuppeteerSharp.ChromiumProcess.HasExited">
            <summary>
            Indicates whether Chromium process has exited. 
            </summary>
        </member>
        <member name="M:PuppeteerSharp.ChromiumProcess.StartAsync">
            <summary>
            Asynchronously starts Chromium process.
            </summary>
            <returns></returns>
        </member>
        <member name="M:PuppeteerSharp.ChromiumProcess.EnsureExitAsync(System.Nullable{System.TimeSpan})">
            <summary>
            Asynchronously waits for graceful Chromium process exit within a given timeout period.
            Kills the Chromium process if it has not exited within this period.
            </summary>
            <param name="timeout">The maximum waiting time for a graceful process exit.</param>
            <returns></returns>
        </member>
        <member name="M:PuppeteerSharp.ChromiumProcess.KillAsync">
            <summary>
            Asynchronously kills Chromium process.
            </summary>
            <returns></returns>
        </member>
        <member name="M:PuppeteerSharp.ChromiumProcess.WaitForExitAsync(System.Nullable{System.TimeSpan})">
            <summary>
            Waits for Chromium process exit within a given timeout.
            </summary>
            <param name="timeout">The maximum wait period.</param>
            <returns><c>true</c> if Chromium process has exited within the given <paramref name="timeout"/>,
            or <c>false</c> otherwise.</returns>
        </member>
        <member name="M:PuppeteerSharp.ChromiumProcess.ToString">
            <inheritdoc />
        </member>
        <member name="T:PuppeteerSharp.ChromiumProcess.State">
            <summary>
            Represents state machine for Chromium process instances. The happy path runs along the
            following state transitions: <see cref="F:PuppeteerSharp.ChromiumProcess.State.Initial"/>
            -> <see cref="F:PuppeteerSharp.ChromiumProcess.State.Starting"/>
            -> <see cref="F:PuppeteerSharp.ChromiumProcess.State.Started"/>
            -> <see cref="F:PuppeteerSharp.ChromiumProcess.State.Exiting"/>
            -> <see cref="F:PuppeteerSharp.ChromiumProcess.State.Exited"/>.
            -> <see cref="F:PuppeteerSharp.ChromiumProcess.State.Disposed"/>.
            </summary>
            <remarks>
            <para>
            This state machine implements the following state transitions:
            <code>
            State     Event              Target State Action
            ======== =================== ============ ==========================================================
            Initial  --StartAsync------> Starting     Start process and wait for endpoint
            Initial  --ExitAsync-------> Exited       Cleanup temp user data
            Initial  --KillAsync-------> Exited       Cleanup temp user data
            Initial  --Dispose---------> Disposed     Cleanup temp user data
            Starting --StartAsync------> Starting     -
            Starting --ExitAsync-------> Exiting      Wait for process exit
            Starting --KillAsync-------> Killing      Kill process
            Starting --Dispose---------> Disposed     Kill process; Cleanup temp user data;  throw ObjectDisposedException on outstanding async operations;
            Starting --endpoint ready--> Started      Complete StartAsync successfully; Log process start
            Starting --process exit----> Exited       Complete StartAsync with exception; Cleanup temp user data
            Started  --StartAsync------> Started      -
            Started  --EnsureExitAsync-> Exiting      Start exit timer; Log process exit
            Started  --KillAsync-------> Killing      Kill process; Log process exit
            Started  --Dispose---------> Disposed     Kill process; Log process exit; Cleanup temp user data; throw ObjectDisposedException on outstanding async operations;
            Started  --process exit----> Exited       Log process exit; Cleanup temp user data
            Exiting  --StartAsync------> Exiting      - (StartAsync throws InvalidOperationException)
            Exiting  --ExitAsync-------> Exiting      -
            Exiting  --KillAsync-------> Killing      Kill process
            Exiting  --Dispose---------> Disposed     Kill process; Cleanup temp user data; throw ObjectDisposedException on outstanding async operations;
            Exiting  --exit timeout----> Killing      Kill process
            Exiting  --process exit----> Exited       Cleanup temp user data; complete outstanding async operations;
            Killing  --StartAsync------> Killing      - (StartAsync throws InvalidOperationException)
            Killing  --KillAsync-------> Killing      -
            Killing  --Dispose---------> Disposed     Cleanup temp user data; throw ObjectDisposedException on outstanding async operations;
            Killing  --process exit----> Exited       Cleanup temp user data; complete outstanding async operations;
            Exited   --StartAsync------> Killing      - (StartAsync throws InvalidOperationException)
            Exited   --KillAsync-------> Exited       -
            Exited   --Dispose---------> Disposed     -
            Disposed --StartAsync------> Disposed     -
            Disposed --KillAsync-------> Disposed     -
            Disposed --Dispose---------> Disposed     -
            </code>
            </para>
            </remarks>
        </member>
        <member name="M:PuppeteerSharp.ChromiumProcess.State.TryEnter(PuppeteerSharp.ChromiumProcess,PuppeteerSharp.ChromiumProcess.State)">
            <summary>
            Attempts thread-safe transitions from a given state to this state.
            </summary>
            <param name="p">The Chromium process</param>
            <param name="fromState">The state from which state transition takes place</param>
            <returns>Returns <c>true</c> if transition is successful, or <c>false</c> if transition
            cannot be made because current state does not equal <paramref name="fromState"/>.</returns>
        </member>
        <member name="M:PuppeteerSharp.ChromiumProcess.State.Leave(PuppeteerSharp.ChromiumProcess)">
            <summary>
            Notifies that state machine is about to transition to another state.
            </summary>
            <param name="p">The Chromium process</param>
        </member>
        <member name="M:PuppeteerSharp.ChromiumProcess.State.StartAsync(PuppeteerSharp.ChromiumProcess)">
            <summary>
            Handles process start request.
            </summary>
            <param name="p">The Chromium process</param>
            <returns></returns>
        </member>
        <member name="M:PuppeteerSharp.ChromiumProcess.State.ExitAsync(PuppeteerSharp.ChromiumProcess,System.TimeSpan)">
            <summary>
            Handles process exit request.
            </summary>
            <param name="p">The Chromium process</param>
            <param name="timeout">The maximum waiting time for a graceful process exit.</param>
            <returns></returns>
        </member>
        <member name="M:PuppeteerSharp.ChromiumProcess.State.KillAsync(PuppeteerSharp.ChromiumProcess)">
            <summary>
            Handles process kill request.
            </summary>
            <param name="p">The Chromium process</param>
            <returns></returns>
        </member>
        <member name="M:PuppeteerSharp.ChromiumProcess.State.WaitForExitAsync(PuppeteerSharp.ChromiumProcess)">
            <summary>
            Handles wait for process exit request.
            </summary>
            <param name="p">The Chromium process</param>
            <returns></returns>
        </member>
        <member name="M:PuppeteerSharp.ChromiumProcess.State.Dispose(PuppeteerSharp.ChromiumProcess)">
            <summary>
            Handles disposal of process and temporary user directory
            </summary>
            <param name="p"></param>
        </member>
        <member name="M:PuppeteerSharp.ChromiumProcess.State.Kill(PuppeteerSharp.ChromiumProcess)">
            <summary>
            Kills process if it is still alive.
            </summary>
            <param name="p"></param>
        </member>
        <member name="T:PuppeteerSharp.ChromiumProcessException">
            <summary>
            Chromium process exception thrown by <see cref="T:PuppeteerSharp.Launcher"/>.
            </summary>
        </member>
        <member name="M:PuppeteerSharp.ChromiumProcessException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:PuppeteerSharp.ChromiumProcessException"/> class.
            </summary>
            <param name="message">Message.</param>
        </member>
        <member name="M:PuppeteerSharp.ChromiumProcessException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:PuppeteerSharp.ChromiumProcessException"/> class.
            </summary>
            <param name="message">Message.</param>
            <param name="innerException">Inner exception.</param>
        </member>
        <member name="T:PuppeteerSharp.Connection">
            <summary>
            A connection handles the communication with a Chromium browser
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Connection.Url">
            <summary>
            Gets the WebSocket URL.
            </summary>
            <value>The URL.</value>
        </member>
        <member name="P:PuppeteerSharp.Connection.Delay">
            <summary>
            Gets the sleep time when a message is received.
            </summary>
            <value>The delay.</value>
        </member>
        <member name="P:PuppeteerSharp.Connection.Transport">
            <summary>
            Gets the Connection transport.
            </summary>
            <value>Connection transport.</value>
        </member>
        <member name="E:PuppeteerSharp.Connection.Disconnected">
            <summary>
            Occurs when the connection is closed.
            </summary>
        </member>
        <member name="E:PuppeteerSharp.Connection.MessageReceived">
            <summary>
            Occurs when a message from chromium is received.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Connection.IsClosed">
            <summary>
            Gets or sets a value indicating whether this <see cref="T:PuppeteerSharp.Connection"/> is closed.
            </summary>
            <value><c>true</c> if is closed; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:PuppeteerSharp.Connection.CloseReason">
            <summary>
            Connection close reason.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Connection.LoggerFactory">
            <summary>
            Gets the logger factory.
            </summary>
            <value>The logger factory.</value>
        </member>
        <member name="F:PuppeteerSharp.Connection.DefaultWebSocketFactory">
            <summary>
            Gets default web socket factory implementation.
            </summary>
        </member>
        <member name="M:PuppeteerSharp.Connection.Dispose">
            <summary>
            Releases all resource used by the <see cref="T:PuppeteerSharp.Connection"/> object.
            It will raise the <see cref="E:PuppeteerSharp.Connection.Disconnected"/> event and dispose <see cref="P:PuppeteerSharp.Connection.Transport"/>.
            </summary>
            <remarks>Call <see cref="M:PuppeteerSharp.Connection.Dispose"/> when you are finished using the <see cref="T:PuppeteerSharp.Connection"/>. The
            <see cref="M:PuppeteerSharp.Connection.Dispose"/> method leaves the <see cref="T:PuppeteerSharp.Connection"/> in an unusable state.
            After calling <see cref="M:PuppeteerSharp.Connection.Dispose"/>, you must release all references to the
            <see cref="T:PuppeteerSharp.Connection"/> so the garbage collector can reclaim the memory that the
            <see cref="T:PuppeteerSharp.Connection"/> was occupying.</remarks>
        </member>
        <member name="T:PuppeteerSharp.ConnectOptions">
            <summary>
            Options for connecting to an existing browser.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.ConnectOptions.IgnoreHTTPSErrors">
            <summary>
            Whether to ignore HTTPS errors during navigation. Defaults to false.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.ConnectOptions.AppMode">
            <summary>
            If set to true, sets Headless = false, otherwise, enables automation.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.ConnectOptions.BrowserWSEndpoint">
            <summary>
            A browser websocket endpoint to connect to.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.ConnectOptions.BrowserURL">
            <summary>
            A browser url to connect to, in format `http://${host}:${port}`.
            Use interchangeably with `browserWSEndpoint` to let Puppeteer fetch it from <see href="https://chromedevtools.github.io/devtools-protocol/#how-do-i-access-the-browser-target">metadata endpoin</see>.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.ConnectOptions.SlowMo">
            <summary>
            Slows down Puppeteer operations by the specified amount of milliseconds. Useful so that you can see what is going on.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.ConnectOptions.KeepAliveInterval">
            <summary>
            Keep alive value.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.ConnectOptions.WebSocketFactory">
            <summary>
            Optional factory for <see cref="T:System.Net.WebSockets.WebSocket"/> implementations.
            If <see cref="P:PuppeteerSharp.ConnectOptions.Transport"/> is set this property will be ignored.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.ConnectOptions.DefaultViewport">
            <summary>
            Gets or sets the default Viewport.
            </summary>
            <value>The default Viewport.</value>
        </member>
        <member name="P:PuppeteerSharp.ConnectOptions.Transport">
            <summary>
            Optional connection transport.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.ConnectOptions.TransportFactory">
            <summary>
            Optional factory for <see cref="T:PuppeteerSharp.Transport.IConnectionTransport"/> implementations.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.ConnectOptions.EnqueueTransportMessages">
            <summary>
            If not <see cref="P:PuppeteerSharp.ConnectOptions.Transport"/> is set this will be use to determine is the default <see cref="T:PuppeteerSharp.Transport.WebSocketTransport"/> will enqueue messages.
            </summary>
            <remarks>
            It's set to <c>true</c> by default because it's the safest way to send commands to Chromium.
            Setting this to <c>false</c> proved to work in .NET Core but it tends to fail on .NET Framework.
            </remarks>
        </member>
        <member name="T:PuppeteerSharp.ConsoleEventArgs">
            <summary>
            <see cref="E:PuppeteerSharp.Page.Console"/> data.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.ConsoleEventArgs.Message">
            <summary>
            Gets the message.
            </summary>
            <value>The message.</value>
        </member>
        <member name="M:PuppeteerSharp.ConsoleEventArgs.#ctor(PuppeteerSharp.ConsoleMessage)">
            <summary>
            Initializes a new instance of the <see cref="T:PuppeteerSharp.ConsoleEventArgs"/> class.
            </summary>
            <param name="message">Message.</param>
        </member>
        <member name="T:PuppeteerSharp.ConsoleMessage">
            <summary>
            ConsoleMessage is part of <see cref="T:PuppeteerSharp.ConsoleEventArgs"/> used by <see cref="E:PuppeteerSharp.Page.Console"/>
            </summary>
        </member>
        <member name="P:PuppeteerSharp.ConsoleMessage.Type">
            <summary>
            Gets the ConsoleMessage type.
            </summary>
            <value>ConsoleMessageType.</value>
        </member>
        <member name="P:PuppeteerSharp.ConsoleMessage.Text">
            <summary>
            Gets the console text.
            </summary>
            <value>The text.</value>
        </member>
        <member name="P:PuppeteerSharp.ConsoleMessage.Args">
            <summary>
            Gets the arguments.
            </summary>
            <value>The arguments.</value>
        </member>
        <member name="P:PuppeteerSharp.ConsoleMessage.Location">
            <summary>
            Gets the location.
            </summary>
        </member>
        <member name="M:PuppeteerSharp.ConsoleMessage.#ctor(PuppeteerSharp.ConsoleType,System.String,System.Collections.Generic.IList{PuppeteerSharp.JSHandle},PuppeteerSharp.ConsoleMessageLocation)">
            <summary>
            Initializes a new instance of the <see cref="T:PuppeteerSharp.ConsoleMessage"/> class.
            </summary>
            <param name="type">Type.</param>
            <param name="text">Text.</param>
            <param name="args">Arguments.</param>
            <param name="location">Message location</param>
        </member>
        <member name="T:PuppeteerSharp.ConsoleMessageLocation">
            <summary>
            Console message location.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.ConsoleMessageLocation.URL">
            <summary>
            URL of the resource if known.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.ConsoleMessageLocation.LineNumber">
            <summary>
            0-based line number in the resource if known.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.ConsoleMessageLocation.ColumnNumber">
            <summary>
            0-based column number in the resource if known.
            </summary>
        </member>
        <member name="M:PuppeteerSharp.ConsoleMessageLocation.Equals(PuppeteerSharp.ConsoleMessageLocation)">
            <inheritdoc/>
        </member>
        <member name="M:PuppeteerSharp.ConsoleMessageLocation.Equals(System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:PuppeteerSharp.ConsoleMessageLocation.GetHashCode">
            <inheritdoc/>
        </member>
        <member name="M:PuppeteerSharp.ConsoleMessageLocation.op_Equality(PuppeteerSharp.ConsoleMessageLocation,PuppeteerSharp.ConsoleMessageLocation)">
            <inheritdoc/>
        </member>
        <member name="M:PuppeteerSharp.ConsoleMessageLocation.op_Inequality(PuppeteerSharp.ConsoleMessageLocation,PuppeteerSharp.ConsoleMessageLocation)">
            <inheritdoc/>
        </member>
        <member name="T:PuppeteerSharp.ConsoleType">
            <summary>
            Console type used on <see cref="T:PuppeteerSharp.ConsoleMessage"/>.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.ConsoleType.Log">
            <summary>
            Log.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.ConsoleType.Debug">
            <summary>
            Debug.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.ConsoleType.Info">
            <summary>
            Info.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.ConsoleType.Error">
            <summary>
            Error.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.ConsoleType.Warning">
            <summary>
            Warning.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.ConsoleType.Dir">
            <summary>
            Dir.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.ConsoleType.Dirxml">
            <summary>
            Dirxml.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.ConsoleType.Table">
            <summary>
            Table.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.ConsoleType.Trace">
            <summary>
            Trace.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.ConsoleType.Clear">
            <summary>
            Clear.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.ConsoleType.StartGroup">
            <summary>
            StartGroup.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.ConsoleType.StartGroupCollapsed">
            <summary>
            StartGroupCollapsed.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.ConsoleType.EndGroup">
            <summary>
            EndGroup.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.ConsoleType.Assert">
            <summary>
            Assert.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.ConsoleType.Profile">
            <summary>
            Profile.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.ConsoleType.ProfileEnd">
            <summary>
            ProfileEnd.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.ConsoleType.Count">
            <summary>
            Count.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.ConsoleType.TimeEnd">
            <summary>
            TimeEnd.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.ConsoleType.Verbose">
            <summary>
            Verbose.
            </summary>
        </member>
        <member name="T:PuppeteerSharp.CookieParam">
            <summary>
            Cookie data.
            </summary>
            <seealso cref="M:PuppeteerSharp.Page.SetContentAsync(System.String,PuppeteerSharp.NavigationOptions)"/>
            <seealso cref="M:PuppeteerSharp.Page.DeleteCookieAsync(PuppeteerSharp.CookieParam[])"/>
            <seealso cref="M:PuppeteerSharp.Page.GetCookiesAsync(System.String[])"/>
        </member>
        <member name="P:PuppeteerSharp.CookieParam.Name">
            <summary>
            Gets or sets the name.
            </summary>
            <value>The name.</value>
        </member>
        <member name="P:PuppeteerSharp.CookieParam.Value">
            <summary>
            Gets or sets the value.
            </summary>
            <value>The value.</value>
        </member>
        <member name="P:PuppeteerSharp.CookieParam.Domain">
            <summary>
            Gets or sets the domain.
            </summary>
            <value>The domain.</value>
        </member>
        <member name="P:PuppeteerSharp.CookieParam.Url">
            <summary>
            Gets or sets the URL.
            </summary>
            <value>The URL.</value>
        </member>
        <member name="P:PuppeteerSharp.CookieParam.Path">
            <summary>
            Gets or sets the path.
            </summary>
            <value>The path.</value>
        </member>
        <member name="P:PuppeteerSharp.CookieParam.Expires">
            <summary>
            Gets or sets the expiration.
            </summary>
            <value>Expiration.</value>
        </member>
        <member name="P:PuppeteerSharp.CookieParam.Size">
            <summary>
            Gets or sets the size.
            </summary>
            <value>The size.</value>
        </member>
        <member name="P:PuppeteerSharp.CookieParam.HttpOnly">
            <summary>
            Gets or sets if it's HTTP only.
            </summary>
            <value>Whether it's http only or not.</value>
        </member>
        <member name="P:PuppeteerSharp.CookieParam.Secure">
            <summary>
            Gets or sets if it's secure.
            </summary>
            <value>Whether it's secure or not.</value>
        </member>
        <member name="P:PuppeteerSharp.CookieParam.Session">
            <summary>
            Gets or sets if it's session only.
            </summary>
            <value>Whether it's session only or not.</value>
        </member>
        <member name="P:PuppeteerSharp.CookieParam.SameSite">
            <summary>
            Gets or sets the cookies SameSite value.
            </summary>
        </member>
        <member name="T:PuppeteerSharp.Credentials">
            <summary>
            Options to be used in <see cref="M:PuppeteerSharp.Page.AuthenticateAsync(PuppeteerSharp.Credentials)"/>
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Credentials.Username">
            <summary>
            Gets or sets the username to be used for authentication
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Credentials.Password">
            <summary>
            Gets or sets the password to be used for authentication
            </summary>
        </member>
        <member name="T:PuppeteerSharp.Dialog">
             <summary>
             <see cref="T:PuppeteerSharp.Dialog"/> objects are dispatched by page via the 'dialog' event.
             </summary>
             <example>
             An example of using Dialog class:
            <code>
            <![CDATA[
             Page.Dialog += async (sender, e) =>
             {
                 await e.Dialog.Accept();
             }
             await Page.EvaluateExpressionAsync("alert('yo');");
             ]]>
             </code>
             </example>
        </member>
        <member name="P:PuppeteerSharp.Dialog.DialogType">
            <summary>
            Dialog's type, can be one of alert, beforeunload, confirm or prompt.
            </summary>
            <value>The type of the dialog.</value>
        </member>
        <member name="P:PuppeteerSharp.Dialog.DefaultValue">
            <summary>
            If dialog is prompt, returns default prompt value. Otherwise, returns empty string.
            </summary>
            <value>The default value.</value>
        </member>
        <member name="P:PuppeteerSharp.Dialog.Message">
            <summary>
            A message displayed in the dialog.
            </summary>
            <value>The message.</value>
        </member>
        <member name="M:PuppeteerSharp.Dialog.#ctor(PuppeteerSharp.CDPSession,PuppeteerSharp.DialogType,System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:PuppeteerSharp.Dialog"/> class.
            </summary>
            <param name="client">Client.</param>
            <param name="type">Type.</param>
            <param name="message">Message.</param>
            <param name="defaultValue">Default value.</param>
        </member>
        <member name="M:PuppeteerSharp.Dialog.Accept(System.String)">
            <summary>
            Accept the Dialog.
            </summary>
            <returns>Task which resolves when the dialog has been accepted.</returns>
            <param name="promptText">A text to enter in prompt. Does not cause any effects if the dialog's type is not prompt.</param>
        </member>
        <member name="M:PuppeteerSharp.Dialog.Dismiss">
            <summary>
            Dismiss the dialog.
            </summary>
            <returns>Task which resolves when the dialog has been dismissed.</returns>
        </member>
        <member name="T:PuppeteerSharp.DialogEventArgs">
            <summary>
            <see cref="E:PuppeteerSharp.Page.Dialog"/> arguments.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.DialogEventArgs.Dialog">
            <summary>
            Dialog data.
            </summary>
            <value>Dialog data.</value>
        </member>
        <member name="M:PuppeteerSharp.DialogEventArgs.#ctor(PuppeteerSharp.Dialog)">
            <summary>
            Initializes a new instance of the <see cref="T:PuppeteerSharp.DialogEventArgs"/> class.
            </summary>
            <param name="dialog">Dialog.</param>
        </member>
        <member name="T:PuppeteerSharp.DialogType">
            <summary>
            Dialog type.
            </summary>
            <seealso cref="T:PuppeteerSharp.Dialog"/>
        </member>
        <member name="F:PuppeteerSharp.DialogType.Alert">
            <summary>
            Alert dialog.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.DialogType.Prompt">
            <summary>
            Prompt dialog.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.DialogType.Confirm">
            <summary>
            Confirm dialog.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.DialogType.BeforeUnload">
            <summary>
            Before unload dialog.
            </summary>
        </member>
        <member name="T:PuppeteerSharp.ElementHandle">
            <summary>
            Inherits from <see cref="T:PuppeteerSharp.JSHandle"/>. It represents an in-page DOM element. 
            ElementHandles can be created by <see cref="M:PuppeteerSharp.Page.QuerySelectorAsync(System.String)"/> or <see cref="M:PuppeteerSharp.Page.QuerySelectorAllAsync(System.String)"/>.
            </summary>
        </member>
        <member name="M:PuppeteerSharp.ElementHandle.ScreenshotAsync(System.String)">
            <summary>
            This method scrolls element into view if needed, and then uses <seealso cref="M:PuppeteerSharp.Page.ScreenshotDataAsync(PuppeteerSharp.ScreenshotOptions)"/> to take a screenshot of the element. 
            If the element is detached from DOM, the method throws an error.
            </summary>
            <returns>The task</returns>
            <param name="file">The file path to save the image to. The screenshot type will be inferred from file extension. 
            If path is a relative path, then it is resolved relative to current working directory. If no path is provided, 
            the image won't be saved to the disk.</param>
        </member>
        <member name="M:PuppeteerSharp.ElementHandle.ScreenshotAsync(System.String,PuppeteerSharp.ScreenshotOptions)">
            <summary>
            This method scrolls element into view if needed, and then uses <seealso cref="M:PuppeteerSharp.Page.ScreenshotDataAsync(PuppeteerSharp.ScreenshotOptions)"/> to take a screenshot of the element. 
            If the element is detached from DOM, the method throws an error.
            </summary>
            <returns>The task</returns>
            <param name="file">The file path to save the image to. The screenshot type will be inferred from file extension. 
            If path is a relative path, then it is resolved relative to current working directory. If no path is provided, 
            the image won't be saved to the disk.</param>
            <param name="options">Screenshot options.</param>
        </member>
        <member name="M:PuppeteerSharp.ElementHandle.ScreenshotStreamAsync">
            <summary>
            This method scrolls element into view if needed, and then uses <seealso cref="M:PuppeteerSharp.Page.ScreenshotDataAsync(PuppeteerSharp.ScreenshotOptions)"/> to take a screenshot of the element. 
            If the element is detached from DOM, the method throws an error.
            </summary>
            <returns>Task which resolves to a <see cref="T:System.IO.Stream"/> containing the image data.</returns>
        </member>
        <member name="M:PuppeteerSharp.ElementHandle.ScreenshotStreamAsync(PuppeteerSharp.ScreenshotOptions)">
            <summary>
            This method scrolls element into view if needed, and then uses <seealso cref="M:PuppeteerSharp.Page.ScreenshotDataAsync(PuppeteerSharp.ScreenshotOptions)"/> to take a screenshot of the element. 
            If the element is detached from DOM, the method throws an error.
            </summary>
            <returns>Task which resolves to a <see cref="T:System.IO.Stream"/> containing the image data.</returns>
            <param name="options">Screenshot options.</param>
        </member>
        <member name="M:PuppeteerSharp.ElementHandle.ScreenshotDataAsync">
            <summary>
            This method scrolls element into view if needed, and then uses <seealso cref="M:PuppeteerSharp.Page.ScreenshotDataAsync(PuppeteerSharp.ScreenshotOptions)"/> to take a screenshot of the element. 
            If the element is detached from DOM, the method throws an error.
            </summary>
            <returns>Task which resolves to a <see cref="T:System.Byte"/>[] containing the image data.</returns>
        </member>
        <member name="M:PuppeteerSharp.ElementHandle.ScreenshotDataAsync(PuppeteerSharp.ScreenshotOptions)">
            <summary>
            This method scrolls element into view if needed, and then uses <seealso cref="M:PuppeteerSharp.Page.ScreenshotDataAsync(PuppeteerSharp.ScreenshotOptions)"/> to take a screenshot of the element. 
            If the element is detached from DOM, the method throws an error.
            </summary>
            <returns>Task which resolves to a <see cref="T:System.Byte"/>[] containing the image data.</returns>
            <param name="options">Screenshot options.</param>
        </member>
        <member name="M:PuppeteerSharp.ElementHandle.ScreenshotBase64Async">
            <summary>
            This method scrolls element into view if needed, and then uses <seealso cref="M:PuppeteerSharp.Page.ScreenshotBase64Async(PuppeteerSharp.ScreenshotOptions)"/> to take a screenshot of the element. 
            If the element is detached from DOM, the method throws an error.
            </summary>
            <returns>Task which resolves to a <see cref="T:System.String"/> containing the image data as base64.</returns>
        </member>
        <member name="M:PuppeteerSharp.ElementHandle.ScreenshotBase64Async(PuppeteerSharp.ScreenshotOptions)">
            <summary>
            This method scrolls element into view if needed, and then uses <seealso cref="M:PuppeteerSharp.Page.ScreenshotBase64Async(PuppeteerSharp.ScreenshotOptions)"/> to take a screenshot of the element. 
            If the element is detached from DOM, the method throws an error.
            </summary>
            <returns>Task which resolves to a <see cref="T:System.String"/> containing the image data as base64.</returns>
            <param name="options">Screenshot options.</param>
        </member>
        <member name="M:PuppeteerSharp.ElementHandle.HoverAsync">
            <summary>
            Scrolls element into view if needed, and then uses <see cref="P:PuppeteerSharp.Page.Mouse"/> to hover over the center of the element.
            </summary>
            <returns>Task which resolves when the element is successfully hovered</returns>
        </member>
        <member name="M:PuppeteerSharp.ElementHandle.ClickAsync(PuppeteerSharp.Input.ClickOptions)">
            <summary>
            Scrolls element into view if needed, and then uses <see cref="P:PuppeteerSharp.Page.Mouse"/> to click in the center of the element.
            </summary>
            <param name="options">click options</param>
            <exception cref="T:PuppeteerSharp.PuppeteerException">if the element is detached from DOM</exception>
            <returns>Task which resolves when the element is successfully clicked</returns>
        </member>
        <member name="M:PuppeteerSharp.ElementHandle.UploadFileAsync(System.String[])">
            <summary>
            Uploads files
            </summary>
            <param name="filePaths">Sets the value of the file input these paths. paths are resolved using <see cref="M:System.IO.Path.GetFullPath(System.String)"/></param>
            <remarks>This method expects <c>elementHandle</c> to point to an <c>input element</c> <see href="https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input"/> </remarks>
            <returns>Task</returns>
        </member>
        <member name="M:PuppeteerSharp.ElementHandle.TapAsync">
            <summary>
            Scrolls element into view if needed, and then uses <see cref="M:PuppeteerSharp.Input.Touchscreen.TapAsync(System.Decimal,System.Decimal)"/> to tap in the center of the element.
            </summary>
            <exception cref="T:PuppeteerSharp.PuppeteerException">if the element is detached from DOM</exception>
            <returns>Task which resolves when the element is successfully tapped</returns>
        </member>
        <member name="M:PuppeteerSharp.ElementHandle.FocusAsync">
            <summary>
            Calls <c>focus</c> <see href="https://developer.mozilla.org/en-US/docs/Web/API/HTMLElement/focus"/> on the element.
            </summary>
            <returns>Task</returns>
        </member>
        <member name="M:PuppeteerSharp.ElementHandle.TypeAsync(System.String,PuppeteerSharp.Input.TypeOptions)">
            <summary>
            Focuses the element, and sends a <c>keydown</c>, <c>keypress</c>/<c>input</c>, and <c>keyup</c> event for each character in the text.
            </summary>
            <param name="text">A text to type into a focused element</param>
            <param name="options">type options</param>
            <remarks>
            To press a special key, like <c>Control</c> or <c>ArrowDown</c> use <see cref="M:PuppeteerSharp.ElementHandle.PressAsync(System.String,PuppeteerSharp.Input.PressOptions)"/>
            </remarks>
            <example>
            <code>
            elementHandle.TypeAsync("#mytextarea", "Hello"); // Types instantly
            elementHandle.TypeAsync("#mytextarea", "World", new TypeOptions { Delay = 100 }); // Types slower, like a user
            </code>
            An example of typing into a text field and then submitting the form:
            <code>
            var elementHandle = await page.GetElementAsync("input");
            await elementHandle.TypeAsync("some text");
            await elementHandle.PressAsync("Enter");
            </code>
            </example>
            <returns>Task</returns>
        </member>
        <member name="M:PuppeteerSharp.ElementHandle.PressAsync(System.String,PuppeteerSharp.Input.PressOptions)">
            <summary>
            Focuses the element, and then uses <see cref="M:PuppeteerSharp.Input.Keyboard.DownAsync(System.String,PuppeteerSharp.Input.DownOptions)"/> and <see cref="M:PuppeteerSharp.Input.Keyboard.UpAsync(System.String)"/>.
            </summary>
            <param name="key">Name of key to press, such as <c>ArrowLeft</c>. See <see cref="T:PuppeteerSharp.Input.KeyDefinitions"/> for a list of all key names.</param>
            <param name="options">press options</param>
            <remarks>
            If <c>key</c> is a single character and no modifier keys besides <c>Shift</c> are being held down, a <c>keypress</c>/<c>input</c> event will also be generated. The <see cref="P:PuppeteerSharp.Input.DownOptions.Text"/> option can be specified to force an input event to be generated.
            </remarks>
            <returns></returns>
        </member>
        <member name="M:PuppeteerSharp.ElementHandle.QuerySelectorAsync(System.String)">
            <summary>
            The method runs <c>element.querySelector</c> within the page. If no element matches the selector, the return value resolve to <c>null</c>.
            </summary>
            <param name="selector">A selector to query element for</param>
            <returns>Task which resolves to <see cref="T:PuppeteerSharp.ElementHandle"/> pointing to the frame element</returns>
        </member>
        <member name="M:PuppeteerSharp.ElementHandle.QuerySelectorAllAsync(System.String)">
            <summary>
            Runs <c>element.querySelectorAll</c> within the page. If no elements match the selector, the return value resolve to <see cref="M:System.Array.Empty``1"/>.
            </summary>
            <param name="selector">A selector to query element for</param>
            <returns>Task which resolves to ElementHandles pointing to the frame elements</returns>
        </member>
        <member name="M:PuppeteerSharp.ElementHandle.QuerySelectorAllHandleAsync(System.String)">
            <summary>
            A utility function to be used with <see cref="M:PuppeteerSharp.Extensions.EvaluateFunctionAsync``1(System.Threading.Tasks.Task{PuppeteerSharp.JSHandle},System.String,System.Object[])"/>
            </summary>
            <param name="selector">A selector to query element for</param>
            <returns>Task which resolves to a <see cref="T:PuppeteerSharp.JSHandle"/> of <c>document.querySelectorAll</c> result</returns>
        </member>
        <member name="M:PuppeteerSharp.ElementHandle.XPathAsync(System.String)">
            <summary>
            Evaluates the XPath expression relative to the elementHandle. If there's no such element, the method will resolve to <c>null</c>.
            </summary>
            <param name="expression">Expression to evaluate <see href="https://developer.mozilla.org/en-US/docs/Web/API/Document/evaluate"/></param>
            <returns>Task which resolves to an array of <see cref="T:PuppeteerSharp.ElementHandle"/></returns>
        </member>
        <member name="M:PuppeteerSharp.ElementHandle.BoundingBoxAsync">
            <summary>
            This method returns the bounding box of the element (relative to the main frame), 
            or null if the element is not visible.
            </summary>
            <returns>The BoundingBox task.</returns>
        </member>
        <member name="M:PuppeteerSharp.ElementHandle.BoxModelAsync">
            <summary>
            returns boxes of the element, or <c>null</c> if the element is not visible. Box points are sorted clock-wise.
            </summary>
            <returns>Task BoxModel task.</returns>
        </member>
        <member name="M:PuppeteerSharp.ElementHandle.ContentFrameAsync">
             <summary>
            Content frame for element handles referencing iframe nodes, or null otherwise.
             </summary>
             <returns>Resolves to the content frame</returns>
        </member>
        <member name="M:PuppeteerSharp.ElementHandle.IsIntersectingViewportAsync">
            <summary>
            Evaluates if the element is visible in the current viewport.
            </summary>
            <returns>A task which resolves to true if the element is visible in the current viewport.</returns>
        </member>
        <member name="T:PuppeteerSharp.ErrorEventArgs">
            <summary>
            <see cref="E:PuppeteerSharp.Page.Error"/> arguments.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.ErrorEventArgs.Error">
            <summary>
            Gets the error.
            </summary>
            <value>The error.</value>
        </member>
        <member name="M:PuppeteerSharp.ErrorEventArgs.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:PuppeteerSharp.ErrorEventArgs"/> class.
            </summary>
            <param name="error">Error.</param>
        </member>
        <member name="T:PuppeteerSharp.EvaluationFailedException">
            <summary>
            Exception thrown by <see cref="M:PuppeteerSharp.ExecutionContext.EvaluateHandleAsync(System.String,System.Object)"/>.
            </summary>
        </member>
        <member name="M:PuppeteerSharp.EvaluationFailedException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:PuppeteerSharp.EvaluationFailedException"/> class.
            </summary>
        </member>
        <member name="M:PuppeteerSharp.EvaluationFailedException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:PuppeteerSharp.EvaluationFailedException"/> class.
            </summary>
            <param name="message">Message.</param>
        </member>
        <member name="M:PuppeteerSharp.EvaluationFailedException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:PuppeteerSharp.EvaluationFailedException"/> class.
            </summary>
            <param name="message">Message.</param>
            <param name="innerException">Inner exception.</param>
        </member>
        <member name="M:PuppeteerSharp.EvaluationFailedException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:PuppeteerSharp.EvaluationFailedException"/> class.
            </summary>
            <param name="info">Info.</param>
            <param name="context">Context.</param>
        </member>
        <member name="T:PuppeteerSharp.ExecutionContext">
            <summary>
            The class represents a context for JavaScript execution. Examples of JavaScript contexts are:
            Each <see cref="P:PuppeteerSharp.ExecutionContext.Frame"/> has a separate <see cref="T:PuppeteerSharp.ExecutionContext"/>
            All kind of web workers have their own contexts
            </summary>
        </member>
        <member name="P:PuppeteerSharp.ExecutionContext.Frame">
            <summary>
            Frame associated with this execution context.
            </summary>
            <remarks>
            NOTE Not every execution context is associated with a frame. For example, workers and extensions have execution contexts that are not associated with frames.
            </remarks>
        </member>
        <member name="M:PuppeteerSharp.ExecutionContext.EvaluateExpressionAsync(System.String)">
            <summary>
            Executes a script in browser context
            </summary>
            <param name="script">Script to be evaluated in browser context</param>
            <remarks>
            If the script, returns a Promise, then the method would wait for the promise to resolve and return its value.
            </remarks>
            <seealso cref="M:PuppeteerSharp.ExecutionContext.EvaluateFunctionAsync``1(System.String,System.Object[])"/>
            <seealso cref="M:PuppeteerSharp.ExecutionContext.EvaluateExpressionHandleAsync(System.String)"/>
            <returns>Task which resolves to script return value</returns>
        </member>
        <member name="M:PuppeteerSharp.ExecutionContext.EvaluateExpressionAsync``1(System.String)">
            <summary>
            Executes a script in browser context
            </summary>
            <typeparam name="T">The type to deserialize the result to</typeparam>
            <param name="script">Script to be evaluated in browser context</param>
            <remarks>
            If the script, returns a Promise, then the method would wait for the promise to resolve and return its value.
            </remarks>
            <seealso cref="M:PuppeteerSharp.ExecutionContext.EvaluateFunctionAsync``1(System.String,System.Object[])"/>
            <seealso cref="M:PuppeteerSharp.ExecutionContext.EvaluateExpressionHandleAsync(System.String)"/>
            <returns>Task which resolves to script return value</returns>
        </member>
        <member name="M:PuppeteerSharp.ExecutionContext.EvaluateFunctionAsync(System.String,System.Object[])">
            <summary>
            Executes a function in browser context
            </summary>
            <param name="script">Script to be evaluated in browser context</param>
            <param name="args">Arguments to pass to script</param>
            <remarks>
            If the script, returns a Promise, then the method would wait for the promise to resolve and return its value.
            <see cref="T:PuppeteerSharp.JSHandle"/> instances can be passed as arguments
            </remarks>
            <seealso cref="M:PuppeteerSharp.ExecutionContext.EvaluateExpressionAsync``1(System.String)"/>
            <seealso cref="M:PuppeteerSharp.ExecutionContext.EvaluateFunctionHandleAsync(System.String,System.Object[])"/>
            <returns>Task which resolves to script return value</returns>
        </member>
        <member name="M:PuppeteerSharp.ExecutionContext.EvaluateFunctionAsync``1(System.String,System.Object[])">
            <summary>
            Executes a function in browser context
            </summary>
            <typeparam name="T">The type to deserialize the result to</typeparam>
            <param name="script">Script to be evaluated in browser context</param>
            <param name="args">Arguments to pass to script</param>
            <remarks>
            If the script, returns a Promise, then the method would wait for the promise to resolve and return its value.
            <see cref="T:PuppeteerSharp.JSHandle"/> instances can be passed as arguments
            </remarks>
            <seealso cref="M:PuppeteerSharp.ExecutionContext.EvaluateExpressionAsync``1(System.String)"/>
            <seealso cref="M:PuppeteerSharp.ExecutionContext.EvaluateFunctionHandleAsync(System.String,System.Object[])"/>
            <returns>Task which resolves to script return value</returns>
        </member>
        <member name="M:PuppeteerSharp.ExecutionContext.QueryObjectsAsync(PuppeteerSharp.JSHandle)">
            <summary>
            The method iterates JavaScript heap and finds all the objects with the given prototype.
            </summary>
            <returns>A task which resolves to a handle to an array of objects with this prototype.</returns>
            <param name="prototypeHandle">A handle to the object prototype.</param>
        </member>
        <member name="T:PuppeteerSharp.Extensions">
            <summary>
            <see cref="T:PuppeteerSharp.JSHandle"/> and <see cref="T:PuppeteerSharp.ElementHandle"/> Extensions.
            </summary>
        </member>
        <member name="M:PuppeteerSharp.Extensions.EvaluateFunctionAsync(System.Threading.Tasks.Task{PuppeteerSharp.ElementHandle},System.String,System.Object[])">
            <summary>
            Runs <paramref name="pageFunction"/> within the frame and passes it the outcome of <paramref name="elementHandleTask"/> as the first argument
            </summary>
            <param name="elementHandleTask">A task that returns an <see cref="T:PuppeteerSharp.ElementHandle"/> that will be used as the first argument in <paramref name="pageFunction"/></param>
            <param name="pageFunction">Function to be evaluated in browser context</param>
            <param name="args">Arguments to pass to <c>pageFunction</c></param>
            <returns>Task</returns>
            <exception cref="T:PuppeteerSharp.SelectorException">If <paramref name="elementHandleTask"/> resolves to <c>null</c></exception>
        </member>
        <member name="M:PuppeteerSharp.Extensions.EvaluateFunctionAsync``1(System.Threading.Tasks.Task{PuppeteerSharp.ElementHandle},System.String,System.Object[])">
            <summary>
            Runs <paramref name="pageFunction"/> within the frame and passes it the outcome of <paramref name="elementHandleTask"/> as the first argument
            </summary>
            <typeparam name="T">The type of the response</typeparam>
            <param name="elementHandleTask">A task that returns an <see cref="T:PuppeteerSharp.ElementHandle"/> that will be used as the first argument in <paramref name="pageFunction"/></param>
            <param name="pageFunction">Function to be evaluated in browser context</param>
            <param name="args">Arguments to pass to <c>pageFunction</c></param>
            <returns>Task which resolves to the return value of <c>pageFunction</c></returns>
            <exception cref="T:PuppeteerSharp.SelectorException">If <paramref name="elementHandleTask"/> resolves to <c>null</c></exception>
        </member>
        <member name="M:PuppeteerSharp.Extensions.EvaluateFunctionAsync``1(PuppeteerSharp.ElementHandle,System.String,System.Object[])">
            <summary>
            Runs <paramref name="pageFunction"/> within the frame and passes it the outcome the <paramref name="elementHandle"/> as the first argument
            </summary>
            <typeparam name="T">The type of the response</typeparam>
            <param name="elementHandle">An <see cref="T:PuppeteerSharp.ElementHandle"/> that will be used as the first argument in <paramref name="pageFunction"/></param>
            <param name="pageFunction">Function to be evaluated in browser context</param>
            <param name="args">Arguments to pass to <c>pageFunction</c></param>
            <returns>Task which resolves to the return value of <c>pageFunction</c></returns>
            <exception cref="T:PuppeteerSharp.SelectorException">If <paramref name="elementHandle"/> is <c>null</c></exception>
        </member>
        <member name="M:PuppeteerSharp.Extensions.EvaluateFunctionAsync(System.Threading.Tasks.Task{PuppeteerSharp.JSHandle},System.String,System.Object[])">
            <summary>
            Runs <paramref name="pageFunction"/> within the frame and passes it the outcome of <paramref name="arrayHandleTask"/> as the first argument. Use only after <see cref="M:PuppeteerSharp.Page.QuerySelectorAllHandleAsync(System.String)"/>
            </summary>
            <param name="arrayHandleTask">A task that returns an <see cref="T:PuppeteerSharp.JSHandle"/> that represents an array of <see cref="T:PuppeteerSharp.ElementHandle"/> that will be used as the first argument in <paramref name="pageFunction"/></param>
            <param name="pageFunction">Function to be evaluated in browser context</param>
            <param name="args">Arguments to pass to <c>pageFunction</c></param>
            <returns>Task</returns>
        </member>
        <member name="M:PuppeteerSharp.Extensions.EvaluateFunctionAsync``1(System.Threading.Tasks.Task{PuppeteerSharp.JSHandle},System.String,System.Object[])">
            <summary>
            Runs <paramref name="pageFunction"/> within the frame and passes it the outcome of <paramref name="arrayHandleTask"/> as the first argument. Use only after <see cref="M:PuppeteerSharp.Page.QuerySelectorAllHandleAsync(System.String)"/>
            </summary>
            <typeparam name="T"></typeparam>
            <param name="arrayHandleTask">A task that returns an <see cref="T:PuppeteerSharp.JSHandle"/> that represents an array of <see cref="T:PuppeteerSharp.ElementHandle"/> that will be used as the first argument in <paramref name="pageFunction"/></param>
            <param name="pageFunction">Function to be evaluated in browser context</param>
            <param name="args">Arguments to pass to <c>pageFunction</c></param>
            <returns>Task which resolves to the return value of <c>pageFunction</c></returns>
        </member>
        <member name="M:PuppeteerSharp.Extensions.EvaluateFunctionAsync``1(PuppeteerSharp.JSHandle,System.String,System.Object[])">
            <summary>
            Runs <paramref name="pageFunction"/> within the frame and passes it the outcome of <paramref name="arrayHandle"/> as the first argument. Use only after <see cref="M:PuppeteerSharp.Page.QuerySelectorAllHandleAsync(System.String)"/>
            </summary>
            <typeparam name="T"></typeparam>
            <param name="arrayHandle">An <see cref="T:PuppeteerSharp.JSHandle"/> that represents an array of <see cref="T:PuppeteerSharp.ElementHandle"/> that will be used as the first argument in <paramref name="pageFunction"/></param>
            <param name="pageFunction">Function to be evaluated in browser context</param>
            <param name="args">Arguments to pass to <c>pageFunction</c></param>
            <returns>Task which resolves to the return value of <c>pageFunction</c></returns>
        </member>
        <member name="T:PuppeteerSharp.Frame">
            <summary>
            Provides methods to interact with a single page frame in Chromium. One <see cref="T:PuppeteerSharp.Page"/> instance might have multiple <see cref="T:PuppeteerSharp.Frame"/> instances.
            At every point of time, page exposes its current frame tree via the <see cref="P:PuppeteerSharp.Page.MainFrame"/> and <see cref="P:PuppeteerSharp.Frame.ChildFrames"/> properties.
            
            <see cref="T:PuppeteerSharp.Frame"/> object's lifecycle is controlled by three events, dispatched on the page object
            - <see cref="E:PuppeteerSharp.Page.FrameAttached"/> - fires when the frame gets attached to the page. A Frame can be attached to the page only once
            - <see cref="E:PuppeteerSharp.Page.FrameNavigated"/> - fired when the frame commits navigation to a different URL
            - <see cref="E:PuppeteerSharp.Page.FrameDetached"/> - fired when the frame gets detached from the page.  A Frame can be detached from the page only once
            </summary>
            <example>
            An example of dumping frame tree
            <code>
            <![CDATA[
            var browser = await Puppeteer.LaunchAsync(new LaunchOptions());
            var page = await browser.NewPageAsync();
            await page.GoToAsync("https://www.google.com/chrome/browser/canary.html");
            dumpFrameTree(page.MainFrame, string.Empty);
            await browser.CloseAsync();
            
            void dumpFrameTree(Frame frame, string indent)
            {
                Console.WriteLine(indent + frame.Url);
                foreach (var child in frame.ChildFrames)
                {
                    dumpFrameTree(child, indent + "  ");
                }
            }
            ]]>
            </code>
            </example>
        </member>
        <member name="P:PuppeteerSharp.Frame.ChildFrames">
            <summary>
            Gets the child frames of the this frame
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Frame.Name">
            <summary>
            Gets the frame's name attribute as specified in the tag
            If the name is empty, returns the id attribute instead
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Frame.Url">
            <summary>
            Gets the frame's url
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Frame.Detached">
            <summary>
            Gets a value indicating if the frame is detached or not
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Frame.ParentFrame">
            <summary>
            Gets the parent frame, if any. Detached frames and main frames return <c>null</c>
            </summary>
        </member>
        <member name="M:PuppeteerSharp.Frame.GoToAsync(System.String,PuppeteerSharp.NavigationOptions)">
            <summary>
            Navigates to an url
            </summary>        
            <param name="url">URL to navigate page to. The url should include scheme, e.g. https://.</param>
            <param name="options">Navigation parameters.</param>
            <returns>Task which resolves to the main resource response. In case of multiple redirects, the navigation will resolve with the response of the last redirect.</returns>
            <seealso cref="M:PuppeteerSharp.Frame.GoToAsync(System.String,System.Nullable{System.Int32},PuppeteerSharp.WaitUntilNavigation[])"/>
        </member>
        <member name="M:PuppeteerSharp.Frame.GoToAsync(System.String,System.Nullable{System.Int32},PuppeteerSharp.WaitUntilNavigation[])">
            <summary>
            Navigates to an url
            </summary>
            <param name="url">URL to navigate page to. The url should include scheme, e.g. https://.</param>
            <param name="timeout">maximum navigation time in milliseconds. Defaults to 30 seconds. Pass 0
            to disable timeout. The default value can be changed by using the <see cref="P:PuppeteerSharp.Page.DefaultNavigationTimeout"/>
            property.</param>
            <param name="waitUntil">When to consider navigation succeeded, defaults to <see cref="F:PuppeteerSharp.WaitUntilNavigation.Load"/>. Given an array of <see cref="T:PuppeteerSharp.WaitUntilNavigation"/>, navigation is considered to be successful after all events have been fired</param>
            <returns>Task which resolves to the main resource response. In case of multiple redirects, the navigation will resolve with the response of the last redirect</returns>
        </member>
        <member name="M:PuppeteerSharp.Frame.WaitForNavigationAsync(PuppeteerSharp.NavigationOptions)">
            <summary>
            This resolves when the frame navigates to a new URL or reloads.
            It is useful for when you run code which will indirectly cause the frame to navigate.
            </summary>
            <param name="options">navigation options</param>
            <returns>Task which resolves to the main resource response. 
            In case of multiple redirects, the navigation will resolve with the response of the last redirect.
            In case of navigation to a different anchor or navigation due to History API usage, the navigation will resolve with `null`.
            </returns>
            <remarks>
            Usage of the <c>History API</c> <see href="https://developer.mozilla.org/en-US/docs/Web/API/History_API"/> to change the URL is considered a navigation
            </remarks>
            <example>
            <code>
            <![CDATA[
            var navigationTask =frame.page.WaitForNavigationAsync();
            await frame.ClickAsync("a.my-link");
            await navigationTask;
            ]]>
            </code>
            </example>
        </member>
        <member name="M:PuppeteerSharp.Frame.EvaluateExpressionAsync(System.String)">
            <summary>
            Executes a script in browser context
            </summary>
            <param name="script">Script to be evaluated in browser context</param>
            <remarks>
            If the script, returns a Promise, then the method would wait for the promise to resolve and return its value.
            </remarks>
            <returns>Task which resolves to script return value</returns>
            <seealso cref="M:PuppeteerSharp.Frame.EvaluateFunctionAsync``1(System.String,System.Object[])"/>
            <seealso cref="M:PuppeteerSharp.Page.EvaluateExpressionAsync``1(System.String)"/>
        </member>
        <member name="M:PuppeteerSharp.Frame.EvaluateExpressionAsync``1(System.String)">
            <summary>
            Executes a script in browser context
            </summary>
            <typeparam name="T">The type to deserialize the result to</typeparam>
            <param name="script">Script to be evaluated in browser context</param>
            <remarks>
            If the script, returns a Promise, then the method would wait for the promise to resolve and return its value.
            </remarks>
            <returns>Task which resolves to script return value</returns>
            <seealso cref="M:PuppeteerSharp.Frame.EvaluateFunctionAsync``1(System.String,System.Object[])"/>
            <seealso cref="M:PuppeteerSharp.Page.EvaluateExpressionAsync``1(System.String)"/>
        </member>
        <member name="M:PuppeteerSharp.Frame.EvaluateFunctionAsync(System.String,System.Object[])">
            <summary>
            Executes a function in browser context
            </summary>
            <param name="script">Script to be evaluated in browser context</param>
            <param name="args">Arguments to pass to script</param>
            <remarks>
            If the script, returns a Promise, then the method would wait for the promise to resolve and return its value.
            <see cref="T:PuppeteerSharp.JSHandle"/> instances can be passed as arguments
            </remarks>
            <returns>Task which resolves to script return value</returns>
            <seealso cref="M:PuppeteerSharp.Frame.EvaluateExpressionAsync``1(System.String)"/>
            <seealso cref="M:PuppeteerSharp.Page.EvaluateFunctionAsync``1(System.String,System.Object[])"/>
        </member>
        <member name="M:PuppeteerSharp.Frame.EvaluateFunctionAsync``1(System.String,System.Object[])">
            <summary>
            Executes a function in browser context
            </summary>
            <typeparam name="T">The type to deserialize the result to</typeparam>
            <param name="script">Script to be evaluated in browser context</param>
            <param name="args">Arguments to pass to script</param>
            <remarks>
            If the script, returns a Promise, then the method would wait for the promise to resolve and return its value.
            <see cref="T:PuppeteerSharp.JSHandle"/> instances can be passed as arguments
            </remarks>
            <returns>Task which resolves to script return value</returns>
            <seealso cref="M:PuppeteerSharp.Frame.EvaluateExpressionAsync``1(System.String)"/>
            <seealso cref="M:PuppeteerSharp.Page.EvaluateFunctionAsync``1(System.String,System.Object[])"/>
        </member>
        <member name="M:PuppeteerSharp.Frame.EvaluateExpressionHandleAsync(System.String)">
            <summary>
            Passes an expression to the <see cref="M:PuppeteerSharp.ExecutionContext.EvaluateExpressionHandleAsync(System.String)"/>, returns a <see cref="T:System.Threading.Tasks.Task"/>, then <see cref="M:PuppeteerSharp.ExecutionContext.EvaluateExpressionHandleAsync(System.String)"/> would wait for the <see cref="T:System.Threading.Tasks.Task"/> to resolve and return its value.
            </summary>
            <example>
            <code>
            var frame = page.MainFrame;
            const handle = Page.MainFrame.EvaluateExpressionHandleAsync("1 + 2");
            </code>
            </example>
            <returns>Resolves to the return value of <paramref name="script"/></returns>
            <param name="script">Expression to be evaluated in the <seealso cref="T:PuppeteerSharp.ExecutionContext"/></param>
        </member>
        <member name="M:PuppeteerSharp.Frame.EvaluateFunctionHandleAsync(System.String,System.Object[])">
            <summary>
            Passes a function to the <see cref="M:PuppeteerSharp.ExecutionContext.EvaluateFunctionAsync(System.String,System.Object[])"/>, returns a <see cref="T:System.Threading.Tasks.Task"/>, then <see cref="M:PuppeteerSharp.ExecutionContext.EvaluateFunctionHandleAsync(System.String,System.Object[])"/> would wait for the <see cref="T:System.Threading.Tasks.Task"/> to resolve and return its value.
            </summary>
            <example>
            <code>
            var frame = page.MainFrame;
            const handle = Page.MainFrame.EvaluateFunctionHandleAsync("() => Promise.resolve(self)");
            return handle; // Handle for the global object.
            </code>
            <see cref="T:PuppeteerSharp.JSHandle"/> instances can be passed as arguments to the <see cref="M:PuppeteerSharp.ExecutionContext.EvaluateFunctionAsync(System.String,System.Object[])"/>:
            
            const handle = await Page.MainFrame.EvaluateExpressionHandleAsync("document.body");
            const resultHandle = await Page.MainFrame.EvaluateFunctionHandleAsync("body => body.innerHTML", handle);
            return await resultHandle.JsonValueAsync(); // prints body's innerHTML
            </example>
            <returns>Resolves to the return value of <paramref name="function"/></returns>
            <param name="function">Function to be evaluated in the <see cref="T:PuppeteerSharp.ExecutionContext"/></param>
            <param name="args">Arguments to pass to <paramref name="function"/></param>
        </member>
        <member name="M:PuppeteerSharp.Frame.GetExecutionContextAsync">
            <summary>
            Gets the <see cref="T:PuppeteerSharp.ExecutionContext"/> associated with the frame.
            </summary>
            <returns><see cref="T:PuppeteerSharp.ExecutionContext"/> associated with the frame.</returns>
        </member>
        <member name="M:PuppeteerSharp.Frame.WaitForSelectorAsync(System.String,PuppeteerSharp.WaitForSelectorOptions)">
            <summary>
            Waits for a selector to be added to the DOM
            </summary>
            <param name="selector">A selector of an element to wait for</param>
            <param name="options">Optional waiting parameters</param>
            <returns>A task that resolves when element specified by selector string is added to DOM.
            Resolves to `null` if waiting for `hidden: true` and selector is not found in DOM.</returns>
            <seealso cref="M:PuppeteerSharp.Frame.WaitForXPathAsync(System.String,PuppeteerSharp.WaitForSelectorOptions)"/>
            <seealso cref="M:PuppeteerSharp.Page.WaitForSelectorAsync(System.String,PuppeteerSharp.WaitForSelectorOptions)"/>
            <exception cref="T:PuppeteerSharp.WaitTaskTimeoutException">If timeout occurred.</exception>
        </member>
        <member name="M:PuppeteerSharp.Frame.WaitForXPathAsync(System.String,PuppeteerSharp.WaitForSelectorOptions)">
            <summary>
            Waits for a selector to be added to the DOM
            </summary>
            <param name="xpath">A xpath selector of an element to wait for</param>
            <param name="options">Optional waiting parameters</param>
            <returns>A task which resolves when element specified by xpath string is added to DOM. 
            Resolves to `null` if waiting for `hidden: true` and xpath is not found in DOM.</returns>
            <example>
            <code>
            <![CDATA[
            var browser = await Puppeteer.LaunchAsync(new LaunchOptions());
            var page = await browser.NewPageAsync();
            string currentURL = null;
            page.MainFrame
                .WaitForXPathAsync("//img")
                .ContinueWith(_ => Console.WriteLine("First URL with image: " + currentURL));
            foreach (var current in new[] { "https://example.com", "https://google.com", "https://bbc.com" })
            {
                currentURL = current;
                await page.GoToAsync(currentURL);
            }
            await browser.CloseAsync();
            ]]>
            </code>
            </example>
            <seealso cref="M:PuppeteerSharp.Frame.WaitForSelectorAsync(System.String,PuppeteerSharp.WaitForSelectorOptions)"/>
            <seealso cref="M:PuppeteerSharp.Page.WaitForXPathAsync(System.String,PuppeteerSharp.WaitForSelectorOptions)"/>
            <exception cref="T:PuppeteerSharp.WaitTaskTimeoutException">If timeout occurred.</exception>
        </member>
        <member name="M:PuppeteerSharp.Frame.WaitForTimeoutAsync(System.Int32)">
            <summary>
            Waits for a timeout
            </summary>
            <param name="milliseconds"></param>
            <returns>A task that resolves when after the timeout</returns>
            <seealso cref="M:PuppeteerSharp.Page.WaitForTimeoutAsync(System.Int32)"/>
            <exception cref="T:PuppeteerSharp.WaitTaskTimeoutException">If timeout occurred.</exception>
        </member>
        <member name="M:PuppeteerSharp.Frame.WaitForFunctionAsync(System.String,PuppeteerSharp.WaitForFunctionOptions,System.Object[])">
            <summary>
            Waits for a function to be evaluated to a truthy value
            </summary>
            <param name="script">Function to be evaluated in browser context</param>
            <param name="options">Optional waiting parameters</param>
            <param name="args">Arguments to pass to <c>script</c></param>
            <returns>A task that resolves when the <c>script</c> returns a truthy value</returns>
            <seealso cref="M:PuppeteerSharp.Page.WaitForFunctionAsync(System.String,PuppeteerSharp.WaitForFunctionOptions,System.Object[])"/>
            <exception cref="T:PuppeteerSharp.WaitTaskTimeoutException">If timeout occurred.</exception>
        </member>
        <member name="M:PuppeteerSharp.Frame.WaitForExpressionAsync(System.String,PuppeteerSharp.WaitForFunctionOptions)">
            <summary>
            Waits for an expression to be evaluated to a truthy value
            </summary>
            <param name="script">Expression to be evaluated in browser context</param>
            <param name="options">Optional waiting parameters</param>
            <returns>A task that resolves when the <c>script</c> returns a truthy value</returns>
            <seealso cref="M:PuppeteerSharp.Page.WaitForExpressionAsync(System.String,PuppeteerSharp.WaitForFunctionOptions)"/>
            <exception cref="T:PuppeteerSharp.WaitTaskTimeoutException">If timeout occurred.</exception>
        </member>
        <member name="M:PuppeteerSharp.Frame.SelectAsync(System.String,System.String[])">
            <summary>
            Triggers a change and input event once all the provided options have been selected. 
            If there's no <![CDATA[<select>]]> element matching selector, the method throws an error.
            </summary>
            <exception cref="T:PuppeteerSharp.SelectorException">If there's no element matching <paramref name="selector"/></exception>
            <param name="selector">A selector to query page for</param>
            <param name="values">Values of options to select. If the <![CDATA[<select>]]> has the multiple attribute, 
            all values are considered, otherwise only the first one is taken into account.</param>
            <returns>Returns an array of option values that have been successfully selected.</returns>
            <seealso cref="M:PuppeteerSharp.Page.SelectAsync(System.String,System.String[])"/>
        </member>
        <member name="M:PuppeteerSharp.Frame.QuerySelectorAsync(System.String)">
            <summary>
            Queries frame for the selector. If there's no such element within the frame, the method will resolve to <c>null</c>.
            </summary>
            <param name="selector">Selector to query frame for</param>
            <returns>Task which resolves to <see cref="T:PuppeteerSharp.ElementHandle"/> pointing to the frame element</returns>
            <seealso cref="M:PuppeteerSharp.Page.QuerySelectorAsync(System.String)"/>
        </member>
        <member name="M:PuppeteerSharp.Frame.QuerySelectorAllAsync(System.String)">
            <summary>
            Queries frame for the selector. If no elements match the selector, the return value resolve to <see cref="M:System.Array.Empty``1"/>.
            </summary>
            <param name="selector">A selector to query frame for</param>
            <returns>Task which resolves to ElementHandles pointing to the frame elements</returns>
            <seealso cref="M:PuppeteerSharp.Page.QuerySelectorAllAsync(System.String)"/>
        </member>
        <member name="M:PuppeteerSharp.Frame.XPathAsync(System.String)">
            <summary>
            Evaluates the XPath expression
            </summary>
            <param name="expression">Expression to evaluate <see href="https://developer.mozilla.org/en-US/docs/Web/API/Document/evaluate"/></param>
            <returns>Task which resolves to an array of <see cref="T:PuppeteerSharp.ElementHandle"/></returns>
            <seealso cref="M:PuppeteerSharp.Page.XPathAsync(System.String)"/>
        </member>
        <member name="M:PuppeteerSharp.Frame.AddStyleTag(PuppeteerSharp.AddTagOptions)">
            <summary>
            Adds a <c><![CDATA[<link rel="stylesheet">]]></c> tag into the page with the desired url or a <c><![CDATA[<link rel="stylesheet">]]></c> tag with the content
            </summary>
            <param name="options">add style tag options</param>
            <returns>Task which resolves to the added tag when the stylesheet's onload fires or when the CSS content was injected into frame</returns>
            <seealso cref="M:PuppeteerSharp.Page.AddStyleTagAsync(PuppeteerSharp.AddTagOptions)"/>
            <seealso cref="M:PuppeteerSharp.Page.AddStyleTagAsync(System.String)"/>
        </member>
        <member name="M:PuppeteerSharp.Frame.AddScriptTag(PuppeteerSharp.AddTagOptions)">
            <summary>
            Adds a <c><![CDATA[<script>]]></c> tag into the page with the desired url or content
            </summary>
            <param name="options">add script tag options</param>
            <returns>Task which resolves to the added tag when the script's onload fires or when the script content was injected into frame</returns>
            <seealso cref="M:PuppeteerSharp.Page.AddScriptTagAsync(PuppeteerSharp.AddTagOptions)"/>
            <seealso cref="M:PuppeteerSharp.Page.AddScriptTagAsync(System.String)"/>
        </member>
        <member name="M:PuppeteerSharp.Frame.AddStyleTagAsync(PuppeteerSharp.AddTagOptions)">
            <summary>
            Adds a <c><![CDATA[<link rel="stylesheet">]]></c> tag into the page with the desired url or a <c><![CDATA[<link rel="stylesheet">]]></c> tag with the content
            </summary>
            <param name="options">add style tag options</param>
            <returns>Task which resolves to the added tag when the stylesheet's onload fires or when the CSS content was injected into frame</returns>
            <seealso cref="M:PuppeteerSharp.Page.AddStyleTagAsync(PuppeteerSharp.AddTagOptions)"/>
            <seealso cref="M:PuppeteerSharp.Page.AddStyleTagAsync(System.String)"/>
        </member>
        <member name="M:PuppeteerSharp.Frame.AddScriptTagAsync(PuppeteerSharp.AddTagOptions)">
            <summary>
            Adds a <c><![CDATA[<script>]]></c> tag into the page with the desired url or content
            </summary>
            <param name="options">add script tag options</param>
            <returns>Task which resolves to the added tag when the script's onload fires or when the script content was injected into frame</returns>
            <seealso cref="M:PuppeteerSharp.Page.AddScriptTagAsync(PuppeteerSharp.AddTagOptions)"/>
            <seealso cref="M:PuppeteerSharp.Page.AddScriptTagAsync(System.String)"/>
        </member>
        <member name="M:PuppeteerSharp.Frame.GetContentAsync">
            <summary>
            Gets the full HTML contents of the page, including the doctype.
            </summary>
            <returns>Task which resolves to the HTML content.</returns>
            <seealso cref="M:PuppeteerSharp.Page.GetContentAsync"/>
        </member>
        <member name="M:PuppeteerSharp.Frame.SetContentAsync(System.String,PuppeteerSharp.NavigationOptions)">
            <summary>
            Sets the HTML markup to the page
            </summary>
            <param name="html">HTML markup to assign to the page.</param>
            <param name="options">The options</param>
            <returns>Task.</returns>
            <seealso cref="M:PuppeteerSharp.Page.SetContentAsync(System.String,PuppeteerSharp.NavigationOptions)"/>
        </member>
        <member name="M:PuppeteerSharp.Frame.GetTitleAsync">
            <summary>
            Returns page's title
            </summary>
            <returns>page's title</returns>
            <seealso cref="M:PuppeteerSharp.Page.GetTitleAsync"/>
        </member>
        <member name="M:PuppeteerSharp.Frame.ClickAsync(System.String,PuppeteerSharp.Input.ClickOptions)">
            <summary>
            Fetches an element with <paramref name="selector"/>, scrolls it into view if needed, and then uses <see cref="P:PuppeteerSharp.Page.Mouse"/> to click in the center of the element.
            </summary>
            <param name="selector">A selector to search for element to click. If there are multiple elements satisfying the selector, the first will be clicked.</param>
            <param name="options">click options</param>
            <exception cref="T:PuppeteerSharp.SelectorException">If there's no element matching <paramref name="selector"/></exception>
            <returns>Task which resolves when the element matching <paramref name="selector"/> is successfully clicked</returns>
        </member>
        <member name="M:PuppeteerSharp.Frame.HoverAsync(System.String)">
            <summary>
            Fetches an element with <paramref name="selector"/>, scrolls it into view if needed, and then uses <see cref="P:PuppeteerSharp.Page.Mouse"/> to hover over the center of the element.
            </summary>
            <param name="selector">A selector to search for element to hover. If there are multiple elements satisfying the selector, the first will be hovered.</param>
            <exception cref="T:PuppeteerSharp.SelectorException">If there's no element matching <paramref name="selector"/></exception>
            <returns>Task which resolves when the element matching <paramref name="selector"/> is successfully hovered</returns>
        </member>
        <member name="M:PuppeteerSharp.Frame.FocusAsync(System.String)">
            <summary>
            Fetches an element with <paramref name="selector"/> and focuses it
            </summary>
            <param name="selector">A selector to search for element to focus. If there are multiple elements satisfying the selector, the first will be focused.</param>
            <exception cref="T:PuppeteerSharp.SelectorException">If there's no element matching <paramref name="selector"/></exception>
            <returns>Task which resolves when the element matching <paramref name="selector"/> is successfully focused</returns>
        </member>
        <member name="M:PuppeteerSharp.Frame.TypeAsync(System.String,System.String,PuppeteerSharp.Input.TypeOptions)">
            <summary>
            Sends a <c>keydown</c>, <c>keypress</c>/<c>input</c>, and <c>keyup</c> event for each character in the text.
            </summary>
            <param name="selector">A selector of an element to type into. If there are multiple elements satisfying the selector, the first will be used.</param>
            <param name="text">A text to type into a focused element</param>
            <param name="options"></param>
            <exception cref="T:PuppeteerSharp.SelectorException">If there's no element matching <paramref name="selector"/></exception>
            <remarks>
            To press a special key, like <c>Control</c> or <c>ArrowDown</c> use <see cref="M:PuppeteerSharp.Input.Keyboard.PressAsync(System.String,PuppeteerSharp.Input.PressOptions)"/>
            </remarks>
            <example>
            <code>
            page.TypeAsync("#mytextarea", "Hello"); // Types instantly
            page.TypeAsync("#mytextarea", "World", new TypeOptions { Delay = 100 }); // Types slower, like a user
            </code>
            </example>
            <returns>Task</returns>
        </member>
        <member name="T:PuppeteerSharp.FrameEventArgs">
            <summary>
            <see cref="E:PuppeteerSharp.Page.FrameAttached"/>, <see cref="E:PuppeteerSharp.Page.FrameDetached"/> and <see cref="E:PuppeteerSharp.Page.FrameNavigated"/> arguments.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.FrameEventArgs.Frame">
            <summary>
            Gets or sets the frame.
            </summary>
            <value>The frame.</value>
        </member>
        <member name="M:PuppeteerSharp.FrameEventArgs.#ctor(PuppeteerSharp.Frame)">
            <summary>
            Initializes a new instance of the <see cref="T:PuppeteerSharp.FrameEventArgs"/> class.
            </summary>
            <param name="frame">Frame.</param>
        </member>
        <member name="T:PuppeteerSharp.GeolocationOption">
            <summary>
            Geolocation option.
            </summary>
            <seealso cref="M:PuppeteerSharp.Page.SetGeolocationAsync(PuppeteerSharp.GeolocationOption)"/>
        </member>
        <member name="P:PuppeteerSharp.GeolocationOption.Latitude">
            <summary>
            Latitude between -90 and 90.
            </summary>
            <value>The latitude.</value>
        </member>
        <member name="P:PuppeteerSharp.GeolocationOption.Longitude">
            <summary>
            Longitude between -180 and 180.
            </summary>
            <value>The longitude.</value>
        </member>
        <member name="P:PuppeteerSharp.GeolocationOption.Accuracy">
            <summary>
            Optional non-negative accuracy value.
            </summary>
            <value>The accuracy.</value>
        </member>
        <member name="M:PuppeteerSharp.GeolocationOption.Equals(PuppeteerSharp.GeolocationOption)">
            <summary>
            Determines whether the specified <see cref="T:PuppeteerSharp.GeolocationOption"/> is equal to the current <see cref="T:PuppeteerSharp.GeolocationOption"/>.
            </summary>
            <param name="other">The <see cref="T:PuppeteerSharp.GeolocationOption"/> to compare with the current <see cref="T:PuppeteerSharp.GeolocationOption"/>.</param>
            <returns><c>true</c> if the specified <see cref="T:PuppeteerSharp.GeolocationOption"/> is equal to the current
            <see cref="T:PuppeteerSharp.GeolocationOption"/>; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:PuppeteerSharp.GeolocationOption.Equals(System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:PuppeteerSharp.GeolocationOption.GetHashCode">
            <inheritdoc/>
        </member>
        <member name="T:PuppeteerSharp.Helpers.AsyncFileHelper">
            <summary>
            Provides static methods for asynchronous file access.
            </summary>
        </member>
        <member name="M:PuppeteerSharp.Helpers.AsyncFileHelper.CreateStream(System.String,System.IO.FileMode)">
            <inheritdoc cref="M:System.IO.FileStream.#ctor(System.String,System.IO.FileMode)" />
        </member>
        <member name="M:PuppeteerSharp.Helpers.AsyncFileHelper.CreateStream(System.String,System.IO.FileMode,System.IO.FileAccess)">
            <inheritdoc cref="M:System.IO.FileStream.#ctor(System.String,System.IO.FileMode,System.IO.FileAccess)" />
        </member>
        <member name="M:PuppeteerSharp.Helpers.AsyncFileHelper.CreateStream(System.String,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare)">
            <inheritdoc cref="M:System.IO.FileStream.#ctor(System.String,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare)" />
        </member>
        <member name="M:PuppeteerSharp.Helpers.AsyncFileHelper.ReadAllText(System.String)">
            <inheritdoc cref="M:System.IO.File.ReadAllText(System.String)" />
        </member>
        <member name="M:PuppeteerSharp.Helpers.AsyncFileHelper.ReadAllText(System.String,System.Text.Encoding)">
            <inheritdoc cref="M:System.IO.File.ReadAllText(System.String,System.Text.Encoding)" />
        </member>
        <member name="M:PuppeteerSharp.Helpers.AsyncFileHelper.OpenWrite(System.String)">
            <inheritdoc cref="M:System.IO.File.OpenWrite(System.String)" />
        </member>
        <member name="M:PuppeteerSharp.Helpers.AsyncFileHelper.OpenRead(System.String)">
            <inheritdoc cref="M:System.IO.File.OpenRead(System.String)" />
        </member>
        <member name="M:PuppeteerSharp.Helpers.AsyncFileHelper.OpenText(System.String)">
            <inheritdoc cref="M:System.IO.File.OpenText(System.String)" />
        </member>
        <member name="M:PuppeteerSharp.Helpers.AsyncFileHelper.OpenText(System.String,System.Text.Encoding)">
            <inheritdoc cref="M:System.IO.File.OpenText(System.String)" />
            <param name="path">File path.</param>
            <param name="encoding">The encoding applied to the contents of the file.</param>
        </member>
        <member name="T:PuppeteerSharp.Helpers.Json.JSHandleMethodConverter">
            <summary>
            JSHandleMethodConverter will throw an exception if a JSHandle object is trying to be serialized
            </summary>
        </member>
        <member name="T:PuppeteerSharp.Helpers.Json.JTokenExtensions">
            <summary>
            A set of extension methods for JToken
            </summary>
        </member>
        <member name="M:PuppeteerSharp.Helpers.Json.JTokenExtensions.AsString(Newtonsoft.Json.Linq.JToken)">
            <summary>
            Shortcut for converting the JToken to a string
            </summary>
            <param name="token">The JToken</param>
            <remarks>Returns null if token is null</remarks>
            <returns>A string representation of the JToken</returns>
        </member>
        <member name="M:PuppeteerSharp.Helpers.Json.JTokenExtensions.ToObject``1(Newtonsoft.Json.Linq.JToken,System.Boolean)">
            <summary>
            Creates an instance of the specified .NET type from the <see cref="T:Newtonsoft.Json.Linq.JToken" />.
            </summary>
            <typeparam name="T">The object type that the token will be deserialized to.</typeparam>
            <param name="token">Json token</param>
            <param name="camelCase">If set to <c>true</c> the CamelCasePropertyNamesContractResolver will be used.</param>
            <returns>The new object created from the JSON value.</returns>
        </member>
        <member name="M:PuppeteerSharp.Helpers.Json.JTokenExtensions.ToObject``1(Newtonsoft.Json.Linq.JToken,Newtonsoft.Json.JsonSerializerSettings)">
            <summary>
            Creates an instance of the specified .NET type from the <see cref="T:Newtonsoft.Json.Linq.JToken" />.
            </summary>
            <typeparam name="T">The object type that the token will be deserialized to.</typeparam>
            <param name="token">Json token</param>
            <param name="jsonSerializerSettings">Serializer settings.</param>
            <returns>The new object created from the JSON value.</returns>
        </member>
        <member name="M:PuppeteerSharp.Helpers.StringExtensions.Quote(System.String)">
            <summary>
            Quotes the specified <see cref="T:System.String"/>.
            </summary>
            <param name="value">The string to quote.</param>
            <returns>A quoted string.</returns>
        </member>
        <member name="M:PuppeteerSharp.Helpers.StringExtensions.UnQuote(System.String)">
            <summary>
            Unquote the specified <see cref="T:System.String"/>.
            </summary>
            <param name="value">The string to unquote.</param>
            <returns>An unquoted string.</returns>
        </member>
        <member name="T:PuppeteerSharp.Helpers.TaskHelper">
            <summary>
            Task helper.
            </summary>
        </member>
        <member name="M:PuppeteerSharp.Helpers.TaskHelper.WithTimeout(System.Threading.Tasks.Task,System.Int32,System.Func{System.TimeSpan,System.Exception})">
            <summary>
            Cancels the <paramref name="task"/> after <paramref name="milliseconds"/> milliseconds
            </summary>
            <returns>The task result.</returns>
            <param name="task">Task to wait for.</param>
            <param name="milliseconds">Milliseconds timeout.</param>
            <param name="exceptionFactory">Optional timeout exception factory.</param>
        </member>
        <member name="M:PuppeteerSharp.Helpers.TaskHelper.WithTimeout(System.Threading.Tasks.Task,System.TimeSpan,System.Func{System.TimeSpan,System.Exception})">
            <summary>
            Cancels the <paramref name="task"/> after a given <paramref name="timeout"/> period
            </summary>
            <returns>The task result.</returns>
            <param name="task">Task to wait for.</param>
            <param name="timeout">The timeout period.</param>
            <param name="exceptionFactory">Optional timeout exception factory.</param>
        </member>
        <member name="M:PuppeteerSharp.Helpers.TaskHelper.WithTimeout(System.Threading.Tasks.Task,System.Func{System.Threading.Tasks.Task},System.Int32)">
            <summary>
            Cancels the <paramref name="task"/> after <paramref name="milliseconds"/> milliseconds
            </summary>
            <returns>The task result.</returns>
            <param name="task">Task to wait for.</param>
            <param name="timeoutAction">Action to be executed on Timeout.</param>
            <param name="milliseconds">Milliseconds timeout.</param>
        </member>
        <member name="M:PuppeteerSharp.Helpers.TaskHelper.WithTimeout(System.Threading.Tasks.Task,System.Func{System.Threading.Tasks.Task},System.TimeSpan)">
            <summary>
            Cancels the <paramref name="task"/> after a given <paramref name="timeout"/> period
            </summary>
            <returns>The task result.</returns>
            <param name="task">Task to wait for.</param>
            <param name="timeoutAction">Action to be executed on Timeout.</param>
            <param name="timeout">The timeout period.</param>
        </member>
        <member name="M:PuppeteerSharp.Helpers.TaskHelper.WithTimeout``1(System.Threading.Tasks.Task{``0},System.Action,System.Int32)">
            <summary>
            Cancels the <paramref name="task"/> after <paramref name="milliseconds"/> milliseconds
            </summary>
            <returns>The task result.</returns>
            <param name="task">Task to wait for.</param>
            <param name="timeoutAction">Action to be executed on Timeout.</param>
            <param name="milliseconds">Milliseconds timeout.</param>
        </member>
        <member name="M:PuppeteerSharp.Helpers.TaskHelper.WithTimeout``1(System.Threading.Tasks.Task{``0},System.Action,System.TimeSpan)">
            <summary>
            Cancels the <paramref name="task"/> after a given <paramref name="timeout"/> period
            </summary>
            <returns>The task result.</returns>
            <param name="task">Task to wait for.</param>
            <param name="timeoutAction">Action to be executed on Timeout.</param>
            <param name="timeout">The timeout period.</param>
        </member>
        <member name="M:PuppeteerSharp.Helpers.TaskHelper.WithTimeout``1(System.Threading.Tasks.Task{``0},System.Int32,System.Func{System.TimeSpan,System.Exception})">
            <summary>
            Cancels the <paramref name="task"/> after <paramref name="milliseconds"/> milliseconds
            </summary>
            <returns>The task result.</returns>
            <param name="task">Task to wait for.</param>
            <param name="milliseconds">Milliseconds timeout.</param>
            <param name="exceptionFactory">Optional timeout exception factory.</param>
            <typeparam name="T">Task return type.</typeparam>
        </member>
        <member name="M:PuppeteerSharp.Helpers.TaskHelper.WithTimeout``1(System.Threading.Tasks.Task{``0},System.TimeSpan,System.Func{System.TimeSpan,System.Exception})">
            <summary>
            Cancels the <paramref name="task"/> after a given <paramref name="timeout"/> period
            </summary>
            <returns>The task result.</returns>
            <param name="task">Task to wait for.</param>
            <param name="timeout">The timeout period.</param>
            <param name="exceptionFactory">Optional timeout exception factory.</param>
            <typeparam name="T">Task return type.</typeparam>
        </member>
        <member name="T:PuppeteerSharp.Helpers.TempDirectory">
            <summary>
            Represents a directory that is deleted on disposal.
            </summary>
        </member>
        <member name="T:PuppeteerSharp.IBrowserOptions">
            <summary>
            Browser options.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.IBrowserOptions.IgnoreHTTPSErrors">
            <summary>
            Whether to ignore HTTPS errors during navigation. Defaults to false.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.IBrowserOptions.DefaultViewport">
            <summary>
            Gets or sets the default Viewport.
            </summary>
            <value>The default Viewport.</value>
        </member>
        <member name="T:PuppeteerSharp.IConnectionOptions">
            <summary>
            Options for <see cref="T:PuppeteerSharp.Connection"/> creation.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.IConnectionOptions.SlowMo">
            <summary>
            Slows down Puppeteer operations by the specified amount of milliseconds. Useful so that you can see what is going on.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.IConnectionOptions.KeepAliveInterval">
            <summary>
            Keep alive value (in milliseconds)
            </summary>
        </member>
        <member name="P:PuppeteerSharp.IConnectionOptions.WebSocketFactory">
            <summary>
            Optional factory for <see cref="T:System.Net.WebSockets.WebSocket"/> implementations.
            If <see cref="P:PuppeteerSharp.IConnectionOptions.Transport"/> is set this property will be ignored.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.IConnectionOptions.Transport">
            <summary>
            Optional connection transport factory.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.IConnectionOptions.TransportFactory">
            <summary>
            Optional factory for <see cref="T:PuppeteerSharp.Transport.IConnectionTransport"/> implementations.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.IConnectionOptions.EnqueueTransportMessages">
            <summary>
            If not <see cref="P:PuppeteerSharp.IConnectionOptions.Transport"/> is set this will be use to determine is the default <see cref="T:PuppeteerSharp.Transport.WebSocketTransport"/> will enqueue messages.
            </summary>
        </member>
        <member name="T:PuppeteerSharp.Input.ClickOptions">
            <summary>
            Options to use when clicking
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Input.ClickOptions.Delay">
            <summary>
            Time to wait between <c>mousedown</c> and <c>mouseup</c> in milliseconds. Defaults to 0
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Input.ClickOptions.ClickCount">
            <summary>
            Defaults to 1. See https://developer.mozilla.org/en-US/docs/Web/API/UIEvent/detail
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Input.ClickOptions.Button">
            <summary>
            The button to use for the click. Defaults to <see cref="F:PuppeteerSharp.Input.MouseButton.Left"/>
            </summary>
        </member>
        <member name="T:PuppeteerSharp.Input.DownOptions">
            <summary>
            options to use with <see cref="M:PuppeteerSharp.Input.Keyboard.DownAsync(System.String,PuppeteerSharp.Input.DownOptions)"/>
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Input.DownOptions.Text">
            <summary>
            If specified, generates an input event with this text
            </summary>
        </member>
        <member name="T:PuppeteerSharp.Input.Key">
            <summary>
            Utility class to be used with <see cref="T:PuppeteerSharp.Input.Keyboard"/> operations
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.Cancel">
            <summary>
            Cancel key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.Help">
            <summary>
            Help key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.Backspace">
            <summary>
            Backspace key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.Tab">
            <summary>
            Tab key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.Clear">
            <summary>
            Clear key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.Enter">
            <summary>
            Enter key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.Shift">
            <summary>
            Shift key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.Control">
            <summary>
            Control key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.Alt">
            <summary>
            Alt key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.Pause">
            <summary>
            Pause key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.CapsLock">
            <summary>
            CapsLock key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.Escape">
            <summary>
            Escape key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.Convert">
            <summary>
            Convert key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.NonConvert">
            <summary>
            NonConvert key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.Accept">
            <summary>
            Accept key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.ModeChange">
            <summary>
            ModeChange key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.PageUp">
            <summary>
            PageUp key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.PageDown">
            <summary>
            PageDown key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.End">
            <summary>
            End key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.Home">
            <summary>
            Home key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.ArrowLeft">
            <summary>
            ArrowLeft key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.ArrowUp">
            <summary>
            ArrowUp key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.ArrowRight">
            <summary>
            ArrowRight key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.ArrowDown">
            <summary>
            ArrowDown key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.Select">
            <summary>
            Select key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.Print">
            <summary>
            Print key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.Execute">
            <summary>
            Execute key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.PrintScreen">
            <summary>
            PrintScreen key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.Insert">
            <summary>
            Insert key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.Delete">
            <summary>
            Delete key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.CloseParentheses">
            <summary>
            ')' key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.ExclamationMark">
            <summary>
            '!' key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.AtSign">
            <summary>
            '@' key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.NumberSign">
            <summary>
            '#' key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.DollarSign">
            <summary>
            '$' key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.Percent">
            <summary>
            '%' key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.Caret">
            <summary>
            '^' key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.Ampersand">
            <summary>
            <![CDATA['&']]> key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.Asterisk">
            <summary>
            Asterisk key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.OpenParentheses">
            <summary>
            '(' key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.Meta">
            <summary>
            Meta key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.ContextMenu">
            <summary>
            ContextMenu key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.F1">
            <summary>
            F1 key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.F2">
            <summary>
            F2 key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.F3">
            <summary>
            F3 key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.F4">
            <summary>
            F4 key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.F5">
            <summary>
            F5 key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.F6">
            <summary>
            F6 key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.F7">
            <summary>
            F7 key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.F8">
            <summary>
            F8 key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.F9">
            <summary>
            F9 key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.F10">
            <summary>
            F10 key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.F12">
            <summary>
            F11 key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.F11">
            <summary>
            F12 key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.F13">
            <summary>
            F13 key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.F14">
            <summary>
            F14 key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.F15">
            <summary>
            F15 key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.F16">
            <summary>
            F16 key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.F17">
            <summary>
            F17 key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.F18">
            <summary>
            F18 key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.F19">
            <summary>
            F19 key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.F20">
            <summary>
            F20 key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.F21">
            <summary>
            F21 key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.F22">
            <summary>
            F22 key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.F23">
            <summary>
            F23 key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.F24">
            <summary>
            F24 key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.NumLock">
            <summary>
            NumLock key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.ScrollLock">
            <summary>
            ScrollLock key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.AudioVolumeMute">
            <summary>
            AudioVolumeMute key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.AudioVolumeDown">
            <summary>
            AudioVolumeDown key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.AudioVolumeUp">
            <summary>
            AudioVolumeUp key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.MediaTrackNext">
            <summary>
            MediaTrackNext key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.MediaTrackPrevious">
            <summary>
            MediaTrackPrevious key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.MediaStop">
            <summary>
            MediaStop key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.MediaPlayPause">
            <summary>
            MediaPlayPause key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.Semicolon">
            <summary>
            ';' key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.Comma">
            <summary>
            ',' key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.EqualsSign">
            <summary>
            '=' key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.PlusSign">
            <summary>
            '+' key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.LesserThan">
            <summary>
            <![CDATA['<']]> key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.MinusSign">
            <summary>
            '-' key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.Underscore">
            <summary>
            '_' key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.Period">
            <summary>
            '.' key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.GreaterThan">
            <summary>
            <![CDATA['>']]> key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.Slash">
            <summary>
            <![CDATA['/']]>key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.QuestionMark">
            <summary>
            '?' key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.Backquote">
            <summary>
            '`' key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.Tilde">
            <summary>
            '~' key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.OpenSquareBrackets">
            <summary>
            <![CDATA['[']]> key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.OpenBrackets">
            <summary>
            '{' key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.CloseSquareBrackets">
            <summary>
            <![CDATA[']']]> key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.Pipe">
            <summary>
            '|' key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.CloseBrackets">
            <summary>
            '}' key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.Backslash">
            <summary>
            '\' key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.AltGraph">
            <summary>
            AltGraph key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.Attn">
            <summary>
            Attn key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.CrSel">
            <summary>
            CrSel key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.ExSel">
            <summary>
            ExSel key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.EraseEof">
            <summary>
            EraseEof key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.Play">
            <summary>
            Play key.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.Key.ZoomOut">
            <summary>
            ZoomOut key.
            </summary>
        </member>
        <member name="M:PuppeteerSharp.Input.Key.ToString">
            <inheritdoc />
        </member>
        <member name="M:PuppeteerSharp.Input.Key.op_Implicit(PuppeteerSharp.Input.Key)~System.String">
            <summary>
            Converts the <paramref name="key"/> to its underlining string value
            </summary>
            <param name="key">The key</param>
        </member>
        <member name="T:PuppeteerSharp.Input.Keyboard">
            <summary>
            Keyboard provides an api for managing a virtual keyboard. The high level api is <see cref="M:PuppeteerSharp.Input.Keyboard.TypeAsync(System.String,PuppeteerSharp.Input.TypeOptions)"/>, which takes raw characters and generates proper keydown, keypress/input, and keyup events on your page.
            
            For finer control, you can use <see cref="M:PuppeteerSharp.Input.Keyboard.DownAsync(System.String,PuppeteerSharp.Input.DownOptions)"/>, <see cref="M:PuppeteerSharp.Input.Keyboard.UpAsync(System.String)"/>, and <see cref="M:PuppeteerSharp.Input.Keyboard.SendCharacterAsync(System.String)"/> to manually fire events as if they were generated from a real keyboard.
            </summary>
        </member>
        <member name="M:PuppeteerSharp.Input.Keyboard.DownAsync(System.String,PuppeteerSharp.Input.DownOptions)">
            <summary>
            Dispatches a <c>keydown</c> event
            </summary>
            <param name="key">Name of key to press, such as <c>ArrowLeft</c>. <see cref="T:PuppeteerSharp.Input.KeyDefinitions"/> for a list of all key names.</param>
            <param name="options">down options</param>
            <remarks>
            If <c>key</c> is a single character and no modifier keys besides <c>Shift</c> are being held down, a <c>keypress</c>/<c>input</c> event will also generated. The <c>text</c> option can be specified to force an input event to be generated.
            If <c>key</c> is a modifier key, <c>Shift</c>, <c>Meta</c>, <c>Control</c>, or <c>Alt</c>, subsequent key presses will be sent with that modifier active. To release the modifier key, use <see cref="M:PuppeteerSharp.Input.Keyboard.UpAsync(System.String)"/>
            After the key is pressed once, subsequent calls to <see cref="M:PuppeteerSharp.Input.Keyboard.DownAsync(System.String,PuppeteerSharp.Input.DownOptions)"/> will have <see href="https://developer.mozilla.org/en-US/docs/Web/API/KeyboardEvent/repeat">repeat</see> set to <c>true</c>. To release the key, use <see cref="M:PuppeteerSharp.Input.Keyboard.UpAsync(System.String)"/>
            </remarks>
            <returns>Task</returns>
        </member>
        <member name="M:PuppeteerSharp.Input.Keyboard.UpAsync(System.String)">
            <summary>
            Dispatches a <c>keyup</c> event.
            </summary>
            <param name="key">Name of key to release, such as `ArrowLeft`. See <see cref="T:PuppeteerSharp.Input.KeyDefinitions"/> for a list of all key names.</param>
            <returns>Task</returns>
        </member>
        <member name="M:PuppeteerSharp.Input.Keyboard.SendCharacterAsync(System.String)">
            <summary>
            Dispatches a <c>keypress</c> and <c>input</c> event. This does not send a <c>keydown</c> or <c>keyup</c> event.
            </summary>
            <param name="charText">Character to send into the page</param>
            <returns>Task</returns>
        </member>
        <member name="M:PuppeteerSharp.Input.Keyboard.TypeAsync(System.String,PuppeteerSharp.Input.TypeOptions)">
            <summary>
            Sends a <c>keydown</c>, <c>keypress</c>/<c>input</c>, and <c>keyup</c> event for each character in the text.
            </summary>
            <param name="text">A text to type into a focused element</param>
            <param name="options">type options</param>
            <remarks>
            To press a special key, like <c>Control</c> or <c>ArrowDown</c>, use <see cref="M:PuppeteerSharp.Input.Keyboard.PressAsync(System.String,PuppeteerSharp.Input.PressOptions)"/>
            </remarks>
            <returns>Task</returns>
        </member>
        <member name="M:PuppeteerSharp.Input.Keyboard.PressAsync(System.String,PuppeteerSharp.Input.PressOptions)">
            <summary>
            Shortcut for <see cref="M:PuppeteerSharp.Input.Keyboard.DownAsync(System.String,PuppeteerSharp.Input.DownOptions)"/> and <see cref="M:PuppeteerSharp.Input.Keyboard.UpAsync(System.String)"/>
            </summary>
            <param name="key">Name of key to press, such as <c>ArrowLeft</c>. <see cref="T:PuppeteerSharp.Input.KeyDefinitions"/> for a list of all key names.</param>
            <param name="options">press options</param>
            <remarks>
            If <paramref name="key"/> is a single character and no modifier keys besides <c>Shift</c> are being held down, a <c>keypress</c>/<c>input</c> event will also generated. The <see cref="P:PuppeteerSharp.Input.DownOptions.Text"/> option can be specified to force an input event to be generated.
            Modifier keys DO effect <see cref="M:PuppeteerSharp.ElementHandle.PressAsync(System.String,PuppeteerSharp.Input.PressOptions)"/>. Holding down <c>Shift</c> will type the text in upper case.
            </remarks>
            <returns>Task</returns>
        </member>
        <member name="T:PuppeteerSharp.Input.Mouse">
            <summary>
            Provides methods to interact with the mouse
            </summary>
        </member>
        <member name="M:PuppeteerSharp.Input.Mouse.#ctor(PuppeteerSharp.CDPSession,PuppeteerSharp.Input.Keyboard)">
            <summary>
            Initializes a new instance of the <see cref="T:PuppeteerSharp.Input.Mouse"/> class.
            </summary>
            <param name="client">The client</param>
            <param name="keyboard">The keyboard</param>
        </member>
        <member name="M:PuppeteerSharp.Input.Mouse.MoveAsync(System.Decimal,System.Decimal,PuppeteerSharp.Input.MoveOptions)">
            <summary>
            Dispatches a <c>mousemove</c> event.
            </summary>
            <param name="x"></param>
            <param name="y"></param>
            <param name="options"></param>
            <returns>Task</returns>
        </member>
        <member name="M:PuppeteerSharp.Input.Mouse.ClickAsync(System.Decimal,System.Decimal,PuppeteerSharp.Input.ClickOptions)">
            <summary>
            Shortcut for <see cref="M:PuppeteerSharp.Input.Mouse.MoveAsync(System.Decimal,System.Decimal,PuppeteerSharp.Input.MoveOptions)"/>, <see cref="M:PuppeteerSharp.Input.Mouse.DownAsync(PuppeteerSharp.Input.ClickOptions)"/> and <see cref="M:PuppeteerSharp.Input.Mouse.UpAsync(PuppeteerSharp.Input.ClickOptions)"/>
            </summary>
            <param name="x"></param>
            <param name="y"></param>
            <param name="options"></param>
            <returns>Task</returns>
        </member>
        <member name="M:PuppeteerSharp.Input.Mouse.DownAsync(PuppeteerSharp.Input.ClickOptions)">
            <summary>
            Dispatches a <c>mousedown</c> event.
            </summary>
            <param name="options"></param>
            <returns>Task</returns>
        </member>
        <member name="M:PuppeteerSharp.Input.Mouse.UpAsync(PuppeteerSharp.Input.ClickOptions)">
            <summary>
            Dispatches a <c>mouseup</c> event.
            </summary>
            <param name="options"></param>
            <returns>Task</returns>
        </member>
        <member name="M:PuppeteerSharp.Input.Mouse.WheelAsync(System.Decimal,System.Decimal)">
            <summary>
            Dispatches a <c>wheel</c> event.
            </summary>
            <returns>Task</returns>
        </member>
        <member name="T:PuppeteerSharp.Input.MouseButton">
            <summary>
            The type of button click to use with <see cref="M:PuppeteerSharp.Input.Mouse.DownAsync(PuppeteerSharp.Input.ClickOptions)"/>, <see cref="M:PuppeteerSharp.Input.Mouse.UpAsync(PuppeteerSharp.Input.ClickOptions)"/> and <see cref="M:PuppeteerSharp.Input.Mouse.ClickAsync(System.Decimal,System.Decimal,PuppeteerSharp.Input.ClickOptions)"/>
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.MouseButton.None">
            <summary>
            Non specified
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.MouseButton.Left">
            <summary>
            The left mouse button
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.MouseButton.Right">
            <summary>
            The right mouse button
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Input.MouseButton.Middle">
            <summary>
            The middle mouse button
            </summary>
        </member>
        <member name="T:PuppeteerSharp.Input.MoveOptions">
            <summary>
            options to use <see cref="M:PuppeteerSharp.Input.Mouse.MoveAsync(System.Decimal,System.Decimal,PuppeteerSharp.Input.MoveOptions)"/>
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Input.MoveOptions.Steps">
            <summary>
            Sends intermediate <c>mousemove</c> events. Defaults to 1
            </summary>
        </member>
        <member name="T:PuppeteerSharp.Input.PressOptions">
            <summary>
            options to use when pressing a key.
            </summary>
            <seealso cref="M:PuppeteerSharp.Input.Keyboard.PressAsync(System.String,PuppeteerSharp.Input.PressOptions)"/>
            <seealso cref="M:PuppeteerSharp.ElementHandle.PressAsync(System.String,PuppeteerSharp.Input.PressOptions)"/>
        </member>
        <member name="P:PuppeteerSharp.Input.PressOptions.Delay">
            <summary>
            Time to wait between <c>keydown</c> and <c>keyup</c> in milliseconds. Defaults to 0.
            </summary>
        </member>
        <member name="T:PuppeteerSharp.Input.Touchscreen">
            <summary>
            Provides methods to interact with the touch screen
            </summary>
        </member>
        <member name="M:PuppeteerSharp.Input.Touchscreen.#ctor(PuppeteerSharp.CDPSession,PuppeteerSharp.Input.Keyboard)">
            <summary>
            Initializes a new instance of the <see cref="T:PuppeteerSharp.Input.Touchscreen"/> class.
            </summary>
            <param name="client">The client</param>
            <param name="keyboard">The keyboard</param>
        </member>
        <member name="M:PuppeteerSharp.Input.Touchscreen.TapAsync(System.Decimal,System.Decimal)">
            <summary>
            Dispatches a <c>touchstart</c> and <c>touchend</c> event.
            </summary>
            <param name="x"></param>
            <param name="y"></param>
            <returns>Task</returns>
            <seealso cref="M:PuppeteerSharp.Page.TapAsync(System.String)"/>
        </member>
        <member name="T:PuppeteerSharp.Input.TypeOptions">
            <summary>
            Options to use when typing
            </summary>
            <seealso cref="M:PuppeteerSharp.Page.TypeAsync(System.String,System.String,PuppeteerSharp.Input.TypeOptions)"/>
            <seealso cref="M:PuppeteerSharp.ElementHandle.TypeAsync(System.String,PuppeteerSharp.Input.TypeOptions)"/>
            <seealso cref="M:PuppeteerSharp.Input.Keyboard.TypeAsync(System.String,PuppeteerSharp.Input.TypeOptions)"/>
        </member>
        <member name="P:PuppeteerSharp.Input.TypeOptions.Delay">
            <summary>
            Time to wait between <c>keydown</c> and <c>keyup</c> in milliseconds. Defaults to 0.
            </summary>
        </member>
        <member name="T:PuppeteerSharp.JSHandle">
            <summary>
            JSHandle represents an in-page JavaScript object. JSHandles can be created with the <see cref="M:PuppeteerSharp.Page.EvaluateExpressionHandleAsync(System.String)"/> and <see cref="M:PuppeteerSharp.Page.EvaluateFunctionHandleAsync(System.String,System.Object[])"/> methods.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.JSHandle.ExecutionContext">
            <summary>
            Gets the execution context.
            </summary>
            <value>The execution context.</value>
        </member>
        <member name="P:PuppeteerSharp.JSHandle.Disposed">
            <summary>
            Gets or sets a value indicating whether this <see cref="T:PuppeteerSharp.JSHandle"/> is disposed.
            </summary>
            <value><c>true</c> if disposed; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:PuppeteerSharp.JSHandle.RemoteObject">
            <summary>
            Gets or sets the remote object.
            </summary>
            <value>The remote object.</value>
        </member>
        <member name="P:PuppeteerSharp.JSHandle.Client">
            <summary>
            Gets the client.
            </summary>
            <value>The client.</value>
        </member>
        <member name="P:PuppeteerSharp.JSHandle.Logger">
            <summary>
            Gets the logger.
            </summary>
            <value>The logger.</value>
        </member>
        <member name="M:PuppeteerSharp.JSHandle.GetPropertyAsync(System.String)">
            <summary>
            Fetches a single property from the referenced object
            </summary>
            <param name="propertyName">property to get</param>
            <returns>Task of <see cref="T:PuppeteerSharp.JSHandle"/></returns>
        </member>
        <member name="M:PuppeteerSharp.JSHandle.GetPropertiesAsync">
            <summary>
            Returns a <see cref="T:System.Collections.Generic.Dictionary`2"/> with property names as keys and <see cref="T:PuppeteerSharp.JSHandle"/> instances for the property values.
            </summary>
            <returns>Task which resolves to a <see cref="T:System.Collections.Generic.Dictionary`2"/></returns>
            <example>
            <code>
            var handle = await page.EvaluateExpressionHandle("({window, document})");
            var properties = await handle.GetPropertiesAsync();
            var windowHandle = properties["window"];
            var documentHandle = properties["document"];
            await handle.DisposeAsync();
            </code>
            </example>
        </member>
        <member name="M:PuppeteerSharp.JSHandle.JsonValueAsync">
            <summary>
            Returns a JSON representation of the object
            </summary>
            <returns>Task</returns>
            <remarks>
            The method will return an empty JSON if the referenced object is not stringifiable. It will throw an error if the object has circular references
            </remarks>
        </member>
        <member name="M:PuppeteerSharp.JSHandle.JsonValueAsync``1">
            <summary>
            Returns a JSON representation of the object
            </summary>
            <typeparam name="T">A strongly typed object to parse to</typeparam>
            <returns>Task</returns>
            <remarks>
            The method will return an empty JSON if the referenced object is not stringifiable. It will throw an error if the object has circular references
            </remarks>
        </member>
        <member name="M:PuppeteerSharp.JSHandle.DisposeAsync">
            <summary>
            Disposes the Handle. It will mark the JSHandle as disposed and release the <see cref="P:PuppeteerSharp.JSHandle.RemoteObject"/>
            </summary>
            <returns>The async.</returns>
        </member>
        <member name="M:PuppeteerSharp.JSHandle.ToString">
            <inheritdoc/>
        </member>
        <member name="T:PuppeteerSharp.Launcher">
            <summary>
            Launcher controls the creation of Chromium processes or the connection remote ones.
            </summary>
        </member>
        <member name="M:PuppeteerSharp.Launcher.#ctor(Microsoft.Extensions.Logging.ILoggerFactory)">
            <summary>
            Initializes a new instance of the <see cref="T:PuppeteerSharp.Launcher"/> class.
            </summary>
            <param name="loggerFactory">Logger factory.</param>
        </member>
        <member name="P:PuppeteerSharp.Launcher.Process">
            <summary>
            Gets Chromium process, if any was created by this launcher.
            </summary>
        </member>
        <member name="M:PuppeteerSharp.Launcher.LaunchAsync(PuppeteerSharp.LaunchOptions)">
            <summary>
            The method launches a browser instance with given arguments. The browser will be closed when the Browser is disposed.
            </summary>
            <param name="options">Options for launching Chrome</param>
            <returns>A connected browser.</returns>
            <remarks>
            See <a href="https://www.howtogeek.com/202825/what%E2%80%99s-the-difference-between-chromium-and-chrome/">this article</a>
            for a description of the differences between Chromium and Chrome.
            <a href="https://chromium.googlesource.com/chromium/src/+/lkcr/docs/chromium_browser_vs_google_chrome.md">This article</a> describes some differences for Linux users.
            </remarks>
        </member>
        <member name="M:PuppeteerSharp.Launcher.ConnectAsync(PuppeteerSharp.ConnectOptions)">
            <summary>
            Attaches Puppeteer to an existing Chromium instance. The browser will be closed when the Browser is disposed.
            </summary>
            <param name="options">Options for connecting.</param>
            <returns>A connected browser.</returns>
        </member>
        <member name="M:PuppeteerSharp.Launcher.GetExecutablePath">
            <summary>
            Gets the executable path.
            </summary>
            <returns>The executable path.</returns>
        </member>
        <member name="T:PuppeteerSharp.LaunchOptions">
            <summary>
            Options for launching the Chrome/ium browser.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.LaunchOptions.IgnoreHTTPSErrors">
            <summary>
            Whether to ignore HTTPS errors during navigation. Defaults to false.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.LaunchOptions.Headless">
            <summary>
            Whether to run browser in headless mode. Defaults to true unless the devtools option is true.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.LaunchOptions.ExecutablePath">
            <summary>
            Path to a Chromium or Chrome executable to run instead of bundled Chromium. If executablePath is a relative path, then it is resolved relative to current working directory.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.LaunchOptions.SlowMo">
            <summary>
            Slows down Puppeteer operations by the specified amount of milliseconds. Useful so that you can see what is going on.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.LaunchOptions.Args">
            <summary>
            Additional arguments to pass to the browser instance. List of Chromium flags can be found <a href="http://peter.sh/experiments/chromium-command-line-switches/">here</a>.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.LaunchOptions.Timeout">
            <summary>
            Maximum time in milliseconds to wait for the browser instance to start. Defaults to 30000 (30 seconds). Pass 0 to disable timeout.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.LaunchOptions.DumpIO">
            <summary>
             Whether to pipe browser process stdout and stderr into process.stdout and process.stderr. Defaults to false.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.LaunchOptions.UserDataDir">
            <summary>
            Path to a User Data Directory.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.LaunchOptions.Env">
            <summary>
            Specify environment variables that will be visible to browser. Defaults to Environment variables.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.LaunchOptions.Devtools">
            <summary>
            Whether to auto-open DevTools panel for each tab. If this option is true, the headless option will be set false.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.LaunchOptions.KeepAliveInterval">
            <summary>
            Keep alive value.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.LaunchOptions.LogProcess">
            <summary>
            Logs process counts after launching chrome and after exiting.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.LaunchOptions.IgnoreDefaultArgs">
            <summary>
            If <c>true</c>, then do not use <see cref="P:PuppeteerSharp.Puppeteer.DefaultArgs"/>.
            Dangerous option; use with care. Defaults to <c>false</c>.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.LaunchOptions.IgnoredDefaultArgs">
            <summary>
            if <see cref="P:PuppeteerSharp.LaunchOptions.IgnoreDefaultArgs"/> is set to <c>false</c> this list will be used to filter <see cref="P:PuppeteerSharp.Puppeteer.DefaultArgs"/>
            </summary>
        </member>
        <member name="P:PuppeteerSharp.LaunchOptions.WebSocketFactory">
            <summary>
            Optional factory for <see cref="T:System.Net.WebSockets.WebSocket"/> implementations.
            If <see cref="P:PuppeteerSharp.LaunchOptions.Transport"/> is set this property will be ignored.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.LaunchOptions.Transport">
            <summary>
            Optional connection transport.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.LaunchOptions.TransportFactory">
            <summary>
            Optional factory for <see cref="T:PuppeteerSharp.Transport.IConnectionTransport"/> implementations.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.LaunchOptions.DefaultViewport">
            <summary>
            Gets or sets the default Viewport.
            </summary>
            <value>The default Viewport.</value>
        </member>
        <member name="P:PuppeteerSharp.LaunchOptions.EnqueueTransportMessages">
            <summary>
            If not <see cref="P:PuppeteerSharp.LaunchOptions.Transport"/> is set this will be use to determine is the default <see cref="T:PuppeteerSharp.Transport.WebSocketTransport"/> will enqueue messages.
            </summary>
            <remarks>
            It's set to <c>true</c> by default because it's the safest way to send commands to Chromium.
            Setting this to <c>false</c> proved to work in .NET Core but it tends to fail on .NET Framework.
            </remarks>
        </member>
        <member name="T:PuppeteerSharp.Media.Clip">
            <summary>
            Clip data.
            </summary>
            <seealso cref="M:PuppeteerSharp.BoundingBox.ToClip"/>
            <seealso cref="P:PuppeteerSharp.ScreenshotOptions.Clip"/>
        </member>
        <member name="P:PuppeteerSharp.Media.Clip.X">
            <summary>
            x-coordinate of top-left corner of clip area.
            </summary>
            <value>The x.</value>
        </member>
        <member name="P:PuppeteerSharp.Media.Clip.Y">
            <summary>
            y-coordinate of top-left corner of clip area.
            </summary>
            <value>The y.</value>
        </member>
        <member name="P:PuppeteerSharp.Media.Clip.Width">
            <summary>
            Width of clipping area.
            </summary>
            <value>The width.</value>
        </member>
        <member name="P:PuppeteerSharp.Media.Clip.Height">
            <summary>
            Height of clipping area.
            </summary>
            <value>The height.</value>
        </member>
        <member name="P:PuppeteerSharp.Media.Clip.Scale">
            <summary>
            Scale of the webpage rendering. Defaults to 1.
            </summary>
            <value>The scale.</value>
        </member>
        <member name="T:PuppeteerSharp.Media.MarginOptions">
            <summary>
            margin options used in <see cref="T:PuppeteerSharp.PdfOptions"/>
            </summary>
        </member>
        <member name="M:PuppeteerSharp.Media.MarginOptions.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:PuppeteerSharp.Media.MarginOptions"/> class.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Media.MarginOptions.Top">
            <summary>
            Top margin, accepts values labeled with units
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Media.MarginOptions.Left">
            <summary>
            Left margin, accepts values labeled with units
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Media.MarginOptions.Bottom">
            <summary>
            Bottom margin, accepts values labeled with units
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Media.MarginOptions.Right">
            <summary>
            Right margin, accepts values labeled with units
            </summary>
        </member>
        <member name="M:PuppeteerSharp.Media.MarginOptions.Equals(System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:PuppeteerSharp.Media.MarginOptions.Equals(PuppeteerSharp.Media.MarginOptions)">
            <inheritdoc/>
        </member>
        <member name="M:PuppeteerSharp.Media.MarginOptions.GetHashCode">
            <inheritdoc/>
        </member>
        <member name="M:PuppeteerSharp.Media.MarginOptions.op_Equality(PuppeteerSharp.Media.MarginOptions,PuppeteerSharp.Media.MarginOptions)">
            <inheritdoc/>
        </member>
        <member name="M:PuppeteerSharp.Media.MarginOptions.op_Inequality(PuppeteerSharp.Media.MarginOptions,PuppeteerSharp.Media.MarginOptions)">
            <inheritdoc/>
        </member>
        <member name="T:PuppeteerSharp.Media.MediaType">
            <summary>
            Media type.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Media.MediaType.Print">
            <summary>
            Media Print.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Media.MediaType.Screen">
            <summary>
            Media Screen.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Media.MediaType.None">
            <summary>
            No media set
            </summary>
        </member>
        <member name="T:PuppeteerSharp.Media.PaperFormat">
            <summary>
            Paper format.
            </summary>
            <seealso cref="P:PuppeteerSharp.PdfOptions.Format"/>
        </member>
        <member name="M:PuppeteerSharp.Media.PaperFormat.#ctor(System.Decimal,System.Decimal)">
            <summary>
            Page width and height in inches.
            </summary>
            <param name="width">Page width in inches</param>
            <param name="height">Page height in inches</param>
        </member>
        <member name="P:PuppeteerSharp.Media.PaperFormat.Width">
            <summary>
            Page width in inches
            </summary>
            <value>The width.</value>
        </member>
        <member name="P:PuppeteerSharp.Media.PaperFormat.Height">
            <summary>
            Page height in inches
            </summary>
            <value>The Height.</value>
        </member>
        <member name="P:PuppeteerSharp.Media.PaperFormat.Letter">
            <summary>
            Letter: 8.5 inches x 11 inches.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Media.PaperFormat.Legal">
            <summary>
            Legal: 8.5 inches by 14 inches.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Media.PaperFormat.Tabloid">
            <summary>
            Tabloid: 11 inches by 17 inches.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Media.PaperFormat.Ledger">
            <summary>
            Ledger: 17 inches by 11 inches.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Media.PaperFormat.A0">
            <summary>
            A0: 33.1 inches by 46.8 inches.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Media.PaperFormat.A1">
            <summary>
            A1: 23.4 inches by 33.1 inches
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Media.PaperFormat.A2">
            <summary>
            A2: 16.5 inches by 23.4 inches
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Media.PaperFormat.A3">
            <summary>
            A3: 11.7 inches by 16.5 inches
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Media.PaperFormat.A4">
            <summary>
            A4: 8.27 inches by 11.7 inches
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Media.PaperFormat.A5">
            <summary>
            A5: 5.83 inches by 8.27 inches
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Media.PaperFormat.A6">
            <summary>
            A6: 4.13 inches by 5.83 inches
            </summary>
        </member>
        <member name="M:PuppeteerSharp.Media.PaperFormat.Equals(System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:PuppeteerSharp.Media.PaperFormat.Equals(PuppeteerSharp.Media.PaperFormat)">
            <inheritdoc/>
        </member>
        <member name="M:PuppeteerSharp.Media.PaperFormat.GetHashCode">
            <inheritdoc/>
        </member>
        <member name="M:PuppeteerSharp.Media.PaperFormat.op_Equality(PuppeteerSharp.Media.PaperFormat,PuppeteerSharp.Media.PaperFormat)">
            <inheritdoc/>
        </member>
        <member name="M:PuppeteerSharp.Media.PaperFormat.op_Inequality(PuppeteerSharp.Media.PaperFormat,PuppeteerSharp.Media.PaperFormat)">
            <inheritdoc/>
        </member>
        <member name="T:PuppeteerSharp.MessageEventArgs">
            <summary>
            <seealso cref="E:PuppeteerSharp.CDPSession.MessageReceived"/> arguments.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.MessageEventArgs.MessageID">
            <summary>
            Gets or sets the message identifier.
            </summary>
            <value>The message identifier.</value>
        </member>
        <member name="P:PuppeteerSharp.MessageEventArgs.MessageData">
            <summary>
            Gets or sets the message data.
            </summary>
            <value>The message data.</value>
        </member>
        <member name="T:PuppeteerSharp.MessageException">
            <summary>
            Exception thrown by <seealso cref="M:PuppeteerSharp.CDPSession.SendAsync``1(System.String,System.Object)"/>
            </summary>
        </member>
        <member name="M:PuppeteerSharp.MessageException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:PuppeteerSharp.MessageException"/> class.
            </summary>
            <param name="message">Message.</param>
        </member>
        <member name="M:PuppeteerSharp.MessageException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:PuppeteerSharp.MessageException"/> class.
            </summary>
            <param name="message">Message.</param>
            <param name="innerException">Inner exception.</param>
        </member>
        <member name="T:PuppeteerSharp.Messaging.RemoteObject">
            <summary>
            Remote object.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Messaging.RemoteObject.Type">
            <summary>
            Gets or sets the type.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Messaging.RemoteObject.Subtype">
            <summary>
            Gets or sets the subtype.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Messaging.RemoteObject.ObjectId">
            <summary>
            Gets or sets the object identifier.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Messaging.RemoteObject.UnserializableValue">
            <summary>
            Gets or sets the unserializable value.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Messaging.RemoteObject.Value">
            <summary>
            Gets or sets the value.
            </summary>
        </member>
        <member name="T:PuppeteerSharp.Messaging.RemoteObjectSubtype">
            <summary>
            Remote object subtype.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Messaging.RemoteObjectSubtype.Other">
            <summary>
            Other.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Messaging.RemoteObjectSubtype.Array">
            <summary>
            Array.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Messaging.RemoteObjectSubtype.Null">
            <summary>
            Null.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Messaging.RemoteObjectSubtype.Node">
            <summary>
            Node.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Messaging.RemoteObjectSubtype.Regexp">
            <summary>
            Regexp.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Messaging.RemoteObjectSubtype.Date">
            <summary>
            Date.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Messaging.RemoteObjectSubtype.Map">
            <summary>
            Map.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Messaging.RemoteObjectSubtype.Set">
            <summary>
            Set.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Messaging.RemoteObjectSubtype.Weakmap">
            <summary>
            Weakmap.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Messaging.RemoteObjectSubtype.Weakset">
            <summary>
            Weakset.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Messaging.RemoteObjectSubtype.Iterator">
            <summary>
            Iterator.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Messaging.RemoteObjectSubtype.Generator">
            <summary>
            Generator.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Messaging.RemoteObjectSubtype.Error">
            <summary>
            Error.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Messaging.RemoteObjectSubtype.Proxy">
            <summary>
            Proxy.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Messaging.RemoteObjectSubtype.Promise">
            <summary>
            Promise.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Messaging.RemoteObjectSubtype.Typedarray">
            <summary>
            Typedarray.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Messaging.RemoteObjectSubtype.Arraybuffer">
            <summary>
            Arraybuffer.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Messaging.RemoteObjectSubtype.Dataview">
            <summary>
            Dataview.
            </summary>
        </member>
        <member name="T:PuppeteerSharp.Messaging.RemoteObjectType">
            <summary>
            Remote object type.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Messaging.RemoteObjectType.Other">
            <summary>
            Other.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Messaging.RemoteObjectType.Object">
            <summary>
            Object.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Messaging.RemoteObjectType.Function">
            <summary>
            Function.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Messaging.RemoteObjectType.Undefined">
            <summary>
            Undefined.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Messaging.RemoteObjectType.String">
            <summary>
            String.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Messaging.RemoteObjectType.Number">
            <summary>
            Number.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Messaging.RemoteObjectType.Boolean">
            <summary>
            Boolean.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Messaging.RemoteObjectType.Symbol">
            <summary>
            Symbol.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Messaging.RemoteObjectType.Bigint">
            <summary>
            Bigint.
            </summary>
        </member>
        <member name="T:PuppeteerSharp.MetricEventArgs">
            <summary>
            <seealso cref="E:PuppeteerSharp.Page.Metrics"/> arguments.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.MetricEventArgs.Title">
            <summary>
            Gets the title.
            </summary>
            <value>The title.</value>
        </member>
        <member name="P:PuppeteerSharp.MetricEventArgs.Metrics">
            <summary>
            Gets the metrics.
            </summary>
            <value>The metrics.</value>
        </member>
        <member name="M:PuppeteerSharp.MetricEventArgs.#ctor(System.String,System.Collections.Generic.Dictionary{System.String,System.Decimal})">
            <summary>
            Initializes a new instance of the <see cref="T:PuppeteerSharp.MetricEventArgs"/> class.
            </summary>
            <param name="title">Title.</param>
            <param name="metrics">Metrics.</param>
        </member>
        <member name="T:PuppeteerSharp.Mobile.DeviceDescriptor">
            <summary>
            Device descriptor.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Mobile.DeviceDescriptor.Name">
            <summary>
            Device name.
            </summary>
            <value>The name.</value>
        </member>
        <member name="P:PuppeteerSharp.Mobile.DeviceDescriptor.UserAgent">
            <summary>
            User Agent
            </summary>
            <value>The user agent.</value>
        </member>
        <member name="P:PuppeteerSharp.Mobile.DeviceDescriptor.ViewPort">
            <summary>
            ViewPort.
            </summary>
            <value>The view port.</value>
        </member>
        <member name="T:PuppeteerSharp.Mobile.DeviceDescriptorName">
            <summary>
            Device descriptor name.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.BlackberryPlayBook">
            <summary>
            Blackberry PlayBook
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.BlackberryPlayBookLandscape">
            <summary>
            Blackberry PlayBook landscape
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.BlackBerryZ30">
            <summary>
            BlackBerry Z30
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.BlackBerryZ30Landscape">
            <summary>
            BlackBerry Z30 landscape
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.GalaxyNote3">
            <summary>
            Galaxy Note 3
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.GalaxyNote3Landscape">
            <summary>
            Galaxy Note 3 landscape
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.GalaxyNoteII">
            <summary>
            Galaxy Note II
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.GalaxyNoteIILandscape">
            <summary>
            Galaxy Note II landscape
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.GalaxySIII">
            <summary>
            Galaxy S III
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.GalaxySIIILandscape">
            <summary>
            Galaxy S III landscape
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.GalaxyS5">
            <summary>
            Galaxy S5
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.GalaxyS5Landscape">
            <summary>
            Galaxy S5 landscape
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.IPad">
            <summary>
            iPad
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.IPadLandscape">
            <summary>
            iPad landscape
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.IPadMini">
            <summary>
            iPad Mini
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.IPadMiniLandscape">
            <summary>
            iPad Mini landscape
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.IPadPro">
            <summary>
            iPad Pro
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.IPadProLandscape">
            <summary>
            iPad Pro landscape
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.IPhone4">
            <summary>
            iPhone 4
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.IPhone4Landscape">
            <summary>
            iPhone 4 landscape
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.IPhone5">
            <summary>
            iPhone 5
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.IPhone5Landscape">
            <summary>
            iPhone 5 landscape
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.IPhone6">
            <summary>
            iPhone 6
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.IPhone6Landscape">
            <summary>
            iPhone 6 landscape
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.IPhone6Plus">
            <summary>
            iPhone 6 Plus
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.IPhone6PlusLandscape">
            <summary>
            iPhone 6 Plus landscape
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.IPhone7">
            <summary>
            iPhone 7
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.IPhone7Landscape">
            <summary>
            iPhone 7 landscape
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.IPhone7Plus">
            <summary>
            iPhone 7 Plus
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.IPhone7PlusLandscape">
            <summary>
            iPhone 7 Plus landscape
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.IPhone8">
            <summary>
            iPhone 8
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.IPhone8Landscape">
            <summary>
            iPhone 8 landscape
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.IPhone8Plus">
            <summary>
            iPhone 8 Plus
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.IPhone8PlusLandscape">
            <summary>
            iPhone 8 Plus landscape
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.IPhoneSE">
            <summary>
            iPhone SE
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.IPhoneSELandscape">
            <summary>
            iPhone SE landscape
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.IPhoneX">
            <summary>
            iPhone X
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.IPhoneXLandscape">
            <summary>
            iPhone X landscape
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.JioPhone2">
            <summary>
            JioPhone 2
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.JioPhone2Landscape">
            <summary>
            JioPhone 2 landscape
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.KindleFireHDX">
            <summary>
            Kindle Fire HDX
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.KindleFireHDXLandscape">
            <summary>
            Kindle Fire HDX landscape
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.LGOptimusL70">
            <summary>
            LG Optimus L70
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.LGOptimusL70Landscape">
            <summary>
            LG Optimus L70 landscape
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.MicrosoftLumia550">
            <summary>
            Microsoft Lumia 550
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.MicrosoftLumia950">
            <summary>
            Microsoft Lumia 950
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.MicrosoftLumia950Landscape">
            <summary>
            Microsoft Lumia 950 landscape
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.Nexus10">
            <summary>
            Nexus 10
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.Nexus10Landscape">
            <summary>
            Nexus 10 landscape
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.Nexus4">
            <summary>
            Nexus 4
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.Nexus4Landscape">
            <summary>
            Nexus 4 landscape
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.Nexus5">
            <summary>
            Nexus 5
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.Nexus5Landscape">
            <summary>
            Nexus 5 landscape
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.Nexus5X">
            <summary>
            Nexus 5X
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.Nexus5XLandscape">
            <summary>
            Nexus 5X landscape
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.Nexus6">
            <summary>
            Nexus 6
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.Nexus6Landscape">
            <summary>
            Nexus 6 landscape
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.Nexus6P">
            <summary>
            Nexus 6P
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.Nexus6PLandscape">
            <summary>
            Nexus 6P landscape
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.Nexus7">
            <summary>
            Nexus 7
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.Nexus7Landscape">
            <summary>
            Nexus 7 landscape
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.NokiaLumia520">
            <summary>
            Nokia Lumia 520
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.NokiaLumia520Landscape">
            <summary>
            Nokia Lumia 520 landscape
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.NokiaN9">
            <summary>
            Nokia N9
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.NokiaN9Landscape">
            <summary>
            Nokia N9 landscape
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.Pixel2">
            <summary>
            Pixel 2
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.Pixel2Landscape">
            <summary>
            Pixel 2 landscape
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.Pixel2XL">
            <summary>
            Pixel 2 XL
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Mobile.DeviceDescriptorName.Pixel2XLLandscape">
            <summary>
            Pixel 2 XL landscape
            </summary>
        </member>
        <member name="T:PuppeteerSharp.Mobile.DeviceDescriptors">
            <summary>
            Device descriptors.
            </summary>
        </member>
        <member name="M:PuppeteerSharp.Mobile.DeviceDescriptors.Get(PuppeteerSharp.Mobile.DeviceDescriptorName)">
            <summary>
            Get the specified device description.
            </summary>
            <returns>The device descriptor.</returns>
            <param name="name">Device Name.</param>
        </member>
        <member name="T:PuppeteerSharp.NavigationException">
            <summary>
            Exception thrown when a <see cref="T:PuppeteerSharp.Page"/> fails to navigate an URL.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.NavigationException.Url">
            <summary>
            Url that caused the exception
            </summary>
            <value>The URL.</value>
        </member>
        <member name="M:PuppeteerSharp.NavigationException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:PuppeteerSharp.NavigationException"/> class.
            </summary>
        </member>
        <member name="M:PuppeteerSharp.NavigationException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:PuppeteerSharp.NavigationException"/> class.
            </summary>
            <param name="message">Message.</param>
        </member>
        <member name="M:PuppeteerSharp.NavigationException.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:PuppeteerSharp.NavigationException"/> class.
            </summary>
            <param name="message">Message.</param>
            <param name="url">Url.</param>
        </member>
        <member name="M:PuppeteerSharp.NavigationException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:PuppeteerSharp.NavigationException"/> class.
            </summary>
            <param name="message">Message.</param>
            <param name="innerException">Inner exception.</param>
        </member>
        <member name="M:PuppeteerSharp.NavigationException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:PuppeteerSharp.NavigationException"/> class.
            </summary>
            <param name="info">Info.</param>
            <param name="context">Context.</param>
        </member>
        <member name="P:PuppeteerSharp.NavigationException.Message">
            <inheritdoc/>
        </member>
        <member name="T:PuppeteerSharp.NavigationOptions">
            <summary>
            Navigation options used by <see cref="M:PuppeteerSharp.Page.WaitForNavigationAsync(PuppeteerSharp.NavigationOptions)"/> and <see cref="M:PuppeteerSharp.Page.SetContentAsync(System.String,PuppeteerSharp.NavigationOptions)"/>
            </summary>
        </member>
        <member name="P:PuppeteerSharp.NavigationOptions.Timeout">
            <summary>
            Maximum navigation time in milliseconds, defaults to 30 seconds, pass <c>0</c> to disable timeout. 
            </summary>
            <remarks>
            The default value can be changed by setting the <see cref="P:PuppeteerSharp.Page.DefaultNavigationTimeout"/> or <see cref="P:PuppeteerSharp.Page.DefaultTimeout"/> property.
            </remarks>
        </member>
        <member name="P:PuppeteerSharp.NavigationOptions.WaitUntil">
            <summary>
            When to consider navigation succeeded, defaults to <see cref="F:PuppeteerSharp.WaitUntilNavigation.Load"/>. Given an array of <see cref="T:PuppeteerSharp.WaitUntilNavigation"/>, navigation is considered to be successful after all events have been fired
            </summary>
        </member>
        <member name="P:PuppeteerSharp.NavigationOptions.Referer">
            <summary>
            Referer header value. If provided it will take prefrence over the referer header value set by <see cref="M:PuppeteerSharp.Page.SetExtraHttpHeadersAsync(System.Collections.Generic.Dictionary{System.String,System.String})"/>
            </summary>
            <value>The referer.</value>
        </member>
        <member name="T:PuppeteerSharp.OverridePermission">
            <summary>
            Override permission.
            </summary>
            <seealso cref="M:PuppeteerSharp.BrowserContext.OverridePermissionsAsync(System.String,System.Collections.Generic.IEnumerable{PuppeteerSharp.OverridePermission})"/>
        </member>
        <member name="F:PuppeteerSharp.OverridePermission.Geolocation">
            <summary>
            Geolocation.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.OverridePermission.Midi">
            <summary>
            MIDI.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.OverridePermission.Notifications">
            <summary>
            Notifications.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.OverridePermission.Push">
            <summary>
            Push.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.OverridePermission.Camera">
            <summary>
            Camera.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.OverridePermission.Microphone">
            <summary>
            Microphone.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.OverridePermission.BackgroundSync">
            <summary>
            Background sync.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.OverridePermission.Sensors">
            <summary>
            Ambient light sensor, Accelerometer, Gyroscope, Magnetometer
            </summary>
        </member>
        <member name="F:PuppeteerSharp.OverridePermission.AccessibilityEvents">
            <summary>
            Accessibility events.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.OverridePermission.ClipboardRead">
            <summary>
            Clipboard read.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.OverridePermission.ClipboardWrite">
            <summary>
            Clipboard write.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.OverridePermission.PaymentHandler">
            <summary>
            Payment handler.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.OverridePermission.MidiSysex">
            <summary>
            MIDI sysex.
            </summary>
        </member>
        <member name="T:PuppeteerSharp.Page">
            <summary>
            Provides methods to interact with a single tab in Chromium. One <see cref="P:PuppeteerSharp.Page.Browser"/> instance might have multiple <see cref="T:PuppeteerSharp.Page"/> instances.
            </summary>
            <example>
            This example creates a page, navigates it to a URL, and then saves a screenshot:
            <code>
            var browser = await Puppeteer.LaunchAsync(new LaunchOptions());
            var page = await browser.NewPageAsync();
            await page.GoToAsync("https://example.com");
            await page.ScreenshotAsync("screenshot.png");
            await browser.CloseAsync();
            </code>
            </example>
        </member>
        <member name="E:PuppeteerSharp.Page.Load">
            <summary>
            Raised when the JavaScript <c>load</c> <see href="https://developer.mozilla.org/en-US/docs/Web/Events/load"/> event is dispatched.
            </summary>
        </member>
        <member name="E:PuppeteerSharp.Page.Error">
            <summary>
            Raised when the page crashes
            </summary>
        </member>
        <member name="E:PuppeteerSharp.Page.Metrics">
            <summary>
            Raised when the JavaScript code makes a call to <c>console.timeStamp</c>. For the list of metrics see <see cref="M:PuppeteerSharp.Page.MetricsAsync"/>.
            </summary>
        </member>
        <member name="E:PuppeteerSharp.Page.Dialog">
            <summary>
            Raised when a JavaScript dialog appears, such as <c>alert</c>, <c>prompt</c>, <c>confirm</c> or <c>beforeunload</c>. Puppeteer can respond to the dialog via <see cref="E:PuppeteerSharp.Page.Dialog"/>'s <see cref="M:PuppeteerSharp.Dialog.Accept(System.String)"/> or <see cref="M:PuppeteerSharp.Dialog.Dismiss"/> methods.
            </summary>
        </member>
        <member name="E:PuppeteerSharp.Page.DOMContentLoaded">
            <summary>
            Raised when the JavaScript <c>DOMContentLoaded</c> <see href="https://developer.mozilla.org/en-US/docs/Web/Events/DOMContentLoaded"/> event is dispatched.
            </summary>
        </member>
        <member name="E:PuppeteerSharp.Page.Console">
            <summary>
            Raised when JavaScript within the page calls one of console API methods, e.g. <c>console.log</c> or <c>console.dir</c>. Also emitted if the page throws an error or a warning.
            The arguments passed into <c>console.log</c> appear as arguments on the event handler.
            </summary>
            <example>
            An example of handling <see cref="E:PuppeteerSharp.Page.Console"/> event:
            <code>
            <![CDATA[
            page.Console += (sender, e) => 
            {
                for (var i = 0; i < e.Message.Args.Count; ++i)
                {
                    System.Console.WriteLine($"{i}: {e.Message.Args[i]}");
                }
            }
            ]]>
            </code>
            </example>
        </member>
        <member name="E:PuppeteerSharp.Page.FrameAttached">
            <summary>
            Raised when a frame is attached.
            </summary>
        </member>
        <member name="E:PuppeteerSharp.Page.FrameDetached">
            <summary>
            Raised when a frame is detached.
            </summary>
        </member>
        <member name="E:PuppeteerSharp.Page.FrameNavigated">
            <summary>
            Raised when a frame is navigated to a new url.
            </summary>
        </member>
        <member name="E:PuppeteerSharp.Page.Response">
            <summary>
            Raised when a <see cref="E:PuppeteerSharp.Page.Response"/> is received.
            </summary>
        </member>
        <member name="E:PuppeteerSharp.Page.Request">
            <summary>
            Raised when a page issues a request. The <see cref="E:PuppeteerSharp.Page.Request"/> object is read-only.
            In order to intercept and mutate requests, see <see cref="M:PuppeteerSharp.Page.SetRequestInterceptionAsync(System.Boolean)"/>
            </summary>
        </member>
        <member name="E:PuppeteerSharp.Page.RequestFinished">
            <summary>
            Raised when a request finishes successfully.
            </summary>
        </member>
        <member name="E:PuppeteerSharp.Page.RequestFailed">
            <summary>
            Raised when a request fails, for example by timing out.
            </summary>
        </member>
        <member name="E:PuppeteerSharp.Page.PageError">
            <summary>
            Raised when an uncaught exception happens within the page.
            </summary>
        </member>
        <member name="E:PuppeteerSharp.Page.WorkerCreated">
            <summary>
            Emitted when a dedicated WebWorker (<see href="https://developer.mozilla.org/en-US/docs/Web/API/Web_Workers_API"/>) is spawned by the page.
            </summary>
        </member>
        <member name="E:PuppeteerSharp.Page.WorkerDestroyed">
            <summary>
            Emitted when a dedicated WebWorker (<see href="https://developer.mozilla.org/en-US/docs/Web/API/Web_Workers_API"/>) is terminated.
            </summary>
        </member>
        <member name="E:PuppeteerSharp.Page.Close">
            <summary>
            Raised when the page closes.
            </summary>
        </member>
        <member name="E:PuppeteerSharp.Page.Popup">
            <summary>
            Raised when the page opens a new tab or window.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Page.DefaultNavigationTimeout">
            <summary>
            This setting will change the default maximum time for the following methods:
            - <see cref="M:PuppeteerSharp.Page.GoToAsync(System.String,PuppeteerSharp.NavigationOptions)"/>
            - <see cref="M:PuppeteerSharp.Page.GoBackAsync(PuppeteerSharp.NavigationOptions)"/>
            - <see cref="M:PuppeteerSharp.Page.GoForwardAsync(PuppeteerSharp.NavigationOptions)"/>
            - <see cref="M:PuppeteerSharp.Page.ReloadAsync(PuppeteerSharp.NavigationOptions)"/>
            - <see cref="M:PuppeteerSharp.Page.SetContentAsync(System.String,PuppeteerSharp.NavigationOptions)"/>
            - <see cref="M:PuppeteerSharp.Page.WaitForNavigationAsync(PuppeteerSharp.NavigationOptions)"/>
            **NOTE** <see cref="P:PuppeteerSharp.Page.DefaultNavigationTimeout"/> takes priority over <seealso cref="P:PuppeteerSharp.Page.DefaultTimeout"/>
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Page.DefaultTimeout">
            <summary>
            This setting will change the default maximum times for the following methods:
            - <see cref="M:PuppeteerSharp.Page.GoBackAsync(PuppeteerSharp.NavigationOptions)"/>
            - <see cref="M:PuppeteerSharp.Page.GoForwardAsync(PuppeteerSharp.NavigationOptions)"/>
            - <see cref="M:PuppeteerSharp.Page.GoToAsync(System.String,PuppeteerSharp.NavigationOptions)"/>
            - <see cref="M:PuppeteerSharp.Page.ReloadAsync(PuppeteerSharp.NavigationOptions)"/>
            - <see cref="M:PuppeteerSharp.Page.SetContentAsync(System.String,PuppeteerSharp.NavigationOptions)"/>
            - <see cref="M:PuppeteerSharp.Page.WaitForFunctionAsync(System.String,System.Object[])"/>
            - <see cref="M:PuppeteerSharp.Page.WaitForNavigationAsync(PuppeteerSharp.NavigationOptions)"/>
            - <see cref="M:PuppeteerSharp.Page.WaitForRequestAsync(System.String,PuppeteerSharp.WaitForOptions)"/>
            - <see cref="M:PuppeteerSharp.Page.WaitForResponseAsync(System.String,PuppeteerSharp.WaitForOptions)"/>
            - <see cref="M:PuppeteerSharp.Page.WaitForXPathAsync(System.String,PuppeteerSharp.WaitForSelectorOptions)"/>
            - <see cref="M:PuppeteerSharp.Page.WaitForSelectorAsync(System.String,PuppeteerSharp.WaitForSelectorOptions)"/>
            - <see cref="M:PuppeteerSharp.Page.WaitForExpressionAsync(System.String,PuppeteerSharp.WaitForFunctionOptions)"/>
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Page.MainFrame">
            <summary>
            Gets page's main frame
            </summary>
            <remarks>
            Page is guaranteed to have a main frame which persists during navigations.
            </remarks>
        </member>
        <member name="P:PuppeteerSharp.Page.Frames">
            <summary>
            Gets all frames attached to the page.
            </summary>
            <value>An array of all frames attached to the page.</value>
        </member>
        <member name="P:PuppeteerSharp.Page.Workers">
            <summary>
            Gets all workers in the page.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Page.Url">
            <summary>
            Shortcut for <c>page.MainFrame.Url</c>
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Page.Target">
            <summary>
            Gets that target this page was created from.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Page.Keyboard">
            <summary>
            Gets this page's keyboard
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Page.Touchscreen">
            <summary>
            Gets this page's touchscreen
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Page.Coverage">
            <summary>
            Gets this page's coverage
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Page.Tracing">
            <summary>
            Gets this page's tracing
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Page.Mouse">
            <summary>
            Gets this page's mouse
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Page.Viewport">
            <summary>
            Gets this page's viewport
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Page.SupportedMetrics">
            <summary>
            List of supported metrics provided by the <see cref="E:PuppeteerSharp.Page.Metrics"/> event.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Page.Browser">
            <summary>
            Get the browser the page belongs to.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Page.BrowserContext">
            <summary>
            Get the browser context that the page belongs to.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Page.IsClosed">
            <summary>
            Get an indication that the page has been closed.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Page.Accessibility">
            <summary>
            Gets the accessibility.
            </summary>
        </member>
        <member name="M:PuppeteerSharp.Page.SetGeolocationAsync(PuppeteerSharp.GeolocationOption)">
            <summary>
            Sets the page's geolocation.
            </summary>
            <returns>The task.</returns>
            <param name="options">Geolocation options.</param>
            <remarks>
            Consider using <seealso cref="M:PuppeteerSharp.BrowserContext.OverridePermissionsAsync(System.String,System.Collections.Generic.IEnumerable{PuppeteerSharp.OverridePermission})"/> to grant permissions for the page to read its geolocation.
            </remarks>
        </member>
        <member name="M:PuppeteerSharp.Page.MetricsAsync">
            <summary>
            Returns metrics
            </summary>
            <returns>Task which resolves into a list of metrics</returns>
            <remarks>
            All timestamps are in monotonic time: monotonically increasing time in seconds since an arbitrary point in the past.
            </remarks>
        </member>
        <member name="M:PuppeteerSharp.Page.TapAsync(System.String)">
            <summary>
            Fetches an element with <paramref name="selector"/>, scrolls it into view if needed, and then uses <see cref="P:PuppeteerSharp.Page.Touchscreen"/> to tap in the center of the element.
            </summary>
            <param name="selector">A selector to search for element to tap. If there are multiple elements satisfying the selector, the first will be clicked.</param>
            <exception cref="T:PuppeteerSharp.SelectorException">If there's no element matching <paramref name="selector"/></exception>
            <returns>Task which resolves when the element matching <paramref name="selector"/> is successfully tapped</returns>
        </member>
        <member name="M:PuppeteerSharp.Page.QuerySelectorAsync(System.String)">
            <summary>
            The method runs <c>document.querySelector</c> within the page. If no element matches the selector, the return value resolve to <c>null</c>.
            </summary>
            <param name="selector">A selector to query page for</param>
            <returns>Task which resolves to <see cref="T:PuppeteerSharp.ElementHandle"/> pointing to the frame element</returns>
            <remarks>
            Shortcut for <c>page.MainFrame.QuerySelectorAsync(selector)</c>
            </remarks>
            <seealso cref="M:PuppeteerSharp.Frame.QuerySelectorAsync(System.String)"/>
        </member>
        <member name="M:PuppeteerSharp.Page.QuerySelectorAllAsync(System.String)">
            <summary>
            Runs <c>document.querySelectorAll</c> within the page. If no elements match the selector, the return value resolve to <see cref="M:System.Array.Empty``1"/>.
            </summary>
            <param name="selector">A selector to query page for</param>
            <returns>Task which resolves to ElementHandles pointing to the frame elements</returns>
            <seealso cref="M:PuppeteerSharp.Frame.QuerySelectorAllAsync(System.String)"/>
        </member>
        <member name="M:PuppeteerSharp.Page.QuerySelectorAllHandleAsync(System.String)">
            <summary>
            A utility function to be used with <see cref="M:PuppeteerSharp.Extensions.EvaluateFunctionAsync``1(System.Threading.Tasks.Task{PuppeteerSharp.JSHandle},System.String,System.Object[])"/>
            </summary>
            <param name="selector">A selector to query page for</param>
            <returns>Task which resolves to a <see cref="T:PuppeteerSharp.JSHandle"/> of <c>document.querySelectorAll</c> result</returns>
        </member>
        <member name="M:PuppeteerSharp.Page.XPathAsync(System.String)">
            <summary>
            Evaluates the XPath expression
            </summary>
            <param name="expression">Expression to evaluate <see href="https://developer.mozilla.org/en-US/docs/Web/API/Document/evaluate"/></param>
            <returns>Task which resolves to an array of <see cref="T:PuppeteerSharp.ElementHandle"/></returns>
            <remarks>
            Shortcut for <c>page.MainFrame.XPathAsync(expression)</c>
            </remarks>
            <seealso cref="M:PuppeteerSharp.Frame.XPathAsync(System.String)"/>
        </member>
        <member name="M:PuppeteerSharp.Page.EvaluateExpressionHandleAsync(System.String)">
            <summary>
            Executes a script in browser context
            </summary>
            <param name="script">Script to be evaluated in browser context</param>
            <remarks>
            If the script, returns a Promise, then the method would wait for the promise to resolve and return its value.
            </remarks>
            <returns>Task which resolves to script return value</returns>
        </member>
        <member name="M:PuppeteerSharp.Page.EvaluateFunctionHandleAsync(System.String,System.Object[])">
            <summary>
            Executes a script in browser context
            </summary>
            <param name="pageFunction">Script to be evaluated in browser context</param>
            <param name="args">Function arguments</param>
            <remarks>
            If the script, returns a Promise, then the method would wait for the promise to resolve and return its value.
            <see cref="T:PuppeteerSharp.JSHandle"/> instances can be passed as arguments
            </remarks>
            <returns>Task which resolves to script return value</returns>
        </member>
        <member name="M:PuppeteerSharp.Page.EvaluateOnNewDocumentAsync(System.String,System.Object[])">
            <summary>
            Adds a function which would be invoked in one of the following scenarios:
            - whenever the page is navigated
            - whenever the child frame is attached or navigated. In this case, the function is invoked in the context of the newly attached frame
            </summary>
            <param name="pageFunction">Function to be evaluated in browser context</param>
            <param name="args">Arguments to pass to <c>pageFunction</c></param>
            <remarks>
            The function is invoked after the document was created but before any of its scripts were run. This is useful to amend JavaScript environment, e.g. to seed <c>Math.random</c>.
            </remarks>
            <example>
            An example of overriding the navigator.languages property before the page loads:
            <code>
            await page.EvaluateOnNewDocumentAsync("() => window.__example = true");
            </code>
            </example>
            <returns>Task</returns>
        </member>
        <member name="M:PuppeteerSharp.Page.QueryObjectsAsync(PuppeteerSharp.JSHandle)">
            <summary>
            The method iterates JavaScript heap and finds all the objects with the given prototype.
            Shortcut for <c>page.MainFrame.GetExecutionContextAsync().QueryObjectsAsync(prototypeHandle)</c>.
            </summary>
            <returns>A task which resolves to a handle to an array of objects with this prototype.</returns>
            <param name="prototypeHandle">A handle to the object prototype.</param>
        </member>
        <member name="M:PuppeteerSharp.Page.SetRequestInterceptionAsync(System.Boolean)">
            <summary>
            Activating request interception enables <see cref="M:PuppeteerSharp.Request.AbortAsync(PuppeteerSharp.RequestAbortErrorCode)">request.AbortAsync</see>, 
            <see cref="M:PuppeteerSharp.Request.ContinueAsync(PuppeteerSharp.Payload)">request.ContinueAsync</see> and <see cref="M:PuppeteerSharp.Request.RespondAsync(PuppeteerSharp.ResponseData)">request.RespondAsync</see> methods.
            </summary>
            <returns>The request interception task.</returns>
            <param name="value">Whether to enable request interception..</param>
        </member>
        <member name="M:PuppeteerSharp.Page.SetOfflineModeAsync(System.Boolean)">
            <summary>
            Set offline mode for the page.
            </summary>
            <returns>Result task</returns>
            <param name="value">When <c>true</c> enables offline mode for the page.</param>
        </member>
        <member name="M:PuppeteerSharp.Page.GetCookiesAsync(System.String[])">
            <summary>
            Returns the page's cookies
            </summary>
            <param name="urls">Url's to return cookies for</param>
            <returns>Array of cookies</returns>
            <remarks>
            If no URLs are specified, this method returns cookies for the current page URL.
            If URLs are specified, only cookies for those URLs are returned.
            </remarks>
        </member>
        <member name="M:PuppeteerSharp.Page.SetCookieAsync(PuppeteerSharp.CookieParam[])">
            <summary>
            Clears all of the current cookies and then sets the cookies for the page
            </summary>
            <param name="cookies">Cookies to set</param>
            <returns>Task</returns>
        </member>
        <member name="M:PuppeteerSharp.Page.DeleteCookieAsync(PuppeteerSharp.CookieParam[])">
            <summary>
            Deletes cookies from the page
            </summary>
            <param name="cookies">Cookies to delete</param>
            <returns>Task</returns>
        </member>
        <member name="M:PuppeteerSharp.Page.AddScriptTagAsync(PuppeteerSharp.AddTagOptions)">
            <summary>
            Adds a <c><![CDATA[<script>]]></c> tag into the page with the desired url or content
            </summary>
            <param name="options">add script tag options</param>
            <remarks>
            Shortcut for <c>page.MainFrame.AddScriptTagAsync(options)</c>
            </remarks>
            <returns>Task which resolves to the added tag when the script's onload fires or when the script content was injected into frame</returns>
            <seealso cref="M:PuppeteerSharp.Frame.AddScriptTagAsync(PuppeteerSharp.AddTagOptions)"/>
        </member>
        <member name="M:PuppeteerSharp.Page.AddScriptTagAsync(System.String)">
            <summary>
            Adds a <c><![CDATA[<script>]]></c> tag into the page with the desired url or content
            </summary>
            <param name="url">script url</param>
            <remarks>
            Shortcut for <c>page.MainFrame.AddScriptTagAsync(new AddTagOptions { Url = url })</c>
            </remarks>
            <returns>Task which resolves to the added tag when the script's onload fires or when the script content was injected into frame</returns>
        </member>
        <member name="M:PuppeteerSharp.Page.AddStyleTagAsync(PuppeteerSharp.AddTagOptions)">
            <summary>
            Adds a <c><![CDATA[<link rel="stylesheet">]]></c> tag into the page with the desired url or a <c><![CDATA[<link rel="stylesheet">]]></c> tag with the content
            </summary>
            <param name="options">add style tag options</param>
            <remarks>
            Shortcut for <c>page.MainFrame.AddStyleTagAsync(options)</c>
            </remarks>
            <returns>Task which resolves to the added tag when the stylesheet's onload fires or when the CSS content was injected into frame</returns>
            <seealso cref="M:PuppeteerSharp.Frame.AddStyleTag(PuppeteerSharp.AddTagOptions)"/>
        </member>
        <member name="M:PuppeteerSharp.Page.AddStyleTagAsync(System.String)">
            <summary>
            Adds a <c><![CDATA[<link rel="stylesheet">]]></c> tag into the page with the desired url or a <c><![CDATA[<link rel="stylesheet">]]></c> tag with the content
            </summary>
            <param name="url">stylesheel url</param>
            <remarks>
            Shortcut for <c>page.MainFrame.AddStyleTagAsync(new AddTagOptions { Url = url })</c>
            </remarks>
            <returns>Task which resolves to the added tag when the stylesheet's onload fires or when the CSS content was injected into frame</returns>
        </member>
        <member name="M:PuppeteerSharp.Page.ExposeFunctionAsync(System.String,System.Action)">
            <summary>
            Adds a function called <c>name</c> on the page's <c>window</c> object.
            When called, the function executes <paramref name="puppeteerFunction"/> in C# and returns a <see cref="T:System.Threading.Tasks.Task"/> which resolves when <paramref name="puppeteerFunction"/> completes.
            </summary>
            <param name="name">Name of the function on the window object</param>
            <param name="puppeteerFunction">Callback function which will be called in Puppeteer's context.</param>
            <remarks>
            If the <paramref name="puppeteerFunction"/> returns a <see cref="T:System.Threading.Tasks.Task"/>, it will be awaited.
            Functions installed via <see cref="M:PuppeteerSharp.Page.ExposeFunctionAsync(System.String,System.Action)"/> survive navigations
            </remarks>
            <returns>Task</returns>
        </member>
        <member name="M:PuppeteerSharp.Page.ExposeFunctionAsync``1(System.String,System.Func{``0})">
            <summary>
            Adds a function called <c>name</c> on the page's <c>window</c> object.
            When called, the function executes <paramref name="puppeteerFunction"/> in C# and returns a <see cref="T:System.Threading.Tasks.Task"/> which resolves to the return value of <paramref name="puppeteerFunction"/>.
            </summary>
            <typeparam name="TResult">The result of <paramref name="puppeteerFunction"/></typeparam>
            <param name="name">Name of the function on the window object</param>
            <param name="puppeteerFunction">Callback function which will be called in Puppeteer's context.</param>
            <remarks>
            If the <paramref name="puppeteerFunction"/> returns a <see cref="T:System.Threading.Tasks.Task"/>, it will be awaited.
            Functions installed via <see cref="M:PuppeteerSharp.Page.ExposeFunctionAsync``1(System.String,System.Func{``0})"/> survive navigations
            </remarks>
            <returns>Task</returns>
        </member>
        <member name="M:PuppeteerSharp.Page.ExposeFunctionAsync``2(System.String,System.Func{``0,``1})">
            <summary>
            Adds a function called <c>name</c> on the page's <c>window</c> object.
            When called, the function executes <paramref name="puppeteerFunction"/> in C# and returns a <see cref="T:System.Threading.Tasks.Task"/> which resolves to the return value of <paramref name="puppeteerFunction"/>.
            </summary>
            <typeparam name="TResult">The result of <paramref name="puppeteerFunction"/></typeparam>
            <typeparam name="T">The parameter of <paramref name="puppeteerFunction"/></typeparam>
            <param name="name">Name of the function on the window object</param>
            <param name="puppeteerFunction">Callback function which will be called in Puppeteer's context.</param>
            <remarks>
            If the <paramref name="puppeteerFunction"/> returns a <see cref="T:System.Threading.Tasks.Task"/>, it will be awaited.
            Functions installed via <see cref="M:PuppeteerSharp.Page.ExposeFunctionAsync``2(System.String,System.Func{``0,``1})"/> survive navigations
            </remarks>
            <returns>Task</returns>
        </member>
        <member name="M:PuppeteerSharp.Page.ExposeFunctionAsync``3(System.String,System.Func{``0,``1,``2})">
            <summary>
            Adds a function called <c>name</c> on the page's <c>window</c> object.
            When called, the function executes <paramref name="puppeteerFunction"/> in C# and returns a <see cref="T:System.Threading.Tasks.Task"/> which resolves to the return value of <paramref name="puppeteerFunction"/>.
            </summary>
            <typeparam name="TResult">The result of <paramref name="puppeteerFunction"/></typeparam>
            <typeparam name="T1">The first parameter of <paramref name="puppeteerFunction"/></typeparam>
            <typeparam name="T2">The second parameter of <paramref name="puppeteerFunction"/></typeparam>
            <param name="name">Name of the function on the window object</param>
            <param name="puppeteerFunction">Callback function which will be called in Puppeteer's context.</param>
            <remarks>
            If the <paramref name="puppeteerFunction"/> returns a <see cref="T:System.Threading.Tasks.Task"/>, it will be awaited.
            Functions installed via <see cref="M:PuppeteerSharp.Page.ExposeFunctionAsync``3(System.String,System.Func{``0,``1,``2})"/> survive navigations
            </remarks>
            <returns>Task</returns>
        </member>
        <member name="M:PuppeteerSharp.Page.ExposeFunctionAsync``4(System.String,System.Func{``0,``1,``2,``3})">
            <summary>
            Adds a function called <c>name</c> on the page's <c>window</c> object.
            When called, the function executes <paramref name="puppeteerFunction"/> in C# and returns a <see cref="T:System.Threading.Tasks.Task"/> which resolves to the return value of <paramref name="puppeteerFunction"/>.
            </summary>
            <typeparam name="TResult">The result of <paramref name="puppeteerFunction"/></typeparam>
            <typeparam name="T1">The first parameter of <paramref name="puppeteerFunction"/></typeparam>
            <typeparam name="T2">The second parameter of <paramref name="puppeteerFunction"/></typeparam>
            <typeparam name="T3">The third parameter of <paramref name="puppeteerFunction"/></typeparam>
            <param name="name">Name of the function on the window object</param>
            <param name="puppeteerFunction">Callback function which will be called in Puppeteer's context.</param>
            <remarks>
            If the <paramref name="puppeteerFunction"/> returns a <see cref="T:System.Threading.Tasks.Task"/>, it will be awaited.
            Functions installed via <see cref="M:PuppeteerSharp.Page.ExposeFunctionAsync``4(System.String,System.Func{``0,``1,``2,``3})"/> survive navigations
            </remarks>
            <returns>Task</returns>
        </member>
        <member name="M:PuppeteerSharp.Page.ExposeFunctionAsync``5(System.String,System.Func{``0,``1,``2,``3,``4})">
            <summary>
            Adds a function called <c>name</c> on the page's <c>window</c> object.
            When called, the function executes <paramref name="puppeteerFunction"/> in C# and returns a <see cref="T:System.Threading.Tasks.Task"/> which resolves to the return value of <paramref name="puppeteerFunction"/>.
            </summary>
            <typeparam name="TResult">The result of <paramref name="puppeteerFunction"/></typeparam>
            <typeparam name="T1">The first parameter of <paramref name="puppeteerFunction"/></typeparam>
            <typeparam name="T2">The second parameter of <paramref name="puppeteerFunction"/></typeparam>
            <typeparam name="T3">The third parameter of <paramref name="puppeteerFunction"/></typeparam>
            <typeparam name="T4">The fourth parameter of <paramref name="puppeteerFunction"/></typeparam>
            <param name="name">Name of the function on the window object</param>
            <param name="puppeteerFunction">Callback function which will be called in Puppeteer's context.</param>
            <remarks>
            If the <paramref name="puppeteerFunction"/> returns a <see cref="T:System.Threading.Tasks.Task"/>, it will be awaited.
            Functions installed via <see cref="M:PuppeteerSharp.Page.ExposeFunctionAsync``5(System.String,System.Func{``0,``1,``2,``3,``4})"/> survive navigations
            </remarks>
            <returns>Task</returns>
        </member>
        <member name="M:PuppeteerSharp.Page.GetContentAsync">
            <summary>
            Gets the full HTML contents of the page, including the doctype.
            </summary>
            <returns>Task which resolves to the HTML content.</returns>
            <seealso cref="M:PuppeteerSharp.Frame.GetContentAsync"/>
        </member>
        <member name="M:PuppeteerSharp.Page.SetContentAsync(System.String,PuppeteerSharp.NavigationOptions)">
            <summary>
            Sets the HTML markup to the page
            </summary>
            <param name="html">HTML markup to assign to the page.</param>
            <param name="options">The navigations options</param>
            <returns>Task.</returns>
            <seealso cref="M:PuppeteerSharp.Frame.SetContentAsync(System.String,PuppeteerSharp.NavigationOptions)"/>
        </member>
        <member name="M:PuppeteerSharp.Page.GoToAsync(System.String,PuppeteerSharp.NavigationOptions)">
            <summary>
            Navigates to an url
            </summary>        
            <param name="url">URL to navigate page to. The url should include scheme, e.g. https://.</param>
            <param name="options">Navigation parameters.</param>
            <returns>Task which resolves to the main resource response. In case of multiple redirects, the navigation will resolve with the response of the last redirect.</returns>
            <seealso cref="M:PuppeteerSharp.Page.GoToAsync(System.String,System.Nullable{System.Int32},PuppeteerSharp.WaitUntilNavigation[])"/>
        </member>
        <member name="M:PuppeteerSharp.Page.GoToAsync(System.String,System.Nullable{System.Int32},PuppeteerSharp.WaitUntilNavigation[])">
            <summary>
            Navigates to an url
            </summary>
            <param name="url">URL to navigate page to. The url should include scheme, e.g. https://.</param>
            <param name="timeout">Maximum navigation time in milliseconds, defaults to 30 seconds, pass <c>0</c> to disable timeout. </param>
            <param name="waitUntil">When to consider navigation succeeded, defaults to <see cref="F:PuppeteerSharp.WaitUntilNavigation.Load"/>. Given an array of <see cref="T:PuppeteerSharp.WaitUntilNavigation"/>, navigation is considered to be successful after all events have been fired</param>
            <returns>Task which resolves to the main resource response. In case of multiple redirects, the navigation will resolve with the response of the last redirect</returns>
            <seealso cref="M:PuppeteerSharp.Page.GoToAsync(System.String,PuppeteerSharp.NavigationOptions)"/>
        </member>
        <member name="M:PuppeteerSharp.Page.GoToAsync(System.String,PuppeteerSharp.WaitUntilNavigation)">
            <summary>
            Navigates to an url
            </summary>
            <param name="url">URL to navigate page to. The url should include scheme, e.g. https://.</param>
            <param name="waitUntil">When to consider navigation succeeded.</param>
            <returns>Task which resolves to the main resource response. In case of multiple redirects, the navigation will resolve with the response of the last redirect</returns>
            <seealso cref="M:PuppeteerSharp.Page.GoToAsync(System.String,PuppeteerSharp.NavigationOptions)"/>
        </member>
        <member name="M:PuppeteerSharp.Page.PdfAsync(System.String)">
            <summary>
            generates a pdf of the page with <see cref="F:PuppeteerSharp.Media.MediaType.Print"/> css media. To generate a pdf with <see cref="F:PuppeteerSharp.Media.MediaType.Screen"/> media call <see cref="M:PuppeteerSharp.Page.EmulateMediaAsync(PuppeteerSharp.Media.MediaType)"/> with <see cref="F:PuppeteerSharp.Media.MediaType.Screen"/>
            </summary>
            <param name="file">The file path to save the PDF to. paths are resolved using <see cref="M:System.IO.Path.GetFullPath(System.String)"/></param>
            <returns></returns>
            <remarks>
            Generating a pdf is currently only supported in Chrome headless
            </remarks>
        </member>
        <member name="M:PuppeteerSharp.Page.PdfAsync(System.String,PuppeteerSharp.PdfOptions)">
            <summary>
             generates a pdf of the page with <see cref="F:PuppeteerSharp.Media.MediaType.Print"/> css media. To generate a pdf with <see cref="F:PuppeteerSharp.Media.MediaType.Screen"/> media call <see cref="M:PuppeteerSharp.Page.EmulateMediaAsync(PuppeteerSharp.Media.MediaType)"/> with <see cref="F:PuppeteerSharp.Media.MediaType.Screen"/>
            </summary>
            <param name="file">The file path to save the PDF to. paths are resolved using <see cref="M:System.IO.Path.GetFullPath(System.String)"/></param>
            <param name="options">pdf options</param>
            <returns></returns>
            <remarks>
            Generating a pdf is currently only supported in Chrome headless
            </remarks>
        </member>
        <member name="M:PuppeteerSharp.Page.PdfStreamAsync">
            <summary>
            generates a pdf of the page with <see cref="F:PuppeteerSharp.Media.MediaType.Print"/> css media. To generate a pdf with <see cref="F:PuppeteerSharp.Media.MediaType.Screen"/> media call <see cref="M:PuppeteerSharp.Page.EmulateMediaAsync(PuppeteerSharp.Media.MediaType)"/> with <see cref="F:PuppeteerSharp.Media.MediaType.Screen"/>
            </summary>
            <returns>Task which resolves to a <see cref="T:System.IO.Stream"/> containing the PDF data.</returns>
            <remarks>
            Generating a pdf is currently only supported in Chrome headless
            </remarks>
        </member>
        <member name="M:PuppeteerSharp.Page.PdfStreamAsync(PuppeteerSharp.PdfOptions)">
            <summary>
            Generates a pdf of the page with <see cref="F:PuppeteerSharp.Media.MediaType.Print"/> css media. To generate a pdf with <see cref="F:PuppeteerSharp.Media.MediaType.Screen"/> media call <see cref="M:PuppeteerSharp.Page.EmulateMediaAsync(PuppeteerSharp.Media.MediaType)"/> with <see cref="F:PuppeteerSharp.Media.MediaType.Screen"/>
            </summary>
            <param name="options">pdf options</param>
            <returns>Task which resolves to a <see cref="T:System.IO.Stream"/> containing the PDF data.</returns>
            <remarks>
            Generating a pdf is currently only supported in Chrome headless
            </remarks>
        </member>
        <member name="M:PuppeteerSharp.Page.PdfDataAsync">
            <summary>
            Generates a pdf of the page with <see cref="F:PuppeteerSharp.Media.MediaType.Print"/> css media. To generate a pdf with <see cref="F:PuppeteerSharp.Media.MediaType.Screen"/> media call <see cref="M:PuppeteerSharp.Page.EmulateMediaAsync(PuppeteerSharp.Media.MediaType)"/> with <see cref="F:PuppeteerSharp.Media.MediaType.Screen"/>
            </summary>
            <returns>Task which resolves to a <see cref="T:System.Byte"/>[] containing the PDF data.</returns>
            <remarks>
            Generating a pdf is currently only supported in Chrome headless
            </remarks>
        </member>
        <member name="M:PuppeteerSharp.Page.PdfDataAsync(PuppeteerSharp.PdfOptions)">
            <summary>
            Generates a pdf of the page with <see cref="F:PuppeteerSharp.Media.MediaType.Print"/> css media. To generate a pdf with <see cref="F:PuppeteerSharp.Media.MediaType.Screen"/> media call <see cref="M:PuppeteerSharp.Page.EmulateMediaAsync(PuppeteerSharp.Media.MediaType)"/> with <see cref="F:PuppeteerSharp.Media.MediaType.Screen"/>
            </summary>
            <param name="options">pdf options</param>
            <returns>Task which resolves to a <see cref="T:System.Byte"/>[] containing the PDF data.</returns>
            <remarks>
            Generating a pdf is currently only supported in Chrome headless
            </remarks>
        </member>
        <member name="M:PuppeteerSharp.Page.SetJavaScriptEnabledAsync(System.Boolean)">
            <summary>
            Enables/Disables Javascript on the page
            </summary>
            <returns>Task.</returns>
            <param name="enabled">Whether or not to enable JavaScript on the page.</param>
        </member>
        <member name="M:PuppeteerSharp.Page.SetBypassCSPAsync(System.Boolean)">
            <summary>
            Toggles bypassing page's Content-Security-Policy.
            </summary>
            <param name="enabled">sets bypassing of page's Content-Security-Policy.</param>
            <returns></returns>
            <remarks>
            CSP bypassing happens at the moment of CSP initialization rather then evaluation.
            Usually this means that <see cref="M:PuppeteerSharp.Page.SetBypassCSPAsync(System.Boolean)"/> should be called before navigating to the domain.
            </remarks>
        </member>
        <member name="M:PuppeteerSharp.Page.EmulateMediaAsync(PuppeteerSharp.Media.MediaType)">
            <summary>
            Emulates a media such as screen or print.
            </summary>
            <returns>Task.</returns>
            <param name="media">Media to set.</param>
        </member>
        <member name="M:PuppeteerSharp.Page.SetViewportAsync(PuppeteerSharp.ViewPortOptions)">
            <summary>
            Sets the viewport.
            In the case of multiple pages in a single browser, each page can have its own viewport size.
            NOTE in certain cases, setting viewport will reload the page in order to set the isMobile or hasTouch properties.
            </summary>
            <returns>The viewport task.</returns>
            <param name="viewport">Viewport options.</param>
        </member>
        <member name="M:PuppeteerSharp.Page.EmulateAsync(PuppeteerSharp.Mobile.DeviceDescriptor)">
            <summary>
            Emulates given device metrics and user agent. 
            </summary>
            <remarks>
            This method is a shortcut for calling two methods:
            page.SetViewportAsync(userAgent)
            page.SetUserAgentAsync(viewport)
            </remarks>
            <returns>Task.</returns>
            <param name="options">Emulation options.</param>
        </member>
        <member name="M:PuppeteerSharp.Page.ScreenshotAsync(System.String)">
            <summary>
            Takes a screenshot of the page
            </summary>
            <returns>The screenshot task.</returns>
            <param name="file">The file path to save the image to. The screenshot type will be inferred from file extension. 
            If path is a relative path, then it is resolved relative to current working directory. If no path is provided, 
            the image won't be saved to the disk.</param>
        </member>
        <member name="M:PuppeteerSharp.Page.ScreenshotAsync(System.String,PuppeteerSharp.ScreenshotOptions)">
            <summary>
            Takes a screenshot of the page
            </summary>
            <returns>The screenshot task.</returns>
            <param name="file">The file path to save the image to. The screenshot type will be inferred from file extension. 
            If path is a relative path, then it is resolved relative to current working directory. If no path is provided, 
            the image won't be saved to the disk.</param>
            <param name="options">Screenshot options.</param>
        </member>
        <member name="M:PuppeteerSharp.Page.ScreenshotStreamAsync">
            <summary>
            Takes a screenshot of the page
            </summary>
            <returns>Task which resolves to a <see cref="T:System.IO.Stream"/> containing the image data.</returns>
        </member>
        <member name="M:PuppeteerSharp.Page.ScreenshotStreamAsync(PuppeteerSharp.ScreenshotOptions)">
            <summary>
            Takes a screenshot of the page
            </summary>
            <returns>Task which resolves to a <see cref="T:System.IO.Stream"/> containing the image data.</returns>
            <param name="options">Screenshot options.</param>
        </member>
        <member name="M:PuppeteerSharp.Page.ScreenshotBase64Async">
            <summary>
            Takes a screenshot of the page
            </summary>
            <returns>Task which resolves to a <see cref="T:System.String"/> containing the image data as base64.</returns>
        </member>
        <member name="M:PuppeteerSharp.Page.ScreenshotBase64Async(PuppeteerSharp.ScreenshotOptions)">
            <summary>
            Takes a screenshot of the page
            </summary>
            <returns>Task which resolves to a <see cref="T:System.String"/> containing the image data as base64.</returns>
            <param name="options">Screenshot options.</param>
        </member>
        <member name="M:PuppeteerSharp.Page.ScreenshotDataAsync">
            <summary>
            Takes a screenshot of the page
            </summary>
            <returns>Task which resolves to a <see cref="T:System.Byte"/>[] containing the image data.</returns>
        </member>
        <member name="M:PuppeteerSharp.Page.ScreenshotDataAsync(PuppeteerSharp.ScreenshotOptions)">
            <summary>
            Takes a screenshot of the page
            </summary>
            <returns>Task which resolves to a <see cref="T:System.Byte"/>[] containing the image data.</returns>
            <param name="options">Screenshot options.</param>
        </member>
        <member name="M:PuppeteerSharp.Page.GetTitleAsync">
            <summary>
            Returns page's title
            </summary>
            <returns>page's title</returns>
            <see cref="M:PuppeteerSharp.Frame.GetTitleAsync"/>
        </member>
        <member name="M:PuppeteerSharp.Page.CloseAsync(PuppeteerSharp.PageCloseOptions)">
            <summary>
            Closes the page.
            </summary>
            <returns>Task.</returns>
        </member>
        <member name="M:PuppeteerSharp.Page.SetCacheEnabledAsync(System.Boolean)">
            <summary>
            Toggles ignoring cache for each request based on the enabled state. By default, caching is enabled.
            </summary>
            <param name="enabled">sets the <c>enabled</c> state of the cache</param>
            <returns>Task</returns>
        </member>
        <member name="M:PuppeteerSharp.Page.ClickAsync(System.String,PuppeteerSharp.Input.ClickOptions)">
            <summary>
            Fetches an element with <paramref name="selector"/>, scrolls it into view if needed, and then uses <see cref="P:PuppeteerSharp.Page.Mouse"/> to click in the center of the element.
            </summary>
            <param name="selector">A selector to search for element to click. If there are multiple elements satisfying the selector, the first will be clicked.</param>
            <param name="options">click options</param>
            <exception cref="T:PuppeteerSharp.SelectorException">If there's no element matching <paramref name="selector"/></exception>
            <returns>Task which resolves when the element matching <paramref name="selector"/> is successfully clicked</returns>
        </member>
        <member name="M:PuppeteerSharp.Page.HoverAsync(System.String)">
            <summary>
            Fetches an element with <paramref name="selector"/>, scrolls it into view if needed, and then uses <see cref="P:PuppeteerSharp.Page.Mouse"/> to hover over the center of the element.
            </summary>
            <param name="selector">A selector to search for element to hover. If there are multiple elements satisfying the selector, the first will be hovered.</param>
            <exception cref="T:PuppeteerSharp.SelectorException">If there's no element matching <paramref name="selector"/></exception>
            <returns>Task which resolves when the element matching <paramref name="selector"/> is successfully hovered</returns>
        </member>
        <member name="M:PuppeteerSharp.Page.FocusAsync(System.String)">
            <summary>
            Fetches an element with <paramref name="selector"/> and focuses it
            </summary>
            <param name="selector">A selector to search for element to focus. If there are multiple elements satisfying the selector, the first will be focused.</param>
            <exception cref="T:PuppeteerSharp.SelectorException">If there's no element matching <paramref name="selector"/></exception>
            <returns>Task which resolves when the element matching <paramref name="selector"/> is successfully focused</returns>
        </member>
        <member name="M:PuppeteerSharp.Page.TypeAsync(System.String,System.String,PuppeteerSharp.Input.TypeOptions)">
            <summary>
            Sends a <c>keydown</c>, <c>keypress</c>/<c>input</c>, and <c>keyup</c> event for each character in the text.
            </summary>
            <param name="selector">A selector of an element to type into. If there are multiple elements satisfying the selector, the first will be used.</param>
            <param name="text">A text to type into a focused element</param>
            <param name="options"></param>
            <exception cref="T:PuppeteerSharp.SelectorException">If there's no element matching <paramref name="selector"/></exception>
            <remarks>
            To press a special key, like <c>Control</c> or <c>ArrowDown</c> use <see cref="M:PuppeteerSharp.Input.Keyboard.PressAsync(System.String,PuppeteerSharp.Input.PressOptions)"/>
            </remarks>
            <example>
            <code>
            page.TypeAsync("#mytextarea", "Hello"); // Types instantly
            page.TypeAsync("#mytextarea", "World", new TypeOptions { Delay = 100 }); // Types slower, like a user
            </code>
            </example>
            <returns>Task</returns>
        </member>
        <member name="M:PuppeteerSharp.Page.EvaluateExpressionAsync(System.String)">
            <summary>
            Executes a script in browser context
            </summary>
            <param name="script">Script to be evaluated in browser context</param>
            <remarks>
            If the script, returns a Promise, then the method would wait for the promise to resolve and return its value.
            </remarks>
            <seealso cref="M:PuppeteerSharp.Page.EvaluateFunctionAsync``1(System.String,System.Object[])"/>
            <returns>Task which resolves to script return value</returns>
        </member>
        <member name="M:PuppeteerSharp.Page.EvaluateExpressionAsync``1(System.String)">
            <summary>
            Executes a script in browser context
            </summary>
            <typeparam name="T">The type to deserialize the result to</typeparam>
            <param name="script">Script to be evaluated in browser context</param>
            <remarks>
            If the script, returns a Promise, then the method would wait for the promise to resolve and return its value.
            </remarks>
            <seealso cref="M:PuppeteerSharp.Page.EvaluateFunctionAsync``1(System.String,System.Object[])"/>
            <returns>Task which resolves to script return value</returns>
        </member>
        <member name="M:PuppeteerSharp.Page.EvaluateFunctionAsync(System.String,System.Object[])">
            <summary>
            Executes a function in browser context
            </summary>
            <param name="script">Script to be evaluated in browser context</param>
            <param name="args">Arguments to pass to script</param>
            <remarks>
            If the script, returns a Promise, then the method would wait for the promise to resolve and return its value.
            <see cref="T:PuppeteerSharp.JSHandle"/> instances can be passed as arguments
            </remarks>
            <seealso cref="M:PuppeteerSharp.Page.EvaluateExpressionAsync``1(System.String)"/>
            <returns>Task which resolves to script return value</returns>
        </member>
        <member name="M:PuppeteerSharp.Page.EvaluateFunctionAsync``1(System.String,System.Object[])">
            <summary>
            Executes a function in browser context
            </summary>
            <typeparam name="T">The type to deserialize the result to</typeparam>
            <param name="script">Script to be evaluated in browser context</param>
            <param name="args">Arguments to pass to script</param>
            <remarks>
            If the script, returns a Promise, then the method would wait for the promise to resolve and return its value.
            <see cref="T:PuppeteerSharp.JSHandle"/> instances can be passed as arguments
            </remarks>
            <seealso cref="M:PuppeteerSharp.Page.EvaluateExpressionAsync``1(System.String)"/>
            <returns>Task which resolves to script return value</returns>
        </member>
        <member name="M:PuppeteerSharp.Page.SetUserAgentAsync(System.String)">
            <summary>
            Sets the user agent to be used in this page
            </summary>
            <param name="userAgent">Specific user agent to use in this page</param>
            <returns>Task</returns>
        </member>
        <member name="M:PuppeteerSharp.Page.SetExtraHttpHeadersAsync(System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            Sets extra HTTP headers that will be sent with every request the page initiates
            </summary>
            <param name="headers">Additional http headers to be sent with every request</param>
            <returns>Task</returns>
        </member>
        <member name="M:PuppeteerSharp.Page.AuthenticateAsync(PuppeteerSharp.Credentials)">
            <summary>
            Provide credentials for http authentication <see href="https://developer.mozilla.org/en-US/docs/Web/HTTP/Authentication"/>
            </summary>
            <param name="credentials">The credentials</param>
            <returns></returns>
            <remarks>
            To disable authentication, pass <c>null</c>
            </remarks>
        </member>
        <member name="M:PuppeteerSharp.Page.ReloadAsync(PuppeteerSharp.NavigationOptions)">
            <summary>
            Reloads the page
            </summary>
            <param name="options">Navigation options</param>
            <returns>Task which resolves to the main resource response. In case of multiple redirects, the navigation will resolve with the response of the last redirect</returns>
            <seealso cref="M:PuppeteerSharp.Page.ReloadAsync(System.Nullable{System.Int32},PuppeteerSharp.WaitUntilNavigation[])"/>
        </member>
        <member name="M:PuppeteerSharp.Page.ReloadAsync(System.Nullable{System.Int32},PuppeteerSharp.WaitUntilNavigation[])">
            <summary>
            Reloads the page
            </summary>
            <param name="timeout">Maximum navigation time in milliseconds, defaults to 30 seconds, pass <c>0</c> to disable timeout. </param>
            <param name="waitUntil">When to consider navigation succeeded, defaults to <see cref="F:PuppeteerSharp.WaitUntilNavigation.Load"/>. Given an array of <see cref="T:PuppeteerSharp.WaitUntilNavigation"/>, navigation is considered to be successful after all events have been fired</param>
            <returns>Task which resolves to the main resource response. In case of multiple redirects, the navigation will resolve with the response of the last redirect</returns>
            <seealso cref="M:PuppeteerSharp.Page.ReloadAsync(PuppeteerSharp.NavigationOptions)"/>
        </member>
        <member name="M:PuppeteerSharp.Page.SelectAsync(System.String,System.String[])">
            <summary>
            Triggers a change and input event once all the provided options have been selected. 
            If there's no <![CDATA[<select>]]> element matching selector, the method throws an error.
            </summary>
            <exception cref="T:PuppeteerSharp.SelectorException">If there's no element matching <paramref name="selector"/></exception>
            <param name="selector">A selector to query page for</param>
            <param name="values">Values of options to select. If the <![CDATA[<select>]]> has the multiple attribute, 
            all values are considered, otherwise only the first one is taken into account.</param>
            <returns>Returns an array of option values that have been successfully selected.</returns>
            <seealso cref="M:PuppeteerSharp.Frame.SelectAsync(System.String,System.String[])"/>
        </member>
        <member name="M:PuppeteerSharp.Page.WaitForTimeoutAsync(System.Int32)">
            <summary>
            Waits for a timeout
            </summary>
            <param name="milliseconds"></param>
            <returns>A task that resolves when after the timeout</returns>
            <seealso cref="M:PuppeteerSharp.Frame.WaitForTimeoutAsync(System.Int32)"/>
        </member>
        <member name="M:PuppeteerSharp.Page.WaitForFunctionAsync(System.String,PuppeteerSharp.WaitForFunctionOptions,System.Object[])">
            <summary>
            Waits for a function to be evaluated to a truthy value
            </summary>
            <param name="script">Function to be evaluated in browser context</param>
            <param name="options">Optional waiting parameters</param>
            <param name="args">Arguments to pass to <c>script</c></param>
            <returns>A task that resolves when the <c>script</c> returns a truthy value</returns>
            <seealso cref="M:PuppeteerSharp.Frame.WaitForFunctionAsync(System.String,PuppeteerSharp.WaitForFunctionOptions,System.Object[])"/>
        </member>
        <member name="M:PuppeteerSharp.Page.WaitForFunctionAsync(System.String,System.Object[])">
            <summary>
            Waits for a function to be evaluated to a truthy value
            </summary>
            <param name="script">Function to be evaluated in browser context</param>
            <param name="args">Arguments to pass to <c>script</c></param>
            <returns>A task that resolves when the <c>script</c> returns a truthy value</returns>
        </member>
        <member name="M:PuppeteerSharp.Page.WaitForExpressionAsync(System.String,PuppeteerSharp.WaitForFunctionOptions)">
            <summary>
            Waits for an expression to be evaluated to a truthy value
            </summary>
            <param name="script">Expression to be evaluated in browser context</param>
            <param name="options">Optional waiting parameters</param>
            <returns>A task that resolves when the <c>script</c> returns a truthy value</returns>
            <seealso cref="M:PuppeteerSharp.Frame.WaitForExpressionAsync(System.String,PuppeteerSharp.WaitForFunctionOptions)"/>
        </member>
        <member name="M:PuppeteerSharp.Page.WaitForSelectorAsync(System.String,PuppeteerSharp.WaitForSelectorOptions)">
            <summary>
            Waits for a selector to be added to the DOM
            </summary>
            <param name="selector">A selector of an element to wait for</param>
            <param name="options">Optional waiting parameters</param>
            <returns>A task that resolves when element specified by selector string is added to DOM.
            Resolves to `null` if waiting for `hidden: true` and selector is not found in DOM.</returns>
            <seealso cref="M:PuppeteerSharp.Page.WaitForXPathAsync(System.String,PuppeteerSharp.WaitForSelectorOptions)"/>
            <seealso cref="M:PuppeteerSharp.Frame.WaitForSelectorAsync(System.String,PuppeteerSharp.WaitForSelectorOptions)"/>
        </member>
        <member name="M:PuppeteerSharp.Page.WaitForXPathAsync(System.String,PuppeteerSharp.WaitForSelectorOptions)">
            <summary>
            Waits for a xpath selector to be added to the DOM
            </summary>
            <param name="xpath">A xpath selector of an element to wait for</param>
            <param name="options">Optional waiting parameters</param>
            <returns>A task which resolves when element specified by xpath string is added to DOM. 
            Resolves to `null` if waiting for `hidden: true` and xpath is not found in DOM.</returns>
            <example>
            <code>
            <![CDATA[
            var browser = await Puppeteer.LaunchAsync(new LaunchOptions());
            var page = await browser.NewPageAsync();
            string currentURL = null;
            page
                .WaitForXPathAsync("//img")
                .ContinueWith(_ => Console.WriteLine("First URL with image: " + currentURL));
            foreach (var current in new[] { "https://example.com", "https://google.com", "https://bbc.com" })
            {
                currentURL = current;
                await page.GoToAsync(currentURL);
            }
            await browser.CloseAsync();
            ]]>
            </code>
            </example>
            <seealso cref="M:PuppeteerSharp.Page.WaitForSelectorAsync(System.String,PuppeteerSharp.WaitForSelectorOptions)"/>
            <seealso cref="M:PuppeteerSharp.Frame.WaitForXPathAsync(System.String,PuppeteerSharp.WaitForSelectorOptions)"/>
        </member>
        <member name="M:PuppeteerSharp.Page.WaitForNavigationAsync(PuppeteerSharp.NavigationOptions)">
            <summary>
            This resolves when the page navigates to a new URL or reloads.
            It is useful for when you run code which will indirectly cause the page to navigate.
            </summary>
            <param name="options">navigation options</param>
            <returns>Task which resolves to the main resource response. 
            In case of multiple redirects, the navigation will resolve with the response of the last redirect.
            In case of navigation to a different anchor or navigation due to History API usage, the navigation will resolve with `null`.
            </returns>
            <remarks>
            Usage of the <c>History API</c> <see href="https://developer.mozilla.org/en-US/docs/Web/API/History_API"/> to change the URL is considered a navigation
            </remarks>
            <example>
            <code>
            <![CDATA[
            var navigationTask = page.WaitForNavigationAsync();
            await page.ClickAsync("a.my-link");
            await navigationTask;
            ]]>
            </code>
            </example>
        </member>
        <member name="M:PuppeteerSharp.Page.WaitForRequestAsync(System.String,PuppeteerSharp.WaitForOptions)">
            <summary>
            Waits for a request.
            </summary>
            <example>
            <code>
            <![CDATA[
            var firstRequest = await page.WaitForRequestAsync("http://example.com/resource");
            return firstRequest.Url;
            ]]>
            </code>
            </example>
            <returns>A task which resolves when a matching request was made.</returns>
            <param name="url">URL to wait for.</param>
            <param name="options">Options.</param>
        </member>
        <member name="M:PuppeteerSharp.Page.WaitForRequestAsync(System.Func{PuppeteerSharp.Request,System.Boolean},PuppeteerSharp.WaitForOptions)">
            <summary>
            Waits for a request.
            </summary>
            <example>
            <code>
            <![CDATA[
            var request = await page.WaitForRequestAsync(request => request.Url === "http://example.com" && request.Method === HttpMethod.Get;
            return request.Url;
            ]]>
            </code>
            </example>
            <returns>A task which resolves when a matching request was made.</returns>
            <param name="predicate">Function which looks for a matching request.</param>
            <param name="options">Options.</param>
        </member>
        <member name="M:PuppeteerSharp.Page.WaitForResponseAsync(System.String,PuppeteerSharp.WaitForOptions)">
            <summary>
            Waits for a response.
            </summary>
            <example>
            <code>
            <![CDATA[
            var firstResponse = await page.WaitForResponseAsync("http://example.com/resource");
            return firstResponse.Url;
            ]]>
            </code>
            </example>
            <returns>A task which resolves when a matching response is received.</returns>
            <param name="url">URL to wait for.</param>
            <param name="options">Options.</param>
        </member>
        <member name="M:PuppeteerSharp.Page.WaitForResponseAsync(System.Func{PuppeteerSharp.Response,System.Boolean},PuppeteerSharp.WaitForOptions)">
            <summary>
            Waits for a response.
            </summary>
            <example>
            <code>
            <![CDATA[
            var response = await page.WaitForResponseAsync(response => response.Url === "http://example.com" && response.Status === HttpStatus.Ok;
            return response.Url;
            ]]>
            </code>
            </example>
            <returns>A task which resolves when a matching response is received.</returns>
            <param name="predicate">Function which looks for a matching response.</param>
            <param name="options">Options.</param>
        </member>
        <member name="M:PuppeteerSharp.Page.GoBackAsync(PuppeteerSharp.NavigationOptions)">
            <summary>
            Navigate to the previous page in history.
            </summary>
            <returns>Task which which resolves to the main resource response. In case of multiple redirects, 
            the navigation will resolve with the response of the last redirect. If can not go back, resolves to null.</returns>
            <param name="options">Navigation parameters.</param>
        </member>
        <member name="M:PuppeteerSharp.Page.GoForwardAsync(PuppeteerSharp.NavigationOptions)">
            <summary>
            Navigate to the next page in history.
            </summary>
            <returns>Task which which resolves to the main resource response. In case of multiple redirects, 
            the navigation will resolve with the response of the last redirect. If can not go forward, resolves to null.</returns>
            <param name="options">Navigation parameters.</param>
        </member>
        <member name="M:PuppeteerSharp.Page.SetBurstModeOffAsync">
            <summary>
            Resets the background color and Viewport after taking Screenshots using BurstMode.
            </summary>
            <returns>The burst mode off.</returns>
        </member>
        <member name="M:PuppeteerSharp.Page.Dispose">
            <summary>
            Releases all resource used by the <see cref="T:PuppeteerSharp.Page"/> object by calling the <see cref="M:PuppeteerSharp.Page.CloseAsync(PuppeteerSharp.PageCloseOptions)"/> method.
            </summary>
            <remarks>Call <see cref="M:PuppeteerSharp.Page.Dispose"/> when you are finished using the <see cref="T:PuppeteerSharp.Page"/>. The
            <see cref="M:PuppeteerSharp.Page.Dispose"/> method leaves the <see cref="T:PuppeteerSharp.Page"/> in an unusable state. After
            calling <see cref="M:PuppeteerSharp.Page.Dispose"/>, you must release all references to the <see cref="T:PuppeteerSharp.Page"/> so
            the garbage collector can reclaim the memory that the <see cref="T:PuppeteerSharp.Page"/> was occupying.</remarks>
        </member>
        <member name="T:PuppeteerSharp.PageAccessibility.Accessibility">
            <summary>
            The Accessibility class provides methods for inspecting Chromium's accessibility tree. 
            The accessibility tree is used by assistive technology such as screen readers or switches.
            
            Accessibility is a very platform-specific thing. On different platforms, there are different screen readers that might have wildly different output.
            Blink - Chrome's rendering engine - has a concept of "accessibility tree", which is than translated into different platform-specific APIs. 
            Accessibility namespace gives users access to the Blink Accessibility Tree.
            Most of the accessibility tree gets filtered out when converting from Blink AX Tree to Platform-specific AX-Tree or by assistive technologies themselves. 
            By default, Puppeteer tries to approximate this filtering, exposing only the "interesting" nodes of the tree.
            </summary>
        </member>
        <member name="M:PuppeteerSharp.PageAccessibility.Accessibility.#ctor(PuppeteerSharp.CDPSession)">
            <summary>
            Initializes a new instance of the <see cref="T:PuppeteerSharp.PageAccessibility.Accessibility"/> class.
            </summary>
            <param name="client">Client.</param>
        </member>
        <member name="M:PuppeteerSharp.PageAccessibility.Accessibility.SnapshotAsync(PuppeteerSharp.PageAccessibility.AccessibilitySnapshotOptions)">
            <summary>
            Snapshots the async.
            </summary>
            <returns>The async.</returns>
            <param name="options">Options.</param>
        </member>
        <member name="T:PuppeteerSharp.PageAccessibility.AccessibilitySnapshotOptions">
            <summary>
            <see cref="M:PuppeteerSharp.PageAccessibility.Accessibility.SnapshotAsync(PuppeteerSharp.PageAccessibility.AccessibilitySnapshotOptions)"/>
            </summary>
            <seealso cref="P:PuppeteerSharp.Page.Accessibility"/>
        </member>
        <member name="P:PuppeteerSharp.PageAccessibility.AccessibilitySnapshotOptions.InterestingOnly">
            <summary>
            Prune uninteresting nodes from the tree. Defaults to true.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.PageAccessibility.AccessibilitySnapshotOptions.Root">
            <summary>
            The root DOM element for the snapshot. Defaults to the whole page.
            </summary>
        </member>
        <member name="T:PuppeteerSharp.PageAccessibility.CheckedState">
            <summary>
            Three-state boolean. See <seealso cref="P:PuppeteerSharp.PageAccessibility.SerializedAXNode.Checked"/> and <seealso cref="P:PuppeteerSharp.PageAccessibility.SerializedAXNode.Pressed"/>
            </summary>
        </member>
        <member name="F:PuppeteerSharp.PageAccessibility.CheckedState.False">
            <summary>
            Flse.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.PageAccessibility.CheckedState.True">
            <summary>
            True.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.PageAccessibility.CheckedState.Mixed">
            <summary>
            Mixed.
            </summary>
        </member>
        <member name="T:PuppeteerSharp.PageAccessibility.SerializedAXNode">
            <summary>
            AXNode.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.PageAccessibility.SerializedAXNode.Role">
            <summary>
            The <see fref="https://www.w3.org/TR/wai-aria/#usage_intro">role</see>.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.PageAccessibility.SerializedAXNode.Name">
            <summary>
            A human readable name for the node.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.PageAccessibility.SerializedAXNode.Value">
            <summary>
            The current value of the node.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.PageAccessibility.SerializedAXNode.Description">
            <summary>
            An additional human readable description of the node.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.PageAccessibility.SerializedAXNode.KeyShortcuts">
            <summary>
            Keyboard shortcuts associated with this node.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.PageAccessibility.SerializedAXNode.RoleDescription">
            <summary>
            A human readable alternative to the role.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.PageAccessibility.SerializedAXNode.ValueText">
            <summary>
            A description of the current value.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.PageAccessibility.SerializedAXNode.Disabled">
            <summary>
            Whether the node is disabled.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.PageAccessibility.SerializedAXNode.Expanded">
            <summary>
            Whether the node is expanded or collapsed.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.PageAccessibility.SerializedAXNode.Focused">
            <summary>
            Whether the node is focused.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.PageAccessibility.SerializedAXNode.Modal">
            <summary>
            Whether the node is <see href="https://en.wikipedia.org/wiki/Modal_window">modal</see>.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.PageAccessibility.SerializedAXNode.Multiline">
            <summary>
            Whether the node text input supports multiline.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.PageAccessibility.SerializedAXNode.Multiselectable">
            <summary>
            Whether more than one child can be selected.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.PageAccessibility.SerializedAXNode.Readonly">
            <summary>
            Whether the node is read only.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.PageAccessibility.SerializedAXNode.Required">
            <summary>
            Whether the node is required.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.PageAccessibility.SerializedAXNode.Selected">
            <summary>
            Whether the node is selected in its parent node.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.PageAccessibility.SerializedAXNode.Checked">
            <summary>
            Whether the checkbox is checked, or "mixed".
            </summary>
        </member>
        <member name="P:PuppeteerSharp.PageAccessibility.SerializedAXNode.Pressed">
            <summary>
            Whether the toggle button is checked, or "mixed".
            </summary>
        </member>
        <member name="P:PuppeteerSharp.PageAccessibility.SerializedAXNode.Level">
            <summary>
            The level of a heading.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.PageAccessibility.SerializedAXNode.ValueMin">
            <summary>
            The minimum value in a node.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.PageAccessibility.SerializedAXNode.ValueMax">
            <summary>
            The maximum value in a node.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.PageAccessibility.SerializedAXNode.AutoComplete">
            <summary>
            What kind of autocomplete is supported by a control.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.PageAccessibility.SerializedAXNode.HasPopup">
            <summary>
            What kind of popup is currently being shown for a node.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.PageAccessibility.SerializedAXNode.Invalid">
            <summary>
            Whether and in what way this node's value is invalid.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.PageAccessibility.SerializedAXNode.Orientation">
            <summary>
            Whether the node is oriented horizontally or vertically.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.PageAccessibility.SerializedAXNode.Children">
            <summary>
            Child nodes of this node, if any.
            </summary>
        </member>
        <member name="M:PuppeteerSharp.PageAccessibility.SerializedAXNode.Equals(PuppeteerSharp.PageAccessibility.SerializedAXNode)">
            <inheritdoc/>
        </member>
        <member name="M:PuppeteerSharp.PageAccessibility.SerializedAXNode.Equals(System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:PuppeteerSharp.PageAccessibility.SerializedAXNode.GetHashCode">
            <inheritdoc/>
        </member>
        <member name="T:PuppeteerSharp.PageCloseOptions">
            <summary>
            Page close options.
            </summary>
            <seealso cref="M:PuppeteerSharp.Page.CloseAsync(PuppeteerSharp.PageCloseOptions)"/>
        </member>
        <member name="P:PuppeteerSharp.PageCloseOptions.RunBeforeUnload">
            <summary>
            Defaults to <c>false</c>. Whether to run the beforeunload page handlers.
            </summary>
            <see href="https://developer.mozilla.org/en-US/docs/Web/Events/beforeunload"/>
        </member>
        <member name="T:PuppeteerSharp.PageCoverage.Coverage">
            <summary>
            gathers information about parts of JavaScript and CSS that were used by the page.
            </summary>
        </member>
        <member name="M:PuppeteerSharp.PageCoverage.Coverage.StartJSCoverageAsync(PuppeteerSharp.PageCoverage.CoverageStartOptions)">
            <summary>
            Starts JS coverage
            </summary>
            <param name="options">Set of configurable options for coverage</param>
            <returns>A task that resolves when coverage is started</returns>
        </member>
        <member name="M:PuppeteerSharp.PageCoverage.Coverage.StopJSCoverageAsync">
            <summary>
            Stops JS coverage and returns coverage reports for all scripts
            </summary>
            <returns>Task that resolves to the array of coverage reports for all stylesheets</returns>
            <remarks>
            JavaScript Coverage doesn't include anonymous scripts by default; however, scripts with sourceURLs are reported.
            </remarks>
        </member>
        <member name="M:PuppeteerSharp.PageCoverage.Coverage.StartCSSCoverageAsync(PuppeteerSharp.PageCoverage.CoverageStartOptions)">
            <summary>
            Starts CSS coverage
            </summary>
            <param name="options">Set of configurable options for coverage</param>
            <returns>A task that resolves when coverage is started</returns>
        </member>
        <member name="M:PuppeteerSharp.PageCoverage.Coverage.StopCSSCoverageAsync">
            <summary>
            Stops JS coverage and returns coverage reports for all non-anonymous scripts
            </summary>
            <returns>Task that resolves to the array of coverage reports for all stylesheets</returns>
            <remarks>
            JavaScript Coverage doesn't include anonymous scripts; however, scripts with sourceURLs are reported.
            </remarks>
        </member>
        <member name="T:PuppeteerSharp.PageCoverage.CoverageEntry">
            <summary>
            Coverage report for all non-anonymous scripts.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.PageCoverage.CoverageEntry.Url">
            <summary>
            Script URL
            </summary>
            <value>Script URL.</value>
        </member>
        <member name="P:PuppeteerSharp.PageCoverage.CoverageEntry.Ranges">
            <summary>
            Script ranges that were executed. Ranges are sorted and non-overlapping.
            </summary>
            <value>Ranges.</value>
        </member>
        <member name="P:PuppeteerSharp.PageCoverage.CoverageEntry.Text">
            <summary>
            Script content
            </summary>
            <value>Script content.</value>
        </member>
        <member name="T:PuppeteerSharp.PageCoverage.CoverageEntryRange">
            <summary>
            Script range.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.PageCoverage.CoverageEntryRange.Start">
            <summary>
            A start offset in text, inclusive.
            </summary>
            <value>Start offset.</value>
        </member>
        <member name="P:PuppeteerSharp.PageCoverage.CoverageEntryRange.End">
            <summary>
            An end offset in text, exclusive.
            </summary>
            <value>End offset.</value>
        </member>
        <member name="M:PuppeteerSharp.PageCoverage.CoverageEntryRange.Equals(System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:PuppeteerSharp.PageCoverage.CoverageEntryRange.GetHashCode">
            <inheritdoc/>
        </member>
        <member name="T:PuppeteerSharp.PageCoverage.CoverageStartOptions">
            <summary>
            Set of configurable options for coverage
            </summary>
        </member>
        <member name="P:PuppeteerSharp.PageCoverage.CoverageStartOptions.ResetOnNavigation">
            <summary>
            Whether to reset coverage on every navigation. Defaults to <c>true</c>.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.PageCoverage.CoverageStartOptions.ReportAnonymousScripts">
            <summary>
            Whether anonymous scripts generated by the page should be reported
            </summary>
            <remarks>
            Anonymous scripts are ones that don't have an associated url. 
            These are scripts that are dynamically created on the page using `eval` or `new Function`. 
            If <see cref="P:PuppeteerSharp.PageCoverage.CoverageStartOptions.ReportAnonymousScripts"/> is set to `true`, anonymous scripts will have `__puppeteer_evaluation_script__` as their URL.
            </remarks>
        </member>
        <member name="T:PuppeteerSharp.PageErrorEventArgs">
            <summary>
            Page error event arguments.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.PageErrorEventArgs.Message">
            <summary>
            Error Message
            </summary>
            <value>The message.</value>
        </member>
        <member name="M:PuppeteerSharp.PageErrorEventArgs.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:PuppeteerSharp.PageErrorEventArgs"/> class.
            </summary>
            <param name="message">Message.</param>
        </member>
        <member name="T:PuppeteerSharp.Payload">
            <summary>
            Payload information.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Payload.Method">
            <summary>
            Gets or sets the HTTP method.
            </summary>
            <value>HTTP method.</value>
        </member>
        <member name="P:PuppeteerSharp.Payload.PostData">
            <summary>
            Gets or sets the post data.
            </summary>
            <value>The post data.</value>
        </member>
        <member name="P:PuppeteerSharp.Payload.Headers">
            <summary>
            Gets or sets the HTTP headers.
            </summary>
            <value>HTTP headers.</value>
        </member>
        <member name="P:PuppeteerSharp.Payload.Url">
            <summary>
            Gets or sets the URL.
            </summary>
            <value>The URL.</value>
        </member>
        <member name="T:PuppeteerSharp.PdfOptions">
            <summary>
            Options to be used in <see cref="M:PuppeteerSharp.Page.PdfAsync(System.String,PuppeteerSharp.PdfOptions)"/>, <see cref="M:PuppeteerSharp.Page.PdfStreamAsync(PuppeteerSharp.PdfOptions)"/> and <see cref="M:PuppeteerSharp.Page.PdfDataAsync(PuppeteerSharp.PdfOptions)"/>
            </summary>
        </member>
        <member name="M:PuppeteerSharp.PdfOptions.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:PuppeteerSharp.PdfOptions"/> class.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.PdfOptions.Scale">
            <summary>
            Scale of the webpage rendering. Defaults to <c>1</c>. Scale amount must be between 0.1 and 2.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.PdfOptions.DisplayHeaderFooter">
            <summary>
            Display header and footer. Defaults to <c>false</c>
            </summary>
        </member>
        <member name="P:PuppeteerSharp.PdfOptions.HeaderTemplate">
            <summary>
            HTML template for the print header. Should be valid HTML markup with following classes used to inject printing values into them:
              <c>date</c> - formatted print date
              <c>title</c> - document title
              <c>url</c> - document location
              <c>pageNumber</c> - current page number
              <c>totalPages</c> - total pages in the document
            </summary>
        </member>
        <member name="P:PuppeteerSharp.PdfOptions.FooterTemplate">
            <summary>
            HTML template for the print footer. Should be valid HTML markup with following classes used to inject printing values into them:
              <c>date</c> - formatted print date
              <c>title</c> - document title
              <c>url</c> - document location
              <c>pageNumber</c> - current page number
              <c>totalPages</c> - total pages in the document
            </summary>
        </member>
        <member name="P:PuppeteerSharp.PdfOptions.PrintBackground">
            <summary>
            Print background graphics. Defaults to <c>false</c>
            </summary>
        </member>
        <member name="P:PuppeteerSharp.PdfOptions.Landscape">
            <summary>
            Paper orientation.. Defaults to <c>false</c>
            </summary>
        </member>
        <member name="P:PuppeteerSharp.PdfOptions.PageRanges">
            <summary>
            Paper ranges to print, e.g., <c>1-5, 8, 11-13</c>. Defaults to the empty string, which means print all pages
            </summary>
        </member>
        <member name="P:PuppeteerSharp.PdfOptions.Format">
            <summary>
            Paper format. If set, takes priority over <see cref="P:PuppeteerSharp.PdfOptions.Width"/> and <see cref="P:PuppeteerSharp.PdfOptions.Height"/>
            </summary>
        </member>
        <member name="P:PuppeteerSharp.PdfOptions.Width">
            <summary>
            Paper width, accepts values labeled with units
            </summary>
        </member>
        <member name="P:PuppeteerSharp.PdfOptions.Height">
            <summary>
            Paper height, accepts values labeled with units
            </summary>
        </member>
        <member name="P:PuppeteerSharp.PdfOptions.MarginOptions">
            <summary>
            Paper margins, defaults to none
            </summary>
        </member>
        <member name="P:PuppeteerSharp.PdfOptions.PreferCSSPageSize">
            <summary>
            Give any CSS <c>@page</c> size declared in the page priority over what is declared in <c>width</c> and <c>height</c> or <c>format</c> options. 
            Defaults to <c>false</c>, which will scale the content to fit the paper size.
            </summary>
        </member>
        <member name="M:PuppeteerSharp.PdfOptions.Equals(System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:PuppeteerSharp.PdfOptions.Equals(PuppeteerSharp.PdfOptions)">
            <inheritdoc/>
        </member>
        <member name="M:PuppeteerSharp.PdfOptions.GetHashCode">
            <inheritdoc/>
        </member>
        <member name="M:PuppeteerSharp.PdfOptions.op_Equality(PuppeteerSharp.PdfOptions,PuppeteerSharp.PdfOptions)">
            <inheritdoc/>
        </member>
        <member name="M:PuppeteerSharp.PdfOptions.op_Inequality(PuppeteerSharp.PdfOptions,PuppeteerSharp.PdfOptions)">
            <inheritdoc/>
        </member>
        <member name="T:PuppeteerSharp.Platform">
            <summary>
            Platform used by a <see cref="T:PuppeteerSharp.BrowserFetcher"/>.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Platform.Unknown">
            <summary>
            Unknown.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Platform.MacOS">
            <summary>
            MacOS.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Platform.Linux">
            <summary>
            Linux.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Platform.Win32">
            <summary>
            Win32.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Platform.Win64">
            <summary>
            Win64.
            </summary>
        </member>
        <member name="T:PuppeteerSharp.PopupEventArgs">
            <summary>
            Popup event arguments. <see cref="E:PuppeteerSharp.Page.Popup"/>
            </summary>
        </member>
        <member name="P:PuppeteerSharp.PopupEventArgs.PopupPage">
            <summary>
            Gets the popup page.
            </summary>
        </member>
        <member name="T:PuppeteerSharp.Puppeteer">
            <summary>
            Provides a method to launch a Chromium instance.
            </summary>
            <example>
            The following is a typical example of using a Puppeteer to drive automation:
            <code>
            var browser = await Puppeteer.LaunchAsync(new LaunchOptions());
            var page = await browser.NewPageAsync();
            await page.GoToAsync("https://www.google.com");
            await Browser.CloseAsync();
            </code>
            </example>
        </member>
        <member name="P:PuppeteerSharp.Puppeteer.DefaultArgs">
            <summary>
            The default flags that Chromium will be launched with.
            </summary>
        </member>
        <member name="M:PuppeteerSharp.Puppeteer.GetExecutablePath">
            <summary>
            A path where Puppeteer expects to find bundled Chromium. Chromium might not exist there if the downloader was not used.
            </summary>
            <returns>The path to chrome.exe</returns>
        </member>
        <member name="M:PuppeteerSharp.Puppeteer.GetDefaultArgs(PuppeteerSharp.LaunchOptions)">
            <summary>
            Returns an array of argument based on the options provided and the platform where the library is running 
            </summary>
            <returns>Chromium arguments.</returns>
            <param name="options">Options.</param>
        </member>
        <member name="M:PuppeteerSharp.Puppeteer.LaunchAsync(PuppeteerSharp.LaunchOptions,Microsoft.Extensions.Logging.ILoggerFactory)">
            <summary>
            The method launches a browser instance with given arguments. The browser will be closed when the Browser is disposed.
            </summary>
            <param name="options">Options for launching Chrome</param>
            <param name="loggerFactory">The logger factory</param>
            <returns>A connected browser.</returns>
            <remarks>
            See <a href="https://www.howtogeek.com/202825/what%E2%80%99s-the-difference-between-chromium-and-chrome/">this article</a>
            for a description of the differences between Chromium and Chrome.
            <a href="https://chromium.googlesource.com/chromium/src/+/lkcr/docs/chromium_browser_vs_google_chrome.md">This article</a> describes some differences for Linux users.
            
            Environment Variables
            Puppeteer looks for certain <see href="https://en.wikipedia.org/wiki/Environment_variable">environment variables</see>() to aid its operations.
            - <c>PUPPETEER_CHROMIUM_REVISION</c> - specify a certain version of Chromium you'd like Puppeteer to use. See <see cref="M:PuppeteerSharp.Puppeteer.LaunchAsync(PuppeteerSharp.LaunchOptions,Microsoft.Extensions.Logging.ILoggerFactory)"/> on how executable path is inferred. 
              **BEWARE**: Puppeteer is only <see href="https://github.com/GoogleChrome/puppeteer/#q-why-doesnt-puppeteer-vxxx-work-with-chromium-vyyy">guaranteed to work</see> with the bundled Chromium, use at your own risk.
            - <c>PUPPETEER_EXECUTABLE_PATH</c> - specify an executable path to be used in <see cref="M:PuppeteerSharp.Puppeteer.LaunchAsync(PuppeteerSharp.LaunchOptions,Microsoft.Extensions.Logging.ILoggerFactory)"/>. 
              **BEWARE**: Puppeteer is only <see href="https://github.com/GoogleChrome/puppeteer/#q-why-doesnt-puppeteer-vxxx-work-with-chromium-vyyy">guaranteed to work</see> with the bundled Chromium, use at your own risk.
            </remarks>
        </member>
        <member name="M:PuppeteerSharp.Puppeteer.ConnectAsync(PuppeteerSharp.ConnectOptions,Microsoft.Extensions.Logging.ILoggerFactory)">
            <summary>
            Attaches Puppeteer to an existing Chromium instance. The browser will be closed when the Browser is disposed.
            </summary>
            <param name="options">Options for connecting.</param>
            <param name="loggerFactory">The logger factory</param>
            <returns>A connected browser.</returns>
        </member>
        <member name="M:PuppeteerSharp.Puppeteer.CreateBrowserFetcher(PuppeteerSharp.BrowserFetcherOptions)">
            <summary>
            Creates the browser fetcher.
            </summary>
            <returns>The browser fetcher.</returns>
            <param name="options">Options.</param>
        </member>
        <member name="P:PuppeteerSharp.Puppeteer.Devices">
             <summary>
             Returns a list of devices to be used with <seealso cref="M:PuppeteerSharp.Page.EmulateAsync(PuppeteerSharp.Mobile.DeviceDescriptor)"/>. 
             </summary>
             <example>
             <code>
            <![CDATA[
             var iPhone = Puppeteer.Devices[DeviceDescriptorName.IPhone6];
             using(var page = await browser.NewPageAsync())
             {
                 await page.EmulateAsync(iPhone);
                 await page.goto('https://www.google.com');
             }
             ]]>
             </code>
             </example>
        </member>
        <member name="T:PuppeteerSharp.PuppeteerException">
            <summary>
            Base exception used to identify any exception thrown by PuppeteerSharp
            </summary>
        </member>
        <member name="M:PuppeteerSharp.PuppeteerException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:PuppeteerSharp.PuppeteerException"/> class.
            </summary>
        </member>
        <member name="M:PuppeteerSharp.PuppeteerException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:PuppeteerSharp.PuppeteerException"/> class.
            </summary>
            <param name="message">Message.</param>
        </member>
        <member name="M:PuppeteerSharp.PuppeteerException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:PuppeteerSharp.PuppeteerException"/> class.
            </summary>
            <param name="message">Message.</param>
            <param name="innerException">Inner exception.</param>
        </member>
        <member name="M:PuppeteerSharp.PuppeteerException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:PuppeteerSharp.PuppeteerException"/> class.
            </summary>
            <param name="info">Info.</param>
            <param name="context">Context.</param>
        </member>
        <member name="T:PuppeteerSharp.RemoteAddress">
            <summary>
            Remote server address.
            </summary>
            <seealso cref="P:PuppeteerSharp.Response.RemoteAddress"/>
        </member>
        <member name="P:PuppeteerSharp.RemoteAddress.IP">
            <summary>
            The IP address of the remote server.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.RemoteAddress.Port">
            <summary>
            The port used to connect to the remote server.
            </summary>
        </member>
        <member name="T:PuppeteerSharp.Request">
            <summary>
            Whenever the page sends a request, the following events are emitted by puppeteer's page:
            <see cref="E:PuppeteerSharp.Page.Request"/> emitted when the request is issued by the page.
            <see cref="E:PuppeteerSharp.Page.Response"/> emitted when/if the response is received for the request.
            <see cref="E:PuppeteerSharp.Page.RequestFinished"/> emitted when the response body is downloaded and the request is complete.
            
            If request fails at some point, then instead of <see cref="E:PuppeteerSharp.Page.RequestFinished"/> event (and possibly instead of <see cref="E:PuppeteerSharp.Page.Response"/> event), the <see cref="E:PuppeteerSharp.Page.RequestFailed"/> event is emitted.
            
            If request gets a 'redirect' response, the request is successfully finished with the <see cref="E:PuppeteerSharp.Page.RequestFinished"/> event, and a new request is issued to a redirected url.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Request.Response">
            <summary>
            Responsed attached to the request.
            </summary>
            <value>The response.</value>
        </member>
        <member name="P:PuppeteerSharp.Request.Failure">
            <summary>
            Gets or sets the failure.
            </summary>
            <value>The failure.</value>
        </member>
        <member name="P:PuppeteerSharp.Request.RequestId">
            <summary>
            Gets or sets the request identifier.
            </summary>
            <value>The request identifier.</value>
        </member>
        <member name="P:PuppeteerSharp.Request.InterceptionId">
            <summary>
            Gets or sets the interception identifier.
            </summary>
            <value>The interception identifier.</value>
        </member>
        <member name="P:PuppeteerSharp.Request.ResourceType">
            <summary>
            Gets or sets the type of the resource.
            </summary>
            <value>The type of the resource.</value>
        </member>
        <member name="P:PuppeteerSharp.Request.Frame">
            <summary>
            Gets the frame.
            </summary>
            <value>The frame.</value>
        </member>
        <member name="P:PuppeteerSharp.Request.IsNavigationRequest">
            <summary>
            Gets whether this request is driving frame's navigation
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Request.Method">
            <summary>
            Gets or sets the HTTP method.
            </summary>
            <value>HTTP method.</value>
        </member>
        <member name="P:PuppeteerSharp.Request.PostData">
            <summary>
            Gets or sets the post data.
            </summary>
            <value>The post data.</value>
        </member>
        <member name="P:PuppeteerSharp.Request.Headers">
            <summary>
            Gets or sets the HTTP headers.
            </summary>
            <value>HTTP headers.</value>
        </member>
        <member name="P:PuppeteerSharp.Request.Url">
            <summary>
            Gets or sets the URL.
            </summary>
            <value>The URL.</value>
        </member>
        <member name="P:PuppeteerSharp.Request.RedirectChain">
            <summary>
            A redirectChain is a chain of requests initiated to fetch a resource.
            If there are no redirects and the request was successful, the chain will be empty.
            If a server responds with at least a single redirect, then the chain will contain all the requests that were redirected.
            redirectChain is shared between all the requests of the same chain.
            </summary>
            <example>
            For example, if the website http://example.com has a single redirect to https://example.com, then the chain will contain one request:
            <code>
            var response = await page.GoToAsync("http://example.com");
            var chain = response.Request.RedirectChain;
            Console.WriteLine(chain.Length); // 1
            Console.WriteLine(chain[0].Url); // 'http://example.com'
            </code>
            If the website https://google.com has no redirects, then the chain will be empty:
            <code>
            var response = await page.GoToAsync("https://google.com");
            var chain = response.Request.RedirectChain;
            Console.WriteLine(chain.Length); // 0
            </code>
            </example>
            <value>The redirect chain.</value>
        </member>
        <member name="M:PuppeteerSharp.Request.ContinueAsync(PuppeteerSharp.Payload)">
            <summary>
            Continues request with optional request overrides. To use this, request interception should be enabled with <see cref="M:PuppeteerSharp.Page.SetRequestInterceptionAsync(System.Boolean)"/>. Exception is immediately thrown if the request interception is not enabled.
            If the URL is set it won't perform a redirect. The request will be silently forwarded to the new url. For example, the address bar will show the original url.
            </summary>
            <param name="overrides">Optional request overwrites.</param>
            <returns>Task</returns>
        </member>
        <member name="M:PuppeteerSharp.Request.RespondAsync(PuppeteerSharp.ResponseData)">
            <summary>
            Fulfills request with given response. To use this, request interception should be enabled with <see cref="M:PuppeteerSharp.Page.SetRequestInterceptionAsync(System.Boolean)"/>. Exception is thrown if request interception is not enabled.
            </summary>
            <param name="response">Response that will fulfill this request</param>
            <returns>Task</returns>
        </member>
        <member name="M:PuppeteerSharp.Request.AbortAsync(PuppeteerSharp.RequestAbortErrorCode)">
            <summary>
            Aborts request. To use this, request interception should be enabled with <see cref="M:PuppeteerSharp.Page.SetRequestInterceptionAsync(System.Boolean)"/>.
            Exception is immediately thrown if the request interception is not enabled.
            </summary>
            <param name="errorCode">Optional error code. Defaults to <see cref="F:PuppeteerSharp.RequestAbortErrorCode.Failed"/></param>
            <returns>Task</returns>
        </member>
        <member name="T:PuppeteerSharp.RequestAbortErrorCode">
            <summary>
            Abort error codes. used by <see cref="M:PuppeteerSharp.Request.AbortAsync(PuppeteerSharp.RequestAbortErrorCode)"/>
            </summary>
        </member>
        <member name="F:PuppeteerSharp.RequestAbortErrorCode.Aborted">
            <summary>
            An operation was aborted (due to user action)
            </summary>
        </member>
        <member name="F:PuppeteerSharp.RequestAbortErrorCode.AccessDenied">
            <summary>
            Permission to access a resource, other than the network, was denied
            </summary>
        </member>
        <member name="F:PuppeteerSharp.RequestAbortErrorCode.AddressUnreachable">
            <summary>
            The IP address is unreachable. This usually means that there is no route to the specified host or network.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.RequestAbortErrorCode.BlockedByClient">
            <summary>
            The client chose to block the request.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.RequestAbortErrorCode.BlockedByResponse">
            <summary>
            The request failed because the response was delivered along with requirements which are not met 
            ('X-Frame-Options' and 'Content-Security-Policy' ancestor checks, for instance).
            </summary>
        </member>
        <member name="F:PuppeteerSharp.RequestAbortErrorCode.ConnectionAborted">
            <summary>
            A connection timed out as a result of not receiving an ACK for data sent.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.RequestAbortErrorCode.ConnectionClosed">
            <summary>
            A connection was closed (corresponding to a TCP FIN).
            </summary>
        </member>
        <member name="F:PuppeteerSharp.RequestAbortErrorCode.ConnectionFailed">
            <summary>
            A connection attempt failed.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.RequestAbortErrorCode.ConnectionRefused">
            <summary>
            A connection attempt was refused.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.RequestAbortErrorCode.ConnectionReset">
            <summary>
             A connection was reset (corresponding to a TCP RST).
            </summary>
        </member>
        <member name="F:PuppeteerSharp.RequestAbortErrorCode.InternetDisconnected">
            <summary>
            The Internet connection has been lost.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.RequestAbortErrorCode.NameNotResolved">
            <summary>
            The host name could not be resolved.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.RequestAbortErrorCode.TimedOut">
            <summary>
            An operation timed out.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.RequestAbortErrorCode.Failed">
            <summary>
             A generic failure occurred.
            </summary>
        </member>
        <member name="T:PuppeteerSharp.RequestEventArgs">
            <summary>
            Arguments used by <see cref="T:PuppeteerSharp.Page"/> events.
            </summary>
            <seealso cref="E:PuppeteerSharp.Page.Request"/>
            <seealso cref="E:PuppeteerSharp.Page.RequestFailed"/>
            <seealso cref="E:PuppeteerSharp.Page.RequestFinished"/>
        </member>
        <member name="P:PuppeteerSharp.RequestEventArgs.Request">
            <summary>
            Gets or sets the request.
            </summary>
            <value>The request.</value>
        </member>
        <member name="T:PuppeteerSharp.ResourceType">
            <summary>
            Resource type.
            </summary>
            <seealso cref="P:PuppeteerSharp.Request.ResourceType"/>
        </member>
        <member name="F:PuppeteerSharp.ResourceType.Unknown">
            <summary>
            Unknown.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.ResourceType.Document">
            <summary>
            Document.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.ResourceType.StyleSheet">
            <summary>
            Stylesheet.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.ResourceType.Image">
            <summary>
            Image.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.ResourceType.Media">
            <summary>
            Media.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.ResourceType.Font">
            <summary>
            Font.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.ResourceType.Script">
            <summary>
            Script.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.ResourceType.TextTrack">
            <summary>
            Texttrack.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.ResourceType.Xhr">
            <summary>
            XHR.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.ResourceType.Fetch">
            <summary>
            Fetch.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.ResourceType.EventSource">
            <summary>
            Event source.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.ResourceType.WebSocket">
            <summary>
            Web Socket.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.ResourceType.Manifest">
            <summary>
            Manifest.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.ResourceType.Ping">
            <summary>
            Ping.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.ResourceType.Other">
            <summary>
            Other.
            </summary>
        </member>
        <member name="T:PuppeteerSharp.Response">
            <summary>
            <see cref="T:PuppeteerSharp.Response"/> class represents responses which are received by page.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Response.Url">
            <summary>
            Contains the URL of the response.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Response.Headers">
            <summary>
            An object with HTTP headers associated with the response. All header names are lower-case.
            </summary>
            <value>The headers.</value>
        </member>
        <member name="P:PuppeteerSharp.Response.Status">
            <summary>
            Contains the status code of the response
            </summary>
            <value>The status.</value>
        </member>
        <member name="P:PuppeteerSharp.Response.Ok">
            <summary>
            Contains a boolean stating whether the response was successful (status in the range 200-299) or not.
            </summary>
            <value><c>true</c> if ok; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:PuppeteerSharp.Response.Request">
            <summary>
            A matching <see cref="P:PuppeteerSharp.Response.Request"/> object.
            </summary>
            <value>The request.</value>
        </member>
        <member name="P:PuppeteerSharp.Response.FromCache">
            <summary>
            True if the response was served from either the browser's disk cache or memory cache.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Response.SecurityDetails">
            <summary>
            Gets or sets the security details.
            </summary>
            <value>The security details.</value>
        </member>
        <member name="P:PuppeteerSharp.Response.FromServiceWorker">
            <summary>
            Gets a value indicating whether the <see cref="T:PuppeteerSharp.Response"/> was served by a service worker.
            </summary>
            <value><c>true</c> if the <see cref="T:PuppeteerSharp.Response"/> was served by a service worker; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:PuppeteerSharp.Response.StatusText">
            <summary>
            Contains the status text of the response (e.g. usually an "OK" for a success).
            </summary>
            <value>The status text.</value>
        </member>
        <member name="P:PuppeteerSharp.Response.RemoteAddress">
            <summary>
            Remove server address.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Response.Frame">
            <summary>
            A <see cref="P:PuppeteerSharp.Response.Frame"/> that initiated this request. Or null if navigating to error pages.
            </summary>
        </member>
        <member name="M:PuppeteerSharp.Response.BufferAsync">
            <summary>
            Returns a Task which resolves to a buffer with response body
            </summary>
            <returns>A Task which resolves to a buffer with response body</returns>
        </member>
        <member name="M:PuppeteerSharp.Response.TextAsync">
            <summary>
            Returns a Task which resolves to a text representation of response body
            </summary>
            <returns>A Task which resolves to a text representation of response body</returns>
        </member>
        <member name="M:PuppeteerSharp.Response.JsonAsync">
            <summary>
            Returns a Task which resolves to a <see cref="T:Newtonsoft.Json.Linq.JObject"/> representation of response body
            </summary>
            <seealso cref="M:PuppeteerSharp.Response.JsonAsync``1"/>
            <returns>A Task which resolves to a <see cref="T:Newtonsoft.Json.Linq.JObject"/> representation of response body</returns>
        </member>
        <member name="M:PuppeteerSharp.Response.JsonAsync``1">
            <summary>
            Returns a Task which resolves to a <typeparamref name="T"/> representation of response body
            </summary>
            <typeparam name="T">The type of the response</typeparam>
            <seealso cref="M:PuppeteerSharp.Response.JsonAsync"/>
            <returns>A Task which resolves to a <typeparamref name="T"/> representation of response body</returns>
        </member>
        <member name="T:PuppeteerSharp.ResponseCreatedEventArgs">
            <summary>
            <see cref="E:PuppeteerSharp.Page.Response"/> arguments.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.ResponseCreatedEventArgs.Response">
            <summary>
            Gets or sets the response.
            </summary>
            <value>The response.</value>
        </member>
        <member name="T:PuppeteerSharp.ResponseData">
            <summary>
            Response that will fulfill a request.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.ResponseData.Body">
            <summary>
            Response body (text content)
            </summary>
            <value>Body as text.</value>
        </member>
        <member name="P:PuppeteerSharp.ResponseData.BodyData">
            <summary>
            Response body (binary content)
            </summary>
            <value>The body as binary.</value>
        </member>
        <member name="P:PuppeteerSharp.ResponseData.Headers">
            <summary>
            Response headers. Header values will be converted to a string.
            </summary>
            <value>Headers.</value>
        </member>
        <member name="P:PuppeteerSharp.ResponseData.ContentType">
            <summary>
            If set, equals to setting <see href="https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Content-Type"/> response header
            </summary>
            <value>The Content-Type.</value>
        </member>
        <member name="P:PuppeteerSharp.ResponseData.Status">
            <summary>
            Response status code.
            </summary>
            <value>Status Code.</value>
        </member>
        <member name="T:PuppeteerSharp.RevisionInfo">
            <summary>
            Revision info.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.RevisionInfo.Revision">
            <summary>
            Gets or sets the revision.
            </summary>
            <value>The revision.</value>
        </member>
        <member name="P:PuppeteerSharp.RevisionInfo.FolderPath">
            <summary>
            Gets or sets the folder path.
            </summary>
            <value>The folder path.</value>
        </member>
        <member name="P:PuppeteerSharp.RevisionInfo.ExecutablePath">
            <summary>
            Gets or sets the executable path.
            </summary>
            <value>The executable path.</value>
        </member>
        <member name="P:PuppeteerSharp.RevisionInfo.Downloaded">
            <summary>
            Gets a value indicating whether this <see cref="T:PuppeteerSharp.RevisionInfo"/> is downloaded.
            </summary>
            <value><c>true</c> if <see cref="P:PuppeteerSharp.RevisionInfo.FolderPath"/> exists; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:PuppeteerSharp.RevisionInfo.Url">
            <summary>
            URL this revision can be downloaded from.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.RevisionInfo.Local">
            <summary>
            Whether the revision is locally available on disk.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.RevisionInfo.Platform">
            <summary>
            Revision platform.
            </summary>
        </member>
        <member name="T:PuppeteerSharp.SameSite">
            <summary>
            SameSite values in cookies
            </summary>
        </member>
        <member name="F:PuppeteerSharp.SameSite.None">
            <summary>
            None
            </summary>
        </member>
        <member name="F:PuppeteerSharp.SameSite.Strict">
            <summary>
            Strict
            </summary>
        </member>
        <member name="F:PuppeteerSharp.SameSite.Lax">
            <summary>
            Lax
            </summary>
        </member>
        <member name="F:PuppeteerSharp.SameSite.Extended">
            <summary>
            Extended
            </summary>
        </member>
        <member name="T:PuppeteerSharp.ScreenshotOptions">
            <summary>
            Options to be used in <see cref="M:PuppeteerSharp.Page.ScreenshotAsync(System.String,PuppeteerSharp.ScreenshotOptions)"/>, <see cref="M:PuppeteerSharp.Page.ScreenshotStreamAsync(PuppeteerSharp.ScreenshotOptions)"/> and <see cref="M:PuppeteerSharp.Page.ScreenshotDataAsync(PuppeteerSharp.ScreenshotOptions)"/>
            </summary>
        </member>
        <member name="P:PuppeteerSharp.ScreenshotOptions.Clip">
            <summary>
            Specifies clipping region of the page.
            </summary>
            <value>The clip.</value>
        </member>
        <member name="P:PuppeteerSharp.ScreenshotOptions.FullPage">
            <summary>
            When <c>true</c>, takes a screenshot of the full scrollable page. Defaults to <c>false</c>.
            </summary>
            <value><c>true</c> if full page; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:PuppeteerSharp.ScreenshotOptions.OmitBackground">
            <summary>
            Hides default white background and allows capturing screenshots with transparency. Defaults to <c>false</c>
            </summary>
            <value><c>true</c> if omit background; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:PuppeteerSharp.ScreenshotOptions.Type">
            <summary>
            Specify screenshot type, can be either jpeg or png. Defaults to 'png'.
            </summary>
            <value>The type.</value>
        </member>
        <member name="P:PuppeteerSharp.ScreenshotOptions.Quality">
            <summary>
            The quality of the image, between 0-100. Not applicable to png images.
            </summary>
            <value>The quality.</value>
        </member>
        <member name="P:PuppeteerSharp.ScreenshotOptions.BurstMode">
            <summary>
            When BurstMode is <c>true</c> the screenshot process will only execute all the screenshot setup actions (background and metrics overrides)
            before the first screenshot call and it will ignore the reset actions after the screenshoot is taken.
            <see cref="M:PuppeteerSharp.Page.SetBurstModeOffAsync"/> needs to be called after the last screenshot is taken.
            </summary>
            <example><![CDATA[
            var screenShotOptions = new ScreenshotOptions
            {
                FullPage = true,
                BurstMode = true
            };
            await page.GoToAsync("https://www.google.com");
            for(var x = 0; x < 100; x++)
            {
                await page.ScreenshotBase64Async(screenShotOptions);
            }
            await page.SetBurstModeOffAsync();
            ]]></example>
        </member>
        <member name="T:PuppeteerSharp.ScreenshotType">
            <summary>
            Screenshot file type.
            </summary>
            <seealso cref="T:PuppeteerSharp.ScreenshotOptions"/>
        </member>
        <member name="F:PuppeteerSharp.ScreenshotType.Jpeg">
            <summary>
            JPEG type.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.ScreenshotType.Png">
            <summary>
            PNG type.
            </summary>
        </member>
        <member name="T:PuppeteerSharp.SecurityDetails">
            <summary>
            Represents the security details when response was received over the secure connection.
            </summary>
            <seealso cref="P:PuppeteerSharp.Response.SecurityDetails"/>
        </member>
        <member name="M:PuppeteerSharp.SecurityDetails.#ctor">
            <summary>
             Initializes a new instance of the <see cref="T:PuppeteerSharp.SecurityDetails"/> class.
            </summary>
        </member>
        <member name="M:PuppeteerSharp.SecurityDetails.#ctor(System.String,System.String,System.Int32,System.Int32,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:PuppeteerSharp.SecurityDetails"/> class.
            </summary>
            <param name="subjectName">Subject name</param>
            <param name="issuer">Issuer</param>
            <param name="validFrom">Valid from</param>
            <param name="validTo">Valid to</param>
            <param name="protocol">Protocol</param>
        </member>
        <member name="P:PuppeteerSharp.SecurityDetails.SubjectName">
            <summary>
            Gets the name of the subject.
            </summary>
            <value>The name of the subject.</value>
        </member>
        <member name="P:PuppeteerSharp.SecurityDetails.Issuer">
            <summary>
            Gets the issuer.
            </summary>
            <value>The issuer.</value>
        </member>
        <member name="P:PuppeteerSharp.SecurityDetails.ValidFrom">
            <summary>
            Gets the valid from.
            </summary>
            <value>The valid from.</value>
        </member>
        <member name="P:PuppeteerSharp.SecurityDetails.ValidTo">
            <summary>
            Gets the valid to.
            </summary>
            <value>The valid to.</value>
        </member>
        <member name="P:PuppeteerSharp.SecurityDetails.Protocol">
            <summary>
            Gets the protocol.
            </summary>
            <value>The protocol.</value>
        </member>
        <member name="T:PuppeteerSharp.SelectorException">
            <summary>
            Exception thrown when an element selector returns null.
            </summary>
            <seealso cref="M:PuppeteerSharp.Extensions.EvaluateFunctionAsync``1(System.Threading.Tasks.Task{PuppeteerSharp.ElementHandle},System.String,System.Object[])"/>
            <seealso cref="M:PuppeteerSharp.Frame.SelectAsync(System.String,System.String[])"/>
            <seealso cref="M:PuppeteerSharp.Page.ClickAsync(System.String,PuppeteerSharp.Input.ClickOptions)"/>
            <seealso cref="M:PuppeteerSharp.Page.TapAsync(System.String)"/>
            <seealso cref="M:PuppeteerSharp.Page.HoverAsync(System.String)"/>
            <seealso cref="M:PuppeteerSharp.Page.FocusAsync(System.String)"/>
            <seealso cref="M:PuppeteerSharp.Page.SelectAsync(System.String,System.String[])"/>
        </member>
        <member name="P:PuppeteerSharp.SelectorException.Selector">
            <summary>
            Gets the selector.
            </summary>
            <value>The selector.</value>
        </member>
        <member name="M:PuppeteerSharp.SelectorException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:PuppeteerSharp.SelectorException"/> class.
            </summary>
            <param name="message">Message.</param>
        </member>
        <member name="M:PuppeteerSharp.SelectorException.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:PuppeteerSharp.SelectorException"/> class.
            </summary>
            <param name="message">Message.</param>
            <param name="selector">Selector.</param>
        </member>
        <member name="T:PuppeteerSharp.Target">
            <summary>
            Target.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Target.Url">
            <summary>
            Gets the URL.
            </summary>
            <value>The URL.</value>
        </member>
        <member name="P:PuppeteerSharp.Target.Type">
            <summary>
            Gets the type. It will be <see cref="P:PuppeteerSharp.TargetInfo.Type"/>.
            Can be `"page"`, `"background_page"`, `"service_worker"`, `"shared_worker"`, `"browser"` or `"other"`.
            </summary>
            <value>The type.</value>
        </member>
        <member name="P:PuppeteerSharp.Target.TargetId">
            <summary>
            Gets the target identifier.
            </summary>
            <value>The target identifier.</value>
        </member>
        <member name="P:PuppeteerSharp.Target.Opener">
            <summary>
            Get the target that opened this target
            </summary>
            <remarks>
            Top-level targets return <c>null</c>.
            </remarks>
        </member>
        <member name="P:PuppeteerSharp.Target.Browser">
            <summary>
            Get the browser the target belongs to.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Target.BrowserContext">
            <summary>
            Get the browser context the target belongs to.
            </summary>
        </member>
        <member name="M:PuppeteerSharp.Target.PageAsync">
            <summary>
            Creates a new <see cref="T:PuppeteerSharp.Page"/>. If the target is not <c>"page"</c> or <c>"background_page"</c> returns <c>null</c>
            </summary>
            <returns>a task that returns a new <see cref="T:PuppeteerSharp.Page"/></returns>
        </member>
        <member name="M:PuppeteerSharp.Target.WorkerAsync">
            <summary>
            If the target is not of type `"service_worker"` or `"shared_worker"`, returns `null`.
            </summary>
            <returns>A task that returns a <see cref="T:PuppeteerSharp.Worker"/></returns>
        </member>
        <member name="M:PuppeteerSharp.Target.CreateCDPSessionAsync">
            <summary>
            Creates a Chrome Devtools Protocol session attached to the target.
            </summary>
            <returns>A task that returns a <see cref="T:PuppeteerSharp.CDPSession"/></returns>
        </member>
        <member name="T:PuppeteerSharp.TargetChangedArgs">
            <summary>
             Event arguments used by target related events.
            </summary>
            <seealso cref="E:PuppeteerSharp.Browser.TargetChanged"/>
            <seealso cref="E:PuppeteerSharp.Browser.TargetCreated"/>
            <seealso cref="E:PuppeteerSharp.Browser.TargetDestroyed"/>
        </member>
        <member name="P:PuppeteerSharp.TargetChangedArgs.TargetInfo">
            <summary>
            Gets or sets the target info.
            </summary>
            <value>The target info.</value>
        </member>
        <member name="P:PuppeteerSharp.TargetChangedArgs.Target">
            <summary>
            Gets or sets the target.
            </summary>
            <value>The target.</value>
        </member>
        <member name="T:PuppeteerSharp.TargetClosedException">
            <summary>
            Exception thrown by the <see cref="T:PuppeteerSharp.Connection"/> when it detects that the target was closed.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.TargetClosedException.CloseReason">
            <summary>
            Close Reason.
            </summary>
            <value>The close reason.</value>
        </member>
        <member name="M:PuppeteerSharp.TargetClosedException.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:PuppeteerSharp.TargetClosedException"/> class.
            </summary>
            <param name="message">Message.</param>
            <param name="closeReason">Close reason.</param>
        </member>
        <member name="T:PuppeteerSharp.TargetInfo">
            <summary>
            Target info.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.TargetInfo.Type">
            <summary>
            Gets the type.
            </summary>
            <value>The type.</value>
        </member>
        <member name="P:PuppeteerSharp.TargetInfo.Url">
            <summary>
            Gets the URL.
            </summary>
            <value>The URL.</value>
        </member>
        <member name="P:PuppeteerSharp.TargetInfo.TargetId">
            <summary>
            Gets the target identifier.
            </summary>
            <value>The target identifier.</value>
        </member>
        <member name="P:PuppeteerSharp.TargetInfo.BrowserContextId">
            <summary>
            Gets or sets the target browser contextId
            </summary>
        </member>
        <member name="P:PuppeteerSharp.TargetInfo.OpenerId">
            <summary>
            Get the target that opened this target
            </summary>
        </member>
        <member name="T:PuppeteerSharp.TargetType">
            <summary>
            Target type.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.TargetType.Other">
            <summary>
            The other.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.TargetType.Page">
            <summary>
            Target type page.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.TargetType.ServiceWorker">
            <summary>
            Target type service worker.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.TargetType.Browser">
            <summary>
            Target type browser.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.TargetType.BackgroundPage">
            <summary>
            Target type background page.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.TargetType.Worker">
            <summary>
            Target type worker.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.TargetType.Javascript">
            <summary>
            Target type javascript.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.TargetType.Network">
            <summary>
            Target type network
            </summary>
        </member>
        <member name="F:PuppeteerSharp.TargetType.Deprecation">
            <summary>
            Target type network
            </summary>
        </member>
        <member name="F:PuppeteerSharp.TargetType.Security">
            <summary>
            Target type security
            </summary>
        </member>
        <member name="F:PuppeteerSharp.TargetType.Recommendation">
            <summary>
            Target type recommendation
            </summary>
        </member>
        <member name="F:PuppeteerSharp.TargetType.SharedWorker">
            <summary>
            Target type shared worker.
            </summary>
        </member>
        <member name="T:PuppeteerSharp.Tracing">
            <summary>
            You can use <see cref="M:PuppeteerSharp.Tracing.StartAsync(PuppeteerSharp.TracingOptions)"/> and <see cref="M:PuppeteerSharp.Tracing.StopAsync"/> to create a trace file which can be opened in Chrome DevTools or timeline viewer.
            </summary>
            <example>
            <code>
            await Page.Tracing.StartAsync(new TracingOptions
            {
                Screenshots = true,
                Path = _file
            });
            await Page.GoToAsync(TestConstants.ServerUrl + "/grid.html");
            await Page.Tracing.StopAsync();
            </code>
            </example>
        </member>
        <member name="M:PuppeteerSharp.Tracing.StartAsync(PuppeteerSharp.TracingOptions)">
            <summary>
            Starts tracing.
            </summary>
            <returns>Start task</returns>
            <param name="options">Tracing options</param>
        </member>
        <member name="M:PuppeteerSharp.Tracing.StopAsync">
            <summary>
            Stops tracing
            </summary>
            <returns>Stop task</returns>
        </member>
        <member name="T:PuppeteerSharp.TracingOptions">
            <summary>
            Tracing options used on <see cref="M:PuppeteerSharp.Tracing.StartAsync(PuppeteerSharp.TracingOptions)"/>.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.TracingOptions.Screenshots">
            <summary>
            Gets or sets a value indicating whether Tracing should captures screenshots in the trace
            </summary>
            <value>Screenshots option</value>
        </member>
        <member name="P:PuppeteerSharp.TracingOptions.Path">
            <summary>
            A path to write the trace file to
            </summary>
            <value>The path.</value>
        </member>
        <member name="P:PuppeteerSharp.TracingOptions.Categories">
            <summary>
            Specify custom categories to use instead of default.
            </summary>
            <value>The categories.</value>
        </member>
        <member name="T:PuppeteerSharp.Transport.IConnectionTransport">
            <summary>
            Connection transport abstraction.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Transport.IConnectionTransport.IsClosed">
            <summary>
            Gets a value indicating whether this <see cref="T:PuppeteerSharp.Transport.IConnectionTransport"/> is closed.
            </summary>
        </member>
        <member name="M:PuppeteerSharp.Transport.IConnectionTransport.StopReading">
            <summary>
            Stops reading incoming data.
            </summary>
        </member>
        <member name="M:PuppeteerSharp.Transport.IConnectionTransport.SendAsync(System.String)">
            <summary>
            Sends a message using the transport.
            </summary>
            <returns>The task.</returns>
            <param name="message">Message to send.</param>
        </member>
        <member name="E:PuppeteerSharp.Transport.IConnectionTransport.Closed">
            <summary>
            Occurs when the transport is closed.
            </summary>
        </member>
        <member name="E:PuppeteerSharp.Transport.IConnectionTransport.MessageReceived">
            <summary>
            Occurs when a message is received.
            </summary>
        </member>
        <member name="T:PuppeteerSharp.Transport.MessageReceivedEventArgs">
            <summary>
            Message received event arguments.
            <see cref="E:PuppeteerSharp.Transport.IConnectionTransport.MessageReceived"/>
            </summary>
        </member>
        <member name="M:PuppeteerSharp.Transport.MessageReceivedEventArgs.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:PuppeteerSharp.Transport.MessageReceivedEventArgs"/> class.
            </summary>
            <param name="message">Message.</param>
        </member>
        <member name="P:PuppeteerSharp.Transport.MessageReceivedEventArgs.Message">
            <summary>
            Transport message.
            </summary>
        </member>
        <member name="T:PuppeteerSharp.Transport.TransportClosedEventArgs">
            <summary>
            <see cref="E:PuppeteerSharp.Transport.IConnectionTransport.Closed"/>
            </summary>
        </member>
        <member name="P:PuppeteerSharp.Transport.TransportClosedEventArgs.CloseReason">
            <summary>
            Gets or sets the close reason.
            </summary>
        </member>
        <member name="M:PuppeteerSharp.Transport.TransportClosedEventArgs.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:PuppeteerSharp.Transport.TransportClosedEventArgs"/> class.
            </summary>
            <param name="closeReason">Close reason.</param>
        </member>
        <member name="T:PuppeteerSharp.Transport.TransportFactory">
            <summary>
            Delegate for creation of <see cref="T:PuppeteerSharp.Transport.IConnectionTransport"/> instances.
            </summary>
            <param name="url">Chromium URL</param>
            <param name="options">Connection options</param>
            <param name="cancellationToken">A cancellation token</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task`1"/> instance for the asynchronous socket create and connect operation.</returns>
        </member>
        <member name="T:PuppeteerSharp.Transport.TransportTaskScheduler">
            <summary>
            Delegate for scheduling of long-running transport tasks
            </summary>
            <param name="taskFactory">Delegate that creates the task to be scheduled.</param>
            <param name="cancellationToken">Cancellation token for the task to be scheduled.</param>
        </member>
        <member name="T:PuppeteerSharp.Transport.WebSocketFactory">
            <summary>
            Delegate for creation of <see cref="T:System.Net.WebSockets.WebSocket"/> instances.
            </summary>
            <param name="url">Chromium URL</param>
            <param name="options">Connection options</param>
            <param name="cancellationToken">A cancellation token</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task`1"/> instance for the asynchronous socket create and connect operation.</returns>
        </member>
        <member name="T:PuppeteerSharp.Transport.WebSocketTransport">
            <summary>
            Default web socket transport.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Transport.WebSocketTransport.DefaultTransportFactory">
            <summary>
            Gets the default <see cref="T:PuppeteerSharp.Transport.WebSocketFactory"/>. This factory does not support Windows 7.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Transport.WebSocketTransport.DefaultWebSocketFactory">
            <summary>
            Gets the default <see cref="T:PuppeteerSharp.Transport.TransportFactory"/>
            </summary>
        </member>
        <member name="F:PuppeteerSharp.Transport.WebSocketTransport.DefaultTransportScheduler">
            <summary>
            Gets the default <see cref="T:PuppeteerSharp.Transport.TransportTaskScheduler"/>
            </summary>
        </member>
        <member name="M:PuppeteerSharp.Transport.WebSocketTransport.#ctor(System.Net.WebSockets.WebSocket,PuppeteerSharp.Transport.TransportTaskScheduler,System.Boolean)">
            <summary>
            Initialize the Transport
            </summary>
            <param name="client">The web socket</param>
            <param name="scheduler">The scheduler to use for long-running tasks.</param>
            <param name="queueRequests">Indicates whether requests should be queued.</param>
        </member>
        <member name="P:PuppeteerSharp.Transport.WebSocketTransport.IsClosed">
            <summary>
            Gets a value indicating whether this <see cref="T:PuppeteerSharp.Transport.IConnectionTransport"/> is closed.
            </summary>
        </member>
        <member name="E:PuppeteerSharp.Transport.WebSocketTransport.Closed">
            <summary>
            Occurs when the transport is closed.
            </summary>
        </member>
        <member name="E:PuppeteerSharp.Transport.WebSocketTransport.MessageReceived">
            <summary>
            Occurs when a message is received.
            </summary>
        </member>
        <member name="M:PuppeteerSharp.Transport.WebSocketTransport.SendAsync(System.String)">
            <summary>
            Sends a message using the transport.
            </summary>
            <returns>The task.</returns>
            <param name="message">Message to send.</param>
        </member>
        <member name="M:PuppeteerSharp.Transport.WebSocketTransport.StopReading">
            <summary>
            Stops reading incoming data.
            </summary>
        </member>
        <member name="M:PuppeteerSharp.Transport.WebSocketTransport.Dispose">
            <inheritdoc/>
        </member>
        <member name="M:PuppeteerSharp.Transport.WebSocketTransport.GetResponseAsync(System.Threading.CancellationToken)">
            <summary>
            Starts listening the socket
            </summary>
            <returns>The start.</returns>
        </member>
        <member name="T:PuppeteerSharp.ViewPortOptions">
            <summary>
            View port options used on <see cref="M:PuppeteerSharp.Page.SetViewportAsync(PuppeteerSharp.ViewPortOptions)"/>.
            </summary>
        </member>
        <member name="M:PuppeteerSharp.ViewPortOptions.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:PuppeteerSharp.ViewPortOptions"/> class.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.ViewPortOptions.Width">
            <summary>
            Gets or sets the width
            </summary>
            <value>The page width width in pixels.</value>
        </member>
        <member name="P:PuppeteerSharp.ViewPortOptions.Height">
            <summary>
            Gets or sets the height.
            </summary>
            <value>The page height in pixels.</value>
        </member>
        <member name="P:PuppeteerSharp.ViewPortOptions.IsMobile">
            <summary>
            Gets or sets whether the meta viewport tag is taken into account.
            </summary>
            <value>Whether the meta viewport tag is taken into account. Defaults to <c>false</c>.</value>
        </member>
        <member name="P:PuppeteerSharp.ViewPortOptions.DeviceScaleFactor">
            <summary>
            Gets or sets the device scale factor.
            </summary>
            <value>Specify device scale factor (can be thought of as dpr). Defaults to 1.</value>
        </member>
        <member name="P:PuppeteerSharp.ViewPortOptions.IsLandscape">
             <summary>
             Gets or sets if viewport is in landscape mode.
             </summary>
            <value>Specifies if viewport is in landscape mode. Defaults to <c>false</c>.</value>
        </member>
        <member name="P:PuppeteerSharp.ViewPortOptions.HasTouch">
            <summary>
            Gets or sets if viewport supports touch events.
            </summary>
            <value>Specifies if viewport supports touch events. Defaults to <c>false</c>.</value>
        </member>
        <member name="P:PuppeteerSharp.ViewPortOptions.Default">
            <summary>
            Default Viewport
            </summary>
        </member>
        <member name="T:PuppeteerSharp.WaitForFunctionOptions">
            <summary>
            Optional waiting parameters.
            </summary>
            <seealso cref="M:PuppeteerSharp.Page.WaitForFunctionAsync(System.String,PuppeteerSharp.WaitForFunctionOptions,System.Object[])"/>
            <seealso cref="M:PuppeteerSharp.Frame.WaitForFunctionAsync(System.String,PuppeteerSharp.WaitForFunctionOptions,System.Object[])"/>
            <seealso cref="T:PuppeteerSharp.WaitForSelectorOptions"/>
        </member>
        <member name="P:PuppeteerSharp.WaitForFunctionOptions.Timeout">
            <summary>
            Maximum time to wait for in milliseconds. Defaults to 30000 (30 seconds). Pass 0 to disable timeout.
            The default value can be changed by setting the <see cref="P:PuppeteerSharp.Page.DefaultTimeout"/> property.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.WaitForFunctionOptions.Polling">
            <summary>
            An interval at which the <c>pageFunction</c> is executed. defaults to <see cref="F:PuppeteerSharp.WaitForFunctionPollingOption.Raf"/>
            </summary>
        </member>
        <member name="P:PuppeteerSharp.WaitForFunctionOptions.PollingInterval">
            <summary>
            An interval at which the <c>pageFunction</c> is executed. If no value is specified will use <see cref="P:PuppeteerSharp.WaitForFunctionOptions.Polling"/>
            </summary>
        </member>
        <member name="T:PuppeteerSharp.WaitForFunctionPollingOption">
            <summary>
            An interval at which the <c>pageFunction</c> is executed.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.WaitForFunctionPollingOption.Raf">
            <summary>
            To constantly execute <c>pageFunction</c> in <c>requestAnimationFrame</c> callback.
            This is the tightest polling mode which is suitable to observe styling changes.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.WaitForFunctionPollingOption.Mutation">
            <summary>
            To execute <c>pageFunction</c> on every DOM mutation.
            </summary>
        </member>
        <member name="T:PuppeteerSharp.WaitForOptions">
            <summary>
            Optional waiting parameters.
            </summary>
            <seealso cref="M:PuppeteerSharp.Page.WaitForRequestAsync(System.Func{PuppeteerSharp.Request,System.Boolean},PuppeteerSharp.WaitForOptions)"/>
            <seealso cref="M:PuppeteerSharp.Page.WaitForRequestAsync(System.String,PuppeteerSharp.WaitForOptions)"/>
            <seealso cref="M:PuppeteerSharp.Page.WaitForResponseAsync(System.String,PuppeteerSharp.WaitForOptions)"/>
            <seealso cref="M:PuppeteerSharp.Page.WaitForResponseAsync(System.Func{PuppeteerSharp.Response,System.Boolean},PuppeteerSharp.WaitForOptions)"/>
        </member>
        <member name="P:PuppeteerSharp.WaitForOptions.Timeout">
            <summary>
            Maximum time to wait for in milliseconds. Defaults to 30000 (30 seconds). Pass 0 to disable timeout.
            The default value can be changed by setting the <see cref="P:PuppeteerSharp.Page.DefaultTimeout"/> property.
            </summary>
        </member>
        <member name="T:PuppeteerSharp.WaitForSelectorOptions">
            <summary>
            Optional waiting parameters.
            </summary>
            <seealso cref="M:PuppeteerSharp.Page.WaitForSelectorAsync(System.String,PuppeteerSharp.WaitForSelectorOptions)"/>
            <seealso cref="M:PuppeteerSharp.Frame.WaitForSelectorAsync(System.String,PuppeteerSharp.WaitForSelectorOptions)"/>
        </member>
        <member name="P:PuppeteerSharp.WaitForSelectorOptions.Timeout">
            <summary>
            Maximum time to wait for in milliseconds. Defaults to 30000 (30 seconds). Pass 0 to disable timeout.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.WaitForSelectorOptions.Visible">
            <summary>
            Wait for selector to become visible.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.WaitForSelectorOptions.Hidden">
            <summary>
            Wait for selector to become hidden.
            </summary>
        </member>
        <member name="T:PuppeteerSharp.WaitTaskTimeoutException">
            <summary>
            Timeout exception that might be thrown by <c>WaitFor</c> methods in <see cref="T:PuppeteerSharp.Frame"/>.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.WaitTaskTimeoutException.Timeout">
            <summary>
            Timeout that caused the exception
            </summary>
            <value>The timeout.</value>
        </member>
        <member name="P:PuppeteerSharp.WaitTaskTimeoutException.ElementType">
            <summary>
            Element type the WaitTask was waiting for
            </summary>
            <value>The element.</value>
        </member>
        <member name="M:PuppeteerSharp.WaitTaskTimeoutException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:PuppeteerSharp.WaitTaskTimeoutException"/> class.
            </summary>
        </member>
        <member name="M:PuppeteerSharp.WaitTaskTimeoutException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:PuppeteerSharp.WaitTaskTimeoutException"/> class.
            </summary>
            <param name="message">Message.</param>
        </member>
        <member name="M:PuppeteerSharp.WaitTaskTimeoutException.#ctor(System.Int32,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:PuppeteerSharp.WaitTaskTimeoutException"/> class.
            </summary>
            <param name="timeout">Timeout.</param>
            <param name="elementType">Element type.</param>
        </member>
        <member name="M:PuppeteerSharp.WaitTaskTimeoutException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:PuppeteerSharp.WaitTaskTimeoutException"/> class.
            </summary>
            <param name="message">Message.</param>
            <param name="innerException">Inner exception.</param>
        </member>
        <member name="M:PuppeteerSharp.WaitTaskTimeoutException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:PuppeteerSharp.WaitTaskTimeoutException"/> class.
            </summary>
            <param name="info">Info.</param>
            <param name="context">Context.</param>
        </member>
        <member name="T:PuppeteerSharp.WaitUntilNavigation">
            <summary>
            Wait until navigation.
            </summary>
        </member>
        <member name="F:PuppeteerSharp.WaitUntilNavigation.Load">
            <summary>
            Consider navigation to be finished when the <c>load</c> event is fired
            </summary>
        </member>
        <member name="F:PuppeteerSharp.WaitUntilNavigation.DOMContentLoaded">
            <summary>
            Consider navigation to be finished when the <c>DOMContentLoaded</c> event is fired
            </summary>
        </member>
        <member name="F:PuppeteerSharp.WaitUntilNavigation.Networkidle0">
            <summary>
            Consider navigation to be finished when there are no more than 0 network connections for at least <c>500</c> ms
            </summary>
        </member>
        <member name="F:PuppeteerSharp.WaitUntilNavigation.Networkidle2">
            <summary>
            Consider navigation to be finished when there are no more than 2 network connections for at least <c>500</c> ms
            </summary>
        </member>
        <member name="T:PuppeteerSharp.Worker">
            <summary>
            The Worker class represents a WebWorker (<see href="https://developer.mozilla.org/en-US/docs/Web/API/Web_Workers_API"/>).
            The events <see cref="E:PuppeteerSharp.Page.WorkerCreated"/> and <see cref="E:PuppeteerSharp.Page.WorkerDestroyed"/> are emitted on the page object to signal the worker lifecycle.
            </summary>
            <example>
            <code>
            <![CDATA[
            page.WorkerCreated += (sender, e) => Console.WriteLine('Worker created: ' + e.Worker.Url);
            page.WorkerDestroyed += (sender, e) => Console.WriteLine('Worker destroyed: ' + e.Worker.Url);
            for (var worker of page.Workers)
            {
                Console.WriteLine('  ' + worker.Url);
            }
            ]]>
            </code>
            </example>
        </member>
        <member name="P:PuppeteerSharp.Worker.Url">
            <summary>
            Gets the Worker URL.
            </summary>
            <value>Worker URL.</value>
        </member>
        <member name="M:PuppeteerSharp.Worker.EvaluateExpressionAsync``1(System.String)">
            <summary>
            Executes a script in browser context
            </summary>
            <typeparam name="T">The type to deserialize the result to</typeparam>
            <param name="script">Script to be evaluated in browser context</param>
            <remarks>
            If the script, returns a Promise, then the method would wait for the promise to resolve and return its value.
            </remarks>
            <seealso cref="M:PuppeteerSharp.ExecutionContext.EvaluateExpressionAsync(System.String)"/>
            <returns>Task which resolves to script return value</returns>
        </member>
        <member name="M:PuppeteerSharp.Worker.EvaluateFunctionAsync(System.String,System.Object[])">
            <summary>
            Executes a function in browser context
            </summary>
            <param name="script">Script to be evaluated in browser context</param>
            <param name="args">Arguments to pass to script</param>
            <remarks>
            If the script, returns a Promise, then the method would wait for the promise to resolve and return its value.
            <see cref="T:PuppeteerSharp.JSHandle"/> instances can be passed as arguments
            </remarks>
            <returns>Task which resolves to script return value</returns>
        </member>
        <member name="M:PuppeteerSharp.Worker.EvaluateFunctionAsync``1(System.String,System.Object[])">
            <summary>
            Executes a function in the context
            </summary>
            <typeparam name="T">The type to deserialize the result to</typeparam>
            <param name="script">Script to be evaluated in browser context</param>
            <param name="args">Arguments to pass to script</param>
            <remarks>
            If the script, returns a Promise, then the method would wait for the promise to resolve and return its value.
            <see cref="T:PuppeteerSharp.JSHandle"/> instances can be passed as arguments
            </remarks>
            <returns>Task which resolves to script return value</returns>
        </member>
        <member name="M:PuppeteerSharp.Worker.EvaluateExpressionHandleAsync(System.String)">
            <summary>
            Executes a script in browser context
            </summary>
            <param name="script">Script to be evaluated in browser context</param>
            <remarks>
            If the script, returns a Promise, then the method would wait for the promise to resolve and return its value.
            </remarks>
            <returns>Task which resolves to script return value</returns>
            <seealso cref="M:PuppeteerSharp.ExecutionContext.EvaluateExpressionHandleAsync(System.String)"/>
        </member>
        <member name="T:PuppeteerSharp.WorkerEventArgs">
            <summary>
            Workder created event arguments.
            </summary>
        </member>
        <member name="P:PuppeteerSharp.WorkerEventArgs.Worker">
            <summary>
            Worker
            </summary>
            <value>The worker.</value>
        </member>
        <member name="M:PuppeteerSharp.WorkerEventArgs.#ctor(PuppeteerSharp.Worker)">
            <summary>
            Initializes a new instance of the <see cref="T:PuppeteerSharp.WorkerEventArgs"/> class.
            </summary>
            <param name="worker">Worker.</param>
        </member>
    </members>
</doc>
