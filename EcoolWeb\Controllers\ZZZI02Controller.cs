﻿using com.ecool.service;
using ECOOL_APP.com.ecool.Models.DTO;
using ECOOL_APP.com.ecool.Models.entity;
using EcoolWeb.CustomAttribute;
using EcoolWeb.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;


namespace EcoolWeb.Controllers
{
    [SessionExpire]
    public class ZZZI02Controller : Controller
    {
        private string Bre_NO = "ZZZI02";
        private string ErrorMsg = string.Empty;
        ZZT10ViewModelService Db = new ZZT10ViewModelService();
        ECOOL_APP.UserProfile user;

        // GET: ZZZI02
        [CheckPermission] //檢查權限
        public ActionResult Index(string ROLE_ID)
        {
           user = UserProfileHelper.Get();
           ViewBag.BRE_NO = Bre_NO;
           ViewBag.Panel_Title = "角色權限維護";

            ROLE_ID = ROLE_ID == null ? user.RoleID_Default : ROLE_ID;
            

            var model = Db.USP_ZZT10ViewModel_QUERY(ROLE_ID, user);

            ViewBag.HRMT24ListItems = HRMT24Service.USP_HRMT24_QUERY("", "","", "2",null, user);


            ViewBag.ROLE_ID = ROLE_ID;

            ViewBag.MY_RoleID = user.RoleID_Default;


            //取得最大角色名稱
            uHRMT24 HRMT24 = HRMT24Service.USP_HRMT24_QUERY("", "", "", "",0, user).FirstOrDefault();
            if (HRMT24!=null)
            {
                ViewBag.MaxROLEName = HRMT24.ROLE_NAME;
            }

            TempData["StatusMessage"] = "【勾選/取消】核取方塊後立即【增加權限/取消權限】<br/>";

            if (ViewBag.MY_RoleID == "0" && ViewBag.ROLE_ID == "0")
            {
                TempData["StatusMessage"] = TempData["StatusMessage"] + "您是" + ViewBag.MaxROLEName + "，擁有最大權限。<br/>";
            }
            else if (ViewBag.MY_RoleID == ViewBag.ROLE_ID)
            {
                TempData["StatusMessage"] = TempData["StatusMessage"] + "自已無法異動自已權限。需要比你更高權限者，才能異動你的權限。<br/>";
            }

          

            return View(model);

        }

        [HttpPost]
        [CheckPermission(ResultType = "Json")] //檢查權限
        public JsonResult Save(string ROLE_ID, string BRE_NO, string ACTION_ID, string Checked)
        {
           
            string Success = string.Empty;
             user = UserProfileHelper.Get();


            string UseYN= PermissionService.GetPermission_Use_YN(BRE_NO, ACTION_ID, user.SCHOOL_NO, user.USER_NO);
            if (UseYN=="N")
            {
                ErrorMsg = "您無權異動資料";
                Success = "false";
            }

            if (Success != "false")
            {
                try
                {
                    uZZT10 Date = new uZZT10();

                    Date.ROLE_ID = ROLE_ID;
                    Date.BRE_NO = BRE_NO;
                    Date.ACTION_ID = ACTION_ID;
                    Date.CRE_DATE = DateTime.Now;
                    Date.CRE_PERSON = user.USER_KEY;

                    if (Checked == "true")
                    {
                        Db.AddDate(Date);
                    }
                    else
                    {
                        Db.DelDate(Date);
                    }



                    if (Db.ErrorMsg != null && Db.ErrorMsg != string.Empty)
                    {
                        Success = "false";
                        ErrorMsg = ErrorMsg + Db.ErrorMsg;
                    }
                    else
                    {
                        Success = "true";
                    }

                }
                catch (Exception ex)
                {
                    Success = "false";
                    ErrorMsg = ErrorMsg + ex;
                }
            }

            
            var data = "{ \"Success\" : \"" + Success + "\" , \"Error\" : \"" + ErrorMsg + "\" }";
            return Json(data, JsonRequestBehavior.AllowGet);
        }

    }
}