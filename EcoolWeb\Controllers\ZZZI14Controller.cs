﻿using com.ecool.service;
using ECOOL_APP.com.ecool.Models.DTO;
using ECOOL_APP.com.ecool.Models.entity;
using ECOOL_APP.EF;
using EcoolWeb.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using MvcPaging;
using ECOOL_APP;
using EcoolWeb.CustomAttribute;

namespace EcoolWeb.Controllers
{
    [SessionExpire]
    public class ZZZI14Controller : Controller
    {

        #region 此控制項 private 定義

        /// <summary>
        /// 此Controller 對應的程式代碼
        /// </summary>
       private string Bre_NO = "ZZZI14";

        /// <summary>
        /// Error 訊息
        /// </summary>
        private string ErrorMsg = string.Empty;

        /// <summary>
        /// 資料庫相關處理
        /// </summary>
        private ZZZI14ViewModelService Db = new ZZZI14ViewModelService();

        /// <summary>
        /// 會員相關資訊
        /// </summary>
        private UserProfile user;

        /// <summary>
        /// 增加明細
        /// </summary>
        static private string AddItem = "AddItem";

        /// <summary>
        /// 新增資料
        /// </summary>
        static private string Create = "Create";

        /// <summary>
        /// 修改資料
        /// </summary>
        static private string Update = "Update";

        /// <summary>
        /// 刪除資料
        /// </summary>
        static private string Deldate = "Deldate";
        #endregion

        #region Controller Action 共用
        /// <summary>
        /// 畫面共用 來源資料 及定義格式
        /// </summary>
        /// <param name="DATA_YN"></param>
        /// <param name="ITEM_NUM"></param>
        /// <param name="model"></param>
        public void ActionShared(string DATA_YN, string ITEM_NUM, ZZZI14ViewModel model)
        {
            ViewBag.BRE_NO = Bre_NO;

            //動作後的狀態訊息
            ViewBag.StatusMessage = TempData["StatusMessage"];

            //判斷新增(Y)/修改(N) 
            ViewBag.DATA_YN = DATA_YN;

            //BRE_NO HTML格式
            IDictionary<string, object> IDObjectBRE_NO = new Dictionary<string, object>();
            IDObjectBRE_NO.Add("class", "form-control");
            IDObjectBRE_NO.Add("placeholder", "必填");
            if (DATA_YN == "Y")
            {
                IDObjectBRE_NO.Add("readonly", "true");
            }
            ViewData["IDObjectBRE_NO"] = IDObjectBRE_NO;
          
            //類別 下拉式選單 來源資料
            List<SelectListItem> BRE_TYPESelectList = uZZT01.BRE_TYPESelectListItem(model.BRE_TYPE);
            ViewBag.BRE_TYPESelectList = BRE_TYPESelectList;

            if (string.IsNullOrWhiteSpace(model.TARGET))
            {
                model.TARGET = "_self";
            }


            //程式類別(目標) 下拉式選單 來源資料
            List<SelectListItem> TARGETSelectList = uZZT01.TargetSelectListItem(model.TARGET);
            ViewBag.TARGETSelectList = TARGETSelectList;

            //動作類別 下拉式選單 來源資料
            List<SelectListItem> ACTION_TYPE_ITEM = uZZT34.Action_TypeSelectListItem("");
            ViewBag.ACTION_TYPE_ITEM = ACTION_TYPE_ITEM;
           

            //計算明細筆數 
            int Count = 0;
            if (model.Details_List != null)
            {
                Count = model.Details_List.Count();
            }

            int TOLTAL;
            if (model.BRE_TYPE =="3")
            {
                TOLTAL = 0;
            }
            else
            {
                TOLTAL = GetTOLTAL(ITEM_NUM, Count);
            }
          
            TempData["TOLTAL"] = TOLTAL;

            //給畫面明細序號
            Db.SetSEQ_NO(model, TOLTAL, Count);
        }

        #endregion


        #region 查詢
        /// <summary>
        /// 功能清單列表
        /// </summary>
        /// <param name="page">頁次</param>
        /// <param name="pageSize">一頁筆數</param>
        /// <returns></returns>
        [CheckPermission(CheckBRE_NO = "ZZZI14", CheckACTION_ID = "Index")] //檢查權限
        public ActionResult Index(int page = 1, int pageSize = 999999)
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "功能清單列表";
            user = UserProfileHelper.Get();

            int count = int.MinValue;
            List<ZZZI14ViewModel> model = Db.GetListData("", page, pageSize, ref count);
            ViewData.Model = model.ToPagedList(page - 1, pageSize, count);

            return View();
        }
        #endregion

      
        #region  編輯畫面 / HttpGet
        /// <summary>
        ///  編輯畫面 / HttpGet
        /// </summary>
        /// <param name="BRE_NO"></param>
        /// <returns></returns>
        [HttpGet]
        [CheckPermission] //檢查權限
        public ActionResult Edit(string BRE_NO)
        {
            ViewBag.Panel_Title = "功能清單維護 - 編輯";
            string DATA_YN = "N";
            ZZZI14ViewModel model = new ZZZI14ViewModel();

            user = UserProfileHelper.Get();
           

            if (string.IsNullOrWhiteSpace(BRE_NO) == false)
            {
                model = Db.GetDetailsData(BRE_NO);
                DATA_YN = model != null ? "Y" : "N";
            }

            this.ActionShared(DATA_YN, "", model);


            return View(model);
        }

         #endregion

        #region 編輯畫面 / HttpPost
        /// <summary>
        ///  編輯畫面 / HttpPost
        /// </summary>
        /// <param name="model"></param>
        /// <param name="DATA_TYPE">btn(增加明細/新增資料/修改資料/刪除資料)</param>
        /// <param name="DATA_YN">判斷新增(Y)/修改(N) </param>
        /// <param name="ITEM_NUM">增加明細筆數</param>
        /// <returns></returns>
        [HttpPost]
        [CheckPermission] //檢查權限
        public ActionResult Edit(ZZZI14ViewModel model, string DATA_TYPE, string DATA_YN, string ITEM_NUM)
        {
            ViewBag.Panel_Title = "功能清單維護 - 編輯";
            user = UserProfileHelper.Get();

            if (DATA_TYPE==AddItem) //新增明細不驗正
	        {
                ModelState.Clear(); //清除驗正

                if (ITEM_NUM=="")
                {
                     ModelState.AddModelError("additem", "【增加明細】請輸入筆數");
                }
	        }
            else
            {

                var OK =this.Save(model, DATA_TYPE, user);

                if (OK)
                {
                    if (DATA_TYPE==Deldate)
                    {
                        TempData["StatusMessage"] = "刪除成功";
                        return RedirectToAction("Index");
                    }
                    else
                    {
                        TempData["StatusMessage"] = "異動成功";
                        return RedirectToAction("Edit", new { BRE_NO = model.BRE_NO });
                    }
                }
                else
                {
                    if (ErrorMsg!="")
                    {
                        ModelState.AddModelError("DetailsError", ErrorMsg);
                    }
                }
            }

            this.ActionShared(DATA_YN, ITEM_NUM, model);


            return View(model);
        }
        #endregion

        #region 存檔
        /// <summary>
        /// 存檔
        /// </summary>
        /// <param name="Data"></param>
        /// <param name="DATA_TYPE"></param>
        /// <param name="user"></param>
        /// <returns></returns>
        public bool Save(ZZZI14ViewModel Data, string DATA_TYPE, UserProfile user)
        {
            bool ReturnBool;

            if (DATA_TYPE == Create || DATA_TYPE==Update) //新增、修改
            {
                //移除DEL有勾選的資料
                if (Data.Details_List!=null)
                {
                    Data.Details_List.RemoveAll(a => a.Del == true);
                }

              

                this.Check(Data, DATA_TYPE);

                 #if DEBUG
                    var errorsaa = ModelState
                                .Where(x => x.Value.Errors.Count > 0)
                                .Select(x => new { x.Key, x.Value.Errors })
                                .ToArray();
                #endif




                if (ModelState.IsValid) //沒有錯誤
                {
                  
                    if (Data.BRE_TYPE!="3")
                    {
                        //取得預設動作值主檔
                        Data.ACTION_ID = Data.Details_List.Where(a => a.DF_ACTION_ID_Check == true && a.Del == false).Select(S => S.ACTION_ID).First();
                    }
                  

                    if (DATA_TYPE == Create)
                    {
                        Db.CreateDate(Data, user);
                    }
                    else
                    {
                        Db.UpDate(Data, user);
                    }
                }
                else
                {
                    ReturnBool = false;
                    return ReturnBool;
                }
            }
            else if (DATA_TYPE == Deldate) //刪除
            {
                Db.DelDate(Data.BRE_NO);
            }
            ErrorMsg = Db.ErrorMsg;

            if (ErrorMsg!=null)
            {
                ReturnBool = false;
            }
            else
            {
                ReturnBool = true;
            }

            return ReturnBool;
        }

        #endregion

        #region 驗証
        /// <summary>
        /// 驗証
        /// </summary>
        /// <param name="Data"></param>
        /// <param name="DATA_TYPE"></param>
        public void Check(ZZZI14ViewModel Data, string DATA_TYPE)
        {
            if (DATA_TYPE == Create)
            {
                if (Data.BRE_NO != null)
                {
                    if (Db.CheckisBRE_NO(Data.BRE_NO) == true)
                    {
                        ModelState.AddModelError("BRE_NO", "【功能代號】重覆");
                    }
                }  
            }

            //標題時CONTROLLER不需填
            if (Data.BRE_TYPE=="3") 
            {
                ModelState.Remove("CONTROLLER"); 
            }
   

            if (Data.BRE_NO_PRE == null && Data.LEVEL_ID > 0)
            {
                ModelState.AddModelError("LEVEL_ID", "【父階功能代號】空白時，【階層】請輸入 0 ");
            }
            else if (Data.BRE_NO_PRE != null && Data.LEVEL_ID == 0)
            {
                ModelState.AddModelError("BRE_NO_PRE", "【階層】輸入 0 時，【父階功能代號】必需輸入");
            }
            else if (Data.BRE_NO_PRE != null && Data.LEVEL_ID != 0)
            {
                if ( Db.CheckisBRE_NO(Data.BRE_NO_PRE)==false)
                {
                     ModelState.AddModelError("BRE_NO_PRE", "輸入的【父階功能代號】不是有效值");
                }
            }

            if (Data.BRE_TYPE != "3")
            {
                if (Data.Details_List==null)
                {
                    ModelState.AddModelError("DetailsError", "未輸入任何明細");
                }
                else
                {
                    var DF_ACTION_ID_Data = Data.Details_List.Where(a => a.DF_ACTION_ID_Check == true && a.Del == false);

                    if (DF_ACTION_ID_Data.Count() > 1)
                    {
                        ModelState.AddModelError("DetailsError", "【預設動作】只能勾選1筆");
                    }
                    else if (DF_ACTION_ID_Data.Count() == 0)
                    {
                        ModelState.AddModelError("DetailsError", "【預設動作】未勾選");
                    }
                    else if (DF_ACTION_ID_Data.Count() == 1)
                    {
                        if (Data.BRE_TYPE == "1" && DF_ACTION_ID_Data.First().ACTION_TYPE != "ALL")
                        {
                            ModelState.AddModelError("DetailsError", "【類別】選「全部」時【預設動作】那筆的【預設類別】也需選「全部」");
                        }
                    }

                    if (Data.Details_List.Where(a => a.Del == false).Count() == 0 && Data.BRE_TYPE != "3")
                    {
                        ModelState.AddModelError("DetailsError", "未輸入任何明細");
                    }

                    if (Data.BRE_TYPE != "3")
                    {
                        var Grouphaving = Data.Details_List.Where(a => a.Del == false).GroupBy(c => c.ACTION_ID).Where(grp => grp.Count() > 1).Select(grp => grp.Key);
                        if (Grouphaving.Count() > 0)
                        {
                            ModelState.AddModelError("DetailsError", "動作代碼不可重覆");
                        }
                    }
                }
            }


            if ( Data.BRE_TYPE!="3")
            {
                foreach (var D_Data in Data.Details_List)
                {
                    if (D_Data.Del == false)
                    {
                        if (D_Data.ACTION_ID == null || D_Data.ACTION_ID=="")
                        {
                            ModelState.AddModelError("Details_List[" + D_Data.ITEM+ "].ACTION_ID", "【動作代碼】必需輸入");   
                        }

                        if (D_Data.ACTION_NAME == null || D_Data.ACTION_NAME == "")
                        {
                            ModelState.AddModelError("Details_List[" + D_Data.ITEM + "].ACTION_NAME", "【動作名稱】必需輸入");
                        }
                    }
                }  
            }
            else
            {
                if (Data.Details_List!=null)
                {
                    foreach (var D_Data in Data.Details_List)
                    {
                        D_Data.Del = true;
                    }
                }
            }
        }


        #endregion

        #region 給畫面明細筆數
        /// <summary>
        /// 給畫面明細筆數
        /// </summary>
        /// <param name="ITEM_NUM">增加筆數</param>
        /// <param name="Count">資料庫筆數</param>
        /// <returns></returns>
        public int GetTOLTAL(string ITEM_NUM, int Count)
        {
            int Temp;
            if (ITEM_NUM != "")
            {
                Temp = Convert.ToInt16(ITEM_NUM) + Count;
            }
            else
            {
                Temp = Count > 0 ? Count : 3;
            }

            return Temp;
        }

        #endregion

    
    }
}