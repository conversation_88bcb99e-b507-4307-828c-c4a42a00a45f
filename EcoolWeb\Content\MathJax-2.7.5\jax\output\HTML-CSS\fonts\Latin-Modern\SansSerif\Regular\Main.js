/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Latin-Modern/SansSerif/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.LatinModernMathJax_SansSerif={directory:"SansSerif/Regular",family:"LatinModernMathJax_SansSerif",testString:"\u00A0\uD835\uDDA0\uD835\uDDA1\uD835\uDDA2\uD835\uDDA3\uD835\uDDA4\uD835\uDDA5\uD835\uDDA6\uD835\uDDA7\uD835\uDDA8\uD835\uDDA9\uD835\uDDAA\uD835\uDDAB\uD835\uDDAC\uD835\uDDAD",32:[0,0,332,0,0],160:[0,0,332,0,0],120224:[694,0,667,28,638],120225:[694,0,667,97,610],120226:[706,11,639,66,587],120227:[694,0,722,96,665],120228:[691,0,597,94,554],120229:[691,0,569,94,526],120230:[706,11,667,66,598],120231:[694,0,708,94,613],120232:[694,0,278,94,183],120233:[694,22,472,42,388],120234:[694,0,694,96,651],120235:[694,0,542,94,499],120236:[694,0,875,100,774],120237:[694,0,708,96,611],120238:[716,22,736,56,679],120239:[694,0,639,96,582],120240:[716,125,736,56,679],120241:[694,0,646,96,617],120242:[716,22,556,44,499],120243:[688,0,681,36,644],120244:[694,22,688,94,593],120245:[694,0,667,14,652],120246:[694,0,944,14,929],120247:[694,0,667,14,652],120248:[694,0,667,3,663],120249:[694,0,611,56,560],120250:[461,11,481,54,409],120251:[694,11,517,82,480],120252:[461,11,444,36,415],120253:[694,11,517,36,434],120254:[461,11,444,35,414],120255:[705,0,306,27,347],120256:[455,206,500,28,485],120257:[694,0,517,81,435],120258:[655,0,239,75,164],120259:[655,205,267,-61,184],120260:[694,0,489,84,471],120261:[694,0,239,81,156],120262:[455,0,794,81,713],120263:[455,0,517,81,435],120264:[461,11,500,30,469],120265:[455,194,517,82,480],120266:[455,194,517,36,434],120267:[455,0,342,82,327],120268:[461,11,383,28,360],120269:[571,11,361,19,332],120270:[444,11,517,81,435],120271:[444,0,461,14,446],120272:[444,0,683,14,668],120273:[444,0,461,0,460],120274:[444,205,461,14,446],120275:[444,0,435,28,402],120276:[694,0,733,42,690],120277:[694,0,733,91,671],120278:[706,11,703,61,646],120279:[694,0,794,91,732],120280:[691,0,642,91,595],120281:[691,0,611,91,564],120282:[706,11,733,61,658],120283:[694,0,794,91,702],120284:[694,0,331,92,239],120285:[694,22,519,46,427],120286:[694,0,764,91,701],120287:[694,0,581,91,534],120288:[694,0,978,91,886],120289:[694,0,794,91,702],120290:[716,22,794,61,732],120291:[694,0,703,91,641],120292:[716,106,794,61,732],120293:[694,0,703,91,653],120294:[716,22,611,49,549],120295:[688,0,733,40,692],120296:[694,22,764,91,672],120297:[694,0,733,27,705],120298:[694,0,1039,24,1014],120299:[694,0,733,37,694],120300:[694,0,733,24,708],120301:[694,0,672,61,616],120302:[475,11,525,41,474],120303:[694,11,561,61,523],120304:[475,11,489,37,457],120305:[694,11,561,37,499],120306:[475,11,511,31,479],120307:[705,0,336,30,381],120308:[469,206,550,25,534],120309:[694,0,561,60,500],120310:[673,0,256,54,201],120311:[673,205,286,-71,224],120312:[694,0,531,69,497],120313:[694,0,256,61,194],120314:[469,0,867,60,806],120315:[469,0,561,60,500],120316:[475,11,550,31,518],120317:[469,194,561,61,523],120318:[469,194,561,37,499],120319:[469,0,372,61,356],120320:[475,11,422,31,396],120321:[589,11,404,20,373],120322:[458,11,561,60,500],120323:[458,0,500,26,473],120324:[458,0,744,24,719],120325:[458,0,500,24,474],120326:[458,205,500,29,473],120327:[458,0,476,31,441],120328:[694,0,667,28,638],120329:[694,0,667,97,696],120330:[706,11,639,130,718],120331:[694,0,722,96,746],120332:[691,0,597,94,687],120333:[691,0,569,94,673],120334:[706,11,667,130,729],120335:[694,0,708,94,761],120336:[694,0,278,94,331],120337:[694,22,472,46,536],120338:[694,0,694,96,785],120339:[694,0,542,94,513],120340:[694,0,875,100,922],120341:[694,0,708,96,759],120342:[716,22,736,119,762],120343:[694,0,639,96,690],120344:[716,125,736,119,762],120345:[694,0,646,96,700],120346:[716,22,556,54,607],120347:[688,0,681,168,790],120348:[694,22,688,137,741],120349:[694,0,667,162,800],120350:[694,0,944,162,1077],120351:[694,0,667,14,758],120352:[694,0,667,151,811],120353:[694,0,611,56,702],120354:[461,11,481,75,474],120355:[694,11,517,82,534],120356:[461,11,444,77,499],120357:[694,11,517,75,582],120358:[461,11,444,77,471],120359:[705,0,306,101,495],120360:[455,206,500,11,571],120361:[694,0,517,81,505],120362:[655,0,239,81,303],120363:[655,205,267,-97,323],120364:[694,0,489,84,543],120365:[694,0,239,81,304],120366:[455,0,794,81,783],120367:[455,0,517,81,505],120368:[461,11,500,71,521],120369:[455,194,517,41,534],120370:[455,194,517,75,531],120371:[455,0,342,82,424],120372:[461,11,383,35,434],120373:[571,11,361,101,410],120374:[444,11,517,98,529],120375:[444,0,461,108,540],120376:[444,0,683,108,762],120377:[444,0,461,0,537],120378:[444,205,461,1,540],120379:[444,0,435,28,494],120380:[694,0,733,47,696],120381:[694,0,733,98,753],120382:[706,11,703,122,773],120383:[694,0,794,98,818],120384:[691,0,642,98,719],120385:[691,0,611,98,703],120386:[706,11,733,122,785],120387:[694,0,794,98,841],120388:[694,0,331,99,378],120389:[694,22,519,52,566],120390:[694,0,764,98,839],120391:[694,0,581,98,548],120392:[694,0,978,98,1025],120393:[694,0,794,98,841],120394:[716,22,794,119,819],120395:[694,0,703,98,749],120396:[716,106,794,119,819],120397:[694,0,703,98,752],120398:[716,22,611,61,651],120399:[688,0,733,171,830],120400:[694,22,764,132,811],120401:[694,0,733,168,847],120402:[694,0,1039,164,1156],120403:[694,0,733,41,792],120404:[694,0,733,166,851],120405:[694,0,672,68,750],120406:[475,11,525,65,545],120407:[694,11,561,68,582],120408:[475,11,489,77,541],120409:[694,11,561,75,638],120410:[475,11,511,72,542],120411:[705,0,336,93,525],120412:[469,206,550,8,621],120413:[694,0,561,67,575],120414:[673,0,256,68,336],120415:[673,205,286,-105,359],120416:[694,0,531,76,575],120417:[694,0,256,68,333],120418:[469,0,867,67,881],120419:[469,0,561,67,575],120420:[475,11,550,70,575],120421:[469,194,561,27,583],120422:[469,194,561,75,590],120423:[469,0,372,68,452],120424:[475,11,422,40,469],120425:[589,11,404,108,445],120426:[458,11,561,81,589],120427:[458,0,500,116,564],120428:[458,0,744,115,810],120429:[458,0,500,28,549],120430:[458,205,500,3,565],120431:[458,0,476,38,527],120662:[694,0,733,42,690],120663:[694,0,733,91,671],120664:[691,0,581,91,534],120665:[694,0,917,60,856],120666:[691,0,642,91,595],120667:[694,0,672,61,616],120668:[694,0,794,91,702],120669:[716,22,856,61,794],120670:[694,0,331,92,239],120671:[694,0,764,91,701],120672:[694,0,672,40,631],120673:[694,0,978,91,886],120674:[694,0,794,91,702],120675:[688,0,733,46,686],120676:[716,22,794,61,732],120677:[691,0,794,91,702],120678:[694,0,703,91,641],120679:[716,22,856,61,794],120680:[694,0,794,61,732],120681:[688,0,733,40,692],120682:[716,0,856,61,794],120683:[694,0,794,61,732],120684:[694,0,733,37,694],120685:[694,0,856,61,794],120686:[716,0,794,49,744],120687:[694,0,917,60,856],120688:[469,12,837,56,781],120689:[705,195,606,56,550],120690:[469,217,698,56,641],120691:[733,12,541,56,485],120692:[480,23,512,56,456],120693:[750,208,566,56,510],120694:[469,217,735,56,679],120695:[705,12,567,56,511],120696:[469,12,477,56,421],120697:[469,12,675,56,619],120698:[694,12,764,56,708],120699:[469,217,735,56,679],120700:[469,0,574,56,518],120701:[750,226,574,56,518],120702:[475,11,599,56,543],120703:[458,12,760,55,703],120704:[469,217,567,56,511],120705:[469,109,524,56,468],120706:[458,12,683,56,627],120707:[458,11,699,55,642],120708:[470,12,728,56,672],120709:[469,217,716,56,660],120710:[469,206,849,56,793],120711:[694,206,870,56,814],120712:[470,12,731,56,675],120713:[716,22,569,56,513],120714:[458,12,519,56,462],120715:[705,12,811,56,755],120716:[469,10,609,56,553],120717:[694,206,730,56,674],120718:[469,245,567,56,511],120719:[458,12,1035,55,978],120720:[694,0,733,47,696],120721:[694,0,733,98,753],120722:[691,0,581,98,673],120723:[694,0,917,64,861],120724:[691,0,642,98,719],120725:[694,0,672,68,750],120726:[694,0,794,98,841],120727:[716,22,856,120,880],120728:[694,0,331,99,378],120729:[694,0,764,98,839],120730:[694,0,672,45,637],120731:[694,0,978,98,1025],120732:[694,0,794,98,841],120733:[688,0,733,53,818],120734:[716,22,794,119,819],120735:[691,0,794,98,841],120736:[694,0,703,98,749],120737:[716,22,856,120,880],120738:[694,0,794,67,871],120739:[688,0,733,171,830],120740:[716,0,856,178,915],120741:[694,0,794,128,812],120742:[694,0,769,60,811],120743:[694,0,856,184,919],120744:[716,0,794,56,836],120745:[682,12,917,130,927],120746:[469,12,837,96,799],120747:[705,195,606,16,620],120748:[469,217,697,114,736],120749:[733,12,541,96,566],120750:[480,23,513,81,516],120751:[750,208,566,98,562],120752:[469,217,735,120,748],120753:[705,12,567,109,605],120754:[469,12,477,76,454],120755:[469,12,676,66,652],120756:[694,12,764,105,708],120757:[469,217,736,22,712],120758:[469,0,574,62,605],120759:[750,226,574,77,521],120760:[475,11,599,95,600],120761:[458,12,760,121,789],120762:[469,217,568,22,568],120763:[469,109,524,106,501],120764:[458,12,683,92,712],120765:[458,11,699,121,728],120766:[470,12,728,120,736],120767:[469,217,716,103,718],120768:[469,206,849,108,786],120769:[694,206,870,120,878],120770:[470,12,731,100,738],120771:[716,22,569,93,589],120772:[458,12,519,99,492],120773:[705,12,811,120,835],120774:[469,10,609,67,639],120775:[694,206,730,98,729],120776:[469,245,567,66,568],120777:[458,12,1035,121,1064],120802:[678,22,500,42,457],120803:[678,0,500,89,424],120804:[678,0,500,42,449],120805:[678,22,500,42,457],120806:[656,0,500,28,471],120807:[656,22,500,39,449],120808:[678,22,500,42,457],120809:[656,11,500,42,457],120810:[678,22,500,42,457],120811:[678,22,500,42,457],120812:[716,22,550,43,506],120813:[716,0,550,76,473],120814:[716,0,550,46,494],120815:[716,22,550,46,503],120816:[694,0,550,31,518],120817:[694,22,550,37,494],120818:[716,22,550,46,503],120819:[695,11,550,46,503],120820:[716,22,550,46,503],120821:[716,22,550,46,503]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"LatinModernMathJax_SansSerif"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/SansSerif/Regular/Main.js"]);
