﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'find', 'pl', {
	find: '<PERSON>na<PERSON><PERSON><PERSON>',
	findOptions: '<PERSON><PERSON><PERSON> wyszukiwan<PERSON>',
	findWhat: '<PERSON><PERSON><PERSON><PERSON><PERSON>:',
	matchCase: 'Uwzględnij wielko<PERSON> liter',
	matchCyclic: 'Cykliczne dopasowanie',
	matchWord: 'Całe słowa',
	notFoundMsg: 'Nie znaleziono szukanego hasła.',
	replace: '<PERSON>ami<PERSON><PERSON>',
	replaceAll: '<PERSON>ami<PERSON><PERSON> wszystko',
	replaceSuccessMsg: '%1 wystąpień zastąpionych.',
	replaceWith: 'Zastąp przez:',
	title: 'Znajdź i zamień'
} );
