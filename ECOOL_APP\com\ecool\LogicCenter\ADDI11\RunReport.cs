﻿using Dapper;
using ECOOL_APP.com.ecool.service;
using ECOOL_APP.com.ecool.util;
using ECOOL_APP.EF;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace ECOOL_APP.com.ecool.LogicCenter.ADDI11
{
    public class RunReport : IDisposable
    {
        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();

        // 單日沒達到標準符號
        public static readonly string storageGridSymbol = "★";
        // Body範圍B8:X18
        public static readonly int bodyMinRow = 8;
        public static readonly int bodyMaxRow = 18;
        public static readonly CellLetter bodyMinCell = CellLetter.B;
        public static readonly CellLetter bodyMaxCell = CellLetter.X;
        Func<CellLetter, string> GetCellName = (letter) => Enum.GetName(typeof(CellLetter), letter);

        // 範本路徑
        public static string GetDataFilePath() => HttpRuntime.AppDomainAppVirtualPath != null ?
            Path.Combine(Directory.GetParent(HttpRuntime.AppDomainAppPath).Parent.FullName, @"EcoolWeb\Content\ExcelSample\ADDI11_SchoolRunStatistics.xlsx") :
            Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);

        //  -- 以下為必須注入之參數 --

        // 實際開始時間
        private DateTime actualStartDate;
        public DateTime SemesterStart { get {
                if (actualStartDate.DayOfWeek == DayOfWeek.Saturday) // 往後移2天從禮拜一開始
                    actualStartDate = actualStartDate.AddDays(2);
                else if (actualStartDate.DayOfWeek == DayOfWeek.Sunday) // 往後移1天從禮拜一開始
                    actualStartDate = actualStartDate.AddDays(1);
                return actualStartDate;
            } set { actualStartDate = value; } }
        // 實際結束時間
        private DateTime actualEndDate;
        public DateTime SemesterEnd { get {
                if (actualEndDate.DayOfWeek == DayOfWeek.Saturday) // 往前移1天從禮拜五結束
                    actualEndDate = actualEndDate.AddDays(-1);
                else if (actualEndDate.DayOfWeek == DayOfWeek.Sunday) // 往前移2天從禮拜五結束
                    actualEndDate = actualEndDate.AddDays(-2);
                return actualEndDate;
            } set { actualEndDate = value; } }

        public string SchoolNo { get; set; }
        public string ClassNo { get; set; }
        // 達標跑步人數
        public int RunPeopleStandard { get; set; } 
        // -- --

        // 跑步統計
        List<ADDI11TotalRunLog> runTotalList;

        /// <summary>
        /// 建立NPOI .xlsx
        /// </summary>
        /// <returns>IWorkbook</returns>
        public IWorkbook BuildWorkBook()
        {
            IWorkbook workbook;
            using (FileStream file = new FileStream(GetDataFilePath(), FileMode.Open, FileAccess.Read))
            {
                workbook = new XSSFWorkbook(file);
            }
            ISheet sheet = workbook.GetSheetAt(0);
            List<Storage> header = CreateHeader();
            LinkedStorage body = CreateBody();

            WriteHeader(header, sheet);
            WriteBody(body, sheet);

            return workbook;
        }

        /// <summary>
        /// 建立表頭的儲存格
        /// </summary>
        /// <returns></returns>
        public List<Storage> CreateHeader()
        {
            List<Storage> storageList = new List<Storage>();

            // 標頭
            int SYear; int Semesters;
            SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters); // 取得目前的Semester
            var school = db.BDMT01.Where(b => b.SCHOOL_NO == SchoolNo).FirstOrDefault();
            string title = $@"{school.CITY} {SYear} 學年度第 {Semesters} 學期  {school.SHORT_NAME} 班級跑步活動次數統計表(有打勾ν處請記得填寫)";
            storageList.Add(new Storage(CellLetter.A, 1) { Value = title });

            //導師姓名
            var teacherInfos = db.HRMT03
                .Join(db.HRMT01, h03 => new { h03.SCHOOL_NO, USER_NO = h03.TEACHER_NO },
                h01 => new { h01.SCHOOL_NO, h01.USER_NO },
                (h03, h01) => new { USER_NO = h03.TEACHER_NO, NAME = h01.NAME, SCHOOL_NO = h03.SCHOOL_NO, CLASS_NO = h03.CLASS_NO, CLASS_NAME = h03.CLASSNAME })
                .Where(h => h.SCHOOL_NO == SchoolNo && h.CLASS_NO == ClassNo)
                .FirstOrDefault();
            storageList.Add(new Storage(CellLetter.B, 19) { Value = teacherInfos?.NAME });
            storageList.Add(new Storage(CellLetter.E, 2) { Value = teacherInfos?.NAME });
            //班級
            storageList.Add(new Storage(CellLetter.P, 3) { Value = teacherInfos?.CLASS_NAME });
            //活動日期-第一次
            storageList.Add(new Storage(CellLetter.S, 4) { Value = SemesterStart.ToString("yyyy年MM月dd日") });
            //活動日期-最終次
            storageList.Add(new Storage(CellLetter.S, 5) { Value = SemesterEnd.ToString("yyyy年MM月dd日") });
            //班級總人數
            int classStudentCount = db.HRMT01
                .Where(h => h.SCHOOL_NO == SchoolNo
                && h.CLASS_NO == ClassNo
                && h.USER_TYPE == UserType.Student
                && h.USER_STATUS == UserStaus.Enabled).Count();
            storageList.Add(new Storage(CellLetter.E, 4) { Value = classStudentCount.ToString() });
            //學期實施總次數
            int runDays = DateHelper.BusinessDaysUntil(SemesterStart.Date, SemesterEnd.Date); // 相差天數為總查詢跑步的天數
            storageList.Add(new Storage(CellLetter.E, 6) { Value = runDays.ToString() });

            return storageList;
        }

        /// <summary>
        /// 建立表身的儲存格
        /// </summary>
        /// <returns></returns>
        public LinkedStorage CreateBody()
        {
            // 撈出每天全校班級有跑步的人數
            string sSQL = $@"select addt24.SCHOOL_NO, addt24.CLASS_NO, RUN_DATE, count(*) as RUN_COUNT from 
                             (
	                             select distinct SCHOOL_NO, USER_NO, CLASS_NO, RUN_DATE, STATUS, SUM(LAP) as LAP from addt24 (nolock)
	                             group by SCHOOL_NO,USER_NO, CLASS_NO, RUN_DATE, STATUS
                             ) as addt24
                             join HRMT01 as h01 (nolock) on h01.SCHOOL_NO = addt24.SCHOOL_NO and h01.USER_NO = addt24.USER_NO    
                             where addt24.status=1 and addt24.LAP!=0 and RUN_DATE>=@START_DATE and RUN_DATE<=@END_DATE
                             and addt24.SCHOOL_NO=@SCHOOL_NO and h01.USER_TYPE='S'  
                             and addt24.CLASS_NO=@CLASS_NO
                             group by addt24.SCHOOL_NO, addt24.CLASS_NO, RUN_DATE
                             order by RUN_DATE desc, CLASS_NO desc";

            var totalRunLogList = db.Database.Connection.Query<ADDI11TotalRunLog>(sSQL, new
            {
                SCHOOL_NO = SchoolNo,
                CLASS_NO = ClassNo,
                START_DATE = SemesterStart.ToString("yyyy/MM/dd"),
                END_DATE = SemesterEnd.ToString("yyyy/MM/dd")
            });

            runTotalList = totalRunLogList.ToList();

            // -- 建立範圍B8:X18之儲存格 --
            int bodyStartRow = GetBodyStartRowIndex(SemesterStart);
            LinkedStorage body = new LinkedStorage(bodyMinCell, bodyStartRow);
            body.Date = SemesterStart;
            this.FillUp(body);
            // -- 儲存格建立完成 --

            return body;
        }

        /// <summary>
        /// Date對應Row起始位置，若為週末則往後挪到週一
        /// </summary>
        /// <param name="date"></param>
        /// <returns></returns>
        private int GetBodyStartRowIndex(DateTime date)
        {
            int startRow = -1;

            switch (date.DayOfWeek)
            {
                case DayOfWeek.Monday:
                    startRow = 8;
                    break;
                case DayOfWeek.Tuesday:
                    startRow = 10;
                    break;
                case DayOfWeek.Wednesday:
                    startRow = 12;
                    break;
                case DayOfWeek.Thursday:
                    startRow = 14;
                    break;
                case DayOfWeek.Friday:
                    startRow = 16;
                    break;
                case DayOfWeek.Saturday:
                    startRow = 8;
                    break;
                case DayOfWeek.Sunday:
                    startRow = 8;
                    break;
            }
            return startRow;
        }

        /// <summary>
        /// 遞迴建立Body的LinkedList
        /// </summary>
        /// <param name="body"></param>
        private void FillUp(LinkedStorage body)
        {
            int nextRow = body.Row;
            CellLetter nextCell = body.Cell;

            string cellName = GetCellName(body.Cell);

            bool isOutDateEndRange() // 日期範圍之外 
            {
                return (body.Date > SemesterEnd);
            }

            bool isReachStantard() // 是否達標
            {
                return runTotalList.Where(r => r.RUN_DATE == body.Date).FirstOrDefault()?.RUN_COUNT >= RunPeopleStandard;
            }

            if (body != null)
            {
                if (body.StorageGridType == StorageGridType.總次數)
                {
                    body.Value = $@"COUNTIF({ cellName + bodyMinRow }:{ cellName + (bodyMaxRow - 1) }, ""{ storageGridSymbol }"")";
                    body.IsFormula = true;
                    nextRow = 8;

                    //禮拜五算完六日不計算
                    if (body.Date.DayOfWeek == DayOfWeek.Friday)
                        body.Date = body.Date.AddDays(2);

                    nextCell++;
                }

                if (body.StorageGridType == StorageGridType.總計)
                {
                    body.Value = $@"COUNTIF({ GetCellName(bodyMinCell) + (body.Row + 1) }:{ GetCellName(bodyMaxCell - 1) + (body.Row + 1) }, ""{ storageGridSymbol }"")";
                    body.IsFormula = true;
                    nextRow += 2; // 合併兩列要跳兩格
                }

                if (body.StorageGridType == StorageGridType.是否達標)
                {
                    if (!isOutDateEndRange()
                        && isReachStantard())
                    {
                        body.Value = storageGridSymbol;
                    }
                    nextRow++;
                }

                if (body.StorageGridType == StorageGridType.日期)
                {
                    body.Date = (body.Previous == null) ?
                        body.Date : body.Previous.Date.AddDays(1);

                    if (!isOutDateEndRange())
                    {
                        body.Value = body.Date.ToString("MM月dd日");

                    }
                    nextRow++;
                }

                body.Next = new LinkedStorage(nextCell, nextRow);
                body.Next.Date = body.Date; // 記錄最後的日期
                body.Next.Previous = body;

                // 結束條件: 抵達最後一個儲存格
                if (nextRow == bodyMaxRow && nextCell == bodyMaxCell)
                {
                    body.Next.Value = $@"SUM({ GetCellName(bodyMaxCell) + bodyMinRow }:{ GetCellName(bodyMaxCell) + (bodyMaxRow - 1) })";
                    body.Next.IsFormula = true;
                    return;
                }

                FillUp(body.Next);
            }
        }

        private static void WriteHeader(List<Storage> header, ISheet sheet)
        {
            foreach (var storage in header)
            {
                var cell = sheet.GetRow(storage.Row - 1).GetCell((int)storage.Cell);
                var style = cell.CellStyle;
                cell.SetCellValue(storage.Value);
            }
        }

        private static void WriteBody(LinkedStorage body, ISheet sheet)
        {
            if (body != null)
            {
                var cell = sheet.GetRow(body.Row - 1).GetCell((int)body.Cell);
                var style = cell.CellStyle;
                if (body.IsFormula)
                {
                    cell.SetCellType(CellType.Formula);
                    cell.SetCellFormula(body.Value);
                }
                else
                {
                    cell.SetCellValue(body.Value);
                }
                WriteBody(body.Next, sheet);
            }
        }

        #region IDisposable 實作
        private bool disposed = false;
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }
        protected virtual void Dispose(bool disposing)
        {
            // Check to see if Dispose has already been called.
            if (!this.disposed)
            {
                if (disposing)
                {
                    db.Dispose();
                    db = null;
                }
                disposed = true;
            }
        }

        ~RunReport()
        {
            Dispose(false);
        }
        #endregion

    }
}
