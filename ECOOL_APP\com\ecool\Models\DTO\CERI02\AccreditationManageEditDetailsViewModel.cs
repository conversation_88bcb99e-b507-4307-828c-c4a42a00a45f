﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class AccreditationManageEditDetailsViewModel
    {
        public bool IsCopy { get; set; }

        /// <summary>
        ///認證細項ID
        /// </summary>
        [DisplayName("認證細項ID")]
        public string ACCREDITATION_ID { get; set; }

        /// <summary>
        ///項次
        /// </summary>
        [DisplayName("項次")]
        public string ITEM_NO { get; set; }

        /// <summary>
        ///主旨
        /// </summary>
        [DisplayName("主旨")]
        [Required]
        public string SUBJECT { get; set; }

        /// <summary>
        ///內容
        /// </summary>
        [DisplayName("內容")]
        [Required]
        public string CONTENT { get; set; }

        /// <summary>
        /// 年級+學期
        /// </summary>
        [Required(ErrorMessage = "*認證時間必輸")]
        public List<string> GRADE_SEMESTERs { get; set; }
        public string IsText { get; set; }
    }
}