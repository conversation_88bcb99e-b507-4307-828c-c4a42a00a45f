﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class GAAI01WearIndexViewModel
    {
        public enum WearModelTypeVal : byte
        {
            未配戴感應登記 = 1,
            有配戴感應登記 = 2,
            未配戴點選登記 = 3,
            有配戴點選登記 = 4,
            全部都配戴登記 = 5,
        }

        public string WhereSYEARSEMESTER { get; set; }

        /// <summary>
        /// 班級
        /// </summary>
        [DisplayName("我要登記班級")]
        public string WhereCLASS_NO { get; set; }

        /// <summary>
        /// 週期
        /// </summary>
        [DisplayName("我要登記日期")]
        public string WhereALARM_ID { get; set; }

        public string ALARM_DATE { get; set; }

        /// <summary>
        /// 學生數
        /// </summary>
        public int STUDENT_NUMBER { get; set; }

        /// <summary>
        /// 學生帳號
        /// </summary>
        public List<string> USER_NOs { get; set; }

        public byte IsSearch { get; set; }

        public GAAT01 gAAT01 { get; set; }

        /// <summary>
        /// 是否已填過
        /// </summary>
        public bool IsWearData { get; set; }

        /// <summary>
        /// 模式
        /// </summary>
        public WearModelTypeVal? WhereWearModelType { get; set; }

        /// <summary>
        /// 每位學生是否配載及未配載原因
        /// </summary>
        public virtual ICollection<GAAI01WearUserMemoViewModel> Users { get; set; }

        public virtual ICollection<GAAI01TagWearDetailsViewModel> TagWearDetails { get; set; }
    }
}