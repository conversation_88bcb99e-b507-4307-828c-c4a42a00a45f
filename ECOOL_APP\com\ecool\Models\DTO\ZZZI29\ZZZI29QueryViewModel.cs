﻿using ECOOL_APP.EF;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.com.ecool.Models.DTO
{
    public class ZZZI29QueryViewModel
    {

        [DisplayName("學校")]
        [Required]
        public string HRMT07_SCHOOL_NO { get; set; }

        [DisplayName("帳號/學號")]
        public string HRMT07_USER_NO { get; set; }


        [DisplayName("姓名")]
        public string HRMT07_NAME { get; set; }

        /// <summary>
        /// 查詢結果
        /// </summary>
        public HRMT01 HRMT01ds { get; set; }

    }

    public class ZZZI29DelViewModel
    {
        [DisplayName("學校")]
        [Required]
        public string HRMT07_SCHOOL_NO { get; set; }

        [DisplayName("帳號/學號")]
        public string HRMT07_USER_NO { get; set; }

        [DisplayName("好友帳號/學號")]
        public string STUDENT_USER_NO { get; set; }

        [DisplayName("學生班級")]
        public string CLASS_NO { get; set; }


    }

    public class ZZZI29ListViewModel
    {
        [DisplayName("學校")]
        [Required]
        public string Q_SCHOOL_NO { get; set; }

        [DisplayName("帳號/學號")]
        public string Q_USER_NO { get; set; }

        public List<ZZZI29QueryViewModel> ListQuery;

    }

}
