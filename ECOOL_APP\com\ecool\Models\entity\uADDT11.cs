﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.Mvc;
using static ECOOL_APP.EF.ADDT11;

namespace ECOOL_APP.com.ecool.Models.entity
{
    public class uADDT11
    {
        public static string STATUS_D = "D";

        public static string STATUS_R = "R";

        public static string STATUS_Z = "Z";
        public static string STATUS_H = "H";

        public uADDT11()
        {
            CASH = 10;
            GetCASH = 2;
        }

        ///Summary
        ///DIALOG_ID
        ///Summary
        [DisplayName("活動代碼")]
        public string DIALOG_ID { get; set; }

        /// <summary>
        ///分類 0.全部、1.學科、2.閱讀、3.行政
        /// </summary>
        [DisplayName("分類")]
        public byte? DIALOG_TYPE { get; set; }

        ///Summary
        ///SCHOOL_NO
        ///Summary
        [DisplayName("學校代碼")]
        [Required]
        public string SCHOOL_NO { get; set; }

        [DisplayName("學校")]
        public string SHORT_NAME { get; set; }

        ///Summary
        ///SYEAR
        ///Summary
        [DisplayName("學年")]
        [Required]
        public Nullable<int> SYEAR { get; set; }

        ///Summary
        ///SEMESTER
        ///Summary
        [DisplayName("學期")]
        [Required]
        public Nullable<byte> SEMESTER { get; set; }

        ///Summary
        ///DIALOG_NAME
        ///Summary
        [DisplayName("活動名稱")]
        [Required]
        [StringLength(50)]
        public string DIALOG_NAME { get; set; }

        ///Summary
        ///DIALOG_EXPRESS
        ///Summary
        [DisplayName("活動內容")]
        [AllowHtml]
        [UIHint("Html")]
        [Required]
        public string DIALOG_EXPRESS { get; set; }

        ///Summary
        ///DIALOG_SDATE
        ///Summary
        [DisplayName("開始日期")]
        [DataType(DataType.Date), DisplayFormat(DataFormatString = "{0:yyyy/MM/dd}", ApplyFormatInEditMode = true)]
        [Required]
        public Nullable<DateTime> DIALOG_SDATE { get; set; }

        ///Summary
        ///DIALOG_EDATE
        ///Summary
        [DisplayName("截止日期")]
        [DataType(DataType.Date), DisplayFormat(DataFormatString = "{0:yyyy/MM/dd}", ApplyFormatInEditMode = true)]
        [Required]
        public Nullable<DateTime> DIALOG_EDATE { get; set; }

        ///Summary
        ///USER_NO
        ///Summary
        [DisplayName("帳號")]
        public string USER_NO { get; set; }

        ///Summary
        ///NAME
        ///Summary
        [DisplayName("姓名")]
        public string NAME { get; set; }

        ///Summary
        ///SNAME
        ///Summary
        [DisplayName("承辦人員")]
        public string SNAME { get; set; }

        ///Summary
        ///JOB_NAME
        ///Summary
        [DisplayName("承辦人員")]
        public string JOB_NAME { get; set; }

        ///Summary
        ///CHG_PERSON
        ///Summary
        [DisplayName("修改人員")]
        public string CHG_PERSON { get; set; }

        ///Summary
        ///CHG_DATE
        ///Summary
        [DisplayName("修改日期")]
        [DataType(DataType.Date), DisplayFormat(DataFormatString = "{0:yyyy/MM/dd}", ApplyFormatInEditMode = true)]
        public Nullable<DateTime> CHG_DATE { get; set; }

        ///Summary
        ///CRE_PERSON
        ///Summary
        [DisplayName("建立人員")]
        public string CRE_PERSON { get; set; }

        ///Summary
        ///CRE_DATE
        ///Summary
        [DisplayName("建立日期")]
        [DataType(DataType.Date), DisplayFormat(DataFormatString = "{0:yyyy/MM/dd}", ApplyFormatInEditMode = true)]
        public Nullable<DateTime> CRE_DATE { get; set; }

        [DisplayName("狀態")]
        public string STATUS { get; set; }

        [DisplayName("狀態")]
        public string STATUS_NAME { get; set; }

        [DisplayName("備註")]
        public string MEMO { get; set; }

        /// <summary>
        ///回答次數 null =>不限制
        /// </summary>
        [DisplayName("限制回答次數")]
        [RegularExpression("^[0-9]+$", ErrorMessage = "請輸入正整數！")]
        public int? ANSWER_COUNT { get; set; }

        /// <summary>
        ///是否允許共享
        /// </summary>
        [DisplayName("是否允許共享")]
        public string COPY_YN { get; set; }

        /// <summary>
        ///是否限制對象
        /// </summary>
        [DisplayName("是否限制對象")]
        public string ANSWER_PERSON_YN { get; set; }

        [DisplayName("亂數出題")]
        public bool RANDOM { get; set; }

        [DisplayName("對象")]
        public string ANSWER_PERSON { get; set; }

        [DisplayName("學生酷幣點數")]
        [RegularExpression("^[0-9]+$", ErrorMessage = "請輸入正整數！")]
        [Range(0, 20, ErrorMessage = "範圍值為0~20")]
        public int? CASH { get; set; }

        [DisplayName("老師酷幣點數")]
        [RegularExpression("^[0-9]+$", ErrorMessage = "請輸入正整數！")]
        [Range(0, 5, ErrorMessage = "範圍值為0~5")]
        public int? GetCASH { get; set; }

        static public string GetSTATUS_NAME(string STATUS)
        {
            if (STATUS == STATUS_D) return "未發佈";
            if (STATUS == STATUS_R) return "已發佈";
            if (STATUS == STATUS_Z) return "提早結束";
            if (STATUS == STATUS_H) return "已發佈";
            return STATUS;
        }

        public List<SelectListItem> GetSTATUSListItem()
        {
            List<SelectListItem> lists = new List<SelectListItem>
            {
                new SelectListItem{Text="未發佈",Value="D"},
                new SelectListItem{Text="已發佈",Value="R"},
                new SelectListItem{Text="提早結束",Value="Z"},
                     new SelectListItem{Text="隱藏",Value="H"}
            };

            return lists;
        }
    }
}