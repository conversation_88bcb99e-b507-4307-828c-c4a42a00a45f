﻿using ECOOL_APP;
using ECOOL_APP.EF;
using EcoolWeb.Models;
using EcoolWeb.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using MvcPaging;
using EcoolWeb.CustomAttribute;
using System.Net;
using System.IO;
using System.Data.Entity.Validation;
using System.Data.Entity;
using com.ecool.service;
using System.ComponentModel.DataAnnotations;
using ECOOL_APP.com.ecool.Models.entity;

namespace EcoolWeb.Controllers
{
    [SessionExpire]
    public class ZZT17Controller : Controller
    {
        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();
        private ECOOL_APP.UserProfile user = UserProfileHelper.Get();
        private string SchoolNO = UserProfileHelper.GetSchoolNo();

        // GET: ZZT17
        public ActionResult Query(ZZT17QueryListViewModel model)
        {
            int PageSize = 50;
            if (model == null) model = new ZZT17QueryListViewModel();
            IQueryable<uZZT17_2> ZZT17List = (from z17 in db.ZZT17
                                              join h01 in db.HRMT01
                                                    on new { z17.SCHOOL_NO, z17.USER_NO }
                                                equals new { h01.SCHOOL_NO, h01.USER_NO }
                                              where z17.SCHOOL_NO == SchoolNO && h01.USER_STATUS != UserStaus.Invalid
                                              select new uZZT17_2
                                              {
                                                  ACTION_ID = z17.ACTION_ID,
                                                  IP_ADRESS = z17.IP_ADRESS,
                                                  LOG_STATUS = z17.LOG_STATUS,
                                                  SCHOOL_NO = z17.SCHOOL_NO,
                                                  ZZT17_NO = z17.ZZT17_NO,
                                                  ACTIONTIME = z17.ACTIONTIME,
                                                  USER_NO = z17.USER_NO,
                                                  SNAME = h01.SNAME,
                                                  USERNAME = h01.NAME,
                                                  SYEAR = h01.SYEAR,
                                                  CLASS_NO = h01.CLASS_NO,
                                                  USER_TYPE = h01.USER_TYPE
                                              });

            if (string.IsNullOrWhiteSpace(model.whereACTIONTIME_S) == false)
            {
                DateTime stime = Convert.ToDateTime(model.whereACTIONTIME_S.Trim());
                ZZT17List = ZZT17List.Where(a => a.ACTIONTIME >= stime);
            }

            if (string.IsNullOrWhiteSpace(model.whereACTIONTIME_E) == false)
            {
                DateTime etime = Convert.ToDateTime(model.whereACTIONTIME_E.Trim() + " 23:59:59");
                // etime = etime.AddHours(23).AddMinutes(59).AddSeconds(59);
                ZZT17List = ZZT17List.Where(a => a.ACTIONTIME <= etime);
            }

            if (string.IsNullOrWhiteSpace(model.whereKeyword) == false)
            {
                ZZT17List = ZZT17List.Where(a => a.USER_NO.Contains(model.whereKeyword.Trim()) || a.USERNAME.Contains(model.whereKeyword.Trim()) || a.SNAME.Contains(model.whereKeyword.Trim()));
            }

            if (string.IsNullOrWhiteSpace(model.whereUserNo2) == false)
            {
                ZZT17List = ZZT17List.Where(a => a.SCHOOL_NO == model.whereSchoolNo.Trim() && a.USER_NO == model.whereUserNo.Trim());
            }

            if (string.IsNullOrWhiteSpace(model.whereCLASS_NO) == false)
            {
                ZZT17List = ZZT17List.Where(a => a.CLASS_NO == model.whereCLASS_NO.Trim());
                PageSize = int.MaxValue;
            }

            if (string.IsNullOrWhiteSpace(model.whereGrade) == false)
            {
                ZZT17List = ZZT17List.Where(a => a.CLASS_NO.Substring(0, 1) == model.whereGrade);
            }

            switch (model.OrdercColumn)
            {
                case "ACTIONTIME":
                    ZZT17List = ZZT17List.OrderByDescending(a => a.ACTIONTIME);
                    break;

                default:
                    ZZT17List = ZZT17List.OrderByDescending(a => a.ACTIONTIME);
                    break;
            }

            model.ZZT17List = ZZT17List.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, PageSize);

            ViewBag.GradeItem = HRMT01.GetGradeItems(model.whereGrade);

            ViewBag.ClassItems = HRMT01.GetClassListData(SchoolNO, model.whereGrade, model.whereCLASS_NO, ref db)
                .Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.whereGrade });

            return View(model);
        }

        public ActionResult CountLoginQuery(ZZT17CountLoginQueryListViewModel model, FormCollection VZZT17)
        {
            int PageSize = 100;
            if (model == null) model = new ZZT17CountLoginQueryListViewModel();
            IQueryable<uZZT17> uZZT17List = (from z17 in db.ZZT17
                                             join h01 in db.HRMT01
                                                   on new { z17.SCHOOL_NO, z17.USER_NO }
                                               equals new { h01.SCHOOL_NO, h01.USER_NO }
                                             where
                                               z17.LOG_STATUS == "LoginSuccess" && z17.SCHOOL_NO == SchoolNO
                                               && h01.USER_STATUS != UserStaus.Invalid
                                             group h01 by new
                                             {
                                                 h01.SYEAR,
                                                 h01.CLASS_NO,
                                                 h01.SEAT_NO,
                                                 h01.USER_NO,
                                                 h01.SNAME,
                                                 h01.NAME,
                                                 h01.USER_TYPE
                                             } into g
                                             select new uZZT17
                                             {
                                                 LOGINCOUNT = (int)g.Count(p => p.USER_KEY != null),
                                                 SYEAR = g.Key.SYEAR,
                                                 CLASS_NO = g.Key.CLASS_NO,
                                                 SEAT_NO = g.Key.SEAT_NO,
                                                 USER_NO = g.Key.USER_NO,
                                                 SNAME = g.Key.SNAME,
                                                 NAME = g.Key.NAME,
                                                 USER_TYPE = g.Key.USER_TYPE
                                             });

            string USER_TYPE = string.Empty;

            if (string.IsNullOrWhiteSpace(VZZT17["ddlUSER_TYPE"]) == false)
            {
                USER_TYPE = VZZT17["ddlUSER_TYPE"];
                uZZT17List = uZZT17List.Where(a => a.USER_TYPE == USER_TYPE);
            }

            if (string.IsNullOrWhiteSpace(model.whereCLASS_NO) == false)
            {
                uZZT17List = uZZT17List.Where(a => a.CLASS_NO == model.whereCLASS_NO.Trim());
                PageSize = int.MaxValue;
            }

            if (string.IsNullOrWhiteSpace(model.whereGrade) == false)
            {
                uZZT17List = uZZT17List.Where(a => a.CLASS_NO.Substring(0, 1) == model.whereGrade);
            }

            switch (model.OrdercColumn)
            {
                //case "ACTIONTIME":
                //    uZZT17List = uZZT17List.OrderByDescending(a => a.ACTIONTIME);
                //    break;
                default:
                    uZZT17List = uZZT17List.OrderByDescending(a => a.LOGINCOUNT);
                    break;
            }

            model.uZZT17List = uZZT17List.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, PageSize);

            List<SelectListItem> USER_TYPEItems = new List<SelectListItem>();
            USER_TYPEItems.Add(new SelectListItem() { Text = "全部", Value = "", Selected = (string.IsNullOrWhiteSpace(USER_TYPE)) });
            USER_TYPEItems.Add(new SelectListItem() { Text = "管理者", Value = "A", Selected = (USER_TYPE == "A") });
            USER_TYPEItems.Add(new SelectListItem() { Text = "老師", Value = "T", Selected = (USER_TYPE == "T") });
            USER_TYPEItems.Add(new SelectListItem() { Text = "學生", Value = "S", Selected = (USER_TYPE == "S") });
            USER_TYPEItems.Add(new SelectListItem() { Text = "家長", Value = "P", Selected = (USER_TYPE == "P") });
            ViewBag.USER_TYPEItems = USER_TYPEItems;

            ViewBag.GradeItem = HRMT01.GetGradeItems(model.whereGrade);

            ViewBag.ClassItems = HRMT01.GetClassListData(SchoolNO, model.whereGrade, model.whereCLASS_NO, ref db)
                .Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.whereGrade });

            return View(model);
        }
    }
}