/*
Copyright (c) 2003-2015, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/

/*
editor_ie7.css
===============

This file contains styles to used by Internet Explorer 7 only.
*/

/* Base it on editor_ie.css, overriding it with styles defined in this file. */
@import url("editor_ie.css");

.cke_rtl .cke_toolgroup,
.cke_rtl .cke_toolbar_separator,
.cke_rtl .cke_button,
.cke_rtl .cke_button *,
.cke_rtl .cke_combo,
.cke_rtl .cke_combo *,
.cke_rtl .cke_path_item,
.cke_rtl .cke_path_item *,
.cke_rtl .cke_path_empty
{
	float: none;
}

.cke_rtl .cke_toolgroup,
.cke_rtl .cke_toolbar_separator,
.cke_rtl .cke_combo_button,
.cke_rtl .cke_combo_button *,
.cke_rtl .cke_button,
.cke_rtl .cke_button_icon
{
	display: inline-block;
	vertical-align: top;
}

.cke_toolbox
{
	display:inline-block;
	padding-bottom: 5px;
	height: 100%;
}
.cke_rtl .cke_toolbox
{
	padding-bottom: 0;
}

.cke_toolbar
{
	margin-bottom: 5px;
}
.cke_rtl .cke_toolbar
{
	margin-bottom: 0;
}

/* IE7: toolgroup must be adapted to toolbar items height. */
.cke_toolgroup
{
	height: 26px;
}

/* Avoid breaking elements that use background gradient when page zoom > 1 (#9548) */
.cke_toolgroup,
.cke_combo
{
	position: relative;
}

a.cke_button
{
	/* IE7: buttons must not float to wrap the toolbar in a whole. */
	float:none;

	/* IE7: buttons have to be aligned to top. Otherwise, some buttons like
	 * source and scayt are displayed a few pixels below the base line.
	 */
	vertical-align:top;
}

.cke_toolbar_separator
{
	display: inline-block;
	float: none;
	vertical-align: top;
	background-color: #c0c0c0;
}

.cke_toolbox_collapser .cke_arrow
{
	margin-top: 0;
}
.cke_toolbox_collapser .cke_arrow
{
	border-width:4px;
}
.cke_toolbox_collapser.cke_toolbox_collapser_min .cke_arrow
{
	border-width:3px;
}

.cke_rtl .cke_button_arrow
{
	padding-top: 8px;
	margin-right: 2px;
}

.cke_rtl .cke_combo_inlinelabel
{
	display: table-cell;
	vertical-align: middle;
}

/*
 * Editor menus are display:table-driven. IE7 doesn't support this approach,
 * hence this position&float hybrid fall-back.
 */
.cke_menubutton
{
	display: block;
	height: 24px;
}

.cke_menubutton_inner
{
	display: block;
	position: relative;
}

.cke_menubutton_icon
{
	height: 16px;
	width: 16px;
}

.cke_menubutton_icon,
.cke_menubutton_label,
.cke_menuarrow
{
	display: inline-block;
}

.cke_menubutton_label
{
	width: auto;
	vertical-align: top;
	line-height: 24px;
	height: 24px;
	margin: 0 10px 0 0;
}

.cke_menuarrow
{
	width: 5px;
	height: 6px;
	padding: 0;
	position: absolute;
	right: 8px;
	top: 10px;

	background-position: 0 0;
}

/* Menus in RTL mode. */
.cke_rtl .cke_menubutton_icon
{
	position: absolute;
	right: 0px;
	top: 0px;
}

.cke_rtl .cke_menubutton_label
{
	float: right;
	clear: both;
	margin: 0 24px 0 10px;
}

.cke_hc .cke_rtl .cke_menubutton_label
{
	margin-right: 0;
}


.cke_rtl .cke_menuarrow
{
	left: 8px;
	right: auto;
	background-position: 0 -24px;
}

.cke_hc .cke_menuarrow
{
	top: 5px;
	padding: 0 5px;
}

.cke_rtl input.cke_dialog_ui_input_text,
.cke_rtl input.cke_dialog_ui_input_password
{
	/* Positioning is required for IE7 on text inputs not to stretch dialog horizontally. (#8971)*/
	position: relative;
}

/* Reset vertical paddings which put editing area under bottom UI space. (#9721) */
.cke_wysiwyg_div
{
	padding-top: 0 !important;
	padding-bottom: 0 !important;
}
