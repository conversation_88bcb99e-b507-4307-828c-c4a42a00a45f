﻿@model BackupLinkInfoViewModel

@using (Html.BeginCollectionItem("BackupLinkInfo"))
{
    var Index = Html.GetIndex("BackupLinkInfo");

    <div class="tr" id="Tr@(Index)">
        <div class="td" style="text-align:center">
            <a role='button' style="cursor:pointer;" onclick="deleteRow('Tr@(Index)')"> <i class='glyphicon glyphicon-remove'></i></a>
        </div>
        <div class="td" style="text-align:center">
            @Html.EditorFor(m => m.BACKUP_NAME, new { htmlAttributes = new { @class = "form-control input-md", @placeholder = "必輸" } })
        </div>
        <div class="td" style="text-align:center">
            @Html.EditorFor(m => m.BACKUP_URL, new { htmlAttributes = new { @class = "form-control input-md", @placeholder = "必輸" } })
        </div>
        <div class="td" style="text-align:center">
            @Html.CheckBoxFor(m => m.IS_ENABLE)
        </div>
    </div>
}