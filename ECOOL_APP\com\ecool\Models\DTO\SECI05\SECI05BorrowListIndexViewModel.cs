﻿
using MvcPaging;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;

namespace ECOOL_APP.EF
{
    public class SECI05BorrowListIndexViewModel
    {
        public string WhereSCHOOL_NO { get; set; }

        public string WhereUSER_NO { get; set; }

       
        /// <summary>
        /// 借書日期(起)
        /// </summary>
        public string WhereBORROW_DATES { get; set; }

        /// <summary>
        /// 借書日期(迄)
        /// </summary>
        public string WhereBORROW_DATEE { get; set; }

        /// <summary>
        /// 分類編號
        /// </summary>
        public string WhereBK_GRP { get; set; }


        public string WhereSEYEAR { get; set; }


        public bool IsRepeatBook { get; set; }


        /// <summary>
        /// 排序欄位
        /// </summary>
        public string OrdercColumn { get; set; }

        /// <summary>
        /// Desc or ASC
        /// </summary>
        public string SyntaxName { get; set; }

        /// <summary>
        /// 頁次
        /// </summary>
        public int Page { get; set; }

        /// <summary>
        /// 一頁筆數 預設筆數
        /// </summary>
        public int PageSize { get; set; }

        /// <summary>
        /// 查詢結果
        /// </summary>
        public IPagedList<SECI05BorrowListViewModel> ListData;

        public SECI05BorrowListIndexViewModel()
        {
            PageSize = int.MaxValue;
            IsRepeatBook = false;
        }

    }
}