﻿@model EcoolWeb.Models.AWAI02IndexViewModel
@{
    ViewBag.Title = "角色娃娃-角色娃娃清單";
    string ImageUrl = Url.Content(@"~/Content/Players/");

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    if (string.IsNullOrWhiteSpace(UUID) == false)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

<a class="btn btn-sm btn-sys" href='@Url.Action("Create", "AWAI02")'>
    新增角色娃娃
</a>

<img src="~/Content/img/web-bar2-revise-18.png" style="width:100%" class="img-responsive App_hide" alt="Responsive image" />
<div class="Div-EZ-AWAI02">
    <div  class="Details">
        <div class="container">
            <div class="row show-grid" style="white-space: nowrap;">

                @foreach (AWAT06 player in Model.PlayerList)
                {
                    string PlayerImageUrl = ImageUrl + player.IMG_FILE;
                    <div class="col-xs-6 col-sm-4">
                        <img src='@PlayerImageUrl' class="img-responsive" alt="Responsive image" />
                        <img src="~/Content/img/web-student-prize-13.png" />
                        <text> 名稱：</text>
                        @player.PLAYER_NAME
                        <br />
                        <img src="~/Content/img/web-student-prize-13.png" />
                        <text>兌換點數：</text>
                        @player.COST_CASH
                        <br />
                        <img src="~/Content/img/web-student-prize-13.png" />
                        <text>開始日期：</text>
                        @player.SDATETIME.Value.ToShortDateString()
                        <br /><br />
                        @Html.ActionLink("【修改】", "Edit", new { PLAYER_NO = player.PLAYER_NO }, new { @class = "btn-default btn btn-xs" })
                    </div>
                }
            </div>
        </div>
    </div>
</div>


