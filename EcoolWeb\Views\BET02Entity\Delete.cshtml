﻿@model ECOOL_APP.EF.BET02

@{
    ViewBag.Title = "Delete";
}

<h2>Delete</h2>

<h3>Are you sure you want to delete this?</h3>
<div>
    <h4>BET02</h4>
    <hr />
    <dl class="dl-horizontal">
        <dt>
            @Html.DisplayNameFor(model => model.SCHOOL_NO)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.SCHOOL_NO)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.CLASS_TYPE)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.CLASS_TYPE)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.SUBJECT)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.SUBJECT)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.CONTENT_TXT)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.CONTENT_TXT)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.ISPUBLISH)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.ISPUBLISH)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.S_DATE)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.S_DATE)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.E_DATE)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.E_DATE)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.TOP_YN)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.TOP_YN)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.CRE_PERSON)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.CRE_PERSON)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.CRE_DATE)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.CRE_DATE)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.CHG_PERSON)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.CHG_PERSON)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.CHG_DATE)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.CHG_DATE)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.MEMO)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.MEMO)
        </dd>

    </dl>

    @using (Html.BeginForm()) {
        @Html.AntiForgeryToken()

        <div class="form-actions no-color">
            <input type="submit" value="Delete" class="btn btn-default" /> |
            @Html.ActionLink("Back to List", "Index")
        </div>
    }
</div>
