﻿using ECOOL_APP;
using ECOOL_APP.com.ecool.Models.dao;
using ECOOL_APP.com.ecool.Models.entity;
using ECOOL_APP.EF;
using System;
using System.Collections.Generic;
using System.Data;
using System.Text;
using System.Web.Mvc;
using System.Linq;

namespace com.ecool.service
{
    public class ADDTService : ServiceBase
    {
        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();

        /// <summary>
        /// 小小讀書人的畫面 PageList 使用
        /// </summary>
        /// <returns></returns>
        public static List<uADDT06> USP_ADDTPendingDetail_QUERY(int APPLY_NO)
        {
            List<uADDT06> list_data = new List<uADDT06>();
            StringBuilder sb = new StringBuilder();
            DataTable dt;
            try
            {
                sb.Append(" SELECT CLASS_NO,SYEAR,SEAT_NO,NAME,PASSPORT_YN,BOOK_NAME,REVIE<PERSON>,SHARE_YN ");
                sb.Append(" FROM ADDT06 ");
                sb.Append(" WHERE 1 = 1 ");
                if (APPLY_NO != 0)
                {
                    sb.Append(" AND APPLY_NO = '" + APPLY_NO + "'");
                }

                dt = new sqlConnection.sqlConnection().executeQueryByDataTableList(sb.ToString());
                foreach (DataRow dr in dt.Rows)
                {
                    list_data.Add(new uADDT06()
                    {
                        CLASS_NO = dr["CLASS_NO"].ToString(),
                        SYEAR = Convert.ToInt32(dr["SYEAR"].ToString()),
                        SEAT_NO = dr["SEAT_NO"].ToString(),
                        NAME = dr["NAME"].ToString(),
                        PASSPORT_YN = dr["PASSPORT_YN"].ToString(),
                        BOOK_NAME = dr["BOOK_NAME"].ToString(),
                        REVIEW = dr["REVIEW"].ToString(),
                        SHARE_YN = dr["SHARE_YN"].ToString()
                    });
                }
            }
            catch (Exception exception)
            {
                throw exception;
            }

            return list_data;
        }

        /// <summary>
        /// 小小讀書人的畫面 PageList 使用
        /// </summary>
        /// <param name="sidx">排序欄位名稱</param>
        /// <param name="sord">排序方式</param>
        /// <param name="page">目前頁數</param>
        /// <param name="pageSize">一頁幾筆</param>
        /// <returns></returns>
        public static List<ADDTViewModel> USP_Old_ADDTVIEWMODEL_QUERY(FormCollection collection, UserProfile User)
        {
            List<ADDTViewModel> list_data = new List<ADDTViewModel>();
            StringBuilder sb = new StringBuilder();
            DataTable dt;
            try
            {
                sb.Append(" SELECT RIGHT('0'+CONVERT(VARCHAR(3), DATEPART(year,getdate())-1911), 3) AS SYEAR, ");
                sb.Append("        CLASS_NO,SEAT_NO,NAME,BOOK_QTY,LEVEL_DESC,UP_DATE ");
                sb.Append(" FROM HRMT01 H01 ");
                sb.Append("      LEFT JOIN ADDT09 A09 ON H01.SCHOOL_NO=A09.SCHOOL_NO AND H01.USER_NO = A09.USER_NO ");
                sb.Append("      LEFT JOIN ADDT08 A08 ON A08.SCHOOL_NO=A09.SCHOOL_NO AND A08.LEVEL_ID = A09.LEVEL_ID ");
                sb.Append(" WHERE 1 = 1 ");
                sb.Append(" AND BOOK_QTY IS NOT NULL AND LEVEL_DESC IS NOT NULL AND UP_DATE IS NOT NULL ");

                dt = new sqlConnection.sqlConnection().executeQueryByDataTableList(sb.ToString());
                foreach (DataRow dr in dt.Rows)
                {
                    list_data.Add(new ADDTViewModel()
                    {
                        SYEAR = dr["SYEAR"].ToString(),
                        CLASS_NO = dr["CLASS_NO"].ToString(),
                        SEAT_NO = dr["SEAT_NO"].ToString(),
                        USERNAME = dr["NAME"].ToString(),
                        BOOK_QTY = Convert.ToInt32(dr["BOOK_QTY"].ToString()),
                        LEVEL_DESC = dr["LEVEL_DESC"].ToString(),
                        UP_DATE = Convert.ToDateTime(dr["UP_DATE"].ToString()),
                    });
                }
            }
            catch (Exception exception)
            {
                throw exception;
            }

            return list_data;
        }

        /// <summary>
        /// 閱讀認證通過資料處理
        /// </summary>
        /// <param name="APPLY_STATUS"></param>
        /// <param name="APPLY_NO"></param>
        /// <param name="txtARTICLE"></param>
        /// <param name="SHARE_YN"></param>
        public static void CheckPendingDetailEDIT(string APPLY_STATUS, int APPLY_NO, string txtARTICLE, string SHARE_YN, UserProfile user)
        {
            string strStatusMsg = string.Empty;
            string BODY_TXT = string.Empty;
            //產是要 Push 批號
            string BATCH_ID = PushService.CreBATCH_ID();

            int iADDT03Count = 0;     //目前年級閱讀護照需完成數
            int iADDT05Count = 0;     //目前年級閱讀護照已完成筆

            short iBOOKGRADE = 0; //目前讀取的書本年級
            List<ADDT09> liChildReadLevel = new List<ADDT09>();
            //取得學生閱讀書籍的基本資料

            ECOOL_DEVEntities db = new ECOOL_DEVEntities();
            List<Tuple<string, string, int>> valuesList = new List<Tuple<string, string, int>>();

            //step 2 批閱畫面會帶出學生的心得，老師直接在畫面上改，確認送出後直接取畫面老師批閱後的資料更新回ADDT06
            // strStatusMsg = new ADDTService().USP_EDIT_ADDT06(APPLY_NO, txtARTICLE, Mode, SHARE_YN);

            ADDT06 a6 = db.ADDT06.Find(APPLY_NO);
            if (a6 == null) return;

            a6.APPLY_STATUS = APPLY_STATUS;

            a6.SHARE_YN = SHARE_YN;
            a6.REVIEW_VERIFY = txtARTICLE;

            //step 3 更新ADDT09學生閱讀認證等級
            //判斷ADDT09是否有該學生資料，若有則更新閱讀本數+1，若無則INSERT 閱讀本數 = 1
            ADDT09 a9 = db.ADDT09.Where(p => p.USER_NO == a6.USER_NO && p.SCHOOL_NO == a6.SCHOOL_NO).FirstOrDefault();
            if (a9 == null)
            {
                a9 = db.ADDT09.Create();
                a9.SCHOOL_NO = a6.SCHOOL_NO;
                a9.USER_NO = a6.USER_NO;
                a9.LEVEL_ID = 0;
                a9.LEVEL_QTY = 0;
                a9.BOOK_QTY = 1;
                db.ADDT09.Add(a9);
            }
            else
            {
                a9.BOOK_QTY = a9.BOOK_QTY + 1;

                //如果學生閱讀通過冊數符合ADDT07)閱讀認證等級設定檔即升級，並將升級資料寫入ADDT09_HIS、AWAT01帳號酷幣點數(參考ADDT07.TO_CASH)、AWAT01_LOG帳號酷幣點數紀錄
                ADDT07 a7 = db.ADDT07.Where(p => p.LEVEL_QTY == a9.BOOK_QTY).FirstOrDefault();
                if (a7 != null)
                {
                    //升等
                    a9.LEVEL_ID = a7.LEVEL_ID;
                    a9.LEVEL_QTY = a7.LEVEL_QTY;
                    a9.UP_DATE = DateTime.Now;

                    //升等歷史
                    ADDT09_HIS a9h = db.ADDT09_HIS.Create();
                    a9h.SCHOOL_NO = a9.SCHOOL_NO;
                    a9h.USER_NO = a9.USER_NO;
                    a9h.BOOK_QTY = a9.BOOK_QTY;
                    a9h.LEVEL_ID = a9.LEVEL_ID.Value;
                    a9h.LEVEL_QTY = a9.LEVEL_QTY.Value;
                    a9h.UP_DATE = a9.UP_DATE;

                    a9h.CLASS_NO = a6.CLASS_NO;
                    a9h.SEAT_NO = a6.SEAT_NO;
                    a9h.NAME = a6.NAME;
                    a9h.SNAME = a6.SNAME;
                    a9h.SYEAR = a6.SYEAR;
                    a9h.SEMESTER = a6.SEMESTER;

                    a9h.STATUS = 0;
                    a9h.CASH_YN = "N";

                    db.ADDT09_HIS.Add(a9h);

                    BODY_TXT = "閱讀認證升級，升為等級" + a9.LEVEL_ID.ToString();

                    PushService.InsertPushDataParents(BATCH_ID, a9.SCHOOL_NO, a9.USER_NO, "", BODY_TXT, "", "ADDT", "CheckPendingDetailEDIT", APPLY_NO.ToString(), "ADDT/PrizeList?USER_NO=" + a9.USER_NO.ToString(), false, ref db);
                    PushService.InsertPushDataMe(BATCH_ID, a9.SCHOOL_NO, a9.USER_NO, "", BODY_TXT, "", "ADDT", "CheckPendingDetailEDIT", APPLY_NO.ToString(), "ADDT/PrizeList?USER_NO=" + a9.USER_NO, false, ref db);
                }
            }

            int iCoolCash = 5;//閱讀一本書的酷幣點數
            if (a6.SHARE_YN == "y" || a6.SHARE_YN == "Y") iCoolCash = iCoolCash + 5;
            CashHelper.AddCash(user, iCoolCash, a6.SCHOOL_NO,
                                    a6.USER_NO, "ADDT", APPLY_NO.ToString(), true, ref db,ref valuesList);

            BODY_TXT = "閱讀認證通過，獲得酷幣" + iCoolCash.ToString() + "點";

            PushService.InsertPushDataParents(BATCH_ID, a6.SCHOOL_NO, a6.USER_NO, "", BODY_TXT, "", "ADDT", "CheckPendingDetailEDIT", APPLY_NO.ToString(), null, false, ref db);
            PushService.InsertPushDataMe(BATCH_ID, a6.SCHOOL_NO, a6.USER_NO, "", BODY_TXT, "", "ADDT", "CheckPendingDetailEDIT", APPLY_NO.ToString(), null, false, ref db);

            if (a6.PASSPORT_YN == "Y")
            {
                //strStatusMsg = new ADDTService().USP_ADD_ADDT05(APPLY_NO, a6);
                //如果學生已通過該護照的指定編號，就不用再塞一次小蘋果，以免PK重複
                bool HadBookID = db.ADDT05.Where(p => p.SCHOOL_NO == a6.SCHOOL_NO && p.USER_NO == a6.USER_NO && p.BOOK_ID == a6.BOOK_ID).Any();
                if (HadBookID == false)
                {
                    iBOOKGRADE = Convert.ToInt16(new ECOOL_APP.com.ecool.util.StringHelper().StrLeft(a6.BOOK_ID, 1));

                    ADDT05 a5 = new ADDT05();
                    a5.SCHOOL_NO = a6.SCHOOL_NO;
                    a5.USER_NO = a6.USER_NO;
                    a5.BOOK_ID = a6.BOOK_ID;
                    a5.BOOK_NAME = a6.BOOK_NAME;
                    a5.GRADE = iBOOKGRADE;
                    a5.VERIFIED_DATE = DateTime.Now;
                    a5.APPLY_NO = a6.APPLY_NO;
                    db.ADDT05.Add(a5);
                   // iADDT03Count = db.ADDT03.Where(p => p.SCHOOL_NO == a6.SCHOOL_NO && p.GRADE == iBOOKGRADE ).Count();     //目前年級閱讀護照需完成數
                  iADDT03Count = db.ADDT03.Where(p => p.SCHOOL_NO == a6.SCHOOL_NO && p.GRADE == iBOOKGRADE && p.BOOK_NAME!="").Count();     //目前年級閱讀護照需完成數
                    iADDT05Count = db.ADDT05.Where(p => p.SCHOOL_NO == a6.SCHOOL_NO && p.USER_NO == a6.USER_NO && p.GRADE == (short)iBOOKGRADE).Count() + db.ADDT05.Local.Count(); //目前年級閱讀護照已完成筆

                    //如果完成讀書護照則小小讀書人升級
                    if (iADDT05Count >= iADDT03Count && iADDT03Count>0)
                    {
                        // strStatusMsg = new ADDTService().USP_ADD_ADDT04(a6);

                        ADDT04 a4 = db.ADDT04.Create();
                        a4.SCHOOL_NO = a6.SCHOOL_NO;
                        a4.USER_NO = a6.USER_NO;
                        a4.PASS_YN = "Y";
                        a4.GRADE = iBOOKGRADE;
                        a4.PASS_DATE = DateTime.Now;
                        db.ADDT04.Add(a4);

                        BODY_TXT = "閱讀護照完成，護照年級為" + iBOOKGRADE.ToString();

                        PushService.InsertPushDataParents(BATCH_ID, a9.SCHOOL_NO, a9.USER_NO, "", BODY_TXT, "", "ADDT", "CheckPendingDetailEDIT", APPLY_NO.ToString(), "", false, ref db);
                        PushService.InsertPushDataMe(BATCH_ID, a9.SCHOOL_NO, a9.USER_NO, "", BODY_TXT, "", "ADDT", "CheckPendingDetailEDIT", APPLY_NO.ToString(), "", false, ref db);
                    }
                }
            }

            try
            {
                db.SaveChanges();
            }
            catch (System.Data.Entity.Validation.DbEntityValidationException dbEx)
            {
                Exception raise = dbEx;
                foreach (var validationErrors in dbEx.EntityValidationErrors)
                {
                    foreach (var validationError in validationErrors.ValidationErrors)
                    {
                        string message = string.Format("{0}:{1}",
                            validationErrors.Entry.Entity.ToString(),
                            validationError.ErrorMessage);
                        // raise a new exception nesting
                        // the current instance as InnerException
                        raise = new InvalidOperationException(message, raise);
                    }
                }
                throw raise;
            }
           
            //Push
            PushHelper.ToPushServer(BATCH_ID);
        }

        //public static void CheckPendingDetailSEDIT(string Mode, int APPLY_NO, string txtARTICLE, string SHARE_YN)
        //{
        //    string strStatusMsg = string.Empty;
        //    strStatusMsg = new ADDTService().USP_SEDIT_ADDT06(APPLY_NO, txtARTICLE, Mode, SHARE_YN);
        //}
        /// <summary>
        /// 檢查閱讀單通過後是否可作廢
        /// </summary>
        /// <param name="APPLY_NO"></param>
        /// <param name="Msg"></param>
        /// <returns></returns>
        public static bool Check_PASS_DEL(int? APPLY_NO, out string Msg, ref ECOOL_DEVEntities db)
        {
            bool ReturnVal = true;

            if (APPLY_NO == null)
            {
                ReturnVal = false;
                Msg = "未傳入閱讀單號，請確認!!";
                return ReturnVal;
            }

            ADDT06 T06 = db.ADDT06.Where(a => a.APPLY_NO == APPLY_NO).FirstOrDefault();
            if (T06 == null)
            {
                ReturnVal = false;
                Msg = "查無此閱讀單，請確認!!";
                return ReturnVal;
            }

            if (T06.APPLY_STATUS != ADDT06.APPLY_STATUS_Val.APPLY_TYPE_Pass)
            {
                ReturnVal = false;
                Msg = "此閱讀單狀態非「通過」狀態，請確認!!";
                return ReturnVal;
            }

            //判斷認證是否升級
            ADDT09 a9 = db.ADDT09.Where(p => p.USER_NO == T06.USER_NO && p.SCHOOL_NO == T06.SCHOOL_NO).FirstOrDefault();
            if (a9 != null)
            {
                ADDT07 T07 = db.ADDT07.Where(a => a.LEVEL_QTY == a9.BOOK_QTY).FirstOrDefault();

                if (T07 != null)
                {
                    ReturnVal = false;
                    Msg = "此學生「閱讀認證」剛好升級，無法作廢!!";
                    return ReturnVal;
                }
            }

            //判斷護照是否升級
            if (T06.PASSPORT_YN == "Y")
            {
                if (T06.BOOK_ID == null)
                {
                    ReturnVal = false;
                    Msg = "異常，此為閱讀護照，但無此BOOK_ID，請連絡系統人員!!";
                    return ReturnVal;
                }

                short ThisBOOKGRADE = Convert.ToInt16(new ECOOL_APP.com.ecool.util.StringHelper().StrLeft(T06.BOOK_ID, 1));

                int ADDT03Count = db.ADDT03.Where(p => p.SCHOOL_NO == T06.SCHOOL_NO && p.GRADE == ThisBOOKGRADE).Count();     //目前年級閱讀護照需完成數
                int ADDT05Count = db.ADDT05.Where(p => p.SCHOOL_NO == T06.SCHOOL_NO && p.USER_NO == T06.USER_NO && p.GRADE == ThisBOOKGRADE).Count(); //此學生這 年級閱讀護照已完成筆數

                if (ADDT05Count == ADDT03Count)
                {
                    ReturnVal = false;
                    Msg = "此學生「閱讀護照」剛好升級，無法作廢!!";
                    return ReturnVal;
                }
            }

            Msg = string.Empty;
            return ReturnVal;
        }

        /// <summary>
        /// 閱讀單通過後作廢資料處理
        /// </summary>
        /// <param name="a06"></param>
        /// <param name="ErrorMsg"></param>
        /// <returns></returns>
        public static bool UpData_PASS_DEL(ADDT06 a06, out string ErrorMsg, ref ECOOL_DEVEntities db, UserProfile user, ref List<Tuple<string, string, int>> valuesList)
        {
            bool ReturnVal = false;
            ErrorMsg = string.Empty;
            string BATCH_ID = PushService.CreBATCH_ID();
            a06.APPLY_STATUS = ADDT06.APPLY_STATUS_Val.APPLY_TYPE_DEL;

            //扣認證書本數
            ADDT09 a9 = db.ADDT09.Where(p => p.USER_NO == a06.USER_NO && p.SCHOOL_NO == a06.SCHOOL_NO).FirstOrDefault();
            if (a9 != null)
            {
                a9.BOOK_QTY = a9.BOOK_QTY - 1;
            }

            //刪除護照
            if (a06.PASSPORT_YN == "Y")
            {
                ADDT05 a05 = db.ADDT05.Where(A => A.SCHOOL_NO == a06.SCHOOL_NO && A.USER_NO == a06.USER_NO && A.BOOK_ID == a06.BOOK_ID && A.BOOK_NAME == a06.BOOK_NAME).FirstOrDefault();
                if (a05 != null)
                {
                    db.ADDT05.Remove(a05);
                }
            }

            int iCoolCash = -5;//閱讀一本書的酷幣點數
            if (a06.SHARE_YN == "y" || a06.SHARE_YN == "Y") iCoolCash = iCoolCash - 5;

            AWAT01_LOG log = db.AWAT01_LOG.Where(A => A.SCHOOL_NO == a06.SCHOOL_NO && A.USER_NO == a06.USER_NO && A.SOURCE_TYPE == "ADDT" && A.SOURCE_NO == a06.APPLY_NO.ToString()).FirstOrDefault();
            if (log != null)
            {
                iCoolCash = ((int)log.CASH_IN) * (-1);
            }

            CashHelper.AddCash(user, iCoolCash, a06.SCHOOL_NO,
                                    a06.USER_NO, "ADDT", a06.APPLY_NO.ToString(), "閱讀認證作廢", true, ref db,"","",ref valuesList);

            string BODY_TXT = "閱讀認證作廢，書名: " + a06.BOOK_NAME + "、減少酷幣點數" + (iCoolCash * (-1)).ToString() + "數";

            //PushService.InsertPushDataParents(BATCH_ID, a06.SCHOOL_NO, a06.USER_NO, "", BODY_TXT, "", "ADDT", "UpData_PASS_DEL", a06.APPLY_NO.ToString(), "", false, ref db);
            PushService.InsertPushDataMe(BATCH_ID, a06.SCHOOL_NO, a06.USER_NO, "", BODY_TXT, "", "ADDT", "UpData_PASS_DEL", a06.APPLY_NO.ToString(), "", false, ref db);
            a06.SHARE_YN = "N";
            try
            {
                db.SaveChanges();
                ReturnVal = true;
            }
            catch (System.Data.Entity.Validation.DbEntityValidationException dbEx)
            {
                foreach (var validationErrors in dbEx.EntityValidationErrors)
                {
                    foreach (var validationError in validationErrors.ValidationErrors)
                    {
                        string message = string.Format("{0}:{1}",
                            validationErrors.Entry.Entity.ToString(),
                            validationError.ErrorMessage);

                        ErrorMsg = ErrorMsg + message;
                    }
                }
            }

            PushHelper.ToPushServer(BATCH_ID);
            return ReturnVal;
        }

        /// <summary>
        /// 閱讀認證待審核項目畫面
        /// </summary>
        /// <param name="sidx">排序欄位名稱</param>
        /// <param name="sord">排序方式</param>
        /// <param name="page">目前頁數</param>
        /// <param name="pageSize">一頁幾筆</param>
        /// <returns></returns>
        public DataTable USP_ADDTPendingVIEWMODEL_QUERY(string sidx, string sord, int page, int pageSize)
        {
            DataTable dt = new DataTable();
            StringBuilder sb = new StringBuilder();
            try
            {
                int startIndex = ((page - 1) * pageSize) + 1;
                int endIndex = page * pageSize;
                sb.Append(" WITH PAGED_ADDT AS ");
                sb.Append(" ( ");
                sb.Append("     SELECT CONVERT(varchar(12), CRE_DATE, 111 ) as CRE_DATE,SYEAR,CASE SEMESTER WHEN 1 THEN'上' WHEN 2 THEN'下' END AS CSEMESTER,SEMESTER,A.CLASS_NO,SEAT_NO,NAME,BOOK_NAME,APPLY_NO , ");
                sb.Append("            ROW_NUMBER() OVER (ORDER BY " + sidx + @" " + sord + @") AS ADDT_NO  ");
                sb.Append("     FROM ADDT06 A (NOLOCK) ");
                sb.Append("     INNER JOIN HRMT03 B (NOLOCK) ON A.CLASS_NO =B.CLASS_NO ");
                sb.Append("     WHERE APPLY_STATUS='0' ");
                sb.Append(" ) ");
                sb.Append(" SELECT  CONVERT(varchar(12), CRE_DATE, 111 ) as CRE_DATE,SYEAR,CSEMESTER,CLASS_NO,SEAT_NO,NAME,BOOK_NAME,APPLY_NO ,ADDT_NO ");
                sb.Append(" FROM PAGED_ADDT ");
                sb.Append(" WHERE ADDT_NO BETWEEN " + startIndex + @" AND " + endIndex + @";");
                dt = new sqlConnection.sqlConnection().executeQueryByDataTableList(sb.ToString());
            }
            catch (Exception exception)
            {
                throw exception;
            }
            return dt;
        }

        /// <summary>
        /// 小小讀書人的畫面
        /// </summary>
        /// <param name="sidx">排序欄位名稱</param>
        /// <param name="sord">排序方式</param>
        /// <param name="page">目前頁數</param>
        /// <param name="pageSize">一頁幾筆</param>
        /// <returns></returns>
        public DataTable USP_ADDTVIEWMODEL_QUERY(string sidx, string sord, int page, int pageSize)
        {
            DataTable dt = new DataTable();
            StringBuilder sb = new StringBuilder();
            try
            {
                int startIndex = ((page - 1) * pageSize) + 1;
                int endIndex = page * pageSize;
                sb.Append(" WITH PAGED_ADDT AS ");
                sb.Append(" ( ");
                //sb.Append("     SELECT RIGHT('0'+CONVERT(VARCHAR(3), DATEPART(year,getdate())-1911), 3) AS SYEAR, ");
                sb.Append("     SELECT  ");
                sb.Append("            CLASS_NO,SEAT_NO,SNAME,NAME,BOOK_QTY,LEVEL_DESC, CONVERT(varchar(12), UP_DATE, 111 ) AS UP_DATE,A09.USER_NO, ");
                sb.Append("            ROW_NUMBER() OVER (ORDER BY " + sidx + @" " + sord + @") AS ADDT_NO  ");
                sb.Append("     FROM HRMT01 H01    ");
                sb.Append("          LEFT JOIN ADDT09 A09 ON H01.SCHOOL_NO=A09.SCHOOL_NO AND H01.USER_NO = A09.USER_NO   ");
                sb.Append("          LEFT JOIN ADDT08 A08 ON A08.SCHOOL_NO=A09.SCHOOL_NO AND A08.LEVEL_ID = A09.LEVEL_ID ");
                sb.Append("     WHERE 1 = 1  AND BOOK_QTY IS NOT NULL AND LEVEL_DESC IS NOT NULL AND UP_DATE IS NOT NULL ");
                sb.Append(" ) ");
                //sb.Append(" SELECT RIGHT('0'+CONVERT(VARCHAR(3), DATEPART(year,getdate())-1911), 3) AS SYEAR, ");
                sb.Append(" SELECT  ");
                sb.Append("        CLASS_NO,SEAT_NO,SNAME,BOOK_QTY,LEVEL_DESC,CONVERT(varchar(12), UP_DATE, 111 )  AS UP_DATE,USER_NO  ");
                sb.Append(" FROM PAGED_ADDT  ");
                sb.Append(" WHERE ADDT_NO BETWEEN " + startIndex + @" AND " + endIndex + @";");
                dt = new sqlConnection.sqlConnection().executeQueryByDataTableList(sb.ToString());
            }
            catch (Exception exception)
            {
                throw exception;
            }
            return dt;
        }

        //public bool USP_ADDTApplyInsert_INSERT(ADDTInsertViewModel ADDModel, out string Msg)
        //{
        //    string strMsg = string.Empty;
        //    try
        //    {
        //        string BookName = ADDModel.BOOK_NAME.Replace("'","''");
        //        string sql =
        //            " INSERT INTO ADDT06(SCHOOL_NO, " +
        //             "        USER_NO, CLASS_NO, SYEAR, " +
        //             "        SEMESTER, SEAT_NO, NAME, SNAME,  " +
        //             "        PASSPORT_YN, BOOK_ID, BOOK_NAME, " +
        //             "        REVIEW, IMG_FILE, APPLY_TYPE, APPLY_STATUS, CRE_DATE ) " +
        //             " VALUES ('" + ADDModel.SCHOOL_NO + "', '" + ADDModel.USER_NO + "', '" + ADDModel.CLASS_NO + "', '" +
        //                            ADDModel.SYEAR + "', '" + ADDModel.SEMESTER + "', '" + ADDModel.SEAT_NO + "', N'" +
        //                            ADDModel.NAME + "', N'" + ADDModel.SNAME + "', '" + ADDModel.PASSPORT_YN + "', '" +
        //                            ADDModel.BOOK_ID + "', N'" + BookName + "', '" + ADDModel.REVIEW + "', '" +
        //                            ADDModel.IMG_FILE + "', '" + ADDModel.APPLY_TYPE + "', '" + ADDModel.APPLY_STATUS + "', GETDATE())";
        //        new sqlConnection.sqlConnection().execute(sql);
        //        Msg = "心得新增成功";
        //        return true;
        //    }
        //    catch (Exception exception)
        //    {
        //        Msg = "心得新增失敗\n"+ exception.Message;
        //        return false;
        //    }

        //}

        //public string USP_ADD_ADDT04(ADDT06 ChildReadBookData)
        //{
        //    string strMsg = string.Empty;
        //    try
        //    {
        //        StringBuilder sb = new StringBuilder();
        //        sb.Append(" INSERT INTO ADDT04(SCHOOL_NO,USER_NO,PASS_YN,GRADE,PASS_DATE) ");
        //        sb.AppendFormat("  VALUES ('{0}','{1}','Y',LEFT({2} ,1),GETDATE()) ",
        //                        ChildReadBookData.SCHOOL_NO, ChildReadBookData.USER_NO,
        //                        ChildReadBookData.BOOK_ID);
        //        new sqlConnection.sqlConnection().execute(sb.ToString());
        //    }
        //    catch (Exception exception)
        //    {
        //        throw exception;
        //        //strMsg = "心得新增失敗";
        //    }
        //    return strMsg;
        //}

        //public string USP_ADD_ADDT05(int APPLY_NO, ADDT06 ChildReadBookData)
        //{
        //    string strMsg = string.Empty;
        //    try
        //    {
        //        string BookName = ChildReadBookData.BOOK_NAME.Replace("'", "''");
        //        StringBuilder sb = new StringBuilder();
        //        sb.Append(" INSERT INTO ADDT05(SCHOOL_NO,USER_NO,BOOK_ID,BOOK_NAME,GRADE,VERIFIED_DATE,APPLY_NO) ");
        //        sb.AppendFormat("  VALUES ('{0}','{1}','{2}',N'{3}',LEFT({4} ,1),GETDATE(),{5} ) ",
        //                        ChildReadBookData.SCHOOL_NO, ChildReadBookData.USER_NO,
        //                        ChildReadBookData.BOOK_ID, BookName,
        //                        ChildReadBookData.BOOK_ID, APPLY_NO);

        //        new sqlConnection.sqlConnection().execute(sb.ToString());
        //    }
        //    catch (Exception exception)
        //    {
        //        throw exception;
        //        //strMsg = "心得新增失敗";
        //    }
        //    return strMsg;
        //}

        //public string USP_SEDIT_ADDT06(int APPLY_NO, string REVIEW, string APPLY_STATUS, string SHARE_YN)
        //{
        //    string strMsg = string.Empty;
        //    try
        //    {
        //        new sqlConnection.sqlConnection().execute(" UPDATE ADDT06 " +
        //                                                  " SET " +
        //                                                  " APPLY_STATUS= '0', " +
        //                                                  " SHARE_YN= '" + SHARE_YN + "', " +
        //                                                  " REVIEW= '" + REVIEW + "'" +
        //                                                  " WHERE APPLY_NO= " + APPLY_NO);
        //        strMsg = "心得批閱成功";
        //    }
        //    catch (Exception exception)
        //    {
        //        throw exception;
        //        //strMsg = "心得新增失敗";
        //    }
        //    return strMsg;
        //}

        //public string USP_EDIT_ADDT06(int APPLY_NO, string REVIEW_VERIFY, string APPLY_STATUS, string SHARE_YN)
        //{
        //    string strMsg = string.Empty;

        //    REVIEW_VERIFY = REVIEW_VERIFY.Replace("'", "''");
        //    try
        //    {
        //        new sqlConnection.sqlConnection().execute(" UPDATE ADDT06 " +
        //                                                  " SET " +
        //                                                  " APPLY_STATUS= '" + APPLY_STATUS + "', " +
        //                                                  " SHARE_YN= '" + SHARE_YN + "', " +
        //                                                  " REVIEW_VERIFY= '" + REVIEW_VERIFY + "'" +
        //                                                  " WHERE APPLY_NO= " + APPLY_NO);
        //        strMsg = "心得批閱成功";
        //    }
        //    catch (Exception exception)
        //    {
        //        throw exception;
        //        //strMsg = "心得新增失敗";
        //    }
        //    return strMsg;
        //}

        //public string USP_ADD_ADDT09(int SCHOOL_NO, string USER_NO)
        //{
        //    string strMsg = string.Empty;
        //    try
        //    {
        //        new sqlConnection.sqlConnection().execute(" INSERT INTO ADDT09    " +
        //                                                  " ( " +
        //                                                  " SCHOOL_NO,USER_NO,LEVEL_ID,LEVEL_QTY,BOOK_QTY " +
        //                                                  " ) " +
        //                                                  " VALUES " +
        //                                                  " ( " + SCHOOL_NO + ",'" + USER_NO + "',0,0,1)");
        //        strMsg = "心得批閱成功";
        //    }
        //    catch (Exception exception)
        //    {
        //        throw exception;
        //        //strMsg = "心得新增失敗";
        //    }
        //    return strMsg;
        //}

        //public string USP_ADDTPendingDetailEdit(string Mode, int APPLY_NO, string txtARTICLE)
        //{
        //    try
        //    {
        //        ADDTSPDAO USP = new ADDTSPDAO();
        //        return USP.ADDTPendingDetail(Mode, APPLY_NO, txtARTICLE);

        //    }
        //    catch (Exception exception)
        //    {
        //        throw exception;
        //    }
        //}
    }
}