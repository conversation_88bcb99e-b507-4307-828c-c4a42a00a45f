﻿@model ECOOL_APP.EF.ADDT15

@{
    ViewBag.Title = "校外榮譽-修改校外榮譽內容";
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@using (Html.BeginForm("MODIFY1", "ADDI07", FormMethod.Post, new { name = "ADDI07Form", enctype = "multipart/form-data" }))
{
    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })
    @Html.HiddenFor(model => model.OAWARD_ID)
    @Html.HiddenFor(model => model.SCHOOL_NO)
    @Html.HiddenFor(model => model.USER_NO)

    <img src="~/Content/img/web-bar2-revise-11.png" style="width:100%" class="img-responsive " alt="Responsive image" />
    <div class="Div-EZ-ADDI07">
        <div class="form-horizontal">
            <div class="form-group">
                @Html.LabelFor(model => model.CLASS_NO, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.DisplayFor(model => model.CLASS_NO, new { htmlAttributes = new { @class = "form-control", disabled = "disabled" } })
                    @Html.HiddenFor(model => model.CLASS_NO)
                    @Html.ValidationMessageFor(model => model.CLASS_NO, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.USERNAME, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.DisplayFor(model => model.USERNAME, new { htmlAttributes = new { @class = "form-control", disabled = "disabled" } })
                    @Html.HiddenFor(model => model.USERNAME)
                    @Html.ValidationMessageFor(model => model.USERNAME, "", new { @class = "text-danger" })
                </div>
            </div>


            <div class="form-group">
                @Html.LabelFor(model => model.OAWARD_ITEM, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.DisplayFor(model => model.OAWARD_ITEM, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.OAWARD_ITEM, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.OAWARD_SCORE, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.DisplayFor(model => model.OAWARD_SCORE, new { htmlAttributes = new { @class = "form-control", disabled = "disabled" } })
                    @Html.HiddenFor(model => model.OAWARD_SCORE)
                    @Html.ValidationMessageFor(model => model.OAWARD_SCORE, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.CASH, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.DisplayFor(model => model.CASH, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.HiddenFor(model => model.CASH)
                    @Html.ValidationMessageFor(model => model.CASH, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.REMARK, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.DisplayFor(model => model.REMARK, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.REMARK, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.IMG_FILE, htmlAttributes: new { @class = "control-label label_dt col-md-2" })
                <div class="col-md-10">
                    <input class="btn btn-default" type="file" name="files" />
                    @Html.ValidationMessage("files", "", new { @class = "text-danger" })
                </div>
            </div>
            @if (!string.IsNullOrWhiteSpace(ViewBag.IMG_FILE))
            {
            <div class="form-group">
                @Html.Label("原上傳圖", htmlAttributes: new { @class = "control-label label_dt col-md-2" })
                <div class="col-md-10">
                    @*<img src='@ViewBag.IMG_FILE' class="img-responsive " alt="Responsive image" />*@
                    @Html.HiddenFor(m => m.IMG_FILE)
                    @{
                        string Url = ViewBag.IMG_FILE + "?v=" + DateTime.Now.ToString();
                        <img src="@Url" class="img-responsive colorbox" alt="Responsive image" href="@Url" style="max-width:300px" id="ImageA1" />
                        <div style="padding-left:15%;padding-top:2%;">
                            @Html.Action("_imagesRotateBtnView", "Comm", new { ImgURL = ViewBag.IMG_FILE, ImgURL_S = "", ImgUrl_M = "", ImgID = "ImageA1", SourceNO = "ADDT15", user_NO = user?.USER_NO, schoo_NO = user?.SCHOOL_NO , sourceID = Model.OAWARD_ID})
                        </div>}
                </div>


               
            </div>
            }


            <div class="form-group">
                <div class="col-md-offset-3 col-md-3">
                    <button value="Save" class="btn btn-default">
                        確定送出
                    </button>
                </div>
                <div class="col-md-offset-1 col-md-3">
                    <a href='@Url.Action("QUERY", "ADDI07")' class="btn btn-default">
                        放棄編輯
                    </a>

                </div>
            </div>
        </div>
    </div>

   
}

@section Scripts {
    <script type="text/javascript">
        $(function () {
            // 初始化 colorbox
            $(".colorbox").colorbox({
                opacity: 0.82,
                width: "70%",
                innerHeight: "500px",
                scrolling: true
            });

            // 初始化 tooltip
            $('[data-toggle="tooltip"]').tooltip();
        });
    </script>
}
