﻿@using ECOOL_APP.com.ecool.Models;
@model  IQueryable<ADDT0809>

@{
    ECOOL_APP.UserProfile user = new ECOOL_APP.UserProfile();

    Layout = "";

    List<string> RankStr = new List<string>();
    RankStr.Add(" number-first");
    RankStr.Add(" number-second");
    RankStr.Add(" number-third");
    var AWAT02List = Model.ToList();
}
@*<link href="~/Content/css/bootstrap.css" rel="stylesheet">
<link href="~/Content/styles/leaderboard.css" rel="stylesheet" />*@
<div class="col-md-6 leaderboard-sunlight-small">
    <strong class="title title-read text-hide">閱讀認證排行榜</strong>
    <ul class="leaderboard-box leaderboard-box-green">

        @{int i = 0;}
        @foreach (var Item in AWAT02List.Take(3).ToList())
        {
            var cssRankStr = "";
            var cssRankStrItem = "";
            cssRankStr = "leaderboard-item";
            cssRankStrItem = "text-hide number" + RankStr.Skip(i).Take(1).FirstOrDefault();

            <li class='@cssRankStr'>
                <span class='@cssRankStrItem'>@Item.BOOK_QTYRANK</span>
                <span class="class">@(Item.CLASS_NO + '班' + Item.SEAT_NO + '號')</span>
                <span class="name">@Item.SNAME</span>
                <span class="value">@Item.BOOK_QTY <small>本</small></span>
            </li>
            i++;
        }


    </ul>
    <ul class="leaderboard-list leaderboard-list-green">
        @foreach (var Item in AWAT02List.Skip(3).Take(6).ToList())
        {
            <li>
                <span class="number">@Item.BOOK_QTYRANK</span>
                <span class="class">@(Item.CLASS_NO + '班' + Item.SEAT_NO + '號')</span>
                <span class="name">@Item.SNAME</span>
                <span class="value">@Item.BOOK_QTY 本</span>
            </li>
        }

    </ul>
</div>
