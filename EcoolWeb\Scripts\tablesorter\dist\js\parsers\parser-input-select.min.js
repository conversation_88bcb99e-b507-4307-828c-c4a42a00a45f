(function(factory){if (typeof define === 'function' && define.amd){define(['jquery'], factory);} else if (typeof module === 'object' && typeof module.exports === 'object'){module.exports = factory(require('jquery'));} else {factory(jQuery);}}(function(jQuery){

/*! Parser: input & select - updated 2018-07-10 (v2.30.7) */
!function(p){"use strict";p.tablesorter.addParser({id:"inputs",is:function(){return!1},format:function(e,t,a){var r=p(a).find("input");return r.length?r.val():e},parsed:!0,type:"text"}),p.tablesorter.addParser({id:"inputs-numeric",is:function(){return!1},format:function(e,t,a){var r=p(a).find("input"),n=r.length?r.val():e,i=p.tablesorter.formatFloat((n||"").replace(/[^\w,. \-()]/g,""),t);return e&&"number"==typeof i?i:e?p.trim(e&&t.config.ignoreCase?e.toLocaleLowerCase():e):e},parsed:!0,type:"numeric"}),p.tablesorter.addParser({id:"checkbox",is:function(){return!1},format:function(e,t,a){var r=p(a),n=t.config.widgetOptions,i=n.group_checkbox?n.group_checkbox:["checked","unchecked"],s=r.find('input[type="checkbox"]'),o=s.length?s[0].checked:"";return s.length?i[o?0:1]:e},parsed:!0,type:"text"}),p.tablesorter.addParser({id:"radio",is:function(){return!1},format:function(e,t,a){var r=p(a).find("input:checked");return r.length?r.val():e},parsed:!0,type:"text"}),p.tablesorter.addParser({id:"select",is:function(){return!1},format:function(e,t,a){var r=p(a).find("select");return r.length?r.val():e},parsed:!0,type:"text"}),p.tablesorter.addParser({id:"select-text",is:function(){return!1},format:function(e,t,a){var r=p(a).find("select");return r.length?r.find("option:selected").text()||"":e},parsed:!0,type:"text"}),p.tablesorter.addParser({id:"textarea",is:function(){return!1},format:function(e,t,a){var r=p(a).find("textarea");return r.length?r.val():e},parsed:!0,type:"text"}),p.tablesorter.defaults.checkboxClass="",p.tablesorter.defaults.checkboxVisible="",p(function(){if(p.fn.on){var h=function(e,t,a,r){e.toggleClass(t+"-"+a,r),(e[0].className||"").match(t+"-")?e.addClass(t):e.removeClass(t)},u=function(e,t){if(e.length&&"INPUT"!==e[0].nodeName&&(e=e.find('input[type="checkbox"]')),e.length){var a=window.navigator.userAgent;"indeterminate"===t?(e.prop("checked",!(-1<a.indexOf("Trident/")||-1<a.indexOf("Edge/"))),e.prop("indeterminate",!0)):(e.prop("checked",t),e.prop("indeterminate",!1))}},f=function(e,r){var n,i=e.children("tbody").children(":visible"),s=i.length,t=e[0].config,a=t&&t.widgetOptions,o=t&&t.$headers.add(p(t.namespace+"_extra_headers"))||e.children("thead"),c=a&&a.$sticky;o.find('input[type="checkbox"]').each(function(){c&&(n=c.find('[data-column="'+e+'"]'));var e=p(this).closest("td, th").attr("data-column"),t=i.filter("."+r+"-"+e).length,a=t===s&&0<s;0===t||a?(u(p(this),a),n&&u(n,a)):(u(p(this),"indeterminate"),n&&u(n,"indeterminate"))})};p("table").on("tablesorter-initialized updateComplete",function(){this.tablesorterBusy=!1;var e=".parser-forms";p(this).addClass(this.config.namespace.slice(1)).children("tbody").off(e).on("mouseleave"+e,function(e){"TBODY"===e.target.nodeName&&p(":focus").blur()}).on("focus"+e,"select, input:not([type=checkbox]), textarea",function(e){var t=p(e.target).closest("tr"),a=t.closest("table")[0].config;!a||a&&a.ignoreChildRow&&t.hasClass(a.cssChildRow)||p(this).data("ts-original-value",this.value)}).on("blur"+e,"input:not([type=checkbox]), textarea",function(e){var t=p(e.target).closest("tr"),a=t.closest("table")[0].config;!a||a&&a.ignoreChildRow&&t.hasClass(a.cssChildRow)||(this.value=p(this).data("ts-original-value"))}).on("change keyup ".split(" ").join(e+" "),"select, input, textarea",function(e){var t=p(this).closest("tr"),a=t.closest("table")[0].config;if(a&&!(a&&a.ignoreChildRow&&t.hasClass(a.cssChildRow)))if(27!==e.which||"INPUT"===this.nodeName&&"checkbox"===this.type){if("change"===e.type||"keyup"===e.type&&13===e.which&&("INPUT"===e.target.nodeName||"TEXTAREA"===e.target.nodeName&&e.altKey)){var r,n=p(e.target),i="checkbox"===e.target.type,s=n.closest("td"),o=s[0].cellIndex,c=a.table.tablesorterBusy,l=a.$headerIndexed&&a.$headerIndexed[o]||[],d=i?e.target.checked:n.val();if(p.isEmptyObject(a)||!1!==c)return;if(i&&(r=a.checkboxClass||"checked",h(s.closest("tr"),r,o,d),f(a.$table,r)),l.length&&(l.hasClass("parser-false")||l.hasClass("sorter-false")&&l.hasClass("filter-false"))||"change"===e.type&&a.table.isUpdating)return;(a&&d!==n.data("ts-original-value")||i)&&(n.data("ts-original-value",d),a.table.tablesorterBusy=!0,p.tablesorter.updateCell(a,s,void 0,function(){a.$table,a.table.tablesorterBusy=!1}))}}else this.value=p(this).data("ts-original-value")}),p(this).children("thead").find('input[type="checkbox"]')&&p(this).off(e).on("tablesorter-ready"+e,function(){var e,t=p(this),a=t.length&&t[0].config;p.isEmptyObject(a)||(this.tablesorterBusy=!0,e=a&&a.checkboxClass||"checked",f(t,e),this.tablesorterBusy=!1)}).children("thead").add(this.config.widgetOptions.$sticky).off(e).on("click"+e+" change"+e,'input[type="checkbox"]',function(e){var t,a,r,n,i,s,o=p(this),c=this.checked,l=o.closest("table"),d=l.length&&l[0].className.match(/(tablesorter\w+)_extra_table/);return d&&(d=d[1],l=p("."+d+":not(."+d+"_extra_table)")),t=l.length&&l[0].config,!(!l.length||!t||l[0].tablesorterBusy)&&(r=parseInt(o.closest("td, th").attr("data-column"),10),i="checkbox"===t.parsers[r].id,a=t.checkboxVisible,l[0].tablesorterBusy=!0,u(n=l.children("tbody").children("tr"+(void 0===a||!0===a?":visible":"")).children(":nth-child("+(r+1)+")"),c),s=t.checkboxClass||"checked",n.each(function(){h(p(this).closest("tr"),s,r,c)}),d?u(l.children("thead").find('[data-column="'+r+'"]'),c):t.widgetOptions.$sticky&&u(t.widgetOptions.$sticky.find("thead").find('[data-column="'+r+'"]'),c),f(l,s),i?p.tablesorter.update(t,void 0,function(){l[0].tablesorterBusy=!1}):l[0].tablesorterBusy=!1,!0)})})}})}(jQuery);return jQuery;}));
