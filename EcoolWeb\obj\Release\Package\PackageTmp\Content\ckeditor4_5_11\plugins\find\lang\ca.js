﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'find', 'ca', {
	find: 'Cerca',
	findOptions: 'Opcions de Cerca',
	findWhat: 'Cerca el:',
	matchCase: 'Distingeix majúscules/minúscules',
	matchCyclic: 'Coincidència cíclica',
	matchWord: 'Només paraules completes',
	notFoundMsg: 'El text especificat no s\'ha trobat.',
	replace: 'Reemplaça',
	replaceAll: 'Reemplaça-ho tot',
	replaceSuccessMsg: '%1 ocurrència/es reemplaçada/es.',
	replaceWith: 'Reemplaça amb:',
	title: 'Cerca i reemplaça'
} );
