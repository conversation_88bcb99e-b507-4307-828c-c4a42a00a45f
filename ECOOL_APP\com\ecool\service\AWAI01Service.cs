﻿using Dapper;
using ECOOL_APP.com.ecool.LogicCenter;
using ECOOL_APP.EF;
using log4net;
using MvcPaging;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Data.SqlClient;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Transactions;
using System.Web;

namespace ECOOL_APP.com.ecool.service
{
    public class AWAI01Service
    {
        /// <summary>
        /// 取得獎品清單
        /// </summary>
        /// <param name="model"></param>
        /// <param name="user"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        public AWAI01IndexViewModel GetProductListData(AWAI01IndexViewModel model, UserProfile user, ref ECOOL_DEVEntities db)
        {
            this.SaveBidToTimeUp(model.Search.WhereSouTable);

            try
            {
                string sSQL = string.Empty;
                string SouTable = AWAI01SearchViewModel.SouTableVal.GetSouTable(model.Search.WhereSouTable);

                IEnumerable<AWAI01IndexListViewModel> SpecificListData = null;
                IEnumerable<AWAI01IndexListViewModel> AwardList = null;
                model.ShowMultiple = false;

                if (model.Search.unProduct == "unProduct") //搜尋已下架商品
                {
                    sSQL = @"Select a.*
                             from " + SouTable + @" a (nolock)
                             where 1=1 and (a.SCHOOL_NO =@WhereSCHOOL_NO or a.SCHOOL_NO =@ALL_SCHOOL_NO)
                             and (a.AWARD_STATUS = @AWARD_STATUS or a.EDATETIME < @Now)
                             and (a.AWARD_STATUS <> '9') ";
                    AwardList = db.Database.Connection.Query<AWAI01IndexListViewModel>(sSQL
                      , new
                      {
                          WhereSCHOOL_NO = model.Search.WhereSCHOOL_NO,
                          ALL_SCHOOL_NO = SharedGlobal.ALL,
                          AWARD_STATUS = Awa.AWARD_STATUS_Val.AWARD_STATUS_N,
                          Now = DateTime.Now,
                      });
                }
                else if (model.Search.whereAWARD_SCHOOL_NO == SharedGlobal.ALL) //總召獎品
                {
                    sSQL = @"Select a.*
                             from " + SouTable + @" a (nolock)
                             where 1=1 and  a.SCHOOL_NO =@ALL_SCHOOL_NO
                             and (a.AWARD_STATUS <> @AWARD_STATUS  and (a.AWARD_STATUS <> '9') and ( (a.EDATETIME >= @Now and a.AWARD_TYPE<>@AWARD_TYPE) or (a.AWARD_TYPE=@AWARD_TYPE)) ) ";
                    AwardList = db.Database.Connection.Query<AWAI01IndexListViewModel>(sSQL
                      , new
                      {
                          ALL_SCHOOL_NO = SharedGlobal.ALL,
                          AWARD_STATUS = Awa.AWARD_STATUS_Val.AWARD_STATUS_N,
                          Now = DateTime.Now,
                          AWARD_TYPE = Awa.AWARD_TYPE_Val.AWARD_TYPE_B,
                      });
                }
                else if (model.Search.whereAWARD_SCHOOL_NO == SharedGlobal.Out_of_stock)
                {
                    // 沒有存貨
                    sSQL = @"Select a.*
                             from " + SouTable + @" a (nolock)
                             where 1=1 and a.QTY_STORAGE = @QTY_STORAGE
                             and (a.SCHOOL_NO =@WhereSCHOOL_NO or a.SCHOOL_NO =@ALL_SCHOOL_NO)
                             and (a.AWARD_STATUS <> @AWARD_STATUS   and (a.AWARD_STATUS <> '9') and ( (a.EDATETIME >= @Now and a.AWARD_TYPE<>@AWARD_TYPE) or (a.AWARD_TYPE=@AWARD_TYPE)) ) ";
                    AwardList = db.Database.Connection.Query<AWAI01IndexListViewModel>(sSQL
                      , new
                      {
                          WhereSCHOOL_NO = model.Search.WhereSCHOOL_NO,
                          ALL_SCHOOL_NO = SharedGlobal.ALL,
                          AWARD_STATUS = Awa.AWARD_STATUS_Val.AWARD_STATUS_N,
                          Now = DateTime.Now,
                          AWARD_TYPE = Awa.AWARD_TYPE_Val.AWARD_TYPE_B,
                          QTY_STORAGE = 0
                      });
                }
                else
                {
                    if (model.Search.whereAWARD_SCHOOL_NO != model.Search.WhereSCHOOL_NO)
                    {
                        model.ShowMultiple = true;

                        sSQL = @"   Select Top 4 a.*
                             from " + SouTable + @" a (nolock)
                             where 1=1 and  a.SCHOOL_NO =@ALL_SCHOOL_NO
                             and (a.AWARD_STATUS <> @AWARD_STATUS and (a.AWARD_STATUS <> '9') and a.EDATETIME >= @Now)";

                        SpecificListData = db.Database.Connection.Query<AWAI01IndexListViewModel>(sSQL
                                          , new
                                          {
                                              ALL_SCHOOL_NO = SharedGlobal.ALL,
                                              AWARD_STATUS = Awa.AWARD_STATUS_Val.AWARD_STATUS_N,
                                              Now = DateTime.Now,
                                          });
                    }

                    sSQL = @"Select a.*
                             from " + SouTable + @" a (nolock)
                             where 1=1 and a.SCHOOL_NO =@WhereSCHOOL_NO
                             and (a.AWARD_STATUS <> @AWARD_STATUS and (a.AWARD_STATUS <> '9') and ((a.EDATETIME >= @Now and a.AWARD_TYPE<>@AWARD_TYPE) or (a.AWARD_TYPE=@AWARD_TYPE) )) ";
                    AwardList = db.Database.Connection.Query<AWAI01IndexListViewModel>(sSQL
                      , new
                      {
                          WhereSCHOOL_NO = model.Search.WhereSCHOOL_NO,
                          AWARD_STATUS = Awa.AWARD_STATUS_Val.AWARD_STATUS_N,
                          Now = DateTime.Now,
                          AWARD_TYPE = Awa.AWARD_TYPE_Val.AWARD_TYPE_B,
                      });
                }

                if (!string.IsNullOrWhiteSpace(model.Search.whereKeyword))
                {
                    AwardList = AwardList.Where(a => a.AWARD_NAME.Contains(model.Search.whereKeyword.Trim()));

                    SpecificListData = SpecificListData?.Where(a => a.AWARD_NAME.Contains(model.Search.whereKeyword.Trim()));
                }

                if (!string.IsNullOrWhiteSpace(model.Search.AWARD_TYPE))
                {
                    var arrAWARD_TYPE = model.Search.AWARD_TYPE.Split(',');
                    AwardList = AwardList.Where(a => arrAWARD_TYPE.Contains(a.AWARD_TYPE.ToString()) && a.AWARD_TYPE != null);

                    SpecificListData = SpecificListData?.Where(a => arrAWARD_TYPE.Contains(a.AWARD_TYPE.ToString()) && a.AWARD_TYPE != null);
                }

                SpecificListData = SpecificListData?.OrderByDescending(a => a.SCHOOL_NO).ThenBy(a => a.HOT_YN == "Y" ? 0 : 1).ThenByDescending(n => n.COST_CASH);

                AwardList = AwardList.OrderByDescending(a => a.SCHOOL_NO).ThenBy(a => a.HOT_YN == "Y" ? 0 : 1).ThenByDescending(n => n.COST_CASH);

                // -- 限制對象檢查: 一筆一筆查看是否要對此user隱藏 --
                if (user != null) // 顧客都可以看沒限制
                {
                    if (SpecificListData != null)
                    {
                        foreach (var specif in SpecificListData)
                        {
                            if (!(specif.BUY_PERSON_YN == "Y"))
                                continue;
                            if (db.REFT01.Where(A => A.REF_KEY == specif.AWARD_NO.ToString()
                                       && A.REF_TABLE == SouTable
                                       && A.SCHOOL_NO == user.SCHOOL_NO
                                       && A.USER_NO == user.USER_NO).Any() == false)
                            {
                                if ((user.ROLE_TYPE >= (int)UserRoles.各校管理者)) // 各校管理者權限以下才隱藏
                                {
                                    specif.IsHidden = true;
                                }
                            }
                        }
                    }

                    if (AwardList != null)
                    {
                        foreach (var award in AwardList)
                        {
                            if (!(award.BUY_PERSON_YN == "Y"))
                                continue;

                            if (db.REFT01.Where(A => A.REF_KEY == award.AWARD_NO.ToString()
                                       && A.REF_TABLE == SouTable
                                       && A.SCHOOL_NO == user.SCHOOL_NO
                                       && A.USER_NO == user.USER_NO).Any() == false)
                            {
                                if ((user.ROLE_TYPE >= (int)UserRoles.各校管理者)) // 各校管理者權限以下才隱藏
                                {
                                    award.IsHidden = true;
                                }
                            }
                        }
                    }
                }
                // -- 限制對象檢查結束 --

                model.SpecificListData = SpecificListData?.ToPagedList(0, model.PageSize);

                model.ListData = AwardList.ToPagedList(model.Search.Page > 0 ? model.Search.Page - 1 : 0, model.PageSize);
            }
            catch (Exception ex)
            {
                throw ex;
            }

            return model;
        }

        public AWAI01IndexViewModel GetProductListDataForFullScreen(AWAI01IndexViewModel model, UserProfile user, ref ECOOL_DEVEntities db)
        {
            var vm = GetProductListData(model, user, ref db);
            vm.ListData = vm.ListData.Where(a => a.FULLSCREEN_YN == "Y" || a.FULLSCREEN_YN == "B").ToPagedList(0, int.MaxValue);

            if (model.OrderType == OrderType.兌換期限由小到大)
            {
                vm.ListData = vm.ListData.OrderBy(l => l.SDATETIME).ToPagedList(0, int.MaxValue);
            }
            else if (model.OrderType == OrderType.兌換期限由大到小)
            {
                vm.ListData = vm.ListData.OrderByDescending(l => l.SDATETIME).ToPagedList(0, int.MaxValue);
            }
            else if (model.OrderType == OrderType.酷幣點數由小到大)
            {
                vm.ListData = vm.ListData.OrderBy(l => l.COST_CASH).ToPagedList(0, int.MaxValue);
            }
            else
            {
                vm.ListData = vm.ListData.OrderByDescending(l => l.COST_CASH).ToPagedList(0, int.MaxValue);
            }

            return vm;
        }

        /// <summary>
        /// 排行榜
        /// </summary>
        /// <param name="model"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        public AWAI01OrderListViewModel GetProductOrderList(AWAI01OrderListViewModel model, ref ECOOL_DEVEntities db)
        {
            string sSQL = string.Empty;
            string SouTable = AWAI01SearchViewModel.SouTableVal.GetSouTable(model.Search.WhereSouTable);

            sSQL = $@" Select  a.*
                      ,Case When isnull(a.QTY_STORAGE,0)+isnull(a.QTY_TRANS,0) >0 Then
                      Round(isnull(a.QTY_TRANS,0)*100 / (isnull(a.QTY_STORAGE,0)+isnull(a.QTY_TRANS,0)*1.00 ),0)
                      Else 0 End as RATE
                      from {SouTable} a (nolock)
                      where 1=1 and  (a.SCHOOL_NO =@ALL_SCHOOL_NO or a.SCHOOL_NO =@WhereSCHOOL_NO)
                      order by RAte desc,isnull(a.QTY_TRANS,0) desc  ";
            var AwardList = db.Database.Connection.Query<AWAI01IndexListViewModel>(sSQL
                      , new
                      {
                          WhereSCHOOL_NO = model.Search.WhereSCHOOL_NO,
                          ALL_SCHOOL_NO = SharedGlobal.ALL,
                      });

            if (!model.Search.StopProduct)
            {
                AwardList = AwardList.Where(a => a.AWARD_STATUS != Awa.AWARD_STATUS_Val.AWARD_STATUS_N
                && a.EDATETIME >= DateTime.Now);
            }
           
            if (model.Search.SDATETIME != null && model.Search.EDATETIME != null) {

                AwardList = AwardList.Where(x => x.SDATETIME >= model.Search.SDATETIME && x.EDATETIME <= model.Search.EDATETIME);
            }
            if (!string.IsNullOrWhiteSpace(model.Search.whereKeyword))
            {
                AwardList = AwardList.Where(a => a.AWARD_NAME.Contains(model.Search.whereKeyword.Trim()));
            }

            if (!string.IsNullOrWhiteSpace(model.Search.AWARD_TYPE))
            {
                var arrAWARD_TYPE = model.Search.AWARD_TYPE.Split(',');
                AwardList = AwardList.Where(a => arrAWARD_TYPE.Contains(a.AWARD_TYPE.ToString()) && a.AWARD_TYPE != null);
            }

            if (!string.IsNullOrWhiteSpace(model.Search.whereAWARD_SCHOOL_NO))
            {
                AwardList = AwardList.Where(a => a.SCHOOL_NO.Contains(model.Search.whereAWARD_SCHOOL_NO.Trim()));
            }
            switch (model.Search.OrdercColumn)
            {
                case "AWARD_NAME":
                    AwardList = AwardList.OrderByDescending(a => a.AWARD_NAME);
                    break;

                case "AWARD_TYPE":
                    AwardList = AwardList.OrderByDescending(a => a.AWARD_TYPE);
                    break;

                case "COST_CASH":
                    AwardList = AwardList.OrderByDescending(a => a.COST_CASH);
                    break;

                case "QTY_STORAGE":
                    AwardList = AwardList.OrderByDescending(a => a.QTY_STORAGE);
                    break;

                case "QTY_TRANS":
                    AwardList = AwardList.OrderByDescending(a => a.QTY_TRANS);
                    break;
                case "RATE":
                    AwardList = AwardList.OrderByDescending(a => a.RATE);
                    break;

                default:

                    AwardList = AwardList.OrderByDescending(a => a.SDATETIME).ThenByDescending(a => a.AWARD_NAME);
                    break;
            }
            model.ListData = AwardList.ToPagedList(model.Search.Page > 0 ? model.Search.Page - 1 : 0, model.PageSize);

            return model;
        }

        public AWAI01BidDataIndexViewModel GetBidDataList(int AWARD_NO, string WhereSouTable, ref ECOOL_DEVEntities db)
        {
            string sSQL = string.Empty;
            string SouTable = AWAI01SearchViewModel.SouTableVal.GetSouTable(WhereSouTable);
            string SouTable_B = AWAI01SearchViewModel.SouTableVal.GetSouTableBID(WhereSouTable);
            AWAI01BidDataIndexViewModel model = new AWAI01BidDataIndexViewModel();

            try
            {
                sSQL = $@"Select a.*
                    from {SouTable} a(nolock)
                    where a.AWARD_NO=@AWARD_NO";

                model.AwaData = db.Database.Connection.Query<AWAI01IndexListViewModel>(sSQL
                         , new
                         {
                             AWARD_NO = AWARD_NO,
                         }).FirstOrDefault();

                sSQL = $@"select a.* ,b.SHORT_NAME
                    from {SouTable_B} a  (nolock)
                    inner join BDMT01 b  (nolock) on a.SCHOOL_NO=b.SCHOOL_NO
                    where a.AWARD_NO=@AWARD_NO
                    order by a.BID_DATE desc ";

                model.BidList = db.Database.Connection.Query<AWAI01BidDataListViewModel>(sSQL
                       , new
                       {
                           AWARD_NO = AWARD_NO,
                       }).ToList();
            }
            catch (Exception ex)
            {
                throw ex;
            }

            return model;
        }

        /// <summary>
        /// 取得單筆獎品資料
        /// </summary>
        /// <param name="model"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        public AWAI01EditViewModel GetProduct(AWAI01EditViewModel model, ref ECOOL_DEVEntities db)
        {
            try
            {
                string sSQL = string.Empty;
                string SouTable = AWAI01SearchViewModel.SouTableVal.GetSouTable(model.Search.WhereSouTable);

                sSQL = @"Select a.*
                             from " + SouTable + @" a (nolock)
                             where 1=1 and a.AWARD_NO =@AWARD_NO ";
                var Award = db.Database.Connection.Query<AWAI01EditDataViewModel>(sSQL
                     , new
                     {
                         AWARD_NO = model.Search.WhereAWARD_NO,
                     });

                model.Edit = Award.FirstOrDefault();

                if (model.Edit.AWARD_TYPE == Awa.AWARD_TYPE_Val.AWARD_TYPE_B)
                {
                    model.Edit.BID_START_PRICE = model.Edit.COST_CASH;
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }

            return model;
        }

        /// <summary>
        /// 兌換取出明細資料
        /// </summary>
        /// <param name="model"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        public AWAI01ExchangeViewModel GetProductExchangeData(AWAI01ExchangeViewModel model, ref ECOOL_DEVEntities db)
        {
            try
            {
                string sSQL = string.Empty;
                string SouTable = AWAI01SearchViewModel.SouTableVal.GetSouTable(model.Search.WhereSouTable);
                string SouTableBID = AWAI01SearchViewModel.SouTableVal.GetSouTableBID(model.Search.WhereSouTable);

                this.SaveBidToTimeUp(model.Search?.WhereSouTable, model.Search?.WhereAWARD_NO);

                sSQL = @"Select a.*
                             from " + SouTable + @" a (nolock)
                             where 1=1 and a.AWARD_NO =@AWARD_NO ";
                var Award = db.Database.Connection.Query<AWAI01IndexListViewModel>(sSQL
                     , new
                     {
                         AWARD_NO = model.Search.WhereAWARD_NO,
                     }).FirstOrDefault();

                model.AwaData = Award;

                sSQL = $@"SELECT TOP 1 B.BID_CASH FROM {SouTableBID} B (NOLOCK) WHERE B.AWARD_NO=@AWARD_NO ORDER BY B.BID_CASH DESC";
                model.AwaData.MAX_PRICE = db.Database.Connection.Query<double>(sSQL
                    , new
                    {
                        AWARD_NO = model.Search.WhereAWARD_NO,
                    }).FirstOrDefault();

                // 查詢所有的限制對象
                if ((model.AwaData.BUY_PERSON_YN ?? "N") == "Y")
                {
                    // 查看名稱
                    var Q_BUY_PERSON = db.Database.Connection.Query<string>(@"select CONTENT_TXT=case when a.BTN_TYPE='person' then Replace(a.CONTENT_TXT,a.USER_NO,'') else a.CONTENT_TXT end
                                                                                 from REFT01_Q a(NOLOCK)
                                                                                 where a.REF_TABLE in ('AWAT02','AWAT09')
                                                                                 and a.REF_KEY=@REF_KEY"
                                                                                 , new { REF_KEY = model.AwaData.AWARD_NO.ToString() }
                                                                                 ).ToList();

                    model.AwaData.BUY_PERSON = string.Join(",", Q_BUY_PERSON.ToArray());
                }

                if (model.AwaData.AWARD_TYPE == Awa.AWARD_TYPE_Val.AWARD_TYPE_B && (model.THIS_PRICE_CASH ?? 0) == 0)
                {
                    if (model.AwaData.BID_PER_UNIT_PRICE != null)
                    {
                        if (model.AwaData.MAX_PRICE < model.AwaData.COST_CASH)
                        {
                            model.THIS_PRICE_CASH = model.AwaData.COST_CASH;
                        }
                        else
                        {
                            model.THIS_PRICE_CASH = model.AwaData.COST_CASH + model.AwaData.BID_PER_UNIT_PRICE;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }

            return model;
        }

        /// <summary>
        /// 異動資料
        /// </summary>
        /// <param name="model"></param>
        /// <param name="files"></param>
        /// <param name="User"></param>
        /// <param name="db"></param>
        /// <param name="Message"></param>
        /// <returns></returns>
        public bool SaveAwat(AWAI01EditViewModel model, HttpPostedFileBase files, UserProfile User, ref ECOOL_DEVEntities db, ref string Message)
        {
            bool Save = false;

            if (model.Search.WhereSouTable == AWAI01SearchViewModel.SouTableVal.Teacher)
            {
                Save = SaveAwatTeacher(model, files, User, ref db, ref Message);
            }
            else
            {
                Save = SaveAwatStudent(model, files, User, ref db, ref Message);
            }

            return Save;
        }

        /// <summary>
        /// 兌換 / 下標
        /// </summary>
        /// <param name="model"></param>
        /// <param name="User"></param>
        /// <param name="db"></param>
        /// <param name="Message"></param>
        /// <returns></returns>
        public AWAI01ExchangeViewModel SaveExchange( AWAI01ExchangeViewModel model, UserProfile User, ref ECOOL_DEVEntities db, ref string Message , ref List<Tuple<string, string, int>> valuesList)
        {
            string SouTable = AWAI01SearchViewModel.SouTableVal.GetSouTable(model.Search.WhereSouTable);
            string LOG_DESC = (model.AwaData.AWARD_TYPE == Awa.AWARD_TYPE_Val.AWARD_TYPE_C) ? "募資公益" : "獎品兌換";

            if (model.AwaData.AWARD_TYPE == Awa.AWARD_TYPE_Val.AWARD_TYPE_C)
            {
                SouTable = SouTable + "_" + Awa.AWARD_TYPE_Val.AWARD_TYPE_C;
            }

            int SYear;
            int Semesters;
            SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);

            if (model.Search.WhereSouTable == AWAI01SearchViewModel.SouTableVal.Teacher)
            {
                AWAT09 SaveUp = db.AWAT09.Where(a => a.AWARD_NO == model.AwaData.AWARD_NO).FirstOrDefault();

                if (SaveUp == null)
                {
                    Message = "找不到此筆資料";
                    model.Status = false;
                    return model;
                }

                if (SaveUp.AWARD_TYPE == Awa.AWARD_TYPE_Val.AWARD_TYPE_B)  //競標
                {
                    AWAT09_B CreB = new AWAT09_B()
                    {
                        BID_NO = Guid.NewGuid().ToString("N"),
                        AWARD_NO = SaveUp.AWARD_NO,
                        SCHOOL_NO = User.SCHOOL_NO,
                        USER_NO = User.USER_NO,
                        SYEAR = Convert.ToByte(SYear),
                        SEMESTER = Convert.ToByte(Semesters),
                        CLASS_NO = User.CLASS_NO,
                        SEAT_NO = User.SEAT_NO,
                        NAME = User.NAME,
                        SNAME = User.SNAME,
                        BID_DATE = DateTime.Now,
                        BID_CASH = model.THIS_PRICE_CASH,
                        BID_STATUS = Awa.BID_STATUS_Val.AWARD_STATUS_OK
                    };

                    db.AWAT09_B.Add(CreB);

                    SaveUp.COST_CASH = model.THIS_PRICE_CASH;
                    model.CostCash = model.THIS_PRICE_CASH;
                }

                if ((SaveUp.AWARD_TYPE != Awa.AWARD_TYPE_Val.AWARD_TYPE_B)
                    || (SaveUp.AWARD_TYPE == Awa.AWARD_TYPE_Val.AWARD_TYPE_B && (SaveUp.COST_CASH ?? 0) >= SaveUp.BID_BUY_PRICE))
                {
                    this.CreTeacherHis(SaveUp, ref db);

                    AWAT03 Cre = new AWAT03()
                    {
                        SCHOOL_NO = User.SCHOOL_NO,
                        USER_NO = User.USER_NO,
                        AWARD_NO = model.AwaData.AWARD_NO,
                        TRANS_DATE = DateTime.Now,
                        SEMESTER = Convert.ToByte(Semesters),
                        SYEAR = Convert.ToByte(SYear),
                        CLASS_NO = User.TEACH_CLASS_NO,
                        SEAT_NO = User.SEAT_NO,
                        NAME = User.NAME,
                        SNAME = User.SNAME,
                        TRANS_QTY = SaveUp.QTY_STORAGE,
                        TRANS_CASH = SaveUp.COST_CASH,
                        TRANS_STATUS = (byte)VAWA004.TRANS_STATUS_Val.TRANS_STATUS_UN,
                    };

                    db.AWAT03.Add(Cre);

                    SaveUp.QTY_STORAGE = (SaveUp.QTY_STORAGE ?? 0) - 1;
                    SaveUp.QTY_TRANS = (SaveUp.QTY_TRANS ?? 0) + 1;
                    SaveUp.AWARD_STATUS = Awa.AWARD_STATUS_Val.AWARD_STATUS_Y;
                    model.CostCash = SaveUp.COST_CASH;
                    ECOOL_APP.CashHelper.TeachAddCash(User, Convert.ToInt32(SaveUp.COST_CASH * -1), User.SCHOOL_NO, User.USER_NO, SouTable, SaveUp.AWARD_NO.ToString(), LOG_DESC, false, null, ref db);
                }
            }
            else
            {
                AWAT02 SaveUp = db.AWAT02.Where(a => a.AWARD_NO == model.AwaData.AWARD_NO).FirstOrDefault();

                if (SaveUp == null)
                {
                    Message = "找不到此筆資料";
                    model.Status = false;
                    return model;
                }

                if (SaveUp.AWARD_TYPE == Awa.AWARD_TYPE_Val.AWARD_TYPE_B)  //競標
                {
                    AWAT02_B CreB = new AWAT02_B()
                    {
                        BID_NO = Guid.NewGuid().ToString("N"),
                        AWARD_NO = SaveUp.AWARD_NO,
                        SCHOOL_NO = User.SCHOOL_NO,
                        USER_NO = User.USER_NO,
                        SYEAR = Convert.ToByte(SYear),
                        SEMESTER = Convert.ToByte(Semesters),
                        CLASS_NO = User.CLASS_NO,
                        SEAT_NO = User.SEAT_NO,
                        NAME = User.NAME,
                        SNAME = User.SNAME,
                        BID_DATE = DateTime.Now,
                        BID_CASH = model.THIS_PRICE_CASH,
                        BID_STATUS = Awa.BID_STATUS_Val.AWARD_STATUS_OK
                    };

                    db.AWAT02_B.Add(CreB);

                    SaveUp.COST_CASH = model.THIS_PRICE_CASH;
                    model.CostCash = SaveUp.COST_CASH;
                }

                if ((SaveUp.AWARD_TYPE != Awa.AWARD_TYPE_Val.AWARD_TYPE_B)
                 || (SaveUp.AWARD_TYPE == Awa.AWARD_TYPE_Val.AWARD_TYPE_B && (SaveUp.COST_CASH ?? 0) >= SaveUp.BID_BUY_PRICE))
                {
                    this.CreStudentHis(SaveUp, ref db);

                    AWAT03 Cre = new AWAT03()
                    {
                        SCHOOL_NO = User.SCHOOL_NO,
                        USER_NO = User.USER_NO,
                        AWARD_NO = model.AwaData.AWARD_NO,
                        TRANS_DATE = DateTime.Now,
                        SEMESTER = Convert.ToByte(Semesters),
                        SYEAR = Convert.ToByte(SYear),
                        CLASS_NO = User.CLASS_NO,
                        SEAT_NO = User.SEAT_NO,
                        NAME = User.NAME,
                        SNAME = User.SNAME,
                        TRANS_QTY = SaveUp.QTY_STORAGE,
                        TRANS_CASH = SaveUp.COST_CASH,
                        TRANS_STATUS = (byte)VAWA004.TRANS_STATUS_Val.TRANS_STATUS_UN,
                    };

                    //數位學生證對換
                    if (!string.IsNullOrWhiteSpace(model.CARD_ID))
                    {
                        Cre.TRANS_STATUS = (byte)VAWA004.TRANS_STATUS_Val.TRANS_STATUS_Receive;
                        Cre.GOT_DATE = DateTime.Now.Date;
                    }

                    db.AWAT03.Add(Cre);

                    SaveUp.QTY_STORAGE = (SaveUp.QTY_STORAGE ?? 0) - 1;
                    SaveUp.QTY_TRANS = (SaveUp.QTY_TRANS ?? 0) + 1;
                    SaveUp.AWARD_STATUS = Awa.AWARD_STATUS_Val.AWARD_STATUS_Y;
                    model.CostCash = SaveUp.COST_CASH;
                    ECOOL_APP.CashHelper.AddCash(User, Convert.ToInt32(SaveUp.COST_CASH * -1), User.SCHOOL_NO, User.USER_NO, SouTable, SaveUp.AWARD_NO.ToString(), LOG_DESC, false, ref db,"", "", ref valuesList);
                }
            }

            try
            {
                db.SaveChanges();
                model.Status = true;
                return model;
            }
            catch (Exception ex)
            {
                Message = "系統發生錯誤;原因:" + ex.Message;
                model.Status = false;
                return model;
            }
        }

        /// <summary>
        /// 下架
        /// </summary>
        /// <param name="model"></param>
        /// <param name="db"></param>
        /// <param name="Message"></param>
        /// <returns></returns>
        public bool SaveAwatUpdataStatus(AWAI01EditViewModel model, ref ECOOL_DEVEntities db, ref string Message)
        {


            if (model.Search.WhereSouTable == AWAI01SearchViewModel.SouTableVal.Teacher)
            {
                AWAT09 SaveUp = db.AWAT09.Where(a => a.AWARD_NO == model.Edit.AWARD_NO).FirstOrDefault();

                if (SaveUp == null)
                {
                    Message = "找不到此筆資料";
                    return false;
                }

                this.CreTeacherHis(SaveUp, ref db);

                SaveUp.AWARD_STATUS = Awa.AWARD_STATUS_Val.AWARD_STATUS_N;
            }
            else
            {
                AWAT02 SaveUp = db.AWAT02.Where(a => a.AWARD_NO == model.Edit.AWARD_NO).FirstOrDefault();

                if (SaveUp == null)
                {
                    Message = "找不到此筆資料";
                    return false;
                }

                this.CreStudentHis(SaveUp, ref db);

                SaveUp.AWARD_STATUS = Awa.AWARD_STATUS_Val.AWARD_STATUS_N;
            }

            try
            {
                db.SaveChanges();
                return true;
            }
            catch (Exception ex)
            {
                Message = "系統發生錯誤;原因:" + ex.Message;
                return false;
            }
        }

        public bool SaveAwatRealDelete(AWAI01EditViewModel model, ref ECOOL_DEVEntities db, ref string Message)
        {
            if (model.Search.WhereSouTable == AWAI01SearchViewModel.SouTableVal.Teacher)
            {
                AWAT09 SaveUp = db.AWAT09.Where(a => a.AWARD_NO == model.Edit.AWARD_NO).FirstOrDefault();

                if (SaveUp == null)
                {
                    Message = "找不到此筆資料";
                    return false;
                }

                if (SaveUp.QTY_TRANS > 0)
                {
                    Message = "刪除失敗，已被兌換過，無法刪除";
                    return false;
                }

                this.CreTeacherHis(SaveUp, ref db);

                db.AWAT09.Remove(SaveUp);
            }
            else
            {
                AWAT02 SaveUp = db.AWAT02.Where(a => a.AWARD_NO == model.Edit.AWARD_NO).FirstOrDefault();

                if (SaveUp == null)
                {
                    Message = "找不到此筆資料";
                    return false;
                }

                if (SaveUp.QTY_TRANS > 0)
                {
                    Message = "刪除失敗，已被兌換過，無法刪除";
                    return false;
                }

                this.CreStudentHis(SaveUp, ref db);

                db.AWAT02.Remove(SaveUp);
            }

            try
            {
                db.SaveChanges();
                return true;
            }
            catch (Exception ex)
            {
                Message = "系統發生錯誤;原因:" + ex.Message;
                return false;
            }
        }

        public bool SaveAwatHide(AWAI01EditViewModel model, ref ECOOL_DEVEntities db, ref string Message)
        {
            if (model.Search.WhereSouTable == AWAI01SearchViewModel.SouTableVal.Teacher)
            {
                AWAT09 SaveUp = db.AWAT09.Where(a => a.AWARD_NO == model.Edit.AWARD_NO).FirstOrDefault();

                if (SaveUp == null)
                {
                    Message = "找不到此筆資料";
                    return false;
                }

                SaveUp.AWARD_STATUS = Awa.AWARD_STATUS_Val.AWARD_STATUS_9;
                this.CreTeacherHis(SaveUp, ref db);
            }
            else
            {
                AWAT02 SaveUp = db.AWAT02.Where(a => a.AWARD_NO == model.Edit.AWARD_NO).FirstOrDefault();

                if (SaveUp == null)
                {
                    Message = "找不到此筆資料";
                    return false;
                }

                SaveUp.AWARD_STATUS = Awa.AWARD_STATUS_Val.AWARD_STATUS_9;
                this.CreStudentHis(SaveUp, ref db);
            }

            try
            {
                db.SaveChanges();
                return true;
            }
            catch (Exception ex)
            {
                Message = "系統發生錯誤;原因:" + ex.Message;
                return false;
            }
        }

        /// <summary>
        /// 學生存檔
        /// </summary>
        /// <param name="model"></param>
        /// <param name="files"></param>
        /// <param name="User"></param>
        /// <param name="db"></param>
        /// <param name="Message"></param>
        /// <returns></returns>
        public bool SaveAwatStudent(AWAI01EditViewModel model, HttpPostedFileBase file, UserProfile User, ref ECOOL_DEVEntities db, ref string Message)
        {
            if (model.Edit.PUSH_YN == "Y")
            {
                if (db.APPT03_Q.Where(a => a.REF_TABLE == "AWAT02" && a.REF_KEY == model.REF_KEY).Any() == false)
                {
                    Message = "勾選了是否通知，但未選取通知人員";
                    return false;
                }
            }

            if (model.Edit.BUY_PERSON_YN == "Y")
            {
                if (db.REFT01_Q.Where(a => a.REF_TABLE == "AWAT02" && a.REF_KEY == model.REF_KEY).Any() == false)
                {
                    Message = "勾選了是否限制對象，但未選取人員";
                    return false;
                }
            }

            AWAT02 SaveUp = null;

            SaveUp = db.AWAT02.Where(a => a.AWARD_NO == model.Edit.AWARD_NO).FirstOrDefault();

            if (SaveUp != null)
            {
                this.CreStudentHis(SaveUp, ref db);

                SaveUp.SCHOOL_NO = model.Edit.SCHOOL_NO;
                SaveUp.AWARD_TYPE = model.Edit.AWARD_TYPE;
                SaveUp.AWARD_NAME = model.Edit.AWARD_NAME;

                SaveUp.SDATETIME = model.Edit.SDATETIME;
                SaveUp.EDATETIME = model.Edit.EDATETIME;
                SaveUp.DESCRIPTION = model.Edit.DESCRIPTION;

                SaveUp.AWARD_STATUS = model.Edit.AWARD_STATUS;

                //當已下架，但被兌換過，重新上架 AWARD_STATUS =>y
                if ((SaveUp.QTY_TRANS ?? 0) > 0 && SaveUp.AWARD_STATUS == Awa.AWARD_STATUS_Val.AWARD_STATUS_N)
                {
                    SaveUp.AWARD_STATUS = Awa.AWARD_STATUS_Val.AWARD_STATUS_Y;
                }
                else if ((SaveUp.QTY_TRANS ?? 0) == 0 && SaveUp.AWARD_STATUS == Awa.AWARD_STATUS_Val.AWARD_STATUS_N)
                { //當已下架，但沒被兌換過，重新上架 AWARD_STATUS =>0
                    SaveUp.AWARD_STATUS = Awa.AWARD_STATUS_Val.AWARD_STATUS_0;
                }

                SaveUp.COST_CASH = model.Edit.COST_CASH;
                SaveUp.QTY_STORAGE = model.Edit.QTY_STORAGE;

                SaveUp.HOT_YN = model.Edit.HOT_YN;
                SaveUp.FULLSCREEN_YN = model.Edit.FULLSCREEN_YN;
                SaveUp.BUY_PERSON_YN = model.Edit.BUY_PERSON_YN;
                SaveUp.READ_LEVEL = model.Edit.READ_LEVEL;
                SaveUp.PASSPORT_LEVEL = model.Edit.PASSPORT_LEVEL;
                SaveUp.QTY_LIMIT = model.Edit.QTY_LIMIT;
                SaveUp.SHOW_DESCRIPTION_YN = model.Edit.SHOW_DESCRIPTION_YN;
                SaveUp.PUSH_YN = model.Edit.PUSH_YN;
                SaveUp.BID_START_PRICE = model.Edit.BID_START_PRICE;
                SaveUp.BID_PER_UNIT_PRICE = model.Edit.BID_PER_UNIT_PRICE;
                SaveUp.BID_BUY_PRICE = model.Edit.BID_BUY_PRICE;
                SaveUp.VIDEO_PATH = model.Edit.VIDEO_PATH;
            }
            else
            {
                SaveUp = new AWAT02()
                {
                    SCHOOL_NO = model.Edit.SCHOOL_NO,
                    AWARD_TYPE = model.Edit.AWARD_TYPE,
                    AWARD_NAME = model.Edit.AWARD_NAME,
                    COST_CASH = model.Edit.COST_CASH,
                    QTY_STORAGE = model.Edit.QTY_STORAGE,
                    SDATETIME = model.Edit.SDATETIME,
                    EDATETIME = model.Edit.EDATETIME,
                    DESCRIPTION = model.Edit.DESCRIPTION,
                    AWARD_STATUS = Awa.AWARD_STATUS_Val.AWARD_STATUS_0,
                    HOT_YN = model.Edit.HOT_YN,
                    FULLSCREEN_YN = model.Edit.FULLSCREEN_YN,
                    BUY_PERSON_YN = model.Edit.BUY_PERSON_YN,
                    READ_LEVEL = model.Edit.READ_LEVEL,
                    PASSPORT_LEVEL = model.Edit.PASSPORT_LEVEL,
                    QTY_LIMIT = model.Edit.QTY_LIMIT,
                    SHOW_DESCRIPTION_YN = model.Edit.SHOW_DESCRIPTION_YN,
                    PUSH_YN = model.Edit.PUSH_YN,
                    BID_START_PRICE = model.Edit.BID_START_PRICE,
                    BID_PER_UNIT_PRICE = model.Edit.BID_PER_UNIT_PRICE,
                    BID_BUY_PRICE = model.Edit.BID_BUY_PRICE,
                    QTY_TRANS = 0,
                    VIDEO_PATH = model.Edit.VIDEO_PATH
                };
                db.AWAT02.Add(SaveUp);
            }

            db.Database.Connection.Open();
            using (TransactionScope tx = new TransactionScope())
            {
                try
                {
                    db.SaveChanges();
                }
                catch (Exception ex)
                {
                    Message = "系統發生錯誤;原因:" + ex.Message;
                    return false;
                }

                if ((SaveUp.BUY_PERSON_YN == "Y"))
                {
                    try
                    {
                        REFT01Service.UpdateStatus(User.USER_KEY, "AWAT02", model.REF_KEY, SaveUp.AWARD_NO.ToString(), (db.Database.Connection) as SqlConnection);
                    }
                    catch (Exception ex)
                    {
                        Message = "系統發生錯誤;原因:" + ex.Message;
                        return false;
                    }
                }

                if (file != null && file.ContentLength > 0)
                {
                    string IMG_FILE = UpLoadFile(SaveUp.AWARD_NO, SaveUp.SCHOOL_NO, file, ref Message, model.Search.WhereSouTable);

                    if (!string.IsNullOrWhiteSpace(Message))
                    {
                        return false;
                    }

                    SaveUp.IMG_FILE = IMG_FILE;

                    try
                    {
                        db.SaveChanges();
                    }
                    catch (Exception ex)
                    {
                        Message = "系統發生錯誤;原因:" + ex.Message;
                        return false;
                    }
                }

                tx.Complete();
            }

            if (SaveUp.PUSH_YN == SharedGlobal.Y)
            {
                string BODY_TXT = "新獎品上架 品名:" + SaveUp.AWARD_NAME + "，兌換點數:" + SaveUp.COST_CASH.ToString();

                PushService.QAppt03ToAPPT03Push("AWAT02", model.REF_KEY, SaveUp.AWARD_NO.ToString(), ref db, User, out Message, BODY_TXT, "AWAI01", "ActionSaveAwat", "", "AWAI01/AwatQ02");
            }

            return true;
        }

        /// <summary>
        /// 老師存檔
        /// </summary>
        /// <param name="model"></param>
        /// <param name="files"></param>
        /// <param name="User"></param>
        /// <param name="db"></param>
        /// <param name="Message"></param>
        /// <returns></returns>
        public bool SaveAwatTeacher(AWAI01EditViewModel model, HttpPostedFileBase file, UserProfile User, ref ECOOL_DEVEntities db, ref string Message)
        {
            if (model.Edit.PUSH_YN == "Y")
            {
                if (db.APPT03_Q.Where(a => a.REF_TABLE == "AWAT09" && a.REF_KEY == model.REF_KEY).Any() == false)
                {
                    Message = "勾選了是否通知，但未選取通知人員";
                    return false;
                }
            }

            if (model.Edit.BUY_PERSON_YN == "Y")
            {
                if (db.REFT01_Q.Where(a => a.REF_TABLE == "AWAT09" && a.REF_KEY == model.REF_KEY).Any() == false)
                {
                    Message = "勾選了是否限制對象，但未選取人員";
                    return false;
                }
            }

            AWAT09 SaveUp = null;

            SaveUp = db.AWAT09.Where(a => a.AWARD_NO == model.Edit.AWARD_NO).FirstOrDefault();

            if (SaveUp != null)
            {
                this.CreTeacherHis(SaveUp, ref db);

                SaveUp.SCHOOL_NO = model.Edit.SCHOOL_NO;
                SaveUp.AWARD_TYPE = model.Edit.AWARD_TYPE;
                SaveUp.AWARD_NAME = model.Edit.AWARD_NAME;

                SaveUp.SDATETIME = model.Edit.SDATETIME;
                SaveUp.EDATETIME = model.Edit.EDATETIME;
                SaveUp.DESCRIPTION = model.Edit.DESCRIPTION;

                SaveUp.AWARD_STATUS = model.Edit.AWARD_STATUS;

                //當已下架，但被兌換過，重新上架 AWARD_STATUS =>y
                if ((SaveUp.QTY_TRANS ?? 0) > 0 && SaveUp.AWARD_STATUS == Awa.AWARD_STATUS_Val.AWARD_STATUS_N)
                {
                    SaveUp.AWARD_STATUS = Awa.AWARD_STATUS_Val.AWARD_STATUS_Y;
                }
                else if ((SaveUp.QTY_TRANS ?? 0) == 0 && SaveUp.AWARD_STATUS == Awa.AWARD_STATUS_Val.AWARD_STATUS_N)
                { //當已下架，但沒被兌換過，重新上架 AWARD_STATUS =>0
                    SaveUp.AWARD_STATUS = Awa.AWARD_STATUS_Val.AWARD_STATUS_0;
                }

                SaveUp.COST_CASH = model.Edit.COST_CASH;
                SaveUp.QTY_STORAGE = model.Edit.QTY_STORAGE;

                SaveUp.HOT_YN = model.Edit.HOT_YN;
                SaveUp.QTY_LIMIT = model.Edit.QTY_LIMIT;
                SaveUp.SHOW_DESCRIPTION_YN = model.Edit.SHOW_DESCRIPTION_YN;
                SaveUp.PUSH_YN = model.Edit.PUSH_YN;
                SaveUp.BID_START_PRICE = model.Edit.BID_START_PRICE;
                SaveUp.BID_PER_UNIT_PRICE = model.Edit.BID_PER_UNIT_PRICE;
                SaveUp.BUY_PERSON_YN = model.Edit.BUY_PERSON_YN;
                SaveUp.BID_BUY_PRICE = model.Edit.BID_BUY_PRICE;
                SaveUp.VIDEO_PATH = model.Edit.VIDEO_PATH;
            }
            else
            {
                SaveUp = new AWAT09()
                {
                    SCHOOL_NO = model.Edit.SCHOOL_NO,
                    AWARD_TYPE = model.Edit.AWARD_TYPE,
                    AWARD_NAME = model.Edit.AWARD_NAME,
                    COST_CASH = model.Edit.COST_CASH,
                    QTY_STORAGE = model.Edit.QTY_STORAGE,
                    SDATETIME = model.Edit.SDATETIME,
                    EDATETIME = model.Edit.EDATETIME,
                    DESCRIPTION = model.Edit.DESCRIPTION,
                    AWARD_STATUS = Awa.AWARD_STATUS_Val.AWARD_STATUS_0,
                    HOT_YN = model.Edit.HOT_YN,
                    QTY_LIMIT = model.Edit.QTY_LIMIT,
                    SHOW_DESCRIPTION_YN = model.Edit.SHOW_DESCRIPTION_YN,
                    PUSH_YN = model.Edit.PUSH_YN,
                    BID_START_PRICE = model.Edit.BID_START_PRICE,
                    BID_PER_UNIT_PRICE = model.Edit.BID_PER_UNIT_PRICE,
                    BUY_PERSON_YN = model.Edit.BUY_PERSON_YN,
                    BID_BUY_PRICE = model.Edit.BID_BUY_PRICE,
                    QTY_TRANS = 0,
                    VIDEO_PATH = model.Edit.VIDEO_PATH
                };
                db.AWAT09.Add(SaveUp);
            }

            db.Database.Connection.Open();
            using (TransactionScope tx = new TransactionScope())
            {
                try
                {
                    db.SaveChanges();
                }
                catch (Exception ex)
                {
                    Message = "系統發生錯誤;原因:" + ex.Message;
                    return false;
                }

                if ((SaveUp.BUY_PERSON_YN == "Y"))
                {
                    try
                    {
                        REFT01Service.UpdateStatus(User.USER_KEY, "AWAT09", model.REF_KEY, SaveUp.AWARD_NO.ToString(), (db.Database.Connection) as SqlConnection);
                    }
                    catch (Exception ex)
                    {
                        Message = "系統發生錯誤;原因:" + ex.Message;
                        return false;
                    }
                }

                if (file != null && file.ContentLength > 0)
                {
                    string IMG_FILE = UpLoadFile(SaveUp.AWARD_NO, SaveUp.SCHOOL_NO, file, ref Message, model.Search.WhereSouTable);

                    if (!string.IsNullOrWhiteSpace(Message))
                    {
                        return false;
                    }

                    SaveUp.IMG_FILE = IMG_FILE;

                    try
                    {
                        db.SaveChanges();
                    }
                    catch (Exception ex)
                    {
                        Message = "系統發生錯誤;原因:" + ex.Message;
                        return false;
                    }
                }

                tx.Complete();
            }

            if (SaveUp.PUSH_YN == SharedGlobal.Y)
            {
                string BODY_TXT = "新獎品上架 品名:" + SaveUp.AWARD_NAME + "，兌換點數:" + SaveUp.COST_CASH.ToString();
                PushService.QAppt03ToAPPT03Push("AWAT09", model.REF_KEY, SaveUp.AWARD_NO.ToString(), ref db, User, out Message, BODY_TXT, "AWAI01", "ActionSaveAwat", "", "AWAI01/AwatQ02");
            }

            return true;
        }

        /// <summary>
        /// 競標自動得標處理
        /// </summary>
        /// <param name="WhereSouTable"></param>
        /// <param name="AWARD_NO"></param>
        public void SaveBidToTimeUp(string WhereSouTable = null, int? AWARD_NO = null)
        {
            int SYear, Semesters;
            SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);
            ECOOL_DEVEntities db = new ECOOL_DEVEntities();
       
            List<Tuple<string, string, int>> valuesList = new List<Tuple<string, string, int>>();
            try
            {
                if (string.IsNullOrWhiteSpace(WhereSouTable) || WhereSouTable == AWAI01SearchViewModel.SouTableVal.Student)
                {
                    string sSQL = @" Select *
                             from AWAT02 a (nolock)
                             where a.AWARD_TYPE=@AWARD_TYPE
                             and a.EDATETIME <=@Now
                             and a.QTY_STORAGE > isnull(a.QTY_TRANS,0)
                             and a.AWARD_STATUS in (@AWARD_STATUS_0,@AWARD_STATUS_y)
                             and ((@AWARD_NO is not null and a.AWARD_NO=@AWARD_NO) or (@AWARD_NO is null) )
                             and EXISTS (select top 1 * from AWAT02_B s (nolock) where s.AWARD_NO=a.AWARD_NO) ";

                    var AwardList = db.Database.Connection.Query<AWAT02>(sSQL
                           , new
                           {
                               AWARD_TYPE = Awa.AWARD_TYPE_Val.AWARD_TYPE_B,
                               AWARD_STATUS_0 = Awa.AWARD_STATUS_Val.AWARD_STATUS_0,
                               AWARD_STATUS_y = Awa.AWARD_STATUS_Val.AWARD_STATUS_Y,
                               AWARD_NO = AWARD_NO,
                               Now = DateTime.Now,
                           }).ToList();

                    foreach (AWAT02 SaveUp in AwardList)
                    {
                        sSQL = @"select top 1 * from AWAT02_B a (nolock) where a.AWARD_NO=@SaveUp_AWARD_NO order by a.BID_CASH desc";
                        var AwardBid = db.Database.Connection.Query<AWAT02_B>(sSQL
                          , new
                          {
                              SaveUp_AWARD_NO = SaveUp.AWARD_NO,
                          }).FirstOrDefault();

                        if (AwardBid != null)
                        {
                            this.CreStudentHis(SaveUp, ref db);

                            AWAT03 Cre = new AWAT03()
                            {
                                SCHOOL_NO = AwardBid.SCHOOL_NO,
                                USER_NO = AwardBid.USER_NO,
                                AWARD_NO = SaveUp.AWARD_NO,
                                TRANS_DATE = DateTime.Now,
                                SEMESTER = Convert.ToByte(Semesters),
                                SYEAR = Convert.ToByte(SYear),
                                CLASS_NO = AwardBid.CLASS_NO,
                                SEAT_NO = AwardBid.SEAT_NO,
                                NAME = AwardBid.NAME,
                                SNAME = AwardBid.SNAME,
                                TRANS_QTY = SaveUp.QTY_STORAGE,
                                TRANS_CASH = SaveUp.COST_CASH,
                                TRANS_STATUS = (byte)VAWA004.TRANS_STATUS_Val.TRANS_STATUS_UN,
                            };

                            db.AWAT03.Add(Cre);

                            SaveUp.QTY_STORAGE = (SaveUp.QTY_STORAGE ?? 0) - 1;
                            SaveUp.QTY_TRANS = (SaveUp.QTY_TRANS ?? 0) + 1;
                            SaveUp.AWARD_STATUS = Awa.AWARD_STATUS_Val.AWARD_STATUS_Y;
                            db.Entry(SaveUp).State = EntityState.Modified;
                            ECOOL_APP.CashHelper.AddCash(null, Convert.ToInt32(SaveUp.COST_CASH * -1), AwardBid.SCHOOL_NO, AwardBid.USER_NO, "AWAT02", SaveUp.AWARD_NO.ToString(), "獎品兌換", false, ref db,"","",ref valuesList);

                            db.SaveChanges();
                        }
                    }
                }

                if (string.IsNullOrWhiteSpace(WhereSouTable) || WhereSouTable == AWAI01SearchViewModel.SouTableVal.Teacher)
                {
                    string sSQL = @" Select *
                             from AWAT09 a (nolock)
                             where a.AWARD_TYPE=@AWARD_TYPE
                             and a.EDATETIME <=@Now
                             and a.QTY_STORAGE > isnull(a.QTY_TRANS,0)
                             and a.AWARD_STATUS in (@AWARD_STATUS_0,@AWARD_STATUS_y)
                             and ((@AWARD_NO is not null and a.AWARD_NO=@AWARD_NO) or (@AWARD_NO is null) )
                             and EXISTS (select top 1 * from AWAT09_B s (nolock) where s.AWARD_NO=a.AWARD_NO) ";

                    var AwardList = db.Database.Connection.Query<AWAT09>(sSQL
                           , new
                           {
                               AWARD_TYPE = Awa.AWARD_TYPE_Val.AWARD_TYPE_B,
                               AWARD_STATUS_0 = Awa.AWARD_STATUS_Val.AWARD_STATUS_0,
                               AWARD_STATUS_y = Awa.AWARD_STATUS_Val.AWARD_STATUS_Y,
                               AWARD_NO = AWARD_NO,
                               Now = DateTime.Now,
                           }).ToList();

                    foreach (var SaveUp in AwardList)
                    {
                        sSQL = @"select top 1 * from AWAT09_B a (nolock) where a.AWARD_NO=@SaveUp_AWARD_NO order by a.BID_CASH desc";
                        var AwardBid = db.Database.Connection.Query<AWAT09_B>(sSQL
                          , new
                          {
                              SaveUp_AWARD_NO = SaveUp.AWARD_NO,
                          }).FirstOrDefault();

                        if (AwardBid != null)
                        {
                            this.CreTeacherHis(SaveUp, ref db);

                            AWAT03 Cre = new AWAT03()
                            {
                                SCHOOL_NO = AwardBid.SCHOOL_NO,
                                USER_NO = AwardBid.USER_NO,
                                AWARD_NO = SaveUp.AWARD_NO,
                                TRANS_DATE = DateTime.Now,
                                SEMESTER = Convert.ToByte(Semesters),
                                SYEAR = Convert.ToByte(SYear),
                                CLASS_NO = AwardBid.CLASS_NO,
                                SEAT_NO = AwardBid.SEAT_NO,
                                NAME = AwardBid.NAME,
                                SNAME = AwardBid.SNAME,
                                TRANS_QTY = SaveUp.QTY_STORAGE,
                                TRANS_CASH = SaveUp.COST_CASH,
                                TRANS_STATUS = (byte)VAWA004.TRANS_STATUS_Val.TRANS_STATUS_UN,
                            };

                            db.AWAT03.Add(Cre);

                            SaveUp.QTY_STORAGE = (SaveUp.QTY_STORAGE ?? 0) - 1;
                            SaveUp.QTY_TRANS = (SaveUp.QTY_TRANS ?? 0) + 1;
                            SaveUp.AWARD_STATUS = Awa.AWARD_STATUS_Val.AWARD_STATUS_Y;
                            db.Entry(SaveUp).State = EntityState.Modified;

                            ECOOL_APP.CashHelper.AddCash(null, Convert.ToInt32(SaveUp.COST_CASH * -1), AwardBid.SCHOOL_NO, AwardBid.USER_NO, "AWAT09", SaveUp.AWARD_NO.ToString(), "獎品兌換", false, ref db,"","",ref valuesList);

                            db.SaveChanges();
                            
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                log4net.ILog logger = LogManager.GetLogger("競標自動得標error");
                logger.Error("Error Message:" + ex.Message.ToString());
                throw;
            }
        }

        /// <summary>
        /// StudentHis
        /// </summary>
        /// <param name="Save"></param>
        /// <param name="db"></param>
        public void CreStudentHis(AWAT02 Save, ref ECOOL_DEVEntities db)
        {
            AWAT02_HIS Cre = new AWAT02_HIS()
            {
                AWARD_NO = Save.AWARD_NO,
                SCHOOL_NO = Save.SCHOOL_NO,
                AWARD_TYPE = Save.AWARD_TYPE,
                AWARD_NAME = Save.AWARD_NAME,
                COST_CASH = Save.COST_CASH,
                QTY_STORAGE = Save.QTY_STORAGE,
                SDATETIME = Save.SDATETIME,
                EDATETIME = Save.EDATETIME,
                DESCRIPTION = Save.DESCRIPTION,
                IMG_FILE = Save.IMG_FILE,
                IMG2_FILE = Save.IMG2_FILE,
                AWARD_STATUS = Save.AWARD_STATUS,
                HOT_YN = Save.HOT_YN,

                CHG_DATE = DateTime.Now,
                READ_LEVEL = Save.READ_LEVEL,
                PASSPORT_LEVEL = Save.PASSPORT_LEVEL,
                QTY_LIMIT = Save.QTY_LIMIT,
                SHOW_DESCRIPTION_YN = Save.SHOW_DESCRIPTION_YN,
                PUSH_YN = Save.PUSH_YN,
                BID_START_PRICE = Save.BID_START_PRICE,
                BID_PER_UNIT_PRICE = Save.BID_PER_UNIT_PRICE,
                BUY_PERSON_YN = Save.BUY_PERSON_YN,
                BID_BUY_PRICE = Save.BID_BUY_PRICE
            };
            db.AWAT02_HIS.Add(Cre);
        }

        /// <summary>
        /// TeacherHis
        /// </summary>
        /// <param name="Save"></param>
        /// <param name="db"></param>
        public void CreTeacherHis(AWAT09 Save, ref ECOOL_DEVEntities db)
        {
            AWAT09_HIS Cre = new AWAT09_HIS()
            {
                AWARD_NO = Save.AWARD_NO,
                SCHOOL_NO = Save.SCHOOL_NO,
                AWARD_TYPE = Save.AWARD_TYPE,
                AWARD_NAME = Save.AWARD_NAME,
                COST_CASH = Save.COST_CASH,
                QTY_STORAGE = Save.QTY_STORAGE,
                SDATETIME = Save.SDATETIME,
                EDATETIME = Save.EDATETIME,
                DESCRIPTION = Save.DESCRIPTION,
                IMG_FILE = Save.IMG_FILE,
                IMG2_FILE = Save.IMG2_FILE,
                AWARD_STATUS = Save.AWARD_STATUS,
                HOT_YN = Save.HOT_YN,
                CHG_DATE = DateTime.Now,
                //READ_LEVEL = Save.READ_LEVEL,
                //PASSPORT_LEVEL = Save.PASSPORT_LEVEL,
                QTY_LIMIT = Save.QTY_LIMIT,
                SHOW_DESCRIPTION_YN = Save.SHOW_DESCRIPTION_YN,
                PUSH_YN = Save.PUSH_YN,
                BID_START_PRICE = Save.BID_START_PRICE,
                BID_PER_UNIT_PRICE = Save.BID_PER_UNIT_PRICE,
                BUY_PERSON_YN = Save.BUY_PERSON_YN,
                BID_BUY_PRICE = Save.BID_BUY_PRICE
            };
            db.AWAT09_HIS.Add(Cre);
        }

        /// <summary>
        /// 上傳圖片
        /// </summary>
        /// <param name="AWARD_NO"></param>
        /// <param name="SchoolNo"></param>
        /// <param name="Imgfile"></param>
        /// <param name="Message"></param>
        /// <returns></returns>
        private string UpLoadFile(int AWARD_NO, string SchoolNo, HttpPostedFileBase Imgfile, ref string Message, string WhereSouTable)
        {
            string Result = string.Empty;

            try
            {
                string newString = string.Empty;

                string[] pathstr = Path.GetFileName(Imgfile.FileName).Split('.');
                if (pathstr.Length > 0)
                {
                    MatchCollection matches = Regex.Matches(pathstr[0], @"[^\W_]+", RegexOptions.IgnoreCase);
                    foreach (Match match in matches)
                    {
                        newString += match.Value;
                    }
                    //a.組檔案名稱
                    Result = AWARD_NO.ToString() + "_" + newString + "." + pathstr[1];
                }
                else
                {
                    newString = Path.GetFileName(Imgfile.FileName);
                    //a.組檔案名稱
                    Result = AWARD_NO.ToString() + "_" + newString;
                }

                //b.組上傳資料夾路徑
                string UploadImageRoot = ConfigHelpercs.GetappSettings("ProductImgPath");

                if (string.IsNullOrWhiteSpace(UploadImageRoot)) UploadImageRoot = @"\Content\ProductIMG\";
                if (UploadImageRoot.StartsWith("~")) UploadImageRoot = UploadImageRoot.Replace("~", "");
                if (UploadImageRoot.EndsWith(@"\") == false && UploadImageRoot.EndsWith(@"/") == false) UploadImageRoot += @"\";
                if (UploadImageRoot.StartsWith(@"\") == false && UploadImageRoot.StartsWith(@"/") == false) UploadImageRoot = @"\" + UploadImageRoot;

                string imgPath = string.Empty;

                if (WhereSouTable == AWAI01SearchViewModel.SouTableVal.Teacher)
                {
                    imgPath = HttpContext.Current.Server.MapPath("~" + UploadImageRoot) + "TeachAwat" + @"\";
                }
                else
                {
                    imgPath = HttpContext.Current.Server.MapPath("~" + UploadImageRoot) + SchoolNo + @"\";
                }

                if (Directory.Exists(imgPath) == false) Directory.CreateDirectory(imgPath);

                //c.縮圖
                System.Drawing.Image image = System.Drawing.Image.FromStream(Imgfile.InputStream);
                double FixWidth = 500;
                double FixHeight = 1000;
                double rate = 1;
                if (image.Width > FixWidth || image.Height > FixHeight)
                {
                    if (image.Width > FixWidth) rate = FixWidth / image.Width;
                    else if (image.Height * rate > FixHeight) rate = FixHeight / image.Height;

                    int w = Convert.ToInt32(image.Width * rate);
                    int h = Convert.ToInt32(image.Height * rate);
                    Bitmap imageOutput = new Bitmap(image, w, h);
                    imageOutput.Save(Path.Combine(imgPath, Result), image.RawFormat);
                    imageOutput.Dispose();
                }
                else
                {
                    //直接儲存
                    Imgfile.SaveAs(Path.Combine(imgPath, Result));
                }
                image.Dispose();
            }
            catch (Exception ex)
            {
                Message = "系統發生錯誤;原因:" + ex.Message;
            }

            return Result;
        }
    }
}