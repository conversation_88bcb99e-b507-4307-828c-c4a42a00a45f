﻿@model RollCallBarcodeEditViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
}

<script src="~/Scripts/timepicker/jquery-ui-sliderAccess.js"></script>
<link href="~/Scripts/timepicker/jquery-ui-timepicker-addon.css" rel="stylesheet" />
<script src="~/Scripts/timepicker/jquery-ui-timepicker-addon.js"></script>
<script src="~/Scripts/timepicker/jquery-ui-timepicker-zh-TW.js"></script>
<script src="~/Scripts/timepicker/jquery.ui.datepicker-zh-TW.js"></script>
@*<script src="~/Scripts/moment.js"></script>*@
<script src="~/Scripts/moment.min.js"></script>
@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@using (Html.BeginForm("Edit1", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1", @class = "form-horizontal", enctype = "multipart/form-data" }))
{

    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })
    @Html.HiddenFor(m => m.Keyword)
    @Html.HiddenFor(m => m.Main.ROLL_CALL_ID)
    <style>
        optgroup[label] {
            color: indianred;
        }

        option {
            color: black;
        }
    </style>
    <div class="panel panel-ZZZ" name="TOP">
        <div class="form-group row">
            <div class="col-md-9" style="color:red">
                <B>
                    說明:<br />
                    1.本模組可以產生紙本點數，再由學生利用紙張代碼「自行」轉到自己的酷幣帳戶。<br />
                    2.建議具體事蹟要打完整，因為會出現在學生的歷程裡面。<br />
                  
                </B>
            </div>
        </div>

        <div class="panel-heading text-center">
            @Html.BarTitle(null, (string)ViewBag.Title)
        </div>

        <div class="panel-body">
            <div class="form-horizontal">
                <div class="form-group row">
                    @Html.LabelFor(m => m.Main.ROLL_CALL_NAME, htmlAttributes: new { @class = "col-md-3 control-label control-label-required" })
                    <div class="col-md-9">
                        @*@if (!ViewBag.isEdite)
                {
                    @Html.EditorFor(m => m.Main.ROLL_CALL_NAME, new { htmlAttributes = new { @class = "form-control ", @placeholder = "必填", disabled = "disabled" } })
                }
                else
                {*@
                        @Html.EditorFor(m => m.Main.ROLL_CALL_NAME, new { htmlAttributes = new { @class = "form-control form-control-required", @placeholder = "為了較好的呈現，建議8個字以內，完整內容可以打在具體事蹟裡面。" } })

                        @*}*@
                    </div>
                    @if (ViewBag.isEdite)
                    {
                        <div class="col-md-9">
                            @Html.ValidationMessageFor(m => m.Main.ROLL_CALL_NAME, "", new { @class = "text-danger" })
                        </div>}
                </div>
                <div class="form-group row">
                    @Html.Label("獎懲類別", htmlAttributes: new { @class = "col-md-3 control-label control-label-required" })
                    <div class="col-md-9">
                        @if (ViewBag.isEdite)
                        {
                            @Html.DropDownListFor(model => model.SUBJECT, (IEnumerable<SelectListItem>)ViewBag.QuickItems, new { @class = "form-control", disabled = "disabled" })
                        }
                        else
                        {
                            @Html.DropDownListFor(model => model.SUBJECT, (IEnumerable<SelectListItem>)ViewBag.QuickItems, new { @class = "form-control" })
                        }
                    </div>
                    <div class="col-md-9">

                    </div>
                </div>


                @*<div class="form-group row">
            <label class="col-md-3 control-label control-label-required"></label>
            預設文字
            <select id="quickselector" onchange="$('#Main_ROLL_CALL_DESC').val(event.target.options[event.target.selectedIndex].value)">
                <option>請選擇</option>
                <option value="品德表現">品德表現</option>
                <option value="會自動自發幫助別人。">會自動自發幫助別人。</option>
                <option value="書包、課桌椅及置物櫃都整理得很整齊。">書包、課桌椅及置物櫃都整理得很整齊。</option>
                <option value="遇見師長會主動問好，是個有禮貌的孩子。">遇見師長會主動問好，是個有禮貌的孩子。</option>
                <option value="友愛同學，並且會主動幫助同學解決困難。">友愛同學，並且會主動幫助同學解決困難。</option>
                <option value="學習表現">學習表現</option>
                <option value="學習單優良">學習單優良</option>
                <option value="認真學習">認真學習</option>
                <option value="課堂表現積極">課堂表現積極</option>
                <option value="上課表現優良">上課表現優良</option>
            </select>
        </div>*@
                <div class="form-group row">
                    @Html.Label("具體事績", htmlAttributes: new { @class = "col-md-3 control-label control-label-required" })

                    <div class="col-md-9">
                        @*@if (!ViewBag.isEdite)
                {
                    @Html.TextAreaFor(m => m.Main.ROLL_CALL_DESC, 2, 100, new { @class = "form-control", @placeholder = Html.DisplayNameFor(n => n.Main.ROLL_CALL_DESC), disabled = "disabled" })

                }
                else
                {*@
                        @Html.TextAreaFor(m => m.Main.ROLL_CALL_DESC, 2, 100, new { @class = "form-control", @placeholder = "本具體事蹟將會出現在學生歷程，建議寫詳細，例如：進行打掃工作有效率，且表現良好" })
                        @*}*@
                    </div>


                    @if (ViewBag.isEdite)
                    {
                        <div class="col-md-9">
                            @Html.ValidationMessageFor(m => m.Main.ROLL_CALL_DESC, "", new { @class = "text-danger", @placeholder = "本具體事蹟將會出現在學生歷程，建議寫詳細，例如：進行打掃工作有效率，且表現良好" })
                        </div>
                    }
                </div>
                <div class="form-group row">
                    @Html.LabelFor(m => m.Main.ROLL_CALL_DATES, htmlAttributes: new { @class = "col-md-3 control-label control-label-required" })
                    <div class="col-md-9">
                        @Html.EditorFor(m => m.Main.ROLL_CALL_DATES, new { htmlAttributes = new { @class = "form-control form-control-required", @type = "text", @placeholder = "必填" } })
                    </div>
                    <div class="col-md-9">
                        @Html.ValidationMessageFor(m => m.Main.ROLL_CALL_DATES, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group row">

                    <label class="col-md-3 control-label control-label-required" for="Main_ROLL_CALL_DATEE">點數兌換結束日期    <br />(起訖不超過2年1個月)</label>


                    <div class="col-md-9">
                        @Html.EditorFor(m => m.Main.ROLL_CALL_DATEE, new { htmlAttributes = new { @class = "form-control form-control-required", @type = "text", @placeholder = "必填" } })
                    </div>
                    <div class="col-md-9">
                        @Html.ValidationMessageFor(m => m.Main.ROLL_CALL_DATEE, "", new { @class = "text-danger" })
                    </div>
                </div>

                <div class="col-md-3 control-label control-label-required">點數</div>
                <div class="tbody" id="PrizeEditorRows">

                    @if (Model.Details == null || Model.Details?.Count() == 0)
                    {
                        if (Model.Details == null)
                        {
                            Model.Details = new List<RollCallBarcodeCashDetailViewModel>();
                        }
                        Model.Details.Add(new RollCallBarcodeCashDetailViewModel()
                        {
                            NUM = 0,
                            CASH = 0
                        });
                    }
                    <div class="col-md-3" style="text-align:center;">
                        預定點數:
                    </div>
                    <div class="col-md-6" style="text-align:left;">
                        預定份數:
                    </div>
                    @{ var num = 0;}
                    @foreach (var Item in Model.Details.ToList())
                    {
                        if (num > 0)
                        {
                            <div class="col-md-3" style="text-align:center;"></div>

                        }
                        Html.RenderPartial("_Details", Item);
                        num++;
                    }


                </div>

                <div class="col-md-12 col-xs-12 text-right">
                    <span class="input-group-btn">
                        @if (Model.Main?.ROLL_CALL_ID == null || Model.Main == null || Model.Main?.ROLL_CASH_SUM == null)
                        {
                            <button class="btn btn-info btn-sm" type="button" onclick="onAddPrizeItem()">
                                <i class="fa fa-plus-circle"></i>   增加不同點數
                            </button>
                        }
                    </span>
                </div>

                <div class="form-group row">
                    @Html.Label("總點數", htmlAttributes: new { @class = "col-md-3 control-label control-label-required" })
                    <div class="col-md-9">

                        @Html.EditorFor(m => m.Main.ROLL_CASH_SUM, new { htmlAttributes = new { @class = "form-control form-control-required", @readonly = "@readonly" } })
                    </div>
                    <div class="col-md-9">
                        @*@Html.ValidationMessageFor(m => m.Main.ROLL_CASH_NUM, "", new { @class = "text-danger" })*@
                    </div>
                </div>

                <div class="form-group">
                    @Html.Label("紙本是否出現點數", htmlAttributes: new { @class = "control-label col-md-3", @for = "CLASS_NO" })
                    <div class="col-md-9">
                        @Html.CheckBoxFor(model => model.Main.IS_SHOW) 不出現點數(只出現神秘點數的字樣)

                    </div>


                </div>

                <div class="form-group">
                    @Html.Label(" 是否給學生重複領取", htmlAttributes: new { @class = "control-label col-md-3", @for = "CLASS_NO" })
                    <div class="col-md-9">
                        @Html.CheckBoxFor(model => model.Main.IS_RemarkY) 可重複(同一活動，每位學生不限制領取幾次)
                        <br />
                        @Html.CheckBoxFor(model => model.Main.IS_RemarkF) 不可重複(同一活動，程式只讓學生領取一次)
                    </div>



                </div>


                    <div class="form-group row">
                        @Html.Label("承辦人", htmlAttributes: new { @class = "col-md-3 control-label control-label-required" })
                        <div class="col-md-9">
                            @Html.Label(user.NAME, new { htmlAttributes = new { @class = "form-control form-control-required", @placeholder = "請填數字，最小可輸入0" } })
                        </div>
                        <div class="col-md-9">
                            @*@Html.ValidationMessageFor(m => m.Main.ROLL_CASH_NUM, "", new { @class = "text-danger" })*@
                        </div>
                    </div>
                    <div class="form-group row">
                        @Html.Label("活動開設日期", htmlAttributes: new { @class = "col-md-3 control-label control-label-required" })
                        <div class="col-md-9">
                            @Html.EditorFor(m => m.Main.CRE_DATE, new { htmlAttributes = new { @class = "form-control form-control", @readonly = "@readonly" } })
                        </div>
                        <div class="col-md-9">

                            @*@Html.ValidationMessageFor(m => m.Main.ROLL_CASH_NUM, "", new { @class = "text-danger" })*@
                        </div>
                    </div>

                </div>
            </div>
        <div class="text-center">
            <hr />
            <button type="button" id="clearform" class="btn btn-default" onclick="onBack()">
                取消
            </button>
            @if (Model.Main?.ROLL_CALL_ID == null)
            {
                <button type="button" class="btn btn-default" onclick="comfireonSave()">
                    <span class="fa fa-check-circle" aria-hidden="true"></span>儲存
                </button>
            }
            else
            {
                <button type="button" class="btn btn-default" onclick="onSave()">
                    <span class="fa fa-check-circle" aria-hidden="true"></span>儲存
                </button>}
            @if (Model.Main?.ROLL_CALL_ID != null)
            {
                <button type="button" class="btn btn-default" onclick="onDel()">
                    <span class="fa fa-trash" aria-hidden="true"></span>作廢
                </button>
            }
        </div>
    </div>
   
}

@section Scripts {
    <script language="JavaScript">

        var targetFormID = '#form1';
        $(function () {
            var EDate = "@string.Format("{0:yyyy/MM/dd}", Model.Main.ROLL_CALL_DATEE)";
            var sDate = "@string.Format("{0:yyyy/MM/dd}", Model.Main.ROLL_CALL_DATES)";

            $("#Main_ROLL_CALL_DATEE").val(EDate);
            $("#Main_ROLL_CALL_DATES").val(sDate);
        });
        function comfireonSave() {

            if (window.confirm("確定儲存嗎？儲存後，點數和份數就不能再更改了喔！")) {
           $(targetFormID).attr("action", "@Url.Action("EditBarcodeSave", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
            }

        }
        function onSave()
        {

            $(targetFormID).attr("action", "@Url.Action("EditBarcodeSave", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }
        function Ondisabeld() {
            document.getElementById('Main_IS_RemarkY').removeAttribute("disabled");
            var Main_IS_RemarkYTrue = false;
            Main_IS_RemarkYTrue = $("#Main_IS_RemarkY").val();
            if (Main_IS_RemarkYTrue=='true') {

                $("#Main_IS_RemarkF").attr("disabled", "disabled");

            }
            else {
                document.getElementById('Main_IS_RemarkF').removeAttribute("disabled");
            }
        }
        function Ondisabeld1() {
            getElementById('Main_IS_RemarkY').removeAttribute("disabled");
            var Main_IS_RemarkYTrue = false;
            Main_IS_RemarkYTrue = $("#Main_IS_RemarkY").val();
            if (Main_IS_RemarkYTrue) {

                $("#Main_IS_RemarkY").attr("disabled", "disabled");

            }

        }
        function sum() {
            var cashItem = 0;
            var Main_ROLL_CASH_NUM = 0;
            var Main_ROLL_CASH_SUM = 0;
            cashItem = $("#Main_CASH").val();
            Main_ROLL_CASH_NUM = $("#Main_ROLL_CASH_NUM").val();
            Main_ROLL_CASH_SUM = cashItem * Main_ROLL_CASH_NUM;
            $('#Main_ROLL_CASH_SUM').val(Main_ROLL_CASH_SUM);
        }

        function onAddPrizeItem() {

            var trLength = $("#PrizeEditorRows .row").length
            trLength = trLength + 1;

             var data = {
               "NUM": 0,
               "CASH": 0,

             };

            $.ajax({
                url: '@Url.Action("_Details")',
                data: data,
                cache: false,
                success: function (html) {
                    $("#PrizeEditorRows").append("<div class=\"col-md-3\" style=\"text-align:center;\"></div>");
                    $("#PrizeEditorRows").append(html);
                }
            });
        }   //增加兌換
        //del 項目
        function deleteRow(Id) {
            $('#' + Id).remove();
        }
        function calate(obj,val,val1)
        {
            var item = 0;
            var item1 = 0;
            var item3 = 0;
            var item4 = "";
            item = $("#" + obj).val();
            item1 = $("#" + val).val();
            item3 = item * item1;
            item4 = "#" + val1;
            $(item4).val(item3);
            var item5 = 0;
            $("input[name='SumCash']").each(function () {


                item5 =item5+parseInt($(this).val());
                $("#Main_ROLL_CASH_SUM").val(item5);

            })

        }
        function onDel()
        {
            var OK = confirm("您確定要作廢?己經給點的，點數「無法」扣回來喔!!")

            if (OK==true)
            {
               $(targetFormID).attr("action", "@Url.Action("Del1", (string)ViewBag.BRE_NO)")
               $(targetFormID).submit();
            }
        }

        function onBack() {
            $(targetFormID).attr("action", "@Url.Action("BarcodeRollCallIndex", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

    var opt = {
        showMonthAfterYear: true,
        format: moment().format('YYYY/MM/DD'),
        showSecond: true,
        showButtonPanel: true,
        showTime: true,
        beforeShow: function () {
            setTimeout(
                function () {
                    $('#ui-datepicker-div').css("z-index", 15);
                }, 100
            );
        },
        onSelect: function (dateText, inst) {
            $('#' + inst.id).attr('value', dateText);
        }
    };

    $("#@Html.IdFor(m => m.Main.ROLL_CALL_DATES)").datepicker(opt);
    $("#@Html.IdFor(m => m.Main.ROLL_CALL_DATEE)").datepicker(opt);

    if ($("#@Html.IdFor(m => m.Main.ROLL_CALL_DATES)").val()==''
        && $("#@Html.IdFor(m => m.Main.ROLL_CALL_DATEE)").val()=='')
    {
        var Today = new Date();
        const d = new Date();
        d.setDate(d.getDate() + 14);
        $("#@Html.IdFor(m => m.Main.ROLL_CALL_DATES)").val(moment(Today).format('YYYY/MM/DD'));
        $("#@Html.IdFor(m => m.Main.ROLL_CALL_DATEE)").val(moment(d).format('YYYY/MM/DD'));
    }
    </script>
}