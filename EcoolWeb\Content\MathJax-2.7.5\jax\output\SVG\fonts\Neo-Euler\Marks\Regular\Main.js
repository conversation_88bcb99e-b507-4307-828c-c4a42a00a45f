/*
 *  /MathJax/jax/output/SVG/fonts/Neo-Euler/Marks/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax.SVG.FONTDATA.FONTS.NeoEulerMathJax_Marks={directory:"Marks/Regular",family:"NeoEulerMathJax_Marks",id:"NEOEULERMARKS",32:[0,0,333,0,0,""],160:[0,0,333,0,0,""],773:[615,-570,0,-445,-53,"-53 570h-392v45h392v-45"],783:[683,-502,0,-497,-154,"-173 502l19 25l-107 123c-17 20 -39 33 -55 33c-17 0 -31 -15 -31 -33c0 -16 10 -29 39 -50zM-323 502l19 25l-107 123c-17 20 -39 33 -55 33c-17 0 -31 -15 -31 -33c0 -16 10 -29 39 -50"],785:[671,-513,0,-390,-108,"-108 513h-30c-3 57 -49 98 -111 98s-108 -41 -111 -98h-30c2 44 8 66 24 93c24 42 66 65 117 65c54 0 97 -25 121 -71c13 -26 18 -46 20 -87"],803:[-93,193,0,-225,-125,"-125 -143c0 -30 -20 -50 -50 -50s-50 20 -50 50s20 50 51 50c29 0 49 -21 49 -50"],804:[-93,193,0,-399,-100,"-100 -143c0 -30 -20 -50 -50 -50s-50 20 -50 50s20 50 51 50c29 0 49 -21 49 -50zM-299 -143c0 -30 -20 -50 -50 -50s-50 20 -50 50s20 50 51 50c29 0 49 -21 49 -50"],805:[-43,243,0,-349,-149,"-149 -143c0 -56 -45 -100 -100 -100c-56 0 -100 44 -100 101c0 55 45 99 102 99c53 0 98 -45 98 -100zM-187 -143c0 38 -25 64 -61 64c-38 0 -63 -25 -63 -63c0 -40 24 -65 62 -65c37 0 62 26 62 64"],814:[-78,207,0,-388,-83,"-83 -78c-6 -84 -65 -129 -153 -129s-147 45 -152 129h32c4 -47 53 -80 120 -80c68 0 117 33 120 80h33"],815:[-78,207,0,-388,-83,"-83 -207h-32c-4 47 -53 80 -120 80c-68 0 -117 -33 -120 -80h-33c6 84 65 129 153 129s147 -45 152 -129"],816:[-95,196,0,-389,-68,"-288 -96c53 -3 99 -45 136 -45c28 0 40 14 60 45l24 -13c-20 -43 -48 -87 -105 -87c-55 0 -101 45 -139 45c-24 0 -38 -20 -53 -45l-24 14c32 66 63 88 101 86"],817:[-116,169,0,-405,-93,"-93 -169h-312v53h312v-53"],818:[-120,165,0,-445,-53,"-53 -165h-392v45h392v-45"],8246:[782,-422,433,30,386,"229 723c-4 9 -7 15 -7 22c0 20 19 37 40 37c20 0 33 -13 38 -32l83 -306c0 -1 3 -9 3 -9c0 -8 -19 -13 -24 -13c-4 0 -5 2 -9 11zM36 723c-3 9 -6 15 -6 22c0 20 18 37 40 37c20 0 32 -13 37 -32l83 -306c0 -1 3 -9 3 -9c0 -8 -18 -13 -24 -13c-4 0 -5 2 -9 11"],8247:[782,-422,626,30,578,"421 723c-3 9 -6 15 -6 22c0 20 18 37 40 37c19 0 32 -13 37 -32l83 -306c0 -1 3 -9 3 -9c0 -8 -18 -13 -24 -13c-4 0 -5 2 -9 11zM229 723c-4 9 -7 15 -7 22c0 20 19 37 40 37c20 0 33 -13 38 -32l83 -306c0 -1 3 -9 3 -9c0 -8 -19 -13 -24 -13c-4 0 -5 2 -9 11zM36 723 c-3 9 -6 15 -6 22c0 20 18 37 40 37c20 0 32 -13 37 -32l83 -306c0 -1 3 -9 3 -9c0 -8 -18 -13 -24 -13c-4 0 -5 2 -9 11"],8406:[750,-479,287,-131,287,"287 595h-346l79 -95l-21 -21l-130 135l130 136l21 -21l-79 -94h346v-40"],8411:[642,-542,0,-599,-100,"-499 592c0 -30 -20 -50 -50 -50s-50 20 -50 50s20 50 51 50c29 0 49 -21 49 -50zM-299 592c0 -30 -20 -50 -50 -50s-50 20 -50 50s20 50 51 50c29 0 49 -21 49 -50zM-100 592c0 -30 -20 -50 -50 -50s-50 20 -50 50s20 50 51 50c29 0 49 -21 49 -50"],8412:[642,-542,0,-799,-100,"-699 592c0 -30 -20 -50 -50 -50s-50 20 -50 50s20 50 51 50c29 0 49 -21 49 -50zM-499 592c0 -30 -20 -50 -50 -50s-50 20 -50 50s20 50 51 50c29 0 49 -21 49 -50zM-299 592c0 -30 -20 -50 -50 -50s-50 20 -50 50s20 50 51 50c29 0 49 -21 49 -50zM-100 592 c0 -30 -20 -50 -50 -50s-50 20 -50 50s20 50 51 50c29 0 49 -21 49 -50"],8413:[716,216,1000,55,944,"944 250c0 -259 -200 -466 -445 -466c-242 0 -444 205 -444 466c0 259 200 466 445 466c242 0 444 -205 444 -466zM500 -176c221 0 404 188 404 426c0 236 -181 426 -405 426c-221 0 -404 -188 -404 -426c0 -236 181 -426 405 -426"],8417:[750,-479,449,0,449,"377 635l-79 94l21 21l130 -136l-130 -135l-21 21l79 95h-305l79 -95l-21 -21l-130 135l130 136l21 -21l-79 -94h305"],8430:[50,221,287,-131,287,"287 -105h-346l79 -95l-21 -21l-130 135l130 136l21 -21l-79 -94h346v-40"],8431:[50,221,287,0,418,"346 -105h-346v40h346l-79 94l21 21l130 -136l-130 -135l-21 21"]};MathJax.Ajax.loadComplete(MathJax.OutputJax.SVG.fontDir+"/Marks/Regular/Main.js");
