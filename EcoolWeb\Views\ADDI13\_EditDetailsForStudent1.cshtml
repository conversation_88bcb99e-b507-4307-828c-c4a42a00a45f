﻿@model BarcodeEditPeopleViewModel
@{
    Layout = "~/Views/Shared/_LayoutSEO.cshtml";
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    bool IsAllList = (this.Request.CurrentExecutionFilePath.Contains("ADDTALLListDetails"));
}
@Html.Partial("_Notice")
<script src="~/Scripts/Pring.js"></script>
<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>
@*<center style="white-space: nowrap;font-size: 30pt;font-weight: bold;">
        程式修正中，請12月20日再兌換，造成不便，請包涵
    </center>

    <img src="~/Content/images/Sorry.PNG" style="width:50%" class="img-responsive " alt="Responsive image" />*@
@if (IsAllList == false)
{
    <div class="row">
        @*   <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 font_Cash" style="white-space:nowrap">
                <img src="~/Content/img/web-revise-secretary-08new.png" style="max-height:40px" class="imgEZ" />
                酷幣點數：@Model.CASH_AVAILABLE
                @if (user != null && user.USER_TYPE == ECOOL_APP.EF.UserType.Teacher)
                {
                    <span class="text-danger">
                        &nbsp;&nbsp;
                        <img src="~/Content/img/web-revise-secretary-08new.png" style="max-height:40px" class="imgEZ" />
                        本月已發放點數: @Model.Month_Given_Cash 。@Model.Special__Cash_Limit
                    </span>
                }
            </div>*@

        @*@if (@Model.BOOKS > 0)
            {
                <div class="col-lg-4 col-md-4  col-sm-6 col-xs-12 font_Cash">
                    <img src="~/Content/img/books.png" style="max-width:40px;" class="imgEZ" />
                    <a id="BorrowIndexSesem" href='@Url.Action("BorrowIndex", (string)ViewBag.BRE_NO)'> <text>本學期借閱本數：</text>@Model.BOOKS</a>
                </div>
            }
            @if (@Model.BOOKS_MONTH >= 0)
            {
                <div class="col-lg-4 col-md-4  col-sm-6 col-xs-12 font_Cash">
                    <img src="~/Content/img/books.png" style="max-width:40px;" class="imgEZ" />
                    <a id="BorrowIndex" href='@Url.Action("BorrowIndex", (string)ViewBag.BRE_NO)'><text>本月借閱本數：</text>@Model.BOOKS_MONTH</a>
                </div>
            }*@
        @if (user != null)
        {
            if (user.USER_TYPE == ECOOL_APP.EF.UserType.Student)
            {
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 font_Cash">
                    @*<img src="~/Content/img/monkey-01.png" style="max-width:40px;max-height:40px;" class="imgEZ" />
                    <a href='@Url.Action("ArrivedChance2", "Home")'>
                        <text>好運次數：</text>

                        @user.Chance_ARRIVED_CASH
                    </a>*@
                </div>
            }
        }
    </div>
}
<div class="row">
    <div class="col-sm-2 col-xs-12">
        <div>
            @*@if (!string.IsNullOrEmpty(Model.PlayerUrl))
            {
                <img src="@(Url.Content(Model.PlayerUrl)+ "?refreshCache=" + DateTime.Now.ToString("mmddss"))" class="imgEZ" style="margin-top:30px;max-width:90%;" />
            }*@
        </div>

    </div>


    @if (Model != null)
    {

        //using (Html.BeginForm("_EditDetails3", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", id = "form1", @AutoComplete = "Off", @target = "_self" }))
        //{
        @Html.HiddenFor(m => m.SCHOOL_NO)
        @Html.HiddenFor(m => m.SHORT_NAME)
        @Html.HiddenFor(m => m.USER_NO)
        @Html.HiddenFor(m => m.NAME)
        @Html.HiddenFor(m => m.GRADE)
        @Html.HiddenFor(m => m.CLASS_NO)
        @Html.HiddenFor(m => m.SEAT_NO)
        @Html.HiddenFor(m => m.CARD_NO)
        @Html.HiddenFor(m => m.BarCode)
        @Html.HiddenFor(m => m.ROLL_CALL_NAME)
        @Html.HiddenFor(m => m.ROLL_CALL_ID)
        @Html.HiddenFor(m => m.CASH)
        @Html.HiddenFor(m => m.txtUSER_NO)
        @Html.HiddenFor(m => m.txtPASSWORD)

    <div class="py-3 col-lg-12">


        <div class="alert alert-danger h3 mb-1 text-black text-center" role="alert">
            <img class="d-block mx-auto" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAC8AAAApCAYAAAClfnCxAAAAAXNSR0IArs4c6QAAAARzQklUCAgICHwIZIgAAAR2SURBVFhH1ZlfaBxVFMbP2XUTTTYhyaYgSBVLa4s1pKG1UlN2Jr60lRaFUpCWYmmrQilKXgyoRbCg5qmirQ9RKw2oT0WQYpoXdzYkpfSPbciLsaEqfdGa7IZsUpOuO8dvluyYNe7OndkbG+chgZ1zv/Obb8+999xZJs1XOh6P55gPQ/YpYn4Y/39hootk2z2xgYELOtNBV981bhjdzPx6SUWRd2LJ5Nu6MmqDT5lmlxC9rwDWGbOsDxTiPEO0wKfi8RYJha4hW9gzI1Euwry+PpEYVYgtG6IFftw0L0NokzKMyAWUT7tyfInAiuEn4vGDFAp95htE5CAe4HPf4xYMqAg+bZoNNtFP0GsIADEeIlrTaFmTAcbmh1QEP2EYPVgOXwqanER64P4rQccHhp80zU05ostBE+fHiUiYeXODZV0JohMIXvbsCadu374G11uCJC0aIzLSlEy2AgQrrb8rEPyEab6GNFrW6vkv4NXmZPIjf+gBan7aNB+cFfkRO2md32Sl4lE9mSrmVfWWNe5H07fzcP0rJHjBTxKVWDzAl3B/n0psIcYXfNowOmzm71QS3LdhA0VaWyk7PEx/Xr+uMoTQvLX7ad6U4WXjxkgqGv0Bk3SVCknjuXPEtbUk09OU3rVLZYgzY0djmUwLX72aVRmgDI8W4C0EH1cRdVyvP3HCDZ3q7FR2H+XzBsrnPZU8SvBoAVai8bqB4GoV0Qrh/2CRtSifW1651OAN41uUyw4vscL9SuDnl85v4P5zXvk84VEuzyPoay+hhfcrhXe04P6z2Lz6yuUtCy9btjyQqqoahesr/2t4tA63mmpq1nBf31yp3GXhPY91JVR1OD/v/rtw/03f8FMdHWuztj0C1yN+XHdidcHD/Ww4l1vXMDh4898YSjqPdncI4E/7BdcKDzEsnQlM3meU4bGT7sdO2hsEXDf8fPnsRfk4bUnRtcj539vb60KRiPM1NS8XeOy8v0o2+9iKoaHMQqZF8Jikp9AxHgkKXhjntgczM5TeubNSOad+PsSpy2nF3asIPn86ErmEWvdc/71owqtXU9XWrXR3cJByY2Ne4Sr3c2zbbU0DAyOFYBcSXw2nDGNYy+lIBSVADBivNFvWk4vgAX5EmE8F0Fw0hKNRqt62zXV+rr8/311quUReRvl8kp/Izp8p02y+K3JT1+moevt2qu3qcllnurtp7vx5LewQmcQrk0edVyZ5eEzSXoDv16Vec/Qo3b97tys3e/Ys3Tl5Upe8M3lPw/1DjFfSbXYo9L0+ZaIIJmrd8b9b/8yxY5TFxNV5wf02hutfwPW9OoUdrSVYbYoQsfP2Mg7UP+PTR3TDL7UeVp4xB973y56lBlPRh/N3/r/Oi9xw4D/Fkx5SedrlFAPnP2b8HPME6sbdcpcTYDkWse3H8+t84B8I7tGTwuwDaBPOuL0N2oMd+KHgDJbNFfeIyTMtoH8D34uxRKLfCS7qHvFWrGYiGj2M5mwz7j30z/ue6ksTIM5hHJvSxUbm02xZs4U0fwEE+rxdAiYhiAAAAABJRU5ErkJggg==" height="40" alt="">
            <span class="d-block pt-3">
                ○○○○○○○○，領取點數失敗! <br />
             
            </span>
        </div>
        <div class="text-center pt-3">
            <button type="button" class="btn btn-default mr-0" onclick="GOBack()">
                返回領取兌換
            </button>
            <br />
            5秒後系統自動轉跳
        </div>
    </div>
        //}

    }

</div>

@section Scripts {
<script language="JavaScript">
        var targetFormID = '#form1';
        $(document).ready(function () {

            //     var l = 0;
            //     l = $("#StatusMessageDiv").length;
            ////     $(".containerEZ")[1].remove();

            //     console.log($(".containerEZ").length)
            //     if ($(".containerEZ").length > 1) {

            //         $(".containerEZ")[0].remove();
            //     }
            if ($(".containerEZ").length > 1) {
                $(".containerEZ")[1].remove();

            }
        });
        $(document).ready(function () {
            $("#PieChartbtn").colorbox({ iframe: true, width: "80%", height: "80%", opacity: 0.82 });
        });
        $(document).ready(function () { $("#MyMOMO").colorbox({ iframe: true, width: "100%", height: "100%", opacity: 0.82 }); });
        $(document).ready(function () {
            $("#Statisticalbtn").colorbox({ iframe: true, width: "80%", height: "80%", opacity: 0.82 });
        });

        $(document).ready(function () {
            $("#MyPHOTO").colorbox({ iframe: true, width: "80%", height: "80%", opacity: 0.82 });
        });
        $(document).ready(function () {
            $(".groupWRITING_NO").colorbox({ iframe: true, opacity: 0.5, width: "99%", height: "99%", rel: 'groupWRITING_NO' });
        });

        $(document).ready(function () {
            $("#MyAPPLY").colorbox({ iframe: true, opacity: 0.5, width: "99%", height: "99%" });
            $("#BorrowIndexSesem,#BorrowIndex").colorbox({ iframe: true, opacity: 0.5, width: "95%", height: "95%" });
            $("#GameCash").colorbox({ iframe: true, opacity: 0.5, width: "95%", height: "95%" });
            $("#MyBOOK").colorbox({ iframe: true, opacity: 0.5, width: "95%", height: "95%" });
        });
                    function GOBack() {
                        window.location = "@Url.Action("GetStudentCashIndex", "ADDI13",new { SCHOOL_NO1=Model.SCHOOL_NO})";
                    }

</script>
}