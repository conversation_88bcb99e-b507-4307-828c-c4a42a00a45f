﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'table', 'fi', {
	border: '<PERSON><PERSON> paksuus',
	caption: '<PERSON><PERSON><PERSON><PERSON>',
	cell: {
		menu: 'Solu',
		insertBefore: 'Lisää solu eteen',
		insertAfter: 'Lisää solu perään',
		deleteCell: 'Poista solut',
		merge: 'Yhdistä solut',
		mergeRight: 'Yhdistä oikealla olevan kanssa',
		mergeDown: 'Yhdistä alla olevan kanssa',
		splitHorizontal: 'Jaa solu vaakasuunnassa',
		splitVertical: 'Jaa solu pystysuunnassa',
		title: 'Solun ominaisuudet',
		cellType: 'Solun tyyppi',
		rowSpan: 'Rivin jatkuvuus',
		colSpan: 'Solun jatkuvuus',
		wordWrap: 'Rivitys',
		hAlign: 'Horisontaali kohdistus',
		vAlign: 'Vertikaali kohdistus',
		alignBaseline: '<PERSON><PERSON> (teksti)',
		bgColor: 'Taustan väri',
		borderColor: '<PERSON><PERSON>n väri',
		data: 'Data',
		header: 'Ylätunniste',
		yes: 'Kyllä',
		no: 'Ei',
		invalidWidth: 'Solun leveyden täytyy olla numero.',
		invalidHeight: 'Solun korkeuden täytyy olla numero.',
		invalidRowSpan: 'Rivin jatkuvuuden täytyy olla kokonaisluku.',
		invalidColSpan: 'Solun jatkuvuuden täytyy olla kokonaisluku.',
		chooseColor: 'Valitse'
	},
	cellPad: 'Solujen sisennys',
	cellSpace: 'Solujen väli',
	column: {
		menu: 'Sarake',
		insertBefore: 'Lisää sarake vasemmalle',
		insertAfter: 'Lisää sarake oikealle',
		deleteColumn: 'Poista sarakkeet'
	},
	columns: 'Sarakkeet',
	deleteTable: 'Poista taulu',
	headers: 'Ylätunnisteet',
	headersBoth: 'Molemmat',
	headersColumn: 'Ensimmäinen sarake',
	headersNone: 'Ei',
	headersRow: 'Ensimmäinen rivi',
	invalidBorder: 'Reunan koon täytyy olla numero.',
	invalidCellPadding: 'Solujen sisennyksen täytyy olla numero.',
	invalidCellSpacing: 'Solujen välin täytyy olla numero.',
	invalidCols: 'Sarakkeiden määrän täytyy olla suurempi kuin 0.',
	invalidHeight: 'Taulun korkeuden täytyy olla numero.',
	invalidRows: 'Rivien määrän täytyy olla suurempi kuin 0.',
	invalidWidth: 'Taulun leveyden täytyy olla numero.',
	menu: 'Taulun ominaisuudet',
	row: {
		menu: 'Rivi',
		insertBefore: 'Lisää rivi yläpuolelle',
		insertAfter: 'Lisää rivi alapuolelle',
		deleteRow: 'Poista rivit'
	},
	rows: 'Rivit',
	summary: 'Yhteenveto',
	title: 'Taulun ominaisuudet',
	toolbar: 'Taulu',
	widthPc: 'prosenttia',
	widthPx: 'pikseliä',
	widthUnit: 'leveysyksikkö'
} );
