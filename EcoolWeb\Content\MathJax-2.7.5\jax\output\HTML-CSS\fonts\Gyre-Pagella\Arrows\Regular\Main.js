/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Gyre-<PERSON>lla/Arrows/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.GyrePagellaMathJax_Arrows={directory:"Arrows/Regular",family:"GyrePagellaMathJax_Arrows",testString:"\u00A0\u219F\u21A1\u21A4\u21A5\u21A7\u21B2\u21B3\u21B4\u21B5\u21C5\u21D6\u21D7\u21D8\u21D9",32:[0,0,250,0,0],160:[0,0,250,0,0],8607:[673,162,460,80,380],8609:[662,173,460,80,380],8612:[400,-100,920,80,840],8613:[635,125,460,80,380],8615:[625,135,460,80,380],8626:[572,73,530,80,450],8627:[572,72,530,80,450],8628:[471,-19,723,80,643],8629:[531,31,613,80,533],8645:[635,135,800,80,720],8662:[522,100,782,80,702],8663:[522,100,782,80,702],8664:[600,22,782,80,702],8665:[600,22,782,80,702],8668:[400,-100,920,80,840],8678:[450,-50,1047,80,967],8679:[705,182,560,80,480],8680:[450,-50,1047,80,967],8681:[682,205,560,80,480],8691:[705,205,560,80,480],8693:[635,135,800,80,720],8694:[740,240,920,80,840],10228:[568,68,1130,80,1050],10235:[400,-100,1370,80,1290],10237:[450,-50,1445,80,1365],10238:[450,-50,1445,80,1365],10239:[400,-100,1370,80,1290],10502:[450,-50,995,80,915],10503:[450,-50,995,80,915]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"GyrePagellaMathJax_Arrows"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Arrows/Regular/Main.js"]);
