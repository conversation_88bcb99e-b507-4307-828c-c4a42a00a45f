(function(factory){if (typeof define === 'function' && define.amd){define(['jquery'], factory);} else if (typeof module === 'object' && typeof module.exports === 'object'){module.exports = factory(require('jquery'));} else {factory(jQuery);}}(function(jQuery){

/*! Widget: stickyHeaders - updated 9/27/2017 (v2.29.0) */
!function(T,w){"use strict";var S=T.tablesorter||{};function x(e,s){var t=isNaN(s.stickyHeaders_offset)?T(s.stickyHeaders_offset):[];return t.length?t.height()||0:parseInt(s.stickyHeaders_offset,10)||0}T.extend(S.css,{sticky:"tablesorter-stickyHeader",stickyVis:"tablesorter-sticky-visible",stickyHide:"tablesorter-sticky-hidden",stickyWrap:"tablesorter-sticky-wrapper"}),S.addHeaderResizeEvent=function(e,s,t){if((e=T(e)[0]).config){var i=T.extend({},{timer:250},t),c=e.config,l=c.widgetOptions,r=function(e){var s,t,i,r,a,d,n=c.$headers.length;for(l.resize_flag=!0,t=[],s=0;s<n;s++)r=(i=c.$headers.eq(s)).data("savedSizes")||[0,0],a=i[0].offsetWidth,d=i[0].offsetHeight,a===r[0]&&d===r[1]||(i.data("savedSizes",[a,d]),t.push(i[0]));t.length&&!1!==e&&c.$table.triggerHandler("resize",[t]),l.resize_flag=!1};if(clearInterval(l.resize_timer),s)return l.resize_flag=!1;r(!1),l.resize_timer=setInterval(function(){l.resize_flag||r()},i.timer)}},S.addWidget({id:"stickyHeaders",priority:54,options:{stickyHeaders:"",stickyHeaders_appendTo:null,stickyHeaders_attachTo:null,stickyHeaders_xScroll:null,stickyHeaders_yScroll:null,stickyHeaders_offset:0,stickyHeaders_filteredToTop:!0,stickyHeaders_cloneId:"-sticky",stickyHeaders_addResizeEvent:!0,stickyHeaders_includeCaption:!0,stickyHeaders_zIndex:2},format:function(e,t,y){if(!(t.$table.hasClass("hasStickyHeaders")||0<=T.inArray("filter",t.widgets)&&!t.$table.hasClass("hasFilters"))){var s,i,r,a,k=t.$table,g=T(y.stickyHeaders_attachTo||y.stickyHeaders_appendTo),d=t.namespace+"stickyheaders ",H=T(y.stickyHeaders_yScroll||y.stickyHeaders_attachTo||w),n=T(y.stickyHeaders_xScroll||y.stickyHeaders_attachTo||w),c=k.children("thead:first").children("tr").not(".sticky-false").children(),u=k.children("tfoot"),l=x(0,y),b=k.parent().closest("."+S.css.table).hasClass("hasStickyHeaders")?k.parent().closest("table.tablesorter")[0].config.widgetOptions.$sticky.parent():[],_=b.length?b.height():0,o=y.$sticky=k.clone().addClass("containsStickyHeaders "+S.css.sticky+" "+y.stickyHeaders+" "+t.namespace.slice(1)+"_extra_table").wrap('<div class="'+S.css.stickyWrap+'">'),v=o.parent().addClass(S.css.stickyHide).css({position:g.length?"absolute":"fixed",padding:parseInt(o.parent().parent().css("padding-left"),10),top:l+_,left:0,visibility:"hidden",zIndex:y.stickyHeaders_zIndex||2}),p=o.children("thead:first"),m="",f=function(e,s){var t,i,r,a,d,n=e.filter(":visible"),c=n.length;for(t=0;t<c;t++)a=s.filter(":visible").eq(t),i="border-box"===(d=n.eq(t)).css("box-sizing")?d.outerWidth():"collapse"===a.css("border-collapse")?w.getComputedStyle?parseFloat(w.getComputedStyle(d[0],null).width):(r=parseFloat(d.css("border-width")),d.outerWidth()-parseFloat(d.css("padding-left"))-parseFloat(d.css("padding-right"))-r):d.width(),a.css({width:i,"min-width":i,"max-width":i})},z=function(e){return!1===e&&b.length?k.position().left:g.length?parseInt(g.css("padding-left"),10)||0:k.offset().left-parseInt(k.css("margin-left"),10)-T(w).scrollLeft()},C=function(){v.css({left:z(),width:k.outerWidth()}),f(k,o),f(c,a)},h=function(e){if(k.is(":visible")){_=b.length?b.offset().top-H.scrollTop()+b.height():0;var s,t=k.offset(),i=x(0,y),r=T.isWindow(H[0]),a=r?H.scrollTop():b.length?parseInt(b[0].style.top,10):H.offset().top,d=g.length?a:H.scrollTop(),n=y.stickyHeaders_includeCaption?0:k.children("caption").height()||0,c=d+i+_-n,l=k.height()-(v.height()+(u.height()||0))-n,o=c>t.top&&c<t.top+l?"visible":"hidden",p="visible"===o?S.css.stickyVis:S.css.stickyHide,f=!v.hasClass(p),h={visibility:o};g.length&&(f=!0,h.top=r?c-g.offset().top:g.scrollTop()),(s=z(r))!==parseInt(v.css("left"),10)&&(f=!0,h.left=s),h.top=(h.top||0)+(!r&&b.length?b.height():i+_),f&&v.removeClass(S.css.stickyVis+" "+S.css.stickyHide).addClass(p).css(h),(o!==m||e)&&(C(),m=o)}};if(g.length&&!g.css("position")&&g.css("position","relative"),o.attr("id")&&(o[0].id+=y.stickyHeaders_cloneId),o.find("> thead:gt(0), tr.sticky-false").hide(),o.find("> tbody, > tfoot").remove(),o.find("caption").toggle(y.stickyHeaders_includeCaption),a=p.children().children(),o.css({height:0,width:0,margin:0}),a.find("."+S.css.resizer).remove(),k.addClass("hasStickyHeaders").bind("pagerComplete"+d,function(){C()}),S.bindEvents(e,p.children().children("."+S.css.header)),y.stickyHeaders_appendTo?T(y.stickyHeaders_appendTo).append(v):k.after(v),t.onRenderHeader)for(i=(r=p.children("tr").children()).length,s=0;s<i;s++)t.onRenderHeader.apply(r.eq(s),[s,t,o]);n.add(H).unbind("scroll resize ".split(" ").join(d).replace(/\s+/g," ")).bind("scroll resize ".split(" ").join(d),function(e){h("resize"===e.type)}),t.$table.unbind("stickyHeadersUpdate"+d).bind("stickyHeadersUpdate"+d,function(){h(!0)}),y.stickyHeaders_addResizeEvent&&S.addHeaderResizeEvent(e),k.hasClass("hasFilters")&&y.filter_columnFilters&&(k.bind("filterEnd"+d,function(){var e=T(document.activeElement).closest("td"),s=e.parent().children().index(e);v.hasClass(S.css.stickyVis)&&y.stickyHeaders_filteredToTop&&(w.scrollTo(0,k.position().top),0<=s&&t.$filters&&t.$filters.eq(s).find("a, select, input").filter(":visible").focus())}),S.filter.bindSearch(k,a.find("."+S.css.filter)),y.filter_hideFilters&&S.filter.hideFilters(t,o)),y.stickyHeaders_addResizeEvent&&k.bind("resize"+t.namespace+"stickyheaders",function(){C()}),h(!0),k.triggerHandler("stickyHeadersInit")}},remove:function(e,s,t){var i=s.namespace+"stickyheaders ";s.$table.removeClass("hasStickyHeaders").unbind("pagerComplete resize filterEnd stickyHeadersUpdate ".split(" ").join(i).replace(/\s+/g," ")).next("."+S.css.stickyWrap).remove(),t.$sticky&&t.$sticky.length&&t.$sticky.remove(),T(w).add(t.stickyHeaders_xScroll).add(t.stickyHeaders_yScroll).add(t.stickyHeaders_attachTo).unbind("scroll resize ".split(" ").join(i).replace(/\s+/g," ")),S.addHeaderResizeEvent(e,!0)}})}(jQuery,window);return jQuery;}));
