/*
 *  /MathJax/jax/output/SVG/fonts/Neo-Euler/Alphabets/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax.SVG.FONTDATA.FONTS.NeoEulerMathJax_Alphabets={directory:"Alphabets/Regular",family:"NeoEulerMathJax_Alphabets",id:"NEOEULERALPHABETS",32:[0,0,333,0,0,""],160:[0,0,333,0,0,""],8486:[689,2,875,25,844,"835 60l9 -9l-25 -53c-115 9 -231 9 -346 1l9 58c47 11 90 36 124 70c27 26 43 61 54 97c14 47 19 96 19 144c0 84 -22 169 -82 229c-40 40 -97 57 -153 57c-62 0 -126 -17 -170 -62c-31 -30 -51 -69 -65 -110c-16 -49 -23 -101 -23 -153c0 -81 21 -164 79 -223 c29 -29 69 -45 110 -53l-21 -55c-110 10 -219 14 -329 6l17 61c61 -3 137 -6 190 -11l6 15c-26 14 -50 30 -71 51c-24 25 -40 56 -51 90c-13 41 -18 85 -18 128c0 95 28 190 96 257c70 71 171 94 269 94c44 0 89 -5 131 -19c35 -12 68 -29 94 -55c60 -60 81 -146 81 -230 c0 -93 -23 -189 -90 -256c-29 -28 -58 -51 -95 -66v-12c85 4 181 9 251 9"]};MathJax.Ajax.loadComplete(MathJax.OutputJax.SVG.fontDir+"/Alphabets/Regular/Main.js");
