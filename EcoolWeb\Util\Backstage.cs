﻿using com.ecool.service;
using ECOOL_APP.com.ecool.Models.entity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace EcoolWeb.Util
{
    public static class Backstage
    {
        public static bool Check(string Authority, object UrlReferrer,bool isBackstage, string FirstPage=null)
        {
            string Back = string.Empty;

            //是否從左邊清單點入
            bool isFirstPage = (string.IsNullOrWhiteSpace(FirstPage)==false) ? true : false;

            if (isBackstage && isFirstPage)
            {
                return true;
            }

            if (UrlReferrer==null)
            {
                return false;
            }

            string StrUrlReferrer = UrlReferrer.ToString();

            if (string.IsNullOrWhiteSpace(StrUrlReferrer) ==false)
            {
                Back = StrUrlReferrer.Replace(Authority, "");
                Back = Back.Replace(@"http://","");
                string[] Arr = Back.Split('/');

                if (Arr.Length>2)
                {
                    Back = Arr[1];
                }
                else
                {
                    Back = Arr[0];
                }

                 var dZZT01 = BreadcrumbService.GetBreadcrumbQUERY(Back).Where(a=>a.BRE_TYPE=="3").FirstOrDefault();

                if (dZZT01!=null)
                {
                    if (dZZT01.BRE_NO=="ZZZ" || dZZT01.BRE_NO == "ACC")
                    {
                        return true;
                    }
                }
            }

            return false;
        }
    }
}