﻿@model GameIndexViewModel
@{
    ViewBag.Title = ViewBag.Panel_Title;
    Layout = "~/Views/Shared/_LayoutGame.cshtml";
    EcoolWeb.Models.UserProfileHelper.SetLayoutCookieData(Layout);
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@using (Html.BeginForm("Index1", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1", enctype = "multipart/form-data" }))
{
    @Html.AntiForgeryToken()
   
    <div id="PageContent">
        @Html.Action("_PageContent1", (string)ViewBag.BRE_NO)
    </div>
}

<div class="modal fade bs-example-modal-lg" id="mySmallModal" tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel" style="z-index:9999">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">

            <div class="input-group">
                <span class="input-group-btn">
                    <button type="button" id="id_copy"
                            data-clipboard-target="#id_text"
                            data-clipboard-action="copy" onclick="OnCopy()">
                        點擊複製
                    </button>
                </span>
                <div id="id_text" style="word-break: break-all;"></div>
                <div id="success" style="display:none">已複製</div>
                <input id="copyStr" type="hidden" value="">
            </div><!-- /input-group -->
        </div>
    </div>
</div>

@section Scripts {
    <!-- clipboard.js v1.7.1 複製內容到剪貼簿的小工具 -->
    <script src="~/Scripts/clipboard.min.js"></script>
    <script language="JavaScript">
        var targetFormID = '#form1'

    function onWinOpenYoutubeUrlLink(GAME_NO) {
        var LevelUrl = '@ECOOL_APP.UrlCustomHelper.GetOwnWebUri()/@Url.Action("FastLevel", (string)ViewBag.BRE_NO)?ActionName=PassMode&GAME_NO=' + GAME_NO + '&Title=闖關地圖'

            $('#id_text').text(LevelUrl)
            $('#copyStr').val(LevelUrl)

            $('#success').hide()
            $('#id_text').show()

            if ($('#mySmallModal').is(':visible') == false) {
                $('#mySmallModal').modal('show');
            }
        }
        function back() {
            var schoolNO = "";

                schoolNO =@(user?.SCHOOL_NO);

            var LevelUrl = '@ECOOL_APP.UrlCustomHelper.GetOwnWebUri()/@Url.Action("TeacherIndex", "Home")/'+schoolNO

            window.location.replace(LevelUrl)
        }
        function GetDetail(id, GAME_TYPE)
        {
           		  $('#@Html.IdFor(m=>m.GAME_NO)').val(id)
                   $(targetFormID).attr("action", "@Url.Action("GetWinnerItem", (string)ViewBag.BRE_NO)")



            $(targetFormID).submit();
        }
      //複製快速連結
        function OnCopy() {
                    var clipboard = new Clipboard("#id_copy");
                    clipboard.on("success", function (element) {//複製成功的回調
                        console.info("複製成功，複製內容：    " + element.text);
                $('#success').show()
                        $('#id_text').hide()

                setTimeout('HidemySmallModal()', 2000);

                    });
                    clipboard.on("error", function (element) {//複製失敗的回調
                        console.info(element);

                        var $input = $('#copyStr');
                $input.val();
                        if (navigator.userAgent.match(/ipad|ipod|iphone/i)) {
                            clipboard.on('success', function (e) {
                                e.clearSelection();
                        $.sDialog({
                                    skin: "red",
                            content: 'copy success!',
                            okBtn: false,
                            cancelBtn: false,
                            lock: true
                        });
                                console.log('copy success!');

                        $('#success').show()
                                $('#id_text').hide()

                        setTimeout('HidemySmallModal()', 2000);

                            });
                        } else {
                    $input.select();
                        }
                //document.execCommand('copy');
                $input.blur();
                    });
                }

                //分頁
                function FunPageProc(page) {
                    if ($(targetFormID).size() > 0) {
                $('#@Html.IdFor(m=>m.Search.Page)').val(page)
                        funAjax()
                    }
            };

            //查詢
            function funAjax() {

            $.ajax({
                    url: '@Url.Action("_PageContent1", (string)ViewBag.BRE_NO)',
                data: $(targetFormID).serialize(),
                type: 'post',
                async: false,
                cache: false,
                dataType: 'html',
                success: function (data) {
                    $('#PageContent1').html(data);
                    }
                });
            }

            function OnEdit(id, GAME_TYPE)
            {
            $('#@Html.IdFor(m=>m.Search.WhereGAME_NO)').val(id)


                   $(targetFormID).attr("action", "@Url.Action("Edit1", (string)ViewBag.BRE_NO)")



            $(targetFormID).submit();
            }

            function OnPassMode(id, Coupons_ITem, LEVEL_NO) {

            $("#GAME_NO").val(id);
            $('form').attr("action", "@Url.Action("LevelGameView1", "Game")");
            $('#@Html.IdFor(m=>m.Coupons_ITem)').val(Coupons_ITem);
            $('#@Html.IdFor(m => m.LEVEL_NO)').val(LEVEL_NO);
        $('form').submit();
            }
         function OnPassMode2(id, Coupons_ITem, LEVEL_NO) {

            $("#GAME_NO").val(id);
            $('form').attr("action", "@Url.Action("LevelGameView2", "Game")");
            $('#@Html.IdFor(m=>m.Coupons_ITem)').val(Coupons_ITem);
            $('#@Html.IdFor(m => m.LEVEL_NO)').val(LEVEL_NO);
        $('form').submit();
            }
            function OnQueryTeamSetView(id) {
            $(targetFormID).attr("action", "@Url.Action("QueryTeamSetView", (string)ViewBag.BRE_NO)?GAME_NO="+id)
                $(targetFormID).submit();
            }

            function OnCashIntoView(id) {
            $('#@Html.IdFor(m=>m.Search.WhereGAME_NO)').val(id)
                $(targetFormID).attr("action", "@Url.Action("CashIntoView", (string)ViewBag.BRE_NO)")
                $(targetFormID).submit();
            }

            function OnRewar(id) {
            $('#@Html.IdFor(m=>m.Search.WhereGAME_NO)').val(id)
                $(targetFormID).attr("action", "@Url.Action("Reward", (string)ViewBag.BRE_NO)")
                $(targetFormID).submit();
            }

            function OnStatistics(id)
            {
            $('#@Html.IdFor(m=>m.Search.WhereGAME_NO)').val(id)
                $(targetFormID).attr("action", "@Url.Action("Statistics", (string)ViewBag.BRE_NO)")
                $(targetFormID).submit();
            }

            function OnStatisticsAns(id)
            {
            $('#@Html.IdFor(m=>m.Search.WhereGAME_NO)').val(id)
                $(targetFormID).attr("action", "@Url.Action("StatisticsAns", (string)ViewBag.BRE_NO)")
                $(targetFormID).submit();
            }

            function OnScoreList(id)
            {
            $('#@Html.IdFor(m=>m.Search.WhereGAME_NO)').val(id)
                $(targetFormID).attr("action", "@Url.Action("ScoreLists", (string)ViewBag.BRE_NO)")
                $(targetFormID).submit();
            }

            function OnBuskerManager(id)
            {
            $('#@Html.IdFor(m=>m.Search.WhereGAME_NO)').val(id)
                $(targetFormID).attr("action", "@Url.Action("BuskerManager", (string)ViewBag.BRE_NO)")
                $(targetFormID).submit();
            }

            function OnLottery(id) {
            $('#@Html.IdFor(m=>m.Search.WhereGAME_NO)').val(id)
                $(targetFormID).attr("action", "@Url.Action("Lottery", (string)ViewBag.BRE_NO)")
                $(targetFormID).submit();
            }

            function OnBatchWork(id) {
            $('#@Html.IdFor(m=>m.Search.WhereGAME_NO)').val(id)
                $(targetFormID).attr("action", "@Url.Action("BatchWork", (string)ViewBag.BRE_NO)")
                $(targetFormID).submit();
            }

            function todoClear() {
            ////重設

            $('#Q_Div').find(":input,:selected").each(function (i) {

                    var type = $(this).attr('type')
                var InPreadonly = $(this).attr('readonly')
                var tag = this.tagName.toLowerCase(); // normalize case

                    if (InPreadonly == false || InPreadonly == undefined) {

                        if (type == 'radio' || type == 'checkbox') {
                            if ($(this).attr("title") == 'Default') {
                                this.checked = true;
                                }
                        else {
                                    this.checked = false;
                                    }
                                }
                    else if (tag == 'select') { //下拉式選單
                                    this.selectedIndex = 0;
                                }
                                else if (type == 'text' || type == 'hidden' || type == 'password' || type == 'textarea') {
                                    this.value = '';
                                }
                            }
                        });

            $(targetFormID).submit();
                    }
    </script>
}