﻿@model SECI02BorrowIndexViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    string data = Request.Params["from"];

    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
    else if (ViewBag.from != null)
    {
        Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
    }
    else
    {
        Layout = "~/Views/Shared/_LayoutSEO.cshtml";
    }

    int BookPageCount = 0;

}

<link href='~/Content/css/EzCss.css' rel='stylesheet' />
<script src="~/Scripts/Highcharts-4.0.1/js/highcharts.js"></script>
<script src="~/Scripts/Highcharts-4.0.1/js/modules/exporting.js"></script>
<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>
<script src="~/Scripts/Blob.js"></script>
<script src="~/Scripts/FileSaver.js"></script>
<script src="~/Scripts/tableexport.js"></script>
<script src="~/Scripts/printThis/printThis.js"></script>
<br />

@using (Html.BeginForm("BorrowIndex", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", id = "form1" }))
{

    @Html.Partial("_Title_Secondary")

    if (data == "SEI05" && ViewBag.from == null)
    {
        @Html.Partial("../SECI05/_SECI05Menu", 2)

    }
    else if (ViewBag.from != null)
    {
        Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
    }
    else
    {
        Html.RenderAction("_Menu", "SECI07", new { NowAction = "BorrowIndex" });
    }
    @Html.HiddenFor(m => m.fromStr)
    <div class="form-inline" role="form">
        <div class="form-group">
            <label class="control-label">學校</label>
        </div>
        <div class="form-group">
            @Html.DropDownListFor(m => m.SearchSCHOOL_NO, (IEnumerable<SelectListItem>)ViewBag.SchoolNoSelectItem, new { @class = "form-control", onchange = "form1.submit();" })
        </div>
        <div class="form-group">
            <label class="control-label">月份</label>
        </div>
        <div class="form-group">
            @Html.DropDownListFor(m => m.WhereMM, (IEnumerable<SelectListItem>)ViewBag.MonthItems, new { @class = "form-control", @onchange = "form1.submit();" })
        </div>
        <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="form1.submit();" />
        <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />
    </div>

    <br />

    <div class="form-inline">
        <div class="col-xs-12 text-right">
            <button type="button" class="btn btn-sm btn-sys" onclick="PrintBooK()">我要列印</button>
            <button class="btn btn-sm btn-sys" onclick='fn_save()'>另存新檔(Excel)</button>
        </div>
    </div>
    <br />

    <div class="text-right">
        <strong>資料統計至@((DateTime.Now.AddDays(-1)).ToLongDateString().ToString())</strong>
    </div>
    <div id="tbData">
        <div style="height:10px"></div>
        <div class="col-sm-12">
            <div class="panel table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            @if (Model.SearchSCHOOL_NO == SharedGlobal.ALL)
                            {
                                <th>
                                    學校
                                </th>
                            }
                            else
                            {
                                <th>
                                    班級
                                </th>
                            }

                            <th style="text-align: center">
                                本學期總冊數
                                @if (!string.IsNullOrWhiteSpace(Model.WhereMM))
                                {
                                    <samp>  (@(((IEnumerable<SelectListItem>)ViewBag.MonthItems).FirstOrDefault(m => m.Value == Model.WhereMM).Text))</samp>
                                }
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var item in Model.BorrowList)
                        {
                            <tr>
                                @if (Model.SearchSCHOOL_NO == SharedGlobal.ALL)
                                {
                                    <td>
                                        @item.SHORT_NAME
                                    </td>
                                }
                                else
                                {
                                    <td>
                                        @item.CLASS_NO
                                    </td>
                                }

                                <td align="center">
                                    @item.BORROW_COUNT
                                </td>
                            </tr>
                            BookPageCount += item.BORROW_COUNT;
                        }

                        <tr>
                            <td></td>
                            <td style="border-top:2px solid #d56666">
                                <div class="text-danger text-center">
                                    本頁總計: @BookPageCount 本書　　
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
}
@if (user != null)
{
    if (user.USER_NO == "0000" || user.USER_NO == "R120713478")
    {
        <img src="~/Content/img/BorrowIndexPIC.jpg" />
    }
}

<script language="javascript">

    var targetFormID = '#form1';

    $(document).ready(function () {
        $(".colorbox").colorbox({ iframe: true, width: "80%", height: "80%", opacity: 0.82 });
           $('#fromStr').val("@ViewBag.from");
    });
    function PrintBooK() {
        $('#tbData').printThis();
    }

    function todoClear() {
        ////重設
        $("#form1").find(":input,:selected").each(function (i) {

            var type = $(this).attr('type')
            var InPreadonly = $(this).attr('readonly')
            var tag = this.tagName.toLowerCase(); // normalize case

            if (InPreadonly == false || InPreadonly == undefined) {

                if (type == 'radio' || type == 'checkbox') {
                    if ($(this).attr("title") == 'Default') {
                        this.checked = true;
                    }
                    else {
                        this.checked = false;
                    }
                }
                else if (tag == 'select') { //下拉式選單
                    this.selectedIndex = 0;
                }
                else if (type == 'text' || type == 'hidden' || type == 'password' || type == 'textarea') {
                    this.value = '';
                }
            }
        });

        form1.submit();
    }

    function fn_save() {
        var blob = new Blob([document.getElementById('tbData').innerHTML], {
            type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8"
        });
        var strFile = "Report.xls";
        saveAs(blob, strFile);
        return false;
    }
</script>