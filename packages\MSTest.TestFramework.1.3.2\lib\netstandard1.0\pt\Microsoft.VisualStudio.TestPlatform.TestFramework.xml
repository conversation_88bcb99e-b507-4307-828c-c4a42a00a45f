<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.VisualStudio.TestPlatform.TestFramework</name>
    </assembly>
    <members>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod">
            <summary>
            O TestMethod para a execução.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.TestMethodName">
            <summary>
            Obtém o nome do método de teste.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.TestClassName">
            <summary>
            Obtém o nome da classe de teste.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.ReturnType">
            <summary>
            Obtém o tipo de retorno do método de teste.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.ParameterTypes">
            <summary>
            Obtém os parâmetros do método de teste.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.MethodInfo">
            <summary>
            Obtém o methodInfo para o método de teste.
            </summary>
            <remarks>
            This is just to retrieve additional information about the method.
            Do not directly invoke the method using MethodInfo. Use ITestMethod.Invoke instead.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.Invoke(System.Object[])">
            <summary>
            Invoca o método de teste.
            </summary>
            <param name="arguments">
            Argumentos a serem passados ao método de teste. (Por exemplo, para testes controlados por dados)
            </param>
            <returns>
            Resultado da invocação do método de teste.
            </returns>
            <remarks>
            This call handles asynchronous test methods as well.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.GetAllAttributes(System.Boolean)">
            <summary>
            Obter todos os atributos do método de teste.
            </summary>
            <param name="inherit">
            Se o atributo definido na classe pai é válido.
            </param>
            <returns>
            Todos os atributos.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.GetAttributes``1(System.Boolean)">
            <summary>
            Obter atributo de tipo específico.
            </summary>
            <typeparam name="AttributeType"> System.Attribute type. </typeparam>
            <param name="inherit">
            Se o atributo definido na classe pai é válido.
            </param>
            <returns>
            Os atributos do tipo especificado.
            </returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.Helper">
            <summary>
            O auxiliar.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Helper.CheckParameterNotNull(System.Object,System.String,System.String)">
            <summary>
            O parâmetro de verificação não nulo.
            </summary>
            <param name="param">
            O parâmetro.
            </param>
            <param name="parameterName">
            O nome do parâmetro.
            </param>
            <param name="message">
            A mensagem.
            </param>
            <exception cref="T:System.ArgumentNullException"> Throws argument null exception when parameter is null. </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Helper.CheckParameterNotNullOrEmpty(System.String,System.String,System.String)">
            <summary>
            O parâmetro de verificação não nulo nem vazio.
            </summary>
            <param name="param">
            O parâmetro.
            </param>
            <param name="parameterName">
            O nome do parâmetro.
            </param>
            <param name="message">
            A mensagem.
            </param>
            <exception cref="T:System.ArgumentException"> Throws ArgumentException when parameter is null. </exception>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod">
            <summary>
            Enumeração para como acessamos as linhas de dados no teste controlado por dados.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod.Sequential">
            <summary>
            As linhas são retornadas em ordem sequencial.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod.Random">
            <summary>
            As linhas são retornadas em ordem aleatória.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute">
            <summary>
            O atributo para definir dados embutidos para um método de teste.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute.#ctor(System.Object)">
            <summary>
            Inicializa uma nova instância da classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute"/>.
            </summary>
            <param name="data1"> O objeto de dados. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute.#ctor(System.Object,System.Object[])">
            <summary>
            Inicializa a nova instância da classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute"/> que ocupa uma matriz de argumentos.
            </summary>
            <param name="data1"> Um objeto de dados. </param>
            <param name="moreData"> Mais dados. </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute.Data">
            <summary>
            Obtém Dados para chamar o método de teste.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute.DisplayName">
            <summary>
            Obtém ou define o nome de exibição nos resultados de teste para personalização.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException">
            <summary>
            A exceção inconclusiva da asserção.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException.#ctor(System.String,System.Exception)">
            <summary>
             Inicializa uma nova instância da classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException"/>.
            </summary>
            <param name="msg"> A mensagem. </param>
            <param name="ex"> A exceção. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException.#ctor(System.String)">
            <summary>
             Inicializa uma nova instância da classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException"/>.
            </summary>
            <param name="msg"> A mensagem. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException.#ctor">
            <summary>
             Inicializa uma nova instância da classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException"/>.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException">
            <summary>
            Classe InternalTestFailureException. Usada para indicar falha interna de um caso de teste
            </summary>
            <remarks>
            This class is only added to preserve source compatibility with the V1 framework.
            For all practical purposes either use AssertFailedException/AssertInconclusiveException.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException.#ctor(System.String,System.Exception)">
            <summary>
             Inicializa uma nova instância da classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException"/>.
            </summary>
            <param name="msg"> A mensagem de exceção. </param>
            <param name="ex"> A exceção. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException.#ctor(System.String)">
            <summary>
             Inicializa uma nova instância da classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException"/>.
            </summary>
            <param name="msg"> A mensagem de exceção. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException.#ctor">
            <summary>
             Inicializa uma nova instância da classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException"/>.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute">
            <summary>
            Atributo que especifica que uma exceção do tipo especificado é esperada
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.#ctor(System.Type)">
            <summary>
            Inicializa uma nova instância da classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute"/> com o tipo especificado
            </summary>
            <param name="exceptionType">Tipo da exceção esperada</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.#ctor(System.Type,System.String)">
            <summary>
            Inicializa uma nova instância da classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute"/> com
             o tipo esperado e a mensagem a ser incluída quando nenhuma exceção é gerada pelo teste.
            </summary>
            <param name="exceptionType">Tipo da exceção esperada</param>
            <param name="noExceptionMessage">
            Mensagem a ser incluída no resultado do teste se ele falhar por não gerar uma exceção
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.ExceptionType">
            <summary>
            Obtém um valor que indica o Tipo da exceção esperada
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.AllowDerivedTypes">
            <summary>
            Obtém ou define um valor que indica se é para permitir tipos derivados do tipo da exceção esperada para
            qualificá-la como esperada
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.NoExceptionMessage">
            <summary>
            Obtém a mensagem a ser incluída no resultado do teste caso o teste falhe devido à não geração de uma exceção
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.Verify(System.Exception)">
            <summary>
            Verifica se o tipo da exceção gerada pelo teste de unidade é esperado
            </summary>
            <param name="exception">A exceção gerada pelo teste de unidade</param>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute">
            <summary>
            Classe base para atributos que especificam que uma exceção de um teste de unidade é esperada
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.#ctor">
            <summary>
            Inicializa uma nova instância da classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute"/> com uma mensagem de não exceção padrão
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.#ctor(System.String)">
            <summary>
            Inicializa a nova instância da classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute"/> com uma mensagem de não exceção
            </summary>
            <param name="noExceptionMessage">
            Mensagem a ser incluída no resultado do teste se ele falhar por não gerar uma
            exceção
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.NoExceptionMessage">
            <summary>
            Obtém a mensagem a ser incluída no resultado do teste caso o teste falhe devido à não geração de uma exceção
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.SpecifiedNoExceptionMessage">
            <summary>
            Obtém a mensagem a ser incluída no resultado do teste caso o teste falhe devido à não geração de uma exceção
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.GetDefaultNoExceptionMessage(System.String)">
            <summary>
            Obtém a mensagem de não exceção padrão
            </summary>
            <param name="expectedExceptionAttributeTypeName">O nome do tipo de atributo ExpectedException</param>
            <returns>A mensagem de não exceção padrão</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.Verify(System.Exception)">
            <summary>
            Determina se uma exceção é esperada. Se o método é retornado, entende-se
            que a exceção era esperada. Se o método gera uma exceção, entende-se
            que a exceção não era esperada e a mensagem de exceção gerada
            é incluída no resultado do teste. A classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.Assert"/> pode ser usada para
            conveniência. Se <see cref="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Inconclusive"/> é usada e há falha de asserção,
            o resultado do teste é definido como Inconclusivo.
            </summary>
            <param name="exception">A exceção gerada pelo teste de unidade</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.RethrowIfAssertException(System.Exception)">
            <summary>
            Gerar a exceção novamente se for uma AssertFailedException ou uma AssertInconclusiveException
            </summary>
            <param name="exception">A exceção a ser gerada novamente se for uma exceção de asserção</param>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper">
            <summary>
            Essa classe é projetada para ajudar o usuário a executar o teste de unidade para os tipos que usam tipos genéricos.
            GenericParameterHelper satisfaz algumas restrições comuns de tipos genéricos,
            como:
            1. construtor público padrão
            2. implementa interface comum: IComparable, IEnumerable
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.#ctor">
            <summary>
            Inicializa a nova instância da classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper"/> que
            satisfaz a restrição 'newable' em genéricos C#.
            </summary>
            <remarks>
            This constructor initializes the Data property to a random value.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.#ctor(System.Int32)">
            <summary>
            Inicializa a nova instância da classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper"/> que
            inicializa a propriedade Data para um valor fornecido pelo usuário.
            </summary>
            <param name="data">Qualquer valor inteiro</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.Data">
            <summary>
            Obtém ou define Data
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.Equals(System.Object)">
            <summary>
            Executa a comparação de valores de dois objetos GenericParameterHelper
            </summary>
            <param name="obj">objeto com o qual comparar</param>
            <returns>verdadeiro se o objeto tem o mesmo valor que 'esse' objeto GenericParameterHelper.
            Caso contrário, falso.</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.GetHashCode">
            <summary>
            Retorna um código hash para esse objeto.
            </summary>
            <returns>O código hash.</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.CompareTo(System.Object)">
            <summary>
            Compara os dados dos dois objetos <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper"/>.
            </summary>
            <param name="obj">O objeto com o qual comparar.</param>
            <returns>
            Um número assinado indicando os valores relativos dessa instância e valor.
            </returns>
            <exception cref="T:System.NotSupportedException">
            Thrown when the object passed in is not an instance of <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.GetEnumerator">
            <summary>
            Retorna um objeto IEnumerator cujo comprimento é derivado
            da propriedade Data.
            </summary>
            <returns>O objeto IEnumerator</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.Clone">
            <summary>
            Retorna um objeto GenericParameterHelper que é igual ao
            objeto atual.
            </summary>
            <returns>O objeto clonado.</returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.Logging.Logger">
            <summary>
            Permite que usuários registrem/gravem rastros de testes de unidade para diagnósticos.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.Logging.Logger.LogMessageHandler">
            <summary>
            Manipulador para LogMessage.
            </summary>
            <param name="message">Mensagem a ser registrada.</param>
        </member>
        <member name="E:Microsoft.VisualStudio.TestTools.UnitTesting.Logging.Logger.OnLogMessage">
            <summary>
            Evento a ser escutado. Acionado quando o gerador do teste de unidade escreve alguma mensagem.
            Principalmente para ser consumido pelo adaptador.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Logging.Logger.LogMessage(System.String,System.Object[])">
            <summary>
            API para o gravador de teste chamar Registrar mensagens.
            </summary>
            <param name="format">Formato de cadeia de caracteres com espaços reservados.</param>
            <param name="args">Parâmetros dos espaços reservados.</param>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryAttribute">
            <summary>
            Atributo TestCategory. Usado para especificar a categoria de um teste de unidade.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryAttribute.#ctor(System.String)">
            <summary>
            Inicializa a nova instância da classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryAttribute"/> e aplica a categoria ao teste.
            </summary>
            <param name="testCategory">
            A Categoria de teste.
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryAttribute.TestCategories">
            <summary>
            Obtém as categorias de teste aplicadas ao teste.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryBaseAttribute">
            <summary>
            Classe base para o atributo "Category"
            </summary>
            <remarks>
            The reason for this attribute is to let the users create their own implementation of test categories.
            - test framework (discovery, etc) deals with TestCategoryBaseAttribute.
            - The reason that TestCategories property is a collection rather than a string,
              is to give more flexibility to the user. For instance the implementation may be based on enums for which the values can be OR'ed
              in which case it makes sense to have single attribute rather than multiple ones on the same test.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryBaseAttribute.#ctor">
            <summary>
            Inicializa a nova instância da classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryBaseAttribute"/>.
            Aplica a categoria ao teste. As cadeias de caracteres retornadas por TestCategories
            são usadas com o comando /category para filtrar os testes
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryBaseAttribute.TestCategories">
            <summary>
            Obtém a categoria de teste aplicada ao teste.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            <summary>
            Classe AssertFailedException. Usada para indicar falha em um caso de teste
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException.#ctor(System.String,System.Exception)">
            <summary>
            Inicializa uma nova instância da classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException"/>.
            </summary>
            <param name="msg"> A mensagem. </param>
            <param name="ex"> A exceção. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException.#ctor(System.String)">
            <summary>
            Inicializa uma nova instância da classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException"/>.
            </summary>
            <param name="msg"> A mensagem. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException.#ctor">
            <summary>
            Inicializa uma nova instância da classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException"/>.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.Assert">
            <summary>
            Uma coleção de classes auxiliares para testar várias condições nos
            testes de unidade. Se a condição testada não é atendida, uma exceção
            é gerada.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.That">
            <summary>
            Obtém uma instância singleton da funcionalidade Asserção.
            </summary>
            <remarks>
            Users can use this to plug-in custom assertions through C# extension methods.
            For instance, the signature of a custom assertion provider could be "public static void IsOfType&lt;T&gt;(this Assert assert, object obj)"
            Users could then use a syntax similar to the default assertions which in this case is "Assert.That.IsOfType&lt;Dog&gt;(animal);"
            More documentation is at "https://github.com/Microsoft/testfx-docs".
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsTrue(System.Boolean)">
            <summary>
            Testa se a condição especificada é verdadeira e gera uma exceção
            se a condição é falsa.
            </summary>
            <param name="condition">
            A condição que o teste espera ser verdadeira.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is false.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsTrue(System.Boolean,System.String)">
            <summary>
            Testa se a condição especificada é verdadeira e gera uma exceção
            se a condição é falsa.
            </summary>
            <param name="condition">
            A condição que o teste espera ser verdadeira.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="condition"/>
            é falsa. A mensagem é mostrada nos resultados de teste.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is false.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsTrue(System.Boolean,System.String,System.Object[])">
            <summary>
            Testa se a condição especificada é verdadeira e gera uma exceção
            se a condição é falsa.
            </summary>
            <param name="condition">
            A condição que o teste espera ser verdadeira.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="condition"/>
            é falsa. A mensagem é mostrada nos resultados de teste.
            </param>
            <param name="parameters">
            Uma matriz de parâmetros a serem usados ao formatar <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is false.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsFalse(System.Boolean)">
            <summary>
            Testa se a condição especificada é falsa e gera uma exceção
            se a condição é verdadeira.
            </summary>
            <param name="condition">
            A condição que o teste espera ser falsa.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is true.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsFalse(System.Boolean,System.String)">
            <summary>
            Testa se a condição especificada é falsa e gera uma exceção
            se a condição é verdadeira.
            </summary>
            <param name="condition">
            A condição que o teste espera ser falsa.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="condition"/>
            é verdadeira. A mensagem é mostrada nos resultados de teste.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is true.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsFalse(System.Boolean,System.String,System.Object[])">
            <summary>
            Testa se a condição especificada é falsa e gera uma exceção
            se a condição é verdadeira.
            </summary>
            <param name="condition">
            A condição que o teste espera ser falsa.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="condition"/>
            é verdadeira. A mensagem é mostrada nos resultados de teste.
            </param>
            <param name="parameters">
            Uma matriz de parâmetros a serem usados ao formatar <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is true.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNull(System.Object)">
            <summary>
            Testa se o objeto especificado é nulo e gera uma exceção
            caso ele não seja.
            </summary>
            <param name="value">
            O objeto que o teste espera ser nulo.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNull(System.Object,System.String)">
            <summary>
            Testa se o objeto especificado é nulo e gera uma exceção
            caso ele não seja.
            </summary>
            <param name="value">
            O objeto que o teste espera ser nulo.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="value"/>
            não é nulo. A mensagem é mostrada nos resultados de teste.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNull(System.Object,System.String,System.Object[])">
            <summary>
            Testa se o objeto especificado é nulo e gera uma exceção
            caso ele não seja.
            </summary>
            <param name="value">
            O objeto que o teste espera ser nulo.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="value"/>
            não é nulo. A mensagem é mostrada nos resultados de teste.
            </param>
            <param name="parameters">
            Uma matriz de parâmetros a serem usados ao formatar <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotNull(System.Object)">
            <summary>
            Testa se o objeto especificado é não nulo e gera uma exceção
            caso ele seja nulo.
            </summary>
            <param name="value">
            O objeto que o teste espera que não seja nulo.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotNull(System.Object,System.String)">
            <summary>
            Testa se o objeto especificado é não nulo e gera uma exceção
            caso ele seja nulo.
            </summary>
            <param name="value">
            O objeto que o teste espera que não seja nulo.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="value"/>
            é nulo. A mensagem é mostrada nos resultados de teste.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotNull(System.Object,System.String,System.Object[])">
            <summary>
            Testa se o objeto especificado é não nulo e gera uma exceção
            caso ele seja nulo.
            </summary>
            <param name="value">
            O objeto que o teste espera que não seja nulo.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="value"/>
            é nulo. A mensagem é mostrada nos resultados de teste.
            </param>
            <param name="parameters">
            Uma matriz de parâmetros a serem usados ao formatar <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreSame(System.Object,System.Object)">
            <summary>
            Testa se os objetos especificados se referem ao mesmo objeto e
            gera uma exceção se as duas entradas não se referem ao mesmo objeto.
            </summary>
            <param name="expected">
            O primeiro objeto a ser comparado. Trata-se do valor esperado pelo teste.
            </param>
            <param name="actual">
            O segundo objeto a ser comparado. Trata-se do valor produzido pelo código em teste.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> does not refer to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreSame(System.Object,System.Object,System.String)">
            <summary>
            Testa se os objetos especificados se referem ao mesmo objeto e
            gera uma exceção se as duas entradas não se referem ao mesmo objeto.
            </summary>
            <param name="expected">
            O primeiro objeto a ser comparado. Trata-se do valor esperado pelo teste.
            </param>
            <param name="actual">
            O segundo objeto a ser comparado. Trata-se do valor produzido pelo código em teste.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="actual"/>
            não é o mesmo que <paramref name="expected"/>. A mensagem é mostrada
            nos resultados de teste.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> does not refer to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreSame(System.Object,System.Object,System.String,System.Object[])">
            <summary>
            Testa se os objetos especificados se referem ao mesmo objeto e
            gera uma exceção se as duas entradas não se referem ao mesmo objeto.
            </summary>
            <param name="expected">
            O primeiro objeto a ser comparado. Trata-se do valor esperado pelo teste.
            </param>
            <param name="actual">
            O segundo objeto a ser comparado. Trata-se do valor produzido pelo código em teste.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="actual"/>
            não é o mesmo que <paramref name="expected"/>. A mensagem é mostrada
            nos resultados de teste.
            </param>
            <param name="parameters">
            Uma matriz de parâmetros a serem usados ao formatar <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> does not refer to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotSame(System.Object,System.Object)">
            <summary>
            Testa se os objetos especificados se referem a objetos diferentes e
            gera uma exceção se as duas entradas se referem ao mesmo objeto.
            </summary>
            <param name="notExpected">
            O primeiro objeto a ser comparado. Trata-se do valor que o teste espera que não
            corresponda a <paramref name="actual"/>.
            </param>
            <param name="actual">
            O segundo objeto a ser comparado. Trata-se do valor produzido pelo código em teste.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> refers to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotSame(System.Object,System.Object,System.String)">
            <summary>
            Testa se os objetos especificados se referem a objetos diferentes e
            gera uma exceção se as duas entradas se referem ao mesmo objeto.
            </summary>
            <param name="notExpected">
            O primeiro objeto a ser comparado. Trata-se do valor que o teste espera que não
            corresponda a <paramref name="actual"/>.
            </param>
            <param name="actual">
            O segundo objeto a ser comparado. Trata-se do valor produzido pelo código em teste.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="actual"/>
            é o mesmo que <paramref name="notExpected"/>. A mensagem é mostrada nos
            resultados de teste.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> refers to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotSame(System.Object,System.Object,System.String,System.Object[])">
            <summary>
            Testa se os objetos especificados se referem a objetos diferentes e
            gera uma exceção se as duas entradas se referem ao mesmo objeto.
            </summary>
            <param name="notExpected">
            O primeiro objeto a ser comparado. Trata-se do valor que o teste espera que não
            corresponda a <paramref name="actual"/>.
            </param>
            <param name="actual">
            O segundo objeto a ser comparado. Trata-se do valor produzido pelo código em teste.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="actual"/>
            é o mesmo que <paramref name="notExpected"/>. A mensagem é mostrada nos
            resultados de teste.
            </param>
            <param name="parameters">
            Uma matriz de parâmetros a serem usados ao formatar <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> refers to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual``1(``0,``0)">
            <summary>
            Testa se os valores especificados são iguais e gera uma exceção
            se os dois valores não são iguais. Tipos numéricos diferentes são tratados
            como desiguais mesmo se os valores lógicos são iguais. 42L não é igual a 42.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="expected">
            O primeiro valor a ser comparado. Trate-se do valor esperado pelo teste.
            </param>
            <param name="actual">
            O segundo valor a ser comparado. Trata-se do valor produzido pelo código em teste.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual``1(``0,``0,System.String)">
            <summary>
            Testa se os valores especificados são iguais e gera uma exceção
            se os dois valores não são iguais. Tipos numéricos diferentes são tratados
            como desiguais mesmo se os valores lógicos são iguais. 42L não é igual a 42.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="expected">
            O primeiro valor a ser comparado. Trate-se do valor esperado pelo teste.
            </param>
            <param name="actual">
            O segundo valor a ser comparado. Trata-se do valor produzido pelo código em teste.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="actual"/>
            não é igual a <paramref name="expected"/>. A mensagem é mostrada nos
            resultados de teste.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual``1(``0,``0,System.String,System.Object[])">
            <summary>
            Testa se os valores especificados são iguais e gera uma exceção
            se os dois valores não são iguais. Tipos numéricos diferentes são tratados
            como desiguais mesmo se os valores lógicos são iguais. 42L não é igual a 42.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="expected">
            O primeiro valor a ser comparado. Trate-se do valor esperado pelo teste.
            </param>
            <param name="actual">
            O segundo valor a ser comparado. Trata-se do valor produzido pelo código em teste.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="actual"/>
            não é igual a <paramref name="expected"/>. A mensagem é mostrada nos
            resultados de teste.
            </param>
            <param name="parameters">
            Uma matriz de parâmetros a serem usados ao formatar <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual``1(``0,``0)">
            <summary>
            Testa se os valores especificados são desiguais e gera uma exceção
            se os dois valores são iguais. Tipos numéricos diferentes são tratados
            como desiguais mesmo se os valores lógicos são iguais. 42L não é igual a 42.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="notExpected">
            O primeiro valor a ser comparado. Trata-se do valor que o teste espera que não
            corresponda a <paramref name="actual"/>.
            </param>
            <param name="actual">
            O segundo valor a ser comparado. Trata-se do valor produzido pelo código em teste.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual``1(``0,``0,System.String)">
            <summary>
            Testa se os valores especificados são desiguais e gera uma exceção
            se os dois valores são iguais. Tipos numéricos diferentes são tratados
            como desiguais mesmo se os valores lógicos são iguais. 42L não é igual a 42.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="notExpected">
            O primeiro valor a ser comparado. Trata-se do valor que o teste espera que não
            corresponda a <paramref name="actual"/>.
            </param>
            <param name="actual">
            O segundo valor a ser comparado. Trata-se do valor produzido pelo código em teste.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="actual"/>
            é igual a <paramref name="notExpected"/>. A mensagem é mostrada nos
            resultados de teste.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual``1(``0,``0,System.String,System.Object[])">
            <summary>
            Testa se os valores especificados são desiguais e gera uma exceção
            se os dois valores são iguais. Tipos numéricos diferentes são tratados
            como desiguais mesmo se os valores lógicos são iguais. 42L não é igual a 42.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="notExpected">
            O primeiro valor a ser comparado. Trata-se do valor que o teste espera que não
            corresponda a <paramref name="actual"/>.
            </param>
            <param name="actual">
            O segundo valor a ser comparado. Trata-se do valor produzido pelo código em teste.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="actual"/>
            é igual a <paramref name="notExpected"/>. A mensagem é mostrada nos
            resultados de teste.
            </param>
            <param name="parameters">
            Uma matriz de parâmetros a serem usados ao formatar <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Object,System.Object)">
            <summary>
            Testa se os objetos especificados são iguais e gera uma exceção
            se os dois objetos não são iguais. Tipos numéricos diferentes são tratados
            como desiguais mesmo se os valores lógicos são iguais. 42L não é igual a 42.
            </summary>
            <param name="expected">
            O primeiro objeto a ser comparado. Trata-se do objeto esperado pelo teste.
            </param>
            <param name="actual">
            O segundo objeto a ser comparado. Trata-se do objeto produzido pelo código em teste.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Object,System.Object,System.String)">
            <summary>
            Testa se os objetos especificados são iguais e gera uma exceção
            se os dois objetos não são iguais. Tipos numéricos diferentes são tratados
            como desiguais mesmo se os valores lógicos são iguais. 42L não é igual a 42.
            </summary>
            <param name="expected">
            O primeiro objeto a ser comparado. Trata-se do objeto esperado pelo teste.
            </param>
            <param name="actual">
            O segundo objeto a ser comparado. Trata-se do objeto produzido pelo código em teste.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="actual"/>
            não é igual a <paramref name="expected"/>. A mensagem é mostrada nos
            resultados de teste.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Object,System.Object,System.String,System.Object[])">
            <summary>
            Testa se os objetos especificados são iguais e gera uma exceção
            se os dois objetos não são iguais. Tipos numéricos diferentes são tratados
            como desiguais mesmo se os valores lógicos são iguais. 42L não é igual a 42.
            </summary>
            <param name="expected">
            O primeiro objeto a ser comparado. Trata-se do objeto esperado pelo teste.
            </param>
            <param name="actual">
            O segundo objeto a ser comparado. Trata-se do objeto produzido pelo código em teste.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="actual"/>
            não é igual a <paramref name="expected"/>. A mensagem é mostrada nos
            resultados de teste.
            </param>
            <param name="parameters">
            Uma matriz de parâmetros a serem usados ao formatar <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Object,System.Object)">
            <summary>
            Testa se os objetos especificados são desiguais e gera uma exceção
            se os dois objetos são iguais. Tipos numéricos diferentes são tratados
            como desiguais mesmo se os valores lógicos são iguais. 42L não é igual a 42.
            </summary>
            <param name="notExpected">
            O primeiro objeto a ser comparado. Trata-se do valor que o teste espera que não
            corresponda a <paramref name="actual"/>.
            </param>
            <param name="actual">
            O segundo objeto a ser comparado. Trata-se do objeto produzido pelo código em teste.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Object,System.Object,System.String)">
            <summary>
            Testa se os objetos especificados são desiguais e gera uma exceção
            se os dois objetos são iguais. Tipos numéricos diferentes são tratados
            como desiguais mesmo se os valores lógicos são iguais. 42L não é igual a 42.
            </summary>
            <param name="notExpected">
            O primeiro objeto a ser comparado. Trata-se do valor que o teste espera que não
            corresponda a <paramref name="actual"/>.
            </param>
            <param name="actual">
            O segundo objeto a ser comparado. Trata-se do objeto produzido pelo código em teste.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="actual"/>
            é igual a <paramref name="notExpected"/>. A mensagem é mostrada nos
            resultados de teste.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Object,System.Object,System.String,System.Object[])">
            <summary>
            Testa se os objetos especificados são desiguais e gera uma exceção
            se os dois objetos são iguais. Tipos numéricos diferentes são tratados
            como desiguais mesmo se os valores lógicos são iguais. 42L não é igual a 42.
            </summary>
            <param name="notExpected">
            O primeiro objeto a ser comparado. Trata-se do valor que o teste espera que não
            corresponda a <paramref name="actual"/>.
            </param>
            <param name="actual">
            O segundo objeto a ser comparado. Trata-se do objeto produzido pelo código em teste.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="actual"/>
            é igual a <paramref name="notExpected"/>. A mensagem é mostrada nos
            resultados de teste.
            </param>
            <param name="parameters">
            Uma matriz de parâmetros a serem usados ao formatar <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Single,System.Single,System.Single)">
            <summary>
            Testa se os floats especificados são iguais e gera uma exceção
            se eles não são iguais.
            </summary>
            <param name="expected">
            O primeiro float a ser comparado. Trata-se do float esperado pelo teste.
            </param>
            <param name="actual">
            O segundo float a ser comparado. Trata-se do float produzido pelo código em teste.
            </param>
            <param name="delta">
            A precisão necessária. Uma exceção será gerada somente se
            <paramref name="actual"/> for diferente de <paramref name="expected"/>
            por mais de <paramref name="delta"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Single,System.Single,System.Single,System.String)">
            <summary>
            Testa se os floats especificados são iguais e gera uma exceção
            se eles não são iguais.
            </summary>
            <param name="expected">
            O primeiro float a ser comparado. Trata-se do float esperado pelo teste.
            </param>
            <param name="actual">
            O segundo float a ser comparado. Trata-se do float produzido pelo código em teste.
            </param>
            <param name="delta">
            A precisão necessária. Uma exceção será gerada somente se
            <paramref name="actual"/> for diferente de <paramref name="expected"/>
            por mais de <paramref name="delta"/>.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="actual"/>
            for diferente de <paramref name="expected"/> por mais de
            <paramref name="delta"/>. A mensagem é mostrada nos resultados de teste.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Single,System.Single,System.Single,System.String,System.Object[])">
            <summary>
            Testa se os floats especificados são iguais e gera uma exceção
            se eles não são iguais.
            </summary>
            <param name="expected">
            O primeiro float a ser comparado. Trata-se do float esperado pelo teste.
            </param>
            <param name="actual">
            O segundo float a ser comparado. Trata-se do float produzido pelo código em teste.
            </param>
            <param name="delta">
            A precisão necessária. Uma exceção será gerada somente se
            <paramref name="actual"/> for diferente de <paramref name="expected"/>
            por mais de <paramref name="delta"/>.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="actual"/>
            for diferente de <paramref name="expected"/> por mais de
            <paramref name="delta"/>. A mensagem é mostrada nos resultados de teste.
            </param>
            <param name="parameters">
            Uma matriz de parâmetros a serem usados ao formatar <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Single,System.Single,System.Single)">
            <summary>
            Testa se os floats especificados são desiguais e gera uma exceção
            se eles são iguais.
            </summary>
            <param name="notExpected">
            O primeiro float a ser comparado. Trata-se do float que o teste espera que não
            corresponda a <paramref name="actual"/>.
            </param>
            <param name="actual">
            O segundo float a ser comparado. Trata-se do float produzido pelo código em teste.
            </param>
            <param name="delta">
            A precisão necessária. Uma exceção será gerada somente se
            <paramref name="actual"/> for diferente de <paramref name="notExpected"/>
            por no máximo <paramref name="delta"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Single,System.Single,System.Single,System.String)">
            <summary>
            Testa se os floats especificados são desiguais e gera uma exceção
            se eles são iguais.
            </summary>
            <param name="notExpected">
            O primeiro float a ser comparado. Trata-se do float que o teste espera que não
            corresponda a <paramref name="actual"/>.
            </param>
            <param name="actual">
            O segundo float a ser comparado. Trata-se do float produzido pelo código em teste.
            </param>
            <param name="delta">
            A precisão necessária. Uma exceção será gerada somente se
            <paramref name="actual"/> for diferente de <paramref name="notExpected"/>
            por no máximo <paramref name="delta"/>.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="actual"/>
            é igual a <paramref name="notExpected"/> ou diferente por menos de
            <paramref name="delta"/>. A mensagem é mostrada nos resultados de teste.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Single,System.Single,System.Single,System.String,System.Object[])">
            <summary>
            Testa se os floats especificados são desiguais e gera uma exceção
            se eles são iguais.
            </summary>
            <param name="notExpected">
            O primeiro float a ser comparado. Trata-se do float que o teste espera que não
            corresponda a <paramref name="actual"/>.
            </param>
            <param name="actual">
            O segundo float a ser comparado. Trata-se do float produzido pelo código em teste.
            </param>
            <param name="delta">
            A precisão necessária. Uma exceção será gerada somente se
            <paramref name="actual"/> for diferente de <paramref name="notExpected"/>
            por no máximo <paramref name="delta"/>.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="actual"/>
            é igual a <paramref name="notExpected"/> ou diferente por menos de
            <paramref name="delta"/>. A mensagem é mostrada nos resultados de teste.
            </param>
            <param name="parameters">
            Uma matriz de parâmetros a serem usados ao formatar <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Double,System.Double,System.Double)">
            <summary>
            Testa se os duplos especificados são iguais e gera uma exceção
            se eles não são iguais.
            </summary>
            <param name="expected">
            O primeiro duplo a ser comparado. Trata-se do duplo esperado pelo teste.
            </param>
            <param name="actual">
            O segundo duplo a ser comparado. Trata-se do duplo produzido pelo código em teste.
            </param>
            <param name="delta">
            A precisão necessária. Uma exceção será gerada somente se
            <paramref name="actual"/> for diferente de <paramref name="expected"/>
            por mais de <paramref name="delta"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Double,System.Double,System.Double,System.String)">
            <summary>
            Testa se os duplos especificados são iguais e gera uma exceção
            se eles não são iguais.
            </summary>
            <param name="expected">
            O primeiro duplo a ser comparado. Trata-se do duplo esperado pelo teste.
            </param>
            <param name="actual">
            O segundo duplo a ser comparado. Trata-se do duplo produzido pelo código em teste.
            </param>
            <param name="delta">
            A precisão necessária. Uma exceção será gerada somente se
            <paramref name="actual"/> for diferente de <paramref name="expected"/>
            por mais de <paramref name="delta"/>.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="actual"/>
            for diferente de <paramref name="expected"/> por mais de
            <paramref name="delta"/>. A mensagem é mostrada nos resultados de teste.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Double,System.Double,System.Double,System.String,System.Object[])">
            <summary>
            Testa se os duplos especificados são iguais e gera uma exceção
            se eles não são iguais.
            </summary>
            <param name="expected">
            O primeiro duplo a ser comparado. Trata-se do duplo esperado pelo teste.
            </param>
            <param name="actual">
            O segundo duplo a ser comparado. Trata-se do duplo produzido pelo código em teste.
            </param>
            <param name="delta">
            A precisão necessária. Uma exceção será gerada somente se
            <paramref name="actual"/> for diferente de <paramref name="expected"/>
            por mais de <paramref name="delta"/>.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="actual"/>
            for diferente de <paramref name="expected"/> por mais de
            <paramref name="delta"/>. A mensagem é mostrada nos resultados de teste.
            </param>
            <param name="parameters">
            Uma matriz de parâmetros a serem usados ao formatar <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Double,System.Double,System.Double)">
            <summary>
            Testa se os duplos especificados são desiguais e gera uma exceção
            se eles são iguais.
            </summary>
            <param name="notExpected">
            O primeiro duplo a ser comparado. Trata-se do duplo que o teste espera que não
            corresponda a <paramref name="actual"/>.
            </param>
            <param name="actual">
            O segundo duplo a ser comparado. Trata-se do duplo produzido pelo código em teste.
            </param>
            <param name="delta">
            A precisão necessária. Uma exceção será gerada somente se
            <paramref name="actual"/> for diferente de <paramref name="notExpected"/>
            por no máximo <paramref name="delta"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Double,System.Double,System.Double,System.String)">
            <summary>
            Testa se os duplos especificados são desiguais e gera uma exceção
            se eles são iguais.
            </summary>
            <param name="notExpected">
            O primeiro duplo a ser comparado. Trata-se do duplo que o teste espera que não
            corresponda a <paramref name="actual"/>.
            </param>
            <param name="actual">
            O segundo duplo a ser comparado. Trata-se do duplo produzido pelo código em teste.
            </param>
            <param name="delta">
            A precisão necessária. Uma exceção será gerada somente se
            <paramref name="actual"/> for diferente de <paramref name="notExpected"/>
            por no máximo <paramref name="delta"/>.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="actual"/>
            é igual a <paramref name="notExpected"/> ou diferente por menos de
            <paramref name="delta"/>. A mensagem é mostrada nos resultados de teste.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Double,System.Double,System.Double,System.String,System.Object[])">
            <summary>
            Testa se os duplos especificados são desiguais e gera uma exceção
            se eles são iguais.
            </summary>
            <param name="notExpected">
            O primeiro duplo a ser comparado. Trata-se do duplo que o teste espera que não
            corresponda a <paramref name="actual"/>.
            </param>
            <param name="actual">
            O segundo duplo a ser comparado. Trata-se do duplo produzido pelo código em teste.
            </param>
            <param name="delta">
            A precisão necessária. Uma exceção será gerada somente se
            <paramref name="actual"/> for diferente de <paramref name="notExpected"/>
            por no máximo <paramref name="delta"/>.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="actual"/>
            é igual a <paramref name="notExpected"/> ou diferente por menos de
            <paramref name="delta"/>. A mensagem é mostrada nos resultados de teste.
            </param>
            <param name="parameters">
            Uma matriz de parâmetros a serem usados ao formatar <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean)">
            <summary>
            Testa se as cadeias de caracteres especificadas são iguais e gera uma exceção
            se elas não são iguais. A cultura invariável é usada para a comparação.
            </summary>
            <param name="expected">
            A primeira cadeia de caracteres a ser comparada. Trata-se da cadeia de caracteres esperada pelo teste.
            </param>
            <param name="actual">
            A segunda cadeia de caracteres a ser comparada. Trata-se da cadeia de caracteres produzida pelo código em teste.
            </param>
            <param name="ignoreCase">
            Um booliano que indica uma comparação que diferencia ou não maiúsculas de minúsculas. (verdadeiro
            indica uma comparação que diferencia maiúsculas de minúsculas.)
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.String)">
            <summary>
            Testa se as cadeias de caracteres especificadas são iguais e gera uma exceção
            se elas não são iguais. A cultura invariável é usada para a comparação.
            </summary>
            <param name="expected">
            A primeira cadeia de caracteres a ser comparada. Trata-se da cadeia de caracteres esperada pelo teste.
            </param>
            <param name="actual">
            A segunda cadeia de caracteres a ser comparada. Trata-se da cadeia de caracteres produzida pelo código em teste.
            </param>
            <param name="ignoreCase">
            Um booliano que indica uma comparação que diferencia ou não maiúsculas de minúsculas. (verdadeiro
            indica uma comparação que diferencia maiúsculas de minúsculas.)
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="actual"/>
            não é igual a <paramref name="expected"/>. A mensagem é mostrada nos
            resultados de teste.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.String,System.Object[])">
            <summary>
            Testa se as cadeias de caracteres especificadas são iguais e gera uma exceção
            se elas não são iguais. A cultura invariável é usada para a comparação.
            </summary>
            <param name="expected">
            A primeira cadeia de caracteres a ser comparada. Trata-se da cadeia de caracteres esperada pelo teste.
            </param>
            <param name="actual">
            A segunda cadeia de caracteres a ser comparada. Trata-se da cadeia de caracteres produzida pelo código em teste.
            </param>
            <param name="ignoreCase">
            Um booliano que indica uma comparação que diferencia ou não maiúsculas de minúsculas. (verdadeiro
            indica uma comparação que diferencia maiúsculas de minúsculas.)
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="actual"/>
            não é igual a <paramref name="expected"/>. A mensagem é mostrada nos
            resultados de teste.
            </param>
            <param name="parameters">
            Uma matriz de parâmetros a serem usados ao formatar <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo)">
            <summary>
            Testa se as cadeias de caracteres especificadas são iguais e gera uma exceção
            se elas não são iguais.
            </summary>
            <param name="expected">
            A primeira cadeia de caracteres a ser comparada. Trata-se da cadeia de caracteres esperada pelo teste.
            </param>
            <param name="actual">
            A segunda cadeia de caracteres a ser comparada. Trata-se da cadeia de caracteres produzida pelo código em teste.
            </param>
            <param name="ignoreCase">
            Um booliano que indica uma comparação que diferencia ou não maiúsculas de minúsculas. (verdadeiro
            indica uma comparação que diferencia maiúsculas de minúsculas.)
            </param>
            <param name="culture">
            Um objeto CultureInfo que fornece informações de comparação específicas de cultura.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo,System.String)">
            <summary>
            Testa se as cadeias de caracteres especificadas são iguais e gera uma exceção
            se elas não são iguais.
            </summary>
            <param name="expected">
            A primeira cadeia de caracteres a ser comparada. Trata-se da cadeia de caracteres esperada pelo teste.
            </param>
            <param name="actual">
            A segunda cadeia de caracteres a ser comparada. Trata-se da cadeia de caracteres produzida pelo código em teste.
            </param>
            <param name="ignoreCase">
            Um booliano que indica uma comparação que diferencia ou não maiúsculas de minúsculas. (verdadeiro
            indica uma comparação que diferencia maiúsculas de minúsculas.)
            </param>
            <param name="culture">
            Um objeto CultureInfo que fornece informações de comparação específicas de cultura.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="actual"/>
            não é igual a <paramref name="expected"/>. A mensagem é mostrada nos
            resultados de teste.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo,System.String,System.Object[])">
            <summary>
            Testa se as cadeias de caracteres especificadas são iguais e gera uma exceção
            se elas não são iguais.
            </summary>
            <param name="expected">
            A primeira cadeia de caracteres a ser comparada. Trata-se da cadeia de caracteres esperada pelo teste.
            </param>
            <param name="actual">
            A segunda cadeia de caracteres a ser comparada. Trata-se da cadeia de caracteres produzida pelo código em teste.
            </param>
            <param name="ignoreCase">
            Um booliano que indica uma comparação que diferencia ou não maiúsculas de minúsculas. (verdadeiro
            indica uma comparação que diferencia maiúsculas de minúsculas.)
            </param>
            <param name="culture">
            Um objeto CultureInfo que fornece informações de comparação específicas de cultura.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="actual"/>
            não é igual a <paramref name="expected"/>. A mensagem é mostrada nos
            resultados de teste.
            </param>
            <param name="parameters">
            Uma matriz de parâmetros a serem usados ao formatar <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean)">
            <summary>
            Testa se as cadeias de caracteres especificadas são desiguais e gera uma exceção
            se elas são iguais. A cultura invariável é usada para a comparação.
            </summary>
            <param name="notExpected">
            A primeira cadeia de caracteres a ser comparada. Trata-se da cadeia de caracteres que o teste espera que não
            corresponda a <paramref name="actual"/>.
            </param>
            <param name="actual">
            A segunda cadeia de caracteres a ser comparada. Trata-se da cadeia de caracteres produzida pelo código em teste.
            </param>
            <param name="ignoreCase">
            Um booliano que indica uma comparação que diferencia ou não maiúsculas de minúsculas. (verdadeiro
            indica uma comparação que diferencia maiúsculas de minúsculas.)
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.String)">
            <summary>
            Testa se as cadeias de caracteres especificadas são desiguais e gera uma exceção
            se elas são iguais. A cultura invariável é usada para a comparação.
            </summary>
            <param name="notExpected">
            A primeira cadeia de caracteres a ser comparada. Trata-se da cadeia de caracteres que o teste espera que não
            corresponda a <paramref name="actual"/>.
            </param>
            <param name="actual">
            A segunda cadeia de caracteres a ser comparada. Trata-se da cadeia de caracteres produzida pelo código em teste.
            </param>
            <param name="ignoreCase">
            Um booliano que indica uma comparação que diferencia ou não maiúsculas de minúsculas. (verdadeiro
            indica uma comparação que diferencia maiúsculas de minúsculas.)
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="actual"/>
            é igual a <paramref name="notExpected"/>. A mensagem é mostrada nos
            resultados de teste.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.String,System.Object[])">
            <summary>
            Testa se as cadeias de caracteres especificadas são desiguais e gera uma exceção
            se elas são iguais. A cultura invariável é usada para a comparação.
            </summary>
            <param name="notExpected">
            A primeira cadeia de caracteres a ser comparada. Trata-se da cadeia de caracteres que o teste espera que não
            corresponda a <paramref name="actual"/>.
            </param>
            <param name="actual">
            A segunda cadeia de caracteres a ser comparada. Trata-se da cadeia de caracteres produzida pelo código em teste.
            </param>
            <param name="ignoreCase">
            Um booliano que indica uma comparação que diferencia ou não maiúsculas de minúsculas. (verdadeiro
            indica uma comparação que diferencia maiúsculas de minúsculas.)
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="actual"/>
            é igual a <paramref name="notExpected"/>. A mensagem é mostrada nos
            resultados de teste.
            </param>
            <param name="parameters">
            Uma matriz de parâmetros a serem usados ao formatar <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo)">
            <summary>
            Testa se as cadeias de caracteres especificadas são desiguais e gera uma exceção
            se elas são iguais.
            </summary>
            <param name="notExpected">
            A primeira cadeia de caracteres a ser comparada. Trata-se da cadeia de caracteres que o teste espera que não
            corresponda a <paramref name="actual"/>.
            </param>
            <param name="actual">
            A segunda cadeia de caracteres a ser comparada. Trata-se da cadeia de caracteres produzida pelo código em teste.
            </param>
            <param name="ignoreCase">
            Um booliano que indica uma comparação que diferencia ou não maiúsculas de minúsculas. (verdadeiro
            indica uma comparação que diferencia maiúsculas de minúsculas.)
            </param>
            <param name="culture">
            Um objeto CultureInfo que fornece informações de comparação específicas de cultura.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo,System.String)">
            <summary>
            Testa se as cadeias de caracteres especificadas são desiguais e gera uma exceção
            se elas são iguais.
            </summary>
            <param name="notExpected">
            A primeira cadeia de caracteres a ser comparada. Trata-se da cadeia de caracteres que o teste espera que não
            corresponda a <paramref name="actual"/>.
            </param>
            <param name="actual">
            A segunda cadeia de caracteres a ser comparada. Trata-se da cadeia de caracteres produzida pelo código em teste.
            </param>
            <param name="ignoreCase">
            Um booliano que indica uma comparação que diferencia ou não maiúsculas de minúsculas. (verdadeiro
            indica uma comparação que diferencia maiúsculas de minúsculas.)
            </param>
            <param name="culture">
            Um objeto CultureInfo que fornece informações de comparação específicas de cultura.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="actual"/>
            é igual a <paramref name="notExpected"/>. A mensagem é mostrada nos
            resultados de teste.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo,System.String,System.Object[])">
            <summary>
            Testa se as cadeias de caracteres especificadas são desiguais e gera uma exceção
            se elas são iguais.
            </summary>
            <param name="notExpected">
            A primeira cadeia de caracteres a ser comparada. Trata-se da cadeia de caracteres que o teste espera que não
            corresponda a <paramref name="actual"/>.
            </param>
            <param name="actual">
            A segunda cadeia de caracteres a ser comparada. Trata-se da cadeia de caracteres produzida pelo código em teste.
            </param>
            <param name="ignoreCase">
            Um booliano que indica uma comparação que diferencia ou não maiúsculas de minúsculas. (verdadeiro
            indica uma comparação que diferencia maiúsculas de minúsculas.)
            </param>
            <param name="culture">
            Um objeto CultureInfo que fornece informações de comparação específicas de cultura.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="actual"/>
            é igual a <paramref name="notExpected"/>. A mensagem é mostrada nos
            resultados de teste.
            </param>
            <param name="parameters">
            Uma matriz de parâmetros a serem usados ao formatar <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsInstanceOfType(System.Object,System.Type)">
            <summary>
            Testa se o objeto especificado é uma instância do tipo
            esperado e gera uma exceção se o tipo esperado não está na
            hierarquia de herança do objeto.
            </summary>
            <param name="value">
            O objeto que o teste espera que seja do tipo especificado.
            </param>
            <param name="expectedType">
            O tipo esperado de <paramref name="value"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsInstanceOfType(System.Object,System.Type,System.String)">
            <summary>
            Testa se o objeto especificado é uma instância do tipo
            esperado e gera uma exceção se o tipo esperado não está na
            hierarquia de herança do objeto.
            </summary>
            <param name="value">
            O objeto que o teste espera que seja do tipo especificado.
            </param>
            <param name="expectedType">
            O tipo esperado de <paramref name="value"/>.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="value"/>
            não é uma instância de <paramref name="expectedType"/>. A mensagem é
            mostrada nos resultados de teste.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsInstanceOfType(System.Object,System.Type,System.String,System.Object[])">
            <summary>
            Testa se o objeto especificado é uma instância do tipo
            esperado e gera uma exceção se o tipo esperado não está na
            hierarquia de herança do objeto.
            </summary>
            <param name="value">
            O objeto que o teste espera que seja do tipo especificado.
            </param>
            <param name="expectedType">
            O tipo esperado de <paramref name="value"/>.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="value"/>
            não é uma instância de <paramref name="expectedType"/>. A mensagem é
            mostrada nos resultados de teste.
            </param>
            <param name="parameters">
            Uma matriz de parâmetros a serem usados ao formatar <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotInstanceOfType(System.Object,System.Type)">
            <summary>
            Testa se o objeto especificado não é uma instância do tipo
            incorreto e gera uma exceção se o tipo especificado está na
            hierarquia de herança do objeto.
            </summary>
            <param name="value">
            O objeto que o teste espera que não seja do tipo especificado.
            </param>
            <param name="wrongType">
            O tipo que <paramref name="value"/> não deve ser.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null and
            <paramref name="wrongType"/> is in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotInstanceOfType(System.Object,System.Type,System.String)">
            <summary>
            Testa se o objeto especificado não é uma instância do tipo
            incorreto e gera uma exceção se o tipo especificado está na
            hierarquia de herança do objeto.
            </summary>
            <param name="value">
            O objeto que o teste espera que não seja do tipo especificado.
            </param>
            <param name="wrongType">
            O tipo que <paramref name="value"/> não deve ser.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="value"/>
            é uma instância de <paramref name="wrongType"/>. A mensagem é mostrada
            nos resultados de teste.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null and
            <paramref name="wrongType"/> is in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotInstanceOfType(System.Object,System.Type,System.String,System.Object[])">
            <summary>
            Testa se o objeto especificado não é uma instância do tipo
            incorreto e gera uma exceção se o tipo especificado está na
            hierarquia de herança do objeto.
            </summary>
            <param name="value">
            O objeto que o teste espera que não seja do tipo especificado.
            </param>
            <param name="wrongType">
            O tipo que <paramref name="value"/> não deve ser.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="value"/>
            é uma instância de <paramref name="wrongType"/>. A mensagem é mostrada
            nos resultados de teste.
            </param>
            <param name="parameters">
            Uma matriz de parâmetros a serem usados ao formatar <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null and
            <paramref name="wrongType"/> is in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Fail">
            <summary>
            Gera uma AssertFailedException.
            </summary>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Fail(System.String)">
            <summary>
            Gera uma AssertFailedException.
            </summary>
            <param name="message">
            A mensagem a ser incluída na exceção. A mensagem é mostrada nos
            resultados de teste.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Fail(System.String,System.Object[])">
            <summary>
            Gera uma AssertFailedException.
            </summary>
            <param name="message">
            A mensagem a ser incluída na exceção. A mensagem é mostrada nos
            resultados de teste.
            </param>
            <param name="parameters">
            Uma matriz de parâmetros a serem usados ao formatar <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Inconclusive">
            <summary>
            Gera uma AssertInconclusiveException.
            </summary>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Inconclusive(System.String)">
            <summary>
            Gera uma AssertInconclusiveException.
            </summary>
            <param name="message">
            A mensagem a ser incluída na exceção. A mensagem é mostrada nos
            resultados de teste.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Inconclusive(System.String,System.Object[])">
            <summary>
            Gera uma AssertInconclusiveException.
            </summary>
            <param name="message">
            A mensagem a ser incluída na exceção. A mensagem é mostrada nos
            resultados de teste.
            </param>
            <param name="parameters">
            Uma matriz de parâmetros a serem usados ao formatar <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Equals(System.Object,System.Object)">
            <summary>
            Os métodos estático igual a sobrecargas são usados para comparar instâncias de dois tipos em relação à igualdade de
            referência. Esse método <b>não</b> deve ser usado para comparar a igualdade de
            duas instâncias. Esse objeto <b>sempre</b> gerará Assert.Fail. Use
            Assert.AreEqual e sobrecargas associadas nos testes de unidade.
            </summary>
            <param name="objA"> Objeto A </param>
            <param name="objB"> Objeto B </param>
            <returns> Sempre falso. </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Action)">
            <summary>
            Testa se o código especificado pelo delegado <paramref name="action"/> gera a exceção exata especificada de tipo <typeparamref name="T"/> (e não de tipo derivado)
            e gera
            <code>
            AssertFailedException
            </code>
            se o código não gera uma exceção ou gera uma exceção de outro tipo diferente de <typeparamref name="T"/>.
            </summary>
            <param name="action">
            Delegado ao código a ser testado e que é esperado que gere exceção.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            O tipo de exceção que se espera que seja gerada.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Action,System.String)">
            <summary>
            Testa se o código especificado pelo delegado <paramref name="action"/> gera a exceção exata especificada de tipo <typeparamref name="T"/> (e não de tipo derivado)
            e gera
            <code>
            AssertFailedException
            </code>
            se o código não gera uma exceção ou gera uma exceção de outro tipo diferente de <typeparamref name="T"/>.
            </summary>
            <param name="action">
            Delegado ao código a ser testado e que é esperado que gere exceção.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="action"/>
            não gera exceção de tipo <typeparamref name="T"/>.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            O tipo de exceção que se espera que seja gerada.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Func{System.Object})">
            <summary>
            Testa se o código especificado pelo delegado <paramref name="action"/> gera a exceção exata especificada de tipo <typeparamref name="T"/> (e não de tipo derivado)
            e gera
            <code>
            AssertFailedException
            </code>
            se o código não gera uma exceção ou gera uma exceção de outro tipo diferente de <typeparamref name="T"/>.
            </summary>
            <param name="action">
            Delegado ao código a ser testado e que é esperado que gere exceção.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            O tipo de exceção que se espera que seja gerada.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Func{System.Object},System.String)">
            <summary>
            Testa se o código especificado pelo delegado <paramref name="action"/> gera a exceção exata especificada de tipo <typeparamref name="T"/> (e não de tipo derivado)
            e gera
            <code>
            AssertFailedException
            </code>
            se o código não gera uma exceção ou gera uma exceção de outro tipo diferente de <typeparamref name="T"/>.
            </summary>
            <param name="action">
            Delegado ao código a ser testado e que é esperado que gere exceção.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="action"/>
            não gera exceção de tipo <typeparamref name="T"/>.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            O tipo de exceção que se espera que seja gerada.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Func{System.Object},System.String,System.Object[])">
            <summary>
            Testa se o código especificado pelo delegado <paramref name="action"/> gera a exceção exata especificada de tipo <typeparamref name="T"/> (e não de tipo derivado)
            e gera
            <code>
            AssertFailedException
            </code>
            se o código não gera uma exceção ou gera uma exceção de outro tipo diferente de <typeparamref name="T"/>.
            </summary>
            <param name="action">
            Delegado ao código a ser testado e que é esperado que gere exceção.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="action"/>
            não gera exceção de tipo <typeparamref name="T"/>.
            </param>
            <param name="parameters">
            Uma matriz de parâmetros a serem usados ao formatar <paramref name="message"/>.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throw exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            O tipo de exceção que se espera que seja gerada.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Action,System.String,System.Object[])">
            <summary>
            Testa se o código especificado pelo delegado <paramref name="action"/> gera a exceção exata especificada de tipo <typeparamref name="T"/> (e não de tipo derivado)
            e gera
            <code>
            AssertFailedException
            </code>
            se o código não gera uma exceção ou gera uma exceção de outro tipo diferente de <typeparamref name="T"/>.
            </summary>
            <param name="action">
            Delegado ao código a ser testado e que é esperado que gere exceção.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="action"/>
            não gera exceção de tipo <typeparamref name="T"/>.
            </param>
            <param name="parameters">
            Uma matriz de parâmetros a serem usados ao formatar <paramref name="message"/>.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            O tipo de exceção que se espera que seja gerada.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsExceptionAsync``1(System.Func{System.Threading.Tasks.Task})">
            <summary>
            Testa se o código especificado pelo delegado <paramref name="action"/> gera a exceção exata especificada de tipo <typeparamref name="T"/> (e não de tipo derivado)
            e gera
            <code>
            AssertFailedException
            </code>
            se o código não gera uma exceção ou gera uma exceção de outro tipo diferente de <typeparamref name="T"/>.
            </summary>
            <param name="action">
            Delegado ao código a ser testado e que é esperado que gere exceção.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            O <see cref="T:System.Threading.Tasks.Task"/> executando o representante.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsExceptionAsync``1(System.Func{System.Threading.Tasks.Task},System.String)">
            <summary>
            Testa se o código especificado pelo delegado <paramref name="action"/> gera a exceção exata especificada de tipo <typeparamref name="T"/> (e não de tipo derivado)
            e gera <code>AssertFailedException</code> se o código não gera uma exceção ou gera uma exceção de outro tipo diferente de <typeparamref name="T"/>.
            </summary>
            <param name="action">Delegado ao código a ser testado e que é esperado que gere exceção.</param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="action"/>
            não gera exceção de tipo <typeparamref name="T"/>.
            </param>
            <typeparam name="T">Type of exception expected to be thrown.</typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            O <see cref="T:System.Threading.Tasks.Task"/> executando o representante.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsExceptionAsync``1(System.Func{System.Threading.Tasks.Task},System.String,System.Object[])">
            <summary>
            Testa se o código especificado pelo delegado <paramref name="action"/> gera a exceção exata especificada de tipo <typeparamref name="T"/> (e não de tipo derivado)
            e gera <code>AssertFailedException</code> se o código não gera uma exceção ou gera uma exceção de outro tipo diferente de <typeparamref name="T"/>.
            </summary>
            <param name="action">Delegado ao código a ser testado e que é esperado que gere exceção.</param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="action"/>
            não gera exceção de tipo <typeparamref name="T"/>.
            </param>
            <param name="parameters">
            Uma matriz de parâmetros a serem usados ao formatar <paramref name="message"/>.
            </param>
            <typeparam name="T">Type of exception expected to be thrown.</typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            O <see cref="T:System.Threading.Tasks.Task"/> executando o representante.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ReplaceNullChars(System.String)">
            <summary>
            Substitui os caracteres nulos ('\0') por "\\0".
            </summary>
            <param name="input">
            A cadeia de caracteres a ser pesquisada.
            </param>
            <returns>
            A cadeia de caracteres convertida com os caracteres nulos substituídos por "\\0".
            </returns>
            <remarks>
            This is only public and still present to preserve compatibility with the V1 framework.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.HandleFail(System.String,System.String,System.Object[])">
            <summary>
            Função auxiliar que cria e gera uma AssertionFailedException
            </summary>
            <param name="assertionName">
            nome da asserção que gera uma exceção
            </param>
            <param name="message">
            mensagem que descreve as condições da falha de asserção
            </param>
            <param name="parameters">
            Os parâmetros.
            </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.CheckParameterNotNull(System.Object,System.String,System.String,System.String,System.Object[])">
            <summary>
            Verifica o parâmetro das condições válidas
            </summary>
            <param name="param">
            O parâmetro.
            </param>
            <param name="assertionName">
            O Nome da asserção.
            </param>
            <param name="parameterName">
            nome do parâmetro
            </param>
            <param name="message">
            mensagem da exceção de parâmetro inválido
            </param>
            <param name="parameters">
            Os parâmetros.
            </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ReplaceNulls(System.Object)">
            <summary>
            Converte com segurança um objeto em uma cadeia de caracteres manipulando valores e caracteres nulos.
            Os valores nulos são convertidos em "(null)". Os caracteres nulos são convertidos em "\\0".
            </summary>
            <param name="input">
            O objeto a ser convertido em uma cadeia de caracteres.
            </param>
            <returns>
            A cadeia de caracteres convertida.
            </returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert">
            <summary>
            A asserção da cadeia de caracteres.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.That">
            <summary>
            Obtém a instância singleton da funcionalidade CollectionAssert.
            </summary>
            <remarks>
            Users can use this to plug-in custom assertions through C# extension methods.
            For instance, the signature of a custom assertion provider could be "public static void ContainsWords(this StringAssert cusomtAssert, string value, ICollection substrings)"
            Users could then use a syntax similar to the default assertions which in this case is "StringAssert.That.ContainsWords(value, substrings);"
            More documentation is at "https://github.com/Microsoft/testfx-docs".
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Contains(System.String,System.String)">
            <summary>
            Testa se a cadeia de caracteres especificada contém a subcadeia especificada
            e gera uma exceção se a subcadeia não ocorre na
            cadeia de teste.
            </summary>
            <param name="value">
            A cadeia de caracteres que se espera que contenha <paramref name="substring"/>.
            </param>
            <param name="substring">
            A cadeia de caracteres que se espera que ocorra em <paramref name="value"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="substring"/> is not found in
            <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Contains(System.String,System.String,System.String)">
            <summary>
            Testa se a cadeia de caracteres especificada contém a subcadeia especificada
            e gera uma exceção se a subcadeia não ocorre na
            cadeia de teste.
            </summary>
            <param name="value">
            A cadeia de caracteres que se espera que contenha <paramref name="substring"/>.
            </param>
            <param name="substring">
            A cadeia de caracteres que se espera que ocorra em <paramref name="value"/>.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="substring"/>
            não está em <paramref name="value"/>. A mensagem é mostrada nos
            resultados de teste.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="substring"/> is not found in
            <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Contains(System.String,System.String,System.String,System.Object[])">
            <summary>
            Testa se a cadeia de caracteres especificada contém a subcadeia especificada
            e gera uma exceção se a subcadeia não ocorre na
            cadeia de teste.
            </summary>
            <param name="value">
            A cadeia de caracteres que se espera que contenha <paramref name="substring"/>.
            </param>
            <param name="substring">
            A cadeia de caracteres que se espera que ocorra em <paramref name="value"/>.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="substring"/>
            não está em <paramref name="value"/>. A mensagem é mostrada nos
            resultados de teste.
            </param>
            <param name="parameters">
            Uma matriz de parâmetros a serem usados ao formatar <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="substring"/> is not found in
            <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.StartsWith(System.String,System.String)">
            <summary>
            Testa se a cadeia de caracteres especificada começa com a subcadeia especificada
            e gera uma exceção se a cadeia de teste não começa com a
            subcadeia.
            </summary>
            <param name="value">
            A cadeia de caracteres que se espera que comece com <paramref name="substring"/>.
            </param>
            <param name="substring">
            A cadeia de caracteres que se espera que seja um prefixo de <paramref name="value"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not begin with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.StartsWith(System.String,System.String,System.String)">
            <summary>
            Testa se a cadeia de caracteres especificada começa com a subcadeia especificada
            e gera uma exceção se a cadeia de teste não começa com a
            subcadeia.
            </summary>
            <param name="value">
            A cadeia de caracteres que se espera que comece com <paramref name="substring"/>.
            </param>
            <param name="substring">
            A cadeia de caracteres que se espera que seja um prefixo de <paramref name="value"/>.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="value"/>
            não começa com <paramref name="substring"/>. A mensagem é
            mostrada nos resultados de teste.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not begin with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.StartsWith(System.String,System.String,System.String,System.Object[])">
            <summary>
            Testa se a cadeia de caracteres especificada começa com a subcadeia especificada
            e gera uma exceção se a cadeia de teste não começa com a
            subcadeia.
            </summary>
            <param name="value">
            A cadeia de caracteres que se espera que comece com <paramref name="substring"/>.
            </param>
            <param name="substring">
            A cadeia de caracteres que se espera que seja um prefixo de <paramref name="value"/>.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="value"/>
            não começa com <paramref name="substring"/>. A mensagem é
            mostrada nos resultados de teste.
            </param>
            <param name="parameters">
            Uma matriz de parâmetros a serem usados ao formatar <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not begin with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.EndsWith(System.String,System.String)">
            <summary>
            Testa se a cadeia de caracteres especificada termina com a subcadeia especificada
            e gera uma exceção se a cadeia de teste não termina com a
            subcadeia.
            </summary>
            <param name="value">
            A cadeia de caracteres que se espera que termine com <paramref name="substring"/>.
            </param>
            <param name="substring">
            A cadeia de caracteres que se espera que seja um sufixo de <paramref name="value"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not end with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.EndsWith(System.String,System.String,System.String)">
            <summary>
            Testa se a cadeia de caracteres especificada termina com a subcadeia especificada
            e gera uma exceção se a cadeia de teste não termina com a
            subcadeia.
            </summary>
            <param name="value">
            A cadeia de caracteres que se espera que termine com <paramref name="substring"/>.
            </param>
            <param name="substring">
            A cadeia de caracteres que se espera que seja um sufixo de <paramref name="value"/>.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="value"/>
            não termina com <paramref name="substring"/>. A mensagem é
            mostrada nos resultados de teste.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not end with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.EndsWith(System.String,System.String,System.String,System.Object[])">
            <summary>
            Testa se a cadeia de caracteres especificada termina com a subcadeia especificada
            e gera uma exceção se a cadeia de teste não termina com a
            subcadeia.
            </summary>
            <param name="value">
            A cadeia de caracteres que se espera que termine com <paramref name="substring"/>.
            </param>
            <param name="substring">
            A cadeia de caracteres que se espera que seja um sufixo de <paramref name="value"/>.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="value"/>
            não termina com <paramref name="substring"/>. A mensagem é
            mostrada nos resultados de teste.
            </param>
            <param name="parameters">
            Uma matriz de parâmetros a serem usados ao formatar <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not end with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Matches(System.String,System.Text.RegularExpressions.Regex)">
            <summary>
            Testa se a cadeia de caracteres especificada corresponde a uma expressão regular e
            gera uma exceção se a cadeia não corresponde à expressão.
            </summary>
            <param name="value">
            A cadeia de caracteres que se espera que corresponda a <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            A expressão regular com a qual se espera que <paramref name="value"/> tenha
            correspondência.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not match
            <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Matches(System.String,System.Text.RegularExpressions.Regex,System.String)">
            <summary>
            Testa se a cadeia de caracteres especificada corresponde a uma expressão regular e
            gera uma exceção se a cadeia não corresponde à expressão.
            </summary>
            <param name="value">
            A cadeia de caracteres que se espera que corresponda a <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            A expressão regular com a qual se espera que <paramref name="value"/> tenha
            correspondência.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="value"/>
            não corresponde a <paramref name="pattern"/>. A mensagem é mostrada nos
            resultados de teste.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not match
            <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Matches(System.String,System.Text.RegularExpressions.Regex,System.String,System.Object[])">
            <summary>
            Testa se a cadeia de caracteres especificada corresponde a uma expressão regular e
            gera uma exceção se a cadeia não corresponde à expressão.
            </summary>
            <param name="value">
            A cadeia de caracteres que se espera que corresponda a <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            A expressão regular com a qual se espera que <paramref name="value"/> tenha
            correspondência.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="value"/>
            não corresponde a <paramref name="pattern"/>. A mensagem é mostrada nos
            resultados de teste.
            </param>
            <param name="parameters">
            Uma matriz de parâmetros a serem usados ao formatar <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not match
            <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.DoesNotMatch(System.String,System.Text.RegularExpressions.Regex)">
            <summary>
            Testa se a cadeia de caracteres especificada não corresponde a uma expressão regular
            e gera uma exceção se a cadeia corresponde à expressão.
            </summary>
            <param name="value">
            A cadeia de caracteres que se espera que não corresponda a <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            A expressão regular com a qual se espera que <paramref name="value"/> é
            esperado não corresponder.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> matches <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.DoesNotMatch(System.String,System.Text.RegularExpressions.Regex,System.String)">
            <summary>
            Testa se a cadeia de caracteres especificada não corresponde a uma expressão regular
            e gera uma exceção se a cadeia corresponde à expressão.
            </summary>
            <param name="value">
            A cadeia de caracteres que se espera que não corresponda a <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            A expressão regular com a qual se espera que <paramref name="value"/> é
            esperado não corresponder.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="value"/>
            corresponde a <paramref name="pattern"/>. A mensagem é mostrada nos resultados de
            teste.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> matches <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.DoesNotMatch(System.String,System.Text.RegularExpressions.Regex,System.String,System.Object[])">
            <summary>
            Testa se a cadeia de caracteres especificada não corresponde a uma expressão regular
            e gera uma exceção se a cadeia corresponde à expressão.
            </summary>
            <param name="value">
            A cadeia de caracteres que se espera que não corresponda a <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            A expressão regular com a qual se espera que <paramref name="value"/> é
            esperado não corresponder.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="value"/>
            corresponde a <paramref name="pattern"/>. A mensagem é mostrada nos resultados de
            teste.
            </param>
            <param name="parameters">
            Uma matriz de parâmetros a serem usados ao formatar <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> matches <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert">
            <summary>
            Uma coleção de classes auxiliares para testar várias condições associadas
            às coleções nos testes de unidade. Se a condição testada não é
            atendida, uma exceção é gerada.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.That">
            <summary>
            Obtém a instância singleton da funcionalidade CollectionAssert.
            </summary>
            <remarks>
            Users can use this to plug-in custom assertions through C# extension methods.
            For instance, the signature of a custom assertion provider could be "public static void AreEqualUnordered(this CollectionAssert cusomtAssert, ICollection expected, ICollection actual)"
            Users could then use a syntax similar to the default assertions which in this case is "CollectionAssert.That.AreEqualUnordered(list1, list2);"
            More documentation is at "https://github.com/Microsoft/testfx-docs".
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.Contains(System.Collections.ICollection,System.Object)">
            <summary>
            Testa se a coleção especificada contém o elemento especificado
            e gera uma exceção se o elemento não está na coleção.
            </summary>
            <param name="collection">
            A coleção na qual pesquisar o elemento.
            </param>
            <param name="element">
            O elemento que se espera que esteja na coleção.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is not found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.Contains(System.Collections.ICollection,System.Object,System.String)">
            <summary>
            Testa se a coleção especificada contém o elemento especificado
            e gera uma exceção se o elemento não está na coleção.
            </summary>
            <param name="collection">
            A coleção na qual pesquisar o elemento.
            </param>
            <param name="element">
            O elemento que se espera que esteja na coleção.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="element"/>
            não está em <paramref name="collection"/>. A mensagem é mostrada nos
            resultados de teste.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is not found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.Contains(System.Collections.ICollection,System.Object,System.String,System.Object[])">
            <summary>
            Testa se a coleção especificada contém o elemento especificado
            e gera uma exceção se o elemento não está na coleção.
            </summary>
            <param name="collection">
            A coleção na qual pesquisar o elemento.
            </param>
            <param name="element">
            O elemento que se espera que esteja na coleção.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="element"/>
            não está em <paramref name="collection"/>. A mensagem é mostrada nos
            resultados de teste.
            </param>
            <param name="parameters">
            Uma matriz de parâmetros a serem usados ao formatar <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is not found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.DoesNotContain(System.Collections.ICollection,System.Object)">
            <summary>
            Testa se a coleção especificada não contém o elemento
            especificado e gera uma exceção se o elemento está na coleção.
            </summary>
            <param name="collection">
            A coleção na qual pesquisar o elemento.
            </param>
            <param name="element">
            O elemento que se espera que não esteja na coleção.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.DoesNotContain(System.Collections.ICollection,System.Object,System.String)">
            <summary>
            Testa se a coleção especificada não contém o elemento
            especificado e gera uma exceção se o elemento está na coleção.
            </summary>
            <param name="collection">
            A coleção na qual pesquisar o elemento.
            </param>
            <param name="element">
            O elemento que se espera que não esteja na coleção.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="element"/>
            está em <paramref name="collection"/>. A mensagem é mostrada nos resultados de
            teste.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.DoesNotContain(System.Collections.ICollection,System.Object,System.String,System.Object[])">
            <summary>
            Testa se a coleção especificada não contém o elemento
            especificado e gera uma exceção se o elemento está na coleção.
            </summary>
            <param name="collection">
            A coleção na qual pesquisar o elemento.
            </param>
            <param name="element">
            O elemento que se espera que não esteja na coleção.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="element"/>
            está em <paramref name="collection"/>. A mensagem é mostrada nos resultados de
            teste.
            </param>
            <param name="parameters">
            Uma matriz de parâmetros a serem usados ao formatar <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreNotNull(System.Collections.ICollection)">
            <summary>
            Testa se todos os itens na coleção especificada são não nulos e gera
            uma exceção se algum elemento é nulo.
            </summary>
            <param name="collection">
            A coleção na qual pesquisar elementos nulos.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a null element is found in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreNotNull(System.Collections.ICollection,System.String)">
            <summary>
            Testa se todos os itens na coleção especificada são não nulos e gera
            uma exceção se algum elemento é nulo.
            </summary>
            <param name="collection">
            A coleção na qual pesquisar elementos nulos.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="collection"/>
            contém um elemento nulo. A mensagem é mostrada nos resultados de teste.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a null element is found in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreNotNull(System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Testa se todos os itens na coleção especificada são não nulos e gera
            uma exceção se algum elemento é nulo.
            </summary>
            <param name="collection">
            A coleção na qual pesquisar elementos nulos.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="collection"/>
            contém um elemento nulo. A mensagem é mostrada nos resultados de teste.
            </param>
            <param name="parameters">
            Uma matriz de parâmetros a serem usados ao formatar <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a null element is found in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreUnique(System.Collections.ICollection)">
            <summary>
            Testa se todos os itens na coleção especificada são exclusivos ou não e
            gera uma exceção se dois elementos na coleção são iguais.
            </summary>
            <param name="collection">
            A coleção na qual pesquisar elementos duplicados.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a two or more equal elements are found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreUnique(System.Collections.ICollection,System.String)">
            <summary>
            Testa se todos os itens na coleção especificada são exclusivos ou não e
            gera uma exceção se dois elementos na coleção são iguais.
            </summary>
            <param name="collection">
            A coleção na qual pesquisar elementos duplicados.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="collection"/>
            contém pelo menos um elemento duplicado. A mensagem é mostrada nos
            resultados de teste.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a two or more equal elements are found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreUnique(System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Testa se todos os itens na coleção especificada são exclusivos ou não e
            gera uma exceção se dois elementos na coleção são iguais.
            </summary>
            <param name="collection">
            A coleção na qual pesquisar elementos duplicados.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="collection"/>
            contém pelo menos um elemento duplicado. A mensagem é mostrada nos
            resultados de teste.
            </param>
            <param name="parameters">
            Uma matriz de parâmetros a serem usados ao formatar <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a two or more equal elements are found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsSubsetOf(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Testa se uma coleção é um subconjunto de outra coleção e
            gera uma exceção se algum elemento no subconjunto não está também no
            superconjunto.
            </summary>
            <param name="subset">
            A coleção que se espera que seja um subconjunto de <paramref name="superset"/>.
            </param>
            <param name="superset">
            A coleção que se espera que seja um superconjunto de <paramref name="subset"/>
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="subset"/> is not found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsSubsetOf(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            Testa se uma coleção é um subconjunto de outra coleção e
            gera uma exceção se algum elemento no subconjunto não está também no
            superconjunto.
            </summary>
            <param name="subset">
            A coleção que se espera que seja um subconjunto de <paramref name="superset"/>.
            </param>
            <param name="superset">
            A coleção que se espera que seja um superconjunto de <paramref name="subset"/>
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando um elemento em
            <paramref name="subset"/> não é encontrado em <paramref name="superset"/>.
            A mensagem é mostrada nos resultados de teste.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="subset"/> is not found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsSubsetOf(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Testa se uma coleção é um subconjunto de outra coleção e
            gera uma exceção se algum elemento no subconjunto não está também no
            superconjunto.
            </summary>
            <param name="subset">
            A coleção que se espera que seja um subconjunto de <paramref name="superset"/>.
            </param>
            <param name="superset">
            A coleção que se espera que seja um superconjunto de <paramref name="subset"/>
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando um elemento em
            <paramref name="subset"/> não é encontrado em <paramref name="superset"/>.
            A mensagem é mostrada nos resultados de teste.
            </param>
            <param name="parameters">
            Uma matriz de parâmetros a serem usados ao formatar <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="subset"/> is not found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsNotSubsetOf(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Testa se uma coleção não é um subconjunto de outra coleção e
            gera uma exceção se todos os elementos no subconjunto também estão no
            superconjunto.
            </summary>
            <param name="subset">
            A coleção que se espera que não seja um subconjunto de <paramref name="superset"/>.
            </param>
            <param name="superset">
            A coleção que se espera que não seja um superconjunto de <paramref name="subset"/>
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if every element in <paramref name="subset"/> is also found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsNotSubsetOf(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            Testa se uma coleção não é um subconjunto de outra coleção e
            gera uma exceção se todos os elementos no subconjunto também estão no
            superconjunto.
            </summary>
            <param name="subset">
            A coleção que se espera que não seja um subconjunto de <paramref name="superset"/>.
            </param>
            <param name="superset">
            A coleção que se espera que não seja um superconjunto de <paramref name="subset"/>
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando todo elemento em
            <paramref name="subset"/> também é encontrado em <paramref name="superset"/>.
            A mensagem é mostrada nos resultados de teste.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if every element in <paramref name="subset"/> is also found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsNotSubsetOf(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Testa se uma coleção não é um subconjunto de outra coleção e
            gera uma exceção se todos os elementos no subconjunto também estão no
            superconjunto.
            </summary>
            <param name="subset">
            A coleção que se espera que não seja um subconjunto de <paramref name="superset"/>.
            </param>
            <param name="superset">
            A coleção que se espera que não seja um superconjunto de <paramref name="subset"/>
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando todo elemento em
            <paramref name="subset"/> também é encontrado em <paramref name="superset"/>.
            A mensagem é mostrada nos resultados de teste.
            </param>
            <param name="parameters">
            Uma matriz de parâmetros a serem usados ao formatar <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if every element in <paramref name="subset"/> is also found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEquivalent(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Testa se duas coleções contêm os mesmos elementos e gera uma
            exceção se alguma das coleções contém um elemento que não está presente na outra
            coleção.
            </summary>
            <param name="expected">
            A primeira coleção a ser comparada. Ela contém os elementos esperados pelo
            teste.
            </param>
            <param name="actual">
            A segunda coleção a ser comparada. Trata-se da coleção produzida
            pelo código em teste.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element was found in one of the collections but not
            the other.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEquivalent(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            Testa se duas coleções contêm os mesmos elementos e gera uma
            exceção se alguma das coleções contém um elemento que não está presente na outra
            coleção.
            </summary>
            <param name="expected">
            A primeira coleção a ser comparada. Ela contém os elementos esperados pelo
            teste.
            </param>
            <param name="actual">
            A segunda coleção a ser comparada. Trata-se da coleção produzida
            pelo código em teste.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando um elemento foi encontrado
            em uma das coleções, mas não na outra. A mensagem é mostrada
            nos resultados de teste.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element was found in one of the collections but not
            the other.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEquivalent(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Testa se duas coleções contêm os mesmos elementos e gera uma
            exceção se alguma das coleções contém um elemento que não está presente na outra
            coleção.
            </summary>
            <param name="expected">
            A primeira coleção a ser comparada. Ela contém os elementos esperados pelo
            teste.
            </param>
            <param name="actual">
            A segunda coleção a ser comparada. Trata-se da coleção produzida
            pelo código em teste.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando um elemento foi encontrado
            em uma das coleções, mas não na outra. A mensagem é mostrada
            nos resultados de teste.
            </param>
            <param name="parameters">
            Uma matriz de parâmetros a serem usados ao formatar <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element was found in one of the collections but not
            the other.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEquivalent(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Testa se duas coleções contêm elementos diferentes e gera uma
            exceção se as duas coleções contêm elementos idênticos sem levar em consideração
            a ordem.
            </summary>
            <param name="expected">
            A primeira coleção a ser comparada. Ela contém os elementos que o teste
            espera que sejam diferentes em relação à coleção real.
            </param>
            <param name="actual">
            A segunda coleção a ser comparada. Trata-se da coleção produzida
            pelo código em teste.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if the two collections contained the same elements, including
            the same number of duplicate occurrences of each element.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEquivalent(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            Testa se duas coleções contêm elementos diferentes e gera uma
            exceção se as duas coleções contêm elementos idênticos sem levar em consideração
            a ordem.
            </summary>
            <param name="expected">
            A primeira coleção a ser comparada. Ela contém os elementos que o teste
            espera que sejam diferentes em relação à coleção real.
            </param>
            <param name="actual">
            A segunda coleção a ser comparada. Trata-se da coleção produzida
            pelo código em teste.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="actual"/>
            contém os mesmos elementos que <paramref name="expected"/>. A mensagem
            é mostrada nos resultados de teste.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if the two collections contained the same elements, including
            the same number of duplicate occurrences of each element.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEquivalent(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Testa se duas coleções contêm elementos diferentes e gera uma
            exceção se as duas coleções contêm elementos idênticos sem levar em consideração
            a ordem.
            </summary>
            <param name="expected">
            A primeira coleção a ser comparada. Ela contém os elementos que o teste
            espera que sejam diferentes em relação à coleção real.
            </param>
            <param name="actual">
            A segunda coleção a ser comparada. Trata-se da coleção produzida
            pelo código em teste.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="actual"/>
            contém os mesmos elementos que <paramref name="expected"/>. A mensagem
            é mostrada nos resultados de teste.
            </param>
            <param name="parameters">
            Uma matriz de parâmetros a serem usados ao formatar <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if the two collections contained the same elements, including
            the same number of duplicate occurrences of each element.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreInstancesOfType(System.Collections.ICollection,System.Type)">
            <summary>
            Testa se todos os elementos na coleção especificada são instâncias
            do tipo esperado e gera uma exceção se o tipo esperado não
            está na hierarquia de herança de um ou mais dos elementos.
            </summary>
            <param name="collection">
            A coleção que contém elementos que o teste espera que sejam do
            tipo especificado.
            </param>
            <param name="expectedType">
            O tipo esperado de cada elemento de <paramref name="collection"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="collection"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of an element in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreInstancesOfType(System.Collections.ICollection,System.Type,System.String)">
            <summary>
            Testa se todos os elementos na coleção especificada são instâncias
            do tipo esperado e gera uma exceção se o tipo esperado não
            está na hierarquia de herança de um ou mais dos elementos.
            </summary>
            <param name="collection">
            A coleção que contém elementos que o teste espera que sejam do
            tipo especificado.
            </param>
            <param name="expectedType">
            O tipo esperado de cada elemento de <paramref name="collection"/>.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando um elemento em
            <paramref name="collection"/> não é uma instância de
            <paramref name="expectedType"/>. A mensagem é mostrada nos resultados de teste.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="collection"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of an element in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreInstancesOfType(System.Collections.ICollection,System.Type,System.String,System.Object[])">
            <summary>
            Testa se todos os elementos na coleção especificada são instâncias
            do tipo esperado e gera uma exceção se o tipo esperado não
            está na hierarquia de herança de um ou mais dos elementos.
            </summary>
            <param name="collection">
            A coleção que contém elementos que o teste espera que sejam do
            tipo especificado.
            </param>
            <param name="expectedType">
            O tipo esperado de cada elemento de <paramref name="collection"/>.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando um elemento em
            <paramref name="collection"/> não é uma instância de
            <paramref name="expectedType"/>. A mensagem é mostrada nos resultados de teste.
            </param>
            <param name="parameters">
            Uma matriz de parâmetros a serem usados ao formatar <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="collection"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of an element in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Testa se as coleções especificadas são iguais e gera uma exceção
            se as duas coleções não são iguais. A igualdade é definida como tendo os mesmos
            elementos na mesma ordem e quantidade. Referências diferentes ao mesmo
            valor são consideradas iguais.
            </summary>
            <param name="expected">
            A primeira coleção a ser comparada. Trata-se da coleção esperada pelo teste.
            </param>
            <param name="actual">
            A segunda coleção a ser comparada. Trata-se da coleção produzida pelo
            código em teste.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            Testa se as coleções especificadas são iguais e gera uma exceção
            se as duas coleções não são iguais. A igualdade é definida como tendo os mesmos
            elementos na mesma ordem e quantidade. Referências diferentes ao mesmo
            valor são consideradas iguais.
            </summary>
            <param name="expected">
            A primeira coleção a ser comparada. Trata-se da coleção esperada pelo teste.
            </param>
            <param name="actual">
            A segunda coleção a ser comparada. Trata-se da coleção produzida pelo
            código em teste.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="actual"/>
            não é igual a <paramref name="expected"/>. A mensagem é mostrada nos
            resultados de teste.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Testa se as coleções especificadas são iguais e gera uma exceção
            se as duas coleções não são iguais. A igualdade é definida como tendo os mesmos
            elementos na mesma ordem e quantidade. Referências diferentes ao mesmo
            valor são consideradas iguais.
            </summary>
            <param name="expected">
            A primeira coleção a ser comparada. Trata-se da coleção esperada pelo teste.
            </param>
            <param name="actual">
            A segunda coleção a ser comparada. Trata-se da coleção produzida pelo
            código em teste.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="actual"/>
            não é igual a <paramref name="expected"/>. A mensagem é mostrada nos
            resultados de teste.
            </param>
            <param name="parameters">
            Uma matriz de parâmetros a serem usados ao formatar <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Testa se as coleções especificadas são desiguais e gera uma exceção
            se as duas coleções são iguais. A igualdade é definida como tendo os mesmos
            elementos na mesma ordem e quantidade. Referências diferentes ao mesmo
            valor são consideradas iguais.
            </summary>
            <param name="notExpected">
            A primeira coleção a ser comparada. Trata-se da coleção que o teste espera
            que não corresponda a <paramref name="actual"/>.
            </param>
            <param name="actual">
            A segunda coleção a ser comparada. Trata-se da coleção produzida pelo
            código em teste.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            Testa se as coleções especificadas são desiguais e gera uma exceção
            se as duas coleções são iguais. A igualdade é definida como tendo os mesmos
            elementos na mesma ordem e quantidade. Referências diferentes ao mesmo
            valor são consideradas iguais.
            </summary>
            <param name="notExpected">
            A primeira coleção a ser comparada. Trata-se da coleção que o teste espera
            que não corresponda a <paramref name="actual"/>.
            </param>
            <param name="actual">
            A segunda coleção a ser comparada. Trata-se da coleção produzida pelo
            código em teste.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="actual"/>
            é igual a <paramref name="notExpected"/>. A mensagem é mostrada nos
            resultados de teste.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Testa se as coleções especificadas são desiguais e gera uma exceção
            se as duas coleções são iguais. A igualdade é definida como tendo os mesmos
            elementos na mesma ordem e quantidade. Referências diferentes ao mesmo
            valor são consideradas iguais.
            </summary>
            <param name="notExpected">
            A primeira coleção a ser comparada. Trata-se da coleção que o teste espera
            que não corresponda a <paramref name="actual"/>.
            </param>
            <param name="actual">
            A segunda coleção a ser comparada. Trata-se da coleção produzida pelo
            código em teste.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="actual"/>
            é igual a <paramref name="notExpected"/>. A mensagem é mostrada nos
            resultados de teste.
            </param>
            <param name="parameters">
            Uma matriz de parâmetros a serem usados ao formatar <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer)">
            <summary>
            Testa se as coleções especificadas são iguais e gera uma exceção
            se as duas coleções não são iguais. A igualdade é definida como tendo os mesmos
            elementos na mesma ordem e quantidade. Referências diferentes ao mesmo
            valor são consideradas iguais.
            </summary>
            <param name="expected">
            A primeira coleção a ser comparada. Trata-se da coleção esperada pelo teste.
            </param>
            <param name="actual">
            A segunda coleção a ser comparada. Trata-se da coleção produzida pelo
            código em teste.
            </param>
            <param name="comparer">
            A implementação de comparação a ser usada ao comparar elementos da coleção.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer,System.String)">
            <summary>
            Testa se as coleções especificadas são iguais e gera uma exceção
            se as duas coleções não são iguais. A igualdade é definida como tendo os mesmos
            elementos na mesma ordem e quantidade. Referências diferentes ao mesmo
            valor são consideradas iguais.
            </summary>
            <param name="expected">
            A primeira coleção a ser comparada. Trata-se da coleção esperada pelo teste.
            </param>
            <param name="actual">
            A segunda coleção a ser comparada. Trata-se da coleção produzida pelo
            código em teste.
            </param>
            <param name="comparer">
            A implementação de comparação a ser usada ao comparar elementos da coleção.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="actual"/>
            não é igual a <paramref name="expected"/>. A mensagem é mostrada nos
            resultados de teste.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer,System.String,System.Object[])">
            <summary>
            Testa se as coleções especificadas são iguais e gera uma exceção
            se as duas coleções não são iguais. A igualdade é definida como tendo os mesmos
            elementos na mesma ordem e quantidade. Referências diferentes ao mesmo
            valor são consideradas iguais.
            </summary>
            <param name="expected">
            A primeira coleção a ser comparada. Trata-se da coleção esperada pelo teste.
            </param>
            <param name="actual">
            A segunda coleção a ser comparada. Trata-se da coleção produzida pelo
            código em teste.
            </param>
            <param name="comparer">
            A implementação de comparação a ser usada ao comparar elementos da coleção.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="actual"/>
            não é igual a <paramref name="expected"/>. A mensagem é mostrada nos
            resultados de teste.
            </param>
            <param name="parameters">
            Uma matriz de parâmetros a serem usados ao formatar <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer)">
            <summary>
            Testa se as coleções especificadas são desiguais e gera uma exceção
            se as duas coleções são iguais. A igualdade é definida como tendo os mesmos
            elementos na mesma ordem e quantidade. Referências diferentes ao mesmo
            valor são consideradas iguais.
            </summary>
            <param name="notExpected">
            A primeira coleção a ser comparada. Trata-se da coleção que o teste espera
            que não corresponda a <paramref name="actual"/>.
            </param>
            <param name="actual">
            A segunda coleção a ser comparada. Trata-se da coleção produzida pelo
            código em teste.
            </param>
            <param name="comparer">
            A implementação de comparação a ser usada ao comparar elementos da coleção.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer,System.String)">
            <summary>
            Testa se as coleções especificadas são desiguais e gera uma exceção
            se as duas coleções são iguais. A igualdade é definida como tendo os mesmos
            elementos na mesma ordem e quantidade. Referências diferentes ao mesmo
            valor são consideradas iguais.
            </summary>
            <param name="notExpected">
            A primeira coleção a ser comparada. Trata-se da coleção que o teste espera
            que não corresponda a <paramref name="actual"/>.
            </param>
            <param name="actual">
            A segunda coleção a ser comparada. Trata-se da coleção produzida pelo
            código em teste.
            </param>
            <param name="comparer">
            A implementação de comparação a ser usada ao comparar elementos da coleção.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="actual"/>
            é igual a <paramref name="notExpected"/>. A mensagem é mostrada nos
            resultados de teste.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer,System.String,System.Object[])">
            <summary>
            Testa se as coleções especificadas são desiguais e gera uma exceção
            se as duas coleções são iguais. A igualdade é definida como tendo os mesmos
            elementos na mesma ordem e quantidade. Referências diferentes ao mesmo
            valor são consideradas iguais.
            </summary>
            <param name="notExpected">
            A primeira coleção a ser comparada. Trata-se da coleção que o teste espera
            que não corresponda a <paramref name="actual"/>.
            </param>
            <param name="actual">
            A segunda coleção a ser comparada. Trata-se da coleção produzida pelo
            código em teste.
            </param>
            <param name="comparer">
            A implementação de comparação a ser usada ao comparar elementos da coleção.
            </param>
            <param name="message">
            A mensagem a ser incluída na exceção quando <paramref name="actual"/>
            é igual a <paramref name="notExpected"/>. A mensagem é mostrada nos
            resultados de teste.
            </param>
            <param name="parameters">
            Uma matriz de parâmetros a serem usados ao formatar <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsSubsetOfHelper(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Determina se a primeira coleção é um subconjunto da segunda
            coleção. Se os conjuntos contiverem elementos duplicados, o número
            de ocorrências do elemento no subconjunto deverá ser menor ou igual
            ao número de ocorrências no superconjunto.
            </summary>
            <param name="subset">
            A coleção que o teste espera que esteja contida em <paramref name="superset"/>.
            </param>
            <param name="superset">
            A coleção que o teste espera que contenha <paramref name="subset"/>.
            </param>
            <returns>
            Verdadeiro se <paramref name="subset"/> é um subconjunto de
            <paramref name="superset"/>, caso contrário, falso.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.GetElementCounts(System.Collections.ICollection,System.Int32@)">
            <summary>
            Cria um dicionário contendo o número de ocorrências de cada
            elemento na coleção especificada.
            </summary>
            <param name="collection">
            A coleção a ser processada.
            </param>
            <param name="nullCount">
            O número de elementos nulos na coleção.
            </param>
            <returns>
            Um dicionário contendo o número de ocorrências de cada elemento
            na coleção especificada.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.FindMismatchedElement(System.Collections.ICollection,System.Collections.ICollection,System.Int32@,System.Int32@,System.Object@)">
            <summary>
            Encontra um elemento incompatível entre as duas coleções. Um elemento
            incompatível é aquele que aparece um número diferente de vezes na
            coleção esperada em relação à coleção real. É pressuposto que
            as coleções sejam referências não nulas diferentes com o
            mesmo número de elementos. O chamador é responsável por esse nível de
            verificação. Se não houver nenhum elemento incompatível, a função retornará
            falso e os parâmetros de saída não deverão ser usados.
            </summary>
            <param name="expected">
            A primeira coleção a ser comparada.
            </param>
            <param name="actual">
            A segunda coleção a ser comparada.
            </param>
            <param name="expectedCount">
            O número esperado de ocorrências de
            <paramref name="mismatchedElement"/> ou 0 se não houver nenhum elemento
            incompatível.
            </param>
            <param name="actualCount">
            O número real de ocorrências de
            <paramref name="mismatchedElement"/> ou 0 se não houver nenhum elemento
            incompatível.
            </param>
            <param name="mismatchedElement">
            O elemento incompatível (poderá ser nulo) ou nulo se não houver nenhum
            elemento incompatível.
            </param>
            <returns>
            verdadeiro se um elemento incompatível foi encontrado. Caso contrário, falso.
            </returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.ObjectComparer">
            <summary>
            compara os objetos usando object.Equals
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException">
            <summary>
            Classe base para exceções do Framework.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException.#ctor">
            <summary>
             Inicializa uma nova instância da classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException"/>.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException.#ctor(System.String,System.Exception)">
            <summary>
             Inicializa uma nova instância da classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException"/>.
            </summary>
            <param name="msg"> A mensagem. </param>
            <param name="ex"> A exceção. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException.#ctor(System.String)">
            <summary>
             Inicializa uma nova instância da classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException"/>.
            </summary>
            <param name="msg"> A mensagem. </param>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages">
            <summary>
              Uma classe de recurso fortemente tipada para pesquisar cadeias de caracteres localizadas, etc.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ResourceManager">
            <summary>
              Retorna a instância de ResourceManager armazenada em cache usada por essa classe.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.Culture">
            <summary>
              Substitui a propriedade CurrentUICulture do thread atual em todas
              as pesquisas de recursos usando essa classe de recurso fortemente tipada.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AccessStringInvalidSyntax">
            <summary>
              Pesquisa uma cadeia de caracteres localizada semelhante a A cadeia de caracteres de acesso tem sintaxe inválida.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ActualHasMismatchedElements">
            <summary>
              Pesquisa uma cadeia de caracteres localizada semelhante a A coleção esperada contém {1} ocorrência(s) de &lt;{2}&gt;. A coleção real contém {3} ocorrência(s). {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AllItemsAreUniqueFailMsg">
            <summary>
              Pesquisa uma cadeia de caracteres localizada semelhante a Item duplicado encontrado:&lt;{1}&gt;. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreEqualCaseFailMsg">
            <summary>
              Pesquisa uma cadeia de caracteres localizada semelhante a Esperado:&lt;{1}&gt;. Maiúsculas e minúsculas diferentes para o valor real:&lt;{2}&gt;. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreEqualDeltaFailMsg">
            <summary>
              Pesquisa uma cadeia de caracteres localizada semelhante a Esperada uma diferença não maior que &lt;{3}&gt; entre o valor esperado &lt;{1}&gt; e o valor real &lt;{2}&gt;. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreEqualDifferentTypesFailMsg">
            <summary>
              Pesquisa uma cadeia de caracteres localizada semelhante a Esperado:&lt;{1} ({2})&gt;. Real:&lt;{3} ({4})&gt;. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreEqualFailMsg">
            <summary>
              Pesquisa uma cadeia de caracteres localizada semelhante a Esperado:&lt;{1}&gt;. Real:&lt;{2}&gt;. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreNotEqualDeltaFailMsg">
            <summary>
              Pesquisa uma cadeia de caracteres localizada semelhante a Esperada uma diferença maior que &lt;{3}&gt; entre o valor esperado &lt;{1}&gt; e o valor real &lt;{2}&gt;. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreNotEqualFailMsg">
            <summary>
              Pesquisa uma cadeia de caracteres localizada semelhante a É esperado qualquer valor, exceto:&lt;{1}&gt;. Real:&lt;{2}&gt;. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreSameGivenValues">
            <summary>
              Pesquisa uma cadeia de caracteres localizada semelhante a Não passe tipos de valores para AreSame(). Os valores convertidos em Object nunca serão os mesmos. Considere usar AreEqual(). {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AssertionFailed">
            <summary>
              Pesquisa uma cadeia de caracteres localizada semelhante à Falha em {0}. {1}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AsyncUITestMethodNotSupported">
            <summary>
              Pesquisa uma cadeia de caracteres localizada similar a TestMethod assíncrono com UITestMethodAttribute sem suporte. Remova o assíncrono ou use o TestMethodAttribute.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.BothCollectionsEmpty">
            <summary>
              Pesquisa uma cadeia de caracteres localizada semelhante a Ambas as coleções estão vazias. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.BothCollectionsSameElements">
            <summary>
              Pesquisa uma cadeia de caracteres localizada semelhante a Ambas as coleções contêm os mesmos elementos.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.BothCollectionsSameReference">
            <summary>
              Pesquisa uma cadeia de caracteres localizada semelhante a Ambas as referências de coleções apontam para o mesmo objeto de coleção. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.BothSameElements">
            <summary>
              Pesquisa uma cadeia de caracteres localizada semelhante a Ambas as coleções contêm os mesmos elementos. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.CollectionEqualReason">
            <summary>
              Pesquisa uma cadeia de caracteres localizada semelhante a {0}({1}).
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.Common_NullInMessages">
            <summary>
              Pesquisa uma cadeia de caracteres localizada semelhante a (nulo).
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.Common_ObjectString">
            <summary>
              Pesquisa uma cadeia de caracteres localizada semelhante a (objeto).
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ContainsFail">
            <summary>
              Pesquisa uma cadeia de caracteres localizada semelhante a A cadeia de caracteres '{0}' não contém a cadeia de caracteres '{1}'. {2}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.DataDrivenResultDisplayName">
            <summary>
              Pesquisa uma cadeia de caracteres localizada semelhante a {0} ({1}).
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.DoNotUseAssertEquals">
            <summary>
              Pesquisa uma cadeia de caracteres localizada semelhante a Assert.Equals não deve ser usado para Asserções. Use Assert.AreEqual e sobrecargas em seu lugar.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ElementNumbersDontMatch">
            <summary>
              Pesquisa uma cadeia de caracteres localizada semelhante a O número de elementos nas coleções não corresponde. Esperado:&lt;{1}&gt;. Real:&lt;{2}&gt;.{0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ElementsAtIndexDontMatch">
            <summary>
              Pesquisa uma cadeia de caracteres localizada semelhante a O elemento no índice {0} não corresponde.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ElementTypesAtIndexDontMatch">
            <summary>
              Pesquisa uma cadeia de caracteres localizada semelhante a O elemento no índice {1} não é de tipo esperado. Tipo esperado:&lt;{2}&gt;. Tipo real:&lt;{3}&gt;.{0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ElementTypesAtIndexDontMatch2">
            <summary>
              Pesquisa uma cadeia de caracteres localizada semelhante a O elemento no índice {1} é (nulo). Tipo esperado:&lt;{2}&gt;.{0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.EndsWithFail">
            <summary>
              Pesquisa uma cadeia de caracteres localizada semelhante a A cadeia de caracteres '{0}' não termina com a cadeia de caracteres '{1}'. {2}..
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.EqualsTesterInvalidArgs">
            <summary>
              Pesquisa uma cadeia de caracteres localizada semelhante a Argumento inválido – EqualsTester não pode usar nulos.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ErrorInvalidCast">
            <summary>
              Pesquisa uma cadeia de caracteres localizada semelhante a Não é possível converter objeto do tipo {0} em {1}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.InternalObjectNotValid">
            <summary>
              Pesquisa uma cadeia de caracteres localizada semelhante a O objeto interno referenciado não é mais válido.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.InvalidParameterToAssert">
            <summary>
              Pesquisa uma cadeia de caracteres localizada semelhante a O parâmetro '{0}' é inválido. {1}..
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.InvalidPropertyType">
            <summary>
              Pesquisa uma cadeia de caracteres localizada semelhante a A propriedade {0} é do tipo {1}; tipo esperado {2}..
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.IsInstanceOfFailMsg">
            <summary>
              Pesquisa uma cadeia de caracteres localizada semelhante a {0} Tipo esperado:&lt;{1}&gt;. Tipo real:&lt;{2}&gt;..
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.IsMatchFail">
            <summary>
              Pesquisa uma cadeia de caracteres localizada semelhante a A cadeia de caracteres '{0}' não corresponde ao padrão '{1}'. {2}..
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.IsNotInstanceOfFailMsg">
            <summary>
              Pesquisa uma cadeia de caracteres localizada semelhante a Tipo incorreto:&lt;{1}&gt;. Tipo real:&lt;{2}&gt;. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.IsNotMatchFail">
            <summary>
              Pesquisa uma cadeia de caracteres localizada semelhante a A cadeia de caracteres '{0}' corresponde ao padrão '{1}'. {2}..
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.NoDataRow">
            <summary>
              Pesquisa uma cadeia de caracteres localizada semelhante a Nenhum DataRowAttribute especificado. Pelo menos um DataRowAttribute é necessário com DataTestMethodAttribute.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.NoExceptionThrown">
            <summary>
              Pesquisa uma cadeia de caracteres localizada semelhante a Nenhuma exceção gerada. A exceção {1} era esperada. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.NullParameterToAssert">
            <summary>
              Pesquisa uma cadeia de caracteres localizada semelhante a O parâmetro '{0}' é inválido. O valor não pode ser nulo. {1}..
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.NumberOfElementsDiff">
            <summary>
              Pesquisa uma cadeia de caracteres localizada semelhante a Número diferente de elementos.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.PrivateAccessorConstructorNotFound">
            <summary>
              Pesquisa uma cadeia de caracteres localizada semelhante a 
                 O construtor com a assinatura especificada não pôde ser encontrado. Talvez seja necessário gerar novamente seu acessador particular
                 ou o membro pode ser particular e definido em uma classe base. Se o último for verdadeiro, será necessário passar o tipo
                 que define o membro no construtor do PrivateObject.
               .
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.PrivateAccessorMemberNotFound">
            <summary>
              Pesquisa uma cadeia de caracteres localizada semelhante a 
                 O membro especificado ({0}) não pôde ser encontrado. Talvez seja necessário gerar novamente seu acessador particular
                 ou o membro pode ser particular e definido em uma classe base. Se o último for verdadeiro, será necessário passar o tipo
                 que define o membro no construtor do PrivateObject.
               .
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.StartsWithFail">
            <summary>
              Pesquisa uma cadeia de caracteres localizada semelhante a A cadeia de caracteres '{0}' não começa com a cadeia de caracteres '{1}'. {2}..
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_ExpectedExceptionTypeMustDeriveFromException">
            <summary>
              Pesquisa uma cadeia de caracteres localizada semelhante a O tipo de exceção esperado deve ser System.Exception ou um tipo derivado de System.Exception.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_FailedToGetExceptionMessage">
            <summary>
              Pesquisa uma cadeia de caracteres localizada semelhante a (Falha ao obter a mensagem para uma exceção do tipo {0} devido a uma exceção.).
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_TestMethodNoException">
            <summary>
              Pesquisa uma cadeia de caracteres localizada semelhante a O método de teste não gerou a exceção esperada {0}. {1}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_TestMethodNoExceptionDefault">
            <summary>
              Pesquisa uma cadeia de caracteres localizada semelhante a O método de teste não gerou uma exceção. Uma exceção era esperada pelo atributo {0} definido no método de teste.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_TestMethodWrongException">
            <summary>
              Pesquisa uma cadeia de caracteres localizada semelhante a O método de teste gerou a exceção {0}, mas era esperada a exceção {1}. Mensagem de exceção: {2}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_TestMethodWrongExceptionDerivedAllowed">
            <summary>
              Pesquisa uma cadeia de caracteres localizada semelhante a O método de teste gerou a exceção {0}, mas era esperado a exceção {1} ou um tipo derivado dela. Mensagem de exceção: {2}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.WrongExceptionThrown">
             <summary>
               Pesquisa uma cadeia de caracteres localizada semelhante a Exceção gerada {2}, mas a exceção {1} era esperada. {0}
            Mensagem de Exceção: {3}
            Rastreamento de Pilha: {4}.
             </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome">
            <summary>
            resultados de teste de unidade
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Failed">
            <summary>
            O teste foi executado, mas ocorreram problemas.
            Os problemas podem envolver exceções ou asserções com falha.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Inconclusive">
            <summary>
            O teste foi concluído, mas não é possível dizer se houve aprovação ou falha.
            Pode ser usado para testes anulados.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Passed">
            <summary>
            O teste foi executado sem nenhum problema.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.InProgress">
            <summary>
            O teste está em execução no momento.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Error">
            <summary>
            Ocorreu um erro de sistema ao tentarmos executar um teste.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Timeout">
            <summary>
            O tempo limite do teste foi atingido.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Aborted">
            <summary>
            O teste foi anulado pelo usuário.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Unknown">
            <summary>
            O teste está em um estado desconhecido
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.UtfHelper">
            <summary>
            Fornece funcionalidade auxiliar para a estrutura do teste de unidade
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.UtfHelper.GetExceptionMsg(System.Exception)">
            <summary>
            Obtém as mensagens de exceção, incluindo as mensagens para todas as exceções internas
            recursivamente
            </summary>
            <param name="ex">Exceção ao obter mensagens para</param>
            <returns>cadeia de caracteres com informações de mensagem de erro</returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestTimeout">
            <summary>
            Enumeração para tempos limite, a qual pode ser usada com a classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute"/>.
            O tipo de enumeração deve corresponder
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.TestTimeout.Infinite">
            <summary>
            O infinito.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestClassAttribute">
            <summary>
            O atributo da classe de teste.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestClassAttribute.GetTestMethodAttribute(Microsoft.VisualStudio.TestTools.UnitTesting.TestMethodAttribute)">
            <summary>
            Obtém um atributo de método de teste que habilita a execução desse teste.
            </summary>
            <param name="testMethodAttribute">A instância de atributo do método de teste definida neste método.</param>
            <returns>O <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestMethodAttribute"/> a ser usado para executar esse teste.</returns>
            <remarks>Extensions can override this method to customize how all methods in a class are run.</remarks>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestMethodAttribute">
            <summary>
            O atributo do método de teste.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestMethodAttribute.Execute(Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod)">
            <summary>
            Executa um método de teste.
            </summary>
            <param name="testMethod">O método de teste a ser executado.</param>
            <returns>Uma matriz de objetos TestResult que representam resultados do teste.</returns>
            <remarks>Extensions can override this method to customize running a TestMethod.</remarks>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestInitializeAttribute">
            <summary>
            O atributo de inicialização do teste.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCleanupAttribute">
            <summary>
            O atributo de limpeza do teste.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.IgnoreAttribute">
            <summary>
            O atributo ignorar.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute">
            <summary>
            O atributo de propriedade de teste.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute.#ctor(System.String,System.String)">
            <summary>
            Inicializa uma nova instância da classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute"/>.
            </summary>
            <param name="name">
            O nome.
            </param>
            <param name="value">
            O valor.
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute.Name">
            <summary>
            Obtém o nome.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute.Value">
            <summary>
            Obtém o valor.
             </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ClassInitializeAttribute">
            <summary>
            O atributo de inicialização de classe.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ClassCleanupAttribute">
            <summary>
            O atributo de limpeza de classe.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssemblyInitializeAttribute">
            <summary>
            O atributo de inicialização de assembly.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssemblyCleanupAttribute">
            <summary>
            O atributo de limpeza de assembly.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.OwnerAttribute">
            <summary>
            Proprietário do Teste
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.OwnerAttribute.#ctor(System.String)">
            <summary>
             Inicializa uma nova instância da classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.OwnerAttribute"/>.
            </summary>
            <param name="owner">
            O proprietário.
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.OwnerAttribute.Owner">
            <summary>
            Obtém o proprietário.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.PriorityAttribute">
            <summary>
            Atributo de prioridade. Usado para especificar a prioridade de um teste de unidade.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PriorityAttribute.#ctor(System.Int32)">
            <summary>
            Inicializa uma nova instância da classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.PriorityAttribute"/>.
            </summary>
            <param name="priority">
            A prioridade.
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.PriorityAttribute.Priority">
            <summary>
            Obtém a prioridade.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DescriptionAttribute">
            <summary>
            Descrição do teste
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DescriptionAttribute.#ctor(System.String)">
            <summary>
            Inicializa uma nova instância da classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DescriptionAttribute"/> para descrever um teste.
            </summary>
            <param name="description">A descrição.</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DescriptionAttribute.Description">
            <summary>
            Obtém a descrição de um teste.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.CssProjectStructureAttribute">
            <summary>
            URI de Estrutura do Projeto de CSS
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CssProjectStructureAttribute.#ctor(System.String)">
            <summary>
            Inicializa a nova instância da classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.CssProjectStructureAttribute"/> para o URI da Estrutura do Projeto CSS.
            </summary>
            <param name="cssProjectStructure">O URI da Estrutura do Projeto ECSS.</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.CssProjectStructureAttribute.CssProjectStructure">
            <summary>
            Obtém o URI da Estrutura do Projeto CSS.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.CssIterationAttribute">
            <summary>
            URI de Iteração de CSS
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CssIterationAttribute.#ctor(System.String)">
            <summary>
            Inicializa uma nova instância da classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.CssIterationAttribute"/> para o URI de Iteração do CSS.
            </summary>
            <param name="cssIteration">O URI de iteração do CSS.</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.CssIterationAttribute.CssIteration">
            <summary>
            Obtém o URI de Iteração do CSS.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.WorkItemAttribute">
            <summary>
            Atributo WorkItem. Usado para especificar um item de trabalho associado a esse teste.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.WorkItemAttribute.#ctor(System.Int32)">
            <summary>
            Inicializa a nova instância da classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.WorkItemAttribute"/> para o Atributo WorkItem.
            </summary>
            <param name="id">A ID para o item de trabalho.</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.WorkItemAttribute.Id">
            <summary>
            Obtém a ID para o item de trabalho associado.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute">
            <summary>
            Atributo de tempo limite. Usado para especificar o tempo limite de um teste de unidade.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute.#ctor(System.Int32)">
            <summary>
            Inicializa uma nova instância da classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute"/>.
            </summary>
            <param name="timeout">
            O tempo limite.
            </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute.#ctor(Microsoft.VisualStudio.TestTools.UnitTesting.TestTimeout)">
            <summary>
            Inicializa a nova instância da classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute"/> com um tempo limite predefinido
            </summary>
            <param name="timeout">
            O tempo limite
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute.Timeout">
            <summary>
            Obtém o tempo limite.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult">
            <summary>
            O objeto TestResult a ser retornado ao adaptador.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.#ctor">
            <summary>
             Inicializa uma nova instância da classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult"/>.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.DisplayName">
            <summary>
            Obtém ou define o nome de exibição do resultado. Útil ao retornar vários resultados.
            Se for nulo, o nome do Método será usado como o DisplayName.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.Outcome">
            <summary>
            Obtém ou define o resultado da execução de teste.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.TestFailureException">
            <summary>
            Obtém ou define a exceção gerada quando o teste falha.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.LogOutput">
            <summary>
            Obtém ou define a saída da mensagem registrada pelo código de teste.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.LogError">
            <summary>
            Obtém ou define a saída da mensagem registrada pelo código de teste.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.DebugTrace">
            <summary>
            Obtém ou define os rastreamentos de depuração pelo código de teste.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.TestContextMessages">
            <summary>
            Gets or sets the debug traces by test code.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.Duration">
            <summary>
            Obtém ou define a duração de execução do teste.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.DatarowIndex">
            <summary>
            Obtém ou define o índice de linha de dados na fonte de dados. Defina somente para os resultados de execuções
            individuais de um teste controlado por dados.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.ReturnValue">
            <summary>
            Obtém ou define o valor retornado do método de teste. (Sempre nulo no momento).
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.ResultFiles">
            <summary>
            Obtém ou define os arquivos de resultado anexados pelo teste.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute">
            <summary>
            Especifica a cadeia de conexão, o nome de tabela e o método de acesso de linha para teste controlado por dados.
            </summary>
            <example>
            [DataSource("Provider=SQLOLEDB.1;Data Source=source;Integrated Security=SSPI;Initial Catalog=EqtCoverage;Persist Security Info=False", "MyTable")]
            [DataSource("dataSourceNameFromConfigFile")]
            </example>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.DefaultProviderName">
            <summary>
            O nome do provedor padrão para a DataSource.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.DefaultDataAccessMethod">
            <summary>
            O método de acesso a dados padrão.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.#ctor(System.String,System.String,System.String,Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod)">
            <summary>
            Inicializa a nova instância da classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute"/>. Essa instância será inicializada com um provedor de dados, uma cadeia de conexão, uma tabela de dados e um método de acesso a dados para acessar a fonte de dados.
            </summary>
            <param name="providerInvariantName">Nome do provedor de dados invariável, como System.Data.SqlClient</param>
            <param name="connectionString">
            Cadeia de conexão específica do provedor de dados. 
            AVISO: a cadeia de conexão pode conter dados confidenciais (por exemplo, uma senha).
            A cadeia de conexão é armazenada em texto sem formatação no código-fonte e no assembly compilado. 
            Restrinja o acesso ao código-fonte e ao assembly para proteger essas formações confidenciais.
            </param>
            <param name="tableName">O nome da tabela de dados.</param>
            <param name="dataAccessMethod">Especifica a ordem para acessar os dados.</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.#ctor(System.String,System.String)">
            <summary>
            Inicializa a nova instância da classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute"/>. Essa instância será inicializada com uma cadeia de conexão e um nome da tabela.
            Especifique a cadeia de conexão e a tabela de dados para acessar a fonte de dados OLEDB.
            </summary>
            <param name="connectionString">
            Cadeia de conexão específica do provedor de dados. 
            AVISO: a cadeia de conexão pode conter dados confidenciais (por exemplo, uma senha).
            A cadeia de conexão é armazenada em texto sem formatação no código-fonte e no assembly compilado. 
            Restrinja o acesso ao código-fonte e ao assembly para proteger essas formações confidenciais.
            </param>
            <param name="tableName">O nome da tabela de dados.</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.#ctor(System.String)">
            <summary>
            Inicializa a nova instância da classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute"/>.  Essa instância será inicializada com um provedor de dados e com uma cadeia de conexão associada ao nome da configuração.
            </summary>
            <param name="dataSourceSettingName">O nome da fonte de dados encontrada na seção &lt;microsoft.visualstudio.qualitytools&gt; do arquivo app.config.</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.ProviderInvariantName">
            <summary>
            Obtém o valor que representa o provedor de dados da fonte de dados.
            </summary>
            <returns>
            O nome do provedor de dados. Se um provedor de dados não foi designado na inicialização do objeto, o provedor de dados padrão de System.Data.OleDb será retornado.
            </returns>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.ConnectionString">
            <summary>
            Obtém o valor que representa a cadeia de conexão da fonte de dados.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.TableName">
            <summary>
            Obtém um valor que indica o nome da tabela que fornece dados.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.DataAccessMethod">
             <summary>
             Obtém o método usado para acessar a fonte de dados.
             </summary>
            
             <returns>
             Um dos <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod"/> valores. Se o <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute"/> não for inicializado, o valor padrão será retornado <see cref="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod.Random"/>.
             </returns>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.DataSourceSettingName">
            <summary>
            Obtém o nome da fonte de dados encontrada na seção &lt;microsoft.visualstudio.qualitytools&gt; no arquivo app.config.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataTestMethodAttribute">
            <summary>
            O atributo para teste controlado por dados em que os dados podem ser especificados de maneira embutida.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataTestMethodAttribute.Execute(Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod)">
            <summary>
            Encontrar todas as linhas de dados e executar.
            </summary>
            <param name="testMethod">
            O Método de teste.
            </param>
            <returns>
            Uma matriz de <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult"/>.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataTestMethodAttribute.RunDataDrivenTest(Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod,Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute[])">
            <summary>
            Executa o método de teste controlado por dados.
            </summary>
            <param name="testMethod"> O método de teste a ser executado. </param>
            <param name="dataRows"> Linha de Dados. </param>
            <returns> Resultados de execução. </returns>
        </member>
    </members>
</doc>
