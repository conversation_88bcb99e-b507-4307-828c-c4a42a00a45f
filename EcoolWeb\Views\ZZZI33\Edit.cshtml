﻿@model ZZZI33EditViewModel
@using EcoolWeb.Util;
@using ECOOL_APP;
@{
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    ViewBag.Permission = com.ecool.service.PermissionService.GetActionPermissionForBreNO(EcoolWeb.Models.UserProfileHelper.GetBRE_NO(), user?.SCHOOL_NO, user?.USER_NO)?.Where(a => a.BoolUse)?.ToList();

    ViewBag.ShowBtn = Model.Title.QUESTIONNAIRE_ID == null ? true : Model.Title.STATUS != SAQT01.STATUSVal.Disabled ? true : false;
}

<link href="~/Content/colorbox/example2/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>
<script src="~/Content/ckeditor4_5_11/ckeditor.js"></script>

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

<button class="btn btn-sm btn-sys" type="button" onclick="onBack()">回投票系統列表</button>
<button class="btn btn-sm btn-sys" type="button" onclick="onExcelIndex()">Excel匯入</button>

<div class="table-92Per">
    <label class="label_dt">PS.</label>
    <br />
    <label class="label_dt">投票發佈後，請勿異動明細題數，以免影響已投票權益。</label>
    <br />
    <label class="label_dt">系統提供【提早結案】功能，來關閉活動。</label>
    <br />
    <i class="glyphicon glyphicon-edit fa-1x"></i><label class="label_dt">  此圖示代表可使用編輯器，點選此圖示就能進入編輯器模式，再點選一次可返回原模式。</label>
    <br />
    <label class="label_dt">簡答題儘量不要超過2題，以免影響頁面呈現。</label>
</div>
<br />

@using (Html.BeginForm("Edit", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "formEdit", name = "formEdit", enctype = "multipart/form-data", ISTASKLIST = ViewBag.ISTASKLIST }))
{
    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })

    @Html.HiddenFor(m => m.Search.OrdercColumn)
    @Html.HiddenFor(m => m.Search.SyntaxName)
    @Html.HiddenFor(m => m.Search.Page)
    @Html.HiddenFor(m => m.Search.whereQUESTIONNAIRE_ID)
    @Html.HiddenFor(m => m.Search.whereSearch)
    @Html.HiddenFor(m => m.Search.BackAction)
    @Html.HiddenFor(m => m.Search.BackController)
    @Html.HiddenFor(m => m.Search.whereIndex)
    @Html.HiddenFor(m => m.Title.QUESTIONNAIRE_ID)
    @Html.HiddenFor(m => m.Title.STATUS)
    @Html.HiddenFor(m => m.SaveType)
    @Html.Hidden("ShowBtn", (bool)ViewBag.ShowBtn)
    @Html.HiddenFor(model => model.REF_KEY)


    @*<button class="btn btn-xs btn-pink   " type="button" onclick="" );" title="全部(不含隱藏和作廢)">全部(不含隱藏和作廢)</button>
    <button class="btn btn-xs btn-pink   " type="button" onclick="">已作廢(含隱藏)</button>*@
    <img src="~/Content/img/web-bar-vote.png" class="img-responsive" alt="Responsive image" />
    <div class="Div-EZ-reader">

        <div class="form-horizontal">
            <div style="height:15px"></div>
            <div class="Caption_Div">
                @ViewBag.Panel_Title
            </div>
            <div style="height:15px"></div>
            <div class="form-group">
                @Html.LabelFor(m => m.Title.QUESTIONNAIRE_NAME, htmlAttributes: new { @class = "col-md-3 control-label" })
                <div class="col-md-9">
                    @Html.EditorFor(m => m.Title.QUESTIONNAIRE_NAME, new { htmlAttributes = new { @class = "form-control input-md", @placeholder = Html.DisplayNameFor(n => n.Title.QUESTIONNAIRE_NAME) } })
                    @Html.ValidationMessageFor(m => m.Title.QUESTIONNAIRE_NAME, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(m => m.Title.QUESTIONNAIRE_DESC, htmlAttributes: new { @class = "col-md-3 control-label" })
                <div class="col-md-9">

                    @Html.TextAreaFor(m => m.Title.QUESTIONNAIRE_DESC, 1, 100, new { @class = "form-control", @placeholder = Html.DisplayNameFor(n => n.Title.QUESTIONNAIRE_DESC) })

                    @Html.ValidationMessageFor(m => m.Title.QUESTIONNAIRE_DESC, "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="form-group">
                @Html.LabelFor(m => m.Title.QUESTIONNAIRE_SDATE, htmlAttributes: new { @class = "col-md-3 control-label" })
                <div class="col-md-9">
                    @Html.EditorFor(m => m.Title.QUESTIONNAIRE_SDATE, new { htmlAttributes = new { @class = "form-control input-md", @type = "text", @placeholder = "格式yyyy/MM/dd" } })
                    @Html.ValidationMessageFor(m => m.Title.QUESTIONNAIRE_SDATE, "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="form-group">
                @Html.LabelFor(m => m.Title.QUESTIONNAIRE_EDATE, htmlAttributes: new { @class = "col-md-3 control-label" })
                <div class="col-md-9">
                    @Html.EditorFor(m => m.Title.QUESTIONNAIRE_EDATE, new { htmlAttributes = new { @class = "form-control input-md", @type = "text", @placeholder = "格式yyyy/MM/dd" } })
                    @Html.ValidationMessageFor(m => m.Title.QUESTIONNAIRE_EDATE, "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="form-group">
                @Html.LabelFor(m => m.Title.CASH, htmlAttributes: new { @class = "col-md-3 control-label" })
                <div class="col-md-9">
                    @Html.EditorFor(m => m.Title.CASH, new { htmlAttributes = new { @class = "form-control input-md", @placeholder = Html.DisplayNameFor(n => n.Title.CASH) } })
                    @Html.ValidationMessageFor(m => m.Title.CASH, "", new { @class = "text-danger" })
                    <br />
                    <label class="text-info">3.	PS:回答可以獲得的點數，請輸入0-5點</label>
                </div>
            </div>
            <div class="form-group">
                @Html.LabelFor(model => model.Title.RESULT, htmlAttributes: new { @class = "control-label col-md-3" })
                <div class="col-md-9">
                    @*<label class='checkbox-inline'>
            @Html.CheckBoxFor(m => m.Title.RESULT)
            <label>是</label>
        </label>

        @Html.ValidationMessageFor(model => model.Title.RESULT, "", new { @class = "text-danger" })
        <br />
        <label class="text-info">PS.投票結束後，可以看投票結果</label>*@
                    @Html.EditorFor(m => m.Title.RESULT_PERSON,
                               "_RadioButtonList",
                               new
                               {
                                   TagName = (Html.NameFor(m => m.Title.RESULT_PERSON)).ToHtmlString(),
                                   RadioItems = SAQT01.Resault_TYPE_VAL.SelectItem(Model.Title.RESULT_PERSON != null ? Model.Title.RESULT_PERSON : "A"),
                                   Position = Position.Horizontal,
                                   Numbers = int.MaxValue,
                                   //onclick = "OnclickWorkType(this.value)",
                               })
                    @Html.ValidationMessageFor(m => m.Title.RESULT_PERSON, "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="form-group">
                @Html.LabelFor(model => model.Title.ANSWER_COUNT, htmlAttributes: new { @class = "control-label col-md-3" })
                <div class="col-md-9">



                    @Html.EditorFor(model => model.Title.ANSWER_COUNT, "", new { htmlAttributes = new { @class = "form-control", @placeholder = "空白為只能投票一次", @onchange = "checkANswerNum();" } })
                    @Html.ValidationMessageFor(model => model.Title.ANSWER_COUNT, "", new { @class = "text-danger" })
                    <br />
                    <label class="text-info">PS.空白為只能投票一次</label>
                </div>
            </div>
            <div class="form-group" >
                @Html.LabelFor(model => model.Title.REGISTERED_BALLOT, htmlAttributes: new { @class = "control-label col-md-3" })
                <div class="col-md-9">
                    <label class='checkbox-inline'>
                        @Html.CheckBoxFor(m => m.Title.REGISTERED_BALLOT)
                        <label>是</label>
                    </label>
                    @Html.ValidationMessageFor(model => model.Title.REGISTERED_BALLOT, "", new { @class = "text-danger" })
                    <br />
                    <label class="text-info">PS.填選不記名，姓名一樣會記錄在資料庫，但從網頁上看不到</label>
                </div>
            </div>
            <div class="form-group">
                @Html.LabelFor(model => model.Title.ANSWER_PERSON_YN, htmlAttributes: new { @class = "control-label col-md-3" })
                <div class="col-md-9">
                    @Html.EditorFor(model => model.Title.ANSWER_PERSON_YN,
                            "_CheckBoxList",
                            new
                            {
                                TagName = (Html.NameFor(m => m.Title.ANSWER_PERSON_YN)).ToHtmlString(),
                                CheckBoxItems = HtnlHelperService.Y_SelectItem(Model.Title != null ? Model.Title.ANSWER_PERSON_YN : ""),
                                Position = Position.Horizontal,
                                Numbers = int.MaxValue,
                                onclick = "",
                            })
                    @Html.ValidationMessageFor(model => model.Title.ANSWER_PERSON_YN, "", new { @class = "text-danger" })
                    <br>
                    <button type="button" class="btn btn-default btn-sm" title="選取對象" id="BTN_ANSWER_PERSON" href="@Url.Action("Index", "REFT01", new { BTN_ID = "#BTN_ANSWER_PERSON",REF_TABLE = "SAQT01" ,REF_KEY= Model.REF_KEY,REF_KEY_ID="#REF_KEY" })">選取對象</button>
                </div>
            </div>
        </div>
    </div>

    <div style="margin-top:20px;margin-bottom:30px;text-align:center">
        <span class="glyphicon glyphicon-circle-arrow-down fa-2x" aria-hidden="true" style="color:#004da0"></span>
    </div>

    <div class="Div-EZ-reader">
        @Html.ValidationMessage("detail", "", new { @class = "text-danger" })
        <div class="form-horizontal">
            <div class="Caption_Div">
                投票明細內容
            </div>
            <div id="editorRows" class="col-md-12">

                @if (Model.Topic != null)
                {
                    foreach (var item in Model.Topic)
                    {
                        item.ShowBtn = ViewBag.ShowBtn;
                        {

                            Html.RenderPartial("_detail", item);
                        }

                    }
                }
            </div>
            <div style="height:25px"></div>
            <div class="row">
                <div class="col-md-12 col-xs-12 text-right">
                    <span class="input-group-btn">
                        @if (ViewBag.ShowBtn)
                        {
                            <button class="btn btn-default btn-sm" type="button" onclick="onAddItem('@SAQT02.Q_TYPEVal.IsInput')">增加問題</button>
                        }
                    </span>
                </div>
            </div>
        </div>
    </div>

    <div style="height:25px"></div>
    <div class="text-center">

        @if (Model.Title.QUESTIONNAIRE_EDATE > DateTime.Now || Model.Title.QUESTIONNAIRE_EDATE == null)
        {
            <button class="btn btn-default" type="button" onclick="onSave()" id="BtnSave">儲存</button>
        }

        @if (Model.Title.STATUS == SAQT01.STATUSVal.NotStarted)
        {
            <a role="button" class="btn btn-default" onclick="onUpdateStatus('@ZZZI33EditViewModel.SaveTypeVal.Release')">發佈</a>
            <a role="button" class="btn btn-default" onclick="onUpdateStatus('@ZZZI33EditViewModel.SaveTypeVal.Disabled')">作廢</a>

        }
        else
        {

            if (Model.Title.STATUS != null)
            {

                var Permission = ViewBag.Permission as List<ControllerPermissionfile>;
                if (Model.Title.STATUS != SAQT01.STATUSVal.Disabled && Model.Title.STATUS != SAQT01.STATUSVal.NotShow)
                {
                    if (Model.Title.STATUS == SAQT01.STATUSVal.NotStarted)
                    {
                        <a role="button" class="btn btn-default" onclick="onUpdateStatus('@ZZZI33EditViewModel.SaveTypeVal.Release')">發佈</a>


                    }
                    else
                    {
                        if (Model.Title.QUESTIONNAIRE_EDATE <= DateTime.Now && Model.Title.QUESTIONNAIRE_EDATE != null) {
                        <a role="button" class="btn btn-default" onclick="onSave()">儲存</a>}
                    }

                }
                if (Permission?.Where(a => a.ActionName == "Del").Any() ?? false)
                {

                    <a role="button" class="btn btn-default" onclick="onUpdateStatus('@ZZZI33EditViewModel.SaveTypeVal.Disabled')">作廢</a>
                }
                else
                {
                    <a role="button" class="btn btn-default" onclick="onUpdateStatus('@ZZZI33EditViewModel.SaveTypeVal.NotShow')">作廢(隱藏)</a>
                }
            }
        }

        @if (Model.Title.STATUS == SAQT01.STATUSVal.StartedOut && Model.Title.QUESTIONNAIRE_EDATE > DateTime.Now)
        {
            <a role="button" class="btn btn-default" onclick="onUpdateStatus('@ZZZI33EditViewModel.SaveTypeVal.End')">提早結束</a>
        }

        @if (Model.Title.STATUS != SAQT01.STATUSVal.Disabled && string.IsNullOrWhiteSpace(Model.Title.QUESTIONNAIRE_ID) == false && Model.Title.QUESTIONNAIRE_EDATE > DateTime.Now)
        {
            <a role="button" class="btn btn-default" onclick="lightbox()">預覽作答(需存檔後才能看到最新內容)</a>
        }

        <button class="btn btn-default" type="button" onclick="onBack()">放棄</button>
    </div>

    <div id="CkeditorModal" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">

                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="modal-title"></h4>
                </div>
                <div class="modal-body">
                    @Html.TextArea("ckeditorText", null, 15, 100, new { @style = "z-index: 10055 !important;" })
                    @Html.Hidden("SOU_ID")
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">確定</button>
                </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div><!-- /.modal -->
}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/3.1.4/css/bootstrap-datetimepicker.min.css" />
<script src="~/Scripts/moment.min.js"></script>
@*<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.13.0/moment.min.js"></script>*@
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/3.1.4/js/bootstrap-datetimepicker.min.js"></script>
@section scripts{
    <script type="text/javascript">

        var targetFormID = '#formEdit';

        $("#BTN_ANSWER_PERSON").colorbox({ iframe: true, width: "80%", height: "80%", opacity: 0.82 });

        $.fn.modal.Constructor.prototype.enforceFocus = function () {
            modal_this = this
            $(document).on('focusin.modal', function (e) {
                if (modal_this.$element[0] !== e.target && !modal_this.$element.has(e.target).length
                    // add whatever conditions you need here:
                    &&
                    !$(e.target.parentNode).hasClass('cke_dialog_ui_input_select') && !$(e.target.parentNode).hasClass('cke_dialog_ui_input_text')) {
                    modal_this.$element.focus()
                }
           })
        };

        function lightbox() {
            var OpenUrl = "@Url.Action("Details", (string)ViewBag.BRE_NO)" + "?Search.whereQUESTIONNAIRE_ID=" + $("#@Html.IdFor(m=>m.Search.whereQUESTIONNAIRE_ID)").val()
                $.colorbox({ width: "80%", height: "80%", iframe: true, href: OpenUrl  });
         }

        ///增加問題
        function onAddItem(Q_TYPE) {

            var data = {
                "Q_TYPE": Q_TYPE,
            };

            $.ajax({
                url: '@Url.Action("_detail")',
                data: data,
                cache: false,
                success: function (html) {
                    $("#editorRows").append(html);
                }
            });
        }
        function checkANswerNum() {
            var num = 0;
            num = $("#Title_ANSWER_COUNT").val();
            //if (num > 2)
            //{
            //    alert("最多可設定回答兩次");
            //}
        }

        //del問題
        function onDelItem(index)
        {
            $('#' + index).remove();
        }

        //copy問題
        function onCopyItem(index) {

            for (instance in CKEDITOR.instances) {
                CKEDITOR.instances[instance].updateElement();
            }

            $('#@Html.IdFor(m => m.Search.whereIndex)').val(index)

             $.ajax({
                 url: '@Url.Action("_detailCopy")',
                 data: $(targetFormID).serialize(),
               cache: false,
               type: 'POST',
                success: function (html) {
                  $("#editorRows").append(html);
               }
             });
        }

        ///下拉回答類別
        function onSelectInput(index, val, text) {
            $("#UPworld" + index).attr("style", "display:none");
            $("#UPIMG" + index).attr("style", "display:none");
            $("#UPPdf" + index).attr("style", "display:none");
            $("#text" + index).attr("style", "display:none");
            var Ipunt_ID = '#Ipunt_' + index
            var Btn_ID = '#Btn_' + index
            var Q_MUTIPLE_CHOICES_OF_NUM = '#Q_MUTIPLE_CHOICES_OF_NUM' + index

            $(Btn_ID).hide();
            $(Q_MUTIPLE_CHOICES_OF_NUM).hide();
            $("#Topic_" + index + "__Q_MUTIPLE_CHOICES_OF_NUM").val('');

            if (val == '@SAQT03.Q_INPUT_TYPEVal.radio' || val == '@SAQT03.Q_INPUT_TYPEVal.checkbox' || val == '@SAQT03.Q_INPUT_TYPEVal.select') {
                $(Btn_ID).show();

                if (val == '@SAQT03.Q_INPUT_TYPEVal.checkbox' ) {
                    $(Q_MUTIPLE_CHOICES_OF_NUM).show();
                }
            }
            if (val == '@SAQT03.Q_INPUT_TYPEVal.UploadWorld') {
               
                console.log("#UPworld" + index);
                $("#UPworld" + index).attr("style", "");

            }
             if (val == '@SAQT03.Q_INPUT_TYPEVal.UploadImage') {
               
                 console.log("#UPIMG" + index);
                 $("#UPIMG" + index).attr("style", "");

            }
              if (val == '@SAQT03.Q_INPUT_TYPEVal.UploadPdf') {
               
                  console.log("#UPPdf" + index);
                  $("#UPPdf" + index).attr("style", "");

            }
            if (val == '@SAQT03.Q_INPUT_TYPEVal.text') {
                $("#text" + index).attr("style", "");
                $("#Topic_" + index + "__MUST").prop('checked', false);
            }
            else {
                $("#Topic_" + index + "__MUST").prop('checked', true);
            }

            $(Ipunt_ID).html('');
            onAddInput(val, index);
        }

        //增加項目 Input
        function onAddInput(Q_INPUT_TYPE, index) {
            var data = {
                "index": index,
                "Q_INPUT_TYPE": Q_INPUT_TYPE,
            };

            var Ipunt_ID = '#Ipunt_' + index

            $.ajax({
                url: '@Url.Action("_Input")',
                data: data,
                cache: false,
                success: function (html) {
                    $(Ipunt_ID).append(html);
                }
            });
        }

        //增加項目
        function BtnOnAddInput(index) {
            var Q_TYPE = $("#Topic_" + index + "__INPUT_TYPE").val()
            onAddInput(Q_TYPE, index)
        }

        function setMustForKey(index, checked)
        {
            if (checked) {
                if ($("#Topic_" + index + "__MUST").prop("checked") ==false) {
                    $("#Topic_" + index + "__MUST").click();
                }
            }
        }

        //del 項目
        function deleteRow(Id) {
            $('#' + Id).remove();
        }

        function onSave() {

            $('#BtnSave').attr('disabled', 'disabled').html("Loading...資料驗証中…請勿其他動作");
            $(targetFormID).attr("action", "@Url.Action("EditSave", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();

        }

        function onUpdateStatus(SaveType)
        {
            if (SaveType=='@ZZZI33EditViewModel.SaveTypeVal.Disabled') {
                var YN = confirm("你確定要作廢?")

                 if (YN) {
                    $('#@Html.IdFor(m=>m.SaveType)').val(SaveType);
                    $(targetFormID).attr("action", "@Url.Action("UpdateStatus", (string)ViewBag.BRE_NO)")
                    $(targetFormID).submit();
                }
            }
            else if (SaveType == '@ZZZI33EditViewModel.SaveTypeVal.NotShow') {

                 var YN = confirm("你確定要作廢?")

                 if (YN) {
                    $('#@Html.IdFor(m=>m.SaveType)').val(SaveType);
                    $(targetFormID).attr("action", "@Url.Action("UpdateStatus", (string)ViewBag.BRE_NO)")
                    $(targetFormID).submit();
                }
            }
            else {
                $('#@Html.IdFor(m=>m.SaveType)').val(SaveType);
                $(targetFormID).attr("action", "@Url.Action("UpdateStatus", (string)ViewBag.BRE_NO)")
                $(targetFormID).submit();
            }
        }

        function onExcelIndex()
        {
              $(targetFormID).attr("action", "@Url.Action("ExcelIndex", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        function onBack() {
            $(targetFormID).attr("action", "@Url.Action("Index", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        function onInckeditorText(IdText)
        {
            var editor = CKEDITOR.instances[IdText];
            if (editor) {
                if (editor) { editor.destroy(true); }
            }
            else {
                CKEDITOR.replace('' + IdText + '', {
                toolbar: 'Image',
                filebrowserImageBrowseUrl: '@Url.Content("~/CkEdit/ImageBrowseUrl")',
                filebrowserAudioBrowseUrl: '@Url.Content("~/CkEdit/AudioBrowseUrl")',
                filebrowserVideoBrowseUrl: '@Url.Content("~/CkEdit/VedioBrowseUrl")',
                });

            }
        }

        function onInCkeditorModal(IdText) {

            $('#SOU_ID').val(IdText)
            $('#modal-title').text($("label[for='" + IdText + "']").text());
            $('#CkeditorModal').modal('show');
            $('#ckeditorText').val($('#' + IdText).val())

            CKEDITOR.replace('ckeditorText', {
                toolbar: 'Image',
                filebrowserImageBrowseUrl: '@Url.Content("~/CkEdit/ImageBrowseUrl")',
                filebrowserAudioBrowseUrl: '@Url.Content("~/CkEdit/AudioBrowseUrl")',
                filebrowserVideoBrowseUrl: '@Url.Content("~/CkEdit/VedioBrowseUrl")',
                });

            $('#CkeditorModal').on('shown.bs.modal', function () {
                var editor = CKEDITOR.instances['ckeditorText'];
                setTimeout(editor.focus(), 1000)
            })
        }

        $('#CkeditorModal').on('hidden.bs.modal', function () {
              var IdText = $('#SOU_ID').val()
              $('#' + IdText).val(CKEDITOR.instances['ckeditorText'].getData())
              var editor = CKEDITOR.instances['ckeditorText'];
              if (editor) {
                  if (editor) { editor.destroy(true); }
              }
        });

        $("#@Html.IdFor(m => m.Title.QUESTIONNAIRE_SDATE),#@Html.IdFor(m => m.Title.QUESTIONNAIRE_EDATE)").datetimepicker({
            showSecond: true,
            format: 'YYYY/MM/DD HH:mm',
            changeMonth: true,
            changeYear: true,
            timeFormat: 'hh:mm',
            stepHour: 2,
            stepMinute: 10,
            stepSecond: 10,
            showOn: "button",
            buttonImage: "../Content/img/icon/calendar.gif",
            buttonImageOnly: true,
        });

        var Today = new Date();

        if ($("#@Html.IdFor(m => m.Title.QUESTIONNAIRE_SDATE)").val() == "") {
            $("#@Html.IdFor(m => m.Title.QUESTIONNAIRE_SDATE)").datepicker("setDate", Today);
        }

        if ($("#@Html.IdFor(m => m.Title.QUESTIONNAIRE_EDATE)").val() == "") {
            Today.setMonth(Today.getMonth() + 1);
            $("#@Html.IdFor(m => m.Title.QUESTIONNAIRE_EDATE)").datepicker('setDate', Today);
        }


        CKEDITOR.replace('@Html.IdFor(m => m.Title.QUESTIONNAIRE_DESC)'
                , {
                    toolbar: 'Image',
                    filebrowserImageBrowseUrl: '@Url.Content("~/CkEdit/ImageBrowseUrl")',
                    filebrowserAudioBrowseUrl: '@Url.Content("~/CkEdit/AudioBrowseUrl")',
                    filebrowserVideoBrowseUrl: '@Url.Content("~/CkEdit/VedioBrowseUrl")',
                }
         );
    </script>
}