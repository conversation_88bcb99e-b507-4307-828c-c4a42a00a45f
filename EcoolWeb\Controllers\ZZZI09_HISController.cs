﻿using com.ecool.service;
using ECOOL_APP;
using ECOOL_APP.com.ecool.Models.DTO;
using ECOOL_APP.EF;
using EcoolWeb.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using ECOOL_APP.com.ecool.service;

namespace EcoolWeb.Controllers
{
    /// <summary>
    /// 轉/離校生學習成果匯出
    /// </summary>
    public class ZZZI09_HISController : Controller
    {
        private ECOOL_APP.EF.ECOOL_DEVEntities db = new ECOOL_APP.EF.ECOOL_DEVEntities();
        private string schoolNO = UserProfileHelper.GetSchoolNo();
        private UserProfile user = UserProfileHelper.Get();
        private ADDI13Service Service = new ADDI13Service();

        public ActionResult ExportRunExcel(ZZZI09_HISViewModel model)
        {
            ViewBag.Title = "運動撲滿- 列印班級名單名冊";
            if (model.Where_SCHOOLNO_FORADMIN == null) model.Where_SCHOOLNO_FORADMIN = schoolNO;
            if (model.Where_SYEAR == 0) model.Where_SYEAR = SysHelper.GetNowSYear(DateTime.Now);
            HRMT01 hrm01 = new HRMT01();
            hrm01 = db.HRMT01.Where(a => a.SCHOOL_NO == schoolNO && a.USER_NO == user.USER_NO).FirstOrDefault();
            ViewBag.showSYear = "N";
            if (hrm01.CLASS_NO != null && hrm01.USER_TYPE == "T")
            {
                byte? SYear = db.HRMT01.Where(a => a.SCHOOL_NO == schoolNO && a.CLASS_NO == hrm01.CLASS_NO && a.USER_STATUS == UserStaus.Enabled).Select(x => x.SYEAR).FirstOrDefault();
                SharedSetViewBag("運動撲滿- 列印班級名單名冊", model.Where_SCHOOLNO_FORADMIN, hrm01.CLASS_NO, SYear, null, "AllSchoolItems", "SYearItems", "ClassItems");
            }
            else
            {
                ViewBag.showSYear = "Y";
                SharedSetViewBag("運動撲滿- 列印各班名單", model.Where_SCHOOLNO_FORADMIN, null, null, null, "AllSchoolItems", "SYearItems", "ClassItems");
            }

            // userno dropdown
            var usernoDrop = HRMT01.GetUserNoListData(model.Where_SCHOOLNO_FORADMIN, model.Where_CLASSNO, ref db).ToList();

            if (model.Where_CLASSNO == null) usernoDrop = new List<SelectListItem>();

            usernoDrop.Insert(0, new SelectListItem() { Text = "請選擇", Value = "" });
            model.IDNO = db.HRMT01.Where(a => a.SCHOOL_NO == schoolNO && a.USER_NO == model.Where_USERNO).FirstOrDefault()?.IDNO;
            // school dropdown
            var schoolDrop = db.HRMT01.Where(h => h.IDNO == model.IDNO)
                .Join(db.BDMT01, h => h.SCHOOL_NO, b => b.SCHOOL_NO, (h, b) => new { h.SCHOOL_NO, b.SHORT_NAME, h.USER_STATUS, h.USER_TYPE })
                .Where(h => h.USER_STATUS == UserStaus.Enabled && h.USER_TYPE == UserType.Student)
                .Distinct()
                .Select(s => new SelectListItem()
                {
                    Text = s.SHORT_NAME,
                    Value = s.SCHOOL_NO
                }).ToList();
            if (model.Where_USERNO == null) schoolDrop = new List<SelectListItem>();

            ViewBag.USER_NOItems = usernoDrop;
            ViewBag.SchoolItems = schoolDrop;

            ViewBag.CanChangeSchool = PermissionService.GetPermission_Use_YN("CanChangeSchool", "ZZZI09_HIS", schoolNO, user.USER_NO) == "Y";

            return View(model);
        }

        public ActionResult ExportRunExcel2(ZZZI09_HISViewModel model)
        {
            if (model.Where_SCHOOLNO_FORADMIN == null) model.Where_SCHOOLNO_FORADMIN = schoolNO;
            if (model.Where_SYEAR == 0) model.Where_SYEAR = SysHelper.GetNowSYear(DateTime.Now);
            HRMT01 hrm01 = new HRMT01();
            hrm01 = db.HRMT01.Where(a => a.SCHOOL_NO == schoolNO && a.USER_NO == user.USER_NO).FirstOrDefault();
            ViewBag.showSYear = "N";
            if (hrm01.CLASS_NO != null && hrm01.USER_TYPE == "T")
            {
                byte? SYear = db.HRMT01.Where(a => a.SCHOOL_NO == schoolNO && a.CLASS_NO == hrm01.CLASS_NO && a.USER_STATUS == UserStaus.Enabled).Select(x => x.SYEAR).FirstOrDefault();
                SharedSetViewBag("匯出", model.Where_SCHOOLNO_FORADMIN, hrm01.CLASS_NO, SYear, null, "AllSchoolItems", "SYearItems", "ClassItems");
            }
            else
            {
                ViewBag.showSYear = "Y";
                SharedSetViewBag("匯出", model.Where_SCHOOLNO_FORADMIN, null, null, null, "AllSchoolItems", "SYearItems", "ClassItems");
            }

            // userno dropdown
            var usernoDrop = HRMT01.GetUserNoListData(model.Where_SCHOOLNO_FORADMIN, model.Where_CLASSNO, ref db).ToList();

            if (model.Where_CLASSNO == null) usernoDrop = new List<SelectListItem>();

            usernoDrop.Insert(0, new SelectListItem() { Text = "請選擇", Value = "" });
            model.IDNO = db.HRMT01.Where(a => a.SCHOOL_NO == schoolNO && a.USER_NO == model.Where_USERNO).FirstOrDefault()?.IDNO;
            // school dropdown
            var schoolDrop = db.HRMT01.Where(h => h.IDNO == model.IDNO)
                .Join(db.BDMT01, h => h.SCHOOL_NO, b => b.SCHOOL_NO, (h, b) => new { h.SCHOOL_NO, b.SHORT_NAME, h.USER_STATUS, h.USER_TYPE })
                .Where(h => h.USER_STATUS == UserStaus.Enabled && h.USER_TYPE == UserType.Student)
                .Distinct()
                .Select(s => new SelectListItem()
                {
                    Text = s.SHORT_NAME,
                    Value = s.SCHOOL_NO
                }).ToList();
            if (model.Where_USERNO == null) schoolDrop = new List<SelectListItem>();

            ViewBag.USER_NOItems = usernoDrop;
            ViewBag.SchoolItems = schoolDrop;

            ViewBag.CanChangeSchool = PermissionService.GetPermission_Use_YN("CanChangeSchool", "ZZZI09_HIS", schoolNO, user.USER_NO) == "Y";

            return View(model);
        }

        [HttpPost]
        public ActionResult Export2(ZZZI09_HISViewModel model)
        {
            string Message = string.Empty;
            List<HRMT01> usernoDrop = new List<HRMT01>();
            if (model.Where_CLASSNO == "ALL")
            {
                usernoDrop = db.HRMT01
               .Where(h => h.SCHOOL_NO == user.SCHOOL_NO

               && h.USER_TYPE == UserType.Student
               && h.USER_STATUS == UserStaus.Enabled).OrderBy(h => h.CLASS_NO).ThenBy(x => x.SEAT_NO).ToList();
            }
            else
            {
                usernoDrop = db.HRMT01
                .Where(h => h.SCHOOL_NO == user.SCHOOL_NO
                && h.CLASS_NO == model.Where_CLASSNO
                && h.USER_TYPE == UserType.Student
                && h.USER_STATUS == UserStaus.Enabled).OrderBy(h => h.CLASS_NO).ThenBy(x => x.SEAT_NO).ToList();
            }
            ADDI13IndexViewModel ADDI13model = new ADDI13IndexViewModel();
            ADDI13model.Details = new List<ADDI13EditPeopleViewModel>();
            foreach (var list in usernoDrop)
            {
                ADDI13EditPeopleViewModel ADDI13Edit = new ADDI13EditPeopleViewModel();

                ADDI13Edit.SEAT_NO = list.SEAT_NO;
                ADDI13Edit.NAME = list.NAME;
                ADDI13Edit.CLASS_NO = list.CLASS_NO;
                ADDI13Edit.SCHOOL_NO = list.SCHOOL_NO;
                ADDI13model.Details.Add(ADDI13Edit);
            }
            string FileName = Service.GetTOExcelFileSample(ADDI13model, ref db, ref Message);
            if (!string.IsNullOrWhiteSpace(Message))
            {
                TempData["StatusMessage"] = Message;
                return RedirectToAction("ExportRunExcel");
            }

            if (System.IO.File.Exists(FileName))
            {
                return File(System.IO.File.ReadAllBytes(FileName), "application/vnd.ms-excel", "報到資料.xlsx");//輸出檔案給Client端
            }
            else
            {
                return RedirectToAction(ErrorHelper.ErrorVal.NotFileError, "Error");
            }
        }

        [HttpPost]
        public ActionResult Export(ZZZI09_HISViewModel model)
        {
            string Message = string.Empty;
            List<HRMT01> usernoDrop = new List<HRMT01>();
            if (model.Where_CLASSNO == "ALL")
            {
                usernoDrop = db.HRMT01
               .Where(h => h.SCHOOL_NO == user.SCHOOL_NO

               && h.USER_TYPE == UserType.Student
               && h.USER_STATUS == UserStaus.Enabled).OrderBy(h => h.CLASS_NO).ThenBy(x => x.SEAT_NO).ToList();
            }
            else
            {
                usernoDrop = db.HRMT01
                .Where(h => h.SCHOOL_NO == user.SCHOOL_NO
                && h.CLASS_NO == model.Where_CLASSNO
                && h.USER_TYPE == UserType.Student
                && h.USER_STATUS == UserStaus.Enabled).OrderBy(h => h.CLASS_NO).ThenBy(x => x.SEAT_NO).ToList();
            }
            ADDI13IndexViewModel ADDI13model = new ADDI13IndexViewModel();
            ADDI13model.Details = new List<ADDI13EditPeopleViewModel>();
            foreach (var list in usernoDrop)
            {
                ADDI13EditPeopleViewModel ADDI13Edit = new ADDI13EditPeopleViewModel();

                ADDI13Edit.SEAT_NO = list.SEAT_NO;
                ADDI13Edit.NAME = list.NAME;
                ADDI13Edit.CLASS_NO = list.CLASS_NO;
                ADDI13Edit.SCHOOL_NO = list.SCHOOL_NO;
                ADDI13model.Details.Add(ADDI13Edit);
            }
            string FileName = Service.GetTOExcelFileSample(ADDI13model, ref db, ref Message);
            if (!string.IsNullOrWhiteSpace(Message))
            {
                TempData["StatusMessage"] = Message;
                return RedirectToAction("ExportRunExcel");
            }

            if (System.IO.File.Exists(FileName))
            {
                return File(System.IO.File.ReadAllBytes(FileName), "application/vnd.ms-excel", "報到資料.xlsx");//輸出檔案給Client端
            }
            else
            {
                return RedirectToAction(ErrorHelper.ErrorVal.NotFileError, "Error");
            }
        }
        public ActionResult Index3(ZZZI09_HISViewModel model)
        {
            if (model.Where_SCHOOLNO_FORADMIN == null) model.Where_SCHOOLNO_FORADMIN = schoolNO;
            List<SelectListItem> ExportTypeItems = new List<SelectListItem>();
            ExportTypeItems.Insert(0, new SelectListItem() { Text = "請選擇", Value = "" });
            ExportTypeItems.Insert(1, new SelectListItem() { Text = "畢業", Value = "1" });
            ExportTypeItems.Insert(2, new SelectListItem() { Text = "轉學", Value = "2" });
            if (model.Where_EXPORTTYPE != null && model.Where_EXPORTTYPE != "0")
            {
                SelectListItem ExportType = new SelectListItem();
                ExportType = ExportTypeItems.Where(x => x.Value == model.Where_EXPORTTYPE.ToString()).FirstOrDefault();
                ExportType.Selected = true;
            }
            ViewBag.ExportTypeItems = ExportTypeItems;
            //if (ask.Contains("AllSchoolItems"))
            //{
                // all school dropdown
                var allSchoolDrop = db.BDMT01
                   .Select(s => new SelectListItem()
                   {
                       Text = s.SHORT_NAME,
                       Value = s.SCHOOL_NO
                   }).ToList();

                ViewBag.AllSchoolItems = allSchoolDrop;
           // }
            // SharedSetViewBag("轉/離校生學習成果匯出", model.Where_SCHOOLNO_FORADMIN, null, (byte?)model.Where_SYEAR, model.Where_EXPORTTYPE, "AllSchoolItems", "SYearItems", "ClassItems");
            var usernoDrop = HRMT01.GetUserNoListDataForSchool(model.Where_SCHOOLNO_FORADMIN, model.Where_CLASSNO, "", (byte?)model.Where_SYEAR, ref db).ToList();
            if (model.Where_CLASSNO == null) usernoDrop = new List<SelectListItem>();
            usernoDrop.Insert(0, new SelectListItem() { Text = "請選擇", Value = "" });
            if (!string.IsNullOrWhiteSpace(model.SName) && !string.IsNullOrWhiteSpace(model.Where_SCHOOLNO_FORADMIN)) {

                if (model.Where_EXPORTTYPE == "1") {

                }

                    List <HRMT01> st = db.HRMT01.Where(a => a.SCHOOL_NO == model.Where_SCHOOLNO_FORADMIN && a.NAME.Contains(model.SName.Trim()) && a.USER_TYPE== UserType.Student).ToList();

                if (model.Where_EXPORTTYPE == "1")
                {
                    st = st.Where(x => x.USER_STATUS == UserStaus.Invalid).ToList();
                    model.VotePeople = st;
                }
                else if (model.Where_EXPORTTYPE == "2")
                {
                    List<HRMT01> hRMT01sItmes = new List<HRMT01>();
                    foreach (var Item in st)
                    {
                        List<HRMT01> hRMT01sObjItems = new List<HRMT01>();
                        hRMT01sObjItems = db.HRMT01.Where(x => x.IDNO == Item.IDNO && x.SCHOOL_NO == model.Where_SCHOOLNO_FORADMIN).ToList();
                        hRMT01sItmes.AddRange(hRMT01sObjItems);
                    }
                    model.VotePeople = hRMT01sItmes;
                }
             
             
            }
            ViewBag.CanChangeSchool = PermissionService.GetPermission_Use_YN("CanChangeSchool", "ZZZI09_HIS", schoolNO, user.USER_NO) == "Y";
            return View(model);
        }
        public ActionResult Index2(ZZZI09_HISViewModel model)
        {
            if (model.Where_SCHOOLNO_FORADMIN == null) model.Where_SCHOOLNO_FORADMIN = schoolNO;
            //if (model.Where_SYEAR == 0) model.Where_SYEAR = SysHelper.GetNowSYear(DateTime.Now);
            List<SelectListItem> ExportTypeItems = new List<SelectListItem>();
            ExportTypeItems.Insert(0, new SelectListItem() { Text = "請選擇", Value = "" });
            ExportTypeItems.Insert(1, new SelectListItem() { Text = "畢業", Value = "1" });
            ExportTypeItems.Insert(2, new SelectListItem() { Text = "轉學", Value = "2" });
            if (model.Where_EXPORTTYPE != null && model.Where_EXPORTTYPE != "0")
            {
                SelectListItem ExportType = new SelectListItem();
                ExportType = ExportTypeItems.Where(x => x.Value == model.Where_EXPORTTYPE.ToString()).FirstOrDefault();
                ExportType.Selected = true;
            }
            ViewBag.ExportTypeItems = ExportTypeItems;
            SharedSetViewBag("轉/離校生學習成果匯出", model.Where_SCHOOLNO_FORADMIN, null, (byte?)model.Where_SYEAR, model.Where_EXPORTTYPE, "AllSchoolItems", "SYearItems", "ClassItems");

            // userno dropdown
            var usernoDrop = HRMT01.GetUserNoListDataForSchool(model.Where_SCHOOLNO_FORADMIN, model.Where_CLASSNO, "", (byte?)model.Where_SYEAR, ref db).ToList();
            if (model.Where_CLASSNO == null) usernoDrop = new List<SelectListItem>();
            usernoDrop.Insert(0, new SelectListItem() { Text = "請選擇", Value = "" });

            model.IDNO = db.HRMT01.Where(a => a.SCHOOL_NO == schoolNO && a.USER_NO == model.Where_USERNO).FirstOrDefault()?.IDNO;

            // school dropdown
            var schoolDrop = db.HRMT01.Where(h => h.IDNO == model.IDNO)
                .Join(db.BDMT01, h => h.SCHOOL_NO, b => b.SCHOOL_NO, (h, b) => new { h.SCHOOL_NO, b.SHORT_NAME })
                .Distinct()
                .Select(s => new SelectListItem()
                {
                    Text = s.SHORT_NAME,
                    Value = s.SCHOOL_NO
                }).ToList();
            if (model.Where_USERNO == null) schoolDrop = new List<SelectListItem>();
            ViewBag.IDNO = model.IDNO;
            ViewBag.USER_NOItems = usernoDrop;
            ViewBag.SchoolItems = schoolDrop;
            ViewBag.Where_EXPORTTYPE = model.Where_EXPORTTYPE;
            ViewBag.Where_SYEAR = model.Where_SYEAR;
            ViewBag.CLASS_NO = model.Class_NO;
            ViewBag.USER_NO = model.User_No;
            ViewBag.SCHOOL_NO = model.School_No;
            ViewBag.CanChangeSchool = PermissionService.GetPermission_Use_YN("CanChangeSchool", "ZZZI09_HIS", schoolNO, user.USER_NO) == "Y";

            return View(model);
        }

        public ActionResult Index(ZZZI09_HISViewModel model)
        {
            if (model.Where_SCHOOLNO_FORADMIN == null) model.Where_SCHOOLNO_FORADMIN = schoolNO;
            //if (model.Where_SYEAR == 0) model.Where_SYEAR = SysHelper.GetNowSYear(DateTime.Now);
            List<SelectListItem> ExportTypeItems = new List<SelectListItem>();
            ExportTypeItems.Insert(0, new SelectListItem() { Text = "請選擇", Value = "" });
            ExportTypeItems.Insert(1, new SelectListItem() { Text = "畢業", Value = "1" });
            ExportTypeItems.Insert(2, new SelectListItem() { Text = "轉學", Value = "2" });
            if (model.Where_EXPORTTYPE != null && model.Where_EXPORTTYPE != "0")
            {
                SelectListItem ExportType = new SelectListItem();
                ExportType = ExportTypeItems.Where(x => x.Value == model.Where_EXPORTTYPE.ToString()).FirstOrDefault();
                ExportType.Selected = true;
            }
            ViewBag.ExportTypeItems = ExportTypeItems;
            SharedSetViewBag("畢業生/轉學生學習成果匯出", model.Where_SCHOOLNO_FORADMIN, null, (byte?)model.Where_SYEAR, model.Where_EXPORTTYPE, "AllSchoolItems", "SYearItems", "ClassItems");

            // userno dropdown
            var usernoDrop = HRMT01.GetUserNoListDataForSchool(model.Where_SCHOOLNO_FORADMIN, model.Where_CLASSNO, "", (byte?)model.Where_SYEAR, ref db).ToList();
            if (model.Where_CLASSNO == null) usernoDrop = new List<SelectListItem>();
            usernoDrop.Insert(0, new SelectListItem() { Text = "請選擇", Value = "" });

            model.IDNO = db.HRMT01.Where(a => a.SCHOOL_NO == schoolNO && a.USER_NO == model.Where_USERNO).FirstOrDefault()?.IDNO;
            ViewBag.IDNO = model.IDNO;
            // school dropdown
            var schoolDrop = db.HRMT01.Where(h => h.IDNO == model.IDNO)
                .Join(db.BDMT01, h => h.SCHOOL_NO, b => b.SCHOOL_NO, (h, b) => new { h.SCHOOL_NO, b.SHORT_NAME })
                .Distinct()
                .Select(s => new SelectListItem()
                {
                    Text = s.SHORT_NAME,
                    Value = s.SCHOOL_NO
                }).ToList();
            if (model.Where_USERNO == null) schoolDrop = new List<SelectListItem>();

            ViewBag.USER_NOItems = usernoDrop;
            ViewBag.SchoolItems = schoolDrop;

            ViewBag.CanChangeSchool = PermissionService.GetPermission_Use_YN("CanChangeSchool", "ZZZI09_HIS", schoolNO, user.USER_NO) == "Y";

            return View(model);
        }

        /// <summary>
        /// ViewBag設定
        /// </summary>
        /// <param name="ask">要求Bags</param>
        public void SharedSetViewBag(string title, string schoolno, string classNO, byte? SYEAR, string ExportType, params string[] ask)
        {
            ViewBag.Panel_Title = title;
            if (ask.Contains("AllSchoolItems"))
            {
                // all school dropdown
                var allSchoolDrop = db.BDMT01
                   .Select(s => new SelectListItem()
                   {
                       Text = s.SHORT_NAME,
                       Value = s.SCHOOL_NO
                   }).ToList();

                ViewBag.AllSchoolItems = allSchoolDrop;
            }
            if (ask.Contains("SYearItems"))
            {
                // syear dropdown
                var syearDrop = db.HRMT01.Where(h => h.SYEAR != null && h.SYEAR != 0 && h.SCHOOL_NO == schoolno)
                  .Select(s => s.SYEAR)
                  .Distinct()
                  .OrderBy(o => o)
                  .Select(s => new SelectListItem()
                  {
                      Text = s.ToString() + "年入學(" + (s + 6) + "年畢業)",
                      Value = s.ToString()
                  }).ToList();

                if (ExportType != null && ExportType == "1")
                {
                    syearDrop = db.HRMT01.Where(h => h.SYEAR != null && h.SYEAR != 0 && h.SCHOOL_NO == schoolno && h.CLASS_NO.StartsWith("6"))
                    .Select(s => s.SYEAR)
                    .Distinct()
                    .OrderBy(o => o)
                    .Select(s => new SelectListItem()
                    {
                        Text = s.ToString()+ "年入學("+(s+6)+ "年畢業)",
                        Value = s.ToString()
                    }).ToList();
                }

                syearDrop.Insert(0, new SelectListItem() { Text = "請選擇", Value = "" });
                syearDrop.Insert(0, new SelectListItem() { Text = "全部", Value = "ALL" });
                if (SYEAR != null && SYEAR != 0)
                {
                    SelectListItem syearDropItem = syearDrop.Where(x => x.Value == SYEAR.ToString()).FirstOrDefault();
                    syearDropItem.Selected = true;
                }
                ViewBag.SYearItems = syearDrop;
            }

            if (ask.Contains("ClassItems"))
            {
                var classDrop = HRMT01.GetSYearClassListData(schoolno, SYEAR, ref db)
                           .ToList();

                if (ExportType != null && ExportType == "1")
                {
                    classDrop = HRMT01.GetSYearClassListData(schoolno, SYEAR, ref db).Where(x => x.Value.StartsWith("6"))
                           .ToList();
                }
                if (classNO != null)
                {
                    classDrop = classDrop.Where(x => x.Value == classNO).ToList();
                    classDrop.Insert(0, new SelectListItem() { Text = "請選擇", Value = "" });
                }
                else
                {
                    classDrop.Insert(0, new SelectListItem() { Text = "全部", Value = "ALL" });
                    classDrop.Insert(0, new SelectListItem() { Text = "請選擇", Value = "" });
                }

                // class dropdown

                ViewBag.ClassItems = classDrop;
            }
        }
    }
}