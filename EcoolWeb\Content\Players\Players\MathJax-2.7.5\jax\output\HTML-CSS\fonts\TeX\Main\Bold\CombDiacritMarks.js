/*
 *  /MathJax/jax/output/HTML-CSS/fonts/TeX/Main/Bold/CombDiacritMarks.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["MathJax_Main-bold"],{768:[706,-503,0,-461,-237],769:[706,-503,0,-339,-115],770:[694,-520,0,-449,-127],771:[694,-552,0,-479,-97],772:[607,-540,0,-495,-81],774:[694,-500,0,-473,-103],775:[695,-525,0,-373,-203],776:[695,-535,0,-479,-97],778:[702,-536,0,-415,-161],779:[714,-511,0,-442,-82],780:[660,-515,0,-445,-131],824:[711,210,0,-734,-161]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/Main/Bold/CombDiacritMarks.js");
