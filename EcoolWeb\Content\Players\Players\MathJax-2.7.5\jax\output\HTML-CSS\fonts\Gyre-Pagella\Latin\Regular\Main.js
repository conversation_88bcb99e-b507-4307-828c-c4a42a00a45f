/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Gyre-Pagella/Latin/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.GyrePagellaMathJax_Latin={directory:"Latin/Regular",family:"GyrePagellaMathJax_Latin",testString:"\u00A0\u00A1\u00A2\u00A4\u00A6\u00A9\u00AA\u00AB\u00B6\u00B8\u00BA\u00BB\u00BF\u00C0\u00C1",32:[0,0,250,0,0],160:[0,0,250,0,0],161:[454,245,278,81,197],162:[562,101,500,61,448],164:[531,-96,500,30,470],166:[726,184,606,275,331],169:[668,19,747,31,718],170:[709,-361,333,24,310],171:[428,-71,500,50,450],182:[695,150,628,39,589],184:[10,225,333,96,304],186:[709,-355,333,10,323],187:[428,-71,500,50,450],191:[454,245,444,49,401],192:[888,3,778,15,756],193:[888,3,778,15,756],194:[887,3,778,15,756],195:[872,3,778,15,756],196:[869,3,778,15,756],197:[939,3,778,15,756],198:[692,3,944,-10,908],199:[709,225,709,22,670],200:[888,3,611,22,572],201:[888,3,611,22,572],202:[887,3,611,22,572],203:[869,3,611,22,572],204:[888,3,337,22,315],205:[888,3,337,22,315],206:[887,3,337,0,337],207:[869,3,337,20,319],208:[692,3,774,14,751],209:[872,20,831,17,813],210:[888,20,786,22,764],211:[888,20,786,22,764],212:[887,20,786,22,764],213:[872,20,786,22,764],214:[869,20,786,22,764],216:[709,20,833,30,797],217:[888,20,778,12,759],218:[888,20,778,12,759],219:[887,20,778,12,759],220:[869,20,778,12,759],221:[888,3,667,9,654],222:[692,3,604,32,574],223:[731,9,556,23,519],224:[677,12,500,32,471],225:[677,12,500,32,471],226:[676,12,500,32,471],227:[645,12,500,32,471],228:[642,12,500,32,471],229:[692,12,500,32,471],230:[469,20,758,30,732],231:[469,225,444,26,413],232:[677,20,479,26,448],233:[677,20,479,26,448],234:[676,20,479,26,448],235:[642,20,479,26,448],236:[677,3,287,11,271],237:[677,3,287,21,281],238:[676,3,287,-10,302],239:[642,3,287,-4,295],241:[645,3,582,6,572],242:[677,20,546,32,514],243:[677,20,546,32,514],244:[676,20,546,32,514],245:[645,20,546,32,514],246:[642,20,546,32,514],248:[474,23,556,16,530],249:[677,12,603,18,581],250:[677,12,603,18,581],251:[676,12,603,18,581],252:[642,12,603,18,581],253:[677,283,556,12,544],254:[726,281,601,-2,544],255:[642,283,556,12,544],256:[846,3,778,15,756],257:[619,12,500,32,471],258:[884,3,778,15,756],259:[671,12,500,32,471],260:[700,250,778,15,756],261:[469,250,500,32,471],262:[888,20,709,22,670],263:[677,20,444,26,413],268:[887,20,709,22,670],269:[676,20,444,26,413],270:[887,3,774,22,751],271:[726,12,611,35,678],272:[692,3,774,14,751],273:[726,12,611,35,579],274:[846,3,611,22,572],275:[619,20,479,26,448],278:[869,3,611,22,572],279:[642,20,479,26,448],280:[692,250,611,22,572],281:[469,250,479,26,448],282:[887,3,611,22,572],283:[676,20,479,26,448],286:[884,20,763,22,728],287:[671,283,556,32,544],290:[709,271,763,22,728],291:[709,283,556,32,544],296:[872,3,337,5,335],297:[645,3,287,-19,311],298:[846,3,337,14,326],299:[619,3,287,-10,302],302:[692,250,337,22,315],303:[642,250,291,21,271],304:[869,3,337,22,315],306:[692,195,724,40,684],307:[642,283,509,40,477],310:[692,271,726,22,719],311:[726,271,556,21,549],313:[888,3,611,22,586],314:[922,3,291,21,271],315:[692,271,611,22,586],316:[726,271,291,21,271],317:[692,3,611,22,586],318:[726,3,291,21,370],321:[692,3,611,22,586],322:[726,3,291,0,296],323:[888,20,831,17,813],324:[677,3,582,6,572],325:[692,271,831,17,813],326:[469,271,582,6,572],327:[887,20,831,17,813],328:[676,3,582,6,572],330:[692,187,831,17,813],331:[469,167,582,6,494],332:[846,20,786,22,764],333:[619,20,546,32,514],336:[888,20,786,22,764],337:[683,20,546,32,526],338:[709,20,998,22,962],339:[469,20,827,32,800],340:[888,3,668,22,669],341:[677,3,395,21,374],342:[692,271,668,22,669],343:[469,271,395,21,374],344:[887,3,668,22,669],345:[676,3,395,21,374],346:[888,20,525,24,503],347:[677,20,424,30,391],350:[709,225,525,24,503],351:[469,225,424,30,391],352:[887,20,525,24,503],353:[676,20,424,30,391],354:[692,225,613,18,595],355:[621,225,326,22,319],356:[887,3,613,18,595],357:[663,12,326,22,355],360:[872,20,778,12,759],361:[645,12,603,18,581],362:[846,20,778,12,759],363:[619,12,603,18,581],366:[939,20,778,12,759],367:[692,12,603,18,581],368:[888,20,778,12,759],369:[683,12,603,18,581],370:[692,250,778,12,759],371:[469,250,603,18,581],376:[869,3,667,9,654],377:[888,3,667,15,638],378:[677,3,500,16,466],379:[869,3,667,15,638],380:[642,3,500,16,466],381:[887,3,667,15,638],382:[676,3,500,16,466],383:[728,3,333,23,341],402:[706,262,500,0,473],416:[781,20,786,22,764],417:[532,20,546,32,567],431:[781,20,778,12,801],432:[532,12,640,18,640],536:[709,271,525,24,503],537:[469,271,424,30,391],538:[692,271,613,18,595],539:[621,271,326,22,319],7840:[700,193,778,15,756],7841:[469,193,500,32,471],7842:[946,3,778,15,756],7843:[703,12,500,32,471],7844:[1046,3,778,15,756],7845:[803,12,500,32,471],7846:[1046,3,778,15,756],7847:[803,12,500,32,471],7848:[1088,3,778,15,756],7849:[845,12,500,32,471],7850:[1022,3,778,15,756],7851:[779,12,500,32,471],7852:[887,193,778,15,756],7853:[676,193,500,32,471],7854:[1043,3,778,15,756],7855:[800,12,500,32,471],7856:[1043,3,778,15,756],7857:[800,12,500,32,471],7858:[1045,3,778,15,756],7859:[802,12,500,32,471],7860:[1015,3,778,15,756],7861:[772,12,500,32,471],7862:[884,193,778,15,756],7863:[671,193,500,32,471],7864:[692,193,611,22,572],7865:[469,193,479,26,448],7866:[946,3,611,22,572],7867:[703,20,479,26,448],7868:[872,3,611,22,572],7869:[645,20,479,26,448],7870:[1046,3,611,22,572],7871:[803,20,479,26,448],7872:[1046,3,611,22,572],7873:[803,20,479,26,448],7874:[1088,3,611,22,572],7875:[845,20,479,26,448],7876:[1022,3,611,22,572],7877:[779,20,479,26,448],7878:[887,193,611,22,572],7879:[676,193,479,26,448],7880:[946,3,337,22,315],7881:[703,3,287,21,271],7882:[692,193,337,22,315],7883:[642,193,287,21,271],7884:[709,193,786,22,764],7885:[469,193,546,32,514],7886:[946,20,786,22,764],7887:[703,20,546,32,514],7888:[1046,20,786,22,764],7889:[803,20,546,32,514],7890:[1046,20,786,22,764],7891:[803,20,546,32,514],7892:[1088,20,786,22,764],7893:[845,20,546,32,514],7894:[1022,20,786,22,764],7895:[779,20,546,32,514],7896:[887,193,786,22,764],7897:[676,193,546,32,514],7898:[888,20,786,22,764],7899:[677,20,546,32,567],7900:[888,20,786,22,764],7901:[677,20,546,32,567],7902:[946,20,786,22,764],7903:[703,20,546,32,567],7904:[872,20,786,22,764],7905:[645,20,546,32,567],7906:[781,193,786,22,764],7907:[532,193,546,32,567],7908:[692,193,778,12,759],7909:[469,193,603,18,581],7910:[946,20,778,12,759],7911:[703,12,603,18,581],7912:[888,20,778,12,801],7913:[677,12,640,18,640],7914:[888,20,778,12,801],7915:[677,12,640,18,640],7916:[946,20,778,12,801],7917:[703,12,640,18,640],7918:[872,20,778,12,801],7919:[645,12,640,18,640],7920:[781,193,778,12,801],7921:[532,193,640,18,640],7922:[888,3,667,9,654],7923:[677,283,556,12,544],7924:[705,193,667,9,654],7925:[459,283,556,12,544],7926:[946,3,667,9,654],7927:[703,283,556,12,544],7928:[872,3,667,9,654],7929:[645,283,556,12,544],64256:[728,3,623,23,631],64257:[728,3,605,23,587],64258:[728,3,608,23,590],64259:[728,3,897,23,876],64260:[728,3,900,23,880]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"GyrePagellaMathJax_Latin"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Latin/Regular/Main.js"]);
