﻿@model ADDI13IndexViewModel
@{
    ViewBag.Title = "View";

}



@{
    ViewBag.Title = "點數領取-列表";
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";

    string SCHOOL_NO = EcoolWeb.Models.UserProfileHelper.GetSchoolNo();
    string LogoAct = "GuestIndex";

}

@Html.Partial("_Notice")

@using (Html.BeginForm("Index1", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", id = "form1", @AutoComplete = "Off", @target = "_blank" }))
{
    @Html.AntiForgeryToken()
    @Html.HiddenFor(m => m.ROLL_CALL_ID)
    @Html.HiddenFor(m => m.SCHOOL_NO1)
    <div class="row">
        <div class="col-md-8 col-md-offset-2">
            <div class="panel with-nav-tabs panel-info" id="panel">
                <div class="panel-heading">
                    <h1>點數領取</h1>
                </div>
                <div class="panel-body">
                    <div class="form-group">
                        <div class="input-group input-group-lg">
                            <span class="input-group-addon"><i class="fa fa-user"></i></span>

                            @Html.EditorFor(m => m.BarCode, new { htmlAttributes = new { @class = "form-control", @placeholder = "先感應數位學生證 ", @onKeyPress = "call(event,this);" } })


                            @Html.EditorFor(m => m.CARD_NO, new { htmlAttributes = new { @class = "form-control", @placeholder = "輸入紙張上的10碼", @onKeyPress = "call(event,this);" } })
                        </div>
                        @Html.ValidationMessage("CARD_NO", "", new { @class = "text-danger" })
                    </div>
                    <input type="button" value="送出" onclick="OnclickCardNO()" />
                </div>
            </div>

        </div>
    </div>}