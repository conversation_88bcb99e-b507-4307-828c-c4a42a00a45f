﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'flash', 'en-au', {
	access: 'Script Access',
	accessAlways: 'Always',
	accessNever: 'Never',
	accessSameDomain: 'Same domain',
	alignAbsBottom: 'Abs Bottom',
	alignAbsMiddle: 'Abs Middle',
	alignBaseline: 'Baseline',
	alignTextTop: 'Text Top',
	bgcolor: 'Background colour',
	chkFull: 'Allow Fullscreen',
	chkLoop: 'Loop',
	chkMenu: 'Enable Flash Menu',
	chkPlay: 'Auto Play',
	flashvars: 'Variables for Flash',
	hSpace: 'HSpace',
	properties: 'Flash Properties',
	propertiesTab: 'Properties',
	quality: 'Quality',
	qualityAutoHigh: 'Auto High', // MISSING
	qualityAutoLow: 'Auto Low', // MISSING
	qualityBest: 'Best', // MISSING
	qualityHigh: 'High', // MISSING
	qualityLow: 'Low', // MISSING
	qualityMedium: 'Medium', // MISSING
	scale: 'Scale',
	scaleAll: 'Show all',
	scaleFit: 'Exact Fit',
	scaleNoBorder: 'No Border',
	title: 'Flash Properties',
	vSpace: 'VSpace',
	validateHSpace: 'HSpace must be a number.',
	validateSrc: 'URL must not be empty.',
	validateVSpace: 'VSpace must be a number.',
	windowMode: 'Window mode',
	windowModeOpaque: 'Opaque', // MISSING
	windowModeTransparent: 'Transparent', // MISSING
	windowModeWindow: 'Window' // MISSING
} );
