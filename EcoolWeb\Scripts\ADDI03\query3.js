﻿$(document).ready(function{

    const query3Module = {
        targetFormID="#contentForm",

    };
    query3Module.init();
    window.addEventListener('error', function (e) {

        console.error('頁面錯誤', e);
    });
})

  

    function ShowBOOK_NAME(SCHOOL_NO_Val, USER_NO_Val, GRADE_Val, TWO_BOOK_ID_Val) {
        $.ajax({
            url: "@Url.Action("GetBOOK_NAME", "ADDI03")",
            method: 'post',
            data: {
                SCHOOL_NO: SCHOOL_NO_Val,
                USER_NO: USER_NO_Val,
                GRADE: GRADE_Val,
                TWO_BOOK_ID: TWO_BOOK_ID_Val
            },
            dataType: 'json',
            cache: false,
            success: function (data) {
                var res = jQuery.parseJSON(data);
                var ImgID = "Img" + USER_NO_Val + GRADE_Val + TWO_BOOK_ID_Val;
                $('#' + ImgID).attr("title", res.BOOK_NAME);
            },
            error: function (xhr, err) {
                console.error("AJAX Error:", err);
                alert("系統發生錯誤，請稍後再試");
            }
        });
    }

    function Chg_BOOK_NO(USER_NO_Val,  TWO_BOOK_ID_Val) {
        //event.returnValue = false;
       @*$.colorbox({href: '@Url.Action("Detail", "ADDI01" )' });*@
    @*window.open('@Url.Action("MaintainOneApple", "ADDI03")' + '?USER_NO=' + USER_NO_Val + '&GRADE=' + GRADE_Val + '&TWO_BOOK_ID=' + TWO_BOOK_ID_Val, config='height=600,width=800,toolbar=no');*@

    @{
        bool IsAdmin = false;
    if (user != null)
    {
        string UseYN = (user != null) ? com.ecool.service.PermissionService.GetPermission_Use_YN("ADDI03", "MaintainOneApple", user.SCHOOL_NO, user.USER_NO) : "N";
    IsAdmin = (UseYN == "Y");
           }
    }

    if ('True'=='@IsAdmin') {
        document.contentForm.enctype = "multipart/form-data";
    document.contentForm.action = '@Url.Action("MaintainOneApple", "ADDI03")' + '?USER_NO=' + USER_NO_Val + '&TWO_BOOK_ID=' + TWO_BOOK_ID_Val;
    document.contentForm.submit();
    }

        }
    function ChangeGrade(Val) {
            var GradeInt = "";
    GradeInt = $('#whereGrade').val();
    $('#GRADE').val(GradeInt);
    $('#Page').val(0);
    console.log(GradeInt);
    $('#Class_No').val("");
    $(targetFormID).submit();
    $("#ShowW").html("");
    $("#ShowW").html("<span>您正在查詢 <font style=\"color:red\">" + $('#Class_No').val() + $('#GRADE').val() + "年級共讀書目<font> 完成的情形。</span>");
        }
        function ClickGRADE(Val)
        {
            $('#GRADE').val(Val);
        $('#Page').val(0);
        $(targetFormID).submit();
        $("#ShowW").html("");
        $("#ShowW").html("<span>您正在查詢 <font style=\"color:red\">" + $('#Class_No').val() + $('#GRADE').val() + "年級共讀書目<font> 完成的情形。</span>");
    }
            function ClickPage(Val) {
                $('#Page').val(Val);
            $(targetFormID).submit();
        }
            function PrintBooK()
            {
        var Class_No = $('#Class_No option:selected').val()

            window.open('@Url.Action("PrintBooK", "ADDI03")' + '?GRADE=' + $('#GRADE').val() + '&Class_No=' + Class_No + '&whereKeyword=' + $('#whereKeyword').val() + '', '_blank')
    }

            function ChangeClassNo(Val) {
            var GradeInt ="";
            if ($('#GRADE').val() != undefined && $('#GRADE').val() != "" && $('#GRADE').val() != "ALL") {
                GradeInt = $('#GRADE').val();
            }
            else {
                GradeInt = Val.substring(0, 1);

            }

            var GradeStr = "";
            if (GradeInt == "1") {
                GradeStr = "一";
            }
            else if (GradeInt == "2") {
                GradeStr = "二";

            }
            else if (GradeInt == "3") {

                GradeStr = "三";
            }
            else if (GradeInt == "4") {
                GradeStr = "四";

            }
            else if (GradeInt == "5") {
                GradeStr = "五";

            }
            else if (GradeInt == "6") {

                GradeStr = "六";
            }
            $("#ShowW").html("<span>您正在查詢 <font style=\"color:red\">" + Val + GradeStr+ "年級共讀書目<font> 完成的情形。</span>");

                ClickGRADE(Val.substring(0, 1))
    }

                $(function () {

            var Val = 0;
                Val = $("#Class_No").val();
                var GradeInt = "";
                if ($('#GRADE').val() != undefined && $('#GRADE').val() != "" && $('#GRADE').val()!="ALL") {
                    GradeInt = $('#GRADE').val();
            }
                else {
                    GradeInt = Val.substring(0, 1);

            }


                var GradeStr = "";
                if (GradeInt == "1") {
                    GradeStr = "一";
            }
                else if (GradeInt == "2") {
                    GradeStr = "二";

            }
                else if (GradeInt == "3") {

                    GradeStr = "三";
            }
                else if (GradeInt == "4") {
                    GradeStr = "四";

            }
                else if (GradeInt == "5") {
                    GradeStr = "五";

            }
                else if (GradeInt == "6") {

                    GradeStr = "六";
            }
                $("#ShowW").html("<span>您正在查詢 <font style=\"color:red\">" + Val + GradeStr + "年級共讀書目</font> 完成的情形。</span>");

        })
            function todoClear() {
                ////重設
                $(targetFormID).find(":input,:selected").each(function (i) {

                    var type = $(this).attr('type')
                    var InPreadonly = $(this).attr('readonly')
                    var tag = this.tagName.toLowerCase(); // normalize case

                    if (InPreadonly == false || InPreadonly == undefined) {

                        if (type == 'radio' || type == 'checkbox') {
                            if ($(this).attr("title") == 'Default') {
                                this.checked = true;
                            }
                            else {
                                this.checked = false;
                            }
                        }
                        else if (tag == 'select') { //下拉式選單
                            this.selectedIndex = 0;
                        }
                        else if (type == 'text' || type == 'hidden' || type == 'password' || type == 'textarea') {
                            this.value = '';
                        }
                    }
                });

            $(targetFormID).submit();
    }

