﻿// JavaScript for ADDI03 Query3 page - 閱讀護照現況一覽表
$(document).ready(function() {
    // 護照現況一覽表模組
    const query3Module = {
        targetFormID: '#contentForm',

        init: function() {
            this.bindEvents();
            this.setupGlobalFunctions();
            this.enhanceUserExperience();
            this.initializeDisplay();
        },

        bindEvents: function() {
            // 綁定下拉選單變更事件
            $('#whereGrade').on('change', this.handleGradeChange.bind(this));
            $('#Class_No').on('change', this.handleClassChange.bind(this));

            // 綁定按鈕事件
            $('.btn-pink[id^="btnGRADE"]').on('click', this.handleGradeFilter.bind(this));
            $('input[value="清除搜尋"]').on('click', this.handleClear.bind(this));
            $('button[onclick*="PrintBooK"]').on('click', this.handlePrint.bind(this));

            // 綁定分頁按鈕
            $('a[onclick*="ClickPage"]').on('click', this.handlePageClick.bind(this));

            // 綁定書籍圖示事件
            $('a[id^="Img"]').on('mouseover', this.handleBookHover.bind(this));
            $('a[id^="Img"]').on('click', this.handleBookClick.bind(this));

            // 綁定搜尋表單提交
            $(this.targetFormID).on('submit', this.handleFormSubmit.bind(this));
        },

        setupGlobalFunctions: function() {
            // 設置全局函數以保持向後兼容
            window.ShowBOOK_NAME = this.showBookName.bind(this);
            window.Chg_BOOK_NO = this.changeBookNo.bind(this);
            window.ChangeGrade = this.handleGradeChange.bind(this);
            window.ClickGRADE = this.clickGrade.bind(this);
            window.ClickPage = this.clickPage.bind(this);
            window.PrintBooK = this.printBook.bind(this);
            window.ChangeClassNo = this.changeClassNo.bind(this);
            window.todoClear = this.handleClear.bind(this);
        },

        showBookName: function(schoolNo, userNo, grade, twoBookId) {
            try {
                $.ajax({
                    url: window.ADDI03_QUERY3_URLS.getBookName,
                    method: 'POST',
                    data: {
                        SCHOOL_NO: schoolNo,
                        USER_NO: userNo,
                        GRADE: grade,
                        TWO_BOOK_ID: twoBookId
                    },
                    dataType: 'json',
                    cache: false,
                    timeout: 10000,
                    success: function(data) {
                        try {
                            const res = typeof data === 'string' ? JSON.parse(data) : data;
                            const imgId = "Img" + userNo + grade + twoBookId;
                            $('#' + imgId).attr("title", res.BOOK_NAME);
                        } catch (parseError) {
                            console.error("解析回應資料時發生錯誤:", parseError);
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error("取得書名時發生錯誤:", { xhr, status, error });
                        // 不顯示錯誤訊息給用戶，因為這只是hover效果
                    }
                });
            } catch (error) {
                console.error("顯示書名時發生錯誤:", error);
            }
        },

        changeBookNo: function(userNo, twoBookId) {
            try {
                // 檢查是否有管理員權限
                if (window.ADDI03_QUERY3_CONFIG && window.ADDI03_QUERY3_CONFIG.isAdmin === 'True') {
                    const form = document.contentForm;
                    if (form) {
                        form.enctype = "multipart/form-data";
                        form.action = window.ADDI03_QUERY3_URLS.maintainOneApple + '?USER_NO=' + userNo + '&TWO_BOOK_ID=' + twoBookId;
                        form.submit();
                    } else {
                        console.error('找不到表單元素');
                    }
                } else {
                    console.log('沒有管理員權限，無法修改書號');
                }
            } catch (error) {
                console.error("修改書號時發生錯誤:", error);
                this.showMessage('修改書號時發生錯誤，請稍後再試', 'error');
            }
        },
        handleGradeChange: function(gradeValue) {
            try {
                // 如果是事件對象，獲取值
                if (typeof gradeValue === 'object' && gradeValue.target) {
                    gradeValue = $(gradeValue.target).val();
                } else if (typeof gradeValue === 'undefined') {
                    gradeValue = $('#whereGrade').val();
                }

                console.log('年級變更為:', gradeValue);
                $('#GRADE').val(gradeValue);
                $('#Page').val(0);
                $('#Class_No').val("");

                this.updateDisplayMessage();
                this.submitForm();
            } catch (error) {
                console.error("年級變更時發生錯誤:", error);
                this.showMessage('年級變更時發生錯誤，請稍後再試', 'error');
            }
        },

        clickGrade: function(gradeValue) {
            try {
                console.log('點擊年級按鈕:', gradeValue);
                $('#GRADE').val(gradeValue);
                $('#Page').val(0);

                this.updateDisplayMessage();
                this.submitForm();
            } catch (error) {
                console.error("點擊年級時發生錯誤:", error);
                this.showMessage('年級選擇時發生錯誤，請稍後再試', 'error');
            }
        },

        clickPage: function(pageValue) {
            try {
                console.log('點擊分頁:', pageValue);
                $('#Page').val(pageValue);
                this.submitForm();
            } catch (error) {
                console.error("分頁切換時發生錯誤:", error);
                this.showMessage('分頁切換時發生錯誤，請稍後再試', 'error');
            }
        },
        printBook: function() {
            try {
                const classNo = $('#Class_No option:selected').val();
                const grade = $('#GRADE').val();
                const keyword = $('#whereKeyword').val();

                const url = window.ADDI03_QUERY3_URLS.printBook +
                           '?GRADE=' + encodeURIComponent(grade) +
                           '&Class_No=' + encodeURIComponent(classNo) +
                           '&whereKeyword=' + encodeURIComponent(keyword);

                console.log('開啟列印頁面:', url);
                window.open(url, '_blank');
            } catch (error) {
                console.error("列印時發生錯誤:", error);
                this.showMessage('列印時發生錯誤，請稍後再試', 'error');
            }
        },

        changeClassNo: function(classValue) {
            try {
                // 如果是事件對象，獲取值
                if (typeof classValue === 'object' && classValue.target) {
                    classValue = $(classValue.target).val();
                }

                let gradeInt = "";
                if ($('#GRADE').val() && $('#GRADE').val() !== "" && $('#GRADE').val() !== "ALL") {
                    gradeInt = $('#GRADE').val();
                } else {
                    gradeInt = classValue.substring(0, 1);
                }

                const gradeStr = this.getGradeChineseName(gradeInt);

                $("#ShowW").html(`<span>您正在查詢 <font style="color:red">${classValue}${gradeStr}年級共讀書目</font> 完成的情形。</span>`);

                this.clickGrade(classValue.substring(0, 1));
            } catch (error) {
                console.error("班級變更時發生錯誤:", error);
                this.showMessage('班級變更時發生錯誤，請稍後再試', 'error');
            }
        },

        getGradeChineseName: function(gradeInt) {
            const gradeMap = {
                "1": "一",
                "2": "二",
                "3": "三",
                "4": "四",
                "5": "五",
                "6": "六"
            };
            return gradeMap[gradeInt] || "";
        },

        initializeDisplay: function() {
            try {
                const classValue = $("#Class_No").val();
                if (!classValue) return;

                let gradeInt = "";
                if ($('#GRADE').val() && $('#GRADE').val() !== "" && $('#GRADE').val() !== "ALL") {
                    gradeInt = $('#GRADE').val();
                } else {
                    gradeInt = classValue.substring(0, 1);
                }

                const gradeStr = this.getGradeChineseName(gradeInt);
                $("#ShowW").html(`<span>您正在查詢 <font style="color:red">${classValue}${gradeStr}年級共讀書目</font> 完成的情形。</span>`);
            } catch (error) {
                console.error("初始化顯示時發生錯誤:", error);
            }
        },

        handleClear: function() {
            try {
                console.log('清除搜尋條件');

                // 重設表單元素
                $(this.targetFormID).find(":input,:selected").each(function(i) {
                    const $element = $(this);
                    const type = $element.attr('type');
                    const isReadonly = $element.attr('readonly');
                    const tag = this.tagName.toLowerCase();

                    // 只處理非唯讀元素
                    if (isReadonly !== 'readonly' && isReadonly !== true) {
                        if (type === 'radio' || type === 'checkbox') {
                            if ($element.attr("title") === 'Default') {
                                this.checked = true;
                            } else {
                                this.checked = false;
                            }
                        } else if (tag === 'select') {
                            // 下拉式選單重設為第一個選項
                            this.selectedIndex = 0;
                        } else if (type === 'text' || type === 'hidden' || type === 'password' || type === 'textarea') {
                            this.value = '';
                        }
                    }
                });

                // 清除顯示訊息
                $("#ShowW").html("");

                // 提交表單
                this.submitForm();

                this.showMessage('搜尋條件已清除', 'success');
            } catch (error) {
                console.error('清除搜尋條件時發生錯誤:', error);
                this.showMessage('清除時發生錯誤，請稍後再試', 'error');
            }
        },

        updateDisplayMessage: function() {
            try {
                const classValue = $('#Class_No').val();
                const gradeValue = $('#GRADE').val();

                if (classValue && gradeValue) {
                    const gradeStr = this.getGradeChineseName(gradeValue);
                    $("#ShowW").html(`<span>您正在查詢 <font style="color:red">${classValue}${gradeStr}年級共讀書目</font> 完成的情形。</span>`);
                }
            } catch (error) {
                console.error("更新顯示訊息時發生錯誤:", error);
            }
        },

        submitForm: function() {
            try {
                const $form = $(this.targetFormID);
                $form.submit();
            } catch (error) {
                console.error("提交表單時發生錯誤:", error);
                this.showMessage('提交表單時發生錯誤，請稍後再試', 'error');
            }
        },

        // 事件處理器
        handleGradeFilter: function(event) {
            event.preventDefault();
            const gradeValue = $(event.target).data('grade') || $(event.target).attr('data-grade');
            if (gradeValue) {
                this.clickGrade(gradeValue);
            }
        },

        handleClassChange: function(event) {
            this.changeClassNo($(event.target).val());
        },

        handlePageClick: function(event) {
            event.preventDefault();
            const onclick = $(event.target).attr('onclick');
            const pageValue = onclick?.match(/ClickPage\('?([^']*)'?\)/)?.[1];
            if (pageValue !== undefined) {
                this.clickPage(pageValue);
            }
        },

        handleBookHover: function(event) {
            const $target = $(event.target).closest('a[id^="Img"]');
            const onmouseover = $target.attr('onmouseover');
            if (onmouseover) {
                const match = onmouseover.match(/ShowBOOK_NAME\('([^']*)','([^']*)','([^']*)','([^']*)'\)/);
                if (match) {
                    this.showBookName(match[1], match[2], match[3], match[4]);
                }
            }
        },

        handleBookClick: function(event) {
            event.preventDefault();
            const $target = $(event.target).closest('a[id^="Img"]');
            const onclick = $target.attr('onclick');
            if (onclick) {
                const match = onclick.match(/Chg_BOOK_NO\('([^']*)',\s*'([^']*)'\)/);
                if (match) {
                    this.changeBookNo(match[1], match[2]);
                }
            }
        },

        handlePrint: function(event) {
            event.preventDefault();
            this.printBook();
        },

        handleFormSubmit: function(event) {
            try {
                // 顯示載入狀態
                this.showLoadingState(true);

                // 記錄搜尋條件
                const searchData = {
                    keyword: $('#whereKeyword').val(),
                    grade: $('#GRADE').val(),
                    classNo: $('#Class_No').val(),
                    page: $('#Page').val()
                };

                console.log('提交搜尋條件:', searchData);

                // 表單會自然提交，這裡只是記錄和顯示狀態
                return true;
            } catch (error) {
                console.error('表單提交時發生錯誤:', error);
                event.preventDefault();
                this.showLoadingState(false);
                this.showMessage('提交時發生錯誤，請稍後再試', 'error');
                return false;
            }
        },

        getGradeNumber: function(chineseName) {
            const gradeMap = {
                "一": "1",
                "二": "2",
                "三": "3",
                "四": "4",
                "五": "5",
                "六": "6"
            };
            return gradeMap[chineseName] || "";
        },

        enhanceUserExperience: function() {
            // 添加鍵盤事件支援
            $('#whereKeyword').on('keypress', function(e) {
                if (e.which === 13) { // Enter鍵
                    e.preventDefault();
                    $(this.targetFormID).submit();
                }
            }.bind(this));

            // 添加表單元素焦點效果
            $('.form-control').on('focus', function() {
                $(this).closest('.form-group').addClass('focused');
            }).on('blur', function() {
                $(this).closest('.form-group').removeClass('focused');
            });

            // 添加按鈕hover效果增強
            $('.btn-pink').on('mouseenter', function() {
                if (!$(this).hasClass('active')) {
                    $(this).addClass('btn-hover');
                }
            }).on('mouseleave', function() {
                $(this).removeClass('btn-hover');
            });

            // 添加表格行hover效果
            $('.table-ecool tbody tr').on('mouseenter', function() {
                $(this).addClass('table-row-hover');
            }).on('mouseleave', function() {
                $(this).removeClass('table-row-hover');
            });
        },

        showLoadingState: function(isLoading) {
            const $submitBtn = $('input[type="submit"]');
            const $clearBtn = $('input[value="清除搜尋"]');

            if (isLoading) {
                $submitBtn.prop('disabled', true).val('搜尋中...');
                $clearBtn.prop('disabled', true);
            } else {
                $submitBtn.prop('disabled', false).val('搜尋');
                $clearBtn.prop('disabled', false);
            }
        },

        showMessage: function(message, type = 'info') {
            // 移除現有訊息
            $('.alert-message').remove();

            // 添加新訊息
            const alertClass = type === 'success' ? 'alert-success' :
                              type === 'error' ? 'alert-danger' : 'alert-info';

            const messageHtml = `
                <div class="alert ${alertClass} alert-message" style="margin: 10px 0;">
                    <button type="button" class="close" data-dismiss="alert">&times;</button>
                    ${message}
                </div>
            `;

            $('.form-inline').first().after(messageHtml);

            // 3秒後自動隱藏
            setTimeout(() => {
                $('.alert-message').fadeOut();
            }, 3000);
        }
    };

    // 初始化模組
    query3Module.init();

    // 設置全局錯誤處理
    window.addEventListener('error', function(e) {
        console.error('頁面錯誤:', e);
    });
});

