/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX-Web/Alphabets/BoldItalic/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXMathJax_Alphabets-bold-italic"]={directory:"Alphabets/BoldItalic",family:"STIXMathJax_Alphabets",weight:"bold",style:"italic",testString:"\u00A0\u0384\u0385\u0386\u0387\u0388\u0389\u038A\u038C\u038E\u038F\u0390\u03AA\u03AB\u03AC",32:[0,0,250,0,0],160:[0,0,250,0,0],900:[680,-516,300,140,319],901:[680,-516,380,27,440],902:[693,0,667,-68,593],903:[459,-311,333,116,264],904:[693,0,700,10,748],905:[693,0,850,9,889],906:[693,0,450,9,503],908:[693,18,722,11,691],910:[693,0,700,8,855],911:[693,0,808,25,774],912:[680,9,278,6,419],938:[905,0,389,-32,486],939:[905,0,611,21,697],940:[680,13,576,-3,574],941:[680,13,454,-5,408],942:[680,205,488,-7,474],943:[680,9,278,2,286],944:[680,13,536,-7,500],970:[655,9,278,2,351],971:[655,13,536,-7,477],972:[680,13,500,-3,441],973:[680,13,536,-7,477],974:[680,13,735,-3,676],1025:[905,0,667,-35,645],1026:[669,205,789,80,737],1027:[947,0,604,-32,658],1028:[685,18,657,44,689],1029:[685,18,556,-22,502],1030:[669,0,389,-32,406],1031:[905,0,389,-35,477],1032:[669,99,500,-8,562],1033:[669,18,954,-59,896],1034:[669,0,982,-32,924],1035:[669,0,830,71,757],1036:[947,0,678,-35,697],1038:[951,18,666,99,712],1039:[669,184,778,-33,791],1040:[683,0,667,-57,604],1041:[669,0,635,-18,629],1042:[669,0,654,-25,624],1043:[669,0,604,-32,658],1044:[669,184,696,-115,718],1045:[669,0,667,-35,645],1046:[678,0,927,-63,969],1047:[686,18,561,-10,549],1048:[669,0,768,-33,790],1049:[948,0,768,-33,790],1050:[678,0,678,-35,697],1051:[669,18,742,-59,764],1052:[669,12,890,-34,912],1053:[669,0,769,-32,791],1054:[685,18,722,53,717],1055:[669,0,767,-35,789],1056:[669,0,590,-30,611],1057:[685,18,667,65,710],1058:[669,0,611,80,681],1059:[669,18,666,99,712],1060:[669,0,833,72,816],1061:[669,0,607,-61,657],1062:[669,184,770,-32,792],1063:[669,0,758,120,780],1064:[669,0,960,-33,982],1065:[669,184,960,-33,982],1066:[669,0,780,107,722],1067:[669,0,985,-19,1007],1068:[669,0,636,-19,578],1069:[685,18,671,4,648],1070:[685,18,905,-38,871],1071:[669,0,710,-65,732],1072:[462,14,527,20,497],1073:[685,13,499,32,570],1074:[462,13,482,25,458],1075:[461,14,368,-8,371],1076:[695,13,496,22,466],1077:[462,13,431,22,415],1078:[462,13,898,-4,890],1079:[462,13,400,-11,378],1080:[462,9,542,34,512],1081:[697,9,542,34,514],1082:[461,8,522,12,527],1083:[462,11,507,-48,477],1084:[449,11,667,-48,637],1085:[462,9,543,13,513],1086:[462,13,500,24,468],1087:[462,9,543,13,513],1088:[462,205,497,-96,470],1089:[462,13,435,26,423],1090:[462,9,777,10,747],1091:[462,205,447,-94,422],1092:[699,205,750,28,717],1093:[462,13,456,-38,511],1094:[462,179,542,34,512],1095:[462,9,531,48,501],1096:[462,9,800,36,770],1097:[462,179,800,36,770],1098:[462,13,594,29,556],1099:[462,13,754,43,724],1100:[462,13,491,43,453],1101:[462,13,444,-12,405],1102:[462,13,740,12,710],1103:[449,11,538,2,508],1105:[655,13,434,22,487],1106:[699,205,523,12,490],1107:[697,14,368,-8,456],1108:[462,13,415,22,450],1109:[462,13,389,0,352],1110:[684,9,278,20,280],1111:[655,9,278,22,382],1112:[685,207,278,-161,307],1113:[462,13,694,-48,656],1114:[462,13,733,12,695],1115:[699,9,556,12,515],1116:[697,8,522,12,527],1118:[697,205,447,-94,436],1119:[462,179,538,30,508],1122:[669,0,761,62,707],1123:[699,13,569,20,531],1130:[669,0,978,-22,918],1131:[449,13,844,-4,825],1138:[685,18,722,53,717],1139:[462,13,500,24,468],1140:[678,18,667,66,750],1141:[462,13,487,15,534],1168:[834,0,539,-32,641],1169:[590,9,360,31,457],8453:[683,14,847,52,795],8470:[675,15,1055,24,1031],57500:[775,235,776,40,765],57501:[775,235,759,44,779],57502:[775,235,658,44,771],57523:[703,205,556,-188,517],58157:[775,207,671,46,675],58159:[775,207,664,-65,706],58161:[775,207,588,-100,671],58163:[775,207,571,46,547],58165:[775,207,508,44,515],58167:[775,207,505,-54,629],58169:[775,207,579,20,583],58171:[775,207,615,46,602],58173:[775,207,355,29,483],58175:[775,207,594,35,656],58177:[775,207,598,18,642],58179:[775,207,697,-34,737],58181:[775,207,571,35,584],58183:[775,207,504,-54,629],58185:[775,235,500,32,506],58187:[775,207,652,1,772],58189:[775,207,636,27,652],58191:[775,207,504,23,514],58193:[775,207,595,46,641],58195:[775,207,474,20,521],58197:[775,207,582,20,584],58199:[775,207,726,1,772],58201:[775,207,622,-41,730],58203:[775,207,720,37,808],58205:[775,207,782,24,795],58207:[775,207,608,20,681],58209:[775,207,727,0,771],58211:[775,207,925,6,978],58215:[775,235,475,-35,509],58219:[775,235,525,-68,651],58223:[775,235,485,16,466],58227:[775,235,530,12,731],58229:[775,235,569,-50,592],58231:[775,207,571,46,547],58233:[775,207,601,46,579],58235:[775,207,525,46,543],58238:[775,235,792,-40,777],58240:[707,14,670,10,662],58242:[707,14,622,14,598],58244:[628,14,411,18,390],58246:[473,14,355,15,338],58248:[666,0,493,25,508],58249:[666,0,480,16,472],58309:[462,207,514,47,475],58310:[462,9,357,55,274],58368:[703,205,556,-188,517]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"STIXMathJax_Alphabets-bold-italic"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Alphabets/BoldItalic/Main.js"]);
