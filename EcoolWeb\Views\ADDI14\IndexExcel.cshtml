﻿@model ADDI14IndexViewModel
@{
    ViewBag.Title = ViewBag.Panel_Title;
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";

    string SCHOOL_NO = EcoolWeb.Models.UserProfileHelper.GetSchoolNo(); ;
    string LogoAct = "GuestIndex";
}

@Html.Partial("_Notice")

@using (Html.BeginForm("IndexExcel", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", id = "form1", @AutoComplete = "Off", @target = "_blank" }))
{
    @Html.AntiForgeryToken()
    <div class="row">
        <div class="col-md-8 col-md-offset-2">
            <div class="panel with-nav-tabs panel-info" id="panel">
                <div class="panel-heading">
                    <h1>@ViewBag.Title</h1>
                </div>
                <div class="panel-body">
                    <div class="form-group">
                        <div class="input-group input-group-lg">
                            <span class="input-group-addon"><i class="fa fa-user"></i></span>
                            @Html.Editor("CARD_NO", new { htmlAttributes = new { @class = "form-control", @placeholder = "請用數位學生證感應", @onKeyPress = "call(event,this);" } })
                        </div>
                        @Html.ValidationMessage("CARD_NO", "", new { @class = "text-danger" })
                    </div>
                </div>
            </div>
            <div id="DetailsView">
                <div style="margin-top:5px;margin-bottom:5px;text-align:center">
                    <span class="glyphicon glyphicon-circle-arrow-down fa-2x" aria-hidden="true" style="color:#004da0"></span>
                </div>
                @Html.ValidationMessage("DetailsError", new { @class = "text-danger" })
                <div class="panel panel-ZZZ">
                    <div class="panel-heading text-center">
                        報到清單
                    </div>
                    <div class="panel-body">
                        <div id="editorRows">
                            @if (Model.Details != null)
                            {
                                foreach (var item in Model.Details)
                                {
                                    @Html.Action("_EditDetails", item)
                                }
                            }
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <div class="col-xs-12 text-center">
                        <div class="form-inline">
                            <input type="button" value="匯出名單" class="btn btn-default" onclick="Save()" />
                            <a class="btn btn-default" href="@Url.Action(LogoAct,"Home")">回首頁</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-6 col-lg-offset-3">
            <div class="use-absolute" id="ErrorDiv">
                <div class="use-absoluteDiv">
                    <div class="alert alert-danger" role="alert">
                        <h1>
                            <i class="fa fa-exclamation-circle"></i>
                            <strong id="ErrorStr"></strong>
                        </h1>
                    </div>
                </div>
            </div>
        </div>
    </div>
}

@section Scripts {
    <script src="~/Scripts/buzz/buzz.min.js"></script>
    <script language="JavaScript">
        var targetFormID = '#form1';
        var SwipeSound = new buzz.sound("@Url.Content("~/Content/mp3/Swipe1.mp3")" );
        (function ($) {
            $(window).on("beforeunload", function () {
                return true;
            })
        })(jQuery);

        function Save()
        {

            $(targetFormID).attr("action", "@Url.Action("Export", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();

        }

        $(document).ready(function () {
            $("#CARD_NO").focus();
        });

        function call(e, input) {
            var code = (e.keyCode ? e.keyCode : e.which);

            if (code == 13) // 13 是 Enter 按鍵的值
            {
                var CARD_NO = $('#CARD_NO').val();

                event.preventDefault();

                if (CARD_NO != '') {

                    var ThisCARD_NO = ''
                    var ThisCheckIn = ''
                    var ThisNAME = ''
                    var ThisNGSpan = ''
                    var ThisOkSpan = ''

                    if ($("#Tr" + CARD_NO).length > 0) {
                        ThisCARD_NO = "#Tr" + CARD_NO
                        ThisCheckIn = '.CheckIn' + CARD_NO
                        ThisNAME = '.NAME' + CARD_NO
                        ThisNGSpan = '#NGSpan' + CARD_NO
                        ThisOkSpan = '#OkSpan' + CARD_NO
                    }
                    else if ($(".Tr@(SCHOOL_NO)" + CARD_NO).length > 0)  {
                        ThisCARD_NO = ".Tr@(SCHOOL_NO)" + CARD_NO
                        ThisCheckIn = '.CheckIn@(SCHOOL_NO)' + CARD_NO
                        ThisNAME = '.NAME@(SCHOOL_NO)' + CARD_NO
                        ThisNGSpan = '.NGSpan@(SCHOOL_NO)' + CARD_NO
                        ThisOkSpan = '.OkSpan@(SCHOOL_NO)' + CARD_NO
                    }

                    if ($(ThisCARD_NO).length > 0) {

                        if ($(ThisCheckIn).val()=='1') {
                            $('#ErrorStr').html('已報到成功，勿重複感應!')
                            $('#ErrorDiv').show()
                        }
                        else {
                            $(ThisNGSpan).hide();
                            $(ThisOkSpan).show();
                            $(ThisCheckIn).val('1');

                            var NAME = $(ThisNAME).val();
                            SwipeOK()
                            $('#ErrorStr').html(NAME + ' 報到成功')
                            $('#ErrorDiv').show()
                        }
                    }
                    else {
                        $('#ErrorStr').html('此數位學生證無在此報到清單裡')
                        $('#ErrorDiv').show()
                    }

                    setTimeout(function () {
                        $('#CARD_NO').val('')
                        $("#CARD_NO").focus();
                        $('#ErrorStr').val('')
                        $('#ErrorDiv').hide()
                    }, 1000);
                }
            }
        }

        function SwipeOK() {

            SwipeSound.play();
         }
    </script>
}