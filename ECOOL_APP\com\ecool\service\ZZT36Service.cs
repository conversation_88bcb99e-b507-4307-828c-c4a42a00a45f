﻿using com.ecool.service;
using ECOOL_APP.com.ecool.Models.entity;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Web.Mvc;

namespace com.ecool.service
{

    public class ZZT36Service : ServiceBase
    {
        /// <summary>
        /// Modify By Orin
        /// Modify Date :2015/05/20
        /// Description : 首頁說明
        /// </summary>
        /// <param name="collection">畫面上的欄位</param>
        /// <returns></returns>
        public static List<uZZT36> USP_ZZT36_QUERY(FormCollection collection)
        {
            List<uZZT36> list_data = new List<uZZT36>();
            StringBuilder sb = new StringBuilder();
            DataTable dt;
            try
            {

                sb.Append(" SELECT  BRE_NO,DATA_CODE,SCHOOL_NO, ");
                sb.Append("         COMMENT,CHG_PERSON,CHG_DATE, ");
                sb.Append("         CRE_PERSON,CRE_DATE ");
                sb.Append(" FROM ZZT36 ");
                sb.Append(" WHERE 1 = 1 ");
                if (collection["BRE_NO"] != null)
                {
                    sb.Append(" AND BRE_NO = " + collection["BRE_NO"]);
                }

                if (collection["DATA_CODE"] != null)
                {
                    sb.Append(" AND DATA_CODE = " + collection["DATA_CODE"]);
                }

                if (collection["SCHOOL_NO"] != null)
                {
                    sb.Append(" AND SCHOOL_NO = " + collection["SCHOOL_NO"]);
                }

                if (collection["COMMENT"] != null)
                {
                    sb.Append(" AND COMMENT = " + collection["COMMENT"]);
                }

                if (collection["CHG_PERSON"] != null)
                {
                    sb.Append(" AND CHG_PERSON = " + collection["CHG_PERSON"]);
                }

                if (collection["CHG_DATE"] != null)
                {
                    sb.Append(" AND CHG_DATE = " + collection["CHG_DATE"]);
                }

                if (collection["CRE_PERSON"] != null)
                {
                    sb.Append(" AND CRE_PERSON = " + collection["CRE_PERSON"]);
                }

                if (collection["CRE_DATE"] != null)
                {
                    sb.Append(" AND CRE_DATE = " + collection["CRE_DATE"]);
                }
                dt = new sqlConnection.sqlConnection().executeQueryByDataTableList(sb.ToString());
                foreach (DataRow dr in dt.Rows)
                {
                    list_data.Add(new uZZT36()
                    {
                        BRE_NO = dr["BRE_NO"].ToString(),
                        DATA_CODE = dr["DATA_CODE"].ToString(),
                        SCHOOL_NO = dr["SCHOOL_NO"].ToString(),
                        COMMENT = dr["COMMENT"].ToString(),
                        CHG_PERSON = dr["CHG_PERSON"].ToString(),
                        CHG_DATE = Convert.ToDateTime(dr["CHG_DATE"].ToString()),
                        CRE_PERSON = dr["CRE_PERSON"].ToString(),
                        CRE_DATE = Convert.ToDateTime(dr["CRE_DATE"].ToString()),

                    });
                }
            }
            catch (Exception exception)
            {

                throw exception;
            }

            return list_data;

        }

        /// <summary>
        /// Modify By Orin
        /// Modify Date :2015/05/20
        /// Description : 首頁說明
        /// </summary>
        /// <param name="collection">畫面上的欄位</param>
        /// <returns></returns>
        public static List<uZZT36> USP_ZZT36_QUERY(string SCHOOL_NO, string DATA_CODE)
        {
            List<uZZT36> list_data = new List<uZZT36>();
            StringBuilder sb = new StringBuilder();
            DataTable dt;
            try
            {

                sb.Append(" SELECT  BRE_NO,DATA_CODE,SCHOOL_NO, ");
                sb.Append("         COMMENT,CHG_PERSON,CHG_DATE, ");
                sb.Append("         CRE_PERSON,CRE_DATE ");
                sb.Append(" FROM ZZT36 ");
                sb.Append(" WHERE 1 = 1 ");

                if (DATA_CODE != string.Empty)
                {
                    sb.Append(" AND DATA_CODE = '" + DATA_CODE + "'");
                }

                if (SCHOOL_NO != null)
                {
                    sb.Append(" AND SCHOOL_NO = '" + SCHOOL_NO +"'");
                }

                dt = new sqlConnection.sqlConnection().executeQueryByDataTableList(sb.ToString());
                foreach (DataRow dr in dt.Rows)
                {
                    list_data.Add(new uZZT36()
                    {
                        BRE_NO = dr["BRE_NO"].ToString(),
                        DATA_CODE = dr["DATA_CODE"].ToString(),
                        SCHOOL_NO = dr["SCHOOL_NO"].ToString(),
                        COMMENT = dr["COMMENT"].ToString(),
                        CHG_PERSON = dr["CHG_PERSON"].ToString(),
                        CHG_DATE = Convert.ToDateTime(dr["CHG_DATE"].ToString()),
                        CRE_PERSON = dr["CRE_PERSON"].ToString(),
                        CRE_DATE = Convert.ToDateTime(dr["CRE_DATE"].ToString()),

                    });
                }
            }
            catch (Exception exception)
            {

                throw exception;
            }

            return list_data;

        }
    }
}
