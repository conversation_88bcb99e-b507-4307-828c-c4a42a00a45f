﻿@model ECOOL_APP.com.ecool.Models.DTO.GradeBorrowBookRankViewModel
@{
    ViewBag.Title = ViewBag.Panel_Title;

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);

    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
    else
    {
        Layout = "~/Views/Shared/_LayoutSEO.cshtml";
    }

    int BookPageCount = 0;

}

<script src="~/Scripts/Highcharts-4.0.1/js/highcharts.js"></script>
<script src="~/Scripts/Highcharts-4.0.1/js/modules/exporting.js"></script>
<script src="~/Scripts/Blob.js"></script>
<script src="~/Scripts/FileSaver.js"></script>
<script src="~/Scripts/tableexport.js"></script>
<script src="~/Scripts/printThis/printThis.js"></script>

@Html.Partial("_Title_Secondary")
@{
    Html.RenderAction("_Menu", new { NowAction = "RankOfGradeBookBorrow" });
}

@using (Html.BeginForm("RankOfGradeBookBorrow", "SECI07", FormMethod.Post, new { id = "form1", name = "form1" }))
{
<div class="row">
    <div class="col-md-12">
        @Html.LabelFor(m => m.Where_SYEAR, new { @class = "col-md-1" })
        <div class="col-md-3">
            @Html.DropDownListFor(m => m.Where_SYEAR, (IEnumerable<SelectListItem>)ViewBag.SYearItems, new { @class = "form-control input-sm", @onchange = "this.form.submit()" })
        </div>
        @Html.LabelFor(m => m.Where_MONTH, new { @class = "col-md-1" })
        <div class="col-md-3">
            @Html.DropDownListFor(m => m.Where_MONTH, (IEnumerable<SelectListItem>)ViewBag.MonthItems, new { @class = "form-control", @onchange = "this.form.submit();" })
        </div>
    </div>
    </div>
    }
    <br />

    @if (ViewBag.SYearItems == null)
    {
        <div>無任何借閱資料</div>
    }
    else if (string.IsNullOrWhiteSpace(Model.Where_SYEAR))
    {
        <div>未輸入學年度</div>
    }
    else
    {
        if (Model.GradeRankList != null && Model.GradeRankList.Count > 0)
        {
            <table class="table table-responsive table-striped table-hover text-center">
                <thead>
                    <tr>
                        <th class="text-center">
                            @Html.DisplayNameFor(m => m.GradeRankList.FirstOrDefault().RANK)
                        </th>
                        <th class="text-center">
                            @Html.DisplayNameFor(m => m.GradeRankList.FirstOrDefault().Grade)
                        </th>
                        <th class="text-center">
                            @Html.DisplayNameFor(m => m.GradeRankList.FirstOrDefault().BORROW_BOOK_COUNT)
                        </th>
                        <th class="text-center">
                            @Html.DisplayNameFor(m => m.GradeRankList.FirstOrDefault().BORROW_BOOK_AVG)

                            @if (string.IsNullOrWhiteSpace(Model.Where_MONTH))
                            {
                                <span>(月平均)</span>
                            }
                            else
                            {
                                <span>(日平均)</span>
                            }
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var item in Model.GradeRankList)
                    {
                        <tr>
                            <td>@item.RANK</td>
                            <td>@item.Grade</td>
                            <td>@item.BORROW_BOOK_COUNT</td>
                            <td>@item.BORROW_BOOK_AVG</td>
                        </tr>
                        BookPageCount += item.BORROW_BOOK_COUNT;
                    }
                    <tr>
                        <td></td>
                        <td></td>
                        <td style="border-top:2px solid #d56666">
                            <div class="text-danger text-center">
                                本頁總計: @BookPageCount 本書　　
                            </div>
                        </td>
                        <td style="border-top:2px solid #d56666">
                            <div class="text-danger text-center">
                                @if (string.IsNullOrWhiteSpace(Model.Where_MONTH))
                                {
                                    @:月平均總計
                                }
                                else
                                {
                                    @:日平均總計
                                }
                                @Model.GradeRankList.Average(a => a.BORROW_BOOK_AVG).ToString("#0.0") 本書
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>

            <div class="col-sm-12">
                @if (Model.GradeColumnChart != null)
                {
                    @Model.GradeColumnChart
                }
            </div>
        }
        else
        {
            <div>資料量不足</div>
        }
    }
