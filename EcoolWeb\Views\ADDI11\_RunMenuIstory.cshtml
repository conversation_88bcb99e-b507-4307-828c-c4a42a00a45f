﻿@using com.ecool.service
@{
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    bool IsTeacherEdit = System.Web.Mvc.Html.HtmlHelperExtensions.PermissionCheck((string)ViewBag.BRE_NO, "TeacherEdit");

}

<div class="form-group cScreen ">
    @if (ViewBag.ISTASKLIST != "True")
    {
        <a role="button" href='@Url.Action("IndexIstory", (string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys  @(ViewBag.NowAction == "IndexIstory" ? "active" : "")">
            運動撲滿說明
        </a>
        <a role="button" href='@Url.Action("RunCountLogIstory", "ADDI11")' class="btn btn-sm btn-sys  @(ViewBag.NowAction == "RunCountLogIstory" ? "active" : "")">
            全校跑步里程一覽
        </a>
        <a role="button" href='@Url.Action("OrderListIstory", (string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys @(ViewBag.NowAction == "OrderListIstory" ? "active" : "")">
            跑步排行榜
        </a>
        <a role="button" href='@Url.Action("RunCountLogInfoIstory", "ADDI11")' class="btn btn-sm btn-sys @(ViewBag.NowAction == "RunCountLogInfoIstory" ? "active" : "")">
            班級跑步總和排行
        </a>
    }
    @if (user != null)
    {
        if (user.USER_TYPE == UserType.Teacher || user.USER_TYPE == UserType.Admin && ViewBag.ISTASKLIST != "True")
        {
            if (ViewBag.LookSchoolRunPermission)
            {
        <a role="button" href='@Url.Action("TotalRunLog",(string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys  @(ViewBag.NowAction=="TotalRunLog" ? "active":"")">
            全校跑步情形
        </a>
            }
            if (ViewBag.LookClassRunPermission)
            {
                <a role="button" href='@Url.Action("TotalClassRunLog",(string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys  @(ViewBag.NowAction=="TotalClassRunLog" ? "active":"")">
                    本班跑步情形
                </a>
            }
        }

        if (ViewBag.ISTASKLIST != "True")
        {
            <a role="button" href='@Url.Action("RunMapIstory",(string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys  @(ViewBag.NowAction=="RunMapIstory" ? "active":"")">
                我的跑步地圖
            </a>

            <a role="button" href='@Url.Action("MyRunLogIstory",(string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys  @(ViewBag.NowAction=="MyRunLogIstory" ? "active":"")">
                我的跑步紀錄
            </a>
        }

    }

    @if (user != null)
    {

        if (user.USER_TYPE == UserType.Student && !user.USER_NO.Contains("run"))
        {
            <a role="button" href='@Url.Action("MyFriendsRunLogIstory",(string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys  @(ViewBag.NowAction=="MyFriendsRunLogIstory" ? "active":"")">
                觀看好友
            </a>

            @*<a role="button" href='@Url.Action("SecretMission",(string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys @(ViewBag.NowAction=="SecretMission" ? "active":"")">
                加點任務 <span class="badge">@ViewBag.SecretMissionCount</span>
            </a>*@

        }

      

        @*if (user.USER_NO.Contains("run") /*運動撲滿小幫手*/)
        {
            <a role="button" href='@Url.Action("Edit",(string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys @(ViewBag.NowAction=="Edit" ? "active":"")">
                新增跑步量
            </a>
            if (ViewBag.ISTASKLIST != "True")
            {
                <a role="button" href='@Url.Action("ExportRunExcel", "ZZZI09_HIS")' class="btn btn-sm btn-sys @(ViewBag.NowAction=="ExportRunExcel" ? "active":"")">
                    跑步名單列印
                </a>
            }
        }*@
    }
</div>