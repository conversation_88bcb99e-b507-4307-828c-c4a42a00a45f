﻿@model GAAI01WearIndexViewModel
@using ECOOL_APP.com.ecool.util;
@{
    ViewBag.Title = ViewBag.Panel_Title;

    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@{
    Html.RenderAction("_PageMenu", new { NowAction = "TagWearIndex" });
}

@using (Html.BeginForm("TagWearIndex", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1", @class = "form-horizontal", enctype = "multipart/form-data" }))
{

    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })
    @Html.HiddenFor(m => m.IsSearch)
    @Html.HiddenFor(m => m.IsWearData)
    @Html.HiddenFor(m => m.WhereWearModelType)
    @Html.HiddenFor(m => m.WhereSYEARSEMESTER)

    <div class="panel panel-ZZZ" name="TOP">
        <div class="panel-heading text-center">
            @Html.BarTitle(null, (string)ViewBag.Title)
        </div>
        <div class="panel-body">
            <div class="form-horizontal">
                <div class="form-group row">
                    @Html.LabelFor(m => m.WhereCLASS_NO, htmlAttributes: new { @class = "col-md-3 control-label control-label-required" })
                    <div class="col-md-9">
                        @Html.DropDownListFor(m => m.WhereCLASS_NO, (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control form-control-required" })
                    </div>
                    <div class="col-md-9">
                        @Html.ValidationMessageFor(m => m.WhereCLASS_NO, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group row">
                    @Html.LabelFor(m => m.WhereALARM_ID, htmlAttributes: new { @class = "col-md-3 control-label control-label-required" })
                    <div class="col-md-9">
                        @Html.DropDownListFor(m => m.WhereALARM_ID, (IEnumerable<SelectListItem>)ViewBag.AlarmCycleSelectItems, new { @class = "form-control form-control-required" })
                    </div>
                    <div class="col-md-9">
                        @Html.ValidationMessageFor(m => m.WhereALARM_ID, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group row">
                    <div class="col-md-3">
                    </div>
                    <div class="col-md-9">
                        @if (Model.IsWearData == true)
                        {
                            <strong style="background-color:crimson;color:aliceblue;font-size:24px">
                                *此班級這個登記日期已登記過，詳細資料請至
                                「<a target="_blank" href="@Url.Action("SearchClassIndex" ,new { WhereCLASS_NO = Model.WhereCLASS_NO ,WhereSYEARSEMESTER=$"{ Model.gAAT01?.SYEAR}_{Model.gAAT01?.SEMESTER}"})" style="color:aliceblue"> 查詢班級登記</a>」
                                查詢
                            </strong>
                        }
                        else
                        {
                            <strong style="color:red">*一週只須登記一次，所以日期以一週做選擇，例如108/9/2~108/9/6</strong>
                        }
                    </div>
                </div>
            </div>

            @if (Model.IsSearch == 1)
            {
                <div class="text-center">
                    <button type="button" class="btn btn-default" onclick="onSave()">
                        <span class="fa fa-check-circle" aria-hidden="true"></span>我要繼續登記
                    </button>
                </div>
            }
            else
            {
                <div class="text-center">
                    <button type="button" class="btn btn-default" onclick="onSearch()">
                        <span class="fa fa-check-circle" aria-hidden="true"></span>我要登記
                    </button>
                </div>
            }
        </div>
    </div>
}

@section Scripts {
    <script language="JavaScript">

        var targetFormID = '#form1';

        $(function () {
            if ($('#@Html.IdFor(m=>m.IsSearch)').val() == 1 && $('#@Html.IdFor(m=>m.IsWearData)').val().toLowerCase() == 'false') {
                onSave()
            }
        });

        function onSave()
        {
            $(targetFormID).attr("action", "@Url.Action("TagWearDetails", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        function onSearch()
        {
            $('#@Html.IdFor(m=>m.IsSearch)').val(1)
            $(targetFormID).attr("action", "@Url.Action("TagWearIndex", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }
    </script>
}