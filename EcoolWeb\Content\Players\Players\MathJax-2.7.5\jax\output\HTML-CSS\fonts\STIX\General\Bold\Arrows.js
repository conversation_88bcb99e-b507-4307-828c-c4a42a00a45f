/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/General/Bold/Arrows.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXGeneral-bold"],{8592:[451,-55,977,68,909],8593:[676,170,584,94,490],8594:[451,-55,977,68,909],8595:[676,170,584,94,490],8596:[451,-55,977,30,948],8597:[736,230,584,94,490],8598:[676,170,977,68,911],8599:[676,170,977,68,911],8600:[676,170,977,68,911],8601:[676,170,977,68,911],8602:[451,-55,977,68,909],8603:[451,-55,977,68,909],8604:[462,-72,956,66,890],8605:[462,-72,956,66,890],8606:[451,-55,977,68,909],8607:[676,165,568,86,482],8608:[451,-55,977,68,909],8609:[676,165,568,86,482],8610:[451,-55,977,68,909],8611:[451,-55,977,68,909],8612:[451,-55,977,68,909],8613:[676,165,584,94,490],8614:[451,-55,977,68,909],8615:[676,165,584,94,490],8616:[732,196,584,94,490],8617:[539,-55,966,66,900],8618:[539,-55,966,66,900],8619:[540,6,966,66,900],8620:[540,6,966,66,900],8621:[451,-55,1297,55,1242],8622:[451,-55,977,30,948],8623:[683,154,562,68,494],8624:[686,170,584,45,503],8625:[686,170,584,81,539],8626:[686,170,584,45,503],8627:[686,170,584,81,539],8628:[686,162,960,66,894],8629:[686,171,960,56,904],8630:[524,0,971,66,905],8631:[524,0,971,66,905],8632:[768,170,977,68,911],8633:[618,114,977,68,909],8634:[693,127,974,105,869],8635:[693,127,974,105,869],8636:[501,-209,977,66,910],8637:[297,-5,977,65,909],8638:[694,162,552,239,481],8639:[694,162,352,71,313],8640:[501,-209,977,66,910],8641:[297,-5,977,66,910],8642:[694,162,552,239,481],8643:[694,162,552,71,313],8644:[618,114,977,68,909],8645:[676,165,864,66,798],8646:[618,114,977,68,909],8647:[618,114,977,68,909],8648:[676,165,864,66,798],8649:[618,114,977,68,909],8650:[676,165,864,66,798],8651:[571,21,977,66,910],8652:[571,21,977,66,910],8653:[570,64,977,68,909],8654:[570,64,1240,50,1190],8655:[570,64,977,68,909],8656:[570,64,977,68,909],8657:[676,170,714,40,674],8658:[570,64,977,68,909],8659:[676,170,714,40,674],8660:[570,64,1240,50,1190],8661:[736,230,714,40,674],8662:[662,156,926,54,872],8663:[662,156,926,54,872],8664:[662,156,926,54,872],8665:[662,156,926,54,872],8668:[451,-55,977,62,914],8669:[451,-55,977,62,914],8678:[551,45,926,60,866],8679:[662,156,685,45,641],8680:[551,45,926,60,866],8681:[662,156,685,45,641],8682:[705,201,685,45,641],8693:[676,165,864,66,798]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/Bold/Arrows.js");
