﻿@model ECOOL_APP.EF.AWAT06
@{
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    if (string.IsNullOrWhiteSpace(UUID) == false)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
    ViewBag.Title = "-角色娃娃-兌換角色娃娃";
    string ImageUrl = Url.Content(@"~/Content/Players/");
    string PlayerImageUrl = ImageUrl + Model.IMG_FILE;
    ECOOL_APP.UserProfile LoginUser = EcoolWeb.Models.UserProfileHelper.Get();
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@using (Html.BeginForm("Buy", "AWAI02", FormMethod.Post))
{
    @Html.AntiForgeryToken()
    @Html.HiddenFor(model => model.PLAYER_NO)
    <img src="~/Content/img/web-bar2-revise-18.png" style="width:100%" class="img-responsive App_hide" alt="Responsive image" />
    <div class="Div-EZ-AWAI02">
        <div class="Details">

            <div class="row">
                <div class="col-md-5 col-sm-5  dl-horizontal-EZ ">
                    <samp class="dt">
                        名稱
                    </samp>
                    <samp class="dd">
                        @Html.DisplayFor(model => model.PLAYER_NAME)
                    </samp>
                </div>
                <div class="col-md-5 col-sm-5   dl-horizontal-EZ ">
                    <samp class="dt">
                        酷幣點數
                    </samp>
                    <samp class="dd">
                        @Html.DisplayFor(model => model.COST_CASH)
                    </samp>
                </div>
            </div>

            <div class="row text-center">
                <div class="col-md-12 text-center">
                    <img src='@PlayerImageUrl' href="@ViewBag.ImageUrl" class="img-responsive " />
                </div>
            </div>
            <div class="row Div-btn-center">
                <div class="form-group">

                    @if (string.IsNullOrWhiteSpace(ViewBag.NGError))
                    {

                        <div class="col-12 alert alert-warning">
                            <b><span class="glyphicon glyphicon-info-sign"></span> 提醒：角色娃娃兌換後，無法退貨</b>
                        </div>
                        <div class="col-md-offset-3 col-md-3">
                            <button value="Create" class="btn btn-default">
                                確定送出
                            </button>
                        </div>
                        <div class="col-md-offset-1 col-md-3">
                            <a href='@Url.Action("Gallery", "AWAI02")' role="button" class="btn btn-default">
                                放棄編輯
                            </a>
                        </div>

                    }
                    else
                    {
                        <div class="col-md-12 text-center">
                            <a href='@Url.Action("Gallery", "AWAI02")' role="button" class="btn btn-default">
                                返回
                            </a>
                        </div>
                    }

                </div>
            </div>

        </div>
    </div>

    
} 

