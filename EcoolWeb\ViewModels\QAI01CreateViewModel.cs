﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

using ECOOL_APP.EF;
using MvcPaging;

namespace EcoolWeb.Models
{
    public class QAI01CreateViewModel
    {
        /// <summary>
        /// 搜尋字串
        /// </summary>
        public string whereKeyword { get; set; }

        /// <summary>
        /// 排序欄位
        /// </summary>
        public string OrdercColumn { get; set; }

        /// <summary>
        /// 頁次
        /// </summary>
        public int Page { get; set; }

        /// <summary>
        /// 一頁筆數 預設20筆
        /// </summary>
        public int PageSize { get; set; }

        public int total_pages { get; set; }

        /// <summary>
        /// 清除條件 
        /// </summary>
        public bool doClear { get; set; }

        public string QUESTIONS_ID {get; set; }
        public string MEMO { get; set; }
        public DateTime? END_DATE { get; set; }

        /// <summary>
        /// 查詢結果
        /// </summary>
        public List<COOC_Medias> MediasList;

        public string SelectMedias_id { get; set; }
        public string SelectMedias_name { get; set; }
        public string SelectMedias_contentLink { get; set; }
        public string SelectMedias_thumbnail { get; set; }
    }

    public class COOC_Videos
    {
        public int now_page { get; set; }

        public int total_pages { get; set; }

        public int records_per_page { get; set; }

        public string keyword { get; set; }

        public List<COOC_Medias> medias { get; set; }

    }

    public class COOC_Medias
    {
        public string id { get; set; }

        public string name { get; set; }

        public string knowledge_map_name { get; set; }

        public string type { get; set; }

        public string content { get; set; }

        public string link { get; set; }

        public string contentLink { get; set; }

        public string thumbnail { get; set; }

    }
}