/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/General/BoldItalic/Latin1Supplement.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXGeneral-bold-italic"],{160:[0,0,250,0,0],161:[494,205,389,19,320],162:[576,143,500,42,439],163:[683,12,500,-32,510],164:[542,10,500,-26,526],165:[669,0,500,33,628],166:[685,18,220,66,154],167:[685,143,500,36,459],168:[655,-525,333,55,397],169:[685,18,747,30,718],170:[685,-399,266,16,330],171:[415,-32,500,12,468],172:[399,-108,606,51,555],173:[282,-166,333,2,271],174:[685,18,747,30,718],175:[623,-553,333,51,393],176:[688,-402,400,83,369],177:[568,0,570,33,537],178:[683,-274,300,2,313],179:[683,-265,300,17,321],180:[697,-516,333,139,379],181:[449,207,576,-60,516],182:[669,193,617,60,679],183:[405,-257,250,51,199],184:[5,218,333,-80,156],185:[683,-274,300,30,301],186:[685,-400,300,56,347],187:[415,-32,500,12,468],188:[683,14,750,7,721],189:[683,14,750,-9,723],190:[683,14,750,7,726],191:[492,205,500,30,421],192:[947,0,667,-68,593],193:[947,0,667,-68,593],194:[940,0,667,-68,593],195:[905,0,667,-68,612],196:[905,0,667,-68,599],197:[1004,0,667,-68,593],198:[669,0,944,-64,918],199:[685,218,667,32,677],200:[947,0,667,-27,653],201:[947,0,667,-27,653],202:[940,0,667,-27,653],203:[905,0,667,-27,653],204:[947,0,389,-32,406],205:[947,0,389,-32,440],206:[940,0,389,-32,469],207:[905,0,389,-32,480],208:[669,0,722,-31,700],209:[905,15,722,-27,748],210:[947,18,722,27,691],211:[947,18,722,27,691],212:[940,18,722,27,691],213:[905,18,722,27,691],214:[905,18,722,27,691],215:[490,-16,570,48,522],216:[764,125,722,27,691],217:[947,18,722,67,744],218:[947,18,722,67,744],219:[940,18,722,67,744],220:[905,18,722,67,744],221:[947,0,611,71,659],222:[669,0,611,-27,573],223:[705,200,500,-200,473],224:[697,14,500,-21,456],225:[697,14,500,-21,456],226:[690,14,500,-21,475],227:[655,14,500,-21,497],228:[655,14,500,-21,485],229:[756,14,500,-21,456],230:[462,13,722,-5,673],231:[462,218,444,-24,392],232:[697,13,444,5,398],233:[697,13,444,5,419],234:[690,13,444,5,462],235:[655,13,444,5,470],236:[697,9,278,2,294],237:[697,9,278,2,310],238:[690,9,278,2,353],239:[655,9,278,2,362],240:[699,13,500,-3,454],241:[655,9,556,-6,507],242:[697,13,500,-3,441],243:[697,13,500,-3,441],244:[690,13,500,-3,462],245:[655,13,500,-3,485],246:[655,13,500,-3,470],247:[535,29,570,33,537],248:[560,119,500,-3,441],249:[697,9,556,15,493],250:[697,9,556,15,493],251:[690,9,556,15,493],252:[655,9,556,15,493],253:[697,205,444,-94,401],254:[699,205,500,-120,446],255:[655,205,444,-94,460]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/BoldItalic/Latin1Supplement.js");
