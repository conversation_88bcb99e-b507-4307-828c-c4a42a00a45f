﻿@model EcoolWeb.ViewModels.ADDTListViewModel
@{
    ViewBag.Title = "閱讀認證獎狀頒發-這次獎狀處理完成清單";
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    if (string.IsNullOrWhiteSpace(UUID) == false)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
}
@Html.Partial("_Title_Secondary")

@Html.Partial("_Notice")

<img src="~/Content/img/web-Bar-09.png" style="width:100%" class="img-responsive App_hide" alt="Responsive image"/>
<div class="table-responsive">
    <div class="text-center">
        <table class="table-ecool table-92Per table-hover table-ecool-reader">
            <thead>
                <tr>
                    <th>
                        學年
                    </th>
                    <th>
                        學期
                    </th>
                    <th>
                        班級
                    </th>
                    <th>
                        座號
                    </th>
                    <th>
                        姓名
                    </th>
                    <th>
                        閱讀冊數
                    </th>
                    <th>
                        認證等級
                    </th>
                    <th>
                        升級日期
                    </th>
                    <th>
                        備註
                    </th>
                </tr>
            </thead>
            <tbody>
                @foreach (var item in Model.A9List)
                {
                    <tr>
                        <td>
                            @Html.DisplayFor(modelItem => item.SYEAR)
                        </td>
                        <td>
                            @Html.DisplayFor(modelItem => item.SEMESTER)
                        </td>
                        <td>
                            @Html.DisplayFor(modelItem => item.CLASS_NO)
                        </td>
                        <td>
                            @Html.DisplayFor(modelItem => item.SEAT_NO)
                        </td>
                        <td>
                            @Html.DisplayFor(modelItem => item.SNAME)
                        </td>
                        <td>
                            @Html.DisplayFor(modelItem => item.BOOK_QTY)
                        </td>
                        <td>
                            @Html.DisplayFor(modelItem => item.LEVEL_DESC)
                        </td>
                        <td>
                            @Html.DisplayFor(modelItem => item.UP_DATE, "ShortDateTime")
                        </td>
                        <td></td>
                    </tr>
                }
            </tbody>
        </table>
    </div>
</div>
<div style="height:25px"></div>


@using (Html.BeginForm("HandleBookCreditList", "ADDT", FormMethod.Post, new { name = "ADDT", id = "ADDT" }))
{
    @Html.HiddenFor(m => m.whereKeyword)
    @Html.HiddenFor(m => m.whereGrade)
    @Html.HiddenFor(m => m.whereCLASS_NO)
    @Html.HiddenFor(m => m.OrdercColumn)
    @Html.HiddenFor(m => m.Page)
    <div class="row">
        <div class="text-center">
            <input type="button" value="返回" class="btn btn-default" onclick="ADDT.submit();" />
        </div>
    </div>
   
}

