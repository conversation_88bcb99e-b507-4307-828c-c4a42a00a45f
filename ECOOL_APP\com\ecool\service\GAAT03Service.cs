﻿using AutoMapper;
using ECOOL_APP.com.ecool.ResultService;
using ECOOL_APP.com.ecool.util;
using ECOOL_APP.EF;
using EntityFramework.Extensions;
using EntityFramework.Utilities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Transactions;
using Dapper;
using log4net;
using System.Data.Entity.Validation;

namespace ECOOL_APP.com.ecool.service
{
    public class GAAT03Service
    {
        private static ILog logger = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        public GAAI01SysSetWearIndexViewModel GetSysSetWearData(GAAI01SysSetWearIndexViewModel model, string SCHOOL_NO, ref ECOOL_DEVEntities db)
        {
            using (var txn = Queries.GetNewReadUncommittedScope())
            {
                model =
                   (from a in db.GAAT03
                    where a.SCHOOL_NO == SCHOOL_NO
                    select new GAAI01SysSetWearIndexViewModel()
                    {
                        SCHOOL_NO = a.SCHOOL_NO,
                        ALARM_CASH = (short)a.ALARM_CASH,
                        ALARM_NOTICE = a.ALARM_NOTICE,
                    }).FirstOrDefault();

                //取出學年度及學期
                int SYear;
                int Semesters;
                SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);

                var gAAT01_S = db.GAAT01_S.Where(a => a.SCHOOL_NO == SCHOOL_NO && a.SYEAR == SYear && a.SEMESTER == Semesters).FirstOrDefault();

                if (gAAT01_S != null)
                {
                    if (model == null) model = new GAAI01SysSetWearIndexViewModel();

                    model.FIRST_DAY = gAAT01_S.FIRST_DAY;
                }

                return model;
            }
        }

        public IResult SaveSysSetWearData(GAAI01SysSetWearIndexViewModel model, UserProfile user, ref ECOOL_DEVEntities db)
        {
            IResult result = new Result(false);

            try
            {
                using (TransactionScope tx = new TransactionScope())
                {
                    try
                    {
                        //主檔
                        GAAT03 SaveUp = null;
                        SaveUp = db.GAAT03.Where(a => a.SCHOOL_NO == user.SCHOOL_NO).FirstOrDefault();

                        if (SaveUp == null)
                        {
                            SaveUp = new GAAT03();
                            SaveUp.SCHOOL_NO = user.SCHOOL_NO;
                            SaveUp.ALARM_CASH = model.ALARM_CASH;
                            SaveUp.ALARM_NOTICE = model.ALARM_NOTICE;
                            SaveUp.CHG_DATE = DateTime.Now;
                            SaveUp.CHG_PERSON = user?.USER_KEY;
                            db.GAAT03.Add(SaveUp);
                        }
                        else
                        {
                            SaveUp.ALARM_CASH = model.ALARM_CASH;
                            SaveUp.ALARM_NOTICE = model.ALARM_NOTICE;
                            SaveUp.CHG_DATE = DateTime.Now;
                            SaveUp.CHG_PERSON = user?.USER_KEY;
                            db.Entry(SaveUp).State = System.Data.Entity.EntityState.Modified;
                        }

                        if (user.ROLE_TYPE <= HRMT24_ENUM.RoleTypeVal.ExceedLevel && model.FIRST_DAY != null)
                        {
                            //取出學年度及學期
                            int SYear;
                            int Semesters;
                            SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);

                            GAAT01_S Gs = db.GAAT01_S.Where(x => x.SCHOOL_NO == SaveUp.SCHOOL_NO && x.SYEAR == SYear && x.SEMESTER == Semesters).FirstOrDefault();

                            //取出這週的星期一
                            DateTime input = (DateTime)model.FIRST_DAY;
                            int delta = DayOfWeek.Monday - input.DayOfWeek;
                            DateTime monday = input.AddDays(delta);

                            StringHelper stringHelper = new StringHelper();

                            //計算週期
                            List<GAAT01> gAAT01s = new List<GAAT01>();
                            for (int CYCLE = 1; CYCLE <= 20; CYCLE++)
                            {
                                GAAT01 T01 = new GAAT01();
                                T01.ALARM_ID = SYear.ToString() + Semesters.ToString() + stringHelper.StrRigth("00" + CYCLE.ToString(), 2);
                                T01.SYEAR = (byte)SYear;
                                T01.SEMESTER = (byte)Semesters;
                                T01.CYCLE = (byte)CYCLE;
                                T01.ALARM_DATES = monday; //星期一
                                T01.ALARM_DATEE = monday.AddDays(4); //星期5
                                T01.CHG_DATE = DateTime.Now;
                                T01.CHG_PERSON = user?.USER_KEY;
                                gAAT01s.Add(T01);
                                db.GAAT01.Add(T01);
                                monday = monday.AddDays(7);
                            }

                            if (gAAT01s?.Count > 0)
                            {
                                db.GAAT01.Where(x => x.SYEAR == SYear && x.SEMESTER == Semesters).Delete();
                               // EFBatchOperation.For(db, db.GAAT01).InsertAll(gAAT01s);
                            }

                            //塞這學期 每班人數及導師
                            db.GAAT01_C.Where(x => x.SYEAR == SYear && x.SEMESTER == Semesters).Delete();

                            string sSQL = $@"INSERT INTO GAAT01_C(SCHOOL_NO,SYEAR,SEMESTER ,CLASS_NO,GRADE ,STUDENT_NUMBER,CHG_PERSON ,CHG_DATE)
                                                SELECT A.SCHOOL_NO,@SYEAR,@SEMESTER,A.CLASS_NO,A.GRADE,COUNT(*) STUDENT_NUMBER,@CHG_PERSON,getdate()
                                                FROM HRMT01 A(NOLOCK)
                                                WHERE A.USER_STATUS != {UserStaus.Invalid} AND A.USER_TYPE = '{UserType.Student}' and ISNUMERIC(A.CLASS_NO)=1
                                                and
												SCHOOL_NO<>'034761'
                                                GROUP BY A.SCHOOL_NO,A.CLASS_NO,A.GRADE";
                            db.Database.Connection.Execute(sSQL, new
                            {
                                SYEAR = SYear,
                                SEMESTER = Semesters,
                                CHG_PERSON = user?.USER_KEY,
                            });

                            sSQL = @"Update GAAT01_C set  TEACHER_NAME = (SELECT top 1 H.NAME FROM HRMT03 T (NOLOCK)
                                        INNER JOIN HRMT01 H (NOLOCK) on T.SCHOOL_NO=H.SCHOOL_NO and T.TEACHER_NO=H.USER_NO
                                        Where T.CLASS_NO=A.CLASS_NO and T.SCHOOL_NO=A.SCHOOL_NO   )
                                        from GAAT01_C A
                                        WHERE A.SYEAR=@SYEAR and A.SEMESTER=@SEMESTER";
                            db.Database.Connection.Execute(sSQL, new
                            {
                                SYEAR = SYear,
                                SEMESTER = Semesters,
                            });

                            db.GAAT01_S.Where(x => x.SYEAR == SYear && x.SEMESTER == Semesters).Delete();

                            //塞這學期 , 學校班級數及 開學日
                            sSQL = @"INSERT INTO GAAT01_S
                                         (SCHOOL_NO,SYEAR,SEMESTER,FIRST_DAY,CLASS_COUNT ,STUDENT_NUMBER,CHG_PERSON,CHG_DATE)
                                         SELECT C.SCHOOL_NO,C.SYEAR,C.SEMESTER,@FIRST_DAY,COUNT(*),Sum(C.STUDENT_NUMBER),@CHG_PERSON,getdate()
                                         FROM GAAT01_C C
                                         WHERE C.SYEAR=@SYEAR AND C.SEMESTER=@SEMESTER
                                         GROUP BY C.SCHOOL_NO,C.SYEAR,C.SEMESTER";
                            db.Database.Connection.Execute(sSQL, new
                            {
                                SYEAR = SYear,
                                SEMESTER = Semesters,
                                CHG_PERSON = user?.USER_KEY,
                                FIRST_DAY = model.FIRST_DAY,
                            });
                        }

                        try
                        {
                            db.SaveChanges();
                        }
                        catch (DbEntityValidationException dbEx)
                        {
                            foreach (var validationErrors in dbEx.EntityValidationErrors)
                            {
                                foreach (var validationError in validationErrors.ValidationErrors)
                                {
                                    logger.Error(string.Format("Class: {0}, Property: {1}, Error: {2}", validationErrors.Entry.Entity.GetType().FullName,
                                    validationError.PropertyName,
                                    validationError.ErrorMessage));
                                }
                            }
                            result.Exception = dbEx;
                            result.Message += dbEx.Message;
                            return result;
                        }
                        catch (Exception ex)
                        {
                            logger.Error(ex);
                            result.Exception = ex;
                            result.Message += ex.Message;
                            return result;
                        }
                    }
                    catch (DbEntityValidationException dbEx)
                    {
                        foreach (var validationErrors in dbEx.EntityValidationErrors)
                        {
                            foreach (var validationError in validationErrors.ValidationErrors)
                            {
                                logger.Error(string.Format("Class: {0}, Property: {1}, Error: {2}", validationErrors.Entry.Entity.GetType().FullName,
                                validationError.PropertyName,
                                validationError.ErrorMessage));
                            }
                        }
                        result.Exception = dbEx;
                        result.Message += dbEx.Message;
                        return result;
                    }
                    catch (Exception ex)
                    {
                        logger.Error(ex);
                        result.Exception = ex;
                        result.Message += ex.Message;
                        return result;
                    }

                    tx.Complete();
                }

                result.Success = true;
            }
            catch (DbEntityValidationException dbEx)
            {
                foreach (var validationErrors in dbEx.EntityValidationErrors)
                {
                    foreach (var validationError in validationErrors.ValidationErrors)
                    {
                        logger.Error(string.Format("Class: {0}, Property: {1}, Error: {2}", validationErrors.Entry.Entity.GetType().FullName,
                        validationError.PropertyName,
                        validationError.ErrorMessage));
                    }
                }
                result.Exception = dbEx;
                result.Message += dbEx.Message;
                return result;
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                result.Exception = ex;
                result.Message += ex.Message;
                return result;
            }

            return result;
        }

        public GAAT03 GetGAAT03(string SCHOOL_NO, ref ECOOL_DEVEntities db)
        {
            return db.GAAT03.Where(x => x.SCHOOL_NO == SCHOOL_NO).FirstOrDefault();
        }
    }
}