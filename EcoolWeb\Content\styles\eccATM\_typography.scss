@use "sass:math";

$font-size-base: 18px;

@function toRem($fontpx) {
  @return math.div($fontpx, $font-size-base) * 1rem;
}

@mixin font($fontpx) {
  font-size: toRem($fontpx);

  @if $fontpx>=40px {
    line-height: 1.25;
  }

  @else if $fontpx>=16px {
    line-height: 1.5;
  }
}

html,
body,
h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  padding: 0;
}

body {
  font-size: $font-size-base;
}

h1 {
  @include font(40px);
  font-weight: bolder;
}

h2 {
  @include font(32px);
  font-weight: bold;
}

h3 {
  @include font(28px);
}

h4 {
  @include font(24px);
  margin-bottom: 0.2rem;
}

h5 {
  @include font(20px);
  margin-bottom: 4px;
}

h6 {
  @include font(16px);
}

.font-size-s {
  font-size: 16px;
}

// .font-color {
//   color: $font-color;
// }

.text-indent-1 {
  text-indent: 1rem;
}

.text-gray-500 {
  color: $gray-500;
}

.text-PC-biger {
  font-weight: bold;

  @include media-breakpoint-up(md) {
    font-size: 1.5rem;
  }
}