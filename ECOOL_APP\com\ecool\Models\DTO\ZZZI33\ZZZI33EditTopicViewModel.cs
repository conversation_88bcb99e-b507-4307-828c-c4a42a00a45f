﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.IO;
using System.Linq;
using System.Text;
using System.Web.Mvc;

namespace ECOOL_APP.EF
{
    public class ZZZI33EditTopicViewModel
    {
        public bool ShowBtn { get; set; }
        public bool isCopy { get; set; }

        public string Html_ID { get; set; }

        /// <summary>
        ///投票ID
        /// </summary>
        [DisplayName("投票ID")]
        public string QUESTIONNAIRE_ID { get; set; }

        /// <summary>
        ///投票題目序號
        /// </summary>
        [DisplayName("序號")]
        public int? Q_NUM { get; set; }

        /// <summary>
        ///投票題目描述
        /// </summary>
        [DisplayName("題目")]
        [UIHint("Html")]
        [AllowHtml]
        public string Q_SUBJECT { get; set; }

        /// <summary>
        ///投票題目排序
        /// </summary>
        [DisplayName("排序")]
        public int? Q_ORDER_BY { get; set; }

        /// <summary>
        ///必填與否
        /// </summary>
        [DisplayName("必填與否")]
        public byte? Q_MUST { get; set; }

        public bool MUST { get; set; }

        [DisplayName("索引鍵")]
        public byte? Q_KEY { get; set; }

        public bool KEY { get; set; }

        /// <summary>
        ///類別
        /// </summary>
        [DisplayName("類別")]
        public byte? Q_TYPE { get; set; }

        /// <summary>
        ///類別
        /// </summary>
        [DisplayName("Input類別")]
        public string INPUT_TYPE { get; set; }

        /// <summary>
        ///說明(給設計人看的)
        /// </summary>
        [DisplayName("說明(給設計人看的)")]
        public string Q_MEMO { get; set; }

        /// <summary>
        ///複選題時，需勾選的筆數(沒填時，不限制)
        /// </summary>
        [DisplayName("複選必需勾選的筆數")]
        public byte? Q_MUTIPLE_CHOICES_OF_NUM { get; set; }

        /// <summary>
        /// Input 設定檔
        /// </summary>
        public List<ZZZI33EditTopic_DViewModel> Topic_D { get; set; }

        public ZZZI33EditTopicViewModel()
        {
            ShowBtn = true;
        }
    }
}