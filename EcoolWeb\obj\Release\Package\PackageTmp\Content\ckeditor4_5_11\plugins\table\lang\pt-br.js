﻿/*
Copyright (c) 2003-2016, CKSource <PERSON> <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'table', 'pt-br', {
	border: 'Borda',
	caption: '<PERSON><PERSON>',
	cell: {
		menu: 'Célula',
		insertBefore: 'Inserir célula a esquerda',
		insertAfter: 'Inserir célula a direita',
		deleteCell: 'Remover Células',
		merge: 'Mesclar Células',
		mergeRight: 'Mesclar com célula a direita',
		mergeDown: 'Mesclar com célula abaixo',
		splitHorizontal: 'Dividir célula horizontalmente',
		splitVertical: 'Dividir célula verticalmente',
		title: 'Propriedades da célula',
		cellType: 'Tipo de célula',
		rowSpan: 'Linhas cobertas',
		colSpan: 'Colunas cobertas',
		wordWrap: 'Quebra de palavra',
		hAlign: 'Alinhamento horizontal',
		vAlign: 'Alinhamento vertical',
		alignBaseline: '<PERSON><PERSON><PERSON> de alinhamento',
		bgColor: 'Cor de fundo',
		borderColor: 'Cor das bordas',
		data: 'Dados',
		header: 'Cabeçalho',
		yes: 'Sim',
		no: 'Não',
		invalidWidth: 'A largura da célula tem que ser um número.',
		invalidHeight: 'A altura da célula tem que ser um número.',
		invalidRowSpan: 'Linhas cobertas tem que ser um número inteiro.',
		invalidColSpan: 'Colunas cobertas tem que ser um número inteiro.',
		chooseColor: 'Escolher'
	},
	cellPad: 'Margem interna',
	cellSpace: 'Espaçamento',
	column: {
		menu: 'Coluna',
		insertBefore: 'Inserir coluna a esquerda',
		insertAfter: 'Inserir coluna a direita',
		deleteColumn: 'Remover Colunas'
	},
	columns: 'Colunas',
	deleteTable: 'Apagar Tabela',
	headers: 'Cabeçalho',
	headersBoth: 'Ambos',
	headersColumn: 'Primeira coluna',
	headersNone: 'Nenhum',
	headersRow: 'Primeira linha',
	invalidBorder: 'O tamanho da borda tem que ser um número.',
	invalidCellPadding: 'A margem interna das células tem que ser um número.',
	invalidCellSpacing: 'O espaçamento das células tem que ser um número.',
	invalidCols: 'O número de colunas tem que ser um número maior que 0.',
	invalidHeight: 'A altura da tabela tem que ser um número.',
	invalidRows: 'O número de linhas tem que ser um número maior que 0.',
	invalidWidth: 'A largura da tabela tem que ser um número.',
	menu: 'Formatar Tabela',
	row: {
		menu: 'Linha',
		insertBefore: 'Inserir linha acima',
		insertAfter: 'Inserir linha abaixo',
		deleteRow: 'Remover Linhas'
	},
	rows: 'Linhas',
	summary: 'Resumo',
	title: 'Formatar Tabela',
	toolbar: 'Tabela',
	widthPc: '%',
	widthPx: 'pixels',
	widthUnit: 'unidade largura'
} );
