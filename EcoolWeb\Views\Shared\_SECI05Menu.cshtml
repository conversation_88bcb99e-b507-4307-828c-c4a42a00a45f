﻿@model int

@{
    int activeIDNumber = Model;

    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

}

<a href="@Url.Action("Index","SECI05")" class="btn btn-sm btn-bold btn-pink @(activeIDNumber==0? "active":"")">學生閱讀狀況</a>
<a href="@Url.Action("booksorder","SECI02")" class="btn btn-sm btn-bold btn-pink @(activeIDNumber==1? "active":"")">借閱排行</a>
@if (user.USER_TYPE == UserType.Admin || user.USER_TYPE == UserType.Teacher)
{
    if (user.USER_TYPE == UserType.Admin)
    {
        <a href="@Url.Action("BorrowIndex","SECI02", new { from = "SEI05" })" class="btn btn-sm btn-bold btn-pink @(activeIDNumber==2? "active":"")">借書統計</a>
        <a class="btn btn-sm btn-bold btn-pink"
           data-html="true" data-toggle="popover" data-container="body" data-placement="bottom" title="高關懷清單"
           data-content='<a href="@Url.Action("CareIndex","SECI02", new { from = "SEI05" })" class="btn btn-sm btn-bold btn-pink colorbox">關懷清單(登入)</a><a href="@Url.Action("CareBookIndex","SECI02", new { from = "SEI05" })" class="btn btn-sm btn-bold btn-pink colorbox">關懷清單(借書)</a>'>
            高關懷
        </a>
    }
    <a href="@Url.Action("_EncourageDiv","SECI05")" class="btn btn-sm btn-bold btn-pink colorbox @(activeIDNumber==3? "active":"")">加油名單</a>
    <a href="@Url.Action("_ReadingPartialEclipseDiv","SECI05")" class="btn btn-sm btn-bold btn-pink colorbox @(activeIDNumber==4? "active":"")">疑似偏食名單</a>
    <a href="@Url.Action("_UnusualListDiv","SECI05")" class="btn btn-sm btn-bold btn-pink colorbox @(activeIDNumber==5? "active":"")">異常名單</a>
}
<br /><br />

<script>
    $(function () {
        // Enables popover
        $("[data-toggle=popover]").popover();
    });
</script>