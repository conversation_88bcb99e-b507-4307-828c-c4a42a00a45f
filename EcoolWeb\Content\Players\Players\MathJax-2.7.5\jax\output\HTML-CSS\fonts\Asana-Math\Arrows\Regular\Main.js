/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Asana-Math/Arrows/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.AsanaMathJax_Arrows={directory:"Arrows/Regular",family:"AsanaMathJax_Arrows",testString:"\u219C\u219D\u219F\u21A1\u21A4\u21A5\u21A7\u21A8\u21AF\u21B2\u21B3\u21B4\u21B5\u21B8\u21B9",32:[0,0,249,0,0],8604:[486,-55,1061,62,1062],8605:[486,-55,1061,-3,997],8607:[712,172,524,47,478],8609:[712,172,524,47,478],8612:[486,-55,1013,36,978],8613:[742,200,524,47,478],8615:[742,200,524,47,478],8616:[712,200,524,47,480],8623:[476,222,524,20,504],8626:[753,0,506,65,442],8627:[753,0,506,65,442],8628:[565,-140,714,24,691],8629:[686,-19,524,50,475],8632:[785,172,1013,64,950],8633:[688,-20,899,51,849],8645:[712,172,883,48,836],8662:[724,246,1101,65,1037],8663:[724,246,1101,65,1037],8664:[724,246,1101,65,1037],8665:[724,246,1101,65,1037],8668:[485,-54,1149,34,1116],8670:[712,172,524,47,478],8671:[712,172,524,47,478],8673:[737,196,524,47,478],8675:[737,196,524,47,478],8676:[499,-31,899,51,849],8677:[500,-32,899,51,849],8678:[554,12,1013,64,950],8679:[713,172,678,56,622],8680:[554,12,1013,64,950],8681:[713,172,678,56,622],8682:[713,172,678,56,622],8683:[713,199,678,56,622],8684:[713,199,678,56,622],8685:[713,200,678,48,631],8686:[856,172,678,56,622],8687:[834,199,678,56,622],8688:[555,11,989,39,951],8689:[750,208,1019,25,988],8690:[750,208,1019,25,988],8691:[713,172,678,56,622],8692:[504,-33,1089,27,1063],8693:[712,172,883,48,836],8694:[845,305,1013,65,949],8695:[487,-56,1013,65,949],8696:[486,-55,1013,65,949],8697:[486,-55,1013,65,949],8698:[486,-55,913,72,842],8699:[486,-55,913,72,842],8700:[488,-57,1013,65,949],8701:[509,-38,1013,65,949],8702:[509,-38,1013,65,949],8703:[509,-38,1013,53,961],10224:[713,173,1013,130,884],10225:[713,173,1013,130,884],10226:[759,0,987,73,929],10227:[759,0,987,72,929],10228:[524,-17,1013,65,949],10235:[486,-55,1513,36,1478],10237:[537,-5,1513,65,1449],10238:[537,-5,1513,65,1449],10239:[486,-55,1513,38,1476],10496:[486,-55,1013,65,949],10497:[486,-55,1013,65,949],10498:[537,-6,1013,65,949],10499:[537,-6,1013,65,949],10500:[537,-6,1013,59,954],10501:[486,-55,1013,65,949],10502:[537,-5,1013,65,949],10503:[537,-5,1013,65,949],10504:[712,172,559,65,495],10505:[712,172,559,65,495],10506:[712,172,803,65,739],10507:[713,171,803,65,739],10508:[486,-55,1013,65,949],10509:[486,-55,1013,65,949],10510:[486,-55,1013,65,949],10511:[486,-55,1013,65,949],10512:[486,-55,1150,27,1124],10513:[486,-55,1211,63,1147],10514:[667,131,559,87,473],10515:[667,131,559,87,473],10516:[489,-58,1150,28,1123],10517:[486,-55,1150,86,1066],10518:[486,-55,1150,28,1122],10519:[486,-55,1150,28,1123],10520:[486,-55,1150,28,1123],10521:[486,-55,1009,57,953],10522:[486,-55,1009,57,953],10523:[486,-55,1059,65,996],10524:[486,-55,1059,65,996],10525:[488,-57,1369,66,1304],10526:[488,-57,1369,66,1304],10527:[490,-59,1426,66,1362],10528:[490,-59,1426,66,1362],10529:[715,173,1013,63,951],10530:[715,173,1013,63,951],10531:[803,175,1013,76,938],10532:[803,175,1013,76,939],10533:[802,176,1013,76,939],10534:[802,176,1013,76,938],10535:[713,172,1013,65,949],10536:[712,172,1013,64,950],10537:[713,172,1013,65,949],10538:[712,172,1013,64,950],10539:[648,172,1013,65,949],10540:[648,172,1013,65,949],10541:[713,172,1013,64,950],10542:[712,172,1013,65,950],10543:[712,172,1013,65,950],10544:[713,172,1013,65,950],10545:[714,172,1013,64,950],10546:[714,172,1013,64,950],10547:[484,-53,961,-3,902],10548:[585,-76,729,54,676],10549:[588,-80,729,54,676],10550:[622,0,729,111,622],10551:[622,0,729,110,619],10552:[643,117,559,105,454],10553:[643,117,559,105,455],10554:[446,-97,869,55,815],10555:[447,-97,869,55,815],10556:[446,-97,869,55,815],10557:[446,-95,869,55,815],10558:[580,62,887,63,825],10559:[580,62,887,62,825],10560:[698,164,987,121,885],10561:[702,160,987,120,883],10562:[665,124,1013,65,949],10563:[665,124,1013,65,949],10564:[665,124,1013,65,949],10565:[665,-98,1013,65,949],10566:[665,-98,1013,65,949],10567:[486,-55,1013,65,949],10568:[504,-33,1260,54,1207],10569:[761,219,541,56,486],10570:[381,52,1013,65,949],10571:[381,52,1013,65,949],10572:[713,171,559,63,497],10573:[713,171,559,63,497],10574:[194,52,1013,65,949],10575:[713,171,459,107,353],10576:[194,52,1013,65,949],10577:[713,171,459,107,353],10578:[489,-21,899,51,849],10579:[489,-21,899,51,849],10580:[667,131,559,46,514],10581:[667,131,559,46,514],10582:[496,-28,899,51,849],10583:[489,-21,899,51,849],10584:[667,131,559,46,514],10585:[667,131,559,46,514],10586:[486,-55,1013,29,985],10587:[486,-55,1013,29,985],10588:[761,195,524,47,478],10589:[761,195,524,47,478],10590:[486,-55,1013,29,985],10591:[486,-55,1013,29,985],10592:[761,195,524,47,478],10593:[761,195,524,47,478],10594:[594,52,1013,65,949],10595:[713,171,759,57,703],10596:[594,52,1013,65,949],10597:[713,171,759,57,703],10598:[594,52,1013,65,949],10599:[560,19,1013,65,949],10600:[627,19,1013,65,949],10601:[627,19,1013,65,949],10602:[594,-135,1013,65,949],10603:[594,-135,1013,65,949],10604:[594,-135,1013,65,949],10605:[594,-135,1013,65,949],10606:[713,171,759,57,703],10607:[713,171,759,57,703],10608:[407,-140,1013,51,963],10609:[613,-41,1013,65,949],10610:[486,-55,1013,65,949],10611:[486,-55,1013,65,949],10612:[486,-55,1013,65,949],10613:[486,136,1013,65,949],10614:[695,154,1013,65,949],10615:[535,-7,1013,65,960],10616:[695,154,1013,65,949],10617:[723,179,1013,65,949],10618:[535,-10,1013,65,957],10619:[723,179,1013,65,949],10620:[576,35,803,65,739],10621:[576,35,803,65,739],10622:[608,66,743,66,678],10623:[608,66,743,66,678]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"AsanaMathJax_Arrows"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Arrows/Regular/Main.js"]);
