/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/General/Bold/GeneralPunctuation.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXGeneral-bold"],{8208:[287,-171,333,44,287],8209:[287,-171,333,44,287],8210:[287,-171,500,0,500],8211:[271,-181,500,0,500],8212:[271,-181,1000,0,1000],8213:[271,-181,2000,0,2000],8215:[-137,287,520,10,510],8216:[691,-356,333,70,254],8217:[691,-356,333,79,263],8218:[155,180,333,79,263],8219:[691,-356,333,79,263],8220:[691,-356,500,32,486],8221:[691,-356,500,14,468],8222:[155,180,500,14,468],8223:[691,-356,500,14,468],8224:[691,134,500,47,453],8225:[691,132,500,45,456],8226:[462,-42,560,70,490],8229:[156,13,666,82,584],8230:[156,13,1000,82,917],8240:[706,29,1110,61,1049],8241:[706,29,1472,61,1411],8242:[713,-438,310,75,235],8243:[713,-438,467,75,392],8244:[713,-438,625,75,550],8245:[713,-438,310,75,235],8246:[713,-438,467,75,392],8247:[713,-438,625,75,550],8248:[117,170,584,91,497],8249:[415,-36,333,51,305],8250:[415,-36,333,28,282],8252:[691,13,625,81,544],8254:[838,-766,500,0,500],8256:[725,-508,798,79,733],8260:[688,12,183,-168,345],8263:[689,13,947,57,892],8270:[236,200,500,56,448],8271:[472,180,333,67,251],8273:[706,200,500,56,448],8279:[713,-438,783,75,708]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/Bold/GeneralPunctuation.js");
