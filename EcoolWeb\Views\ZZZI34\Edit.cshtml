﻿@model ZZZI34EditViewModel
@using EcoolWeb.Util;
@using ECOOL_APP.com.ecool.service
@{
    ViewBag.Title = ViewBag.Panel_Title;
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

    int ShowBtn = ZZZI34EditViewModel.ShowBtnVal.False;

    bool IsAdmin = ViewBag.IsAdmin ?? false;

    if (Model.Main?.ART_GALLERY_NO == null)
    {
        ShowBtn = ZZZI34EditViewModel.ShowBtnVal.True;
    }
    else
    {
        string str = com.ecool.service.PermissionService.GetPermission_Use_YN("ZZZI34", "Edit", user.SCHOOL_NO, user.USER_NO);

        if (user != null)
        {
            if (IsAdmin || str=="Y")
            {
                if (Model.SaveType == ZZZI34EditViewModel.SaveTypeVal.Save)
                {
                    if (Model.Main.STATUS == ADDT21.STATUSVal.Pass)
                    {
                        ShowBtn = ZZZI34EditViewModel.ShowBtnVal.NotCash;
                    }
                    else
                    {
                        ShowBtn = ZZZI34EditViewModel.ShowBtnVal.True;
                    }
                }
                else
                {
                    ShowBtn = ZZZI34EditViewModel.ShowBtnVal.Verify;
                }

            }
            else if ((Model.Main.STATUS == ADDT21.STATUSVal.NotStarted || Model.Main.STATUS == ADDT21.STATUSVal.Verification)
                && Model.Main.CRE_PERSON == user.USER_KEY)
            {
                ShowBtn = ZZZI34EditViewModel.ShowBtnVal.True;
            }
            else if (Model.Main.STATUS == ADDT21.STATUSVal.Verification && (Model.Main.VERIFIER == user.USER_NO) || (ViewBag.VisibleVerify))
            {
                ShowBtn = ShowBtn = ZZZI34EditViewModel.ShowBtnVal.Verify;
            }
        }

        if (Model.Main.STATUS == ADDT21.STATUSVal.Disabled)
        {
            ShowBtn = ZZZI34EditViewModel.ShowBtnVal.False;
        }
    }

    ViewBag.ShowBtn = ShowBtn;

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }

}

@* 燈箱套件需要 *@

<link href="~/Content/colorbox/example2/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>
<link href="~/Content/css/EzCss.min.css" rel="stylesheet" />
@Html.Partial("_Title_Secondary")
@Html.Partial("_Alert")

<div id="Msg" class="alert alert-dismissible alert-danger d-none" >
    <img src="@Url.Content("~/Content/img/Warning.png")" class="msg-img" alt="" />
    @Html.ValidationMessage("Error", "", new { @class = "text-danger" })
</div>

@if (Model.Main?.STATUS == null)
{
    Html.RenderAction("_ArtGalleryMenu", new { NowAction = "Edit" });
}
else
{
    Html.RenderAction("_ArtGalleryMenu", new { NowAction = "" });
}

@using (Html.BeginForm("Edit", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "formEdit", name = "formEdit", enctype = "multipart/form-data" }))
{

    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })

    @Html.HiddenFor(m => m.SaveType)

    @Html.HiddenFor(m => m.Search.OrdercColumn)
    @Html.HiddenFor(m => m.Search.SyntaxName)
    @Html.HiddenFor(m => m.Search.Page)
    @Html.HiddenFor(m => m.Search.WhereSCHOOL_NO)
    @Html.HiddenFor(m => m.Search.WhereUSER_NO)
    @Html.HiddenFor(m => m.Search.WhereART_GALLERY_NO)
    @Html.HiddenFor(m => m.Search.WhereSTATUS)
    @Html.HiddenFor(m => m.Search.WhereART_GALLERY_TYPE)
    @Html.HiddenFor(m => m.Search.WhereMyWork)
    @Html.HiddenFor(m => m.IsTempSave)

    @Html.HiddenFor(m => m.Main.ART_GALLERY_NO)
    @Html.HiddenFor(m => m.Main.ART_GALLERY_TYPE)
    @Html.HiddenFor(m => m.Main.SCHOOL_NO)
    @Html.HiddenFor(m => m.Main.STATUS)
    @Html.HiddenFor(m => m.Main.VERIFIER)
    @Html.HiddenFor(m => m.Main.CRE_PERSON)
    string ImgUrl = "";
    if (Model.Main != null)
    {
        string NewImg = Model.Main.COVER_FILE;
        ImgUrl = new ZZZI34Service().GetDirectorySysArtGalleryPath(Model.Main.SCHOOL_NO, Model.Main.ART_GALLERY_NO, NewImg);
    }
    <div class="ribbon ribbon-online-gallery-pink">
        <img class="ribbon-img" src="~/Content/images/ribbon-img-gallery.png" alt="">
        <strong class="ribbon-title">
            線上藝廊
        </strong>
    </div>
    <div class="ribbon-content ribbon-content-edit ribbon-online-gallery-pink-bg">
        <fieldset @(ViewBag.ShowBtn != ZZZI34EditViewModel.ShowBtnVal.True && ViewBag.ShowBtn != ZZZI34EditViewModel.ShowBtnVal.NotCash ? "disabled" : "")>
            <div class="form-horizontal">
                <div class="d-block mb-3 mx-0 mx-md-5">
                    @if (user.USER_TYPE == UserType.Teacher || user.USER_TYPE == UserType.Admin && ViewBag.ISTASKLIST != "True")
                    {
                        <a role="button" href='@Url.Action("Index2",(string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys mb-3 @(ViewBag.NowAction=="Edit" ? "active":"")">
                            批次線上藝廊
                        </a>
                    }
                    <br />
                    <strong class="Caption_Div mx-0">@ViewBag.Panel_Title</strong>
                </div>
                <div class="form-group">
                    <label class="col-md-3 control-label">
                        <font color="red">*</font>
                        @Html.LabelFor(m => m.Main.ART_SUBJECT)
                    </label>
                    <div class="col-md-9">
                        @Html.EditorFor(m => m.Main.ART_SUBJECT, new { htmlAttributes = new { @class = "form-control input-md", @placeholder = Html.DisplayNameFor(n => n.Main.ART_SUBJECT) } })
                        @Html.ValidationMessageFor(m => m.Main.ART_SUBJECT, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-3 control-label">
                        @*<font color="red">*</font>*@
                        @Html.LabelFor(m => m.Main.ART_DESC)
                    </label>
                    <div class="col-md-9">
                        @Html.TextAreaFor(m => m.Main.ART_DESC, 2, 100, new { @class = "form-control", @placeholder = Html.DisplayNameFor(n => n.Main.ART_DESC) })
                        @*@Html.ValidationMessageFor(m => m.Main.ART_DESC, "", new { @class = "text-danger" })*@
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-3 control-label">
                        <font color="red">*</font>
                        @Html.LabelFor(m => m.Main.COVER_FILE)
                    </label>
                    <div class="col-md-9">
                        @Html.HiddenFor(m => m.Main.COVER_FILE)
                        @Html.ValidationMessageFor(m => m.Main.COVER_FILE, "", new { @class = "text-danger" })
                        @if (!string.IsNullOrWhiteSpace(Model.Main?.COVER_FILE))
                        {
                            <div id="DivCOVER_FILE">
                                @if (ViewBag.ShowBtn == ZZZI34EditViewModel.ShowBtnVal.True || ViewBag.ShowBtn == ZZZI34EditViewModel.ShowBtnVal.NotCash)
                                {
                                    <a role='button' onclick="DelFile('@Model.Main.ART_GALLERY_NO','','','')"> <i class='glyphicon glyphicon-remove'></i></a>
                                }

                                @{
                                    string Url = new ZZZI34Service().GetDirectorySysArtGalleryPath(Model.Main.SCHOOL_NO, Model.Main.ART_GALLERY_NO, Model.Main.COVER_FILE);
                     
                                
                                                <img src="@Url?v=@DateTime.Now.ToString()" class="img-responsive colorbox" alt="Responsive image" href="@Url" style="max-width:300px" id="ImageA1" />
                                    <div style="padding-left:15%;padding-top:2%;">
                                        @Html.Action("_imagesRotateBtnView", "Comm", new { ImgURL = ImgUrl, ImgURL_S = "", ImgUrl_M = "", ImgID = "ImageA1" })
                                    </div>
                                }
                            </div>
                            <div id="DivUploadCoverFile" style="display:none">
                                @Html.TextBoxFor(m => m.Main.UploadCoverFile, new { @class = "form-control input-md", @type = "file" })
                                @Html.ValidationMessageFor(m => m.Main.UploadCoverFile, "", new { @class = "text-danger" })
                                <br />
                                <label class="text-info">PS.請上傳 jpg、jpeg、png、gif、bmp 等格式的圖片</label>
                            </div>
                        }
                        else
                        {
                            <div>
                                @Html.TextBoxFor(m => m.Main.UploadCoverFile, new { @class = "form-control input-md", @type = "file" })
                                @Html.ValidationMessageFor(m => m.Main.UploadCoverFile, "", new { @class = "text-danger" })
                                <br />
                                <label class="text-info">PS.請上傳 jpg、jpeg、png、gif、bmp 等格式的圖片</label>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </fieldset>
    </div>
    <div class="my-3 text-center">
        <span class="glyphicon glyphicon-circle-arrow-down fa-2x text-ribbon" aria-hidden="true"></span>
    </div>
    <div class="ribbon-content ribbon-content-edit ribbon-online-gallery-pink-bg">
        <div class="form-horizontal">
            <strong class="Caption_Div d-block mb-3 mx-0 mx-md-5">
                選擇檔案類別(因為伺服器空間不足，原本可以上傳影片，現在都改上傳到Youtube)
            </strong>
            <fieldset id="fieldset_WORK_TYPE" @(string.IsNullOrWhiteSpace(Model.Main.ART_GALLERY_NO) == false ? "disabled" : "")>
                <div class="form-group">
                    <label class="col-md-3 control-label">
                        <font color="red">*</font>
                        @Html.LabelFor(m => m.Main.WORK_TYPE)
                    </label>
                    <div class="col-md-9">
                        @Html.EditorFor(m => m.Main.WORK_TYPE,
                            "_RadioButtonList",
                            new
                            {
                                TagName = (Html.NameFor(m => m.Main.WORK_TYPE)).ToHtmlString(),
                                RadioItems = ADDT21.WORK_TYPE_VAL.SelectItem(Model.Main.WORK_TYPE != null ? Model.Main.WORK_TYPE : ADDT21.WORK_TYPE_VAL.photo),
                                Position = Position.Horizontal,
                                Numbers = int.MaxValue,
                                onclick = "OnclickWorkType(this.value)",
                            })
                        @Html.ValidationMessageFor(m => m.Main.WORK_TYPE, "", new { @class = "text-danger" })
                    </div>
                </div>
            </fieldset>
            @*<div class="form-group">

                    <label class="text-info">
                        說明:<br>
                        <span id="PsPhoto" style="color:red;display:none">選擇@(ADDT21.WORK_TYPE_VAL.GetDesc(ADDT21.WORK_TYPE_VAL.photo))，請上傳 jpg、jpeg、png、gif、bmp 等格式的圖片。<br></span>
                        <span id="PsVideo" style="display:none">
                            1.選擇@(ADDT21.WORK_TYPE_VAL.GetDesc(ADDT21.WORK_TYPE_VAL.video))，請上傳<font color="red">mp4</font>等格式的影片，請勿把副檔名改成 mp4，請使用轉檔軟體。<br>
                            2.建議總檔案大小不要超過<font color="red">20M</font>。<br />
                            *3.若檔案太大，<font color="red">請上傳到youtube，並改用第三個功能</font>。<br />
                        </span>
                        <span id="PsYoutube" style="color:red;display:none">選擇@(ADDT21.WORK_TYPE_VAL.GetDesc(ADDT21.WORK_TYPE_VAL.Youtube))，請輸入 有效Youtube 網址 ，https://www.youtube.com/watch?v=影片id，影片權限請公開 。<br></span>
                    </label>
                    <br />
                    <br />
                </div>*@
        </div>
    </div>

    if ((Model.Main.ART_GALLERY_TYPE == ADDT21.ART_GALLERY_TYPE_VAL.Campus && ShowBtn == ZZZI34EditViewModel.ShowBtnVal.True)
    || (Model.Main.ART_GALLERY_TYPE == ADDT21.ART_GALLERY_TYPE_VAL.Personal && ShowBtn == ZZZI34EditViewModel.ShowBtnVal.Verify))
    {
        <div class="my-3 text-center">
            <span class="glyphicon glyphicon-circle-arrow-down fa-2x text-ribbon" aria-hidden="true"></span>
        </div>
        <div class="ribbon-content ribbon-content-edit ribbon-online-gallery-pink-bg">
            <div class="form-horizontal">
                <div class="row">
                    <div class="col-md-3">
                        <strong class="Caption_Div d-block mb-3 ml-0 ml-md-5">
                            批次給分的貼心工具
                        </strong>
                    </div>
                    <div class="col-md-9">
                        <div class="input-group my-3">
                            @Html.Editor("CASH", new { htmlAttributes = new { @class = "form-control input-sm", @placeholder = "0~10" } })
                            <span class="input-group-btn">
                                <button class="btn btn-default btn-sm" type="button" onclick="AutoCash($('#CASH').val())">全部自動帶入這個酷幣值</button>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }

    <div class="my-3 text-center">
        <span class="glyphicon glyphicon-circle-arrow-down fa-2x text-ribbon" aria-hidden="true"></span>
    </div>
    <div class="ribbon-content ribbon-content-edit ribbon-online-gallery-pink-bg">

        @Html.ValidationMessage("People", "", new { @class = "text-danger" })
        <strong class="Caption_Div d-block mb-3 mx-0 mx-md-5">
            <font color="red">*</font>上傳作品
        </strong>

        <div id="editorRows">

            @if (Model.People != null)
            {
                List<string> itemlis = Model.People.Select(x => x.PHOTO_USER_NO).Distinct().ToList();

                foreach (var item1 in itemlis)
                {
                    ZZZI34EditPeopleViewModel item = Model.People.Where(x => x.PHOTO_USER_NO == item1).FirstOrDefault();


                    item.ShowBtn = ViewBag.ShowBtn;
                    @Html.Partial("_People", item)

                }
            }
            else
            {
                if (Model.Main.ART_GALLERY_TYPE == ADDT21.ART_GALLERY_TYPE_VAL.Personal)
                {
                    ZZZI34EditPeopleViewModel item = new ZZZI34EditPeopleViewModel();
                    item.ShowBtn = ViewBag.ShowBtn;
                    item.ART_GALLERY_TYPE = Model.Main.ART_GALLERY_TYPE;
                    if (user != null)
                    {
                        item.PHOTO_SCHOOL_NO = user.SCHOOL_NO;
                        item.PHOTO_USER_NO = user.USER_NO;
                        item.PHOTO_CLASS_NO = user.CLASS_NO;
                    }
                    @Html.Partial("_People", item)
                }
            }
        </div>

        <div class="row">
            <div class="col-md-12 col-xs-12 text-right">
                <span class="input-group-btn">
                    @if (ViewBag.ShowBtn == ZZZI34EditViewModel.ShowBtnVal.True)
                    {
                        if (Model.Main.ART_GALLERY_TYPE == ADDT21.ART_GALLERY_TYPE_VAL.Campus)
                        {
                            <button class="btn2 btn-default btn-sm" type="button" onclick="onAddItem()">
                                增加作者
                            </button>
                        }
                    }
                </span>
            </div>
        </div>
        @if (Model.Main.STATUS != null && Model.Main.STATUS != ADDT21.STATUSVal.NotStarted && Model.Main.STATUS != ADDT21.STATUSVal.Disabled)
        {
            <div style="height:25px"></div>
            <div class="form-group">
                @Html.LabelFor(m => m.Main.BACK_MEMO, htmlAttributes: new { @class = "col-md-2 control-label" })
                <div class="col-md-10">
                    @Html.EditorFor(m => m.Main.BACK_MEMO, new { htmlAttributes = new { @class = "form-control input-md", @placeholder = Html.DisplayNameFor(n => n.Main.BACK_MEMO) } })
                    @Html.ValidationMessageFor(m => m.Main.BACK_MEMO, "", new { @class = "text-danger" })
                </div>
            </div>
        }

    </div>

    <div style="height:25px"></div>
    <div class="text-center">

        @if (Model.Main.STATUS == null)
        {
            if (user != null)
            {
                if (Model.Main.ART_GALLERY_TYPE == ADDT21.ART_GALLERY_TYPE_VAL.Campus)
                {
                    <button id="BtnSave" class="btn2 btn-default" type="button" onclick="onSave(false,'')">儲存</button>
                }
                else
                {
                    <button id="BtnSave" class="btn2 btn-default" type="button" onclick="onSave(false)">送審</button>
                }
            }
        }

        @if (!string.IsNullOrWhiteSpace(Model.Main.ART_GALLERY_NO))
        {
            string str = com.ecool.service.PermissionService.GetPermission_Use_YN("ZZZI34", "Edit", user.SCHOOL_NO, user.USER_NO);

            if (Model.Main.ART_GALLERY_TYPE == ADDT21.ART_GALLERY_TYPE_VAL.Campus)
            {

                if (Model.Main.STATUS == ADDT21.STATUSVal.NotStarted && Model.Main.CRE_PERSON == user.USER_KEY )
                {
                    <button id="BtnSave" class="btn2 btn-default" type="button" onclick="onSave(false)">儲存</button>
                    <a role="button" class="btn2 btn-default" onclick="onUpdateStatus('@ZZZI34EditViewModel.SaveTypeVal.Verify')">發佈</a>
                    <a role="button" class="btn2 btn-default" onclick="onUpdateStatus('@ZZZI34EditViewModel.SaveTypeVal.Disabled')">作廢</a>
                }
                else if (Model.Main.STATUS == ADDT21.STATUSVal.Pass && (IsAdmin|| str=="Y"))
                {
                    <button id="BtnSave" class="btn2 btn-default" type="button" onclick="onSave(false)">儲存</button>
                    <a role="button" class="btn2 btn-default" onclick="onUpdateStatus('@ZZZI34EditViewModel.SaveTypeVal.Disabled')">作廢</a>
                }
            }
            else if (Model.Main.ART_GALLERY_TYPE == ADDT21.ART_GALLERY_TYPE_VAL.Personal)
            {
                if (Model.Main.STATUS == ADDT21.STATUSVal.NotStarted && Model.Main.CRE_PERSON == user.USER_KEY)
                {
                    <button id="BtnSave" class="btn2 btn-default" type="button" onclick="onSave(false)">確定修改</button>
                    <a role="button" class="btn2 btn-default" onclick="onUpdateStatus('@ZZZI34EditViewModel.SaveTypeVal.Release')">送審</a>
                    <a role="button" class="btn2 btn-default" onclick="onUpdateStatus('@ZZZI34EditViewModel.SaveTypeVal.Disabled')">作廢</a>
                }
                else if (Model.Main.STATUS == ADDT21.STATUSVal.Verification
                    && (Model.Main.VERIFIER == user.USER_NO ||  (IsAdmin || str == "Y") || Model.Main.CRE_PERSON == user.USER_KEY || ViewBag.VisibleVerify))
                {
                    if ((IsAdmin || Model.Main.CRE_PERSON == user.USER_KEY) && ShowBtn == ZZZI34EditViewModel.ShowBtnVal.True)
                    {
                        <button id="BtnSave" class="btn2 btn-default" type="button" onclick="onSave(false)">確定修改</button>
                    }
                    if ((Model.Main.VERIFIER == user.USER_NO ||  (IsAdmin || str == "Y") || ViewBag.VisibleVerify) && (ShowBtn == ZZZI34EditViewModel.ShowBtnVal.Verify))
                    {
                        <a role="button" class="btn2 btn-default" onclick="onUpdateStatus('@ZZZI34EditViewModel.SaveTypeVal.Verify')">通過</a>
                    }
                    <a role="button" class="btn2 btn-default" onclick="onUpdateStatus('@ZZZI34EditViewModel.SaveTypeVal.Disabled')">作廢</a>
                }
                else if (Model.Main.STATUS == ADDT21.STATUSVal.Pass && (IsAdmin || str == "Y"))
                {
                    <button id="BtnSave" class="btn2 btn-default" type="button" onclick="onSave(false)">儲存</button>
                    <a role="button" class="btn2 btn-default" onclick="onUpdateStatus('@ZZZI34EditViewModel.SaveTypeVal.Disabled')">作廢</a>
                }
            }
        }

        <button class="btn2 btn-default" type="button" onclick="onBack()">放棄</button>
    </div>

}
<div class="modal fade" id="myModal" role="dialog">
    <div class="modal-content">

        <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal">&times;</button>
            <h4 class="modal-title">上傳進度</h4>
        </div>
    </div>
    <div class="modal-body">
        <div class="progress progress-striped active">
            <div class="progress-bar progress-bar-success" id="barr" role="progressbar" aria-valuenow="60" aria-valuemin="0" aria-valuemax="100" style="width:0%;">

                <span class="sr-only">40% 完成</span>
            </div>
        </div>
    </div>
    <div class="modal-footer">

        <button type="button" class="btn btn-default" data-dismiss="modal">Close </button>
    </div>
</div>

@section Scripts {
    <script language="JavaScript">

        var targetFormID = '#formEdit';

        $(".colorboxPHOTO").colorbox({ photo: true, maxWidth: "90%", maxHeight: "90%", opacity: 0.82 });
        $(".colorbox").colorbox({ iframe: true, width: "90%", height: "90%", opacity: 0.82 });

        window.onload = function () {
            OnclickWorkType($('input:radio:checked[name="@Html.NameFor(m => m.Main.WORK_TYPE)"]').val())
        };

        function ShosPs(Val)
        {

            $("#PsPhoto").hide()
            $("#PsVideo").hide()
            $("#PsYoutube").hide()

            if (Val == '@ADDT21.WORK_TYPE_VAL.Youtube') {
                $("#PsYoutube").show()
            }
            else if (Val == '@ADDT21.WORK_TYPE_VAL.photo')
            {
                $("#PsPhoto").show()
            }
            @*else if (Val == '@ADDT21.WORK_TYPE_VAL.video')
            {
                $("#PsVideo").show()
            }*@
        }

        function OnclickWorkType(Val)
        {

            if ($('#@Html.IdFor(m=>m.Main.ART_GALLERY_NO)').val()=='') {
             if ('@ShowBtn'=='@ZZZI34EditViewModel.ShowBtnVal.True') {
                if (Val == '@ADDT21.WORK_TYPE_VAL.Youtube') {
                    $('.DivPHOTO_FILE').show()
                    $('.DivUpPhotoFiles').hide()
                }
                else {
                    $('.DivPHOTO_FILE').hide()
                    $('.DivUpPhotoFiles').show()
                }
             }

             ShosPs(Val)
            }
        }

          ///增加作者
        function onAddItem() {

            var data = {
                "ART_GALLERY_TYPE": $('#@Html.IdFor(m=>m.Main.ART_GALLERY_TYPE)').val(),
                "ShowBtn":'@ViewBag.ShowBtn',
            };

            $.ajax({
                url: '@Url.Action("_People")',
                data: data,
                cache: false,
                success: function (html) {
                    $("#editorRows").append(html);
                }
            });
        }

        //del作者
        function onDelItem(index) {
            $('#' + index).remove();
        }

        //增加主題
        function BtnOnAddInput(index) {
            var data = {
                "index": index,
                "ART_GALLERY_TYPE": $('#@Html.IdFor(m=>m.Main.ART_GALLERY_TYPE)').val(),
                "WORK_TYPE": $('input:radio:checked[name="@Html.NameFor(m => m.Main.WORK_TYPE)"]').val(),
                "ShowBtn":'@ViewBag.ShowBtn',
            };

             var editorPhotoRows = "#editorPhotoRows_" + index

            $.ajax({
                url: '@Url.Action("_Photo")',
                data: data,
                cache: false,
                success: function (html) {
                    $(editorPhotoRows).append(html);
                }
            });
        }

        //del 項目
        function deleteRow(Id) {
            $('#' + Id).remove();
        }

        function AutoCash(Val)
        {
            var re = /^[0-9]+$/;
            if (!re.test(Val)) {
                alert("只能輸入數字");
                $("#CASH").val('')
                return false;
            }

            if (parseInt(Val) > parseInt(10) || parseInt(Val) < parseInt(0) ) {
                alert("輸入0~10的數字");
                $("#CASH").val('')
                return false;
            }

            if ($(".CASH").length==0) {
                alert("請新增作者/主題");
                $("#CASH").val('')
                return false;
            }

            $(".CASH").each(function (i) {
                this.value = Val;
            });
        }

        function ChangeUSER_NOUseReplaceWith(ClassValue, tagId, tagName) {

            $.ajax(
                {
                    url: '@Url.Action("_GetUSER_NODDLHtml", (string)ViewBag.BRE_NO)',
                    data: {
                        tagId: tagId,
                        tagName: tagName,
                        CLASS_NO: ClassValue
                    },
                    type: 'post',
                    cache: false,
                    async: false,
                    dataType: 'html',
                    success: function (data) {
                        if (data.length > 0) {
                            $('#' + tagId + '').replaceWith(data);
                        }
                    }
                });

        }

         function onBack() {
            $(targetFormID).attr("action", "@Url.Action("ArtGalleryList", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        function onUpdateStatus(SaveType)
        {
            $(":disabled", $(targetFormID)).removeAttr("disabled");
            $('#@Html.IdFor(m=>m.SaveType)').val(SaveType);
            $(targetFormID).attr("action", "@Url.Action("UpdateStatus", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        function str2json(str, val, obj) {
            var obj = str.indexOf("'") != -1 ? JSON.parse(str.replace(/'/g, "\"")) : JSON.parse(str);
            return (val === undefined ? obj : obj[val])
        };
        var timerr = 1;
                function onSave(Val) {
                    var OBtnSave = $('#BtnSave').html();
                    $('#BtnSave').each(function () { $(this).attr('disabled', 'disabled').html("Loading...資料驗証中…請勿其他動作"); })
            $("#myModal").modal('show');
             if ($('#@Html.IdFor(m=>m.Main.ART_GALLERY_NO)').val() != '')
            {
                $('#fieldset_WORK_TYPE').removeProp('disabled');
            }

            var formdata = new FormData($('form').get(0));



            $('#Msg').addClass('d-none');

            $('span[data-valmsg-for="Error"]').html('');


            var id = setInterval(setTime, 500);
            clearInterval(id);
             $.ajax({
                url: '@Url.Action("CheckJosn", (string)ViewBag.BRE_NO)',
                data: formdata,
                processData: false,
                contentType: false,
                type: 'POST',
                async: false,
                dataType: 'json',               // xml/json/script/html
                cache: false,                   // 是否允許快取
                success: function (data) {

                    var res = $.parseJSON(data);
                    $("#barr").css("width", "100%");

                    if (res.Success.toLowerCase() == 'true') {
                        SaveAction(Val)
                    }
                    else {
                        $("#myModal").modal('hide');
                             if ($('#@Html.IdFor(m=>m.Main.ART_GALLERY_NO)').val() != '')
                             {
                                 $('#fieldset_WORK_TYPE').attr("disabled", "disabled");
                              }

                        $('#Msg').removeClass('d-none')
                        $('span[data-valmsg-for="Error"]').html('發生錯誤;原因:<br />' + res.Error);
                        $('#BtnSave').removeProp('disabled').html(OBtnSave);
                        $('#BtnSave').each(function () { $(this).removeProp('disabled').html(OBtnSave); })
                        $('html, body').animate({
                            scrollTop: $('#LayoutTOP').offset().top
                        }, 'show');
                    }

                },
                error: function (xhr, ajaxOptions, thrownError) {
                    alert(xhr.status);
                    alert(thrownError);
                }
            });
        }
        function setTime() {
            timerr++;
            if (timerr < "70") {
                $("#barr").css("width", timerr + "%");

            }
        }
        function SaveAction(Val)
        {
            var id = setInterval(setTime, 500);
            clearInterval(id);
                    $('#@Html.IdFor(m=>m.IsTempSave)').val(Val)

                    $(":disabled", $(targetFormID)).removeAttr("disabled");
                    $('#BtnSave').attr('disabled', 'disabled').html("Loading...資料處理中…請勿其他動作");

                    $(targetFormID).attr("action", "@Url.Action("EditSave", (string)ViewBag.BRE_NO)")
                    $(targetFormID).submit();
        }

        function DelFile(ART_GALLERY_NO_Val, PHOTO_NO_Val, Index, PhotoIndex) {

                 if (PHOTO_NO_Val=='') {
                    $("#DivCOVER_FILE").remove();
                    $('#DivUploadCoverFile').show();
                    $('#@Html.IdFor(m=>m.Main.COVER_FILE)').val('')
                }
                else {
                    $("#DivPHOTO_FILE_URL_" + PhotoIndex).remove();
                    $('#DivUpPhotoFiles_' + PhotoIndex).show();
                    $('#People_' + Index + '__Photo_' + PhotoIndex+'__PHOTO_FILE').val('')
                }
        }

        function DelYoutube(Index, PhotoIndex)
        {
            $("#DivPHOTO_FILE_URL_" + PhotoIndex).remove();
            $('#People_' + Index + '__Photo_' + PhotoIndex + '__PHOTO_FILE').val('')
            $('#DivPHOTO_FILE_' + PhotoIndex).show();
        }
    </script>
}
