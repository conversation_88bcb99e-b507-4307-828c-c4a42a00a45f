﻿using ECOOL_APP;
using ECOOL_APP.EF;
using EcoolWeb.Models;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Net.Http;
using System.Web;
using System.Web.Http;
using EcoolWeb.Util;
using EcoolWeb.CustomAttribute;
using EntityFramework.Extensions;
using log4net;
using Newtonsoft.Json.Linq;
using System.Linq.Expressions;
using EcoolWeb.ViewModels;
using ECOOL_APP.com.ecool.util;
using Newtonsoft.Json;
using EcoolWeb.Service;
using static EcoolWeb.Controllers.SSOController;

namespace EcoolWeb.Controllers
{
    public class LoginApiController : ApiBase
    {
       // private static ILog logger = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
      
        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();
        static string APPVersion = System.Web.Configuration.WebConfigurationManager.AppSettings["APPVersion"].Trim();
        private static ILog logger = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        /// <summary>
        /// APP 開始CALL 寫入 認證檔
        /// </summary>
        /// <param name="Data"></param>
        /// <returns></returns>
        [HttpPost, HttpGet]
        [AllowAnonymous]
        public UseData FirstInApp(LoginAppFirstInAppViewModel Data)
        {
            logger.Info("FirstInApp Log USERNO");
       
               var result = new UseData();

            if (Data == null) Data = new LoginAppFirstInAppViewModel();
            if (Data!=null&&Data.USER_NO != null) {

                logger.Info("FirstInApp Log USERNO"+ Data.USER_NO);
            }
           
            string Err = string.Empty;
            
            DateTime LAST_LOGIN_TIME;

            if (DateTime.TryParseExact(Data.LAST_LOGIN_TIME, "yyyyMMddHHmmss", CultureInfo.InvariantCulture, DateTimeStyles.None, out LAST_LOGIN_TIME) == false)
            {
                result.Success = "0";
                result.TempNO = string.Empty;
                result.Error = "LAST_LOGIN_TIME 格式錯誤";
                WebApiHelper.InsertCkLog(Data, "LAST_LOGIN_TIME 格式錯誤");
            }

            // 檢查通關密碼是否正確，正確寫入 HRMT05_CHECK 、HRMT05
            var OK = AppHelper.AppCheck(Data.CkAppKey, Data.LAST_LOGIN_TIME, Data.UUID, out Err);
            bool IsEncrypt = !string.IsNullOrEmpty(Data.CkAppKey);

            if (Data.OS_TYPE == HRMT05.OS_TypeVal.Android)
            {
                if (OK == false && IsEncrypt == false) OK = true;
            }

            if (OK)
            {
                //通關檔只保留24小時
                var DiffDate = LAST_LOGIN_TIME.AddHours(-24);

                var DelCk = (from a in db.HRMT05_CHECK
                             where a.UUID == Data.UUID
                             || a.LAST_LOGIN_TIME < DiffDate
                             select a).ToList();

                if (DelCk != null && DelCk.Count > 0)
                {
                    db.HRMT05_CHECK.RemoveRange(DelCk);
                }
                Random rnd = new Random();

                int TempNo = rnd.Next(0, 9999);

                //int TempNo = 0001;

                //當有USER_NO需驗証密碼，比對錯 重登
                if (string.IsNullOrWhiteSpace(Data.USER_NO) == false)
                {
                    string Error = string.Empty;
                    HRMT01 FindUser = EcoolWeb.Controllers.HomeController.CheckLogin(Data.SCHOOL_NO, Data.USER_NO, Data.Password, out Error, HomeController.SLogingTypeVal.AppAuto);

                    if (FindUser == null)
                    {
                        LogHelper.AddLogToDB(Data.SCHOOL_NO, Data.USER_NO, System.Web.HttpContext.Current.Request.UserHostAddress, "APP登入首頁失敗", "LoginFail");
                        Data.SCHOOL_NO = string.Empty;
                        Data.USER_NO = string.Empty;
                        result.Error = Error;
                    }
                }

                HRMT05_CHECK dbCk = new HRMT05_CHECK();
                if (string.IsNullOrEmpty(Data.CkAppKey))
                {
                    dbCk.CHECK_ID = TempNo.ToString();
                }
                else
                {
                    dbCk.CHECK_ID = AppHelper.GetCheckId(Data.LAST_LOGIN_TIME, Data.UUID, TempNo);
                }

                dbCk.UUID = Data.UUID;
                dbCk.DEVICE_TOKEN = Data.DEVICE_TOKEN;
                dbCk.SCHOOL_NO = Data.SCHOOL_NO;
                dbCk.USER_NO = Data.USER_NO;
                dbCk.OS_TYPE = Data.OS_TYPE;
                dbCk.LAST_LOGIN_TIME = LAST_LOGIN_TIME;
                dbCk.TempNO = TempNo;

                db.HRMT05_CHECK.Add(dbCk);

                var DelH05 = (from a in db.HRMT05
                              where a.UUID == Data.UUID
                              select a).FirstOrDefault();

                if (DelH05 != null)
                {
                    db.HRMT05.Remove(DelH05);
                }

                //2017/03/01 <  (2017/04/12 - 7天 )
                // db.HRMT05.Where(a => a.LAST_LOGIN_TIME < DateTime.Today.AddDays(-7)).Delete();

                HRMT05 H05 = new HRMT05();
                H05.UUID = Data.UUID;
                H05.DEVICE_TOKEN = Data.DEVICE_TOKEN;
                H05.SCHOOL_NO = Data.SCHOOL_NO;
                H05.USER_NO = Data.USER_NO;
                H05.OS_TYPE = Data.OS_TYPE;
                H05.LAST_LOGIN_TIME = LAST_LOGIN_TIME;
                H05.STATUS = HRMT05.StatusVal.normal;
                H05.OS_VER = Data.OS_VER;
                H05.APP_VER = Data.APP_VER;
                db.HRMT05.Add(H05);

                try
                {
                    db.SaveChanges();

                    //塞Session
                    ApiBaseModel CkDb = new ApiBaseModel();
                    CkDb.UUID = Data.UUID;
                    CkDb.RealCheckId = dbCk.CHECK_ID;
                    WebApiHelper.ApiSetSession(CkDb);

                    //存Cookie
                    UserProfileHelper.SetUuidCookieData(dbCk.CHECK_ID, Data.UUID, IsEncrypt);

                    result.Success = "1";
                    result.TempNO = TempNo.ToString();

                    result.UserData = UserProfileHelper.Get();

                    if (result.UserData != null)
                    {
                        result.PlayerUrl = Url.Content(result.UserData.PlayerUrl);
                    }
                    LogHelper.AddLogToDB(H05.SCHOOL_NO, H05.USER_NO, System.Web.HttpContext.Current.Request.UserHostAddress, "APP登入首頁成功！", "LoginSuccess");
                    WebApiHelper.InsertCkLog(Data, "APP開起驗証成功", H05.SCHOOL_NO, H05.USER_NO, H05.LAST_LOGIN_TIME, null, H05.OS_TYPE);
                }
                catch (Exception ex)
                {
                    result.Success = "0";
                    result.TempNO = string.Empty;
                    result.Error = ex.Message;

                    WebApiHelper.InsertCkLog(Data, "APP開起驗証失敗;" + ex.Message, H05.SCHOOL_NO, H05.USER_NO, H05.LAST_LOGIN_TIME, null, H05.OS_TYPE);
                }
            }
            else
            {
                result.Success = "0";
                result.TempNO = string.Empty;
                result.Error = Err;
                WebApiHelper.InsertCkLog(Data, "APP開起驗証失敗;" + Err);
            }

            AppVerViewModel AppVerData = new AppVerViewModel();
            AppVerData.OS_TYPE = Data.OS_TYPE.ToString();
            result.APP_VER_NOW = this.GetAppVerData(AppVerData);

            return result;
        }
        [HttpGet]
        [AllowAnonymous]
        public string GetVersion()
        {
            
            string result = "";
            result = APPVersion;
            return result;
        }
        /// <summary>
        /// 選學校 更新
        /// </summary>
        /// <param name="QData"></param>
        /// <returns></returns>
        [HttpPost, HttpGet]
        [AllowAnonymous]
        public UseData SelectUpSCHOOL(LoginAppSelectUpSCHOOLViewModel QData)
        {
            ApiBaseModel Base = WebApiHelper.GetApiBaseModelALL(Request);
            var result = new UseData();
            string schoolNO = QData.SCHOOL_NO;
            BDMT01 T01 = db.BDMT01.Where(a => a.SCHOOL_NO == schoolNO).FirstOrDefault();

            HRMT05_CHECK DbCk = db.HRMT05_CHECK.Where(a => a.CHECK_ID == Base.RealCheckId && a.UUID == Base.UUID).FirstOrDefault();
            if (DbCk == null)
            {
                DbCk = db.HRMT05_CHECK.Where(a => a.TempNO.ToString() == Base.CheckId && a.UUID == Base.UUID).FirstOrDefault();
            }
            HRMT05 H05 = db.HRMT05.Where(a => a.UUID == Base.UUID).FirstOrDefault();
            if (DbCk == null || H05 == null || T01 == null)
            {
                result.Success = "0";
                return result;
            }

            DbCk.SCHOOL_NO = QData.SCHOOL_NO;
            DbCk.USER_NO = string.Empty;

            H05.SCHOOL_NO = QData.SCHOOL_NO;
            H05.USER_NO = string.Empty;

            db.SaveChanges();

            UserProfileHelper.SessionDispose();
            UserProfileHelper.Set(QData.SCHOOL_NO);

            result.UserData = new UserProfile();
            result.UserData.SCHOOL_NO = QData.SCHOOL_NO;
            result.UserData.SCHOOL_NAME = T01.SHORT_NAME;
            result.UserData.USER_TYPE = UserType.Guest;
            result.UserData.NAME = "訪客";
            result.Success = "1";

            return result;
        }

        [HttpPost]
        [AllowAnonymous]
        public UseData GETUSERInfo(string access_token)
        {
            SSO_TaipeiService sSO_TaipeiServicetemp = new SSO_TaipeiService();
            string strGetSSONoCookie = "";
            UseData useData = new UseData();
            HRMT01 CheckUser = null;
            log4net.ILog logger = LogManager.GetLogger("GETUSERInfo親師生");
            if (!string.IsNullOrEmpty(access_token))
            {
                logger.Info("親師生 GETUSERInfo 空");
                CheckUser = db.HRMT01.Include("HRMT25").Where(x => x.accessToken == access_token).FirstOrDefault();
            }
         
            //SSO_TaipeiService SSOservice = new SSO_TaipeiService();
            //SSOTokenObject tokenObj = new SSOTokenObject();
            //tokenObj.access_token = access_token;
            //SSOservice.AccessData( ref access_token);
            //LogHelper.LogToTxt("親師生 access_token1：" + access_token);
          
         
            if (string.IsNullOrEmpty(access_token) || access_token == "")
            {
                useData.Success = "false";
                return useData;
            }
            else if (access_token == "test123")
            {
                HRMT01 FindUser = null;
                FindUser = db.HRMT01.Include("HRMT25").Where(user => user.USER_NO == "coolman" && user.SCHOOL_NO == "111111").FirstOrDefault();
                ZZT08 zZT08 = new ZZT08();
                zZT08 = db.ZZT08.Where(x => x.SCHOOL_NO == "111111" && x.USER_NO == "coolman").FirstOrDefault();
                //填入UserProfile
                UserProfile LoginUser = UserProfile.FillUserProfile(FindUser);
                UserProfileHelper.Set(LoginUser);
                LogHelper.AddLogToDB(LoginUser.SCHOOL_NO, LoginUser.USER_NO, System.Web.HttpContext.Current.Request.UserHostAddress, "SSO登入首頁成功", "LoginSuccess");
                useData.PlayerUrl = UserProfile.GetPlayerUrl(ref db, LoginUser.SCHOOL_NO, LoginUser.USER_NO, LoginUser.SEX, LoginUser.USER_TYPE);
                useData.UserData = new UserProfile();
                useData.UserData = LoginUser;
                useData.UserBackData = new UserBackProfile();
                useData.UserBackData.USER_NO = LoginUser.USER_NO;
                useData.UserBackData.PASSWORD = zZT08.PASSWORD;
                useData.UserBackData.SCHOOL_NO = LoginUser.SCHOOL_NO;
                useData.UserBackData.SCHOOL_NAME = LoginUser.SCHOOL_NAME;
                return useData;
            }
            else if (CheckUser!=null) {
                ZZT08 zZT08 = new ZZT08();
                HRMT01 FindUser = null;
                logger.Info("親師生 GETUSERInfo CheckUser不為空" + access_token);
                FindUser = db.HRMT01.Include("HRMT25").Where(x => x.accessToken == access_token).FirstOrDefault();
                if (FindUser == null)
                {
                    logger.Info("親師生 GETUSERInfo CheckUser FindUser 空");
                    useData.Success = "false";
                    useData.Error = "該帳號不在系統中，請聯絡相關人員!";
                    return useData;
                }
                else
                {
                    zZT08 = db.ZZT08.Where(x => x.SCHOOL_NO == FindUser.SCHOOL_NO && x.USER_NO == FindUser.USER_NO).FirstOrDefault();
                }
           
            //填入UserProfile
            UserProfile LoginUser = UserProfile.FillUserProfile(FindUser);
              
            UserProfileHelper.Set(LoginUser);
            LogHelper.AddLogToDB(LoginUser.SCHOOL_NO, LoginUser.USER_NO, System.Web.HttpContext.Current.Request.UserHostAddress, "SSO登入首頁成功", "LoginSuccess");
            useData.UserData = new UserProfile();
                useData.PlayerUrl= UserProfile.GetPlayerUrl(ref db, LoginUser.SCHOOL_NO, LoginUser.USER_NO, LoginUser.SEX, LoginUser.USER_TYPE);
             useData.UserData = LoginUser;
            useData.UserBackData = new UserBackProfile();
            useData.UserBackData.USER_NO = LoginUser.USER_NO;
            useData.UserBackData.PASSWORD = zZT08.PASSWORD;
            useData.UserBackData.SCHOOL_NO = LoginUser.SCHOOL_NO;
            useData.UserBackData.SCHOOL_NAME = LoginUser.SCHOOL_NAME;
            return useData;
        }
            else
            {
                BackReturnObj reObj = new BackReturnObj();

                reObj.code = "def502009f2bf6d841cf6b05ddf1d635222a042aeef99734ddfbe9cae736e5bb39b6bbba91542c596992e5164634320cf53c218e77c56af30e8b79cb1aff04e617c2afef192705471f9ba7e6e9b1ded5f735226fde9f43a63fa4d4334fb1fe0dcc1f7f300de57e2b9d2575aa0e969422a37fb45161e94e8aa4f8adac378acf91165d00a6da94b33b6a588880fc0eee75650413ed92562194eefaf81a3e7cf23af53e57108a31e9fb1bd8229da841eb71e3c44286ec6a48ac5805046a21c68a6b530ec8f186fb2e021f4b520630fb5a57e389d316e3b126de101b81a943a05cbb6f2a5e8dcd8ea9c03642564da8dbd8fdcd10b9b8748e923aa13d37a7649fd5503ee19c0d3d482021abc08bcf8315dd12cb7b2a02dd0b7f7e596941ca292beaba0fa03951ee24ec34f52432e1375dedc018b66c0de5aa045eda190869e7236e15e6172048d9422b7d9d5493babb6483cbd501c91599aa52abfe537c389caef56eb203cccdfcaf1cb6b2e0b477b25a6290c75434ff97f9b2e97fdec734dd7d69d92eac1a8347376a01e8931b9b170e744e161b48be3196cc1fe9918f";
                string accessTokenStr = "";
                accessTokenStr = "{\"token_type\": \"Bearer\",\"expires_in\": 1209600, \"access_token\": \"  ";
                accessTokenStr = accessTokenStr + access_token;
           
                accessTokenStr = accessTokenStr + "\"}";
                SSOTokenObject TokenObj = JsonConvert.DeserializeObject<SSOTokenObject>(accessTokenStr);
                dynamic ProfileObj =  sSO_TaipeiServicetemp.GetProfile(TokenObj);
                UserObject UserObj = sSO_TaipeiServicetemp.GetUser(TokenObj);
                IDNOObject IDNOObjects = sSO_TaipeiServicetemp.GetIDNO(TokenObj);
                logger.Info("親師生 GETUSERInfo" + access_token);
                logger.Info(" 親師生 GETUSERInfo access_token1：" + access_token);
                if (ProfileObj != null && UserObj!=null && IDNOObjects != null)
                {
           
                    List<HRMT01> hRMT01sItem = new List<HRMT01>();
                    hRMT01sItem = sSO_TaipeiServicetemp.AccessData(ref accessTokenStr);
                    if (hRMT01sItem.Count() > 0)
                    {
                        HRMT01 FindUser = null;
                        ZZT08 zZT08 = new ZZT08();
                        string idcard = hRMT01sItem.Select(x => x.IDNO).FirstOrDefault();
                        logger.Info(" 親師生 GETUSERInfo access_token1 IDNO：" + idcard);
                        using (ECOOL_DEVEntities db = new ECOOL_DEVEntities())
                        {
                            //db.BDMT03.Where(x => x.SCHOOL_NO == str_input[0]).Select(x=>x.SCHOOL_NO.FirstOrDefault();

                            FindUser = db.HRMT01.Include("HRMT25").Where(user => user.IDNO == idcard).FirstOrDefault();
                            if (FindUser == null)
                            {
                                useData.Success = "false";
                                useData.Error = "該帳號不在系統中，請聯絡相關人員!";
                                return useData;
                            }
                            else
                            {
                                zZT08 = db.ZZT08.Where(x => x.SCHOOL_NO == FindUser.SCHOOL_NO && x.USER_NO == FindUser.USER_NO).FirstOrDefault();
                            }
                        }
                        //填入UserProfile
                        UserProfile LoginUser = UserProfile.FillUserProfile(FindUser);
                        UserProfileHelper.Set(LoginUser);
                        LogHelper.AddLogToDB(LoginUser.SCHOOL_NO, LoginUser.USER_NO, System.Web.HttpContext.Current.Request.UserHostAddress, "SSO登入首頁成功", "LoginSuccess");
                        useData.PlayerUrl = UserProfile.GetPlayerUrl(ref db, LoginUser.SCHOOL_NO, LoginUser.USER_NO, LoginUser.SEX, LoginUser.USER_TYPE);
                        useData.UserData = new UserProfile();
                        useData.UserData = LoginUser;
                        useData.UserBackData = new UserBackProfile();
                        useData.UserBackData.USER_NO = LoginUser.USER_NO;
                        useData.UserBackData.PASSWORD = zZT08.PASSWORD;
                        useData.UserBackData.SCHOOL_NO = LoginUser.SCHOOL_NO;
                        useData.UserBackData.SCHOOL_NAME = LoginUser.SCHOOL_NAME;
                        return useData;

                    }
                    else {

                        useData.Success = "false";
                        useData.Error = "該帳號不在系統中，請聯絡相關人員!";
                        return useData;

                    }
               }
                else { 
                    using (var client = new HttpClient())
                {
                    //LogHelper.LogToTxt("親師生 access_token：" + access_token);
                    var json = client.GetStringAsync(" https://cooc.tp.edu.tw/oauth/getUserinfo?access_token=" + access_token).Result;
                    //LogHelper.LogToTxt("親師生：" + json);

                    logger.Info("親師生 GETUSERInfo access_token1：" + json);
                    dynamic userObj = JsonConvert.DeserializeObject<dynamic>(json);
                 
                    string uuidstr = "";
                    try
                    {
                        uuidstr = userObj.id_number.ToString();
                    }
                    catch (Exception e)
                    {
                        useData.Success = "false";
                        useData.Error = "該帳號不在系統中，請聯絡相關人員!";
                        return useData;
                        //return RedirectToAction("COOCGuestIndex", "HOME", new { Message = "該帳號不在系統中，請聯絡相關人員!" });
                    }
                    if (!string.IsNullOrEmpty(uuidstr))
                    {
                        string idcard = "";
                        idcard = uuidstr;
                        HRMT01 FindUser = null;
                        ZZT08 zZT08 = new ZZT08();
                        string SchoolNO = "";
                        using (ECOOL_DEVEntities db = new ECOOL_DEVEntities())
                        {
                            //db.BDMT03.Where(x => x.SCHOOL_NO == str_input[0]).Select(x=>x.SCHOOL_NO.FirstOrDefault();

                            FindUser = db.HRMT01.Include("HRMT25").Where(user => user.IDNO == idcard).FirstOrDefault();
                            if (FindUser == null)
                            {
                                useData.Success = "false";
                                useData.Error = "該帳號不在系統中，請聯絡相關人員!";
                                return useData;
                            }
                            else
                            {
                                zZT08 = db.ZZT08.Where(x => x.SCHOOL_NO == FindUser.SCHOOL_NO && x.USER_NO == FindUser.USER_NO).FirstOrDefault();
                            }
                        }
                        //填入UserProfile
                        UserProfile LoginUser = UserProfile.FillUserProfile(FindUser);
                        UserProfileHelper.Set(LoginUser);
                        LogHelper.AddLogToDB(LoginUser.SCHOOL_NO, LoginUser.USER_NO, System.Web.HttpContext.Current.Request.UserHostAddress, "SSO登入首頁成功", "LoginSuccess");
                       useData.PlayerUrl = UserProfile.GetPlayerUrl(ref db, LoginUser.SCHOOL_NO, LoginUser.USER_NO, LoginUser.SEX, LoginUser.USER_TYPE);
                       useData.UserData = new UserProfile();
                        useData.UserData = LoginUser;
                        useData.UserBackData = new UserBackProfile();
                        useData.UserBackData.USER_NO = LoginUser.USER_NO;
                        useData.UserBackData.PASSWORD = zZT08.PASSWORD;
                        useData.UserBackData.SCHOOL_NO = LoginUser.SCHOOL_NO;
                        useData.UserBackData.SCHOOL_NAME = LoginUser.SCHOOL_NAME;
                        return useData;
                    }
                    }
                }

                return useData;
            }
        }

        /// <summary>
        /// 登入
        /// </summary>
        /// <param name="SouData"></param>
        /// <returns></returns>
        [HttpPost]
        [AllowAnonymous]
        public UseData Login(LoginAppLoginViewModel SouData)
        {
            string Message;
            var result = new UseData();
            log4net.ILog logger = LogManager.GetLogger("APP　登入超怪的");
            if (SouData.UserNo != null) {

                logger.Info("APP　登入超怪的 userno" + SouData.UserNo);
            }
            if (SouData.UserNo == null || SouData.UserNo == "") {

                logger.Info("APP　登入超怪的 userno 為空");
            }
            if (SouData.SchoolNo != null)
            {
                logger.Info("APP　登入超怪的 SchoolNo" + SouData.SchoolNo);
            }
            if (SouData.SchoolNo == null || SouData.SchoolNo == "")
            {

                logger.Info("APP　登入超怪的 SchoolNo 為空");
            }
            logger.Info("APP　登入超怪的 Password" + SouData.Password);
            logger.Info("APP　登入超怪的 Password" + SouData.SLogingType);
            UserProfile LoginUser = new UserProfile();
            HRMT01 FindUser = EcoolWeb.Controllers.HomeController.CheckLogin(SouData.SchoolNo, SouData.UserNo, SouData.Password, out Message, SouData.SLogingType);
            try
            {
                if (FindUser == null)
            {
             
                LogHelper.AddLogToDB(SouData.SchoolNo, SouData.UserNo, System.Web.HttpContext.Current.Request.UserHostAddress, "APP登入首頁失敗", "LoginFail");

                result.Success = "0";
                result.Error = Message;

                return result;
            }

            //填入UserProfile
              LoginUser = UserProfile.FillUserProfile(FindUser);
                if (LoginUser != null)
                {
                    logger.Info("APP　登入超怪的 LoginUser不是空的" );
                }
                else {

                    logger.Info("APP　登入超怪的 LoginUser不是空的");
                }
               

          LogHelper.AddLogToDB(LoginUser.SCHOOL_NO, LoginUser.USER_NO, System.Web.HttpContext.Current.Request.UserHostAddress, "APP登入首頁成功", "LoginSuccess");
                try
                {
                    ApiBaseModel Base = WebApiHelper.GetApiBaseModelALL(System.Web.HttpContext.Current.Request);
                    //ApiBaseModel Base = WebApiHelper.GetApiBaseModelALL(Request);
                    logger.Info("APP　登入超怪的 Base");
                   
                    if (Base!=null && Base.UUID != null) {
                        UserProfileHelper.SetUUID(Base.UUID);
                        logger.Info("APP　登入超怪的 UUID"+ Base.UUID);
                    }
                    if (Base != null && Base.RealCheckId != null)
                    {
                        logger.Info("APP　登入超怪的 RealCheckId" + Base.RealCheckId);
                    }
                    
                    if (Base == null)
                    {

                        logger.Info("APP　登入超怪的 Base null");
                    }

                    HRMT05_CHECK DbCk = db.HRMT05_CHECK.Where(a => a.CHECK_ID == Base.RealCheckId && a.UUID == Base.UUID).FirstOrDefault();
            if (DbCk == null)
            {
                //暫時允許用TempNO
                DbCk = db.HRMT05_CHECK.Where(a => a.TempNO.ToString() == Base.RealCheckId && a.UUID == Base.UUID).FirstOrDefault();
            }
            if (DbCk == null)
            {
                //只用UUID
                DbCk = db.HRMT05_CHECK.Where(a => a.UUID == Base.UUID).FirstOrDefault();
                if (DbCk.TempNO.HasValue)
                {
                    Base.RealCheckId = DbCk.TempNO.ToString();
                    //UserProfileHelper.SetUuidCookieData(Base.RealCheckId, Base.UUID, false);
                }
            }
            result.TempNO = Base.RealCheckId;

            if (DbCk == null)
            {
                if (Base.RealCheckId == null) Base.RealCheckId = string.Empty;
                if (Base.UUID == null) Base.UUID = string.Empty;
                result.Success = "0";
                result.Error = "異常登入，請重新選擇學校。學校：" + FindUser.SCHOOL_NO + "帳號：" + FindUser.USER_NO + "UUID：" + Base.UUID + "CheckId：" + Base.RealCheckId;

                return result;
            }

            DbCk.SCHOOL_NO = FindUser.SCHOOL_NO;
            DbCk.USER_NO = FindUser.USER_NO;

            HRMT05 H05 = db.HRMT05.Where(a => a.UUID == Base.UUID).FirstOrDefault();

            if (H05 == null)
            {
                result.Success = "0";
                result.Error = "異常登入，請關閉APP後重新啟動";

                return result;
            }

            H05.SCHOOL_NO = FindUser.SCHOOL_NO;
            H05.USER_NO = FindUser.USER_NO;

           
                db.SaveChanges();
            }
            catch (Exception ex)
            {
                result.Success = "0";
                result.Error = ex.Message;
                logger.Info("APP　登入超怪的 Password ex" + ex.Message);
                return result;
            }
          
            UserProfileHelper.Set(LoginUser);

            result.Success = "1";
            result.Error = "";
            result.UserData = UserProfileHelper.Get();

            if (result.UserData != null)
            {

                LogHelper.AddLogToDB(LoginUser.SCHOOL_NO, LoginUser.USER_NO, System.Web.HttpContext.Current.Request.UserHostAddress, "SSO登入首頁成功"+ result.UserData.SCHOOL_NO+" "+ result.UserData.USER_NO, "LoginSuccess");
                result.PlayerUrl = Url.Content(result.UserData.PlayerUrl);

                result.Badge = NoticeApiController.getBadgeNumber(result.UserData.SCHOOL_NO, result.UserData.USER_NO, ref db);
            }

            //家長第一次登入(未綁定任何學生前)
            if (FindUser.USER_TYPE == UserType.Parents)
            {
                if (HRMT06.CkPanyStudent(FindUser.SCHOOL_NO, FindUser.USER_NO, db) == false)
                {
                    result.isParentsHrmt06Null = true;
                }
            }
            }
            catch (Exception ex)
            {
                result.Success = "0";
                result.Error = ex.Message;
                logger.Info("APP　登入超怪的 Password ex1" + ex.Message);
                return result;
            }
            return result;
        }

        /// <summary>
        /// 取得 UserProfile api 資訊
        /// </summary>
        /// <returns></returns>
        [HttpPost, HttpGet]
        [AllowAnonymous]
        [SessionExpire]
        public UseData GetUserProfile()
        {
            UserProfile user = UserProfileHelper.Get();

            if (user != null)
            {
                //更新顯示
                UserProfile.RefreshCashInfo(user, ref db);
                UserProfileHelper.Set(user);
            }

            var result = new UseData();

            result.Success = "1";
            result.Error = "";
            result.UserData = user;

            if (result.UserData != null)
            {
                result.PlayerUrl = Url.Content(result.UserData.PlayerUrl);
            }

            if (result.UserData == null)
            {
                string SCHOOL_NO = UserProfileHelper.GetSchoolNo();
                string SHORT_NAME = string.Empty;

                BDMT01 T01 = db.BDMT01.Where(a => a.SCHOOL_NO == SCHOOL_NO).FirstOrDefault();

                if (T01 != null)
                {
                    SHORT_NAME = T01.SHORT_NAME;
                }

                result.UserData = new UserProfile();
                result.UserData.SCHOOL_NO = SCHOOL_NO;
                result.UserData.SCHOOL_NAME = SHORT_NAME;
                result.UserData.USER_TYPE = UserType.Guest;
                result.UserData.NAME = "訪客";
                result.Success = "1";
            }

            return result;
        }

        /// <summary>
        /// 登出
        /// </summary>
        /// <param name="SouData"></param>
        /// <returns></returns>
        [HttpPost]
        public bool Logout(LoginAppLoginViewModel SouData)
        {
            ApiBaseModel Base = WebApiHelper.GetApiBaseModelALL(Request);

            HRMT05_CHECK DbCk = db.HRMT05_CHECK.Where(a => a.CHECK_ID == Base.RealCheckId && a.UUID == Base.UUID).FirstOrDefault();
            LogHelper.AddLogToDB(DbCk.SCHOOL_NO, DbCk.USER_NO, System.Web.HttpContext.Current.Request.UserHostAddress, "APP登出", "LoginOUT");

            DbCk.SCHOOL_NO = "";
            DbCk.USER_NO = "";

            HRMT05 H05 = db.HRMT05.Where(a => a.UUID == Base.UUID).FirstOrDefault();

            H05.SCHOOL_NO = "";
            H05.USER_NO = "";

            db.SaveChanges();

            return true;
        }
        [HttpPost]
        [AllowAnonymous]
        public IHttpActionResult ForgetPasswordAPP(ForgetPasswordViewModel model)
        {
            Func<bool, string,string, IHttpActionResult> funcOK = (success, message,color) =>
            {
                model.Success = success;
                model.Message = message;
                model.color = color;
                return Ok(model);
            };

            //if (HttpContext.Current.Session["Captcha"]?.ToString() != model.Captcha)
            //{
            //    return funcOK(false, "驗證碼輸入錯誤。");
            //}

            bool hasFind = false;
            Expression<Func<HRMT01, bool>> func = (h) => h.BIRTHDAY == model.BirthDay && h.IDNO == model.IdNumber;
            var find = db.HRMT01.Where(func).FirstOrDefault();
            if (find == null)
            {
                return funcOK(false, "找不到對應的使用者。","1");
            }
            if (string.IsNullOrEmpty(find.IDNO))
            {
                return funcOK(false, "您的密碼無法還原到預設值，系統找不到您的身分證ID", "1");
            }
            if (find.USER_TYPE == "S")
            {   // 若為學生角色需再篩選班級座號
                if (string.IsNullOrEmpty(model.ClassNo))
                {
                    return funcOK(false, "班級座號尚未輸入", "1");
                }
                hasFind = $"{find.CLASS_NO}{find.SEAT_NO}" == model.ClassNo;
                if (!hasFind)
                {
                    return funcOK(false, "班級座號輸入錯誤。", "1");
                }
            }

            {   // 其他角色
                hasFind = true;
            }

            try
            {
                int Hrmt25Count = 0;
                Hrmt25Count = db.HRMT25.Where(x => x.USER_NO == find.USER_NO && x.SCHOOL_NO == find.SCHOOL_NO && x.ROLE_ID == HRMT24_ENUM.ROLE_SCHOOL_ADMIN).Count();
                if (find.SCHOOL_NO == "393601" || find.SCHOOL_NO == "323601" || find.SCHOOL_NO == "423613" || find.SCHOOL_NO == "413608"
                    || find.SCHOOL_NO == "343610")
                {
                    //2019/11/1周老師說只有coolman跟helper 會變為0000
                    if (find.USER_NO == "coolman" || find.USER_NO == "helper")
                    {
                        db.ZZT08.SingleOrDefault(z => z.SCHOOL_NO == find.SCHOOL_NO && z.USER_NO == find.USER_NO)
                      .PASSWORD = "0000";
                    }
                    else
                    {
                        //密碼重設 =>已重設為民國年生日
                        TaiwanCalendar cal = new TaiwanCalendar();
                        int twYear = cal.GetYear((DateTime)find.BIRTHDAY);
                        db.ZZT08.SingleOrDefault(z => z.SCHOOL_NO == find.SCHOOL_NO && z.USER_NO == find.USER_NO)
                        .PASSWORD = twYear.ToString() + new StringHelper().StrRigth("00" + find.BIRTHDAY.Value.Month.ToString(), 2)
                        + new StringHelper().StrRigth("00" + find.BIRTHDAY.Value.Day.ToString(), 2);
                    }
                }
                else
                {  //2019/11/1周老師說只有coolman跟helper 會變為0000
                    if (find.USER_NO == "coolman" || find.USER_NO == "helper")
                    {
                        db.ZZT08.SingleOrDefault(z => z.SCHOOL_NO == find.SCHOOL_NO && z.USER_NO == find.USER_NO)
                 .PASSWORD = "0000";
                    }
                    else
                    {
                        //密碼重設 => 身分證後4碼(預設)
                        db.ZZT08.SingleOrDefault(z => z.SCHOOL_NO == find.SCHOOL_NO && z.USER_NO == find.USER_NO)
                    .PASSWORD = find.IDNO.Substring(find.IDNO.Length - 4);
                    }
                }
                db.SaveChanges();
                return funcOK(true, "密碼已成功還原為預設值，請盡速更換新密碼。", "2");
            }
            catch (Exception)
            {
                return funcOK(false, "密碼還原時發生了例外的錯誤", "1");
            }
        }
        [HttpPost]
        [AllowAnonymous]
        public IHttpActionResult ForgetPassword(ForgetPasswordViewModel model)
        {
            Func<bool, string, IHttpActionResult> funcOK = (success, message) =>
            {
                model.Success = success;
                model.Message = message;
                return Ok(model);
            };

            if (HttpContext.Current.Session["Captcha"]?.ToString() != model.Captcha)
            {
                return funcOK(false, "驗證碼輸入錯誤。");
            }

            bool hasFind = false;
            Expression<Func<HRMT01, bool>> func = (h) => h.BIRTHDAY == model.BirthDay && h.IDNO == model.IdNumber;
            var find = db.HRMT01.Where(func).FirstOrDefault();
            if (find == null)
            {
                return funcOK(false, "找不到對應的使用者。");
            }
            if (string.IsNullOrEmpty(find.IDNO))
            {
                return funcOK(false, "您的密碼無法還原到預設值，系統找不到您的身分證ID");
            }
            if (find.USER_TYPE == "S")
            {   // 若為學生角色需再篩選班級座號
                if (string.IsNullOrEmpty(model.ClassNo))
                {
                    return funcOK(false, "班級座號尚未輸入");
                }
                hasFind = $"{find.CLASS_NO}{find.SEAT_NO}" == model.ClassNo;
                if (!hasFind)
                {
                    return funcOK(false, "班級座號輸入錯誤。");
                }
            }

            {   // 其他角色
                hasFind = true;
            }

            try
            {
                int Hrmt25Count = 0;
                Hrmt25Count = db.HRMT25.Where(x => x.USER_NO == find.USER_NO && x.SCHOOL_NO == find.SCHOOL_NO && x.ROLE_ID == HRMT24_ENUM.ROLE_SCHOOL_ADMIN).Count();
                if (find.SCHOOL_NO == "393601" || find.SCHOOL_NO == "323601" || find.SCHOOL_NO == "423613" || find.SCHOOL_NO == "413608"
                    || find.SCHOOL_NO == "343610")
                {
                    //2019/11/1周老師說只有coolman跟helper 會變為0000
                    if (find.USER_NO == "coolman" || find.USER_NO == "helper")
                    {
                        db.ZZT08.SingleOrDefault(z => z.SCHOOL_NO == find.SCHOOL_NO && z.USER_NO == find.USER_NO)
                      .PASSWORD = "0000";
                    }
                    else
                    {
                        //密碼重設 =>已重設為民國年生日
                        TaiwanCalendar cal = new TaiwanCalendar();
                        int twYear = cal.GetYear((DateTime)find.BIRTHDAY);
                        db.ZZT08.SingleOrDefault(z => z.SCHOOL_NO == find.SCHOOL_NO && z.USER_NO == find.USER_NO)
                        .PASSWORD = twYear.ToString() + new StringHelper().StrRigth("00" + find.BIRTHDAY.Value.Month.ToString(), 2)
                        + new StringHelper().StrRigth("00" + find.BIRTHDAY.Value.Day.ToString(), 2);
                    }
                }
                else
                {  //2019/11/1周老師說只有coolman跟helper 會變為0000
                    if (find.USER_NO == "coolman" || find.USER_NO == "helper")
                    {
                        db.ZZT08.SingleOrDefault(z => z.SCHOOL_NO == find.SCHOOL_NO && z.USER_NO == find.USER_NO)
                 .PASSWORD = "0000";
                    }
                    else
                    {
                        //密碼重設 => 身分證後4碼(預設)
                        db.ZZT08.SingleOrDefault(z => z.SCHOOL_NO == find.SCHOOL_NO && z.USER_NO == find.USER_NO)
                    .PASSWORD = find.IDNO.Substring(find.IDNO.Length - 4);
                    }
                }
                db.SaveChanges();
                return funcOK(true, "密碼已成功還原為預設值，請盡速更換新密碼。");
            }
            catch (Exception)
            {
                return funcOK(false, "密碼還原時發生了例外的錯誤");
            }
        }

        /// <summary>
        /// 更新Push
        /// </summary>
        /// <param name="SouData"></param>
        /// <returns></returns>
        [HttpPost]
        public bool SetPush(PushViewModel SouData)
        {
            ApiBaseModel Base = WebApiHelper.GetApiBaseModelALL(Request);

            HRMT05_CHECK DbCk = null;

            if (Base.OS_TYPE == HRMT05.OS_TypeVal.Android)
            {
                int TempNO = 0;
                int.TryParse(Base.RealCheckId, out TempNO);

                DbCk = db.HRMT05_CHECK.Where(a => a.TempNO == TempNO && a.UUID == Base.UUID).FirstOrDefault();
            }
            else
            {
                DbCk = db.HRMT05_CHECK.Where(a => a.CHECK_ID == Base.RealCheckId && a.UUID == Base.UUID).FirstOrDefault();
            }

            HRMT05 T05 = db.HRMT05.Where(a => a.UUID == Base.UUID).FirstOrDefault();

            if (DbCk == null || T05 == null)
            {
                return false;
            }

            DbCk.DEVICE_TOKEN = SouData.DEVICE_TOKEN;

            T05.DEVICE_TOKEN = SouData.DEVICE_TOKEN;
            T05.STATUS = HRMT05.StatusVal.normal;

            db.SaveChanges();

            return true;
        }

        [HttpPost]
        public string GetDEVICE_TOKEN()
        {
            ApiBaseModel Base = WebApiHelper.GetApiBaseModelALL(Request);
            HRMT05 T05 = db.HRMT05.Where(a => a.UUID == Base.UUID && a.STATUS == HRMT05.StatusVal.normal).FirstOrDefault();

            if (T05 == null)
            {
                return string.Empty;
            }
            else
            {
                return T05.DEVICE_TOKEN;
            }
        }

        /// <summary>
        /// // WebApi 產生 Session (暫無使用)
        /// </summary>
        /// <param name="Password"></param>
        /// <returns></returns>
        [HttpPost]
        public bool UniversalPASS(string Password = null)
        {
            return WebApiHelper.GetCKSetSession(Request);
        }

        [HttpPost]
        [AllowAnonymous]
        public string GetAppVerData(AppVerViewModel Data)
        {
            string ReturnVal = string.Empty;

            var Temp = db.SYST01.Where(a => a.SYS_ID == Data.SYS_ID).FirstOrDefault();

            if (Temp != null)
            {
                if (Data.OS_TYPE == "1") //IOS
                {
                    ReturnVal = Temp.APP_VER_BY_IOS;
                }
                else if (Data.OS_TYPE == "2")
                {
                    ReturnVal = Temp.APP_VER_BY_ANDROID;
                }
            }

            if (string.IsNullOrEmpty(ReturnVal))
            {
                ReturnVal = "1.0.0";
            }

            return ReturnVal;
        }

        /// <summary>
        /// Debug for 認證碼 =>APP 開始CALL 寫入 認證檔
        /// </summary>
        /// <param name="Data"></param>
        /// <returns></returns>
        [HttpPost]
        [AllowAnonymous]
        public string DebugGetKey(LoginAppFirstInAppViewModel Data)
        {
            var Key = AppHelper.GetAppKey(Data.LAST_LOGIN_TIME, Data.UUID);

            return Key;
        }

        /// <summary>
        /// Debug 取得CheckId 未使用 DES加密 後字串 ，
        /// </summary>
        /// <param name="LAST_LOGIN_TIME"></param>
        /// <param name="UUID"></param>
        /// <param name="TempNo"></param>
        /// <returns></returns>
        [HttpPost]
        [AllowAnonymous]
        public string DebugDecryptGetCheckId(LoginAppDebugViewModel Data)
        {
            string md5String = AppHelper.GetCheckId(Data.LAST_LOGIN_TIME, Data.UUID, Data.TempNo);

            return md5String;
        }

        /// <summary>
        /// Debug 取得CheckId 使用 DES加密 後字串
        /// </summary>
        /// <param name="LAST_LOGIN_TIME"></param>
        /// <param name="UUID"></param>
        /// <param name="TempNo"></param>
        /// <returns></returns>
        [HttpPost]
        [AllowAnonymous]
        public string DebugEncryptGetCheckId(LoginAppDebugViewModel Data)
        {
            string md5String = AppHelper.GetCheckId(Data.LAST_LOGIN_TIME, Data.UUID, Data.TempNo);

            string Key = AppHelper.GetEncryptCheckId(md5String, Data.UUID);

            Key = Uri.EscapeDataString(Key);

            return Key;
        }

        [HttpPost]
        [AllowAnonymous]
        public string EncryptToMd5(LoginAppEncryptToMd5DesViewModel Data)
        {
            string ReturnVal = string.Empty;

            string UnMd5Text = Data.UUID + Data.Key + Data.LAST_LOGIN_TIME + Data.TempNo;

            var md5 = AppHelper.MD5Encrypt32(UnMd5Text);

            return md5;
        }

        [HttpPost]
        [AllowAnonymous]
        public string EncryptToDes(LoginAppEncryptToDesViewModel Data)
        {
            int NewPageKey = Convert.ToInt32(Data.PageKey) + Convert.ToInt32(Data.TempNO);

            string EncryptCheckId = AppHelper.Encrypt(Data.CheckId, NewPageKey.ToString());
            var Key = Uri.EscapeDataString(EncryptCheckId);

            return Key;
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}