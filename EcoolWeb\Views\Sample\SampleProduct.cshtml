﻿@using System.Collections;
@{
    ViewBag.Title = "SampleProduct";
    Layout = "~/Views/Shared/_Layout.cshtml";

    List<Hashtable> htbPRODUCT = ViewData["PRODUCT_HTB"] == null ? new List<Hashtable>() : (List<Hashtable>)ViewData["PRODUCT_HTB"];
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
}
<br />
@*<h4>學生酷幣點數兌換獎品清單</h4>*@

@*<button class="btn btn-link">酷幣點數排行榜</button>*@
<img src="~/Content/img/web-student-prize-02.png" />
@*<button class="btn btn-link">兌獎名單</button>*@
<img src="~/Content/img/web-student-prize-03.png" />
@*<button class="btn btn-link">年級排行</button>*@
<img src="~/Content/img/web-student-prize-04.png" />
<button class="btn btn-link" onclick="window.location.href='../Sample/SampleProductMana'">新增獎品</button>
<button class="btn btn-link">酷幣比對點</button>
<button class="btn btn-link">酷幣紀錄</button>

<img src="~/Content/img/web-student_png-14.png" style="background-repeat: no-repeat; background-size: 100% 100%;height: 55px;" />
<div class="row show-grid">
    @{
        if (null != htbPRODUCT && htbPRODUCT.Count != 0)
        {

            foreach (Hashtable htb in htbPRODUCT)
            {
                <div class="col-xs-6 col-sm-4">
                    <img src="../@htb["IMG_FILE"]" class="img-responsive" alt="Responsive image" />
                    <p class="text-center"><strong>@htb["AWARD_NAME"]</strong></p>
                    <p>
                        <img src="../Content/img/icon/button01.gif" />兌換點數：@htb["COST_CASH"]<br />
                        <img src="../Content/img/icon/button01.gif" />剩餘數量：@htb["QTY_STORAGE"]<br />
                        <img src="../Content/img/icon/button01.gif" />開始日期：@htb["SDATETIME"]<br />
                        <img src="../Content/img/icon/button01.gif" />兌換期限：@htb["EDATETIME"]<br />
                        @if (null != user && user.USER_TYPE == ECOOL_APP.EF.UserType.Teacher)
                        {
                            <button class="btn btn-link" onclick="window.location.href='../Sample/SampleProductMana?AWARD_NO=@htb["AWARD_NO"]&MANA_STS=U'">修改</button>
                            @("/");
                            <button class="btn btn-link" onclick="window.location.href='../Sample/SampleProductMana?AWARD_NO=@htb["AWARD_NO"]&MANA_STS=D'">刪除</button>
                        }
                    </p>
                </div>
            }
        }
}
</div>