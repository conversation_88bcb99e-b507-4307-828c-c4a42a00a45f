﻿@model ECOOL_APP.com.ecool.Models.DTO.ZZZI26EditViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

<img src="~/Content/img/web-bar3-revise-21.png" style="width:100%" class="img-responsive " alt="Responsive image" />
<div class="Div-EZ-ZZZI26">
    <div class="form-horizontal">
        <label class="control-label">* 以下是成功清單：</label>
        <br /><br />
        <div class="table-responsive">
            <table class="table-ecool table-hover">
                <thead>
                    <tr>
                        <th>
                            @Html.DisplayNameFor(model => model.Details_List.First().NAME)
                        </th>
                        <th>
                            @Html.DisplayNameFor(model => model.Details_List.First().SEAT_NO)
                        </th>
                        <th>
                            @Html.DisplayNameFor(model => model.Details_List.First().BOOK_NAME)
                        </th>
                        <th>
                            @Html.DisplayNameFor(model => model.Details_List.First().PASSPORT_YN)
                        </th>
                        <th>
                            @Html.DisplayNameFor(model => model.Details_List.First().MEMO)
                        </th>
                        <th>
                            推薦與否
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var item in Model.Details_List)
                    {
                        <tr>
                            <td align="center">
                                @Html.DisplayFor(modelItem => item.NAME)
                            </td>
                            <td align="center">
                                @Html.DisplayFor(modelItem => item.SEAT_NO)
                            </td>
                            <td align="center">
                                @Html.DisplayFor(modelItem => item.BOOK_NAME)
                            </td>
                            <td align="center">
                                @Html.DisplayFor(modelItem => item.PASSPORT_YN)
                            </td>
                            <td align="center">
                                @if (item.OK_YN == "Y")
                                {
                                    @Html.Raw(HttpUtility.HtmlDecode(item.MEMO))
                                }
                                else
                                {
                                    <font color="red">@Html.DisplayFor(modelItem => item.MEMO)</font>
                                }
                            </td>
                            <td align="center">
                                @if (item.SHARE_YN == SharedGlobal.Y)
                                {
                                    <font color="red">推薦</font>
                                }
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>
</div>
<div style="height:20px"></div>
<div class=" text-center">
    @Html.ActionLink("回代申請閱讀認證 - 新增(選擇模式)", "Index", null, new { @class = "btn btn-default" })
</div>