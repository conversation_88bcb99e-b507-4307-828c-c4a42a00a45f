﻿using MvcPaging;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;

namespace ECOOL_APP.EF
{
    public class ADDI11OrderListViewModel
    {
        /// <summary>
        /// 搜尋字串
        /// </summary>
        public string WhereKeyword { get; set; }

        /// <summary>
        /// 只顯示某年級
        /// </summary>
        public string WhereGrade { get; set; }

        /// <summary>
        /// 只顯示某一班
        /// </summary>
        public string WhereCLASS_NO { get; set; }

        /// <summary>
        /// 只顯示某一位學生的文章
        /// </summary>
        public string WhereUserNo { get; set; }

        /// <summary>
        /// 只顯示某一所學校
        /// </summary>
        public string WhereSCHOOL_NO { get; set; }

        /// <summary>
        /// 月排行榜
        /// </summary>
        public bool WhereIsMonthTop { get; set; }

        /// <summary>
        /// 開始跑步日期
        /// </summary>
        public string WhereStartRunDate { get; set; }

        /// <summary>
        /// 結束跑步日期
        /// </summary>
        public string WhereendRunDate { get; set; }
        public string WhereUserType { get; set; }

        public bool IsPrint { get; set; }

        public bool IsToExcel { get; set; }

        /// <summary>
        /// 排序欄位
        /// </summary>
        public string OrdercColumn { get; set; }

        public int Page { get; set; }

        /// <summary>
        /// 一頁筆數 預設筆數
        /// </summary>
        public int PageSize { get; set; }

        /// <summary>
        /// 查詢結果
        /// </summary>
        public IPagedList<ADDI11OrderListDataViewModel> ListData;

        public ADDI11OrderListViewModel()
        {
            PageSize = PageGlobal.DfTeamPageSize;
        }
    }
}