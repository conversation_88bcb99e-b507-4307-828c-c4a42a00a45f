/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Neo-Euler/Operators/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.NeoEulerMathJax_Operators={directory:"Operators/Regular",family:"NeoEulerMathJax_Operators",testString:"\u00A0\u2206\u220C\u220F\u2210\u2211\u221B\u221C\u222C\u222D\u222E\u2236\u2237\u2238\u2239",32:[0,0,333,0,0],160:[0,0,333,0,0],8710:[696,4,713,30,689],8716:[720,192,666,103,581],8719:[950,50,1056,70,985],8720:[950,50,1056,70,985],8721:[950,50,1056,56,971],8731:[988,1,833,70,849],8732:[988,1,833,70,849],8748:[950,161,856,49,807],8749:[950,161,1156,49,1107],8750:[950,161,556,49,507],8758:[455,12,216,50,164],8759:[455,12,569,50,517],8760:[455,-206,756,46,710],8761:[455,12,863,46,817],8762:[455,12,756,46,710],8763:[455,12,551,22,530],8772:[720,192,597,54,563],8775:[740,172,597,54,563],8777:[720,192,551,22,530],8779:[577,-108,551,22,530],8780:[597,-102,597,54,563],8802:[720,192,830,81,749],8803:[661,-33,830,81,749],8820:[770,142,807,84,721],8821:[770,142,807,88,725],8824:[770,142,807,71,716],8825:[770,142,807,72,716],8836:[720,192,777,83,673],8837:[720,192,777,103,693],8896:[714,4,775,11,768],8897:[688,12,775,6,756],8898:[598,2,666,55,609],8899:[578,22,666,55,609],8924:[733,54,807,79,700],8925:[745,52,807,102,724],8944:[533,-60,627,76,550],10764:[950,161,1456,49,1407]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"NeoEulerMathJax_Operators"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Operators/Regular/Main.js"]);
