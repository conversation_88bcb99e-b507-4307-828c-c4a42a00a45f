﻿@model ECOOL_APP.EF.BDMT05IndexViewModel
@{
    ViewBag.Title = ViewBag.Panel_Title;
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@{
    Html.RenderAction("_EGameMenu", new { NowAction = "BDMT05Index" });
}

@using (Html.BeginForm("Index", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1", @class = "form-horizontal" }))
{

    <section id="feature" class="feature-section feature-section-2">

        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <h3 style="color: #6ba1ab;">
                        <strong>@ViewBag.Title</strong>
                    </h3>
                </div>
                <!-- /.col-lg-12 -->
            </div>
            <hr style="margin: 19px 0px;">
            <div class="row">
                <div class="col-md-12 well">
                    <a role="button" class="btn btn-primary" onclick="onAdd()"><i class="fa fa-fw -square -circle fa-plus-square"></i>新增關卡</a>
                </div>
            </div>

            <div id="PageContent">
                @Html.Action("_BDMT05PageContent", (string)ViewBag.BRE_NO)
            </div>
        </div><!-- /.container -->
    </section><!-- /.feature-section -->
    <div style="height:25px"></div>
}



@section scripts{
    <script type="text/javascript">

        var targetFormID = '#form1';

        function FunPageProc(page) {
            if ($(targetFormID).size() > 0) {
                $('#@Html.IdFor(m=>m.Page)').val(page)
                funAjax()
            }
        };


        function onBtnDetails(Value) {
            $('#@Html.IdFor(m=>m.whereGAME_NO)').val(Value)
            $(targetFormID).attr("action", "@Url.Action("Edit", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        function delete_show(Value) {

            if (confirm("您確定要刪除？") == true) {
               $('#@Html.IdFor(m=>m.whereGAME_NO)').val(Value)
                $(targetFormID).attr("action", "@Url.Action("Delete", (string)ViewBag.BRE_NO)")
                $(targetFormID).submit();
            }
        }


        function onAdd() {
           $('#@Html.IdFor(m=>m.whereGAME_NO)').val('')
            $(targetFormID).attr("action", "@Url.Action("Edit", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }


        //查詢
        function funAjax() {

            $.ajax({
                url: '@Url.Action("_PageContent", (string)ViewBag.BRE_NO)',
                data: $(targetFormID).serialize(),
                type: 'post',
                async: false,
                cache: false,
                dataType: 'html',
                success: function (data) {
                    $('#PageContent').html(data);
                }
            });
        }
    </script>
}




