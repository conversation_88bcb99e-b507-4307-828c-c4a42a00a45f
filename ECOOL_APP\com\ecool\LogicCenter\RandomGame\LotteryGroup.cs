﻿using ECOOL_APP.com.ecool.LogicCenter.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.com.ecool.LogicCenter.RandomGame
{
    public class LotteryGroup<T> : IGroupWeight<T>
    {
        // 遞減比例
        public double decrementRate = 0.5;

        public string GroupName { get; set; }
        public int DataCount { get; set; }
        public double Weights { get; set; }
        public IEnumerable<T> Datas { get; set; }

        public LotteryGroup(string g, int gc, int w, IEnumerable<T> datas)
        {
            this.GroupName = g;
            this.DataCount = gc;
            this.Weights = w;
            this.Datas = datas;
        }

        /// <summary>
        /// 降低權重及群組母體總數
        /// </summary>
        public void ReduceWeightsAndCount()
        {
            // 這裡去控制誰要降權重
            // if(Group == ?) ... Weights * 0.?
            Weights = Weights * decrementRate;
            DataCount--;
        }
    }
}