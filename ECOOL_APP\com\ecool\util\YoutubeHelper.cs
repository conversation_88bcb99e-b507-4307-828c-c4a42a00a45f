﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Web;

namespace ECOOL_APP.com.ecool.util
{
    public class YoutubeHelper
    {
        static public string ConvertToEmbedYouTubeVideo(string StrUrl, ref string Message)
        {
            var EmbedYouTubeUrl = string.Empty;
            var VideoId = GetYouTubeVideoIdFromUrl(StrUrl, ref Message);

            if (!string.IsNullOrWhiteSpace(Message))
            {
                return EmbedYouTubeUrl;
            }
            else
            {
                EmbedYouTubeUrl = "https://www.youtube.com/embed/" + VideoId;
                return EmbedYouTubeUrl;
            }
        }

        static private string GetYouTubeVideoIdFromUrl(string url, ref string Message)
        {
            Uri uri = null;
            if (!Uri.TryCreate(url, UriKind.Absolute, out uri))
            {
                try
                {
                    uri = new UriBuilder("http", url).Uri;
                }
                catch
                {
                    Message = "無效的網址";
                    return "";
                }
            }

            string host = uri.Host;
            string[] youTubeHosts = { "www.youtube.com", "youtube.com", "youtu.be", "www.youtu.be" };
            if (!youTubeHosts.Contains(host))
            {
                Message = "不是youtube影片網址";
                return "";
            }

            var query = HttpUtility.ParseQueryString(uri.Query);

            if (query.AllKeys.Contains("v"))
            {
                return Regex.Match(query["v"], @"^[a-zA-Z0-9_-]{11}$").Value;
            }
            else if (query.AllKeys.Contains("u"))
            {
                // some urls have something like "u=/watch?v=AAAAAAAAA16"
                return Regex.Match(query["u"], @"/watch\?v=([a-zA-Z0-9_-]{11})").Groups[1].Value;
            }
            else
            {
                // remove a trailing forward space
                var last = uri.Segments.Last().Replace("/", "");
                if (Regex.IsMatch(last, @"^v=[a-zA-Z0-9_-]{11}$"))
                    return last.Replace("v=", "");

                string[] segments = uri.Segments;
                if (segments.Length > 2 && segments[segments.Length - 2] != "v/" && segments[segments.Length - 2] != "watch/" && segments[segments.Length - 2] != "embed/")
                    return "";

                return Regex.Match(last, @"^[a-zA-Z0-9_-]{11}$").Value;
            }
        }
    }
}