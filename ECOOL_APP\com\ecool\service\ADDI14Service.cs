﻿using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using ECOOL_APP.com.ecool.util;
using NPOI.SS.UserModel;

namespace ECOOL_APP.EF
{
    public class ADDI14Service
    {
        /// <summary>
        /// 是否Error true(錯誤) ,false(沒錯誤)
        /// </summary>
        protected bool _CheckErr = false;

        /// <summary>
        /// 格式是否Error true(錯誤) ,false(沒錯誤)
        /// </summary>
        protected bool _CheckDataTypeErr = false;

        /// <summary>
        /// 必輸未輸 true(錯誤) ,false(沒錯誤)
        /// </summary>
        protected bool _CheckMustErr = false;

        /// <summary>
        /// ErrorRowCellExcel
        /// </summary>
        protected string _ErrorRowCellExcel = string.Empty;

        /// <summary>
        /// Excel Sheet名稱
        /// </summary>
        private string[] ArraySheetNames;

        /// <summary>
        ///  Excel 必輸欄位
        /// </summary>
        private string[] MustArray;

        public ADDI14IndexViewModel GetExcelToViewModel(HttpPostedFileBase files, string SCHOOL_NO, ref ECOOL_DEVEntities db, ref string Message)
        {
            ADDI14IndexViewModel model = new ADDI14IndexViewModel();

            NPOIHelper npoi = new NPOIHelper(); //NEW NPOIHelper

            npoi.onDataTypeConflict += new DataRowCellHandler(this.NPOI_DataTypeConflict); //宣告使用 輸入內容的型態 是否設定 及 Excel存儲格式一樣 ex. 日期 輸入[aaa] ，Excel存儲格式設日期，或欄位第一行有定義 System.DateTime
            npoi.onLineCheckValue += new DataRowCellHandler(this.NPOI_LineCheckValue);

            ArraySheetNames = new string[] { "CardList" }; //Excel Sheet名稱,因為是params,所以可以傳入多個(也會變成DataTable Name)
            MustArray = new string[] { "CLASS_NO", "SEAT_NO" }; // Excel 必輸欄位

            string _Error = string.Empty; ;

            try
            {
                DataSet ds = npoi.Excel2Table(files.InputStream, 0, 2, 1, ArraySheetNames);

                if (ds == null)
                {
                    _Error = "上傳錯誤，上傳Excel不符合規定，上傳之 Excel 檔, 請依規定格式填寫，請先下載Sample Excel";
                    Message = _Error;
                    return null;
                }

                ///讀取資料筆數為0
                if (ds.Tables.Count == 0)
                {
                    _Error = "上傳錯誤，錯誤原因如下:<br><br>讀取資料筆數為0，請確認 上傳Excel的 Sheet Name 是否有以下 ";
                    foreach (string ThisSheet in ArraySheetNames)
                    {
                        _Error = _Error + "【" + ThisSheet.ToString() + "】";
                    }
                    _Error = _Error + " Sheet Name ，請確認";

                    Message = _Error;

                    return null;
                }

                ///EXCEL內容資料型態數誤
                if (_CheckDataTypeErr || _CheckMustErr)
                {
                    _Error = "上傳錯誤，錯誤原因如下:<br><br> " + _ErrorRowCellExcel;
                    Message = _Error;

                    return null;
                }

                List<ADDI14EditPeopleViewModel> aDDI14s = ds.Tables[ArraySheetNames[0]].ToList<ADDI14EditPeopleViewModel>();

                StringHelper stringHelper = new StringHelper();

                aDDI14s = aDDI14s.Select(a =>
                {
                    a.SCHOOL_NO = a.SCHOOL_NO ?? SCHOOL_NO;
                    a.SEAT_NO = stringHelper.StrRigth(("00" + a.SEAT_NO), 2);
                    return a;
                }).ToList();

                var KeyList = aDDI14s.Select(a => a.SCHOOL_NO + a.CLASS_NO + a.SEAT_NO).ToList();

                var query = (from a in db.HRMT01
                             join c in db.BDMT01 on a.SCHOOL_NO equals c.SCHOOL_NO
                             where KeyList.Contains(a.SCHOOL_NO + a.CLASS_NO + a.SEAT_NO)
                             && UserStaus.OkUserStausList.Contains(a.USER_STATUS) && a.USER_TYPE == UserType.Student
                             select new ADDI14EditPeopleViewModel()
                             {
                                 SCHOOL_NO = a.SCHOOL_NO,
                                 SHORT_NAME = c.SHORT_NAME,
                                 USER_NO = a.USER_NO,
                                 NAME = a.NAME,
                                 GRADE = a.GRADE,
                                 CLASS_NO = a.CLASS_NO,
                                 SEAT_NO = a.SEAT_NO,
                                 CARD_NO = a.CARD_NO,
                                 CheckIn = "0",
                             }).OrderBy(a => a.SCHOOL_NO).ThenBy(a => a.CLASS_NO).ThenBy(a => a.SEAT_NO);

                model.Details = query.ToList();

                var UnUserNo = (from p in aDDI14s
                                where !(from q in model.Details
                                        select q.SCHOOL_NO + q.CLASS_NO + q.SEAT_NO).Contains(p.SCHOOL_NO + p.CLASS_NO + p.SEAT_NO)
                                select p).ToList();

                if (UnUserNo != null)
                {
                    if (UnUserNo.Count() > 0)
                    {
                        foreach (var item in UnUserNo)
                        {
                            if (string.IsNullOrEmpty(_Error))
                            {
                                _Error = $"學校代碼:{item.SCHOOL_NO};班級:{item.CLASS_NO};座號:{item.SEAT_NO}";
                            }
                            else
                            {
                                _Error = _Error + "、" + $"學校代碼:{item.SCHOOL_NO};班級:{item.CLASS_NO};座號:{item.SEAT_NO}";
                            }
                        }
                        _Error = "以下資料不是有效值，請確認：<br><br>" + _Error;
                        Message = _Error;

                        return null;
                    }
                }

                return model;
            }
            catch (Exception ex)
            {
                _Error = "上傳錯誤，錯誤原因如下:<br><br>" + ex;
                Message = _Error;

                return null;
            }
        }

        public string GetToExceFile(ADDI14IndexViewModel model, ref ECOOL_DEVEntities db, ref string Message)
        {
            if (model.Details?.Count == 0 || model.Details == null)
            {
                Message = "查無任何資料";
                return string.Empty;
            }

            try
            {
                var ExcelTable = model.Details.Select(a =>
                {
                    a.CheckIn = a.CheckIn == "1" ? "O" : "X";
                    return a;
                }).AsDataTable();

                NPOIHelper npoi = new NPOIHelper();
                string TempleteFileFullName = HttpContext.Current.Server.MapPath(@"~/Content/ExcelSample/CardExportADDI14.xlsx");
                FileStream xlsfile = new FileStream(TempleteFileFullName, FileMode.Open, FileAccess.Read);

                IWorkbook aBook = WorkbookFactory.Create(xlsfile);
                npoi.ExportExcelFromTemplete(ExcelTable, aBook, "CardList", false, 2);

                string SCHOOL_NO = model.Details.Select(a => a.SCHOOL_NO).FirstOrDefault();
                string SysTempPath = HttpContext.Current.Server.MapPath($@"~/Content/Temp/{SCHOOL_NO}");
                string strTMPFile = SysTempPath + @"\" + DateTime.Now.ToString("yyyyMMdd") + @"\";

                if (Directory.Exists(strTMPFile) == false)
                {
                    string DelDirectory = SysTempPath;

                    if (Directory.Exists(DelDirectory))
                    {
                        Directory.Delete(DelDirectory, true);
                    }

                    Directory.CreateDirectory(strTMPFile);
                }

                strTMPFile = strTMPFile + @"\CardExport" + DateTime.Now.ToString("yyyyMMddHHmmss") + ".xlsx";

                System.IO.FileStream FS = new FileStream(strTMPFile, FileMode.Create);
                aBook.Write(FS);
                FS.Close();

                return strTMPFile;
            }
            catch (Exception ex)
            {
                Message = ex.Message;
                return string.Empty;
            }
        }

        #region 作用:NPOI EXCEL內容資料型態數誤，顯示的錯誤訊息內容

        protected void NPOI_DataTypeConflict(object sender, DataRowCellFilledArgs e)
        {
            _CheckErr = true;
            _CheckDataTypeErr = true;
            _ErrorRowCellExcel = _ErrorRowCellExcel + "Sheet名稱【" + e.SheetName + "】- EXCEL內容資料型態錯誤,欄位名稱【" + e.ColCaption + "】，第" + e.RowNo.ToString() + "列-儲存格內容為「" + e.CellToString + "」<br/>";
        }

        #endregion 作用:NPOI EXCEL內容資料型態數誤，顯示的錯誤訊息內容

        protected void NPOI_LineCheckValue(object sender, DataRowCellFilledArgs e)
        {
            if (e.SheetName == ArraySheetNames[0])
            {
                foreach (var item in MustArray)
                {
                    if (Convert.IsDBNull(e.Row[item]) || (e.Row[item] ?? "").ToString() == "")
                    {
                        _CheckErr = true;
                        _CheckMustErr = true;
                        _ErrorRowCellExcel = _ErrorRowCellExcel + "Sheet名稱【" + e.SheetName + "】-欄位名稱【" + e.ColCaption + "】，第" + e.RowNo.ToString() + "列-必需輸入<br/>";
                    }
                }
            }
        }
    }
}