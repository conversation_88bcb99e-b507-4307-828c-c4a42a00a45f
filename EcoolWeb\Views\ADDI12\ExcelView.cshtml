﻿@{
    /**/

    ViewBag.Title = ViewBag.Panel_Title;
}
@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

   
        @{Html.RenderAction("_Menu", new { NowAction = "ExcelView" });}

@using (Html.BeginForm((string)ViewBag.Action, (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", enctype = "multipart/form-data" }))
{

    @Html.AntiForgeryToken()
    <img src="~/Content/img/web-bar2-revise-22.png" style="width:100%" class="img-responsive " alt="Responsive image" />
    <div class="Div-EZ-ADDI09">
        <div class="form-horizontal">
            <label class="control-label">@ViewBag.Panel_Title</label>
            <br />
            <br />
            <div >
                <label class="control-label col-md-3" for="QUESTIONS_TXT">上傳Excel檔</label>
                <div class="col-md-9">
                    <input class="btn btn-default" type="file" name="files" placeholder="必填" accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel" />
                    @Html.ValidationMessage("files", "", new { @class = "text-danger" })
                </div>
            </div>
            <div class=" text-center">
                <button class="btn btn-default">送出</button>
            </div>

        </div>
        <div class="form-group">


            <div class="form-group">
                <label class="text-danger">

                    上傳說明:<br /> <span>
                        1.上傳之 Excel 檔, 請依小小舞台格式填寫(<a href="@Url.Content("~/Content/ExcelSample/ADDI12SAMPLE.xlsx")" target="_blank" class="btn-table-link">下載 Sample</a>)
                    </span><br />
                </label>
            </div>
        </div>
    </div>


}
