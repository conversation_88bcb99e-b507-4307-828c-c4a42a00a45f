﻿using ECOOL_APP.com.ecool.Models.entity;
using ECOOL_APP.EF;
using MvcPaging;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace EcoolWeb.ViewModels
{
    public class AWA007QueryViewModel
    {
          /// <summary>
        /// 搜尋字串
        /// </summary>
        public string whereKeyword {get;set;}

        /// <summary>
        /// 只顯示某一位學生的文章
        /// </summary>
        public string whereUserNo { get; set; }

        /// <summary>
        /// 只顯示某一位學生的暱稱
        /// </summary>
        public string whereSNAME { get; set; }

        /// <summary>
        /// 只顯示某一位學生的獎品名稱
        /// </summary>
        public string whereAWARD_NAME { get; set; }

        /// <summary>
        /// 只顯示某一位學生的班級
        /// </summary>
        public string whereCLASS_NO { get; set; }

        public string whereSchoolNo { get; set; }
        
        /// <summary>
        /// 只顯示某一位學生的姓名
        /// </summary>
        public string whereName { get; set; }


        /// <summary>
        /// 月排行榜
        /// </summary>
        public bool WhereIsMonthTop { get; set; }

        public bool IsPrint { get; set; }

        public bool IsToExcel { get; set; }


        /// <summary>
        /// 排序欄位
        /// </summary>
        public string OrdercColumn { get; set; }
        
        public int Page { get; set; }

        /// <summary>
        /// 查詢結果
        /// </summary>
        public IPagedList<uAWA006> AWA006List;

        public AWA007QueryViewModel()
        {
            Page = 0;
            OrdercColumn = "PLAYER_NO";
        }
    }
}