﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'table', 'id', {
	border: 'Ukuran batas',
	caption: '<PERSON><PERSON><PERSON> halaman',
	cell: {
		menu: 'Sel',
		insertBefore: '<PERSON>si<PERSON>',
		insertAfter: '<PERSON><PERSON><PERSON>',
		deleteCell: 'Hapus Sel',
		merge: 'Gabungkan Sel',
		mergeRight: 'Gabungkan ke Kanan',
		mergeDown: 'Gabungkan ke Bawah',
		splitHorizontal: 'Pisahkan Sel Secara Horisontal',
		splitVertical: 'Pisahkan Sel Secara Vertikal',
		title: 'Properti Sel',
		cellType: 'Tipe Sel',
		rowSpan: 'Rentang antar baris',
		colSpan: 'Rentang antar kolom',
		wordWrap: 'Word Wrap',
		hAlign: 'Jajaran Horisontal',
		vAlign: 'Jajaran Vertikal',
		alignBaseline: 'Dasar',
		bgColor: '<PERSON><PERSON>',
		borderColor: '<PERSON><PERSON>',
		data: 'Data',
		header: 'Header',
		yes: 'Ya',
		no: 'Tidak',
		invalidWidth: 'Lebar sel harus sebuah angka.',
		invalidHeight: 'Tinggi sel harus sebuah angka',
		invalidRowSpan: 'Rentang antar baris harus angka seluruhnya.',
		invalidColSpan: 'Rentang antar kolom harus angka seluruhnya',
		chooseColor: 'Pilih'
	},
	cellPad: 'Sel spasi dalam',
	cellSpace: 'Spasi antar sel',
	column: {
		menu: 'Kolom',
		insertBefore: 'Sisip Kolom Sebelum',
		insertAfter: 'Sisip Kolom Sesudah',
		deleteColumn: 'Hapus Kolom'
	},
	columns: 'Kolom',
	deleteTable: 'Hapus Tabel',
	headers: 'Headers',
	headersBoth: 'Keduanya',
	headersColumn: 'Kolom pertama',
	headersNone: 'Tidak ada',
	headersRow: 'Baris Pertama',
	invalidBorder: 'Ukuran batasan harus sebuah angka',
	invalidCellPadding: '\'Spasi dalam\' sel harus angka positif.',
	invalidCellSpacing: 'Spasi antar sel harus angka positif.',
	invalidCols: 'Jumlah kolom harus sebuah angka lebih besar dari 0',
	invalidHeight: 'Tinggi tabel harus sebuah angka.',
	invalidRows: 'Jumlah barus harus sebuah angka dan lebih besar dari 0.',
	invalidWidth: 'Lebar tabel harus sebuah angka.',
	menu: 'Properti Tabel',
	row: {
		menu: 'Baris',
		insertBefore: 'Sisip Baris Sebelum',
		insertAfter: 'Sisip Baris Sesudah',
		deleteRow: 'Hapus Baris'
	},
	rows: 'Baris',
	summary: 'Intisari',
	title: 'Properti Tabel',
	toolbar: 'Tabe',
	widthPc: 'persen',
	widthPx: 'piksel',
	widthUnit: 'lebar satuan'
} );
