﻿@model ECOOL_APP.com.ecool.Models.DTO.ZZZI28AddViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    if (string.IsNullOrWhiteSpace(UUID) == false)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
}


@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")


<label class="label_dt">說明</label>
<br />
<br />
@using (Html.BeginForm("Edit", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", enctype = "multipart/form-data" }))
{
    @Html.AntiForgeryToken()

    @Html.HiddenFor(model=>model.SCHOOL_NO)
    @Html.HiddenFor(model => model.PARENTS_USER_NO)
    @Html.HiddenFor(model => model.REF_BRE_NO)
    <div class="panel panel-ACC">
        <div class="panel-heading  text-center">
            @Html.BarTitle()
        </div>
        <div class="panel-body">
            <div class="form-horizontal">

                @Html.ValidationSummary(true, "", new { @class = "text-danger" })

                <div class="form-group">
                    @Html.LabelFor(model => model.CLASS_NO, htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-9">
                        @Html.DropDownListFor(model => model.CLASS_NO, (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control", @onchange = "ChangeUSER_NOUseReplaceWith()" })
                        @Html.ValidationMessageFor(model => model.CLASS_NO, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group">
                    @Html.Label("名字/座號", htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-9">
                        @Html.DropDownListFor(model => model.STUDENT_USER_NO, (IEnumerable<SelectListItem>)ViewBag.USER_NOItems, new { @class = "form-control" })
                        @Html.ValidationMessageFor(model => model.STUDENT_USER_NO, "", new { @class = "text-danger" })
                    </div>
                </div>
            </div>
            <div class="form-group">
                <div class="col-md-12 text-center">
                    <div class="form-inline">
                        <input type="submit" value="下一步" class="btn btn-default"  />
                    </div>
                </div>
            </div>
        </div>
    </div>
 }


<script language="javascript">

    window.onload = function () {
        ChangeUSER_NOUseReplaceWith()
    }


    function SetUSER_NODDLEmpty() {
        $('#STUDENT_USER_NO').empty();
        $('#STUDENT_USER_NO').append($('<option></option>').val(' ').text('請選擇' + $("label[for='CLASS_NO']").text() + '...').prop('selected', true));
    }


    function ChangeUSER_NOUseReplaceWith() {

        var selectedSCHOOL_NO = $.trim($('#SCHOOL_NO option:selected').val());
        var selectedCLASS_NO = $.trim($('#CLASS_NO option:selected').val());
        var selectedUSER_NO = $.trim($('#STUDENT_USER_NO option:selected').val());


        if (selectedCLASS_NO.length == 0) {
            SetUSER_NODDLEmpty();
        }
        else {
            $.ajax(
            {
                url: '@Url.Action("_GetUSER_NODDLHtml", (string)ViewBag.BRE_NO)',
                data: {
                    tagId: 'STUDENT_USER_NO',
                    tagName: 'STUDENT_USER_NO',
                    SCHOOL_NO: selectedSCHOOL_NO,
                    CLASS_NO: selectedCLASS_NO,
                    USER_NO: selectedUSER_NO
                },
                type: 'post',
                cache: false,
                async: false,
                dataType: 'html',
                success: function (data) {
                    if (data.length > 0) {
                        $('#STUDENT_USER_NO').replaceWith(data);
                    }
                }
            });
        }
    }


</script>