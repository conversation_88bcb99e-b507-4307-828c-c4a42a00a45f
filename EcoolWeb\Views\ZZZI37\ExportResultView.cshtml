﻿@model ZZZI37IndexViewModel
@{
    /**/

    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
}
<script src="~/Scripts/FileSaver.js"></script>
<script src="~/Scripts/jquery.wordexport.js"></script>
<style>
    .PageNext {
        page-break-after: always;
    }

    ul li {
        list-style-type: none;
    }
    /*.artlist{height:100%; overflow:hidden; margin:10px;}
    .artlist ul{ margin:0px;}
    .artlist ul li{ list-style:none;line-height:28px;}
    .artlist ul li span{ float:right;padding-right:10px;}
    .artlist ul li a{ color:#333;}*/
</style>

<div style="height:10px"></div>

<div id="DivPrintBooKWord">
    <div class="form-inline">
        <div class="col-xs-12 text-right">
            <button type="button" class="btn btn-sm btn-sys" onclick="PrintToWordJS()" style=" visibility: hidden;">轉存Word</button>
            <input id="print" TYPE="button" VALUE="列印此頁"
                   ONCLICK="varitext()">
        </div>
    </div>
    @using (Html.BeginForm("ExportResultView", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1" }))
    {
        @Html.AntiForgeryToken()
        @Html.Hidden("HtmlCode")
    }
    <script type="text/javascript">

        var targetFormID = '#form1';
        function PrintToWord() {
            $('#HtmlCode').val($("#HtmlCodeDiv").html())
            $(targetFormID).attr('action', '@Html.Raw(@Url.Action("ToWord", (string)ViewBag.BRE_NO))')
            $(targetFormID).attr('target', '_blank').submit().removeAttr('target');
        }

        function PrintToWordJS() {
            $("#HtmlCodeDiv").wordExport("寫信給耶誕老人");
        }

        function varitext(text) {
            text = document
            print(text)
        }
        $(document).ready(function () {
            $("#print").attr("style","visibility:hidden");
                varitext();

        });
    </script>
</div>
<div id="HtmlCodeDiv">
    <div class="artlist">
        <div style="height:20px;" />
        <div class="col-md-12" style="width:1200px;   height:600px;">
            <ul style="list-style: outside;">
                <li style="font-size:45px ;padding-right:10px;">
                    @Model.EngADDRESS<br />
                    @Model.EngCity<br />
                </li>
                @if (Model.CountryNO == "1")
                {
                    <li style="font-size:45px ;padding-left:700px;margin-top:277px">

                        Père Noël<br />
                        F-33500 Libourne<br />
                        France
                    </li>
                }
                else if (Model.CountryNO == "2")
                {

                    <li style="font-size:45px ;padding-left:700px;margin-top:277px">
                        Weihnachtsmann<br />
                        Weihnachtspostfiliale<br />
                        D-16798 Himmelpfort<br />
                        Germany
                    </li>
                }
                else if (Model.CountryNO == "3")
                {

                    <li style="font-size:45px ;padding-left:700px;margin-top:277px">
                        JEŽIŠKO<br />
                        99 999 JEŽIŠKO<br />
                        Slovakia
                    </li>
                }
            </ul>
        </div>
        <div class="PageNext"></div>

        <div class="col-md-12" style="width:1300px;   height:630px;">

            <img src="~/Content/img/chris.jpg" />
        </div>

        @if (Model.DataList != null)
        {
            foreach (var item in Model.DataList)
            {

                <div class="PageNext"></div>
                <div style="height:15px"></div>
                <div>
                    <div class="col-md-12" style="background-image:url('@ECOOL_APP.UrlCustomHelper.Url_Content(" ~/Content/img/Chris_BG.jpg")');width:1300px;   height:730px; background-repeat:no-repeat;  background-size:100% AUTO; ">
                        <div class="p-context" style="padding-top:50px">

                            <font size="8">
                                @if (!string.IsNullOrWhiteSpace(item.IMG_FILE))
                                {
                                    List<string>
                                        ArrImg = item.IMG_FILE.Split('|').ToList();

                                    foreach (var imgName in ArrImg)
                                    {
                                        string ImgPath = $@"{ViewBag.ImgPath}\{imgName}";
                                        <img src="@Url.Content(ImgPath)" style="margin:10px;max-height:250px;width:auto" href="@Url.Content(ImgPath)" class="img-responsive " />
                                    }
                                }
                                <div>
                                    @if (!string.IsNullOrWhiteSpace(item.ARTICLE_VERIFY))
                                    {
                                        @Html.Raw(HttpUtility.HtmlDecode(item.ARTICLE_VERIFY.Replace("\r\n", "<br />")))
                                    }
                                    else
                                    {
                                        @Html.Raw(HttpUtility.HtmlDecode(item.ARTICLE.Replace("\r\n", "<br />")))
                                    }
                                </div>
                            </font>
                        </div>
                        <div style="height:15px"></div>
                        <div class="text-left">
                            <font size="7">
                                By @item.CLASS_NO @item.NAME
                            </font>
                        </div>
                    </div>
                </div>
            }
        }
        else
        {
            <div class="text-center"><h1>此條件查無任何資料</h1></div>
        }
    </div>
</div>