﻿@model AccreditationTypeIndexViewModel

@Html.AntiForgeryToken()
@Html.HiddenFor(m => m.Keyword)
@Html.HiddenFor(m => m.OrderByColumnName)
@Html.HiddenFor(m => m.SortType)
@Html.HiddenFor(m => m.Page)

<div class="toolbar text-right">
    <a role="button" class="btn btn-sm btn-sys" href='@Url.Action("Edit",(string)ViewBag.BRE_NO)'>
        <span class="fa fa-plus" aria-hidden="true"></span>
        新增護照
    </a>
</div>

<div class="panel panel-ZZZ" name="TOP">
    <div class="panel-heading text-center">
        @Html.BarTitle(null, (string)ViewBag.Title)
    </div>
    <div class="table-responsive">
        <table class="table-ecool table-92Per table-hover table-ecool-ZZZ">
            <thead class="bg-primary-dark text-white">
                <tr class="thead-primary">
                    <th class="text-nowrap">@Html.DisplaySortColumnFor(m => m.ListData.First().TYPE_NAME, Model?.OrderByColumnName, Model?.SortType, true)</th>
                    <th class="text-nowrap">@Html.DisplaySortColumnFor(m => m.ListData.First().CASH, Model?.OrderByColumnName, Model?.SortType, true)</th>
                    <th class="text-nowrap">@Html.DisplaySortColumnFor(m => m.ListData.First().CHG_PERSON, Model?.OrderByColumnName, Model?.SortType, true)</th>
                    <th class="text-nowrap">@Html.DisplaySortColumnFor(m => m.ListData.First().CHG_DATE, Model?.OrderByColumnName, Model?.SortType, true)</th>
                    <th style="width:60px"></th>
                </tr>
            </thead>
            <tbody>
                @if (Model.ListData?.Count() > 0)
                {
                    foreach (var item in Model.ListData)
                    {

                        <tr class="text-center">
                            <td>@Html.DisplayFor(modelItem => item.TYPE_NAME)</td>
                            <td>@Html.DisplayFor(modelItem => item.CASH)</td>
                            <td>@Html.DisplayFor(modelItem => item.CHG_PERSON_NAME)</td>
                            <td>@Html.DisplayFor(modelItem => item.CHG_DATE)</td>

                            <td class="text-nowrap">
                                <a href="@Url.Action("Edit",new {Keyword = item.TYPE_ID })" role="button" class="btn btn-xs btn-Basic" title="編輯">
                                    <span class="fa fa-pencil" aria-hidden="true"></span> 編輯
                                </a>
                                <button type="button" onclick="onBtnDel('@item.TYPE_ID')" role="button" class="btn btn-xs btn-Basic" title="刪除">
                                    <span class="glyphicon glyphicon-trash" aria-hidden="true"></span> 刪除
                                </button>
                            </td>
                        </tr>
                    }
                }
            </tbody>
        </table>
        @if ((Model.ListData?.Count() ?? 0) == 0)
        {
            <div class="text-center" style="padding:10px"><strong>目前無資料</strong></div>
        }
    </div>
</div>

@Html.Pager(Model.ListData.PageSize, Model.ListData.PageNumber, Model.ListData.TotalItemCount).Options(o => o.DisplayTemplate(PageGlobal.DfDisplayTemplate)
.MaxNrOfPages(PageGlobal.DfMaxNrOfPages)
.SetPreviousPageText(PageGlobal.DfSetPreviousPageText)
.SetNextPageText(PageGlobal.DfSetNextPageText)
)