/*
Copyright (c) 2003-2015, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/

/*
dialog.css
============

This file styles dialogs and all widgets available inside of it (tabs, buttons,
fields, etc.).

Dialogs are a complex system because they're very flexible. The CKEditor API
makes it easy to create and customize dialogs by code, by making use of several
different widgets inside its contents.

All dialogs share a main dialog strucuture, which can be visually represented
as follows:

+-- .cke_dialog -------------------------------------------------+
| +-- .cke_dialog_body ----------------------------------------+ |
| | +-- .cke_dialog_title --+ +-- .cke_dialog_close_button --+ | |
| | |                       | |                              | | |
| | +-----------------------+ +------------------------------+ | |
| | +-- .cke_dialog_tabs ------------------------------------+ | |
| | |                                                        | | |
| | +--------------------------------------------------------+ | |
| | +-- .cke_dialog_contents --------------------------------+ | |
| | | +-- .cke_dialog_contents_body -----------------------+ | | |
| | | |                                                    | | | |
| | | +----------------------------------------------------+ | | |
| | | +-- .cke_dialog_footer ------------------------------+ | | |
| | | |                                                    | | | |
| | | +----------------------------------------------------+ | | |
| | +--------------------------------------------------------+ | |
| +------------------------------------------------------------+ |
+----------------------------------------------------------------+

Comments in this file will give more details about each of the above blocks.
*/

/* The outer container of the dialog. */
.cke_dialog
{
	/* Mandatory: Because the dialog.css file is loaded on demand, we avoid
		showing an unstyled dialog by hidding it. Here, we restore its visibility. */
	visibility: visible;
}

/* The inner boundary container. */
.cke_dialog_body
{
	z-index: 1;
	background: #eaeaea;
	border: 1px solid #b2b2b2;
	border-bottom-color: #999;
	border-radius: 3px;
	box-shadow: 0 0 3px rgba(0, 0, 0, .15);
}

/* Due to our reset we have to recover the styles of some elements. */
.cke_dialog strong
{
	font-weight: bold;
}

/* The dialog title. */
.cke_dialog_title
{
	font-weight: bold;
	font-size: 13px;
	cursor: move;
	position: relative;

	color: #474747;
	text-shadow: 0 1px 0 rgba(255,255,255,.75);

	border-bottom: 1px solid #999;
	padding: 6px 10px;

	border-radius: 2px 2px 0 0;
	box-shadow: 0 1px 0 #fff inset;

	background: #cfd1cf;
	background-image: linear-gradient(to bottom, #f5f5f5, #cfd1cf);
	filter: progid:DXImageTransform.Microsoft.gradient(gradientType=0, startColorstr='#f5f5f5', endColorstr='#cfd1cf');
}

.cke_dialog_spinner
{
	border-radius: 50%;

	width: 12px;
	height: 12px;
	overflow: hidden;

	text-indent: -9999em;

	border-top: 2px solid rgba(102, 102, 102, 0.2);
	border-right: 2px solid rgba(102, 102, 102, 0.2);
	border-bottom: 2px solid rgba(102, 102, 102, 0.2);
	border-left: 2px solid rgba(102, 102, 102, 1);

	-webkit-animation: dialog_spinner 1s infinite linear;
	animation: dialog_spinner 1s infinite linear;
}

/* A GIF fallback for IE8 and IE9 which does not support CSS animations. */
.cke_browser_ie8 .cke_dialog_spinner,
.cke_browser_ie9 .cke_dialog_spinner
{
	background: url(images/spinner.gif) center top no-repeat;
	width: 16px;
	height: 16px;
	border: 0;
}

@-webkit-keyframes dialog_spinner
{
	0% {
		-webkit-transform: rotate(0deg);
		transform: rotate(0deg);
	}
	100% {
		-webkit-transform: rotate(360deg);
		transform: rotate(360deg);
	}
}

@keyframes dialog_spinner
{
	0% {
		-webkit-transform: rotate(0deg);
		transform: rotate(0deg);
	}
	100% {
		-webkit-transform: rotate(360deg);
		transform: rotate(360deg);
	}
}

/* The outer part of the dialog contants, which contains the contents body
   and the footer. */
.cke_dialog_contents
{
	background-color: #fff;
	overflow: auto;
	padding: 15px 10px 5px 10px;
	margin-top: 30px;
	border-top: 1px solid #bfbfbf;
	border-radius: 0 0 3px 3px;
}

/* The contents body part, which will hold all elements available in the dialog. */
.cke_dialog_contents_body
{
	overflow: auto;
	padding: 17px 10px 5px 10px;
	margin-top: 22px;
}

/* The dialog footer, which usually contains the "Ok" and "Cancel" buttons as
   well as a resize handler. */
.cke_dialog_footer
{
	text-align: right;
	position: relative;
	border: none;
	outline: 1px solid #bfbfbf;
	box-shadow: 0 1px 0 #fff inset;
	border-radius: 0 0 2px 2px;
	background: #cfd1cf;
	background-image: linear-gradient(to bottom, #ebebeb, #cfd1cf);
	filter: progid:DXImageTransform.Microsoft.gradient(gradientType=0, startColorstr='#ebebeb', endColorstr='#cfd1cf');
}

.cke_rtl .cke_dialog_footer
{
	text-align: left;
}

.cke_hc .cke_dialog_footer
{
	outline: none;
	border-top: 1px solid #fff;
}

.cke_dialog .cke_resizer
{
	margin-top: 22px;
}

.cke_dialog .cke_resizer_rtl
{
	margin-left: 5px;
}

.cke_dialog .cke_resizer_ltr
{
	margin-right: 5px;
}

/*
Dialog tabs
-------------

Tabs are presented on some of the dialogs to make it possible to have its
contents split on different groups, visible one after the other.

The main element that holds the tabs can be made hidden, in case of no tabs
available.

The following is the visual representation of the tabs block:

+-- .cke_dialog_tabs ------------------------------------+
|  +-- .cke_dialog_tab --+ +-- .cke_dialog_tab --+ ...   |
|  |                     | |                     |       |
|  +---------------------+ +---------------------+       |
+--------------------------------------------------------+

The .cke_dialog_tab_selected class is appended to the active tab.
*/

/* The main tabs container. */
.cke_dialog_tabs
{
	height: 24px;
	display: inline-block;
	margin: 5px 0 0;
	position: absolute;
	z-index: 2;
	left: 10px;
}

.cke_rtl .cke_dialog_tabs
{
	right: 10px;
}

/* A single tab (an <a> element). */
a.cke_dialog_tab
{

	height: 16px;
	padding: 4px 8px;
	margin-right: 3px;
	display: inline-block;
	cursor: pointer;
	line-height: 16px;
	outline: none;
	color: #595959;
	border: 1px solid #bfbfbf;

	border-radius: 3px 3px 0 0;

	background: #d4d4d4;
	background-image: linear-gradient(to bottom, #fafafa, #ededed);
	filter: progid:DXImageTransform.Microsoft.gradient(gradientType=0, startColorstr='#fafafa', endColorstr='#ededed');
}

.cke_rtl a.cke_dialog_tab
{
	margin-right: 0;
	margin-left: 3px;
}

/* A hover state of a regular inactive tab. */
a.cke_dialog_tab:hover,
a.cke_dialog_tab:focus
{
	background: #ebebeb;
	background: linear-gradient(to bottom, #ebebeb 0%,#dfdfdf 100%);
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ebebeb', endColorstr='#dfdfdf',GradientType=0 );
}

a.cke_dialog_tab_selected
{
	background: #fff;
	color: #383838;
	border-bottom-color: #fff;
	cursor: default;
	filter: none;
}

/* A hover state for selected tab. */
a.cke_dialog_tab_selected:hover,
a.cke_dialog_tab_selected:focus,
{
	background: #ededed;
	background: linear-gradient(to bottom, #ededed 0%,#ffffff 100%);
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ededed', endColorstr='#ffffff',GradientType=0 );
}

.cke_hc a.cke_dialog_tab:hover,
.cke_hc a.cke_dialog_tab:focus,
.cke_hc a.cke_dialog_tab_selected
{
	border: 3px solid;
	padding: 2px 6px;
}

a.cke_dialog_tab_disabled
{
	color: #bababa;
	cursor: default;
}

/* The .cke_single_page class is appended to the dialog outer element in case
   of dialogs that has no tabs. */
.cke_single_page .cke_dialog_tabs
{
	display: none;
}

.cke_single_page .cke_dialog_contents
{
	padding-top: 5px;
	margin-top: 0;
	border-top: none;
}

/* The close button at the top of the dialog. */

a.cke_dialog_close_button
{
	background-image: url(images/close.png);
	background-repeat: no-repeat;
	background-position: 50%;
	position: absolute;
	cursor: pointer;
	text-align: center;
	height: 20px;
	width: 20px;
	top: 4px;
	z-index: 5;
	opacity: 0.8;
	filter: alpha(opacity = 80);
}

.cke_dialog_close_button:hover
{
	opacity: 1;
	filter: alpha(opacity = 100);
}

.cke_hidpi .cke_dialog_close_button
{
	background-image: url(images/hidpi/close.png);
	background-size: 16px;
}

.cke_dialog_close_button span
{
	display: none;
}

.cke_hc .cke_dialog_close_button span
{
	display: inline;
	cursor: pointer;
	font-weight: bold;
	position: relative;
	top: 3px;
}

.cke_ltr .cke_dialog_close_button
{
	right: 5px;
}

.cke_rtl .cke_dialog_close_button
{
	left: 6px;
}

.cke_dialog_close_button
{
	top: 4px;
}

/*
Dialog UI Elements
--------------------

The remaining styles define the UI elements that can be used inside dialog
contents.

Most of the UI elements on dialogs contain a textual label. All of them share
the same labelling structure, having the label text inside an element with
.cke_dialog_ui_labeled_label and the element specific part inside the
.cke_dialog_ui_labeled_content class.
*/

/* If an element is supposed to be disabled, the .cke_disabled class is
   appended to it. */
div.cke_disabled .cke_dialog_ui_labeled_content div *
{
	background-color: #ddd;
	cursor: default;
}

/*
Horizontal-Box and Vertical-Box
---------------------------------

There are basic layou element used by the editor to properly align elements in
the dialog. They're basically tables that have each cell filled by UI elements.

The following is the visual representation of a H-Box:

+-- .cke_dialog_ui_hbox --------------------------------------------------------------------------------+
|  +-- .cke_dialog_ui_hbox_first --+ +-- .cke_dialog_ui_hbox_child --+ +-- .cke_dialog_ui_hbox_last --+ |
|  +                               + +                               + +                              + |
|  +-------------------------------+ +-------------------------------+ +------------------------------+ |
+-------------------------------------------------------------------------------------------------------+

It is possible to have nested V/H-Boxes.
*/

.cke_dialog_ui_vbox table,
.cke_dialog_ui_hbox table
{
	margin: auto;
}

.cke_dialog_ui_vbox_child
{
	padding: 5px 0px;
}

.cke_dialog_ui_hbox
{
	width: 100%;
}

.cke_dialog_ui_hbox_first,
.cke_dialog_ui_hbox_child,
.cke_dialog_ui_hbox_last
{
	vertical-align: top;
}

.cke_ltr .cke_dialog_ui_hbox_first,
.cke_ltr .cke_dialog_ui_hbox_child
{
	padding-right: 10px;
}

.cke_rtl .cke_dialog_ui_hbox_first,
.cke_rtl .cke_dialog_ui_hbox_child
{
	padding-left: 10px;
}

.cke_ltr .cke_dialog_footer_buttons .cke_dialog_ui_hbox_first,
.cke_ltr .cke_dialog_footer_buttons .cke_dialog_ui_hbox_child
{
	padding-right: 5px;
}

.cke_rtl .cke_dialog_footer_buttons .cke_dialog_ui_hbox_first,
.cke_rtl .cke_dialog_footer_buttons .cke_dialog_ui_hbox_child
{
	padding-left: 5px;
	padding-right: 0;
}

/* Applies to all labeled dialog fields */
.cke_hc div.cke_dialog_ui_input_text,
.cke_hc div.cke_dialog_ui_input_password,
.cke_hc div.cke_dialog_ui_input_textarea,
.cke_hc div.cke_dialog_ui_input_select,
.cke_hc div.cke_dialog_ui_input_file
{
	border: 1px solid;
}

/*
Text Input
------------

The basic text field to input text.

+-- .cke_dialog_ui_text --------------------------+
|  +-- .cke_dialog_ui_labeled_label ------------+ |
|  |                                            | |
|  +--------------------------------------------+ |
|  +-- .cke_dialog_ui_labeled_content ----------+ |
|  | +-- div.cke_dialog_ui_input_text --------+ | |
|  | | +-- input.cke_dialog_ui_input_text --+ | | |
|  | | |                                    | | | |
|  | | +------------------------------------+ | | |
|  | +----------------------------------------+ | |
|  +--------------------------------------------+ |
+-------------------------------------------------+
*/

/*
Textarea
----------

The textarea field to input larger text.

+-- .cke_dialog_ui_textarea --------------------------+
|  +-- .cke_dialog_ui_labeled_label ----------------+ |
|  |                                                | |
|  +------------------------------------------------+ |
|  +-- .cke_dialog_ui_labeled_content --------------+ |
|  | +-- div.cke_dialog_ui_input_textarea --------+ | |
|  | | +-- input.cke_dialog_ui_input_textarea --+ | | |
|  | | |                                        | | | |
|  | | +----------------------------------------+ | | |
|  | +--------------------------------------------+ | |
|  +------------------------------------------------+ |
+-----------------------------------------------------+
*/

textarea.cke_dialog_ui_input_textarea
{
	overflow: auto;
	resize: none;
}

input.cke_dialog_ui_input_text,
input.cke_dialog_ui_input_password,
textarea.cke_dialog_ui_input_textarea
{
	background-color: #fff;
	border: 1px solid #c9cccf;
	border-top-color: #aeb3b9;
	padding: 4px 6px;
	outline: none;
	width: 100%;
	*width: 95%;

	box-sizing: border-box;

	border-radius: 3px;

	box-shadow: 0 1px 2px rgba(0,0,0,.15) inset;
}

input.cke_dialog_ui_input_text:hover,
input.cke_dialog_ui_input_password:hover,
textarea.cke_dialog_ui_input_textarea:hover
{
	border: 1px solid #aeb3b9;
	border-top-color: #a0a6ad;
}

input.cke_dialog_ui_input_text:focus,
input.cke_dialog_ui_input_password:focus,
textarea.cke_dialog_ui_input_textarea:focus,
select.cke_dialog_ui_input_select:focus
{
	outline: none;
	border: 1px solid #139ff7;
	border-top-color: #1392e9;
}

/*
Button
--------

The buttons used in the dialog footer or inside the contents.

+-- a.cke_dialog_ui_button -----------+
|  +-- span.cke_dialog_ui_button --+  |
|  |                               |  |
|  +-------------------------------+  |
+-------------------------------------+
*/

/* The outer part of the button. */
a.cke_dialog_ui_button
{
	display: inline-block;
	*display: inline;
	*zoom: 1;

	padding: 4px 0;
	margin: 0;

	text-align: center;
	color: #333;
	vertical-align: middle;
	cursor: pointer;

	border: 1px solid #b6b6b6;
	border-bottom-color: #999;
	border-radius: 3px;
	box-shadow: 0 1px 0 rgba(255,255,255,.5), 0 0 2px rgba(255,255,255,.15) inset, 0 1px 0 rgba(255,255,255,.15) inset;

	background: #e4e4e4;
	background-image: linear-gradient(to bottom, #ffffff, #e4e4e4);
	filter: progid:DXImageTransform.Microsoft.gradient(gradientType=0, startColorstr='#ffffff', endColorstr='#e4e4e4');

}

span.cke_dialog_ui_button
{
	padding: 0 10px;
}

a.cke_dialog_ui_button:hover
{
	border-color: #9e9e9e;

	background: #ccc;
	background-image: linear-gradient(to bottom, #f2f2f2, #ccc);
	filter: progid:DXImageTransform.Microsoft.gradient(gradientType=0, startColorstr='#f2f2f2', endColorstr='#cccccc');
}

/* 	:focus/:active styles for dialog buttons: regular and footer. */
a.cke_dialog_ui_button:focus,
a.cke_dialog_ui_button:active
{
	border-color: #969696;
	outline: none;
	box-shadow: 0 0 6px rgba(0,0,0,.4) inset;
}

.cke_hc a.cke_dialog_ui_button:hover,
.cke_hc a.cke_dialog_ui_button:focus,
.cke_hc a.cke_dialog_ui_button:active
{
	border: 3px solid;
	padding-top: 1px;
	padding-bottom: 1px;
}

.cke_hc a.cke_dialog_ui_button:hover span,
.cke_hc a.cke_dialog_ui_button:focus span,
.cke_hc a.cke_dialog_ui_button:active span
{
	padding-left: 10px;
	padding-right: 10px;
}

/*
a.cke_dialog_ui_button[style*="width"]
{
	display: block !important;
	width: auto !important;
}
*/
/* The inner part of the button (both in dialog tabs and dialog footer). */
.cke_dialog_footer_buttons a.cke_dialog_ui_button span
{
	color: inherit;
	font-size: 12px;
	font-weight: bold;
	line-height: 18px;
	padding: 0 12px;
}

/* Special class appended to the Ok button. */
a.cke_dialog_ui_button_ok
{
	color: #fff;
	text-shadow: 0 -1px 0 #55830c;
	border-color: #62a60a #62a60a #4d9200;

	background: #69b10b;
	background-image: linear-gradient(to bottom, #9ad717, #69b10b);
	filter: progid:DXImageTransform.Microsoft.gradient(gradientType=0, startColorstr='#9ad717', endColorstr='#69b10b');
}

a.cke_dialog_ui_button_ok:hover
{
	border-color: #5b9909 #5b9909 #478500;

	background: #88be14;
	background: linear-gradient(to bottom, #88be14 0%,#5d9c0a 100%);
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#88be14', endColorstr='#5d9c0a',GradientType=0 );
}

a.cke_dialog_ui_button_ok.cke_disabled {
	border-color: #7D9F51;

	background: #8DAD62;
	background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#B3D271), to(#8DAD62));
	background-image: -webkit-linear-gradient(top, #B3D271, #8DAD62);
	background-image: -o-linear-gradient(top, #B3D271, #8DAD62);
	background-image: linear-gradient(to bottom, #B3D271, #8DAD62);
	background-image: -moz-linear-gradient(top, #B3D271, #8DAD62);
	filter: progid:DXImageTransform.Microsoft.gradient(gradientType=0, startColorstr='#B3D271', endColorstr='#8DAD62');
}

a.cke_dialog_ui_button_ok.cke_disabled span {
	color: #E0E8D1;
}

/* Default text shadow used for inner parts of all dialog buttons (both in dialog tabs and dialog footer). */
a.cke_dialog_ui_button span
{
	text-shadow: 0 1px 0 #fff;
}

/* Text shadow used for inner part of OK dialog button in footer. */
a.cke_dialog_ui_button_ok span
{
	text-shadow: 0 -1px 0 #55830c;
}

span.cke_dialog_ui_button
{
	cursor: pointer;
}

/* 	:focus/:active styles for dialog footer buttons (ok & cancel) */
a.cke_dialog_ui_button_ok:focus,
a.cke_dialog_ui_button_ok:active,
a.cke_dialog_ui_button_cancel:focus,
a.cke_dialog_ui_button_cancel:active
{
	border-width: 2px;
	padding: 3px 0;
}

a.cke_dialog_ui_button_ok:focus,
a.cke_dialog_ui_button_ok:active
{
	border-color: #568C0A;
}

a.cke_dialog_ui_button_ok.cke_disabled:focus,
a.cke_dialog_ui_button_ok.cke_disabled:active
{
	border-color: #6F8C49;
}

/* 	:focus/:active styles for dialog footer buttons (ok & cancel) spans */
a.cke_dialog_ui_button_ok:focus span,
a.cke_dialog_ui_button_ok:active span,
a.cke_dialog_ui_button_cancel:focus span,
a.cke_dialog_ui_button_cancel:active span
{
	padding: 0 11px;	/* Thick button border must be compensated. */
}

/* A special container that holds the footer buttons. */
.cke_dialog_footer_buttons
{
	display: inline-table;
	margin: 5px;
	width: auto;
	position: relative;
	vertical-align: middle;
}

/*
Styles for other dialog element types.
*/

div.cke_dialog_ui_input_select
{
	display: table;
}

select.cke_dialog_ui_input_select
{
	height: 25px;
	line-height: 25px;

	background-color: #fff;
	border: 1px solid #c9cccf;
	border-top-color: #aeb3b9;
	padding: 3px 3px 3px 6px;
	outline: none;
	border-radius: 3px;
	box-shadow: 0 1px 2px rgba(0,0,0,.15) inset;
}

.cke_dialog_ui_input_file
{
	width: 100%;
	height: 25px;
}

.cke_hc .cke_dialog_ui_labeled_content input:focus,
.cke_hc .cke_dialog_ui_labeled_content select:focus,
.cke_hc .cke_dialog_ui_labeled_content textarea:focus
{
	outline: 1px dotted;
}

/*
 * Some utility CSS classes for dialog authors.
 */
.cke_dialog .cke_dark_background
{
	background-color: #DEDEDE;
}

.cke_dialog .cke_light_background
{
	background-color: #EBEBEB;
}

.cke_dialog .cke_centered
{
	text-align: center;
}

.cke_dialog a.cke_btn_reset
{
	float: right;
	background: url(images/refresh.png) top left no-repeat;
	width: 16px;
	height: 16px;
	border: 1px none;
	font-size: 1px;
}

.cke_hidpi .cke_dialog a.cke_btn_reset {
	background-size: 16px;
	background-image: url(images/hidpi/refresh.png);
}

.cke_rtl .cke_dialog a.cke_btn_reset
{
	float: left;
}

.cke_dialog a.cke_btn_locked,
.cke_dialog a.cke_btn_unlocked
{
	float: left;
	width: 16px;
	height: 16px;
	background-repeat: no-repeat;
	border: none 1px;
	font-size: 1px;
}

.cke_dialog a.cke_btn_locked .cke_icon
{
	display: none;
}

.cke_rtl .cke_dialog a.cke_btn_locked,
.cke_rtl .cke_dialog a.cke_btn_unlocked
{
	float: right;
}

.cke_dialog a.cke_btn_locked
{
	background-image: url(images/lock.png);
}

.cke_dialog a.cke_btn_unlocked
{
	background-image: url(images/lock-open.png);
}

.cke_hidpi .cke_dialog a.cke_btn_unlocked,
.cke_hidpi .cke_dialog a.cke_btn_locked {
	background-size: 16px;
}

.cke_hidpi .cke_dialog a.cke_btn_locked {
	background-image: url(images/hidpi/lock.png);
}

.cke_hidpi .cke_dialog a.cke_btn_unlocked {
	background-image: url(images/hidpi/lock-open.png);
}

.cke_dialog .cke_btn_over
{
	border: outset 1px;
	cursor: pointer;
}

/*
The rest of the file contains style used on several common plugins. There is a
tendency that these will be moved to the plugins code in the future.
*/

.cke_dialog  .ImagePreviewBox
{
	border: 2px ridge black;
	overflow: scroll;
	height: 200px;
	width: 300px;
	padding: 2px;
	background-color: white;
}

.cke_dialog .ImagePreviewBox table td
{
	white-space: normal;
}

.cke_dialog  .ImagePreviewLoader
{
	position: absolute;
	white-space: normal;
	overflow: hidden;
	height: 160px;
	width: 230px;
	margin: 2px;
	padding: 2px;
	opacity: 0.9;
	filter: alpha(opacity = 90);

	background-color: #e4e4e4;
}

.cke_dialog .FlashPreviewBox
{
	white-space: normal;
	border: 2px ridge black;
	overflow: auto;
	height: 160px;
	width: 390px;
	padding: 2px;
	background-color: white;
}

.cke_dialog .cke_pastetext
{
	width: 346px;
	height: 170px;
}

.cke_dialog .cke_pastetext textarea
{
	width: 340px;
	height: 170px;
	resize: none;
}

.cke_dialog iframe.cke_pasteframe
{
	width: 346px;
	height: 130px;
	background-color: white;
	border: 1px solid #aeb3b9;
	border-radius: 3px;
}

.cke_dialog .cke_hand
{
	cursor: pointer;
}

.cke_disabled
{
	color: #a0a0a0;
}

.cke_dialog_body .cke_label
{
	display: none;
}

.cke_dialog_body label
{
	display: inline;
	margin-bottom: auto;
	cursor: default;
}

.cke_dialog_body label.cke_required
{
	font-weight: bold;
}

a.cke_smile
{
	overflow: hidden;
	display: block;
	text-align: center;
	padding: 0.3em 0;
}

a.cke_smile img
{
	vertical-align: middle;
}

a.cke_specialchar
{
	cursor: inherit;
	display: block;
	height: 1.25em;
	padding: 0.2em 0.3em;
	text-align: center;
}

a.cke_smile,
a.cke_specialchar
{
	border: 1px solid transparent;
}

a.cke_smile:hover,
a.cke_smile:focus,
a.cke_smile:active,
a.cke_specialchar:hover,
a.cke_specialchar:focus,
a.cke_specialchar:active
{
	background: #fff;
	outline: 0;
}

a.cke_smile:hover,
a.cke_specialchar:hover
{
	border-color: #888;
}

a.cke_smile:focus,
a.cke_smile:active,
a.cke_specialchar:focus,
a.cke_specialchar:active
{
	border-color: #139FF7;
}

/**
 * Styles specific to "cellProperties" dialog.
 */

.cke_dialog_contents a.colorChooser
{
	display: block;
	margin-top: 6px;
	margin-left: 10px;
	width: 80px;
}

.cke_rtl .cke_dialog_contents a.colorChooser
{
	margin-right: 10px;
}

/* Compensate focus outline for some input elements. (#6200) */
.cke_dialog_ui_checkbox_input:focus,
.cke_dialog_ui_radio_input:focus,
.cke_btn_over
{
	outline: 1px dotted #696969;
}

.cke_iframe_shim
{
	display: block;
	position: absolute;
	top: 0;
	left: 0;
	z-index: -1;
	filter: alpha(opacity = 0);
	width: 100%;
	height: 100%;
}
