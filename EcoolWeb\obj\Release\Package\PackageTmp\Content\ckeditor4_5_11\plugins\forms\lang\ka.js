﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'forms', 'ka', {
	button: {
		title: 'ღილაკის პარამეტრები',
		text: 'ტექსტი',
		type: 'ტიპი',
		typeBtn: 'ღილაკი',
		typeSbm: 'გაგზავნა',
		typeRst: 'გასუფთავება'
	},
	checkboxAndRadio: {
		checkboxTitle: 'მონიშვნის ღილაკის (Checkbox) პარამეტრები',
		radioTitle: 'ასარჩევი ღილაკის (Radio) პარამეტრები',
		value: 'ტექსტი',
		selected: 'არჩეული',
		required: 'Required' // MISSING
	},
	form: {
		title: 'ფორმის პარამეტრები',
		menu: 'ფორმის პარამეტრები',
		action: 'ქმედება',
		method: 'მეთოდი',
		encoding: 'კოდირება'
	},
	hidden: {
		title: 'მალული ველის პარამეტრები',
		name: 'სახელი',
		value: 'მნიშვნელობა'
	},
	select: {
		title: 'არჩევის ველის პარამეტრები',
		selectInfo: 'ინფორმაცია',
		opAvail: 'შესაძლებელი ვარიანტები',
		value: 'მნიშვნელობა',
		size: 'ზომა',
		lines: 'ხაზები',
		chkMulti: 'მრავლობითი არჩევანის საშუალება',
		required: 'Required', // MISSING
		opText: 'ტექსტი',
		opValue: 'მნიშვნელობა',
		btnAdd: 'დამატება',
		btnModify: 'შეცვლა',
		btnUp: 'ზემოთ',
		btnDown: 'ქვემოთ',
		btnSetValue: 'ამორჩეულ მნიშვნელოვნად დაყენება',
		btnDelete: 'წაშლა'
	},
	textarea: {
		title: 'ტექსტური არის პარამეტრები',
		cols: 'სვეტები',
		rows: 'სტრიქონები'
	},
	textfield: {
		title: 'ტექსტური ველის პარამეტრები',
		name: 'სახელი',
		value: 'მნიშვნელობა',
		charWidth: 'სიმბოლოს ზომა',
		maxChars: 'ასოების მაქსიმალური ოდენობა',
		required: 'Required', // MISSING
		type: 'ტიპი',
		typeText: 'ტექსტი',
		typePass: 'პაროლი',
		typeEmail: 'Email', // MISSING
		typeSearch: 'Search', // MISSING
		typeTel: 'Telephone Number', // MISSING
		typeUrl: 'URL'
	}
} );
