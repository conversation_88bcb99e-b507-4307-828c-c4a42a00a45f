﻿@model GAAI0CareListIndexViewModel
@using ECOOL_APP.com.ecool.util;
@{
    ViewBag.Title = ViewBag.Panel_Title;

    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
}
<style>
    .modal {
        text-align: center;
    }

    @@media screen and (min-width: 768px) {
        .modal:before {
            display: inline-block;
            vertical-align: middle;
            content: " ";
            height: 100%;
        }
    }

    .modal-dialog {
        display: inline-block;
        text-align: left;
        vertical-align: middle;
    }
</style>
<link href="~/Scripts/tablesorter/dist/css/theme.blue.css" rel="stylesheet" />

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@{
    Html.RenderAction("_PageMenu", new { NowAction = "CareList" });
}

@using (Html.BeginForm("CareList", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1", @class = "form-horizontal", enctype = "multipart/form-data" }))
{

    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })
    @Html.HiddenFor(m => m.IsSearch)
    @Html.HiddenFor(m => m.DivId)
    @Html.HiddenFor(m => m.WhereSCHOOL_NO)
    @Html.HiddenFor(m => m.WhereSYEAR)
    @Html.HiddenFor(m => m.WhereSEMESTER)

    <div class="panel panel-ZZZ" name="TOP">
        <div class="panel-heading text-center">
            @Html.BarTitle(null, (string)ViewBag.Title)
        </div>
        <div class="panel-body">
            <div class="form-horizontal">
                <div class="form-group row">
                    @Html.LabelFor(m => m.WhereCLASS_NO, htmlAttributes: new { @class = "col-md-3 control-label" })
                    <div class="col-md-9">
                        @Html.DropDownListFor(m => m.WhereCLASS_NO, (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control" })
                    </div>
                    <div class="col-md-9">
                        @Html.ValidationMessageFor(m => m.WhereCLASS_NO, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-md-3 control-label">
                        時間
                    </label>
                    <div class="col-md-9">
                        @if (ViewBag.AlarmSyearSemesterItem != null)
                        {
                            @Html.DropDownListFor(m => m.WhereSYEARSEMESTER, (IEnumerable<SelectListItem>)ViewBag.AlarmSyearSemesterItem, new { @class = "form-control" })
                        }
                    </div>
                    <div class="col-md-9">
                        @Html.ValidationMessageFor(m => m.WhereSYEARSEMESTER, "", new { @class = "text-danger" })
                    </div>
                </div>
            </div>
            <div class="text-center">
                <button type="button" class="btn btn-default" onclick="onSearch()">
                    <span class="fa fa-check-circle" aria-hidden="true"></span>搜尋
                </button>
            </div>
        </div>
    </div>

    if (Model.IsSearch == 1)
    {
        if (Model.CareFoDateClass?.Count() >= 0 && Model.CareFoUser?.Count > 0)
        {
            <br />
            <div class="text-center">
                <div id="CareFoDateClassDiv" class="text-center">
                    <div class="row no-print">
                        <div class="col-md-11 col-md-offset-1 text-right">
                            <button type="button" class="btn btn-default btn-xs" onclick="onprint('#CareFoDateClassTable')">
                                列印
                            </button>
                        </div>
                    </div>
                    <table id="CareFoDateClassTable" class="tablesorter-blue">
                        <thead>
                            <tr class="text-center">
                                <td style="width:100px"><input name="inputCareFoDateClassAll" id="inputCareFoDateClassAll" value="ALL" type="checkbox">全部</td>
                                <td>日期</td>
                                <td>沒有配戴的學生</td>
                                <td>未配戴原因</td>
                            </tr>
                        </thead>
                        @if (Model.CareFoDateClass?.Count > 0)
                        {
                            <tbody>
                                @foreach (var item in Model.CareFoDateClass.Where(x => x.UN_WEAR_USERs != null))
                                {
                                    <tr>
                                        <td nowrap="nowrap" class="text-center">
                                            @Html.Partial("_CareFoDateClass", item)
                                        </td>
                                        <td class="text-center" nowrap="nowrap">@(DateHelper.ToSimpleTaiwanDate(item.ALARM_DATES)) - @(DateHelper.ToSimpleTaiwanDate(item.ALARM_DATEE))</td>
                                        <td>@item.UN_WEAR_USERs</td>
                                        <td style="min-width:200px">@item.UN_WEAR_MEMOs</td>
                                    </tr>
                                }
                            </tbody>

                        }
                    </table>
                    <div class="text-center">
                        <hr />
                        <button type="button" class="btn btn-default" onclick="onPrintCare('CareFoDateClassTable')">
                            <span class="fa fa-check-circle" aria-hidden="true"></span>列印關懷通知書
                        </button>
                    </div>
                </div>
                <br />
                <div id="CareFoUserDiv">
                    <div class="row no-print">
                        <div class="col-md-11 col-md-offset-1 text-right">
                            <button type="button" class="btn btn-default btn-xs" onclick="onprint('CareFoUserTable')">
                                列印
                            </button>
                        </div>
                    </div>
                    <table id="CareFoUserTable" class="tablesorter-blue">
                        <thead>
                            <tr class="text-center">
                                <td nowrap="nowrap">
                                    <input name="inputCareFoUserAll" id="inputCareFoUserAll" value="ALL" type="checkbox">全部
                                </td>
                                <td>沒有配戴的學生</td>
                                <td>次數</td>
                            </tr>
                        </thead>
                        @if (Model.CareFoUser?.Count > 0)
                        {
                            <tbody>
                                @foreach (var item in Model.CareFoUser)
                                {
                                    <tr>
                                        <td class="text-center">
                                            @Html.Partial("_CareFoUser", item)
                                        </td>
                                        <td>@item.CLASS_NO @item.NAME @item.SEAT_NO</td>
                                        <td>@item.UN_WEAR_COUNT</td>
                                    </tr>
                                }
                            </tbody>

                        }
                    </table>
                    <div class="text-center">
                        <hr />
                        <button type="button" class="btn btn-default" onclick="onPrintCare('CareFoUserTable')">
                            <span class="fa fa-check-circle" aria-hidden="true"></span>列印關懷通知書
                        </button>
                    </div>
                </div>
            </div>
        }
        else
        {
            <div class="text-center">
                <h2>
                    查無任何資料
                </h2>
            </div>
        }

    }

}

@section Scripts {
    <script src="~/Scripts/printThis/printThis.js"></script>
    <script src="~/Scripts/tablesorter/dist/js/jquery.tablesorter.js"></script>
    <script src="~/Scripts/tablesorter/dist/js/jquery.tablesorter.widgets.js"></script>
    <script language="JavaScript">

        var targetFormID = '#form1';

        $(function() {
            $(".tablesorter-blue").tablesorter();

            $("#inputCareFoDateClassAll").click(function(){
                 $('#CareFoDateClassTable input:checkbox').not(this).prop('checked', this.checked);
            });

            $("#inputCareFoUserAll").click(function(){
                 $('#CareFoUserTable input:checkbox').not(this).prop('checked', this.checked);
            });
        });

        function onSearch()
        {
            $('#@Html.IdFor(m=>m.IsSearch)').val(1)
            $(targetFormID).attr("action", "@Url.Action("CareList", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        function onprint(DivId) {
            $(DivId).printThis();
        }

        function onPrintCare(DivId) {

            if (DivId == "CareFoDateClassTable" && $('#CareFoDateClassTable input:checkbox:checked').length == 0) {
                alert('未勾取任何資料')
                return false;
            }

           if (DivId == "CareFoUserTable" && $('#CareFoUserTable input:checkbox:checked').length == 0) {
                alert('未勾取任何資料')
                return false;
            }

            $('#@Html.IdFor(m=>m.DivId)').val(DivId)
            $(targetFormID).attr("action", "@Url.Action("PrintCareList", (string)ViewBag.BRE_NO)")
            $(targetFormID).attr('target', '_blank')
            $(targetFormID).submit();
            $(targetFormID).attr('target', '_self')
        }
    </script>
}