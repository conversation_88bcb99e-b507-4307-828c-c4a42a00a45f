﻿@model BatchCashIntoIndexViewModel

<link href="~/Content/colorbox/example2/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>

@Html.HiddenFor(m => m.Search.WhereGAME_NAME)
@Html.HiddenFor(m => m.Search.WhereGAME_NO)
@Html.HiddenFor(m => m.Search.WhereLOTTERY_NO)
@Html.HiddenFor(m => m.Search.WhereSCHOOL_NO)
@Html.HiddenFor(m => m.Search.SyntaxName)
@Html.HiddenFor(m => m.Search.Page)
@Html.HiddenFor(m => m.Search.OrdercColumn)
@Html.HiddenFor(m => m.Search.WhereTEMP_USER_ID)

@Html.HiddenFor(m => m.CashSearch.SyntaxName)
@Html.HiddenFor(m => m.CashSearch.Page)
@Html.HiddenFor(m => m.CashSearch.OrdercColumn)

<br />
<div class="form-inline" role="form" id="Q_Div">

    <div class="form-group">
        <label class="control-label">學校</label>
    </div>

    @*<div class="form-inlineEZ">
        <div class="form-group">
            <label>學 校 </label>
            @Html.DropDownListFor(m => m.CashSearch.WhereSCHOOL_NO, ((IEnumerable<SelectListItem>)ViewBag.SchoolNoSelectItem).OrderBy(a => a.Text), "選擇學校...", new { @class = "form-control", @onchange = "FunPageProc(1)" })
        </div>
    </div>*@
    <div class="form-group">
        @Html.DropDownListFor(m => m.CashSearch.WhereSCHOOL_NO, (IEnumerable<SelectListItem>)ViewBag.SchoolNoSelectItem, new { @class = "form-control-Login  selectpicker", @onchange = "FunPageProc(1)" })
    </div>
    <div class="form-group">
        <label class="control-label">班級</label>
    </div>
    <div class="form-group">
        @Html.DropDownListFor(m => m.CashSearch.WhereCLASS_NO, (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control", @onchange = "FunPageProc(1)" })
    </div>
    <div class="form-group">
        <label class="control-label">姓名</label>
    </div>
    <div class="form-group">
        @Html.EditorFor(model => model.CashSearch.WhereNAME, new { htmlAttributes = new { @class = "form-control", @style = "width:100px" } })
    </div>
    <br />
    <div class="form-group" style="padding-top:20px">
        <label class="control-label">卡號</label>
    </div>
    <div class="form-group">
        @Html.EditorFor(model => model.CashSearch.WhereCARD_NO, new { htmlAttributes = new { @class = "form-control" } })
    </div>
    <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1)" />
    <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />
</div>
<br />

<div class="panel panel-ZZZ" name="TOP">
    <div class="panel-heading text-center">
        人員編輯
    </div>
    <div class="table-responsive">

        <table class="table-ecool table-92Per table-hover table-ecool-ZZZ">
            <thead>
                <tr>
                    <th>
                    </th>
                    <th>
                        <samp class="form-group">
                            @Html.DisplayNameFor(model => model.ListData.First().SHORT_NAME)
                        </samp>
                    </th>
                    <th>
                        <samp class="form-group">
                            @Html.DisplayNameFor(model => model.ListData.First().NAME)
                        </samp>
                    </th>
                    <th>
                        <samp class="form-group">
                            身份
                        </samp>
                    </th>
                    <th>
                        <samp class="form-group">
                            @Html.DisplayNameFor(model => model.ListData.First().USER_NO)
                        </samp>
                    </th>
                    <th>
                        <samp class="form-group">
                            @Html.DisplayNameFor(model => model.ListData.First().CLASS_NO)
                        </samp>
                    </th>
                    <th>
                        <samp class="form-group">
                            @Html.DisplayNameFor(model => model.ListData.First().SEAT_NO)
                        </samp>
                    </th>
                    <th>
                        <samp class="form-group">
                            @Html.DisplayNameFor(model => model.ListData.First().PHONE)
                        </samp>
                    </th>
                    <th>
                        <samp class="form-group">
                            卡號
                        </samp>
                    </th>
                    <th>
                        <samp class="form-group">
                            活動<br />酷幣餘額
                        </samp>
                    </th>
                    <th>
                    </th>
                </tr>
            </thead>
            <tbody>
                @if (Model.ListData.Count() > 0)
                {
                    foreach (var item in Model.ListData)
                    {

                        <tr>
                            <td>

                                <button type="button" class="btn btn-xs btn-Basic" onclick="OnEdit('@item.TEMP_USER_ID')"> <i class="fa fa-edit"></i> 編輯</button>
                                <button type="button" class="btn btn-xs btn-Basic" onclick="OndDel('@item.TEMP_USER_ID')"> <i class="fa fa-trash-o"></i> 刪除</button>
                                @if (item.GAME_USER_TYPE == UserType.Guest || item.GAME_USER_TYPE == UserType.VIP)
                                {
                                    if (item.STATUS == (byte)ADDT27.StatusVal.使用中)
                                    {
                                        <button type="button" class="btn btn-xs btn-Basic" onclick="OndDisable('@item.TEMP_USER_ID')"> <i class="fa fa-trash-o"></i>停止使用</button>
                                    }
                                    else if (item.STATUS == (byte)ADDT27.StatusVal.停用)
                                    {
                                        <button type="button" class="btn btn-xs btn-Basic" onclick="OnEnable('@item.TEMP_USER_ID')"> <i class="fa fa-trash-o"></i>啟用使用</button>
                                    }
                                }
                            </td>
                            <td align="center">
                                @if (item.GAME_USER_TYPE == UserType.Student)
                                {
                                    @Html.DisplayFor(modelItem => item.SHORT_NAME)
                                }
                                else
                                {
                                    <font color="#0000AD">臨時卡</font>
                                    @Html.DisplayFor(modelItem => item.SHORT_NAME)
                                }
                            </td>
                            <td align="center">
                                @Html.DisplayFor(modelItem => item.NAME)
                                @if (item.STATUS == (byte)ADDT27.StatusVal.停用)
                                {
                                    <font color="red">(停用)</font>
                                }
                            </td>

                            <td align="center">
                                @if (string.IsNullOrWhiteSpace(item.GAME_USER_TYPE_DESC))
                                {
                                    @UserType.GetDesc(item.GAME_USER_TYPE)
                                }
                                else
                                {
                                    @Html.DisplayFor(modelItem => item.GAME_USER_TYPE_DESC)
                                }
                            </td>
                            <td align="center">
                                @if (item.GAME_USER_TYPE == UserType.Student)
                                {
                                    @Html.DisplayFor(modelItem => item.USER_NO)
                                }
                            </td>
                            <td align="center">
                                @Html.DisplayFor(modelItem => item.CLASS_NO)
                            </td>
                            <td align="center">
                                @Html.DisplayFor(modelItem => item.SEAT_NO)
                            </td>
                            <td align="center">
                                @Html.DisplayFor(modelItem => item.PHONE)
                            </td>
                            <td align="center">
                                @Html.DisplayFor(modelItem => item.GAME_USER_ID)
                            </td>
                            <td align="center">
                                @item.CASH_AVAILABLE
                            </td>
                            <td align="center">
                                <button type="button" class="colorbox btn btn-link" href="@Url.Action("QueryUserGameData", (string)ViewBag.BRE_NO,new {GAME_NO=item.GAME_NO,TEMP_USER_ID=item.TEMP_USER_ID })">現況查詢</button>
                            </td>
                        </tr>
                    }
                }
            </tbody>
        </table>
    </div>
</div>

<div>
    @Html.Pager(Model.ListData.PageSize, Model.ListData.PageNumber, Model.ListData.TotalItemCount).Options(o => o.DisplayTemplate(PageGlobal.DfDisplayTemplate)
                                                              .MaxNrOfPages(PageGlobal.DfMaxNrOfPages)
                                                              .SetPreviousPageText(PageGlobal.DfSetPreviousPageText)
                                                              .SetNextPageText(PageGlobal.DfSetNextPageText)
                                                             )
</div>

<script type="text/javascript">
    $(document).ready(function () {
        $('.selectpicker').selectpicker({
            liveSearch: true,
            showSubtext: true
        });
    });
    $(".colorbox").colorbox({ iframe: true, width: "90%", height: "90%", opacity: 0.82 });
</script>