﻿using com.ecool.service;
using EcoolWeb.Models;
using EcoolWeb.Util;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.Caching;
using System.Web.Mvc;
using ECOOL_APP;
using ECOOL_APP.EF;
using System.Text.RegularExpressions;
using System.Drawing;
using EcoolWeb.CustomAttribute;
using ECOOL_APP.com.ecool.util;
using System.Text;
using System.Net;

namespace EcoolWeb.Controllers
{
    [SessionExpire]
    public class Awat2Controller : Controller
    {
        //清單列表
        public ActionResult Awat2Q02()
        {
            return RedirectToAction("Awat2Q02", "AWAI01");
        }
    }
}