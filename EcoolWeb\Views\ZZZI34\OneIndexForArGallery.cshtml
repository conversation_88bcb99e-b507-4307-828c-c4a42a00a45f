﻿@model ZZZI34WorkIndexViewModel
@{
    /**/

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode || (Model?.WhereIsColorboxForUser ?? false) || !string.IsNullOrWhiteSpace(Model?.ShareViewPHOTO_NO))
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
    else
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }

}

@using (Html.BeginForm("Index", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1" }))
{
    @Html.HiddenFor(m => m.Search.WhereSCHOOL_NO)
    @Html.HiddenFor(m => m.Search.WhereUSER_NO)
    <div id="check1"></div>
    @Html.AntiForgeryToken()
    <div id="OnePageContent" style="min-height:400px">
        @Html.Action("_OnePageContent", (string)ViewBag.BRE_NO, new { FromURL = Model.FromURL })
    </div>

    @*  if (!string.IsNullOrEmpty(Model?.ShareViewPHOTO_NO))
        {*@
    <!-- Modal -->
    <div class="modal fade" id="LikeNickNameModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="myModalLabel">推薦-帳號未登入請先登入或是訪客，請輸入訪客姓名</h4>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        @Html.Label("訪客姓名", htmlAttributes: new { @class = "col-md-3 control-label" })
                        <div class="col-md-9">
                            @Html.Hidden("PHOTO_NO")
                            @Html.Hidden("LikeCount")
                            @Html.Editor("NICK_NAME", new { htmlAttributes = new { @class = "form-control input-md", @placeholder = "請輸訪客姓名" } })
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" onclick="ModalSHARE_show()">確定</button>
                    <button type="button" class="btn btn-default" data-dismiss="modal">放棄</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal -->
    <div class="modal fade" id="LikeListModal" tabindex="-1" role="dialog" aria-labelledby="LikeListModal">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title">按讚人員</h4>
                </div>
                <div class="modal-body modal-body-scroll" style="max-height:300px">
                    <div id="LikeListPageContent">
                        @Html.Action("_LikeListView", (string)ViewBag.BRE_NO)
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">關閉</button>
                </div>
            </div>
        </div>
    </div>
    @* }*@

}

<script type="text/javascript">

                var targetFormID = '#form1';

                jQuery(document).ready(function () {
                    var $modal = $('#myShareUrlModal');

                    $modal.on('show.bs.modal', function () {
                        var $this = $(this);
                        var $modal_dialog = $this.find('.modal-dialog');
                        $this.css('display', 'block');

                        $modal_dialog.css({ 'margin-top': Math.max(0, ($(window).height() - $modal_dialog.height()) - 180) });
                    });

                    var $QrCodeModal = $('#myQrCodeModal');

                    $QrCodeModal.on('show.bs.modal', function () {
                        var $this = $(this);
                        var $modal_dialog = $this.find('.modal-dialog');
                        $this.css('display', 'block');
                        $modal_dialog.css({ 'margin-top': Math.max(0, ($(window).height() - $modal_dialog.height()) - 180) });
                    });

                });

                     function funAjaxGetLikeList(PHOTO_NO) {
                        $.ajax({
                            url: '@Url.Action("_LikeListView", (string)ViewBag.BRE_NO)',
                            data: {
                                PHOTO_NO: PHOTO_NO,
                            },     // data
                            type: 'post',
                            async: false,
                            cache: false,
                            dataType: 'html',
                            success: function (data) {
                                $('#LikeListPageContent').html(data);

                                $('#LikeListModal').modal('show');
                            },

                            error: function (xhr, ajaxOptions, thrownError) {
                                alert(xhr.status);
                                alert(thrownError);
                            }
                        });
                    }

                //分頁
                function FunPageProc(pageno) {

                    $('#@Html.IdFor(m=>m.WorkSearch.Page)').val(pageno)
                      $.ajax({
                            url: '@Url.Action("_OnePageContent", (string)ViewBag.BRE_NO)',
                            data: $(targetFormID).serialize(),
                            type: 'post',
                            async: false,
                            cache: false,
                            dataType: 'html',
                            success: function (data) {
                                $('#OnePageContent').html(data);
                            },
                            error: function (xhr, ajaxOptions, thrownError) {
                                alert(xhr.status);
                                alert(thrownError);
                            }
                        });
                }

                function FunNext() {
                    $("#Search_WhereART_GALLERY_NO").val('@Model.Search.WhereART_GALLERY_NO');

                    //$('.One').find('#next').click()
                    $("#next").click();
                }

                function FunPrev() {
                    $("#Search_WhereART_GALLERY_NO").val('@Model.Search.WhereART_GALLERY_NO');

                    //$('.One').find('#prev').click()
                    $("#prev").click();
                }

                 function ModalSHARE_show()
                    {

                        var NICK_NAME = $('#NICK_NAME').val()
                        var PHOTO_NO = $('#PHOTO_NO').val()
                        var LikeCount = $('#LikeCount').val()

                        if (LikeCount=='') {
                            LikeCount = 0;
                        }

                        if (NICK_NAME=='') {
                            alert('未輸入訪客姓名')
                            return false;
                        }

                        if (PHOTO_NO == '') {
                            alert('異常，未取得PHOTO_NO，請先「放棄、，重新點選')
                            return false;
                        }

                        SHARE_show(PHOTO_NO, NICK_NAME, LikeCount)
                        $('#LikeNickNameModal').modal('hide');
                    }

                    function SHARE_show(PHOTO_NO, NICK_NAME, LikeCount)
                    {
                        $("#check1").html("");
                        var str = "";
                        str = $("#check1").html();
                        $("#check1").html(str + "on0");
                        $("#check1").attr("style", "");
                        var  nowpage=$("#WorkSearch_Page").val();
                         $.ajax({
                            url: "@(Url.Action("ShareSave", (string)ViewBag.BRE_NO))",     // url位置
                            type: 'post',                   // post/get
                            data: {
                                PHOTO_NO: PHOTO_NO,
                                NICK_NAME: NICK_NAME,
                                LikeCount: LikeCount
                            },     // data
                            dataType: 'json',               // xml/json/script/html
                            cache: false,                   // 是否允許快取
                             success: function (data) {
                                 str = $("#check1").html();
                                 $("#check1").html(str+"on1");
                                var res = jQuery.parseJSON(data);

                                if (res.Success == 0) { //失敗
                                    alert(res.Error);
                                }
                                else if (res.Success == 1) //成功
                                {
                                    str = $("#check1").html();
                                    $("#check1").html(str+"on2");
                                    FunPageProc(0)
                                    str = $("#check1").html();
                                    $("#check1").html(str + "on3");
                                    $("#check1").attr("style", "display:none");
                                    FunPageProc(nowpage);

                                }
                                else if (res.Success == 2) { //未輸入訪客名稱 或 未登入
                                    str = $("#check1").html();
                                    $("#check1").html(str+"on4");
                                    $('#LikeNickNameModal').modal('show');
                                    $('#PHOTO_NO').val(PHOTO_NO);
                                    $('#LikeCount').val(LikeCount);
                                    $('#LikeNickNameModal').on('shown.bs.modal', function () {
                                        $('#NICK_NAME').focus()
                                    })
                                    str = $("#check1").html();
                                    $("#check1").html(str + "on6");
                                    $("#check1").attr("style", "display:none");
                                 }

                            },
                             error: function (xhr, err) {
                                 str = $("#check1").html();
                                 $("#check1").html(str + "on7");
                                alert("readyState: " + xhr.readyState + "\nstatus: " + xhr.status);
                                 alert("responseText: " + xhr.responseText);
                                 $("#check1").attr("style", "display:none");
                            }

                        });
                    }
</script>