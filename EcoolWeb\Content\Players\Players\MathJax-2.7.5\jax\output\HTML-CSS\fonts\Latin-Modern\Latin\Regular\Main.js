/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Latin-Modern/Latin/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.LatinModernMathJax_Latin={directory:"Latin/Regular",family:"LatinModernMathJax_Latin",testString:"\u00A0\u00A1\u00A2\u00A4\u00A6\u00A9\u00AA\u00AB\u00B6\u00B8\u00BA\u00BB\u00BF\u00C0\u00C1",32:[0,0,332,0,0],160:[0,0,332,0,0],161:[500,216,278,86,192],162:[476,45,444,34,415],164:[492,-8,778,147,630],166:[750,250,278,119,159],169:[683,0,683,0,683],170:[705,-333,449,35,414],171:[483,0,556,111,444],182:[694,194,611,56,582],184:[5,200,444,89,356],186:[705,-333,419,24,396],187:[483,0,556,112,445],191:[500,205,472,56,415],192:[909,0,750,32,717],193:[909,0,750,32,717],194:[869,0,750,32,717],195:[829,0,750,32,717],196:[831,0,750,32,717],197:[892,0,750,32,717],198:[683,0,903,32,874],199:[705,200,722,56,665],200:[909,0,681,33,652],201:[909,0,681,33,652],202:[869,0,681,33,652],203:[831,0,681,33,652],204:[909,0,361,28,333],205:[909,0,361,28,333],206:[869,0,361,28,333],207:[831,0,361,28,335],208:[683,0,764,35,707],209:[829,0,750,33,716],210:[909,22,778,56,721],211:[909,22,778,56,721],212:[869,22,778,56,721],213:[829,22,778,56,721],214:[831,22,778,56,721],216:[739,56,778,56,721],217:[909,22,750,33,716],218:[909,22,750,33,716],219:[869,22,750,33,716],220:[831,22,750,33,716],221:[909,0,750,11,738],222:[683,0,625,36,569],223:[705,11,500,28,471],224:[698,11,500,32,483],225:[698,11,500,32,483],226:[692,11,500,32,483],227:[651,11,500,32,483],228:[652,11,500,32,483],229:[705,11,500,32,483],230:[448,11,722,45,693],231:[448,200,444,34,415],232:[698,11,444,28,415],233:[698,11,444,28,415],234:[692,11,444,28,415],235:[652,11,444,28,415],236:[698,0,278,15,247],237:[698,0,278,33,263],238:[692,0,278,-13,291],239:[652,0,278,2,277],241:[651,0,556,32,535],242:[698,11,500,28,471],243:[698,11,500,28,471],244:[692,11,500,28,471],245:[651,11,500,28,471],246:[652,11,500,28,471],248:[534,102,500,35,464],249:[698,11,556,32,535],250:[698,11,556,32,535],251:[692,11,556,32,535],252:[652,11,556,32,535],253:[698,205,528,19,508],254:[694,194,556,28,521],255:[652,205,528,19,508],256:[787,0,750,32,717],257:[620,11,500,32,483],258:[918,0,750,32,717],259:[690,11,500,32,483],260:[716,211,750,32,717],261:[448,211,500,32,483],262:[909,22,722,56,665],263:[698,11,444,34,415],268:[869,22,722,56,665],269:[692,11,444,34,415],270:[869,0,764,35,707],271:[694,11,556,34,574],272:[683,0,764,35,707],274:[787,0,681,33,652],275:[620,11,444,28,415],278:[864,0,681,33,652],279:[657,11,444,28,415],280:[680,211,681,33,652],281:[448,211,444,28,415],282:[869,0,681,33,652],283:[692,11,444,28,415],286:[918,22,785,56,735],287:[690,206,500,28,485],290:[705,290,785,56,735],291:[738,206,500,28,485],296:[829,0,361,15,348],297:[651,0,278,-27,306],298:[787,0,361,1,362],299:[620,0,278,-41,320],302:[683,211,361,28,333],303:[669,211,278,24,253],304:[864,0,361,28,333],306:[683,22,839,28,790],307:[657,205,556,33,461],310:[683,290,778,33,736],311:[694,290,528,28,511],313:[909,0,625,33,582],314:[920,0,278,33,276],315:[683,290,625,33,582],316:[694,290,278,33,255],317:[683,0,625,33,582],318:[694,0,278,33,302],321:[683,0,625,33,582],322:[694,0,336,48,286],323:[909,0,750,33,716],324:[698,0,556,32,535],325:[683,290,750,33,716],326:[442,290,556,32,535],327:[869,0,750,33,716],328:[692,0,556,32,535],330:[705,22,750,28,713],331:[442,216,506,32,457],332:[787,22,778,56,721],333:[620,11,500,28,471],336:[932,22,778,56,721],337:[697,11,500,28,471],338:[705,22,1014,70,985],339:[448,11,778,28,749],340:[909,22,736,35,732],341:[698,0,392,28,364],342:[683,290,736,35,732],343:[442,290,392,28,364],344:[869,22,736,35,732],345:[692,0,392,28,364],346:[909,22,556,56,499],347:[698,11,394,33,360],350:[705,200,556,56,499],351:[448,200,394,33,360],352:[869,22,556,56,499],353:[692,11,394,33,360],354:[677,200,722,36,685],355:[615,200,389,19,347],356:[869,0,722,36,685],357:[692,11,389,19,332],360:[829,22,750,33,716],361:[651,11,556,32,535],362:[787,22,750,33,716],363:[620,11,556,32,535],366:[892,22,750,33,716],367:[705,11,556,32,535],368:[932,22,750,33,716],369:[697,11,556,32,535],370:[683,211,750,33,716],371:[442,211,556,32,555],376:[831,0,750,11,738],377:[909,0,611,56,560],378:[698,0,444,28,401],379:[864,0,611,56,560],380:[657,0,444,28,401],381:[869,0,611,56,560],382:[692,0,444,28,401],383:[705,0,306,33,357],416:[789,22,778,55,720],417:[536,11,500,28,488],431:[789,22,750,33,726],432:[536,11,556,32,571],536:[705,290,556,56,499],537:[448,290,394,33,360],538:[677,290,722,36,685],539:[615,290,389,19,332],7840:[716,200,750,32,717],7841:[448,200,500,32,483],7842:[967,0,750,32,717],7843:[709,11,500,32,483],7844:[1052,0,750,32,717],7845:[832,11,500,32,483],7846:[1052,0,750,32,717],7847:[832,11,500,32,483],7848:[1078,0,750,32,717],7849:[858,11,500,32,483],7850:[1002,0,750,32,717],7851:[782,11,500,32,483],7852:[869,200,750,32,717],7853:[692,200,500,32,483],7854:[1101,0,750,32,717],7855:[873,11,500,32,483],7856:[1101,0,750,32,717],7857:[873,11,500,32,483],7858:[1127,0,750,32,717],7859:[899,11,500,32,483],7860:[1051,0,750,32,717],7861:[823,11,500,32,483],7862:[918,200,750,32,717],7863:[690,200,500,32,483],7864:[680,200,681,33,652],7865:[448,200,444,28,415],7866:[967,0,681,33,652],7867:[709,11,444,28,415],7868:[829,0,681,33,652],7869:[651,11,444,28,415],7870:[1052,0,681,33,652],7871:[832,11,444,28,415],7872:[1052,0,681,33,652],7873:[832,11,444,28,415],7874:[1078,0,681,33,652],7875:[858,11,444,28,416],7876:[1002,0,681,33,652],7877:[782,11,444,28,415],7878:[869,200,681,33,652],7879:[692,200,444,28,415],7880:[967,0,361,28,333],7881:[709,0,278,33,247],7882:[683,200,361,28,333],7883:[657,200,278,33,247],7884:[705,200,778,56,721],7885:[448,200,500,28,471],7886:[967,22,778,56,721],7887:[709,11,500,28,471],7888:[1052,22,778,56,721],7889:[832,11,500,28,471],7890:[1052,22,778,56,721],7891:[832,11,500,28,471],7892:[1078,22,778,56,721],7893:[858,11,500,28,471],7894:[1002,22,778,56,721],7895:[782,11,500,28,471],7896:[869,200,778,56,721],7897:[692,200,500,28,471],7898:[909,22,778,55,720],7899:[698,11,500,28,488],7900:[909,22,778,55,720],7901:[698,11,500,28,488],7902:[967,22,778,55,720],7903:[709,11,500,28,488],7904:[829,22,778,55,720],7905:[651,11,500,28,488],7906:[789,200,778,55,720],7907:[536,200,500,28,488],7908:[683,200,750,33,716],7909:[442,200,556,32,535],7910:[967,22,750,33,716],7911:[709,11,556,32,535],7912:[909,22,750,33,726],7913:[698,11,556,32,571],7914:[909,22,750,33,726],7915:[698,11,556,32,571],7916:[967,22,750,33,726],7917:[709,11,556,32,571],7918:[829,22,750,33,726],7919:[651,11,556,32,571],7920:[789,200,750,33,726],7921:[536,200,556,32,571],7922:[909,0,750,11,738],7923:[698,205,528,19,508],7924:[683,200,750,11,738],7925:[431,205,528,19,508],7926:[967,0,750,11,738],7927:[709,205,528,19,508],7928:[829,0,750,11,738],7929:[651,205,528,19,508],64256:[705,0,583,27,628],64257:[705,0,556,27,527],64258:[705,0,556,27,527],64259:[705,0,833,27,804],64260:[705,0,833,27,804]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"LatinModernMathJax_Latin"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Latin/Regular/Main.js"]);
