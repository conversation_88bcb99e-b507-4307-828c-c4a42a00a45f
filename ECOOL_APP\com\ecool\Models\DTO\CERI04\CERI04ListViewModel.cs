﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class CERI04ListViewModel
    {
        public string ACCREDITATION_TYPE { get; set; }

        public string ACCREDITATION_ID { get; set; }

        [DisplayName("護照")]
        public string TYPE_NAME { get; set; }

        /// <summary>
        ///護照細項名稱
        /// </summary>
        [DisplayName("細項名稱")]
        public string ACCREDITATION_NAME { get; set; }

        /// <summary>
        ///學校代碼
        /// </summary>
        [DisplayName("學校代碼")]
        public string SCHOOL_NO { get; set; }

        /// <summary>
        ///年級
        /// </summary>
        [DisplayName("年級")]
        public byte? GRADE { get; set; }

        public string ITEM_NO { get; set; }

        /// <summary>
        ///班級
        /// </summary>
        [DisplayName("班級")]
        public string CLASS_NO { get; set; }

        /// <summary>
        ///通過主旨
        /// </summary>
        [DisplayName("通過主旨")]
        public string SUBJECT { get; set; }

        /// <summary>
        ///通過內容
        /// </summary>
        [DisplayName("通過內容")]
        public string CONTENT { get; set; }

        /// <summary>
        ///異動日期
        /// </summary>
        [DisplayName("最後異動時間")]
        public string VERIFIED_DATE { get; set; }

        /// <summary>
        /// 認證老師
        /// </summary>
        [DisplayName("認證老師")]
        public string VERIFIER { get; set; }

        public string VERIFIER_STR { get; set; }

        public int CLASS_COUNT { get; set; }

        public int CLASS_PASS_COUNT { get; set; }

        public short CASH { get; set; }
        public string IsText { get; set; }
    }
}