﻿@{
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    if (string.IsNullOrWhiteSpace(UUID) == false)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
    else
    {
        if (ViewData["Layout"] != null)
        {
            Layout = (string)ViewData["Layout"];
        }
    }

    if (Layout == null)
    {
        <link href="~/Content/css/bootstrap.css;" rel="stylesheet" />
        <link href="~/Content/css/EzCss.css;" rel="stylesheet" />
    }

    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

    string ActionName = SharedGlobal.HomeIndex;

    if (user != null)
    {
        if (user.USER_TYPE == UserType.Student)
        {
            ActionName = "StudentIndex";
        }
        else if (user.USER_TYPE == UserType.Admin || user.USER_TYPE == UserType.Teacher)
        {
            ActionName = "TeacherIndex";
        }
        else
        {
            ActionName = "GuestIndex";
        }
    }

}

@if (SharedGlobal.IsMenuLayout(Layout) == false)
{
    <div style="margin: 0px auto;text-align:center">
        <img src="~/Content/img/@SharedGlobal.Logo" style="margin: 0px auto;text-align:center;" class="img-responsive" />
    </div>

    <div class="col-xs-12 text-center">
        <br />
        <a role="button" class="btn btn-sm btn-sys" href="@Url.Action(ActionName,"Home")">回首頁</a>
    </div>

}

<div class="text-center">
    <br />    <br />    <br />    <br />    <br />    <br />  <br />    <br />    <br />
    <h1><b>《系統訊息》</b></h1>
    <h2><b>@ViewData["Title"]</b></h2>
    <h5>@ViewData["Description"]</h5>
    <h5>@ViewData["HttpCode"]</h5>
</div>