﻿@{
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

    string DateNowStr = DateTime.Now.ToString("yyyy_MM_dd_H_mm_ss");

    if (System.Web.Configuration.WebConfigurationManager.AppSettings["TestMode"] == "true")
    {
        ViewBag.Title = "兒童月測試頁";
    }
    else
    {
        ViewBag.Title = "兒童月首頁";
    }

    EcoolWeb.Models.UserProfileHelper.SetLayoutCookieData("~/Views/Shared/_LayoutChildMonth.cshtml");
}
<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@ViewBag.Title</title>
    @Styles.Render("~/Content/css")
    <link href="~/Content/css/EzCss.css?@DateNowStr" rel="stylesheet" />
    <link href="~/Content/font-awesome/css/font-awesome.min.css" rel="stylesheet" />
    @Scripts.Render("~/bundles/jquery")
    @Scripts.Render("~/bundles/modernizr")
    @Scripts.Render("~/bundles/bootstrap")
    @RenderSection("css", required: false)
    <!--[if lte IE 9 ]>
        <style type="text/css">
            .schoolselector {
                filter: progid:DXImageTransform.Microsoft.AlphaImageLoader(
                src='@Url.Content("~/Content/img/web-19plus-body.png")',
                sizingMethod='scale');

                -ms-filter: "progid:DXImageTransform.Microsoft.AlphaImageLoader(
                src='@Url.Content("~/Content/img/web-19plus-body.png")',
                sizingMethod='scale')";
            }
    </style>
    <![endif]-->
    <!--[if !IE]><!-->
    <style type="text/css">
          .schoolselector {
        background-image:url('@Url.Content("~/Content/img/web-19plus-body.png")');
        }
    </style>
    <!--<![endif]-->
    <!--[if !IE]><!-->
    <style type="text/css">
        .modal {
            text-align: center;
            padding: 0 !important;
        }

            .modal:before {
                content: '';
                display: inline-block;
                height: 100%;
                vertical-align: middle;
                margin-right: -4px;
            }

        .modal-dialog {
            display: inline-block;
            text-align: left;
            vertical-align: middle;
        }

        .modal-content {
            -webkit-border-radius: 0px !important;
            -moz-border-radius: 0px !important;
            border-radius: 0px !important;
        }

        .selector {
            background-image: url('@Url.Content("~/Content/img/web_png-11body.png")');
        }
    </style>
    <!--<![endif]-->

    <style type="text/css">
        .classInputCode {
            display: none
        }

        /*兒童月活動首頁 by suhan*/
        body {
            background: #fffde3;
        }

        h1 {
            margin: 0;
        }

        .mt-1 {
            margin-top: 1.5rem;
        }

        .navbar-phone {
            background-color: #6ebce1 !important;
            border-bottom: 1px solid transparent !important;
        }

        .btn-User {
            padding: 1rem 1.5rem;
            margin: .5rem !important;
            display: inline-block;
            background-color: #009cd3 !important;
            color: #fff !important;
            border-radius: 4rem;
        }

        .btn-svg {
            display: block;
            margin: .5rem auto;
            height: 5rem;
            fill: #ffffff;
        }

        .btn-map {
            margin-bottom: 1.5rem;
            background-color: #0090db;
            border: 3px dashed #86c9ff;
            box-shadow: 0 0 0 3px #0090db, 0 2px 3px 2px #bc6632;
            background-image: repeating-linear-gradient(45deg, transparent 5px, hsla(217, 100%, 30%, 0.2) 5px, hsla(217, 100%, 30%, 0.2) 10px, hsla(5, 53%, 63%, 0) 10px, hsla(5, 53%, 63%, 0) 35px, hsl(206, 58%, 62%, 0.5) 35px, hsl(206, 58%, 62%, 0.5) 40px, hsla(217, 100%, 30%, 0.2) 40px, hsla(217, 100%, 30%, 0.2) 50px, hsla(197, 62%, 11%, 0) 50px, hsla(197, 62%, 11%, 0) 60px, hsl(206, 58%, 62%, 0.5) 60px, hsl(206, 58%, 62%, 0.5) 70px, hsla(242, 49%, 55%, 0.5) 70px, hsla(242, 49%, 55%, 0.5) 80px, hsla(242, 49%, 55%, 0.5) 80px, hsla(242, 49%, 55%, 0.5) 90px, hsl(206, 58%, 62%, 0.5) 90px, hsl(206, 58%, 62%, 0.5) 110px, hsla(5, 53%, 63%, 0) 110px, hsla(5, 53%, 63%, 0) 120px, hsla(217, 100%, 30%, 0.2) 120px, hsla(217, 100%, 30%, 0.2) 140px), repeating-linear-gradient(135deg, transparent 5px, hsla(217, 100%, 30%, 0.2) 5px, hsla(217, 100%, 30%, 0.2) 10px, hsla(5, 53%, 63%, 0) 10px, hsla(5, 53%, 63%, 0) 35px, hsl(206, 58%, 62%, 0.5) 35px, hsl(206, 58%, 62%, 0.5) 40px, hsla(217, 100%, 30%, 0.2) 40px, hsla(217, 100%, 30%, 0.2) 50px, hsla(197, 62%, 11%, 0) 50px, hsla(197, 62%, 11%, 0) 60px, hsl(206, 58%, 62%, 0.5) 60px, hsl(206, 58%, 62%, 0.5) 70px, hsla(242, 49%, 55%, 0.5) 70px, hsla(242, 49%, 55%, 0.5) 80px, hsla(242, 49%, 55%, 0.5) 80px, hsla(242, 49%, 55%, 0.5) 90px, hsl(206, 58%, 62%, 0.5) 90px, hsl(206, 58%, 62%, 0.5) 110px, hsla(5, 53%, 63%, 0) 110px, hsla(5, 53%, 63%, 0) 140px, hsla(217, 100%, 30%, 0.2) 140px, hsla(217, 100%, 30%, 0.2) 160px);
        }

            .btn-map:hover {
                border-color: #073080;
                background-color: #0070ff;
            }

        .btn-award {
            background-color: #e83f4c;
            border: 7px double #ff7c32;
            box-shadow: 0 0 0 3px #e83f4c, 0 2px 3px 2px #bc6632;
            ;
        }

            .btn-award:hover {
                border-color: #940000;
                background-color: #c71d0e;
            }

        .table-login td {
            text-align: center;
        }

        .table-login table {
            margin: 0 auto;
        }

        /*彩帶基本結構*/
        .ribbon {
            font-size: 1.93rem;
            position: relative;
            color: #fff;
            text-align: center;
            padding: .2rem 0;
            margin: 1.5rem 10.5rem 0 10.5rem;
            display: block;
        }

            .ribbon:before,
            .ribbon:after {
                content: "";
                position: absolute;
                display: block;
                bottom: 1.5rem;
                border: 1.2rem solid;
                z-index: -1;
                width: 13rem;
            }

            .ribbon:before {
                right: -11.5rem;
                border-left-width: 12rem;
            }

            .ribbon:after {
                left: -11.5rem;
                border-right-width: 12rem;
            }

            .ribbon .ribbon-content:before,
            .ribbon .ribbon-content:after {
                content: "";
                position: absolute;
                display: block;
                border-style: solid;
                bottom: 2.6rem;
            }

            .ribbon .ribbon-content:before {
                left: -1px;
                border-width: 0 0 1.3rem 1.7rem;
            }

            .ribbon .ribbon-content:after {
                right: -1px;
                border-width: 1.3rem 0 0 1.7rem;
            }

        /*彩帶顏色1*/
        .ribbon-color1 {
            background: #ffdeaf;
            color: #824200;
        }

            .ribbon-color1:before,
            .ribbon-color1:after {
                border-color: #facb9c;
            }

            .ribbon-color1:before {
                border-right-color: transparent;
            }

            .ribbon-color1:after {
                border-left-color: transparent;
            }

            .ribbon-color1 .ribbon-content:before {
                border-color: transparent transparent #c38240 transparent;
            }

            .ribbon-color1 .ribbon-content:after {
                border-color: transparent transparent transparent #c38240;
            }

        /*彩帶顏色2*/
        .ribbon-color2 {
            background: #e6d8fc;
            color: #5317ac;
        }

            .ribbon-color2:before,
            .ribbon-color2:after {
                border-color: #d3bef4;
            }

            .ribbon-color2:before {
                border-right-color: transparent;
            }

            .ribbon-color2:after {
                border-left-color: transparent;
            }

            .ribbon-color2 .ribbon-content:before {
                border-color: transparent transparent #977cc3 transparent;
            }

            .ribbon-color2 .ribbon-content:after {
                border-color: transparent transparent transparent #977cc3;
            }

        @@media (max-width: 767px) {
            body {
                padding-top: 3rem;
            }

            .ribbon {
                margin-right: 1.5rem;
                margin-left: 1.5rem;
            }

                .ribbon:before,
                .ribbon:after {
                    width: 3rem;
                }

                .ribbon:before {
                    right: -1.5rem;
                    border-left-width: 2rem;
                }

                .ribbon:after {
                    left: -1.5rem;
                    border-right-width: 2rem;
                }
        }
    </style>
    <!--[if lt IE 9]>
      <script src="~/Scripts/html5shiv.min.js"></script>
      <script src="~/Scripts/respond.min.js"></script>
    <![endif]-->
    <script src="~/Scripts/respond.min.js"></script>
    @{ Html.RenderPartial("_GoogleAnalytics"); }
</head>

@Html.Partial("../Shared/_CheckBrowser")
<body>
    <!---手機 login-->
    <div class="visible-xs">
        <!---手機 -->
        <nav class="navbar navbar-phone navbar-fixed-top" role="navigation">

            @if (user != null)
            {
                <div class="navbar-header pull-left">
                    <button type="button" class="btn btn-navbar" data-toggle="collapse" data-target=".navbar-ex1-collapse" title="Menu">
                        <span class="sr-only">Toggle navigation</span>
                        <span class="icon-bar"></span>
                        <span class="icon-bar"></span>
                        <span class="icon-bar"></span>
                    </button>

                    <a class="line-left"></a>
                </div>
            }

            @if (user == null)
            {
                <a role="button" class="btn-User" href="@Url.Action("LoginChildMonthPage","Home")">
                    <i class="fa fa-user" style="font-size:2.5em" title="會員登入"></i>
                </a>
            }
            else
            {
                <a role="button" class="btn-User" href="@Url.Action("Logout","Home")">
                    <i class="fa fa-power-off" style="font-size:2.5em" title="登出"></i>
                </a>
            }
            <a class="line-right"></a>
        </nav>
    </div>

    <!---手機/電腦 活動視覺圖 -->
    <h1>
        <a href="@Url.Action(SharedGlobal.HomeIndex,"Home")">
            <img src='@Url.Content("~/Content/img/childrensMonth-insidePic.png")' class="img-responsive visible-xs" alt="兒童月" />
        </a>

        <a href="@Url.Action(SharedGlobal.HomeIndex,"Home")">
            <img src="@Url.Content("~/Content/img/childrensMonth-banner-PC.png")" style="width:100%" class="img-responsive hidden-xs"
                 alt="兒童月" />
        </a>
       
    </h1>

    <div class="containerEZ">
        <div class="row">
            <div class="col-lg-3 col-md-3 col-sm-3">
                @Html.Action("ChildMonthMenu", "Menu")
            </div>

            <div class="col-lg-7 col-md-7 col-sm-7 col-xs-12">
                @RenderBody()
            </div>
        </div>
    </div>
    @RenderSection("scripts", required: false)
</body>
</html>