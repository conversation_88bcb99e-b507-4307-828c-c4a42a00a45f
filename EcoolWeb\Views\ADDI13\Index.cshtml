﻿@model ADDI13IndexViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";

    string SCHOOL_NO = EcoolWeb.Models.UserProfileHelper.GetSchoolNo();
    string LogoAct = "GuestIndex";

}

@Html.Partial("_Notice")

@using (Html.BeginForm("Index", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", id = "form1", @AutoComplete = "Off", @target = "_blank" }))
{
    @Html.AntiForgeryToken()
    @Html.HiddenFor(m => m.ROLL_CALL_ID)
    <div class="row">
        <div class="col-md-8 col-md-offset-2">
            <div class="panel with-nav-tabs panel-info" id="panel">
                <div class="panel-heading">
                    <h1>@ViewBag.Title</h1>
                </div>
                <div class="panel-body">
                    <div>
                        <div class="input-group input-group-lg">
                            <span class="input-group-addon"><i class="fa fa-user"></i></span>
                            @Html.Editor("CARD_NO", new { htmlAttributes = new { @class = "form-control", @placeholder = "請用數位學生證感應", @onKeyPress = "call(event,this);" } })
                        </div>
                        @Html.ValidationMessage("CARD_NO", "", new { @class = "text-danger" })
                    </div>
                </div>
            </div>
            <div id="DetailsView">
                <div style="margin-top:5px;margin-bottom:5px;text-align:center">
                    <span class="glyphicon glyphicon-circle-arrow-down fa-2x" aria-hidden="true" style="color:#004da0"></span>
                </div>
                @Html.ValidationMessage("DetailsError", new { @class = "text-danger" })
                <div class="panel panel-ZZZ">
                    <div class="panel-heading text-center">
                        報到清單
                    </div>
                    <div class="panel-body">
                        <div class="table-responsive">
                            <div class="css-table" style="width:92%;" id="tbData">
                                <div class="tr" style=" font-weight: bold;text-align:center;color:#004da0;text-overflow:ellipsis; ">
                                    <div class="th" style="text-align:center">
                                        刪除
                                    </div>
                                    <div class="th" style="text-align:center">
                                        學校
                                    </div>
                                    <div class="th" style="text-align:center">
                                        全名
                                    </div>
                                    <div class="th" style="text-align:center">
                                        學號
                                    </div>
                                    <div class="th" style="text-align:center">
                                        班級
                                    </div>
                                    <div class="th" style="text-align:center">
                                        座號
                                    </div>
                                    <div class="th" style="text-align:center">
                                        日期
                                    </div>
                                </div>
                                <div id="editorRows" class="tbody">
                                    @if (Model != null && Model.Details != null && Model.Details.Count() > 0)
                                    {
                                        @Html.Action("_EditDetailsList", (string)ViewBag.BRE_NO, new { model = Model.Details.ToList() })
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                  

                    <div class="form-group">
                        <div class="col-xs-12 text-center">
                            <div class="form-inline">
                                <input type="button" value="匯出名單" class="btn btn-default" onclick="ExportSave()" />
                                <input type="button" value="點名完畢，儲存" class="btn btn-default" onclick="onSave()" />
                                <a class="btn btn-default" href="@Url.Action("GuestIndex","Home")">回首頁</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-6 col-lg-offset-3">
            <div class="use-absolute" id="ErrorDiv">
                <div class="use-absoluteDiv">
                    <div class="alert alert-danger" role="alert">
                        <h1>
                            <i class="fa fa-exclamation-circle"></i>
                            <strong id="ErrorStr"></strong>
                        </h1>
                    </div>
                </div>
            </div>
        </div>
    </div>
}

@section Scripts {
    <script src="~/Scripts/buzz/buzz.min.js"></script>
    <script language="JavaScript">
        var targetFormID = '#form1';

        var SwipeSound = new buzz.sound("@Url.Content("~/Content/mp3/Swipe1.mp3")" );

        (function ($) {
         
            $(window).on("beforeunload", function () {
    return true;
})
        })(jQuery);

        function ExportSave()
        {
            $(targetFormID).attr("action", "@Url.Action("Export", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        function onSave() {
             var OK = confirm("您確定點完名了?")

            if (OK==true)
            {

                 $(targetFormID).attr("target","_self")
                 $(targetFormID).attr("action", "@Url.Action("RollCallSave", (string)ViewBag.BRE_NO)")
                 $(targetFormID).submit();
            }
        }

        $(document).ready(function () {
            $("#CARD_NO").focus();

        });

        function call(e, input) {
            var code = (e.keyCode ? e.keyCode : e.which);

            if (code == 13) // 13 是 Enter 按鍵的值
            {
                var CARD_NO = $('#CARD_NO').val();

                event.preventDefault();

                if (CARD_NO != '') {

                    if ($("#Tr" + CARD_NO).length > 0 || $("#@(SCHOOL_NO)" + CARD_NO).length >0 ) {
                        $('#ErrorStr').html('請勿重複刷卡，謝謝')
                        $('#ErrorDiv').show()

                        setTimeout(function () {
                            $('#CARD_NO').val('')
                            $("#CARD_NO").focus();
                            $('#ErrorStr').val('')
                            $('#ErrorDiv').hide()
                        }, 2000);
                    }
                    else {

                        $('#CARD_NO').prop('readonly', true);

                        setTimeout(function () {
                            OnKeyinUse(CARD_NO)
                        });
                    }

                }
            }
        }

        function OnKeyinUse(CARD_NO) {

            var data = {
                "CARD_NO": CARD_NO
             };

            $.ajax({
                url: '@Url.Action("_OpenPersonAddData", (string)ViewBag.BRE_NO)',
                data: data,
                cache: false,
                success: function (html) {
                    $("#editorRows").prepend(html);
                    $('#ErrorDiv').show()
                    if (html.length>0) {
                        $('#ErrorStr').html('感應成功…')
                        SwipeOK()
                        setTimeout(function () {
                            $('#CARD_NO').prop('readonly', false);
                            $('#CARD_NO').val('')
                            $("#CARD_NO").focus();
                            $('#ErrorDiv').hide()
                            $('#ErrorStr').html('');
                        }, 300);
                    }
                    else {
                        $('#ErrorStr').html('此數位學生證對應不到學生資料…')
                        setTimeout(function () {
                            $('#CARD_NO').prop('readonly', false);
                            $('#CARD_NO').val('')
                            $("#CARD_NO").focus();
                            $('#ErrorDiv').hide()
                            $('#ErrorStr').html('');
                        }, 1300);
                    }

                }
            });
        }

         function SwipeOK() {

                 SwipeSound.play();
         }

        //del 項目
        function deleteRow(Id) {
            $('#' + Id).remove();
        }
    </script>
}