﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class REFT01EditListViewModel
    {
        /// <summary>
        /// 來源TABLE
        /// </summary>
        public string REF_TABLE { get; set; }

        /// <summary>
        /// 來源KEY
        /// </summary>
        public string REF_KEY { get; set; }

        /// <summary>
        /// TABLE 資料狀態
        /// </summary>
        public byte STATUS { get; set; }

        /// <summary>
        /// 點選的功能類別
        /// </summary>
        public string BTN_TYPE { get; set; }

        /// <summary>
        /// 點選的功能類別細項
        /// </summary>
        public string ITEM_VAL { get; set; }

        /// <summary>
        /// 學校
        /// </summary>
        public string whereSCHOOL_NO { get; set; }

        /// <summary>
        /// 已選取人數
        /// </summary>
        public int SelectDataCount { get; set; }

        /// <summary>
        /// 選取清單
        /// </summary>
        public List<REFT01QListViewModel> DataList { get; set; }

        /// <summary>
        /// 選取清單
        /// </summary>
        public List<REFT01QListViewModel> CHECKDataList { get; set; }

        /// <summary>
        /// 已選Div 高度 + 跟筆數一起拉大 , - 固定200pt
        /// </summary>
        public string DivHeight { get; set; }

        /// <summary>
        /// ErrorMsg
        /// </summary>
        public string ErrorMsg { get; set; }

        public string DataType { get; set; }

        static public class DataTypeVal
        {
            public static string DataTypeAdd = "Add";

            public static string DataTypeDel = "Del";
        }

        static public class DivHeightVal
        {
            public static string DivHeightP = "+";

            public static string DivHeightM = "-";
        }

        public REFT01EditListViewModel()
        {
            STATUS = REFT01.StatusVal.TempKey;
            BTN_TYPE = REFT01_Q.BTN_TYPE_VAL.sys_role;
            ErrorMsg = string.Empty;
        }
    }
}