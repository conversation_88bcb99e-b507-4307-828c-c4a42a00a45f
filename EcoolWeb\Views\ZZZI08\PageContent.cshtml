﻿@model ECOOL_APP.com.ecool.Models.DTO.ZZZI08ListViewModel
<link href="~/Content/css/EzCss.css" rel="stylesheet" />

@if (Model.Q_COPY_YN == "N")
{
    @Html.PermissionButton("新增有獎徵答活動", "button", (string)ViewBag.BRE_NO, "Save", new { @class = "btn btn-sm btn-sys", @onclick = "onAdd()" }, 2, ViewBag.BtnPermission as List<ECOOL_APP.ControllerPermissionfile>)

}

@Html.Hidden("uADDT11.DIALOG_ID")
@Html.HiddenFor(model => model.SearchType)
@Html.HiddenFor(model => model.VIEW_DATA_TYPE)
@Html.HiddenFor(model => model.OrderByName)
@Html.HiddenFor(model => model.SyntaxName)
@Html.HiddenFor(model => model.Page)
@Html.HiddenFor(model => model.Q_COPY_YN)

@if (Model.SearchType == 1)
{

    <div class="form-inline" role="form" id="Q_Div">
        @if (Model.Q_COPY_YN == "Y")
        {
            <div class="form-group">
                <label class="control-label">學校</label>
                <div class="form-group">
                    @Html.DropDownListFor(m => m.Q_SCHOOL_NO, (IEnumerable<SelectListItem>)ViewBag.SchoolNoSelectItem, new { @class = "form-control", @onchange = "FunPageProc(1)" })
                </div>
            </div>
            <br />

        }
        <div class="form-group">
            <label class="control-label">
                @Html.DisplayNameFor(model => model.ADDT11List.First().SYEAR)
                /@Html.DisplayNameFor(model => model.ADDT11List.First().DIALOG_NAME)
                /@Html.DisplayNameFor(model => model.ADDT11List.First().SNAME)
                /@Html.DisplayNameFor(model => model.ADDT11List.First().DIALOG_SDATE)
                /@Html.DisplayNameFor(model => model.ADDT11List.First().DIALOG_EDATE)
            </label>
        </div>
        <div class="form-group">
            @Html.EditorFor(model => model.SearchContents, new { htmlAttributes = new { @class = "form-control" } })
        </div>

        <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1)" />
        <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />
    </div>
}

<img src="~/Content/img/web-bar2-revise-07.png" class="img-responsive" alt="Responsive image" />
<div class="table-responsive">
    <table class="table-ecool table-92Per table-hover table-ecool-ADDO05">
        <thead>
            <tr>
                @if (Model.Q_COPY_YN == "Y")
                {
                    <th>
                        帶入
                    </th>
                    <th>
                        預覽
                    </th>
                }
                else
                {
                    <th>
                        修改
                    </th>
                    <th>
                        刪除
                    </th>
                }
                <th>

                    <samp class="form-group" style="cursor:pointer;" onclick="FunSort('SHORT_NAME')">
                        @Html.DisplayNameFor(model => model.ADDT11List.First().SHORT_NAME)
                    </samp>
                </th>
                <th>

                    <samp class="form-group" style="cursor:pointer;" onclick="FunSort('SYEAR')">
                        @Html.DisplayNameFor(model => model.ADDT11List.First().SYEAR)
                    </samp>
                </th>
                <th>
                    <samp class="form-group" style="cursor:pointer;" onclick="FunSort('SEMESTER')">
                        @Html.DisplayNameFor(model => model.ADDT11List.First().SEMESTER)
                    </samp>
                </th>
                <th>
                    <samp class="form-group" style="cursor:pointer;" onclick="FunSort('DIALOG_NAME')">
                        @Html.DisplayNameFor(model => model.ADDT11List.First().DIALOG_NAME)
                    </samp>
                </th>
                <th>
                    <samp class="form-group" style="cursor:pointer;" onclick="FunSort('DIALOG_TYPE')">
                        @Html.DisplayNameFor(model => model.ADDT11List.First().DIALOG_TYPE)
                    </samp>
                </th>
                <th>
                    <samp class="form-group" style="cursor:pointer;" onclick="FunSort('SNAME')">
                        @Html.DisplayNameFor(model => model.ADDT11List.First().SNAME)
                    </samp>
                </th>
                <th>
                    <samp class="form-group" style="cursor:pointer;" onclick="FunSort('DIALOG_SDATE')">
                        @Html.DisplayNameFor(model => model.ADDT11List.First().DIALOG_SDATE)
                    </samp>
                </th>
                <th>
                    <samp class="form-group" style="cursor:pointer;" onclick="FunSort('DIALOG_EDATE')">
                        @Html.DisplayNameFor(model => model.ADDT11List.First().DIALOG_EDATE)
                    </samp>
                </th>
                <th>
                    @Html.DisplayNameFor(model => model.ADDT11List.First().STATUS_NAME)
                </th>
            </tr>
        </thead>
        <tbody>
            @foreach (var item in Model.ADDT11List)
            {
                <tr>
                    @if (Model.Q_COPY_YN == "Y")
                    {
                        <td align="center">
                            <button class="btn btn-xs btn-Basic" onclick="onBtnLink('@item.DIALOG_ID','@ViewBag.VIEW_C')">帶入</button>
                        </td>
                        <td align="center">
                            <button id="PreView@(item.DIALOG_ID)" class="btn btn-xs btn-Basic" role="button" href='@Url.Action("PreView", (string)ViewBag.BRE_NO, new { DIALOG_ID = item.DIALOG_ID, PreviewY="Y" })'>預覽</button>
                            <script>
                                $("#PreView@(item.DIALOG_ID)").colorbox({ iframe: true, width: "80%", height: "80%", opacity: 0.82 });
                            </script>
                        </td>
                    }
                    else
                    {
                        <td align="center">
                            <button class="btn btn-xs btn-Basic" onclick="onBtnLink('@item.DIALOG_ID','@ViewBag.VIEW_U')">修改</button>
                        </td>
                        <td align="center">
                            <button class="btn btn-xs btn-Basic" onclick="onBtnLink('@item.DIALOG_ID','@ViewBag.VIEW_D')">刪除</button>
                        </td>
                    }
                    <td align="center">
                        @Html.DisplayFor(modelItem => item.SHORT_NAME)
                    </td>
                    <td align="center">
                        @Html.DisplayFor(modelItem => item.SYEAR)
                    </td>
                    <td align="center">
                        @Html.DisplayFor(modelItem => item.SEMESTER)
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.DIALOG_NAME)
                    </td>
                    <td>
                        @ADDT11.GetDialogTypeDesc((byte?)item.DIALOG_TYPE)
                    </td>
                    <td align="center">
                        @Html.DisplayFor(modelItem => item.SNAME)
                    </td>
                    <td align="center">
                        @Html.DisplayFor(modelItem => item.DIALOG_SDATE)
                    </td>
                    <td align="center">
                        @Html.DisplayFor(modelItem => item.DIALOG_EDATE)
                    </td>
                    <td align="center">
                        @Html.DisplayFor(modelItem => item.STATUS_NAME)
                    </td>
                </tr>
            }
        </tbody>
    </table>
</div>

<div>
    @Html.Pager(Model.ADDT11List.PageSize, Model.ADDT11List.PageNumber, Model.ADDT11List.TotalItemCount).Options(o => o
    .DisplayTemplate("BootstrapPagination")
    .MaxNrOfPages(5)
    .SetPreviousPageText("上頁")
    .SetNextPageText("下頁")
    .AlwaysAddFirstPageNumber()
)
</div>