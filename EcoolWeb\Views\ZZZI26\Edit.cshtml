﻿@model ECOOL_APP.com.ecool.Models.DTO.ZZZI26EditViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;
}
<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>
@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")
@*
        <div class="alert alert-dismissible alert-danger">
             <button class="close" type="button" data-dismiss="alert">×</button>
                <strong>
                <strong>
                        @if(ViewBag.SumCount != null) {
                         if (Model.Details_List.Count() == ViewBag.SumCount)
                         {
                             <samp> 批次代閱讀認證作業 - 全部成功</samp>
                         }
                         else if (ViewBag.SumCount == ViewBag.ErrCount)
                         {
                             <samp>批次代閱讀認證作業 - 全部失敗</samp>
                         }
                         else
                         {
                             <samp>批次代閱讀認證作業 - 有一部份失敗</samp>
                         }
                        }
                     </strong>
    </div>*@



@using (Html.BeginForm("Edit", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", enctype = "multipart/form-data", autocomplete = "off" }))
{
    @Html.AntiForgeryToken()

    <img src="~/Content/img/web-bar3-revise-21.png" style="width:100%" class="img-responsive " alt="Responsive image" />
    if (ViewBag.FailList != null)
    {
        <div class="Div-EZ-reader">
            <div class="Details">
                <div class="table-responsive">
                    <div class="text-center">
                        <table class="table-ecool table-92Per table-hover">
                            <caption class="Caption_Div_Left">
                                失敗清單：
                            </caption>
                            <thead>
                                <tr>
                                    <th>檔名</th>
                                    <th>訊息</th>
                                </tr>
                            </thead>
                            <tbody>

                                @foreach (var item in (Dictionary<string, string>)ViewBag.FailList)
                                {
                                    if (item.Value != string.Empty && item.Value != null)
                                    {
                                        <tr>
                                            <td>@item.Key</td>
                                            <td>@item.Value</td>
                                        </tr>
                                    }
                                }

                            </tbody>
                        </table>
                        <div style="height:15px"></div>
                        <div class="btn-group btn-group-justified" role="group">
                            共 @ViewBag.ErrCount 人
                        </div>
                    </div>
                </div>
            </div>
        </div> }
    <div class="Div-EZ-ZZZI26">
        <div class="form-horizontal">
            <label class="control-label">@ViewBag.Panel_Title</label>
            <br /><br />
            <div>
                @{ 

                    string Bookstr = ViewBag.Panel_Title;
                    int BookstrInt = 1;
                    BookstrInt = Bookstr.IndexOf("多學生單一書本(ZIP)");

                }
                @if (BookstrInt == -1)
                {
                <input class='prompttest' type="button" value="書名不記錄" style="float:right">
                }
                @*<button onclick="NOBOOKNAME()">書名不紀錄</button>*@
            </div>
            <div class="table-responsive">
                @Html.ValidationMessage("DetailsError", new { @class = "text-danger" })
                <table class="table-ecool table-hover">
                    <thead>
                        <tr class="text-center">
                            <th>
                                序
                            </th>
                            <th>
                                刪
                            </th>
                            <th>
                                姓名 / 座號
                            </th>
                            <th>
                                書名
                            </th>
                            @if (ViewBag.VerifyUseYN == "Y")
                            {
                                <th>
                                    上傳
                                </th>
                            }
                            <th>
                                推薦
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        @{
                            <tr>
                                <td class="text-center"></td>
                                <td class="text-center"></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td class="text-center">全選<input onclick="CheckAll(this)" value="全選" type="checkbox" /></td>
                            </tr>
                            int Num = 0;

                            foreach (var Data in Model.Details_List)
                            {
                                <tr>
                                    <td class="text-center">
                                        @(Num + 1)
                                    </td>
                                    <td class="text-center">
                                        <input id="Details_List_Del_@Num" name="Details_List[@Num].Del" type="checkbox" value="true" @(Data.Del ? "checked=\"checked\"" : "") />
                                    </td>
                                    <td>
                                        <select class="form-control input-sm" name="Details_List[@Num].USER_NO" style="min-width:97px;">
                                            @{
                                                string SelectedVal = string.Empty;

                                                if (Model.Search.ModeVal == (byte)EcoolWeb.Controllers.ZZZI26Controller.Mode.ManyStudentIndex)
                                                {
                                                    <option value="">請選擇姓名...</option>
                                                }
                                                if (ViewBag.USER_NOItems != null)
                                                {
                                                    foreach (var item in ViewBag.USER_NOItems as IEnumerable<SelectListItem>)
                                                    {
                                                        SelectedVal = Data.USER_NO == item.Value ? "selected" : "";
                                                        <option value="@item.Value" @SelectedVal>@item.Text</option>
                                                    }
                                                }
                                            }
                                        </select>
                                        @Html.ValidationMessageFor(model => model.Details_List[Num].USER_NO, "", new { @class = "text-danger" })
                                    </td>
                                    <td>
                                        @Html.EditorFor(model => model.Details_List[Num].BOOK_NAME, new { htmlAttributes = new { @class = "form-control  input-sm", @style = "min-width:80px;max-width:97pt" } })
                                        @Html.ValidationMessageFor(model => model.Details_List[Num].BOOK_NAME, "", new { @class = "text-danger" })
                                    </td>
                                    @if (string.IsNullOrWhiteSpace(Model.Search.TEMP_BATCH_KEY) == false)
                                    {
                                        <td>
                                            <img class="img-responsive" src="@Url.Content(Data.IMG_FILE)" href="@Url.Content(Data.IMG_FILE)" style="max-height:150pt" oncontextmenu="javascript: window.open('@Url.Content(Data.IMG_FILE)')" />
                                            @Html.HiddenFor(model => model.Details_List[Num].IMG_FILE)
                                        </td>
                                    }
                                    else
                                    {
                                        if (ViewBag.VerifyUseYN == "Y")
                                        {
                                            <td>
                                                <input class="form-control input-sm" type="file" name="files" id="files_@Num" value="瀏覽" style="min-width:100px" />
                                                @Html.ValidationMessageFor(model => model.Details_List[Num].files, "", new { @class = "text-danger" })
                                            </td>
                                        }
                                    }
                                    <td class="text-center">
                                        <input name="Details_List[@Num].SHARE_YN" id="Details_SHARE_YN_@Num" type="checkbox" value="Y" @(Data.SHARE_YN == "Y" ? "checked=\"checked\"" : "") />
                                    </td>

                                </tr>
                                Num++;
                            }
                        }

                    </tbody>
                </table>
            </div>
            <div class="form-group">
                <div>

                    @Html.HiddenFor(model => model.Search.TEMP_BATCH_KEY)
                    @Html.HiddenFor(model => model.Search.ModeVal)
                    @Html.HiddenFor(model => model.Search.SCHOOL_NO)
                    @Html.HiddenFor(model => model.Search.NumType)
                    @Html.HiddenFor(model => model.Search.CLASS_NO)
                    @Html.Hidden("TOLTAL", TempData["TOLTAL"])
                    @Html.Hidden("DATA_TYPE")
                    @Html.Hidden("Num", Num)
                    @Html.Hidden("VerifyUseYN", (string)ViewBag.VerifyUseYN)


                    @Html.ValidationMessage("additem", new { @class = "text-danger" })
                    @if (string.IsNullOrWhiteSpace(Model.Search.TEMP_BATCH_KEY))
                    {
                        <div class="col-md-6 col-xs-8">
                            <div class="input-group">
                                @Html.TextBox("ADDNUM", TempData["ADDNUM"], new { @class = "form-control", @placeholder = "請填筆數", onchange = "if (isNaN(this.value) || this.value<=0) {alert('請填數字!');this.value=''};" })
                                <span class="input-group-btn">
                                    <input type=button class="btn btn-default" name=additem value="增加筆數" onclick="AddItem()">
                                </span>
                            </div><!-- /input-group -->
                        </div>
                        <div class="col-md-6 col-xs-4">
                            <input type="button" value="存檔" class="btn btn-default" onclick="Save('Save')" />
                        </div>
                    }
                    else
                    {
                        <div class="col-md-12 col-xs-12 text-center">
                            <input type="button" value="存檔" class="btn btn-default" onclick="Save('Save')" />
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>

    <div style="height:20px"></div>

    <!-- Modal -->
    <div class="modal fade" id="myModal" role="dialog">
        <div class="modal-dialog">
            <!-- Modal content-->
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">新功能---適合不需要記錄書名的學校</h4>
                </div>
                <div class="modal-body">
                    <p>提醒你，按確定後，書名會統一出現「書名請見閱讀單」</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn" data-dismiss="modal" onclick="sud()" >確定</button>
                    <button type="button" class="btn" data-dismiss="modal"  >離開</button>
                </div>
            </div>
        </div>
    </div>
}
<div class="text-center">
    @Html.ActionLink("回選擇模式", "Index", new { controller = (string)ViewBag.BRE_NO }, new { @class = "btn btn-default" })
</div>

@section Scripts {
    <script language="JavaScript">

        $(document).ready(function () {
            $(".img-responsive").colorbox({ opacity: 0.82, width: "90%" });
        });
        var button = document.querySelector('.prompttest');
        var showtxt = document.querySelector('.show');

        function popup3(e) {
            $("#myModal").modal();
            //var guest = window.prompt('您好!請輸入您的姓名', '迪迪希');
            //if (guest == null || "") {
            //    showtxt.innerHTML = '您已取消輸入'
            //} else {
            //    showtxt.innerHTML = 'Good Day' + guest + '^^'
            //}

        }


        function sud() {
   
            var trCount = 0;
            var trStr = "";
          
 
            $("tr").each(function () {
      
                trStr = "#Details_List_" + trCount + "__BOOK_NAME";
                $(trStr).val("");
                $(trStr).val("書名請見閱讀單");
                trCount++;
            });
        }
        function show() {
            window.sessionStorage.userdata = "0";
            $("#myModal").modal();
        }
        button.addEventListener('click', popup3);
        function AddItem() {
            form1.DATA_TYPE.value = 'AddItem'
            form1.action = '@Url.Action("Edit", (string)ViewBag.BRE_NO)';
            form1.submit();
        }
        function CheckAll(obj) {
            var checkvalue = $(obj).is(":checked")
            console.log(checkvalue);
            if (checkvalue) {
                $("input[type='checkbox'][value='Y']").each(function () { this.checked = true; });

            }
            else {
                $("input[type='checkbox'][value='Y']").each(function () { this.checked = false; });
            }
        }
        function Save(Val)
        {

            var Msg = '';

            if ($('#VerifyUseYN').val() == 'Y') {
                for (var i = 0; i < form1.Num.value; i++) {
                    if ($("#Details_List_Del_" + i).prop('checked') == false) {
                        if ($('#files_' + i).val() == '') {
                            Msg = Msg + '第' + (i + 1) + '筆未上傳檔案\n';
                        }
                    }
                }
            }




            var BoolOK = false;

            if (Msg!='') {
                BoolOK = confirm(Msg +'\n 請問是否繼續')
            }
            else {
                BoolOK = true;
            }

            if (BoolOK) {
                form1.DATA_TYPE.value = Val
                form1.action = '@Url.Action("Edit", (string)ViewBag.BRE_NO)';
                form1.submit();
            }
        }
    </script>
}