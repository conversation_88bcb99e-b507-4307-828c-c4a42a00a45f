﻿@model ECOOL_APP.EF.ADDT20

@{
    ViewBag.Title = "即時加點-刪除特殊加扣點";
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

<div class="text-center">
    <h3>確定要刪除這筆資料嗎</h3>
</div>

<img src="~/Content/img/web-bar2-revise-2200R.png" style="width:100%" class="img-responsive " alt="Responsive image" />
<div class="Div-EZ-ADDI06">
    <div class="Details">
        <dl class="dl-horizontal">
            <dt>
                @Html.DisplayNameFor(model => model.NAME)
            </dt>

            <dd>
                @Html.DisplayFor(model => model.NAME)
            </dd>

            <dt>
                @Html.DisplayNameFor(model => model.CONTENT_TXT)
            </dt>

            <dd>
                @Html.DisplayFor(model => model.CONTENT_TXT)
            </dd>

            <dt>
                @Html.DisplayNameFor(model => model.CASH)
            </dt>

            <dd>
                @Html.DisplayFor(model => model.CASH)
            </dd>

            <dt>
                @Html.DisplayNameFor(model => model.MEMO)
            </dt>

            <dd>
                @Html.DisplayFor(model => model.MEMO)
            </dd>
        </dl>

        @using (Html.BeginForm("DeleteConfirm", "ADDI09", FormMethod.Post))
        {
            @Html.AntiForgeryToken()
            @Html.HiddenFor(m => m.BATCH_CASH_ID)
            @Html.HiddenFor(m => m.NUM)

            <div class="Div-btn-center">
                <button value="Save" type="submit" class="btn btn-default">
                    確定送出
                </button>
                <a href='@Url.Action("ListView", "ADDI09")' class="btn btn-default">
                    放棄編輯
                </a>
            </div>
        }
    </div>



</div>
