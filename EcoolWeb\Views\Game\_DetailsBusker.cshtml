﻿
@model GameBuskerAddDetailsViewModel

@using (Html.BeginCollectionItem("Details", true))
{
    var Index = Html.GetIndex("Details");

    <div class="tr" id="Tr@(Index)">
        <div class="td" style="text-align:center">
            <a role='button' style="cursor:pointer;" onclick="deleteRow('Tr@(Index)')"> <i class='glyphicon glyphicon-remove'></i></a>
            @Html.HiddenFor(m => m.TEMP_USER_ID,new { Value = Model.TEMP_USER_ID })
            @Html.HiddenFor(m => m.NAME, new { Value = Model.NAME })
        </div>
        <div class="td" style="text-align:center">
            @Model.GAME_USER_ID
            @Html.HiddenFor(m => m.GAME_USER_ID, new { Value = Model.GAME_USER_ID })
        </div>

        <div class="td" style="text-align:center">
            @Model.NAME
        </div>
    </div>
}

