﻿using ECOOL_APP;
using ECOOL_APP.com.ecool.LogicCenter;
using ECOOL_APP.com.ecool.Models.DTO;
using ECOOL_APP.com.ecool.service;
using ECOOL_APP.EF;
using EcoolWeb.CustomAttribute;
using EcoolWeb.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace EcoolWeb.Controllers
{
    [SessionExpire]
    public class SECI07Controller : Controller
    {
        /// <summary>
        /// 此Controller 對應的程式代碼
        /// </summary>
        private static string Bre_NO = "SECI07";

        private static string Bre_Name = "閱讀儀表板";

        /// <summary>
        /// 會員相關資訊
        /// </summary>
        private UserProfile user;

        private string DefaultSCHOOL_NO = string.Empty;
        private string USER_NO = string.Empty;

        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();
        private SECI07Service service = new SECI07Service();

        public ActionResult _Menu(string NowAction)
        {
            ViewBag.NowAction = NowAction;
            return PartialView();
        }

        /// <summary>
        /// 班級
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ActionResult RankOfClassBookBorrow(ClassBorrowBookRankViewModel model)
        {
            this.Shared("各班借書統計");

            if (model.Where_SCHOOLNO == null)
            {
                model.Where_SCHOOLNO = user.SCHOOL_NO;
            }

            int SYear, Semesters;
            SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);

            if (string.IsNullOrWhiteSpace(model.Where_SYEAR))
            {
                model.Where_SYEAR = SYear.ToString();
            }
            var SYearItems = GetSYearsItems(model.Where_SYEAR);

            if (SYearItems.Where(x => x.Selected == true).Any() == false)
            {
                model.Where_SYEAR = SYearItems.Where(a => !string.IsNullOrEmpty(a.Value)).OrderByDescending(a => int.Parse(a.Value)).Select(x => x.Value).FirstOrDefault();

                SYearItems.Select(a =>
                {
                    a.Selected = a.Value == model.Where_SYEAR;
                    return a;
                }).ToList();
            }

            ViewBag.SYearItems = SYearItems;
            ViewBag.MonthItems = SysHelper.GetSemesterMonthItems(model.Where_MONTH);

            if (!string.IsNullOrWhiteSpace(model.Where_SYEAR))
            {
                model = service.GetRankOfClassBookBorrow(model, ref db);
            }

            return View(model);
        }

        /// <summary>
        /// 年級
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ActionResult RankOfGradeBookBorrow(GradeBorrowBookRankViewModel model)
        {
            this.Shared("各年級借書排行榜");

            if (model.Where_SCHOOLNO == null)
            {
                model.Where_SCHOOLNO = user.SCHOOL_NO;
            }

            int SYear, Semesters;
            SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);

            if (string.IsNullOrWhiteSpace(model.Where_SYEAR))
            {
                model.Where_SYEAR = SYear.ToString();
            }
            var SYearItems = GetSYearsItems(model.Where_SYEAR);

            if (SYearItems.Where(x => x.Selected == true).Any() == false)
            {
                model.Where_SYEAR = SYearItems.Where(a => !string.IsNullOrEmpty(a.Value)).OrderByDescending(a => int.Parse(a.Value)).Select(x => x.Value).FirstOrDefault();

                SYearItems.Select(a =>
                {
                    a.Selected = a.Value == model.Where_SYEAR;
                    return a;
                }).ToList();
            }

            ViewBag.SYearItems = SYearItems;
            ViewBag.MonthItems = SysHelper.GetSemesterMonthItems(model.Where_MONTH);

            if (!string.IsNullOrWhiteSpace(model.Where_SYEAR))
            {
                model = service.GetRankOfGradeBookBorrow(model, ref db);
            }

            return View(model);
        }

        /// <summary>
        /// 各校
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ActionResult RankOfSchoolBookBorrow(SchoolBorrowBookViewModel model)
        {
            user = UserProfileHelper.Get();
            this.Shared("學校借書統計");

            int SYear, Semesters;
            SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);

            if (string.IsNullOrWhiteSpace(model.Where_SYEAR))
            {
                model.Where_SYEAR = SYear.ToString();
            }
            var SYearItems = GetSYearsItems(model.Where_SYEAR);

            if (SYearItems.Where(x => x.Selected == true).Any() == false)
            {
                model.Where_SYEAR = SYearItems.Where(a => !string.IsNullOrEmpty(a.Value)).OrderByDescending(a => int.Parse(a.Value)).Select(x => x.Value).FirstOrDefault();

                SYearItems.Select(a =>
                {
                    a.Selected = a.Value == model.Where_SYEAR;
                    return a;
                }).ToList();
            }

            ViewBag.SYearItems = SYearItems;
            ViewBag.MonthItems = SysHelper.GetSemesterMonthItems(model.Where_MONTH);

            if (!string.IsNullOrWhiteSpace(model.Where_SYEAR))
            {
                model = service.GetRankOfSchoolBookBorrow(model, ref db, user);
            }

            return View(model);
        }

        public ActionResult ClassBookBorrow(ClassBorrowBookViewModel model)
        {
            this.Shared("各班借閱書量");

            if (model.Where_SCHOOLNO == null)
            {
                model.Where_SCHOOLNO = user.SCHOOL_NO;
            }

            int SYear, Semesters;
            SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);

            if (string.IsNullOrWhiteSpace(model.Where_SYEAR))
            {
                model.Where_SYEAR = SYear.ToString();
            }
            var SYearItems = GetSYearsItems(model.Where_SYEAR);

            if (SYearItems.Where(x => x.Selected == true).Any() == false)
            {
                model.Where_SYEAR = SYearItems.Where(a => !string.IsNullOrEmpty(a.Value)).OrderByDescending(a => int.Parse(a.Value)).Select(x => x.Value).FirstOrDefault();

                SYearItems.Select(a =>
                {
                    a.Selected = a.Value == model.Where_SYEAR;
                    return a;
                }).ToList();
            }

            ViewBag.SYearItems = SYearItems;
            ViewBag.MonthItems = SysHelper.GetSemesterMonthItems(model.Where_MONTH);
            model = service.GetClassBookBorrow(model, ref db);

            return View(model);
        }

        public ActionResult BookFlowTime(BookFlowTimeViewModel model)
        {
            this.Shared("借/還書時間點統計");

            if (model.Where_SCHOOLNO == null)
            {
                model.Where_SCHOOLNO = user.SCHOOL_NO;
            }

            int SYear, Semesters;
            SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);

            if (string.IsNullOrWhiteSpace(model.Where_SYEAR))
            {
                model.Where_SYEAR = SYear.ToString();
            }
            var SYearItems = GetSYearsItems(model.Where_SYEAR);

            if (SYearItems.Where(x => x.Selected == true).Any() == false)
            {
                model.Where_SYEAR = SYearItems.Where(a => !string.IsNullOrEmpty(a.Value)).OrderByDescending(a => int.Parse(a.Value)).Select(x => x.Value).FirstOrDefault();

                SYearItems.Select(a =>
                {
                    a.Selected = a.Value == model.Where_SYEAR;
                    return a;
                }).ToList();
            }

            ViewBag.SYearItems = SYearItems;
            ViewBag.MonthItems = GetMonthItems();

            model = service.GetBookFlowTime(model, ref db);

            return View(model);
        }

        public ActionResult BookTypeAnalysis(BookTypeBorrowAnalysisViewModel model)
        {
            this.Shared("總借書類別統計");

            if (model.Where_SCHOOLNO == null)
            {
                model.Where_SCHOOLNO = user.SCHOOL_NO;
            }

            int SYear, Semesters;
            SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);

            if (string.IsNullOrWhiteSpace(model.Where_SYEAR))
            {
                model.Where_SYEAR = SYear.ToString();
            }
            var SYearItems = GetSYearsItems(model.Where_SYEAR);

            if (SYearItems.Where(x => x.Selected == true).Any() == false)
            {
                model.Where_SYEAR = SYearItems.Where(a => !string.IsNullOrEmpty(a.Value)).OrderByDescending(a => int.Parse(a.Value)).Select(x => x.Value).FirstOrDefault();

                SYearItems.Select(a =>
                {
                    a.Selected = a.Value == model.Where_SYEAR;
                    return a;
                }).ToList();
            }

            ViewBag.SYearItems = SYearItems;

            model = service.GetBookTypeAnalysis(BorrowAnalysisType.MaleBorrowList, model, ref db);
            model = service.GetBookTypeAnalysis(BorrowAnalysisType.FemaleBorrowList, model, ref db);
            model = service.GetBookTypeAnalysis(BorrowAnalysisType.GradeBorrowList, model, ref db);
            model = service.GetBookTypeAnalysis(BorrowAnalysisType.MonthBorrowList, model, ref db);

            return View(model);
        }

        public ActionResult MonthBestSeller(MonthBestSellerViewModel model)
        {
            this.Shared("月暢銷書");

            if (model.Where_SCHOOLNO == null)
            {
                model.Where_SCHOOLNO = user.SCHOOL_NO;
            }

            if (model.Where_YEAR == null)
            {
                model.Where_YEAR = DateTime.Now.Year;
            }

            var YearItems = GetYearsItems(model.Where_YEAR.ToString());

            if (YearItems.Where(x => x.Selected == true).Any() == false)
            {
                model.Where_YEAR = YearItems.Where(a => !string.IsNullOrEmpty(a.Value)).OrderByDescending(a => int.Parse(a.Value)).Select(x => int.Parse(x.Value)).FirstOrDefault();

                YearItems.Select(a =>
                {
                    a.Selected = a.Value == model.Where_YEAR.ToString();
                    return a;
                }).ToList();
            }

            ViewBag.YearItems = YearItems;

            ViewBag.MonthItems = GetMonthItems();

            var genderItem = LogicCenter.GetEnumSelectList<Gender>().ToList();
            genderItem.Insert(0, new SelectListItem() { Text = "全部", Value = "" });
            ViewBag.GenderItems = genderItem;

            model = service.GetMonthBestSeller(model, ref db);

            return View(model);
        }

        #region Shared

        public List<SelectListItem> GetSYearsItems(string defaultSelectValue, bool IsAll = true)
        {
            List<SelectListItem> SYEARItems = new List<SelectListItem>();
            if (IsAll)
            {
                SYEARItems.Add(new SelectListItem() { Text = "全部", Value = "", Selected = string.IsNullOrWhiteSpace(defaultSelectValue) });
            }
            else
            {
                SYEARItems.Add(new SelectListItem() { Text = "請選擇...", Value = "", Selected = string.IsNullOrWhiteSpace(defaultSelectValue) });
            }

            Byte MinYear = this.db.HRMT01.Where(a => a.USER_TYPE == UserType.Student).Min(a => a.SYEAR).Value;
            Byte MaxYear = this.db.HRMT01.Where(a => a.USER_TYPE == UserType.Student).Max(a => a.SYEAR).Value;
            for (Byte i = MinYear; i <= MaxYear; i++)
            {
                SYEARItems.Add(new SelectListItem() { Text = i.ToString(), Value = i.ToString(), Selected = i.ToString() == defaultSelectValue });
            }

            return SYEARItems;
        }

        protected List<SelectListItem> GetYearsItems(string defaultSelectValue)
        {
            List<SelectListItem> YEARItems = new List<SelectListItem>();

            int MinYear = this.db.HRMT01.Where(a => a.USER_TYPE == UserType.Student).Min(a => a.SYEAR).Value + 1910;
            for (int i = MinYear; i <= DateTime.Now.Year; i++)
            {
                YEARItems.Add(new SelectListItem() { Text = i.ToString(), Value = i.ToString(), Selected = i.ToString() == defaultSelectValue });
            }

            return YEARItems;
        }

        protected List<SelectListItem> GetMonthItems()
        {
            var months = new int[] { 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12 }
                         .Select(s => new SelectListItem() { Text = s + "月", Value = s.ToString() })
                         .ToList();
            months.Insert(0, new SelectListItem() { Text = "全部", Value = "" });
            return months;
        }

        private void Shared(string Panel_Title)
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = Bre_Name + " - " + Panel_Title;

            user = UserProfileHelper.Get();
            DefaultSCHOOL_NO = UserProfileHelper.GetSchoolNo();

            if (user != null)
            {
                if (user.SCHOOL_NO != null)
                {
                    DefaultSCHOOL_NO = user.SCHOOL_NO;
                    USER_NO = user.USER_NO;
                }
            }
        }

        #endregion Shared

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}