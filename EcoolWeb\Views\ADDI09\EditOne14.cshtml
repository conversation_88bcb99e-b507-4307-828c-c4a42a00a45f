﻿@model ECOOL_APP.com.ecool.Models.DTO.ADDI09EditOoneViewModel
@using ECOOL_APP.com.ecool.Models.DTO;
@using EcoolWeb.Util;
@{ ViewBag.CashArray = new short[] { 30, 25, 20, 15, 10, 5, 3 }; }
<link href="~/Content/css/EzCss.css" rel="stylesheet" />
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/buzz/1.2.1/buzz.min.js">
</script>
@{
    ViewBag.Title = ViewBag.Panel_Title;
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")
@Html.ValidationMessage("ErrorMsg", new { @class = "text-danger" })


@using (Html.BeginForm("EditOne142", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", enctype = "multipart/form-data" }))
{

    @Html.AntiForgeryToken()
    @Html.Hidden("SYS_TABLE_TYPE", (string)ViewBag.SYS_TABLE_TYPE)
    @Html.HiddenFor(model => model.USER_NAME)
    @Html.Hidden("CASH2", Model.CASH)

    @Html.ValidationSummary(true, "", new { @class = "text-danger" })

    <style>
        optgroup[label] {
            color: indianred;
        }

        option {
            color: black;
        }
    </style>
    <div class="Div-EZ-ADDI09">

        <div class="form-horizontal">

            <div class="form-group">
                <label class="control-label col-md-4">獎懲類別</label>
                <div class="col-md-8">
                    @Html.DropDownListFor(model => model.SUBJECT, (IEnumerable<SelectListItem>)ViewBag.QuickItems, new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.SUBJECT, "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-4">   具體事蹟<br />的預設文字 </label>

                <div class="col-md-8">
                    @Html.DropDownList("quickselector", (IEnumerable<SelectListItem>)ViewBag.CONTENT_TXT, new {
                 @class = "form-control",
                   @onchange = "$('#CONTENT_TXT').val(event.target.options[event.target.selectedIndex].value)"})
                    @Html.ValidationMessage("quickselector", "", new { @class = "text-danger" })
                    @*<select id="quickselector" onchange="$('#CONTENT_TXT').val(event.target.options[event.target.selectedIndex].value)">
                            <option>請選擇</option>
                            <option value="品德表現">品德表現</option>
                            <option value="會自動自發幫助別人。">會自動自發幫助別人。</option>
                            <option value="書包、課桌椅及置物櫃都整理得很整齊。">書包、課桌椅及置物櫃都整理得很整齊。</option>
                            <option value="遇見師長會主動問好，是個有禮貌的孩子。">遇見師長會主動問好，是個有禮貌的孩子。</option>
                            <option value="友愛同學，並且會主動幫助同學解決困難。">友愛同學，並且會主動幫助同學解決困難。</option>
                            <option value="學習表現">學習表現</option>
                            <option value="學習單優良">學習單優良</option>
                            <option value="認真學習">認真學習</option>
                            <option value="課堂表現積極">課堂表現積極</option>
                            <option value="上課表現優良">上課表現優良</option>
                        </select>*@
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-4">具體事蹟</label>

                <div class="col-md-8">
                    @Html.TextAreaFor(model => model.CONTENT_TXT, 5, 100, new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.CONTENT_TXT, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.CASH, htmlAttributes: new { @class = "control-label col-md-4" })
                <div class="col-md-8  col-sm-10" style="padding-top:7px;">

                    @if (ViewBag.CashArray != null)
                    {
                        foreach (short c in ViewBag.CashArray)
                        {
                            bool bind = false;
                            if (Model != null) { bind = (Model.CASH == c); }
                            if (bind)
                            {
                                @Html.RadioButtonFor(model => model.CASH, c, new { @checked = "checked", onchange = "$('#USER_NO').focus();" })
                            }
                            else
                            {
                                @Html.RadioButtonFor(model => model.CASH, c, new { @onclick = "CashChange(this.value)", onchange = "$('#USER_NO').focus();" })
                            }
                            @Html.Label(c.ToString())
                            @:&nbsp
                        }
                    }
                </div><br />
                <div class="col-md-8" style="display:inline;white-space:nowrap;">
                    <label class="control-label col-md-3"></label>

                    &nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;

                    @if (user.USER_TYPE == "T" && ViewBag.uhRMT25YN != "Y")
                    {
                        <input id="CASH" name="CASH" type="number" style="width:60px" min="1" max="50" onchange="checkNum(this)">
                    }
                    else
                    {
                        <input id="CASH" name="CASH" type="number" style="width:60px" min="1" max="100" onchange="checkNumAdmin(this)">
                    }      <b>(自訂數字，老師50點以內;管理者100點以內)</b>
                </div>
            </div>
            <div class="form-group" style="margin-bottom:0px;">
                @Html.Label("學號/班級座號/數位學生證", htmlAttributes: new { @class = "control-label col-md-4" })
                <div class="col-md-8">
                    @Html.EditorFor(model => model.USER_NO, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.USER_NO, "", new { @class = "text-danger" })
                    <div style="font-size:1rem">
                        @(AttributeHelper.GetDescriptionAttribute<ADDI09EditOoneViewModel>(nameof(Model.USER_NO)) )
                    </div>
                </div>
            </div>
            <div class="form-group">
                <div class="col-md-4">
                </div>
                <div class="col-md-8" style="text-align:center;vertical-align:middle;">
                    @Html.Label(" ", new { @id = "SHOWNAME", style = "margin-top:5px" })
                </div>
            </div>
        </div>
    </div>

    <div style="height:20px"></div>
    <div class="text-center">
        <button type="button" id="myButton" data-loading-text="Loading..." class="btn btn-default" autocomplete="off">
            確定給點
        </button>
        @Html.ActionLink("回選擇模式", "EditOneIndex", new { SYS_TABLE_TYPE = ViewBag.SYS_TABLE_TYPE }, new { @class = "btn btn-default", @role = "button" })
        <button type="button" class="btn btn-default btn-danger" onclick="location.reload(true);">
            清除
        </button>

    </div>
    <div id="challenge-panel" class="challenge-panel-bg">
        <div id="challenge-ribbon" class="challenge-ribbon animated"></div>
        <div id="challenge-info" class="challenge-info animated"></div>
    </div>

    <div style="width:100%;height:100%;background-color:@SharedGlobal.Logo_loading_background_color;display:none;z-index:999;position:fixed;left:0;top:0" id="loading" class="challenge-loading">
        <div style="margin: 0px auto;text-align:center">
            <img src="@Url.Content(SharedGlobal.Logo_loading)" style="max-height:350px;" />
            <br />


            <h3 style="color:#80b4fb">寫入中…</h3>

        </div>
    </div>

    <link href="~/Content/css/jquery-ui.min.css" rel="stylesheet" />

<script src = @Url.Content("~/Scripts/jquery-3.6.4.min.js") ></script>
    <script src="~/Scripts/jquery-ui.min.js"></script>
    <div id="dialog-confirm" style="display:none;" title="確認給點？">
        <span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span>您確定要給點嗎？
    </div>

    <script type="text/javascript">
        $(function() {
            // 初始化變數
            var game_falseSound = new buzz.sound("@Url.Content("~/Content/mp3/game_false.mp3")");
            
            // 檢查是否有預設值
            var preValue = "@(Session["ADDI09_EditOne14_SUBJECT"] != null && Session["ADDI09_EditOne14_CASH"] != null)";
            if (preValue === "True") {
                var CSAHin = Number("@Session["ADDI09_EditOne14_CASH"]");
                if (CSAHin !== 3 && CSAHin !== 5 && CSAHin !== 10 && CSAHin !== 15 && CSAHin !== 20 && CSAHin !== 25 && CSAHin !== 30) {
                    $("input[type='number']").val(@(Session["ADDI09_EditOne14_CASH"]));
                }
            }

            // 綁定事件處理
            $('#@Html.IdFor(m => m.USER_NO), #@Html.IdFor(m => m.CLASS_SEAT)').on('change', function() {
                DbQuery();
            });

            // 確定給點按鈕點擊事件
            $('#myButton').on('click', function() {
                var str = $("#CONTENT_TXT").val();
                var UID = $("#USER_NO").val();
                
                if (str && str.length > 0) {
                    if (UID && UID.length > 0) {
                        $('#loading').fadeIn(100);
                        form1.submit();
                    } else {
                        $('#USER_NO').focus();
                        alert("請填入學號/班級座號/數位學生證");
                    }
                } else {
                    $('#USER_NO').focus();
                    alert("請填入具體事蹟");
                }
            });

            // 檢查數字輸入（管理者）
            function checkNumAdmin(obj) {
                $("input:checked").prop("checked", false);
                var num = $(obj).val();
                
                if (num.length > 0) {
                    $("input[type='radio']").prop('disabled', true);
                } else {
                    $("input[type='radio']").prop('disabled', false);
                }
                
                if (num > 100) {
                    alert("管理者給點最高100");
                }
                if (num < 100) {
                    $('#USER_NO').focus();
                }
            }

            // 檢查數字輸入（一般使用者）
            function checkNum(obj) {
                $("input:checked").prop("checked", false);
                var num = $(obj).val();
                
                if (num.length > 0) {
                    $("input[type='radio']").prop('disabled', true);
                }
                
                if (num > 50) {
                    if (@user.ROLE_LEVEL !== '@HRMT24_ENUM.QAdminLevel.ToString()' && @user.ROLE_LEVEL !== '@HRMT24_ENUM.QAdminLevel.ToString()') {
                        alert("老師給點最高50");
                    }
                    if ((@user.ROLE_LEVEL === '@HRMT24_ENUM.QAdminLevel.ToString()' || @user.ROLE_LEVEL === '@HRMT24_ENUM.QAdminLevel.ToString()')) {
                        $('#USER_NO').focus();
                    }
                }
                if (num < 50) {
                    $('#USER_NO').focus();
                }
            }

            // 提交點數
            function PointSubmit() {
                $('#loading').fadeIn(100);
                var UNAME = $('#@Html.IdFor(m => m.USER_NO)').val();
                
                if (!UNAME) {
                    window.alert('請輸入學號或班級座號');
                    $('#loading').fadeOut(100);
                    return;
                }
                
                var SUBJECT = $('#@Html.IdFor(m => m.SUBJECT)').val();
                if (!SUBJECT) {
                    window.alert('請選擇獎懲主旨');
                    $('#loading').fadeOut(100);
                    return;
                }

                form1.submit();
            }

            // 更新點數
            function CashChange(ans) {
                $('#@Html.Id("CASH2")').val(ans);
            }

            // 查詢使用者資料
            function DbQuery() {
                $.ajax({
                    url: "@(Url.Action("ToQueryUser", "ADDI09"))",
                    type: 'POST',
                    data: {
                        "USER_NO": $('#@Html.IdFor(m => m.USER_NO)').val(),
                        "CLASS_SEAT": $('#@Html.IdFor(m => m.CLASS_SEAT)').val()
                    },
                    dataType: 'json',
                    cache: false,
                    success: function(data) {
                        var res = jQuery.parseJSON(data);
                        
                        if (!res.USER_NO) {
                            $("#USER_NO").val("");
                            game_falseSound.play();
                            alert(res.USER_NAME);
                            $('#myButton').text("確定給點");
                            $('#SHOWNAME').text(" ");
                        } else {
                            $('#@Html.IdFor(m => m.USER_NAME)').val(res.USER_NAME);
                            $('#myButton').text("確定給點：" + res.USER_NAME);
                            $('#SHOWNAME').text(res.USER_NAME);
                            PointSubmit();
                        }
                    },
                    error: function(xhr, err) {
                        console.error("Ajax error:", err);
                    }
                });
            }

            // 鍵盤事件處理
            $(document).on('keypress', function(e) {
                var GameUserID = $('#USER_NO');
                GameUserID.focus();
                
                if (e.which === 13) {
                    if ($('#loading').is(":hidden")) {
                        if (!GameUserID.is(":focus")) {
                            var str = $("#CONTENT_TXT").val();
                            var UID = $("#USER_NO").val();
                            
                            if (str && str.length > 0) {
                                if (UID && UID.length > 0) {
                                    $('#loading').fadeIn(100);
                                    DbQuery();
                                } else {
                                    alert("請填入學號/班級座號/數位學生證");
                                }
                            } else {
                                alert("請填入具體事蹟");
                            }
                        }
                    } else {
                        return false;
                    }
                }
            });

            // 初始化焦點
            var el = document.getElementById('USER_NO');
            if (el) {
                el.focus();
                if ($('#USER_NO').length > 0) {
                    el.setSelectionRange(10, 10);
                }
            }

            // 檢查狀態訊息
            if ($("#StatusMessageDiv").length > 0) {
                game_falseSound.play();
            }
        });
    </script>
}

