﻿@{
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

    ECOOL_DEVEntities db = new ECOOL_DEVEntities();
    List<BDMT01> SchoolList = db.BDMT01.OrderBy(school => school.SHORT_NAME).ToList();
    List<BDMT01> SchoolListOutter = db.BDMT01.Where(s => s.CITY != "臺北市").OrderBy(school => school.SHORT_NAME).ToList();
    string DateNowStr = DateTime.Now.ToString("yyyy_MM_dd_H_mm_ss");

    string ZONE_ID = string.Empty;
    short i = 1;

    EcoolWeb.Models.UserProfileHelper.SetLayoutCookieData("~/Views/Shared/_LayoutPortal.cshtml");

    if (System.Web.Configuration.WebConfigurationManager.AppSettings["TestMode"] == "true")
    {
        ViewBag.Title = "測試頁";
    }
    else
    {
        ViewBag.Title = "臺北e酷幣首頁";
    }
}
<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@ViewBag.Title</title>
    @Styles.Render("~/Content/css")
    <link href="~/Content/css/EzCss.css?@DateNowStr" rel="stylesheet" />
    <link href="~/Content/font-awesome/css/font-awesome.min.css" rel="stylesheet" />
    @Scripts.Render("~/bundles/jquery")
    @Scripts.Render("~/bundles/modernizr")
    @Scripts.Render("~/bundles/bootstrap")

    <!--[if lte IE 9 ]>
        <style type="text/css">
            .schoolselector {
                filter: progid:DXImageTransform.Microsoft.AlphaImageLoader(
                src='@Url.Content("~/Content/img/web-19plus-body.png")',
                sizingMethod='scale');

                -ms-filter: "progid:DXImageTransform.Microsoft.AlphaImageLoader(
                src='@Url.Content("~/Content/img/web-19plus-body.png")',
                sizingMethod='scale')";
            }
    </style>
    <![endif]-->
    <!--[if !IE]><!-->
    <style type="text/css">
          .schoolselector {
        background-image:url('@Url.Content("~/Content/img/web-19plus-body.png")');
        }
    </style>
    <!--<![endif]-->
    @{ Html.RenderPartial("_GoogleAnalytics"); }
</head>

@Html.Partial("../Shared/_CheckBrowser")
<body style="background-image:url('@Url.Content("~/Content/img/web-01.png")');background-repeat:repeat">
    <div class="visible-xs">
        <!---手機 -->
        <nav class="navbar navbar-phone navbar-fixed-top" role="navigation">
            <span class="btn-logo">
                <img src="~/Content/img/web-student_png-16.png" class="img-responsive " alt="Responsive image" />
            </span>
            <a role="button" class="btn-User" href="@Url.Action("LoginPage","Home")">
                <i class="fa fa-user" style="font-size:2.5em" title="會員登入"></i>
            </a>
        </nav>

        <div style="height:50px"></div>
    </div>

    <!---手機 -->
    <img src='@Url.Content("~/Content/img/web-revise-phone0308-00.png")' class="img-responsive visible-xs" alt="Responsive image" />
    <img src='@Url.Content("~/Content/img/web_0717_logo-11.png")' style="width:100%" class="img-responsive hidden-xs" alt="Responsive image" />

    <div class="containerEZ">
        <div class="row">
            <div class="col-md-3 mt-4">
                @if (null == user)
                {
                    <div class="hidden-xs">
                        @Html.Partial("../Shared/_Login")

                    </div>
                }

                <div class="school-menu">
                    <strong class="school-menu-title">學校</strong>

                    @{

                        int No = 0;

                        List<SelectListItem> ThisSelectItem = new List<SelectListItem>();

                        ThisSelectItem.Add(new SelectListItem() { Text = "全部區域", Value = "", Selected = true });

                        foreach (var item in SchoolList.GroupBy(a => new { a.ZONE_ID, a.CITY }).Select(a => new { a.Key.CITY, a.Key.ZONE_ID }).OrderByDescending(a => a.CITY).ThenBy(a => a.ZONE_ID).ToList())
                        {
                            ThisSelectItem.Add(new SelectListItem() { Text = item.CITY + '-' + (item.ZONE_ID ?? "其他"), Value = Convert.ToString(++No), Selected = false });
                        }

                        ViewBag.ZONE_IDSelectItem = ThisSelectItem;

                        short Zone_Num = 1;
                    }

                    <select class="selectpicker show-menu-arrow mx-auto mt-2" title="" date-style="btn btn-default m-0" data-size="auto" data-live-search="true" id="ZONE_IDSelectItem" name="ZONE_IDSelectItem">
                        @foreach (var item in (IEnumerable<SelectListItem>)ViewBag.ZONE_IDSelectItem)
                        {
                            <option data-tokens="@item.Text" value="@item.Value" @(item.Selected == true ? "selected" : "")>@item.Text</option>
                        }
                    </select>

                    <ul class="school-menu-list">
                        @foreach (var ThisZONE_ID in SchoolList.GroupBy(a => new { a.ZONE_ID, a.CITY }).Select(a => new { a.Key.CITY, a.Key.ZONE_ID }).OrderByDescending(a => a.CITY).ThenBy(a => a.ZONE_ID).ToList())
                        {
                            @:<li class="Div-Zone Zone_Num_@Zone_Num">
                                @*if (i % 2 == 0)
                                {
                                    <strong class="Div-Zone col-xs-6 text-center mx-auto">
                                        &nbsp;
                                    </strong>
                                }*@
                                <strong>
                                    @if (string.IsNullOrWhiteSpace(ThisZONE_ID.ZONE_ID))
                                    {
                                        @ThisZONE_ID.CITY
                                    }
                                    else
                                    {
                                        @ThisZONE_ID.ZONE_ID
                                    }
                                </strong>
                                i = 1;
                                @:<ul class="school-menu-child">
                                    foreach (var school in SchoolList.Where(a => a.ZONE_ID == ThisZONE_ID.ZONE_ID && a.CITY == ThisZONE_ID.CITY))
                                    {
                                        <li>
                                            @Html.RouteLink(school.SHORT_NAME, "SchoolRoute", new { school = school.SCHOOL_NO }, null)
                                        </li>

                                        i++;
                                    }

                                @:</ul>
                            @:</li>
                            Zone_Num++;
                        }
                    </ul>
                </div>
            </div>
            <div class="col-md-7 mt-4">
                @RenderBody()
            </div>
            <div class="hidden-xs col-md-2">
                <img src="~/Content/img/web-10-0701.png" class="img-responsive " alt="" />
            </div>
        </div>
    </div>

    <script>
        $(document).ready(function () {
            $('#ZONE_IDSelectItem').selectpicker();
            $('#ZONE_IDSelectItem').on('change', function () {
                var selectedVal = $(this).find("option:selected").val();
                if (selectedVal > 0) {
                    $('.Div-Zone').hide();
                    $(".Zone_Num_" + selectedVal).show();
                }
                else {
                    $('.Div-Zone').show();
                }
            });
        });
    </script>
</body>
</html>