﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.com.ecool.Models.DTO
{
    public class ADDI09EditOoneViewModel : ADDI09EditViewModel
    {
        ///Summary
        ///學號
        ///Summary
        [DisplayName("學號(6碼)")]
        [Description("※備註: 會依據輸入值長度判別為學號(6碼)、班級座號(5碼)、學生證內碼(10碼)。")]
        public string USER_NO { get; set; }

        public string GetUserNO()
        {
            if (this.USER_NO == null) return string.Empty;
            return this.USER_NO;
        }

        ///Summary
        ///班級座號
        ///Summary
        [DisplayName("或班級座號(5碼)")]
        public string CLASS_SEAT { get; set; }

        public string USER_NAME { get; set; }

        public bool IsFix { get; set; }

        public bool IsRandom { get; set; }

        public bool IsRandomHighPoint { get; set; }

        public override string SUBJECT
        {
            get
            {
                return base.SUBJECT;
            }
            set
            {
                base.SUBJECT = value;
                this.CONTENT_TXT = value;
            }
        }
    }

    public class ADDI09EditViewModel
    {
        ///Summary
        ///功能類型
        ///Summary
        public string SYS_TABLE_TYPE { get; set; }

        ///Summary
        ///校內表現
        ///Summary
        public string ADDT14_STYLE { get; set; }

        /// <summary>
        /// 是否為個別給點
        /// </summary>
        public bool Individual_Give { get; set; }

        ///Summary
        ///獎懲主旨
        ///Summary
        [DisplayName("獎懲主旨")]
        [Required(ErrorMessage = "*此欄位必填")]
        public virtual string SUBJECT { get; set; }

        ///Summary
        ///獎懲內容
        ///Summary
        [DisplayName("獎懲內容")]
        [Required(ErrorMessage = "*此欄位必填")]
        public string CONTENT_TXT { get; set; }

        ///Summary
        ///備註
        ///Summary
        [DisplayName("備註")]
        public string MEMO { get; set; }

        [DisplayName("上傳圖檔")]
        public string IMG_FILE { get; set; }

        ///Summary
        ///獎懲點數
        ///Summary
        [DisplayName("獎懲點數")]
        [Required(ErrorMessage = "*此欄位必填")]
        public int CASH { get; set; }

        /// <summary>
        /// 好運給點 (目前抽一次100點)
        /// </summary>
        [DisplayName("好運次數")]
        [Range(1, 3, ErrorMessage = "給予好運次數需介於1~3次")]
        [Required(ErrorMessage = "*此欄位必填")]
        public int LuckPointCount { get; set; }

        /// <summary>
        /// 選取清單
        /// </summary>
        public List<ADDI09SelectListViewModel> DataList { get; set; }

        public List<ADDI09SelectListViewModel> CHECKDataList { get; set; }
        public virtual ICollection<QuerySelectPrintViewModel> Chk { get; set; }
        /// <summary>
        /// 已選取人數
        /// </summary>
        public int SelectDataCount { get; set; }

        /// <summary>
        /// 已選Div 高度 + 跟筆數一起拉大 , - 固定200pt
        /// </summary>
        public string DivHeight { get; set; }

        public string ShowType { get; set; }

        static public class DivHeightVal
        {
            public static string DivHeightP = "+";

            public static string DivHeightM = "-";
        }

        static public class ShowTypeVal
        {
            public static string ShowTypeQSV = "QuerySelectView";

            public static string ShowTypeE = "Edit";

            public static string ShowTypeQSV1 = "QuerySelectView1";
        }

        /// <summary>
        /// 功能類型清單
        /// </summary>
        /// <returns></returns>
        static public List<Tuple<string, string>> SYSTableTypeItem()
        {
            List<Tuple<string, string>> DataList = new List<Tuple<string, string>>();

            //校內
            DataList.Add(Tuple.Create(SysTableTypeVal.DATA_TABLE_IN, GetSysTableTypeString(SysTableTypeVal.DATA_TABLE_IN, string.Empty)));

            //校外
            DataList.Add(Tuple.Create(SysTableTypeVal.DATA_TABLE_OBC, GetSysTableTypeString(SysTableTypeVal.DATA_TABLE_OBC, string.Empty)));

            //其他
            DataList.Add(Tuple.Create(SysTableTypeVal.DATA_TABLE_OTHER, GetSysTableTypeString(SysTableTypeVal.DATA_TABLE_OTHER, string.Empty)));

            ////其他-班級
            //DataList.Add(Tuple.Create(SysTableTypeVal.DATA_TABLE_OTHER_CLASS, GetSysTableTypeString(SysTableTypeVal.DATA_TABLE_OTHER_CLASS, string.Empty)));

            ////老師加扣點LOG
            //DataList.Add(Tuple.Create(SysTableTypeVal.DATA_TABLE_OTHER_CLASS, GetSysTableTypeString(SysTableTypeVal.DATA_TABLE_AWAT08_LOG, string.Empty)));

            return DataList;
        }

        /// <summary>
        /// 功能類型 Val
        /// </summary>
        static public class SysTableTypeVal
        {
            /// <summary>
            /// 校內表現
            /// </summary>
            public static string DATA_TABLE_IN = "ADDT14";
            /// <summary>
            /// 校內表現
            /// </summary>
            public static string DATA_TABLE_INADD = "ADDT141";

            /// <summary>
            /// 校外榮礜
            /// </summary>
            public static string DATA_TABLE_OBC = "ADDT15";

            /// <summary>
            /// 其它
            /// </summary>
            public static string DATA_TABLE_OTHER = "ADDT20";

            /// <summary>
            /// 其它-班級服務
            /// </summary>
            public static string DATA_TABLE_OTHER_CLASS = "ADDT20_CLASS";

            /// <summary>
            /// 老師紀錄酷幣Table
            /// </summary>
            public static string DATA_TABLE_AWAT08_LOG = "AWAT08_LOG";

            /// <summary>
            /// 好運給點
            /// </summary>
            public static string DATA_TABLE_AWAT01_LOG_LUCKY = "AWAT01_LOG_LUCKY";
            /// <summary>
            /// 班級批次-校內表現
            /// </summary>
            public static string DATA_TABLE_ADDT14_LEADER_BATCH = "ADDT14_LEADER_BATCH";
            /// <summary>
            /// 班級批次-校內表現
            /// </summary>
            public static string DATA_TABLE_ADDT14_CLASS_BATCH = "ADDT14_CLASS_BATCH";

            /// <summary>
            /// 班級批次-特殊加扣點
            /// </summary>
            public static string DATA_TABLE_ADDT20_CLASS_BATCH = "ADDT20_CLASS_BATCH";

            /// <summary>
            /// 快速大量加點
            /// </summary>
            public static string DATA_TABLE_QUICKBULK = "QUICKBULK";
        }

        /// <summary>
        /// 取得功能類型 中文名稱
        /// </summary>
        /// <param name="Val"></param>
        /// <param name="ADDT14_STYLE"></param>
        ///  <param name="Subject">快速大量加點判別TypeString</param>
        /// <returns></returns>
        static public string GetSysTableTypeString(string Val, string ADDT14_STYLE, string Subject = "")
        {
            string ans = string.Empty;

            if (Val == SysTableTypeVal.DATA_TABLE_IN)
                ans = "校內表現";
            else if (Val == SysTableTypeVal.DATA_TABLE_OBC)
                ans = "校外榮礜";
            else if (Val == SysTableTypeVal.DATA_TABLE_OTHER)
                ans = "特殊加扣點";
            else if (Val == SysTableTypeVal.DATA_TABLE_ADDT14_LEADER_BATCH)
                ans = "七個習慣代言人";
            else if (Val == SysTableTypeVal.DATA_TABLE_OTHER_CLASS)
                ans = "特殊加扣點-班級";
            else if (Val == SysTableTypeVal.DATA_TABLE_AWAT08_LOG)
                ans = "老師加扣點";
            else if (Val == SysTableTypeVal.DATA_TABLE_AWAT01_LOG_LUCKY)
                ans = "加好運集點";
            else if (Val == SysTableTypeVal.DATA_TABLE_ADDT14_CLASS_BATCH)
                ans = "無敵加點-校內表現";
            else if (Val == SysTableTypeVal.DATA_TABLE_ADDT20_CLASS_BATCH)
                ans = "無敵加點-特殊加扣點";
            else if (Val == SysTableTypeVal.DATA_TABLE_QUICKBULK)
            {
                if (Subject == "志工" || Subject == "校內競賽" || Subject == "品德表現" || Subject == "學習表現" || Subject == "其他" || Subject == "領導人" || Subject == "七個習慣代言人")
                {
                    ans = "快速大量加點-校內表現-" + Subject;
                }
                else if (Subject == "班級服務" || Subject == "班級小幫手" || Subject == "班級幫手和榮譽")
                {
                    ans = "快速大量加點-特殊加扣點-" + Subject;
                }
                else if (Subject == "班級幹部")
                {
                    ans = "快速大量加點-" + Subject;
                }
                else
                {
                    ans = "快速大量加點";
                }
            }
            else
                ans = "";

            if (string.IsNullOrWhiteSpace(ADDT14_STYLE) == false)
            {
                if (ADDT14_STYLE == "HELPER") ans += "班級幫手和榮譽";
                if (ADDT14_STYLE == "LEADER") ans += "班級幹部";
            }
            return ans;
        }
    }

    public class ADDI09SelectListViewModel
    {
        /// <summary>
        /// 是否選取
        /// </summary>
        public bool Chk { get; set; }

        ///Summary
        ///學校
        ///Summary
        [DisplayName("學校")]
        public string SCHOOL_NO { get; set; }

        ///Summary
        ///帳號
        ///Summary
        [DisplayName("學號")]
        public string USER_NO { get; set; }

        ///Summary
        ///班級
        ///Summary
        [DisplayName("班級")]
        public string CLASS_NO { get; set; }

        ///Summary
        ///座號
        ///Summary
        [DisplayName("座號")]
        public string SEAT_NO { get; set; }

        ///Summary
        ///姓名
        ///Summary
        [DisplayName("姓名")]
        public string NAME { get; set; }
        public string PersonType { get; set; }
        public string TABLType { get; set; }
        public int Cash { get; set; }
        ///Summary
        ///簡稱
        ///Summary
        [DisplayName("簡稱")]
        public string SNAME { get; set; }

        [DisplayName("獎懲點數")]
        public int TempCash { get; set; }
    }
}