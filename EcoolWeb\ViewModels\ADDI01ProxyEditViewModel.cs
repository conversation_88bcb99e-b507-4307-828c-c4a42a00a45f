﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Web;

namespace EcoolWeb.ViewModels
{
    public class ADDI01ProxyEditViewModel
    {
        public ADDI01ProxySearchViewModel Search { get; set; }
        public List<ADDI01ProxyDetailsViewModel> Details_List { get; set; }

        /// <summary>
        /// 增加明細筆數
        /// </summary>
        public int? ADDNUM;
    }

    public class ADDI01ProxyDetailsViewModel
    {

        [DisplayName("刪除")]
        public bool Del { get; set; }

        ///Summary
        ///APPLY_NO
        ///Summary
        [DisplayName("APPLY_NO")]
        public int? APPLY_NO { get; set; }

        ///Summary
        ///SCHOOL_NO
        ///Summary
        [DisplayName("學校代碼")]
        public string SCHOOL_NO { get; set; }

        ///Summary
        ///USER_NO
        ///Summary
        [DisplayName("帳號")]
        [Required]
        public string USER_NO { get; set; }

        ///Summary
        ///CLASS_NO
        ///Summary
        [DisplayName("班級")]
        public string CLASS_NO { get; set; }

        ///Summary
        ///SYEAR
        ///Summary
        [DisplayName("學年度")]
        public byte? SYEAR { get; set; }

        ///Summary
        ///SEMESTER
        ///Summary
        [DisplayName("學期")]
        public byte? SEMESTER { get; set; }

        ///Summary
        ///SEAT_NO
        ///Summary
        [DisplayName("座號")]
        public string SEAT_NO { get; set; }

        ///Summary
        ///NAME
        ///Summary
        [DisplayName("姓名")]
        public string NAME { get; set; }

        ///Summary
        ///SNAME
        ///Summary
        [DisplayName("姓名")]
        public string SNAME { get; set; }

        ///Summary
        ///SUBECT
        ///Summary
        [DisplayName("標題")]
        public string SUBECT { get; set; }

        ///Summary
        ///ARTICLE
        ///Summary
        [DisplayName("文章")]
        public string ARTICLE { get; set; }

        ///Summary
        ///圖片名稱
        ///Summary
        [DisplayName("圖檔")]
        public string IMG_FILE { get; set; }

        ///Summary
        ///GIVE_POINT 給予酷幣點數
        ///Summary
        [DisplayName("點數")]
        public short GIVE_POINT { get; set; }

        ///Summary
        ///MEMO
        ///Summary
        [DisplayName("備註")]
        public string MEMO { get; set; }

        public string OK_YN { get; set; }

        public HttpPostedFileBase files { get; set; }

    }

}