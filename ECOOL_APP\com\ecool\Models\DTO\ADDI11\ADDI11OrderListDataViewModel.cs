﻿
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Text;

namespace ECOOL_APP.EF
{
    public  class ADDI11OrderListDataViewModel
    {
        [DisplayName("排名")]
        public int Rank { get; set; }

        [DisplayName("學校")]
        public string SCHOOL_NO { get; set; }


        [DisplayName("學號")]
        public string USER_NO { get; set; }
        [DisplayName("姓名")]
        public string NAME { get; set; }
        [DisplayName("姓名")]
        public string SNAME { get; set; }
        [DisplayName("性別")]
        public string SEX { get; set; }
        [DisplayName("年級")]
        public Nullable<byte> GRADE { get; set; }
        [DisplayName("班級")]
        public string CLASS_NO { get; set; }
        [DisplayName("座號")]
        public string SEAT_NO { get; set; }
        public Nullable<byte> USER_STATUS { get; set; }
        public string USER_TYPE { get; set; }

        [DisplayName("累計里程")]
        public double Total_M { get; set; }


        [DisplayName("最後跑步時間")]
        public Nullable<DateTime> LAST_RUN_DATE { get; set; }
    }
}