﻿@model ECOOL_APP.com.ecool.Models.DTO.ZZZI11ListViewModel

<link href="~/Content/css/EzCss.css" rel="stylesheet" />

@helper  buttonFun() {

string ActiveAll = (Model.Search.STATUS == string.Empty || Model.Search.STATUS == null) ? "active" : "";
string ActiveANSWERS = (Model.Search.STATUS == ECOOL_APP.com.ecool.Models.entity.uADDT16.STATUS_Val.STATUS_1) ? "active" : "";
string ActiveQUESTIONS = (Model.Search.STATUS == ECOOL_APP.com.ecool.Models.entity.uADDT16.STATUS_Val.STATUS_2) ? "active" : "";
string ActiveQEnd = (Model.Search.STATUS == ECOOL_APP.com.ecool.Models.entity.uADDT16.STATUS_Val.STATUS_Z) ? "active" : "";

    <div class="text-right">

            <button class="btn btn-xs btn-pink @ActiveAll" type="button" onclick="btnSTATUS('')">全部</button>

            @Html.PermissionButton("待回覆", "button", (string)ViewBag.BRE_NO, "ANSWERS", new { @class = "btn btn-xs btn-pink " + ActiveANSWERS, @onclick = "btnSTATUS('" + ECOOL_APP.com.ecool.Models.entity.uADDT16.STATUS_Val.STATUS_1 + "')" }, 1, ViewBag.BtnPermission as List<ECOOL_APP.ControllerPermissionfile>)
            @Html.PermissionButton("待讀取", "button", (string)ViewBag.BRE_NO, "QUESTIONS", new { @class = "btn btn-xs btn-pink " + ActiveQUESTIONS, @onclick = "btnSTATUS('" + ECOOL_APP.com.ecool.Models.entity.uADDT16.STATUS_Val.STATUS_2 + "')" }, 1, ViewBag.BtnPermission as List<ECOOL_APP.ControllerPermissionfile>)
            @Html.PermissionButton("問題已解決", "button", (string)ViewBag.BRE_NO, "Index", new { @class = "btn btn-xs btn-pink " + ActiveQEnd, @onclick = "btnSTATUS('" + ECOOL_APP.com.ecool.Models.entity.uADDT16.STATUS_Val.STATUS_Z + "')" }, 1, ViewBag.BtnPermission as List<ECOOL_APP.ControllerPermissionfile>)
            @Html.PermissionButton("問題未解決", "button", (string)ViewBag.BRE_NO, "Index", new { @class = "btn btn-xs btn-pink " + ActiveQEnd, @onclick = "btnSTATUS('" + ECOOL_APP.com.ecool.Models.entity.uADDT16.STATUS_Val.STATUS_UnZ + "')" }, 1, ViewBag.BtnPermission as List<ECOOL_APP.ControllerPermissionfile>)
    </div>

}


@Html.PermissionButton("新增問題主題", "button", (string)ViewBag.BRE_NO, "QUESTIONS", new { @class = "btn btn-sm btn-sys", @onclick = "onAdd()" }, 1, ViewBag.BtnPermission as List<ECOOL_APP.ControllerPermissionfile>)

<br />
<br />
<div class="form-inline" role="form">
    <div class="form-group">
        <label class="control-label">主旨/內容</label>
    </div>
    <div class="form-group">
        @Html.EditorFor(model => model.Search.SearchContents, new { htmlAttributes = new { @class = "form-control"} })
        @Html.HiddenFor(model => model.Search.Page)
        @Html.HiddenFor(model => model.Search.OrderByName)
        @Html.HiddenFor(model => model.Search.Q_QUESTIONS_ID)

        @Html.HiddenFor(model => model.Search.SyntaxName)
        @Html.HiddenFor(model => model.Search.DetailsPage)
        @Html.HiddenFor(model => model.Search.STATUS)
    </div>
    <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1)" />
    <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />
</div>
<br/>

@buttonFun()
<div class="panel panel-ZZZ" name="TOP">
    <div class="panel-heading text-center">
        @Html.BarTitle()
    </div>
    <div class="table-responsive">
        <table class="table-ecool table-92Per table-hover table-ecool-ZZZ">
            <thead>
                <tr>
                    <th>
                        <samp class="form-group" style="cursor:pointer;" onclick="FunSort('SUBJECT')">
                        @Html.DisplayNameFor(model => model.ADDT16List.First().SUBJECT)
                        </samp>
                    </th>
                    <th>
                        <samp class="form-group" style="cursor:pointer;" onclick="FunSort('ANS_COUNT')">
                            @Html.DisplayNameFor(model => model.ADDT16List.First().ANS_COUNT)
                        </samp>
                    </th>
                    <th>
                        <samp class="form-group" style="cursor:pointer;" onclick="FunSort('SNAME')">
                            @Html.DisplayNameFor(model => model.ADDT16List.First().SCHOOL_NAME)
                        </samp>
                    </th>
                    <th>
                        <samp class="form-group" style="cursor:pointer;" onclick="FunSort('SNAME')">
                            @Html.DisplayNameFor(model => model.ADDT16List.First().SNAME)
                        </samp>
                    </th>
                    <th>
                        <samp class="form-group" style="cursor:pointer;" onclick="FunSort('CRE_DATE')">
                        @Html.DisplayNameFor(model => model.ADDT16List.First().CRE_DATE)
                        </samp>

                    </th>
                    <th>
                        @Html.DisplayNameFor(model => model.ADDT16List.First().MEMO)

                    </th>
                </tr>
            </thead>
            <tbody>
                @foreach (var item in Model.ADDT16List)
            {
                    <tr onclick="onBtnLink('@item.QUESTIONS_ID','@EcoolWeb.Controllers.ZZZI11Controller.DATA_TYPE.DATA_TYPE_U')" title="詳細內容" style="cursor:pointer">
                  
                        <td>
                            @Html.DisplayFor(modelItem => item.SUBJECT)
                        </td>
                        <td align="center">
                            <span class="badge">@Html.DisplayFor(modelItem => item.ANS_COUNT)</span>
                        </td>
                        <td>
                            @Html.DisplayFor(modelItem => item.SCHOOL_NAME)
                        </td>
                        <td>
                            @Html.DisplayFor(modelItem => item.SNAME)
                        </td>
                        <td align="center">
                            @Html.DisplayFor(modelItem => item.CRE_DATE)
                        </td>
                        <td style="text-align: left;">
                            <font color="red">
                                @Html.DisplayFor(modelItem => item.MEMO)
                            </font>
                        </td>
                    </tr>
                }
            </tbody>
        </table>
    </div>
</div>


<div>
    @Html.Pager(Model.ADDT16List.PageSize, Model.ADDT16List.PageNumber, Model.ADDT16List.TotalItemCount).Options(o => o
        .DisplayTemplate("BootstrapPagination")
             .MaxNrOfPages(5)
             .SetPreviousPageText("上頁")
             .SetNextPageText("下頁")
    )
</div>



