﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
   public class AWAT14RankCount
    {
        public string WhereSCHOOL_NO { get; set; }
        public string Where<PERSON><PERSON><PERSON><PERSON><PERSON> { get; set; }
        public string SCHO<PERSON>_NO { get; set; }
        public string USER_NO { get; set; }
        public string BGC<PERSON>OR { get; set; }
        public string CLASS_NO { get; set; }
        public string RANK_DESC { get; set; }
        //public double? CLASSSUMKM { get; set; }
        //public double? AVGCLASS { get; set; }
        public int? CASH_ALL { get; set; }
        public int CASH_Rank { get; set; }
        public int CASH_Rank0 { get; set; }
        [DisplayName("等級名稱")]
        public string LEVEL_DESC { get; set; }
       
        [DisplayName("人數")]
        public int LEVEL_DESCCount { get; set; }
        [DisplayName("班級人數")]
        public int CLASSCount { get; set; }
        public List<AWAT14PeopleEditList> AWAT14PeopleEditList { get; set; }
    }
}
