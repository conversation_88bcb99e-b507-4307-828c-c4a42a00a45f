﻿@model EcoolWeb.ViewModels.CloseTransViewMode
@{
    ViewBag.Title = "定存結清";

}
@Html.Partial("_Title_Secondary")
<style>
    .container {
        max-width: 800px;
        margin: 0 auto;
        padding: 40px;
    }

    /*.header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30px;
    }*/
    .school-name {
        display: flex;
        justify-content: center;
        font-size: 24px;
        font-weight: bold;
    }

    .start-btn-wrapper {
        display: flex;
        justify-content: center;
    }

    .start-btn {
        background-color: #007bff;
        color: #fff;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
    }

    .info-section {
        display: flex;
        justify-content: center;
        margin-bottom: 40px;
        margin-top: 5px;
        color: blue;
        font-size: 18px; /* 將文字大小設為 18px */
    }

    .info-section1 {
        display: flex;
        justify-content: center;
        margin-top: 2px;
        color: blue;
        font-size: 18px; /* 將文字大小設為 18px */
    }

    th {
        background-color: white;
        color: black;
    }

    tr:nth-child(even) {
        background-color: white;
    }

    .start-button {
        background-color: #007bff; /* 藍色背景 */
        color: #fff; /* 白色文字 */
        border: none; /* 無邊框 */
        padding: 10px 20px; /* 內邊距 */
        font-size: 16px; /* 文字大小 */
        border-radius: 4px; /* 圓角 */
        cursor: pointer; /* 鼠標指針變為手型 */
        transition: background-color 0.3s ease; /* 漸變效果 */
    }

        .start-button:hover {
            background-color: #0056b3; /* 懸停時變深色 */
        }

    .popup-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.3s, visibility 0.3s;
    }

    .popup-window {
        background-color: white;
        padding: 30px;
        border-radius: 5px;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
        text-align: center;
        max-width: 400px;
        width: 90%;
    }

    .popup-buttons {
        margin-top: 20px;
    }

        .popup-buttons button {
            margin: 0 10px;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            background-color: #007bff;
            color: white;
            cursor: pointer;
        }

            .popup-buttons button.cancel-btn {
                background-color: #6c757d;
            }
</style>
@using (Html.BeginForm("CloseTrans", "AWAI07", FormMethod.Post, new { name = "AWAI07", id = "AWAI07" }))
{


    @Html.HiddenFor(m => m.Page)
    @Html.HiddenFor(m => m.SyntaxName)

    if (Model != null && Model.BDMT01List != null && Model.BDMT01List.Count() > 0 && Model.BDMT01List.Count() > 1)
    {
        <div class="form-group">
            <label class="control-label">書名/學號/姓名</label>
        </div><div class="form-group">

            @Html.EditorFor(m => m.whereKeyword, new { htmlAttributes = new { @class = "form-control input-sm" } })
        </div>
        <button type="button" class="btn-default btn-sm" onclick="FunPageProc(1)" id="glyphicon-search">
            <span class="glyphicon glyphicon-search fa-1x" aria-hidden="true" id="search-img">搜尋</span>
        </button>
        <button type="button" class="btn-default btn-sm" onclick="ClearFun(1)" id="glyphicon-search">
            清除搜尋條件
        </button>
        <div class="table-responsive">

            <div class="text-center" id="tbData">
                <table class="table table-bordered">

                    <thead>

                        <tr>

                            <th>學校名稱</th>
                            <th>進行日期</th>
                            <th>狀態</th>
                            <th></th>


                        </tr>

                    </thead>







                    @*<table>*@
                    @*<tr>
                            <th>學校名稱</th>
                            <th>進行日期</th>
                            <th>狀態</th>
                            <th></th>
                        </tr>*@

                    <tbody>


                        @if (Model != null && Model.BDMT01List != null && Model.BDMT01List.Count() > 0)
                        {
                            foreach (var bDMT in Model.BDMT01List)
                            {

                                <tr>

                                    <td>@bDMT.SCHOOL_NAME</td>
                                    @if (bDMT.ISSquare != null)
                                    {
                                        if (bDMT.ISSquare == true)
                                        {
                                            <td>@bDMT.ISSquareDate</td>
                                            <td>已結清</td>
                                            <td></td>
                                        }
                                        else
                                        {
                                            <td></td>
                                            <td>未執行</td>
                                            <td><button>開始結清</button></td>

                                        }

                                    }
                                    else
                                    {
                                        <td>@bDMT.ISSquareDate</td>
                                        <td>未執行</td>
                                        <td><button type="button" onclick="showPopup('@bDMT.SCHOOL_NO')" >開始結清</button></td>

                                    }


                                </tr>

                            }
                        }
                        <div>
                            @Html.Pager(Model.BDMT01List.PageSize, Model.BDMT01List.PageNumber, Model.BDMT01List.TotalItemCount).Options(o => o
    .DisplayTemplate("BootstrapPagination")
   .MaxNrOfPages(5)
   .SetPreviousPageText("上頁")
   .SetNextPageText("下頁")
)
                        </div>

                    </tbody>

                </table>
            </div>

        </div>

    }
    else
    {
        BDMT01 bDMT01Temp = new BDMT01();
        bDMT01Temp = Model.BDMT01List.FirstOrDefault();

        <div class="container">

            <div class="header">

                <div class="school-info">


                    <div class="school-name">@bDMT01Temp.SCHOOL_NAME</div>


                    @if (bDMT01Temp.ISSquare != null)
                    {
                        if (bDMT01Temp.ISSquare == true)
                        {
                            <div class="start-btn-wrapper">

                                <span>已結清</span>


                            </div>

                        }
                        else
                        {
                            <div class="start-btn-wrapper">
                                <button type="button" onclick="showPopup('@bDMT01Temp.SCHOOL_NO')" >開始</button>
                            </div>

                        }

                    }
                    else
                    {

                        <div class="start-btn-wrapper">
                            <button type="button"onclick="showPopup('@bDMT01Temp.SCHOOL_NO')" >開始</button>
                        </div>

                     }

                    @*<button type="button" onclick="showPopup('@bDMT01Temp.SCHOOL_NO')" class="start-button">開始</button>*@
                </div>
            </div>

            <div class="info-section">

                <p>
                    ※本功能使用時機：酷幣轉為P幣前必須進行的功能。
                </p>

            </div>

            <div class="info-section1">
                <p>

                    一旦執行本功能，所有學生的定存都會自動轉為活存

                </p>
            </div>
            <div class="info-section">
                <p>

                    並依比例給予利息(並且不會打八折計算)。

                </p>
            </div>
        </div>


    }


}
<div class="popup-overlay" id="popup-overlay1">
    <div class="popup-window">
        <h2>您確定要把所有定存轉移嗎?</h2>
        <p>一旦實施將無法復原。</p>
        <div class="popup-buttons">
            <button class="cancel-btn" onclick="hidePopup()">取消</button>
            <button class="confirm-btn" onclick="confirmTransfer()">確定進行</button>
        </div>
    </div>
</div>
<script>
    var targetFormID = "#AWAI07";
    function CLoseTran(SCHOOL_NO) {
        $.ajax({
            url: "@Url.Action("CloseTransSave", (string)ViewBag.BRE_NO)",
            type: 'post',
            data: {
                SCHOOL_NO: SCHOOL_NO
            },
            dataType: 'json',
            cache: false,
            success: function (data) {
                if (data.Success == 'false') {
                    alert(data.Error);
                }
                else {
                    alert("結清完畢");
                    window.location.reload();
                }

            }, error: function () {
                alert("readyState: " + xhr.readyState + "\nstatus: " + xhr.status);
                alert("responseText: " + xhr.responseText);
            }


        });
        //hidePopup();
        ClearFun();
    }
    function showPopup(SCHOOL_NO) {
        $("#SyntaxName").val("");
        $("#SyntaxName").val(SCHOOL_NO);
        const popupOverlay = document.getElementById('popup-overlay1');
        popupOverlay.style.opacity = '1';
        popupOverlay.style.visibility = 'visible';
    }
    function hidePopup() {
        const popupOverlay = document.getElementById('popup-overlay1');
        popupOverlay.style.opacity = '0';
        popupOverlay.style.visibility = 'hidden';
    }

    function confirmTransfer() {
        // 在這裡添加您的確認邏輯
        var scHOOL_NO = $("#SyntaxName").val();
        CLoseTran(scHOOL_NO);

    }
    function FunPageProc(page) {
        $("#Page").val(page);
                if ($(targetFormID).size() > 0) {
                    $('#Page').val(page)
                    $(targetFormID).attr('action','@Html.Raw(@Url.Action("CloseTrans", "AWAI07"))')
                    $(targetFormID).submit();
                }
            };
        function ClearFun() {
            $("#whereKeyword").val("");
            $('#Page').val("1");
       $(targetFormID).attr('action','@Html.Raw(@Url.Action("CloseTrans", "AWAI07"))')
                    $(targetFormID).submit();


        }
</script>
