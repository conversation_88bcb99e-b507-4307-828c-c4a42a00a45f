﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Net;
using System.Net.Mail;
using System.IO;
using ECOOL_APP.com.ecool.util;
using com.ecool.util;
using log4net;

namespace ECOOL_APP.com.ecool.util
{
    public class MailHelper
    {

        static ILog logger = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        /// <summary>
        /// 是否啟用
        /// </summary>
        static bool MailEnabled = Convert.ToBoolean(System.Web.Configuration.WebConfigurationManager.AppSettings["MailEnabled"].Trim());

        /// <summary>
        /// 寄信人Email顯示名稱
        /// </summary>
        static string displayName = "e酷幣";

        /// <summary>
        /// 寄信人Email
        /// </summary>
        static string sendMail = System.Web.Configuration.WebConfigurationManager.AppSettings["sendMail"].Trim();

        /// <summary>
        /// 寄信smtp server
        /// </summary>
        static string smtpServer = System.Web.Configuration.WebConfigurationManager.AppSettings["smtpServer"].Trim();

        /// <summary>
        /// 寄信smtp server的Port，預設25
        /// </summary>
        static int smtpPort = Convert.ToInt32(System.Web.Configuration.WebConfigurationManager.AppSettings["smtpPort"].Trim());

        /// <summary>
        /// 寄信帳號
        /// </summary>
        static string mailAccount = System.Web.Configuration.WebConfigurationManager.AppSettings["mailAccount"].Trim();

        /// <summary>
        /// 寄信密碼
        /// </summary>
        static string mailPwd = System.Web.Configuration.WebConfigurationManager.AppSettings["mailPwd"].Trim();


      
        /// <summary>
        /// 簡易的寄信函數
        /// </summary>
        /// <param name="MailList"></param>
        /// <param name="Subject"></param>
        /// <param name="Body"></param>
        public bool SendMailByGmail(List<string> MailList, string Subject, string Body)
        {
            if (MailEnabled)
            {
                try
                {
                    MailMessage msg = new MailMessage();
                    //收件者，以逗號分隔不同收件者 ex "<EMAIL>,<EMAIL>"

                    msg.To.Add(string.Join(",", MailList.ToArray()));
                  
                    msg.From = new MailAddress(sendMail, displayName, System.Text.Encoding.BigEndianUnicode);
                    //郵件標題 
                    msg.Subject = Subject;

                    //郵件標題編碼  
                    msg.SubjectEncoding = System.Text.Encoding.BigEndianUnicode;

                    //頁尾
                    string footer = @"<br/><br/><br/><br/><br/><br/><font color = 'red'> 請注意：此郵件由系統自動傳送，請勿直接回覆此郵件</font>";

                    //郵件內容
                    msg.Body = Body + footer;

                    msg.IsBodyHtml = true;
                    msg.BodyEncoding = System.Text.Encoding.BigEndianUnicode;//郵件內容編碼 
                    msg.Priority = MailPriority.Normal;//郵件優先級 
                                                       //建立 SmtpClient 物件 並設定 Gmail的smtp主機及Port 
                    #region 其它 Host
                    /*
                     *  outlook.com smtp.live.com port:25
                     *  yahoo smtp.mail.yahoo.com.tw port:465
                    */
                    #endregion

                    SmtpClient MySmtp = new SmtpClient(smtpServer, smtpPort);
                    //設定你的帳號密碼
                    MySmtp.Credentials = new System.Net.NetworkCredential(mailAccount, mailPwd);
                    //Gmial 的 smtp 使用 SSL
                    MySmtp.EnableSsl = true;
                    MySmtp.DeliveryMethod = SmtpDeliveryMethod.Network;
                    MySmtp.Send(msg);
                }
                catch (Exception ex)
                {
                    logger.Error("寄信失敗 : " + ex);
                    return false;
                }
            }
            return true;
        }


        #region 寄信相關

        /// <summary>
        /// 完整的寄信函數
        /// </summary>
        /// <param name="MailFrom">寄信人Email Address</param>
        /// <param name="MailTos">收信人Email Address</param>
        /// <param name="Ccs">副本Email Address</param>
        /// <param name="MailSub">主旨</param>
        /// <param name="MailBody">內文</param>
        /// <param name="isBodyHtml">是否為Html格式</param>
        /// <param name="files">要夾帶的附檔</param>
        /// <returns>回傳寄信是否成功(true:成功,false:失敗)</returns>
        public bool Mail_Send(string MailFrom, string[] MailTos, string[] Ccs, string MailSub, string MailBody, bool isBodyHtml, Dictionary<string, Stream> files)
        {
            if (MailEnabled)
            {
                try
                {
                    //沒給寄信人mail address
                    if (string.IsNullOrEmpty(MailFrom))
                    {//※有些公司的Smtp Server會規定寄信人的Domain Name須是該Smtp Server的Domain Name，例如底下的 system.com.tw
                        MailFrom = sendMail;
                    }

                    //命名空間： System.Web.Mail已過時，http://msdn.microsoft.com/zh-tw/library/system.web.mail.mailmessage(v=vs.80).aspx
                    //建立MailMessage物件
                    MailMessage mms = new MailMessage();
                    //指定一位寄信人MailAddress
                    mms.From = new MailAddress(MailFrom);
                    //信件主旨
                    mms.Subject = MailSub;
                    //信件內容
                    mms.Body = MailBody;

                    //信件內容 是否採用Html格式
                    mms.IsBodyHtml = isBodyHtml;

                    if (MailTos != null)//防呆
                    {
                        for (int i = 0; i < MailTos.Length; i++)
                        {
                            //加入信件的收信人(們)address
                            if (!string.IsNullOrEmpty(MailTos[i].Trim()))
                            {
                                mms.To.Add(new MailAddress(MailTos[i].Trim()));
                            }

                        }
                    }//End if (MailTos !=null)//防呆

                    if (Ccs != null) //防呆
                    {
                        for (int i = 0; i < Ccs.Length; i++)
                        {
                            if (!string.IsNullOrEmpty(Ccs[i].Trim()))
                            {
                                //加入信件的副本(們)address
                                mms.CC.Add(new MailAddress(Ccs[i].Trim()));
                            }

                        }
                    }//End if (Ccs!=null) //防呆


                    //附件處理
                    if (files != null && files.Count > 0)//寄信時有夾帶附檔
                    {
                        foreach (string fileName in files.Keys)
                        {
                            Attachment attfile = new Attachment(files[fileName], fileName);
                            mms.Attachments.Add(attfile);
                        }//end foreach
                    }//end if 

                    using (SmtpClient client = new SmtpClient(smtpServer, smtpPort))//或公司、客戶的smtp_server
                    {
                        if (!string.IsNullOrEmpty(mailAccount) && !string.IsNullOrEmpty(mailPwd))//.config有帳密的話
                        {//寄信要不要帳密？眾說紛紜Orz，分享一下經驗談....

                            //網友阿尼尼:http://www.dotblogs.com.tw/kkc123/archive/2012/06/26/73076.aspx
                            //※公司內部不用認證,寄到外部信箱要特別認證 Account & Password

                            //自家公司MIS:
                            //※要看smtp server的設定呀~

                            //結論...
                            //※程式在客戶那邊執行的話，問客戶，程式在自家公司執行的話，問自家公司MIS，最準確XD
                            client.Credentials = new NetworkCredential(mailAccount, mailPwd);//寄信帳密
                        }
                        client.Send(mms);//寄出一封信
                    }//end using 

                    //釋放每個附件，才不會Lock住
                    if (mms.Attachments != null && mms.Attachments.Count > 0)
                    {
                        for (int i = 0; i < mms.Attachments.Count; i++)
                        {
                            mms.Attachments[i].Dispose();
                            mms.Attachments[i] = null;
                        }
                    }

                    return true;//成功
                }
                catch (Exception ex)
                {
                   
                    logger.Error("寄信失敗 : " + ex);

                    return false;
                }
            }


            return true;

        }//End 寄信
        #endregion
    }
}
