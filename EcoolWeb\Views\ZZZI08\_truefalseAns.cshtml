﻿@model ECOOL_APP.com.ecool.Models.DTO.ZZZI08.ZZZI08ADDT12ViewModel

@using (Html.BeginCollectionItem("truefalseAns", Model.isCopy))
{
    var index = Html.GetIndex("truefalseAns");
    <div id="@index">
        @Html.HiddenFor(m => m.Html_ID, new { @Value = index })
        @Html.HiddenFor(m => m.ITEM)
        @Html.HiddenFor(m => m.Q_NUM)
        @Html.HiddenFor(m => m.Q_TYPE)

        <div class="panel panel-default">
            <div class="panel-body">
                <div class="form-group">
                    <div class="col-md-12 control-label-left">
                        正確答案
                    </div>
                    <div class="col-md-12 control-label-left">
                        @Html.EditorFor(m => m.TRUE_ANS,
                            "_RadioButtonList",
                            new
                            {
                                TagName = (Html.NameFor(m => m.TRUE_ANS)).ToHtmlString(),
                                RadioItems = EcoolWeb.Util.HtnlHelperService.OXSelectItem(Model.TRUE_ANS),
                                Position = Position.Horizontal,
                                Numbers = int.MaxValue
                            })
                    </div>
                </div>
                <div class="form-group">
                    <div class="col-md-12 control-label-left">
                        @Html.DisplayNameFor(m => m.Q_TEXT)
                        <a title="一般輸入"   style="cursor:pointer;display:none"   class="glyphicon glyphicon-pencil fa-1x" role="button"   onclick="OnremoveCkeditor('@Html.IdFor(m => m.Q_TEXT)')" id="<EMAIL>(m => m.Q_TEXT)"> </a>
                        <a title="編輯器輸入" style="cursor:pointer;display:inline" class="glyphicon glyphicon-edit fa-1x"   role="button"   onclick="OnLondCkeditor('@Html.IdFor(m => m.Q_TEXT)')"   id="<EMAIL>(m => m.Q_TEXT)" ></a>
                    </div>
                    <div class="col-md-12 text-left">
                        @Html.TextAreaFor(m => m.Q_TEXT, new { @class = "form-control", @rows = "2" })
                        @Html.ValidationMessageFor(m => m.Q_TEXT, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group">
                    <div class="col-md-12 text-right">
                        @if (Model.ShowBtn)
                        {
                            <button class="btn btn-info btn-sm glyphicon glyphicon-share-alt" type="button" onclick="onCopyItem('@index','TruefalseEditorRows', '_truefalseAnsCopy')" title="複製"></button>
                            <button class="btn btn-danger btn-sm glyphicon glyphicon-trash" type="button" onclick="onDelItem('@index')" title="刪除"></button>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>
}