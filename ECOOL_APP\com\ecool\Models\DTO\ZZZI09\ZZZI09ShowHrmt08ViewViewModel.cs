﻿
using DotNet.Highcharts;
using ECOOL_APP.com.ecool.Models.DTO;
using MvcPaging;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;

namespace ECOOL_APP.EF
{
    public class ZZZI09ShowHrmt08ViewViewModel
    {
        /// <summary>
        /// 只顯示某身份証
        /// </summary>
        public string whereIDN<PERSON> { get; set; }

        /// <summary>
        /// 查詢結果
        /// </summary>
        public IPagedList<SECI03Hrmt08ListViewModel> Hrmt08List;

        /// <summary>
        /// 身高趨示圖
        /// </summary>
        public Highcharts TALLchart;

        /// <summary>
        /// 體重趨示圖
        /// </summary>
        public Highcharts WEIGHTchart;


        /// <summary>
        /// 視力長條圖 RIGHT 
        /// </summary>
        public Highcharts RIGHT_VISIONColumnChart;

        /// <summary>
        /// 視力長條圖 LEFT
        /// </summary>
        public Highcharts LEFT_VISIONColumnChart;
    }
}