﻿using ECOOL_APP.com.ecool.Models.entity;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace com.ecool.service
{
    public class HRMT24Service 
    {
        /// <summary>
        /// 取得角色清單
        /// </summary>
        /// <param name="ROLE_ID">角色代碼</param>
        /// <param name="ROLE_NAME">角色名稱</param>
        /// <param name="NotROLE_ID">不需要的角色代碼</param>
        /// <param name="R_TYPE">1.(全部),2.依權限取得角色清單</param>
        /// <param name="User">UserProfile</param>
        /// <returns></returns>
        public static List<uHRMT24> USP_HRMT24_QUERY(string ROLE_ID , string ROLE_NAME, string NotROLE_ID, string R_TYPE, decimal? ROLE_LEVEL, ECOOL_APP.UserProfile User)
        {

            List<uHRMT24> list_data = new List<uHRMT24>();
            StringBuilder sb = new StringBuilder();
            DataTable dt;
            try
            {
                sb.Append(" SELECT A.ROLE_ID,A.ROLE_NAME,A.CHG_PERSON,A.CHG_DATE,A.ROLE_TYPE,A.ROLE_LEVEL  ");
                sb.Append(" FROM HRMT24 A (nolock) ");
                sb.Append(" WHERE 1 = 1 ");

                if (ROLE_ID != string.Empty)
                {
                    sb.AppendFormat(" AND A.ROLE_ID ='{0}' ", ROLE_ID);
                }

                if (ROLE_NAME != string.Empty)
                {
                    sb.AppendFormat(" AND ROLE_NAME ='{0}' ", ROLE_NAME);
                }

                if (NotROLE_ID != string.Empty)
                {
                    sb.AppendFormat(" AND ROLE_ID  NOT IN ('{0}') ", NotROLE_ID);
                }

                if (R_TYPE=="2")
                {
                   sb.AppendFormat(" AND ROLE_LEVEL >={0} ", User.ROLE_LEVEL);
                }

                if (ROLE_LEVEL != null)
                {
                    sb.AppendFormat(" AND ROLE_LEVEL  = {0}  ", ROLE_LEVEL);
                }

                sb.Append(" Order by ROLE_TYPE,ROLE_LEVEL ");

                dt = new sqlConnection.sqlConnection().executeQueryByDataTableList(sb.ToString());

                foreach (DataRow dr in dt.Rows)
                {
                    uHRMT24 Data = new uHRMT24();

                    Data.ROLE_ID = dr["ROLE_ID"].ToString();
                    Data.ROLE_NAME = dr["ROLE_NAME"].ToString();
                    Data.ROLE_TYPE = dr["ROLE_TYPE"] == DBNull.Value ? (int?)null : (int)dr["ROLE_TYPE"];
                    Data.ROLE_LEVEL = dr["ROLE_LEVEL"] == DBNull.Value ? (decimal?)null : (decimal)dr["ROLE_LEVEL"];
                    list_data.Add(Data);
                }

                dt.Clear();
                dt.Dispose();
                sb.Clear();
            }
            catch (Exception exception)
            {
                throw exception;
            }
            return list_data;
        }
    }
}
