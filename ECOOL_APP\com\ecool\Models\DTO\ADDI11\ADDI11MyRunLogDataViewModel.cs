﻿
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.IO;
using System.Linq;
using System.Text;

namespace ECOOL_APP.EF
{
    public class ADDI11MyRunLogDataViewModel
    {
        /// <summary>
        ///跑步NO
        /// </summary>
        [DisplayName("跑步NO")]
        public string RUN_ID { get; set; }

        /// <summary>
        ///學校代碼
        /// </summary>
        [DisplayName("學校代碼")]
        public string SCHOOL_NO { get; set; }

        /// <summary>
        ///帳號
        /// </summary>
        [DisplayName("帳號")]
        public string USER_NO { get; set; }

        /// <summary>
        ///班級
        /// </summary>
        [DisplayName("班級")]
        public string CLASS_NO { get; set; }

        /// <summary>
        ///學年
        /// </summary>
        [DisplayName("學年")]
        public byte? SYEAR { get; set; }

        /// <summary>
        ///學期
        /// </summary>
        [DisplayName("學期")]
        public byte? SEMESTER { get; set; }

        /// <summary>
        ///座號
        /// </summary>
        [DisplayName("座號")]
        public string SEAT_NO { get; set; }

        /// <summary>
        ///姓名
        /// </summary>
        [DisplayName("姓名")]
        public string NAME { get; set; }

        /// <summary>
        ///簡稱
        /// </summary>
        [DisplayName("簡稱")]
        public string SNAME { get; set; }

        /// <summary>
        ///跑步日期
        /// </summary>
        [DisplayName("跑步日期")]
        [DisplayFormat(DataFormatString = "{0:yyyy/MM/dd}", ApplyFormatInEditMode = true)]
        public DateTime? RUN_DATE { get; set; }

        /// <summary>
        ///圈數
        /// </summary>
        [DisplayName("圈數")]
        public double? LAP { get; set; }

        /// <summary>
        ///公尺
        /// </summary>
        [DisplayName("公尺")]
        public int? LAP_M { get; set; }
        [DisplayName("今日總圈數")]
        public double? SUMLap { get; set; }
        /// <summary>
        ///建立人
        /// </summary>
        [DisplayName("建立人")]
        public string CRE_PERSON { get; set; }

        /// <summary>
        ///建立日期
        /// </summary>
        [DisplayName("建立日期")]
        public DateTime? CRE_DATE { get; set; }

        /// <summary>
        ///修改人
        /// </summary>
        [DisplayName("修改人")]
        public string CHG_PERSON { get; set; }

        /// <summary>
        ///修改日期
        /// </summary>
        [DisplayName("修改日期")]
        public DateTime? CHG_DATE { get; set; }

        /// <summary>
        ///狀態
        /// </summary>
        [DisplayName("狀態")]
        public string STATUS { get; set; }



        /// <summary>
        ///累計里程
        /// </summary>
        [DisplayName("累計里程")]
        public double Total_M { get; set; }

    }
}