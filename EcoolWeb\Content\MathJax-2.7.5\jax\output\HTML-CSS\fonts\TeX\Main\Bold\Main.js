/*
 *  /MathJax/jax/output/HTML-CSS/fonts/TeX/Main/Bold/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["MathJax_Main-bold"]={directory:"Main/Bold",family:"MathJax_Main",weight:"bold",testString:"MathJax Main ^ \u210F \u2223",skew:{305:0.0319,567:0.0958,8463:-0.0319,8467:0.128,8706:0.0958},Ranges:[[160,255,"Latin1Supplement"],[256,383,"LatinExtendedA"],[384,591,"LatinExtendedB"],[688,767,"SpacingModLetters"],[768,879,"CombDiacritMarks"],[8192,8303,"GeneralPunctuation"],[8400,8447,"CombDiactForSymbols"],[8448,8527,"LetterlikeSymbols"],[8592,8703,"Arrows"],[8704,8959,"MathOperators"],[8960,9215,"MiscTechnical"],[9632,9727,"GeometricShapes"],[9728,9983,"MiscSymbols"],[10176,10223,"MiscMathSymbolsA"],[10224,10239,"SupplementalArrowsA"],[10752,11007,"SuppMathOperators"]],32:[0,0,250,0,0],33:[705,-1,350,89,260],34:[694,-329,603,38,492],35:[694,193,958,64,893],36:[750,56,575,64,510],37:[750,56,958,65,893],38:[705,11,894,48,836],39:[694,-329,319,74,261],40:[750,249,447,103,382],41:[750,249,447,64,343],42:[750,-306,575,73,501],43:[633,131,894,64,829],44:[171,194,319,74,258],45:[278,-166,383,13,318],46:[171,-1,319,74,245],47:[750,250,575,63,511],48:[654,10,575,45,529],49:[655,0,575,80,494],50:[654,0,575,57,517],51:[655,11,575,47,526],52:[656,0,575,32,542],53:[655,11,575,57,517],54:[655,11,575,48,526],55:[676,11,575,64,558],56:[654,11,575,48,526],57:[654,11,575,48,526],58:[444,-1,319,74,245],59:[444,194,319,74,248],60:[587,85,894,96,797],61:[393,-109,894,64,829],62:[587,85,894,96,797],63:[700,-1,543,65,478],64:[699,6,894,64,829],65:[698,0,869,40,828],66:[686,0,818,39,752],67:[697,11,831,64,766],68:[686,0,882,39,817],69:[680,0,756,39,723],70:[680,0,724,39,675],71:[697,10,904,64,845],72:[686,0,900,39,860],73:[686,0,436,25,410],74:[686,11,594,8,527],75:[686,0,901,39,852],76:[686,0,692,39,643],77:[686,0,1092,39,1052],78:[686,0,900,39,860],79:[696,10,864,64,798],80:[686,0,786,39,721],81:[696,193,864,64,805],82:[686,11,862,39,858],83:[697,11,639,64,574],84:[675,0,800,41,758],85:[686,11,885,39,845],86:[686,7,869,25,843],87:[686,7,1189,24,1164],88:[686,0,869,33,835],89:[686,0,869,19,849],90:[686,0,703,64,645],91:[750,250,319,128,293],92:[750,250,575,63,511],93:[750,250,319,25,190],94:[694,-520,575,126,448],95:[-10,61,575,0,574],96:[706,-503,575,114,338],97:[453,6,559,32,558],98:[694,6,639,29,600],99:[453,6,511,39,478],100:[694,6,639,38,609],101:[452,6,527,32,494],102:[700,0,351,40,452],103:[455,201,575,30,558],104:[694,0,639,37,623],105:[695,0,319,40,294],106:[695,200,351,-71,274],107:[694,0,607,29,587],108:[694,0,319,40,301],109:[450,0,958,37,942],110:[450,0,639,37,623],111:[452,5,575,32,542],112:[450,194,639,29,600],113:[450,194,607,38,609],114:[450,0,474,29,442],115:[453,6,454,38,414],116:[635,5,447,21,382],117:[450,6,639,37,623],118:[444,3,607,26,580],119:[444,4,831,25,805],120:[444,0,607,21,586],121:[444,200,607,23,580],122:[444,0,511,32,462],123:[750,250,575,70,504],124:[750,249,319,129,190],125:[750,250,575,70,504],126:[344,-202,575,96,478],915:[680,0,692,39,643],916:[698,0,958,56,901],920:[696,10,894,64,829],923:[698,0,806,40,765],926:[675,0,767,48,718],928:[680,0,900,39,860],931:[686,0,831,63,766],933:[697,0,894,64,829],934:[686,0,831,64,766],936:[686,0,894,64,829],937:[696,0,831,51,779]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"MathJax_Main-bold"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Main/Bold/Main.js"]);
