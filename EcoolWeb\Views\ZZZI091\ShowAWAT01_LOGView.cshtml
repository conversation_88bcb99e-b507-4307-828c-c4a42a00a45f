﻿@model ZZZI09ShowAWAT01_LOGViewViewModel
@using ECOOL_APP.com.ecool.util

@if (TempData["StatusMessage"] != null)
{
    @Html.Partial("_Notice")
}
else
{

    int TrNum = 1;
    <table class="table-ecool table-92Per table-hover table-ecool-AWA003">
        <thead>
            <tr>
                <td align="center">
                    日期
                </td>
                <td align="center">
                    支出
                </td>
                <td align="center">
                    存入
                </td>
                <td align="center">
                    給扣點人員
                </td>
                <td align="center">
                    異動說明
                </td>
                <td align="center">
                    目前點數
                </td>
            </tr>
        </thead>
        <tbody>
            @foreach (var item in Model.AWAT01_LOG_List)
            {
                <tr style="@(TrNum % 2 ==0 ? "background-color:#D3FF93" : "")">
                    <td align="center">
                        @Html.DisplayFor(modelItem => item.LOG_TIME, "ShortDateTime")
                    </td>
                        <td align="center">
                            @if (item.CASH_IN < 0)
                        {
                                @(item.CASH_IN * -1);
                        }
                        </td>
                        <td align="center">
                            @if (item.CASH_IN >= 0)
                        {
                                @Html.DisplayFor(modelItem => item.CASH_IN)
                            }
                        </td>
                        <td align="center">
                            @Html.DisplayFor(modelItem => item.LOG_PERSON_NAME)
                        </td>

                        <td style="text-align: left;white-space:normal">
                            @StringHelper.LeftStringR(item.LOG_DESC, 15)
                        </td>
                        <td align="center">
                            @Html.DisplayFor(modelItem => item.AWAT01_CASH_AVAILABLE)
                        </td>
                   
                </tr>

                TrNum = TrNum + 1;
            }
        </tbody>
        <tfoot></tfoot>
    </table>
}
 
