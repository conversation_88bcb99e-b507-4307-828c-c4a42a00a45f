﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Entity;
using System.Linq;
using System.Net;
using System.Web;
using System.Web.Mvc;
using ECOOL_APP.EF;
using EcoolWeb.CustomAttribute;

namespace EcoolWeb.Controllers
{
    [SessionExpire]
    public class BET02EntityController : Controller
    {
        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();

        // GET: BET02Entity
        public ActionResult Index()
        {
            return View(db.BET02.ToList());
        }

        // GET: BET02Entity/Details/5
        public ActionResult Details(string id)
        {
            if (id == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }
            BET02 bET02 = db.BET02.Find(id);
            if (bET02 == null)
            {
                return HttpNotFound();
            }
            return View(bET02);
        }

        // GET: BET02Entity/Create
        public ActionResult Create()
        {
            return View();
        }

        // POST: BET02Entity/Create
        // 若要免於過量張貼攻擊，請啟用想要繫結的特定屬性，如需
        // 詳細資訊，請參閱 http://go.microsoft.com/fwlink/?LinkId=317598。
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Create([Bind(Include = "BULLET_ID,SCHOOL_NO,CLASS_TYPE,SUBJECT,CONTENT_TXT,ISPUBLISH,S_DATE,E_DATE,TOP_YN,CRE_PERSON,CRE_DATE,CHG_PERSON,CHG_DATE,MEMO")] BET02 bET02)
        {
            if (ModelState.IsValid)
            {
                db.BET02.Add(bET02);
                db.SaveChanges();
                return RedirectToAction("Index");
            }

            return View(bET02);
        }

        // GET: BET02Entity/Edit/5
        public ActionResult Edit(string id)
        {
            if (id == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }
            BET02 bET02 = db.BET02.Find(id);
            if (bET02 == null)
            {
                return HttpNotFound();
            }
            return View(bET02);
        }

        // POST: BET02Entity/Edit/5
        // 若要免於過量張貼攻擊，請啟用想要繫結的特定屬性，如需
        // 詳細資訊，請參閱 http://go.microsoft.com/fwlink/?LinkId=317598。
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit([Bind(Include = "BULLET_ID,SCHOOL_NO,CLASS_TYPE,SUBJECT,CONTENT_TXT,ISPUBLISH,S_DATE,E_DATE,TOP_YN,CRE_PERSON,CRE_DATE,CHG_PERSON,CHG_DATE,MEMO")] BET02 bET02)
        {
            if (ModelState.IsValid)
            {
                db.Entry(bET02).State = System.Data.Entity.EntityState.Modified;
                db.SaveChanges();
                return RedirectToAction("Index");
            }
            return View(bET02);
        }

        // GET: BET02Entity/Delete/5
        public ActionResult Delete(string id)
        {
            if (id == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }
            BET02 bET02 = db.BET02.Find(id);
            if (bET02 == null)
            {
                return HttpNotFound();
            }
            return View(bET02);
        }

        // POST: BET02Entity/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public ActionResult DeleteConfirmed(string id)
        {
            BET02 bET02 = db.BET02.Find(id);
            db.BET02.Remove(bET02);
            db.SaveChanges();
            return RedirectToAction("Index");
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
