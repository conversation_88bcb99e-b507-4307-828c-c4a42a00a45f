﻿@model GAAI01MonthWearIndexViewModel
@using ECOOL_APP.com.ecool.util;
@{    ECOOL_DEVEntities db = new ECOOL_DEVEntities();
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    ViewBag.Title = ViewBag.Panel_Title;
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
}
<div class="row" id="btnPirnt">
    <div class="col-md-12 text-center">
        <button type="button" class="btn btn-default btn-xs no-print" onclick="onprint()">
            列印
        </button>
        <button type="button" class="btn btn-default btn-xs no-print" onclick="fn_save()">
            另存Excel
        </button>
    </div>
</div>

<div id="printDIV" style="margin: 0px auto;">

    <style>
        #Main td {
            height: 45px;
            font-size:14px;
            border: 1px solid #000000;
        }
        #Main tr {
            border: 1px solid #000000;
        }
    </style>
    @if (Model.MonthMaxGradeWearrRate != null && Model.dMT01 != null)
    {
        <div class="text-center">
            <h4>
                @(Model.dMT01.SCHOOL_NAME) 防身警報器每月工作檢核表
            </h4>
        </div>
        <table id="Main" align="center" style="border: 1px solid #000000; border-collapse: collapse;padding:5px;width:95%" rules="all" cellpadding='5' ;>
            <tr>
                <td align="center">學校名稱</td>
                <td colspan="4" align="center">@Model.dMT01.SCHOOL_NAME</td>
                <td align="center">檢核月份</td>
                <td>@DateHelper.ToSimpleTaiwanDateCYYMM(Model.WhereYearMonth, ".")</td>

            </tr>
            <tr>
                <td rowspan="8" align="center" valign="middle">配<br />載<br />率</td>
            </tr>

            <tr align="center">
                <td>抽檢日期</td>
                <td>年級</td>
                <td>學生人數</td>
                <td>配載人數</td>
                <td>配載比率%</td>
                <td>未配載原因</td>

            </tr>
            @if (Model.MonthMaxGradeWearrRate.Count() == 0)
            {


                <tr align="center">
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
                <tr align="center">
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
                <tr align="center">
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
                <tr align="center">
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
                <tr align="center">
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
                <tr align="center">
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
                <tr align="center">
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
            }

            else
            {

                byte[] chineseNumber = { 1, 2, 3, 4, 5, 6 };
                foreach (var item1 in chineseNumber)
                {
                    GAAI01MonthMaxGradeWearrRateViewModel item = new GAAI01MonthMaxGradeWearrRateViewModel();

                    item = Model.MonthMaxGradeWearrRate.Where(x => x.GRADE == item1).FirstOrDefault();
                    if (item != null)
                    {
                        decimal wareRate = 0;
                        int STUDNUMber = 0;

                        STUDNUMber = db.HRMT01.Where(x => x.SCHOOL_NO == user.SCHOOL_NO && x.GRADE == item.GRADE && x.USER_TYPE == "S" && x.USER_STATUS == UserStaus.Enabled).Count();
                <tr align="center">
                    <td></td>
                    <td>@item.GRADE</td>
                    <td>@STUDNUMber</td>
                    <td>@item.WEAR_NUMBER</td>

                    @if (item.WEAR_NUMBER > STUDNUMber)
                    {
                        wareRate = 1;




                    }

                    else
                    {
                        if (STUDNUMber != 0)
                        {

                            wareRate = (decimal)((decimal)item.WEAR_NUMBER / (decimal)STUDNUMber);
                        }

                        //  wareRate = item.WEAR_RATE.Value;


                    }
                    <td>
                        @if (wareRate != null)
                        {
                             @wareRate.ToString("#,0%")

                        }
                    </td>
                    <td>@item.UN_WEAR_MEMO</td>

                </tr>
                    }
                    else
                    {
                        <tr align="center">
                            <td>@item1</td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>

                        </tr>

                    }
                }
            }

            <tr align="center">
                <td>作為</td>
                <td colspan="5">工作項目</td>
                <td>是否完成</td>
            </tr>
            <tr>
                <td align="center">一</td>
                <td colspan="5">於學校首頁設定防身警報器專網連結</td>
                <td>
                    是☑  否☐
                    <br />備註
                </td>
              

            </tr>
            <tr>
                <td align="center">二</td>
                <td colspan="5">期初提供給家長的一封信</td>
                <td>
                    是☑  否☐
                    <br />備註
                </td>
              
            </tr>
            <tr>
                <td align="center">三</td>
                <td colspan="5">張貼海報及跑馬燈</td>
                <td>
                    是☑  否☐
                    <br />備註
                </td>
            
            </tr>
            <tr>
                <td align="center">四</td>
                <td colspan="5">播放教學影片</td>
                <td>
                    是☑  否☐
                    <br />備註
                </td>
             
            </tr>
            <tr>
                <td align="center">五</td>
                <td colspan="5">舉辦安全演練</td>
                <td>
                    是☑  否☐
                    <br />各班教學時進行安全演練
                </td>
               
            </tr>
            <tr>
                <td align="center">六</td>
                <td colspan="5">班級導師排路隊時檢查配戴</td>
                <td>
                    是☑  否☐
                    <br />備註
                </td>
               
            </tr>
            <tr>
                <td align="center">七</td>
                <td colspan="5">生教組及導護於上放學時檢查</td>
                <td>
                    是☑  否☐
                    <br />備註
                </td>
               
            </tr>
            <tr>
                <td align="center">八</td>
                <td colspan="5">訂定學生配戴防身警報器獎勵機制</td>
                <td>
                    是☑  否☐
                    <br />備註
                </td>
              
            </tr>
            <tr>
                <td align="center">九</td>
                <td colspan="5">其他作為：配合學校日對家長進行講解</td>
                <td></td>
           
            </tr>
        </table>
        <table style="width:95%" align="center">
            <tr>
                <td colspan="7">
                    說明：請學校每月檢核紀錄，以提供督學到校視導複核。
                </td>
            </tr>
        </table>
        <table style="width:95%" align="center">
            <tr>
                <td colspan="2">核章：填表人</td>
                <td colspan="2">主任</td>
                <td colspan="2">校長</td>
                <td colspan="1">督學複核</td>
            </tr>
        </table>
    }
</div>

@section Scripts {
    <script src="~/Scripts/printThis/printThis.js"></script>
    <script src="~/Scripts/Blob.js"></script>
    <script src="~/Scripts/FileSaver.js"></script>
    <script src="~/Scripts/tableexport.js"></script>
    <script language="JavaScript">
        window.onload = function () {
            onprint();
        }

        function onprint() {
            $('#btnPirnt').hide();
            $('#printDIV').printThis();
            $('#btnPirnt').show();
        }

        function fn_save() {
            var blob = new Blob([document.getElementById('printDIV').innerHTML], {
                type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8"
            });
            var strFile = "Report.xls";

            saveAs(blob, strFile);
            funAjax()
            return false;
        }
    </script>
}