/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX-Web/Monospace/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.STIXMathJax_Monospace={directory:"Monospace/Regular",family:"STIXMathJax_Monospace",testString:"\u00A0\uD835\uDE70\uD835\uDE71\uD835\uDE72\uD835\uDE73\uD835\uDE74\uD835\uDE75\uD835\uDE76\uD835\uDE77\uD835\uDE78\uD835\uDE79\uD835\uDE7A\uD835\uDE7B\uD835\uDE7C\uD835\uDE7D",32:[0,0,525,0,0],160:[0,0,525,0,0],120432:[673,0,525,26,496],120433:[662,0,525,29,480],120434:[672,11,525,40,482],120435:[662,0,525,25,483],120436:[662,0,525,31,500],120437:[662,0,525,34,488],120438:[672,11,525,37,495],120439:[662,0,525,26,496],120440:[662,0,525,84,438],120441:[662,11,525,85,476],120442:[662,0,525,30,494],120443:[662,0,525,37,487],120444:[662,0,525,21,501],120445:[662,0,525,31,491],120446:[672,11,525,56,466],120447:[662,0,525,31,479],120448:[672,139,525,56,466],120449:[662,11,525,26,520],120450:[672,11,525,52,470],120451:[662,0,525,26,496],120452:[662,11,525,9,514],120453:[662,8,525,17,506],120454:[662,8,525,11,512],120455:[662,0,525,24,497],120456:[662,0,525,15,507],120457:[662,0,525,47,479],120458:[459,6,525,58,516],120459:[609,6,525,17,481],120460:[459,6,525,78,464],120461:[609,6,525,41,505],120462:[459,6,525,60,462],120463:[615,0,525,42,437],120464:[461,228,525,29,508],120465:[609,0,525,17,505],120466:[610,0,525,84,448],120467:[610,227,525,47,362],120468:[609,0,525,24,505],120469:[609,0,525,63,459],120470:[456,0,525,2,520],120471:[456,0,525,17,505],120472:[459,6,525,62,460],120473:[456,221,525,17,481],120474:[456,221,525,45,530],120475:[456,0,525,37,485],120476:[459,6,525,72,457],120477:[580,6,525,25,448],120478:[450,6,525,17,505],120479:[450,4,525,22,500],120480:[450,4,525,15,508],120481:[450,0,525,23,498],120482:[450,227,525,24,501],120483:[450,0,525,32,473],120822:[681,11,525,55,467],120823:[681,0,525,110,435],120824:[681,0,525,52,470],120825:[681,11,525,43,479],120826:[682,0,525,29,493],120827:[670,11,525,52,470],120828:[681,11,525,58,464],120829:[686,11,525,43,479],120830:[681,11,525,43,479],120831:[681,11,525,58,464]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"STIXMathJax_Monospace"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Monospace/Regular/Main.js"]);
