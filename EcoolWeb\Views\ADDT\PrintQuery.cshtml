﻿@model EcoolWeb.Models.ADDT06ViewModel
@{
    ViewBag.Title = "認證審核一覽表 PRINT";
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
    int i = 1;

}
<style type="text/css" media="print">
    /* 列印時隱藏 */
    .cScreen {
        display: none;
    }
</style>
<script src="~/Scripts/Blob.js"></script>
<script src="~/Scripts/FileSaver.js"></script>
<script src="~/Scripts/tableexport.js"></script>
<div class="Div-EZ-Right cScreen">
    <button id="ButtonExcel" class="btn btn-sm btn-sys">匯出excel</button>
</div>

<div class="print table-92Per" style="margin: 0px auto; " id="tbData">
    <table class='table-ecool table-ecool-pink-SEC' border='1'>
        <thead>
            <tr></tr>
            <tr class='text-center'>
                <th>
                    申請日期
                </th>
                <th>
                    班級
                </th>
                <th>
                    座號
                </th>
                <th>
                    姓名
                </th>
                <th>
                    書名
                </th>
                <th>
                    狀態
                </th>
            </tr>
        </thead>
        <tbody>
            @foreach (var item in Model.ADDT06List)
            {
                if (i % 50 == 0)
                {
                    @Html.Raw(@"</tbody></table>");
                    @Html.Raw(@"<div style='page-break-before:always;'></div><br/>");
                    @Html.Raw(@"<table class='table-ecool table-ecool-pink-SEC' border='1'>
                        <thead>
                            <tr></tr>
                            <tr class='text-center'>
                                <th>
                                    申請日期
                                </th>
                                <th>
                                    班級
                                </th>
                                <th>
                                    座號
                                </th>
                                <th>
                                    姓名
                                </th>
                                <th>
                                    書名
                                </th>
                                <th>
                                    狀態
                                </th>
                            </tr>
                        </thead>
                   <tbody>");
                }

                <tr>
                    <td>
                        @Html.DisplayFor(modelItem => item.CRE_DATE, "ShortDateTime")
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.CLASS_NO)
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.SEAT_NO)
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.NAME)
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.BOOK_NAME)
                    </td>
                    <td>
                        @ECOOL_APP.EF.ADDStatus.GetADDT06StatusString(item.APPLY_STATUS)
                    </td>
                 
                </tr>

                i = i + 1;
            }
        </tbody>
    </table>
</div>



<script type="text/javascript">
    window.onload = function () {
        window.print()
    }



    $(function () {
        $('#ButtonExcel').click(function () {
            var blob = new Blob([document.getElementById('tbData').innerHTML], {
                type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8"
            });
            var strFile = "Report.xls";
            saveAs(blob, strFile);
            return false;
        });
    });
</script>

