﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace ECOOL_APP.EF
{
    [MetadataType(typeof(HRMT01MetaData))]
    public partial class HRMT01
    {
        public class HRMT01MetaData
        {
            [DisplayName("學號")]
            public string USER_NO { get; set; }

            [DisplayName("班級")]
            public string CLASS_NO { get; set; }

            [DisplayName("座號")]
            public string SEAT_NO { get; set; }

            [DisplayName("姓名")]
            public string NAME { get; set; }

            [DisplayName("姓名")]
            public string SNAME { get; set; }

            [DisplayName("學校")]
            public string SCHOOL_NO { get; set; }

            [DisplayName("USER_KEY")]
            public string USER_KEY { get; set; }

            [DisplayName("性別")]
            public string SEX { get; set; }

            public Nullable<byte> GRADE { get; set; }

            public Nullable<System.DateTime> BIRTHDAY { get; set; }
            public Nullable<byte> USER_STATUS { get; set; }
            public string USER_TYPE { get; set; }
            public string TEACHER_TYPE { get; set; }
            public string E_MAIL { get; set; }
            public string TEL_NO { get; set; }
            public string PHONE { get; set; }
      
            public virtual ZZT08 ZZT08 { get; set; }
        }
    }
}
