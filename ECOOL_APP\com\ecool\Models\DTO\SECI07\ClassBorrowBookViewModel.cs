﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.com.ecool.Models.DTO
{
    public class ClassBorrowBookViewModel
    {
        public string Where_SCHOOLNO { get; set; }
        [Display(Name = "班級")]
        public string Where_CLASSNO { get; set; }
        [Display(Name = "學年度")]
        public string Where_SYEAR { get; set; }
        [Display(Name = "月份")]
        public string Where_MONTH { get; set; }

        /// <summary>
        /// 借書總計
        /// </summary>
        public int BorrowBookTotal { get {
                int totalBorrow = 0;
                if (this.BorrowCountList != null)
                {
                    foreach(var item in BorrowCountList)
                    {
                        totalBorrow += item.BORROW_BOOK_COUNT;
                    }
                }
                return totalBorrow;
            } }

        public List<ClassBorrowBook> BorrowCountList { get; set; }
    }

    public class ClassBorrowBook
    {
        [Display(Name = "班級")]
        public string CLASS_NO { get; set; }
        [Display(Name = "借書量(本)")]
        public int BORROW_BOOK_COUNT { get; set; }
    }
}
