/*
 *  /MathJax/jax/output/HTML-CSS/fonts/TeX/Main/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.MathJax_Main={directory:"Main/Regular",family:"MathJax_Main",testString:"MathJax Main ^ \u210F \u2223",skew:{305:0.0278,567:0.0833,8467:0.111,8472:0.111,8706:0.0833},Ranges:[[688,767,"SpacingModLetters"],[768,879,"CombDiacritMarks"],[9632,9727,"GeometricShapes"],[9728,9983,"MiscSymbols"]],32:[0,0,250,0,0],33:[716,-1,278,78,199],34:[694,-379,500,34,372],35:[694,194,833,56,777],36:[750,56,500,55,444],37:[750,56,833,56,776],38:[716,22,778,42,727],39:[694,-379,278,78,212],40:[750,250,389,94,333],41:[750,250,389,55,294],42:[750,-320,500,64,435],43:[583,82,778,56,722],44:[121,194,278,78,210],45:[252,-179,333,11,277],46:[120,0,278,78,199],47:[750,250,500,56,445],48:[666,22,500,39,460],49:[666,0,500,83,427],50:[666,0,500,50,449],51:[665,22,500,42,457],52:[677,0,500,28,471],53:[666,22,500,50,449],54:[666,22,500,42,456],55:[676,22,500,55,485],56:[666,22,500,43,457],57:[666,22,500,42,456],58:[430,0,278,78,199],59:[430,194,278,78,202],60:[540,40,778,83,694],61:[367,-133,778,56,722],62:[540,40,778,83,694],63:[705,-1,472,55,416],64:[705,11,778,56,722],65:[716,0,750,32,717],66:[683,0,708,28,651],67:[705,21,722,56,666],68:[683,0,764,27,708],69:[680,0,681,25,652],70:[680,0,653,25,610],71:[705,22,785,56,735],72:[683,0,750,25,724],73:[683,0,361,21,339],74:[683,22,514,25,465],75:[683,0,778,25,736],76:[683,0,625,25,582],77:[683,0,917,29,887],78:[683,0,750,25,724],79:[705,22,778,56,722],80:[683,0,681,27,624],81:[705,193,778,56,728],82:[683,22,736,27,732],83:[705,22,556,55,500],84:[677,0,722,36,685],85:[683,22,750,25,724],86:[683,22,750,19,730],87:[683,22,1028,18,1009],88:[683,0,750,23,726],89:[683,0,750,11,738],90:[683,0,611,55,560],91:[750,250,278,118,255],92:[750,250,500,56,444],93:[750,250,278,22,159],94:[694,-531,500,112,387],95:[-25,62,500,0,499],96:[699,-505,500,106,295],97:[448,11,500,34,493],98:[694,11,556,20,522],99:[448,11,444,34,415],100:[694,11,556,34,535],101:[448,11,444,28,415],102:[705,0,306,26,372],103:[453,206,500,29,485],104:[694,0,556,25,542],105:[669,0,278,26,255],106:[669,205,306,-55,218],107:[694,0,528,20,511],108:[694,0,278,26,263],109:[442,0,833,25,819],110:[442,0,556,25,542],111:[448,10,500,28,471],112:[442,194,556,20,522],113:[442,194,528,33,535],114:[442,0,392,20,364],115:[448,11,394,33,359],116:[615,10,389,18,333],117:[442,11,556,25,542],118:[431,11,528,19,508],119:[431,11,722,18,703],120:[431,0,528,11,516],121:[431,204,528,19,508],122:[431,0,444,28,401],123:[750,250,500,65,434],124:[750,249,278,119,159],125:[750,250,500,65,434],126:[318,-215,500,83,416],160:[0,0,250,0,0],168:[669,-554,500,95,404],172:[356,-89,667,56,611],175:[590,-544,500,69,430],176:[715,-542,500,147,352],177:[666,0,778,56,722],180:[699,-505,500,203,393],215:[491,-9,778,147,630],247:[537,36,778,56,721],305:[442,0,278,26,255],567:[442,205,306,-55,218],710:[694,-531,500,112,387],711:[644,-513,500,114,385],713:[590,-544,500,69,430],714:[699,-505,500,203,393],715:[699,-505,500,106,295],728:[694,-515,500,92,407],729:[669,-549,500,190,309],732:[668,-565,500,83,416],915:[680,0,625,25,582],916:[716,0,833,46,786],920:[705,22,778,56,722],923:[716,0,694,32,661],926:[677,0,667,42,624],928:[680,0,750,25,724],931:[683,0,722,55,666],933:[705,0,778,55,722],934:[683,0,722,56,665],936:[683,0,778,55,722],937:[704,0,722,44,677],8194:[0,0,500,0,0],8195:[0,0,999,0,0],8196:[0,0,333,0,0],8197:[0,0,250,0,0],8198:[0,0,167,0,0],8201:[0,0,167,0,0],8202:[0,0,83,0,0],8211:[285,-248,500,0,499],8212:[285,-248,1000,0,999],8216:[694,-379,278,64,198],8217:[694,-379,278,78,212],8220:[694,-379,500,128,466],8221:[694,-379,500,34,372],8224:[705,216,444,55,389],8225:[705,205,444,55,389],8230:[120,0,1172,78,1093],8242:[560,-43,275,30,262],8407:[714,-516,0,-471,-29],8463:[695,13,540,42,562],8465:[705,10,722,55,693],8467:[705,20,417,6,397],8472:[453,216,636,67,625],8476:[716,22,722,40,715],8501:[694,0,611,55,555],8592:[511,11,1000,55,944],8593:[694,193,500,17,483],8594:[511,11,1000,56,944],8595:[694,194,500,17,483],8596:[511,11,1000,55,944],8597:[772,272,500,17,483],8598:[720,195,1000,29,944],8599:[720,195,1000,55,970],8600:[695,220,1000,55,970],8601:[695,220,1000,29,944],8614:[511,11,1000,55,944],8617:[511,11,1126,55,1070],8618:[511,11,1126,55,1070],8636:[511,-230,1000,55,944],8637:[270,11,1000,55,944],8640:[511,-230,1000,56,944],8641:[270,11,1000,56,944],8652:[671,11,1000,55,944],8656:[525,24,1000,56,944],8657:[694,194,611,31,579],8658:[525,24,1000,56,944],8659:[694,194,611,31,579],8660:[526,25,1000,34,966],8661:[772,272,611,31,579],8704:[694,22,556,0,556],8706:[715,22,531,42,566],8707:[694,0,556,56,500],8709:[772,78,500,39,460],8711:[683,33,833,46,786],8712:[540,40,667,84,583],8713:[716,215,667,84,583],8715:[540,40,667,83,582],8722:[270,-230,778,84,694],8723:[500,166,778,56,722],8725:[750,250,500,56,445],8726:[750,250,500,56,444],8727:[465,-35,500,64,435],8728:[444,-55,500,55,444],8729:[444,-55,500,55,444],8730:[800,200,833,72,853],8733:[442,11,778,56,722],8734:[442,11,1000,55,944],8736:[694,0,722,55,666],8739:[750,249,278,119,159],8741:[750,250,500,132,367],8743:[598,22,667,55,611],8744:[598,22,667,55,611],8745:[598,22,667,55,611],8746:[598,22,667,55,611],8747:[716,216,417,55,472],8764:[367,-133,778,55,722],8768:[583,83,278,55,222],8771:[464,-36,778,55,722],8773:[589,-22,1000,55,722],8776:[483,-55,778,55,722],8781:[484,-16,778,55,722],8784:[670,-133,778,56,722],8800:[716,215,778,56,722],8801:[464,-36,778,56,722],8804:[636,138,778,83,694],8805:[636,138,778,83,694],8810:[568,67,1000,56,944],8811:[567,67,1000,55,944],8826:[539,41,778,84,694],8827:[539,41,778,83,694],8834:[540,40,778,84,694],8835:[540,40,778,83,693],8838:[636,138,778,84,694],8839:[636,138,778,83,693],8846:[598,22,667,55,611],8849:[636,138,778,84,714],8850:[636,138,778,64,694],8851:[598,0,667,61,605],8852:[598,0,667,61,605],8853:[583,83,778,56,722],8854:[583,83,778,56,722],8855:[583,83,778,56,722],8856:[583,83,778,56,722],8857:[583,83,778,56,722],8866:[694,0,611,55,555],8867:[694,0,611,55,555],8868:[668,0,778,55,723],8869:[668,0,778,55,723],8872:[750,249,867,119,811],8900:[488,-12,500,12,488],8901:[310,-190,278,78,199],8902:[486,-16,500,3,497],8904:[505,5,900,26,873],8942:[900,30,278,78,199],8943:[310,-190,1172,78,1093],8945:[820,-100,1282,133,1148],8968:[750,250,444,174,422],8969:[750,250,444,21,269],8970:[750,250,444,174,422],8971:[750,250,444,21,269],8994:[388,-122,1000,55,944],8995:[378,-134,1000,55,944],9136:[744,244,412,55,357],9137:[744,244,412,56,357],10216:[750,250,389,110,333],10217:[750,250,389,55,278],10222:[744,244,412,173,357],10223:[744,244,412,56,240],10229:[511,11,1609,55,1525],10230:[511,11,1638,84,1553],10231:[511,11,1859,55,1803],10232:[525,24,1609,56,1553],10233:[525,24,1638,56,1582],10234:[525,24,1858,56,1802],10236:[511,11,1638,55,1553],10815:[683,0,750,28,721],10927:[636,138,778,84,694],10928:[636,138,778,83,694]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"MathJax_Main"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Main/Regular/Main.js"]);
