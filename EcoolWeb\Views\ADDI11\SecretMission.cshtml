﻿@model List<ADDI11SecretMissionListViewModel>

@{

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);

}
@if (AppMode == false)
{
    @Html.Partial("_Title_Secondary")
}
@{    Html.RenderAction("_RunMenu", new { NowAction = "SecretMission" });}

@Html.Partial("_Notice")

<b>說明:</b><br /><font>
    1.每10公里可以執行一次神秘任務。<br />
    2.執行神秘任務後可以獲得30點酷幣。
</font>

<img src="~/Content/img/web-bar-Run.png" class="img-responsive" />
<div class="table-responsive">
    <div class="text-center" id="tbData">
        <table class="table-ecool table-92Per table-hover table-ecool-reader">
            <thead>
                <tr>
                    <th>@Html.DisplayNameFor(m => m.First().TASK_DESC)</th>
                    <th>@Html.DisplayNameFor(m => m.First().RUN_TOTAL_KM)</th>
                    <th></th>
                    <th></th>
                </tr>
            </thead>
            <tbody>
                @foreach (var item in Model)
                {

                    <tr>
                        <td style="text-align: left">@Html.DisplayFor(modelItem => item.TASK_DESC)</td>
                        <td style="text-align: center">@Html.DisplayFor(modelItem => item.RUN_TOTAL_KM)</td>
                        <td>
                            @if (item.IsExecutedTask)
                            {
                                <a role="button" href='@Url.Action("SecretMissionEdit",(string)ViewBag.BRE_NO,new {SOU_SCHOOL_NO=item.SCHOOL_NO,SOU_ITEM_NO=item.ITEM_NO })' class="btn btn-xs btn-Basic">
                                    修改
                                </a>
                            }
                        </td>
                        <td>
                            @if (item.IsExecutedTask)
                            {

                                <b>已完成</b>
                            }
                            else
                            {
                                if (item.IsTask)
                                {
                                    <a role="button" href='@Url.Action("SecretMissionEdit",(string)ViewBag.BRE_NO,new {SOU_SCHOOL_NO=item.SCHOOL_NO,SOU_ITEM_NO=item.ITEM_NO })' class="btn btn-xs btn-Basic">
                                        執行任務
                                    </a>
                                }
                                else
                                {
                                    <b>里程不足</b>
                                }
                            }
                        </td>
                    </tr>
                }
            </tbody>
        </table>
    </div>
</div>