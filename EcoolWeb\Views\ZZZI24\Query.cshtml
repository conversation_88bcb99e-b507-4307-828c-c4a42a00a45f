﻿@model EcoolWeb.ViewModels.HRMT01QueryViewModel
@using ECOOL_APP.com.ecool.util
@{
    ViewBag.Title = "老師清單維護";
    int i = 0;
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

    int UseCashPageTotal = 0;
    ECOOL_DEVEntities db = null;
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>

@using (Html.BeginForm("QUERY", "ZZZI24", FormMethod.Post, new { id = "ZZZI24", name = "ZZZI24" }))
{

    @Html.Hidden("hidSelectTRANS_NO")
    @Html.Hidden("hidListCount")

    <br />
    <div class="form-inline" role="form" id="Q_Div">
        <div class="form-group">
            <label class="control-label">帳號/姓名</label>
        </div>
        <div class="form-group">
            @Html.EditorFor(m => m.whereKeyword, new { htmlAttributes = new { @class = "form-control  input-sm" } })
            @Html.HiddenFor(m => m.OrdercColumn)
            @Html.HiddenFor(m => m.whereUserNo)

            @Html.HiddenFor(m => m.Page)

            <input type="hidden" id="whereStatus" name="whereStatus" value="@Model.whereStatus" />
        </div>
        <div class="form-group">
            <label class="control-label">狀態</label>
        </div>
        <div class="form-group">
            @Html.DropDownList("ddlStatus", (IEnumerable<SelectListItem>)ViewBag.StatusItem, new { @class = "form-control input-sm", @onchange = "FunPageProc(1)" })
        </div>

        <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1)" />
        <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />
        <br />
        <div class="row">
            <div class="col-xs-12 text-right">
                顯示 @Html.DropDownList("ddlPageCount", (IEnumerable<SelectListItem>)ViewBag.PageCount, new { onchange = "btnSearch_onclick();" }) 筆
            </div>
        </div>
    </div>

    <div style="height:5px"></div>
    <div class="panel panel-ACC">
        <div class="panel-heading text-center">
            @Html.BarTitle()
        </div>
        <div class="table-responsive">
            <table class="table-ecool table-92Per table-hover table-ecool-ACC">
                <thead>
                    <tr>
                        <th style="text-align: center;">
                            選擇
                            <input type="checkbox" id="chkALL" name="chkALL" />
                        </th>
                        <th style="text-align: center">
                            帳號
                        </th>
                        <th style="text-align: center">
                            帶班
                        </th>
                        <th style="text-align: center">
                            姓名
                        </th>
                        <th style="text-align: center">
                            狀態
                        </th>
                        <th style="text-align: center">
                            本月給點總數
                        </th>
                        <th style="text-align: center">
                            每月特殊給點上限
                        </th>
                        <th style="text-align: center">
                            密碼異動
                        </th>
                    </tr>
                </thead>
                <div class="form-group">
                    @Html.LabelFor(model => model.CHECK_CASH_LIMIT, htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-9">
                        @Html.CheckBox("CHECK_CASH_LIMIT", Model.CHECK_CASH_LIMIT ?? false, new { @class = "", value = true, onchange = "SaveCHECK_CASH_LIMIT(this);" })
                    </div>
                </div>
                <tbody>
                    @foreach (var item in Model.HRMT01List)
                    {

                        <tr align="center">
                            <td>
                                <input type="checkbox" id='[@i].chkTRANS_NO' name='[@i].chkTRANS_NO'>
                                <input type="hidden" id='[@i].SCHOOL_NO' name='[@i].SCHOOL_NO' value=@item.SCHOOL_NO />
                                <input type="hidden" id='[@i].USER_NO' name='[@i].USER_NO' value=@item.USER_NO />
                            </td>
                            <td>
                                @if (user.RoleID_Default == HRMT24_ENUM.SuperAdminROLE)
                                {
                                    @Html.DisplayFor(modelItem => item.USER_NO)
                                }
                                else
                                {
                                    @StringHelper.LeftStringR(item.USER_NO, 5, "*****")
                                }
                            </td>
                            <td>
                                @Html.DisplayFor(modelItem => item.CLASS_NO)
                            </td>
                            <td>
                                @Html.DisplayFor(modelItem => item.NAME)
                            </td>
                            <td>
                                @UserStaus.GetDesc(item.USER_STATUS)
                            </td>
                            <td>
                                @{  string CashErrMsg;
                                    short ThisCashCash;
                                    ThisCashCash= ECOOL_APP.UserProfile.GetUseCashLimit(item.SCHOOL_NO, item.USER_NO, item.USER_TYPE, ref db, out ThisCashCash, out CashErrMsg);
                                }
                               
                                @ThisCashCash
                            </td>
                            <td>
                                @{
                                    string LimitShow = "未設定";

                                    string ErrMsg;
                                    short ThisMonthCash;
                                    if (ViewBag.DefaultCashLimit != null)
                                    {

                                        int CashLimit = ECOOL_APP.UserProfile.GetCashLimit(item.SCHOOL_NO, item.USER_NO, item.USER_TYPE, ref db, out ThisMonthCash, out ErrMsg);
                                        LimitShow = ThisMonthCash.ToString() + " / " + CashLimit;

                                    }
                                }
                                <button id="CashLimit_@i" class="btn btn-xs btn-Basic" type="button"  style="color:#f0ad4e;"
                                        href="@Url.Action("CashLimit", "ZZZI24", new { STATUS = "EDIT", SCHOOL_NO = item.SCHOOL_NO, USER_NO = item.USER_NO })">
                                    @LimitShow
                                </button>
                            </td>
                            <td>
                                <button id="ResetPassword_@i" class="btn btn-xs btn-Basic" type="button" href="@Url.Action("ResetPassword", "ZZT08", new { STATUS = "EDIT", SCHOOL_NO = item.SCHOOL_NO, USER_NO = item.USER_NO })">重設密碼</button>
                            </td>
                        </tr>
                        i++;
                        UseCashPageTotal += item.USE_CASH;
                    }
                    <tr>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td style="border-top:2px solid #d56666">
                            <div class="text-danger">
                                本頁總計發放: @UseCashPageTotal 酷幣　　
                            </div>
                        </td>
                        <td></td>
                        <td></td>
                    </tr>
                </tbody>
            </table>
        </div>
        <div class="panel-footer text-center">
            <input type="button" id="btnStartUser" value="啟用" class="btn btn-default" onclick="btnStartUser_onclick();" />
            <input type="button" id="btnUnLockUser" value="解鎖定" class="btn btn-default" onclick="btnStartUser_onclick();" />
            <input type="button" id="btnCancelUser" value="停用" class="btn btn-default" onclick="btnCancelUser_onclick();" />
            <label style="font-style:normal; font-size:14px;position:relative;top:-5px;">批次設定給點上限：</label>
            @Html.Editor("HowMuch", new { htmlAttributes = new { @class = "", style = "width:80px;height:32px;position:relative;top:-5px;" } })
            <input type="button" id="btnCashLimit" value="設定" class="btn btn-default" onclick="btnCashLimit_onclick();" style="" />
        </div>
    </div>
    <div>
        @Html.Pager(Model.HRMT01List.PageSize, Model.HRMT01List.PageNumber, Model.HRMT01List.TotalItemCount).Options(o => o
        .DisplayTemplate("BootstrapPagination")
             .MaxNrOfPages(5)
             .SetPreviousPageText("上頁")
             .SetNextPageText("下頁")
    )
    </div>
    <div class="form-group">
        <label class="text-danger">
            給點上限說明：
        </label><br />
        <label class="text-danger">
            1.橘色的數字，代表每個月控管的點數，對應即時加點、批次加扣點的橘色按鈕。
        </label><br />
                <label class="text-danger">
                    2.例如：

                </label><br />
        <label class="text-danger">
            (1)25/300意思：這個月可以發放控管的300點，已經發放25點。

        </label><br />
                <label class="text-danger">
                    (2)88/0意思：這個月可以發放「無限制點」，已經發放88點。(0代表無限制)會被點數限制：批次加扣<br />
                    &nbsp;&nbsp;&nbsp;、即時加點的
                </label><br />
                        <label class="text-danger">
                            (3)橘色按鈕，線上投稿、閱讀認證、藝廊、校內外表現等獲得的點數都不在控管的範圍。如有疑<br />
                            &nbsp;&nbsp;&nbsp;&nbsp;問，<a   class="text-danger" href="https://youtu.be/CoNHkV9Z-Pw">請看影片說明。</a>
                        </label>
    </div>
}

@section scripts{
    <script>
        var targetFormID = '#ZZZI24';

        $(document).ready(function () {
            $("[id^='ResetPassword']").colorbox({ iframe: true, width: '50%', height: '50%', opacity: 0.82 });
            $("[id^='CashLimit']").colorbox({ iframe: true, width: '50%', height: '50%', opacity: 0.82 });
            $("#hidListCount").val($('#ddlPageCount option:selected').val());
        });

        function btnCancelUser_onclick(TRANS_NO) {

            if ($("input:checkbox:checked").length == 0) {
                alert("請至少勾選一位");
                return;
            }

            $("#hidSelectTRANS_NO").val(TRANS_NO);
            document.ZZZI24.enctype = "multipart/form-data";
            document.ZZZI24.action = "CancelUser";
            document.ZZZI24.submit();
        }
        function SaveCHECK_CASH_LIMIT(obj)
        {


            
            var yes = confirm("確認設定啟用老師給點上限");
            if (yes == true) {
                var CashLimitVal = "";
                var strMsg = "";
                CashLimitVal = $("#CHECK_CASH_LIMIT").is(":checked");
                console.log("CashLimitVal" + CashLimitVal);
                $.ajax({
                    url: '@Url.Action("SetCashLimitVal")',
                    data: { CashLimitVal: CashLimitVal },
                    type: 'post',
                    async: false,
                    cache: false,
                    dataType: 'json',
                    success: function (result) {
                        if (result == true) {
                            strMsg += '\r\n';
                        }
                    }
                });
            }
            else {
                var CHECK_CASH_LIMITtemp = $("#CHECK_CASH_LIMIT").is(":checked");
                if (CHECK_CASH_LIMITtemp == false) {
                    document.getElementById("CHECK_CASH_LIMIT").checked = true;

                }
                if (CHECK_CASH_LIMITtemp == true) {
                    document.getElementById("CHECK_CASH_LIMIT").checked = false;

                }
            }
        }
        function btnStartUser_onclick(TRANS_NO) {

            if ($("input:checkbox:checked").length == 0) {
                alert("請至少勾選一位");
                return;
            }

            $("#hidSelectTRANS_NO").val(TRANS_NO);
            document.ZZZI24.enctype = "multipart/form-data";
            document.ZZZI24.action = "StartUser";
            document.ZZZI24.submit();
        }

        function btnCashLimit_onclick(TRANS_NO) {

            if ($("input:checkbox:checked").length == 0) {
                alert("請至少勾選一位");
                return;
            }

            $("#hidSelectTRANS_NO").val(TRANS_NO);
            document.ZZZI24.enctype = "multipart/form-data";
            document.ZZZI24.action = "CashLimitMany";
            document.ZZZI24.submit();
        }

        function btnSearch_onclick() {
            $("#hidListCount").val($('#ddlPageCount option:selected').val());
            document.ZZZI24.enctype = "multipart/form-data";
            document.ZZZI24.action = "Query";
            document.ZZZI24.submit();
        }

        $("#chkALL").click(function () {

            if ($("#chkALL").prop("checked")) {
                $("input:checkbox").each(function () {
                    $(this).prop("checked", true);
                });
            }
            else {
                $("input:checkbox").each(function () {
                    $(this).prop("checked", false);
                });
            }
        });

        function FunPageProc(page) {
            if ($(targetFormID).size() > 0) {
                $('#Page').val(page)
                $(targetFormID).submit();
            }
        };

        function doSort(SortCol) {
            $("#OrdercColumn").val(SortCol);
            FunPageProc(1)
        }
        function doSearch(ColName, whereValue) {
            $("#" + ColName).val(whereValue);
            FunPageProc(1)
        }

        function todoClear() {
            ////重設

            $('#Q_Div').find(":input,:selected").each(function (i) {

                var type = $(this).attr('type')
                var InPreadonly = $(this).attr('readonly')
                var tag = this.tagName.toLowerCase(); // normalize case

                if (InPreadonly == false || InPreadonly == undefined) {

                    if (type == 'radio' || type == 'checkbox') {
                        if ($(this).attr("title") == 'Default') {
                            this.checked = true;
                        }
                        else {
                            this.checked = false;
                        }
                    }
                    else if (tag == 'select') { //下拉式選單
                        this.selectedIndex = 0;
                    }
                    else if (type == 'text' || type == 'hidden' || type == 'password' || type == 'textarea') {
                        this.value = '';
                    }
                }
            });

            FunPageProc(1)
        }
    </script>
}