﻿@model ECOOL_APP.EF.ADDT01
@using global::ECOOL_APP.com.ecool.util

<style>
    .card {
        box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2);
        border: 1px solid #b7b7b7;
        transition: 0.3s;
        background-color: white;
        padding: 2%;
        margin: 2%;
    }

        .card:hover {
            box-shadow: 0 8px 16px 0 rgba(0,0,0,0.2);
        }

        .card input {
            border: 1px solid #e9e9e9;
            border-radius: 0;
        }
</style>
<div class="card">
    <label style="color:deepskyblue">
        <span class="glyphicon glyphicon-envelope" aria-hidden="true"></span>
        Mail Information
    </label>
    @Html.HiddenFor(model => model.MdnKids.Name)
    @Html.HiddenFor(model => model.MdnKids.SchoolName)
    @Html.HiddenFor(model => model.MdnKids.Grade)
    @Html.HiddenFor(model => model.MdnKids.Class_No)
    @Html.HiddenFor(model => model.MdnKids.ArticleTitle)
    @{
        var checkBoxsSENDlist = new List<CheckBoxListInfo>();
        CheckBoxListInfo cheboxSEND = new CheckBoxListInfo();
        cheboxSEND.DisplayText = "是 <br />";
        cheboxSEND.Value = "Y";
        cheboxSEND.IsChecked = Model.SEND_EMAIL_YN == "Y" ? true : false;
        checkBoxsSENDlist.Add(cheboxSEND);
        var htmlAttributeSEND = new Dictionary<string, object>();
        htmlAttributeSEND.Add("id", "SEND_EMAIL_YN");
    }
    <div class="form-group" style="">
        @Html.LabelFor(model => model.SEND_EMAIL_YN, htmlAttributes: new { @class = "control-label col-md-2 col-sm-3" })

        <div class="col-md-9 col-sm-6" style="padding-top:7px">
            @Html.CheckBoxList("SEND_EMAIL_YN", (List<CheckBoxListInfo>)checkBoxsSENDlist, htmlAttributeSEND, 1)
        </div>
    </div>
    <div class="form-group">
        @Html.LabelFor(model => model.MdnKids.Name, htmlAttributes: new { @class = "control-label col-md-2 col-sm-3" })
        <div class="col-md-9 col-sm-6" style="padding-top:7px">
            @Model.MdnKids.Name
        </div>
    </div>
    <div class="form-group">
        @Html.LabelFor(model => model.MdnKids.SchoolName, htmlAttributes: new { @class = "control-label col-md-2 col-sm-3" })
        <div class="col-md-9 col-sm-6" style="padding-top:7px">
            @Model.MdnKids.SchoolName
        </div>
    </div>
    <div class="form-group">
        @Html.LabelFor(model => model.MdnKids.Grade, htmlAttributes: new { @class = "control-label col-md-2 col-sm-3" })
        <div class="col-md-9 col-sm-6" style="padding-top:7px">
            @Model.MdnKids.Grade
        </div>
    </div>
    <div class="form-group">
        @Html.LabelFor(model => model.MdnKids.Class_No, htmlAttributes: new { @class = "control-label col-md-2 col-sm-3" })
        <div class="col-md-9 col-sm-6" style="padding-top:7px">
            @Model.MdnKids.Class_No
        </div>
    </div>
    <div class="form-group">
        @Html.LabelFor(model => model.MdnKids.IDNO, htmlAttributes: new { @class = "control-label col-md-2 col-sm-3" })
        <div class="col-md-9 col-sm-6" style="padding-top:7px">
            @Html.HiddenFor(m => m.MdnKids.IDNO)
            @Html.Editor("inputIDNO", new { htmlAttributes = new { @class = "form-control", maxlength = "10" } })
            @Html.ValidationMessageFor(m => m.MdnKids.IDNO, "", new { @class = "text-danger" })
        </div>
    </div>
    <div class="form-group">
        @Html.LabelFor(model => model.MdnKids.Address, htmlAttributes: new { @class = "control-label col-md-2 col-sm-3" })
        <div class="col-md-9 col-sm-6" style="padding-top:7px">
            @Html.EditorFor(m => m.MdnKids.Address, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(m => m.MdnKids.Address, "", new { @class = "text-danger" })
        </div>
    </div>
    <div class="form-group">
        @Html.LabelFor(model => model.MdnKids.ResidenceAddress, htmlAttributes: new { @class = "control-label col-md-2 col-sm-3" })
        <div class="col-md-9 col-sm-6" style="padding-top:7px">
            @Html.EditorFor(m => m.MdnKids.ResidenceAddress, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(m => m.MdnKids.ResidenceAddress, "", new { @class = "text-danger" })
        </div>
    </div>
    <div class="form-group">
        @Html.LabelFor(model => model.MdnKids.Phone, htmlAttributes: new { @class = "control-label col-md-2 col-sm-3" })
        <div class="col-md-9 col-sm-6" style="padding-top:7px">
            @Html.EditorFor(m => m.MdnKids.Phone, new { htmlAttributes = new { @class = "form-control", maxlength = "10" } })
            @Html.ValidationMessageFor(m => m.MdnKids.Phone, "", new { @class = "text-danger" })
        </div>
    </div>
    <div class="form-group">
        @Html.LabelFor(model => model.MdnKids.Email, htmlAttributes: new { @class = "control-label col-md-2 col-sm-3" })
        <div class="col-md-9 col-sm-6" style="padding-top:7px">
            @Html.EditorFor(m => m.MdnKids.Email, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(m => m.MdnKids.Email, "", new { @class = "text-danger" })
        </div>
    </div>

    <div class="form-group" style="@(cheboxSEND.IsChecked? "":"display:none;")" id="sendMailShow">
        @Html.LabelFor(model => model.MdnKids.Email2, htmlAttributes: new { @class = "control-label col-md-2 col-sm-3" })
        <div class="col-md-9 col-sm-6" style="padding-top:7px">
            @Html.EditorFor(m => m.MdnKids.Email2, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(m => m.MdnKids.Email2, "", new { @class = "text-danger" })
        </div>
    </div>

    <div class="form-group">
        @Html.LabelFor(model => model.MdnKids.IntroPerson, htmlAttributes: new { @class = "control-label col-md-2 col-sm-3" })
        <div class="col-md-9 col-sm-6" style="padding-top:7px">
            @Html.EditorFor(m => m.MdnKids.IntroPerson, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(m => m.MdnKids.IntroPerson, "", new { @class = "text-danger" })
        </div>
    </div>
    @if (Model.WRITING_STATUS == (int)ADDStatus.eADDT01Status.Verified)
    {<br />
        <span>※備註: 輸入個資後, 請填異動原因(投稿國語日報), 再按異動批閱內容的按鈕</span>
    }
</div>

@section Scripts {
    <script src="~/Scripts/ADDI01/mdnKidsModal.js" nonce="cmlvaw"></script>
}