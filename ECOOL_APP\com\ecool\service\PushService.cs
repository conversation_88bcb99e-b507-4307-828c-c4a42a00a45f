﻿using com.ecool.sqlConnection;
using ECOOL_APP.com.ecool.Models.DTO;
using ECOOL_APP.com.ecool.util;
using ECOOL_APP.EF;
using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using System.Data.SqlClient;
using System.Data;
using EntityFramework.Extensions;
using Dapper;
using log4net;
using System.Transactions;

namespace ECOOL_APP
{
    public class PushService
    {
        private static ILog logger = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        /// <summary>
        /// 產生批號
        /// </summary>
        /// <returns></returns>
        public static string CreBATCH_ID()
        {
            return DateTime.Now.ToString("yyyyMMddHHmmssfff") + Guid.NewGuid().ToString();
        }

        /// <summary>
        /// 產生Push SCHOOL_NO+USER_NO 本人 資料
        /// </summary>
        /// <param name="BATCH_ID">批號</param>
        /// <param name="SCHOOL_NO">學校</param>
        /// <param name="USER_NO">帳號</param>
        /// <param name="TITLE_TXT">標題</param>
        /// <param name="BODY_TXT">內容</param>
        /// <param name="CRE_PERSON">增加人(處錯)</param>
        /// <param name="REF_SOU_BRE_NO">來源程式(處錯)</param>
        /// <param name="REF_SOU_ITEM">來源細項(處錯)</param>
        /// <param name="REF_SOU_KEY">來源單號(處錯)</param>
        /// <param name="TO_PATH">通知點入連至的網頁</param>
        /// <param name="RightAway">是否同時Push</param>
        /// <param name="db">Entitie</param>
        /// <param name="T02List">批次新增用</param>
        public static void InsertPushDataMe(string BATCH_ID, string SCHOOL_NO, string USER_NO, string TITLE_TXT, string BODY_TXT
            , string CRE_PERSON, string REF_SOU_BRE_NO, string REF_SOU_ITEM, string REF_SOU_KEY, string TO_PATH, bool RightAway, ref ECOOL_DEVEntities db
            , List<APPT02> T02List = null)
        {
            InsertPushData(BATCH_ID, SCHOOL_NO, USER_NO, TITLE_TXT, BODY_TXT, CRE_PERSON, REF_SOU_BRE_NO, REF_SOU_ITEM, REF_SOU_KEY, TO_PATH, RightAway, ref db
                , false //家長
                , false //好友
                , true  //自已
                , false //班導
                );
        }

        /// <summary>
        /// 產生Push 家長 資料
        /// </summary>
        /// <param name="BATCH_ID">批號</param>
        /// <param name="SCHOOL_NO">學校</param>
        /// <param name="USER_NO">帳號</param>
        /// <param name="TITLE_TXT">標題</param>
        /// <param name="BODY_TXT">內容</param>
        /// <param name="CRE_PERSON">增加人(處錯)</param>
        /// <param name="REF_SOU_BRE_NO">來源程式(處錯)</param>
        /// <param name="REF_SOU_ITEM">來源細項(處錯)</param>
        /// <param name="REF_SOU_KEY">來源單號(處錯)</param>
        /// <param name="TO_PATH">通知點入連至的網頁</param>
        /// <param name="RightAway">是否同時Push</param>
        /// <param name="db">Entitie</param>
        /// <param name="T02List">批次新增用</param>
        public static void InsertPushDataParents(string BATCH_ID, string SCHOOL_NO, string USER_NO, string TITLE_TXT, string BODY_TXT
            , string CRE_PERSON, string REF_SOU_BRE_NO, string REF_SOU_ITEM, string REF_SOU_KEY, string TO_PATH, bool RightAway, ref ECOOL_DEVEntities db
            , List<APPT02> T02List = null)
        {
            InsertPushData(BATCH_ID, SCHOOL_NO, USER_NO, TITLE_TXT, BODY_TXT, CRE_PERSON, REF_SOU_BRE_NO, REF_SOU_ITEM, REF_SOU_KEY, TO_PATH, RightAway, ref db
                , true //家長
                , false //好友
                , false  //自已
                , false //班導
                );
        }

        /// <summary>
        /// 產生Push 好友 資料
        /// </summary>
        /// <param name="BATCH_ID">批號</param>
        /// <param name="SCHOOL_NO">學校</param>
        /// <param name="USER_NO">帳號</param>
        /// <param name="TITLE_TXT">標題</param>
        /// <param name="BODY_TXT">內容</param>
        /// <param name="CRE_PERSON">增加人(處錯)</param>
        /// <param name="REF_SOU_BRE_NO">來源程式(處錯)</param>
        /// <param name="REF_SOU_ITEM">來源細項(處錯)</param>
        /// <param name="REF_SOU_KEY">來源單號(處錯)</param>
        /// <param name="TO_PATH">通知點入連至的網頁</param>
        /// <param name="RightAway">是否同時Push</param>
        /// <param name="db">Entitie</param>
        /// <param name="T02List">批次新增用</param>
        public static void InsertPushDatafriend(string BATCH_ID, string SCHOOL_NO, string USER_NO, string TITLE_TXT, string BODY_TXT
            , string CRE_PERSON, string REF_SOU_BRE_NO, string REF_SOU_ITEM, string REF_SOU_KEY, string TO_PATH, bool RightAway, ref ECOOL_DEVEntities db
            , List<APPT02> T02List = null)
        {
            InsertPushData(BATCH_ID, SCHOOL_NO, USER_NO, TITLE_TXT, BODY_TXT, CRE_PERSON, REF_SOU_BRE_NO, REF_SOU_ITEM, REF_SOU_KEY, TO_PATH, RightAway, ref db
                , false //家長
                , true //好友
                , false  //自已
                , false //班導
                );
        }

        /// <summary>
        /// 產生Push 班導 資料
        /// </summary>
        /// <param name="BATCH_ID">批號</param>
        /// <param name="SCHOOL_NO">學校</param>
        /// <param name="USER_NO">帳號</param>
        /// <param name="TITLE_TXT">標題</param>
        /// <param name="BODY_TXT">內容</param>
        /// <param name="CRE_PERSON">增加人(處錯)</param>
        /// <param name="REF_SOU_BRE_NO">來源程式(處錯)</param>
        /// <param name="REF_SOU_ITEM">來源細項(處錯)</param>
        /// <param name="REF_SOU_KEY">來源單號(處錯)</param>
        /// <param name="TO_PATH">通知點入連至的網頁</param>
        /// <param name="RightAway">是否同時Push</param>
        /// <param name="db">Entitie</param>
        /// <param name="T02List">批次新增用</param>
        public static void InsertPushDataTEACHER(string BATCH_ID, string SCHOOL_NO, string USER_NO, string TITLE_TXT, string BODY_TXT
            , string CRE_PERSON, string REF_SOU_BRE_NO, string REF_SOU_ITEM, string REF_SOU_KEY, string TO_PATH, bool RightAway, ref ECOOL_DEVEntities db
            , List<APPT02> T02List = null)
        {
            InsertPushData(BATCH_ID, SCHOOL_NO, USER_NO, TITLE_TXT, BODY_TXT, CRE_PERSON, REF_SOU_BRE_NO, REF_SOU_ITEM, REF_SOU_KEY, TO_PATH, RightAway, ref db
                , false //家長
                , false //好友
                , false  //自已
                , true //班導
                );
        }

        /// <summary>
        /// 增加要 Push 資料(APPT02)(清單) TRIGGER 產生APPT02_WAIT_PUSH ，TRIGGER 會判斷是否「開通通知」，才會塞進APPT02_WAIT_PUSH
        /// </summary>
        /// <param name="BATCH_ID">批號</param>
        /// <param name="SCHOOL_NO">學校</param>
        /// <param name="USER_NO">帳號</param>
        /// <param name="TITLE_TXT">標題</param>
        /// <param name="BODY_TXT">內容</param>
        /// <param name="CRE_PERSON">增加人(處錯)</param>
        /// <param name="REF_SOU_BRE_NO">來源程式(處錯)</param>
        /// <param name="REF_SOU_ITEM">來源細項(處錯)</param>
        /// <param name="REF_SOU_KEY">來源單號(處錯)</param>
        /// <param name="TO_PATH">通知點入連至的網頁</param>
        /// <param name="RightAway">是否同時Push</param>
        /// <param name="db">Entitie</param>
        /// <param name="AddParents">是否同時Push家長，預設 false</param>
        /// <param name="Addfriend">是否同時Push好友，預設 false</param>
        /// <param name="addMe">是否通知傳入的SCHOOL_NO+USER_NO 本人，預設 true</param>
        /// <param name="addTEACHER">是否通知班導，預設 false</param>
        /// <param name="T02List">批次新增用</param>
        public static void InsertPushData(string BATCH_ID, string SCHOOL_NO, string USER_NO, string TITLE_TXT, string BODY_TXT
            , string CRE_PERSON, string REF_SOU_BRE_NO, string REF_SOU_ITEM, string REF_SOU_KEY, string TO_PATH, bool RightAway, ref ECOOL_DEVEntities db
            , bool AddParents = false, bool Addfriend = false, bool addMe = true, bool addTEACHER = false
            , List<APPT02> T02List = null)
        {
            HRMT01 T01 = db.HRMT01.Where(a => a.SCHOOL_NO == SCHOOL_NO && a.USER_NO == USER_NO).FirstOrDefault();

            if (T01 != null)
            {
                //傳入SCHOOL_NO+USER_NO 本人
                if (addMe)
                {
                    string MeBODY_TXT = T01.SNAME + "，" + BODY_TXT;

                    InsertPushData(BATCH_ID, SCHOOL_NO, USER_NO, TITLE_TXT, MeBODY_TXT, CRE_PERSON, REF_SOU_BRE_NO, REF_SOU_ITEM, REF_SOU_KEY, TO_PATH, false, ref db, T02List);
                }

                //班導
                if (addTEACHER)
                {
                    var Ht03List = db.HRMT03.Where(a => a.SCHOOL_NO == SCHOOL_NO && a.CLASS_NO == T01.CLASS_NO).ToList();

                    string TEACHERBODY_TXT = "你的學生 " + T01.SNAME + "，" + BODY_TXT;

                    if (Ht03List.Count > 0)
                    {
                        foreach (var item in Ht03List)
                        {
                            InsertPushData(BATCH_ID, item.SCHOOL_NO, item.TEACHER_NO, TITLE_TXT, TEACHERBODY_TXT, CRE_PERSON, REF_SOU_BRE_NO, REF_SOU_ITEM, REF_SOU_KEY, TO_PATH, false, ref db, T02List);
                        }
                    }
                }

                //家長
                if (AddParents)
                {
                    var T06List = db.HRMT06.Where(a => a.SCHOOL_NO == SCHOOL_NO && a.STUDENT_USER_NO == USER_NO).ToList();

                    string ParentsBODY_TXT = "你的寶貝 " + T01.SNAME + " ," + BODY_TXT;

                    if (T06List.Count > 0)
                    {
                        foreach (var item in T06List)
                        {
                            InsertPushData(BATCH_ID, item.SCHOOL_NO, item.PARENTS_USER_NO, TITLE_TXT, ParentsBODY_TXT, CRE_PERSON, REF_SOU_BRE_NO, REF_SOU_ITEM, REF_SOU_KEY, TO_PATH, false, ref db, T02List);
                        }
                    }
                }

                //朋友
                if (Addfriend)
                {
                    var T07List = db.HRMT07.Where(a => a.SCHOOL_NO == SCHOOL_NO && a.STUDENT_USER_NO == USER_NO).ToList();

                    string friendBODY_TXT = "你的朋友 " + T01.SNAME + "," + BODY_TXT;

                    if (T07List.Count > 0)
                    {
                        foreach (var item in T07List)
                        {
                            InsertPushData(BATCH_ID, item.SCHOOL_NO, item.USER_NO, TITLE_TXT, friendBODY_TXT, CRE_PERSON, REF_SOU_BRE_NO, REF_SOU_ITEM, REF_SOU_KEY, TO_PATH, false, ref db, T02List);
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 增加要 Push 資料(APPT02)(清單) TRIGGER 產生APPT02_WAIT_PUSH ，TRIGGER 會判斷是否「開通通知」，才會塞進APPT02_WAIT_PUSH
        /// </summary>
        /// <param name="BATCH_ID">批號</param>
        /// <param name="SCHOOL_NO">要通知的SCHOOL_NO</param>
        /// <param name="USER_NO">要通知的USER_NO</param>
        /// <param name="TITLE_TXT">標題</param>
        /// <param name="BODY_TXT">內容</param>
        /// <param name="CRE_PERSON">增加人(處錯)</param>
        /// <param name="REF_SOU_BRE_NO">來源程式(處錯)</param>
        /// <param name="REF_SOU_ITEM">來源細項(處錯)</param>
        /// <param name="REF_SOU_KEY">來源單號(處錯)</param>
        /// <param name="TO_PATH">通知點入連至的網頁</param>
        /// <param name="RightAway">是否同時Push</param>
        /// <param name="db">Entitie</param>
        /// <param name="T02List">批次新增用</param>
        public static void InsertPushData(string BATCH_ID, string SCHOOL_NO, string USER_NO
            , string TITLE_TXT, string BODY_TXT, string CRE_PERSON, string REF_SOU_BRE_NO, string REF_SOU_ITEM, string REF_SOU_KEY, string TO_PATH
            , bool RightAway, ref ECOOL_DEVEntities db, List<APPT02> T02List)
        {
            PushSaveModel Save = new PushSaveModel();
            Save.BATCH_ID = BATCH_ID;
            Save.SCHOOL_NO = SCHOOL_NO;
            Save.USER_NO = USER_NO;
            Save.TITLE_TXT = TITLE_TXT;
            Save.BODY_TXT = BODY_TXT;

            Save.CRE_PERSON = CRE_PERSON;
            Save.REF_SOU_BRE_NO = REF_SOU_BRE_NO;
            Save.REF_SOU_ITEM = REF_SOU_ITEM;
            Save.REF_SOU_KEY = REF_SOU_KEY;

            Save.TO_PATH = TO_PATH;

            if (RightAway && T02List == null)
            {
                InsertPushData(Save);
            }
            else
            {
                InsertPushData(Save, ref db, T02List);
            }
        }

        /// <summary>
        /// APPT03_Q (角色) 塞進 APPT03 (每個人)
        /// </summary>
        /// <param name="REF_TABLE"></param>
        /// <param name="REF_KEY">原來key</param>
        /// <param name="UpREF_KEY">新的key</param>
        /// <param name="Db"></param>
        /// <param name="user"></param>
        /// <param name="ErrorMsg"></param>
        /// <param name="MESSAGE"></param>
        /// <returns></returns>
        public static List<APPT03> QAppt03ToAPPT03(string REF_TABLE, string REF_KEY, string UpREF_KEY, ref ECOOL_DEVEntities Db, UserProfile user, out string ErrorMsg, string MESSAGE)
        {
            List<APPT03> ReturnList = new List<APPT03>();

            ErrorMsg = "";
            try
            {
                string sSQL = $@"
                            SELECT DISTINCT A.*
                            ,Case When (Select count(*) from HRMT05 e  (NOLOCK)  where e.SCHOOL_NO=a.SCHOOL_NO and e.USER_NO=a.USER_NO and e.STATUS='{HRMT05.StatusVal.normal}' and len(e.DEVICE_TOKEN)>10)>0 Then 'Y' else 'N' end as INSTALL_APP_YN
                            FROM HRMT01 A (NOLOCK)
                            LEFT OUTER JOIN HRMT25 B  (NOLOCK) ON A.SCHOOL_NO =B.SCHOOL_NO AND A.USER_NO =B.USER_NO
                            LEFT OUTER JOIN HRMT25_UN C  (NOLOCK) ON A.USER_TYPE =C.USER_TYPE
                            WHERE A.USER_STATUS='{UserStaus.Enabled}'
                            AND (
                                EXISTS
                                (
                                 select M.*
                                 from APPT03_Q M  (NOLOCK)
                                 where M.REF_TABLE=@REF_TABLE AND M.REF_KEY=@REF_KEY
                                AND M.BTN_TYPE='{APPT03_Q.BTN_TYPE_VAL.sys_role}'
                                 AND  M.USER_TYPE = A.USER_TYPE
                                 AND ((M.SCHOOL_NO!='ALL' AND M.SCHOOL_NO=A.SCHOOL_NO) OR (M.SCHOOL_NO='ALL'))
                                )
                                OR
                                EXISTS
                                (
                                select M.*
                                 from APPT03_Q M  (NOLOCK)
                                 where M.REF_TABLE=@REF_TABLE AND M.REF_KEY=@REF_KEY
                                AND M.BTN_TYPE='{APPT03_Q.BTN_TYPE_VAL.role}'
                                AND  M.ROLE_ID =ISNULL(B.ROLE_ID,C.ROLE_ID)
                                AND ((M.SCHOOL_NO!='ALL' AND M.SCHOOL_NO=A.SCHOOL_NO) OR (M.SCHOOL_NO='ALL'))
                                )
                                OR
                                EXISTS
                                (
                                 select M.*
                                 from APPT03_Q M  (NOLOCK)
                                 where M.REF_TABLE=@REF_TABLE AND M.REF_KEY=@REF_KEY
                                AND M.BTN_TYPE='{APPT03_Q.BTN_TYPE_VAL.grade}'
                                 AND  M.GRADE = A.GRADE
                                 AND ((M.SCHOOL_NO!='ALL' AND M.SCHOOL_NO=A.SCHOOL_NO) OR (M.SCHOOL_NO='ALL'))
                                )
                                OR
                                EXISTS
                                (
                                 select M.*
                                 from APPT03_Q M  (NOLOCK)
                                 where M.REF_TABLE=@REF_TABLE AND M.REF_KEY=@REF_KEY
                                AND M.BTN_TYPE='{APPT03_Q.BTN_TYPE_VAL.Class}'
                                 AND  M.CLASS_NO = A.CLASS_NO
                                 AND ((M.SCHOOL_NO!='ALL' AND M.SCHOOL_NO=A.SCHOOL_NO) OR (M.SCHOOL_NO='ALL'))
                                )
                                OR
                                EXISTS
                                (
                                 select M.*
                                 from APPT03_Q M  (NOLOCK)
                                 where M.REF_TABLE=@REF_TABLE AND M.REF_KEY=@REF_KEY
                                AND M.BTN_TYPE='{APPT03_Q.BTN_TYPE_VAL.person}'
                                 AND  M.USER_NO = A.USER_NO
                                 AND ((M.SCHOOL_NO!='ALL' AND M.SCHOOL_NO=A.SCHOOL_NO) OR (M.SCHOOL_NO='ALL'))
                                )
                            )";

                var QTemp = Db.Database.Connection.Query<PushHRMT01Model>(sSQL
                    , new
                    {
                        REF_TABLE = REF_TABLE,
                        REF_KEY = REF_KEY,
                    });

                string NewREF_KEY;

                var TempBool = Db.APPT03_Q.Where(a => a.REF_TABLE == REF_TABLE && a.REF_KEY == REF_KEY && a.STATUS == APPT03.StatusVal.TempKey).Any();

                if (TempBool)
                {
                    if (string.IsNullOrWhiteSpace(UpREF_KEY))
                    {
                        NewREF_KEY = DateTime.Now.ToString("yyyyMMddHHmmssfff");
                    }
                    else
                    {
                        NewREF_KEY = UpREF_KEY;
                    }
                }
                else
                {
                    NewREF_KEY = REF_KEY;

                    Db.APPT03.Where(a => a.REF_KEY == REF_KEY && a.REF_TABLE == REF_TABLE).Delete();
                }

                //批次id =>這次執行id
                string BATCH_KEY = "BATCH" + DateTime.Now.ToString("yyyyMMddHHmmssfff");

                var APPT03List = QTemp.ToList()
                       .Select(x => new APPT03
                       {
                           BATCH_KEY = BATCH_KEY,
                           REF_TABLE = REF_TABLE,
                           REF_KEY = NewREF_KEY,
                           SCHOOL_NO = x.SCHOOL_NO,
                           USER_NO = x.USER_NO,
                           CRE_PERSON = (user != null) ? user.USER_KEY : "",
                           CRE_DATE = DateTime.Now,
                           STATUS = APPT03.StatusVal.RealKey,
                           INSTALL_APP_YN = x.INSTALL_APP_YN
                       }).ToList();

                if (APPT03List.Count() > 0)
                {
                    var OK = APPT03List.SqlBatchData("APPT03", out ErrorMsg);
                    if (OK)
                    {
                        ReturnList = APPT03List;
                    }
                }
                if (string.IsNullOrWhiteSpace(ErrorMsg))
                {
                    MESSAGE = StringHelper.MakePlainText(MESSAGE);

                    if (Encoding.Default.GetBytes(MESSAGE).Length >= 4000)
                    {
                        MESSAGE = StringHelper.SubStr(MESSAGE, 0, 3990) + "...";
                    }

                    Db.APPT03_Q.Where(a => a.REF_TABLE == REF_TABLE && a.REF_KEY == REF_KEY)
                     .Update(b => new APPT03_Q
                     {
                         STATUS = APPT03.StatusVal.RealKey,
                         MESSAGE = MESSAGE,
                         REF_KEY = NewREF_KEY
                     });
                }
            }
            catch (Exception ex)
            {
                logger.Error(ex.Message);
                ErrorMsg = ex.Message;
            }

            return ReturnList;
        }

        //    return ReturnBool;
        //}
        /// <summary>
        /// APPT03_Q to APPT03 同時 Push => APPT03ToPushData
        /// </summary>
        /// <param name="REF_TABLE"></param>
        /// <param name="REF_KEY"></param>
        /// <param name="UpREF_KEY"></param>
        /// <param name="Db"></param>
        /// <param name="user"></param>
        /// <param name="ErrorMsg"></param>
        /// <param name="BODY_TXT"></param>
        /// <param name="REF_SOU_BRE_NO"></param>
        /// <param name="REF_SOU_ITEM"></param>
        /// <param name="TITLE_TXT"></param>
        /// <param name="TO_PATH"></param>
        public static void QAppt03ToAPPT03Push(string REF_TABLE, string REF_KEY, string UpREF_KEY, ref ECOOL_DEVEntities Db, UserProfile user, out string ErrorMsg
            , string BODY_TXT = "", string REF_SOU_BRE_NO = "", string REF_SOU_ITEM = "", string TITLE_TXT = "", string TO_PATH = "")
        {
            List<APPT03> APPT03List = QAppt03ToAPPT03(REF_TABLE, REF_KEY, UpREF_KEY, ref Db, user, out ErrorMsg, BODY_TXT);

            if (APPT03List.Count() > 0)
            {
                string NewREF_KEY = APPT03List.FirstOrDefault().REF_KEY;
                PushService.APPT03ToPushData(APPT03List, out ErrorMsg, BODY_TXT, REF_SOU_BRE_NO, REF_SOU_ITEM, NewREF_KEY, TITLE_TXT, TO_PATH);
            }
        }

        //    OutAPPT03List = APPT03List;
        /// <summary>
        /// APPT03 塞進  APPT02 同時  Push
        /// </summary>
        /// <param name="APPT03List"></param>
        /// <param name="ErrorMsg"></param>
        /// <param name="BODY_TXT"></param>
        /// <param name="REF_SOU_BRE_NO"></param>
        /// <param name="REF_SOU_ITEM"></param>
        /// <param name="REF_SOU_KEY"></param>
        /// <param name="TITLE_TXT"></param>
        /// <param name="TO_PATH"></param>
        public static void APPT03ToPushData(List<APPT03> APPT03List, out string ErrorMsg, string BODY_TXT = "", string REF_SOU_BRE_NO = "", string REF_SOU_ITEM = "", string REF_SOU_KEY = "", string TITLE_TXT = "", string TO_PATH = "")
        {
            string BATCH_ID = CreBATCH_ID();

            List<APPT02> T02List = (from a in APPT03List
                                    select new APPT02
                                    {
                                        BATCH_ID = BATCH_ID,
                                        SCHOOL_NO = a.SCHOOL_NO,
                                        USER_NO = a.USER_NO,
                                        TITLE_TXT = TITLE_TXT,
                                        BODY_TXT = BODY_TXT,
                                        SOUND = "default",
                                        BADGE = 1,
                                        F_DATE = DateTime.Now,
                                        CHG_DATE = DateTime.Now,
                                        CHG_PERSON = a.CRE_PERSON,
                                        CRE_DATE = DateTime.Now,
                                        CRE_PERSON = a.CRE_PERSON,
                                        REF_SOU_BRE_NO = REF_SOU_BRE_NO,
                                        REF_SOU_ITEM = REF_SOU_ITEM,
                                        REF_SOU_KEY = REF_SOU_KEY,
                                        TO_PATH = TO_PATH,
                                        STATUS = a.INSTALL_APP_YN == "Y" ? APPT02.StatusVal.Cre : APPT02.StatusVal.UnRead,
                                        DEL_YN = "N"
                                    }).ToList();

            var OK = T02List.SqlBatchData("APPT02", out ErrorMsg);

            if (OK)
            {
                PushHelper.ToPushServer(BATCH_ID);
            }
        }

        /// <summary>
        /// Push 後資料處理
        /// </summary>
        /// <param name="ListTmp"></param>
        /// <param name="LisErr"></param>
        /// <param name="BATCH_ID"></param>
        public static void PushAfterData(List<APPT02_PUSH_TEMP> ListTmp, List<APPT02_ERROR> LisErr, string BATCH_ID)
        {
            if (ListTmp.Count > 0 || LisErr.Count > 0)
            {
                using (SqlConnection conn = new sqlConnection().getConnection4Query())
                {
                    //  SqlTransaction trans = conn.BeginTransaction();

                    using (SqlBulkCopy sbCopy = new SqlBulkCopy(conn))
                    {
                        if (ListTmp.Count > 0)
                        {
                            sbCopy.DestinationTableName = "APPT02_PUSH_TEMP";
                            sbCopy.WriteToServer(ListTmp.AsDataTable());

                            IDbCommand cmd = new SqlCommand();
                            cmd.Parameters.Add(new SqlParameter("@STATUS", APPT02.StatusVal.UnRead));
                            cmd.Parameters.Add(new SqlParameter("@HRMT05_STATUS", HRMT05.StatusVal.Invalid));
                            cmd.Parameters.Add(new SqlParameter("@BATCH_ID", BATCH_ID));

                            cmd.Connection = conn;
                            // cmd.Transaction = trans;

                            string sSQL = @" UPDATE APPT02 SET ";
                            sSQL = sSQL + "  STATUS = @STATUS ";
                            sSQL = sSQL + " , PUSH_TYPE ='1' ";
                            sSQL = sSQL + " FROM APPT02 U  ";
                            sSQL = sSQL + " INNER JOIN (Select M.NOTIFICATION_ID from APPT02_PUSH_TEMP M Where M.BATCH_ID=@BATCH_ID group by  M.NOTIFICATION_ID ) S ON U.NOTIFICATION_ID = S.NOTIFICATION_ID  ";

                            cmd.CommandText = sSQL;
                            cmd.ExecuteNonQuery();

                            sSQL = @" UPDATE APPT02 SET ";
                            sSQL = sSQL + "  STATUS = @STATUS ";
                            sSQL = sSQL + " , PUSH_TYPE ='0' ";
                            sSQL = sSQL + " FROM APPT02 U  ";
                            sSQL = sSQL + " Where U.BATCH_ID=@BATCH_ID  and isnull(U.PUSH_TYPE,'') <>'1' ";

                            cmd.CommandText = sSQL;
                            cmd.ExecuteNonQuery();

                            sSQL = @" update HRMT05 set STATUS =@HRMT05_STATUS
                             from HRMT05 a
                             inner join APPT02_PUSH_TEMP b on a.DEVICE_TOKEN=b.DEVICE_TOKEN and b.SENTOK=0
                             where b.BATCH_ID=@BATCH_ID
                             and (charindex('InvalidToken',b.MSG,1) >0 or charindex('InvalidRegistration',b.MSG,1) >0) ";
                            cmd.CommandText = sSQL;
                            cmd.ExecuteNonQuery();

                            sSQL = @" delete APPT02_WAIT_PUSH  ";
                            sSQL = sSQL + " FROM APPT02_WAIT_PUSH U  ";
                            sSQL = sSQL + " INNER JOIN (Select M.NOTIFICATION_ID,M.DEVICE_TOKEN from APPT02_PUSH_TEMP M Where M.BATCH_ID=@BATCH_ID group by  M.NOTIFICATION_ID,M.DEVICE_TOKEN ) S ON U.NOTIFICATION_ID = S.NOTIFICATION_ID AND U.DEVICE_TOKEN=S.DEVICE_TOKEN ";
                            cmd.CommandText = sSQL;
                            cmd.ExecuteNonQuery();

                            cmd.CommandText = "DELETE APPT02_PUSH_TEMP Where datediff(d,CRE_DATE,GETDATE())>1 ";
                            cmd.ExecuteNonQuery();
                        }

                        if (LisErr.Count() > 0)
                        {
                            sbCopy.DestinationTableName = "APPT02_ERROR";
                            sbCopy.WriteToServer(LisErr.AsDataTable());
                        }

                        //   trans.Commit();
                    }
                }
            }
        }

        public static void PushAfterData(APPT02_PUSH_TEMP ListTmp, string PUSH_TYPE)
        {
            using (SqlConnection conn = new sqlConnection().getConnection4Query())
            {
                using (TransactionScope tx = new TransactionScope())
                {
                    try
                    {
                        string insertQuery = @"INSERT INTO APPT02_PUSH_TEMP(NOTIFICATION_ID,BATCH_ID,DEVICE_TOKEN,CRE_DATE,MSG,SENTOK)
                                            VALUES (@NOTIFICATION_ID ,@BATCH_ID,@DEVICE_TOKEN,@CRE_DATE,@MSG,@SENTOK)";

                        conn.Execute(insertQuery, ListTmp);

                        string sSQL = @" UPDATE APPT02 SET ";
                        sSQL = sSQL + "  STATUS = @STATUS ";
                        sSQL = sSQL + " , PUSH_TYPE =@PUSH_TYPE ";
                        sSQL = sSQL + " FROM APPT02 U Where  NOTIFICATION_ID=@NOTIFICATION_ID";
                        conn.Execute(sSQL, new { PUSH_TYPE = PUSH_TYPE, STATUS = APPT02.StatusVal.UnRead, NOTIFICATION_ID = ListTmp.NOTIFICATION_ID });

                        sSQL = @" update HRMT05 set STATUS =@HRMT05_STATUS
                                 from HRMT05 a
                                 inner join APPT02_PUSH_TEMP b on a.DEVICE_TOKEN=b.DEVICE_TOKEN and b.SENTOK=0
                                 where b.BATCH_ID=@BATCH_ID and b.NOTIFICATION_ID=@NOTIFICATION_ID and b.DEVICE_TOKEN=@DEVICE_TOKEN
                                 and (charindex('InvalidToken',b.MSG,1) >0 or charindex('InvalidRegistration',b.MSG,1) >0) ";
                        conn.Execute(sSQL, new
                        {
                            HRMT05_STATUS = HRMT05.StatusVal.Invalid,
                            BATCH_ID = ListTmp.BATCH_ID,
                            NOTIFICATION_ID = ListTmp.NOTIFICATION_ID,
                            DEVICE_TOKEN = ListTmp.DEVICE_TOKEN
                        });

                        sSQL = @" delete APPT02_WAIT_PUSH  ";
                        sSQL = sSQL + " FROM APPT02_WAIT_PUSH U  ";
                        sSQL = sSQL + " Where NOTIFICATION_ID=@NOTIFICATION_ID and  DEVICE_TOKEN=@DEVICE_TOKEN";
                        conn.Execute(sSQL, new
                        {
                            NOTIFICATION_ID = ListTmp.NOTIFICATION_ID,
                            DEVICE_TOKEN = ListTmp.DEVICE_TOKEN
                        });

                        sSQL = @"DELETE APPT02_PUSH_TEMP Where datediff(d,CRE_DATE,GETDATE())>1 ";
                        conn.Execute(sSQL);
                    }
                    catch (Exception ex)
                    {
                        throw ex;
                    }

                    tx.Complete();
                }
            }
        }

        /// <summary>
        /// 取得ios 待通知資料
        /// </summary>
        /// <param name="Query"></param>
        /// <returns></returns>
        public List<PushSaveModel> GetIOSListData(PushQueryModel Query)
        {
            return GetListData(HRMT05.OS_TypeVal.IOS.ToString(), Query);
        }

        /// <summary>
        /// 取得Android待通知資料
        /// </summary>
        /// <param name="Query"></param>
        /// <returns></returns>
        public List<PushSaveModel> GetAndroidListData(PushQueryModel Query)
        {
            return GetListData(HRMT05.OS_TypeVal.Android.ToString(), Query);
        }

        //    if (APPT03List.Count() > 0)
        //    {
        //        ReturnBool = APPT03List.SqlBatchData("APPT03", out ErrorMsg);
        //    }
        //    else
        //    {
        //        ErrorMsg = string.Empty;
        //    }
        private static void InsertPushData(PushSaveModel Query, ref ECOOL_DEVEntities db, List<APPT02> T02List)
        {
            SharedSave(Query, ref db, T02List);
        }

        //    var APPT03List = TempAPPT03.ToList()
        //    .Select(x=>new APPT03
        //    {
        //        BATCH_KEY = BATCH_KEY,
        //        REF_TABLE = x.REF_TABLE,
        //        REF_KEY = x.REF_KEY,
        //        SCHOOL_NO = x.SCHOOL_NO,
        //        USER_NO = x.USER_NO,
        //        CRE_PERSON = x.CRE_PERSON,
        //        CRE_DATE = x.CRE_DATE,
        //        STATUS = x.STATUS
        //    }).ToList();
        private static void InsertPushData(PushSaveModel Query)
        {
            ECOOL_DEVEntities db = new ECOOL_DEVEntities();

            SharedSave(Query, ref db, null);

            PushHelper.ToPushServer(Query.BATCH_ID);
        }

        //    //批次id =>這次執行id
        //    string BATCH_KEY = "BATCH" + DateTime.Now.ToString("yyyyMMddHHmmssfff");
        private static void SharedSave(PushSaveModel Query, ref ECOOL_DEVEntities db, List<APPT02> T02List)
        {
            APPT02 T02 = new APPT02();
            T02.BATCH_ID = Query.BATCH_ID;
            T02.SCHOOL_NO = Query.SCHOOL_NO;
            T02.USER_NO = Query.USER_NO;
            T02.TITLE_TXT = Query.TITLE_TXT;
            T02.BODY_TXT = Query.BODY_TXT;

            if (string.IsNullOrWhiteSpace(Query.SOUND))
            {
                T02.SOUND = "default";
            }
            else
            {
                T02.SOUND = Query.SOUND;
            }

            T02.BADGE = 1;

            if (Query.F_DATE == null)
            {
                T02.F_DATE = DateTime.Now;
            }
            else
            {
                T02.F_DATE = Query.F_DATE;
            }

            T02.CHG_DATE = DateTime.Now;
            T02.CHG_PERSON = Query.CRE_PERSON;
            T02.CRE_DATE = DateTime.Now;
            T02.CRE_PERSON = Query.CRE_PERSON;
            T02.REF_SOU_BRE_NO = Query.REF_SOU_BRE_NO;
            T02.REF_SOU_ITEM = Query.REF_SOU_ITEM;
            T02.REF_SOU_KEY = Query.REF_SOU_KEY;
            T02.TO_PATH = Query.TO_PATH;

            if (db.HRMT05.Where(a => a.SCHOOL_NO == Query.SCHOOL_NO && a.USER_NO == Query.USER_NO
                 && a.STATUS == HRMT05.StatusVal.normal
                 && a.DEVICE_TOKEN != null && (a.DEVICE_TOKEN ?? "").Length > 10).Any() == false)
            {
                T02.STATUS = APPT02.StatusVal.UnRead;
            }
            else
            {
                T02.STATUS = APPT02.StatusVal.Cre;
            }

            T02.DEL_YN = "N";

            if (T02List == null)
            {
                //儲存資料
                db.APPT02.Add(T02);

                if (Query.RightAway)
                {
                    db.SaveChanges();
                }
            }
            else
            {
                T02List.Add(T02);
            }
        }

        /// <summary>
        /// 通知資料語法
        /// </summary>
        /// <param name="OS_TYPE"></param>
        /// <param name="Query"></param>
        /// <returns></returns>
        private List<PushSaveModel> GetListData(string OS_TYPE, PushQueryModel Query)
        {
            StringBuilder sb = new StringBuilder();

            sb.Append(" SELECT a.DEVICE_TOKEN ");
            sb.Append(", B.NOTIFICATION_ID ,B.SCHOOL_NO,B.USER_NO,B.TITLE_TXT,B.BODY_TXT,B.SOUND ");
            sb.AppendFormat(" ,BADGE = isnull((SELECT Sum(S.BADGE) from APPT02 S (NOLOCK) Where A.SCHOOL_NO = S.SCHOOL_NO AND A.USER_NO = S.USER_NO   AND S.STATUS = '{0}'),0) ", APPT02.StatusVal.UnRead);

            sb.Append(" + isnull((SELECT Sum(M.BADGE) FROM APPT02_WAIT_PUSH M(NOLOCK)  Where A.SCHOOL_NO = M.SCHOOL_NO AND A.USER_NO = M.USER_NO AND A.DEVICE_TOKEN =M.DEVICE_TOKEN   ");
            if (Query.BATCH_ID != null)
            {
                sb.AppendFormat(" and M.BATCH_ID = '{0}' ", Query.BATCH_ID);
            }
            else
            {
                sb.AppendFormat(" and M.F_DATE <= DATEADD(minute,-5,getdate()) ");
            }

            sb.Append(" and M.NOTIFICATION_ID < B.NOTIFICATION_ID),0)+1 ");
            sb.Append(" FROM HRMT05 A (NOLOCK)  ");
            sb.Append("  INNER JOIN APPT02_WAIT_PUSH B(NOLOCK) ON A.SCHOOL_NO = B.SCHOOL_NO AND A.USER_NO = B.USER_NO AND A.DEVICE_TOKEN =B.DEVICE_TOKEN");
            sb.AppendFormat("  Where A.STATUS = '{0}' ", HRMT05.StatusVal.normal);
            sb.AppendFormat(" and A.OS_TYPE = '{0}'  ", OS_TYPE);
            sb.Append("  and a.DEVICE_TOKEN is not null and len(a.DEVICE_TOKEN)>10  ");

            if (Query.BATCH_ID != null)
            {
                sb.AppendFormat(" and B.BATCH_ID = '{0}' ", Query.BATCH_ID);
            }
            else
            {
                sb.AppendFormat(" and B.F_DATE <= DATEADD(minute,-5,getdate()) ");
            }

            if (Query.NOTIFICATION_ID != null)
            {
                sb.AppendFormat(" and A.NOTIFICATION_ID = '{0}' ", Query.NOTIFICATION_ID);
            }

            if (Query.SCHOOL_NO != null)
            {
                sb.AppendFormat(" and A.SCHOOL_NO = '{0}' ", Query.SCHOOL_NO);
            }

            if (Query.USER_NO != null)
            {
                sb.AppendFormat(" and A.USER_NO = '{0}' ", Query.USER_NO);
            }

            sb.Append("  order by B.F_DATE, B.NOTIFICATION_ID, a.DEVICE_TOKEN  ");
            logger.Info(sb.ToString());

            var Temp = new sqlConnection().executeQueryByDataTableList(sb.ToString()).ToList<PushSaveModel>();
            // logger.Info(Temp.Count().ToString());

            return Temp;
        }

        //public static void APPT03PushData(ref ECOOL_DEVEntities db, out string ErrorMsg
        //, string REF_TABLE, string REF_KEY, string CRE_PERSON, string SCHOOL_NO, string USER_NO,string StrUSER_TYPE
        // ,byte STATUS
        //, bool BoolGreAPPT02 = false
        //, string BODY_TXT =""
        //, string REF_SOU_BRE_NO = ""
        //, string REF_SOU_ITEM = ""
        //, string TITLE_TXT = ""
        //, string TO_PATH = "")
        //{
        //    APPT03QueryModel QData = new APPT03QueryModel();
        //    QData.REF_TABLE = REF_TABLE;
        //    QData.REF_KEY = REF_KEY;
        //    QData.CRE_PERSON = CRE_PERSON;
        //    QData.SCHOOL_NO = SCHOOL_NO;
        //    QData.USER_NO = USER_NO;
        //    QData.ArrUSER_TYPE = StrUSER_TYPE.Split(',');
        //    QData.STATUS = STATUS;

        //    List<APPT03> TempList = new List<APPT03>() ;

        //   bool Ok = InsertAPPT03(QData, ref db, out ErrorMsg,out TempList);

        //    if (Ok && BoolGreAPPT02)
        //    {
        //        APPT03ToPushData(TempList, out ErrorMsg, BODY_TXT, REF_SOU_BRE_NO, REF_SOU_ITEM, REF_KEY, TITLE_TXT, TO_PATH);
        //    }
        //}

        //private static bool InsertAPPT03(APPT03QueryModel QData
        //    , ref ECOOL_DEVEntities db,out string ErrorMsg,out List<APPT03> OutAPPT03List)
        //{
        //    bool ReturnBool = false;

        //    //只保留6小時
        //    var DiffDate = DateTime.Now.AddHours(-6);

        //    //Delete 暫存檔
        //    db.APPT03.Where(a=>a.STATUS == APPT03.StatusVal.TempKey
        //    && (a.CRE_DATE < DiffDate || (a.REF_KEY == QData.REF_KEY && a.REF_TABLE == QData.REF_TABLE))).Delete();

        //    var TempAPPT03 = (from b in db.HRMT01
        //                      where b.USER_STATUS == UserStaus.Enabled
        //                      select new
        //                      {
        //                          REF_TABLE = QData.REF_TABLE,
        //                          REF_KEY = QData.REF_KEY,
        //                          SCHOOL_NO = b.SCHOOL_NO,
        //                          USER_NO = b.USER_NO,
        //                          CRE_PERSON = QData.CRE_PERSON,
        //                          CRE_DATE = DateTime.Now,
        //                          STATUS = QData.STATUS,
        //                          USER_TYPE =b.USER_TYPE
        //                      });

        //    if (string.IsNullOrWhiteSpace(QData.SCHOOL_NO) == false)
        //    {
        //        TempAPPT03 = TempAPPT03.Where(a => a.SCHOOL_NO == QData.SCHOOL_NO);
        //    }

        //    if (string.IsNullOrWhiteSpace(QData.USER_NO) == false)
        //    {
        //        TempAPPT03 = TempAPPT03.Where(a => a.USER_NO == QData.USER_NO);
        //    }

        //    if (QData.ArrUSER_TYPE != null && QData.ArrUSER_TYPE.Length>0)
        //    {
        //        TempAPPT03 = TempAPPT03.Where(a => QData.ArrUSER_TYPE.Contains(a.USER_TYPE));
        //    }
    }
}