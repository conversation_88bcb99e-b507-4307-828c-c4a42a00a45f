﻿@model ADDI10StatisticsVoteDetailsViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;
    ECOOL_APP.com.ecool.service.ADDI10Service aDDI10Ser = new ECOOL_APP.com.ecool.service.ADDI10Service();
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")
@Html.HiddenFor(x => x.OrdercColumn)
@Html.HiddenFor(x => x.SyntaxName)
@Html.HiddenFor(x => x.whereQ_NUM)
<link href="~/Content/colorbox/example4/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>
<div class="modal fade" id="myModal1" role="dialog">
    <div class="modal-content">

        <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal">&times;</button>
            <h4 class="modal-title">下載進度</h4>
        </div>
    </div>
    <div class="modal-body">
        <div class="progress progress-striped active">
            <div class="progress-bar progress-bar-success" id="barr1" role="progressbar" aria-valuenow="60" aria-valuemin="0" aria-valuemax="100" style="width:0%;">

                <span class="sr-only">40% 完成</span>
            </div>
        </div>
    </div>
    <div class="modal-footer">

        <button type="button" class="btn btn-default" data-dismiss="modal">Close </button>
    </div>
</div>
<div class="Div-EZ-reader" id="form1">
    <div class="table-responsive">
        <table class="table-ecool table-92Per table-hover">
           @{
               string strcss = "";

               if (AppMode==false) {
                   strcss = "caption-side:top";
               }
               }
            <caption class="Caption_Div_Left" style="@strcss">
                活動名稱：@Model.Title.QUESTIONNAIRE_NAME <br />
                題目：@Html.Raw(HttpUtility.HtmlDecode(Model.Topic.Q_SUBJECT)) <br />
                @if (!string.IsNullOrWhiteSpace(Model.ANSWER))
                {
                    <samp> 回答：@Html.Raw(HttpUtility.HtmlDecode(Model.ANSWER))</samp>
                }

                <br /><br />

                <samp class="dt">年級</samp>
                <samp class="dd">
                    @Html.DropDownListFor(m => m.whereGrade1, (IEnumerable<SelectListItem>)ViewBag.GradeItem, new { onchange = "FunPageProc(1)" })
                </samp>

                <samp class="dt">班級</samp>
                <samp class="dd">
                    @Html.DropDownListFor(m => m.whereCLASS_NO1, (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { onchange = "FunPageProc(1)" })
                </samp>

                <samp class="dt">   姓名</samp>
                <samp class="dd"> @Html.EditorFor(m => m.WhereSearch)</samp>


                <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1)" />
                <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />
                @if (Model.GetSAQT03.Q_INPUT_TYPE == "UploadImage" || Model.GetSAQT03.Q_INPUT_TYPE == "UploadWorld" || Model.GetSAQT03.Q_INPUT_TYPE == "UploadPdf")
                {
                    <button type="button" class="btn-yellow btn btn-sm" onclick="DownLoadJPG()">批次下載</button>}
            </caption>
            <thead>
                <tr>
                    <th>
                        @Html.DisplayNameFor(model => model.VotePeople.First().SHORT_NAME)
                    </th>
                    <th>
                        @Html.DisplayNameFor(model => model.VotePeople.First().SNAME)
                    </th>
                    <th onclick="doSort('CLASS_NO')">

                        @Html.DisplayNameFor(model => model.VotePeople.First().CLASS_NO)
                        <img id="CLASS_NO" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                    </th>
                    <th onclick="doSort('SEAT_NO')">
                        @*<a href="#" onclick="doSort('SEAT_NO')">*@
                        @Html.DisplayNameFor(model => model.VotePeople.First().SEAT_NO)@*</a>*@
                        <img id="SEAT_NO" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                    </th>
                    <th onclick="doSort('ANSWER_DATE')">
                        @Html.DisplayNameFor(model => model.VotePeople.First().ANSWER_DATE)
                        <img id="ANSWER_DATE" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                    </th>
                    @if (string.IsNullOrWhiteSpace(Model.ANSWER))
                    {
                        <th>
                            回答
                        </th>
                    }
                    <th> </th>
                </tr>
            </thead>
            <tbody>
                @foreach (var item in Model.VotePeople)
                {

                    if (Model.Topic.Q_TYPE.ToString() != SAQT03.Q_INPUT_TYPEVal.radio && Model.Topic.Q_TYPE.ToString() != SAQT03.Q_INPUT_TYPEVal.checkbox)
                    {

                <tr>

                    @if (!string.IsNullOrWhiteSpace(item.ANSWER))
                    {
                        <td align="center">
                            @Html.DisplayFor(modelItem => item.SHORT_NAME)
                        </td>
                        <td align="center">
                            @Html.DisplayFor(modelItem => item.SNAME)
                        </td>
                        <td align="center">
                            @if (string.IsNullOrWhiteSpace(item.CLASS_NO))
                            {
                                <samp>-</samp>
                            }
                            else
                            {
                                @Html.DisplayFor(modelItem => item.CLASS_NO)
                            }
                        </td>
                        <td align="center">
                            @if (string.IsNullOrWhiteSpace(item.SEAT_NO))
                            {
                                <samp>-</samp>
                            }
                            else
                            {
                                @Html.DisplayFor(modelItem => item.SEAT_NO)
                            }
                        </td>
                        <td align="center">
                            @Html.DisplayFor(modelItem => item.ANSWER_DATE)
                        </td>

                        if (!string.IsNullOrWhiteSpace(item.ANSWER))
                        {
                            if (Model.GetSAQT03.Q_INPUT_TYPE == "UploadImage")
                            {

                                string ii = "";
                                ii = item.ANSWER.Replace(".", "_M.");

                                string tempPath = aDDI10Ser.GetDirectorySysArtGalleryPath(item.SCHOOL_NO, item.QUESTIONNAIRE_ID, item.ANSWER_ID, ii);
                                if (tempPath == "")
                                {

                                    tempPath = aDDI10Ser.GetDirectorySysArtGalleryPath(item.SCHOOL_NO, item.QUESTIONNAIRE_ID, item.ANSWER_ID, item.ANSWER);

                                }
                                @*<td align="left"></td>*@
                                string ImgSrc = Url.Action("DownLoadJPG", "ADDI10", new { SCHOOL_NO = item.SCHOOL_NO, QUESTIONNAIRE_ID = item.QUESTIONNAIRE_ID, ANSWERID = item.ANSWER_ID, ANSWER = item.ANSWER });

                                <td align="center">
                                    <img src="@tempPath" class="img-responsive colorboxPHOTO" style="max-width:100px" id="ImageA1" title=" @item.ANSWER" alt="@item.ANSWER" href="@tempPath" />
                                    @*<img src="@tempPath" class="img-responsive colorboxPHOTO" alt="@item.ANSWER" href="@tempPath" style="max-width:100px" id="ImageA1" title=" @item.ANSWER" />*@
                                    @*<img class="img-responsive colorbox" src="@tempPath" title="@item.ANSWER" alt="@item.ANSWER" style="width:50px;max-height:50px" href="@tempPath" />*@
                                    @*<a href="#" onClick="window.open('@ImgSrc')">下載</a>會自動等比例縮小成(大圖寬度1600px，小圖寬度200px))*@

                                </td>
                                if (user.USER_TYPE == "A")
                                {

                                    <td align="center">
                                        @Html.Action("_imagesRotateBtnView", "Comm", new { ImgURL = tempPath, ImgURL_S = "", ImgUrl_M = "", ImgID = "ImageA1" })
                                    </td>

                                }
                            }
                            else if (Model.GetSAQT03.Q_INPUT_TYPE == "UploadWorld")
                            {
                                string tempPath = aDDI10Ser.GetDirectorySysArtGalleryPath(item.SCHOOL_NO, item.QUESTIONNAIRE_ID, item.ANSWER_ID, item.ANSWER);
                                <td align="left">

                                    <a href="@tempPath">下載</a>

                                </td>
                            }
                            else if (Model.GetSAQT03.Q_INPUT_TYPE == "UploadPdf")
                            {
                                string tempPath = aDDI10Ser.GetDirectorySysArtGalleryPath(item.SCHOOL_NO, item.QUESTIONNAIRE_ID, item.ANSWER_ID, item.ANSWER);
                                <td align="left">

                                    <a href="@tempPath">下載</a>

                                </td>
                            }
                            else
                            {
                                <td align="left">
                                    @Html.DisplayFor(modelItem => item.ANSWER)
                                </td>

                            }


                        }

                    }
                </tr>
                    }
                }
            </tbody>
        </table>

        <div style="height:15px"></div>
        <div align="center" role="group">
            @{
                int Pcount = 0;

            }
            @if (Model.Topic.Q_TYPE.ToString() != SAQT03.Q_INPUT_TYPEVal.radio && Model.Topic.Q_TYPE.ToString() != SAQT03.Q_INPUT_TYPEVal.checkbox)
            {
                Pcount = Model.VotePeople.Where(x => !string.IsNullOrWhiteSpace(x.ANSWER)).Count();
            }
            else
            {
                Pcount = Model.VotePeople.Count();
            }
            共 @(Pcount) 人
        </div>

        <div style="height:25px"></div>
    </div>
</div>

<script>
    $(".colorboxPHOTO").colorbox({ photo: true, maxWidth: "90%", maxHeight: "90%", opacity: 0.82 });
    var targetFormID = '#form1';
    $(document).ready(function () {

        $('#cboxClose').click(function () {
            window.parent.location.reload(true);

        });
        $('#cboxOverlay').click(function () {
            window.parent.location.reload(true);

        });
        $(".progressImg").colorbox({ opacity: 0.82, width: "70%", innerHeight: "500px", scrolling: true });
    });
    function FunPageProc(page) {
        var whereGradetxt = $("#whereGrade1").val();
        console.log(whereGradetxt);
        var whereCLASS_NOtxt = $("#whereCLASS_NO1").val();
        $('#@Html.IdFor(m=>m.whereGrade)').val(whereGradetxt);

        $('#@Html.IdFor(m=>m.whereCLASS_NO)').val(whereCLASS_NOtxt);
            if ($(targetFormID).size() > 0) {
                $('#@Html.IdFor(m=>m.Page)').val(page)

                funAjax()
            }
    };

    function todoClear() {
        ////重設
              $(targetFormID).find(":input,:selected").each(function (i) {

            var type = $(this).attr('type')
            var InPreadonly = $(this).attr('readonly')
            var tag = this.tagName.toLowerCase(); // normalize case

            if (InPreadonly == false || InPreadonly == undefined) {

                if (type == 'radio' || type == 'checkbox') {
                    if ($(this).attr("title") == 'Default') {
                        this.checked = true;
                    }
                    else {
                        this.checked = false;
                    }
                }
                else if (tag == 'select') { //下拉式選單
                    this.selectedIndex = 0;
                }
                else if (type == 'text' || type == 'hidden' || type == 'password' || type == 'textarea') {
                    this.value = '';
                }
            }
        });

        $(targetFormID).submit();
    }
    function funAjax() {
        var whereGradetxt = $("#whereGrade1").val();
        console.log(whereGradetxt);
        var whereCLASS_NOtxt = $("#whereCLASS_NO1").val();
        var WhereSearchtxt = $("#WhereSearch").val();
        console.log(WhereSearchtxt);
        $.ajax({
            type: "GET",
                url: '@(Url.Action("StatisticsVoteDetails", (string)ViewBag.BRE_NO))',
            data: {
                QUESTIONNAIRE_ID: '@Model.Topic.QUESTIONNAIRE_ID',
                Q_NUM: '@Model.Topic.Q_NUM',
                ANSWER:'@Model.ANSWER',
                OrdercColumn: $('#OrdercColumn').val(),
                SyntaxName: $('#SyntaxName').val(),
                whereGrade: whereGradetxt,
                whereCLASS_NO: whereCLASS_NOtxt,
                whereSearch:WhereSearchtxt
            },
                async: false,
                cache: false,
                dataType: 'html',
                success: function (data) {

                    $("#cboxLoadedContent").html('');
                    $("#cboxLoadedContent").html(data);
                }
            });
    }
    function DownLoadJPG() {

        var whereGradetxt = $("#whereGrade1").val();
        console.log(whereGradetxt);
        var whereCLASS_NOtxt = $("#whereCLASS_NO1").val();
        var WhereSearchtxt = $("#WhereSearch").val();
        $("#myModal1").modal('show');
        var id = setInterval(func, 10);
        var percent = 5;
        var percentW = "";
        percent = 5* id;
        percentW = percent + "%";
        $("#barr1").css("width", percentW);
        var postData = { "QUESTIONNAIRE_ID": "@Model.Topic.QUESTIONNAIRE_ID","Q_NUM":"@Model.Topic.Q_NUM"};

        var func =   $.ajax({
            type: "POST",
            url: "@Url.Action("DownloadFIMG1", "ADDI10")",



            data: JSON.stringify({
                postData
            }),
            contentType: "application/json; charset=utf-8",
            dataType: "json",


               }).done(function (data) {
  

            // alert('File download a success!');
            //var response = JSON.parse(data);
                   window.open("@Url.Action("DownloadZIP", (string)ViewBag.BRE_NO)?file=" + data.filename);
      //     window.location = "@Url.Action("DownloadZIP", (string)ViewBag.BRE_NO)?file=" + data.filename;
            //var blob = new Blob([data]);
            //var link = document.createElement('a');
            //link.href = window.URL.createObjectURL(blob);
            //link.download = "Sample.pdf";
            //link.click();
                 
                       $("#barr1").css("width", "100%");
                       $("#myModal1").modal('hide');
               
               });
     
    }
    function doSort(SortCol) {
        var sort = "";
        sort = SortCol;
        OrderByName = $('#OrdercColumn').val();
        SyntaxName = $('#SyntaxName').val();
        $('#OrdercColumn').val(SortCol);

        if (OrderByName == SortCol) {

            if (SyntaxName == "Desc") {

                $('#OrdercColumn').val(sort);
                $('#SyntaxName').val("ASC");
            }
            else {

                $('#OrdercColumn').val(sort);
                $('#SyntaxName').val("Desc");
            }
        }
        else {

            $('#OrdercColumn').val(sort);
            $('#SyntaxName').val("Desc");
        }
        funAjax()
    }
</script>