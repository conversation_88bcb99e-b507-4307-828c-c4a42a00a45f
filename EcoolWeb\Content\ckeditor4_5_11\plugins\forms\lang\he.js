﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'forms', 'he', {
	button: {
		title: 'מאפייני כפתור',
		text: 'טקסט (ערך)',
		type: 'סוג',
		typeBtn: 'כפתור',
		typeSbm: 'שליחה',
		typeRst: 'איפוס'
	},
	checkboxAndRadio: {
		checkboxTitle: 'מאפייני תיבת סימון',
		radioTitle: 'מאפייני לחצן אפשרויות',
		value: 'ערך',
		selected: 'מסומן',
		required: 'Required' // MISSING
	},
	form: {
		title: 'מאפיני טופס',
		menu: 'מאפיני טופס',
		action: 'שלח אל',
		method: 'סוג שליחה',
		encoding: 'קידוד'
	},
	hidden: {
		title: 'מא<PERSON>יני שדה חבוי',
		name: 'שם',
		value: 'ערך'
	},
	select: {
		title: 'מאפייני שדה בחירה',
		selectInfo: 'מידע',
		opAvail: 'אפשרויות זמינות',
		value: 'ערך',
		size: 'גודל',
		lines: 'שורות',
		chkMulti: 'איפשור בחירות מרובות',
		required: 'Required', // MISSING
		opText: 'טקסט',
		opValue: 'ערך',
		btnAdd: 'הוספה',
		btnModify: 'שינוי',
		btnUp: 'למעלה',
		btnDown: 'למטה',
		btnSetValue: 'קביעה כברירת מחדל',
		btnDelete: 'מחיקה'
	},
	textarea: {
		title: 'מאפייני איזור טקסט',
		cols: 'עמודות',
		rows: 'שורות'
	},
	textfield: {
		title: 'מאפייני שדה טקסט',
		name: 'שם',
		value: 'ערך',
		charWidth: 'רוחב לפי תווים',
		maxChars: 'מקסימום תווים',
		required: 'Required', // MISSING
		type: 'סוג',
		typeText: 'טקסט',
		typePass: 'סיסמה',
		typeEmail: 'דוא"ל',
		typeSearch: 'חיפוש',
		typeTel: 'מספר טלפון',
		typeUrl: 'כתובת (URL)'
	}
} );
