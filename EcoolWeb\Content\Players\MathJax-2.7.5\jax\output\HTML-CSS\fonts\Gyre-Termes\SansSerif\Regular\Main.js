/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Gyre-Termes/SansSerif/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.GyreTermesMathJax_SansSerif={directory:"SansSerif/Regular",family:"GyreTermesMathJax_SansSerif",testString:"\u00A0\uD835\uDDA0\uD835\uDDA1\uD835\uDDA2\uD835\uDDA3\uD835\uDDA4\uD835\uDDA5\uD835\uDDA6\uD835\uDDA7\uD835\uDDA8\uD835\uDDA9\uD835\uDDAA\uD835\uDDAB\uD835\uDDAC\uD835\uDDAD",32:[0,0,250,0,0],160:[0,0,250,0,0],120224:[671,0,616,17,602],120225:[671,0,623,79,579],120226:[687,17,672,48,627],120227:[671,0,676,89,621],120228:[671,0,625,90,571],120229:[671,0,572,90,540],120230:[687,17,725,44,656],120231:[671,0,677,83,599],120232:[671,0,271,100,186],120233:[671,17,467,19,393],120234:[671,0,621,79,612],120235:[671,0,520,80,497],120236:[671,0,778,75,706],120237:[671,0,676,76,600],120238:[687,17,722,38,686],120239:[671,0,625,91,575],120240:[687,50,722,38,686],120241:[671,0,675,93,632],120242:[687,17,621,48,575],120243:[671,0,565,21,547],120244:[671,17,677,85,600],120245:[671,0,618,30,596],120246:[671,0,871,22,856],120247:[671,0,617,22,599],120248:[671,0,615,13,609],120249:[671,0,567,28,539],120250:[496,14,517,42,496],120251:[671,14,518,54,485],120252:[496,14,464,31,441],120253:[671,14,518,26,457],120254:[496,14,518,40,475],120255:[673,0,259,18,239],120256:[496,201,519,29,452],120257:[671,0,523,70,453],120258:[650,0,214,60,156],120259:[650,201,207,-18,149],120260:[671,0,464,58,466],120261:[671,0,215,68,144],120262:[496,0,778,71,705],120263:[496,0,523,70,454],120264:[496,14,518,36,472],120265:[496,201,519,55,486],120266:[496,201,518,26,457],120267:[496,0,313,70,301],120268:[496,14,466,34,425],120269:[615,6,259,14,235],120270:[482,14,523,65,449],120271:[482,0,462,10,448],120272:[482,0,666,6,652],120273:[482,0,464,17,437],120274:[482,201,463,20,441],120275:[482,0,466,31,423],120276:[671,0,654,26,635],120277:[671,0,664,82,608],120278:[682,11,658,44,621],120279:[671,0,662,77,621],120280:[671,0,612,79,569],120281:[671,0,560,74,535],120282:[682,11,711,42,644],120283:[671,0,663,68,598],120284:[671,0,263,63,198],120285:[671,11,510,24,440],120286:[671,0,658,74,653],120287:[671,0,561,80,529],120288:[671,0,762,66,705],120289:[671,0,663,68,602],120290:[682,11,708,40,672],120291:[671,0,611,76,577],120292:[682,40,708,43,675],120293:[671,0,662,80,617],120294:[682,11,607,32,573],120295:[671,0,553,14,540],120296:[671,11,664,76,596],120297:[671,0,605,24,585],120298:[671,0,852,13,840],120299:[671,0,604,22,590],120300:[671,0,605,27,588],120301:[671,0,556,30,523],120302:[505,8,506,28,474],120303:[671,8,559,59,523],120304:[505,8,507,34,473],120305:[671,8,559,29,493],120306:[505,8,506,22,475],120307:[671,0,303,14,283],120308:[505,201,560,34,490],120309:[671,0,564,67,494],120310:[685,0,264,67,195],120311:[685,201,257,4,189],120312:[671,0,507,59,499],120313:[671,0,264,67,193],120314:[505,0,813,60,748],120315:[505,0,563,63,498],120316:[505,8,558,35,516],120317:[505,201,559,58,522],120318:[505,201,559,28,492],120319:[505,0,358,63,339],120320:[505,8,507,29,471],120321:[620,4,304,14,272],120322:[497,8,563,58,493],120323:[497,0,504,14,484],120324:[497,0,702,5,690],120325:[497,0,504,16,483],120326:[497,201,503,9,485],120327:[497,0,455,21,423],120328:[671,0,616,-33,552],120329:[671,0,616,29,610],120330:[687,17,669,63,667],120331:[671,0,668,39,655],120332:[671,0,614,40,648],120333:[671,0,559,40,632],120334:[687,17,722,60,703],120335:[671,0,665,33,692],120336:[671,0,258,50,279],120337:[671,17,457,-2,488],120338:[671,0,608,29,704],120339:[671,0,518,30,463],120340:[671,0,766,25,799],120341:[671,0,664,26,693],120342:[687,17,720,55,721],120343:[671,0,616,41,632],120344:[687,50,720,55,721],120345:[671,0,668,43,666],120346:[687,17,617,40,615],120347:[671,0,564,108,651],120348:[671,17,668,75,696],120349:[671,0,618,135,701],120350:[671,0,871,127,961],120351:[671,0,605,-28,682],120352:[671,0,615,118,714],120353:[671,0,554,-22,630],120354:[496,14,516,16,478],120355:[671,14,513,4,495],120356:[496,14,462,27,466],120357:[671,14,510,24,554],120358:[496,14,516,35,490],120359:[673,0,252,39,337],120360:[496,201,510,-18,505],120361:[671,0,516,20,484],120362:[650,0,203,19,243],120363:[650,201,192,-115,232],120364:[671,0,458,8,492],120365:[671,0,203,18,238],120366:[496,0,771,21,740],120367:[496,0,516,20,484],120368:[496,14,516,31,486],120369:[496,196,510,-43,490],120370:[496,196,513,22,514],120371:[496,0,304,19,357],120372:[496,14,463,13,433],120373:[615,6,257,50,295],120374:[482,14,516,40,504],120375:[482,0,462,72,510],120376:[482,0,666,68,714],120377:[482,0,455,-33,488],120378:[482,201,453,-42,493],120379:[482,0,458,-19,465],120380:[671,0,654,26,694],120381:[671,0,664,82,698],120382:[682,11,658,103,715],120383:[671,0,662,77,704],120384:[671,0,612,79,696],120385:[671,0,560,74,678],120386:[682,11,711,103,742],120387:[671,0,663,68,741],120388:[671,0,263,63,341],120389:[671,11,510,59,583],120390:[671,0,658,74,768],120391:[671,0,561,80,553],120392:[671,0,762,66,848],120393:[671,0,663,68,745],120394:[682,11,708,101,753],120395:[671,0,611,76,682],120396:[682,40,708,104,757],120397:[671,0,662,80,718],120398:[682,11,607,71,654],120399:[671,0,553,132,683],120400:[671,11,664,117,739],120401:[671,0,605,167,728],120402:[671,0,852,156,983],120403:[671,0,604,22,725],120404:[671,0,605,170,731],120405:[671,0,556,30,666],120406:[505,8,506,52,525],120407:[671,8,559,59,582],120408:[505,8,507,77,542],120409:[671,8,559,74,636],120410:[505,8,506,65,534],120411:[671,0,303,82,421],120412:[505,201,560,29,596],120413:[671,0,564,67,571],120414:[685,0,264,67,341],120415:[685,201,257,-38,335],120416:[671,0,507,59,593],120417:[671,0,264,67,336],120418:[505,0,813,60,828],120419:[505,0,563,63,575],120420:[505,8,558,79,577],120421:[505,201,559,15,584],120422:[505,201,559,72,598],120423:[505,0,358,63,446],120424:[505,8,507,59,533],120425:[620,4,304,95,376],120426:[497,8,563,87,599],120427:[497,0,504,120,590],120428:[497,0,702,111,796],120429:[497,0,504,16,585],120430:[497,201,503,36,591],120431:[497,0,455,21,521],120662:[671,0,769,80,689],120663:[671,0,686,80,606],120664:[671,0,621,80,541],120665:[650,0,829,80,749],120666:[671,0,650,80,570],120667:[671,0,653,80,573],120668:[671,0,690,80,610],120669:[682,11,792,80,712],120670:[671,0,295,80,215],120671:[671,0,739,80,659],120672:[671,0,769,80,689],120673:[671,0,799,80,719],120674:[671,0,694,80,614],120675:[671,0,675,80,595],120676:[682,11,792,80,712],120677:[671,0,690,80,610],120678:[671,0,661,80,581],120679:[682,11,792,80,712],120680:[671,0,648,80,568],120681:[671,0,686,80,606],120682:[671,0,721,80,641],120683:[671,0,758,80,678],120684:[671,0,728,80,648],120685:[671,0,721,80,641],120686:[682,0,770,80,690],120687:[650,0,829,80,749],120688:[505,10,762,80,682],120689:[671,202,637,80,557],120690:[497,230,718,80,638],120691:[666,10,574,80,494],120692:[505,9,565,80,485],120693:[677,194,603,80,523],120694:[505,202,615,80,535],120695:[664,10,578,80,498],120696:[497,6,371,80,291],120697:[497,0,631,80,551],120698:[673,0,731,80,651],120699:[497,202,663,80,583],120700:[497,0,653,80,573],120701:[677,194,603,80,523],120702:[505,8,641,80,561],120703:[497,6,759,80,679],120704:[493,202,635,80,555],120705:[503,194,592,80,512],120706:[524,8,711,80,631],120707:[497,6,619,80,539],120708:[483,10,622,80,542],120709:[493,212,760,80,680],120710:[497,212,750,79,670],120711:[661,212,720,80,640],120712:[497,-4,762,80,682],120713:[671,11,585,80,505],120714:[505,8,542,80,462],120715:[671,10,615,80,535],120716:[507,10,676,80,596],120717:[671,212,729,80,649],120718:[493,202,615,80,535],120719:[608,-4,764,80,684],120720:[671,0,769,80,689],120721:[671,0,776,80,696],120722:[671,0,764,80,684],120723:[650,0,829,80,749],120724:[671,0,777,80,697],120725:[671,0,796,80,716],120726:[671,0,833,80,753],120727:[682,11,809,80,729],120728:[671,0,438,80,358],120729:[671,0,854,80,774],120730:[671,0,769,80,689],120731:[671,0,942,80,862],120732:[671,0,837,80,757],120733:[671,0,818,80,738],120734:[682,11,809,80,729],120735:[671,0,833,80,753],120736:[671,0,766,80,686],120737:[682,11,809,80,729],120738:[671,0,791,80,711],120739:[671,0,711,80,631],120740:[671,0,721,80,641],120741:[671,0,768,80,688],120742:[671,0,863,80,783],120743:[671,0,779,80,699],120744:[682,0,847,80,767],120745:[640,10,829,142,811],120746:[505,10,823,80,743],120747:[671,202,751,80,671],120748:[497,230,718,80,638],120749:[666,10,663,80,583],120750:[505,9,612,80,532],120751:[677,194,668,80,588],120752:[505,202,672,80,592],120753:[664,10,620,80,540],120754:[497,6,365,80,285],120755:[497,0,694,80,614],120756:[673,0,731,80,651],120757:[497,202,767,80,687],120758:[497,0,637,80,557],120759:[677,194,653,80,573],120760:[505,8,658,80,578],120761:[497,6,788,80,708],120762:[493,202,738,80,658],120763:[503,194,607,80,527],120764:[524,8,779,80,699],120765:[497,6,648,80,568],120766:[483,10,629,80,549],120767:[493,212,775,80,695],120768:[497,212,822,80,742],120769:[661,212,756,80,676],120770:[497,-4,778,80,698],120771:[671,11,624,80,544],120772:[505,8,594,80,514],120773:[671,10,661,80,581],120774:[507,10,758,80,678],120775:[671,212,742,80,662],120776:[493,202,658,80,578],120777:[603,-4,786,80,706],120802:[652,14,519,43,470],120803:[652,0,536,102,327],120804:[652,0,518,34,473],120805:[652,14,518,32,468],120806:[652,0,517,28,481],120807:[638,14,518,35,475],120808:[652,14,518,43,475],120809:[638,0,518,46,482],120810:[652,14,518,37,475],120811:[652,14,518,38,471],120812:[666,8,507,29,468],120813:[652,0,525,68,347],120814:[666,0,507,30,466],120815:[666,8,507,29,467],120816:[652,0,506,24,472],120817:[652,8,507,27,468],120818:[666,8,507,32,470],120819:[652,0,506,29,478],120820:[666,8,506,22,475],120821:[666,8,507,28,467]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"GyreTermesMathJax_SansSerif"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/SansSerif/Regular/Main.js"]);
