﻿@model ADDI11MyRunLogViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);

    if (AppMode || (Model?.WhereIsColorboxForUser ?? false))
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
}
@Html.HiddenFor(m => m.OrdercColumn)
@Html.HiddenFor(m => m.SyntaxName)
@if (AppMode == false)
{
    @Html.Partial("_Title_Secondary")
}
@Html.Partial("_Notice")

@{
    if ((Model?.WhereIsColorboxForUser ?? false) == false)
    {
        Html.RenderAction("_RunMenuIstory", new { NowAction = "RunCountLogIstory" });
    }
}

@using (Html.BeginForm("RunCountLogIstory", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1" }))
{
    @Html.AntiForgeryToken()

    <div id="PageContent">
        @Html.Action("_PageRunCountLog", (string)ViewBag.BRE_NO)
    </div>
}

@section scripts{
    <script>
        var targetFromID = "#form1";
        $(document).ready(function () {

            $(".progress").colorbox({ opacity: 0.82, width: "70%", innerHeight: "500px", scrolling: true });
        });
        function searchFriendRunLog(userno,schoolno,classno) {

            $("#@(Html.IdFor(m=>m.WhereSCHOOL_NO))").val(schoolno);

            $(targetFromID).submit();
        }
        function funAjax() {
        
       
        $.ajax({
            type: "GET",
                url: '@(Url.Action("_PageRunCountLog", (string)ViewBag.BRE_NO))',
            data: {
             
                OrdercColumn: $('#OrdercColumn').val(),
                SyntaxName: $('#SyntaxName').val(),
               
                WhereSCHOOL_NO:$('#WhereSCHOOL_NO').val(),
            },
                async: false,
                cache: false,
                dataType: 'html',
            success: function (data) {
              
                $("#form1").html('');
               
                $("#form1").html(data);
                }
            });
    }
        function doSort(SortCol) {
            var sort = "";
            sort = SortCol;
            OrderByName = $('#OrdercColumn').val();
            SyntaxName = $('#SyntaxName').val();
            $('#OrdercColumn').val(SortCol);
      
            if (OrderByName == SortCol) {

                if (SyntaxName == "Desc") {

                    $('#OrdercColumn').val(sort);
                    $('#SyntaxName').val("ASC");
                }
                else {

                    $('#OrdercColumn').val(sort);
                    $('#SyntaxName').val("Desc");
                }
            }
            else {

                $('#OrdercColumn').val(sort);
                $('#SyntaxName').val("Desc");
            }
            funAjax()
        }
    </script>
}