﻿using Dapper;
using ECOOL_APP.EF;
using MvcPaging;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Web.Mvc;
using ECOOL_APP.com.ecool.util;
using DotNet.Highcharts;
using DotNet.Highcharts.Enums;
using DotNet.Highcharts.Options;
using DotNet.Highcharts.Helpers;
using System.Data.Entity;
using System.Transactions;
using System.Data;
using log4net;

namespace ECOOL_APP.com.ecool.service
{
    public class AWAI07Service
    {
        // 提早解約打八折
        public static readonly double ReleaseDiscount = 0.8;
        private static ILog logger = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        public AWAI07IndexViewModel GetListData(AWAI07IndexViewModel model, ref ECOOL_DEVEntities db)
        {
            string sSQL = @"SELECT A.*,B.SHORT_NAME AS SCHOOL_NAME
                           , C.NAME AS USER_NAME,D.PERIOD_DESC
                           FROM AWAT10 A(NOLOCK)
                           JOIN AWAT11 D (NOLOCK) ON A.PERIOD_TYPE=D.PERIOD_TYPE
                           JOIN BDMT01 B(NOLOCK) ON A.SCHOOL_NO = B.SCHOOL_NO
                           LEFT OUTER JOIN HRMT01 C (NOLOCK) ON A.SCHOOL_NO = C.SCHOOL_NO AND A.USER_NO = C.USER_NO
                           WHERE 1 = 1
                           and A.SCHOOL_NO = @SCHOOL_NO
                           and A.USER_NO = @USER_NO
                           ORDER BY A.SCHOOL_NO,A.USER_NO,A.STATUS,A.PERIOD_DATES DESC";
            var QTemp = db.Database.Connection.Query<AWAI07ListViewModel>(sSQL
                , new
                {
                    SCHOOL_NO = model.Search.WhereSCHOOL_NO,
                    USER_NO = model.Search.WhereUSER_NO,
                });

            if (!string.IsNullOrWhiteSpace(model.Search.WhereSTATUS))
            {
                QTemp = QTemp.Where(a => a.STATUS == model.Search.WhereSTATUS);
            }

            model.ListData = QTemp.ToPagedList(model.Search.Page > 0 ? model.Search.Page - 1 : 0, model.PageSize);

            model.Time_Deposit = db.AWAT10.Where(a => a.SCHOOL_NO == model.Search.WhereSCHOOL_NO && a.USER_NO == model.Search.WhereUSER_NO
                                                 && a.STATUS == AWAT10.StatusVal.SetUp).Select(a => a.AMT).Sum() ?? 0;

            HRMT01 H01 = db.HRMT01.Where(a => a.SCHOOL_NO == model.Search.WhereSCHOOL_NO && a.USER_NO == model.Search.WhereUSER_NO).FirstOrDefault();

            if (H01?.USER_TYPE == UserType.Student)
            {
                model.Demand_Deposit = db.AWAT01.Where(a => a.SCHOOL_NO == H01.SCHOOL_NO && a.USER_NO == H01.USER_NO).Select(a => a.CASH_AVAILABLE).FirstOrDefault() ?? 0;
            }
            else
            {
                model.Demand_Deposit = db.AWAT08.Where(a => a.SCHOOL_NO == H01.SCHOOL_NO && a.USER_NO == H01.USER_NO).Select(a => a.CASH_AVAILABLE).FirstOrDefault() ?? 0;
            }

            return model;
        }

        public AWAI07EditViewModel GetEditData(AWAI07EditViewModel model, ref ECOOL_DEVEntities db)
        {
            model.Edit = (from a in db.AWAT10
                          where a.A_NO == model.Search.WhereA_NO
                          select new AWAI07EditDataViewModel()
                          {
                              A_NO = a.A_NO,
                              SCHOOL_NO = a.SCHOOL_NO,
                              USER_NO = a.USER_NO,
                              ACCT_CODE = a.ACCT_CODE,
                              BANK_DATE = a.BANK_DATE,
                              PERIOD_TYPE = a.PERIOD_TYPE,
                              INTEREST_RATE = a.INTEREST_RATE,
                              INTEREST_AMT = a.INTEREST_AMT,
                              PERIOD_DATES = a.PERIOD_DATES,
                              PERIOD_DATEE = a.PERIOD_DATEE,
                              STATUS = a.STATUS,
                              CHG_PERSON = a.CHG_PERSON,
                              CHG_DATE = a.CHG_DATE,
                              CLOSE_DATE = a.CLOSE_DATE,
                              CLOSE_RATE = a.CLOSE_RATE,
                              AMT = a.AMT,
                              MATURITY_TYPE = a.MATURITY_TYPE,
                              PrincipleAndInterestAmt = a.AMT + a.INTEREST_AMT,
                              ClosePrincipleAndInterestAmt = a.AMT + a.INTEREST_AMT,
                          }
                           ).FirstOrDefault();

            return model;
        }

        public bool SaveData(AWAI07EditViewModel data, UserProfile User, ref ECOOL_DEVEntities db, ref string Message, ref List<Tuple<string, string, int>> valuesList)
        {
            AWAT10 SaveUp = null;

            SaveUp = db.AWAT10.Where(a => a.A_NO == data.Search.WhereA_NO).FirstOrDefault();

            if (SaveUp != null)
            {
                SaveUp.ACCT_CODE = data.Edit.ACCT_CODE;
                SaveUp.PERIOD_TYPE = data.Edit.PERIOD_TYPE;
                SaveUp.INTEREST_RATE = db.AWAT11.Where(a => a.PERIOD_TYPE == (int)(SaveUp.PERIOD_TYPE)).Select(a => a.PERIOD_RATE).FirstOrDefault();
                SaveUp.PERIOD_DATES = DateTime.ParseExact(DateTime.Now.ToString("yyyy/MM/dd 00:00:00"), "yyyy/MM/dd hh:mm:ss", CultureInfo.InvariantCulture);
                SaveUp.PERIOD_DATEE = SaveUp.PERIOD_DATES.Value.AddMonths((int)SaveUp.PERIOD_TYPE);
                SaveUp.CHG_PERSON = User.USER_KEY;
                SaveUp.CHG_DATE = DateTime.Now;
                SaveUp.AMT = data.Edit.AMT;
                SaveUp.MATURITY_TYPE = data.Edit.MATURITY_TYPE;

                if (data.IsTemp)
                {
                    SaveUp.STATUS = AWAT10.StatusVal.NotStarted;
                }
                else
                {
                    SaveUp.STATUS = AWAT10.StatusVal.SetUp;
                }
            }
            else
            {
                SaveUp = new AWAT10();

                SaveUp.A_NO = Guid.NewGuid().ToString("N");
                SaveUp.SCHOOL_NO = User.SCHOOL_NO;
                SaveUp.USER_NO = User.USER_NO;
                SaveUp.ACCT_CODE = data.Edit.ACCT_CODE;
                SaveUp.BANK_DATE = DateTime.Now;
                SaveUp.PERIOD_TYPE = data.Edit.PERIOD_TYPE;
                SaveUp.INTEREST_RATE = db.AWAT11.Where(a => a.PERIOD_TYPE == (int)(SaveUp.PERIOD_TYPE)).Select(a => a.PERIOD_RATE).FirstOrDefault();
                SaveUp.PERIOD_DATES = DateTime.ParseExact(DateTime.Now.ToString("yyyy/MM/dd 00:00:00"), "yyyy/MM/dd hh:mm:ss", CultureInfo.InvariantCulture);
                SaveUp.PERIOD_DATEE = SaveUp.PERIOD_DATES.Value.AddMonths((int)SaveUp.PERIOD_TYPE);

                if (data.IsTemp)
                {
                    SaveUp.STATUS = AWAT10.StatusVal.NotStarted;
                }
                else
                {
                    SaveUp.STATUS = AWAT10.StatusVal.SetUp;
                }

                SaveUp.CHG_PERSON = User.USER_KEY;
                SaveUp.CHG_DATE = DateTime.Now;
                SaveUp.AMT = data.Edit.AMT;
                SaveUp.MATURITY_TYPE = data.Edit.MATURITY_TYPE;

                if (SaveUp.ACCT_CODE == AWAT10.AcctCodeVal.Round_Amount_Savings)
                {
                    double temp = Convert.ToDouble(SaveUp.INTEREST_RATE / 12);
                    SaveUp.INTEREST_AMT = (Convert.ToDecimal(CompoundInterest(Convert.ToDouble(SaveUp.INTEREST_RATE / 12), Convert.ToDouble(SaveUp.AMT), Convert.ToInt32(SaveUp.PERIOD_TYPE))) - SaveUp.AMT);
                }
                else
                {
                    double temp = Convert.ToDouble(SaveUp.INTEREST_RATE / 12);
                    SaveUp.INTEREST_AMT = (Convert.ToDecimal(SimpleInterest(Convert.ToDouble(SaveUp.INTEREST_RATE / 12), Convert.ToDouble(SaveUp.AMT), Convert.ToInt32(SaveUp.PERIOD_TYPE))) - SaveUp.AMT);
                }

                db.AWAT10.Add(SaveUp);
            }

            if (SaveUp.STATUS == AWAT10.StatusVal.SetUp)
            {
                if (User.USER_TYPE == UserType.Teacher)
                {
                    ECOOL_APP.CashHelper.TeachAddCash(User, Convert.ToInt32(SaveUp.AMT * -1), User.SCHOOL_NO, User.USER_NO, "AWAT10", SaveUp.A_NO.ToString(), "銀行定存", false, null, ref db);
                }
                else
                {
                    ECOOL_APP.CashHelper.AddCash(User, Convert.ToInt32(SaveUp.AMT * -1), User.SCHOOL_NO, User.USER_NO, "AWAT10", SaveUp.A_NO.ToString(), "銀行定存", false, ref db,"", "",ref valuesList);
                }
            }

            try
            {
                db.SaveChanges();
                return true;
            }
            catch (Exception ex)
            {
                Message = "系統發生錯誤;原因:" + ex.Message;
                return false;
            }
        }

        public bool DelData(AWAI07EditViewModel data, UserProfile User, ref ECOOL_DEVEntities db, ref string Message)
        {
            AWAT10 SaveUp = null;

            SaveUp = db.AWAT10.Where(a => a.A_NO == data.Search.WhereA_NO).FirstOrDefault();

            if (SaveUp == null)
            {
                Message = "系統發生錯誤;原因:找不到此筆定存單";
                return false;
            }

            if (SaveUp.STATUS != AWAT10.StatusVal.NotStarted)
            {
                Message = "系統發生錯誤;原因:此筆定存單目前狀態無法作廢";
                return false;
            }

            SaveUp.STATUS = AWAT10.StatusVal.Disabled;
            SaveUp.CHG_PERSON = User.USER_KEY;
            SaveUp.CHG_DATE = DateTime.Now;

            try
            {
                db.SaveChanges();
                return true;
            }
            catch (Exception ex)
            {
                Message = "系統發生錯誤;原因:" + ex.Message;
                return false;
            }
        }
        public bool CloseSaveDataTranse(AWAI07EditViewModel data, UserProfile User, ref ECOOL_DEVEntities db, ref string Message, ref List<Tuple<string, string, int>> valuesList ,string school_no) {
            List<AWAT10> aWAT10s = new List<AWAT10>();
            //1.撈出學校中和約有成立的定存
            aWAT10s = TakeBankList(school_no);
            foreach (var item in aWAT10s) {
                AWAT10 SaveUp = null;

                SaveUp = db.AWAT10.Where(a => a.A_NO == item.A_NO).FirstOrDefault();
                AWAT10_HIS aWAT10_HIS = new AWAT10_HIS();
                SaveUp.STATUS = AWAT10.StatusVal.Terminate;
                SaveUp.CHG_PERSON = User.USER_KEY;
                SaveUp.CHG_DATE = DateTime.Now;
                SaveUp.CLOSE_DATE = DateTime.Now;
                decimal? AddCash;
                double CloseRate;
                bool OK = GetClosePrincipleAndInterestTran(item.A_NO, ref db, ref Message, out CloseRate, out AddCash);
                if (OK)
                {
                    SaveUp.CLOSE_RATE = Convert.ToDecimal(CloseRate);
                    SaveUp.INTEREST_AMT = AddCash - item.AMT;
                    var Ht01 = db.HRMT01.Where(a => a.SCHOOL_NO == item.SCHOOL_NO && a.USER_NO == item.USER_NO).FirstOrDefault();
                    if (Ht01.USER_TYPE == UserType.Teacher)
                    {
                        ECOOL_APP.CashHelper.TeachAddCash(User, Convert.ToInt32(AddCash), Ht01.SCHOOL_NO, Ht01.USER_NO, "AWAT10", item.A_NO.ToString(), $"銀行定存結清解約，本金{item.AMT.Value.ToString("#,0")}+利息{item.INTEREST_AMT.Value.ToString("#,0")}", false, null, ref db);
                    }
                    else
                    {
                        ECOOL_APP.CashHelper.AddCash(User, Convert.ToInt32(AddCash), Ht01.SCHOOL_NO, Ht01.USER_NO, "AWAT10", item.A_NO.ToString(), $"銀行定存結清解約，本金{ item.AMT.Value.ToString("#,0")}+利息{ item.INTEREST_AMT.Value.ToString("#,0")}", false, ref db, "", "", ref valuesList);
                    }
                    aWAT10_HIS.A_NO_ID = Guid.NewGuid().ToString("N");
                    aWAT10_HIS.A_NO = item.A_NO;
                    aWAT10_HIS.SCHOOL_NO = item.SCHOOL_NO;
                    aWAT10_HIS.USER_NO = item.USER_NO;
                    aWAT10_HIS.ACCT_CODE = item.ACCT_CODE;
                    aWAT10_HIS.BANK_DATE = item.BANK_DATE;
                    aWAT10_HIS.PERIOD_TYPE = item.PERIOD_TYPE;
                    aWAT10_HIS.INTEREST_RATE = item.INTEREST_RATE;
                    aWAT10_HIS.PERIOD_DATES = item.PERIOD_DATES;
                    aWAT10_HIS.PERIOD_DATEE = item.PERIOD_DATEE;
                    aWAT10_HIS.STATUS = item.STATUS;
                    aWAT10_HIS.CHG_PERSON = User.USER_NO;
                    aWAT10_HIS.CHG_DATE = DateTime.Now;
                    aWAT10_HIS.CLOSE_DATE = item.CLOSE_DATE;
                    aWAT10_HIS.CLOSE_RATE = item.CLOSE_RATE;
                    aWAT10_HIS.AMT = item.AMT;
                    aWAT10_HIS.MATURITY_TYPE = item.MATURITY_TYPE;
                    aWAT10_HIS.INTEREST_AMT = item.INTEREST_AMT;
                    aWAT10_HIS.LOG_TIME = DateTime.Now;
                    aWAT10_HIS.LOG_Person = User.USER_NO;
                    aWAT10_HIS.ISSUECESS = true;
                }
                else {
                    //history
                    aWAT10_HIS.A_NO_ID = Guid.NewGuid().ToString("N");
                    aWAT10_HIS.A_NO = item.A_NO;
                    aWAT10_HIS.SCHOOL_NO = item.SCHOOL_NO;
                    aWAT10_HIS.USER_NO = item.USER_NO;
                    aWAT10_HIS.ACCT_CODE = item.ACCT_CODE;
                    aWAT10_HIS.BANK_DATE = item.BANK_DATE;
                    aWAT10_HIS.PERIOD_TYPE = item.PERIOD_TYPE;
                    aWAT10_HIS.INTEREST_RATE = item.INTEREST_RATE;
                    aWAT10_HIS.PERIOD_DATES = item.PERIOD_DATES;
                    aWAT10_HIS.PERIOD_DATEE = item.PERIOD_DATEE;
                    aWAT10_HIS.STATUS = item.STATUS;
                    aWAT10_HIS.CHG_PERSON = User.USER_NO;
                    aWAT10_HIS.CHG_DATE = DateTime.Now;
                    aWAT10_HIS.CLOSE_DATE = item.CLOSE_DATE;
                    aWAT10_HIS.CLOSE_RATE = item.CLOSE_RATE;
                    aWAT10_HIS.AMT = item.AMT;
                    aWAT10_HIS.MATURITY_TYPE = item.MATURITY_TYPE;
                    aWAT10_HIS.INTEREST_AMT = item.INTEREST_AMT;
                    aWAT10_HIS.LOG_TIME = DateTime.Now;
                    aWAT10_HIS.LOG_Person = User.USER_NO;
                    aWAT10_HIS.ISSUECESS = false;
                    continue;
                }
                try
                {
                    db.AWAT10_HIS.Add(aWAT10_HIS);
                    db.SaveChanges();
                    continue; 
                }
                catch (Exception ex)
                {
                    Message = "系統發生錯誤;原因:" + ex.Message;
                    continue;
                }

            }
            return true;
        }
        public bool CloseSaveData(AWAI07EditViewModel data, UserProfile User, ref ECOOL_DEVEntities db, ref string Message ,ref List<Tuple<string, string, int>> valuesList)
        {
            AWAT10 SaveUp = null;

            SaveUp = db.AWAT10.Where(a => a.A_NO == data.Search.WhereA_NO).FirstOrDefault();

            if (SaveUp == null)
            {
                Message = "系統發生錯誤;原因:找不到此筆定存單";
                return false;
            }

            if (SaveUp.STATUS != AWAT10.StatusVal.SetUp)
            {
                Message = "系統發生錯誤;原因:此筆定存單目前狀態無法解約";
                return false;
            }

            SaveUp.STATUS = AWAT10.StatusVal.Terminate;
            SaveUp.CHG_PERSON = User.USER_KEY;
            SaveUp.CHG_DATE = DateTime.Now;
            SaveUp.CLOSE_DATE = DateTime.Now;

            decimal? AddCash;
            double CloseRate;

            bool OK = GetClosePrincipleAndInterest(SaveUp.A_NO, ref db, ref Message, out CloseRate, out AddCash);

            if (OK)
            {
                SaveUp.CLOSE_RATE = Convert.ToDecimal(CloseRate);
                SaveUp.INTEREST_AMT = AddCash - SaveUp.AMT;

                var Ht01 = db.HRMT01.Where(a => a.SCHOOL_NO == SaveUp.SCHOOL_NO && a.USER_NO == SaveUp.USER_NO).FirstOrDefault();

                if (Ht01.USER_TYPE == UserType.Teacher)
                {
                    ECOOL_APP.CashHelper.TeachAddCash(User, Convert.ToInt32(AddCash), Ht01.SCHOOL_NO, Ht01.USER_NO, "AWAT10", SaveUp.A_NO.ToString(), $"銀行定存解約，本金{SaveUp.AMT.Value.ToString("#,0")}+利息{SaveUp.INTEREST_AMT.Value.ToString("#,0")}", false, null, ref db);
                }
                else
                {
                    ECOOL_APP.CashHelper.AddCash(User, Convert.ToInt32(AddCash), Ht01.SCHOOL_NO, Ht01.USER_NO, "AWAT10", SaveUp.A_NO.ToString(), $"銀行定存解約，本金{ SaveUp.AMT.Value.ToString("#,0")}+利息{ SaveUp.INTEREST_AMT.Value.ToString("#,0")}", false, ref db,"", "",ref valuesList);
                }
            }
            else
            {
                return false;
            }

            try
            {
                db.SaveChanges();
                return true;
            }
            catch (Exception ex)
            {
                Message = "系統發生錯誤;原因:" + ex.Message;
                return false;
            }
        }

        /// <summary>
        /// 到期後本利和
        /// </summary>
        /// <param name="ACCT_CODE"></param>
        /// <param name="PERIOD_TYPE"></param>
        /// <param name="AMT"></param>
        /// <param name="db"></param>
        /// <param name="Message"></param>
        /// <param name="PrincipleAndInterestAmt"></param>
        /// <returns></returns>
        public bool GetPrincipleAndInterest(byte? MATURITY_TYPE, byte? ACCT_CODE, byte PERIOD_TYPE, decimal AMT,
            DateTime StartDate, DateTime TestCloseDate,bool TransCash, ref ECOOL_DEVEntities db, ref string Message, out double CloseRate,
            out decimal? PrincipleAndInterestAmt, out string MathJaxFunction)
        {
            MathJaxFunction = "";
            try
            {
                DateTime dtStart = StartDate;
                DateTime dtEnd = TestCloseDate;

                // 計算時間差 item1: 年 , item2: 月
                var result = dtEnd.TimespanToDate(dtStart);

                int iMonths = (result.Item1 * 12) + result.Item2;

                if (iMonths > 0)
                {
                    // 查現時利率表 => 合約利率(按月)最高不超過合約簽約時(期別)的利率
                    var queryRateList = db.AWAT11.ToList();
                    var dtAWAT11 = queryRateList
                        .Where(a => iMonths < PERIOD_TYPE ? (a.PERIOD_TYPE <= iMonths) : a.PERIOD_TYPE <= PERIOD_TYPE)
                        .OrderByDescending(a => a.PERIOD_TYPE).FirstOrDefault();

                    if (dtAWAT11 == null)
                    {
                        PrincipleAndInterestAmt = AMT;
                        CloseRate = 0;
                        return false;
                    }

                    CloseRate = Convert.ToDouble(dtAWAT11.PERIOD_RATE);
                    double amt = Convert.ToDouble(AMT);
                    double interest = 0;

                    if ((int)MATURITY_TYPE == 1)
                    {
                        Tuple<double, string> caculate_result;
                        if (ACCT_CODE == AWAT10.AcctCodeVal.Round_Amount_Savings)
                        {
                            caculate_result = CompoundInterest_ForSimulator(Convert.ToDouble(CloseRate), amt, iMonths, PERIOD_TYPE, queryRateList, TransCash);
                            interest = caculate_result.Item1;
                        }
                        else
                        {
                            caculate_result = SimpleInterest_ForSimulator(Convert.ToDouble(CloseRate), amt, iMonths, PERIOD_TYPE, queryRateList, TransCash);

                            interest = caculate_result.Item1;
                        }
                        MathJaxFunction = caculate_result.Item2;
                    }
                    else //MATURITY_TYPE == 2
                    {
                        Tuple<double, string> caculate_result;
                        caculate_result = SimpleInterest_ForSimulator(Convert.ToDouble(CloseRate), amt, iMonths, PERIOD_TYPE, queryRateList, TransCash);
                        interest = caculate_result.Item1;
                        MathJaxFunction = caculate_result.Item2;
                    }

                    PrincipleAndInterestAmt = Convert.ToDecimal(interest);
                }
                else // 未滿一個月退回原本金
                {
                    CloseRate = 0;
                    PrincipleAndInterestAmt = AMT;
                    MathJaxFunction = "存款未滿一個月，退還本金" + AMT;
                }

                return true;
            }
            catch (Exception ex)
            {
                PrincipleAndInterestAmt = 0;
                CloseRate = 0;
                Message = "系統發生錯誤;原因:" + ex.Message;
                return false;
            }
        }

        public bool GetCloseTransPrincipleAndInterest(string A_NO, ref ECOOL_DEVEntities db, ref string Message, out double CloseRate,
           out decimal? PrincipleAndInterestAmt)
        {
            try {

                var dtT10 = db.AWAT10.Where(a => a.A_NO == A_NO).FirstOrDefault();

                if (dtT10 == null)
                {
                    Message = $"系統發生錯誤;原因:找不到此筆定存單{A_NO}";
                    PrincipleAndInterestAmt = 0;
                    CloseRate = 0;
                    return false;
                }
                string MaxJaxFunction;
                return GetPrincipleAndInterest((byte?)dtT10.MATURITY_TYPE, (byte?)dtT10.ACCT_CODE, (byte)dtT10.PERIOD_TYPE,
                    (decimal)dtT10.AMT, (DateTime)dtT10.PERIOD_DATES, DateTime.Now, true, ref db, ref Message, out CloseRate, out PrincipleAndInterestAmt, out MaxJaxFunction);
            }
            catch (Exception ex)
            {
                PrincipleAndInterestAmt = 0;
                CloseRate = 0;
                Message = "系統發生錯誤;原因:" + ex.Message;
                return false;
            }

        }
        public bool GetClosePrincipleAndInterestTran(string A_NO, ref ECOOL_DEVEntities db, ref string Message, out double CloseRate,
         out decimal? PrincipleAndInterestAmt)
        {
            try
            {
                var dtT10 = db.AWAT10.Where(a => a.A_NO == A_NO).FirstOrDefault();

                if (dtT10 == null)
                {
                    Message = $"系統發生錯誤;原因:找不到此筆定存單{A_NO}";
                    PrincipleAndInterestAmt = 0;
                    CloseRate = 0;
                    return false;
                }
                string MaxJaxFunction;
                return GetPrincipleAndInterest((byte?)dtT10.MATURITY_TYPE, (byte?)dtT10.ACCT_CODE, (byte)dtT10.PERIOD_TYPE,
                    (decimal)dtT10.AMT, (DateTime)dtT10.PERIOD_DATES, DateTime.Now, true, ref db, ref Message, out CloseRate, out PrincipleAndInterestAmt, out MaxJaxFunction);
            }
            catch (Exception ex)
            {
                PrincipleAndInterestAmt = 0;
                CloseRate = 0;
                Message = "系統發生錯誤;原因:" + ex.Message;
                return false;
            }

        }
            /// <summary>
            /// 使用者直接解約後本利和
            /// </summary>
            /// <param name="A_NO"></param>
            /// <param name="db"></param>
            /// <param name="Message"></param>
            /// <param name="PrincipleAndInterestAmt"></param>
            /// <returns></returns>
            public bool GetClosePrincipleAndInterest(string A_NO, ref ECOOL_DEVEntities db, ref string Message, out double CloseRate,
            out decimal? PrincipleAndInterestAmt)
        {
            try
            {
                var dtT10 = db.AWAT10.Where(a => a.A_NO == A_NO).FirstOrDefault();

                if (dtT10 == null)
                {
                    Message = $"系統發生錯誤;原因:找不到此筆定存單{A_NO}";
                    PrincipleAndInterestAmt = 0;
                    CloseRate = 0;
                    return false;
                }
                string MaxJaxFunction;
                return GetPrincipleAndInterest((byte?)dtT10.MATURITY_TYPE, (byte?)dtT10.ACCT_CODE, (byte)dtT10.PERIOD_TYPE,
                    (decimal)dtT10.AMT, (DateTime)dtT10.PERIOD_DATES, DateTime.Now,false, ref db, ref Message, out CloseRate, out PrincipleAndInterestAmt, out MaxJaxFunction);
            }
            catch (Exception ex)
            {
                PrincipleAndInterestAmt = 0;
                CloseRate = 0;
                Message = "系統發生錯誤;原因:" + ex.Message;
                return false;
            }
        }

        /// <summary>
        /// 單利
        /// </summary>
        /// <param name="rate">月利率</param>
        /// <param name="cash"></param>
        /// <param name="times"></param>
        /// <returns></returns>
        public double SimpleInterest(double rate, double cash, int times)
        {
            double ret = cash;
            double InterestAmt = 0;
            for (int i = 1; i <= times; i++)
            {
                InterestAmt = InterestAmt + (ret * (rate));
            }

            return Math.Round(ret + InterestAmt, 0);
        }

        /// <summary>
        /// 複利
        /// </summary>
        /// <param name="rate">月利率</param>
        /// <param name="cash"></param>
        /// <param name="times"></param>
        /// <returns></returns>
        public double CompoundInterest(double rate, double cash, int times)
        {
            double ret = cash;
            for (int i = 1; i <= times; i++)
            {
                ret = ret * (1 + rate);
            }

            return Math.Round(ret, 0);
        }

        /// <summary>
        /// 單利 - 用模擬器算
        /// </summary>
        public Tuple<double, string> SimpleInterest_ForSimulator(double year_rate, double cash, int times, int period, List<AWAT11> rateQueryList, bool transtype)
        {
            string mathJaxfunction = $@"本利和計算公式：<br><br> \( {Math.Round(cash, 0)} +"; // 組數學公式
            double ret = cash;

            // 週期次數
            int timesOfPeriod = times / period;
            // 剩餘月數
            int remainTimes = times % period;
            string year_rate_percentage = (year_rate * 100).ToString("F2");

            for (int i = 1; i <= timesOfPeriod; i++)
            {
                ret += cash * (year_rate / 12) * period;

                mathJaxfunction += $@"{cash} \times ( {{ {year_rate_percentage} \%  \over 12 }} \times {period} )";
                if (i < timesOfPeriod) mathJaxfunction += @"\\ + ";
            }
            // 剩下週期
            if (remainTimes != 0)
            {
                // 查利率表
                year_rate = Convert.ToDouble(rateQueryList
                    .Where(r => r.PERIOD_TYPE <= remainTimes)
                    .OrderByDescending(o => o.PERIOD_TYPE).FirstOrDefault()?
                    .PERIOD_RATE);
                year_rate_percentage = (year_rate * 100).ToString("F2");
                if (timesOfPeriod != 0) mathJaxfunction += @"\\ + ";
                if (!transtype)
                {



                    // 到期前提早解約利率要打折
                    ret += cash * (year_rate / 12) * remainTimes * ReleaseDiscount;

                    mathJaxfunction += $@"{cash} \times ( {{ {year_rate_percentage} \% \over 12 }} \times {remainTimes} \times 0.8 ) ";
                }
                else {
                    ret += cash * (year_rate / 12) * remainTimes;
                    mathJaxfunction += $@"{cash} \times ( {{ {year_rate_percentage} \% \over 12 }} \times {remainTimes} ) ";

                }

            
            }

            ret = Math.Round(ret, 0);
            mathJaxfunction += $@"\\ = {ret} \)";

            return Tuple.Create(ret, mathJaxfunction);
        }

        /// <summary>
        /// 複利 - 用模擬器算
        /// </summary>
        public Tuple<double, string> CompoundInterest_ForSimulator(double year_rate, double cash, int times, int period, List<AWAT11> rateQueryList,bool transtype)
        {
            string mathJaxfunction = $@"本利和計算公式：<br><br> \( {Math.Round(cash, 0)}  \times"; // 組數學公式
            double ret = cash;

            // 週期次數
            int timesOfPeriod = times / period;
            // 剩餘月數
            int remainTimes = times % period;
            string year_rate_percentage = (year_rate * 100).ToString("F2");

            for (int i = 1; i <= timesOfPeriod; i++)
            {
                ret *= Math.Pow(1 + (year_rate / 12), period);

                mathJaxfunction += $@" ( 1 + {{ {year_rate_percentage} \% \over 12 }} )^{{{period}}}";
                if (i < timesOfPeriod || remainTimes > 0) mathJaxfunction += @"\\ \times ";
            }
            // 剩下週期
            if (remainTimes != 0)
            {
                // 查利率表
                year_rate = Convert.ToDouble(rateQueryList
                    .Where(r => r.PERIOD_TYPE <= remainTimes)
                    .OrderByDescending(o => o.PERIOD_TYPE).FirstOrDefault()?
                    .PERIOD_RATE);
                year_rate_percentage = (year_rate * 100).ToString("F2");

              
                if (!transtype)
                {

                    // 到期前提早解約利率要打折
                    ret *= Math.Pow(1 + (year_rate / 12) * 0.8, remainTimes);


                    mathJaxfunction += $@" ( 1 + {{ {year_rate_percentage} \% \over 12 }} \times 0.8 )^{{{remainTimes}}}";
                }
                else {

                    ret *= Math.Pow(1 + (year_rate / 12), remainTimes);

                    mathJaxfunction += $@" ( 1 + {{ {year_rate_percentage} \% \over 12 }} )^{{{remainTimes}}}";
                }
            }

            ret = Math.Round(ret, 0);
            mathJaxfunction += $@"\\ = {ret} \)";

            return Tuple.Create(ret, mathJaxfunction);
        }

        public AWAI07DescViewModel GetDesc(string SCHOOL_NO, ref ECOOL_DEVEntities db)
        {
            AWAI07DescViewModel model = new AWAI07DescViewModel();
            model.AWAT10SEXPLAIN = db.BDMT01.Where(a => a.SCHOOL_NO == SCHOOL_NO).Select(a => a.AWAT10SEXPLAIN).FirstOrDefault();

            model.PeriodTypeList = db.AWAT11.OrderBy(a => a.PERIOD_TYPE).ToList();

            return model;
        }

        public List<SelectListItem> PeriodTypeItem(ref ECOOL_DEVEntities db, byte? SelectedVal = null)
        {
            List<SelectListItem> ThisSelectItem = new List<SelectListItem>();

            string SelectedValString = SelectedVal == null ? "" : SelectedVal.ToString();

            ThisSelectItem.Add(new SelectListItem() { Text = "請選擇..", Value = string.Empty, Selected = string.IsNullOrWhiteSpace(SelectedValString) });

            foreach (var item in db.AWAT11.OrderBy(a => a.PERIOD_TYPE).ToList())
            {
                ThisSelectItem.Add(new SelectListItem() { Text = $"{item.PERIOD_DESC}", Value = item.PERIOD_TYPE.ToString(), Selected = SelectedValString == item.ToString() });
            }

            return ThisSelectItem;
        }
        
        public List<AWAT10> TakeBankList(string school_no) {
            ECOOL_DEVEntities db = new ECOOL_DEVEntities();
            List<AWAT10> aWAT10s = new List<AWAT10>();
            string sSQL = $@"SELECT * FROM AWAT10 A WHERE A.STATUS='{AWAT10.StatusVal.SetUp}'  and SCHOOL_NO='{school_no}'";
            var T10List = db.Database.Connection.Query<AWAT10>(sSQL).ToList();
            if (db.Database.Connection.State != ConnectionState.Open) db.Database.Connection.Open();
            aWAT10s = (List<AWAT10>)T10List;
            return aWAT10s;
        }
        public void CheckAutoBankTask() {
            ECOOL_DEVEntities db = new ECOOL_DEVEntities();
            string sSQL = $@"SELECT * FROM AWAT10 A WHERE A.STATUS='{AWAT10.StatusVal.SetUp}' and A.PERIOD_DATEE<=getdate()";
            var T10List = db.Database.Connection.Query<AWAT10>(sSQL).ToList();
            foreach (var item in T10List) {
                AutoBankTask(item.SCHOOL_NO);
                System.Threading.Thread.Sleep(1000);

            }
        }
        public void AutoBankTask(string SCHOOL_NO)
        {
            ECOOL_DEVEntities db = new ECOOL_DEVEntities();
            List<Tuple<string, string, int>> valuesList = new List<Tuple<string, string, int>>();
            string sSQL = $@"SELECT * FROM AWAT10 A WHERE A.STATUS='{AWAT10.StatusVal.SetUp}' and A.PERIOD_DATEE<=getdate() and SCHOOL_NO='{SCHOOL_NO}'";
            var T10List = db.Database.Connection.Query<AWAT10>(sSQL).ToList();

            if (db.Database.Connection.State != ConnectionState.Open) db.Database.Connection.Open();

            using (TransactionScope tx = new TransactionScope())
            {
                foreach (var item in T10List)
                {
                    if (item.MATURITY_TYPE == AWAT10.MATURITY_TYPE_Val.Auto) //到期自動續存
                    {
                        AWAT10 Cre = new AWAT10();

                        Cre.A_NO = Guid.NewGuid().ToString("N");
                        Cre.SCHOOL_NO = item.SCHOOL_NO;
                        Cre.USER_NO = item.USER_NO;
                        Cre.ACCT_CODE = item.ACCT_CODE;
                        Cre.BANK_DATE = DateTime.Now;
                        Cre.PERIOD_TYPE = item.PERIOD_TYPE;
                        Cre.INTEREST_RATE = item.INTEREST_RATE;
                        Cre.PERIOD_DATES = DateTime.ParseExact(DateTime.Now.AddDays(1).ToString("yyyy/MM/dd 00:00:00"), "yyyy/MM/dd hh:mm:ss", CultureInfo.InvariantCulture);
                        Cre.PERIOD_DATEE = Cre.PERIOD_DATES.Value.AddMonths((int)Cre.PERIOD_TYPE);
                        Cre.STATUS = AWAT10.StatusVal.SetUp;
                        Cre.CHG_PERSON = "系統自動產生";
                        Cre.CHG_DATE = DateTime.Now;

                        Cre.MATURITY_TYPE = item.MATURITY_TYPE;

                        //整存整付 本息續存
                        if (item.ACCT_CODE == AWAT10.AcctCodeVal.Round_Amount_Savings)
                        {
                            Cre.AMT = item.AMT + item.INTEREST_AMT;

                            Cre.INTEREST_AMT = (Convert.ToDecimal(CompoundInterest(Convert.ToDouble(Cre.INTEREST_RATE / 12), Convert.ToDouble(Cre.AMT), Convert.ToInt32(Cre.PERIOD_TYPE))) - Cre.AMT);
                        }
                        //存本取息
                        else if (item.ACCT_CODE == AWAT10.AcctCodeVal.Withdrawals_of_interest_savings)
                        {
                            Cre.AMT = item.AMT;

                            Cre.INTEREST_AMT = (Convert.ToDecimal(SimpleInterest(Convert.ToDouble(Cre.INTEREST_RATE / 12), Convert.ToDouble(Cre.AMT), Convert.ToInt32(Cre.PERIOD_TYPE))) - Cre.AMT);

                            var Hr = db.HRMT01.Where(a => a.SCHOOL_NO == Cre.SCHOOL_NO && a.USER_NO == Cre.USER_NO).FirstOrDefault();

                            if (Hr?.USER_TYPE == UserType.Teacher)
                            {
                                ECOOL_APP.CashHelper.TeachAddCash(null, Convert.ToInt32(item.INTEREST_AMT), Cre.SCHOOL_NO, Cre.USER_NO, "AWAT10", item.A_NO.ToString(), "銀行定存到期自動續存-存本取息", false, null, ref db);
                            }
                            else
                            {
                                ECOOL_APP.CashHelper.AddCash(null, Convert.ToInt32(item.INTEREST_AMT), Cre.SCHOOL_NO, Cre.USER_NO, "AWAT10", item.A_NO.ToString(), "銀行定存到期自動續存-存本取息", false, ref db, "", "", ref valuesList);
                            }
                        }

                        db.AWAT10.Add(Cre);

                        item.STATUS = AWAT10.StatusVal.Expire;
                        db.Entry(item).State = System.Data.Entity.EntityState.Modified;
                    }
                    else if (item.MATURITY_TYPE == AWAT10.MATURITY_TYPE_Val.Close) //到期解約
                    {
                        var Hr = db.HRMT01.Where(a => a.SCHOOL_NO == item.SCHOOL_NO && a.USER_NO == item.USER_NO).FirstOrDefault();

                        if (Hr?.USER_TYPE == UserType.Teacher)
                        {
                            ECOOL_APP.CashHelper.TeachAddCash(null, Convert.ToInt32(item.AMT + item.INTEREST_AMT), item.SCHOOL_NO, item.USER_NO, "AWAT10", item.A_NO.ToString(), "銀行定存到期解約", false, null, ref db);
                        }
                        else
                        {
                            if (item.SCHOOL_NO == "363605" && item.USER_NO == "108063")
                            {
                                ECOOL_APP.CashHelper.AddCash(null, Convert.ToInt32(item.AMT + item.INTEREST_AMT), item.SCHOOL_NO, item.USER_NO, "AWAT10", item.A_NO.ToString(), "銀行定存到期解約", false, ref db, "", "", ref valuesList);


                            }
                            else
                            {

                                ECOOL_APP.CashHelper.AddCash(null, Convert.ToInt32(item.AMT + item.INTEREST_AMT), item.SCHOOL_NO, item.USER_NO, "AWAT10", item.A_NO.ToString(), "銀行定存到期解約", false, ref db, "", "", ref valuesList);


                            }

                        }

                        item.STATUS = AWAT10.StatusVal.Expire;
                        db.Entry(item).State = System.Data.Entity.EntityState.Modified;
                    }
                }
                try
                {
                    db.SaveChanges();
                }
                catch (Exception ex)
                {
                    logger.Error(ex);
                    throw;
                }

                tx.Complete();
            }

            if (db != null)
            {
                db.Dispose();
                db = null;
            }
        }


        public void AutoBankTask()
        {
            ECOOL_DEVEntities db = new ECOOL_DEVEntities();
            List<Tuple<string, string, int>> valuesList = new List<Tuple<string, string, int>>();
            string sSQL = $@"SELECT * FROM AWAT10 A WHERE A.STATUS='{AWAT10.StatusVal.SetUp}' and A.PERIOD_DATEE<=getdate()";
            var T10List = db.Database.Connection.Query<AWAT10>(sSQL).ToList();

            if (db.Database.Connection.State != ConnectionState.Open) db.Database.Connection.Open();

            using (TransactionScope tx = new TransactionScope())
            {
                foreach (var item in T10List)
                {
                    if (item.MATURITY_TYPE == AWAT10.MATURITY_TYPE_Val.Auto) //到期自動續存
                    {
                        AWAT10 Cre = new AWAT10();

                        Cre.A_NO = Guid.NewGuid().ToString("N");
                        Cre.SCHOOL_NO = item.SCHOOL_NO;
                        Cre.USER_NO = item.USER_NO;
                        Cre.ACCT_CODE = item.ACCT_CODE;
                        Cre.BANK_DATE = DateTime.Now;
                        Cre.PERIOD_TYPE = item.PERIOD_TYPE;
                        Cre.INTEREST_RATE = item.INTEREST_RATE;
                        Cre.PERIOD_DATES = DateTime.ParseExact(DateTime.Now.AddDays(1).ToString("yyyy/MM/dd 00:00:00"), "yyyy/MM/dd hh:mm:ss", CultureInfo.InvariantCulture);
                        Cre.PERIOD_DATEE = Cre.PERIOD_DATES.Value.AddMonths((int)Cre.PERIOD_TYPE);
                        Cre.STATUS = AWAT10.StatusVal.SetUp;
                        Cre.CHG_PERSON = "系統自動產生";
                        Cre.CHG_DATE = DateTime.Now;

                        Cre.MATURITY_TYPE = item.MATURITY_TYPE;

                        //整存整付 本息續存
                        if (item.ACCT_CODE == AWAT10.AcctCodeVal.Round_Amount_Savings)
                        {
                            Cre.AMT = item.AMT + item.INTEREST_AMT;

                            Cre.INTEREST_AMT = (Convert.ToDecimal(CompoundInterest(Convert.ToDouble(Cre.INTEREST_RATE / 12), Convert.ToDouble(Cre.AMT), Convert.ToInt32(Cre.PERIOD_TYPE))) - Cre.AMT);
                        }
                        //存本取息
                        else if (item.ACCT_CODE == AWAT10.AcctCodeVal.Withdrawals_of_interest_savings)
                        {
                            Cre.AMT = item.AMT;

                            Cre.INTEREST_AMT = (Convert.ToDecimal(SimpleInterest(Convert.ToDouble(Cre.INTEREST_RATE / 12), Convert.ToDouble(Cre.AMT), Convert.ToInt32(Cre.PERIOD_TYPE))) - Cre.AMT);

                            var Hr = db.HRMT01.Where(a => a.SCHOOL_NO == Cre.SCHOOL_NO && a.USER_NO == Cre.USER_NO).FirstOrDefault();

                            if (Hr?.USER_TYPE == UserType.Teacher)
                            {
                                ECOOL_APP.CashHelper.TeachAddCash(null, Convert.ToInt32(item.INTEREST_AMT), Cre.SCHOOL_NO, Cre.USER_NO, "AWAT10", item.A_NO.ToString(), "銀行定存到期自動續存-存本取息", false, null, ref db);
                            }
                            else
                            {
                                ECOOL_APP.CashHelper.AddCash(null, Convert.ToInt32(item.INTEREST_AMT), Cre.SCHOOL_NO, Cre.USER_NO, "AWAT10", item.A_NO.ToString(), "銀行定存到期自動續存-存本取息", false, ref db,"","",ref valuesList);
                            }
                        }

                        db.AWAT10.Add(Cre);

                        item.STATUS = AWAT10.StatusVal.Expire;
                        db.Entry(item).State = System.Data.Entity.EntityState.Modified;
                    }
                    else if (item.MATURITY_TYPE == AWAT10.MATURITY_TYPE_Val.Close) //到期解約
                    {
                        var Hr = db.HRMT01.Where(a => a.SCHOOL_NO == item.SCHOOL_NO && a.USER_NO == item.USER_NO).FirstOrDefault();

                        if (Hr?.USER_TYPE == UserType.Teacher)
                        {
                            ECOOL_APP.CashHelper.TeachAddCash(null, Convert.ToInt32(item.AMT + item.INTEREST_AMT), item.SCHOOL_NO, item.USER_NO, "AWAT10", item.A_NO.ToString(), "銀行定存到期解約", false, null, ref db);
                        }
                        else
                        {
                            if (item.SCHOOL_NO == "363605" && item.USER_NO == "108063")
                            {
                                ECOOL_APP.CashHelper.AddCash(null, Convert.ToInt32(item.AMT + item.INTEREST_AMT), item.SCHOOL_NO, item.USER_NO, "AWAT10", item.A_NO.ToString(), "銀行定存到期解約", false, ref db, "", "", ref valuesList);


                            }
                            else {

                                ECOOL_APP.CashHelper.AddCash(null, Convert.ToInt32(item.AMT + item.INTEREST_AMT), item.SCHOOL_NO, item.USER_NO, "AWAT10", item.A_NO.ToString(), "銀行定存到期解約", false, ref db, "", "", ref valuesList);


                            }

                        }

                        item.STATUS = AWAT10.StatusVal.Expire;
                        db.Entry(item).State = System.Data.Entity.EntityState.Modified;
                    }
                }
                try
                {
                    db.SaveChanges();
                }
                catch (Exception ex)
                {
                    logger.Error(ex);
                    throw;
                }

                tx.Complete();
            }

            if (db != null)
            {
                db.Dispose();
                db = null;
            }
        }
    }
}