﻿@model ECOOL_APP.com.ecool.Models.DTO.ADDI09EditViewModel
@using ECOOL_APP.com.ecool.Models.DTO;

<link href="~/Content/css/EzCss.css" rel="stylesheet" />
@{
    ViewBag.Title = ViewBag.Panel_Title;

}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")
@Html.ValidationMessage("ErrorMsg", new { @class = "text-danger" })
@if (ViewBag.Title == "快速大量加點 - 獎勵點數輸入")
{
    <label class="text-primary">
        說明:
    </label>
    <label class="text-primary">
      本功能是給級任導師使用，其他老師看不到學生名單
    </label><br />
    }
@Html.ActionLink("回「新增批次給點/扣點」首頁", (string)ViewBag.IndexActionName, null, new { @class = "btn btn-sm btn-sys", @role = "button" })

@using (Html.BeginForm("Edit", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", enctype = "multipart/form-data" }))
{


    if (!ViewData.ModelState.IsValid)
    {
        <div class="alert alert-danger">
            @Html.ValidationSummary()
        </div>
    }
    @Html.AntiForgeryToken()
    @Html.Hidden("SYS_TABLE_TYPE", (string)ViewBag.SYS_TABLE_TYPE)
    @Html.Hidden("Individual_Give", true)
    @Html.Hidden("DeleteTemp", false)


    @Html.ValidationSummary(true, "", new { @class = "text-danger" })
    <img src="~/Content/img/web-bar2-revise-22.png" style="width:100%" class="img-responsive " alt="Responsive image" />
    <div class="Div-EZ-ADDI09">
        <div class="form-horizontal">
            @Html.Action("_ViewQuickBulk1", (string)ViewBag.BRE_NO, Model)
        </div>
    </div>
    <div id="QuerySelectDataList">
        @Html.ValidationMessage("DataList", new { @class = "text-danger" })
        @Html.Action("_QuerySelectDataList", (string)ViewBag.BRE_NO, new { ShowType = "Edit" })
    </div>

    <div style="height:20px"></div>
    <div class="text-center">
        <button type="button" id="myButton" data-loading-text="Loading..." class="btn btn-default" autocomplete="off">
            確定給點
        </button>
    </div>

}
<script type="text/javascript">
    $('#myButton').on('click', function () {
        var $btn = $(this).button('loading....')
        form1.submit();
    });
    function CheckAll(obj) {
        var checkvalue = $(obj).is(":checked")
        console.log(checkvalue);
        if (checkvalue) {
            $("input[type='checkbox'][value='true']").each(function () { this.checked = true; });

        }
        else {
            $("input[type='checkbox'][value='true']").each(function () { this.checked = false; });
        }
    }
</script>
