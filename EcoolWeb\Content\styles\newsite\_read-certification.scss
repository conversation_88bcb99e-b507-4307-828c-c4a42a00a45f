.read-certification {
    max-width: 10rem;

    @include media-breakpoint-up(lg) {
        max-width: 100%;
    }

    &-title {
        display: inline-block;
        background-image: url(../images/read-certification-title.png);
        background-repeat: no-repeat;
        background-size: 100% auto;
        font-size: 0;
        height: 2.3rem;
        max-width: 159px;
        width: 100%;
    }

    &-lv {
        display: inline-block;
        margin: 0.15rem;
        width: 1.5rem;
        height: 2rem;
        font-size: 0;
        background-image: url(../images/read-certification-card-bg.png);
        background-repeat: no-repeat;
        background-size: 100% auto;

        @include media-breakpoint-up(lg) {
            width: 32px;
            height: 42px;
        }

        span {
            display: inline-block;
            padding-top: 0.1rem;
            margin-top: 0.1rem;
            min-width: 1.4rem;
            font-family: -webkit-body;
            font-size: 1.2rem;
            line-height: 1;
            text-align: center;
            color: #fff;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.5) 0%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0) 51%, rgba(229, 229, 229, 0) 100%),
                radial-gradient(ellipse at center, #948527 35% 0%, #3b3200 100%);
            border-radius: 1rem;
            transform: scale(0.6);

            @include media-breakpoint-up(lg) {
                padding-top: 0;
                margin-top: 0.52rem;
                min-width: 1.1rem;
                font-family: -webkit-body;
                transform: scale(1);
            }
        }

        &1 {
            @extend .read-certification-lv;
        }

        &2 {
            @extend .read-certification-lv;

            span {
                filter: hue-rotate(240deg);
            }
        }

        &3 {
            @extend .read-certification-lv;

            span {
                filter: hue-rotate(330deg);
            }
        }

        &4 {
            @extend .read-certification-lv;

            span {
                filter: hue-rotate(32deg);
            }
        }

        &5 {
            @extend .read-certification-lv;

            span {
                filter: hue-rotate(137deg);
            }
        }

        &6 {
            @extend .read-certification-lv;

            span {
                filter: hue-rotate(28deg) brightness(0.8) contrast(1.7) saturate(0.2);
            }
        }

        &7 {
            @extend .read-certification-lv;

            span {
                filter: hue-rotate(212deg) brightness(0.8) contrast(1.7) saturate(0.5);
            }
        }

        &8 {
            @extend .read-certification-lv;

            span {
                filter: hue-rotate(240deg) brightness(0.9) contrast(1.7) saturate(0.5);
            }
        }

        &9 {
            @extend .read-certification-lv;

            span {
                filter: hue-rotate(239deg) brightness(0.8) contrast(1.7) saturate(0.5);
            }
        }

        &10 {
            @extend .read-certification-lv;

            span {
                filter: hue-rotate(274deg) brightness(0.8) contrast(1.7) saturate(0.7);
            }
        }
    }
}

.opacity-50 {
    opacity: 0.5;
}