/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Latin-Modern/Marks/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.LatinModernMathJax_Marks={directory:"Marks/Regular",family:"LatinModernMathJax_Marks",testString:"\u00A0\u02DB\u02DD\u0305\u0309\u030F\u0311\u0323\u0326\u032C\u032D\u032E\u032F\u0330\u0331",32:[0,0,332,0,0],160:[0,0,332,0,0],731:[5,211,500,135,364],733:[697,-511,500,128,420],773:[670,-630,0,-460,-68],777:[729,-520,0,-361,-167],783:[732,-546,0,-434,-142],785:[742,-605,0,-451,-77],803:[-94,200,0,-318,-211],806:[-66,290,0,-308,-220],812:[-96,243,0,-446,-82],813:[-108,255,0,-446,-82],814:[-96,233,0,-451,-77],815:[-118,255,0,-451,-77],816:[-118,310,0,-449,-79],817:[-131,162,0,-444,-83],818:[-103,143,0,-460,-68],819:[-103,293,0,-460,-68],831:[820,-630,0,-460,-68],8192:[0,0,500,0,0],8193:[0,0,1000,0,0],8199:[0,0,500,0,0],8200:[0,0,250,0,0],8203:[0,0,0,0,0],8204:[0,0,0,0,0],8205:[0,0,0,0,0],8208:[245,-187,333,11,276],8210:[342,-302,680,56,624],8213:[270,-230,1152,56,1096],8215:[-103,293,504,56,448],8218:[104,195,278,79,199],8222:[104,195,472,86,400],8226:[445,-55,500,55,445],8239:[0,0,200,0,0],8240:[750,56,1140,53,1087],8241:[750,56,1457,56,1417],8246:[748,-430,511,60,451],8247:[748,-430,711,60,651],8249:[483,0,389,111,277],8250:[483,0,389,112,278],8251:[492,-8,778,147,630],8253:[756,0,472,43,402],8274:[751,-1,500,56,444],8287:[0,0,222,0,0],8288:[0,0,0,0,0],8289:[640,240,960,40,920],8290:[0,0,0,0,0],8291:[0,0,0,0,0],8292:[0,0,0,0,0],8400:[711,-601,0,-475,-53],8401:[711,-601,0,-475,-53],8402:[750,250,0,-284,-244],8403:[520,20,0,-284,-244],8404:[751,-589,0,-473,-55],8405:[751,-589,0,-473,-55],8406:[711,-521,0,-472,-56],8408:[400,-100,0,-414,-114],8411:[696,-604,0,-530,2],8412:[696,-604,0,-640,112],8413:[772,272,0,-786,258],8414:[750,250,0,-764,236],8415:[1028,528,0,-1042,514],8417:[711,-521,0,-499,-29],8420:[831,311,0,-924,396],8421:[750,250,0,-458,-69],8422:[750,250,0,-352,-176],8424:[-96,188,0,-530,2],8425:[726,-552,0,-444,-84],8426:[510,10,0,-706,178],8427:[750,250,0,-531,3],8428:[-171,281,0,-475,-53],8429:[-171,281,0,-475,-53],8430:[-91,281,0,-472,-56],8431:[-91,281,0,-472,-56],8432:[827,-518,0,-401,-126],11800:[500,256,472,56,415],12310:[770,270,458,56,402],12311:[770,270,458,57,402]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"LatinModernMathJax_Marks"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Marks/Regular/Main.js"]);
