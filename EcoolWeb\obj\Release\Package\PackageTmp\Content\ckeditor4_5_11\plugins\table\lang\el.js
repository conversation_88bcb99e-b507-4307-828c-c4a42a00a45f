﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'table', 'el', {
	border: 'Πάχος Περιγράμματος',
	caption: 'Λεζάντα',
	cell: {
		menu: 'Κελί',
		insertBefore: 'Εισαγωγή Κελιού Πριν',
		insertAfter: 'Εισαγωγή Κελιού Μετά',
		deleteCell: 'Διαγραφή Κελιών',
		merge: 'Ενοποίηση Κελιών',
		mergeRight: 'Συγχώνευση Με Δεξιά',
		mergeDown: 'Συγχώνευση Με Κάτω',
		splitHorizontal: 'Οριζόντια Διαίρεση Κελιού',
		splitVertical: 'Κατακόρυφη Διαίρεση Κελιού',
		title: 'Ιδιότητες <PERSON>',
		cellType: '<PERSON><PERSON><PERSON><PERSON>ο<PERSON>',
		rowSpan: '<PERSON><PERSON><PERSON><PERSON>',
		colSpan: '<PERSON>ύρος Στηλών',
		wordWrap: 'Αναδίπλωση Λέξεων',
		hAlign: 'Οριζόντια Στοίχιση',
		vAlign: 'Κάθετη Στοίχιση',
		alignBaseline: 'Γραμμή Βάσης',
		bgColor: 'Χρώμα Φόντου',
		borderColor: 'Χρώμα Περιγράμματος',
		data: 'Δεδομένα',
		header: 'Κεφαλίδα',
		yes: 'Ναι',
		no: 'Όχι',
		invalidWidth: 'Το πλάτος του κελιού πρέπει να είναι αριθμός.',
		invalidHeight: 'Το ύψος του κελιού πρέπει να είναι αριθμός.',
		invalidRowSpan: 'Το εύρος των γραμμών πρέπει να είναι ακέραιος αριθμός.',
		invalidColSpan: 'Το εύρος των στηλών πρέπει να είναι ακέραιος αριθμός.',
		chooseColor: 'Επιλέξτε'
	},
	cellPad: 'Αναπλήρωση κελιών',
	cellSpace: 'Απόσταση κελιών',
	column: {
		menu: 'Στήλη',
		insertBefore: 'Εισαγωγή Στήλης Πριν',
		insertAfter: 'Εισαγωγή Στήλης Μετά',
		deleteColumn: 'Διαγραφή Στηλών'
	},
	columns: 'Στήλες',
	deleteTable: 'Διαγραφή Πίνακα',
	headers: 'Κεφαλίδες',
	headersBoth: 'Και τα δύο',
	headersColumn: 'Πρώτη στήλη',
	headersNone: 'Κανένα',
	headersRow: 'Πρώτη Γραμμή',
	invalidBorder: 'Το πάχος του περιγράμματος πρέπει να είναι ένας αριθμός.',
	invalidCellPadding: 'Η αναπλήρωση των κελιών πρέπει να είναι θετικός αριθμός.',
	invalidCellSpacing: 'Η απόσταση μεταξύ των κελιών πρέπει να είναι ένας θετικός αριθμός.',
	invalidCols: 'Ο αριθμός των στηλών πρέπει να είναι μεγαλύτερος από 0.',
	invalidHeight: 'Το ύψος του πίνακα πρέπει να είναι αριθμός.',
	invalidRows: 'Ο αριθμός των σειρών πρέπει να είναι μεγαλύτερος από 0.',
	invalidWidth: 'Το πλάτος του πίνακα πρέπει να είναι ένας αριθμός.',
	menu: 'Ιδιότητες Πίνακα',
	row: {
		menu: 'Γραμμή',
		insertBefore: 'Εισαγωγή Γραμμής Πριν',
		insertAfter: 'Εισαγωγή Γραμμής Μετά',
		deleteRow: 'Διαγραφή Γραμμών'
	},
	rows: 'Γραμμές',
	summary: 'Περίληψη',
	title: 'Ιδιότητες Πίνακα',
	toolbar: 'Πίνακας',
	widthPc: 'τοις εκατό',
	widthPx: 'pixel',
	widthUnit: 'μονάδα πλάτους'
} );
