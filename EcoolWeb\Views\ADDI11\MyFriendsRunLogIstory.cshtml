﻿@model ADDI11MyRunLogViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);

    if (AppMode || (Model?.WhereIsColorboxForUser ?? false))
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
}

@if (AppMode == false)
{
    @Html.Partial("_Title_Secondary")
}
@Html.Partial("_Notice")

@{
    if ((Model?.WhereIsColorboxForUser ?? false) == false)
    {
        Html.RenderAction("_RunMenuIstory", new { NowAction = "MyFriendsRunLogIstory" });
    }
}

@using (Html.BeginForm("MyFriendsRunLogIstory", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1" }))
{
    @Html.AntiForgeryToken()

    <div id="PageContent">
        @Html.Action("_PageMyFriendsRunLog", (string)ViewBag.BRE_NO)
    </div>
}

@section scripts{
    <script>
        var targetFromID = "#form1";
        function searchFriendRunLog(userno,schoolno,classno) {
            $("#@(Html.IdFor(m=>m.WhereUSER_NO))").val(userno);
            $("#@(Html.IdFor(m=>m.WhereSCHOOL_NO))").val(schoolno);
            $("#@(Html.IdFor(m=>m.WhereCLASS_NO))").val(classno);
            $(targetFromID).submit();
        }
    </script>
}