﻿@using ECOOL_APP.com.ecool.Models.entity;
@model IEnumerable<ECOOL_APP.EF.ADDT01>
@{
    ViewBag.Title = ViewBag.UserName + "線上投稿";
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    int i = 0;
    List<Image_File_Multiple> ImageModel = (List<Image_File_Multiple>)ViewBag.ImageUrl;
}





<div class="PageNext"></div>
<br /> <br />
<div class="Div-EZ-Pink">
    <div class="form-horizontal">
        <label class="control-label-D" style="font-size:0.6cm;">* 學習匯總</label>
        <br />
        <div class="form-horizontal">
            <label class="control-label-D"  style="font-size:0.6cm;">* 學習匯總</label>
            <br />
            <div class="row" >
                <div class="col-md-12 col-sm-12 dl-horizontal-EZ">
                    <samp class="dt" style="font-size:0.5cm;">
                        學校名稱
                    </samp>
                    <samp class="dd"  style="font-size:0.5cm;">
                        @ViewBag.SchoolName
                    </samp>
                </div>

            </div>
            <div class="row">
                <div class="col-md-12 col-sm-12 dl-horizontal-EZ">
                    <samp class="dt"  style="font-size:0.5cm;">
                        學生名稱
                    </samp>
                    <samp class="dd"  style="font-size:0.5cm;">
                        @ViewBag.UserName
                    </samp>
                </div>

            </div>
            <div class="row" style="font-size:0.5cm;">
                <div class="col-md-12 col-sm-12 dl-horizontal-EZ">
                    <samp class="dt"  style="font-size:0.5cm;">
                        學校期間
                    </samp>
                    <samp class="dd"  style="font-size:0.5cm;">
                        @ViewBag.SYear
                    </samp>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12 col-sm-12 dl-horizontal-EZ">
                    <samp class="dt"  style="font-size:0.5cm;">
                        總獲得酷幣點數
                    </samp>
                    <samp class="dd"  style="font-size:0.5cm;">
                        @ViewBag.CoolCash
                    </samp>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12 col-sm-12 dl-horizontal-EZ">
                    <samp class="dt"  style="font-size:0.5cm;"> 
                        線上投稿總篇數
                    </samp>
                    <samp class="dd"  style="font-size:0.5cm;">
                        @ViewBag.SumAcount
                    </samp>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12 col-sm-12 dl-horizontal-EZ">
                    <samp class="dt"  style="font-size:0.5cm;">
                        閱讀認證等級
                    </samp>
                    <samp class="dd"  style="font-size:0.5cm;">
                        @ViewBag.PassPort
                    </samp>
                </div>
            </div>
        </div>
    </div>
</div>
<div style="height:50px"></div>

@foreach (var item in Model)
{
  
    <div class="PageNext"></div>
    <div style="height:15px"></div>
    <div class="Div-EZ-Pink">
        <div class="Details">
            <div class="row">
                <div class="col-md-5 col-sm-6 dl-horizontal-EZ">
                    <samp class="dt" style="font-size:0.8cm;">
                        投稿日期
                    </samp>
                    <samp class="dd" style="font-size:0.7cm;">
                        @Html.DisplayFor(model => item.CRE_DATE, "ShortDateTime")
                    </samp>
                </div>
                <div class="col-md-3 col-sm-6  dl-horizontal-EZ">
                    <samp class="dt" style="font-size:0.8cm;">
                        班級
                    </samp>
                    <samp class="dd" style="font-size:0.7cm;">
                        @Html.DisplayFor(model => item.CLASS_NO)
                    </samp>
                </div>
                <div class="col-md-4 col-sm-6  dl-horizontal-EZ">
                    <samp class="dt" style="font-size:0.8cm;">
                        姓名
                    </samp>
                    <samp class="dd"  style="font-size:0.7cm;">
                        @Html.DisplayFor(model => item.SNAME)
                    </samp>
                </div>
            </div>
            <div class="row">
                <div class="col-md-8 col-sm-6  dl-horizontal">
                    <samp class="dt" style="font-size:0.8cm;">
                        文章標題
                    </samp>

                    <samp class="dd"  style="font-size:0.7cm;">
                        @Html.DisplayFor(model => item.SUBJECT)
                    </samp>
                </div>
                <div class="col-md-4 col-sm-12  dl-horizontal">
                    <samp class="dt" style="font-size:0.8cm;">
                        酷幣值
                    </samp>
                    <samp class="dd"  style="font-size:0.7cm;">
                        @Html.DisplayFor(model => item.CASH)
                    </samp>
                </div>
            </div>
            <div style="height:15px"></div>
            <div class="row">

            </div>
        </div>
        <div class="col-md-12">
            <div class="p-context"style="font-size:0.7cm;">
               

                @if (ImageModel != null && (ImageModel.Any()))
                {
                    var ThisList = ImageModel.Where(a => a.APPLY_NO == item.WRITING_NO).ToList();

                    if (ThisList != null)
                    {
                        foreach (var ThisItem in ThisList)
                        {
                            foreach (var img in ThisItem.ImageUrl)
                            {
                                
                                    <img src="@img" style="float:right;margin:10px;max-height:250px;width:auto" href="@img" class="img-responsive " />
                             
                               
                            }
                        }
                    }
                }
                <div> 
                    @if (!string.IsNullOrWhiteSpace(item.ARTICLE_VERIFY))
                    {
                        @Html.Raw(HttpUtility.HtmlDecode(item.ARTICLE_VERIFY.Replace("\r\n", "<br/>")))
                    }
                    else
                    {
                        @Html.Raw(HttpUtility.HtmlDecode(item.ARTICLE.Replace("\r\n", "<br/>")))
                    }
                   
                </div>
            
            </div>
         
        </div>
        @if (string.IsNullOrWhiteSpace(item.VERIFY_COMMENT) == false)
                {
            <hr />
                <div class="col-md-12 col-sm-12" style="padding-bottom:20px">
                    <table>
                        <tr>
                            <td width="120px">教師評語：</td>
                            <td width="400px" colspan="2" style="white-space: pre-line;font-size:0.5cm;" >@Html.Raw(HttpUtility.HtmlDecode(item.VERIFY_COMMENT))</td>
                        </tr>
                    </table>
                </div>
        }
    </div>
    <div style="height:50px"></div>
    i++;
}
