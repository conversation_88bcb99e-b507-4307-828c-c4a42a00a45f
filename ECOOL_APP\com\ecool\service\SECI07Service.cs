﻿using Dapper;
using DotNet.Highcharts;
using DotNet.Highcharts.Enums;
using DotNet.Highcharts.Helpers;
using DotNet.Highcharts.Options;
using ECOOL_APP.com.ecool.LogicCenter;
using ECOOL_APP.com.ecool.Models.DTO;
using ECOOL_APP.EF;
using System;
using System.Collections.Generic;

using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.com.ecool.service
{
    public class SECI07Service
    {
        /// <summary>
        /// 查詢班級借書排名
        /// </summary>
        public ClassBorrowBookRankViewModel GetRankOfClassBookBorrow(ClassBorrowBookRankViewModel model, ref ECOOL_DEVEntities db)
        {
            using (var txn = Queries.GetNewReadUncommittedScope())
            {
                string whereSql = "";
                //string AvgSql = "  round(CONVERT(money,BORROW_BOOK_COUNT)/12,1)";
                string MonthSql = "";
                if (!string.IsNullOrEmpty(model.Where_MONTH))
                {
                    whereSql += " and MONTH(l.BORROW_DATE)=@MONTH ";
                    //AvgSql = " round(CONVERT(money,BORROW_BOOK_COUNT)/30,1) ";
                    MonthSql = "and  MONTH(BORROW_DATE)=@MONTH";
                }

                if (string.IsNullOrWhiteSpace(model.OrdercColumn))
                {
                    model.OrdercColumn = "CLASSNO2";
                }
                string NoRentBook = $@"   RIGHT OUTER JOIN  (select CLASS_NO as CLASSNO2 ,SCHOOL_NO  as SCHOOLNO1 , 0   as BORROW_BOOK_COUNT2 from HRMT01 where  SCHOOL_NO=@SCHOOL_NO and CLASS_NO<>'' and USER_TYPE='S'  and USER_STATUS ='1'     group by SCHOOL_NO,CLASS_NO )jj    on kk.CLASSNO1=jj.CLASSNO2  and kk.SCHOOL_NO=jj.SCHOOLNO1";
                string sql = $@"
                             select CLASSNO2 as CLASS_NO ,case when BORROW_BOOK_COUNT1 IS NULL then 0 else  BORROW_BOOK_COUNT1 end as  BORROW_BOOK_COUNT,
                            (SELECT count(USER_NO) FROM [HRMT01] where SCHOOL_NO=@SCHOOL_NO and CLASS_NO=t.CLASSNO2 and USER_TYPE='S' and USER_STATUS<>9  ) CLASSQTY,
                            round(CONVERT(money,BORROW_BOOK_COUNT1)/(SELECT count(USER_NO) FROM [HRMT01] where SCHOOL_NO=@SCHOOL_NO and CLASS_NO=t.CLASSNO2 and USER_TYPE='S' and USER_STATUS<>9  ),1) BORROW_BOOK_AVG,
                            RANK() OVER(ORDER BY BORROW_BOOK_COUNT1 DESC) AS RANK,
RANK() OVER(ORDER BY round(CONVERT(money,BORROW_BOOK_COUNT1)/(SELECT count(USER_NO) FROM [HRMT01] where SCHOOL_NO=t.SCHOOL_NO and CLASS_NO=t.CLASSNO2 and USER_TYPE='S' and USER_STATUS<>9  ),1) DESC) AS AVG_RANK
                             from
                             (
	                                  select * from(   select h.CLASS_NO as CLASSNO1 ,h.SCHOOL_NO, COUNT(*) BORROW_BOOK_COUNT1
	                              from DB2_L_WORK l (nolock)
	                             inner join HRMT01 h (nolock) on l.NO_READ = h.IDNO and l.SCHOOL_NO = h.SCHOOL_NO
	                             where l.SCHOOL_NO=@SCHOOL_NO and l.SEYEAR=@SYEAR
                                 and h.CLASS_NO <> '' and h.USER_STATUS ='1' and h.USER_TYPE='S'
                                 {whereSql}
	                             group by h.SCHOOL_NO,h.CLASS_NO ) as kk {NoRentBook}
                             ) t order by " + model.OrdercColumn;

                var rankList = db.Database.Connection.Query<ClassBorrowBookRank>(sql, new
                {
                    SCHOOL_NO = model.Where_SCHOOLNO,
                    SYEAR = model.Where_SYEAR,
                    MONTH = model.Where_MONTH
                }).ToList();

                model.ClassRankList = rankList;

                return model;
            }
        }

        /// <summary>
        /// 查詢年級借書排名
        /// </summary>
        public GradeBorrowBookRankViewModel GetRankOfGradeBookBorrow(GradeBorrowBookRankViewModel model, ref ECOOL_DEVEntities db)
        {
            using (var txn = Queries.GetNewReadUncommittedScope())
            {
                string whereSql = "";
                string AvgSql = "  round(CONVERT(money,BORROW_BOOK_COUNT)/12,1)";

                if (!string.IsNullOrEmpty(model.Where_MONTH))
                {
                    whereSql += " and MONTH(l.BORROW_DATE)=@MONTH ";
                    AvgSql = " round(CONVERT(money,BORROW_BOOK_COUNT)/30,1) ";
                }

                string sql = $@"
                             select Grade,BORROW_BOOK_COUNT,{AvgSql} BORROW_BOOK_AVG, RANK() OVER(ORDER BY BORROW_BOOK_COUNT DESC) AS RANK
                             from
                             (
	                             select h.Grade, COUNT(*) BORROW_BOOK_COUNT
	                              from DB2_L_WORK l (nolock)
	                             inner join HRMT01 h (nolock) on l.NO_READ = h.IDNO and l.SCHOOL_NO = h.SCHOOL_NO
	                             where l.SCHOOL_NO=@SCHOOL_NO and l.SEYEAR=@SYEAR
                                 and h.Grade <> '' and h.USER_STATUS ='1' and h.USER_TYPE='S'
                                 {whereSql}
	                             group by h.Grade
                             ) t order by RANK ";

                var rankList = db.Database.Connection.Query<GradeBorrowBookRank>(sql, new
                {
                    SCHOOL_NO = model.Where_SCHOOLNO,
                    SYEAR = model.Where_SYEAR,
                    MONTH = model.Where_MONTH
                }).ToList();

                model.GradeRankList = rankList;

                if (model.GradeRankList?.Count > 0)
                {
                    model.GradeColumnChart = GetGradeColumnChart(model);
                }

                return model;
            }
        }

        /// <summary>
        ///  酷幣各類別點數 長條圖
        /// </summary>
        /// <param name="PerData"></param>
        /// <returns></returns>
        public Highcharts GetGradeColumnChart(GradeBorrowBookRankViewModel model)
        {
            var returnPoint = new List<Point>();

            foreach (var item in model.GradeRankList.OrderBy(x => x.Grade))
            {
                returnPoint.Add(new Point
                {
                    Y = item.BORROW_BOOK_COUNT,
                    Name = item.Grade + "年級"
                });
            }

            Data data = new Data(returnPoint.ToArray());

            Highcharts TempGradeColumnChart = new Highcharts("TempGradeColumnChart")
           .InitChart(new Chart
           {
               DefaultSeriesType = ChartTypes.Column,
           })
           .SetTitle(new Title { Text = "各年級借書量(本)" })
           .SetXAxis(new XAxis
           {
               Type = AxisTypes.Category
                        ,
               Labels = new XAxisLabels
               {
                   Style = "fontSize: '12px', fontFamily: 'Verdana, sans-serif'"
               }
           })

           .SetYAxis(new YAxis { Title = new YAxisTitle { Text = "借書量(本)" }, AllowDecimals = false })
           .SetSeries(new[]
                   {
                        new Series {
                            Data = data,
                            Name = "借書量(本)",
                            PlotOptionsBar = new PlotOptionsBar {
                                ColorByPoint = true,
                            },
                        }
                   })
           .SetPlotOptions(new PlotOptions
           {
               Column = new PlotOptionsColumn
               {
                   DataLabels = new PlotOptionsColumnDataLabels
                   {
                       Enabled = true,
                       Rotation = 0,
                       Color = System.Drawing.Color.Black,
                       Format = "{point.y:.1f}",
                       Style = "fontSize: '9px',fontFamily: 'Verdana, sans-serif'"
                   }
               }
           })
           .SetTooltip(new Tooltip { PointFormat = "{point.y:.1f} 本</b>" })
           .SetLegend(new Legend { Enabled = false });

            return TempGradeColumnChart;
        }

        /// <summary>
        /// 查詢校借書排名
        /// </summary>
        public SchoolBorrowBookViewModel GetRankOfSchoolBookBorrow(SchoolBorrowBookViewModel model, ref ECOOL_DEVEntities db, UserProfile user)
        {
            using (var txn = Queries.GetNewReadUncommittedScope())
            {
                string whereSql = "";
                string AvgSql = "  round(CONVERT(money,T.BORROW_BOOK_COUNT)/12,1)";
                string MonthSql = "";
                if (!string.IsNullOrEmpty(model.Where_MONTH))
                {
                    whereSql += " and right(A.RET_YYMM,2)=right('00'+CONVERT(nvarchar(2),@MONTH),2)  ";
                    AvgSql = " round(CONVERT(money,T.BORROW_BOOK_COUNT)/30,1) ";
                }
                if (string.IsNullOrEmpty(model.OrdercColumn))
                {
                    model.OrdercColumn = "RANK";
                }

                string sql = "";
                //------------------------------------
                if (user != null && user.USER_NO != "0000")
                {
                    sql = $@" Select T.Months,T.BORROW_BOOK_COUNT,{AvgSql} BORROW_BOOK_AVG,
                                     RANK() OVER(ORDER BY BORROW_BOOK_COUNT DESC) AS RANK,
                                    RANK() OVER(ORDER BY ( {AvgSql} ) DESC) AS RANK_AVG,
                                    B.SHORT_NAME
                             from
                             (
                               select A.SCHOOL_NO,SUM(A.QTY)  BORROW_BOOK_COUNT,right(A.RET_YYMM,2) Months
                    from DB2_L_WORK2 A (nolock)
                    where A.SEYEAR = @SYEAR
                                { whereSql}
                group by A.SCHOOL_NO,right(A.RET_YYMM,2)
                             ) T
                            INNER JOIN BDMT01  B(nolock) ON T.SCHOOL_NO = B.SCHOOL_NO where B.SCHOOL_NO=" + user.SCHOOL_NO;
                    sql += $@" order by " + model.OrdercColumn;
                }
                else if (user != null && user.USER_TYPE == "A" && user.USER_NO == "0000")
                {
                    sql = $@"
                             Select T.SCHOOL_NO,T.BORROW_BOOK_COUNT,
                            (SELECT count(USER_NO) FROM [HRMT01] where SCHOOL_NO=t.SCHOOL_NO  and USER_TYPE='S' and USER_STATUS<>9  ) SCHOOLQTY,
                              round(CONVERT(money,T.BORROW_BOOK_COUNT)/(SELECT count(USER_NO) FROM [HRMT01] where SCHOOL_NO=t.SCHOOL_NO  and USER_TYPE='S' and USER_STATUS<>9  ),1) BORROW_BOOK_AVG,
                            RANK() OVER(ORDER BY BORROW_BOOK_COUNT DESC) AS RANK,
                            RANK() OVER(ORDER BY ( round(CONVERT(money,T.BORROW_BOOK_COUNT)/(SELECT count(USER_NO) FROM [HRMT01] where SCHOOL_NO=t.SCHOOL_NO  and USER_TYPE='S' and USER_STATUS<>9  ),1)) DESC) AS RANK_AVG,
                            B.SHORT_NAME
                             from
                             (
	                            select A.SCHOOL_NO,SUM(A.QTY)  BORROW_BOOK_COUNT{MonthSql}
                                from DB2_L_WORK2 A (nolock)
                                where A.SEYEAR=@SYEAR
                                {whereSql}
                                group by A.SCHOOL_NO
                             ) T
                            INNER JOIN BDMT01  B  (nolock) ON T.SCHOOL_NO =B.SCHOOL_NO
                            order by " + model.OrdercColumn;
                }
                var rankList = db.Database.Connection.Query<SchoolBorrowBookRank>(sql, new
                {
                    SYEAR = model.Where_SYEAR,
                    MONTH = model.Where_MONTH
                }).ToList();

                model.SchoolRankList = rankList;

                return model;
            }
        }

        /// <summary>
        /// 查詢班級借書量
        /// </summary>
        public ClassBorrowBookViewModel GetClassBookBorrow(ClassBorrowBookViewModel model, ref ECOOL_DEVEntities db)
        {
            string whereSql = "";
            if (!string.IsNullOrEmpty(model.Where_MONTH))
            {
                whereSql += " and MONTH(l.BORROW_DATE)=@MONTH ";
            }

            string sql = $@" select CLASSNO2 as CLASS_NO ,case when BORROW_BOOK_COUNT1 is null then 0 else BORROW_BOOK_COUNT1 end as BORROW_BOOK_COUNT from ( select * from (
                              select h.CLASS_NO  as CLASSNO1 , COUNT(*) BORROW_BOOK_COUNT1
	                          from DB2_L_WORK l (nolock)
	                          inner join HRMT01 h (nolock) on l.NO_READ = h.IDNO and l.SCHOOL_NO = h.SCHOOL_NO
	                          where l.SCHOOL_NO=@SCHOOL_NO and l.SEYEAR=@SYEAR and h.CLASS_NO <> '' and h.USER_STATUS ='1' and h.USER_TYPE='S'
                              {whereSql}
	                          group by h.CLASS_NO ) as kk
                           ";

            string NoRentBook = $@"  RIGHT OUTER JOIN (select CLASS_NO   as CLASSNO2 , 0 as BORROW_BOOK_COUNT2 from HRMT01  where SCHOOL_NO=@SCHOOL_NO  and CLASS_NO<>''and USER_STATUS ='1'  and USER_TYPE='S'   group by SCHOOL_NO,CLASS_NO ) jj    on kk.CLASSNO1=jj.CLASSNO2  ) t order by CLASS_NO ";
            sql = sql + NoRentBook;
            var borrowList = db.Database.Connection.Query<ClassBorrowBook>(sql, new
            {
                SCHOOL_NO = model.Where_SCHOOLNO,
                SYEAR = model.Where_SYEAR,
                MONTH = model.Where_MONTH
            });

            model.BorrowCountList = borrowList.OrderBy(b => b.CLASS_NO).ToList();

            return model;
        }

        public BookFlowTimeViewModel GetBookFlowTime(BookFlowTimeViewModel model, ref ECOOL_DEVEntities db)
        {
            string whereSql = "";
            if (!string.IsNullOrEmpty(model.Where_Month))
            {
                whereSql += $" and SUBSTRING(DATE_OUT,5,2)='{model.Where_Month.PadLeft(2, '0')}' ";
            }

            // -- 借書時間(以小時為時間單位)
            string sql = $@"
                            select *,count(*) COUNT from(
                            select
                            Case when
                            ISDATE(
                              SUBSTRING(DATE_OUT,9,2)+':00:00')=1 Then
                             convert(datetime, SUBSTRING(DATE_OUT,9,2)+':00:00', 112)
                            Else null End as HOUR
                             from DB2_L_WORK l (nolock)
                             join HRMT01 h on l.SCHOOL_NO= h.SCHOOL_NO and l.NO_READ = h.IDNO
                             where l.DATE_OUT <> '' and l.SCHOOL_NO = @SCHOOL_NO and l.SEYEAR = @SYEAR and h.USER_STATUS ='1' and h.USER_TYPE='S'
                             	and  SUBSTRING(DATE_OUT,9,2)>=07
                              {whereSql}
                             ) t
                             where HOUR <> ''
                            group by HOUR
                            order by HOUR
                       ";
            var timeborrowList = db.Database.Connection.Query<TimeOfBook>(sql, new
            {
                SCHOOL_NO = model.Where_SCHOOLNO,
                SYEAR = model.Where_SYEAR
            });

            model.TimeOfBookBorrowList = timeborrowList.ToList();

            // -- 還書時間(以小時為時間單位)
            whereSql = string.Empty;
            if (!string.IsNullOrEmpty(model.Where_Month))
            {
                whereSql += $" and SUBSTRING(DATE_RET,5,2)='{model.Where_Month.PadLeft(2, '0')}' ";
            }
            sql = $@"
                      select *,count(*) COUNT from(
                       select
                       Case when
                       ISDATE(
                         SUBSTRING(DATE_RET,9,2)+':00:00')=1 Then
                        convert(datetime, SUBSTRING(DATE_RET,9,2)+':00:00', 112)
                       Else null End as HOUR
                        from DB2_L_WORK l (nolock)
                        join HRMT01 h on l.SCHOOL_NO= h.SCHOOL_NO and l.NO_READ = h.IDNO
                         where l.DATE_RET <> '' and l.SCHOOL_NO = @SCHOOL_NO and l.SEYEAR = @SYEAR and h.USER_STATUS ='1' and h.USER_TYPE='S'
                        	and  SUBSTRING(DATE_RET,9,2)>=07
                          {whereSql}
                        ) t
                        where HOUR  <> ''
                       group by HOUR
                       order by HOUR
                    ";
            var timeRetList = db.Database.Connection.Query<TimeOfBook>(sql, new
            {
                SCHOOL_NO = model.Where_SCHOOLNO,
                SYEAR = model.Where_SYEAR
            });

            model.TimeOfBookReturnList = timeRetList.ToList();

            return model;
        }

        public BookTypeBorrowAnalysisViewModel GetBookTypeAnalysis(BorrowAnalysisType type, BookTypeBorrowAnalysisViewModel model, ref ECOOL_DEVEntities db)
        {
            string sql = "";
            if (type == BorrowAnalysisType.MaleBorrowList || type == BorrowAnalysisType.FemaleBorrowList)
            {
                int SexNum;

                if (type == BorrowAnalysisType.MaleBorrowList) SexNum = (int)Gender.男生;
                else SexNum = (int)Gender.女生;

                sql = $@"
                            select TYPE_ID,TYPE_NAME,SEX,QTY, CONVERT(money,QTY) /CONVERT(money,sum(QTY) over ()) RATE
                            from (
                              --每個類別讀書量
                               select  b.TYPE_ID,b.TYPE_NAME,h.SEX, a.SEYEAR,a.SCHOOL_NO, count(*) QTY
                               from DB2_L_WORK a (nolock)
                               inner join BDMT04 b (nolock)  on left(a.BK_GRP,3) BETWEEN b.VAL_S and b.VAL_E
                               join HRMT01 h on a.NO_READ = h.IDNO and a.SCHOOL_NO = h.SCHOOL_NO
                               where b.MAIN_YN='Y' and h.USER_STATUS ='1' and h.USER_TYPE='S'
                               group by b.TYPE_ID,b.TYPE_NAME,h.SEX, a.SEYEAR,a.SCHOOL_NO
                             union
                               select 99 TYPE_ID,'其他' TYPE_NAME,h.SEX, a.SEYEAR,a.SCHOOL_NO, count(*) QTY
                               from DB2_L_WORK a (nolock)
                               join HRMT01 h on a.NO_READ = h.IDNO and a.SCHOOL_NO = h.SCHOOL_NO
                               and not exists (Select t.VAL_S,t.VAL_E from BDMT04 t (nolock) where left(a.BK_GRP,3) BETWEEN t.VAL_S and t.VAL_E and t.MAIN_YN='Y')
                               where h.USER_STATUS ='1' and h.USER_TYPE='S'
                               group by h.SEX, a.SEYEAR,a.SCHOOL_NO
                            ) T1
                            where SEYEAR=@SYEAR and SCHOOL_NO=@SCHOOL_NO and SEX={SexNum}
                            group by TYPE_ID,TYPE_NAME, SEX,QTY
                          ";
            }
            if (type == BorrowAnalysisType.GradeBorrowList) // MAIN_YN => Y: 主要要顯示類別  N: 都歸類為其他
            {
                sql = $@"
                            select TYPE_ID,TYPE_NAME,GRADE,QTY, CONVERT(money,QTY) /CONVERT(money,sum(QTY) over ()) RATE
                            from (
                              --每個類別讀書量
                               select  b.TYPE_ID,b.TYPE_NAME,h.GRADE, a.SEYEAR,a.SCHOOL_NO, count(*) QTY
                               from DB2_L_WORK a (nolock)
                               inner join BDMT04 b (nolock)  on left(a.BK_GRP,3) BETWEEN b.VAL_S and b.VAL_E
                               join HRMT01 h on a.NO_READ = h.IDNO and a.SCHOOL_NO = h.SCHOOL_NO
                               where b.MAIN_YN='Y' and h.USER_STATUS ='1' and h.USER_TYPE='S'
                               group by b.TYPE_ID,b.TYPE_NAME,h.GRADE, a.SEYEAR,a.SCHOOL_NO
                             union
                               select 99 TYPE_ID,'其他' TYPE_NAME,h.GRADE, a.SEYEAR,a.SCHOOL_NO, count(*) QTY
                               from DB2_L_WORK a (nolock)
                               join HRMT01 h on a.NO_READ = h.IDNO and a.SCHOOL_NO = h.SCHOOL_NO
                               and not exists (Select t.VAL_S,t.VAL_E from BDMT04 t (nolock) where left(a.BK_GRP,3) BETWEEN t.VAL_S and t.VAL_E and t.MAIN_YN='Y')
                               where h.USER_STATUS ='1' and h.USER_TYPE='S'
                               group by h.GRADE, a.SEYEAR,a.SCHOOL_NO
                            ) T1
                            where SEYEAR=@SYEAR and SCHOOL_NO=@SCHOOL_NO
                            group by TYPE_ID,TYPE_NAME, GRADE,QTY
                          ";
            }
            if (type == BorrowAnalysisType.MonthBorrowList)
            {
                sql = $@"
                            select TYPE_ID,TYPE_NAME,BORROW_MONTH,QTY, CONVERT(money,QTY) /CONVERT(money,sum(QTY) over ()) RATE
                            from (
                              --每個類別讀書量
                               select  b.TYPE_ID,b.TYPE_NAME,MONTH(a.BORROW_DATE) BORROW_MONTH, a.SEYEAR,a.SCHOOL_NO, count(*) QTY
                               from DB2_L_WORK a (nolock)
                               inner join BDMT04 b (nolock)  on left(a.BK_GRP,3) BETWEEN b.VAL_S and b.VAL_E
                               join HRMT01 h on a.NO_READ = h.IDNO and a.SCHOOL_NO = h.SCHOOL_NO
                               where b.MAIN_YN='Y' and h.USER_STATUS ='1' and h.USER_TYPE='S'
                               group by b.TYPE_ID,b.TYPE_NAME,MONTH(a.BORROW_DATE), a.SEYEAR,a.SCHOOL_NO
                             union
                               select 99 TYPE_ID,'其他' TYPE_NAME,MONTH(a.BORROW_DATE) BORROW_MONTH, a.SEYEAR,a.SCHOOL_NO, count(*) QTY
                               from DB2_L_WORK a (nolock)
                               join HRMT01 h on a.NO_READ = h.IDNO and a.SCHOOL_NO = h.SCHOOL_NO
                               and not exists (Select t.VAL_S,t.VAL_E from BDMT04 t (nolock) where left(a.BK_GRP,3) BETWEEN t.VAL_S and t.VAL_E and t.MAIN_YN='Y')
                               where h.USER_STATUS ='1' and h.USER_TYPE='S'
                               group by MONTH(a.BORROW_DATE), a.SEYEAR,a.SCHOOL_NO
                            ) T1
                            where SEYEAR=@SYEAR and SCHOOL_NO=@SCHOOL_NO
                            group by TYPE_ID,TYPE_NAME, BORROW_MONTH,QTY
                          ";
            }

            var list = db.Database.Connection.Query<BookTypeBorrowAnalysis>(sql, new
            {
                SCHOOL_NO = model.Where_SCHOOLNO,
                SYEAR = model.Where_SYEAR,
            }).ToList();

            model.AnalysisListDictionary.Add(type, list);

            return model;
        }

        public MonthBestSellerViewModel GetMonthBestSeller(MonthBestSellerViewModel model, ref ECOOL_DEVEntities db)
        {
            string whereSql = "";
            DateTime startQueryDate;
            DateTime endQueryDate;
            // 篩選 - 年分
            startQueryDate = new DateTime((int)model.Where_YEAR, 1, 1);
            endQueryDate = new DateTime((int)model.Where_YEAR + 1, 1, 1);
            // 篩選 - 月分
            if (model.Where_Month != null)
            {
                startQueryDate = startQueryDate.AddMonths((int)model.Where_Month - 1);
                endQueryDate = startQueryDate.AddMonths(1);
            }
            if (model.Where_SCHOOLNO != null)
            {
                whereSql += " and h.SCHOOL_NO = @SCHOOL_NO ";
            }
            if (model.Where_Sex != null)
            {
                whereSql += " and h.SEX = @SEX ";
            }
            string sql = $@"
                            select *, RANK() OVER(ORDER BY BORROW_COUNT DESC) AS RANK
                            from(
                                select l.BKNAME, year(l.BORROW_DATE) BORROW_YEAR, month(l.BORROW_DATE) BORROW_MONTH, count(*) BORROW_COUNT  from
                                (
	                                select NO_READ, l.BKNAME ,l.BORROW_DATE
	                                from DB2_L_WORK l (nolock)
                                ) l
                                join HRMT01 (nolock)  h on l.NO_READ =h.IDNO
                                where h.USER_STATUS ='1' and h.USER_TYPE='S'
                                and l.BORROW_DATE >='{startQueryDate.ToString("yyyy-MM-dd")}' and l.BORROW_DATE <'{endQueryDate.ToString("yyyy-MM-dd")}'
                                {whereSql}
                                group by BKNAME, year(l.BORROW_DATE) , month(l.BORROW_DATE)
                            ) T
                            order by RANK, BORROW_YEAR desc, BORROW_MONTH desc
                       ";
            var monthOfBookSellList = db.Database.Connection.Query<MonthOfBookSell>(sql, new
            {
                SCHOOL_NO = model.Where_SCHOOLNO,
                SEX = model.Where_Sex
            });

            model.MonthOfBookSellList = monthOfBookSellList.ToList();

            return model;
        }
    }
}