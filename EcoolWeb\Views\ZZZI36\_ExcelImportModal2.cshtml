﻿<link href="@Url.Content("~/Content/css/fileupload.css")" rel="stylesheet" type="text/css" />

<!-- Modal -->
<div class="modal fade" id="excelModal" role="dialog">
    <div class="modal-dialog">
        <!-- Modal content-->
        <div class="modal-content">
            <div class="modal-header" style="background-color:#4783bf;color:white">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">檔案上傳</h4>
            </div>
            <div class="modal-body">
                <h4 class="text-info">Step1. 選擇一個Excel檔案</h4>
                <a href="@Url.Content("~/Content/ExcelSample/ZZZI36_ImportTCardNo.xls")" target="_blank" class="btn-table-link pull-right">
                    <span class="glyphicon glyphicon-download" aria-hidden="true"></span>
                    下載 Sample
                </a>
                <!--file input example -->
                <span class="control-fileupload">
                    <label for="file">選擇檔案</label>
                    <input type="file" name="file" id="file" required accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel">
                </span>
                <br />

                <p>欄位範例: </p>
                <table class="table table-bordered">
                    <thead>
                        <tr>

                            <th>帳號<span class="text-danger">*</span></th>

                            <th>卡片內碼<span class="text-danger">*</span></th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>

                            <td>100001</td>

                            <td>9122112321000111</td>
                        </tr>
                        <tr>
                            <td>　</td>
                            <td>　</td>
                        </tr>
                    </tbody>
                </table>
                <p><span class="text-danger">*表示必填欄位</span></p>

                <hr />
                <h4 class="text-info">Step2. 上傳檔案</h4>
                <button type="submit" class="btn btn-default btn-lg">確定上傳</button>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
    $(function () {
        $('input[type=file]').change(function () {
            var t = $(this).val();
            var labelText = 'File : ' + t.substr(12, t.length);
            $(this).prev('label').text(labelText);
        })
    });
</script>