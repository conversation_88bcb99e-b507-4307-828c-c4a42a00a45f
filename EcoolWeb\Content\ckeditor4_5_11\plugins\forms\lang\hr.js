﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'forms', 'hr', {
	button: {
		title: 'Button svojstva',
		text: 'Tekst (vrijednost)',
		type: 'Vrsta',
		typeBtn: 'Gumb',
		typeSbm: '<PERSON><PERSON>lji',
		typeRst: '<PERSON><PERSON><PERSON><PERSON>'
	},
	checkboxAndRadio: {
		checkboxTitle: 'Checkbox svojstva',
		radioTitle: 'Radio Button svojstva',
		value: 'Vrijednost',
		selected: 'Odabrano',
		required: 'Required' // MISSING
	},
	form: {
		title: 'Form svojstva',
		menu: 'Form svojstva',
		action: 'Akcija',
		method: 'Metoda',
		encoding: 'Encoding'
	},
	hidden: {
		title: 'Hidden Field svojstva',
		name: 'Ime',
		value: 'Vrijednost'
	},
	select: {
		title: 'Selection svojstva',
		selectInfo: 'Info',
		opAvail: 'Dostupne opcije',
		value: 'Vrijednost',
		size: '<PERSON><PERSON><PERSON><PERSON>',
		lines: 'linija',
		chkMulti: 'Doz<PERSON><PERSON> višestruki odabir',
		required: 'Required', // MISSING
		opText: 'Tekst',
		opValue: 'Vrijednost',
		btnAdd: 'Dodaj',
		btnModify: 'Promijeni',
		btnUp: 'Gore',
		btnDown: 'Dolje',
		btnSetValue: 'Postavi kao odabranu vrijednost',
		btnDelete: 'Obriši'
	},
	textarea: {
		title: 'Textarea svojstva',
		cols: 'Kolona',
		rows: 'Redova'
	},
	textfield: {
		title: 'Text Field svojstva',
		name: 'Ime',
		value: 'Vrijednost',
		charWidth: 'Širina',
		maxChars: 'Najviše karaktera',
		required: 'Required', // MISSING
		type: 'Vrsta',
		typeText: 'Tekst',
		typePass: 'Šifra',
		typeEmail: 'Email',
		typeSearch: 'Traži',
		typeTel: 'Broj telefona',
		typeUrl: 'URL'
	}
} );
