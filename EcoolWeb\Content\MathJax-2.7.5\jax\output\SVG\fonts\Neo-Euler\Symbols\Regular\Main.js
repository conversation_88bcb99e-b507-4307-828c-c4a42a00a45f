/*
 *  /MathJax/jax/output/SVG/fonts/Neo-Euler/Symbols/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax.SVG.FONTDATA.FONTS.NeoEulerMathJax_Symbols={directory:"Symbols/Regular",family:"NeoEulerMathJax_Symbols",id:"NEOEULERSYMBOLS",32:[0,0,333,0,0,""],160:[0,0,333,0,0,""],8992:[915,0,444,180,452,"180 0c0 117 1 235 2 352c2 177 9 272 10 286c13 169 48 277 135 277c70 0 125 -37 125 -90c0 -23 -12 -41 -37 -41c-18 0 -44 13 -44 41c0 20 6 25 11 29c-11 16 -34 30 -54 30c-47 0 -58 -151 -61 -195c-2 -27 -8 -113 -8 -318c0 -29 0 -132 2 -154v-61 c1 -52 3 -104 3 -156h-84"],8993:[925,0,444,-23,265,"265 925c0 -186 -1 -464 -11 -624c-13 -209 -61 -301 -151 -301c-69 0 -126 35 -126 91c0 15 7 40 37 40c19 0 44 -14 44 -41c0 -19 -6 -25 -11 -29c9 -13 31 -30 55 -30c61 0 72 157 76 206c2 25 8 105 8 306c0 21 0 56 -1 63v60c-2 86 -4 172 -4 259h84"],9001:[737,237,388,107,330,"150 250l180 -473l-37 -14l-186 487l186 487l37 -14"],9002:[737,237,388,57,280,"94 737l186 -487l-186 -487l-37 14l180 473l-180 473"],9115:[1808,0,883,292,851,"292 10c0 652 93 1306 507 1798h52l-11 -15c-372 -505 -438 -1153 -438 -1783v-10h-110v10"],9116:[620,0,875,292,403,"403 593v-566c0 -26 -1 -27 -28 -27h-55c-27 0 -28 1 -28 27v566c0 26 1 27 28 27h55c27 0 28 -1 28 -27"],9117:[1808,0,883,292,851,"799 0c-414 491 -507 1146 -507 1798v10h110v-10c0 -630 66 -1278 438 -1783l11 -15h-52"],9118:[1808,0,873,22,581,"74 1808c414 -491 507 -1146 507 -1798v-10h-110v10c0 630 -66 1278 -438 1783l-11 15h52"],9119:[620,0,875,472,583,"583 593v-566c0 -26 -1 -27 -28 -27h-55c-27 0 -28 1 -28 27v566c0 26 1 27 28 27h55c27 0 28 -1 28 -27"],9120:[1808,0,873,22,581,"581 1798c0 -652 -93 -1306 -507 -1798h-52l11 15c372 505 438 1153 438 1783v10h110v-10"],9121:[1799,0,666,326,659,"326 0v1799h333v-69h-264v-1730h-69"],9122:[602,0,666,326,395,"326 0v602h69v-602h-69"],9123:[1800,-1,666,326,659,"326 1v1799h69v-1730h264v-69h-333"],9124:[1799,0,666,7,340,"271 0v1730h-264v69h333v-1799h-69"],9125:[602,0,666,271,340,"271 0v602h69v-602h-69"],9126:[1800,-1,666,7,340,"271 70v1730h69v-1799h-333v69h264"],9127:[909,0,889,395,718,"492 443v-437c-6 -6 -8 -6 -29 -6h-39c-28 0 -29 1 -29 27v430c0 159 84 343 281 452h34c8 -2 8 -7 8 -24c0 -19 -1 -20 -6 -23c-154 -99 -220 -253 -220 -419"],9128:[1820,0,889,170,492,"492 457v-451c-6 -6 -8 -6 -29 -6h-39c-28 0 -29 1 -29 27v423c0 56 -9 148 -57 250c-53 109 -132 165 -163 188c-4 3 -5 4 -5 22s1 19 11 27c175 122 214 320 214 433v423c0 26 1 27 29 27h39c21 0 23 0 29 -6v-451c0 -72 -21 -298 -264 -453 c250 -160 264 -394 264 -453"],9129:[909,0,889,395,718,"708 0h-32c-194 108 -281 289 -281 452v430c0 26 1 27 29 27h39c21 0 23 0 29 -6v-437c0 -215 110 -347 211 -413c14 -10 15 -11 15 -29c0 -17 0 -23 -10 -24"],9130:[320,0,889,395,492,"492 314v-308c-6 -6 -8 -6 -29 -6h-39c-28 0 -29 1 -29 27v266c0 26 1 27 29 27h39c21 0 23 0 29 -6"],9131:[909,0,889,170,492,"492 465v-459c-6 -6 -8 -6 -29 -6h-39c-28 0 -29 1 -29 27v430c0 118 -47 292 -214 401c-10 8 -11 9 -11 27c0 15 0 23 10 24h32c212 -118 280 -315 280 -444"],9132:[1820,0,889,395,718,"492 435v-429c-6 -6 -8 -6 -29 -6h-39c-28 0 -29 1 -29 27v422c0 171 90 354 265 461c-174 106 -265 288 -265 461v422c0 26 1 27 29 27h39c21 0 23 0 29 -6v-429c0 -114 26 -207 70 -287c53 -96 115 -140 151 -166c4 -3 5 -4 5 -22s-1 -19 -5 -22 c-37 -27 -100 -72 -153 -170c-49 -88 -68 -184 -68 -283"],9133:[909,0,889,170,492,"395 452v430c0 26 1 27 29 27h39c21 0 23 0 29 -6v-459c0 -127 -66 -325 -280 -444c-11 0 -31 0 -33 1c-9 2 -9 8 -9 23c0 19 1 20 6 23c188 121 219 311 219 405"],9134:[381,0,444,181,265,"182 0l-1 381h83l1 -381h-83"]};MathJax.Ajax.loadComplete(MathJax.OutputJax.SVG.fontDir+"/Symbols/Regular/Main.js");
