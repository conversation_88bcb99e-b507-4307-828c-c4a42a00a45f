﻿
@model ECOOL_APP.com.ecool.Models.DTO.ZZZI26EditViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;
}


@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")


@using (Html.BeginForm((string)ViewBag.Action, (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", enctype = "multipart/form-data" }))
{

    @Html.AntiForgeryToken()


    @Html.ValidationSummary(true, "", new { @class = "text-danger" })

    @Html.HiddenFor(model => model.Search.ModeVal)
    @Html.HiddenFor(model => model.Search.SCHOOL_NO)
    <img src="~/Content/img/web-bar3-revise-21.png" style="width:100%" class="img-responsive " alt="Responsive image" />
    <div class="Div-EZ-ZZZI26">
        <div class="form-horizontal">
            <label class="control-label">@ViewBag.Panel_Title</label>
            <br /><br />
                <div class="form-group">
                    @Html.LabelFor(model => model.Search.CLASS_NO, htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-9">
                        @Html.DropDownListFor(model => model.Search.CLASS_NO, (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control" })
                        @Html.ValidationMessageFor(model => model.Search.CLASS_NO, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group">
                    @Html.LabelFor(model => model.Search.NumType, htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-9">
                        @Html.DropDownListFor(model => model.Search.NumType, (IEnumerable<SelectListItem>)ViewBag.NumTypeItems, new { @class = "form-control" })
                        @Html.ValidationMessageFor(model => model.Search.NumType, "", new { @class = "text-danger" })
                    </div>
                </div>
            <div class=" text-right">
                @Html.PermissionButton("下一步", "button", (string)ViewBag.BRE_NO, "Index", new { @class = "btn btn-default", onclick = "Add()" }, 1, ViewBag.BtnPermission as List<ECOOL_APP.ControllerPermissionfile>)
            </div>
        </div>
    </div>


}

@section css {
    <style>
        .form-group.focused {
            background-color: #f8f9fa;
            border-radius: 4px;
            padding: 10px;
            transition: all 0.3s ease;
        }

        .input-validation-error {
            border-color: #dc3545 !important;
            box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
        }

        .field-validation-error {
            display: block;
            margin-top: 5px;
            font-size: 0.875em;
        }

        .validation-summary-errors {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 15px;
        }

        .validation-summary-errors ul {
            margin-bottom: 0;
            padding-left: 20px;
        }

        .btn {
            transition: all 0.3s ease;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .form-control:focus {
            border-color: #80bdff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
    </style>
}

@section Scripts {
    <script nonce="cmlvaw">
        // 設置全局 URL 配置
        window.ZZZI26_URLS = {
            editAction: "@Url.Action("Edit", (string)ViewBag.BRE_NO)"
        };
    </script>
    <script src="~/Scripts/ZZZI26/manystudentindex.js" nonce="cmlvaw"></script>
}

