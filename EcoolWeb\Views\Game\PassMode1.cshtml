﻿@model GamePassModexViewModel
@{
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
    EcoolWeb.Models.UserProfileHelper.SetLayoutCookieData(Layout);

    string GROUP_ID = string.Empty;
}

<script src="~/Scripts/vertical-timeline/js/modernizr.js"></script>
<script src="~/Scripts/vertical-timeline/js/main.js"></script>
<link href="~/Scripts/vertical-timeline/css/reset.css" rel="stylesheet" />
<link href="~/Scripts/vertical-timeline/css/style.css" rel="stylesheet" />

@using (Html.BeginForm("PassMode1", "Game", FormMethod.Post, new { name = "form1", id = "form1" }))
{
    @Html.HiddenFor(m => m.GAME_NO)
    @Html.HiddenFor(m => m.LEVEL_NO)
    @Html.HiddenFor(m => m.UnApply)
    @Html.HiddenFor(m => m.Coupons_ITem)
}

<div class="panel with-nav-tabs panel-info">
    <div class="panel-heading">
        <h1 style="font-size: 3.31rem;">@Model.Main.GAME_NAME</h1>
    </div>
    <div class="panel-body" style="background-color:#eef6fa;">
        <div>
            @if (Model?.Details?.Count > 0)
            {
                <section id="cd-timeline">

                    @*<div class="cd-timeline-block">
                    <div class="cd-timeline-img">
                        <img src="~/Content/img/maps-and-flags.svg" alt="Location">
                    </div>

                    <div class="cd-timeline-content">
                        <h2>
                            <img src="~/Content/img/tasks.svg" style="height:35px" />
                            <b>活動</b>
                        </h2>
                        <br />
                        <div class="form-inline">
                            <button type="button" class="btn btn-default btn-lg" onclick="OnApplyView()"><span class="glyphicon glyphicon-credit-card" aria-hidden="true"></span> 活動報名機台</button>
                            <button type="button" class="btn btn-default btn-lg" onclick="OnCreQRCode()"><span class="glyphicon glyphicon-qrcode" aria-hidden="true"></span> QR CODE頁面，「掃描此QR CODE，連結至訪客產生個人臨時卡頁面」</button>
                        </div>
                    </div>
                </div>*@

                    @*<div class="cd-timeline-block">
                    <div class="cd-timeline-img">
                        <img src="~/Content/img/maps-and-flags.svg" alt="Location">
                    </div>

                    <div class="cd-timeline-content">
                        <h2>
                            <img src="~/Content/img/tasks.svg" style="height:35px" />
                            <b>查詢</b>
                        </h2>
                        <br />
                        <div class="form-inline">
                            <button type="button" class="btn btn-default btn-lg" onclick="OnCashQuery()"><span class="glyphicon glyphicon-credit-card" aria-hidden="true"></span> 查詢機台</button>
                            <button type="button" class="btn btn-default btn-lg" onclick="OnQueryUniteCreQRCode()"><span class="glyphicon glyphicon-qrcode" aria-hidden="true"></span> QR CODE網頁，「掃描此QR CODE，連結至中獎清單明細」</button>
                            <button type="button" class="btn btn-default btn-lg" onclick="OnCashQueryQRCode()"><span class="glyphicon glyphicon-globe" aria-hidden="true"></span> 中獎清單明細</button>
                        </div>
                    </div>
                </div>*@

                    @*<div class="cd-timeline-block">
                    <div class="cd-timeline-img">
                        <img src="~/Content/img/maps-and-flags.svg" alt="Location">
                    </div>

                    <div class="cd-timeline-content">
                        <h2>
                            <img src="~/Content/img/maps-and-flags.svg" style="height:35px" />
                            <b>小組闖關</b>
                        </h2>
                        <br />
                        <div class="form-inline">
                            <div class="form-inline">
                                <button type="button" class="btn btn-default btn-lg" onclick="OnQueryTeamQRCode()"><span class="glyphicon glyphicon-qrcode" aria-hidden="true"></span> QR CODE網頁，「掃描此QR CODE，小組闖關狀態」</button>
                                <button type="button" class="btn btn-default btn-lg" onclick="OnQueryTeamStatus()"><span class="glyphicon glyphicon-globe" aria-hidden="true"></span> 小組闖關狀態</button>
                            </div>
                        </div>
                    </div>
                </div>*@

                    @if (Model.Main.GAME_TYPE == (byte)ADDT26.GameType.一般)
                    {
                        <div class="cd-timeline-block">
                            <div class="cd-timeline-img">
                                <img src="~/Content/img/maps-and-flags.svg" alt="Location">
                            </div>

                            <div class="cd-timeline-content">
                                <h2>
                                    <img src="~/Content/img/tasks.svg" style="height:35px" />
                                    <b>街頭藝人</b>
                                </h2>
                                <br />
                                <div class="form-inline">
                                    <button type="button" class="btn btn-default btn-lg" onclick="OnBuskerAddView()"><span class="glyphicon glyphicon-camera" aria-hidden="true"></span> 街頭藝人現場報名機台</button>
                                    <br>
                                    <button type="button" class="btn btn-default btn-lg" onclick="OnBuskerLikeView()">
                                        <span class="glyphicon glyphicon-credit-card" aria-hidden="true"></span> 街頭藝人打賞畫面機台
                                    </button>
                                    <br>
                                    <button type="button" class="btn btn-default btn-lg" onclick="OnBuskerGuestLikeView()"><span class="glyphicon glyphicon-qrcode" aria-hidden="true"></span> 家長打賞網址，「掃描此QR CODE，幫目前表演者按讚」</button>
                                </div>
                            </div> <!-- cd-timeline-content -->
                        </div> <!-- cd-timeline-block -->
                    }

                    @foreach (var item in Model.Details.Where(A => A.LEVEL_TYPE != ADDT26_D.LevelType.Apply).ToList())
                    {

                        if (string.IsNullOrWhiteSpace(item.GROUP_ID))
                        {

                            <div class="cd-timeline-block">
                                <div class="cd-timeline-img">
                                    <img src="~/Content/img/maps-and-flags.svg" alt="Location">
                                </div>

                                <div class="cd-timeline-content" style="color:aquamarine">
                                    <h2>
                                        <img src="~/Content/img/position.svg" style="height:35px" />
                                        @item.LEVEL_NAME
                                    </h2>
                                    <br />
                                    <div class="form-inline">
                                        @if (item.LEVEL_TYPE == ADDT26_D.LevelType.Prize)
                                        {
                                            <button type="button" class="btn btn-danger btn-lg" onclick="OnLevelView('@item.LEVEL_NO')"><span class="glyphicon glyphicon-credit-card" aria-hidden="true"></span> 進入此關卡畫面</button>
                                        }
                                        else if (item.LEVEL_TYPE == ADDT26_D.LevelType.ItemPrize)
                                        {

                                            <button type="button" class="btn btn-danger btn-lg" onclick="OnLevelGameView('@item.Coupons_ITem','@item.LEVEL_NO')"><span class="glyphicon glyphicon-credit-card" aria-hidden="true"></span> 進入此關卡畫面</button>

                                        }
                                        else
                                        {
                                            <button type="button" class="btn btn-default btn-lg" onclick="OnLevelView('@item.LEVEL_NO')"><span class="glyphicon glyphicon-credit-card" aria-hidden="true"></span> 進入此關卡畫面</button>
                                        }
                                    </div>
                                </div> <!-- cd-timeline-content -->
                            </div> <!-- cd-timeline-block -->
                        }

                        else
                        {
                            if (GROUP_ID != item.GROUP_ID)
                            {
                                <div class="cd-timeline-block">
                                    <div class="cd-timeline-img">
                                        <img src="~/Content/img/maps-and-flags.svg" alt="Location">
                                    </div>

                                    <div class="cd-timeline-content">
                                        <h2>
                                            <img src="~/Content/img/tasks.svg" style="height:35px" />

                                            <b>第@(item.G_ORDER_BY)題:@item.G_SUBJECT</b>
                                        </h2>
                                        <br />
                                        <div class="form-inline">
                                            @foreach (var Gitem in Model.Details.Where(a => a.GROUP_ID == item.GROUP_ID && a.GAME_NO == item.GAME_NO).OrderBy(a => a.LEVEL_ITEM).ToList())
                                            {
                                                <button type="button" class="btn btn-default btn-lg" onclick="OnLevelView('@Gitem.LEVEL_NO')"><span class="glyphicon glyphicon-credit-card" aria-hidden="true"></span> @Gitem.LEVEL_NAME</button>
                                            }
                                        </div>
                                    </div> <!-- cd-timeline-content -->
                                </div> <!-- cd-timeline-block -->

                                GROUP_ID = item.GROUP_ID;
                            }
                        }

                    }
                </section>
            }
            <div id="DivAddButton">
                <i id="title" class="fa fa-arrow-left fa-3x"></i>
                <button type="button" onclick="OnBack()" style="margin: -15px 0px;" class="btn btn-primary  btn-sm">回上一頁</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script language="JavaScript">
        var targetFormID = '#form1';

        function OnApplyView() {
             $(targetFormID).attr("action", "@Url.Action("ApplyView", "Game")")
             $(targetFormID).submit();
        }

        function OnQueryTeamQRCode() {
             $(targetFormID).attr("action", "@Url.Action("QueryTeamQRCode", "Game" ,new { GAME_NO=Model.GAME_NO })")
             $(targetFormID).submit();
        }

        function OnQueryTeamStatus() {
             $(targetFormID).attr("action", "@Url.Action("QueryTeamStatus", "Game")")
             $(targetFormID).submit();
        }

        function OnCreQRCode() {
             $(targetFormID).attr("action", "@Url.Action("CreQRCode", "Game")")
             $(targetFormID).submit();
        }

        function OnLevelGameView(Coupons_ITem,LEVEL_NO_VAL) {
            $(targetFormID).attr("action", "@Url.Action("LevelGameView1", "Game")")
            $('#@Html.IdFor(m=>m.Coupons_ITem)').val(Coupons_ITem)
            $('#@Html.IdFor(m => m.LEVEL_NO)').val(LEVEL_NO_VAL)
             $(targetFormID).submit();
        }

        function OnLevelView(LEVEL_NO_VAL) {
            $(targetFormID).attr("action", "@Url.Action("LevelView1", "Game")")
            $('#@Html.IdFor(m=>m.LEVEL_NO)').val(LEVEL_NO_VAL)
             $(targetFormID).submit();
        }
        function OnQueryUniteCreQRCode() {
             $(targetFormID).attr("action", "@Url.Action("QueryUniteCreQRCode", "Game")")
             $(targetFormID).submit();
        }
            function OnBack() {
            $(targetFormID).attr("action", "@Url.Action("GameIndex1", "Game")")
            $('#@Html.IdFor(m=>m.LEVEL_NO)').val('')
             $(targetFormID).submit();
         }
        function OnCashQuery() {
             $(targetFormID).attr("action", "@Url.Action("CashQuery", "Game")")
             $(targetFormID).submit();
        }

        function OnCashQueryQRCode() {
            document.location.href = "@Url.Action("QueryUserDataList", "Game")?GAME_NO=@Model.GAME_NO";
        }

        function OnBuskerAddView() {
              $(targetFormID).attr("action", "@Url.Action("BuskerAddView", "Game")")
             $(targetFormID).submit();
        }

        function OnBuskerGuestLikeView() {
             $(targetFormID).attr("action", "@Url.Action("BuskerGuestLikeView", "Game")")
             $(targetFormID).submit();
        }

        function OnBuskerLikeView(Val) {

            if (Val=="G") {
                 $('#@Html.IdFor(m=>m.UnApply)').val(true)
            }
            else {
                 $('#@Html.IdFor(m=>m.UnApply)').val(false)
            }

             $(targetFormID).attr("action", "@Url.Action("BuskerLikeView", "Game")")
             $(targetFormID).submit();
        }
    </script>
}