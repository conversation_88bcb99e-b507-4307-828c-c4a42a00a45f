﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.Mvc;
using System.Web.Routing;

namespace EcoolWeb.Util
{
    public class DropDownListHelper
    {
        /// <summary>
        /// 產生下拉選單html(以IDictionary傳入下拉選單的值).
        /// </summary>
        /// <param name="tagID">拉選單的TagID</param>
        /// <param name="tagName">拉選單的tagName</param>
        /// <param name="optionData">下拉選單Option的Text與Value.</param>
        /// <param name="htmlAttributes">The HTML attributes.</param>
        /// <param name="defaultSelectValue">預選值.</param>
        /// <param name="appendOptionLabel">是否加入預設空白選項.</param>
        /// <param name="optionLabel">如果appendOptionLabel為true,optionLabel為第一個項目要顯示的文字,如果沒有指定則顯示[請選擇].</param>
        /// <returns></returns>
        public static string GetDropdownList(string tagID, string tagName, IDictionary<string, string> optionData, object htmlAttributes, string defaultSelectValue, bool appendOptionLabel, string optionLabel)
        {
            if (string.IsNullOrEmpty(tagID))
            {
                throw new ArgumentNullException("ID", "產生DropDownList時 tag ID 不得為空");
            }

            if (string.IsNullOrEmpty(tagName))
            {
                tagName = tagID;
            }

            TagBuilder select = new TagBuilder("select");
            select.Attributes.Add("id", tagID);
            select.Attributes.Add("name", tagName);

            StringBuilder renderHtmlTag = new StringBuilder();
            IDictionary<string, string> newOptionData = new Dictionary<string, string>();
            if (appendOptionLabel)
            {
                newOptionData.Add(new KeyValuePair<string, string>(optionLabel ?? "請選擇", ""));
            }
            foreach (var item in optionData)
            {
                newOptionData.Add(item);
            }
            foreach (var option in newOptionData)
            {
                TagBuilder optionTag = new TagBuilder("option");
                optionTag.Attributes.Add("value", option.Value);
                if (!string.IsNullOrEmpty(defaultSelectValue) && defaultSelectValue.Equals(option.Value))
                {
                    optionTag.Attributes.Add("selected", "selected");
                }
                optionTag.SetInnerText(option.Key);
                renderHtmlTag.AppendLine(optionTag.ToString(TagRenderMode.Normal));
            }
            select.MergeAttributes(new RouteValueDictionary(htmlAttributes));
            select.InnerHtml = renderHtmlTag.ToString();
            return select.ToString();
        }
        /// <summary>
        /// 產生下拉選單html(以IDictionary傳入下拉選單的值).
        /// </summary>
        /// <param name="tagID">拉選單的TagID</param>
        /// <param name="tagName">拉選單的tagName</param>
        /// <param name="optionData">下拉選單Option的Text與Value.</param>
        /// <param name="htmlAttributes">The HTML attributes.</param>
        /// <param name="defaultSelectValue">預選值.</param>
        /// <param name="appendOptionLabel">是否加入預設空白選項.</param>
        /// <param name="optionLabel">如果appendOptionLabel為true,optionLabel為第一個項目要顯示的文字,如果沒有指定則顯示[請選擇].</param>
        /// <returns></returns>
        public static string GetDropdownOnchangeList(string tagID, string tagName, IQueryable<SelectListItem> optionData, object htmlAttributes, string defaultSelectValue, bool appendOptionLabel, string optionLabel)
        {
            if (string.IsNullOrEmpty(tagID))
            {
                throw new ArgumentNullException("ID", "產生DropDownList時 tag ID 不得為空");
            }

            if (string.IsNullOrEmpty(tagName))
            {
                tagName = tagID;
            }

            TagBuilder select = new TagBuilder("select");
            select.Attributes.Add("id", tagID);
            select.Attributes.Add("name", tagName);

            StringBuilder renderHtmlTag = new StringBuilder();

            IDictionary<string, string> newOptionData = new Dictionary<string, string>();

            if (appendOptionLabel)
            {
                newOptionData.Add(new KeyValuePair<string, string>(optionLabel ?? "請選擇", ""));
            }

            foreach (var item in optionData)
            {
                newOptionData.Add(item.Text, item.Value);
            }

            foreach (var option in newOptionData)
            {
                TagBuilder optionTag = new TagBuilder("option");
                optionTag.Attributes.Add("value", option.Value);

                if (!string.IsNullOrEmpty(defaultSelectValue) && defaultSelectValue.Equals(option.Value))
                {
                    optionTag.Attributes.Add("selected", "selected");
                }

                optionTag.SetInnerText(option.Key);
                renderHtmlTag.AppendLine(optionTag.ToString(TagRenderMode.Normal));
            }

            select.MergeAttributes(new RouteValueDictionary(htmlAttributes));
            select.InnerHtml = renderHtmlTag.ToString();

            return select.ToString();
        }

        /// <summary>
        /// 產生下拉選單html(以IDictionary傳入下拉選單的值).
        /// </summary>
        /// <param name="tagID">拉選單的TagID</param>
        /// <param name="tagName">拉選單的tagName</param>
        /// <param name="optionData">下拉選單Option的Text與Value.</param>
        /// <param name="htmlAttributes">The HTML attributes.</param>
        /// <param name="defaultSelectValue">預選值.</param>
        /// <param name="appendOptionLabel">是否加入預設空白選項.</param>
        /// <param name="optionLabel">如果appendOptionLabel為true,optionLabel為第一個項目要顯示的文字,如果沒有指定則顯示[請選擇].</param>
        /// <returns></returns>
        public static string GetDropdownList(string tagID, string tagName, IQueryable<SelectListItem> optionData, object htmlAttributes, string defaultSelectValue, bool appendOptionLabel, string optionLabel)
        {
            if (string.IsNullOrEmpty(tagID))
            {
                throw new ArgumentNullException("ID", "產生DropDownList時 tag ID 不得為空");
            }

            if (string.IsNullOrEmpty(tagName))
            {
                tagName = tagID;
            }

            TagBuilder select = new TagBuilder("select");
            select.Attributes.Add("id", tagID);
            select.Attributes.Add("name", tagName);
            
            StringBuilder renderHtmlTag = new StringBuilder();

            IDictionary<string, string> newOptionData = new Dictionary<string, string>();

            if (appendOptionLabel)
            {
                newOptionData.Add(new KeyValuePair<string, string>(optionLabel ?? "請選擇", ""));
            }

            foreach (var item in optionData)
            {
                newOptionData.Add(item.Text, item.Value);
            }

            foreach (var option in newOptionData)
            {
                TagBuilder optionTag = new TagBuilder("option");
                optionTag.Attributes.Add("value", option.Value);

                if (!string.IsNullOrEmpty(defaultSelectValue) && defaultSelectValue.Equals(option.Value))
                {
                    optionTag.Attributes.Add("selected", "selected");
                }

                optionTag.SetInnerText(option.Key);
                renderHtmlTag.AppendLine(optionTag.ToString(TagRenderMode.Normal));
            }

            select.MergeAttributes(new RouteValueDictionary(htmlAttributes));
            select.InnerHtml = renderHtmlTag.ToString();

            return select.ToString();
        }

        public static string GetDropdownList(string tagID, string tagName, List<SelectListItem> optionData, object htmlAttributes, string defaultSelectValue, bool appendOptionLabel, string optionLabel, bool IsLivesearch)
        {
            if (string.IsNullOrEmpty(tagID))
            {
                throw new ArgumentNullException("ID", "產生DropDownList時 tag ID 不得為空");
            }

            if (string.IsNullOrEmpty(tagName))
            {
                tagName = tagID;
            }

            TagBuilder select = new TagBuilder("select");
            select.Attributes.Add("id", tagID);
            select.Attributes.Add("name", tagName);
            if (IsLivesearch)
            {
                select.Attributes.Add("date-style", "input-sm");
                select.Attributes.Add("data-size", "auto");
                select.Attributes.Add("data - live - search", "true");
            }

            StringBuilder renderHtmlTag = new StringBuilder();

            IDictionary<string, string> newOptionData = new Dictionary<string, string>();

            if (appendOptionLabel)
            {
                newOptionData.Add(new KeyValuePair<string, string>(optionLabel ?? "請選擇", ""));
            }

            foreach (var item in optionData)
            {
                int newOptionDataCoun = 0;
                newOptionDataCoun = newOptionData.Where(x => x.Key == item.Text).Count();
                if(newOptionDataCoun == 0) {
                newOptionData.Add(item.Text, item.Value);
                }
            }

            foreach (var option in newOptionData)
            {
                TagBuilder optionTag = new TagBuilder("option");
                optionTag.Attributes.Add("value", option.Value);

                if (!string.IsNullOrEmpty(defaultSelectValue) && defaultSelectValue.Equals(option.Value))
                {
                    optionTag.Attributes.Add("selected", "selected");
                }

                optionTag.SetInnerText(option.Key);
                renderHtmlTag.AppendLine(optionTag.ToString(TagRenderMode.Normal));
            }

            select.MergeAttributes(new RouteValueDictionary(htmlAttributes));
            select.InnerHtml = renderHtmlTag.ToString();

            return select.ToString();
        }
    }
}