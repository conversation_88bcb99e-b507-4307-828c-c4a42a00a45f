﻿@model ECOOL_APP.EF.ADDT14
@using EcoolWeb.Util;
@using ECOOL_APP.com.ecool.service
@{
    ViewBag.Title = "校內表現-修改校內表現內容";
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@using (Html.BeginForm("MODIFY", "ADDI06", FormMethod.Post, new { name = "form1", enctype = "multipart/form-data" }))
{
    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })
    @Html.HiddenFor(model => model.IAWARD_ID)
    @Html.HiddenFor(model => model.SCHOOL_NO)
    @Html.HiddenFor(model => model.USER_NO)
    if (ViewBag.backUrl != null)
    {
        TempData["fromSOurce"] = ViewBag.backUrl;
    }
    else
    {
        TempData["fromSOurce"] = "ADDI06";
    }

    <img src="~/Content/img/web-bar2-revise-10.png" style="width:100%" class="img-responsive " alt="Responsive image" />
    <DIV class="Div-EZ-ADDI06">
        <div class="form-horizontal">
            <div class="form-group">
                @Html.LabelFor(model => model.CLASS_NO, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.EditorFor(model => model.CLASS_NO, new { htmlAttributes = new { @class = "form-control", disabled = "disabled" } })
                    @Html.HiddenFor(model => model.CLASS_NO)
                    @Html.ValidationMessageFor(model => model.CLASS_NO, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.USERNAME, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.EditorFor(model => model.USERNAME, new { htmlAttributes = new { @class = "form-control", disabled = "disabled" } })
                    @Html.HiddenFor(model => model.USERNAME)
                    @Html.ValidationMessageFor(model => model.USERNAME, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.IAWARD_KIND, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.EditorFor(model => model.IAWARD_KIND, new { htmlAttributes = new { @class = "form-control", disabled = "disabled" } })
                    @Html.HiddenFor(model => model.IAWARD_KIND)
                    @Html.Label(" ", new { @id = "lbIAWARD_KIND", @style = "color:blue;" })
                    @Html.ValidationMessageFor(model => model.IAWARD_KIND, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.IAWARD_ITEM, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.EditorFor(model => model.IAWARD_ITEM, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.IAWARD_ITEM, "", new { @class = "text-danger" })
                </div>
            </div>
          
            <div class="form-group">
                @Html.LabelFor(model => model.CASH, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.EditorFor(model => model.CASH, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.HiddenFor(model => model.CASH)
                    @Html.ValidationMessageFor(model => model.CASH, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.REMARK, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.EditorFor(model => model.REMARK, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.REMARK, "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="form-group">
                @Html.LabelFor(model => model.IMG_FILE, htmlAttributes: new { @class = "control-label label_dt col-md-2" })
                <div class="col-md-10">
                    <input class="btn btn-default" type="file" name="files" />
                    @Html.ValidationMessage("files", "", new { @class = "text-danger" })
                </div>
            </div>
            @if (!string.IsNullOrWhiteSpace(ViewBag.IMG_FILE))
            {
                <div class="form-group">
                    @Html.Label("原上傳圖", htmlAttributes: new { @class = "control-label label_dt col-md-2" })
                    <div class="col-md-10">
                        @*<img src='@ViewBag.IMG_FILE' class="img-responsive " alt="Responsive image" />*@
                        @Html.HiddenFor(m => m.IMG_FILE)
                        @{

                            string Url = ViewBag.IMG_FILE + "?v=" + DateTime.Now.ToString();
                            string schoolNO = user.SCHOOL_NO;
                            <img src="@Url" class="img-responsive colorbox" alt="Responsive image" href="@Url" style="max-width:300px" id="ImageA1" />
                            <div style="padding-left:15%;padding-top:2%;">
                                @Html.Action("_imagesRotateBtnView", "Comm", new { ImgURL = ViewBag.IMG_FILE, ImgURL_S = "", ImgUrl_M = "", ImgID = "ImageA1", SourceNO = "ADDT14", user_NO = user?.USER_NO, schoo_NO = schoolNO, sourceID = Model.IAWARD_ID })
                            </div>
                        }
                    </div>
                </div>
            }

            <div class="form-group">
                <div class="col-md-offset-3 col-md-3">
                    <button value="Save" class="btn btn-default">
                        確定送出
                    </button>
                </div>
                <div class="col-md-offset-1 col-md-3">
                    <a href='@Url.Action("QUERY", "ADDI06")' class="btn btn-default">
                        放棄編輯
                    </a>
                </div>
            </div>
        </div>
    </DIV>

}

@section Scripts
    {
    <script type="text/javascript">
        $(function () {
            $("#IAWARD_KIND").on('change', function () { 
                IAWARD_KIND_onchange(); 
            });

            // 初始化 colorbox
            $(".colorbox").colorbox({
                opacity: 0.82,
                width: "70%",
                innerHeight: "500px",
                scrolling: true
            });
        });

        function IAWARD_KIND_onchange() {
            var IAWARD_KIND;
            switch ($("#IAWARD_KIND option:selected").text()) {
                case "請選擇事蹟":
                    IAWARD_KIND = '';
                    break;
                case "志工":
                    IAWARD_KIND = '志工類每學期1次每次50-100點';
                    break;
                case "領導人":
                    IAWARD_KIND = '領導人類每學期1次每次50-100點';
                    break;
                case "七個習慣代言人":
                    IAWARD_KIND = '七個習慣代言人類每學期1次每次50-100點';
                    break;
                case "校內競賽":
                    IAWARD_KIND = '校內競賽每次10-50點';
                    break;
                case "品德表現":
                    IAWARD_KIND = '品德表現類10-50點';
                    break;
                case "學習表現":
                    IAWARD_KIND = '學習表現類10-50點';
                    break;
            }

            $("#lbIAWARD_KIND").html(IAWARD_KIND);
        }
    </script>
}