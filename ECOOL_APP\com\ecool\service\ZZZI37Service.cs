﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Transactions;
using ECOOL_APP.EF;

namespace ECOOL_APP.com.ecool.service
{
    public class ZZZI37Service
    {
        public ZZZI37IndexViewModel GetListData(ZZZI37IndexViewModel model, ref ECOOL_DEVEntities db)
        {
            using (TransactionScope ts = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions() { IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted }))
            {
                var QTemp = db.ADDT01.Where(a => a.SCHOOL_NO == model.SCHOOL_NO && a.PUBLISH_CHRIS_YN == SharedGlobal.Y && a.WRITING_STATUS != (byte)ADDStatus.eADDT01Status.Disable);

                if (model.CRE_DATEs != null && model.CRE_DATEs != DateTime.MinValue)
                {
                    QTemp = QTemp.Where(a => a.CRE_DATE >= model.CRE_DATEs);
                }

                if (model.CRE_DATEe != null && model.CRE_DATEe != DateTime.MinValue)
                {
                    QTemp = QTemp.Where(a => a.CRE_DATE <= model.CRE_DATEe);
                }
                if (model.CLASS_NO != null && model.CLASS_NO != "")
                {
                    QTemp = QTemp.Where(a => a.CLASS_NO == model.CLASS_NO);
                }
                QTemp = QTemp.OrderBy(a => a.SCHOOL_NO).ThenBy(a => a.CLASS_NO).ThenBy(a => a.SEAT_NO).ThenBy(a => a.CRE_DATE);

                model.DataList = QTemp.Select(a => new ZZZI37IndexListViewModel
                {
                    WRITING_NO = a.WRITING_NO,
                    SCHOOL_NO = a.SCHOOL_NO,
                    USER_NO = a.USER_NO,
                    CLASS_NO = a.CLASS_NO,
                    SYEAR = a.SYEAR,
                    SEMESTER = a.SEMESTER,
                    SEAT_NO = a.SEAT_NO,
                    NAME = a.NAME,
                    SNAME = a.SNAME,
                    SUBJECT = a.SUBJECT,
                    ARTICLE = a.ARTICLE,
                    ARTICLE_VERIFY = a.ARTICLE_VERIFY,
                    VERIFY_COMMENT = a.VERIFY_COMMENT,
                    IMG_FILE = a.IMG_FILE,
                    VOICE_FILE = a.VOICE_FILE,
                    SHARE_YN = a.SHARE_YN,
                    CASH = a.CASH,
                    READ_COUNT = a.READ_COUNT,
                    WRITING_STATUS = a.WRITING_STATUS,
                    VERIFIER = a.VERIFIER,
                    VERIFIED_DATE = a.VERIFIED_DATE,
                    CRE_DATE = a.CRE_DATE,
                    CHG_DATE = a.CHG_DATE,
                    BACK_MEMO = a.BACK_MEMO,
                    DEL_PERSON = a.DEL_PERSON,
                    DEL_DATE = a.DEL_DATE,
                    PUBLISH_MDNKIDS_YN = a.PUBLISH_MDNKIDS_YN,
                }).ToList();

                return model;
            }
        }
    }
}