﻿@using System.Collections;
@{
    ViewBag.Title = "閱讀護照-閱讀書單";
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }

    List<Hashtable> htbBook = ViewData["BOOK_HTB"] == null ? new List<Hashtable>() : (List<Hashtable>)ViewData["BOOK_HTB"];

    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    string ddlCLASS_NO = string.Empty;
    string ddlGrade = string.Empty;
    string whereKeyword = string.Empty;

    if (user != null)
    {
        ddlCLASS_NO = user.TEACH_CLASS_NO ?? user.CLASS_NO;
        ddlGrade = string.IsNullOrWhiteSpace(ddlCLASS_NO) == false ? new ECOOL_APP.com.ecool.util.StringHelper().StrLeft(ddlCLASS_NO, 1) : "";

        if (user.USER_TYPE == UserType.Student)
        {
           // whereKeyword = user.USER_NO;
        }
    }

    int WhereGRADE = string.IsNullOrWhiteSpace(ddlGrade) == false ? Convert.ToInt32(ddlGrade) : 1;
}

@if (AppMode == false)
{
    @Html.Partial("_Title_Secondary")
}

@if (null != htbBook && htbBook.Count != 0)
 {
    <div class="form-group">
        <a role="button" href='@Url.Action("rpp", "rpp")' class="btn btn-sm btn-sys ">
            護照說明
        </a>

        <a href='@Url.Action("rpp_book", "rpp")' role="button" class="btn btn-sm btn-sys active">
            閱讀書單
        </a>

        @if (string.IsNullOrWhiteSpace(ddlCLASS_NO) == false)
        {
            @Html.ActionLink("本學年護照", "Query3", "ADDI03", new { GRADE = WhereGRADE, QShowYN = "N" }, new { @role = "button", @class = "btn btn-sm btn-sys" })
        }
        
            @Html.ActionLink("護照現況一覽表", "Query3", "ADDI03", new { GRADE = WhereGRADE, whereKeyword = whereKeyword }, new { @role = "button", @class = "btn btn-sm btn-sys" })
        
        <a href='@Url.Action("Query2", "ADDI03",new { ddlGrade= ddlGrade, ddlCLASS_NO= ddlCLASS_NO ,whereKeyword=whereKeyword})' role="button" class="btn btn-sm btn-sys">
            護照完成一覽表
        </a>
        @if (user != null)
        {
            if (user.USER_TYPE == UserType.Student)
            {
                <a role="button" href='@Url.Action("Query4", "ADDI03")' class="btn btn-sm btn-sys">
                    我的護照
                </a>
            }
            else if (user.USER_TYPE == UserType.Parents)
            {
                <a role="button" href='@Url.Action("Query4", "ADDI03")' class="btn btn-sm btn-sys">
                    寶貝護照
                </a>
            }
        }
    </div>
}


<img src="~/Content/img/web-bar2-revise-05.png" style="width:100%" class="img-responsive App_hide" alt="Responsive image" />
<div class="table-responsive">
    <div class="text-center">
        <table class="table-ecool table-92Per table-hover table-ecool-rpp">
            <thead>
                <tr>
                    <th>閱讀年級</th>
                    <th>書本編號</th>
                    <th>書名</th>
                </tr>
            </thead>
                @{
                    if (null != htbBook && htbBook.Count != 0)
                    {
                        foreach (Hashtable htb in htbBook)
                        {
                            string bkColor = string.Empty;
                            switch (htb["GRADE"].ToString())
                            {
                                case "一年級":
                                    bkColor = "#E6F1CC";
                                    break;
                                case "二年級":
                                    bkColor = "#FFF8E0";
                                    break;
                                case "三年級":
                                    bkColor = "#E8E2EF";
                                    break;
                                case "四年級":
                                    bkColor = "#ECFFFF";
                                    break;
                                case "五年級":
                                    bkColor = "#FFF2D5";
                                    break;
                                case "六年級":
                                    bkColor = "#FDEAF2";
                                    break;
                                default:
                                    break;
                            }

                            <tr bgcolor="@bkColor">
                                <td align="center">@htb["GRADE"]</td>
                                <td align="center">@htb["BOOK_ID"]</td>
                                <td align="left">@htb["BOOK_NAME"]</td>
                            </tr>
                        }
                    }
                    else
                    {
                        <tr bgcolor="#F4E0BB">
                            <td align="center" bgcolor="#F4E0BB" colspan="3">查無資料!!</td>
                        </tr>
                    }
                }
         </table>
    </div>
</div>

