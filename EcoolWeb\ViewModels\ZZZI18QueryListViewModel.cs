﻿using ECOOL_APP.com.ecool.Models.entity;
using ECOOL_APP.EF;
using MvcPaging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
namespace EcoolWeb.ViewModels
{
    public class ZZZI18QueryListViewModel
    {
        /// <summary>
        /// 搜尋字串
        /// </summary>
        public string whereKeyword {get;set;}

        /// <summary>
        /// 只顯示某一位學生的文章
        /// </summary>
        public string whereUserNo { get; set; }

        /// <summary>
        /// 排序欄位
        /// </summary>
        public string OrdercColumn { get; set; }

        public int Page { get; set; }



        /// <summary>
        /// 查詢結果
        /// </summary>
        public IPagedList<uZZZI18> uZZZI18List;

        public ZZZI18QueryListViewModel()
        {
            Page = 0;
            OrdercColumn = "CRE_DATE";
        }
    }
}