﻿using ECOOL_APP.EF;
using MvcPaging;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.com.ecool.Models.DTO
{
    public class APPT03QueryViewModel
    {

        /// <summary>
        /// 來源TABLE
        /// </summary>
        public string REF_TABLE { get; set; }

        /// <summary>
        /// 來源KEY 
        /// </summary>
        public string REF_KEY { get; set; }


        /// <summary>
        /// TABLE 資料狀態 
        /// </summary>
        public byte STATUS { get; set; }

        /// <summary>
        /// 點選的功能類別
        /// </summary>
        public string BTN_TYPE { get; set; }

  

        /// <summary>
        /// 學校
        /// </summary>
        [DisplayName("學校")]
        public string whereSCHOOL_NO { get; set; }


        /// <summary>
        /// 系統角色
        /// </summary>
        [DisplayName("系統角色")]
        public string whereUSER_TYPE { get; set; }


        /// <summary>
        /// 學號/帳號
        /// </summary>
        [DisplayName("學號/帳號")]
        public string whereUSER_NO { get; set; }

        /// <summary>
        /// 班級
        /// </summary>
        [DisplayName("班級")]
        public string whereCLASS_NO { get; set; }


        /// <summary>
        /// 年級
        /// </summary>
        [DisplayName("年級")]
        public string whereGRADE { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        [DisplayName("姓名")]
        public string whereNAME { get; set; }


        //系統角色清單
        public List<string> USER_TYPE_LIST { get; set; }

        //學校角色清單
        public List<HRMT24> Role_LIST { get; set; }

        /// <summary>
        /// 年級
        /// </summary>
        public List<byte> Grade_LIST { get; set; }

        /// <summary>
        /// 班級
        /// </summary>
        public List<string> Class_LIST { get; set; }

        /// <summary>
        /// 人員清單
        /// </summary>
        public IPagedList<APPT03Hrmt01ListViewModel> HRMT01List { get; set; }

        /// <summary>
        /// 排序欄位
        /// </summary>
        public string OrderByName { get; set; }

        /// <summary>
        /// Desc or ASC
        /// </summary>
        public string SyntaxName { get; set; }

        /// <summary>
        /// 頁次
        /// </summary>
        public int Page { get; set; }

        /// <summary>
        /// 一頁多少筆
        /// </summary>
        public int PageSize { get; set; }

        [DisplayName("學號")]
        public string USER_NO { get; set; }

        [DisplayName("班級")]
        public string CLASS_NO { get; set; }

        [DisplayName("年級")]
        public string SYEAR { get; set; }

        [DisplayName("姓名")]
        public string NAME { get; set; }

        public APPT03QueryViewModel()
        {
            REF_TABLE = "APPT03";
            STATUS = APPT03.StatusVal.TempKey;
            BTN_TYPE = APPT03_Q.BTN_TYPE_VAL.role;
            Page = 1;
            PageSize = 10;
            OrderByName = "";
            SyntaxName = "ASC";
        }

    
    }
}
