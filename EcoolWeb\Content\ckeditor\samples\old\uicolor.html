<!DOCTYPE html>
<!--
Copyright (c) 2003-2015, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
-->
<html>
<head>
	<meta charset="utf-8">
	<title>UI Color Picker &mdash; CKEditor Sample</title>
	<script src="../../ckeditor.js"></script>
	<link rel="stylesheet" href="sample.css">
</head>
<body>
	<h1 class="samples">
		<a href="index.html">CKEditor Samples</a> &raquo; UI Color
	</h1>
	<div class="warning deprecated">
		This sample is not maintained anymore. Check out its <a href="http://sdk.ckeditor.com/samples/uicolor.html">brand new version in CKEditor SDK</a>.
	</div>
	<div class="description">
		<p>
			This sample shows how to automatically replace <code>&lt;textarea&gt;</code> elements
			with a CKEditor instance with an option to change the color of its user interface.<br>
			<strong>Note:</strong>The UI skin color feature depends on the CKEditor skin
			compatibility. The Moono and Kama skins are examples of skins that work with it.
		</p>
	</div>
	<form action="sample_posteddata.php" method="post">
	<p>
		This editor instance has a UI color value defined in configuration to change the skin color,
		To specify the color of the user interface, set the <code>uiColor</code> property:
	</p>
	<pre class="samples">
CKEDITOR.replace( '<em>textarea_id</em>', {
	<strong>uiColor: '#14B8C4'</strong>
});</pre>
	<p>
		Note that <code><em>textarea_id</em></code> in the code above is the <code>id</code> attribute of
		the <code>&lt;textarea&gt;</code> element to be replaced.
	</p>
	<p>
		<textarea cols="80" id="editor1" name="editor1" rows="10">&lt;p&gt;This is some &lt;strong&gt;sample text&lt;/strong&gt;. You are using &lt;a href="http://ckeditor.com/"&gt;CKEditor&lt;/a&gt;.&lt;/p&gt;</textarea>
		<script>

			// Replace the <textarea id="editor"> with an CKEditor
			// instance, using default configurations.
			CKEDITOR.replace( 'editor1', {
				uiColor: '#14B8C4',
				toolbar: [
					[ 'Bold', 'Italic', '-', 'NumberedList', 'BulletedList', '-', 'Link', 'Unlink' ],
					[ 'FontSize', 'TextColor', 'BGColor' ]
				]
			});

		</script>
	</p>
	<p>
		<input type="submit" value="Submit">
	</p>
	</form>
	<div id="footer">
		<hr>
		<p>
			CKEditor - The text editor for the Internet - <a class="samples" href="http://ckeditor.com/">http://ckeditor.com</a>
		</p>
		<p id="copy">
			Copyright &copy; 2003-2015, <a class="samples" href="http://cksource.com/">CKSource</a> - Frederico
			Knabben. All rights reserved.
		</p>
	</div>
</body>
</html>
