﻿@model GameScoreListsViewModel
@{
    ViewBag.Title = ViewBag.Panel_Title;
    Layout = "~/Views/Shared/_LayoutGame.cshtml";
    EcoolWeb.Models.UserProfileHelper.SetLayoutCookieData(Layout);
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@{
    if (!string.IsNullOrWhiteSpace(Model?.Search?.WhereGAME_NO))
    {
        Html.RenderAction("_ByOneGameMenu", new { NowAction = "ScoreLists" });
    }
}

@using (Html.BeginForm("ScoreLists", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1", enctype = "multipart/form-data" }))
{
    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })
    <div id="PageContent">
        @Html.Action("_ScorePersons", (string)ViewBag.BRE_NO, Model)
    </div>
}

@section Scripts {
    <script language="JavaScript">
    var targetFormID = '#form1'

        function doSort(SortCol) {

            var SyntaxName =  $('#@Html.IdFor(m=>m.SyntaxName)').val();
            var OrdercColumn = $('#@Html.IdFor(m=>m.OrdercColumn)').val();

            if (OrdercColumn == SortCol) {
                if (SyntaxName=="DESC") {
                    $('#@Html.IdFor(m=>m.SyntaxName)').val('ASC')
                }
                else {
                    $('#@Html.IdFor(m=>m.SyntaxName)').val('DESC')
                }
            }
            else {
                 $('#@Html.IdFor(m=>m.SyntaxName)').val('DESC')
            }

            $('#@Html.IdFor(m=>m.OrdercColumn)').val(SortCol);
            FunPageProc(1)
        }

        //分頁
         function FunPageProc(page) {
            if ($(targetFormID).size() > 0) {
                $('#@Html.IdFor(m=>m.Page)').val(page)
                funAjax()
            }
        };

         //查詢
        function funAjax() {
            $.ajax({
                url: '@Url.Action("_ScorePersons", (string)ViewBag.BRE_NO)',
                data: $(targetFormID).serialize(),
                type: 'post',
                async: false,
                cache: false,
                dataType: 'html',
                success: function (data) {
                    $('#PageContent').html(data);
                }
            });
        }

        function todoClear() {
            ////重設

            $('#Q_Div').find(":input,:selected").each(function (i) {

                var type = $(this).attr('type')
                var InPreadonly = $(this).attr('readonly')
                var tag = this.tagName.toLowerCase(); // normalize case

                if (InPreadonly == false || InPreadonly == undefined) {

                    if (type == 'radio' || type == 'checkbox') {
                        if ($(this).attr("title") == 'Default') {
                            this.checked = true;
                        }
                        else {
                            this.checked = false;
                        }
                    }
                    else if (tag == 'select') { //下拉式選單
                        this.selectedIndex = 0;
                    }
                    else if (type == 'text' || type == 'hidden' || type == 'password' || type == 'textarea') {
                        this.value = '';
                    }
                }
            });

            $(targetFormID).submit();
        }
    </script>
}