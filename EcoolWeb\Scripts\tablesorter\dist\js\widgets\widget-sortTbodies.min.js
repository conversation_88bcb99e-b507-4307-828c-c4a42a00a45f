(function(factory){if (typeof define === 'function' && define.amd){define(['jquery'], factory);} else if (typeof module === 'object' && typeof module.exports === 'object'){module.exports = factory(require('jquery'));} else {factory(jQuery);}}(function(jQuery){

/*! tablesorter tbody sorting widget (BETA) - 11/26/2016 (v2.28.0)
 * Requires tablesorter v2.22.2+ and jQuery 1.4+
 * by <PERSON>
 * Contributors: <PERSON>
 */
!function(x){"use strict";var q=x.tablesorter;q.sortTbodies={init:function(o,r){var t,e,s,n,i,d=o.namespace+"sortTbody",a=o.$table.children("tbody"),b=a.length;for(r.sortTbody_original_serverSideSorting=o.serverSideSorting,r.sortTbody_original_cssInfoBlock=o.cssInfoBlock,o.cssInfoBlock=r.sortTbody_noSort,q.sortTbodies.setTbodies(o,r),t=0;t<b;t++)a.eq(t).attr("data-ts-original-order",t);for(o.$table.unbind("sortBegin updateComplete ".split(" ").join(d+" ")).bind("sortBegin"+d,function(){q.sortTbodies.sorter(o)}).bind("updateComplete"+d,function(){q.sortTbodies.setTbodies(o,r),q.updateCache(o,null,o.$tbodies)}).bind("sortEnd",function(){var t=r.sortTbody_primaryRow;r.sortTbody_lockHead&&t&&o.$table.find(t).each(function(){x(this).parents("tbody").prepend(this)})}),(x.isEmptyObject(o.parsers)||o.$tbodies.length!==a.length)&&(q.sortTbodies.setTbodies(o,r),q.updateCache(o,null,o.$tbodies)),b=(i=a.children("tr")).length,t=0;t<o.columns;t++){if(n=0,"numeric"===o.parsers[t].type)for(e=0;e<b;e++)s=q.getParsedText(o,i.eq(e).children()[t],t),n=Math.max(Math.abs(s)||0,n);o.$headerIndexed[t].attr("data-ts-col-max-value",n)}},setTbodies:function(t,o){t.$tbodies=t.$table.children("tbody").not("."+o.sortTbody_noSort)},sorter:function(m){var t=m.$table,o=m.widgetOptions;if(!0!==o.sortTbody_busy){o.sortTbody_busy=!0;var r=t.children("tbody").not("."+o.sortTbody_noSort),S=o.sortTbody_primaryRow||"tr:eq(0)",v=m.sortList||[],$=v.length;$&&(m.serverSideSorting=!o.sortTbody_sortRows,r.sort(function(t,o){var r,e,s,n,i,d,a,b,l,c,y,T=m.table,p=m.parsers,f=m.textSorter||"",u=x(t),g=x(o),h=u.find(S).children("td, th"),_=g.find(S).children("td, th");for(r=0;r<$;r++){if(a=v[r][0],s=0===v[r][1],e=q.getElementText(m,h.eq(a),a),b=p[a].format(e,T,h[a],a),e=q.getElementText(m,_.eq(a),a),l=p[a].format(e,T,_[a],a),m.sortStable&&b===l&&1===$)return u.attr("data-ts-original-order")-g.attr("data-ts-original-order");if(d=(n=/n/i.test(p&&p[a]&&p[a].type||""))&&m.strings[a]?(i=m.$headerIndexed[a].attr("data-ts-col-max-value")||179e306,n="boolean"==typeof q.string[m.strings[a]]?(s?1:-1)*(q.string[m.strings[a]]?-1:1):m.strings[a]&&q.string[m.strings[a]]||0,m.numberSorter?m.numberSorter(b,l,s,i,T):q["sortNumeric"+(s?"Asc":"Desc")](b,l,n,i,a,m)):(c=s?b:l,y=s?l:b,"function"==typeof f?f(c,y,s,a,T):"object"==typeof f&&f.hasOwnProperty(a)?f[a](c,y,s,a,T):q["sortNatural"+(s?"Asc":"Desc")](b,l,a,m)))return d}return u.attr("data-ts-original-order")-g.attr("data-ts-original-order")}),q.sortTbodies.restoreTbodies(m,o,r),o.sortTbody_busy=!1)}},restoreTbodies:function(t,o,r){var e,s,n,i,d,a,b,l=t.$table,c=!0,y=0;if(l.hide(),r.appendTo(l),i=(s=l.children("tbody")).length,d=(e=s.filter("."+o.sortTbody_noSort).appendTo(l)).length)for(;c&&y<d;){for(c=!1,a=0;a<d;a++)(b=i<=(b=parseInt(e.eq(a).attr("data-ts-original-order"),10))?i:b<0?0:b)!==e.eq(a).index()&&(c=!0,n=e.eq(a).detach(),i<=b?n.appendTo(l):0===b?n.prependTo(l):n.insertBefore(l.children("tbody:eq("+b+")")));y++}l.show()}},q.addWidget({id:"sortTbody",priority:40,options:{sortTbody_lockHead:!1,sortTbody_primaryRow:null,sortTbody_sortRows:!1,sortTbody_noSort:"tablesorter-no-sort-tbody"},init:function(t,o,r,e){q.sortTbodies.init(r,e)},remove:function(t,o,r){o.$table.unbind("sortBegin updateComplete ".split(" ").join(o.namespace+"sortTbody ")),o.serverSideSorting=r.sortTbody_original_serverSideSorting,o.cssInfoBlock=r.sortTbody_original_cssInfoBlock}})}(jQuery);return jQuery;}));
