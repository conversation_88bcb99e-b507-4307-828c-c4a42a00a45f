//bootstrap v4.1.3 設置
//BS4參數調整
$font-family-sans-serif: "微軟正黑體",
// Safari for OS X and iOS (San Francisco)
-apple-system,
// Chrome < 56 for OS X (San Francisco)
BlinkMacSystemFont,
// Windows
"Segoe UI",
// Android
"Roboto",
// Basic web fallback
"Helvetica Neue",
Arial,
sans-serif,
// Emoji fonts
"Apple Color Emoji",
"Segoe UI Emoji",
"Segoe UI Symbol";
// $font-size-base: 1rem;
$container-max-widths: (sm: 960px,
  md:960px,
  lg: 960px,
  xl: 1140px);

$primary: #eb6100;
$secondary: #fbf5ca;
$yellow: #fcfb84;
$third: #f8b551;
$th-color: #7f5100;
$th-bgcolor: #ffebb7;
$td-bgcolor1: #fef3da;
$td-bgcolor2: #fef7e8;
$theme-colors: ("third": $third);

.btn-grey {
  background-color: #A0A0A0;
  color: #fff;
}

@import "learning-history/bs4-custom";

//----客製化---//
//元件模組
@import "learning-history/_typography.scss";
@import "learning-history/_table.scss";

body {
  background-color: #eee;
  word-break: break-all;
}

// 列印的固定按鈕
.fixed-btn {
  position: fixed;
  top: 0.5rem;
  right: 0.5rem;
  z-index: 1000;
}

//列印及另存按鈕
.btn-html5 {
  &::before {
    content: "";
    display: inline-block;
    height: 1.3em;
    width: 1.3em;
    line-height: 1.5;
    vertical-align: text-bottom;
    background-image: url("data:image/svg+xml,%3C%3Fxml version='1.0'%3F%3E%3Csvg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' xmlns:svgjs='http://svgjs.com/svgjs' version='1.1' width='512' height='512' x='0' y='0' viewBox='0 0 32 32' style='enable-background:new 0 0 512 512' xml:space='preserve' class=''%3E%3Cg%3E%3Cg xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='m6.34033 26.87341 9.25934 3.06952c.2663.07605.54541.07605.79907 0l9.27203-3.06952c.48206-.16492.82446-.59613.86255-1.11621l1.71228-22.38733c.06342-.73565-.52001-1.36987-1.26837-1.36987h-21.95611c-.73566 0-1.31909.63422-1.26837 1.36987l1.72497 22.38733c.03809.52008.38062.9513.86261 1.11621zm17.35175-19.63488v2.9173h-11.88495l.4693 3.19641h11.08587l-.62152 9.513-6.78601 2.30853-6.51959-2.35931-.34247-4.84528h3.38666v2.38458l3.80518.95129 3.39935-1.09082.40588-3.72906h-11.20001l-.68494-9.24664z' fill='%23ffffff' data-original='%23000000' class=''/%3E%3C/g%3E%3C/g%3E%3C/svg%3E%0A");
    background-repeat: no-repeat;
    background-position: center center;
    background-size: 100% auto;
  }
}

.btn-print {
  &::before {
    content: "";
    display: inline-block;
    height: 1.3em;
    width: 1.3em;
    line-height: 1.5;
    vertical-align: text-bottom;
    background-image: url("data:image/svg+xml,%3C%3Fxml version='1.0'%3F%3E%3Csvg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' xmlns:svgjs='http://svgjs.com/svgjs' version='1.1' width='512' height='512' x='0' y='0' viewBox='0 0 512 512' style='enable-background:new 0 0 512 512' xml:space='preserve' class=''%3E%3Cg%3E%3Cg xmlns='http://www.w3.org/2000/svg'%3E%3Cg%3E%3Cpath d='M472.178,133.907h-54.304V35.132c0-9.425-7.641-17.067-17.067-17.067H111.193c-9.425,0-17.067,7.641-17.067,17.067v98.775 H39.822C17.864,133.907,0,151.772,0,173.73v171.702c0,21.958,17.864,39.822,39.822,39.822h54.306v91.614 c0,9.425,7.641,17.067,17.067,17.067h289.61c9.425,0,17.067-7.641,17.067-17.067v-91.614h54.306 c21.958,0,39.822-17.864,39.822-39.822V173.73C512,151.773,494.136,133.907,472.178,133.907z M128.259,52.199h255.482v81.708 H128.259V52.199z M383.738,459.801H128.262c0-3.335,0-135.503,0-139.628h255.477C383.738,324.402,383.738,456.594,383.738,459.801 z M400.807,234.122h-43.443c-9.425,0-17.067-7.641-17.067-17.067s7.641-17.067,17.067-17.067h43.443 c9.425,0,17.067,7.641,17.067,17.067S410.234,234.122,400.807,234.122z' fill='%23ffffff' data-original='%23000000' class=''/%3E%3C/g%3E%3C/g%3E%3Cg xmlns='http://www.w3.org/2000/svg'%3E%3C/g%3E%3Cg xmlns='http://www.w3.org/2000/svg'%3E%3C/g%3E%3Cg xmlns='http://www.w3.org/2000/svg'%3E%3C/g%3E%3Cg xmlns='http://www.w3.org/2000/svg'%3E%3C/g%3E%3Cg xmlns='http://www.w3.org/2000/svg'%3E%3C/g%3E%3Cg xmlns='http://www.w3.org/2000/svg'%3E%3C/g%3E%3Cg xmlns='http://www.w3.org/2000/svg'%3E%3C/g%3E%3Cg xmlns='http://www.w3.org/2000/svg'%3E%3C/g%3E%3Cg xmlns='http://www.w3.org/2000/svg'%3E%3C/g%3E%3Cg xmlns='http://www.w3.org/2000/svg'%3E%3C/g%3E%3Cg xmlns='http://www.w3.org/2000/svg'%3E%3C/g%3E%3Cg xmlns='http://www.w3.org/2000/svg'%3E%3C/g%3E%3Cg xmlns='http://www.w3.org/2000/svg'%3E%3C/g%3E%3Cg xmlns='http://www.w3.org/2000/svg'%3E%3C/g%3E%3Cg xmlns='http://www.w3.org/2000/svg'%3E%3C/g%3E%3C/g%3E%3C/svg%3E%0A");
    background-repeat: no-repeat;
    background-position: center center;
    background-size: 100% auto;
  }
}

//PC頁寬
.container {
  padding: 0;
  margin: 0 auto;
  width: 24cm;
  background-color: #fff;
  box-shadow: 0 0 6px rgba(0, 0, 0, .05);
}

//封面
.cover {
  img {
    margin: 0;
    width: 100%;
    max-height: 100%;
    position: relative;
  }
}

//空白蝴蝶頁
.print-page {
  min-height: 100vh;

  .slogan {
    display: block;
    margin-top: 50vh;
    text-align: center;
    font-size: 31pt;
    color: #1275b2;
  }
}

//通用顏色與樣式
h1 {
  font-weight: bolder;
  letter-spacing: 0.5rem;
}

h2,
h3 {
  font-weight: bold;
  letter-spacing: 0.25rem;
}

p {
  font-size: 1.25rem;
}

section {
  margin: 0;
}



// 主要黃色背景
.bg-custom-yellow {
  background-color: $secondary;
  border: 2pt solid $yellow;
  height: auto;
}

// 橘色標題下方的白底基本設定
.bg-custom-white {
  background-color: $white;
  border: 2pt solid $third;
  margin: 0 auto;
  border-radius: 0.25rem;
}



// 橘色標題樣式
.heading-h2 {
  position: relative;
  margin: 0 auto;
  text-align: center;
  background-color: $primary;
  color: $white;
  border-radius: 0.5rem;
  z-index: 2;
}


// 背景位置
.bgPosition {
  margin-top: 2rem;

  &-profile {
    margin-top: -2cm;

    .bg-custom-white {
      margin-bottom: 1.5rem;
      margin-top: -2pt;
      min-height: 7.5cm;

      &:last-child {
        margin-bottom: 2rem;
      }
    }
  }

  .heading-h2 {
    &:first-child {
      margin-top: -1.5rem;
    }
  }

  .heading-h2+.bg-custom-white {
    margin-top: -2pt;
    margin-bottom: 1rem;
  }

  .heading-h2+.break-avoid {
    margin-top: -2pt;
    margin-bottom: 1rem;
  }

}

.profile {

  //最上面的裝飾
  >img:first-child {
    display: inline-block;
    position: relative;
    z-index: 1;
    margin-top: 1rem;
    width: 100%;
  }

  .img-outBox {
    position: relative;

    img[alt^="decoration"] {
      display: block;

      &[alt*="giraffe"] {
        width: 80%;
        transform: translate(10%, 12%);
      }

      &[alt*="boy"] {
        width: 80%;
        transform: translate(10%, 70%);
      }
    }
  }

  .bg-custom-white {
    margin-top: -0.5cm;
    padding-top: 0.5cm;
  }

  .profilePhoto {
    width: 280px;
    height: 280px;
    border: 2pt dashed #d1c0a5;
    border-radius: 1rem;
    overflow: hidden;
    transform: translate(10%, 8%);

    >img {
      object-fit: cover;
      height: 100%;
      width: 100%;
    }
  }

  .table th,
  .table td {
    padding: 0.425rem;
  }
}

.record {
  .bg-custom-yellow {
    background-color: #fffcda;
    margin: 0;
  }

  .heading-h2 {
    width: 100%;
  }

  .profilePhoto {
    width: 200px;
    height: 200px;
    border-radius: 1rem;
    overflow: hidden;
    margin: 0 auto;
    margin-bottom: 1rem;

    >img {
      object-fit: cover;
      height: 100%;
      width: 100%;
    }
  }

  h3 {
    background-color: #45b1be;
    color: $white;
    text-align: center;
    width: 30%;
    margin: 0 auto;
    padding: 0.5rem;
    margin-top: 1rem;
    border-radius: 2rem;
  }

  .readingAward,
  .readingPassportAward {
    flex-wrap: wrap;

    img {
      width: 8%;
      height: auto;
      margin-bottom: 1rem;
    }
  }
}


// 榮譽表現
.honorArea {
  padding-top: 1rem;

  &:last-child {
    padding-bottom: 2rem;
  }

  .honorPaper {
    height: 210px;
    width: 210px;
    display: flex;
    justify-content: center;
    align-items: center;

    img {
      object-fit: contain;
      object-position: 50% 50%;
      width: auto;
      height: auto;
      max-width: 100%;
      max-height: 100%;
    }
  }
}

//兌換獎品示圖
.img-exchange-with {
  width: 100%;
  max-width: 70px;
  max-height: 70px;
}

// 線上投稿、閱讀認證共同樣式
.onlineSubmit,
.readingCertification {
  .break-avoid+.break-avoid {
    padding-top: 4rem;
  }

  .bg-custom-white {
    margin-bottom: 0.5rem;
  }

  .bg-custom-white+.content {
    width: 96%;
    margin: 0.5cm auto;
    margin-bottom: 0rem;

    .col-12 {
      text-align: center;
      padding-left: 0.25rem;
      padding-right: 0.25rem;

      p {
        text-align: left;
      }

      .grid {
        .grid-item:only-of-type {
          width: 60%;
          left: 50% !important;
          transform: translateX(-50%);

          img {
            max-width: 100%;
          }
        }
      }

      & :only-child {
        .grid-item:only-of-type {
          width: 100%;

          img {
            max-width: 100%;
          }
        }
      }
    }
  }

  .break-avoid:last-child {
    padding-bottom: 2rem;
  }
}

// 線上投稿
.onlineSubmit {
  .bg-custom-white+.content {
    .grid {

      .grid-sizer,
      .grid-item {
        width: calc(33.3333% - 15px);
      }

      .grid-item {
        img {
          max-width: 100%;
          margin-bottom: 15px;
        }
      }
    }

    hr {
      border-top: 1px dotted $th-color;
    }
  }
}


.waterfall {
  &-imgs {
    display: grid;
    grid-gap: 10px;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    grid-auto-rows: 20px;
    clear: both;
    min-height: 472px;
    page-break-inside: avoid;
  }

  &-item {
    height: 250px;
    img {
      display: block;
      max-width: 100%;
      max-height: 100%;
      height: auto;
      width: auto;
      float: left;
      page-break-inside: avoid;
    }
  }

}
.waterfall {
  &-imgs-big {
    display: grid;
    grid-gap: 10px;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    grid-auto-rows: 20px;
    clear: both;
    min-height: 500px;
    page-break-inside: avoid;
    .waterfall-item{
      height: 500px;
    }
  }
}
@for $i from 1 through 30 {
  .waterfall-item[grid-fr="#{$i}"] {
    grid-row-end: span $i;    
  }
}
// 閱讀認證
.readingCertification {
  .bg-custom-white+.content {
    .col-12 {
      .readingPaper {
        margin: 0 auto;
        width: 60%;

        :only-child {
          max-width: 100%;
        }
      }

      .readingPaper:only-child {
        width: 100%;

        :only-child {
          max-width: 100%;
          height: auto;
        }
      }
    }
  }
}

// .readingCertification {

// }

// 線上藝廊
.onlineGallery {
  .galleryAll {
    width: 100%;
    text-align: center;

    .gallery-item {
      width: calc(50% - 2rem);
      padding: 1rem 0.25rem;
      display: inline-block;
      vertical-align: text-top;

      .card {
        border: 1px solid #eee;
        border-radius: 0.25rem;
        width: 100%;

        img {
          max-width: 100%;
        }

        &-body {
          padding: 0.5rem;

          .card-title {
            text-align: left;
          }
        }
      }
    }
  }
}

//圖表
.charts {
  overflow: auto;
}

// 列印樣式
@import "learning-history/_print.scss";