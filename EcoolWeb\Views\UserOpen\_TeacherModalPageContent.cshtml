﻿@model TeacherModalViewModel

@Html.HiddenFor(m => m.OrderByColumnName)
@Html.HiddenFor(m => m.SortType)
@Html.HiddenFor(m => m.Page)
@Html.HiddenFor(m => m.DivEditorRowsID)
@Html.HiddenFor(m => m.UrlAction)

<div id="TeacherModaleditorRows" style="display:none">
    @if (Model.oTeachers != null)
    {
        foreach (var Item in Model.oTeachers)
        {
            @Html.Partial("_DetailsTeacher", Item)
        }
    }
</div>

<div class="row">
    <div class="col-lg-6">
        <div class="input-group">
            @Html.EditorFor(m => m.SearchContent, new { htmlAttributes = new { @class = "form-control", @placeholder = $"姓名" } })
            <span class="input-group-btn">
                <button class="btn btn-default" type="button" onclick="FunPageProc(1)">搜尋</button>
            </span>
        </div><!-- /input-group -->
    </div><!-- /.col-lg-6 -->
</div><!-- /.row -->

<div class="panel panel-ZZZ" name="TOP">
    <div class="table-responsive">
        <table class="table-ecool table-92Per table-hover">
            <thead>
                <tr>
                    <th class="text-nowrap">@Html.DisplaySortColumnFor(m => m.Teachers.First().NAME, Model?.OrderByColumnName, Model?.SortType, true)</th>
                    <th style="width:60px"></th>
                </tr>
            </thead>
            <tbody>
                @if (Model.Teachers?.Count() > 0)
                {
                    foreach (var item in Model.Teachers)
                    {

                        <tr id="TrDetails@(item.USER_KEY)">
                            <td>
                                @Html.DisplayFor(modelItem => item.NAME)
                            </td>

                            <td class="text-nowrap">
                                <a role="button" class="btn btn-xs btn-Basic" title="加入"
                                   onclick="AddRoTeacher('@(item.USER_KEY)')">
                                    <span class="fa fa-plus" aria-hidden="true"></span>加入
                                </a>
                            </td>
                        </tr>
                    }
                }
            </tbody>
        </table>
    </div>
</div>

@Html.Pager(Model.Teachers.PageSize, Model.Teachers.PageNumber, Model.Teachers.TotalItemCount).Options(o => o.DisplayTemplate(PageGlobal.DfDisplayTemplate)
    .MaxNrOfPages(PageGlobal.DfMaxNrOfPages)
    .SetPreviousPageText(PageGlobal.DfSetPreviousPageText)
    .SetNextPageText(PageGlobal.DfSetNextPageText)
    )