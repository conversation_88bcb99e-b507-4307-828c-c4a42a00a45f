﻿@model SECI01MyPhotoViewModel
@{
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";

    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

}
<link href="~/Content/colorbox/example2/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>

@Html.Partial("_Notice")

@using (Html.BeginForm("SavePhoto", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "formEdit", name = "formEdit", enctype = "multipart/form-data" }))
{
    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })

    <div class="bar-div">
        @if (user.USER_TYPE == UserType.Student)
        {
            <div style="padding-top:2%;">
                <div class="form-group">
                    <label class="col-md-12 control-label text-primary">
                        入學學年
                    </label>
                    <div class="col-md-12">
                        @Html.DisplayFor(m => m.SYEAR_START)
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-12 control-label text-primary">
                        畢業學年
                    </label>
                    <div class="col-md-12">
                        @Html.DisplayFor(m => m.SYEAR_END)
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-12 control-label text-primary">
                        我的班級
                    </label>
                    <div class="col-md-12">
                        @Html.DisplayFor(m => m.CLASSNAME)
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-12 control-label text-primary">
                        我的座號
                    </label>
                    <div class="col-md-12">
                        @Html.DisplayFor(m => m.SEAT_NO)
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-12 control-label text-primary">
                        我的班級導師
                    </label>
                    <div class="col-md-12">
                        @Html.DisplayFor(m => m.CurrentClassTeacherName) 老師
                    </div>
                </div>
            </div>
        }

        @Html.HiddenFor(m => m.PHOTO)
        <div style="height:15px"></div>
        <div class="form-group">
            <label class="col-md-12 control-label">
                <font color="red">*</font>
                上傳個人照片
            </label>
            <div class="col-md-12">
                @Html.TextBoxFor(m => m.File, new { @class = "form-control input-md", @type = "file" })
                @Html.ValidationMessageFor(m => m.File, "", new { @class = "text-danger" })
                <br />
                <label class="text-info">
                    PS.<br />
                    1.請上傳 jpg、jpeg、png、gif、bmp 等格式的圖片<br />
                    2.本張照片會出現在「e本書的基本資料裡」，也會出現在「APP時光寶盒的首頁」，建議用自己喜歡的照片，照片直式、橫式不拘。
                </label>
            </div>
        </div>
        <div style="height:15px"></div>
        <div class="text-center">

            <button id="BtnSave" class="btn btn-default" type="button" onclick="onSave()">上傳</button>
        </div>
        <div style="height:15px"></div>

        <div class="row">
            <div class="col-xs-10 col-md-10">
                <div class="thumbnail" style="height:470px;display:flex;align-items:center;justify-content:center;">
                    @if (!string.IsNullOrWhiteSpace(Model.PHOTO))
                    {
                        <a role='button' onclick="DelFile()" title="刪除"> <i class='glyphicon glyphicon-remove'></i></a>


                        <img src="@(Model.PhotoPath + "?refreshCache=" + DateTime.Now.ToString("mmddss"))" class="img-responsive colorbox" id="ImageX@(Model.PHOTO_NO)" alt="Responsive image" href="@Model.PhotoPath" style="max-height:455px;" />
                        <div style="padding-left:15%;padding-top:2%;">
                            @Html.Action("_imagesRotateBtnView", "Comm", new { ImgURL = Model.PhotoPath, ImgID = "ImageX" + Model.PHOTO_NO })
                        </div>
                    }
                    else
                    {
                        <div style="background-color:darkgray;width:315px;height:455px;text-align: center;line-height:455px;">寬 450px X 高 600px  </div>
                    }
                </div>
            </div>
        </div>
    </div>

}

<script language="JavaScript">
    var targetFormID = '#formEdit';
    $(".colorbox").colorbox({ iframe: true, width: "90%", height: "90%", opacity: 0.82 });
    $(function () {
       
        $('#cboxClose').click(function () {
            window.parent.location.reload(true);

        });
        $('#cboxContent').click(function () {
            window.parent.location.reload(true);

        });
     
    });
    function onSave() {
        $('#BtnSave').attr('disabled', 'disabled').html("Loading...資料處理中…請勿其他動作");
        $(targetFormID).submit();
    }

    function DelFile()
    {
         $(targetFormID).attr("action", "@Url.Action("DelPhoto", (string)ViewBag.BRE_NO)")
         $(targetFormID).submit();
    }
</script>