﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;
using com.ecool.service;
using Dapper;
using ECOOL_APP;
using ECOOL_APP.com.ecool.ResultService;
using ECOOL_APP.com.ecool.service;
using ECOOL_APP.EF;
using EcoolWeb.CustomAttribute;
using EcoolWeb.Models;
using log4net;

namespace EcoolWeb.Controllers
{
    [SessionExpire]
    public class ADDI13Controller : Controller
    {
        public static ILog logger = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        /// <summary>
        /// 此Controller 對應的程式代碼
        /// </summary>
        private static string Bre_NO = "ADDI13";

        private string Bre_Name = BreadcrumbService.GetBRE_NAME_forBRE_NO(Bre_NO);

        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();

        private bool IsAdmin = false;

        private string SCHOOL_NO = string.Empty;

        private ADDI13Service Service = new ADDI13Service();

        /// <summary>
        /// 會員相關資訊
        /// </summary>
        private UserProfile user;

        private string USER_NO = string.Empty;

        /// <summary>
        /// 列表
        /// </summary>
        /// <returns></returns>
        [CheckPermission(CheckACTION_ID = "RollCallIndex")] //檢查權限
        public ActionResult RollCallIndex()
        {
            this.Shared();
            return View();
        }
        /// <summary>
        /// 列表
        /// </summary>
        /// <returns></returns>
        [CheckPermission(CheckBRE_NO = "ADDI131", CheckACTION_ID = "BarcodeRollCallIndex")] //檢查權限
        public ActionResult BarcodeRollCallIndex()
        {
            this.Shared();
            return View();
        }
        
        [CheckPermission(CheckACTION_ID = "RollCallIndex")] //檢查權限
        public ActionResult _PageContent(RollCallIndexViewModel model)
        {
            this.Shared();
            ViewBag.Permission = PermissionService.GetActionPermissionForBreNO(UserProfileHelper.GetBRE_NO(), user?.SCHOOL_NO, user?.USER_NO)?.Where(a => a.BoolUse)?.ToList();
            if (model == null) model = new RollCallIndexViewModel();
            model = Service.GetListData(model, SCHOOL_NO, (byte)AWAT12.RollCallTypeVal.酷嗶點名, ref db);
            return PartialView(model);
        }
        [CheckPermission(CheckBRE_NO= "ADDI131", CheckACTION_ID = "BarcodeRollCallIndex")]
        //檢查權限
        public ActionResult _PageContent1(RollCallBarcodeIndexViewModel model)
        {
            this.Shared();

           
                if (user.ROLE_LEVEL == 0|| user.ROLE_LEVEL ==4)
                {
        
                }
                else
                {
                model.search = new RollCallBarcodeSearchViewModel();
                model.search.USER_NO = user?.USER_NO;
                model.search.USER_key = user?.USER_KEY;
                model.search.wSCHOOL_NO = user?.SCHOOL_NO;
                }
     
            ViewBag.Permission = PermissionService.GetActionPermissionForBreNO("ADDI131", user?.SCHOOL_NO, user?.USER_NO)?.Where(a => a.BoolUse)?.ToList();
            if (model == null) model = new RollCallBarcodeIndexViewModel();
            model = Service.GetBarcodeListData(model, SCHOOL_NO, (byte)ADDT37.RollCallTypeVal.Brcode給點, ref db);
            return PartialView(model);
        }

        /// <summary>
        /// 快速大量加點
        /// </summary>
        /// <param name="Data"></param>
        /// <returns></returns>
        public ActionResult _ViewQuickBulk(RollCallBarcodeEditViewModel Data)
        {
            SelectListGroup group1 = new SelectListGroup() { Name = "校內榮譽(不受每個月控管的總點數)" };
            SelectListGroup group2 = new SelectListGroup() { Name = "班級表現" };

            ViewBag.QuickItems = new List<SelectListItem>()
            {
                new SelectListItem() { Text = "學習表現", Value = "學習表現", Group = group1 },

                new SelectListItem() { Text = "校內競賽", Value = "校內競賽", Group = group1},
                new SelectListItem() { Text = "品德表現", Value = "品德表現", Group = group1},
                 new SelectListItem() { Text = "志工", Value = "志工", Group = group1 },
                new SelectListItem() { Text = "其他", Value = "其他", Group = group1},
                new SelectListItem() { Text = "領導人", Value = "領導人", Group = group1},
              new SelectListItem() { Text = "七個習慣代言人", Value = "七個習慣代言人", Group = group1},
                new SelectListItem() { Text = "班級幹部(不受每個月控管的總點數)", Value = "班級幹部", Group = group2 },
                new SelectListItem() { Text = "班級幫手和榮譽(每個月總點數有控管)", Value = "班級幫手和榮譽", Group = group2 },
                new SelectListItem() { Text = "班級特殊加扣點(每個月總點數有控管)", Value = "班級服務", Group = group2 }
            };

            return PartialView(Data);
        }
        public ActionResult PrintModify(RollCallEditViewModel model1)
        {
            RollCallBarcodeCashViewModel model = new RollCallBarcodeCashViewModel();
            model.Keyword = model1.Keyword;
            model = this.Service.GetBarCodeRollCallCashData(model, ref db);
            return View(model);
        }
        public ActionResult PrintModify1(RollCallEditViewModel model1)
        {
            RollCallBarcodeCashViewModel model = new RollCallBarcodeCashViewModel();
            model.Keyword = model1.Keyword;
            model = this.Service.GetBarCodeRollCallCashData(model, ref db);
            return View(model);
        }
        /// <summary>
        /// 編輯
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [CheckPermission(CheckACTION_ID = "Edit")] //檢查權限
        public ActionResult Edit(RollCallEditViewModel model)
        {
            this.Shared("編輯");

            if (model == null) model = new RollCallEditViewModel();

            if (!string.IsNullOrWhiteSpace(model.Keyword))
            {
                model = this.Service.GetEditData(model, ref db);
            }

            return View(model);
        }
        public ActionResult BarcodeRollCallIndex1(RollCallBarcodeEditViewModel model)
        {
            this.Shared();
            return View(model);
        }

        public ActionResult StatisticsVoteDetail(RollCallBarcodeEditViewModel model)
        {
            this.Shared("明細");
            if (model == null) model = new RollCallBarcodeEditViewModel();

            if (!string.IsNullOrWhiteSpace(model.Keyword))
            {
                model = this.Service.GetEdit1DataDetail(model, ref db);
                int i = 1;
                if (string.IsNullOrWhiteSpace(model.OrdercColumn))
                {
                    foreach (var item in model.Details.OrderBy(x => x.CHG_DATE).ToList())
                    {
                        item.Rank = i;
                        i++;
                    }
                }

                else {

                    foreach (var item in model.Details.ToList())
                    {
                        item.Rank = i;
                        i++;
                    }
                }
            }
            
            return PartialView(model);

        }
        /// <summary>
        /// 編輯
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [CheckPermission(CheckBRE_NO = "ADDI131", CheckACTION_ID = "Edit1")] //檢查權限
        public ActionResult Edit1(RollCallBarcodeEditViewModel model)
        {
            this.Shared("編輯");
        
            ViewBag.Panel_Title= "發放紙本點數-編輯";
            if (model == null) model = new RollCallBarcodeEditViewModel();
            model.Main = new RollCallBarcodeEditMainViewModel();
            model.Main.CRE_DATE = DateTime.Now;
            if (model.Main.ROLL_CALL_DATES == null && model.Main.ROLL_CALL_DATEE == null) {
                model.Main.ROLL_CALL_DATES = DateTime.Now;
                model.Main.ROLL_CALL_DATEE = DateTime.Now.AddMonths(1);
                model.Main.IS_Remark = true;

            }
            
            if (!string.IsNullOrWhiteSpace(model.Keyword))
            {
                model = this.Service.GetEdit1Data(model, ref db);
            }
            if (model.Main.IS_SHOW == null) {

                model.Main.IS_SHOW = false;
            }
            if (model.Main.IS_Remark == null)
            {

                model.Main.IS_Remark = true;
            }
            List<SelectListItem> HRMT25Select = new List<SelectListItem>();
            SelectListGroup group1 = new SelectListGroup() { Name = "校內榮譽(不受每個月控管的總點數)" };

            //導師進來看到group2group3
            //行政人員不要看到group2
            SelectListGroup group2 = new SelectListGroup() { Name = "班級表現" };
            SelectListGroup group3 = new SelectListGroup() { Name = "特殊表現(不出現在E本書)" };
            //行政人員不要看到group2
            int NteacherCount = 0;
            int YteacheCount = 0;
            NteacherCount = db.HRMT25.Where(x => x.SCHOOL_NO == user.SCHOOL_NO && x.USER_NO == user.USER_NO &&x.ROLE_ID == "8" ).Count();
            YteacheCount = db.HRMT01.Where(x=>x.SCHOOL_NO == user.SCHOOL_NO && x.USER_NO == user.USER_NO &&x.CLASS_NO!=null).Count();
            int ADMINteacherCount = 0;
            ADMINteacherCount = db.HRMT25.Where(x => x.SCHOOL_NO == user.SCHOOL_NO && x.USER_NO == user.USER_NO && (x.ROLE_ID == "1" || x.ROLE_ID == "0" || x.ROLE_ID == "2" || x.ROLE_ID == "4")).Count();
            if (NteacherCount > 0 && ADMINteacherCount==0)
            {

                List<SelectListItem> HRMT25TempSelect = new List<SelectListItem>();
                HRMT25TempSelect = new List<SelectListItem>()
                {
                    new SelectListItem() { Text = "學習表現", Value = "學習表現", Group = group1 },

                new SelectListItem() { Text = "校內競賽", Value = "校內競賽", Group = group1 },
                new SelectListItem() { Text = "品德表現", Value = "品德表現", Group = group1 },
                 new SelectListItem() { Text = "志工", Value = "志工", Group = group1 },
                new SelectListItem() { Text = "其他", Value = "其他", Group = group1 },
             new SelectListItem() { Text = "領導人", Value = "領導人", Group = group1 },

              new SelectListItem() { Text = "七個習慣代言人", Value = "七個習慣代言人", Group = group1}
                
                };
                HRMT25Select.AddRange(HRMT25TempSelect);

            }
            if (YteacheCount > 0 && ADMINteacherCount == 0)
            {
                List<SelectListItem> HRMT25TempSelect = new List<SelectListItem>();

                HRMT25TempSelect = new List<SelectListItem>()
                {
                new SelectListItem() { Text = "班級幹部(不受每個月控管的總點數)", Value = "班級幹部", Group = group2 },
                new SelectListItem() { Text = "班級幫手和榮譽(每個月總點數有控管)", Value = "班級幫手和榮譽", Group = group2 },
                new SelectListItem() { Text = "班級特殊加扣點(每個月總點數有控管)", Value = "班級服務", Group = group2 }


                };
                HRMT25Select.AddRange(HRMT25TempSelect);
            }
          
            if (ADMINteacherCount > 0) {
                List<SelectListItem> HRMT25TempSelect = new List<SelectListItem>();
                HRMT25TempSelect = new List<SelectListItem>()
                {
                    new SelectListItem() { Text = "學習表現", Value = "學習表現", Group = group1 },

                new SelectListItem() { Text = "校內競賽", Value = "校內競賽", Group = group1 },
                new SelectListItem() { Text = "品德表現", Value = "品德表現", Group = group1 },
                 new SelectListItem() { Text = "志工", Value = "志工", Group = group1 },
                new SelectListItem() { Text = "其他", Value = "其他", Group = group1 },
             new SelectListItem() { Text = "領導人", Value = "領導人", Group = group1 },

              new SelectListItem() { Text = "七個習慣代言人", Value = "七個習慣代言人", Group = group1},
                new SelectListItem() { Text = "班級幹部(不受每個月控管的總點數)", Value = "班級幹部", Group = group2 },
                new SelectListItem() { Text = "班級幫手和榮譽(每個月總點數有控管)", Value = "班級幫手和榮譽", Group = group2 },
                new SelectListItem() { Text = "班級特殊加扣點(每個月總點數有控管)", Value = "班級服務", Group = group2 }


                };
                HRMT25Select.AddRange(HRMT25TempSelect);
            }
            SelectListItem listItem = new SelectListItem();
            listItem = new SelectListItem() { Text = "小獎勵(班級加點，受點數控管)", Value = "小獎勵(班級加點，受點數控管)", Group = group3 };
            HRMT25Select.Add(listItem);
            listItem = new SelectListItem() { Text = "小獎勵(學校加點，不受點數控管)", Value = "小獎勵(學校加點，不受點數控管)", Group = group3 };
            HRMT25Select.Add(listItem);
            ViewBag.QuickItems = HRMT25Select;
            string sSQL = $@"  select count(*)  from ADDT38 a inner join ADDT37 b on a.ROLL_CALL_ID = b.ROLL_CALL_ID where a.ROLL_CALL_ID=@ROLL_CALL_ID and USER_NO is not null";
            var temp = db.Database.Connection.Query<int>(sSQL, new
            {
                ROLL_CALL_ID = model.Main.ROLL_CALL_ID
            }).FirstOrDefault();
            ViewBag.isEdite = false;
          
            return View(model);
        }
        [CheckPermissionSeeion(CheckBRE_NO = "ADDI131", CheckACTION_ID = "Edit1")] //檢查權限
        public ActionResult _Details(RollCallBarcodeCashDetailViewModel Item)
        {
            string sSQL = $@"  select count(*)  from ADDT38 a inner join ADDT37 b on a.ROLL_CALL_ID = b.ROLL_CALL_ID where a.ROLL_CALL_ID=@ROLL_CALL_ID and USER_NO is not null";
            var temp = db.Database.Connection.Query<int>(sSQL, new
            {
                ROLL_CALL_ID = Item.ROLL_CALL_ID
            }).FirstOrDefault();
            ViewBag.isEdite = true;
            if (temp != 0)
            {
                ViewBag.isEdite = false;
            }
            return PartialView(Item);
        }
        /// <summary>
        /// 編輯 Save
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [CheckPermission(CheckACTION_ID = "Edit")] //檢查權限
        [ValidateAntiForgeryToken]
        [HttpPost]
        public ActionResult EditSave(RollCallEditViewModel model)
        {
            this.Shared("編輯");

            if (model == null) model = new RollCallEditViewModel();

            string Message = string.Empty;

            if (model.Main == null)
            {
                return RedirectToAction("Edit");
            }
           

            if (string.IsNullOrWhiteSpace(Message)) {
            if (ModelState.IsValid == false )
            {
                Message = "警告!輸入的內容有錯誤";
            }
            else
            {
                var Result = this.Service.SaveEditData(model, (byte)AWAT12.RollCallTypeVal.酷嗶點名, user, ref db);

                if (Result.Success)
                {
                    TempData[SharedGlobal.StatusMessageName] = "存檔完成";

                    return RedirectToAction(nameof(ADDI13Controller.RollCallIndex));
                }

                Message += Result.Message;
            }
            }
            TempData[SharedGlobal.StatusMessageName] = Message;

#if DEBUG
            var errorsaa = ModelState
                        .Where(x => x.Value.Errors.Count > 0)
                        .Select(x => new { x.Key, x.Value.Errors })
                        .ToArray();
#endif

            return View(nameof(ADDI13Controller.Edit), model);
        }
        /// <summary>
        /// 編輯 Save
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [CheckPermission(CheckBRE_NO = "ADDI131", CheckACTION_ID = "Edit1")] //檢查權限
        [ValidateAntiForgeryToken]
        [HttpPost]
        public ActionResult EditBarcodeSave(RollCallBarcodeEditViewModel model)
        {
            this.Shared("編輯");
            ViewBag.Panel_Title = "發放紙本點數-編輯";
            if (model == null) model = new RollCallBarcodeEditViewModel();
            SelectListGroup group1 = new SelectListGroup() { Name = "校內榮譽(不受每個月控管的總點數)" };
            SelectListGroup group2 = new SelectListGroup() { Name = "班級表現" };
            RollCallBarcodeEditViewModel model2 = new RollCallBarcodeEditViewModel();
            ViewBag.QuickItems = new List<SelectListItem>()
            {
                new SelectListItem() { Text = "學習表現", Value = "學習表現", Group = group1 },

                new SelectListItem() { Text = "校內競賽", Value = "校內競賽", Group = group1},
                new SelectListItem() { Text = "品德表現", Value = "品德表現", Group = group1},
                 new SelectListItem() { Text = "志工", Value = "志工", Group = group1 },
                new SelectListItem() { Text = "其他", Value = "其他", Group = group1},
                   new SelectListItem() { Text = "領導人", Value = "領導人", Group = group1},
                        new SelectListItem() { Text = "七個習慣代言人", Value = "七個習慣代言人", Group = group1},
                new SelectListItem() { Text = "班級幹部(不受每個月控管的總點數)", Value = "班級幹部", Group = group2 },
                new SelectListItem() { Text = "班級幫手和榮譽(每個月總點數有控管)", Value = "班級幫手和榮譽", Group = group2 },
                new SelectListItem() { Text = "班級特殊加扣點(每個月總點數有控管)", Value = "班級服務", Group = group2 }
            };

            //檢查給點教師的限制
            //每月給點上限：綁的程式是
            //一、批次加扣點裡面的「特殊加扣點」、「班級小幫手」
            //二、即時加點的「特殊加點--固定」、「特殊加點--隨機」
            string ErrMsg;
            short ThisMonthCash=0;
            int CashTotal = 0;
            int CashLimit = 0;
              string Message = string.Empty;
            if (model.SUBJECT.Contains("批次特殊加扣點") || model.SUBJECT.Contains("即時加點特殊加扣點") || model.SUBJECT.Contains("特殊加扣點")
              || model.SUBJECT.Contains("批次校內表現班級小幫手") || model.SUBJECT.Contains("批次校內表現班級幫手和榮譽")
              || model.SUBJECT.Contains("校內表現-班級幫手和榮譽") || model.SUBJECT.Contains("班級幫手和榮譽") || model.SUBJECT.Contains("校內表現-班級服務")
                 || model.SUBJECT.Contains("批次快速大量加點-特殊加扣點") || model.SUBJECT.Contains("特殊加扣點-小獎勵(班級加點，受點數控管)") || model.SUBJECT.Contains("小獎勵(班級加點，受點數控管") || model.SUBJECT.Contains("班級幫手和榮譽") || model.SUBJECT.Contains("班級服務"))
            {
                 CashLimit = UserProfile.GetCashLimit(user.SCHOOL_NO, user.USER_NO, user.USER_TYPE, ref db, out ThisMonthCash, out ErrMsg);
                if (string.IsNullOrWhiteSpace(ErrMsg) == false)
                {


                    Message = ErrMsg;
                }
            }
            if (model.SUBJECT == "小獎勵(班級加點，受點數控管)" && model.Main.ROLL_CASH_SUM!=null)
            {
                CashTotal = (int)model.Main.ROLL_CASH_SUM;
            }
            
            string sSQLtemp = $@"  select count(*)  from ADDT38 a inner join ADDT37 b on a.ROLL_CALL_ID = b.ROLL_CALL_ID where a.ROLL_CALL_ID=@ROLL_CALL_ID and USER_NO is not null";
            var temp1 = db.Database.Connection.Query<int>(sSQLtemp, new
            {
                ROLL_CALL_ID = model.Main.ROLL_CALL_ID
            }).FirstOrDefault();
            ViewBag.isEdite = true;
            if (temp1 != 0)
            {
                ViewBag.isEdite = false;
                db.ADDT37.Where(x => x.ROLL_CALL_ID == model.Keyword).FirstOrDefault();
            }
        
            if (!string.IsNullOrWhiteSpace(model.Keyword))
            {

                model.Details = (from a in db.ADDT38
                                 where a.ROLL_CALL_ID == model.Keyword
                                 select new RollCallBarcodeCashDetailViewModel()
                                 {
                                     ROLL_CALL_ID = a.ROLL_CALL_ID,

                                     CASH = (short)(a.CASH ?? 0),
                                     NUM = (short)(a.NUM ?? 0),
                                 }).Distinct().ToList();
            }
            if (model.Main == null)
            {
                return RedirectToAction("Edit1");
            }
            if (string.IsNullOrWhiteSpace(model.Main.ROLL_CALL_DESC))
            {

                Message = "請填寫具體事蹟";

            }
            else if (model.Main.ROLL_CALL_DATES == null || model.Main.ROLL_CALL_DATEE == null)
            {

                Message = "請填寫開始結束時間";

            }
            else if (ModelState.IsValid == false && temp1 == 0)
            {
                Message = "警告!輸入的內容有錯誤";
            }
            else if (model.Main.ROLL_CALL_DATES != null && model.Main.ROLL_CALL_DATEE != null && model.Main.ROLL_CALL_DATEE > model.Main.ROLL_CALL_DATES && (model.Main.ROLL_CALL_DATEE- model.Main.ROLL_CALL_DATES).Value.Days>=(365*2+30))
            {
                Message = "設定活動時間，超過2年1個月";

            }
            else if (CashLimit > 0 && CashLimit != short.MaxValue)
            {
                if (model.SUBJECT.Contains("批次特殊加扣點") || model.SUBJECT.Contains("即時加點特殊加扣點") || model.SUBJECT.Contains("特殊加扣點")
               || model.SUBJECT.Contains("批次校內表現班級小幫手") || model.SUBJECT.Contains("批次校內表現班級幫手和榮譽")
               || model.SUBJECT.Contains("校內表現-班級幫手和榮譽") || model.SUBJECT.Contains("班級幫手和榮譽") || model.SUBJECT.Contains("校內表現-班級服務")
                  || model.SUBJECT.Contains("批次快速大量加點-特殊加扣點") || model.SUBJECT.Contains("特殊加扣點-小獎勵(班級加點，受點數控管)") || model.SUBJECT.Contains("小獎勵(班級加點，受點數控管") || model.SUBJECT.Contains("班級幫手和榮譽") || model.SUBJECT.Contains("班級服務"))
                {
                    if ((CashTotal + ThisMonthCash) > CashLimit)
                    {
                        Message = "本次給點將超過本月給點限制：" + CashLimit.ToString() + "。您本月已發出點數：" + ThisMonthCash.ToString();
                    }
                }
            }
          if(Message==null || Message=="")
            {

                List<ADDT38> IS_PRSENTFalse = new List<ADDT38>();
                IS_PRSENTFalse = db.ADDT38.Where(a => a.ROLL_CALL_ID == model.Main.ROLL_CALL_ID && a.IS_PRESENT == false).ToList();
                //多加若已有人使用則無法異動點數
                string sSQL = $@"  select count(*)  from ADDT38 a inner join ADDT37 b on a.ROLL_CALL_ID = b.ROLL_CALL_ID where a.ROLL_CALL_ID=@ROLL_CALL_ID and USER_NO is not null";
                var temp = db.Database.Connection.Query<int>(sSQL, new
                {
                    ROLL_CALL_ID = model.Main.ROLL_CALL_ID
                }).FirstOrDefault();


                if (temp != 0) {
                    TempData[SharedGlobal.StatusMessageName] = "存檔完成";
                    var Result2 = this.Service.UpdateADDT37Info(model, user, ref db);
                    return RedirectToAction(nameof(ADDI13Controller.BarcodeRollCallIndex));
                }
                else {
                    bool reNEWADDT38 = false;
                    string sSQL1 = $@"   select count(*)  from ADDT38 a inner
                                 join ADDT37 b on a.ROLL_CALL_ID = b.ROLL_CALL_ID where a.ROLL_CALL_ID = @ROLL_CALL_ID";
                    var temp3 = db.Database.Connection.Query<ADDT38>(sSQL1, new
                    {
                        ROLL_CALL_ID = model.Main.ROLL_CALL_ID
                    }).ToList();

                    if (model.Details != null && model.Details.Count() > 0)
                    {
                        //代表點數有修改
                        foreach (var item in model.Details.ToList())
                        {
                            int temp3Count = 0;
                            temp3Count = temp3.Where(x => x.CASH == item.CASH && x.NUM == item.NUM).Count();
                            if (temp3Count == 0)
                            {
                                reNEWADDT38 = true;
                            }
                        }
                        if (reNEWADDT38)
                        {
                            if (IS_PRSENTFalse != null && IS_PRSENTFalse.Count() > 0 && temp == 0)
                            {
                                this.Service.RemoveADDT38(IS_PRSENTFalse, ref db);
                            }



                            //新增更新ADDT37
                            var Result = this.Service.SaveEditBarcodeData(model, (byte)ADDT37.RollCallTypeVal.Brcode給點, user, ref db);

                            if (Result.Success && temp == 0)
                            {
                                TempData[SharedGlobal.StatusMessageName] = "存檔完成";
                                var Result1 = this.Service.SaveBarcodeRollCallData(model, user, ref db);
                                var Result2 = this.Service.UpdateADDT37Info(model, user, ref db);
                                return RedirectToAction(nameof(ADDI13Controller.BarcodeRollCallIndex));
                            }

                            Message += Result.Message;
                        }
                        else
                        {
                            TempData[SharedGlobal.StatusMessageName] = "存檔完成";
                            var Result2 = this.Service.UpdateADDT37Info(model, user, ref db);
                            return RedirectToAction(nameof(ADDI13Controller.BarcodeRollCallIndex));
                        }
                    }
                    else {
                        if (temp3.Count() > 0) {
                            TempData[SharedGlobal.StatusMessageName] = "存檔完成";
                            var Result2 = this.Service.UpdateADDT37Info(model, user, ref db);
                            return RedirectToAction(nameof(ADDI13Controller.BarcodeRollCallIndex));

                        }
                    }

                }
            }
           
            else {
                TempData[SharedGlobal.StatusMessageName] = Message;
            }

#if DEBUG
            var errorsaa = ModelState
                        .Where(x => x.Value.Errors.Count > 0)
                        .Select(x => new { x.Key, x.Value.Errors })
                        .ToArray();
#endif

            return View(nameof(ADDI13Controller.Edit1), model);
        }
     

        /// <summary>
        /// 編輯 Del
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [CheckPermission(CheckBRE_NO = "ADDI131", CheckACTION_ID = "Edit1")] //檢查權限
        [ValidateAntiForgeryToken]
        [HttpPost]
        public ActionResult Del1(RollCallBarcodeEditViewModel model)
        {
            this.Shared("編輯");

            if (string.IsNullOrWhiteSpace(model.Keyword) && string.IsNullOrWhiteSpace(model.Main?.ROLL_CALL_ID))
            {
                return RedirectToAction(nameof(ADDI13Controller.BarcodeRollCallIndex));
            }

            string Message = string.Empty;

            var Result = this.Service.DelDate1(model, user, ref db);

            if (Result.Success)
            {
                TempData[SharedGlobal.StatusMessageName] = "作廢完成";
                return RedirectToAction(nameof(ADDI13Controller.BarcodeRollCallIndex));
            }
            Message += Result.Message;

            TempData[SharedGlobal.StatusMessageName] = Message;

            return RedirectToAction(nameof(ADDI13Controller.BarcodeRollCallIndex));
        }

        #region 作廢

        /// <summary>
        /// 編輯 Del
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [CheckPermission(CheckACTION_ID = "Edit")] //檢查權限
        [ValidateAntiForgeryToken]
        [HttpPost]
        public ActionResult Del(RollCallEditViewModel model)
        {
            this.Shared("編輯");

            if (string.IsNullOrWhiteSpace(model.Keyword) && string.IsNullOrWhiteSpace(model.Main?.ROLL_CALL_ID))
            {
                return RedirectToAction(nameof(ADDI13Controller.RollCallIndex));
            }

            string Message = string.Empty;
            List<Tuple<string, string, int>> valuesList = new List<Tuple<string, string, int>>();
            var Result = this.Service.DelDate(model, user, ref db,ref valuesList);

            if (Result.Success)
            {
                TempData[SharedGlobal.StatusMessageName] = "作廢完成";
                return RedirectToAction(nameof(ADDI13Controller.RollCallIndex));
            }
            Message += Result.Message;

            TempData[SharedGlobal.StatusMessageName] = Message;

            return RedirectToAction(nameof(ADDI13Controller.RollCallIndex));
        }

        #endregion 作廢

        /// <summary>
        /// 給點
        /// </summary>
        /// <returns></returns>
        [CheckPermission(CheckACTION_ID = "RollCallCash")] //檢查權限
        public ActionResult RollCallCash(RollCallCashViewModel model)
        {
            this.Shared("給點");

            if (!string.IsNullOrWhiteSpace(model.Keyword))
            {
                model = this.Service.GetRollCallCashData(model, ref db);
            }
            else
            {
                return RedirectToAction(nameof(ADDI13Controller.RollCallIndex));
            }

            return View(model);
        }

        public ActionResult _RollCallCashDetail(RollCallCashDetailViewModel Item)
        {
            return PartialView("_RollCallCashDetail", Item);
        }

        public ActionResult RollCallCashSave(RollCallCashViewModel model)
        {
            this.Shared("給點");

            if (model == null) model = new RollCallCashViewModel();

            string Message = string.Empty;

            if (string.IsNullOrWhiteSpace(model.Keyword))
            {
                return RedirectToAction("RollCallIndex");
            }

            if (ModelState.IsValid == false)
            {
                Message = "警告!輸入的內容有錯誤";
            }
            else
            {
                List<Tuple<string, string, int>> valuesList = new List<Tuple<string, string, int>>();
                var Result = this.Service.SaveRollCallCashData(model, user, ref db,ref valuesList);

                if (Result.Success)
                {
                    TempData[SharedGlobal.StatusMessageName] = "給點完成";

                    return RedirectToAction(nameof(ADDI13Controller.RollCallIndex));
                }

                Message += Result.Message;
            }

            TempData[SharedGlobal.StatusMessageName] = Message;

#if DEBUG
            var errorsaa = ModelState
                        .Where(x => x.Value.Errors.Count > 0)
                        .Select(x => new { x.Key, x.Value.Errors })
                        .ToArray();
#endif

            return View(nameof(ADDI13Controller.RollCallCash), model);
        }

        /// <summary>
        /// 點名
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ActionResult Index(ADDI13IndexViewModel model)
        {
            RollCallCashViewModel CallCashModel = new RollCallCashViewModel();
            this.Shared();
            this.LoginOUT();

            CallCashModel.Keyword = model.ROLL_CALL_ID;
            CallCashModel = Service.GetRollCallCashData(CallCashModel, ref db);
            if (CallCashModel.Details.Count() > 0)
            {
                using (var txn = Queries.GetNewReadUncommittedScope())
                {
                    model.Details = (from item in CallCashModel.Details
                                     join a in db.BDMT01 on item.SCHOOL_NO equals a.SCHOOL_NO
                                     select new ADDI13EditPeopleViewModel
                                     {
                                         SCHOOL_NO = item.SCHOOL_NO,
                                         SHORT_NAME = a.SHORT_NAME,
                                         USER_NO = item.USER_NO,
                                         NAME = item.NAME,
                                         GRADE = item.GRADE,
                                         CLASS_NO = item.CLASS_NO,
                                         SEAT_NO = item.SEAT_NO
                                     }
                                   ).ToList();
                }
            }

            return View(model);
        }
        public ActionResult GetStudentCashIndex(ADDI13IndexViewModel model, string ROLL_CALL_ID)
        {
            SECI01Service Service = new SECI01Service();
            string Message = "";
            RollCallBarcodeCashViewModel CallCashModel = new RollCallBarcodeCashViewModel();
            this.Shared();
            ViewBag.IsUseZZZI09 = new SECSharedService().IsUseZZZI09(user, ref db, ref Message);
            model.DATA_ANGLE_TYPE = UserProfileHelper.GetDataAngleType(user);
            //線上投稿資料
            var MyADDT01List = from a01 in db.ADDT01
                               join h01 in db.HRMT01 on new { a01.USER_NO, a01.SCHOOL_NO } equals new { h01.USER_NO, h01.SCHOOL_NO } into h01_join
                               from h01 in h01_join.DefaultIfEmpty()
                               where a01.SCHOOL_NO == user.SCHOOL_NO
                               //&& (!UserStaus.NGUserStausList.Contains(h01.USER_STATUS))
                                && a01.WRITING_STATUS != (byte)ADDStatus.eADDT01Status.Disable
                               select a01;

            List<string> MyChild = new List<string>();
            if (model.DATA_ANGLE_TYPE == UserProfileHelper.AngleVal.MyChildData)
            {
                var Ht06 = HRMT06.GetMyPanyStudent(user.SCHOOL_NO, user.USER_NO, db);

                if (Ht06 != null)
                {
                    if (Ht06.Count() > 0)
                    {
                        MyChild = Ht06.Select(a => a.STUDENT_USER_NO).ToList();
                    }
                }
            }

            if (MyChild.Count == 0)
            {
                MyChild.Add("");
            }
            if (model != null && !string.IsNullOrEmpty(model.BarCode))
            {
                model.CARD_NO = model.BarCode;
            }
            HRMT01 thisUser = db.HRMT01.Where(a => a.SCHOOL_NO == user.SCHOOL_NO && a.USER_NO == user.USER_NO).FirstOrDefault();
            if (thisUser == null)
            {


            }
            else {
               model.wIsQhisSchool = (db.HRMT01.Where(a => a.IDNO == thisUser.IDNO && a.USER_STATUS == UserStaus.Invalid).Count() > 0) ? true : false;
                if (!string.IsNullOrWhiteSpace(thisUser.PHOTO))
                {
                    model.PlayerUrl = Service.GetDirectorySysMyPhotoPath(thisUser.SCHOOL_NO, thisUser.USER_NO, thisUser.PHOTO);
                }
                else
                {
                    model.PlayerUrl = UserProfile.GetPlayerUrl(ref db, thisUser.SCHOOL_NO, thisUser.USER_NO, thisUser.SEX, thisUser.USER_TYPE);
                }
                //看單一學生
                if (model.DATA_ANGLE_TYPE == UserProfileHelper.AngleVal.OneData)
                {
                    MyADDT01List = MyADDT01List.Where(a => a.USER_NO == thisUser.USER_NO);

                    model.arrWRITING_NO = MyADDT01List.OrderByDescending(a => a.WRITING_NO).Select(a => a.WRITING_NO).ToList();
                }
                //看寶貝
                if (model.DATA_ANGLE_TYPE == UserProfileHelper.AngleVal.MyChildData)
                {
                    MyADDT01List = MyADDT01List.Where(a => MyChild.Contains(a.USER_NO));
                }

                //看單一班級
                if (model.DATA_ANGLE_TYPE == UserProfileHelper.AngleVal.ClassData)
                {
                    MyADDT01List = MyADDT01List.Where(a => a.CLASS_NO == thisUser.CLASS_NO);
                }
                model.WritingCount = MyADDT01List.Count();
                model.WritingShareCount = MyADDT01List.Where(a => a.SHARE_YN == "Y").Count();
                model.ADDT01List = MyADDT01List.OrderByDescending(a => a.WRITING_NO).Take(5).ToList();

                //閱讀認證
                var MyADDT06List = from a06 in db.ADDT06
                                   join h01 in db.HRMT01
                                       on new { a06.SCHOOL_NO, a06.USER_NO }
                                       equals new { h01.SCHOOL_NO, h01.USER_NO } into h01_join
                                   from h01 in h01_join.DefaultIfEmpty()
                                   where a06.SCHOOL_NO == thisUser.SCHOOL_NO
                                    //&& (!UserStaus.NGKeyinUserStausList.Contains(h01.USER_STATUS))
                                    && a06.APPLY_STATUS != ADDT06.APPLY_STATUS_Val.APPLY_TYPE_DEL
                                   select a06;

                //看單一學生
                if (model.DATA_ANGLE_TYPE == UserProfileHelper.AngleVal.OneData)
                {
                    MyADDT06List = MyADDT06List.Where(a => a.USER_NO == thisUser.USER_NO);

                    model.arrAPPLY_NO = MyADDT06List.OrderByDescending(a => a.APPLY_NO).Select(a => a.APPLY_NO).ToList();
                }

                //看寶貝
                if (model.DATA_ANGLE_TYPE == UserProfileHelper.AngleVal.MyChildData)
                {
                    MyADDT06List = MyADDT06List.Where(a => MyChild.Contains(a.USER_NO));
                }

                //看單一班級
                if (model.DATA_ANGLE_TYPE == UserProfileHelper.AngleVal.ClassData)
                {
                    MyADDT06List = MyADDT06List.Where(a => a.CLASS_NO == thisUser.CLASS_NO);
                }
                MyADDT06List = MyADDT06List.Where(x => x.APPLY_STATUS != ADDT06.APPLY_STATUS_Val.APPLY_TYPE_Back && x.APPLY_STATUS != ADDT06.APPLY_STATUS_Val.APPLY_TYPE_TempSave);
                model.BookCount = MyADDT06List.Count();
                model.BookShareCount = MyADDT06List.Where(a => a.SHARE_YN == "y").Count();
                model.ADDT06List = MyADDT06List.OrderByDescending(a => a.APPLY_NO).Take(5).ToList();
            }
            return View(model);


        }
        public ActionResult Index1Point(ADDI13IndexViewModel model, string ROLL_CALL_ID) {


            RollCallBarcodeCashViewModel CallCashModel = new RollCallBarcodeCashViewModel();
            this.Shared();
            if (model != null && !string.IsNullOrEmpty(model.BarCode))
            {
                model.CARD_NO = model.BarCode;
            }

            ViewBag.ROLL_CALL_ID = ROLL_CALL_ID;
            ViewBag.SCHOOL_NO = model.SCHOOL_NO1;
            ViewBag.Barcode = model.BarCode;
            return View(model);
        }
        /// <summary>
        /// 點名
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ActionResult Index1(ADDI13IndexViewModel model, string ROLL_CALL_ID)
        {
            RollCallBarcodeCashViewModel CallCashModel = new RollCallBarcodeCashViewModel();
            this.Shared();
            if (model!=null&&!string.IsNullOrEmpty(model.BarCode))
            {
                model.CARD_NO = model.BarCode;
            }
           
            ViewBag.ROLL_CALL_ID = ROLL_CALL_ID;
            ViewBag.SCHOOL_NO = model.SCHOOL_NO1;
            ViewBag.Barcode = model.BarCode;
            return View(model);
        }

        /// <summary>
        /// 點名
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ActionResult Index2(ADDI13IndexViewModel model, string ROLL_CALL_ID,string redirect)
        {
            RollCallBarcodeCashViewModel CallCashModel = new RollCallBarcodeCashViewModel();
            this.Shared();
            if (model != null && !string.IsNullOrEmpty(model.BarCode))
            {
                model.CARD_NO = model.BarCode;
            }
            ViewBag.redirectUrl = redirect;
            ViewBag.BarCode = model.CARD_NO;
            return View(model);
        }
        public ActionResult _EditDetails(ADDI13EditPeopleViewModel Item)
        {
            this.Shared();
            return PartialView(Item);
        }
        public ActionResult _OpenGetCashATM(string CARD_NO, string SCHOOL_NO1,string BarCode)
        {
            BarcodeEditPeopleViewModel model = new BarcodeEditPeopleViewModel();
            this.Shared();
          
            bool IScheck = false;
            bool OK = false;
            int SYear;
            int Semesters;
            string Message = "";
            ADDI13Service aDDI13Service = new ADDI13Service();
            SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);
            string BATCH_CASH_ID = aDDI13Service.GetNewBatchCashId("ADDT14", model.SCHOOL_NO);
            bool ISOK = true;
            
            if (SCHOOL_NO1 == null) {

                TempData[SharedGlobal.StatusMessageName] = "取不到學校編號，請重新選學校";
                ISOK = false;
                return PartialView("Index2");
            }
            if (CARD_NO == null)
            {
                TempData[SharedGlobal.StatusMessageName] = "學號有誤，請輸入";
                ISOK = false;
                return PartialView("Index2");

            }
            if (BarCode == null)
            {
                logger.Info("代碼錯誤　　是空的");
                TempData[SharedGlobal.StatusMessageName] = "代碼錯誤";
                ISOK = false;
                return PartialView("Index2");

            }
            List<RollCallBarcodeEditMainViewModel> aDDT37s = new List<RollCallBarcodeEditMainViewModel>();
            HRMT01 rMT01 = new HRMT01();
           
            rMT01 = db.HRMT01.Where(x => x.SCHOOL_NO == SCHOOL_NO1 && x.CARD_NO == CARD_NO && x.USER_STATUS==UserStaus.Enabled).FirstOrDefault();

            if (rMT01 == null)
            {
                rMT01 = db.HRMT01.Where(x => x.SCHOOL_NO == SCHOOL_NO1 && x.USER_NO == CARD_NO && x.USER_STATUS == UserStaus.Enabled).FirstOrDefault();
                if (rMT01 == null)
                {
                    TempData[SharedGlobal.StatusMessageName] = "此帳號有誤，請重新輸入";
                    ISOK = false;
                    return PartialView("Index2");
                }
                else {
                    if (rMT01.USER_NO.Contains("A") && rMT01.USER_NO.Length < 10 && rMT01.USER_NO.Length > 6)
                        {
                        CARD_NO = rMT01.USER_NO.Substring(1, rMT01.USER_NO.Length - 1);
                        
                        rMT01 = db.HRMT01.Where(x => x.SCHOOL_NO == SCHOOL_NO1 && x.USER_NO == CARD_NO && x.USER_STATUS == UserStaus.Enabled).FirstOrDefault();
                    }

                }
            }
            if (ISOK == false) {
                TempData[SharedGlobal.StatusMessageName] = "此帳號有誤，請重新輸入";
                return PartialView("Index2");
            }
            else
            {

                IResult result = this.Service.checkBarcode(BarCode, ref db, SCHOOL_NO1, rMT01);
                if (!result.Success)
                {
                    TempData[SharedGlobal.StatusMessageName] = result.Message;
                    return PartialView("Index2");
                }
                else
                {
                    aDDT37s = (List<RollCallBarcodeEditMainViewModel>)result.ModelItem;
                    if (aDDT37s.Count() > 0)
                    {
                        List<HRMT01> hRMTlist = new List<HRMT01>();
                        string CRE_PERSONKey = "";
                        ADDT38 SaveUp = null;
                        CRE_PERSONKey = aDDT37s.FirstOrDefault().CRE_PERSON;
                        HRMT01 rMT012 = new HRMT01();
                        rMT012 = db.HRMT01.Where(x => x.USER_KEY == CRE_PERSONKey).FirstOrDefault();
                  
                            model.SCHOOL_NO = rMT01.SCHOOL_NO;
                        model.USER_NO = rMT01.USER_NO;
                        model.NAME = rMT01.NAME;
                        model.SEAT_NO = rMT01.SEAT_NO;
                        model.CLASS_NO = rMT01.CLASS_NO;
                        model.ROLL_CALL_NAME = aDDT37s.FirstOrDefault().ROLL_CALL_NAME;
                        model.BarCode = BarCode;
                        model.CASH = aDDT37s.FirstOrDefault().CASH;
                        model.ROLL_CALL_ID = aDDT37s.FirstOrDefault().ROLL_CALL_ID;
                        SaveUp = db.ADDT38.Where(a => a.ROLL_CALL_ID == model.ROLL_CALL_ID && a.BarCode == model.BarCode).FirstOrDefault();


                        hRMTlist.Add(rMT01);
                        var Result = this.Service.InsertADDT38(model, ref db, hRMTlist, Session.SessionID, BATCH_CASH_ID, model.SCHOOL_NO);
                        if (SaveUp.SUBJECT == "小獎勵(班級加點，受點數控管)" || SaveUp.SUBJECT == "小獎勵(學校加點，不受點數控管)")
                        {
                            OK = aDDI13Service.InsertADDT20(BATCH_CASH_ID, (byte)SYear, (byte)Semesters, aDDT37s.FirstOrDefault().ROLL_CALL_DESC, hRMTlist, user, model, SaveUp, Session.SessionID, ref db, model.BarCode);

                        }
                        else {
                            OK = aDDI13Service.InsertADDT14(BATCH_CASH_ID, (byte)SYear, (byte)Semesters, aDDT37s.FirstOrDefault().ROLL_CALL_DESC, hRMTlist, user, model, SaveUp, Session.SessionID, ref db, model.BarCode);

                        }
                              
                        AWAT01 UserCash = db.AWAT01.Where(user => user.SCHOOL_NO == model.SCHOOL_NO && user.USER_NO == model.USER_NO).FirstOrDefault();
                        if (UserCash != null)
                        { model.SumCash = UserCash.CASH_AVAILABLE.Value; }
                        else
                        {
                            model.SumCash = 0;

                        }
                        string strMsg = "";
                        
                        strMsg = model.NAME + "獲得" + model.CASH + "點，目前共有" + model.SumCash+"點";
                        if (Result.Success)
                        {
                            TempData[SharedGlobal.StatusMessageName] = "恭喜領取點數 成功！"+ strMsg;

                            //UserProfile.RefreshCashInfo(user, ref db);
                            // UserProfileHelper.Set(user);
                            return RedirectToAction("Index2", new { ROLL_CALL_ID = model.ROLL_CALL_ID, SCHOOL_NO1 = model.SCHOOL_NO });
                        }
                        Message += Result.Message;
                        TempData[SharedGlobal.StatusMessageName] = Message;
                        return View("Index2", new { ROLL_CALL_ID = model.ROLL_CALL_ID, SCHOOL_NO1 = model.SCHOOL_NO });
                    }
                }

            }
            return View();
        }

            public ActionResult _OpenGetCash(string CARD_NO,string SCHOOL_NO1) {
            BarcodeEditPeopleViewModel model = new BarcodeEditPeopleViewModel();
            this.Shared();
            bool IScheck = false;
            if (!string.IsNullOrWhiteSpace(SCHOOL_NO1))
            {
                SCHOOL_NO = SCHOOL_NO1;
            }
            List<RollCallBarcodeEditMainViewModel> aDDT37s = new List<RollCallBarcodeEditMainViewModel>();
            HRMT01 Hrt01 = new HRMT01();
            if (user != null) {

                Hrt01 = db.HRMT01.Where(x => x.SCHOOL_NO == user.SCHOOL_NO && x.USER_NO == user.USER_NO && x.USER_STATUS == 1).FirstOrDefault();
                if (Hrt01.USER_NO.Contains("A") && Hrt01.USER_NO.Length < 10　&& Hrt01.USER_NO.Length >6)
                {

                    model.txtUSER_NO = Hrt01.USER_NO.Substring(1, Hrt01.USER_NO.Length - 1);
                    Hrt01 = db.HRMT01.Where(x => x.USER_NO == model.txtUSER_NO && x.SCHOOL_NO == user.SCHOOL_NO && x.USER_STATUS == UserStaus.Enabled).FirstOrDefault();
                }
            }
           
            IResult result = this.Service.checkBarcode(CARD_NO, ref db, SCHOOL_NO, Hrt01);

            if (!result.Success)
            {
                TempData[SharedGlobal.StatusMessageName]= result.Message;
                return PartialView("Index1");
            }
            else {
                aDDT37s= (List<RollCallBarcodeEditMainViewModel>)result.ModelItem;
            if (aDDT37s.Count() > 0) {
                    string CRE_PERSONKey = "";
                    CRE_PERSONKey = aDDT37s.FirstOrDefault().CRE_PERSON;
                    HRMT01 rMT01 = new HRMT01();
                    rMT01 = db.HRMT01.Where(x => x.USER_KEY == CRE_PERSONKey).FirstOrDefault();
                    AWAT01 UserCash = db.AWAT01.Where(user => user.SCHOOL_NO == rMT01.SCHOOL_NO && user.USER_NO == rMT01.USER_NO).FirstOrDefault();
                    if (UserCash != null)
                    { model.SumCash = UserCash.CASH_AVAILABLE.Value; }
                    else {
                        model.SumCash = 0;

                    }
                        model.SCHOOL_NO = SCHOOL_NO;
            model.USER_NO = user?.USER_NO;
            model.NAME = rMT01.NAME;
            model.ROLL_CALL_NAME = aDDT37s.FirstOrDefault().ROLL_CALL_NAME;
            model.BarCode = CARD_NO;
            model.CASH = aDDT37s.FirstOrDefault().CASH;
            model.ROLL_CALL_ID = aDDT37s.FirstOrDefault().ROLL_CALL_ID;
            }
            }
            ViewBag.ROLL_CALL_ID = model.ROLL_CALL_ID;
            ViewBag.SCHOOL_NO = SCHOOL_NO;
            ViewBag.Barcode = model.BarCode;
            return PartialView("_EditDetails1", model);
        }
        public ActionResult GetCASHFINAL(BarcodeEditPeopleViewModel model) {

            ADDI13Service aDDI13Service = new ADDI13Service();
            ADDI13IndexViewModel model2 = new ADDI13IndexViewModel();


            this.Shared();
            string Message = "";
            bool OK = false;
            int SYear;
            int Semesters;
            SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);
            string BATCH_CASH_ID = aDDI13Service.GetNewBatchCashId("ADDT14", model.SCHOOL_NO);
            if (user != null)
            {
                string PSW = "";
                PSW = db.ZZT08.Where(x => x.USER_NO == user.USER_NO && x.SCHOOL_NO == user.SCHOOL_NO).Select(x => x.PASSWORD).FirstOrDefault();
                model.txtUSER_NO = user.USER_NO;
                model.txtPASSWORD = PSW;
                HRMT01 hRMT = db.HRMT01.Where(x => x.USER_NO == model.txtUSER_NO && x.SCHOOL_NO == model.SCHOOL_NO && x.USER_STATUS == UserStaus.Enabled).FirstOrDefault();
                model.SHORT_NAME = hRMT.NAME;
            }
            if (!string.IsNullOrWhiteSpace(model.txtUSER_NO) && !string.IsNullOrWhiteSpace(model.txtPASSWORD))
            {
                int ZZT08Count = 0;
                ZZT08Count = db.ZZT08.Where(x => x.USER_NO == model.txtUSER_NO && x.PASSWORD == model.txtPASSWORD).Count();
                List<HRMT01> hRMTlist = new List<HRMT01>();
                if (ZZT08Count > 0)
                {
                    HRMT01 hRMT = db.HRMT01.Where(x => x.USER_NO == model.txtUSER_NO && x.SCHOOL_NO == model.SCHOOL_NO && x.USER_STATUS == UserStaus.Enabled).FirstOrDefault();
                    model.GRADE = hRMT.GRADE;
                    model.USER_NO = hRMT.USER_NO;
                    model.CLASS_NO = hRMT.CLASS_NO;
                    model.SHORT_NAME = hRMT.NAME;
                     model.NAME = hRMT.NAME;
                    model.SEAT_NO = hRMT.SEAT_NO;
                    hRMTlist.Add(hRMT);
                
                    ADDT38 SaveUp = null;
                    ADDT37 SaveUp1 = null;
                    SaveUp = db.ADDT38.Where(a => a.ROLL_CALL_ID == model.ROLL_CALL_ID && a.BarCode == model.BarCode).FirstOrDefault();
                    SaveUp1 = db.ADDT37.Where(a => a.ROLL_CALL_ID == model.ROLL_CALL_ID).FirstOrDefault();

                    var Result = this.Service.InsertADDT38(model, ref db, hRMTlist, Session.SessionID, BATCH_CASH_ID, model.SCHOOL_NO);
                    if (string.IsNullOrWhiteSpace(Result.Message))
                    {

                        if (SaveUp.SUBJECT == "小獎勵(班級加點，受點數控管)" || SaveUp.SUBJECT == "小獎勵(學校加點，不受點數控管)")
                        {
                            OK = aDDI13Service.InsertADDT20(BATCH_CASH_ID, (byte)SYear, (byte)Semesters, SaveUp1.ROLL_CALL_DESC, hRMTlist, user, model, SaveUp, Session.SessionID, ref db, model.BarCode);

                        }
                        else
                        {
                            OK = aDDI13Service.InsertADDT14(BATCH_CASH_ID, (byte)SYear, (byte)Semesters, SaveUp1.ROLL_CALL_DESC, hRMTlist, user, model, SaveUp, Session.SessionID, ref db, model.BarCode);

                        }



                        //OK = aDDI13Service.InsertADDT14(BATCH_CASH_ID, (byte)SYear, (byte)Semesters, SaveUp1.ROLL_CALL_DESC, hRMTlist, user, model, SaveUp, Session.SessionID, ref db, model.BarCode);
                        if (Result.Success)
                        {
                            TempData[SharedGlobal.StatusMessageName] = "恭喜領取點數 成功！";
                            if (user != null)
                            {
                                UserProfile.RefreshCashInfo(user, ref db);

                                UserProfileHelper.Set(user);
                            }
                          
                            return PartialView("_EditDetails3", model);
                        }
                        Message += Result.Message;

                        TempData[SharedGlobal.StatusMessageName] = Message;
                        model2.ROLL_CALL_ID = model.ROLL_CALL_ID;

                        model2.ROLL_CALL_ID = model.ROLL_CALL_ID;
                        model2.SCHOOL_NO1 = model.SCHOOL_NO;
                        return View("Index1", model2);
                      //  return View("Index1", new { model2 = model2, ROLL_CALL_ID = model.ROLL_CALL_ID });
                    }
                    else {
                        Message += Result.Message;
                        TempData[SharedGlobal.StatusMessageName] = Message;
                        model2.ROLL_CALL_ID = model.ROLL_CALL_ID;
                        model2.SCHOOL_NO1 = model.SCHOOL_NO;
                        return View("Index1", model2);
                    }
                }
                else
                {

                    TempData["StatusMessage"] = "請重新輸入帳號密碼";
                    model2.ROLL_CALL_ID = model.ROLL_CALL_ID;
                    model2.SCHOOL_NO1 = model.SCHOOL_NO;
                    return View("Index1", model2);
                }
            }
            else
            {

                TempData["StatusMessage"] = "請重新輸入帳號密碼";
                model2.ROLL_CALL_ID = model.ROLL_CALL_ID;
                model2.SCHOOL_NO1 = model.SCHOOL_NO;
                return View("Index1", model2);

            }
            return View("Index1", new { ROLL_CALL_ID = model.ROLL_CALL_ID, SCHOOL_NO1 = model.SCHOOL_NO });
        }
      //  public ActionResult _GetStudentCashDetail() { }
        public ActionResult GetStudentCashPerson(BarcodeEditPeopleViewModel model)
        {
            try
            {


                string Message = "";
                this.Shared();
                logger.Info("恭喜領取點數 成功 ADDI13   BATCH_CASH_ID1");

               SECI01Service Service = new SECI01Service();
                bool IScheck = false;
                if (!string.IsNullOrWhiteSpace(model.SCHOOL_NO))
                {
                    SCHOOL_NO = model.SCHOOL_NO;
                }
                List<RollCallBarcodeEditMainViewModel> aDDT37s = new List<RollCallBarcodeEditMainViewModel>();
                if (!string.IsNullOrWhiteSpace(model.CARD_NO))
                {
                    model.BarCode = model.CARD_NO;

                }
                ViewBag.IsUseZZZI09 = new SECSharedService().IsUseZZZI09(user, ref db, ref Message);
                model.DATA_ANGLE_TYPE = UserProfileHelper.GetDataAngleType(user);
                //線上投稿資料
                var MyADDT01List = from a01 in db.ADDT01
                                   join h01 in db.HRMT01 on new { a01.USER_NO, a01.SCHOOL_NO } equals new { h01.USER_NO, h01.SCHOOL_NO } into h01_join
                                   from h01 in h01_join.DefaultIfEmpty()
                                   where a01.SCHOOL_NO == user.SCHOOL_NO
                                    //&& (!UserStaus.NGUserStausList.Contains(h01.USER_STATUS))
                                    && a01.WRITING_STATUS != (byte)ADDStatus.eADDT01Status.Disable
                                   select a01;

                List<string> MyChild = new List<string>();
                if (model.DATA_ANGLE_TYPE == UserProfileHelper.AngleVal.MyChildData)
                {
                    var Ht06 = HRMT06.GetMyPanyStudent(user.SCHOOL_NO, user.USER_NO, db);

                    if (Ht06 != null)
                    {
                        if (Ht06.Count() > 0)
                        {
                            MyChild = Ht06.Select(a => a.STUDENT_USER_NO).ToList();
                        }
                    }
                }
                logger.Info("恭喜領取點數 成功 ADDI13   BATCH_CASH_ID2");
                if (MyChild.Count == 0)
                {
                    MyChild.Add("");
                }
                if (model != null && !string.IsNullOrEmpty(model.BarCode))
                {
                    model.CARD_NO = model.BarCode;
                }
                logger.Info("恭喜領取點數 成功 ADDI13   BATCH_CASH_ID3");
                HRMT01 thisUser = db.HRMT01.Where(a => a.SCHOOL_NO == user.SCHOOL_NO && a.USER_NO == user.USER_NO).FirstOrDefault();
                if (thisUser == null)
                {


                }
                else
                {
                    model.wIsQhisSchool = (db.HRMT01.Where(a => a.IDNO == thisUser.IDNO && a.USER_STATUS == UserStaus.Invalid).Count() > 0) ? true : false;
                    if (!string.IsNullOrWhiteSpace(thisUser.PHOTO))
                    {
                        model.PlayerUrl = Service.GetDirectorySysMyPhotoPath(thisUser.SCHOOL_NO, thisUser.USER_NO, thisUser.PHOTO);
                    }
                    else
                    {
                        model.PlayerUrl = UserProfile.GetPlayerUrl(ref db, thisUser.SCHOOL_NO, thisUser.USER_NO, thisUser.SEX, thisUser.USER_TYPE);
                    }
                    //看單一學生
                    if (model.DATA_ANGLE_TYPE == UserProfileHelper.AngleVal.OneData)
                    {
                        MyADDT01List = MyADDT01List.Where(a => a.USER_NO == thisUser.USER_NO);

                        model.arrWRITING_NO = MyADDT01List.OrderByDescending(a => a.WRITING_NO).Select(a => a.WRITING_NO).ToList();
                    }
                    //看寶貝
                    if (model.DATA_ANGLE_TYPE == UserProfileHelper.AngleVal.MyChildData)
                    {
                        MyADDT01List = MyADDT01List.Where(a => MyChild.Contains(a.USER_NO));
                    }
                    logger.Info("恭喜領取點數 成功 ADDI13   BATCH_CASH_ID4");
                    //看單一班級
                    if (model.DATA_ANGLE_TYPE == UserProfileHelper.AngleVal.ClassData)
                    {
                        MyADDT01List = MyADDT01List.Where(a => a.CLASS_NO == thisUser.CLASS_NO);
                    }
                    model.WritingCount = MyADDT01List.Count();
                    model.WritingShareCount = MyADDT01List.Where(a => a.SHARE_YN == "Y").Count();
                    model.ADDT01List = MyADDT01List.OrderByDescending(a => a.WRITING_NO).Take(5).ToList();
                    logger.Info("恭喜領取點數 成功 ADDI13   BATCH_CASH_ID5");
                    //閱讀認證
                    var MyADDT06List = from a06 in db.ADDT06
                                       join h01 in db.HRMT01
                                           on new { a06.SCHOOL_NO, a06.USER_NO }
                                           equals new { h01.SCHOOL_NO, h01.USER_NO } into h01_join
                                       from h01 in h01_join.DefaultIfEmpty()
                                       where a06.SCHOOL_NO == thisUser.SCHOOL_NO
                                        //&& (!UserStaus.NGKeyinUserStausList.Contains(h01.USER_STATUS))
                                        && a06.APPLY_STATUS != ADDT06.APPLY_STATUS_Val.APPLY_TYPE_DEL
                                       select a06;

                    //看單一學生
                    if (model.DATA_ANGLE_TYPE == UserProfileHelper.AngleVal.OneData)
                    {
                        MyADDT06List = MyADDT06List.Where(a => a.USER_NO == thisUser.USER_NO);

                        model.arrAPPLY_NO = MyADDT06List.OrderByDescending(a => a.APPLY_NO).Select(a => a.APPLY_NO).ToList();
                    }
                    logger.Info("恭喜領取點數 成功 ADDI13   BATCH_CASH_ID6");
                    //看寶貝
                    if (model.DATA_ANGLE_TYPE == UserProfileHelper.AngleVal.MyChildData)
                    {
                        MyADDT06List = MyADDT06List.Where(a => MyChild.Contains(a.USER_NO));
                    }

                    //看單一班級
                    if (model.DATA_ANGLE_TYPE == UserProfileHelper.AngleVal.ClassData)
                    {
                        MyADDT06List = MyADDT06List.Where(a => a.CLASS_NO == thisUser.CLASS_NO);
                    }
                    MyADDT06List = MyADDT06List.Where(x => x.APPLY_STATUS != ADDT06.APPLY_STATUS_Val.APPLY_TYPE_Back && x.APPLY_STATUS != ADDT06.APPLY_STATUS_Val.APPLY_TYPE_TempSave);
                    model.BookCount = MyADDT06List.Count();
                    model.BookShareCount = MyADDT06List.Where(a => a.SHARE_YN == "y").Count();
                    model.ADDT06List = MyADDT06List.OrderByDescending(a => a.APPLY_NO).Take(5).ToList();
                }
                logger.Info("恭喜領取點數 成功 ADDI13   BATCH_CASH_ID7");
                IResult result = this.Service.checkBarcode(model.BarCode, ref db, SCHOOL_NO, thisUser);
                ADDI13IndexViewModel model2 = new ADDI13IndexViewModel();
                logger.Info("恭喜領取點數 成功 ADDI13   result"+ result.Success);
                if (!result.Success)
                {
                    TempData[SharedGlobal.StatusMessageName] = result.Message;
                    // TempData[SharedGlobal.StatusMessageName] = Message;
                    model2.ROLL_CALL_ID = model.ROLL_CALL_ID;
                    model2.SCHOOL_NO1 = model.SCHOOL_NO;


                    model2.wIsQhisSchool = model.wIsQhisSchool;
                    model2.PlayerUrl = model.PlayerUrl;
                    model2.arrWRITING_NO = model.arrWRITING_NO;
                    model2.DATA_ANGLE_TYPE = model.DATA_ANGLE_TYPE;
                    model2.WritingCount = model.WritingCount;
                    model2.WritingShareCount = model.WritingShareCount;
                    model2.ADDT01List = model.ADDT01List;
                    model2.arrAPPLY_NO = model.arrAPPLY_NO;
                    model2.BookCount = model.BookCount;
                    model2.BookShareCount = model.BookShareCount;
                    return PartialView("GetStudentCashIndex", model2);
                }
                else
                {
                    aDDT37s = (List<RollCallBarcodeEditMainViewModel>)result.ModelItem;
                    if (aDDT37s.Count() > 0)
                    {
                        string CRE_PERSONKey = "";
                        CRE_PERSONKey = aDDT37s.FirstOrDefault().CRE_PERSON;
                        HRMT01 rMT01 = new HRMT01();
                        rMT01 = db.HRMT01.Where(x => x.USER_KEY == CRE_PERSONKey).FirstOrDefault();

                        model.SCHOOL_NO = SCHOOL_NO;
                        model.USER_NO = user?.USER_NO;
                        model.NAME = rMT01.NAME;
                        model.ROLL_CALL_NAME = aDDT37s.FirstOrDefault().ROLL_CALL_NAME;
                        model.BarCode = model.BarCode;
                        model.CASH = aDDT37s.FirstOrDefault().CASH;
                        // model.SumCash = UserCash.CASH_AVAILABLE.Value;
                        model.ROLL_CALL_ID = aDDT37s.FirstOrDefault().ROLL_CALL_ID;
                    }
                }

                ADDI13Service aDDI13Service = new ADDI13Service();



                this.Shared();
                logger.Info("恭喜領取點數 成功 ADDI13   BATCH_CASH_ID8");
                bool OK = false;
                int SYear;
                int Semesters;
                SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);
                string BATCH_CASH_ID = aDDI13Service.GetNewBatchCashId("ADDT14", model.SCHOOL_NO);
               
                logger.Info("恭喜領取點數 成功 ADDI13   BATCH_CASH_ID9");
                logger.Info("恭喜領取點數 成功 ADDI13   BATCH_CASH_ID");
                if (user != null)
                {
                    string PSW = "";
                    PSW = db.ZZT08.Where(x => x.USER_NO == user.USER_NO && x.SCHOOL_NO == user.SCHOOL_NO).Select(x => x.PASSWORD).FirstOrDefault();
                    model.txtUSER_NO = user.USER_NO;
                    model.txtPASSWORD = PSW;
                    HRMT01 hRMT = db.HRMT01.Where(x => x.USER_NO == model.txtUSER_NO && x.SCHOOL_NO == model.SCHOOL_NO && x.USER_STATUS == UserStaus.Enabled).FirstOrDefault();

                    if (model.txtUSER_NO.Contains("A") && model.txtUSER_NO.Length < 10 && model.txtUSER_NO.Length > 6)
                    {

                        model.txtUSER_NO = model.txtUSER_NO.Substring(1, model.txtUSER_NO.Length - 1);
                        hRMT = db.HRMT01.Where(x => x.USER_NO == model.txtUSER_NO && x.SCHOOL_NO == model.SCHOOL_NO && x.USER_STATUS == UserStaus.Enabled).FirstOrDefault();
                    }
                    model.SHORT_NAME = hRMT.NAME;

                    List<HRMT01> hRMTlist = new List<HRMT01>();
                    hRMTlist.Add(hRMT);
                    //  return PartialView("_EditDetailsForStudent", model);


                    ADDT38 SaveUp = null;
                    ADDT37 SaveUp1 = null;
                    SaveUp = db.ADDT38.Where(a => a.ROLL_CALL_ID == model.ROLL_CALL_ID && a.BarCode == model.BarCode).FirstOrDefault();
                    SaveUp1 = db.ADDT37.Where(a => a.ROLL_CALL_ID == model.ROLL_CALL_ID).FirstOrDefault();
                    logger.Info("恭喜領取點數 成功 ADDI13");
                    if (SaveUp.SUBJECT == "小獎勵(班級加點，受點數控管)" || SaveUp.SUBJECT == "小獎勵(學校加點，不受點數控管)") {
                        BATCH_CASH_ID = aDDI13Service.GetNewBatchCashId("ADDT20", model.SCHOOL_NO);

                    }
                    var Result = this.Service.InsertADDT38(model, ref db, hRMTlist, Session.SessionID, BATCH_CASH_ID, model.SCHOOL_NO);
                    if (string.IsNullOrWhiteSpace(Result.Message))
                    {
                        logger.Info("恭喜領取點數 成功 ADDI13   TEST");
                        if (SaveUp.SUBJECT == "小獎勵(班級加點，受點數控管)" || SaveUp.SUBJECT == "小獎勵(學校加點，不受點數控管)")
                        {
                            OK = aDDI13Service.InsertADDT20(BATCH_CASH_ID, (byte)SYear, (byte)Semesters, SaveUp1.ROLL_CALL_DESC, hRMTlist, user, model, SaveUp, Session.SessionID, ref db, model.BarCode);

                        }
                        else {

                            OK = aDDI13Service.InsertADDT14(BATCH_CASH_ID, (byte)SYear, (byte)Semesters, SaveUp1.ROLL_CALL_DESC, hRMTlist, user, model, SaveUp, Session.SessionID, ref db, model.BarCode);
                        }
          
                        if (Result.Success)
                        {
                            HRMT01 hRMT01 = new HRMT01();
                            hRMT01 = db.HRMT01.Where(x => x.SCHOOL_NO == model.SCHOOL_NO && x.USER_NO == model.txtUSER_NO && x.USER_STATUS == UserStaus.Enabled).FirstOrDefault();
                            AWAT01 UserCash = db.AWAT01.Where(user => user.SCHOOL_NO == model.SCHOOL_NO && user.USER_NO == model.txtUSER_NO).FirstOrDefault();
                            if (hRMT01.USER_TYPE == UserType.Student)
                            {
                                if (UserCash != null)
                                { model.SumCash = UserCash.CASH_AVAILABLE.Value; }
                                else
                                {
                                    model.SumCash = 0;

                                }
                            }
                            else {

                                AWAT08 aWAT08= db.AWAT08.Where(user => user.SCHOOL_NO == model.SCHOOL_NO && user.USER_NO == model.txtUSER_NO).FirstOrDefault();

                                if (aWAT08 != null)
                                { model.SumCash = aWAT08.CASH_AVAILABLE.Value; }
                                else
                                {
                                    model.SumCash = 0;

                                }
                            }
                         
                            string strMsg = "";

                            strMsg = hRMT.NAME + "獲得" + model.CASH + "點，目前共有" + model.SumCash + "點";
                            TempData[SharedGlobal.StatusMessageName] = "恭喜領取點數 成功！" + strMsg;

                            logger.Info("恭喜領取點數 成功 ADDI13   TEST2");
                            if (user != null)
                            {
                                logger.Info("恭喜領取點數 成功 ADDI13   TEST3");
                                UserProfile.RefreshCashInfo(user, ref db);
                                logger.Info("恭喜領取點數 成功 ADDI13   TEST4");
                                UserProfileHelper.Set(user);
                            }

                            return PartialView("_EditDetailsForStudent", model);
                        }
                        Message += Result.Message;

                        TempData[SharedGlobal.StatusMessageName] = Message;
                        model2.ROLL_CALL_ID = model.ROLL_CALL_ID;
                        model2.SCHOOL_NO1 = model.SCHOOL_NO;


                        model2.wIsQhisSchool = model.wIsQhisSchool;
                        model2.PlayerUrl = model.PlayerUrl;
                        model2.arrWRITING_NO = model.arrWRITING_NO;
                        model2.DATA_ANGLE_TYPE = model.DATA_ANGLE_TYPE;
                        model2.WritingCount = model.WritingCount;
                        model2.WritingShareCount = model.WritingShareCount;
                        model2.ADDT01List = model.ADDT01List;
                        model2.arrAPPLY_NO = model.arrAPPLY_NO;
                        model2.BookCount = model.BookCount;
                        model2.BookShareCount = model.BookShareCount;

                        return PartialView("GetStudentCashIndex", model2);
                    }
                    else
                    {
                        Message += Result.Message;
                        TempData[SharedGlobal.StatusMessageName] = Message;
                        model2.ROLL_CALL_ID = model.ROLL_CALL_ID;
                        model2.SCHOOL_NO1 = model.SCHOOL_NO;
                        model2.wIsQhisSchool = model.wIsQhisSchool;
                        model2.PlayerUrl = model.PlayerUrl;
                        model2.arrWRITING_NO = model.arrWRITING_NO;
                        model2.DATA_ANGLE_TYPE = model.DATA_ANGLE_TYPE;
                        model2.WritingCount = model.WritingCount;
                        model2.WritingShareCount = model.WritingShareCount;
                        model2.ADDT01List = model.ADDT01List;
                        model2.arrAPPLY_NO = model.arrAPPLY_NO;
                        model2.BookCount = model.BookCount;
                        model2.BookShareCount = model.BookShareCount;

                        return PartialView("GetStudentCashIndex", model2);
                    }
                }
                else
                {
                    TempData["StatusMessage"] = "請重新輸入帳號密碼";
                    model2.ROLL_CALL_ID = model.ROLL_CALL_ID;
                    model2.SCHOOL_NO1 = model.SCHOOL_NO;
                    model2.wIsQhisSchool = model.wIsQhisSchool;
                    model2.PlayerUrl = model.PlayerUrl;
                    model2.arrWRITING_NO = model.arrWRITING_NO;
                    model2.DATA_ANGLE_TYPE = model.DATA_ANGLE_TYPE;
                    model2.WritingCount = model.WritingCount;
                    model2.WritingShareCount = model.WritingShareCount;
                    model2.ADDT01List = model.ADDT01List;
                    model2.arrAPPLY_NO = model.arrAPPLY_NO;
                    model2.BookCount = model.BookCount;
                    model2.BookShareCount = model.BookShareCount;

                    return PartialView("GetStudentCashIndex", model2);

                }
            }
            catch (Exception e) {
                ADDI13IndexViewModel model2 = new ADDI13IndexViewModel();
                model2.ROLL_CALL_ID = model.ROLL_CALL_ID;
                model2.SCHOOL_NO1 = model.SCHOOL_NO;
                model2.wIsQhisSchool = model.wIsQhisSchool;
                model2.PlayerUrl = model.PlayerUrl;
                model2.arrWRITING_NO = model.arrWRITING_NO;
                model2.DATA_ANGLE_TYPE = model.DATA_ANGLE_TYPE;
                model2.WritingCount = model.WritingCount;
                model2.WritingShareCount = model.WritingShareCount;
                model2.ADDT01List = model.ADDT01List;
                model2.arrAPPLY_NO = model.arrAPPLY_NO;
                model2.BookCount = model.BookCount;
                model2.BookShareCount = model.BookShareCount;
                logger.Info("恭喜領取點數 成功 ADDI13   BATCH_CASH_ID"+ e.Message);
                return PartialView("GetStudentCashIndex", model2);
            }

        }
        public ActionResult GetCashPerson(BarcodeEditPeopleViewModel model)
        {
            ADDI13Service aDDI13Service = new ADDI13Service();
            ADDI13IndexViewModel model2 = new ADDI13IndexViewModel();


            this.Shared();
            string Message = "";
            bool OK = false;
            int SYear;
            int Semesters;
            SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);
            string BATCH_CASH_ID = aDDI13Service.GetNewBatchCashId("ADDT14", model.SCHOOL_NO);
           
            if (user != null && user.USER_TYPE != "P") {
                if (model.SCHOOL_NO != user.SCHOOL_NO)
                {

                    TempData[SharedGlobal.StatusMessageName] = "此帳號有誤，請重新登入";


                    ADDI13IndexViewModel model23 = new ADDI13IndexViewModel();
                    model23.ROLL_CALL_ID = model.ROLL_CALL_ID;
                    model23.SCHOOL_NO1 = model.SCHOOL_NO;
                    if (user != null)
                    {
                        LogHelper.AddLogToDB(user.SCHOOL_NO, null, System.Web.HttpContext.Current.Request.UserHostAddress, "HOME", "LoginOUT");
                        this.Request.RequestContext.HttpContext.Session[CONSTANT.SESSION_USER_KEY] = null;
                        this.Request.RequestContext.HttpContext.Session[CONSTANT.SESSION_SCHOOL_KEY] = null;
                    }

                    model2.ROLL_CALL_ID = model.ROLL_CALL_ID;
                    model2.SCHOOL_NO1 = model.SCHOOL_NO;
                    return PartialView("_EditDetails1", model);
                }
                string PSW = "";
                PSW = db.ZZT08.Where(x => x.USER_NO == user.USER_NO && x.SCHOOL_NO == user.SCHOOL_NO).Select(x => x.PASSWORD).FirstOrDefault();
                model.txtUSER_NO = user.USER_NO;
                model.txtPASSWORD = PSW;
                if (user.USER_NO.Contains("A")) {


                }
                HRMT01 hRMT = db.HRMT01.Where(x => x.USER_NO == model.txtUSER_NO && x.SCHOOL_NO == model.SCHOOL_NO && x.USER_STATUS == UserStaus.Enabled).FirstOrDefault();
                model.SHORT_NAME = hRMT.NAME;
                List<HRMT01> hRMTlist = new List<HRMT01>();
                hRMTlist.Add(hRMT);
              
                ADDT38 SaveUp = null;
                ADDT37 SaveUp1 = null;
                SaveUp = db.ADDT38.Where(a => a.ROLL_CALL_ID == model.ROLL_CALL_ID && a.BarCode == model.BarCode).FirstOrDefault();
                SaveUp1 = db.ADDT37.Where(a => a.ROLL_CALL_ID == model.ROLL_CALL_ID).FirstOrDefault();
                if (hRMT != null) {
                    IResult result = this.Service.checkBarcode(model.BarCode, ref db, model.SCHOOL_NO, hRMT);
                    if (!result.Success)
                    {
                        TempData[SharedGlobal.StatusMessageName] = result.Message;

                        TempData["StatusMessage"] = result.Message;

                        model2.ROLL_CALL_ID = model.ROLL_CALL_ID;
                        model2.SCHOOL_NO1 = model.SCHOOL_NO;
                        return PartialView("_EditDetails1", model);

                    }

                }
              
                var Result = this.Service.InsertADDT38(model, ref db, hRMTlist, Session.SessionID, BATCH_CASH_ID, model.SCHOOL_NO);
                if (string.IsNullOrWhiteSpace(Result.Message))
                {

                    if (SaveUp.SUBJECT == "小獎勵(班級加點，受點數控管)" || SaveUp.SUBJECT == "小獎勵(學校加點，不受點數控管)")
                    {
                        OK = aDDI13Service.InsertADDT20(BATCH_CASH_ID, (byte)SYear, (byte)Semesters, SaveUp1.ROLL_CALL_DESC, hRMTlist, user, model, SaveUp, Session.SessionID, ref db, model.BarCode);

                    }
                    else
                    {

                        OK = aDDI13Service.InsertADDT14(BATCH_CASH_ID, (byte)SYear, (byte)Semesters, SaveUp1.ROLL_CALL_DESC, hRMTlist, user, model, SaveUp, Session.SessionID, ref db, model.BarCode);
                    }


                    //OK = aDDI13Service.InsertADDT14(BATCH_CASH_ID, (byte)SYear, (byte)Semesters, SaveUp1.ROLL_CALL_DESC, hRMTlist, user, model, SaveUp, Session.SessionID, ref db, model.BarCode);
                    AWAT01 UserCash = db.AWAT01.Where(user => user.SCHOOL_NO == model.SCHOOL_NO && user.USER_NO == model.txtUSER_NO).FirstOrDefault();
                    if (UserCash != null)
                    { model.SumCash = UserCash.CASH_AVAILABLE.Value; }
                    else
                    {
                        model.SumCash = 0;

                    }
                    if (Result.Success)
                    {
                        string strMsg = "";
                        strMsg = hRMT.NAME + "獲得" + model.CASH + "點，目前共有" + model.SumCash + "點";
                        TempData[SharedGlobal.StatusMessageName] = "恭喜領取點數 成功！" + strMsg;
                     
                        if (user != null)
                        {
                            UserProfile.RefreshCashInfo(user, ref db);

                            UserProfileHelper.Set(user);
                        }

                        return PartialView("_EditDetails3", model);
                    }
                    Message += Result.Message;

                    TempData[SharedGlobal.StatusMessageName] = Message;

                    model2.ROLL_CALL_ID = model.ROLL_CALL_ID;
                    model2.SCHOOL_NO1 = model.SCHOOL_NO;
                    return View("Index1", model2);
                }
                else
                {
                    Message += Result.Message;
                    TempData[SharedGlobal.StatusMessageName] = Message;
                    model2.ROLL_CALL_ID = model.ROLL_CALL_ID;
                    model2.SCHOOL_NO1 = model.SCHOOL_NO;
                    return View("Index1", model2);
                }
            }
           else  if (user != null && user.USER_TYPE == "P")
            {

                TempData["StatusMessage"] = "請用學生的帳號領取點數，謝謝。";

                model2.ROLL_CALL_ID = model.ROLL_CALL_ID;
                model2.SCHOOL_NO1 = model.SCHOOL_NO;
                return PartialView("_EditDetails1", model);
            }
            if (!string.IsNullOrWhiteSpace(model.txtUSER_NO) && !string.IsNullOrWhiteSpace(model.txtPASSWORD))
            {
                int ZZT08Count = 0;
                ZZT08Count = db.ZZT08.Where(x => x.USER_NO == model.txtUSER_NO && x.PASSWORD == model.txtPASSWORD &&x.SCHOOL_NO == model.SCHOOL_NO).Count();
                List<HRMT01> hRMTlist = new List<HRMT01>();
                if (ZZT08Count > 0) {
                    HRMT01 hRMT= db.HRMT01.Where(x => x.USER_NO == model.txtUSER_NO && x.SCHOOL_NO == model.SCHOOL_NO && x.USER_STATUS == UserStaus.Enabled).FirstOrDefault();

                    if (hRMT != null && hRMT.USER_TYPE != "P")
                    {


                        model.GRADE = hRMT.GRADE;
                        model.USER_NO = hRMT.USER_NO;
                        model.CLASS_NO = hRMT.CLASS_NO;
                        model.SHORT_NAME = hRMT.NAME;
                        //   model.NAME = hRMT.NAME;
                        model.SEAT_NO = hRMT.SEAT_NO;
                        hRMTlist.Add(hRMT);
                    }
                    else if (hRMT != null && hRMT.USER_TYPE == "P")
                    {

                        TempData["StatusMessage"] = "請用學生的帳號領取點數，謝謝。";

                        model2.ROLL_CALL_ID = model.ROLL_CALL_ID;
                        model2.SCHOOL_NO1 = model.SCHOOL_NO;
                        return PartialView("_EditDetails1", model);
                    }
                    else {
                        TempData["StatusMessage"] = "請重新輸入帳號密碼，此帳號為停用的帳號";
                        model2.ROLL_CALL_ID = model.ROLL_CALL_ID;
                        model2.SCHOOL_NO1 = model.SCHOOL_NO;
                        return PartialView("_EditDetails1", model);
                    }
                    //return PartialView("_EditDetails2", model);

                    ADDT38 SaveUp = null;
                    ADDT37 SaveUp1 = null;
                    SaveUp = db.ADDT38.Where(a => a.ROLL_CALL_ID == model.ROLL_CALL_ID && a.BarCode == model.BarCode).FirstOrDefault();
                    SaveUp1 = db.ADDT37.Where(a => a.ROLL_CALL_ID == model.ROLL_CALL_ID).FirstOrDefault();
                    if (hRMT != null)
                    {
                        IResult result = this.Service.checkBarcode(model.BarCode, ref db, model.SCHOOL_NO, hRMT);
                        if (!result.Success)
                        {
                            TempData[SharedGlobal.StatusMessageName] = result.Message;

                            TempData["StatusMessage"] = result.Message;

                            model2.ROLL_CALL_ID = model.ROLL_CALL_ID;
                            model2.SCHOOL_NO1 = model.SCHOOL_NO;
                            return PartialView("_EditDetails1", model);

                        }

                    }

                    var Result = this.Service.InsertADDT38(model, ref db, hRMTlist, Session.SessionID, BATCH_CASH_ID, model.SCHOOL_NO);

                    if (string.IsNullOrWhiteSpace(Result.Message))
                    {


                        if (SaveUp.SUBJECT == "小獎勵(班級加點，受點數控管)" || SaveUp.SUBJECT == "小獎勵(學校加點，不受點數控管)")
                        {
                            OK = aDDI13Service.InsertADDT20(BATCH_CASH_ID, (byte)SYear, (byte)Semesters, SaveUp1.ROLL_CALL_DESC, hRMTlist, user, model, SaveUp, Session.SessionID, ref db, model.BarCode);

                        }
                        else
                        {

                            OK = aDDI13Service.InsertADDT14(BATCH_CASH_ID, (byte)SYear, (byte)Semesters, SaveUp1.ROLL_CALL_DESC, hRMTlist, user, model, SaveUp, Session.SessionID, ref db, model.BarCode);
                        }


                    //    OK = aDDI13Service.InsertADDT14(BATCH_CASH_ID, (byte)SYear, (byte)Semesters, SaveUp1.ROLL_CALL_DESC, hRMTlist, user, model, SaveUp, Session.SessionID, ref db, model.BarCode);

                        AWAT01 UserCash = db.AWAT01.Where(user => user.SCHOOL_NO == model.SCHOOL_NO && user.USER_NO == model.txtUSER_NO).FirstOrDefault();
                        if (UserCash != null)
                        { model.SumCash = UserCash.CASH_AVAILABLE.Value; }
                        else
                        {
                            model.SumCash = 0;

                        }
                        if (Result.Success)
                        {
                            string strMsg = "";
                            strMsg = hRMT.NAME + "獲得" + model.CASH + "點，目前共有" + model.SumCash + "點";
                            TempData[SharedGlobal.StatusMessageName] = "恭喜領取點數 成功！" + strMsg;
                            if (user != null)
                            {
                                UserProfile.RefreshCashInfo(user, ref db);

                                UserProfileHelper.Set(user);
                            }

                            return PartialView("_EditDetails3", model);
                        }
                        Message += Result.Message;

                        TempData[SharedGlobal.StatusMessageName] = Message;

                        model2.ROLL_CALL_ID = model.ROLL_CALL_ID;
                        model2.SCHOOL_NO1 = model.SCHOOL_NO;
                        return View("Index1", model2);
                    }
                    else
                    {
                        Message += Result.Message;
                        TempData[SharedGlobal.StatusMessageName] = Message;
                        model2.ROLL_CALL_ID = model.ROLL_CALL_ID;
                        model2.SCHOOL_NO1 = model.SCHOOL_NO;
                        return View("Index1", model2);
                    }
                }
                else
                {

                    TempData["StatusMessage"] = "請重新輸入帳號密碼";
                    model2.ROLL_CALL_ID = model.ROLL_CALL_ID;
                    model2.SCHOOL_NO1 = model.SCHOOL_NO;
                    return PartialView("_EditDetails1", model);
                }
            }
            else {

                TempData["StatusMessage"] = "請重新輸入帳號密碼";
                model2.ROLL_CALL_ID = model.ROLL_CALL_ID;
                model2.SCHOOL_NO1 = model.SCHOOL_NO;
                return PartialView("_EditDetails1", model);

            }
            return View("Index1", new { ROLL_CALL_ID = model.ROLL_CALL_ID, SCHOOL_NO1 = model.SCHOOL_NO });
        }
        public ActionResult _OpenPersonAddData(string CARD_NO)
        {
            this.Shared();
            var model = this.Service.GetAddPersonData(CARD_NO, ref db, SCHOOL_NO);

            return PartialView("_EditDetails", model);
        }

        public ActionResult _EditDetailsList(ADDI13IndexViewModel Item)
        {
            this.Shared();
            List<ADDI13EditPeopleViewModel> peoples = new List<ADDI13EditPeopleViewModel>();
            RollCallCashViewModel CallCashModel = new RollCallCashViewModel();
            CallCashModel.Keyword = Item.ROLL_CALL_ID;
            CallCashModel = Service.GetRollCallCashData(CallCashModel, ref db);
            if (CallCashModel.Details.Count() > 0)
            {
                using (var txn = Queries.GetNewReadUncommittedScope())
                {
                    Item.Details = (from item in CallCashModel.Details
                                    join a in db.BDMT01 on item.SCHOOL_NO equals a.SCHOOL_NO
                                    select new ADDI13EditPeopleViewModel
                                    {
                                        SCHOOL_NO = item.SCHOOL_NO,
                                        SHORT_NAME = a.SHORT_NAME,
                                        USER_NO = item.USER_NO,
                                        NAME = item.NAME,
                                        GRADE = item.GRADE,
                                        CLASS_NO = item.CLASS_NO,
                                        SEAT_NO = item.SEAT_NO,
                                        CHG_DATE = item.CHG_DATE ?? DateTime.Now
                                    }
                                   ).ToList();
                }
            }
            return PartialView(Item.Details);
        }

        /// <summary>
        /// 匯出
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>

        public ActionResult Export(ADDI13IndexViewModel model)
        {
            this.Shared();
            RollCallCashViewModel CallCashModel = new RollCallCashViewModel();
            ADDI13IndexViewModel model1 = new ADDI13IndexViewModel();
            CallCashModel.Keyword = model.ROLL_CALL_ID;
            CallCashModel = Service.GetRollCallCashData(CallCashModel, ref db);
            if (CallCashModel.Details.Count() > 0)
            {
                using (var txn = Queries.GetNewReadUncommittedScope())
                {
                    model1.Details = (from item in CallCashModel.Details
                                      join a in db.BDMT01 on item.SCHOOL_NO equals a.SCHOOL_NO
                                      select new ADDI13EditPeopleViewModel
                                      {
                                          SCHOOL_NO = item.SCHOOL_NO,
                                          SHORT_NAME = a.SHORT_NAME,
                                          USER_NO = item.USER_NO,
                                          NAME = item.NAME,
                                          GRADE = item.GRADE,
                                          CLASS_NO = item.CLASS_NO,
                                          SEAT_NO = item.SEAT_NO,
                                          CHG_DATE = item.CHG_DATE ?? DateTime.Now
                                     }
                                   ).ToList();
               
                }
            }
            List<ADDI13EditPeopleViewModel> PeopleViewModel = new List<ADDI13EditPeopleViewModel>();
            if (model != null&& model.Details != null && model.Details.Count() > 0) {
                PeopleViewModel.AddRange(model.Details);
            }
            if (model1 != null && model1.Details != null && model1.Details.Count() > 0)
            {
                PeopleViewModel.AddRange(model1.Details);
            }

            if (
                PeopleViewModel != null && PeopleViewModel.Count() > 0
                ) {
                model.Details = PeopleViewModel;
            }
   
            string Message = string.Empty;
            string FileName = Service.GetToExceFile(model, ref db, ref Message);

            if (!string.IsNullOrWhiteSpace(Message))
            {
                TempData["StatusMessage"] = Message;
                return RedirectToAction("Index");
            }

            if (System.IO.File.Exists(FileName))
            {
                return File(System.IO.File.ReadAllBytes(FileName), "application/vnd.ms-excel", "報到資料.xlsx");//輸出檔案給Client端
            }
            else
            {
                return RedirectToAction(ErrorHelper.ErrorVal.NotFileError, "Error");
            }
        }

        /// <summary>
        /// 點名存檔
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>

        public ActionResult RollCallSave(ADDI13IndexViewModel model)
        {
            this.Shared();

            if (model == null) model = new ADDI13IndexViewModel();

            string Message = string.Empty;

            if ((model.Details?.Count ?? 0) == 0)
            {
                return View("Index", model);
            }

            if (ModelState.IsValid == false)
            {
                Message = "警告!輸入的內容有錯誤";
            }
            else
            {
                var Result = this.Service.SaveRollCallData(model, ref db);

                if (Result.Success)
                {
                    TempData[SharedGlobal.StatusMessageName] = "點名完畢";
                    TempData["BackInfo"] = "已儲存完畢，請重新登入";
                    return RedirectToAction(nameof(ADDI13Controller.RollCallIndex));
                }

                Message += Result.Message;
            }

            TempData[SharedGlobal.StatusMessageName] = Message;

#if DEBUG
            var errorsaa = ModelState
                        .Where(x => x.Value.Errors.Count > 0)
                        .Select(x => new { x.Key, x.Value.Errors })
                        .ToArray();
#endif

            return View("Index", model);
        }

        /// <summary>
        /// 一次性需求 問李小姐
        /// </summary>
        /// <returns></returns>
        public ActionResult AnswerRaceYes()
        {
            this.Shared();
            this.LoginOUT();
            return View();
        }

        /// <summary>
        /// 一次性需求 問李小姐
        /// </summary>
        /// <returns></returns>
        public ActionResult AnswerRaceNO()
        {
            this.Shared();
            this.LoginOUT();
            return View();
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }

        private void LoginOUT()
        {
            UserProfile LoginUser = UserProfileHelper.Get();

            if (LoginUser != null)
            {
                LogHelper.AddLogToDB(LoginUser.SCHOOL_NO, null, System.Web.HttpContext.Current.Request.UserHostAddress, "HOME", "LoginOUT");
                this.Request.RequestContext.HttpContext.Session[CONSTANT.SESSION_USER_KEY] = null;
            }
        }

        #region Shared

        private void SetTitle(string Panel_Title = "")
        {
            if (!string.IsNullOrWhiteSpace(Panel_Title))
            {
                ViewBag.Panel_Title = Panel_Title;
            }
            else
            {
                ViewBag.Panel_Title = Bre_Name + " - 列表 ";
            }
        }

        private void Shared(string Panel_Title = "")
        {
            ViewBag.BRE_NO = Bre_NO;

            if (!string.IsNullOrWhiteSpace(Panel_Title))
            {
                ViewBag.Panel_Title = Bre_Name + "-" + Panel_Title;
            }
            else
            {
                ViewBag.Panel_Title = Bre_Name + " - 列表 ";
            }

            user = UserProfileHelper.Get();
            SCHOOL_NO = UserProfileHelper.GetSchoolNo();

            if (user != null)
            {
                if (user.SCHOOL_NO != null)
                {
                    SCHOOL_NO = user.SCHOOL_NO;
                    USER_NO = user.USER_NO;
                }
            }
            IsAdmin = HRMT24_ENUM.CheckQAdmin(user);
            ViewBag.IsAdmin = IsAdmin;
        }

        #endregion Shared
    }
}