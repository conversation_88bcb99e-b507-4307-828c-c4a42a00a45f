﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public partial class HRMT07
    {

        /// <summary>
        /// 判斷是否訂閱此學生
        /// </summary>
        /// <param name="SchoolNo">學校代碼</param>
        /// <param name="UserNo">帳號</param>
        /// <returns>true 已訂閱, false 未訂閱</returns>
        static public bool CkADDStudent(string SchoolNo, string UserNo, string STUDENT_USER_NO, ECOOL_DEVEntities db = null)
        {

            bool dbNull = false;
            bool ReturnVal = false;

            if (db == null) { db = new ECOOL_DEVEntities(); dbNull = true; }

            if (db.HRMT07.Where(a => a.SCHOOL_NO == SchoolNo && a.USER_NO == UserNo && a.STUDENT_USER_NO== STUDENT_USER_NO).Any())
            {
                ReturnVal = true;
            }

            if (dbNull)
            {
                db.Dispose();
            }

            return ReturnVal;
        }

        /// <summary>
        /// 取得已訂閱的好友
        /// </summary>
        /// <param name="user"></param>
        /// <param name="db"></param>
        /// <returns>回傳 List HRMT07</returns>
        static public List<HRMT07> GetMyADDStudent(UserProfile user, ECOOL_DEVEntities db = null)
        {
            if (user == null)
            {
                return null;
            }

            if (isFriends(user) == false)
            {
                return null;
            }

            bool dbNull = false;

            if (db == null) { db = new ECOOL_DEVEntities(); dbNull = true; }

            List<HRMT07> T07 = db.HRMT07.Where(a => a.SCHOOL_NO == user.SCHOOL_NO && a.USER_NO == user.USER_NO).ToList();

            if (dbNull) db.Dispose();

            if (T07 == null || T07.Count() == 0)
            {
                return null;
            }
            else
            {
                return T07;
            }

        }

        static public int GetMyCount(UserProfile user, ECOOL_DEVEntities db = null)
        {
            var H07 = GetMyADDStudent(user, db);

            if (H07!=null)
            {
                return H07.Count();
            }
            else
            {
                return 0;
            }
           
        }



        /// <summary>
        /// 取得已訂閱的好友
        /// </summary>
        /// <param name="user"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        static public string GetStringMyADDStudent(UserProfile user, ECOOL_DEVEntities db = null)
        {

            var Data = GetMyADDStudent(user, db);

            if (Data == null || Data.Count() == 0)
            {
                return string.Empty;
            }
            else
            {
                string StringMyStudent = string.Join(",", Data.Select(a => a.STUDENT_USER_NO));

                return StringMyStudent;
            }
        }

        /// <summary>
        /// 取得已訂閱的好友
        /// </summary>
        /// <param name="user"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        static public string[] GetArrMyADDStudent(UserProfile user, ECOOL_DEVEntities db = null)
        {

            var Data = GetMyADDStudent(user, db);

            if (Data == null || Data.Count() == 0)
            {
                return null;
            }
            else
            {
                string[] ArrMyStudent = Data.Select(a => a.STUDENT_USER_NO).ToArray();
                return ArrMyStudent;
            }
        }

        //判斷是否可加好友
        static public bool isFriends(UserProfile user)
        {
            if (user == null)
            {
                return false;
            }


            if (user.USER_TYPE == UserType.Student || user.USER_TYPE == UserType.Parents || user.USER_TYPE == UserType.Teacher)
            {
                return true;
            }
            else
            {
                return false;
            }

        }

        /// <summary>
        /// 增加好友
        /// </summary>
        /// <param name="SCHOOL_NO"></param>
        /// <param name="USER_NO"></param>
        /// <param name="STUDENT_USER_NO"></param>
        /// <param name="Msg"></param>
        /// <returns></returns>
        static public bool AddFriendsData(string SCHOOL_NO, string USER_NO,string STUDENT_USER_NO,out string Msg)
        {
            string ErrMsg = string.Empty;

            if (string.IsNullOrWhiteSpace(SCHOOL_NO) || string.IsNullOrWhiteSpace(USER_NO) || string.IsNullOrWhiteSpace(STUDENT_USER_NO))
            {
                Msg = "未傳入有效參數";
                return false;
            }


            using (ECOOL_DEVEntities db = new ECOOL_DEVEntities())
            {
                if (CkADDStudent(SCHOOL_NO, USER_NO, STUDENT_USER_NO, db))
                {
                    Msg = "此學生已訂閱過";
                    return false;
                }


                int HCount = db.HRMT07.Where(a => a.SCHOOL_NO == SCHOOL_NO && a.USER_NO == USER_NO).Count();
                if (HCount>=5)
                {
                    Msg = "您的好友數已滿5位";
                    return false;
                }



                HRMT07 T07 = new HRMT07();
                T07.SCHOOL_NO = SCHOOL_NO;
                T07.USER_NO = USER_NO;
                T07.STUDENT_USER_NO = STUDENT_USER_NO;
                T07.CRE_PERSON = SCHOOL_NO + '_' + USER_NO;
                T07.CRE_DATE = DateTime.Now;

                db.HRMT07.Add(T07);

                try
                {
                    db.SaveChanges();
                    Msg = "訂閱成功";
                    return true;
                }
                catch (Exception EX)
                {
                    Msg = "新增訂閱發生錯誤;"+ EX.Message;
                    return false;
                }
            }
        }
    }
}
