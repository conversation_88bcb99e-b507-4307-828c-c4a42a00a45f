﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class GameRewardInfoViewModel
    {
        /// <summary>
        ///活動ID
        /// </summary>
        [DisplayName("活動ID")]
        public string GAME_NO { get; set; }

        /// <summary>
        ///鼓勵語與獎勵 - 項次
        /// </summary>
        [DisplayName("鼓勵語與獎勵 - 項次")]
        public byte? ITEM_NO { get; set; }

        /// <summary>
        ///答題正確比例-起
        /// </summary>
        [Required]
        [Range(0, 100, ErrorMessage = "*「答題正確比例(起)」此欄位請輸入0~100數字")]
        [DisplayName("答題正確比例(起)")]
        public decimal? RATE_S { get; set; }

        /// <summary>
        ///答題正確比例-迄
        /// </summary>
        [DisplayName("答題正確比例(迄)")]
        [Required]
        [Range(0, 100, ErrorMessage = "*「答題正確比例(迄)」此欄位請輸入0~100數字")]
        public decimal? RATE_E { get; set; }

        /// <summary>
        ///鼓勵語
        /// </summary>
        [DisplayName("鼓勵語")]
        [Required]
        public string REWARD_DESC { get; set; }

        /// <summary>
        ///獎勵點數
        /// </summary>
        [DisplayName("獎勵點數")]
        [Required]
        public int? REWARD_CASH { get; set; }

        /// <summary>
        ///修改人
        /// </summary>
        [DisplayName("修改人")]
        public string CHG_PERSON { get; set; }

        /// <summary>
        ///修改時間
        /// </summary>
        [DisplayName("修改時間")]
        public DateTime? CHG_DATE { get; set; }
    }
}