﻿@model ECOOL_APP.com.ecool.Models.DTO.ADDI09EditViewModel
@using ECOOL_APP.com.ecool.Models.DTO
@using ECOOL_APP.com.ecool.util
@using ECOOL_APP
@using EcoolWeb.Models
<link href="~/Content/eCoolRWDtable/eCoolRWDtable.min.css?@DateTime.Now.ToString("yyyy_MM_dd_H_mm_ss");" rel="stylesheet" />
@{

    UserProfile user = UserProfileHelper.Get();
    string SYS_TABLE_TYPE = Request.Params["SYS_TABLE_TYPE"];
    bool Individual_Give = Convert.ToBoolean(Request.Params["Individual_Give"] == "" ? "false" : Request.Params["Individual_Give"]);
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
}
@*<style>
        .select-editable {
            position: relative;
            background-color: white;
            border: solid grey 1px;
            width: 120px;
            height: 18px;
        }

            .select-editable select {
                position: absolute;
                top: 0px;
                left: 0px;
                font-size: 14px;
                border: none;
                width: 120px;
                margin: 0;
            }

            .select-editable input {
                position: absolute;
                top: 0px;
                left: 0px;
                width: 100px;
                padding: 1px;
                font-size: 12px;
                border: none;
            }

                .select-editable select:focus, .select-editable input:focus {
                    outline: none;
                }
    </style>*@
<div style="text-align: center; margin-top: 20px; margin-bottom: 30px;">
    <span class="glyphicon glyphicon-circle-arrow-down fa-2x" aria-hidden="true" style="color:#004da0"></span>
</div>

@using (Html.BeginForm("Edit1", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form2", name = "form2", enctype = "multipart/form-data", @AutoComplete = "Off" }))
{
    if (Model.ShowType == "Edit1" && Individual_Give) // Edit畫面可修改點數
    {
        <div class="Div-EZ-ADDI09">
            <div class="form-horizontal">
                <div style="height:15px"></div>
                <div class="row">
                    <div class="col-md-3">
                        <div class="Caption_Div">
                            批次給分的貼心工具
                        </div>
                    </div>
                    <div class="col-md-9">
                        <div class="input-group">
                            <input class="form-control input-sm text-box single-line" id="sweetAutoCash" placeholder="輸入獎懲點數" type="number" value="">
                            <span class="input-group-btn">
                                <button class="btn btn-default btn-sm" type="button" onclick="sweetAutoImport()">全部自動帶入這個點數</button>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div style="text-align: center; margin-top: 20px; margin-bottom: 30px;">
            <span class="glyphicon glyphicon-circle-arrow-down fa-2x" aria-hidden="true" style="color:#004da0"></span>
        </div>
    }
    if (Model.ShowType == "Edit1") // Edit畫面可修改點數
    {
        <center style="text-align: center; margin-top: 30px; margin-bottom: 30px;font-size:20px">再次確認列表</center>}

    <div class="Div-EZ-ADDI09">
        <div class="form-horizontal">
            <div class="row">
                <div class="col-xs-10">

                    <label class="text-danger">
                        <samp>

                            @Html.HiddenFor(model => model.ShowType)

                            @if (Model.ShowType == ECOOL_APP.com.ecool.Models.DTO.ADDI09EditViewModel.ShowTypeVal.ShowTypeQSV)
                            {
                                <samp>* 已選取人員清單，請點選學號「刪除」</samp>

                            }
                            else
                            {
                                <samp>* 已選取 / 匯入 - 人員清單</samp>
                            }
                        </samp>
                        <samp>
                            總共已選 <span class="badge">@Model.SelectDataCount</span> 人
                        </samp>
                    </label>
                </div>
                @*<div class="col-xs-2 text-right">
                        <a href="@Url.Action("TempDataToExcel", new { SYS_TABLE_TYPE = ViewBag.SYS_TABLE_TYPE })" title="匯出excel">
                            <img src="~/Content/img/excel-icon-16677.png" style="width:25px;height:auto" />
                        </a>
                    </div>*@
            </div>

            <br />
            <div class="btn-group btn-group-xs" role="group">
                @Html.HiddenFor(model => model.DivHeight)
                <button type="button" class="btn btn-default" id="DivHeight_plus" onclick="setDivHeight('+')"><i class="glyphicon glyphicon-plus"></i></button>
                <button type="button" class="btn btn-default" id="DivHeight_minus" onclick="setDivHeight('-')"><i class="glyphicon glyphicon-minus"></i></button>
            </div>

            <div class="table-responsive" id="DivSelectData">
                <table class="table-ecool  table-hover">
                    <thead>
                        <tr>

                            <th align="center">
                                @*全選<input onclick="CheckAll(this)" value="全選" type="checkbox" />*@
                            </th>
                            <th class="hidden-xs">
                                @Html.DisplayNameFor(model => model.DataList.First().USER_NO)
                            </th>
                            <th class="hidden-xs">
                                @Html.DisplayNameFor(model => model.DataList.First().SNAME)
                            </th>
                            @if (SYS_TABLE_TYPE != ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_AWAT08_LOG)
                            {
                                <th class="hidden-xs">
                                    @Html.DisplayNameFor(model => model.DataList.First().CLASS_NO)
                                </th>

                                <th class="hidden-xs">
                                    @Html.DisplayNameFor(model => model.DataList.First().SEAT_NO)
                                </th>
                                <th class="hidden-xs">班級幹部<br />(選項請點擊V圖示<br />或直接輸入內容)</th>
                                <th class="hidden-xs">具體事蹟<br />(選項請點擊V圖示<br />或直接輸入內容)</th>
                                <th class="hidden-xs" style="width: 74px;">酷幣點數</th>

                            }

                            @if (Model.ShowType == "Edit1" && Individual_Give) // Edit畫面可修改點數
                            {
                                <th align="center">
                                    @Html.DisplayNameFor(model => model.DataList.First().TempCash)
                                </th>
                            }
                        </tr>
                    </thead>
                    <tbody>
                        @{int Num = 0; }
                        @foreach (var item in Model.DataList)
                        {
                       
                          
                        <tr style="cursor:pointer">
                           
                                <td class="text-center">
                                    @*<input id="DataList_@Num" name="DataList[@Num]" type="checkbox" value="true" />*@
                                </td>
                       
                                <td class="hidden-xs" data-th="學號：" onclick="@(Individual_Give ? "" : "onBtnLink('" + item.USER_NO + "','')")" align="center">
                                    @if (!string.IsNullOrWhiteSpace(item.SEAT_NO) && item.USER_NO.Length != 9)
                                    {
                                        @item.USER_NO
                                    }
                                    else
                                    {
                                        @StringHelper.LeftStringR(item.USER_NO, 5, "*****");
                                    }
                                </td>

                                <td    data-th="簡稱：" onclick="@(Individual_Give ? "" : "onBtnLink('" + item.USER_NO + "','')")" align="center">
                                    @Html.DisplayFor(modelItem => item.SNAME)
                                </td>
                                @if (SYS_TABLE_TYPE != ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_AWAT08_LOG)
                                {
                                    <td   class="hidden-xs" data-th="班級：" onclick="@(Individual_Give ? "" : "onBtnLink('" + item.USER_NO + "','')")" align="center">
                                        @Html.DisplayFor(modelItem => item.CLASS_NO)
                                    </td>
                                    <td  class="hidden-xs" data-th="座號：" onclick="@(Individual_Give ? "" : "onBtnLink('" + item.USER_NO + "','')")" align="center">
                                        @Html.DisplayFor(modelItem => item.SEAT_NO)
                                    </td>

                                    <td data-th="班級幹部：" class="flex50" align="center">


                                        <div class="select-editable">
                                            @Html.DropDownListFor(modelItem => item.PersonType, (IEnumerable<SelectListItem>)ViewBag.PersonType, new { @onchange = "this.nextElementSibling.value=this.value;ADDItem('" + item.USER_NO + "','');" })
                                            <input type="text" name="PersonType" id="PersonType@(item.USER_NO)" value="@(item.PersonType)" onchange="ADDItem('@item.USER_NO','')" />
                                            @Html.ValidationMessageFor(modelItem => item.PersonType, "", new { @class = "field-validation-valid text-danger" })
                                        </div>
                                    </td>
                                    <td data-th="具體事蹟：" class="flex50" align="center">


                                        <div class="select-editable">
                                            @Html.DropDownListFor(modelItem => item.TABLType, (IEnumerable<SelectListItem>)ViewBag.TABLType, new { @onchange = "this.nextElementSibling.value=this.value;ADDItem('" + item.USER_NO + "','');" })
                                            <input type="text" name="TABLType" id="TABLType@(item.USER_NO)" value="@(item.TABLType)" onchange="ADDItem('@item.USER_NO','')" />
                                            @Html.ValidationMessageFor(modelItem => item.TABLType, "", new { @class = "text-danger" })
                                        </div>
                                    </td>
                                    <td data-th="酷幣點數：" class="flex50" align="center">

                                        <input class="text-right" style="max-width: 50px;" name="Cash" id="Cash@(item.USER_NO)" value="@(item.Cash)" style="width:50px" onchange="ADDItem('@item.USER_NO','')" />
                                    </td>
                                }

                                @if (Model.ShowType == "Edit1" && Individual_Give) // Edit畫面可修改點數
                                {
                                    <td align="center">
                                        <input type="text" class="btn btn-default btn-xs"
                                               placeholder="0"
                                               style="margin-top:11px;"
                                               onchange="changeCash(event, '@item.USER_NO','@user.USER_NO')"
                                               value="@(item.TempCash == 0 ? "" : item.TempCash.ToString())" />
                                    </td>

                                }
                        </tr>
                            Num++;
                        }
                    </tbody>
                </table>
            </div>
        </div>

        <div class="text-center">
            @if (Model.ShowType == ECOOL_APP.com.ecool.Models.DTO.ADDI09EditViewModel.ShowTypeVal.ShowTypeQSV1)
            {
            <a type="button" id="myButton" href="#" data-loading-text="Loading..." class="btn btn-default" autocomplete="off" onclick="checkInfos();">
                設定完成0000
            </a>
                @*<button id="myButton" autocomplete="off" type="button" onclick="checkInfos();" class="btn btn-default"> 確定匯入1</button>*@

            }
        </div>
    </div>
}
<script language="JavaScript">
    var targetFormID = '#form2';
    function checkInfos() {
        console.log("checkstatus" );
        var checkstatus = true;
        var str = "";
        $("input[name='TABLType']").each(function () {
            var te = $(this).val();
            if (te == "" || te == undefined) {
                //alert("請確認是否有填寫具體事蹟");
                str = "請確認是否有填寫具體事蹟"
                checkstatus = false;
            }
            else {
                $("input[name='PersonType']").each(function () {

                    var te1 = $(this).val();
                    if (te1 == "" || te1 == undefined) {
                       // alert("請確認是否有填寫班級幹部");

                        str = "請確認是否有填寫班級幹部";
                        checkstatus = false;
                    }
                    else {
                        $("input[name='Cash']").each(function () {
                            var te2 = $(this).val();
                            if (te2 == ""||te2 == 0 || te2 == undefined) {
                               // alert("請確認是否有填寫酷幣點數");

                                str = "請確認是否有填寫酷幣點數";
                                checkstatus = false;
                            }


                        });


                    }
                });

            }
            if (checkstatus == false) {
                alert(str);
                return false;
            }

            console.log("checkstatus" + checkstatus);
        })

        console.log("checkstatus" + checkstatus);
        //alert("checkstatus" + checkstatus);
        if (checkstatus === true) {
                    $("#myButton").attr('href', '@Url.Action("Edit1", (string)ViewBag.BRE_NO, new { SYS_TABLE_TYPE = ViewBag.SYS_TABLE_TYPE, ADDT14_STYLE = ViewBag.ADDT14_STYLE })')

          //  alert("checkstatus" + checkstatus);
            $("#myButton").submit();
            @*var xhr = new XMLHttpRequest();
            xhr.open("POST", '@Url.Action("Edit1", (string)ViewBag.BRE_NO, new { SYS_TABLE_TYPE = ViewBag.SYS_TABLE_TYPE, ADDT14_STYLE = ViewBag.ADDT14_STYLE })');
            xhr.onload = function () {
                console.log(this.response);
            };
            xhr.send(data);
             $("#myButton").attr('href', '@Url.Action("Edit1", (string)ViewBag.BRE_NO, new { SYS_TABLE_TYPE = ViewBag.SYS_TABLE_TYPE, ADDT14_STYLE = ViewBag.ADDT14_STYLE })')

          //  alert("checkstatus" + checkstatus);
            $("#myButton").submit();*@
                  @*$.ajax({
            url: '@Url.Action("Edit1", (string)ViewBag.BRE_NO, new { SYS_TABLE_TYPE = ViewBag.SYS_TABLE_TYPE, ADDT14_STYLE = ViewBag.ADDT14_STYLE })',
                      data: $(targetFormID).serialize(),
            type: 'post',
            async: false,
            cache: false,
            dataType: 'html',
            success: function (data) {
                $('#QuerySelectPageContent1').html(data);
                if (ShowType == '@ECOOL_APP.com.ecool.Models.DTO.ADDI09EditViewModel.ShowTypeVal.ShowTypeQSV1') {
                    funAjax()
                }
            }
        });*@

        }
        else { return false; }
    }
    DivHeight()
    $('#EditmyButton').on('click', function () {

      $.ajax({
            url: '@Url.Action("Edit1", (string)ViewBag.BRE_NO, new { SYS_TABLE_TYPE = ViewBag.SYS_TABLE_TYPE, ADDT14_STYLE = ViewBag.ADDT14_STYLE })',
            data: data,
            type: 'post',
            async: false,
            cache: false,
            dataType: 'html',
            success: function (data) {
                $('#QuerySelectDataList1').html(data);
                if (ShowType == '@ECOOL_APP.com.ecool.Models.DTO.ADDI09EditViewModel.ShowTypeVal.ShowTypeQSV1') {
                    funAjax()
                }
            }
        });
        form1.submit();
    });
    function ADDItem(USER_NO, DataType) {
          var PersonTypestr = "";
        var TABLTypestr = "";
        var Cash = "";
        var ShowType = $('#ShowType').val();
        PersonTypestr = "#PersonType" + USER_NO;
        TABLTypestr = "#TABLType" + USER_NO;
        Cash = "#Cash" + USER_NO;

        var PersonTypeItem = $(PersonTypestr).val();
        var TABLTypeItem = $(TABLTypestr).val();
        var CashItem = $(Cash).val();
        if (DataType == '') {
            if (ShowType == '@ECOOL_APP.com.ecool.Models.DTO.ADDI09EditViewModel.ShowTypeVal.ShowTypeQSV1') {
                DataType = '@EcoolWeb.Controllers.ADDI09Controller.DataTypeVal.DataTypeM'
            }
        }

        var data = {
            "SYS_TABLE_TYPE": '@ViewBag.SYS_TABLE_TYPE',
            "ADDT14_STYLE": '@ViewBag.ADDT14_STYLE',
            "ShowType": ShowType,
            "DivHeight": $('#DivHeight').val(),
            "USER_NO": USER_NO,
            "DataType": DataType,
            "PersonType": PersonTypeItem,
            "TABLType": TABLTypeItem,
            "Cash" :CashItem
        };

        $.ajax({
            url: '@Url.Action("_QuerySelectDataList1", (string)ViewBag.BRE_NO, new { Individual_Give = Individual_Give })',
            data: data,
            type: 'post',
            async: false,
            cache: false,
            dataType: 'html',
            success: function (data) {
             
                console.log("_QuerySelectDataList1");
                $('#QuerySelectDataList1').html(data);
               
                if (ShowType == '@ECOOL_APP.com.ecool.Models.DTO.ADDI09EditViewModel.ShowTypeVal.ShowTypeQSV1') {
                    funAjax()
                }
            }
        });
    }
    function CheckAll(obj) {
        var checkvalue = $(obj).is(":checked")
        if (checkvalue) {
            $("input[type='checkbox'][value='Y']").each(function () { this.checked = true; });

        }
        else {
            $("input[type='checkbox'][value='Y']").each(function () { this.checked = false; });
        }
    }
    function DivHeight() {
        var FixedHeight = 300;
        var Height = $('#DivSelectData').innerHeight();

        var DivHeight = $('#DivHeight').val();
        if (DivHeight == '') DivHeight = '@ECOOL_APP.com.ecool.Models.DTO.ADDI09EditViewModel.DivHeightVal.DivHeightM'

        $('#DivHeight_plus').hide();
        $('#DivHeight_minus').hide();
        if (DivHeight == "-") {
            $('#DivHeight_plus').show();
        }
        else {
            $('#DivHeight_minus').show();
        }

        if (Height > FixedHeight && DivHeight == "-") {
            $('#DivSelectData').css("height", FixedHeight).css("overflow", "auto")
        }
        else {
            $('#DivSelectData').removeClass();
        }
    }

    function onBtnLink(USER_NO, DataType) {
        var PersonTypestr = "";
        var TABLTypestr = "";
        var ShowType = $('#ShowType').val();
        //PersonTypestr = "#PersonType" + USER_NO;
        //TABLTypestr = "#TABLTypeType" + USER_NO;
        //var PersonTypeItem = $("'"+PersonTypestr+"'").val();
        //var TABLTypeItem = $("'" + TABLTypestr + "'").val();
        if (DataType == '') {
            if (ShowType == '@ECOOL_APP.com.ecool.Models.DTO.ADDI09EditViewModel.ShowTypeVal.ShowTypeQSV1') {
                DataType = '@EcoolWeb.Controllers.ADDI09Controller.DataTypeVal.DataTypeD'
            }
        }

        var data = {
            "SYS_TABLE_TYPE": '@ViewBag.SYS_TABLE_TYPE',
            "ADDT14_STYLE": '@ViewBag.ADDT14_STYLE',
            "ShowType": ShowType,
            "DivHeight": $('#DivHeight').val(),
            "USER_NO": USER_NO,
            "DataType": DataType
        };

        $.ajax({
            url: '@Url.Action("_QuerySelectDataList1", (string)ViewBag.BRE_NO, new { Individual_Give = Individual_Give })',
            data: data,
            type: 'post',
            async: false,
            cache: false,
            dataType: 'html',
            success: function (data) {
                $('#QuerySelectDataList1').html(data);
                if (ShowType == '@ECOOL_APP.com.ecool.Models.DTO.ADDI09EditViewModel.ShowTypeVal.ShowTypeQSV1') {
                    funAjax()
                }
            }
        });

    }

    function setDivHeight(Value) {

        $('#DivHeight').val(Value);

        onBtnLink('', '')
    }

    // -------------------- 手動加點 --------------------
    // -------------------- ------- --------------------

    function sweetAutoImport() {
        ChangeCashAJAX(true, '', '','');
    }

    function changeCash(e, userno,loginUserNO) {
        console.log("target");
        //let cash = $(e.target).val();
        ChangeCashAJAX(false, userno, e.target.value);
    }
    function setTime() {
        timerr++;
        if (timerr < "70") {
            $("#barr").css("width", timerr + "%");

        }
    }

    function ChangeCashAJAX(isAll, userno, cash) {

       @*if ('@user.USER_NO' == loginUserNO) {*@
        let data = {
            vm :[]
            };

        // 全部改動
            if (isAll) {

            let dataList = JSON.parse('@Html.Raw(Json.Encode(Model.DataList))');
        let cash = $("#sweetAutoCash").val();
            $.each(dataList, function (idx, item) {
            data.vm.push({ User_No: item.USER_NO, cash: cash});
        });
        } else {
            data.vm.push({ User_No: userno, cash: cash });
        }

        // ajax
        $.ajax({
    url: '@Url.Action("ChangeTempCash", (string)ViewBag.BRE_NO)',
            data: data,
            type: 'post',
            async: false,
            cache: false,
            dataType: 'html',
            success: function () {

                if (isAll) {
                    console.log("onBtnLink");
                onBtnLink('', '');
            }
        }
            });
        }
    //}
    // -------------------- ------- --------------------
</script>
@section Scripts {
    <script type="text/javascript">
        $(function() {
            // 設定表格高度
            function setDivHeight(type) {
                var div = $('#DivSelectData');
                var currentHeight = div.height();
                var newHeight = type === '+' ? currentHeight + 100 : currentHeight - 100;
                div.height(newHeight);
                $('#DivHeight').val(newHeight);
            }

            // 自動帶入點數
            function sweetAutoImport() {
                var cash = $('#sweetAutoCash').val();
                if (!cash) {
                    alert('請輸入點數');
                    return;
                }
                $('input[name="Cash"]').val(cash);
            }

            // 檢查資訊
            function checkInfos() {
                var hasError = false;
                $('input[name="Cash"]').each(function() {
                    if (!$(this).val()) {
                        hasError = true;
                        return false;
                    }
                });

                if (hasError) {
                    alert('請填寫所有點數');
                    return;
                }

                $('#myButton').button('loading');
                form2.submit();
            }

            // 新增項目
            function ADDItem(USER_NO, CLASS_SEAT) {
                var PersonType = $('#PersonType' + USER_NO).val();
                var TABLType = $('#TABLType' + USER_NO).val();
                var Cash = $('#Cash' + USER_NO).val();

                $.ajax({
                    url: '@Url.Action("ADDItem", "ADDI09")',
                    type: 'POST',
                    data: {
                        USER_NO: USER_NO,
                        CLASS_SEAT: CLASS_SEAT,
                        PersonType: PersonType,
                        TABLType: TABLType,
                        Cash: Cash
                    },
                    success: function(response) {
                        if (response.success) {
                            // 成功處理
                        } else {
                            alert(response.message || '操作失敗');
                        }
                    },
                    error: function(xhr, err) {
                        console.error('Ajax error:', err);
                        alert('操作失敗，請稍後再試');
                    }
                });
            }

            // 變更點數
            function changeCash(event, USER_NO, CRE_PERSON) {
                var cash = $(event.target).val();
                if (!cash) return;

                $.ajax({
                    url: '@Url.Action("ChangeCash", "ADDI09")',
                    type: 'POST',
                    data: {
                        USER_NO: USER_NO,
                        Cash: cash,
                        CRE_PERSON: CRE_PERSON
                    },
                    success: function(response) {
                        if (response.success) {
                            // 成功處理
                        } else {
                            alert(response.message || '操作失敗');
                        }
                    },
                    error: function(xhr, err) {
                        console.error('Ajax error:', err);
                        alert('操作失敗，請稍後再試');
                    }
                });
            }

            // 按鈕連結
            function onBtnLink(USER_NO, CLASS_SEAT) {
                window.location.href = '@Url.Action("EditOne", "ADDI09")?USER_NO=' + USER_NO + '&CLASS_SEAT=' + CLASS_SEAT;
            }

            // 綁定事件
            $('#DivHeight_plus').on('click', function() { setDivHeight('+'); });
            $('#DivHeight_minus').on('click', function() { setDivHeight('-'); });
            $('#myButton').on('click', function() { checkInfos(); });
        });
    </script>
}