$(document).ready(function () {
    const detailModule = {

        init: function () {
            this.bindEvents();
            this.setupGlobalFunctions();
            this.handleImages();
        },
        bindEvents: function () {

            // 綁定導航按鈕事件
            $('a[onclick*="onGo"]').on('click', this.handleNavigation.bind(this));

            // 綁定表單提交事件
            $('form[name="form1"]').on('submit', this.handleFormSubmit.bind(this));
        },

        handleFormSubmit: function(event) {
            try {
                console.log('表單提交');
                return true;
            } catch (error) {
                console.error('表單提交時發生錯誤:', error);
                event.preventDefault();
                ADDI05Common.showMessage('表單提交時發生錯誤，請稍後再試', 'error');
                return false;
            }

        },
        setupGlobalFunctions: function () {
            // 設置全局函數以保持向後兼容
            window.onGo = this.onGo.bind(this);
        },
        handleNavigation: function(event) {
            event.preventDefault();

            try {
                const $button = $(event.target);
                const onclick = $button.attr('onclick');

                if (onclick) {
                    // 解析onclick中的參數
                    const match = onclick.match(/onGo\('([^']*)'\)/);
                    if (match) {
                        const action = match[1];
                        this.onGo(action);
                    }
                }
            } catch (error) {
                console.error('導航處理時發生錯誤:', error);
                ADDI05Common.showMessage('導航時發生錯誤，請稍後再試', 'error');
            }
        },
        onGo: function (actionVal) {
            // 使用共用的onGo函數
            return ADDI05Common.onGo(actionVal);
        },
        handleImages: function () {
            // 使用共用的圖片處理功能
            return ADDI05Common.handleImages('.p-context');
        },
    }
    // 初始化模組
    detailModule.init();

    // 設置全局錯誤處理
    window.addEventListener('error', function (e) {
        console.error('頁面錯誤:', e);
    });
})