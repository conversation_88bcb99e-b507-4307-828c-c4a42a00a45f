﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.Mvc;

namespace ECOOL_APP.com.ecool.Models.entity
{
    public class uZZT34
    {

       [DisplayName("功能代碼")]
        public string BRE_NO { get; set; }

        [DisplayName("動作代碼")]
        public string ACTION_ID { get; set; }

        [DisplayName("動作名稱")]
        public string ACTION_NAME { get; set; }

        //ALL.全部(不卡權限) ,R 依角色限制權限
        [DisplayName("動作類別")]
        public string ACTION_TYPE { get; set; }

         [DisplayName("說明")]
        public string FUN_DESC { get; set; }

          [DisplayName("修改人員")]
        public string CHG_PERSON { get; set; }

          [DisplayName("修改日期")]
        public DateTime CHG_DATE { get; set; }

          [DisplayName("建檔人員")]
        public string CRE_PERSON { get; set; }

        [DisplayName("建檔日期")]
        public DateTime CRE_DATE { get; set; }


        public static Dictionary<string, string> GetAction_TypeItem()
        {
            Dictionary<string, string> Item = new Dictionary<string, string>();

           Item.Add("ALL","全部");
           Item.Add("R","依角色");


           return Item;
        }


        static public string ParserAction_Type_Name(string Action_Type)
        {
  
            Dictionary<string, string> TEMP = GetAction_TypeItem();

            string NAME = TEMP["" + Action_Type + ""].ToString();
                
            return NAME;
        }



        public static List<SelectListItem> Action_TypeSelectListItem(string SelectedVal)
        {
            List<SelectListItem> SelectItem = new List<SelectListItem>();

            Dictionary<string, string> TEMP = GetAction_TypeItem();

            IEnumerable<SelectListItem> mySelectList = new SelectList(TEMP, "key", "value", SelectedVal);

            SelectItem = mySelectList.ToList();

            return SelectItem;

        }





    }




}
