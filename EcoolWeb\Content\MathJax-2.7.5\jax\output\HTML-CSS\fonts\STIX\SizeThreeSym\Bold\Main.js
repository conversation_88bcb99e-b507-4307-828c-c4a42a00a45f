/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/SizeThreeSym/Bold/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXSizeThreeSym-bold"]={directory:"SizeThreeSym/Bold",family:"STIXSizeThreeSym",weight:"bold",32:[0,0,250,0,0],40:[2104,355,750,163,686],41:[2104,355,750,64,587],47:[2104,355,1102,11,1091],91:[2104,355,538,236,540],92:[2104,355,1102,11,1091],93:[2104,355,538,-2,302],123:[2104,355,906,124,736],125:[2104,355,906,170,782],160:[0,0,250,0,0],8730:[2604,471,1076,104,1139],8968:[2104,355,595,236,599],8969:[2104,355,595,-4,359],8970:[2104,355,595,236,599],8971:[2104,355,595,-4,359],10216:[2104,355,765,108,720],10217:[2104,355,765,45,657]};MathJax.OutputJax["HTML-CSS"].initFont("STIXSizeThreeSym-bold");MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/SizeThreeSym/Bold/Main.js");
