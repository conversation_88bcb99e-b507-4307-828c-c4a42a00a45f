﻿@using ECOOL_APP.com.ecool.Models.entity;
@model IEnumerable<ECOOL_APP.EF.ADDT06>

@{
    ViewBag.Title = ViewBag.UserName + "-閱讀護照明細";
    int i = 0;
}

@{
    List<uADDT04Q02> liADDT04Q02 = (List<uADDT04Q02>)ViewBag.ListA04Q2;

    <div class="Div-EZ-reader">
        <div class="form-horizontal">
            <label class="control-label-D" style="font-size:0.6cm;">* 學習匯總</label>
            <br />
            <div class="row">
                <div class="col-md-12 col-sm-12 dl-horizontal-EZ">
                    <samp class="dt" style="font-size:0.6cm;">
                        學校名稱
                    </samp>
                    <samp class="dd" style="font-size:0.5cm;">
                        @ViewBag.SchoolName
                    </samp>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12 col-sm-12 dl-horizontal-EZ">
                    <samp class="dt" style="font-size:0.6cm;">
                        學生名稱
                    </samp>
                    <samp class="dd" style="font-size:0.5cm;">
                        @ViewBag.UserName
                    </samp>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12 col-sm-12 dl-horizontal-EZ">
                    <samp class="dt" style="font-size:0.6cm;">
                        學校期間
                    </samp>
                    <samp class="dd" style="font-size:0.5cm;">
                        @ViewBag.SYear
                    </samp>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12 col-sm-12 dl-horizontal-EZ">
                    <samp class="dt" style="font-size:0.6cm;">
                        總獲得酷幣點數
                    </samp>
                    <samp class="dd" style="font-size:0.5cm;">
                        @ViewBag.CoolCash
                    </samp>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12 col-sm-12 dl-horizontal-EZ">
                    <samp class="dt" style="font-size:0.6cm;">
                        線上投稿總篇數
                    </samp>
                    <samp class="dd" style="font-size:0.5cm;">
                        @ViewBag.SumAcount
                    </samp>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12 col-sm-12 dl-horizontal-EZ">
                    <samp class="dt" style="font-size:0.6cm;">
                        閱讀認證等級
                    </samp>
                    <samp class="dd" style="font-size:0.5cm;">
                        @ViewBag.PassPort
                    </samp>
                </div>
            </div>
        </div>
    </div>
    <div style="height:50px"></div>
    <div class="Div-EZ-reader">
        <div class="form-horizontal">
            <label class="control-label-D">* 閱讀護照等級</label>
            <br />
            <table class="table-ecool table-92Per table-hover">
                <thead>
                    <tr>
                        <td><div style="font-size:0.6cm;">班級</div></td>
                        <td><div style="font-size:0.6cm;">姓名</div></td>
                        <td><div style="font-size:0.6cm;">一年級<br>認證</div></td>
                        <td><div style="font-size:0.6cm;">二年級<br>認證</div></td>
                        <td><div style="font-size:0.6cm;">三年級<br>認證</div></td>
                        <td><div style="font-size:0.6cm;">四年級<br>認證</div></td>
                        <td><div style="font-size:0.6cm;">五年級<br>認證</div></td>
                        <td><div style="font-size:0.6cm;">六年級<br>認證</div></td>
                        <td><div style="font-size:0.6cm;">全部認證<br>通過</div></td>
                        <td><div style="font-size:0.6cm;">備註</div></td>
                    </tr>
                </thead>

                <tbody>
                    @if (liADDT04Q02 != null)
                    {
                        foreach (var item in liADDT04Q02)
                        {

                            <tr style="text-align:center">
                                <td><div style="font-size:0.5cm;">@item.CLASS_NO</div></td>
                                <td><div style="font-size:0.5cm;">@item.SONAME</div></td>
                                <td> <div style="font-size:0.5cm;">@item.UserStatus_NO01</div></td>
                                <td> <div style="font-size:0.5cm;">@item.UserStatus_NO02</div></td>
                                <td><div style="font-size:0.5cm;"> @item.UserStatus_NO03 </div></td>
                                <td><div style="font-size:0.5cm;">  @item.UserStatus_NO04 </div></td>
                                <td><div style="font-size:0.5cm;">  @item.UserStatus_NO05 </div></td>
                                <td><div style="font-size:0.5cm;"> @item.UserStatus_NO06 </div></td>
                                <td><div style="font-size:0.5cm;"> @item.FinishStatus </div></td>
                                <td><div style="font-size:0.5cm;"></div></td>
                            </tr>

                        }

                    }
                </tbody>
            </table>
        </div>
    </div>
    <div style="height:50px"></div>

}
@foreach (var item in Model)
{
    <p style="page-break-before:always"></p>
    List<Image_File> ImageModel = (List<Image_File>)ViewBag.ImageUrl;
    <div class="PageNext"></div>
    <div style="height:15px"></div>
    <div class="Div-EZ-reader">
        <div class="Details">
            <div class="row">
                <div class="col-md-5 col-sm-5 dl-horizontal-EZ">
                    <samp class="dt" style="font-size:0.6cm;">
                        申請日
                    </samp>
                    <samp class="dd" style="font-size:0.5cm;">
                        @Html.DisplayFor(model => item.CRE_DATE, "ShortDateTime")
                    </samp>
                </div>
                <div class="col-md-3 col-sm-3  dl-horizontal-EZ ">
                    <samp class="dt" style="font-size:0.6cm;">
                        班級
                    </samp>
                    <samp class="dd" style="font-size:0.5cm;">
                        @Html.DisplayFor(model => item.CLASS_NO)
                    </samp>
                </div>
                <div class="col-md-4 col-sm-4 dl-horizontal-EZ">
                    <samp class="dt" style="font-size:0.6cm;">
                        姓名
                    </samp>
                    <samp class="dd" style="font-size:0.5cm;">
                        @Html.DisplayFor(model => item.SNAME)
                    </samp>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12 col-sm-12  dl-horizontal-EZ">
                    <samp class="dt" style="font-size:0.6cm;">
                        書名
                    </samp>
                    <samp class="dd" style="font-size:0.5cm;">
                        @Html.DisplayFor(model => item.BOOK_NAME)
                    </samp>
                </div>
            </div>
            <div style="height:15px"></div>
            <div class="row">
                @if (ImageModel[i].APPLY_NO == item.APPLY_NO && ImageModel[i].ImageUrl != null)
                {
                    if (string.IsNullOrWhiteSpace(item.REVIEW))
                    {
                        <div class="col-md-12 p-context">
                            <img src="@ImageModel[i].ImageUrl" id="imgArticle" class="img-responsive " style="float:right;margin:5px;" href="@ImageModel[i].ImageUrl" />
                        </div>
                    }
                    else
                    {
                        <div class="col-md-12 p-context" style="font-size:0.7cm;">
                            <img src="@ImageModel[i].ImageUrl" id="imgArticle" class="img-responsive " style="float:right;margin:10px;max-height:250px;width:auto" href="@ImageModel[i].ImageUrl" />
                            @if (ViewBag.ShowOriginalArticle == "V")
                            {

                                @Html.Raw(HttpUtility.HtmlDecode(item.REVIEW_VERIFY.Replace("\r\n", "<br />")))
                            }
                            else
                            {
                                @Html.Raw(HttpUtility.HtmlDecode(item.REVIEW.Replace("\r\n", "<br />")))
                            }
                        </div>
                    }
                }
            </div>
            @if (string.IsNullOrWhiteSpace(item.VERIFY_COMMENT) == false)
            {
                <hr />
                <div class="col-md-12 col-sm-12" style="padding-bottom:20px">
                    <table>
                        <tr>
                            <td width="120px">教師評語：</td>
                            <td width="400px" colspan="2" style="white-space: pre-line">@Html.Raw(HttpUtility.HtmlDecode(item.VERIFY_COMMENT))</td>
                        </tr>
                    </table>
                </div>
            }
        </div>
    </div>
    <div style="height:50px"></div>
    i++;
}