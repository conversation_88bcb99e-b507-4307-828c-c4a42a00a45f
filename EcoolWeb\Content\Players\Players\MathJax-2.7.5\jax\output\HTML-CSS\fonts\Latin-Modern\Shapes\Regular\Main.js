/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Latin-Modern/Shapes/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.LatinModernMathJax_Shapes={directory:"Shapes/Regular",family:"LatinModernMathJax_Shapes",testString:"\u00A0\u2422\u2423\u2500\u2502\u251C\u2524\u252C\u2534\u253C\u2581\u2588\u2591\u2592\u2593",32:[0,0,332,0,0],160:[0,0,332,0,0],9250:[694,11,556,28,521],9251:[249,105,500,42,458],9472:[270,-230,666,-20,686],9474:[770,270,666,313,353],9500:[770,-230,666,313,686],9508:[270,270,666,-20,353],9516:[270,270,666,313,686],9524:[770,-230,666,-20,353],9532:[770,270,666,-20,686],9601:[83,0,664,0,664],9608:[664,0,664,0,664],9617:[664,0,664,0,664],9618:[664,0,664,0,664],9619:[664,0,664,0,664],9642:[358,-142,328,56,272],9643:[358,-142,328,56,272],9644:[417,-84,778,56,722],9645:[417,-84,778,56,722],9655:[678,178,858,56,802],9665:[678,178,858,56,802],9675:[592,92,796,56,740],9679:[592,92,796,56,740],9702:[445,-55,500,55,445],9828:[727,130,778,56,722],9829:[716,33,778,56,722],9830:[727,163,778,56,722],9831:[727,130,778,28,750],9834:[695,29,611,55,556],9901:[467,-36,500,-78,577],9902:[606,104,500,-189,688],11012:[520,20,1062,56,1006],11013:[468,-31,977,56,921],11014:[672,193,612,87,524],11015:[693,172,612,87,524],11020:[468,-31,1022,89,933],11021:[672,172,549,56,492],11034:[640,240,960,40,920],11057:[990,490,997,56,941],11059:[510,10,1463,56,1407]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"LatinModernMathJax_Shapes"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Shapes/Regular/Main.js"]);
