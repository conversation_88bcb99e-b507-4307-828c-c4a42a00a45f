﻿using ECOOL_APP.com.ecool.Models.entity;
using ECOOL_APP.EF;
using MvcPaging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace EcoolWeb.ViewModels
{
    public class AWA002Query2ViewModel
    {
        /// <summary>
        /// 搜尋字串
        /// </summary>
        public string whereKeyword { get; set; }

        /// <summary>
        /// 只顯示某一位學生的文章
        /// </summary>
        public string whereUserNo { get; set; }

        /// <summary>
        /// 只顯示某一位學生的姓名
        /// </summary>
        public string whereName { get; set; }

        /// <summary>
        /// 只顯示某班級
        /// </summary>
        public string whereCLASS_NO { get; set; }

        /// <summary>
        /// 只顯示某年級
        /// </summary>
        public string whereGrade { get; set; }
        public string whereSOURCETABLE { get; set; }
        public string whereLOG_TIME_S { get; set; }

        public string whereLOG_TIME_E { get; set; }

        public bool whereIsExcel { get; set; }
        public string whereFromType { get; set; }
        public bool WhereIsPassbook { get; set; }
        public bool whereIsPrint { get; set; }
        public string[] whereArrCHART_DESC { get; set; }

        /// <summary>
        /// 排序欄位
        /// </summary>
        public string OrdercColumn { get; set; }

        public int Page { get; set; }

        /// <summary>
        /// 查詢結果
        /// </summary>
        public IPagedList<uAWAT01_Detail_LOG> VAWAT01_LOG;

        public AWA002Query2ViewModel()
        {
            Page = 0;
            OrdercColumn = "LOG_TIME";
        }
    }
}