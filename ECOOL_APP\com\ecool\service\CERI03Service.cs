﻿using AutoMapper;
using ECOOL_APP.com.ecool.ResultService;
using ECOOL_APP.com.ecool.util;
using ECOOL_APP.EF;
using EntityFramework.Extensions;
using EntityFramework.Utilities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Transactions;

namespace ECOOL_APP.com.ecool.service
{
    public class CERI03Service
    {
        #region 取得編輯資料

        public CERI03EditViewModel GetEditData(CERI03EditViewModel model, ref ECOOL_DEVEntities db)
        {
            using (var txn = Queries.GetNewReadUncommittedScope())
            {
                var temp = db.CERT03.Where(a => a.REQUIRED_ID == model.Keyword).FirstOrDefault();
                model.Main = Mapper.Map<CERI03EditMainViewModel>(temp);

                if (temp != null)
                {
                    var tempG = db.CERT03_G.Where(a => a.REQUIRED_ID == model.Keyword).ToList();

                    if (tempG != null)
                    {
                        if (model.Main.REQUIRED_TYPE == (byte)CERT03.RequiredTypeVal.GRADE)
                        {
                            model.GRADEs = tempG.Select(a => (byte)a.GRADE).ToList();
                        }
                        else
                        {
                            model.GRADE_SEMESTERs = tempG.Select(a => $"{(byte)a.GRADE}_{(byte)a.SEMESTER}").ToList();
                        }
                    }
                }
            }

            return model;
        }

        #endregion 取得編輯資料

        #region Save處理

        public IResult SaveEditData(CERI03EditViewModel model, string SCHOOL_NO, UserProfile user, ref ECOOL_DEVEntities db)
        {
            IResult result = new Result(false);

            try
            {
                using (TransactionScope tx = new TransactionScope())
                {
                    //主檔
                    CERT03 SaveUp = null;
                    SaveUp = db.CERT03.Where(a => a.REQUIRED_ID == model.Main.REQUIRED_ID).FirstOrDefault();

                    if (SaveUp == null)
                    {
                        SaveUp = db.CERT03.Where(a => a.ACCREDITATION_ID == model.ACCREDITATION_ID).OrderByDescending(a => a.CHG_DATE).FirstOrDefault();

                        if (SaveUp != null)
                        {
                            var cERT03s = db.CERT03.Where(a => a.ACCREDITATION_ID == model.ACCREDITATION_ID && a.REQUIRED_ID != SaveUp.REQUIRED_ID).Select(a => a.REQUIRED_ID).ToList();

                            if (cERT03s?.Count() > 0)
                            {
                                db.CERT03_G.Where(a => cERT03s.Contains(a.REQUIRED_ID)).Delete();
                                db.CERT03.Where(a => cERT03s.Contains(a.REQUIRED_ID)).Delete();
                            }
                        }
                    }

                    if (SaveUp == null)
                    {
                        SaveUp = new CERT03();

                        SaveUp = Mapper.Map<CERT03>(model.Main);

                        SaveUp.REQUIRED_ID = Guid.NewGuid().ToString("N");
                        SaveUp.CHG_PERSON = user?.USER_KEY;
                        SaveUp.CHG_DATE = DateTime.Now;

                        db.CERT03.Add(SaveUp);
                    }
                    else
                    {
                        // 只更新ViewModel的部分到Entity
                        Mapper.Map(model.Main, SaveUp);

                        SaveUp.CHG_PERSON = user?.USER_KEY;
                        SaveUp.CHG_DATE = DateTime.Now;

                        db.Entry(SaveUp).State = System.Data.Entity.EntityState.Modified;

                        db.CERT03_G.Where(a => a.REQUIRED_ID == SaveUp.REQUIRED_ID).Delete();
                    }

                    var ItemNum = 1;

                    List<CERT03_G> cERT03_Gs = new List<CERT03_G>();
                    StringHelper stringHelper = new StringHelper();

                    if (SaveUp.REQUIRED_TYPE == (byte)CERT03.RequiredTypeVal.GRADE)
                    {
                        if (model.GRADEs?.Count() > 0)
                        {
                            foreach (var item in model.GRADEs.Where(a => a != 0))
                            {
                                CERT03_G CreD = new CERT03_G();

                                CreD.REQUIRED_ID = SaveUp.REQUIRED_ID;
                                CreD.ITEM_NO = ItemNum;
                                CreD.SCHOOL_NO = SCHOOL_NO;
                                CreD.GRADE = item;
                                CreD.SEMESTER = null;
                                CreD.CHG_PERSON = user?.USER_KEY;
                                CreD.CHG_DATE = DateTime.Now;
                                cERT03_Gs.Add(CreD);

                                ItemNum++;
                            }
                        }
                    }
                    else if (SaveUp.REQUIRED_TYPE == (byte)CERT03.RequiredTypeVal.GRADE_SEMESTER)
                    {
                        if (model.GRADE_SEMESTERs?.Count() > 0)
                        {
                            foreach (var item in model.GRADE_SEMESTERs.Where(a => a != string.Empty))
                            {
                                CERT03_G CreD = new CERT03_G();

                                CreD.REQUIRED_ID = SaveUp.REQUIRED_ID;
                                CreD.ITEM_NO = ItemNum;
                                CreD.SCHOOL_NO = SCHOOL_NO;
                                CreD.GRADE = Convert.ToByte(stringHelper.StrLeft(item, 1));
                                CreD.SEMESTER = Convert.ToByte(stringHelper.StrRigth(item, 1));
                                CreD.CHG_PERSON = user?.USER_KEY;
                                CreD.CHG_DATE = DateTime.Now;
                                cERT03_Gs.Add(CreD);

                                ItemNum++;
                            }
                        }
                    }

                    if (cERT03_Gs?.Count > 0)
                    {
                        EFBatchOperation.For(db, db.CERT03_G).InsertAll(cERT03_Gs);
                    }

                    try
                    {
                        db.SaveChanges();
                    }
                    catch (Exception ex)
                    {
                        result.Exception = ex;
                        result.Message += ex.Message;
                        return result;
                    }

                    tx.Complete();
                }

                result.Success = true;
            }
            catch (Exception ex)
            {
                result.Exception = ex;
                result.Message += ex.Message;
                return result;
            }

            return result;
        }

        #endregion Save處理

        #region 作廢處理

        public IResult DelDate(string ACCREDITATION_ID, ref ECOOL_DEVEntities db)
        {
            IResult result = new Result(false);

            try
            {
                using (TransactionScope tx = new TransactionScope())
                {
                    var cERT03s = db.CERT03.Where(a => a.ACCREDITATION_ID == ACCREDITATION_ID).Select(a => a.REQUIRED_ID).ToList();

                    if (cERT03s.Count() > 0)
                    {
                        db.CERT03_G.Where(a => cERT03s.Contains(a.REQUIRED_ID)).Delete();
                        db.CERT03.Where(a => cERT03s.Contains(a.REQUIRED_ID)).Delete();

                        try
                        {
                            db.SaveChanges();
                        }
                        catch (Exception ex)
                        {
                            result.Exception = ex;
                            result.Message += ex.Message;
                            return result;
                        }
                    }

                    tx.Complete();
                }

                result.Success = true;
            }
            catch (Exception ex)
            {
                result.Exception = ex;
                result.Message += ex.Message;
            }

            return result;
        }

        #endregion 作廢處理
    }
}