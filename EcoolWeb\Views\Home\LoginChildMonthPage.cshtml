﻿<!DOCTYPE html>
<html lang="zh-Hant-TW">
<head>
    @{
        ViewBag.Title = "登入";
        Layout = "~/Views/Shared/_LayoutEmpty.cshtml";

        ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

        ECOOL_DEVEntities db = new ECOOL_DEVEntities();
        List<BDMT01> SchoolList = db.BDMT01.OrderBy(school => school.SHORT_NAME).ToList();

    }

    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    @Styles.Render("~/Content/css")
    <link href="@Url.Content("~/Content/css/EzCss.css")?@DateTime.Now.ToString("yyyy_MM_dd_H_mm_ss")" rel="stylesheet" />
    <link href="@Url.Content("~/Content/font-awesome/css/font-awesome.min.css")" rel="stylesheet" />
    <style>
        body{
            background-image:url('@Url.Content("~/Content/img/web-01.png")');
            background-repeat:repeat;
        }
    </style>
    @RenderSection("css", required: false)
</head>
<body>
    <div class="visible-xs">
        <!---手機 -->
        <nav class="navbar navbar-phone navbar-fixed-top" role="navigation">
            <span class="btn-logo">
                <a href='@Url.Action("ChildMonthIndex", "Home")' class="btn-logo-layout">
                    <img src="~/Content/img/childrensMonthLogo.jpg" class="img-responsive " alt="Responsive image" title="回首頁" />
                </a>
            </span>
            <a role="button" class="btn-User" href="@Url.Action("LoginChildMonthPage","Home")">
                <i class="fa fa-user" style="font-size:2.5em" title="會員登入"></i>
            </a>
        </nav>
        <div style="height:50px"></div>
    </div>

    <div>
        <div class="text-center">
            <div class="visible-xs">
                <!---手機 -->
                <img src='@Url.Content("~/Content/img/childrensMonth-banner-PC.png")' class="img-responsive " alt="Responsive image" />
            </div>
            <div class="hidden-xs">
                <a href='@Url.Action("ChildMonthIndex", "Home")'>
                    <img src='@Url.Content("~/Content/img/childrensMonth-banner-PC.png")' title="回首頁" />
                </a>
            </div>
        </div>
        <br>
        <div class="containerEZ">
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    @Html.Partial("../Shared/_LoginPhone")
                </div>
            </div>
        </div>
    </div>
    @Scripts.Render("~/bundles/modernizr")
    @Scripts.Render("~/bundles/jquery")
    @Scripts.Render("~/bundles/bootstrap")
    @RenderSection("scripts", required: false)
</body>
</html>