﻿using ECOOL_APP.EF;
using MvcPaging;
using System.Collections.Generic;

namespace ECOOL_APP.com.ecool.Models.DTO
{
    public class ADDI09ListViewViewModel
    {
        /// <summary>
        /// 結果清單
        /// </summary>
        public IPagedList<ADDT20_Extension> ADDT20List { get; set; }
        public List<ADDT20_Extension> ADDT20PrintList { get; set; }
        public ADDI09SearchViewModel Search { get; set; }
        public bool WhereIsPassbook { get; set; }
        public ADDI09ListViewColNameViewModel ColName { get; set; }
    }

    public class ADDI09ListViewColNameViewModel
    {

        public string EN_CRE_DATE { get; set; }

        public string EN_CRE_PERSON { get; set; }

        public string EN_NAME { get; set; }

        public string EN_SUBJECT { get; set; }

        public string CI_SUBJECT { get; set; }

    }
}
