/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX-Web/Marks/Bold/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXMathJax_Marks-bold"]={directory:"Marks/Bold",family:"STIXMathJax_Marks",weight:"bold",testString:"\u00A0\u02B0\u02B1\u02B2\u02B3\u02B4\u02B5\u02B6\u02B7\u02B8\u02B9\u02BA\u02BB\u02BC\u02BD",32:[0,0,250,0,0],160:[0,0,250,0,0],688:[842,-335,378,6,365],689:[848,-336,378,7,365],690:[868,-179,300,25,273],691:[699,-335,270,12,266],692:[690,-326,292,10,264],693:[690,-163,319,10,342],694:[684,-345,404,20,397],695:[681,-331,550,23,528],696:[690,-179,380,16,374],697:[684,-421,208,30,216],698:[684,-421,356,19,364],699:[685,-350,333,39,223],700:[686,-351,333,39,223],701:[686,-351,250,39,223],702:[662,-382,334,65,250],703:[662,-382,334,65,250],704:[690,-240,353,30,333],705:[690,-240,353,30,333],706:[760,-414,317,30,297],707:[760,-414,317,30,297],708:[720,-453,317,-14,332],709:[720,-453,317,-14,332],712:[720,-455,279,112,167],716:[70,195,278,112,167],717:[-88,160,370,20,350],718:[-7,192,333,15,253],719:[-7,192,333,80,318],720:[474,-4,333,79,254],721:[474,-294,333,79,254],722:[378,-62,333,65,268],723:[378,-62,333,65,268],724:[206,-4,333,51,281],725:[206,-4,333,51,281],726:[227,-9,334,61,273],727:[150,-84,334,61,273],731:[44,173,333,90,319],733:[713,-528,333,-13,425],734:[481,-186,292,0,302],735:[744,-506,260,10,250],736:[684,-190,420,10,410],737:[842,-335,190,5,186],738:[695,-320,300,19,278],739:[690,-335,380,12,376],740:[855,-335,328,20,323],741:[676,0,405,40,368],742:[676,0,405,40,368],743:[676,0,405,40,368],744:[676,0,405,40,368],745:[676,0,405,40,368],748:[70,167,314,5,309],749:[720,-528,395,5,390],759:[-108,235,333,-16,349],773:[838,-788,0,-500,0],777:[751,-491,0,-336,-131],781:[730,-530,0,-277,-211],782:[730,-530,0,-358,-142],783:[713,-528,0,-469,-31],784:[828,-528,0,-401,-98],785:[691,-528,0,-401,-98],786:[867,-532,0,-342,-158],787:[867,-532,0,-342,-158],788:[867,-532,0,-342,-158],789:[867,-532,0,-116,68],790:[-70,255,0,-369,-131],791:[-70,255,0,-369,-131],792:[-58,288,0,-425,-223],793:[-58,288,0,-288,-86],794:[752,-531,0,-410,-93],795:[505,-352,0,-62,66],796:[-33,313,0,-375,-190],797:[-70,272,0,-365,-135],798:[-70,272,0,-365,-135],799:[-70,287,0,-356,-144],800:[-140,206,0,-356,-144],801:[75,287,0,-241,-22],802:[75,287,0,-94,125],803:[-109,238,0,-314,-185],804:[-109,238,0,-419,-80],805:[-66,279,0,-356,-143],806:[-88,423,0,-342,-158],807:[0,218,0,-363,-137],808:[44,173,0,-364,-135],809:[-107,239,0,-277,-222],810:[-86,260,0,-425,-93],811:[-104,242,0,-420,-95],812:[-83,259,0,-418,-81],813:[-85,261,0,-418,-81],814:[-78,241,0,-401,-98],815:[-78,241,0,-401,-98],816:[-108,235,0,-432,-67],817:[-137,209,0,-415,-85],818:[-137,187,0,-500,0],819:[-137,287,0,-500,0],820:[316,-189,0,-432,-67],821:[282,-224,0,-414,-108],822:[282,-224,0,-510,-10],823:[580,74,0,-410,-43],825:[-33,313,0,-375,-190],826:[-71,245,0,-425,-93],827:[-70,264,0,-353,-167],828:[-89,234,0,-410,-109],829:[719,-520,0,-350,-150],830:[881,-516,0,-314,-187],831:[938,-788,0,-500,0],838:[717,-544,0,-410,-107],839:[-137,322,0,0,330],844:[837,-547,0,-446,-81],857:[-66,368,0,-359,-89],860:[-79,242,0,-401,300],864:[674,-529,0,-432,398],865:[691,-534,0,-403,265],866:[-54,293,0,-432,377],8208:[287,-171,333,44,287],8209:[287,-171,333,44,287],8210:[287,-171,500,0,500],8213:[271,-181,2000,0,2000],8215:[-137,287,520,10,510],8218:[155,180,333,79,263],8219:[691,-356,333,79,263],8222:[155,180,500,14,468],8223:[691,-356,500,14,468],8226:[462,-42,560,70,490],8229:[156,13,666,82,584],8240:[706,29,1110,61,1049],8241:[706,29,1472,61,1411],8246:[713,-438,467,75,392],8247:[713,-438,625,75,550],8248:[117,170,584,91,497],8249:[415,-36,333,51,305],8250:[415,-36,333,28,282],8252:[691,13,625,81,544],8256:[725,-508,798,79,733],8263:[689,13,947,57,892],8270:[236,200,500,56,448],8271:[472,180,333,67,251],8273:[706,200,500,56,448],8400:[846,-637,0,-470,14],8401:[846,-637,0,-470,14],8402:[662,156,0,-298,-223],8406:[846,-508,0,-500,-16],8411:[666,-537,0,-512,37],8412:[666,-537,0,-627,132],8413:[760,254,0,-753,256],8417:[846,-508,0,-515,79],8420:[1055,169,0,-998,519],8421:[662,155,0,-470,12],8422:[662,156,0,-390,-111],8423:[760,172,0,-643,200],8424:[-109,238,0,-512,37],8425:[717,-544,0,-510,54],8426:[441,-65,0,-688,148],8427:[775,235,0,-505,208],8428:[-166,375,0,-470,14],8429:[-166,375,0,-470,14],8430:[-35,373,0,-490,-6],8431:[-35,373,0,-470,14],8432:[845,-543,0,-385,-115],57438:[762,-565,0,95,425],57441:[-137,437,0,0,330],57442:[-137,552,0,0,330],57444:[837,-565,333,-16,349],57445:[-137,409,0,-16,349],57446:[801,-565,0,91,430],57447:[-137,409,0,-16,349],57560:[688,13,400,57,343],57561:[663,0,314,54,260],57562:[663,0,425,54,371],57997:[734,-484,0,92,498]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"STIXMathJax_Marks-bold"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Marks/Bold/Main.js"]);
