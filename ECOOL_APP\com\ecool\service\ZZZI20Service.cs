﻿using Dapper;
using ECOOL_APP.com.ecool.LogicCenter;
using ECOOL_APP.EF;
using log4net;
using MvcPaging;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Entity;
using System.Data.SqlClient;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Transactions;
using System.Web;

namespace ECOOL_APP.com.ecool.service
{
    public class ZZZI20Service
    {


        public bool insertATMCash(string SchoolNO,string USER_NO, ref string Message, ref List<Tuple<string, string, int>> valuesList) {
            ECOOL_DEVEntities db = new ECOOL_DEVEntities();

            string sSQL = $@"SELECT * FROM AWAT10 A WHERE A.SCHOOL_NO='{SchoolNO}' and A.USER_NO='{USER_NO}' and A.STATUS='{AWAT10.StatusVal.SetUp}' and A.PERIOD_DATEE>getdate()";
            var T10List = db.Database.Connection.Query<AWAT10>(sSQL).ToList();

            if (db.Database.Connection.State != ConnectionState.Open) db.Database.Connection.Open();

            using (TransactionScope tx = new TransactionScope())
            {

                foreach (var item in T10List)
                {

                    //if (item.MATURITY_TYPE == AWAT10.MATURITY_TYPE_Val.Close) //到期解約
                    //{
                        var Hr = db.HRMT01.Where(a => a.SCHOOL_NO == item.SCHOOL_NO && a.USER_NO == item.USER_NO).FirstOrDefault();

                        if (Hr?.USER_TYPE == UserType.Teacher)
                        {
                            ECOOL_APP.CashHelper.TeachAddCash(null, Convert.ToInt32(item.AMT + item.INTEREST_AMT), item.SCHOOL_NO, item.USER_NO, "AWAT10", item.A_NO.ToString(), "銀行定存到期解約", false, null, ref db);
                        }
                        else
                        {
                            ECOOL_APP.CashHelper.AddCash(null, Convert.ToInt32(item.AMT + item.INTEREST_AMT), item.SCHOOL_NO, item.USER_NO, "AWAT10", item.A_NO.ToString(), "銀行定存到期解約", false, ref db, "", "", ref valuesList);
                        }

                        item.STATUS = AWAT10.StatusVal.Expire;
                        db.Entry(item).State = System.Data.Entity.EntityState.Modified;
                    //}
                }
                try
                {
                    db.SaveChanges();
                }
                catch (Exception ex)
                {
                    Message=ex.Message;
                    return false;
                }
                tx.Complete();
                return true;

            }



        }
        public bool TurnIn(string IDNO,string SchoolNO,  ref string Message, ref List<Tuple<string, string, int>> valuesList)

        {

            bool temp = true;
            if (string.IsNullOrWhiteSpace(IDNO)) return false;

          

            ECOOL_DEVEntities db = new ECOOL_DEVEntities();
            var History = db.HRMV01.Where(a => a.IDNO == IDNO);
            HRMV01 HrV01 = new HRMV01();
            int HRMT01Int32 = 0;
            HrV01 = db.HRMV01.Where(a => a.IDNO == IDNO && a.USER_STATUS != UserStaus.Enabled).FirstOrDefault();
            HRMT01Int32 = db.AWAT01_LOG.Where(a => a.SCHOOL_NO == HrV01.SCHOOL_NO && a.USER_NO == HrV01.USER_NO && a.LOG_DESC.Contains("酷幣匯轉區域管理")).Count();
            if (HRMT01Int32 == 0)
            {



                HRMV01 TurnIn = History.Where(a => a.SCHOOL_NO == SchoolNO && a.USER_STATUS != UserStaus.Invalid).OrderByDescending(a => a.GRADE).FirstOrDefault();

                HRMV01 TurnOut = History.Where(a => a.SCHOOL_NO != SchoolNO).OrderByDescending(a => a.GRADE).FirstOrDefault();

                AWAT01 UserCash_Out =
                    db.AWAT01.Where(user => user.SCHOOL_NO == TurnOut.SCHOOL_NO && user.USER_NO == TurnOut.USER_NO).FirstOrDefault();

                int TurnOutCash = 0;
                if (UserCash_Out != null)
                {
                    if (UserCash_Out.CASH_AVAILABLE.HasValue)
                        TurnOutCash = UserCash_Out.CASH_AVAILABLE.Value;
                }

                HRMT01 student = db.HRMT01.Where(a => a.SCHOOL_NO == TurnIn.SCHOOL_NO && a.USER_NO == TurnIn.USER_NO).FirstOrDefault();
                student.USER_STATUS = UserStaus.Enabled;

                string BATCH_ID = PushService.CreBATCH_ID();

                ECOOL_APP.CashHelper.AddCashZZI20( TurnOutCash, TurnIn.SCHOOL_NO, TurnIn.USER_NO, "ZZZI20", TurnIn.USER_NO, "酷幣匯轉區域管理", true, ref db, "", "",ref valuesList);

                string BODY_TXT = "酷幣匯轉區域管理，獲得酷幣點數" + (TurnOutCash).ToString() + "數";

                PushService.InsertPushDataMe(BATCH_ID, TurnIn.SCHOOL_NO, TurnIn.USER_NO, "", BODY_TXT, "", "Home", "ArrivedChanceRun", TurnIn.USER_NO, "", false, ref db);

                try
                {
                    db.SaveChanges();
                    Message += "異動成功";

                }
                catch (Exception ex)
                {
                    Message += "異動失敗, 系統錯誤原因:" + ex.Message;
                    return false;
                }

                //Push
                PushHelper.ToPushServer(BATCH_ID);
                return true;
            }
            else {
                HRMV01 TurnIn = History.Where(a => a.SCHOOL_NO == SchoolNO && a.USER_STATUS != UserStaus.Invalid).OrderByDescending(a => a.GRADE).FirstOrDefault();
                HRMT01 student = db.HRMT01.Where(a => a.SCHOOL_NO == TurnIn.SCHOOL_NO && a.USER_NO == TurnIn.USER_NO).FirstOrDefault();
                student.USER_STATUS = UserStaus.Enabled;

                try
                {
                    db.SaveChanges();
                    Message += "異動成功";

                }
                catch (Exception ex)
                {
                    Message += "異動失敗, 系統錯誤原因:" + ex.Message;
                    return false;
                }
                return true;

            }
        }



        }
}