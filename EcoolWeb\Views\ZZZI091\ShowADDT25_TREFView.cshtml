﻿@model ZZZI09ShowADDT25_TREFViewViewModel

@{
    /**/

    if (Model.Datalist != null && Model.Datalist.Count() != 0)
    {
        if (Model.Datalist.Count() > 0)
        {

            <section class="row px-3 secretMission">
                <div class="col-lg-12 bgPosition">
                    <h2 class="heading-h2">神秘任務</h2>
                    @foreach (var item in Model.Datalist)
                    {
                        <div class="col-12">
                            <h4><b>@item.TASK_DESC</b></h4>
                        </div>
                        <div class="col-12">
                            <h4>答:</h4>
                        </div>
                        <div class="col-12">

                            <p>@item.ANSWERS</p>
                            <div class="row">
                                @foreach (var Name in item.FileName)
                                {
                                    if (!string.IsNullOrWhiteSpace(Name))
                                    {
                                        string ImgPath = item.FilePaths + @"\" + Name;

                                        <div class="col-6">
                                            <img src="@Url.Content(ImgPath)" href="@Url.Content(ImgPath)" class="img-fluid " />
                                        </div>
                                    }
                                }
                            </div>
                        </div>

                    }

                </div>
            </section>
        }
    }}
