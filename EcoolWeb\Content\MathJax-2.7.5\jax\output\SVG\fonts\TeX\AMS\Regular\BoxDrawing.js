/*
 *  /MathJax/jax/output/SVG/fonts/TeX/AMS/Regular/BoxDrawing.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax.SVG.FONTDATA.FONTS.MathJax_AMS,{9484:[694,-306,500,55,444,"76 306Q62 306 59 319T55 386V500V596Q55 664 57 676T68 692Q71 694 250 694Q428 694 432 692Q444 685 444 674Q444 665 432 656Q428 654 261 654H95V487Q95 355 95 336T90 312Q84 306 76 306"],9488:[694,-306,500,55,445,"424 306Q418 306 413 310T406 318L404 321V654H238Q71 654 68 656Q55 662 55 674T68 692Q71 694 250 694H379Q432 694 438 688Q443 683 443 662T444 500T444 338T438 312Q432 306 424 306"],9492:[366,22,500,55,444,"55 172V287Q55 341 58 353T76 366Q88 366 95 351V18H261Q428 18 432 16Q444 9 444 -2Q444 -11 432 -20Q428 -22 250 -22H120Q67 -22 61 -16Q56 -11 56 10T55 172"],9496:[366,22,500,55,444,"404 351Q410 366 424 366Q437 366 440 353T444 288V172V72Q444 8 443 -4T432 -20Q428 -22 250 -22Q71 -22 68 -20Q55 -14 55 -2T68 16Q71 18 238 18H404V351"],9585:[694,195,889,0,860,"19 -195Q13 -195 7 -188T0 -176Q0 -169 18 -151L822 683Q835 694 840 694T852 688T860 674Q860 667 810 614T460 252Q57 -167 44 -179Q27 -195 19 -195"],9586:[694,195,889,0,860,"0 675Q0 681 6 687T19 694Q27 694 44 678L460 247Q759 -62 809 -115T860 -175Q860 -183 852 -189T840 -195Q835 -195 822 -184L18 649Q0 667 0 675"]});MathJax.Ajax.loadComplete(MathJax.OutputJax.SVG.fontDir+"/AMS/Regular/BoxDrawing.js");
