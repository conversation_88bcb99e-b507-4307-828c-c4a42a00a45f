//icon

%iconHover {
  // cursor: pointer;
  &:hover {
    filter: brightness(0.8);
  }
  &:active {
    filter: brightness(1.1);
  }
}

// icon 引入
@mixin icon($iconName, $bgSize) {
  background-image: url(../images/#{$iconName});
  background-size: $bgSize;
}

// 設定 icon 各種尺寸
@mixin iconSize($iconSize) {
  height: $iconSize;
  width: $iconSize;
}

.icon {
  &::before {
    display: inline-block;
    @include iconSize(1em);
  }
  &-size-xs {
    &::before {
      display: inline-block;
      @include iconSize(1.2rem);
      vertical-align: text-bottom;
    }
  }
  &-size-s {
    &::before {
      display: inline-block;
      @include iconSize(1.4rem);
      vertical-align: text-bottom;
    }
  }
  &-size-m {
    &::before {
      display: inline-block;
      @include iconSize(2rem);
      vertical-align: text-bottom;
    }
  }
  &-size-lg {
    &::before {
      display: inline-block;
      @include iconSize(3.5rem);
      vertical-align: text-bottom;
    }
  }
  &-size-xl {
    &::before {
      display: inline-block;
      @include iconSize(5rem);
      vertical-align: text-bottom;
    }
  }
  &-announce {
    &::before {
      content: "";
      @include icon("icon-announce.svg", 100%);
    }
  }
  &-bigMedal {
    &::before {
      content: "";
      @include icon("icon-bigMedal.svg", 100%);
    }
  }
  &-books {
    &::before {
      content: "";
      @include icon("icon-books.svg", 100%);
    }
  }
  &-deposit {
    &::before {
      content: "";
      @include icon("icon-deposit.svg", 100%);
    }
  }
  &-wallet {
    &::before {
      content: "";
      @include icon("icon-wallet.svg", 100%);
    }
  }
  &-ecool {
    &::before {
      content: "";
      @include icon("icon-ecool.png", 100%);
    }
  }
  &-gift {
    &::before {
      content: "";
      @include icon("icon-gift.svg", 100%);
    }
  }
  &-iStory {
    &::before {
      content: "";
      @include icon("icon-iStory.svg", 100%);
    }
  }
  &-medal {
    &::before {
      content: "";
      @include icon("icon-medal.svg", 100%);
    }
  }
 
  &-search {
    &::before {
      content: "";
      @include icon("icon-search.png", 100%);
    }
  }
 
  &-sport {
    &::before {
      content: "";
      @include icon("icon-sport.svg", 100%);
    }
  }
 
  // &-info {
  //   &::before {
  //     content: "";
  //     @include icon("icon-info.svg", 100%);
  //     @extend %iconHover;
  //   }
  // }
  
  
  // &-ban {
  //   height: 1rem;
  //   width: 1rem;
  //   @extend %iconHover;
  //   &::before {
  //     content: "";
  //     @include icon("icon-ban.svg", 100%);
  //   }
  // }
}



