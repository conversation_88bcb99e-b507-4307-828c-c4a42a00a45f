﻿@model EcoolWeb.Models.AWA005QueryViewModel
@{
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
    ViewBag.Title = "酷幣給點紀錄-學生數位存摺";
}
@Html.Partial("_Title_Secondary")
<script type='text/javascript' src='~/Scripts/jquery.simplemodal.js'></script>
<script src="~/Scripts/Blob.js"></script>
<script src="~/Scripts/FileSaver.js"></script>
<script src="~/Scripts/tableexport.js"></script>
<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>
@section scripts{
    <script>

        var targetFormID = '#AWA002';
        $(document).ready(function () {
            $(".colorbox").colorbox({ iframe: true, width: "80%", height: "80%", opacity: 0.82 });
        });
        function FunPageProc(page) {
            if ($(targetFormID).size() > 0) {
                $('#Page').val(page)
                $(targetFormID).submit();
            }
        };

        function doSort(SortCol) {
            $("#OrdercColumn").val(SortCol);
            FunPageProc(1)
        }

        function doSearch(ColName, whereValue) {
            $("#" + ColName).val(whereValue);
            FunPageProc(1)
        }


        function todoClear() {
            $("#doClear").val(true);
            $("#whereKeyword").val('');
            $("#OrdercColumn").val('');
            $("#whereCLASS_NO").val('');
            $("#whereGrade").val('');
            $("#whereSeat_NO").val('');


            @* $("#@Html.IdFor(m=>m.WhereIsMonthTop)").val(false);*@
            FunPageProc(1)
        }
    </script>
}
@using (Html.BeginForm("Query5", "AWA002", FormMethod.Post, new { id = "AWA002" }))
{

    if (user != null)
    {
        if (user.USER_TYPE != UserType.Student && user.USER_TYPE != UserType.Parents)
        {
            @Html.ActionLink("各類酷幣給點統計", "Query", null, new { @class = "btn btn-sm btn-sys" })
            @Html.ActionLink("學生e酷幣給點紀錄", "Query2", null, new { @class = "btn btn-sm btn-sys " })
            @Html.ActionLink("學生數位存摺", "Query5", null, new { @class = "btn btn-sm btn-sys active" })
        }

    }

    <div class="form-inline" role="form">
        <div class="form-group">
            <label class="control-label">學號/姓名/班級/異動說明</label>
        </div>
        <div class="form-group">
            @Html.EditorFor(m => m.whereKeyword, new { htmlAttributes = new { @class = "form-control input-sm" } })
            @Html.HiddenFor(m => m.OrdercColumn)
            @Html.HiddenFor(m => m.Page)
            @Html.Hidden("User_No", Request["User_No"])
        </div>
        <div class="form-group">
            <label class="control-label">年級</label>
        </div>
        <div class="form-group">
            @Html.DropDownListFor(m => m.whereGrade, (IEnumerable<SelectListItem>)ViewBag.GradeItem, new { @class = "form-control input-sm", onchange = "FunPageProc(1)" })
        </div>
        <div class="form-group">
            <label class="control-label">班級</label>
        </div>
        <div class="form-group">
            @Html.DropDownListFor(m => m.whereCLASS_NO, (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control input-sm", onchange = "FunPageProc(1)" })
        </div>
        <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1)" />
        <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />
    </div>
    <table class="table-ecool table-92Per table-hover table-ecool-AWA003">
        <thead>
            <tr>

                <th style="text-align: center;cursor:pointer;" onclick="doSort('USER_NO');">
                    學號
                    <img id="USER_NO" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                </th>
                <th style="text-align: center;cursor:pointer;" onclick="doSort('SNAME');">
                    姓名
                    <img id="SNAME" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                </th>
                <th style="text-align: center;cursor:pointer;" onclick="doSort('CLASS_NO');">
                    班級
                    <img id="CLASS_NO" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                </th>
                <th style="text-align: center;cursor:pointer;" onclick="doSort('SEAT_NO');">
                    座號
                    <img id="SEAT_NO" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                </th>
                <th style="text-align: center;cursor:pointer;"></th>
            </tr>


        </thead>
        <tbody>
            @foreach (var item in Model.VAWA005List)
            {
                <tr>
                    <td align="center">
                        @Html.DisplayFor(modelItem => item.USER_NO)
                    </td>
                    <td align="center">
                        @Html.DisplayFor(modelItem => item.SNAME)
                    </td>
                    <td align="center">
                        @Html.DisplayFor(modelItem => item.CLASS_NO)
                    </td>
                    <td align="center">
                        @Html.DisplayFor(modelItem => item.SEAT_NO)
                    </td>
                    <td align="center">
                        @Html.ActionLink("數位存摺", "Query2", "AWA002", new { USER_NO = @Html.DisplayFor(modelItem => item.USER_NO), WhereIsPassbook = "True" }, new { @class = "btn btn-xs btn-Basic colorbox", title = "數位存摺" })
                        @*@Html.ActionLink(item.NAME, "Query2", "AWA002", new { USER_NO = item.USER_NO, LogDESC = item.LOG_DESC }, new { @class = "btn-table-link" })*@
                    </td>
                </tr>
            }

        </tbody>

    </table>
    <tbody>
        <tr>
            @Html.Pager(Model.VAWA005List.PageSize, Model.VAWA005List.PageNumber, Model.VAWA005List.TotalItemCount).Options(o => o
                  .DisplayTemplate("BootstrapPagination")
                 .MaxNrOfPages(5)
                 .SetPreviousPageText("上頁")
                 .SetNextPageText("下頁")
             )
        </tr>
    </tbody>

}
