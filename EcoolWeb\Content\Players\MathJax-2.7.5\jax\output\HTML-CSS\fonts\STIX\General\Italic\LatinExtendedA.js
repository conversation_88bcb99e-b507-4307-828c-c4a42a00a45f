/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/General/Italic/LatinExtendedA.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXGeneral-italic"],{256:[757,0,611,-51,564],257:[543,11,501,17,481],258:[862,0,611,-51,564],259:[650,11,501,17,481],260:[668,169,611,-51,626],261:[441,169,501,17,529],262:[876,18,667,66,689],263:[664,11,444,30,431],264:[875,18,667,66,689],265:[661,11,444,30,427],266:[818,18,667,66,689],267:[606,11,444,30,425],268:[875,18,667,66,689],269:[661,11,444,30,473],270:[875,0,722,-8,700],271:[691,13,609,15,697],272:[653,0,722,-8,700],273:[683,13,500,15,580],274:[757,0,611,-1,634],275:[542,11,444,31,466],276:[866,0,611,-1,634],277:[650,11,444,31,471],278:[818,0,611,-1,634],279:[606,11,444,31,412],280:[653,175,611,-1,634],281:[441,175,444,31,412],282:[875,0,611,-1,634],283:[661,11,444,31,502],284:[877,18,722,52,722],285:[661,206,500,8,471],286:[866,18,722,52,722],287:[650,206,500,8,476],288:[818,18,722,52,722],289:[606,206,500,8,471],290:[666,267,722,52,722],291:[724,206,500,8,471],292:[875,0,722,-8,769],293:[875,9,500,19,478],294:[653,0,722,-8,769],295:[683,9,500,19,478],296:[836,0,333,-8,444],297:[624,11,278,30,357],298:[757,0,333,-8,439],299:[543,11,278,29,341],300:[866,0,333,-8,448],301:[650,11,278,46,347],302:[653,169,333,-8,384],303:[654,169,278,49,303],304:[818,0,333,-8,384],306:[653,18,750,-8,783],307:[654,207,500,49,500],308:[877,18,444,-6,536],309:[661,207,278,-124,353],310:[653,267,667,7,722],311:[683,267,444,14,461],312:[459,0,542,5,601],313:[876,0,556,-8,559],314:[876,11,278,41,348],315:[653,267,556,-8,559],316:[683,267,278,7,279],317:[666,0,556,-8,595],318:[693,11,278,41,448],319:[653,0,556,-8,559],320:[683,11,323,41,386],321:[653,0,556,-8,559],322:[683,11,278,37,307],323:[876,15,667,-20,727],324:[664,9,500,14,474],325:[653,267,667,-20,727],326:[441,267,500,14,474],327:[875,15,667,-20,727],328:[661,9,500,14,475],329:[691,9,577,58,540],330:[666,18,722,-8,700],331:[441,208,500,14,442],332:[757,18,722,60,699],333:[543,11,500,27,511],334:[866,18,722,60,709],335:[650,11,500,27,533],336:[876,18,722,60,720],337:[664,11,500,27,541],338:[666,8,944,49,964],339:[441,12,667,20,646],340:[876,0,611,-13,588],341:[664,0,389,45,412],342:[653,267,611,-13,588],343:[441,267,389,-2,412],344:[875,0,611,-13,588],345:[663,0,389,45,426],346:[876,18,500,17,508],347:[664,13,389,16,403],348:[877,18,500,17,508],349:[661,13,389,16,385],350:[667,217,500,17,508],351:[442,217,389,16,366],352:[875,18,500,17,532],353:[663,13,389,16,426],354:[653,217,556,59,633],355:[546,217,278,-38,296],356:[875,0,556,59,633],357:[693,11,278,38,453],358:[653,0,556,59,633],359:[546,11,278,28,296],360:[836,18,722,102,765],361:[624,11,500,42,475],362:[757,18,722,102,765],363:[543,11,500,42,475],364:[866,18,722,102,765],365:[650,11,500,42,480],366:[907,18,722,102,765],367:[691,11,500,42,475],368:[876,18,722,102,765],369:[664,11,500,42,511],370:[653,169,722,102,765],371:[441,169,500,42,538],372:[877,18,833,71,906],373:[661,18,667,15,648],374:[877,0,556,78,633],375:[661,206,444,-24,426],376:[818,0,556,78,633],377:[876,0,556,-6,606],378:[664,81,389,-2,390],379:[818,0,556,-6,606],380:[606,81,389,-2,380],381:[875,0,556,-6,606],382:[663,81,389,-2,426],383:[683,0,383,13,513]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/Italic/LatinExtendedA.js");
