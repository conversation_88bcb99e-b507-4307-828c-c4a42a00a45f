﻿@model  GameLeveViewModel
@{
    ViewBag.Title = "View"; Layout = "~/Views/Shared/_LayoutGame.cshtml";
}
<div class="Title_Secondary" style="width:740px;">中獎名單</div>
<br />
<a role="button" href='@Url.Action("GameIndex1",(string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys text-right">
    <span class="fa fa-plus" aria-hidden="true"></span>
    回抽獎兌換獎品 - 列表
</a>
@using (Html.BeginForm("GetWinnerItemDetail", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1", enctype = "multipart/form-data" }))
{
    <div id="PageContent">
        @Html.Action("GetWinnerItemDetail", (string)ViewBag.BRE_NO, new { GAME_NO = Model.GAME_NO })
    </div>
}
@section scripts{
    <script>
    function funAjax() {
        //var whereGradetxt = $("#whereGrade1").val();
        //console.log(whereGradetxt);
        //var whereCLASS_NOtxt = $("#whereCLASS_NO1").val();
        var WhereSearchtxt = $("#Main_ROLL_CALL_ID").val();
        //console.log(WhereSearchtxt);
        $.ajax({
            type: "GET",
                url: '@(Url.Action("GetWinnerItemDetail", (string)ViewBag.BRE_NO))',
            data: {
                Keyword:WhereSearchtxt,
                OrdercColumn: $('#OrdercColumn').val(),
                SyntaxName: $('#SyntaxName').val(),
            },
                async: false,
                cache: false,
                dataType: 'html',
                success: function (data) {

                    $("#cboxLoadedContent").html('');
                    $("#cboxLoadedContent").html(data);
                }
            });
    }
    function doSort(SortCol) {
        var sort = "";
        sort = SortCol;
        OrderByName = $('#OrdercColumn').val();
        SyntaxName = $('#SyntaxName').val();
        $('#OrdercColumn').val(SortCol);
        console.log(SyntaxName);
        if (OrderByName == SortCol) {

            if (SyntaxName == "Desc") {

                $('#OrdercColumn').val(sort);
                $('#SyntaxName').val("ASC");
            }
            else {

                $('#OrdercColumn').val(sort);
                $('#SyntaxName').val("Desc");
            }
        }
        else {

            $('#OrdercColumn').val(sort);
            $('#SyntaxName').val("Desc");
        }
        funAjax()
    }
    </script>}