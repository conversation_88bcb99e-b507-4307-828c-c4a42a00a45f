﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class GAAI01SearchNotReportedViewModel
    {
        /// <summary>
        ///防身周期ID
        /// </summary>
        [DisplayName("防身周期ID")]
        public string ALARM_ID { get; set; }

        /// <summary>
        ///週期
        /// </summary>
        [DisplayName("週期")]
        public byte? CYCLE { get; set; }

        /// <summary>
        ///登記日期_開始
        /// </summary>
        [DisplayName("週期日期_開始")]
        [DataType(DataType.Date), DisplayFormat(DataFormatString = "{0:yyyy/MM/dd}", ApplyFormatInEditMode = true)]
        public DateTime? ALARM_DATES { get; set; }

        /// <summary>
        ///登記日期_結束
        /// </summary>
        [DisplayName("週期日期_結束")]
        [DataType(DataType.Date), DisplayFormat(DataFormatString = "{0:yyyy/MM/dd}", ApplyFormatInEditMode = true)]
        public DateTime? ALARM_DATEE { get; set; }

        /// <summary>
        /// 全校班級數量
        /// </summary>
        public int CLASS_COUNT { get; set; }

        /// <summary>
        /// 沒有填報的班級數量
        /// </summary>
        public int NotReportedClassCount { get; set; }

        /// <summary>
        /// 有填報班級數量
        /// </summary>
        public int ReportedClassCount { get; set; }

        /// <summary>
        /// 填報率=有填報班級數量/全校班級數量
        /// </summary>
        public decimal? ReportedClassRate { get; set; }
    }
}