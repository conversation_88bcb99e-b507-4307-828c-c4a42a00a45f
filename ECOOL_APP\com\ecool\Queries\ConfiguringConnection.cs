﻿using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    internal class ConfiguringConnection : IConnection
    {
        public ConfiguringConnection(IConnection parent, Action<DbContext> setup)
        {
            Parent = parent;
            Setup = setup;
        }

        public T ToContext<T>() where T : DbContext
        {
            var context = Parent.ToContext<T>();
            Setup(context);
            return context;
        }

        protected IConnection Parent { get; }
        protected Action<DbContext> Setup { get; }
    }
}