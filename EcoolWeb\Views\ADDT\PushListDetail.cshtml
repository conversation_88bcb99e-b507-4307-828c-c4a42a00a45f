﻿@model ECOOL_APP.EF.ADDT06
@{
    ViewBag.Title = "閱讀護照-明細內容";
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();

    if (string.IsNullOrWhiteSpace(UUID) == false)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
}
<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>

@Html.Partial("_Title_Secondary")

<img src="~/Content/img/web-Bar-09.png" style="width:100%" class="img-responsive App_hide" alt="Responsive image" />
<div class="Div-EZ-reader">
    <div class="Details">
        <div class="row">
            <div class="col-md-5 col-sm-5 dl-horizontal-EZ">
                <samp class="dt">
                    申請日
                </samp>
                <samp class="dd">
                    @Html.DisplayFor(model => model.CRE_DATE, "ShortDateTime")
                </samp>
            </div>
            <div class="col-md-3 col-sm-3  dl-horizontal-EZ ">
                <samp class="dt">
                    班級
                </samp>
                <samp class="dd">
                    @Html.DisplayFor(model => model.CLASS_NO)
                </samp>
            </div>
            <div class="col-md-3 col-sm-3  dl-horizontal-EZ ">
                <samp class="dt">
                    座號
                </samp>
                <samp class="dd">
                    @Html.DisplayFor(model => model.SEAT_NO)
                </samp>
            </div>
            <div class="col-md-4 col-sm-4 dl-horizontal-EZ">
                <samp class="dt">
                    姓名
                </samp>
                <samp class="dd">
                    @Html.DisplayFor(model => model.SNAME)
                </samp>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12 col-sm-12  dl-horizontal-EZ">
                <samp class="dt">
                    書名
                </samp>
                <samp class="dd">
                    @Html.DisplayFor(model => model.BOOK_NAME)
                </samp>
            </div>
        </div>
        <div style="height:20px"></div>
        <div class="row">
            <div class="col-md-12 col-sm-12  dl-horizontal-EZ">
                <samp class="dt">
                    心得
                </samp>
                <div class="p-context">
                    @if (string.IsNullOrWhiteSpace(ViewBag.ImageUrl) == false)
                    {
                        <img id="imgArticle" class="img-responsive " src='@ViewBag.ImageUrl' href="@ViewBag.ImageUrl" style="float:right;margin:10px;max-height:250px;width:auto" />
                    }

                    @if (ViewBag.ShowOriginalArticle == "V")
                    {
                        @Html.Raw(HttpUtility.HtmlDecode(Model.REVIEW_VERIFY.Replace("\r\n", "<br />")))
                    }
                    else
                    {
                        if (Model.REVIEW != null)
                        {
                            @Html.Raw(HttpUtility.HtmlDecode(Model.REVIEW.Replace("\r\n", "<br />")))
                        }

                    }
                </div>
            </div>
        </div>
        <div class="row Div-btn-center">

            @if (ViewBag.ShowOriginalArticle == "O")
            {
                @Html.ActionLink("批閱後文章", "PushListDetail", new { APPLY_NO = Model.APPLY_NO, ShowOriginal = false }, new { @role = "button", @class = "btn btn-default" })
            }
            else if (ViewBag.ShowOriginalArticle == "V")
            {
                <a href="@Url.Action("PushListDetail", "ADDT",new { APPLY_NO = Model.APPLY_NO,ShowOriginal = true})" role="button" class="btn btn-default">
                    學生原稿
                </a>
            }
            <a href='@Url.Action("PushList", "ADDT", new { ListType=Request.Params["ListType"] })' role="button" class="btn btn-default">
                返回
            </a>
        </div>
    </div>
</div>

<script language="javascript">
    $(document).ready(function () {
        $("#imgArticle").colorbox({ opacity: 0.82 });
    });
</script>