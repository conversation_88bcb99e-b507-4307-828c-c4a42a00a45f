﻿@model AWAI01EditViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title; ;
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    if (string.IsNullOrWhiteSpace(UUID) == false)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
}
<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@using (Html.BeginForm("ActionSaveAwat", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "contentForm", name = "contentForm", enctype = "multipart/form-data" }))
{
    <img src="~/Content/img/web-bar2-revise-09.png" style="width:100%" class="img-responsive App_hide" alt="Responsive image" />
    <div class="Div-EZ-Awat02" id="showView">

        @Html.Partial("_Awat02Form", Model)
        <div class="text-center">
            <div class="form-group">
                @if (Model.Search.WhereAWARD_STS == "M")
                {
                    if (string.IsNullOrWhiteSpace(Model.Search.unProduct))
                    {
                        <div class="col-md-offset-3 col-md-5">
                            <input type="submit" value="確定修改" class="btn btn-default" onclick="doModify();" />
                        </div>
                    }
                    else
                    {
                        <div class="col-md-8">
                            <input type="submit" value="修改同時重新上架" class="btn btn-default" onclick="doModify();" />
                            <input type="submit" value="刪除獎品" class="btn btn-default" onclick="doRealDelete();" />
                            <input type="submit" value="隱藏獎品" class="btn btn-default" onclick="doHide();" />
                        </div>
                    }
                }
                else if (Model.Search.WhereAWARD_STS == "D")
                {
                    <div class="col-md-offset-1 col-md-7">
                        <input type="submit" value="下架獎品" class="btn btn-default" onclick="doDelete();" />
                        <input type="submit" value="刪除獎品" class="btn btn-default" onclick="doRealDelete();" />

                    </div>
                }
         
                <div class="col-md-offset-1 col-md-2">
                    <input type="button" value="返回" class="btn btn-default" onclick="goBack();" />
                </div>
            </div>
        </div>

    </div>
}


@section Scripts
{
    <script type="text/javascript">
        var targetFormID = '#contentForm';

        function goBack() {
            $(targetFormID).attr("action", "@Url.Action(Model.Search.BackAction, (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        function doModify() {
            $(targetFormID).attr("action", "@Url.Action("ActionSaveAwat", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        //下架
        function doDelete() {
            $(targetFormID).attr("action", "@Url.Action("AwatDelete", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        //刪除獎品
        function doRealDelete() {
            $(targetFormID).attr("action", "@Url.Action("AwatRealDelete", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

         //隱藏獎品
        function doHide() {
            $(targetFormID).attr("action", "@Url.Action("AwatHide", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }
    </script>
}