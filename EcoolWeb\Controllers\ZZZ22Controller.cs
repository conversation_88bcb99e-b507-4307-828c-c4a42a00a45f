﻿using ECOOL_APP.com.ecool.util;
using ECOOL_APP.EF;
using EcoolWeb.CustomAttribute;
using EcoolWeb.Models;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Web;
using System.Web.Mvc;
namespace EcoolWeb.Controllers
{
    [SessionExpire]
    public class ZZZ22Controller : Controller
    {
        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();
        private ECOOL_APP.UserProfile user = UserProfileHelper.Get();
        // GET: ZZZ22
        public ActionResult MODIFY()
        {
            HRMT04 H04 = db.HRMT04.FirstOrDefault();
            return View(H04);
        }    
        
        [HttpPost]
        [ValidateInput(false)]
        public ActionResult MODIFY(HRMT04 H04)
        {
            HRMT04 oldH04 = new HRMT04();
            int USER_ID = Convert.ToInt16(H04.USER_ID);
            oldH04 = db.HRMT04.Where(p => p.USER_ID == USER_ID).FirstOrDefault();
            oldH04.USERNAME = H04.USERNAME;
            oldH04.EMAIL = H04.EMAIL;
            oldH04.TEL = H04.TEL;
            oldH04.ECOOLEXPLAIN = HtmlUtility.SanitizeHtml(H04.ECOOLEXPLAIN);

            db.Entry(oldH04).State = EntityState.Modified;

            try
            {
                db.SaveChanges();
                TempData["StatusMessage"] = "異動成功";
            }
            catch (Exception ex)
            {
                TempData["StatusMessage"] = "異動失敗, 系統錯誤原因:" + ex.Message;
            }
          

            return RedirectToAction("../ZZZ22/MODIFY");
        }
    }
   
}