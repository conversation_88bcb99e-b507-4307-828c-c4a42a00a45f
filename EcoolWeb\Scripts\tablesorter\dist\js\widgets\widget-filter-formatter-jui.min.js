(function(factory){if (typeof define === 'function' && define.amd){define(['jquery'], factory);} else if (typeof module === 'object' && typeof module.exports === 'object'){module.exports = factory(require('jquery'));} else {factory(jQuery);}}(function(jQuery){

/*! Widget: filter jQuery UI formatter functions - updated 7/17/2014 (v2.17.5) */
!function(f){"use strict";var u=f.tablesorter||{},m=".compare-select",v=u.filterFormatter=f.extend({},u.filterFormatter,{addCompare:function(e,a,t){if(t.compare&&f.isArray(t.compare)&&1<t.compare.length){var n="",d=[m.slice(1)," "+m.slice(1),""],i=t.cellText?'<label class="'+d.join("-label")+a+'">'+t.cellText+"</label>":"";f.each(t.compare,function(e,a){n+="<option "+(t.selected===e?"selected":"")+">"+a+"</option>"}),e.wrapInner('<div class="'+d.join("-wrapper")+a+'" />').prepend(i+'<select class="'+d.join("")+a+'" />').find("select").append(n)}},updateCompare:function(e,a,t){var n=a.val()||"",d=n.replace(/\s*?[><=]\s*?/g,""),i=n.match(/[><=]/g)||"";return t.compare&&(f.isArray(t.compare)&&(i=(i||[]).join("")||t.compare[t.selected||0]),e.find(m).val(i)),[n,d]},uiSpinner:function(r,a,e){var o=f.extend({delayed:!0,addToggle:!0,exactMatch:!0,value:1,cellText:"",compare:"",min:0,max:100,step:1,disabled:!1},e),s=r.closest("table")[0].config,t=f('<input class="filter" type="hidden">').appendTo(r).bind("change"+s.namespace+"filter",function(){n({value:this.value,delayed:!1})}),c=[],n=function(e,a){var t,n=!0,d=e&&e.value&&u.formatFloat((e.value+"").replace(/[><=]/g,""))||r.find(".spinner").val()||o.value,i=(f.isArray(o.compare)?r.find(m).val()||o.compare[o.selected||0]:o.compare)||"",l=e&&"boolean"==typeof e.delayed?e.delayed:!s.$table[0].hasInitialized||(o.delayed||"");o.addToggle&&(n=r.find(".toggle").is(":checked")),t=o.disabled||!n?"disable":"enable",u.isEmptyObject(r.find(".spinner").data())||(r.find(".filter").val(n?(i||(o.exactMatch?"=":""))+d:"").trigger(a?"":"search",l).end().find(".spinner").spinner(t).val(d),c.length&&(c.find(".spinner").spinner(t).val(d).end().find(m).val(i),o.addToggle&&(c.find(".toggle")[0].checked=n)))};return o.oldcreate=o.create,o.oldspin=o.spin,o.create=function(e,a){n(),"function"==typeof o.oldcreate&&o.oldcreate(e,a)},o.spin=function(e,a){n(a),"function"==typeof o.oldspin&&o.oldspin(e,a)},o.addToggle&&f('<div class="button"><input id="uispinnerbutton'+a+'" type="checkbox" class="toggle" /><label for="uispinnerbutton'+a+'"></label></div>').appendTo(r).find(".toggle").bind("change",function(){n()}),r.closest("thead").find("th[data-column="+a+"]").addClass("filter-parsed"),f('<input class="spinner spinner'+a+'" />').val(o.value).appendTo(r).spinner(o).bind("change keyup",function(){n()}),s.$table.bind("filterFomatterUpdate"+s.namespace+"filter",function(){var e=v.updateCompare(r,t,o)[0];r.find(".spinner").val(e),n({value:e},!0),u.filter.formatterUpdated(r,a)}),o.compare&&(v.addCompare(r,a,o),r.find(m).bind("change",function(){n()})),s.$table.bind("stickyHeadersInit"+s.namespace+"filter",function(){c=s.widgetOptions.$sticky.find(".tablesorter-filter-row").children().eq(a).empty(),o.addToggle&&f('<div class="button"><input id="stickyuispinnerbutton'+a+'" type="checkbox" class="toggle" /><label for="stickyuispinnerbutton'+a+'"></label></div>').appendTo(c).find(".toggle").bind("change",function(){r.find(".toggle")[0].checked=this.checked,n()}),f('<input class="spinner spinner'+a+'" />').val(o.value).appendTo(c).spinner(o).bind("change keyup",function(){r.find(".spinner").val(this.value),n()}),o.compare&&(v.addCompare(c,a,o),c.find(m).bind("change",function(){r.find(m).val(f(this).val()),n()}))}),s.$table.bind("filterReset"+s.namespace+"filter",function(){f.isArray(o.compare)&&r.add(c).find(m).val(o.compare[o.selected||0]),o.addToggle&&(r.find(".toggle")[0].checked=!1),r.find(".spinner").spinner("value",o.value),setTimeout(function(){n()},0)}),n(),t},uiSlider:function(r,o,e){var s=f.extend({delayed:!0,valueToHeader:!1,exactMatch:!0,cellText:"",compare:"",allText:"all",value:0,min:0,max:100,step:1,range:"min"},e),c=r.closest("table")[0].config,a=f('<input class="filter" type="hidden">').appendTo(r).bind("change"+c.namespace+"filter",function(){t({value:this.value})}),p=[],t=function(e,a){var t=void 0!==e&&u.formatFloat((e.value+"").replace(/[><=]/g,""))||s.value,n=s.compare?t:t===s.min?s.allText:t,d=(f.isArray(s.compare)?r.find(m).val()||s.compare[s.selected||0]:s.compare)||"",i=d+n,l=e&&"boolean"==typeof e.delayed?e.delayed:!c.$table[0].hasInitialized||(s.delayed||"");s.valueToHeader?r.closest("thead").find("th[data-column="+o+"]").find(".curvalue").html(" ("+i+")"):r.find(".ui-slider-handle").addClass("value-popup").attr("data-value",i),u.isEmptyObject(r.find(".slider").data())||(r.find(".filter").val(d?d+t:t===s.min?"":(s.exactMatch?"=":"")+t).trigger(a?"":"search",l).end().find(".slider").slider("value",t),p.length&&(p.find(m).val(d).end().find(".slider").slider("value",t),s.valueToHeader?p.closest("thead").find("th[data-column="+o+"]").find(".curvalue").html(" ("+i+")"):p.find(".ui-slider-handle").addClass("value-popup").attr("data-value",i)))};return r.closest("thead").find("th[data-column="+o+"]").addClass("filter-parsed"),s.valueToHeader&&r.closest("thead").find("th[data-column="+o+"]").find(".tablesorter-header-inner").append('<span class="curvalue" />'),s.oldcreate=s.create,s.oldslide=s.slide,s.create=function(e,a){t(),"function"==typeof s.oldcreate&&s.oldcreate(e,a)},s.slide=function(e,a){t(a),"function"==typeof s.oldslide&&s.oldslide(e,a)},f('<div class="slider slider'+o+'"/>').appendTo(r).slider(s),c.$table.bind("filterFomatterUpdate"+c.namespace+"filter",function(){var e=v.updateCompare(r,a,s)[0];r.find(".slider").slider("value",e),t({value:e},!1),u.filter.formatterUpdated(r,o)}),s.compare&&(v.addCompare(r,o,s),r.find(m).bind("change",function(){t({value:r.find(".slider").slider("value")})})),c.$table.bind("filterReset"+c.namespace+"filter",function(){f.isArray(s.compare)&&r.add(p).find(m).val(s.compare[s.selected||0]),setTimeout(function(){t({value:s.value})},0)}),c.$table.bind("stickyHeadersInit"+c.namespace+"filter",function(){p=c.widgetOptions.$sticky.find(".tablesorter-filter-row").children().eq(o).empty(),f('<div class="slider slider'+o+'"/>').val(s.value).appendTo(p).slider(s).bind("change keyup",function(){r.find(".slider").slider("value",this.value),t()}),s.compare&&(v.addCompare(p,o,s),p.find(m).bind("change",function(){r.find(m).val(f(this).val()),t()}))}),a},uiRange:function(l,r,e){var o=f.extend({delayed:!0,valueToHeader:!1,values:[0,100],min:0,max:100,range:!0},e),s=l.closest("table")[0].config,t=f('<input class="filter" type="hidden">').appendTo(l).bind("change"+s.namespace+"filter",function(){a()}),c=[],a=function(){var e=t.val(),a=e.split(" - ");""===e&&(a=[o.min,o.max]),a&&a[1]&&n({values:a,delay:!1},!0)},n=function(e,a){var t=e&&e.values||o.values,n=t[0]+" - "+t[1],d=t[0]===o.min&&t[1]===o.max?"":n,i=e&&"boolean"==typeof e.delayed?e.delayed:!s.$table[0].hasInitialized||(o.delayed||"");o.valueToHeader?l.closest("thead").find("th[data-column="+r+"]").find(".currange").html(" ("+n+")"):l.find(".ui-slider-handle").addClass("value-popup").eq(0).attr("data-value",t[0]).end().eq(1).attr("data-value",t[1]),u.isEmptyObject(l.find(".range").data())||(l.find(".filter").val(d).trigger(a?"":"search",i).end().find(".range").slider("values",t),c.length&&(c.find(".range").slider("values",t),o.valueToHeader?c.closest("thead").find("th[data-column="+r+"]").find(".currange").html(" ("+n+")"):c.find(".ui-slider-handle").addClass("value-popup").eq(0).attr("data-value",t[0]).end().eq(1).attr("data-value",t[1])))};return l.closest("thead").find("th[data-column="+r+"]").addClass("filter-parsed"),o.valueToHeader&&l.closest("thead").find("th[data-column="+r+"]").find(".tablesorter-header-inner").append('<span class="currange"/>'),o.oldcreate=o.create,o.oldslide=o.slide,o.create=function(e,a){n(),"function"==typeof o.oldcreate&&o.oldcreate(e,a)},o.slide=function(e,a){n(a),"function"==typeof o.oldslide&&o.oldslide(e,a)},f('<div class="range range'+r+'"/>').appendTo(l).slider(o),s.$table.bind("filterFomatterUpdate"+s.namespace+"filter",function(){a(),u.filter.formatterUpdated(l,r)}),s.$table.bind("filterReset"+s.namespace+"filter",function(){l.find(".range").slider("values",o.values),setTimeout(function(){n()},0)}),s.$table.bind("stickyHeadersInit"+s.namespace+"filter",function(){c=s.widgetOptions.$sticky.find(".tablesorter-filter-row").children().eq(r).empty(),f('<div class="range range'+r+'"/>').val(o.value).appendTo(c).slider(o).bind("change keyup",function(){l.find(".range").val(this.value),n()})}),t},uiDateCompare:function(l,t,e){var r,a,o=f.extend({cellText:"",compare:"",endOfDay:!0,defaultDate:"",changeMonth:!0,changeYear:!0,numberOfMonths:1},e),s=l.closest("table")[0].config,n=l.closest("thead").find("th[data-column="+t+"]").addClass("filter-parsed"),d=f('<input class="dateCompare" type="hidden">').appendTo(l).bind("change"+s.namespace+"filter",function(){var e=this.value;e&&o.onClose(e)}),c=[],i=function(e){var a,t,n=r.datepicker("getDate")||"",d=(f.isArray(o.compare)?l.find(m).val()||o.compare[o.selected||0]:o.compare)||"",i=!s.$table[0].hasInitialized||(o.delayed||"");r.datepicker("setDate",(""===n?"":n)||null),""===n&&(e=!1),t=(a=r.datepicker("getDate"))&&(o.endOfDay&&/<=/.test(d)?a.setHours(23,59,59,999):a.getTime())||"",a&&o.endOfDay&&"="===d&&(d="",t+=" - "+a.setHours(23,59,59,999),e=!1),l.find(".dateCompare").val(d+t).trigger(e?"":"search",i).end(),c.length&&c.find(".dateCompare").val(d+t).end().find(m).val(d)};return a='<input type="text" class="date date'+t+'" placeholder="'+(n.data("placeholder")||n.attr("data-placeholder")||s.widgetOptions.filter_placeholder.search||"")+'" />',r=f(a).appendTo(l),o.oldonClose=o.onClose,o.onClose=function(e,a){i(),"function"==typeof o.oldonClose&&o.oldonClose(e,a)},r.datepicker(o),s.$table.bind("filterReset"+s.namespace+"filter",function(){f.isArray(o.compare)&&l.add(c).find(m).val(o.compare[o.selected||0]),l.add(c).find(".date").val(o.defaultDate).datepicker("setDate",o.defaultDate||null),setTimeout(function(){i()},0)}),s.$table.bind("filterFomatterUpdate"+s.namespace+"filter",function(){var e,a=d.val();/\s+-\s+/.test(a)?(l.find(m).val("="),e=a.split(/\s+-\s+/)[0],r.datepicker("setDate",e||null)):e=""!==(e=v.updateCompare(l,d,o)[1].toString()||"")?/\d{5}/g.test(e)?new Date(Number(e)):e||"":"",l.add(c).find(".date").datepicker("setDate",e||null),setTimeout(function(){i(!0),u.filter.formatterUpdated(l,t)},0)}),o.compare&&(v.addCompare(l,t,o),l.find(m).bind("change",function(){i()})),s.$table.bind("stickyHeadersInit"+s.namespace+"filter",function(){(c=s.widgetOptions.$sticky.find(".tablesorter-filter-row").children().eq(t).empty()).append(a).find(".date").datepicker(o),o.compare&&(v.addCompare(c,t,o),c.find(m).bind("change",function(){l.find(m).val(f(this).val()),i()}))}),d.val(o.defaultDate?o.defaultDate:"")},uiDatepicker:function(i,n,e){var a,d,l=f.extend({endOfDay:!0,textFrom:"from",textTo:"to",from:"",to:"",changeMonth:!0,changeYear:!0,numberOfMonths:1},e),r=[],t=i.closest("table")[0].config,o=function(e){return e instanceof Date&&isFinite(e)},s=f('<input class="dateRange" type="hidden">').appendTo(i).bind("change"+t.namespace+"filter",function(){var e=this.value;e.match(" - ")?(e=e.split(" - "),i.find(".dateTo").val(e[1]),d(e[0])):e.match(">=")?d(e.replace(">=","")):e.match("<=")&&d(e.replace("<=",""))}),c=i.closest("thead").find("th[data-column="+n+"]").addClass("filter-parsed");return a="<label>"+l.textFrom+'</label><input type="text" class="dateFrom" placeholder="'+(c.data("placeholderFrom")||c.attr("data-placeholder-from")||t.widgetOptions.filter_placeholder.from||"")+'" /><label>'+l.textTo+'</label><input type="text" class="dateTo" placeholder="'+(c.data("placeholderTo")||c.attr("data-placeholder-to")||t.widgetOptions.filter_placeholder.to||"")+'" />',f(a).appendTo(i),l.oldonClose=l.onClose,d=l.onClose=function(e,a){var t,n=i.find(".dateFrom").datepicker("getDate"),d=i.find(".dateTo").datepicker("getDate");n=o(n)?n.getTime():"",d=o(d)&&(l.endOfDay?d.setHours(23,59,59,999):d.getTime())||"",t=n?d?n+" - "+d:">="+n:d?"<="+d:"",i.add(r).find(".dateRange").val(t).trigger("search"),n=n?new Date(n):"",d=d?new Date(d):"",/<=/.test(t)?i.add(r).find(".dateFrom").datepicker("option","maxDate",d||null).end().find(".dateTo").datepicker("option","minDate",null).datepicker("setDate",d||null):/>=/.test(t)?i.add(r).find(".dateFrom").datepicker("option","maxDate",null).datepicker("setDate",n||null).end().find(".dateTo").datepicker("option","minDate",n||null):i.add(r).find(".dateFrom").datepicker("option","maxDate",null).datepicker("setDate",n||null).end().find(".dateTo").datepicker("option","minDate",null).datepicker("setDate",d||null),"function"==typeof l.oldonClose&&l.oldonClose(e,a)},l.defaultDate=l.from||"",i.find(".dateFrom").datepicker(l),l.defaultDate=l.to||"+7d",i.find(".dateTo").datepicker(l),t.$table.bind("filterFomatterUpdate"+t.namespace+"filter",function(){var e=s.val()||"",a="",t="";/\s+-\s+/.test(e)?(a=(e=e.split(/\s+-\s+/)||[])[0]||"",t=e[1]||""):/>=/.test(e)?a=e.replace(/>=/,"")||"":/<=/.test(e)&&(t=e.replace(/<=/,"")||""),a=""!==a?/\d{5}/g.test(a)?new Date(Number(a)):a||"":"",t=""!==t?/\d{5}/g.test(t)?new Date(Number(t)):t||"":"",i.add(r).find(".dateFrom").datepicker("setDate",a||null),i.add(r).find(".dateTo").datepicker("setDate",t||null),setTimeout(function(){d(),u.filter.formatterUpdated(i,n)},0)}),t.$table.bind("stickyHeadersInit"+t.namespace+"filter",function(){(r=t.widgetOptions.$sticky.find(".tablesorter-filter-row").children().eq(n).empty()).append(a),l.defaultDate=l.from||"",r.find(".dateFrom").datepicker(l),l.defaultDate=l.to||"+7d",r.find(".dateTo").datepicker(l)}),i.closest("table").bind("filterReset"+t.namespace+"filter",function(){i.add(r).find(".dateFrom").val("").datepicker("setDate",l.from||null),i.add(r).find(".dateTo").val("").datepicker("setDate",l.to||null),setTimeout(function(){d()},0)}),s.val(l.from?l.to?l.from+" - "+l.to:">="+l.from:l.to?"<="+l.to:"")}})}(jQuery);return jQuery;}));
