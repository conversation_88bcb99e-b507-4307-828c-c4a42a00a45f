/*
 *  /MathJax/jax/output/SVG/fonts/STIX-Web/DoubleStruck/BoldItalic/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax.SVG.FONTDATA.FONTS["STIXMathJax_DoubleStruck-bold-italic"]={directory:"DoubleStruck/BoldItalic",family:"STIXMathJax_DoubleStruck",weight:"bold",style:"italic",id:"STIXWEBDOUBLESTRUCKBI",32:[0,0,250,0,0,""],160:[0,0,250,0,0,""],8450:[685,14,713,35,704,"704 545l-41 -45c-51 77 -115 122 -210 122c-47 0 -99 -2 -134 -38l-137 -502c37 -31 88 -33 134 -33c122 0 181 62 273 128l36 -44c-88 -91 -204 -147 -332 -147c-153 0 -258 100 -258 254c0 236 180 445 423 445c126 0 177 -38 246 -140zM128 116l118 438 c-89 -73 -148 -181 -148 -297c0 -48 11 -97 30 -141"],8461:[669,0,773,21,808,"808 669l-180 -669h-209l81 300h-189l-81 -300h-209l180 669h209l-80 -299h189l80 299h209zM725 606h-77l-146 -543h77zM327 606h-77l-146 -543h77"],8469:[669,0,760,27,783,"783 669l-178 -669h-157l-225 485h-2l-129 -485h-65l178 669h155l227 -485h2l129 485h65zM562 86l-240 520h-70l-5 -21l241 -522h69"],8473:[669,0,497,18,715,"198 669h329c103 0 188 -43 188 -157c0 -169 -116 -262 -279 -262h-142l-67 -250h-209zM652 501c0 32 -12 81 -47 91l-3 -12c-21 -86 -46 -172 -69 -257c75 9 119 112 119 178zM466 319l76 282c-17 5 -39 5 -56 5h-96l-79 -293h99c17 0 38 2 56 6zM324 606h-77l-146 -543 h77"],8474:[685,74,754,35,734,"639 11l27 -47c-33 -30 -60 -38 -104 -38c-59 0 -94 35 -124 81c-44 -14 -89 -21 -135 -21c-158 0 -268 91 -268 254c0 236 180 445 423 445c161 0 276 -85 276 -254c0 -164 -84 -319 -230 -396c15 -23 37 -46 67 -46c25 0 46 10 68 22zM637 537l-112 -417 c97 62 146 182 146 294c0 43 -10 87 -34 123zM455 104l130 483c-36 23 -89 35 -132 35c-39 0 -93 -6 -128 -23l-135 -506c49 45 84 72 154 72c48 0 82 -24 111 -61zM134 132l113 424c-96 -64 -149 -185 -149 -299c0 -47 10 -86 36 -125zM402 59c-19 25 -42 43 -74 43 c-35 0 -66 -18 -89 -43c25 -6 51 -10 77 -10c29 0 58 4 86 10"],8477:[669,0,727,18,718,"198 669h332c103 0 188 -43 188 -157c0 -107 -49 -198 -151 -239l96 -273h-212c-8 15 -13 33 -19 49l-72 201h-66l-67 -250h-209zM607 593l-73 -271c81 17 121 103 121 179c0 41 -11 72 -48 92zM469 319l75 282c-18 2 -37 5 -55 5h-99l-79 -293h102c14 0 42 1 56 6z M574 63l-67 192c-26 -5 -53 -5 -80 -5l68 -187h79zM324 606h-77l-146 -543h77"],8484:[669,0,807,23,837,"837 606l-528 -541v-2h394l-16 -63h-664v63l531 541v2h-346l16 63h613v-63zM753 606h-107l-538 -543h108"],8508:[449,13,730,32,715,"715 386h-117l-67 -256c-5 -19 -11 -38 -11 -58c0 -8 1 -14 6 -21c17 4 28 11 36 18c11 8 19 17 34 31c13 12 27 25 41 37l41 -45c-22 -21 -84 -79 -108 -90c-33 -15 -70 -15 -105 -15c-70 0 -128 39 -128 114c0 14 3 27 6 40l65 245h-77c-34 -129 -67 -258 -103 -386 h-190c31 129 68 257 102 386h-108v63h683v-63zM535 386h-63l-62 -234c-4 -14 -10 -31 -10 -46c0 -36 25 -56 60 -56l2 1c-3 8 -5 16 -5 24c0 15 3 30 7 44zM267 386h-64l-85 -323h63"],8511:[669,0,796,35,821,"821 669l-179 -669h-210l166 606h-188l-167 -606h-208l180 669h606zM739 606h-78l-147 -543h80zM341 606h-78l-147 -543h80"],8517:[669,0,748,18,733,"198 669h289c148 0 246 -102 246 -249c0 -225 -168 -420 -399 -420h-316zM635 534l-111 -412c90 70 146 175 146 290c0 43 -12 85 -35 122zM451 90l130 488c-38 23 -86 28 -130 28h-15h-46l-146 -543h43h16c50 0 105 5 148 27zM324 606h-77l-146 -543h77"],8518:[699,13,633,45,698,"698 699l-186 -699h-193l9 32h-1c-35 -36 -78 -45 -127 -45c-91 0 -155 70 -155 160c0 157 89 315 263 315c50 0 84 -9 123 -42h1l75 279h191zM618 636h-64l-154 -573h64zM289 393l-91 -340c10 -2 20 -3 30 -3c79 0 126 76 143 144c11 40 27 87 27 130 c0 42 -25 75 -69 75c-11 0 -30 -1 -40 -6zM138 70l83 309c-70 -39 -113 -136 -113 -214c0 -44 5 -60 30 -95"],8519:[462,13,575,45,540,"451 131l36 -38c-53 -84 -145 -106 -239 -106c-113 0 -203 63 -203 183c0 174 136 292 305 292c90 0 190 -55 190 -155c0 -39 -14 -77 -25 -114h-281l-35 -133c17 -10 37 -10 56 -10c107 0 118 17 196 81zM406 256h60c6 17 11 34 11 52c0 29 -17 49 -40 64zM342 256 l36 135c-11 6 -24 8 -37 8c-14 0 -45 -2 -55 -14l-35 -129h91zM218 372c-64 -41 -110 -126 -110 -202c0 -40 9 -61 31 -93c26 98 51 197 79 295"],8520:[669,0,379,40,413,"413 669l-49 -180h-193l50 180h192zM332 606h-64l-16 -54h64zM354 449l-121 -449h-193l122 449h192zM273 386h-64l-88 -323h64"],8521:[669,205,421,-93,455,"455 669l-49 -180h-193l50 180h192zM374 606h-64l-16 -54h64zM398 449l-123 -458c-34 -127 -155 -196 -281 -196c-33 0 -57 6 -87 18l32 57c17 -9 55 -12 74 -12c12 0 24 0 35 4l157 587h193zM318 386h-67l-138 -514c48 23 85 66 99 118"]};MathJax.Ajax.loadComplete(MathJax.OutputJax.SVG.fontDir+"/DoubleStruck/BoldItalic/Main.js");
