﻿@model ZZZI09ShowADDT22ViewViewModel
@using ECOOL_APP.com.ecool.service
@using ECOOL_APP.com.ecool.util

<style type="text/css">

    .wrapper {
        max-width: 80%;
        margin: 40px auto;
    }

    .element {
        background: #EEE;
        border: 2px solid #999;
        margin-bottom: 20px;
        width: 600px;
        float: left;
        margin-right: 20px;
        padding: 10px;
        box-sizing: border-box;
        border-radius: 5px;
    }
</style>
<style media="print">

    .element {
        background: #EEE;
        border: 2px solid #999;
        margin-bottom: 50px;
        width: 600px;
        
        float: left;
        margin-right: 20px;
        padding: 10px;
        box-sizing: border-box;
        border-radius: 5px;
    }

</style>
<script src="~/Scripts/grids.js"></script>

@if (Model.ADDT22List.Count > 0)
{
    <div class="wrapper">
        <div class="row">
            @foreach (var item in Model.ADDT22List)
            {
                <div class="col-lg-6" style="vertical-align:text-top;">
                    <div class="element">
                        @if (item.WORK_TYPE == ADDT21.WORK_TYPE_VAL.photo)
                        {

                            string NewImg = item.PHOTO_FILE.Replace(Path.GetExtension(item.PHOTO_FILE), "_M" + Path.GetExtension(item.PHOTO_FILE));

                            string ImgUrl = new ZZZI34Service().GetDirectorySysArtGalleryPath(item.PHOTO_SCHOOL_NO, item.ART_GALLERY_NO, NewImg);
                            if (ImgUrl == "")
                            {


                                ImgUrl = new ZZZI34Service().GetDirectorySysArtGalleryPath(item.PHOTO_SCHOOL_NO, item.ART_GALLERY_NO, item.PHOTO_FILE.Replace(Path.GetExtension(item.PHOTO_FILE), "_S" + Path.GetExtension(item.PHOTO_FILE)));
                                if (ImgUrl == "")
                                {
                                    ImgUrl = new ZZZI34Service().GetDirectorySysArtGalleryPath(item.PHOTO_SCHOOL_NO, item.ART_GALLERY_NO, item.PHOTO_FILE);


                                }
                            }

                            <img src="@ImgUrl" class="img-responsive" title="@item.PHOTO_SUBJECT" alt="@item.PHOTO_SUBJECT" />

                        }
                        @*else if (item.WORK_TYPE == ADDT21.WORK_TYPE_VAL.video)
                        {
                            string VideoUrl = new ZZZI34Service().GetDirectorySysArtGalleryPath(item.PHOTO_SCHOOL_NO, item.ART_GALLERY_NO, item.PHOTO_FILE);

                            <video class="Div-EZ-ArtGallery-img-all" controls>
                                <source src="@VideoUrl" type="video/mp4">
                                Your browser does not support the video tag.
                            </video>

                        }*@
                        else
                        {
                            <iframe src="@item.PHOTO_FILE"></iframe>
                        }

                        <div class="caption">
                            <h3>@item.PHOTO_SUBJECT</h3>
                            <p>@item.PHOTO_DESC </p>
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>

}

<script type="text/javascript">
    $('.element').responsiveEqualHeightGrid();
</script>