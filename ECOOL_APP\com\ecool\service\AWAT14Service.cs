﻿
using com.ecool.sqlConnection;
using Dapper;
using ECOOL_APP;
using ECOOL_APP.com.ecool.Models.DTO.AWAT14;
using ECOOL_APP.com.ecool.ResultService;
using ECOOL_APP.EF;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Transactions;

namespace com.ecool.service
{
   public class AWAT14Service
    {

        public IResult InsertAWAT15List(AWAT14SysSetIntervalViewModel model, UserProfile user, ref ECOOL_DEVEntities db) {

            ECOOL_DEVEntities db1 = new ECOOL_DEVEntities();
            IResult result = new Result(false);
            using (TransactionScope tx = new TransactionScope())
            {
                try
                {
                    List<AWAT14> AWAT14list = new List<AWAT14>();
                    int? CASHRANK = 0;
                    AWAT14list = db.AWAT14.Where(x => x.SCHOOL_NO == user.SCHOOL_NO).ToList();
                    int SYear;
                    int Semesters;

                    SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);
                    foreach (var item in AWAT14list)
                    {
                        AWAT01 aWAT01temp = new AWAT01();
                        aWAT01temp = db.AWAT01.Where(x => x.SCHOOL_NO == user.SCHOOL_NO && x.USER_NO == item.USER_NO).FirstOrDefault();
                        int ATM = 0;
                          ATM=  (db.AWAT10.Where(w => w.STATUS == "1 " && w.SCHOOL_NO == item.SCHOOL_NO && w.USER_NO == item.USER_NO)
                                               .Sum(w => (int?)w.AMT) ?? 0);
                        IQueryable<VAWAT15> AWAT15List = (from y in db.AWAT01

                                                          join aLog in
                        (from log in db.AWAT01_LOG
                         where 1 == 1
                         group log by new { log.SCHOOL_NO, log.USER_NO } into logGroup
                         select new
                         {
                             logGroup.Key.SCHOOL_NO,
                             logGroup.Key.USER_NO,
                             CASH_ALL = logGroup.Sum(x => x.ADD_CASH_ALL),
                             CASH_AVAILABLE = logGroup.Sum(x => x.ADD_CASH_AVAILABLE),
                             CASH_WORKHARD = logGroup.Sum(x => x.ADD_CASH_WORKHARD)
                         })

                           on new { y.SCHOOL_NO, y.USER_NO } equals new { aLog.SCHOOL_NO, aLog.USER_NO }
                         where y.SCHOOL_NO==item.SCHOOL_NO &&y.USER_NO == item.USER_NO
                         select new VAWAT15
                         {
                             CASH_ALL = (int)y.CASH_ALL,
                             SUMCASH_AVAILABLE = ((aLog.CASH_AVAILABLE < 0) ? (y.CASH_AVAILABLE ?? 0) : (aLog.CASH_AVAILABLE ?? 0))
                             + ATM


                         }
                         ).AsQueryable();

                        var mainQuery = from temp in AWAT15List
                                        select new VAWAT15
                                        {
                                            CASH_ALL = (int)(temp.SUMCASH_AVAILABLE > temp.CASH_ALL ? temp.SUMCASH_AVAILABLE : temp.CASH_ALL),

                                            SUMCASH_AVAILABLE = temp.SUMCASH_AVAILABLE,
                                        };
                        List<VAWAT15> AWAT15Listtemp= mainQuery.ToList();
                        int CASH_ALL = 0;
                        if (AWAT15Listtemp != null && AWAT15Listtemp.Count()!=0) {

                            CASH_ALL = AWAT15Listtemp.FirstOrDefault().CASH_ALL;

                        }
                                                    CASHRANK = CASH_ALL/ model.AWAT14_CASH;
                        HRMT01 hRMT01 = new HRMT01();
                        hRMT01 = db.HRMT01.Where(x => x.SCHOOL_NO == user.SCHOOL_NO && x.USER_NO == item.USER_NO && x.USER_STATUS == 1).FirstOrDefault();

                        AWAT15 wAT15 = db.AWAT15.Where(x => x.SCHOOL_NO == user.SCHOOL_NO && x.USER_NO == item.USER_NO && x.CASH_Rank == CASHRANK).FirstOrDefault();
                       
                        
                        if (hRMT01 != null) {

                            if (CASHRANK > 1)
                            {
                                for (int i = 1; i < CASHRANK; i++)
                                {
                                    AWAT15 wAT15Item = new AWAT15();
                                    wAT15 = db.AWAT15.Where(x => x.SCHOOL_NO == user.SCHOOL_NO && x.USER_NO == item.USER_NO && x.CASH_Rank == i).FirstOrDefault();

                                    if (wAT15 == null)
                                    {
                                        wAT15Item.USER_NO = item.USER_NO;
                                        wAT15Item.SCHOOL_NO = item.SCHOOL_NO;
                                        wAT15Item.CASH_Rank = (int)i;
                                        wAT15Item.CreatDate = DateTime.Now;
                                        wAT15Item.SYEAR = Convert.ToByte(SYear);
                                        wAT15Item.SEMESTER = Convert.ToByte(Semesters);
                                        wAT15Item.CLASS_NO = hRMT01.CLASS_NO;
                                        wAT15Item.SEAT_NO = hRMT01.SEAT_NO;
                                        wAT15Item.NAME = hRMT01.NAME;
                                        wAT15Item.SNAME = hRMT01.SNAME;
                                        wAT15Item.IsReMark = 0;
                                        wAT15Item.AWAT14_CASH = (int)model.AWAT14_CASH;
                                        db.AWAT15.Add(wAT15Item);



                                    }
                                    //else
                                    //{

                                    //    wAT15.AWAT14_CASH = (int)model.AWAT14_CASH;

                                    //}
                                }


                            }
                            else {
                                AWAT15 wAT15Item = new AWAT15();
                                if (wAT15 == null)
                                {
                                    wAT15Item.USER_NO = item.USER_NO;
                                    wAT15Item.SCHOOL_NO = item.SCHOOL_NO;
                                    wAT15Item.CASH_Rank = (int)CASHRANK;
                                    wAT15Item.CreatDate = DateTime.Now;
                                    wAT15Item.SYEAR = Convert.ToByte(SYear);
                                    wAT15Item.SEMESTER = Convert.ToByte(Semesters);
                                    wAT15Item.CLASS_NO = hRMT01.CLASS_NO;
                                    wAT15Item.SEAT_NO = hRMT01.SEAT_NO;
                                    wAT15Item.NAME = hRMT01.NAME;
                                    wAT15Item.SNAME = hRMT01.SNAME;
                                    wAT15Item.IsReMark = 0;
                                    wAT15Item.AWAT14_CASH = (int)model.AWAT14_CASH;
                                    db.AWAT15.Add(wAT15Item);



                                }
                                //else
                                //{

                                //    wAT15.AWAT14_CASH = (int)model.AWAT14_CASH;

                                //}

                            }
                       
                        }

                    }
                    result.Success = true;
                    db.SaveChanges();
                    tx.Complete();
                    return result;
                }
                catch (Exception ex)
                {
                    //logger.Error(ex);
                    result.Exception = ex;
                    result.Message += ex.Message;
                    return result;
                }
            }

        }

       
       public IResult SaveSysSetAWAT14Data(AWAT14SysSetIntervalViewModel model, UserProfile user, ref ECOOL_DEVEntities db)
        {
            IResult result = new Result(false);
            using (TransactionScope tx = new TransactionScope())
            {
                try
            {
             
                    BDMT01 bDMT01 = new BDMT01();
                    bDMT01 = db.BDMT01.Where(x => x.SCHOOL_NO == user.SCHOOL_NO).FirstOrDefault();
                    bDMT01.AWAT14_CASH = model.AWAT14_CASH;
                 
                    bDMT01 = db.BDMT01.Where(x => x.SCHOOL_NO == user.SCHOOL_NO).FirstOrDefault();

                    List<AWAT14> AWAT14list = new List<AWAT14>();
                    int? CASHRANK = 0;
                    AWAT14list = db.AWAT14.Where(x => x.SCHOOL_NO == user.SCHOOL_NO).ToList();
                   // SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);
                    foreach (var item in AWAT14list)
                    {

                        AWAT01 aWAT01temp = new AWAT01();
                        aWAT01temp = db.AWAT01.Where(x => x.SCHOOL_NO == user.SCHOOL_NO && x.USER_NO == item.USER_NO).FirstOrDefault();
                        int ATM = 0;
                        ATM = (db.AWAT10.Where(w => w.STATUS == "1 " && w.SCHOOL_NO == item.SCHOOL_NO && w.USER_NO == item.USER_NO)
                                             .Sum(w => (int?)w.AMT) ?? 0);
                        IQueryable<VAWAT15> AWAT15List = (from y in db.AWAT01

                                                          join aLog in
                        (from log in db.AWAT01_LOG
                         where 1 == 1
                         group log by new { log.SCHOOL_NO, log.USER_NO } into logGroup
                         select new
                         {
                             logGroup.Key.SCHOOL_NO,
                             logGroup.Key.USER_NO,
                             CASH_ALL = logGroup.Sum(x => x.ADD_CASH_ALL),
                             CASH_AVAILABLE = logGroup.Sum(x => x.ADD_CASH_AVAILABLE),
                             CASH_WORKHARD = logGroup.Sum(x => x.ADD_CASH_WORKHARD)
                         })

                           on new { y.SCHOOL_NO, y.USER_NO } equals new { aLog.SCHOOL_NO, aLog.USER_NO }
                                                          where y.SCHOOL_NO == item.SCHOOL_NO && y.USER_NO == item.USER_NO
                                                          select new VAWAT15
                                                          {
                                                              CASH_ALL = (int)y.CASH_ALL,
                                                              SUMCASH_AVAILABLE = ((aLog.CASH_AVAILABLE < 0) ? (y.CASH_AVAILABLE ?? 0) : (aLog.CASH_AVAILABLE ?? 0))
                                                              + ATM


                                                          }
                         ).AsQueryable();

                        var mainQuery = from AT15 in AWAT15List
                                        select new VAWAT15
                                        {
                                            CASH_ALL = (int)(AT15.SUMCASH_AVAILABLE > AT15.CASH_ALL ? AT15.SUMCASH_AVAILABLE : AT15.CASH_ALL),

                                            SUMCASH_AVAILABLE = AT15.SUMCASH_AVAILABLE,
                                        };
                        List<VAWAT15> AWAT15Listtemp = mainQuery.ToList();
                        int CASH_ALL = 0;
                        if (AWAT15Listtemp != null && AWAT15Listtemp.Count() != 0)
                        {

                            CASH_ALL = AWAT15Listtemp.FirstOrDefault().CASH_ALL;

                        }
                        CASHRANK = CASH_ALL / model.AWAT14_CASH;
                        HRMT01 hRMT01 = new HRMT01();
                        hRMT01 = db.HRMT01.Where(x => x.SCHOOL_NO == user.SCHOOL_NO && x.USER_NO == item.USER_NO && x.USER_STATUS == 1).FirstOrDefault();

                        AWAT14 wAT14 = db.AWAT14.Where(x => x.SCHOOL_NO == user.SCHOOL_NO && x.USER_NO == item.USER_NO ).FirstOrDefault();

                        AWAT14_LOG wAT14ItemLOg = new AWAT14_LOG();



                        wAT14.AWAT14_CASH = CASH_ALL;
                        wAT14.CASH_Rank = CASHRANK;

                        wAT14.ARRIVED_CASH = (((CASH_ALL / bDMT01.AWAT14_CASH) + 1) * bDMT01
                                   .AWAT14_CASH) - CASH_ALL;
                        wAT14.CHG_DATE = DateTime.Now;
                        wAT14ItemLOg.SCHOOL_NO = user.SCHOOL_NO;
                        wAT14ItemLOg.USER_NO = user.USER_NO;
                        wAT14ItemLOg.CASH_Rank = CASHRANK;
                        wAT14ItemLOg.CASH_ALL = CASH_ALL;
                        wAT14ItemLOg.ARRIVED_CASH = (((CASH_ALL / bDMT01.AWAT14_CASH) + 1) * bDMT01
                                   .AWAT14_CASH) - CASH_ALL;
                        wAT14ItemLOg.CHG_DATE= DateTime.Now;
                        wAT14ItemLOg.AWAT14_CASH = bDMT01
                                   .AWAT14_CASH;
                        db.AWAT14_LOG.Add(wAT14ItemLOg);
                    }
                    result.ModelItem = bDMT01;

                    //string sSQLD = $@"UPDATE awat14
                    //               SET awat14.AWAT14_CASH = bDMT01.AWAT14_CASH
                    //                  ,CASH_Rank=AWAT01.CASH_ALL/bDMT01.AWAT14_CASH
                    //                 ,ARRIVED_CASH=(AWAT01.CASH_ALL/bDMT01.AWAT14_CASH+1)*bDMT01
                    //               .AWAT14_CASH-AWAT01.CASH_ALL,CHG_DATE=getdate()
                    //               FROM AWAT14 awat14
                    //               JOIN AWAT01 AWAT01 ON awat14.USER_NO = AWAT01.USER_NO AND awat14.SCHOOL_NO = AWAT01.SCHOOL_NO
                    //               JOIN BDMT01 bDMT01 on  AWAT01.SCHOOL_NO = bDMT01.SCHOOL_NO where awat14.SCHOOL_NO=@SCHOOL_NO ";

                    //var temp = db.Database.Connection.Query(sSQLD
                    //      , new
                    //      {
                    //          SCHOOL_NO = user.SCHOOL_NO
                    //      });
                    //string sSQLD1 = $@"insert into AWAT14_LOG([SCHOOL_NO]
                    //                                  ,[USER_NO]
                    //                                  ,[CASH_Rank]
                    //                                  ,[CASH_ALL]
                    //                                  ,[ARRIVED_CASH]
                    //                                  ,[CHG_DATE]
                    //                                  ,[AWAT14_CASH])

                    //               select awat14.[SCHOOL_NO]
                    //                     ,awat14.[USER_NO],AWAT01.CASH_ALL/bDMT01.AWAT14_CASH,
                    //               AWAT01.CASH_ALL,(AWAT01.CASH_ALL/bDMT01.AWAT14_CASH+1)*bDMT01.AWAT14_CASH-AWAT01.CASH_ALL,getdate(),bDMT01.AWAT14_CASH
                                   
                    //               FROM AWAT14 awat14
                    //               JOIN AWAT01 AWAT01 ON awat14.USER_NO = AWAT01.USER_NO AND awat14.SCHOOL_NO = AWAT01.SCHOOL_NO
                    //               JOIN BDMT01 bDMT01 on  AWAT01.SCHOOL_NO = bDMT01.SCHOOL_NO where awat14.SCHOOL_NO=@SCHOOL_NO";

                    //var temp1 = db.Database.Connection.Query(sSQLD1
                    //   , new
                    //   {
                    //       SCHOOL_NO = user.SCHOOL_NO
                    //   });
                    List<AWAT15> ListAwat15 = new List<AWAT15>();
                    ListAwat15 = db.AWAT15.Where(x => x.SCHOOL_NO == user.SCHOOL_NO && x.IsReMark==0 && x.CASH_Rank== CASHRANK).ToList();
                    db.AWAT15.RemoveRange(ListAwat15);
                   
                    result.Success = true;
                    db.SaveChanges();
                    tx.Complete();
             
                return result;
            }
            catch (Exception ex)
            {
                //logger.Error(ex);
                result.Exception = ex;
                result.Message += ex.Message;
                return result;
            }
            }
        }
        public List<ADDV14> GetALLCash(string SCHOOL_NO, ref int Count,string wheresql,string orderbysql,int page, int pageSize)
        {

            List<ADDV14> list = new List<ADDV14>();
           string sql = $@"SELECT  SYEAR, CLASS_NO, SEAT_NO, SNAME, USERNAME, SCHOOL_NO, USER_NO, 
                   CASE WHEN SUMCASH_AVAILABLE > CASH_ALL THEN SUMCASH_AVAILABLE ELSE CASH_ALL END AS CASH_ALL, 
                   SUMCASH_AVAILABLE, LEVEL_DESC, DP_DATE, CASH_Rank
FROM      (SELECT  CONVERT(NVARCHAR(MAX), h.SYEAR) AS SYEAR, h.CLASS_NO, h.SEAT_NO, h.SNAME, h.NAME AS USERNAME, 
                                      h.SCHOOL_NO, h.USER_NO, a.CASH_ALL, 
                                      CASE WHEN a.CASH_AVAILABLE < 0 THEN ISNULL(xy.CASH_AVAILABLE, 0) ELSE ISNULL(a.CASH_AVAILABLE, 
                                      0) END + ISNULL
                                          ((SELECT  SUM(AMT) AS Expr1
                                            FROM       dbo.AWAT10
                                            WHERE    (SCHOOL_NO = h.SCHOOL_NO) AND (USER_NO = h.USER_NO) AND (STATUS = 1)), 0) 
                                      AS SUMCASH_AVAILABLE, '第' + CONVERT(NVARCHAR(MAX), x.CASH_Rank) + '級' AS LEVEL_DESC,
                                          (SELECT  TOP (1) CHG_DATE
                                           FROM       dbo.AWAT14_LOG AS l
                                           WHERE    (SCHOOL_NO = h.SCHOOL_NO) AND (USER_NO = h.USER_NO) AND (CASH_ALL = xy.CASH_ALL)
                                           ORDER BY CHG_DATE) AS DP_DATE, CONVERT(INT, x.CASH_Rank) AS CASH_Rank
                   FROM       dbo.AWAT14 AS x INNER JOIN
                                      dbo.HRMT01 AS h ON x.SCHOOL_NO = h.SCHOOL_NO AND x.USER_NO = h.USER_NO INNER JOIN
                                      dbo.AWAT01 AS xy ON h.SCHOOL_NO = xy.SCHOOL_NO AND h.USER_NO = xy.USER_NO INNER JOIN
                                          (SELECT  SCHOOL_NO, USER_NO, SUM(ADD_CASH_ALL) AS CASH_ALL, SUM(ADD_CASH_AVAILABLE) 
                                                              AS CASH_AVAILABLE, SUM(ADD_CASH_WORKHARD) AS CASH_WORKHARD
                                           FROM   dbo.AWAT01_LOG AS logGroup
                                           GROUP BY SCHOOL_NO, USER_NO) AS a ON x.SCHOOL_NO = a.SCHOOL_NO AND 
                                      x.USER_NO = a.USER_NO
                   WHERE   (h.USER_STATUS = '1') AND (h.USER_TYPE = 'S') AND h.SCHOOL_NO='";
            sql = sql + SCHOOL_NO + $@"' ) AS temp where 1=1 ";
            if (!string.IsNullOrWhiteSpace(wheresql))
            {
                sql = sql + wheresql;

            }
            string lastSql = $@"GROUP BY SYEAR, CLASS_NO, SEAT_NO, SNAME, USERNAME, SCHOOL_NO, USER_NO, CASH_ALL, SUMCASH_AVAILABLE, 
                   LEVEL_DESC, DP_DATE, CASH_Rank ";
            //if (!string.IsNullOrWhiteSpace(orderbysql))
            //{
            //    lastSql = lastSql + orderbysql;

            //}


            sql = sql + lastSql;
            string ThisError = "";
            DataTable dt;
            SqlConnection conn = null;
            com.ecool.sqlConnection.sqlConnection getConn = new com.ecool.sqlConnection.sqlConnection();
            conn = getConn.getConnection4Query();
            //var temp=conn.Query<ADDV14>(sql).ToList();
            dt = new sqlConnection.sqlConnection().executeQueryBSqlDataReaderOrderListPage(page, pageSize, sql, sql, orderbysql, ref Count, ref ThisError, orderbysql);
       //  dt = new sqlConnection.sqlConnection().executeQueryGetCash(SCHOOL_NO, ref Count);
            if (dt != null)
            {
                foreach (DataRow dr in dt.Rows)
                {

                    ADDV14 ReturnData = new ADDV14();
                    ReturnData.SYEAR = (dr["SYEAR"] == DBNull.Value ? "" : (string)dr["SYEAR"]);
                    ReturnData.CLASS_NO = (dr["CLASS_NO"] == DBNull.Value ? "" : (string)dr["CLASS_NO"]);
                    ReturnData.SEAT_NO = (dr["SEAT_NO"] == DBNull.Value ? "" : (string)dr["SEAT_NO"]);
                    ReturnData.SNAME = (dr["SNAME"] == DBNull.Value ? "" : (string)dr["SNAME"]);
                    ReturnData.USERNAME = (dr["USERNAME"] == DBNull.Value ? "" : (string)dr["USERNAME"]);
                    ReturnData.SCHOOL_NO = (dr["SCHOOL_NO"] == DBNull.Value ? "" : (string)dr["SCHOOL_NO"]);
                    ReturnData.USER_NO = (dr["USER_NO"] == DBNull.Value ? "" : (string)dr["USER_NO"]);
                    ReturnData.CASH_ALL = (dr["CASH_ALL"] == DBNull.Value ? 0 : (decimal)dr["CASH_ALL"]);
                    ReturnData.SUMCASH_AVAILABLE = (dr["SUMCASH_AVAILABLE"] == DBNull.Value ? 0 : (decimal)dr["SUMCASH_AVAILABLE"]);
                    ReturnData.LEVEL_DESC = (dr["LEVEL_DESC"] == DBNull.Value ? "" : (string)dr["LEVEL_DESC"]);
                    ReturnData.DP_DATE = (dr["DP_DATE"] == DBNull.Value ? DateTime.Now : (DateTime)dr["DP_DATE"]);
                    ReturnData.CASH_Rank = (dr["CASH_Rank"] == DBNull.Value ? 0 : (int)dr["CASH_Rank"]);
                    list.Add(ReturnData);
                }



            }
            return list;
            }
        public BDMT01 GetSchooLSingel(AWAT14SysSetIntervalViewModel model) {

            ECOOL_DEVEntities db = new ECOOL_DEVEntities();
            BDMT01 bdmt = new BDMT01();
            bdmt = db.BDMT01.Where(x => x.SCHOOL_NO == model.SCHOOL_NO).FirstOrDefault();
            return bdmt;

        }
        public AWAT14RankCount AWAT14RankCountDetail(AWAT14RankViewModel model, ref ECOOL_DEVEntities db) {
            AWAT14RankCount AwaRankCountItem = new AWAT14RankCount();

            string sSQLD = $@"select SCHOOL_NO=[SCHOOL_NO]
                                       ,USER_NO= [USER_NO]
                                       ,CASH_Rank=[CASH_Rank]
                                       ,ARRIVED_CASH=ARRIVED_CASH
                                       ,CLASS_NO=CLASS_NO
                                       ,[CHG_DATE]
                                       ,[AWAT14_CASH]
                                       ,CASH_ALL
                                       ,LEVEL_DESC
                                       ,NAME
                                       ,SEAT_NO 
                                       ,CASH_AVAILABLE
                                       ,SUMCASH_AVAILABLE
                                       ,CASH_DEPOSIT
                                       ,CASE when SUMCASH_AVAILABLE>CASH_ALL then SUMCASH_AVAILABLE else CASH_ALL end as CASH_ALL
                                    from(
                                    
                                    
                                            select SCHOOL_NO=[SCHOOL_NO]
                                            ,USER_NO= [USER_NO],
                                            CASH_Rank=[CASH_Rank]
                                            ,ARRIVED_CASH=ARRIVED_CASH
                                            ,CLASS_NO=CLASS_NO
                                            ,[CHG_DATE]
                                            ,[AWAT14_CASH]
                                            ,CASH_ALL
                                            ,LEVEL_DESC
                                            ,NAME
                                            ,SEAT_NO
                                            ,CASH_AVAILABLE = w1.CASH_AVAILABLE,
                                             SUMCASH_AVAILABLE=w1.CASH_AVAILABLE+isnull((
                                             select Sum(w.AMT) from AWAT10 w (nolock) where w.STATUS=1 and w.SCHOOL_NO=w1.SCHOOL_NO and w.USER_NO=w1.USER_NO and w.STATUS =1
                                             ),0),
                                             CASH_DEPOSIT = isnull((
                                              select Sum(w.AMT) from AWAT10 w (nolock) where w.STATUS=1 and w.SCHOOL_NO=w1.SCHOOL_NO and w.USER_NO=w1.USER_NO and w.STATUS =1
                                              ),0)
                        from (

                        SELECT a.[SCHOOL_NO] ,a.[USER_NO],[CASH_Rank] ,[ARRIVED_CASH],k.CLASS_NO

                                ,[CHG_DATE] ,a.[AWAT14_CASH] ,case when aLog.CASH_ALL<0 then b.CASH_ALL else aLog.CASH_ALL end as CASH_ALL , CASE  WHEN [CASH_Rank] >= 11 THEN '第11級以上' ELSE '第' + CAST([CASH_Rank] AS NVARCHAR(50)) + '級'
                                 END AS LEVEL_DESC ,k.NAME,k.SEAT_NO
		               ,case when  aLog.CASH_AVAILABLE <0 then b.CASH_AVAILABLE else aLog.CASH_AVAILABLE end as CASH_AVAILABLE
                          FROM AWAT14 a
                          inner join AWAT01  b on a.SCHOOL_NO=b.SCHOOL_NO and a.USER_NO=b.USER_NO
                          inner join HRMT01 k  on k.SCHOOL_NO =b.SCHOOL_NO and k.USER_NO =b.USER_NO
                          inner join
	                                 (
	                                 select SCHOOL_NO, USER_NO, SUM(ADD_CASH_ALL) as CASH_ALL, SUM(ADD_CASH_AVAILABLE) as CASH_AVAILABLE, SUM(ADD_CASH_WORKHARD) as CASH_WORKHARD
	                                 from AWAT01_LOG (nolock)
                                     where 1=1 
	                                 group by SCHOOL_NO, USER_NO
	                                 ) aLog
	                                 on k.SCHOOL_NO = aLog.SCHOOL_NO and k.USER_NO = aLog.USER_NO
                         where a.SCHOOL_NO =@SCHOOL_NO  and CASH_Rank >0  and k.USER_STATUS=1 and k.USER_TYPE='S'
                          " ;

            if (model.WhereRankNO == "第11級以上")
            {

                sSQLD = sSQLD + " and CASH_Rank >=11 ";
            }
            else {
               string RANKSTR= model.WhereRankNO.Replace("第", "").Replace("級", "");
                int RANKSTRNO = Int32.Parse(RANKSTR);
                sSQLD = sSQLD + " and CASH_Rank=" + RANKSTRNO;
            }

            sSQLD = sSQLD + "    )w1  )as temp";


            var temp = db.Database.Connection.Query<AWAT14PeopleEditList>(sSQLD
     , new
     {
         SCHOOL_NO = model.WhereSCHOOL_NO
     });
            if (model.OrdercColumn == "CLASS_NO")
            {
                if (model.SyntaxName == "ASC")
                {
                    temp = temp.OrderBy(X => X.CLASS_NO);
                }
                else
                {
                    temp = temp.OrderByDescending(a => a.CLASS_NO);
                }
            }
            else if (model.OrdercColumn == "SEAT_NO")
            {
                if (model.SyntaxName == "ASC")
                {
                    temp = temp.OrderBy(X => X.SEAT_NO);
                }

                else
                {
                    temp = temp.OrderByDescending(X => X.SEAT_NO);
                }
            }
            else if (model.OrdercColumn == "CASH_ALL")
            {
                if (model.SyntaxName == "ASC")
                {
                    temp = temp.OrderBy(X => X.CASH_ALL);
                }

                else
                {
                    temp = temp.OrderByDescending(X => X.CASH_ALL);
                }
            }
            else {
                temp = temp.OrderByDescending(X => X.CASH_ALL);
            }

            List<AWAT14PeopleEditList> AWAT14PeopleEditLists = new List<AWAT14PeopleEditList>();

            AWAT14PeopleEditLists = temp.ToList();
            AwaRankCountItem.AWAT14PeopleEditList = AWAT14PeopleEditLists;
            AwaRankCountItem.WhereSCHOOL_NO = model.WhereSCHOOL_NO;
            AwaRankCountItem.WhereRANKNO = model.WhereRankNO;
            return AwaRankCountItem;
        }

        public List<AWAT14RankCount> AWAT14RankCount(AWAT14RankLogViewModel model, ref ECOOL_DEVEntities db)
        {
            AWAT14RankCount awatCount = new AWAT14RankCount();
            string sSQLD = $@"
 
select RANK_DESC,BGCOLOR,AWAT14_CASH   ,COUNT(AWAT14_CASH) AS LEVEL_DESCCount,CASE  WHEN  RANKNO='第11級以上'then 12*(select AWAT14_CASH from BDMT01 where SCHOOL_NO=@SCHOOL_NO)-1

else ( (CAST(replace( replace( replace(RANK_DESC,'第',''),'級',''),'以上','') as int)+1)*(select AWAT14_CASH from BDMT01 where SCHOOL_NO=@SCHOOL_NO))-1  end as CASH_Rank 
 ,CASE  WHEN  RANKNO='第11級以上'then 11*(select AWAT14_CASH from BDMT01 where SCHOOL_NO=@SCHOOL_NO)
 else  (CAST(replace( replace( replace(RANK_DESC,'第',''),'級',''),'以上','') as int))*(select AWAT14_CASH from BDMT01 where SCHOOL_NO=@SCHOOL_NO)
 end as  CASH_Rank0

from (

select *from AWAT14_IMG img
left join  


(
    SELECT [CASH_Rank],
        CASE  WHEN [CASH_Rank] >= 11 THEN '第11級以上' ELSE '第' + CAST([CASH_Rank] AS NVARCHAR(50)) + '級'
        END AS RANKNO,bb.AWAT14_CASH
	 ,(select  top 1 BGCOLOR  from AWAT14_IMG where SCHOOL_NO='ALL'and  RANK_DESC=  CASE  WHEN [CASH_Rank] > 11 THEN '第11級以上' ELSE '第' + CAST([CASH_Rank] AS NVARCHAR(50)) + '級'
        END) as BGCOLORe
    FROM
        AWAT14  a 
		
		inner join BDMT01  bb on a.SCHOOL_NO= bb.SCHOOL_NO
	   inner join HRMT01 hr on hr.SCHOOL_NO=a.SCHOOL_NO and a.USER_NO=hr.USER_NO
		where a.SCHOOL_NO=@SCHOOL_NO  and CASH_Rank >0 and hr.USER_STATUS=1 and hr.USER_TYPE='S'
) kk on kk.RANKNO=img.RANK_DESC ) as gfdg GROUP BY RANK_DESC,AWAT14_CASH	,BGCOLOR ,RANKNO order by CAST(replace( replace( replace(RANK_DESC,'第',''),'級',''),'以上','') as int) desc";
            var temp = db.Database.Connection.Query<AWAT14RankCount>(sSQLD
      , new
      {
          SCHOOL_NO = model.WhereSCHOOL_NO
      });


            if (model.OrdercColumn == "CASH_Rank")
            {
                if (model.SyntaxName == "ASC")
                {
                    temp = temp.OrderBy(X => X.CASH_Rank);
                }
                else
                {
                    temp = temp.OrderByDescending(a => a.CASH_Rank);
                }
            }
            else if (model.OrdercColumn == "LEVEL_DESCCount")
            {
                if (model.SyntaxName == "ASC")
                {
                    temp = temp.OrderBy(X => X.LEVEL_DESCCount);
                }

                else
                {
                    temp = temp.OrderByDescending(X => X.LEVEL_DESCCount);
                }
            }
            List<AWAT14RankCount> AWAT14RankCountLists = new List<AWAT14RankCount>();

            AWAT14RankCountLists = temp.ToList();
            if (model.SyntaxName == "ASC")
            { model.SyntaxName = "DESC"; }

            else { model.SyntaxName = "ASC"; }
                return AWAT14RankCountLists;
        }
    }
}
