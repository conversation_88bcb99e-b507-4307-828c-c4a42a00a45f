﻿@model SECI05BorrowListIndexViewModel
@{
    ViewBag.Title = ViewBag.Panel_Title;
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
}
<link href="~/Content/css/EzCss.css" rel="stylesheet" />
<script src="~/Scripts/printThis/printThis.js"></script>
<script src="~/Scripts/timepicker/jquery-ui-sliderAccess.js"></script>
<link href="~/Scripts/timepicker/jquery-ui-timepicker-addon.css" rel="stylesheet" />
<script src="~/Scripts/timepicker/jquery-ui-timepicker-addon.js"></script>
<script src="~/Scripts/timepicker/jquery-ui-timepicker-zh-TW.js"></script>
<script src="~/Scripts/timepicker/jquery.ui.datepicker-zh-TW.js"></script>
@*<script src="~/Scripts/moment.js"></script>*@
<script src="~/Scripts/moment.min.js"></script>
@using (Html.BeginForm("BorrowList", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1" }))
{




<div style="padding-top:25px;width:95%;margin:0px auto;">
    @Html.Partial("_Title_Secondary")
    @Html.Partial("_Notice")


    <div class="form-inline">
        <div class="col-xs-12 text-right">
            <button type="button" class="btn btn-sm btn-sys" onclick="PrintBooK()">我要列印</button>
        </div>
    </div>
    <div class="form-inline" role="form" id="Q_Div">
        <div class="form-group">
            <label class="control-label">
                借書日期
            </label>
        </div>
        <div class="form-group">
            @Html.EditorFor(model => model.WhereBORROW_DATES, new { htmlAttributes = new { @class = "form-control", @type = "text" } })
        </div>
        <div class="form-group">
            ~ 
        </div>
        <div class="form-group">
            @Html.EditorFor(model => model.WhereBORROW_DATEE, new { htmlAttributes = new { @class = "form-control", @type = "text" } })
        </div>
        @Html.HiddenFor(model => model.OrdercColumn)
        @Html.HiddenFor(model => model.SyntaxName)
        @Html.HiddenFor(model => model.Page)
        @Html.HiddenFor(model => model.WhereBK_GRP)
        @Html.HiddenFor(model => model.WhereSEYEAR)
        @Html.HiddenFor(model => model.WhereSCHOOL_NO)
        @Html.HiddenFor(model => model.WhereUSER_NO)
        @Html.HiddenFor(model => model.IsRepeatBook)


        <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick=" form1.submit();" />
        <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />
    </div>
    <div style="height:10px"></div>

    <br />
    @if (user != null)
    {
        if (user.USER_TYPE == UserType.Admin || user.USER_TYPE == UserType.Teacher)
        {
            <div id="btnDiv">
                <div class="form-inline" style="text-align:left">
                    <div class="col-xs-12 text-right">
                        <button class="btn btn-xs btn-pink @(Model.IsRepeatBook == false ? "active":"")" type="button" onclick="DoIsRepeatBook('false')">全部</button>
                        <button class="btn btn-xs btn-pink @(Model.IsRepeatBook == true ? "active":"")" onclick="DoIsRepeatBook('true')" type="button">重複借書</button>
                    </div>
                </div>
            </div>
        }
    }
 

    <div id="tbData" class="row">
        <div class="col-sm-12 col-xs-12">
            <table class="table-ecool table-ecool-Bule-SEC">
                <thead>
                    <tr>

                        <th>
                            <samp class="form-group" style="cursor:pointer;" onclick="FunSort('BK_GRP')">
                                @Html.DisplayNameFor(m => m.ListData.First().BK_GRP)
                            </samp>
                        </th>
                        <th>
                            <samp class="form-group" style="cursor:pointer;" onclick="FunSort('BKNAME')">
                                @Html.DisplayNameFor(m => m.ListData.First().BKNAME)
                            </samp>
                        </th>
                        <th>
                            <samp class="form-group" style="cursor:pointer;" onclick="FunSort('TYPE_NAME')">
                                @Html.DisplayNameFor(m => m.ListData.First().TYPE_NAME)
                            </samp>
                        </th>
                        <th>
                            <samp class="form-group" style="cursor:pointer;" onclick="FunSort('BORROW_YN')">
                                @Html.DisplayNameFor(m => m.ListData.First().BORROW_YN)
                            </samp>
                        </th>
                        <th>
                            <samp class="form-group" style="cursor:pointer;" onclick="FunSort('BORROW_DATE')">
                                @Html.DisplayNameFor(m => m.ListData.First().BORROW_DATE)
                            </samp>
                        </th>
                        <th>
                            <samp class="form-group" style="cursor:pointer;" onclick="FunSort('DATE_RET')">
                                @Html.DisplayNameFor(m => m.ListData.First().DATE_RET)
                            </samp>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @if (Model.ListData != null)
                    {
                        foreach (var item in Model.ListData)
                        {
                            <tr class="text-center">
                                <td>
                                    @if (string.IsNullOrWhiteSpace(item.BK_GRP))
                                    {
                                       <font>未輸入</font>
                                    }
                                    else
                                    {
                                        @Html.DisplayFor(modelItem => item.BK_GRP)
                                    }
                                   

                                </td>
                                <td>

                                    @Html.DisplayFor(modelItem => item.BKNAME)

                                </td>
                                <td>

                                    @Html.DisplayFor(modelItem => item.TYPE_NAME)

                                </td>
                                <td>

                                    @Html.DisplayFor(modelItem => item.BORROW_YN)

                                </td>
                                <td>

                                    @Html.DisplayFor(modelItem => item.BORROW_DATE)

                                </td>
                                <td>
                                    @if (item.DATE_RET == DateTime.MinValue || item.DATE_RET == null)
                                    {
                                       <font color="red">未歸還</font>
                                    }
                                    else
                                    {
                                        @Html.DisplayFor(modelItem => item.DATE_RET)
                                    }
                                  

                                </td>
                            </tr>
                        }

                    }
                    else
                    {
                        <tr class="text-center">
                            <td colspan="4">無任何借書資料</td>
                        </tr>
                    }

                </tbody>
            </table>
        </div>
    </div>
</div>
}
<script language="javascript">

    

        var opt = {
        showMonthAfterYear: true,
        format: moment().format('YYYY-MM-DD h:mm:ss'),
        showSecond: true,
        showButtonPanel: true,
        showTime: true,
        beforeShow: function () {
            setTimeout(
                function () {
                    $('#ui-datepicker-div').css("z-index", 15);
                }, 100
            );
        },
        onSelect: function (dateText, inst) {
            $('#' + inst.id).attr('value', dateText);
        }
    };

    $("#@Html.IdFor(m => m.WhereBORROW_DATES)").datetimepicker(opt);
    $("#@Html.IdFor(m => m.WhereBORROW_DATEE)").datetimepicker(opt);

    function DoIsRepeatBook(Val)
    {
        $('#@Html.IdFor(m=>m.IsRepeatBook)').val(Val)
        form1.submit();
    }

    function PrintBooK() {
        $('#tbData').printThis();
    }


    function FunSort(SortName) {
        if (form1.OrdercColumn.value == SortName) {
            if (form1.SyntaxName.value == "Desc") {
                form1.SyntaxName.value = "ASC"
            }
            else {
                form1.SyntaxName.value = "Desc"
            }
        }
        else {
            form1.OrdercColumn.value = SortName;
            form1.SyntaxName.value = "Desc";
        }
        form1.submit();
    }

    function todoClear() {
        ////重設

        $("#Q_Div").find(":input,:selected").each(function (i) {

            var type = $(this).attr('type')
            var InPreadonly = $(this).attr('readonly')
            var tag = this.tagName.toLowerCase(); // normalize case

            if (InPreadonly == false || InPreadonly == undefined) {

                if (type == 'radio' || type == 'checkbox') {
                    if ($(this).attr("title") == 'Default') {
                        this.checked = true;
                    }
                    else {
                        this.checked = false;
                    }
                }
                else if (tag == 'select') { //下拉式選單
                    this.selectedIndex = 0;
                }
                else if (type == 'text' || type == 'hidden' || type == 'password' || type == 'textarea') {
                    this.value = '';
                }
            }
        });

        form1.submit();
    }
</script>