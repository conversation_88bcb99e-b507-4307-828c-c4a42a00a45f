/*
 *  /MathJax/jax/output/HTML-CSS/fonts/TeX/Caligraphic/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.MathJax_Caligraphic={directory:"Caligraphic/Regular",family:"MathJax_Caligraphic",testString:"MATHJAX CALIGRAPHIC",skew:{65:0.194,66:0.139,67:0.139,68:0.0833,69:0.111,70:0.111,71:0.111,72:0.111,73:0.0278,74:0.167,75:0.0556,76:0.139,77:0.139,78:0.0833,79:0.111,80:0.0833,81:0.111,82:0.0833,83:0.139,84:0.0278,85:0.0833,86:0.0278,87:0.0833,88:0.139,89:0.0833,90:0.139},32:[0,0,250,0,0],48:[452,22,500,39,460],49:[453,0,500,86,426],50:[453,0,500,44,449],51:[452,216,500,42,456],52:[464,194,500,28,471],53:[453,216,500,50,448],54:[665,22,500,42,456],55:[463,216,500,55,485],56:[666,21,500,43,456],57:[453,216,500,42,457],65:[728,50,798,30,819],66:[705,22,657,32,664],67:[705,25,527,12,533],68:[683,0,771,19,766],69:[705,22,528,30,564],70:[683,32,719,18,829],71:[704,119,595,44,599],72:[683,48,845,18,803],73:[683,0,545,-30,642],74:[683,119,678,47,839],75:[705,22,762,32,732],76:[705,22,690,32,656],77:[705,50,1201,28,1137],78:[789,50,820,-27,979],79:[705,22,796,58,777],80:[683,57,696,19,733],81:[705,131,817,114,787],82:[682,22,848,19,837],83:[705,22,606,18,642],84:[717,68,545,34,833],85:[683,28,626,-17,687],86:[683,52,613,25,658],87:[683,53,988,25,1034],88:[683,0,713,52,807],89:[683,143,668,31,714],90:[683,0,725,37,767],160:[0,0,250,0,0]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"MathJax_Caligraphic"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Caligraphic/Regular/Main.js"]);
