﻿@model List<ECOOL_APP.EF.AWAT06>
@{
    ViewBag.Title = "角色娃娃-我的角色娃娃";
    string ImageUrl = Url.Content(@"~/Content/Players/");

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    if (string.IsNullOrWhiteSpace(UUID) == false)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

<a class="btn btn-sm btn-sys" href='@Url.Action("Gallery","AWAI02")'>
    角色娃娃首頁
</a>

<img src="~/Content/img/web-bar2-revise-18.png" style="width:100%" class="img-responsive App_hide" alt="Responsive image" />
<div class="Div-EZ-AWAI02">
    <div class="Details">
        <div class="container">
            <div class="row show-grid" style="white-space: nowrap;">
                @if (Model.Count==0)
                {
                    <div class="row Div-btn-center">無任何角色娃娃</div>
                }


                @foreach (AWAT06 player in Model)
                {
                    string PlayerImageUrl = ImageUrl + player.IMG_FILE;
                    <div class="col-xs-6 col-sm-4" style="text-align:center">
                        <img src='@PlayerImageUrl' class="img-responsive" alt="Responsive image" />
                        <br />
                        @Html.ActionLink("【使用】", "Setdefault", new { PLAYER_NO = player.PLAYER_NO }, new { @class = "btn btn-sm btn-default" })
                    </div>
                }
            </div>
        </div>
    </div>
</div>
