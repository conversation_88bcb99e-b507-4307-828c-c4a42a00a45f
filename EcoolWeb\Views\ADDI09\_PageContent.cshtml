﻿
@model ECOOL_APP.com.ecool.Models.DTO.ADDI09ListViewViewModel
@using ECOOL_APP.com.ecool.Models.DTO;
@{
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    ECOOL_DEVEntities db = new ECOOL_DEVEntities();

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
}
<link href="~/Content/colorbox/example4/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>

@functions {
    private enum ActionMove
    {
        MODIFY,
        Delete,
        Delete1
    }
    /// <summary>
    /// [維護][作廢] => 導向URL設定
    /// </summary>
    /// <param name="act">導向Action名稱</param>
    /// <param name="SYS_TABLE_TYPE">資料表名稱</param>
    /// <param name="item">ADDT20物件</param>
    /// <returns></returns>
    private string UrlDirector(ActionMove act, string SYS_TABLE_TYPE, ADDT20_Extension item)
    {
        string controllerName = "", urlParameters = "";

        if (SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OTHER || SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OTHER_CLASS || SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_ADDT20_CLASS_BATCH)
        {
            controllerName = "ADDI09";
            urlParameters = $"?BATCH_CASH_ID={item.BATCH_CASH_ID}&NUM={item.NUM}";
        }
        else if (SYS_TABLE_TYPE== "ZZZI20"){

            controllerName = "ZZZI20";


        }
        else if (SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_IN || SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_ADDT14_CLASS_BATCH || SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_QUICKBULK)
        {
            controllerName = "ADDI06";
            urlParameters = $"?IAWARD_ID={item.IAWARD_ID}";
        }
        else if (SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OBC)
        {
            controllerName = "ADDI07";
            urlParameters = $"?OAWARD_ID={item.OAWARD_ID}";
        }
        return Url.Action(Enum.GetName(typeof(ActionMove), act), controllerName) + urlParameters;
    }

}



@Html.ActionLink("新增批次給點/扣點", (string)ViewBag.IndexActionName, null, new { @class = "btn btn-sm btn-sys", @role = "button" })

@Html.HiddenFor(model => model.Search.Page)
@Html.HiddenFor(model => model.Search.OrderByName)
@Html.HiddenFor(model => model.Search.SyntaxName)
@Html.HiddenFor(model => model.Search.BATCH_CASH_ID)
@Html.HiddenFor(model => model.Search.fromSOurce)
<br />

<div id="Q_DIV">
    <div class="form-inline" role="form">
        <div class="form-group">
            <label class="control-label">執行日期/學號/姓名/獎懲主旨</label>
        </div>
        <div class="form-group">
            @Html.EditorFor(model => model.Search.SearchContents, new { htmlAttributes = new { @class = "form-control" } })
        </div>
        <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1)" />
        <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />
    </div>
    <br />

    @if (ViewBag.IndexActionName == "SysIndex" && ViewBag.temfromSouce != "AWA002")
    {
        <div class="form-group">
            顯示： @Html.DropDownListFor(model => model.Search.SYS_TABLE_TYPE, (IEnumerable<SelectListItem>)ViewBag.SysTableTypeItem, new { @class = "input-sm", @style = "width:120px", @onchange = "FunPageProc(1)" })
        </div>
    }
    @if (ViewBag.temfromSouce == "AWA002")
    {

        <div class="form-group">
            顯示： @Html.DropDownListFor(model => model.Search.SYS_TABLE_TYPE, (IEnumerable<SelectListItem>)ViewBag.SysTableTypeItem, new { @class = "input-sm", @style = "width:120px;", @onchange = "FunPageProc(1)", @disabled = "disabled" })
        </div>
    }
</div>
@if (AppMode == false)
{
    <div class="row">
        <div class="col-xs-12 text-right">
            <button type="button" class="btn btn-sm btn-sys" onclick="PrintBooK()">我要列印</button>
            @if (!Model.WhereIsPassbook)
            {
                <button type="button" class="btn btn-sm btn-sys" onclick="ToExcel()">匯出excel</button>
            }
        </div>
    </div>
}



<img src="~/Content/img/web-bar2-revise-22.png" style="width:100%" class="img-responsive " alt="Responsive image" />
<div class="Div-EZ-ADDI09">
    <div class="form-horizontal">
        <div class="table-responsive">
            <table class="table-ecool table-hover">
                <thead>
                    <tr>
                        <th>

                            <samp class="form-group" style="cursor:pointer;" onclick="FunSort('@Model.ColName.EN_CRE_DATE')">
                                @Html.DisplayNameFor(model => model.ADDT20List.First().CRE_DATE)
                            </samp>
                        </th>
                        <th>

                            <samp class="form-group" style="cursor:pointer;" onclick="FunSort('@Model.ColName.EN_CRE_PERSON')">
                                @Html.DisplayNameFor(model => model.ADDT20List.First().CRE_PERSON)
                            </samp>
                        </th>
                        <th>

                            <samp class="form-group" style="cursor:pointer;" onclick="FunSort('USER_NO')">
                                @Html.DisplayNameFor(model => model.ADDT20List.First().USER_NO)
                            </samp>
                        </th>
                        <th>
                            <samp class="form-group" style="cursor:pointer;" onclick="FunSort('@Model.ColName.EN_NAME')">
                                @Html.DisplayNameFor(model => model.ADDT20List.First().NAME)
                            </samp>
                        </th>
                        <th>
                            <samp class="form-group" style="cursor:pointer;" onclick="FunSort('CLASS_NO')">
                                @Html.DisplayNameFor(model => model.ADDT20List.First().CLASS_NO)
                            </samp>
                        </th>

                        @if (Model.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OTHER
                            || Model.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OTHER_CLASS
                            || Model.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_QUICKBULK)
                        {
                            <th>
                                <samp class="form-group" style="cursor:pointer;" onclick="FunSort('SEAT_NO')">
                                    @Html.DisplayNameFor(model => model.ADDT20List.First().SEAT_NO)
                                </samp>
                            </th>
                        }
                        <th>

                            <samp class="form-group" style="cursor:pointer;" onclick="FunSort('@Model.ColName.EN_SUBJECT')">
                                @Model.ColName.CI_SUBJECT
                            </samp>
                        </th>
                        <th>
                        </th>
                        <th colspan="2" style="text-align:left">
                            <samp class="form-group" style="cursor:pointer;" onclick="FunSort('CASH')">
                                @Html.DisplayNameFor(model => model.ADDT20List.First().CASH)
                            </samp>
                        </th>
                        <th></th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var item in Model.ADDT20List)
                    {
                        <tr onclick="onBtnLink('@item.BATCH_CASH_ID','@item.SCHOOL_NO','@item.USER_NO','@item.NUM')" style="cursor:pointer">
                            <td align="center">
                                @Html.DisplayFor(modelItem => item.CRE_DATE)
                            </td>
                            @if (item.CRE_PERSON == "系統紙本酷幣點數")
                            {
                                ADDT38 adt038 = new ADDT38();
                                HRMT01 hRMT = new HRMT01();
                                adt038 = db.ADDT38.Where(x => x.SCHOOL_NO == item.SCHOOL_NO && x.USER_NO == item.USER_NO && x.BATCH_CASH_ID == item.BATCH_CASH_ID).FirstOrDefault();
                                hRMT = db.HRMT01.Where(x => x.SCHOOL_NO == item.SCHOOL_NO && x.USER_KEY == adt038.CRE_PERSON).FirstOrDefault();
                                <td align="center"> @hRMT.NAME   </td>
                            }
                            else
                            {
                                <td align="center">
                                    @Html.DisplayFor(modelItem => item.CRE_PERSON)
                                </td>}
                            <td align="center">
                                @Html.DisplayFor(modelItem => item.USER_NO)
                            </td>
                            <td align="center">
                                @Html.DisplayFor(modelItem => item.NAME)
                            </td>
                            <td align="center">
                                @Html.DisplayFor(modelItem => item.CLASS_NO)
                            </td>
                            @if (Model.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OTHER
                                || Model.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OTHER_CLASS
                                || Model.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_QUICKBULK)
                            {
                                <td align="center">
                                    @Html.DisplayFor(modelItem => item.SEAT_NO)
                                </td>
                            }
                            <td align="center">
                                @Html.DisplayFor(modelItem => item.SUBJECT)
                            </td>
                            <td align="center">
                                @if (!string.IsNullOrWhiteSpace(item.IMG_FILE))
                                {
                                    string Img_Path = string.Empty;
                                    if (Model.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OTHER || Model.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OTHER_CLASS)
                                    {
                                        Img_Path = new ECOOL_APP.FileHelper().GetDirectorySysPathImg(item.SCHOOL_NO, EcoolWeb.Controllers.ADDI09Controller.ImgPath, Path.Combine(item.BATCH_CASH_ID, item.USER_NO), item.IMG_FILE);
                                    }
                                    else if (Model.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_IN)
                                    {
                                        Img_Path = new ECOOL_APP.FileHelper().GetDirectorySysPathImg(item.SCHOOL_NO, EcoolWeb.Controllers.ADDI06Controller.ImgPath, item.NUM.ToString(), item.IMG_FILE);
                                    }
                                    else if (Model.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OBC)
                                    {
                                        Img_Path = new ECOOL_APP.FileHelper().GetDirectorySysPathImg(item.SCHOOL_NO, EcoolWeb.Controllers.ADDI07Controller.ImgPath, item.NUM.ToString(), item.IMG_FILE);
                                    }
                                    <img class="Img_Path" src="@Img_Path" style="max-height:30px" href="@Img_Path" />
                                }
                            </td>
                            <td align="center">
                                @Html.DisplayFor(modelItem => item.CASH)
                            </td>
                            <td style="text-align: left;white-space:nowrap">
                                @if (!(Model.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OTHER || Model.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OTHER_CLASS))
                                {

                                    if ((user.ROLE_LEVEL == (decimal)4.00 || user.USER_TYPE == "A" || user.NAME == item.CRE_PERSON) && Model.Search.SYS_TABLE_TYPE != "ZZZI20")
                                    {
                                        <a class="btn btn-xs btn-Basic" href="@UrlDirector(ActionMove.MODIFY, Model.Search.SYS_TABLE_TYPE, item)">維護</a><br />
                                        <a class="btn btn-xs btn-Basic" href="@UrlDirector(ActionMove.Delete, Model.Search.SYS_TABLE_TYPE, item)">作廢</a>
                                    }
                                    else if (Model.Search.SYS_TABLE_TYPE == "ZZZI20")
                                    {

                                        <a class="btn btn-xs btn-Basic" href="@Url.Action("Delete1", "ADDI09", new { USER_NO=item.USER_NO,NUM=""})">作廢</a>
                                    }

                                }
                                else if (user?.USER_TYPE != UserType.Student && user?.USER_TYPE != UserType.Parents)
                                {
                                    if (user.ROLE_LEVEL == (decimal)4.00 || user.USER_TYPE == "A" || user.NAME == item.CRE_PERSON)
                                    {
                                        <a class="btn btn-xs btn-Basic" href="@UrlDirector(ActionMove.Delete, Model.Search.SYS_TABLE_TYPE, item)">作廢</a>
                                    }

                                }

                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>

    </div>
    <div>
        @Html.Pager(Model.ADDT20List.PageSize, Model.ADDT20List.PageNumber, Model.ADDT20List.TotalItemCount).Options(o => o
         .DisplayTemplate("BootstrapPagination")
            .MaxNrOfPages(5)
            .SetPreviousPageText("上頁")
            .SetNextPageText("下頁")
    )
    </div>
</div>
<br />
<div class="text-center">
    @Html.ActionLink("返回", "SysIndex", (string)ViewBag.BRE_NO, htmlAttributes: new { @class = "btn btn-default" })
</div>


<script type="text/javascript">
    $(document).ready(function () {
        $(".Img_Path").colorbox({ opacity: 0.82, photo: true, maxWidth: '95%', maxHeight: '95%' });
    });

</script>






