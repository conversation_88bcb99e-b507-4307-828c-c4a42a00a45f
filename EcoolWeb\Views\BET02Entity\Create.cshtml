﻿@model ECOOL_APP.EF.BET02

@{
    ViewBag.Title = "Create";
}

<h2>Create</h2>


@using (Html.BeginForm()) 
{
    @Html.AntiForgeryToken()
    
    <div class="form-horizontal">
        <h4>BET02</h4>
        <hr />
        @Html.ValidationSummary(true, "", new { @class = "text-danger" })
        <div class="form-group">
            @Html.LabelFor(model => model.BULLET_ID, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.BULLET_ID, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.BULLET_ID, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.SCHOOL_NO, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.SCHOOL_NO, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.SCHOOL_NO, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.CLASS_TYPE, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.CLASS_TYPE, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.CLASS_TYPE, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.SUBJECT, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.SUBJECT, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.SUBJECT, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.CONTENT_TXT, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.CONTENT_TXT, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.CONTENT_TXT, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.ISPUBLISH, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.ISPUBLISH, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.ISPUBLISH, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.S_DATE, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.S_DATE, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.S_DATE, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.E_DATE, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.E_DATE, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.E_DATE, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.TOP_YN, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.TOP_YN, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.TOP_YN, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.CRE_PERSON, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.CRE_PERSON, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.CRE_PERSON, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.CRE_DATE, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.CRE_DATE, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.CRE_DATE, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.CHG_PERSON, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.CHG_PERSON, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.CHG_PERSON, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.CHG_DATE, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.CHG_DATE, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.CHG_DATE, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.MEMO, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.MEMO, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.MEMO, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            <div class="col-md-offset-2 col-md-10">
                <input type="submit" value="Create" class="btn btn-default" />
            </div>
        </div>
    </div>
}

<div>
    @Html.ActionLink("Back to List", "Index")
</div>

@section Scripts {
    @Scripts.Render("~/bundles/jqueryval")
}
