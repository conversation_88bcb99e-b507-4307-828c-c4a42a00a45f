﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class RollCallCashDetailViewModel
    {
        /// <summary>
        ///點名 ID
        /// </summary>
        [DisplayName("點名 ID")]
        public string ROLL_CALL_ID { get; set; }

        /// <summary>
        ///學校ID
        /// </summary>
        [DisplayName("學校ID")]
        public string SCHOOL_NO { get; set; }

        /// <summary>
        ///帳號
        /// </summary>
        [DisplayName("帳號")]
        public string USER_NO { get; set; }

        /// <summary>
        ///年級
        /// </summary>
        [DisplayName("年級")]
        public byte? GRADE { get; set; }

        [DisplayName("給點時間")]
        public Nullable<System.DateTime> CHG_DATE { get; set; }

        /// <summary>
        ///班級
        /// </summary>
        [DisplayName("班級")]
        public string CLASS_NO { get; set; }

        /// <summary>
        ///學年度
        /// </summary>
        [DisplayName("學年度")]
        public byte? SYEAR { get; set; }

        /// <summary>
        ///學期
        /// </summary>
        [DisplayName("學期")]
        public byte? SEMESTER { get; set; }

        /// <summary>
        ///姓名
        /// </summary>
        [DisplayName("姓名")]
        public string NAME { get; set; }

        /// <summary>
        ///簡稱
        /// </summary>
        [DisplayName("簡稱")]
        public string SNAME { get; set; }

        /// <summary>
        ///座號
        /// </summary>
        [DisplayName("座號")]
        public string SEAT_NO { get; set; }

        /// <summary>
        ///是否報到
        /// </summary>
        [DisplayName("是否報到")]
        public bool? IS_PRESENT { get; set; }

        /// <summary>
        ///點數
        /// </summary>
        [DisplayName("點數")]
        public short CASH { get; set; }

        public byte STATUS { get; set; }
    }
}