﻿using AutoMapper;
using <PERSON>pper;
using ECOOL_APP.com.ecool.ResultService;
using ECOOL_APP.com.ecool.util;
using ECOOL_APP.EF;
using EntityFramework.Extensions;
using EntityFramework.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Transactions;
using System.Web.Mvc;

namespace ECOOL_APP.com.ecool.service
{
    public class CERI05Service
    {
        /// <summary>
        /// 全校補登處理
        /// </summary>
        /// <param name="SCHOOL_NO"></param>
        /// <param name="user"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        public IResult SaveSchoolData(string SCHOOL_NO,string ThisACCREDITATION_ID, string ThisITEM_NO, UserProfile user, string CLASS_NO, ref ECOOL_DEVEntities db)
        {
            IResult result = new Result(false);

            try
            {
                using (TransactionScope tx = new TransactionScope())
                {
                    int SYear; int Semesters;
                    SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters); // 取得目前的Semester

                    db.Database.Connection.Execute("sp_SetSyearAccreditationSchoolRecordEntryData"
                    , new { SYear = SYear, Semesters = Semesters, SCHOOL_NO = SCHOOL_NO, User_SCHOOL_NO = user?.SCHOOL_NO, User_USER_NO = user?.USER_NO, User_USER_NAME = user?.NAME, ACCREDITATION_ID= ThisACCREDITATION_ID, ITEM_NO= ThisITEM_NO ,CLASS_NO= CLASS_NO }
                    , commandType: CommandType.StoredProcedure);

                    tx.Complete();
                }
                result.Success = true;
            }
            catch (Exception ex)
            {
                result.Exception = ex;
                result.Message += ex.Message;
                return result;
            }

            return result;
        }

        public IResult SaveSchoolDataItem(string SCHOOL_NO, string ThisACCREDITATION_ID, string ThisITEM_NO, UserProfile user, ref ECOOL_DEVEntities db)
        {
            IResult result = new Result(false);

            try
            {
                using (TransactionScope tx = new TransactionScope())
                {
                    int SYear; int Semesters;
                    SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters); // 取得目前的Semester

                    db.Database.Connection.Execute("sp_SetSyearAccreditationSchoolRecordEntryDataItem"
                    , new { SYear = SYear, Semesters = Semesters, SCHOOL_NO = SCHOOL_NO, User_SCHOOL_NO = user?.SCHOOL_NO, User_USER_NO = user?.USER_NO, User_USER_NAME = user?.NAME }
                    , commandType: CommandType.StoredProcedure);

                    tx.Complete();
                }
                result.Success = true;
            }
            catch (Exception ex)
            {
                result.Exception = ex;
                result.Message += ex.Message;
                return result;
            }

            return result;
        }
        public bool GetCERT05(string SCHOOL_NO, string ThisACCREDITATION_ID, string ThisITEM_NO, UserProfile user, ref ECOOL_DEVEntities db)
        {

            int GetCERT05Count = 0;
            bool GetCERT05CountBool = true;
            int SYear; int Semesters;
            SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters); // 取得目前的Semester
            string sSQL = $@"Select g.ACCREDITATION_ID,g.ITEM_NO
							
                            from(select a.ACCREDITATION_ID,a.ITEM_NO,a.GRADE GRADE
                            from CERT03_G a (nolock)
                            where a.SCHOOL_NO=@SCHOOL_NO
                            group by a.ACCREDITATION_ID,a.ITEM_NO,a.GRADE) g
                            join HRMT01 b (nolock) on  (  ( b.GRADE>=g.GRADE))
                            join CERT02 t02 (nolock) on g.ACCREDITATION_ID = t02.ACCREDITATION_ID and t02.DEL_YN='N'
                            join CERT02_D t02D (nolock)on g.ACCREDITATION_ID =t02D.ACCREDITATION_ID and g.ITEM_NO=t02D.ITEM_NO
                            left join CERT05 c (nolock) on g.ACCREDITATION_ID =c.ACCREDITATION_ID and g.ITEM_NO =c.ITEM_NO and b.SCHOOL_NO=c.SCHOOL_NO and b.USER_NO=c.USER_NO
                            where b.SCHOOL_NO=@SCHOOL_NO and b.USER_TYPE='S' and b.USER_STATUS!=9 and g.ACCREDITATION_ID =@ACCREDITATION_ID and t02D.ACCREDITATION_ID =@ACCREDITATION_ID 
							and  g.ITEM_NO=@ITEM_NO  and t02D.ITEM_NO=@ITEM_NO 
                            and c.USER_NO is null
							group by  g.ACCREDITATION_ID,g.ITEM_NO,b.SCHOOL_NO,b.GRADE,b.CLASS_NO,g.GRADE";
            var temp = db.Database.Connection.Query<CERI05EditAccreditationtViewModel>(sSQL
             , new
             {
                 SCHOOL_NO = user?.SCHOOL_NO,
                 Semesters= Semesters,
                 ACCREDITATION_ID = ThisACCREDITATION_ID,
                 ITEM_NO= ThisITEM_NO
             }).AsQueryable();
            GetCERT05Count = temp.Count();
            if (GetCERT05Count == 0) {
                GetCERT05CountBool = false;

            }
            return GetCERT05CountBool;
        }
        /// <summary>
        /// 個人補登查詢學生列表
        /// </summary>
        /// <param name="model"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        public CERI05PersonalViewModel GetUserListData(CERI05PersonalViewModel model, ref ECOOL_DEVEntities db)
        {
            //預設值
            int SYear;
            int Semesters;
            SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);

            var temp = db.HRMT01.Where(a => a.SCHOOL_NO == model.WhereSCHOOL_NO && a.USER_STATUS != UserStaus.Invalid && a.USER_TYPE == UserType.Student);

            if (model.WhereGRADE != null)
            {
                temp = temp.Where(a => a.GRADE == model.WhereGRADE);
            }

            if (!string.IsNullOrWhiteSpace(model.WhereCLASS_NO))
            {
                temp = temp.Where(a => a.CLASS_NO == model.WhereCLASS_NO);
            }

            if (!string.IsNullOrWhiteSpace(model.WhereUser))
            {
                temp = temp.Where(a => a.NAME.Contains(model.WhereUser.Trim()) || a.USER_NO.Contains(model.WhereUser.Trim()));
            }

            if (!string.IsNullOrWhiteSpace(model.OrderByColumnName) && model.OrderByColumnName != nameof(HRMT01.SEAT_NO))
            {
                temp = temp.OrderBy(model.OrderByColumnName, model.SortType);
            }
            else if (model.OrderByColumnName == nameof(HRMT01.SEAT_NO))
            {
                if (model.SortType == PageGlobal.SortType.DESC)
                {
                    temp = temp.OrderBy(a => a.SCHOOL_NO).ThenBy(a => a.CLASS_NO).ThenByDescending(a => a.SEAT_NO);
                }
                else
                {
                    temp = temp.OrderBy(a => a.SCHOOL_NO).ThenBy(a => a.CLASS_NO).ThenBy(a => a.SEAT_NO);
                }
            }
            else
            {
                temp = temp.OrderBy(a => a.SCHOOL_NO).ThenBy(a => a.CLASS_NO).ThenBy(a => a.SEAT_NO);
            }

            model.ListData = temp.ToEzPagedList(model.Page, model.PageSize);

            model.Page = model.ListData.PageNumber;

            return model;
        }

        /// <summary>
        /// 個人補登 學生認證明細
        /// </summary>
        /// <param name="model"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        public CERI05PersonalEditViewModel GetPersonalEditData(CERI05PersonalEditViewModel model, ref ECOOL_DEVEntities db)
        {
            using (var txn = Queries.GetNewReadUncommittedScope())
            {
                model.MyData = db.HRMT01.Where(a => a.SCHOOL_NO == model.ThisSCHOOL_NO && a.USER_NO == model.ThisUSER_NO).FirstOrDefault();

                string sSQL = $@" select b.ACCREDITATION_ID, c.TYPE_NAME, a.ACCREDITATION_NAME, b.SUBJECT, b.CONTENT,b.ITEM_NO
                                ,(Case When isnull(p.USER_NO,'')<>'' Then 1 else 0 End) O_IS_PASS
                                ,(Case When isnull(p.USER_NO,'')<>'' Then 1 else 0 End) IS_PASS
                                    ,'ISTIME' as states 

                         	,g.SCHOOL_NO
						
                                from CERT02 a(nolock)
                                join CERT02_D b(nolock) on a.ACCREDITATION_ID = b.ACCREDITATION_ID
	                              join CERT03_G g on g.ACCREDITATION_ID=b.ACCREDITATION_ID  and g.ITEM_NO=b.ITEM_NO and g.SCHOOL_NO=@SCHOOL_NO
                                      join HRMT01 k on k.SCHOOL_NO=@SCHOOL_NO and k.USER_NO= @USER_NO
                                join CERT01 c (nolock) on a.ACCREDITATION_TYPE = c.TYPE_ID and c.DEL_YN='N'
                                left join CERT05 p (nolock) on p.ACCREDITATION_ID=b.ACCREDITATION_ID and p.ITEM_NO= b.ITEM_NO and p.SCHOOL_NO= @SCHOOL_NO and p.USER_NO=@USER_NO
                                	OUTER apply dbo.udf_Split(P.PersonText,',') sb     
							   where a.SCHOOL_NO = @SCHOOL_NO and a.DEL_YN='N'  and ( k.GRADE>=g.GRADE)  group by c.TYPE_NAME,a.ACCREDITATION_NAME,b.SUBJECT,b.ACCREDITATION_ID, b.CONTENT,p.USER_NO,b.ITEM_NO	,g.SCHOOL_NO, sb.word
                               	union ALL
                                	select b.ACCREDITATION_ID, c.TYPE_NAME, a.ACCREDITATION_NAME, b.SUBJECT, b.CONTENT,b.ITEM_NO
                                ,(Case When isnull(p.USER_NO,'')<>'' Then 1 else 0 End) O_IS_PASS
                                ,(Case When isnull(p.USER_NO,'')<>'' Then 1 else 0 End) IS_PASS
                                ,'NO' as states
                           	,g.SCHOOL_NO
							
                                from CERT02 a(nolock)
                                join CERT02_D b(nolock) on a.ACCREDITATION_ID = b.ACCREDITATION_ID
	                              join CERT03_G g on g.ACCREDITATION_ID=b.ACCREDITATION_ID  and g.ITEM_NO=b.ITEM_NO and g.SCHOOL_NO=@SCHOOL_NO
                                      join HRMT01 k on k.SCHOOL_NO= @SCHOOL_NO and k.USER_NO= @USER_NO
                                join CERT01 c (nolock) on a.ACCREDITATION_TYPE = c.TYPE_ID and c.DEL_YN='N'
                                left join CERT05 p (nolock) on p.ACCREDITATION_ID=b.ACCREDITATION_ID and p.ITEM_NO= b.ITEM_NO and p.SCHOOL_NO= @SCHOOL_NO and p.USER_NO=@USER_NO
                                          	OUTER apply dbo.udf_Split(P.PersonText,',') sb  
							   where a.SCHOOL_NO =  @SCHOOL_NO and a.DEL_YN='N'  and ( k.GRADE<g.GRADE)  
								
						          and  b.ACCREDITATION_ID+'_'+b.ITEM_NO not in (
								   
								   select b.ACCREDITATION_ID+'_'+b.ITEM_NO 
                                from CERT02 a(nolock)
                                join CERT02_D b(nolock) on a.ACCREDITATION_ID = b.ACCREDITATION_ID
	                              join CERT03_G g on g.ACCREDITATION_ID=b.ACCREDITATION_ID  and g.ITEM_NO=b.ITEM_NO and g.SCHOOL_NO=@SCHOOL_NO
                                      join HRMT01 k on k.SCHOOL_NO= @SCHOOL_NO and k.USER_NO= @USER_NO
                                join CERT01 c (nolock) on a.ACCREDITATION_TYPE = c.TYPE_ID and c.DEL_YN='N'
                                left join CERT05 p (nolock) on p.ACCREDITATION_ID=b.ACCREDITATION_ID and p.ITEM_NO= b.ITEM_NO and p.SCHOOL_NO= @SCHOOL_NO and p.USER_NO=@USER_NO
                       
							  where a.SCHOOL_NO =  @SCHOOL_NO and a.DEL_YN='N'  and ( k.GRADE>=g.GRADE)  group by c.TYPE_NAME,a.ACCREDITATION_NAME,b.SUBJECT,b.ACCREDITATION_ID, b.CONTENT,p.USER_NO,b.ITEM_NO	,g.SCHOOL_NO 
								   
								   
								   )
								group by c.TYPE_NAME,a.ACCREDITATION_NAME,b.SUBJECT,b.ACCREDITATION_ID, b.CONTENT,p.USER_NO,b.ITEM_NO	,g.SCHOOL_NO, sb.word
                                order by c.TYPE_NAME,a.ACCREDITATION_NAME,b.SUBJECT";
                var temp = db.Database.Connection.Query<CERI05EditAccreditationtViewModel>(sSQL
                 , new
                 {
                     SCHOOL_NO = model.ThisSCHOOL_NO,
                     USER_NO = model.ThisUSER_NO,
                 }).AsQueryable();

                model.Accreditationts = temp.ToList();
            }

            return model;
        }
        /// <summary>
        /// 個人補登 學生認證明細
        /// </summary>
        /// <param name="model"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        public CERI05PersonalEditViewModel GetPersonalEditData1(CERI05PersonalEditViewModel model, ref ECOOL_DEVEntities db)
        {
            using (var txn = Queries.GetNewReadUncommittedScope())
            {
                model.MyData = db.HRMT01.Where(a => a.SCHOOL_NO == model.ThisSCHOOL_NO && a.USER_NO == model.ThisUSER_NO).FirstOrDefault();

                string sSQL = $@" select b.ACCREDITATION_ID, c.TYPE_NAME, a.ACCREDITATION_NAME, b.SUBJECT, b.CONTENT,b.ITEM_NO
                                ,(Case When isnull(p.USER_NO,'')<>'' Then 1 else 0 End) O_IS_PASS
                                ,(Case When isnull(p.USER_NO,'')<>'' Then 1 else 0 End) IS_PASS
                                    ,'ISTIME' as states 

                         	,g.SCHOOL_NO
							   , sb.word as PersonText
                                from CERT02 a(nolock)
                                join CERT02_D b(nolock) on a.ACCREDITATION_ID = b.ACCREDITATION_ID
	                              join CERT03_G g on g.ACCREDITATION_ID=b.ACCREDITATION_ID  and g.ITEM_NO=b.ITEM_NO and g.SCHOOL_NO=@SCHOOL_NO
                                      join HRMT01 k on k.SCHOOL_NO=@SCHOOL_NO and k.USER_NO= @USER_NO
                                join CERT01 c (nolock) on a.ACCREDITATION_TYPE = c.TYPE_ID and c.DEL_YN='N'
                                left join CERT05 p (nolock) on p.ACCREDITATION_ID=b.ACCREDITATION_ID and p.ITEM_NO= b.ITEM_NO and p.SCHOOL_NO= @SCHOOL_NO and p.USER_NO=@USER_NO
                                	OUTER apply dbo.udf_Split(P.PersonText,',') sb     
							   where a.SCHOOL_NO = @SCHOOL_NO and a.DEL_YN='N'  and ( k.GRADE>=g.GRADE)  group by c.TYPE_NAME,a.ACCREDITATION_NAME,b.SUBJECT,b.ACCREDITATION_ID, b.CONTENT,p.USER_NO,b.ITEM_NO	,g.SCHOOL_NO, sb.word
                               	union ALL
                                	select b.ACCREDITATION_ID, c.TYPE_NAME, a.ACCREDITATION_NAME, b.SUBJECT, b.CONTENT,b.ITEM_NO
                                ,(Case When isnull(p.USER_NO,'')<>'' Then 1 else 0 End) O_IS_PASS
                                ,(Case When isnull(p.USER_NO,'')<>'' Then 1 else 0 End) IS_PASS
                                ,'NO' as states
                           	,g.SCHOOL_NO
								   , sb.word as PersonText
                                from CERT02 a(nolock)
                                join CERT02_D b(nolock) on a.ACCREDITATION_ID = b.ACCREDITATION_ID
	                              join CERT03_G g on g.ACCREDITATION_ID=b.ACCREDITATION_ID  and g.ITEM_NO=b.ITEM_NO and g.SCHOOL_NO=@SCHOOL_NO
                                      join HRMT01 k on k.SCHOOL_NO= @SCHOOL_NO and k.USER_NO= @USER_NO
                                join CERT01 c (nolock) on a.ACCREDITATION_TYPE = c.TYPE_ID and c.DEL_YN='N'
                                left join CERT05 p (nolock) on p.ACCREDITATION_ID=b.ACCREDITATION_ID and p.ITEM_NO= b.ITEM_NO and p.SCHOOL_NO= @SCHOOL_NO and p.USER_NO=@USER_NO
                                          	OUTER apply dbo.udf_Split(P.PersonText,',') sb  
							   where a.SCHOOL_NO =  @SCHOOL_NO and a.DEL_YN='N'  and ( k.GRADE<g.GRADE)  
								
						          and  b.ACCREDITATION_ID+'_'+b.ITEM_NO not in (
								   
								   select b.ACCREDITATION_ID+'_'+b.ITEM_NO 
                                from CERT02 a(nolock)
                                join CERT02_D b(nolock) on a.ACCREDITATION_ID = b.ACCREDITATION_ID
	                              join CERT03_G g on g.ACCREDITATION_ID=b.ACCREDITATION_ID  and g.ITEM_NO=b.ITEM_NO and g.SCHOOL_NO=@SCHOOL_NO
                                      join HRMT01 k on k.SCHOOL_NO= @SCHOOL_NO and k.USER_NO= @USER_NO
                                join CERT01 c (nolock) on a.ACCREDITATION_TYPE = c.TYPE_ID and c.DEL_YN='N'
                                left join CERT05 p (nolock) on p.ACCREDITATION_ID=b.ACCREDITATION_ID and p.ITEM_NO= b.ITEM_NO and p.SCHOOL_NO= @SCHOOL_NO and p.USER_NO=@USER_NO
                       
							  where a.SCHOOL_NO =  @SCHOOL_NO and a.DEL_YN='N'  and ( k.GRADE>=g.GRADE)  group by c.TYPE_NAME,a.ACCREDITATION_NAME,b.SUBJECT,b.ACCREDITATION_ID, b.CONTENT,p.USER_NO,b.ITEM_NO	,g.SCHOOL_NO 
								   
								   
								   )
								group by c.TYPE_NAME,a.ACCREDITATION_NAME,b.SUBJECT,b.ACCREDITATION_ID, b.CONTENT,p.USER_NO,b.ITEM_NO	,g.SCHOOL_NO, sb.word
                                order by c.TYPE_NAME,a.ACCREDITATION_NAME,b.SUBJECT";
                var temp = db.Database.Connection.Query<CERI05EditAccreditationtViewModel>(sSQL
                 , new
                 {
                     SCHOOL_NO = model.ThisSCHOOL_NO,
                     USER_NO = model.ThisUSER_NO,
                 }).AsQueryable();

                model.Accreditationt1s = temp.ToList();
            }

            return model;
        }
        public IResult SavePersonalEditData(CERI05PersonalEditViewModel model, UserProfile user, ref ECOOL_DEVEntities db, ref List<Tuple<string, string, int>> valuesList)
        {
            IResult result = new Result(false);

            try
            {
                var Hr = db.HRMT01.Where(a => a.SCHOOL_NO == model.ThisSCHOOL_NO && a.USER_NO == model.ThisUSER_NO).NoLock(x => x.FirstOrDefault());

                var CashData = (from a in db.CERT01
                                join b in db.CERT02 on a.TYPE_ID equals b.ACCREDITATION_TYPE
                                join c in db.CERT02_D on b.ACCREDITATION_ID equals c.ACCREDITATION_ID
                                where a.DEL_YN == SharedGlobal.N && a.SCHOOL_NO == Hr.SCHOOL_NO && b.DEL_YN == SharedGlobal.N
                                select new { c.ACCREDITATION_ID, c.ITEM_NO, b.ACCREDITATION_NAME, c.SUBJECT, a.TYPE_NAME, a.CASH }).ToListNoLock();

                var oCERT05s = db.CERT05.Where(a => a.SCHOOL_NO == Hr.SCHOOL_NO && a.USER_NO == Hr.USER_NO).ToListNoLock();

                using (TransactionScope tx = new TransactionScope())
                {
                    int SYear = SysHelper.GetNowSYear(DateTime.Now);

                    List<CERT04_V_M> cERT04VMs = new List<CERT04_V_M>();
                    List<CERT04_V_M> cERT04VMModifys = new List<CERT04_V_M>();
                    List<CERT04_V_D> cERT04VDs = new List<CERT04_V_D>();
                    List<CERT04_V_D> cERT04VDsModifys = new List<CERT04_V_D>();
                    List<CERT05> CrePass = new List<CERT05>();
                    int ACCREDITATIONCount = 0;
                    foreach (var ThisItem in model.Accreditationts)
                    {
                        if ((ThisItem.O_IS_PASS ?? false) != (ThisItem.IS_PASS ?? false))
                        {
                            ACCREDITATIONCount = CashData.Where(a => a.ACCREDITATION_ID == ThisItem.ACCREDITATION_ID && a.ITEM_NO == ThisItem.ITEM_NO).Count();
                            if (ACCREDITATIONCount > 0) {
                               var CashDataItem= CashData.Where(a => a.ACCREDITATION_ID == ThisItem.ACCREDITATION_ID && a.ITEM_NO == ThisItem.ITEM_NO).FirstOrDefault();
                               var CERT05Item = db.CERT05.Where(x=>x.SCHOOL_NO== user.SCHOOL_NO && x.USER_NO== model.ThisUSER_NO && x.ACCREDITATION_ID== ThisItem.ACCREDITATION_ID && x.ITEM_NO == ThisItem.ITEM_NO).FirstOrDefault();
                               var VerF= (from k in db.CERT04_V_D
                                 join s in db.CERT04_V_M on k.A_VERIFIER_ID equals s.A_VERIFIER_ID
                                 where k.SCHOOL_NO == user.SCHOOL_NO && k.USER_NO == model.ThisUSER_NO && s.ACCREDITATION_ID == ThisItem.ACCREDITATION_ID && s.ITEM_NO == ThisItem.ITEM_NO
                                 select new
                                 {k.A_VERIFIER_ID }).FirstOrDefault();
                                CERT04_V_M CERT04_V_MI = null;
                                if (VerF != null)
                                {
                                    CERT04_V_MI = db.CERT04_V_M.Where(x => x.A_VERIFIER_ID == VerF.A_VERIFIER_ID && x.ACCREDITATION_ID == ThisItem.ACCREDITATION_ID && x.ITEM_NO == ThisItem.ITEM_NO).FirstOrDefault();

                                }
                                else {
                                    CERT04_V_MI = new CERT04_V_M();

                                    CERT04_V_MI.A_VERIFIER_ID = Guid.NewGuid().ToString("N");
                                    CERT04_V_MI.ACCREDITATION_ID = ThisItem.ACCREDITATION_ID;
                                    CERT04_V_MI.ITEM_NO = ThisItem.ITEM_NO;
                                    CERT04_V_MI.VERIFIER_SCHOOL_NO = user.SCHOOL_NO;
                                    CERT04_V_MI.VERIFIER_USER_NO = user.USER_NO;
                                    CERT04_V_MI.VERIFIER_NAME = user.NAME;
                                    CERT04_V_MI.VERIFIED_DATE = DateTime.Now;
                                    CERT04_V_MI.SCHOOL_NO = Hr.SCHOOL_NO;
                                    CERT04_V_MI.GRADE = Hr.GRADE;
                                    CERT04_V_MI.CLASS_NO = Hr.CLASS_NO;
                                    CERT04_V_MI.SYEAR = (byte)SYear;
                                    CERT04_V_MI.CHG_PERSON = user?.USER_KEY;
                                    CERT04_V_MI.CHG_DATE = DateTime.Now;
                                }
                           
                                CERT04_V_MI.CHG_PERSON = user?.USER_KEY;

                                CERT04_V_MI.CHG_DATE = DateTime.Now;
                                if (ThisItem.IS_PASS ?? false && CERT05Item == null)
                                {
                                    if (!(ThisItem.IS_PASS ?? false))
                                    {
                                        CERT04_V_MI.CHG_MEMO = "個人補登(取消)";
                                    }
                                    else {
                                        CERT04_V_MI.CHG_MEMO = "個人補登";
                                    }
                                      
                                }
                                else
                                {
                                    if (! (ThisItem.IS_PASS ?? false))
                                    {
                                        CERT04_V_MI.CHG_MEMO = "個人補登(取消)";
                                    }
                                    else {
                                        CERT04_V_MI.CHG_MEMO = "個人補登";
                                    }
                                   
                                }
                                if (VerF != null)
                                {

                                    cERT04VMModifys.Add(CERT04_V_MI);
                                    db.Entry(CERT04_V_MI).State = System.Data.Entity.EntityState.Modified;
                                }
                                else {
                                    cERT04VMs.Add(CERT04_V_MI);
                                    db.CERT04_V_M.Add(CERT04_V_MI);
                                }
                                var ThisCashData = CashData.Where(a => a.ACCREDITATION_ID == CERT04_V_MI.ACCREDITATION_ID && a.ITEM_NO == CERT04_V_MI.ITEM_NO).FirstOrDefault();
                                CERT04_V_D CreD = new CERT04_V_D();
                                if (VerF != null)
                                {
                                    CreD = db.CERT04_V_D.Where(x => x.A_VERIFIER_ID == VerF.A_VERIFIER_ID && x.SCHOOL_NO == user.SCHOOL_NO && x.USER_NO == model.ThisUSER_NO).FirstOrDefault();

                                }
                                else {
                                    CreD.A_VERIFIER_ID = CERT04_V_MI.A_VERIFIER_ID;
                                    CreD.SCHOOL_NO = Hr.SCHOOL_NO;
                                    CreD.USER_NO = Hr.USER_NO;
                                    CreD.O_IS_PASS = ThisItem.O_IS_PASS ?? false;
                                    CreD.IS_PASS = ThisItem.IS_PASS;
                                }
                                
                                CreD.SCHOOL_NO = Hr.SCHOOL_NO;
                                CreD.USER_NO = Hr.USER_NO;
                                CreD.O_IS_PASS = ThisItem.O_IS_PASS ?? false;
                                CreD.IS_PASS = ThisItem.IS_PASS??false;
                                

                                if (VerF != null)
                                {

                                    cERT04VDsModifys.Add(CreD);
                                    db.Entry(CreD).State = System.Data.Entity.EntityState.Modified;
                                }
                                else
                                {
                                    cERT04VDs.Add(CreD);
                                    db.CERT04_V_D.Add(CreD);
                                }
                                if (CreD.IS_PASS ?? false)
                                {
                                    if (CERT05Item == null)
                                    {
                                        if (! (ThisItem.IS_PASS ?? false))
                                        {


                                        }
                                        else {
                                            CERT05 CreP = new CERT05();
                                            CreP.ACCREDITATION_ID = ThisItem.ACCREDITATION_ID;
                                            CreP.ITEM_NO = ThisItem.ITEM_NO;
                                            CreP.SCHOOL_NO = Hr.SCHOOL_NO;
                                            CreP.USER_NO = Hr.USER_NO;
                                            CreP.CASH = ThisCashData?.CASH ?? 0;
                                            CrePass.Add(CreP);
                                            db.CERT05.Add(CreP);
                                            if (CreP.CASH > 0)
                                            {
                                                if (VerF != null)
                                                {
                                                
                                                    ECOOL_APP.CashHelper.AddCash(user, (short)CreP.CASH, Hr.SCHOOL_NO, Hr.USER_NO, "CERT05", VerF.A_VERIFIER_ID
                                                    , $"護照:{ThisCashData.TYPE_NAME}-{ThisCashData.ACCREDITATION_NAME}({ThisCashData.SUBJECT})  通過，獲得 {ThisCashData.CASH} 點", true, ref db,"", "",ref valuesList);

                                                }
                                                else
                                                {

                                                    ECOOL_APP.CashHelper.AddCash(user, (short)CreP.CASH, Hr.SCHOOL_NO, Hr.USER_NO, "CERT05", CERT04_V_MI.A_VERIFIER_ID
                                                  , $"護照:{ThisCashData.TYPE_NAME}-{ThisCashData.ACCREDITATION_NAME}({ThisCashData.SUBJECT})  通過，獲得 {ThisCashData.CASH} 點", true, ref db,"", "", ref valuesList);

                                                }

                                            }

                                        }
                                      
                                    }
                                    else {
                                        //if (CERT05Item.CASH > 0)
                                       // {
                                            
                                           
                                            if ((ThisItem.O_IS_PASS ?? false) != (ThisItem.IS_PASS ?? false))
                                            {

                                                ECOOL_APP.CashHelper.AddCash(user, (int)CERT05Item.CASH, Hr.SCHOOL_NO, Hr.USER_NO, "CERT05", CERT04_V_MI.A_VERIFIER_ID, $"取消 原「護照:{ThisCashData.TYPE_NAME}-{ThisCashData.ACCREDITATION_NAME}({ThisCashData.SUBJECT}) 通過」，扣 {CERT05Item.CASH } 點", true, ref db,"","", ref valuesList);
                                            CERT05 CERT05ID = new CERT05();
                                          CERT05ID = db.CERT05.Where(b => b.ACCREDITATION_ID == CERT04_V_MI.ACCREDITATION_ID && b.ITEM_NO == CERT04_V_MI.ITEM_NO && b.SCHOOL_NO == Hr.SCHOOL_NO && b.USER_NO == Hr.USER_NO).FirstOrDefault();
                                            if (CERT05ID != null) {

                                                db.Entry(CERT05ID).State = System.Data.Entity.EntityState.Deleted;


                                            }

                                        
                                            //EFBatchOperation.For(db, db.CERT05).Where(b => b.ACCREDITATION_ID == CERT04_V_MI.ACCREDITATION_ID && b.ITEM_NO == CERT04_V_MI.ITEM_NO
                                            //    && b.SCHOOL_NO == Hr.SCHOOL_NO && b.USER_NO == Hr.USER_NO).Delete();
                                            }
                                            else {


                                                if (VerF != null)
                                                {
                                                    ECOOL_APP.CashHelper.AddCash(user, (short)CERT05Item.CASH, Hr.SCHOOL_NO, Hr.USER_NO, "CERT05", VerF.A_VERIFIER_ID
                                                    , $"護照:{ThisCashData.TYPE_NAME}-{ThisCashData.ACCREDITATION_NAME}({ThisCashData.SUBJECT})  通過，獲得 {ThisCashData.CASH} 點", true, ref db,"","", ref valuesList);
                                                }
                                                else
                                                {

                                                    ECOOL_APP.CashHelper.AddCash(user, (short)CERT05Item.CASH, Hr.SCHOOL_NO, Hr.USER_NO, "CERT05", CERT04_V_MI.A_VERIFIER_ID
                                                  , $"護照:{ThisCashData.TYPE_NAME}-{ThisCashData.ACCREDITATION_NAME}({ThisCashData.SUBJECT})  通過，獲得 {ThisCashData.CASH} 點", true, ref db,"", "", ref valuesList);
                                                }
                                            }
                                        //}
                                        
                                    }
                                 
                                   
                                }
                                else
                                {
                                    var oThisCERT05 = oCERT05s.Where(x => x.ACCREDITATION_ID == CERT04_V_MI.ACCREDITATION_ID && x.ITEM_NO == CERT04_V_MI.ITEM_NO).FirstOrDefault();

                                    if (oThisCERT05 != null)
                                    {
                                        short O_CASH = (short)(oThisCERT05.CASH * -1);

                                        if (O_CASH <= 0)
                                        {
                                           
                                            ECOOL_APP.CashHelper.AddCash(user, O_CASH, Hr.SCHOOL_NO, Hr.USER_NO, "CERT05", CERT04_V_MI.A_VERIFIER_ID, $"取消 原「護照:{ThisCashData.TYPE_NAME}-{ThisCashData.ACCREDITATION_NAME}({ThisCashData.SUBJECT}) 通過」，扣 {O_CASH} 點", true, ref db,"", "", ref valuesList);
                                            CERT05 CERT05ID = new CERT05();
                                            CERT05ID = db.CERT05.Where(b => b.ACCREDITATION_ID == CERT04_V_MI.ACCREDITATION_ID && b.ITEM_NO == CERT04_V_MI.ITEM_NO
                                            && b.SCHOOL_NO == Hr.SCHOOL_NO && b.USER_NO == Hr.USER_NO).FirstOrDefault();
                                            if (CERT05ID != null)
                                            {
                                                db.Entry(CERT05ID).State = System.Data.Entity.EntityState.Deleted;



                                            }
                                            //EFBatchOperation.For(db, db.CERT05).Where(b => b.ACCREDITATION_ID == CERT04_V_MI.ACCREDITATION_ID && b.ITEM_NO == CERT04_V_MI.ITEM_NO
                                            //&& b.SCHOOL_NO == Hr.SCHOOL_NO && b.USER_NO == Hr.USER_NO).Delete();
                                        }
                                    }
                                }
                            }
                            else
                            {
                                CERT04_V_M SaveUp = new CERT04_V_M();
                                SaveUp.A_VERIFIER_ID = Guid.NewGuid().ToString("N");
                                SaveUp.ACCREDITATION_ID = ThisItem.ACCREDITATION_ID;
                                SaveUp.ITEM_NO = ThisItem.ITEM_NO;
                                SaveUp.VERIFIER_SCHOOL_NO = user.SCHOOL_NO;
                                SaveUp.VERIFIER_USER_NO = user.USER_NO;
                                SaveUp.VERIFIER_NAME = user.NAME;
                                SaveUp.VERIFIED_DATE = DateTime.Now;
                                SaveUp.SCHOOL_NO = Hr.SCHOOL_NO;
                                SaveUp.GRADE = Hr.GRADE;
                                SaveUp.CLASS_NO = Hr.CLASS_NO;
                                SaveUp.SYEAR = (byte)SYear;
                                SaveUp.CHG_PERSON = user?.USER_KEY;
                                SaveUp.CHG_DATE = DateTime.Now;

                                if (ThisItem.IS_PASS ?? false)
                                {
                                    SaveUp.CHG_MEMO = "個人補登";
                                }
                                else
                                {
                                    SaveUp.CHG_MEMO = "個人補登(取消)";
                                }

                                cERT04VMs.Add(SaveUp);
                                db.CERT04_V_M.Add(SaveUp);
                           CERT04_V_D CreD = new CERT04_V_D();
                                CreD.A_VERIFIER_ID = SaveUp.A_VERIFIER_ID;
                                CreD.SCHOOL_NO = Hr.SCHOOL_NO;
                                CreD.USER_NO = Hr.USER_NO;
                                CreD.O_IS_PASS = ThisItem.O_IS_PASS ?? false;
                                CreD.IS_PASS = ThisItem.IS_PASS;

                                cERT04VDs.Add(CreD);
                                db.CERT04_V_D.Add(CreD);
                                var ThisCashData = CashData.Where(a => a.ACCREDITATION_ID == SaveUp.ACCREDITATION_ID && a.ITEM_NO == SaveUp.ITEM_NO).FirstOrDefault();
                                if (CreD.IS_PASS ?? false)
                                {
                                    CERT05 CreP = new CERT05();
                                    CreP.ACCREDITATION_ID = SaveUp.ACCREDITATION_ID;
                                    CreP.ITEM_NO = SaveUp.ITEM_NO;
                                    CreP.SCHOOL_NO = Hr.SCHOOL_NO;
                                    CreP.USER_NO = Hr.USER_NO;
                                    CreP.CASH = ThisCashData?.CASH ?? 0;
                                    CrePass.Add(CreP);
                                    db.CERT05.Add(CreP);
                                    if (CreP.CASH > 0)
                                    {
                                        ECOOL_APP.CashHelper.AddCash(user, (short)CreP.CASH, Hr.SCHOOL_NO, Hr.USER_NO, "CERT05", SaveUp.A_VERIFIER_ID
                                            , $"護照:{ThisCashData.TYPE_NAME}-{ThisCashData.ACCREDITATION_NAME}({ThisCashData.SUBJECT})  通過，獲得 {ThisCashData.CASH} 點", true, ref db,"", "", ref valuesList);
                                    }
                                }
                                else
                                {
                                    var oThisCERT05 = oCERT05s.Where(x => x.ACCREDITATION_ID == SaveUp.ACCREDITATION_ID && x.ITEM_NO == SaveUp.ITEM_NO).FirstOrDefault();

                                    if (oThisCERT05 != null)
                                    {
                                        short O_CASH = (short)(oThisCERT05.CASH * -1);

                                        if (O_CASH < 0)
                                        {
                                            ECOOL_APP.CashHelper.AddCash(user, O_CASH, Hr.SCHOOL_NO, Hr.USER_NO, "CERT05", SaveUp.A_VERIFIER_ID, $"取消 原「護照:{ThisCashData.TYPE_NAME}-{ThisCashData.ACCREDITATION_NAME}({ThisCashData.SUBJECT}) 通過」，扣 {O_CASH} 點", true, ref db,"", "",ref valuesList);
                                            CERT05 CERT05ID = new CERT05();
                                            CERT05ID = db.CERT05.Where(b => b.ACCREDITATION_ID == SaveUp.ACCREDITATION_ID && b.ITEM_NO == SaveUp.ITEM_NO
                                            && b.SCHOOL_NO == Hr.SCHOOL_NO && b.USER_NO == Hr.USER_NO).FirstOrDefault();
                                            if (CERT05ID != null)
                                            {
                                                db.Entry(CERT05ID).State = System.Data.Entity.EntityState.Deleted;



                                            }
                                            //EFBatchOperation.For(db, db.CERT05).Where(b => b.ACCREDITATION_ID == SaveUp.ACCREDITATION_ID && b.ITEM_NO == SaveUp.ITEM_NO
                                            //&& b.SCHOOL_NO == Hr.SCHOOL_NO && b.USER_NO == Hr.USER_NO).Delete();
                                        }
                                    }
                                }
                            }







                         
                        }
                    }

                    if (CrePass?.Count > 0)
                    {
                 
                     //  EFBatchOperation.For(db, db.CERT04_V_M).InsertAll(cERT04VMs);
                       // EFBatchOperation.For(db, db.CERT04_V_D).InsertAll(cERT04VDs);
                     //   EFBatchOperation.For(db, db.CERT05).InsertAll(CrePass);
                    }
                    //if (cERT04VMModifys.Count() > 0) {
                        
                    //    EFBatchOperation.For(db, db.CERT04_V_M).UpdateAll(cERT04VMModifys,C=>C.ColumnsToUpdate(x=>x.CHG_DATE , x=>x.CHG_MEMO , x=>x.CHG_PERSON));
                    //    if (cERT04VDsModifys.Count() > 0) { 
                    //    EFBatchOperation.For(db, db.CERT04_V_D).UpdateAll(cERT04VDsModifys, C => C.ColumnsToUpdate(x => x.SCHOOL_NO, x => x.USER_NO, x => x.O_IS_PASS, x => x.IS_PASS));
                    //    }
                    //}
                    try
                    {
                        db.SaveChanges();
                    }
                    catch (Exception ex)
                    {
                        result.Exception = ex;
                        result.Message += ex.Message;
                        return result;
                    }

                    tx.Complete();
                }

                result.Success = true;
            }
            catch (Exception ex)
            {
                result.Exception = ex;
                result.Message += ex.Message;
                return result;
            }

            return result;
        }
    }
}