﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.com.ecool.Models.DTO
{
    public class BookFlowTimeViewModel
    {
        [Display(Name = "學校")]
        public string Where_SCHOOLNO { get; set; }
        [Display(Name ="學年度")]
        public string Where_SYEAR { get; set; }
        [Display(Name = "月份")]
        public string Where_Month { get; set; }
        public List<TimeOfBook> TimeOfBookBorrowList { get; set; }
        public List<TimeOfBook> TimeOfBookReturnList { get; set; }
    }

  
    public class TimeOfBook
    {
        [Display(Name = "時間(點)")]
        public DateTime HOUR { get; set; }
        [Display(Name = "書本流動量")]
        public int COUNT { get; set; }
    }
}
