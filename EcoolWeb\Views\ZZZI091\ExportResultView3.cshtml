﻿@model ZZZI09ExportResultViewViewModel

@{
    ECOOL_DEVEntities db = new ECOOL_DEVEntities();
    Layout = "~/Views/Shared/_LayoutNewEmpty.cshtml";
    string DateNowStr = DateTime.Now.ToString("yyyy_MM_dd_H_mm_ss");
    string UserDomain = HttpContext.Current.Request.Url.Scheme + "://" + HttpContext.Current.Request.Url.Host + ":" + HttpContext.Current.Request.Url.Port;
    var titelName = "";
    HRMT01 st = db.HRMT01.Where(a => a.SCHOOL_NO == Model.School_No && a.USER_NO == Model.User_No).FirstOrDefault();
    if (Model.Where_SCHOOLNO_FORADMIN != null && Model.School_No != Model.Where_SCHOOLNO_FORADMIN && string.IsNullOrEmpty(Model.IDNO))
    {

        HRMT01 st1 = db.HRMT01.Where(a => a.SCHOOL_NO == Model.Where_SCHOOLNO_FORADMIN && a.USER_NO == st.USER_NO).FirstOrDefault();
        st = db.HRMT01.Where(a => a.SCHOOL_NO == Model.School_No && a.IDNO == st1.IDNO).FirstOrDefault();

    }
    if (Model.Where_SCHOOLNO_FORADMIN != null && Model.School_No != Model.Where_SCHOOLNO_FORADMIN && !string.IsNullOrEmpty(Model.IDNO))
    {

        HRMT01 st1 = db.HRMT01.Where(a => a.SCHOOL_NO == Model.School_No && a.IDNO == Model.IDNO).FirstOrDefault();
        st = db.HRMT01.Where(a => a.SCHOOL_NO == Model.School_No && a.IDNO == st1.IDNO).FirstOrDefault();

    }
    titelName = st.USER_NO + st.SNAME;

}
@*<script src="https://code.jquery.com/jquery-2.1.3.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/1.3.3/jspdf.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>*@

@section head{

    <link href="@<EMAIL>("~/Content/styles/learning-history.min.css")?v=@DateNowStr" rel="stylesheet" />
}
<div class="fixed-btn d-print-none">
    <button type="button" class="btn btn-primary btn-sm mb-1 btn-html5" id="btnSaveHtml">
        另存 HTML
    </button>
    <br>
    <button type="button" class="btn btn-primary btn-sm float-right btn-print" id="btnPrint">
        列印
    </button>
</div>



<div id="print" class="container">
    <section class="cover" title="封面">
        <img src="@Model.COVERJPGFileName" alt="">
    </section>
    <section class="print-page" title="蝴蝶頁">
        <strong class="slogan">臺北e酷幣 留住你的童年回憶</strong>
    </section>

    @*個人資料*@
    @Html.Action("ShowSportsView", new { WhereSCHOOL_NO = st.SCHOOL_NO, WhereUSER_NO = st.USER_NO, LogimUser=ViewBag.LogimUser })

    @if (string.IsNullOrWhiteSpace(Model.IDNO))
    {
        <section class="row px-4 mb-4 record">
            <div class="col-12">
                @Html.Action("_PersonalDiv1", "SECI01", new { wSCHOOL_NO = st.SCHOOL_NO, wUSER_NO = st.USER_NO, wDATA_ANGLE_TYPE = EcoolWeb.Models.UserProfileHelper.AngleVal.OneData, LogimUser = ViewBag.LogimUser })
            </div>
        </section>
    }

    @*線上*@
    @Html.Action("Details3", new { School_No = st.SCHOOL_NO, User_No = st.USER_NO, S_DATE = Model.S_DATE, E_DATE = Model.E_DATE, IDNO = Model.IDNO, LogimUser = ViewBag.LogimUser })

    @*藝廊*@
    @Html.Action("ShowADDT22View", new { School_No = st.SCHOOL_NO, User_No = st.USER_NO, S_DATE = Model.S_DATE, E_DATE = Model.E_DATE, IDNO = Model.IDNO})

    @*閱讀*@
    @if (Model.ReadYN != "true")
    {
       
                @Html.Action("ADDTALLListDetails3", new { School_No = st.SCHOOL_NO, User_No = st.USER_NO, S_DATE = Model.S_DATE, E_DATE = Model.E_DATE, IDNO = Model.IDNO, LogimUser = ViewBag.LogimUser })
           
    }
    @*校內*@
   
            @Html.Action("ShowADDT14View", new { School_No = st.SCHOOL_NO, User_No = st.USER_NO, S_DATE = Model.S_DATE, E_DATE = Model.E_DATE, IDNO = Model.IDNO, LogimUser = ViewBag.LogimUser })
   

    @*校外*@

            @Html.Action("ShowADDT15View", new { School_No = st.SCHOOL_NO, User_No = st.USER_NO, S_DATE = Model.S_DATE, E_DATE = Model.E_DATE, IDNO = Model.IDNO, LogimUser = ViewBag.LogimUser })
    
    @*閱讀偏食*@
  
            @Html.Action("_SEI05Partial1", new { WhereSCHOOL_NO = st.SCHOOL_NO, WhereUSER_NO = st.USER_NO, WhereCLASS_NO = Model.Class_NO, LogimUser = ViewBag.LogimUser })
       
    @*數位存摺*@
    @*@Html.Action("ShowAWAT01_LOGView", new { School_No = Model.School_No, User_No = st.USER_NO, S_DATE = Model.S_DATE, E_DATE = Model.E_DATE, IDNO = Model.IDNO })*@

    @*點數統計*@

            @Html.Action("ShowCashPreView", new { School_No = Model.School_No, User_No = st.USER_NO, S_DATE = Model.S_DATE, E_DATE = Model.E_DATE, IDNO = Model.IDNO, LogimUser = ViewBag.LogimUser })
        
    @*健康*@
   
            @Html.Action("ShowHrmt08View", new { whereSCHOOL_NO = Model.School_No, whereUSER_NO = st.USER_NO, whereIDNO = Model.IDNO, LogimUser = ViewBag.LogimUser })
    

    @*專長護照*@

    @*神秘任務*@


    @Html.Action("ShowADDT25_TREFView", new { whereSCHOOL_NO = Model.School_No, whereUSER_NO = st.USER_NO, S_DATE = Model.S_DATE, E_DATE = Model.E_DATE, IDNO = Model.IDNO, LogimUser = ViewBag.LogimUser })



    @Html.Action("ShowCERT05View", new { School_No = Model.School_No, User_No = st.USER_NO, fromWhere = "Export", LogimUser = ViewBag.LogimUser })


</div>
@*<div id="download"></div>*@


@section scripts{
    @* 線上投稿-瀑布流圖片排版 *@
    <script src="@Url.Content("~/Scripts/imagesloaded.pkgd.min.js")"></script>
    <script>
   
        function resizeGridItem(item) {
            grid = document.getElementsByClassName('waterfall-imgs')[0];
            rowHeight = parseInt(window.getComputedStyle(grid).getPropertyValue('grid-auto-rows'));
            rowGap = parseInt(window.getComputedStyle(grid).getPropertyValue('grid-row-gap'));
            rowSpan = Math.ceil((item.querySelector('.waterfall-item img').clientHeight + rowGap) / (rowHeight + rowGap));
            item.setAttribute(`grid-fr`, rowSpan);
        }
        //$(document).ready(function () {

          
        //});
        function print_html(){
   var random = new Date().getTime();
   var pdf = new jsPDF('p','pt','a4');
   // 設置輸出比例 數值越大比列越小
   pdf.internal.scaleFactor = 2;
   var options = {
      pagesplit: true, //設置是否自動分頁
      "background": '#ffffff'   //如果導出的pdf為黑色背景，需要將導出的html模塊內容背景 設置成白色。
   };
   var printHtml = $('#download').get(0);   // 頁面某一個div裏面的內容，通過id獲取div內容
   pdf.addHTML(printHtml,15, 15, options,function() {
      pdf.save('htmltopdf_'+random+'.pdf');
      parent.closeLoading();
   });
}
        function resizeAllGridItems() {
            allItems = document.getElementsByClassName("waterfall-item");
            for (x = 0; x < allItems.length; x++) {
                resizeGridItem(allItems[x]);
            }
        }

        function resizeInstance(instance) {
            item = instance.elements[0];
            resizeGridItem(item);
        }
        if (document.querySelectorAll('.waterfall-item img')) {
            window.onload = resizeAllGridItems();
            window.addEventListener('resize', resizeAllGridItems);

            allItems = document.getElementsByClassName('waterfall-item');
            for (x = 0; x < allItems.length; x++) {
                imagesLoaded(allItems[x], resizeInstance);
            }
        }
    </script>
    @* 圖表-閱讀類別統計\個人借閱數量趨示圖\各類別酷幣點數表(在校生)\身高趨勢圖\體重趨勢圖\體適能趨勢圖-800公尺跑走\體適能趨勢圖-仰臥起坐\體適能趨勢圖-立定跳遠\體適能趨勢圖-坐姿體前彎 *@
    <script src="@Url.Content("~/Scripts/Highcharts-4.0.1/js/highcharts.js")"></script>
    <script src="@Url.Content("~/Scripts/Highcharts-4.0.1/js/modules/exporting.js")"></script>
    @* 另存HTML *@
    <script src="@Url.Content("~/Scripts/FileSaver.min.js")"></script>
    <script>

        //另存HTML
        function saveHtmlDoc() {
            var blob = new Blob([$("html").html()], {
                type: "html/plain;charset=utf-8"
            });
            var strFile = "@ViewBag.Panel_Title"+".html";
            saveAs(blob, strFile);
            return false;
        }
        let BtnSave = document.getElementById('btnSaveHtml');
        BtnSave.addEventListener('click', function () {
            saveHtmlDoc();
        });
        //列印
        function onPrintDoc() {
            window.print();
        }
        let BtnPrint = document.getElementById('btnPrint');
        BtnPrint.addEventListener('click', function () {
            onPrintDoc();
        });
        function print_html() {
            var random = new Date().getTime();
            var pdf = new jsPDF('p', 'pt', 'a4');
            // 設置輸出比例 數值越大比列越小
            pdf.internal.scaleFactor = 2;
            var options = {
                pagesplit: true, //設置是否自動分頁
                "background": '#ffffff'   //如果導出的pdf為黑色背景，需要將導出的html模塊內容背景 設置成白色。
            };
            var printHtml = $('#download').get(0);   // 頁面某一個div裏面的內容，通過id獲取div內容
            pdf.addHTML(printHtml, 15, 15, options, function () {
                pdf.save('htmltopdf_' + random + '.pdf');
                parent.closeLoading();
            });
        }
    </script>

}
