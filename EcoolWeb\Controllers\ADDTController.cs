﻿using com.ecool.service;
using <PERSON>pper;
using ECOOL_APP;
using ECOOL_APP.com.ecool.Models;
using ECOOL_APP.com.ecool.Models.DTO;
using ECOOL_APP.com.ecool.Models.entity;
using ECOOL_APP.com.ecool.service;
using ECOOL_APP.com.ecool.util;
using ECOOL_APP.EF;
using EcoolWeb.CustomAttribute;
using EcoolWeb.Models;
using EcoolWeb.Util;
using EcoolWeb.ViewModels;
using Epub.Net;
using Epub.Net.Models;
using MvcPaging;
using NPOI.SS.UserModel;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Entity.SqlServer;
using System.Data.Entity.Validation;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Text.RegularExpressions;
using System.Transactions;
using System.Web;
using System.Web.Mvc;

namespace EcoolWeb.Controllers
{
    [SessionExpire]
    public class ADDTController : Controller
    {
        private static string Bre_NO = "ADDT";
        private UserProfile user = UserProfileHelper.Get();
        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();
        private string SchoolNO = UserProfileHelper.GetSchoolNo();
        private string USER_NO = string.Empty;
        private bool AppMode = string.IsNullOrEmpty(EcoolWeb.Models.UserProfileHelper.GetUUID()) == false;
        private bool IsAdmin = false;

        /// <summary>
        /// Menu 清單
        /// </summary>
        /// <param name="NowAction"></param>
        /// <returns></returns>
        public ActionResult _PageMenu(string NowAction)
        {
            if (user != null)
            {
                var hrmt01 = db.HRMT01.Where(x => x.USER_NO == user.USER_NO && x.SCHOOL_NO == user.SCHOOL_NO).FirstOrDefault();
                user.CLASS_NO = hrmt01.CLASS_NO == null ? "" : hrmt01.CLASS_NO;
            }
            ViewBag.Permission = PermissionService.GetActionPermissionForBreNO(UserProfileHelper.GetBRE_NO(), user?.SCHOOL_NO, user?.USER_NO)?.Where(a => a.BoolUse)?.ToList();
            ViewBag.NowAction = NowAction;
            return PartialView();
        }

        /// <summary>
        /// 此Controller 對應的程式代碼
        /// </summary>
        public ActionResult ADDT08()
        {
            ViewBag.ADDT08 = db.ADDT08.Where(p => p.SCHOOL_NO == SchoolNO).ToList();
            ViewBag.ADDT07 = db.ADDT07.ToList();
            return PartialView();
        }
        public IQueryable<ADDT0809> QeuryDataRankHis(ADDTListViewModel model)
        {

            IQueryable<ADDT0809> RankBOOK;
            string SchoolNO = UserProfileHelper.GetSchoolNo();
   
           
            if (string.IsNullOrWhiteSpace(model.whereSchoolNo) == false)
            {
                SchoolNO = model.whereSchoolNo;
            }

            string sSQL = @"select *from (SELECT          a9.SCHOOL_NO, a9.USER_NO, a9.LEVEL_ID, a8.LEVEL_DESC, a9.LEVEL_QTY, 
                              
                                      a9.UP_DATE,
                        h1.SYEAR, h1.CLASS_NO, h1.SEAT_NO, h1.SNAME, h1.NAME as USERNAME,
                                (SELECT COUNT(distinct APPLY_NO) AS Expr1
                                  FROM    dbo.ADDT06 AS a6
                                  WHERE   (SCHOOL_NO = a9.SCHOOL_NO) AND (USER_NO = a9.USER_NO) and APPLY_STATUS!='9' ";
            string sSQL7 = @"	 ,
								        (SELECT COUNT(APPLY_NO) AS Expr1
                                  FROM    dbo.ADDT06 AS a6
                                  WHERE   (SCHOOL_NO = a9.SCHOOL_NO) AND (USER_NO = a9.USER_NO) and APPLY_STATUS!='9' ) AS BOOK_QTY";
            string sSQL4 = @"  ,(select SUM (ShareCount)   from (SELECT      
                                (SELECT COUNT(APPLY_NO) AS Expr1
                                  FROM    dbo.ADDT06 AS a6
                                  WHERE(SCHOOL_NO = a9.SCHOOL_NO) AND(USER_NO = a9.USER_NO))
                          AS ShareCount 

                            FROM dbo.ADDT09 AS a9 LEFT OUTER JOIN
                            dbo.HRMT01 AS h1 ON h1.SCHOOL_NO = a9.SCHOOL_NO AND h1.USER_NO = a9.USER_NO LEFT OUTER JOIN
                            dbo.ADDT08 AS a8 ON a8.SCHOOL_NO = a9.SCHOOL_NO AND a8.LEVEL_ID = a9.LEVEL_ID
                             WHERE(h1.USER_STATUS <> 8) AND(h1.USER_STATUS <> 9) AND(a9.BOOK_QTY > 0)
                            and h1.SCHOOL_NO = @SCHOOL_NO";
                             string sSQL5 = @" ) kk  ) as ShareCountSum";


           string sSQL1 = @") 
                          AS ShareCount ";

            string sSQL3 = @" FROM  dbo.ADDT09 AS a9 LEFT OUTER JOIN
                            dbo.HRMT01 AS h1 ON h1.SCHOOL_NO = a9.SCHOOL_NO AND h1.USER_NO = a9.USER_NO LEFT OUTER JOIN
                            dbo.ADDT08 AS a8 ON a8.SCHOOL_NO = a9.SCHOOL_NO AND a8.LEVEL_ID = a9.LEVEL_ID
                             WHERE  (h1.USER_STATUS <> 8) AND (h1.USER_STATUS <> 9) AND (a9.BOOK_QTY > 0) 
                            and h1.SCHOOL_NO=@SCHOOL_NO
                              ) k ";

            if (model.WhereUP_DATE_START != null && model.WhereUP_DATE_END != null)
            {
                string sSQL6 = " and CRE_DATE between @SDate and @EDate";
                string sSQL2 = @"  ,(select SUM (ShareCount)   from (SELECT      
                                (SELECT COUNT(APPLY_NO) AS Expr1
                                  FROM    dbo.ADDT06 AS a6
                                  WHERE(SCHOOL_NO = a9.SCHOOL_NO) AND(USER_NO = a9.USER_NO)  and APPLY_STATUS!='9' and CRE_DATE between @SDate and @EDate  )
                          AS ShareCount

                            FROM dbo.ADDT09 AS a9 LEFT OUTER JOIN
                            dbo.HRMT01 AS h1 ON h1.SCHOOL_NO = a9.SCHOOL_NO AND h1.USER_NO = a9.USER_NO LEFT OUTER JOIN
                            dbo.ADDT08 AS a8 ON a8.SCHOOL_NO = a9.SCHOOL_NO AND a8.LEVEL_ID = a9.LEVEL_ID
                             WHERE(h1.USER_STATUS <> 8) AND(h1.USER_STATUS <> 9) AND(a9.BOOK_QTY > 0)
                            and h1.SCHOOL_NO = @SCHOOL_NO　";
                sSQL = sSQL + sSQL6 + sSQL1+ sSQL7  + sSQL2+ sSQL5 + sSQL3;
                RankBOOK= db.Database.Connection.Query<ADDT0809>(sSQL, new { SCHOOL_NO = SchoolNO, SDate = model.WhereUP_DATE_START, EDate = model.WhereUP_DATE_END }).AsQueryable();
            }
            else
            {
                sSQL = sSQL+ sSQL1 + sSQL7 + sSQL4 + sSQL5 + sSQL3;

                RankBOOK= db.Database.Connection.Query<ADDT0809>(sSQL, new { SCHOOL_NO = SchoolNO }).AsQueryable();
            }

            if (string.IsNullOrWhiteSpace(model.whereKeyword) == false)
            {
                RankBOOK = RankBOOK.Where(a =>a.USER_NO==model.whereKeyword.Trim() || a.USERNAME.Contains(model.whereKeyword.Trim())).AsQueryable();
            }

            if (string.IsNullOrWhiteSpace(model.whereCLASS_NO) == false)
            {
                RankBOOK = RankBOOK.Where(a => a.CLASS_NO == model.whereCLASS_NO.Trim()).AsQueryable();
             
            }

            if (string.IsNullOrWhiteSpace(model.whereGrade) == false)
            {
                RankBOOK = RankBOOK.Where(a => a.CLASS_NO.Substring(0, 1) == model.whereGrade).AsQueryable();
            }
            switch (model.OrdercColumn)
            {
                case "CLASS_NO":
                    RankBOOK = RankBOOK.OrderByDescending(a => a.CLASS_NO).ThenByDescending(a => a.BOOK_QTY);
                    break;

                case "SEAT_NO":
                    RankBOOK = RankBOOK.OrderByDescending(a => a.SEAT_NO).ThenByDescending(a => a.BOOK_QTY);
                    break;

                case "SNAME":
                    RankBOOK = RankBOOK.OrderByDescending(a => a.SNAME).ThenByDescending(a => a.BOOK_QTY);
                    break;

                case "BOOK_QTY":
                    RankBOOK = RankBOOK.OrderByDescending(a => a.BOOK_QTY);
                    break;
                case "ShareCount":
                    RankBOOK = RankBOOK.OrderByDescending(a => a.ShareCount);
                    break;
                default:
                    RankBOOK = RankBOOK.OrderByDescending(a => a.BOOK_QTY);
                    break;
            }
            return RankBOOK;
        }
        public IQueryable<ADDT0809> QeuryDataRank(ADDTListViewModel model)

        {

            IQueryable<ADDT0809> RankBOOK;

            string SchoolNO = UserProfileHelper.GetSchoolNo();

            if (string.IsNullOrWhiteSpace(model.whereSchoolNo) == false)
            {
                SchoolNO = model.whereSchoolNo;
            }
            string sSQL = @"SELECT SCHOOL_NO, USER_NO  , LEVEL_ID  , LEVEL_DESC 
                           , LEVEL_QTY 
      　　　　　　　　　　　, BOOK_QTY 
      　　　　　　　　　　　, UP_DATE 
      　　　　　　　　　　　, SYEAR 
      　　　　　　　　　　　, CLASS_NO 
      　　　　　　　　　　　, SEAT_NO 
      　　　　　　　　　　　, SNAME 
      　　　　　　　　　　　, NAME 
      　　　　　　　　　　　, ShareCount ,DENSE_RANK()OVER(PARTITION BY  SCHOOL_NO order by BOOK_QTY desc) as BOOK_QTYRANK
 　　　　　　　　　　　　  FROM  ADDV05  w1
                          join HRMT01 hh1  (nolock) on  w1.SCHOOL_NO= hh1.SCHOOL_NO and  w1.USER_NO=hh1.USER_NO and USER_STATUS<>@USER_STATUS and USER_TYPE='S'  and  hh1.CLASS_NO is not null
                           where hh1.SCHOOL_NO=@SCHOOL_NO ";

            RankBOOK = db.Database.Connection.Query <ADDT0809>(sSQL, new { USER_STATUS = UserStaus.Invalid, SCHOOL_NO = SchoolNO }).AsQueryable();
            return RankBOOK;
        }
            #region 閱讀認證排行榜

            /// <summary>
            /// 閱讀認證排行榜
            /// </summary>
            /// <param name="model"></param>
            /// <returns></returns>
            public ActionResult ADDTList(ADDTListViewModel model)
        {
            user = UserProfileHelper.Get();
            if (user == null)
            {
                return RedirectToAction("PermissionError1999", "Error");
            }
            string UseYN = "N";
            string SchoolNO = UserProfileHelper.GetSchoolNo();
            ViewBag.BookExplain = db.BDMT01.Where(p => p.SCHOOL_NO == SchoolNO).FirstOrDefault().BOOKEXPLAIN;
            if (model == null) model = new ADDTListViewModel();

            try
            {
                if (model.doClear)
                {
                    model.ClearWhere();
                }

                int PageSize = (AppMode) ? 18 : 20;

                if (model.WhereIsMonthTop)
                {
                    string sSQL = @" Select a.SYEAR,a.SCHOOL_NO,a.USER_NO,a.CLASS_NO,a.SEAT_NO,a.NAME as USERNAME,a.SNAME
                                 ,count(*) as BOOK_QTY
                                 from ADDT06 a (nolock)
                                 where CONVERT(nvarchar(6),a.CRE_DATE,112) = CONVERT(nvarchar(6),GETDATE(),112)
                                 and a.SCHOOL_NO = @SCHOOL_NO
                                 group by a.SYEAR,a.SCHOOL_NO,a.USER_NO,a.CLASS_NO,a.SEAT_NO,a.NAME,a.SNAME
                                 HAVING count(*)>0
                                 order by BOOK_QTY desc";

                    IQueryable<ADDTMonthTopViewModel> ADDTMonthTop = db.Database.Connection.Query<ADDTMonthTopViewModel>(sSQL, new { SCHOOL_NO = SchoolNO }).AsQueryable();

                    if (string.IsNullOrWhiteSpace(model.whereKeyword) == false)
                    {
                        ADDTMonthTop = ADDTMonthTop.Where(a => a.USER_NO.Contains(model.whereKeyword.Trim()) || a.USERNAME.Contains(model.whereKeyword.Trim()));
                    }

                    if (string.IsNullOrWhiteSpace(model.whereCLASS_NO) == false)
                    {
                        ADDTMonthTop = ADDTMonthTop.Where(a => a.CLASS_NO == model.whereCLASS_NO.Trim());
                        PageSize = int.MaxValue;
                    }

                    if (string.IsNullOrWhiteSpace(model.whereGrade) == false)
                    {
                        ADDTMonthTop = ADDTMonthTop.Where(a => a.CLASS_NO.Substring(0, 1) == model.whereGrade);
                    }

                    switch (model.OrdercColumn)
                    {
                        case "CLASS_NO":
                            ADDTMonthTop = ADDTMonthTop.OrderByDescending(a => a.CLASS_NO).ThenByDescending(a => a.BOOK_QTY);
                            break;

                        case "SEAT_NO":
                            ADDTMonthTop = ADDTMonthTop.OrderByDescending(a => a.SEAT_NO).ThenByDescending(a => a.BOOK_QTY);
                            break;

                        case "SNAME":
                            ADDTMonthTop = ADDTMonthTop.OrderByDescending(a => a.SNAME).ThenByDescending(a => a.BOOK_QTY);
                            break;

                        case "BOOK_QTY":
                            ADDTMonthTop = ADDTMonthTop.OrderByDescending(a => a.BOOK_QTY);
                            break;

                        default:
                            ADDTMonthTop = ADDTMonthTop.OrderByDescending(a => a.BOOK_QTY);
                            break;
                    }

                    model.MonthTopList = ADDTMonthTop.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, PageSize);
                }
                else
                {
                    IQueryable<ADDT0809> ADDT0809List = (from v5 in db.ADDV05
                                                         where v5.SCHOOL_NO == SchoolNO
                                                         select new ADDT0809
                                                         {
                                                             SYEAR = SqlFunctions.StringConvert((double)v5.SYEAR),
                                                             CLASS_NO = v5.CLASS_NO,
                                                             SEAT_NO = v5.SEAT_NO,
                                                             SNAME = v5.SNAME,
                                                             USERNAME = v5.NAME,
                                                             BOOK_QTY = v5.BOOK_QTY,
                                                             LEVEL_DESC = v5.LEVEL_DESC + "(" + v5.LEVEL_ID + ")",
                                                             UP_DATE = v5.UP_DATE,
                                                             USER_NO = v5.USER_NO,
                                                             ShareCount = v5.ShareCount
                                                         });

                    if (string.IsNullOrWhiteSpace(model.whereKeyword) == false)
                    {
                        ADDT0809List = ADDT0809List.Where(a => a.USER_NO.Contains(model.whereKeyword.Trim()) || a.USERNAME.Contains(model.whereKeyword.Trim()));
                    }

                    if (string.IsNullOrWhiteSpace(model.whereCLASS_NO) == false)
                    {
                        ADDT0809List = ADDT0809List.Where(a => a.CLASS_NO == model.whereCLASS_NO.Trim());
                        PageSize = int.MaxValue;
                    }

                    if (string.IsNullOrWhiteSpace(model.whereGrade) == false)
                    {
                        ADDT0809List = ADDT0809List.Where(a => a.CLASS_NO.Substring(0, 1) == model.whereGrade);
                    }

                    if (model.WhereUP_DATE_START != null)
                    {
                        ADDT0809List = ADDT0809List.Where(a => a.UP_DATE >= model.WhereUP_DATE_START);
                    }

                    if (model.WhereUP_DATE_END != null)
                    {
                        ADDT0809List = ADDT0809List.Where(a => a.UP_DATE <= model.WhereUP_DATE_END);
                    }

                    switch (model.OrdercColumn)
                    {
                        case "CLASS_NO":
                            ADDT0809List = ADDT0809List.OrderByDescending(a => a.CLASS_NO);
                            break;

                        case "SEAT_NO":
                            ADDT0809List = ADDT0809List.OrderByDescending(a => a.SEAT_NO);
                            break;

                        case "SNAME":
                            ADDT0809List = ADDT0809List.OrderByDescending(a => a.SNAME);
                            break;

                        case "BOOK_QTY":
                            ADDT0809List = ADDT0809List.OrderByDescending(a => a.BOOK_QTY);
                            break;

                        case "LEVEL_DESC":
                            ADDT0809List = ADDT0809List.OrderByDescending(a => a.LEVEL_DESC);
                            break;

                        case "UP_DATE":
                            ADDT0809List = ADDT0809List.OrderByDescending(a => a.UP_DATE);
                            break;

                        case "ShareCount":
                            ADDT0809List = ADDT0809List.OrderByDescending(a => a.ShareCount);
                            break;

                        default:
                            ADDT0809List = ADDT0809List.OrderByDescending(a => a.BOOK_QTY);
                            break;
                    }

                    if (model.IsPrint)
                    {
                        PageSize = int.MaxValue;
                    }

                    model.ADDT0809List = ADDT0809List.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, PageSize);

                    if (model.IsPrint)
                    {
                        if (model.ADDT0809List.Count() > 10000)
                        {
                            TempData["StatusMessage"] = "請縮小條件範圍，目前只顯示前10000筆";
                            model.ADDT0809List = ADDT0809List.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, 10000);
                        }
                    }
                }
            }
            catch (DbEntityValidationException ex)
            {
                var entityError = ex.EntityValidationErrors.SelectMany(x => x.ValidationErrors).Select(x => x.ErrorMessage);
                var getFullMessage = string.Join("; ", entityError);
                var exceptionMessage = string.Concat(ex.Message, "errors are: ", getFullMessage);
            }

            ViewBag.GradeItem = HRMT01.GetGradeItems(model.whereGrade);

            ViewBag.ClassItems = HRMT01.GetClassListData(SchoolNO, model.whereGrade, model.whereCLASS_NO, ref db)
               .Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.whereGrade });

            //判斷是否有權限
            UseYN = (user != null) ? PermissionService.GetPermission_Use_YN("ADDI02", "ADDTList_apply", SchoolNO, user.USER_NO) : "N";
            if (UseYN == "N")
            {
                ViewBag.VisableInsert = false;
            }
            else
            {
                ViewBag.VisableInsert = true;
            }
            if (model.isCarousel)
            {
                return PartialView(model);
            }
            else
            {
                return View(model);
            }
        }

        public ActionResult ExportExcelADDTList(ADDTListViewModel model)
        {
            string UseYN = "N";
            DataTable DataTableExcel = new DataTable();
            string SchoolNO = UserProfileHelper.GetSchoolNo();
            ViewBag.BookExplain = db.BDMT01.Where(p => p.SCHOOL_NO == SchoolNO).FirstOrDefault().BOOKEXPLAIN;
            if (model == null) model = new ADDTListViewModel();
            int PageSize = 20;
            if (model.WhereIsMonthTop)
            {
                string sSQL = @" Select a.SYEAR,a.SCHOOL_NO,a.USER_NO,a.CLASS_NO,a.SEAT_NO,a.NAME as USERNAME,a.SNAME
                                 ,count(*) as BOOK_QTY
                                 from ADDT06 a (nolock)
                                 where CONVERT(nvarchar(6),a.CRE_DATE,112) = CONVERT(nvarchar(6),GETDATE(),112)
                                 and a.SCHOOL_NO = @SCHOOL_NO
                                 group by a.SYEAR,a.SCHOOL_NO,a.USER_NO,a.CLASS_NO,a.SEAT_NO,a.NAME,a.SNAME
                                 HAVING count(*)>0
                                 order by BOOK_QTY desc";

                IQueryable<ADDTMonthTopViewModel> ADDTMonthTop = db.Database.Connection.Query<ADDTMonthTopViewModel>(sSQL, new { SCHOOL_NO = SchoolNO }).AsQueryable();

                if (string.IsNullOrWhiteSpace(model.whereKeyword) == false)
                {
                    ADDTMonthTop = ADDTMonthTop.Where(a => a.USER_NO.Contains(model.whereKeyword.Trim()) || a.USERNAME.Contains(model.whereKeyword.Trim()));
                }

                if (string.IsNullOrWhiteSpace(model.whereCLASS_NO) == false)
                {
                    ADDTMonthTop = ADDTMonthTop.Where(a => a.CLASS_NO == model.whereCLASS_NO.Trim());
                    PageSize = int.MaxValue;
                }

                if (string.IsNullOrWhiteSpace(model.whereGrade) == false)
                {
                    ADDTMonthTop = ADDTMonthTop.Where(a => a.CLASS_NO.Substring(0, 1) == model.whereGrade);
                }

                switch (model.OrdercColumn)
                {
                    case "CLASS_NO":
                        ADDTMonthTop = ADDTMonthTop.OrderByDescending(a => a.CLASS_NO).ThenByDescending(a => a.BOOK_QTY);
                        break;

                    case "SEAT_NO":
                        ADDTMonthTop = ADDTMonthTop.OrderByDescending(a => a.SEAT_NO).ThenByDescending(a => a.BOOK_QTY);
                        break;

                    case "SNAME":
                        ADDTMonthTop = ADDTMonthTop.OrderByDescending(a => a.SNAME).ThenByDescending(a => a.BOOK_QTY);
                        break;

                    case "BOOK_QTY":
                        ADDTMonthTop = ADDTMonthTop.OrderByDescending(a => a.BOOK_QTY);
                        break;

                    default:
                        ADDTMonthTop = ADDTMonthTop.OrderByDescending(a => a.BOOK_QTY);
                        break;
                }
                PageSize = int.MaxValue;

                model.MonthTopList = ADDTMonthTop.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, PageSize);
                var DataList = model.MonthTopList.ToList();
                DataTableExcel = DataList?.AsDataTable();
            }
            else
            {
                IQueryable<ADDT0809> ADDT0809List = (from v5 in db.ADDV05
                                                     where v5.SCHOOL_NO == SchoolNO
                                                     select new ADDT0809
                                                     {
                                                         SYEAR = SqlFunctions.StringConvert((double)v5.SYEAR),
                                                         CLASS_NO = v5.CLASS_NO,
                                                         SEAT_NO = v5.SEAT_NO,
                                                         SNAME = v5.SNAME,
                                                         USERNAME = v5.NAME,
                                                         BOOK_QTY = v5.BOOK_QTY,
                                                         LEVEL_DESC = v5.LEVEL_DESC + "(" + v5.LEVEL_ID + ")",
                                                         UP_DATE = v5.UP_DATE,
                                                         USER_NO = v5.USER_NO,
                                                         ShareCount = v5.ShareCount
                                                     });

                if (string.IsNullOrWhiteSpace(model.whereKeyword) == false)
                {
                    ADDT0809List = ADDT0809List.Where(a => a.USER_NO.Contains(model.whereKeyword.Trim()) || a.USERNAME.Contains(model.whereKeyword.Trim()));
                }

                if (string.IsNullOrWhiteSpace(model.whereCLASS_NO) == false)
                {
                    ADDT0809List = ADDT0809List.Where(a => a.CLASS_NO == model.whereCLASS_NO.Trim());
                    PageSize = int.MaxValue;
                }

                if (string.IsNullOrWhiteSpace(model.whereGrade) == false)
                {
                    ADDT0809List = ADDT0809List.Where(a => a.CLASS_NO.Substring(0, 1) == model.whereGrade);
                }

                if (model.WhereUP_DATE_START != null)
                {
                    ADDT0809List = ADDT0809List.Where(a => a.UP_DATE >= model.WhereUP_DATE_START);
                }

                if (model.WhereUP_DATE_END != null)
                {
                    ADDT0809List = ADDT0809List.Where(a => a.UP_DATE <= model.WhereUP_DATE_END);
                }

                switch (model.OrdercColumn)
                {
                    case "CLASS_NO":
                        ADDT0809List = ADDT0809List.OrderByDescending(a => a.CLASS_NO);
                        break;

                    case "SEAT_NO":
                        ADDT0809List = ADDT0809List.OrderByDescending(a => a.SEAT_NO);
                        break;

                    case "SNAME":
                        ADDT0809List = ADDT0809List.OrderByDescending(a => a.SNAME);
                        break;

                    case "BOOK_QTY":
                        ADDT0809List = ADDT0809List.OrderByDescending(a => a.BOOK_QTY);
                        break;

                    case "LEVEL_DESC":
                        ADDT0809List = ADDT0809List.OrderByDescending(a => a.LEVEL_DESC);
                        break;

                    case "UP_DATE":
                        ADDT0809List = ADDT0809List.OrderByDescending(a => a.UP_DATE);
                        break;

                    case "ShareCount":
                        ADDT0809List = ADDT0809List.OrderByDescending(a => a.ShareCount);
                        break;

                    default:
                        ADDT0809List = ADDT0809List.OrderByDescending(a => a.BOOK_QTY);
                        break;
                }

                PageSize = int.MaxValue;

                model.ADDT0809List = ADDT0809List.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, PageSize);
                var DataList = model.ADDT0809List.ToList();
                DataTableExcel = DataList?.AsDataTable();
            }

            DataTableExcel.Columns.Add("RowNum", typeof(int));
            int i = 0;
            foreach (DataRow row in DataTableExcel.Rows)
            { row["RowNum"] = ++i; }
            NPOIHelper npoi = new NPOIHelper();
            string TempleteFileFullName = Server.MapPath(@"~/Content/ExcelSample/ADDTADDTList.xlsx");

            FileStream xlsfile = new FileStream(TempleteFileFullName, FileMode.Open, FileAccess.Read);
            IWorkbook aBook = WorkbookFactory.Create(xlsfile);

            npoi.ExportExcelFromTemplete(DataTableExcel, aBook, "閱讀認證排行榜", false, 2);

            string strTMPFile = System.Web.Configuration.WebConfigurationManager.AppSettings["FilePath"] + @"\TMP\" + DateTime.Now.Date.ToString("yyyyMMdd");

            if (Directory.Exists(strTMPFile) == false)
            {
                Directory.CreateDirectory(strTMPFile);
            }

            strTMPFile = strTMPFile + @"\閱讀認證排行榜" + Session.SessionID + ".xlsx";

            System.IO.FileStream FS = new FileStream(strTMPFile, FileMode.Create);
            aBook.Write(FS);
            FS.Close();

            return File(strTMPFile, "application/vnd.ms-excel", "閱讀認證排行榜.xlsx");//輸出檔案給Client端
        }

        #endregion 閱讀認證排行榜
        public ActionResult BorrowALLList_HIS(SECI02BooksOrderViewModel model)
        {
            if (user == null)
            {
                return RedirectToAction("SessionTimeOutError", "Error");
            }
            if (model.WhereUP_DATE_START == null || model.WhereUP_DATE_END == null)
            {

                model.WhereUP_DATE_START = DateTime.Parse(DateTime.Now.AddMonths(-6).ToString("yyyy/MM/dd 00:00"));
                model.WhereUP_DATE_END = DateTime.Parse(DateTime.Now.Date.ToString("yyyy/MM/dd 23:59"));
            }

            int PageSize = (AppMode) ? 12 : 20;

            if (model == null) model = new SECI02BooksOrderViewModel();
            ViewBag.GradeItem = HRMT01.GetGradeItems(model.whereGrade);
            var ClassItems = HRMT01.GetClassListData(SchoolNO, model.whereGrade, model.whereCLASS_NO, ref db)
              .Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.whereCLASS_NO });
            if (!string.IsNullOrWhiteSpace(user.TEACH_CLASS_NO))
            {
                ClassItems = ClassItems.Where(a => a.Value == user.TEACH_CLASS_NO);
            }
            ViewBag.ClassItems = ClassItems;
            IQueryable<ADDT0809> ADDT0809List = null;

            //string SQL2 = $@" union
            //select SCHOOL_NO, GRADE, CLASS_NO, USER_NO, NAME, SNAME,0 AS SE_QTY,SEAT_NO,0 AS book_QTY,IDNO  from dbo.HRMT01 AS b where IDNO NOT IN(select NO_READ from dbo.DB2_L_WORK2 where SCHOOL_NO = b.SCHOOL_NO";
            string sSQL = @"		SELECT 
    　　　　　　　　　　　　　　　　　　　　　j.[SCHOOL_NO],j.[GRADE],j.[CLASS_NO],
    　　　　　　　　　　　　　　　　　　　　　j.[USER_NO],j.[NAME],j.[SNAME],j.[SEAT_NO],
    　　　　　　　　　　　　　　　　　　　　　j.[IDNO],k.total_SE_QTY as SE_QTY,
	　　　　　　　　　　　　　　　　　　　　　book_QTYdata.total_book_QTY as book_QTY
　　　　　　　　　　　　　　　　　　　　　　　　　　　　FROM 
　　　　　　　　　　　　　　　　　　　　　　　　　　　　LDB_WORK2 j
　　　　　　　　　　　　　　　　　　　　　　　　　　　　INNER JOIN (
　　　　　　　　　　　　　　　　　　　　　　　　　　　　    SELECT 
　　　　　　　　　　　　　　　　　　　　　　　　　　　　        [USER_NO],[SCHOOL_NO], SUM([SE_QTY]) AS total_SE_QTY
　　　　　　　　　　　　　　　　　　　　　　　　　　　　    FROM 
　　　　　　　　　　　　　　　　　　　　　　　　　　　　       LDB_WORK2
　　　　　　　　　　　　　　　　　　　　　　　　　　　　    GROUP BY 
　　　　　　　　　　　　　　　　　　　　　　　　　　　　        [SCHOOL_NO],
　　　　　　　　　　　　　　　　　　　　　　　　　　　　        [USER_NO]
　　　　　　　　　　　　　　　　　　　　　　　　　　　　) k 
　　　　　　　　　　　　　　　　　　　　　　　　　　　　
　　　　　　　　　　　　　　　　　　　　　　　　　　　　ON j.SCHOOL_NO = k.SCHOOL_NO
　　　　　　　　　　　　　　　　　　　　　　　　　　　　AND j.USER_NO = k.USER_NO
　　　　　　　　　　　　　　　　　　　　　　　　　　　　
　　　　　　　　　　　　　　　　　　　　　　　　　　　　INNER JOIN (
　　　　　　　　　　　　　　　　　　　　　　　　　　　　    SELECT 
　　　　　　　　　　　　　　　　　　　　　　　　　　　　        [USER_NO],[SCHOOL_NO],
　　　　　　　　　　　　　　　　　　　　　　　　　　　　        SUM(book_QTY) AS total_book_QTY
　　　　　　　　　　　　　　　　　　　　　　　　　　　　    FROM 
　　　　　　　　　　　　　　　　　　　　　　　　　　　　       LDB_WORK2
　　　　　　　　　　　　　　　　　　　　　　　　　　　　    GROUP BY 
　　　　　　　　　　　　　　　　　　　　　　　　　　　　        [SCHOOL_NO],
　　　　　　　　　　　　　　　　　　　　　　　　　　　　        [USER_NO]
　　　　　　　　　　　　　　　　　　　　　　　　　　　　) book_QTYdata  ON j.SCHOOL_NO = book_QTYdata.SCHOOL_NO
　　　　　　　　　　　　　　　　　　　　　　　　　　　　AND j.USER_NO = book_QTYdata.USER_NO
　　　　　　　　　　　　　　　　　　　　　　　　　　　　　WHERE 
　　　　　　　　　　　　　　　　　　　　　　　　　　　　　   j.SCHOOL_NO = @SCHOOL_NO  ";

            string sSQLtemp= @"	group by 
            j.[SCHOOL_NO],
　　　　　　　　　　　　　　　　　　　　　　　　　　　　　   j.[GRADE],
　　　　　　　　　　　　　　　　　　　　　　　　　　　　　   j.[CLASS_NO],
　　　　　　　　　　　　　　　　　　　　　　　　　　　　　   j.[USER_NO],
　　　　　　　　　　　　　　　　　　　　　　　　　　　　　   j.[NAME],
　　　　　　　　　　　　　　　　　　　　　　　　　　　　　   j.[SNAME],
　　　　　　　　　　　　　　　　　　　　　　　　　　　　　   j.[SE_QTY],
　　　　　　　　　　　　　　　　　　　　　　　　　　　　　   j.[SEAT_NO],
　　　　　　　　　　　　　　　　　　　　　　　　　　　　　   j.[book_QTY],
　　　　　　　　　　　　　　　　　　　　　　　　　　　　　   j.[IDNO],
　　　　　　　　　　　　　　　　　　　　　　　　　　　　　
　　　　　　　　　　　　　　　　　　　　　　　　　　　　　   k.total_SE_QTY,book_QTYdata.total_book_QTY";



            //SQL2 = SQL2 + ") and SCHOOL_NO = @SCHOOL_NO AND USER_TYPE = 'S' AND USER_STATUS <> 9";

            string SQL2 = "";
            SQL2 = sSQL + sSQLtemp;
            IQueryable<V_DB_L_WORK2ViewModel> BooksList = db.Database.Connection.Query<V_DB_L_WORK2ViewModel>(SQL2
             , new
             {
                 SCHOOL_NO = SchoolNO,
             
              
             }).AsQueryable();
            if (model.WhereUP_DATE_START != null && model.WhereUP_DATE_END != null) {
                sSQLtemp = @" and j.BORROW_DATE between @StartDate and @EndDate 	group by 
            j.[SCHOOL_NO],
　　　　　　　　　　　　　　　　　　　　　　　　　　　　　   j.[GRADE],
　　　　　　　　　　　　　　　　　　　　　　　　　　　　　   j.[CLASS_NO],
　　　　　　　　　　　　　　　　　　　　　　　　　　　　　   j.[USER_NO],
　　　　　　　　　　　　　　　　　　　　　　　　　　　　　   j.[NAME],
　　　　　　　　　　　　　　　　　　　　　　　　　　　　　   j.[SNAME],
　　　　　　　　　　　　　　　　　　　　　　　　　　　　　   j.[SE_QTY],
　　　　　　　　　　　　　　　　　　　　　　　　　　　　　   j.[SEAT_NO],
　　　　　　　　　　　　　　　　　　　　　　　　　　　　　   j.[book_QTY],
　　　　　　　　　　　　　　　　　　　　　　　　　　　　　   j.[IDNO],
　　　　　　　　　　　　　　　　　　　　　　　　　　　　　
　　　　　　　　　　　　　　　　　　　　　　　　　　　　　   k.total_SE_QTY,book_QTYdata.total_book_QTY";

                //  sSQLtemp = sSQLtemp + SQL2;
                SQL2 = sSQL + sSQLtemp;
                BooksList = db.Database.Connection.Query<V_DB_L_WORK2ViewModel>(SQL2
             , new
             {
                 SCHOOL_NO = SchoolNO,
                 StartDate=model.WhereUP_DATE_START,
                 EndDate=model.WhereUP_DATE_END

             }).AsQueryable();

            }
            int ShareCountSum = 0;

            ViewBag.ShareCountSum = ShareCountSum;
            if (!string.IsNullOrEmpty(model.whereGrade)) {

                byte Grades = byte.Parse(model.whereGrade);
                BooksList = BooksList.Where(x => x.GRADE == Grades);

            }
            if (!string.IsNullOrEmpty(model.whereCLASS_NO))
            {

                
                BooksList = BooksList.Where(x => x.CLASS_NO == model.whereCLASS_NO);

            }
            if (!string.IsNullOrEmpty(model.whereSeat_NO))
            {
                BooksList = BooksList.Where(x => x.CLASS_NO == model.whereSeat_NO);
            }
            if (!string.IsNullOrEmpty(model.whereKeyword))
            {
                BooksList = BooksList.Where(a => a.USER_NO == model.whereKeyword.Trim() || a.NAME.Contains(model.whereKeyword.Trim())).AsQueryable();
            
            }
            switch (model.OrdercColumn)
            {
                case "SE_QTY":
                    BooksList = BooksList.OrderByDescending(a => a.SE_QTY);
                    break;
                case "CLASS_NO":
                    BooksList = BooksList.OrderByDescending(a => a.CLASS_NO);
                    break;
                case " SEAT_NO":
                    BooksList = BooksList.OrderByDescending(a => a.SEAT_NO);
                    break;
                case " SNAME":
                    BooksList = BooksList.OrderByDescending(a => a.SNAME);
                    break;
                default:
                    BooksList = BooksList.OrderByDescending(a => a.SE_QTY).ThenBy(a => a.SNAME);
                    break;
            }
            model.BooksListViewModel = BooksList.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, PageSize);
            if (model.IsPrint)
            {
                if (model.BooksList.Count() > 10000)
                {
                    TempData["StatusMessage"] = "請縮小條件範圍，目前只顯示前10000筆";
                    if (BooksList != null)
                    {
                        model.BooksListViewModel = BooksList.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, 10000);
                    }
                }
            }

            if (model.isCarousel)
            {
                return PartialView(model);
            }
            else
            {
                return View(model);
            }



        }
        public ActionResult ADDTALLList_HIS(ADDTListViewModel model) {

            if (user == null)
            {
                return RedirectToAction("SessionTimeOutError", "Error");
            }
            if (model.WhereUP_DATE_START == null || model.WhereUP_DATE_END == null)
            {

                model.WhereUP_DATE_START = DateTime.Parse(DateTime.Now.AddMonths(-6).ToString("yyyy/MM/dd 00:00"));
                model.WhereUP_DATE_END = DateTime.Parse(DateTime.Now.Date.ToString("yyyy/MM/dd 23:59"));
            }
            ////權限 批閱
            //string UseYN = PermissionService.GetPermission_Use_YN("ADDT", "ADDTList_CheckPendingDetail", SchoolNO, user.USER_NO);
            //ViewBag.VisableVerify = (UseYN == "Y");

            ////權限 已批閱後修改/作廢
            //ViewBag.PASS_DEL_ALL_YN = PermissionService.GetPermission_Use_YN("ADDT", "PASS_DEL_ALL", SchoolNO, user.USER_NO);

            ////權限 查詢已作廢資料
            //ViewBag.VisibleSearchDelList = PermissionService.GetPermission_Use_YN("ADDT", "QUERY_DEL", SchoolNO, user.USER_NO);

            ////批次作廢
            //ViewBag.VisibleBATCH_DEL = PermissionService.GetPermission_Use_YN("ADDT", "BATCH_DEL", SchoolNO, user.USER_NO);

            int PageSize = (AppMode) ? 12 : 20;

            if (model == null) model = new ADDTListViewModel();

            //if (user != null)
            //{
            //    if (((ViewBag.VisableVerify) && string.IsNullOrEmpty(user.TEACH_CLASS_NO) == false))
            //    {
            //        if (string.IsNullOrWhiteSpace(model.whereCLASS_NO))
            //        {
            //            model.whereCLASS_NO = user.TEACH_CLASS_NO;
            //        }
            //    }
            //}
            ViewBag.GradeItem = HRMT01.GetGradeItems(model.whereGrade);
            var ClassItems = HRMT01.GetClassListData(SchoolNO, model.whereGrade, model.whereCLASS_NO, ref db)
              .Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.whereCLASS_NO });

            if (!string.IsNullOrWhiteSpace(user.TEACH_CLASS_NO))
            {
                ClassItems = ClassItems.Where(a => a.Value == user.TEACH_CLASS_NO);
            }
            ViewBag.ClassItems = ClassItems;
            IQueryable<ADDT0809> ADDT0809List = null;
                   ADDT0809List = QeuryDataRankHis(model);
            int ShareCountSum = 0;
           
            ViewBag.ShareCountSum = ShareCountSum;
            if (ADDT0809List.ToList().Count() > 0)
            {
                model.ADDT0809List = ADDT0809List.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, PageSize);
            }
            else {
                model.ADDT0809List = null;
            }
            return View(model);
        }
        #region 認證審核一覽表

        /// <summary>
        /// 【認證審核一覽表】
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ActionResult ADDTALLList(ADDT06ViewModel model)
        {
            if (user == null)
            {
                return RedirectToAction("SessionTimeOutError", "Error");
            }

            //權限 批閱
            string UseYN = PermissionService.GetPermission_Use_YN("ADDT", "ADDTList_CheckPendingDetail", SchoolNO, user.USER_NO);
            ViewBag.VisableVerify = (UseYN == "Y");

            //權限 已批閱後修改/作廢
            ViewBag.PASS_DEL_ALL_YN = PermissionService.GetPermission_Use_YN("ADDT", "PASS_DEL_ALL", SchoolNO, user.USER_NO);

            //權限 查詢已作廢資料
            ViewBag.VisibleSearchDelList = PermissionService.GetPermission_Use_YN("ADDT", "QUERY_DEL", SchoolNO, user.USER_NO);

            //批次作廢
            ViewBag.VisibleBATCH_DEL = PermissionService.GetPermission_Use_YN("ADDT", "BATCH_DEL", SchoolNO, user.USER_NO);

            int PageSize = (AppMode) ? 12 : 20;

            if (model == null) model = new ADDT06ViewModel();

            if (user != null)
            {
                if (((ViewBag.VisableVerify) && string.IsNullOrEmpty(user.TEACH_CLASS_NO) == false))
                {
                    if (string.IsNullOrWhiteSpace(model.whereCLASS_NO))
                    {
                        model.whereCLASS_NO = user.TEACH_CLASS_NO;
                    }
                }
            }

            IQueryable<ADDT06> ADDT06List = GetQueryData(model, ref PageSize, ViewBag.VisableVerify);
            if (model.PictureMode)
            {
                /* 篩選圖片的(無文字) */
                ADDT06List = ADDT06List.Where(a => (a.REVIEW == null || a.REVIEW == "") && !(a.IMG_FILE == null || a.IMG_FILE == ""));
            }
            //導師3年級升4年級
            if (string.IsNullOrWhiteSpace(model.whereCLASS_NO) == false)
            {
                ViewBag.isShow = "true";
            }
            model.ADDT06List = ADDT06List.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, PageSize);
            ViewBag.ImageUrl = GetImageDictionaryUrl(model.ADDT06List.Select(s => s).ToList());
            ViewBag.ImageUrl1 = GetImageDictionaryNOSmallUrl(model.ADDT06List.Select(s => s).ToList());
            ViewBag.GradeItem = HRMT01.GetGradeItems(model.whereGrade);

            var ClassItems = HRMT01.GetClassListData(SchoolNO, model.whereGrade, model.whereCLASS_NO, ref db)
                .Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.whereCLASS_NO });

            if (!string.IsNullOrWhiteSpace(user.TEACH_CLASS_NO))
            {
                ClassItems = ClassItems.Where(a => a.Value == user.TEACH_CLASS_NO);
            }
            ViewBag.ClassItems = ClassItems;

            return View(model);
        }

        #endregion 認證審核一覽表

        #region 認證審核一覽表 點選進入明細畫面

        /// <summary>
        /// 【認證審核一覽表】 -->明細
        /// </summary>
        /// <param name="APPLY_NO"></param>
        /// <param name="ShowOriginal"></param>
        /// <returns></returns>
        public ActionResult ADDTALLListDetails(int? APPLY_NO, bool? ShowOriginal, ADDT06ViewModel Search)
        {
            List<ADDT06> liaDDT06 = new List<ADDT06>();

            if (db.ADDT06 == null)
            {
                return HttpNotFound();
            }
            else if (APPLY_NO != null)
            {
                liaDDT06 = db.ADDT06.Where(a => a.APPLY_NO == APPLY_NO).ToList();
                //組圖檔路徑
                ViewBag.ImageUrl = GetImageDictionaryUrl(liaDDT06);
                ViewBag.ImageUrl1 = GetImageDictionaryNOSmallUrl(liaDDT06);
                if (liaDDT06.Count() > 0)
                {
                    if (liaDDT06.FirstOrDefault().APPLY_STATUS == ADDT06.APPLY_STATUS_Val.APPLY_TYPE_DEL && string.IsNullOrWhiteSpace(liaDDT06.FirstOrDefault().BACK_MEMO) == false)
                    {
                        TempData["StatusMessage"] = liaDDT06.FirstOrDefault().BACK_MEMO;
                    }
                }
            }
            else if (APPLY_NO == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }
            this.ALLSearchData(Search);

            //預設顯示老師批閱的文章
            if (string.IsNullOrWhiteSpace(liaDDT06.FirstOrDefault().REVIEW_VERIFY) == false)
                ViewBag.ShowOriginalArticle = (ShowOriginal == true) ? "O" : "V";

            return View(liaDDT06);
        }

        #endregion 認證審核一覽表 點選進入明細畫面

        #region 認證審核一覽表-列印畫面

        /// <summary>
        /// 認證審核一覽表-列印畫面
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ActionResult PrintQuery(ADDT06ViewModel model)
        {
            //權限 批閱
            string UseYN = PermissionService.GetPermission_Use_YN("ADDT", "ADDTList_CheckPendingDetail", SchoolNO, user.USER_NO);
            ViewBag.VisableVerify = (UseYN == "Y");

            int PageSize = int.MaxValue;

            if (model == null) model = new ADDT06ViewModel();

            IQueryable<ADDT06> ADDT06List = GetQueryData(model, ref PageSize);

            model.ADDT06List = ADDT06List.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, PageSize);

            return View(model);
        }

        #endregion 認證審核一覽表-列印畫面

        #region 作廢畫面+資料處理

        [HttpGet]
        /// <summary>
        /// 閱讀單通過後作廢畫面
        /// </summary>
        /// <param name="APPLY_NO"></param>
        /// <param name="Search"></param>
        /// <returns></returns>
        public ActionResult PASS_DEL(int? APPLY_NO, ADDT06ViewModel Search)
        {
            bool BtnPassDel = false;
            string ErrMsg = string.Empty;
            string ImageUrl = string.Empty;

            if (APPLY_NO == null) return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            ADDT06 Model = db.ADDT06.Where(a => a.APPLY_NO == APPLY_NO).FirstOrDefault();
            if (Model == null)
            {
                return HttpNotFound();
            }

            //權限
            if (PermissionService.GetActionPermissionMyClassNO(Model.CLASS_NO, SchoolNO, "ADDT", "PASS_DEL_ALL", user, Model.USER_NO) == false)
            {
                return RedirectToAction("PermissionError", "Error");
            }

            //是否權改修改權限
            ViewBag.ShowImg = PermissionService.GetPermission_Use_YN("ADDT", "IMG_UP", SchoolNO, user.USER_NO) == "Y" ? true : false;

            //是否權改推薦權限
            ViewBag.ShowShare = PermissionService.GetPermission_Use_YN("ADDT", "SHARE_UP", SchoolNO, user.USER_NO) == "Y" ? true : false;

            if (Model != null)
            {
                BtnPassDel = ADDTService.Check_PASS_DEL(Model.APPLY_NO, out ErrMsg, ref db);
                if (BtnPassDel == false)
                {
                    TempData["StatusMessage"] = ErrMsg;
                }
                ImageUrl = GetImageUrl(Model);
            }

            ViewBag.ImageUrl = ImageUrl;
            ViewBag.ImageUrl1 = ImageUrl;
            ViewBag.BtnPassDel = BtnPassDel;

            //退回原因選單
            ViewBag.BackSelectItem = BDMT02Service.GetRefSelectListItem("ADDT", "BACK_REASON", "ALL", SchoolNO, Model.BACK_MEMO, true, null, true, null, ref db);

            this.ALLSearchData(Search);
            return View(Model);
        }

        /// <summary>
        /// 閱讀單通過後作廢畫面 資料處理
        /// </summary>
        /// <param name="ViewModel"></param>
        /// <param name="Search"></param>
        /// <returns></returns>
        [HttpPost]
        [ValidateAntiForgeryToken]
        [ValidateInput(false)]
        public ActionResult PASS_DEL([Bind(Include = "APPLY_NO,REVIEW_VERIFY,SHARE_YN,BACK_MEMO")] ADDT06 ViewModel, ADDT06ViewModel Search, string BtnType, HttpPostedFileBase file)
        {
            bool BtnPassDel = false;
            string ErrMsg = string.Empty;
            string ImageUrl = string.Empty;

            if (ViewModel == null) return new HttpStatusCodeResult(HttpStatusCode.BadRequest); ;
            if (ViewModel.APPLY_NO == 0) return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            List<Tuple<string, string, int>> valuesList = new List<Tuple<string, string, int>>();
            ADDT06 oModel = db.ADDT06.Where(a => a.APPLY_NO == ViewModel.APPLY_NO).FirstOrDefault();
            if (oModel.SHARE_YN == null) {
                oModel.SHARE_YN = "N";


            }
            if (oModel == null)
            {
                return HttpNotFound();
            }

            //權限
            if (PermissionService.GetActionPermissionMyClassNO(oModel.CLASS_NO, SchoolNO, "ADDT", "PASS_DEL_ALL", user, oModel.USER_NO) == false)
            {
                return RedirectToAction("PermissionError", "Error");
            }

            BtnPassDel = ADDTService.Check_PASS_DEL(oModel.APPLY_NO, out ErrMsg, ref db);

            if (BtnPassDel == true && BtnType == PASS_TYPE.PASS_TYPE_D)
            {
                oModel.BACK_MEMO = user.NAME + user.USER_TYPE_DESC + "(已批閱後作廢 " + DateTime.Today.Date.ToString("yyyy/MM/dd") + ")：" + ViewModel.BACK_MEMO;
                oModel.DEL_DATE = DateTime.Now;
                oModel.DEL_PERSON = user.USER_KEY;
                oModel.SHARE_YN = "N";
                bool OK = ADDTService.UpData_PASS_DEL(oModel, out ErrMsg, ref db, user,ref valuesList);
                if (OK)
                {
                    ErrMsg = "作廢成功";
                    BtnPassDel = false;
                }
                else
                {
                    CreADDT06_DEL_LOG(oModel, ErrMsg, user);
                }
            }
            else if (BtnType == PASS_TYPE.PASS_TYPE_E)
            {
                string NEW_SHARE_YN = DataConvertHelper.BoolToYn(ViewModel.SHARE_YN);
                short ChangeShare = 0;
                if (oModel.SHARE_YN == "Y" || oModel.SHARE_YN == "y")
                {
                    if (NEW_SHARE_YN == "N" || NEW_SHARE_YN == "n") ChangeShare = -5;
                }
                else if (oModel.SHARE_YN == "N" || oModel.SHARE_YN == "n")
                {
                    //int iCoolCash = 5;//閱讀一本書的酷幣點數
                    //if (a6.SHARE_YN == "y" || a6.SHARE_YN == "Y") iCoolCash = iCoolCash + 5;
                    if (NEW_SHARE_YN == "Y" || NEW_SHARE_YN == "y") ChangeShare = 5;
                }
                oModel.SHARE_YN = NEW_SHARE_YN;
                oModel.REVIEW_VERIFY = HtmlUtility.SanitizeHtml(ViewModel.REVIEW_VERIFY);

                //處理上傳檔案
                if (file != null)
                {
                    bool ans = doNewImage(oModel, file);

                    if (ans == false)
                    {
                        ErrMsg = "上傳圖片失敗";
                    }
                }

                try
                {
                    if (ChangeShare != 0)
                    {
                        string LogDesc = (ChangeShare > 0) ? "閱讀認證-推薦" : "閱讀認證-取消推薦";
                        CashHelper.AddCash(user, ChangeShare, oModel.SCHOOL_NO,
                                                oModel.USER_NO, "ADDT", oModel.APPLY_NO.ToString(), LogDesc, true, ref db, "", "", ref valuesList);
                    }

                    db.SaveChanges();
                    ErrMsg = "修改成功";
                }
                catch (Exception ex)
                {
                    ErrMsg = "修改失敗，請連絡系統人員，Error:" + ex.Message;
                }
            }
            else if (BtnType == PASS_TYPE.PASS_TYPE_Mail)
            {
                bool OK = MailRemind(oModel, user);

                if (OK)
                {
                    ErrMsg = "寄發Mail通知成功";
                }
            }

            ImageUrl = GetImageUrl(oModel);
            ViewBag.ImageUrl = ImageUrl;
            ViewBag.ImageUrl1 = ImageUrl;
            ViewBag.BtnPassDel = BtnPassDel;
            //退回原因選單
            ViewBag.BackSelectItem = BDMT02Service.GetRefSelectListItem("ADDT", "BACK_REASON", "ALL", SchoolNO, oModel.BACK_MEMO, true, null, true, null, ref db);
            TempData["StatusMessage"] = ErrMsg;

            //是否權改修改權限
            ViewBag.ShowImg = PermissionService.GetPermission_Use_YN("ADDT", "IMG_UP", SchoolNO, user.USER_NO) == "Y" ? true : false;

            //是否權改推薦權限
            ViewBag.ShowShare = PermissionService.GetPermission_Use_YN("ADDT", "SHARE_UP", SchoolNO, user.USER_NO) == "Y" ? true : false;

            this.ALLSearchData(Search);
            return View(oModel);
        }

        #endregion 作廢畫面+資料處理

        #region 閱讀認證成果匯出

        /// <summary>
        /// 【閱讀認證成果匯出】
        /// </summary>
        /// <param name="School_No"></param>
        /// <param name="ShowOriginal"></param>
        /// <returns></returns>
        public ActionResult ADDTALLListDetails3(string School_No, string User_No, string S_DATE, string E_DATE, bool? ShowOriginal, string IDNO,string LogimUser)
        {
            user = UserProfileHelper.Get();
            ViewBag.IDNO = IDNO;
            ViewBag.School_No = School_No;
            ViewBag.User_No = User_No;
            ViewBag.S_DATE = S_DATE;
            ViewBag.E_DATE = E_DATE;

            List<ADDT06> liaDDT06 = new List<ADDT06>();

            if (db.ADDT06 == null)
            {
                return HttpNotFound();
            }
            else if (School_No != string.Empty && (User_No != string.Empty || IDNO != string.Empty))
            {
                List<Tuple<string, string, int>> valuesList = new List<Tuple<string, string, int>>();
                string[] ArrUSER_NO;

                if (string.IsNullOrWhiteSpace(User_No) && string.IsNullOrWhiteSpace(IDNO) == false)
                {
                    ArrUSER_NO = db.HRMT01.Where(a => a.SCHOOL_NO == School_No && a.IDNO == IDNO ).Select(a => a.USER_NO).ToArray();
                }
                else
                {
                    ArrUSER_NO = new[] { User_No };
                    if (LogimUser != "Y") { 
                    string IDNOSTR = "";
                    IDNOSTR = db.HRMT01.Where(x => x.SCHOOL_NO == user.SCHOOL_NO && x.USER_NO == user.USER_NO).Select(x => x.IDNO).FirstOrDefault();
                    if (IDNO == IDNOSTR)
                    {

                        if (UserProfileHelper.CheckSeeStudentIDNOData(user, School_No, User_No, IDNO, ref db) == false)
                        {
                            return RedirectToAction("NotSeeDataError", "Error");
                        }

                    }
                    else
                    {
                        if (UserProfileHelper.CheckSeeStudentData(user, School_No, User_No, ref db) == false)
                        {
                            return RedirectToAction("NotSeeDataError", "Error");
                        }

                    }
                    }
                }

                var Temp = db.ADDT06.Where(a => a.SCHOOL_NO == School_No && a.APPLY_STATUS != ADDT06.APPLY_STATUS_Val.APPLY_TYPE_DEL && a.APPLY_STATUS != ADDT06.APPLY_STATUS_Val.APPLY_TYPE_Back);

                if (string.IsNullOrWhiteSpace(User_No) == false)
                {
                    Temp = Temp.Where(a => a.USER_NO == User_No);
                }
                else
                {
                    User_No = ArrUSER_NO.FirstOrDefault();
                }

                if (ArrUSER_NO.Length > 0)
                {
                    Temp = Temp.Where(a => ArrUSER_NO.Contains(a.USER_NO));
                }

                if (string.IsNullOrWhiteSpace(S_DATE) == false)
                {
                    DateTime dateFromS_DATE = Convert.ToDateTime(S_DATE);
                    Temp = Temp.Where(a => a.CRE_DATE >= dateFromS_DATE);
                }

                if (string.IsNullOrWhiteSpace(E_DATE) == false)
                {
                    DateTime dateFromE_DATE = Convert.ToDateTime(E_DATE);
                    Temp = Temp.Where(a => a.CRE_DATE <= dateFromE_DATE);
                }

                liaDDT06 = Temp.ToList();

                //if (liaDDT06.Count() == 0)
                //{
                //    return View(liaDDT06);
                //}
                //組圖檔路徑
                ViewBag.ImageUrl = GetImageDictionaryNOSmallUrl(liaDDT06);
                ViewBag.ImageUrl1 = GetImageDictionaryNOSmallUrl(liaDDT06);
                //取出學校名稱
                ViewBag.SchoolName = db.BDMT01.Where(p => p.SCHOOL_NO == School_No).FirstOrDefault().SHORT_NAME;

                //取出學生名稱 及 學習期間
                ViewBag.UserName = db.HRMT01.Where(p => p.SCHOOL_NO == School_No && p.USER_NO == User_No).Select(p => p.NAME).FirstOrDefault();

                if (liaDDT06.Count() > 0)
                {
                    //學習期間
                    ViewBag.SYear = liaDDT06.Min(a => a.SYEAR).Value.ToString() + "學年度 ~ " + liaDDT06.Max(a => a.SYEAR).Value.ToString() + "學年度";
                }

                //取出酷幣點數
                AWAT01 UserCash = db.AWAT01.Where(u => u.SCHOOL_NO == School_No && u.USER_NO == User_No).FirstOrDefault();
                if (UserCash != null)
                {
                    ViewBag.CoolCash = UserCash.CASH_ALL.Value;
                }

                //線上投稿總篇數
                ViewBag.SumAcount = db.ADDT01.Where(a => a.SCHOOL_NO == School_No && a.USER_NO == User_No).Count();

                var T0809 = (from a09 in db.ADDT09
                             join a08 in db.ADDT08
                                   on new { a09.SCHOOL_NO, LEVEL_ID = (byte)a09.LEVEL_ID }
                               equals new { a08.SCHOOL_NO, LEVEL_ID = a08.LEVEL_ID }
                             where a09.USER_NO == User_No && a09.SCHOOL_NO == School_No
                             select new ADDT0809
                             {
                                 LEVEL_DESC = a08.LEVEL_DESC
                             }).FirstOrDefault();

                //閱讀認證等級
                ViewBag.PassPort = T0809 != null ? T0809.LEVEL_DESC : "";
            }

            #region "閱讀護照等級撈出"

            //取出該校所有的小朋友閱讀護照通過認證暫存表
            List<uADDT04> litmpADDI04 = new List<uADDT04>();
            //待回傳閱讀護照通過認證
            List<uADDT04Q02> liADDT04Q02 = new List<uADDT04Q02>();

            var temp = (from a04 in db.ADDT04
                        join h01 in db.HRMT01 on new { a04.SCHOOL_NO, a04.USER_NO } equals new { h01.SCHOOL_NO, h01.USER_NO }
                        where a04.USER_NO == User_No
                        && a04.SCHOOL_NO == School_No
                        orderby
                          a04.SCHOOL_NO,
                          h01.CLASS_NO
                        select new uADDT04
                        {
                            CLASS_NO = h01.CLASS_NO,
                            NAME = h01.NAME,
                            SONAME = (h01.SEAT_NO + h01.NAME),
                            GRADE = a04.GRADE,
                            PASS_DATE = a04.PASS_DATE,
                            USER_NO = h01.USER_NO
                        });

            litmpADDI04 = temp.ToList();

            int iStuSeat = -1;    //資料位置
            string Name = string.Empty;
            for (int i = 0; i < litmpADDI04.Count(); i++)
            {
                if (Name != litmpADDI04.ToList()[i].NAME)
                {
                    //取得該學生該年度的學習護照閱讀總筆數
                    int iADDT04Count = litmpADDI04.Where(p => p.USER_NO == litmpADDI04.ToList()[i].USER_NO).Count();

                    liADDT04Q02.Add(new uADDT04Q02
                    {
                        CLASS_NO = litmpADDI04.ToList()[i].CLASS_NO,
                        SONAME = litmpADDI04.ToList()[i].SONAME,
                        UserDate_NO01 = (litmpADDI04.ToList()[i].GRADE == 1) ? "一年級" : "",
                        UserDate_NO02 = (litmpADDI04.ToList()[i].GRADE == 2) ? "二年級" : "",
                        UserDate_NO03 = (litmpADDI04.ToList()[i].GRADE == 3) ? "三年級" : "",
                        UserDate_NO04 = (litmpADDI04.ToList()[i].GRADE == 4) ? "四年級" : "",
                        UserDate_NO05 = (litmpADDI04.ToList()[i].GRADE == 5) ? "五年級" : "",
                        UserDate_NO06 = (litmpADDI04.ToList()[i].GRADE == 6) ? "六年級" : "",
                        UserStatus_NO01 = (litmpADDI04.ToList()[i].GRADE == 1) ? Convert.ToDateTime(litmpADDI04.ToList()[i].PASS_DATE).ToString("yyyy/MM/dd") : "",
                        UserStatus_NO02 = (litmpADDI04.ToList()[i].GRADE == 2) ? Convert.ToDateTime(litmpADDI04.ToList()[i].PASS_DATE).ToString("yyyy/MM/dd") : "",
                        UserStatus_NO03 = (litmpADDI04.ToList()[i].GRADE == 3) ? Convert.ToDateTime(litmpADDI04.ToList()[i].PASS_DATE).ToString("yyyy/MM/dd") : "",
                        UserStatus_NO04 = (litmpADDI04.ToList()[i].GRADE == 4) ? Convert.ToDateTime(litmpADDI04.ToList()[i].PASS_DATE).ToString("yyyy/MM/dd") : "",
                        UserStatus_NO05 = (litmpADDI04.ToList()[i].GRADE == 5) ? Convert.ToDateTime(litmpADDI04.ToList()[i].PASS_DATE).ToString("yyyy/MM/dd") : "",
                        UserStatus_NO06 = (litmpADDI04.ToList()[i].GRADE == 6) ? Convert.ToDateTime(litmpADDI04.ToList()[i].PASS_DATE).ToString("yyyy/MM/dd") : "",
                        FinishStatus = (iADDT04Count == 6) ? "Y" : ""
                    });
                    iStuSeat++;
                    Name = litmpADDI04.ToList()[i].NAME;
                }
                else
                {
                    liADDT04Q02.ToList()[iStuSeat].UserDate_NO01 = (litmpADDI04.ToList()[i].GRADE == 1) ? "一年級" : liADDT04Q02.ToList()[iStuSeat].UserDate_NO01;
                    liADDT04Q02.ToList()[iStuSeat].UserDate_NO02 = (litmpADDI04.ToList()[i].GRADE == 2) ? "二年級" : liADDT04Q02.ToList()[iStuSeat].UserDate_NO02;
                    liADDT04Q02.ToList()[iStuSeat].UserDate_NO03 = (litmpADDI04.ToList()[i].GRADE == 3) ? "三年級" : liADDT04Q02.ToList()[iStuSeat].UserDate_NO03;
                    liADDT04Q02.ToList()[iStuSeat].UserDate_NO04 = (litmpADDI04.ToList()[i].GRADE == 4) ? "四年級" : liADDT04Q02.ToList()[iStuSeat].UserDate_NO04;
                    liADDT04Q02.ToList()[iStuSeat].UserDate_NO05 = (litmpADDI04.ToList()[i].GRADE == 5) ? "五年級" : liADDT04Q02.ToList()[iStuSeat].UserDate_NO05;
                    liADDT04Q02.ToList()[iStuSeat].UserDate_NO06 = (litmpADDI04.ToList()[i].GRADE == 6) ? "六年級" : liADDT04Q02.ToList()[iStuSeat].UserDate_NO06;

                    liADDT04Q02.ToList()[iStuSeat].UserStatus_NO01 = (litmpADDI04.ToList()[i].GRADE == 1) ? Convert.ToDateTime(litmpADDI04.ToList()[i].PASS_DATE).ToString("yyyy/MM/dd") : liADDT04Q02.ToList()[iStuSeat].UserStatus_NO01;
                    liADDT04Q02.ToList()[iStuSeat].UserStatus_NO02 = (litmpADDI04.ToList()[i].GRADE == 2) ? Convert.ToDateTime(litmpADDI04.ToList()[i].PASS_DATE).ToString("yyyy/MM/dd") : liADDT04Q02.ToList()[iStuSeat].UserStatus_NO02;
                    liADDT04Q02.ToList()[iStuSeat].UserStatus_NO03 = (litmpADDI04.ToList()[i].GRADE == 3) ? Convert.ToDateTime(litmpADDI04.ToList()[i].PASS_DATE).ToString("yyyy/MM/dd") : liADDT04Q02.ToList()[iStuSeat].UserStatus_NO03;
                    liADDT04Q02.ToList()[iStuSeat].UserStatus_NO04 = (litmpADDI04.ToList()[i].GRADE == 4) ? Convert.ToDateTime(litmpADDI04.ToList()[i].PASS_DATE).ToString("yyyy/MM/dd") : liADDT04Q02.ToList()[iStuSeat].UserStatus_NO04;
                    liADDT04Q02.ToList()[iStuSeat].UserStatus_NO05 = (litmpADDI04.ToList()[i].GRADE == 5) ? Convert.ToDateTime(litmpADDI04.ToList()[i].PASS_DATE).ToString("yyyy/MM/dd") : liADDT04Q02.ToList()[iStuSeat].UserStatus_NO05;
                    liADDT04Q02.ToList()[iStuSeat].UserStatus_NO06 = (litmpADDI04.ToList()[i].GRADE == 6) ? Convert.ToDateTime(litmpADDI04.ToList()[i].PASS_DATE).ToString("yyyy/MM/dd") : liADDT04Q02.ToList()[iStuSeat].UserStatus_NO06;
                }
            }
            ViewBag.ListA04Q2 = liADDT04Q02;

            #endregion "閱讀護照等級撈出"

            ViewBag.School_No = School_No;
            ViewBag.User_No = User_No;
            ViewBag.S_DATE = S_DATE;
            ViewBag.E_DATE = E_DATE;

            return PartialView(liaDDT06);
        }

        #endregion 閱讀認證成果匯出

        #region 閱讀認證成果匯出

        /// <summary>
        /// 【閱讀認證成果匯出】
        /// </summary>
        /// <param name="School_No"></param>
        /// <param name="ShowOriginal"></param>
        /// <returns></returns>
        public ActionResult ADDTALLListDetails2(string School_No, string User_No, string S_DATE, string E_DATE, bool? ShowOriginal, string IDNO)
        {
            ViewBag.IDNO = IDNO;
            ViewBag.School_No = School_No;
            ViewBag.User_No = User_No;
            ViewBag.S_DATE = S_DATE;
            ViewBag.E_DATE = E_DATE;

            List<ADDT06> liaDDT06 = new List<ADDT06>();

            if (db.ADDT06 == null)
            {
                return HttpNotFound();
            }
            else if (School_No != string.Empty && (User_No != string.Empty || IDNO != string.Empty))
            {
                string[] ArrUSER_NO;

                if (string.IsNullOrWhiteSpace(User_No) && string.IsNullOrWhiteSpace(IDNO) == false)
                {
                    ArrUSER_NO = db.HRMT01.Where(a => a.SCHOOL_NO == School_No && a.IDNO == IDNO ).Select(a => a.USER_NO).ToArray();
                }
                else
                {
                    ArrUSER_NO = new[] { User_No };

                    if (UserProfileHelper.CheckSeeStudentData(user, School_No, User_No, ref db) == false)
                    {
                        return RedirectToAction("NotSeeDataError", "Error");
                    }
                }

                var Temp = db.ADDT06.Where(a => a.SCHOOL_NO == School_No && a.APPLY_STATUS != ADDT06.APPLY_STATUS_Val.APPLY_TYPE_DEL && a.APPLY_STATUS != ADDT06.APPLY_STATUS_Val.APPLY_TYPE_Back);

                if (string.IsNullOrWhiteSpace(User_No) == false)
                {
                    Temp = Temp.Where(a => a.USER_NO == User_No);
                }
                else
                {
                    User_No = ArrUSER_NO.FirstOrDefault();
                }

                if (ArrUSER_NO.Length > 0)
                {
                    Temp = Temp.Where(a => ArrUSER_NO.Contains(a.USER_NO));
                }

                if (string.IsNullOrWhiteSpace(S_DATE) == false)
                {
                    DateTime dateFromS_DATE = Convert.ToDateTime(S_DATE);
                    Temp = Temp.Where(a => a.CRE_DATE >= dateFromS_DATE);
                }

                if (string.IsNullOrWhiteSpace(E_DATE) == false)
                {
                    DateTime dateFromE_DATE = Convert.ToDateTime(E_DATE);
                    Temp = Temp.Where(a => a.CRE_DATE <= dateFromE_DATE);
                }

                liaDDT06 = Temp.ToList();

                //if (liaDDT06.Count() == 0)
                //{
                //    return View(liaDDT06);
                //}
                //組圖檔路徑
                ViewBag.ImageUrl = GetImageDictionaryNOSmallUrl(liaDDT06);
                ViewBag.ImageUrl1 = GetImageDictionaryNOSmallUrl(liaDDT06);
                //取出學校名稱
                ViewBag.SchoolName = db.BDMT01.Where(p => p.SCHOOL_NO == School_No).FirstOrDefault().SHORT_NAME;

                //取出學生名稱 及 學習期間
                ViewBag.UserName = db.HRMT01.Where(p => p.SCHOOL_NO == School_No && p.USER_NO == User_No).Select(p => p.NAME).FirstOrDefault();

                if (liaDDT06.Count() > 0)
                {
                    //學習期間
                    ViewBag.SYear = liaDDT06.Min(a => a.SYEAR).Value.ToString() + "學年度 ~ " + liaDDT06.Max(a => a.SYEAR).Value.ToString() + "學年度";
                }

                //取出酷幣點數
                AWAT01 UserCash = db.AWAT01.Where(u => u.SCHOOL_NO == School_No && u.USER_NO == User_No).FirstOrDefault();
                if (UserCash != null)
                {
                    ViewBag.CoolCash = UserCash.CASH_ALL.Value;
                }

                //線上投稿總篇數
                ViewBag.SumAcount = db.ADDT01.Where(a => a.SCHOOL_NO == School_No && a.USER_NO == User_No).Count();

                var T0809 = (from a09 in db.ADDT09
                             join a08 in db.ADDT08
                                   on new { a09.SCHOOL_NO, LEVEL_ID = (byte)a09.LEVEL_ID }
                               equals new { a08.SCHOOL_NO, LEVEL_ID = a08.LEVEL_ID }
                             where a09.USER_NO == User_No && a09.SCHOOL_NO == School_No
                             select new ADDT0809
                             {
                                 LEVEL_DESC = a08.LEVEL_DESC
                             }).FirstOrDefault();

                //閱讀認證等級
                ViewBag.PassPort = T0809 != null ? T0809.LEVEL_DESC : "";
            }

            #region "閱讀護照等級撈出"

            //取出該校所有的小朋友閱讀護照通過認證暫存表
            List<uADDT04> litmpADDI04 = new List<uADDT04>();
            //待回傳閱讀護照通過認證
            List<uADDT04Q02> liADDT04Q02 = new List<uADDT04Q02>();

            var temp = (from a04 in db.ADDT04
                        join h01 in db.HRMT01 on new { a04.SCHOOL_NO, a04.USER_NO } equals new { h01.SCHOOL_NO, h01.USER_NO }
                        where a04.USER_NO == User_No
                        && a04.SCHOOL_NO == School_No
                        orderby
                          a04.SCHOOL_NO,
                          h01.CLASS_NO
                        select new uADDT04
                        {
                            CLASS_NO = h01.CLASS_NO,
                            NAME = h01.NAME,
                            SONAME = (h01.SEAT_NO + h01.NAME),
                            GRADE = a04.GRADE,
                            PASS_DATE = a04.PASS_DATE,
                            USER_NO = h01.USER_NO
                        });

            litmpADDI04 = temp.ToList();

            int iStuSeat = -1;    //資料位置
            string Name = string.Empty;
            for (int i = 0; i < litmpADDI04.Count(); i++)
            {
                if (Name != litmpADDI04.ToList()[i].NAME)
                {
                    //取得該學生該年度的學習護照閱讀總筆數
                    int iADDT04Count = litmpADDI04.Where(p => p.USER_NO == litmpADDI04.ToList()[i].USER_NO).Count();

                    liADDT04Q02.Add(new uADDT04Q02
                    {
                        CLASS_NO = litmpADDI04.ToList()[i].CLASS_NO,
                        SONAME = litmpADDI04.ToList()[i].SONAME,
                        UserDate_NO01 = (litmpADDI04.ToList()[i].GRADE == 1) ? "一年級" : "",
                        UserDate_NO02 = (litmpADDI04.ToList()[i].GRADE == 2) ? "二年級" : "",
                        UserDate_NO03 = (litmpADDI04.ToList()[i].GRADE == 3) ? "三年級" : "",
                        UserDate_NO04 = (litmpADDI04.ToList()[i].GRADE == 4) ? "四年級" : "",
                        UserDate_NO05 = (litmpADDI04.ToList()[i].GRADE == 5) ? "五年級" : "",
                        UserDate_NO06 = (litmpADDI04.ToList()[i].GRADE == 6) ? "六年級" : "",
                        UserStatus_NO01 = (litmpADDI04.ToList()[i].GRADE == 1) ? Convert.ToDateTime(litmpADDI04.ToList()[i].PASS_DATE).ToString("yyyy/MM/dd") : "",
                        UserStatus_NO02 = (litmpADDI04.ToList()[i].GRADE == 2) ? Convert.ToDateTime(litmpADDI04.ToList()[i].PASS_DATE).ToString("yyyy/MM/dd") : "",
                        UserStatus_NO03 = (litmpADDI04.ToList()[i].GRADE == 3) ? Convert.ToDateTime(litmpADDI04.ToList()[i].PASS_DATE).ToString("yyyy/MM/dd") : "",
                        UserStatus_NO04 = (litmpADDI04.ToList()[i].GRADE == 4) ? Convert.ToDateTime(litmpADDI04.ToList()[i].PASS_DATE).ToString("yyyy/MM/dd") : "",
                        UserStatus_NO05 = (litmpADDI04.ToList()[i].GRADE == 5) ? Convert.ToDateTime(litmpADDI04.ToList()[i].PASS_DATE).ToString("yyyy/MM/dd") : "",
                        UserStatus_NO06 = (litmpADDI04.ToList()[i].GRADE == 6) ? Convert.ToDateTime(litmpADDI04.ToList()[i].PASS_DATE).ToString("yyyy/MM/dd") : "",
                        FinishStatus = (iADDT04Count == 6) ? "Y" : ""
                    });
                    iStuSeat++;
                    Name = litmpADDI04.ToList()[i].NAME;
                }
                else
                {
                    liADDT04Q02.ToList()[iStuSeat].UserDate_NO01 = (litmpADDI04.ToList()[i].GRADE == 1) ? "一年級" : liADDT04Q02.ToList()[iStuSeat].UserDate_NO01;
                    liADDT04Q02.ToList()[iStuSeat].UserDate_NO02 = (litmpADDI04.ToList()[i].GRADE == 2) ? "二年級" : liADDT04Q02.ToList()[iStuSeat].UserDate_NO02;
                    liADDT04Q02.ToList()[iStuSeat].UserDate_NO03 = (litmpADDI04.ToList()[i].GRADE == 3) ? "三年級" : liADDT04Q02.ToList()[iStuSeat].UserDate_NO03;
                    liADDT04Q02.ToList()[iStuSeat].UserDate_NO04 = (litmpADDI04.ToList()[i].GRADE == 4) ? "四年級" : liADDT04Q02.ToList()[iStuSeat].UserDate_NO04;
                    liADDT04Q02.ToList()[iStuSeat].UserDate_NO05 = (litmpADDI04.ToList()[i].GRADE == 5) ? "五年級" : liADDT04Q02.ToList()[iStuSeat].UserDate_NO05;
                    liADDT04Q02.ToList()[iStuSeat].UserDate_NO06 = (litmpADDI04.ToList()[i].GRADE == 6) ? "六年級" : liADDT04Q02.ToList()[iStuSeat].UserDate_NO06;

                    liADDT04Q02.ToList()[iStuSeat].UserStatus_NO01 = (litmpADDI04.ToList()[i].GRADE == 1) ? Convert.ToDateTime(litmpADDI04.ToList()[i].PASS_DATE).ToString("yyyy/MM/dd") : liADDT04Q02.ToList()[iStuSeat].UserStatus_NO01;
                    liADDT04Q02.ToList()[iStuSeat].UserStatus_NO02 = (litmpADDI04.ToList()[i].GRADE == 2) ? Convert.ToDateTime(litmpADDI04.ToList()[i].PASS_DATE).ToString("yyyy/MM/dd") : liADDT04Q02.ToList()[iStuSeat].UserStatus_NO02;
                    liADDT04Q02.ToList()[iStuSeat].UserStatus_NO03 = (litmpADDI04.ToList()[i].GRADE == 3) ? Convert.ToDateTime(litmpADDI04.ToList()[i].PASS_DATE).ToString("yyyy/MM/dd") : liADDT04Q02.ToList()[iStuSeat].UserStatus_NO03;
                    liADDT04Q02.ToList()[iStuSeat].UserStatus_NO04 = (litmpADDI04.ToList()[i].GRADE == 4) ? Convert.ToDateTime(litmpADDI04.ToList()[i].PASS_DATE).ToString("yyyy/MM/dd") : liADDT04Q02.ToList()[iStuSeat].UserStatus_NO04;
                    liADDT04Q02.ToList()[iStuSeat].UserStatus_NO05 = (litmpADDI04.ToList()[i].GRADE == 5) ? Convert.ToDateTime(litmpADDI04.ToList()[i].PASS_DATE).ToString("yyyy/MM/dd") : liADDT04Q02.ToList()[iStuSeat].UserStatus_NO05;
                    liADDT04Q02.ToList()[iStuSeat].UserStatus_NO06 = (litmpADDI04.ToList()[i].GRADE == 6) ? Convert.ToDateTime(litmpADDI04.ToList()[i].PASS_DATE).ToString("yyyy/MM/dd") : liADDT04Q02.ToList()[iStuSeat].UserStatus_NO06;
                }
            }
            ViewBag.ListA04Q2 = liADDT04Q02;

            #endregion "閱讀護照等級撈出"

            ViewBag.School_No = School_No;
            ViewBag.User_No = User_No;
            ViewBag.S_DATE = S_DATE;
            ViewBag.E_DATE = E_DATE;

            return PartialView(liaDDT06);
        }

        #endregion 閱讀認證成果匯出

        #region 匯出各人認證Epub

        /// <summary>
        /// 匯出各人認證Epub
        /// </summary>
        /// <param name="School_No"></param>
        /// <param name="User_No"></param>
        /// <param name="S_DATE"></param>
        /// <param name="E_DATE"></param>
        /// <param name="IDNO"></param>
        /// <returns></returns>
        public ActionResult SaveEpub(string School_No, string User_No, string S_DATE, string E_DATE, string IDNO)
        {
            string[] ArrUSER_NO;

            if (string.IsNullOrWhiteSpace(User_No) && string.IsNullOrWhiteSpace(IDNO) == false)
            {
                ArrUSER_NO = db.HRMT01.Where(a => a.SCHOOL_NO == School_No && a.IDNO == IDNO && a.USER_STATUS == UserStaus.Invalid).Select(a => a.USER_NO).ToArray();
            }
            else
            {
                ArrUSER_NO = new[] { User_No };

                if (UserProfileHelper.CheckSeeStudentData(user, School_No, User_No, ref db) == false)
                {
                    return RedirectToAction("NotSeeDataError", "Error");
                }
            }

            HRMT01 H01 = db.HRMT01.Where(a => a.SCHOOL_NO == School_No && ArrUSER_NO.Contains(a.USER_NO)).FirstOrDefault();

            var Temp = db.ADDT06.Where(a => a.SCHOOL_NO == School_No && a.APPLY_STATUS != ADDT06.APPLY_STATUS_Val.APPLY_TYPE_DEL);

            if (string.IsNullOrWhiteSpace(User_No) == false)
            {
                Temp = Temp.Where(a => a.USER_NO == User_No);
            }
            else
            {
                User_No = ArrUSER_NO.FirstOrDefault();
            }

            if (ArrUSER_NO.Length > 0)
            {
                Temp = Temp.Where(a => ArrUSER_NO.Contains(a.USER_NO));
            }

            if (string.IsNullOrWhiteSpace(S_DATE) == false)
            {
                DateTime dateFromS_DATE = Convert.ToDateTime(S_DATE);
                Temp = Temp.Where(a => a.CRE_DATE >= dateFromS_DATE);
            }

            if (string.IsNullOrWhiteSpace(E_DATE) == false)
            {
                DateTime dateFromE_DATE = Convert.ToDateTime(E_DATE);
                Temp = Temp.Where(a => a.CRE_DATE <= dateFromE_DATE);
            }

            EBook Book = new EBook();

            foreach (var item in Temp.ToList())
            {
                Chapter This = new Chapter();
                This.Name = item.BOOK_NAME;

                string REVIEW = string.Empty;

                if (string.IsNullOrWhiteSpace(item.REVIEW_VERIFY) == false)
                {
                    REVIEW = item.REVIEW_VERIFY.Replace("\r\n", "<br/>");
                }
                else
                {
                    if (string.IsNullOrWhiteSpace(item.REVIEW) == false)
                    {
                        REVIEW = item.REVIEW.Replace("\r\n", "<br/>");
                    }
                }

                string HtmlStr = string.Empty;

                if (string.IsNullOrWhiteSpace(REVIEW) == false)
                {
                    HtmlStr = REVIEW;
                }

                string Image = GetImageUrl(item);

                if (string.IsNullOrWhiteSpace(Image) == false)
                {
                    HtmlStr = HtmlStr + String.Format("<img src = '{0}' />", Image);
                }

                This.Content = HtmlStr;
                Book.Chapters.Add(This);
            }

            Book.Title = H01.NAME + "閱讀認証";
            Book.Language = new Language("zh-TW");
            Book.Publisher = "ecc.hhups.tp.edu.tw";
            Book.Creator = H01.NAME;

            //Book.CssLinkS.Add(Server.MapPath("~/Content/css/jquery-ui.min.css"));
            //Book.CssLinkS.Add(Server.MapPath("~/Content/css/jquery.validationEngine.css"));
            //Book.CssLinkS.Add(Server.MapPath("~/Content/css/bootstrap.css"));
            //Book.CssLinkS.Add(Server.MapPath("~/Content/css/Site.css"));
            //Book.CssLinkS.Add(Server.MapPath("~/Content/css/EzCss.css"));

            // string filepath = Path.GetTempPath() + @"/" + Book.Title + ".Epub";
            // string filepath = Path.GetTempPath() + @"/" + Book.Title + ".pdf";
            string filepath = Server.MapPath($@"~\Content\Epub\{H01.SCHOOL_NO}\{H01.USER_NO}\");
            if (Directory.Exists(filepath) == false)
            {
                Directory.CreateDirectory(filepath);
            }

            string destinationFile = filepath + Book.Title + ".epub";

            Book.GenerateEpub(destinationFile);

            //取得檔案名稱
            string filename = System.IO.Path.GetFileName(destinationFile);

            //讀成串流
            Stream iStream = new FileStream(destinationFile, FileMode.Open, FileAccess.Read, FileShare.Read);

            //回傳出檔案
            return File(iStream, "application/unknown", filename);
        }

        #endregion 匯出各人認證Epub

        //取得小小讀書人學生待審核(任務清單進入)
        public ActionResult ADDTList_CheckPending(ADDT06ViewModel model)
        {
            if (model == null) model = new ADDT06ViewModel();

            model.whereAPPLY_STATUS = ADDT06.APPLY_STATUS_Val.APPLY_TYPE_WithVerify;

            int PageSize = int.MaxValue;
            IQueryable<ADDT06> ADDT06List = GetQueryData(model, ref PageSize);

            model.ADDT06List = ADDT06List.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, PageSize);

            ViewBag.GradeItem = HRMT01.GetGradeItems(model.whereGrade);

            ViewBag.ClassItems = HRMT01.GetClassListData(SchoolNO, model.whereGrade, model.whereCLASS_NO, ref db)
                .Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.whereGrade });

            return View(model);
        }

        private void Shared(string Panel_Title = "")
        {
            ViewBag.BRE_NO = Bre_NO;

            if (!string.IsNullOrWhiteSpace(Panel_Title))
            {
                ViewBag.Panel_Title = Panel_Title;
            }
            else
            {
                ViewBag.Panel_Title = "";
            }

            user = UserProfileHelper.Get();
            SchoolNO = UserProfileHelper.GetSchoolNo();

            if (user != null)
            {
                if (user.SCHOOL_NO != null)
                {
                    SchoolNO = user.SCHOOL_NO;
                    USER_NO = user.USER_NO;
                }
            }
            IsAdmin = HRMT24_ENUM.CheckQAdmin(user);
            ViewBag.IsAdmin = IsAdmin;
        }

        public ActionResult _OnePageContent(EcoolWeb.Models.ADDT06ViewModel model)
        {
            bool? ShowOriginal = null;
            if (model.OrdercColumn == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }

            int applyNO = Int32.Parse(model.OrdercColumn);
            ADDT06 aDDT06 = db.ADDT06.Where(a => a.APPLY_NO == applyNO).FirstOrDefault();
            if (aDDT06 == null)
            {
                return HttpNotFound();
            }

            //預設顯示老師批閱的文章
            if (string.IsNullOrWhiteSpace(aDDT06.REVIEW_VERIFY) == false)
                ViewBag.ShowOriginalArticle = (ShowOriginal == true) ? "O" : "V";

            //組圖檔路徑
            ViewBag.ImageUrl = GetImageUrl(aDDT06);
            ViewBag.ImageUrl1 = GetImageUrl(aDDT06);
            return View(aDDT06);
            return PartialView(model);
        }

        public ActionResult OneIndex(EcoolWeb.Models.ADDT06ViewModel model)
        {
            this.Shared();

            return View(model);
        }

        [HttpGet]
        //小小讀書人申請認證
        public ActionResult ADDTList_apply()
        {
            ViewBag.BookID = Request["BookID"];
            ViewBag.BookStatus = Request["BookStatus"];

            List<uADDT03> liADDT03 = new List<uADDT03>();
            try
            {
                if (ViewBag.BookStatus == "ALL")
                {
                    liADDT03 = ADDT03Service.USP_ReadADDT03_QUERY(user.USER_NO, user.SCHOOL_NO, "");
                }
                else
                {
                    liADDT03 = ADDT03Service.USP_ReadADDT03_QUERY(user.USER_NO, user.SCHOOL_NO, user.GRADE.ToString());
                }
            }
            catch (Exception e)
            {
                throw;
            }

            //取得線上投稿學生投稿說明檔
            string SchoolNO = UserProfileHelper.GetSchoolNo();
            ViewBag.ADDT06SEXPLAIN = db.BDMT01.Where(p => p.SCHOOL_NO == SchoolNO).FirstOrDefault().ADDT06SEXPLAIN;

            var ClassNoitems = db.HRMT01.Where(u => u.SCHOOL_NO == user.SCHOOL_NO && string.IsNullOrEmpty(u.CLASS_NO) == false).
                Select(x => new SelectListItem { Text = x.CLASS_NO, Value = x.CLASS_NO }).Distinct().OrderBy(o => o.Value);

            if (ViewBag.BookID != String.Empty)
            {
                ViewBag.BookList = liADDT03.Select(x => new SelectListItem { Text = x.BOOK_NAME, Value = x.BOOK_ID, Selected = x.BOOK_ID == ViewBag.BookID }).OrderBy(a => a.Text);
            }
            else
            {
                ViewBag.BookList = liADDT03.Select(x => new SelectListItem { Text = x.BOOK_NAME, Value = x.BOOK_ID }).OrderBy(a => a.Text).OrderBy(a => a.Text);
            }

            if (PermissionService.GetPermission_Use_YN("ADDT", "ADDTList_apply", SchoolNO, user?.USER_NO) == SharedGlobal.Y || user?.USER_NO == "coolman")
            {
                ViewBag.ClassItems = HRMT01.GetClassListData(SchoolNO, user?.TEACH_CLASS_NO, ref db);

                ViewBag.USER_NOItems = HRMT01.GetUserNoListData(SchoolNO, user?.TEACH_CLASS_NO, ref db);
            }

            ViewBag.RemindItems = BDMT02Service.GetRefDataList("ADDT", "REMIND", "ALL", SchoolNO, ref db);

            return View(liADDT03);
        }

        [HttpGet]
        public JsonResult GetNameData(string Class_No)
        {
            List<SelectListItem> items = new List<SelectListItem>();
            if (!string.IsNullOrWhiteSpace(Class_No))
            {
                var ltCLASS_NO = db.HRMT01.Where(p => p.CLASS_NO == Class_No && p.SCHOOL_NO == user.SCHOOL_NO && p.USER_TYPE == UserType.Student
                                                && (!UserStaus.NGUserStausList.Contains(p.USER_STATUS))).OrderBy(p => p.SEAT_NO).ToList();
                foreach (var item in ltCLASS_NO)
                {
                    items.Add(new SelectListItem() { Text = item.SEAT_NO + " " + item.NAME, Value = item.USER_NO });
                }
                if (!items.Count.Equals(0))
                {
                    items.Insert(0, new SelectListItem() { Text = "請選擇學生", Value = "" });
                }
            }

            return Json(items, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// 閱讀認證待審核明細
        /// </summary>
        /// <param name="Mode"></param>
        /// <param name="BackAction"></param>
        /// <param name="APPLY_NO"></param>
        /// <returns></returns>
        public ActionResult ADDTList_CheckPendingDetail(string Mode, int APPLY_NO, ADDT06ViewModel Search)
        {
            List<uADDT06> liADDT06 = ADDTService.USP_ADDTPendingDetail_QUERY(APPLY_NO);

            ADDT06 aDDT06 = db.ADDT06.Find(APPLY_NO);
            if (aDDT06 == null)
            {
                return HttpNotFound();
            }
            UserProfile user = UserProfileHelper.Get();
            //批改權限
            if (PermissionService.GetActionPermissionMyClassNO(aDDT06.CLASS_NO, aDDT06.SCHOOL_NO, "ADDT", "ADDTList_CheckPendingDetail", user, aDDT06.USER_NO) == false)
            {
                return RedirectToAction("LoginPage", "Home", new { returnURL = UrlCustomHelper.GetOwnWebUri() + $@"/EcoolWeb/{Bre_NO}/ADDTList_CheckPendingDetail?Mode=Edit&Rule=A&APPLY_NO=" + APPLY_NO + "&BackAction=ADDTALLList&whereAPPLY_STATUS=0&OrdercColumn=APPLY_NO&Page=1" });
            }

            //直接作廢
            ViewBag.VisibleDEL = PermissionService.GetActionPermissionMyClassNO(aDDT06.CLASS_NO, aDDT06.SCHOOL_NO, "ADDT", "DEL", user, aDDT06.USER_NO);

            //退回再修
            ViewBag.VisibleBACK = PermissionService.GetActionPermissionMyClassNO(aDDT06.CLASS_NO, aDDT06.SCHOOL_NO, "ADDT", "BACK", user, aDDT06.USER_NO);

            //是否權改修改權限
            ViewBag.ShowImg = PermissionService.GetPermission_Use_YN("ADDT", "IMG_UP", user.SCHOOL_NO, user.USER_NO) == "Y" ? true : false;

            //是否權改推薦權限
            ViewBag.ShowShare = PermissionService.GetPermission_Use_YN("ADDT", "SHARE_UP", user.SCHOOL_NO, user.USER_NO) == "Y" ? true : false;

            if (liADDT06.Any())
            {
                uADDT06 u6 = liADDT06.First();
                if (u6.REVIEW != null) u6.REVIEW = u6.REVIEW.Replace("\r\n", "<br/>");
            }

            this.ALLSearchData(Search);

            //組圖檔路徑
            ViewBag.ImageUrl = GetImageUrl(aDDT06);
            ViewBag.ImageUrl1 = GetImageUrl(aDDT06);

            string SchoolNO = UserProfileHelper.GetSchoolNo();
            ViewBag.ADDT06TEXPLAIN = db.BDMT01.Where(p => p.SCHOOL_NO == SchoolNO).FirstOrDefault().ADDT06TEXPLAIN;

            //退回原因選單
            ViewBag.BackSelectItem = BDMT02Service.GetRefSelectListItem("ADDT", "BACK_REASON", "ALL", SchoolNO, aDDT06.BACK_MEMO, true, null, true, null, ref db);

            return View(liADDT06);
        }

        //學生修改
        public ActionResult ADDTList_CheckPendingDetail2(int APPLY_NO)
        {
            List<uADDT06> liADDT06 = ADDTService.USP_ADDTPendingDetail_QUERY(APPLY_NO);

            ADDT06 aDDT06 = db.ADDT06.Find(APPLY_NO);
            if (aDDT06 == null)
            {
                return HttpNotFound();
            }
            //組圖檔路徑
            ViewBag.ImageUrl = GetImageUrl(aDDT06);
            ViewBag.ImageUrl1 = GetImageUrl(aDDT06);
            //取得線上投稿學生投稿說明檔
            string SchoolNO = UserProfileHelper.GetSchoolNo();
            ViewBag.ADDT06TEXPLAIN = db.BDMT01.Where(p => p.SCHOOL_NO == SchoolNO).FirstOrDefault().ADDT06TEXPLAIN;

            if (aDDT06.APPLY_STATUS == ADDT06.APPLY_STATUS_Val.APPLY_TYPE_Back && aDDT06.USER_NO == user.USER_NO && aDDT06.SCHOOL_NO == user.SCHOOL_NO)
            {
                TempData["StatusMessage"] = aDDT06.BACK_MEMO;
            }

            return View(liADDT06);
        }

        /// <summary>
        ///  閱讀認證待審核明細 -->資料處理
        /// </summary>
        /// <param name="file"></param>
        /// <returns></returns>
        [HttpPost]
        [ValidateInput(false)]
        public ActionResult ADDTList_CheckPendingDetailEDIT(HttpPostedFileBase file, ADDT06ViewModel Search)
        {
            string Mode = string.Empty;
            int APPLY_NO = Convert.ToInt32(Request["APPLY_NO"].ToString());

            //產是要 Push 批號
            string BATCH_ID = PushService.CreBATCH_ID();
            string Md = Request["Mode"].ToString();

            if (Request["Mode"].ToString() == "Edit") //批閱
            {
                try
                {
                    Mode = ADDT06.APPLY_STATUS_Val.APPLY_TYPE_Pass;

                    string txtARTICLE = HttpUtility.HtmlDecode(Request["txtARTICLE"].ToString());
                    string SHARE_YN = Request["SHARE_YN"] ?? "N";

                    ADDTService.CheckPendingDetailEDIT(Mode, APPLY_NO, txtARTICLE, SHARE_YN, user);

                    //處理上傳檔案
                    if (file != null)
                    {
                        ADDT06 AT06 = db.ADDT06.Where(p => p.APPLY_NO == APPLY_NO).FirstOrDefault();
                        bool ans = doNewImage(AT06, file);
                        db.SaveChanges();

                        string BODY_TXT = "閱讀認證，書名:" + AT06.BOOK_NAME + "已被老師批閱";

                        PushService.InsertPushDataParents(BATCH_ID, AT06.SCHOOL_NO, AT06.USER_NO, "", BODY_TXT, "", "ADDT", "ADDTList_CheckPendingDetailEDIT", AT06.APPLY_NO.ToString(), "ADDT/ADDTALLListDetails?APPLY_NO=" + AT06.APPLY_NO.ToString(), false, ref db);
                        PushService.InsertPushDataMe(BATCH_ID, AT06.SCHOOL_NO, AT06.USER_NO, "", BODY_TXT, "", "ADDT", "ADDTList_CheckPendingDetailEDIT", AT06.APPLY_NO.ToString(), "ADDT/ADDTALLListDetails?APPLY_NO=" + AT06.APPLY_NO.ToString(), false, ref db);
                    }

                    TempData["StatusMessage"] = "批閱完成";
                }
                catch (Exception e)
                {
                    TempData["StatusMessage"] = "異動失敗, 系統錯誤原因:" + e.Message;
                }
            }
            else if (Request["Mode"].ToString() == ADDT06.APPLY_STATUS_Val.APPLY_TYPE_DEL || Request["Mode"].ToString() == ADDT06.APPLY_STATUS_Val.APPLY_TYPE_Back)
            {
                ADDT06 AT06 = db.ADDT06.Where(p => p.APPLY_NO == APPLY_NO).FirstOrDefault();
                if (AT06 == null)
                {
                    return RedirectToAction("NotFindError", "Error");
                }

                AT06.APPLY_STATUS = Request["Mode"].ToString();
                string TempACTION_ID = string.Empty;

                if (AT06.APPLY_STATUS == ADDT06.APPLY_STATUS_Val.APPLY_TYPE_DEL) //作廢
                {
                    TempACTION_ID = "DEL";
                    AT06.BACK_MEMO = user.NAME + user.USER_TYPE_DESC + "(作廢 " + DateTime.Today.Date.ToString("yyyy/MM/dd") + ")：" + Request["BACK_MEMO"].ToString();
                }
                else
                {
                    TempACTION_ID = "BACK";
                    AT06.BACK_MEMO = user.NAME + user.USER_TYPE_DESC + "(退回 " + DateTime.Today.Date.ToString("yyyy/MM/dd") + ")：" + Request["BACK_MEMO"].ToString();
                }

                if (PermissionService.GetActionPermissionMyClassNO(AT06.CLASS_NO, AT06.SCHOOL_NO, "ADDT", TempACTION_ID, user, AT06.USER_NO) == false)
                {
                    return RedirectToAction("PermissionError", "Error");
                }

                AT06.DEL_DATE = DateTime.Now;
                AT06.DEL_PERSON = user.USER_KEY;

                string BODY_TXT = "閱讀認證，書名:" + AT06.BOOK_NAME + " - " + AT06.BACK_MEMO;

                // PushService.InsertPushDataParents(BATCH_ID, AT06.SCHOOL_NO, AT06.USER_NO, "", BODY_TXT, "", "ADDT", "ADDTList_CheckPendingDetailEDIT", AT06.APPLY_NO.ToString(), "ADDT/ADDTALLListDetails?APPLY_NO=" + AT06.APPLY_NO.ToString(), false, ref db);
                PushService.InsertPushDataMe(BATCH_ID, AT06.SCHOOL_NO, AT06.USER_NO, "", BODY_TXT, "", "ADDT", "ADDTList_CheckPendingDetailEDIT", AT06.APPLY_NO.ToString(), "ADDT/ADDTALLListDetails?APPLY_NO=" + AT06.APPLY_NO.ToString(), false, ref db);

                try
                {
                    db.SaveChanges();
                    TempData["StatusMessage"] = "異動完成";
                }
                catch (Exception e)
                {
                    TempData["StatusMessage"] = "異動失敗, 系統錯誤原因:" + e.Message;
                }
            }

            //Push
            PushHelper.ToPushServer(BATCH_ID);

            string strRedirectToAction = Search.BackAction;

            if (String.IsNullOrEmpty(strRedirectToAction))
            {
                strRedirectToAction = (Request["Rule"].ToString() == "T") ? "../ADDT/ADDTList_CheckPending" : "../ADDI02/QUERY";
            }

            return RedirectToAction(strRedirectToAction, Search);
        }

        //學生修改
        [HttpPost]
        public ActionResult ADDTList_CheckPendingDetailEDIT2(HttpPostedFileBase file)
        {
            int APPLY_NO = Convert.ToInt32(Request["APPLY_NO"].ToString());
            //string txtARTICLE = HttpUtility.HtmlDecode(Request["txtARTICLE"].ToString());

            ADDT06 AT06 = db.ADDT06.Where(p => p.APPLY_NO == APPLY_NO).FirstOrDefault();

            if (Request["Mode"].ToString() == ADDT06.APPLY_STATUS_Val.APPLY_TYPE_DEL)
            {
                if (user?.USER_NO != AT06.USER_NO)
                {
                    if (PermissionService.GetActionPermissionMyClassNO(AT06.CLASS_NO, AT06.SCHOOL_NO, "ADDT", ADDT06.APPLY_STATUS_Val.APPLY_TYPE_DEL, user, AT06.USER_NO) == false)
                    {
                        return RedirectToAction("PermissionError", "Error");
                    }
                }

                AT06.APPLY_STATUS = ADDT06.APPLY_STATUS_Val.APPLY_TYPE_DEL;
                AT06.BACK_MEMO = user.NAME + user.USER_TYPE_DESC + "(作廢 " + DateTime.Today.Date.ToString("yyyy/MM/dd") + ")：" + "作者直接作廢";
                AT06.DEL_DATE = DateTime.Now;
                AT06.DEL_PERSON = user.USER_KEY;

                try
                {
                    db.SaveChanges();
                    TempData["StatusMessage"] = "作廢完成";
                }
                catch (Exception e)
                {
                    TempData["StatusMessage"] = "異動失敗, 系統錯誤原因:" + e.Message;
                }
            }
            else
            {
                AT06.REVIEW = Request["txtARTICLE"].ToString();
                AT06.BACK_MEMO = null;

                bool TempSave = false;
                bool.TryParse(Request["TempSave"], out TempSave);

                if (TempSave)
                {
                    AT06.APPLY_STATUS = ADDT06.APPLY_STATUS_Val.APPLY_TYPE_TempSave;
                }
                else
                {
                    AT06.APPLY_STATUS = ADDT06.APPLY_STATUS_Val.APPLY_TYPE_WithVerify;
                }

                //處理上傳檔案
                bool ans = doNewImage(AT06, file);

                try
                {
                    db.SaveChanges();
                    string str = "../ADDT/ADDTList_CheckPendingDetail2?APPLY_NO=" + AT06.APPLY_NO + "&BackAction=QUERY";
                    TempData["StatusMessage"] = "※閱讀認證送審成功，請耐心等待評分，老師審核前，可自行修改內容。</br>※若有上傳圖片照片，請注意版權，並可以用「<a href=\"" + str + "\" style=\"color: blue\" >修改  </a>」功能調整圖片的方向。</br>※文章必須自行創作，切勿抄襲。";
                }
                catch (Exception e)
                {
                    TempData["StatusMessage"] = "異動失敗, 系統錯誤原因:" + e.Message;
                }
            }

            string strRedirectToAction = "../ADDI02/QUERY";
            return RedirectToAction(strRedirectToAction);
        }

        /// <summary>小小讀書人申請認證：新增</summary>
        [HttpPost]
        public ActionResult reader_applyInsert(HttpPostedFileBase file)
        {
            var USER_NO = Request["USER_NO"].ToString();

            var user = db.HRMT01.Where(a => a.SCHOOL_NO == SchoolNO && a.USER_NO == USER_NO).FirstOrDefault();
            string Message = "";
         
           
            Regex regexcode = new Regex(@".*\.(jpg|jpeg|png|gif|bmp)");
            if (file!=null&&file.ContentLength>0&&regexcode.IsMatch(file.FileName.ToLower()) == false)
            {
                Message = "請上傳 jpg、jpeg、png、gif、bmp 等格式的圖片";
                TempData["StatusMessage"] = Message;
                return RedirectToAction("../ADDI02/QUERY", "ADDT");
            }
            //預設值
            int SYear;
            int Semesters;
            SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);
            ADDT06 AT06 = new ADDT06();
            AT06.SCHOOL_NO = user.SCHOOL_NO;

            AT06.USER_NO = user.USER_NO;
            AT06.CLASS_NO = user.CLASS_NO;

            AT06.SYEAR = (byte)SYear;
            AT06.SEMESTER = (byte)Semesters;
            AT06.SEAT_NO = user.SEAT_NO;
            AT06.NAME = user.NAME;
            AT06.SNAME = (user.NAME.Length > 2) ? user.NAME.Substring(user.NAME.Length - 2, 2) : user.NAME;
            AT06.PASSPORT_YN = Request["ddlReadrppBook"].ToString();
            AT06.BOOK_ID = (Request["ddlReadrppBook"].ToString() == "Y") ? Request["BOOK_ID"].ToString() : null;
            AT06.BOOK_NAME = (Request["ddlReadrppBook"].ToString() == "Y") ? Request["BOOK_NAME"].ToString() : Request["txtBOOK_NAME"].ToString();
            AT06.REVIEW = Request["txtARTICLE"].ToString();
            AT06.APPLY_TYPE = 0;

            bool TempSave = false;
            bool.TryParse(Request["TempSave"], out TempSave);

            if (TempSave)
            {
                AT06.APPLY_STATUS = ADDT06.APPLY_STATUS_Val.APPLY_TYPE_TempSave;
            }
            else
            {
                AT06.APPLY_STATUS = ADDT06.APPLY_STATUS_Val.APPLY_TYPE_WithVerify;
            }

            AT06.CRE_DATE = DateTime.Now;

            //同一天同一人不能新增同一本書
            string StatusMemo = string.Empty;
            string BOOKNAME = "";
            if (this.isReadBooK(AT06.SCHOOL_NO, AT06.USER_NO, AT06.BOOK_NAME, ADDT06.APPLY_TYPE_Val.APPLY_TYPE_Student, out StatusMemo) == true)
            {
                TempData["TempAT06"] = AT06;
                TempData["StatusMessage"] = "您好，這本書已經申請過了喔!!<br/>" + StatusMemo + "書名改成" + AT06.BOOK_NAME + DateTime.Now.ToString("yyyyMMdd"); ;
                BOOKNAME = AT06.BOOK_NAME + DateTime.Now.ToString("yyyyMMdd");

                //return RedirectToAction("ADDTList_apply", "ADDT");
            }
            else
            {
                BOOKNAME = AT06.BOOK_NAME;
            }

            //書名一樣就自動當作是Passport
            if (AT06.PASSPORT_YN != "Y")
            {
                ADDT03 PassItem =
                    db.ADDT03.Where(a => a.SCHOOL_NO == AT06.SCHOOL_NO && a.BOOK_NAME == AT06.BOOK_NAME).FirstOrDefault();
                if (PassItem != null)
                {
                    AT06.PASSPORT_YN = "Y";
                    AT06.BOOK_ID = PassItem.BOOK_ID;
                }
            }
            AT06.BOOK_NAME = BOOKNAME;
            //儲存資料
            db.ADDT06.Add(AT06);

            //產是要 Push 批號
            string BATCH_ID = PushService.CreBATCH_ID();

            if (TempSave == false)
            {
                string BODY_TXT = "閱讀認證，有篇新文章，閱讀書名:" + AT06.BOOK_NAME;

                PushService.InsertPushDataParents(BATCH_ID, AT06.SCHOOL_NO, AT06.USER_NO, "", BODY_TXT, "", "ADDT", "reader_applyInsert", AT06.APPLY_NO.ToString(), "ADDT/ADDTALLListDetails?APPLY_NO=" + AT06.APPLY_NO.ToString(), false, ref db);

                PushService.InsertPushDataTEACHER(BATCH_ID, AT06.SCHOOL_NO, AT06.USER_NO, "", BODY_TXT, "", "ADDT", "reader_applyInsert", AT06.APPLY_NO.ToString(), "ADDT/ADDTALLList?whereAPPLY_STATUS=0", false, ref db);
            }

            try
            {
                db.SaveChanges();
                string str = "../ADDT/ADDTList_CheckPendingDetail2?APPLY_NO=" + AT06.APPLY_NO.ToString() + "&BackAction=QUERY";
                TempData["StatusMessage"] = "※閱讀認證送審成功，請耐心等待評分，老師審核前，可自行修改內容。</br>※若有上傳圖片照片，請注意版權，並可以用「<a href=\"" + str + "\" style=\"color: blue\" >修改  </a>」功能調整圖片的方向。</br>※文章必須自行創作，切勿抄襲。";
                bool? SendMailReadToTecher = false;
                SendMailReadToTecher = db.BDMT01.Where(x => x.SCHOOL_NO == user.SCHOOL_NO).Select(x => x.SendMailReadToTecher).FirstOrDefault();
                //發mail 給班導
                if (SendMailReadToTecher == null || !(bool)SendMailReadToTecher)
                {
                    MailToTeacher(AT06);
                }
            }
            catch (Exception e)
            {
                TempData["StatusMessage"] = "異動失敗, 系統錯誤原因:" + e.Message;
            }

            //處理上傳檔案
            bool ans = doNewImage(AT06, file);
            //(Eric 2019.11.23)如果檔案上傳不成功應該要作廢
            if (ans == false && string.IsNullOrEmpty(AT06.REVIEW))
            { AT06.APPLY_STATUS = ADDT06.APPLY_STATUS_Val.APPLY_TYPE_DEL; }
            if (string.IsNullOrEmpty(AT06.REVIEW) && string.IsNullOrWhiteSpace(AT06.IMG_FILE))
            { AT06.APPLY_STATUS = ADDT06.APPLY_STATUS_Val.APPLY_TYPE_DEL; }
            db.SaveChanges();

            if (TempSave == false)
            {
                //Push
                PushHelper.ToPushServer(BATCH_ID);
            }

            return RedirectToAction("../ADDI02/QUERY", "ADDT");
        }

        // GET: ADDT PageList
        public ActionResult Old_ADDTList(FormCollection collection, string sortOrder, int page = 1, int pageSize = 3)
        {
            UserProfile user = UserProfileHelper.Get();
            List<ADDTViewModel> model = null;
            if (user != null)
            {
                model = ADDTService.USP_Old_ADDTVIEWMODEL_QUERY(collection, user);
            }
            //var result = model.OrderBy(d => d.BOOK_QTY).ToPagedList(page, pageSize);
            IEnumerable<ADDTViewModel> result = null;
            ViewBag.BOOKSortParm = sortOrder == "BOOK_QTY" ? "BOOK_QTY" : "CLASS_NO";
            switch (sortOrder)
            {
                case "BOOK_QTY":
                    //result = model.OrderByDescending(d => d.BOOK_QTY).ToPagedList(page, pageSize);
                    return View(result);

                default:
                    //result = model.OrderByDescending(d => d.CLASS_NO).ToPagedList(page, pageSize);
                    return View(result);
            }
        }

        [HttpGet]
        public JsonResult GetBookNameData(string GRADEBOOK)
        {
            List<uADDT03> liADDT03 = new List<uADDT03>();
            if (GRADEBOOK == "true")
            {
                liADDT03 = ADDT03Service.USP_ReadADDT03_QUERY(user.USER_NO, user.SCHOOL_NO, "");
            }
            else
            {
                liADDT03 = ADDT03Service.USP_ReadADDT03_QUERY(user.USER_NO, user.SCHOOL_NO, user.GRADE.ToString());
            }

            List<SelectListItem> items = new List<SelectListItem>();
            if (!string.IsNullOrWhiteSpace(GRADEBOOK))
            {
                foreach (var item in liADDT03)
                {
                    items.Add(new SelectListItem() { Text = item.BOOK_NAME, Value = item.BOOK_ID });
                }
                if (!items.Count.Equals(0))
                {
                    items.Insert(0, new SelectListItem() { Text = "請選擇", Value = "" });
                }
            }

            return Json(items, JsonRequestBehavior.AllowGet);
        }

        public bool doNewImage(ADDT06 aDDT06, HttpPostedFileBase Imgfile)
        {
            if (Imgfile == null) return false;
            //a.組檔案名稱
            aDDT06.IMG_FILE = aDDT06.APPLY_NO.ToString() + "_" + Path.GetFileName(Imgfile.FileName);
            System.Drawing.Image image = System.Drawing.Image.FromStream(Imgfile.InputStream);

            return ImgData(aDDT06, image);
        }

        public bool doNewImageReal(ADDT06 aDDT06, string Imgfile)
        {
            if (string.IsNullOrWhiteSpace(Imgfile)) return false;

            Imgfile = System.Web.HttpContext.Current.Request.MapPath(Imgfile);
            //a.組檔案名稱
            aDDT06.IMG_FILE = aDDT06.APPLY_NO.ToString() + "_" + Path.GetFileName(Imgfile);
            Imgfile = Imgfile.Replace("%20", " ");
            System.Drawing.Image image = System.Drawing.Image.FromFile(Imgfile);

            return ImgData(aDDT06, image);
        }

        public bool ImgData(ADDT06 aDDT06, Image image)
        {
            Regex regexCode = new Regex(@".*\.(jpg|jpeg|png|gif|bmp)");
            if (regexCode.IsMatch(aDDT06.IMG_FILE.ToLower()) == false)
            {
                return false;
            }

            Bitmap imageOutput = null;
            Bitmap imageSmallOutput = null;
            //b.組上傳資料夾路徑
            string UploadImageRoot =
                System.Web.Configuration.WebConfigurationManager.AppSettings["UploadImageRoot"];
            if (UploadImageRoot == null) UploadImageRoot = @"~\Content\";
            if (UploadImageRoot.EndsWith(@"\") == false) UploadImageRoot += @"\";
            string imgsmallpath = string.Format(@"{0}ADDT06IMG\{1}\Small\", System.Web.HttpContext.Current.Server.MapPath(UploadImageRoot), aDDT06.SCHOOL_NO.ToString());
            string imgPath = string.Format(@"{0}ADDT06IMG\{1}\", System.Web.HttpContext.Current.Server.MapPath(UploadImageRoot), aDDT06.SCHOOL_NO.ToString());
            if (Directory.Exists(imgPath) == false) Directory.CreateDirectory(imgPath);
            if (Directory.Exists(imgsmallpath) == false) Directory.CreateDirectory(imgsmallpath);
            try
            {
                double FixWidth = 1000;
                double FixHeight = 1400;
                double smallFixWidth = 200;
                double smallFixHeight = 150;
                double rate = 1;
                double maxPx = 1;
                if (image.Width > FixWidth || image.Height > FixHeight)
                {
                    if (image.Width > FixWidth) rate = FixWidth / image.Width;
                    else if (image.Height * rate > FixHeight) rate = FixHeight / image.Height;

                    int w = Convert.ToInt32(image.Width * rate);
                    int h = Convert.ToInt32(image.Height * rate);
                    imageOutput = new Bitmap(image, w, h);
                    imageOutput.Save(Path.Combine(imgPath, aDDT06.IMG_FILE), image.RawFormat);
                    imageOutput.Dispose();
                }
                else
                {
                    //直接儲存
                    imageOutput = new Bitmap(image);
                    imageOutput.Save(Path.Combine(imgPath, aDDT06.IMG_FILE), image.RawFormat);
                    imageOutput.Dispose();
                }

                if (image.Width > smallFixWidth || image.Height > smallFixHeight)  //如果圖片的寬大於最大值或高大於最大值就往下執行
                {
                    if (image.Width > smallFixWidth) rate = smallFixWidth / image.Width;
                    else if (image.Height * rate > smallFixHeight) rate = smallFixHeight / image.Height;

                    int w = Convert.ToInt32(image.Width * rate);
                    int h = Convert.ToInt32(image.Height * rate);
                    imageSmallOutput = new Bitmap(image, w, h);
                    imageSmallOutput.Save(Path.Combine(imgsmallpath, aDDT06.IMG_FILE), image.RawFormat);
                    imageSmallOutput.Dispose();
                }
                else
                {//直接儲存
                    imageSmallOutput = new Bitmap(image);
                    imageSmallOutput.Save(Path.Combine(imgsmallpath, aDDT06.IMG_FILE), image.RawFormat);
                    imageSmallOutput.Dispose();
                }
                image.Dispose();
            }
            catch (Exception)
            {
                if (image != null)
                {
                    image.Dispose();
                }

                return false;
            }

            return true;
        }

        public List<Image_File> GetImageDictionaryNOSmallUrl(List<ADDT06> liaDDT06)
        {
            List<Image_File> dicImage = new List<Image_File>();

            foreach (var item in liaDDT06)
            {
                string UploadImageRoot =
                   System.Web.Configuration.WebConfigurationManager.AppSettings["UploadImageRoot"];
                var sysPath = System.Web.HttpContext.Current.Request.MapPath(UploadImageRoot);
                string smallimageurl = "";

                string imgPath = string.Format(@"{0}ADDT06IMG\{1}\", sysPath, item.SCHOOL_NO.ToString());
                smallimageurl = GetImageUrl(item);
                if (item.IMG_FILE != null)
                {
                    if (!System.IO.File.Exists(Path.Combine(imgPath, item.IMG_FILE)))
                    {
                        smallimageurl = GetImageUrl(item);
                    }
                }

                dicImage.Add(new Image_File { APPLY_NO = item.APPLY_NO, ImageUrl = smallimageurl });
            }
            return dicImage;
        }

        public List<Image_File> GetImageDictionaryUrl(List<ADDT06> liaDDT06)
        {
            List<Image_File> dicImage = new List<Image_File>();

            foreach (var item in liaDDT06)
            {
                string UploadImageRoot =
                   System.Web.Configuration.WebConfigurationManager.AppSettings["UploadImageRoot"];
                var sysPath = System.Web.HttpContext.Current.Request.MapPath(UploadImageRoot);
                string smallimageurl = "";

                string imgPath = string.Format(@"{0}ADDT06IMG\{1}\Small\", sysPath, item.SCHOOL_NO.ToString());
                smallimageurl = GetSmallImageUrl(item);
                if (item.IMG_FILE != null)
                {
                    if (!System.IO.File.Exists(Path.Combine(imgPath, item.IMG_FILE)))
                    {
                        smallimageurl = GetImageUrl(item);
                    }
                }

                dicImage.Add(new Image_File { APPLY_NO = item.APPLY_NO, ImageUrl = smallimageurl });
            }
            return dicImage;
        }

        // 推薦文清單
        public ActionResult PushList(ADDT06QueryViewModel model)
        {
            user = UserProfileHelper.Get();
            if (user == null)
            {
                return RedirectToAction("PermissionError1999", "Error");
            }
            string UseYN = "N";
            if (model == null) model = new ADDT06QueryViewModel();
            int PageSize = 20;

            string SchoolNO = UserProfileHelper.GetSchoolNo();

            int syear, sem;
            SysHelper.SemestersInfo(DateTime.Today, out syear, out sem);

            IQueryable<ADDT06> ADDT06List = from a in db.ADDT06
                                            join h in db.HRMT01 on new { a.SCHOOL_NO, a.USER_NO } equals new { h.SCHOOL_NO, h.USER_NO }
                                            where a.SCHOOL_NO == SchoolNO && (a.SHARE_YN == "y" || a.SHARE_YN == "Y")
                                            && (h.USER_STATUS != UserStaus.Disable && h.USER_STATUS != UserStaus.Invalid)
                                            &&a.APPLY_STATUS !="9"
                                             
                                            select a;

            if (string.IsNullOrWhiteSpace(model.whereKeyword) == false)
            {
                ADDT06List = ADDT06List.Where(a => a.USER_NO.Contains(model.whereKeyword) || a.NAME.Contains(model.whereKeyword) || a.BOOK_NAME.Contains(model.whereKeyword));
            }

            if (string.IsNullOrWhiteSpace(model.whereUserNo) == false)
            {
                ADDT06List = ADDT06List.Where(a => a.USER_NO == model.whereUserNo);
            }

            if (string.IsNullOrWhiteSpace(model.whereCLASS_NO) == false)
            {
                var HrList = db.HRMT01.Where(a => a.SCHOOL_NO == SchoolNO && a.CLASS_NO == model.whereCLASS_NO).Select(a => a.USER_NO).ToList();
                ADDT06List = ADDT06List.Where(a => HrList.Contains(a.USER_NO));
                PageSize = int.MaxValue;
            }

            if (string.IsNullOrWhiteSpace(model.whereGrade) == false)
            {
                var HrList = db.HRMT01.Where(a => a.SCHOOL_NO == SchoolNO && a.CLASS_NO.Substring(0, 1) == model.whereGrade).Select(a => a.USER_NO).ToList();
                ADDT06List = ADDT06List.Where(a => HrList.Contains(a.USER_NO));
            }

            switch (model.OrdercColumn)
            {
                case "CLASS_NO":
                    ADDT06List = ADDT06List.OrderByDescending(a => a.CLASS_NO);
                    break;

                case "SEAT_NO":
                    ADDT06List = ADDT06List.OrderByDescending(a => a.SEAT_NO);
                    break;

                case "SNAME":
                    ADDT06List = ADDT06List.OrderByDescending(a => a.SNAME);
                    break;

                case "BOOK_NAME":
                    ADDT06List = ADDT06List.OrderByDescending(a => a.BOOK_NAME);
                    break;

                case "CRE_DATE":
                    ADDT06List = ADDT06List.OrderByDescending(a => a.CRE_DATE);
                    break;

                default:
                    ADDT06List = ADDT06List.OrderByDescending(a => a.CRE_DATE);
                    break;
            }

            if (model.ListType == ADDT06ListType.推薦清單圖)
            {
                /* 篩選圖片的(無文字) */
                ADDT06List = ADDT06List.Where(a => (a.REVIEW == null || a.REVIEW == "") && !(a.IMG_FILE == null || a.IMG_FILE == ""));
                PageSize = 12;
            }
            //else if (model.ListType == ADDT06ListType.推薦清單文)
            //{
            //    /* 篩選要有文字心得的 */
            //    ADDT06List = ADDT06List.Where(a => !(a.REVIEW == null || a.REVIEW == ""));
            //}
            //判斷是否有權限
            UseYN = (user != null) ? PermissionService.GetPermission_Use_YN("ADDI02", "ADDTList_apply", user.SCHOOL_NO, user.USER_NO) : "N";
            if (UseYN == "N")
            {
                ViewBag.VisableInsert = false;
            }
            else
            {
                ViewBag.VisableInsert = true;
            }
            //組圖檔路徑
            model.ADDT06List = ADDT06List.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, PageSize);
            ViewBag.ImageUrl = GetImageDictionaryUrl(model.ADDT06List.Select(s => s).ToList());
            ViewBag.ImageUrl1 = GetImageDictionaryNOSmallUrl(model.ADDT06List.Select(s => s).ToList());
            ViewBag.GradeItem = HRMT01.GetGradeItems(model.whereGrade);

            ViewBag.ClassItems = HRMT01.GetClassListData(SchoolNO, model.whereGrade, model.whereCLASS_NO, ref db)
                .Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.whereGrade });

            return View(model);
        }

        // 推薦文明細
        public ActionResult PushListDetail(int? APPLY_NO, bool? ShowOriginal)
        {
            if (APPLY_NO == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }
            ADDT06 aDDT06 = db.ADDT06.Where(a => a.APPLY_NO == APPLY_NO).FirstOrDefault();
            if (aDDT06 == null)
            {
                return HttpNotFound();
            }

            //預設顯示老師批閱的文章
            if (string.IsNullOrWhiteSpace(aDDT06.REVIEW_VERIFY) == false)
                ViewBag.ShowOriginalArticle = (ShowOriginal == true) ? "O" : "V";

            //組圖檔路徑
            ViewBag.ImageUrl = GetImageUrl(aDDT06);
            ViewBag.ImageUrl1 = GetImageUrl(aDDT06);
            return View(aDDT06);
        }

        //獎品清單
        public ActionResult PrizeList(string USER_NO, ADDTListViewModel Search)
        {
            List<PrizeListViewModel> ltPLVM = new List<PrizeListViewModel>();
            if (USER_NO == null || USER_NO == string.Empty)
            {
                ltPLVM = (from at09 in db.ADDT09
                          join at09_his in db.ADDT09_HIS
                          on new { at09.USER_NO, at09.SCHOOL_NO }
                          equals new { at09_his.USER_NO, at09_his.SCHOOL_NO }
                          where at09.SCHOOL_NO == SchoolNO
                          select new PrizeListViewModel
                          {
                              LEVEL_DESC = ((from at08 in db.ADDT08
                                             where at08.SCHOOL_NO == at09.SCHOOL_NO &&
                                                   at09_his.LEVEL_ID == at08.LEVEL_ID
                                             select new
                                             {
                                                 at08.LEVEL_DESC
                                             }).FirstOrDefault().LEVEL_DESC),
                              LEVEL_ID = (int)(at09_his.LEVEL_ID),
                              NAME = at09_his.NAME,
                              SNAME = at09_his.SNAME,
                              BOOK_QTY = at09.BOOK_QTY.ToString(),
                              UP_DATE = at09_his.UP_DATE.HasValue ? at09_his.UP_DATE : null
                          }).Distinct().OrderByDescending(a => a.UP_DATE).ToList();
            }
            else
            {
                ltPLVM = (from at09 in db.ADDT09
                          join at09_his in db.ADDT09_HIS
                          on new { at09.USER_NO, at09.SCHOOL_NO }
                          equals new { at09_his.USER_NO, at09_his.SCHOOL_NO }
                          where at09.USER_NO == USER_NO && at09.SCHOOL_NO == SchoolNO
                          select new PrizeListViewModel
                          {
                              LEVEL_DESC = ((from at08 in db.ADDT08
                                             where at08.SCHOOL_NO == at09.SCHOOL_NO &&
                                                   at09_his.LEVEL_ID == at08.LEVEL_ID
                                             select new
                                             {
                                                 at08.LEVEL_DESC
                                             }).FirstOrDefault().LEVEL_DESC),
                              LEVEL_ID = (int)(at09_his.LEVEL_ID),
                              NAME = at09_his.NAME,
                              SNAME = at09_his.SNAME,
                              BOOK_QTY = at09_his.BOOK_QTY.ToString(),
                              UP_DATE = at09_his.UP_DATE.HasValue ? at09_his.UP_DATE : null
                          }).Distinct().OrderByDescending(a => a.UP_DATE).ToList();
            }

            this.SearchData(Search);

            return View(ltPLVM);
        }

        //閱讀認證獎狀處理清單
        [CheckPermission(CheckBRE_NO = "ZZZI16")]
        public ActionResult HandleBookCreditList(ADDTListViewModel model, int PageSize = 50)
        {
            List<SelectListItem> PageSizeItems = new List<SelectListItem>();
            PageSizeItems.Add(new SelectListItem() { Text = "顯示10筆", Value = "10", Selected = PageSize == 10 });
            PageSizeItems.Add(new SelectListItem() { Text = "顯示50筆", Value = "50", Selected = PageSize == 50 });
            PageSizeItems.Add(new SelectListItem() { Text = "顯示100筆", Value = "100", Selected = PageSize == 100 });
            PageSizeItems.Add(new SelectListItem() { Text = "顯示200筆", Value = "200", Selected = PageSize == 200 });
            PageSizeItems.Add(new SelectListItem() { Text = "顯示全部筆數", Value = int.MaxValue.ToString(), Selected = PageSize == int.MaxValue });
            ViewBag.PageSizeItems = PageSizeItems;

            ViewBag.GradeItem = HRMT01.GetGradeItems(model.whereGrade);

            ViewBag.ClassItems = HRMT01.GetClassListData(SchoolNO, model.whereGrade, model.whereCLASS_NO, ref db)
                .Select(x => new SelectListItem { Text = x.Text, Value = x.Value, Selected = x.Value == model.whereGrade });

            if (model == null) model = new ADDTListViewModel();

            if (model.a09_his_STATUS == null) model.a09_his_STATUS = 0;

            try
            {
                IQueryable<ADDT0809> ADDT0809List = GetADDT0809List(model);

                if (string.IsNullOrWhiteSpace(model.whereCLASS_NO) == false)
                {
                    PageSize = int.MaxValue;
                }

                model.ADDT0809List = ADDT0809List.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, PageSize);
            }
            catch (DbEntityValidationException ex)
            {
                var entityError = ex.EntityValidationErrors.SelectMany(x => x.ValidationErrors).Select(x => x.ErrorMessage);
                var getFullMessage = string.Join("; ", entityError);
                var exceptionMessage = string.Concat(ex.Message, "errors are: ", getFullMessage);
            }

            if (model.A9List == null) model.A9List = new List<ADDT0809>();
            model.A9List = model.ADDT0809List.ToList();

            return View(model);
        }

        public IQueryable<ADDT0809> GetADDT0809List(ADDTListViewModel model)
        {
            IQueryable<ADDT0809> TempList = (from a09_his in db.ADDT09_HIS
                                             join a08 in db.ADDT08
                                                   on new { a09_his.LEVEL_ID, a09_his.SCHOOL_NO }
                                               equals new { a08.LEVEL_ID, a08.SCHOOL_NO }
                                             where a09_his.STATUS == model.a09_his_STATUS && a09_his.SCHOOL_NO == SchoolNO
                                             select new ADDT0809
                                             {
                                                 UPNO = a09_his.UPNO.ToString(),
                                                 SYEAR = a09_his.SYEAR.ToString(),
                                                 SEMESTER = a09_his.SEMESTER.ToString(),
                                                 CLASS_NO = a09_his.CLASS_NO,
                                                 SEAT_NO = a09_his.SEAT_NO,
                                                 USERNAME = a09_his.NAME,
                                                 SNAME = a09_his.SNAME,
                                                 BOOK_QTY = a09_his.BOOK_QTY,
                                                 LEVEL_DESC = a08.LEVEL_DESC,
                                                 UP_DATE = a09_his.UP_DATE,
                                                 STATUS = a09_his.STATUS.ToString(),
                                                 SCHOOL_NO = a09_his.SCHOOL_NO,
                                                 USER_NO = a09_his.USER_NO,
                                                 DP_PERSON = a09_his.DP_PERSON,
                                                 DP_DATE = a09_his.DP_DATE
                                             });

            if (string.IsNullOrWhiteSpace(model.whereKeyword) == false)
            {
                TempList = TempList.Where(a => a.USER_NO.Contains(model.whereKeyword.Trim()) || a.SNAME.Contains(model.whereKeyword.Trim()));
            }

            if (string.IsNullOrWhiteSpace(model.whereCLASS_NO) == false)
            {
                TempList = TempList.Where(a => a.CLASS_NO == model.whereCLASS_NO.Trim());
            }

            if (string.IsNullOrWhiteSpace(model.whereGrade) == false)
            {
                TempList = TempList.Where(a => a.CLASS_NO.Substring(0, 1) == model.whereGrade);
            }

            if (model.a09_his_STATUS == 1)
            {
                TempList = TempList.OrderByDescending(a => a.DP_DATE).OrderByDescending(a => a.UP_DATE);
            }
            else
            {
                switch (model.OrdercColumn)
                {
                    case "CLASS_NO":
                        TempList = TempList.OrderByDescending(a => a.CLASS_NO);
                        break;

                    case "SEAT_NO":
                        TempList = TempList.OrderByDescending(a => a.SEAT_NO);
                        break;

                    case "SNAME":
                        TempList = TempList.OrderByDescending(a => a.SNAME);
                        break;

                    case "BOOK_QTY":
                        TempList = TempList.OrderByDescending(a => a.BOOK_QTY);
                        break;

                    case "LEVEL_DESC":
                        TempList = TempList.OrderByDescending(a => a.LEVEL_DESC);
                        break;

                    case "UP_DATE":
                        TempList = TempList.OrderByDescending(a => a.UP_DATE);
                        break;

                    default:
                        TempList = TempList.OrderBy(a => a.UPNO);
                        break;
                }
            }

            return TempList;
        }

        [HttpPost]
        public ActionResult HandleBookCreditEDIT(ADDTListViewModel model)
        {
            if (model.A9List == null) model.A9List = new List<ADDT0809>();

            //移除沒勾選的資料
            model.A9List.RemoveAll(a => a.Chk == false);

            if (model.A9List.Count() > 0)
            {
                foreach (ADDT0809 it in model.A9List)
                {
                    int UPNO = Convert.ToInt32(it.UPNO);
                    ADDT09_HIS A09_HIS = db.ADDT09_HIS.Where(p => p.UPNO == UPNO).FirstOrDefault();
                    if (A09_HIS != null)
                    {
                        A09_HIS.STATUS = (byte)1;
                        A09_HIS.DP_DATE = DateTime.Now;
                        A09_HIS.DP_PERSON = user.USER_KEY;
                    }
                }

                try
                {
                    db.SaveChanges();
                    TempData["StatusMessage"] = "以下閱讀認證獎狀頒發已完成";
                }
                catch (Exception ex)
                {
                    throw ex;
                }
            }
            else
            {
                TempData["StatusMessage"] = "未勾選任何資料";
            }

            return View(model);
        }

        [HttpPost]
        public ActionResult ExportExcel(ADDTListViewModel model)
        {
            if (model == null) model = new ADDTListViewModel();

            IQueryable<ADDT0809> IQueryableExcel = GetADDT0809List(model);

            DataTable DataTableExcel = IQueryableExcel.ToList().AsDataTable();

            NPOIHelper npoi = new NPOIHelper();
            string TempleteFileFullName = Server.MapPath(@"~/Content/ExcelSample/ADDT_ExportExcel.xlsx");

            FileStream xlsfile = new FileStream(TempleteFileFullName, FileMode.Open, FileAccess.Read);
            IWorkbook aBook = WorkbookFactory.Create(xlsfile);

            npoi.ExportExcelFromTemplete(DataTableExcel, aBook, "獎狀處理清單", false, 2);

            string strTMPFile = System.Web.Configuration.WebConfigurationManager.AppSettings["FilePath"] + @"\TMP\" + DateTime.Now.Date.ToString("yyyyMMdd");

            if (Directory.Exists(strTMPFile) == false)
            {
                Directory.CreateDirectory(strTMPFile);
            }

            strTMPFile = strTMPFile + @"\獎狀處理清單_" + Session.SessionID + ".xlsx";

            System.IO.FileStream FS = new FileStream(strTMPFile, FileMode.Create);
            aBook.Write(FS);
            FS.Close();

            return File(strTMPFile, "application/vnd.ms-excel", "獎狀處理清單.xlsx");//輸出檔案給Client端
        }

        [CheckPermission(CheckACTION_ID = "BATCH_DEL")] //檢查權限
        public ActionResult BatchDeleteView(ADDTBatchDeleteViewViewModel model)
        {
            var DelApplys = model.StringDelApply.Split(',').Where(x => x.Length > 2).ToList();

            model.BatchDeleteList = (from a in db.ADDT06
                                     where DelApplys.Contains(a.APPLY_NO.ToString())
                                     && a.APPLY_STATUS != ADDT06.APPLY_STATUS_Val.APPLY_TYPE_DEL
                                     select new ADDTBatchDeleteListDataViewViewModel()
                                     {
                                         aDDT06 = a,
                                         OK = false,
                                         Error = "",
                                         APPLY_NO = a.APPLY_NO,
                                     }).ToListNoLock();

            //退回原因選單
            ViewBag.BackSelectItem = BDMT02Service.GetRefSelectListItem("ADDT", "BACK_REASON", "ALL", SchoolNO, "", true, null, true, null, ref db);

            return View(model);
        }

        public ActionResult _BatchDeleteList(ADDTBatchDeleteListDataViewViewModel Item)
        {
            return PartialView("_BatchDeleteList", Item);
        }

        [CheckPermission(CheckACTION_ID = "BATCH_DEL")] //檢查權限
        [HttpPost]
        [ValidateAntiForgeryToken]
        [ValidateInput(false)]
        public ActionResult BatchDeleteData(ADDTBatchDeleteViewViewModel model)
        {
            using (TransactionScope tx = new TransactionScope())
            {
                ModelState.Clear();
                try
                {
                    if (model.BatchDeleteList?.Count > 0)
                    {
                        List<Tuple<string, string, int>> valuesList = new List<Tuple<string, string, int>>();
                        foreach (var item in model.BatchDeleteList)
                        {
                            bool BtnPassDel = false;
                            string ErrMsg = string.Empty;

                            var aDDT06 = db.ADDT06.Where(x => x.APPLY_NO == item.APPLY_NO).FirstOrDefault();

                            if (aDDT06 != null)
                            {
                                aDDT06.BACK_MEMO = user.NAME + user.USER_TYPE_DESC + "(批次作廢 " + DateTime.Today.Date.ToString("yyyy/MM/dd") + ")：" + model.BACK_MEMO;
                                aDDT06.DEL_DATE = DateTime.Now;
                                aDDT06.DEL_PERSON = user.USER_KEY;

                                //未通過資料
                                if (aDDT06.APPLY_STATUS == ADDT06.APPLY_STATUS_Val.APPLY_TYPE_WithVerify
                                    || aDDT06.APPLY_STATUS == ADDT06.APPLY_STATUS_Val.APPLY_TYPE_WithVerify_X
                                    || aDDT06.APPLY_STATUS == ADDT06.APPLY_STATUS_Val.APPLY_TYPE_Back
                                    || aDDT06.APPLY_STATUS == ADDT06.APPLY_STATUS_Val.APPLY_TYPE_TempSave
                                    )
                                {
                                    aDDT06.APPLY_STATUS = ADDT06.APPLY_STATUS_Val.APPLY_TYPE_DEL;
                                    db.Entry(aDDT06).State = System.Data.Entity.EntityState.Modified;
                                    try
                                    {
                                        db.SaveChanges();
                                        item.OK = true;
                                    }
                                    catch (Exception ex)
                                    {
                                        item.OK = false;
                                        item.Error = ex.Message;
                                    }
                                }
                                else if (aDDT06.APPLY_STATUS == ADDT06.APPLY_STATUS_Val.APPLY_TYPE_DEL)
                                {
                                    item.OK = true;
                                    item.Error = "此資料早已被作廢";
                                }
                                else //已通過的資料
                                {
                                    BtnPassDel = ADDTService.Check_PASS_DEL(aDDT06.APPLY_NO, out ErrMsg, ref db);

                                    if (BtnPassDel)
                                    {
                                        item.OK = ADDTService.UpData_PASS_DEL(aDDT06, out ErrMsg, ref db, user,ref valuesList);
                                        if (item.OK == false)
                                        {
                                            item.Error = ErrMsg;
                                        }
                                        else
                                        {
                                            item.OK = true;
                                        }
                                    }
                                    else
                                    {
                                        item.OK = false;
                                        item.Error = ErrMsg;
                                    }
                                }

                                if (item.OK == false)
                                {
                                    CreADDT06_DEL_LOG(aDDT06, item.Error, user);
                                }

                                item.aDDT06 = aDDT06;
                            }
                        }

                        tx.Complete();
                    }
                }
                catch (Exception ex)
                {
                    throw ex;
                }
            }

            return View(model);
        }

        [CheckPermission(CheckACTION_ID = "BATCH_DEL")] //檢查權限
        public ActionResult DEL_LOG_LIST(ADDTBatchDeleteViewViewModel model)
        {
            model.BatchDeleteList = (from a in db.ADDT06
                                     join b in db.ADDT06_Del_Log on a.APPLY_NO equals b.APPLY_NO
                                     where a.APPLY_STATUS != ADDT06.APPLY_STATUS_Val.APPLY_TYPE_DEL
                                     where b.DEL_PERSON == user.USER_KEY 
                                     select new ADDTBatchDeleteListDataViewViewModel()
                                     {
                                         aDDT06 = a,
                                         Error = b.ERROR,
                                         APPLY_NO = a.APPLY_NO,
                                     }).OrderBy(x=>x.aDDT06.CRE_DATE).ToListNoLock();

            return View(model);
        }

        /// <summary>
        /// 判斷此書是否閱讀過，閱讀是否閱讀護照，讀過但閱讀護照，此次只能讀閱讀護照
        /// </summary>
        /// <param name="SCHOOL_NO"></param>
        /// <param name="USER_NO"></param>
        /// <param name="BOOK_NAME"></param>
        /// <param name="APPLY_TYPE">申請方式 0:學生線上申請, 1:老師代申請 2:批次上傳</param>
        /// <returns>true 已閱讀 、false 未閱讀</returns>
        public bool isReadBooK(string SCHOOL_NO, string USER_NO, string BOOK_NAME, int APPLY_TYPE, out string StatusMemo)
        {
            bool ReturnBool = false;
            StatusMemo = string.Empty;

            //判斷 ADDT06小小讀書人申請閱讀認證是否有資料
            var ListT06 = db.ADDT06.Where(p => p.USER_NO == USER_NO && p.SCHOOL_NO == SCHOOL_NO && p.BOOK_NAME == BOOK_NAME && p.APPLY_STATUS != ADDT06.APPLY_STATUS_Val.APPLY_TYPE_DEL &&p.BOOK_NAME != "書名請見閱讀單");
            if (ListT06 == null || ListT06.Count() == 0)
            {
                //判斷 學生是否通過閱讀護照 for (舊資料)
                ReturnBool = isADDT05(SCHOOL_NO, USER_NO, BOOK_NAME);
            }
            else // 此書 ADDT06 有資料了
            {
                ADDT06 a6 = ListT06.First();
                if (a6.APPLY_STATUS == ADDT06.APPLY_STATUS_Val.APPLY_TYPE_WithVerify)
                {
                    StatusMemo = "(待審核)";
                }
                //判斷此書在 小小讀書人申請閱讀認證 是否申請閱讀護照
                ReturnBool = ListT06.Where(a => a.PASSPORT_YN == "Y").Any();
                if (ReturnBool == false) //沒有
                {
                    //判斷 學生是否通過閱讀護照 for (舊資料)
                    ReturnBool = isADDT05(SCHOOL_NO, USER_NO, BOOK_NAME);
                    if (ReturnBool == false)
                    {
                        if (APPLY_TYPE == ADDT06.APPLY_TYPE_Val.APPLY_TYPE_Student) //學生線上申請
                        {
                            ReturnBool = true; //學生重覆申請
                        }
                        else
                        {
                            if (isADDT03(SCHOOL_NO, BOOK_NAME) == false) //此書是否有閱讀護照設定 ，沒有
                            {
                                ReturnBool = true; //重覆申請
                            }
                        }
                    }
                }
            }

            return ReturnBool;
        }

        /// <summary>
        /// 是否 ADDT03閱讀護照書籍設定
        /// </summary>
        /// <param name="SCHOOL_NO"></param>
        /// <param name="BOOK_NAME"></param>
        /// <returns></returns>
        public bool isADDT03(string SCHOOL_NO, string BOOK_NAME)
        {
            return db.ADDT03.Where(A => A.SCHOOL_NO == SCHOOL_NO && A.BOOK_NAME == BOOK_NAME).Any();
        }

        public void SearchData(ADDTListViewModel Data)
        {
            if (Data == null) Data = new ADDTListViewModel();

            TempData["Search"] = Data;
        }

        public void ALLSearchData(ADDT06ViewModel Data)
        {
            if (Data == null) Data = new ADDT06ViewModel();

            TempData["Search"] = Data;
        }

        private void CreADDT06_DEL_LOG(ADDT06 aDDT06, string Error, UserProfile user)
        {
            ADDT06_Del_Log Log = new ADDT06_Del_Log();
            Log.DEL_ERROR_ID = Guid.NewGuid().ToString("N");
            Log.APPLY_NO = aDDT06.APPLY_NO;
            Log.BACK_MEMO = aDDT06.BACK_MEMO;
            Log.DEL_DATE = DateTime.Now;
            Log.DEL_PERSON = user.USER_KEY;
            Log.ERROR = Error;
            db.ADDT06_Del_Log.Add(Log);
            db.SaveChanges();
        }

        private IQueryable<ADDT06> GetQueryData(ADDT06ViewModel model, ref int PageSize, bool VisableVerify = false)
        {
            var ADDT06List = from a06 in db.ADDT06
                             join h01 in db.HRMT01
                                 on new { a06.SCHOOL_NO, a06.USER_NO }
                                 equals new { h01.SCHOOL_NO, h01.USER_NO } into h01_join
                             from h01 in h01_join.DefaultIfEmpty()
                             where a06.SCHOOL_NO == SchoolNO && (!UserStaus.NGKeyinUserStausList.Contains(h01.USER_STATUS))
                             select a06;

            if (string.IsNullOrWhiteSpace(model.whereKeyword) == false)
            {
                ADDT06List = ADDT06List.Where(a => a.USER_NO.Contains(model.whereKeyword.Trim()) || a.NAME.Contains(model.whereKeyword.Trim()) || a.BOOK_NAME.Contains(model.whereKeyword.Trim()));
            }
            if (model.WhereUP_DATE_START != null && model.WhereUP_DATE_END != null)
            {
                ADDT06List = ADDT06List.Where(a => a.CRE_DATE >= model.WhereUP_DATE_START && a.CRE_DATE <= model.WhereUP_DATE_END);

            }
            if (string.IsNullOrWhiteSpace(model.whereUserNo) == false)
            {
                ADDT06List = ADDT06List.Where(a => a.USER_NO == model.whereUserNo.Trim());
            }

            if (string.IsNullOrWhiteSpace(model.whereBOOK_NAME) == false)
            {
                ADDT06List = ADDT06List.Where(a => a.BOOK_NAME == model.whereBOOK_NAME.Trim());
            }

            if (string.IsNullOrWhiteSpace(model.whereAPPLY_STATUS) == false)
            {
                ADDT06List = ADDT06List.Where(a => a.APPLY_STATUS == model.whereAPPLY_STATUS);
            }
            else
            {
                ADDT06List = ADDT06List.Where(a => a.APPLY_STATUS != ADDT06.APPLY_STATUS_Val.APPLY_TYPE_DEL && a.APPLY_STATUS != ADDT06.APPLY_STATUS_Val.APPLY_TYPE_TempSave);
            }

            if (string.IsNullOrWhiteSpace(model.whereCLASS_NO) == false)
            {
                List<string> hrmt01list = new List<string>();
                hrmt01list = (from x in db.HRMT01 where x.CLASS_NO == model.whereCLASS_NO && x.SCHOOL_NO == SchoolNO select x.USER_NO).ToList();
                ADDT06List = ADDT06List.Where(a => hrmt01list.Contains(a.USER_NO));
                PageSize = int.MaxValue;
            }

            if (string.IsNullOrWhiteSpace(model.whereGrade) == false)
            {
                ADDT06List = ADDT06List.Where(a => a.CLASS_NO.Substring(0, 1) == model.whereGrade);
            }

            switch (model.OrdercColumn)
            {
                case "CRE_DATE":
                    ADDT06List = ADDT06List.OrderByDescending(a => a.CRE_DATE);
                    break;

                case "CLASS_NO":
                    ADDT06List = ADDT06List.OrderByDescending(a => a.CLASS_NO);
                    break;

                case "SEAT_NO":
                    ADDT06List = ADDT06List.OrderByDescending(a => a.SEAT_NO);
                    break;

                case "SNAME":
                    ADDT06List = ADDT06List.OrderByDescending(a => a.SNAME);
                    break;

                default:
                    ADDT06List = ADDT06List.OrderByDescending(a => a.APPLY_NO);
                    break;
            }

            return ADDT06List;
        }

        private bool MailRemind(ADDT06 aDDT06, UserProfile user)
        {
            HRMT01 tHrmt01 = (from c in db.HRMT01
                              where c.SCHOOL_NO == user.SCHOOL_NO && c.USER_NO == user.USER_NO && (!string.IsNullOrEmpty(c.E_MAIL))
                              select c).FirstOrDefault();

            if (tHrmt01 != null)
            {
                if (string.IsNullOrWhiteSpace(tHrmt01.E_MAIL) == false)
                {
                    string SUBJECT = "臺北e酷幣-閱讀認證批閱後需作廢";

                    StringBuilder MailBodyHtml = new StringBuilder();

                    string UrlString = @"http://" + Request.Url.Authority + Request.ApplicationPath + string.Format(@"/ADDT/PASS_DEL?APPLY_NO={0}", aDDT06.APPLY_NO);

                    MailBodyHtml.Append("<table border='0' cellspacing='0' cellpadding='0' width='640'>");
                    MailBodyHtml.Append("<tr>");
                    MailBodyHtml.AppendFormat("<td><p><font color='#0066FF'>{0}─臺北e酷-閱讀認證批閱後需作廢通知！</font></p><br/></td>", user.SCHOOL_NAME);
                    MailBodyHtml.Append("</tr>");
                    MailBodyHtml.Append("<tr>");
                    MailBodyHtml.AppendFormat("<td><p> 親愛的 <font color='#0066FF'>{0}</font> {1}您好:<br/><br/>", user.NAME, user.USER_TYPE_DESC);
                    MailBodyHtml.AppendFormat("您的學生 <font color='#0066FF'>{0}</font> 在臺北e酷幣閱讀認證，申請一篇閱讀認證，書名：<font color='#0066FF'>{1}</font> <br/>", aDDT06.NAME, aDDT06.BOOK_NAME);
                    MailBodyHtml.Append("此閱讀認證需作廢，希望您能撥空前往作廢。謝謝！ <br/><br/>");
                    MailBodyHtml.AppendFormat(@"詳細資料： <a href = '{0}' target = '_blank'>{0}</a></p></td>", UrlString);
                    MailBodyHtml.Append("</tr>");
                    MailBodyHtml.Append("<tr>");
                    MailBodyHtml.AppendFormat("<td><p align = 'right'><br/>by 臺北e酷幣</p></td>");
                    MailBodyHtml.Append("</tr>");
                    MailBodyHtml.Append("</table>");

                    List<string> Mail = new List<string>();
                    Mail.Add(tHrmt01.E_MAIL);

                    MailHelper MailHelper = new MailHelper();
                    MailHelper.SendMailByGmail(Mail, SUBJECT, MailBodyHtml.ToString());
                }
            }

            return true;
        }

        private void MailToTeacher(ADDT06 aDDT06)
        {
            HRMT01 TEACHER = (from c in db.HRMT01
                              join d in db.HRMT03 on new { SCHOOL_NO = c.SCHOOL_NO, USER_NO = c.USER_NO } equals new { SCHOOL_NO = d.SCHOOL_NO, USER_NO = d.TEACHER_NO }
                              where d.SCHOOL_NO == aDDT06.SCHOOL_NO && d.CLASS_NO == aDDT06.CLASS_NO && (!string.IsNullOrEmpty(c.E_MAIL))
                              select c).FirstOrDefault();

            var bDMT01 = db.BDMT01.Where(x => x.SCHOOL_NO == aDDT06.SCHOOL_NO).FirstOrDefault();

            if (TEACHER != null)
            {
                if (string.IsNullOrWhiteSpace(TEACHER.E_MAIL) == false)
                {
                    string SUBJECT = "閱讀認證通知";

                    StringBuilder MailBodyHtml = new StringBuilder();

                    string UrlString = @"http://" + Request.Url.Authority + Request.ApplicationPath + string.Format(@"/ADDT/ADDTList_CheckPendingDetail?Mode=Edit&Rule=A&APPLY_NO={0}&BackAction=ADDTALLList&whereAPPLY_STATUS=0&OrdercColumn=APPLY_NO&Page=1", aDDT06.APPLY_NO);

                    MailBodyHtml.Append("<table border='0' cellspacing='0' cellpadding='0' width='640'>");
                    MailBodyHtml.Append("<tr>");
                    MailBodyHtml.AppendFormat("<td><p><font color='#0066FF'>{0}─臺北e酷幣閱讀認證通知通知！</font></p><br/></td>", bDMT01.SCHOOL_NAME);
                    MailBodyHtml.Append("</tr>");
                    MailBodyHtml.Append("<tr>");
                    MailBodyHtml.AppendFormat("<td><p> 親愛的 <font color='#0066FF'>{0}</font> 老師您好:<br/><br/>", TEACHER.NAME);
                    MailBodyHtml.AppendFormat("您的學生 <font color='#0066FF'>{0}</font> 在臺北e酷幣閱讀認證寫了一片心得，書名：<font color='#0066FF'>{1}</font> <br/>", aDDT06.NAME, aDDT06.BOOK_NAME);
                    MailBodyHtml.Append("希望您能撥空前往批閱。謝謝！ <br/><br/>");
                    MailBodyHtml.AppendFormat(@"詳細資料： <a href = '{0}' target = '_blank'>{0}</a></p></td>", UrlString);
                    MailBodyHtml.Append("</tr>");
                    MailBodyHtml.Append("<tr>");
                    MailBodyHtml.AppendFormat("<td><p align = 'right'><br/>by 臺北e酷幣</p></td>");
                    MailBodyHtml.Append("</tr>");
                    MailBodyHtml.Append("</table>");

                    List<string> Mail = new List<string>();
                    Mail.Add(TEACHER.E_MAIL);

                    MailHelper MailHelper = new MailHelper();
                    MailHelper.SendMailByGmail(Mail, SUBJECT, MailBodyHtml.ToString());
                }
            }
        }

        private string GetSmallImageUrl(ADDT06 aDDT06)
        {
            if (string.IsNullOrEmpty(aDDT06.IMG_FILE) == false)
            {
                string UploadImageRoot =
                   System.Web.Configuration.WebConfigurationManager.AppSettings["UploadImageRoot"];
                if (UploadImageRoot == null) UploadImageRoot = @"~\Content\";
                if (UploadImageRoot.EndsWith(@"\") == false) UploadImageRoot += @"\";

                var sysPath = System.Web.HttpContext.Current.Request.MapPath(UploadImageRoot);

                string imgPath = string.Format(@"{0}ADDT06IMG\{1}\Small\", sysPath, aDDT06.SCHOOL_NO.ToString());

                if (System.IO.File.Exists(Path.Combine(imgPath, aDDT06.IMG_FILE)))
                {
                    string imgUrl = string.Format(@"{0}ADDT06IMG\{1}\Small\{2}", UploadImageRoot, aDDT06.SCHOOL_NO, aDDT06.IMG_FILE);

                    var strUrl = string.Empty;

                    try
                    {
                        strUrl = Url.Content(imgUrl);
                    }
                    catch (Exception)
                    {
                        strUrl = imgUrl;
                    }

                    return strUrl;
                }
            }
            return string.Empty;
            //return string.Format(Url.Content("~/Content/SchoolIMG/{0}/ADDT06.jpg"), aDDT06.SCHOOL_NO);
        }

        private string GetImageUrl(ADDT06 aDDT06)
        {
            if (string.IsNullOrEmpty(aDDT06.IMG_FILE) == false)
            {
                string UploadImageRoot =
                   System.Web.Configuration.WebConfigurationManager.AppSettings["UploadImageRoot"];
                if (UploadImageRoot == null) UploadImageRoot = @"~\Content\";
                if (UploadImageRoot.EndsWith(@"\") == false) UploadImageRoot += @"\";

                var sysPath = System.Web.HttpContext.Current.Request.MapPath(UploadImageRoot);

                string imgPath = string.Format(@"{0}ADDT06IMG\{1}\", sysPath, aDDT06.SCHOOL_NO.ToString());

                if (System.IO.File.Exists(Path.Combine(imgPath, aDDT06.IMG_FILE)))
                {
                    string imgUrl = string.Format(@"{0}ADDT06IMG\{1}\{2}", UploadImageRoot, aDDT06.SCHOOL_NO, aDDT06.IMG_FILE);

                    var strUrl = string.Empty;

                    try
                    {
                        strUrl = Url.Content(imgUrl);
                    }
                    catch (Exception)
                    {
                        strUrl = imgUrl;
                    }

                    return strUrl;
                }
            }
            return string.Empty;
            //return string.Format(Url.Content("~/Content/SchoolIMG/{0}/ADDT06.jpg"), aDDT06.SCHOOL_NO);
        }

        /// <summary>
        /// 學生是否通過閱讀護照  PS.ADDT05學生閱讀護照紀錄表
        /// </summary>
        /// <param name="SCHOOL_NO"></param>
        /// <param name="USER_NO"></param>
        /// <param name="BOOK_NAME"></param>
        /// <returns></returns>
        private bool isADDT05(string SCHOOL_NO, string USER_NO, string BOOK_NAME)
        {
            return db.ADDT05.Where(A => A.SCHOOL_NO == SCHOOL_NO && A.USER_NO == USER_NO && A.BOOK_NAME == BOOK_NAME && A.BOOK_NAME != "書名請見閱讀單").Any();
        }

        public static class PASS_TYPE
        {
            /// <summary>
            /// 批閱通過後作廢
            /// </summary>
            public static string PASS_TYPE_D = "Del";

            /// <summary>
            /// 批閱通過後作廢
            /// </summary>
            public static string PASS_TYPE_E = "Edit";

            /// <summary>
            /// 批閱通過後作廢無法作廢寄發mail通知
            /// </summary>
            public static string PASS_TYPE_Mail = "ToMail";
        }
    }
}