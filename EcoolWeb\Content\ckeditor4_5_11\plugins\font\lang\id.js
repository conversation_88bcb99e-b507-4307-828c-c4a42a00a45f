﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'font', 'id', {
	fontSize: {
		label: '<PERSON>kura<PERSON>',
		voiceLabel: '<PERSON>kura<PERSON> Huruf',
		panelTitle: 'Ukura<PERSON> Huruf'
	},
	label: '<PERSON>ru<PERSON>',
	panelTitle: 'Font Name', // MISSING
	voiceLabel: '<PERSON>ru<PERSON>'
} );
