﻿using ECOOL_APP;
using ECOOL_APP.com.ecool.service;
using ECOOL_APP.EF;
using EcoolWeb.CustomAttribute;
using EcoolWeb.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace EcoolWeb.Controllers
{
    public class SECI08Controller : Controller
    {
        /// <summary>
        /// 此Controller 對應的程式代碼
        /// </summary>
        private static string Bre_NO = "SECI08";

        private static string Bre_Name = "運動儀表板";

        /// <summary>
        /// 會員相關資訊
        /// </summary>
        private UserProfile user;

        private string DefaultSCHOOL_NO = string.Empty;
        private string USER_NO = string.Empty;

        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();
        private SECI08Service seci08_service = new SECI08Service();

        public ActionResult _Menu(string NowAction)
        {
            ViewBag.NowAction = NowAction;
            return PartialView();
        }

        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult TotalRunLog(ADDI11TotalRunLogViewModel model)
        {
            // 全校跑步
            this.Shared("全校跑步情形");

            model.runLogViewType = TotalRunLogType.全校跑步情形;
            model.whereSCHOOL_NO = user.SCHOOL_NO;

            model = seci08_service.GetTotalRunLog(model, user, ref db);

            return View(model);
        }




        #region Shared

        private void Shared(string Panel_Title)
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = Bre_Name + " - " + Panel_Title;

            user = UserProfileHelper.Get();
            DefaultSCHOOL_NO = UserProfileHelper.GetSchoolNo();

            if (user != null)
            {
                if (user.SCHOOL_NO != null)
                {
                    DefaultSCHOOL_NO = user.SCHOOL_NO;
                    USER_NO = user.USER_NO;
                }
            }
        }

        #endregion Shared

    }
}