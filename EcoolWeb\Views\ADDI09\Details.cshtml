﻿@model ECOOL_APP.com.ecool.Models.DTO.ADDI09DetailsViewModel
@using ECOOL_APP.com.ecool.Models.DTO;

<link href="@Url.Content("~/Content/css/EzCss.min.css")" rel="stylesheet" />
@{
    ViewBag.Title = ViewBag.Panel_Title;

}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@using (Html.BeginForm((string)ViewBag.Action, (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", enctype = "multipart/form-data" }))
{

    @Html.HiddenFor(model => model.Search.Page)
    @Html.HiddenFor(model => model.Search.OrderByName)
    @Html.HiddenFor(model => model.Search.SyntaxName)
    @Html.HiddenFor(model => model.Search.BATCH_CASH_ID)
    @Html.HiddenFor(model => model.Search.SearchContents)
    @Html.HiddenFor(model => model.Search.SYS_TABLE_TYPE)

    @Html.Hidden("ThisSaveBATCH_CASH_ID", (string)ViewBag.ThisSaveBATCH_CASH_ID)
}

<img src="~/Content/img/web-bar2-revise-22.png" style="width:100%" class="img-responsive " alt="Responsive image" />
<div class="Div-EZ-ADDI09">
    <div class="form-horizontal">
        @if (Model.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OTHER)
        {
            @Html.Action("_DetailsOTHER", (string)ViewBag.BRE_NO, Model.aADDT20)
        }
        else if (Model.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_IN)
        {
            @Html.Action("_DetailsIN", (string)ViewBag.BRE_NO, Model.aADDT14)
        }
        else if (Model.Search.SYS_TABLE_TYPE == ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OBC)
        {
            @Html.Action("_DetailsOBC", (string)ViewBag.BRE_NO, Model.aADDT15)
        }
    </div>
</div>
<div style="height:15px"></div>
<div class="text-center">
    <button class="btn btn-default" id="myButton">回上一頁</button>
</div>

 

<script type="text/javascript">
    $('#myButton').on('click', function () {
        var $btn = $(this).button('loading....')
        form1.action = '@Html.Raw(@Url.Action("ListView", (string)ViewBag.BRE_NO))'
        form1.submit();
    })
</script>

