﻿/**
 * @license Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
 * For licensing, see LICENSE.md or http://ckeditor.com/license
 */

CKEDITOR.plugins.setLang( 'specialchar', 'tt', {
	euro: 'Евро тамгасы',
	lsquo: 'Сул бер иңле куштырнаклар',
	rsquo: 'Уң бер иңле куштырнаклар',
	ldquo: 'Сул ике иңле куштырнаклар',
	rdquo: 'Уң ике иңле куштырнаклар',
	ndash: 'Кыска сызык',
	mdash: 'Озын сызык',
	iexcl: 'Әйләндерелгән өндәү билгесе',
	cent: 'Цент тамгасы',
	pound: 'Фунт тамгасы',
	curren: 'Акча берәмлеге тамгасы',
	yen: '<PERSON>ена тамгасы',
	brvbar: 'Broken bar', // MISSING
	sect: 'Парагра<PERSON> би<PERSON>',
	uml: 'Диерезис',
	copy: 'Хокук иясе булу билгесе',
	ordf: 'Feminine ordinal indicator', // MISSING
	laquo: 'Ачылучы чыршысыман җәя',
	not: 'Юклык ишарəсе',
	reg: 'Теркәләнгән булу билгесе',
	macr: 'Макрон',
	deg: 'Градус билгесе',
	sup2: 'Икенче өске индекс',
	sup3: 'Өченче өске индекс',
	acute: 'Басым билгесе',
	micro: 'Микро билгесе',
	para: 'Параграф билгесе',
	middot: 'Уртадагы нокта',
	cedil: 'Седиль',
	sup1: 'Беренче өске индекс',
	ordm: 'Masculine ordinal indicator', // MISSING
	raquo: 'Ябылучы чыршысыман җәя',
	frac14: 'Гади дүрттән бер билгесе',
	frac12: 'Гади икедән бер билгесе',
	frac34: 'Гади дүрттән өч билгесе',
	iquest: 'Әйләндерелгән өндәү билгесе',
	Agrave: 'Гравис белән латин A баш хәрефе',
	Aacute: 'Басым билгесе белән латин A баш хәрефе',
	Acirc: 'Циркумфлекс белән латин A баш хәрефе',
	Atilde: 'Тильда белән латин A баш хәрефе',
	Auml: 'Диерезис белән латин A баш хәрефе',
	Aring: 'Өстендә боҗра булган латин A баш хәрефе',
	AElig: 'Латин Æ баш хәрефе',
	Ccedil: 'Седиль белән латин C баш хәрефе',
	Egrave: 'Гравис белән латин E баш хәрефе',
	Eacute: 'Басым билгесе белән латин E баш хәрефе',
	Ecirc: 'Циркумфлекс белән латин E баш хәрефе',
	Euml: 'Диерезис белән латин E баш хәрефе',
	Igrave: 'Гравис белән латин I баш хәрефе',
	Iacute: 'Басым билгесе белән латин I баш хәрефе',
	Icirc: 'Циркумфлекс белән латин I баш хәрефе',
	Iuml: 'Диерезис белән латин I баш хәрефе',
	ETH: 'Латин Eth баш хәрефе',
	Ntilde: 'Тильда белән латин N баш хәрефе',
	Ograve: 'Гравис белән латин O баш хәрефе',
	Oacute: 'Басым билгесе белән латин O баш хәрефе',
	Ocirc: 'Циркумфлекс белән латин O баш хәрефе',
	Otilde: 'Тильда белән латин O баш хәрефе',
	Ouml: 'Диерезис белән латин O баш хәрефе',
	times: 'Тапкырлау билгесе',
	Oslash: 'Сызык белән латин O баш хәрефе',
	Ugrave: 'Гравис белән латин U баш хәрефе',
	Uacute: 'Басым билгесе белән латин U баш хәрефе',
	Ucirc: 'Циркумфлекс белән латин U баш хәрефе',
	Uuml: 'Диерезис белән латин U баш хәрефе',
	Yacute: 'Басым билгесе белән латин Y баш хәрефе',
	THORN: 'Латин Thorn баш хәрефе',
	szlig: 'Латин beta юл хәрефе',
	agrave: 'Гравис белән латин a юл хәрефе',
	aacute: 'Басым билгесе белән латин a юл хәрефе',
	acirc: 'Циркумфлекс белән латин a юл хәрефе',
	atilde: 'Тильда белән латин a юл хәрефе',
	auml: 'Диерезис белән латин a юл хәрефе',
	aring: 'Өстендә боҗра булган латин a юл хәрефе',
	aelig: 'Латин æ юл хәрефе',
	ccedil: 'Седиль белән латин c юл хәрефе',
	egrave: 'Гравис белән латин e юл хәрефе',
	eacute: 'Басым билгесе белән латин e юл хәрефе',
	ecirc: 'Циркумфлекс белән латин e юл хәрефе',
	euml: 'Диерезис белән латин e юл хәрефе',
	igrave: 'Гравис белән латин i юл хәрефе',
	iacute: 'Басым билгесе белән латин i юл хәрефе',
	icirc: 'Циркумфлекс белән латин i юл хәрефе',
	iuml: 'Диерезис белән латин i юл хәрефе',
	eth: 'Латин eth юл хәрефе',
	ntilde: 'Тильда белән латин n юл хәрефе',
	ograve: 'Гравис белән латин o юл хәрефе',
	oacute: 'Басым билгесе белән латин o юл хәрефе',
	ocirc: 'Циркумфлекс белән латин o юл хәрефе',
	otilde: 'Тильда белән латин o юл хәрефе',
	ouml: 'Диерезис белән латин o юл хәрефе',
	divide: 'Бүлү билгесе',
	oslash: 'Сызык белән латин o юл хәрефе',
	ugrave: 'Гравис белән латин u юл хәрефе',
	uacute: 'Басым билгесе белән латин u юл хәрефе',
	ucirc: 'Циркумфлекс белән латин u юл хәрефе',
	uuml: 'Диерезис белән латин u юл хәрефе',
	yacute: 'Басым билгесе белән латин y юл хәрефе',
	thorn: 'Латин thorn юл хәрефе',
	yuml: 'Диерезис белән латин y юл хәрефе',
	OElig: 'Латин лигатура OE баш хәрефе',
	oelig: 'Латин лигатура oe юл хәрефе',
	'372': 'Циркумфлекс белән латин W баш хәрефе',
	'374': 'Циркумфлекс белән латин Y баш хәрефе',
	'373': 'Циркумфлекс белән латин w юл хәрефе',
	'375': 'Циркумфлекс белән латин y юл хәрефе',
	sbquo: 'Single low-9 quotation mark', // MISSING
	'8219': 'Single high-reversed-9 quotation mark', // MISSING
	bdquo: 'Double low-9 quotation mark', // MISSING
	hellip: 'Ятма эллипс',
	trade: 'Сәүдә маркасы билгесе',
	'9658': 'Black right-pointing pointer', // MISSING
	bull: 'Маркер',
	rarr: 'Уң якка ук',
	rArr: 'Уң якка икеләтә ук',
	hArr: 'Ике якка икеләтә ук',
	diams: 'Black diamond suit', // MISSING
	asymp: 'якынча'
} );
