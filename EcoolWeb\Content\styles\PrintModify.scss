@import "../../node_modules/bootstrap/scss/bootstrap-grid";
body {
    padding: 0;
    margin: 0;
}

.text-number {
    font-size: 1.25rem;
    font-weight: bold;
    font-family: monospace;
}

.text-pink {
    color: #d74494;
}

.text-light {
    color: #db0000;
}

.text-info {
    color: #0032c1;
}

.p-1 {
    padding: 0.5rem;
}

.print-modify {
    &-list {
        display: block;
        margin: 0 auto;
        padding: 0 .5cm;
        max-width: 800px;

        >li {
            position: relative;
            float: left;
            display: block;
            height: 9.6cm;
            padding: 1.3rem 1rem;
            background-image: url(../images/print-modify-bg1.png), linear-gradient(to bottom, #fff 70%, #ffe5cb 100%);
            background-repeat: no-repeat;
            background-position: 0 0;
            background-size: 100% auto, 100% 100%;
            border: 1px #cdcdcd solid;
            page-break-after: avoid;
            overflow: hidden;

            &::after {
                position: absolute;
                bottom: -10px;
                right: -10px;
                left: -10px;
                content: "";
                display: block;
                border-top: 20px dotted #fff;
            }

            ol {
                padding-left: 1em;
                line-height: 1.2;
            }

            .txt {
                line-height: 1.4;
                font-size: 0.725rem;
                width: 98%;
                margin: 0 auto;
            }

            .text-date {
                font-size: .875rem;
                font-weight: bold;
                letter-spacing: 0;
                text-align: center;
                margin: 0 0 0.3rem 0;
            }
        }

        .QRCode {
            display: inline-block;
            width: 100%;
            max-width: 3.5cm;
            height: auto;
        }

        .barCode {
            display: block;
            width: 85%;
            height: auto;
            margin: 0.5rem auto 0 auto;
        }


    }

    &-title {
        padding: 0;
        margin: 0;
        font-size: 1.125rem;
        line-height: 1.2;
        font-weight: bold;
    }

    &-icon {
        &::before {
            content: "";
            display: inline-block;
            height: 1em;
            width: 1em;
            margin-left: 0.5rem;
            background-image: url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg id='uuid-e48140a9-dc4b-447e-b0c4-bc1081f23703' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 13.61 13.61'%3E%3Cdefs%3E%3Cstyle%3E.uuid-047b6f30-d66c-441a-9193-b5721a6814d8%7Bfill:%23ea5514;%7D%3C/style%3E%3C/defs%3E%3Cpolygon class='uuid-047b6f30-d66c-441a-9193-b5721a6814d8' points='2.89 5.81 7.57 .04 9.41 3.13 2.89 5.81'/%3E%3Cpolygon class='uuid-047b6f30-d66c-441a-9193-b5721a6814d8' points='2.79 7.74 10.21 7.39 9.14 10.82 2.79 7.74'/%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-size: 100% auto;
        }
    }

    &-list-sixEqual {
        max-width: 600px;

        >li {
            background-image: url(../images/print-modify-bg.png), linear-gradient(to bottom, #fff 70%, #ffdc7f 100%);
            padding: 0.5cm 0.5cm 0 0.5cm;
            height: 7cm;

            .text-date {
                margin: 0.25rem 0;
            }
        }
    }
}


@media print {

    html,
    body {
        padding: 0;
        margin: 0;
        -webkit-print-color-adjust: exact;
    }

    .print-modify-list {
        >li {
            page-break-inside: avoid;

        }

        >li:nth-child(4n) {
            page-break-after: always; //在標籤後換頁
        }

        img {
            page-break-inside: avoid;
        }
    }

}

@page {
    size: A4 portrait;
    padding: 0;
    margin: 1rem 0 0 0;
}