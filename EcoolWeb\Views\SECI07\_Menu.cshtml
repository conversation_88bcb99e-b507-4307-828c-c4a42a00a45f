﻿@{
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
}

<div class="form-group">
    <a role="button" href='@Url.Action("RankOfClassBookBorrow",(string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys @(ViewBag.NowAction=="RankOfClassBookBorrow" ? "active":"")">
        各班借書統計
    </a>

    <a role="button" href='@Url.Action("RankOfGradeBookBorrow",(string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys @(ViewBag.NowAction=="RankOfGradeBookBorrow" ? "active":"")">
        各年級借書排行榜
    </a>

    @if (HRMT24_ENUM.CheckQAdmin(user))
    {
        <a role="button" href='@Url.Action("RankOfSchoolBookBorrow",(string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys @(ViewBag.NowAction=="RankOfSchoolBookBorrow" ? "active":"")">
            學校借書統計
        </a>
    }

    @*<a role="button" href='@Url.Action("ClassBookBorrow",(string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys @(ViewBag.NowAction=="ClassBookBorrow" ? "active":"")">
        各班借書統計
    </a>*@
    <a role="button" href='@Url.Action("BookFlowTime",(string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys @(ViewBag.NowAction=="BookFlowTime" ? "active":"")">
        借/還書時間點統計
    </a>
    <a role="button" href='@Url.Action("BookTypeAnalysis",(string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys @(ViewBag.NowAction=="BookTypeAnalysis" ? "active":"")">
        總借書類別分析
    </a>
    <a role="button" href='@Url.Action("MonthBestSeller",(string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys @(ViewBag.NowAction=="MonthBestSeller" ? "active":"")">
        月暢銷書
    </a>

    <a href="@Url.Action("CareBookIndex","SECI02")" role="button" class="btn btn-sm btn-sys @(ViewBag.NowAction=="CareBookIndex" ? "active":"")">
        關懷清單(借書)
    </a>

    @*<a href="@Url.Action("BorrowIndex","SECI02")" role="button" class="btn btn-sm btn-sys @(ViewBag.NowAction=="BorrowIndex" ? "active":"")">
        借書統計
    </a>*@

    <p style="color:red">「學生啟用後，可以看完整資訊」</p>
</div>