(function(factory){if (typeof define === 'function' && define.amd){define(['jquery'], factory);} else if (typeof module === 'object' && typeof module.exports === 'object'){module.exports = factory(require('jquery'));} else {factory(jQuery);}}(function(jQuery){

/*! Parser: weekday - updated 11/22/2015 (v2.24.6) */
!function(w){"use strict";var y=w.tablesorter;y.dates||(y.dates={}),y.dates.weekdays||(y.dates.weekdays={}),y.dates.weekdays.en={sun:"Sun",mon:"Mon",tue:"Tue",wed:"Wed",thu:"Thu",fri:"Fri",sat:"Sat"},y.dates.weekStartList={sun:"1995",mon:"1996",fri:"1999",sat:"2000"},y.dates.weekdaysXref=["sun","mon","tue","wed","thu","fri","sat"],y.addParser({id:"weekday",is:function(){return!1},format:function(e,t,a,r){if(e){var n,s,i,d=t.config,o=d.globalize&&(d.globalize[r]||d.globalize)||{},u=y.dates.weekdays[o.lang||"en"],f=y.dates.weekdaysXref;for(s in d.ignoreCase&&(e=e.toLowerCase()),u)if("string"==typeof s&&(n=u[s],d.ignoreCase&&(n=n.toLowerCase()),e.match(n)))return-1<(i=w.inArray(s,f))?i:e}return e},type:"numeric"}),y.addParser({id:"weekday-index",is:function(){return!1},format:function(e,t){if(e){var a=t.config,r=new Date(e);if(r instanceof Date&&isFinite(r))return new Date("1/"+(r.getDay()+1)+"/"+y.dates.weekStartList[a.weekStarts||"sun"])}return e},type:"numeric"})}(jQuery);return jQuery;}));
