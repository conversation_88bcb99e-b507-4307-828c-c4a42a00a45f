/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/SizeThreeSym/Regular/All.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.STIXSizeThreeSym,{710:[777,-564,1460,0,1460],711:[777,-564,1460,0,1460],732:[774,-608,1458,-2,1458],759:[-117,283,1458,-2,1458],773:[820,-770,0,-2000,0],780:[777,-564,0,-1610,-150],816:[-117,283,0,-1612,-152],818:[-127,177,0,-2000,0],824:[662,156,0,-543,-132],8254:[820,-770,2000,0,2000],8400:[749,-584,0,-1773,-29],8401:[749,-584,0,-1773,-29],8406:[735,-482,0,-1773,-29],8407:[735,-482,0,-1773,-29],8428:[-123,288,0,-1773,-29],8429:[-123,288,0,-1773,-29],8430:[-26,279,0,-1773,-29],8431:[-26,279,0,-1773,-29],8731:[2565,510,1076,112,1110],8732:[2565,510,1076,112,1110],9140:[766,-544,2147,78,2069],9141:[139,83,2147,78,2069],9180:[70,161,1886,0,1886],9181:[803,-572,1886,0,1886],9184:[66,212,2312,0,2312],9185:[842,-564,2312,0,2312],10098:[2066,393,842,265,790],10099:[2066,393,842,52,577],10214:[2066,394,647,225,597],10215:[2066,394,647,50,422],10218:[2067,394,1091,104,955],10219:[2067,394,1091,136,987],10627:[2066,394,1031,143,867],10628:[2066,394,1031,164,888],10629:[2066,393,1029,180,914],10630:[2066,393,1029,115,849]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/SizeThreeSym/Regular/All.js");
