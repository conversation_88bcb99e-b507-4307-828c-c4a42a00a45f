﻿@model BarcodeEditPeopleViewModel

@{
    ViewBag.Title = "點數領取-列表";
    Layout = "~/Views/Shared/_LayoutSEO.cshtml";

    string SCHOOL_NO = EcoolWeb.Models.UserProfileHelper.GetSchoolNo();

    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    string LogoAct = "GuestIndex";
    bool IsAllList = (this.Request.CurrentExecutionFilePath.Contains("ADDTALLListDetails"));
}

@Html.Partial("_Notice")
@*<center style="white-space: nowrap;font-size: 30pt;font-weight: bold;">
        程式修正中，請12月20日再兌換，造成不便，請包涵
    </center>

    <img src="~/Content/images/Sorry.PNG"  style="width:50%" class="img-responsive " alt="Responsive image" />*@
@if (IsAllList == false)
{
    <div class="row">
        @*   <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 font_Cash" style="white-space:nowrap">
                <img src="~/Content/img/web-revise-secretary-08new.png" style="max-height:40px" class="imgEZ" />
                酷幣點數：@Model.CASH_AVAILABLE
                @if (user != null && user.USER_TYPE == ECOOL_APP.EF.UserType.Teacher)
                {
                    <span class="text-danger">
                        &nbsp;&nbsp;
                        <img src="~/Content/img/web-revise-secretary-08new.png" style="max-height:40px" class="imgEZ" />
                        本月已發放點數: @Model.Month_Given_Cash 。@Model.Special__Cash_Limit
                    </span>
                }
            </div>*@

        @*@if (@Model.BOOKS > 0)
            {
                <div class="col-lg-4 col-md-4  col-sm-6 col-xs-12 font_Cash">
                    <img src="~/Content/img/books.png" style="max-width:40px;" class="imgEZ" />
                    <a id="BorrowIndexSesem" href='@Url.Action("BorrowIndex", (string)ViewBag.BRE_NO)'> <text>本學期借閱本數：</text>@Model.BOOKS</a>
                </div>
            }
            @if (@Model.BOOKS_MONTH >= 0)
            {
                <div class="col-lg-4 col-md-4  col-sm-6 col-xs-12 font_Cash">
                    <img src="~/Content/img/books.png" style="max-width:40px;" class="imgEZ" />
                    <a id="BorrowIndex" href='@Url.Action("BorrowIndex", (string)ViewBag.BRE_NO)'><text>本月借閱本數：</text>@Model.BOOKS_MONTH</a>
                </div>
            }*@
        @if (user != null)
        {
            if (user.USER_TYPE == ECOOL_APP.EF.UserType.Student)
            {
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 font_Cash">
                    <img src="~/Content/img/monkey-01.png" style="max-width:40px;max-height:40px;" class="imgEZ" />
                    <a href='@Url.Action("ArrivedChance2", "Home")'>
                        <text>好運次數：</text>

                        @user.Chance_ARRIVED_CASH
                    </a>
                </div>
            }
        }
    </div>
}
    <div class="row">
        <div class="col-sm-2 col-xs-12">
            <div>
                @if (!string.IsNullOrEmpty(Model.PlayerUrl))
                {
                    <img src="@(Url.Content(Model.PlayerUrl)+ "?refreshCache=" + DateTime.Now.ToString("mmddss"))" class="imgEZ" style="margin-top:30px;max-width:90%;" />
                }
            </div>
        </div>
        <div class="col-sm-10 col-xs-12">


            <div>
                @if ("ADDI13" == "ADDI13")
                {
                    <div style="height:25px"></div>
                    if (!AppMode)
                    {
                        <button type="button" class="btn btn-sm btn-bold btn-pink" onclick="printScreenH()">我要列印</button>
                    }
                    <button type="button" class="btn btn-sm btn-bold btn-pink" title="個人化編輯" id="MyPHOTO" href="@Url.Action("MyPhoto", (string)ViewBag.BRE_NO)">個人化編輯</button>
                    <button type="button" class="btn btn-sm btn-bold btn-pink" title="點數分析" id="PieChartbtn" href="@Url.Action("_PieChartDiv", (string)ViewBag.BRE_NO)">點數分析</button>
                    <button type="button" class="btn btn-sm btn-bold btn-pink" title="加值應用統計" id="Statisticalbtn" href="@Url.Action("_StatisticalDiv", (string)ViewBag.BRE_NO)">加值應用統計</button>
                    if (!AppMode)
                    {
                        <button type="button" class="btn btn-sm btn-bold btn-pink" onclick="GameCash()">取得點數</button>
                    }
                    else
                    {

                        <button type="button" class="btn btn-sm btn-bold btn-pink" title="取得點數" id="GameCash" href="@(Url.Action("GetStudentCashIndex", "ADDI13") + "?PRINT=Y&SCHOOL_NO1="+user.SCHOOL_NO)">取得點數</button>
                    }
                    if (Model.wIsQhisSchool && ViewBag.IsUseZZZI09 == true)
                    {
                        <button type="button" class="btn btn-sm btn-bold btn-pink" title="學習成果匯出(過去學校)" id="ExportResult" onclick="ExportResultWinOpen()">學習成果匯出(過去學校)</button>
                    }

                    if (Model.DATA_ANGLE_TYPE != EcoolWeb.Models.UserProfileHelper.AngleVal.OneData)
                    {
                        if (ViewBag.IsUseZZZI09 == true)
                        {
                            <button type="button" class="btn btn-sm btn-bold btn-pink" title="學習成果匯出" id="ExportResult" onclick="ExportResultWinOpen()">學習成果匯出</button>
                        }
                    }
                    else
                    {
                        <button type="button" class="btn btn-sm btn-bold btn-pink" title="我的線上投稿" onclick="funBookW()">我的線上投稿</button>


                        <a role="button" href='@Url.Action("BOOK_APPLY", (string)ViewBag.BRE_NO)' class="btn btn-sm btn-bold btn-pink">我的閱讀認證</a>
                        <a role="button" href='@Url.Action("Index3", (string)ViewBag.BRE_NO)' id="MyMOMO" class="btn btn-sm btn-bold btn-pink">我的默默等級</a>
                        <div style="display:none">
                            @{
                                int No = 1;
                                string IdName = string.Empty;

                                <div class="arrWRITING_NO">
                                    @foreach (var item in Model.arrWRITING_NO)
                                    {
                                        IdName = "W" + No.ToString();

                                        <a id="@IdName" class="groupWRITING_NO" href='@Url.Action("BOOK_Details", "ADDI01" , new {  WRITING_NO = item})'>
                                            @item
                                        </a>
                                        No++;
                                    }
                                </div>

                                No = 1;

                                <div class="arrAPPLY_NO">
                                    @foreach (var item in Model.arrAPPLY_NO)
                                    {
                                        IdName = "A" + No.ToString();

                                        <a id="@IdName" class="groupAPPLY_NO" href='@Url.Action("BOOK_Details", "ADDI01" , new {  WRITING_NO = item})'>
                                            @item
                                        </a>

                                        No++;
                                    }
                                </div>

                            }
                        </div>
                    }
                }
            </div>

            @if (Model != null)
            {

                //using (Html.BeginForm("_EditDetails3", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", id = "form1", @AutoComplete = "Off", @target = "_self" }))
                //{
                @Html.HiddenFor(m => m.SCHOOL_NO)
                @Html.HiddenFor(m => m.SHORT_NAME)
                @Html.HiddenFor(m => m.USER_NO)
                @Html.HiddenFor(m => m.NAME)
                @Html.HiddenFor(m => m.GRADE)
                @Html.HiddenFor(m => m.CLASS_NO)
                @Html.HiddenFor(m => m.SEAT_NO)
                @Html.HiddenFor(m => m.CARD_NO)
                @Html.HiddenFor(m => m.BarCode)
                @Html.HiddenFor(m => m.ROLL_CALL_NAME)
                @Html.HiddenFor(m => m.ROLL_CALL_ID)
                @Html.HiddenFor(m => m.CASH)
                @Html.HiddenFor(m => m.txtUSER_NO)
                @Html.HiddenFor(m => m.txtPASSWORD)

                <table align="center">
                    <tr>
                        <td align="center">
                            <br /><br />
                            <span style="white-space: nowrap;font-size: 30pt;font-weight: bold; color:blue">

                                恭喜 @Model.SHORT_NAME

                                同學 ，
                            </span>

                            <span style="white-space: nowrap;font-size: 30pt;font-weight: bold;">
                                因為
                            </span>
                            <span style="white-space: nowrap;font-size: 30pt;font-weight: bold; color:red">
                                @Model.ROLL_CALL_NAME 活動，
                            </span>       <br />
                            <span style="white-space: nowrap;font-size: 30pt;font-weight: bold;">
                                獲得酷幣點數
                            </span>

                            <span style="white-space: nowrap;font-size: 30pt;font-weight: bold;color:blue">


                                @Model.CASH 點


                            </span>
                            <br />
                            <span style="white-space: nowrap;font-size: 30pt;font-weight: bold;">  如有問題請洽    </span>
                            <span style="white-space: nowrap;font-size: 30pt;font-weight: bold;color:blue"> @Model.NAME </span>

                            <span style="white-space: nowrap;font-size: 30pt;font-weight: bold;">  老師</span>
                        </td>

                    </tr>
                    <tr>
                        <td align="center" style="white-space: nowrap;font-size: 16pt;font-weight: bold;color:blue">
                            <a style="margin: -15px 0px;" class="btn btn-default" href="@Url.Action("GetStudentCashIndex", "ADDI13",new { SCHOOL_NO1=Model.SCHOOL_NO})">回領取畫面</a>

                        </td>
                    </tr>
                    <tr></tr>

                </table>
                //}

            }
        </div>



    </div>

@section Scripts {

    <script src="~/Scripts/buzz/buzz.min.js"></script>

    <script language="JavaScript">
        var targetFormID = '#form1';

 var game_falseSound = new buzz.sound("@Url.Content("~/Content/mp3/game_false.mp3")");
          var game_trueSound = new buzz.sound("@Url.Content("~/Content/mp3/game_true.mp3")");

        (function ($) {

            $(window).on("beforeunload", function () {
    return true;
})
        })(jQuery);

        function ExportSave()
        {
            $(targetFormID).attr("action", "@Url.Action("Export", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        function onSave() {
             var OK = confirm("您確定點完名了?")

            if (OK==true)
            {

                 $(targetFormID).attr("target","_self")
                 $(targetFormID).attr("action", "@Url.Action("RollCallSave", (string)ViewBag.BRE_NO)")
                 $(targetFormID).submit();
            }
        }

        $(document).ready(function () {
            $("#CARD_NO").focus();

        });
        function OnclickCardNO() {

            var SCHOOL_NO1 = "";
            var CARD_NO = $('#CARD_NO').val();
            SCHOOL_NO1 = $("#SCHOOL_NO1").val();

            if (CARD_NO != '') {

                if ($("#Tr" + CARD_NO).length > 0 || $("#@(SCHOOL_NO)" + CARD_NO).length > 0) {
                    $('#ErrorStr').html('請勿重複刷卡，謝謝')
                    $('#ErrorDiv').show()

                    setTimeout(function () {
                        $('#CARD_NO').val('')
                        $("#CARD_NO").focus();
                        $('#ErrorStr').val('')
                        $('#ErrorDiv').hide()
                    }, 2000);
                }
                else {

                    $('#CARD_NO').prop('readonly', true);
                    $(".row").attr("style", "display:none");
                    setTimeout(function () {
                        OnKeyinUse(CARD_NO, SCHOOL_NO1)
                    });
                }

            }
        }
        function call(e, input) {
            var code = (e.keyCode ? e.keyCode : e.which);

            if (code == 13) // 13 是 Enter 按鍵的值
            {
                var SCHOOL_NO1 = "";
                var CARD_NO = $('#CARD_NO').val();
               SCHOOL_NO1 = $("#SCHOOL_NO1").val();
                event.preventDefault();

                if (CARD_NO != '') {

                    if ($("#Tr" + CARD_NO).length > 0 || $("#@(SCHOOL_NO)" + CARD_NO).length >0 ) {
                        $('#ErrorStr').html('請勿重複刷卡，謝謝')
                        $('#ErrorDiv').show()

                        setTimeout(function () {
                            $('#CARD_NO').val('')
                            $("#CARD_NO").focus();
                            $('#ErrorStr').val('')
                            $('#ErrorDiv').hide()
                        }, 2000);
                    }
                    else {

                        $('#CARD_NO').prop('readonly', true);
                        $(".row").attr("style", "display:none");
                        setTimeout(function () {
                            OnKeyinUse(CARD_NO, SCHOOL_NO1)
                        });
                    }

                }
            }
        }

        function OnKeyinUse(CARD_NO, SCHOOL_NO1) {
            $("#StatusMessageDiv").remove()
            $(window).unbind('beforeunload');
            var data = {
                "CARD_NO": CARD_NO,
                "SCHOOL_NO": SCHOOL_NO1
            };
            $("div[id='loading']").attr("style", "width: 100%; height: 100%; background-color: black; z-index: 999; position: fixed; left: 0px; top: 0px;");
            $.ajax({
                url: '@Url.Action("GetStudentCashPerson", (string)ViewBag.BRE_NO)',
                data: data,
                cache: false,
                success: function (html) {
                    $(".containerEZ").html('');

                    $(".containerEZ").html(html);
                    $("footer").remove();
                    $("div[style='width:auto;height:80px']").attr("style", "width:auto;height:50px")
                    console.log(html);
                    $('#loading').hide();
                    $('#ErrorDiv').show();



                    if (html.length > 0) {

                        var l = 0;
                        l = $("#StatusMessageDiv").length;
                        let pattern = /恭喜領取點數 成功！/i;
                        let result = $("#StatusMessageHtmlMsg").html().match(pattern);
                        if (result == "恭喜領取點數 成功！") {


                            game_trueSound.play();
                        }
                        else {
                            if ($("#StatusMessageHtmlMsg").html() != undefined) {
                                game_falseSound.play();
                            }
                        }
                        if (l > 0) {
                            var i = 0;
                            $(".row").each(function () {
                                if (i == 0) {
                                    $(this).html('');
                                }
                                i++;
                            })
                        }
                        $('#ErrorStr').html('感應成功…')

                        $("div[id='loading']").attr("style", "width: 100%; height: 100%; background-color: black; z-index: 999; position: fixed; left: 0px; top: 0px;display:none");
                        //SwipeOK()
                        setTimeout(function () {

                                $('#CARD_NO').prop('readonly', false);
                                $('#CARD_NO').val('')
                                $("#CARD_NO").focus();
                                $('#ErrorDiv').hide()
                                $('#ErrorStr').html('');


                        }, 100);
                    }
                    else {
                        $("div[id='loading']").attr("style", "width: 100%; height: 100%; background-color: black; z-index: 999; position: fixed; left: 0px; top: 0px;display:none");

                        $('#ErrorStr').html('此數位學生證對應不到學生資料…')
                        setTimeout(function () {
                            $('#CARD_NO').prop('readonly', false);
                            $('#CARD_NO').val('')
                            $("#CARD_NO").focus();
                            $('#ErrorDiv').hide()
                            $('#ErrorStr').html('');
                        }, 800);
                    }

                }
            });
        }

         function SwipeOK() {

                 SwipeSound.play();
         }

        //del 項目
        function deleteRow(Id) {
            $('#' + Id).remove();
        }
    </script>
}
