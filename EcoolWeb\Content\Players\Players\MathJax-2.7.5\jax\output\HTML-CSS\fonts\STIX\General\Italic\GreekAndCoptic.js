/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/General/Italic/GreekAndCoptic.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXGeneral-italic"],{900:[649,-494,289,160,322],901:[649,-494,333,70,387],902:[678,0,611,-51,564],903:[441,-330,333,150,261],904:[678,0,630,7,679],905:[678,0,740,4,821],906:[678,0,350,3,429],908:[678,18,722,58,699],910:[678,0,580,8,725],911:[678,0,762,-6,739],912:[649,11,278,49,387],913:[668,0,611,-51,564],914:[653,0,611,-8,588],917:[653,0,611,-1,634],918:[653,0,556,-6,606],919:[653,0,722,-8,769],921:[653,0,333,-8,384],922:[653,0,667,7,722],924:[653,0,833,-18,872],925:[653,15,667,-20,727],927:[667,18,722,60,699],929:[653,0,611,0,605],932:[653,0,556,59,633],935:[653,0,611,-29,655],938:[856,0,333,-8,460],939:[856,0,556,78,648],940:[649,11,552,27,549],941:[649,11,444,30,425],942:[649,205,474,14,442],943:[649,11,278,49,288],944:[649,10,478,19,446],970:[606,11,278,49,359],971:[606,10,478,19,446],972:[649,11,500,27,468],973:[649,10,478,19,446],974:[649,11,686,27,654],976:[694,10,456,45,436],978:[668,0,596,78,693],984:[667,205,722,60,699],985:[441,205,500,27,468],986:[666,207,673,55,665],987:[458,185,444,30,482],988:[653,0,557,8,645],989:[433,190,487,32,472],990:[773,18,645,19,675],991:[683,0,457,31,445],992:[666,207,708,7,668],993:[552,210,528,93,448],1008:[441,13,533,-16,559],1012:[667,18,722,60,699],1014:[441,11,444,24,414]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/Italic/GreekAndCoptic.js");
