﻿using ECOOL_APP;
using ECOOL_APP.EF;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using log4net;
using Newtonsoft.Json.Linq;
using System.Net;
using System.IO;
using EcoolWeb.ViewModels.SSO;
using PuppeteerSharp;

namespace EcoolWeb.Service
{
    public class SSO_TaipeiService
    {
        public SSOUserRepository Repository;

        public SSOTokenObject TokenObj { get; set; }
        public UserObject UserObj { get; set; }
        public dynamic ProfileObj { get; set; }
        public List<SchoolObject> schoolObjects { get; set; }
        public IDNOObject IDNOObjects { get; set; }
        public Relationship RelationshipObj { get; set; }
        private static ILog logger = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
    
        public SSO_TaipeiService(string code)
        {
           TokenObj = GetAccessToken(code);



            //logger.Info("access_token1" + TokenObj.access_token);
            //LogHelper.LogToTxt("json" + TokenObj);
//#if DEBUG
                 //    TokenObj = JsonConvert.DeserializeObject<SSOTokenObject>("{\"token_type\": \"Bearer\",\"expires_in\": 1209600, \"access_token\": \"eyJ0eXAiOiJKV1QiLCJqdGkiOiI1YzMwZTA1OGI3YzA0ZjUwODMwY2JiZmE0NjkzZmQyYSIsImFsZyI6IlJTMjU2In0.eyJzdWIiOiJUUF9FRFVfVE9LRU4iLCJqdGkiOiI1YzMwZTA1OGI3YzA0ZjUwODMwY2JiZmE0NjkzZmQyYSIsImlzcyI6Imh0dHBzOi8vbGRhcC50cC5lZHUudHciLCJpYXQiOjE3MDY1MzY5NzEsIm5iZiI6MTcwNjUzNjk3MSwiZXhwIjoxNzA2NjIzMzcxLCJhZHUiOjUzLCJzY29wZXMiOlsiZ3JvdXBfaW5mbyIsInNjaG9vbCIsInByb2ZpbGUiLCJ1c2VyIiwiaWRubyIsInBhcmVudF9pbmZvIl19.rclfSMZW9X7_ZFXxx88aNsff8-943_9-UXFxBlsyW66m-VHXv3RTbwIoLzRbv0s8tZgZERpUh6WkVcI7VMVrAu6HKtCEiotIoOG4bw-Njy9r-xhPSt30iHlIBjRvUfEUGP70Zl8BL-bwGvotnS-HkJEE6Ch2i5oQOr4guMjIIrPR3fVhVpbE2cBT1evBdOPHNgy8Trmw9QZmw8Z1bH_EdD3wEXOuekThDMjwCxwqoqeaSB-3iJFvL9_Nybq9DM9I62n4AFoNLjGZKtExTWq9X9FacN4yCQwga5T0RyC6xuH6t9N-Mh3qDfFZU-Yb8kVYS0_OObAQZzfGQmy4KGy59DHjBaYI6oSaA9unlIf1v1YQuDNFfHfedD1QYppvs-PcC9h3V0r2GwhDH4pfb5UogzWs5iAQ_RpqDNnN7RKkhZDxO-dgJaaIL1sMljs2m_d_-vEGqJ4BNU8V1yCkRv8LOJpQ1uCvX8691wGuwuzHQDOO_28FFQXHY51-rDJzvgrpJE7hUGkhC52_ah4Oi4cxzlH0QKdAaTwuLMHBHA_4WL1o2YQDytDzPI8tZarjESJUDLlPe0qGYOPNUE3sN6BD1ZbpwtzUMueQ-lfKrXyGf6FrAhVTPyIOWt_mElVW0wvWg2UM3Uv7PyEikCXugzh9TLiq7HlnpNMOnlEEp5y3q1I\"}");
//#endif

            UserObj = GetUser(TokenObj);
        //   UserObj = JsonConvert.DeserializeObject<UserObject>("{\"role\":\"家長\",\"gender\":null,\"birthDate\":null,\"o\":null,\"organization\":null,\"character\":null,\"error\":null}");
            logger.Info("access_token1" + UserObj);
            logger.Info("access_token1" + TokenObj.access_token);
            ProfileObj = GetProfile(TokenObj);
           // ProfileObj = JsonConvert.DeserializeObject<dynamic>("{\"role\":\"家長\",\"uuid\":\"eccd7d08-7bc9-4b7c-b748-f5fc8a036d4a\",\"name\":\"孫茹\",\"email\":\"<EMAIL>\",\"mobile\":\"0920196511\",\"email_login\":false,\"mobile_login\":false}");

            schoolObjects = GetSchools(TokenObj);
            IDNOObjects = GetIDNO(TokenObj);
            Repository = new SSOUserRepository();
        }

        public SSO_TaipeiService()
        {
            Repository = new SSOUserRepository();
        }

#region link to SSO ask for infos methodds

        public SSOTokenObject GetAccessToken(string code)
        {
            SSOTokenObject SSOTokenObject = null;
            try
            {
                using (var client = new HttpClient())
                {
                    logger.Info("code");
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic",
                        Convert.ToBase64String(Encoding.Default.GetBytes($"{SSOConfig.Keys.CLIENT_ID}:{SSOConfig.Keys.CLIENT_SECRET}"))
                        );
                    // content-type 加到httpclient時要關閉驗證 => (否則要加到HttpContent內)
                    client.DefaultRequestHeaders.TryAddWithoutValidation("Content-type", "application/x-www-form-urlencoded");
                    client.DefaultRequestHeaders.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));

                    var formdata = new List<KeyValuePair<string, string>>();
                    formdata.Add(new KeyValuePair<string, string>("grant_type", "authorization_code"));
                    formdata.Add(new KeyValuePair<string, string>("code", code));
                    formdata.Add(new KeyValuePair<string, string>("client_id", SSOConfig.Keys.CLIENT_ID));
                    formdata.Add(new KeyValuePair<string, string>("client_secret", SSOConfig.Keys.CLIENT_SECRET));
                    formdata.Add(new KeyValuePair<string, string>("redirect_uri", SSOConfig.Keys.REDIRECT_URI));

                    var req = new HttpRequestMessage(HttpMethod.Post, SSOConfig.Keys.TOKEN_ENDPOINT) { Content = new FormUrlEncodedContent(formdata) };
                    var res = client.SendAsync(req).Result;

                    SSOTokenObject = JsonConvert.DeserializeObject<SSOTokenObject>(
                        res.Content.ReadAsStringAsync().Result);

                    return SSOTokenObject;
                }
            }
            catch (Exception ex)
            {
                logger.Info(ex.InnerException);
                //LogHelper.LogToTxt("ex" + ex.InnerException);
            }
            return SSOTokenObject;
        }
     

        public Relationship GetIDNORelation(SSOTokenObject tokenObj, string IDNO) {

            if (tokenObj?.access_token == null) throw new ArgumentNullException("尚未取得Access Token");

            string ParesentRelation = SSOConfig.Keys.ParesentRelation_ENDPOINT + IDNO + "/Relationship";
            HttpWebRequest request = (HttpWebRequest)WebRequest.Create(ParesentRelation);
            request.Method = "POST";
            //request.ContentType = "application/x-www-form-urlencoded";
            //request.Headers.Add("Authorization", "Bearer" + enObj.access_token);
            request.ContentType = "application/json";
            request.Headers.Add("Authorization", "Bearer " + tokenObj.access_token);

            HttpWebResponse response = (HttpWebResponse)request.GetResponse();
            StreamReader reader = new StreamReader(response.GetResponseStream());
            string str = reader.ReadToEnd();

            RelationshipObj = JsonConvert.DeserializeObject<Relationship>(str);

            //log.Debug("GetRelationship child:" + str);
            response.Close();
            return RelationshipObj;
        }
            
        public IDNOObject GetIDNO(SSOTokenObject tokenObj)
        {
            if (tokenObj?.access_token == null) throw new ArgumentNullException("尚未取得Access Token");
            IDNOObject IDNOObjects = new IDNOObject();
            using (var client = new HttpClient())
            {
                client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", tokenObj.access_token);
                try
                {
                    var json = client.GetStringAsync(SSOConfig.Keys.IDNO_ENDPOINT).Result;
                    IDNOObjects = JsonConvert.DeserializeObject<IDNOObject>(json);
                    //  //LogHelper.LogToTxt("json" + json);
                    if (IDNOObjects == null || string.IsNullOrEmpty(IDNOObjects.idno))
                    {
                        //LogHelper.LogToTxt("json空");
                    }
                    else
                    {
                        //  //LogHelper.LogToTxt("json" + IDNOObjects.idno);
                    }
                }
                catch (Exception ex)
                {
                    //LogHelper.LogToTxt("ex" + ex.InnerException);
                }
                // //LogHelper.LogToTxt("codes3");

                return IDNOObjects;
            }
        }

        public UserObject GetUser(SSOTokenObject tokenObj)
        {
            UserObject userObj = null;
            if (tokenObj?.access_token == null) throw new ArgumentNullException("尚未取得Access Token");
            try {
                using (var client = new HttpClient())
                {
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", tokenObj.access_token);

                    var json = client.GetStringAsync(SSOConfig.Keys.USER_ENDPOINT).Result;
                    logger.Info(json);
                    logger.Error(json);
                  userObj = JsonConvert.DeserializeObject<UserObject>(json);
                    return userObj;
                }
            } catch (Exception e)

            {
                logger.Info("SSOGetUser" + e.InnerException);


            }
            return userObj;
        }

        public dynamic GetProfile(SSOTokenObject tokenObj)
        {
            dynamic profileObj = null;
           
            if (tokenObj?.access_token == null) throw new ArgumentNullException("尚未取得Access Token");
            try
            {
                using (var client = new HttpClient())
                {
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", tokenObj.access_token);

                    var json = client.GetStringAsync(SSOConfig.Keys.PROFILE_ENDPOINT).Result;
                    logger.Info(json);
                    logger.Error(json);
                     
                    profileObj = JsonConvert.DeserializeObject<dynamic>(json);
                  
                    return profileObj;
                }

            }
            catch (Exception e) {
                logger.Info("SO GetProfile "+e.InnerException);

            }
            return profileObj;
        }

        public UserObject GetSchoolOU(SSOTokenObject tokenObj)
        {
            if (tokenObj?.access_token == null) throw new ArgumentNullException("尚未取得Access Token");

            using (var client = new HttpClient())
            {
                client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", tokenObj.access_token);

                var json = client.GetStringAsync(SSOConfig.Keys.OU_ENDPOINT).Result;
                UserObject userObj = JsonConvert.DeserializeObject<UserObject>(json);
                return userObj;
            }
        }

        public List<SchoolObject> GetSchools(SSOTokenObject tokenObj)
        {
            if (tokenObj?.access_token == null) throw new ArgumentNullException("尚未取得Access Token");
            logger.Info("access_token"+tokenObj.access_token);
            List<SchoolObject> schoolObjList = new List<SchoolObject>();
            try
            {
                using (var client = new HttpClient())
                {
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", tokenObj.access_token);

                    var json = client.GetStringAsync(SSOConfig.Keys.SCHOOL_ENDPOINT).Result;
                  schoolObjList = JsonConvert.DeserializeObject<List<SchoolObject>>(json);
                    return schoolObjList;
                }
            }
            catch (Exception e) {
                logger.Info("SOGetSchools" + e.InnerException);


            }
            return schoolObjList;
        }

#endregion link to SSO ask for infos methodds

        public string GetIPAddress()
        {
            System.Web.HttpContext context = System.Web.HttpContext.Current;
            string ipAddress = context.Request.ServerVariables["HTTP_X_FORWARDED_FOR"];

            if (!string.IsNullOrEmpty(ipAddress))
            {
                string[] addresses = ipAddress.Split(',');
                if (addresses.Length != 0)
                {
                    return addresses[0];
                }
            }
            return context.Request.ServerVariables["REMOTE_ADDR"];
        }
        public List<T> GetDynamicToList<T>(dynamic Value)
        {
            List<T> dict = null;
            if (Value != null)
            {

                try
                {

                    string stringJson = JsonConvert.SerializeObject(Value);
                    if (stringJson.IndexOf("[") == 0)
                    {
                        JArray jsonResponse = JArray.Parse(stringJson);
                        dict = jsonResponse.ToObject<List<T>>();
                    }
                    else
                    {

                        var NewValue = (T)Convert.ChangeType(Value, typeof(T));
                        dict = new List<T>();
                        dict.Add(NewValue);
                    }

                }
                catch (Exception ex) { }

            }

            return dict;


        }
        /// <summary>
        ///
        /// </summary>
        /// <returns></returns>
        public List<HRMT01> AccessData(ref string accessToken)
        {
            string Msg = "";
            string school_no = "";
            string uuid="";
            string schoolName ="";
            string domainNm;
            List<HRMT01> hrt01s = new List<HRMT01>();
            List<string> schoolNamestring = new List<string>();
         logger.Info("AccessData");
            accessToken = TokenObj.access_token;
            logger.Info("SSO_TaipeiService access_token" + TokenObj.access_token);
            bool ISDouBLESCHOOL = false;
            if (ProfileObj != null ) {
               
                try
                {
                    logger.Info("AccessData1");
           
                    // logger.Info(values);
                    List<string> KeyNames = this.GetDynamicToList<string>(ProfileObj?.o);

                    var KeyNameList = KeyNames.Where(x => x != "bureau").ToList();
                    if (KeyNameList.Count > 1)
                    {

                        foreach (var keyDomainName in KeyNames) { 
                       string IDNO= IDNOObjects.idno;
                        BDMT01 bDMT01 = new BDMT01();
                        bDMT01 = Repository.FindUSERBYIDNO(IDNO);
                        logger.Info(UserObj.name+"異常，您有多間學校身份別");
                        domainNm = keyDomainName;
                        schoolName = ProfileObj?.organization[domainNm];
                        string school_no1 = "";
                        if (bDMT01 != null) {
                            school_no1 = schoolObjects.Where(x => x.description == schoolName).Select(x => x.tpUniformNumbers).FirstOrDefault();
                            if (school_no1 == bDMT01.SCHOOL_NO) {
                                school_no = school_no1;
                                ISDouBLESCHOOL = true;
                            }
                        }
                      
                        logger.Info(schoolName);
                        }
                    }
                    else {

                        domainNm = KeyNameList.FirstOrDefault();
                        schoolName = ProfileObj?.organization[domainNm];
                        logger.Info(schoolName);
                    }

                   
                  
                 

               



                }
                catch (Exception e) {
                    logger.Info("EX:" + e.InnerException);
                    try { schoolName = ProfileObj?.organization; }
                    catch (Exception ex) {

                        logger.Info("EX:" + ex.InnerException);
                    }
                  
                }
            }
            if (UserObj != null && (UserObj.name == "林美麗" || UserObj.name == "林小花"))
            {
                uuid = UserObj.uuid;
                if (UserObj.role == "老師")
                {
                    HRMT01 hRMT01 = Repository.TESTUSERTeacher(UserObj.name);
                    hRMT01.uuid = uuid;
                    hrt01s.Add(hRMT01);
                
                    return hrt01s;
                }
                else
                {
                    HRMT01 hRMT01 = Repository.TESTUSERSTUDENT(UserObj.name);
                    hRMT01.uuid = uuid;
                    hrt01s.Add(hRMT01);
        
                    return hrt01s;
                }
            }
            else if (schoolObjects != null && schoolObjects.Count() != 0&&!ISDouBLESCHOOL)
            {
                school_no = schoolObjects.Where(x => x.description == schoolName).Select(x => x.tpUniformNumbers).FirstOrDefault();
            }

            if (school_no == "" || schoolObjects == null || schoolObjects.Count() == 0)
            {
                HRMT01 hRMT01 = new HRMT01();
                hRMT01 = null;
                hrt01s = null;
                return hrt01s;
            }
            HRMT01 FindUser = null;
            try
            {
                if (UserObj != null && (UserObj.name == "林美麗" || UserObj.name == "林小花"|| UserObj.name == "莫里斯"))
            {
                    uuid = UserObj.uuid;
                    logger.Info(UserObj.name);
                if (UserObj.role == "老師")
                {
                    FindUser = Repository.TESTUSERTeacher(UserObj.name);
                        FindUser.uuid = uuid;
                    logger.Info(UserObj.role);
                }
                else
                {
                    logger.Info("無資料");
                    FindUser = Repository.TESTUSERSTUDENT(UserObj.name);
                }
                school_no = FindUser.SCHOOL_NO;
            }
           
            string IDNO = IDNOObjects.idno;
            logger.Info("IDNO"+IDNO);
                // 查詢學校
                BDMT01 school = new BDMT01();
            if (UserObj.role !="家長"){ 
                       school = Repository.FindSchoolByNO(school_no);
                }

                string role= ProfileObj?.role;
            if (UserObj.role != "家長"&&school == null)
            {
                HRMT01 hRMT01 = new HRMT01();
                hRMT01 = null;
                    hrt01s = null;
                    return hrt01s;
            }

           else if (UserObj.role == "家長")
            {
                    uuid = UserObj.uuid;
                    Relationship RelationshipInfo = new Relationship();
                    logger.Info("RelationshipInfo");
                    RelationshipInfo = GetIDNORelation(TokenObj, IDNO);
                    if (RelationshipInfo != null) {
                        logger.Info("RelationshipInfo is not null");
                        List<DATA_LISTDetail> DATA_LISTDetailItems = new List<DATA_LISTDetail>();
                        DATA_LISTDetailItems = RelationshipInfo.DATA_LIST;
                        if (DATA_LISTDetailItems != null && DATA_LISTDetailItems.Count!= 0)
                        {

                            foreach (var item in DATA_LISTDetailItems) {
                                logger.Info("SchoolCode" + item.SchoolCode);
                                school = Repository.FindSchoolByNO(item.SchoolCode); 
                              logger.Info("DATA_LISTDetail");

                               string ChildIDNumber = "";
                                ChildIDNumber = item.IDNumber;
                                HRMT01 ChildUSER = new HRMT01();
                                logger.Info("DATA_LISTDetail"+ ChildIDNumber);
                                ChildUSER =Repository.FindSTUDENT(item.SchoolCode, ChildIDNumber);
                                if (ChildUSER != null) {
                                    string ParentUSER_NO= "A" + ChildUSER.USER_NO;
                                    logger.Info("ParentUSER_NO" + ParentUSER_NO);
                                    FindUser = Repository.FindParent(item.SchoolCode, ParentUSER_NO);
                                    FindUser.uuid = uuid;
                                    FindUser.IDNO = ChildIDNumber;
                                    hrt01s.Add(FindUser);
                                  
                                }
                            }
                            return hrt01s;
                        }
                      
                        return hrt01s;
                    }
            }
          else  if (UserObj.role == "學生" || role == "學生")
            {
                    uuid = UserObj.uuid;
                    //LogHelper.LogToTxt("學生" + school.SCHOOL_NO);
                    //LogHelper.LogToTxt(IDNO);
                    FindUser = Repository.FindSTUDENT(school.SCHOOL_NO, IDNO);
                    FindUser.uuid = uuid;
                    hrt01s.Add(FindUser);
                    return hrt01s;
                }
            else
            {
                //LogHelper.LogToTxt("老師" + school.SCHOOL_NO);
                //LogHelper.LogToTxt("老師" + IDNO);
                logger.Info("老師" + IDNO);
                FindUser = Repository.FindTeacher(school.SCHOOL_NO, IDNO);
                    FindUser.uuid = uuid;
                    hrt01s.Add(FindUser);
                    return hrt01s;
                    //return FindUser;
                }
                //if (UserObj.role == "學生" || ProfileObj.role == "學生") return null;

                //LogHelper.LogToTxt("老師");
            }
            catch (Exception e) {
         
                logger.Info("EXCE" + e.InnerException);
                hrt01s = null;
                return hrt01s;
               // return FindUser;

            }
            return hrt01s;
         //   return FindUser;
            //aTeacher.USER_NO = userno,
            // E_MAIL = UserObj.email,
            // FULL_NAME = UserObj.name,
            // CITY_ID = school?.CITY_ID,
            // SS_ID = school?.SS_ID,
            // SCHOOL_NO = school?.SCHOOL_NO,
            // STATUS = FGDT02.StatusVal.Confirmed,
            // ROLE_ID = HRMT24.RoleVal.TeacherLed,
            // QSHOW_ROLE = HRMT24.RoleVal.TeacherLed,
            // USER_STATUS = UserStatus.OK,
            // INIT_STATUS = InitStatus.NotEdit,
            // JOB_TITLE = UserObj.role,
            // TEL = UserObj.mobile,
            // CRE_PERSON = UserObj.email,
            // CHG_PERSON = UserObj.email,
            // CRE_DATE = today,
            // CHG_DATE = today,
            // SchoolName = school?.SHORT_NAME
        }
    }

#region For SSO Models
   
    public static class SSOConfig
    {
        public static SSOConfigKey Keys { get; set; }
        private static readonly string _locatingPath = AppDomain.CurrentDomain.BaseDirectory + @"\bin\taipei_sso_config.json";

        static SSOConfig()
        {
            var jsonText = System.IO.File.ReadAllText(_locatingPath);
            Keys = Newtonsoft.Json.JsonConvert.DeserializeObject<SSOConfigKey>(jsonText);
        }

        public class SSOConfigKey
        {
            public string CLIENT_ID { get; set; }
            public string CLIENT_SECRET { get; set; }
            public string ALLOWED_SCOPES { get; set; }
            public string AUTH_ENDPOINT { get; set; }
            public string TOKEN_ENDPOINT { get; set; }
            public string PROFILE_ENDPOINT { get; set; }
            public string USER_ENDPOINT { get; set; }
            public string SCHOOL_ENDPOINT { get; set; }
            public string REDIRECT_URI { get; set; }
            public string OU_ENDPOINT { get; set; }
            public string IDNO_ENDPOINT { get; set; }
            public string ParesentRelation_ENDPOINT { get; set; }
        }
    }

    public class SSOTokenObject
    {
        public string access_token { get; set; }
        public string token_type { get; set; }
        public long expires_in { get; set; }
        public string refresh_token { get; set; }
    }

    public class ouObject
    {
        public string ou { get; set; }
        public string description { get; set; }
    }

    public class UserObject
    {
        public string role { get; set; }
        public string uuid { get; set; }
        public string name { get; set; }
        public string email { get; set; }
        public bool email_login { get; set; }
        public string mobile { get; set; }
        public bool mobile_login { get; set; }
    }

    public class IDNOObject
    {
        public string idno { get; set; }
    }

    public class ProfileObject
    {
        // 共通
        public string role { get; set; }

        public int gender { get; set; }
        public string birthDate { get; set; }
        public string o { get; set; }
        public string organization { get; set; }
        public string character { get; set; }

        // 老師屬性
        public string unit { get; set; }

        public string title { get; set; }
        public List<teachClassDic> teachClass { get; set; }

        // 學生屬性
        public string studentId { get; set; }

        public string @class { get; set; }
        public string className { get; set; }
        public string seat { get; set; }

        public class teachClassDic
        {
            public string key { get; set; }
            public string nae { get; set; }
        }
    }

    public class SchoolObject
    {
        //學校或機構代號
        public string o { get; set; }

        //會計統一編號
        public string tpUniformNumbers { get; set; }

        //學校全銜
        public string description { get; set; }
    }

#endregion For SSO Models

    public class SSOUserRepository
    {
        public ECOOL_DEVEntities db = new ECOOL_DEVEntities();

        public List<BDMT01> FindAllSchool()
        {
            return db.BDMT01.ToList();
        }

        public BDMT01 FindSchoolByNO(string schoolno)
        {
            return db.BDMT01.FirstOrDefault(s => s.SCHOOL_NO == schoolno);
        }

        public BDMT01 FindSchoolByName(string schoolname)
        {
            return db.BDMT01.FirstOrDefault(s => s.SCHOOL_NAME == schoolname);
        }
        public BDMT01 FindUSERBYIDNO(string IDNO) {
            string SCHOOLNO=
            db.HRMT01.Where(x => x.IDNO == IDNO && x.USER_STATUS == UserStaus.Enabled).Select(x => x.SCHOOL_NO).FirstOrDefault();
            if (SCHOOLNO != "" && SCHOOLNO != null) {
                return db.BDMT01.Where
                (x => x.SCHOOL_NO == SCHOOLNO).FirstOrDefault();

            }
            else{

                return null;
            }
           
        }
        public HRMT01 TESTUSERTeacher(string username)
        {
            HRMT01 aTeacher =

              db.HRMT01.Include("HRMT25").Where(a => a.NAME == username && a.USER_STATUS != UserStaus.Disable).FirstOrDefault();

            return aTeacher;
        }

        public HRMT01 TESTUSERSTUDENT(string username)
        {
            HRMT01 aSTUDENT =

              db.HRMT01.Include("HRMT25").Where(a => a.NAME == username && a.USER_STATUS != UserStaus.Disable).FirstOrDefault();

            return aSTUDENT;
        }

        internal HRMT01 FindTeacher(string sCHOOL_NO, string IDNO)
        {
            HRMT01 aTeacher =
            db.HRMT01.Include("HRMT25").Where(a => a.SCHOOL_NO == sCHOOL_NO && a.IDNO == IDNO && a.USER_TYPE == UserType.Teacher && a.USER_STATUS != UserStaus.Disable).FirstOrDefault();

            return aTeacher;
        }

        internal HRMT01 FindSTUDENT(string sCHOOL_NO, string IDNO)
        {
            HRMT01 aSTUDENT =
            db.HRMT01.Include("HRMT25").Where(a => a.SCHOOL_NO == sCHOOL_NO && a.IDNO == IDNO && a.USER_TYPE == UserType.Student && a.USER_STATUS != UserStaus.Disable).FirstOrDefault();

            return aSTUDENT;
        }

        internal HRMT01 FindParent(string sCHOOL_NO, string USER_NO)
        {
            HRMT01 aSTUDENT =
            db.HRMT01.Include("HRMT25").Where(a => a.SCHOOL_NO == sCHOOL_NO && a.USER_NO == USER_NO && a.USER_TYPE == UserType.Parents && a.USER_STATUS != UserStaus.Disable).FirstOrDefault();

            return aSTUDENT;
        }
    }
}