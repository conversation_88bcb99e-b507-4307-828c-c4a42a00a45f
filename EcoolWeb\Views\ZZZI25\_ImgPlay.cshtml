﻿@model List<ECOOL_APP.com.ecool.Models.entity.uADDT19>
<style type="text/css">
    .pics {
        padding: 0;
        margin: 0;
        overflow: hidden;
    }

        .pics img {
            width: 350px;
            height: 250px;
            top: 0;
            left: 0;
        }
</style>
<script src="~/Scripts/jquery-ui.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.cycle/3.0.3/jquery.cycle.all.js"></script>

    @if (Model != null && Model.Count() != 0)
    {
        @:<div class="panel panel-Img" name="TOP" style="width:98%;text-align:center;margin: 0px auto">
            @:<div class="panel-heading text-center" style="height:25px">




                @:首頁輪播


            @:</div>
            @:<div class="pics m-auto">

                string ImgUrl = string.Empty;
                if (Model != null && Model.Count() != 0)
                {
                    foreach (var m in Model)
                    {
                        if (m.IMG_FILE != null && m.IMG_FILE != string.Empty)
                        {
                            ImgUrl = string.Format(@"{0}{1}\{2}", ViewBag.SYSUrl, m.IMG_ID, m.IMG_FILE);

                            if (m.IMG_LINK != null && m.IMG_LINK != string.Empty)
                            {

                                <div class="panel-body">
                                    <a href="@m.IMG_LINK" target="_blank">
                                        <p>@m.IMG_TITLE</p>
                                        <img src="@Url.Content(ImgUrl)" class="img-responsive" style="text-align:center;margin: 0px auto" />
                                    </a>
                                </div>

                            }
                            else
                            {
                               
                                <div class="panel-body">
                                    <p>@m.IMG_TITLE</p>
                                    <img src="@Url.Content(ImgUrl)" class="img-responsive" style="text-align:center;margin: 0px auto" />
                                </div>
                            }
                        }
                    }
                }
            @:</div>
        @:</div>
    }

<script>
    $(document).ready(function () {
        $('.pics').cycle({
            fx: 'fade',

            timeout: 500


        });
    });</script>