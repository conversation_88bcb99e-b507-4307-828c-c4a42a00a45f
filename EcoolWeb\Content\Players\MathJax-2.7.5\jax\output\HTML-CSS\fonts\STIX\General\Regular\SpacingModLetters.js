/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/General/Regular/SpacingModLetters.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.STIXGeneral,{688:[848,-336,378,7,365],689:[848,-336,378,7,365],690:[852,-169,300,44,244],691:[681,-336,252,5,252],692:[680,-335,277,10,257],693:[680,-168,325,10,338],694:[680,-335,390,6,379],695:[680,-331,520,6,512],696:[680,-176,370,14,361],697:[684,-421,208,90,257],698:[684,-421,305,19,324],699:[686,-443,333,79,218],700:[686,-443,333,79,218],701:[686,-443,333,79,218],702:[680,-485,198,35,163],703:[680,-485,198,35,163],704:[690,-295,326,23,303],705:[690,-295,326,23,303],706:[755,-419,317,33,285],707:[755,-419,317,33,285],708:[713,-461,317,-9,327],709:[713,-461,317,-9,327],712:[713,-448,278,119,159],716:[70,195,278,119,159],717:[-104,159,334,11,323],718:[-21,192,333,25,249],719:[-21,192,333,84,308],720:[460,-19,333,89,244],721:[460,-299,333,89,244],722:[365,-75,333,72,262],723:[365,-75,333,71,261],724:[205,-18,333,51,281],725:[205,-18,333,51,281],726:[218,-26,333,71,263],727:[144,-100,333,71,263],730:[711,-512,333,67,266],731:[0,165,333,64,249],733:[678,-507,333,-3,376],734:[443,-186,298,0,263],735:[662,-425,333,48,284],736:[684,-219,378,24,335],737:[848,-336,215,19,197],738:[681,-331,291,36,261],739:[680,-336,380,5,372],740:[850,-336,341,45,319],741:[662,0,413,48,373],742:[662,0,405,40,365],743:[662,0,405,40,365],744:[662,0,405,40,365],745:[662,0,405,40,365],748:[70,147,333,21,311],749:[665,-507,405,10,395],759:[-113,219,333,1,331]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/Regular/SpacingModLetters.js");
