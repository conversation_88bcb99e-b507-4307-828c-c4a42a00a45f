﻿@model ADDI11OrderListViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);

    if (Model != null)
    {
        if (Model.IsPrint)
        {
            Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
        }
    }
}
<script src="~/Scripts/Blob.js"></script>
<script src="~/Scripts/FileSaver.js"></script>
<script src="~/Scripts/tableexport.js"></script>
<link href="~/Scripts/timepicker/jquery-ui-timepicker-addon.css" rel="stylesheet" />
<script src="~/Scripts/timepicker/jquery-ui-timepicker-addon.js"></script>
<script src="~/Scripts/timepicker/jquery-ui-timepicker-zh-TW.js"></script>
<script src="~/Scripts/timepicker/jquery.ui.datepicker-zh-TW.js"></script>
@*<script src="~/Scripts/moment.js"></script>*@
<script src="~/Scripts/moment.min.js"></script>
@if (AppMode == false)
{
    @Html.Partial("_Title_Secondary")
}
@Html.Partial("_Notice")

@{
    Html.RenderAction("_RunMenuIstory", new { NowAction = "OrderListIstory" });
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
}


@using (Html.BeginForm("Index", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1" }))
{
    @Html.AntiForgeryToken()
    <div id="PageContent">
        @Html.Action("_PageOrderList", (string)ViewBag.BRE_NO)
    </div>
}



@section scripts{
    <script type="text/javascript">

        var targetFormID = '#form1';

          window.onload = function () {
                if ($('#@Html.IdFor(m=>m.IsPrint)').val() == "true" && $('#@Html.IdFor(m=>m.IsToExcel)').val() != "true"  ) {
                    window.print()
                }
        }


        $(function () {
            var MonthDEF = "";
            MonthDEF = $(".btn-pink.active").attr("Mouth");
            if (MonthDEF == "true") {
                $("#Sdate").attr("hidden", "hidden");
            }
                $('#ButtonExcel').click(function () {
                   // $('#@Html.IdFor(m=>m.IsToExcel)').val(true)
                    var blob = new Blob([document.getElementById('tbData').innerHTML], {
                        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8"
                    });
                    var strFile = "Report.xls";
                    saveAs(blob, strFile);
                    return false;
                });
                initDatepicker();
            });
                function initDatepicker() {
                   var opt = {
                        showMonthAfterYear: true,
                        format: moment().format('YYYY-MM-DD'),
                        showSecond: true,
                        showButtonPanel: true,
                        showTime: true,
                        beforeShow: function () {
                            setTimeout(
                                function () {
                                    $('#ui-datepicker-div').css("z-index", 15);
                                }, 100
                            );
                        },
                        onSelect: function (dateText, inst) {
                            $('#' + inst.id).attr('value', dateText);
                        }
                    };
                    $("#@Html.IdFor(m => m.WhereStartRunDate)").datetimepicker(opt);
                    $("#@Html.IdFor(m => m.WhereendRunDate)").datetimepicker(opt);
        }
        function PrintBooK()
        {
            $('#@Html.IdFor(m=>m.IsPrint)').val(true)
            $(targetFormID).attr('action','@Html.Raw(@Url.Action("OrderList", (string)ViewBag.BRE_NO))')
            $(targetFormID).attr('target', '_blank').submit().removeAttr('target');
            $('#@Html.IdFor(m=>m.IsPrint)').val(false)
        }

        function FunPageProc(page) {
            if ($(targetFormID).size() > 0) {
                $('#@Html.IdFor(m=>m.Page)').val(page)
                funAjax()
            }
        };
        function doUSERTYPEGet(val) {

            $("#@Html.IdFor(m=>m.WhereUserType)").val(val);
            FunPageProc(1)
        }
        function doMonthTop(val)
        {
            $("#@Html.IdFor(m=>m.WhereIsMonthTop)").val(val);
          

                 $("#Sdate").attr("hidden", "hidden");
           
                FunPageProc(1)
            }


        function doSearch(ColName, whereValue) {
            $("#" + ColName).val(whereValue);
            FunPageProc(1)
        }



        function doSort(SortCol) {
            $('#@Html.IdFor(m => m.OrdercColumn)').val(SortCol);
            FunPageProc(1)
        }

        //查詢
        function funAjax() {

            $.ajax({
                url: '@Url.Action("_PageOrderList", (string)ViewBag.BRE_NO)',
                data: $(targetFormID).serialize(),
                type: 'post',
                async: false,
                cache: false,
                dataType: 'html',
                success: function (data) {
                    $('#PageContent').html(data);
                }
            });
        }

        function todoClear() {
            ////重設

            $("#Q_Div").find(":input,:selected").each(function (i) {

                var type = $(this).attr('type')
                var InPreadonly = $(this).attr('readonly')
                var tag = this.tagName.toLowerCase(); // normalize case

                if (InPreadonly == false || InPreadonly == undefined) {

                    if (type == 'radio' || type == 'checkbox') {
                        if ($(this).attr("title") == 'Default') {
                            this.checked = true;
                        }
                        else {
                            this.checked = false;
                        }
                    }
                    else if (tag == 'select') { //下拉式選單
                        this.selectedIndex = 0;
                    }
                    else if (type == 'text' || type == 'hidden' || type == 'password' || type == 'textarea') {
                        this.value = '';
                    }
                }
            });

            FunPageProc(1);
        }

    </script>
}




