﻿@model GameTotalGraphDetailsViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

<div class="Div-EZ-ADDI05">
    <div class="Details">
        <div class="table-responsive">
            <div class="text-center">
                <table class="table-ecool table-92Per table-hover">
                    <caption class="Caption_Div_Left">
                        活動名稱：@Model.GameInfo.GAME_NAME<br />

                        @if (Model.IsSeeGroupId)
                        {
                            <span>
                                題目：@Html.Raw(HttpUtility.HtmlDecode(Model.AnsQInfo.G_SUBJECT))
                            </span><br />
                            <span>回答：@(Model.AnsInfo.LEVEL_NAME)</span>
                        }
                        <br /><br />
                    </caption>
                    <thead>
                        <tr>
                            <th>
                                學校
                            </th>
                            <th>
                                學年度
                            </th>
                            <th>
                                學期
                            </th>
                            <th>
                                班級
                            </th>
                            <th>
                                座號
                            </th>
                            <th>
                                姓名
                            </th>
                            <th>
                                答題日期
                            </th>
                            @if (Model.IsSeeGroupId == false)
                            {
                                <th>答對/答錯</th>
                            }
                        </tr>
                    </thead>
                    <tbody>
                        @if (Model.AnsPersons.Count > 0)
                        {
                            foreach (var item in Model.AnsPersons)
                            {
                                <tr>
                                    <td align="center">
                                        @if (!string.IsNullOrEmpty(item.SHORT_NAME))
                                        {
                                            @Html.DisplayFor(modelItem => item.SHORT_NAME)
                                        }
                                        else
                                        {
                                            <span>
                                                卡片
                                            </span>
                                        }
                                    </td>
                                    <td align="center">
                                        @Html.DisplayFor(modelItem => item.SYEAR)
                                    </td>
                                    <td align="center">
                                        @Html.DisplayFor(modelItem => item.SEMESTER)
                                    </td>
                                    <td align="center">
                                        @if (string.IsNullOrWhiteSpace(item.CLASS_NO))
                                        {
                                            <samp>-</samp>
                                        }
                                        else
                                        {
                                            @Html.DisplayFor(modelItem => item.CLASS_NO)
                                        }
                                    </td>
                                    <td align="center">
                                        @if (string.IsNullOrWhiteSpace(item.SEAT_NO))
                                        {
                                            <samp>-</samp>
                                        }
                                        else
                                        {
                                            @Html.DisplayFor(modelItem => item.SEAT_NO)
                                        }
                                    </td>
                                    <td align="center">
                                        @Html.DisplayFor(modelItem => item.SNAME)
                                    </td>
                                    <td align="center">
                                        @Html.DisplayFor(modelItem => item.CRE_DATE)
                                    </td>
                                    @if (Model.IsSeeGroupId == false)
                                    {
                                        <td align="center">
                                            @if ((item.SCORE ?? 0) >= 100)
                                            {
                                                <span>O</span>
                                            }
                                            else
                                            {
                                                <span>X</span>
                                            }
                                        </td>
                                    }
                                </tr>
                            }
                        }
                    </tbody>
                </table>
                <div style="height:15px"></div>
                <div class="btn-group btn-group-justified" role="group">
                    共 @(Model.AnsPersons?.Count()) 人
                </div>
                <div style="height:25px"></div>
            </div>
        </div>
    </div>
</div>