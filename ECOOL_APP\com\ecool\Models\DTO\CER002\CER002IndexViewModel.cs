﻿using MvcPaging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class CER002IndexViewModel : SearchFormViewModelBase
    {
        
        /// <summary>
        /// 查詢結果
        /// </summary>
        public IPagedList<HRMT01> Hrmt01ListData;
        /// <summary>
        /// 查詢結果
        /// </summary>
        public IPagedList<CER002ListViewModel> ListData;
          public ZZZI19IndexViewModel ListDataHRMT01 { get; set; }
        public string WhereUser { get; set; }
      

        //public byte? WhereGRADE { get; set; }

        //public string WhereCLASS_NO { get; set; }
        //public string WhereUser { get; set; }
        //public string whereKeyword { get; set; }
        /////// <summary>
        /////// 排序欄位
        /////// </summary>
        ////public string OrdercColumn { get; set; }



        ///// <summary>
        ///// 搜尋年級
        ///// </summary>
        //public string whereGrade { get; set; }

        ///// <summary>
        ///// 搜尋班級
        ///// </summary>
        //public string whereClass_No { get; set; }

        /// <summary>
        /// 護照細項名稱
        /// </summary>
        public string WhereACCREDITATION_NAME { get; set; }

        /// <summary>
        /// 護照ID
        /// </summary>
        public string WhereACCREDITATION_TYPE { get; set; }

        public byte WhereGRADE_SEMESTER_TYPE { get; set; }
    }
}