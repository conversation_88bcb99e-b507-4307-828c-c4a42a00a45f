/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX-Web/DoubleStruck/Italic/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXMathJax_DoubleStruck-italic"]={directory:"DoubleStruck/Italic",family:"STIXMathJax_DoubleStruck",style:"italic",testString:"\u00A0\u2102\u210D\u2115\u2119\u211A\u211D\u2124\u213C\u213F\u2145\u2146\u2147\u2148\u2149",32:[0,0,250,0,0],160:[0,0,250,0,0],8450:[666,18,702,35,702],8461:[653,0,732,17,767],8469:[653,0,727,25,755],8473:[653,0,687,17,686],8474:[666,71,723,35,713],8477:[653,0,687,17,686],8484:[653,0,754,7,750],8508:[428,12,635,40,630],8511:[653,0,750,30,780],8517:[653,0,713,17,703],8518:[683,11,581,40,634],8519:[441,11,515,40,485],8520:[653,0,293,27,346],8521:[653,217,341,-104,394]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"STIXMathJax_DoubleStruck-italic"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/DoubleStruck/Italic/Main.js"]);
