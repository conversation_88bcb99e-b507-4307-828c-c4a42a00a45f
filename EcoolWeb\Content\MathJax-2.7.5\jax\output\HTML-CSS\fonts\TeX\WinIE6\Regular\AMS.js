/*
 *  /MathJax/jax/output/HTML-CSS/fonts/TeX/WinIE6/Regular/AMS.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.MathJax_WinIE6,{58048:[437,-64,500,58,417],58049:[437,-64,500,64,422],58050:[430,23,222,91,131],58051:[431,23,389,55,331],58052:[365,-132,778,55,719],58053:[753,175,778,83,694],58054:[753,175,778,83,694],58055:[708,209,778,82,693],58056:[708,209,778,82,693],58058:[694,-306,500,55,444],58059:[694,-306,500,55,444],58060:[366,22,500,55,444],58061:[366,22,500,55,444],58062:[694,195,889,0,860],58063:[694,195,889,0,860],58064:[689,0,778,55,722],58065:[689,0,778,55,722],58066:[575,20,722,84,637],58067:[575,20,722,84,637],58068:[539,41,778,83,694],58069:[576,19,722,84,637],58070:[576,19,722,84,637],58071:[539,41,778,83,694],58072:[716,132,667,56,611],58073:[471,82,667,24,643],58074:[471,82,667,23,643],58075:[601,101,778,15,762],58076:[694,111,944,49,895],58077:[367,-133,778,56,722]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/WinIE6/Regular/AMS.js");
