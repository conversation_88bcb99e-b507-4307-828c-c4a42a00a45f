/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Latin-Modern/Symbols/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.LatinModernMathJax_Symbols={directory:"Symbols/Regular",family:"LatinModernMathJax_Symbols",testString:"\u00A0\u2300\u2305\u2306\u2310\u2319\u231C\u231D\u231E\u231F\u2320\u2321\u2329\u232A\u239B",32:[0,0,332,0,0],160:[0,0,332,0,0],8960:[596,96,778,43,735],8965:[270,155,778,56,722],8966:[367,252,778,56,722],8976:[367,-133,667,47,620],8985:[367,-133,667,47,620],8988:[770,-490,392,56,336],8989:[770,-490,392,56,336],8990:[10,270,392,56,336],8991:[10,270,392,56,336],8992:[1344,0,1185,551,1129],8993:[1322,22,1185,56,685],9001:[750,250,389,110,336],9002:[750,250,389,53,279],9115:[1495,0,875,277,823],9116:[498,0,875,277,379],9117:[1495,0,875,277,823],9118:[1495,0,875,52,598],9119:[498,0,875,496,598],9120:[1495,0,875,52,598],9121:[1500,0,667,321,647],9122:[1000,0,667,321,381],9123:[1500,0,667,321,647],9124:[1500,0,667,20,346],9125:[1000,0,667,286,346],9126:[1500,0,667,20,346],9127:[750,0,902,400,755],9128:[1500,0,902,147,502],9129:[750,0,902,400,755],9130:[748,0,902,400,502],9131:[750,0,902,147,502],9132:[1500,0,902,400,755],9133:[750,0,902,147,502],9138:[846,0,1576,56,1520],9139:[847,0,1576,57,1520],9143:[1820,0,1056,111,742],10178:[684,0,778,56,722],10200:[684,184,946,56,890],10201:[684,184,986,56,930],10202:[684,0,1026,56,970],10203:[684,0,1026,56,970],10204:[400,-100,948,56,892],10205:[684,184,946,56,890],10206:[684,184,946,56,890],10208:[610,110,572,56,516],10209:[501,1,614,56,558],10210:[501,1,730,56,674],10211:[501,1,730,56,674],10214:[750,250,410,118,388],10215:[750,250,410,22,292],10218:[750,250,570,110,517],10219:[750,250,570,53,460]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"LatinModernMathJax_Symbols"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Symbols/Regular/Main.js"]);
