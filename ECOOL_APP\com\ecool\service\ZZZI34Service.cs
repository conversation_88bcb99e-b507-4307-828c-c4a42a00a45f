﻿using com.ecool.service;
using Dapper;
using ECOOL_APP.EF;
using EntityFramework.Extensions;
using MvcPaging;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Entity;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Imaging;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Text.RegularExpressions;
using System.Transactions;
using System.Web;

namespace ECOOL_APP.com.ecool.service
{
    public class ZZZI34Service
    {
        /// <summary>
        /// 藝廊
        /// </summary>
        /// <param name="model"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        public ZZZI34IndexViewModel GetArtGalleryListData(ZZZI34IndexViewModel model, UserProfile User, ref ECOOL_DEVEntities db, bool VisibleVerify)
        {
            string sSQL = @"Select a.*, '" + UrlCustomHelper.GetOwnWebUri() + $@"/EcoolWeb/ZZZI34/QRCODEUrl?WhereART_GALLERY_NO=" + "'+ a.ART_GALLERY_NO  as QRCODEGARY , '" + UrlCustomHelper.GetOwnWebUri() + $@"/EcoolWeb/ZZZI34/QRCODEUrl?PHOTO_USER_NO=" + "' + a.USER_NO +"+$@"'&PHOTO_SCHOOL_NO='+ a.SCHOOL_NO as QRCODEGARYPHOTO , a.ART_DESC";
                          sSQL =sSQL+ @", WorkCount=(SELECT COUNT(*) FROM ADDT22 b (NOLOCK) Where B.ART_GALLERY_NO =a.ART_GALLERY_NO and b.PHOTO_STATUS=@PHOTO_STATUS)
                            from ADDT21 a (nolock)
                            where a.SCHOOL_NO =@SCHOOL_NO and (SELECT COUNT(*) FROM ADDT22 b (NOLOCK) Where B.ART_GALLERY_NO =a.ART_GALLERY_NO and b.PHOTO_STATUS=@PHOTO_STATUS)>0";

            if ((model.Search.WhereMyWork ?? false))
            {
                sSQL = sSQL + @" and ((a.USER_NO = @USER_NO) or a.ART_GALLERY_NO in (SELECT ART_GALLERY_NO FROM ADDT22 k (NOLOCK) Where k.PHOTO_SCHOOL_NO=@SCHOOL_NO and k.PHOTO_USER_NO=@USER_NO  and k.PHOTO_STATUS=0))  ";

            }
            else
            {
                if (!string.IsNullOrWhiteSpace(model.Search.WhereART_GALLERY_NO))
                {
                    sSQL = sSQL + "  and  (a.ART_GALLERY_NO=@ART_GALLERY_NO)   ";

                }
                if (model.Search.WhereSYEAR != null && model.Search.WhereSYEAR != 0)
                {
                    sSQL = sSQL + " and  (a.SYEAR=@SYEAR)  ";
                }
                
                if (!string.IsNullOrWhiteSpace(model.Search.WhereKeyword))
                {
                    sSQL = sSQL + @" and a.ART_GALLERY_NO in (select b.ART_GALLERY_NO from ADDT22 b (nolock) where b.PHOTO_SCHOOL_NO =@SCHOOL_NO
                                     and ((b.PHOTO_USER_NO LIKE @WhereKeyword)  or (b.PHOTO_NAME LIKE @WhereKeyword) )
                                     )";
                }

                if (!string.IsNullOrWhiteSpace(model.Search.WhereGrade))
                {
                    sSQL = sSQL + @" and a.ART_GALLERY_NO in (select b.ART_GALLERY_NO from ADDT22 b (nolock) where b.PHOTO_SCHOOL_NO =@SCHOOL_NO
                                     and left(b.PHOTO_CLASS_NO,1)=@WhereGrade)";
                }

                if (!string.IsNullOrWhiteSpace(model.Search.WhereCLASS_NO))
                {
                    sSQL = sSQL + @" and a.ART_GALLERY_NO in (select b.ART_GALLERY_NO from ADDT22 b (nolock) where b.PHOTO_SCHOOL_NO =@SCHOOL_NO
                                     and b.PHOTO_CLASS_NO=@WhereCLASS_NO)";
                }

                if (string.IsNullOrEmpty(model.Search.WhereSTATUS))
                {
                    if (!string.IsNullOrWhiteSpace(model.Search.WhereUSER_NO))
                    {
                        sSQL = sSQL + " and (a.USER_NO = @USER_NO  or (isnull(a.VERIFIER,'')=@USER_NO and a.STATUS=@Verification ) or  (a.STATUS=@Pass)  ) and a.ART_GALLERY_NO in (SELECT ART_GALLERY_NO FROM ADDT22 k (NOLOCK) Where k.PHOTO_SCHOOL_NO=@SCHOOL_NO and k.PHOTO_USER_NO=@USER_NO  and k.PHOTO_STATUS=0) ";
                    }
                    else
                    {
                        if (User?.USER_TYPE != UserType.Admin && VisibleVerify == false)
                        {
                            sSQL = sSQL + " and  (a.STATUS=@Pass)   ";
                        }
                    }
                }
                else
                {
                    sSQL = sSQL + " and  (a.STATUS=@STATUS)   ";
                    decimal? Level = PermissionService.GetROLE_LEVEL(User?.USER_KEY, User?.SCHOOL_NO, User?.USER_NO);

                    string ISLEVEL = Level != null ? ((int)Level).ToString() : "";
                    if (ISLEVEL == HRMT24_ENUM.QAdminLevel.ToString() || ISLEVEL == HRMT24_ENUM.SuperAdminROLE )
                    {
                        ISLEVEL = "Y";
                    }
                    if ((!string.IsNullOrWhiteSpace(model.Search.WhereUSER_NO) && User.USER_TYPE != "A") && ISLEVEL != "Y")
                    {
                        if (!string.IsNullOrEmpty(User?.TEACH_CLASS_NO))
                        {
                            sSQL = sSQL + " and (a.USER_NO = @USER_NO  or isnull(a.VERIFIER,'')=@USER_NO) and ((a.STATUS=@Verification ) or  (a.STATUS=@Pass) ) ";
                        }
                        else
                        {
                            sSQL = sSQL + " and a.USER_NO = @USER_NO  ";
                        }
                    }
                }
            }

            var temp = db.Database.Connection.Query<ZZZI34IndexListDataViewModel>(sSQL
                , new
                {
                    ART_GALLERY_NO= model.Search.WhereART_GALLERY_NO,
                    SCHOOL_NO = model.Search.WhereSCHOOL_NO,
                    USER_NO = model.Search.WhereUSER_NO,
                    Verification = ADDT21.STATUSVal.Verification,
                    PHOTO_STATUS = ADDT22.STATUSVal.OK,
                    Pass = ADDT21.STATUSVal.Pass,
                    STATUS = model.Search.WhereSTATUS,
                    SYEAR = model.Search.WhereSYEAR,
                    WhereKeyword = "%" + model.Search.WhereKeyword + "%",
                    WhereGrade = model.Search.WhereGrade,
                    WhereCLASS_NO = model.Search.WhereCLASS_NO,
                });

            if (!string.IsNullOrWhiteSpace(model.Search.WhereSTATUS))
            {
                temp = temp.Where(a => a.STATUS == model.Search.WhereSTATUS);
            }
            else
            {
                temp = temp.Where(a => a.STATUS != ADDT21.STATUSVal.Disabled);
            }

            if (!string.IsNullOrWhiteSpace(model.Search.WhereSearch))
            {
                temp = temp.Where(a => (a.ART_SUBJECT ?? "").Contains(model.Search.WhereSearch.Trim()));
            }

            if (!string.IsNullOrWhiteSpace(model.Search.WhereART_GALLERY_TYPE))
            {
                temp = temp.Where(a => a.ART_GALLERY_TYPE == model.Search.WhereART_GALLERY_TYPE);
            }

            temp = temp.OrderByDescending(a => a.CRE_DATE).ThenBy(a => a.ART_SUBJECT);

            model.ListData = temp.ToPagedList(model.Search.Page > 0 ? model.Search.Page - 1 : 0, model.PageSize);

            return model;
        }

        /// <summary>
        /// 作品
        /// </summary>
        /// <param name="model"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        public ZZZI34WorkIndexViewModel GetArtGalleryWorkListData(ZZZI34WorkIndexViewModel model, UserProfile User, ref ECOOL_DEVEntities db)
        {
            string sSQL = $@"SELECT
                            RANK() OVER(ORDER BY a.CRE_DATE desc,b.PHOTO_ORDER_BY) AS ROWID
                            ,a.ART_SUBJECT,a.WORK_TYPE,a.STATUS
                            ,(select Count(*) from ADDT23 c  (NOLOCK) where b.PHOTO_NO =c.PHOTO_NO) as SHARE_COUNT
                            ,(select Count(*) from ADDT23 d  (NOLOCK) where b.PHOTO_NO =d.PHOTO_NO and ((d.SCHOOL_NO='{(User?.SCHOOL_NO ?? "")}' and d.USER_NO='{(User?.USER_NO ?? "")}') or ('{(User?.USER_NO ?? "")}'='' and IP_ADDRESS='{(model.IP_ADDRESS ?? "")}')) ) as IsLikeCount
                            ,b.*
                            FROM ADDT21 a (NOLOCK)
                            INNER JOIN ADDT22 b (NOLOCK) ON a.ART_GALLERY_NO =b.ART_GALLERY_NO
                            WHERE 1=1
                            and b.PHOTO_STATUS='{ADDT22.STATUSVal.OK}' ";

            if (!string.IsNullOrEmpty(model.Search.WhereSCHOOL_NO))
            {
                sSQL = sSQL + $@" and a.SCHOOL_NO = @SCHOOL_NO ";

                if (!string.IsNullOrWhiteSpace(model.Search.WherePHOTO_NO))
                {
                    sSQL = sSQL + $@" and b.PHOTO_NO=@PHOTO_NO   ";
                }

                if (!string.IsNullOrWhiteSpace(model.Search.WhereART_GALLERY_NO))
                {
                    sSQL = sSQL + $@" and a.ART_GALLERY_NO=@ART_GALLERY_NO   ";

                    if (!string.IsNullOrWhiteSpace(model.Search.WhereUSER_NO))
                    {
                        sSQL = sSQL + $@" and ((a.USER_NO = @USER_NO  and a.STATUS!='{ADDT21.STATUSVal.Disabled}')  or (a.STATUS='{ADDT21.STATUSVal.Pass}') )   ";
                    }
                    else
                    {
                        if (!HRMT24_ENUM.CheckQAdmin(User))
                        {
                            sSQL = sSQL + $" and  a.STATUS='{ADDT21.STATUSVal.Pass}' ";
                        }
                    }
                }
                else
                {
                    if ((model.Search.WhereMyWork ?? false))
                    {
                        sSQL = sSQL + $@" and ( b.PHOTO_USER_NO = @USER_NO  and  a.STATUS!='{ADDT21.STATUSVal.Disabled}')   ";
                    }
                    else
                    {
                        sSQL = sSQL + $" and  a.STATUS='{ADDT21.STATUSVal.Pass}' ";
                    }

                    if (model.Search.WhereSYEAR != null && model.Search.WhereSYEAR != 0)
                    {
                        sSQL = sSQL + " and  (a.SYEAR=@SYEAR)   ";
                    }

                    if (!string.IsNullOrWhiteSpace(model.Search.WhereKeyword))
                    {
                        sSQL = sSQL + @" and ((b.PHOTO_USER_NO LIKE @WhereKeyword) or (b.PHOTO_NAME LIKE @WhereKeyword)) ";
                    }

                    if (!string.IsNullOrWhiteSpace(model.Search.WhereGrade))
                    {
                        sSQL = sSQL + @" and left(b.PHOTO_CLASS_NO,1)=@WhereGrade ";
                    }

                    if (!string.IsNullOrWhiteSpace(model.Search.WhereCLASS_NO))
                    {
                        sSQL = sSQL + @" and b.PHOTO_CLASS_NO=@WhereCLASS_NO ";
                    }
                }
            }
            else
            {
                sSQL = sSQL + $@" and b.PHOTO_NO=@PHOTO_NO   ";
            }

            sSQL = sSQL + $@" order by  a.CRE_DATE desc,b.PHOTO_ORDER_BY ";

            var temp = db.Database.Connection.Query<ZZZI34IndexWorkListDataViewModel>(sSQL
           , new
           {
               SCHOOL_NO = model.Search.WhereSCHOOL_NO,
               USER_NO = model.Search.WhereUSER_NO,
               ART_GALLERY_NO = model.Search.WhereART_GALLERY_NO,
               SYEAR = model.Search.WhereSYEAR,
               WhereKeyword = "%" + model.Search.WhereKeyword + "%",
               WhereGrade = model.Search.WhereGrade,
               WhereCLASS_NO = model.Search.WhereCLASS_NO,
               PHOTO_NO = model.Search.WherePHOTO_NO,
           });
            if (model.WorkSearch!= null)
            {
                model.WorkListData = temp.ToPagedList(model.WorkSearch.Page > 0 ? model.WorkSearch.Page - 1 : 0, model.PageSize);
            }
            else {

                model.WorkListData = temp.ToPagedList(0, model.PageSize);
            }
            return model;
        }

        public ZZZI34EditViewModel GetEditArtGallery(ZZZI34EditViewModel model, UserProfile User, ref ECOOL_DEVEntities db)
        {
            model.Main = (from a in db.ADDT21
                          where a.ART_GALLERY_NO == model.Search.WhereART_GALLERY_NO
                          select new ZZZI34EditMainViewModel()
                          {
                              ART_GALLERY_NO = a.ART_GALLERY_NO,
              
                              ART_GALLERY_TYPE = a.ART_GALLERY_TYPE,
                              ART_SUBJECT = a.ART_SUBJECT,
                              ART_DESC = a.ART_DESC,
                              WORK_TYPE = a.WORK_TYPE,
                              COVER_FILE = a.COVER_FILE,
                              STATUS = a.STATUS,
                              VERIFIER = a.VERIFIER,
                              SCHOOL_NO = a.SCHOOL_NO,
                              CRE_PERSON = a.CRE_PERSON
                          }).FirstOrDefault();

            if (model.Main != null)
            {
                var Photo = (from p in db.ADDT22
                             where p.ART_GALLERY_NO == model.Main.ART_GALLERY_NO
                             select new ZZZI34EditPhotoViewModel()
                             {
                                 ART_GALLERY_NO = p.ART_GALLERY_NO,
                                 ART_GALLERY_TYPE = model.Main.ART_GALLERY_TYPE,
                                 WORK_TYPE = model.Main.WORK_TYPE,
                                 PHOTO_NO = p.PHOTO_NO,
                                 PHOTO_FILE = p.PHOTO_FILE,
                                 PHOTO_SUBJECT = p.PHOTO_SUBJECT,
                                 PHOTO_DESC = p.PHOTO_DESC,
                                 PHOTO_SNAME=p.PHOTO_SNAME,
                                 PHOTO_CASH = p.PHOTO_CASH,
                                 PHOTO_CHG_DATE =p.CHG_DATE,
                                 PHOTO_STATUS = p.PHOTO_STATUS,
                                 PHOTO_ORDER_BY = p.PHOTO_ORDER_BY,
                                 PHOTO_SCHOOL_NO = p.PHOTO_SCHOOL_NO,
                                 PHOTO_USER_NO = p.PHOTO_USER_NO,
                                 PHOTO_CLASS_NO = p.PHOTO_CLASS_NO,
                                 AutherYN=p.AutherYN
                             }).ToList();

                var People = (from p in Photo
                              where p.ART_GALLERY_NO == model.Main.ART_GALLERY_NO
                              group p by new
                              {
                                  p.PHOTO_SCHOOL_NO,
                                  p.PHOTO_CLASS_NO,
                                  p.PHOTO_USER_NO,
                                   p.PHOTO_SNAME,
                                   p.PHOTO_CHG_DATE,
                                  p.AutherYN
                              } into Peo
                              select new ZZZI34EditPeopleViewModel()
                              {
                                  PHOTO_SCHOOL_NO = Peo.Key.PHOTO_SCHOOL_NO,
                                  PHOTO_CHG_DATE=Peo.Key.PHOTO_CHG_DATE,
                              PHOTO_SNAME = Peo.Key.PHOTO_SNAME,
                                  PHOTO_USER_NO = Peo.Key.PHOTO_USER_NO,
                                  PHOTO_CLASS_NO = Peo.Key.PHOTO_CLASS_NO,
                                  ART_GALLERY_TYPE = model.Main.ART_GALLERY_TYPE,
                                  AutherYN=Peo.Key.AutherYN,
                                  Photo = Photo.Where(b => b.ART_GALLERY_NO == model.Main.ART_GALLERY_NO && b.PHOTO_SCHOOL_NO == Peo.Key.PHOTO_SCHOOL_NO && b.PHOTO_USER_NO == Peo.Key.PHOTO_USER_NO).ToList()
                              }).ToList();

                model.People = People;
            }

            return model;
        }
        public  Bitmap KiRotate(Bitmap bmp, float angle)
        {
            int w = bmp.Width + 1;
            int h = bmp.Height + 1;

            PixelFormat pf;

            pf = bmp.PixelFormat;

            Bitmap tmp = new Bitmap(w, h, pf);
            Graphics g = Graphics.FromImage(tmp);
            g.DrawImageUnscaled(bmp, 1, 1);
            g.Dispose();

            GraphicsPath path = new GraphicsPath();
            path.AddRectangle(new RectangleF(0f, 0f, w, h));
            Matrix mtrx = new Matrix();
            mtrx.Rotate(angle);
            RectangleF rct = path.GetBounds(mtrx);

            Bitmap dst = new Bitmap((int)rct.Width, (int)rct.Height, pf);
            g = Graphics.FromImage(dst);
            g.TranslateTransform(-rct.X, -rct.Y);
            g.RotateTransform(angle);
            g.InterpolationMode = InterpolationMode.HighQualityBilinear;
            g.DrawImageUnscaled(tmp, 0, 0);
            g.Dispose();

            tmp.Dispose();

            return dst;
        }
        public ZZZI34LikeListViewModel GetLikeListData(ZZZI34LikeListViewModel model, ref ECOOL_DEVEntities db)
        {
            string sSQL = @"select a.* ,b.SHORT_NAME
            from ADDT23 a (NOLOCK)
            left outer join  BDMT01 b (NOLOCK) on a.SCHOOL_NO=b.SCHOOL_NO
            where a.PHOTO_NO=@PHOTO_NO and a.COMMENT_STATUS=@COMMENT_STATUS";

            model.LikeList = db.Database.Connection.Query<ZZZI34ShareViewModel>(sSQL
           , new
           {
               PHOTO_NO = model.PHOTO_NO,
               COMMENT_STATUS = ADDT23.COMMENT_STATUS_VAL.OK,
           }).ToList();

            return model;
        }

        public ZZZI34OrderListViewModel GetOrderListData(ZZZI34OrderListViewModel model, ref ECOOL_DEVEntities db, ref string Message)
        {
            string sSQLD = $@"  SELECT b.PHOTO_SCHOOL_NO,b.PHOTO_USER_NO,count(DISTINCT b.PHOTO_NO) as WorkCount,sum(b.PHOTO_CASH) as SumCash
	                            FROM ADDT22 b (nolock)
	                            where b.PHOTO_SCHOOL_NO=@SCHOOL_NO
	                            and b.PHOTO_STATUS='{ADDT22.STATUSVal.OK}' ";
            //月排行榜
            if (model.WhereIsMonthTop == true)
            {
                sSQLD = sSQLD + " and CONVERT(nvarchar(6),b.CRE_DATE,112) = CONVERT(nvarchar(6), GETDATE(), 112) ";
            }
            else
            {
                string extensionSQL = (model.whereSTART_CRE_DATE.HasValue ? " AND b.CRE_DATE >= @START_CRE_DATE" : "") +
               (model.whereEND_CRE_DATE.HasValue ? " AND b.CRE_DATE <= @END_CRE_DATE" : "");
                sSQLD = sSQLD + extensionSQL;
            }

            sSQLD = sSQLD + " group by b.PHOTO_SCHOOL_NO,b.PHOTO_USER_NO ";

            string sSQLE = $@"  SELECT b.PHOTO_SCHOOL_NO,b.PHOTO_USER_NO,count(DISTINCT c.COMMENT_NO) as LikeCount
	                              FROM ADDT22 b (nolock)
	                              left outer join ADDT23 c (nolock) on b.PHOTO_NO=c.PHOTO_NO and c.COMMENT_STATUS='{ADDT23.COMMENT_STATUS_VAL.OK}' and c.COMMENT_TYPE='{ADDT23.COMMENT_TYPEVal.like}'
	                              where b.PHOTO_SCHOOL_NO=@SCHOOL_NO
	                              and b.PHOTO_STATUS='{ADDT22.STATUSVal.OK}' ";
            //月排行榜
            if (model.WhereIsMonthTop == true)
            {
                sSQLE = sSQLE + " and CONVERT(nvarchar(6),b.CRE_DATE,112) = CONVERT(nvarchar(6), GETDATE(), 112) ";
            }
            else
            {
                string extensionSQL = (model.whereSTART_CRE_DATE.HasValue ? " AND b.CRE_DATE >= @START_CRE_DATE" : "") +
               (model.whereEND_CRE_DATE.HasValue ? " AND b.CRE_DATE <= @END_CRE_DATE" : "");
                sSQLE = sSQLE + extensionSQL;
            }
            sSQLE = sSQLE + " group by b.PHOTO_SCHOOL_NO,b.PHOTO_USER_NO ";

            string sSQL = $@"select a.SCHOOL_NO, a.USER_NO, a.NAME, a.SNAME, a.SEX,
                            a.GRADE, a.CLASS_NO, a.SEAT_NO, a.USER_STATUS,
                            a.USER_TYPE, AG.WorkCount,AG.SumCash, LI.LikeCount
                            from HRMT01 a (nolock)
                            JOIN ({sSQLD}) AS AG ON AG.PHOTO_USER_NO = a.USER_NO AND  AG.PHOTO_SCHOOL_NO = a.SCHOOL_NO
                            JOIN ({sSQLE}) AS LI ON LI.PHOTO_USER_NO = a.USER_NO AND  LI.PHOTO_SCHOOL_NO = a.SCHOOL_NO
                            where a.USER_TYPE = '{UserType.Student}' AND a.USER_STATUS <> {UserStaus.Disable} AND a.USER_STATUS <> {UserStaus.Invalid}
                            and a.SCHOOL_NO=@SCHOOL_NO ";

            if (string.IsNullOrWhiteSpace(model.WhereUserNo) == false)
            {
                sSQL = sSQL + " and a.USER_NO =@USER_NO ";
            }

            if (string.IsNullOrWhiteSpace(model.WhereCLASS_NO) == false)
            {
                model.PageSize = int.MaxValue;
                sSQL = sSQL + " and a.CLASS_NO =@CLASS_NO ";
            }

            if (string.IsNullOrWhiteSpace(model.WhereGrade) == false)
            {
                sSQL = sSQL + " and left(a.CLASS_NO,1) =@Grade ";
            }

            if (string.IsNullOrWhiteSpace(model.WhereKeyword) == false)
            {
                sSQL = sSQL + " and (a.USER_NO like @WhereKeyword or a.NAME like @WhereKeyword or a.SNAME like @WhereKeyword) ";
            }

            switch (model.OrdercColumn)
            {
                case "WorkCount":
                    sSQL = sSQL + " Order by AG.WorkCount Desc,a.SNAME ";
                    break;

                case "LikeCount":
                    sSQL = sSQL + " Order by LI.LikeCount Desc,a.SNAME ";
                    break;

                case "SumCash":
                    sSQL = sSQL + " Order by AG.SumCash Desc,a.SNAME ";
                    break;

                default:
                    sSQL = sSQL + " Order by AG.WorkCount Desc,a.SNAME ";
                    break;
            }

            var temp = db.Database.Connection.Query<ZZZI34OrderListDataViewModel>(sSQL
           , new
           {
               SCHOOL_NO = model.WhereSCHOOL_NO,
               USER_NO = model.WhereUserNo,
               CLASS_NO = model.WhereCLASS_NO,
               Grade = model.WhereGrade,
               START_CRE_DATE = model.whereSTART_CRE_DATE,
               END_CRE_DATE = model.whereEND_CRE_DATE,
               WhereKeyword = '%' + model.WhereKeyword?.Trim() + '%',
           });

            if (model.IsPrint)
            {
                model.PageSize = int.MaxValue;
            }

            model.ListData = temp.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, model.PageSize);
            if (model.IsToExcel)
            {
                model.PageSize = int.MaxValue;
                model.ListData = temp.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, model.PageSize); ;
            }
            if (model.IsPrint)
            {
                if (model.ListData.Count() > 10000)
                {
                    Message = "請縮小條件範圍，目前只顯示前10000筆";
                    model.ListData = temp.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, 10000);
                }
            }

            return model;
        }
       
            public bool IsCheck(ZZZI34EditViewModel data, UserProfile User, ref ECOOL_DEVEntities db, ref string Message)
        {
            if (string.IsNullOrWhiteSpace(data.Main?.ART_SUBJECT))
            {
                Message = Message + "*藝廊名稱未輸入</br>";
            }
            else if (data.Main?.ART_SUBJECT.Length >= 20)
            {
                Message = Message + "*藝廊名稱僅接受20個字以內的文字</br>";
            }

            //if (!string.IsNullOrWhiteSpace(data.Main?.ART_DESC))
            //{
            //    if (data.Main?.ART_DESC.Length >= 255)
            //    {
            //        Message = Message + "*藝廊描述僅接受255個字以內的文字</br>";
            //    }
            //}

            if (string.IsNullOrWhiteSpace(data.Main?.WORK_TYPE))
            {
                Message = Message + "*作品類別未輸入</br>";
            }

            if (string.IsNullOrWhiteSpace(data.Main?.COVER_FILE) && (data.Main?.UploadCoverFile?.ContentLength ?? 0) == 0)
            {
                Message = Message + "*未選取封面</br>";
            }

            if ((data.People?.Count() ?? 0) == 0 && (data.Details_List?.Count() ?? 0) == 0)
            {
                Message = Message + "*無作者</br>";
            }
            else
            {
                if (data.Details_List != null)
                {
                    foreach (var item in data.Details_List)
                    {
                        if ((item.IMG_FILE?.Count() ?? 0) == 0)
                        {
                            var SNAME = db.HRMT01.Where(a => a.SCHOOL_NO == item.SCHOOL_NO && a.USER_NO == item.USER_NO).Select(a => a.SNAME).FirstOrDefault();
                            if (string.IsNullOrWhiteSpace(SNAME))
                            {
                                Message = Message + "*無輸入主題</br>";
                            }
                            else
                            {
                                Message = Message + $"*{SNAME}-無輸入主題</br>";
                            }
                        }
                    }
                }
                else
                {
                    foreach (var item in data.People)
                    {
                        if ((item.Photo?.Count() ?? 0) == 0)
                        {
                            var SNAME = db.HRMT01.Where(a => a.SCHOOL_NO == item.PHOTO_SCHOOL_NO && a.USER_NO == item.PHOTO_USER_NO).Select(a => a.SNAME).FirstOrDefault();
                            if (string.IsNullOrWhiteSpace(SNAME))
                            {
                                Message = Message + "*無輸入主題</br>";
                            }
                            else
                            {
                                Message = Message + $"*{SNAME}-無輸入主題</br>";
                            }
                        }
                    }
                }
            }

            ADDT21 SaveUp = null;
            if (data.Main != null) {

                SaveUp = db.ADDT21.Where(a => a.ART_GALLERY_NO == data.Main.ART_GALLERY_NO).FirstOrDefault();
            }
           
            if (SaveUp != null)
            {
                string str = PermissionService.GetPermission_Use_YN("ZZZI34", "Edit", User.SCHOOL_NO, User.USER_NO);


                if (SaveUp.STATUS == ADDT21.STATUSVal.Disabled)
                {
                    Message = "此狀態不能異動資料</br>";
                    return false;
                }

                if (SaveUp.CRE_PERSON != User.USER_KEY && !HRMT24_ENUM.CheckQAdmin(User)&& str != "Y")
                {
                  
                    Message = "系統發生錯誤;原因:你無異動資料權限</br>";
                    return false;
                }
            }

            List<ADDT22> SaveADDT22_List = new List<ADDT22>();

            int ORDER_BY_NUM = 0;
            int GroupNum = 0;
            int Item_No = 0;

            string CheckNo = string.Empty;
            if (data.Details_List != null)
            {
                if (data.Details_List.Count() > 0)
                {
                    //移除DEL有勾選的資料
                    data.Details_List.RemoveAll(a => a.Del == true);
                    foreach (var Ditem in data.Details_List)
                    {
                        GroupNum = GroupNum + 1;
                        if (Ditem.IMG_FILE?.Count() > 0)
                        {
                            //foreach (var PhotoItem in Ditem.IMG_FILE)
                            //{
                            if (!string.IsNullOrWhiteSpace(CheckNo))
                            {
                                if (CheckNo != Ditem.PHOTO_USER_NO)
                                {
                                    Item_No = 1;
                                }
                                else
                                {
                                    Item_No = Item_No + 1;
                                }
                            }
                            else
                            {
                                Item_No = Item_No + 1;
                            }

                            ORDER_BY_NUM = ORDER_BY_NUM + 1;
                            var Hr = db.HRMT01.Where(a => a.SCHOOL_NO == Ditem.SCHOOL_NO && a.USER_NO == Ditem.USER_NO).FirstOrDefault();

                            if (Hr == null)
                            {
                                Message = Message + $"找不到此帳號{Ditem.USER_NO}</br>";
                                return false;
                            }
                            if (string.IsNullOrWhiteSpace(Ditem.PHOTO_SUBJECT))
                            {
                                if (data.Main.ART_GALLERY_TYPE == ADDT21.ART_GALLERY_TYPE_VAL.Campus)
                                {
                                    Message = Message + $"{Hr.SNAME}-主題{Item_No}未輸入</br>";
                                }
                                else
                                {
                                    Message = Message + $"主題{Item_No}-未輸入</br>";
                                }
                            }
                            else
                            {
                                if (Ditem.PHOTO_SUBJECT.Length > 10)
                                {
                                    if (data.Main.ART_GALLERY_TYPE == ADDT21.ART_GALLERY_TYPE_VAL.Campus)
                                    {
                                        Message = Message + $"{Hr.SNAME}-主題內容:{Ditem.PHOTO_SUBJECT}-輸入長度超過10碼</br>";
                                    }
                                    else
                                    {
                                        Message = Message + $"主題內容:{Ditem.PHOTO_SUBJECT}-輸入長度超過10碼</br>";
                                    }
                                }

                                if (!string.IsNullOrWhiteSpace(Ditem.PHOTO_DESC))
                                {
                                    if (Ditem.PHOTO_DESC?.Length > 255)
                                    {
                                        if (data.Main.ART_GALLERY_TYPE == ADDT21.ART_GALLERY_TYPE_VAL.Campus)
                                        {
                                            Message = Message + $"{Hr.SNAME}-內容:{Ditem.PHOTO_DESC}-輸入長度超過255碼</br>";
                                        }
                                        else
                                        {
                                            Message = Message + $"內容:{Ditem?.PHOTO_DESC}-輸入長度超過255碼</br>";
                                        }
                                    }
                                }
                                if (string.IsNullOrWhiteSpace(Ditem.IMG_FILE) &&
                                            (Ditem.IMG_FILE == null)
                                         )
                                {
                                    if (data.Main.ART_GALLERY_TYPE == ADDT21.ART_GALLERY_TYPE_VAL.Campus)
                                    {
                                        Message = Message + $"{Hr.SNAME}-主題「{Ditem.PHOTO_SUBJECT}」，未上傳檔案</br>";
                                    }
                                    else
                                    {
                                        Message = Message + $"主題「{Ditem.PHOTO_SUBJECT}」，未上傳檔案</br>";
                                    }
                                }

                                if (data.Main.ART_GALLERY_TYPE == ADDT21.ART_GALLERY_TYPE_VAL.Campus
                                    && (SaveUp != null ? (SaveUp.STATUS ?? ADDT21.STATUSVal.NotStarted) : ADDT21.STATUSVal.NotStarted) == ADDT21.STATUSVal.NotStarted)
                                {
                                    if (Ditem.PHOTO_CASH == null)
                                    {
                                        Message = Message + $"{Hr.SNAME}-主題「{Ditem.PHOTO_SUBJECT}」，未輸入酷幣點數</br>";
                                    }
                                    else if ((Ditem.PHOTO_CASH ?? 0) < 0)
                                    {
                                        Message = Message + $"{Hr.SNAME}-主題「{Ditem.PHOTO_SUBJECT}」，酷幣點數小於0</br>";
                                    }
                                    else if ((Ditem.PHOTO_CASH ?? 0) > 10)
                                    {
                                        Message = Message + $"{Hr.SNAME}-主題「{Ditem.PHOTO_SUBJECT}」，酷幣點數大於10</br>";
                                    }
                                }
                            }
                            //}
                        }
                    }
                }
            }
            if (data.People != null)
            {
                if (data.People.Count() > 0)
                {
                    foreach (var Peitem in data.People)
                    {
                        GroupNum = GroupNum + 1;

                        if (Peitem.Photo?.Count() > 0)
                        {
                            foreach (var PhotoItem in Peitem.Photo)
                            {
                                if (!string.IsNullOrWhiteSpace(CheckNo))
                                {
                                    if (CheckNo != Peitem.PHOTO_USER_NO)
                                    {
                                        Item_No = 1;
                                    }
                                    else
                                    {
                                        Item_No = Item_No + 1;
                                    }
                                }
                                else
                                {
                                    Item_No = Item_No + 1;
                                }

                                ORDER_BY_NUM = ORDER_BY_NUM + 1;

                                if (string.IsNullOrWhiteSpace(Peitem.PHOTO_SCHOOL_NO) || string.IsNullOrWhiteSpace(Peitem.PHOTO_USER_NO))
                                {
                                    Message = Message + $"第{GroupNum}筆作者，姓名/座號-未輸入</br>";
                                }
                                else
                                {
                                    var Hr = db.HRMT01.Where(a => a.SCHOOL_NO == Peitem.PHOTO_SCHOOL_NO && a.USER_NO == Peitem.PHOTO_USER_NO).FirstOrDefault();

                                    if (Hr == null)
                                    {
                                        Message = Message + $"找不到此帳號{Peitem.PHOTO_USER_NO}</br>";
                                        return false;
                                    }

                                    CheckNo = Peitem.PHOTO_USER_NO;

                                    if (string.IsNullOrWhiteSpace(PhotoItem.PHOTO_SUBJECT))
                                    {
                                        if (data.Main.ART_GALLERY_TYPE == ADDT21.ART_GALLERY_TYPE_VAL.Campus)
                                        {
                                            Message = Message + $"{Hr.SNAME}-主題{Item_No}未輸入</br>";
                                        }
                                        else
                                        {
                                            Message = Message + $"主題{Item_No}-未輸入</br>";
                                        }
                                    }
                                    else
                                    {
                                        if (PhotoItem.PHOTO_SUBJECT.Length > 10)
                                        {
                                            if (data.Main.ART_GALLERY_TYPE == ADDT21.ART_GALLERY_TYPE_VAL.Campus)
                                            {
                                                Message = Message + $"{Hr.SNAME}-主題內容:{PhotoItem.PHOTO_SUBJECT}-輸入長度超過10碼</br>";
                                            }
                                            else
                                            {
                                                Message = Message + $"主題內容:{PhotoItem.PHOTO_SUBJECT}-輸入長度超過10碼</br>";
                                            }
                                        }

                                        if (!string.IsNullOrWhiteSpace(PhotoItem.PHOTO_DESC))
                                        {
                                            if (PhotoItem.PHOTO_DESC?.Length > 255)
                                            {
                                                if (data.Main.ART_GALLERY_TYPE == ADDT21.ART_GALLERY_TYPE_VAL.Campus)
                                                {
                                                    Message = Message + $"{Hr.SNAME}-內容:{PhotoItem.PHOTO_DESC}-輸入長度超過255碼</br>";
                                                }
                                                else
                                                {
                                                    Message = Message + $"內容:{PhotoItem?.PHOTO_DESC}-輸入長度超過255碼</br>";
                                                }
                                            }
                                        }

                                        if (data.Main.WORK_TYPE == ADDT21.WORK_TYPE_VAL.Youtube)
                                        {
                                            if (!string.IsNullOrWhiteSpace(PhotoItem.PHOTO_FILE))
                                            {
                                                if (PhotoItem.PHOTO_FILE.ToLower().IndexOf(@"https://www.youtube.com/embed/") == -1)
                                                {
                                                    var Index = PhotoItem.PHOTO_FILE.ToLower().IndexOf(@"https://www.youtube.com/watch?v=");

                                                    if (Index == -1)
                                                    {
                                                        if (data.Main.ART_GALLERY_TYPE == ADDT21.ART_GALLERY_TYPE_VAL.Campus)
                                                        {
                                                            Message = Message + $"{Hr.SNAME}-主題「{PhotoItem.PHOTO_SUBJECT}」，輸入 Youtube 網址 不是有效的，請輸入https://www.youtube.com/embed/影片id</br>";
                                                        }
                                                        else
                                                        {
                                                            Message = Message + $"主題「{PhotoItem.PHOTO_SUBJECT}」，輸入 Youtube 網址 不是有效的，請輸入https://www.youtube.com/embed/影片id</br>";
                                                        }
                                                    }
                                                    else
                                                    {
                                                        PhotoItem.PHOTO_FILE = PhotoItem.PHOTO_FILE.Replace(@"https://www.youtube.com/watch?v=", @"https://www.youtube.com/embed/");
                                                    }
                                                }

                                                Uri urlCheck = new Uri(PhotoItem.PHOTO_FILE);
                                                WebRequest request = WebRequest.Create(urlCheck);
                                                request.Timeout = 15000;
                                                WebResponse response;
                                                try
                                                {
                                                    response = request.GetResponse();
                                                }
                                                catch (Exception)
                                                {
                                                    if (data.Main.ART_GALLERY_TYPE == ADDT21.ART_GALLERY_TYPE_VAL.Campus)
                                                    {
                                                        Message = Message + $"{Hr.SNAME}-主題「{PhotoItem.PHOTO_SUBJECT}」，輸入 Youtube 網址{PhotoItem.PHOTO_FILE} 不是有效的</br>";
                                                    }
                                                    else
                                                    {
                                                        Message = Message + $"主題「{PhotoItem.PHOTO_SUBJECT}」，輸入 Youtube 網址{PhotoItem.PHOTO_FILE} 不是有效的</br>";
                                                    }
                                                }
                                            }
                                            else
                                            {
                                                if (data.Main.ART_GALLERY_TYPE == ADDT21.ART_GALLERY_TYPE_VAL.Campus)
                                                {
                                                    Message = Message + $"{Hr.SNAME}-主題「{PhotoItem.PHOTO_SUBJECT}」，未輸入 Youtube 網址</br>";
                                                }
                                                else
                                                {
                                                    Message = Message + $"主題「{PhotoItem.PHOTO_SUBJECT}」，未輸入 Youtube 網址</br>";
                                                }
                                            }
                                        }
                                        else
                                        {
                                            if (string.IsNullOrWhiteSpace(PhotoItem.PHOTO_FILE) &&
                                                (PhotoItem.PhotoFiles == null || ((PhotoItem.PhotoFiles != null ? PhotoItem.PhotoFiles.ContentLength : 0)) <= 0)
                                             )
                                            {
                                                if (data.Main.ART_GALLERY_TYPE == ADDT21.ART_GALLERY_TYPE_VAL.Campus)
                                                {
                                                    Message = Message + $"{Hr.SNAME}-主題「{PhotoItem.PHOTO_SUBJECT}」，未上傳檔案</br>";
                                                }
                                                else
                                                {
                                                    Message = Message + $"主題「{PhotoItem.PHOTO_SUBJECT}」，未上傳檔案</br>";
                                                }
                                            }
                                        }

                                        if (data.Main.ART_GALLERY_TYPE == ADDT21.ART_GALLERY_TYPE_VAL.Campus
                                            && (SaveUp != null ? (SaveUp.STATUS ?? ADDT21.STATUSVal.NotStarted) : ADDT21.STATUSVal.NotStarted) == ADDT21.STATUSVal.NotStarted)
                                        {
                                            if (PhotoItem.PHOTO_CASH == null)
                                            {
                                                Message = Message + $"{Hr.SNAME}-主題「{PhotoItem.PHOTO_SUBJECT}」，未輸入酷幣點數</br>";
                                            }
                                            else if ((PhotoItem.PHOTO_CASH ?? 0) < 0)
                                            {
                                                Message = Message + $"{Hr.SNAME}-主題「{PhotoItem.PHOTO_SUBJECT}」，酷幣點數小於0</br>";
                                            }
                                            else if ((PhotoItem.PHOTO_CASH ?? 0) > 10)
                                            {
                                                Message = Message + $"{Hr.SNAME}-主題「{PhotoItem.PHOTO_SUBJECT}」，酷幣點數大於10</br>";
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            if (data.Main?.WORK_TYPE == ADDT21.WORK_TYPE_VAL.photo && ORDER_BY_NUM < 3)
            {
                Message = Message + $"作品類別選擇影像時，必須開3幅畫作以上</br>";
            }

            if (string.IsNullOrWhiteSpace(Message))
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        public ZZZI34ViewBackModel  SaveArtGallery(ZZZI34EditViewModel data, UserProfile User, ref ECOOL_DEVEntities db, ref string Message, bool IsTempSave = false)
        {
            int SYear;
            int Semesters;
            SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);
            ZZZI34ViewBackModel zZZI34ViewBack = new ZZZI34ViewBackModel();
            ADDT21 SaveUp = null;

            SaveUp = db.ADDT21.Where(a => a.ART_GALLERY_NO == data.Main.ART_GALLERY_NO).FirstOrDefault();

            if (db.Database.Connection.State != ConnectionState.Open) db.Database.Connection.Open();

            using (TransactionScope tx = new TransactionScope())
            {
                if (SaveUp != null)
                {
                    SaveUp.ART_SUBJECT = data.Main.ART_SUBJECT;
                    SaveUp.ART_DESC = data.Main.ART_DESC;
                    SaveUp.WORK_TYPE = data.Main.WORK_TYPE;
                    SaveUp.CHG_PERSON = User.USER_KEY;
                    SaveUp.CHG_DATE = DateTime.Now;
                   
                    if (data.Main.UploadCoverFile?.ContentLength > 0)
                    {
                        SaveUp.COVER_FILE = UpLoadFile(SaveUp.SCHOOL_NO, SaveUp.ART_GALLERY_NO, data.Main.UploadCoverFile, ref Message);

                        if (string.IsNullOrWhiteSpace(Message) == false)
                        {

                            zZZI34ViewBack.status = false;
                            return zZZI34ViewBack;
                        }
                    }
                }
                else if (SaveUp == null)
                {
                    SaveUp = new ADDT21();

                    SaveUp.ART_GALLERY_NO = Guid.NewGuid().ToString("N");
                    SaveUp.ART_GALLERY_TYPE = data.Main.ART_GALLERY_TYPE;
                    SaveUp.ART_SUBJECT = data.Main.ART_SUBJECT;
                    SaveUp.ART_DESC = data.Main.ART_DESC;
                    SaveUp.WORK_TYPE = data.Main.WORK_TYPE;
           
                    if (SaveUp.ART_GALLERY_TYPE == ADDT21.ART_GALLERY_TYPE_VAL.Campus)
                    {
                        SaveUp.STATUS = ADDT21.STATUSVal.NotStarted;
                    }
                    else
                    {
                        SaveUp.STATUS = ADDT21.STATUSVal.Verification;
                    }

                    SaveUp.SCHOOL_NO = User.SCHOOL_NO;
                    SaveUp.USER_NO = User.USER_NO;
                    SaveUp.NAME = User.NAME;
                    SaveUp.SNAME = User.SNAME;
                    SaveUp.CLASS_NO = User.CLASS_NO;
                    SaveUp.SYEAR = Convert.ToByte(SYear);
                    SaveUp.SEMESTER = Convert.ToByte(Semesters);
                    SaveUp.CRE_PERSON = User.USER_KEY;
                    SaveUp.CRE_DATE = DateTime.Now;
                    SaveUp.CHG_PERSON = User.USER_KEY;
                    SaveUp.CHG_DATE = DateTime.Now;

                    SaveUp.COVER_FILE = UpLoadFile(SaveUp.SCHOOL_NO, SaveUp.ART_GALLERY_NO, data.Main.UploadCoverFile, ref Message);

                    if (string.IsNullOrWhiteSpace(Message) == false)
                    {
                        zZZI34ViewBack.status = false;
                        return zZZI34ViewBack;
                    }

                    //找老師
                    HRMT03 TeachInfo =
                        db.HRMT03.Where(a => a.SCHOOL_NO == SaveUp.SCHOOL_NO && a.CLASS_NO == SaveUp.CLASS_NO).FirstOrDefault();
                    if (TeachInfo != null)
                    {
                        SaveUp.VERIFIER = TeachInfo.TEACHER_NO;
                    }

                    db.ADDT21.Add(SaveUp);
                }

                List<ADDT22> SaveADDT22_List = new List<ADDT22>();

                int ORDER_BY_NUM = 0;

                string CheckNo = string.Empty;
                if (data.Details_List != null)

                {
                    if (data.Details_List.Count() > 0)
                    {   //移除DEL有勾選的資料
                        data.Details_List.RemoveAll(a => a.Del == true);
                        foreach (var Peitem in data.Details_List)
                        {
                            ORDER_BY_NUM = ORDER_BY_NUM + 1;

                            bool IsCreSavePhotoUp = false;

                            ADDT22 SavePhotoUp = null;
                            var Hr = db.HRMT01.Where(a => a.SCHOOL_NO == SaveUp.SCHOOL_NO && a.USER_NO == Peitem.USER_NO).FirstOrDefault();
                            if (Hr == null)
                            {
                                Message = $"找不到此帳號{Peitem.USER_NO}";
                                zZZI34ViewBack.status = false;
                                return zZZI34ViewBack;
                            }
                            if (string.IsNullOrWhiteSpace(Peitem.PHOTO_NO))
                            {
                                SavePhotoUp = new ADDT22();
                                SavePhotoUp.PHOTO_NO = Guid.NewGuid().ToString("N");
                                SavePhotoUp.ART_GALLERY_NO = SaveUp.ART_GALLERY_NO;
                                SavePhotoUp.PHOTO_STATUS = ADDT22.STATUSVal.OK;
                                SavePhotoUp.PHOTO_SYEAR = Convert.ToByte(SYear);
                                SavePhotoUp.PHOTO_SEMESTER = Convert.ToByte(Semesters);
                                SavePhotoUp.CRE_PERSON = User.USER_KEY;
                                SavePhotoUp.CRE_DATE = DateTime.Now;
                                SavePhotoUp.AutherYN = false;
                                IsCreSavePhotoUp = true;
                            }
                            else
                            {
                                SavePhotoUp = db.ADDT22.Where(a => a.ART_GALLERY_NO == SaveUp.ART_GALLERY_NO && a.PHOTO_NO == Peitem.PHOTO_NO).FirstOrDefault();

                                if (SavePhotoUp == null)
                                {
                                    SavePhotoUp = new ADDT22();
                                    SavePhotoUp.PHOTO_NO = Peitem.PHOTO_NO;
                                    SavePhotoUp.ART_GALLERY_NO = SaveUp.ART_GALLERY_NO;
                                    SavePhotoUp.PHOTO_STATUS = ADDT22.STATUSVal.OK;
                                    SavePhotoUp.PHOTO_SYEAR = Convert.ToByte(SYear);
                                    SavePhotoUp.PHOTO_SEMESTER = Convert.ToByte(Semesters);
                                    SavePhotoUp.CRE_PERSON = User.USER_KEY;
                                    SavePhotoUp.AutherYN = false;
                                    SavePhotoUp.CRE_DATE = DateTime.Now;

                                    IsCreSavePhotoUp = true;
                                }

                                if (SavePhotoUp.PHOTO_STATUS == ADDT22.STATUSVal.NG)
                                {
                                    Message = "此狀態不能異動資料(已作廢)";
                                    zZZI34ViewBack.status = false;
                                    return zZZI34ViewBack;
                                }
                            }

                            if (Peitem.IMG_FILE != null)
                            {
                                SavePhotoUp.PHOTO_FILE = CopyUpLoadFile(SaveUp.SCHOOL_NO, SaveUp.ART_GALLERY_NO, Peitem.SIMG_PATH, Peitem.MIMG_PATH, Peitem.IMG_FILE, Peitem.filenames, ref Message);
                                if (string.IsNullOrWhiteSpace(Message) == false)
                                {
                                    Message = $"主題「{Peitem.PHOTO_SUBJECT}」，" + Message;
                                    zZZI34ViewBack.status = false;
                                    return zZZI34ViewBack;
                                }
                            }
                            SavePhotoUp.PHOTO_SUBJECT = Peitem.PHOTO_SUBJECT;
                            SavePhotoUp.PHOTO_DESC = Peitem.PHOTO_DESC;

                            if (SaveUp.ART_GALLERY_TYPE == ADDT21.ART_GALLERY_TYPE_VAL.Campus && SaveUp.STATUS == ADDT21.STATUSVal.NotStarted)
                            {
                                SavePhotoUp.PHOTO_CASH = Peitem.PHOTO_CASH;
                            }

                            SavePhotoUp.PHOTO_SCHOOL_NO = SaveUp.SCHOOL_NO;
                            SavePhotoUp.PHOTO_USER_NO = Peitem.USER_NO;
                            SavePhotoUp.PHOTO_NAME = Hr?.NAME;
                            SavePhotoUp.PHOTO_SNAME = Hr?.SNAME;
                            SavePhotoUp.PHOTO_CLASS_NO = Hr?.CLASS_NO;
                            SavePhotoUp.AutherYN= false;
                            SavePhotoUp.CHG_PERSON = User.USER_KEY;
                            SavePhotoUp.CHG_DATE = DateTime.Now;

                            SavePhotoUp.PHOTO_ORDER_BY = ORDER_BY_NUM;

                            if (IsCreSavePhotoUp)
                            {
                                db.ADDT22.Add(SavePhotoUp);
                            }

                            SaveADDT22_List.Add(SavePhotoUp);
                        }
                    }
                }
                if (data.People != null)
                {
                    if (data.People.Count() > 0)
                    {
                        foreach (var Peitem in data.People)
                        {
                            if (Peitem.Photo?.Count() > 0)
                            {
                                foreach (var PhotoItem in Peitem.Photo)
                                {
                                    ORDER_BY_NUM = ORDER_BY_NUM + 1;

                                    bool IsCreSavePhotoUp = false;

                                    ADDT22 SavePhotoUp = null;

                                    var Hr = db.HRMT01.Where(a => a.SCHOOL_NO == SaveUp.SCHOOL_NO && a.USER_NO == Peitem.PHOTO_USER_NO).FirstOrDefault();

                                    if (Hr == null)
                                    {
                                        Message = $"找不到此帳號{Peitem.PHOTO_USER_NO}";
                                        zZZI34ViewBack.status = false;
                                        return zZZI34ViewBack;
                                    }

                                    if (string.IsNullOrWhiteSpace(PhotoItem.PHOTO_NO))
                                    {
                                        SavePhotoUp = new ADDT22();
                                        SavePhotoUp.PHOTO_NO = Guid.NewGuid().ToString("N");
                                        SavePhotoUp.ART_GALLERY_NO = SaveUp.ART_GALLERY_NO;
                                        SavePhotoUp.PHOTO_STATUS = ADDT22.STATUSVal.OK;
                                        SavePhotoUp.PHOTO_SYEAR = Convert.ToByte(SYear);
                                        SavePhotoUp.PHOTO_SEMESTER = Convert.ToByte(Semesters);
                                        SavePhotoUp.AutherYN = false;
                                        SavePhotoUp.CRE_PERSON = User.USER_KEY;
                                        SavePhotoUp.CRE_DATE = DateTime.Now;

                                        IsCreSavePhotoUp = true;
                                    }
                                    else
                                    {
                                        SavePhotoUp = db.ADDT22.Where(a => a.ART_GALLERY_NO == SaveUp.ART_GALLERY_NO && a.PHOTO_NO == PhotoItem.PHOTO_NO).FirstOrDefault();

                                        if (SavePhotoUp == null)
                                        {
                                            SavePhotoUp = new ADDT22();
                                            SavePhotoUp.PHOTO_NO = PhotoItem.PHOTO_NO;
                                            SavePhotoUp.ART_GALLERY_NO = SaveUp.ART_GALLERY_NO;
                                            SavePhotoUp.PHOTO_STATUS = ADDT22.STATUSVal.OK;
                                            SavePhotoUp.PHOTO_SYEAR = Convert.ToByte(SYear);
                                            SavePhotoUp.AutherYN = false;
                                            SavePhotoUp.PHOTO_SEMESTER = Convert.ToByte(Semesters);
                                            SavePhotoUp.CRE_PERSON = User.USER_KEY;
                                            SavePhotoUp.CRE_DATE = DateTime.Now;

                                            IsCreSavePhotoUp = true;
                                        }

                                        if (SavePhotoUp.PHOTO_STATUS == ADDT22.STATUSVal.NG)
                                        {
                                            Message = "此狀態不能異動資料(已作廢)";
                                            zZZI34ViewBack.status = false;
                                            return zZZI34ViewBack;
                                        }
                                    }

                                    if (data.Main.WORK_TYPE == ADDT21.WORK_TYPE_VAL.Youtube)
                                    {
                                        if (!string.IsNullOrWhiteSpace(PhotoItem.PHOTO_FILE))
                                        {
                                            if (PhotoItem.PHOTO_FILE.ToLower().IndexOf("https://www.youtube.com/embed/") == -1)
                                            {
                                                var Index = PhotoItem.PHOTO_FILE.ToLower().IndexOf("https://www.youtube.com/watch?v=");

                                                if (Index == -1 && IsTempSave == false)
                                                {
                                                    Message = $"主題「{PhotoItem.PHOTO_SUBJECT}」，輸入 Youtube 網址 不是有效的，請輸入https://www.youtube.com/embed/影片id";
                                                    zZZI34ViewBack.status = false;
                                                    return zZZI34ViewBack;
                                                }
                                                else
                                                {
                                                    PhotoItem.PHOTO_FILE = PhotoItem.PHOTO_FILE.Replace("https://www.youtube.com/watch?v=", "https://www.youtube.com/embed/");
                                                }
                                            }

                                            Uri urlCheck = new Uri(PhotoItem.PHOTO_FILE);
                                            WebRequest request = WebRequest.Create(urlCheck);
                                            request.Timeout = 15000;
                                            WebResponse response;
                                            try
                                            {
                                                response = request.GetResponse();
                                                SavePhotoUp.PHOTO_FILE = PhotoItem.PHOTO_FILE;
                                            }
                                            catch (Exception)
                                            {
                                                Message = $"主題「{PhotoItem.PHOTO_SUBJECT}」，輸入 Youtube 網址{PhotoItem.PHOTO_FILE} 不是有效的";
                                                zZZI34ViewBack.status = false;
                                                return zZZI34ViewBack;
                                            }
                                        }
                                    }
                                    else
                                    {
                                        if (PhotoItem.PhotoFiles?.ContentLength > 0)
                                        {
                                            SavePhotoUp.PHOTO_FILE = UpLoadFile(SaveUp.SCHOOL_NO, SaveUp.ART_GALLERY_NO, PhotoItem.PhotoFiles, ref Message, data.Main.WORK_TYPE);
                                            if (string.IsNullOrWhiteSpace(Message) == false)
                                            {
                                                Message = $"主題「{PhotoItem.PHOTO_SUBJECT}」，" + Message;
                                                zZZI34ViewBack.status = false;
                                                return zZZI34ViewBack;
                                            }
                                        }
                                    }

                                    SavePhotoUp.PHOTO_SUBJECT = PhotoItem.PHOTO_SUBJECT;
                                    SavePhotoUp.PHOTO_DESC = PhotoItem.PHOTO_DESC;

                                    if (SaveUp.ART_GALLERY_TYPE == ADDT21.ART_GALLERY_TYPE_VAL.Campus && SaveUp.STATUS == ADDT21.STATUSVal.NotStarted)
                                    {
                                        SavePhotoUp.PHOTO_CASH = PhotoItem.PHOTO_CASH;
                                    }

                                    SavePhotoUp.PHOTO_SCHOOL_NO = SaveUp.SCHOOL_NO;
                                    SavePhotoUp.PHOTO_USER_NO = Peitem.PHOTO_USER_NO;
                                    SavePhotoUp.PHOTO_NAME = Hr?.NAME;
                                    SavePhotoUp.PHOTO_SNAME = Hr?.SNAME;
                                    SavePhotoUp.PHOTO_CLASS_NO = Hr?.CLASS_NO;
                                    SavePhotoUp.AutherYN = Peitem.AutherYN;
                                    SavePhotoUp.CHG_PERSON = User.USER_KEY;
                                    SavePhotoUp.CHG_DATE = DateTime.Now;

                                    SavePhotoUp.PHOTO_ORDER_BY = ORDER_BY_NUM;

                                    if (IsCreSavePhotoUp)
                                    {
                                        db.ADDT22.Add(SavePhotoUp);
                                    }

                                    SaveADDT22_List.Add(SavePhotoUp);
                                }
                            }
                        }
                    }
                }

                if (SaveADDT22_List.Count() > 0)
                {
                    var OrtGallery = db.ADDT22.Where(o => o.ART_GALLERY_NO == SaveUp.ART_GALLERY_NO).ToList();

                    if (OrtGallery.Count > 0)
                    {
                        var Difference = from o in OrtGallery
                                         where !(
                                             from N in SaveADDT22_List
                                             select N.PHOTO_NO
                                           ).Contains(o.PHOTO_NO)
                                         select o;
                        if (data.Main.WORK_TYPE == ADDT21.WORK_TYPE_VAL.Youtube)
                        {
                            db.ADDT22.RemoveRange(Difference);
                        }
                        else
                        {
                            foreach (var item in Difference)
                            {
                                DelFile(item.PHOTO_SCHOOL_NO, item.ART_GALLERY_NO, item.PHOTO_FILE, ref Message);

                                if (string.IsNullOrWhiteSpace(Message) == false)
                                {
                                    Message = $"主題「{item.PHOTO_SUBJECT}」，" + Message;
                                    zZZI34ViewBack.status = false;
                                    return zZZI34ViewBack;
                                }
                            }

                            db.ADDT22.RemoveRange(Difference);
                        }
                    }
                }

                try
                {
                    db.SaveChanges();
                }
                catch (Exception ex)
                {
                    Message = "系統發生錯誤;原因:" + ex.Message;
                    zZZI34ViewBack.status = false;
                    return zZZI34ViewBack;
                }

                tx.Complete();
            }
            zZZI34ViewBack.WhereART_GALLERY_NO = SaveUp.ART_GALLERY_NO;
            zZZI34ViewBack.status = true;
            return zZZI34ViewBack;
        }

        public bool ShareSaveData(ZZZI34ShareViewModel model, UserProfile User, ref ECOOL_DEVEntities db, ref string Message, ref int LikeCount, ref List<Tuple<string, string, int>> valuesList)
        {
            var CheckPhoto = db.ADDT22.Where(a => a.PHOTO_NO == model.PHOTO_NO).FirstOrDefault();
            if (CheckPhoto == null)
            {
                Message = $"系統發生錯誤;原因:找不到此筆資料PHOTO_NO={model.PHOTO_NO}";
                return false;
            }

            if (string.IsNullOrWhiteSpace(model.NICK_NAME) && User == null)
            {
                Message = $"未輸入訪客名稱";
                return false;
            }

            if (string.IsNullOrWhiteSpace(model.IP_ADDRESS))
            {
                Message = $"未取得訪客IP";
                return false;
            }

            var check = db.ADDT23.Where(a => a.PHOTO_NO == model.PHOTO_NO);

            if (User != null)
            {
                check = check.Where(a => a.USER_NO == User.USER_NO && a.SCHOOL_NO == User.SCHOOL_NO);
            }
            else
            {
                check = check.Where(a => a.IP_ADDRESS == model.IP_ADDRESS);
            }

            if (check.Any() == false)
            {
                if (db.Database.Connection.State != ConnectionState.Open) db.Database.Connection.Open();
                using (TransactionScope tx = new TransactionScope())
                {
                    try
                    {
                        ADDT23 SaveUp = new ADDT23();

                        SaveUp.COMMENT_NO = Guid.NewGuid().ToString("N");
                        SaveUp.COMMENT_TYPE = ADDT23.COMMENT_TYPEVal.like;
                        SaveUp.PHOTO_NO = CheckPhoto.PHOTO_NO;

                        if (User != null)
                        {
                            SaveUp.SCHOOL_NO = User.SCHOOL_NO;
                            SaveUp.USER_NO = User.USER_NO;
                            SaveUp.CLASS_NO = User.CLASS_NO;
                            SaveUp.NICK_NAME = User.SNAME;
                        }
                        else
                        {
                            SaveUp.NICK_NAME = model.NICK_NAME;
                        }

                        SaveUp.COMMENT = model.COMMENT;
                        SaveUp.CRE_DATE = DateTime.Now;
                        SaveUp.IP_ADDRESS = model.IP_ADDRESS;
                        SaveUp.COMMENT_STATUS = ADDT23.COMMENT_STATUS_VAL.OK;

                        db.ADDT23.Add(SaveUp);

                        try
                        {
                            db.SaveChanges();
                        }
                        catch (Exception ex)
                        {
                            Message = "系統發生錯誤;原因:" + ex.Message;
                            return false;
                        }

                        //累積15個讚，加2點

                        LikeCount = db.ADDT23.Where(a => a.PHOTO_NO == CheckPhoto.PHOTO_NO).Count();

                        if (LikeCount == 15)
                        {
                            ECOOL_APP.CashHelper.AddCash(User, 2, CheckPhoto.PHOTO_SCHOOL_NO, CheckPhoto.PHOTO_USER_NO, "ADDT23", CheckPhoto.PHOTO_NO, true, ref db ,ref valuesList);
                        }

                        try
                        {
                            db.SaveChanges();
                            tx.Complete();
                            return true;
                        }
                        catch (Exception ex)
                        {
                            Message = "系統發生錯誤;原因:" + ex.Message;
                            return false;
                        }
                        
                    }
                    catch (Exception ex)
                    {
                        Message = "系統發生錯誤;原因:" + ex.Message;
                        return false;
                    }
                }
            }

            return true;
        }

        public bool UpdateStatus(ZZZI34EditViewModel data, UserProfile User, ref ECOOL_DEVEntities db, ref string Message, bool VisibleVerify , ref List<Tuple<string, string, int>> valuesList)
        {
            bool ISAdmin = HRMT24_ENUM.CheckQAdmin(User);

            ADDT21 SaveUp = db.ADDT21.Where(a => a.ART_GALLERY_NO == data.Search.WhereART_GALLERY_NO).FirstOrDefault();

            if (SaveUp == null)
            {
                Message = "系統發生錯誤;原因:找不到此筆資料";
                return false;
            }

            if (data.SaveType == ZZZI34EditViewModel.SaveTypeVal.Release)
            {
                if (SaveUp.STATUS != ADDT21.STATUSVal.NotStarted)
                {
                    Message = "您目前的狀態不可發行!!";
                    return false;
                }

                if (SaveUp.CRE_PERSON != User.USER_KEY && !ISAdmin)
                {
                    Message = "系統發生錯誤;原因:你無異動資料權限(A)";
                    return false;
                }

                SaveUp.STATUS = ADDT21.STATUSVal.Verification;
                Message = "發行完成";
            }
            else if (data.SaveType == ZZZI34EditViewModel.SaveTypeVal.Verify)
            {
                if (SaveUp.STATUS != ADDT21.STATUSVal.NotStarted && SaveUp.STATUS != ADDT21.STATUSVal.Verification)
                {
                    Message = "您目前的狀態不可異動!!";
                    return false;
                }

                if (SaveUp.ART_GALLERY_TYPE == ADDT21.ART_GALLERY_TYPE_VAL.Campus)
                {
                    if (SaveUp.CRE_PERSON != User.USER_KEY && !ISAdmin)
                    {
                        Message = "系統發生錯誤;原因:你無異動資料權限(B)";
                        return false;
                    }

                    Message = "公開完成";
                }
                else
                {
                    if (SaveUp.VERIFIER != User.USER_NO && !ISAdmin && VisibleVerify == false)
                    {
                        Message = "系統發生錯誤;原因:你無異動資料權限(C)";
                        return false;
                    }

                    Message = "審核通過";
                }

                SaveUp.STATUS = ADDT21.STATUSVal.Pass;
                if (data.Details_List != null && data.Details_List.Count() > 0)
                {
                    var SaveUpList = db.ADDT22.Where(a => a.ART_GALLERY_NO == SaveUp.ART_GALLERY_NO).ToList();

                    if (SaveUpList != null)
                    {
                        foreach (var itmes in SaveUpList)
                        {
                            var SaveUpD = db.ADDT22.Where(a => a.ART_GALLERY_NO == SaveUp.ART_GALLERY_NO && a.PHOTO_NO == itmes.PHOTO_NO).FirstOrDefault();
                            if (SaveUp.ART_GALLERY_TYPE == ADDT21.ART_GALLERY_TYPE_VAL.Personal)
                            {
                                if (itmes.PHOTO_CASH == null)
                                {
                                    Message = $"主題「{itmes.PHOTO_SUBJECT}」，未輸入酷幣點數";
                                    return false;
                                }
                                else if ((itmes.PHOTO_CASH ?? 0) < 0)
                                {
                                    Message = $"主題「{itmes.PHOTO_SUBJECT}」，酷幣點數小於0";
                                    return false;
                                }
                                else if ((itmes.PHOTO_CASH ?? 0) > 10)
                                {
                                    Message = $"主題「{itmes.PHOTO_SUBJECT}」，酷幣點數大於10";
                                    return false;
                                }

                                SaveUpD.PHOTO_CASH = itmes.PHOTO_CASH;
                                db.Entry(SaveUpD).State = System.Data.Entity.EntityState.Modified;
                            }

                            ECOOL_APP.CashHelper.AddCash(User, Convert.ToInt32(SaveUpD.PHOTO_CASH), SaveUpD.PHOTO_SCHOOL_NO, SaveUpD.PHOTO_USER_NO, "ZZZI34", SaveUpD.PHOTO_NO.ToString(), true, ref db ,ref valuesList);
                        }
                    }
                }
                else
                {
                    foreach (var PeopleItem in data.People)
                    {
                        foreach (var PhotoItem in PeopleItem.Photo)
                        {
                            var SaveUpD = db.ADDT22.Where(a => a.ART_GALLERY_NO == SaveUp.ART_GALLERY_NO && a.PHOTO_NO == PhotoItem.PHOTO_NO).FirstOrDefault();

                            if (SaveUpD != null)
                            {
                                if (SaveUp.ART_GALLERY_TYPE == ADDT21.ART_GALLERY_TYPE_VAL.Personal)
                                {
                                    if (PhotoItem.PHOTO_CASH == null)
                                    {
                                        Message = $"主題「{SaveUpD.PHOTO_SUBJECT}」，未輸入酷幣點數";
                                        return false;
                                    }
                                    else if ((PhotoItem.PHOTO_CASH ?? 0) < 0)
                                    {
                                        Message = $"主題「{SaveUpD.PHOTO_SUBJECT}」，酷幣點數小於0";
                                        return false;
                                    }
                                    else if ((PhotoItem.PHOTO_CASH ?? 0) > 10)
                                    {
                                        Message = $"主題「{SaveUpD.PHOTO_SUBJECT}」，酷幣點數大於10";
                                        return false;
                                    }

                                    SaveUpD.PHOTO_CASH = PhotoItem.PHOTO_CASH;
                                    db.Entry(SaveUpD).State = System.Data.Entity.EntityState.Modified;
                                }
                                AWAT14 aWAT14 = new AWAT14();
                                int? AWAT14_CASH = 0;
                                int? RankNO = 0;
                                int CashALLSum = 0;
                                aWAT14 = db.AWAT14.Where(x => x.SCHOOL_NO == SaveUpD.PHOTO_SCHOOL_NO && x.USER_NO == SaveUpD.PHOTO_USER_NO).FirstOrDefault();
                                if (aWAT14 == null)
                                {

                                    AWAT14_CASH = db.BDMT01.Where(x => x.SCHOOL_NO == SaveUpD.PHOTO_SCHOOL_NO).Select(x => x.AWAT14_CASH).FirstOrDefault();

                                    RankNO = (CashALLSum / AWAT14_CASH);
                                    aWAT14 = new AWAT14();
                                    aWAT14.SCHOOL_NO = SaveUpD.PHOTO_SCHOOL_NO;
                                    aWAT14.USER_NO = SaveUpD.PHOTO_USER_NO;
                                    aWAT14.AWAT14_CASH = (int)AWAT14_CASH;
                                    aWAT14.ARRIVED_CASH = (CashALLSum / AWAT14_CASH + 1) * AWAT14_CASH - CashALLSum;
                                    aWAT14.CASH_Rank = RankNO;
                                    aWAT14.CHG_DATE = DateTime.Now;
                                    db.AWAT14.Add(aWAT14);
                                    db.SaveChanges();
                                }
                                ECOOL_APP.CashHelper.AddCash(User, Convert.ToInt32(SaveUpD.PHOTO_CASH), SaveUpD.PHOTO_SCHOOL_NO, SaveUpD.PHOTO_USER_NO, "ZZZI34", SaveUpD.PHOTO_NO.ToString(), true, ref db,ref valuesList);
                            }
                        }
                    }
                }
            }
            else if (data.SaveType == ZZZI33EditViewModel.SaveTypeVal.Disabled)
            {
                if (SaveUp.CRE_PERSON != User.USER_KEY && SaveUp.VERIFIER != User.USER_NO && !ISAdmin && VisibleVerify == false)
                {
                    Message = "系統發生錯誤;原因:你無異動資料權限(D)";
                    return false;
                }

                if (SaveUp.CRE_PERSON != User.USER_KEY)
                {
                    if (string.IsNullOrWhiteSpace(data.Main.BACK_MEMO))
                    {
                        Message = "系統發生錯誤;原因:未輸入作廢原因";
                        return false;
                    }
                    SaveUp.BACK_MEMO = data.Main.BACK_MEMO;

                    var SaveUpD = db.ADDT22.Where(a => a.ART_GALLERY_NO == SaveUp.ART_GALLERY_NO && a.PHOTO_STATUS == ADDT22.STATUSVal.OK && (a.PHOTO_CASH ?? 0) > 0).ToList();

                    foreach (var item in SaveUpD)
                    {
                        ECOOL_APP.CashHelper.AddCash(User, Convert.ToInt32(item.PHOTO_CASH) * -1, item.PHOTO_SCHOOL_NO, item.PHOTO_USER_NO, "ZZZI34", item.PHOTO_NO.ToString(), "藝廊作廢", false, ref db,"", "",ref valuesList);
                    }
                }
                else
                {
                    if (SaveUp.STATUS == ADDT21.STATUSVal.NotStarted)
                    {
                        SaveUp.BACK_MEMO = "作者自行作廢";
                    }
                    else
                    {
                        if (string.IsNullOrWhiteSpace(data.Main.BACK_MEMO))
                        {
                            Message = "系統發生錯誤;原因:未輸入作廢原因";
                            return false;
                        }
                        SaveUp.BACK_MEMO = data.Main.BACK_MEMO;
                    }
                }

                SaveUp.DEL_DATE = DateTime.Now;
                SaveUp.DEL_PERSON = User.USER_KEY;
                SaveUp.STATUS = ADDT21.STATUSVal.Disabled;
                Message = "資料已作廢";
            }

            SaveUp.CHG_PERSON = User.USER_KEY; ;
            SaveUp.CHG_DATE = DateTime.Now;

            db.Entry(SaveUp).State = System.Data.Entity.EntityState.Modified;

            try
            {
                db.SaveChanges();
                return true;
            }
            catch (Exception ex)
            {
                Message = "系統發生錯誤;原因:" + ex.Message;
                return false;
            }
        }

        /// <summary>
        /// DelFileData
        /// </summary>
        /// <param name="User"></param>
        /// <param name="db"></param>
        /// <param name="Message"></param>
        /// <param name="ART_GALLERY_NO"></param>
        /// <param name="PHOTO_NO"></param>
        /// <returns></returns>
        //public bool DelFileData(UserProfile User, ref ECOOL_DEVEntities db,ref string Message,string ART_GALLERY_NO, string PHOTO_NO = null)
        //{
        //    var SaveUp = db.ADDT21.Where(a => a.ART_GALLERY_NO == ART_GALLERY_NO).FirstOrDefault();

        //    if (SaveUp == null)
        //    {
        //        Message = "系統發生錯誤;原因:找不到此筆資料";
        //        return false;
        //    }

        //    if (SaveUp.STATUS == ADDT21.STATUSVal.Disabled)
        //    {
        //        Message = "系統發生錯誤;原因:此狀態不能異動資料";
        //        return false;
        //    }

        //    if (SaveUp.CRE_PERSON != User.USER_KEY && User.USER_TYPE!= UserType.Admin)
        //    {
        //        Message = "系統發生錯誤;原因:你無異動資料權限";
        //        return false;
        //    }

        //    if (string.IsNullOrWhiteSpace(PHOTO_NO))
        //    {
        //        DelFile(SaveUp.SCHOOL_NO, SaveUp.ART_GALLERY_NO, SaveUp.COVER_FILE, ref Message);
        //        if (string.IsNullOrWhiteSpace(Message) == false)
        //        {
        //            return false;
        //        }

        //        SaveUp.COVER_FILE = null;
        //    }
        //    else
        //    {
        //        var SaveUpD = db.ADDT22.Where(a => a.ART_GALLERY_NO == ART_GALLERY_NO && a.PHOTO_NO == PHOTO_NO).FirstOrDefault();

        //        if (SaveUpD == null)
        //        {
        //            Message = "系統發生錯誤;原因:找不到此筆資料";
        //            return false;
        //        }

        //        DelFile(SaveUp.SCHOOL_NO, SaveUp.ART_GALLERY_NO, SaveUpD.PHOTO_FILE, ref Message);
        //        if (string.IsNullOrWhiteSpace(Message) == false)
        //        {
        //            return false;
        //        }

        //        SaveUpD.PHOTO_FILE = null;
        //    }

        //    try
        //    {
        //        db.SaveChanges();
        //        return true;
        //    }
        //    catch (Exception ex)
        //    {
        //        Message = "系統發生錯誤;原因:" + ex.Message;
        //        return false;
        //    }
        //}

        public bool DelFile(string SCHOOL_NO, string ART_GALLERY_NO, string PHOTO_FILE, ref string Message)
        {
            try
            {
                string tempPath = GetSysArtGalleryPath(SCHOOL_NO, ART_GALLERY_NO) + PHOTO_FILE;

                if (File.Exists(tempPath))
                {
                    File.Delete(tempPath);
                }

                return true;
            }
            catch (Exception ex)
            {
                Message = "系統發生錯誤;原因:" + ex.Message;
                return false;
            }
        }

        public string CopyUpLoadFile(string SCHOOL_NO, string ART_GALLERY_NO, string Sfileimg, string Mfileimg, string IMG_FILE, string fileName, ref string Message)
        {
            try
            {
                string tempPath = GetSysArtGalleryPath(SCHOOL_NO, ART_GALLERY_NO);
                if (Directory.Exists(tempPath) == false)
                {
                    Directory.CreateDirectory(tempPath);
                }

                if (!string.IsNullOrWhiteSpace(Sfileimg))
                {
                    string UpLoadFile = tempPath + "\\" + fileName;
                    string Extension = Path.GetExtension(UpLoadFile);
                    string NewPath = UpLoadFile.Replace(Extension, "_S" + Extension);
                    string NewPath1 = UpLoadFile.Replace(Extension, "_M" + Extension);
                    System.IO.File.Copy(Sfileimg, UpLoadFile, true);
                    System.IO.File.Copy(Sfileimg, NewPath, true);
                    System.IO.File.Copy(Mfileimg, NewPath, true);
                }
            }
            catch (Exception ex)
            {
                Message = "系統發生錯誤;原因:" + ex.Message;
            }
            return fileName;
        }
        public string UpLoadfodoFile( HttpPostedFileBase file, ref string Message)
        {
            string fileName = string.Empty;

            try
            {
                string tempPath = GetSysfodoimgPath();

                if (Directory.Exists(tempPath) == false)
                {
                    Directory.CreateDirectory(tempPath);
                }

                if (file != null && file.ContentLength > 0)
                {
                    fileName = Path.GetFileName(file.FileName);


                    string UpLoadFile = tempPath + "\\" + fileName;

                    if (System.IO.File.Exists(UpLoadFile))
                    {
                        System.IO.File.Delete(UpLoadFile);
                    }

                    file.SaveAs(UpLoadFile);


                }
            }
            catch (Exception ex)
            {
                Message = "系統發生錯誤;原因:" + ex.Message;
            }

            return fileName;
        }
        public string UpLoadFile(string SCHOOL_NO, string ART_GALLERY_NO, HttpPostedFileBase file, ref string Message, string WORK_TYPE = null)
        {
            string fileName = string.Empty;

            try
            {
                string tempPath = GetSysArtGalleryPath(SCHOOL_NO, ART_GALLERY_NO);

                if (Directory.Exists(tempPath) == false)
                {
                    Directory.CreateDirectory(tempPath);
                }

                if (file != null && file.ContentLength > 0)
                {
                    fileName = Path.GetFileName(file.FileName);

                    if (string.IsNullOrWhiteSpace(WORK_TYPE) || WORK_TYPE == ADDT21.WORK_TYPE_VAL.photo)
                    {
                        Regex regexCode = new Regex(@".*\.(jpg|jpeg|png|gif|bmp)");
                        if (regexCode.IsMatch(fileName.ToLower()) == false)
                        {
                            Message = "請上傳 jpg、jpeg、png、gif、bmp 等格式的圖片";
                            return fileName;
                        }
                    }
                    else
                    {
                        Regex regexCode = new Regex(@".*\.(mp4)");
                        if (regexCode.IsMatch(fileName.ToLower()) == false)
                        {
                            Message = "請上傳 mp4 等格式的影片";
                            return fileName;
                        }
                    }

                    if (file.ContentLength / 1024 > (1024 * 20)) // 20MB
                    {
                        Message = "上傳檔案不能超過20MB";
                        return fileName;
                    }

                    string UpLoadFile = tempPath + "\\" + fileName;

                    if (System.IO.File.Exists(UpLoadFile))
                    {
                        System.IO.File.Delete(UpLoadFile);
                    }

                    file.SaveAs(UpLoadFile);

                    if (string.IsNullOrWhiteSpace(WORK_TYPE) || WORK_TYPE == ADDT21.WORK_TYPE_VAL.photo)
                    {
                        SaveImageM(file, UpLoadFile);
                        SaveImageS(file, UpLoadFile);
                    }
                }
            }
            catch (Exception ex)
            {
                Message = "系統發生錯誤;原因:" + ex.Message;
            }

            return fileName;
        }

        public string UpLoadFilesetImg(string SCHOOL_NO, string ART_GALLERY_NO, HttpPostedFileBase file, ref string Message, string WORK_TYPE = null)
        {
            string fileName = string.Empty;

            try
            {
                string tempPath = GetSysArtGalleryimgPath(SCHOOL_NO, ART_GALLERY_NO);

                if (Directory.Exists(tempPath) == false)
                {
                    Directory.CreateDirectory(tempPath);
                }

                if (file != null && file.ContentLength > 0)
                {
                    fileName = Path.GetFileName(file.FileName);

                    if (string.IsNullOrWhiteSpace(WORK_TYPE) || WORK_TYPE == ADDT21.WORK_TYPE_VAL.photo)
                    {
                        Regex regexCode = new Regex(@".*\.(jpg|jpeg|png|gif|bmp)");
                        if (regexCode.IsMatch(fileName.ToLower()) == false)
                        {
                            Message = "請上傳 jpg、jpeg、png、gif、bmp 等格式的圖片";
                            return fileName;
                        }
                    }
                    else
                    {
                        Regex regexCode = new Regex(@".*\.(mp4)");
                        if (regexCode.IsMatch(fileName.ToLower()) == false)
                        {
                            Message = "請上傳 mp4 等格式的影片";
                            return fileName;
                        }
                    }

                    if (file.ContentLength / 1024 > (1024 * 20)) // 20MB
                    {
                        Message = "上傳檔案不能超過20MB";
                        return fileName;
                    }

                    string UpLoadFile = tempPath + "\\" + fileName;

                    if (System.IO.File.Exists(UpLoadFile))
                    {
                        System.IO.File.Delete(UpLoadFile);
                    }

                    file.SaveAs(UpLoadFile);

                    if (string.IsNullOrWhiteSpace(WORK_TYPE) || WORK_TYPE == ADDT21.WORK_TYPE_VAL.photo)
                    {
                        SaveImageM(file, UpLoadFile);
                        SaveImageS(file, UpLoadFile);
                    }
                }
            }
            catch (Exception ex)
            {
                Message = "系統發生錯誤;原因:" + ex.Message;
            }

            return fileName;
        }

        private void SaveImageM(HttpPostedFileBase file, string UpLoadFile)
        {
            string Extension = Path.GetExtension(UpLoadFile);
            string NewPath = UpLoadFile.Replace(Extension, "_M" + Extension);

            SaveThumbnailImage(file, 1600, 1600, NewPath);
        }

        private void SaveImageS(HttpPostedFileBase file, string UpLoadFile)
        {
            string Extension = Path.GetExtension(UpLoadFile);
            string NewPath = UpLoadFile.Replace(Extension, "_S" + Extension);

            SaveThumbnailImage(file, 200, 150, NewPath);
        }

        public void SaveThumbnailImage(string filePath, double FixWidth, double FixHeight, string NewImagePath)
        {
            System.Drawing.Image image = System.Drawing.Image.FromFile(filePath);

            double rate = 1;
            if (image.Width > FixWidth || image.Height > FixHeight)
            {
                if (image.Width > FixWidth) rate = FixWidth / image.Width;
                else if (image.Height * rate > FixHeight) rate = FixHeight / image.Height;

                int w = Convert.ToInt32(image.Width * rate);
                int h = Convert.ToInt32(image.Height * rate);
                Bitmap imageOutput = new Bitmap(image, w, h);

                imageOutput.Save(NewImagePath, image.RawFormat);
                imageOutput.Dispose();
            }
            else

            {
                int w = Convert.ToInt32(image.Width * rate);
                int h = Convert.ToInt32(image.Height * rate);
                Bitmap imageOutput = new Bitmap(image, w, h);

                imageOutput.Save(NewImagePath, image.RawFormat);
                imageOutput.Dispose();
            }

            image.Dispose();
        }

        public void SaveThumbnailImage(HttpPostedFileBase file, double FixWidth, double FixHeight, string NewImagePath)
        {
            System.Drawing.Image image = System.Drawing.Image.FromStream(file.InputStream);

            double rate = 1;
            if (image.Width > FixWidth || image.Height > FixHeight)
            {
                if (image.Width > FixWidth) rate = FixWidth / image.Width;
                else if (image.Height * rate > FixHeight) rate = FixHeight / image.Height;

                int w = Convert.ToInt32(image.Width * rate);
                int h = Convert.ToInt32(image.Height * rate);
                Bitmap imageOutput = new Bitmap(image, w, h);
                imageOutput.Save(NewImagePath, image.RawFormat);
                imageOutput.Dispose();
            }
            else
            {
                //直接儲存
                file.SaveAs(NewImagePath);
            }

            image.Dispose();
        }
        public string CopyArtGalleryPath(string SCHOOL_NO, string ART_GALLERY_NO, string FileName, string CopyFileName) {
            string TempPath = GetSetArtimgGalleryPath(SCHOOL_NO, ART_GALLERY_NO) + @"\" + FileName;
            string sourceDir = System.Web.Configuration.WebConfigurationManager.AppSettings["CopyFilePath"]+@"\"+SCHOOL_NO+@"\"+ART_GALLERY_NO+@"\";
            if (System.IO.File.Exists(HttpContext.Current.Server.MapPath(TempPath)))
            {
                try
                {
                    File.Copy(Path.Combine(sourceDir, FileName), Path.Combine(sourceDir, CopyFileName));
                    return Uri.EscapeUriString(UrlCustomHelper.Url_Content(TempPath));
                }
                catch (IOException copyError)
                {
                    Console.WriteLine(copyError.Message);
                }
               
            }
            else {

                return Uri.EscapeUriString(UrlCustomHelper.Url_Content(TempPath));
            }

            return string.Empty;

        }
        public static string GetSetArtGalleryPath()
        {
            string ReturnImgUrl = string.Empty;

            string UploadImageRoot = System.Web.Configuration.WebConfigurationManager.AppSettings["UploadImageRoot"];
            if (UploadImageRoot == null) UploadImageRoot = @"~\Content\";

            string ArtGallery = "ArtGallery";
            ReturnImgUrl = $@"{UploadImageRoot}{ArtGallery}\";

            return ReturnImgUrl;
        }
        public static string GetSetimgFodoPath()
        {
            string ReturnImgUrl = string.Empty;

            string UploadImageRoot = "";
              UploadImageRoot = @"~\fodo\";

            string ArtGallery = "img";
            ReturnImgUrl = $@"{UploadImageRoot}\";

            return ReturnImgUrl;
        }
        public static string GetSetimgPath()
        {
            string ReturnImgUrl = string.Empty;

            string UploadImageRoot = System.Web.Configuration.WebConfigurationManager.AppSettings["UploadImageRoot"];
            if (UploadImageRoot == null) UploadImageRoot = @"~\Content\";

            string ArtGallery = "img";
            ReturnImgUrl = $@"{UploadImageRoot}{ArtGallery}\";

            return ReturnImgUrl;
        }

        public static string GetSetArtGalleryPath(string SCHOOL_NO, string ART_GALLERY_NO)
        {
            string ReturnImgUrl = string.Empty;

            ReturnImgUrl = GetSetArtGalleryPath() + $@"{SCHOOL_NO}\{ART_GALLERY_NO}";

            return ReturnImgUrl;
        }

        public static string GetSetArtimgGalleryPath(string SCHOOL_NO, string ART_GALLERY_NO)
        {
            string ReturnImgUrl = string.Empty;

            ReturnImgUrl = GetSetimgPath() + $@"{SCHOOL_NO}\{ART_GALLERY_NO}";

            return ReturnImgUrl;
        }

        public string GetSetArtimgpGalleryPath(string SCHOOL_NO, string ART_GALLERY_NO, string FileName)
        {
            string ReturnImgUrl = string.Empty;

            ReturnImgUrl = GetSetimgPath();
            string TempPath = ReturnImgUrl + @"\" + FileName;

            if (System.IO.File.Exists(HttpContext.Current.Server.MapPath(TempPath)))
            {
                return Uri.EscapeUriString(UrlCustomHelper.Url_Content(TempPath));
            }

            return string.Empty;
        }

        /// <summary>
        /// 虛擬 Path
        /// </summary>
        /// <param name="SCHOOL_NO"></param>
        /// <param name="ART_GALLERY_NO"></param>
        /// <param name="FileName"></param>
        /// <returns></returns>
        public string GetDirectorySysArtGalleryPath(string SCHOOL_NO, string ART_GALLERY_NO, string FileName)
        {
            string TempPath = GetSetArtGalleryPath(SCHOOL_NO, ART_GALLERY_NO) + @"\" + FileName;

            if (System.IO.File.Exists(HttpContext.Current.Server.MapPath(TempPath)))
            {
                return Uri.EscapeUriString(UrlCustomHelper.Url_Content(TempPath));
            }
         
            return string.Empty;
        }

        public string GetDirectorySysArtimgGalleryPath(string SCHOOL_NO, string ART_GALLERY_NO, string FileName)
        {
            string TempPath = GetSetArtGalleryPath(SCHOOL_NO, ART_GALLERY_NO) + @"\" + FileName;

            if (System.IO.File.Exists(HttpContext.Current.Server.MapPath(TempPath)))
            {
                return Uri.EscapeUriString(UrlCustomHelper.Url_Content(TempPath));
            }

            return string.Empty;
        }

        public string GetDirectorySysArtGalleryimgPath(string SCHOOL_NO, string ART_GALLERY_NO, string FileName)
        {
            string TempPath = GetSetArtimgGalleryPath(SCHOOL_NO, ART_GALLERY_NO) + @"\" + FileName;

            if (System.IO.File.Exists(HttpContext.Current.Server.MapPath(TempPath)))
            {
                return Uri.EscapeUriString(UrlCustomHelper.Url_Content(TempPath));
            }

            return string.Empty;
        }

        public string GetSysArtGalleryPath(string SCHOOL_NO, string ART_GALLERY_NO)
        {
            return HttpContext.Current.Server.MapPath(GetSetArtGalleryPath()) + @"\" + SCHOOL_NO + @"\" + ART_GALLERY_NO + @"\";
        }

        public string GetSysArtGalleryimgPath(string SCHOOL_NO, string ART_GALLERY_NO)
        {
            return HttpContext.Current.Server.MapPath(GetSetimgPath()) + @"\" + SCHOOL_NO + @"\" + ART_GALLERY_NO + @"\";
        }

        public string GetSysfodoimgPath()
        {
            return HttpContext.Current.Server.MapPath(GetSetimgFodoPath()) + @"\" ;
        }
    }
}