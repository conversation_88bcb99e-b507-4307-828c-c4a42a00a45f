﻿@using System.Collections;
@using com.ecool.service;
@using EcoolWeb.Models;

@{
    string MODE = Request["MODE"];
    string Title = string.Empty;
    string AWARD_STATUS = string.Empty;
    string AWARD_TYPE =string.Empty;
    string radHOT_YN = "N";
    string sAWARD_STATUS =  string.Empty;
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    string SchoolNo = UserProfileHelper.GetSchoolNo();
    string ReadLevel=string.Empty;
    string PassportLevel = string.Empty;
    string radPUSH_YN = "N";
    string REF_KEY = Request["REF_KEY"];

    string SHOW_DESCRIPTION_YN = string.Empty;

    if (Model!=null && Model.Count>0)
    {

        if (string.IsNullOrWhiteSpace(Request["REF_KEY"]))
        {
            REF_KEY = Convert.ToString(Model[0]["AWARD_NO"]);
        }




        if (string.IsNullOrWhiteSpace(Model[0]["SCHOOL_NO"])==false)
        {
            SchoolNo = Model[0]["SCHOOL_NO"];
        }
        if (string.IsNullOrWhiteSpace(Model[0]["HOT_YN"]) == false)
        {
            radHOT_YN = Model[0]["HOT_YN"];
        }

        if (Convert.IsDBNull(Model[0]["PUSH_YN"]) ==false )
        {
            radPUSH_YN = Model[0]["PUSH_YN"];
        }

        AWARD_TYPE =Model[0]["AWARD_TYPE"];
        AWARD_STATUS = Model[0]["AWARD_STATUS"];

        if (Convert.IsDBNull(Model[0]["READ_LEVEL"]) == false && Model[0]["READ_LEVEL"] != null) { ReadLevel =Model[0]["READ_LEVEL"].ToString(); }
        if (Convert.IsDBNull(Model[0]["PASSPORT_LEVEL"]) == false && Model[0]["PASSPORT_LEVEL"] != null) { PassportLevel = Model[0]["PASSPORT_LEVEL"].ToString(); }

        sAWARD_STATUS = Model[0]["AWARD_STATUS"];

        if (Convert.IsDBNull(Model[0]["SHOW_DESCRIPTION_YN"]) == false)
        {
            SHOW_DESCRIPTION_YN = (Model[0]["SHOW_DESCRIPTION_YN"] == "Y") ? "checked" : "";
        }


    }

    switch (MODE)
    {
        case "ADD":
            Title = "新增獎品";
            AWARD_STATUS = "0";

            break;
    }

    string AWARD_TYPE_A = string.Empty, AWARD_TYPE_T = string.Empty, AWARD_TYPE_S = string.Empty;
    string AWARD_TYPE_P = string.Empty, AWARD_TYPE_C = string.Empty;
    switch (AWARD_TYPE)
    {
        case "A":
            AWARD_TYPE_A = "selected";
            break;
        case "T":
            AWARD_TYPE_T = "selected";
            break;
        case "S":
            AWARD_TYPE_S = "selected";
            break;
        case "P":
            AWARD_TYPE_P = "selected";
            break;
        case "C":
            AWARD_TYPE_C = "selected";
            break;
        default:
            AWARD_TYPE_S = "selected";
            break;
    }

    string radHOT_Y = (radHOT_YN != string.Empty && radHOT_YN == "Y") ? "checked" : "";
    string radHOT_N = (radHOT_YN != string.Empty && radHOT_YN == "N") ? "checked" : "";
    string PUSH_YN = (radPUSH_YN != string.Empty && radPUSH_YN == "Y") ? "checked" : "";
    string sCOST_CASHDis = (sAWARD_STATUS != string.Empty && sAWARD_STATUS != "0") ? "readonly" : "";

    ViewBag.SchoolNoSelectItem = BDMT01Service.GetSCHOOL_NO_ALL(SchoolNo, null, user);
    ViewBag.ReadLevelSelectItem = UserProfileHelper.GetSelectListItemImgReadLEVEL(ReadLevel);
    ViewBag.PassportLevelSelectItem = UserProfileHelper.GetSelectListItemImgPassportLEVEL(PassportLevel);



}


    <div class="form-horizontal">
        <div class="form-group">
            @Html.Label("學校", htmlAttributes: new { @class = "control-label label_dt col-md-3 col-sm-3 col-lg-2" })
            <div class="col-md-9 col-sm-9 col-lg-10">
                @Html.DropDownList("SCHOOL_NO", (List<SelectListItem>)ViewBag.SchoolNoSelectItem, new { @class = "form-control" })
                <br />
                <label class="text-info"><img src='~/Content/img/web-revise-prize-03s.png' style="max-height:35px;margin-right:5px">PS.選擇【全部學校】為{總召獎品}</label>
            </div>
        </div>
        <div class="form-group">
            @Html.Label("獎品狀態", htmlAttributes: new { @class = "control-label label_dt col-md-3 col-sm-3 col-lg-2" })
            <div class="col-md-9 col-sm-9 col-lg-10">
                <select id="AWARD_TYPE" name="AWARD_TYPE" class="form-control">
                    <option value="A" @AWARD_TYPE_A>活動</option>
                    <option value="T" @AWARD_TYPE_T>票券</option>
                    <option value="S" @AWARD_TYPE_S>實體</option>
                    <option value="P" @AWARD_TYPE_P>一般募資</option>
                    <option value="C" @AWARD_TYPE_C>公益募資</option>
                </select>
            </div>
        </div>
        <div class="form-group">
            @Html.Label("獎品名稱", htmlAttributes: new { @class = "control-label label_dt col-md-3 col-sm-3 col-lg-2" })
            <div class="col-md-9 col-sm-9 col-lg-10">
                <input type="hidden" id="ParamAWARD_STATUS" name="ParamAWARD_STATUS" value="@AWARD_STATUS"  />
                <input type="text" id="ParamAWARD_NAME" name="ParamAWARD_NAME" class="form-control" maxlength="50" value="@(null != Model && Model.Count != 0 ? Model[0]["AWARD_NAME"] : "")" />
                <br/>
                <label  class = "text-info">PS.獎品名稱.最多50個中英文字元</label>
                
            </div>
        </div>
        <div class="form-group">
            @Html.Label("兌換點數", htmlAttributes: new { @class = "control-label label_dt col-md-3 col-sm-3 col-lg-2" })
            <div class="col-md-9 col-sm-9 col-lg-10">
                <input type="text" id="ParamCOST_CASH" name="ParamCOST_CASH" class="form-control" @sCOST_CASHDis maxlength="6" value="@(null != Model && Model.Count != 0 ? Model[0]["COST_CASH"] : "")" />
                <input type="hidden" id="hidParamCOST_CASH" name="hidParamCOST_CASH" class="form-control" maxlength="6" value="@(null != Model && Model.Count != 0 ? Model[0]["COST_CASH"] : "")" />
            </div>
        </div>
        <div class="form-group">
            @Html.Label("獎品數量", htmlAttributes: new { @class = "control-label label_dt col-md-3 col-sm-3 col-lg-2" })
            <div class="col-md-9 col-sm-9 col-lg-10">
                <input type="text" id="ParamCOUNT_TOTAL" name="ParamQTY_STORAGE" class="form-control" maxlength="6" value="@(null != Model && Model.Count != 0 ? Model[0]["QTY_STORAGE"] : "")" />
            </div>
        </div>
        <div class="form-group">
            @Html.Label("限制數量", htmlAttributes: new { @class = "control-label label_dt col-md-3 col-sm-3 col-lg-2" })
            <div class="col-md-9 col-sm-9 col-lg-10">
                <input type="text" id="QTY_LIMIT" name="QTY_LIMIT" class="form-control" maxlength="6" value="@(null != Model && Model.Count != 0 ? Model[0]["QTY_LIMIT"] : "")" />
                <br />
                <label class="text-info">PS.限制 每一個可兌換的數量，不填，不限制</label>
            </div>
        </div>
        <div class="form-group">
            @Html.Label("閱讀認證", htmlAttributes: new { @class = "control-label label_dt col-md-3 col-sm-3 col-lg-2" })
            <div class="col-md-9 col-sm-9 col-lg-10">
                @Html.DropDownList("READ_LEVEL", (List<SelectListItem>)ViewBag.ReadLevelSelectItem, new { @class = "form-control" })
                <br/>
                <label class="text-info"><img src="~/Content/img/web-revise-secretary-22.png" style="max-height:35px;margin-right:5px">PS.限制 閱讀認證 滿 1-10級，才可兌換該獎品</label>
            </div>
        </div>
        <div class="form-group">
            @Html.Label("閱讀護照", htmlAttributes: new { @class = "control-label label_dt col-md-3 col-sm-3 col-lg-2" })
            <div class="col-md-9 col-sm-9 col-lg-10">
                @Html.DropDownList("PASSPORT_LEVEL", (List<SelectListItem>)ViewBag.PassportLevelSelectItem, new { @class = "form-control" })
                <br />
                <label class="text-info"><img src="~/Content/img/web-revise-secretary-38.png" style="max-height:35px;margin-right:5px">PS.限制 閱讀護照 滿 1-6級，才可兌換該獎品</label>
            </div>
        </div>
        <div class="form-group">
            @Html.Label("開始兌換", htmlAttributes: new { @class = "control-label label_dt col-md-3 col-sm-3 col-lg-2" })
            <div class="col-md-9 col-sm-9 col-lg-10">
                <input type="text" id="ParamSDATETIME" name="ParamSDATETIME" class="form-control" readonly="readonly" value="@(null != Model && Model.Count != 0 ? Model[0]["SDATETIME"] : "")" onchange="ValidDate();" />
                <select id="selSH" name="selSH"></select>時
                <select id="selSM" name="selSM"></select>分
            </div>
        </div>
        <div class="form-group">
            @Html.Label("兌換期限", htmlAttributes: new { @class = "control-label label_dt col-md-3 col-sm-3 col-lg-2" })
            <div class="col-md-9 col-sm-9 col-lg-10">
                <input type="text" id="ParamEDATETIME" name="ParamEDATETIME" class="form-control" readonly="readonly" value="@(null != Model && Model.Count != 0 ? Model[0]["EDATETIME"] : "")" onchange="ValidDate();" />
                <select id="selEH" name="selEH"></select>時
                <select id="selEM" name="selEM"></select>分
            </div>
        </div>
        <div class="form-group">
            @Html.Label("備註說明", htmlAttributes: new { @class = "control-label label_dt col-md-3 col-sm-3 col-lg-2" })
            <div class="col-md-9 col-sm-9 col-lg-10">
                <input type="text" id="ParamDESCRIPTION" name="ParamDESCRIPTION" class="form-control" value="@(null != Model && Model.Count != 0 ? Model[0]["DESCRIPTION"] : "")" />
                <br/>
                <label class="text-info">【備註說明】是否顯示於獎品清單中</label>
                <input type="checkbox" id="SHOW_DESCRIPTION_YN" name="SHOW_DESCRIPTION_YN" value="Y" @SHOW_DESCRIPTION_YN /> <label class="text-info">是</label>
            </div>
        </div>

        <div class="form-group">
            @Html.Label("熱門獎項", htmlAttributes: new { @class = "control-label label_dt col-md-3 col-sm-3 col-lg-2" })
            <div class="col-md-9 col-sm-9 col-lg-10">
                <input type="radio" id="radHOT_Y" name="radHOT_YN" value="Y" @radHOT_Y /><label class="control-label">是</label>
                <input type="radio" id="radHOT_N" name="radHOT_YN" value="N" @radHOT_N /><label class="control-label">否</label>
                <br />
                <label class="text-info"><img src='~/Content/img/icons-07.png' style="max-height:35px;margin-right:5px">PS.此欄位會影響排序，順序【總召獎品、熱門獎項、兌換點數】</label>
            </div>
        </div>
        <div class="form-group">
            @Html.Label("是否通知", htmlAttributes: new { @class = "control-label label_dt col-md-3 col-sm-3 col-lg-2" })
            <div class="col-md-9 col-sm-9 col-lg-10">
                <input type="checkbox" id="radPUSH_YN" name="radPUSH_YN" value="Y" @PUSH_YN /><label class="control-label">是</label>
            </div>
            <button type="button" class="btn btn-default" title="選取通知人員" id="BTN_PUSH" href="@Url.Action("Index", "APPT03", new { BTN_ID = "#BTN_PUSH",REF_TABLE = "AWAT02" ,REF_KEY= REF_KEY})">選取通知人員</button>
        </div>
        <div class="form-group">
            @Html.Label("上傳圖片", htmlAttributes: new { @class = "control-label label_dt col-md-3 col-sm-3 col-lg-2" })
            <div class="col-md-9 col-sm-9 col-lg-10">
                @Html.Action("Upload", "Comm")
                <br />
                <label class="text-info">PS.上傳圖片，請上傳約 1:1 圖片，瘦高圖會變形</label>
            </div>
        </div>
        @if (null != Model && Model.Count != 0)
        {
            if (Model[0]["IMG_FILE"] != null)
            {
                string IMG_FILE= Model[0]["IMG_FILE"];
                string aImgUrl = ViewBag.ImgUrl + Model[0]["SCHOOL_NO"] + @"/"+ IMG_FILE;
                <div class="form-group">
                    @Html.Label("原圖片", htmlAttributes: new { @class = "control-label label_dt col-md-3 col-sm-3 col-lg-2" })
                    <div class="col-md-9 col-sm-9 col-lg-10">
                        @Html.Hidden("IMG_FILE", IMG_FILE)
                        <img src="@aImgUrl" class="img-responsive" style="max-height:300px;max-width:300px" />
                    </div>
                </div>
            }
        }
    </div>
<input type="hidden" id="ParamAWARD_NO" name="ParamAWARD_NO" value="@(null != Model && Model.Count != 0 ? Model[0]["AWARD_NO"] : "")" />
<input type="hidden" id="REF_KEY" name="REF_KEY" value="@REF_KEY" />
@{
    if (null != Model)
    {

        <script type="text/javascript">

            $(document).ready(function () {
                $("#BTN_PUSH").colorbox({
                    iframe: true, width: "80%", height: "80%", opacity: 0.82 
                    });
            });


            $(function () {
                var TData = [];
                for (var hi = 0; hi <= 23; hi++) {
                    if (hi == 0)
                        TData.push("<option value='" + hi + "' selected>" + hi + "</option>");
                    else
                        TData.push("<option value='" + hi + "' >" + hi + "</option>");
                }
                $('#selSH').append(TData);
                $('#selEH').append(TData);

                $('#selSH').val("@(null != Model && Model.Count != 0 ? Model[0]["SH"]??"0" : "0")");
                $('#selEH').val("@(null != Model && Model.Count != 0 ? Model[0]["EH"] ?? "0" : "0")");

                TData = [];
                for (var mi = 0; mi <= 59; mi++) {
                    TData.push("<option value='" + mi + "' >" + (mi.toString().length > 1 ? mi : "0" + mi) + "</option>");
                }
                $('#selSM').append(TData);
                $('#selEM').append(TData);

                $('#selSM').val("@(null != Model && Model.Count != 0 ? Model[0]["SM"] ?? "0" : "0")");
                $('#selEM').val("@(null != Model && Model.Count != 0 ? Model[0]["EM"] ?? "0" : "0")");

                $("#ParamSDATETIME").datepicker({
                    dateFormat: "yy/mm/dd",
                    changeMonth: true,
                    changeYear: true,
                    showOn: "button",
                    buttonImage: "../Content/img/icon/calendar.gif",
                    buttonImageOnly: true,

                });

                $('#ParamSTIME').val()
                $("#ParamEDATETIME").datepicker({
                    dateFormat: "yy/mm/dd",
                    changeMonth: true,
                    changeYear: true,
                    showOn: "button",
                    buttonImage: "../Content/img/icon/calendar.gif",
                    buttonImageOnly: true
                });
            });
        </script>
    }
}