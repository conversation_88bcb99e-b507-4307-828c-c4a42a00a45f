﻿@model RollCallBarcodeIndexViewModel
@using ECOOL_APP;
@{
    var Permission = ViewBag.Permission as List<ControllerPermissionfile>;
}

@Html.AntiForgeryToken()
@Html.HiddenFor(m => m.Keyword)
@Html.HiddenFor(m => m.OrderByColumnName)
@Html.HiddenFor(m => m.SortType)
@Html.HiddenFor(m => m.Page)
<link href="~/Content/colorbox/example4/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>

<div class="panel panel-ZZZ" name="TOP">
    <div class="panel-heading text-center">
        @Html.BarTitle(null, "發放紙本點數")
    </div>
    <div class="table-responsive">
        <table class="table-ecool table-hover table-ecool-ZZZ">
            <thead class="bg-primary-dark text-white">
                <tr class="thead-primary">
                    <th class="text-nowrap">@Html.DisplaySortColumnFor(m => m.ListData.First().ROLL_CALL_NAME, Model?.OrderByColumnName, Model?.SortType, true)</th>
                    <th class="text-nowrap">@Html.DisplaySortColumnFor(m => m.ListData.First().ROLL_CALL_DATES, Model?.OrderByColumnName, Model?.SortType, true)</th>
                    <th class="text-nowrap">@Html.DisplaySortColumnFor(m => m.ListData.First().ROLL_CALL_DATEE, Model?.OrderByColumnName, Model?.SortType, true)</th>
                    <th class="text-nowrap"><a id="button_ROLL_CALL_DATEE" role="button" style="cursor: pointer;">領取</a></th>
                    <th class="text-nowrap"><a id="button_ROLL_CALL_DATEE" role="button" style="cursor: pointer;">發放(點/人)</a></th>
                    <th width="100" class="text-nowrap">@Html.DisplaySortColumnFor(m => m.ListData.First().CRE_PERSON_NAME, Model?.OrderByColumnName, Model?.SortType, true)</th>
                    <th width="300"></th>
                </tr>
            </thead>
            <tbody>
                @if (Model.ListData?.Count() > 0)
                {
                    foreach (var item in Model.ListData)
                    {

                        <tr class="text-center">
                            <td>@Html.DisplayFor(modelItem => item.ROLL_CALL_NAME)</td>
                            <td>@string.Format("{0:yyyy/MM/dd}", item.ROLL_CALL_DATES)</td>
                            <td>@string.Format("{0:yyyy/MM/dd}", item.ROLL_CALL_DATEE)</td>
                            <td>@Html.DisplayFor(modelItem => item.SumCash)/@Html.DisplayFor(modelItem => item.personCount)</td>
                            <td>@Html.DisplayFor(modelItem => item.NOSumCash)/@Html.DisplayFor(modelItem => item.NOpersonCount)</td>
                            <td>@Html.DisplayFor(modelItem => item.CRE_PERSON_NAME)</td>
                            <td class="d-flex flex-nowrap flex-md-wrap">
                                <a href="@Url.Action("Edit1",new {Keyword = item.ROLL_CALL_ID })" role="button" class="col-6 col-lg-3 btn btn-xs btn-Basic px-0" title="編輯">
                                    <span class="fa fa-pencil" aria-hidden="true"></span><br/>編輯
                                </a>
                                <a href="@Url.Action("BarcodeRollCallIndex1",new {Keyword = item.ROLL_CALL_ID })" target="_blank" role="button" class="col-6 col-lg-3 btn btn-xs btn-Basic px-0" title="兌換情形">
                                    <span class="fa fa-eye" aria-hidden="true"></span><br />兌換<br />情形
                                </a>
                                <a href="@Url.Action("PrintModify",new {Keyword = item.ROLL_CALL_ID })" target="_blank" role="button" class="col-6 col-lg-3 btn btn-xs btn-Basic px-0" title="列印完整版">
                                    <span class="fa fa-print" aria-hidden="true"></span><br />列印<br />自選版
                                </a>                             
                                <a href="@Url.Action("PrintModify1",new {Keyword = item.ROLL_CALL_ID })" target="_blank" role="button" class="col-6 col-lg-3 btn btn-xs btn-Basic px-0" title="列印紙本個人點數">
                                    <span class="fa fa-print" aria-hidden="true"></span><br />列印<br />常用版
                                </a>
                            </td>
                        </tr>
                    }
                }
            </tbody>
        </table>
        @if ((Model.ListData?.Count() ?? 0) == 0)
        {
            <div class="text-center p-1"><strong>目前無資料</strong></div>
        }
    </div>
</div>

@Html.Pager(Model.ListData.PageSize, Model.ListData.PageNumber, Model.ListData.TotalItemCount).Options(o => o.DisplayTemplate(PageGlobal.DfDisplayTemplate)
.MaxNrOfPages(PageGlobal.DfMaxNrOfPages)
.SetPreviousPageText(PageGlobal.DfSetPreviousPageText)
.SetNextPageText(PageGlobal.DfSetNextPageText)
)
<script>
    $(document).ready(function () {
        $(".progress").colorbox({ opacity: 0.82, width: "70%", innerHeight: "500px", scrolling: true });
    });
</script>