/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Gyre-Pagella/Size4/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.GyrePagellaMathJax_Size4={directory:"Size4/Regular",family:"GyrePagellaMathJax_Size4",testString:"\u00A0\u0302\u0303\u0306\u030C\u0311\u032C\u032D\u032E\u032F\u0330\u2016\u2044\u20E9\u221A",32:[0,0,250,0,0],40:[1093,593,573,135,483],41:[1093,593,573,90,438],47:[1428,928,1032,80,952],91:[1099,599,479,135,389],92:[1428,928,1032,80,952],93:[1099,599,479,90,344],123:[1098,598,521,90,431],124:[1079,579,218,80,138],125:[1098,598,521,90,431],160:[0,0,250,0,0],770:[713,-542,1045,0,1045],771:[702,-542,1042,0,1042],774:[709,-550,1070,0,1070],780:[712,-541,1045,0,1045],785:[722,-563,1070,0,1070],812:[-60,231,1045,0,1045],813:[-70,241,1045,0,1045],814:[-60,219,1070,0,1070],815:[-78,237,1070,0,1070],816:[-78,238,1042,0,1042],8214:[1079,579,396,80,316],8260:[1428,928,1032,80,952],8425:[779,-651,1855,0,1855],8730:[1380,850,743,120,773],8739:[1079,579,218,80,138],8741:[1079,579,396,80,316],8968:[1099,579,479,135,389],8969:[1099,579,479,90,344],8970:[1079,599,479,135,389],8971:[1079,599,479,90,344],9001:[1433,933,528,90,438],9002:[1433,933,528,90,438],9140:[779,-651,1855,0,1855],9141:[-181,309,1855,0,1855],9180:[788,-570,2528,0,2528],9181:[-100,318,2528,0,2528],9182:[805,-588,2538,0,2538],9183:[-118,335,2538,0,2538],9184:[736,-526,2572,0,2572],9185:[-56,266,2572,0,2572],10214:[1099,599,487,135,397],10215:[1099,599,487,90,352],10216:[1433,933,528,90,438],10217:[1433,933,528,90,438],10218:[1433,933,812,90,722],10219:[1433,933,812,90,722],10222:[1093,593,399,135,309],10223:[1093,593,399,90,264]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"GyrePagellaMathJax_Size4"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Size4/Regular/Main.js"]);
