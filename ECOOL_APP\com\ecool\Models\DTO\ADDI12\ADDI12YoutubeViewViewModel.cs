﻿
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Text;

namespace ECOOL_APP.EF
{
    public class ADDI12YoutubeViewViewModel
    {
        /// <summary>
        ///小小舞臺ID
        /// </summary>
        [DisplayName("小小舞臺ID")]
        public string STAGE_ID { get; set; }

        /// <summary>
        ///學校no
        /// </summary>
        [DisplayName("學校no")]
        public string SCHOOL_NO { get; set; }

        /// <summary>
        ///標題目稱
        /// </summary>
        [DisplayName("標題目稱")]
        public string STAGE_NAME { get; set; }

        /// <summary>
        ///排序
        /// </summary>
        [DisplayName("排序")]
        public int? ORDER_BY { get; set; }

        /// <summary>
        ///建立改人
        /// </summary>
        [DisplayName("建立改人")]
        public string CRE_PERSON { get; set; }

        /// <summary>
        ///建立日期
        /// </summary>
        [DisplayName("建立日期")]
        public DateTime? CRE_DATE { get; set; }

        /// <summary>
        ///修改人
        /// </summary>
        [DisplayName("修改人")]
        public string CHG_PERSON { get; set; }

        /// <summary>
        ///修改日期
        /// </summary>
        [DisplayName("修改日期")]
        public DateTime? CHG_DATE { get; set; }

        /// <summary>
        ///封面
        /// </summary>
        [DisplayName("封面")]
        public string YOUTUBE_IMG { get; set; }

        /// <summary>
        ///網址
        /// </summary>
        [DisplayName("網址")]
        public string YOUTUBE_URL { get; set; }

        /// <summary>
        ///是否首播
        /// </summary>
        [DisplayName("是否首播")]
        public bool? IS_PREMIER { get; set; }

        /// <summary>
        ///開始日期
        /// </summary>
        [DisplayName("開始日期")]
        public DateTime? STAGE_DATES { get; set; }

        /// <summary>
        ///結束日期
        /// </summary>
        [DisplayName("結束日期")]
        public DateTime? STAGE_DATEE { get; set; }

        /// <summary>
        ///瀏覽人數
        /// </summary>
        [DisplayName("瀏覽人數")]
        public float READ_COUNT { get; set; }

        /// <summary>
        ///狀態
        /// </summary>
        [DisplayName("狀態")]
        public byte? STATUS { get; set; }


        /// <summary>
        ///首播按讚數
        /// </summary>
        [DisplayName("首播按讚數")]
        public int PremierLikeCount { get; set; }

        /// <summary>
        ///一般按讚數
        /// </summary>
        [DisplayName("一般按讚數")]
        public int LikeCount { get; set; }



        /// <summary>
        ///使用者是否推薦過
        /// </summary>
        [DisplayName("是否按讚數過")]
        public int? IsLikeCount { get; set; }

        /// <summary>
        /// 是否能按讚
        /// </summary>
        public bool BtnLike { get; set; }

        /// <summary>
        /// 打賞感應的卡號
        /// </summary>
        public string CARD_ID { get; set; }
    }
}