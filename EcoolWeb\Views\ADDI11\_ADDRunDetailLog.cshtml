﻿@model ADDI11EditViewModel
    @{ 

        string SCHOOL_NO = EcoolWeb.Models.UserProfileHelper.GetSchoolNo();
    }
<style>

    .table-65Per {
        width: 65%;
    }


    table.table-ecool.table-65Per.table-hover.table-ecool-reader {
        margin-top: 200px;
    }
</style>
<div class="table-responsive">
    <div class="text-center" id="tbData">
        <table class="table-ecool table-100Per table-hover table-ecool-reader">
            <thead>
                <tr>
                    @*<th></th>*@
                    <th style="font-size:20px">

                        編號

                    </th>
                    <th style="font-size:20px">
                        班級

                    </th>
                    <th style="font-size:20px">
                        座號
                    </th>
                    <th style="font-size:20px">
                        姓名
                    </th>
                    <th style="font-size:20px">
                        圈數
                    </th>
                    <th style="font-size:20px">
                        當日總圈數
                    </th>
                    <th style="font-size:20px">
                        跑步時間
                    </th>
                </tr>
            </thead>
            <tbody>
                @{int Row = 1; }
                @foreach (var item in Model.Peoples)
                {


                    <tr>
                        @*<td>


                                <a role='button' style="cursor:pointer;" onclick="deleteRow('@item.USER_NO','@item.RunDtae')"> <i class='glyphicon glyphicon-remove'></i></a>
                            </td>*@
                        <td style="text-align: center;font-size:20px">@Row</td>
                        <td style="text-align: center;font-size:20px">@Html.DisplayFor(modelItem => item.CLASS_NO)</td>
                        <td style="text-align: center;font-size:20px">@Html.DisplayFor(modelItem => item.SEAT_NO)</td>
                        <td style="text-align: center;font-size:20px">@Html.DisplayFor(modelItem => item.NAME)</td>
                        <td style="text-align: center;font-size:20px">@Html.DisplayFor(modelItem => item.LAP)</td>
                        <td style="text-align: center;font-size:20px">@Html.DisplayFor(modelItem => item.SUMLap)</td>
                        <td style="text-align: center;font-size:20px">@Html.DisplayFor(modelItem => item.CHG_DATE)</td>
                    </tr>
                    Row++;
                }
            </tbody>
        </table>
    </div>
</div>

@if (Model.Peoples.Count() == 0)
{
    <div class="text-center">
        <h3>暫時無資料</h3>
    </div>
}
<div>
    @Html.Pager(Model.Peoples.PageSize, Model.Peoples.PageNumber, Model.Peoples.TotalItemCount).Options(o => o
                            .DisplayTemplate(PageGlobal.DfDisplayTemplate)
                            .MaxNrOfPages(PageGlobal.DfMaxNrOfPages)
                            .SetPreviousPageText(PageGlobal.DfSetPreviousPageText)
                            .SetNextPageText(PageGlobal.DfSetNextPageText)
                            )
</div>
<script type="text/javascript">

function FunPageProc(page) {


        var SCHOOL_NO1 = "";
        var CARD_NO = $('#CARD_NO').val();
        var LAP = $("#LAP").val();
    var ONE_LAP_M = $("#ONE_LAP_M").val();
        SCHOOL_NO1 = $("#SCHOOL_NO1").val('@SCHOOL_NO');
       // $('#CARD_NO').prop('readonly', true);
        var data = {
            "CARD_NO": CARD_NO,
            "SCHOOL_NO1": '@SCHOOL_NO',
            "LAP": LAP,
            "ONE_LAP_M": ONE_LAP_M,
            "page": page
        };
        console.log(data);

        if ($(targetFormID).size() > 0) {
            $('#Page').val(page)
               $.ajax({

               url: '@Url.Action("_ADDRunDetailLog", (string)ViewBag.BRE_NO)',
                data: data,
                   cache: false,
                   success: function (html) {
                       console.log(html);

                               $("#editorRows").html('');
                                 $("#editorRows").prepend(html);
                                            }


                                        });
        }
    }

</script>