@charset "UTF-8";
body {
  font-family: "微軟正黑體", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Helvetica Neue", <PERSON><PERSON>, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
  color: #000;
}

h1 {
  margin: 0;
  padding: 0;
}

.row {
  margin: 0;
}

@keyframes slideUpDown2 {
  0% {
    transform: translateY(0px);
  }
  15% {
    transform: translateY(0px);
  }
  30% {
    transform: translateY(-12%);
  }
  60% {
    transform: translateY(-12%);
  }
  75% {
    transform: translateY(-55%);
  }
  100% {
    transform: translateY(-55%);
  }
}
.leaderboard-page-bg-cold {
  background: linear-gradient(left, rgba(190, 247, 229, 0.2) 0%, rgba(190, 247, 229, 0.2) 50%, rgba(190, 247, 229, 0) 50%, rgba(190, 247, 229, 0) 100%), linear-gradient(#34C4DB 0%, #C7FFFB 45%, #D6FFF7 70%, #25C5D1 100%);
  overflow: hidden;
}
@media (max-width: 991.98px) {
  .leaderboard-page-bg-cold {
    background: linear-gradient(#34C4DB 0%, #C7FFFB 25%, #D6FFF7 35%, #25C5D1 50%, #C7FFFB 75%, #D6FFF7 85%, #25C5D1 100%);
  }
}
.leaderboard-page-bg-warm {
  background: linear-gradient(left, rgba(247, 190, 190, 0.2) 0%, rgba(247, 190, 190, 0.2) 50%, rgba(247, 190, 190, 0) 50%, rgba(247, 190, 190, 0) 100%), linear-gradient(#FFAB17 0%, #FFFBC7 45%, #FFF4D6 80%, #FFE7BE 100%);
  overflow: hidden;
}
@media (max-width: 991.98px) {
  .leaderboard-page-bg-warm {
    background: linear-gradient(#FFAB17 0%, #FFFBC7 25%, #FFF4D6 40%, #FFE7BE 48%, #FFAB17 55%, #FFFBC7 85%, #FFF4D6 100%);
  }
}
.leaderboard-page-bg-merge {
  background: linear-gradient(#FFAB17 5%, #FFFBC7 30%, #FFF4D6 40%, #D6FFF7 60%, #C7FFFB 80%, #25C5D1 100%);
  overflow: hidden;
}
@media (max-width: 991.98px) {
  .leaderboard-page-bg-merge {
    background-attachment: fixed;
    animation-name: slideUpDown2;
    animation-duration: 20s;
    animation-iteration-count: infinite;
    animation-direction: alternate;
  }
}

.leaderboard-sunlight-small {
  padding: 0;
  background-image: url(../images/bg-sunlight.png);
  background-repeat: no-repeat;
  background-position: center top;
  background-size: 101% 49%;
}

@keyframes slideUpDown {
  0% {
    transform: translateY(0px);
  }
  30% {
    transform: translateY(0px);
  }
  70% {
    transform: translateY(-46%);
  }
  100% {
    transform: translateY(-46%);
  }
}
.leaderboard-page-layout {
  min-height: 100vh;
  padding: 0 2.5rem;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
}
@media (max-width: 991.98px) {
  .leaderboard-page-layout {
    padding: 2rem;
    animation-name: slideUpDown;
    animation-duration: 10s;
    animation-iteration-count: infinite;
    animation-direction: alternate;
  }
}
.leaderboard-page-layout > div {
  flex: 1 1 100%;
}

.title {
  display: block;
  width: 100%;
  height: 20vh;
  background-repeat: no-repeat, no-repeat;
  position: absolute;
  z-index: 4;
}
@media (max-width: 991.98px) {
  .title {
    margin-top: 2rem;
    height: 13rem;
  }
}
.title-total {
  margin-left: -0.5rem;
  background-image: url(../images/Leaderboard-title-total.png), url(../images/shadow.png);
  background-position: top 0 left 0, top 1.7rem left 3rem;
  background-size: auto 89.4%, auto 86%;
}
.title-existing {
  margin-right: -0.5rem;
  background-image: url(../images/Leaderboard-title-existing.png), url(../images/shadow.png);
  background-position: top 0 right 0, top 1.7rem right 3rem;
  background-size: auto 89.4%, auto 86%;
}
.title-read {
  margin-left: -0.5rem;
  background-image: url(../images/Leaderboard-title-read.png), url(../images/shadow.png);
  background-position: top 0 left 0, top 1.7rem left 3rem;
  background-size: auto 89.4%, auto 86%;
}
.title-sports {
  margin-right: -0.5rem;
  background-image: url(../images/Leaderboard-title-sports.png), url(../images/shadow.png);
  background-position: top 0 right 0, top 1.7rem right 3rem;
  background-size: auto 89.4%, auto 86%;
}
.title-ecool {
  background-image: url(../images/Leaderboard-title-ecool.png), url(../images/shadow.png);
  background-position: center, center;
  background-size: auto 89.4%, auto 86%;
}

.leaderboard-item {
  flex: 1 1 40%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: 14vh;
  margin: 1.3rem;
  padding: 0.5rem 0 0 0;
  list-style-type: none;
  background: #fff;
  border: 0.725rem solid #eecc0c;
  border-radius: 4.5rem;
  text-align: center;
  box-shadow: 0 1rem 1rem rgba(0, 0, 0, 0.3);
  position: relative;
  z-index: 2;
}
@media (max-width: 991.98px) {
  .leaderboard-item {
    padding-top: 0;
    min-height: 11rem;
    border-width: 0.5rem;
    border-radius: 3rem;
  }
}
@media (max-width: 767.98px) {
  .leaderboard-item {
    min-height: 8rem;
  }
}
.leaderboard-item::before {
  content: "";
  display: block;
  height: 10vh;
  background-image: url(../images/Leaderboard-crown.png);
  background-repeat: no-repeat;
  background-position: center, center;
  background-size: auto 8.5vh;
  position: absolute;
  top: -8vh;
  left: 0;
  right: 0;
  z-index: 2;
}
.leaderboard-item:first-child {
  flex: 0 1 48%;
  margin: 0 21%;
  z-index: 1;
}
.leaderboard-item:first-child::before {
  background-size: auto 9.5vh;
  top: -9vh;
  z-index: 1;
}
.leaderboard-item:first-child::after {
  content: "";
  display: block;
  height: 5.1vh;
  background-image: url(../images/Leaderboard-coin-rare.png);
  background-repeat: no-repeat;
  background-position: center, center;
  background-size: auto 5.1vh;
  position: absolute;
  bottom: -3rem;
  left: 0;
  right: 0;
}
.leaderboard-item span {
  display: block;
  line-height: 1.12;
  font-weight: bold;
  position: relative;
  z-index: 3;
  text-shadow: 1px 1px #fff, -1px 1px #fff, 1px -1px #fff, 1px -1px #fff;
}
.leaderboard-item span.number {
  display: block;
  height: 4.8vh;
  background-repeat: no-repeat;
  background-position: center, center;
  position: absolute;
  top: -3.2vh;
  left: 0;
  right: 0;
  z-index: 2;
}
.leaderboard-item span.number-first {
  background-image: url(../images/Leaderboard-first.png);
  background-size: auto 5.1vh;
  top: -4.3vh;
}
.leaderboard-item span.number-second {
  background-image: url(../images/Leaderboard-second.png);
  background-size: auto 4.8vh;
}
.leaderboard-item span.number-third {
  background-image: url(../images/Leaderboard-third.png);
  background-size: auto 4.8vh;
}
.leaderboard-item span.class {
  color: #A24C00;
  font-size: 1.525rem;
}
.leaderboard-item span.name {
  font-size: 2.725rem;
}
@media (max-width: 767.98px) {
  .leaderboard-item span.name {
    font-size: 2.125rem;
  }
}
.leaderboard-item span.value {
  font-size: 2.125rem;
}
@media (max-width: 767.98px) {
  .leaderboard-item span.value {
    font-size: 1.725rem;
  }
}
.leaderboard-box {
  margin: -3.5rem -0.5rem 0 -0.5rem;
  padding: 23rem 1rem 1rem 1rem;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  margin-top: 0;
  padding-top: 17rem;
}
@media (max-width: 991.98px) {
  .leaderboard-box {
    padding-top: 18rem;
  }
}
.leaderboard-box-red .leaderboard-item {
  border-color: #C91F74;
}
.leaderboard-box-red::before {
  content: "";
  display: block;
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  background-image: url(../images/Leaderboard-coin-many.png);
  background-repeat: no-repeat;
  background-position: right 4.5vw top 20vh;
  background-size: auto 9.4vh;
  z-index: 0;
  pointer-events: none;
}
@media (max-width: 1199.98px) {
  .leaderboard-box-red::before {
    background-position: right 4.5vw top 22vh;
  }
}
@media (max-width: 991.98px) {
  .leaderboard-box-red::before {
    background-position: right 7.5vw top 18rem;
    background-size: 28% auto;
  }
}
.leaderboard-box-red::after {
  content: "";
  display: block;
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  background-image: url(../images/Leaderboard-starlight-three.png), url(../images/Leaderboard-starlight-two.png), url(../images/Leaderboard-img-trophy.png);
  background-repeat: no-repeat;
  background-position: right 10.5vw top 10vh, left 2.5vw top 27vh, left 6.5vw top 14vh;
  background-size: auto 7.1vh, auto 6.1vh, auto 17.8vh;
  z-index: 1;
  pointer-events: none;
}
@media (max-width: 1199.98px) {
  .leaderboard-box-red::after {
    background-position: right 10.5vw top 12vh, left 2.5vw top 24vh, left 6.5vw top 19vh;
  }
}
@media (max-width: 991.98px) {
  .leaderboard-box-red::after {
    background-position: right 17.5vw top 10rem, left 4.5rem top 18rem, left 15vw top 15rem;
    background-size: 12% auto, 13% auto, 21% auto;
  }
}
.leaderboard-box-purple .leaderboard-item {
  border-color: #7F3ACE;
}
.leaderboard-box-purple .number {
  filter: hue-rotate(285deg);
}
.leaderboard-box-purple::before {
  content: "";
  display: block;
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  background-image: url(../images/Leaderboard-img-money-jar.png), url(../images/Leaderboard-coin-many.png);
  background-repeat: no-repeat;
  background-position: left 4.5vw top 20vh, right 4.5vw top 22vh;
  background-size: auto 12vh, auto 9.4vh;
  z-index: 0;
  pointer-events: none;
}
@media (max-width: 767.98px) {
  .leaderboard-box-purple::before {
    background-position: left 6.5vw top 14rem, right 4.5vw top 16rem;
    background-size: 28% auto, 31% auto;
  }
}
.leaderboard-box-purple::after {
  content: "";
  display: block;
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  background-image: url(../images/Leaderboard-starlight-three2.png), url(../images/Leaderboard-starlight-three3.png);
  background-repeat: no-repeat;
  background-position: left 11.5vw top 10.2vh, right 2vw top 21.5vh;
  background-size: auto 7.6vh, auto 7.8vh;
  z-index: 1;
  pointer-events: none;
}
@media (max-width: 767.98px) {
  .leaderboard-box-purple::after {
    background-position: left 3.5vw top 8rem, right -2.5vw top 15rem;
    background-size: 17% auto, 17% auto;
  }
}
.leaderboard-box-green .leaderboard-item {
  border-color: #23972F;
}
.leaderboard-box-green .number {
  filter: hue-rotate(151deg);
}
.leaderboard-box-green::before {
  content: "";
  display: block;
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  background-image: url(../images/Leaderboard-coin-many.png);
  background-repeat: no-repeat;
  background-position: right 5.5vw top 22vh;
  background-size: auto 9.4vh;
  z-index: 0;
  pointer-events: none;
}
@media (max-width: 991.98px) {
  .leaderboard-box-green::before {
    background-position: right 14% top 19rem;
  }
}
.leaderboard-box-green::after {
  content: "";
  display: block;
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  background-image: url(../images/Leaderboard-starlight-three.png), url(../images/Leaderboard-starlight-two.png), url(../images/Leaderboard-img-book-girl.png), url(../images/Leaderboard-img-read-girl.png);
  background-repeat: no-repeat;
  background-position: right 10.5vw top 10vh, left 2.5vw top 27vh, left 4.5vw top 19vh, right 4vw top 10.5vh;
  background-size: auto 7.1vh, auto 6.1vh, auto 14.8vh, auto 18.5vh;
  z-index: 1;
  pointer-events: none;
}
@media (max-width: 1199.98px) {
  .leaderboard-box-green::after {
    background-position: right 19.5vw top 10vh, left 8.5vw top 22vh, left 5.5vw top 16rem, right 7% top 13rem;
    background-size: auto 8.1%, auto 5.1%, 17rem auto, 10rem auto;
  }
}
@media (max-width: 767.98px) {
  .leaderboard-box-green::after {
    background-position: right 19.5vw top 10vh, left 8.5vw top 22vh, left 0.5vw top 19rem, right 4% top 13rem;
    background-size: auto 8.1%, auto 5.1%, 13rem auto, 8rem auto;
  }
}
.leaderboard-box-blue .leaderboard-item {
  border-color: #3498DB;
}
.leaderboard-box-blue .number {
  filter: hue-rotate(215deg);
}
.leaderboard-box-blue::before {
  content: "";
  display: block;
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  background-image: url(../images/Leaderboard-coin-medium.png);
  background-repeat: no-repeat;
  background-position: left 4.5vw top 20vh;
  background-size: auto 8.3vh;
  z-index: 0;
  pointer-events: none;
}
@media (max-width: 991.98px) {
  .leaderboard-box-blue::before {
    background-position: left 1.5vw top 22rem;
    background-size: 30.3vw auto;
  }
}
.leaderboard-box-blue::after {
  content: "";
  display: block;
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  background-image: url(../images/Leaderboard-starlight-three2.png), url(../images/Leaderboard-starlight-three3.png), url(../images/Leaderboard-img-bankroll.png), url(../images/Leaderboard-img-run-girl.png);
  background-repeat: no-repeat;
  background-position: left 11.5vw top 10.2vh, right 2vw top 21.5vh, right 4vw top 23.8vh, left 7vw top 14.3vh;
  background-size: auto 7.6vh, auto 7.8vh, auto 9.5vh, auto 21vh;
  z-index: 1;
  pointer-events: none;
}
@media (max-width: 991.98px) {
  .leaderboard-box-blue::after {
    background-position: left 11.5vw top 10.2vh, right 2vw top 21.5vh, right 5vw top 18.8rem, left 11vw top 11.3rem;
    background-size: auto 7.6vh, auto 7.8vh, 7.5rem auto, 8rem auto;
  }
}

.leaderboard-list {
  margin: -0.5rem 0 0 0;
  padding: 0 1.5rem;
  font-size: 1.925rem;
}
@media (max-width: 991.98px) {
  .leaderboard-list {
    font-size: 1.5rem;
    padding: 0;
  }
}
@media (max-width: 767.98px) {
  .leaderboard-list {
    margin-left: 1.5rem;
  }
}
.leaderboard-list li {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 0.7rem;
  padding: 0.5rem;
  background: #FAF5E1;
  box-shadow: 0 0 0.5rem rgba(0, 0, 0, 0.2);
  border: 0.5rem solid #fff;
  position: relative;
  min-height: 6vh;
}
@media (max-width: 767.98px) {
  .leaderboard-list li {
    margin-top: 0.2rem;
    padding: 0;
    min-height: auto;
  }
}
.leaderboard-list li span {
  display: block;
  padding: 0 1.5rem;
  flex: 1 1 auto;
  text-align: left;
  font-weight: bold;
}
@media (max-width: 991.98px) {
  .leaderboard-list li span {
    padding: 0;
  }
}
.leaderboard-list li .number {
  flex: 0 0 auto;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: -0.3rem 0 -0.3rem -1.7rem;
  padding: 0 1rem;
  font-size: 2.1rem;
  color: #fff;
  border-radius: 0.5rem;
  background: #666;
  box-shadow: 0 0.3rem 0 0 #333;
}
@media (max-width: 991.98px) {
  .leaderboard-list li .class {
    padding-left: 1rem;
  }
}
.leaderboard-list li .value {
  padding-right: 0.5rem;
  text-align: right;
}
.leaderboard-list-red li .number {
  background: #C91F74;
  box-shadow: 0 0.3rem 0 0 #840000;
}
.leaderboard-list-purple li .number {
  background: #8B1BCB;
  box-shadow: 0 0.3rem 0 0 #350554;
}
.leaderboard-list-green li .number {
  background: #23972F;
  box-shadow: 0 0.3rem 0 0 #00630A;
}
.leaderboard-list-blue li .number {
  background: #3498DB;
  box-shadow: 0 0.3rem 0 0 #004081;
}

.title-ecool {
  background-position: center, center top 82%;
  background-size: auto 90%, auto 92%;
  top: 36%;
  height: 29vh;
}
@media (max-width: 991.98px) {
  .title-ecool {
    background-position: center, center top 82%;
    background-size: auto 90%, auto 92%;
    top: 0;
    height: 16rem;
  }
}

.leaderboard-page-layout-merge {
  padding: 0;
  background-image: url(../images/bg-sunlight.png);
  background-repeat: no-repeat;
  background-position: center top;
  background-size: 100% 54%;
  background-attachment: fixed;
}
@media (max-width: 991.98px) {
  .leaderboard-page-layout-merge {
    animation-name: none;
    padding-top: 21rem;
  }
}
.leaderboard-page-layout-merge::before {
  content: "";
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-image: url(../images/bg-sunlight.png);
  background-repeat: no-repeat;
  background-position: center top;
  background-size: 100% 43%;
  transform: rotate(180deg);
}
.leaderboard-page-layout-merge .title {
  width: 91%;
  height: 16vh;
  top: 0;
}
@media (max-width: 991.98px) {
  .leaderboard-page-layout-merge .title {
    height: 10rem;
    top: -2rem;
  }
}
.leaderboard-page-layout-merge .title-total {
  background-position: top 0 left 1rem, top 1.2rem left 3.5rem;
}
.leaderboard-page-layout-merge .title-existing {
  background-position: top 0 right 0, top 1.1rem right 2rem;
}
.leaderboard-page-layout-merge .title-read {
  background-position: top 0 left 7vw, top 1.2rem left 8.5vw;
  top: -4vh;
}
@media (max-width: 1199.98px) {
  .leaderboard-page-layout-merge .title-read {
    background-position: top 0 left 5vw, top 1.2rem left 7.5vw;
    top: -7vh;
  }
}
@media (max-width: 991.98px) {
  .leaderboard-page-layout-merge .title-read {
    top: -2rem;
  }
}
.leaderboard-page-layout-merge .title-sports {
  background-position: top 0 right 4vw, top 1.1rem right 5vw;
  top: -4vh;
}
@media (max-width: 1199.98px) {
  .leaderboard-page-layout-merge .title-sports {
    background-position: top 0 right 1vw, top 1.1rem right 3vw;
    top: -7vh;
  }
}
@media (max-width: 991.98px) {
  .leaderboard-page-layout-merge .title-sports {
    top: -2rem;
  }
}
.leaderboard-page-layout-merge .leaderboard-box {
  margin: 0;
  padding: 8rem 2rem 1rem 1rem;
}
@media (max-width: 991.98px) {
  .leaderboard-page-layout-merge .leaderboard-box {
    padding: 10rem 1rem 3rem 1rem;
  }
}
.leaderboard-page-layout-merge .leaderboard-box-red::before {
  background-position: right 9.5vw top 13rem;
}
@media (max-width: 991.98px) {
  .leaderboard-page-layout-merge .leaderboard-box-red::before {
    background-position: right 9.5vw top 10rem;
  }
}
.leaderboard-page-layout-merge .leaderboard-box-red::after {
  background-position: right 17.5vw top 10rem, left 2.5rem top 16rem, left 7vw top 9rem;
}
@media (max-width: 991.98px) {
  .leaderboard-page-layout-merge .leaderboard-box-red::after {
    background-position: right 17.5vw top 9rem, left 2.5rem top 15rem, left 16vw top 9rem;
  }
}
.leaderboard-page-layout-merge .leaderboard-box-purple::before {
  background-position: left 6.5vw top 12rem, right 4.5vw top 14rem;
}
@media (max-width: 991.98px) {
  .leaderboard-page-layout-merge .leaderboard-box-purple::before {
    background-position: left 6.5vw top 9rem, right 4.5vw top 10rem;
  }
}
.leaderboard-page-layout-merge .leaderboard-box-purple::after {
  background-position: left 8.5vw top 15rem, right 1.5vw top 18rem;
}
@media (max-width: 991.98px) {
  .leaderboard-page-layout-merge .leaderboard-box-purple::after {
    background-position: left 8.5vw top 13rem, right 1.5vw top 15rem;
  }
}
.leaderboard-page-layout-merge .leaderboard-box-green::before {
  background-position: right 5.5vw top 11vh;
}
@media (max-width: 1199.98px) {
  .leaderboard-page-layout-merge .leaderboard-box-green::before {
    background-position: right 5.5vw top 14vh;
  }
}
@media (max-width: 991.98px) {
  .leaderboard-page-layout-merge .leaderboard-box-green::before {
    background-position: right 12% top 11rem;
    right: 0;
  }
}
.leaderboard-page-layout-merge .leaderboard-box-green::after {
  right: -7vw;
  left: 0;
  bottom: -2vw;
  background-position: right 23.5vw top 2vh, left 1.5vw top 2vh, left 1.5vw top 7vh, right 4.5vw top 25.5vh;
  z-index: 2;
}
@media (max-width: 991.98px) {
  .leaderboard-page-layout-merge .leaderboard-box-green::after {
    background-position: right 30vw top 5vh, left 8.5vw top 22vh, left 4.5vw top 9rem, right 18% top 7rem;
    z-index: 1;
  }
}
.leaderboard-page-layout-merge .leaderboard-box-blue::before {
  background-position: left 5.5vw top 11vh;
}
@media (max-width: 1199.98px) {
  .leaderboard-page-layout-merge .leaderboard-box-blue::before {
    background-position: left 5.5vw top 14vh;
  }
}
@media (max-width: 991.98px) {
  .leaderboard-page-layout-merge .leaderboard-box-blue::before {
    background-position: left 11.5vw top 14rem;
  }
}
.leaderboard-page-layout-merge .leaderboard-box-blue::after {
  background-image: url(../images/Leaderboard-starlight-three2.png), url(../images/Leaderboard-starlight-three3.png), url(../images/Leaderboard-img-bankroll.png);
  background-position: left 11.5vw top 10.2vh, right 8vw top 10.5vh, right 10vw top 12.8vh;
  background-size: auto 7.6vh, auto 7.8vh, auto 9.5vh;
}
@media (max-width: 991.98px) {
  .leaderboard-page-layout-merge .leaderboard-box-blue::after {
    background-position: left 6.5vw top 25.2vh, right 2vw top 18.5vh, right 9vw top 10rem;
  }
}
.leaderboard-page-layout-merge .img-run-girl {
  display: block;
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  background-image: url(../images/Leaderboard-img-run-girl.png);
  background-repeat: no-repeat;
  background-position: right 4vw top 10vh;
  background-size: auto 21vh;
  z-index: 3;
  pointer-events: none;
}
@media (max-width: 1199.98px) {
  .leaderboard-page-layout-merge .img-run-girl {
    background-position: right 2vw top 10vh;
  }
}
@media (max-width: 991.98px) {
  .leaderboard-page-layout-merge .img-run-girl {
    background-position: left 17vw top 5rem;
    z-index: 1;
  }
}/*# sourceMappingURL=leaderboard.css.map */