﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

using ECOOL_APP.EF;
using MvcPaging;

namespace EcoolWeb.Models
{
    public class ADDI01OrderListViewModel
    {
        /// <summary>
        /// 搜尋字串
        /// </summary>
        public string whereKeyword { get; set; }

        /// <summary>
        /// 只顯示某年級
        /// </summary>
        public string whereGrade { get; set; }

        /// <summary>
        /// 只顯示某一班
        /// </summary>
        public string whereCLASS_NO { get; set; }

        /// <summary>
        /// 只顯示某一位學生的文章
        /// </summary>
        public string whereUserNo { get; set; }

        /// <summary>
        /// 月排行榜
        /// </summary>
        public bool WhereIsMonthTop { get; set; }

        public DateTime? whereSTART_CRE_DATE { get; set; }

        public DateTime? whereEND_CRE_DATE { get; set; }

        /// <summary>
        /// 排序欄位
        /// </summary>
        public string OrdercColumn { get; set; }

        public string msg { get; set; }
        public int Page { get; set; }

        /// <summary>
        /// 查詢結果
        /// </summary>
        public IPagedList<ADDV01> ADDV01List;

        /// <summary>
        /// 是否輪播不同網頁
        /// </summary>
        public bool isCarousel { get; set; }

        public bool? IsPrint { get; set; }

        public bool? IsToExcel { get; set; }

        public ADDI01OrderListViewModel()
        {
            Page = 0;
            OrdercColumn = "WRITING_QTY";
            WhereIsMonthTop = false;
        }
    }
}