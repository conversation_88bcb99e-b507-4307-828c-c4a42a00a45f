﻿@using System.Collections;
@using ECOOL_APP.com.ecool.Models.entity;
@{
    ViewBag.Title = "批次閱讀認證-檔案上傳";
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    if (string.IsNullOrWhiteSpace(UUID) == false)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }

    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    string BookID = ViewBag.BookID;
    bool BookStatus = (ViewBag.BookStatus == "ALL") ? true : false;
    string ReadrppBookCheckF = (BookID == null) ? "selected" : "";
    string ReadrppBookCheckT = (BookID != null) ? "selected" : "";
    IEnumerable<SelectListItem> BookList = Model.Select(x => new SelectListItem { Text = x.BOOK_ID + " " + x.BOOK_NAME, Value = x.BOOK_ID, Selected = x.BOOK_ID == BookID });

    var Book_Info = from Book in this.Model
                    where Book.BOOK_ID == BookID && Book.SCHOOL_NO == user.SCHOOL_NO
                    select Book;
    string Book_Name = (Book_Info.FirstOrDefault() != null) ? Book_Info.FirstOrDefault().BOOK_NAME : string.Empty;
}
@model IEnumerable<ADDT03>

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")


@using (Html.BeginForm(null, null, FormMethod.Post, new { name = "contentForm", id = "contentForm" }))
{
    <br />
    <img src="~/Content/img/web-bar2-revise-20.png" style="width:100%" class="img-responsive " alt="Responsive image" />
    <div class="Div-EZ-reader">
        <div class="form-horizontal">
            <div class="form-group">
                @Html.Label("是否為閱讀護照書籍", htmlAttributes: new { @class = "control-label-left label_dt col-md-4 col-sm-4 col-lg-4" })
                <div class="col-md-8 col-sm-8 col-lg-8">
                    <select id="ddlReadrppBook" name="ddlReadrppBook" onchange="ddlReadrppBook_onchange();" class="form-control input-sm">
                        <option value="N" @ReadrppBookCheckF>否</option>
                        <option value="Y" @ReadrppBookCheckT>是</option>
                    </select>
                </div>
            </div>

            <div class="form-group">
                @Html.Label("閱讀書名", htmlAttributes: new { @class = "control-label-left label_dt col-md-4 col-sm-4 col-lg-4" })
                <div class="col-md-8 col-sm-8 col-lg-8">
                    <samp class="dd">
                        <input type="text" id="txtBOOK_NAME" name="txtBOOK_NAME" value="@Book_Name" class="form-control input-sm" />
                        @Html.DropDownList("BOOK_ID", BookList.OrderBy(a => a.Text), "選擇書名", new { onchange = "ddlBOOK_NAME_onchange();", @class = "form-control input-sm" })
                        @Html.Hidden("BOOK_NAME", Book_Name)
                        <input type="hidden" id="hidBOOK_NAME" name="hidBOOK_NAME">
                    </samp>
                </div>
            </div>
            <div class="form-group">
                @Html.Label("檔名上傳類型", htmlAttributes: new { @class = "control-label-left label_dt col-md-4 col-sm-4 col-lg-4" })
                <div class="col-md-8 col-sm-8 col-lg-8">
                    <input type="radio" id="radUserNo" name="radFileType" onchange="FileType_onchange();" value="U" checked />學號
                    <input type="radio" id="radSeatNo" name="radFileType" onchange="FileType_onchange();" value="S" />座號
                </div>
            </div>
            <div class="form-group" id="TRsClassNo" style="display: none;">
                @Html.Label("班級", htmlAttributes: new { @class = "control-label-left label_dt col-md-4 col-sm-4 col-lg-4" })
                <div class="col-md-8 col-sm-8 col-lg-8">
                    @Html.DropDownList("Class_No", (IEnumerable<SelectListItem>)ViewBag.ClassNoItem, new { @class = "form-control input-sm" })
                </div>
            </div>
            <div class="form-group">
                <span class="control-label-left label_dt col-md-4 col-sm-4 col-lg-4">上傳檔案</span>
                <div class="col-md-8 col-sm-8 col-lg-8">
                    <input type="file" name="file" id="file" accept="zip/*" class="form-control" style="height:45px" />
                    <label class="text-info">
                        <br />
                        1.批次上傳學生心得(請用ZIP檔)<br />
                        2.圖檔格式限制jpg，並需小於6MB<br />
                        3.壓縮檔限用ZIP檔，並需小於100MB<br />
                        4.檔名上傳類型選【學號】，檔名一定是要【學號】<br />
                        5.檔名上傳類型選【座號】，檔名一定是要【座號】<br />
                    </label>
                </div>
            </div>
            <div class=" text-center">


                <button id="btnSend" class="btn btn-default" onclick="btnSend_onclick();">
                    確定送出
                </button>



            </div>
        </div>
    </div>

}

@section Scripts
{
    <script type="text/javascript">
    $(function () {
        ddlReadrppBook_onchange();
    });



    //抓取閱讀護照上所選到的書名
    function ddlBOOK_NAME_onchange() {
        $("#BOOK_NAME").val($("#BOOK_ID option:selected").text());
    }

    //設定是否讀取閱讀護照書籍
    function ddlReadrppBook_onchange() {
        try {

            if ($('select[name="ddlReadrppBook"]').val() == 'N') {
                $('#txtBOOK_NAME').show();
                $('#BOOK_ID').hide();
            }
            else {
                $('#txtBOOK_NAME').hide();
                $('#BOOK_ID').show();
            }
        }
        catch (err) {
        }
    }

    function btnCancel_onclick() {
        document.contentForm.enctype = "multipart/form-data";
        document.contentForm.action = "../ADDT02/ADDTList_CheckPending";
        document.contentForm.submit();
    }

    function FileType_onchange() {
        try {
            if ($('input[name="radFileType"][value="S"]').is(':checked') == true) {
                $('#TRsClassNo').show();
            }
            else {
                $('#TRsClassNo').hide();
            }
        }
        catch (err) {
        }
    }

    function btnSend_onclick() {
        var strMsg = '';
        try {
            if ($('input[name="radupdatepic"][value="Y"]').is(':checked') == true && $('#file').val() == '') {
                strMsg += '上傳檔案需為必填\r\n';
            }
            if ($('select[name="ddlReadrppBook"]').val() == 'Y' && $('select[name="BOOK_ID"]').val() == '') {
                strMsg += '請選擇閱讀書名\r\n';
            }
            if ($('select[name="ddlReadrppBook"]').val() == 'N' && $('#txtBOOK_NAME').val() == '' && $("#txtBOOK_NAME").val().trim().length == 0) {
                strMsg += '閱讀書名為必填\r\n';
            }
            if (strMsg != '') {
                alert(strMsg);
                return;
            }
            else {
                $("#btnSend").attr("disabled", true);

                strMsg = "請確定以下資訊是否正確\r\n\r\n"

                strMsg = strMsg + "是否為閱讀護照書籍：" + $('#ddlReadrppBook').val() + "\r\n";

                if ($('#ddlReadrppBook').val()=="Y") {
                    strMsg = strMsg + "閱讀書名：" + $('#BOOK_ID :selected').text();
                }
                else {
                    strMsg = strMsg + "閱讀書名：" + $('#txtBOOK_NAME').val() ;
                }
                strMsg = strMsg + "\r\n";

                strMsg = strMsg + "檔名上傳類型："

                if ($('#radUserNo').is(':checked') == true) {
                    strMsg = strMsg + "學號";
                }
                else {
                    strMsg = strMsg + "座號";
                    strMsg = strMsg + "\r\n";
                    strMsg = strMsg + "班級：" + $('#Class_No').val() + "\r\n";
                }

                YN = confirm(strMsg)

                if (YN==true) {
                    document.contentForm.enctype = "multipart/form-data";
                    document.contentForm.action = "SetUpload";
                    document.contentForm.submit();
                }
                else {
                    $("#btnSend").attr("disabled", false);
                }
            }
        }
        catch (err) {
        }
    }
    </script>

}