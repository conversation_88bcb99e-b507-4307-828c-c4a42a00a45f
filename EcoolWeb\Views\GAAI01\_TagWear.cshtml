﻿@model GAAI01TagWearDetailsViewModel
@ViewBag.Message
@if (Model != null)
{
    using (Html.BeginCollectionItem("TagWearDetails"))
    {
        var Index = Html.GetIndex("TagWearDetails");

        <div class="tr" id="Tr@(Model.CARD_NO)">
            <div class="td" style="text-align:center">
                @Html.HiddenFor(m => m.SCHOOL_NO)
                @Html.HiddenFor(m => m.SHORT_NAME)
                @Html.HiddenFor(m => m.USER_NO)
                @Html.HiddenFor(m => m.NAME)
                @Html.HiddenFor(m => m.GRADE)
                @Html.HiddenFor(m => m.CLASS_NO)
                @Html.HiddenFor(m => m.SEAT_NO)
                @Html.HiddenFor(m => m.CARD_NO)
                @Html.HiddenFor(m => m.IS_WEAR)
                @Model.NAME
            </div>
            <div class="td" style="text-align:center" id="@(Model.SCHOOL_NO+Model.USER_NO)">
                @Model.USER_NO
            </div>
            <div class="td" style="text-align:center" id="@(Model.SCHOOL_NO+Model.CLASS_NO+Model.SEAT_NO)">
                @Model.CLASS_NO
            </div>
            <div class="td" style="text-align:center">
                @Model.SEAT_NO
            </div>
            <div class="td" style="text-align:center">
                @if (Model.IS_WEAR)
                {
                    <strong>有配載</strong>
                }
                else
                {
                    <strong style="color:red">未配載</strong>
                }
            </div>
        </div>
    }
}