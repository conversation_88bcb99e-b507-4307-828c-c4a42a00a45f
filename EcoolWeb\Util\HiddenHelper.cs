﻿using Microsoft.CSharp.RuntimeBinder;
using NPOI.SS.Formula.Functions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Web;

namespace System.Web.Mvc.Html
{
    public static partial class HiddenHelper
    {

        public static MvcHtmlString HiddenList(this HtmlHelper htmlHelper, dynamic dyna)
        {
            var sb = new StringBuilder();
            //Type t = dyna.GetType();
            //PropertyInfo[] pis = t.GetProperties();

            //    foreach (PropertyInfo pi in pis)
            //    {
            //        try
            //        {
            //            var builder = new TagBuilder("input");
            //            builder.MergeAttribute("type", "hidden");
            //            builder.MergeAttribute("value", dyna[pi.Name]);
            //            builder.MergeAttribute("name", pi.Name);
            //            builder.MergeAttribute("id", pi.Name);
            //            sb.Append(builder.ToString(TagRenderMode.Normal));
            //        }
            //        catch (RuntimeBinderException ex)
            //        {
            //            Console.WriteLine("Error:" + ex.Message);
            //        }
            //    }
            
            return MvcHtmlString.Create(sb.ToString());
        }
    }
}