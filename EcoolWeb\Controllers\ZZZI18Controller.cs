﻿using ECOOL_APP;
using ECOOL_APP.EF;
using EcoolWeb.Models;
using EcoolWeb.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using MvcPaging;
using EcoolWeb.CustomAttribute;
using System.Data.Entity.Validation;
using System.Collections;
using System.Data.Entity;
using System.Net;
using com.ecool.service;
using System.IO;
using System.Text.RegularExpressions;
using ECOOL_APP.com.ecool.Models.entity;

namespace EcoolWeb.Controllers
{
    [SessionExpire]
    public class ZZZI18Controller : Controller
    {
        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();
        private UserProfile user = UserProfileHelper.Get();
        // GET: ZZZI18
        public ActionResult Query(ZZZI18QueryListViewModel model)
        {
            IQueryable<uZZZI18> ZZZI18List = (from w05 in db.WFT05
                                       join h01 in db.HRMT01
                                             on new { w05.SCHOOL_NO, w05.USER_NO }
                                         equals new { h01.SCHOOL_NO, h01.USER_NO }
                                       where w05.STATUS=="0"
                                       select new uZZZI18 
                                       {
                                           SCHOOL_NAME =
                                             ((from bdmt01 in db.BDMT01
                                               where
                                                 bdmt01.SCHOOL_NO == h01.SCHOOL_NO
                                               select new
                                               {
                                                   bdmt01.SCHOOL_NAME
                                               }).FirstOrDefault().SCHOOL_NAME),
                                           WFT05_NO = w05.WFT05_NO,
                                           SCHOOL_NO = h01.SCHOOL_NO,
                                           USER_NO= h01.USER_NO,
                                           USERNAME = h01.NAME,
                                           SNAME= h01.SNAME
                                       });
            if (string.IsNullOrWhiteSpace(model.whereKeyword) == false)
            {
                ZZZI18List = ZZZI18List.Where(a => a.SCHOOL_NO.Contains(model.whereKeyword.Trim()) || a.SCHOOL_NAME.Contains(model.whereKeyword.Trim()) || a.SNAME.Contains(model.whereKeyword.Trim()));
            }

            switch (model.OrdercColumn)
            {
                default:
                    ZZZI18List = ZZZI18List.OrderByDescending(a => a.SCHOOL_NO);
                    break;
            }

            model.uZZZI18List = ZZZI18List.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, 20);

            return View(model);
        }

        [HttpPost]
        public ActionResult Modify(FormCollection ZZZI18)
        {
            for (int i = 0; i < 20; i++)
            {
                if (ZZZI18["[" + i + "].chkWFT05_NO"] == "on")
                {
                    if (ZZZI18["[" + i + "].WFT05_NO"] != null || ZZZI18["[" + i + "].WFT05_NO"] != string.Empty)
                    {
                        WFT05 WFT05 = new WFT05();
                        int iWFT05_NO = Convert.ToInt32(ZZZI18["[" + i + "].WFT05_NO"]);
                        WFT05 = db.WFT05.Where(p => p.WFT05_NO == iWFT05_NO).FirstOrDefault();
                        WFT05.STATUS = "1";
                        db.Entry(WFT05).State = EntityState.Modified;
                        db.SaveChanges();
                    }

                }
            }
            //更新顯示
            UserProfile.RefreshCashInfo(user, ref db);
            UserProfileHelper.Set(user);
            return RedirectToAction("Query", "ZZZI18");
        }

        [HttpPost]
        public ActionResult ExportExcel(FormCollection form)
        {

            string strHtml = form["hHtml"];
            strHtml = HttpUtility.HtmlDecode(strHtml);//Html解碼
            byte[] b = System.Text.Encoding.Default.GetBytes(strHtml);//字串轉byte陣列
            return File(b, "application/vnd.ms-excel", "E酷幣異常清單.xls");//輸出檔案給Client端
        }
    }
}