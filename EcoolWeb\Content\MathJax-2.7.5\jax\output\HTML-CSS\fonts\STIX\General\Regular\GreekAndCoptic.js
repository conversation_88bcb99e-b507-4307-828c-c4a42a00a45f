/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/General/Regular/GreekAndCoptic.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.STIXGeneral,{894:[459,141,278,80,219],900:[662,-507,277,113,240],901:[662,-507,333,18,316],902:[683,0,722,15,707],903:[459,-348,278,81,192],904:[683,0,750,8,737],905:[683,0,850,8,836],906:[683,0,470,8,449],908:[683,14,722,8,688],910:[683,0,840,8,818],911:[683,0,744,8,715],912:[662,10,340,18,316],913:[674,0,722,15,707],914:[662,0,667,17,593],917:[662,0,611,12,597],918:[662,0,612,10,598],919:[662,0,722,18,703],921:[662,0,333,18,315],922:[662,0,731,33,723],924:[662,0,889,12,864],925:[662,11,722,12,707],927:[676,14,722,34,688],929:[662,0,557,16,542],932:[662,0,611,17,593],935:[662,0,722,10,704],938:[873,0,333,18,316],939:[873,0,722,29,703],940:[662,10,543,29,529],941:[662,10,439,25,407],942:[662,217,512,10,452],943:[662,10,275,20,267],944:[662,10,524,16,494],945:[460,10,543,29,529],946:[683,217,496,55,466],947:[457,218,474,10,444],948:[683,10,500,29,470],949:[460,10,439,25,407],950:[683,218,441,35,407],951:[460,217,512,10,452],952:[683,10,496,27,468],953:[460,10,275,20,267],954:[460,0,500,7,503],955:[683,11,497,12,492],956:[450,217,528,55,516],957:[460,14,455,20,443],958:[683,218,441,35,407],959:[460,10,505,35,473],960:[450,14,501,9,482],961:[460,217,496,55,466],962:[460,218,441,35,432],963:[450,10,548,29,518],964:[450,10,477,3,442],965:[460,10,524,16,494],966:[460,217,623,29,593],967:[460,220,500,11,486],968:[460,217,694,20,684],969:[460,10,625,29,595],970:[622,10,340,18,316],971:[622,10,524,16,494],972:[662,10,505,35,473],973:[662,10,524,16,494],974:[662,10,625,29,595],976:[693,10,450,54,411],977:[683,10,554,0,544],978:[676,0,722,29,698],981:[683,217,623,29,593],982:[450,10,762,6,726],984:[676,217,722,34,688],985:[460,217,500,29,470],986:[676,218,667,28,622],987:[490,218,461,35,436],988:[662,0,556,11,546],989:[450,190,470,80,435],990:[797,14,703,13,678],991:[662,0,511,64,455],992:[676,218,801,11,767],993:[573,216,528,-6,487],1008:[460,10,551,42,515],1009:[460,215,500,29,470],1012:[676,14,722,34,688],1013:[460,10,439,25,407],1014:[460,10,444,32,414]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/Regular/GreekAndCoptic.js");
