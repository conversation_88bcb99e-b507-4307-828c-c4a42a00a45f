﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Web;
using ECOOL_APP;
using ECOOL_APP.EF;

namespace EcoolWeb.Models
{
    public class DownloadManagerIndex2ViewModel
    {
        [DisplayName("文件名稱")]
        public string whereSearch { get; set; }
        public string ACT { get; set; }
        public string DL_ID { get; set; }
        public string FileType { get; set; }
        /// <summary>
        /// 一頁筆數 預設筆數
        /// </summary>
        public int PageSize { get; set; }
        public string ShowType { get; set; }
        /// <summary>
        /// 排序欄位
        /// </summary>
        public string OrdercColumn { get; set; }

        /// <summary>
        /// 是否顯示作廢說明文件，Y=是
        /// </summary>
        public string ShowDisable { get; set; }

        /// <summary>
        /// 頁次
        /// </summary>
        public int Page { get; set; }

        public string BackAction { get; set; }

        public string BackController { get; set; }
        public SortedList<string, SortedList<string, List<DLMT01>>> GroupDataItem;
        /// <summary>
        /// 查詢結果
        /// </summary>
        public SortedList<string, List<DLMT01> > GroupData;
        //public IPagedList<FAQT01> ListData;

        public DownloadManagerIndex2ViewModel()
        {
            PageSize =50;
        }
    }
}