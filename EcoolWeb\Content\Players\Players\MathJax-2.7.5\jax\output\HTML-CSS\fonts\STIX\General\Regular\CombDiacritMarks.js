/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/General/Regular/CombDiacritMarks.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.STIXGeneral,{773:[820,-770,0,-480,20],777:[751,-492,0,-307,-118],781:[700,-500,0,-250,-195],782:[700,-500,0,-326,-133],783:[678,-507,0,-401,-22],784:[767,-507,0,-373,-92],785:[664,-507,0,-373,-92],786:[745,-502,0,-299,-160],787:[745,-502,0,-299,-160],788:[745,-502,0,-299,-160],789:[745,-502,0,-85,54],790:[-53,224,0,-351,-127],791:[-53,224,0,-371,-147],792:[-53,283,0,-397,-210],793:[-53,283,0,-267,-80],794:[735,-531,0,-380,-80],795:[474,-345,0,-44,51],796:[-71,266,0,-360,-232],797:[-53,240,0,-345,-115],798:[-53,240,0,-345,-115],799:[-53,250,0,-326,-134],800:[-124,168,0,-326,-134],801:[75,287,0,-235,1],802:[75,287,0,-54,182],803:[-118,217,0,-280,-181],804:[-119,218,0,-379,-81],805:[-69,268,0,-329,-130],806:[-110,353,0,-299,-160],807:[0,215,0,-334,-125],808:[0,165,0,-322,-137],809:[-102,234,0,-250,-210],810:[-98,235,0,-385,-73],811:[-110,227,0,-380,-75],812:[-73,240,0,-385,-74],813:[-73,240,0,-385,-74],814:[-68,225,0,-370,-89],815:[-59,216,0,-370,-89],816:[-113,219,0,-395,-65],817:[-141,195,0,-385,-74],818:[-141,191,0,-480,20],819:[-141,300,0,-480,20],820:[320,-214,0,-401,-71],821:[274,-230,0,-384,-78],822:[274,-230,0,-480,20],823:[580,74,0,-380,-41],825:[-71,266,0,-280,-152],826:[-53,190,0,-385,-73],827:[-53,227,0,-313,-147],828:[-65,189,0,-380,-79],829:[715,-525,0,-326,-135],830:[829,-499,0,-283,-177],831:[928,-770,0,-480,20],838:[681,-538,0,-350,-68],839:[-140,292,1,11,323],844:[777,-532,0,-386,-56],857:[-65,367,0,-357,-87],860:[-76,233,0,-373,295],864:[633,-517,0,-395,365],865:[664,-507,0,-373,295],866:[-65,270,0,-395,355]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/Regular/CombDiacritMarks.js");
