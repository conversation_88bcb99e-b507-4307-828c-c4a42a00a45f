﻿
@model GameLotteryPeopleViewModel

@using (Html.BeginCollectionItem("People"))
{
    @Html.HiddenFor(m => m.TEMP_USER_ID)
    @Html.HiddenFor(m => m.NAME)
    @Html.HiddenFor(m => m.GAME_USER_TYPE)
    @Html.HiddenFor(m => m.PHONE)
    @Html.HiddenFor(m => m.USER_NO)
    @Html.HiddenFor(m => m.SCHOOL_NO)
    @Html.HiddenFor(m => m.SNAME)
    @Html.HiddenFor(m => m.SEX)
    @Html.HiddenFor(m => m.GRADE)
    @Html.HiddenFor(m => m.CLASS_NO)
    @Html.HiddenFor(m => m.SEAT_NO)
}

