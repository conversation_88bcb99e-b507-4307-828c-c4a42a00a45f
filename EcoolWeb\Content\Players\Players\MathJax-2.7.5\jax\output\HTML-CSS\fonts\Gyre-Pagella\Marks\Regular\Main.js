/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Gyre-<PERSON>lla/Marks/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.GyrePagellaMathJax_Marks={directory:"Marks/Regular",family:"GyrePagellaMathJax_Marks",testString:"\u00A0\u02DB\u02DD\u0305\u0309\u030F\u0311\u0323\u0326\u032C\u032D\u032E\u032F\u0330\u0331",32:[0,0,250,0,0],160:[0,0,250,0,0],731:[15,250,313,49,265],733:[683,-502,380,73,437],773:[646,-598,0,-416,-83],777:[721,-540,0,-337,-162],783:[718,-537,0,-497,-133],785:[706,-577,0,-431,-69],803:[-93,193,0,-300,-200],806:[-77,271,0,-347,-205],812:[-60,202,0,-424,-76],813:[-70,212,0,-424,-76],814:[-60,189,0,-431,-69],815:[-78,207,0,-431,-69],816:[-78,198,0,-421,-79],817:[-116,169,0,-405,-93],818:[-60,108,0,-416,-83],819:[-60,216,0,-416,-83],831:[754,-598,0,-416,-83],8192:[0,0,500,0,0],8193:[0,0,1000,0,0],8199:[0,0,500,0,0],8200:[0,0,250,0,0],8203:[0,0,0,0,0],8204:[0,0,0,0,0],8205:[0,0,0,0,0],8208:[287,-215,333,17,312],8210:[375,-315,660,80,580],8213:[280,-220,1160,80,1080],8215:[-60,216,493,80,413],8218:[110,153,278,22,210],8222:[110,153,500,51,449],8226:[450,-50,560,80,480],8239:[0,0,200,0,0],8240:[709,20,1000,63,961],8241:[709,20,1323,63,1284],8246:[779,-446,493,60,433],8247:[779,-446,693,60,633],8249:[428,-71,331,66,265],8250:[428,-71,331,66,265],8251:[534,34,606,19,587],8253:[734,5,444,43,395],8274:[692,0,500,34,466],8287:[0,0,222,0,0],8288:[0,0,0,0,0],8289:[660,160,940,60,880],8290:[0,0,0,0,0],8291:[0,0,0,0,0],8292:[0,0,0,0,0],8400:[784,-640,0,-442,-58],8401:[784,-640,0,-442,-58],8402:[650,150,0,-274,-226],8403:[500,0,0,-280,-220],8404:[862,-639,0,-453,-47],8405:[862,-639,0,-453,-47],8406:[784,-544,0,-443,-57],8408:[410,-90,0,-410,-90],8411:[672,-572,0,-520,20],8412:[672,-572,0,-630,130],8413:[668,168,0,-668,168],8414:[650,150,0,-650,150],8415:[851,351,0,-851,351],8417:[784,-544,0,-479,-21],8420:[698,213,0,-776,276],8421:[650,150,0,-413,-87],8422:[650,150,0,-358,-142],8424:[-60,160,0,-520,20],8425:[771,-646,0,-433,-66],8426:[400,-100,0,-630,130],8427:[650,150,0,-502,2],8428:[-170,314,0,-442,-58],8429:[-170,314,0,-442,-58],8430:[-74,314,0,-443,-57],8431:[-74,314,0,-443,-57],8432:[769,-511,0,-367,-122],11800:[499,240,444,49,401],12310:[670,170,474,80,394],12311:[670,170,474,80,394]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"GyrePagellaMathJax_Marks"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Marks/Regular/Main.js"]);
