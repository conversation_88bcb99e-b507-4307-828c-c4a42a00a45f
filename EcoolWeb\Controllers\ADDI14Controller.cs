﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Web;
using System.Web.Mvc;
using com.ecool.service;
using ECOOL_APP;
using ECOOL_APP.EF;
using EcoolWeb.CustomAttribute;
using EcoolWeb.Models;

namespace EcoolWeb.Controllers
{
    [SessionExpire]
    public class ADDI14Controller : Controller
    {
        /// <summary>
        /// 此Controller 對應的程式代碼
        /// </summary>
        private static string Bre_NO = "ADDI14";

        private string Bre_Name = BreadcrumbService.GetBRE_NAME_forBRE_NO(Bre_NO);

        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();

        private bool IsAdmin = false;

        private string SCHOOL_NO = string.Empty;

        private readonly ADDI14Service Service = new ADDI14Service();

        /// <summary>
        /// 會員相關資訊
        /// </summary>
        private UserProfile user;

        private string USER_NO = string.Empty;

        public ActionResult Index()
        {
            this.Shared();
            this.LoginOUT();
            return View();
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult IndexExcel(HttpPostedFileBase files)
        {
            this.Shared();
            this.LoginOUT();
            if (files == null || files.FileName == string.Empty) ModelState.AddModelError("files", "請上傳檔案");
            else if (files != null)
            {
                Regex regexCode = new Regex(@".*\.(xls|xlsx)");

                string fileNameExtension = Path.GetExtension(files.FileName);

                if (regexCode.IsMatch(fileNameExtension.ToLower()) == false)
                {
                    ModelState.AddModelError("files", "請上傳Excel格式為xls、xlsx");
                }
            }

            if (ModelState.IsValid == false)
            {
                TempData["StatusMessage"] = "錯誤\r\n";
                return View("Index");
            }
            else
            {
                string Message = string.Empty;
                var model = this.Service.GetExcelToViewModel(files, SCHOOL_NO, ref db, ref Message);

                if (!string.IsNullOrEmpty(Message))
                {
                    TempData["StatusMessage"] = Message;
                    return RedirectToAction("Index");
                }
                return View(model);
            }
        }

        public ActionResult _EditDetails(ADDI14EditPeopleViewModel Item)
        {
            this.Shared();
            return PartialView(Item);
        }

        public ActionResult Export(ADDI14IndexViewModel model)
        {
            this.Shared();

            string Message = string.Empty;
            string FileName = Service.GetToExceFile(model, ref db, ref Message);

            if (!string.IsNullOrWhiteSpace(Message))
            {
                TempData["StatusMessage"] = Message;
                return RedirectToAction("Index");
            }

            if (System.IO.File.Exists(FileName))
            {
                return File(System.IO.File.ReadAllBytes(FileName), "application/vnd.ms-excel", "報到資料.xlsx");//輸出檔案給Client端
            }
            else
            {
                return RedirectToAction(ErrorHelper.ErrorVal.NotFileError, "Error");
            }
        }

        private void LoginOUT()
        {
            UserProfile LoginUser = UserProfileHelper.Get();

            if (LoginUser != null)
            {
                LogHelper.AddLogToDB(LoginUser.SCHOOL_NO, null, System.Web.HttpContext.Current.Request.UserHostAddress, "HOME", "LoginOUT");
                this.Request.RequestContext.HttpContext.Session[CONSTANT.SESSION_USER_KEY] = null;
            }
        }

        #region Shared

        private void SetTitle(string Panel_Title = "")
        {
            if (!string.IsNullOrWhiteSpace(Panel_Title))
            {
                ViewBag.Panel_Title = Panel_Title;
            }
            else
            {
                ViewBag.Panel_Title = Bre_Name;
            }
        }

        private void Shared(string Panel_Title = "")
        {
            ViewBag.BRE_NO = Bre_NO;

            if (!string.IsNullOrWhiteSpace(Panel_Title))
            {
                ViewBag.Panel_Title = Panel_Title;
            }
            else
            {
                ViewBag.Panel_Title = Bre_Name;
            }

            user = UserProfileHelper.Get();
            SCHOOL_NO = UserProfileHelper.GetSchoolNo();

            if (user != null)
            {
                if (user.SCHOOL_NO != null)
                {
                    SCHOOL_NO = user.SCHOOL_NO;
                    USER_NO = user.USER_NO;
                }
            }
            IsAdmin = HRMT24_ENUM.CheckQAdmin(user);
            ViewBag.IsAdmin = IsAdmin;
        }

        #endregion Shared

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}