(function(factory){if (typeof define === 'function' && define.amd){define(['jquery'], factory);} else if (typeof module === 'object' && typeof module.exports === 'object'){module.exports = factory(require('jquery'));} else {factory(jQuery);}}(function(jQuery){

!function(h){"use strict";var f=h.tablesorter,c=".tscolsel",S=f.columnSelector={queryAll:"@media only all { [columns] { display: none; } } ",queryBreak:"@media all and (min-width: [size]) { [columns] { display: table-cell; } } ",init:function(e,t,o){var l,a,n=f.debug(t,"columnSelector");(l=h(o.columnSelector_layout)).find("input").add(l.filter("input")).length?(t.$table.addClass(t.namespace.slice(1)+"columnselector"),(a=t.selector={$container:h(o.columnSelector_container||"<div>")}).$style=h("<style></style>").prop("disabled",!0).appendTo("head"),a.$breakpoints=h("<style></style>").prop("disabled",!0).appendTo("head"),a.isInitializing=!0,S.setUpColspan(t,o),S.setupSelector(t,o),o.columnSelector_mediaquery&&S.setupBreakpoints(t,o),a.isInitializing=!1,a.$container.length?S.updateCols(t,o):n&&console.warn("ColumnSelector >> container not found"),t.$table.off("refreshColumnSelector"+c).on("refreshColumnSelector"+c,function(e,t,o){S.refreshColumns(this.config,t,o)}),n&&console.log("ColumnSelector >> Widget initialized")):n&&console.error("ColumnSelector >> ERROR: Column Selector aborting, no input found in the layout! ***")},refreshColumns:function(e,t,o){var l,a,n,c,r=e.selector,s=h.isArray(o||t),i=e.widgetOptions;if(null!=t&&r.$container.length){if("selectors"===t&&(r.$container.empty(),S.setupSelector(e,i),S.setupBreakpoints(e,i),void 0===o&&null!==o&&(o=r.auto)),s)for(a=o||t,h.each(a,function(e,t){a[e]=parseInt(t,10)}),l=0;l<e.columns;l++)c=0<=h.inArray(l,a),(n=r.$container.find("input[data-column="+l+"]")).length&&(n.prop("checked",c),r.states[l]=c);c=!0===o||!0===t||"auto"===t&&!1!==o,n=r.$container.find('input[data-column="auto"]').prop("checked",c),S.updateAuto(e,i,n)}else S.updateBreakpoints(e,i),S.updateCols(e,i);S.saveValues(e,i),S.adjustColspans(e,i)},setupSelector:function(t,o){var e,l,a,n,c,r,s,i=t.selector,u=i.$container,d=o.columnSelector_saveColumns&&f.storage,p=d?f.storage(t.table,"tablesorter-columnSelector"):[],m=d?f.storage(t.table,"tablesorter-columnSelector-auto"):{};for(i.auto=h.isEmptyObject(m)||"boolean"!==h.type(m.auto)?o.columnSelector_mediaqueryState:m.auto,i.states=[],i.$column=[],i.$wrapper=[],i.$checkbox=[],e=0;e<t.columns;e++)n=(a=t.$headerIndexed[e]).attr(o.columnSelector_priority)||1,r=a.attr("data-column"),c=f.getColumnData(t.table,t.headers,r),m=f.getData(a,c,"columnSelector"),isNaN(n)&&0<n.length||"disable"===m||o.columnSelector_columns[r]&&"disable"===o.columnSelector_columns[r]?i.states[r]=null:(i.states[r]=p&&void 0!==p[r]&&null!==p[r]?p[r]:void 0!==o.columnSelector_columns[r]&&null!==o.columnSelector_columns[r]?o.columnSelector_columns[r]:"true"===m||"false"!==m,i.$column[r]=h(this),u.length&&(l=a.attr(o.columnSelector_name)||a.text().trim(),"function"==typeof o.columnSelector_layoutCustomizer&&(s=a.find("."+f.css.headerIn),l=o.columnSelector_layoutCustomizer(s.length?s:a,l,parseInt(r,10))),i.$wrapper[r]=h(o.columnSelector_layout.replace(/\{name\}/g,l)).appendTo(u),i.$checkbox[r]=i.$wrapper[r].find("input").add(i.$wrapper[r].filter("input")).attr("data-column",r).toggleClass(o.columnSelector_cssChecked,i.states[r]).prop("checked",i.states[r]).on("change",function(){if(!i.isInitializing){var e=h(this).attr("data-column");if(!S.checkChange(t,this.checked))return this.checked=!this.checked,!1;t.selector.states[e]=this.checked,S.updateCols(t,o)}}).change()))},checkChange:function(e,t){for(var o=e.widgetOptions,l=o.columnSelector_maxVisible,a=o.columnSelector_minVisible,n=e.selector.states,c=n.length,r=0;0<=c--;)n[c]&&r++;return!(t&null!==l&&l<=r||!t&&null!==a&&r<=a)},setupBreakpoints:function(e,t){var o=e.selector;t.columnSelector_mediaquery&&(o.lastIndex=-1,S.updateBreakpoints(e,t),e.$table.off("updateAll"+c).on("updateAll"+c,function(){S.setupSelector(e,t),S.setupBreakpoints(e,t),S.updateBreakpoints(e,t),S.updateCols(e,t)})),o.$container.length&&(t.columnSelector_mediaquery&&(o.$auto=h(t.columnSelector_layout.replace(/\{name\}/g,t.columnSelector_mediaqueryName)).prependTo(o.$container),o.$auto.find("input").add(o.$auto.filter("input")).attr("data-column","auto").prop("checked",o.auto).toggleClass(t.columnSelector_cssChecked,o.auto).on("change",function(){S.updateAuto(e,t,h(this))}).change()),e.$table.off("update"+c).on("update"+c,function(){S.updateCols(e,t)}))},updateAuto:function(e,t,o){var l=e.selector;l.auto=o.prop("checked")||!1,h.each(l.$checkbox,function(e,t){t&&(t[0].disabled=l.auto,l.$wrapper[e].toggleClass("disabled",l.auto))}),t.columnSelector_mediaquery&&S.updateBreakpoints(e,t),S.updateCols(e,t),e.selector.$popup&&e.selector.$popup.find(".tablesorter-column-selector").html(l.$container.html()).find("input").each(function(){var e=h(this).attr("data-column");h(this).prop("checked","auto"===e?l.auto:l.states[e])}),S.saveValues(e,t),S.adjustColspans(e,t),l.auto&&e.$table.triggerHandler(t.columnSelector_updated)},addSelectors:function(e,t,o){var l=[],a=" col:nth-child("+o+")";return l.push(t+a+","+t+"_extra_table"+a),a=" tr:not(."+e.columnSelector_classHasSpan+') th[data-column="'+(o-1)+'"]',l.push(t+a+","+t+"_extra_table"+a),a=" tr:not(."+e.columnSelector_classHasSpan+") td:nth-child("+o+")",l.push(t+a+","+t+"_extra_table"+a),a=" tr td:not("+t+e.columnSelector_classHasSpan+')[data-column="'+(o-1)+'"]',l.push(t+a+","+t+"_extra_table"+a),l},updateBreakpoints:function(e,t){var o,l,a,n,c=[],r=e.selector,s=e.namespace+"columnselector",i=[],u="";if(t.columnSelector_mediaquery&&!r.auto)return r.$breakpoints.prop("disabled",!0),void r.$style.prop("disabled",!1);if(t.columnSelector_mediaqueryHidden)for(a=0;a<e.columns;a++)l=f.getColumnData(e.table,e.headers,a),c[a+1]="false"===f.getData(e.$headerIndexed[a],l,"columnSelector"),c[a+1]&&(i=i.concat(S.addSelectors(t,s,a+1)));for(o=0;o<t.columnSelector_maxPriorities;o++)n=[],e.$headers.filter("["+t.columnSelector_priority+"="+(o+1)+"]").each(function(){a=parseInt(h(this).attr("data-column"),10)+1,c[a]||(n=n.concat(S.addSelectors(t,s,a)))}),n.length&&(i=i.concat(n),u+=S.queryBreak.replace(/\[size\]/g,t.columnSelector_breakpoints[o]).replace(/\[columns\]/g,n.join(",")));r.$style&&r.$style.prop("disabled",!0),i.length&&r.$breakpoints.prop("disabled",!1).text(S.queryAll.replace(/\[columns\]/g,i.join(","))+u)},updateCols:function(e,t){if(!(t.columnSelector_mediaquery&&e.selector.auto||e.selector.isInitializing)){var o,l=e.selector,a=[],n=e.namespace+"columnselector";l.$container.find("input[data-column]").filter('[data-column!="auto"]').each(function(){this.checked||(o=parseInt(h(this).attr("data-column"),10)+1,a=a.concat(S.addSelectors(t,n,o))),h(this).toggleClass(t.columnSelector_cssChecked,this.checked)}),t.columnSelector_mediaquery&&l.$breakpoints.prop("disabled",!0),l.$style&&l.$style.prop("disabled",!1).text(a.length?a.join(",")+" { display: none; }":""),S.saveValues(e,t),S.adjustColspans(e,t),e.$table.triggerHandler(t.columnSelector_updated)}},setUpColspan:function(e,t){var o,l,a,n=h(window),c=!1,r=e.$table.add(h(e.namespace+"_extra_table")).children().children("tr").children("th, td"),s=r.length;for(o=0;o<s;o++)1<(l=r[o].colSpan)&&(c=!0,r.eq(o).addClass(e.namespace.slice(1)+"columnselector"+t.columnSelector_classHasSpan).attr("data-col-span",l),f.computeColumnIndex(r.eq(o).parent().addClass(t.columnSelector_classHasSpan)));c&&t.columnSelector_mediaquery&&(a=e.namespace+"columnselector",n.off(a).on("resize"+a,f.window_resize).on("resizeEnd"+a,function(){n.off("resize"+a,f.window_resize),S.adjustColspans(e,t),n.on("resize"+a,f.window_resize)}))},adjustColspans:function(e,t){var o,l,a,n,c,r,s=e.selector,i=t.filter_filteredRow||"filtered",u=t.columnSelector_mediaquery&&s.auto,d=e.$table.children("thead, tfoot").children().children().add(h(e.namespace+"_extra_table").children("thead, tfoot").children().children()).add(e.$table.find(".group-header").children()),p=d.length;for(o=0;o<p;o++)if(r=d.eq(o),c=(a=parseInt(r.attr("data-column"),10)||r[0].cellIndex)+(n=parseInt(r.attr("data-col-span"),10)||1),1<n){for(l=a;l<c;l++)(!u&&!1===s.states[l]||u&&e.$headerIndexed[l]&&!e.$headerIndexed[l].is(":visible"))&&n--;n?r.removeClass(i)[0].colSpan=n:r.addClass(i)}else void 0!==s.states[a]&&null!==s.states[a]&&r.toggleClass(i,!u&&!s.states[a])},saveValues:function(e,t){if(t.columnSelector_saveColumns&&f.storage){var o=e.selector;f.storage(e.$table[0],"tablesorter-columnSelector-auto",{auto:o.auto}),f.storage(e.$table[0],"tablesorter-columnSelector",o.states)}},attachTo:function(e,t){var o,l,a,n=(e=h(e)[0]).config,c=h(t);c.length&&n&&(c.find(".tablesorter-column-selector").length||c.append('<span class="tablesorter-column-selector"></span>'),o=n.selector,l=n.widgetOptions,c.find(".tablesorter-column-selector").html(o.$container.html()).find("input").each(function(){var e=h(this).attr("data-column"),t="auto"===e?o.auto:o.states[e];h(this).toggleClass(l.columnSelector_cssChecked,t).prop("checked",t)}),o.$popup=c.on("change","input",function(){if(!o.isInitializing){if(!S.checkChange(n,this.checked))return this.checked=!this.checked,!1;a=h(this).toggleClass(l.columnSelector_cssChecked,this.checked).attr("data-column"),o.$container.find('input[data-column="'+a+'"]').prop("checked",this.checked).trigger("change")}}))}};f.window_resize=function(){f.timer_resize&&clearTimeout(f.timer_resize),f.timer_resize=setTimeout(function(){h(window).trigger("resizeEnd")},250)},f.addWidget({id:"columnSelector",priority:10,options:{columnSelector_container:null,columnSelector_columns:{},columnSelector_saveColumns:!0,columnSelector_layout:'<label><input type="checkbox">{name}</label>',columnSelector_layoutCustomizer:null,columnSelector_name:"data-selector-name",columnSelector_mediaquery:!0,columnSelector_mediaqueryName:"Auto: ",columnSelector_mediaqueryState:!0,columnSelector_mediaqueryHidden:!1,columnSelector_maxVisible:null,columnSelector_minVisible:null,columnSelector_breakpoints:["20em","30em","40em","50em","60em","70em"],columnSelector_maxPriorities:6,columnSelector_priority:"data-priority",columnSelector_cssChecked:"checked",columnSelector_classHasSpan:"hasSpan",columnSelector_updated:"columnUpdate"},init:function(e,t,o,l){S.init(e,o,l)},remove:function(e,t,o,l){var a=t.selector;!l&&a&&(a&&a.$container.empty(),a.$popup&&a.$popup.empty(),a.$style.remove(),a.$breakpoints.remove(),h(t.namespace+"columnselector"+o.columnSelector_classHasSpan).removeClass(o.filter_filteredRow||"filtered"),t.$table.find("[data-col-span]").each(function(e,t){var o=h(t);o.attr("colspan",o.attr("data-col-span"))}),t.$table.off("updateAll"+c+" update"+c))}})}(jQuery);return jQuery;}));
