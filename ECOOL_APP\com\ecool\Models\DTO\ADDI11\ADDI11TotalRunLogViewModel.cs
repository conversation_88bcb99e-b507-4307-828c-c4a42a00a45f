﻿using MvcPaging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace ECOOL_APP.EF
{
    public enum TotalRunLogType
    {
        本班跑步情形,
        全校跑步情形
    }
    public class ADDI11TotalRunLogViewModel
    {
        public TotalRunLogType runLogViewType { get; set; }
        public string whereSCHOOL_NO { get; set; }
        public string whereCLASS_NO { get; set; }
        public string whereRUN_YEAR { get; set; }
        public string whereRUN_MONTH { get; set; }

        public string OrderColumn { get; set; }
        public string SortBy { get; set; }

        public IPagedList<ADDI11TotalRunLog> TotalRunLogList { get; set; }
        public List<SelectListItem> YearDropDown { get; set; }
        public List<SelectListItem> MonthDropDown { get; set; }
        public List<SelectListItem> ClassDropDown { get; set; }

        public int Page { get; set; }
        public int PageSize { get; set; }
        public ADDI11TotalRunLogViewModel()
        {
            PageSize = 15;
            Page = 0;
            SortBy = "DESC";
        }
    }
    public class ADDI11TotalRunLog
    {
        public string SCHOOL_NO { get; set; }
        public string CLASS_NO { get; set; }
        public DateTime RUN_DATE { get; set; }
        public int RUN_COUNT { get; set; }

        public List<Runner> RUNNERS { get;set;}

        public ADDI11TotalRunLog()
        {
            RUNNERS = new List<Runner>();
        }
    }

    public class Runner
    {
        public string NAME { get; set; }
        public double LAP { get; set; }
    }
}