﻿@model IEnumerable<ECOOL_APP.com.ecool.Models.DTO.ADDTViewModel>
@using PagedList
@using PagedList.Mvc
@{
    ViewBag.Title = "小小讀書人學生閱讀成果";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<br/>
<br />
<br />
<table class="table">
    <tr>

        <th>
            @Html.DisplayNameFor(model => model.SYEAR)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.SEME)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.CLASS_NO)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.SEAT_NO)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.USERNAME)
        </th>
        <th>
            @*@string sBOOK_QTY = model => model.USERNAME;
            @Html.ActionLink(sBOOK_QTY, "ADDTList", new { sortOrder = ViewBag.BOOKSortParm, page = x })*@
             @Html.DisplayNameFor(model => model.BOOK_QTY)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.LEVEL_DESC)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.UP_DATE)
        </th>
    </tr>

    @foreach (var item in Model)
    {
        <tr>
            <td>
                @Html.DisplayFor(modelItem => item.SYEAR)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.SEME)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.CLASS_NO)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.SEAT_NO)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.USERNAME)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.BOOK_QTY)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.LEVEL_DESC)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.UP_DATE)
            </td>
        </tr>
    }
</table>
@Html.PagedListPager((IPagedList)Model, x => Url.Action("ADDTList", new { sortOrder = "CLASS_NO", page = x }))