﻿@{
    ViewBag.Title ="YES";
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
    string SCHOOL_NO = EcoolWeb.Models.UserProfileHelper.GetSchoolNo();
}

<!DOCTYPE html>

<div class="col-md-12">

    <img src="~/Content/img/Yes.png" id="yesimage" style="display:block; margin:auto;width: 300px;
  height: 300px;" />
    <div class="input-group input-group-lg">
        <span class="input-group-addon"><i class="fa fa-user"></i></span>
        @Html.Editor("CARD_NO", new { htmlAttributes = new { @class = "form-control", @placeholder = "請用數位學生證感應", @onKeyPress = "call(event,this);" } })
    </div>
    <div class="row">
        <div class="col-lg-6 col-lg-offset-3">
            <div class="use-absolute" id="ErrorDiv">
                <div class="use-absoluteDiv">
                    <div class="alert alert-danger" role="alert">
                        <h1>
                            <i class="fa fa-exclamation-circle"></i>
                            <strong id="ErrorStr"></strong>
                        </h1>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="row">
    <div class="col-lg-6 col-lg-offset-3">
        <div class="use-absolute" id="ErrorDiv">
            <div class="use-absoluteDiv">
                <div class="alert alert-danger" role="alert">
                    <h1>
                        <i class="fa fa-exclamation-circle"></i>
                        <strong id="ErrorStr"></strong>
                    </h1>
                </div>
            </div>
        </div>
    </div>
</div>

    <script language="JavaScript">
        $(document).ready(function () {
            $("#CARD_NO").focus();
        });
           function call(e, input) {
            var code = (e.keyCode ? e.keyCode : e.which);

            if (code == 13) // 13 是 Enter 按鍵的值
            {
                var CARD_NO = $('#CARD_NO').val();

                event.preventDefault();

                if (CARD_NO != '') {

                    if ($("#Tr" + CARD_NO).length > 0 || $("#@(SCHOOL_NO)" + CARD_NO).length >0 ) {
                        $('#ErrorStr').html('請勿重複刷卡，謝謝')
                        $('#ErrorDiv').show()

                        setTimeout(function () {
                            $('#CARD_NO').val('')
                            $("#CARD_NO").focus();
                            $('#ErrorStr').val('')
                            $('#ErrorDiv').hide()
                        }, 2000);
                    }
                    else {

                        $('#CARD_NO').prop('readonly', true);

                        setTimeout(function () {
                            OnKeyinUse(CARD_NO)
                        });
                    }

                }
            }
        }
          function OnKeyinUse(CARD_NO) {

            var data = {
                "CARD_NO": CARD_NO
             };

            $.ajax({
                url: '@Url.Action("_OpenPersonAddData", (string)ViewBag.BRE_NO)',
                data: data,
                cache: false,
                success: function (html) {
                    $("#editorRows").prepend(html);
                    $('#ErrorDiv').show()
                    if (html.length>0) {
                        $('#ErrorStr').html('感應成功…')
                        $("#yesimage").attr("src", '@Url.Content("~/Content/img/right.png")');
                        setTimeout(function () {
                            $('#CARD_NO').prop('readonly', false);
                            $('#CARD_NO').val('')
                            $("#CARD_NO").focus();
                            $('#ErrorDiv').hide()
                            $('#ErrorStr').html('');
                               $("#yesimage").attr("src", '@Url.Content("~/Content/img/Yes.png")');
                        }, 1000);

                    }
                    else {
                        $('#ErrorStr').html('此數位學生證對應不到學生資料…')
                        setTimeout(function () {
                            $('#CARD_NO').prop('readonly', false);
                            $('#CARD_NO').val('')
                            $("#CARD_NO").focus();
                            $('#ErrorDiv').hide()
                            $('#ErrorStr').html('');
                        }, 1500);
                    }

                }
            });
        }
    </script>
