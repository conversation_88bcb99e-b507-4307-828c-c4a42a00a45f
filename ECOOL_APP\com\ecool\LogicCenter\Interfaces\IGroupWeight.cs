﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.com.ecool.LogicCenter.Interfaces
{

    /// <summary>
    /// 群組權重 (不重複)
    /// </summary>
    public interface IGroupWeight<T>
    {
        string GroupName { get; set; }
        int DataCount { get; set; }
        double Weights { get; set; }

        IEnumerable<T> Datas { get; set; }

        void ReduceWeightsAndCount();
    }
}
