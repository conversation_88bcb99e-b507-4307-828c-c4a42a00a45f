﻿@model SECI05IndexViewModel
@{

    Layout = "~/Views/Shared/_LayoutWebView.cshtml";
}
@using (Html.BeginForm("Index", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1" }))
{
    <div id="tbData">
        <div class="row">
            <div class="col-sm-12 ">
                <table class="table-ecool table-ecool-Bule-SEC">
                    <thead>
                        <tr>
                            <th colspan="4">
                                目前借閱書籍
                            </th>
                        </tr>
                        <tr>
                            <th>
                                分類編號
                            </th>
                            <th>
                                書名
                            </th>
                            <th>
                                借閱時間
                            </th>
                            <th>
                                備註
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        @if ((Model.NowBorrowList != null ? Model.NowBorrowList.Count : 0) > 0)
                        {
                            foreach (var item in Model.NowBorrowList.Take(5))
                            {
                                <tr class="text-center">
                                    <td title="分類編號">@item.BK_GRP</td>
                                    <td title="書名">@item.BKNAME</td>
                                    <td title="借閱時間">
                                        @if (item.BORROW_DATE != null)
                                        {
                                            @item.BORROW_DATE.ToString("yyyy/MM/dd")
                                        }
                                    </td>
                                    <td title="備註">
                                        @if (item.EXPIRED_DAY >= 14)
                                        {
                                            <font>借閱天數超過14天</font>
                                        }
                                    </td>
                                </tr>
                            }

                        }
                        else
                        {
                            <tr class="text-center">
                                <td colspan="4">暫時無正在借書的數量</td>
                            </tr>
                        }
                    </tbody>
                    <tfoot>
                        <tr class="text-right">
                            <td colspan="4">
                                <button type="button" class="colorbox btn btn-link-ez btn-xs" href='@Url.Action("BorrowList", (string)ViewBag.BRE_NO,new { WhereSCHOOL_NO=Model.WhereSCHOOL_NO, WhereUSER_NO=Model.WhereUSER_NO })'>more</button>
                            </td>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
        <div style="height:50px"></div>
        @if (Model.BorrowTypeQty != null)
        {
            SECI05BorrowTypeQtyViewModel Top1 = Model.BorrowTypeQty.OrderByDescending(a => a.Rate).FirstOrDefault();
            SECI05BorrowTypeQtyViewModel Top2 = null;
            if (Model.BorrowTypeQty.Count > 1)
            {
                Top2 = Model.BorrowTypeQty.OrderByDescending(a => a.Rate).Skip(1).FirstOrDefault();
            }

            if (Top1.Rate >= 0.85)
            {
                <div class="text-left" style="color:red;font-size:25px;margin-left:20px">
                    您的@(Top1.TYPE_NAME)：比例為@(Top1.Rate.ToString("P"))，<br />注意是否有閱讀偏食，建議多類別閱讀。
                </div>
            }
            else
            {
                <div class="text-left" style="color:red;font-size:25px;margin-left:20px">
                    你的閱讀量最高類別為@(Top1.TYPE_NAME)，比例為@(Top1.Rate.ToString("P"))；<br />
                    @if (Top2 != null)
                    {
                        <text> 第二高為  @(Top2.TYPE_NAME)：比例為@(Top2.Rate.ToString("P"))；</text> <br />
                    }
                    沒有閱讀偏食，請繼續多元閱讀，豐富人生。
                </div>
            }

            <div class="row">
                <div class="col-sm-12 col-xs-12">
                    <div class="text-right">
                        <div class="form-inline" role="form" id="search">
                            <div class="form-group">
                                <label class="control-label">請選擇時間</label>
                            </div>
                            <div class="form-group">
                                @Html.DropDownListFor(m => m.WhereSEYEAR, (IEnumerable<SelectListItem>)ViewBag.GradeSeyearItem, new { @class = "form-control input-sm", onchange = "form1.submit();" })
                            </div>
                        </div>
                    </div>
                    <table class="table-ecool table-ecool-Bule-SEC">
                        <thead>

                            <tr>
                                <th>
                                </th>
                                @foreach (var item in Model.BorrowTypeQty)
                                {
                                    <th>
                                        @item.TYPE_NAME
                                    </th>
                                }
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="text-center">
                                    <strong>數量</strong>
                                </td>
                                @foreach (var item in Model.BorrowTypeQty)
                                {
                                    <td class="text-center">
                                        @item.QTY
                                    </td>
                                }
                            </tr>
                            <tr>
                                <td class="text-center">
                                    <strong>比例</strong>
                                </td>
                                @foreach (var item in Model.BorrowTypeQty)
                                {
                                    <td class="text-center">
                                        @((item.Rate).ToString("P"))
                                    </td>
                                }
                            </tr>
                        </tbody>
                        <tfoot>
                            <tr class="text-right">
                                <td colspan="@(Model.BorrowTypeQty.Count+1)">
                                    <button type="button" class="colorbox btn btn-link-ez btn-xs" href='@Url.Action("TypeList", (string)ViewBag.BRE_NO,new { WhereSEYEAR =Model.WhereSEYEAR,WhereSCHOOL_NO=Model.WhereSCHOOL_NO, WhereUSER_NO=Model.WhereUSER_NO })'>more</button>
                                </td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        }

        <div style="height:50px"></div>
        <div class="row">
            <div class="col-sm-12">
                @if (Model.BorrowColumnChart != null)
                {
                    @Model.BorrowColumnChart
                }
            </div>
        </div>
        <div style="height:50px"></div>
        <div class="row">
            <div class="col-sm-12">
                @if (Model.GradeQtyCharts != null)
                {
                    @Model.GradeQtyCharts
                }
            </div>
        </div>
    </div>
}
<script>

    $(document).ready(function () {
        $(".containerEZ").attr("style","width:96.5%");
    });
</script>