﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class ADDI14EditPeopleViewModel
    {
        /// <summary>
        /// 簽到
        /// </summary>
        public string CheckIn { get; set; }

        /// <summary>
        ///學校
        /// </summary>
        [DisplayName("學校")]
        public string SCHOOL_NO { get; set; }

        /// <summary>
        ///學校簡稱
        /// </summary>
        [DisplayName("學校")]
        public string SHORT_NAME { get; set; }

        /// <summary>
        ///學號
        /// </summary>
        [DisplayName("學號")]
        public string USER_NO { get; set; }

        /// <summary>
        ///全名
        /// </summary>
        [DisplayName("全名")]
        public string NAME { get; set; }

        /// <summary>
        ///年級
        /// </summary>
        [DisplayName("年級")]
        public byte? GRADE { get; set; }

        /// <summary>
        ///班級
        /// </summary>
        [DisplayName("班級")]
        public string CLASS_NO { get; set; }

        /// <summary>
        ///座號
        /// </summary>
        [DisplayName("座號")]
        public string SEAT_NO { get; set; }

        /// <summary>
        ///卡號
        /// </summary>
        [DisplayName("卡號")]
        public string CARD_NO { get; set; }
    }
}