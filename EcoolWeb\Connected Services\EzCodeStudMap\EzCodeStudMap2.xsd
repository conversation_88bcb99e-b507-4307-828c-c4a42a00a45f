<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:tns="http://schemas.datacontract.org/2004/07/EzCodeStudMap" elementFormDefault="qualified" targetNamespace="http://schemas.datacontract.org/2004/07/EzCodeStudMap" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:complexType name="ArrayOfEzCodeStudData">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="EzCodeStudData" nillable="true" type="tns:EzCodeStudData" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfEzCodeStudData" nillable="true" type="tns:ArrayOfEzCodeStudData" />
  <xs:complexType name="EzCodeStudData">
    <xs:sequence>
      <xs:element minOccurs="0" name="cardExternalNo" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="cardInternalNo" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="eduNumber" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="studIdNumber" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="studName" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="EzCodeStudData" nillable="true" type="tns:EzCodeStudData" />
</xs:schema>