﻿@model ECOOL_APP.EF.QAT01
@{
    ViewBag.Title = "酷課雲影片-詳細資料";
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

    var Search = TempData["Search"] as EcoolWeb.Models.ADDI01IndexViewModel;

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    if (string.IsNullOrWhiteSpace(UUID) == false)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
}

<!DOCTYPE html>
<meta http-equiv="Accept-Ranges" content="bytes" />

<script src="https://cdn.plyr.io/1.8.8/plyr.js"></script>
<link rel="stylesheet" href="https://cdn.plyr.io/1.8.8/plyr.css">
<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>Details</title>
</head>
<body>
    @Html.Partial("_Title_Secondary")

    @Html.Partial("_Notice")

    <div class="Div-EZ-Pink">
        <div class="Details">
            <div class="row">
                <div class="col-md-6 col-sm-5 dl-horizontal-EZ">
                    <samp class="dt">
                        建立日期
                    </samp>
                    <samp class="dd">
                        @Html.DisplayFor(model => model.CRE_DATE, "ShortDateTime")
                    </samp>
                </div>
                <div class="col-md-6 col-sm-5 dl-horizontal-EZ">
                    <samp class="dt">
                        建立教師
                    </samp>
                    <samp class="dd">
                        @Html.DisplayFor(model => model.Q_USER_NAME)
                    </samp>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6 col-sm-12  dl-horizontal-EZ">
                    <samp class="dt">
                        影片名稱
                    </samp>
                    <samp class="dd">
                        @Html.DisplayFor(model => model.MEDIAS_NAME)
                    </samp>
                </div>
            </div>
            <div>

           
       
                @if (Path.GetExtension(Model.MEDIAS_CONTENTLINK).IndexOf("mp4") >0)
                {
                    <video poster="@Model.MEDIAS_CONTENTLINK" controls class="img-responsive">
                        <source src="@Model.MEDIAS_CONTENTLINK" type="video/mp4">
                        <object>
                            <embed src="@Model.MEDIAS_CONTENTLINK" type="application/x-shockwave-flash" allowfullscreen="false" allowscriptaccess="always" />
                        </object>
                    </video>
                    <script>plyr.setup();</script>
                }
                else if (Model.MEDIAS_CONTENTLINK.IndexOf("youtube")>0)
                {

           
                    string youtubeId =  (Model.MEDIAS_CONTENTLINK.Substring(Model.MEDIAS_CONTENTLINK.LastIndexOf("=")+1)).Trim();
                    <div data-type="youtube" data-video-id="@youtubeId"></div>
                    <script>plyr.setup();</script>
                }
                <br/>
            </div>
            <samp class="dt">
                未完成清單
            </samp>
            <div style="height:15px">
            </div>

            <div class="Div-Feedback">
                <div>
                    @{List<QAT02> NOFinishList = Model.QAT02.Where(a => a.A_STATUS == "0").OrderBy(a => a.CLASS_NO).ThenBy(a => a.SEAT_NO).ToList();}
                    <div class="row" style="margin: 0px auto">
                        @{  
                            foreach (QAT02 q2 in NOFinishList)
                          {

                            <div class="form-group">
                                <div class="col-md-4">
                                    班級：@q2.CLASS_NO
                                </div>
                                <div class="col-md-4">
                                    座號： @q2.SEAT_NO
                                </div>
                                <div class="col-md-4">
                                    姓名： @q2.NAME
                                </div>
                            </div>
                                                      
                          }
                        }
                    </div>

                </div>
            </div>
        

            <samp class="dt">
                已完成清單
            </samp>
            <div style="height:15px">
            </div>

            <div class="Div-Feedback">
                <div>
                    @{List<QAT02> FinishList = Model.QAT02.Where(a => a.A_STATUS == "1").OrderBy(a => a.CLASS_NO).ThenBy(a => a.SEAT_NO).ToList();}
                    <div class="row" style="margin: 0px auto">
                        @{
                            foreach (QAT02 q2 in FinishList)
                            {

                                <div class="form-group">
                                    <div class="col-md-4">
                                        班級：@q2.CLASS_NO
                                    </div>
                                    <div class="col-md-4">
                                        座號： @q2.SEAT_NO
                                    </div>
                                    <div class="col-md-4">
                                        姓名： @q2.NAME
                                    </div>
                                </div>

                            }
                        }
                    </div>

                </div>
            </div>
        
        </div>
    </div>



</body>
</html>
