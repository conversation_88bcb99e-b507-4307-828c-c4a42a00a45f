﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'table', 'bs', {
	border: 'Okvir',
	caption: '<PERSON>slov',
	cell: {
		menu: 'Cell',
		insertBefore: 'Insert Cell Before',
		insertAfter: 'Insert Cell After',
		deleteCell: 'Briši æelije',
		merge: 'Spoji æelije',
		mergeRight: 'Merge Right',
		mergeDown: 'Merge Down',
		splitHorizontal: 'Split Cell Horizontally',
		splitVertical: 'Split Cell Vertically',
		title: 'Cell Properties',
		cellType: 'Cell Type',
		rowSpan: 'Rows Span',
		colSpan: 'Columns Span',
		wordWrap: 'Word Wrap',
		hAlign: 'Horizontal Alignment',
		vAlign: 'Vertical Alignment',
		alignBaseline: 'Baseline',
		bgColor: 'Background Color',
		borderColor: 'Border Color',
		data: 'Data',
		header: 'Header',
		yes: 'Yes',
		no: 'No',
		invalidWidth: 'Cell width must be a number.',
		invalidHeight: 'Cell height must be a number.',
		invalidRowSpan: 'Rows span must be a whole number.',
		invalidColSpan: 'Columns span must be a whole number.',
		chooseColor: 'Choose'
	},
	cellPad: 'Uvod æelija',
	cellSpace: 'Razmak æelija',
	column: {
		menu: 'Column',
		insertBefore: 'Insert Column Before',
		insertAfter: 'Insert Column After',
		deleteColumn: 'Briši kolone'
	},
	columns: 'Kolona',
	deleteTable: 'Delete Table', // MISSING
	headers: 'Headers', // MISSING
	headersBoth: 'Both', // MISSING
	headersColumn: 'First column', // MISSING
	headersNone: 'None',
	headersRow: 'First Row', // MISSING
	invalidBorder: 'Border size must be a number.', // MISSING
	invalidCellPadding: 'Cell padding must be a positive number.', // MISSING
	invalidCellSpacing: 'Cell spacing must be a positive number.', // MISSING
	invalidCols: 'Number of columns must be a number greater than 0.', // MISSING
	invalidHeight: 'Table height must be a number.', // MISSING
	invalidRows: 'Number of rows must be a number greater than 0.', // MISSING
	invalidWidth: 'Table width must be a number.', // MISSING
	menu: 'Svojstva tabele',
	row: {
		menu: 'Row',
		insertBefore: 'Insert Row Before',
		insertAfter: 'Insert Row After',
		deleteRow: 'Briši redove'
	},
	rows: 'Redova',
	summary: 'Summary', // MISSING
	title: 'Svojstva tabele',
	toolbar: 'Tabela',
	widthPc: 'posto',
	widthPx: 'piksela',
	widthUnit: 'width unit' // MISSING
} );
