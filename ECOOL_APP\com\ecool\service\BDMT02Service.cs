﻿using ECOOL_APP.EF;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.Mvc;

namespace ECOOL_APP.com.ecool.service
{
    public class BDMT02Service
    {
        /// <summary>
        /// 其他選項Val
        /// </summary>
        static public string OtherVal = "Other";

        static public bool GetDataListShow(string wBRE_NO, string wDATA_CODE, string wDATA_TYPE, string wSCHOOL_NO, ref ECOOL_DEVEntities db)
        {
            bool ShowYN = true;
            var TempBDMT02Count = db.BDMT02.Where(a => a.BRE_NO == wBRE_NO && a.DATA_CODE == wDATA_CODE && a.SCHOOL_NO == wSCHOOL_NO && a.Close_YN == "Y").Count();
            if (TempBDMT02Count > 0)
            {
                ShowYN = false;
            }
            if (string.IsNullOrWhiteSpace(wSCHOOL_NO)) {

                ShowYN = false;

            }
            return ShowYN;
        }

        static public List<BDMT02_REF> GetRefDataList(string wBRE_NO, string wDATA_CODE, string wDATA_TYPE, string wSCHOOL_NO, ref ECOOL_DEVEntities db)
        {
            var TempBDMT02_REF = db.BDMT02_REF.Where(a => a.BRE_NO == wBRE_NO && a.DATA_CODE == wDATA_CODE && a.SCHOOL_NO == wSCHOOL_NO && a.DEL_YN == "N");

            if (string.IsNullOrWhiteSpace(wDATA_TYPE) == false)
            {
                TempBDMT02_REF = TempBDMT02_REF.Where(a => a.DATA_TYPE == wDATA_TYPE);
            }

            List<BDMT02_REF> aBDMT02_REF = TempBDMT02_REF.OrderBy(a => a.ITEM_NO).ToList();

            if (aBDMT02_REF.Count == 0)
            {
                TempBDMT02_REF = db.BDMT02_REF.Where(a => a.BRE_NO == wBRE_NO && a.DATA_CODE == wDATA_CODE && a.SCHOOL_NO == "ALL");

                if (string.IsNullOrWhiteSpace(wDATA_TYPE) == false)
                {
                    TempBDMT02_REF = TempBDMT02_REF.Where(a => a.DATA_TYPE == wDATA_TYPE);
                }

                aBDMT02_REF = TempBDMT02_REF.OrderBy(a => a.ITEM_NO).ToList();
            }

            return aBDMT02_REF;
        }

        static public BDMT02_REF GetRefFirstData(string wBRE_NO, string wDATA_CODE, string wDATA_TYPE, string wSCHOOL_NO, ref ECOOL_DEVEntities db)
        {
            BDMT02_REF aBDMT02_REF = db.BDMT02_REF.Where(a => a.BRE_NO == wBRE_NO && a.DATA_CODE == wDATA_CODE && a.DATA_TYPE == wDATA_TYPE && a.SCHOOL_NO == wSCHOOL_NO)
                .FirstOrDefault();

            if (aBDMT02_REF == null)
            {
                aBDMT02_REF = db.BDMT02_REF.Where(a => a.BRE_NO == wBRE_NO && a.DATA_CODE == wDATA_CODE && a.DATA_TYPE == wDATA_TYPE && a.SCHOOL_NO == "ALL").OrderBy(a => a.ITEM_NO).FirstOrDefault();
            }

            return aBDMT02_REF;
        }

        static public string GetRefFirstVal(string wBRE_NO, string wDATA_CODE, string wDATA_TYPE, string wSCHOOL_NO, ref ECOOL_DEVEntities db)
        {
            BDMT02_REF aBDMT02_REF = GetRefFirstData(wBRE_NO, wDATA_CODE, wDATA_TYPE, wSCHOOL_NO, ref db);

            if (aBDMT02_REF != null)
            {
                return aBDMT02_REF.CONTENT_VAL;
            }
            else
            {
                return null;
            }
        }

        static public string GetRefFirstTXT(string wBRE_NO, string wDATA_CODE, string wDATA_TYPE, string wSCHOOL_NO, ref ECOOL_DEVEntities db)
        {
            BDMT02_REF aBDMT02_REF = GetRefFirstData(wBRE_NO, wDATA_CODE, wDATA_TYPE, wSCHOOL_NO, ref db);

            if (aBDMT02_REF != null)
            {
                return aBDMT02_REF.CONTENT_TXT;
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// 抓取BDMT02_REF 資料產生下拉式選單資料
        /// </summary>
        /// <param name="wBRE_NO"></param>
        /// <param name="wDATA_CODE"></param>
        /// <param name="wDATA_TYPE"></param>
        /// <param name="wSCHOOL_NO"></param>
        /// <param name="defaultSelectedVal">預選值</param>
        /// <param name="appOtherOptionLabel">是否其他選項</param>
        /// <param name="OtherOptionLabel">「其他選項」Text值</param>
        /// <param name="appEndOptionLabel">是否加入預設空白選項</param>
        /// <param name="EndoptionLabel">如果appendOptionLabel為true,optionLabel為第一個項目要顯示的文字,如果沒有指定則顯示[請選擇].</param>
        /// <param name="db"></param>
        /// <returns></returns>
        static public List<SelectListItem> GetRefSelectListItem(string wBRE_NO, string wDATA_CODE, string wDATA_TYPE, string wSCHOOL_NO
            , string defaultSelectedVal, bool appOtherOptionLabel, string OtherOptionLabel, bool appEndOptionLabel, string EndoptionLabel
            , ref ECOOL_DEVEntities db)
        {
            List<BDMT02_REF> TempData = GetRefDataList(wBRE_NO, wDATA_CODE, wDATA_TYPE, wSCHOOL_NO, ref db);

            List<SelectListItem> TempSelectListItem = new List<SelectListItem>();

            //是否加入預設空白選項
            if (appEndOptionLabel)
            {
                bool EndSelected = false;

                if (string.IsNullOrWhiteSpace(EndoptionLabel))
                {
                    EndoptionLabel = "請選擇";
                }

                if (string.IsNullOrWhiteSpace(defaultSelectedVal))
                {
                    EndSelected = true;
                }

                TempSelectListItem.Add(new SelectListItem { Text = EndoptionLabel, Value = "", Selected = EndSelected });
            }

            List<SelectListItem> DataList = TempData.Select(x => new SelectListItem { Text = x.CONTENT_TXT, Value = x.CONTENT_VAL, Selected = x.CONTENT_VAL == defaultSelectedVal }).ToList();

            TempSelectListItem.AddRange(DataList);

            //是否其他選項
            if (appOtherOptionLabel)
            {
                bool OtherSelected = false;

                if (string.IsNullOrWhiteSpace(OtherOptionLabel))
                {
                    OtherOptionLabel = "其他";
                }

                if (TempSelectListItem.Where(a => a.Selected == true).Any() == false && string.IsNullOrWhiteSpace(defaultSelectedVal) == false)
                {
                    OtherSelected = true;
                }

                TempSelectListItem.Add(new SelectListItem { Text = OtherOptionLabel, Value = "Other", Selected = OtherSelected });
            }

            return TempSelectListItem;
        }
    }
}