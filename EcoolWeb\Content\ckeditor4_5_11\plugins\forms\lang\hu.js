﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'forms', 'hu', {
	button: {
		title: 'Gomb tulajdon<PERSON>gai',
		text: '<PERSON>z<PERSON><PERSON>g (Érték)',
		type: 'Tí<PERSON>',
		typeBtn: 'Gomb',
		typeSbm: 'Küld<PERSON>',
		typeRst: 'Alaphelyzet'
	},
	checkboxAndRadio: {
		checkboxTitle: 'Jelölőnégyzet tulajdonságai',
		radioTitle: 'Választógomb tulajdonságai',
		value: '<PERSON>rt<PERSON><PERSON>',
		selected: 'Kiv<PERSON><PERSON>ztott',
		required: 'Required' // MISSING
	},
	form: {
		title: 'Űrlap tulajdonságai',
		menu: 'Űrlap tulajdonságai',
		action: 'Adatfeldolgozást végző hivatkozás',
		method: '<PERSON>tküld<PERSON> módja',
		encoding: '<PERSON>ó<PERSON><PERSON><PERSON>'
	},
	hidden: {
		title: '<PERSON>jtett me<PERSON> tula<PERSON>',
		name: '<PERSON><PERSON><PERSON>',
		value: '<PERSON>rt<PERSON><PERSON>'
	},
	select: {
		title: '<PERSON>g<PERSON>rd<PERSON><PERSON><PERSON> lista tulajdons<PERSON>gai',
		selectInfo: 'Alaptulajdonságok',
		opAvail: 'Elérhető opciók',
		value: 'Érték',
		size: 'Méret',
		lines: 'sor',
		chkMulti: 'több sor is kiválasztható',
		required: 'Required', // MISSING
		opText: 'Szöveg',
		opValue: 'Érték',
		btnAdd: 'Hozzáad',
		btnModify: 'Módosít',
		btnUp: 'Fel',
		btnDown: 'Le',
		btnSetValue: 'Legyen az alapértelmezett érték',
		btnDelete: 'Töröl'
	},
	textarea: {
		title: 'Szövegterület tulajdonságai',
		cols: 'Karakterek száma egy sorban',
		rows: 'Sorok száma'
	},
	textfield: {
		title: 'Szövegmező tulajdonságai',
		name: 'Név',
		value: 'Érték',
		charWidth: 'Megjelenített karakterek száma',
		maxChars: 'Maximális karakterszám',
		required: 'Required', // MISSING
		type: 'Típus',
		typeText: 'Szöveg',
		typePass: 'Jelszó',
		typeEmail: 'Ímél',
		typeSearch: 'Keresés',
		typeTel: 'Telefonszám',
		typeUrl: 'URL'
	}
} );
