(function(factory){if (typeof define === 'function' && define.amd){define(['jquery'], factory);} else if (typeof module === 'object' && typeof module.exports === 'object'){module.exports = factory(require('jquery'));} else {factory(jQuery);}}(function(jQuery){

/*! Widget: vertical-group (BETA) - updated 12/13/2017 (v2.29.1) */
!function(h){"use strict";var G=h.tablesorter,f=G.css;function g(r){r.removeClass(f.verticalGroupHide+" "+f.verticalGroupShow)}function C(r,e,a){e.parent().removeClass(r.zebra[(a+1)%2]).addClass(r.zebra[a%2])}function r(r,e,a){var o=-1,i=r.tBodies[0].rows,t=G.hasWidget(r,"zebra"),l=[],s=[];if(!a.vertical_group_lock){if(a.vertical_group_lock=!0,""===(l=h.map(e.$headerIndexed,function(r){return r.hasClass(f.verticalGroupHeader)?1:""})).join(""))return g(h(i).find("."+f.verticalGroupHide+",."+f.verticalGroupShow)),void(a.vertical_group_lock=!1);for(var c=0;c<i.length;c++)for(var u=!1,v=0;v<e.columns;v++)if(l[v]&&i[c].cells[v]){var d=h(i[c].cells[v]),p=G.isValueInArray(v,e.sortList),n=d.html();p<0?g(d):u||n!==s[v]?0===p&&(u=!0,d.hasClass(f.verticalGroupShow)||d.addClass(f.verticalGroupShow),d.removeClass(f.verticalGroupHide),t&&C(a,d,p?o:++o)):(d.hasClass(f.verticalGroupHide)||d.addClass(f.verticalGroupHide),t&&C(a,d,o),d.removeClass(f.verticalGroupShow)),s[v]=n}else o++;a.vertical_group_lock=!1}}h.extend(G.css,{verticalGroupHeader:"tablesorter-vertical-group",verticalGroupHide:"tablesorter-vertical-group-hide",verticalGroupShow:"tablesorter-vertical-group-show"}),G.addWidget({id:"vertical-group",priority:99,init:r,format:r})}(jQuery);return jQuery;}));
