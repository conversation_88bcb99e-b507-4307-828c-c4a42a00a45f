﻿@model ADDI11EditViewModel
@using EcoolWeb.Util;
@using ECOOL_APP.com.ecool.service

@{
    /**/

    ViewBag.Title = "跑步新增結果";
}

@{
    Html.RenderAction("_RunMenu", new { NowAction = "Edit" });
}


@Html.Partial("_Title_Secondary")

<div class="table-responsive">
<div class="alert alert-success">班級：@(Model.SearchCLASS_NO)，日期：@(Model.RunDate?.ToString("yyyy/MM/dd")) 的跑步新增結果如下：</div>
    <div class="css-table">
        
        @if (Model.People != null)
        {
            foreach (var item in Model.People)
            {
                @Html.Action("_People", item)
            }
        }
    </div>
</div>