.list-icons-box {
    position: relative;
    padding: 2rem 1rem 1rem 1rem;
    border: 1px solid #eee;

    li {
        padding: .5rem 0 .5rem 35px;

        img {
            margin-left: -35px;
            margin-right: 5px;
        }
    }

    &::before {
        content: attr(data-title);
        position: absolute;
        top: -1rem;
        display: block;
        padding: 0 1rem;
        background-color: #fff;
        font-size: 1.7rem;
    }
}


.redeem-block {
    padding: 1rem 2rem;
    display: block;
    width: 90%;
    background-color: rgba(243, 200, 176, 0.2);
    margin: 0px auto;

    &-havetitle {
        margin-top: -30px;
        padding-top: calc(30px + 1rem);
    }
}

.redeem-box {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    margin: 2rem 0 1rem 0;
    background-color: #fff;

    img {
        display: block;
        width: 100%;
        max-width: 200px;
        max-height: 200px;
        margin: 0 auto;
    }

    &-bidding {
        position: relative;
        background-color: #ffe0e0;
        border: 4px #f1c2a8 double;
        box-shadow: 0 0 6px #ab8a76;

        &::after {
            position: absolute;
            content: url('../images/icon-redeem-bidding.png');
            top: 3px;
            right: 3px;
            opacity: 0.5;
        }
    }
}

.redeem-row {
    display: flex;
    flex-wrap: wrap;

    >div {
        flex: 0 1 50%;

        @media screen and (max-width: 768px) {
            flex: 1 1 100%;
        }
    }
}