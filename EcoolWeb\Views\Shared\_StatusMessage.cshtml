﻿

@if (string.IsNullOrWhiteSpace((string)TempData["StatusMessageCenter"]) == false)
{
    string HtmlMsg = TempData["StatusMessageCenter"].ToString();

    <div class="text-center">
        <div class="alert alert-dismissible alert-danger">
            <button class="close" type="button" data-dismiss="alert">×</button>

            <strong style="font-size:25px">@Html.Raw(HttpUtility.HtmlDecode(HtmlMsg))</strong>

        </div>
    </div>
}




