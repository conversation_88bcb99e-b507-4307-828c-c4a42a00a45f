(function(factory){if (typeof define === 'function' && define.amd){define(['jquery'], factory);} else if (typeof module === 'object' && typeof module.exports === 'object'){module.exports = factory(require('jquery'));} else {factory(jQuery);}}(function(jQuery){

/*! Parser: metric */
!function(h){"use strict";var f={"Y|Yotta|yotta":[1e24,Math.pow(1024,8)],"Z|Zetta|zetta":[1e21,Math.pow(1024,7)],"E|Exa|exa":[1e18,Math.pow(1024,6)],"P|Peta|peta":[1e15,Math.pow(1024,5)],"T|Tera|tera":[1e12,Math.pow(1024,4)],"G|Giga|giga":[1e9,Math.pow(1024,3)],"M|Mega|mega":[1e6,<PERSON>.pow(1024,2)],"k|Kilo|kilo":[1e3,1024],"h|hecto":[100,100],"da|deka":[10,10],"d|deci":[.1,.1],"c|centi":[.01,.01],"m|milli":[.001,.001],"µ|micro":[1e-6,1e-6],"n|nano":[1e-9,1e-9],"p|pico":[1e-12,1e-12],"f|femto":[1e-15,1e-15],"a|atto":[1e-18,1e-18],"z|zepto":[1e-21,1e-21],"y|yocto":[1e-24,1e-24]},l=/^[b|bit|byte|o|octet]/i;h.tablesorter.addParser({id:"metric",is:function(){return!1},format:function(e,t,a,o){var r,i,c,n,m="m|meter",p=h.tablesorter.formatFloat(e.replace(/[^\w,. \-()]/g,""),t),d=t.config.$headerIndexed[o],s=d.data("metric");if(s||(r=(d.attr("data-metric-name")||m).split("|"),c=d.attr("data-metric-name-full")||"",n=d.attr("data-metric-name-abbr")||"",s=[c||r[1]||r[0].substring(1),n||r[0]],i=l.test(s.join("")),s[2]=new RegExp("(\\d+)(\\s+)?([Zz]etta|[Ee]xa|[Pp]eta|[Tt]era|[Gg]iga|[Mm]ega|kilo|hecto|deka|deci|centi|milli|micro|nano|pico|femto|atto|zepto|yocto)("+((""===c?"":c+"|"+n)||(i?s[0].toLowerCase()+"|"+s[0].toUpperCase():s[0])+"|"+(i?s[1].toLowerCase()+"|"+s[1].toUpperCase():s[1]))+")"),s[3]=new RegExp("(\\d+)(\\s+)?(Z|E|P|T|G|M|k|h|da|d|c|m|µ|n|p|f|a|z|y)("+(n||(i?s[1].toLowerCase()+"|"+s[1].toUpperCase():s[1]))+")"),d.data("metric",s)),r=e.match(s[2])||e.match(s[3]))for(m in f)if(r[3].match(m))return i=l.test(r[4])?1:0,p*f[m][i];return p},type:"numeric"})}(jQuery);return jQuery;}));
