/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/General/Regular/MiscMathSymbolsA.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.STIXGeneral,{10176:[584,0,685,50,634],10177:[811,127,1145,35,1110],10178:[662,0,693,52,641],10179:[529,27,685,60,625],10180:[529,27,685,61,626],10181:[702,198,455,55,400],10182:[702,198,455,55,400],10183:[536,29,620,31,589],10184:[533,25,966,60,906],10185:[533,25,966,60,906],10187:[662,156,838,0,799],10188:[806,213,325,20,325],10189:[662,156,838,0,799],10192:[744,242,1064,39,1025],10193:[536,29,620,31,589],10194:[536,31,620,48,572],10195:[584,0,685,50,634],10196:[584,0,685,50,634],10197:[582,80,1019,40,965],10198:[582,80,1019,54,979],10199:[582,80,1228,40,1188],10200:[718,213,866,50,816],10201:[718,213,866,50,816],10202:[662,0,1376,64,1312],10203:[662,0,1376,64,1312],10204:[403,-103,849,50,799],10205:[450,-57,1574,55,1519],10206:[450,-57,1574,55,1519],10207:[693,187,502,101,401],10208:[795,289,790,45,745],10209:[589,87,764,45,719],10210:[589,87,803,45,758],10211:[589,87,803,45,758],10212:[662,158,1182,45,1137],10213:[662,158,1182,45,1137],10214:[717,213,504,188,482],10215:[717,213,504,22,316],10218:[719,213,610,73,545],10219:[719,213,610,65,537],10220:[719,213,488,178,466],10221:[719,213,488,22,310]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/Regular/MiscMathSymbolsA.js");
