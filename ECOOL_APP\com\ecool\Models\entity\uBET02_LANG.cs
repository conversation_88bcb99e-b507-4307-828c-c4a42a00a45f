﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Web.Mvc;

namespace ECOOL_APP.com.ecool.Models.entity
{
    public class uBET02_LANG
    {

        ///Summary
        ///
        ///Summary
        [Display(Name = "BET02DTO_BULLET_ID", ResourceType = typeof(PageResource))]
        [StringLength(100)]
        public string BULLET_ID { get; set; }

        ///Summary
        ///
        ///Summary
        [Required]
        public string LANGUAGE_ID { get; set; }

        ///Summary
        ///
        ///Summary
        [Display(Name = "BET02DTO_SUBJECT", ResourceType = typeof(PageResource))]
        [StringLength(400)]
        [Required]
        public string SUBJECT { get; set; }

        ///Summary
        ///
        ///Summary
        [Display(Name = "BET02DTO_CONTENT_TXT", ResourceType = typeof(PageResource))]
        [Required]
        [UIHint("Html")]
        [AllowHtml]
        public string CONTENT_TXT { get; set; }

        ///Summary
        ///
        ///Summary
        [Display(Name = "CRE_PERSON", ResourceType = typeof(PageResource))]
        [StringLength(100)]
        public string CRE_PERSON { get; set; }

        ///Summary
        ///
        ///Summary
        [Display(Name = "CRE_DATE", ResourceType = typeof(PageResource))]
        public DateTime? CRE_DATE { get; set; }

        ///Summary
        ///
        ///Summary
        [Display(Name = "CHG_PERSON", ResourceType = typeof(PageResource))]
        [StringLength(100)]
        public string CHG_PERSON { get; set; }

        ///Summary
        ///
        ///Summary
        [Display(Name = "CHG_DATE", ResourceType = typeof(PageResource))]
        public DateTime? CHG_DATE { get; set; }

    }

}
