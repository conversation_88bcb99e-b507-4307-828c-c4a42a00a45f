(function(factory){if (typeof define === 'function' && define.amd){define(['jquery'], factory);} else if (typeof module === 'object' && typeof module.exports === 'object'){module.exports = factory(require('jquery'));} else {factory(jQuery);}}(function(jQuery){

/*! Widget: columns - updated 5/24/2017 (v2.28.11) */
!function(b){"use strict";var v=b.tablesorter||{};v.addWidget({id:"columns",priority:65,options:{columns:["primary","secondary","tertiary"]},format:function(e,r,o){var t,s,n,i,a,d,l,c,h=r.$table,f=r.$tbodies,m=r.sortList,y=m.length,u=o&&o.columns||["primary","secondary","tertiary"],p=u.length-1;for(l=u.join(" "),s=0;s<f.length;s++)(n=(t=v.processTbody(e,f.eq(s),!0)).children("tr")).each(function(){if(a=b(this),"none"!==this.style.display&&(d=a.children().removeClass(l),m&&m[0]&&(d.eq(m[0][0]).addClass(u[0]),1<y)))for(c=1;c<y;c++)d.eq(m[c][0]).addClass(u[c]||u[p])}),v.processTbody(e,t,!1);if(i=!1!==o.columns_thead?["thead tr"]:[],!1!==o.columns_tfoot&&i.push("tfoot tr"),i.length&&(n=h.find(i.join(",")).children().removeClass(l),y))for(c=0;c<y;c++)n.filter('[data-column="'+m[c][0]+'"]').addClass(u[c]||u[p])},remove:function(e,r,o){var t,s,n=r.$tbodies,i=(o.columns||["primary","secondary","tertiary"]).join(" ");for(r.$headers.removeClass(i),r.$table.children("tfoot").children("tr").children("th, td").removeClass(i),t=0;t<n.length;t++)(s=v.processTbody(e,n.eq(t),!0)).children("tr").each(function(){b(this).children().removeClass(i)}),v.processTbody(e,s,!1)}})}(jQuery);return jQuery;}));
