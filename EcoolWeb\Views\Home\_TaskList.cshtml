﻿@{
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
}
<img src="~/Content/img/web-bar2-revise-12.png" style="width:100%" class="imgEZ" alt="Responsive image" />
<div class="Div-EZ-task text-center">
    <div class="Details text-center">
        <div style="height:15px"></div>
        <div class="row">
            <div class="col-md-6" style="height:65px;">
                <a href='@Url.Action("VerifyList", "ADDI01",new { whereWritingStatus="0,2"})'>
                    <img src="~/Content/img/web-teacher-03n.png" class="imgEZ" alt="Responsive image" style="max-width:200px" title="待批閱線上投稿" />
                    &nbsp;<span class="lnkFont2">@(ViewBag.ADDT01Qty??"0")</span>
                </a>
            </div>

            <div class="col-md-6" style="height:65px;">
                @if (ViewBag.ADDTHrmt25!=null&&ViewBag.ADDTHrmt25 > 0)
                {
                    <a href='@Url.Action("../ADDT/ADDTList_CheckPending", "ADDT")'>
                        <img src="~/Content/img/web-teacher-02n.png" class="imgEZ" alt="Responsive image" style="max-width:200px;" title="待批閱閱讀認證" />
                        &nbsp;<span class="lnkFont2">@(ViewBag.ADDT06Cnt ?? "0")</span>
                    </a>
                }
                else
                {

                    string class_NOstr = "";
                    if (user?.TEACH_CLASS_NO!=null) {

                        class_NOstr = user?.TEACH_CLASS_NO;
                        }
                    <a href='@Url.Action("../ADDT/ADDTList_CheckPending", "ADDT",new { whereCLASS_NO=class_NOstr})'>
                        <img src="~/Content/img/web-teacher-02n.png" class="imgEZ" alt="Responsive image" style="max-width:200px;" title="待批閱閱讀認證" />
                        &nbsp;<span class="lnkFont2">@(ViewBag.ADDT06Cnt??"0")</span>
                    </a>
                }

            </div>

            @*<div class="row">
            <div class="col-md-6" style="height:65px;">
                <a href='@Url.Action("../ZZZI09/INDEX", "ZZZI09")'>
                    <img src="~/Content/img/web-teacher-04.png" class="imgEZ" alt="Responsive image"  style="max-width:200px"  title="學習成果匯出" />
                    &nbsp;<span class="lnkFont2">0</span>
                </a>
            </div>
            @if (user.USER_TYPE == "A")
            {
                <div class="col-md-6" style="height:65px;">
                    <a href='@Url.Action("../ADDI03/QUERY2", "ZZZI06",new { UrlddlBooKGrade = "ALL" })'>
                        <img src="~/Content/img/web-teacher-05.png" class="imgEZ" alt="Responsive image"  style="max-width:200px"  title="閱讀護照獎狀待頒發" />
                        &nbsp;<span class="lnkFont2">0</span>
                    </a>
                </div>
            }
        </div>*@
            <div class="col-md-6" style="height:65px;">
                <a href='@Url.Action("ArtGalleryList", "ZZZI34",new { WhereVerify = true, WhereFrom="Tasklist"})'>
                    <img src="~/Content/img/onLineArt.png" class="imgEZ" alt="Responsive image" style="max-width:200px" title="待批閱線上藝廊" />
                    &nbsp;<span class="lnkFont2">@(ViewBag.ADDT21Qty??"0")</span>
                </a>
            </div>

            @if (EcoolWeb.Models.UserProfileHelper.CheckROLE_SCHOOL_ADMIN(user, null))
            {

                <div class="col-md-6" style="height:65px;">
                    <a href='@Url.Action("../ADDT/HandleBookCreditList", "ADDT")'>
                        <img src="~/Content/img/web-teacher-10n.png" class="imgEZ" alt="Responsive image" style="max-width:200px" title="閱讀認證獎狀待頒發" />
                        &nbsp;<span class="lnkFont2">@(ViewBag.ADDT0809??"0")</span>
                    </a>
                </div>
                if (user.USER_TYPE == "A") {


                <div class="col-md-6" style="height:65px;">
                    <a href='@Url.Action("Index", "ZZZI11", new { STATUS = ECOOL_APP.com.ecool.Models.entity.uADDT16.STATUS_Val.STATUS_1 })'>
                        <img src="~/Content/img/web-teacher-08n.png" class="imgEZ" alt="Responsive image" style="max-width:200px" title="問題反應待回覆" />
                        &nbsp;<span class="lnkFont2">@(ViewBag.ZZZI11_Q?? "0")</span>
                    </a>
                </div>

                <div class="col-md-6" style="height:65px;">
                    <a href='@Url.Action("Index", "ZZZI11", new { STATUS = ECOOL_APP.com.ecool.Models.entity.uADDT16.STATUS_Val.STATUS_2 })'>
                        <img src="~/Content/img/web-teacher-09n.png" class="imgEZ" alt="Responsive image" style="max-width:200px" title="問題反應待讀取" />
                        &nbsp;<span class="lnkFont2">@(ViewBag.ZZZI11_A ?? "0") </span>
                    </a>
                </div>

                <div class="col-md-6" style="height:65px;">
                    <a href='@Url.Action("Index", "ZZZI31", new { STATUS = ECOOL_APP.com.ecool.Models.entity.uQAT16.STATUS_Val.STATUS_1 })'>
                        <img src="~/Content/img/web-teacher-18n.png" class="imgEZ" alt="Responsive image" style="max-width:200px" title="填報待回覆" />
                        &nbsp;<span class="lnkFont2">@(ViewBag.ZZZI31_Q ?? "0")</span>
                    </a>
                </div>   
                <div class="col-md-6" style="height:65px;">
                    <a href='@Url.Action("Index", "ZZZI31", new { STATUS = ECOOL_APP.com.ecool.Models.entity.uQAT16.STATUS_Val.STATUS_2 })'>
                        <img src="~/Content/img/web-teacher-19n.png" class="imgEZ" alt="Responsive image" style="max-width:200px" title="填報待讀取" />
                        &nbsp;<span class="lnkFont2">@(ViewBag.ZZZI31_A ?? "0")</span>
                    </a>
                </div>  }

                <div class="col-md-6" style="height:65px;">
                    <a href='@Url.Action("Index", "ZZZI20")'>
                        <img src="~/Content/img/ZZI19_Count.png" class="imgEZ" alt="Responsive image" style="max-width:200px" title="轉學生酷幣轉匯" />
                        &nbsp;<span class="lnkFont2">@(ViewBag.ShowTurnInCount??"0")</span>
                    </a>
                </div>
                <div class="col-md-6" style="height:65px;">
                    <a href='@Url.Action("QUERY", "ZZZI19", new { whereStatus =UserStaus.Disable})'>
                        <img src="~/Content/img/ZZI12_2Count.png" class="imgEZ" alt="Responsive image" style="max-width:200px" title="學生停用的筆數" />
                        &nbsp;<span class="lnkFont2">@(ViewBag.StudentStatuInCount??"0")</span>
                    </a>
                </div>
                <div class="col-md-6" style="height:65px;">
                    <a href='@Url.Action("../AWA004/ModifyQuery", "AWA004")'>
                        <img src="~/Content/img/web-teacher-06n.png" class="imgEZ" alt="Responsive image" style="max-width:200px" title="兌換獎品待頒發" />
                        &nbsp;<span class="lnkFont2">@(ViewBag.VAWA004??"0")</span>
                    </a>
                </div>

                <div class="col-md-6" style="height:65px;">
                    <a href='@Url.Action("../AWAT14/ModifyQuery", "AWAT14")'>
                        <img src="~/Content/img/ZZI78_5Count.png" class="imgEZ" alt="Responsive image" style="max-width:200px" title="酷幣點數升級名單" />
                        &nbsp;<span class="lnkFont2">@(ViewBag.awat15Count??"0") </span>
                    </a>
                </div>

            }
        </div>
    </div>
</div>