﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class ZZZI09ShowADDT22PHOTOViewViewModel
    {
        /// <summary>
        ///藝廊 NO.
        /// </summary>
        [DisplayName("藝廊 NO.")]
        public string ART_GALLERY_NO { get; set; }

        /// <summary>
        ///藝廊名稱
        /// </summary>
        [DisplayName("藝廊名稱")]
        public string ART_SUBJECT { get; set; }

        /// <summary>
        ///作品類別
        /// </summary>
        [DisplayName("作品類別")]
        public string WORK_TYPE { get; set; }

        /// <summary>
        ///照片 NO.
        /// </summary>
        [DisplayName("照片 NO.")]
        public string PHOTO_NO { get; set; }

        /// <summary>
        ///照片檔名
        /// </summary>
        [DisplayName("照片檔名")]
        public string PHOTO_FILE { get; set; }

        /// <summary>
        ///照片描述
        /// </summary>
        [DisplayName("主題")]
        public string PHOTO_SUBJECT { get; set; }

        /// <summary>
        ///
        /// </summary>
        [DisplayName("內容簡介")]
        public string PHOTO_DESC { get; set; }

        /// <summary>
        ///建立日期
        /// </summary>
        [DisplayName("建立日期")]
        public DateTime? CRE_DATE { get; set; }

        /// <summary>
        ///照片作者學校代碼
        /// </summary>
        [DisplayName("照片作者學校代碼")]
        public string PHOTO_SCHOOL_NO { get; set; }

        /// <summary>
        ///照片作者帳號
        /// </summary>
        [DisplayName("照片作者帳號")]
        public string PHOTO_USER_NO { get; set; }

        public string PHOTO_CLASS_NO { get; set; }
    }
}