﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'liststyle', 'lv', {
	armenian: '<PERSON>ēņu skaitļi',
	bulletedTitle: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> saraksta uzstādījumi',
	circle: 'Ap<PERSON>',
	decimal: '<PERSON><PERSON><PERSON><PERSON> (1, 2, 3, utt)',
	decimalLeadingZero: 'Decimālie ar nulli (01, 02, 03, utt)',
	disc: 'Disks',
	georgian: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> skaitļi (an, ban, gan, utt)',
	lowerAlpha: '<PERSON><PERSON> alfab<PERSON>ta (a, b, c, d, e, utt)',
	lowerGreek: '<PERSON><PERSON> grieķu (alfa, beta, gamma, utt)',
	lowerRoman: '<PERSON><PERSON> romāņu (i, ii, iii, iv, v, utt)',
	none: 'Nekas',
	notset: '<nav norādīts>',
	numberedTitle: 'N<PERSON><PERSON><PERSON>ta saraksta uzstādījumi',
	square: '<PERSON><PERSON><PERSON><PERSON>ts',
	start: 'Sākt',
	type: 'Tips',
	upperAlpha: '<PERSON><PERSON> alfabēta (A, B, C, D, E, utt)',
	upperRoman: 'Lielie romāņu (I, II, III, IV, V, utt)',
	validateStartNumber: 'Saraksta sākuma numuram jābūt veselam skaitlim'
} );
