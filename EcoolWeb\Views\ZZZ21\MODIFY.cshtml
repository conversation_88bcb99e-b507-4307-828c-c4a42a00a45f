﻿@using System.Collections;
@model IEnumerable<ADDT08>
@{
    ViewBag.Title = "閱讀認證-認證等級維護";
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
}


@section Scripts
{
    <script type="text/javascript">
        function btnSend_onclick() {
            document.ZZZ21.enctype = "multipart/form-data";
            document.ZZZ21.action = "MODIFY";
            document.ZZZ21.submit();
        }
    </script>
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@using (Html.BeginForm("MODIFY", "ZZZ21", FormMethod.Post, new { id = "ZZZ21", name = "ZZZ21" }))
{
    <img src="~/Content/img/web-bar2-revise-14.png" style="width:100%" class="img-responsive " alt="Responsive image" />
    <div class="table-responsive">
       <div class="text-center">
           <table class="table-ecool table-92Per table-hover table-ecool-reader">
               <thead>
                   <tr>
                       <td align="center">編號</td>
                       <td align="center">等級</td>
                   </tr>
               </thead>
               <tbody>
                   @foreach (var item in Model)
                    {
                        string LEVEL_DESC = string.Empty;
                        if (item.SCHOOL_NO != user.SCHOOL_NO)
                        {
                            switch (item.LEVEL_ID)
                            {
                                case 1:
                                    LEVEL_DESC = "第一級";
                                    break;
                                case 2:
                                    LEVEL_DESC = "第二級";
                                    break;
                                case 3:
                                    LEVEL_DESC = "第三級";
                                    break;
                                case 4:
                                    LEVEL_DESC = "第四級";
                                    break;
                                case 5:
                                    LEVEL_DESC = "第五級";
                                    break;
                                case 6:
                                    LEVEL_DESC = "第六級";
                                    break;
                                case 7:
                                    LEVEL_DESC = "第七級";
                                    break;
                                case 8:
                                    LEVEL_DESC = "第八級";
                                    break;
                                case 9:
                                    LEVEL_DESC = "第九級";
                                    break;
                                case 10:
                                    LEVEL_DESC = "第十級";
                                    break;
                                case 11:
                                    LEVEL_DESC = "第十一級";
                                    break;
                                default:
                                    LEVEL_DESC = item.LEVEL_DESC;
                                    break;
                            }
                        }
                        else
                        {
                            LEVEL_DESC = item.LEVEL_DESC;
                        }

                       <tr>
                           <td align="center" style="width:10px">
                               @Html.Hidden("txtLeveL_ID_" + item.LEVEL_ID, item.LEVEL_ID, new { @class = "form-control input-sm", disabled = "disabled" })
                               @Html.Hidden("hidLeveL_ID_" + item.LEVEL_ID, item.LEVEL_ID, new { @class = "form-control input-sm" })
                               <label>@item.LEVEL_ID</label>
                               
                           </td>
                           <td align="center">
                               @Html.TextBox("txtLEVEL_DESC_" + item.LEVEL_ID, LEVEL_DESC, new { @class = "form-control input-sm" })
                           </td>
                       </tr>
                   }
               </tbody>
           </table>
        </div>
    </div>
    <div class="text-center">
        <button class="btn btn-default" type="button" id="btnSend" name="btnSend" onclick="btnSend_onclick();">
            確定送出
        </button>
    </div>
}

