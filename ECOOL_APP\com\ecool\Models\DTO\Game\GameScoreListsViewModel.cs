﻿using MvcPaging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.Mvc;

namespace ECOOL_APP.EF
{
    public class GameScoreListsViewModel
    {
        /// <summary>
        ///  原活動列表的查詢條件
        /// </summary>
        public GameSearchViewModel Search { get; set; }

        /// <summary>
        /// 成積類型
        /// </summary>
        public string SCORE_TYPE { get; set; }

        public string WhereSCHOOL_NO { get; set; }

        /// <summary>
        /// 查詢姓名
        /// </summary>
        public string WhereNAME { get; set; }

        /// <summary>
        /// 查詢班級
        /// </summary>
        public string WhereCLASS_NO { get; set; }

        /// <summary>
        /// 查詢年級
        /// </summary>
        public string WhereYEAR { get; set; }

        /// <summary>
        /// 學號
        /// </summary>
        public string WhereUSER_NO { get; set; }

        /// <summary>
        /// 排序欄位
        /// </summary>
        public string OrdercColumn { get; set; }

        /// <summary>
        /// Desc or ASC
        /// </summary>
        public string SyntaxName { get; set; }

        /// <summary>
        /// 頁次
        /// </summary>
        public int Page { get; set; }

        /// <summary>
        /// 一頁筆數 預設筆數
        /// </summary>
        public int PageSize { get; set; }

        /// <summary>
        /// 活動資訊
        /// </summary>
        public ADDT26 GameInfo { get; set; }

        /// <summary>
        /// 題目
        /// </summary>
        public List<ADDT26_Q> Q_DATA { get; set; }

        /// <summary>
        /// 查詢結果
        /// </summary>
        public IPagedList<GameScoreListPersonsViewModel> ListData;

        /// <summary>
        /// 建構式 預設值
        /// </summary>
        public GameScoreListsViewModel()
        {
            PageSize = 100;
        }

        //SCORE_TYPE
        public static class ScoreTypeVal
        {
            /// <summary>
            /// 全部成積
            /// </summary>
            public static string AllScore = "AllScore";

            /// <summary>
            /// 最優成績
            /// </summary>
            public static string MaxScore = "MaxScore";

            /// <summary>
            /// 滿分成績
            /// </summary>
            public static string PassScore = "PassScore";

            public static List<SelectListItem> GetSelectListItem(string SelectedVal)
            {
                List<SelectListItem> SelectItem = new List<SelectListItem>();

                SelectItem.Add(new SelectListItem() { Text = GetDesc(GameScoreListsViewModel.ScoreTypeVal.AllScore), Value = GameScoreListsViewModel.ScoreTypeVal.AllScore, Selected = SelectedVal == GameScoreListsViewModel.ScoreTypeVal.AllScore });
                SelectItem.Add(new SelectListItem() { Text = GetDesc(GameScoreListsViewModel.ScoreTypeVal.MaxScore), Value = GameScoreListsViewModel.ScoreTypeVal.MaxScore, Selected = SelectedVal == GameScoreListsViewModel.ScoreTypeVal.MaxScore });
                SelectItem.Add(new SelectListItem() { Text = GetDesc(GameScoreListsViewModel.ScoreTypeVal.PassScore), Value = GameScoreListsViewModel.ScoreTypeVal.PassScore, Selected = SelectedVal == GameScoreListsViewModel.ScoreTypeVal.PassScore });

                return SelectItem;
            }

            public static string GetDesc(string Val)
            {
                if (Val == AllScore)
                {
                    return "全部成積";
                }
                else if (Val == MaxScore)
                {
                    return "最優成績";
                }
                else if (Val == PassScore)
                {
                    return "滿分成績";
                }
                else
                {
                    return "";
                }
            }
        }
    }
}