﻿@model ZZZI34WorkIndexViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode || (Model?.WhereIsColorboxForUser ?? false))
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

<style type="text/css">

    .modal-body-scroll {
        /* 100% = dialog height, 120px = header + footer */
        max-height: calc(100% - 120px);
        overflow-y: scroll;
    }

    .wrap {
        display: inline-block;
        position: relative;
    }

    .overlap {
        display: none
    }

    .poptooltip .overlap {
        display: block;
        position: absolute;
        height: 100%;
        width: 100%;
        z-index: 1000;
    }
</style>

@using (Html.BeginForm("Index", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1" }))
{
    <div id="check"></div>
    @Html.AntiForgeryToken()
    <div id="WorkPageContent">
        @Html.Action("_WorkPageContent", (string)ViewBag.BRE_NO)
    </div>

    <!-- Modal -->
    <div class="modal fade" id="LikeNickNameModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="myModalLabel">推薦-帳號未登入請先登入或是訪客，請輸入訪客姓名</h4>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        @Html.Label("訪客姓名", htmlAttributes: new { @class = "col-md-3 control-label" })
                        <div class="col-md-9">
                            @Html.Hidden("PHOTO_NO")
                            @Html.Hidden("LikeCount")
                            @Html.Editor("NICK_NAME", new { htmlAttributes = new { @class = "form-control input-md", @placeholder = "請輸訪客姓名" } })
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" onclick="ModalSHARE_show()">確定</button>
                    <button type="button" class="btn btn-default" data-dismiss="modal">放棄</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal -->
    <div class="modal fade" id="LikeListModal" tabindex="-1" role="dialog" aria-labelledby="LikeListModal">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title">按讚人員</h4>
                </div>
                <div class="modal-body modal-body-scroll" style="max-height:300px">
                    <div id="LikeListPageContent">
                        @Html.Action("_LikeListView", (string)ViewBag.BRE_NO)
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">關閉</button>
                </div>
            </div>
        </div>
    </div>
}

@section scripts{
    <script type="text/javascript">

        var targetFormID = '#form1';

        $('[rel="tooltip"]').tooltip({
            animated: 'fade',
            placement: 'bottom',
        });

        function FunPageProcTwo(page) {
            if ($(targetFormID).size() > 0) {
                $('#@Html.IdFor(m=>m.WorkSearch.Page)').val(page)
                funAjax()
            }
        };

        function doSearch(ColName, whereValue) {
            $("#" + ColName).val(whereValue);
            FunPageProc(1)
        }

        function GoBack() {
            $('#@Html.IdFor(m => m.Search.WhereART_GALLERY_NO)').val('')

            if ($('#@Html.IdFor(m => m.Search.WhereMyWork)').val() == 'true') {
                 $(targetFormID).attr("action", "@Url.Action("ArtGalleryList", (string)ViewBag.BRE_NO, new { WhereMyWork = true })")
                $(targetFormID).submit();
            }
            else {
                $(targetFormID).attr("action", "@Url.Action("ArtGalleryList", (string)ViewBag.BRE_NO)")
                $(targetFormID).submit();
            }
        }

        function Edit_show(Value) {
                $('#@Html.IdFor(m => m.Search.WhereART_GALLERY_NO)').val(Value)
                $(targetFormID).attr("action", "@Url.Action("Edit", (string)ViewBag.BRE_NO, new { SaveType = ZZZI34EditViewModel.SaveTypeVal.Save })")
                $(targetFormID).submit();
        }

        function ModalSHARE_show()
        {

            var NICK_NAME = $('#NICK_NAME').val()
            var PHOTO_NO = $('#PHOTO_NO').val()
            var LikeCount = $('#LikeCount').val()

            if (LikeCount=='') {
                LikeCount = 0;
            }

            if (NICK_NAME=='') {
                alert('未輸入訪客姓名')
                return false;
            }

            if (PHOTO_NO == '') {
                alert('異常，未取得PHOTO_NO，請先「放棄、，重新點選')
                return false;
            }

            SHARE_show(PHOTO_NO, NICK_NAME, LikeCount)
            $('#LikeNickNameModal').modal('hide');
        }

        function SHARE_show(PHOTO_NO, NICK_NAME, LikeCount)
        {
            $("#check").html("");
            var str = "";
            str = $("#check").html();
            $("#check").html(str + "0");
        
             $.ajax({
                url: "@(Url.Action("ShareSave", (string)ViewBag.BRE_NO))",     // url位置
                type: 'post',                   // post/get
                data: {
                    PHOTO_NO: PHOTO_NO,
                    NICK_NAME: NICK_NAME,
                    LikeCount: LikeCount
                },     // data
                dataType: 'json',               // xml/json/script/html
                cache: false,                   // 是否允許快取
                 success: function (data) {
                     str = $("#check").html();
                     $("#check").html(str+"1");
                    var res = jQuery.parseJSON(data);

                     if (res.Success == 0) { //失敗 
                         str = $("#check").html();
                         $("#check").html(str +"2");
                        alert(res.Error);
                    }
                    else if (res.Success == 1) //成功
                     {
                         str = $("#check").html();
                         $("#check").html(str +"3");
                     
                         funAjax()
                         str = $("#check").html();
                         $("#check").html(str +"4");
                        //$('#Link_' + PHOTO_NO).attr('disabled', true);

                        //$('<samp rel="tooltip" title="您已經按讚過" class="wrap poptooltip animated"><samp class="overlap"></samp><a id="Link_' + PHOTO_NO +'" class="btn btn-primary btn-xs" role="button" disabled><i class="glyphicon glyphicon-thumbs-up"></i> 按讚</a></samp>').replaceAll('#Link_' + PHOTO_NO);

                        //$('#Font_' + PHOTO_NO).html('<text>' + res.LikeCount + ' 人按讚</text>')

                        //$('[rel="tooltip"]').tooltip({
                        //    animated: 'fade',
                        //    placement: 'bottom',
                        //});
                    }
                     else if (res.Success == 2) { //未輸入訪客名稱 或 未登入
                         str = $("#check").html();
                         $("#check").html(str +"5");
                        $('#LikeNickNameModal').modal('show');
                        $('#PHOTO_NO').val(PHOTO_NO);
                        $('#LikeCount').val(LikeCount);
                        $('#LikeNickNameModal').on('shown.bs.modal', function () {
                            $('#NICK_NAME').focus()
                        })
                    }
                },
                 error: function (xhr, err) {
                     str = $("#check").html();
                     $("#check").html(str+"6");
                    alert("readyState: " + xhr.readyState + "\nstatus: " + xhr.status);
                    alert("responseText: " + xhr.responseText);
                }

            });
        }

        function doSort(SortCol) {
            $('#@Html.IdFor(m => m.Search.OrdercColumn)').val(SortCol);
            FunPageProc(1)
        }
        function DownLoadJPG() {
            window.location = "@Url.Action("ExportFileZZI34", (string)ViewBag.BRE_NO)";
        }
        //查詢
        function funAjax() {
            $.ajax({
                url: '@Url.Action("_WorkPageContent", (string)ViewBag.BRE_NO)',
                data: $(targetFormID).serialize(),
                type: 'post',
                async: false,
                cache: false,
                dataType: 'html',
                success: function (data) {
                    $('#WorkPageContent').html(data);
                },
                error: function (xhr, ajaxOptions, thrownError) {
                    alert(xhr.status);
                    alert(thrownError);
                }
            });
        }
        function ExportFILE() {

              @*$.ajax({
                url: '@Url.Action("ExportFileZZI34", (string)ViewBag.BRE_NO)',
                data: $(targetFormID).serialize(),
                type: 'post',
                async: false,
                cache: false,
                dataType: 'html',
                success: function (data) {
                 
                },
                error: function (xhr, ajaxOptions, thrownError) {
                    alert(xhr.status);
                    alert(thrownError);
                }
            });*@
            $("#myModal").modal('show');
            $("#ExportFID").attr('disabled', true).html("Loading...傳送中，請勿重複點選");
            window.location = "@Url.Action("ExportFileZZI34", (string)ViewBag.BRE_NO)";
            setTimeout(function () {
                $("#barr").css("width", "80%");
            }, 5000);
            setTimeout(function () {
                $("#barr").css("width", "90%");
            }, 1000);

            $("#ExportFID").attr("disabled", false);
            setTimeout(function () {
                $("#myModal").modal('hide');
            }, 5000);
        }
        function funAjaxGetLikeList(PHOTO_NO) {
            $.ajax({
                url: '@Url.Action("_LikeListView", (string)ViewBag.BRE_NO)',
                data: {
                    PHOTO_NO: PHOTO_NO,
                },     // data
                type: 'post',
                async: false,
                cache: false,
                dataType: 'html',
                success: function (data) {
                    $('#LikeListPageContent').html(data);

                    $('#LikeListModal').modal('show');
                },

                error: function (xhr, ajaxOptions, thrownError) {
                    alert(xhr.status);
                    alert(thrownError);
                }
            });
        }

        function todoClear() {
            ////重設

            $("#Q_Div").find(":input,:selected").each(function (i) {

                var type = $(this).attr('type')
                var InPreadonly = $(this).attr('readonly')
                var tag = this.tagName.toLowerCase(); // normalize case

                if (InPreadonly == false || InPreadonly == undefined) {

                    if (type == 'radio' || type == 'checkbox') {
                        if ($(this).attr("title") == 'Default') {
                            this.checked = true;
                        }
                        else {
                            this.checked = false;
                        }
                    }
                    else if (tag == 'select') { //下拉式選單
                        this.selectedIndex = 0;
                    }
                    else if (type == 'text' || type == 'hidden' || type == 'password' || type == 'textarea') {
                        this.value = '';
                    }
                }
            });

            FunPageProc(1);
        }
    </script>
}