﻿@model ZZZI19IndexViewModel
@{
    ViewBag.Title = "學生清單維護";
    int i = 0;


    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
}


<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")
<br />
<label class="label_dt">登入次數說明:登入次數為「網頁登入」與「APP登入」，一年內登入加總。</label>


@using (Html.BeginForm("Edit1", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "ZZZI19E", id = "ZZZI19E" }))
{
    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })
    @Html.HiddenFor(m => m.zZZI19Hrmt.USER_NO)
    @Html.HiddenFor(m => m.zZZI19Hrmt.USER_STATUS)
    @Html.HiddenFor(m => m.zZZI19Hrmt.IDNO)
    @*@Html.ValidationSummary(true, "", new { @class = "text-danger" })*@
    @*@Html.ValidationSummary(true, "", new { @class = "text-danger" })*@
    <div>

        <img src="~/Content/img/web-bar3-revise-114.png" style="width:100%" class="img-responsive " alt="Responsive image" />
        <div class="Div-EZ-ADDI05">
            <div class="form-horizontal">
                <div style="height:15px"></div>
                <div class="Caption_Div">
                    @*@ViewBag.Panel_Title*@
                </div>
                <div style="height:15px"></div>
                <div>
                    <div class="form-group">
                        @Html.Label("學號", htmlAttributes: new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            @Html.DisplayFor(model => model.zZZI19Hrmt.USER_NO, new { htmlAttributes = new { @class = "form-control", @placeholder = "必填" } })

                            @Html.ValidationMessageFor(model => model.zZZI19Hrmt.USER_NO, "", new { @class = "text-danger" })
                        </div>
                    </div>
                    <div class="form-group">
                        @Html.LabelFor(model => model.zZZI19Hrmt.NAME, htmlAttributes: new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            @Html.DisplayFor(model => model.zZZI19Hrmt.NAME, new { htmlAttributes = new { @class = "form-control", @placeholder = "必填" } })

                            @Html.ValidationMessageFor(model => model.zZZI19Hrmt.NAME, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div class="form-group">
                        @Html.LabelFor(model => model.zZZI19Hrmt.USER_STATUS, htmlAttributes: new { @class = "control-label col-md-2 col-sm-3" })
                        <div class="col-md-9  col-sm-6" style="padding-top:7px">
                            @Html.RadioButtonFor(model => model.zZZI19Hrmt.USER_STATUS, 1, new { @id = "USER_STATUS" })
                            @Html.Label("在校")

                            @Html.RadioButtonFor(model => model.zZZI19Hrmt.USER_STATUS, 9, new { @id = "USER_STATUS" })
                            @Html.Label("轉出")
                        </div>
                    </div>

                    @*<div class="form-group">
                            @Html.LabelFor(model => model.zZZI19Hrmt.GRADE, htmlAttributes: new { @class = "control-label col-md-3" })
                            <div class="col-md-9">
                                @Html.DropDownListFor(model => model.zZZI19Hrmt.GRADE, (IEnumerable<SelectListItem>)ViewBag.GradeItem, new { @class = "form-control", @placeholder = "必填" })
                                @Html.ValidationMessageFor(model => model.zZZI19Hrmt.GRADE, "", new { @class = "text-danger" })
                            </div>
                        </div>*@

                    @*<div class="form-group">
                            @Html.LabelFor(model => model.zZZI19Hrmt.CLASS_NO, htmlAttributes: new { @class = "control-label col-md-3" })
                            <div class="col-md-9">
                                @Html.DropDownListFor(model => model.zZZI19Hrmt.GRADE, (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control", @placeholder = "必填" })
                                @Html.ValidationMessageFor(model => model.zZZI19Hrmt.CLASS_NO, "", new { @class = "text-danger" })
                            </div>
                        </div>*@
                    @*<div class="form-group">
                            @Html.LabelFor(model => model.zZZI19Hrmt.NO, htmlAttributes: new { @class = "control-label col-md-3" })
                            <div class="col-md-9">
                                @Html.EditorFor(model => model.zZZI19Hrmt.NO, new { htmlAttributes = new { @class = "form-control", @placeholder = "必填" } })
                                @Html.ValidationMessageFor(model => model.zZZI19Hrmt.NO, "", new { @class = "text-danger" })
                            </div>
                        </div>*@

                    @*<div class="form-group">

                            @Html.Label("學年度", htmlAttributes: new { @class = "control-label col-md-3" })


                            <div class="col-md-9">
                                @Html.DropDownListFor(model => model.zZZI19Hrmt.SYEAR, (IEnumerable<SelectListItem>)ViewBag.YearItems, new { @class = "form-control", @placeholder = "必填" })
                                @Html.ValidationMessageFor(model => model.zZZI19Hrmt.SYEAR, "", new { @class = "text-danger" })
                            </div>
                        </div>*@

                    <div class="form-group">
                        @Html.LabelFor(model => model.zZZI19Hrmt.IDNO, htmlAttributes: new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            @Html.DisplayFor(model => model.zZZI19Hrmt.IDNO, new { htmlAttributes = new { @class = "form-control", @type = "text" } })
                            @Html.ValidationMessageFor(model => model.zZZI19Hrmt.IDNO, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    @*<div class="form-group">
                            @Html.LabelFor(model => model.zZZI19Hrmt.BIRTHDAY, htmlAttributes: new { @class = "control-label col-md-3" })
                            <div class="col-md-9">
                                @Html.EditorFor(model => model.zZZI19Hrmt.BIRTHDAY, new { htmlAttributes = new { @class = "form-control", @type = "text" } })
                                @Html.ValidationMessageFor(model => model.zZZI19Hrmt.BIRTHDAY, "", new { @class = "text-danger" })
                            </div>
                        </div>*@


                </div>
                <div>
                    <button class="btn btn-default" type="button" onclick="btnADDUser_onclick();" id="BtnSave">儲存</button>
                    @*<input type="button" id="ADD" value="新增" class="btn btn-default" onclick="btnADDUser_onclick();" />*@
                    @*<input type="button" id="back" value="返回" class="btn btn-default" onclick="btnback();" />*@

                </div>
            </div>
        </div>

    </div>}
@section scripts{
    <script>
        var targetFormID = '#ZZZI19E';



            $(document).ready(function () {

                $("[id^='ResetPassword']").colorbox({ iframe: true, width: '50%', height: '50%', opacity: 0.82 });
            });

            function doSort(VAL)
            {
                $('#@Html.IdFor(m=>m.OrdercColumn)').val(VAL)
                document.ZZZI19.enctype = "multipart/form-data";
                document.ZZZI19.action = "QUERY";
                document.ZZZI19.submit();
            }

            function btnCancelUser_onclick() {
                document.ZZZI19.enctype = "multipart/form-data";
                document.ZZZI19.action = "CancelUser";
                document.ZZZI19.submit();
        }
        function btnADDUser_onclick() {


            $(targetFormID).attr("action", "@Url.Action("EditSave1", (string)ViewBag.BRE_NO)")
           // $("[name='zZZI19Hrmt.USER_STATUS']").val('');
            var STRVAL = '';
            STRVAL = $("[name='zZZI19Hrmt.USER_STATUS']:checked").val();
            console.log(STRVAL);
            $("[name='zZZI19Hrmt.USER_STATUS']").val(STRVAL);
            $(targetFormID).submit();

            if ("@ViewBag.Msg" == "轉出成功") {

                window.parent.$.colorbox.close();
            
            }
            //document.ZZZI19.enctype = "multipart/form-data";
            //document.ZZZI19.action = "ADDUser";
            //document.ZZZI19.submit();
        }
            function btnStartUser_onclick() {
                document.ZZZI19.enctype = "multipart/form-data";
                document.ZZZI19.action = "StartUser";
                document.ZZZI19.submit();
            }

            function btnSearch_onclick() {
                document.ZZZI19.enctype = "multipart/form-data";
                document.ZZZI19.action = "Query";
                document.ZZZI19.submit();
            }

        
            function FunPageProc(page) {
                if ($(targetFormID).size() > 0) {
                    $('#Page').val(page)
                    $(targetFormID).submit();
                }
            };
            function doSort(SortCol) {
                $("#OrdercColumn").val(SortCol);
                FunPageProc(1)
            }

            function doSearch(ColName, whereValue) {
                $("#" + ColName).val(whereValue);
                FunPageProc(1)
            }

            function todoClear() {
                ////重設

                $('#Q_Div').find(":input,:selected").each(function (i) {

                    var type = $(this).attr('type')
                    var InPreadonly = $(this).attr('readonly')
                    var tag = this.tagName.toLowerCase(); // normalize case

                    if (InPreadonly == false || InPreadonly == undefined) {

                        if (type == 'radio' || type == 'checkbox') {
                            if ($(this).attr("title") == 'Default') {
                                this.checked = true;
                            }
                            else {
                                this.checked = false;
                            }
                        }
                        else if (tag == 'select') { //下拉式選單
                            this.selectedIndex = 0;
                        }
                        else if (type == 'text' || type == 'hidden' || type == 'password' || type == 'textarea') {
                            this.value = '';
                        }
                    }
                });

                FunPageProc(1)
            }
    </script>
}