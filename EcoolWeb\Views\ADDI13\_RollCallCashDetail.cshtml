﻿@model RollCallCashDetailViewModel

@using (Html.BeginCollectionItem("Details", true))
{
    var Index = Html.GetIndex("Details");

    <li class="list-group-item clearfix">
        <div class="row">
            <div class="col-xs-2 ">
                @Html.HiddenFor(m => m.SCHOOL_NO)
                @Html.HiddenFor(m => m.USER_NO)
                @Html.HiddenFor(m => m.CLASS_NO)
                @Html.HiddenFor(m => m.SEAT_NO)
                @Html.HiddenFor(m => m.NAME)
                @Html.HiddenFor(m => m.SNAME)
                @Html.DisplayFor(m => m.CLASS_NO)
            </div>
            <div class="col-xs-2">
                @Html.DisplayFor(m => m.SEAT_NO)
            </div>
            <div class="col-xs-4">
                @Html.DisplayFor(m => m.NAME)
            </div>

            <div class="col-xs-4 text-center">
                @if (Model.STATUS == (byte)AWAT12.StatusVal.己給點)
                {
                    @Html.DisplayFor(m => m.CASH)
                }
                else
                {
                    @Html.EditorFor(m => m.CASH, new { htmlAttributes = new { @class = "Input_Cash form-control form-control-required", @placeholder = "請填數字，請填 0 ~ 50 數字 " } })
                }
            </div>
            <div class="col-xs-4">
                @Html.DisplayFor(m => m.CHG_DATE)
            </div>
        </div>
    </li>

}