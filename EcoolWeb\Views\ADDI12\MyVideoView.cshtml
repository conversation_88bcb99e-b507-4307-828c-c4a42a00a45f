﻿@model ADDI12IndexListViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);

    if (AppMode || (Model?.WhereIsColorboxForUser ?? false))
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
    string DateNowStr = DateTime.Now.ToString("yyyy_MM_dd_H_mm_ss");
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")
@Html.Action("_ListBeginForm", (string)ViewBag.BRE_NO, new { ActionResultType = ADDI12IndexListViewModel.ActionResultTypeVal.MyVideoView })