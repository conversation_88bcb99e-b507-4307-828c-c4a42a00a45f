/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX-Web/Alphabets/Italic/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXMathJax_Alphabets-italic"]={directory:"Alphabets/Italic",family:"STIXMathJax_Alphabets",style:"italic",testString:"\u00A0\u0384\u0385\u0386\u0387\u0388\u0389\u038A\u038C\u038E\u038F\u0390\u03AA\u03AB\u03AC",32:[0,0,250,0,0],160:[0,0,250,0,0],900:[649,-494,289,160,322],901:[649,-494,333,70,387],902:[678,0,611,-51,564],903:[441,-330,333,150,261],904:[678,0,630,7,679],905:[678,0,740,4,821],906:[678,0,350,3,429],908:[678,18,722,58,699],910:[678,0,580,8,725],911:[678,0,762,-6,739],912:[649,11,278,49,387],938:[856,0,333,-8,460],939:[856,0,556,78,648],940:[649,11,552,27,549],941:[649,11,444,30,425],942:[649,205,474,14,442],943:[649,11,278,49,288],944:[649,10,478,19,446],970:[606,11,278,49,359],971:[606,10,478,19,446],972:[649,11,500,27,468],973:[649,10,478,19,446],974:[649,11,686,27,654],1025:[856,0,611,1,631],1026:[653,208,723,70,663],1027:[914,0,569,-36,603],1028:[666,18,657,67,680],1029:[667,18,500,7,498],1030:[653,0,333,-7,382],1031:[856,0,333,-31,433],1032:[653,18,444,-34,463],1033:[653,16,961,-35,901],1034:[653,0,966,-28,906],1035:[653,0,786,70,701],1036:[914,0,621,-28,657],1038:[887,14,656,110,716],1039:[653,179,722,-25,747],1040:[668,0,611,-49,566],1041:[653,0,590,-28,603],1042:[653,0,597,-23,571],1043:[653,0,569,-36,603],1044:[653,179,655,-103,696],1045:[653,0,611,1,631],1046:[661,0,956,-55,972],1047:[668,16,564,9,548],1048:[653,0,708,-25,749],1049:[887,0,708,-25,749],1050:[661,0,621,-28,657],1051:[653,16,699,-35,740],1052:[653,0,814,-33,855],1053:[653,0,708,-26,749],1054:[666,18,712,60,699],1055:[653,0,704,-29,745],1056:[653,0,568,-24,578],1057:[666,18,667,67,690],1058:[653,0,556,70,644],1059:[653,14,656,110,716],1060:[653,0,772,73,758],1061:[653,0,575,-67,617],1062:[653,179,706,-25,747],1063:[653,0,622,54,663],1064:[653,0,936,-14,977],1065:[653,179,936,-14,977],1066:[653,0,695,63,652],1067:[653,0,852,-28,893],1068:[653,0,597,-28,537],1069:[666,18,658,15,636],1070:[666,18,877,-32,850],1071:[653,0,635,-49,676],1072:[441,11,514,23,482],1073:[683,11,498,36,535],1074:[441,11,442,31,423],1075:[441,11,390,1,384],1076:[683,11,489,30,470],1077:[441,11,440,34,422],1078:[441,11,799,0,791],1079:[441,11,376,-18,357],1080:[441,11,527,29,495],1081:[667,11,527,29,495],1082:[441,11,491,18,485],1083:[441,12,474,-44,442],1084:[432,12,633,-45,601],1085:[441,9,504,20,472],1086:[441,11,489,29,470],1087:[441,9,511,19,479],1088:[441,205,483,-77,464],1089:[441,11,441,27,422],1090:[441,9,741,17,709],1091:[441,206,421,-61,389],1092:[683,205,702,29,677],1093:[441,11,444,-35,439],1094:[441,182,527,29,495],1095:[441,9,482,42,450],1096:[441,11,785,31,753],1097:[441,182,785,31,753],1098:[441,11,567,12,528],1099:[441,11,689,50,657],1100:[441,11,471,50,433],1101:[441,11,408,7,391],1102:[441,11,674,21,655],1103:[432,9,481,-25,449],1105:[606,11,440,34,475],1106:[683,208,479,20,448],1107:[664,11,390,1,455],1108:[441,11,428,26,441],1109:[442,13,389,-9,341],1110:[654,11,278,43,258],1111:[606,11,278,43,357],1112:[652,207,278,-172,231],1113:[441,12,679,-44,631],1114:[441,11,697,21,649],1115:[683,9,511,20,479],1116:[664,11,491,18,485],1118:[667,206,421,-61,417],1119:[441,182,527,29,495],1122:[653,0,681,19,621],1123:[683,11,542,13,504],1130:[653,0,953,-55,893],1131:[432,11,741,0,686],1138:[666,18,712,60,699],1139:[441,11,489,29,470],1140:[662,18,646,76,742],1141:[441,18,464,34,528],1168:[783,0,524,-30,622],1169:[507,11,337,42,404],8453:[676,14,855,47,808],8470:[668,15,1046,19,1031],57500:[756,218,753,37,787],57501:[756,218,706,42,732],57502:[756,218,624,42,724],57523:[681,207,500,-141,504],58156:[756,218,613,42,612],58158:[756,218,595,-47,644],58160:[756,218,514,-58,634],58162:[756,218,536,40,522],58164:[756,218,478,29,491],58166:[756,218,440,11,482],58168:[756,218,512,32,536],58170:[756,218,529,20,519],58172:[756,217,326,-10,453],58174:[756,218,546,57,558],58176:[756,218,557,52,619],58178:[756,217,630,0,696],58180:[756,218,466,32,495],58182:[756,218,454,9,468],58184:[756,240,533,27,498],58186:[756,217,591,14,710],58188:[756,218,584,32,591],58190:[756,218,468,1,460],58192:[756,218,534,42,560],58194:[756,218,448,32,537],58196:[756,218,514,32,545],58198:[756,218,663,-2,690],58200:[756,218,632,4,700],58202:[756,218,668,32,736],58204:[756,217,733,42,758],58206:[756,218,602,32,590],58208:[756,218,666,42,778],58210:[756,217,889,32,897],58214:[756,240,444,7,482],58218:[756,240,528,-57,648],58222:[756,240,457,31,445],58226:[756,240,528,8,715],58228:[756,240,533,-16,559],58230:[756,218,533,42,525],58232:[756,218,533,35,506],58234:[756,218,477,42,539],58237:[756,218,710,-50,694],58239:[683,10,606,10,601],58241:[683,10,554,39,540],58243:[579,10,353,6,323],58245:[460,10,326,15,278],58247:[668,0,490,30,502],58249:[668,0,490,30,478],58368:[681,207,500,-141,504]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"STIXMathJax_Alphabets-italic"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Alphabets/Italic/Main.js"]);
