﻿using Dapper;
using ECOOL_APP;
using ECOOL_APP.EF;
using EcoolWeb.Models;
using MvcPaging;
using System;
using System.Linq;
using System.Web;
using System.Web.Mvc;

using ECOOL_APP.com.ecool.service;
using ECOOL_APP.com.ecool.LogicCenter;
using ECOOL_APP.com.ecool.Models;
using com.ecool.service;
using EcoolWeb.ViewModels;
using System.Collections.Generic;

namespace EcoolWeb.Controllers
{
    public class BarcCodeMyCashController : Controller
    {
        /// <summary>
        /// 此Controller 對應的程式代碼
        /// </summary>
        private string Bre_NO = "BarcCodeMyCash";

        /// <summary>
        /// 會員相關資訊
        /// </summary>
        private UserProfile user;

        private string SCHOOL_NO = string.Empty;

        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();

        public ActionResult Index(BarcCodeMyCashIndexViewModel model)
        {
            string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
            bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);

            this.Shared("酷幣查詢與獎品對兌");
            user = UserProfileHelper.Get();
            if (AppMode || model.ShowOnMobile == "True")
            {
                model.WhereKeyword = user?.USER_NO;
            }
            int hrmt25Count = 0;
            if (Request.UrlReferrer?.ToString().Contains("Index3") == true)
            {
                model.WhereUrlReferrer = "Index3";
                ViewBag.ATMUID = "T";
            }
            if (user != null)
            {
                hrmt25Count = db.HRMT25.Where(x => x.USER_NO == user.USER_NO && x.SCHOOL_NO == user.SCHOOL_NO && x.ROLE_ID == "4").Count();
            }

            if (user != null && (user.USER_NO != "coolman" || user.USER_TYPE != "A") && hrmt25Count <= 0 && AppMode == false && model.ShowOnMobile != "True")
            {
                if (Request.UrlReferrer?.ToString().Contains("Index3") == true)
                {
                    ViewBag.ATMUID = "T";
                }
                else
                {
                    this.Request.RequestContext.HttpContext.Session[CONSTANT.SESSION_USER_KEY] = null;
                }
            }

            return View();
        }

        public ActionResult Index3Page(BarcCodeMyCashIndexViewModel model)
        {
            string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
            bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);

            this.Shared("酷幣查詢與獎品對兌");
            user = UserProfileHelper.Get();
            if (AppMode || model.ShowOnMobile == "True")
            {
                model.WhereKeyword = user?.USER_NO;
            }
            int hrmt25Count = 0;
            if (Request.UrlReferrer?.ToString().Contains("Index3") == true)
            {
                model.WhereUrlReferrer = "Index3";
                ViewBag.ATMUID = "T";
            }
            if (user != null)
            {
                hrmt25Count = db.HRMT25.Where(x => x.USER_NO == user.USER_NO && x.SCHOOL_NO == user.SCHOOL_NO && x.ROLE_ID == "4").Count();
            }

            if (user != null && (user.USER_NO != "coolman" || user.USER_TYPE != "A") && hrmt25Count <= 0 && AppMode == false && model.ShowOnMobile != "True")
            {
                if (Request.UrlReferrer?.ToString().Contains("Index3") == true)
                {
                    ViewBag.ATMUID = "T";
                }
                else
                {
                    this.Request.RequestContext.HttpContext.Session[CONSTANT.SESSION_USER_KEY] = null;
                }
            }

            return View();
        }
 
        public ActionResult IstoryUserIndexForPresent()
        {
          
            BarcCodeMyCashIndexViewModel model2 = new BarcCodeMyCashIndexViewModel();
            model2.WhereSchoolNo = "403605";
            model2.WhereKeyword = "107038";
            return RedirectToAction("IstoryUserIndex", model2);
        }
        [HttpGet]
        public ActionResult _PageContent2()
        {
            Shared();
           BarcCodeMyCashIndexViewModel model2 = new BarcCodeMyCashIndexViewModel();
            model2.WhereKeyword = user.USER_NO;
            model2.WhereSchoolNo = user.SCHOOL_NO;
            return RedirectToAction("IstoryUserIndex", model2);
        }
        public ActionResult IstoryUserIndex(BarcCodeMyCashIndexViewModel model2)
        {
            if (string.IsNullOrWhiteSpace(model2.WhereSchoolNo))
            {
                model2.WhereSchoolNo = SCHOOL_NO;
            }
            model2.LoginYN = "N";
            user = UserProfileHelper.Get();
            if (user == null) {
                model2.LoginYN = "Y";
                return RedirectToAction("LoginPage", "Home", new { returnURL = UrlCustomHelper.GetOwnWebUri() + $@"/EcoolWeb/{Bre_NO}/_PageContent2?Mode=Edit" });

            }
            if (string.IsNullOrWhiteSpace(model2.WhereKeyword) && UserProfileHelper.Get() != null)
            {
                UserProfile user = UserProfileHelper.Get();
                model2.WhereKeyword = user.USER_NO;
                model2.WhereSchoolNo = user.SCHOOL_NO;
            }
            this.Request.RequestContext.HttpContext.Session[CONSTANT.SESSION_USER_KEY] = model2.WhereKeyword;
            this.Request.RequestContext.HttpContext.Session[CONSTANT.SESSION_SCHOOL_KEY] = model2.WhereSchoolNo;
            //if (string.IsNullOrWhiteSpace(model2.WhereSchoolNo))
            //{
            //    TempData["StatusMessage"] = "請先選取學校";
            //    return PartialView(model2);
            //}

            HRMT01 hRMT01 = db.HRMT01.Where(a => a.SCHOOL_NO == model2.WhereSchoolNo && a.USER_NO == model2.WhereKeyword).FirstOrDefault();
            if (hRMT01 == null && string.IsNullOrWhiteSpace(model2.WhereKeyword) == false)
            {
                hRMT01 = db.HRMT01.Include("HRMT25").Where(a => a.CARD_NO == model2.WhereKeyword && a.SCHOOL_NO == model2.WhereSchoolNo).FirstOrDefault();
            }
            UserProfile LoginUser = UserProfile.FillUserProfile(hRMT01);
            UserProfileHelper.Set(LoginUser);
            BarcCodeMyCashIndexViewModel model1 = new BarcCodeMyCashIndexViewModel();

            UserIndexViewModel model = new UserIndexViewModel();
            //AWAT01 tCASH = db.AWAT01.Where(a => a.SCHOOL_NO == hRMT01.SCHOOL_NO && a.USER_NO == hRMT01.USER_NO).FirstOrDefault();

            model.Run = (Convert.ToDouble(db.ADDT25.Where(a => a.SCHOOL_NO == hRMT01.SCHOOL_NO && a.USER_NO == hRMT01.USER_NO)
                .Select(a => a.RUN_TOTAL_METER).FirstOrDefault() ?? 0) / 1000.00).ToString("#,0.000");
            IQueryable<ADDT06> ADDT06List = db.ADDT06.Where(p => p.SCHOOL_NO == hRMT01.SCHOOL_NO && p.APPLY_STATUS == "0");

            if (!string.IsNullOrWhiteSpace(hRMT01.CLASS_NO) || PermissionService.GetPermission_Use_YN("ADDT", "ADDTList_CheckPendingDetail", user.SCHOOL_NO, user.USER_NO) == "N")
            {
                ADDT06List = ADDT06List.Where(a => a.CLASS_NO == hRMT01.CLASS_NO);
            }

            ViewBag.ADDT06Cnt = ADDT06List.Count();
            //線上投稿資料
            model.arrWRITING_NO = (from a01 in db.ADDT01
                                   where a01.SCHOOL_NO == hRMT01.SCHOOL_NO && a01.USER_NO == hRMT01.USER_NO
                                    && a01.WRITING_STATUS != (byte)ADDStatus.eADDT01Status.Disable && a01.WRITING_STATUS != (byte)ADDStatus.eADDT01Status.Back
                                   orderby a01.WRITING_NO ascending
                                   select a01.WRITING_NO).ToList();
            //線上投稿待批閱數量
            IQueryable<ADDT01> ADDT01List = db.ADDT01.Where(a => a.SCHOOL_NO == hRMT01.SCHOOL_NO &&
            (a.WRITING_STATUS == (byte)ADDStatus.eADDT01Status.Draft || a.WRITING_STATUS == (byte)ADDStatus.eADDT01Status.TurnVerify));
            if (!string.IsNullOrWhiteSpace(hRMT01.CLASS_NO) || PermissionService.GetPermission_Use_YN("ADDI01", "Verify", hRMT01.SCHOOL_NO, user.USER_NO) == "N")
            {
                ADDT01List = ADDT01List.Where(a => a.VERIFIER == hRMT01.USER_NO);
            }

            ViewBag.ADDT01Qty = ADDT01List.Count();
            //線上藝廊待批閱數量
            IQueryable<ADDT21> ADDT21List = db.ADDT21.Where(a => a.SCHOOL_NO == hRMT01.SCHOOL_NO && a.STATUS == ADDT21.STATUSVal.Verification
            );

            if (!string.IsNullOrWhiteSpace(hRMT01.CLASS_NO) || PermissionService.GetPermission_Use_YN("ZZZI34", "Verify", hRMT01.SCHOOL_NO, hRMT01.USER_NO) == "N")
            {
                ADDT21List = ADDT21List.Where(a => a.VERIFIER == hRMT01.USER_NO);
            }

            ViewBag.ADDT21Qty = ADDT21List.Count();

            ViewBag.WFT05 = db.WFT05.Count();
            //閱讀認證獎狀待頒發
            IQueryable<ADDT0809> TempList = (from a09_his in db.ADDT09_HIS
                                             join a08 in db.ADDT08
                                                   on new { a09_his.LEVEL_ID, a09_his.SCHOOL_NO }
                                               equals new { a08.LEVEL_ID, a08.SCHOOL_NO }
                                             where a09_his.STATUS == 0 && a09_his.SCHOOL_NO == hRMT01.SCHOOL_NO
                                             select new ADDT0809
                                             {
                                                 UPNO = a09_his.UPNO.ToString(),
                                                 SYEAR = a09_his.SYEAR.ToString(),
                                                 SEMESTER = a09_his.SEMESTER.ToString(),
                                                 CLASS_NO = a09_his.CLASS_NO,
                                                 SEAT_NO = a09_his.SEAT_NO,
                                                 USERNAME = a09_his.NAME,
                                                 SNAME = a09_his.SNAME,
                                                 BOOK_QTY = a09_his.BOOK_QTY,
                                                 LEVEL_DESC = a08.LEVEL_DESC,
                                                 UP_DATE = a09_his.UP_DATE,
                                                 STATUS = a09_his.STATUS.ToString(),
                                                 SCHOOL_NO = a09_his.SCHOOL_NO,
                                                 USER_NO = a09_his.USER_NO,
                                                 DP_PERSON = a09_his.DP_PERSON,
                                                 DP_DATE = a09_his.DP_DATE
                                             });
            ViewBag.ADDT0809 = TempList.Count();

            if (hRMT01 != null)
            {
                model.IDNO = hRMT01.IDNO;

                if (!string.IsNullOrWhiteSpace(hRMT01.PHOTO))
                {
                    SECI01Service Service1 = new SECI01Service();
                    model.PlayerUrl = Service1.GetDirectorySysMyPhotoPath(hRMT01.SCHOOL_NO, hRMT01.USER_NO, hRMT01.PHOTO);
                }
            }

            if (string.IsNullOrWhiteSpace(model.PlayerUrl))
            {
                model.PlayerUrl = "";
            }
            // 等級分數計算
            // 公式: 目前酷幣值 + 定存酷幣值 + 每月的借書量 x 100 + 跑步里程 x 20
            //學生酷幣
            AWAT01 tCASH = db.AWAT01.Where(a => a.SCHOOL_NO == hRMT01.SCHOOL_NO && a.USER_NO == hRMT01.USER_NO).FirstOrDefault();
            if (tCASH != null)
            {
                if (tCASH.CASH_AVAILABLE.HasValue) model1.UserCash = tCASH.CASH_AVAILABLE.Value;
                if (tCASH.CASH_AVAILABLE.HasValue) model.CASH = tCASH.CASH_AVAILABLE.Value;
            }

            //定存
            AWAI07Service Service = new AWAI07Service();
            AWAI07IndexViewModel modelA7 = new AWAI07IndexViewModel();
            modelA7.Search = new AWAI07SearchViewModel();
            modelA7.Search.WhereSCHOOL_NO = hRMT01.SCHOOL_NO;
            modelA7.Search.WhereUSER_NO = hRMT01.USER_NO;
            modelA7.Search.WhereSTATUS = AWAT10.StatusVal.SetUp;
            Service.GetListData(modelA7, ref db);
            if (modelA7.ListData != null)
            {
                var SumAMT = modelA7.ListData.Sum(a => a.AMT);
                if (SumAMT.HasValue) model.UserAWAI07 = SumAMT.Value;
            }
            //借書量
            SECI05Service ServiceS5 = new SECI05Service();
            SECI05IndexViewModel BookModel = new SECI05IndexViewModel();
            BookModel.WhereSCHOOL_NO = hRMT01.SCHOOL_NO;
            BookModel.WhereUSER_NO = hRMT01.USER_NO;
            BookModel = ServiceS5.GetBorrowData(BookModel, null, ref db);
            if (BookModel.MyBorrow != null)
            {
                model1.UserBookQty = BookModel.MyBorrow.THIS_SESEM_QTY;
                model1.AllBookQty = BookModel.MyBorrow.ALL_QTY;
                model1.MonthBookQty = BookModel.MyBorrow.THIS_MM_QTY;
            }
            //運動
            ADDI11Service ServiceA11 = new ADDI11Service();
            ADDI11MyRunLogViewModel modelA11 = new ADDI11MyRunLogViewModel();
            modelA11.WhereSCHOOL_NO = hRMT01.SCHOOL_NO;
            modelA11.WhereUSER_NO = hRMT01.USER_NO;
            modelA11 = ServiceA11.GetMyRunLog(modelA11, user, ref db);

            model1.RUN_TOTAL_METER = (modelA11.MyRunRank != null) ? modelA11.MyRunRank.RUN_TOTAL_METER ?? 0 : 0;
            model1.point = LogicCenter.CaculateMyATM_point(model1.UserCash,
                Convert.ToInt32(model1.UserAWAI07),
                model1.MonthBookQty,
                Convert.ToDouble(model1.RUN_TOTAL_METER ?? 0) / 1000.000);
            model1.Level = LogicCenter.CaculateMyATM_Level(model1.point);
            model.USER_NO = hRMT01.USER_NO;
            model.SCHOOL_NO = hRMT01.SCHOOL_NO;
            ViewBag.Level = model1.Level;
            return View(model);
        }

        // GET: Barcode
        public ActionResult Index2(string WhereSchoolNo, string TimeoutSeconds,string NoBook)
        {
            this.Shared("酷幣查詢與獎品對兌");

            user = UserProfileHelper.Get();
            if (user != null)
            {
                if (user.USER_KEY == UserType.Admin)
                {
                    ViewBag.Gift = true;
                }
                ViewBag.school_no = WhereSchoolNo;
            }
            else {
                ViewBag.school_no = WhereSchoolNo;
        
           

            }
            return View();
        }
        public ActionResult _BookQtyRank(BarcCodeMyCashIndexViewModel model) {

            IQueryable<ADDT0809> RankBOOK;

            string SchoolNO = UserProfileHelper.GetSchoolNo();

            if (string.IsNullOrWhiteSpace(model.WhereSchoolNo) == false)
            {
                SchoolNO = model.WhereSchoolNo;
            }
            string sSQL = @"SELECT w1.SCHOOL_NO, w1.USER_NO  , LEVEL_ID  , LEVEL_DESC 
                           , LEVEL_QTY 
      　　　　　　　　　　　, BOOK_QTY 
      　　　　　　　　　　　, UP_DATE 
      　　　　　　　　　　　, w1.SYEAR 
      　　　　　　　　　　　, w1.CLASS_NO 
      　　　　　　　　　　　, w1.SEAT_NO 
      　　　　　　　　　　　, w1.SNAME 
      　　　　　　　　　　　, w1.NAME 
      　　　　　　　　　　　, ShareCount ,DENSE_RANK()OVER(PARTITION BY  w1.SCHOOL_NO order by BOOK_QTY desc) as BOOK_QTYRANK
 　　　　　　　　　　　　  FROM  ADDV05  w1
                          join HRMT01 hh1  (nolock) on  w1.SCHOOL_NO= hh1.SCHOOL_NO and  w1.USER_NO=hh1.USER_NO and USER_STATUS<>@USER_STATUS and USER_TYPE='S'  and  hh1.CLASS_NO is not null
                           where hh1.SCHOOL_NO=@SCHOOL_NO ";

            RankBOOK = db.Database.Connection.Query<ADDT0809>(sSQL, new { USER_STATUS = UserStaus.Invalid, SCHOOL_NO = SchoolNO }).AsQueryable();
            return PartialView(RankBOOK);

        }

        public ActionResult _RunRank(BarcCodeMyCashIndexViewModel model)
        {

            string SchoolNO = UserProfileHelper.GetSchoolNo();

            if (string.IsNullOrWhiteSpace(model.WhereSchoolNo) == false)
            {
                SchoolNO = model.WhereSchoolNo;
            }

            string sSQLD = $@" select b.SCHOOL_NO,b.USER_NO, MAX(RUN_DATE) as LAST_RUN_DATE, Sum(b.LAP_M) as Total_M
                                from ADDT24 b (nolock)
                                inner join HRMT01 c on b.SCHOOL_NO=c.SCHOOL_NO and b.USER_NO=c.USER_NO
                                Where  b.SCHOOL_NO=@SCHOOL_NO and b.STATUS='{ADDT24.STATUSVal.OK}' and  CONVERT(nvarchar(6),b.RUN_DATE,112)< = CONVERT(nvarchar(6), GETDATE(), 112)  and b.LAP>0
                                   ";
            sSQLD = sSQLD + " group by b.SCHOOL_NO,b.USER_NO ";

            string sSQL1 = $@"
                             select * , RANK() OVER(ORDER BY Total_M DESC) AS RANK
                             from(
                                select a.SCHOOL_NO, a.USER_NO, a.NAME, a.SNAME, a.SEX,
                                a.GRADE, a.CLASS_NO, a.SEAT_NO, a.USER_STATUS,
                                a.USER_TYPE, AG.Total_M, AG.LAST_RUN_DATE
                                from HRMT01 a (nolock)
                                JOIN ({sSQLD}) AS AG
                                ON AG.USER_NO = a.USER_NO AND  AG.SCHOOL_NO = a.SCHOOL_NO
                             ) t
                             where USER_STATUS <> {UserStaus.Disable} AND USER_STATUS <> {UserStaus.Invalid}
                             and SCHOOL_NO=@SCHOOL_NO  ";

            var temp = db.Database.Connection.Query<ADDI11OrderListDataViewModel>(sSQL1
          , new
          {
              SCHOOL_NO = SchoolNO


          }).AsQueryable();
            return PartialView(temp);
        }
            public ActionResult LeaderIndex2(BarcCodeMyCashIndexViewModel model)
        {


            string SchoolNO = UserProfileHelper.GetSchoolNo();

            if (string.IsNullOrWhiteSpace(model.WhereSchoolNo) )
            {
             model.WhereSchoolNo= SchoolNO;
            }

            else
            {

                model.WhereSchoolNo = SchoolNO;
            }
            ViewBag.WhereKeyword = "";
            ViewBag.FROMACTION = "";
            ViewBag.TimeoutSeconds = null;
            if (!string.IsNullOrWhiteSpace(model.WhereKeyword))
            {
                ViewBag.WhereKeyword = model.WhereKeyword;

            }
            if (!string.IsNullOrWhiteSpace(model.FROMACTION))
            {
                ViewBag.FROMACTION = model.FROMACTION;

            }
            if (model.TimeoutSeconds != null)
            {
                ViewBag.TimeoutSeconds = model.TimeoutSeconds;

            }

            return View(model);
        }
        public ActionResult LeaderIndex3(BarcCodeMyCashIndexViewModel model)
        {
            List<BarCoderankViewModel> barCoderankViewModelsItem = new List<BarCoderankViewModel>();
            AWA003QueryViewModel aWA003model = new AWA003QueryViewModel();
            aWA003model.OrdercColumn = "SUMCASH_AVAILABLE";
            AWA003Controller aWA003 = new AWA003Controller();
            IQueryable<ADDT0809> RankBOOK;
           
            string SchoolNO = UserProfileHelper.GetSchoolNo();
            if (!string.IsNullOrWhiteSpace(model.WhereSchoolNo))
            {
                SchoolNO = model.WhereSchoolNo;
            }
            else
            {

                model.WhereSchoolNo = SchoolNO;
            }
            ViewBag.WhereKeyword = "";
            ViewBag.FROMACTION = "";
            ViewBag.TimeoutSeconds = null;
            if (!string.IsNullOrWhiteSpace(model.WhereKeyword))
            {
                ViewBag.WhereKeyword = model.WhereKeyword;

            }
            if (!string.IsNullOrWhiteSpace(model.FROMACTION))
            {
                ViewBag.FROMACTION = model.FROMACTION;

            }
            if (model.TimeoutSeconds != null)
            {
                ViewBag.TimeoutSeconds = model.TimeoutSeconds;

            }
            IQueryable<HRMT01QTY> aWA003HRMT01QTY = aWA003.QeuryDataRank(aWA003model);
            IQueryable<ADDI11OrderListDataViewModel> temp;
            string sSQLD = $@" select b.SCHOOL_NO,b.USER_NO, MAX(RUN_DATE) as LAST_RUN_DATE, Sum(b.LAP_M) as Total_M
                                from ADDT24 b (nolock)
                                inner join HRMT01 c on b.SCHOOL_NO=c.SCHOOL_NO and b.USER_NO=c.USER_NO
                                Where  b.SCHOOL_NO=@SCHOOL_NO and b.STATUS='{ADDT24.STATUSVal.OK}' and  CONVERT(nvarchar(6),b.RUN_DATE,112)< = CONVERT(nvarchar(6), GETDATE(), 112)  and b.LAP>0
                                   ";
            sSQLD = sSQLD + " group by b.SCHOOL_NO,b.USER_NO ";

            string sSQL1 = $@"
                             select * , RANK() OVER(ORDER BY Total_M DESC) AS RANK
                             from(
                                select a.SCHOOL_NO, a.USER_NO, a.NAME, a.SNAME, a.SEX,
                                a.GRADE, a.CLASS_NO, a.SEAT_NO, a.USER_STATUS,
                                a.USER_TYPE, AG.Total_M, AG.LAST_RUN_DATE
                                from HRMT01 a (nolock)
                                JOIN ({sSQLD}) AS AG
                                ON AG.USER_NO = a.USER_NO AND  AG.SCHOOL_NO = a.SCHOOL_NO
                             ) t
                             where USER_STATUS <> {UserStaus.Disable} AND USER_STATUS <> {UserStaus.Invalid}
                             and SCHOOL_NO=@SCHOOL_NO  Order by Total_M Desc, CLASS_NO, SEAT_NO ";

           temp = db.Database.Connection.Query<ADDI11OrderListDataViewModel>(sSQL1
          , new
          {
              SCHOOL_NO = SchoolNO


          }).AsQueryable();
            string sSQL = @"SELECT w1.SCHOOL_NO, w1.USER_NO  , LEVEL_ID  , LEVEL_DESC 
                           , LEVEL_QTY 
      　　　　　　　　　　　, BOOK_QTY 
      　　　　　　　　　　　, UP_DATE 
      　　　　　　　　　　　, w1.SYEAR 
      　　　　　　　　　　　, w1.CLASS_NO 
      　　　　　　　　　　　, w1.SEAT_NO 
      　　　　　　　　　　　, w1.SNAME 
      　　　　　　　　　　　, w1.NAME 
      　　　　　　　　　　　, ShareCount ,DENSE_RANK()OVER(PARTITION BY  w1.SCHOOL_NO order by BOOK_QTY desc) as BOOK_QTYRANK
 　　　　　　　　　　　　  FROM  ADDV05  w1
                          join HRMT01 hh1  (nolock) on  w1.SCHOOL_NO= hh1.SCHOOL_NO and  w1.USER_NO=hh1.USER_NO and USER_STATUS<>@USER_STATUS and USER_TYPE='S'  and  hh1.CLASS_NO is not null
                           where hh1.SCHOOL_NO=@SCHOOL_NO ";

            RankBOOK = db.Database.Connection.Query<ADDT0809>(sSQL, new { USER_STATUS = UserStaus.Invalid, SCHOOL_NO = SchoolNO }).AsQueryable();

           // aWA003HRMT01QTY.OrderByDescending(x => x.den_rank).Take(3);
            foreach (var Items in aWA003HRMT01QTY.OrderBy(x => x.den_rank).Take(3).ToList()) {

                BarCoderankViewModel barCoderankViewModelInfo = new BarCoderankViewModel();
                barCoderankViewModelInfo.SCHOOL_NO = SchoolNO;
                barCoderankViewModelInfo.ALLRANK = (int)Items.den_rank;
                barCoderankViewModelInfo.CLASS_NO = Items.CLASS_NO;
                 barCoderankViewModelInfo.SEAT_NO = Items.SEAT_NO;
                barCoderankViewModelInfo.SNAME = Items.NAME;
                barCoderankViewModelInfo.ALLNumberInfo = Items.GA;
                barCoderankViewModelInfo.Ranktype = "DenRank";
                barCoderankViewModelsItem.Add(barCoderankViewModelInfo);
            }
           // aWA003HRMT01QTY.OrderByDescending(x => x.SUMCASHden_rank).Take(3);
            foreach (var Items in aWA003HRMT01QTY.OrderBy(x => x.SUMCASHden_rank).Take(3).ToList())
            {

                BarCoderankViewModel barCoderankViewModelInfo = new BarCoderankViewModel();
                barCoderankViewModelInfo.SCHOOL_NO = SchoolNO;
                barCoderankViewModelInfo.ALLRANK = (int)Items.SUMCASHden_rank;
                barCoderankViewModelInfo.CLASS_NO = Items.CLASS_NO;
                barCoderankViewModelInfo.SEAT_NO = Items.SEAT_NO;
                barCoderankViewModelInfo.SNAME = Items.NAME;
                barCoderankViewModelInfo.ALLNumberInfo = Items.SUMCASH_AVAILABLE;
                barCoderankViewModelInfo.Ranktype = "SumCashRank";
                barCoderankViewModelsItem.Add(barCoderankViewModelInfo);
            }
         //   temp.Take(3);

            foreach (var Items in temp.Take(3).ToList())
            {

                BarCoderankViewModel barCoderankViewModelInfo = new BarCoderankViewModel();
                barCoderankViewModelInfo.SCHOOL_NO = SchoolNO;
                barCoderankViewModelInfo.ALLRANK = (int)Items.Rank;
                barCoderankViewModelInfo.CLASS_NO = Items.CLASS_NO;
                barCoderankViewModelInfo.SEAT_NO = Items.SEAT_NO;
                barCoderankViewModelInfo.SNAME = Items.NAME;
                barCoderankViewModelInfo.Total_M = ((Items.Total_M / 1000).ToString("#,0.00"));
                barCoderankViewModelInfo.Ranktype = "RunRank";
                barCoderankViewModelsItem.Add(barCoderankViewModelInfo);
            }

            foreach (var Items in RankBOOK.OrderBy(x => x.BOOK_QTYRANK).Take(3).ToList())
            {

                BarCoderankViewModel barCoderankViewModelInfo = new BarCoderankViewModel();
                barCoderankViewModelInfo.SCHOOL_NO = SchoolNO;
                barCoderankViewModelInfo.ALLRANK = (int)Items.BOOK_QTYRANK;
                barCoderankViewModelInfo.CLASS_NO = Items.CLASS_NO;
                barCoderankViewModelInfo.SEAT_NO = Items.SEAT_NO;
                barCoderankViewModelInfo.SNAME = Items.SNAME;
                barCoderankViewModelInfo.ALLNumberInfo = Items.BOOK_QTY;
                barCoderankViewModelInfo.Ranktype = "BookRank";
                barCoderankViewModelsItem.Add(barCoderankViewModelInfo);
            }
            return View(barCoderankViewModelsItem);

        }
         public ActionResult LeaderIndex(BarcCodeMyCashIndexViewModel model)
        {

            AWA003QueryViewModel aWA003model = new AWA003QueryViewModel();
            aWA003model.OrdercColumn = "SUMCASH_AVAILABLE";
            AWA003Controller aWA003 = new AWA003Controller();
            string SchoolNO = UserProfileHelper.GetSchoolNo();
            if (!string.IsNullOrWhiteSpace(model.WhereSchoolNo))
            {
                SchoolNO = model.WhereSchoolNo;
            }
            else
            {

                model.WhereSchoolNo = SchoolNO;
            }
            ViewBag.WhereKeyword = "";
            ViewBag.FROMACTION = "";
            ViewBag.TimeoutSeconds = null;
            if (!string.IsNullOrWhiteSpace(model.WhereKeyword))
            {
                ViewBag.WhereKeyword = model.WhereKeyword;
               
            }
            if (!string.IsNullOrWhiteSpace(model.FROMACTION))
            {
                ViewBag.FROMACTION = model.FROMACTION;
         
            }
            if (model.TimeoutSeconds != null)
            {
                ViewBag.TimeoutSeconds = model.TimeoutSeconds;

            }


            IQueryable<HRMT01QTY> aWA003HRMT01QTY = aWA003.QeuryDataRank(aWA003model);
         //   var AWAT02List = aWA003HRMT01QTY.OrderByDescending(a => a.den_rank).Take(10).ToList();
            //--累計酷幣排行

          //  var aWA003HRMT01List = aWA003HRMT01QTY.OrderByDescending(a => a.SUMCASHden_rank).Take(10).ToList();
            //----現有總資產排行
            return View(aWA003HRMT01QTY);
        }
            public ActionResult Index21()
        {
            this.Shared("酷幣查詢與獎品對兌");

            user = UserProfileHelper.Get();
            if (user != null)
            {
                if (user.USER_KEY == UserType.Admin)
                {
                    ViewBag.Gift = true;
                }
            }
            return View();
        }

        public ActionResult Index3()
        {
            this.Shared("酷幣查詢與獎品對兌");

            user = UserProfileHelper.Get();
            ViewBag.Gift = true;
            return View();
        }
        [AllowAnonymous]
        public ActionResult Index4(BarcCodeMyCashIndexViewModel model)
        {
            this.Shared("酷幣查詢與獎品對兌");

            user = UserProfileHelper.Get();
            ViewBag.Gift = true;
            return View();
        }
        [AllowAnonymous]
        public ActionResult Index5(BarcCodeMyCashIndexViewModel model)
        {
            this.Shared("酷幣查詢與獎品對兌");

            user = UserProfileHelper.Get();
            ViewBag.Gift = true;
            return View();
        }

        public ActionResult _PageContent(BarcCodeMyCashIndexViewModel model)
        {
            user = UserProfileHelper.Get();
            this.Shared("酷幣查詢與獎品對兌");
            int hrmt25Count = 0;

            if (user != null)
            {
                hrmt25Count = db.HRMT25.Where(x => x.USER_NO == user.USER_NO && x.SCHOOL_NO == user.SCHOOL_NO && x.ROLE_ID == "4").Count();
            }
            if (ViewBag.ATMUID == "T")
            {
                hrmt25Count = 1;
            }

            var REF = db.BDMT02_REF.Where(A => A.BRE_NO == "BarcCodeMyCash" && A.DATA_CODE == "BarcCodeMyCash_Mode" && A.DATA_TYPE == "ALL" && A.SCHOOL_NO == model.WhereSchoolNo && A.IS_SELECT == 1).FirstOrDefault();
     

            model.DelayTime = REF?.DelayTime;
            model.ChangeMode = REF?.CONTENT_VAL;
          
            ViewBag.hrmt25Count = hrmt25Count;
        
      
            if (string.IsNullOrWhiteSpace(model.WhereSchoolNo))
            {
                model.WhereSchoolNo = SCHOOL_NO;
            }

            if (string.IsNullOrWhiteSpace(model.WhereSchoolNo))
            {
                TempData["StatusMessage"] = "此學號無效。處理方式：請先選學校，或將瀏灠器全部關閉後重新操作一次";
                return PartialView(model);
            }

            //酷幣排行榜
            var RankCashSort = from w1 in db.AWAT01
                               join h1 in db.HRMT01
                               on new { w1.SCHOOL_NO, w1.USER_NO } equals new { h1.SCHOOL_NO, h1.USER_NO }
                               into h1Table
                               from hh1 in h1Table.DefaultIfEmpty()
                               where w1.SCHOOL_NO == model.WhereSchoolNo && w1.CASH_ALL > 0 && (!UserStaus.NGUserStausList.Contains(hh1.USER_STATUS))
                               select new HRMT01QTY { CLASS_NO = hh1.CLASS_NO, NAME = hh1.SNAME, USER_NO = hh1.USER_NO, QTY = w1.CASH_ALL, CASH_ALL = w1.CASH_ALL, CASH_AVAILABLE = w1.CASH_AVAILABLE, };

            var AWAT01List = RankCashSort.OrderByDescending(a => a.CASH_ALL).Take(10).ToList();
            string SortBoard = string.Empty;
            string SortBoard2 = string.Empty;
            short i = 1;
            foreach (var t01 in AWAT01List)
            {
                int deAMT = 0;
                List<AWAT10> AT10list = new List<AWAT10>();
                AT10list = db.AWAT10.Where(x => x.STATUS == "1" && x.SCHOOL_NO == model.WhereSchoolNo && x.USER_NO == t01.USER_NO).ToList();
                if (AT10list.Count() > 0)
                {
                    foreach (var item in AT10list)
                    {
                        if (item.AMT != null)

                        {
                            deAMT += (int)item.AMT;
                        }
                    }
                }
                else
                {
                    int? CASH_AVAILABLE = 0;
                    CASH_AVAILABLE = db.AWAT01.Where(x => x.SCHOOL_NO == model.WhereSchoolNo && x.USER_NO == t01.USER_NO).Select(x => x.CASH_AVAILABLE).FirstOrDefault();
                    deAMT = (int)CASH_AVAILABLE;
                }
                if (i <= 3) SortBoard += string.Format("   第{0}名：{1} {2}  ", i, t01.CLASS_NO, t01.NAME);
                SortBoard2 += string.Format("第{0}名：{1}({2}點)　", i, t01.NAME, deAMT);
                i++;
            }
            ViewBag.SortBoard = SortBoard;
            ViewBag.SortBoard2 = SortBoard2;

            HRMT01 FindUser = db.HRMT01.Include("HRMT25").Where(a => a.USER_NO == model.WhereKeyword && a.SCHOOL_NO == model.WhereSchoolNo && a.USER_TYPE != UserType.Guest && a.USER_STATUS ==UserStaus.Enabled).FirstOrDefault();
            //    if (FindUser.USER_TYPE == "S")
            //   {
            //找學生卡對應
            if (FindUser == null && string.IsNullOrWhiteSpace(model.WhereKeyword) == false)
            {
                FindUser = db.HRMT01.Include("HRMT25").Where(a => a.CARD_NO == model.WhereKeyword && a.SCHOOL_NO == model.WhereSchoolNo).FirstOrDefault();
            }

            if (FindUser == null && string.IsNullOrWhiteSpace(model.WhereKeyword) == false)
            {
                TempData["StatusMessage"] = "此學號無效";
                model.WhereKeyword = "";
                return PartialView(model);
            }

            if (FindUser == null)
            {
                return PartialView(model);
            }

            #region PageContentB

            model.ShowStep2 = true;
            model.UserNAME = FindUser.SNAME;
            //借書量
            SECI05Service ServiceS5 = new SECI05Service();
            SECI05IndexViewModel BookModel = new SECI05IndexViewModel();
            BookModel.WhereSCHOOL_NO = FindUser.SCHOOL_NO;
            BookModel.WhereUSER_NO = FindUser.USER_NO;
            BookModel = ServiceS5.GetBorrowData(BookModel, null, ref db);
            if (BookModel.MyBorrow != null)
            {
                model.UserBookQty = BookModel.MyBorrow.THIS_SESEM_QTY;
                model.AllBookQty = BookModel.MyBorrow.ALL_QTY;
                model.MonthBookQty = BookModel.MyBorrow.THIS_MM_QTY;
            }

            //學生酷幣
            AWAT01 tCASH = db.AWAT01.Where(a => a.SCHOOL_NO == FindUser.SCHOOL_NO && a.USER_NO == FindUser.USER_NO).FirstOrDefault();
            if (tCASH != null)
            {
                if (tCASH.CASH_AVAILABLE.HasValue) model.UserCash = tCASH.CASH_AVAILABLE.Value;
            }

            //定存
            AWAI07Service Service = new AWAI07Service();
            AWAI07IndexViewModel modelA7 = new AWAI07IndexViewModel();
            modelA7.Search = new AWAI07SearchViewModel();
            modelA7.Search.WhereSCHOOL_NO = FindUser.SCHOOL_NO;
            modelA7.Search.WhereUSER_NO = FindUser.USER_NO;
            modelA7.Search.WhereSTATUS = AWAT10.StatusVal.SetUp;
            Service.GetListData(modelA7, ref db);
            if (modelA7.ListData != null)
            {
                var SumAMT = modelA7.ListData.Sum(a => a.AMT);
                if (SumAMT.HasValue) model.UserAWAI07 = SumAMT.Value;
            }

            //運動
            ADDI11Service ServiceA11 = new ADDI11Service();
            ADDI11MyRunLogViewModel modelA11 = new ADDI11MyRunLogViewModel();
            modelA11.WhereSCHOOL_NO = FindUser.SCHOOL_NO;
            modelA11.WhereUSER_NO = FindUser.USER_NO;
            modelA11 = ServiceA11.GetMyRunLog(modelA11, user, ref db);

            model.RUN_TOTAL_METER = (modelA11.MyRunRank != null) ? modelA11.MyRunRank.RUN_TOTAL_METER ?? 0 : 0;
            // 下次升級
            string upgraderesultStr;
            model.RUN_UPGRADE_METER = ServiceA11.NextUpgradeRunningMiles(model.RUN_UPGRADE_METER, ref db, out upgraderesultStr);
            ViewBag.RUN_UPGRADE_RESULTSTR = upgraderesultStr;

            //取出可對兌獎品

            //閱讀等級
            ADDT09 a9 = db.ADDT09.Where(a => a.SCHOOL_NO == FindUser.SCHOOL_NO && a.USER_NO == FindUser.USER_NO).FirstOrDefault();
            byte L_ID = 0;
            if (a9 != null) L_ID = (a9.LEVEL_ID ?? 0);

            //閱讀認證.等級
            var PassportLEVEL = db.ADDT04.Where(p => p.USER_NO == FindUser.USER_NO && p.SCHOOL_NO == FindUser.SCHOOL_NO && p.PASS_YN == "Y").Count();

            string sSQL = @"SELECT  A.*
            FROM AWAT02 A (NOLOCK)
            WHERE (A.SCHOOL_NO=@SCHOOL_NO OR A.SCHOOL_NO='ALL')
            AND A.AWARD_STATUS<>'N' AND A.QTY_STORAGE>0
            AND A.SDATETIME <= GETDATE()
            AND A.EDATETIME >= GETDATE()
            AND A.COST_CASH <= @CASH_AVAILABLE
            AND (A.READ_LEVEL IS  NULL OR A.READ_LEVEL<=@LEVEL_ID)
            AND (A.PASSPORT_LEVEL IS  NULL OR A.PASSPORT_LEVEL<=@PassportLEVEL)
            AND (ISNULL(A.BUY_PERSON_YN,'0')='0' OR (ISNULL(A.BUY_PERSON_YN,'0')='1'
            AND (SELECT COUNT(*) FROM REFT01 B (NOLOCK) WHERE B.REF_KEY=A.AWARD_NO AND B.REF_TABLE='AWAT02' AND B.SCHOOL_NO=@SCHOOL_NO AND B.USER_NO=@USER_NO)>0
            ))
            ORDER BY A.COST_CASH DESC";
            var QTemp = db.Database.Connection.Query<AWAT02>(sSQL
                , new
                {
                    SCHOOL_NO = FindUser.SCHOOL_NO,
                    CASH_AVAILABLE = (tCASH?.CASH_AVAILABLE ?? 0),
                    LEVEL_ID = L_ID,
                    PassportLEVEL = PassportLEVEL,
                    USER_NO = FindUser.USER_NO
                });

            model.AWAT02List = QTemp.ToList();

            sSQL = @"select a.BOOK_QTY,b.LEVEL_DESC ,b.LEVEL_ID
						 ,Case when a.LEVEL_ID<10 Then (select Top 1 L.LEVEL_QTY from ADDT07 L (nolock) Where L.LEVEL_ID>a.LEVEL_ID order by L.LEVEL_ID)-a.BOOK_QTY
						       else 0 end as UNLEVEL_QTY
						 from ADDT09 a (nolock)
						 left outer join ADDT08 b (nolock) on a.SCHOOL_NO=b.SCHOOL_NO and a.LEVEL_ID=b.LEVEL_ID
						 where a.SCHOOL_NO=@SCHOOL_NO  and a.USER_NO=@USER_NO ";
            model.MyData = db.Database.Connection.Query<ZZZI09MyDataViewModel>(sSQL
                , new
                {
                    SCHOOL_NO = FindUser.SCHOOL_NO,
                    USER_NO = FindUser.USER_NO,
                }).FirstOrDefault();

            // 等級分數計算
            // 公式: 目前酷幣值 + 定存酷幣值 + 每月的借書量 x 100 + 跑步里程 x 20
            model.point = LogicCenter.CaculateMyATM_point(model.UserCash,
                Convert.ToInt32(model.UserAWAI07),
                model.MonthBookQty,
                Convert.ToDouble(model.RUN_TOTAL_METER ?? 0) / 1000.000);
            model.Level = LogicCenter.CaculateMyATM_Level(model.point);
            //  }
            if (model.MyData == null) model.MyData = new ZZZI09MyDataViewModel();
            string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
            ViewBag.ATMUID = FindUser.USER_TYPE;
            bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
            if (AppMode)
            {
                return PartialView("_PageContentForAPP", model);
            }
            else
            {
                model.WhereKeyword = model.WhereKeyword;
                return PartialView("_PageContentB", model);
            }

            #endregion PageContentB
        }

        public ActionResult _PageContent4(BarcCodeMyCashIndexViewModel model)
        {
            user = UserProfileHelper.Get();
            var REF = db.BDMT02_REF.Where(A => A.BRE_NO == "BarcCodeMyCash" && A.DATA_CODE == "BarcCodeMyCash_Mode" && A.DATA_TYPE == "ALL" && A.SCHOOL_NO == model.WhereSchoolNo && A.IS_SELECT == 1).FirstOrDefault();
            int hrmt25Count = 0;
            if (user != null)
            {
                hrmt25Count = db.HRMT25.Where(x => x.USER_NO == user.USER_NO && x.SCHOOL_NO == user.SCHOOL_NO && x.ROLE_ID == "4").Count();
            }
            if (ViewBag.ATMUID == "T")
            {
                hrmt25Count = 1;
            }

            model.DelayTime = REF?.DelayTime;
            model.ChangeMode = REF?.CONTENT_VAL;
            ViewBag.hrmt25Count = hrmt25Count;
            this.Shared("酷幣查詢與獎品對兌");
    
            if (string.IsNullOrWhiteSpace(model.WhereSchoolNo))
            {
                model.WhereSchoolNo = SCHOOL_NO;
            }

            if (string.IsNullOrWhiteSpace(model.WhereSchoolNo))
            {
                TempData["StatusMessage"] = "此學號無效。處理方式：請先選學校，或將瀏灠器全部關閉後重新操作一次";
                return PartialView(model);
            }
            
            //酷幣排行榜
            //var RankCashSort = from w1 in db.AWAT01
            //                   join h1 in db.HRMT01
            //                   on new { w1.SCHOOL_NO, w1.USER_NO } equals new { h1.SCHOOL_NO, h1.USER_NO }
            //                   into h1Table
            //                   from hh1 in h1Table.DefaultIfEmpty()
            //                   where w1.SCHOOL_NO == model.WhereSchoolNo && w1.CASH_ALL > 0 && (!UserStaus.NGUserStausList.Contains(hh1.USER_STATUS))
            //                   select new HRMT01QTY { CLASS_NO = hh1.CLASS_NO, NAME = hh1.SNAME, USER_NO = hh1.USER_NO, QTY = w1.CASH_ALL, CASH_ALL = w1.CASH_ALL, CASH_AVAILABLE = w1.CASH_AVAILABLE, };
            //   select Sum(w.AMT) from AWAT10 w (nolock) where w.STATUS=1 and w.SCHOOL_NO=w1.SCHOOL_NO and w.USER_NO=w1.USER_NO and w.STATUS =1
            AWA003Controller aWA003 = new AWA003Controller();
            AWA003QueryViewModel aWA003model = new AWA003QueryViewModel();
            aWA003model.OrdercColumn = "SUMCASH_AVAILABLE";

            if (!string.IsNullOrWhiteSpace(model.WhereSchoolNo))
            {
                aWA003model.whereSchoolNo = model.WhereSchoolNo;
            }
            IQueryable<HRMT01QTY> aWA003HRMT01QTY = aWA003.QeuryDataAwat10Rank(aWA003model);


            var AWAT01List = aWA003HRMT01QTY.OrderByDescending(a => a.SUMCASH_AVAILABLE).Take(10).ToList();
            //if (user != null)
            //{
            //    model.SUMCASH_AVAILABLE = aWA003HRMT01QTY.Where(X => X.USER_NO == user.USER_NO && X.SCHOOL_NO == user.SCHOOL_NO).Select(x => x.SUMCASH_AVAILABLE).FirstOrDefault();
            //}
            aWA003model.OrdercColumn = "CASH_ALL";
            aWA003HRMT01QTY = aWA003.QeuryDataAwat10Rank(aWA003model);
            var AWAT02List = aWA003HRMT01QTY.OrderByDescending(a => a.CASH_ALL).Take(10).ToList();

            string SortBoard = string.Empty;
            string SortBoard2 = string.Empty;
            short i = 1;
            foreach (var t01 in AWAT01List)
            {
                //int deAMT = 0;
                //List<AWAT10> AT10list = new List<AWAT10>();
                //AT10list = db.AWAT10.Where(x => x.STATUS == "1" && x.SCHOOL_NO == model.WhereSchoolNo && x.USER_NO == t01.USER_NO).ToList();
                //if (AT10list.Count() > 0)
                //{
                //    foreach (var item in AT10list)
                //    {
                //        if (item.AMT != null)

                //        {
                //            deAMT += (int)item.AMT;
                //        }
                //    }
                //}
                //else
                //{
                //    int? CASH_AVAILABLE = 0;
                //    CASH_AVAILABLE = db.AWAT01.Where(x => x.SCHOOL_NO == model.WhereSchoolNo && x.USER_NO == t01.USER_NO).Select(x => x.CASH_AVAILABLE).FirstOrDefault();
                //    deAMT = (int)CASH_AVAILABLE;
                //}

                SortBoard2 += string.Format("第{0}名：{1}({2}點)　", i, t01.NAME, t01.SUMCASH_AVAILABLE);
                i++;
            }
            i = 1;
            foreach (var t01 in AWAT02List)
            {

                if (i <= 3) { SortBoard += string.Format("   第{0}名：{1} {2}  ", i, t01.CLASS_NO, t01.NAME); i++; }
            }
            ViewBag.AWAT01List = AWAT02List;
            ViewBag.SortBoard = SortBoard;
          ViewBag.SortBoard2 = SortBoard2;

            HRMT01 FindUser = db.HRMT01.Include("HRMT25").Where(a => a.USER_NO == model.WhereKeyword && a.SCHOOL_NO == model.WhereSchoolNo && a.USER_TYPE != UserType.Guest && a.USER_STATUS ==UserStaus.Enabled).FirstOrDefault();
            //    if (FindUser.USER_TYPE == "S")
            //   {
            //找學生卡對應
            if (FindUser == null && string.IsNullOrWhiteSpace(model.WhereKeyword) == false)
            {
                FindUser = db.HRMT01.Include("HRMT25").Where(a => a.CARD_NO == model.WhereKeyword && a.SCHOOL_NO == model.WhereSchoolNo && a.USER_TYPE != UserType.Guest && a.USER_STATUS == UserStaus.Enabled).FirstOrDefault();
            }

            if (FindUser == null && string.IsNullOrWhiteSpace(model.WhereKeyword) == false)
            {
                TempData["StatusMessage"] = "此學號無效";
                model.WhereKeyword = "";
                return PartialView(model);
            }

            if (FindUser == null)
            {
                return PartialView(model);
            }
            if (FindUser != null)
            {
                model.SUMCASH_AVAILABLE = aWA003HRMT01QTY.Where(X => X.USER_NO == FindUser.USER_NO && X.SCHOOL_NO == FindUser.SCHOOL_NO).Select(x => x.SUMCASH_AVAILABLE).FirstOrDefault();
            }

            #region PageContentB

            model.ShowStep2 = true;
            model.UserNAME = FindUser.SNAME;
            model.CLASS_NO = FindUser.CLASS_NO;
            //借書量
            SECI05Service ServiceS5 = new SECI05Service();
            SECI05IndexViewModel BookModel = new SECI05IndexViewModel();
            BookModel.WhereSCHOOL_NO = FindUser.SCHOOL_NO;
            BookModel.WhereUSER_NO = FindUser.USER_NO;
            BookModel = ServiceS5.GetBorrowDataATM(BookModel, null, ref db);
            if (BookModel.MyBorrow != null)
            {
                model.UserBookQty = BookModel.MyBorrow.THIS_SESEM_QTY;
                model.AllBookQty = BookModel.MyBorrow.ALL_QTY;
                model.MonthBookQty = BookModel.MyBorrow.THIS_MM_QTY;
            }

            //學生酷幣
            AWAT01 tCASH = db.AWAT01.Where(a => a.SCHOOL_NO == FindUser.SCHOOL_NO && a.USER_NO == FindUser.USER_NO).FirstOrDefault();
            if (tCASH != null)
            {
                if (tCASH.CASH_AVAILABLE.HasValue) model.UserCash = tCASH.CASH_AVAILABLE.Value;
            }

            //定存
            AWAI07Service Service = new AWAI07Service();
            AWAI07IndexViewModel modelA7 = new AWAI07IndexViewModel();
            modelA7.Search = new AWAI07SearchViewModel();
            modelA7.Search.WhereSCHOOL_NO = FindUser.SCHOOL_NO;
            modelA7.Search.WhereUSER_NO = FindUser.USER_NO;
            modelA7.Search.WhereSTATUS = AWAT10.StatusVal.SetUp;
            Service.GetListData(modelA7, ref db);
            if (modelA7.ListData != null)
            {
                var SumAMT = modelA7.ListData.Sum(a => a.AMT);
                if (SumAMT.HasValue) model.UserAWAI07 = SumAMT.Value;
            }

            //運動
            ADDI11Service ServiceA11 = new ADDI11Service();
            ADDI11MyRunLogViewModel modelA11 = new ADDI11MyRunLogViewModel();
            modelA11.WhereSCHOOL_NO = FindUser.SCHOOL_NO;
            modelA11.WhereUSER_NO = FindUser.USER_NO;
            modelA11 = ServiceA11.GetMyRunLogATM(modelA11, user, ref db);

            model.RUN_TOTAL_METER = (modelA11.MyRunRank != null) ? modelA11.MyRunRank.RUN_TOTAL_METER ?? 0 : 0;
            // 下次升級
            string upgraderesultStr;
            model.RUN_UPGRADE_METER = ServiceA11.NextUpgradeRunningMiles(model.RUN_UPGRADE_METER, ref db, out upgraderesultStr);
            ViewBag.RUN_UPGRADE_RESULTSTR = upgraderesultStr;

            //取出可對兌獎品

            //閱讀等級
            ADDT09 a9 = db.ADDT09.Where(a => a.SCHOOL_NO == FindUser.SCHOOL_NO && a.USER_NO == FindUser.USER_NO).FirstOrDefault();
            byte L_ID = 0;
            if (a9 != null) L_ID = (a9.LEVEL_ID ?? 0);

            //閱讀認證.等級
            var PassportLEVEL = db.ADDT04.Where(p => p.USER_NO == FindUser.USER_NO && p.SCHOOL_NO == FindUser.SCHOOL_NO && p.PASS_YN == "Y").Count();

            string sSQL = @"SELECT  A.*
            FROM AWAT02 A (NOLOCK)
            WHERE (A.SCHOOL_NO=@SCHOOL_NO OR A.SCHOOL_NO='ALL') AND A.FULLSCREEN_YN!='Y'
            AND A.AWARD_STATUS<>'N' AND A.QTY_STORAGE>0
            AND A.SDATETIME <= GETDATE()
            AND A.EDATETIME >= GETDATE()
            AND A.COST_CASH <= @CASH_AVAILABLE
            AND (A.READ_LEVEL IS  NULL OR A.READ_LEVEL<=@LEVEL_ID)
            AND (A.PASSPORT_LEVEL IS  NULL OR A.PASSPORT_LEVEL<=@PassportLEVEL)
            AND (ISNULL(A.BUY_PERSON_YN,'0')='0' OR (ISNULL(A.BUY_PERSON_YN,'0')='Y'
            AND (SELECT COUNT(*) FROM REFT01 B (NOLOCK) WHERE B.REF_KEY=A.AWARD_NO AND B.REF_TABLE='AWAT02' AND B.SCHOOL_NO=@SCHOOL_NO AND B.USER_NO=@USER_NO)>0
            ))
            ORDER BY A.COST_CASH DESC";
            var QTemp = db.Database.Connection.Query<AWAT02>(sSQL
                , new
                {
                    SCHOOL_NO = FindUser.SCHOOL_NO,
                    CASH_AVAILABLE = (tCASH?.CASH_AVAILABLE ?? 0),
                    LEVEL_ID = L_ID,
                    PassportLEVEL = PassportLEVEL,
                    USER_NO = FindUser.USER_NO
                });

            model.AWAT02List = QTemp.ToList();

            sSQL = @"select a.BOOK_QTY,b.LEVEL_DESC ,b.LEVEL_ID
						 ,Case when a.LEVEL_ID<10 Then (select Top 1 L.LEVEL_QTY from ADDT07 L (nolock) Where L.LEVEL_ID>a.LEVEL_ID order by L.LEVEL_ID)-a.BOOK_QTY
						       else 0 end as UNLEVEL_QTY
						 from ADDT09 a (nolock)
						 left outer join ADDT08 b (nolock) on a.SCHOOL_NO=b.SCHOOL_NO and a.LEVEL_ID=b.LEVEL_ID
						 where a.SCHOOL_NO=@SCHOOL_NO  and a.USER_NO=@USER_NO ";
            model.MyData = db.Database.Connection.Query<ZZZI09MyDataViewModel>(sSQL
                , new
                {
                    SCHOOL_NO = FindUser.SCHOOL_NO,
                    USER_NO = FindUser.USER_NO,
                }).FirstOrDefault();

            // 等級分數計算
            // 公式: 目前酷幣值 + 定存酷幣值 + 每月的借書量 x 100 + 跑步里程 x 20
            model.point = LogicCenter.CaculateMyATM_point(model.UserCash,
                Convert.ToInt32(model.UserAWAI07),
                model.MonthBookQty,
                Convert.ToDouble(model.RUN_TOTAL_METER ?? 0) / 1000.000);
            model.Level = LogicCenter.CaculateMyATM_Level(model.point);
            //  }
            if (model.MyData == null) model.MyData = new ZZZI09MyDataViewModel();
            string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
            ViewBag.ATMUID = FindUser.USER_TYPE;
            bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
            if (AppMode)
            {
                return PartialView("_PageContentForAPP", model);
            }
            else
            {
                model.WhereKeyword = model.WhereKeyword;
                return PartialView("_PageContent4B", model);
            }

            #endregion PageContentB
        }

        public ActionResult _PageContent5(BarcCodeMyCashIndexViewModel model)
        {
            int hrmt25Count = 0;
            this.Shared("酷幣查詢與獎品對兌");
            user = UserProfileHelper.Get();
            if (user != null)
            {
                hrmt25Count = db.HRMT25.Where(x => x.USER_NO == user.USER_NO && x.SCHOOL_NO == user.SCHOOL_NO && x.ROLE_ID == "4").Count();
            }
            if (ViewBag.ATMUID == "T")
            {
                hrmt25Count = 1;
            }

            var REF = db.BDMT02_REF.Where(A => A.BRE_NO == "BarcCodeMyCash" && A.DATA_CODE == "BarcCodeMyCash_Mode" && A.DATA_TYPE == "ALL" && A.SCHOOL_NO == model.WhereSchoolNo && A.IS_SELECT == 1).FirstOrDefault();


            model.DelayTime = REF?.DelayTime;
            model.ChangeMode = REF?.CONTENT_VAL;
            ViewBag.hrmt25Count = hrmt25Count;
           
            if (string.IsNullOrWhiteSpace(model.WhereSchoolNo))
            {
                model.WhereSchoolNo = SCHOOL_NO;
            }

            if (string.IsNullOrWhiteSpace(model.WhereSchoolNo))
            {
                TempData["StatusMessage"] = "此學號無效。處理方式：請先選學校，或將瀏灠器全部關閉後重新操作一次";
                return PartialView(model);
            }

            //酷幣排行榜
            //var RankCashSort = from w1 in db.AWAT01
            //                   join h1 in db.HRMT01
            //                   on new { w1.SCHOOL_NO, w1.USER_NO } equals new { h1.SCHOOL_NO, h1.USER_NO }
            //                   into h1Table
            //                   from hh1 in h1Table.DefaultIfEmpty()
            //                   where w1.SCHOOL_NO == model.WhereSchoolNo && w1.CASH_ALL > 0 && (!UserStaus.NGUserStausList.Contains(hh1.USER_STATUS))
            //                   select new HRMT01QTY { CLASS_NO = hh1.CLASS_NO, NAME = hh1.SNAME, USER_NO = hh1.USER_NO, QTY = w1.CASH_ALL, CASH_ALL = w1.CASH_ALL, CASH_AVAILABLE = w1.CASH_AVAILABLE, };
            //酷幣簡易需要
            AWA003QueryViewModel aWA003model = new AWA003QueryViewModel();
            aWA003model.OrdercColumn = "SUMCASH_AVAILABLE";
            AWA003Controller aWA003 = new AWA003Controller();
            if (!string.IsNullOrWhiteSpace(model.WhereSchoolNo))
            {
                aWA003model.whereSchoolNo = model.WhereSchoolNo;
            }
            IQueryable<HRMT01QTY> aWA003HRMT01QTY = aWA003.QeuryDataAwat10Rank(aWA003model);
            var AWAT01List = aWA003HRMT01QTY.OrderByDescending(a => a.SUMCASH_AVAILABLE).Take(10).ToList();
            aWA003model.OrdercColumn = "CASH_ALL";
           
            aWA003HRMT01QTY = aWA003.QeuryDataAwat10Rank(aWA003model);

            var AWAT02List = aWA003HRMT01QTY.OrderByDescending(a => a.CASH_ALL).Take(10).ToList();
            string SortBoard = string.Empty;
            string SortBoard2 = string.Empty;
            short i = 1;
            foreach (var t01 in AWAT01List)
            {
                //    int deAMT = 0;
                //    List<AWAT10> AT10list = new List<AWAT10>();
                //    AT10list = db.AWAT10.Where(x => x.STATUS == "1" && x.SCHOOL_NO == model.WhereSchoolNo && x.USER_NO == t01.USER_NO).ToList();
                //    if (AT10list.Count() > 0)
                //    {
                //        foreach (var item in AT10list)
                //        {
                //            if (item.AMT != null)

                //            {
                //                deAMT += (int)item.AMT;
                //            }
                //        }
                //    }
                //    else
                //    {
                //        int? CASH_AVAILABLE = 0;
                //        CASH_AVAILABLE = db.AWAT01.Where(x => x.SCHOOL_NO == model.WhereSchoolNo && x.USER_NO == t01.USER_NO).Select(x => x.CASH_AVAILABLE).FirstOrDefault();
                //        deAMT = (int)CASH_AVAILABLE;
                //    }

                SortBoard2 += string.Format("第{0}名：{1}({2}點)　", i, t01.NAME, t01.SUMCASH_AVAILABLE);
                i++;
            }
            i = 1;
            //foreach (var t01 in AWAT02List)
            //{

            //    if (i <= 3) { SortBoard += string.Format("   第{0}名：{1} {2}  ", i, t01.CLASS_NO, t01.NAME); i++; }
            //}
            ViewBag.AWAT01List = AWAT02List;
            ViewBag.SortBoard = SortBoard;
            ViewBag.SortBoard2 = SortBoard2;

            HRMT01 FindUser = db.HRMT01.Include("HRMT25").Where(a => a.USER_NO == model.WhereKeyword && a.SCHOOL_NO == model.WhereSchoolNo && a.USER_TYPE != UserType.Guest && a.USER_STATUS == UserStaus.Enabled).FirstOrDefault();
            //    if (FindUser.USER_TYPE == "S")
            //   {
            //找學生卡對應
            if (FindUser == null && string.IsNullOrWhiteSpace(model.WhereKeyword) == false)
            {
                // hRMT01QTYItem.SUMCASH_AVAILABLE;
                FindUser = db.HRMT01.Include("HRMT25").Where(a => a.CARD_NO == model.WhereKeyword && a.SCHOOL_NO == model.WhereSchoolNo && a.USER_TYPE != UserType.Guest && a.USER_STATUS == UserStaus.Enabled).FirstOrDefault();
            }

            if (FindUser == null && string.IsNullOrWhiteSpace(model.WhereKeyword) == false)
            {
                TempData["StatusMessage"] = "此學號無效";
                model.WhereKeyword = "";
                return PartialView(model);
            }

            if (FindUser == null)
            {
                return PartialView(model);
            }
            if (FindUser != null)
            {
                AWAI07Service Service1 = new AWAI07Service();

                //AWAI07IndexViewModel model1 = new AWAI07IndexViewModel();
                //model1.Search = new AWAI07SearchViewModel();
                //model1.Search.WhereSCHOOL_NO = FindUser.USER_NO;
                //model1.Search.WhereUSER_NO = FindUser.SCHOOL_NO;
                //model1 = Service1.GetListData(model1, ref db);
             model.SUMCASH_AVAILABLE = aWA003HRMT01QTY.Where(X => X.USER_NO == FindUser.USER_NO && X.SCHOOL_NO == FindUser.SCHOOL_NO).Select(x => x.SUMCASH_AVAILABLE).FirstOrDefault();
            }
            //AWA003QueryViewModel Vmodel = new AWA003QueryViewModel();
            //IQueryable<HRMT01QTY> HRMT01QTYList;
            //AWA003Controller aWA003 = new AWA003Controller();
            //Vmodel.whereKeyword = FindUser.USER_NO;
            //HRMT01QTYList = aWA003.QeuryData(Vmodel);
            //HRMT01QTY hRMT01QTYItem = new HRMT01QTY();
            //hRMT01QTYItem = HRMT01QTYList.FirstOrDefault();
            //model.SUMCASH_AVAILABLE = hRMT01QTYItem.SUMCASH_AVAILABLE;

            #region PageContentB

            model.ShowStep2 = true;
            model.UserNAME = FindUser.SNAME;
            model.CLASS_NO = FindUser.CLASS_NO;
           

            //學生酷幣簡易需要
            AWAT01 tCASH = db.AWAT01.Where(a => a.SCHOOL_NO == FindUser.SCHOOL_NO && a.USER_NO == FindUser.USER_NO).FirstOrDefault();
            if (tCASH != null)
            {
                if (tCASH.CASH_AVAILABLE.HasValue) model.UserCash = tCASH.CASH_AVAILABLE.Value;
            }

            //定存簡易需要
            AWAI07Service Service = new AWAI07Service();
            AWAI07IndexViewModel modelA7 = new AWAI07IndexViewModel();
            modelA7.Search = new AWAI07SearchViewModel();
            modelA7.Search.WhereSCHOOL_NO = FindUser.SCHOOL_NO;
            modelA7.Search.WhereUSER_NO = FindUser.USER_NO;
            modelA7.Search.WhereSTATUS = AWAT10.StatusVal.SetUp;
            Service.GetListData(modelA7, ref db);
            if (modelA7.ListData != null)
            {
                var SumAMT = modelA7.ListData.Sum(a => a.AMT);
                if (SumAMT.HasValue) model.UserAWAI07 = SumAMT.Value;
            }


            

     

            // 等級分數計算
            // 公式: 目前酷幣值 + 定存酷幣值 + 每月的借書量 x 100 + 跑步里程 x 20
            model.point = LogicCenter.CaculateMyATM_point(model.UserCash,
                Convert.ToInt32(model.UserAWAI07),
                model.MonthBookQty,
                Convert.ToDouble(model.RUN_TOTAL_METER ?? 0) / 1000.000);
            model.Level = LogicCenter.CaculateMyATM_Level(model.point);
            //  }
            if (model.MyData == null) model.MyData = new ZZZI09MyDataViewModel();
            string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
            ViewBag.ATMUID = FindUser.USER_TYPE;
            bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
            if (AppMode)
            {
                return PartialView("_PageContentForAPP", model);
            }
            else
            {
                model.WhereKeyword = model.WhereKeyword;
                return PartialView("_PageContent5B", model);
            }

            #endregion PageContentB
        }

        public ActionResult _PageContent3(BarcCodeMyCashIndexViewModel model)
        {
            this.Shared("酷幣查詢與獎品對兌");
            user = UserProfileHelper.Get();
            int hrmt25Count = 0;
            if (user != null)
            {
                hrmt25Count = db.HRMT25.Where(x => x.USER_NO == user.USER_NO && x.SCHOOL_NO == user.SCHOOL_NO && x.ROLE_ID == "4").Count();
            }
            if (ViewBag.ATMUID == "T")
            {
                hrmt25Count = 1;
            }
            var REF = db.BDMT02_REF.Where(A => A.BRE_NO == "BarcCodeMyCash" && A.DATA_CODE == "BarcCodeMyCash_Mode" && A.DATA_TYPE == "ALL" && A.SCHOOL_NO == model.WhereSchoolNo && A.IS_SELECT == 1).FirstOrDefault();


            model.DelayTime = REF?.DelayTime;
            model.ChangeMode = REF?.CONTENT_VAL;
            ViewBag.hrmt25Count = hrmt25Count;
        

         
            if (string.IsNullOrWhiteSpace(model.WhereSchoolNo))
            {
                model.WhereSchoolNo = SCHOOL_NO;
            }

            if (string.IsNullOrWhiteSpace(model.WhereSchoolNo))
            {
                TempData["StatusMessage"] = "此學號無效。處理方式：請先選學校，或將瀏灠器全部關閉後重新操作一次";
                return PartialView(model);
            }
          
            //酷幣排行榜
            var RankCashSort = from w1 in db.AWAT01
                               join h1 in db.HRMT01
                               on new { w1.SCHOOL_NO, w1.USER_NO } equals new { h1.SCHOOL_NO, h1.USER_NO }
                               into h1Table
                               from hh1 in h1Table.DefaultIfEmpty()
                               where w1.SCHOOL_NO == model.WhereSchoolNo && w1.CASH_ALL > 0 && (!UserStaus.NGUserStausList.Contains(hh1.USER_STATUS))
                               select new HRMT01QTY { CLASS_NO = hh1.CLASS_NO, NAME = hh1.SNAME, USER_NO = hh1.USER_NO, QTY = w1.CASH_ALL, CASH_ALL = w1.CASH_ALL, CASH_AVAILABLE = w1.CASH_AVAILABLE, };

            var AWAT01List = RankCashSort.OrderByDescending(a => a.CASH_ALL).Take(10).ToList();
            string SortBoard = string.Empty;
            string SortBoard2 = string.Empty;
            short i = 1;
            foreach (var t01 in AWAT01List)
            {
                int deAMT = 0;
                List<AWAT10> AT10list = new List<AWAT10>();
                AT10list = db.AWAT10.Where(x => x.STATUS == "1" && x.SCHOOL_NO == model.WhereSchoolNo && x.USER_NO == t01.USER_NO).ToList();
                if (AT10list.Count() > 0)
                {
                    foreach (var item in AT10list)
                    {
                        if (item.AMT != null)

                        {
                            deAMT += (int)item.AMT;
                        }
                    }
                }
                else
                {
                    int? CASH_AVAILABLE = 0;
                    CASH_AVAILABLE = db.AWAT01.Where(x => x.SCHOOL_NO == model.WhereSchoolNo && x.USER_NO == t01.USER_NO).Select(x => x.CASH_AVAILABLE).FirstOrDefault();
                    deAMT = (int)CASH_AVAILABLE;
                }
                if (i <= 3) SortBoard += string.Format("   第{0}名：{1} {2}  ", i, t01.CLASS_NO, t01.NAME);
                SortBoard2 += string.Format("第{0}名：{1}({2}點)　", i, t01.NAME, deAMT);
                i++;
            }
            ViewBag.SortBoard = SortBoard;
            ViewBag.SortBoard2 = SortBoard2;

            HRMT01 FindUser = db.HRMT01.Include("HRMT25").Where(a => a.USER_NO == model.WhereKeyword && a.SCHOOL_NO == model.WhereSchoolNo && a.USER_TYPE != UserType.Guest&& a.USER_STATUS==UserStaus.Enabled).FirstOrDefault();
            //    if (FindUser.USER_TYPE == "S")
            //   {
            //找學生卡對應
            if (FindUser == null && string.IsNullOrWhiteSpace(model.WhereKeyword) == false)
            {
                FindUser = db.HRMT01.Include("HRMT25").Where(a => a.CARD_NO == model.WhereKeyword && a.SCHOOL_NO == model.WhereSchoolNo && a.USER_TYPE != UserType.Guest && a.USER_STATUS == UserStaus.Enabled).FirstOrDefault();
            }

            if (FindUser == null && string.IsNullOrWhiteSpace(model.WhereKeyword) == false)
            {
                TempData["StatusMessage"] = "此學號無效";
                model.WhereKeyword = "";
                return PartialView(model);
            }

            if (FindUser == null)
            {
                return PartialView(model);
            }

            #region PageContentB

            model.ShowStep2 = true;
            model.UserNAME = FindUser.SNAME;
            //借書量
            SECI05Service ServiceS5 = new SECI05Service();
            SECI05IndexViewModel BookModel = new SECI05IndexViewModel();
            BookModel.WhereSCHOOL_NO = FindUser.SCHOOL_NO;
            BookModel.WhereUSER_NO = FindUser.USER_NO;
            BookModel = ServiceS5.GetBorrowData(BookModel, null, ref db);
            if (BookModel.MyBorrow != null)
            {
                model.UserBookQty = BookModel.MyBorrow.THIS_SESEM_QTY;
                model.AllBookQty = BookModel.MyBorrow.ALL_QTY;
                model.MonthBookQty = BookModel.MyBorrow.THIS_MM_QTY;
            }

            //學生酷幣
            AWAT01 tCASH = db.AWAT01.Where(a => a.SCHOOL_NO == FindUser.SCHOOL_NO && a.USER_NO == FindUser.USER_NO).FirstOrDefault();
            if (tCASH != null)
            {
                if (tCASH.CASH_AVAILABLE.HasValue) model.UserCash = tCASH.CASH_AVAILABLE.Value;
            }

            //定存
            AWAI07Service Service = new AWAI07Service();
            AWAI07IndexViewModel modelA7 = new AWAI07IndexViewModel();
            modelA7.Search = new AWAI07SearchViewModel();
            modelA7.Search.WhereSCHOOL_NO = FindUser.SCHOOL_NO;
            modelA7.Search.WhereUSER_NO = FindUser.USER_NO;
            modelA7.Search.WhereSTATUS = AWAT10.StatusVal.SetUp;
            Service.GetListData(modelA7, ref db);
            if (modelA7.ListData != null)
            {
                var SumAMT = modelA7.ListData.Sum(a => a.AMT);
                if (SumAMT.HasValue) model.UserAWAI07 = SumAMT.Value;
            }

            //運動
            ADDI11Service ServiceA11 = new ADDI11Service();
            ADDI11MyRunLogViewModel modelA11 = new ADDI11MyRunLogViewModel();
            modelA11.WhereSCHOOL_NO = FindUser.SCHOOL_NO;
            modelA11.WhereUSER_NO = FindUser.USER_NO;
            modelA11 = ServiceA11.GetMyRunLog(modelA11, user, ref db);

            model.RUN_TOTAL_METER = (modelA11.MyRunRank != null) ? modelA11.MyRunRank.RUN_TOTAL_METER ?? 0 : 0;
            // 下次升級
            string upgraderesultStr;
            model.RUN_UPGRADE_METER = ServiceA11.NextUpgradeRunningMiles(model.RUN_UPGRADE_METER, ref db, out upgraderesultStr);
            ViewBag.RUN_UPGRADE_RESULTSTR = upgraderesultStr;

            //取出可對兌獎品

            //閱讀等級
            ADDT09 a9 = db.ADDT09.Where(a => a.SCHOOL_NO == FindUser.SCHOOL_NO && a.USER_NO == FindUser.USER_NO).FirstOrDefault();
            byte L_ID = 0;
            if (a9 != null) L_ID = (a9.LEVEL_ID ?? 0);

            //閱讀認證.等級
            var PassportLEVEL = db.ADDT04.Where(p => p.USER_NO == FindUser.USER_NO && p.SCHOOL_NO == FindUser.SCHOOL_NO && p.PASS_YN == "Y").Count();

            string sSQL = @"SELECT  A.*
            FROM AWAT02 A (NOLOCK)
            WHERE (A.SCHOOL_NO=@SCHOOL_NO OR A.SCHOOL_NO='ALL')
            AND A.AWARD_STATUS<>'N' AND A.QTY_STORAGE>0
            AND A.SDATETIME <= GETDATE()
            AND A.EDATETIME >= GETDATE()
            AND A.COST_CASH <= @CASH_AVAILABLE
            AND (A.READ_LEVEL IS  NULL OR A.READ_LEVEL<=@LEVEL_ID)
            AND (A.PASSPORT_LEVEL IS  NULL OR A.PASSPORT_LEVEL<=@PassportLEVEL)
            AND (ISNULL(A.BUY_PERSON_YN,'0')='0' OR (ISNULL(A.BUY_PERSON_YN,'0')='1'
            AND (SELECT COUNT(*) FROM REFT01 B (NOLOCK) WHERE B.REF_KEY=A.AWARD_NO AND B.REF_TABLE='AWAT02' AND B.SCHOOL_NO=@SCHOOL_NO AND B.USER_NO=@USER_NO)>0
            ))
            ORDER BY A.COST_CASH DESC";
            var QTemp = db.Database.Connection.Query<AWAT02>(sSQL
                , new
                {
                    SCHOOL_NO = FindUser.SCHOOL_NO,
                    CASH_AVAILABLE = (tCASH?.CASH_AVAILABLE ?? 0),
                    LEVEL_ID = L_ID,
                    PassportLEVEL = PassportLEVEL,
                    USER_NO = FindUser.USER_NO
                });

            model.AWAT02List = QTemp.ToList();

            sSQL = @"select a.BOOK_QTY,b.LEVEL_DESC ,b.LEVEL_ID
						 ,Case when a.LEVEL_ID<10 Then (select Top 1 L.LEVEL_QTY from ADDT07 L (nolock) Where L.LEVEL_ID>a.LEVEL_ID order by L.LEVEL_ID)-a.BOOK_QTY
						       else 0 end as UNLEVEL_QTY
						 from ADDT09 a (nolock)
						 left outer join ADDT08 b (nolock) on a.SCHOOL_NO=b.SCHOOL_NO and a.LEVEL_ID=b.LEVEL_ID
						 where a.SCHOOL_NO=@SCHOOL_NO  and a.USER_NO=@USER_NO ";
            model.MyData = db.Database.Connection.Query<ZZZI09MyDataViewModel>(sSQL
                , new
                {
                    SCHOOL_NO = FindUser.SCHOOL_NO,
                    USER_NO = FindUser.USER_NO,
                }).FirstOrDefault();

            // 等級分數計算
            // 公式: 目前酷幣值 + 定存酷幣值 + 每月的借書量 x 100 + 跑步里程 x 20
            model.point = LogicCenter.CaculateMyATM_point(model.UserCash,
                Convert.ToInt32(model.UserAWAI07),
                model.MonthBookQty,
                Convert.ToDouble(model.RUN_TOTAL_METER ?? 0) / 1000.000);
            model.Level = LogicCenter.CaculateMyATM_Level(model.point);
            //  }
            if (model.MyData == null) model.MyData = new ZZZI09MyDataViewModel();
            string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
            ViewBag.ATMUID = FindUser.USER_TYPE;
            bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
            if (AppMode)
            {
                return PartialView("_PageContentForAPP", model);
            }
            else
            {
                model.WhereKeyword = model.WhereKeyword;
                return PartialView("_PageContent3B", model);
            }

            #endregion PageContentB
        }

        public ActionResult LoginProduct(BarcCodeMyCashLoginViewModel model)
        {
            return View(model);
        }

        public ActionResult EGameIndex()
        {
            this.Shared("行動學習E酷幣市集");
            return View();
        }

        public ActionResult _EGameMenu(string NowAction)
        {
            this.Shared();
            ViewBag.NowAction = NowAction;
            return PartialView();
        }

        private void Shared(string Panel_Title = "")
        {
            ViewBag.BRE_NO = Bre_NO;

            if (!string.IsNullOrWhiteSpace(Panel_Title))
            {
                ViewBag.Panel_Title = Panel_Title;
            }

            user = UserProfileHelper.Get();
            SCHOOL_NO = UserProfileHelper.GetSchoolNo();

            if (user != null)
            {
                if (user.SCHOOL_NO != null)
                {
                    SCHOOL_NO = user.SCHOOL_NO;
                }
            }
        }

        // GET: LinkManager
        public ActionResult BDMT05Index()
        {
            this.Shared("關主設定關卡");
            return View();
        }

        public ActionResult _BDMT05PageContent(BDMT05IndexViewModel model)
        {
            this.Shared();
            if (model == null) model = new BDMT05IndexViewModel();

            //model.Search.BackAction = "Index";
            //model.Search.BackController = Bre_NO;
            model = GetLinkManagerData(ref db);
            return PartialView(model);
        }

        public BDMT05IndexViewModel GetLinkManagerData(ref ECOOL_DEVEntities db)
        {
            string sSQL = @"select a.* from BDMT05 a (nolock) Where 1=1 ";

            var temp = db.Database.Connection.Query<BDMT05IndexListViewModel>(sSQL);

            //if (string.IsNullOrWhiteSpace(model.Search.whereSearch) == false)
            //{
            //    temp = temp.Where(a => a.WEB_NAME.Contains(model.Search.whereSearch.Trim()));
            //}
            BDMT05IndexViewModel model = new BDMT05IndexViewModel();
            model.ListData = temp.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, model.PageSize);

            return model;
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult EditSave(BDMT05EditViewModel model)
        {
            this.Shared();
            if (model == null) model = new BDMT05EditViewModel();

            string Message = string.Empty;

            if (string.IsNullOrEmpty(model.GAME_IMG) && (model.Files == null || model.Files.ContentLength == 0))
            {
                ModelState.AddModelError("GAME_IMG", "未上傳網站圖片");
            }

            if (!string.IsNullOrEmpty(model.LINK_URL))
            {
                if (model.LINK_URL.ToLower().IndexOf("http://") == -1 && model.LINK_URL.ToLower().IndexOf("https://") == -1)
                {
                    model.LINK_URL = @"http://" + model.LINK_URL;
                }

                Uri urlCheck = new Uri(model.LINK_URL.Trim());
                System.Net.WebRequest request = System.Net.WebRequest.Create(urlCheck);
                request.Timeout = 15000;
                System.Net.WebResponse response;
                try
                {
                    response = request.GetResponse();
                }
                catch (Exception)
                {
                    ModelState.AddModelError("LINK_URL", "輸入的網站網址無效，請確認");
                }
            }

            if (ModelState.IsValid == false)
            {
                Message = "警告!!輸入的內容有錯誤";
            }
            else
            {
                bool OK = SaveBDMT05(model, user, ref db, ref Message);

                if (OK)
                {
                    TempData["StatusMessageCenter"] = "儲存完成";

                    return View("BDMT05Index");
                    //LinkManagerIndexViewModel QModel = new LinkManagerIndexViewModel();
                    //QModel.Search = model.Search;
                    //return View("Index", QModel);
                }
            }

            TempData["StatusMessageCenter"] = Message;

            return View("Edit", model);
        }

        public bool SaveBDMT05(BDMT05EditViewModel data, UserProfile User, ref ECOOL_DEVEntities db, ref string Message)
        {
            BDMT05 SaveUp = null;

            SaveUp = db.BDMT05.Where(a => a.GAME_NO == data.GAME_NO).FirstOrDefault();

            if (SaveUp != null)
            {
                SaveUp.GAME_NAME = data.GAME_NAME;
                SaveUp.CASH = data.CASH;
                SaveUp.LINK_URL = data.LINK_URL;
                SaveUp.GAME_DESC = data.GAME_DESC;
                SaveUp.CHG_DATE = DateTime.Now;
                SaveUp.CHG_PERSON = User.USER_NO;

                if (data.Files != null && data.Files.ContentLength > 0)
                {
                    SaveUp.GAME_IMG = UpLoadFile(SaveUp.GAME_NO, data.Files, User, ref Message);

                    if (!string.IsNullOrWhiteSpace(Message))
                    {
                        return false;
                    }
                }
            }
            else if (SaveUp == null)
            {
                SaveUp = new BDMT05();

                SaveUp.GAME_NO = Guid.NewGuid().ToString("N");
                SaveUp.GAME_NAME = data.GAME_NAME;
                SaveUp.CASH = data.CASH;
                SaveUp.LINK_URL = data.LINK_URL;
                SaveUp.GAME_DESC = data.GAME_DESC;
                SaveUp.CRE_PERSON = User.USER_NO;
                SaveUp.CRE_DATE = DateTime.Now;
                SaveUp.CHG_PERSON = User.USER_NO;
                SaveUp.CHG_DATE = DateTime.Now;

                SaveUp.GAME_IMG = UpLoadFile(SaveUp.GAME_IMG, data.Files, User, ref Message);

                if (!string.IsNullOrWhiteSpace(Message))
                {
                    return false;
                }

                db.BDMT05.Add(SaveUp);
            }

            try
            {
                db.SaveChanges();
                return true;
            }
            catch (Exception ex)
            {
                Message = "系統發生錯誤;原因:" + ex.Message;
                return false;
            }
        }

        public string UpLoadFile(string LINK_NO, HttpPostedFileBase File, UserProfile User, ref string Message)
        {
            string ReturnFileName = string.Empty;

            try
            {
                if (File != null && File.ContentLength > 0)
                {
                    string tempPath = GetSysLinkManagerPath(LINK_NO);

                    if (System.IO.Directory.Exists(tempPath) == false)
                    {
                        System.IO.Directory.CreateDirectory(tempPath);
                    }
                    else
                    {
                        System.IO.Directory.Delete(tempPath, true);
                        System.IO.Directory.CreateDirectory(tempPath);
                    }

                    ReturnFileName = System.IO.Path.GetFileName(File.FileName);
                    string UpLoadFile = tempPath + "\\" + ReturnFileName;

                    if (System.IO.File.Exists(UpLoadFile))
                    {
                        System.IO.File.Delete(UpLoadFile);
                    }

                    File.SaveAs(UpLoadFile);
                }
            }
            catch (Exception ex)
            {
                Message = "系統發生錯誤;原因:" + ex.Message;
            }

            return ReturnFileName;
        }

        /// <summary>
        /// 實際 Path
        /// </summary>
        /// <param name="LINK_NO"></param>
        /// <returns></returns>
        public string GetSysLinkManagerPath(string LINK_NO)
        {
            return Server.MapPath(SharedGlobal.GameFilePath) + @"\" + DirectoryPath + @"\" + LINK_NO;
        }

        public ActionResult Edit(BDMT05EditViewModel model)
        {
            this.Shared("關主設定關卡-修改");
            ModelState.Clear();
            if (model == null) model = new BDMT05EditViewModel();
            model.BackAction = (string.IsNullOrWhiteSpace(model.BackAction)) ? RouteData.Values["action"].ToString() : model.BackAction;
            model.BackController = RouteData.Values["Controller"].ToString();

            model = GetBDMT05Edit(model, ref db);

            return View(model);
        }

        public BDMT05EditViewModel GetBDMT05Edit(BDMT05EditViewModel model, ref ECOOL_DEVEntities db)
        {
            model = (from a in db.BDMT05
                     where a.GAME_NO == model.whereGAME_NO
                     select new BDMT05EditViewModel()
                     {
                         GAME_NO = a.GAME_NO,
                         CASH = a.CASH,
                         GAME_NAME = a.GAME_NAME,
                         GAME_IMG = a.GAME_IMG,
                         LINK_URL = a.LINK_URL,
                         GAME_DESC = a.GAME_DESC,
                         CRE_PERSON = a.CRE_PERSON,
                         CRE_DATE = a.CRE_DATE,
                         CHG_PERSON = a.CHG_PERSON,
                         CHG_DATE = a.CHG_DATE,
                     }).FirstOrDefault();

            if (model != null)
            {
                model.GAME_IMG_PATH = GetDirectorySysLinkManagerPath(model.GAME_NO, model.GAME_IMG);
            }

            return model;
        }

        /// <summary>
        /// 虛擬 Path
        /// </summary>
        /// <param name="LINK_NO"></param>
        /// <param name="FileName"></param>
        /// <returns></returns>
        public string GetDirectorySysLinkManagerPath(string LINK_NO, string FileName)
        {
            string TempPath = GetDirectorySysLinkManagerPath(LINK_NO) + FileName;

            if (System.IO.File.Exists(Server.MapPath(TempPath)))
            {
                return UrlCustomHelper.Url_Content(TempPath);
            }

            return string.Empty;
        }

        /// <summary>
        /// 虛擬 Path
        /// </summary>
        /// <param name="FGD_ID"></param>
        /// <param name="NEWS_ID"></param>
        /// <param name="FileName"></param>
        /// <returns></returns>
        public string GetDirectorySysLinkManagerPath(string LINK_NO)
        {
            string TempPath = SharedGlobal.GameFilePath + @"\" + DirectoryPath + @"\" + LINK_NO + @"\";

            return TempPath;
        }

        private string DirectoryPath = "LinkFile";

        [HttpGet]
        public ActionResult EGameTicket0()
        {
            ViewBag.TicketUrl = "/BarcCodeMyCash/gameticket";
            return View();
        }

        [HttpGet]
        public ActionResult EGameTicket()
        {
            ViewBag.Ticket = "102063";
            return View();
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}