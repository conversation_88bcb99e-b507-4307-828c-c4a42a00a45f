﻿
@{
    ViewBag.Title = "進步加值";

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
}
<br />
<span style="font-size: 12pt;font-weight: bold; color:red">
    好運不常有，努力一定有！酷幣點數大放送來囉！
</span>
<br />

<span style="font-size: 12pt;font-weight: bold;color:blue">
      因為您的努力，積極學習參與，所以你有機會參加酷幣大放送活動！只要你的酷幣每增加<br />
    100點，你就能多玩一次拉霸，獲得更多酷幣！
</span>
<br />

<img id="BT1" src="~/Content/mp4/BT_Active-C1_1x.png" style="width:100px;height:auto;visibility:hidden" class="col-md-offset-1" onclick="ToPlay();" onmouseover="BT1_OVER();" onmouseout="BT1_OUT();" />
<a href="@Url.Action("ArrivedChanceRun2", "Home")">
    <img id="BT2" src="~/Content/mp4/BT_Result-C1_1x.png" style="width:100px;height:auto;visibility:visible" class="col-md-offset-2" onmouseover="BT2_OVER();" onmouseout="BT2_OUT();" />
</a>
<div style="position:relative">
    @if (AppMode)
    {
        <img style="width:400px ;height: auto" src="~/Content/mp4/ST01-5_Slot-A1_W2-600_HQ.gif" />
    }
    else
    {
        <video id="SlotPlayer" style="position:absolute; top:1px;left:1px; width:600px ;height: auto;visibility:visible" loop="loop">
            <source src="~/Content/mp4/ST01-5_Slot-A1_W2-600_HQ.mp4" type="video/mp4">
        </video>
    }


    @*<video id="SlotPlayer2" style="position:absolute;top:1px;left:1px; width:600px ;height: auto;visibility:hidden" loop="loop" >
        <source src="~/Content/mp4/ST01-5_Slot-A2_W2-600_HQ.mp4" type="video/mp4">
    </video>

    <video id="SlotPlayer3" style="position:absolute;top:1px;left:1px; width:600px ;height: auto;visibility:hidden"  loop="loop">
        <source src="~/Content/mp4/ST01-5_Slot-A3_W2-600_HQ.mp4" type="video/mp4">
    </video>*@
</div>


<script type="text/javascript">
    window.history.forward(1);

    $(document).ready(function () {
        var video = document.getElementById("SlotPlayer");
        video.load();
        video.play();
        //var video2 = document.getElementById("SlotPlayer2");
        //video2.load();
        //var video3 = document.getElementById("SlotPlayer3");
        //video3.load();
    });


    function ToPlay() {
        @*var video = document.getElementById("SlotPlayer");
        video.src = '@Url.Content("~/Content/mp4/ST01-5_Slot-A2_W2-600_HQ.mp4")';

        video.load();
        video.play();*@

        var video = document.getElementById("SlotPlayer");
        video.style.visibility = 'hidden';

        var video2 = document.getElementById("SlotPlayer2");
        video2.style.visibility = 'visible';
        video2.play();

        var bt1 = document.getElementById("BT1");
        bt1.style.visibility = 'hidden';

        window.setTimeout("ToPlay2()", 2100);
    }

    function ToPlay2() {
        @*var video = document.getElementById("SlotPlayer");
        video.src = '@Url.Content("~/Content/mp4/ST01-5_Slot-A3_W2-600_HQ.mp4")';

        video.load();
        video.play();*@

        var video2 = document.getElementById("SlotPlayer2");
        video2.style.visibility = 'hidden';

        var video3 = document.getElementById("SlotPlayer3");
        video3.style.visibility = 'visible';
        video3.play();

        var bt2 = document.getElementById("BT2");
        bt2.style.visibility = 'visible';
    }

    function BT1_OVER() {
        var bt1 = document.getElementById("BT1");
        bt1.src = '@Url.Content("~/Content/mp4/BT_Active-C2_1x.png")';
    }

    function BT1_OUT() {
        var bt1 = document.getElementById("BT1");
        bt1.src = '@Url.Content("~/Content/mp4/BT_Active-C1_1x.png")';
    }

    function BT2_OVER() {
        var bt2 = document.getElementById("BT2");
        bt2.src = '@Url.Content("~/Content/mp4/BT_Result-C1_1x.png")';
    }

    function BT2_OUT() {
        var bt2 = document.getElementById("BT2");
        bt2.src = '@Url.Content("~/Content/mp4/BT_Result-C2_1x.png")';
    }
</script>