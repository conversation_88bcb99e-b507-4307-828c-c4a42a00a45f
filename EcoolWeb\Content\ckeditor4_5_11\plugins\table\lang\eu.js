﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'table', 'eu', {
	border: 'Ertzaren zabalera',
	caption: 'Epigrafea',
	cell: {
		menu: 'Gelaxka',
		insertBefore: 'Txertatu gelaxka aurretik',
		insertAfter: 'Txertatu gelaxka ondoren',
		deleteCell: 'Ezabatu gelaxkak',
		merge: 'Batu gelaxkak',
		mergeRight: 'Batu eskuinetara',
		mergeDown: 'Batu behera',
		splitHorizontal: 'Banatu gelaxka horizontalki',
		splitVertical: 'Banatu gelaxka bertikalki',
		title: 'Gelaxkaren propietateak',
		cellType: 'Gelaxka-mota',
		rowSpan: 'Errenkaden hedadura',
		colSpan: 'Zutabeen hedadura',
		wordWrap: 'It<PERSON>lbira',
		hAlign: 'Lerrokatze horizontala',
		vAlign: 'Lerrokatze bertikala',
		alignBaseline: '<PERSON><PERSON><PERSON>-lerroan',
		bgColor: 'Atzeko planoaren kolorea',
		borderColor: 'Ertzaren kolorea',
		data: 'Data',
		header: 'Goiburua',
		yes: 'Bai',
		no: 'Ez',
		invalidWidth: 'Gelaxkaren zabalera zenbaki bat izan behar da.',
		invalidHeight: 'Gelaxkaren altuera zenbaki bat izan behar da.',
		invalidRowSpan: 'Errenkaden hedadura zenbaki osoa izan behar da.',
		invalidColSpan: 'Zutabeen hedadura zenbaki osoa izan behar da.',
		chooseColor: 'Aukeratu'
	},
	cellPad: 'Gelaxken betegarria',
	cellSpace: 'Gelaxka arteko tartea',
	column: {
		menu: 'Zutabea',
		insertBefore: 'Txertatu zutabea aurretik',
		insertAfter: 'Txertatu zutabea ondoren',
		deleteColumn: 'Ezabatu zutabeak'
	},
	columns: 'Zutabeak',
	deleteTable: 'Ezabatu taula',
	headers: 'Goiburuak',
	headersBoth: 'Biak',
	headersColumn: 'Lehen zutabea',
	headersNone: 'Bat ere ez',
	headersRow: 'Lehen errenkada',
	invalidBorder: 'Ertzaren tamaina zenbaki bat izan behar da.',
	invalidCellPadding: 'Gelaxken betegarria zenbaki bat izan behar da.',
	invalidCellSpacing: 'Gelaxka arteko tartea zenbaki bat izan behar da.',
	invalidCols: 'Zutabe kopurua 0 baino handiagoa den zenbakia izan behar da.',
	invalidHeight: 'Taularen altuera zenbaki bat izan behar da.',
	invalidRows: 'Errenkada kopurua 0 baino handiagoa den zenbakia izan behar da.',
	invalidWidth: 'Taularen zabalera zenbaki bat izan behar da.',
	menu: 'Taularen propietateak',
	row: {
		menu: 'Errenkada',
		insertBefore: 'Txertatu errenkada aurretik',
		insertAfter: 'Txertatu errenkada ondoren',
		deleteRow: 'Ezabatu errenkadak'
	},
	rows: 'Errenkadak',
	summary: 'Laburpena',
	title: 'Taularen propietateak',
	toolbar: 'Taula',
	widthPc: 'ehuneko',
	widthPx: 'pixel',
	widthUnit: 'zabalera unitatea'
} );
