﻿@model GameBuskerOpenPersonViewViewModel

<div class="form-inline" role="form">
    <div class="form-group">
        <label class="control-label">學校</label>
    </div>
    <div class="form-group">
        @Html.DropDownListFor(m => m.WhereSCHOOL_NO, (IEnumerable<SelectListItem>)Model.SchoolNoSelectItem, new { @class = "form-control" })
    </div>

    <div class="form-group">
        <label class="control-label">班級</label>
    </div>
    <div class="form-group">
        @Html.DropDownListFor(m => m.WhereCLASS_NO, (IEnumerable<SelectListItem>)Model.ClassItems, new { @class = "form-control" })
    </div>

    <div class="form-group">
        <label class="control-label">姓名</label>
    </div>
    <div class="form-group">
        @Html.EditorFor(m => m.WhereNAME)
    </div>

    <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="onAddItem();" />
</div>
<br />

<div class="table-responsive">
    <div class="css-table" style="width:92%;">
        <div class="tr" style=" font-weight: bold;text-align:center;color:#004da0;text-overflow:ellipsis; ">
            <div class="th" style="text-align:center">
                <button id="AllAddRowBtn" type="button" class="btn btn-default btn-xs" onclick="AllAddRow()"><i class='fa fa-plus'></i>全部加入</button>
            </div>
            <div class="th" style="text-align:center">
                學校
            </div>
            <div class="th" style="text-align:center">
                全名
            </div>
            <div class="th" style="text-align:center">
                身份
            </div>
            <div class="th" style="text-align:center">
                學號/帳號
            </div>
            <div class="th" style="text-align:center">
                班級
            </div>
            <div class="th" style="text-align:center">
                座號
            </div>
            <div class="th" style="text-align:center">
                電話
            </div>
            <div class="th" style="text-align:center">
                卡號
            </div>
        </div>
        <div id="editorRows" class="tbody">
            @if ((Model.PersonData).Count() > 0)
            {
                foreach (var Item in Model.PersonData)
                {
                    <div class="tr" id="Tr@(Item.TEMP_USER_ID)">
                        <div class="td" style="text-align:center">
                            <button type="button" class="AddRowBtn btn btn-default btn-xs" onclick="AddRow('@(Item.TEMP_USER_ID)')"><i class='fa fa-plus'></i>加入</button>
                        </div>
                        <div class="td" style="text-align:center">
                            @if (Item.GAME_USER_TYPE == UserType.Student)
                            {
                                @Item.SHORT_NAME
                            }
                            else
                            {
                                <text>卡片</text>
                            }
                        </div>
                        <div class="td" style="text-align:center">
                            @Item.NAME
                        </div>
                        <div class="td" style="text-align:center">
                            @if (string.IsNullOrWhiteSpace(Item.GAME_USER_TYPE_DESC))
                            {
                                @UserType.GetDesc(Item.GAME_USER_TYPE)
                            }
                            else
                            {
                                @Item.GAME_USER_TYPE_DESC
                            }
                        </div>
                        <div class="td" style="text-align:center">
                            @Item.USER_NO
                        </div>
                        <div class="td" style="text-align:center">
                            @Item.CLASS_NO
                        </div>
                        <div class="td" style="text-align:center">
                            @Item.SEAT_NO
                        </div>
                        <div class="td" style="text-align:center">
                            @Item.PHONE
                        </div>
                        <div class="td" style="text-align:center">
                            @Item.GAME_USER_ID
                        </div>
                    </div>
                }
            }
        </div>
    </div>
</div>