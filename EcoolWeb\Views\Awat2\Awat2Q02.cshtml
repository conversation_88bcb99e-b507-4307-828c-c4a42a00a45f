﻿@model List<ECOOL_APP.EF.AWAT09>
@using System.Collections;
@using ECOOL_APP.com.ecool.util;
@{

    Layout = "~/Views/Shared/_Layout.cshtml";
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    string unProduct = (Request["unProduct"] != null) ? Request["unProduct"] : "";

    if (string.IsNullOrWhiteSpace(unProduct) ==false)
    {
        ViewBag.Title = "老師兌換獎品-兌換獎品一覽表(下架商品)";
    }
    else
    {
        ViewBag.Title = "老師兌換獎品-兌換獎品一覽表";
    }

}
<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>
@section Scripts
    {
    <script type="text/javascript">

        $(document).ready(function () {
            $(".img-Product").colorbox({ opacity: 0.82 });
        });

        function AWARD_TYPE_onchange(val) {
            $('#AWARD_TYPE').val(val);
            document.form1.enctype = "multipart/form-data";
            document.form1.action = "Awat2Q02";
            document.form1.submit();
        }

        function funGetModify(AWARD_NO, MODE, unProduct) {
            $('#hidAWARD_NO').val(AWARD_NO);
            $('#hidAWARD_STS').val('M');
            document.form1.enctype = "multipart/form-data";
            document.form1.action = "Awat2Mana02?MODE=" + MODE + "&unProduct=" + unProduct;
            document.form1.submit();
        }

        function funGetDelete(AWARD_NO) {
            $('#hidAWARD_NO').val(AWARD_NO);
            $('#hidAWARD_STS').val('D');
            document.form1.action = "Awat2Mana02?MODE=DEL";
            document.form1.submit();
        }

        function funGetExchange(AWARD_NO) {
            var btnId = '#btn_' + AWARD_NO
            $('#hidAWARD_NO').val(AWARD_NO);
            document.form1.action = "Awat2Exchange02";
            document.form1.submit();
            $(btnId).text('我要兌換...讀取中...')
        }
    </script>
}


@helper btnFun()
{
string AWARD_TYPE_All = (string.IsNullOrWhiteSpace(ViewBag.AWARD_TYPE)) ? "active" : "";
string AWARD_TYPE_A = (ViewBag.AWARD_TYPE == "A") ? "active" : "";
string AWARD_TYPE_T = (ViewBag.AWARD_TYPE == "T") ? "active" : "";
string AWARD_TYPE_S = (ViewBag.AWARD_TYPE == "S") ? "active" : "";
    string AWARD_TYPE_PC = (ViewBag.AWARD_TYPE == "P,C") ? "active" : "";
    <div class="row text-right">
        <samp>獎品狀態：</samp>
            <button class="btn btn-xs btn-pink  @AWARD_TYPE_All" type="button" onclick="doSearch('AWARD_TYPE', '')">全部</button>
            <button class="btn btn-xs btn-pink  @AWARD_TYPE_A" type="button" onclick="doSearch('AWARD_TYPE', 'A')">活動</button>
            <button class="btn btn-xs btn-pink  @AWARD_TYPE_T" type="button" onclick="doSearch('AWARD_TYPE', 'T')">票券</button>
            <button class="btn btn-xs btn-pink  @AWARD_TYPE_S" type="button" onclick="doSearch('AWARD_TYPE', 'S')">實體</button>
        @Html.Hidden("AWARD_TYPE", (string)ViewBag.AWARD_TYPE)
    </div>

}
@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")




@Html.ActionLink("老師酷幣點數排行榜", "QueryTeacher", "AWA003", new { Awat = "Awat" }, new { @class = "btn btn-sm btn-sys" })
@Html.ActionLink("老師兌獎名單", "QUERY", "AWA006", new { Awat = "Awat" }, new { @class = "btn btn-sm btn-sys" })

@if (ViewBag.VisableInsert == true)
{
    <a class="btn btn-sm btn-sys" href='@Url.Action("Awat202", "Awat2", new { MODE = "ADD" })'>
        新增獎品
    </a>
}

@if (user != null)
{
    if (ViewBag.VisableModify == true)
    {
        if (string.IsNullOrWhiteSpace(unProduct))
        {
            @Html.ActionLink("搜尋已下架商品", "Awat2Q02", "Awat2", new { unProduct = "unProduct" }, new { @class = "btn btn-sm btn-sys" }) <br />
        }
        else
        {
            @Html.ActionLink("搜尋上架中商品", "Awat2Q02", "Awat2", new { unProduct = "" }, new { @class = "btn btn-sm btn-sys" }) <br />
        }
    }
}


<br />
<label class="label_dt"><img src='~/Content/img/icons-07.png' style="max-height:30px;margin-right:5px">   此獎品圖示：代表熱門獎品</label>
<br />
<label class="label_dt"><img src='~/Content/img/web-student-prize-05.png' style="max-height:30px;margin-right:5px">   此獎品圖示：新上架獎品</label>



<div style="width:95%">
    @if (user != null)
    {
        if (user.USER_TYPE == "S")
        {
            string Explain = ViewBag.AwatQ02TEXPLAIN;
            @Html.Raw(HttpUtility.HtmlDecode(@Explain))
        }
        else if (user.USER_TYPE == "T")
        {
            string Explain = ViewBag.AwatQ02TEXPLAIN;
            @Html.Raw(HttpUtility.HtmlDecode(@Explain))
        }
        <br />
    }
</div>

<form action="#" name="form1" id="form1" method="post">
    <div class="form-inline" role="form">
        <div class="form-group">
            <label class="control-label">品名</label>
        </div>
        <div class="form-group">
            @Html.Editor("whereKeyword", (string)ViewBag.whereKeyword, new { htmlAttributes = new { @class = "form-control input-sm" } })
        </div>
        <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="form1.submit();" />
        <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />
    </div>
    @btnFun()
    <img src="~/Content/img/web-bar2-revise-23.png" style="width:100%" class="img-responsive " alt="Responsive image" />
    <div class="Div-EZ-Awat">
        <div style="height:35px"></div>
        <div class="p-context">
            @if (Model.Count == 0)
            {
                <div class="text-center">
                    <label class="label_dd_font18">無任何商品</label>
                </div>
            }
            <div class="row">
                @{
                    string divrowSt = "<div class='row-hidden'>";
                    string divrowEd = "</div> <!--row-hidden End-->";
                    int num = 0;

                    foreach (AWAT09 award in Model)
                    {
                        string aImgUrl = ViewBag.ImgUrl + @"/" + award.IMG_FILE;

                        num++;

                        if (num % 2 != 0)
                        {
                            @Html.Raw(HttpUtility.HtmlDecode(divrowSt))
                        }

                        <div class="col-md-6 col-xs-12 col-height">
                            <div class="box">
                                <div>
                                    <img src='@aImgUrl' title="@award.AWARD_NAME" href="@aImgUrl">
                                </div>
                            </div>

                            <div class="prod-caption">
                                <div class="form-group">
                                    <label class="label_dd_font18" title="@award.AWARD_NAME">@StringHelper.LeftStringR(award.AWARD_NAME, 13)</label>
                                </div>
                                <div class="form-group">
                                    <div class="prod-text">
                                        兌換點數：@award.COST_CASH
                                    </div>

                                    @if (award.AWARD_TYPE == "P" || award.AWARD_TYPE == "C")
                                    {
                                        <div class="prod-text">已募資點數：@(award.COST_CASH * award.QTY_TRANS)</div>


                                    }
                                    else
                                    {
                                        <div class="prod-text">
                                            剩餘數量：@award.QTY_STORAGE
                                        </div>
                                    }
                                    <br />
                                    開始日期：@award.SDATETIME.Value.ToShortDateString()<br />
                                    兌換期限：@award.EDATETIME.Value.ToShortDateString()<br />
                                    @if (award.SHOW_DESCRIPTION_YN == "Y")
                                    {
                                        @award.DESCRIPTION
                                    }
                                </div>

                                <div class="prod-icon">
                                    @if (award.HOT_YN == "Y")
                                    {
                                        <img src='~/Content/img/icons-07.png' style="height:30px;width:30px;max-height:30px;max-width:30px">
                                    }

                                    @if (Convert.ToDateTime(award.SDATETIME).AddMonths(+1) >= DateTime.Now)
                                    {
                                        <img src='~/Content/img/web-student-prize-05.png' style="height:30px;width:30px;max-height:30px;max-width:30px" />
                                    }

                           

                                    @if (null != user && user.USER_TYPE != "S" && user.USER_TYPE != "P")
                                    {
                                        string NG = EcoolWeb.Controllers.AWAI01Controller.CheckCxchange(award, user);


                                        if (string.IsNullOrWhiteSpace(NG) == false)
                                        {

                                            <button role="button" class="btn-default btn btn-xs btn-prod" title="@Html.Raw(StringHelper.UnHtml(HttpUtility.HtmlDecode(NG)))">
                                                無法兌換
                                            </button>
                                        }
                                        else
                                        {
                                            string BtnId = "btn_" + award.AWARD_NO;

                                            @Html.ActionLink("我要兌換", "Awat2Exchange02", "Awat2", new { hidAWARD_NO = award.AWARD_NO }, new { @class = "btn-default btn btn-xs btn-prod" })
                                        }
                                    }
                                    
                                    <a class="btn-default btn btn-xs btn-prod" href='@Url.Action("Query", "AWA006" , new {  whereKeyword = award.AWARD_NO, Awat = "Awat_Key" })'>兌獎名單</a>

                                    @if (ViewBag.VisableModify == true || ViewBag.VisableDelete == true)
                                    {


                                        if (ViewBag.VisableModify == true)
                                        {
                                            if (((award.SCHOOL_NO != "ALL") || (award.SCHOOL_NO == "ALL" && ECOOL_APP.EF.HRMT24_ENUM.QOutSchoolList.Contains((byte)user.ROLE_TYPE))))
                                            {
                                                <a class="btn-default btn btn-xs btn-prod" style="" role="button" onclick="funGetModify(@award.AWARD_NO,'EDIT','@unProduct')">修改</a>
                                            }
                                        }
                                        if (ViewBag.VisableDelete == true)
                                        {
                                            if (unProduct == "" && ((award.SCHOOL_NO != "ALL") || (award.SCHOOL_NO == "ALL" && ECOOL_APP.EF.HRMT24_ENUM.QOutSchoolList.Contains((byte)user.ROLE_TYPE))))
                                            {
                                                <a class="btn-default btn btn-xs btn-prod" role="button" onclick="funGetDelete(@award.AWARD_NO)">下架/刪除</a>
                                            }
                                        }

                                    }
                                </div>

                            </div>
                        </div>
                        if (num % 2 == 0)
                        {
                            @Html.Raw(HttpUtility.HtmlDecode(divrowEd))
                        }
                    }
                    if (num % 2 != 0)
                    {
                        @Html.Raw(HttpUtility.HtmlDecode(divrowEd))
                    }

                }
            </div><!--/row-->

           
        </div>
    </div>
    <input type="hidden" id="hidunProduct" name="unProduct" value="@unProduct" />
    <input type="hidden" id="hidAWARD_NO" name="hidAWARD_NO" />
    <input type="hidden" id="hidAWARD_STS" name="hidAWARD_STS" />
</form>
