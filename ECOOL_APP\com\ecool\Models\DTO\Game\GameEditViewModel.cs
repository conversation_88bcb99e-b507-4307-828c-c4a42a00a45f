﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class GameEditViewModel
    {
        /// <summary>
        ///活動類行 1.一般 2.有獎徵答
        /// </summary>
        [DisplayName("活動類行 1.一般 2.有獎徵答")]
        public byte? GAME_TYPE { get; set; }

        /// <summary>
        /// 查詢
        /// </summary>
        public GameSearchViewModel Search { get; set; }

        /// <summary>
        /// 主檔
        /// </summary>
        public GameEditMainViewModel Main { get; set; }

        /// <summary>
        /// 明細-一般模式
        /// </summary>
        public virtual ICollection<GameEditDetailsViewModel> Details { get; set; }

        public virtual ICollection<GameLotteryPrizeViewModel> PrizeDetails { get; set; }

        /// <summary>
        /// 明細-有獎徵答模式
        /// </summary>
        public virtual ICollection<GameEditDetailsQAViewModel> DetailsQA { get; set; }
    }
}