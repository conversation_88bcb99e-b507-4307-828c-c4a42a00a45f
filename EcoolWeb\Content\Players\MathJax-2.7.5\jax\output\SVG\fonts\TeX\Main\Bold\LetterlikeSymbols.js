/*
 *  /MathJax/jax/output/SVG/fonts/TeX/Main/Bold/LetterlikeSymbols.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax.SVG.FONTDATA.FONTS["MathJax_Main-bold"],{8463:[694,8,668,45,642,"477 56Q477 48 479 46T490 43Q522 45 544 75T577 140Q582 156 585 159T605 162H611H622Q642 162 642 148Q642 138 632 114T602 62T550 13T478 -8Q429 -8 394 17T358 83Q358 95 395 199T433 350Q433 400 394 400H388H383Q335 400 291 363Q256 332 236 298Q233 293 202 170T169 40Q160 18 141 5T99 -8Q70 -8 58 9T45 39Q45 51 116 336L167 540H80V607H184L188 622H184Q183 622 179 622T169 623T157 624T146 624T136 624T131 625Q119 628 119 642Q119 647 123 661T129 679Q133 684 142 685T220 690Q293 694 307 694Q324 694 328 679Q328 673 311 607H494V540H294Q286 507 278 473T264 420L260 403Q260 400 269 408Q327 451 393 451H401H410Q425 451 439 450T476 442T515 424T544 391T556 337Q556 286 517 179T477 56"],8465:[702,8,831,64,798,"65 502Q65 564 99 611T174 680T250 701Q251 701 257 701T269 702Q319 702 374 680T466 633T542 578T592 542L602 538L621 537Q669 537 695 542T725 550T730 560Q732 570 736 572T756 575H764H777Q798 575 798 559Q798 535 780 519Q762 500 727 493T622 486Q532 486 483 504T386 572Q382 576 371 588T355 603T341 616T327 628T313 637T298 645T282 649T264 651Q215 651 174 609T132 501Q132 434 184 393T312 347Q327 346 330 343T333 322T330 301T312 296Q276 296 236 307T157 341T91 406T65 502ZM406 314Q406 351 427 378T480 418T541 437T598 443Q645 443 655 442Q722 435 760 407T798 338Q798 326 794 324T772 321H764Q739 321 734 325T729 341T717 365Q690 392 618 392H611Q586 392 572 366Q561 345 561 315Q561 291 577 275Q595 260 643 241T706 211Q747 186 747 140Q747 78 696 39Q667 15 617 1Q578 -8 480 -8H439Q379 -8 345 5T228 74Q182 105 152 119T86 137Q71 138 68 142T64 164Q64 175 64 177T68 184T78 188T99 188H151L226 187L238 185Q275 176 305 158T376 106T443 54Q478 31 489 31H490Q494 32 500 34T524 43T554 62T579 92T593 135Q593 162 575 179T533 204T479 225T432 255Q406 278 406 314"],8467:[702,19,474,-1,446,"245 -19Q228 -19 212 -16T184 -6T162 9T143 27T129 46T118 66T111 84T106 99T102 111L101 116L69 89L36 62Q31 60 24 62Q-1 88 -1 98Q-1 101 1 105Q1 106 73 170L95 189V197Q95 242 112 317T159 476T241 624T353 701Q357 702 367 702Q428 702 444 641Q446 630 446 606Q446 454 241 246L215 220L212 203Q203 150 203 114Q203 113 203 106T204 95T205 82T209 67T214 54T223 43T236 35T253 32Q277 32 305 44T352 70T389 98T407 112Q409 113 412 113Q420 113 432 95Q445 77 443 70Q440 64 416 44T342 3T245 -19ZM387 615Q387 651 366 651Q342 651 321 604T276 470L241 331Q246 331 280 373T350 486T387 615"],8472:[461,210,740,72,726,"399 159Q410 159 421 151T433 126Q433 104 410 85Q408 84 410 78Q411 72 414 66T428 51T455 43Q483 43 506 55T543 83T568 125T584 166T594 206Q595 211 596 214Q610 273 610 301Q610 365 542 365H538Q483 365 429 344T337 292T269 229T225 175T210 150L255 99Q261 92 274 78T292 58T305 41T316 22T321 3T324 -23Q324 -87 283 -148T174 -210H171Q161 -210 152 -209T128 -201T101 -180T81 -141T72 -78Q72 -72 72 -60T73 -45Q79 4 102 65L108 81Q84 117 84 167Q84 273 140 367T269 461Q285 461 285 447Q285 440 282 431Q278 418 276 415T264 410Q228 404 201 336T174 219Q174 218 176 202L184 214Q252 303 348 360T549 417Q614 417 658 391T719 317Q726 292 726 260Q726 148 646 70T451 -8Q407 -8 377 17T346 92Q346 159 396 159H399ZM178 -160Q200 -160 216 -132T232 -75Q232 -63 228 -56T203 -26Q196 -18 192 -14Q185 -5 176 5T161 20T156 27L153 28Q151 28 146 8T137 -42T132 -89Q132 -160 178 -160"],8476:[711,16,831,42,824,"133 87Q166 34 218 34Q232 34 238 47T247 99Q248 105 248 127Q248 135 248 144T247 169T245 239T243 382Q242 534 241 565T234 612Q219 651 190 651Q168 651 151 630T134 580Q134 565 148 548T178 516T209 468T223 394Q218 243 131 243Q102 243 84 266T64 319Q64 334 69 337T95 340Q117 340 121 337T126 317Q127 294 133 294Q140 294 146 318Q150 339 150 382L151 413Q141 437 103 485T64 572Q64 623 100 662T197 702Q235 702 273 684T339 634Q407 702 610 710Q615 710 630 710T651 711Q673 711 677 709Q682 706 753 578T824 444Q824 437 817 432Q799 420 758 399T686 361T654 344T657 289T665 177T670 115Q676 78 708 46L735 69Q762 93 769 93L807 73Q812 68 812 62Q812 57 805 51T759 18L710 -16H680H669Q617 -16 573 17Q527 52 515 114Q514 118 508 218T501 326V330H397V281Q397 197 384 135T327 28Q281 -16 223 -16H220Q180 -16 151 -7T107 18T86 46T78 68L74 67Q64 67 53 78T42 97Q42 106 51 109T60 114V119Q60 120 60 122L59 124Q59 129 64 135T78 149T91 160Q102 163 109 155Q115 133 119 133Q124 133 137 123T150 102Q150 98 146 94Q144 90 133 87ZM664 419L540 644H535Q517 644 487 637Q396 621 371 582L376 571Q396 512 397 435V392H494Q598 393 610 396Q611 397 615 398Q626 401 645 409T664 419"],8501:[694,0,703,64,638,"590 427Q581 427 579 433T575 450T568 470V468L532 288L541 281Q620 220 634 165L637 154V124Q637 74 628 46Q623 32 612 16T592 0Q580 0 578 19T569 69T538 121Q532 126 385 240T236 355Q234 355 231 338T225 291T222 237Q222 222 223 213T225 201T228 195T231 190Q238 179 261 160T300 119T316 73Q316 41 291 23T231 1Q226 0 149 0H98Q73 0 69 3T64 24Q64 43 67 47T85 51H89Q119 51 134 55T152 64T154 76Q154 95 125 141T96 220Q96 243 104 270T123 319T145 360T164 391T172 404T150 421T102 468T68 529L65 541V570Q65 620 74 648Q79 664 91 679T111 694Q122 694 123 675T132 625T164 573Q168 569 319 452T471 335Q471 337 486 409T502 488Q502 489 491 493T467 511T448 546V573Q448 602 452 624T462 659T474 680T486 691T493 694Q499 694 502 691T507 682T513 673Q517 667 534 651T557 630Q558 629 590 616T631 587Q638 577 638 543Q637 489 622 458T590 427"]});MathJax.Ajax.loadComplete(MathJax.OutputJax.SVG.fontDir+"/Main/Bold/LetterlikeSymbols.js");
