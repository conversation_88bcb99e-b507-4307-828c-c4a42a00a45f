﻿@model ADDI11SecretMissionEditViewModel

@{
    Html.RenderAction("_RunMenu", new { NowAction = "SecretMission" });
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@using (Html.BeginForm("SecretMission", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "formEdit", name = "formEdit", enctype = "multipart/form-data" }))
{
    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })

    @Html.HiddenFor(m => m.Task.SCHOOL_NO)
    @Html.HiddenFor(m => m.Task.ITEM_NO)
    @Html.HiddenFor(m => m.Task.RUN_TOTAL_KM)
    @Html.HiddenFor(m => m.Task.TASK_DESC)
    @Html.HiddenFor(m => m.Ans.SCHOOL_NO)
    @Html.HiddenFor(m => m.Ans.USER_NO)

    <img src="~/Content/img/web-bar-Run.png" class="img-responsive" />
    <fieldset>
        <div class="Div-EZ-reader">
            <div class="form-horizontal">
                <div style="height:15px"></div>
                <div class="Caption_Div">
                    @Model.Task.TASK_DESC
                </div>
                <div style="height:15px"></div>
                <div class="form-group">

                    @if (Model.Task.IS_TEXT ?? false)
                    {
                        <div class="form-group">
                            <label class="col-md-3 control-label">
                                請問答
                            </label>
                            <div class="col-md-9">
                                @Html.TextAreaFor(m => m.Ans.ANSWERS, new { cols = "200", rows = "15", @class = "form-control" })
                                @Html.ValidationMessageFor(m => m.Ans.ANSWERS, "", new { @class = "text-danger" })
                            </div>
                        </div>
                    }
                    @if (Model.Task.FILE_COUNT > 0)
                    {
                        <div class="form-group">
                            <label class="col-md-3 control-label">
                                請上傳
                            </label>
                            <div class="col-md-9">
                                @Html.TextBoxFor(m => m.UploadFiles, new { @class = "form-control input-md", @type = "file", @multiple = "multiple", @accept = "image/*", })
                                @Html.ValidationMessageFor(m => m.UploadFiles, "", new { @class = "text-danger" })
                            </div>
                        </div>
                        if (!string.IsNullOrWhiteSpace(Model.Ans?.UPLOAD_FILES))
                        {
                            <div class="form-group">
                                <label class="col-md-3 control-label">
                                    原上傳圖片
                                </label>
                                <div class="col-md-9">
                                    @Html.HiddenFor(m => m.Ans.UPLOAD_FILES)
                                    @if (Model.FileName != null)
                                    {
                                        foreach (var Name in Model.FileName)
                                        {
                                            string ImgPath = Model.FilePaths + @"\" + Name;

                                            <img src="@Url.Content(ImgPath)" href="@Url.Content(ImgPath)" class="img-responsive " />
                                        }
                                    }
                                </div>
                            </div>
                        }
                    }
                </div>
            </div>
            <div class="text-center" style="padding-bottom:30px">
                <input type="button" class="btn-yellow btn" value="確定" id="BtnSave" onclick="onSave();" />
            </div>
        </div>
    </fieldset>

}

<script type="text/javascript">

    var targetFormID = '#formEdit';

    function onSave()
    {

        $('#BtnSave').attr('disabled', 'disabled').html("Loading...資料處理中…請勿其他動作");
         $(targetFormID).attr("action", "@Url.Action("SecretMissionSave", (string)ViewBag.BRE_NO)")
         $(targetFormID).submit();
    }
</script>