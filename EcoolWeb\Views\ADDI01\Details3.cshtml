﻿@using ECOOL_APP.com.ecool.Models.entity;
@model IEnumerable<ECOOL_APP.EF.ADDT01>

@{
    ViewBag.Title = ViewBag.UserName + "線上投稿";
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    int i = 0;
    List<Image_File_Multiple>
        ImageModel = (List<Image_File_Multiple>
            )ViewBag.ImageUrl;
}
@if (Model!=null&&Model.Count() > 0) { 
<section class="row px-3 onlineSubmit">
    <div class="col-lg-12 bg-custom-yellow bgPosition">
       

        @foreach (var item in Model)
        { 
            
            <h2 class="heading-h2">線上投稿</h2>
            <div class="break-avoid">
                <div class="bg-custom-white">
                    <table class="table">
                        <tr>
                            <td width="70%" class="pt-3 pb-1 pl-4">
                                <table>
                                    <tr>
                                        <th scope="row" class="pr-3">投稿日期</th>
                                        <td> @Html.DisplayFor(model => item.CRE_DATE, "ShortDateTime")</td>
                                    </tr>
                                </table>
                            </td>
                            <td class="pt-3 pb-1 pr-4">
                                <table>
                                    <tr>
                                        <th scope="row" class="pr-3">班級</th>
                                        <td> @Html.DisplayFor(model => item.CLASS_NO)</td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td class="pb-3 pt-1 pl-4">
                                <table>
                                    <tr>
                                        <th scope="row" class="pr-3">文章標題</th>
                                        <td> @Html.DisplayFor(model => item.SUBJECT)</td>
                                    </tr>
                                </table>
                            </td>
                            <td class="pb-3 pt-1 pr-4">
                                <table>
                                    <tr>
                                        <th scope="row" class="pr-3">幣值</th>
                                        <td> @Html.DisplayFor(model => item.CASH)</td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        @if (string.IsNullOrWhiteSpace(item.VERIFY_COMMENT) == false)
                        {
                            <tr>
                                <td colspan="2" class="pb-3 pt-1 pl-4">
                                    <table>
                                        <tr>
                                            <th width=100 class="pr-3">教師評語</th>
                                            <td>@Html.Raw(HttpUtility.HtmlDecode(item.VERIFY_COMMENT))</td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                        }
                    </table>
                </div>
                <div class="row content">
                    <div class="col-12">
                        <p>
                            @if (!string.IsNullOrWhiteSpace(item.ARTICLE_VERIFY))
                            {
                                @Html.Raw(HttpUtility.HtmlDecode(item.ARTICLE_VERIFY.Replace("\r\n", "<br />")))
                            }
                            else
                            {
                                @Html.Raw(HttpUtility.HtmlDecode(item.ARTICLE.Replace("\r\n", "<br />")))
                            }
                        </p>
                        <div class="waterfall-imgs" id="ADDI01GRid">
                            @if (ImageModel != null && (ImageModel.Any()))
                            {
                                var ThisList = ImageModel.Where(a => a.APPLY_NO == item.WRITING_NO).ToList();

                                if (ThisList != null)
                                {
                                    foreach (var ThisItem in ThisList)
                                    {
                                        foreach (var img in ThisItem.ImageUrl)
                                        {
                                            <div class="waterfall-item">
                                                <img src="@img" alt="" />
                                            </div>
                                        }
                                    }
                                }
                            }
                        </div>
                    </div>
                </div>
            </div>

            i++;
        }
    </div>
</section>}