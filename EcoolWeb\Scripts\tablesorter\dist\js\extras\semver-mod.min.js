/*! Modified semver.js for node.js (v4.3.3, 3/27/2015) */
!function(){var h,n={exports:{}}.exports=L;h="object"==typeof process&&process.env&&process.env.NODE_DEBUG&&/\bsemver\b/i.test(process.env.NODE_DEBUG)?function(){var r=Array.prototype.slice.call(arguments,0);r.unshift("SEMVER"),console.log.apply(console,r)}:function(){},n.SEMVER_SPEC_VERSION="2.0.0";var i=256,o=Number.MAX_SAFE_INTEGER||9007199254740991,c=n.re=[],r=n.src=[],e=0,t=e++;r[t]="0|[1-9]\\d*";var s=e++;r[s]="[0-9]+";var a=e++;r[a]="\\d*[a-zA-Z-][a-zA-Z0-9-]*";var p=e++;r[p]="("+r[t]+")\\.("+r[t]+")\\.("+r[t]+")";var u=e++;r[u]="("+r[s]+")\\.("+r[s]+")\\.("+r[s]+")";var l=e++;r[l]="(?:"+r[t]+"|"+r[a]+")";var f=e++;r[f]="(?:"+r[s]+"|"+r[a]+")";var v=e++;r[v]="(?:-("+r[l]+"(?:\\."+r[l]+")*))";var m=e++;r[m]="(?:-?("+r[f]+"(?:\\."+r[f]+")*))";var g=e++;r[g]="[0-9A-Za-z-]+";var w=e++;r[w]="(?:\\+("+r[g]+"(?:\\."+r[g]+")*))";var y=e++,d="v?"+r[p]+r[v]+"?"+r[w]+"?";r[y]="^"+d+"$";var j="[v=\\s]*"+r[u]+r[m]+"?"+r[w]+"?",E=e++;r[E]="^"+j+"$";var b=e++;r[b]="((?:<|>)?=?)";var $=e++;r[$]=r[s]+"|x|X|\\*";var k=e++;r[k]=r[t]+"|x|X|\\*";var R=e++;r[R]="[v=\\s]*("+r[k]+")(?:\\.("+r[k]+")(?:\\.("+r[k]+")(?:"+r[v]+")?"+r[w]+"?)?)?";var S=e++;r[S]="[v=\\s]*("+r[$]+")(?:\\.("+r[$]+")(?:\\.("+r[$]+")(?:"+r[m]+")?"+r[w]+"?)?)?";var x=e++;r[x]="^"+r[b]+"\\s*"+r[R]+"$";var I=e++;r[I]="^"+r[b]+"\\s*"+r[S]+"$";var T=e++;r[T]="(?:~>?)";var V=e++;r[V]="(\\s*)"+r[T]+"\\s+",c[V]=new RegExp(r[V],"g");var A=e++;r[A]="^"+r[T]+r[R]+"$";var C=e++;r[C]="^"+r[T]+r[S]+"$";var N=e++;r[N]="(?:\\^)";var M=e++;r[M]="(\\s*)"+r[N]+"\\s+",c[M]=new RegExp(r[M],"g");var _=e++;r[_]="^"+r[N]+r[R]+"$";var D=e++;r[D]="^"+r[N]+r[S]+"$";var X=e++;r[X]="^"+r[b]+"\\s*("+j+")$|^$";var z=e++;r[z]="^"+r[b]+"\\s*("+d+")$|^$";var G=e++;r[G]="(\\s*)"+r[b]+"\\s*("+j+"|"+r[R]+")",c[G]=new RegExp(r[G],"g");var O=e++;r[O]="^\\s*("+r[R]+")\\s+-\\s+("+r[R]+")\\s*$";var P=e++;r[P]="^\\s*("+r[S]+")\\s+-\\s+("+r[S]+")\\s*$";var Z=e++;r[Z]="(<|>)?=?\\s*\\*";for(var q=0;q<34;q++)h(q,r[q]),c[q]||(c[q]=new RegExp(r[q]));function B(r,e){if(r instanceof L)return r;if("string"!=typeof r)return null;if(r.length>i)return null;if(!(e?c[E]:c[y]).test(r))return null;try{return new L(r,e)}catch(r){return null}}function L(r,e){if(r instanceof L){if(r.loose===e)return r;r=r.version}else if("string"!=typeof r)throw new TypeError("Invalid Version: "+r);if(r.length>i)throw new TypeError("version is longer than "+i+" characters");if(!(this instanceof L))return new L(r,e);h("SemVer",r,e),this.loose=e;var t=r.trim().match(e?c[E]:c[y]);if(!t)throw new TypeError("Invalid Version: "+r);if(this.raw=r,this.major=+t[1],this.minor=+t[2],this.patch=+t[3],this.major>o||this.major<0)throw new TypeError("Invalid major version");if(this.minor>o||this.minor<0)throw new TypeError("Invalid minor version");if(this.patch>o||this.patch<0)throw new TypeError("Invalid patch version");t[4]?this.prerelease=t[4].split(".").map(function(r){if(/^[0-9]+$/.test(r)){var e=+r;if(0<=e&&e<o)return e}return r}):this.prerelease=[],this.build=t[5]?t[5].split("."):[],this.format()}n.parse=B,n.valid=function(r,e){var t=B(r,e);return t?t.version:null},n.clean=function(r,e){var t=B(r.trim().replace(/^[=v]+/,""),e);return t?t.version:null},window.semver=n.SemVer=L,L.prototype.format=function(){return this.version=this.major+"."+this.minor+"."+this.patch,this.prerelease.length&&(this.version+="-"+this.prerelease.join(".")),this.version},L.prototype.inspect=function(){return'<SemVer "'+this+'">'},L.prototype.toString=function(){return this.version},L.prototype.compare=function(r){return h("SemVer.compare",this.version,this.loose,r),r instanceof L||(r=new L(r,this.loose)),this.compareMain(r)||this.comparePre(r)},L.prototype.compareMain=function(r){return r instanceof L||(r=new L(r,this.loose)),F(this.major,r.major)||F(this.minor,r.minor)||F(this.patch,r.patch)},L.prototype.comparePre=function(r){if(r instanceof L||(r=new L(r,this.loose)),this.prerelease.length&&!r.prerelease.length)return-1;if(!this.prerelease.length&&r.prerelease.length)return 1;if(!this.prerelease.length&&!r.prerelease.length)return 0;var e=0;do{var t=this.prerelease[e],n=r.prerelease[e];if(h("prerelease compare",e,t,n),void 0===t&&void 0===n)return 0;if(void 0===n)return 1;if(void 0===t)return-1;if(t!==n)return F(t,n)}while(++e)},L.prototype.inc=function(r,e){switch(r){case"premajor":this.prerelease.length=0,this.patch=0,this.minor=0,this.major++,this.inc("pre",e);break;case"preminor":this.prerelease.length=0,this.patch=0,this.minor++,this.inc("pre",e);break;case"prepatch":this.prerelease.length=0,this.inc("patch",e),this.inc("pre",e);break;case"prerelease":0===this.prerelease.length&&this.inc("patch",e),this.inc("pre",e);break;case"major":0===this.minor&&0===this.patch&&0!==this.prerelease.length||this.major++,this.minor=0,this.patch=0,this.prerelease=[];break;case"minor":0===this.patch&&0!==this.prerelease.length||this.minor++,this.patch=0,this.prerelease=[];break;case"patch":0===this.prerelease.length&&this.patch++,this.prerelease=[];break;case"pre":if(0===this.prerelease.length)this.prerelease=[0];else{for(var t=this.prerelease.length;0<=--t;)"number"==typeof this.prerelease[t]&&(this.prerelease[t]++,t=-2);-1===t&&this.prerelease.push(0)}e&&(this.prerelease[0]===e?isNaN(this.prerelease[1])&&(this.prerelease=[e,0]):this.prerelease=[e,0]);break;default:throw new Error("invalid increment argument: "+r)}return this.format(),this},n.inc=function(r,e,t,n){"string"==typeof t&&(n=t,t=void 0);try{return new L(r,t).inc(e,n).version}catch(r){return null}},n.diff=function(r,e){{if(W(r,e))return null;var t=B(r),n=B(e);if(t.prerelease.length||n.prerelease.length){for(var i in t)if(("major"===i||"minor"===i||"patch"===i)&&t[i]!==n[i])return"pre"+i;return"prerelease"}for(var i in t)if(("major"===i||"minor"===i||"patch"===i)&&t[i]!==n[i])return i}},n.compareIdentifiers=F;var U=/^[0-9]+$/;function F(r,e){var t=U.test(r),n=U.test(e);return t&&n&&(r=+r,e=+e),t&&!n?-1:n&&!t?1:r<e?-1:e<r?1:0}function H(r,e,t){return new L(r,t).compare(e)}function J(r,e,t){return H(e,r,t)}function K(r,e,t){return 0<H(r,e,t)}function Q(r,e,t){return H(r,e,t)<0}function W(r,e,t){return 0===H(r,e,t)}function Y(r,e,t){return 0!==H(r,e,t)}function rr(r,e,t){return 0<=H(r,e,t)}function er(r,e,t){return H(r,e,t)<=0}function tr(r,e,t,n){var i;switch(e){case"===":"object"==typeof r&&(r=r.version),"object"==typeof t&&(t=t.version),i=r===t;break;case"!==":"object"==typeof r&&(r=r.version),"object"==typeof t&&(t=t.version),i=r!==t;break;case"":case"=":case"==":i=W(r,t,n);break;case"!=":i=Y(r,t,n);break;case">":i=K(r,t,n);break;case">=":i=rr(r,t,n);break;case"<":i=Q(r,t,n);break;case"<=":i=er(r,t,n);break;default:throw new TypeError("Invalid operator: "+e)}return i}function nr(r,e){if(r instanceof nr){if(r.loose===e)return r;r=r.value}if(!(this instanceof nr))return new nr(r,e);h("comparator",r,e),this.loose=e,this.parse(r),this.semver===ir?this.value="":this.value=this.operator+this.semver.version,h("comp",this)}n.rcompareIdentifiers=function(r,e){return F(e,r)},n.major=function(r,e){return new L(r,e).major},n.minor=function(r,e){return new L(r,e).minor},n.patch=function(r,e){return new L(r,e).patch},n.compare=H,n.compareLoose=function(r,e){return H(r,e,!0)},n.rcompare=J,n.sort=function(r,t){return r.sort(function(r,e){return n.compare(r,e,t)})},n.rsort=function(r,t){return r.sort(function(r,e){return n.rcompare(r,e,t)})},n.gt=K,n.lt=Q,n.eq=W,n.neq=Y,n.gte=rr,n.lte=er,n.cmp=tr,n.Comparator=nr;var ir={};function or(r,e){if(r instanceof or&&r.loose===e)return r;if(!(this instanceof or))return new or(r,e);if(this.loose=e,this.raw=r,this.set=r.split(/\s*\|\|\s*/).map(function(r){return this.parseRange(r.trim())},this).filter(function(r){return r.length}),!this.set.length)throw new TypeError("Invalid SemVer Range: "+r);this.format()}function sr(r){return!r||"x"===r.toLowerCase()||"*"===r}function ar(r,e,t,n,i,o,s,a,p,c,u,h,l){return((e=sr(t)?"":sr(n)?">="+t+".0.0":sr(i)?">="+t+"."+n+".0":">="+e)+" "+(a=sr(p)?"":sr(c)?"<"+(+p+1)+".0.0":sr(u)?"<"+p+"."+(+c+1)+".0":h?"<="+p+"."+c+"."+u+"-"+h:"<="+a)).trim()}function pr(r,e){for(var t=0;t<r.length;t++)if(!r[t].test(e))return!1;if(e.prerelease.length){for(t=0;t<r.length;t++){if(h(r[t].semver),r[t].semver===ir)return!0;if(0<r[t].semver.prerelease.length){var n=r[t].semver;if(n.major===e.major&&n.minor===e.minor&&n.patch===e.patch)return!0}}return!1}return!0}function cr(r,e,t){try{e=new or(e,t)}catch(r){return!1}return e.test(r)}function ur(r,e,t,n){var i,o,s,a,p;switch(r=new L(r,n),e=new or(e,n),t){case">":i=K,o=er,s=Q,a=">",p=">=";break;case"<":i=Q,o=rr,s=K,a="<",p="<=";break;default:throw new TypeError('Must provide a hilo val of "<" or ">"')}if(cr(r,e,n))return!1;for(var c=0;c<e.set.length;++c){var u=e.set[c],h=null,l=null;if(u.forEach(function(r){h=h||r,l=l||r,i(r.semver,h.semver,n)?h=r:s(r.semver,l.semver,n)&&(l=r)}),h.operator===a||h.operator===p)return!1;if((!l.operator||l.operator===a)&&o(r,l.semver))return!1;if(l.operator===p&&s(r,l.semver))return!1}return!0}nr.prototype.parse=function(r){var e=this.loose?c[X]:c[z],t=r.match(e);if(!t)throw new TypeError("Invalid comparator: "+r);this.operator=t[1],"="===this.operator&&(this.operator=""),t[2]?this.semver=new L(t[2],this.loose):this.semver=ir},nr.prototype.inspect=function(){return'<SemVer Comparator "'+this+'">'},nr.prototype.toString=function(){return this.value},nr.prototype.test=function(r){return h("Comparator.test",r,this.loose),this.semver===ir||("string"==typeof r&&(r=new L(r,this.loose)),tr(r,this.operator,this.semver,this.loose))},(n.Range=or).prototype.inspect=function(){return'<SemVer Range "'+this.range+'">'},or.prototype.format=function(){return this.range=this.set.map(function(r){return r.join(" ").trim()}).join("||").trim(),this.range},or.prototype.toString=function(){return this.range},or.prototype.parseRange=function(r){var p=this.loose;r=r.trim(),h("range",r,p);var e=p?c[P]:c[O];r=r.replace(e,ar),h("hyphen replace",r),r=r.replace(c[G],"$1$2$3"),h("comparator trim",r,c[G]),r=(r=(r=r.replace(c[V],"$1~")).replace(c[M],"$1^")).split(/\s+/).join(" ");var t=p?c[X]:c[z],n=r.split(" ").map(function(r){return t=p,h("comp",e=r),s=t,e=e.trim().split(/\s+/).map(function(r){return function(s,r){h("caret",s,r);var e=r?c[D]:c[_];return s.replace(e,function(r,e,t,n,i){var o;return h("caret",s,r,e,t,n,i),o=sr(e)?"":sr(t)?">="+e+".0.0 <"+(+e+1)+".0.0":sr(n)?"0"===e?">="+e+"."+t+".0 <"+e+"."+(+t+1)+".0":">="+e+"."+t+".0 <"+(+e+1)+".0.0":i?(h("replaceCaret pr",i),"-"!==i.charAt(0)&&(i="-"+i),"0"===e?"0"===t?">="+e+"."+t+"."+n+i+" <"+e+"."+t+"."+(+n+1):">="+e+"."+t+"."+n+i+" <"+e+"."+(+t+1)+".0":">="+e+"."+t+"."+n+i+" <"+(+e+1)+".0.0"):(h("no pr"),"0"===e?"0"===t?">="+e+"."+t+"."+n+" <"+e+"."+t+"."+(+n+1):">="+e+"."+t+"."+n+" <"+e+"."+(+t+1)+".0":">="+e+"."+t+"."+n+" <"+(+e+1)+".0.0"),h("caret return",o),o})}(r,s)}).join(" "),h("caret",e),a=t,e=e.trim().split(/\s+/).map(function(r){return s=r,e=a?c[C]:c[A],s.replace(e,function(r,e,t,n,i){var o;return h("tilde",s,r,e,t,n,i),o=sr(e)?"":sr(t)?">="+e+".0.0 <"+(+e+1)+".0.0":sr(n)?">="+e+"."+t+".0 <"+e+"."+(+t+1)+".0":i?(h("replaceTilde pr",i),"-"!==i.charAt(0)&&(i="-"+i),">="+e+"."+t+"."+n+i+" <"+e+"."+(+t+1)+".0"):">="+e+"."+t+"."+n+" <"+e+"."+(+t+1)+".0",h("tilde return",o),o});var s,e}).join(" "),h("tildes",e),h("replaceXRanges",i=e,o=t),e=i.split(/\s+/).map(function(r){return function(u,r){u=u.trim();var e=r?c[I]:c[x];return u.replace(e,function(r,e,t,n,i,o){h("xRange",u,r,e,t,n,i,o);var s=sr(t),a=s||sr(n),p=a||sr(i),c=p;return"="===e&&c&&(e=""),s?r=">"===e||"<"===e?"<0.0.0":"*":e&&c?(a&&(n=0),p&&(i=0),">"===e?(e=">=",a?(t=+t+1,i=n=0):p&&(n=+n+1,i=0)):"<="===e&&(e="<",a?t=+t+1:n=+n+1),r=e+t+"."+n+"."+i):a?r=">="+t+".0.0 <"+(+t+1)+".0.0":p&&(r=">="+t+"."+n+".0 <"+t+"."+(+n+1)+".0"),h("xRange return",r),r})}(r,o)}).join(" "),h("xrange",e),h("replaceStars",n=e,t),e=n.trim().replace(c[Z],""),h("stars",e),e;var e,t,n,i,o,a,s}).join(" ").split(/\s+/);return this.loose&&(n=n.filter(function(r){return!!r.match(t)})),n=n.map(function(r){return new nr(r,p)})},n.toComparators=function(r,e){return new or(r,e).set.map(function(r){return r.map(function(r){return r.value}).join(" ").trim().split(" ")})},or.prototype.test=function(r){if(!r)return!1;"string"==typeof r&&(r=new L(r,this.loose));for(var e=0;e<this.set.length;e++)if(pr(this.set[e],r))return!0;return!1},n.satisfies=cr,n.maxSatisfying=function(r,e,t){return r.filter(function(r){return cr(r,e,t)}).sort(function(r,e){return J(r,e,t)})[0]||null},n.validRange=function(r,e){try{return new or(r,e).range||"*"}catch(r){return null}},n.ltr=function(r,e,t){return ur(r,e,"<",t)},n.gtr=function(r,e,t){return ur(r,e,">",t)},n.outside=ur,"function"==typeof define&&define.amd&&define(n)}();