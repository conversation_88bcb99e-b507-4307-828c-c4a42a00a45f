﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.com.ecool.Models.DTO
{
    public class ZZZI25SearchViewModel
    {
        /// <summary>
        /// 排序欄位
        /// </summary>
        public string OrderByName { get; set; }

        /// <summary>
        /// Desc or ASC
        /// </summary>
        public string SyntaxName { get; set; }

        /// <summary>
        /// 頁次
        /// </summary>
        public int Page { get; set; }

        [DisplayName("搜尋條件")]
        public string SearchContents { get; set; }

        /// <summary>
        /// Q_IMG_ID
        /// </summary>
        public string Q_IMG_ID { get; set; }

        [DisplayName("狀態")]
        public string STATUS { get; set; }

        public ZZZI25SearchViewModel()
        {
            Page = 1;
            STATUS = "";
            OrderByName = "IMG_DATES";
            SyntaxName = "Desc";
        }
    }
}
