﻿using com.ecool.service;
using ECOOL_APP;
using Microsoft.Owin;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using System.Web.Routing;

namespace EcoolWeb.CustomAttribute
{
    public class CheckPermissionAttribute : ActionFilterAttribute
    {
        /// <summary>
        /// 要檢查的程式代碼，未傳入預設Controller Name
        /// </summary>
        public string CheckBRE_NO { get; set; }

        /// <summary>
        /// 要檢查的動作代碼，未傳入預設ACTION Name
        /// </summary>
        public string CheckACTION_ID { get; set; }

        /// <summary>
        /// 無權限時 回傳類型 未傳入預設 轉至 RedirectToRouteResult
        /// </summary>
        public string ResultType { get; set; }

        /// <summary>
        /// 是否取得Controller所有ACTION權限 ，未傳入預設 false ,ex 當同一個畫面有多個btn就需要使用
        /// </summary>
        public bool IsGetAllActionPermission { get; set; }

        /// <summary>
        /// 限定.HRMT01.帳號類型, 未傳入時，依 HRMT25角色權限來卡
        /// </summary>
        public string USER_TYPE { get; set; }

        public override void OnActionExecuting(ActionExecutingContext filterContext)
        {
            HttpSessionStateBase session = filterContext.HttpContext.Session;
            Controller control = filterContext.Controller as Controller;

            UserProfile user = (UserProfile)session[CONSTANT.SESSION_USER_KEY];

            string SCHOOL_NO = string.Empty;
            string USER_NO = string.Empty;

            SCHOOL_NO = Models.UserProfileHelper.GetSchoolNo();
            if (SCHOOL_NO == null) SCHOOL_NO = "";

            if (user != null)
            {
                USER_NO = user.USER_NO;
            }

            string _controller = control.RouteData.Values["Controller"].ToString();
            string _action = control.RouteData.Values["action"].ToString();

            string BRE_NO = CheckBRE_NO != null ? CheckBRE_NO : control.RouteData.Values["Controller"].ToString();
            string ACTION_ID = CheckACTION_ID != null ? CheckACTION_ID : control.RouteData.Values["action"].ToString();

            string UseYN = "N";

            if (!string.IsNullOrWhiteSpace(ACTION_ID))
            {
                var arrActionId = ACTION_ID.Split(',');

                foreach (var ThisActionId in arrActionId)
                {
                    UseYN = PermissionService.GetPermission_Use_YN(BRE_NO, ThisActionId, SCHOOL_NO, USER_NO);

                    if (UseYN == "Y")
                    {
                        break;
                    }
                }
            }

            //無使用權限
            if (UseYN == "N")
            {
                if (ResultType == "Json")
                {
                    var data = "{ \"Success\" : \"false\" , \"Error\" : \"您無權異動資料\" }";
                    var JsonResult = new JsonResult();
                    JsonResult.Data = data;

                    filterContext.Result = JsonResult;
                }
                else
                {
                    if (user == null)
                    {
                        filterContext.Result = new RedirectToRouteResult(
                        new RouteValueDictionary
                        {
                            { "controller", "Home" },
                            { "action", "LoginPage" },
                                { "returnURL",new PathString($"/{_controller}/{_action}") }
                        });
                    }
                    else
                    {
                        filterContext.Result = new RedirectToRouteResult(
                         new RouteValueDictionary
                         {
                            { "controller", "Error" },
                            { "action", "PermissionError" }
                         });
                    }
                }
            }

            if (IsGetAllActionPermission == true)
            {
                //取得Controller所有ACTION權限
                filterContext.Controller.ViewBag.BtnPermission = PermissionService.GetActionPermissionForBreNO(BRE_NO, SCHOOL_NO, USER_NO);
            }

            base.OnActionExecuting(filterContext);
        }
    }
}