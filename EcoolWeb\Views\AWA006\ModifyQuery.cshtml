﻿@* 
   ***
    此張View已不再使用 => 老師頒獎已整合至 AWA004/ModifyQuery
   ***
*@
@model EcoolWeb.ViewModels.AWA006QueryViewModel
@{ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();}
@{
    ViewBag.Title = "學生兌獎情形";
    int i = 0;
}
<style>
    .table thead > tr > th, .table tbody > tr > th, .table tfoot > tr > th, .table thead > tr > td, .table tbody > tr > td, .table tfoot > tr > td {
        border-top: none;
    }
</style>
<script type="text/javascript">
    function exportExcel() {

        var sHtml = htmlEncode($("#OutList")[0].outerHTML);//做html編碼

        $("input[name='hHtml']").val(sHtml);

        //表單提交
        document.AWA006.enctype = "multipart/form-data";
        document.AWA006.action = "ExportExcel";
        document.AWA006.submit();
        //$("form[name='AWA004']").submit();
    }
    //↓出自：http://stackoverflow.com/questions/1219860/javascript-jquery-html-encoding
    function htmlEncode(value) {
        //create a in-memory div, set it's inner text(which jQuery automatically encodes)
        //then grab the encoded contents back out.  The div never exists on the page.
        return $('<div/>').text(value).html();
    }


</script>

@using (Html.BeginForm("ModifyQuery", "AWA006", FormMethod.Post, new { name = "AWA006", id = "AWA006" }))
{
    @section scripts{
        <script>

            function btnTRANS_NO_onclick(TRANS_NO) {
                $("#hidSelectTRANS_NO").val(TRANS_NO);
                document.AWA006.enctype = "multipart/form-data";
                document.AWA006.action = "CancelTrans";
                document.AWA006.submit();
            }

            function btnSend_onclick() {
                document.AWA006.enctype = "multipart/form-data";
                document.AWA006.action = "Modify";
                document.AWA006.submit();
            }

            function btnSearch_onclick() {
                document.AWA006.enctype = "multipart/form-data";
                document.AWA006.action = "ModifyQuery";
                document.AWA006.submit();
            }

            $("#chkALL").click(function () {

                if ($("#chkALL").prop("checked")) {
                    $("input:checkbox").each(function () {
                        $(this).prop("checked", true);
                    });
                }
                else {
                    $("input:checkbox").each(function () {
                        $(this).prop("checked", false);
                    });
                }
            });

            var targetFormID = '#AWA006';
            $(function () {

                // Fields
                var _pageLinkers = $(".pager> a");

                // Binding click event
                _pageLinkers.each(function (i, item) {
                    var page = getParameterByName($(item).attr('href'), 'page')
                    $(item).attr('href', '#').click(function () { postPage(page); });
                });

            });

            function getParameterByName(url, name) {
                name = name.replace(/[\[]/, "\\[").replace(/[\]]/, "\\]");
                var regex = new RegExp("[\\?&]" + name + "=([^&#]*)"),
                    results = regex.exec(url);
                return results === null ? "" : decodeURIComponent(results[1].replace(/\+/g, " "));
            }

            function postPage(page) {
                if ($(targetFormID).size() > 0) {
                    $('<input>')
                        .attr({ type: 'hidden', id: 'page', name: 'page', value: page })
                        .appendTo($(targetFormID));
                    $(targetFormID).submit();
                }
            };

            function doSearch(ColName, whereValue) {
                $("#" + ColName).val(whereValue);
                document.AWA006.enctype = "multipart/form-data";
                document.AWA006.action = "ModifyQuery";
                document.AWA006.submit();
            }
        </script>
    }
    <div class="col-md-6">
        <input type="button" id="btnSend" value="設定為已兌換" class="btn-primary btn btn-sm" onclick="btnSend_onclick();" />
        <input type="button" id="btnExcel" value="匯出Excel" class="btn-primary btn btn-sm" onclick="exportExcel();" />
    </div>
    <div class=" col-md-offset-1 col-md-9" style="margin:10px">
        <span>搜尋瀏覽請輸入相關字串[學號/姓名]:</span>
        @Html.EditorFor(m => m.whereKeyword, new { htmlAttributes = new { @style = "width:100px" } })
        @Html.HiddenFor(m => m.whereSNAME)
        @Html.HiddenFor(m => m.whereAWARD_NAME)
        @Html.HiddenFor(m => m.whereCLASS_NO)
        @Html.Hidden("hidSelectTRANS_NO")
        <input id="btnSearch" name="btnSearch" onclick="btnSearch_onclick();" type="button" value="搜尋" />
    </div>

    <br />
    <div class="pager">
        @Html.Pager(Model.VAWA005List.PageSize, Model.VAWA005List.PageNumber, Model.VAWA005List.TotalItemCount)
        @*@Html.Pager(Model.ADDT01List.PageSize,Model.ADDT01List.PageNumber,Model.ADDT01List.TotalItemCount).Options(o=>o.AddRouteValueFor(m=>m.whereKeyword))*@
        @*Displaying @<EMAIL> of @Model.ADDT01List.TotalItemCount item(s)*@
        @Html.Hidden("hHtml")
    </div>

    <img src="~/Content/img/web-Bar-20.png" />

    <table class="table" style="white-space: nowrap;" id="OutList">
        <tr class="ListColName">
            <th style="text-align: center;">
                選擇全部
                <input type="checkbox" id="chkALL" name="chkALL" />
            </th>
            <th style="text-align: center;">
                學年
            </th>
            <th style="text-align: center;">
                學期
            </th>
            <th style="text-align: center;">
                班級
            </th>
            <th style="text-align: center;">
                座號
            </th>
            <th style="text-align: center;">
                姓名
            </th>
            <th style="text-align: center;">
                獎品
            </th>
            <th style="text-align: center;">
                酷幣數
            </th>
            <th style="text-align: center;">
                狀態
            </th>
            <th style="text-align: center;">
                日期
            </th>
            <th style="text-align: center;">
                備註
            </th>
        </tr>
        @{
    string iUser_no = (user != null) ? user.USER_NO : "";
        }
        @foreach (var item in Model.VAWA005List)
        {
            <tr class="ListRow">
                <td>
                    <input type="checkbox" id='[@i].chkTRANS_NO' name='[@i].chkTRANS_NO'>
                    <input type="hidden" id='[@i].TRANS_NO' name='[@i].TRANS_NO' value=@item.TRANS_NO />
                    @*@Html.CheckBox("check", new { value = item.TRANS_NO.ToString(), id = item.TRANS_NO.ToString() })*@
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.SYEAR)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.SEMESTER)
                </td>
                <td style="text-align: center;cursor:pointer;" onclick="doSearch('whereCLASS_NO','@item.CLASS_NO');">
                    @Html.DisplayFor(modelItem => item.CLASS_NO)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.SEAT_NO)
                </td>
                @if (item.SNAME.Trim() == "")
                {
                    <td>
                        @Html.DisplayFor(modelItem => item.SNAME)
                    </td>
                }
                else
                {
                    <td style="text-align: center;cursor:pointer;" onclick="doSearch('whereSNAME','@item.NAME');">
                        @Html.DisplayFor(modelItem => item.NAME)
                    </td>
                }

                <td style="text-align: center;cursor:pointer;" onclick="doSearch('whereAWARD_NAME','@item.AWARD_NAME');">
                    @Html.DisplayFor(modelItem => item.AWARD_NAME)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.COST_CASH)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.CTRANS_STATUS)
                    @if (item.CTRANS_STATUS == "已訂未領")
                    {
                        <br />
                        <input type="button" id='[@i].btnTRANS_NO' name='[@i].btnTRANS_NO' value="取消領取" onclick="btnTRANS_NO_onclick('@item.TRANS_NO');" class="btn-primary btn btn-sm">
                        <input type="hidden" id='[@i].TRANS_NO' name='[@i].TRANS_NO' value=@item.TRANS_NO />
                    }
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.TRANS_DATE, "ShortDateTime")
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.MEMO)
                </td>
            </tr>
                    i++;
        }
    </table>

    <div class="col-md-6">
        <input type="button" id="btnSend2" value="設定為已兌換" class="btn-primary btn btn-sm" onclick="btnSend_onclick();" />
        <input type="button" id="btnExcel" value="匯出Excel" class="btn-primary btn btn-sm" onclick="exportExcel();" />
    </div>
}
