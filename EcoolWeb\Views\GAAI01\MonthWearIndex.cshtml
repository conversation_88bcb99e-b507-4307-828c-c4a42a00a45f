﻿@model GAAI01MonthWearIndexViewModel

@using ECOOL_APP.com.ecool.util;
@{ECOOL_DEVEntities db = new ECOOL_DEVEntities();
    ViewBag.Title = ViewBag.Panel_Title;

    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
}

<link href="~/Scripts/tablesorter/dist/css/theme.blue.css" rel="stylesheet" />
@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@{
    Html.RenderAction("_PageMenu", new { NowAction = "MonthWearIndex" });
}

@using (Html.BeginForm("MonthWearIndex", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1", @class = "form-horizontal", enctype = "multipart/form-data" }))
{

    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })
    @Html.HiddenFor(m => m.<PERSON>)

    <div class="panel panel-ZZZ" name="TOP">
        <div class="panel-heading text-center">
            @Html.BarTitle(null, (string)ViewBag.Title)
        </div>
        <div class="panel-body">
            <div class="form-horizontal">
                <div class="form-group row">
                    <label class="col-md-3 control-label">
                        年月
                    </label>
                    <div class="col-md-9">
                        @if (ViewBag.YearMonthItem != null)
                        {
                            @Html.DropDownListFor(m => m.WhereYearMonth, (IEnumerable<SelectListItem>)ViewBag.YearMonthItem, new { @class = "form-control" })
                        }
                    </div>
                    <div class="col-md-9">
                        @Html.ValidationMessageFor(m => m.WhereYearMonth, "", new { @class = "text-danger" })
                    </div>
                </div>
            </div>
            <div class="text-center">
                <button type="button" class="btn btn-default" onclick="onSearch()">
                    <span class="fa fa-check-circle" aria-hidden="true"></span>搜尋
                </button>
            </div>
        </div>
    </div>

    if (Model.IsSearch == 1)
    {
        if (Model.MonthMaxGradeWearrRate != null && Model.dMT01 != null)
        {
            string[] chineseNumber = { "零", "一年級", "二年級", "三年級", "四年級", "五年級", "六年級", "七年級", "八年級", "九年級" };
            <br />
            <div class="text-center">
                <div id="MonthMaxGradeWearrRateDiv" class="text-center">
                    <div class="row">
                        <div class="col-md-12 text-center">
                            <button type="button" class="btn btn-default btn-xs no-print" onclick="onprint('#MonthMaxGradeWearrRateDiv')">
                                列印
                            </button>
                            <a role="button" class="btn btn-default btn-xs no-print" target="_blank" href="@Url.Action("Official",new {WhereSCHOOL_NO= Model.WhereSCHOOL_NO,WhereYearMonth =Model.WhereYearMonth})">
                                匯出到規定的格式
                            </a>
                        </div>
                    </div>
                    <h4>
                        @(Model.dMT01.SCHOOL_NAME) 防身警報器每月年級工作檢核表
                    </h4>
                    <table id="MonthMaxGradeWearrRateTable" class="tablesorter-blue">
                        <thead>
                            <tr class="text-center">
                                <td>日期</td>
                                <td>年級</td>
                                <td>學生人數</td>
                                <td>配戴人數</td>
                                <td>配戴比率</td>
                                <td>未配戴學因</td>
                            </tr>
                        </thead>
                        @if (Model.MonthMaxGradeWearrRate?.Count > 0)
                        {

                            
                            <tbody>
                                @foreach (var item in Model.MonthMaxGradeWearrRate)
                                {
                                    int STUDNUMber = 0;
                                    decimal wareRate = 0;
                                    

                                    STUDNUMber = db.HRMT01.Where(x => x.SCHOOL_NO == user.SCHOOL_NO && x.GRADE == item.GRADE && x.USER_TYPE == "S" && x.USER_STATUS == UserStaus.Enabled).Count();
                                <tr class="text-center">
                                    <td nowrap="nowrap"> </td>
                                    @if (item.GRADE != null)
                                    {
                                        <td>@chineseNumber[(int)item.GRADE]</td>

                                    }
                                    else
                                    {
                                        <td></td>}
                                    <td>@STUDNUMber</td>


                                    @if (item.WEAR_NUMBER > STUDNUMber)
                                    {
                                        wareRate = 1;

                                        <td>@STUDNUMber</td>


                                    }

                                    else
                                    {
                                        if (STUDNUMber != 0)
                                        {

                                            wareRate = (decimal)((decimal)item.WEAR_NUMBER / (decimal)STUDNUMber);
                                        }

                                        //  wareRate = item.WEAR_RATE.Value;

                                        <td>


                                            @item.WEAR_NUMBER
                                        </td>
                                    }

                                    @if (item.WEAR_NUMBER > STUDNUMber)
                                    {
                                        wareRate = 1;




                                    }

                                    else
                                    {
                                        if (STUDNUMber != 0)
                                        {

                                            wareRate = (decimal)((decimal)item.WEAR_NUMBER / (decimal)STUDNUMber);
                                        }

                                        //  wareRate = item.WEAR_RATE.Value;


                                    }
                                    <td>
                                        @if (wareRate != null)
                                        {
                                            @wareRate.ToString("#,0%")

                                        }
                                    </td>

                                    <td>@item.UN_WEAR_MEMO</td>
                                </tr>
                                }
                            </tbody>

                        }
                    </table>
                    <div class="text-center print-only">
                        <table style="width:95%">
                            <tr>
                                <td>填表人</td>
                                <td>主任</td>
                                <td>校長</td>
                                <td>督學</td>
                            </tr>
                        </table>
                    </div>
                </div>
                <br />
                <div id="MonthMaxClassWearrRateDiv">
                    <div class="row">
                        <div class="col-md-12 text-center">
                            <button type="button" class="btn btn-default btn-xs no-print" onclick="onprint('#MonthMaxClassWearrRateDiv')">
                                列印
                            </button>
                        </div>
                    </div>
                    <h4>
                        @(Model.dMT01.SCHOOL_NAME) 防身警報器每月每班工作檢核表
                    </h4>
                    <table id="WearDetailListTable" class="tablesorter-blue">
                        <thead>
                            <tr class="text-center">
                                <td>日期</td>
                                <td>班級</td>
                                <td>學生人數</td>
                                <td>配戴人數</td>
                                <td>配戴比率</td>
                                <td>未配戴學因</td>
                            </tr>
                        </thead>

                        @if (Model.MonthMaxClassWearrRate?.Count > 0)
                        {
                            <tbody>
                                @foreach (var item in Model.MonthMaxClassWearrRate)
                                {
                                    int STUDNUMber = 0;
                                    STUDNUMber = db.HRMT01.Where(x => x.SCHOOL_NO == user.SCHOOL_NO && x.CLASS_NO == item.CLASS_NO && x.USER_TYPE == "S" && x.USER_STATUS == UserStaus.Enabled).Count();
                                    <tr class="text-center">
                                        <td nowrap="nowrap">@(DateHelper.ToSimpleTaiwanDate(item.ALARM_DATES)) - @(DateHelper.ToSimpleTaiwanDate(item.ALARM_DATEE))</td>
                                        <td>@item.CLASS_NO</td>
                                        <td>@STUDNUMber</td>
                                        <td>@item.WEAR_NUMBER</td>
                                        <td>
                                            @if (item.WEAR_RATE != null)
                                            {
                                                @item.WEAR_RATE.Value.ToString("#,0%")

                                            }
                                        </td>
                                        <td>@item.UN_WEAR_MEMO</td>
                                    </tr>
                                }
                            </tbody>

                        }
                    </table>
                </div>
            </div>
        }
        else
        {
            <div class="text-center">
                <h2>
                    查無任何資料
                </h2>
            </div>
        }

    }

}

@section Scripts {
    <script src="~/Scripts/printThis/printThis.js"></script>
    <script src="~/Scripts/tablesorter/dist/js/jquery.tablesorter.js"></script>
    <script src="~/Scripts/tablesorter/dist/js/jquery.tablesorter.widgets.js"></script>
    <script language="JavaScript">

        var targetFormID = '#form1';

        $(function() {
            $(".tablesorter-blue").tablesorter();
        });

        function onSearch()
        {
            $('#@Html.IdFor(m=>m.IsSearch)').val(1)
            $(targetFormID).attr("action", "@Url.Action("MonthWearIndex", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        function onprint(DivId) {
              $(DivId).printThis();
        }
    </script>
}