﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using ECOOL_APP;
using EcoolWeb.Models;
using ECOOL_APP.com.ecool.Models.entity;
using com.ecool.service;
using EcoolWeb.CustomAttribute;

namespace EcoolWeb.Controllers
{
    [SessionExpire]
    public class ADDT03Controller : Controller
    {
        // GET: ADDT03
        public ActionResult _ADDT03()
        {
            UserProfile user = UserProfileHelper.Get();
            List<uADDT03> liADDT03 = new List<uADDT03>();
            try
            {
                liADDT03 = ADDT03Service.USP_ReadADDT03_QUERY(user.USER_NO, user.SCHOOL_NO, user.GRADE.ToString());
            }
            catch (Exception e)
            {
                throw;
            }
            return PartialView("../ADDT03/_ADDT03", liADDT03);
        }
    }
}