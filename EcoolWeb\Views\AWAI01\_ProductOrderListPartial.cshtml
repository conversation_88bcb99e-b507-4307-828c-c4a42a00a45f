﻿@model AWAI01OrderListViewModel


@Html.HiddenFor(m => m.Search.OrdercColumn)
@Html.HiddenFor(m => m.Search.SyntaxName)
@Html.HiddenFor(m => m.Search.Page)
@Html.HiddenFor(m => m.Search.BackAction)
@Html.HiddenFor(m => m.Search.BackController)

@Html.HiddenFor(m => m.Search.WhereSCHOOL_NO)
@Html.HiddenFor(m => m.Search.WhereAWARD_NO)
@Html.HiddenFor(m => m.Search.WhereAWARD_STS)
@Html.HiddenFor(m => m.Search.WhereSouTable)
@Html.HiddenFor(m => m.Search.unProduct)
@Html.HiddenFor(m => m.Search.whereAWARD_SCHOOL_NO)
@Html.HiddenFor(m => m.Search.AWARD_TYPE)

<div id="Q_Div">
    <div class="form-inline" role="form">
        <div class="form-group">
            <label class="control-label">
                @Html.DisplayNameFor(model => model.Search.whereKeyword)
            </label>
        </div>
        <div class="form-group">
            @Html.EditorFor(m => m.Search.whereKeyword, new { htmlAttributes = new { @class = "form-control" } })
        </div>
        <br />
        <div id="Sdate">
            <div class="form-group">
                <label class="control-label">日期(起)</label>
            </div>

            <div class="form-group">
                @Html.EditorFor(m => m.Search.SDATETIME, new { htmlAttributes = new { @class = "form-control input-sm", autocomplete = "off" } })
            </div>
            <div class="form-group">
                <label class="control-label">日期(迄)</label>
            </div>
            <div class="form-group">
                @Html.EditorFor(m => m.Search.EDATETIME, new { htmlAttributes = new { @class = "form-control input-sm", autocomplete = "off" } })
            </div>
        </div>
        <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1)" />
        <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />
    </div>
    <div style="height:10px"></div>
    <div class="form-group">
        @Html.LabelFor(model => model.Search.StopProduct, htmlAttributes: new { @class = "control-label" })
        @Html.CheckBoxFor(m => m.Search.StopProduct)
        <label>是</label>
    </div>
</div>

<label class="label_dt"><img src='~/Content/img/web-revise-prize-03s.png' style="max-height:30px;margin-right:5px">   此獎品圖示：代表總召學校獎品</label>



<br />
<div class="row">
    <div class="col-md-6 col-xs-12 text-left">
        <samp>獎品類別：</samp>
        @foreach (var item in Model.AwardSchoolItemQuery)
        {
            string active = item.Selected ? "active" : "";
            <button class="btn btn-xs btn-pink @(active)" type="button" onclick="doSearch('@Html.IdFor(m => m.Search.whereAWARD_SCHOOL_NO)', '@item.Value');">@item.Text</button>
        }
    </div>
    <div class="col-md-6 col-xs-12 text-left">
        <samp>獎品狀態：</samp>
        @foreach (var item in Model.AwardTypeItemQuery)
        {
            string active = item.Selected ? "active" : "";
            <button class="btn btn-xs btn-pink @(active)" type="button" onclick="doSearch('@Html.IdFor(m => m.Search.AWARD_TYPE)', '@item.Value');">@item.Text</button>
        }
    </div>
</div>
<br />

<img src="~/Content/img/web-bar2-revise-09.png" style="width:100%" class="img-responsive App_hide" alt="Responsive image" />
<div class="table-responsive">
    <table class="table-ecool table-92Per table-hover table-ecool-reader">
        <thead>
            <tr>
                <th></th>
                <th   onclick="doSort('AWARD_NAME');">

                    @Html.DisplayNameFor(m => m.ListData.First().AWARD_NAME)
                    <img id="AWARD_NAME" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                </th>
                <th onclick="doSort('AWARD_TYPE');">

                    @Html.DisplayNameFor(m => m.ListData.First().AWARD_TYPE)
                    <img id="AWARD_TYPE" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />

                </th>
              
                <th   onclick="doSort('COST_CASH');">

                    @Html.DisplayNameFor(m => m.ListData.First().COST_CASH)
                    <img id="COST_CASH" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                </th>
                <th  onclick="doSort('QTY_STORAGE');">

                    @Html.DisplayNameFor(m => m.ListData.First().QTY_STORAGE)
                    <img id="QTY_STORAGE" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                </th>
                <th   onclick="doSort('QTY_TRANS');">
                    @Html.DisplayNameFor(m => m.ListData.First().QTY_TRANS)
                    <img id="QTY_TRANS" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                </th>
                <th  onclick="doSort('RATE');">

                    @Html.DisplayNameFor(m => m.ListData.First().RATE)

                    <img id="RATE" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                </th>
            </tr>
        </thead>
        <tbody>
            @foreach (var item in Model.ListData)
            {
                string aImgUrl = string.Empty;
                if (Model.Search.WhereSouTable == AWAI01SearchViewModel.SouTableVal.Teacher)
                {
                    aImgUrl = ViewBag.ImgUrl + @"/" + item.IMG_FILE;
                }
                else
                {
                    aImgUrl = ViewBag.ImgUrl + item.SCHOOL_NO + @"/" + item.IMG_FILE;
                }
            <tr>
                <td class="ImgUrl">
                    <img src='@aImgUrl' title="@item.AWARD_NAME" href="@aImgUrl" style="max-width:120px;max-height:120px">

                </td>
                <td>
                    @if (item.SCHOOL_NO == SharedGlobal.ALL)
                    {
                        <img src='~/Content/img/web-revise-prize-03s.png' style="max-height:30px;" class="img-responsive" />
                    }
                    @Html.DisplayFor(modelItem => item.AWARD_NAME)
                </td>
                <td>@Awa.AWARD_TYPE_Val.GetDesc(item.AWARD_TYPE)</td>
                <td align="center">@Html.DisplayFor(modelItem => item.COST_CASH)</td>
                <td align="center">@Html.DisplayFor(modelItem => item.QTY_STORAGE)</td>
                <td align="center">@Html.DisplayFor(modelItem => item.QTY_TRANS)</td>
                <td align="center">
                    @Html.DisplayFor(modelItem => item.RATE)<text>%</text>

                </td>
                @*@Html.DisplayFor(modelItem => item.AWARD_NO)*@
                <td align="center"> <a class="btn-default btn btn-xs btn-prod" style="" role="button" onclick="funGetModify('@Html.DisplayFor(modelItem => item.AWARD_NO)')">修改</a>   </td>
            </tr>
            }
        </tbody>
    </table>
</div>

<div>
    @Html.Pager(Model.ListData.PageSize, Model.ListData.PageNumber, Model.ListData.TotalItemCount).Options(o => o
                            .DisplayTemplate(PageGlobal.DfDisplayTemplate)
                            .MaxNrOfPages(PageGlobal.DfMaxNrOfPages)
                            .SetPreviousPageText(PageGlobal.DfSetPreviousPageText)
                            .SetNextPageText(PageGlobal.DfSetNextPageText)
                            )
</div>


<script>


    var targetFormID = '#form1';
    window.onload = function () { initDatepicker(); }
    $(document).ready(function () {
        $(".ImgUrl img").colorbox({ opacity: 0.82 });
    });

      function funGetModify(AWARD_NO) {
            $('#@Html.IdFor(m=>m.Search.WhereAWARD_NO)').val(AWARD_NO);
          $('#@Html.IdFor(m=>m.Search.BackController)').val('AWAI01');
            $('#@Html.IdFor(m => m.Search.BackAction)').val('ProductOrderList');
            $('#@Html.IdFor(m=>m.Search.WhereAWARD_STS)').val('M');
            $(targetFormID).attr("action", "@Url.Action("AwatMana02", (string) ViewBag.BRE_NO)")
            $(targetFormID).submit();
    }
  
    function doSort(SortCol) {
        $("#Search_OrdercColumn").val(SortCol);
        FunPageProc(1);
    }
function FunPageProc(page) {
            if ($(targetFormID).size() > 0) {
                $('#@Html.IdFor(m=>m.Search.Page)').val(page)
                funAjax()
            }
    };
      //查詢
                 function funAjax() {

            $.ajax({
                     url: '@Url.Action("_ProductOrderListPartial", (string)ViewBag.BRE_NO)',
                data: $(targetFormID).serialize(),
                type: 'post',
                async: false,
                cache: false,
                dataType: 'html',
                success: function (data) {
                    $('#PageContent').html(data);
                         }
                     });
    }
    function initDatepicker() {
                   var opt = {
                        showMonthAfterYear: true,
                        format: moment().format('YYYY-MM-DD'),
                        showSecond: true,
                        showButtonPanel: true,
                        showTime: true,
                        beforeShow: function () {
                            setTimeout(
                                function () {
                                    $('#ui-datepicker-div').css("z-index", 15);
                                }, 100
                            );
                        },
                        onSelect: function (dateText, inst) {
                            $('#' + inst.id).attr('value', dateText);
                        }
                    };
                    $("#@Html.IdFor(m => m.Search.SDATETIME)").datetimepicker(opt);
                    $("#@Html.IdFor(m => m.Search.EDATETIME)").datetimepicker(opt);
        }
            
</script>





