﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Text;

namespace ECOOL_APP.EF
{
    public class ADDI05TotalGraphDetailsViewModel
    {
        public string DIALOG_ID { get; set; }
        public int? Q_NUM { get; set; }

        public string ANSWER { get; set; }

        [DisplayName("班級")]
        public string CLASS_NO { get; set; }

        public ADDT11 MADDT11 { get; set; }

        public ADDT12 DADDT12 { get; set; }

        public List<ADDI05TotalGraphDetailsListViewModel> AnsList { get; set; }
    }

    public class ADDI05TotalGraphDetailsListViewModel
    {
        /// <summary>
        ///流水號
        /// </summary>
        [DisplayName("流水號")]
        public string HIS_NO { get; set; }

        /// <summary>
        ///活動ID
        /// </summary>
        [DisplayName("活動ID")]
        public string DIALOG_ID { get; set; }

        /// <summary>
        ///學校代碼
        /// </summary>
        [DisplayName("學校代碼")]
        public string SCHOOL_NO { get; set; }

        [DisplayName("學校")]
        public string SHORT_NAME { get; set; }

        /// <summary>
        ///帳號
        /// </summary>
        [DisplayName("帳號")]
        public string USER_NO { get; set; }

        /// <summary>
        ///班級
        /// </summary>
        [DisplayName("班級")]
        public string CLASS_NO { get; set; }

        /// <summary>
        ///學年度
        /// </summary>
        [DisplayName("學年度")]
        public int? SYEAR { get; set; }

        /// <summary>
        ///學期
        /// </summary>
        [DisplayName("學期")]
        public byte? SEMESTER { get; set; }

        /// <summary>
        ///座號
        /// </summary>
        [DisplayName("座號")]
        public string SEAT_NO { get; set; }

        /// <summary>
        ///姓名
        /// </summary>
        [DisplayName("姓名")]
        public string NAME { get; set; }

        /// <summary>
        ///簡稱
        /// </summary>
        [DisplayName("簡稱")]
        public string SNAME { get; set; }

        /// <summary>
        ///建立人
        /// </summary>
        [DisplayName("答題人")]
        public string CRE_PERSON { get; set; }

        /// <summary>
        ///建立日期
        /// </summary>
        [DisplayName("答題日期")]
        public DateTime? CRE_DATE { get; set; }

        /// <summary>
        ///是否答題正確
        /// </summary>
        [DisplayName("是否答題正確")]
        public string RIGHT_YN { get; set; }

        /// <summary>
        ///題目序號
        /// </summary>
        [DisplayName("題目序號")]
        public int? Q_NUM { get; set; }

        /// <summary>
        ///正確與否
        /// </summary>
        [DisplayName("是否全部答對")]
        public string ALL_RIGHT_YN { get; set; }

        /// <summary>
        ///回答內容
        /// </summary>
        [DisplayName("回答內容")]
        public string ANSWER { get; set; }
    }
}