/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/SizeTwoSym/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.STIXSizeTwoSym={directory:"SizeTwoSym/Regular",family:"STIXSizeTwoSym",Ranges:[[688,767,"All"],[768,824,"All"],[8254,8254,"All"],[8400,8431,"All"],[8730,8732,"All"],[9115,9145,"All"],[9180,9185,"All"],[10098,10099,"All"],[10214,10219,"All"],[10627,10630,"All"],[11004,11007,"All"]],32:[0,0,250,0,0],40:[1566,279,589,139,503],41:[1566,279,608,114,478],47:[1566,279,806,25,781],91:[1566,279,459,190,422],92:[1566,279,806,25,781],93:[1566,279,459,37,269],95:[-127,177,1500,0,1500],123:[1566,279,717,124,531],125:[1566,279,717,186,593],160:[0,0,250,0,0],770:[777,-564,0,-1150,-171],771:[760,-608,0,-1152,-173],8730:[2056,404,1124,110,1157],8968:[1566,279,524,190,479],8969:[1566,279,526,47,336],8970:[1566,279,524,190,479],8971:[1566,279,526,47,336],9182:[143,81,1460,0,1460],9183:[797,-573,1460,0,1460],10216:[1566,279,622,95,531],10217:[1566,279,622,91,527]};MathJax.OutputJax["HTML-CSS"].initFont("STIXSizeTwoSym");MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/SizeTwoSym/Regular/Main.js");
