﻿<link href="@Url.Content("~/Content/css/fileupload.css")" rel="stylesheet" type="text/css" />

<!-- Modal -->
<div class="modal fade" id="excelModal" role="dialog">
    <div class="modal-dialog">
        <!-- Modal content-->
        <div class="modal-content">
            <div class="modal-header" style="background-color:#4783bf;color:white">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">檔案上傳</h4>
            </div>
            <div class="modal-body">
                <h4 class="text-info">Step1. 選擇一個Excel檔案</h4>
                <a href="@Url.Content("~/Content/ExcelSample/ImportTeacherZZZI08Sample.xls")" target="_blank" class="btn-table-link pull-right">
                    <span class="glyphicon glyphicon-download" aria-hidden="true"></span>
                    下載 Sample
                </a>
                <!--file input example -->
                <span class="control-fileupload">
                    <label for="file">選擇檔案</label>
                    <input type="file" name="files" id="files" required accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel">
                </span>
                <br />

                <p>欄位範例: </p>
                <table class="table table-bordered">
                    <thead>
                        <tr>

                            <th>學校代號<span class="text-danger">*</span></th>

                            <th>登入帳號(單一登入帳號)<span class="text-danger">*</span></th>
                            <th>帳號(同身分證字號)<span class="text-danger">*</span></th>
                            <th>教師姓名<span class="text-danger">*</span></th>
                            <th>身分證字號<span class="text-danger">*</span></th>
                            <th>性別<span class="text-danger">*</span></th>
                            <th>生日<span class="text-danger">*</span></th>
                            <th>電話<span class="text-danger">*</span></th>
                            <th>在校狀態(1:在校, 2:離校)<span class="text-danger">*</span></th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>

                            <td>154696</td>

                            <td><EMAIL></td>
                            <td>A123456789</td>
                            <td>李大明</td>
                            <td>A123456789</td>
                            <td>1</td>
                            <td>1978/9/20</td>
                            <td>090000000</td>
                            <td>1</td>
                        </tr>
                    </tbody>
                </table>
                <p><span class="text-danger">*表示必填欄位</span></p>
                <p><span class="text-danger">性別(2:女 / 1:男)</span></p>
                <hr />
                <h4 class="text-info">Step2. 上傳檔案</h4>
                <button type="submit" class="btn btn-default btn-lg">確定上傳</button>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
    $(function () {
        $('input[type=file]').change(function () {
            var t = $(this).val();
            var labelText = 'File : ' + t.substr(12, t.length);
            $(this).prev('label').text(labelText);
        })
    });
</script>