﻿using ECOOL_APP;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace EcoolWeb.Models
{
    public class LoginAppFirstInAppViewModel
    {
        /// <summary>
        /// UUID + Key + LAST_LOGIN_TIME ==>MD5 加密
        /// </summary>
        public string CkAppKey { get; set; }

        /// <summary>
        /// UUID
        /// </summary>
        public string UUID { get; set; }

        /// <summary>
        /// DEVICE_TOKEN
        /// </summary>
        public string DEVICE_TOKEN { get; set; }

        /// <summary>
        /// 當下時間 格式為 yyyyMMddHHmmss
        /// </summary>
        public string LAST_LOGIN_TIME { get; set; }

        /// <summary>
        /// 學校代碼
        /// </summary>
        public string SCHOOL_NO { get; set; }

        /// <summary>
        /// 帳號/學號
        /// </summary>
        public string USER_NO { get; set; }

        /// <summary>
        /// 手機作業系統(除錯用) 1.IOS 2.android
        /// </summary>
        public byte? OS_TYPE { get; set; }

        /// <summary>
        /// 作業系統 版本 (除錯用)
        /// </summary>
        public string OS_VER { get; set; }

        /// <summary>
        /// Ecool APP  版本 (除錯用)
        /// </summary>
        public string APP_VER { get; set; }

        /// <summary>
        /// * 密碼
        /// </summary>
        public string Password { get; set; }
    }

    public class LoginAppDebugViewModel
    {
        /// <summary>
        /// UUID
        /// </summary>
        public string UUID { get; set; }

        /// <summary>
        /// 當下時間 格式為 yyyyMMddHHmmss
        /// </summary>
        public string LAST_LOGIN_TIME { get; set; }

        /// <summary>
        /// 暫存序號
        /// </summary>
        public int TempNo { get; set; }
    }

    public class UseData
    {
        public string Success { get; set; }
        public string TempNO { get; set; }

        public bool isParentsHrmt06Null { get; set; }

        public UserProfile UserData;
        public UserBackProfile UserBackData;
        public string PlayerUrl { get; set; }

        public int? Badge { get; set; }

        public string Error { get; set; }

        public string APP_VER_NOW { get; set; }
    }

    public class PushViewModel
    {
        /// <summary>
        /// DEVICE_TOKEN
        /// </summary>
        public string DEVICE_TOKEN { get; set; }
    }

    public class AppVerViewModel
    {
        /// <summary>
        /// OS_TYPE
        /// </summary>
        public string OS_TYPE { get; set; }

        /// <summary>
        /// SYS_ID
        /// </summary>
        public string SYS_ID { get; set; }

        public AppVerViewModel()
        {
            SYS_ID = "001";
        }
    }
}