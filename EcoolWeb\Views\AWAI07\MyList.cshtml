﻿@model AWAI07IndexViewModel

@{
    ViewBag.Title = "酷幣定存-我的資產";
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

<script src="~/Scripts/Highcharts-4.0.1/js/highcharts.js"></script>
<script src="~/Scripts/Highcharts-4.0.1/js/modules/exporting.js"></script>

@using (Html.BeginForm("Index", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1" }))
{
    @Html.AntiForgeryToken()
    <div id="PageContent">
        @Html.Action("_PageContent", (string)ViewBag.BRE_NO)
    </div>
}



@section scripts{
    <script type="text/javascript">

        var targetFormID = '#form1';

        function FunPageProc(page) {
            if ($(targetFormID).size() > 0) {
                $('#@Html.IdFor(m=>m.Search.Page)').val(page)
                funAjax()
            }
        };

        function onAdd() {
            $('#@Html.IdFor(m => m.Search.WhereA_NO)').val('')
            $(targetFormID).attr("action", "@Url.Action("Edit", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        function Edit_show(Value) {
                $('#@Html.IdFor(m => m.Search.WhereA_NO)').val(Value)
                $(targetFormID).attr("action", "@Url.Action("Edit", (string)ViewBag.BRE_NO)")
                $(targetFormID).submit();
        }


        function doSort(SortCol) {
            $('#@Html.IdFor(m => m.Search.OrdercColumn)').val(SortCol);
            FunPageProc(1)
        }

        //查詢
        function funAjax() {

            $.ajax({
                url: '@Url.Action("_PageContent", (string)ViewBag.BRE_NO)',
                data: $(targetFormID).serialize(),
                type: 'post',
                async: false,
                cache: false,
                dataType: 'html',
                success: function (data) {
                    $('#PageContent').html(data);
                }
            });
        }

        function todoClear() {
            ////重設

            $("#Q_Div").find(":input,:selected").each(function (i) {

                var type = $(this).attr('type')
                var InPreadonly = $(this).attr('readonly')
                var tag = this.tagName.toLowerCase(); // normalize case

                if (InPreadonly == false || InPreadonly == undefined) {

                    if (type == 'radio' || type == 'checkbox') {
                        if ($(this).attr("title") == 'Default') {
                            this.checked = true;
                        }
                        else {
                            this.checked = false;
                        }
                    }
                    else if (tag == 'select') { //下拉式選單
                        this.selectedIndex = 0;
                    }
                    else if (type == 'text' || type == 'hidden' || type == 'password' || type == 'textarea') {
                        this.value = '';
                    }
                }
            });

            FunPageProc(1);
        }

    </script>
}




