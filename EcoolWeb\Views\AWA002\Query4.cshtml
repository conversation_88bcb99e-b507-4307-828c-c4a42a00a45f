﻿@model EcoolWeb.ViewModels.AWA01labViewModel
@{
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
}
<link href="~/Content/colorbox/example4/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>
@if (AppMode == false)
{
    @Html.Partial("_Title_Secondary")
}
@Html.Partial("_Notice")


@using (Html.BeginForm("Query4", "AWA002", FormMethod.Post, new { id = "AWA002" }))
{








    <br />
    <div class="form-inline" role="form">
        <div class="form-group">
            <label class="control-label">異常日期</label>
        </div>
        @*<div class="form-group">
            @Html.EditorFor(m => m.whereKeyword, new { htmlAttributes = new { @class = "form-control input-sm" } })
 
        </div>*@
        <div class="form-inline" role="form">
            @Html.HiddenFor(m => m.OrdercColumn)
            @Html.HiddenFor(m => m.Page)
            @Html.Hidden("User_No", Request["User_No"])
            <div class="form-group">
                <label class="control-label">日期區間</label>
            </div>
            <div class="form-group">
                <div class="row">
                    <div class="col-xs-3">
                        @Html.EditorFor(m => m.whereLOG_TIME_S, new { htmlAttributes = new { @class = "form-control input-sm", @type = "text" } })
                    </div>
                    <div class="col-xs-1">
                        至
                    </div>
                    <div class="col-xs-3">
                        @Html.EditorFor(m => m.whereLOG_TIME_E, new { htmlAttributes = new { @class = "form-control input-sm", @type = "text" } })
                    </div>
                    <div class="col-xs-1">
                    </div>
                </div>
            </div>
        </div>

        <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1)" />
        <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />
    </div>
    <strong>
        <font style="color:red">
           說明：本功能為當學生獲得多於500點時，系統自動記錄方便各校管理者掌握。<br />
           異動多於500點:不包括定存解約，禮物取消。
        </font>
    </strong>
}

@if (AppMode == false)
{
    @*<div class="row">
            <div class="col-xs-12 text-right">
                <button type="button" class="btn btn-sm btn-sys" onclick="ToExcel()">匯出excel</button>
            </div>
        </div>*@
}
@*<img src="~/Content/img/web-bar2-revise-bk2.png" style="width:100%" class="img-responsive " alt="Responsive image" />*@
<table class="table-ecool table-92Per table-hover table-ecool-AWA003">
    @*<thead>
        <tr>
            <th align="center">
                異常日期(起)
            </th>
            <th align="center">
                異常日期(迄)
            </th>
            <th align="center">

            </th>
        </tr>
    </thead>*@
    <tbody>
        @*@foreach (var item in Model.labnormaloglist)
        {
            <tr>

                <td align="center">
                    @item.StartDate.Value.ToShortDateString()
                </td>
                <td align="center">
                    @item.EndDate.Value.ToShortDateString()

                </td>
                <td align="center">
                    @Html.ActionLink("詳細", "_PageContent", "AWA002", new { StartDate = item.StartDate, EndDate = item.EndDate }, new { @class = "btn-table-link progress" })
                </td>


            </tr>
        }*@
        @Html.Action("_PageContent", (string)ViewBag.BRE_NO)
    </tbody>
</table>
@*<div>
    @Html.Pager(Model.labnormaloglist.PageSize, Model.labnormaloglist.PageNumber, Model.labnormaloglist.TotalItemCount).Options(o => o
              .DisplayTemplate("BootstrapPagination")
             .MaxNrOfPages(5)
             .SetPreviousPageText("上頁")
             .SetNextPageText("下頁")
         )
</div>*@

@section scripts{
    <script>
        var targetFormID = '#AWA002';
        $(document).ready(function () { $(".progress").colorbox({ opacity: 0.82, width: "70%", innerHeight: "500px", scrolling: true });});
        function FunPageProc(page) {

            if ($(targetFormID).size() > 0) {
                $('#Page').val(page)
                $(targetFormID).submit();
            }
        };

        function ToExcel() {
            $(targetFormID).attr('action', '@Url.Action("PrintExcel_Query", "AWA002")').attr('target', '_blank');
            $(targetFormID).submit();
        };

        function todoClear() {

            ////重設
            $("#OrderList").find(":input,:selected").each(function (i) {

                var type = $(this).attr('type')
                var InPreadonly = $(this).attr('readonly')
                var tag = this.tagName.toLowerCase(); // normalize case

                if (InPreadonly == false || InPreadonly == undefined) {

                    if (type == 'radio' || type == 'checkbox') {
                        if ($(this).attr("title") == 'Default') {
                            this.checked = true;
                        }
                        else {
                            this.checked = false;
                        }
                    }
                    else if (tag == 'select') { //下拉式選單
                        this.selectedIndex = 0;
                    }
                    else if (type == 'text' || type == 'hidden' || type == 'password' || type == 'textarea') {
                        this.value = '';
                    }
                }
            });

            $(targetFormID).submit();
        }

        $(document).ready(function () {

            $("#whereLOG_TIME_S,#whereLOG_TIME_E").datepicker({
                dateFormat: "yy/mm/dd",
                changeMonth: true,
                changeYear: true,
                showOn: "both",
                buttonImage: "../Content/img/icon/calendar.gif",
                buttonImageOnly: true,
            });
        });
    </script>
}