﻿
@{
    ViewBag.Title = ViewBag.Panel_Title;
}
<link href="~/Content/eCoolRWDtable/eCoolRWDtable.min.css?@DateTime.Now.ToString("yyyy_MM_dd_H_mm_ss");" rel="stylesheet" />
<link href="~/Content/colorbox/example2/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>
@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@Html.ActionLink("回「新增批次給點/扣點」首頁", "SysIndex", null, new { @class = "btn btn-sm btn-sys", @role = "button" })

@using (Html.BeginForm("QuerySelectView1", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", enctype = "multipart/form-data" }))
{
    @Html.AntiForgeryToken()
    <div class="modal fade" id="myModal" role="dialog">
        <div class="modal-content">

            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">上傳進度</h4>
            </div>
        </div>
        <div class="modal-body">
            <div class="progress progress-striped active">
                <div class="progress-bar progress-bar-success" id="barr" role="progressbar" aria-valuenow="60" aria-valuemin="0" aria-valuemax="100" style="width:0%;">

                    <span class="sr-only">40% 完成</span>
                </div>
            </div>
        </div>
        <div class="modal-footer">

            <button type="button" class="btn btn-default" data-dismiss="modal">Close </button>
        </div>
    </div>
    <div id="QuerySelectPageContent1">
        @Html.Action("_QuerySelectPageContent1", (string)ViewBag.BRE_NO, new { SYS_TABLE_TYPE = ViewBag.SYS_TABLE_TYPE })
    </div>
    <div id="QuerySelectDataList1">
        @using (Html.BeginForm("Edit1", (string)ViewBag.BRE_NO, FormMethod.Post, new { SYS_TABLE_TYPE = ViewBag.SYS_TABLE_TYPE, ADDT14_STYLE = ViewBag.ADDT14_STYLE, name = "form2", enctype = "multipart/form-data" }))
        {
            @Html.Action("_QuerySelectDataList1", (string)ViewBag.BRE_NO, new { SYS_TABLE_TYPE = ViewBag.SYS_TABLE_TYPE, ADDT14_STYLE = ViewBag.ADDT14_STYLE, ShowType = ViewContext.RouteData.Values["action"] })
        }  
    
    </div>

}



@section Scripts {
    <script language="JavaScript">

        function CheckALL() {
            $('#BtnSave').attr('disabled', 'disabled').html("Loading...資料驗証中…請勿其他動作");
            $("#myModal").modal('show');

            var id = setInterval(setTime, 10);
            var percent = 10;
            var percentW = "";
            percent = 10 * id;
            percentW = percent + "%";
            console.log(percentW);
            clearInterval(id);
            $("#barr").css("width", percentW);
            setTimeout(function () {


                $("#pageSize").val('2147483647');
                FunPageProc(1);
                $("td[id='USER_NOlist']").each(function () {
                    var C01 = $(this).text().trim();
                    var C02 = "";
                    var C03 = "";
                    C02 = "tr_" + C01;
                    C03 = C01;

                    var p = document.getElementById(C02);
                    p.onclick = onBtnLink(C03, 'ADD');
                });

            }, 3000);
            setTimeout(function () {
                $("#barr").css("width", "100%");
            }, 3000);
            //$("#barr").css("width", "100%");
            //called second

            //$("td[id='USER_NOlist']").each(function () {
            //    var C01 = $(this).text().trim();
            //    var C02 = "";
            //    var C03 = "";
            //    C02 = "tr_" + C01;
            //    C03 = C01;

            //    var p = document.getElementById(C02);
            //    p.onclick = onBtnLink(C03, 'ADD');

            //});
            //$("#barr").css("width", "100%");
        }

        //分頁
        function FunPageProc(pageno) {
            $('#Search_Page').val(pageno);
            funAjax()
        }

        //排序
        function FunSort(SortName) {

            OrderByName = $('#Search_OrderByName').val();
            SyntaxName = $('#Search_SyntaxName').val();

            if (OrderByName == SortName) {

                if (SyntaxName == "Desc") {
                    $('#Search_SyntaxName').val("ASC");
                }
                else {
                    $('#Search_SyntaxName').val("Desc");
                }
            }
            else {
                $('#Search_OrderByName').val(SortName);
                $('#Search_SyntaxName').val("Desc");
            }
            funAjax()
        }


        //查詢
        function funAjax() {

            var data = {
                "SYS_TABLE_TYPE": '@Html.Raw(ViewBag.SYS_TABLE_TYPE)',
                "Search.OrderByName": $('#Search_OrderByName').val(),
                "Search.SyntaxName": $('#Search_SyntaxName').val() ,
                "Search.Page": $('#Search_Page').val() ,
                "Search.SearchContents": $('#Search_SearchContents').val(),
                "Search.USER_NO": $('#Search_USER_NO').val(),
                "Search.CLASS_NO": $('#Search_CLASS_NO').val(),
                "Search.SYEAR": $('#Search_SYEAR').val(),
                "Search.NAME": $('#Search_NAME').val(),
                "pageSize": $('#pageSize').val(),
            };

            $.ajax({
                url: '@Url.Action("_QuerySelectPageContent1", (string)ViewBag.BRE_NO)',
                data: data,
                type: 'post',
                async: false,
                cache: false,
                dataType: 'html',
                success: function (data) {
                    $('#QuerySelectPageContent1').html(data);
                }
            });
        }




    </script>
}
