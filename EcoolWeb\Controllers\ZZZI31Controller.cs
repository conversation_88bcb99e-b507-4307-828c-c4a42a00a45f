﻿
using ECOOL_APP.com.ecool.Models.DTO;
using ECOOL_APP.com.ecool.Models.entity;
using ECOOL_APP.EF;
using EcoolWeb.Models;
using System;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using MvcPaging;
using ECOOL_APP;
using EcoolWeb.CustomAttribute;
using ECOOL_APP.com.ecool.service;
using System.IO;
using System.Collections.Generic;
using com.ecool.service;
using ECOOL_APP.com.ecool.util;

namespace EcoolWeb.Controllers
{
    [SessionExpire]
    public class ZZZI31Controller : Controller
    {

        /// <summary>
        /// 此Controller 對應的程式代碼
        /// </summary>
        private static string Bre_NO = "ZZZI31";

        /// <summary>
        /// Error 訊息
        /// </summary>
        private string ErrorMsg = string.Empty;

        /// <summary>
        /// 資料庫相關處理
        /// </summary>
        private ZZZI31Service Db = new ZZZI31Service();
        private ECOOL_DEVEntities EntitiesDb = new ECOOL_DEVEntities();
        /// <summary>
        /// 會員相關資訊
        /// </summary>
        private UserProfile user;
        private string DefaultSCHOOL_NO = string.Empty;
        private string USER_KEY = string.Empty;
        private string USER_NO = string.Empty;



        #region  列表清單

        [CheckPermission] //檢查權限
        public ActionResult Index()
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "填報程式-列表";
            return View();
        }

        [CheckPermission(CheckACTION_ID = "Index", IsGetAllActionPermission = true)] //檢查權限
        public ActionResult _PageContent(ZZZI31ListViewModel Data, string STATUS, int pageSize = 30)
        {
            ViewBag.BRE_NO = Bre_NO;
            this.Shared();

            int count = int.MinValue;

            if (Data.Search == null) Data.Search = new ZZZI31SearchViewModel();
            if (STATUS != null && STATUS != string.Empty) Data.Search.STATUS = STATUS;

            var DataList = Db.GetListData(Data,user, pageSize, ref count);

            Data.QAT16List = DataList.ToPagedList(Data.Search.Page - 1, pageSize, count);


            return PartialView(Data);
        }

        #endregion



        #region  詳細內容
        [HttpPost]
        [CheckPermission(CheckACTION_ID = "Index", IsGetAllActionPermission = true)] //檢查權限
        public ActionResult Details(ZZZI31DetailsViewModel Data)
        {
            this.DetailsData(Data);
            return View(Data);
        }

        #endregion

        #region  詢問問題

        [HttpPost]
        [CheckPermission(IsGetAllActionPermission = true)] //檢查權限
        [ValidateAntiForgeryToken]
        public ActionResult QUESTIONS(ZZZI31QUESTIONSViewModel Data, HttpPostedFileBase files, string DATA_TYPE)
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "問題反應維護-詢問";
            this.Shared();


            if (DATA_TYPE != string.Empty && DATA_TYPE != null)
            {

                if (DATA_TYPE == ZZZI31Controller.DATA_TYPE.DATA_TYPE_U || DATA_TYPE == ZZZI31Controller.DATA_TYPE.DATA_TYPE_A)
                {
#if DEBUG
                    var errorsaa = ModelState
                                .Where(x => x.Value.Errors.Count > 0)
                                .Select(x => new { x.Key, x.Value.Errors })
                                .ToArray();
#endif

                    if (ModelState.IsValid == false) ErrorMsg = "錯誤\r\n";
                    if (ModelState.IsValid) //沒有錯誤
                    {
                        Data.uQAT16.CRE_DATE = DateTime.Now;
                        Data.uQAT16.CHG_DATE = DateTime.Now;
                        Data.uQAT16.SCHOOL_NO = DefaultSCHOOL_NO;
                        Data.uQAT16.STATUS = uQAT16.STATUS_Val.STATUS_1;

                        if (User != null)
                        {
                            Data.uQAT16.CHG_PERSON = user.USER_KEY;
                            Data.uQAT16.CRE_PERSON = user.USER_KEY;
                            Data.uQAT16.NAME = user.NAME;
                            Data.uQAT16.USER_NO = user.USER_NO;
                        }
                        if (files != null) Data.uQAT16.FILE_NAME = Path.GetFileName(files.FileName);

                        if (Data.uQAT16.QUESTIONS_ID != string.Empty && Data.uQAT16.QUESTIONS_ID != null && DATA_TYPE == ZZZI31Controller.DATA_TYPE.DATA_TYPE_U)
                        {
                            Db.UpDate(Data.uQAT16);
                        }
                        else if (DATA_TYPE == ZZZI31Controller.DATA_TYPE.DATA_TYPE_A)
                        {
                            Db.CreateDateQAT16(Data.uQAT16);
                        }

                        bool ans = DoLoadFile(Data.uQAT16.QUESTIONS_ID,"", files);
                    }
                }
                else if (DATA_TYPE == ZZZI31Controller.DATA_TYPE.DATA_TYPE_D)
                {
                    Db.DelAllDate(Data.uQAT16.QUESTIONS_ID);
                    this.DelAllFile(Data.uQAT16.QUESTIONS_ID,"");
                }


                ErrorMsg = ErrorMsg + Db.ErrorMsg;

                if (string.IsNullOrWhiteSpace(ErrorMsg))
                {
                    if (DATA_TYPE == ZZZI31Controller.DATA_TYPE.DATA_TYPE_D)
                    {
                        TempData["StatusMessage"] = "刪除成功";
                        return View("Index");
                    }
                    else
                    {
                        Mail("2", Data.uQAT16.QUESTIONS_ID, "", "問題維護", Data.uQAT16.SUBJECT + "<br/><br/>" + Data.uQAT16.QUESTIONS_TXT);

                        TempData["StatusMessage"] = "存檔成功";
                    }

                    return View("Index");
                }
                else
                {
                    TempData["StatusMessage"] = ErrorMsg;
                }
            }
            else
            {
                Data.uQAT16 = new uQAT16();
                ModelState.Clear();

                if (Data.Search.Q_QUESTIONS_ID != string.Empty && Data.Search.Q_QUESTIONS_ID != null)
                {
                    Data.uQAT16 = Db.GetGetDetailsData(Data.Search.Q_QUESTIONS_ID);
                }
                else
                {
                    if (user != null)
                    {
                        Data.uQAT16.SNAME = user.SNAME;

                        HRMT01 dataHRMT01 = EntitiesDb.HRMT01.Where(a => a.USER_NO == user.USER_NO && a.SCHOOL_NO == a.SCHOOL_NO).FirstOrDefault();
                        if (dataHRMT01 != null) Data.uQAT16.E_MAIL = dataHRMT01.E_MAIL;
                    }
                }
            }

            return View(Data);
        }

        #endregion

        #region  再提問
        [CheckPermission(CheckACTION_ID = "QUESTIONS", IsGetAllActionPermission = true)] //檢查權限
        [ValidateAntiForgeryToken]
        public ActionResult Add_QUESTIONS(ZZZI31ANSWERSViewModel Data, HttpPostedFileBase files, string DATA_TYPE, string ANS_TYPE)
        {

            string Action = QUESTIONS_ANSWERS(Data, files, DATA_TYPE, ANS_TYPE);

            if (Action == "Details")
            {
                ZZZI31DetailsViewModel TData = new ZZZI31DetailsViewModel();
                TData.Search = Data.Search;

                this.DetailsData(TData);
                return View("Details", TData);
            }
            else if (Action == "InternalError")
            {
                return RedirectToAction("InternalError", "Error", new { error = "異常無ANS_TYPE" });
            }

            return View("ANSWERS", Data);
        }
        #endregion

        #region  回覆
        [CheckPermission(IsGetAllActionPermission = true)] //檢查權限
        [ValidateAntiForgeryToken]
        public ActionResult ANSWERS(ZZZI31ANSWERSViewModel Data, HttpPostedFileBase files, string DATA_TYPE, string ANS_TYPE)
        {

            string Action = QUESTIONS_ANSWERS(Data, files, DATA_TYPE, ANS_TYPE);

            if (Action== "Details")
            {
                ZZZI31DetailsViewModel TData = new ZZZI31DetailsViewModel();
                TData.Search = Data.Search;

                this.DetailsData(TData);
                return View("Details", TData);
            }
            else if(Action == "InternalError")
            {
                return RedirectToAction("InternalError", "Error", new { error = "異常無ANS_TYPE" });
            }
         

            return View(Data);
        }
        #endregion


        private string QUESTIONS_ANSWERS(ZZZI31ANSWERSViewModel Data, HttpPostedFileBase files, string DATA_TYPE, string ANS_TYPE)
        {
            string ReturnVal = string.Empty;
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.ANS_TYPE = ANS_TYPE;


            if (ANS_TYPE == uQAT17.ANS_TYPE_Val.ANS_TYPE_A)
            {
                ViewBag.Panel_Title = "問題反應維護-回覆問題";
                ViewBag.Permission = "ANSWERS";
                ViewBag.Action = "ANSWERS";
            }
            else
            {
                ViewBag.Panel_Title = "問題反應維護-提出問題";
                ViewBag.Permission = "QUESTIONS";
                ViewBag.Action = "Add_QUESTIONS";
            }

            if (Data.uQAT17 == null) Data.uQAT17 = new uQAT17();
            if (Data.uQAT17.ANS_TYPE == null) Data.uQAT17.ANS_TYPE = ANS_TYPE;
            if (Data.uQAT17.ANS_TYPE == string.Empty || Data.uQAT17.ANS_TYPE == null) return "InternalError";



            this.Shared();

            if (DATA_TYPE != string.Empty && DATA_TYPE != null)
            {

                if (DATA_TYPE == ZZZI31Controller.DATA_TYPE.DATA_TYPE_U || DATA_TYPE == ZZZI31Controller.DATA_TYPE.DATA_TYPE_A)
                {
#if DEBUG
                    var errorsaa = ModelState
                                .Where(x => x.Value.Errors.Count > 0)
                                .Select(x => new { x.Key, x.Value.Errors })
                                .ToArray();
#endif

                    if (ModelState.IsValid == false) ErrorMsg = "錯誤\r\n";
                    if (ModelState.IsValid) //沒有錯誤
                    {

                        Data.uQAT17.CRE_DATE = DateTime.Now;
                        Data.uQAT17.CHG_DATE = DateTime.Now;
                        Data.uQAT17.SCHOOL_NO = DefaultSCHOOL_NO;
                        Data.uQAT17.QUESTIONS_ID = Data.Search.Q_QUESTIONS_ID;

                        Data.uQAT17.STATUS = Data.uQAT17.ANS_TYPE == uQAT17.ANS_TYPE_Val.ANS_TYPE_Q ? uQAT17.STATUS_Val.STATUS_1 : uQAT17.STATUS_Val.STATUS_2;

                        if (User != null)
                        {
                            Data.uQAT17.CHG_PERSON = user.USER_KEY;
                            Data.uQAT17.CRE_PERSON = user.USER_KEY;
                            Data.uQAT17.NAME = user.NAME;
                            Data.uQAT17.USER_NO = user.USER_NO;
                        }
                        if (files != null) Data.uQAT17.FILE_NAME = Path.GetFileName(files.FileName);

                        if (Data.uQAT17.ITEM_NO != null && DATA_TYPE == ZZZI31Controller.DATA_TYPE.DATA_TYPE_U)
                        {
                            Db.UpQAT17Date(Data.uQAT17);
                        }
                        else if (DATA_TYPE == ZZZI31Controller.DATA_TYPE.DATA_TYPE_A)
                        {
                            Db.CreateDateQAT17(Data.uQAT17);
                        }

                        bool ans = DoLoadFile(Data.uQAT17.QUESTIONS_ID, Data.uQAT17.ITEM_NO.ToString(), files);
                    }
                }
                else if (DATA_TYPE == ZZZI31Controller.DATA_TYPE.DATA_TYPE_D)
                {
                    Db.DelQAT17Date(Data.uQAT17.QUESTIONS_ID, Data.uQAT17.ITEM_NO.ToString());
                    this.DelAllFile(Data.uQAT17.QUESTIONS_ID, Data.uQAT17.ITEM_NO.ToString());
                }


                ErrorMsg = ErrorMsg + Db.ErrorMsg;

                if (string.IsNullOrWhiteSpace(ErrorMsg))
                {
                    if (DATA_TYPE == ZZZI31Controller.DATA_TYPE.DATA_TYPE_D)
                    {
                        TempData["StatusMessage"] = "刪除成功";
                    }
                    else
                    {
                        TempData["StatusMessage"] = "存檔成功";
                    }


                    Mail(Data.uQAT17.ANS_TYPE, Data.uQAT17.QUESTIONS_ID, Data.uQAT17.ITEM_NO.ToString(), "問題維護", Data.uQAT17.ANSWERS);

                    ReturnVal = "Details";
                }
                else
                {
                    TempData["StatusMessage"] = ErrorMsg;
                }
            }
            else
            {
          
                ModelState.Clear();

                if (Data.ITEM_NO != null)
                {
                    Data.uQAT17 = Db.GetDetailQAT17Data(Data.Search.Q_QUESTIONS_ID, Data.ITEM_NO.ToString());
                }
                else
                {
                    if (user != null)
                    {
                        Data.uQAT17.SNAME = user.SNAME;

                        HRMT01 dataHRMT01 = EntitiesDb.HRMT01.Where(a => a.USER_NO == user.USER_NO && a.SCHOOL_NO == a.SCHOOL_NO).FirstOrDefault();
                        if (dataHRMT01 != null) Data.uQAT17.E_MAIL = dataHRMT01.E_MAIL;
                    }
                }
            }

            return ReturnVal;
        }

        /// <summary>
        /// 發Mail
        /// </summary>
        /// <param name="ANS_TYPE"></param>
        /// <param name="QUESTIONS_ID"></param>
        /// <param name="ITEM_NO"></param>
        /// <param name="SUBJECT"></param>
        /// <param name="ANSWERS"></param>
        private void Mail(string ANS_TYPE,string QUESTIONS_ID,string ITEM_NO,string SUBJECT ,string ANSWERS)
        {

            ECOOL_DEVEntities db = new ECOOL_DEVEntities();


            List<string> Mail = new List<string>();

            //詢問
            if (ANS_TYPE=="2")
            {

                if (string.IsNullOrEmpty(ITEM_NO))
                {
                    Mail = (from c in db.WFT04
                            join o in db.HRMT01 on c.USER_KEY equals o.USER_KEY
                            where c.BRE_NO == "ZZZI31" && c.RECEIPT_ID == QUESTIONS_ID
                            && c.KEY_NO == "ZZZI31_ZZZI31_Q" && (!string.IsNullOrEmpty(o.E_MAIL))
                            select o.E_MAIL.ToString()).ToList<string>();
                }
                else
                {
                    Mail = (from c in db.WFT04
                            join o in db.HRMT01 on c.USER_KEY equals o.USER_KEY
                            where c.BRE_NO == "ZZZI31" && c.RECEIPT_ID == QUESTIONS_ID+'_'+ ITEM_NO
                            && c.KEY_NO == "ZZZI31_ZZZI31_Q" && (!string.IsNullOrEmpty(o.E_MAIL))
                            select o.E_MAIL.ToString()).ToList<string>();
                } 
            }
            //回覆
            else if (ANS_TYPE == "1")
            {
                Mail = (from c in db.WFT04
                        join o in db.HRMT01 on c.USER_KEY equals o.USER_KEY
                        where c.BRE_NO == "ZZZI31" && c.RECEIPT_ID == QUESTIONS_ID + '_' + ITEM_NO
                        && c.KEY_NO == "ZZZI31_ZZZI31_A" && (!string.IsNullOrEmpty(o.E_MAIL))
                        select o.E_MAIL.ToString()).ToList<string>();
            }


            if (Mail.Count() > 0)
            {
                MailHelper MailHelper = new MailHelper();
                MailHelper.SendMailByGmail(Mail, SUBJECT, ANSWERS);
            }

        }


        #region  已解決(結案)
        public ActionResult CLOSE(ZZZI31ListViewModel Data)
        {
            this.Shared();

            string UseYN;
            UseYN = PermissionService.GetPermission_Use_YN(Bre_NO, "ANSWERS", DefaultSCHOOL_NO, USER_NO);
            if (UseYN=="N")
            {
                UseYN = PermissionService.GetPermission_Use_YN(Bre_NO, "QUESTIONS", DefaultSCHOOL_NO, USER_NO);

                if (UseYN == "N")
                {
                    return RedirectToAction("PermissionError", "Error");
                }
            }


            Db.Update_STATUS(Data.Search.Q_QUESTIONS_ID, "CLOSE");

            ErrorMsg = ErrorMsg + Db.ErrorMsg;

            if (string.IsNullOrWhiteSpace(ErrorMsg))
            {
                TempData["StatusMessage"] = "此問題狀態已更新已解決";
            }
            else
            {
                TempData["StatusMessage"] = ErrorMsg;
            }
              

            return View("Index");
        }

        #endregion

        #region  作廢
        [CheckPermission(CheckACTION_ID = "ANSWERS", IsGetAllActionPermission = true)] //檢查權限
        public ActionResult INVALID(ZZZI31ListViewModel Data)
        {
            Db.Update_STATUS(Data.Search.Q_QUESTIONS_ID, "INVALID");

            ErrorMsg = ErrorMsg + Db.ErrorMsg;

            if (string.IsNullOrWhiteSpace(ErrorMsg))
            {
                TempData["StatusMessage"] = "此問題狀態已更新已作廢";
            }
            else
            {
                TempData["StatusMessage"] = ErrorMsg;
            }

            
            return View("Index");
        }

        #endregion

        #region  刪除檔案

        public ActionResult DelFile(ZZZI31QUESTIONSViewModel Data,string QUESTIONS_ID, string ITEM_NO, string FileName)
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "問題反應維護-刪除檔案";
            this.Shared();


            try
            {
                string tempPath = System.Web.Configuration.WebConfigurationManager.AppSettings["FilePath"];

                tempPath = tempPath+ @"\ZZI11\" + QUESTIONS_ID;

                if (ITEM_NO != "")
                {
                    tempPath = tempPath + @"\" + ITEM_NO;
                }

                tempPath = tempPath + @"\" + FileName;

                if (System.IO.File.Exists(tempPath))
                {
                    System.IO.File.Delete(tempPath);
                    Db.FILE_NAME_Empty(QUESTIONS_ID, ITEM_NO);
                }

              

            }
            catch (Exception ex)
            {
                TempData["StatusMessage"] = "刪除檔案失敗\r\n"+ ex;
            }

            if (ITEM_NO != "")
            {
                return View("ANSWERS", Data);
            }
            else
            {
                return View("QUESTIONS", Data);
            }

          
        }


        #endregion

        #region  上傳檔案處理
        /// <summary>
        /// 上傳檔案處理
        /// </summary>
        /// <param name="QUESTIONS_ID"></param>
        /// <param name="file"></param>
        /// <returns></returns>
        private bool DoLoadFile(string QUESTIONS_ID, string ITEM_NO, HttpPostedFileBase file)
        {

            if (file == null) return false;

            try
            {
                string fileName = Path.GetFileName(file.FileName);
                string tempPath = System.Web.Configuration.WebConfigurationManager.AppSettings["FilePath"] + @"\ZZI11\" + QUESTIONS_ID;

                if (ITEM_NO != string.Empty && ITEM_NO != null)
                {
                    tempPath = tempPath + @"\" + ITEM_NO;
                }

                if (Directory.Exists(tempPath) == false)
                {
                    Directory.CreateDirectory(tempPath);
                }
                else
                {
                    //刪原檔
                    string[] tempFile = Directory.GetFiles(tempPath);

                    if (tempFile.Length >= 0)
                    {
                        foreach (var item in tempFile)
                        {
                            System.IO.File.Delete(item);
                        }
                    }
                }

                string UpLoadFile = tempPath + @"\" + fileName;

                file.SaveAs(UpLoadFile);
            }
            catch (Exception ex)
            {
                ErrorMsg += file.FileName + "上傳失敗" + ex + "r\n";
                return false;
            }

            return true;
        }
        #endregion

        #region   刪除目錄
        /// <summary>
        /// 刪除 整個目錄
        /// </summary>
        /// <param name="QUESTIONS_ID"></param>
        private void DelAllFile(string QUESTIONS_ID, string ITEM_NO)
        {
            try
            {
                string tempPath = System.Web.Configuration.WebConfigurationManager.AppSettings["FilePath"] + @"\ZZI11\" + QUESTIONS_ID;

                if (ITEM_NO!=null && ITEM_NO!=string.Empty)
                {
                    tempPath = tempPath + @"\" + ITEM_NO;
                }


                if (System.IO.File.Exists(tempPath))
                {
                    System.IO.Directory.Delete(tempPath, true);
                }
            }
            catch (Exception ex)
            {

                throw ex;
            }

        }
        #endregion

        #region  下載

        [CheckPermission(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult DownLoad(string QUESTIONS_ID, string ITEM_NO, string name)
        {

            string tempPath = System.Web.Configuration.WebConfigurationManager.AppSettings["FilePath"] + "\\ZZI11\\" + QUESTIONS_ID;
            if (ITEM_NO != string.Empty && ITEM_NO != null)
            {
                tempPath = tempPath + "\\" + ITEM_NO;
            }

            return File(tempPath + "\\" + name, "text/plain", Server.HtmlEncode(name));
        }
        #endregion

        #region  Shared
        private void Shared()
        {
            user = UserProfileHelper.Get();
            DefaultSCHOOL_NO = UserProfileHelper.GetSchoolNo();



            if (user != null)
            {
                if (user.SCHOOL_NO != null)
                {
                    DefaultSCHOOL_NO = user.SCHOOL_NO;
                    USER_KEY = user.USER_KEY;
                    USER_NO = user.USER_NO;
                }
            }

            ViewBag.USER_KEY = USER_KEY;
        }
        #endregion

        private void DetailsData(ZZZI31DetailsViewModel Data, int pageSize = 30)
        {
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = "詳細內容";
            this.Shared();

            var Ans = ViewBag.BtnPermission as List<ECOOL_APP.ControllerPermissionfile>;
            TempData["boolAns"] = Ans.Where(a => a.ActionName == "ANSWERS" && a.BoolUse==true).Any() ;
            

            if (Data.uQAT16 == null) Data.uQAT16 = new uQAT16();

            if (Data.Search.Q_QUESTIONS_ID != null)
            {
                Data.uQAT16 = Db.GetGetDetailsData(Data.Search.Q_QUESTIONS_ID);
                int count = int.MinValue;
                var DataList = Db.GetListQAT17Data(Data.Search.Q_QUESTIONS_ID, Data.Search.DetailsPage, pageSize, ref count);
                Data.uQAT17List = DataList.ToPagedList(Data.Search.DetailsPage - 1, pageSize, count);

                var QAT16UnRead = Data.uQAT16.STATUS == "2";
                int UnReadCount = Data.uQAT17List.Where(a => a.STATUS == "2").Count();

                if (user!=null && (UnReadCount>0 || QAT16UnRead ==true))
                {
                    if (user.USER_KEY== Data.uQAT16.CRE_PERSON)
                    {
                        Db.Update_A_STATUS(Data.uQAT16.QUESTIONS_ID);
                    }
                }
            }
        }

        public static class DATA_TYPE
        {
            /// <summary>
            /// 新增
            /// </summary>
            public static string DATA_TYPE_A = "A";

            /// <summary>
            /// 修改
            /// </summary>
            public static string DATA_TYPE_U = "U";

            /// <summary>
            /// 刪除
            /// </summary>
            public static string DATA_TYPE_D = "D";
        }

    }
}