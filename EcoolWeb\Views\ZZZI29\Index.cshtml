﻿@model ECOOL_APP.com.ecool.Models.DTO.ZZZI29ListViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

<a role="button" href='@Url.Action("ADD", (string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys">
    新增好友
</a>

<br />
<label class="label_dt"> 訂閱好友數，人數最多5人</label>


<div class="panel panel-ACC">
    <div class="panel-heading text-center">
        @Html.BarTitle()
    </div>
    <div class="table-responsive">
        <table class="table-ecool table-92Per table-hover table-ecool-ACC">
            <thead>
                <tr>

                    <th style="text-align: center;" class="AppMode_hide">
                        年級
                    </th>
                    <th style="text-align: center">
                        班級
                    </th>
                    <th style="text-align: center" class="AppMode_hide">
                        學號
                    </th>
                    <th style="text-align: center">
                        座號
                    </th>
                    <th style="text-align: center">
                        姓名
                    </th>
                    <th style="text-align: center" class="AppMode_hide">
                        學生帳號狀態
                    </th>
                    <th style="text-align: center">

                    </th>
                </tr>
            </thead>
            <tbody>
                @foreach (var item in Model.ListQuery)
                {
                    <tr align="center">
                        <td class="AppMode_hide">
                            @HRMT01.ParserGrade(item.HRMT01ds.GRADE)
                        </td>
                        <td>
                            @Html.DisplayFor(modelItem => item.HRMT01ds.CLASS_NO)
                        </td>
                        <td class="AppMode_hide">
                            @Html.DisplayFor(modelItem => item.HRMT01ds.USER_NO)
                        </td>
                        <td>
                            @Html.DisplayFor(modelItem => item.HRMT01ds.SEAT_NO)
                        </td>
                        <td>
                            @Html.DisplayFor(modelItem => item.HRMT01ds.NAME)
                        </td>
                        <td class="AppMode_hide">
                            @UserStaus.GetDesc(item.HRMT01ds.USER_STATUS)
                        </td>
                        @*<td>
                                <a class="btn btn-xs btn-Basic" role="button" onclick="if (confirm('你確定要「刪除」這位好友?')==true) {document.location.href='@Url.Action("DEL", (string)ViewBag.BRE_NO, new { HRMT07_SCHOOL_NO = item.HRMT07_SCHOOL_NO, HRMT07_USER_NO=item.HRMT07_USER_NO, STUDENT_USER_NO = item.HRMT01ds.USER_NO})'}">刪除</a>
                            </td>*@
                        <td>
                            <a class="btn btn-xs btn-Basic" role="button" onclick="gogo ='@Url.Action("DEL", (string)ViewBag.BRE_NO, new { HRMT07_SCHOOL_NO = item.HRMT07_SCHOOL_NO, HRMT07_USER_NO=item.HRMT07_USER_NO, STUDENT_USER_NO = item.HRMT01ds.USER_NO})'; currentForm = this;$('#dialog-confirm').dialog('open');">刪除</a>
                        </td>
                    </tr>
                }
            </tbody>
        </table>
    </div>
</div>
<link href="~/Content/css/jquery-ui.min.css" rel="stylesheet" />
<script src="~/Scripts/jquery-3.6.4.min.js"></script>
@*<script src="~/Scripts/jquery-1.10.2.min.js"></script>*@
<script src="~/Scripts/jquery-ui.min.js"></script>

<div id="dialog-confirm" style="display:none;" title="確認刪除？">
    <span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span>您確定要刪除嗎？
</div>
<script language="javascript">
    var currentForm;
    var gogo;
    $("#dialog-confirm").dialog({
        resizable: false,
        autoOpen: false,
        height: 180,
        modal: true,
        buttons: {
            '確認': function () {
                document.location.href = gogo;
            },
            '取消': function () {
                $(this).dialog('close');
            }
        }
    });
    $('#checkin').submit(function () {
        currentForm = this; $('#dialog-confirm').dialog('open');
        return false;
    });

</script>