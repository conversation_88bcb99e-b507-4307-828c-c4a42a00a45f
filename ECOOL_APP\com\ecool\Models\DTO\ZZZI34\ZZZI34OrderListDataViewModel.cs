﻿
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Text;

namespace ECOOL_APP.EF
{
    public class ZZZI34OrderListDataViewModel
    {
        [DisplayName("學校")]
        public string SCHOOL_NO { get; set; }
        [DisplayName("學號")]
        public string USER_NO { get; set; }
        [DisplayName("姓名")]
        public string NAME { get; set; }
        [DisplayName("姓名")]
        public string SNAME { get; set; }
        [DisplayName("性別")]
        public string SEX { get; set; }
        [DisplayName("年級")]
        public Nullable<byte> GRADE { get; set; }
        [DisplayName("班級")]
        public string CLASS_NO { get; set; }
        [DisplayName("座號")]
        public string SEAT_NO { get; set; }
        public Nullable<byte> USER_STATUS { get; set; }
        public string USER_TYPE { get; set; }

        [DisplayName("作品數")]
        public int WorkCount { get; set; }

        [DisplayName("作品獲得點數")]
        public int SumCash { get; set; }

        [DisplayName("按讚數")]
        public int LikeCount { get; set; }


    }
}