﻿@model ZZZI09ExportResultViewViewModel

@{
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
}

<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>
<script src="~/Scripts/Highcharts-4.0.1/js/highcharts.js"></script>
<script src="~/Scripts/Highcharts-4.0.1/js/modules/exporting.js"></script>
<script src="~/Scripts/Blob.js"></script>
<script src="~/Scripts/FileSaver.js"></script>
<script src="~/Scripts/tableexport.js"></script>
<script src="~/Scripts/printThis/printThis.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/polyfills.umd.js"></script>
<style>
    /*.PageNext {
        page-break-after: always;
    }*/

    thead {
        display: table-header-group;
    }

    tfoot {
        display: table-row-group;
    }

    tr {
        page-break-inside: avoid;
    }

    .form-horizontal {
        width: 1500px;
        height: 1200px;
    }

    .Div-EZ-Health {
        height: 1300px;
    }
</style>

<div style="height:10px"></div>

<div id="DivPrintBooKPDF">
    <div class="form-inline">
        <div class="col-xs-12 text-right">
            @*<button type="button" class="btn btn-sm btn-sys" onclick="PrintBooKPDF()">列印PDF</button>*@
            <button type="button" class="btn btn-sm btn-sys" onclick="printThis()" id="printbotton">列印</button>
            <button class="btn btn-sm btn-sys" onclick='fn_save()' id="printbotton">另存新檔(Html)</button>
        </div>
    </div>

    @using (Html.BeginForm("ExportResultView", "ZZZI09", FormMethod.Post, new { id = "form1", name = "form1" }))
    {
        @Html.AntiForgeryToken()
        @Html.Hidden("HtmlCode")
    }
    <script type="text/javascript">

            var targetFormID = '#form1';
            $(function () {
                $("div[id^='secI06']").each(function () {
                    $(this).attr('style', 'display:none');
                });
                $("img[id^='secI06']").attr('style', 'display:none');
                print_html();
            })
        function print_html() {
            var random = new Date().getTime();
            var pdf = new jsPDF('p', 'pt', 'a4');   // 設置輸出比例 數值越大比列越小
            pdf.internal.scaleFactor = 2;
            var options = {
                pagesplit: true, //設置是否自動分頁
                "background": '#ffffff'   //如果導出的pdf為黑色背景，需要將導出的html模塊內容背景 設置成白色。
            };
            var printHtml = $('html').get(0);    // 頁面某一個div裏面的內容，通過id獲取div內容
            pdf.addHTML(printHtml, 15, 15, options, function () {
                pdf.save('htmltopdf_' + random + '.pdf');
                parent.closeLoading();
            });
        }
        function fn_save() {
            var blob = new Blob([$("html").html()], {
                type: "html/plain;charset=utf-8"
            });
            var strFile = "Report.html";
            saveAs(blob, strFile);
            return false;
        }

        function PrintBooKPDF()
        {
           $('#HtmlCode').val($("html").html())
           $(targetFormID).attr('action','@Html.Raw(@Url.Action("Index", "PDF"))')
           $(targetFormID).attr('target', '_blank').submit().removeAttr('target');
        }

        function printThis() {

            $("#TALLchart1_container").attr("style", "display:none");
            $("#WEIGHTchart1_container").attr("style", "display:none");
            $("#FitnesschartC_container").attr("style", "display:none");
            $("#FitnesschartSU_container").attr("style", "display:none");
            $("#FitnesschartV_container").attr("style", "display:none");
            $("#Fitnesschart_container").attr("style", "display:none");
            $(".printweight").attr("style", "");
            $(".printhight").attr("style", "");

            $("button[id ^= 'printbotton']").each(function () { $(this).attr("style", "display:none"); })
            $(".printFitnesschartC").attr("style", "");
            $(".printchartSU").attr("style", "");
            $(".printchartSL").attr("style", "");
            $(".printchartV").attr("style", "");
            window.print();
            $("button[id ^= 'printbotton']").each(function () { $(this).attr("style", ""); })
            $("#TALLchart1_container").attr("style", "");
            $("#WEIGHTchart1_container").attr("style", "");
            $("#FitnesschartC_container").attr("style", "");
            $("#FitnesschartSU_container").attr("style", "");
            $("#FitnesschartV_container").attr("style", "");
            $("#Fitnesschart_container").attr("style", "");
            $(".printweight").attr("style", "display:none");
            $(".printhight").attr("style", "display:none");
            $(".printFitnesschartC").attr("style", "display:none");
            $(".printchartSU").attr("style", "display:none");
            $(".printchartSL").attr("style", "display:none");
            $(".printchartV").attr("style", "display:none");

        }
    </script>
</div>

@*健康秘書*@
@Html.Action("ShowSportsView", new { WhereSCHOOL_NO = Model.School_No, WhereUSER_NO = Model.User_No })

@if (string.IsNullOrWhiteSpace(Model.IDNO))
{
    <div id='PrintDiv' style="width:90%; margin: 0px auto;">
        @Html.Action("_PersonalDiv", "SECI01", new { wSCHOOL_NO = Model.School_No, wUSER_NO = Model.User_No, wDATA_ANGLE_TYPE = EcoolWeb.Models.UserProfileHelper.AngleVal.OneData })
    </div>
}

@*線上*@
@Html.Action("Details2", "ADDI01", new { School_No = Model.School_No, User_No = Model.User_No, S_DATE = Model.S_DATE, E_DATE = Model.E_DATE, IDNO = Model.IDNO })
<p style="page-break-after:always"></p>
@*藝廊*@
@Html.Action("ShowADDT22View", new { School_No = Model.School_No, User_No = Model.User_No, S_DATE = Model.S_DATE, E_DATE = Model.E_DATE, IDNO = Model.IDNO })
@if (Model.ReadYN != "true") { 
@*閱讀*@
@Html.Action("ADDTALLListDetails2", "ADDT", new { School_No = Model.School_No, User_No = Model.User_No, S_DATE = Model.S_DATE, E_DATE = Model.E_DATE, IDNO = Model.IDNO })
}
@*校內*@
@Html.Action("ShowADDT14View", new { School_No = Model.School_No, User_No = Model.User_No, S_DATE = Model.S_DATE, E_DATE = Model.E_DATE, IDNO = Model.IDNO })

@*校外*@
@Html.Action("ShowADDT15View", new { School_No = Model.School_No, User_No = Model.User_No, S_DATE = Model.S_DATE, E_DATE = Model.E_DATE, IDNO = Model.IDNO })
@*閱讀偏食*@
@Html.Action("_SEI05Partial", "SECI05", new { WhereSCHOOL_NO = Model.School_No, WhereUSER_NO = Model.User_No, WhereCLASS_NO = Model.Class_NO })
@*數位存摺*@
@*@Html.Action("ShowAWAT01_LOGView", new { School_No = Model.School_No, User_No = Model.User_No, S_DATE = Model.S_DATE, E_DATE = Model.E_DATE, IDNO = Model.IDNO })*@

@*點數統計*@
@Html.Action("ShowCashPreView", new { School_No = Model.School_No, User_No = Model.User_No, S_DATE = Model.S_DATE, E_DATE = Model.E_DATE, IDNO = Model.IDNO })

@*健康*@
@Html.Action("ShowHrmt08View", new { whereSCHOOL_NO = Model.School_No, whereUSER_NO = Model.User_No, whereIDNO = Model.IDNO })

@*神秘任務*@
@Html.Action("ShowADDT25_TREFView", new { whereSCHOOL_NO = Model.School_No, whereUSER_NO = Model.User_No, S_DATE = Model.S_DATE, E_DATE = Model.E_DATE, IDNO = Model.IDNO })

@*專長護照*@
@Html.Action("ShowCERT05View", new { School_No = Model.School_No, User_No = Model.User_No, fromWhere = "Export" })

