﻿
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Text;

namespace ECOOL_APP.EF
{
   public class SECI01BorrowDataViewModel
    {
        /// <summary>
        ///學生身份證字號
        /// </summary>
        [DisplayName("學生身份證字號")]
        public string NO_READ { get; set; }

        /// <summary>
        ///借書的學年
        /// </summary>
        [DisplayName("學年")]
        public string SEYEAR { get; set; }

        /// <summary>
        ///借書的學期
        /// </summary>
        [DisplayName("學期")]
        public string SESEM { get; set; }

        /// <summary>
        ///借書的日期
        /// </summary>
        [DisplayName("借書的日期")]
        public string BORROW_DATE { get; set; }

        /// <summary>
        ///分類號
        /// </summary>
        [DisplayName("分類號")]
        public string BK_GRP { get; set; }

        /// <summary>
        ///學生姓名
        /// </summary>
        [DisplayName("學生姓名")]
        public string NAME_READ { get; set; }


        /// <summary>
        ///書名
        /// </summary>
        [DisplayName("書名")]
        public string BKNAME { get; set; }

        /// <summary>
        ///ISBN編號
        /// </summary>
        [DisplayName("ISBN編號")]
        public string ISBN { get; set; }

        /// <summary>
        ///學校
        /// </summary>
        [DisplayName("學校")]
        public string SCHOOL_NO { get; set; }
    }
}