﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.Mvc;

namespace ECOOL_APP.com.ecool.Models.entity
{
    public class uZZT01
    {
        ///Summary
        ///
        ///Summary
        [DisplayName("功能代碼")]
        public string BRE_NO { get; set; }

        ///Summary
        ///
        ///Summary
        [DisplayName("功能名稱")]
        public string BRE_NAME { get; set; }

        ///Summary
        ///
        ///Summary
        [DisplayName("控制項名稱")]
        public string CONTROLLER { get; set; }

        ///Summary
        ///
        ///Summary
        [DisplayName("動作代碼")]
        public string ACTION_ID { get; set; }

        ///Summary
        ///
        ///Summary
        [DisplayName("階層")]
        public decimal LEVEL_ID { get; set; }

        ///Summary
        ///
        ///Summary
        [DisplayName("階層等級號碼")]
        public string LEVEL_NO { get; set; }

        ///Summary
        ///
        ///Summary
        [DisplayName("網址")]
        public string LINK_ADDR { get; set; }

        ///Summary
        ///
        ///Summary
        [DisplayName("父階功能代號")]
        public string BRE_NO_PRE { get; set; }

        ///Summary
        ///
        ///Summary
        [DisplayName("同階順序")]
        public decimal ORDER_BY { get; set; }

        ///Summary
        ///
        ///Summary
        [DisplayName("程式類別(目標)")]
        public string TARGET { get; set; }

        ///Summary
        ///
        ///Summary
        [DisplayName("類別")]
        public string BRE_TYPE { get; set; }

        // (1.全部、2.依角色、3.標題)

        ///Summary
        ///
        ///Summary
        [DisplayName("說明")]
        public string FUN_DESC { get; set; }

        ///Summary
        ///
        ///Summary
        [DisplayName("群組說明")]
        public string GROUP_DESC { get; set; }

        ///Summary
        ///
        ///Summary
        [DisplayName("有效日期(起)")]
        public DateTime WORK_DATES { get; set; }

        ///Summary
        ///
        ///Summary
        [DisplayName("有效日期(迄)")]
        public DateTime WORK_DATEE { get; set; }

        [DisplayName("修改人員")]
        public string CHG_PERSON { get; set; }

        [DisplayName("修改日期")]
        public DateTime CHG_DATE { get; set; }

        [DisplayName("建檔人員")]
        public string CRE_PERSON { get; set; }

        [DisplayName("建檔日期")]
        public DateTime CRE_DATE { get; set; }

        [DisplayName("APP是否使用")]
        public string APP_USE_YN { get; set; }

        [DisplayName("啟用")]
        public bool? ENABLE { get; set; }

        public enum enumBRE_TYPE : int
        {
            全部 = 1,
            依角色 = 2,
            標題 = 3
        }

        // (1.全部、2.依角色、3.標題)
        static public string ParserBRE_TYPE(byte? BRE_TYPE)
        {
            if (BRE_TYPE.HasValue == false) return string.Empty;

            string NAME = ((enumBRE_TYPE)BRE_TYPE).ToString();

            return NAME;
        }

        public static List<SelectListItem> BRE_TYPESelectListItem(string SelectedVal)
        {
            List<SelectListItem> SelectItem = new List<SelectListItem>();

            Type enumType = typeof(enumBRE_TYPE);

            foreach (enumBRE_TYPE item in Enum.GetValues(enumType))
            {
                string name = Enum.GetName(enumType, item);

                SelectListItem NewDATA = new SelectListItem();
                NewDATA.Text = Enum.GetName(enumType, item);
                NewDATA.Value = ((int)item).ToString();

                if (string.IsNullOrWhiteSpace(SelectedVal) == false)
                {
                    if (item.ToString() == SelectedVal)
                    {
                        NewDATA.Selected = true;
                    }
                }

                SelectItem.Add(NewDATA);
            }

            return SelectItem;
        }

        public static List<SelectListItem> TargetSelectListItem(string SelectedVal)
        {
            List<SelectListItem> SelectItem = new List<SelectListItem>();

            if (!string.IsNullOrWhiteSpace(SelectedVal))
            {
                SelectedVal = SelectedVal.ToLower();
            }

            SelectItem.Add(new SelectListItem() { Text = "", Value = "", Selected = string.IsNullOrWhiteSpace(SelectedVal) });
            SelectItem.Add(new SelectListItem() { Text = "_blank", Value = "_blank", Selected = SelectedVal == @"_blank" });
            SelectItem.Add(new SelectListItem() { Text = "_self", Value = "_self", Selected = SelectedVal == @"_self" });
            SelectItem.Add(new SelectListItem() { Text = "_parent", Value = "_parent", Selected = SelectedVal == @"_parent" });
            SelectItem.Add(new SelectListItem() { Text = "_top", Value = "_top", Selected = SelectedVal == @"_top" });

            return SelectItem;
        }
    }
}