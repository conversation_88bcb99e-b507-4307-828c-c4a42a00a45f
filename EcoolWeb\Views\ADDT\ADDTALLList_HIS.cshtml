﻿@model EcoolWeb.ViewModels.ADDTListViewModel
@{
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    ViewBag.Title = "閱讀認證查詢功能";

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    string HidStyle = "";
    int DataCount = 0;
    int RowNumber = 0;
    bool IsStudent = false;
    if (user != null)
    {
        if (user.USER_TYPE == UserType.Student) { IsStudent = true; }
    }

    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }

    //if (Model.isCarousel || Model.IsPrint)
    //{
    //    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
    //    HidStyle = "display:none";
    //}

}
<script type='text/javascript' src='~/Scripts/jquery.simplemodal.js'></script>
<script src="~/Scripts/Blob.js"></script>
<script src="~/Scripts/FileSaver.js"></script>
<script src="~/Scripts/tableexport.js"></script>
<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>
<link href="~/Scripts/timepicker/jquery-ui-timepicker-addon.css" rel="stylesheet" />
<script src="~/Scripts/timepicker/jquery-ui-timepicker-addon.js"></script>
<script src="~/Scripts/timepicker/jquery-ui-timepicker-zh-TW.js"></script>
<script src="~/Scripts/timepicker/jquery.ui.datepicker-zh-TW.js"></script>
@*<script src="~/Scripts/moment.js"></script>*@
<script src="~/Scripts/moment.min.js"></script>
@section scripts{
    <script>
        var targetFormID = '#ADDT';

            window.onload = function () {

               initDatepicker();
         }
        $(document).ready(function () {
            $(".colorbox").colorbox({ iframe: true, width: "80%", height: "80%", opacity: 0.82 });
        });
        function initDatepicker() {
               var opt = {
                    showMonthAfterYear: true,
                    format: moment().format('YYYY-MM-DD'),
                    showSecond: true,
                    showButtonPanel: true,
                    showTime: true,
                    beforeShow: function () {
                        setTimeout(
                            function () {
                                $('#ui-datepicker-div').css("z-index", 15);
                                $('#ui-datepicker-div').css("top", "220px");
                            }, 100
                        );
                    },
                    onSelect: function (dateText, inst) {
                        $('#' + inst.id).attr('value', dateText);
                    }
                };
                $("#@Html.IdFor(m => m.WhereUP_DATE_START)").datetimepicker(opt);
                $("#@Html.IdFor(m => m.WhereUP_DATE_END)").datetimepicker(opt);
        }
        function FunPageProc(page) {
            if ($(targetFormID).size() > 0) {
                $('#Page').val(page)
                $(targetFormID).submit();
            }
        };

        function doSort(SortCol) {
            $("#OrdercColumn").val(SortCol);
            FunPageProc(1)
        }

        function doSearch(ColName, whereValue) {
            $("#" + ColName).val(whereValue);
            FunPageProc(1)
        }


           function todoClear() {
                $("#doClear").val(true);
                $("#whereKeyword").val('');
                $("#OrdercColumn").val('');
                $("#whereCLASS_NO").val('');
                $("#whereGrade").val('');
                $("#WhereUP_DATE_START").val('');
                $("#WhereUP_DATE_END").val('');
               $("#whereSeat_NO").val('');
                @*$("#@Html.IdFor(m=>m.WhereIsMonthTop)").val(false);*@
                FunPageProc(1)
            }
    </script>


}
@using (Html.BeginForm("ADDTALLList_HIS", "ADDT", FormMethod.Post, new { id = "ADDT", name = "ADDT" }))
{
    <div>
        @*說明視窗*@
  
        <div id='container' style="@HidStyle">
            <a href='@Url.Action("ADDTALLList_HIS","ADDT", new { ADDTList = "ADDTList" })' role="button" class="btn btn-sm btn-sys  active">
                閱讀單查詢功能
            </a>
            <a href='@Url.Action("BorrowALLList_HIS","ADDT", new { ADDTList = "BorrowList" })' role="button" class="btn btn-sm btn-sys ">
                閱讀校楷模查詢
            </a>

            <a href='@Url.Action("AWA002_HIS","AWA002", new { ADDTList = "AWA002_HIS" })' role="button" class="btn btn-sm btn-sys  ">
                學生獲得點數查詢
            </a>
            <a href='@Url.Action("AWA002_HISTeacher","AWA002", new { ADDTList = "AWA002_HIS" })' role="button" class="btn btn-sm btn-sys  ">
                老師每個月給點查詢
            </a>
        </div>

        @Html.HiddenFor(m => m.OrdercColumn)
        @Html.HiddenFor(m => m.Page)
        @*@Html.HiddenFor(m => m.WhereIsMonthTop)
        @Html.HiddenFor(m => m.IsPrint)
        @Html.HiddenFor(m => m.IsToExcel)*@

        <div class="form-inline" style="@HidStyle" id="Q_Div">
            <div class="form-inline" role="form">
                <div class="form-group">
                    <label class="control-label">學號/姓名</label>
                </div>
                <div class="form-group">
                    @Html.EditorFor(m => m.whereKeyword, new { htmlAttributes = new { @class = "form-control input-sm" } })
                </div>
                <div class="form-group">
                    <label class="control-label">年級</label>
                </div>
                <div class="form-group">
                    @Html.DropDownListFor(m => m.whereGrade, (IEnumerable<SelectListItem>)ViewBag.GradeItem, new { @class = "form-control input-sm", onchange = "FunPageProc(1)" })
                </div>
                <div class="form-group">
                    <label class="control-label">班級</label>
                </div>
                <div class="form-group">
                    @Html.DropDownListFor(m => m.whereCLASS_NO, (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control input-sm", onchange = "FunPageProc(1)" })
                </div>
                <div class="form-group">
                    <label class="control-label">座號</label>
                </div>
                <div class="form-group">

                    @Html.EditorFor(m => m.whereSeat_NO, new { htmlAttributes = new { @class = "form-control input-sm", @style = "width:70px" } })
                </div>
                <br />
                <div id="Sdate">
                    <div class="form-group">
                        <label class="control-label">搜尋日期(起)</label>
                    </div>

                    <div class="form-group">
                        @Html.EditorFor(m => m.WhereUP_DATE_START, new { htmlAttributes = new { id = "WhereUP_DATE_START", @class = "form-control input-sm", autocomplete = "off" } })
                    </div>
                    <div class="form-group">
                        <label class="control-label">搜尋日期(迄)</label>
                    </div>
                    <div class="form-group">
                        @Html.EditorFor(m => m.WhereUP_DATE_END, new { htmlAttributes = new { id = "WhereUP_DATE_END", @class = "form-control input-sm", autocomplete = "off" } })
                    </div>
                </div>
                <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1)" />
                <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" /><br />
                <label><font color="FireBrick">這段區間，學校一共通過</font></label>

                <font color="FireBrick">
                    @(Model.ADDT0809List?.FirstOrDefault().ShareCountSUM ?? 0)
                </font>
                <label><font color="FireBrick">次閱讀認證。  </font></label>
            </div>

        </div>
    </div>
    <div class="table-responsive">
        <div class="text-center" id="tbData">
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th>編號</th>
                        <th style="text-align: center;cursor:pointer;" onclick="doSort('CLASS_NO');">
                            班級
                            <img id="CLASS_NO" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </th>
                        <th class="Carousel_hide" style="text-align: center;cursor:pointer;" onclick="doSort('SEAT_NO');">
                            座號
                            <img id="SEAT_NO" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </th>
                        <th style="text-align: center;cursor:pointer;" onclick="doSort('USER_NO');">
                            學號
                            <img id="USER_NO" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </th>
                        <th style="text-align: center;cursor:pointer;" onclick="doSort('SNAME');">
                            姓名
                            <img id="SNAME" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </th>
                        @*@if (Model.WhereIsMonthTop == true)
                    {
                        <th style="text-align: center;cursor:pointer;" onclick="doSort('BOOK_QTY');">
                            本月閱讀冊數
                            <img id="BOOK_QTY" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                        </th>
                    }*@

                        <th style="text-align: center;cursor:pointer;" onclick="doSort('ShareCount');">
                            閱讀認證冊數
                            <img id="ShareCount" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px;" />
                        </th>

                        <th style="text-align: center;cursor:pointer; vertical-align: middle;" onclick="doSort('BOOK_QTY');">
                            總閱讀冊數
                            <img id="BOOK_QTY" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px;" />
                        </th>


                    </tr>
                </thead>
                <tbody>
                    @if (Model.ADDT0809List == null)
                    {

                    }
                    else
                    {
                        foreach (var item in Model.ADDT0809List)
                        {
                            DataCount++;

                            RowNumber = Model.ADDT0809List.PageSize * (Model.ADDT0809List.PageNumber - 1) + DataCount;
                            if (Model.isCarousel && DataCount > 5) { break; }
                            <tr>
                                <td>@RowNumber</td>
                                <td class="bigger" style="text-align: center;cursor:pointer;" onclick="doSearch('whereCLASS_NO','@item.CLASS_NO');">
                                    @Html.DisplayFor(modelItem => item.CLASS_NO)
                                </td>
                                <td class="Carousel_hide">
                                    @Html.DisplayFor(modelItem => item.SEAT_NO)
                                </td>
                                <td class="Carousel_hide">
                                    @Html.DisplayFor(modelItem => item.USER_NO)
                                </td>
                                <td class="bigger">
                                    @Html.DisplayFor(modelItem => item.SNAME)
                                </td>
                                <td class="bigger">
                                    <a href="@Url.Action("ADDTALLList","ADDT", new { WhereUP_DATE_START =Model.WhereUP_DATE_START , WhereUP_DATE_END = Model.WhereUP_DATE_END,whereUserNo= Html.DisplayFor(modelItem => item.USER_NO)})" class=" colorbox ">       @Html.DisplayFor(modelItem => item.ShareCount)</a>

                                </td>

                                <td class="bigger">
                                    <a href="@Url.Action("ADDTALLList","ADDT", new { whereUserNo= Html.DisplayFor(modelItem => item.USER_NO),whereAPPLY_STATUS="2"})" class=" colorbox ">
                                        @Html.DisplayFor(modelItem => item.BOOK_QTY)
                                    </a>
                                </td>



                            </tr>
                        }
                    }




                </tbody>
            </table>
            @if (Model.ADDT0809List != null)
            {
                <div>
                    @Html.Pager(Model.ADDT0809List.PageSize, Model.ADDT0809List.PageNumber, Model.ADDT0809List.TotalItemCount).Options(o => o
                            .DisplayTemplate("BootstrapPagination")
                           .MaxNrOfPages(5)
                           .SetPreviousPageText("上頁")
                           .SetNextPageText("下頁")
                       )
                </div>}
            else
            { <div>
    查無資料，請重新查詢
</div>

            }
        </div>
    </div>
}