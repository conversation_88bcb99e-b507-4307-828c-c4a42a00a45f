﻿@model ECOOL_APP.EF.AWAT06

@{
    ViewBag.Title = "角色娃娃-新增角色娃娃";

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    if (string.IsNullOrWhiteSpace(UUID) == false)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")


@using (Html.BeginForm("Create", "AWAI02", FormMethod.Post, new { name = "AWAI02Form", enctype = "multipart/form-data" })) 
{
    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })

    <img src="~/Content/img/web-bar2-revise-18.png" style="width:100%" class="img-responsive App_hide" alt="Responsive image" />
    <div class="Div-EZ-AWAI02">
        <div class="form-horizontal">


            <div class="form-group">
                @Html.Label("名稱", htmlAttributes: new { @class = "control-label col-md-2 col-sm-3" })
                <div class="col-md-9  col-sm-6">
                    @Html.EditorFor(model => model.PLAYER_NAME, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.PLAYER_NAME, "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="form-group">
                @Html.Label("類型", htmlAttributes: new { @class = "control-label col-md-2 col-sm-3" })
                <div class="col-md-9  col-sm-6">
                    @Html.EditorFor(model => model.PLAYER_TYPE, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.PLAYER_TYPE, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                @Html.Label("酷幣點數", htmlAttributes: new { @class = "control-label col-md-2 col-sm-3" })
                <div class="col-md-9  col-sm-6">
                    @Html.EditorFor(model => model.COST_CASH, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.COST_CASH, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                @Html.Label("限制性別", htmlAttributes: new { @class = "control-label col-md-2 col-sm-3" })
                <div class="col-md-10  col-sm-9" style="padding-top:7px">
                    @Html.RadioButtonFor(model => model.PLAYER_SEX, 0)
                    @Html.Label("不限")
                    &nbsp
                    @Html.RadioButtonFor(model => model.PLAYER_SEX, 1)
                    @Html.Label("男生")
                    &nbsp
                    @Html.RadioButtonFor(model => model.PLAYER_SEX, 2)
                    @Html.Label("女生")
                    &nbsp
                </div>
            </div>

            <div class="form-group">
                <span class="control-label col-md-2 col-sm-3">上傳圖檔</span>
                <div class="col-md-10 col-sm-9"> 
                    <input type="file" id="Imgfile" name="Imgfile" class="form-control" style="height:45px"/>
                </div>
            </div>


            <div class="form-group">
                <div class="col-md-offset-3 col-md-3">
                    <button value="Create" class="btn btn-default">
                        確定送出
                    </button>
                </div>
                <div class="col-md-offset-1 col-md-3">

                    <a href='@Url.Action("Index", "AWAI02")' role="button" class="btn btn-default">
                        放棄編輯
                        </a>
                </div>
            </div>
        </div>
    </div>
  
} 