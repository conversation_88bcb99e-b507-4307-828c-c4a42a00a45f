/*
 Highcharts JS v6.0.6 (2018-02-05)
 Highstock as a plugin for Highcharts

 (c) 2017 Torstein Honsi

 License: www.highcharts.com/license
*/
(function(G){"object"===typeof module&&module.exports?module.exports=G:G(Highcharts)})(function(G){(function(a){var C=a.defined,x=a.each,z=a.extend,u=a.merge,B=a.pick,t=a.timeUnits,D=a.win;a.Time=function(a){this.update(a,!1)};a.Time.prototype={defaultOptions:{},update:function(r){var n=B(r&&r.useUTC,!0),t=this;this.options=r=u(!0,this.options||{},r);this.Date=r.Date||D.Date;this.timezoneOffset=(this.useUTC=n)&&r.timezoneOffset;this.getTimezoneOffset=this.timezoneOffsetFunction();(this.variableTimezone=
!(n&&!r.getTimezoneOffset&&!r.timezone))||this.timezoneOffset?(this.get=function(a,b){var f=b.getTime(),d=f-t.getTimezoneOffset(b);b.setTime(d);a=b["getUTC"+a]();b.setTime(f);return a},this.set=function(v,b,f){var d;if(-1!==a.inArray(v,["Milliseconds","Seconds","Minutes"]))b["set"+v](f);else d=t.getTimezoneOffset(b),d=b.getTime()-d,b.setTime(d),b["setUTC"+v](f),v=t.getTimezoneOffset(b),d=b.getTime()+v,b.setTime(d)}):n?(this.get=function(a,b){return b["getUTC"+a]()},this.set=function(a,b,f){return b["setUTC"+
a](f)}):(this.get=function(a,b){return b["get"+a]()},this.set=function(a,b,f){return b["set"+a](f)})},makeTime:function(r,n,t,v,b,f){var d,k,g;this.useUTC?(d=this.Date.UTC.apply(0,arguments),k=this.getTimezoneOffset(d),d+=k,g=this.getTimezoneOffset(d),k!==g?d+=g-k:k-36E5!==this.getTimezoneOffset(d-36E5)||a.isSafari||(d-=36E5)):d=(new this.Date(r,n,B(t,1),B(v,0),B(b,0),B(f,0))).getTime();return d},timezoneOffsetFunction:function(){var r=this,n=this.options,t=D.moment;if(!this.useUTC)return function(a){return 6E4*
(new Date(a)).getTimezoneOffset()};if(n.timezone){if(t)return function(a){return 6E4*-t.tz(a,n.timezone).utcOffset()};a.error(25)}return this.useUTC&&n.getTimezoneOffset?function(a){return 6E4*n.getTimezoneOffset(a)}:function(){return 6E4*(r.timezoneOffset||0)}},dateFormat:function(r,n,t){if(!a.defined(n)||isNaN(n))return a.defaultOptions.lang.invalidDate||"";r=a.pick(r,"%Y-%m-%d %H:%M:%S");var v=this,b=new this.Date(n),f=this.get("Hours",b),d=this.get("Day",b),k=this.get("Date",b),g=this.get("Month",
b),m=this.get("FullYear",b),l=a.defaultOptions.lang,q=l.weekdays,c=l.shortWeekdays,e=a.pad,b=a.extend({a:c?c[d]:q[d].substr(0,3),A:q[d],d:e(k),e:e(k,2," "),w:d,b:l.shortMonths[g],B:l.months[g],m:e(g+1),y:m.toString().substr(2,2),Y:m,H:e(f),k:f,I:e(f%12||12),l:f%12||12,M:e(v.get("Minutes",b)),p:12>f?"AM":"PM",P:12>f?"am":"pm",S:e(b.getSeconds()),L:e(Math.round(n%1E3),3)},a.dateFormats);a.objectEach(b,function(h,c){for(;-1!==r.indexOf("%"+c);)r=r.replace("%"+c,"function"===typeof h?h.call(v,n):h)});
return t?r.substr(0,1).toUpperCase()+r.substr(1):r},getTimeTicks:function(a,n,u,v){var b=this,f=[],d={},k,g=new b.Date(n),m=a.unitRange,l=a.count||1,q;if(C(n)){b.set("Milliseconds",g,m>=t.second?0:l*Math.floor(b.get("Milliseconds",g)/l));m>=t.second&&b.set("Seconds",g,m>=t.minute?0:l*Math.floor(b.get("Seconds",g)/l));m>=t.minute&&b.set("Minutes",g,m>=t.hour?0:l*Math.floor(b.get("Minutes",g)/l));m>=t.hour&&b.set("Hours",g,m>=t.day?0:l*Math.floor(b.get("Hours",g)/l));m>=t.day&&b.set("Date",g,m>=t.month?
1:l*Math.floor(b.get("Date",g)/l));m>=t.month&&(b.set("Month",g,m>=t.year?0:l*Math.floor(b.get("Month",g)/l)),k=b.get("FullYear",g));m>=t.year&&b.set("FullYear",g,k-k%l);m===t.week&&b.set("Date",g,b.get("Date",g)-b.get("Day",g)+B(v,1));k=b.get("FullYear",g);v=b.get("Month",g);var c=b.get("Date",g),e=b.get("Hours",g);n=g.getTime();b.variableTimezone&&(q=u-n>4*t.month||b.getTimezoneOffset(n)!==b.getTimezoneOffset(u));g=g.getTime();for(n=1;g<u;)f.push(g),g=m===t.year?b.makeTime(k+n*l,0):m===t.month?
b.makeTime(k,v+n*l):!q||m!==t.day&&m!==t.week?q&&m===t.hour&&1<l?b.makeTime(k,v,c,e+n*l):g+m*l:b.makeTime(k,v,c+n*l*(m===t.day?1:7)),n++;f.push(g);m<=t.hour&&1E4>f.length&&x(f,function(h){0===h%18E5&&"000000000"===b.dateFormat("%H%M%S%L",h)&&(d[h]="day")})}f.info=z(a,{higherRanks:d,totalRange:m*l});return f}}})(G);(function(a){var C=a.addEvent,x=a.Axis,z=a.Chart,u=a.css,B=a.defined,t=a.each,D=a.extend,r=a.noop,n=a.pick,E=a.timeUnits,v=a.wrap;v(a.Series.prototype,"init",function(b){var a;b.apply(this,
Array.prototype.slice.call(arguments,1));(a=this.xAxis)&&a.options.ordinal&&C(this,"updatedData",function(){delete a.ordinalIndex})});v(x.prototype,"getTimeTicks",function(a,f,d,k,g,m,l,q){var c=0,e,h,A={},b,F,L,p=[],w=-Number.MAX_VALUE,y=this.options.tickPixelInterval,H=this.chart.time;if(!this.options.ordinal&&!this.options.breaks||!m||3>m.length||void 0===d)return a.call(this,f,d,k,g);F=m.length;for(e=0;e<F;e++){L=e&&m[e-1]>k;m[e]<d&&(c=e);if(e===F-1||m[e+1]-m[e]>5*l||L){if(m[e]>w){for(h=a.call(this,
f,m[c],m[e],g);h.length&&h[0]<=w;)h.shift();h.length&&(w=h[h.length-1]);p=p.concat(h)}c=e+1}if(L)break}a=h.info;if(q&&a.unitRange<=E.hour){e=p.length-1;for(c=1;c<e;c++)H.dateFormat("%d",p[c])!==H.dateFormat("%d",p[c-1])&&(A[p[c]]="day",b=!0);b&&(A[p[0]]="day");a.higherRanks=A}p.info=a;if(q&&B(y)){q=H=p.length;e=[];var J;for(b=[];q--;)c=this.translate(p[q]),J&&(b[q]=J-c),e[q]=J=c;b.sort();b=b[Math.floor(b.length/2)];b<.6*y&&(b=null);q=p[H-1]>k?H-1:H;for(J=void 0;q--;)c=e[q],k=Math.abs(J-c),J&&k<.8*
y&&(null===b||k<.8*b)?(A[p[q]]&&!A[p[q+1]]?(k=q+1,J=c):k=q,p.splice(k,1)):J=c}return p});D(x.prototype,{beforeSetTickPositions:function(){var a,f=[],d=!1,k,g=this.getExtremes(),m=g.min,l=g.max,q,c=this.isXAxis&&!!this.options.breaks,g=this.options.ordinal,e=Number.MAX_VALUE,h=this.chart.options.chart.ignoreHiddenSeries;k="highcharts-navigator-xaxis"===this.options.className;!this.options.overscroll||this.max!==this.dataMax||this.chart.mouseIsDown&&!k||this.eventArgs&&(!this.eventArgs||"navigator"===
this.eventArgs.trigger)||(this.max+=this.options.overscroll,!k&&B(this.userMin)&&(this.min+=this.options.overscroll));if(g||c){t(this.series,function(l,b){if(!(h&&!1===l.visible||!1===l.takeOrdinalPosition&&!c)&&(f=f.concat(l.processedXData),a=f.length,f.sort(function(h,c){return h-c}),e=Math.min(e,n(l.closestPointRange,e)),a))for(b=a-1;b--;)f[b]===f[b+1]&&f.splice(b,1)});a=f.length;if(2<a){k=f[1]-f[0];for(q=a-1;q--&&!d;)f[q+1]-f[q]!==k&&(d=!0);!this.options.keepOrdinalPadding&&(f[0]-m>k||l-f[f.length-
1]>k)&&(d=!0)}else this.options.overscroll&&(2===a?e=f[1]-f[0]:1===a?(e=this.options.overscroll,f=[f[0],f[0]+e]):e=this.overscrollPointsRange);d?(this.options.overscroll&&(this.overscrollPointsRange=e,f=f.concat(this.getOverscrollPositions())),this.ordinalPositions=f,k=this.ordinal2lin(Math.max(m,f[0]),!0),q=Math.max(this.ordinal2lin(Math.min(l,f[f.length-1]),!0),1),this.ordinalSlope=l=(l-m)/(q-k),this.ordinalOffset=m-k*l):(this.overscrollPointsRange=n(this.closestPointRange,this.overscrollPointsRange),
this.ordinalPositions=this.ordinalSlope=this.ordinalOffset=void 0)}this.isOrdinal=g&&d;this.groupIntervalFactor=null},val2lin:function(a,f){var b=this.ordinalPositions;if(b){var k=b.length,g,m;for(g=k;g--;)if(b[g]===a){m=g;break}for(g=k-1;g--;)if(a>b[g]||0===g){a=(a-b[g])/(b[g+1]-b[g]);m=g+a;break}f=f?m:this.ordinalSlope*(m||0)+this.ordinalOffset}else f=a;return f},lin2val:function(a,f){var b=this.ordinalPositions;if(b){var k=this.ordinalSlope,g=this.ordinalOffset,m=b.length-1,l;if(f)0>a?a=b[0]:a>
m?a=b[m]:(m=Math.floor(a),l=a-m);else for(;m--;)if(f=k*m+g,a>=f){k=k*(m+1)+g;l=(a-f)/(k-f);break}return void 0!==l&&void 0!==b[m]?b[m]+(l?l*(b[m+1]-b[m]):0):a}return a},getExtendedPositions:function(){var a=this,f=a.chart,d=a.series[0].currentDataGrouping,k=a.ordinalIndex,g=d?d.count+d.unitName:"raw",m=a.options.overscroll,l=a.getExtremes(),q,c;k||(k=a.ordinalIndex={});k[g]||(q={series:[],chart:f,getExtremes:function(){return{min:l.dataMin,max:l.dataMax+m}},options:{ordinal:!0},val2lin:x.prototype.val2lin,
ordinal2lin:x.prototype.ordinal2lin},t(a.series,function(e){c={xAxis:q,xData:e.xData.slice(),chart:f,destroyGroupedData:r};c.xData=c.xData.concat(a.getOverscrollPositions());c.options={dataGrouping:d?{enabled:!0,forced:!0,approximation:"open",units:[[d.unitName,[d.count]]]}:{enabled:!1}};e.processData.apply(c);q.series.push(c)}),a.beforeSetTickPositions.apply(q),k[g]=q.ordinalPositions);return k[g]},getOverscrollPositions:function(){var b=this.options.overscroll,f=this.overscrollPointsRange,d=[],
k=this.dataMax;if(a.defined(f))for(d.push(k);k<=this.dataMax+b;)k+=f,d.push(k);return d},getGroupIntervalFactor:function(a,f,d){var b;d=d.processedXData;var g=d.length,m=[];b=this.groupIntervalFactor;if(!b){for(b=0;b<g-1;b++)m[b]=d[b+1]-d[b];m.sort(function(a,b){return a-b});m=m[Math.floor(g/2)];a=Math.max(a,d[0]);f=Math.min(f,d[g-1]);this.groupIntervalFactor=b=g*m/(f-a)}return b},postProcessTickInterval:function(a){var b=this.ordinalSlope;return b?this.options.breaks?this.closestPointRange||a:a/
(b/this.closestPointRange):a}});x.prototype.ordinal2lin=x.prototype.val2lin;v(z.prototype,"pan",function(a,f){var b=this.xAxis[0],k=b.options.overscroll,g=f.chartX,m=!1;if(b.options.ordinal&&b.series.length){var l=this.mouseDownX,q=b.getExtremes(),c=q.dataMax,e=q.min,h=q.max,A=this.hoverPoints,I=b.closestPointRange||b.overscrollPointsRange,l=(l-g)/(b.translationSlope*(b.ordinalSlope||I)),F={ordinalPositions:b.getExtendedPositions()},I=b.lin2val,L=b.val2lin,p;F.ordinalPositions?1<Math.abs(l)&&(A&&
t(A,function(p){p.setState()}),0>l?(A=F,p=b.ordinalPositions?b:F):(A=b.ordinalPositions?b:F,p=F),F=p.ordinalPositions,c>F[F.length-1]&&F.push(c),this.fixedRange=h-e,l=b.toFixedRange(null,null,I.apply(A,[L.apply(A,[e,!0])+l,!0]),I.apply(p,[L.apply(p,[h,!0])+l,!0])),l.min>=Math.min(q.dataMin,e)&&l.max<=Math.max(c,h)+k&&b.setExtremes(l.min,l.max,!0,!1,{trigger:"pan"}),this.mouseDownX=g,u(this.container,{cursor:"move"})):m=!0}else m=!0;m&&(k&&(b.max=b.dataMax+k),a.apply(this,Array.prototype.slice.call(arguments,
1)))})})(G);(function(a){function C(){return Array.prototype.slice.call(arguments,1)}function x(a){a.apply(this);this.drawBreaks(this.xAxis,["x"]);this.drawBreaks(this.yAxis,z(this.pointArrayMap,["y"]))}var z=a.pick,u=a.wrap,B=a.each,t=a.extend,D=a.isArray,r=a.fireEvent,n=a.Axis,E=a.Series;t(n.prototype,{isInBreak:function(a,b){var f=a.repeat||Infinity,d=a.from,k=a.to-a.from;b=b>=d?(b-d)%f:f-(d-b)%f;return a.inclusive?b<=k:b<k&&0!==b},isInAnyBreak:function(a,b){var f=this.options.breaks,d=f&&f.length,
k,g,m;if(d){for(;d--;)this.isInBreak(f[d],a)&&(k=!0,g||(g=z(f[d].showPoints,this.isXAxis?!1:!0)));m=k&&b?k&&!g:k}return m}});u(n.prototype,"setTickPositions",function(a){a.apply(this,Array.prototype.slice.call(arguments,1));if(this.options.breaks){var b=this.tickPositions,f=this.tickPositions.info,d=[],k;for(k=0;k<b.length;k++)this.isInAnyBreak(b[k])||d.push(b[k]);this.tickPositions=d;this.tickPositions.info=f}});u(n.prototype,"init",function(a,b,f){var d=this;f.breaks&&f.breaks.length&&(f.ordinal=
!1);a.call(this,b,f);a=this.options.breaks;d.isBroken=D(a)&&!!a.length;d.isBroken&&(d.val2lin=function(a){var b=a,f,l;for(l=0;l<d.breakArray.length;l++)if(f=d.breakArray[l],f.to<=a)b-=f.len;else if(f.from>=a)break;else if(d.isInBreak(f,a)){b-=a-f.from;break}return b},d.lin2val=function(a){var b,f;for(f=0;f<d.breakArray.length&&!(b=d.breakArray[f],b.from>=a);f++)b.to<a?a+=b.len:d.isInBreak(b,a)&&(a+=b.len);return a},d.setExtremes=function(a,b,f,l,q){for(;this.isInAnyBreak(a);)a-=this.closestPointRange;
for(;this.isInAnyBreak(b);)b-=this.closestPointRange;n.prototype.setExtremes.call(this,a,b,f,l,q)},d.setAxisTranslation=function(a){n.prototype.setAxisTranslation.call(this,a);a=d.options.breaks;var b=[],f=[],l=0,q,c,e=d.userMin||d.min,h=d.userMax||d.max,A=z(d.pointRangePadding,0),k,F;B(a,function(a){c=a.repeat||Infinity;d.isInBreak(a,e)&&(e+=a.to%c-e%c);d.isInBreak(a,h)&&(h-=h%c-a.from%c)});B(a,function(a){k=a.from;for(c=a.repeat||Infinity;k-c>e;)k-=c;for(;k<e;)k+=c;for(F=k;F<h;F+=c)b.push({value:F,
move:"in"}),b.push({value:F+(a.to-a.from),move:"out",size:a.breakSize})});b.sort(function(a,p){return a.value===p.value?("in"===a.move?0:1)-("in"===p.move?0:1):a.value-p.value});q=0;k=e;B(b,function(a){q+="in"===a.move?1:-1;1===q&&"in"===a.move&&(k=a.value);0===q&&(f.push({from:k,to:a.value,len:a.value-k-(a.size||0)}),l+=a.value-k-(a.size||0))});d.breakArray=f;d.unitLength=h-e-l+A;r(d,"afterBreaks");d.options.staticScale?d.transA=d.options.staticScale:d.unitLength&&(d.transA*=(h-d.min+A)/d.unitLength);
A&&(d.minPixelPadding=d.transA*d.minPointOffset);d.min=e;d.max=h})});u(E.prototype,"generatePoints",function(a){a.apply(this,C(arguments));var b=this.xAxis,f=this.yAxis,d=this.points,k,g=d.length,m=this.options.connectNulls,l;if(b&&f&&(b.options.breaks||f.options.breaks))for(;g--;)k=d[g],l=null===k.y&&!1===m,l||!b.isInAnyBreak(k.x,!0)&&!f.isInAnyBreak(k.y,!0)||(d.splice(g,1),this.data[g]&&this.data[g].destroyElements())});a.Series.prototype.drawBreaks=function(a,b){var f=this,d=f.points,k,g,m,l;a&&
B(b,function(b){k=a.breakArray||[];g=a.isXAxis?a.min:z(f.options.threshold,a.min);B(d,function(c){l=z(c["stack"+b.toUpperCase()],c[b]);B(k,function(b){m=!1;if(g<b.from&&l>b.to||g>b.from&&l<b.from)m="pointBreak";else if(g<b.from&&l>b.from&&l<b.to||g>b.from&&l>b.to&&l<b.from)m="pointInBreak";m&&r(a,m,{point:c,brk:b})})})})};a.Series.prototype.gappedPath=function(){var n=this.currentDataGrouping,b=n&&n.totalRange,n=this.options.gapSize,f=this.points.slice(),d=f.length-1,k=this.yAxis;if(n&&0<d)for("value"!==
this.options.gapUnit&&(n*=this.closestPointRange),b&&b>n&&(n=b);d--;)f[d+1].x-f[d].x>n&&(b=(f[d].x+f[d+1].x)/2,f.splice(d+1,0,{isNull:!0,x:b}),this.options.stacking&&(b=k.stacks[this.stackKey][b]=new a.StackItem(k,k.options.stackLabels,!1,b,this.stack),b.total=0));return this.getGraphPath(f)};u(a.seriesTypes.column.prototype,"drawPoints",x);u(a.Series.prototype,"drawPoints",x)})(G);(function(a){var C=a.arrayMax,x=a.arrayMin,z=a.Axis,u=a.defaultPlotOptions,B=a.defined,t=a.each,D=a.extend,r=a.format,
n=a.isNumber,E=a.merge,v=a.pick,b=a.Point,f=a.Tooltip,d=a.wrap,k=a.Series.prototype,g=k.processData,m=k.generatePoints,l={approximation:"average",groupPixelWidth:2,dateTimeLabelFormats:{millisecond:["%A, %b %e, %H:%M:%S.%L","%A, %b %e, %H:%M:%S.%L","-%H:%M:%S.%L"],second:["%A, %b %e, %H:%M:%S","%A, %b %e, %H:%M:%S","-%H:%M:%S"],minute:["%A, %b %e, %H:%M","%A, %b %e, %H:%M","-%H:%M"],hour:["%A, %b %e, %H:%M","%A, %b %e, %H:%M","-%H:%M"],day:["%A, %b %e, %Y","%A, %b %e","-%A, %b %e, %Y"],week:["Week from %A, %b %e, %Y",
"%A, %b %e","-%A, %b %e, %Y"],month:["%B %Y","%B","-%B %Y"],year:["%Y","%Y","-%Y"]}},q={line:{},spline:{},area:{},areaspline:{},column:{approximation:"sum",groupPixelWidth:10},arearange:{approximation:"range"},areasplinerange:{approximation:"range"},columnrange:{approximation:"range",groupPixelWidth:10},candlestick:{approximation:"ohlc",groupPixelWidth:10},ohlc:{approximation:"ohlc",groupPixelWidth:5}},c=a.defaultDataGroupingUnits=[["millisecond",[1,2,5,10,20,25,50,100,200,500]],["second",[1,2,5,
10,15,30]],["minute",[1,2,5,10,15,30]],["hour",[1,2,3,4,6,8,12]],["day",[1]],["week",[1]],["month",[1,3,6]],["year",null]],e=a.approximations={sum:function(a){var h=a.length,b;if(!h&&a.hasNulls)b=null;else if(h)for(b=0;h--;)b+=a[h];return b},average:function(a){var h=a.length;a=e.sum(a);n(a)&&h&&(a/=h);return a},averages:function(){var a=[];t(arguments,function(h){a.push(e.average(h))});return void 0===a[0]?void 0:a},open:function(a){return a.length?a[0]:a.hasNulls?null:void 0},high:function(a){return a.length?
C(a):a.hasNulls?null:void 0},low:function(a){return a.length?x(a):a.hasNulls?null:void 0},close:function(a){return a.length?a[a.length-1]:a.hasNulls?null:void 0},ohlc:function(a,b,c,l){a=e.open(a);b=e.high(b);c=e.low(c);l=e.close(l);if(n(a)||n(b)||n(c)||n(l))return[a,b,c,l]},range:function(a,b){a=e.low(a);b=e.high(b);if(n(a)||n(b))return[a,b];if(null===a&&null===b)return null}};k.groupData=function(a,b,c,f){var h=this.data,p=this.options.data,w=[],y=[],A=[],J=a.length,d,K,k=!!b,g=[];f="function"===
typeof f?f:e[f]||q[this.type]&&e[q[this.type].approximation]||e[l.approximation];var I=this.pointArrayMap,m=I&&I.length,F=0;K=0;var r,v;m?t(I,function(){g.push([])}):g.push([]);r=m||1;for(v=0;v<=J&&!(a[v]>=c[0]);v++);for(v;v<=J;v++){for(;void 0!==c[F+1]&&a[v]>=c[F+1]||v===J;){d=c[F];this.dataGroupInfo={start:K,length:g[0].length};K=f.apply(this,g);void 0!==K&&(w.push(d),y.push(K),A.push(this.dataGroupInfo));K=v;for(d=0;d<r;d++)g[d].length=0,g[d].hasNulls=!1;F+=1;if(v===J)break}if(v===J)break;if(I){d=
this.cropStart+v;var u=h&&h[d]||this.pointClass.prototype.applyOptions.apply({series:this},[p[d]]),B;for(d=0;d<m;d++)B=u[I[d]],n(B)?g[d].push(B):null===B&&(g[d].hasNulls=!0)}else d=k?b[v]:null,n(d)?g[0].push(d):null===d&&(g[0].hasNulls=!0)}return[w,y,A]};k.processData=function(){var a=this.chart,b=this.options.dataGrouping,e=!1!==this.allowDG&&b&&v(b.enabled,a.options.isStock),l=this.visible||!a.options.chart.ignoreHiddenSeries,f,p=this.currentDataGrouping,w;this.forceCrop=e;this.groupPixelWidth=
null;this.hasProcessed=!0;if(!1!==g.apply(this,arguments)&&e){this.destroyGroupedData();var y=this.processedXData,q=this.processedYData,d=a.plotSizeX,a=this.xAxis,m=a.options.ordinal,K=this.groupPixelWidth=a.getGroupPixelWidth&&a.getGroupPixelWidth();if(K){this.isDirty=f=!0;this.points=null;e=a.getExtremes();w=e.min;e=e.max;m=m&&a.getGroupIntervalFactor(w,e,this)||1;K=K*(e-w)/d*m;d=a.getTimeTicks(a.normalizeTimeTickInterval(K,b.units||c),Math.min(w,y[0]),Math.max(e,y[y.length-1]),a.options.startOfWeek,
y,this.closestPointRange);y=k.groupData.apply(this,[y,q,d,b.approximation]);q=y[0];m=y[1];if(b.smoothed&&q.length){b=q.length-1;for(q[b]=Math.min(q[b],e);b--&&0<b;)q[b]+=K/2;q[0]=Math.max(q[0],w)}w=d.info;this.closestPointRange=d.info.totalRange;this.groupMap=y[2];B(q[0])&&q[0]<a.dataMin&&l&&(a.min===a.dataMin&&(a.min=q[0]),a.dataMin=q[0]);this.processedXData=q;this.processedYData=m}else this.groupMap=null;this.hasGroupedData=f;this.currentDataGrouping=w;this.preventGraphAnimation=(p&&p.totalRange)!==
(w&&w.totalRange)}};k.destroyGroupedData=function(){var a=this.groupedData;t(a||[],function(b,c){b&&(a[c]=b.destroy?b.destroy():null)});this.groupedData=null};k.generatePoints=function(){m.apply(this);this.destroyGroupedData();this.groupedData=this.hasGroupedData?this.points:null};d(b.prototype,"update",function(b){this.dataGroup?a.error(24):b.apply(this,[].slice.call(arguments,1))});d(f.prototype,"tooltipFooterHeaderFormatter",function(a,b,c){var e=this.chart.time,h=b.series,p=h.tooltipOptions,w=
h.options.dataGrouping,l=p.xDateFormat,q,f=h.xAxis;return f&&"datetime"===f.options.type&&w&&n(b.key)?(a=h.currentDataGrouping,w=w.dateTimeLabelFormats,a?(f=w[a.unitName],1===a.count?l=f[0]:(l=f[1],q=f[2])):!l&&w&&(l=this.getXDateFormat(b,p,f)),l=e.dateFormat(l,b.key),q&&(l+=e.dateFormat(q,b.key+a.totalRange-1)),r(p[(c?"footer":"header")+"Format"],{point:D(b.point,{key:l}),series:h},e)):a.call(this,b,c)});d(k,"destroy",function(a){this.destroyGroupedData();a.call(this)});d(k,"setOptions",function(a,
b){a=a.call(this,b);var c=this.type,e=this.chart.options.plotOptions,h=u[c].dataGrouping;q[c]&&(h||(h=E(l,q[c])),a.dataGrouping=E(h,e.series&&e.series.dataGrouping,e[c].dataGrouping,b.dataGrouping));this.chart.options.isStock&&(this.requireSorting=!0);return a});d(z.prototype,"setScale",function(a){a.call(this);t(this.series,function(a){a.hasProcessed=!1})});z.prototype.getGroupPixelWidth=function(){var a=this.series,b=a.length,c,e=0,l=!1,p;for(c=b;c--;)(p=a[c].options.dataGrouping)&&(e=Math.max(e,
p.groupPixelWidth));for(c=b;c--;)(p=a[c].options.dataGrouping)&&a[c].hasProcessed&&(b=(a[c].processedXData||a[c].data).length,a[c].groupPixelWidth||b>this.chart.plotSizeX/e||b&&p.forced)&&(l=!0);return l?e:0};z.prototype.setDataGrouping=function(a,b){var c;b=v(b,!0);a||(a={forced:!1,units:null});if(this instanceof z)for(c=this.series.length;c--;)this.series[c].update({dataGrouping:a},!1);else t(this.chart.options.series,function(b){b.dataGrouping=a},!1);b&&this.chart.redraw()}})(G);(function(a){var C=
a.each,x=a.Point,z=a.seriesType,u=a.seriesTypes;z("ohlc","column",{lineWidth:1,tooltip:{pointFormat:'\x3cspan style\x3d"color:{point.color}"\x3e\u25cf\x3c/span\x3e \x3cb\x3e {series.name}\x3c/b\x3e\x3cbr/\x3eOpen: {point.open}\x3cbr/\x3eHigh: {point.high}\x3cbr/\x3eLow: {point.low}\x3cbr/\x3eClose: {point.close}\x3cbr/\x3e'},threshold:null,states:{hover:{lineWidth:3}},stickyTracking:!0},{directTouch:!1,pointArrayMap:["open","high","low","close"],toYData:function(a){return[a.open,a.high,a.low,a.close]},
pointValKey:"close",pointAttrToOptions:{stroke:"color","stroke-width":"lineWidth"},pointAttribs:function(a,t){t=u.column.prototype.pointAttribs.call(this,a,t);var D=this.options;delete t.fill;!a.options.color&&D.upColor&&a.open<a.close&&(t.stroke=D.upColor);return t},translate:function(){var a=this,t=a.yAxis,D=!!a.modifyValue,r=["plotOpen","plotHigh","plotLow","plotClose","yBottom"];u.column.prototype.translate.apply(a);C(a.points,function(n){C([n.open,n.high,n.low,n.close,n.low],function(u,v){null!==
u&&(D&&(u=a.modifyValue(u)),n[r[v]]=t.toPixels(u,!0))});n.tooltipPos[1]=n.plotHigh+t.pos-a.chart.plotTop})},drawPoints:function(){var a=this,t=a.chart;C(a.points,function(u){var r,n,x,v,b=u.graphic,f,d=!b;void 0!==u.plotY&&(b||(u.graphic=b=t.renderer.path().add(a.group)),b.attr(a.pointAttribs(u,u.selected&&"select")),n=b.strokeWidth()%2/2,f=Math.round(u.plotX)-n,x=Math.round(u.shapeArgs.width/2),v=["M",f,Math.round(u.yBottom),"L",f,Math.round(u.plotHigh)],null!==u.open&&(r=Math.round(u.plotOpen)+
n,v.push("M",f,r,"L",f-x,r)),null!==u.close&&(r=Math.round(u.plotClose)+n,v.push("M",f,r,"L",f+x,r)),b[d?"attr":"animate"]({d:v}).addClass(u.getClassName(),!0))})},animate:null},{getClassName:function(){return x.prototype.getClassName.call(this)+(this.open<this.close?" highcharts-point-up":" highcharts-point-down")}})})(G);(function(a){var C=a.defaultPlotOptions,x=a.each,z=a.merge,u=a.seriesType,B=a.seriesTypes;u("candlestick","ohlc",z(C.column,{states:{hover:{lineWidth:2}},tooltip:C.ohlc.tooltip,
threshold:null,lineColor:"#000000",lineWidth:1,upColor:"#ffffff",stickyTracking:!0}),{pointAttribs:function(a,u){var r=B.column.prototype.pointAttribs.call(this,a,u),n=this.options,t=a.open<a.close,v=n.lineColor||this.color;r["stroke-width"]=n.lineWidth;r.fill=a.options.color||(t?n.upColor||this.color:this.color);r.stroke=a.lineColor||(t?n.upLineColor||v:v);u&&(a=n.states[u],r.fill=a.color||r.fill,r.stroke=a.lineColor||r.stroke,r["stroke-width"]=a.lineWidth||r["stroke-width"]);return r},drawPoints:function(){var a=
this,u=a.chart;x(a.points,function(r){var n=r.graphic,t,v,b,f,d,k,g,m=!n;void 0!==r.plotY&&(n||(r.graphic=n=u.renderer.path().add(a.group)),n.attr(a.pointAttribs(r,r.selected&&"select")).shadow(a.options.shadow),d=n.strokeWidth()%2/2,k=Math.round(r.plotX)-d,t=r.plotOpen,v=r.plotClose,b=Math.min(t,v),t=Math.max(t,v),g=Math.round(r.shapeArgs.width/2),v=Math.round(b)!==Math.round(r.plotHigh),f=t!==r.yBottom,b=Math.round(b)+d,t=Math.round(t)+d,d=[],d.push("M",k-g,t,"L",k-g,b,"L",k+g,b,"L",k+g,t,"Z","M",
k,b,"L",k,v?Math.round(r.plotHigh):b,"M",k,t,"L",k,f?Math.round(r.yBottom):t),n[m?"attr":"animate"]({d:d}).addClass(r.getClassName(),!0))})}})})(G);var X=function(a){var C=a.each,x=a.seriesTypes,z=a.stableSort;return{getPlotBox:function(){return a.Series.prototype.getPlotBox.call(this.options.onSeries&&this.chart.get(this.options.onSeries)||this)},translate:function(){x.column.prototype.translate.apply(this);var a=this.options,B=this.chart,t=this.points,D=t.length-1,r,n,E=a.onSeries;r=E&&B.get(E);
var a=a.onKey||"y",E=r&&r.options.step,v=r&&r.points,b=v&&v.length,f=this.xAxis,d=this.yAxis,k=0,g,m,l,q;if(r&&r.visible&&b)for(k=(r.pointXOffset||0)+(r.barW||0)/2,r=r.currentDataGrouping,m=v[b-1].x+(r?r.totalRange:0),z(t,function(a,b){return a.x-b.x}),a="plot"+a[0].toUpperCase()+a.substr(1);b--&&t[D]&&!(g=v[b],r=t[D],r.y=g.y,g.x<=r.x&&void 0!==g[a]&&(r.x<=m&&(r.plotY=g[a],g.x<r.x&&!E&&(l=v[b+1])&&void 0!==l[a]&&(q=(r.x-g.x)/(l.x-g.x),r.plotY+=q*(l[a]-g[a]),r.y+=q*(l.y-g.y))),D--,b++,0>D)););C(t,
function(a,b){var c;a.plotX+=k;void 0===a.plotY&&(0<=a.plotX&&a.plotX<=f.len?a.plotY=B.chartHeight-f.bottom-(f.opposite?f.height:0)+f.offset-d.top:a.shapeArgs={});(n=t[b-1])&&n.plotX===a.plotX&&(void 0===n.stackIndex&&(n.stackIndex=0),c=n.stackIndex+1);a.stackIndex=c})}}}(G);(function(a,C){function x(a){v[a+"pin"]=function(b,d,k,g,m){var l=m&&m.anchorX;m=m&&m.anchorY;"circle"===a&&g>k&&(b-=Math.round((g-k)/2),k=g);b=v[a](b,d,k,g);l&&m&&(b.push("M","circle"===a?b[1]-b[4]:b[1]+b[4]/2,d>m?d:d+g,"L",
l,m),b=b.concat(v.circle(l-1,m-1,2,2)));return b}}var z=a.addEvent,u=a.each,B=a.merge,t=a.noop,D=a.Renderer,r=a.seriesType,n=a.TrackerMixin,E=a.VMLRenderer,v=a.SVGRenderer.prototype.symbols;r("flags","column",{pointRange:0,allowOverlapX:!1,shape:"flag",stackDistance:12,textAlign:"center",tooltip:{pointFormat:"{point.text}\x3cbr/\x3e"},threshold:null,y:-30,fillColor:"#ffffff",lineWidth:1,states:{hover:{lineColor:"#000000",fillColor:"#ccd6eb"}},style:{fontSize:"11px",fontWeight:"bold"}},{sorted:!1,
noSharedTooltip:!0,allowDG:!1,takeOrdinalPosition:!1,trackerGroups:["markerGroup"],forceCrop:!0,init:a.Series.prototype.init,pointAttribs:function(a,f){var b=this.options,k=a&&a.color||this.color,g=b.lineColor,m=a&&a.lineWidth;a=a&&a.fillColor||b.fillColor;f&&(a=b.states[f].fillColor,g=b.states[f].lineColor,m=b.states[f].lineWidth);return{fill:a||k,stroke:g||k,"stroke-width":m||b.lineWidth||0}},translate:C.translate,getPlotBox:C.getPlotBox,drawPoints:function(){var b=this.points,f=this.chart,d=f.renderer,
k,g,m=this.options,l=m.y,q,c,e,h,A,n,r=this.yAxis,t={},p=[];for(c=b.length;c--;)e=b[c],n=e.plotX>this.xAxis.len,k=e.plotX,h=e.stackIndex,q=e.options.shape||m.shape,g=e.plotY,void 0!==g&&(g=e.plotY+l-(void 0!==h&&h*m.stackDistance)),e.anchorX=h?void 0:e.plotX,A=h?void 0:e.plotY,h=e.graphic,void 0!==g&&0<=k&&!n?(h||(h=e.graphic=d.label("",null,null,q,null,null,m.useHTML).attr(this.pointAttribs(e)).css(B(m.style,e.style)).attr({align:"flag"===q?"left":"center",width:m.width,height:m.height,"text-align":m.textAlign}).addClass("highcharts-point").add(this.markerGroup),
e.graphic.div&&(e.graphic.div.point=e),h.shadow(m.shadow),h.isNew=!0),0<k&&(k-=h.strokeWidth()%2),q={y:g,anchorY:A},m.allowOverlapX&&(q.x=k,q.anchorX=e.anchorX),h.attr({text:e.options.title||m.title||"A"})[h.isNew?"attr":"animate"](q),m.allowOverlapX||(t[e.plotX]?t[e.plotX].size=Math.max(t[e.plotX].size,h.width):t[e.plotX]={align:0,size:h.width,target:k,anchorX:k}),e.tooltipPos=f.inverted?[r.len+r.pos-f.plotLeft-g,this.xAxis.len-k]:[k,g+r.pos-f.plotTop]):h&&(e.graphic=h.destroy());m.allowOverlapX||
(a.objectEach(t,function(a){a.plotX=a.anchorX;p.push(a)}),a.distribute(p,this.xAxis.len),u(b,function(a){var b=a.graphic&&t[a.plotX];b&&(a.graphic[a.graphic.isNew?"attr":"animate"]({x:b.pos,anchorX:a.anchorX}),a.graphic.isNew=!1)}));m.useHTML&&a.wrap(this.markerGroup,"on",function(b){return a.SVGElement.prototype.on.apply(b.apply(this,[].slice.call(arguments,1)),[].slice.call(arguments,1))})},drawTracker:function(){var a=this.points;n.drawTrackerPoint.apply(this);u(a,function(b){var d=b.graphic;d&&
z(d.element,"mouseover",function(){0<b.stackIndex&&!b.raised&&(b._y=d.y,d.attr({y:b._y-8}),b.raised=!0);u(a,function(a){a!==b&&a.raised&&a.graphic&&(a.graphic.attr({y:a._y}),a.raised=!1)})})})},animate:t,buildKDTree:t,setClip:t});v.flag=function(a,f,d,k,g){var b=g&&g.anchorX||a;g=g&&g.anchorY||f;return v.circle(b-1,g-1,2,2).concat(["M",b,g,"L",a,f+k,a,f,a+d,f,a+d,f+k,a,f+k,"Z"])};x("circle");x("square");D===E&&u(["flag","circlepin","squarepin"],function(a){E.prototype.symbols[a]=v[a]})})(G,X);(function(a){function C(a,
b,c){this.init(a,b,c)}var x=a.addEvent,z=a.Axis,u=a.correctFloat,B=a.defaultOptions,t=a.defined,D=a.destroyObjectProperties,r=a.each,n=a.fireEvent,E=a.hasTouch,v=a.isTouchDevice,b=a.merge,f=a.pick,d=a.removeEvent,k=a.wrap,g,m={height:v?20:14,barBorderRadius:0,buttonBorderRadius:0,liveRedraw:a.svg&&!v,margin:10,minWidth:6,step:.2,zIndex:3,barBackgroundColor:"#cccccc",barBorderWidth:1,barBorderColor:"#cccccc",buttonArrowColor:"#333333",buttonBackgroundColor:"#e6e6e6",buttonBorderColor:"#cccccc",buttonBorderWidth:1,
rifleColor:"#333333",trackBackgroundColor:"#f2f2f2",trackBorderColor:"#f2f2f2",trackBorderWidth:1};B.scrollbar=b(!0,m,B.scrollbar);a.swapXY=g=function(a,b){var c=a.length,e;if(b)for(b=0;b<c;b+=3)e=a[b+1],a[b+1]=a[b+2],a[b+2]=e;return a};C.prototype={init:function(a,q,c){this.scrollbarButtons=[];this.renderer=a;this.userOptions=q;this.options=b(m,q);this.chart=c;this.size=f(this.options.size,this.options.height);q.enabled&&(this.render(),this.initEvents(),this.addEvents())},render:function(){var a=
this.renderer,b=this.options,c=this.size,e;this.group=e=a.g("scrollbar").attr({zIndex:b.zIndex,translateY:-99999}).add();this.track=a.rect().addClass("highcharts-scrollbar-track").attr({x:0,r:b.trackBorderRadius||0,height:c,width:c}).add(e);this.track.attr({fill:b.trackBackgroundColor,stroke:b.trackBorderColor,"stroke-width":b.trackBorderWidth});this.trackBorderWidth=this.track.strokeWidth();this.track.attr({y:-this.trackBorderWidth%2/2});this.scrollbarGroup=a.g().add(e);this.scrollbar=a.rect().addClass("highcharts-scrollbar-thumb").attr({height:c,
width:c,r:b.barBorderRadius||0}).add(this.scrollbarGroup);this.scrollbarRifles=a.path(g(["M",-3,c/4,"L",-3,2*c/3,"M",0,c/4,"L",0,2*c/3,"M",3,c/4,"L",3,2*c/3],b.vertical)).addClass("highcharts-scrollbar-rifles").add(this.scrollbarGroup);this.scrollbar.attr({fill:b.barBackgroundColor,stroke:b.barBorderColor,"stroke-width":b.barBorderWidth});this.scrollbarRifles.attr({stroke:b.rifleColor,"stroke-width":1});this.scrollbarStrokeWidth=this.scrollbar.strokeWidth();this.scrollbarGroup.translate(-this.scrollbarStrokeWidth%
2/2,-this.scrollbarStrokeWidth%2/2);this.drawScrollbarButton(0);this.drawScrollbarButton(1)},position:function(a,b,c,e){var h=this.options.vertical,l=0,d=this.rendered?"animate":"attr";this.x=a;this.y=b+this.trackBorderWidth;this.width=c;this.xOffset=this.height=e;this.yOffset=l;h?(this.width=this.yOffset=c=l=this.size,this.xOffset=b=0,this.barWidth=e-2*c,this.x=a+=this.options.margin):(this.height=this.xOffset=e=b=this.size,this.barWidth=c-2*e,this.y+=this.options.margin);this.group[d]({translateX:a,
translateY:this.y});this.track[d]({width:c,height:e});this.scrollbarButtons[1][d]({translateX:h?0:c-b,translateY:h?e-l:0})},drawScrollbarButton:function(a){var b=this.renderer,c=this.scrollbarButtons,e=this.options,h=this.size,l;l=b.g().add(this.group);c.push(l);l=b.rect().addClass("highcharts-scrollbar-button").add(l);l.attr({stroke:e.buttonBorderColor,"stroke-width":e.buttonBorderWidth,fill:e.buttonBackgroundColor});l.attr(l.crisp({x:-.5,y:-.5,width:h+1,height:h+1,r:e.buttonBorderRadius},l.strokeWidth()));
l=b.path(g(["M",h/2+(a?-1:1),h/2-3,"L",h/2+(a?-1:1),h/2+3,"L",h/2+(a?2:-2),h/2],e.vertical)).addClass("highcharts-scrollbar-arrow").add(c[a]);l.attr({fill:e.buttonArrowColor})},setRange:function(a,b){var c=this.options,e=c.vertical,l=c.minWidth,d=this.barWidth,f,q,g=this.rendered&&!this.hasDragged?"animate":"attr";t(d)&&(a=Math.max(a,0),f=Math.ceil(d*a),this.calculatedWidth=q=u(d*Math.min(b,1)-f),q<l&&(f=(d-l+q)*a,q=l),l=Math.floor(f+this.xOffset+this.yOffset),d=q/2-.5,this.from=a,this.to=b,e?(this.scrollbarGroup[g]({translateY:l}),
this.scrollbar[g]({height:q}),this.scrollbarRifles[g]({translateY:d}),this.scrollbarTop=l,this.scrollbarLeft=0):(this.scrollbarGroup[g]({translateX:l}),this.scrollbar[g]({width:q}),this.scrollbarRifles[g]({translateX:d}),this.scrollbarLeft=l,this.scrollbarTop=0),12>=q?this.scrollbarRifles.hide():this.scrollbarRifles.show(!0),!1===c.showFull&&(0>=a&&1<=b?this.group.hide():this.group.show()),this.rendered=!0)},initEvents:function(){var a=this;a.mouseMoveHandler=function(b){var c=a.chart.pointer.normalize(b),
e=a.options.vertical?"chartY":"chartX",h=a.initPositions;!a.grabbedCenter||b.touches&&0===b.touches[0][e]||(c=a.cursorToScrollbarPosition(c)[e],e=a[e],e=c-e,a.hasDragged=!0,a.updatePosition(h[0]+e,h[1]+e),a.hasDragged&&n(a,"changed",{from:a.from,to:a.to,trigger:"scrollbar",DOMType:b.type,DOMEvent:b}))};a.mouseUpHandler=function(b){a.hasDragged&&n(a,"changed",{from:a.from,to:a.to,trigger:"scrollbar",DOMType:b.type,DOMEvent:b});a.grabbedCenter=a.hasDragged=a.chartX=a.chartY=null};a.mouseDownHandler=
function(b){b=a.chart.pointer.normalize(b);b=a.cursorToScrollbarPosition(b);a.chartX=b.chartX;a.chartY=b.chartY;a.initPositions=[a.from,a.to];a.grabbedCenter=!0};a.buttonToMinClick=function(b){var c=u(a.to-a.from)*a.options.step;a.updatePosition(u(a.from-c),u(a.to-c));n(a,"changed",{from:a.from,to:a.to,trigger:"scrollbar",DOMEvent:b})};a.buttonToMaxClick=function(b){var c=(a.to-a.from)*a.options.step;a.updatePosition(a.from+c,a.to+c);n(a,"changed",{from:a.from,to:a.to,trigger:"scrollbar",DOMEvent:b})};
a.trackClick=function(b){var c=a.chart.pointer.normalize(b),e=a.to-a.from,h=a.y+a.scrollbarTop,d=a.x+a.scrollbarLeft;a.options.vertical&&c.chartY>h||!a.options.vertical&&c.chartX>d?a.updatePosition(a.from+e,a.to+e):a.updatePosition(a.from-e,a.to-e);n(a,"changed",{from:a.from,to:a.to,trigger:"scrollbar",DOMEvent:b})}},cursorToScrollbarPosition:function(a){var b=this.options,b=b.minWidth>this.calculatedWidth?b.minWidth:0;return{chartX:(a.chartX-this.x-this.xOffset)/(this.barWidth-b),chartY:(a.chartY-
this.y-this.yOffset)/(this.barWidth-b)}},updatePosition:function(a,b){1<b&&(a=u(1-u(b-a)),b=1);0>a&&(b=u(b-a),a=0);this.from=a;this.to=b},update:function(a){this.destroy();this.init(this.chart.renderer,b(!0,this.options,a),this.chart)},addEvents:function(){var a=this.options.inverted?[1,0]:[0,1],b=this.scrollbarButtons,c=this.scrollbarGroup.element,e=this.mouseDownHandler,h=this.mouseMoveHandler,d=this.mouseUpHandler,a=[[b[a[0]].element,"click",this.buttonToMinClick],[b[a[1]].element,"click",this.buttonToMaxClick],
[this.track.element,"click",this.trackClick],[c,"mousedown",e],[c.ownerDocument,"mousemove",h],[c.ownerDocument,"mouseup",d]];E&&a.push([c,"touchstart",e],[c.ownerDocument,"touchmove",h],[c.ownerDocument,"touchend",d]);r(a,function(a){x.apply(null,a)});this._events=a},removeEvents:function(){r(this._events,function(a){d.apply(null,a)});this._events.length=0},destroy:function(){var a=this.chart.scroller;this.removeEvents();r(["track","scrollbarRifles","scrollbar","scrollbarGroup","group"],function(a){this[a]&&
this[a].destroy&&(this[a]=this[a].destroy())},this);a&&this===a.scrollbar&&(a.scrollbar=null,D(a.scrollbarButtons))}};k(z.prototype,"init",function(a){var b=this;a.apply(b,Array.prototype.slice.call(arguments,1));b.options.scrollbar&&b.options.scrollbar.enabled&&(b.options.scrollbar.vertical=!b.horiz,b.options.startOnTick=b.options.endOnTick=!1,b.scrollbar=new C(b.chart.renderer,b.options.scrollbar,b.chart),x(b.scrollbar,"changed",function(a){var c=Math.min(f(b.options.min,b.min),b.min,b.dataMin),
h=Math.max(f(b.options.max,b.max),b.max,b.dataMax)-c,d;b.horiz&&!b.reversed||!b.horiz&&b.reversed?(d=c+h*this.to,c+=h*this.from):(d=c+h*(1-this.from),c+=h*(1-this.to));b.setExtremes(c,d,!0,!1,a)}))});k(z.prototype,"render",function(a){var b=Math.min(f(this.options.min,this.min),this.min,f(this.dataMin,this.min)),c=Math.max(f(this.options.max,this.max),this.max,f(this.dataMax,this.max)),e=this.scrollbar,h=this.titleOffset||0;a.apply(this,Array.prototype.slice.call(arguments,1));if(e){this.horiz?(e.position(this.left,
this.top+this.height+2+this.chart.scrollbarsOffsets[1]+(this.opposite?0:h+this.axisTitleMargin+this.offset),this.width,this.height),h=1):(e.position(this.left+this.width+2+this.chart.scrollbarsOffsets[0]+(this.opposite?h+this.axisTitleMargin+this.offset:0),this.top,this.width,this.height),h=0);if(!this.opposite&&!this.horiz||this.opposite&&this.horiz)this.chart.scrollbarsOffsets[h]+=this.scrollbar.size+this.scrollbar.options.margin;isNaN(b)||isNaN(c)||!t(this.min)||!t(this.max)?e.setRange(0,0):(h=
(this.min-b)/(c-b),b=(this.max-b)/(c-b),this.horiz&&!this.reversed||!this.horiz&&this.reversed?e.setRange(h,b):e.setRange(1-b,1-h))}});k(z.prototype,"getOffset",function(a){var b=this.horiz?2:1,c=this.scrollbar;a.apply(this,Array.prototype.slice.call(arguments,1));c&&(this.chart.scrollbarsOffsets=[0,0],this.chart.axisOffset[b]+=c.size+c.options.margin)});k(z.prototype,"destroy",function(a){this.scrollbar&&(this.scrollbar=this.scrollbar.destroy());a.apply(this,Array.prototype.slice.call(arguments,
1))});a.Scrollbar=C})(G);(function(a){function C(a){this.init(a)}var x=a.addEvent,z=a.Axis,u=a.Chart,B=a.color,t=a.defaultOptions,D=a.defined,r=a.destroyObjectProperties,n=a.each,E=a.erase,v=a.error,b=a.extend,f=a.grep,d=a.hasTouch,k=a.isArray,g=a.isNumber,m=a.isObject,l=a.merge,q=a.pick,c=a.removeEvent,e=a.Scrollbar,h=a.Series,A=a.seriesTypes,I=a.wrap,F=[].concat(a.defaultDataGroupingUnits),L=function(a){var b=f(arguments,g);if(b.length)return Math[a].apply(0,b)};F[4]=["day",[1,2,3,4]];F[5]=["week",
[1,2,3]];A=void 0===A.areaspline?"line":"areaspline";b(t,{navigator:{height:40,margin:25,maskInside:!0,handles:{width:7,height:15,symbols:["navigator-handle","navigator-handle"],enabled:!0,lineWidth:1,backgroundColor:"#f2f2f2",borderColor:"#999999"},maskFill:B("#6685c2").setOpacity(.3).get(),outlineColor:"#cccccc",outlineWidth:1,series:{type:A,fillOpacity:.05,lineWidth:1,compare:null,dataGrouping:{approximation:"average",enabled:!0,groupPixelWidth:2,smoothed:!0,units:F},dataLabels:{enabled:!1,zIndex:2},
id:"highcharts-navigator-series",className:"highcharts-navigator-series",lineColor:null,marker:{enabled:!1},pointRange:0,threshold:null},xAxis:{overscroll:0,className:"highcharts-navigator-xaxis",tickLength:0,lineWidth:0,gridLineColor:"#e6e6e6",gridLineWidth:1,tickPixelInterval:200,labels:{align:"left",style:{color:"#999999"},x:3,y:-4},crosshair:!1},yAxis:{className:"highcharts-navigator-yaxis",gridLineWidth:0,startOnTick:!1,endOnTick:!1,minPadding:.1,maxPadding:.1,labels:{enabled:!1},crosshair:!1,
title:{text:null},tickLength:0,tickWidth:0}}});a.Renderer.prototype.symbols["navigator-handle"]=function(a,b,c,e,h){a=h.width/2;b=Math.round(a/3)+.5;h=h.height;return["M",-a-1,.5,"L",a,.5,"L",a,h+.5,"L",-a-1,h+.5,"L",-a-1,.5,"M",-b,4,"L",-b,h-3,"M",b-1,4,"L",b-1,h-3]};C.prototype={drawHandle:function(a,b,c,e){var p=this.navigatorOptions.handles.height;this.handles[b][e](c?{translateX:Math.round(this.left+this.height/2),translateY:Math.round(this.top+parseInt(a,10)+.5-p)}:{translateX:Math.round(this.left+
parseInt(a,10)),translateY:Math.round(this.top+this.height/2-p/2-1)})},drawOutline:function(a,b,c,e){var p=this.navigatorOptions.maskInside,w=this.outline.strokeWidth(),h=w/2,w=w%2/2,y=this.outlineHeight,d=this.scrollbarHeight,f=this.size,g=this.left-d,k=this.top;c?(g-=h,c=k+b+w,b=k+a+w,a=["M",g+y,k-d-w,"L",g+y,c,"L",g,c,"L",g,b,"L",g+y,b,"L",g+y,k+f+d].concat(p?["M",g+y,c-h,"L",g+y,b+h]:[])):(a+=g+d-w,b+=g+d-w,k+=h,a=["M",g,k,"L",a,k,"L",a,k+y,"L",b,k+y,"L",b,k,"L",g+f+2*d,k].concat(p?["M",a-h,k,
"L",b+h,k]:[]));this.outline[e]({d:a})},drawMasks:function(a,b,c,e){var p=this.left,w=this.top,h=this.height,y,d,f,g;c?(f=[p,p,p],g=[w,w+a,w+b],d=[h,h,h],y=[a,b-a,this.size-b]):(f=[p,p+a,p+b],g=[w,w,w],d=[a,b-a,this.size-b],y=[h,h,h]);n(this.shades,function(a,b){a[e]({x:f[b],y:g[b],width:d[b],height:y[b]})})},renderElements:function(){var a=this,b=a.navigatorOptions,c=b.maskInside,e=a.chart,h=e.inverted,d=e.renderer,f;a.navigatorGroup=f=d.g("navigator").attr({zIndex:8,visibility:"hidden"}).add();
var g={cursor:h?"ns-resize":"ew-resize"};n([!c,c,!c],function(c,p){a.shades[p]=d.rect().addClass("highcharts-navigator-mask"+(1===p?"-inside":"-outside")).attr({fill:c?b.maskFill:"rgba(0,0,0,0)"}).css(1===p&&g).add(f)});a.outline=d.path().addClass("highcharts-navigator-outline").attr({"stroke-width":b.outlineWidth,stroke:b.outlineColor}).add(f);b.handles.enabled&&n([0,1],function(c){b.handles.inverted=e.inverted;a.handles[c]=d.symbol(b.handles.symbols[c],-b.handles.width/2-1,0,b.handles.width,b.handles.height,
b.handles);a.handles[c].attr({zIndex:7-c}).addClass("highcharts-navigator-handle highcharts-navigator-handle-"+["left","right"][c]).add(f);var p=b.handles;a.handles[c].attr({fill:p.backgroundColor,stroke:p.borderColor,"stroke-width":p.lineWidth}).css(g)})},update:function(a){n(this.series||[],function(a){a.baseSeries&&delete a.baseSeries.navigatorSeries});this.destroy();l(!0,this.chart.options.navigator,this.options,a);this.init(this.chart)},render:function(b,c,e,h){var p=this.chart,w,d,y=this.scrollbarHeight,
f,k=this.xAxis;w=k.fake?p.xAxis[0]:k;var l=this.navigatorEnabled,H,m=this.rendered;d=p.inverted;var A,n=p.xAxis[0].minRange,r=p.xAxis[0].options.maxRange;if(!this.hasDragged||D(e)){if(!g(b)||!g(c))if(m)e=0,h=q(k.width,w.width);else return;this.left=q(k.left,p.plotLeft+y+(d?p.plotWidth:0));this.size=H=f=q(k.len,(d?p.plotHeight:p.plotWidth)-2*y);p=d?y:f+2*y;e=q(e,k.toPixels(b,!0));h=q(h,k.toPixels(c,!0));g(e)&&Infinity!==Math.abs(e)||(e=0,h=p);b=k.toValue(e,!0);c=k.toValue(h,!0);A=Math.abs(a.correctFloat(c-
b));A<n?this.grabbedLeft?e=k.toPixels(c-n,!0):this.grabbedRight&&(h=k.toPixels(b+n,!0)):D(r)&&A>r&&(this.grabbedLeft?e=k.toPixels(c-r,!0):this.grabbedRight&&(h=k.toPixels(b+r,!0)));this.zoomedMax=Math.min(Math.max(e,h,0),H);this.zoomedMin=Math.min(Math.max(this.fixedWidth?this.zoomedMax-this.fixedWidth:Math.min(e,h),0),H);this.range=this.zoomedMax-this.zoomedMin;H=Math.round(this.zoomedMax);e=Math.round(this.zoomedMin);l&&(this.navigatorGroup.attr({visibility:"visible"}),m=m&&!this.hasDragged?"animate":
"attr",this.drawMasks(e,H,d,m),this.drawOutline(e,H,d,m),this.navigatorOptions.handles.enabled&&(this.drawHandle(e,0,d,m),this.drawHandle(H,1,d,m)));this.scrollbar&&(d?(d=this.top-y,w=this.left-y+(l||!w.opposite?0:(w.titleOffset||0)+w.axisTitleMargin),y=f+2*y):(d=this.top+(l?this.height:-y),w=this.left-y),this.scrollbar.position(w,d,p,y),this.scrollbar.setRange(this.zoomedMin/f,this.zoomedMax/f));this.rendered=!0}},addMouseEvents:function(){var a=this,b=a.chart,c=b.container,e=[],h,f;a.mouseMoveHandler=
h=function(b){a.onMouseMove(b)};a.mouseUpHandler=f=function(b){a.onMouseUp(b)};e=a.getPartsEvents("mousedown");e.push(x(c,"mousemove",h),x(c.ownerDocument,"mouseup",f));d&&(e.push(x(c,"touchmove",h),x(c.ownerDocument,"touchend",f)),e.concat(a.getPartsEvents("touchstart")));a.eventsToUnbind=e;a.series&&a.series[0]&&e.push(x(a.series[0].xAxis,"foundExtremes",function(){b.navigator.modifyNavigatorAxisExtremes()}))},getPartsEvents:function(a){var b=this,c=[];n(["shades","handles"],function(p){n(b[p],
function(e,h){c.push(x(e.element,a,function(a){b[p+"Mousedown"](a,h)}))})});return c},shadesMousedown:function(a,b){a=this.chart.pointer.normalize(a);var c=this.chart,e=this.xAxis,p=this.zoomedMin,h=this.left,d=this.size,w=this.range,f=a.chartX,g,k;c.inverted&&(f=a.chartY,h=this.top);1===b?(this.grabbedCenter=f,this.fixedWidth=w,this.dragOffset=f-p):(a=f-h-w/2,0===b?a=Math.max(0,a):2===b&&a+w>=d&&(a=d-w,e.reversed?(a-=w,k=this.getUnionExtremes().dataMin):g=this.getUnionExtremes().dataMax),a!==p&&
(this.fixedWidth=w,b=e.toFixedRange(a,a+w,k,g),D(b.min)&&c.xAxis[0].setExtremes(Math.min(b.min,b.max),Math.max(b.min,b.max),!0,null,{trigger:"navigator"})))},handlesMousedown:function(a,b){this.chart.pointer.normalize(a);a=this.chart;var c=a.xAxis[0],e=a.inverted&&!c.reversed||!a.inverted&&c.reversed;0===b?(this.grabbedLeft=!0,this.otherHandlePos=this.zoomedMax,this.fixedExtreme=e?c.min:c.max):(this.grabbedRight=!0,this.otherHandlePos=this.zoomedMin,this.fixedExtreme=e?c.max:c.min);a.fixedRange=null},
onMouseMove:function(a){var b=this,c=b.chart,e=b.left,p=b.navigatorSize,h=b.range,d=b.dragOffset,f=c.inverted;a.touches&&0===a.touches[0].pageX||(a=c.pointer.normalize(a),c=a.chartX,f&&(e=b.top,c=a.chartY),b.grabbedLeft?(b.hasDragged=!0,b.render(0,0,c-e,b.otherHandlePos)):b.grabbedRight?(b.hasDragged=!0,b.render(0,0,b.otherHandlePos,c-e)):b.grabbedCenter&&(b.hasDragged=!0,c<d?c=d:c>p+d-h&&(c=p+d-h),b.render(0,0,c-d,c-d+h)),b.hasDragged&&b.scrollbar&&b.scrollbar.options.liveRedraw&&(a.DOMType=a.type,
setTimeout(function(){b.onMouseUp(a)},0)))},onMouseUp:function(a){var b=this.chart,c=this.xAxis,e=c&&c.reversed,h=this.scrollbar,p,d,f=a.DOMEvent||a;(!this.hasDragged||h&&h.hasDragged)&&"scrollbar"!==a.trigger||(h=this.getUnionExtremes(),this.zoomedMin===this.otherHandlePos?p=this.fixedExtreme:this.zoomedMax===this.otherHandlePos&&(d=this.fixedExtreme),this.zoomedMax===this.size&&(d=e?h.dataMin:h.dataMax),0===this.zoomedMin&&(p=e?h.dataMax:h.dataMin),c=c.toFixedRange(this.zoomedMin,this.zoomedMax,
p,d),D(c.min)&&b.xAxis[0].setExtremes(Math.min(c.min,c.max),Math.max(c.min,c.max),!0,this.hasDragged?!1:null,{trigger:"navigator",triggerOp:"navigator-drag",DOMEvent:f}));"mousemove"!==a.DOMType&&(this.grabbedLeft=this.grabbedRight=this.grabbedCenter=this.fixedWidth=this.fixedExtreme=this.otherHandlePos=this.hasDragged=this.dragOffset=null)},removeEvents:function(){this.eventsToUnbind&&(n(this.eventsToUnbind,function(a){a()}),this.eventsToUnbind=void 0);this.removeBaseSeriesEvents()},removeBaseSeriesEvents:function(){var a=
this.baseSeries||[];this.navigatorEnabled&&a[0]&&(!1!==this.navigatorOptions.adaptToUpdatedData&&n(a,function(a){c(a,"updatedData",this.updatedDataHandler)},this),a[0].xAxis&&c(a[0].xAxis,"foundExtremes",this.modifyBaseAxisExtremes))},init:function(a){var b=a.options,c=b.navigator,h=c.enabled,d=b.scrollbar,p=d.enabled,b=h?c.height:0,f=p?d.height:0;this.handles=[];this.shades=[];this.chart=a;this.setBaseSeries();this.height=b;this.scrollbarHeight=f;this.scrollbarEnabled=p;this.navigatorEnabled=h;this.navigatorOptions=
c;this.scrollbarOptions=d;this.outlineHeight=b+f;this.opposite=q(c.opposite,!h&&a.inverted);var g=this,d=g.baseSeries,p=a.xAxis.length,k=a.yAxis.length,m=d&&d[0]&&d[0].xAxis||a.xAxis[0];a.extraMargin={type:g.opposite?"plotTop":"marginBottom",value:(h||!a.inverted?g.outlineHeight:0)+c.margin};a.inverted&&(a.extraMargin.type=g.opposite?"marginRight":"plotLeft");a.isDirtyBox=!0;g.navigatorEnabled?(g.xAxis=new z(a,l({breaks:m.options.breaks,ordinal:m.options.ordinal},c.xAxis,{id:"navigator-x-axis",yAxis:"navigator-y-axis",
isX:!0,type:"datetime",index:p,offset:0,keepOrdinalPadding:!0,startOnTick:!1,endOnTick:!1,minPadding:0,maxPadding:0,zoomEnabled:!1},a.inverted?{offsets:[f,0,-f,0],width:b}:{offsets:[0,-f,0,f],height:b})),g.yAxis=new z(a,l(c.yAxis,{id:"navigator-y-axis",alignTicks:!1,offset:0,index:k,zoomEnabled:!1},a.inverted?{width:b}:{height:b})),d||c.series.data?g.updateNavigatorSeries():0===a.series.length&&I(a,"redraw",function(b,c){0<a.series.length&&!g.series&&(g.setBaseSeries(),a.redraw=b);b.call(a,c)}),g.renderElements(),
g.addMouseEvents()):g.xAxis={translate:function(b,c){var e=a.xAxis[0],h=e.getExtremes(),d=e.len-2*f,p=L("min",e.options.min,h.dataMin),e=L("max",e.options.max,h.dataMax)-p;return c?b*e/d+p:d*(b-p)/e},toPixels:function(a){return this.translate(a)},toValue:function(a){return this.translate(a,!0)},toFixedRange:z.prototype.toFixedRange,fake:!0};a.options.scrollbar.enabled&&(a.scrollbar=g.scrollbar=new e(a.renderer,l(a.options.scrollbar,{margin:g.navigatorEnabled?0:10,vertical:a.inverted}),a),x(g.scrollbar,
"changed",function(b){var c=g.size,e=c*this.to,c=c*this.from;g.hasDragged=g.scrollbar.hasDragged;g.render(0,0,c,e);(a.options.scrollbar.liveRedraw||"mousemove"!==b.DOMType&&"touchmove"!==b.DOMType)&&setTimeout(function(){g.onMouseUp(b)})}));g.addBaseSeriesEvents();g.addChartEvents()},getUnionExtremes:function(a){var b=this.chart.xAxis[0],c=this.xAxis,e=c.options,h=b.options,d;a&&null===b.dataMin||(d={dataMin:q(e&&e.min,L("min",h.min,b.dataMin,c.dataMin,c.min)),dataMax:q(e&&e.max,L("max",h.max,b.dataMax,
c.dataMax,c.max))});return d},setBaseSeries:function(a,b){var c=this.chart,e=this.baseSeries=[];a=a||c.options&&c.options.navigator.baseSeries||0;n(c.series||[],function(b,c){b.options.isInternal||!b.options.showInNavigator&&(c!==a&&b.options.id!==a||!1===b.options.showInNavigator)||e.push(b)});this.xAxis&&!this.xAxis.fake&&this.updateNavigatorSeries(b)},updateNavigatorSeries:function(e){var h=this,d=h.chart,p=h.baseSeries,f,g,m=h.navigatorOptions.series,A,q={enableMouseTracking:!1,index:null,linkedTo:null,
group:"nav",padXAxis:!1,xAxis:"navigator-x-axis",yAxis:"navigator-y-axis",showInLegend:!1,stacking:!1,isInternal:!0,visible:!0},r=h.series=a.grep(h.series||[],function(b){var e=b.baseSeries;return 0>a.inArray(e,p)?(e&&(c(e,"updatedData",h.updatedDataHandler),delete e.navigatorSeries),b.destroy(),!1):!0});p&&p.length&&n(p,function(a){var c=a.navigatorSeries,w=b({color:a.color},k(m)?t.navigator.series:m);c&&!1===h.navigatorOptions.adaptToUpdatedData||(q.name="Navigator "+p.length,f=a.options||{},A=
f.navigatorOptions||{},g=l(f,q,w,A),w=A.data||w.data,h.hasNavigatorData=h.hasNavigatorData||!!w,g.data=w||f.data&&f.data.slice(0),c&&c.options?c.update(g,e):(a.navigatorSeries=d.initSeries(g),a.navigatorSeries.baseSeries=a,r.push(a.navigatorSeries)))});if(m.data&&(!p||!p.length)||k(m))h.hasNavigatorData=!1,m=a.splat(m),n(m,function(a,b){q.name="Navigator "+(r.length+1);g=l(t.navigator.series,{color:d.series[b]&&!d.series[b].options.isInternal&&d.series[b].color||d.options.colors[b]||d.options.colors[0]},
q,a);g.data=a.data;g.data&&(h.hasNavigatorData=!0,r.push(d.initSeries(g)))});this.addBaseSeriesEvents()},addBaseSeriesEvents:function(){var a=this,b=a.baseSeries||[];b[0]&&b[0].xAxis&&x(b[0].xAxis,"foundExtremes",this.modifyBaseAxisExtremes);n(b,function(b){x(b,"show",function(){this.navigatorSeries&&this.navigatorSeries.setVisible(!0,!1)});x(b,"hide",function(){this.navigatorSeries&&this.navigatorSeries.setVisible(!1,!1)});!1!==this.navigatorOptions.adaptToUpdatedData&&b.xAxis&&x(b,"updatedData",
this.updatedDataHandler);x(b,"remove",function(){this.navigatorSeries&&(E(a.series,this.navigatorSeries),this.navigatorSeries.remove(!1),delete this.navigatorSeries)})},this)},modifyNavigatorAxisExtremes:function(){var a=this.xAxis,b;a.getExtremes&&(!(b=this.getUnionExtremes(!0))||b.dataMin===a.min&&b.dataMax===a.max||(a.min=b.dataMin,a.max=b.dataMax))},modifyBaseAxisExtremes:function(){var a=this.chart.navigator,b=this.getExtremes(),c=b.dataMin,e=b.dataMax,b=b.max-b.min,h=a.stickToMin,d=a.stickToMax,
f=this.options.overscroll,k,m,l=a.series&&a.series[0],A=!!this.setExtremes;this.eventArgs&&"rangeSelectorButton"===this.eventArgs.trigger||(h&&(m=c,k=m+b),d&&(k=e+f,h||(m=Math.max(k-b,l&&l.xData?l.xData[0]:-Number.MAX_VALUE))),A&&(h||d)&&g(m)&&(this.min=this.userMin=m,this.max=this.userMax=k));a.stickToMin=a.stickToMax=null},updatedDataHandler:function(){var a=this.chart.navigator,b=this.navigatorSeries;a.stickToMax=a.xAxis.reversed?0===Math.round(a.zoomedMin):Math.round(a.zoomedMax)>=Math.round(a.size);
a.stickToMin=g(this.xAxis.min)&&this.xAxis.min<=this.xData[0]&&(!this.chart.fixedRange||!a.stickToMax);b&&!a.hasNavigatorData&&(b.options.pointStart=this.xData[0],b.setData(this.options.data,!1,null,!1))},addChartEvents:function(){x(this.chart,"redraw",function(){var a=this.navigator,b=a&&(a.baseSeries&&a.baseSeries[0]&&a.baseSeries[0].xAxis||a.scrollbar&&this.xAxis[0]);b&&a.render(b.min,b.max)})},destroy:function(){this.removeEvents();this.xAxis&&(E(this.chart.xAxis,this.xAxis),E(this.chart.axes,
this.xAxis));this.yAxis&&(E(this.chart.yAxis,this.yAxis),E(this.chart.axes,this.yAxis));n(this.series||[],function(a){a.destroy&&a.destroy()});n("series xAxis yAxis shades outline scrollbarTrack scrollbarRifles scrollbarGroup scrollbar navigatorGroup rendered".split(" "),function(a){this[a]&&this[a].destroy&&this[a].destroy();this[a]=null},this);n([this.handles],function(a){r(a)},this)}};a.Navigator=C;I(z.prototype,"zoom",function(a,b,c){var e=this.chart,h=e.options,d=h.chart.zoomType,f=h.navigator,
h=h.rangeSelector,g;this.isXAxis&&(f&&f.enabled||h&&h.enabled)&&("x"===d?e.resetZoomButton="blocked":"y"===d?g=!1:"xy"===d&&this.options.range&&(e=this.previousZoom,D(b)?this.previousZoom=[this.min,this.max]:e&&(b=e[0],c=e[1],delete this.previousZoom)));return void 0!==g?g:a.call(this,b,c)});I(u.prototype,"init",function(a,b,c){x(this,"beforeRender",function(){var a=this.options;if(a.navigator.enabled||a.scrollbar.enabled)this.scroller=this.navigator=new C(this)});a.call(this,b,c)});I(u.prototype,
"setChartSize",function(a){var b=this.legend,c=this.navigator,e,h,d,f;a.apply(this,[].slice.call(arguments,1));c&&(h=b&&b.options,d=c.xAxis,f=c.yAxis,e=c.scrollbarHeight,this.inverted?(c.left=c.opposite?this.chartWidth-e-c.height:this.spacing[3]+e,c.top=this.plotTop+e):(c.left=this.plotLeft+e,c.top=c.navigatorOptions.top||this.chartHeight-c.height-e-this.spacing[2]-(this.rangeSelector&&this.extraBottomMargin?this.rangeSelector.getHeight():0)-(h&&"bottom"===h.verticalAlign&&h.enabled&&!h.floating?
b.legendHeight+q(h.margin,10):0)),d&&f&&(this.inverted?d.options.left=f.options.left=c.left:d.options.top=f.options.top=c.top,d.setAxisSize(),f.setAxisSize()))});I(h.prototype,"addPoint",function(a,b,c,e,h){var d=this.options.turboThreshold;d&&this.xData.length>d&&m(b,!0)&&this.chart.navigator&&v(20,!0);a.call(this,b,c,e,h)});I(u.prototype,"addSeries",function(a,b,c,e){a=a.call(this,b,!1,e);this.navigator&&this.navigator.setBaseSeries(null,!1);q(c,!0)&&this.redraw();return a});I(h.prototype,"update",
function(a,b,c){a.call(this,b,!1);this.chart.navigator&&!this.options.isInternal&&this.chart.navigator.setBaseSeries(null,!1);q(c,!0)&&this.chart.redraw()});u.prototype.callbacks.push(function(a){var b=a.navigator;b&&(a=a.xAxis[0].getExtremes(),b.render(a.min,a.max))})})(G);(function(a){function C(a){this.init(a)}var x=a.addEvent,z=a.Axis,u=a.Chart,B=a.css,t=a.createElement,D=a.defaultOptions,r=a.defined,n=a.destroyObjectProperties,E=a.discardElement,v=a.each,b=a.extend,f=a.fireEvent,d=a.isNumber,
k=a.merge,g=a.pick,m=a.pInt,l=a.splat,q=a.wrap;b(D,{rangeSelector:{verticalAlign:"top",buttonTheme:{"stroke-width":0,width:28,height:18,padding:2,zIndex:7},floating:!1,x:0,y:0,height:void 0,inputPosition:{align:"right",x:0,y:0},buttonPosition:{align:"left",x:0,y:0},labelStyle:{color:"#666666"}}});D.lang=k(D.lang,{rangeSelectorZoom:"Zoom",rangeSelectorFrom:"From",rangeSelectorTo:"To"});C.prototype={clickButton:function(a,b){var c=this,e=c.chart,f=c.buttonOptions[a],k=e.xAxis[0],m=e.scroller&&e.scroller.getUnionExtremes()||
k||{},p=m.dataMin,w=m.dataMax,y,n=k&&Math.round(Math.min(k.max,g(w,k.max))),q=f.type,r,m=f._range,t,u,W,B=f.dataGrouping;if(null!==p&&null!==w){e.fixedRange=m;B&&(this.forcedDataGrouping=!0,z.prototype.setDataGrouping.call(k||{chart:this.chart},B,!1));if("month"===q||"year"===q)k?(q={range:f,max:n,chart:e,dataMin:p,dataMax:w},y=k.minFromRange.call(q),d(q.newMax)&&(n=q.newMax)):m=f;else if(m)y=Math.max(n-m,p),n=Math.min(y+m,w);else if("ytd"===q)if(k)void 0===w&&(p=Number.MAX_VALUE,w=Number.MIN_VALUE,
v(e.series,function(a){a=a.xData;p=Math.min(a[0],p);w=Math.max(a[a.length-1],w)}),b=!1),n=c.getYTDExtremes(w,p,e.time.useUTC),y=t=n.min,n=n.max;else{x(e,"beforeRender",function(){c.clickButton(a)});return}else"all"===q&&k&&(y=p,n=w);y+=f._offsetMin;n+=f._offsetMax;c.setSelected(a);k?k.setExtremes(y,n,g(b,1),null,{trigger:"rangeSelectorButton",rangeSelectorButton:f}):(r=l(e.options.xAxis)[0],W=r.range,r.range=m,u=r.min,r.min=t,x(e,"load",function(){r.range=W;r.min=u}))}},setSelected:function(a){this.selected=
this.options.selected=a},defaultButtons:[{type:"month",count:1,text:"1m"},{type:"month",count:3,text:"3m"},{type:"month",count:6,text:"6m"},{type:"ytd",text:"YTD"},{type:"year",count:1,text:"1y"},{type:"all",text:"All"}],init:function(a){var b=this,c=a.options.rangeSelector,d=c.buttons||[].concat(b.defaultButtons),g=c.selected,k=function(){var a=b.minInput,c=b.maxInput;a&&a.blur&&f(a,"blur");c&&c.blur&&f(c,"blur")};b.chart=a;b.options=c;b.buttons=[];a.extraTopMargin=c.height;b.buttonOptions=d;this.unMouseDown=
x(a.container,"mousedown",k);this.unResize=x(a,"resize",k);v(d,b.computeButtonRange);void 0!==g&&d[g]&&this.clickButton(g,!1);x(a,"load",function(){a.xAxis&&a.xAxis[0]&&x(a.xAxis[0],"setExtremes",function(c){this.max-this.min!==a.fixedRange&&"rangeSelectorButton"!==c.trigger&&"updatedData"!==c.trigger&&b.forcedDataGrouping&&this.setDataGrouping(!1,!1)})})},updateButtonStates:function(){var a=this.chart,b=a.xAxis[0],h=Math.round(b.max-b.min),f=!b.hasVisibleSeries,g=a.scroller&&a.scroller.getUnionExtremes()||
b,k=g.dataMin,m=g.dataMax,a=this.getYTDExtremes(m,k,a.time.useUTC),p=a.min,l=a.max,n=this.selected,q=d(n),r=this.options.allButtonsEnabled,t=this.buttons;v(this.buttonOptions,function(a,c){var e=a._range,d=a.type,g=a.count||1,w=t[c],A=0;a=a._offsetMax-a._offsetMin;c=c===n;var y=e>m-k,K=e<b.minRange,v=!1,u=!1,e=e===h;("month"===d||"year"===d)&&h+36E5>=864E5*{month:28,year:365}[d]*g-a&&h-36E5<=864E5*{month:31,year:366}[d]*g+a?e=!0:"ytd"===d?(e=l-p+a===h,v=!c):"all"===d&&(e=b.max-b.min>=m-k,u=!c&&q&&
e);d=!r&&(y||K||u||f);g=c&&e||e&&!q&&!v;d?A=3:g&&(q=!0,A=2);w.state!==A&&w.setState(A)})},computeButtonRange:function(a){var b=a.type,c=a.count||1,d={millisecond:1,second:1E3,minute:6E4,hour:36E5,day:864E5,week:6048E5};if(d[b])a._range=d[b]*c;else if("month"===b||"year"===b)a._range=864E5*{month:30,year:365}[b]*c;a._offsetMin=g(a.offsetMin,0);a._offsetMax=g(a.offsetMax,0);a._range+=a._offsetMax-a._offsetMin},setInputValue:function(a,b){var c=this.chart.options.rangeSelector,e=this.chart.time,d=this[a+
"Input"];r(b)&&(d.previousValue=d.HCTime,d.HCTime=b);d.value=e.dateFormat(c.inputEditDateFormat||"%Y-%m-%d",d.HCTime);this[a+"DateBox"].attr({text:e.dateFormat(c.inputDateFormat||"%b %e, %Y",d.HCTime)})},showInput:function(a){var b=this.inputGroup,c=this[a+"DateBox"];B(this[a+"Input"],{left:b.translateX+c.x+"px",top:b.translateY+"px",width:c.width-2+"px",height:c.height-2+"px",border:"2px solid silver"})},hideInput:function(a){B(this[a+"Input"],{border:0,width:"1px",height:"1px"});this.setInputValue(a)},
drawInput:function(a){function c(){var a=q.value,b=(n.inputDateParser||Date.parse)(a),c=f.xAxis[0],e=f.scroller&&f.scroller.xAxis?f.scroller.xAxis:c,g=e.dataMin,e=e.dataMax;b!==q.previousValue&&(q.previousValue=b,d(b)||(b=a.split("-"),b=Date.UTC(m(b[0]),m(b[1])-1,m(b[2]))),d(b)&&(f.time.useUTC||(b+=6E4*(new Date).getTimezoneOffset()),w?b>h.maxInput.HCTime?b=void 0:b<g&&(b=g):b<h.minInput.HCTime?b=void 0:b>e&&(b=e),void 0!==b&&c.setExtremes(w?b:c.min,w?c.max:b,void 0,void 0,{trigger:"rangeSelectorInput"})))}
var h=this,f=h.chart,g=f.renderer.style||{},l=f.renderer,n=f.options.rangeSelector,p=h.div,w="min"===a,q,r,v=this.inputGroup;this[a+"Label"]=r=l.label(D.lang[w?"rangeSelectorFrom":"rangeSelectorTo"],this.inputGroup.offset).addClass("highcharts-range-label").attr({padding:2}).add(v);v.offset+=r.width+5;this[a+"DateBox"]=l=l.label("",v.offset).addClass("highcharts-range-input").attr({padding:2,width:n.inputBoxWidth||90,height:n.inputBoxHeight||17,stroke:n.inputBoxBorderColor||"#cccccc","stroke-width":1,
"text-align":"center"}).on("click",function(){h.showInput(a);h[a+"Input"].focus()}).add(v);v.offset+=l.width+(w?10:0);this[a+"Input"]=q=t("input",{name:a,className:"highcharts-range-selector",type:"text"},{top:f.plotTop+"px"},p);r.css(k(g,n.labelStyle));l.css(k({color:"#333333"},g,n.inputStyle));B(q,b({position:"absolute",border:0,width:"1px",height:"1px",padding:0,textAlign:"center",fontSize:g.fontSize,fontFamily:g.fontFamily,top:"-9999em"},n.inputStyle));q.onfocus=function(){h.showInput(a)};q.onblur=
function(){h.hideInput(a)};q.onchange=c;q.onkeypress=function(a){13===a.keyCode&&c()}},getPosition:function(){var a=this.chart,b=a.options.rangeSelector,a="top"===b.verticalAlign?a.plotTop-a.axisOffset[0]:0;return{buttonTop:a+b.buttonPosition.y,inputTop:a+b.inputPosition.y-10}},getYTDExtremes:function(a,b,d){var c=this.chart.time,e=new c.Date(a),h=c.get("FullYear",e);d=d?c.Date.UTC(h,0,1):+new c.Date(h,0,1);b=Math.max(b||0,d);e=e.getTime();return{max:Math.min(a||e,e),min:b}},render:function(a,b){var c=
this,e=c.chart,d=e.renderer,f=e.container,k=e.options,p=k.exporting&&!1!==k.exporting.enabled&&k.navigation&&k.navigation.buttonOptions,m=D.lang,l=c.div,n=k.rangeSelector,k=n.floating,q=c.buttons,l=c.inputGroup,r=n.buttonTheme,u=n.buttonPosition,x=n.inputPosition,B=n.inputEnabled,z=r&&r.states,C=e.plotLeft,E,M=c.buttonGroup,Q;Q=c.rendered;var R=c.options.verticalAlign,G=e.legend,T=G&&G.options,U=u.y,S=x.y,V=Q||!1,P=0,N=0,O;if(!1!==n.enabled){Q||(c.group=Q=d.g("range-selector-group").attr({zIndex:7}).add(),
c.buttonGroup=M=d.g("range-selector-buttons").add(Q),c.zoomText=d.text(m.rangeSelectorZoom,g(C+u.x,C),15).css(n.labelStyle).add(M),E=g(C+u.x,C)+c.zoomText.getBBox().width+5,v(c.buttonOptions,function(a,b){q[b]=d.button(a.text,E,0,function(){var e=a.events&&a.events.click,d;e&&(d=e.call(a));!1!==d&&c.clickButton(b);c.isActive=!0},r,z&&z.hover,z&&z.select,z&&z.disabled).attr({"text-align":"center"}).add(M);E+=q[b].width+g(n.buttonSpacing,5)}),!1!==B&&(c.div=l=t("div",null,{position:"relative",height:0,
zIndex:1}),f.parentNode.insertBefore(l,f),c.inputGroup=l=d.g("input-group").add(Q),l.offset=0,c.drawInput("min"),c.drawInput("max")));C=e.plotLeft-e.spacing[3];c.updateButtonStates();p&&this.titleCollision(e)&&"top"===R&&"right"===u.align&&u.y+M.getBBox().height-12<(p.y||0)+p.height&&(P=-40);"left"===u.align?O=u.x-e.spacing[3]:"right"===u.align&&(O=u.x+P-e.spacing[1]);M.align({y:u.y,width:M.getBBox().width,align:u.align,x:O},!0,e.spacingBox);c.group.placed=V;c.buttonGroup.placed=V;!1!==B&&(P=p&&this.titleCollision(e)&&
"top"===R&&"right"===x.align&&x.y-l.getBBox().height-12<(p.y||0)+p.height+e.spacing[0]?-40:0,"left"===x.align?O=C:"right"===x.align&&(O=-Math.max(e.axisOffset[1],-P)),l.align({y:x.y,width:l.getBBox().width,align:x.align,x:x.x+O-2},!0,e.spacingBox),f=l.alignAttr.translateX+l.alignOptions.x-P+l.getBBox().x+2,p=l.alignOptions.width,m=M.alignAttr.translateX+M.getBBox().x,O=M.getBBox().width+20,(x.align===u.align||m+O>f&&f+p>m&&U<S+l.getBBox().height)&&l.attr({translateX:l.alignAttr.translateX+(e.axisOffset[1]>=
-P?0:-P),translateY:l.alignAttr.translateY+M.getBBox().height+10}),c.setInputValue("min",a),c.setInputValue("max",b),c.inputGroup.placed=V);c.group.align({verticalAlign:R},!0,e.spacingBox);a=c.group.getBBox().height+20;b=c.group.alignAttr.translateY;"bottom"===R&&(G=T&&"bottom"===T.verticalAlign&&T.enabled&&!T.floating?G.legendHeight+g(T.margin,10):0,a=a+G-20,N=b-a-(k?0:n.y)-10);if("top"===R)k&&(N=0),e.titleOffset&&(N=e.titleOffset+e.options.title.margin),N+=e.margin[0]-e.spacing[0]||0;else if("middle"===
R)if(S===U)N=0>S?b+void 0:b;else if(S||U)N=0>S||0>U?N-Math.min(S,U):b-a+NaN;c.group.translate(n.x,n.y+Math.floor(N));!1!==B&&(c.minInput.style.marginTop=c.group.translateY+"px",c.maxInput.style.marginTop=c.group.translateY+"px");c.rendered=!0}},getHeight:function(){var a=this.options,b=this.group,d=a.y,f=a.buttonPosition.y,a=a.inputPosition.y,b=b?b.getBBox(!0).height+13+d:0,d=Math.min(a,f);if(0>a&&0>f||0<a&&0<f)b+=Math.abs(d);return b},titleCollision:function(a){return!(a.options.title.text||a.options.subtitle.text)},
update:function(a){var b=this.chart;k(!0,b.options.rangeSelector,a);this.destroy();this.init(b);b.rangeSelector.render()},destroy:function(){var b=this,e=b.minInput,d=b.maxInput;b.unMouseDown();b.unResize();n(b.buttons);e&&(e.onfocus=e.onblur=e.onchange=null);d&&(d.onfocus=d.onblur=d.onchange=null);a.objectEach(b,function(a,c){a&&"chart"!==c&&(a.destroy?a.destroy():a.nodeType&&E(this[c]));a!==C.prototype[c]&&(b[c]=null)},this)}};z.prototype.toFixedRange=function(a,b,f,k){var c=this.chart&&this.chart.fixedRange;
a=g(f,this.translate(a,!0,!this.horiz));b=g(k,this.translate(b,!0,!this.horiz));f=c&&(b-a)/c;.7<f&&1.3>f&&(k?a=b-c:b=a+c);d(a)&&d(b)||(a=b=void 0);return{min:a,max:b}};z.prototype.minFromRange=function(){var a=this.range,b={month:"Month",year:"FullYear"}[a.type],f,k=this.max,l,m,n=function(a,c){var d=new Date(a),e=d["get"+b]();d["set"+b](e+c);e===d["get"+b]()&&d.setDate(0);return d.getTime()-a};d(a)?(f=k-a,m=a):(f=k+n(k,-a.count),this.chart&&(this.chart.fixedRange=k-f));l=g(this.dataMin,Number.MIN_VALUE);
d(f)||(f=l);f<=l&&(f=l,void 0===m&&(m=n(f,a.count)),this.newMax=Math.min(f+m,this.dataMax));d(k)||(f=void 0);return f};q(u.prototype,"init",function(a,b,d){x(this,"init",function(){this.options.rangeSelector.enabled&&(this.rangeSelector=new C(this))});a.call(this,b,d)});q(u.prototype,"render",function(a,b,d){var c=this.axes,e=this.rangeSelector;e&&(v(c,function(a){a.updateNames();a.setScale()}),this.getAxisMargins(),e.render(),c=e.options.verticalAlign,e.options.floating||("bottom"===c?this.extraBottomMargin=
!0:"middle"!==c&&(this.extraTopMargin=!0)));a.call(this,b,d)});q(u.prototype,"update",function(b,d,f,k){var c=this.rangeSelector,e;this.extraTopMargin=this.extraBottomMargin=!1;c&&(c.render(),e=d.rangeSelector&&d.rangeSelector.verticalAlign||c.options&&c.options.verticalAlign,c.options.floating||("bottom"===e?this.extraBottomMargin=!0:"middle"!==e&&(this.extraTopMargin=!0)));b.call(this,a.merge(!0,d,{chart:{marginBottom:g(d.chart&&d.chart.marginBottom,this.margin.bottom),spacingBottom:g(d.chart&&
d.chart.spacingBottom,this.spacing.bottom)}}),f,k)});q(u.prototype,"redraw",function(a,b,d){var c=this.rangeSelector;c&&!c.options.floating&&(c.render(),c=c.options.verticalAlign,"bottom"===c?this.extraBottomMargin=!0:"middle"!==c&&(this.extraTopMargin=!0));a.call(this,b,d)});u.prototype.adjustPlotArea=function(){var a=this.rangeSelector;this.rangeSelector&&(a=a.getHeight(),this.extraTopMargin&&(this.plotTop+=a),this.extraBottomMargin&&(this.marginBottom+=a))};u.prototype.callbacks.push(function(a){function b(){c=
a.xAxis[0].getExtremes();d(c.min)&&f.render(c.min,c.max)}var c,f=a.rangeSelector,g,k;f&&(k=x(a.xAxis[0],"afterSetExtremes",function(a){f.render(a.min,a.max)}),g=x(a,"redraw",b),b());x(a,"destroy",function(){f&&(g(),k())})});a.RangeSelector=C})(G);(function(a){var C=a.arrayMax,x=a.arrayMin,z=a.Axis,u=a.Chart,B=a.defined,t=a.each,D=a.extend,r=a.format,n=a.grep,E=a.inArray,v=a.isNumber,b=a.isString,f=a.map,d=a.merge,k=a.pick,g=a.Point,m=a.Renderer,l=a.Series,q=a.splat,c=a.SVGRenderer,e=a.VMLRenderer,
h=a.wrap,A=l.prototype,G=A.init,F=A.processData,L=g.prototype.tooltipFormatter;a.StockChart=a.stockChart=function(c,e,g){var h=b(c)||c.nodeName,l=arguments[h?1:0],m=l.series,p=a.getOptions(),n,w=k(l.navigator&&l.navigator.enabled,p.navigator.enabled,!0),r=w?{startOnTick:!1,endOnTick:!1}:null,t={marker:{enabled:!1,radius:2}},v={shadow:!1,borderWidth:0};l.xAxis=f(q(l.xAxis||{}),function(a,b){return d({minPadding:0,maxPadding:0,overscroll:0,ordinal:!0,title:{text:null},labels:{overflow:"justify"},showLastLabel:!0},
p.xAxis,p.xAxis&&p.xAxis[b],a,{type:"datetime",categories:null},r)});l.yAxis=f(q(l.yAxis||{}),function(a,b){n=k(a.opposite,!0);return d({labels:{y:-2},opposite:n,showLastLabel:!(!a.categories&&"category"!==a.type),title:{text:null}},p.yAxis,p.yAxis&&p.yAxis[b],a)});l.series=null;l=d({chart:{panning:!0,pinchType:"x"},navigator:{enabled:w},scrollbar:{enabled:k(p.scrollbar.enabled,!0)},rangeSelector:{enabled:k(p.rangeSelector.enabled,!0)},title:{text:null},tooltip:{split:k(p.tooltip.split,!0),crosshairs:!0},
legend:{enabled:!1},plotOptions:{line:t,spline:t,area:t,areaspline:t,arearange:t,areasplinerange:t,column:v,columnrange:v,candlestick:v,ohlc:v}},l,{isStock:!0});l.series=m;return h?new u(c,l,g):new u(l,e)};h(z.prototype,"autoLabelAlign",function(a){var b=this.chart,c=this.options,b=b._labelPanes=b._labelPanes||{},d=this.options.labels;return this.chart.options.isStock&&"yAxis"===this.coll&&(c=c.top+","+c.height,!b[c]&&d.enabled)?(15===d.x&&(d.x=0),void 0===d.align&&(d.align="right"),b[c]=this,"right"):
a.apply(this,[].slice.call(arguments,1))});h(z.prototype,"destroy",function(a){var b=this.chart,c=this.options&&this.options.top+","+this.options.height;c&&b._labelPanes&&b._labelPanes[c]===this&&delete b._labelPanes[c];return a.apply(this,Array.prototype.slice.call(arguments,1))});h(z.prototype,"getPlotLinePath",function(c,d,e,g,h,l){var p=this,m=this.isLinked&&!this.series?this.linkedParent.series:this.series,n=p.chart,q=n.renderer,w=p.left,r=p.top,u,y,x,z,A=[],C=[],H,D;if("xAxis"!==p.coll&&"yAxis"!==
p.coll)return c.apply(this,[].slice.call(arguments,1));C=function(a){var c="xAxis"===a?"yAxis":"xAxis";a=p.options[c];return v(a)?[n[c][a]]:b(a)?[n.get(a)]:f(m,function(a){return a[c]})}(p.coll);t(p.isXAxis?n.yAxis:n.xAxis,function(a){if(B(a.options.id)?-1===a.options.id.indexOf("navigator"):1){var b=a.isXAxis?"yAxis":"xAxis",b=B(a.options[b])?n[b][a.options[b]]:n[b][0];p===b&&C.push(a)}});H=C.length?[]:[p.isXAxis?n.yAxis[0]:n.xAxis[0]];t(C,function(b){-1!==E(b,H)||a.find(H,function(a){return a.pos===
b.pos&&a.len&&b.len})||H.push(b)});D=k(l,p.translate(d,null,null,g));v(D)&&(p.horiz?t(H,function(a){var b;y=a.pos;z=y+a.len;u=x=Math.round(D+p.transB);if(u<w||u>w+p.width)h?u=x=Math.min(Math.max(w,u),w+p.width):b=!0;b||A.push("M",u,y,"L",x,z)}):t(H,function(a){var b;u=a.pos;x=u+a.len;y=z=Math.round(r+p.height-D);if(y<r||y>r+p.height)h?y=z=Math.min(Math.max(r,y),p.top+p.height):b=!0;b||A.push("M",u,y,"L",x,z)}));return 0<A.length?q.crispPolyLine(A,e||1):null});c.prototype.crispPolyLine=function(a,
b){var c;for(c=0;c<a.length;c+=6)a[c+1]===a[c+4]&&(a[c+1]=a[c+4]=Math.round(a[c+1])-b%2/2),a[c+2]===a[c+5]&&(a[c+2]=a[c+5]=Math.round(a[c+2])+b%2/2);return a};m===e&&(e.prototype.crispPolyLine=c.prototype.crispPolyLine);h(z.prototype,"hideCrosshair",function(a,b){a.call(this,b);this.crossLabel&&(this.crossLabel=this.crossLabel.hide())});h(z.prototype,"drawCrosshair",function(a,b,c){var d,e;a.call(this,b,c);if(B(this.crosshair.label)&&this.crosshair.label.enabled&&this.cross){a=this.chart;var f=this.options.crosshair.label,
g=this.horiz;d=this.opposite;e=this.left;var h=this.top,l=this.crossLabel,m,p=f.format,n="",q="inside"===this.options.tickPosition,t=!1!==this.crosshair.snap,w=0;b||(b=this.cross&&this.cross.e);m=g?"center":d?"right"===this.labelAlign?"right":"left":"left"===this.labelAlign?"left":"center";l||(l=this.crossLabel=a.renderer.label(null,null,null,f.shape||"callout").addClass("highcharts-crosshair-label"+(this.series[0]&&" highcharts-color-"+this.series[0].colorIndex)).attr({align:f.align||m,padding:k(f.padding,
8),r:k(f.borderRadius,3),zIndex:2}).add(this.labelGroup),l.attr({fill:f.backgroundColor||this.series[0]&&this.series[0].color||"#666666",stroke:f.borderColor||"","stroke-width":f.borderWidth||0}).css(D({color:"#ffffff",fontWeight:"normal",fontSize:"11px",textAlign:"center"},f.style)));g?(m=t?c.plotX+e:b.chartX,h+=d?0:this.height):(m=d?this.width+e:0,h=t?c.plotY+h:b.chartY);p||f.formatter||(this.isDatetimeAxis&&(n="%b %d, %Y"),p="{value"+(n?":"+n:"")+"}");b=t?c[this.isXAxis?"x":"y"]:this.toValue(g?
b.chartX:b.chartY);l.attr({text:p?r(p,{value:b},a.time):f.formatter.call(this,b),x:m,y:h,visibility:b<this.min||b>this.max?"hidden":"visible"});b=l.getBBox();if(g){if(q&&!d||!q&&d)h=l.y-b.height}else h=l.y-b.height/2;g?(d=e-b.x,e=e+this.width-b.x):(d="left"===this.labelAlign?e:0,e="right"===this.labelAlign?e+this.width:a.chartWidth);l.translateX<d&&(w=d-l.translateX);l.translateX+b.width>=e&&(w=-(l.translateX+b.width-e));l.attr({x:m+w,y:h,anchorX:g?m:this.opposite?0:a.chartWidth,anchorY:g?this.opposite?
a.chartHeight:0:h+b.height/2})}});A.init=function(){G.apply(this,arguments);this.setCompare(this.options.compare)};A.setCompare=function(a){this.modifyValue="value"===a||"percent"===a?function(b,c){var d=this.compareValue;if(void 0!==b&&void 0!==d)return b="value"===a?b-d:b/d*100-(100===this.options.compareBase?0:100),c&&(c.change=b),b}:null;this.userOptions.compare=a;this.chart.hasRendered&&(this.isDirty=!0)};A.processData=function(){var a,b=-1,c,d,e=!0===this.options.compareStart?0:1,f,g;F.apply(this,
arguments);if(this.xAxis&&this.processedYData)for(c=this.processedXData,d=this.processedYData,f=d.length,this.pointArrayMap&&(b=E("close",this.pointArrayMap),-1===b&&(b=E(this.pointValKey||"y",this.pointArrayMap))),a=0;a<f-e;a++)if(g=d[a]&&-1<b?d[a][b]:d[a],v(g)&&c[a+e]>=this.xAxis.min&&0!==g){this.compareValue=g;break}};h(A,"getExtremes",function(a){var b;a.apply(this,[].slice.call(arguments,1));this.modifyValue&&(b=[this.modifyValue(this.dataMin),this.modifyValue(this.dataMax)],this.dataMin=x(b),
this.dataMax=C(b))});z.prototype.setCompare=function(a,b){this.isXAxis||(t(this.series,function(b){b.setCompare(a)}),k(b,!0)&&this.chart.redraw())};g.prototype.tooltipFormatter=function(b){b=b.replace("{point.change}",(0<this.change?"+":"")+a.numberFormat(this.change,k(this.series.tooltipOptions.changeDecimals,2)));return L.apply(this,[b])};h(l.prototype,"render",function(a){this.chart.is3d&&this.chart.is3d()||this.chart.polar||!this.xAxis||this.xAxis.isRadial||(!this.clipBox&&this.animate?(this.clipBox=
d(this.chart.clipBox),this.clipBox.width=this.xAxis.len,this.clipBox.height=this.yAxis.len):this.chart[this.sharedClipKey]?this.chart[this.sharedClipKey].attr({width:this.xAxis.len,height:this.yAxis.len}):this.clipBox&&(this.clipBox.width=this.xAxis.len,this.clipBox.height=this.yAxis.len));a.call(this)});h(u.prototype,"getSelectedPoints",function(a){var b=a.call(this);t(this.series,function(a){a.hasGroupedData&&(b=b.concat(n(a.points||[],function(a){return a.selected})))});return b});h(u.prototype,
"update",function(a,b){"scrollbar"in b&&this.navigator&&(d(!0,this.options.scrollbar,b.scrollbar),this.navigator.update({},!1),delete b.scrollbar);return a.apply(this,Array.prototype.slice.call(arguments,1))})})(G)});
