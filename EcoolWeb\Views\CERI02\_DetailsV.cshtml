﻿@model HRMT01

@using (Html.BeginCollectionItem("oTeachers"))
{
    var Index = Html.GetIndex("oTeachers");

    <li class="list-group-item clearfix" id="Tr@(Index)">
        <a class="btn btn-xs btn-Basic" role="button" onclick="deleteRow('Tr@(Index)')">
            <span class="glyphicon glyphicon-remove" aria-hidden="true"></span>
        </a>
        <div class="row">
            <div class="col-sm-10">
                @Html.HiddenFor(m => m.USER_KEY)
                @Html.HiddenFor(m => m.SCHOOL_NO)
                @Html.HiddenFor(m => m.USER_NO)
                @Html.HiddenFor(m => m.NAME)
                <span>@Model.NAME</span>
                <span style="color:red">(字數最多十五個字)</span>
            </div>
        </div>
    </li>

}