/*!
 * jQuery Conveyor Ticker (jConveyorTicker)
 * Description: jQuery plugin to create simple horizontal conveyor belt animated tickers.
 *
 * Copyright (c) 2017 <PERSON>XD Lda
 *
 * Licensed under the MIT license:
 *   http://www.opensource.org/licenses/mit-license.php
 *
 * Project home:
 *   https://github.com/lluz/jquery-conveyor-ticker
 *
 * Version:  1.1.0
 *
 */
.jctkr-wrapper,
.jctkr-wrapper * {
  box-sizing: border-box;
}
.jctkr-wrapper {
  display: inline-block;
  position: relative;
  width: 100%;
  height:30px;
  vertical-align: top;
  overflow: hidden;
}
.jctkr-wrapper ul {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  margin: 0;
  padding: 0;
  list-style: none;
  white-space: nowrap;
  font-size: 0;
  text-align: left;
  opacity: 0;
  -webkit-transition: opacity 1s;
  transition: opacity 1s;
}
.jctkr-wrapper.jctkr-initialized ul {
  opacity: 1;
}
.jctkr-wrapper ul li {
  display: inline-block;
  font-size: .725rem;
}
.jctkr-label {
  display: inline-block;
}
