﻿@model ADDI10EditViewModel
@{
    ViewBag.Title = ViewBag.Panel_Title;

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }

    if (Model.StudentCardVote == true)
    {
        Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
    }

}

<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>

@if (Model.StudentCardVote == false)
{
    @Html.Partial("_Title_Secondary")
}

@Html.Partial("_Notice")
<div class="row">
    <div class="@(Model.StudentCardVote==true ? "col-md-8 col-md-offset-2":"col-md-12")">

        @using (Html.BeginForm("EditSave", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "formEdit", name = "formEdit", enctype = "multipart/form-data", @AutoComplete = "Off" }))
        {
            @Html.AntiForgeryToken()
            @Html.HiddenFor(m => m.Search.OrdercColumn)
            @Html.HiddenFor(m => m.Search.SyntaxName)
            @Html.HiddenFor(m => m.Search.Page)
            @Html.HiddenFor(m => m.Search.WhereQUESTIONNAIRE_ID)
            @Html.HiddenFor(m => m.Search.WhereSearch)
            @Html.HiddenFor(m => m.Search.BackAction)
            @Html.HiddenFor(m => m.Search.BackController)
            @Html.HiddenFor(m => m.StudentCardVote)

            if (Model.StudentCardVote == false)
            {
                <button class="btn btn-sm btn-sys" type="button" onclick="onBack()">回投票系統列表</button>
            }

            <div id="StudentCard" style="display:none">
                <div class="panel with-nav-tabs panel-info" id="panel">
                    <div class="panel-heading">
                        <h1>@Html.DisplayFor(m => m.Title.QUESTIONNAIRE_NAME)  <font color="#FF0000">我要用數位學生證投票 </font></h1>
                    </div>
                    <div class="panel-body">
                        <div >
                            <div class="input-group input-group-lg">
                                <span class="input-group-addon"><i class="fa fa-user"></i></span>
                                @Html.Editor("CARD_NO", new { htmlAttributes = new { @class = "form-control", @placeholder = "請用數位學生證感應", @onKeyPress = "call(event,this);" } })
                            </div>
                            @Html.ValidationMessage("CARD_NO", "", new { @class = "text-danger" })
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-xs-12 text-center">
                            <div class="form-inline">
                                <button type="button" id="clearform" class="btn btn-default" onclick="onGoVoteDiv()">
                                    不要投票了，回上一頁
                                </button>
                                @*<a class="btn btn-default" href="@Url.Action("Index","ADDI10")">回投票入口-列表</a>*@
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div id="VoteDiv">
                <img src="~/Content/img/web-bar-vote.png" class="img-responsive" alt="Responsive image" />
                <div class="Div-EZ-reader">
                    <div class="form-horizontal">
                        <div class="Details">
                            <div class="dl-horizontal-EZ">
                                <samp class="dt">
                                    @Html.DisplayNameFor(m => m.Title.QUESTIONNAIRE_NAME)
                                </samp>
                                <samp class="dd">
                                    @Html.DisplayFor(m => m.Title.QUESTIONNAIRE_NAME)
                                </samp>
                            </div>
                            <div class="dl-horizontal-EZ">
                                <samp class="dt">
                                    @Html.DisplayNameFor(m => m.Title.QUESTIONNAIRE_SDATE)
                                </samp>
                                <samp class="dd">
                                    @{
                                        string STime = "";
                                        STime = ((DateTime)Model.Title.QUESTIONNAIRE_SDATE).ToShortTimeString();
                                    
                                        }
                                    @Html.DisplayFor(m => m.Title.QUESTIONNAIRE_SDATE)    @STime
                                </samp>
                
                                            </div>
                
                            <div class="dl-horizontal-EZ">
                                @{
                                    string ETime = "";
                                    ETime = ((DateTime)Model.Title.QUESTIONNAIRE_EDATE).ToShortTimeString();

                                }
                                <samp class="dt">


                                    @Html.DisplayNameFor(m => m.Title.QUESTIONNAIRE_EDATE)
                                </samp>
                                <samp class="dd">
                                    @Html.DisplayFor(m => m.Title.QUESTIONNAIRE_EDATE)    @ETime
                                </samp>
                            </div>
                            @if ((Model.Title.CASH ?? 0) > 0)
                            {
                                <div class="dl-horizontal-EZ">
                                    <samp class="dt">
                                        @Html.DisplayNameFor(m => m.Title.CASH)
                                    </samp>
                                    <samp class="dd">
                                        @Html.DisplayFor(m => m.Title.CASH)
                                    </samp>
                                </div>
                            }
                            <div class="dl-horizontal-EZ">
                                <samp class="dt">
                                    詳細活動內容
                                </samp>
                            </div>
                            <div style="height:15px"></div>
                            <div class="p-context">
                                @Html.Raw(HttpUtility.HtmlDecode(Model.Title.QUESTIONNAIRE_DESC))
                            </div>
                            <div style="height:15px"></div>
                            <div style="height:1px;background-color:#DDDDDD;width:99%"></div>
                        </div>
                        <div style="padding-left:10px">
                            <div>
                                <div>
                                    @if (Model.Topic != null && Model.Topic.Count() > 0)
                                    {
                                        int InputNum = 0;

                                        foreach (var item in Model.Topic)
                                        {
                                            if (item.Q_TYPE != SAQT02.Q_TYPEVal.IsText)
                                            {
                                                @Html.HiddenFor(m => m.Topic[InputNum].Q_NUM)

                                                <div class="label_dt_S" style="font-size:16px">
                                                    <samp>Q@(InputNum + 1).</samp>
                                                    @{ string str = "";}
                                                    @if (item.Topic_D.FirstOrDefault().Q_INPUT_TYPE == SAQT03.Q_INPUT_TYPEVal.UploadWorld)
                                                    {
                                                        str = "(請上傳一個10M以下的Word 檔)";

                                                    }
                                                    @if (item.Topic_D.FirstOrDefault().Q_INPUT_TYPE == SAQT03.Q_INPUT_TYPEVal.UploadImage)
                                                    {
                                                        str = "(請上傳一個10M以下的圖片檔)";

                                                    }
                                                    @if (item.Topic_D.FirstOrDefault().Q_INPUT_TYPE == SAQT03.Q_INPUT_TYPEVal.UploadPdf)
                                                    {
                                                        str = "(請一個6M以下的Pdf檔)";

                                                    }
                                                    @Html.Raw(HttpUtility.HtmlDecode(item.Q_SUBJECT) + str)


                                                    @if (item.Q_MUTIPLE_CHOICES_OF_NUM != null)
                                                    {
                                                        <font color="red">(PS.必需勾選@(item.Q_MUTIPLE_CHOICES_OF_NUM)筆)</font>
                                                    }
                                                </div>

                                                <div style="height:12px"></div>

                                                <div>
                                                    <div class="col-md-12">
                                                        @foreach (var D_item in item.Topic_D)
                                                        {
                                                            if (item.Q_TYPE == SAQT02.Q_TYPEVal.IsInput)
                                                            {
                                                                if ((D_item.Q_INPUT_TYPE == SAQT03.Q_INPUT_TYPEVal.checkbox || D_item.Q_INPUT_TYPE == SAQT03.Q_INPUT_TYPEVal.radio)
                                                                    && Model.StudentCardVote == true)
                                                                {

                                                                    string ThisAns = "," + (item.ANSWER ?? "") + ",";
                                                                    string ThisQ_VAL = "," + D_item.Q_VAL + ",";

                                                                    string ThisId = string.Format("Topic{0}_ArrANSWER_{1}", InputNum, D_item.Q_VAL);
                                                                    string Thisname = string.Format("Topic[{0}].ArrANSWER", InputNum);

                                                                    <div class="btn-group btn-group" data-toggle="buttons">

                                                                        @if (D_item.Q_INPUT_TYPE == SAQT03.Q_INPUT_TYPEVal.checkbox)
                                                                        {
                                                                            <label class="btn @(ThisAns.IndexOf(ThisQ_VAL) >= 0 ? "active" : "")">
                                                                                <input name="@Thisname" id="@ThisId" value="@D_item.Q_VAL" type="@(D_item.Q_INPUT_TYPE)" autocomplete="off" @(ThisAns.IndexOf(ThisQ_VAL) >= 0 ? "checked" : "")>
                                                                                <i class="fa fa-square-o fa-2x"></i><i class="fa fa-check-square-o fa-2x"></i>
                                                                            </label>
                                                                        }

                                                                        else
                                                                        { @*@ThisQ_VAL  @(ThisAns.IndexOf(ThisQ_VAL))*@

                                                                        <label class="btn @(ThisAns.IndexOf(ThisQ_VAL) >= 0 ? "active" : "")" onclick="removeObj(this)">
                                                                            <input name="@Thisname" id="@ThisId" value="@D_item.Q_VAL" type="@(D_item.Q_INPUT_TYPE)" autocomplete="off" @(ThisAns.IndexOf(ThisQ_VAL) >= 0 ? "checked" : "")>
                                                                            <i class="fa fa-circle-o fa-2x"></i><i class="fa fa-dot-circle-o fa-2x"></i>
                                                                        </label>

                                                                    }

                                                                        <span style="color:black;font-size:1.5em;">
                                                                            @Html.Raw(HttpUtility.HtmlDecode(D_item.Q_VAL))
                                                                        </span>
                                                                    </div>
                                                                    <br />

                                                                }
                                                                else if (D_item.Q_INPUT_TYPE == SAQT03.Q_INPUT_TYPEVal.UploadWorld)
                                                                {

                                                                    D_item.Q_ACCEPT = ".doc,.docx";
                                                                    @SAQT03.CreInputHtml(D_item, item.ANSWER, item.Q_MUST, InputNum, null)
                                                                    <span style="color:red">上傳word檔案(.doc,.docx)不能超過10MB</span>
                                                                }
                                                                else if (D_item.Q_INPUT_TYPE == SAQT03.Q_INPUT_TYPEVal.UploadImage)
                                                                {
                                                                    D_item.Q_ACCEPT = ".jpg,.jpeg,.png";
                                                                    @SAQT03.CreInputHtml(D_item, item.ANSWER, item.Q_MUST, InputNum, null)
                                                                    <span style="color:red">圖片上傳後，會自動等比例縮小成(大圖寬度1600px，小圖寬度200px)</span>
                                                                }

                                                                else if (D_item.Q_INPUT_TYPE == SAQT03.Q_INPUT_TYPEVal.UploadPdf)
                                                                {
                                                                    D_item.Q_ACCEPT = ".jpg,.jpeg,.png,.pdf";
                                                                    @SAQT03.CreInputHtml(D_item, item.ANSWER, item.Q_MUST, InputNum, null)
                                                                    <span style="color:red">上傳pdf檔案(.pdf)不能超過6MB</span>
                                                                }
                                                                else if (D_item.Q_INPUT_TYPE == SAQT03.Q_INPUT_TYPEVal.text)
                                                                {
                                                                    @SAQT03.CreInputHtml(D_item, item.ANSWER, 1 , InputNum, null)
                                                                }
                                                                else
                                                                {
                                                                    @SAQT03.CreInputHtml(D_item, item.ANSWER, item.Q_MUST, InputNum, null)
                                                                }

                                                            }
                                                            else if (item.Q_TYPE == SAQT02.Q_TYPEVal.IsStudentMenu)
                                                            {
                                                                <div id="StudentMenu@(InputNum)">
                                                                    @Html.Action("_Index", "StudentMenu", new { Index = InputNum })
                                                                </div>
                                                            }

                                                            else
                                                            {
                                                                @SAQT03.CreSelectHtml(item.Topic_D, item.ANSWER, item.Q_MUST, InputNum, null)
                                                            }

                                                        }
                                                        @Html.ValidationMessageFor(m => m.Topic[InputNum].ANSWER, "", new { @class = "text-danger" })
                                                    </div>
                                                </div>

                                            }

                                            @Html.HiddenFor(m => m.Topic[InputNum].Index, new { @Value = InputNum })
                                            InputNum = InputNum + 1;
                                            <br />
                                        }
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div style="height:25px"></div>
                <div class="text-center">
                    @if (Model.StudentCardVote == true)
                    {
                        <a style="margin: -15px 0px;" role="button" class="btn btn-success" onclick="onStudentCard()">選好了，我要數位學生證投票</a>
                        <a style="margin: -15px 0px;" class="btn btn-default" href="@Url.Action("Index","ADDI10")">回投票入口-列表</a>
                    }
                    else
                    {
                        <a style="margin: -15px 0px;" role="button" class="btn btn-default" onclick="onEdit()">投票</a>
                    }
                </div>
            </div>

        }
    </div>
</div>

<div class="row">
    <div class="col-lg-6 col-lg-offset-3">
        <div class="use-absolute" id="ErrorDiv">
            <div class="use-absoluteDiv">
                <div class="alert alert-danger" role="alert">
                    <h1>
                        <i class="fa fa-exclamation-circle"></i>
                        <strong id="ErrorStr" style="font-size:50px"></strong>
                    </h1>
                </div>
            </div>
        </div>
    </div>
</div>

@section scripts{
    <script src="~/Scripts/buzz/buzz.min.js"></script>
    <script type="text/javascript">
        var targetFormID = '#formEdit';

         var SwipeSound = new buzz.sound("@Url.Content("~/Content/mp3/Swipe1.mp3")" );

        var StudentCardVote = $("#@Html.IdFor(m=>m.StudentCardVote)").val()

        if (StudentCardVote.toLowerCase() == 'true') {
            $('#StatusMessageDiv').hide()
            var StatusMessageHtmlMsghtml = $("#StatusMessageHtmlMsg").html();

            if (StatusMessageHtmlMsghtml != '' && StatusMessageHtmlMsghtml != undefined) {

                    $('#ErrorDiv').show()
                    $('#ErrorStr').html(StatusMessageHtmlMsghtml)

                    setTimeout(function () {
                    $('#CARD_NO').prop('readonly', false);
                    $('#CARD_NO').val('')
                    $("#CARD_NO").focus();
                    $('#ErrorDiv').hide()
                    $('#ErrorStr').html('');
                        }, 3400);
            }
        }

        $(document).ready(function () {

            var SysUrl = 'http://' + '@Request.Url.Authority' + '@Request.ApplicationPath'

        $(".form-horizontal img").each(function () {

            var ThisSrc = $(this).attr("src")

            if (ThisSrc.indexOf("http") != 0) {
                $(this).attr("href", ThisSrc);
            }
            else if (ThisSrc.indexOf("data:image") != 0) {
                $(this).attr("href", ThisSrc);
            }
            else {
                $(this).attr("href", SysUrl + ThisSrc);
            }
        });

         $(".form-horizontal img").colorbox({ photo: true });
    });

    function onEdit() {
        $(targetFormID).submit();
    }

    function onBack() {
        $(targetFormID).attr("action", "@Url.Action("Index", (string)ViewBag.BRE_NO)")
        $(targetFormID).submit();
        }

    function onGoVoteDiv() {
        $('#VoteDiv').show();
        $('#StudentCard').hide()
    }

        function onStudentCard() {
            var data = {
                "Search.WhereQUESTIONNAIRE_ID": $("#Search_WhereQUESTIONNAIRE_ID").val(),
            };
              $.ajax({
            url: '@Url.Action("GetVoteDetails", (string)ViewBag.BRE_NO)',
            type: 'post',
             data: $("#formEdit").serialize(),
            dataType: 'json',               // xml/json/script/html
            cache: false,
            success: function (data) {

                var res = jQuery.parseJSON(data);
                console.log(res);
                if (res.Success.toLowerCase() == 'false') {

                    //$('#ErrorDiv').show()
                    //$('#ErrorStr').html(res.Error)
                    $(targetFormID).submit();
                    setTimeout(function () {
                        $('#CARD_NO').prop('readonly', false);
                        $('#CARD_NO').val('')
                        $("#CARD_NO").focus();
                        $('#ErrorDiv').hide()
                        $('#ErrorStr').html('');
                         }, 2000);
                }
                else {

                   $('#VoteDiv').hide();
          $('#StudentCard').show()
     $("#CARD_NO").focus();

                }
            }
        });

        }
        function removeObj(obj)
        {
            var classAttr = "";
            classAttr = $(obj).attr("class", "btn");
            //console.log(classAttr);
        }
    function call(e, input) {
        var code = (e.keyCode ? e.keyCode : e.which);

        if (code == 13) // 13 是 Enter 按鍵的值
        {
            var CARD_NO = $('#CARD_NO').val();

            event.preventDefault();

            if (CARD_NO != '') {

                 $('#CARD_NO').prop('readonly', true);

                setTimeout(function () {
                    OnKeyinUse(CARD_NO)
                });

            }
        }
        }

     function SwipeOK() {
         SwipeSound.play();
     }

    function OnKeyinUse(CARD_NO) {

        var data = {
            "CARD_NO": CARD_NO,
        };

        $.ajax({
            url: '@Url.Action("CheckCardNo", (string)ViewBag.BRE_NO)',
            type: 'post',
            data: data,
            dataType: 'json',               // xml/json/script/html
            cache: false,
            success: function (data) {

                var res = jQuery.parseJSON(data);

                if (res.Success.toLowerCase() == 'false') {

                    $('#ErrorDiv').show()
                    $('#ErrorStr').html(res.Error)

                    setTimeout(function () {
                        $('#CARD_NO').prop('readonly', false);
                        $('#CARD_NO').val('')
                        $("#CARD_NO").focus();
                        $('#ErrorDiv').hide()
                        $('#ErrorStr').html('');
                         }, 2000);
                }
                else {

                    SwipeOK()
                $('#ErrorDiv').show()
                    $('#ErrorStr').html("投票成功")
                     setTimeout(function () {
                       $(targetFormID).submit();
                     }, 2500);

                }
            }
        });
    }
    </script>
}