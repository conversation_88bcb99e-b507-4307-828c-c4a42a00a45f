﻿$(document).ready(function () {
    const PassModule = {
        targetFormID:"#form1",
    init: function() { },
        bindEvent: function () { },
        setupGlobalFunctio: function () { },
        onGo: function (actionVal) {

            return ADDI05Common.onGo(actionVal);
        },
        todoClear: function () {
            if (ADDI05Common.todoClear(this.targetFormID, `${this.targetFormID}DivSearch :input,:selected`)) {

                return this.FunPageProc(1);
            }
        },
        FunPageProc: function (pageno) {

            return ADDI05Common.FunPageProc(pageno,'Page');
        },
        doSort: function (sortCol) {

            return ADDI05Common.doSort(sortCol,'OrderRank');
        },
        ToExcel: function () {


        }
    }

    PassModule.init();
})