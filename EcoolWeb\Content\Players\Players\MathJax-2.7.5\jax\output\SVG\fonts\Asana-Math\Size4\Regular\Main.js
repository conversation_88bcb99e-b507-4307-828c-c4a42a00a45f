/*
 *  /MathJax/jax/output/SVG/fonts/Asana-Math/Size4/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax.SVG.FONTDATA.FONTS.AsanaMathJax_Size4={directory:"Size4/Regular",family:"AsanaMathJax_Size4",id:"ASANAMATHSIZE4",32:[0,0,249,0,0,""],124:[1428,887,272,86,187,"187 -887h-101v2315h101v-2315"],770:[783,-627,2017,0,2017,"2017 627l-1009 93l-1008 -93v47l1008 109l1009 -109v-47"],771:[772,-642,1864,0,1865,"1865 772c-71 -99 -384 -130 -597 -130c-226 0 -626 67 -814 67c-138 0 -367 -10 -412 -67h-42c71 99 384 130 597 130c222 0 619 -67 807 -67c138 0 374 10 420 67h41"],774:[664,-506,1761,0,1762,"0 664h190c19 -57 307 -98 691 -98s672 41 691 98h190c-18 -44 -54 -66 -153 -93c-149 -42 -413 -65 -728 -65c-334 0 -604 25 -756 71c-79 26 -107 46 -125 87"],780:[792,-627,1959,0,1960,"1960 792l-980 -101l-980 101v-47l979 -118l981 118v47"],8214:[1428,887,553,86,468,"468 -887h-101v2315h101v-2315zM187 -887h-101v2315h101v-2315"],8260:[1234,955,564,-254,568,"568 1231l-689 -2186h-133l689 2189"],8406:[790,-519,3579,0,3579,"3579 636h-3507l79 -96l-21 -21l-130 135l130 136l21 -21l-79 -96h3507v-37"],8407:[790,-519,3579,0,3579,"3507 636h-3507v37h3507l-79 96l21 21l130 -136l-130 -135l-21 21"],8730:[3175,0,946,63,979,"979 3175l-334 -3175l-439 1372l-131 -49l-12 25l250 122l313 -1016l292 2721h61"],10181:[1276,1276,450,53,397,"397 -1276c-157 0 -344 230 -344 692c0 638 218 1121 218 1738c0 49 -43 88 -91 88c-28 0 -56 -14 -73 -36c30 -1 50 -24 50 -52c0 -31 -23 -52 -53 -52s-51 21 -51 52c0 69 59 122 127 122s127 -53 127 -122c0 -609 -155 -1134 -155 -1738c0 -346 114 -659 245 -659v-33 "],10182:[1276,1276,450,53,397,"397 -584c0 -462 -187 -692 -344 -692v33c131 0 245 313 245 659c0 604 -155 1129 -155 1738c0 69 59 122 127 122s127 -53 127 -122c0 -31 -21 -52 -51 -52s-53 21 -53 52c0 28 20 51 50 52c-17 22 -45 36 -73 36c-48 0 -91 -39 -91 -88c0 -617 218 -1100 218 -1738"],10214:[1704,852,534,84,504,"504 -846l-6 -6c-32 1 -94 2 -126 2h-24l-2 -2c-12 0 -30 1 -50 1c-18 0 -36 -1 -50 -1l-2 2h-24c-32 0 -94 -1 -126 -2l-10 13c10 34 10 125 10 185v2160c0 60 0 151 -10 185l10 13c32 -1 94 -2 126 -2h24l2 2c14 0 32 -1 50 -1c20 0 38 1 50 1l2 -2h24c32 0 94 1 126 2 l6 -6v-26l-4 -5h-96c-38 0 -68 -11 -70 -35c-4 -37 -4 -91 -4 -126v-2160c0 -35 0 -89 4 -126c2 -24 32 -35 70 -35h96l4 -5v-26zM246 -654v2160c0 49 0 118 -6 161c-32 -2 -56 -13 -58 -35c-4 -37 -4 -91 -4 -126v-2160c0 -35 0 -89 4 -126c2 -22 26 -33 58 -35 c6 43 6 112 6 161"],10215:[1704,852,534,84,504,"84 -846l6 -6c32 1 94 2 126 2h24l2 -2c12 0 30 1 50 1c18 0 36 -1 50 -1l2 2h24c32 0 94 -1 126 -2l10 13c-10 34 -10 125 -10 185v2160c0 60 0 151 10 185l-10 13c-32 -1 -94 -2 -126 -2h-24l-2 2c-14 0 -32 -1 -50 -1c-20 0 -38 1 -50 1l-2 -2h-24c-32 0 -94 1 -126 2 l-6 -6v-26l4 -5h96c38 0 68 -11 70 -35c4 -37 4 -91 4 -126v-2160c0 -35 0 -89 -4 -126c-2 -24 -32 -35 -70 -35h-96l-4 -5v-26zM342 -654v2160c0 49 0 118 6 161c32 -2 56 -13 58 -35c4 -37 4 -91 4 -126v-2160c0 -35 0 -89 -4 -126c-2 -22 -26 -33 -58 -35 c-6 43 -6 112 -6 161"]};MathJax.Ajax.loadComplete(MathJax.OutputJax.SVG.fontDir+"/Size4/Regular/Main.js");
