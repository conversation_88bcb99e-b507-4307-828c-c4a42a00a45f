﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class ADDI13IndexViewModel
    {
        public string ROLL_CALL_ID { get; set; }
        public string SCHOOL_NO1 { get; set; }
        public string BarCode { get; set; }
        public string CARD_NO { get; set; }

        /// <summary>
        /// 角色娃娃 圖 路徑
        /// </summary>
        public string PlayerUrl { get; set; }
        public string wREF_BRE_NO { get; set; }
        /// <summary>
        /// 可看舊學校 Hmto1.USER_STATUS ='9'
        /// </summary>
        public bool wIsQhisSchool { get; set; }
        /// <summary>
        /// 資料角度
        /// </summary>
        public string DATA_ANGLE_TYPE { get; set; }
        /// <summary>
        /// 我的 線上投稿 全部單號  for 電子書用
        /// </summary>
        public List<int> arrWRITING_NO;
        /// <summary>
        /// 我的 線上投稿 TOP 5 筆
        /// </summary>
        public List<ADDT01> ADDT01List;

        /// <summary>
        /// 我的 閱讀認證 全部單號  for 電子書用
        /// </summary>
        public List<int> arrAPPLY_NO;

        /// <summary>
        /// 我的 閱讀認證 TOP 5筆
        /// </summary>
        ///         /// <summary>
        /// 線上投稿.投稿數
        /// </summary>
        public Int32 WritingCount { get; set; }

        /// <summary>
        /// 線上投稿.推薦數
        /// </summary>
        public Int32 WritingShareCount { get; set; }

        /// <summary>
        /// 閱讀認證.投稿數
        /// </summary>
        public Int32 BookCount { get; set; }

        /// <summary>
        /// 閱讀認證.推薦數
        /// </summary>
        public Int32 BookShareCount { get; set; }

        public List<ADDT06> ADDT06List;
        public virtual ICollection<ADDI13EditPeopleViewModel> Details { get; set; }
    }
}