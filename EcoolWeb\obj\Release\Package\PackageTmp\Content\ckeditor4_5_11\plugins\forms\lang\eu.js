﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'forms', 'eu', {
	button: {
		title: '<PERSON><PERSON><PERSON><PERSON> ezaugarriak',
		text: '<PERSON>ua (balioa)',
		type: '<PERSON><PERSON>',
		typeBtn: '<PERSON><PERSON><PERSON>',
		typeSbm: 'B<PERSON><PERSON>',
		typeRst: '<PERSON><PERSON><PERSON><PERSON>'
	},
	checkboxAndRadio: {
		checkboxTitle: 'Kontrol-laukiaren propietateak',
		radioTitle: 'Aukera-botoiaren propietateak',
		value: 'Balioa',
		selected: 'Hautatuta',
		required: 'Beharrezkoa'
	},
	form: {
		title: 'Formularioaren propietateak',
		menu: 'Formularioaren propietateak',
		action: 'Ekintza',
		method: 'Metodoa',
		encoding: 'Kodeketa'
	},
	hidden: {
		title: 'Ezkutuko eremuaren propietateak',
		name: '<PERSON><PERSON><PERSON>',
		value: '<PERSON><PERSON>'
	},
	select: {
		title: 'Hautespen eremuaren propietateak',
		selectInfo: 'Hautatu informazioa',
		opAvail: '<PERSON><PERSON><PERSON><PERSON> dauden aukerak',
		value: '<PERSON><PERSON>',
		size: '<PERSON>ain<PERSON>',
		lines: 'lerro kopurua',
		chkMulti: 'baimendu hautapen anitzak',
		required: 'Beharrezkoa',
		opText: 'Testua',
		opValue: 'Balioa',
		btnAdd: 'Gehitu',
		btnModify: 'Aldatu',
		btnUp: 'Gora',
		btnDown: 'Behera',
		btnSetValue: 'Ezarri hautatutako balio bezala',
		btnDelete: 'Ezabatu'
	},
	textarea: {
		title: 'Testu-arearen propietateak',
		cols: 'Zutabeak',
		rows: 'Errenkadak'
	},
	textfield: {
		title: 'Testu-eremuaren propietateak',
		name: 'Izena',
		value: 'Balioa',
		charWidth: 'Karaktere-zabalera',
		maxChars: 'Gehienezko karaktereak',
		required: 'Beharrezkoa',
		type: 'Mota',
		typeText: 'Testua',
		typePass: 'Pasahitza',
		typeEmail: 'E-posta',
		typeSearch: 'Bilatu',
		typeTel: 'Telefono zenbakia',
		typeUrl: 'URLa'
	}
} );
