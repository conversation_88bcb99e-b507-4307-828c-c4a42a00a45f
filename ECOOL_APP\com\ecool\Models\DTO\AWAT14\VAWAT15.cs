﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.com.ecool.Models.DTO.AWAT14
{
   public class VAWAT15
    {

        public int CASH_Rank { get; set; }
        
        public Nullable<byte> SYEAR { get; set; }
        public Nullable<byte> SEMESTER { get; set; }
        public string CLASS_NO { get; set; }
        public string SEAT_NO { get; set; }
        public string SNAME { get; set; }
        public string SHORT_NAME { get; set; }
        public string IsReMarkDesc { get; set; }
        public int? IsReMark { get; set; }
        public Nullable<System.DateTime> CreatDate { get; set; }
        public Nullable<System.DateTime> GOT_DATE { get; set; }
        public Nullable<System.DateTime> CANCEL_DATE { get; set; }
        public string MEMO { get; set; }
        public string SCHOOL_NO { get; set; }
        public Nullable<int> AWAT14_CASH { get; set; }
        public string NAME { get; set; }
        public string USER_NO { get; set; }

        public int AWAT15NO { get; set; }
        public int CASH_ALL { get; set; }

        public int? SUMCASH_AVAILABLE { get; set; }
        //public Nullable<byte> TRANS_STATUS { get; set; }
        //public string AWARD_TYPE { get; set; }
        //public string IMG_FILE { get; set; }
    }
}
