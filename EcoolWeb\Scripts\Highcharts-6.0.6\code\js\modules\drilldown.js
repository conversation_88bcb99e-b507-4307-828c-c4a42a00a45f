/*
 Highcharts JS v6.0.6 (2018-02-05)
 Highcharts Drilldown module

 Author: Torstein Honsi
 License: www.highcharts.com/license

*/
(function(n){"object"===typeof module&&module.exports?module.exports=n:n(Highcharts)})(function(n){(function(f){var n=f.animObject,z=f.noop,A=f.color,B=f.defaultOptions,k=f.each,p=f.extend,H=f.format,C=f.objectEach,t=f.pick,q=f.wrap,m=f.Chart,v=f.seriesTypes,D=v.pie,u=v.column,E=f.Tick,w=f.fireEvent,F=f.inArray,G=1;p(<PERSON>.lang,{drillUpText:"\u25c1 Back to {series.name}"});B.drilldown={animation:{duration:500},drillUpButton:{position:{align:"right",x:-10,y:10}}};f.SVGRenderer.prototype.Element.prototype.fadeIn=
function(a){this.attr({opacity:.1,visibility:"inherit"}).animate({opacity:t(this.newOpacity,1)},a||{duration:250})};m.prototype.addSeriesAsDrilldown=function(a,b){this.addSingleSeriesAsDrilldown(a,b);this.applyDrilldown()};m.prototype.addSingleSeriesAsDrilldown=function(a,b){var d=a.series,c=d.xAxis,e=d.yAxis,g,h=[],x=[],l,r,m;m={colorIndex:t(a.colorIndex,d.colorIndex)};this.drilldownLevels||(this.drilldownLevels=[]);l=d.options._levelNumber||0;(r=this.drilldownLevels[this.drilldownLevels.length-
1])&&r.levelNumber!==l&&(r=void 0);b=p(p({_ddSeriesId:G++},m),b);g=F(a,d.points);k(d.chart.series,function(a){a.xAxis!==c||a.isDrilling||(a.options._ddSeriesId=a.options._ddSeriesId||G++,a.options._colorIndex=a.userOptions._colorIndex,a.options._levelNumber=a.options._levelNumber||l,r?(h=r.levelSeries,x=r.levelSeriesOptions):(h.push(a),x.push(a.options)))});a=p({levelNumber:l,seriesOptions:d.options,levelSeriesOptions:x,levelSeries:h,shapeArgs:a.shapeArgs,bBox:a.graphic?a.graphic.getBBox():{},color:a.isNull?
(new f.Color(A)).setOpacity(0).get():A,lowerSeriesOptions:b,pointOptions:d.options.data[g],pointIndex:g,oldExtremes:{xMin:c&&c.userMin,xMax:c&&c.userMax,yMin:e&&e.userMin,yMax:e&&e.userMax},resetZoomButton:this.resetZoomButton},m);this.drilldownLevels.push(a);c&&c.names&&(c.names.length=0);b=a.lowerSeries=this.addSeries(b,!1);b.options._levelNumber=l+1;c&&(c.oldPos=c.pos,c.userMin=c.userMax=null,e.userMin=e.userMax=null);d.type===b.type&&(b.animate=b.animateDrilldown||z,b.options.animation=!0)};m.prototype.applyDrilldown=
function(){var a=this.drilldownLevels,b;a&&0<a.length&&(b=a[a.length-1].levelNumber,k(this.drilldownLevels,function(a){a.levelNumber===b&&k(a.levelSeries,function(a){a.options&&a.options._levelNumber===b&&a.remove(!1)})}));this.resetZoomButton&&(this.resetZoomButton.hide(),delete this.resetZoomButton);this.pointer.reset();this.redraw();this.showDrillUpButton()};m.prototype.getDrilldownBackText=function(){var a=this.drilldownLevels;if(a&&0<a.length)return a=a[a.length-1],a.series=a.seriesOptions,H(this.options.lang.drillUpText,
a)};m.prototype.showDrillUpButton=function(){var a=this,b=this.getDrilldownBackText(),d=a.options.drilldown.drillUpButton,c,e;this.drillUpButton?this.drillUpButton.attr({text:b}).align():(e=(c=d.theme)&&c.states,this.drillUpButton=this.renderer.button(b,null,null,function(){a.drillUp()},c,e&&e.hover,e&&e.select).addClass("highcharts-drillup-button").attr({align:d.position.align,zIndex:7}).add().align(d.position,!1,d.relativeTo||"plotBox"))};m.prototype.drillUp=function(){if(this.drilldownLevels&&
0!==this.drilldownLevels.length){for(var a=this,b=a.drilldownLevels,d=b[b.length-1].levelNumber,c=b.length,e=a.series,g,h,f,l,m=function(b){var c;k(e,function(a){a.options._ddSeriesId===b._ddSeriesId&&(c=a)});c=c||a.addSeries(b,!1);c.type===f.type&&c.animateDrillupTo&&(c.animate=c.animateDrillupTo);b===h.seriesOptions&&(l=c)};c--;)if(h=b[c],h.levelNumber===d){b.pop();f=h.lowerSeries;if(!f.chart)for(g=e.length;g--;)if(e[g].options.id===h.lowerSeriesOptions.id&&e[g].options._levelNumber===d+1){f=e[g];
break}f.xData=[];k(h.levelSeriesOptions,m);w(a,"drillup",{seriesOptions:h.seriesOptions});l.type===f.type&&(l.drilldownLevel=h,l.options.animation=a.options.drilldown.animation,f.animateDrillupFrom&&f.chart&&f.animateDrillupFrom(h));l.options._levelNumber=d;f.remove(!1);l.xAxis&&(g=h.oldExtremes,l.xAxis.setExtremes(g.xMin,g.xMax,!1),l.yAxis.setExtremes(g.yMin,g.yMax,!1));h.resetZoomButton&&(a.resetZoomButton=h.resetZoomButton,a.resetZoomButton.show())}w(a,"drillupall");this.redraw();0===this.drilldownLevels.length?
this.drillUpButton=this.drillUpButton.destroy():this.drillUpButton.attr({text:this.getDrilldownBackText()}).align();this.ddDupes.length=[]}};m.prototype.callbacks.push(function(){var a=this;a.drilldown={update:function(b,d){f.merge(!0,a.options.drilldown,b);t(d,!0)&&a.redraw()}}});q(m.prototype,"showResetZoom",function(a){this.drillUpButton||a.apply(this,Array.prototype.slice.call(arguments,1))});u.prototype.animateDrillupTo=function(a){if(!a){var b=this,d=b.drilldownLevel;k(this.points,function(a){var b=
a.dataLabel;a.graphic&&a.graphic.hide();b&&(b.hidden="hidden"===b.attr("visibility"),b.hidden||(b.hide(),a.connector&&a.connector.hide()))});f.syncTimeout(function(){b.points&&k(b.points,function(a,b){b=b===(d&&d.pointIndex)?"show":"fadeIn";var c="show"===b?!0:void 0,e=a.dataLabel;if(a.graphic)a.graphic[b](c);e&&!e.hidden&&(e.fadeIn(),a.connector&&a.connector.fadeIn())})},Math.max(this.chart.options.drilldown.animation.duration-50,0));this.animate=z}};u.prototype.animateDrilldown=function(a){var b=
this,d=this.chart.drilldownLevels,c,e=n(this.chart.options.drilldown.animation),g=this.xAxis;a||(k(d,function(a){b.options._ddSeriesId===a.lowerSeriesOptions._ddSeriesId&&(c=a.shapeArgs)}),c.x+=t(g.oldPos,g.pos)-g.pos,k(this.points,function(a){a.graphic&&a.graphic.attr(c).animate(p(a.shapeArgs,{fill:a.color||b.color}),e);a.dataLabel&&a.dataLabel.fadeIn(e)}),this.animate=null)};u.prototype.animateDrillupFrom=function(a){var b=n(this.chart.options.drilldown.animation),d=this.group,c=d!==this.chart.columnGroup,
e=this;k(e.trackerGroups,function(a){if(e[a])e[a].on("mouseover")});c&&delete this.group;k(this.points,function(e){var g=e.graphic,k=a.shapeArgs,l=function(){g.destroy();d&&c&&(d=d.destroy())};g&&(delete e.graphic,b.duration?g.animate(k,f.merge(b,{complete:l})):(g.attr(k),l()))})};D&&p(D.prototype,{animateDrillupTo:u.prototype.animateDrillupTo,animateDrillupFrom:u.prototype.animateDrillupFrom,animateDrilldown:function(a){var b=this.chart.options.drilldown.animation,d=this.chart.drilldownLevels[this.chart.drilldownLevels.length-
1].shapeArgs,c=d.start,e=(d.end-c)/this.points.length;a||(k(this.points,function(a,h){var g=a.shapeArgs;if(a.graphic)a.graphic.attr(f.merge(d,{start:c+h*e,end:c+(h+1)*e}))[b?"animate":"attr"](g,b)}),this.animate=null)}});f.Point.prototype.doDrilldown=function(a,b,d){var c=this.series.chart,e=c.options.drilldown,f=(e.series||[]).length,h;c.ddDupes||(c.ddDupes=[]);for(;f--&&!h;)e.series[f].id===this.drilldown&&-1===F(this.drilldown,c.ddDupes)&&(h=e.series[f],c.ddDupes.push(this.drilldown));w(c,"drilldown",
{point:this,seriesOptions:h,category:b,originalEvent:d,points:void 0!==b&&this.series.xAxis.getDDPoints(b).slice(0)},function(b){var c=b.point.series&&b.point.series.chart,d=b.seriesOptions;c&&d&&(a?c.addSingleSeriesAsDrilldown(b.point,d):c.addSeriesAsDrilldown(b.point,d))})};f.Axis.prototype.drilldownCategory=function(a,b){C(this.getDDPoints(a),function(d){d&&d.series&&d.series.visible&&d.doDrilldown&&d.doDrilldown(!0,a,b)});this.chart.applyDrilldown()};f.Axis.prototype.getDDPoints=function(a){var b=
[];k(this.series,function(d){var c,e=d.xData,f=d.points;for(c=0;c<e.length;c++)if(e[c]===a&&d.options.data[c]&&d.options.data[c].drilldown){b.push(f?f[c]:!0);break}});return b};E.prototype.drillable=function(){var a=this.pos,b=this.label,d=this.axis,c="xAxis"===d.coll&&d.getDDPoints,e=c&&d.getDDPoints(a);c&&(b&&e.length?(b.drillable=!0,b.addClass("highcharts-drilldown-axis-label").on("click",function(b){d.drilldownCategory(a,b)})):b&&b.drillable&&(b.on("click",null),b.removeClass("highcharts-drilldown-axis-label")))};
q(E.prototype,"addLabel",function(a){a.call(this);this.drillable()});q(f.Point.prototype,"init",function(a,b,d,c){var e=a.call(this,b,d,c);c=(a=b.xAxis)&&a.ticks[c];e.drilldown&&f.addEvent(e,"click",function(a){b.xAxis&&!1===b.chart.options.drilldown.allowPointDrilldown?b.xAxis.drilldownCategory(e.x,a):e.doDrilldown(void 0,void 0,a)});c&&c.drillable();return e});q(f.Series.prototype,"drawDataLabels",function(a){a.call(this);k(this.points,function(a){var b=a.options.dataLabels,c=t(a.dlOptions,b&&b.style,
{});a.drilldown&&a.dataLabel&&(b&&b.color&&(c.color=b.color),a.dataLabel.addClass("highcharts-drilldown-data-label"))},this)});var y=function(a,b,d){a[d?"addClass":"removeClass"]("highcharts-drilldown-point")},I=function(a){a.call(this);k(this.points,function(a){a.drilldown&&a.graphic&&y(a.graphic,"pointer",!0)})},J=function(a,b){var d=a.apply(this,Array.prototype.slice.call(arguments,1));this.drilldown&&this.series.halo&&"hover"===b?y(this.series.halo,"pointer",!0):this.series.halo&&y(this.series.halo,
"auto",!1);return d};C(v,function(a){q(a.prototype,"drawTracker",I);q(a.prototype.pointClass.prototype,"setState",J)})})(n)});
