(function(factory){if (typeof define === 'function' && define.amd){define(['jquery'], factory);} else if (typeof module === 'object' && typeof module.exports === 'object'){module.exports = factory(require('jquery'));} else {factory(jQuery);}}(function(jQuery){

/*! Widget: formatter - 2/9/2015 (v2.19.1) */
!function(d){"use strict";var u=d.tablesorter;u.formatter={init:function(t){var e=t.widgetOptions.formatter_event+" pagerComplete updateComplete ".split(" ").join(".tsformatter ");t.$table.off(e.replace(/\s+/g," ")).on(e,function(){u.formatter.setup(t)}),u.formatter.setup(t)},setup:function(t){if(!d.isEmptyObject(t.cache)){var e,o,r,n,i,a,l,c=t.widgetOptions,f={config:t,wo:c},s=[],m=[];for(l=0;l<t.columns;l++)m[l]=t.$headerIndexed[l],s[l]=u.getColumnData(t.table,c.formatter_column,l)||!1;for(o=0;o<t.$tbodies.length;o++){for(e=u.processTbody(t.table,t.$tbodies.eq(o),!0),a=(n=t.cache[o]).normalized.length,r=0;r<a;r++)for(f.$row=n.normalized[r][t.columns].$row,f.$cells=f.$row.children("th, td"),l=0;l<t.columns;l++)s[l]&&(f.columnIndex=l,f.$header=m[l],f.$cell=f.$cells.eq(l),i=f.$cell[0],f.text=i.getAttribute(t.textAttribute)||i.textContent||f.$cell.text(),i.innerHTML=s[l](f.text,f));u.processTbody(t.table,e,!1)}}}},u.addWidget({id:"formatter",priority:100,options:{formatter_column:{},formatter_event:"applyFormatter"},init:function(t){u.formatter.init(t.config)}})}(jQuery);return jQuery;}));
