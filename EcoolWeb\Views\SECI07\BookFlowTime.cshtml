﻿@model ECOOL_APP.com.ecool.Models.DTO.BookFlowTimeViewModel
@using ECOOL_APP.com.ecool.Models.DTO
@using ECOOL_APP.com.ecool.LogicCenter
@using DotNet.Highcharts;
@using DotNet.Highcharts.Enums;
@using DotNet.Highcharts.Helpers;
@using DotNet.Highcharts.Options;

@{
    ViewBag.Title = ViewBag.Panel_Title;

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);

    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
    else
    {
        Layout = "~/Views/Shared/_LayoutSEO.cshtml";
    }

}

@functions{
    Highcharts BookFlowTimeLineChart(List<TimeOfBook> List, string title)
    {
        List<Series> Series = new List<Series>();
        // 填滿小時
        for (int hour = 7; hour <= 20; hour++)
        {
            if (!List.Any(s => s.HOUR.Hour == hour))
            {
                List.Add(new TimeOfBook() { HOUR = new DateTime(TimeSpan.TicksPerHour * hour), COUNT = 0 });
            }
        }
        List = List.OrderBy(t => t.HOUR.Hour).ToList();

        Series.Add(new Series()
        {
            Data = new Data(List.Select(s => new Point() { Y = s.COUNT, Name = s.HOUR.ToString("hh") + " 時間(點)" }).ToArray()),
            Name = "時間點"
        });

        Highcharts FlowTimeAnalysisChart = new Highcharts("FlowTimeAnalysisChartFor" + title)
       .InitChart(new DotNet.Highcharts.Options.Chart
       {
           DefaultSeriesType = ChartTypes.Line,
           BackgroundColor = new DotNet.Highcharts.Helpers.BackColorOrGradient(System.Drawing.Color.Transparent)
       })
       .SetTitle(new Title { Text = title })
       .SetXAxis(new XAxis
       {
           Categories = new List<string> {  "上午 7", "上午 8", "上午 9", "上午 10", "上午 11","上午 12"
           , "下午 1", "下午 2", "下午 3", "下午 4", "下午 5", "下午 6", "下午 7", "下午 8"}.ToArray()
       })
       .SetYAxis(new YAxis { Title = new YAxisTitle { Text = "書量(本)" }, Min = 0, AllowDecimals = false })
       .SetSeries(Series.ToArray())
       .SetPlotOptions(new PlotOptions
       {
           Column = new PlotOptionsColumn
           {
               DataLabels = new PlotOptionsColumnDataLabels
               {
                   Enabled = true,
                   Rotation = 0,
                   Color = System.Drawing.Color.Black,
                   Format = "{point.y:.1f}",
                   Style = "fontSize: '14px',fontFamily: 'Verdana, sans-serif'"
               }
           }
       })
       .SetTooltip(new Tooltip { PointFormat = "{point.y:.0f} 本 </b>" })
       .SetLegend(new Legend { Enabled = false });

        FlowTimeAnalysisChart.SetCredits(new Credits { Enabled = false, Text = "", Href = "" });

        return FlowTimeAnalysisChart;
    }
}

<div class="containerEZ">
    @Html.Partial("_Title_Secondary")
    @{
        Html.RenderAction("_Menu", new { NowAction = "BookFlowTime" });
    }

    @using (Html.BeginForm("BookFlowTime", "SECI07", FormMethod.Post, new { id = "form1", name = "form1" }))
    {
    <div class="row">
        <div class="col-md-12">
            @Html.LabelFor(m => m.Where_SYEAR, new { @class = "col-md-1" })
            <div class="col-md-3">
                @Html.DropDownListFor(m => m.Where_SYEAR, (IEnumerable<SelectListItem>)ViewBag.SYearItems, new { @class = "form-control input-sm", @onchange = "this.form.submit()" })
            </div>


            @Html.LabelFor(m => m.Where_Month, new { @class = "col-md-1" })
            <div class="col-md-3">
                @Html.DropDownListFor(m => m.Where_Month, (IEnumerable<SelectListItem>)ViewBag.MonthItems, new { @class = "form-control input-sm", @onchange = "this.form.submit()" })
            </div>
        </div>
    </div>
    }
    <br />
    <div class="row">
        <div class="col-md-12 text-danger alert alert-success">
            統計總借書量: @Model.TimeOfBookBorrowList.Sum(m => m.COUNT) 本
        </div>
    </div>
    <div class="row" style="background-color:#ecf6ff">
        @this.BookFlowTimeLineChart(Model.TimeOfBookBorrowList, "借書時間點統計圖表")
    </div>
    <br />
    <div class="row">
        <div class="col-md-12 text-danger alert alert-info">
            統計總還書量: @Model.TimeOfBookReturnList.Sum(m => m.COUNT) 本
        </div>
    </div>
    <div class="row" style="background-color:#ecf6ff">
        @this.BookFlowTimeLineChart(Model.TimeOfBookReturnList, "還書時間點統計圖表")
    </div>
</div>


@section scripts{
    <script src="~/Scripts/Highcharts-4.0.1/js/highcharts.js"></script>
    <script src="~/Scripts/Highcharts-4.0.1/js/modules/exporting.js"></script>
}