﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'forms', 'no', {
	button: {
		title: 'Egenskaper for knapp',
		text: 'Tekst (verdi)',
		type: 'Type',
		typeBtn: 'Knapp',
		typeSbm: 'Send',
		typeRst: 'Nullstill'
	},
	checkboxAndRadio: {
		checkboxTitle: 'Egenskaper for avmerkingsboks',
		radioTitle: 'Egenskaper for alternativknapp',
		value: '<PERSON>',
		selected: 'Valgt',
		required: 'Required' // MISSING
	},
	form: {
		title: 'Egenskaper for skjema',
		menu: 'Egenskaper for skjema',
		action: 'Handling',
		method: 'Metode',
		encoding: 'Encoding'
	},
	hidden: {
		title: 'Egenskaper for skjult felt',
		name: 'Navn',
		value: '<PERSON>'
	},
	select: {
		title: 'Egenskaper for rullegardinliste',
		selectInfo: 'Info',
		opAvail: 'Tilgjenglige alternativer',
		value: '<PERSON>',
		size: 'Størrelse',
		lines: 'Linjer',
		chkMulti: 'Tillat flervalg',
		required: 'Required', // MISSING
		opText: 'Tekst',
		opValue: 'Verdi',
		btnAdd: 'Legg til',
		btnModify: 'Endre',
		btnUp: 'Opp',
		btnDown: 'Ned',
		btnSetValue: 'Sett som valgt',
		btnDelete: 'Slett'
	},
	textarea: {
		title: 'Egenskaper for tekstområde',
		cols: 'Kolonner',
		rows: 'Rader'
	},
	textfield: {
		title: 'Egenskaper for tekstfelt',
		name: 'Navn',
		value: 'Verdi',
		charWidth: 'Tegnbredde',
		maxChars: 'Maks antall tegn',
		required: 'Required', // MISSING
		type: 'Type',
		typeText: 'Tekst',
		typePass: 'Passord',
		typeEmail: 'Epost',
		typeSearch: 'Søk',
		typeTel: 'Telefonnummer',
		typeUrl: 'URL'
	}
} );
