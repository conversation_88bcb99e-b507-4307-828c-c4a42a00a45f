﻿<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="Access-Control-Allow-Origin" content="*">
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewBag.Title</title>
    @Styles.Render("~/Content/cssTwo")

    <link href="~/Content/font-awesome/css/font-awesome.min.css" rel="stylesheet" />
    @Scripts.Render("~/bundles/modernizr")
    @Scripts.Render("~/bundles/jquery")
    @Scripts.Render("~/bundles/bootstrap")
    <link href="~/Content/styles/leaderboard.min.css?@DateTime.Now.ToString("yyyy_MM_dd_H_mm_ss");" rel="stylesheet" />
    @{
        EcoolWeb.Models.UserProfileHelper.SetLayoutCookieData("~/Views/Shared/_LayoutEmpty.cshtml");
    }

    @{ Html.RenderPartial("_GoogleAnalytics"); }
    @RenderSection("css", required: false)
    @RenderSection("head", required: false)
</head>
<body>
    @RenderBody()
</body>
</html>
@RenderSection("scripts", required: false)
<script type="text/javascript">
    window.history.forward(1);
</script>
