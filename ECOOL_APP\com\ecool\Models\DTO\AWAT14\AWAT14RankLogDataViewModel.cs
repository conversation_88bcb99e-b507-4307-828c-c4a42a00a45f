﻿
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.IO;
using System.Linq;
using System.Text;

namespace ECOOL_APP.EF
{
    public class AWAT14RankLogDataViewModel
    {
        public int? CASH_ALL { get; set; }
        public int CASH_Rank { get; set; }
        [DisplayName("等級名稱")]
        public string LEVEL_DESC { get; set; }

        
        /// <summary>
        ///學校代碼
        /// </summary>
        [DisplayName("學校代碼")]
        public string SCHOOL_NO { get; set; }

        /// <summary>
        ///帳號
        /// </summary>
        [DisplayName("帳號")]
        public string USER_NO { get; set; }

        /// <summary>
        ///班級
        /// </summary>
        [DisplayName("班級")]
        public string CLASS_NO { get; set; }

        /// <summary>
        ///學年
        /// </summary>
        [DisplayName("學年")]
        public byte? SYEAR { get; set; }

        /// <summary>
        ///學期
        /// </summary>
        [DisplayName("學期")]
        public byte? SEMESTER { get; set; }

        /// <summary>
        ///座號
        /// </summary>
        [DisplayName("座號")]
        public string SEAT_NO { get; set; }

        /// <summary>
        ///姓名
        /// </summary>
        [DisplayName("姓名")]
        public string NAME { get; set; }

        /// <summary>
        ///簡稱
        /// </summary>
        [DisplayName("簡稱")]
        public string SNAME { get; set; }

        [Display(Name = "處理日期")]
        [DataType(DataType.Date), DisplayFormat(DataFormatString = "{0:yyyy/MM/dd}", ApplyFormatInEditMode = true)]
        public DateTime? DP_DATE { get; set; }





    }
}