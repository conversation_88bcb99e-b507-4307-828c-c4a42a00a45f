﻿function A4printScreenH(Url, block, bool) {
    /// <summary>
    /// 說明 :列印div包起來的部分並且「列印」完畢後自動關閉列印網頁 會用window.open
    /// </summary>
    /// <param name="block">div id</param> 
    var WinWidth = $(window).width();
    var value = block.innerHTML;
    var printPage = window.open("", "printPage", "scrollbars=yes,resizable=yes,status=yes ,top=0,width=" + WinWidth + ",height=550,left=0");
    printPage.document.open();
    printPage.document.writeln("<HTML><meta http-equiv='Content-Type' content='text/html; charset=big5'><head></head>");



    printPage.document.writeln("<link href='http://" + Url + "/Content/css/EzCss.css' rel='stylesheet' />")
    printPage.document.writeln("<link href='http://" + Url + "/Content/css/jquery-ui.min.css' rel='stylesheet' />")
    printPage.document.writeln("<link href='http://" + Url + "/Content/css/jquery.validationEngine.css' rel='stylesheet' />")
    printPage.document.writeln("<link href='http://" + Url + "/Content/css/bootstrap.css' rel='stylesheet' />")
    printPage.document.writeln("<link href='http://" + Url + "/Content/css/Site.css' rel='stylesheet' />")
    printPage.document.writeln("<BODY onload='window.print()'>");
    //printPage.document.writeln("<OBJECT classid='clsid:1663ed61-23eb-11d2-b92f-008048fdd814' codebase='ScriptX.cab#Version=5,0,5,35' id=factory style='DISPLAY: none' viewastext></OBJECT>");
    //printPage.document.writeln("<BODY onload='SetPrintSettings(" + bool + ");window.close();'>");
    //printPage.document.writeln("<script defer>");
    //printPage.document.writeln(" function SetPrintSettings(bool) { ");
    //printPage.document.writeln(" factory.printing.portrait = bool ;");
    //printPage.document.writeln(" factory.printing.topMargin = 10 ;");
    //printPage.document.writeln(" factory.printing.bottomMargin = 10;");
    //printPage.document.writeln(" factory.printing.leftMargin = 10;");
    //printPage.document.writeln(" factory.printing.rightMargin = 10;");
    //printPage.document.writeln(" factory.DoPrint(true);");
    //printPage.document.writeln(" }");
    //printPage.document.writeln(" </script>");
    printPage.document.writeln(" <div style='max-width:595px;max-height:842px;'>");
    printPage.document.writeln(value);
    printPage.document.writeln(" </div >");
    printPage.document.close("</BODY></HTML>");
}