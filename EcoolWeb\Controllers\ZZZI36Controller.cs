﻿using ECOOL_APP;
using ECOOL_APP.com.ecool.service;
using ECOOL_APP.EF;
using EcoolWeb.CustomAttribute;
using EcoolWeb.Models;
using EcoolWeb.ViewModels;
using log4net;
using MvcPaging;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace EcoolWeb.Controllers
{
    /// <summary>
    /// 學生內外碼匯入
    /// </summary>
    [SessionExpire]
    public class ZZZI36Controller : Controller
    {
        private ECOOL_DEVEntities EntitiesDb = new ECOOL_DEVEntities();
        private static ILog logger = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
#if !DEBUG

        [CheckPermission(CheckACTION_ID = "Index")]
#endif

        public ActionResult Index()
        {
            return View();
        }
        public ActionResult ImportCardTeacherExcel() {

            return View();

        }
        public ActionResult Index1()
        {
            return View();
        }
        public ActionResult Index21()
        {
            return View();
        }
        public ActionResult Index2()
        {
            return View();
        }
        public ActionResult Index31(ZZZI36ImpoertExcelViewModel model)
        {
            if (model == null) model = new ZZZI36ImpoertExcelViewModel();
            ImportViewShare1(model);
            return View(model);
        }

        public ActionResult Index3(ZZZI36ImpoertExcelViewModel model)
        {
            if (model == null) model = new ZZZI36ImpoertExcelViewModel();
            ImportViewShare1(model);
            return View(model);
        }
        public ActionResult Index51(ZZZI36ImpoertExcelViewModel model)
        {
            ImportViewShare1(model);
            return View(model);
        }
        public ActionResult DownloadExcel(string FilePath)
        {
            //讀成串流
            Stream iStream = new FileStream(FilePath, FileMode.Open, FileAccess.Read, FileShare.Read);

            //回傳出檔案
            return File(iStream, "application/vnd.ms-excel", System.IO.Path.GetFileName(FilePath));
        }

        public ActionResult Index4(ZZZI36ImpoertExcelViewModel model)
        {
            ImportViewShare(model);
            return View(model);
        }
        public ActionResult Index41(ZZZI36ImpoertExcelViewModel model)
        {
            ImportViewShare1(model);
            return View(model);
        }

        public ActionResult Index5(ZZZI36ImpoertExcelViewModel model)
        {
            ImportViewShare(model);
            return View(model);
        }

        public ActionResult ShareIndex()
        {
            return View();
        }
        public ActionResult ShareIndex1()
        {
            return View();
        }
#if !DEBUG

        [CheckPermission(CheckACTION_ID = "Import")]
#endif

        private void ImportViewShare(ZZZI36ImpoertExcelViewModel model)
        {
            //ZZZI36ImpoertExcelViewModel model = new ZZZI36ImpoertExcelViewModel();
            ViewBag.Panel_Title = "匯入健康資料";

            UserProfile user = UserProfileHelper.Get();
            string SchoolNO = user.SCHOOL_NO;

            //預設值
            int SYear;
            int Semesters;
            SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);

            //if (string.IsNullOrWhiteSpace(model.SYEAR)) model.SYEAR = SYear.ToString();
            //if (string.IsNullOrWhiteSpace(model.SEMESTER)) model.SEMESTER = Semesters.ToString();

            // ViewBag.SEMESTERItems = HRMT01.GetSEMESTERItems(model.SEMESTER.ToString());
            // ViewBag.SYEARItems = GetSYearsItems(model.SYEAR.ToString());

            var logList = EntitiesDb.Temp_Info_LOG.Where(a => a.SCHOOL_NO == SchoolNO).OrderByDescending(a => a.EVENT_TIME); ;
            model.LogList = logList.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, 50);

            //model.SYEAR = SYear.ToString();
        }
        private void ImportViewShare1(ZZZI36ImpoertExcelViewModel model)
        {
            //ZZZI36ImpoertExcelViewModel model = new ZZZI36ImpoertExcelViewModel();
            ViewBag.Panel_Title = "匯入健康資料";

            UserProfile user = UserProfileHelper.Get();
            string SchoolNO = user.SCHOOL_NO;

            //預設值
            int SYear;
            int Semesters;
            SysHelper.SemestersInfo(DateTime.Now, out SYear, out Semesters);

            //if (string.IsNullOrWhiteSpace(model.SYEAR)) model.SYEAR = SYear.ToString();
            //if (string.IsNullOrWhiteSpace(model.SEMESTER)) model.SEMESTER = Semesters.ToString();

            // ViewBag.SEMESTERItems = HRMT01.GetSEMESTERItems(model.SEMESTER.ToString());
            // ViewBag.SYEARItems = GetSYearsItems(model.SYEAR.ToString());

            var logList = EntitiesDb.Temp_ModifyInfo_LOG.Where(a => a.SCHOOL_NO == SchoolNO).OrderByDescending(a => a.EVENT_TIME); ;
            model.ModifyInfoList = logList.ToPagedList(model.Page > 0 ? model.Page - 1 : 0, 50);

            //model.SYEAR = SYear.ToString();
        }
        [ValidateAntiForgeryToken]
        [AcceptVerbs(HttpVerbs.Post)]
        public ActionResult ImportCard(HttpPostedFileBase file)
        {
            string schoolNo = UserProfileHelper.GetSchoolNo();
            using (HrmtImportService importService = new HrmtImportService())
            {
                bool state = importService.ImportCardNoFromExcel(file, schoolNo);

                if (!state)
                {
                    TempData["StatusMessage"] = importService.R_Message;
                }
                else
                {
                    TempData["StatusMessage"] = importService.R_ModifyHt01.Aggregate((l, r) => l + r);
                }

                return RedirectToAction("Index");
            }
        }
       

        [ValidateAntiForgeryToken]
        [AcceptVerbs(HttpVerbs.Post)]
        public ActionResult ImportTeacherCard(HttpPostedFileBase file)
        {
            string schoolNo = UserProfileHelper.GetSchoolNo();
            using (HrmtImportService importService = new HrmtImportService())
            {
                bool state = importService.ImportCardNoFromExcelTeacher(file, schoolNo);

                if (!state)
                {
                    TempData["StatusMessage"] = importService.R_Message;
                }
                else
                {
                    TempData["StatusMessage"] = importService.R_ModifyHt01.Aggregate((l, r) => l + r);
                }

                return RedirectToAction("ImportCardTeacherExcel");
            }
        }

        [ValidateAntiForgeryToken]
        [AcceptVerbs(HttpVerbs.Post)]
        public ActionResult ImportTeacherClassZZZI08(HttpPostedFileBase files)
        {
            string schoolNo = UserProfileHelper.GetSchoolNo();
            Dictionary<bool, string> dic = new Dictionary<bool, string>();
            Dictionary<bool, string> dic2 = new Dictionary<bool, string>();

            int stryear;
            int strMonth;
            int stream;
            stryear = DateTime.Now.Year - 1911;
            strMonth = DateTime.Now.Month;

            if (strMonth > 7)

            {
                stream = 1;
            }
            else if (strMonth <= 2)
            {
                stream = 1;
                stryear = stryear - 1;
            }
            else
            {
                stream = 2;
                stryear = stryear - 1;
            }
            UserProfile user = UserProfileHelper.Get();
            string ExclePath1 = @"\ZZZI36Excel\" + user.SCHOOL_NO + @"\" + DateTime.Now.ToString("yyyyMMdd_HH_mm_ss") + @"\";
            var tempPaths = System.Web.Configuration.WebConfigurationManager.AppSettings["FilePath"] + ExclePath1;
            System.IO.Directory.CreateDirectory(tempPaths);
            string tempPath1 = tempPaths + System.IO.Path.GetFileName(files.FileName);
            files.SaveAs(tempPath1);
            using (HrmtImportService importService = new HrmtImportService())
            {
                dic2 = importService.ImportTeacherClassInfo(files);
                //dic = importService.ImportTeacherInfo(file);

                if (dic2.Keys.Contains(true))
                {
                    Temp_Info_LOG templateInfo = new Temp_Info_LOG();
                    templateInfo.EVENT_TIME = DateTime.Now;
                    templateInfo.SCHOOL_NO = dic2[true];
                    templateInfo.SEMESTER = (byte)stream;
                    templateInfo.SYEAR = (byte)stryear;
                    templateInfo.IMPORT_TYPE = "Temp_BASCLS";

                    templateInfo.SCHOOL_NO = user.SCHOOL_NO;
                    templateInfo.USER_NO = user.USER_NO;
                    importService.MERGETEMPHRMTBySchool(dic2[true]);
                    //建立上傳路徑
                    string ExclePath = @"\ZZZI36Excel\" + templateInfo.SCHOOL_NO + @"\" + templateInfo.EVENT_TIME.ToString("yyyyMMdd_HH_mm_ss") + @"\";
                    var tempPath = System.Web.Configuration.WebConfigurationManager.AppSettings["FilePath"] + ExclePath;
                    System.IO.Directory.CreateDirectory(tempPath);
                    templateInfo.FILE_PATH = tempPath + System.IO.Path.GetFileName(files.FileName);
                    copy(tempPath1, templateInfo.FILE_PATH);
                    files.InputStream.Flush();

                    files.InputStream.Close();
                    files.InputStream.Dispose();
                    EntitiesDb.Temp_Info_LOG.Add(templateInfo);
                    EntitiesDb.SaveChanges();
                }
                //else
                //{
                //    if (dic2.Keys.Contains(true))
                //    {
                //        importService.MERGETEMPHRMTBySchool(dic2[true]);
                //    }
                //}

                if (dic2.Keys.Contains(false)
                    //|| dic2.Keys.Contains(false)
                    )
                {
                    TempData["StatusMessage"] = importService.R_Message;
                }
                else
                {
                    if (importService.R_ModifyHt01.Count() > 0)
                    {
                        TempData["StatusMessage"] = importService.R_ModifyHt01?.Aggregate((l, r) => l + r);
                    }
                }

                return RedirectToAction("Index4");
            }
        }
        [ValidateAntiForgeryToken]
        [AcceptVerbs(HttpVerbs.Post)]
        public ActionResult ImportTeacherClassModifyZZZI08(HttpPostedFileBase files)
        {
            string schoolNo = UserProfileHelper.GetSchoolNo();
            Dictionary<bool, string> dic = new Dictionary<bool, string>();
            Dictionary<bool, string> dic2 = new Dictionary<bool, string>();

            int stryear;
            int strMonth;
            int stream;
            stryear = DateTime.Now.Year - 1911;
            strMonth = DateTime.Now.Month;

            if (strMonth > 7)

            {
                stream = 1;
            }
            else if (strMonth <= 2)
            {
                stream = 1;
                stryear = stryear - 1;
            }
            else
            {
                stream = 2;
                stryear = stryear - 1;
            }
            UserProfile user = UserProfileHelper.Get();
            string ExclePath1 = @"\ZZZI36ModifyExcel\" + user.SCHOOL_NO + @"\" + DateTime.Now.ToString("yyyyMMdd_HH_mm_ss") + @"\";
            var tempPaths = System.Web.Configuration.WebConfigurationManager.AppSettings["FilePath"] + ExclePath1;
            System.IO.Directory.CreateDirectory(tempPaths);
            string tempPath1 = tempPaths + System.IO.Path.GetFileName(files.FileName);
            files.SaveAs(tempPath1);
            using (HrmtImportService importService = new HrmtImportService())
            {
                dic2 = importService.ImportTeacherClassInfo(files);
                //dic = importService.ImportTeacherInfo(file);

                if (!dic2.Keys.Contains(false))
                {
                    Temp_ModifyInfo_LOG templateInfo = new Temp_ModifyInfo_LOG();
                    templateInfo.EVENT_TIME = DateTime.Now;
                    templateInfo.SCHOOL_NO = dic2[true];
                    templateInfo.SEMESTER = (byte)stream;
                    templateInfo.SYEAR = (byte)stryear;
                    templateInfo.IMPORT_TYPE = "Temp_BASCLS";

                    templateInfo.SCHOOL_NO = user.SCHOOL_NO;
                    templateInfo.USER_NO = user.USER_NO;
                    importService.MERGETEMPHRMTBySchool(dic2[true]);
                    //建立上傳路徑
                    string ExclePath = @"\ZZZI36ModifyExcel\" + templateInfo.SCHOOL_NO + @"\" + templateInfo.EVENT_TIME.ToString("yyyyMMdd_HH_mm_ss") + @"\";
                    var tempPath = System.Web.Configuration.WebConfigurationManager.AppSettings["FilePath"] + ExclePath;
                    System.IO.Directory.CreateDirectory(tempPath);
                    templateInfo.FILE_PATH = tempPath + System.IO.Path.GetFileName(files.FileName);
                    try
                    {
                        copy(tempPath1, templateInfo.FILE_PATH);
                        files.InputStream.Flush();

                        files.InputStream.Close();
                        files.InputStream.Dispose();
                        EntitiesDb.Temp_ModifyInfo_LOG.Add(templateInfo);
                        EntitiesDb.SaveChanges();
                    }
                    catch (Exception e) {

                    }
                }
                //else
                //{
                //    if (dic2.Keys.Contains(true))
                //    {
                //        importService.MERGETEMPHRMTBySchool(dic2[true]);
                //    }
                //}

                if (dic2.Keys.Contains(false)
                    //|| dic2.Keys.Contains(false)
                    )
                {
                    TempData["StatusMessage"] = importService.R_Message;
                }
                else
                {
                    if (importService.R_ModifyHt01.Count() > 0)
                    {
                        TempData["StatusMessage"] = importService.R_ModifyHt01?.Aggregate((l, r) => l + r);
                    }
                }

                return RedirectToAction("Index41");
            }
        }
        [ValidateAntiForgeryToken]
        [AcceptVerbs(HttpVerbs.Post)]
        public ActionResult ImportStudentModifyZZZI08(HttpPostedFileBase files)
        {
            string schoolNo = UserProfileHelper.GetSchoolNo();
            Dictionary<bool, string> dic = new Dictionary<bool, string>();
            Dictionary<bool, string> dic2 = new Dictionary<bool, string>();
            int stryear;
            int strMonth;
            int stream;
            stryear = DateTime.Now.Year - 1911;
            strMonth = DateTime.Now.Month;
            if (strMonth > 7)

            {
                stream = 1;
            }
            else if (strMonth <= 2)
            {
                stream = 1;
                stryear = stryear - 1;
            }
            else
            {
                stream = 2;
                stryear = stryear - 1;
            }
            UserProfile user = UserProfileHelper.Get();
            string ExclePath1 = @"\ZZZI36ModifyExcel\" + user.SCHOOL_NO + @"\" + DateTime.Now.ToString("yyyyMMdd_HH_mm_ss") + @"\";
            var tempPaths = System.Web.Configuration.WebConfigurationManager.AppSettings["FilePath"] + ExclePath1;
            System.IO.Directory.CreateDirectory(tempPaths);
            string tempPath1 = tempPaths + System.IO.Path.GetFileName(files.FileName);
            files.SaveAs(tempPath1);
            using (HrmtImportService importService = new HrmtImportService())
            {
                //  dic2 = importService.ImportTeacherClassInfo(file);
                dic = importService.ImportStudentInfo(files);

                if (dic.Keys.Contains(true))
                {
                    Temp_ModifyInfo_LOG templateInfo = new Temp_ModifyInfo_LOG();
                    templateInfo.EVENT_TIME = DateTime.Now;
                    templateInfo.SCHOOL_NO = dic[true];
                    templateInfo.SEMESTER = (byte)stream;
                    templateInfo.SYEAR = (byte)stryear;
                    templateInfo.IMPORT_TYPE = "Temp_STUDENT";
                    //  UserProfile user = UserProfileHelper.Get();
                    templateInfo.SCHOOL_NO = user.SCHOOL_NO;
                    templateInfo.USER_NO = user.USER_NO;
                    importService.MERGETEMPHRMTBySchool(dic[true]);
                    //建立上傳路徑
                    string ExclePath = @"\ZZZI36ModifyExcel\" + templateInfo.SCHOOL_NO + @"\" + templateInfo.EVENT_TIME.ToString("yyyyMMdd_HH_mm_ss") + @"\";
                    var tempPath = System.Web.Configuration.WebConfigurationManager.AppSettings["FilePath"] + ExclePath;
                    System.IO.Directory.CreateDirectory(tempPath);
                    templateInfo.FILE_PATH = tempPath + System.IO.Path.GetFileName(files.FileName);
                    try
                    {
                        copy(tempPath1, templateInfo.FILE_PATH);
                        files.InputStream.Flush();
                        logger.Info("ImportStudentModifyZZZI08 1");
                        files.InputStream.Close();
                        logger.Info("ImportStudentModifyZZZI08 2");
                        files.InputStream.Dispose();
                        logger.Info("ImportStudentModifyZZZI08 3");
                        EntitiesDb.Temp_ModifyInfo_LOG.Add(templateInfo);
                        logger.Info("ImportStudentModifyZZZI08 4");
                        EntitiesDb.SaveChanges();
                    }
                    catch (Exception e) {


                    }
                }
                //else
                //{
                //    if (dic2.Keys.Contains(true))
                //    {
                //        importService.MERGETEMPHRMTBySchool(dic2[true]);
                //    }
                //}

                if (dic.Keys.Contains(false)
                    //|| dic2.Keys.Contains(false)
                    )
                {
                    TempData["StatusMessage"] = importService.R_Message;
                }
                else
                {
                    if (importService.R_ModifyHt01.Count() > 0)
                    {
                        TempData["StatusMessage"] = importService.R_ModifyHt01?.Aggregate((l, r) => l + r);
                    }
                }

                return RedirectToAction("Index51");
            }
        }

        [ValidateAntiForgeryToken]
        [AcceptVerbs(HttpVerbs.Post)]
        public ActionResult ImportStudentZZZI08(HttpPostedFileBase files)
        {
            string schoolNo = UserProfileHelper.GetSchoolNo();
            Dictionary<bool, string> dic = new Dictionary<bool, string>();
            Dictionary<bool, string> dic2 = new Dictionary<bool, string>();
            int stryear;
            int strMonth;
            int stream;
            stryear = DateTime.Now.Year - 1911;
            strMonth = DateTime.Now.Month;
            if (strMonth > 7)

            {
                stream = 1;
            }
            else if (strMonth <= 2)
            {
                stream = 1;
                stryear = stryear - 1;
            }
            else
            {
                stream = 2;
                stryear = stryear - 1;
            }
            UserProfile user = UserProfileHelper.Get();
            string ExclePath1 = @"\ZZZI36Excel\" + user.SCHOOL_NO + @"\" + DateTime.Now.ToString("yyyyMMdd_HH_mm_ss") + @"\";
            var tempPaths = System.Web.Configuration.WebConfigurationManager.AppSettings["FilePath"] + ExclePath1;
            System.IO.Directory.CreateDirectory(tempPaths);
            string tempPath1 = tempPaths + System.IO.Path.GetFileName(files.FileName);
            files.SaveAs(tempPath1);
            using (HrmtImportService importService = new HrmtImportService())
            {
                //  dic2 = importService.ImportTeacherClassInfo(file);
                dic = importService.ImportStudentInfo(files);

                if (dic.Keys.Contains(true))
                {
                    Temp_Info_LOG templateInfo = new Temp_Info_LOG();
                    templateInfo.EVENT_TIME = DateTime.Now;
                    templateInfo.SCHOOL_NO = dic[true];
                    templateInfo.SEMESTER = (byte)stream;
                    templateInfo.SYEAR = (byte)stryear;
                    templateInfo.IMPORT_TYPE = "Temp_STUDENT";
                    //  UserProfile user = UserProfileHelper.Get();
                    templateInfo.SCHOOL_NO = user.SCHOOL_NO;
                    templateInfo.USER_NO = user.USER_NO;
                    importService.MERGETEMPHRMTBySchool(dic[true]);
                    //建立上傳路徑
                    string ExclePath = @"\ZZZI36Excel\" + templateInfo.SCHOOL_NO + @"\" + templateInfo.EVENT_TIME.ToString("yyyyMMdd_HH_mm_ss") + @"\";
                    var tempPath = System.Web.Configuration.WebConfigurationManager.AppSettings["FilePath"] + ExclePath;
                    System.IO.Directory.CreateDirectory(tempPath);
                    templateInfo.FILE_PATH = tempPath + System.IO.Path.GetFileName(files.FileName);
                    try
                    {
                        copy(tempPath1, templateInfo.FILE_PATH);
                        files.InputStream.Flush();

                        files.InputStream.Close();
                        files.InputStream.Dispose();
                        EntitiesDb.Temp_Info_LOG.Add(templateInfo);
                        EntitiesDb.SaveChanges();

                    }
                    catch (Exception e) {


                    }
                  
                }
                //else
                //{
                //    if (dic2.Keys.Contains(true))
                //    {
                //        importService.MERGETEMPHRMTBySchool(dic2[true]);
                //    }
                //}

                if (dic.Keys.Contains(false)
                    //|| dic2.Keys.Contains(false)
                    )
                {
                    TempData["StatusMessage"] = importService.R_Message;
                }
                else
                {
                    if (importService.R_ModifyHt01.Count() > 0)
                    {
                        TempData["StatusMessage"] = importService.R_ModifyHt01?.Aggregate((l, r) => l + r);
                    }
                }

                return RedirectToAction("Index5");
            }
        }

        public void copy(String oldPath, String newPath)
        {
            FileStream input = null;
            FileStream output = null;
            try
            {
                input = new FileStream(oldPath, FileMode.Open);
                output = new FileStream(newPath, FileMode.Create, FileAccess.ReadWrite);

                byte[] buffer = new byte[32768];
                int read;
                while ((read = input.Read(buffer, 0, buffer.Length)) > 0)
                {
                    output.Write(buffer, 0, read);
                }
            }
            catch (Exception e)
            {
            }
            finally
            {
                input.Close();
                input.Dispose();
                output.Close();
                output.Dispose();
            }
        }
        [ValidateAntiForgeryToken]
        [AcceptVerbs(HttpVerbs.Post)]
        public ActionResult ImportTeacherModifyZZZI08(HttpPostedFileBase files)
        {
            string schoolNo = UserProfileHelper.GetSchoolNo();
            Dictionary<bool, string> dic = new Dictionary<bool, string>();
            Dictionary<bool, string> dic2 = new Dictionary<bool, string>();
            int stryear;
            int strMonth;
            int stream;
            stryear = DateTime.Now.Year - 1911;
            strMonth = DateTime.Now.Month;
            UserProfile user = UserProfileHelper.Get();
            string ExclePath1 = @"\ZZZI36ModifyExcel\" + user.SCHOOL_NO + @"\" + DateTime.Now.ToString("yyyyMMdd_HH_mm_ss") + @"\";
            var tempPaths = System.Web.Configuration.WebConfigurationManager.AppSettings["FilePath"] + ExclePath1;
            System.IO.Directory.CreateDirectory(tempPaths);
            string tempPath1 = tempPaths + System.IO.Path.GetFileName(files.FileName);
            files.SaveAs(tempPath1);
            if (strMonth > 7)

            {
                stream = 1;
            }
            else if (strMonth <= 2)
            {
                stream = 1;
                stryear = stryear - 1;
            }
            else
            {
                stream = 2;
                stryear = stryear - 1;
            }
            using (HrmtImportService importService = new HrmtImportService())
            {
                //  dic2 = importService.ImportTeacherClassInfo(file);
                dic = importService.ImportTeacherInfo(files);

                if (dic.Keys.Contains(true))
                {
                    Temp_ModifyInfo_LOG templateInfo = new Temp_ModifyInfo_LOG();
                    templateInfo.EVENT_TIME = DateTime.Now;
                    templateInfo.SCHOOL_NO = dic[true];
                    templateInfo.SEMESTER = (byte)stream;
                    templateInfo.SYEAR = (byte)stryear;
                    templateInfo.IMPORT_TYPE = "Temp_TEABAS";

                    templateInfo.SCHOOL_NO = user.SCHOOL_NO;
                    templateInfo.USER_NO = user.USER_NO;
                    importService.MERGETEMPHRMTBySchool(dic[true]);
                    //建立上傳路徑
                    string ExclePath = @"\ZZZI36ModifyExcel\" + templateInfo.SCHOOL_NO + @"\" + templateInfo.EVENT_TIME.ToString("yyyyMMdd_HH_mm_ss") + @"\";
                    var tempPath = System.Web.Configuration.WebConfigurationManager.AppSettings["FilePath"] + ExclePath;
                    System.IO.Directory.CreateDirectory(tempPath);

                    templateInfo.FILE_PATH = tempPath + System.IO.Path.GetFileName(files.FileName);
                    try
                    {
                        copy(tempPath1, templateInfo.FILE_PATH);
                        logger.Debug("ImportTeacher1");

                       files.InputStream.Flush();
                        logger.Debug("ImportTeacher2");
                        files.InputStream.Close();
                        logger.Debug("ImportTeacher3");
                        files.InputStream.Dispose();
                        logger.Debug("ImportTeacher4");
                        EntitiesDb.Temp_ModifyInfo_LOG.Add(templateInfo);
                        EntitiesDb.SaveChanges();
                    }
                    catch (Exception e) {
                        logger.Debug("ImportTeacher" + e.Message);
                        logger.Debug("ImportTeacher"+e.StackTrace);
                    }
                }
                //else
                //{
                //    if (dic2.Keys.Contains(true))
                //    {
                //        importService.MERGETEMPHRMTBySchool(dic2[true]);
                //    }
                //}

                if (dic.Keys.Contains(false)
                    //|| dic2.Keys.Contains(false)
                    )
                {
                    TempData["StatusMessage"] = importService.R_Message;
                }
                else
                {
                    if (importService.R_ModifyHt01.Count() > 0)
                    {
                        TempData["StatusMessage"] = importService.R_ModifyHt01?.Aggregate((l, r) => l + r);
                    }
                }

                return RedirectToAction("Index31");
            }
        }
        [ValidateAntiForgeryToken]
        [AcceptVerbs(HttpVerbs.Post)]
        public ActionResult ImportTeacherZZZI08(HttpPostedFileBase files)
        {
            string schoolNo = UserProfileHelper.GetSchoolNo();
            Dictionary<bool, string> dic = new Dictionary<bool, string>();
            Dictionary<bool, string> dic2 = new Dictionary<bool, string>();
            int stryear;
            int strMonth;
            int stream;
            stryear = DateTime.Now.Year - 1911;
            strMonth = DateTime.Now.Month;
            UserProfile user = UserProfileHelper.Get();
            string ExclePath1 = @"\ZZZI36Excel\" + user.SCHOOL_NO + @"\" + DateTime.Now.ToString("yyyyMMdd_HH_mm_ss") + @"\";
            var tempPaths = System.Web.Configuration.WebConfigurationManager.AppSettings["FilePath"] + ExclePath1;
            System.IO.Directory.CreateDirectory(tempPaths);
            string tempPath1 = tempPaths + System.IO.Path.GetFileName(files.FileName);
            files.SaveAs(tempPath1);
            if (strMonth > 7)

            {
                stream = 1;
            }
            else if (strMonth <= 2)
            {
                stream = 1;
                stryear = stryear - 1;
            }
            else
            {
                stream = 2;
                stryear = stryear - 1;
            }
            using (HrmtImportService importService = new HrmtImportService())
            {
                //  dic2 = importService.ImportTeacherClassInfo(file);
                dic = importService.ImportTeacherInfo(files);

                if (dic.Keys.Contains(true))
                {
                    Temp_Info_LOG templateInfo = new Temp_Info_LOG();
                    templateInfo.EVENT_TIME = DateTime.Now;
                    templateInfo.SCHOOL_NO = dic[true];
                    templateInfo.SEMESTER = (byte)stream;
                    templateInfo.SYEAR = (byte)stryear;
                    templateInfo.IMPORT_TYPE = "Temp_TEABAS";

                    templateInfo.SCHOOL_NO = user.SCHOOL_NO;
                    templateInfo.USER_NO = user.USER_NO;
                    importService.MERGETEMPHRMTBySchool(dic[true]);
                    //建立上傳路徑
                    string ExclePath = @"\ZZZI36Excel\" + templateInfo.SCHOOL_NO + @"\" + templateInfo.EVENT_TIME.ToString("yyyyMMdd_HH_mm_ss") + @"\";
                    var tempPath = System.Web.Configuration.WebConfigurationManager.AppSettings["FilePath"] + ExclePath;
                    System.IO.Directory.CreateDirectory(tempPath);
                    templateInfo.FILE_PATH = tempPath + System.IO.Path.GetFileName(files.FileName);
                    copy(tempPath1, templateInfo.FILE_PATH);

                    files.InputStream.Flush();

                    files.InputStream.Close();
                    files.InputStream.Dispose();
                    EntitiesDb.Temp_Info_LOG.Add(templateInfo);
                    EntitiesDb.SaveChanges();
                }
                //else
                //{
                //    if (dic2.Keys.Contains(true))
                //    {
                //        importService.MERGETEMPHRMTBySchool(dic2[true]);
                //    }
                //}

                if (dic.Keys.Contains(false)
                    //|| dic2.Keys.Contains(false)
                    )
                {
                    TempData["StatusMessage"] = importService.R_Message;
                }
                else
                {
                    if (importService.R_ModifyHt01.Count() > 0)
                    {
                        TempData["StatusMessage"] = importService.R_ModifyHt01?.Aggregate((l, r) => l + r);
                    }
                }

                return RedirectToAction("Index3");
            }
        }
    }
}