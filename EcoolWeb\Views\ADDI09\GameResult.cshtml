﻿
@model ECOOL_APP.com.ecool.Models.DTO.ADDI09EditOoneViewModel
@using ECOOL_APP.com.ecool.Models.DTO;

<link href="~/Content/css/EzCss.css" rel="stylesheet" />
@{
    ViewBag.Title = ViewBag.Panel_Title;
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
}
@Html.Partial("_Title_Secondary")
<div style="text-align:center">
    <h3>
        恭喜
    </h3>
    <h3 style="color:blue">
        @Model.USER_NO  @Model.USER_NAME
    </h3>
    <h3>
        小朋友，因為
    </h3>
    <h3 style="color:blue">
        @Model.SUBJECT
    </h3>
    <h3>
        獲得
    </h3>
    <h1 style="color:red">
        @Model.CASH  點
    </h1>
    <br />
    <a href='@Url.Action(ViewBag.NextAction,new { IsRandom = Model.IsRandom, IsFix = Model.IsFix})' role="button" class="btn btn-sm btn-sys">
        下一筆
    </a>
</div>
<div style="text-align:center">
    <video id="SlotPlayer" style="width:250px ;height: auto" autoplay="autoplay" loop="loop">
        <source src="~/Content/mp4/RealtimePoint2.mp4" type="video/mp4">
    </video>
</div>
    <script type="text/javascript">
        window.history.forward(1);
    </script>

