﻿@model CkEditEditViewModel
@{
    ViewBag.Title = ViewBag.Title;
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
    string CkEditorAction = EcoolWeb.Controllers.CkEditController.GetBrowseUrlName(Model.Search.SeeREF_TYPE);
}


@using (Html.BeginForm("EditCkEdit", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "formEdit", name = "formEdit", @class = "form-horizontal", enctype = "multipart/form-data" }))
{

    @Html.AntiForgeryToken()

    @Html.HiddenFor(m => m.Search.OrdercColumn)
    @Html.HiddenFor(m => m.Search.SyntaxName)
    @Html.HiddenFor(m => m.Search.Page)

    @Html.HiddenFor(m => m.Search.SeeREF_TYPE)
    @Html.HiddenFor(m => m.Search.SeeSCHOOL_NO)
    @Html.HiddenFor(m => m.Search.SeeUSER_NO)

    @Html.HiddenFor(m => m.Search.BackAction)
    @Html.HiddenFor(m => m.Search.BackController)
    @Html.HiddenFor(m => m.Search.CKEditorFuncNum)
    @Html.HiddenFor(m => m.Search.CKEditor)
    @Html.HiddenFor(m => m.Search.langCode)



    @Html.HiddenFor(m => m.Search.WhereREF_NO)
    @Html.HiddenFor(m => m.Data.REF_NO)

    <div style="padding: 30px 30px;">
        <h3 class="page-header">
            @if (Model.Search.SeeREF_TYPE == BDMT03.REF_TYPEVal.Audio)
            {
                <span class="glyphicon glyphicon-star" aria-hidden="true">Audio 參考來源<small>（僅接受 mp3)</small></span>
            }
            else if (Model.Search.SeeREF_TYPE == BDMT03.REF_TYPEVal.Vedio)
            {
                <span class="glyphicon glyphicon-star" aria-hidden="true">Vedio 參考來源<small>（僅接受 mp4)</small></span>
            }
            else if (Model.Search.SeeREF_TYPE == BDMT03.REF_TYPEVal.Image)
            {
                <span class="glyphicon glyphicon-star" aria-hidden="true">Image 參考來源<small>（僅接受 jpg、jpeg、png、gif、bmp)</small></span>
            }

        </h3>
        @Html.Partial("_Notice")
        <div class="col-lg-8 col-sm-8">
            <form class="form-horizontal">
                <fieldset>
                    <div class="form-group">
                        @Html.LabelFor(m => m.Data.SUBJECT, htmlAttributes: new { @class = "col-md-4 control-label" })
                        <div class="col-md-8">
                            @Html.EditorFor(m => m.Data.SUBJECT, new { htmlAttributes = new { @class = "form-control input-md", @placeholder = Html.DisplayNameFor(m => m.Data.SUBJECT) } })
                            @Html.ValidationMessageFor(m => m.Data.SUBJECT, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    @if (Model.Data == null)
                    {
                         <!-- File Button -->
                        <div class="form-group">
                            <label class="col-md-4 control-label" for="上傳">上傳(可多選,大小限制10MB)</label>
                            <div class="col-md-8">
                                <input name="files" class="input-file btn btn-sm" type="file" multiple="multiple">
                                @Html.ValidationMessage("files", "", new { @class = "text-danger" })
                            </div>
                        </div>
                    }
                    else
                    {
                         <!-- File Button -->
                        <div class="form-group">
                            <label class="col-md-4 control-label" for="上傳">上傳</label>
                            <div class="col-md-8">
                                <input name="files" class="input-file btn btn-sm" type="file">
                                @Html.ValidationMessage("files", "", new { @class = "text-danger" })
                            </div>
                        </div>
                    }
               
          

                    @if (Model.Data != null)
                    {
                        @Html.HiddenFor(model => model.Data.FILE)

                        if (string.IsNullOrWhiteSpace(Model.Data.FILE_PATH) == false)
                        {
                            <div class="form-group">
                                @if (Model.Search.SeeREF_TYPE == BDMT03.REF_TYPEVal.Audio)
                                {
                                    <label class="col-md-4 control-label" for="原Audio">原Audio</label>
                                    <div class="col-md-8">
                                        @Html.HiddenFor(model => model.Data.FILE)
                                        <audio controls>
                                            <source src="@Url.Content(Model.Data.FILE_PATH)" type="audio/mp3">
                                            您的瀏覽器不支援此 HTML5 audio 標籤
                                        </audio>
                                    </div>
                                }
                                else if (Model.Search.SeeREF_TYPE == BDMT03.REF_TYPEVal.Vedio)
                                {
                                        <label class="col-md-4 control-label" for="原Vedio">原Vedio</label>
                                        <div class="col-md-8">
                                            @Html.HiddenFor(model => model.Data.FILE)
                                            <video width="360" height="270" controls>
                                                <source src="@Url.Content(Model.Data.FILE_PATH)" type="video/mp4">
                                                您的瀏覽器不支援此 HTML5 Vedio標籤
                                            </video>
                                        </div>
                                }
                                else if (Model.Search.SeeREF_TYPE == BDMT03.REF_TYPEVal.Image)
                                {
                                    <label class="col-md-4 control-label" for="原Image">原Image</label>
                                        <div class="col-md-8">
                                            @Html.HiddenFor(model => model.Data.FILE)
                                            <img src="@Url.Content(Model.Data.FILE_PATH)" alt="原Image" />
                                        </div>
                                }
                            </div>
                        }
                    }
                </fieldset>
            </form>
            <span class="pull-right">
                <button type="button" style="margin: -15px 0px;" class="btn btn-success btn-sm" onclick="onSave()">確定</button>
                @if (Model.Data != null)
                {
                    <button type="button" style="margin: -15px 0px;" class="btn btn-danger btn-sm" onclick="onDel()">刪除</button>
                }
                <a role="button" style="margin: -15px 0px;" class="btn btn-success btn-sm" onclick="onBack()">回列表</a>
            </span>
        </div>
    </div>


}

@section scripts{
    <script type="text/javascript">

        var targetFormID = '#formEdit';

        function onSave() {
            $(targetFormID).attr("action", "@Url.Action("EditCkEditSave", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        function onDel() {
            $(targetFormID).attr("action", "@Url.Action("DelCkEditSave", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        function onBack() {
            $(targetFormID).attr("action", "@Url.Action(CkEditorAction, (string)ViewBag.BRE_NO
                                           ,new {
                                               CKEditorFuncNum = Model.Search.CKEditorFuncNum,
                                               CKEditor = Model.Search.CKEditor,
                                               langCode = Model.Search.langCode,
                                               Controller= Model.Search.BackController,
                                               Action = Model.Search.BackAction
                                           })")
            $(targetFormID).submit();
        }
    </script>
}



