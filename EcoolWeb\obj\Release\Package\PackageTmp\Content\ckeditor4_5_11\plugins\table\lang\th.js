﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'table', 'th', {
	border: 'ขนาดเส้นขอบ',
	caption: 'หัวเรื่องของตาราง',
	cell: {
		menu: 'ช่องตาราง',
		insertBefore: 'Insert Cell Before',
		insertAfter: 'Insert Cell After',
		deleteCell: 'ลบช่อง',
		merge: 'ผสานช่อง',
		mergeRight: 'Merge Right',
		mergeDown: 'Merge Down',
		splitHorizontal: 'Split Cell Horizontally',
		splitVertical: 'Split Cell Vertically',
		title: 'Cell Properties',
		cellType: 'Cell Type',
		rowSpan: 'Rows Span',
		colSpan: 'Columns Span',
		wordWrap: 'Word Wrap',
		hAlign: 'Horizontal Alignment',
		vAlign: 'Vertical Alignment',
		alignBaseline: 'Baseline',
		bgColor: 'Background Color',
		borderColor: 'Border Color',
		data: 'Data',
		header: 'Header',
		yes: 'Yes',
		no: 'No',
		invalidWidth: 'Cell width must be a number.',
		invalidHeight: 'Cell height must be a number.',
		invalidRowSpan: 'Rows span must be a whole number.',
		invalidColSpan: 'Columns span must be a whole number.',
		chooseColor: 'Choose'
	},
	cellPad: 'ระยะแนวตั้ง',
	cellSpace: 'ระยะแนวนอนน',
	column: {
		menu: 'คอลัมน์',
		insertBefore: 'Insert Column Before',
		insertAfter: 'Insert Column After',
		deleteColumn: 'ลบสดมน์'
	},
	columns: 'สดมน์',
	deleteTable: 'ลบตาราง',
	headers: 'ส่วนหัว',
	headersBoth: 'ทั้งสองอย่าง',
	headersColumn: 'คอลัมน์แรก',
	headersNone: 'None',
	headersRow: 'แถวแรก',
	invalidBorder: 'ขนาดเส้นกรอบต้องเป็นจำนวนตัวเลข',
	invalidCellPadding: 'ช่องว่างภายในเซลล์ต้องเลขจำนวนบวก',
	invalidCellSpacing: 'ช่องว่างภายในเซลล์ต้องเป็นเลขจำนวนบวก',
	invalidCols: 'จำนวนคอลัมน์ต้องเป็นจำนวนมากกว่า 0',
	invalidHeight: 'ส่วนสูงของตารางต้องเป็นตัวเลข',
	invalidRows: 'จำนวนของแถวต้องเป็นจำนวนมากกว่า 0',
	invalidWidth: 'ความกว้างตารางต้องเป็นตัวเลข',
	menu: 'คุณสมบัติของ ตาราง',
	row: {
		menu: 'แถว',
		insertBefore: 'Insert Row Before',
		insertAfter: 'Insert Row After',
		deleteRow: 'ลบแถว'
	},
	rows: 'แถว',
	summary: 'สรุปความ',
	title: 'คุณสมบัติของ ตาราง',
	toolbar: 'ตาราง',
	widthPc: 'เปอร์เซ็น',
	widthPx: 'จุดสี',
	widthUnit: 'หน่วยความกว้าง'
} );
