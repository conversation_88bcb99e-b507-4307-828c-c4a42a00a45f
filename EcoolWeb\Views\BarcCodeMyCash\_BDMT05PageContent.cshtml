﻿
@model ECOOL_APP.EF.BDMT05IndexViewModel

@{
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
}

@Html.HiddenFor(m => m.Page)


@*<div class="form-inline text-right" role="form" id="search">
    <div class="form-group">
        @Html.EditorFor(m => m.Search.whereSearch, new { htmlAttributes = new { @class = "form-control input-sm", @placeholder = "輸入要查詢的網站" } })
    </div>
    <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1)" />

</div>*@

<div class="table-responsive">
    <table class="table table-striped">
        <thead>
            <tr>
                <th></th>
                <th>@Html.DisplayNameFor(m => m.ListData.First().GAME_NAME)</th>
                <th>@Html.DisplayNameFor(m => m.ListData.First().LINK_URL)</th>
                <th>@Html.DisplayNameFor(m => m.ListData.First().CASH)</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var item in Model.ListData)
            {

                <tr>
                    <td align="center">

                        <a class="btn btn-default" onclick="onBtnDetails('@item.GAME_NO')"><em class="fa fa-pencil"></em></a>
                        <a class="btn btn-danger" onclick="delete_show('@item.GAME_NO')">
                            <em class="fa fa-trash"></em>
                        </a>

                    </td>
                    <td>@Html.DisplayFor(modelItem => item.GAME_NAME)</td>
                    <td>@Html.DisplayFor(modelItem => item.LINK_URL)</td>
                    <td>@Html.DisplayFor(modelItem => item.CASH)</td>
                </tr>
            }
        </tbody>
    </table>
</div>
@Html.Pager(Model.ListData.PageSize, Model.ListData.PageNumber, Model.ListData.TotalItemCount).Options(o => o
                            .DisplayTemplate(PageGlobal.DfDisplayTemplate)
                            .MaxNrOfPages(PageGlobal.DfMaxNrOfPages)
                            .SetPreviousPageText(PageGlobal.DfSetPreviousPageText)
                            .SetNextPageText(PageGlobal.DfSetNextPageText)
                            )
