﻿// JavaScript for ADDI09 QuerySelectData - 查詢選擇資料模組
// 整理自 _QuerySelectDataList.cshtml 的所有 JavaScript 功能
$(document).ready(function () {
    const QuerySelectModule = {
        // 配置選項
        config: {
            defaultPageSize: 10,
            maxPageSize: 100,
            searchDelay: 300,
            fixedHeight: 300
        },

        // 當前狀態
        state: {
            currentPage: 1,
            totalPages: 0,
            totalRecords: 0,
            searchKeyword: '',
            sortColumn: '',
            sortDirection: 'asc',
            selectedItems: [],
            isLoading: false,
            timerr: 0
        },

        init: function () {
            this.setupGlobalFunctions();
            this.bindEvents();
            this.initializeComponents();
        },

        bindEvents: function () {
            // 綁定搜尋相關事件
            this.bindSearchEvents();

            // 綁定分頁相關事件
            this.bindPaginationEvents();

            // 綁定排序相關事件
            this.bindSortEvents();

            // 綁定選擇相關事件
            this.bindSelectionEvents();

            // 綁定表單相關事件
            this.bindFormEvents();

            // 綁定數字操作事件
            this.bindNumberOperationEvents();

            // 綁定點數變更事件
            this.bindCashChangeEvents();
        },

        setupGlobalFunctions: function () {
            try {
                // 設置全局函數以保持向後兼容
                window.QuerySelectData_Search = this.performSearch.bind(this);
                window.QuerySelectData_Sort = this.performSort.bind(this);
                window.QuerySelectData_Page = this.changePage.bind(this);
                window.QuerySelectData_Select = this.selectItem.bind(this);
                window.QuerySelectData_Clear = this.clearSelection.bind(this);
                window.QuerySelectData_GetSelected = this.getSelectedItems.bind(this);

                // 從 _QuerySelectDataList.cshtml 整理的全局函數
                window.onBtnLink = this.onBtnLink.bind(this);
                window.setDivHeight = this.setDivHeight.bind(this);
                window.addNum = this.addNum.bind(this);
                window.delNum = this.delNum.bind(this);
                window.formatNum = this.formatNum.bind(this);
                window.sweetAutoImport = this.sweetAutoImport.bind(this);
                window.changeCash = this.changeCash.bind(this);
                window.DivHeight = this.divHeight.bind(this);
                window.ChangeCashAJAX = this.changeCashAJAX.bind(this);
                window.plus = this.plus.bind(this);
                window.setTime = this.setTime.bind(this);

                // 確認全局函數已設置
                console.log('QuerySelectData 全局函數已設置:', {
                    onBtnLink: typeof window.onBtnLink,
                    addNum: typeof window.addNum,
                    delNum: typeof window.delNum,
                    formatNum: typeof window.formatNum,
                    sweetAutoImport: typeof window.sweetAutoImport,
                    changeCash: typeof window.changeCash,
                    DivHeight: typeof window.DivHeight,
                    ChangeCashAJAX: typeof window.ChangeCashAJAX
                });

            } catch (error) {
                console.error('QuerySelectData 設置全局函數時發生錯誤:', error);
            }
        },

        initializeComponents: function () {
            try {
                console.log('QuerySelectData 模組初始化');

                // 執行 DivHeight (對應原始的 DivHeight() 調用)
                this.divHeight();

                // 初始化搜尋框
                this.initializeSearchBox();

                // 初始化分頁控制項
                this.initializePagination();

                // 初始化排序控制項
                this.initializeSorting();

                // 初始化選擇控制項
                this.initializeSelection();

                // 初始化高度控制
                this.initializeDivHeight();

                // 初始化全選功能
                this.initializeCheckAll();

                // 初始化編輯按鈕事件
                this.initializeEditButton();

                // 載入初始資料
                this.loadInitialData();

            } catch (error) {
                console.error('QuerySelectData 初始化時發生錯誤:', error);
            }
        },

        // ==================== 從 _QuerySelectDataList.cshtml 整理的核心功能 ====================

        // DivHeight 功能 (對應原始的 DivHeight 函數)
        divHeight: function() {
            try {
                const fixedHeight = this.config.fixedHeight;
                const height = $('#DivSelectData').innerHeight();
                let divHeight = $('#DivHeight').val();

                if (divHeight === '') {
                    divHeight = window.ADDI09_CONSTANTS ? window.ADDI09_CONSTANTS.DivHeightM : 'M';
                }

                $('#DivHeight_plus').hide();
                $('#DivHeight_minus').hide();

                if (divHeight === "-") {
                    $('#DivHeight_plus').show();
                } else {
                    $('#DivHeight_minus').show();
                }

                if (height > fixedHeight && divHeight === "-") {
                    $('#DivSelectData').css("height", fixedHeight).css("overflow", "auto");
                } else {
                    $('#DivSelectData').removeClass();
                }

            } catch (error) {
                console.error('QuerySelectData divHeight 時發生錯誤:', error);
            }
        },

        // onBtnLink 功能 (對應原始的 onBtnLink 函數)
        onBtnLink: function(userNo, dataType) {
            try {
                console.log('onBtnLink 被調用:', { userNo, dataType });

                const showType = $('#ShowType').val();
                console.log('當前 ShowType:', showType);

                if (dataType === '') {
                    const showTypeQSV = window.ADDI09_CONSTANTS ? window.ADDI09_CONSTANTS.ShowTypeQSV : 'ShowTypeQSV';
                    const dataTypeD = window.ADDI09_CONSTANTS ? window.ADDI09_CONSTANTS.DataTypeD : 'DataTypeD';

                    if (showType === showTypeQSV) {
                        dataType = dataTypeD;
                    }
                }

                const data = {
                    "SYS_TABLE_TYPE": this.getSysTableType(),
                    "ADDT14_STYLE": this.getAddt14Style(),
                    "ShowType": showType,
                    "DivHeight": $('#DivHeight').val(),
                    "USER_NO": userNo,
                    "DataType": dataType
                };

                console.log('onBtnLink 發送資料:', data);

                const urls = this.getUrls();
                console.log('使用的 URL:', urls.querySelectDataList);

                if (!urls.querySelectDataList) {
                    console.error('找不到 querySelectDataList URL');
                    this.showMessage('系統配置錯誤：找不到查詢URL', 'error');
                    return;
                }

                $.ajax({
                    url: urls.querySelectDataList,
                    data: data,
                    type: 'post',
                    async: false,
                    cache: false,
                    dataType: 'html',
                    success: function (response) {
                        console.log('onBtnLink AJAX 成功');
                        $('#QuerySelectDataList').html(response);
                        const showTypeQSV = window.ADDI09_CONSTANTS ? window.ADDI09_CONSTANTS.ShowTypeQSV : 'ShowTypeQSV';
                        if (showType === showTypeQSV) {
                            this.funAjax();
                        }
                    }.bind(this),
                    error: function(xhr, status, error) {
                        console.error('QuerySelectData onBtnLink AJAX 錯誤:', { status, error, xhr });
                        this.showMessage('操作失敗，請稍後再試', 'error');
                    }.bind(this)
                });

            } catch (error) {
                console.error('QuerySelectData onBtnLink 時發生錯誤:', error);
                this.showMessage('操作時發生錯誤，請稍後再試', 'error');
            }
        },

        // setDivHeight 功能 (對應原始的 setDivHeight 函數)
        setDivHeight: function(value) {
            try {
                $('#DivHeight').val(value);
                this.onBtnLink('', '');
            } catch (error) {
                console.error('QuerySelectData setDivHeight 時發生錯誤:', error);
            }
        },

        // addNum 功能 (對應原始的 addNum 函數)
        addNum: function(inputId, userNo) {
            try {
                console.log("addNum:", inputId);
                const selector = "#" + inputId;
                let inputNum = parseInt($(selector).val()) || 0;
                $(selector).val(++inputNum);
                this.changeCashAJAX(false, userNo, $(selector).val());
            } catch (error) {
                console.error('QuerySelectData addNum 時發生錯誤:', error);
            }
        },

        // delNum 功能 (對應原始的 delNum 函數)
        delNum: function(inputId, userNo) {
            try {
                const selector = "#" + inputId;
                let inputNum = parseInt($(selector).val()) || 0;
                $(selector).val(--inputNum);
                console.log(typeof inputNum);
                this.changeCashAJAX(false, userNo, $(selector).val());
            } catch (error) {
                console.error('QuerySelectData delNum 時發生錯誤:', error);
            }
        },

        // formatNum 功能 (對應原始的 formatNum 函數)
        formatNum: function(inputId, userNo) {
            try {
                const selector = "#" + inputId;
                $(selector).val(0);
                this.changeCashAJAX(false, userNo, $(selector).val());
            } catch (error) {
                console.error('QuerySelectData formatNum 時發生錯誤:', error);
            }
        },

        // plus 功能 (對應原始的 plus 函數)
        plus: function() {
            try {
                let inputNum = parseInt($(".inputNum").val()) || 0;
                $(".inputNum").val(++inputNum);
                console.log(typeof inputNum);
            } catch (error) {
                console.error('QuerySelectData plus 時發生錯誤:', error);
            }
        },

        // sweetAutoImport 功能 (對應原始的 sweetAutoImport 函數)
        sweetAutoImport: function() {
            try {
                this.changeCashAJAX(true, '', '', '');
            } catch (error) {
                console.error('QuerySelectData sweetAutoImport 時發生錯誤:', error);
            }
        },

        // changeCash 功能 (對應原始的 changeCash 函數)
        changeCash: function(event, userNo, loginUserNo) {
            try {
                console.log('changeCash:', event.target);
                this.changeCashAJAX(false, userNo, event.target.value);
            } catch (error) {
                console.error('QuerySelectData changeCash 時發生錯誤:', error);
            }
        },

        // setTime 功能 (對應原始的 setTime 函數)
        setTime: function() {
            try {
                this.state.timerr++;
                if (this.state.timerr < 70) {
                    $("#barr").css("width", this.state.timerr + "%");
                }
            } catch (error) {
                console.error('QuerySelectData setTime 時發生錯誤:', error);
            }
        },

        // ChangeCashAJAX 功能 (對應原始的 ChangeCashAJAX 函數)
        changeCashAJAX: function(isAll, userNo, cash, extra) {
            try {
                let data = {
                    vm: []
                };

                // 全部改動
                if (isAll) {
                    const dataList = this.getDataList();
                    const sweetCash = $("#sweetAutoCash").val();

                    $.each(dataList, function (idx, item) {
                        data.vm.push({ User_No: item.USER_NO, cash: sweetCash });
                    });
                } else {
                    data.vm.push({ User_No: userNo, cash: cash });
                }

                const urls = this.getUrls();

                // AJAX 請求
                $.ajax({
                    url: urls.changeTempCash,
                    data: data,
                    type: 'post',
                    async: false,
                    cache: false,
                    dataType: 'html',
                    success: function () {
                        if (isAll) {
                            console.log("onBtnLink after changeCashAJAX");
                            this.onBtnLink('', '');
                        }
                    }.bind(this),
                    error: function(xhr, status, error) {
                        console.error('QuerySelectData changeCashAJAX 錯誤:', error);
                        this.showMessage('變更點數失敗，請稍後再試', 'error');
                    }.bind(this)
                });

            } catch (error) {
                console.error('QuerySelectData changeCashAJAX 時發生錯誤:', error);
                this.showMessage('變更點數時發生錯誤，請稍後再試', 'error');
            }
        },

        // ==================== 事件綁定方法 ====================

        // 綁定搜尋相關事件
        bindSearchEvents: function () {
            // 基本的搜尋事件綁定
            try {
                // 可以在這裡添加搜尋相關的事件綁定
            } catch (error) {
                console.error('QuerySelectData 綁定搜尋事件時發生錯誤:', error);
            }
        },

        // 綁定分頁相關事件
        bindPaginationEvents: function () {
            try {
                // 可以在這裡添加分頁相關的事件綁定
            } catch (error) {
                console.error('QuerySelectData 綁定分頁事件時發生錯誤:', error);
            }
        },

        // 綁定排序相關事件
        bindSortEvents: function () {
            try {
                // 可以在這裡添加排序相關的事件綁定
            } catch (error) {
                console.error('QuerySelectData 綁定排序事件時發生錯誤:', error);
            }
        },

        // 綁定選擇相關事件
        bindSelectionEvents: function () {
            try {
                // 綁定表格行點擊事件 (使用事件委派處理動態生成的元素)
                $(document).on('click', 'tr[onclick*="onBtnLink"]', function(event) {
                    event.preventDefault();
                    const onclick = $(this).attr('onclick');
                    if (onclick) {
                        // 解析 onclick 屬性中的參數
                        const match = onclick.match(/onBtnLink\('([^']*)',\s*'([^']*)'\)/);
                        if (match) {
                            console.log('表格行點擊:', match[1], match[2]);
                            this.onBtnLink(match[1], match[2]);
                        }
                    }
                }.bind(this));

                // 綁定高度控制按鈕事件 (使用事件委派)
                $(document).on('click', 'button[onclick*="setDivHeight"]', function(event) {
                    event.preventDefault();
                    const onclick = $(this).attr('onclick');
                    if (onclick) {
                        const match = onclick.match(/setDivHeight\('([^']*)'\)/);
                        if (match) {
                            console.log('高度控制按鈕點擊:', match[1]);
                            this.setDivHeight(match[1]);
                        }
                    }
                }.bind(this));

            } catch (error) {
                console.error('QuerySelectData 綁定選擇事件時發生錯誤:', error);
            }
        },

        // 綁定表單相關事件
        bindFormEvents: function () {
            try {
                // 可以在這裡添加表單相關的事件綁定
            } catch (error) {
                console.error('QuerySelectData 綁定表單事件時發生錯誤:', error);
            }
        },

        // 綁定數字操作按鈕事件
        bindNumberOperationEvents: function() {
            try {
                // 綁定 +1 按鈕
                $(document).on('click', '.jq-addNum', function(event) {
                    event.preventDefault();
                    const onclick = $(this).attr('onclick');
                    if (onclick) {
                        const match = onclick.match(/addNum\('([^']*)',\s*'([^']*)'\)/);
                        if (match) {
                            this.addNum(match[1], match[2]);
                        }
                    }
                }.bind(this));

                // 綁定 -1 按鈕
                $(document).on('click', '.jq-delNum', function(event) {
                    event.preventDefault();
                    const onclick = $(this).attr('onclick');
                    if (onclick) {
                        const match = onclick.match(/delNum\('([^']*)',\s*'([^']*)'\)/);
                        if (match) {
                            this.delNum(match[1], match[2]);
                        }
                    }
                }.bind(this));

                // 綁定 zero 按鈕
                $(document).on('click', '.jq-format', function(event) {
                    event.preventDefault();
                    const onclick = $(this).attr('onclick');
                    if (onclick) {
                        const match = onclick.match(/formatNum\('([^']*)',\s*'([^']*)'\)/);
                        if (match) {
                            this.formatNum(match[1], match[2]);
                        }
                    }
                }.bind(this));

                // 綁定批次自動匯入按鈕
                $(document).on('click', 'button[onclick*="sweetAutoImport"]', function(event) {
                    event.preventDefault();
                    this.sweetAutoImport();
                }.bind(this));

            } catch (error) {
                console.error('QuerySelectData 綁定數字操作事件時發生錯誤:', error);
            }
        },

        // 綁定點數變更事件
        bindCashChangeEvents: function() {
            try {
                // 綁定點數輸入框變更事件
                $(document).on('change', 'input[onchange*="changeCash"]', function(event) {
                    const onchange = $(this).attr('onchange');
                    if (onchange) {
                        // 解析 onchange 屬性中的參數
                        const match = onchange.match(/changeCash\(event,\s*'([^']*)',\s*'([^']*)'\)/);
                        if (match) {
                            this.changeCash(event, match[1], match[2]);
                        }
                    }
                }.bind(this));

            } catch (error) {
                console.error('QuerySelectData 綁定點數變更事件時發生錯誤:', error);
            }
        },

        // ==================== 初始化方法 ====================

        // 初始化高度控制
        initializeDivHeight: function() {
            try {
                // 綁定高度控制按鈕事件
                $('#DivHeight_plus').on('click', function() {
                    this.setDivHeight('+');
                }.bind(this));

                $('#DivHeight_minus').on('click', function() {
                    this.setDivHeight('-');
                }.bind(this));

            } catch (error) {
                console.error('QuerySelectData 初始化高度控制時發生錯誤:', error);
            }
        },

        // 初始化全選功能 (對應原始的 $("#chkALL").click)
        initializeCheckAll: function() {
            try {
                $("#chkALL").on('click', function () {
                    if ($("#chkALL").prop("checked")) {
                        $("input:checkbox").each(function () {
                            if ($(this).attr("id") !== 'cbPicture') {
                                $(this).prop("checked", true);
                            }
                        });
                    } else {
                        $("input:checkbox").each(function () {
                            if ($(this).attr("id") !== 'cbPicture') {
                                $(this).prop("checked", false);
                            }
                        });
                    }
                }.bind(this));

            } catch (error) {
                console.error('QuerySelectData 初始化全選功能時發生錯誤:', error);
            }
        },

        // 初始化編輯按鈕 (對應原始的 $('#EditmyButton').on('click'))
        initializeEditButton: function() {
            try {
                $('#EditmyButton').on('click', function () {
                    const urls = this.getUrls();
                    const data = this.getFormData();

                    $.ajax({
                        url: urls.edit,
                        data: data,
                        type: 'post',
                        async: false,
                        cache: false,
                        dataType: 'html',
                        success: function (response) {
                            $('#QuerySelectDataList').html(response);
                            const showType = $('#ShowType').val();
                            if (showType === 'ShowTypeQSV') {
                                this.funAjax();
                            }
                        }.bind(this),
                        error: function(xhr, status, error) {
                            console.error('QuerySelectData 編輯按鈕錯誤:', error);
                            this.showMessage('編輯操作失敗，請稍後再試', 'error');
                        }.bind(this)
                    });

                    // 提交表單
                    if (document.forms.form1) {
                        document.forms.form1.submit();
                    }
                }.bind(this));

            } catch (error) {
                console.error('QuerySelectData 初始化編輯按鈕時發生錯誤:', error);
            }
        },

        // 其他初始化方法
        initializeSearchBox: function () {
            try {
                // 搜尋框初始化邏輯
            } catch (error) {
                console.error('QuerySelectData 初始化搜尋框時發生錯誤:', error);
            }
        },

        initializePagination: function () {
            try {
                // 分頁初始化邏輯
            } catch (error) {
                console.error('QuerySelectData 初始化分頁時發生錯誤:', error);
            }
        },

        initializeSorting: function () {
            try {
                // 排序初始化邏輯
            } catch (error) {
                console.error('QuerySelectData 初始化排序時發生錯誤:', error);
            }
        },

        initializeSelection: function () {
            try {
                // 選擇初始化邏輯
            } catch (error) {
                console.error('QuerySelectData 初始化選擇時發生錯誤:', error);
            }
        },

        loadInitialData: function () {
            try {
                // 載入初始資料邏輯
            } catch (error) {
                console.error('QuerySelectData 載入初始資料時發生錯誤:', error);
            }
        },

        // ==================== 輔助方法 ====================

        // 獲取系統表格類型
        getSysTableType: function() {
            return window.ADDI09_SYS_TABLE_TYPE || $('#SYS_TABLE_TYPE').val() || '';
        },

        // 獲取 ADDT14 樣式
        getAddt14Style: function() {
            return window.ADDI09_ADDT14_STYLE || $('#ADDT14_STYLE').val() || '';
        },

        // 獲取資料清單
        getDataList: function() {
            // 嘗試從全局變數獲取資料清單
            if (window.ADDI09_DATA_LIST) {
                return window.ADDI09_DATA_LIST;
            }

            // 嘗試從 JSON 解析 (對應原始的 JSON.parse)
            try {
                const dataListElement = $('[data-datalist]');
                if (dataListElement.length > 0) {
                    return JSON.parse(dataListElement.attr('data-datalist'));
                }
            } catch (e) {
                console.warn('無法解析資料清單:', e);
            }

            // 如果都沒有，返回空陣列
            return [];
        },

        // 獲取表單資料
        getFormData: function() {
            return {
                SYS_TABLE_TYPE: this.getSysTableType(),
                ADDT14_STYLE: this.getAddt14Style(),
                ShowType: $('#ShowType').val(),
                DivHeight: $('#DivHeight').val()
            };
        },

        // funAjax 方法 (對應原始的 funAjax 調用)
        funAjax: function() {
            try {
                console.log('QuerySelectData funAjax 被調用');
                // 可以在這裡添加特定的AJAX邏輯
            } catch (error) {
                console.error('QuerySelectData funAjax 時發生錯誤:', error);
            }
        },

        // 顯示訊息
        showMessage: function (message, type = 'info') {
            try {
                // 可以整合現有的訊息顯示系統
                if (typeof ADDI09Common !== 'undefined' && ADDI09Common.showMessage) {
                    ADDI09Common.showMessage(message, type);
                } else {
                    // 預設使用alert
                    alert(message);
                }
            } catch (error) {
                console.error('QuerySelectData 顯示訊息時發生錯誤:', error);
                alert(message);
            }
        },

        // 獲取URL配置
        getUrls: function () {
            return window.ADDI09_QUERY_SELECT_URLS ||
                   window.ADDI09_URLS ||
                   {
                       queryData: '/ADDI09/QueryData',
                       selectData: '/ADDI09/SelectData',
                       querySelectDataList: window.ADDI09_URLS ? window.ADDI09_URLS.querySelectDataList : '',
                       changeTempCash: window.ADDI09_URLS ? window.ADDI09_URLS.changeTempCash : '',
                       edit: window.ADDI09_URLS ? window.ADDI09_URLS.edit : ''
                   };
        },

        // 其他查詢選擇功能的佔位方法
        performSearch: function (keyword) {
            console.log('QuerySelectData performSearch:', keyword);
        },

        performSort: function (column) {
            console.log('QuerySelectData performSort:', column);
        },

        changePage: function (page) {
            console.log('QuerySelectData changePage:', page);
        },

        selectItem: function (itemId, isSelected) {
            console.log('QuerySelectData selectItem:', itemId, isSelected);
        },

        clearSelection: function () {
            console.log('QuerySelectData clearSelection');
        },

        getSelectedItems: function () {
            return this.state.selectedItems.slice();
        }
    };

    // 初始化模組
    QuerySelectModule.init();

    // 將模組暴露到全局，方便調試
    window.QuerySelectModule = QuerySelectModule;

    // 設置全局錯誤處理
    window.addEventListener('error', function (e) {
        console.error('QuerySelectData 頁面錯誤:', e);
    });

    console.log('QuerySelectData.js 已載入完成');
});

// 立即執行的全局函數設置 (確保在 DOM 載入前就可用)
(function() {
    'use strict';

    // 臨時的全局函數，直到模組完全載入
    window.onBtnLink = function(userNo, dataType) {
        console.log('臨時 onBtnLink 被調用:', userNo, dataType);

        // 等待模組載入完成
        const checkModule = function() {
            if (window.QuerySelectModule && window.QuerySelectModule.onBtnLink) {
                console.log('使用模組的 onBtnLink');
                window.QuerySelectModule.onBtnLink(userNo, dataType);
            } else {
                console.log('模組尚未載入，等待中...');
                setTimeout(checkModule, 100);
            }
        };

        checkModule();
    };

    // 其他重要的全局函數也做類似處理
    window.addNum = function(inputId, userNo) {
        if (window.QuerySelectModule && window.QuerySelectModule.addNum) {
            window.QuerySelectModule.addNum(inputId, userNo);
        } else {
            console.warn('addNum: 模組尚未載入');
        }
    };

    window.delNum = function(inputId, userNo) {
        if (window.QuerySelectModule && window.QuerySelectModule.delNum) {
            window.QuerySelectModule.delNum(inputId, userNo);
        } else {
            console.warn('delNum: 模組尚未載入');
        }
    };

    window.formatNum = function(inputId, userNo) {
        if (window.QuerySelectModule && window.QuerySelectModule.formatNum) {
            window.QuerySelectModule.formatNum(inputId, userNo);
        } else {
            console.warn('formatNum: 模組尚未載入');
        }
    };

    window.sweetAutoImport = function() {
        if (window.QuerySelectModule && window.QuerySelectModule.sweetAutoImport) {
            window.QuerySelectModule.sweetAutoImport();
        } else {
            console.warn('sweetAutoImport: 模組尚未載入');
        }
    };

    window.changeCash = function(event, userNo, loginUserNo) {
        if (window.QuerySelectModule && window.QuerySelectModule.changeCash) {
            window.QuerySelectModule.changeCash(event, userNo, loginUserNo);
        } else {
            console.warn('changeCash: 模組尚未載入');
        }
    };

    window.setDivHeight = function(value) {
        if (window.QuerySelectModule && window.QuerySelectModule.setDivHeight) {
            window.QuerySelectModule.setDivHeight(value);
        } else {
            console.warn('setDivHeight: 模組尚未載入');
        }
    };

    window.DivHeight = function() {
        if (window.QuerySelectModule && window.QuerySelectModule.divHeight) {
            window.QuerySelectModule.divHeight();
        } else {
            console.warn('DivHeight: 模組尚未載入');
        }
    };

    console.log('QuerySelectData 臨時全局函數已設置');
})();