﻿@model EcoolWeb.ViewModels.ZZZI18QueryListViewModel
@{
    ViewBag.Title = "e酷幣異常人員清單";
    int i = 0;
}
<style>
    .table thead > tr > th, .table tbody > tr > th, .table tfoot > tr > th, .table thead > tr > td, .table tbody > tr > td, .table tfoot > tr > td {
        border-top: none;
    }
</style>
@section scripts{
    <script>

        function exportExcel() {

            var sHtml = htmlEncode($("#OutList")[0].outerHTML);//做html編碼

            $("input[name='hHtml']").val(sHtml);

            //表單提交
            document.ZZZI18.enctype = "multipart/form-data";
            document.ZZZI18.action = "ExportExcel";
            document.ZZZI18.submit();
            //$("form[name='AWA004']").submit();
        }
        //↓出自：http://stackoverflow.com/questions/1219860/javascript-jquery-html-encoding
        function htmlEncode(value) {
            //create a in-memory div, set it's inner text(which j<PERSON><PERSON><PERSON> automatically encodes)
            //then grab the encoded contents back out.  The div never exists on the page.
            return $('<div/>').text(value).html();
        }

        function btnSend_onclick() {
            document.ZZZI18.enctype = "multipart/form-data";
            document.ZZZI18.action = "Modify";
            document.ZZZI18.submit();
        }

        function btnSearch_onclick() {
            document.ZZZI18.enctype = "multipart/form-data";
            document.ZZZI18.action = "Query";
            document.ZZZI18.submit();
        }

        $("#chkALL").click(function () {

            if ($("#chkALL").prop("checked")) {
                $("input:checkbox").each(function () {
                    $(this).prop("checked", true);
                });
            }
            else {
                $("input:checkbox").each(function () {
                    $(this).prop("checked", false);
                });
            }
        });

        $(function () {

            // Fields
            var _pageLinkers = $(".pager> a");

            // Binding click event
            _pageLinkers.each(function (i, item) {
                var page = getParameterByName($(item).attr('href'), 'page')
                $(item).attr('href', '#').click(function () { postPage(page); });
            });

        });

        function getParameterByName(url, name) {
            name = name.replace(/[\[]/, "\\[").replace(/[\]]/, "\\]");
            var regex = new RegExp("[\\?&]" + name + "=([^&#]*)"),
                results = regex.exec(url);
            return results === null ? "" : decodeURIComponent(results[1].replace(/\+/g, " "));
        }

        function postPage(page) {
            var targetFormID = '#ZZZI18';
            if ($(targetFormID).size() > 0) {
                $('<input>')
                    .attr({ type: 'hidden', id: 'page', name: 'page', value: page })
                    .appendTo($(targetFormID));
                $(targetFormID).submit();
            }
        };

    </script>
}

@using (Html.BeginForm("Query", "ZZZI18", FormMethod.Post, new { NAME = "ZZZI18", id = "ZZZI18" }))
{
    @*
        <span>搜尋瀏覽請輸入相關字串[學號/姓名]:</span>
        @Html.EditorFor(m => m.whereKeyword)
        <input type="submit" value="搜尋" />
    *@
    <br />
    <div class="pager">
        @Html.Pager(Model.uZZZI18List.PageSize, Model.uZZZI18List.PageNumber, Model.uZZZI18List.TotalItemCount)
        @*@Html.Pager(Model.ADDT01List.PageSize,Model.ADDT01List.PageNumber,Model.ADDT01List.TotalItemCount).Options(o=>o.AddRouteValueFor(m=>m.whereKeyword))*@
        @*Displaying @<EMAIL> of @Model.ADDT01List.TotalItemCount item(s)*@
        @Html.Hidden("hHtml")
    </div>

    <div class="col-md-2">
        <input type="button" id="btnSend" value="已確認人員" class="btn-primary btn btn-sm" onclick="btnSend_onclick();" />
        @*<input type="button" id="btnExcel" value="匯出Excel" class="btn-primary btn btn-sm" onclick="exportExcel();" />*@
    </div>
    <h4>e酷幣異常人員清單</h4>

    <table class="table" style="white-space: nowrap;" id="OutList">
        <tr class="ListColName">
            <th style="text-align: center;">
                處理狀態
                <input type="checkbox" id="chkALL" name="chkALL" />
            </th>
            <td align="center">
                學校
            </td>
            <td align="center">
                姓名
            </td>
        </tr>

        @foreach (var item in Model.uZZZI18List)
        {
            <tr class="ListRow">
                <td>
                    <input type="checkbox" id='[@i].chkWFT05_NO' name='[@i].chkWFT05_NO'>
                    <input type="hidden" id='[@i].WFT05_NO' name='[@i].WFT05_NO' value=@item.WFT05_NO />
                    @*@Html.CheckBox("check", new { value = item.TRANS_NO.ToString(), id = item.TRANS_NO.ToString() })*@
                </td>
                <td align="center">
                    @Html.DisplayFor(modelItem => item.SCHOOL_NAME)
                </td>
                <td align="center">
                    @Html.ActionLink(item.SNAME, "Query", "AWA002", new { SCHOOL_NO = item.SCHOOL_NO, USER_NO = item.USER_NO }, new { @style = "color:blue;" })
                </td>
            </tr>
            i++;
        }

    </table>
}  