﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'table', 'hi', {
	border: 'बॉर्डर साइज़',
	caption: 'शीर्षक',
	cell: {
		menu: 'खाना',
		insertBefore: 'पहले सैल डालें',
		insertAfter: 'बाद में सैल डालें',
		deleteCell: 'सैल डिलीट करें',
		merge: 'सैल मिलायें',
		mergeRight: 'बाँया विलय',
		mergeDown: 'नीचे विलय करें',
		splitHorizontal: 'सैल को क्षैतिज स्थिति में विभाजित करें',
		splitVertical: 'सैल को लम्बाकार में विभाजित करें',
		title: 'Cell Properties',
		cellType: 'Cell Type',
		rowSpan: 'Rows Span',
		colSpan: 'Columns Span',
		wordWrap: 'Word Wrap',
		hAlign: 'Horizontal Alignment',
		vAlign: 'Vertical Alignment',
		alignBaseline: 'Baseline',
		bgColor: 'Background Color',
		borderColor: 'Border Color',
		data: 'Data',
		header: 'Header',
		yes: 'Yes',
		no: 'No',
		invalidWidth: 'Cell width must be a number.',
		invalidHeight: 'Cell height must be a number.',
		invalidRowSpan: 'Rows span must be a whole number.',
		invalidColSpan: 'Columns span must be a whole number.',
		chooseColor: 'Choose'
	},
	cellPad: 'सैल पैडिंग',
	cellSpace: 'सैल अंतर',
	column: {
		menu: 'कालम',
		insertBefore: 'पहले कालम डालें',
		insertAfter: 'बाद में कालम डालें',
		deleteColumn: 'कालम डिलीट करें'
	},
	columns: 'कालम',
	deleteTable: 'टेबल डिलीट करें',
	headers: 'Headers', // MISSING
	headersBoth: 'Both', // MISSING
	headersColumn: 'First column', // MISSING
	headersNone: 'None',
	headersRow: 'First Row', // MISSING
	invalidBorder: 'Border size must be a number.', // MISSING
	invalidCellPadding: 'Cell padding must be a positive number.', // MISSING
	invalidCellSpacing: 'Cell spacing must be a positive number.', // MISSING
	invalidCols: 'Number of columns must be a number greater than 0.', // MISSING
	invalidHeight: 'Table height must be a number.', // MISSING
	invalidRows: 'Number of rows must be a number greater than 0.', // MISSING
	invalidWidth: 'Table width must be a number.', // MISSING
	menu: 'टेबल प्रॉपर्टीज़',
	row: {
		menu: 'पंक्ति',
		insertBefore: 'पहले पंक्ति डालें',
		insertAfter: 'बाद में पंक्ति डालें',
		deleteRow: 'पंक्तियाँ डिलीट करें'
	},
	rows: 'पंक्तियाँ',
	summary: 'सारांश',
	title: 'टेबल प्रॉपर्टीज़',
	toolbar: 'टेबल',
	widthPc: 'प्रतिशत',
	widthPx: 'पिक्सैल',
	widthUnit: 'width unit' // MISSING
} );
