/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Latin-Modern/Size1/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.LatinModernMathJax_Size1={directory:"Size1/Regular",family:"LatinModernMathJax_Size1",testString:"\u00A0\u0302\u0303\u0305\u0306\u030C\u0311\u032C\u032D\u032E\u032F\u0330\u0332\u0333\u033F",32:[0,0,332,0,0],40:[797,297,422,116,362],41:[797,297,422,60,306],47:[905,405,617,56,561],91:[800,300,340,161,313],92:[905,405,617,56,561],93:[800,300,340,27,179],123:[800,300,540,86,454],124:[851,351,278,117,161],125:[800,300,540,86,454],160:[0,0,332,0,0],770:[746,-572,644,0,644],771:[751,-550,652,0,652],773:[670,-630,568,0,568],774:[742,-578,658,0,658],780:[740,-566,644,0,644],785:[756,-592,658,0,658],812:[-96,270,644,0,644],813:[-108,282,644,0,644],814:[-96,260,658,0,658],815:[-118,282,658,0,658],816:[-118,319,652,0,652],818:[-103,143,568,0,568],819:[-103,293,568,0,568],831:[820,-630,568,0,568],8214:[851,351,354,56,298],8260:[905,405,617,56,561],8400:[711,-601,667,56,611],8401:[711,-601,667,56,611],8406:[711,-521,659,56,603],8407:[711,-521,659,56,603],8417:[711,-521,715,56,659],8425:[730,-546,735,0,735],8428:[-171,281,667,56,611],8429:[-171,281,667,56,611],8430:[-91,281,659,56,603],8431:[-91,281,659,56,603],8592:[510,10,1463,56,1407],8593:[912,436,500,20,480],8594:[510,10,1463,56,1407],8595:[936,412,500,20,480],8596:[510,10,1442,56,1386],8597:[757,257,572,56,516],8598:[947,436,1495,56,1439],8599:[947,436,1495,56,1439],8600:[936,447,1495,56,1439],8601:[936,447,1495,56,1439],8602:[510,10,1463,56,1407],8603:[510,10,1463,56,1407],8606:[510,10,1463,56,1407],8607:[912,436,572,56,516],8608:[510,10,1463,56,1407],8609:[936,412,572,56,516],8610:[510,10,1658,56,1602],8611:[510,10,1658,56,1602],8612:[510,10,1443,56,1387],8613:[912,416,632,55,576],8614:[510,10,1443,56,1387],8615:[916,412,632,55,576],8617:[550,10,1463,56,1407],8618:[550,10,1463,56,1407],8619:[550,50,1463,56,1407],8620:[550,50,1463,56,1407],8621:[510,10,1442,56,1386],8622:[510,10,1442,56,1386],8624:[834,334,797,56,741],8625:[834,334,797,56,741],8626:[834,334,797,56,741],8627:[834,334,797,56,741],8630:[659,-229,1330,56,1274],8631:[659,-229,1330,56,1274],8636:[503,-230,1478,56,1422],8637:[270,3,1478,56,1422],8638:[930,436,441,112,385],8639:[930,436,441,56,329],8640:[503,-230,1478,56,1422],8641:[270,3,1478,56,1422],8642:[936,430,441,112,385],8643:[936,430,441,56,329],8644:[672,172,1484,56,1428],8645:[936,436,896,56,840],8646:[672,172,1484,56,1428],8647:[750,250,1463,56,1407],8648:[912,436,992,56,936],8649:[750,250,1463,56,1407],8650:[936,412,992,56,936],8651:[600,100,1484,56,1428],8652:[600,100,1484,56,1428],8653:[520,20,1457,56,1401],8654:[520,20,1534,56,1478],8655:[520,20,1457,56,1401],8656:[520,20,1457,56,1401],8657:[909,436,652,56,596],8658:[520,20,1457,56,1401],8659:[936,409,652,56,596],8660:[520,20,1534,56,1478],8661:[961,461,652,56,596],8662:[915,505,1532,56,1476],8663:[915,505,1532,56,1476],8664:[1005,415,1532,56,1476],8665:[1005,415,1532,56,1476],8666:[617,117,1461,56,1405],8667:[617,117,1461,56,1405],8668:[510,10,1463,56,1407],8669:[510,10,1463,56,1407],8678:[520,20,1496,56,1440],8679:[948,436,652,56,596],8680:[520,20,1496,56,1440],8681:[936,448,652,56,596],8691:[948,448,652,56,596],8693:[936,436,896,56,840],8694:[990,490,1463,56,1407],8719:[950,450,1278,56,1221],8720:[950,450,1278,56,1221],8721:[950,450,1444,56,1387],8730:[850,350,1000,110,1020],8739:[851,351,278,117,161],8741:[851,351,354,56,298],8747:[1361,861,999,56,943],8748:[1361,861,1419,56,1363],8749:[1361,861,1839,56,1783],8750:[1361,861,999,56,943],8751:[1361,861,1419,56,1363],8752:[1361,861,1839,56,1783],8753:[1361,861,999,56,943],8754:[1361,861,999,56,943],8755:[1361,861,999,56,943],8866:[684,184,946,56,890],8867:[684,184,946,56,890],8868:[684,184,986,56,930],8869:[684,184,946,56,890],8896:[957,435,1111,66,1043],8897:[935,457,1111,66,1043],8898:[939,417,1111,66,1046],8899:[917,439,1111,66,1046],8968:[800,300,458,178,429],8969:[800,300,458,29,280],8970:[800,300,458,178,429],8971:[800,300,458,29,280],9001:[800,300,428,124,372],9002:[800,300,428,56,304],9140:[730,-546,735,0,735],9141:[-116,300,735,0,735],9180:[745,-540,1006,0,1006],9181:[-110,315,1006,0,1006],9182:[815,-509,993,1,994],9183:[-79,385,993,0,993],9184:[853,-613,1048,0,1048],9185:[-183,423,1048,0,1048],10145:[468,-31,1423,56,1367],10214:[800,300,444,130,422],10215:[800,300,444,22,314],10216:[800,300,428,124,372],10217:[800,300,428,56,304],10218:[800,300,623,124,567],10219:[800,300,623,56,499],10222:[813,313,305,108,249],10223:[813,313,305,56,197],10502:[520,20,1437,56,1381],10503:[520,20,1437,56,1381],10752:[902,402,1511,104,1408],10753:[902,402,1511,104,1408],10754:[902,402,1511,104,1408],10755:[917,439,1111,66,1046],10756:[917,439,1111,66,1046],10757:[936,436,1111,66,1046],10758:[936,436,1111,66,1046],10761:[879,379,1371,56,1315],10764:[1361,861,2259,56,2203],10769:[1361,861,999,56,943],11012:[520,20,1508,56,1452],11013:[468,-31,1423,56,1367],11014:[895,416,612,87,524],11015:[916,395,612,87,524],11020:[468,-31,1468,89,1379],11021:[895,395,549,56,492],11057:[990,490,1463,56,1407]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"LatinModernMathJax_Size1"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Size1/Regular/Main.js"]);
