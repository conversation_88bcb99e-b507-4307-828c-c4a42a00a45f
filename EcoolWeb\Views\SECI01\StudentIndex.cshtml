﻿@model ECOOL_APP.com.ecool.Models.DTO.SECSharedSearchViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;


    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);


    if (Model.PRINT == "Y")
    {
        Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
    }
    else
    {
        if (AppMode)
        {
            Layout = "~/Views/Shared/_LayoutWebView.cshtml";
        }
        else
        {
            Layout = "~/Views/Shared/_LayoutSEO.cshtml";
        }
    }
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    string ErrMsg;
    short ThisMonthCash;
    string LimitShow = string.Empty;
    ECOOL_DEVEntities db = null;
    int CashLimit = 0;
    CashLimit = ECOOL_APP.UserProfile.
         GetCashLimit(user.SCHOOL_NO, user.USER_NO, user.USER_TYPE, ref db, out ThisMonthCash, out ErrMsg);
    if (CashLimit != short.MaxValue && CashLimit != 0)
    {
        LimitShow = "您本月有" + CashLimit + "點酷幣可以發給特殊表現的孩子，你已經發放了" + ThisMonthCash.ToString() + "點。";
    }
}


<script src="~/Scripts/Pring.js"></script>
<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")
<div class="form-group ">
    <label class="text-danger col-md-12">
        @LimitShow
    </label>
</div>
<div id='PrintDiv'>
    @Html.Action("_PersonalDiv", (string)ViewBag.BRE_NO, new { wSCHOOL_NO = Model.SCHOOL_NO, wUSER_NO = Model.USER_NO, wCLASS_NO = Model.CLASS_NO, wDATA_ANGLE_TYPE = Model.DATA_ANGLE_TYPE, wREF_BRE_NO = ViewBag.BRE_NO, wPRINT = Model.PRINT })
</div>
@Html.Hidden("PRINT", Model.PRINT)

<script language="javascript">

    $(document).ready(function () {
        $("#PieChartbtn").colorbox({ iframe: true, width: "80%", height: "80%", opacity: 0.82 });
    });
    $(document).ready(function () { $("#MyMOMO").colorbox({ iframe: true, width: "100%", height: "100%", opacity: 0.82 });});
    $(document).ready(function () {
        $("#Statisticalbtn").colorbox({ iframe: true, width: "80%", height: "80%", opacity: 0.82 });
    });

    $(document).ready(function () {
        $("#MyPHOTO").colorbox({ iframe: true, width: "80%", height: "80%", opacity: 0.82 });
    });

    function ExportResultWinOpen()
    {
        window.open('@Url.Action("ExportResult", (string)ViewBag.BRE_NO, new { wSCHOOL_NO = Model.SCHOOL_NO, wUSER_NO = Model.USER_NO, wCLASS_NO = Model.CLASS_NO, wDATA_ANGLE_TYPE = Model.DATA_ANGLE_TYPE })', '_blank')
    }

    if ($('#PRINT').val()=="Y") {
        window.onload=function()
        {
            window.print();
        }
    }








</script>