﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Reflection;
using System.Resources;
using System.Text;
using System.Threading.Tasks;
using System.Web.Mvc;

namespace ECOOL_APP.EF
{
    public static class EnumExtensions
    {
        public static string GetEnumDescription(this Enum value)
        {
            FieldInfo fi = value.GetType().GetField(value.ToString());

            DescriptionAttribute[] attributes = (DescriptionAttribute[])fi.GetCustomAttributes(typeof(DescriptionAttribute), false);
            //若取不到屬性，則取名稱
            if ((attributes != null) && (attributes.Length > 0))
                return attributes[0].Description;
            else
                return value.ToString();
        }

        public static string GetDisplayName(this Enum enumValue)
        {
            var enumMember = enumValue.GetType()
                            .GetMember(enumValue.ToString());

            DisplayAttribute displayAttrib = null;
            if (enumMember.Any())
            {
                displayAttrib = enumMember
                            .First()
                            .GetCustomAttribute<DisplayAttribute>();
            }

            string name = null;
            Type resource = null;

            if (displayAttrib != null)
            {
                name = displayAttrib.Name;
                resource = displayAttrib.ResourceType;
            }

            return String.IsNullOrEmpty(name) ? enumValue.ToString()
                : resource == null ? name
                : new ResourceManager(resource).GetString(name);
        }

        /// <summary>
        /// 列舉轉SelectList
        /// </summary>
        /// <typeparam name="T">型別</typeparam>
        /// <returns></returns>
        public static IEnumerable<SelectListItem> GetEnumSelectList<T>(string SelectedVal)
        {
            return Enum.GetValues(typeof(T)).Cast<T>().Select(
                enu => new SelectListItem() { Text = Enum.GetName(typeof(T), enu), Value = Convert.ToInt32(enu).ToString(), Selected = Convert.ToInt32(enu).ToString() == SelectedVal });
        }

        /// <summary>
        /// 列舉轉SelectList
        /// </summary>
        /// <typeparam name="T">型別</typeparam>
        /// <returns></returns>
        public static List<SelectListItem> GetEnumSelectListDisplayName<T>(string SelectedVal, bool IsAddEmpty = false)
        {
            List<SelectListItem> TempEnum = new List<SelectListItem>();

            if (IsAddEmpty)
            {
                TempEnum.Add(new SelectListItem() { Text = "請選擇", Value = "", Selected = string.IsNullOrWhiteSpace(SelectedVal) });
            }

            TempEnum.AddRange(Enum.GetValues(typeof(T)).Cast<T>().Select(
              enu => new SelectListItem()
              {
                  Text = enu.GetType()
                            .GetMember(enu.ToString())
                            .First()
                            .GetCustomAttribute<DisplayAttribute>()
                            .GetName(),
                  Value = Convert.ToInt32(enu).ToString(),
                  Selected = Convert.ToInt32(enu).ToString() == SelectedVal
              }).ToList());

            return TempEnum;
        }

        /// <summary>
        ///  String to  Enum
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="value"></param>
        /// <returns></returns>
        public static T ParseEnum<T>(string value)
        {
            return (T)Enum.Parse(typeof(T), value, true);
        }

        /// <summary>
        /// 取得列舉名稱
        /// </summary>
        /// <typeparam name="T">型別</typeparam>
        /// <param name="e"></param>
        /// <returns></returns>
        public static string GetEnumName<T>(object e)
        {
            if (!typeof(T).IsEnum) throw new TypeAccessException("不是Enum型別");

            if (e == null)
            {
                return string.Empty;
            }

            return Enum.GetName(typeof(T), e).ToString();
        }

        public static List<T> ToList<T>()
        {
            return Enum.GetValues(typeof(T)).Cast<T>().ToList<T>();
        }

        public static IEnumerable<T> ToEnumerable<T>()
        {
            return Enum.GetValues(typeof(T)).Cast<T>();
        }
    }
}