﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;

namespace ECOOL_APP.com.ecool.Models.entity
{
    public class uBET02
    {

        ///Summary
        ///
        ///Summary
        [Display(Name = "BET02DTO_BULLET_ID", ResourceType = typeof(PageResource))]
        public string BULLET_ID { get; set; }

        ///Summary
        ///
        ///Summary
        [Display(Name = "SCHOOL_NO", ResourceType = typeof(PageResource))]
        [StringLength(100)]
        public string SCHOOL_NO { get; set; }



        ///Summary
        ///
        ///Summary
        [Display(Name = "BET02DTO_CLASS_TYPE", ResourceType = typeof(PageResource))]
        [StringLength(20)]
        public string CLASS_TYPE { get; set; }

        ///Summary
        ///
        ///Summary
        [Display(Name = "BET02DTO_SUBJECT", ResourceType = typeof(PageResource))]
        [StringLength(400)]
        public string SUBJECT { get; set; }

        ///Summary
        ///
        ///Summary
        [Display(Name = "BET02DTO_CONTENT_TXT", ResourceType = typeof(PageResource))]
        public string CONTENT_TXT { get; set; }

        ///Summary
        ///
        ///Summary
        [Display(Name = "BET02DTO_ISPUBLISH", ResourceType = typeof(PageResource))]
        [StringLength(2)]
        public string ISPUBLISH { get; set; }

        ///Summary
        ///
        ///Summary
        [Display(Name = "BET02DTO_S_DATE", ResourceType = typeof(PageResource))]
        [DataType(DataType.Date), DisplayFormat(DataFormatString = "{0:yyyy/MM/dd}", ApplyFormatInEditMode = true)]
        public DateTime S_DATE { get; set; }

        ///Summary
        ///
        ///Summary
        [Display(Name = "BET02DTO_E_DATE", ResourceType = typeof(PageResource))]
        [DataType(DataType.Date), DisplayFormat(DataFormatString = "{0:yyyy/MM/dd}", ApplyFormatInEditMode = true)]
        public DateTime E_DATE { get; set; }

        ///Summary
        ///
        ///Summary
        [Display(Name = "BET02DTO_TOP_YN", ResourceType = typeof(PageResource))]
        [StringLength(20)]
        public string TOP_YN { get; set; }

        ///Summary
        ///
        ///Summary
        [Display(Name = "CRE_PERSON", ResourceType = typeof(PageResource))]
        [StringLength(100)]
        public string CRE_PERSON { get; set; }

        ///Summary
        ///
        ///Summary
        [Display(Name = "CRE_DATE", ResourceType = typeof(PageResource))]
        [DataType(DataType.Date), DisplayFormat(DataFormatString = "{0:yyyy/MM/dd}", ApplyFormatInEditMode = true)]
        public DateTime CRE_DATE { get; set; }

        ///Summary
        ///
        ///Summary
        [Display(Name = "CHG_PERSON", ResourceType = typeof(PageResource))]
        [StringLength(100)]
        public string CHG_PERSON { get; set; }

        ///Summary
        ///
        ///Summary
        [Display(Name = "CHG_DATE", ResourceType = typeof(PageResource))]
        [DataType(DataType.Date), DisplayFormat(DataFormatString = "{0:yyyy/MM/dd}", ApplyFormatInEditMode = true)]
        public DateTime CHG_DATE { get; set; }

        ///Summary
        ///
        ///Summary
        [Display(Name = "MEMO", ResourceType = typeof(PageResource))]
        [StringLength(100)]
        public string MEMO { get; set; }

    }
}
