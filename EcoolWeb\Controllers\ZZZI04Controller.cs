﻿using com.ecool.service;
using ECOOL_APP.com.ecool.Models.entity;
using EcoolWeb.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using ECOOL_APP;
using System.IO;
using MvcPaging;
using System.Text.RegularExpressions;
using System.Drawing;
using System.Drawing.Imaging;
using EcoolWeb.CustomAttribute;
using System.Web.Mvc.Html;
using Microsoft.Security.Application;
using ECOOL_APP.EF;
using ECOOL_APP.com.ecool.util;

namespace EcoolWeb.Controllers
{
    [SessionExpire]
    public class ZZZI04Controller : Controller
    {
        private static string Bre_NO = "ZZZI04";
        private static string Bre_Name = BreadcrumbService.GetBRE_NAME(Bre_NO);

        private string ErrorMsg;
        private BET02Service Db = new BET02Service();
        private ECOOL_DEVEntities Edb = new ECOOL_DEVEntities();
        private UserProfile user;

        /// <summary>
        /// 控制按鈕權限
        /// </summary>
        /// <param name="SType"></param>
        public void Shared()
        {
            user = UserProfileHelper.Get();
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Bre_Name = Bre_Name;
            ViewBag.Btn = HtmlHelperExtensions.PermissionCheck(Bre_NO, "Save");
        }

        // GET: BT02Amdin
        [CheckPermission(CheckBRE_NO = "ZZZI04", IsGetAllActionPermission = true)] //檢查權限

        #region 公佈欄後台查詢列表(管理者)

        public ActionResult List(FormCollection Form, int page = 1, int pageSize = 10)
        {
            this.Shared();
            ViewBag.Panel_Title = Bre_Name + "-清單列表";

            int count = int.MinValue;

            List<BT02AmdinViewModel> model = null;

            if (user != null)
            {
                model = BET02Service.USP_BET02DTO_QUERY(Form, user, page, pageSize, ref count);
            }

            //var result = model.OrderBy(d => d.S_DATE).ToPagedList(page, pageSize);
            ViewData.Model = model.ToPagedList(page - 1, pageSize, count);
            return View();
        }

        #endregion 公佈欄後台查詢列表(管理者)

        #region 公佈欄後台 新增、修改 畫面(管理者)

        [CheckPermission(CheckBRE_NO = "ZZZI04", CheckACTION_ID = "Save", IsGetAllActionPermission = true)] //檢查權限
        public ActionResult Create(string BULLET_ID)
        {
            this.Shared();
            ViewBag.Panel_Title = Bre_Name + "-編輯";

            BT02AmdinViewModel model = new BT02AmdinViewModel();

            if (string.IsNullOrWhiteSpace(BULLET_ID) == false)
            {

           
                model = Db.GetData(BULLET_ID, user);
                ViewBag.BULLET_ID = model.BULLET_ID;
                ViewBag.submit = Resources.Resource.MODIFY;
                model.REF_KEY = model.BULLET_ID;
            }
            else
            {
                ViewBag.submit = Resources.Resource.INSERT;
            }

            List<SelectListItem> SCHOOL_NO_ITEMS;
            List<SelectListItem> CLASS_TYPE_ITEMS;

            if (SharedGlobal.HomeIndex != "ChildMonthIndex")
            {
                if (user.ROLE_TYPE == 1 || user.ROLE_TYPE == 2)
                {
                    SCHOOL_NO_ITEMS = BDMT01Service.GetSCHOOL_NO("", "", user).Where(a => a.Value == "").ToList();
                    CLASS_TYPE_ITEMS = BET01Service.GetCLASS_TYPE(null).Where(a => a.Value == "1").ToList();
                }
                else
                {
                    SCHOOL_NO_ITEMS = BDMT01Service.GetSCHOOL_NO("", "", user);
                    CLASS_TYPE_ITEMS = BET01Service.GetCLASS_TYPE(null).Where(a => a.Value == "2").ToList();
                }
            }
            else
            {
                SCHOOL_NO_ITEMS = BDMT01Service.GetSCHOOL_NO("", "", user).Where(a => a.Value == "").ToList();
                CLASS_TYPE_ITEMS = BET01Service.GetCLASS_TYPE(null).Where(a => a.Value == "1").ToList();
            }

            ViewBag.SchoolNoItems = SCHOOL_NO_ITEMS;
            ViewBag.ClassTypeItems = CLASS_TYPE_ITEMS;

            List<uSYST02> LANG = SYST02Serivice.USP_SYST02_QUERY(user.SCHOOL_NO);
            ViewBag.LANG = LANG;

            ViewBag.ISPUBLISH = model.ISPUBLISH == null ? true : model.ISPUBLISH == "N" ? false : true;
            ViewBag.TOP_YN = model.TOP_YN == null ? false : model.TOP_YN == "N" ? false : true;

            ViewBag.PUSH_YN = model.PUSH_YN == null ? false : model.PUSH_YN == "N" ? false : true;

            return View(model);
        }

        #endregion 公佈欄後台 新增、修改 畫面(管理者)

        #region 公佈欄後台 資料處理(管理者)

        [CheckPermission(CheckBRE_NO = "ZZZI04", CheckACTION_ID = "Save", IsGetAllActionPermission = true)] //檢查權限
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Create(BT02AmdinViewModel Data, IEnumerable<HttpPostedFileBase> files)
        {
            this.Shared();
            ViewBag.Panel_Title = Bre_Name + "-編輯";

            if (string.IsNullOrWhiteSpace(Data.SCHOOL_NO) == false && Data.CLASS_TYPE == "1")
                ModelState.AddModelError("CLASS_TYPE", "系統公告需學校選擇「全部」");

            if (Data.S_DATE > Data.E_DATE)
                ModelState.AddModelError("S_DATE", "開始日期不能小於結束日期");

            if (Convert.ToBoolean(Data.PUSH_YN))
            {
                if (Edb.APPT03_Q.Where(a => a.REF_TABLE == "BET02" && a.REF_KEY == Data.REF_KEY).Any() == false)
                {
                    ModelState.AddModelError("PUSH_YN", "勾選了是否通知，但未選取通知人員");
                }
            }
            Data.LinkContext = Data.LinkContext;
            //過濾XSS
            Data.CONTENT_TXT = HtmlUtility.SanitizeHtml(Data.CONTENT_TXT);
            // Data.CONTENT_TXT = Sanitizer.GetSafeHtmlFragment(Data.CONTENT_TXT);

            if (string.IsNullOrEmpty(Data.BULLET_ID) == false)//非新增模式
            {
                //判斷Create USer 與Login USer是不同人
                if (user.USER_KEY != Data.CRE_PERSON)
                {
                    ECOOL_DEVEntities Edb = new ECOOL_DEVEntities();

                    HRMT01 H01 = Edb.HRMT01.Where(a => a.USER_KEY == Data.CRE_PERSON).FirstOrDefault();
                    if (H01 != null)
                    {
                        if (PermissionService.CompareROLE_LEVEL(user.ROLE_LEVEL, Data.CRE_PERSON) == false)
                        {
                            ModelState.AddModelError("ErrorMsg", "您無權限異動他人公告");
                        }
                    }
                    else
                    {
                        HRMT24 H24 = null;

                        if (Data.CLASS_TYPE == "1")
                        {
                            H24 = Edb.HRMT24.Where(a => a.ROLE_TYPE == 3).OrderByDescending(a => a.ROLE_LEVEL).FirstOrDefault();

                            if (H24 != null)
                            {
                                if (PermissionService.CompareROLE_LEVEL(user.ROLE_LEVEL, H24.ROLE_LEVEL) == false)
                                {
                                    ModelState.AddModelError("ErrorMsg", "此公告建人者已不存在，你的權限等級需大於「" + H24.ROLE_NAME + "」");
                                }
                            }
                            else
                            {
                                H24 = Edb.HRMT24.Where(a => a.ROLE_TYPE <= 2).OrderBy(a => a.ROLE_LEVEL).FirstOrDefault();
                                if (PermissionService.CompareROLE_LEVEL(user.ROLE_LEVEL, H24.ROLE_LEVEL) == false)
                                {
                                    ModelState.AddModelError("ErrorMsg", "此公告建人者已不存在，你的權限等級需大於「" + H24.ROLE_NAME + "」");
                                }
                            }
                        }
                        else
                        {
                            H24 = Edb.HRMT24.Where(a => a.ROLE_TYPE <= 2).OrderBy(a => a.ROLE_LEVEL).FirstOrDefault();

                            if (H24 != null)
                            {
                                if (PermissionService.CompareROLE_LEVEL(user.ROLE_LEVEL, H24.ROLE_LEVEL) == false)
                                {
                                    ModelState.AddModelError("ErrorMsg", "此公告建人者已不存在，你的權限等級需大於「" + H24.ROLE_NAME + "」");
                                }
                            }
                        }
                    }
                }
            }

            if (ModelState.IsValid == false) { TempData["StatusMessage"] = "錯誤，未輸入完整資訊"; }

            if (ModelState.IsValid)
            {
                Data.CHG_DATE = DateTime.Now;
                Data.CHG_PERSON = user.USER_KEY;

                if (Data.BULLET_ID == null || Data.BULLET_ID == "")
                {
                    Data.CRE_DATE = DateTime.Now;
                    Data.CRE_PERSON = user.USER_KEY;
                    Db.CreateAddDate(Data, user);
                    ErrorMsg = Db.ErrorMsg;

                    if (string.IsNullOrWhiteSpace(ErrorMsg))
                    {
                        UpLoadFile(Data, files);
                        Db.UpDate(Data, user); //資料處理
                    }
                }
                else
                {
                    UpLoadFile(Data, files);
                    Db.UpDate(Data, user); //資料處理
                    ErrorMsg = Db.ErrorMsg;
                }

                if (string.IsNullOrWhiteSpace(ErrorMsg))
                {
                    if (Convert.ToBoolean(Data.PUSH_YN) && Convert.ToBoolean((Data.ISPUBLISH)))
                    {
                        string StrUserType = UserType.Student + ',' + UserType.Parents + ',' + UserType.Teacher;
                        string BODY_TXT = "公告 主旨:" + Data.Details_LANG[0].SUBJECT + "，內容:" + Data.Details_LANG[0].CONTENT_TXT;

                        PushService.QAppt03ToAPPT03Push("BET02", Data.REF_KEY, Data.BULLET_ID, ref Edb, user, out ErrorMsg, BODY_TXT, Bre_NO, "Create");
                    }

                    ErrorMsg = "存檔成功";
                }

                TempData["StatusMessage"] = ErrorMsg;

                return RedirectToAction("Create", new { BULLET_ID = Data.BULLET_ID });
            }

            ViewBag.BULLET_ID = Data.BULLET_ID;

            List<SelectListItem> SCHOOL_NO_ITEMS;
            List<SelectListItem> CLASS_TYPE_ITEMS;

            if (SharedGlobal.HomeIndex != "ChildMonthIndex")
            {
                if (user.ROLE_TYPE == 1 || user.ROLE_TYPE == 2)
                {
                    SCHOOL_NO_ITEMS = BDMT01Service.GetSCHOOL_NO("", "", user).Where(a => a.Value == "").ToList();
                    CLASS_TYPE_ITEMS = BET01Service.GetCLASS_TYPE(null).Where(a => a.Value == "1").ToList();
                }
                else
                {
                    SCHOOL_NO_ITEMS = BDMT01Service.GetSCHOOL_NO("", "", user);
                    CLASS_TYPE_ITEMS = BET01Service.GetCLASS_TYPE(null).Where(a => a.Value == "2").ToList();
                }
            }
            else
            {
                SCHOOL_NO_ITEMS = BDMT01Service.GetSCHOOL_NO("", "", user).Where(a => a.Value == "").ToList();
                CLASS_TYPE_ITEMS = BET01Service.GetCLASS_TYPE(null).Where(a => a.Value == "1").ToList();
            }

            ViewBag.SchoolNoItems = SCHOOL_NO_ITEMS;
            ViewBag.ClassTypeItems = CLASS_TYPE_ITEMS;

            List<uSYST02> LANG = SYST02Serivice.USP_SYST02_QUERY(user.SCHOOL_NO);
            ViewBag.LANG = LANG;

            ViewBag.ISPUBLISH = Data.ISPUBLISH == null ? true : Data.ISPUBLISH == "N" ? false : true;
            ViewBag.TOP_YN = Data.TOP_YN == null ? false : Data.TOP_YN == "N" ? false : true;
            ViewBag.PUSH_YN = Data.PUSH_YN == null ? false : Data.PUSH_YN == "N" ? false : true;

            if (string.IsNullOrWhiteSpace(Data.BULLET_ID) == false)
            {
                ViewBag.submit = Resources.Resource.MODIFY;
            }
            else
            {
                ViewBag.submit = Resources.Resource.INSERT;
            }

            return View(Data);
        }

        #endregion 公佈欄後台 資料處理(管理者)

        #region 公佈欄下載(全部人)

        public ActionResult DownLoad(String BULLET_ID, String name)
        {
            string tempPath = System.Web.Configuration.WebConfigurationManager.AppSettings["FilePath"] + "\\BET02\\" + BULLET_ID;
            return File(tempPath + "\\" + name, "text/plain", Server.HtmlEncode(name));
        }

        #endregion 公佈欄下載(全部人)

        #region 附件刪除(管理者)

        [CheckPermission(CheckBRE_NO = "ZZZI04", CheckACTION_ID = "Save", IsGetAllActionPermission = true)] //檢查權限
        public ActionResult Delete(String BULLET_ID, String name, string ITEM_NO)
        {
            this.Shared();
            ViewBag.Panel_Title = Bre_Name + "-附件刪除";

            string tempPath = System.Web.Configuration.WebConfigurationManager.AppSettings["FilePath"] + "\\BET02\\" + BULLET_ID + "\\" + name;

            try
            {
                Db.Del_FILE(BULLET_ID, ITEM_NO);

                if (System.IO.File.Exists(tempPath))
                {
                    System.IO.File.Delete(tempPath);
                }
            }
            catch (Exception ex)
            {
                TempData["StatusMessage"] = "附件檔案刪除失敗" + ex.Message;
            }

            return RedirectToAction("Create", new { BULLET_ID = BULLET_ID });
        }

        #endregion 附件刪除(管理者)

        #region 公佈欄後台 刪除(管理者)

        [CheckPermission(CheckBRE_NO = "ZZZI04", CheckACTION_ID = "Save", IsGetAllActionPermission = true)] //檢查權限
        public ActionResult DeleteALL(string BULLET_ID)
        {
            this.Shared();
            ViewBag.Panel_Title = Bre_Name + "-刪除";

            try
            {
                Db.DelDate(BULLET_ID);
                string tempPath = System.Web.Configuration.WebConfigurationManager.AppSettings["FilePath"] + "\\BET02\\" + BULLET_ID;
                if (System.IO.File.Exists(tempPath))
                {
                    System.IO.Directory.Delete(tempPath, true);
                }

                TempData["StatusMessage"] = "刪除成功";
            }
            catch (Exception ex)
            {
                TempData["StatusMessage"] = "刪除失敗" + ex.Message;
            }

            return RedirectToAction("List");
        }

        #endregion 公佈欄後台 刪除(管理者)

        #region 公佈欄清單畫面(前端平台)
        [HttpPost]
        [AllowAnonymous]
        public ActionResult Index(string SearchContents, string SCHOOL_NO, string CLASS_TYPE, int page = 1, int pageSize = 15, string PrevAction = "Index", string Layout = "")
        {
            user = UserProfileHelper.Get();
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = Bre_Name + "-一覽表";
            ViewBag.PrevAction = PrevAction;
            ViewBag.Layout = Layout;

            string BULLET_ID = string.Empty;
            int count = int.MinValue;

            if (string.IsNullOrWhiteSpace(SCHOOL_NO) == true) SCHOOL_NO = EcoolWeb.Models.UserProfileHelper.GetSchoolNo();
            //if (string.IsNullOrWhiteSpace(CLASS_TYPE) == true) CLASS_TYPE = "2";

            FormCollection Form = new FormCollection();

            Form["SCHOOL_NO"] = SCHOOL_NO;
            //Form["CLASS_TYPE"] = CLASS_TYPE;
            Form["ISPUBLISH"] = "Y";
            Form["SUBJECT"] = SearchContents;

            TempData["SearchContents"] = SearchContents;

            List<BT02AmdinViewModel> model = BET02Service.USP_BET02DTO_QUERY4Users(Form, user, page, pageSize, ref count);
            model.OrderByDescending(e => e.TOP_YN).OrderByDescending(e => e.S_DATE);

            ViewData.Model = model.ToPagedList(page - 1, pageSize, count);
            return View();
        }

        #endregion 公佈欄清單畫面(前端平台)

        // GET: Details

        #region 公佈欄詳細畫面(前端平台)

       
        [HttpPost]
        [AllowAnonymous]
        public ActionResult Details(string BULLET_ID, string SearchContents, int page = 1, string PrevAction = "Index", string Layout = "")
        {
            user = UserProfileHelper.Get();
            ViewBag.BRE_NO = Bre_NO;
            ViewBag.Panel_Title = Bre_Name + "-明細";
            ViewBag.PrevAction = PrevAction;
            ViewBag.page = page;
            ViewBag.SearchContents = SearchContents;
            ViewBag.Layout = Layout;

            BT02AmdinViewModel model = new BT02AmdinViewModel();
            model = Db.GetData(BULLET_ID, user);
            return View(model);
        }

        #endregion 公佈欄詳細畫面(前端平台)

        private void UpLoadFile(BT02AmdinViewModel Data, IEnumerable<HttpPostedFileBase> files)
        {
            if (ErrorMsg == null)
            {
                List<uBET02_FILE> ListDa = new List<uBET02_FILE>();

                int Item = 0;

                foreach (var file in files)
                {
                    if (file != null && file.ContentLength > 0)
                    {
                        Item = Item + 1;
                        string fileName = Path.GetFileName(file.FileName);
                        string tempPath = System.Web.Configuration.WebConfigurationManager.AppSettings["FilePath"] + "\\BET02\\" + Data.BULLET_ID;
                        string UpLoadFile = tempPath + "\\" + fileName;

                        if (Directory.Exists(tempPath) == false)
                        {
                            Directory.CreateDirectory(tempPath);
                        }

                        file.SaveAs(UpLoadFile);

                        try
                        {
                            Regex regexCode = new Regex(@".*\.(jpg|jpeg|png|gif|bmp)");
                            if (regexCode.IsMatch(fileName.ToLower())) //判斷是否圖片
                            {
                                System.Drawing.Image image = System.Drawing.Image.FromFile(UpLoadFile);
                                //必須使用絕對路徑

                                ImageFormat thisFormat = image.RawFormat;
                                //取得影像的格式

                                int fixWidth = 0;
                                int fixHeight = 0;

                                //第一種縮圖用
                                int maxPx = Convert.ToInt16(800);    //宣告一個最大值，demo是把該值寫到web.config裡

                                if (image.Width > maxPx || image.Height > maxPx)  //如果圖片的寬大於最大值或高大於最大值就往下執行
                                {
                                    if (image.Width >= image.Height)  //圖片的寬大於圖片的高

                                    {
                                        fixWidth = maxPx;
                                        //設定修改後的圖寬
                                        fixHeight = Convert.ToInt32((Convert.ToDouble(fixWidth) / Convert.ToDouble(image.Width)) * Convert.ToDouble(image.Height));
                                        //設定修改後的圖高
                                    }
                                    else
                                    {
                                        fixHeight = maxPx;
                                        //設定修改後的圖高
                                        fixWidth = Convert.ToInt32((Convert.ToDouble(fixHeight) / Convert.ToDouble(image.Height)) * Convert.ToDouble(image.Width));
                                        //設定修改後的圖寬
                                    }
                                }
                                else
                                //圖片沒有超過設定值，不執行縮圖
                                {
                                    fixHeight = image.Height;
                                    fixWidth = image.Width;
                                }

                                Bitmap imageOutput = new Bitmap(image, fixWidth, fixHeight);

                                image.Dispose();
                                //釋放掉圖檔

                                //砍掉原圖檔
                                System.IO.File.Delete(UpLoadFile);

                                //輸出一個新圖(就是修改過的圖)
                                //string fixSaveName = string.Concat(fileName+"_sys",".jpg");
                                //副檔名不應該這樣給，但因為此範例沒有讀取檔案的部份所以demo就直接給啦

                                imageOutput.Save(tempPath + "\\" + fileName, thisFormat);
                                //將修改過的圖存於設定的位子

                                imageOutput.Dispose();
                                //釋放記憶體
                            }

                            ListDa.Add(new uBET02_FILE()
                            {
                                BULLET_ID = Data.BULLET_ID,
                                ITEM_NO = Item.ToString(),
                                FILE_NAME = fileName
                            });
                        }
                        catch (Exception ex)
                        {
                            ErrorMsg += fileName + "上傳失敗" + ex + "r\n";
                        }
                    }
                }

                Data.Details_FILE = ListDa;
            }
        }

        /// <summary>
        /// ckeditor上傳圖片
        /// </summary>
        /// <param name="upload">預設參數叫upload</param>
        /// <param name="CKEditorFuncNum"></param>
        /// <param name="CKEditor"></param>
        /// <param name="langCode"></param>
        /// <returns></returns>
        [HttpPost]
        public ActionResult UploadPicture(HttpPostedFileBase upload, string CKEditorFuncNum, string CKEditor, string langCode)
        {
            string result = "";
            if (upload != null && upload.ContentLength > 0)
            {
                DateTime dt = DateTime.Now;
                string fileName = dt.ToString("yyyy_MM_dd_HH_mm_ss") + "_" + Path.GetFileName(upload.FileName);
                string SCHOOL_NO = EcoolWeb.Models.UserProfileHelper.GetSchoolNo();
                string UploadImageRoot = System.Web.Configuration.WebConfigurationManager.AppSettings["UploadImageRoot"];
                if (UploadImageRoot == null) UploadImageRoot = @"~\Content\";
                if (UploadImageRoot.EndsWith(@"\") == false) UploadImageRoot += @"\";
                string imgPath = string.Format(@"{0}ZZZI04IMG\{1}\", Request.MapPath(UploadImageRoot), SCHOOL_NO);
                if (Directory.Exists(imgPath) == false) Directory.CreateDirectory(imgPath);

                string UpLoadFile = imgPath + @"\" + fileName;

                System.Drawing.Image image = System.Drawing.Image.FromStream(upload.InputStream);
                double FixWidth = 500;
                double FixHeight = 1000;
                double rate = 1;
                if (image.Width > FixWidth || image.Height > FixHeight)
                {
                    if (image.Width > FixWidth) rate = FixWidth / image.Width;
                    else if (image.Height * rate > FixHeight) rate = FixHeight / image.Height;

                    int w = Convert.ToInt32(image.Width * rate);
                    int h = Convert.ToInt32(image.Height * rate);
                    Bitmap imageOutput = new Bitmap(image, w, h);
                    imageOutput.Save(Path.Combine(imgPath, fileName), image.RawFormat);
                    imageOutput.Dispose();
                }
                else
                {
                    //直接儲存
                    upload.SaveAs(Path.Combine(imgPath, fileName));
                }
                image.Dispose();

                string tempImageUrl = @"~/Content/ZZZI04IMG/" + SCHOOL_NO + @"/" + fileName;
                var imageUrl = Url.Content(tempImageUrl);

                var vMessage = string.Empty;

                result = @"<html><body><script>window.parent.CKEDITOR.tools.callFunction(" + CKEditorFuncNum + ", \"" + imageUrl + "\", \"" + vMessage + "\");</script></body></html>";
            }

            return Content(result);
        }
    }
}