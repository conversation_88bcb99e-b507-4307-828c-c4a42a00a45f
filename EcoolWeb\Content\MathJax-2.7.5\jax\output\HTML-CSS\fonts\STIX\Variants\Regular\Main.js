/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/Variants/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.STIXVariants={directory:"Variants/Regular",family:"STIXVariants",Ranges:[[32,32,"All"],[119,124,"All"],[160,160,"All"],[411,411,"All"],[612,612,"All"],[8242,8279,"All"],[8512,8512,"All"],[8592,8595,"All"],[8657,8674,"All"],[8709,8941,"All"],[8994,8995,"All"],[9251,9251,"All"],[9641,9641,"All"],[10812,10990,"All"]],8242:[565,-28,340,44,295],8463:[683,10,579,47,547],8709:[729,74,523,28,502],8726:[710,222,523,46,478],8730:[943,11,737,67,767]};MathJax.OutputJax["HTML-CSS"].initFont("STIXVariants");MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/Variants/Regular/Main.js");
