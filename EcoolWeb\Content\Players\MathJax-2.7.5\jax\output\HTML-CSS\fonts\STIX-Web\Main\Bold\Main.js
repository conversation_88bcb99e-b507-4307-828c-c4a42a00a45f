/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX-Web/Main/Bold/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXMathJax_Main-bold"]={directory:"Main/Bold",family:"STIXMathJax_Main",weight:"bold",testString:"\u00A0\u00A3\u00A5\u00A7\u00A8\u00AC\u00AE\u00AF\u00B0\u00B1\u00B4\u00B5\u00B7\u00D7\u00F0",32:[0,0,250,0,0],33:[691,13,333,81,251],34:[691,-404,555,83,472],35:[700,0,500,5,495],36:[750,99,500,29,472],37:[706,29,749,61,688],38:[691,16,833,62,789],39:[691,-404,278,75,204],40:[694,168,333,46,306],41:[694,168,333,27,287],42:[691,-255,500,56,448],43:[563,57,750,65,685],44:[155,180,250,39,223],45:[287,-171,333,44,287],46:[156,13,250,41,210],47:[691,19,278,-24,302],48:[688,13,500,24,476],49:[688,0,500,65,441],50:[688,0,500,17,478],51:[688,14,500,16,468],52:[688,0,500,19,476],53:[676,8,500,22,470],54:[688,13,500,28,475],55:[676,0,500,17,477],56:[688,13,500,28,472],57:[688,13,500,26,473],58:[472,13,333,82,251],59:[472,180,333,82,266],60:[534,24,750,80,670],61:[399,-107,750,68,682],62:[534,24,750,80,670],63:[689,13,500,57,445],64:[691,19,930,108,822],65:[690,0,722,9,689],66:[676,0,667,16,619],67:[691,19,722,49,687],68:[676,0,722,14,690],69:[676,0,667,16,641],70:[676,0,611,16,583],71:[691,19,778,37,755],72:[676,0,778,21,759],73:[676,0,389,20,370],74:[676,96,500,3,478],75:[676,0,778,30,769],76:[677,0,667,19,638],77:[676,0,944,14,921],78:[676,18,722,16,701],79:[691,19,778,35,743],80:[676,0,611,16,600],81:[691,176,778,35,743],82:[676,0,722,26,716],83:[692,19,556,35,513],84:[676,0,667,31,636],85:[676,19,722,16,701],86:[676,18,722,16,701],87:[676,15,1000,19,981],88:[676,0,722,16,699],89:[676,0,722,15,699],90:[676,0,667,28,634],91:[678,149,333,67,301],92:[691,19,278,-25,303],93:[678,149,333,32,266],94:[676,-311,581,73,509],95:[-75,125,500,0,500],96:[713,-528,333,8,246],97:[473,14,500,25,488],98:[676,14,556,17,521],99:[473,14,444,25,430],100:[676,14,556,25,534],101:[473,14,444,25,427],102:[691,0,333,14,389],103:[473,206,500,28,483],104:[676,0,556,15,534],105:[691,0,278,15,256],106:[691,203,333,-57,263],107:[676,0,556,22,543],108:[676,0,278,15,256],109:[473,0,833,15,814],110:[473,0,556,21,539],111:[473,14,500,25,476],112:[473,205,556,19,524],113:[473,205,556,34,536],114:[473,0,444,28,434],115:[473,14,389,25,361],116:[630,12,333,19,332],117:[461,14,556,16,538],118:[461,14,500,21,485],119:[461,14,722,23,707],120:[461,0,500,12,484],121:[461,205,500,16,482],122:[461,0,444,21,420],123:[698,175,394,22,340],124:[691,19,220,66,154],125:[698,175,394,54,372],126:[333,-173,520,29,491],160:[0,0,250,0,0],163:[684,16,500,21,477],165:[676,0,500,-64,547],167:[691,132,500,57,443],168:[666,-537,333,-2,337],172:[399,-108,750,65,685],174:[691,19,747,26,721],175:[637,-565,333,1,331],176:[688,-402,400,57,343],177:[518,151,770,65,685],180:[713,-528,333,86,324],181:[461,206,556,33,536],183:[417,-248,250,41,210],215:[538,33,702,66,636],240:[691,14,500,25,476],247:[537,31,570,33,537],295:[676,0,556,15,534],305:[461,0,278,15,256],567:[461,203,333,-57,260],710:[704,-528,333,-2,335],711:[704,-528,333,-2,335],713:[637,-565,370,20,350],714:[713,-528,266,20,258],715:[713,-528,266,20,258],728:[691,-528,333,15,318],729:[666,-537,333,102,231],730:[750,-537,333,60,273],732:[674,-547,333,-16,349],768:[713,-528,0,-369,-131],769:[713,-528,0,-369,-131],770:[704,-528,0,-418,-81],771:[674,-547,0,-432,-67],772:[637,-565,0,-415,-85],774:[691,-528,0,-401,-98],775:[666,-537,0,-314,-185],776:[666,-537,0,-419,-80],778:[750,-537,0,-356,-143],779:[713,-528,0,-469,-31],780:[704,-528,0,-418,-81],824:[662,156,0,-410,31],913:[690,0,722,9,689],914:[676,0,667,16,619],915:[676,0,620,16,593],916:[690,0,722,33,673],917:[676,0,667,16,641],918:[676,0,667,28,634],919:[676,0,778,21,759],920:[692,18,778,35,743],921:[676,0,389,20,370],922:[676,0,778,30,769],923:[690,0,707,9,674],924:[676,0,944,14,921],925:[676,18,722,16,701],926:[676,0,647,40,607],927:[691,19,778,35,743],928:[676,0,778,21,759],929:[676,0,611,16,600],931:[676,0,671,28,641],932:[676,0,667,31,636],933:[692,0,703,7,693],934:[676,0,836,18,818],935:[676,0,722,16,699],936:[692,0,808,15,797],937:[692,0,768,28,740],945:[473,14,644,25,618],946:[692,205,556,45,524],947:[473,205,518,12,501],948:[692,14,502,26,477],949:[473,14,444,28,429],950:[692,205,459,23,437],951:[473,205,585,12,545],952:[692,14,501,25,476],953:[461,14,326,15,304],954:[473,0,581,21,559],955:[692,18,547,19,527],956:[461,205,610,45,588],957:[473,14,518,15,495],958:[692,205,468,23,439],959:[473,14,500,25,476],960:[461,18,631,20,609],961:[473,205,547,45,515],962:[473,203,464,23,444],963:[461,14,568,25,529],964:[461,14,492,18,457],965:[473,14,576,12,551],966:[473,205,653,24,629],967:[473,205,612,21,586],968:[473,205,763,12,751],969:[473,14,733,26,708],976:[697,10,500,54,462],977:[692,14,647,12,620],978:[692,0,743,7,733],981:[676,205,653,24,629],982:[461,14,864,9,851],984:[691,205,778,35,743],985:[473,205,500,25,476],986:[691,211,680,45,645],987:[503,203,504,23,483],988:[676,0,620,16,593],989:[461,205,491,45,458],990:[797,14,757,35,715],991:[692,0,485,29,453],992:[692,205,839,33,801],993:[639,205,611,29,583],1008:[473,19,563,12,546],1009:[473,205,511,25,486],1012:[691,19,778,35,743],1013:[473,14,444,25,430],1014:[473,14,444,14,419],8211:[271,-181,500,0,500],8212:[271,-181,1000,0,1000],8216:[691,-356,333,70,254],8217:[691,-356,333,79,263],8220:[691,-356,500,32,486],8221:[691,-356,500,14,468],8224:[691,134,500,47,453],8225:[691,132,500,45,456],8230:[156,13,1000,82,917],8242:[713,-438,310,75,235],8243:[713,-438,467,75,392],8244:[713,-438,625,75,550],8245:[713,-438,310,75,235],8254:[838,-766,500,0,500],8260:[688,12,183,-168,345],8279:[713,-438,783,75,708],8407:[846,-508,0,-470,14],8463:[685,10,576,50,543],8465:[701,25,790,54,735],8467:[699,14,500,43,632],8472:[541,219,850,55,822],8476:[701,25,884,50,841],8487:[674,18,758,35,723],8498:[676,0,616,48,546],8501:[694,34,766,76,690],8502:[694,34,703,60,659],8503:[694,34,562,71,493],8504:[694,34,599,40,559],8592:[451,-55,977,68,909],8593:[676,170,584,94,490],8594:[451,-55,977,68,909],8595:[676,170,584,94,490],8596:[451,-55,977,30,948],8597:[736,230,584,94,490],8598:[676,170,977,68,911],8599:[676,170,977,68,911],8600:[676,170,977,68,911],8601:[676,170,977,68,911],8602:[451,-55,977,68,909],8603:[451,-55,977,68,909],8606:[451,-55,977,68,909],8608:[451,-55,977,68,909],8610:[451,-55,977,68,909],8611:[451,-55,977,68,909],8614:[451,-55,977,68,909],8617:[539,-55,966,66,900],8618:[539,-55,966,66,900],8619:[540,6,966,66,900],8620:[540,6,966,66,900],8621:[451,-55,1297,55,1242],8622:[451,-55,977,30,948],8624:[686,170,584,45,503],8625:[686,170,584,81,539],8630:[524,0,971,66,905],8631:[524,0,971,66,905],8634:[693,127,974,105,869],8635:[693,127,974,105,869],8636:[501,-209,977,66,910],8637:[297,-5,977,65,909],8638:[694,162,552,239,481],8639:[694,162,352,71,313],8640:[501,-209,977,66,910],8641:[297,-5,977,66,910],8642:[694,162,552,239,481],8643:[694,162,552,71,313],8644:[618,114,977,68,909],8646:[618,114,977,68,909],8647:[618,114,977,68,909],8648:[676,165,864,66,798],8649:[618,114,977,68,909],8650:[676,165,864,66,798],8651:[571,21,977,66,910],8652:[571,21,977,66,910],8653:[570,64,977,68,909],8654:[570,64,1240,50,1190],8655:[570,64,977,68,909],8656:[570,64,977,68,909],8657:[676,170,714,40,674],8658:[570,64,977,68,909],8659:[676,170,714,40,674],8660:[570,64,1240,50,1190],8661:[736,230,714,40,674],8669:[451,-55,977,62,914],8704:[676,0,599,5,594],8705:[785,29,539,63,476],8706:[686,10,559,44,559],8707:[676,0,599,76,523],8708:[803,127,599,76,523],8709:[594,90,787,50,737],8711:[676,0,681,23,658],8712:[547,13,750,82,668],8713:[680,146,750,82,668],8715:[547,13,750,82,668],8717:[499,-35,500,60,440],8722:[297,-209,750,66,685],8723:[657,12,770,65,685],8724:[793,57,750,65,685],8725:[732,193,584,78,506],8726:[411,-93,452,25,427],8727:[502,-34,585,82,503],8728:[409,-95,394,40,354],8729:[414,-91,493,85,408],8730:[946,259,965,130,1016],8733:[450,0,772,80,692],8734:[450,0,964,80,884],8736:[569,0,792,50,708],8737:[569,74,792,50,708],8738:[534,26,695,27,667],8739:[690,189,288,100,188],8740:[690,189,411,23,388],8741:[690,189,487,100,387],8742:[690,189,617,23,594],8743:[536,28,640,52,588],8744:[536,28,640,52,588],8745:[541,33,650,66,584],8746:[541,33,650,66,584],8747:[824,320,553,32,733],8756:[575,41,750,66,685],8757:[575,41,750,66,685],8764:[374,-132,750,67,682],8765:[374,-132,750,67,682],8768:[575,40,348,53,295],8769:[444,-62,750,67,682],8770:[463,-45,750,68,683],8771:[463,-45,750,68,683],8773:[568,60,750,68,683],8774:[568,150,750,68,683],8776:[508,-26,750,68,683],8778:[568,75,750,68,683],8781:[518,13,750,68,683],8782:[484,-22,750,68,683],8783:[484,-107,750,68,683],8784:[667,-107,750,68,682],8785:[667,161,750,68,682],8786:[667,161,750,68,682],8787:[667,161,750,68,682],8790:[471,-63,750,68,682],8791:[809,-107,750,68,682],8796:[844,-107,750,68,682],8800:[662,156,750,68,682],8801:[507,-27,750,68,682],8804:[627,121,750,80,670],8805:[627,120,750,80,670],8806:[729,222,750,80,670],8807:[729,222,750,80,670],8808:[729,294,750,80,670],8809:[729,294,750,80,670],8810:[534,24,1000,38,961],8811:[534,24,1000,38,961],8812:[732,193,417,46,371],8814:[625,115,750,80,670],8815:[625,115,750,80,670],8816:[717,235,750,80,670],8817:[717,235,750,80,670],8818:[690,182,750,67,682],8819:[690,182,750,67,682],8822:[734,226,750,80,670],8823:[734,226,750,80,670],8826:[531,23,750,80,670],8827:[531,23,750,80,670],8828:[645,138,750,80,670],8829:[645,138,750,80,670],8830:[676,169,750,67,682],8831:[676,169,750,67,682],8832:[625,115,750,80,670],8833:[625,115,750,80,670],8834:[547,13,750,82,668],8835:[547,13,750,82,668],8838:[647,101,750,82,668],8839:[647,101,750,82,668],8840:[747,201,750,82,668],8841:[747,201,750,82,668],8842:[734,200,750,82,668],8843:[734,200,750,82,668],8846:[541,33,650,66,584],8847:[532,27,750,87,663],8848:[532,27,750,87,663],8849:[644,93,750,87,663],8850:[644,93,750,87,663],8851:[541,33,650,66,584],8852:[541,33,650,66,584],8853:[634,130,864,50,814],8854:[634,130,864,50,814],8855:[634,130,864,50,814],8856:[634,130,864,50,814],8857:[594,90,784,50,734],8858:[634,130,842,39,803],8859:[634,130,864,50,814],8861:[634,130,864,50,814],8862:[661,158,910,45,865],8863:[661,158,910,45,865],8864:[661,158,910,45,865],8865:[661,158,910,45,865],8866:[676,0,750,91,659],8867:[676,0,750,91,659],8868:[676,0,750,91,659],8869:[676,0,750,91,659],8872:[676,0,750,91,659],8873:[676,0,972,91,882],8874:[676,0,944,91,856],8876:[676,0,913,21,822],8877:[676,0,912,21,822],8878:[676,0,1096,21,1024],8879:[676,0,1104,21,1016],8882:[534,24,750,81,669],8883:[534,24,750,81,669],8884:[621,113,750,81,669],8885:[621,113,750,81,669],8888:[436,-96,884,50,834],8890:[461,216,498,74,424],8891:[536,189,640,52,588],8892:[697,28,640,52,588],8900:[515,-17,584,43,541],8904:[604,72,870,67,803],8905:[604,72,870,57,817],8906:[604,72,870,53,813],8907:[604,72,870,97,773],8908:[604,72,870,97,773],8909:[463,-45,750,68,683],8910:[536,28,640,41,599],8911:[536,28,640,41,599],8912:[600,67,750,63,687],8913:[600,67,750,63,687],8914:[541,33,750,65,685],8915:[541,33,750,65,685],8916:[643,33,650,66,584],8918:[534,24,750,80,670],8919:[534,24,750,80,670],8920:[534,24,1336,40,1296],8921:[534,24,1336,40,1296],8922:[916,408,750,80,670],8923:[916,408,750,80,670],8926:[645,138,750,80,670],8927:[645,138,750,80,670],8928:[735,199,750,80,670],8929:[735,199,750,80,670],8934:[690,200,750,67,682],8935:[690,200,750,67,682],8936:[676,187,750,67,682],8937:[676,187,750,67,682],8938:[625,115,750,81,669],8939:[625,115,750,81,669],8940:[711,228,750,81,669],8941:[711,228,750,81,669],8942:[678,174,584,205,375],8943:[351,-181,977,62,914],8945:[579,75,977,162,815],8968:[731,193,469,164,459],8969:[731,193,469,10,305],8970:[732,193,469,164,459],8971:[732,193,469,10,305],8994:[378,-129,1026,37,990],8995:[378,-129,1026,37,990],9416:[690,19,695,0,695],9651:[811,127,1145,35,1110],9661:[811,127,1145,35,1110],9674:[795,289,790,45,745],9837:[740,5,437,86,389],9838:[818,210,490,97,393],9839:[818,210,490,52,438],10216:[732,193,445,69,399],10217:[732,193,445,46,376],10815:[676,0,734,27,707],10846:[887,28,640,52,588],10877:[648,140,750,80,670],10878:[648,140,750,80,670],10887:[646,213,750,80,670],10888:[646,213,750,80,670],10889:[792,305,750,67,682],10890:[792,305,750,67,682],10901:[648,140,750,80,670],10902:[648,140,750,80,670],10927:[619,111,750,80,670],10928:[619,111,750,80,670],10949:[730,222,750,80,670],10950:[730,222,750,80,670]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"STIXMathJax_Main-bold"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Main/Bold/Main.js"]);
