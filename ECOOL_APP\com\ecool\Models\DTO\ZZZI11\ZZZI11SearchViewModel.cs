﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.Mvc;

namespace ECOOL_APP.com.ecool.Models.DTO
{
    public class ZZZI11SearchViewModel
    {
        /// <summary>
        /// 排序欄位
        /// </summary>
        public string OrderByName { get; set; }

        /// <summary>
        /// Desc or ASC
        /// </summary>
        public string SyntaxName { get; set; }

        /// <summary>
        /// 頁次
        /// </summary>
        public int Page { get; set; }

        /// <summary>
        /// 內頁頁次
        /// </summary>
        public int DetailsPage { get; set; }


        [DisplayName("搜尋條件")]
        public string SearchContents { get; set; }


        public string Q_QUESTIONS_ID { get; set; }

        [DisplayName("狀態")]
        public string STATUS { get; set; }

        public ZZZI11SearchViewModel()
        {
            Page = 1;
            DetailsPage = 1;
            OrderByName = "CRE_DATE";
            SyntaxName = "Desc";
        }



    }
}
