﻿@model EcoolWeb.ViewModels.ADDTBatchDeleteViewViewModel
@{
    ViewBag.Title = "作廢失敗記錄一覽表";
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@{
    Html.RenderAction("_PageMenu", new { NowAction = "BATCH_DEL" });
}

@using (Html.BeginForm("DEL_LOG_LIST", "ADDT", FormMethod.Post, new { id = "form1", name = "form1" }))
{
    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })
    @Html.HiddenFor(m => m.StringDelApply)
    <h3 style="color:red">以下只會呈現未作廢的資料</h3>
    <img src="~/Content/img/web-Bar-09.png" class="img-responsive App_hide" alt="Responsive image" />
    <div class="table-responsive">
        <div class="text-center">
            <table class="table-ecool table-92Per table-hover table-ecool-reader">
                <thead>
                    <tr>
                        <th style="text-align: center;">
                            申請日期
                        </th>
                        <th style="text-align: center;">
                            班級
                        </th>
                        <th style="text-align: center;">
                            座號
                        </th>
                        <th style="text-align: center;">
                            姓名
                        </th>
                        <th style="text-align: center;">
                            書名
                        </th>
                        <th style="text-align: center;">
                            目前狀態
                        </th>
                        <th style="text-align: center;">
                            失敗原因
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var item in Model.BatchDeleteList.Where(x => x.OK == false))
                    {
                        <tr>
                            <td style="text-align:left;white-space:nowrap;">
                                @Html.DisplayFor(modelItem => item.aDDT06.CRE_DATE, "ShortDateTime")
                            </td>
                            <td>
                                @Html.DisplayFor(modelItem => item.aDDT06.CLASS_NO)
                            </td>
                            <td>
                                @Html.DisplayFor(modelItem => item.aDDT06.SEAT_NO)
                            </td>
                            <td style="text-align:center;">
                                @Html.DisplayFor(modelItem => item.aDDT06.NAME)
                            </td>
                            <td align="left">
                                @Html.DisplayFor(modelItem => item.aDDT06.BOOK_NAME)

                                @if (item.aDDT06.SHARE_YN == "Y" || item.aDDT06.SHARE_YN == "y")
                                {
                                    <img src="~/Content/img/icons-like-05.png" />
                                }
                            </td>
                            <td style="text-align:center;">
                                @ECOOL_APP.EF.ADDStatus.GetADDT06StatusString(item.aDDT06.APPLY_STATUS)
                            </td>
                            <td>
                                <span style="color:red">@item.Error</span>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>
}