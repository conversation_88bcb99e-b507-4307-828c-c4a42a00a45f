﻿@model EcoolWeb.Models.ADDI01IndexViewModel
<script src=@Url.Content("~/Scripts/clipboard.min.js") nonce="cmlvaw"></script>


<style type="text/css">
    .hideContent {
        overflow: hidden;
        height: 37px;
        line-height: 37px;
    }

    .showContent {
        overflow: hidden;
    }

    .show-more {
        padding: 10px 0;
        text-align: right;
    }
</style>

@{
    if ((Model?.WhereIsColorboxForUser ?? false))
    {
        ViewBag.Title = "我的訂閱文章";
    }
    else
    {
        ViewBag.Title = "線上投稿-線上投稿一覽表";
    }

    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode || (Model?.WhereIsColorboxForUser ?? false))
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }

}

@helper  buttonFun(ECOOL_APP.UserProfile user, bool AppMode)
{
    string ActiveShareYN = Model.whereShareYN ? "active" : "";
    string ActiveComment = Model.whereComment ? "active" : "";
    string ActiveCommentCash = Model.whereCommentCash ? "active" : "";

    string btnActiveAll = AppMode ? "全部" : "全部(不含作廢)";

    <br />
    if (user != null)
    {
        <div id="btnDiv" class="showContent">

            <div class="form-inline" style="text-align:left">
                @{
                    string ActiveAll = (Model.whereWritingStatus == string.Empty || Model.whereWritingStatus == null) ? "active" : "";
                    string ActiveUN = (Model.whereWritingStatus == Convert.ToSByte(ADDStatus.eADDT01Status.Draft).ToString() + "," + Convert.ToSByte(ADDStatus.eADDT01Status.TurnVerify).ToString()) ? "active" : "";
                    string ActiveIN = (Model.whereWritingStatus == Convert.ToSByte(ADDStatus.eADDT01Status.Verified).ToString()) ? "active" : "";
                    string ActiveDel = (Model.whereWritingStatus == Convert.ToSByte(ADDStatus.eADDT01Status.Disable).ToString()) ? "active" : "";

                    string w1 = (AppMode) ? "col-xs-8" : "col-xs-6";
                    string w2 = (AppMode) ? "col-xs-12 text-left" : "col-xs-6 text-right";
                }

                @if (AppMode == false)
                {
                    <div class="@w1 text-left" style="padding-left: 0px">
                        <button class="btn btn-xs btn-pink  @ActiveShareYN " type="button" onclick="Clearleft();doSearchBool('whereShareYN', @Model.whereShareYN.ToString().ToLower()  );" title="被推薦文章">被推薦文章</button>
                        <button class="btn btn-xs btn-pink  @ActiveComment " type="button" onclick="Clearleft();doSearchBool('whereComment', @Model.whereComment.ToString().ToLower() );">被鼓勵文章</button>
                        <button class="btn btn-xs btn-pink  @ActiveCommentCash " type="button" onclick="Clearleft();doSearchBool('whereCommentCash', @Model.whereCommentCash.ToString().ToLower() );">有幫助的鼓勵文章</button>
                    </div>
                }

                <div class="@w2">
                    @if (AppMode)
                    {
                        <button class="btn btn-xs btn-pink  @ActiveShareYN " type="button" onclick="Clearleft();doSearchBool('whereShareYN', @Model.whereShareYN.ToString().ToLower()  );" title="被推薦文章">被推薦文章</button>
                        <button class="btn btn-xs btn-pink  @ActiveComment " type="button" onclick="Clearleft();doSearchBool('whereComment', @Model.whereComment.ToString().ToLower() );">被鼓勵文章</button>
                        <button class="btn btn-xs btn-pink  @ActiveCommentCash " type="button" onclick="Clearleft();doSearchBool('whereCommentCash', @Model.whereCommentCash.ToString().ToLower() );">有幫助的鼓勵文章</button>
                    }

                    @if ((ViewBag.VisibleVerify == "Y" || ViewBag.VisibleSearchDelList == "Y") && Model.BackAction == "Index")
                    {
                        <button class="btn btn-xs btn-pink  @ActiveAll" type="button" onclick="DoWRITING_STATUS('')">@btnActiveAll</button>
                        <button class="btn btn-xs btn-pink  @ActiveUN" onclick="DoWRITING_STATUS('@Convert.ToSByte(ADDStatus.eADDT01Status.Draft),@Convert.ToSByte(ADDStatus.eADDT01Status.TurnVerify)')" type="button">待批閱</button>
                        <button class="btn btn-xs btn-pink  @ActiveIN" onclick="DoWRITING_STATUS('@Convert.ToSByte(ADDStatus.eADDT01Status.Verified)')" type="button">已批閱</button>
                        if (ViewBag.VisibleSearchDelList == "Y")
                        {
                            <button class="btn btn-xs btn-pink  @ActiveDel" onclick="DoWRITING_STATUS('@Convert.ToSByte(ADDStatus.eADDT01Status.Disable)')" type="button">已作廢</button>
                        }
                    }
                    @if (user != null)
                    {
                        if (user.USER_TYPE == UserType.Student || user.USER_TYPE == UserType.Parents)
                        {
                            string MyBtnName = string.Empty;
                            string whereUserNoValue = string.Empty;
                            string whereFriendsUserNo = string.Empty;
                            user = EcoolWeb.Models.UserProfileHelper.Get();
                            if (user.USER_TYPE == UserType.Student)
                            {
                                MyBtnName = "我的文章";
                                whereUserNoValue = user.USER_NO;
                            }
                            else if (user.USER_TYPE == UserType.Parents)
                            {
                                MyBtnName = "寶貝文章";
                                whereUserNoValue = HRMT06.GetStringMyPanyStudent(user);
                            }

                            whereFriendsUserNo = HRMT07.GetStringMyADDStudent(user);

                            string ActiveAll2 = (Model.whereUserNo == string.Empty || (Model.whereUserNo == null && Model.whereWritingStatus != Convert.ToSByte(ADDStatus.eADDT01Status.DraftUnSubmit).ToString())) ? "active" : "";
                            string ActiveMe = (Model.whereUserNo == whereUserNoValue && Model.whereWritingStatus != Convert.ToSByte(ADDStatus.eADDT01Status.DraftUnSubmit).ToString()) ? "active" : "";
                            string ActiveDraft = (Model.whereWritingStatus == Convert.ToSByte(ADDStatus.eADDT01Status.DraftUnSubmit).ToString()) ? "active" : "";
                            string ActiveBack = (Model.whereWritingStatus == Convert.ToSByte(ADDStatus.eADDT01Status.Back).ToString()) ? "active" : "";

                            string ActiveDisable = (Model.whereWritingStatus == Convert.ToSByte(ADDStatus.eADDT01Status.Disable).ToString()) ? "active" : "";
                            string ActiveFriends = (Model.whereUserNo == whereFriendsUserNo) ? "active" : "";

                            <button class="btn btn-xs btn-pink  @ActiveAll2" type="button" onclick="$('#whereWritingStatus').val('');doSearch('whereUserNo', '');">全部</button>
                            <button class="btn btn-xs btn-pink  @ActiveMe" type="button" onclick="$('#whereWritingStatus').val('');doSearch('whereUserNo','@whereUserNoValue');">@MyBtnName</button>
                            if (user.USER_TYPE == UserType.Student)
                            {
                                <button class="btn btn-xs btn-pink  @ActiveDraft" onclick="DoSEARCH_With_STATUS('@Convert.ToSByte(ADDStatus.eADDT01Status.DraftUnSubmit)','@whereUserNoValue');" type="button">草稿</button>
                                <button class="btn btn-xs btn-pink  @ActiveDisable" onclick="DoSEARCH_With_STATUS('@Convert.ToSByte(ADDStatus.eADDT01Status.Disable)','@whereUserNoValue');" type="button">作廢</button>
                                <button class="btn btn-xs btn-pink  @ActiveBack" onclick="DoSEARCH_With_STATUS('@Convert.ToSByte(ADDStatus.eADDT01Status.Back)','@whereUserNoValue');" type="button">再修改</button>

                            }

                            if (string.IsNullOrWhiteSpace(whereFriendsUserNo) == false)
                            {
                                <button class="btn btn-xs btn-pink  @ActiveFriends" onclick="doSearch('whereUserNo','@whereFriendsUserNo');" type="button">訂閱文章</button>
                            }
                        }
                    }
                </div>
            </div>
        </div>}

    @*<div class="show-more">
            <a class="btn btn-xs btn-pink" href="#">更多</a>
        </div>*@

}

@helper  buttonFunD(ADDV03 item, ECOOL_APP.UserProfile user, bool AppMode)
{
    if (item.WRITING_STATUS.Value == (byte)ADDStatus.eADDT01Status.DraftUnSubmit || item.WRITING_STATUS.Value == (byte)ADDStatus.eADDT01Status.Draft || item.WRITING_STATUS.Value == (byte)ADDStatus.eADDT01Status.TurnVerify || item.WRITING_STATUS.Value == (byte)ADDStatus.eADDT01Status.Back || item.WRITING_STATUS.Value == (byte)ADDStatus.eADDT01Status.Verified)
    {
        if (user != null)
        {
            if (user.USER_NO == item.USER_NO)
            {
                if (string.IsNullOrWhiteSpace(item.BACK_MEMO) == false && (item.WRITING_STATUS.Value == (byte)ADDStatus.eADDT01Status.Back))
                {

                    @Html.ActionLink("遭退回請修改", "Edit",
                                                new
                                                {
                                                    WRITING_NO = item.WRITING_NO,
                                                    BackAction = Model.BackAction,
                                                    OrdercColumn = Model.OrdercColumn,
                                                    whereKeyword = Model.whereKeyword,
                                                    whereUserNo = Model.whereUserNo,
                                                    whereWritingStatus = Model.whereWritingStatus,
                                                    whereShareYN = Model.whereShareYN,
                                                    whereComment = Model.whereComment,
                                                    whereCommentCash = Model.whereCommentCash,
                                                    whereCLASS_NO = Model.whereCLASS_NO,
                                                    whereGrade = Model.whereGrade,
                                                    Page = Model.Page
                                                }, new { @class = "btn btn-xs btn-Basic",title=item.BACK_MEMO })
                }
                else if (string.IsNullOrWhiteSpace(item.BACK_MEMO) == true && (item.WRITING_STATUS.Value == (byte)ADDStatus.eADDT01Status.Back))
                {
                    @Html.ActionLink("退回修改", "Edit",
                                                new
                                                {
                                                    WRITING_NO = item.WRITING_NO,
                                                    BackAction = Model.BackAction,
                                                    OrdercColumn = Model.OrdercColumn,
                                                    whereKeyword = Model.whereKeyword,
                                                    whereUserNo = Model.whereUserNo,
                                                    whereWritingStatus = Model.whereWritingStatus,
                                                    whereShareYN = Model.whereShareYN,
                                                    whereComment = Model.whereComment,
                                                    whereCommentCash = Model.whereCommentCash,
                                                    whereCLASS_NO = Model.whereCLASS_NO,
                                                    whereGrade = Model.whereGrade,
                                                    Page = Model.Page
                                                }, new { @class = "btn btn-xs btn-Basic",title=item.BACK_MEMO })
                }
                else if (item.WRITING_STATUS.Value == (byte)ADDStatus.eADDT01Status.DraftUnSubmit || item.WRITING_STATUS.Value == (byte)ADDStatus.eADDT01Status.Draft)
                {
                    @Html.ActionLink("修改", "Edit",
                                                new
                                                {
                                                    WRITING_NO = item.WRITING_NO,
                                                    BackAction = Model.BackAction,
                                                    OrdercColumn = Model.OrdercColumn,
                                                    whereKeyword = Model.whereKeyword,
                                                    whereUserNo = Model.whereUserNo,
                                                    whereWritingStatus = Model.whereWritingStatus,
                                                    whereShareYN = Model.whereShareYN,
                                                    whereComment = Model.whereComment,
                                                    whereCommentCash = Model.whereCommentCash,
                                                    whereCLASS_NO = Model.whereCLASS_NO,
                                                    whereGrade = Model.whereGrade,
                                                    Page = Model.Page
                                                }, new { @class = "btn btn-xs btn-Basic" })
                }

            }
            else if (
                   (user.USER_NO == item.VERIFIER || (ViewBag.VisibleVerify == "Y" && com.ecool.service.PermissionService.isMyClassNO(item.CLASS_NO, user, user.USER_NO)))
                && (item.WRITING_STATUS.Value == (byte)ADDStatus.eADDT01Status.Draft || item.WRITING_STATUS.Value == (byte)ADDStatus.eADDT01Status.TurnVerify)
                )
            {
                @Html.ActionLink("批閱", "Verify",
                                               new
                                               {
                                                   WRITING_NO = item.WRITING_NO,
                                                   BackAction = Model.BackAction,
                                                   OrdercColumn = Model.OrdercColumn,
                                                   whereKeyword = Model.whereKeyword,
                                                   whereUserNo = Model.whereUserNo,
                                                   whereWritingStatus = Model.whereWritingStatus,
                                                   whereShareYN = Model.whereShareYN,
                                                   whereComment = Model.whereComment,
                                                   whereCommentCash = Model.whereCommentCash,
                                                   whereCLASS_NO = Model.whereCLASS_NO,
                                                   whereGrade = Model.whereGrade,
                                                   Page = Model.Page
                                               }, new { @class = "btn btn-xs btn-Basic" })
            }
            else if (user != null && (user.USER_TYPE == "A" || ViewBag.ISLEVEL == "4.0000") && string.IsNullOrWhiteSpace(item.BACK_MEMO) == false && (item.WRITING_STATUS.Value == (byte)ADDStatus.eADDT01Status.Back))
            {
                @Html.ActionLink("遭退回請修改", "Edit",
                                            new
                                            {
                                                WRITING_NO = item.WRITING_NO,
                                                BackAction = Model.BackAction,
                                                OrdercColumn = Model.OrdercColumn,
                                                whereKeyword = Model.whereKeyword,
                                                whereUserNo = Model.whereUserNo,
                                                whereWritingStatus = Model.whereWritingStatus,
                                                whereShareYN = Model.whereShareYN,
                                                whereComment = Model.whereComment,
                                                whereCommentCash = Model.whereCommentCash,
                                                whereCLASS_NO = Model.whereCLASS_NO,
                                                whereGrade = Model.whereGrade,
                                                Page = Model.Page
                                            }, new { @class = "btn btn-xs btn-Basic",title=item.BACK_MEMO })
            }

            else if (ViewBag.VisiblePASS_DEL == "Y" && item.WRITING_STATUS == (byte)ADDStatus.eADDT01Status.Verified)
            {
                @Html.ActionLink("批閱後作廢&修改", "PASS_DEL",
                                               new
                                               {
                                                   WRITING_NO = item.WRITING_NO,
                                                   BackAction = Model.BackAction,
                                                   OrdercColumn = Model.OrdercColumn,
                                                   whereKeyword = Model.whereKeyword,
                                                   whereUserNo = Model.whereUserNo,
                                                   whereWritingStatus = Model.whereWritingStatus,
                                                   whereShareYN = Model.whereShareYN,
                                                   whereComment = Model.whereComment,
                                                   whereCommentCash = Model.whereCommentCash,
                                                   whereCLASS_NO = Model.whereCLASS_NO,
                                                   whereGrade = Model.whereGrade,
                                                   Page = Model.Page
                                               }, new { @class = "btn btn-xs btn-Basic" })
            }
        }
    }
}

@if (AppMode)
{
    @*<div class="Title_Secondary" style="font-size:20px;text-align:center">線上投稿一覽表</div>
        <br>*@
}
else
{
    if (user != null)
    {

        @Html.Partial("_Title_Secondary")
        <div class="p-context">@Html.Raw(HttpUtility.HtmlDecode(@Model.Context))</div>}
}

@using (Html.BeginForm("Index", "ADDI01", FormMethod.Post, new { id = "ADDI01", name = "form1" }))
{

    if (user == null)
    { }
    else
    {
        <div class="form-inline">

            @if (ViewBag.VisibleADD == "Y" && (Model?.WhereIsColorboxForUser ?? false) == false)
            {
                if (user?.USER_TYPE == UserType.Student)
                {
                    <a role="button" href='@Url.Action("Create", "ADDI01")' class="btn btn-sm btn-sys">
                        我要投稿
                    </a>
                    <br />
                }
            }

            @Html.Partial("_Notice")

            @if ((Model?.WhereIsColorboxForUser ?? false) == false)
            {
                <button type="button" class="btn-default btn-sm" onclick="showSearch()" id="glyphicon-search">
                    <span class="glyphicon glyphicon-search fa-1x" aria-hidden="true" id="search-img">顯示搜尋條件</span>
                </button>
                <br />

                <div class="form-inline" role="form" id="search">
                    @if (user != null || user?.USER_TYPE == UserType.Admin || user?.USER_TYPE == UserType.Teacher)
                    {
                        <div class="form-group">
                            <label class="control-label">學號/姓名/文章標題</label>
                        </div>

                    }
                    else
                    {
                        <div class="form-group">
                            <label class="control-label">學號/文章標題</label>
                        </div>

                    }
                    <div class="form-group">
                        @Html.EditorFor(m => m.whereKeyword, new { htmlAttributes = new { @class = "form-control input-sm" } })
                    </div>
                    <div class="form-group">
                        <label class="control-label">年級</label>
                    </div>
                    <div class="form-group">
                        @Html.DropDownListFor(m => m.whereGrade, (IEnumerable<SelectListItem>)ViewBag.GradeItem, new { @class = "form-control input-sm", onchange = "FunPageProc(1)" })
                    </div>
                    <div class="form-group">
                        <label class="control-label">班級</label>
                    </div>
                    <div class="form-group">
                        @Html.DropDownListFor(m => m.whereCLASS_NO, (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control input-sm", onchange = "FunPageProc(1)" })
                    </div>
                    @if (user != null || user?.USER_TYPE == UserType.Admin || user?.USER_TYPE == UserType.Teacher)
                    {

                        <div class="form-group">
                            <label class="control-label">座號</label>
                        </div>

                        <div class="form-group">

                            @Html.EditorFor(m => m.whereSeat_NO, new { htmlAttributes = new { @class = "form-control input-sm", @style = "width:70px" } })
                        </div>}

                    <div class="clearfix" style="padding-right:15px">
                        <div class="pull-right">

                            <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1)" />
                            <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" style="margin-left: 4px;" />
                        </div>
                    </div>
                </div>

            }
        </div>}
    @Html.HiddenFor(m => m.WhereIsColorboxForUser)
    @Html.HiddenFor(m => m.OrdercColumn)
    @Html.HiddenFor(m => m.whereUserNo)
    @Html.HiddenFor(m => m.whereWritingStatus)
    @Html.HiddenFor(m => m.Page)
    @Html.HiddenFor(m => m.whereShareYN)
    @Html.HiddenFor(m => m.whereComment)
    @Html.HiddenFor(m => m.whereCommentCash)
    @Html.Hidden("AppMode", AppMode)

    <input type="hidden" id="doClear" value="false" />

    if ((Model?.WhereIsColorboxForUser ?? false) == false)
    {
        @buttonFun(user, AppMode)
    }
    //if ((Model?.WhereIsColorboxForUser ?? false))
    //{
    if (user == null)
    {

        <h1>  <center><b>《系統訊息》</b></center></h1><br />
        <h1>
            <b>你沒有閱讀權限，如果想觀看精彩內容，請登入</b></h1>

            }
            else
            {
            <img src="~/Content/img/web-revise-submit-04.png" style="width:100%" class="img-responsive App_hide" alt="Responsive image" />
            <div class="table-responsive">
                <div class="text-center">
                    <table class="table-ecool table-92Per table-hover table-ecool-List">
                        <thead>
                            <tr>
                                @if (AppMode)
                                {
                                    <th></th>
                                }
                                <th class="AppGird" style="text-align: center;cursor:pointer;" onclick="doSort('CRE_DATE');">
                                    投稿日
                                    <img id="CRE_DATE" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                                </th>
                                <th class="AppMode_hide" style="text-align: center;">
                                    學年
                                </th>
                                <th class="AppMode_hide" style="text-align: center;">
                                    學期
                                </th>

                                <th class="AppGird" style="text-align: center;cursor:pointer;" onclick="doSort('CLASS_NO');">
                                    班級
                                    <img id="CLASS_NO" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                                </th>
                                @if (user != null || user?.USER_TYPE == UserType.Admin || user?.USER_TYPE == UserType.Teacher)
                                {

                                }
                                <th class="AppGird" style="text-align: center;cursor:pointer;">
                                    座號
                                    @*<img id="CLASS_NO" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />*@
                                </th>
                                <th class="AppGird" style="text-align: center;cursor:pointer;" onclick="doSort('SNAME');">
                                    姓名
                                    <img id="SNAME" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                                </th>


                                <th class="AppGird" style="text-align: center;cursor:pointer;" onclick="doSort('SUBJECT');">
                                    文章標題
                                    <img id="SUBJECT" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                                </th>
                                <th class="AppGird" style="text-align: center;cursor:pointer;" onclick="doSort('READ_COUNT');">
                                    點閱數
                                    <img id="READ_COUNT" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                                </th>
                                <th class="AppGird" style="text-align: center;" onclick="doSort('comment');">
                                    留言數
                                    <img id="comment" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                                </th>
                                @{ string str1 = Convert.ToSByte(ADDStatus.eADDT01Status.Disable) + "," + Convert.ToSByte(ADDStatus.eADDT01Status.Back); }
                                @if ((Model.whereWritingStatus == str1))
                                {

                                    <th class="AppGird" style="text-align: center;cursor:pointer;" onclick="doSort('DEL_DATE');">
                                        作廢日期
                                        <img id="DEL_DATE" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                                    </th>

                                }
                                @if (AppMode == false)
                                {
                                    <th></th>
                                }
                            </tr>
                        </thead>
                        <tbody>
                            @if (Model.ADDV03List.Count() == 0)
                            {
                                <tr><td colspan="8">目前無需要批閱的內容</td></tr>
                            }
                            else
                            {
                                foreach (var item in Model.ADDV03List)
                                {
                                    if (item.WRITING_STATUS == (byte)ADDStatus.eADDT01Status.Back)
                                    {
                                        /* 再修改只有本人、老師、管理者能看到 */
                                        if (user?.USER_NO != item.USER_NO && user?.USER_TYPE != UserType.Admin && user?.USER_TYPE != UserType.Teacher)
                                        {
                                            continue; // 跳過不顯示
                                        }
                                    }
                                    <tr style="text-align: center;">
                                        @if (AppMode)
                                        {
                                            <td>
                                                @buttonFunD(item, user, AppMode)
                                            </td>
                                            <td>
                                                @item.CRE_DATE.Value.ToString("MM/dd")
                                            </td>
                                        }
                                        else
                                        {
                                            <td>
                                                @Html.DisplayFor(modelItem => item.CRE_DATE, "ShortDateTime")
                                            </td>
                                        }

                                        <td class="AppMode_hide">
                                            @Html.DisplayFor(modelItem => item.SYEAR)
                                        </td>
                                        <td class="AppMode_hide">
                                            @Html.DisplayFor(modelItem => item.SEMESTER)
                                        </td>

                                        <td>
                                            @Html.DisplayFor(modelItem => item.CLASS_NO)
                                        </td>
                                        @if (user?.USER_NO == item.USER_NO || user?.USER_TYPE == UserType.Admin || user?.USER_TYPE == UserType.Teacher)
                                        {

                                            if (item.AutherYN != null && item.AutherYN == false)
                                            {


                                                <td>
                                                    @Html.DisplayFor(modelItem => item.SEAT_NO)
                                                </td>
                                                <td style="text-align: center;cursor:pointer;white-space:nowrap" onclick="doSearch('whereUserNo','@item.USER_NO');">
                                                    @Html.DisplayFor(modelItem => item.SNAME)
                                                </td>
                                            }
                                            else
                                            {

                                                <td></td>
                                                <td style="text-align: center;cursor:pointer;white-space:nowrap" onclick="doSearch('whereUserNo','@item.USER_NO');">
                                                    暫時不顯示
                                                </td>

                                            }

                                        }
                                        else
                                        {
                                            if (item.AutherYN != null && item.AutherYN == false)
                                            {

                                                <td>
                                                    @Html.DisplayFor(modelItem => item.SEAT_NO)
                                                </td>
                                                <td style="text-align: center;cursor:pointer;white-space:nowrap" onclick="doSearch('whereUserNo','@item.USER_NO');">
                                                    @Html.DisplayFor(modelItem => item.SNAME)
                                                </td>
                                            }
                                            else
                                            {
                                                <td></td>
                                                <td style="text-align: center;cursor:pointer;white-space:nowrap" onclick="doSearch('whereUserNo','@item.USER_NO');">
                                                    暫時不顯示
                                                </td>

                                            }
                                        }
                                        <td style="text-align: left;white-space:normal">
                                            <a href='@Url.Action("Details", "ADDI01"
                                , new {
                                    WRITING_NO = item.WRITING_NO,
                                    BackAction = Model.BackAction,
                                    OrdercColumn = Model.OrdercColumn,
                                    whereKeyword = Model.whereKeyword,
                                    whereUserNo = Model.whereUserNo,
                                    whereWritingStatus = Model.whereWritingStatus,
                                    whereShareYN = Model.whereShareYN,
                                    whereComment = Model.whereComment,
                                    whereCommentCash = Model.whereCommentCash,
                                    whereCLASS_NO = Model.whereCLASS_NO,
                                    whereGrade = Model.whereGrade,
                                    Page = Model.Page
                                })' class="btn-table-link">
                                                @if (AppMode)
                                                {
                                                    <Text>@item.SUBJECT.Substring(0, (item.SUBJECT.Length >= 5) ? 5 : item.SUBJECT.Length) ...</Text>

                                                }
                                                else
                                                {
                                                    if (item.WRITING_STATUS == (byte)ADDStatus.eADDT01Status.Disable)
                                                    {
                                                        string[] strleng = null;
                                                        string strleng1 = "";
                                                        if (!string.IsNullOrEmpty(item.BACK_MEMO))
                                                        {
                                                            strleng = item.BACK_MEMO.Split('：');
                                                            if (strleng != null && strleng.Length > 0)
                                                            {
                                                                strleng1 = strleng[0];

                                                            }
                                                        }

                                                        <Text>@item.SUBJECT <font style="color:red"> @(strleng1)</font> </Text>
                                                    }
                                                    else
                                                    {
                                                        <Text>@item.SUBJECT </Text>
                                                    }
                                                }
                                            </a>

                                            @if (item.SHARE_YN == "y")
                                            {
                                                <img src="~/Content/img/icons-like-05.png" />
                                            }
                                            @if (item.WRITING_STATUS == (byte)ADDStatus.eADDT01Status.Verified && item.CASH.HasValue)
                                            {

                                                if (item.CASH > 0)
                                                {
                                                    <span style="color:red">
                                                        +
                                                        @item.CASH
                                                    </span>
                                                }
                                                else
                                                { <span style="color:red">
                                                        @item.CASH
                                                    </span>
                                                }

                                            }
                                            @if (item.WRITING_STATUS == (byte)ADDStatus.eADDT01Status.Back)
                                            {
                                                <span style="color:red">[再修改]</span>
                                            }
                                        </td>
                                        <td>
                                            @Html.DisplayFor(modelItem => item.READ_COUNT)
                                        </td>
                                        <td>
                                            <span title="@Html.DisplayFor(modelItem => item.LastComment)">
                                                @Html.DisplayFor(modelItem => item.CommentCount)
                                            </span>
                                        </td>
                                        @if ((Model.whereWritingStatus == str1))
                                        {
                                            <td data-toggle="tooltip" title="狀態:  @ADDStatus.GetADDT01StatusString((byte)item.WRITING_STATUS)  ">
                                                @Html.DisplayFor(modelItem => item.DEL_DATE, "ShortDateTime")
                                            </td>

                                        }

                                        @if (AppMode == false)
                                        {
                                            <td>
                                                @buttonFunD(item, user, AppMode)
                                            </td>
                                        }
                                    </tr>
                                }
                            }
                        </tbody>
                    </table>
                </div>
            </div>

            <div>
                @Html.Pager(Model.ADDV03List.PageSize, Model.ADDV03List.PageNumber, Model.ADDV03List.TotalItemCount).Options(o => o
                      .DisplayTemplate("BootstrapPagination")
                     .MaxNrOfPages(5)
                     .SetPreviousPageText("上頁")
                     .SetNextPageText("下頁")
                 )
            </div>
            }
            }

@section Scripts {
    <script nonce="cmlvaw">
        // 設置全局 URL 配置
        window.ADDI01_URLS = {
            indexAction: "@Url.Action("Index", "ADDI01")"
        };
    </script>
    <script src="~/Scripts/ADDI01/index.js" nonce="cmlvaw"></script>
}
