﻿@model ECOOL_APP.com.ecool.Models.DTO.SECI05EncourageViewModel
@{
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
}

<div class="container">
    <h4>@ViewBag.Panel_Title</h4>
    <p class="text-danger">※ 加油名單為本學期借書量後30%學生</p>
    @using (Html.BeginForm("_EncourageDiv", "SECI05", FormMethod.Post))
    {
        <div>
            @Html.DropDownListFor(m => m.WhereCLASS_NO, (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control", onchange = "this.form.submit()" })
        </div>
        <br />
        if (Model.EncourageUserList != null && Model.EncourageUserList.Count > 0)
        {
            <table class="table table-bordered table-hover">
                <thead class="text-primary">
                    <tr>
                        <th>@Html.DisplayNameFor(m => m.EncourageUserList.FirstOrDefault().NAME)</th>
                        <th>@Html.DisplayNameFor(m => m.EncourageUserList.FirstOrDefault().CLASS_NO)</th>
                        <th>@Html.DisplayNameFor(m => m.EncourageUserList.FirstOrDefault().USER_NO)</th>
                        <th>@Html.DisplayNameFor(m => m.EncourageUserList.FirstOrDefault().THIS_SESEM_QTY)</th>
                        <th>@Html.DisplayNameFor(m => m.EncourageUserList.FirstOrDefault().SumQTY)</th>
                        <th>@Html.DisplayNameFor(m => m.EncourageUserList.FirstOrDefault().LAST_BORROW_DATE)</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var u in Model.EncourageUserList)
                    {
                        <tr class="LinkTR" onclick="window.open('@Url.Action("Index","SECI05",new {  whereUser_NO = u.USER_NO, WhereCLASS_NO = u.CLASS_NO })','_blank')">
                            <td>@u.NAME</td>
                            <td>@u.CLASS_NO</td>
                            <td>@u.USER_NO</td>
                            <td>@u.THIS_SESEM_QTY</td>
                            <td>@u.SumQTY</td>
                            <td>
                            @u.LAST_BORROW_DATE
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        }
        else
        {
            <div>統計母體數量不足。</div>
        }
    }

</div>


@section css{
    <style>
        .LinkTR:hover {
            cursor: pointer;
        }
    </style>
}
