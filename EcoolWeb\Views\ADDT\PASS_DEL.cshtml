﻿@model ECOOL_APP.EF.ADDT06
@using EcoolWeb.Util;

@{
    ViewBag.Title = "閱讀認證-已批閱後修改/作廢";
    EcoolWeb.Models.ADDT06ViewModel Search = TempData["Search"] as EcoolWeb.Models.ADDT06ViewModel;

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    if (string.IsNullOrWhiteSpace(UUID) == false)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
}

<link href="~/Content/css/EzCss.css" rel="stylesheet" />
<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
<script src="~/Content/ckeditor/ckeditor.js"></script>
<script src="~/Content/colorbox/jquery.colorbox.js"></script>

@Html.Partial("_Title_Secondary")

@using (Html.BeginForm("PASS_DEL", "ADDT"
     , new
     {
         whereKeyword = Search.whereKeyword,
         whereUserNo = Search.whereUserNo,
         whereBOOK_NAME = Search.whereBOOK_NAME,
         whereAPPLY_STATUS = Search.whereAPPLY_STATUS,
         OrdercColumn = Search.OrdercColumn,
         whereCLASS_NO = Search.whereCLASS_NO,
         whereGrade = Search.whereGrade,
         Page = Search.Page
     }
    , FormMethod.Post, new { name = "form1", enctype = "multipart/form-data" }))
{
    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })
    @Html.HiddenFor(model => model.APPLY_NO)
    @Html.Partial("_Notice")
    @Html.Hidden("BtnType")
    <img src="~/Content/img/web-Bar-09.png" style="width:100%" class="img-responsive App_hide" alt="Responsive image" />
    <div class="Div-EZ-reader">
        <div class="form-horizontal">
            <div class="form-group">
                @Html.LabelFor(model => model.PASSPORT_YN, htmlAttributes: new { @class = "ccontrol-label-left label_dt col-md-4" })
                <div class="col-md-8">
                    @Html.CheckBox("PASSPORT_YN", DataConvertHelper.YnToBool(Model.PASSPORT_YN), new { @disabled = "disabled" }) 是
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.USER_NO, htmlAttributes: new { @class = "ccontrol-label-left label_dt col-md-4" })
                <div class="col-md-8">
                    @Html.EditorFor(model => model.USER_NO, new { htmlAttributes = new { @class = "form-control", @readonly = "readonly", @type = "text" } })
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.NAME, htmlAttributes: new { @class = "ccontrol-label-left label_dt col-md-4" })
                <div class="col-md-8">
                    @Html.EditorFor(model => model.NAME, new { htmlAttributes = new { @class = "form-control", @readonly = "readonly", @type = "text" } })
                </div>
            </div>
            <div class="form-group">
                @Html.LabelFor(model => model.SEAT_NO, htmlAttributes: new { @class = "ccontrol-label-left label_dt col-md-4" })
                <div class="col-md-8">
                    @Html.EditorFor(model => model.SEAT_NO, new { htmlAttributes = new { @class = "form-control", @readonly = "readonly", @type = "text" } })
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.BOOK_NAME, htmlAttributes: new { @class = "ccontrol-label-left label_dt col-md-4" })
                <div class="col-md-8">
                    @Html.EditorFor(model => model.BOOK_NAME, new { htmlAttributes = new { @class = "form-control", @readonly = "readonly", @type = "text" } })
                    <br />
                    <label class="text-info">PS.因牽涉到共讀本，故書名無法更動</label>
                </div>
            </div>
            <div class="form-group">
                @Html.LabelFor(model => model.REVIEW_VERIFY, htmlAttributes: new { @class = "ccontrol-label-left label_dt col-md-12" })
                <div class="col-md-12">
                    @Html.TextAreaFor(model => model.REVIEW_VERIFY, 15, 200, new { @class = "ckeditor" })
                    @Html.ValidationMessageFor(model => model.REVIEW_VERIFY, "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="form-group">
                @Html.Label("原上傳圖檔", htmlAttributes: new { @class = "ccontrol-label-left label_dt  col-md-12" })
                <div class="col-md-12">
                    @if (string.IsNullOrWhiteSpace(ViewBag.ImageUrl) == false)
                    {
                        <img src='@ViewBag.ImageUrl' style="width:auto;height:auto;max-width:300px" alt="無原上傳圖檔" title="原上傳圖檔" class="group1" href="@ViewBag.ImageUrl" />
                    }
                </div>
            </div>

            @if (ViewBag.ShowImg)
            {
                <div class="form-group">
                    @Html.LabelFor(model => model.IMG_FILE, htmlAttributes: new { @class = "ccontrol-label-left label_dt col-md-4" })
                    <div class="col-md-8">
                        @Html.Action("Upload", "Comm")
                    </div>
                </div>
            }

            <div class="form-group">
                @Html.LabelFor(model => model.SHARE_YN, htmlAttributes: new { @class = "ccontrol-label-left label_dt col-md-4" })
                <div class="col-md-8">
                    @if (ViewBag.ShowShare)
                    {
                    
                            @Html.CheckBox("SHARE_YN", DataConvertHelper.YnToBool(Model.SHARE_YN))
                            <samp>推薦</samp>
                       
                    }
                    else
                    {
                        if (Model.SHARE_YN.ToUpper() == "Y")
                        {
                            <div>推薦</div>
                        }
                        @Html.Hidden("SHARE_YN", DataConvertHelper.YnToBool(Model.SHARE_YN))
                    }
                    @Html.ValidationMessageFor(model => model.SHARE_YN, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group  Div-btn-center">
                <a href='@Url.Action("ADDTALLList", "ADDT"
                       ,new {
                           whereKeyword = Search.whereKeyword,
                           whereUserNo=Search.whereUserNo,
                           whereBOOK_NAME=Search.whereBOOK_NAME,
                           whereAPPLY_STATUS=Search.whereAPPLY_STATUS,
                           OrdercColumn = Search.OrdercColumn,
                           whereCLASS_NO= Search.whereCLASS_NO,
                              whereGrade = Search.whereGrade,
                           Page = Search.Page
                       })' class="btn btn-default">
                    返回
                </a>
                @if (Model.APPLY_STATUS != ADDT06.APPLY_STATUS_Val.APPLY_TYPE_DEL)
                {
                    <samp>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</samp>
                    <button type="button" id="EditButton" data-loading-text="Loading..." class="btn btn-default" autocomplete="off">
                        修改
                    </button>
                }
            </div>
        </div>

        @if (Model.APPLY_STATUS != ADDT06.APPLY_STATUS_Val.APPLY_TYPE_DEL)
        {
            <div class="Div-BK">
                <div class="Div-btn-center">
                    @if (ViewBag.BtnPassDel == true)
                    {
                        <div class="form-group text-left">
                            &nbsp;&nbsp;
                            @Html.Label("退回/作廢原因", htmlAttributes: new { @class = "ccontrol-label-left label_dt col-md-12 col-sm-12" })
                            <div class="col-md-12  col-sm12">
                                @Html.DropDownList("BACK_MEMO_DropDownList", (IEnumerable<SelectListItem>)ViewBag.BackSelectItem, new { @class = "form-control", @onchange = "BackDropDownList(this.value)" })
                                @Html.Hidden("BACK_MEMO")
                            </div>
                        </div>
                        <div class="form-group">
                            <div>
                                <button type="button" id="DelButton" data-loading-text="Loading..." class="btn btn-default" autocomplete="off">
                                    作廢
                                </button>
                            </div>
                        </div>
                    }
                    else
                    {
                        <button type="button" id="NotDelButton" data-loading-text="Loading..." class="btn btn-default" autocomplete="off">
                            無法作廢寄發Mail提醒
                        </button>
                    }
                </div>
            </div>
        }
    </div>
}

<script type="text/javascript">

    $(document).ready(function () {
        $(".group1").colorbox({ rel: 'group1', opacity: 0.82 });
        var SHARE = $("#SHARE_YN").val();
        if ("@Model.SHARE_YN"=="N")
        {
            $("#SHARE_YN").removeAttr("checked");
        }
    });

    $('#DelButton').on('click', function () {

        var $this = $(this);
        $this.attr('disabled', 'disabled').html("Loading...");

        var ErrorMsg = ''

        if ($('#BACK_MEMO').val() == '') {
            ErrorMsg = ErrorMsg + '請輸入退回/作廢原因'
        }
        if (ErrorMsg != '') {
            $this.removeAttr('disabled').html('作廢');
            alert(ErrorMsg)
            return false;
        }
        else {
            var YN = confirm("你確定要作廢?")
            if (YN) {
                $('#BtnType').val("@EcoolWeb.Controllers.ADDTController.PASS_TYPE.PASS_TYPE_D")
                form1.submit();
            }
            else {
                $this.removeAttr('disabled').html('作廢');
            }
        }
    })

    $('#NotDelButton').on('click', function () {

        var $this = $(this);
        $this.attr('disabled', 'disabled').html("Loading...");
        $('#BtnType').val("@EcoolWeb.Controllers.ADDTController.PASS_TYPE.PASS_TYPE_Mail")
        form1.submit();
    })

    $('#EditButton').on('click', function () {

        var $this = $(this);
        $this.attr('disabled', 'disabled').html("Loading...");
        $('#BtnType').val("@EcoolWeb.Controllers.ADDTController.PASS_TYPE.PASS_TYPE_E")
        form1.submit();

    })

    function BackDropDownList(Val) {
        if (Val == '@ECOOL_APP.com.ecool.service.BDMT02Service.OtherVal') {
            $('#BACK_MEMO').val("")
            $('#BACK_MEMO').attr("type", "text").attr("placeholder", "請輸入原因").addClass("form-control");
        }
        else {
            $('#BACK_MEMO').attr("type", "hidden").removeClass();
            $('#BACK_MEMO').val(Val)
        }
    }
</script>