﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'flash', 'pt', {
	access: 'Acesso ao Script',
	accessAlways: 'Sempre',
	accessNever: 'Nunca',
	accessSameDomain: 'Mesmo dominio',
	alignAbsBottom: 'Abs inferior',
	alignAbsMiddle: 'Abs centro',
	alignBaseline: 'Linha de base',
	alignTextTop: 'Topo do texto',
	bgcolor: 'Cor de fundo',
	chkFull: 'Permitir Ecrã inteiro',
	chkLoop: 'Cíclico',
	chkMenu: 'Permitir menu do Flash',
	chkPlay: 'Reproduzir automaticamente',
	flashvars: 'Variaveis para o Flash',
	hSpace: 'Esp. Horiz',
	properties: 'Propriedades do Flash',
	propertiesTab: 'Propriedades',
	quality: 'Qualidade',
	qualityAutoHigh: 'Alta Automaticamente',
	qualityAutoLow: 'Baixa Automaticamente',
	qualityBest: 'Melhor',
	qualityHigh: 'Alta',
	qualityLow: 'Baixa',
	qualityMedium: 'Média',
	scale: 'Escala',
	scaleAll: 'Mostrar tudo',
	scaleFit: 'Tamanho exato',
	scaleNoBorder: 'Sem bordas',
	title: 'Propriedades do Flash',
	vSpace: 'Esp.Vert',
	validateHSpace: 'HSpace tem de ser um numero.',
	validateSrc: 'O URL não pode ficar vazio',
	validateVSpace: 'VSpace tem de ser um numero.',
	windowMode: 'Modo de janela',
	windowModeOpaque: 'Opaco',
	windowModeTransparent: 'Transparente',
	windowModeWindow: 'Janela'
} );
