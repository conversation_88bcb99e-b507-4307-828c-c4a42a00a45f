﻿
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Text;

namespace ECOOL_APP.EF
{
    public class AWAI01ExchangeViewModel
    {

  
        public string CARD_ID { get; set; }
        public bool Status { get; set; }

        public AWAI01SearchViewModel Search { get; set; }


        public AWAI01IndexListViewModel AwaData { get; set; }

        /// <summary>
        /// 買完後關閉
        /// </summary>
        public string AfterClose { get; set; }

        /// <summary>
        /// 我要出價
        /// </summary>
        [DisplayName("我要出價")]
        public int? THIS_PRICE_CASH { get; set; }
         public int? CostCash { get; set; }
    }
}