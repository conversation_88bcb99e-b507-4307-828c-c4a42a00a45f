/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX-Web/Symbols/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.STIXMathJax_Symbols={directory:"Symbols/Regular",family:"STIXMathJax_Symbols",testString:"\u00A0\u2300\u2302\u2305\u2306\u230C\u230D\u230E\u230F\u2310\u2311\u2312\u2313\u2315\u2316",32:[0,0,250,0,0],160:[0,0,250,0,0],8960:[487,-14,606,25,581],8962:[774,0,926,55,871],8965:[577,0,620,48,572],8966:[728,0,620,48,572],8972:[166,215,463,52,412],8973:[166,215,463,52,412],8974:[876,-495,463,52,412],8975:[876,-495,463,52,412],8976:[393,-115,600,48,552],8977:[439,-65,523,75,449],8978:[331,0,762,50,712],8979:[331,0,762,50,712],8981:[582,189,847,26,796],8982:[748,246,1100,53,1047],8983:[749,245,1100,53,1047],8984:[662,156,926,55,871],8985:[393,-115,600,48,552],8986:[671,69,685,64,622],8988:[662,-281,463,51,411],8989:[662,-281,463,51,411],8990:[164,217,463,51,411],8991:[164,217,463,52,412],9001:[713,213,400,77,335],9002:[713,213,400,65,323],9004:[692,186,926,83,843],9005:[592,88,986,55,931],9006:[450,140,624,-18,574],9010:[562,56,889,80,809],9014:[751,156,926,85,841],9021:[683,179,910,84,826],9023:[703,176,683,60,623],9024:[703,176,683,60,623],9043:[751,176,794,55,739],9072:[751,176,794,55,739],9084:[584,220,871,50,820],9107:[386,-120,913,85,841],9108:[633,127,926,24,902],9135:[286,-220,315,0,315],9142:[495,-11,926,55,871],9166:[731,225,926,50,856],9186:[558,53,1144,54,1090],9187:[680,178,910,82,828],9188:[286,-220,1094,47,1047],9189:[527,20,1018,23,995],9190:[434,-72,926,55,871],9191:[606,97,798,194,733],10176:[584,0,685,50,634],10177:[811,127,1145,35,1110],10178:[662,0,693,52,641],10179:[529,27,685,60,625],10180:[529,27,685,61,626],10181:[702,198,455,55,400],10182:[702,198,455,55,400],10183:[536,29,620,31,589],10184:[533,25,966,60,906],10185:[533,25,966,60,906],10187:[662,156,902,0,863],10188:[806,213,325,20,325],10189:[662,156,902,0,863],10192:[744,242,1064,39,1025],10193:[536,29,620,31,589],10194:[536,31,620,48,572],10195:[584,0,685,50,634],10196:[584,0,685,50,634],10197:[582,80,1019,40,965],10198:[582,80,1019,54,979],10199:[582,80,1228,40,1188],10200:[718,213,866,50,816],10201:[718,213,866,50,816],10202:[662,0,1376,64,1312],10203:[662,0,1376,64,1312],10204:[403,-103,849,50,799],10205:[450,-57,1574,55,1519],10206:[450,-57,1574,55,1519],10207:[693,187,502,101,401],10208:[795,289,790,45,745],10209:[589,87,764,45,719],10210:[589,87,803,45,758],10211:[589,87,803,45,758],10212:[662,158,1182,45,1137],10213:[662,158,1182,45,1137],10214:[717,213,504,188,482],10215:[717,213,504,22,316],10218:[719,213,610,73,545],10219:[719,213,610,65,537],10220:[719,213,488,178,466],10221:[719,213,488,22,310],10624:[695,189,594,85,509],10625:[487,-14,565,46,519],10626:[566,59,503,110,393],10627:[719,213,596,108,477],10628:[719,213,596,119,488],10629:[719,213,463,70,393],10630:[719,213,463,70,393],10631:[719,214,511,115,367],10632:[719,214,511,144,396],10633:[719,213,511,100,352],10634:[719,213,511,159,411],10635:[719,213,469,188,447],10636:[719,213,469,22,281],10637:[719,213,469,188,447],10638:[719,213,469,22,281],10639:[719,213,469,188,447],10640:[719,213,469,22,281],10641:[719,213,400,73,357],10642:[719,213,400,73,357],10643:[649,143,685,34,591],10644:[649,143,685,94,651],10645:[649,143,685,86,643],10646:[649,143,685,42,599],10649:[661,155,211,50,161],10650:[662,156,511,177,334],10651:[547,72,685,42,662],10652:[584,0,685,50,634],10653:[584,0,685,50,634],10654:[547,0,685,11,675],10655:[396,0,685,24,643],10656:[517,13,685,57,654],10657:[609,-12,685,77,607],10658:[547,0,685,42,662],10659:[547,0,685,42,662],10660:[547,200,685,23,643],10661:[547,200,685,42,662],10662:[547,0,900,40,860],10663:[547,0,900,40,860],10664:[574,72,685,29,649],10665:[574,72,685,36,656],10666:[578,68,685,29,649],10667:[578,68,685,36,656],10668:[562,58,706,34,680],10669:[562,58,706,26,672],10670:[562,58,706,34,680],10671:[562,58,708,26,672],10672:[583,79,762,50,712],10673:[717,79,762,50,712],10674:[819,79,762,50,712],10675:[832,79,762,50,712],10676:[832,79,762,50,712],10677:[623,119,910,24,886],10678:[623,119,842,50,792],10679:[623,119,842,50,792],10680:[623,119,842,50,792],10681:[623,119,842,50,792],10682:[623,119,842,50,792],10683:[623,119,842,50,792],10684:[623,119,842,50,792],10685:[882,179,842,50,792],10686:[623,119,842,50,792],10687:[623,119,842,50,792],10688:[623,119,842,50,792],10689:[623,119,842,50,792],10690:[623,119,1091,50,1056],10691:[623,119,1091,50,1056],10692:[662,158,910,45,865],10693:[662,158,910,45,865],10694:[662,158,910,45,865],10695:[662,158,910,45,865],10696:[662,158,910,45,865],10697:[712,207,1046,64,982],10698:[1003,127,1145,35,1110],10699:[811,259,1145,35,1110],10700:[811,127,1145,35,1110],10701:[811,127,1165,15,1150],10702:[698,193,780,70,710],10703:[531,25,857,48,777],10704:[531,25,857,80,809],10705:[582,80,810,93,716],10706:[582,80,810,93,716],10707:[582,80,810,93,716],10708:[582,80,810,94,717],10709:[582,80,810,93,716],10710:[602,100,810,74,736],10711:[602,100,810,74,736],10712:[620,116,511,177,334],10713:[620,116,511,176,333],10714:[620,116,688,177,511],10715:[620,116,688,177,511],10716:[430,0,926,70,854],10717:[653,0,926,70,854],10718:[695,189,926,70,854],10719:[403,-103,1145,50,1095],10720:[662,157,910,45,865],10721:[512,8,667,24,613],10722:[414,0,790,64,726],10723:[662,156,685,47,637],10724:[842,156,685,47,637],10725:[662,156,685,48,637],10726:[584,78,798,60,738],10727:[695,189,628,48,580],10728:[811,127,1145,35,1110],10729:[811,127,1145,35,1110],10730:[744,241,762,32,730],10732:[743,241,762,50,712],10733:[743,241,762,50,712],10734:[747,243,762,97,665],10735:[747,243,762,97,665],10736:[747,243,762,32,730],10737:[747,243,762,32,730],10738:[747,243,762,65,697],10739:[747,243,762,65,697],10740:[521,13,926,55,871],10742:[765,80,520,94,426],10743:[662,80,520,94,426],10746:[532,25,685,64,621],10747:[532,25,685,64,621],10748:[713,213,459,77,394],10749:[713,213,459,65,382],10750:[540,36,762,93,669],10751:[316,-190,762,93,669],57498:[719,213,708,18,690],57499:[719,213,708,18,690],57535:[836,236,636,50,586],57536:[836,236,636,50,586],57537:[836,236,636,50,586],57538:[836,236,636,50,586],57539:[386,-120,750,50,700],57540:[478,-28,750,50,700],57541:[478,-28,750,50,700],57542:[286,-220,750,50,700],57543:[402,-120,750,50,700],57544:[386,-120,1000,50,950],57545:[478,-28,1000,50,950],57546:[544,38,1000,50,950],57547:[386,-120,750,50,700],57548:[478,-28,750,50,700],57549:[544,38,750,50,700],57550:[836,236,636,50,586],57551:[836,236,636,50,586],57552:[836,236,636,50,586],57553:[836,236,636,50,586],57554:[692,186,926,83,843],57555:[633,127,926,24,902],57556:[633,127,926,24,902],57557:[286,-220,1000,50,950],57558:[386,-120,750,50,700]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"STIXMathJax_Symbols"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Symbols/Regular/Main.js"]);
