﻿@model ECOOL_APP.EF.BarcCodeMyCashIndexViewModel
@{ @*string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();*@

}
<script type="text/javascript">
</script>
@if (Model.LoginYN == "N")
{
    <div class="table-responsive">

        <text>
            請先登入E酷幣
        </text>
    </div>
}
else
{
    Layout = null;
    @Html.AntiForgeryToken()
    <div id="PageContent">
        @Html.Action("IstoryUserIndex", (string)ViewBag.BRE_NO, new { model = Model, HttpMethod = "POST" })
    </div>
}