﻿@model RollCallBarcodeEditViewModel
@using ECOOL_APP.EF
@{
    /**/
    ECOOL_DEVEntities db = new ECOOL_DEVEntities();
    ViewBag.Title = "領取紙本點數人員一覽表";
}

<div class="Title_Secondary" style="width:740px;">領取紙本點數人員一覽表</div>
<br />
@Html.Partial("_Notice")
@*<div class="toolbar text-right" style="width:750px;">*@
<a role="button" href='@Url.Action("BarcodeRollCallIndex",(string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys text-right">
    <span class="fa fa-plus" aria-hidden="true"></span>
    回活動列表
</a>
@*</div>*@
@using (Html.BeginForm("StatisticsVoteDetail", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1", enctype = "multipart/form-data" }))
{
    <div id="PageContent">
        @Html.Action("StatisticsVoteDetail", (string)ViewBag.BRE_NO,new { Keyword=Model.Keyword })
    </div>
}

@section scripts{
    <script>
    function funAjax() {
        //var whereGradetxt = $("#whereGrade1").val();
        //console.log(whereGradetxt);
        //var whereCLASS_NOtxt = $("#whereCLASS_NO1").val();
        var WhereSearchtxt = $("#Main_ROLL_CALL_ID").val();
        //console.log(WhereSearchtxt);
        $.ajax({
            type: "GET",
                url: '@(Url.Action("StatisticsVoteDetail", (string)ViewBag.BRE_NO))',
            data: {
                Keyword:WhereSearchtxt,
                OrdercColumn: $('#OrdercColumn').val(),
                SyntaxName: $('#SyntaxName').val(),
            },
                async: false,
                cache: false,
                dataType: 'html',
                success: function (data) {

                    $("#cboxLoadedContent").html('');
                    $("#cboxLoadedContent").html(data);
                }
            });
    }
    function doSort(SortCol) {
        var sort = "";
        sort = SortCol;
        OrderByName = $('#OrdercColumn').val();
        SyntaxName = $('#SyntaxName').val();
        $('#OrdercColumn').val(SortCol);
        console.log(SyntaxName);
        if (OrderByName == SortCol) {

            if (SyntaxName == "Desc") {

                $('#OrdercColumn').val(sort);
                $('#SyntaxName').val("ASC");
            }
            else {

                $('#OrdercColumn').val(sort);
                $('#SyntaxName').val("Desc");
            }
        }
        else {

            $('#OrdercColumn').val(sort);
            $('#SyntaxName').val("Desc");
        }
        funAjax()
    }
    </script>}