﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'table', 'tt', {
	border: 'Чик калынлыгы',
	caption: 'Исем',
	cell: {
		menu: 'Күзәнәк',
		insertBefore: 'Алдына күзәнәк өстәү',
		insertAfter: 'Артына күзәнәк өстәү',
		deleteCell: 'Күзәнәкләрне бетерү',
		merge: 'Күзәнәкләрне берләштерү',
		mergeRight: 'Уң яктагы белән берләштерү',
		mergeDown: 'Астагы белән берләштерү',
		splitHorizontal: 'Күзәнәкне юлларга бүлү',
		splitVertical: 'Күзәнәкне баганаларга бүлү',
		title: 'Күзәнәк үзлекләре',
		cellType: 'Күзәнәк төре',
		rowSpan: 'Юлларны берләштерү',
		colSpan: 'Баганаларны берләштерү',
		wordWrap: 'Текстны күчерү',
		hAlign: 'Ятма тигезләү',
		vAlign: 'Асма тигезләү',
		alignBaseline: 'Таяныч сызыгы',
		bgColor: 'Фон төсе',
		borderColor: 'Чик төсе',
		data: 'Мәгълүмат',
		header: 'Башлык',
		yes: 'Әйе',
		no: 'Юк',
		invalidWidth: 'Cell width must be a number.', // MISSING
		invalidHeight: 'Cell height must be a number.', // MISSING
		invalidRowSpan: 'Rows span must be a whole number.', // MISSING
		invalidColSpan: 'Columns span must be a whole number.', // MISSING
		chooseColor: 'Сайлау'
	},
	cellPad: 'Cell padding', // MISSING
	cellSpace: 'Cell spacing', // MISSING
	column: {
		menu: 'Багана',
		insertBefore: 'Сулдан баганалар өстәү',
		insertAfter: 'Уңнан баганалар өстәү',
		deleteColumn: 'Баганаларны бетерү'
	},
	columns: 'Баганалар',
	deleteTable: 'Таблицаны бетерү',
	headers: 'Башлыклар',
	headersBoth: 'Икесе дә',
	headersColumn: 'Беренче багана',
	headersNone: 'Һичбер',
	headersRow: 'Беренче юл',
	invalidBorder: 'Чик киңлеге сан булырга тиеш.',
	invalidCellPadding: 'Cell padding must be a positive number.', // MISSING
	invalidCellSpacing: 'Күзәнәкләр аралары уңай сан булырга тиеш.',
	invalidCols: 'Number of columns must be a number greater than 0.', // MISSING
	invalidHeight: 'Таблица биеклеге сан булырга тиеш.',
	invalidRows: 'Number of rows must be a number greater than 0.', // MISSING
	invalidWidth: 'Таблица киңлеге сан булырга тиеш',
	menu: 'Таблица үзлекләре',
	row: {
		menu: 'Юл',
		insertBefore: 'Өстән юллар өстәү',
		insertAfter: 'Астан юллар өстәү',
		deleteRow: 'Юлларны бетерү'
	},
	rows: 'Юллар',
	summary: 'Йомгаклау',
	title: 'Таблица үзлекләре',
	toolbar: 'Таблица',
	widthPc: 'процент',
	widthPx: 'Нокталар',
	widthUnit: 'киңлек берәмлеге'
} );
