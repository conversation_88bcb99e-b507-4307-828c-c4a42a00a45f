﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'forms', 'af', {
	button: {
		title: '<PERSON>no<PERSON> eienskappe',
		text: '<PERSON><PERSON> (<PERSON>aar<PERSON>)',
		type: 'Soort',
		typeBtn: 'Knop',
		typeSbm: 'Stuur',
		typeRst: 'Maak leeg'
	},
	checkboxAndRadio: {
		checkboxTitle: 'Merkhokkie eienskappe',
		radioTitle: 'Radioknoppie eienskappe',
		value: 'Waarde',
		selected: 'Geselekteer',
		required: 'Required' // MISSING
	},
	form: {
		title: 'Vorm eienskappe',
		menu: 'Vorm eienskappe',
		action: 'A<PERSON>ie',
		method: 'Metode',
		encoding: 'Kodering'
	},
	hidden: {
		title: 'Verborge veld eienskappe',
		name: '<PERSON><PERSON>',
		value: 'Waarde'
	},
	select: {
		title: 'Keuseveld eienskappe',
		selectInfo: 'Info',
		opAvail: 'Beskikbare opsies',
		value: 'Waarde',
		size: '<PERSON><PERSON><PERSON>',
		lines: '<PERSON>yne',
		chkMulti: 'Laat meer as een keuse toe',
		required: 'Required', // MISSING
		opText: 'Teks',
		opValue: 'Waarde',
		btnAdd: 'Byvoeg',
		btnModify: 'Wysig',
		btnUp: 'Op',
		btnDown: 'Af',
		btnSetValue: 'Stel as geselekteerde waarde',
		btnDelete: 'Verwyder'
	},
	textarea: {
		title: 'Teks-area eienskappe',
		cols: 'Kolomme',
		rows: 'Rye'
	},
	textfield: {
		title: 'Teksveld eienskappe',
		name: 'Naam',
		value: 'Waarde',
		charWidth: 'Breedte (karakters)',
		maxChars: 'Maksimum karakters',
		required: 'Required', // MISSING
		type: 'Soort',
		typeText: 'Teks',
		typePass: 'Wagwoord',
		typeEmail: 'Email', // MISSING
		typeSearch: 'Search', // MISSING
		typeTel: 'Telephone Number', // MISSING
		typeUrl: 'URL'
	}
} );
