﻿@model AWAI07IndexViewModel

@{
    Html.RenderAction("_BankMenu", new { NowAction = "MyList" });
}

@Html.HiddenFor(m => m.Search.OrdercColumn)
@Html.HiddenFor(m => m.Search.SyntaxName)
@Html.HiddenFor(m => m.Search.Page)
@Html.HiddenFor(m => m.Search.WhereA_NO)
@Html.HiddenFor(m => m.Search.WhereSCHOOL_NO)
@Html.HiddenFor(m => m.Search.WhereUSER_NO)
@if (Model != null && Model.ListData != null)
{
    <div class="form-inline" role="form" id="Q_Div">
        <div class="form-group">
            <label class="control-label">
                @Html.DisplayNameFor(model => model.Search.WhereSTATUS)
            </label>
        </div>
        <div class="form-group">
            @Html.DropDownListFor(m => m.Search.WhereSTATUS, (List<SelectListItem>)ViewBag.STATUS, new { @class = "form-control input-md" })
        </div>
        <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1)" />
        <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />
    </div>

    <img src="~/Content/img/web-bar-Bank.png" class="img-responsive" alt="Responsive image" />
    <div class="table-responsive">

        <table class="table-ecool table-92Per table-hover table-ecool-reader">
            <thead>
                <tr>
                    <th></th>
                    <th>@Html.DisplayNameFor(m => m.ListData.First().BANK_DATE)</th>
                    <th>@Html.DisplayNameFor(m => m.ListData.First().ACCT_CODE)</th>
                    <th>@Html.DisplayNameFor(m => m.ListData.First().PERIOD_TYPE)</th>
                    <th>@Html.DisplayNameFor(m => m.ListData.First().INTEREST_RATE)</th>
                    <th>@Html.DisplayNameFor(m => m.ListData.First().PERIOD_DATES)</th>
                    <th>@Html.DisplayNameFor(m => m.ListData.First().PERIOD_DATEE)</th>
                    <th>@Html.DisplayNameFor(m => m.ListData.First().AMT)</th>
                    <th>@Html.DisplayNameFor(m => m.ListData.First().STATUS)</th>
                </tr>
            </thead>
            <tbody>
                @foreach (var item in Model?.ListData.OrderByDescending(x=>x.ACCT_CODE))
                {
                    <tr>
                        <td align="center">
                            @if (item.STATUS == AWAT10.StatusVal.NotStarted || item.STATUS == AWAT10.StatusVal.SetUp)
                            {
                                <button class="btn btn-xs btn-Basic" onclick="Edit_show('@item.A_NO')">解約</button>
                            }
                            else
                            {
                                <button class="btn btn-xs btn-Basic" onclick="Edit_show('@item.A_NO')">明細</button>
                            }
                        </td>
                        <td>@(item.BANK_DATE != null ? item.PERIOD_DATES.Value.ToString("yyyy-MM-dd") : "" )</td>
                        <td>@AWAT10.AcctCodeVal.GetDesc(item.ACCT_CODE)</td>
                        <td>@Html.DisplayFor(modelItem => item.PERIOD_DESC)</td>
                        <td>@(item.INTEREST_RATE != null ? item.INTEREST_RATE.Value.ToString("P") : "" )</td>
                        <td>@(item.PERIOD_DATES != null ? item.PERIOD_DATES.Value.ToString("yyyy-MM-dd") : "" )</td>
                        <td>@(item.PERIOD_DATEE != null ? item.PERIOD_DATEE.Value.ToString("yyyy-MM-dd") : "" )</td>
                        <td>
                            @item.AMT.Value.ToString("#")
                        </td>
                        <td>
                            @if (item.STATUS == "8")
                            {

                                @(item.BANK_DATE != null ? item.CHG_DATE.Value.ToString("yyyy-MM-dd") : "")<br />

                            }
                            @if (AWAT10.AcctCodeVal.GetDesc(item.ACCT_CODE) == "本息續存")
                            {

                                @AWAT10.StatusVal.GetDesc(item.STATUS)<br />
                            if (AWAT10.StatusVal.GetDesc(item.STATUS) == "到期已自動轉存")
                            {
                                  
@:(
                            @(item.PERIOD_DATEE != null ? item.PERIOD_DATEE.Value.ToString("yyyy-MM-dd") : "" )<br />
                            @:解約)
                            }
                            }
                            else
                            {



                            if (item.STATUS == AWAT10.StatusVal.NotStarted || item.STATUS == AWAT10.StatusVal.SetUp)
                            { @AWAT10.StatusVal.GetDesc(item.STATUS)}
                            else
                            {

                            if (item.STATUS != "8")
                            {

                            @(item.PERIOD_DATEE != null ? item.CHG_DATE.Value.ToString("yyyy-MM-dd") : "" )<br />}
                            @:解約
                            }


                            }

                        </td>
                    </tr>
                }
            </tbody>
        </table>
    </div>

    <div>
        @Html.Pager(Model.ListData.PageSize, Model.ListData.PageNumber, Model.ListData.TotalItemCount).Options(o => o
                                  .DisplayTemplate(PageGlobal.DfDisplayTemplate)
                                  .MaxNrOfPages(PageGlobal.DfMaxNrOfPages)
                                  .SetPreviousPageText(PageGlobal.DfSetPreviousPageText)
                                  .SetNextPageText(PageGlobal.DfSetNextPageText)
                                  )
    </div>
    if (Model.PieChart != null)
    {
        <div class="row">
            <div class="col-sm-12">
                @Model.PieChart
            </div>
            <div class="col-sm-offset-3 col-sm-6 col-sm-offset-3">
                <h3> 資產總計，酷幣 :<font color="	#0088A8">@( (Model.Time_Deposit + Model.Demand_Deposit).Value.ToString("#,0")) </font>  點</h3>
            </div>
        </div>
    }
}
else
{
    <img src="~/Content/img/web-bar-Bank.png" class="img-responsive" alt="Responsive image" />
    <div class="table-responsive">

        <ht>無資料</ht>
    </div>

}
