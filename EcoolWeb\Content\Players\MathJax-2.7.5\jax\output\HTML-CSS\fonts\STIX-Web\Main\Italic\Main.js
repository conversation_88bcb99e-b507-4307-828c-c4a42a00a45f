/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX-Web/Main/Italic/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXMathJax_Main-italic"]={directory:"Main/Italic",family:"STIXMathJax_Main",style:"italic",testString:"\u00A0\u00A3\u00A5\u00A7\u00A8\u00AF\u00B0\u00B4\u00B5\u00B7\u00F0\u0127\u0131\u0237\u02C6",32:[0,0,250,0,0],33:[667,11,333,39,304],34:[666,-421,420,144,432],35:[676,0,501,2,540],36:[731,89,500,32,497],37:[706,19,755,80,705],38:[666,18,778,76,723],39:[666,-421,214,132,241],40:[669,181,333,42,315],41:[669,180,333,16,289],42:[666,-255,500,128,492],43:[506,0,675,86,590],44:[101,129,250,-5,135],45:[255,-192,333,49,282],46:[100,11,250,27,138],47:[666,18,278,-65,386],48:[676,7,500,32,497],49:[676,0,500,50,409],50:[676,0,500,12,452],51:[676,7,500,16,465],52:[676,0,500,1,479],53:[666,7,500,15,491],54:[686,7,500,30,521],55:[666,8,500,75,537],56:[676,7,500,30,493],57:[676,17,500,23,492],58:[441,11,333,50,261],59:[441,129,333,26,261],60:[516,10,675,84,592],61:[386,-120,675,86,590],62:[516,10,675,84,592],63:[664,12,500,132,472],64:[666,18,920,118,806],65:[668,0,611,-51,564],66:[653,0,611,-8,588],67:[666,18,667,66,689],68:[653,0,722,-8,700],69:[653,0,611,-1,634],70:[653,0,611,8,645],71:[666,18,722,52,722],72:[653,0,722,-8,769],73:[653,0,333,-8,384],74:[653,18,444,-6,491],75:[653,0,667,7,722],76:[653,0,556,-8,559],77:[653,0,833,-18,872],78:[653,15,667,-20,727],79:[666,18,722,60,699],80:[653,0,611,0,605],81:[666,182,722,59,699],82:[653,0,611,-13,588],83:[667,18,500,17,508],84:[653,0,556,59,633],85:[653,18,722,102,765],86:[653,18,611,76,688],87:[653,18,833,71,906],88:[653,0,611,-29,655],89:[653,0,556,78,633],90:[653,0,556,-6,606],91:[663,153,389,21,391],92:[666,18,278,-41,319],93:[663,153,389,12,382],94:[666,-301,422,0,422],95:[-75,125,500,0,500],96:[664,-492,333,120,311],97:[441,11,501,17,476],98:[683,11,500,23,473],99:[441,11,444,30,425],100:[683,13,500,15,527],101:[441,11,444,31,412],102:[678,207,278,-147,424],103:[441,206,500,8,471],104:[683,9,500,19,478],105:[654,11,278,49,264],106:[652,207,278,-124,279],107:[683,11,444,14,461],108:[683,11,278,41,279],109:[441,9,722,12,704],110:[441,9,500,14,474],111:[441,11,500,27,468],112:[441,205,504,-75,472],113:[441,209,500,25,484],114:[441,0,389,45,412],115:[442,13,389,16,366],116:[546,11,278,38,296],117:[441,11,500,42,475],118:[441,18,444,20,426],119:[441,18,667,15,648],120:[441,11,444,-27,447],121:[441,206,444,-24,426],122:[428,81,389,-2,380],123:[687,177,400,51,407],124:[666,18,275,105,171],125:[687,177,400,-7,349],126:[323,-183,541,40,502],160:[0,0,250,0,0],163:[670,8,500,10,517],165:[653,0,500,28,605],167:[666,162,500,53,461],168:[606,-508,333,107,405],175:[583,-532,333,99,411],176:[676,-390,400,101,387],180:[664,-494,333,180,403],181:[428,209,500,-30,497],183:[310,-199,250,70,181],240:[683,11,500,27,482],295:[683,9,500,19,478],305:[441,11,278,47,235],567:[441,207,278,-124,246],710:[661,-492,333,91,385],711:[661,-492,333,121,426],728:[650,-492,333,117,418],729:[606,-508,333,207,305],730:[707,-508,333,155,355],732:[624,-517,333,100,427],913:[668,0,611,-51,564],914:[653,0,611,-8,588],915:[653,0,611,8,645],916:[668,0,611,-32,526],917:[653,0,611,-1,634],918:[653,0,556,-6,606],919:[653,0,722,-8,769],920:[666,18,722,60,699],921:[653,0,333,-8,384],922:[653,0,667,7,722],923:[668,0,611,-51,564],924:[653,0,833,-18,872],925:[653,15,667,-20,727],926:[653,0,651,-6,680],927:[666,18,722,60,699],928:[653,0,722,-8,769],929:[653,0,611,0,605],931:[653,0,620,-6,659],932:[653,0,556,59,633],933:[668,0,556,78,648],934:[653,0,741,50,731],935:[653,0,611,-29,655],936:[667,0,675,77,778],937:[666,0,762,-6,739],945:[441,11,552,27,549],946:[678,205,506,-40,514],947:[435,206,410,19,438],948:[668,11,460,24,460],949:[441,11,444,30,425],950:[683,185,454,30,475],951:[441,205,474,14,442],952:[678,11,480,27,494],953:[441,11,278,49,235],954:[441,13,444,14,465],955:[678,16,458,-12,431],956:[428,205,526,-33,483],957:[441,18,470,20,459],958:[683,185,454,30,446],959:[441,11,500,27,468],960:[428,18,504,19,536],961:[441,205,504,-40,471],962:[441,185,454,30,453],963:[428,11,498,27,531],964:[428,11,410,12,426],965:[441,10,478,19,446],966:[441,205,622,27,590],967:[441,207,457,-108,498],968:[441,205,584,15,668],969:[439,11,686,27,654],976:[694,10,456,45,436],977:[678,10,556,19,526],978:[668,0,596,78,693],981:[683,205,627,27,595],982:[428,11,792,17,832],984:[666,205,722,60,699],985:[441,205,500,27,468],986:[666,207,673,55,665],987:[458,185,444,30,482],988:[653,0,557,8,645],989:[433,190,487,32,472],990:[773,18,645,19,675],991:[683,0,457,31,445],992:[666,207,708,7,668],993:[552,210,528,93,448],1008:[441,13,533,-16,559],1009:[441,205,516,27,484],1012:[666,18,722,60,699],1013:[441,11,444,30,420],1014:[441,11,444,24,414],8211:[243,-197,500,-6,505],8212:[243,-197,889,-6,894],8216:[666,-436,333,171,310],8217:[666,-436,333,151,290],8220:[666,-436,556,166,514],8221:[666,-436,556,151,499],8224:[666,159,500,101,488],8225:[666,143,500,22,491],8254:[820,-770,500,0,500],8260:[676,10,167,-169,337],8407:[760,-548,0,-453,-17],8467:[687,11,579,48,571],8706:[668,11,471,40,471],9416:[676,14,684,0,684]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"STIXMathJax_Main-italic"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Main/Italic/Main.js"]);
