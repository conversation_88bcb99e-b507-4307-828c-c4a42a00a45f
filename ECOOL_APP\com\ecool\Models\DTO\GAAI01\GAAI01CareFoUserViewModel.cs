﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class GAAI01CareFoUserViewModel
    {
        public bool Checked { get; set; }

        /// <summary>
        ///學校代碼
        /// </summary>
        [DisplayName("學校代碼")]
        public string SCHOOL_NO { get; set; }

        /// <summary>
        ///班級代碼
        /// </summary>
        [DisplayName("班級代碼")]
        public string CLASS_NO { get; set; }

        /// <summary>
        ///使用者代碼
        /// </summary>
        [DisplayName("使用者代碼")]
        public string USER_NO { get; set; }

        /// <summary>
        ///姓名
        /// </summary>
        [DisplayName("姓名")]
        public string NAME { get; set; }

        /// <summary>
        ///簡稱
        /// </summary>
        [DisplayName("簡稱")]
        public string SNAME { get; set; }

        /// <summary>
        ///座號
        /// </summary>
        [DisplayName("座號")]
        public string SEAT_NO { get; set; }

        /// <summary>
        /// 未配載次數
        /// </summary>
        public int UN_WEAR_COUNT { get; set; }
    }
}