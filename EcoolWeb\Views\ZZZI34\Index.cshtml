﻿@model ZZZI34IndexViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }

}
<style type="text/css">
    /* set border-box so that percents can be used for width, padding, etc (personal preference) */
    .cycle-slideshow, .cycle-slideshow * {
        -webkit-box-sizing: border-box;
        -moz-box-sizing: border-box;
        box-sizing: border-box;
    }

    .cycle-slideshow {
        width: 100%;
        margin: 10px auto;
        padding: 0;
        position: relative;
        background: url(http://malsup.github.com/images/spinner.gif) 50% 50% no-repeat;
    }

        /* slideshow images (for most of the demos, these are the actual "slides") */
        .cycle-slideshow img {
            /*
    some of these styles will be set by the plugin (by default) but setting them here
    helps avoid flash-of-unstyled-content
    */
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            padding: 0;
            display: block;
        }

            /* in case script does not load */
            .cycle-slideshow img:first-child {
                position: static;
                z-index: 100;
            }
</style>
<script src="~/Scripts/jquery.cycle2.js"></script>

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@{
    Html.RenderAction("_ArtGalleryMenu", new { NowAction = "Index" });
}

<div class="cycle-slideshow">
    <img src="~/Content/img/GalleryBook_A1-2_01.png" class="img-responsive" />
    <img src="~/Content/img/GalleryBook_A1-2_02.png" class="img-responsive" />
</div>
<br />

<div>
    @{string Explain = ViewBag.ZZZI34SEXPLAIN;}
    @Html.Raw(HttpUtility.HtmlDecode(@Explain))
</div>