﻿@model ECOOL_APP.com.ecool.Models.DTO.ZZZI25EditViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;
}

@helper  buttonFun()
{

    <div class="text-center">
        @if (Model.uADDT19.IMG_ID != null)
        {
            @Html.PermissionButton("存檔", "button", (string)ViewBag.PermissionBRE_NO, (string)ViewBag.PermissionAction, new { @class = "btn btn-default", onclick = "Save('" + EcoolWeb.Controllers.ZZZI25Controller.DATA_TYPE.DATA_TYPE_U + "')" }, 1, ViewBag.BtnPermission as List<ECOOL_APP.ControllerPermissionfile>)
            @Html.PermissionButton("刪除", "button", (string)ViewBag.PermissionBRE_NO, (string)ViewBag.PermissionAction, new { @class = "btn btn-default", onclick = "Save('" + EcoolWeb.Controllers.ZZZI25Controller.DATA_TYPE.DATA_TYPE_D + "')" }, 1, ViewBag.BtnPermission as List<ECOOL_APP.ControllerPermissionfile>)
        }
        else
        {
            @Html.PermissionButton("存檔", "button", (string)ViewBag.PermissionBRE_NO, (string)ViewBag.PermissionAction, new { @class = "btn btn-default", onclick = "Save('" + EcoolWeb.Controllers.ZZZI25Controller.DATA_TYPE.DATA_TYPE_A + "')" }, 1, ViewBag.BtnPermission as List<ECOOL_APP.ControllerPermissionfile>)
        }
    </div>
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@if (Model?.IMG_TYPE == ADDT19.IMG_TYPEVal.PremierViewImage)
{
    @Html.PermissionButton("回首播圖片輪播設定列表", "button", (string)ViewBag.PermissionBRE_NO, (string)ViewBag.PermissionAction, new { @class = "btn btn-sm btn-sys", onclick = "Index()" }, 1, ViewBag.BtnPermission as List<ECOOL_APP.ControllerPermissionfile>)
    Html.RenderAction("_Menu", "ADDI12", new { NowAction = ADDI12IndexListViewModel.ActionResultTypeVal.PremierViewImage });
}
else
{
    @Html.PermissionButton("回列表", "button", (string)ViewBag.PermissionBRE_NO, (string)ViewBag.PermissionAction, new { @class = "btn btn-sm btn-sys", onclick = "Index()" }, 1, ViewBag.BtnPermission as List<ECOOL_APP.ControllerPermissionfile>)

}

@using (Html.BeginForm((string)ViewBag.Action, (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", enctype = "multipart/form-data" }))
{

    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })
    <font style="color:red">※輪播六張圖以內，橫幅可以直接超連結，最佳大小500*200</font>
    <div class="panel panel-ZZZ" name="TOP">
        <div class="panel-heading text-center">
            @Html.BarTitle(null, (string)ViewBag.Title)
        </div>
        <div class="panel-body">
            <div class="form-horizontal">
                <div class="form-group">
                    @Html.LabelFor(model => model.uADDT19.IMG_TITLE, htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-9">
                        @Html.EditorFor(model => model.uADDT19.IMG_TITLE, new { htmlAttributes = new { @class = "form-control", @placeholder = "必填" } })
                        @Html.ValidationMessageFor(model => model.uADDT19.IMG_TITLE, "", new { @class = "text-danger" })
                    </div>
                </div>

                <div class="form-group">
                    @Html.LabelFor(model => model.uADDT19.IMG_LINK, htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-9">
                        @Html.EditorFor(model => model.uADDT19.IMG_LINK, new { htmlAttributes = new { @class = "form-control" } })
                        @Html.ValidationMessageFor(model => model.uADDT19.IMG_LINK, "", new { @class = "text-danger" })
                    </div>
                </div>

                <div class="form-group">
                    @Html.LabelFor(model => model.uADDT19.IMG_DATES, htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-9">
                        @Html.EditorFor(model => model.uADDT19.IMG_DATES, new { htmlAttributes = new { @class = "form-control", @readonly = "readonly", @type = "text" } })
                        @Html.ValidationMessageFor(model => model.uADDT19.IMG_DATES, "", new { @class = "text-danger" })
                    </div>
                </div>

                <div class="form-group">
                    @Html.LabelFor(model => model.uADDT19.IMG_DATEE, htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-9">
                        @Html.EditorFor(model => model.uADDT19.IMG_DATEE, new { htmlAttributes = new { @class = "form-control", @readonly = "readonly", @type = "text" } })
                        @Html.ValidationMessageFor(model => model.uADDT19.IMG_DATEE, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group">
                    @Html.LabelFor(model => model.uADDT19.STATUS, htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-9">
                        @Html.DropDownListFor(model => model.uADDT19.STATUS, (IEnumerable<SelectListItem>)ViewBag.STATUS, new { @class = "form-control" })
                    </div>
                </div>

                <div class="form-group">
                    <label class="control-label col-md-3" for="QUESTIONS_TXT">上傳圖片</label>
                    <div class="col-md-9">
                        <input class="btn btn-default" type="file" name="files" placeholder="必填" />
                        @Html.ValidationMessage("files", "", new { @class = "text-danger" })

                        @if (Model.uADDT19.IMG_FILE != null && Model.uADDT19.IMG_FILE != string.Empty)
                        {
                            @Html.HiddenFor(model => model.uADDT19.IMG_FILE)

                            if (ViewBag.ImageUrl != string.Empty && ViewBag.ImageUrl != null)
                            {
                                if (Model.uADDT19.IMG_LINK != string.Empty && Model.uADDT19.IMG_LINK != null)
                                {
                                    <a href="@Model.uADDT19.IMG_LINK" target="_blank" onchange="isValidURL(this)">
                                        <img src="@ViewBag.ImageUrl" class="img-responsive" alt="@Model.uADDT19.IMG_TITLE" />
                                    </a>
                                }
                                else
                                {
                                    <img src="@ViewBag.ImageUrl" class="img-responsive" alt="@Model.uADDT19.IMG_TITLE" />
                                }
                            }
                        }

                        @Html.ValidationMessageFor(model => model.uADDT19.IMG_FILE, "", new { @class = "text-danger" })
                    </div>
                </div>
            </div>
        </div>
        @buttonFun()
    </div>

    @Html.Hidden("DATA_TYPE")
    @Html.HiddenFor(model => model.uADDT19.IMG_ID)
    @Html.HiddenFor(model => model.Search.Page)
    @Html.HiddenFor(model => model.Search.OrderByName)
    @Html.HiddenFor(model => model.Search.Q_IMG_ID)
    @Html.HiddenFor(model => model.Search.SearchContents)
    @Html.HiddenFor(model => model.Search.SyntaxName)
    @Html.HiddenFor(model => model.Search.STATUS)
    @Html.HiddenFor(m => m.IMG_TYPE)
}

@section Scripts {
    <script language="JavaScript">

        $(document).ready(function () {

            $("#uADDT19_IMG_DATES,#uADDT19_IMG_DATEE").datepicker({
                dateFormat: "yy/mm/dd",
                changeMonth: true,
                changeYear: true,
                showOn: "button",
                buttonImage: "../Content/img/icon/calendar.gif",
                buttonImageOnly: true,
            });

            var Today = new Date();

            if ($("#uADDT19_IMG_DATES").val() == "") {
                $("#uADDT19_IMG_DATES").datepicker("setDate", Today);
            }

            if ($("#uADDT19_IMG_DATEE").val() == "") {
                Today.setMonth(Today.getMonth() + 1);
                $("#uADDT19_IMG_DATEE").datepicker('setDate', Today);
            }

        });
       
        function Index() {
            form1.action = '@Html.Raw(@Url.Action("Index", (string)ViewBag.BRE_NO))'
            form1.submit();
        }

        function DelFile(IMG_ID, IMG_FILE) {
            form1.action = '@Html.Raw(@Url.Action("DelFile", (string)ViewBag.BRE_NO))' + '?IMG_ID=' + IMG_ID + '&IMG_FILE=' + IMG_FILE
            form1.submit();
        }

        function Save(Val) {
            form1.DATA_TYPE.value = Val
            form1.submit();
        }
    </script>
}