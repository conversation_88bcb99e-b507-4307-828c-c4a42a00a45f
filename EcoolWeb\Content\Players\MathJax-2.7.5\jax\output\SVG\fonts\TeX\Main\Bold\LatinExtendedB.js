/*
 *  /MathJax/jax/output/SVG/fonts/TeX/Main/Bold/LatinExtendedB.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax.SVG.FONTDATA.FONTS["MathJax_Main-bold"],{567:[451,201,439,-12,420,"297 360T297 373T294 392T288 400T278 401H276Q237 398 200 363Q181 343 170 325T156 299T149 287T129 282H123H116Q102 282 97 284T92 298Q93 303 98 315T118 349T151 390T201 427T267 451H279Q357 451 388 422T420 354V339L370 138Q321 -60 317 -69Q287 -157 163 -194Q133 -201 99 -201Q39 -201 14 -178T-12 -125Q-12 -94 11 -69T68 -43Q93 -43 108 -57T123 -95Q123 -121 100 -151H104Q131 -151 155 -125T193 -60Q195 -54 244 141T294 345Q297 360 297 373"]});MathJax.Ajax.loadComplete(MathJax.OutputJax.SVG.fontDir+"/Main/Bold/LatinExtendedB.js");
