﻿@model ADDI12EditViewModel

@{
    ViewBag.Title = ViewBag.Panel_Title;
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);

    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
    int row = 0;
}
<link href="~/Content/datatables.min.css" rel="stylesheet" />

<link href="~/Content/colorbox/example2/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")
@if (AppMode)
{
    <a role="button" href='@Url.Action("Index",(string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys  @(ViewBag.NowAction=="Index" ? "active":"")">
        小小舞臺說明
    </a>
    <a role="button" href='@Url.Action("Edit",(string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys @(ViewBag.NowAction=="Edit" ? "active":"")">
        我要申請小小舞臺
    </a>
    if (user?.USER_NO == "stage")
    {
        <a role="button" href='@Url.Action("Edit1",(string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys @(ViewBag.NowAction=="Edit" ? "active":"")">
            我要申請小小舞臺(小小舞台幫手)
        </a>

    }
    <a role="button" href='@Url.Action("PremierView",(string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys @(ViewBag.NowAction==ADDI12IndexListViewModel.ActionResultTypeVal.PremierView ? "active":"")">
        首播影片 <span class="badge">@ViewBag.PremierCount</span>
    </a>
    <a role="button" href='@Url.Action("AllVideoView",(string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys @(ViewBag.NowAction==ADDI12IndexListViewModel.ActionResultTypeVal.AllVideoView ? "active":"")">
        一般影片 <span class="badge">@ViewBag.AllVideoCount</span>
    </a>
    if (user != null)
    {

        if ((user.USER_TYPE == UserType.Teacher || user.USER_TYPE == UserType.Admin))
        {
            <a role="button" href='@Url.Action("MyUploadVideoView",(string)ViewBag.BRE_NO)' class="btn btn-sm btn-sys @(ViewBag.NowAction==ADDI12IndexListViewModel.ActionResultTypeVal.MyUploadVideoView ? "active":"")">
                我上傳的影片
            </a>
        }
    }
}

@{
    Html.RenderAction("_Menu", new { NowAction = "Edit1" });
}

@using (Html.BeginForm("Edit1", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "formEdit", id = "formEdit", enctype = "multipart/form-data" }))
{

    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })
    @Html.HiddenFor(model => model.Main.STAGE_ID)

    @Html.HiddenFor(m => m.Search.WhereSCHOOL_NO)
    @Html.HiddenFor(m => m.Search.SyntaxName)
    @Html.HiddenFor(m => m.Search.Page)
    @Html.HiddenFor(m => m.Search.OrdercColumn)
    @Html.HiddenFor(m => m.Search.WhereSearch)
    @Html.HiddenFor(m => m.Search.WhereSTAGE_ID)

    @Html.HiddenFor(m => m.ActionResultType)

    <div class="panel panel-ZZZ" name="TOP">
        <div class="panel-heading text-center">
            小小舞臺-編輯
        </div>
        <div class="panel-body">
            <div class="form-horizontal">
                <div class="form-group">
                    @Html.Label("學校", htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-9">
                        @Html.DropDownListFor(model => model.Main.SCHOOL_NO, (IEnumerable<SelectListItem>)ViewBag.SchoolNoSelectItem, new { @class = "form-control", onchange = "form1.submit();" })
                        @Html.ValidationMessageFor(model => model.Main.SCHOOL_NO, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group">
                    @Html.LabelFor(model => model.Main.STAGE_NAME, htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-9">
                        @Html.EditorFor(model => model.Main.STAGE_NAME, new { htmlAttributes = new { @class = "form-control", @placeholder = "此欄位限制40個字，最佳字數為20個字" } })
                        @Html.ValidationMessageFor(model => model.Main.STAGE_NAME, "", new { @class = "text-danger" })
                    </div>
                </div>

                <div class="form-group">
                    @Html.LabelFor(model => model.Main.YOUTUBE_IMG, htmlAttributes: new { @class = "control-label col-md-3" })
                    @Html.HiddenFor(m => m.Main.YOUTUBE_IMG)
                    <div class="col-md-9">
                        @if (Model.Main != null)
                        {
                            if (!string.IsNullOrWhiteSpace(Model.Main.YOUTUBE_IMG_Path))
                            {
                                <img src="@Model.Main.YOUTUBE_IMG_Path" class="img-responsive colorbox" alt="Responsive image" href="@Model.Main.YOUTUBE_IMG_Path" style="max-width:300px" />
                                <br />
                            }
                        }
                        <div>
                            @Html.TextBoxFor(m => m.Main.UploadYoutubeFile, new { @class = "form-control input-md", @type = "file" })
                            @Html.ValidationMessageFor(m => m.Main.UploadYoutubeFile, "", new { @class = "text-danger" })
                            <br />
                            <label class="text-info">PS.請上傳 jpg、jpeg、png、gif、bmp 等格式的圖片</label>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    @Html.LabelFor(model => model.Main.YOUTUBE_URL, htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-9">

                        <div class="input-group">
                            @Html.EditorFor(model => model.Main.YOUTUBE_URL, new { htmlAttributes = new { @class = "form-control", @placeholder = Html.DisplayNameFor(n => n.Main.YOUTUBE_URL) } })
                            <span class="input-group-btn">
                                <button class="btn btn-info" type="button" id="CheckYoutube">檢查網址</button>
                            </span>
                        </div><!-- /input-group -->

                        @Html.ValidationMessageFor(model => model.Main.YOUTUBE_URL, "", new { @class = "text-danger" })
                    </div>
                    <br />
                    <label class="text-info">PS.請輸入 有效Youtube 網址 ，https://www.youtube.com/watch?v=影片id，影片權限請公開 。</label>
                </div>

                @if (ViewBag.Live == SharedGlobal.Y)
                {
                    <div class="form-group">
                        @Html.LabelFor(model => model.Main.IS_PREMIER, htmlAttributes: new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            <label class='checkbox-inline'>
                                @Html.CheckBoxFor(m => m.Main.IS_PREMIER)
                                <label>是</label>
                            </label>
                            @Html.ValidationMessageFor(model => model.Main.IS_PREMIER, "", new { @class = "text-danger" })

                            <br />
                            <label class="text-info">PS.當勾選需填寫首播開始日期及結束日期</label>
                        </div>
                    </div>
                }

                <div class="form-group">
                    @Html.LabelFor(m => m.Main.STAGE_DATES, htmlAttributes: new { @class = "col-md-3 control-label" })
                    <div class="col-md-9">
                        @Html.EditorFor(m => m.Main.STAGE_DATES, new { htmlAttributes = new { @class = "form-control input-md", @type = "text", @placeholder = "格式yyyy/MM/dd" } })
                        @Html.ValidationMessageFor(m => m.Main.STAGE_DATES, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group">
                    @Html.LabelFor(m => m.Main.STAGE_DATEE, htmlAttributes: new { @class = "col-md-3 control-label" })
                    <div class="col-md-9">
                        @Html.EditorFor(m => m.Main.STAGE_DATEE, new { htmlAttributes = new { @class = "form-control input-md", @type = "text", @placeholder = "格式yyyy/MM/dd" } })
                        @Html.ValidationMessageFor(m => m.Main.STAGE_DATEE, "", new { @class = "text-danger" })
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="DetailsView">
        <div style="margin-top:20px;margin-bottom:30px;text-align:center">
            <span class="glyphicon glyphicon-circle-arrow-down fa-2x" aria-hidden="true" style="color:#004da0"></span>
        </div>
        @Html.ValidationMessage("DetailsError", new { @class = "text-danger" })
        <div class="panel panel-ZZZ">
            <div class="panel-heading text-center">
                隊伍人員
            </div>
            <div class="panel-body">
                <div class="table-responsive">
                    <div class="css-table" style="width:100%;">

                        <div id="editorRows" class="tbody">
                            <table id="teamTable" class="table table-hover table-responsive" style="width:100%; border: none;">
                                <thead>
                                    <tr>
                                        <th>刪除</th>
                                        <th>學校</th>
                                        <th>全名</th>
                                        <th>學號</th>
                                        <th>班級</th>
                                        <th>座號</th>
                                    </tr>
                                </thead>
                                <tbody id="tbRow">
                                    @if (Model.Details?.Count() > 0)
                                    {
                                        foreach (var Item in Model.Details)
                                        {
                                            <tr id="Tr@(row)">
                                                <td>
                                                    @Html.Hidden($"Details.Index", row)

                                                    @Html.Hidden($"Details[{row}].STAGE_PERSON_ITEM", Item.STAGE_PERSON_ITEM)
                                                    @Html.Hidden($"Details[{row}].SCHOOL_NO", Item.SCHOOL_NO)
                                                    @Html.Hidden($"Details[{row}].SHORT_NAME", Item.SHORT_NAME)
                                                    @Html.Hidden($"Details[{row}].USER_NO", Item.USER_NO)
                                                    @Html.Hidden($"Details[{row}].NAME", Item.NAME)
                                                    @Html.Hidden($"Details[{row}].GRADE", Item.GRADE)
                                                    @Html.Hidden($"Details[{row}].CLASS_NO", Item.CLASS_NO)
                                                    @Html.Hidden($"Details[{row}].SEAT_NO", Item.SEAT_NO)

                                                    <a role='button' style="cursor:pointer;" onclick="deleteRow('Tr@(row++)')"> <i class='glyphicon glyphicon-remove'></i></a>
                                                </td>
                                                <td>@Item.SHORT_NAME</td>
                                                <td>@Item.NAME</td>
                                                <td>@Item.USER_NO</td>
                                                <td>@Item.CLASS_NO</td>
                                                <td>@Item.SEAT_NO</td>
                                            </tr>
                                        }
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="panel-footer">
                <div class="row">
                    <div class="col-md-12 col-xs-12 text-right">
                        <span class="input-group-btn">
                            <button class="btn btn-info btn-sm" type="button" onclick="onAddItem()">
                                增加人員
                            </button>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="form-group">
        <div class="col-xs-12 text-center">
            <div class="form-inline">
                <input type="button" value="存檔" class="btn btn-default" onclick="Save()" />

                @if (!string.IsNullOrWhiteSpace(Model.Main?.STAGE_ID))
                {
                    <input type="button" value="作廢" class="btn btn-default" onclick="Del()" />
                }

                <button class="btn btn-default" type="button" onclick="onBack()">放棄</button>
            </div>
        </div>
    </div>

    <div class="modal fade bs-example-modal-lg" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel" id="myModal">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title">請選擇要加入隊伍的人員</h4>
                </div>
                <div class="modal-body" style="overflow-y:scroll;max-height: calc(40vh)">
                    <div id="OpenPersonView">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal" id="ModalClose">Close</button>
                </div>
            </div>
        </div>
    </div>

}

<div style="width:100%;height:100%;display:none;z-index:999;position:absolute" id="loading">
    <div style="margin: 0px auto;text-align:center">
        <img src="~/Content/img/<EMAIL>" style="max-height:350px;" />
        <br />
        <h3>處理中…</h3>
    </div>
</div>

<div class="row">
    <div class="col-lg-6 col-lg-offset-3">
        <div class="use-absolute" id="ErrorDiv">
            <div class="use-absoluteDiv">
                <div class="alert alert-danger" role="alert">
                    <h1>
                        <i class="fa fa-exclamation-circle"></i>
                        <strong id="ErrorStr"></strong>
                    </h1>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="~/Scripts/datatables.min.js"></script>
    <script language="JavaScript">
        var teamtable;

        teamtable = $('#teamTable').DataTable({
            searching: false,
            lengthChange: false,
            info: false,
            paging: false,
        });

        var targetFormID = '#formEdit';

        $(".colorbox").colorbox({ iframe: true, width: "90%", height: "90%", opacity: 0.82 });

        $("#@Html.IdFor(m=>m.Main.STAGE_DATES),#@Html.IdFor(m=>m.Main.STAGE_DATEE)").datepicker({
            dateFormat: "yy/mm/dd",
            changeMonth: true,
            changeYear: true,
            showOn: "button",
            buttonImage: "../Content/img/icon/calendar.gif",
            buttonImageOnly: true,
        });

         $(function () {
             $('#CheckYoutube').click(function () {

                      $.ajax({
                        url: "@(Url.Action("GetUrlArgument", (string)ViewBag.BRE_NO))",     // url位置
                        type: 'post',                   // post/get
                        data: {
                            StrUrl: $('#@Html.IdFor(model => model.Main.YOUTUBE_URL)').val(),
                        },     // data
                        dataType: 'json',               // xml/json/script/html
                        cache: false,                   // 是否允許快取
                        success: function (data) {
                            var res = jQuery.parseJSON(data);

                            if (res.Success == 0) { //失敗
                                $('#@Html.IdFor(model => model.Main.YOUTUBE_URL)').val('')
                                alert(res.Error);
                            }
                            else if (res.Success == 1) //成功
                            {
                               alert('正確');
                               $('#@Html.IdFor(model => model.Main.YOUTUBE_URL)').val(res.EmbedYouTubeUrl)
                            }
                        },
                        error: function (xhr, err) {
                            alert("readyState: " + xhr.readyState + "\nstatus: " + xhr.status);
                            alert("responseText: " + xhr.responseText);
                        }
                    });

            });
        });

        function Save()
        {
            $(targetFormID).attr("action", "@Url.Action("EditSave", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        function Del()
        {
            $(targetFormID).attr("action", "@Url.Action("DelSave", (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

        function onBack() {

            $(targetFormID).attr("action", "@Url.Action(Model.ActionResultType, (string)ViewBag.BRE_NO)")
            $(targetFormID).submit();
        }

       //增加明細
        function onAddItem() {

             $.ajax({
                url: '@Url.Action("_OpenPersonStageView", (string)ViewBag.BRE_NO)',
                data: $(targetFormID).serialize(),
                type: 'post',
                async: false,
                cache: false,
                dataType: 'html',
                success: function (data) {
                    $('#OpenPersonView').html(data);
                }
            });

             if ($('#myModal').is(':visible') == false) {
                 $('#myModal').modal('show');
             }
        }

        function AllAddRow() {

            $('#ErrorStr').html('處理中')
            $('#ErrorDiv').show()
            $('#AllAddRowBtn').prop("disabled", true);

            var ArrId = new Array();
            var Num = 0;

            $(".AddRowBtn").each(function (index) {
                ArrId[Num] = $(this).attr('id');
                Num = Num + 1;
            });

            ArrId.sort()

            $.each(ArrId, function (i, key) {

                setTimeout(function () {
                    $('#' + key).click();
                }, 0);

            });

            IsAddRowBtnHide()
        }

        function IsAddRowBtnHide() {

            setTimeout(function () {

                if ($(".AddRowBtn").length == 0) {
                    $('#ErrorDiv').hide()
                    $('#AllAddRowBtn').prop("disabled", false);
                }
                else {
                    IsAddRowBtnHide()
                }
            }, 2000);
        }

        //del 項目
        function deleteRow(Id) {
            $('#' + Id).remove();
        }

        var row = parseInt('@(row)');
        function AddRow(SCHOOL_NO, USER_NO) {

            var result;

            var data = {
                "SCHOOL_NO": SCHOOL_NO,
                "USER_NO": USER_NO
             };

            $.ajax({
                url: '@Url.Action("_OpenPersonAddData", (string)ViewBag.BRE_NO)',
                data: data,
                async: false, //啟用同步請求
                cache: false,
                error: function (xhr) {
                    result = true;
                },
                success: function (data) {

                    var tr =
                        $("<tr>").append(`
                                 <td>
                                    <input type="hidden" name="Details.Index" value="` + row +`" />
                                    <input type="hidden" name="Details[` + row + `].SHORT_NAME" value="` + data.SHORT_NAME + `">
                                    <input type="hidden" name="Details[` + row +`].SCHOOL_NO" value="` + data.SCHOOL_NO + `">
                                    <input type="hidden" name="Details[`+ row +`].USER_NO" value="` + data.USER_NO + `">
                                    <input type="hidden" name="Details[`+ row + `].CLASS_NO" value="` + data.CLASS_NO + `">
                                    <input type="hidden" name="Details[`+ row + `].SEAT_NO" value="` + data.SEAT_NO + `">
                                    <input type="hidden" name="Details[`+ row + `].NAME" value="` + data.NAME + `">

                                     <a role='button' style="cursor:pointer;"
                                        onclick="teamtable.row($(this).parent().parent()).remove().draw();
                                                    $(this).parent().parent().remove();">
                                        <i class='glyphicon glyphicon-remove'></i>
                                    </a>
                                 </td>
                        `)
                            .append("<td>" + data.SHORT_NAME + "</td>")
                            .append("<td>" + data.NAME + "</td>")
                            .append("<td>" + data.USER_NO + "</td>")
                            .append("<td>" + data.CLASS_NO + "</td>")
                            .append("<td>" + data.SEAT_NO + "</td>");

                    result = true;
                    deleteRow('Tr' + USER_NO);

                    teamtable.row.add(tr).draw();
                    row++;

                    result = true;
                }
            });

            return result;
        }
    </script>
}