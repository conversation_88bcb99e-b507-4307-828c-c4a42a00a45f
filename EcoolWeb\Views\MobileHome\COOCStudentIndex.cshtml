﻿@model ECOOL_APP.com.ecool.Models.DTO.SECI01IndexViewModel
@using ECOOL_APP.com.ecool.util
@{
    ViewBag.Title = "e酷幣學生首頁";
    Layout = "~/Views/Shared/_LayoutWebView.cshtml";

    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();

    int len = 15;

    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode)
    {
        len = 10;
    }
}
<style>
    .modal-body {
        position: relative;
        padding: 10px;
    }
</style>
@Html.Partial("_Notice")

@Html.Action("_ImgPlay", "ZZZI25")
@*<div class="modal fade bs-example-modal-lg" tabindex="-1" role="dialog" aria-labelledby="REMINDModalLabel" id="remind-modal">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">

                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title">酷幣主機停機(暫停服務)公告：</h4>
                </div>
                <div class="modal-body" id="remind-content">
                    <ol>
                        配合市政府機電檢修停電，酷幣主機預計計於109年1月17日(星期五)下午7時至109年1月19日(星期日)上午9時止暫停服務，不便之處，請包涵。
                    </ol>
                </div>
            </div>
        </div>
    </div>*@

@if (ViewBag.GetDataListShowYN == true || ViewBag.GetALLDataListShowYN == true)
{
    if (ViewBag.RemindItems != null || ViewBag.ALLRemindItems != null)
    {
        <div class="modal fade bs-example-modal-lg" tabindex="-1" role="dialog" aria-labelledby="REMINDModalLabel" id="remind-modal">

            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    @if (ViewBag.GetALLDataListShowYN == true)
                    {
                        foreach (var item in ViewBag.ALLRemindItems as List<BDMT02_REF>)
                        {
                            var str = "";
                            str = "https://line.me/R/ti/p/@101hrbss";
                            string str2 = "";
                            str2 = "https://www.youtube.com/watch?v=3m3RLukLqSU";
                            if (item.ITEM_NO == "001")
                            {
                                <div class="modal-header">
                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                                    <h4 class="modal-title">
                                        @item.CONTENT_TXT
                                        <br />


                                    </h4>
                                </div>
                            }
                            else
                            {
                                <div class="modal-body" id="remind-content">


                                    @if (item.SCHOOL_NO == "ALL" && item.ITEM_NO == "003" && item.CONTENT_TXT == "【觀看說明影片】")
                                    {

                                    }
                                    else
                                    {
                                        @item.CONTENT_TXT

                                    }


                                    <br />
                                    @if (item.SCHOOL_NO == "ALL" && item.ITEM_NO == "003")
                                    {



                                        <a href="@str2">【觀看說明影片】</a><br />

                                        <br />

                                        @:客服連結<a href="@str">@str</a>
                                    }
                                </div>
                            }
                        }
                    }
                    @if (ViewBag.GetDataListShowYN == true)
                    {
                        foreach (var item in ViewBag.RemindItems as List<BDMT02_REF>)
                        {
                            var str = "";
                            str = "https://line.me/R/ti/p/@101hrbss";
                            string str2 = "";
                            str2 = "https://www.youtube.com/watch?v=3m3RLukLqSU";
                            if (item.ITEM_NO == "001")
                            {
                                <div class="modal-header">
                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                                    <h4 class="modal-title">
                                        @item.CONTENT_TXT
                                        <br />

                                    </h4>
                                </div>
                            }
                            else
                            {
                                <div class="modal-body" id="remind-content">


                                    @if (item.SCHOOL_NO == "ALL" && item.ITEM_NO == "003" && item.CONTENT_TXT == "【觀看說明影片】")
                                    {

                                    }
                                    else
                                    {
                                        @item.CONTENT_TXT

                                    }


                                    <br />
                                    @if (item.SCHOOL_NO == "ALL" && item.ITEM_NO == "003")
                                    {

                                        <a href="@str2">【觀看說明影片】</a><br /><br />

                                        <br />


                                        @:客服連結<a href="@str">@str</a>
                                    }
                                </div>
                            }

                        }
                    }
                </div>
            </div>
        </div>
    }

}
@*<div id='PrintDiv' style="width:95%;margin: 0px auto; ">
        @Html.Action("_PersonalDiv", "SECI01", new { wSCHOOL_NO = Model.SCHOOL_NO, wUSER_NO = Model.USER_NO, wCLASS_NO = Model.CLASS_NO, wDATA_ANGLE_TYPE = Model.DATA_ANGLE_TYPE, wREF_BRE_NO = ViewBag.BRE_NO, wPRINT = Model.PRINT })
    </div>*@
<style>
    .Div-EZ-ZZZI04 {
        width: 100%;
    }

    .Div-EZ-ZZZI04-Bar {
        width: 100%;
    }
</style>

<div id="PersonalDivContent">
    <div class="row">
        @if (user != null)
        {
            if (user.USER_TYPE == UserType.Parents)
            {
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 font_Cash" style="white-space:nowrap;text-align:center">
                    @user.SNAME  的家長您好，@user.SNAME  的酷幣點數：@Model.CASH_AVAILABLE
                </div>
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 font_Cash" style="white-space:nowrap;text-align:center">  <a id="UserIndex" href='@Url.Action("CooCIndex", "User")'><img src="~/Content/img/Tr_box_200w_b.png" style="max-height:40px" class="imgEZ" /></a></div>
            }
            else
            {
                if (Model != null)
                {
                    <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 font_Cash" style="white-space:nowrap;text-align:center">
                        <img src="~/Content/img/web-revise-secretary-08new.png" style="max-height:40px" class="imgEZ" />
                        目前酷幣點數：@(Model.CASH_AVAILABLE ?? 0)
                    </div>

                    <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 font_Cash" style="white-space:nowrap;text-align:center">
                        <img src="~/Content/img/web-revise-secretary-08new.png" style="max-height:40px" class="imgEZ" />
                        目前定存：@(Model.CASH_DEPOSIT ?? 0)
                    </div>
                    <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 font_Cash" style="white-space:nowrap;text-align:center">   <a id="UserIndex" href='@Url.Action("CooCIndex", "User")'><img src="~/Content/img/Tr_box_200w_b.png" style="max-height:40px" class="imgEZ" /></a></div>
                }
            }
            if (user.USER_TYPE == ECOOL_APP.EF.UserType.Student || user.USER_TYPE == ECOOL_APP.EF.UserType.Parents)
            {
                if (Model.BOOKS >= 0)
                {
                    <div class="col-lg-4 col-md-4  col-sm-6 col-xs-12 font_Cash" style="text-align:center">
                        <img src="~/Content/img/books.png" style="max-width:40px;" class="imgEZ" />
                        <a id="BorrowIndexSesem" href='@Url.Action("BorrowIndex", (string)ViewBag.BRE_NO)'> <text>本學期借閱本數：</text>@Model.BOOKS </a>
                    </div>
                }
                if (Model.BOOKS_MONTH >= 0)
                {
                    <div class="col-lg-4 col-md-4  col-sm-6 col-xs-12 font_Cash" style="text-align:center">
                        <img src="~/Content/img/books.png" style="max-width:40px;" class="imgEZ" />
                        <text>本月份借閱本數：</text>@Model.BOOKS_MONTH
                    </div>
                }
                if (user.USER_TYPE == ECOOL_APP.EF.UserType.Student && user.Chance_ARRIVED_CASH > 0)
                {
                    <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 font_Cash" style="text-align:center">
                        <img src="~/Content/img/monkey-01.png" style="max-width:40px;max-height:40px;" class="imgEZ" />
                        <a href='@Url.Action("ArrivedChance2", "Home")'>
                            <text>好運次數：</text>

                            @user.Chance_ARRIVED_CASH
                        </a>
                    </div>
                }

            }

        }
    </div>

    <div class="row">
        <div class="col-sm-2 col-xs-12 text-center">
            <div>

                <img src='@Url.Content(Model.PlayerUrl)' style="max-height:240px" class="imgEZ" />
            </div>
        </div>
        <div class="col-sm-10 col-xs-12">
            <div>
                @if (Model.wREF_BRE_NO == EcoolWeb.Controllers.SECI01Controller.REF_BRE_NO_VAL.C_SECI01)
                {
                    <div style="height:25px"></div>
                    <button type="button" class="btn btn-sm btn-bold btn-pink" onclick="printScreenH()">我要列印</button>
                    <button type="button" class="btn btn-sm btn-bold btn-pink" title="點數分析" id="PieChartbtn" href="@Url.Action("_PieChartDiv", (string)ViewBag.BRE_NO)">點數分析</button>
                    <button type="button" class="btn btn-sm btn-bold btn-pink" title="加值應用統計" id="Statisticalbtn" href="@Url.Action("_StatisticalDiv", (string)ViewBag.BRE_NO)">加值應用統計</button>

                    if (Model.wIsQhisSchool)
                    {
                        <button type="button" class="btn btn-sm btn-bold btn-pink" title="學習成果匯出(過去學校)" id="ExportResult" onclick="ExportResultWinOpen()">學習成果匯出(過去學校)</button>
                    }

                    if (Model.DATA_ANGLE_TYPE != EcoolWeb.Models.UserProfileHelper.AngleVal.OneData)
                    {
                        <button type="button" class="btn btn-sm btn-bold btn-pink" title="學習成果匯出" id="ExportResult" onclick="ExportResultWinOpen()">學習成果匯出</button>
                    }
                    else
                    {
                        <button type="button" class="btn btn-sm btn-bold btn-pink" title="我的線上投稿" onclick="funBookW()">我的線上投稿</button>

                        @*<button type="button" class="btn btn-sm btn-bold btn-pink" title="我的閱讀認證" id="MyAPPLY" href="@Url.Action("BOOK_APPLY", (string)ViewBag.BRE_NO)">我的閱讀認證</button>*@
                        <a role="button" href='@Url.Action("BOOK_APPLY", (string)ViewBag.BRE_NO)' class="btn btn-sm btn-bold btn-pink">我的閱讀認證</a>
                        <div style="display:none">
                            @{
                                int No = 1;
                                string IdName = string.Empty;

                                <div class="arrWRITING_NO">
                                    @foreach (var item in Model.arrWRITING_NO)
                                    {
                                        IdName = "W" + No.ToString();

                                        <a id="@IdName" class="groupWRITING_NO" href='@Url.Action("BOOK_Details", "ADDI01" , new {  WRITING_NO = item})'>
                                            @item
                                        </a>
                                        No++;
                                    }
                                </div>

                                No = 1;

                                <div class="arrAPPLY_NO">
                                    @foreach (var item in Model.arrAPPLY_NO)
                                    {
                                        IdName = "A" + No.ToString();

                                        <a id="@IdName" class="groupAPPLY_NO" href='@Url.Action("BOOK_Details", "ADDI01" , new {  WRITING_NO = item})'>
                                            @item
                                        </a>

                                        No++;
                                    }
                                </div>

                            }
                        </div>
                    }
                }
            </div>

            @if (user != null)
            {
                if (user.USER_TYPE != UserType.Teacher)
                {
                    <div class="row">
                        <div class="col-xs-12 font_Last_Day_MEMO">
                            <img src="~/Content/img/web-revise-secretary-07.png" class="imgEZ" />
                            @Html.Raw(Model.Last_Day_MEMO)
                        </div>
                    </div>
                }
            }

            <div style="height:25px"></div>

            <div class="row">
                <div class="col-sm-4">
                    <table class="table-ecool table-ecool-Bule-SEC">
                        <thead>
                            <tr>
                                <th colspan="2">
                                    線上投稿 @Model.DATA_TYPE_NAME
                                </th>
                            </tr>
                            <tr>
                                <th>
                                    投稿數
                                </th>
                                <th>
                                    推薦數
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="text-center">
                                <td title="投稿數" style="vertical-align:middle;">@Model.WritingCount</td>
                                <td title="推薦數" style="vertical-align:middle;">@Model.WritingShareCount</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="col-sm-4">
                    <table class="table-ecool table-ecool-Bule-SEC">
                        <thead>
                            <tr>
                                <th colspan="2">
                                    閱讀認證 @Model.DATA_TYPE_NAME
                                </th>
                            </tr>
                            <tr>
                                <th>
                                    閱單數
                                </th>
                                <th>
                                    推薦數
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="text-center">
                                <td title="閱單數" style="vertical-align:middle;">@Model.BookCount</td>
                                <td title="推薦數" style="vertical-align:middle;">@Model.BookShareCount</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="col-sm-4">
                    <table class="table-ecool table-ecool-Bule-SEC">
                        <thead>
                            <tr>
                                <th colspan="2">
                                    校內表現/校外榮譽 @Model.DATA_TYPE_NAME
                                </th>
                            </tr>
                            <tr>
                                <th>
                                    校內筆數
                                </th>
                                <th>
                                    校外筆數
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="text-center">
                                <td title="閱讀等級" style="vertical-align:middle;">@Model.SchoolInCount</td>
                                <td title="護照等級" style="vertical-align:middle;">@Model.SchoolObcCount</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    @if (Model.DATA_ANGLE_TYPE == EcoolWeb.Models.UserProfileHelper.AngleVal.OneData)
    {

        int Num = 0;
        <br />
        <div style="height:25px"></div>
        @*以下是閱讀等級徽章*@
        <div class="row">
            <div class="col-sm-2 col-xs-12 text-center">
                <img src="~/Content/img/web-revise-secretary-09.png" class="imgEZ" />
            </div>
            <div class="col-sm-10 col-xs-12 @(AppMode==true? "text-center" :"")">
                @if (Model.ReadImgURL != null)
                {

                    foreach (var ReadImg in Model.ReadImgURL as Dictionary<byte, string>)
                    {
                        Num = Num + 1;
                        <img alt="@ReadImg.Key" src="@Url.Content(ReadImg.Value)" class="imgEz_Badge" />

                        if (AppMode == true && Num == 5)
                        {
                            <br />
                        }

                    }
                }
            </div>
        </div>
        <br />
        <div style="height:25px"></div>

        @*以下是認證等級徽章*@
        <div class="row">
            <div class="col-sm-2 col-xs-12 text-center">
                <img src="~/Content/img/web-revise-secretary-11.png" class="imgEZ" />
            </div>

            <div class="col-sm-10 col-xs-12 @(AppMode==true? "text-center" :"")">
                @if (Model.ReadImgURL != null)
                {
                    foreach (var PassportImg in Model.PassportImgURL as Dictionary<byte, string>)
                    {
                        <img alt="@PassportImg.Key" src="@Url.Content(PassportImg.Value)" class="imgEz_BadgeBook" />
                    }
                }
            </div>
        </div>
    }

    <div style="height:25px"></div>

    <div>
        @Html.Partial("_BET02Partial")
    </div>

    @if (Model.wREF_BRE_NO == EcoolWeb.Controllers.SECI01Controller.REF_BRE_NO_VAL.C_SECI01
            || Model.wREF_BRE_NO == EcoolWeb.Controllers.SECI01Controller.REF_BRE_NO_VAL.C_MobileHome
            )
    {
        <div style="height:25px"></div>
        <div>
            <div class="row">
                <div class="col-sm-6">
                    <table class="table-ecool table-hover table-ecool-pink-SEC">
                        <thead>
                            <tr class="text-center">
                                <th colspan="3">
                                    線上投稿 @Model.DATA_TYPE_NAME
                                </th>
                            </tr>
                            <tr class="text-center">
                                <th>
                                    日期
                                </th>
                                <th>
                                    標題
                                </th>
                                <th>
                                    狀態
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            @if (Model.ADDT01List.Count > 0)
                            {
                                foreach (var aADDT01 in Model.ADDT01List)
                                {
                                    <tr onclick="onBtnLinkADDT01('@aADDT01.WRITING_NO','@aADDT01.USER_NO','@Model.DATA_ANGLE_TYPE')" style="cursor:pointer">
                                        <td title="線上投稿.日期" class="text-center">@Convert.ToDateTime(aADDT01.CRE_DATE).ToString("yyyy/MM/dd")</td>
                                        <td title="線上投稿.標題">
                                            @StringHelper.LeftStringR(aADDT01.SUBJECT, len)

                                            @if (aADDT01.SHARE_YN == "Y")
                                            {
                                                <img src="~/Content/img/icons-like-05.png" />
                                            }

                                            @if (aADDT01.WRITING_STATUS == (byte)ADDStatus.eADDT01Status.Verified && aADDT01.CASH.HasValue)
                                            {
                                                <span style="color:red">+@aADDT01.CASH</span>
                                            }
                                        </td>
                                        <td title="線上投稿.狀態" class="text-center">@ADDStatus.GetADDT01StatusString((byte)aADDT01.WRITING_STATUS)</td>
                                    </tr>
                                }
                            }
                            else
                            {
                                <tr class="text-center">
                                    <td colspan="3">無線上投稿資料</td>
                                </tr>
                            }
                        </tbody>
                        <tfoot>
                            <tr class="text-right">
                                <td colspan="3">
                                    <button type="button" class="btn btn-link-ez btn-xs" onclick="onBtnLinkADDT01('','@Model.wUSER_NO','@Model.DATA_ANGLE_TYPE')">more</button>
                                </td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
                <div class="col-sm-6">

                    <table class="table-ecool table-hover table-ecool-pink-SEC">
                        <thead>
                            <tr class="text-center">
                                <th colspan="3">
                                    建議與鼓勵 @Model.DATA_TYPE_NAME
                                </th>
                            </tr>
                            <tr class="text-center">
                                <th>
                                    日期
                                </th>
                                <th>
                                    文章標題
                                </th>
                                <th>
                                    留言
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            @if (Model.ADDT02List.Count > 0)
                            {
                                foreach (var aADDT02 in Model.ADDT02List)
                                {
                                    <tr onclick="onBtnLinkADDT01('@aADDT02.WRITING_NO','@aADDT02.USER_NO','@Model.DATA_ANGLE_TYPE')" style="cursor:pointer">
                                        <td title="建議與鼓勵.日期" class="text-center">@Convert.ToDateTime(aADDT02.CRE_DATE).ToString("yyyy/MM/dd")</td>
                                        <td title="建議與鼓勵.文章標題">@StringHelper.LeftStringR(aADDT02.ADDT01.SUBJECT, len)</td>
                                        <td title="建議與鼓勵.留言">@StringHelper.LeftStringR(aADDT02.COMMENT, len)</td>
                                    </tr>
                                }
                            }
                            else
                            {
                                <tr class="text-center">
                                    <td colspan="3">無建議與鼓勵資料</td>
                                </tr>
                            }
                        </tbody>
                        <tfoot>
                            <tr class="text-right">
                                <td colspan="3">
                                    <button type="button" class="btn btn-link-ez btn-xs" onclick="onBtnLinkADDT01('','@Model.wUSER_NO','@Model.DATA_ANGLE_TYPE')">more</button>
                                </td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-6">
                    <table class="table-ecool  table-hover table-ecool-yellow-SEC">
                        <thead>
                            <tr class="text-center">
                                <th colspan="3">
                                    閱讀認證 @Model.DATA_TYPE_NAME
                                </th>
                            </tr>
                            <tr class="text-center">
                                <th>
                                    日期
                                </th>
                                <th>
                                    書名
                                </th>
                                <th>
                                    狀態
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            @if (Model.ADDT06List.Count > 0)
                            {
                                foreach (var aADDT06 in Model.ADDT06List)
                                {
                                    <tr onclick="onBtnLinkADDT06('@aADDT06.APPLY_NO','@Model.DATA_ANGLE_TYPE')" style="cursor:pointer">
                                        <td title="閱讀認證.日期" class="text-center">@Convert.ToDateTime(aADDT06.CRE_DATE).ToString("yyyy/MM/dd")</td>
                                        <td title="閱讀認證.書名">
                                            @aADDT06.BOOK_NAME

                                            @if (aADDT06.SHARE_YN == "Y")
                                            {
                                                <img src="~/Content/img/icons-like-05.png" />
                                            }
                                        </td>
                                        <td title="閱讀認證.狀態">@ADDStatus.GetADDT06StatusString(aADDT06.APPLY_STATUS)</td>
                                    </tr>
                                }
                            }
                            else
                            {
                                <tr class="text-center">

                                    <td colspan="3">無閱讀認證資料</td>
                                </tr>
                            }
                        </tbody>
                        <tfoot>
                            <tr class="text-right">
                                <td colspan="3">
                                    <button type="button" class="btn btn-link-ez  btn-xs" onclick="onBtnLinkADDT06('', '@Model.DATA_ANGLE_TYPE')">more</button>
                                </td>
                            </tr>
                        </tfoot>
                    </table>
                </div>

                <div class="col-sm-6">
                    <table class="table-ecool table-hover table-ecool-Tangerine-SEC">
                        <thead>
                            <tr class="text-center">
                                <th colspan="3">
                                    校內表現/校外榮譽 @Model.DATA_TYPE_NAME
                                </th>
                            </tr>
                            <tr class="text-center">
                                <th>
                                    日期
                                </th>
                                <th>
                                    內容
                                </th>
                                <th>
                                    獲得點數
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            @if (Model.SchoolDataList.Count > 0)
                            {
                                foreach (var item in Model.SchoolDataList)
                                {
                                    <tr onclick="onBtnLinkSchoolData('@item.NO','@item.USER_NO','@item.SYS_TABLE')" style="cursor:pointer">
                                        <td title="校內表現/校外榮譽.日期" class="text-center">@Convert.ToDateTime(item.CRE_DATE).ToString("yyyy/MM/dd")</td>
                                        <td title="表現/榮譽內容">@StringHelper.LeftStringR(item.CONTENT_TXT, len)</td>
                                        <td title="獲得點數" class="text-center">@item.CASH</td>
                                    </tr>
                                }
                            }
                            else
                            {
                                <tr class="text-center">
                                    <td colspan="3">無校內表現/校外榮譽資料</td>
                                </tr>
                            }
                        </tbody>
                        <tfoot>
                            <tr class="text-center">

                                <td colspan="2" align="right"><button type="button" class="btn btn-link-ez  btn-xs" onclick="onBtnLinkSchoolData('','@Model.wUSER_NO','@ECOOL_APP.com.ecool.Models.DTO.ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_IN')">校內more</button></td>
                                <td><button type="button" class="btn btn-link-ez  btn-xs" onclick="onBtnLinkSchoolData('','@Model.wUSER_NO','@ECOOL_APP.com.ecool.Models.DTO.ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_OBC')">校外more</button></td>
                            </tr>
                        </tfoot>
                    </table>
                </div>

                <br />
                @if (user != null)
                {
                    if (user.USER_TYPE != UserType.Parents)
                    {
                        <div class="col-sm-12">
                            <table class="table-ecool table-hover table-ecool-Tangerine2-SEC">
                                <thead>
                                    <tr class="text-center">
                                        <th colspan="6">
                                            可兌換獎品
                                        </th>
                                    </tr>
                                    <tr class="text-center">
                                        <th>
                                            圖示
                                        </th>
                                        <th>
                                            品名
                                        </th>
                                        <th>
                                            兌換點數
                                        </th>
                                        <th class="hidden-xs">
                                            剩餘數量
                                        </th>
                                        <th class="hidden-xs">
                                            兌換期限
                                        </th>
                                        <th>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @if (Model.AWAT02List != null || Model.AWAT09List != null)
                                    {
                                        int AwatCount = 0;

                                        if (Model.AWAT02List != null)
                                        {
                                            AwatCount = Model.AWAT02List.Count;
                                        }
                                        else
                                        {
                                            AwatCount = Model.AWAT09List.Count;
                                        }

                                        if (AwatCount > 0)
                                        {
                                            if (Model.AWAT02List != null)
                                            {
                                                foreach (var aAWAT02 in Model.AWAT02List)
                                                {
                                                    <tr>
                                                        <td title="可兌換獎品.圖示" align="center" valign="middle">
                                                            <img src='@Url.Content(ViewBag.SysAwardPath+ aAWAT02.SCHOOL_NO + @"/"+aAWAT02.IMG_FILE)' class="img-responsive" alt="Responsive image" style="width:100%;max-width:70px;max-height:70px;" />
                                                        </td>
                                                        <td title="可兌換獎品.品名">@StringHelper.LeftStringR(aAWAT02.AWARD_NAME, 15)</td>
                                                        <td title="可兌換獎品.兌換點數" class="text-center">@aAWAT02.COST_CASH</td>
                                                        <td title="可兌換獎品.剩餘數量" class="text-center hidden-xs">@aAWAT02.QTY_STORAGE</td>

                                                        <td title="可兌換獎品.兌換期限" class="hidden-xs text-center">@aAWAT02.EDATETIME.Value.ToString("yyyy/MM/dd")</td>

                                                        <td class="text-center">
                                                            @{
                                                                string NG = EcoolWeb.Controllers.AWAI01Controller.CheckCxchange(aAWAT02);

                                                                if (string.IsNullOrWhiteSpace(NG) == false)
                                                                {
                                                                    <button role="button" class="btn-default btn btn-xs disabled" disabled>
                                                                        無法兌換 -
                                                                        @Html.Raw(HttpUtility.HtmlDecode(NG))
                                                                    </button>

                                                                }
                                                                else
                                                                {
                                                                    <button type="button" class="btn-default btn btn-xs" onclick="funGetExchange('@aAWAT02.AWARD_NO')">
                                                                        我要兌換
                                                                    </button>
                                                                }

                                                            }
                                                        </td>
                                                    </tr>
                                                }
                                            }

                                            if (Model.AWAT09List != null)
                                            {
                                                foreach (var aAWAT09 in Model.AWAT09List)
                                                {
                                                    <tr>
                                                        <td title="可兌換獎品.圖示" align="center" valign="middle">
                                                            <img src='@Url.Content(ViewBag.SysAwardPath +aAWAT09.IMG_FILE)' class="img-responsive" alt="Responsive image" style="width:100%;max-width:70px;max-height:70px;" />
                                                        </td>
                                                        <td title="可兌換獎品.品名">@StringHelper.LeftStringR(aAWAT09.AWARD_NAME, 15)</td>
                                                        <td title="可兌換獎品.兌換點數" class="text-center">@aAWAT09.COST_CASH</td>
                                                        <td title="可兌換獎品.剩餘數量" class="text-center hidden-xs">@aAWAT09.QTY_STORAGE</td>
                                                        <td title="可兌換獎品.兌換期限" class="text-center hidden-xs">@aAWAT09.EDATETIME.Value.ToString("yyyy/MM/dd")</td>
                                                        <td title="" class="text-center">@aAWAT09.EDATETIME.Value.ToString("yyyy/MM/dd")</td>
                                                    </tr>
                                                }
                                            }
                                        }
                                        else
                                        {
                                            <tr class="text-center">
                                                <td colspan="6">無可兌換獎品</td>
                                            </tr>
                                        }
                                    }
                                </tbody>
                                <tfoot>
                                    <tr class="text-right">
                                        <td colspan="6">
                                            <button type="button" class="btn btn-link-ez  btn-xs" onclick="onBtnLinkAWAT02()">more</button>
                                        </td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    }
                }
            </div>
        </div>

        @*if (ViewBag.QAT02Qty > 0)
            {
                <div class="col-md-6" style="height:65px;">
                    <a href='@Url.Action("Index", "QAI01")'>
                        <img src="~/Content/img/web-student-todo1.png" class="imgEZ" alt="Responsive image" style="max-width:220px" title="待觀看酷課雲影片" />
                        &nbsp;<span class="lnkFont2">@ViewBag.QAT02Qty</span>
                    </a>
                </div>
            }*@

    }
</div>
<div class="col-md-6" style="height:65px;">
    <a href='@Url.Action("Index", "ADDI05")'>
        <span class="lnkFont2" style="width:300px">有獎徵答活動</span>
    </a>
</div>

<script language="javascript" type="text/javascript">

        //$(document).ready(function () {
        //    $(".groupWRITING_NO").colorbox({ iframe: true, opacity: 0.5, width: "99%", height: "99%", rel: 'groupWRITING_NO' });
        //});

        //$(document).ready(function () {
        //    $("#MyAPPLY").colorbox({ iframe: true, opacity: 0.5, width: "99%", height: "99%" });
        //});
    window.onload = function () {
        RemindShow()
    }
    function RemindShow() {
        var remind_font = $("#remind-content").text().length;

        if (remind_font > 0) {
            $('#remind-modal').modal('show');
        }
    }
        function printScreenH() {
            window.open('@Url.Action("Index", "SECI01")' + '?PRINT=Y', '_blank');
        }

        function funBookW() {

            if ($('#W1').length > 0) {
                $('#W1').click();
            }
            else {
                alert('無任何資料')
            }
        }

        function funBookA() {
            $('#A1').click();
        }

        //換獎品mory
        function onBtnLinkAWAT02() {
            window.open('@Url.Action("AwatQ02", "Awat")', '_blank');
        }

        //線上投稿
        function onBtnLinkADDT01(NoVal, USER_NO, DATA_TYPE) {

            if (NoVal != '') {
                window.open('@Url.Action("Details", "ADDI01")' + '?WRITING_NO=' + NoVal + '&whereUserNo=' + USER_NO + '', '_blank');
            }
            else {

                if (DATA_TYPE == '@EcoolWeb.Models.UserProfileHelper.AngleVal.OneData') {
                    window.open('@Url.Action("Index", "ADDI01")' + '?whereUserNo=' + USER_NO + '', '_blank');
                }
                else {
                    window.open('@Url.Action("Index", "ADDI01")', '_blank');
                }
            }
        }

        //閱讀認證
        function onBtnLinkADDT06(NoVal, DATA_TYPE) {
            if (NoVal != '') {
                window.open('@Url.Action("ADDTALLListDetails", "ADDT")' + '?APPLY_NO=' + NoVal + '', '_blank');
            }
            else {
                if (DATA_TYPE == '@EcoolWeb.Models.UserProfileHelper.AngleVal.OneData') {
                    window.open('@Url.Action("QUERY", "ADDI02")', '_blank');
                }
                else {
                    window.open('@Url.Action("ADDTALLList", "ADDT")', '_blank');
                }
            }
        }

        function onBtnLinkSchoolData(NoVal, USER_NO, DATA_TYPE) {
            if (DATA_TYPE == '@ECOOL_APP.com.ecool.Models.DTO.ADDI09EditViewModel.SysTableTypeVal.DATA_TABLE_IN') {
                window.open('@Url.Action("QUERY", "ADDI06")', '_blank');
            }
            else {
                window.open('@Url.Action("QUERY", "ADDI07")', '_blank');
            }
        }

        function funGetExchange(AWARD_NO) {
            window.open('@Url.Action("AwatExchange02", "Awat")' + '?hidAWARD_NO=' + AWARD_NO, '_blank')
        }
</script>