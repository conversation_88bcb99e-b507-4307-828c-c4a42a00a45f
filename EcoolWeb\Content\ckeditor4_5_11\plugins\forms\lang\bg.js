﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'forms', 'bg', {
	button: {
		title: 'Настройки на бутона',
		text: 'Текст (стойност)',
		type: 'Тип',
		typeBtn: 'Бутон',
		typeSbm: 'Добави',
		typeRst: 'Нулиране'
	},
	checkboxAndRadio: {
		checkboxTitle: 'Checkbox Properties',
		radioTitle: 'Настройки на радиобутон',
		value: 'Стойност',
		selected: 'Избрано',
		required: 'Required' // MISSING
	},
	form: {
		title: 'Настройки на формата',
		menu: 'Настройки на формата',
		action: 'Действие',
		method: 'Метод',
		encoding: 'Кодиране'
	},
	hidden: {
		title: 'Настройки за скрито поле',
		name: '<PERSON><PERSON><PERSON>',
		value: 'Стойност'
	},
	select: {
		title: 'Selection Field Properties',
		selectInfo: 'Select Info',
		opAvail: 'Налични опции',
		value: 'Стойност',
		size: 'Размер',
		lines: 'линии',
		chkMulti: 'Allow multiple selections',
		required: 'Required', // MISSING
		opText: 'Текст',
		opValue: 'Стойност',
		btnAdd: 'Добави',
		btnModify: 'Промени',
		btnUp: 'На горе',
		btnDown: 'На долу',
		btnSetValue: 'Set as selected value',
		btnDelete: 'Изтриване'
	},
	textarea: {
		title: 'Опции за текстовата зона',
		cols: 'Колони',
		rows: 'Редове'
	},
	textfield: {
		title: 'Настройки за текстово поле',
		name: 'Име',
		value: 'Стойност',
		charWidth: 'Ширина на знаците',
		maxChars: 'Макс. знаци',
		required: 'Required', // MISSING
		type: 'Тип',
		typeText: 'Текст',
		typePass: 'Парола',
		typeEmail: 'Email',
		typeSearch: 'Търсене',
		typeTel: 'Телефонен номер',
		typeUrl: 'Уеб адрес'
	}
} );
