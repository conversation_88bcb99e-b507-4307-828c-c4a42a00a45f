/*
 *  /MathJax/jax/output/HTML-CSS/fonts/TeX/AMS/Regular/BBBold.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.MathJax_AMS,{32:[0,0,250,0,0],65:[701,1,722,17,703],66:[683,1,667,11,620],67:[702,19,722,39,684],68:[683,1,722,16,688],69:[683,1,667,12,640],70:[683,1,611,12,584],71:[702,19,778,39,749],72:[683,1,778,14,762],73:[683,1,389,20,369],74:[683,77,500,6,478],75:[683,1,778,22,768],76:[683,1,667,12,640],77:[683,1,944,17,926],78:[683,20,722,20,702],79:[701,19,778,34,742],80:[683,1,611,16,597],81:[701,181,778,34,742],82:[683,1,722,16,705],83:[702,12,556,28,528],84:[683,1,667,33,635],85:[683,19,722,16,709],86:[683,20,722,0,719],87:[683,19,1000,5,994],88:[683,1,722,16,705],89:[683,1,722,16,704],90:[683,1,667,29,635],107:[683,1,556,17,534]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/AMS/Regular/BBBold.js");
