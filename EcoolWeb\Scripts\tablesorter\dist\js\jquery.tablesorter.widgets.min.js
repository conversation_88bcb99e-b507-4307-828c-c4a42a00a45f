(function(factory){if (typeof define === 'function' && define.amd){define(['jquery'], factory);} else if (typeof module === 'object' && typeof module.exports === 'object'){module.exports = factory(require('jquery'));} else {factory(jQuery);}}(function(jQuery){

/*! tablesorter (FORK) - updated 2018-11-20 (v2.31.1)*/
!function(e){"function"==typeof define&&define.amd?define(["jquery"],e):"object"==typeof module&&"object"==typeof module.exports?module.exports=e(require("jquery")):e(jQuery)}(function(e){return function(b,y,_){"use strict";var v=b.tablesorter||{};b.extend(!0,v.defaults,{fixedUrl:"",widgetOptions:{storage_fixedUrl:"",storage_group:"",storage_page:"",storage_storageType:"",storage_tableId:"",storage_useSessionStorage:""}}),v.storage=function(e,t,r,i){var a,l,s,n=!1,o={},c=(e=b(e)[0]).config,d=c&&c.widgetOptions,f=v.debug(c,"storage"),h=(i&&i.storageType||d&&d.storage_storageType).toString().charAt(0).toLowerCase(),u=h?"":i&&i.useSessionStorage||d&&d.storage_useSessionStorage,p=b(e),g=i&&i.id||p.attr(i&&i.group||d&&d.storage_group||"data-table-group")||d&&d.storage_tableId||e.id||b(".tablesorter").index(p),m=i&&i.url||p.attr(i&&i.page||d&&d.storage_page||"data-table-page")||d&&d.storage_fixedUrl||c&&c.fixedUrl||y.location.pathname;if("c"!==h&&(h="s"===h||u?"sessionStorage":"localStorage")in y)try{y[h].setItem("_tmptest","temp"),n=!0,y[h].removeItem("_tmptest")}catch(e){console.warn(h+" is not supported in this browser")}if(f&&console.log("Storage >> Using",n?h:"cookies"),b.parseJSON&&(o=n?b.parseJSON(y[h][t]||"null")||{}:(l=_.cookie.split(/[;\s|=]/),0!==(a=b.inArray(t,l)+1)&&b.parseJSON(l[a]||"null")||{})),void 0===r||!y.JSON||!JSON.hasOwnProperty("stringify"))return o&&o[m]?o[m][g]:"";o[m]||(o[m]={}),o[m][g]=r,n?y[h][t]=JSON.stringify(o):((s=new Date).setTime(s.getTime()+31536e6),_.cookie=t+"="+JSON.stringify(o).replace(/\"/g,'"')+"; expires="+s.toGMTString()+"; path=/")}}(e,window,document),function(C){"use strict";var z=C.tablesorter||{};z.themes={bootstrap:{table:"table table-bordered table-striped",caption:"caption",header:"bootstrap-header",sortNone:"",sortAsc:"",sortDesc:"",active:"",hover:"",icons:"",iconSortNone:"bootstrap-icon-unsorted",iconSortAsc:"glyphicon glyphicon-chevron-up",iconSortDesc:"glyphicon glyphicon-chevron-down",filterRow:"",footerRow:"",footerCells:"",even:"",odd:""},jui:{table:"ui-widget ui-widget-content ui-corner-all",caption:"ui-widget-content",header:"ui-widget-header ui-corner-all ui-state-default",sortNone:"",sortAsc:"",sortDesc:"",active:"ui-state-active",hover:"ui-state-hover",icons:"ui-icon",iconSortNone:"ui-icon-carat-2-n-s ui-icon-caret-2-n-s",iconSortAsc:"ui-icon-carat-1-n ui-icon-caret-1-n",iconSortDesc:"ui-icon-carat-1-s ui-icon-caret-1-s",filterRow:"",footerRow:"",footerCells:"",even:"ui-widget-content",odd:"ui-state-default"}},C.extend(z.css,{wrapper:"tablesorter-wrapper"}),z.addWidget({id:"uitheme",priority:10,format:function(e,t,r){var i,a,l,s,n,o,c,d,f,h,u,p,g,m=z.themes,b=t.$table.add(C(t.namespace+"_extra_table")),y=t.$headers.add(C(t.namespace+"_extra_headers")),_=t.theme||"jui",v=m[_]||{},w=C.trim([v.sortNone,v.sortDesc,v.sortAsc,v.active].join(" ")),x=C.trim([v.iconSortNone,v.iconSortDesc,v.iconSortAsc].join(" ")),S=z.debug(t,"uitheme");for(S&&(n=new Date),b.hasClass("tablesorter-"+_)&&t.theme===t.appliedTheme&&r.uitheme_applied||(r.uitheme_applied=!0,h=m[t.appliedTheme]||{},u=(g=!C.isEmptyObject(h))?[h.sortNone,h.sortDesc,h.sortAsc,h.active].join(" "):"",p=g?[h.iconSortNone,h.iconSortDesc,h.iconSortAsc].join(" "):"",g&&(r.zebra[0]=C.trim(" "+r.zebra[0].replace(" "+h.even,"")),r.zebra[1]=C.trim(" "+r.zebra[1].replace(" "+h.odd,"")),t.$tbodies.children().removeClass([h.even,h.odd].join(" "))),v.even&&(r.zebra[0]+=" "+v.even),v.odd&&(r.zebra[1]+=" "+v.odd),b.children("caption").removeClass(h.caption||"").addClass(v.caption),d=b.removeClass((t.appliedTheme?"tablesorter-"+(t.appliedTheme||""):"")+" "+(h.table||"")).addClass("tablesorter-"+_+" "+(v.table||"")).children("tfoot"),t.appliedTheme=t.theme,d.length&&d.children("tr").removeClass(h.footerRow||"").addClass(v.footerRow).children("th, td").removeClass(h.footerCells||"").addClass(v.footerCells),y.removeClass((g?[h.header,h.hover,u].join(" "):"")||"").addClass(v.header).not(".sorter-false").unbind("mouseenter.tsuitheme mouseleave.tsuitheme").bind("mouseenter.tsuitheme mouseleave.tsuitheme",function(e){C(this)["mouseenter"===e.type?"addClass":"removeClass"](v.hover||"")}),y.each(function(){var e=C(this);e.find("."+z.css.wrapper).length||e.wrapInner('<div class="'+z.css.wrapper+'" style="position:relative;height:100%;width:100%"></div>')}),t.cssIcon&&y.find("."+z.css.icon).removeClass(g?[h.icons,p].join(" "):"").addClass(v.icons||""),z.hasWidget(t.table,"filter")&&(a=function(){b.children("thead").children("."+z.css.filterRow).removeClass(g&&h.filterRow||"").addClass(v.filterRow||"")},r.filter_initialized?a():b.one("filterInit",function(){a()}))),i=0;i<t.columns;i++)o=t.$headers.add(C(t.namespace+"_extra_headers")).not(".sorter-false").filter('[data-column="'+i+'"]'),c=z.css.icon?o.find("."+z.css.icon):C(),(f=y.not(".sorter-false").filter('[data-column="'+i+'"]:last')).length&&(o.removeClass(w),c.removeClass(x),f[0].sortDisabled?c.removeClass(v.icons||""):(l=v.sortNone,s=v.iconSortNone,f.hasClass(z.css.sortAsc)?(l=[v.sortAsc,v.active].join(" "),s=v.iconSortAsc):f.hasClass(z.css.sortDesc)&&(l=[v.sortDesc,v.active].join(" "),s=v.iconSortDesc),o.addClass(l),c.addClass(s||"")));S&&console.log("uitheme >> Applied "+_+" theme"+z.benchmark(n))},remove:function(e,t,r,i){if(r.uitheme_applied){var a=t.$table,l=t.appliedTheme||"jui",s=z.themes[l]||z.themes.jui,n=a.children("thead").children(),o=s.sortNone+" "+s.sortDesc+" "+s.sortAsc,c=s.iconSortNone+" "+s.iconSortDesc+" "+s.iconSortAsc;a.removeClass("tablesorter-"+l+" "+s.table),r.uitheme_applied=!1,i||(a.find(z.css.header).removeClass(s.header),n.unbind("mouseenter.tsuitheme mouseleave.tsuitheme").removeClass(s.hover+" "+o+" "+s.active).filter("."+z.css.filterRow).removeClass(s.filterRow),n.find("."+z.css.icon).removeClass(s.icons+" "+c))}}})}(e),function(b){"use strict";var y=b.tablesorter||{};y.addWidget({id:"columns",priority:65,options:{columns:["primary","secondary","tertiary"]},format:function(e,t,r){var i,a,l,s,n,o,c,d,f=t.$table,h=t.$tbodies,u=t.sortList,p=u.length,g=r&&r.columns||["primary","secondary","tertiary"],m=g.length-1;for(c=g.join(" "),a=0;a<h.length;a++)(l=(i=y.processTbody(e,h.eq(a),!0)).children("tr")).each(function(){if(n=b(this),"none"!==this.style.display&&(o=n.children().removeClass(c),u&&u[0]&&(o.eq(u[0][0]).addClass(g[0]),1<p)))for(d=1;d<p;d++)o.eq(u[d][0]).addClass(g[d]||g[m])}),y.processTbody(e,i,!1);if(s=!1!==r.columns_thead?["thead tr"]:[],!1!==r.columns_tfoot&&s.push("tfoot tr"),s.length&&(l=f.find(s.join(",")).children().removeClass(c),p))for(d=0;d<p;d++)l.filter('[data-column="'+u[d][0]+'"]').addClass(g[d]||g[m])},remove:function(e,t,r){var i,a,l=t.$tbodies,s=(r.columns||["primary","secondary","tertiary"]).join(" ");for(t.$headers.removeClass(s),t.$table.children("tfoot").children("tr").children("th, td").removeClass(s),i=0;i<l.length;i++)(a=y.processTbody(e,l.eq(i),!0)).children("tr").each(function(){b(this).children().removeClass(s)}),y.processTbody(e,a,!1)}})}(e),function(A){"use strict";var I,O,E=A.tablesorter||{},b=E.css,o=E.keyCodes;A.extend(b,{filterRow:"tablesorter-filter-row",filter:"tablesorter-filter",filterDisabled:"disabled",filterRowHide:"hideme"}),A.extend(o,{backSpace:8,escape:27,space:32,left:37,down:40}),E.addWidget({id:"filter",priority:50,options:{filter_cellFilter:"",filter_childRows:!1,filter_childByColumn:!1,filter_childWithSibs:!0,filter_columnAnyMatch:!0,filter_columnFilters:!0,filter_cssFilter:"",filter_defaultAttrib:"data-value",filter_defaultFilter:{},filter_excludeFilter:{},filter_external:"",filter_filteredRow:"filtered",filter_filterLabel:'Filter "{{label}}" column by...',filter_formatter:null,filter_functions:null,filter_hideEmpty:!0,filter_hideFilters:!1,filter_ignoreCase:!0,filter_liveSearch:!0,filter_matchType:{input:"exact",select:"exact"},filter_onlyAvail:"filter-onlyAvail",filter_placeholder:{search:"",select:""},filter_reset:null,filter_resetOnEsc:!0,filter_saveFilters:!1,filter_searchDelay:300,filter_searchFiltered:!0,filter_selectSource:null,filter_selectSourceSeparator:"|",filter_serversideFiltering:!1,filter_startsWith:!1,filter_useParsedData:!1},format:function(e,t,r){t.$table.hasClass("hasFilters")||I.init(e,t,r)},remove:function(e,t,r,i){var a,l,s=t.$table,n=t.$tbodies,o="addRows updateCell update updateRows updateComplete appendCache filterReset filterAndSortReset filterFomatterUpdate filterEnd search stickyHeadersInit ".split(" ").join(t.namespace+"filter ");if(s.removeClass("hasFilters").unbind(o.replace(E.regex.spaces," ")).find("."+b.filterRow).remove(),r.filter_initialized=!1,!i){for(a=0;a<n.length;a++)(l=E.processTbody(e,n.eq(a),!0)).children().removeClass(r.filter_filteredRow).show(),E.processTbody(e,l,!1);r.filter_reset&&A(document).undelegate(r.filter_reset,"click"+t.namespace+"filter")}}}),O=(I=E.filter={regex:{regex:/^\/((?:\\\/|[^\/])+)\/([migyu]{0,5})?$/,child:/tablesorter-childRow/,filtered:/filtered/,type:/undefined|number/,exact:/(^[\"\'=]+)|([\"\'=]+$)/g,operators:/[<>=]/g,query:"(q|query)",wild01:/\?/g,wild0More:/\*/g,quote:/\"/g,isNeg1:/(>=?\s*-\d)/,isNeg2:/(<=?\s*\d)/},types:{or:function(e,t,r){if(!O.orTest.test(t.iFilter)&&!O.orSplit.test(t.filter)||O.regex.test(t.filter))return null;var i,a,l,s=A.extend({},t),n=t.filter.split(O.orSplit),o=t.iFilter.split(O.orSplit),c=n.length;for(i=0;i<c;i++){s.nestedFilters=!0,s.filter=""+(I.parseFilter(e,n[i],t)||""),s.iFilter=""+(I.parseFilter(e,o[i],t)||""),l="("+(I.parseFilter(e,s.filter,t)||"")+")";try{if(a=new RegExp(t.isMatch?l:"^"+l+"$",e.widgetOptions.filter_ignoreCase?"i":"").test(s.exact)||I.processTypes(e,s,r))return a}catch(e){return null}}return a||!1},and:function(e,t,r){if(O.andTest.test(t.filter)){var i,a,l,s,n=A.extend({},t),o=t.filter.split(O.andSplit),c=t.iFilter.split(O.andSplit),d=o.length;for(i=0;i<d;i++){n.nestedFilters=!0,n.filter=""+(I.parseFilter(e,o[i],t)||""),n.iFilter=""+(I.parseFilter(e,c[i],t)||""),s=("("+(I.parseFilter(e,n.filter,t)||"")+")").replace(O.wild01,"\\S{1}").replace(O.wild0More,"\\S*");try{l=new RegExp(t.isMatch?s:"^"+s+"$",e.widgetOptions.filter_ignoreCase?"i":"").test(n.exact)||I.processTypes(e,n,r),a=0===i?l:a&&l}catch(e){return null}}return a||!1}return null},regex:function(e,t){if(O.regex.test(t.filter)){var r,i=t.filter_regexCache[t.index]||O.regex.exec(t.filter),a=i instanceof RegExp;try{a||(t.filter_regexCache[t.index]=i=new RegExp(i[1],i[2])),r=i.test(t.exact)}catch(e){r=!1}return r}return null},operators:function(e,t){if(O.operTest.test(t.iFilter)&&""!==t.iExact){var r,i,a,l=e.table,s=t.parsed[t.index],n=E.formatFloat(t.iFilter.replace(O.operators,""),l),o=e.parsers[t.index]||{},c=n;return(s||"numeric"===o.type)&&(a=A.trim(""+t.iFilter.replace(O.operators,"")),n="number"!=typeof(i=I.parseFilter(e,a,t,!0))||""===i||isNaN(i)?n:i),r=!s&&"numeric"!==o.type||isNaN(n)||void 0===t.cache?(a=isNaN(t.iExact)?t.iExact.replace(E.regex.nondigit,""):t.iExact,E.formatFloat(a,l)):t.cache,O.gtTest.test(t.iFilter)?i=O.gteTest.test(t.iFilter)?n<=r:n<r:O.ltTest.test(t.iFilter)&&(i=O.lteTest.test(t.iFilter)?r<=n:r<n),i||""!==c||(i=!0),i}return null},notMatch:function(e,t){if(O.notTest.test(t.iFilter)){var r,i=t.iFilter.replace("!",""),a=I.parseFilter(e,i,t)||"";return O.exact.test(a)?""===(a=a.replace(O.exact,""))||A.trim(a)!==t.iExact:(r=t.iExact.search(A.trim(a)),""===a||(t.anyMatch?r<0:!(e.widgetOptions.filter_startsWith?0===r:0<=r)))}return null},exact:function(e,t){if(O.exact.test(t.iFilter)){var r=t.iFilter.replace(O.exact,""),i=I.parseFilter(e,r,t)||"";return t.anyMatch?0<=A.inArray(i,t.rowArray):i==t.iExact}return null},range:function(e,t){if(O.toTest.test(t.iFilter)){var r,i,a,l,s=e.table,n=t.index,o=t.parsed[n],c=t.iFilter.split(O.toSplit);return i=c[0].replace(E.regex.nondigit,"")||"",a=E.formatFloat(I.parseFilter(e,i,t),s),i=c[1].replace(E.regex.nondigit,"")||"",l=E.formatFloat(I.parseFilter(e,i,t),s),(o||"numeric"===e.parsers[n].type)&&(a=""===(r=e.parsers[n].format(""+c[0],s,e.$headers.eq(n),n))||isNaN(r)?a:r,l=""===(r=e.parsers[n].format(""+c[1],s,e.$headers.eq(n),n))||isNaN(r)?l:r),r=!o&&"numeric"!==e.parsers[n].type||isNaN(a)||isNaN(l)?(i=isNaN(t.iExact)?t.iExact.replace(E.regex.nondigit,""):t.iExact,E.formatFloat(i,s)):t.cache,l<a&&(i=a,a=l,l=i),a<=r&&r<=l||""===a||""===l}return null},wild:function(e,t){if(O.wildOrTest.test(t.iFilter)){var r=""+(I.parseFilter(e,t.iFilter,t)||"");!O.wildTest.test(r)&&t.nestedFilters&&(r=t.isMatch?r:"^("+r+")$");try{return new RegExp(r.replace(O.wild01,"\\S{1}").replace(O.wild0More,"\\S*"),e.widgetOptions.filter_ignoreCase?"i":"").test(t.exact)}catch(e){return null}}return null},fuzzy:function(e,t){if(O.fuzzyTest.test(t.iFilter)){var r,i=0,a=t.iExact.length,l=t.iFilter.slice(1),s=I.parseFilter(e,l,t)||"";for(r=0;r<a;r++)t.iExact[r]===s[i]&&(i+=1);return i===s.length}return null}},init:function(r){E.language=A.extend(!0,{},{to:"to",or:"or",and:"and"},E.language);var e,t,i,a,l,s,n,o,c=r.config,d=c.widgetOptions,f=function(e,t,r){return""===(t=t.trim())?"":(e||"")+t+(r||"")};if(c.$table.addClass("hasFilters"),c.lastSearch=[],d.filter_searchTimer=null,d.filter_initTimer=null,d.filter_formatterCount=0,d.filter_formatterInit=[],d.filter_anyColumnSelector='[data-column="all"],[data-column="any"]',d.filter_multipleColumnSelector='[data-column*="-"],[data-column*=","]',s="\\{"+O.query+"\\}",A.extend(O,{child:new RegExp(c.cssChildRow),filtered:new RegExp(d.filter_filteredRow),alreadyFiltered:new RegExp("(\\s+(-"+f("|",E.language.or)+f("|",E.language.to)+")\\s+)","i"),toTest:new RegExp("\\s+(-"+f("|",E.language.to)+")\\s+","i"),toSplit:new RegExp("(?:\\s+(?:-"+f("|",E.language.to)+")\\s+)","gi"),andTest:new RegExp("\\s+("+f("",E.language.and,"|")+"&&)\\s+","i"),andSplit:new RegExp("(?:\\s+(?:"+f("",E.language.and,"|")+"&&)\\s+)","gi"),orTest:new RegExp("(\\|"+f("|\\s+",E.language.or,"\\s+")+")","i"),orSplit:new RegExp("(?:\\|"+f("|\\s+(?:",E.language.or,")\\s+")+")","gi"),iQuery:new RegExp(s,"i"),igQuery:new RegExp(s,"ig"),operTest:/^[<>]=?/,gtTest:/>/,gteTest:/>=/,ltTest:/</,lteTest:/<=/,notTest:/^\!/,wildOrTest:/[\?\*\|]/,wildTest:/\?\*/,fuzzyTest:/^~/,exactTest:/[=\"\|!]/}),s=c.$headers.filter(".filter-false, .parser-false").length,!1!==d.filter_columnFilters&&s!==c.$headers.length&&I.buildRow(r,c,d),i="addRows updateCell update updateRows updateComplete appendCache filterReset "+"filterAndSortReset filterResetSaved filterEnd search ".split(" ").join(c.namespace+"filter "),c.$table.bind(i,function(e,t){return s=d.filter_hideEmpty&&A.isEmptyObject(c.cache)&&!(c.delayInit&&"appendCache"===e.type),c.$table.find("."+b.filterRow).toggleClass(d.filter_filteredRow,s),/(search|filter)/.test(e.type)||(e.stopPropagation(),I.buildDefault(r,!0)),"filterReset"===e.type||"filterAndSortReset"===e.type?(c.$table.find("."+b.filter).add(d.filter_$externalFilters).val(""),"filterAndSortReset"===e.type?E.sortReset(this.config,function(){I.searching(r,[])}):I.searching(r,[])):"filterResetSaved"===e.type?E.storage(r,"tablesorter-filters",""):"filterEnd"===e.type?I.buildDefault(r,!0):(t="search"===e.type?t:"updateComplete"===e.type?c.$table.data("lastSearch"):"",/(update|add)/.test(e.type)&&"updateComplete"!==e.type&&(c.lastCombinedFilter=null,c.lastSearch=[],setTimeout(function(){c.$table.triggerHandler("filterFomatterUpdate")},100)),I.searching(r,t,!0)),!1}),d.filter_reset&&(d.filter_reset instanceof A?d.filter_reset.click(function(){c.$table.triggerHandler("filterReset")}):A(d.filter_reset).length&&A(document).undelegate(d.filter_reset,"click"+c.namespace+"filter").delegate(d.filter_reset,"click"+c.namespace+"filter",function(){c.$table.triggerHandler("filterReset")})),d.filter_functions)for(l=0;l<c.columns;l++)if(n=E.getColumnData(r,d.filter_functions,l))if(o=!((a=c.$headerIndexed[l].removeClass("filter-select")).hasClass("filter-false")||a.hasClass("parser-false")),!(e="")===n&&o)I.buildSelect(r,l);else if("object"==typeof n&&o){for(t in n)"string"==typeof t&&(e+=""===e?'<option value="">'+(a.data("placeholder")||a.attr("data-placeholder")||d.filter_placeholder.select||"")+"</option>":"",0<=(i=s=t).indexOf(d.filter_selectSourceSeparator)&&(i=(s=t.split(d.filter_selectSourceSeparator))[1],s=s[0]),e+="<option "+(i===s?"":'data-function-name="'+t+'" ')+'value="'+s+'">'+i+"</option>");c.$table.find("thead").find("select."+b.filter+'[data-column="'+l+'"]').append(e),(n="function"==typeof(i=d.filter_selectSource)||E.getColumnData(r,i,l))&&I.buildSelect(c.table,l,"",!0,a.hasClass(d.filter_onlyAvail))}I.buildDefault(r,!0),I.bindSearch(r,c.$table.find("."+b.filter),!0),d.filter_external&&I.bindSearch(r,d.filter_external),d.filter_hideFilters&&I.hideFilters(c),c.showProcessing&&(i="filterStart filterEnd ".split(" ").join(c.namespace+"filter-sp "),c.$table.unbind(i.replace(E.regex.spaces," ")).bind(i,function(e,t){a=t?c.$table.find("."+b.header).filter("[data-column]").filter(function(){return""!==t[A(this).data("column")]}):"",E.isProcessing(r,"filterStart"===e.type,t?a:"")})),c.filteredRows=c.totalRows,i="tablesorter-initialized pagerBeforeInitialized ".split(" ").join(c.namespace+"filter "),c.$table.unbind(i.replace(E.regex.spaces," ")).bind(i,function(){I.completeInit(this)}),c.pager&&c.pager.initialized&&!d.filter_initialized?(c.$table.triggerHandler("filterFomatterUpdate"),setTimeout(function(){I.filterInitComplete(c)},100)):d.filter_initialized||I.completeInit(r)},completeInit:function(e){var t=e.config,r=t.widgetOptions,i=I.setDefaults(e,t,r)||[];i.length&&(t.delayInit&&""===i.join("")||E.setFilters(e,i,!0)),t.$table.triggerHandler("filterFomatterUpdate"),setTimeout(function(){r.filter_initialized||I.filterInitComplete(t)},100)},formatterUpdated:function(e,t){var r=e&&e.closest("table"),i=r.length&&r[0].config,a=i&&i.widgetOptions;a&&!a.filter_initialized&&(a.filter_formatterInit[t]=1)},filterInitComplete:function(e){var t,r,i=e.widgetOptions,a=0,l=function(){i.filter_initialized=!0,e.lastSearch=e.$table.data("lastSearch"),e.$table.triggerHandler("filterInit",e),I.findRows(e.table,e.lastSearch||[]),E.debug(e,"filter")&&console.log("Filter >> Widget initialized")};if(A.isEmptyObject(i.filter_formatter))l();else{for(r=i.filter_formatterInit.length,t=0;t<r;t++)1===i.filter_formatterInit[t]&&a++;clearTimeout(i.filter_initTimer),i.filter_initialized||a!==i.filter_formatterCount?i.filter_initialized||(i.filter_initTimer=setTimeout(function(){l()},500)):l()}},processFilters:function(e,t){var r,i=[],a=t?encodeURIComponent:decodeURIComponent,l=e.length;for(r=0;r<l;r++)e[r]&&(i[r]=a(e[r]));return i},setDefaults:function(e,t,r){var i,a,l,s,n,o=E.getFilters(e)||[];if(r.filter_saveFilters&&E.storage&&(a=E.storage(e,"tablesorter-filters")||[],(i=A.isArray(a))&&""===a.join("")||!i||(o=I.processFilters(a))),""===o.join(""))for(n=t.$headers.add(r.filter_$externalFilters).filter("["+r.filter_defaultAttrib+"]"),l=0;l<=t.columns;l++)s=l===t.columns?"all":l,o[l]=n.filter('[data-column="'+s+'"]').attr(r.filter_defaultAttrib)||o[l]||"";return t.$table.data("lastSearch",o),o},parseFilter:function(e,t,r,i){return i||r.parsed[r.index]?e.parsers[r.index].format(t,e.table,[],r.index):t},buildRow:function(e,t,r){var i,a,l,s,n,o,c,d,f,h=r.filter_cellFilter,u=t.columns,p=A.isArray(h),g='<tr role="search" class="'+b.filterRow+" "+t.cssIgnoreRow+'">';for(l=0;l<u;l++)t.$headerIndexed[l].length&&(g+=1<(f=t.$headerIndexed[l]&&t.$headerIndexed[l][0].colSpan||0)?'<td data-column="'+l+"-"+(l+f-1)+'" colspan="'+f+'"':'<td data-column="'+l+'"',g+=p?h[l]?' class="'+h[l]+'"':"":""!==h?' class="'+h+'"':"",g+="></td>");for(t.$filters=A(g+="</tr>").appendTo(t.$table.children("thead").eq(0)).children("td"),l=0;l<u;l++)o=!1,(s=t.$headerIndexed[l])&&s.length&&(i=I.getColumnElm(t,t.$filters,l),d=E.getColumnData(e,r.filter_functions,l),n=r.filter_functions&&d&&"function"!=typeof d||s.hasClass("filter-select"),a=E.getColumnData(e,t.headers,l),o="false"===E.getData(s[0],a,"filter")||"false"===E.getData(s[0],a,"parser"),n?g=A("<select>").appendTo(i):((d=E.getColumnData(e,r.filter_formatter,l))?(r.filter_formatterCount++,(g=d(i,l))&&0===g.length&&(g=i.children("input")),g&&(0===g.parent().length||g.parent().length&&g.parent()[0]!==i[0])&&i.append(g)):g=A('<input type="search">').appendTo(i),g&&(f=s.data("placeholder")||s.attr("data-placeholder")||r.filter_placeholder.search||"",g.attr("placeholder",f))),g&&(c=(A.isArray(r.filter_cssFilter)?void 0!==r.filter_cssFilter[l]&&r.filter_cssFilter[l]||"":r.filter_cssFilter)||"",g.addClass(b.filter+" "+c),(f=(c=r.filter_filterLabel).match(/{{([^}]+?)}}/g))||(f=["{{label}}"]),A.each(f,function(e,t){var r=new RegExp(t,"g"),i=s.attr("data-"+t.replace(/{{|}}/g,"")),a=void 0===i?s.text():i;c=c.replace(r,A.trim(a))}),g.attr({"data-column":i.attr("data-column"),"aria-label":c}),o&&(g.attr("placeholder","").addClass(b.filterDisabled)[0].disabled=!0)))},bindSearch:function(a,e,t){if(a=A(a)[0],(e=A(e)).length){var r,l=a.config,s=l.widgetOptions,i=l.namespace+"filter",n=s.filter_$externalFilters;!0!==t&&(r=s.filter_anyColumnSelector+","+s.filter_multipleColumnSelector,s.filter_$anyMatch=e.filter(r),n&&n.length?s.filter_$externalFilters=s.filter_$externalFilters.add(e):s.filter_$externalFilters=e,E.setFilters(a,l.$table.data("lastSearch")||[],!1===t)),r="keypress keyup keydown search change input ".split(" ").join(i+" "),e.attr("data-lastSearchTime",(new Date).getTime()).unbind(r.replace(E.regex.spaces," ")).bind("keydown"+i,function(e){if(e.which===o.escape&&!a.config.widgetOptions.filter_resetOnEsc)return!1}).bind("keyup"+i,function(e){s=a.config.widgetOptions;var t=parseInt(A(this).attr("data-column"),10),r="boolean"==typeof s.filter_liveSearch?s.filter_liveSearch:E.getColumnData(a,s.filter_liveSearch,t);if(void 0===r&&(r=s.filter_liveSearch.fallback||!1),A(this).attr("data-lastSearchTime",(new Date).getTime()),e.which===o.escape)this.value=s.filter_resetOnEsc?"":l.lastSearch[t];else{if(""!==this.value&&("number"==typeof r&&this.value.length<r||e.which!==o.enter&&e.which!==o.backSpace&&(e.which<o.space||e.which>=o.left&&e.which<=o.down)))return;if(!1===r&&""!==this.value&&e.which!==o.enter)return}I.searching(a,!0,!0,t)}).bind("search change keypress input blur ".split(" ").join(i+" "),function(e){var t=parseInt(A(this).attr("data-column"),10),r=e.type,i="boolean"==typeof s.filter_liveSearch?s.filter_liveSearch:E.getColumnData(a,s.filter_liveSearch,t);!a.config.widgetOptions.filter_initialized||e.which!==o.enter&&"search"!==r&&"blur"!==r&&("change"!==r&&"input"!==r||!0!==i&&(!0===i||"INPUT"===e.target.nodeName)||this.value===l.lastSearch[t])||(e.preventDefault(),A(this).attr("data-lastSearchTime",(new Date).getTime()),I.searching(a,"keypress"!==r,!0,t))})}},searching:function(e,t,r,i){var a,l=e.config.widgetOptions;void 0===i?a=!1:void 0===(a="boolean"==typeof l.filter_liveSearch?l.filter_liveSearch:E.getColumnData(e,l.filter_liveSearch,i))&&(a=l.filter_liveSearch.fallback||!1),clearTimeout(l.filter_searchTimer),void 0===t||!0===t?l.filter_searchTimer=setTimeout(function(){I.checkFilters(e,t,r)},a?l.filter_searchDelay:10):I.checkFilters(e,t,r)},equalFilters:function(e,t,r){var i,a=[],l=[],s=e.columns+1;for(t=A.isArray(t)?t:[],r=A.isArray(r)?r:[],i=0;i<s;i++)a[i]=t[i]||"",l[i]=r[i]||"";return a.join(",")===l.join(",")},checkFilters:function(e,t,r){var i=e.config,a=i.widgetOptions,l=A.isArray(t),s=l?t:E.getFilters(e,!0),n=s||[];if(A.isEmptyObject(i.cache))i.delayInit&&(!i.pager||i.pager&&i.pager.initialized)&&E.updateCache(i,function(){I.checkFilters(e,!1,r)});else if(l&&(E.setFilters(e,s,!1,!0!==r),a.filter_initialized||(i.lastSearch=[],i.lastCombinedFilter="")),a.filter_hideFilters&&i.$table.find("."+b.filterRow).triggerHandler(I.hideFiltersCheck(i)?"mouseleave":"mouseenter"),!I.equalFilters(i,i.lastSearch,n)||!1===t){if(!1===t&&(i.lastCombinedFilter="",i.lastSearch=[]),s=s||[],s=Array.prototype.map?s.map(String):s.join("�").split("�"),a.filter_initialized&&i.$table.triggerHandler("filterStart",[s]),!i.showProcessing)return I.findRows(e,s,n),!1;setTimeout(function(){return I.findRows(e,s,n),!1},30)}},hideFiltersCheck:function(e){if("function"==typeof e.widgetOptions.filter_hideFilters){var t=e.widgetOptions.filter_hideFilters(e);if("boolean"==typeof t)return t}return""===E.getFilters(e.$table).join("")},hideFilters:function(i,e){var a;(e||i.$table).find("."+b.filterRow).addClass(b.filterRowHide).bind("mouseenter mouseleave",function(e){var t=e,r=A(this);clearTimeout(a),a=setTimeout(function(){/enter|over/.test(t.type)?r.removeClass(b.filterRowHide):A(document.activeElement).closest("tr")[0]!==r[0]&&r.toggleClass(b.filterRowHide,I.hideFiltersCheck(i))},200)}).find("input, select").bind("focus blur",function(e){var t=e,r=A(this).closest("tr");clearTimeout(a),a=setTimeout(function(){clearTimeout(a),r.toggleClass(b.filterRowHide,I.hideFiltersCheck(i)&&"focus"!==t.type)},200)})},defaultFilter:function(e,t){if(""===e)return e;var r=O.iQuery,i=t.match(O.igQuery).length,a=1<i?A.trim(e).split(/\s/):[A.trim(e)],l=a.length-1,s=0,n=t;for(l<1&&1<i&&(a[1]=a[0]);r.test(n);)n=n.replace(r,a[s++]||""),r.test(n)&&s<l&&""!==(a[s]||"")&&(n=t.replace(r,n));return n},getLatestSearch:function(e){return e?e.sort(function(e,t){return A(t).attr("data-lastSearchTime")-A(e).attr("data-lastSearchTime")}):e||A()},findRange:function(e,t,r){var i,a,l,s,n,o,c,d,f,h=[];if(/^[0-9]+$/.test(t))return[parseInt(t,10)];if(!r&&/-/.test(t))for(f=(a=t.match(/(\d+)\s*-\s*(\d+)/g))?a.length:0,d=0;d<f;d++){for(l=a[d].split(/\s*-\s*/),s=parseInt(l[0],10)||0,(n=parseInt(l[1],10)||e.columns-1)<s&&(i=s,s=n,n=i),n>=e.columns&&(n=e.columns-1);s<=n;s++)h[h.length]=s;t=t.replace(a[d],"")}if(!r&&/,/.test(t))for(f=(o=t.split(/\s*,\s*/)).length,c=0;c<f;c++)""!==o[c]&&(d=parseInt(o[c],10))<e.columns&&(h[h.length]=d);if(!h.length)for(d=0;d<e.columns;d++)h[h.length]=d;return h},getColumnElm:function(t,e,r){return e.filter(function(){var e=I.findRange(t,A(this).attr("data-column"));return-1<A.inArray(r,e)})},multipleColumns:function(e,t){var r=e.widgetOptions,i=r.filter_initialized||!t.filter(r.filter_anyColumnSelector).length,a=A.trim(I.getLatestSearch(t).attr("data-column")||"");return I.findRange(e,a,!i)},processTypes:function(e,t,r){var i,a=null,l=null;for(i in I.types)A.inArray(i,r.excludeMatch)<0&&null===l&&null!==(l=I.types[i](e,t,r))&&(t.matchedOn=i,a=l);return a},matchType:function(e,t){var r=e.widgetOptions,i=e.$headerIndexed[t];return!i.hasClass("filter-exact")&&(!!i.hasClass("filter-match")||(r.filter_columnFilters?i=e.$filters.find("."+b.filter).add(r.filter_$externalFilters).filter('[data-column="'+t+'"]'):r.filter_$externalFilters&&(i=r.filter_$externalFilters.filter('[data-column="'+t+'"]')),!!i.length&&"match"===e.widgetOptions.filter_matchType[(i[0].nodeName||"").toLowerCase()]))},processRow:function(t,r,e){var i,a,l,s,n,o=t.widgetOptions,c=!0,d=o.filter_$anyMatch&&o.filter_$anyMatch.length,f=o.filter_$anyMatch&&o.filter_$anyMatch.length?I.multipleColumns(t,o.filter_$anyMatch):[];if(r.$cells=r.$row.children(),r.matchedOn=null,r.anyMatchFlag&&1<f.length||r.anyMatchFilter&&!d){if(r.anyMatch=!0,r.isMatch=!0,r.rowArray=r.$cells.map(function(e){if(-1<A.inArray(e,f)||r.anyMatchFilter&&!d)return r.parsed[e]?n=r.cacheArray[e]:(n=r.rawArray[e],n=A.trim(o.filter_ignoreCase?n.toLowerCase():n),t.sortLocaleCompare&&(n=E.replaceAccents(n))),n}).get(),r.filter=r.anyMatchFilter,r.iFilter=r.iAnyMatchFilter,r.exact=r.rowArray.join(" "),r.iExact=o.filter_ignoreCase?r.exact.toLowerCase():r.exact,r.cache=r.cacheArray.slice(0,-1).join(" "),e.excludeMatch=e.noAnyMatch,null!==(a=I.processTypes(t,r,e)))c=a;else if(o.filter_startsWith)for(c=!1,f=Math.min(t.columns,r.rowArray.length);!c&&0<f;)f--,c=c||0===r.rowArray[f].indexOf(r.iFilter);else c=0<=(r.iExact+r.childRowText).indexOf(r.iFilter);if(r.anyMatch=!1,r.filters.join("")===r.filter)return c}for(f=0;f<t.columns;f++)r.filter=r.filters[f],r.index=f,e.excludeMatch=e.excludeFilter[f],r.filter&&(r.cache=r.cacheArray[f],i=r.parsed[f]?r.cache:r.rawArray[f]||"",r.exact=t.sortLocaleCompare?E.replaceAccents(i):i,r.iExact=!O.type.test(typeof r.exact)&&o.filter_ignoreCase?r.exact.toLowerCase():r.exact,r.isMatch=I.matchType(t,f),i=c,s=o.filter_columnFilters&&t.$filters.add(o.filter_$externalFilters).filter('[data-column="'+f+'"]').find("select option:selected").attr("data-function-name")||"",t.sortLocaleCompare&&(r.filter=E.replaceAccents(r.filter)),o.filter_defaultFilter&&O.iQuery.test(e.defaultColFilter[f])&&(r.filter=I.defaultFilter(r.filter,e.defaultColFilter[f])),r.iFilter=o.filter_ignoreCase?(r.filter||"").toLowerCase():r.filter,a=null,(l=e.functions[f])&&("function"==typeof l?a=l(r.exact,r.cache,r.filter,f,r.$row,t,r):"function"==typeof l[s||r.filter]&&(a=l[n=s||r.filter](r.exact,r.cache,r.filter,f,r.$row,t,r))),c=!!(i=null===a?(a=I.processTypes(t,r,e),n=!0===l&&("and"===r.matchedOn||"or"===r.matchedOn),null===a||n?!0===l?r.isMatch?0<=(""+r.iExact).search(r.iFilter):r.filter===r.exact:(n=(r.iExact+r.childRowText).indexOf(I.parseFilter(t,r.iFilter,r)),!o.filter_startsWith&&0<=n||o.filter_startsWith&&0===n):a):a)&&c);return c},findRows:function(e,r,t){if(!I.equalFilters(e.config,e.config.lastSearch,t)&&e.config.widgetOptions.filter_initialized){var i,a,l,s,n,o,c,d,f,h,u,p,g,m,b,y,_,v,w,x,S,C,z,$=A.extend([],r),F=e.config,R=F.widgetOptions,T=E.debug(F,"filter"),k={anyMatch:!1,filters:r,filter_regexCache:[]},H={noAnyMatch:["range","operators"],functions:[],excludeFilter:[],defaultColFilter:[],defaultAnyFilter:E.getColumnData(e,R.filter_defaultFilter,F.columns,!0)||""};for(k.parsed=[],f=0;f<F.columns;f++)k.parsed[f]=R.filter_useParsedData||F.parsers&&F.parsers[f]&&F.parsers[f].parsed||E.getData&&"parsed"===E.getData(F.$headerIndexed[f],E.getColumnData(e,F.headers,f),"filter")||F.$headerIndexed[f].hasClass("filter-parsed"),H.functions[f]=E.getColumnData(e,R.filter_functions,f)||F.$headerIndexed[f].hasClass("filter-select"),H.defaultColFilter[f]=E.getColumnData(e,R.filter_defaultFilter,f)||"",H.excludeFilter[f]=(E.getColumnData(e,R.filter_excludeFilter,f,!0)||"").split(/\s+/);for(T&&(console.log("Filter >> Starting filter widget search",r),m=new Date),F.filteredRows=0,t=$||[],c=F.totalRows=0;c<F.$tbodies.length;c++){if(d=E.processTbody(e,F.$tbodies.eq(c),!0),f=F.columns,a=F.cache[c].normalized,s=A(A.map(a,function(e){return e[f].$row.get()})),""===t.join("")||R.filter_serversideFiltering)s.removeClass(R.filter_filteredRow).not("."+F.cssChildRow).css("display","");else{if(i=(s=s.not("."+F.cssChildRow)).length,(R.filter_$anyMatch&&R.filter_$anyMatch.length||void 0!==r[F.columns])&&(k.anyMatchFlag=!0,k.anyMatchFilter=""+(r[F.columns]||R.filter_$anyMatch&&I.getLatestSearch(R.filter_$anyMatch).val()||""),R.filter_columnAnyMatch)){for(w=k.anyMatchFilter.split(O.andSplit),x=!1,y=0;y<w.length;y++)1<(S=w[y].split(":")).length&&(isNaN(S[0])?A.each(F.headerContent,function(e,t){-1<t.toLowerCase().indexOf(S[0])&&(r[C=e]=S[1])}):C=parseInt(S[0],10)-1,0<=C&&C<F.columns&&(r[C]=S[1],w.splice(y,1),y--,x=!0));x&&(k.anyMatchFilter=w.join(" && "))}if(v=R.filter_searchFiltered,u=F.lastSearch||F.$table.data("lastSearch")||[],v)for(y=0;y<f+1;y++)b=r[y]||"",v||(y=f),v=v&&u.length&&0===b.indexOf(u[y]||"")&&!O.alreadyFiltered.test(b)&&!O.exactTest.test(b)&&!(O.isNeg1.test(b)||O.isNeg2.test(b))&&!(""!==b&&F.$filters&&F.$filters.filter('[data-column="'+y+'"]').find("select").length&&!I.matchType(F,y));for(_=s.not("."+R.filter_filteredRow).length,v&&0===_&&(v=!1),T&&console.log("Filter >> Searching through "+(v&&_<i?_:"all")+" rows"),k.anyMatchFlag&&(F.sortLocaleCompare&&(k.anyMatchFilter=E.replaceAccents(k.anyMatchFilter)),R.filter_defaultFilter&&O.iQuery.test(H.defaultAnyFilter)&&(k.anyMatchFilter=I.defaultFilter(k.anyMatchFilter,H.defaultAnyFilter),v=!1),k.iAnyMatchFilter=R.filter_ignoreCase&&F.ignoreCase?k.anyMatchFilter.toLowerCase():k.anyMatchFilter),o=0;o<i;o++)if(z=s[o].className,!(o&&O.child.test(z)||v&&O.filtered.test(z))){if(k.$row=s.eq(o),k.rowIndex=o,k.cacheArray=a[o],l=k.cacheArray[F.columns],k.rawArray=l.raw,k.childRowText="",!R.filter_childByColumn){for(z="",h=l.child,y=0;y<h.length;y++)z+=" "+h[y].join(" ")||"";k.childRowText=R.filter_childRows?R.filter_ignoreCase?z.toLowerCase():z:""}if(p=!1,g=I.processRow(F,k,H),n=l.$row,b=!!g,h=l.$row.filter(":gt(0)"),R.filter_childRows&&h.length){if(R.filter_childByColumn)for(R.filter_childWithSibs||(h.addClass(R.filter_filteredRow),n=n.eq(0)),y=0;y<h.length;y++)k.$row=h.eq(y),k.cacheArray=l.child[y],k.rawArray=k.cacheArray,b=I.processRow(F,k,H),p=p||b,!R.filter_childWithSibs&&b&&h.eq(y).removeClass(R.filter_filteredRow);p=p||g}else p=b;n.toggleClass(R.filter_filteredRow,!p)[0].display=p?"":"none"}}F.filteredRows+=s.not("."+R.filter_filteredRow).length,F.totalRows+=s.length,E.processTbody(e,d,!1)}F.lastCombinedFilter=$.join(""),F.lastSearch=$,F.$table.data("lastSearch",$),R.filter_saveFilters&&E.storage&&E.storage(e,"tablesorter-filters",I.processFilters($,!0)),T&&console.log("Filter >> Completed search"+E.benchmark(m)),R.filter_initialized&&(F.$table.triggerHandler("filterBeforeEnd",F),F.$table.triggerHandler("filterEnd",F)),setTimeout(function(){E.applyWidget(F.table)},0)}},getOptionSource:function(e,t,r){var i=(e=A(e)[0]).config,a=!1,l=i.widgetOptions.filter_selectSource,s=i.$table.data("lastSearch")||[],n="function"==typeof l||E.getColumnData(e,l,t);if(r&&""!==s[t]&&(r=!1),!0===n)a=l(e,t,r);else{if(n instanceof A||"string"===A.type(n)&&0<=n.indexOf("</option>"))return n;if(A.isArray(n))a=n;else if("object"===A.type(l)&&n&&null===(a=n(e,t,r)))return null}return!1===a&&(a=I.getOptions(e,t,r)),I.processOptions(e,t,a)},processOptions:function(a,l,r){if(!A.isArray(r))return!1;var s,e,t,i,n,o,c=(a=A(a)[0]).config,d=null!=l&&0<=l&&l<c.columns,f=!!d&&c.$headerIndexed[l].hasClass("filter-select-sort-desc"),h=[];if(r=A.grep(r,function(e,t){return!!e.text||A.inArray(e,r)===t}),d&&c.$headerIndexed[l].hasClass("filter-select-nosort"))return r;for(i=r.length,t=0;t<i;t++)o=(e=r[t]).text?e.text:e,n=(d&&c.parsers&&c.parsers.length&&c.parsers[l].format(o,a,[],l)||o).toString(),n=c.widgetOptions.filter_ignoreCase?n.toLowerCase():n,e.text?(e.parsed=n,h[h.length]=e):h[h.length]={text:e,parsed:n};for(s=c.textSorter||"",h.sort(function(e,t){var r=f?t.parsed:e.parsed,i=f?e.parsed:t.parsed;return d&&"function"==typeof s?s(r,i,!0,l,a):d&&"object"==typeof s&&s.hasOwnProperty(l)?s[l](r,i,!0,l,a):!E.sortNatural||E.sortNatural(r,i)}),r=[],i=h.length,t=0;t<i;t++)r[r.length]=h[t];return r},getOptions:function(e,t,r){var i,a,l,s,n,o,c,d,f=(e=A(e)[0]).config,h=f.widgetOptions,u=[];for(a=0;a<f.$tbodies.length;a++)for(n=f.cache[a],l=f.cache[a].normalized.length,i=0;i<l;i++)if(s=n.row?n.row[i]:n.normalized[i][f.columns].$row[0],!r||!s.className.match(h.filter_filteredRow))if(h.filter_useParsedData||f.parsers[t].parsed||f.$headerIndexed[t].hasClass("filter-parsed")){if(u[u.length]=""+n.normalized[i][t],h.filter_childRows&&h.filter_childByColumn)for(d=n.normalized[i][f.columns].$row.length-1,o=0;o<d;o++)u[u.length]=""+n.normalized[i][f.columns].child[o][t]}else if(u[u.length]=n.normalized[i][f.columns].raw[t],h.filter_childRows&&h.filter_childByColumn)for(d=n.normalized[i][f.columns].$row.length,o=1;o<d;o++)c=n.normalized[i][f.columns].$row.eq(o).children().eq(t),u[u.length]=""+E.getElementText(f,c,t);return u},buildSelect:function(e,t,r,i,a){if(e=A(e)[0],t=parseInt(t,10),e.config.cache&&!A.isEmptyObject(e.config.cache)){var l,s,n,o,c,d,f,h=e.config,u=h.widgetOptions,p=h.$headerIndexed[t],g='<option value="">'+(p.data("placeholder")||p.attr("data-placeholder")||u.filter_placeholder.select||"")+"</option>",m=h.$table.find("thead").find("select."+b.filter+'[data-column="'+t+'"]').val();if(void 0!==r&&""!==r||null!==(r=I.getOptionSource(e,t,a))){if(A.isArray(r)){for(l=0;l<r.length;l++)if((f=r[l]).text){for(s in f["data-function-name"]=void 0===f.value?f.text:f.value,g+="<option",f)f.hasOwnProperty(s)&&"text"!==s&&(g+=" "+s+'="'+f[s].replace(O.quote,"&quot;")+'"');f.value||(g+=' value="'+f.text.replace(O.quote,"&quot;")+'"'),g+=">"+f.text.replace(O.quote,"&quot;")+"</option>"}else""+f!="[object Object]"&&(0<=(s=n=f=(""+f).replace(O.quote,"&quot;")).indexOf(u.filter_selectSourceSeparator)&&(s=(o=n.split(u.filter_selectSourceSeparator))[0],n=o[1]),g+=""!==f?"<option "+(s===n?"":'data-function-name="'+f+'" ')+'value="'+s+'">'+n+"</option>":"");r=[]}c=(h.$filters?h.$filters:h.$table.children("thead")).find("."+b.filter),u.filter_$externalFilters&&(c=c&&c.length?c.add(u.filter_$externalFilters):u.filter_$externalFilters),(d=c.filter('select[data-column="'+t+'"]')).length&&(d[i?"html":"append"](g),A.isArray(r)||d.append(r).val(m),d.val(m))}}},buildDefault:function(e,t){var r,i,a,l=e.config,s=l.widgetOptions,n=l.columns;for(r=0;r<n;r++)a=!((i=l.$headerIndexed[r]).hasClass("filter-false")||i.hasClass("parser-false")),(i.hasClass("filter-select")||!0===E.getColumnData(e,s.filter_functions,r))&&a&&I.buildSelect(e,r,"",t,i.hasClass(s.filter_onlyAvail))}}).regex,E.getFilters=function(e,t,r,i){var a,l,s,n,o=[],c=e?A(e)[0].config:"",d=c?c.widgetOptions:"";if(!0!==t&&d&&!d.filter_columnFilters||A.isArray(r)&&I.equalFilters(c,r,c.lastSearch))return A(e).data("lastSearch")||[];if(c&&(c.$filters&&(l=c.$filters.find("."+b.filter)),d.filter_$externalFilters&&(l=l&&l.length?l.add(d.filter_$externalFilters):d.filter_$externalFilters),l&&l.length))for(o=r||[],a=0;a<c.columns+1;a++)n=a===c.columns?d.filter_anyColumnSelector+","+d.filter_multipleColumnSelector:'[data-column="'+a+'"]',(s=l.filter(n)).length&&(s=I.getLatestSearch(s),A.isArray(r)?(i&&1<s.length&&(s=s.slice(1)),a===c.columns&&(s=(n=s.filter(d.filter_anyColumnSelector)).length?n:s),s.val(r[a]).trigger("change"+c.namespace)):(o[a]=s.val()||"",a===c.columns?s.slice(1).filter('[data-column*="'+s.attr("data-column")+'"]').val(o[a]):s.slice(1).val(o[a])),a===c.columns&&s.length&&(d.filter_$anyMatch=s));return o},E.setFilters=function(e,t,r,i){var a=e?A(e)[0].config:"",l=E.getFilters(e,!0,t,i);return void 0===r&&(r=!0),a&&r&&(a.lastCombinedFilter=null,a.lastSearch=[],I.searching(a.table,t,i),a.$table.triggerHandler("filterFomatterUpdate")),0!==l.length}}(e),function(z,$){"use strict";var F=z.tablesorter||{};function R(e,t){var r=isNaN(t.stickyHeaders_offset)?z(t.stickyHeaders_offset):[];return r.length?r.height()||0:parseInt(t.stickyHeaders_offset,10)||0}z.extend(F.css,{sticky:"tablesorter-stickyHeader",stickyVis:"tablesorter-sticky-visible",stickyHide:"tablesorter-sticky-hidden",stickyWrap:"tablesorter-sticky-wrapper"}),F.addHeaderResizeEvent=function(e,t,r){if((e=z(e)[0]).config){var i=z.extend({},{timer:250},r),o=e.config,c=o.widgetOptions,a=function(e){var t,r,i,a,l,s,n=o.$headers.length;for(c.resize_flag=!0,r=[],t=0;t<n;t++)a=(i=o.$headers.eq(t)).data("savedSizes")||[0,0],l=i[0].offsetWidth,s=i[0].offsetHeight,l===a[0]&&s===a[1]||(i.data("savedSizes",[l,s]),r.push(i[0]));r.length&&!1!==e&&o.$table.triggerHandler("resize",[r]),c.resize_flag=!1};if(clearInterval(c.resize_timer),t)return c.resize_flag=!1;a(!1),c.resize_timer=setInterval(function(){c.resize_flag||a()},i.timer)}},F.addWidget({id:"stickyHeaders",priority:54,options:{stickyHeaders:"",stickyHeaders_appendTo:null,stickyHeaders_attachTo:null,stickyHeaders_xScroll:null,stickyHeaders_yScroll:null,stickyHeaders_offset:0,stickyHeaders_filteredToTop:!0,stickyHeaders_cloneId:"-sticky",stickyHeaders_addResizeEvent:!0,stickyHeaders_includeCaption:!0,stickyHeaders_zIndex:2},format:function(e,r,p){if(!(r.$table.hasClass("hasStickyHeaders")||0<=z.inArray("filter",r.widgets)&&!r.$table.hasClass("hasFilters"))){var t,i,a,l,g=r.$table,m=z(p.stickyHeaders_attachTo||p.stickyHeaders_appendTo),s=r.namespace+"stickyheaders ",b=z(p.stickyHeaders_yScroll||p.stickyHeaders_attachTo||$),n=z(p.stickyHeaders_xScroll||p.stickyHeaders_attachTo||$),o=g.children("thead:first").children("tr").not(".sticky-false").children(),y=g.children("tfoot"),c=R(0,p),_=g.parent().closest("."+F.css.table).hasClass("hasStickyHeaders")?g.parent().closest("table.tablesorter")[0].config.widgetOptions.$sticky.parent():[],v=_.length?_.height():0,d=p.$sticky=g.clone().addClass("containsStickyHeaders "+F.css.sticky+" "+p.stickyHeaders+" "+r.namespace.slice(1)+"_extra_table").wrap('<div class="'+F.css.stickyWrap+'">'),w=d.parent().addClass(F.css.stickyHide).css({position:m.length?"absolute":"fixed",padding:parseInt(d.parent().parent().css("padding-left"),10),top:c+v,left:0,visibility:"hidden",zIndex:p.stickyHeaders_zIndex||2}),f=d.children("thead:first"),x="",h=function(e,t){var r,i,a,l,s,n=e.filter(":visible"),o=n.length;for(r=0;r<o;r++)l=t.filter(":visible").eq(r),i="border-box"===(s=n.eq(r)).css("box-sizing")?s.outerWidth():"collapse"===l.css("border-collapse")?$.getComputedStyle?parseFloat($.getComputedStyle(s[0],null).width):(a=parseFloat(s.css("border-width")),s.outerWidth()-parseFloat(s.css("padding-left"))-parseFloat(s.css("padding-right"))-a):s.width(),l.css({width:i,"min-width":i,"max-width":i})},S=function(e){return!1===e&&_.length?g.position().left:m.length?parseInt(m.css("padding-left"),10)||0:g.offset().left-parseInt(g.css("margin-left"),10)-z($).scrollLeft()},C=function(){w.css({left:S(),width:g.outerWidth()}),h(g,d),h(o,l)},u=function(e){if(g.is(":visible")){v=_.length?_.offset().top-b.scrollTop()+_.height():0;var t,r=g.offset(),i=R(0,p),a=z.isWindow(b[0]),l=a?b.scrollTop():_.length?parseInt(_[0].style.top,10):b.offset().top,s=m.length?l:b.scrollTop(),n=p.stickyHeaders_includeCaption?0:g.children("caption").height()||0,o=s+i+v-n,c=g.height()-(w.height()+(y.height()||0))-n,d=o>r.top&&o<r.top+c?"visible":"hidden",f="visible"===d?F.css.stickyVis:F.css.stickyHide,h=!w.hasClass(f),u={visibility:d};m.length&&(h=!0,u.top=a?o-m.offset().top:m.scrollTop()),(t=S(a))!==parseInt(w.css("left"),10)&&(h=!0,u.left=t),u.top=(u.top||0)+(!a&&_.length?_.height():i+v),h&&w.removeClass(F.css.stickyVis+" "+F.css.stickyHide).addClass(f).css(u),(d!==x||e)&&(C(),x=d)}};if(m.length&&!m.css("position")&&m.css("position","relative"),d.attr("id")&&(d[0].id+=p.stickyHeaders_cloneId),d.find("> thead:gt(0), tr.sticky-false").hide(),d.find("> tbody, > tfoot").remove(),d.find("caption").toggle(p.stickyHeaders_includeCaption),l=f.children().children(),d.css({height:0,width:0,margin:0}),l.find("."+F.css.resizer).remove(),g.addClass("hasStickyHeaders").bind("pagerComplete"+s,function(){C()}),F.bindEvents(e,f.children().children("."+F.css.header)),p.stickyHeaders_appendTo?z(p.stickyHeaders_appendTo).append(w):g.after(w),r.onRenderHeader)for(i=(a=f.children("tr").children()).length,t=0;t<i;t++)r.onRenderHeader.apply(a.eq(t),[t,r,d]);n.add(b).unbind("scroll resize ".split(" ").join(s).replace(/\s+/g," ")).bind("scroll resize ".split(" ").join(s),function(e){u("resize"===e.type)}),r.$table.unbind("stickyHeadersUpdate"+s).bind("stickyHeadersUpdate"+s,function(){u(!0)}),p.stickyHeaders_addResizeEvent&&F.addHeaderResizeEvent(e),g.hasClass("hasFilters")&&p.filter_columnFilters&&(g.bind("filterEnd"+s,function(){var e=z(document.activeElement).closest("td"),t=e.parent().children().index(e);w.hasClass(F.css.stickyVis)&&p.stickyHeaders_filteredToTop&&($.scrollTo(0,g.position().top),0<=t&&r.$filters&&r.$filters.eq(t).find("a, select, input").filter(":visible").focus())}),F.filter.bindSearch(g,l.find("."+F.css.filter)),p.filter_hideFilters&&F.filter.hideFilters(r,d)),p.stickyHeaders_addResizeEvent&&g.bind("resize"+r.namespace+"stickyheaders",function(){C()}),u(!0),g.triggerHandler("stickyHeadersInit")}},remove:function(e,t,r){var i=t.namespace+"stickyheaders ";t.$table.removeClass("hasStickyHeaders").unbind("pagerComplete resize filterEnd stickyHeadersUpdate ".split(" ").join(i).replace(/\s+/g," ")).next("."+F.css.stickyWrap).remove(),r.$sticky&&r.$sticky.length&&r.$sticky.remove(),z($).add(r.stickyHeaders_xScroll).add(r.stickyHeaders_yScroll).add(r.stickyHeaders_attachTo).unbind("scroll resize ".split(" ").join(i).replace(/\s+/g," ")),F.addHeaderResizeEvent(e,!0)}})}(e,window),function(d,t){"use strict";var f=d.tablesorter||{};d.extend(f.css,{resizableContainer:"tablesorter-resizable-container",resizableHandle:"tablesorter-resizable-handle",resizableNoSelect:"tablesorter-disableSelection",resizableStorage:"tablesorter-resizable"}),d(function(){var e="<style>body."+f.css.resizableNoSelect+" { -ms-user-select: none; -moz-user-select: -moz-none;-khtml-user-select: none; -webkit-user-select: none; user-select: none; }."+f.css.resizableContainer+" { position: relative; height: 1px; }."+f.css.resizableHandle+" { position: absolute; display: inline-block; width: 8px;top: 1px; cursor: ew-resize; z-index: 3; user-select: none; -moz-user-select: none; }</style>";d("head").append(e)}),f.resizable={init:function(e,t){if(!e.$table.hasClass("hasResizable")){e.$table.addClass("hasResizable");var r,i,a,l,s=e.$table,n=s.parent(),o=parseInt(s.css("margin-top"),10),c=t.resizable_vars={useStorage:f.storage&&!1!==t.resizable,$wrap:n,mouseXPosition:0,$target:null,$next:null,overflow:"auto"===n.css("overflow")||"scroll"===n.css("overflow")||"auto"===n.css("overflow-x")||"scroll"===n.css("overflow-x"),storedSizes:[]};for(f.resizableReset(e.table,!0),c.tableWidth=s.width(),c.fullWidth=Math.abs(n.width()-c.tableWidth)<20,c.useStorage&&c.overflow&&(f.storage(e.table,"tablesorter-table-original-css-width",c.tableWidth),l=f.storage(e.table,"tablesorter-table-resized-width")||"auto",f.resizable.setWidth(s,l,!0)),t.resizable_vars.storedSizes=a=(c.useStorage?f.storage(e.table,f.css.resizableStorage):[])||[],f.resizable.setWidths(e,t,a),f.resizable.updateStoredSizes(e,t),t.$resizable_container=d('<div class="'+f.css.resizableContainer+'">').css({top:o}).insertBefore(s),i=0;i<e.columns;i++)r=e.$headerIndexed[i],l=f.getColumnData(e.table,e.headers,i),"false"===f.getData(r,l,"resizable")||d('<div class="'+f.css.resizableHandle+'">').appendTo(t.$resizable_container).attr({"data-column":i,unselectable:"on"}).data("header",r).bind("selectstart",!1);f.resizable.bindings(e,t)}},updateStoredSizes:function(e,t){var r,i,a=e.columns,l=t.resizable_vars;for(l.storedSizes=[],r=0;r<a;r++)i=e.$headerIndexed[r],l.storedSizes[r]=i.is(":visible")?i.width():0},setWidth:function(e,t,r){e.css({width:t,"min-width":r?t:"","max-width":r?t:""})},setWidths:function(e,t,r){var i,a,l=t.resizable_vars,s=d(e.namespace+"_extra_headers"),n=e.$table.children("colgroup").children("col");if((r=r||l.storedSizes||[]).length){for(i=0;i<e.columns;i++)f.resizable.setWidth(e.$headerIndexed[i],r[i],l.overflow),s.length&&(a=s.eq(i).add(n.eq(i)),f.resizable.setWidth(a,r[i],l.overflow));(a=d(e.namespace+"_extra_table")).length&&!f.hasWidget(e.table,"scroller")&&f.resizable.setWidth(a,e.$table.outerWidth(),l.overflow)}},setHandlePosition:function(a,l){var s,n=a.$table.height(),e=l.$resizable_container.children(),o=Math.floor(e.width()/2);f.hasWidget(a.table,"scroller")&&(n=0,a.$table.closest("."+f.css.scrollerWrap).children().each(function(){var e=d(this);n+=e.filter('[style*="height"]').length?e.height():e.children("table").height()})),!l.resizable_includeFooter&&a.$table.children("tfoot").length&&(n-=a.$table.children("tfoot").height()),s=3.3<=parseFloat(d.fn.jquery)?0:a.$table.position().left,e.each(function(){var e=d(this),t=parseInt(e.attr("data-column"),10),r=a.columns-1,i=e.data("header");i&&(!i.is(":visible")||!l.resizable_addLastColumn&&f.resizable.checkVisibleColumns(a,t)?e.hide():(t<r||t===r&&l.resizable_addLastColumn)&&e.css({display:"inline-block",height:n,left:i.position().left-s+i.outerWidth()-o}))})},checkVisibleColumns:function(e,t){var r,i=0;for(r=t+1;r<e.columns;r++)i+=e.$headerIndexed[r].is(":visible")?1:0;return 0===i},toggleTextSelection:function(e,t,r){var i=e.namespace+"tsresize";t.resizable_vars.disabled=r,d("body").toggleClass(f.css.resizableNoSelect,r),r?d("body").attr("unselectable","on").bind("selectstart"+i,!1):d("body").removeAttr("unselectable").unbind("selectstart"+i)},bindings:function(l,s){var e=l.namespace+"tsresize";s.$resizable_container.children().bind("mousedown",function(e){var t,r=s.resizable_vars,i=d(l.namespace+"_extra_headers"),a=d(e.target).data("header");t=parseInt(a.attr("data-column"),10),r.$target=a=a.add(i.filter('[data-column="'+t+'"]')),r.target=t,r.$next=e.shiftKey||s.resizable_targetLast?a.parent().children().not(".resizable-false").filter(":last"):a.nextAll(":not(.resizable-false)").eq(0),t=parseInt(r.$next.attr("data-column"),10),r.$next=r.$next.add(i.filter('[data-column="'+t+'"]')),r.next=t,r.mouseXPosition=e.pageX,f.resizable.updateStoredSizes(l,s),f.resizable.toggleTextSelection(l,s,!0)}),d(document).bind("mousemove"+e,function(e){var t=s.resizable_vars;t.disabled&&0!==t.mouseXPosition&&t.$target&&(s.resizable_throttle?(clearTimeout(t.timer),t.timer=setTimeout(function(){f.resizable.mouseMove(l,s,e)},isNaN(s.resizable_throttle)?5:s.resizable_throttle)):f.resizable.mouseMove(l,s,e))}).bind("mouseup"+e,function(){s.resizable_vars.disabled&&(f.resizable.toggleTextSelection(l,s,!1),f.resizable.stopResize(l,s),f.resizable.setHandlePosition(l,s))}),d(t).bind("resize"+e+" resizeEnd"+e,function(){f.resizable.setHandlePosition(l,s)}),l.$table.bind("columnUpdate pagerComplete resizableUpdate ".split(" ").join(e+" "),function(){f.resizable.setHandlePosition(l,s)}).bind("resizableReset"+e,function(){f.resizableReset(l.table)}).find("thead:first").add(d(l.namespace+"_extra_table").find("thead:first")).bind("contextmenu"+e,function(){var e=0===s.resizable_vars.storedSizes.length;return f.resizableReset(l.table),f.resizable.setHandlePosition(l,s),s.resizable_vars.storedSizes=[],e})},mouseMove:function(e,t,r){if(0!==t.resizable_vars.mouseXPosition&&t.resizable_vars.$target){var i,a=0,l=t.resizable_vars,s=l.$next,n=l.storedSizes[l.target],o=r.pageX-l.mouseXPosition;if(l.overflow){if(0<n+o){for(l.storedSizes[l.target]+=o,f.resizable.setWidth(l.$target,l.storedSizes[l.target],!0),i=0;i<e.columns;i++)a+=l.storedSizes[i];f.resizable.setWidth(e.$table.add(d(e.namespace+"_extra_table")),a)}s.length||(l.$wrap[0].scrollLeft=e.$table.width())}else l.fullWidth?(l.storedSizes[l.target]+=o,l.storedSizes[l.next]-=o):l.storedSizes[l.target]+=o,f.resizable.setWidths(e,t);l.mouseXPosition=r.pageX,e.$table.triggerHandler("stickyHeadersUpdate")}},stopResize:function(e,t){var r=t.resizable_vars;f.resizable.updateStoredSizes(e,t),r.useStorage&&(f.storage(e.table,f.css.resizableStorage,r.storedSizes),f.storage(e.table,"tablesorter-table-resized-width",e.$table.width())),r.mouseXPosition=0,r.$target=r.$next=null,e.$table.triggerHandler("stickyHeadersUpdate"),e.$table.triggerHandler("resizableComplete")}},f.addWidget({id:"resizable",priority:40,options:{resizable:!0,resizable_addLastColumn:!1,resizable_includeFooter:!0,resizable_widths:[],resizable_throttle:!1,resizable_targetLast:!1},init:function(e,t,r,i){f.resizable.init(r,i)},format:function(e,t,r){f.resizable.setHandlePosition(t,r)},remove:function(e,t,r,i){if(r.$resizable_container){var a=t.namespace+"tsresize";t.$table.add(d(t.namespace+"_extra_table")).removeClass("hasResizable").children("thead").unbind("contextmenu"+a),r.$resizable_container.remove(),f.resizable.toggleTextSelection(t,r,!1),f.resizableReset(e,i),d(document).unbind("mousemove"+a+" mouseup"+a)}}}),f.resizableReset=function(l,s){d(l).each(function(){var e,t,r=this.config,i=r&&r.widgetOptions,a=i.resizable_vars;if(l&&r&&r.$headerIndexed.length){for(a.overflow&&a.tableWidth&&(f.resizable.setWidth(r.$table,a.tableWidth,!0),a.useStorage&&f.storage(l,"tablesorter-table-resized-width",a.tableWidth)),e=0;e<r.columns;e++)t=r.$headerIndexed[e],i.resizable_widths&&i.resizable_widths[e]?f.resizable.setWidth(t,i.resizable_widths[e],a.overflow):t.hasClass("resizable-false")||f.resizable.setWidth(t,"",a.overflow);r.$table.triggerHandler("stickyHeadersUpdate"),f.storage&&!s&&f.storage(this,f.css.resizableStorage,[])}})}}(e,window),function(r){"use strict";var c=r.tablesorter||{};function d(e){var t=c.storage(e.table,"tablesorter-savesort");return t&&t.hasOwnProperty("sortList")&&r.isArray(t.sortList)?t.sortList:[]}function f(e,t){return(t||d(e)).join(",")!==e.sortList.join(",")}c.addWidget({id:"saveSort",priority:20,options:{saveSort:!0},init:function(e,t,r,i){t.format(e,r,i,!0)},format:function(t,e,r,i){var a,l=e.$table,s=!1!==r.saveSort,n={sortList:e.sortList},o=c.debug(e,"saveSort");o&&(a=new Date),l.hasClass("hasSaveSort")?s&&t.hasInitialized&&c.storage&&f(e)&&(c.storage(t,"tablesorter-savesort",n),o&&console.log("saveSort >> Saving last sort: "+e.sortList+c.benchmark(a))):(l.addClass("hasSaveSort"),n="",c.storage&&(n=d(e),o&&console.log('saveSort >> Last sort loaded: "'+n+'"'+c.benchmark(a)),l.bind("saveSortReset",function(e){e.stopPropagation(),c.storage(t,"tablesorter-savesort","")})),i&&n&&0<n.length?e.sortList=n:t.hasInitialized&&n&&0<n.length&&f(e,n)&&c.sortOn(e,n))},remove:function(e,t){t.$table.removeClass("hasSaveSort"),c.storage&&c.storage(e,"tablesorter-savesort","")}})}(e),e.tablesorter});return jQuery;}));
