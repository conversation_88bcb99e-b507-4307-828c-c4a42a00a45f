/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/Variants/Bold/All.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXVariants-bold"],{32:[0,0,250,0,0],124:[691,189,340,126,214],160:[0,0,250,0,0],411:[666,0,536,60,526],8243:[586,-12,713,44,669],8244:[586,-12,1032,44,988],8245:[586,-12,394,44,350],8246:[586,-12,713,44,669],8247:[586,-12,1032,44,988],8279:[586,-12,1351,43,1306],8512:[691,0,780,55,725],8592:[451,-55,428,68,428],8593:[680,15,556,80,476],8594:[451,-55,428,0,360],8595:[680,15,556,80,476],8657:[600,15,714,40,674],8659:[600,15,714,40,674],8719:[676,0,734,27,707],8720:[676,0,734,27,707],8721:[676,0,690,39,649],8733:[431,0,750,56,687],8739:[451,19,290,89,201],8772:[543,45,750,68,683],8775:[648,144,750,68,683],8777:[598,64,750,68,683],8800:[687,183,750,68,682],8802:[747,243,750,68,682],8808:[728,293,750,80,670],8809:[728,293,750,80,670],8814:[672,166,750,80,670],8815:[672,166,750,80,670],8816:[742,236,750,80,670],8817:[742,236,750,80,670]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/Variants/Bold/All.js");
