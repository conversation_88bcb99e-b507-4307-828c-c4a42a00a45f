﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.com.ecool.Models.DTO
{

    /// <summary>
    /// Cash 總計
    /// </summary>
    public class SECSharedTotalCashDataViewModel
    {

        /// <summary>
        /// 酷幣總發行量(以前到現在) 
        /// </summary>
        public int? SINACE_Total_CASH_ALL { get; set; }

        /// <summary>
        /// 在校生累積點數
        /// </summary>
        public int? ToTAL_CASH_ALL { get; set; }


        /// <summary>
        /// 尚未兌換點數
        /// </summary>
        public int? ToTAL_CASH_AVAILABLE { get; set; }
    }
}
