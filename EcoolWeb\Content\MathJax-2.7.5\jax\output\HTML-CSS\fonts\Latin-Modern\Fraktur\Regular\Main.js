/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Latin-Modern/Fraktur/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.LatinModernMathJax_Fraktur={directory:"Fraktur/Regular",family:"LatinModernMathJax_Fraktur",testString:"\u00A0\u210C\u2128\u212D\uD835\uDD04\uD835\uDD05\uD835\uDD07\uD835\uDD08\uD835\uDD09\uD835\uDD0A\uD835\uDD0D\uD835\uDD0E\uD835\uDD0F\uD835\uDD10\uD835\uDD11",32:[0,0,332,0,0],160:[0,0,332,0,0],8460:[667,133,720,-8,645],8488:[729,139,602,11,533],8493:[686,24,612,59,613],120068:[697,27,717,22,709],120069:[691,27,904,49,815],120071:[690,27,831,27,746],120072:[686,24,662,86,641],120073:[686,155,611,11,621],120074:[692,25,785,66,711],120077:[686,139,552,-18,522],120078:[681,27,668,16,690],120079:[686,27,666,32,645],120080:[692,27,1049,27,1049],120081:[686,29,832,29,830],120082:[729,27,828,11,746],120083:[692,219,823,6,804],120084:[729,69,828,11,783],120086:[689,27,828,56,756],120087:[703,27,669,24,676],120088:[697,27,645,-26,666],120089:[686,27,831,29,826],120090:[686,28,1046,21,1055],120091:[689,27,719,27,709],120092:[686,219,834,26,741],120094:[471,36,500,65,497],120095:[686,31,513,86,444],120096:[466,29,389,72,359],120097:[612,34,498,13,430],120098:[467,31,400,70,364],120099:[679,238,329,30,324],120100:[470,209,503,16,455],120101:[689,198,521,76,435],120102:[675,21,279,14,268],120103:[673,202,280,-9,196],120104:[686,26,389,24,363],120105:[686,20,279,97,277],120106:[475,26,766,7,757],120107:[475,23,526,18,521],120108:[481,28,488,66,413],120109:[538,214,500,12,430],120110:[480,224,489,59,418],120111:[474,21,389,15,395],120112:[479,30,442,-28,407],120113:[641,21,333,26,349],120114:[474,26,517,8,514],120115:[533,28,511,51,439],120116:[533,28,773,44,693],120117:[473,188,388,10,370],120118:[524,219,498,45,437],120119:[471,215,390,-7,314],120172:[688,31,847,29,827],120173:[685,31,1043,56,963],120174:[677,32,723,71,729],120175:[685,29,981,30,896],120176:[687,29,782,73,733],120177:[684,147,721,17,734],120178:[692,27,927,74,844],120179:[684,127,850,0,753],120180:[683,25,654,31,623],120181:[681,142,652,-8,615],120182:[682,26,789,20,813],120183:[684,28,786,30,764],120184:[686,33,1239,26,1232],120185:[681,33,982,26,968],120186:[726,29,976,11,881],120187:[685,223,977,19,944],120188:[726,82,976,11,917],120189:[689,29,977,19,977],120190:[685,31,978,82,906],120191:[691,30,789,30,798],120192:[689,39,850,16,871],120193:[687,29,981,25,966],120194:[682,30,1235,31,1240],120195:[682,35,849,32,835],120196:[689,214,983,32,879],120197:[718,137,726,17,633],120198:[472,32,602,80,587],120199:[691,32,589,86,504],120200:[473,26,463,87,424],120201:[632,29,588,-1,511],120202:[471,28,471,80,429],120203:[681,242,387,37,387],120204:[473,208,594,16,541],120205:[687,203,615,88,507],120206:[686,26,331,2,327],120207:[683,207,331,-19,238],120208:[683,25,464,33,432],120209:[682,24,336,100,315],120210:[476,31,921,16,900],120211:[474,28,653,3,608],120212:[482,34,609,107,515],120213:[558,208,603,-2,519],120214:[485,212,595,87,515],120215:[473,26,459,12,453],120216:[480,35,522,-24,482],120217:[654,27,393,47,407],120218:[473,35,588,9,604],120219:[546,28,604,56,507],120220:[549,33,917,55,815],120221:[471,188,458,8,449],120222:[559,222,589,60,515],120223:[472,215,461,-8,377]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"LatinModernMathJax_Fraktur"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Fraktur/Regular/Main.js"]);
