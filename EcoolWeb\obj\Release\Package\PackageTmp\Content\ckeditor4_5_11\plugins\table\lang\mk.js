﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'table', 'mk', {
	border: 'Border size', // MISSING
	caption: 'Caption', // MISSING
	cell: {
		menu: 'Cell',
		insertBefore: 'Insert Cell Before',
		insertAfter: 'Insert Cell After',
		deleteCell: 'Delete Cells',
		merge: 'Merge Cells',
		mergeRight: 'Merge Right',
		mergeDown: 'Merge Down',
		splitHorizontal: 'Split Cell Horizontally',
		splitVertical: 'Split Cell Vertically',
		title: 'Cell Properties',
		cellType: 'Cell Type',
		rowSpan: 'Rows Span',
		colSpan: 'Columns Span',
		wordWrap: 'Word Wrap',
		hAlign: 'Horizontal Alignment',
		vAlign: 'Vertical Alignment',
		alignBaseline: 'Baseline',
		bgColor: 'Background Color',
		borderColor: 'Border Color',
		data: 'Data',
		header: 'Header',
		yes: 'Yes',
		no: 'No',
		invalidWidth: 'Cell width must be a number.',
		invalidHeight: 'Cell height must be a number.',
		invalidRowSpan: 'Rows span must be a whole number.',
		invalidColSpan: 'Columns span must be a whole number.',
		chooseColor: 'Choose'
	},
	cellPad: 'Cell padding', // MISSING
	cellSpace: 'Cell spacing', // MISSING
	column: {
		menu: 'Column',
		insertBefore: 'Insert Column Before',
		insertAfter: 'Insert Column After',
		deleteColumn: 'Delete Columns'
	},
	columns: 'Columns',
	deleteTable: 'Delete Table', // MISSING
	headers: 'Headers', // MISSING
	headersBoth: 'Both', // MISSING
	headersColumn: 'First column', // MISSING
	headersNone: 'None',
	headersRow: 'First Row', // MISSING
	invalidBorder: 'Border size must be a number.', // MISSING
	invalidCellPadding: 'Cell padding must be a positive number.', // MISSING
	invalidCellSpacing: 'Cell spacing must be a positive number.', // MISSING
	invalidCols: 'Number of columns must be a number greater than 0.', // MISSING
	invalidHeight: 'Table height must be a number.', // MISSING
	invalidRows: 'Number of rows must be a number greater than 0.', // MISSING
	invalidWidth: 'Table width must be a number.', // MISSING
	menu: 'Table Properties', // MISSING
	row: {
		menu: 'Row',
		insertBefore: 'Insert Row Before',
		insertAfter: 'Insert Row After',
		deleteRow: 'Delete Rows'
	},
	rows: 'Rows',
	summary: 'Summary', // MISSING
	title: 'Table Properties', // MISSING
	toolbar: 'Table', // MISSING
	widthPc: 'percent', // MISSING
	widthPx: 'pixels', // MISSING
	widthUnit: 'width unit' // MISSING
} );
