﻿@model EcoolWeb.ViewModels.AWA006QueryViewModel
@{ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();}
@{
    ViewBag.Title = "老師兌獎情形";
    int i = 0;
    string iUser_no = (user != null) ? user.USER_NO : "";
}
@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")


@using (Html.BeginForm("Query", "AWA006", FormMethod.Post, new { name = "AWA006", id = "AWA006" }))
{

    @Html.Hidden("Awat", Request["Awat"])
    @Html.HiddenFor(m => m.OrderColumn)
    <input type="hidden" name="SortBy" value="@Model.SortBy" />
    @Html.HiddenFor(m => m.Page)
    @Html.HiddenFor(m => m.whereSNAME)
    @Html.HiddenFor(m => m.whereAWARD_NAME)
    @Html.HiddenFor(m => m.whereCLASS_NO)
    @Html.Hidden("RoleName", "Teacher")

    @Html.Hidden("hidSelectTRANS_NO")
    <br />

    <span>每頁筆數</span>
    @Html.DropDownListFor(m=>m.PageSize,
        new List<SelectListItem>() {
            new SelectListItem() { Text="20", Value="20" },
            new SelectListItem() { Text = "40", Value = "40"},
            new SelectListItem() { Text = "60", Value = "60"},
            new SelectListItem() { Text = "80", Value = "80"},
            new SelectListItem() { Text = "100", Value = "100"}
        }, new { onchange= "document.forms[0].submit()" }
        )
    <br /> <br />

    <div id="Q-div">
        @if (ViewBag.Show)
        {
            <div class="form-inline">
                <div class="form-group">
                    <label class="control-label">學校</label>
                </div>
                <div class="form-group">
                    @Html.DropDownList("whereSchoolNo", (IEnumerable<SelectListItem>)ViewBag.SchoolNoSelectItem, new { @class = "form-control input-sm", @onchange = "FunPageProc(1);" })
                </div>
            </div>
            <br />
        }
        <div class="form-inline">
            <div class="form-inline" role="form">
                <div class="form-group">

                    <label class="control-label">獎品ID/帳號/姓名/品名</label>
                </div>
                <div class="form-group">
                    @Html.EditorFor(m => m.whereKeyword, new { htmlAttributes = new { @class = "form-control input-sm" } })



                </div>

                <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1);" />
                <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />
            </div>
        </div>
    </div>





}
<img src="~/Content/img/web-bar3-revise-34.png" style="width:100%" class="img-responsive " alt="Responsive image" />
<div class="table-responsive">
    <div class="text-center">
        <table class="table-ecool table-92Per table-hover table-ecool-ADDO05">
            <thead>
                <tr>
                    @if (ViewBag.Show)
                    {
                        <th style="text-align: center;">
                            <a href="#" class="orderColumn" onclick="document.getElementById('OrderColumn').value='SHORT_NAME';document.forms[0].submit()">
                                學校
                            </a>
                        </th>
                    }
                    <th>
                        姓名
                    </th>
                    <th>
                        獎品
                    </th>
                    <th>
                        <a href="#" class="orderColumn" onclick="document.getElementById('OrderColumn').value='COST_CASH';document.forms[0].submit()">
                            酷幣數
                        </a>
                    </th>
                    <th>
                        狀態
                    </th>
                    <th>
                        <a href="#" class="orderColumn" onclick="document.getElementById('OrderColumn').value='TRANS_DATE';document.forms[0].submit()">
                            日期
                        </a>
                    </th>
                    <th>
                    </th>

                </tr>
            </thead>
            <tbody>
                @foreach (var item in Model.VAWA005List)
                {
                    <tr>
                        @if (ViewBag.Show)
                        {
                            <td style="white-space:nowrap">
                                @Html.DisplayFor(modelItem => item.SHORT_NAME)
                            </td>
                        }
                        @if (item.SNAME.Trim() == "")
                        {
                            <td>
                                @Html.DisplayFor(modelItem => item.SNAME)
                            </td>
                        }
                        else
                        {
                            <td style="text-align: center;cursor:pointer;" onclick="doSearch('whereSNAME','@item.SNAME');">
                                @Html.DisplayFor(modelItem => item.SNAME)
                            </td>
                        }

                        <td style="text-align: center;cursor:pointer;" onclick="doSearch('whereAWARD_NAME','@item.AWARD_NAME');">
                            @Html.DisplayFor(modelItem => item.AWARD_NAME)
                        </td>
                        <td>
                            @Html.DisplayFor(modelItem => item.COST_CASH)
                        </td>
                        <td style="white-space:nowrap">
                            @Html.DisplayFor(modelItem => item.CTRANS_STATUS)
                            @if (item.CTRANS_STATUS == "已訂未領" && item.USER_NO == iUser_no)
                            {
                                <br />
                                <input type="button" id='[@i].btnTRANS_NO' name='[@i].btnTRANS_NO' value="取消領取" onclick="btnTRANS_NO_onclick('@item.TRANS_NO');" class="btn btn-xs btn-Basic">
                                <input type="hidden" id='[@i].TRANS_NO' name='[@i].TRANS_NO' value=@item.TRANS_NO />
                            }
                        </td>
                        <td>
                            @Html.DisplayFor(modelItem => item.TRANS_DATE, "ShortDateTime")
                        </td>
                        <td>
                            @Html.DisplayFor(modelItem => item.MEMO)
                        </td>
                    </tr>
                    i++;
                }
            </tbody>
        </table>
    </div>
</div>

<div>
    @Html.Pager(Model.VAWA005List.PageSize, Model.VAWA005List.PageNumber, Model.VAWA005List.TotalItemCount).Options(o => o
              .DisplayTemplate("BootstrapPagination")
             .MaxNrOfPages(5)
             .SetPreviousPageText("上頁")
             .SetNextPageText("下頁")
         )
</div>
<div style="height:15px"></div>
<div style="text-align:center;">
    @if (Request["Awat"] != null)
    {
        <a href='@Url.Action("Awat2Q02", "Awat2")' role="button" class="btn btn-default">
            返回
        </a>
        <br />
    }
</div>

@section css{
    <style>
        .orderColumn {
            color: #6badcf
        }
    </style>
}
@section scripts{
    <script>
        function btnTRANS_NO_onclick(TRANS_NO) {
            $("#hidSelectTRANS_NO").val(TRANS_NO);
            document.AWA006.enctype = "multipart/form-data";
            document.AWA006.action = "CancelTrans";
            document.AWA006.submit();
        }

        var targetFormID = '#AWA006';

        function FunPageProc(page) {
            if ($(targetFormID).size() > 0) {
                $('#Page').val(page)
                $(targetFormID).submit();
            }
        };

        function doSearch(ColName, whereValue) {
            $("#" + ColName).val(whereValue);
            FunPageProc(1)
        }

        function todoClear() {
            ////重設

            $('#Q-Div').find(":input,:selected").each(function (i) {

                var type = $(this).attr('type')
                var InPreadonly = $(this).attr('readonly')
                var tag = this.tagName.toLowerCase(); // normalize case

                if (InPreadonly == false || InPreadonly == undefined) {

                    if (type == 'radio' || type == 'checkbox') {
                        if ($(this).attr("title") == 'Default') {
                            this.checked = true;
                        }
                        else {
                            this.checked = false;
                        }
                    }
                    else if (tag == 'select') { //下拉式選單
                        this.selectedIndex = 0;
                    }
                    else if (type == 'text' || type == 'hidden' || type == 'password' || type == 'textarea') {
                        this.value = '';
                    }
                }
            });

            $(targetFormID).submit();
        }
    </script>
}