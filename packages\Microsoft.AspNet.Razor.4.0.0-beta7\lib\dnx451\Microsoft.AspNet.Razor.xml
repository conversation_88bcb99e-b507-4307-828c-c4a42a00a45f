<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.AspNet.Razor</name>
    </assembly>
    <members>
        <member name="T:Microsoft.AspNet.Razor.CSharpRazorCodeLanguage">
            <summary>
            Defines the C# Code Language for Razor
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.CSharpRazorCodeLanguage.LanguageName">
            <summary>
            Returns the name of the language: "csharp"
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.CSharpRazorCodeLanguage.CreateCodeParser">
            <summary>
            Constructs a new instance of the code parser for this language
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.CSharpRazorCodeLanguage.CreateChunkGenerator(System.String,System.String,System.String,Microsoft.AspNet.Razor.RazorEngineHost)">
            <summary>
            Constructs a new instance of the chunk generator for this language with the specified settings
            </summary>
        </member>
        <member name="T:Microsoft.AspNet.Razor.DocumentParseCompleteEventArgs">
            <summary>
            Arguments for the DocumentParseComplete event in RazorEditorParser
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.DocumentParseCompleteEventArgs.TreeStructureChanged">
            <summary>
            Indicates if the tree structure has actually changed since the previous re-parse.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.DocumentParseCompleteEventArgs.GeneratorResults">
            <summary>
            The results of the chunk generation and parsing
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.DocumentParseCompleteEventArgs.SourceChange">
            <summary>
            The TextChange which triggered the re-parse
            </summary>
        </member>
        <member name="T:Microsoft.AspNet.Razor.ErrorSink">
            <summary>
            Used to manage <see cref="T:Microsoft.AspNet.Razor.RazorError"/>s encountered during the Razor parsing phase.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.ErrorSink.#ctor">
            <summary>
            Instantiates a new instance of <see cref="T:Microsoft.AspNet.Razor.ErrorSink"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.ErrorSink.Errors">
            <summary>
            <see cref="T:Microsoft.AspNet.Razor.RazorError"/>s collected.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.ErrorSink.OnError(Microsoft.AspNet.Razor.RazorError)">
            <summary>
            Tracks the given <paramref name="error"/>.
            </summary>
            <param name="error">The <see cref="T:Microsoft.AspNet.Razor.RazorError"/> to track.</param>
        </member>
        <member name="M:Microsoft.AspNet.Razor.ErrorSink.OnError(Microsoft.AspNet.Razor.SourceLocation,System.String)">
            <summary>
            Creates and tracks a new <see cref="T:Microsoft.AspNet.Razor.RazorError"/>.
            </summary>
            <param name="location"><see cref="T:Microsoft.AspNet.Razor.SourceLocation"/> of the error.</param>
            <param name="message">A message describing the error.</param>
        </member>
        <member name="M:Microsoft.AspNet.Razor.ErrorSink.OnError(Microsoft.AspNet.Razor.SourceLocation,System.String,System.Int32)">
            <summary>
            Creates and tracks a new <see cref="T:Microsoft.AspNet.Razor.RazorError"/>.
            </summary>
            <param name="location"><see cref="T:Microsoft.AspNet.Razor.SourceLocation"/> of the error.</param>
            <param name="message">A message describing the error.</param>
            <param name="length">The length of the error.</param>
        </member>
        <member name="T:Microsoft.AspNet.Razor.ParserResults">
            <summary>
            Represents the results of parsing a Razor document
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.ParserResults.#ctor(Microsoft.AspNet.Razor.Parser.SyntaxTree.Block,System.Collections.Generic.IEnumerable{Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor},Microsoft.AspNet.Razor.ErrorSink)">
            <summary>
            Instantiates a new <see cref="T:Microsoft.AspNet.Razor.ParserResults"/> instance.
            </summary>
            <param name="document">The <see cref="T:Microsoft.AspNet.Razor.Parser.SyntaxTree.Block"/> for the syntax tree.</param>
            <param name="tagHelperDescriptors">
            The <see cref="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor"/>s that apply to the current Razor document.
            </param>
            <param name="errorSink">
            The <see cref="P:Microsoft.AspNet.Razor.ParserResults.ErrorSink"/> used to collect <see cref="T:Microsoft.AspNet.Razor.RazorError"/>s encountered when parsing the
            current Razor document.
            </param>
        </member>
        <member name="M:Microsoft.AspNet.Razor.ParserResults.#ctor(System.Boolean,Microsoft.AspNet.Razor.Parser.SyntaxTree.Block,System.Collections.Generic.IEnumerable{Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor},Microsoft.AspNet.Razor.ErrorSink)">
            <summary>
            Instantiates a new <see cref="T:Microsoft.AspNet.Razor.ParserResults"/> instance.
            </summary>
            <param name="success"><c>true</c> if parsing was successful, <c>false</c> otherwise.</param>
            <param name="document">The <see cref="T:Microsoft.AspNet.Razor.Parser.SyntaxTree.Block"/> for the syntax tree.</param>
            <param name="tagHelperDescriptors">
            The <see cref="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor"/>s that apply to the current Razor document.
            </param>
            <param name="errorSink">
            The <see cref="P:Microsoft.AspNet.Razor.ParserResults.ErrorSink"/> used to collect <see cref="T:Microsoft.AspNet.Razor.RazorError"/>s encountered when parsing the
            current Razor document.
            </param>
        </member>
        <member name="P:Microsoft.AspNet.Razor.ParserResults.Success">
            <summary>
            Indicates if parsing was successful (no errors).
            </summary>
            <value><c>true</c> if parsing was successful, <c>false</c> otherwise.</value>
        </member>
        <member name="P:Microsoft.AspNet.Razor.ParserResults.Document">
            <summary>
            The root node in the document's syntax tree.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.ParserResults.ErrorSink">
            <summary>
            Used to aggregate <see cref="T:Microsoft.AspNet.Razor.RazorError"/>s.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.ParserResults.ParserErrors">
            <summary>
            The list of errors which occurred during parsing.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.ParserResults.TagHelperDescriptors">
            <summary>
            The <see cref="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor"/>s found for the current Razor document.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.ParserResults.Prefix">
            <summary>
            Text used as a required prefix when matching HTML.
            </summary>
        </member>
        <member name="T:Microsoft.AspNet.Razor.PartialParseResult">
            <summary>
            The result of attempting an incremental parse
            </summary>
            <remarks>
            Either the Accepted or Rejected flag is ALWAYS set.
            Additionally, Provisional may be set with Accepted and SpanContextChanged may be set with Rejected.
            Provisional may NOT be set with Rejected and SpanContextChanged may NOT be set with Accepted.
            </remarks>
        </member>
        <member name="F:Microsoft.AspNet.Razor.PartialParseResult.Rejected">
            <summary>
            Indicates that the edit could not be accepted and that a reparse is underway.
            </summary>
        </member>
        <member name="F:Microsoft.AspNet.Razor.PartialParseResult.Accepted">
            <summary>
            Indicates that the edit was accepted and has been added to the parse tree
            </summary>
        </member>
        <member name="F:Microsoft.AspNet.Razor.PartialParseResult.Provisional">
            <summary>
            Indicates that the edit was accepted, but that a reparse should be forced when idle time is available
            since the edit may be misclassified
            </summary>
            <remarks>
            This generally occurs when a "." is typed in an Implicit Expression, since editors require that this
            be assigned to Code in order to properly support features like IntelliSense.  However, if no further edits
            occur following the ".", it should be treated as Markup.
            </remarks>
        </member>
        <member name="F:Microsoft.AspNet.Razor.PartialParseResult.SpanContextChanged">
            <summary>
            Indicates that the edit caused a change in the span's context and that if any statement completions were active prior to starting this
            partial parse, they should be reinitialized.
            </summary>
        </member>
        <member name="F:Microsoft.AspNet.Razor.PartialParseResult.AutoCompleteBlock">
            <summary>
            Indicates that the edit requires an auto completion to occur
            </summary>
        </member>
        <member name="T:Microsoft.AspNet.Razor.RazorCodeLanguage">
            <summary>
            Represents a code language in Razor.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorCodeLanguage.Languages">
            <summary>
            Gets the list of registered languages mapped to file extensions (without a ".")
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorCodeLanguage.LanguageName">
            <summary>
            The name of the language (for use in System.Web.Compilation.BuildProvider.GetDefaultCompilerTypeForLanguage)
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorCodeLanguage.GetLanguageByExtension(System.String)">
            <summary>
            Gets the RazorCodeLanguage registered for the specified file extension
            </summary>
            <param name="fileExtension">The extension, with or without a "."</param>
            <returns>The language registered for that extension</returns>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorCodeLanguage.CreateCodeParser">
            <summary>
            Constructs the code parser.  Must return a new instance on EVERY call to ensure thread-safety
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorCodeLanguage.CreateChunkGenerator(System.String,System.String,System.String,Microsoft.AspNet.Razor.RazorEngineHost)">
            <summary>
            Constructs the chunk generator.  Must return a new instance on EVERY call to ensure thread-safety
            </summary>
        </member>
        <member name="T:Microsoft.AspNet.Razor.RazorEditorParser">
             <summary>
             Parser used by editors to avoid reparsing the entire document on each text change
             </summary>
             <remarks>
             This parser is designed to allow editors to avoid having to worry about incremental parsing.
             The CheckForStructureChanges method can be called with every change made by a user in an editor and
             the parser will provide a result indicating if it was able to incrementally reparse the document.
            
             The general workflow for editors with this parser is:
             0. User edits document
             1. Editor builds TextChange structure describing the edit and providing a reference to the _updated_ text buffer
             2. Editor calls CheckForStructureChanges passing in that change.
             3. Parser determines if the change can be simply applied to an existing parse tree node
               a.  If it can, the Parser updates its parse tree and returns PartialParseResult.Accepted
               b.  If it can not, the Parser starts a background parse task and return PartialParseResult.Rejected
             NOTE: Additional flags can be applied to the PartialParseResult, see that enum for more details.  However,
                   the Accepted or Rejected flags will ALWAYS be present
            
             A change can only be incrementally parsed if a single, unique, Span (see Microsoft.AspNet.Razor.Parser.SyntaxTree) in the syntax tree can
             be identified as owning the entire change.  For example, if a change overlaps with multiple spans, the change cannot be
             parsed incrementally and a full reparse is necessary.  A Span "owns" a change if the change occurs either a) entirely
             within it's boundaries or b) it is a pure insertion (see TextChange) at the end of a Span whose CanGrow flag (see Span) is
             true.
            
             Even if a single unique Span owner can be identified, it's possible the edit will cause the Span to split or merge with other
             Spans, in which case, a full reparse is necessary to identify the extent of the changes to the tree.
            
             When the RazorEditorParser returns Accepted, it updates CurrentParseTree immediately.  However, the editor is expected to
             update it's own data structures independently.  It can use CurrentParseTree to do this, as soon as the editor returns from
             CheckForStructureChanges, but it should (ideally) have logic for doing so without needing the new tree.
            
             When Rejected is returned by CheckForStructureChanges, a background parse task has _already_ been started.  When that task
             finishes, the DocumentStructureChanged event will be fired containing the new generated code, parse tree and a reference to
             the original TextChange that caused the reparse, to allow the editor to resolve the new tree against any changes made since
             calling CheckForStructureChanges.
            
             If a call to CheckForStructureChanges occurs while a reparse is already in-progress, the reparse is cancelled IMMEDIATELY
             and Rejected is returned without attempting to reparse.  This means that if a conusmer calls CheckForStructureChanges, which
             returns Rejected, then calls it again before DocumentParseComplete is fired, it will only recieve one DocumentParseComplete
             event, for the second change.
             </remarks>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorEditorParser.#ctor(Microsoft.AspNet.Razor.RazorEngineHost,System.String)">
            <summary>
            Constructs the editor parser.  One instance should be used per active editor.  This
            instance _can_ be shared among reparses, but should _never_ be shared between documents.
            </summary>
            <param name="host">The <see cref="T:Microsoft.AspNet.Razor.RazorEngineHost"/> which defines the environment in which the generated code will live.  <see cref="F:RazorEngineHost.DesignTimeMode"/> should be set if design-time code mappings are desired</param>
            <param name="sourceFileName">The physical path to use in line pragmas</param>
        </member>
        <member name="E:Microsoft.AspNet.Razor.RazorEditorParser.DocumentParseComplete">
            <summary>
            Event fired when a full reparse of the document completes
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorEditorParser.CheckForStructureChanges(Microsoft.AspNet.Razor.Text.TextChange)">
            <summary>
            Determines if a change will cause a structural change to the document and if not, applies it to the existing tree.
            If a structural change would occur, automatically starts a reparse
            </summary>
            <remarks>
            NOTE: The initial incremental parsing check and actual incremental parsing (if possible) occurs
            on the callers thread.  However, if a full reparse is needed, this occurs on a background thread.
            </remarks>
            <param name="change">The change to apply to the parse tree</param>
            <returns>A PartialParseResult value indicating the result of the incremental parse</returns>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorEditorParser.Dispose">
            <summary>
            Disposes of this parser.  Should be called when the editor window is closed and the document is unloaded.
            </summary>
        </member>
        <member name="T:Microsoft.AspNet.Razor.RazorEngineHost">
            <summary>
            Defines the environment in which a Razor template will live
            </summary>
            <remarks>
            The host defines the following things:
            * What method names will be used for rendering markup, expressions etc.  For example "Write", "WriteLiteral"
            * The namespace imports to be added to every page generated via this host
            * The default Base Class to inherit the generated class from
            * The default Class Name and Namespace for the generated class (can be overridden by parameters in RazorTemplateEngine.GeneratedCode)
            * The language of the code in a Razor page
            * The markup, code parsers and chunk generators to use (the system will select defaults, but a Host gets a change to augment them)
                ** See DecorateNNN methods
            * Additional code to add to the generated code (see PostProcessGeneratedCode)
            </remarks>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorEngineHost.#ctor(Microsoft.AspNet.Razor.RazorCodeLanguage)">
            <summary>
            Creates a host which uses the specified code language and the HTML markup language
            </summary>
            <param name="codeLanguage">The code language to use</param>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorEngineHost.GeneratedClassContext">
            <summary>
            Details about the methods and types that should be used to generate code for Razor constructs
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorEngineHost.NamespaceImports">
            <summary>
            A list of namespaces to import in the generated file
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorEngineHost.DefaultBaseClass">
            <summary>
            The base-class of the generated class
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorEngineHost.DesignTimeMode">
            <summary>
            Indicates if the parser and chunk generator should run in design-time mode
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorEngineHost.DefaultClassName">
            <summary>
            The name of the generated class
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorEngineHost.DefaultNamespace">
            <summary>
            The namespace which will contain the generated class
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorEngineHost.StaticHelpers">
            <summary>
            Boolean indicating if helper methods should be instance methods or static methods
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorEngineHost.CodeLanguage">
            <summary>
            The language of the code within the Razor template.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorEngineHost.TagHelperDescriptorResolver">
            <summary>
            The <see cref="T:Microsoft.AspNet.Razor.TagHelpers.ITagHelperDescriptorResolver"/> used to resolve <see cref="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor"/>s.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorEngineHost.EnableInstrumentation">
            <summary>
            Boolean indicating if instrumentation code should be injected into the output page
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorEngineHost.IsIndentingWithTabs">
            <summary>
            Gets or sets whether the design time editor is using tabs or spaces for indentation.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorEngineHost.TabSize">
            <summary>
            Tab size used by the hosting editor, when indenting with tabs.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorEngineHost.InstrumentedSourceFilePath">
            <summary>
            Gets or sets the path to use for this document when generating Instrumentation calls
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorEngineHost.CreateMarkupParser">
            <summary>
            Constructs the markup parser.  Must return a new instance on EVERY call to ensure thread-safety
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorEngineHost.DecorateRazorParser(Microsoft.AspNet.Razor.Parser.RazorParser,System.String)">
            <summary>
            Provides an opportunity for derived types to modify the instance of <see cref="T:Microsoft.AspNet.Razor.Parser.RazorParser"/>
            used by the <see cref="T:Microsoft.AspNet.Razor.RazorTemplateEngine"/> to parse the Razor tree.
            </summary>
            <param name="incomingRazorParser">The <see cref="T:Microsoft.AspNet.Razor.Parser.RazorParser"/></param>
            <param name="sourceFileName">The file name of the Razor file being parsed.</param>
            <returns>Either the same code parser, after modifications, or a different code parser.</returns>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorEngineHost.DecorateCodeParser(Microsoft.AspNet.Razor.Parser.ParserBase)">
            <summary>
            Gets an instance of the code parser and is provided an opportunity to decorate or replace it
            </summary>
            <param name="incomingCodeParser">The code parser</param>
            <returns>Either the same code parser, after modifications, or a different code parser</returns>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorEngineHost.DecorateMarkupParser(Microsoft.AspNet.Razor.Parser.ParserBase)">
            <summary>
            Gets an instance of the markup parser and is provided an opportunity to decorate or replace it
            </summary>
            <param name="incomingMarkupParser">The markup parser</param>
            <returns>Either the same markup parser, after modifications, or a different markup parser</returns>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorEngineHost.DecorateChunkGenerator(Microsoft.AspNet.Razor.Chunks.Generators.RazorChunkGenerator)">
            <summary>
            Gets an instance of the chunk generator and is provided an opportunity to decorate or replace it
            </summary>
            <param name="incomingChunkGenerator">The chunk generator</param>
            <returns>Either the same chunk generator, after modifications, or a different chunk generator</returns>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorEngineHost.DecorateCodeGenerator(Microsoft.AspNet.Razor.CodeGenerators.CodeGenerator,Microsoft.AspNet.Razor.CodeGenerators.CodeGeneratorContext)">
            <summary>
            Gets an instance of the code generator and is provided an opportunity to decorate or replace it
            </summary>
            <param name="incomingBuilder">The code generator</param>
            <returns>Either the same code generator, after modifications, or a different code generator.</returns>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorError.Message">
            <summary>
            Gets (or sets) the message describing the error.
            </summary>
            <remarks>Set property is only accessible for deserialization purposes.</remarks>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorError.Location">
            <summary>
            Gets (or sets) the start position of the erroneous text.
            </summary>
            <remarks>Set property is only accessible for deserialization purposes.</remarks>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorError.Length">
            <summary>
            Gets or sets the length of the erroneous text.
            </summary>
            <remarks>Set property is only accessible for deserialization purposes.</remarks>
        </member>
        <member name="T:Microsoft.AspNet.Razor.RazorTemplateEngine">
            <summary>
            Entry-point to the Razor Template Engine
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorTemplateEngine.#ctor(Microsoft.AspNet.Razor.RazorEngineHost)">
            <summary>
            Constructs a new RazorTemplateEngine with the specified host
            </summary>
            <param name="host">
            The host which defines the environment in which the generated template code will live.
            </param>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorTemplateEngine.Host">
            <summary>
            The RazorEngineHost which defines the environment in which the generated template code will live
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorTemplateEngine.ParseTemplate(Microsoft.AspNet.Razor.Text.ITextBuffer,System.Nullable{System.Threading.CancellationToken})">
            <summary>
            Parses the template specified by the TextBuffer and returns it's result
            </summary>
            <remarks>
            <para>
            IMPORTANT: This does NOT need to be called before GeneratedCode! GenerateCode will automatically
            parse the document first.
            </para>
            <para>
            The cancel token provided can be used to cancel the parse.  However, please note
            that the parse occurs _synchronously_, on the callers thread.  This parameter is
            provided so that if the caller is in a background thread with a CancellationToken,
            it can pass it along to the parser.
            </para>
            </remarks>
            <param name="input">The input text to parse.</param>
            <param name="cancelToken">A token used to cancel the parser.</param>
            <returns>The resulting parse tree.</returns>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorTemplateEngine.GenerateCode(Microsoft.AspNet.Razor.Text.ITextBuffer,System.String,System.String,System.String,System.Nullable{System.Threading.CancellationToken})">
            <summary>
            Parses the template specified by the TextBuffer, generates code for it, and returns the constructed code.
            </summary>
            <remarks>
            <para>
            The cancel token provided can be used to cancel the parse.  However, please note
            that the parse occurs _synchronously_, on the callers thread.  This parameter is
            provided so that if the caller is in a background thread with a CancellationToken,
            it can pass it along to the parser.
            </para>
            <para>
            The className, rootNamespace and sourceFileName parameters are optional and override the default
            specified by the Host.  For example, the WebPageRazorHost in System.Web.WebPages.Razor configures the
            Class Name, Root Namespace and Source File Name based on the virtual path of the page being compiled.
            However, the built-in RazorEngineHost class uses constant defaults, so the caller will likely want to
            change them using these parameters.
            </para>
            </remarks>
            <param name="input">The input text to parse.</param>
            <param name="cancelToken">A token used to cancel the parser.</param>
            <param name="className">
            The name of the generated class, overriding whatever is specified in the Host.  The default value (defined
            in the Host) can be used by providing null for this argument.
            </param>
            <param name="rootNamespace">The namespace in which the generated class will reside, overriding whatever is
            specified in the Host.  The default value (defined in the Host) can be used by providing null for this
            argument.
            </param>
            <param name="sourceFileName">
            The file name to use in line pragmas, usually the original Razor file, overriding whatever is specified in
            the Host.  The default value (defined in the Host) can be used by providing null for this argument.
            </param>
            <returns>The resulting parse tree AND generated code.</returns>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorTemplateEngine.GenerateCode(System.IO.Stream,System.String,System.String,System.String)">
            <summary>
            Parses the contents specified by the <paramref name="inputStream"/> and returns the generated code.
            </summary>
            <param name="inputStream">A <see cref="T:System.IO.Stream"/> that represents the contents to be parsed.</param>
            <param name="className">The name of the generated class. When <c>null</c>, defaults to
            <see cref="P:Microsoft.AspNet.Razor.RazorEngineHost.DefaultClassName"/> (<c>Host.DefaultClassName</c>).</param>
            <param name="rootNamespace">The namespace in which the generated class will reside. When <c>null</c>,
            defaults to <see cref="P:Microsoft.AspNet.Razor.RazorEngineHost.DefaultNamespace"/> (<c>Host.DefaultNamespace</c>).</param>
            <param name="sourceFileName">
            The file name to use in line pragmas, usually the original Razor file.
            </param>
            <returns>A <see cref="T:Microsoft.AspNet.Razor.CodeGenerators.GeneratorResults"/> that represents the results of parsing the content.</returns>
            <remarks>
            This overload calculates the checksum of the contents of <paramref name="inputStream"/> prior to code
            generation. The checksum is used for producing the <c>#pragma checksum</c> line pragma required for
            debugging.
            </remarks>
        </member>
        <member name="T:Microsoft.AspNet.Razor.SourceLocation">
            <summary>
            A location in a Razor file.
            </summary>
        </member>
        <member name="F:Microsoft.AspNet.Razor.SourceLocation.Undefined">
            <summary>
            An undefined <see cref="T:Microsoft.AspNet.Razor.SourceLocation"/>.
            </summary>
        </member>
        <member name="F:Microsoft.AspNet.Razor.SourceLocation.Zero">
            <summary>
            A <see cref="T:Microsoft.AspNet.Razor.SourceLocation"/> with <see cref="P:Microsoft.AspNet.Razor.SourceLocation.AbsoluteIndex"/>, <see cref="P:Microsoft.AspNet.Razor.SourceLocation.LineIndex"/>, and
            <see cref="P:Microsoft.AspNet.Razor.SourceLocation.CharacterIndex"/> initialized to 0.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.SourceLocation.#ctor(System.Int32,System.Int32,System.Int32)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNet.Razor.SourceLocation"/>.
            </summary>
            <param name="absoluteIndex">The absolute index.</param>
            <param name="lineIndex">The line index.</param>
            <param name="characterIndex">The character index.</param>
        </member>
        <member name="M:Microsoft.AspNet.Razor.SourceLocation.#ctor(System.String,System.Int32,System.Int32,System.Int32)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNet.Razor.SourceLocation"/>.
            </summary>
            <param name="filePath">The file path.</param>
            <param name="absoluteIndex">The absolute index.</param>
            <param name="lineIndex">The line index.</param>
            <param name="characterIndex">The character index.</param>
        </member>
        <member name="P:Microsoft.AspNet.Razor.SourceLocation.FilePath">
            <summary>
            Path of the file.
            </summary>
            <remarks>When <c>null</c>, the parser assumes the location is in the file currently being processed.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNet.Razor.SourceLocation.AbsoluteIndex">
            <remarks>Set property is only accessible for deserialization purposes.</remarks>
        </member>
        <member name="P:Microsoft.AspNet.Razor.SourceLocation.LineIndex">
            <summary>
            Gets the 1-based index of the line referred to by this Source Location.
            </summary>
            <remarks>Set property is only accessible for deserialization purposes.</remarks>
        </member>
        <member name="P:Microsoft.AspNet.Razor.SourceLocation.CharacterIndex">
            <remarks>Set property is only accessible for deserialization purposes.</remarks>
        </member>
        <member name="M:Microsoft.AspNet.Razor.SourceLocation.ToString">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNet.Razor.SourceLocation.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNet.Razor.SourceLocation.GetHashCode">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNet.Razor.SourceLocation.Equals(Microsoft.AspNet.Razor.SourceLocation)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNet.Razor.SourceLocation.CompareTo(Microsoft.AspNet.Razor.SourceLocation)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNet.Razor.SourceLocation.Advance(Microsoft.AspNet.Razor.SourceLocation,System.String)">
            <summary>
            Advances the <see cref="T:Microsoft.AspNet.Razor.SourceLocation"/> by the length of the <paramref name="text" />.
            </summary>
            <param name="left">The <see cref="T:Microsoft.AspNet.Razor.SourceLocation"/> to advance.</param>
            <param name="text">The <see cref="T:System.String"/> to advance <paramref name="left"/> by.</param>
            <returns>The advanced <see cref="T:Microsoft.AspNet.Razor.SourceLocation"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNet.Razor.SourceLocation.op_Addition(Microsoft.AspNet.Razor.SourceLocation,Microsoft.AspNet.Razor.SourceLocation)">
            <summary>
            Adds two <see cref="T:Microsoft.AspNet.Razor.SourceLocation"/>s.
            </summary>
            <param name="left">The left operand.</param>
            <param name="right">The right operand.</param>
            <returns>A <see cref="T:Microsoft.AspNet.Razor.SourceLocation"/> that is the sum of the left and right operands.</returns>
            <exception cref="T:System.ArgumentException">if the <see cref="P:Microsoft.AspNet.Razor.SourceLocation.FilePath"/> of the left and right operands
            are different, and neither is null.</exception>
        </member>
        <member name="M:Microsoft.AspNet.Razor.SourceLocation.op_Subtraction(Microsoft.AspNet.Razor.SourceLocation,Microsoft.AspNet.Razor.SourceLocation)">
            <summary>
            Subtracts two <see cref="T:Microsoft.AspNet.Razor.SourceLocation"/>s.
            </summary>
            <param name="left">The left operand.</param>
            <param name="right">The right operand.</param>
            <returns>A <see cref="T:Microsoft.AspNet.Razor.SourceLocation"/> that is the difference of the left and right operands.</returns>
            <exception cref="T:System.ArgumentException">if the <see cref="P:Microsoft.AspNet.Razor.SourceLocation.FilePath"/> of the left and right operands
            are different.</exception>
        </member>
        <member name="M:Microsoft.AspNet.Razor.SourceLocation.op_LessThan(Microsoft.AspNet.Razor.SourceLocation,Microsoft.AspNet.Razor.SourceLocation)">
            <summary>
            Determines whether the first operand is less than the second operand.
            </summary>
            <param name="left">The left operand.</param>
            <param name="right">The right operand.</param>
            <returns><c>true</c> if <paramref name="left"/> is less than <paramref name="right"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNet.Razor.SourceLocation.op_GreaterThan(Microsoft.AspNet.Razor.SourceLocation,Microsoft.AspNet.Razor.SourceLocation)">
            <summary>
            Determines whether the first operand is greater than the second operand.
            </summary>
            <param name="left">The left operand.</param>
            <param name="right">The right operand.</param>
            <returns><c>true</c> if <paramref name="left"/> is greater than <paramref name="right"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNet.Razor.SourceLocation.op_Equality(Microsoft.AspNet.Razor.SourceLocation,Microsoft.AspNet.Razor.SourceLocation)">
            <summary>
            Determines whether the operands are equal.
            </summary>
            <param name="left">The left operand.</param>
            <param name="right">The right operand.</param>
            <returns><c>true</c> if <paramref name="left"/> and <paramref name="right"/> are equal.</returns>
        </member>
        <member name="M:Microsoft.AspNet.Razor.SourceLocation.op_Inequality(Microsoft.AspNet.Razor.SourceLocation,Microsoft.AspNet.Razor.SourceLocation)">
            <summary>
            Determines whether the operands are not equal.
            </summary>
            <param name="left">The left operand.</param>
            <param name="right">The right operand.</param>
            <returns><c>true</c> if <paramref name="left"/> and <paramref name="right"/> are not equal.</returns>
        </member>
        <member name="M:Microsoft.AspNet.Razor.StateMachine`1.Stop">
            <summary>
            Returns a result indicating that the machine should stop executing and return null output.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.StateMachine`1.Transition(Microsoft.AspNet.Razor.StateMachine{`0}.State)">
            <summary>
            Returns a result indicating that this state has no output and the machine should immediately invoke the specified state
            </summary>
            <remarks>
            By returning no output, the state machine will invoke the next state immediately, before returning
            controller to the caller of <see cref="M:Microsoft.AspNet.Razor.StateMachine`1.Turn"/>
            </remarks>
        </member>
        <member name="M:Microsoft.AspNet.Razor.StateMachine`1.Transition(`0,Microsoft.AspNet.Razor.StateMachine{`0}.State)">
            <summary>
            Returns a result containing the specified output and indicating that the next call to
            <see cref="M:Microsoft.AspNet.Razor.StateMachine`1.Turn"/> should invoke the provided state.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.StateMachine`1.Stay">
            <summary>
            Returns a result indicating that this state has no output and the machine should remain in this state
            </summary>
            <remarks>
            By returning no output, the state machine will re-invoke the current state again before returning
            controller to the caller of <see cref="M:Microsoft.AspNet.Razor.StateMachine`1.Turn"/>
            </remarks>
        </member>
        <member name="M:Microsoft.AspNet.Razor.StateMachine`1.Stay(`0)">
            <summary>
            Returns a result containing the specified output and indicating that the next call to
            <see cref="M:Microsoft.AspNet.Razor.StateMachine`1.Turn"/> should re-invoke the current state.
            </summary>
        </member>
        <member name="T:Microsoft.AspNet.Razor.Chunks.AddTagHelperChunk">
            <summary>
            A <see cref="T:Microsoft.AspNet.Razor.Chunks.Chunk"/> used to look up <see cref="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor"/>s.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.Chunks.AddTagHelperChunk.LookupText">
            <summary>
            Text used to look up <see cref="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor"/>s.
            </summary>
        </member>
        <member name="T:Microsoft.AspNet.Razor.Chunks.RemoveTagHelperChunk">
            <summary>
            A <see cref="T:Microsoft.AspNet.Razor.Chunks.Chunk"/> used to look up <see cref="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor"/>s that should be ignored
            within the Razor page.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.Chunks.RemoveTagHelperChunk.LookupText">
            <summary>
            Text used to look up <see cref="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor"/>s that should be ignored within the Razor
            page.
            </summary>
        </member>
        <member name="T:Microsoft.AspNet.Razor.Chunks.TagHelperChunk">
            <summary>
            A <see cref="T:Microsoft.AspNet.Razor.Chunks.ParentChunk"/> that represents a special HTML tag.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.Chunks.TagHelperChunk.#ctor(System.String,Microsoft.AspNet.Razor.Runtime.TagHelpers.TagMode,System.Collections.Generic.IList{System.Collections.Generic.KeyValuePair{System.String,Microsoft.AspNet.Razor.Chunks.Chunk}},System.Collections.Generic.IEnumerable{Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor})">
            <summary>
            Instantiates a new <see cref="T:Microsoft.AspNet.Razor.Chunks.TagHelperChunk"/>.
            </summary>
            <param name="tagName">The tag name associated with the tag helpers HTML element.</param>
            <param name="tagMode">HTML syntax of the element in the Razor source.</param>
            <param name="attributes">The attributes associated with the tag helpers HTML element.</param>
            <param name="descriptors">
            The <see cref="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor"/>s associated with this tag helpers HTML element.
            </param>
        </member>
        <member name="P:Microsoft.AspNet.Razor.Chunks.TagHelperChunk.Attributes">
            <summary>
            The HTML attributes.
            </summary>
            <remarks>
            These attributes are <see cref="T:System.String"/> => <see cref="T:Microsoft.AspNet.Razor.Chunks.Chunk"/> so attribute values can consist
            of all sorts of Razor specific pieces.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNet.Razor.Chunks.TagHelperChunk.Descriptors">
            <summary>
            The <see cref="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor"/>s that are associated with the tag helpers HTML element.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.Chunks.TagHelperChunk.TagName">
            <summary>
            The HTML tag name.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.Chunks.TagHelperChunk.TagMode">
            <summary>
            Gets the HTML syntax of the element in the Razor source.
            </summary>
        </member>
        <member name="T:Microsoft.AspNet.Razor.Chunks.TagHelperPrefixDirectiveChunk">
            <summary>
            A <see cref="T:Microsoft.AspNet.Razor.Chunks.Chunk"/> for the <c>@tagHelperPrefix</c> directive.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.Chunks.TagHelperPrefixDirectiveChunk.Prefix">
            <summary>
            Text used as a required prefix when matching HTML start and end tags in the Razor source to available 
            tag helpers.
            </summary>
        </member>
        <member name="T:Microsoft.AspNet.Razor.Chunks.Generators.AddOrRemoveTagHelperChunkGenerator">
            <summary>
            A <see cref="T:Microsoft.AspNet.Razor.Chunks.Generators.SpanChunkGenerator"/> responsible for generating <see cref="T:Microsoft.AspNet.Razor.Chunks.AddTagHelperChunk"/>s and
            <see cref="T:Microsoft.AspNet.Razor.Chunks.RemoveTagHelperChunk"/>s.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.Chunks.Generators.AddOrRemoveTagHelperChunkGenerator.#ctor(System.Boolean,System.String)">
            <summary>
            Instantiates a new <see cref="T:Microsoft.AspNet.Razor.Chunks.Generators.AddOrRemoveTagHelperChunkGenerator"/>.
            </summary>
            <param name="lookupText">
            Text used to look up <see cref="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor"/>s that should be added or removed.
            </param>
        </member>
        <member name="P:Microsoft.AspNet.Razor.Chunks.Generators.AddOrRemoveTagHelperChunkGenerator.LookupText">
            <summary>
            Gets the text used to look up <see cref="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor"/>s that should be added to or
            removed from the Razor page.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.Chunks.Generators.AddOrRemoveTagHelperChunkGenerator.RemoveTagHelperDescriptors">
            <summary>
            Whether we want to remove <see cref="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor"/>s from the Razor page.
            </summary>
            <remarks>If <c>true</c> <see cref="M:Microsoft.AspNet.Razor.Chunks.Generators.AddOrRemoveTagHelperChunkGenerator.GenerateChunk(Microsoft.AspNet.Razor.Parser.SyntaxTree.Span,Microsoft.AspNet.Razor.Chunks.Generators.ChunkGeneratorContext)"/> generates <see cref="T:Microsoft.AspNet.Razor.Chunks.AddTagHelperChunk"/>s,
            <see cref="T:Microsoft.AspNet.Razor.Chunks.RemoveTagHelperChunk"/>s otherwise.</remarks>
        </member>
        <member name="M:Microsoft.AspNet.Razor.Chunks.Generators.AddOrRemoveTagHelperChunkGenerator.GenerateChunk(Microsoft.AspNet.Razor.Parser.SyntaxTree.Span,Microsoft.AspNet.Razor.Chunks.Generators.ChunkGeneratorContext)">
            <summary>
            Generates <see cref="T:Microsoft.AspNet.Razor.Chunks.AddTagHelperChunk"/>s if <see cref="P:Microsoft.AspNet.Razor.Chunks.Generators.AddOrRemoveTagHelperChunkGenerator.RemoveTagHelperDescriptors"/> is
            <c>true</c>, otherwise <see cref="T:Microsoft.AspNet.Razor.Chunks.RemoveTagHelperChunk"/>s are generated.
            </summary>
            <param name="target">
            The <see cref="T:Microsoft.AspNet.Razor.Parser.SyntaxTree.Span"/> responsible for this <see cref="T:Microsoft.AspNet.Razor.Chunks.Generators.AddOrRemoveTagHelperChunkGenerator"/>.
            </param>
            <param name="context">A <see cref="T:Microsoft.AspNet.Razor.Chunks.Generators.ChunkGeneratorContext"/> instance that contains information about
            the current chunk generation process.</param>
        </member>
        <member name="T:Microsoft.AspNet.Razor.Chunks.Generators.TagHelperChunkGenerator">
            <summary>
            A <see cref="T:Microsoft.AspNet.Razor.Chunks.Generators.ParentChunkGenerator"/> that is responsible for generating valid <see cref="T:Microsoft.AspNet.Razor.Chunks.TagHelperChunk"/>s.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.Chunks.Generators.TagHelperChunkGenerator.#ctor(System.Collections.Generic.IEnumerable{Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor})">
            <summary>
            Instantiates a new <see cref="T:Microsoft.AspNet.Razor.Chunks.Generators.TagHelperChunkGenerator"/>.
            </summary>
            <param name="tagHelperDescriptors">
            <see cref="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor"/>s associated with the current HTML tag.
            </param>
        </member>
        <member name="M:Microsoft.AspNet.Razor.Chunks.Generators.TagHelperChunkGenerator.GenerateStartParentChunk(Microsoft.AspNet.Razor.Parser.SyntaxTree.Block,Microsoft.AspNet.Razor.Chunks.Generators.ChunkGeneratorContext)">
            <summary>
            Starts the generation of a <see cref="T:Microsoft.AspNet.Razor.Chunks.TagHelperChunk"/>.
            </summary>
            <param name="target">
            The <see cref="T:Microsoft.AspNet.Razor.Parser.SyntaxTree.Block"/> responsible for this <see cref="T:Microsoft.AspNet.Razor.Chunks.Generators.TagHelperChunkGenerator"/>.
            </param>
            <param name="context">A <see cref="T:Microsoft.AspNet.Razor.Chunks.Generators.ChunkGeneratorContext"/> instance that contains information about
            the current chunk generation process.</param>
        </member>
        <member name="M:Microsoft.AspNet.Razor.Chunks.Generators.TagHelperChunkGenerator.GenerateEndParentChunk(Microsoft.AspNet.Razor.Parser.SyntaxTree.Block,Microsoft.AspNet.Razor.Chunks.Generators.ChunkGeneratorContext)">
            <summary>
            Ends the generation of a <see cref="T:Microsoft.AspNet.Razor.Chunks.TagHelperChunk"/> capturing all previously visited children
            since the <see cref="M:Microsoft.AspNet.Razor.Chunks.Generators.TagHelperChunkGenerator.GenerateStartParentChunk(Microsoft.AspNet.Razor.Parser.SyntaxTree.Block,Microsoft.AspNet.Razor.Chunks.Generators.ChunkGeneratorContext)"/> method was called.
            </summary>
            <param name="target">
            The <see cref="T:Microsoft.AspNet.Razor.Parser.SyntaxTree.Block"/> responsible for this <see cref="T:Microsoft.AspNet.Razor.Chunks.Generators.TagHelperChunkGenerator"/>.
            </param>
            <param name="context">A <see cref="T:Microsoft.AspNet.Razor.Chunks.Generators.ChunkGeneratorContext"/> instance that contains information about
            the current chunk generation process.</param>
        </member>
        <member name="T:Microsoft.AspNet.Razor.Chunks.Generators.TagHelperPrefixDirectiveChunkGenerator">
            <summary>
            A <see cref="T:Microsoft.AspNet.Razor.Chunks.Generators.SpanChunkGenerator"/> responsible for generating
            <see cref="T:Microsoft.AspNet.Razor.Chunks.TagHelperPrefixDirectiveChunk"/>s.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.Chunks.Generators.TagHelperPrefixDirectiveChunkGenerator.#ctor(System.String)">
            <summary>
            Instantiates a new <see cref="T:Microsoft.AspNet.Razor.Chunks.Generators.TagHelperPrefixDirectiveChunkGenerator"/>.
            </summary>
            <param name="prefix">
            Text used as a required prefix when matching HTML.
            </param>
        </member>
        <member name="P:Microsoft.AspNet.Razor.Chunks.Generators.TagHelperPrefixDirectiveChunkGenerator.Prefix">
            <summary>
            Text used as a required prefix when matching HTML.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.Chunks.Generators.TagHelperPrefixDirectiveChunkGenerator.GenerateChunk(Microsoft.AspNet.Razor.Parser.SyntaxTree.Span,Microsoft.AspNet.Razor.Chunks.Generators.ChunkGeneratorContext)">
            <summary>
            Generates <see cref="T:Microsoft.AspNet.Razor.Chunks.TagHelperPrefixDirectiveChunk"/>s.
            </summary>
            <param name="target">
            The <see cref="T:Microsoft.AspNet.Razor.Parser.SyntaxTree.Span"/> responsible for this <see cref="T:Microsoft.AspNet.Razor.Chunks.Generators.TagHelperPrefixDirectiveChunkGenerator"/>.
            </param>
            <param name="context">A <see cref="T:Microsoft.AspNet.Razor.Chunks.Generators.ChunkGeneratorContext"/> instance that contains information about
            the current chunk generation process.</param>
        </member>
        <member name="T:Microsoft.AspNet.Razor.CodeGenerators.CodeGeneratorContext">
            <summary>
            Context object with information used to generate a Razor page.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.CodeGenerators.CodeGeneratorContext.#ctor(Microsoft.AspNet.Razor.Chunks.Generators.ChunkGeneratorContext,Microsoft.AspNet.Razor.ErrorSink)">
            <summary>
            Instantiates a new instance of the <see cref="T:Microsoft.AspNet.Razor.CodeGenerators.CodeGeneratorContext"/> object.
            </summary>
            <param name="generatorContext">A <see cref="T:Microsoft.AspNet.Razor.Chunks.Generators.ChunkGeneratorContext"/> to copy information from.</param>
            <param name="errorSink">
            The <see cref="P:Microsoft.AspNet.Razor.CodeGenerators.CodeGeneratorContext.ErrorSink"/> used to collect <see cref="T:Microsoft.AspNet.Razor.RazorError"/>s encountered
            when parsing the current Razor document.
            </param>
        </member>
        <member name="P:Microsoft.AspNet.Razor.CodeGenerators.CodeGeneratorContext.ExpressionRenderingMode">
            <summary>
            The current C# rendering mode.
            </summary>
            <remarks>
            <see cref="F:Microsoft.AspNet.Razor.CodeGenerators.ExpressionRenderingMode.WriteToOutput"/> forces C# generation to write
            <see cref="T:Microsoft.AspNet.Razor.Chunks.Chunk"/>s to the output page, i.e. WriteLiteral("Hello World").
            <see cref="F:Microsoft.AspNet.Razor.CodeGenerators.ExpressionRenderingMode.InjectCode"/> writes <see cref="T:Microsoft.AspNet.Razor.Chunks.Chunk"/> values in their
            rawest form, i.g. "Hello World".
            </remarks>
        </member>
        <member name="P:Microsoft.AspNet.Razor.CodeGenerators.CodeGeneratorContext.TargetWriterName">
            <summary>
            The C# writer to write <see cref="T:Microsoft.AspNet.Razor.Chunks.Chunk"/> information to.
            </summary>
            <remarks>
            If <see cref="P:Microsoft.AspNet.Razor.CodeGenerators.CodeGeneratorContext.TargetWriterName"/> is <c>null</c> values will be written using a default write method
            i.e. WriteLiteral("Hello World").
            If <see cref="P:Microsoft.AspNet.Razor.CodeGenerators.CodeGeneratorContext.TargetWriterName"/> is not <c>null</c> values will be written to the given
            <see cref="P:Microsoft.AspNet.Razor.CodeGenerators.CodeGeneratorContext.TargetWriterName"/>, i.e. WriteLiteralTo(myWriter, "Hello World").
            </remarks>
        </member>
        <member name="P:Microsoft.AspNet.Razor.CodeGenerators.CodeGeneratorContext.Checksum">
            <summary>
            Gets or sets the <c>SHA1</c> based checksum for the file whose location is defined by
            <see cref="P:Microsoft.AspNet.Razor.Chunks.Generators.ChunkGeneratorContext.SourceFile"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.CodeGenerators.CodeGeneratorContext.ErrorSink">
            <summary>
            Used to aggregate <see cref="T:Microsoft.AspNet.Razor.RazorError"/>s.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.CodeGenerators.CSharpCodeGenerator.CreateCodeWriter">
            <summary>
            Protected for testing.
            </summary>
            <returns>A new instance of <see cref="T:Microsoft.AspNet.Razor.CodeGenerators.CSharpCodeWriter"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNet.Razor.CodeGenerators.CSharpCodeWriter.WriteLineNumberDirective(Microsoft.AspNet.Razor.SourceLocation,System.String)">
            <summary>
            Writes a <c>#line</c> pragma directive for the line number at the specified <paramref name="location"/>.
            </summary>
            <param name="location">The location to generate the line pragma for.</param>
            <param name="file">The file to generate the line pragma for.</param>
            <returns>The current instance of <see cref="T:Microsoft.AspNet.Razor.CodeGenerators.CSharpCodeWriter"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNet.Razor.CodeGenerators.CSharpLineMappingWriter.#ctor(Microsoft.AspNet.Razor.CodeGenerators.CSharpCodeWriter,Microsoft.AspNet.Razor.SourceLocation,System.String)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNet.Razor.CodeGenerators.CSharpLineMappingWriter"/> used for generation of runtime
            line mappings. The constructed instance of <see cref="T:Microsoft.AspNet.Razor.CodeGenerators.CSharpLineMappingWriter"/> does not track
            mappings between the Razor content and the generated content.
            </summary>
            <param name="writer">The <see cref="T:Microsoft.AspNet.Razor.CodeGenerators.CSharpCodeWriter"/> to write output to.</param>
            <param name="documentLocation">The <see cref="T:Microsoft.AspNet.Razor.SourceLocation"/> of the Razor content being mapping.</param>
            <param name="sourceFileName">The input file path.</param>
        </member>
        <member name="T:Microsoft.AspNet.Razor.CodeGenerators.CSharpTagHelperCodeRenderer">
            <summary>
            Renders tag helper rendering code.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.CodeGenerators.CSharpTagHelperCodeRenderer.#ctor(Microsoft.AspNet.Razor.CodeGenerators.Visitors.IChunkVisitor,Microsoft.AspNet.Razor.CodeGenerators.CSharpCodeWriter,Microsoft.AspNet.Razor.CodeGenerators.CodeGeneratorContext)">
            <summary>
            Instantiates a new <see cref="T:Microsoft.AspNet.Razor.CodeGenerators.CSharpTagHelperCodeRenderer"/>.
            </summary>
            <param name="bodyVisitor">The <see cref="T:Microsoft.AspNet.Razor.CodeGenerators.Visitors.IChunkVisitor"/> used to render chunks found in the body.</param>
            <param name="writer">The <see cref="T:Microsoft.AspNet.Razor.CodeGenerators.CSharpCodeWriter"/> used to write code.</param>
            <param name="context">A <see cref="T:Microsoft.AspNet.Razor.CodeGenerators.CodeGeneratorContext"/> instance that contains information about
            the current code generation process.</param>
        </member>
        <member name="M:Microsoft.AspNet.Razor.CodeGenerators.CSharpTagHelperCodeRenderer.RenderTagHelper(Microsoft.AspNet.Razor.Chunks.TagHelperChunk)">
            <summary>
            Renders the code for the given <paramref name="chunk"/>.
            </summary>
            <param name="chunk">A <see cref="T:Microsoft.AspNet.Razor.Chunks.TagHelperChunk"/> to render.</param>
        </member>
        <member name="M:Microsoft.AspNet.Razor.CodeGenerators.CSharpTagHelperCodeRenderer.GenerateUniqueId">
            <summary>
            Generates a unique ID for an HTML element.
            </summary>
            <returns>
            A globally unique ID.
            </returns>
        </member>
        <member name="F:Microsoft.AspNet.Razor.CodeGenerators.ExpressionRenderingMode.WriteToOutput">
             <summary>
             Indicates that expressions should be written to the output stream
             </summary>
             <example>
             If @foo is rendered with WriteToOutput, the code generator would output the following code:
            
             Write(foo);
             </example>
        </member>
        <member name="F:Microsoft.AspNet.Razor.CodeGenerators.ExpressionRenderingMode.InjectCode">
             <summary>
             Indicates that expressions should simply be placed as-is in the code, and the context in which
             the code exists will be used to render it
             </summary>
             <example>
             If @foo is rendered with InjectCode, the code generator would output the following code:
            
             foo
             </example>
        </member>
        <member name="T:Microsoft.AspNet.Razor.CodeGenerators.GeneratedTagHelperContext">
            <summary>
            Contains necessary information for the tag helper code generation process.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.CodeGenerators.GeneratedTagHelperContext.#ctor">
            <summary>
            Instantiates a new instance of the <see cref="T:Microsoft.AspNet.Razor.CodeGenerators.GeneratedTagHelperContext"/> with default values.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.CodeGenerators.GeneratedTagHelperContext.AddHtmlAttributeValuesMethodName">
            <summary>
            The name of the method used to add unbound, complex tag helper attributes to TagHelperExecutionContexts.
            </summary>
            <remarks>
            Method signature should be
            <code>
            public void AddHtmlAttributeValues(
                string attributeName,
                TagHelperExecutionContext executionContext,
                params Microsoft.AspNet.Mvc.Razor.AttributeValue[] values)
            </code>
            </remarks>
        </member>
        <member name="P:Microsoft.AspNet.Razor.CodeGenerators.GeneratedTagHelperContext.CreateTagHelperMethodName">
            <summary>
            The name of the method used to create a tag helper.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.CodeGenerators.GeneratedTagHelperContext.RunnerRunAsyncMethodName">
            <summary>
            The name of the <see cref="P:Microsoft.AspNet.Razor.CodeGenerators.GeneratedTagHelperContext.RunnerTypeName"/> method used to run tag helpers.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.CodeGenerators.GeneratedTagHelperContext.ScopeManagerBeginMethodName">
            <summary>
            The name of the <see cref="P:Microsoft.AspNet.Razor.CodeGenerators.GeneratedTagHelperContext.ExecutionContextTypeName"/> method used to start a scope.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.CodeGenerators.GeneratedTagHelperContext.ScopeManagerEndMethodName">
            <summary>
            The name of the <see cref="P:Microsoft.AspNet.Razor.CodeGenerators.GeneratedTagHelperContext.ExecutionContextTypeName"/> method used to end a scope.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.CodeGenerators.GeneratedTagHelperContext.ExecutionContextAddTagHelperAttributeMethodName">
            <summary>
            The name of the <see cref="P:Microsoft.AspNet.Razor.CodeGenerators.GeneratedTagHelperContext.ExecutionContextTypeName"/> method used to add tag helper attributes.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.CodeGenerators.GeneratedTagHelperContext.ExecutionContextAddMinimizedHtmlAttributeMethodName">
            <summary>
            The name of the <see cref="P:Microsoft.AspNet.Razor.CodeGenerators.GeneratedTagHelperContext.ExecutionContextTypeName"/> method used to add minimized HTML attributes.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.CodeGenerators.GeneratedTagHelperContext.ExecutionContextAddHtmlAttributeMethodName">
            <summary>
            The name of the <see cref="P:Microsoft.AspNet.Razor.CodeGenerators.GeneratedTagHelperContext.ExecutionContextTypeName"/> method used to add HTML attributes.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.CodeGenerators.GeneratedTagHelperContext.ExecutionContextAddMethodName">
            <summary>
            The name of the <see cref="P:Microsoft.AspNet.Razor.CodeGenerators.GeneratedTagHelperContext.ExecutionContextTypeName"/> method used to add tag helpers.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.CodeGenerators.GeneratedTagHelperContext.ExecutionContextOutputPropertyName">
            <summary>
            The property accessor for the tag helper's output.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.CodeGenerators.GeneratedTagHelperContext.FormatInvalidIndexerAssignmentMethodName">
            <summary>
            The name of the method used to format an error message about using an indexer when the tag helper property
            is <c>null</c>.
            </summary>
            <remarks>
            Method signature should be
            <code>
            public string FormatInvalidIndexerAssignment(
                string attributeName,       // Name of the HTML attribute associated with the indexer.
                string tagHelperTypeName,   // Full name of the tag helper type.
                string propertyName)        // Dictionary property in the tag helper.
            </code>
            </remarks>
        </member>
        <member name="P:Microsoft.AspNet.Razor.CodeGenerators.GeneratedTagHelperContext.MarkAsHtmlEncodedMethodName">
            <summary>
            The name of the method used to wrap a <see cref="T:System.String"/> value and mark it as HTML-encoded.
            </summary>
            <remarks>Used together with <see cref="P:Microsoft.AspNet.Razor.CodeGenerators.GeneratedTagHelperContext.ExecutionContextAddHtmlAttributeMethodName"/>.</remarks>
        </member>
        <member name="P:Microsoft.AspNet.Razor.CodeGenerators.GeneratedTagHelperContext.StartTagHelperWritingScopeMethodName">
            <summary>
            The name of the method used to start a new writing scope.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.CodeGenerators.GeneratedTagHelperContext.EndTagHelperWritingScopeMethodName">
            <summary>
            The name of the method used to end a writing scope.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.CodeGenerators.GeneratedTagHelperContext.RunnerTypeName">
            <summary>
            The name of the type used to run tag helpers.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.CodeGenerators.GeneratedTagHelperContext.ScopeManagerTypeName">
            <summary>
            The name of the type used to create scoped <see cref="P:Microsoft.AspNet.Razor.CodeGenerators.GeneratedTagHelperContext.ExecutionContextTypeName"/> instances.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.CodeGenerators.GeneratedTagHelperContext.ExecutionContextTypeName">
            <summary>
            The name of the type describing a specific tag helper scope.
            </summary>
            <remarks>
            Contains information about in-scope tag helpers, HTML attributes, and the tag helpers' output.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNet.Razor.CodeGenerators.GeneratedTagHelperContext.TagHelperContentTypeName">
            <summary>
            The name of the type containing tag helper content.
            </summary>
            <remarks>
            Contains the data returned by EndTagHelperWriteScope().
            </remarks>
        </member>
        <member name="P:Microsoft.AspNet.Razor.CodeGenerators.GeneratedTagHelperContext.WriteTagHelperAsyncMethodName">
            <summary>
            The name of the method used to write <see cref="P:Microsoft.AspNet.Razor.CodeGenerators.GeneratedTagHelperContext.ExecutionContextTypeName"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.CodeGenerators.GeneratedTagHelperContext.WriteTagHelperToAsyncMethodName">
            <summary>
            The name of the method used to write <see cref="P:Microsoft.AspNet.Razor.CodeGenerators.GeneratedTagHelperContext.ExecutionContextTypeName"/> to a specified
            <see cref="T:System.IO.TextWriter"/>.
            </summary>
        </member>
        <member name="T:Microsoft.AspNet.Razor.CodeGenerators.GeneratorResults">
            <summary>
            The results of parsing and generating code for a Razor document.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.CodeGenerators.GeneratorResults.#ctor(Microsoft.AspNet.Razor.ParserResults,Microsoft.AspNet.Razor.CodeGenerators.CodeGeneratorResult,Microsoft.AspNet.Razor.Chunks.ChunkTree)">
            <summary>
            Instantiates a new <see cref="T:Microsoft.AspNet.Razor.CodeGenerators.GeneratorResults"/> instance.
            </summary>
            <param name="parserResults">The results of parsing a document.</param>
            <param name="codeGeneratorResult">The results of generating code for the document.</param>
            <param name="chunkTree">A <see cref="P:Microsoft.AspNet.Razor.CodeGenerators.GeneratorResults.ChunkTree"/> for the document.</param>
        </member>
        <member name="M:Microsoft.AspNet.Razor.CodeGenerators.GeneratorResults.#ctor(Microsoft.AspNet.Razor.Parser.SyntaxTree.Block,System.Collections.Generic.IEnumerable{Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor},Microsoft.AspNet.Razor.ErrorSink,Microsoft.AspNet.Razor.CodeGenerators.CodeGeneratorResult,Microsoft.AspNet.Razor.Chunks.ChunkTree)">
            <summary>
            Instantiates a new <see cref="T:Microsoft.AspNet.Razor.CodeGenerators.GeneratorResults"/> instance.
            </summary>
            <param name="document">The <see cref="T:Microsoft.AspNet.Razor.Parser.SyntaxTree.Block"/> for the syntax tree.</param>
            <param name="tagHelperDescriptors">
            The <see cref="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor"/>s that apply to the current Razor document.
            </param>
            <param name="errorSink">
            The <see cref="T:Microsoft.AspNet.Razor.ErrorSink"/> used to collect <see cref="T:Microsoft.AspNet.Razor.RazorError"/>s encountered when parsing the
            current Razor document.
            </param>
            <param name="codeGeneratorResult">The results of generating code for the document.</param>
            <param name="chunkTree">A <see cref="P:Microsoft.AspNet.Razor.CodeGenerators.GeneratorResults.ChunkTree"/> for the document.</param>
        </member>
        <member name="P:Microsoft.AspNet.Razor.CodeGenerators.GeneratorResults.GeneratedCode">
            <summary>
            The generated code for the document.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.CodeGenerators.GeneratorResults.DesignTimeLineMappings">
            <summary>
            <see cref="T:Microsoft.AspNet.Razor.CodeGenerators.LineMapping"/>s used to project code from a file during design time.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.CodeGenerators.GeneratorResults.ChunkTree">
            <summary>
            A <see cref="T:Microsoft.AspNet.Razor.Chunks.ChunkTree"/> for the document.
            </summary>
        </member>
        <member name="T:Microsoft.AspNet.Razor.CodeGenerators.TagHelperAttributeValueCodeRenderer">
            <summary>
            Renders code for tag helper property initialization.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.CodeGenerators.TagHelperAttributeValueCodeRenderer.RenderAttributeValue(Microsoft.AspNet.Razor.TagHelpers.TagHelperAttributeDescriptor,Microsoft.AspNet.Razor.CodeGenerators.CSharpCodeWriter,Microsoft.AspNet.Razor.CodeGenerators.CodeGeneratorContext,System.Action{Microsoft.AspNet.Razor.CodeGenerators.CSharpCodeWriter},System.Boolean)">
            <summary>
            Called during Razor's code generation process to generate code that instantiates the value of the tag
            helper's property. Last value written should not be or end with a semicolon.
            </summary>
            <param name="attributeDescriptor">
            The <see cref="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperAttributeDescriptor"/> to generate code for.
            </param>
            <param name="writer">The <see cref="T:Microsoft.AspNet.Razor.CodeGenerators.CSharpCodeWriter"/> that's used to write code.</param>
            <param name="context">A <see cref="T:Microsoft.AspNet.Razor.Chunks.Generators.ChunkGeneratorContext"/> instance that contains
            information about the current code generation process.</param>
            <param name="renderAttributeValue">
            <see cref="T:System.Action"/> that renders the raw value of the HTML attribute.
            </param>
            <param name="complexValue">
            Indicates whether or not the source attribute value contains more than simple text. <c>false</c> for plain
            C# expressions e.g. <c>"PropertyName"</c>. <c>true</c> if the attribute value contain at least one in-line
            Razor construct e.g. <c>"@(@readonly)"</c>.
            </param>
        </member>
        <member name="P:Microsoft.AspNet.Razor.CodeGenerators.Visitors.CSharpCodeVisitor.WriteMethodName">
            <summary>
            Method used to write an <see cref="T:System.Object"/> to the current output.
            </summary>
            <remarks>Default is to HTML encode all but a few types.</remarks>
        </member>
        <member name="P:Microsoft.AspNet.Razor.CodeGenerators.Visitors.CSharpCodeVisitor.WriteToMethodName">
            <summary>
            Method used to write an <see cref="T:System.Object"/> to a specified <see cref="T:System.IO.TextWriter"/>.
            </summary>
            <remarks>Default is to HTML encode all but a few types.</remarks>
        </member>
        <member name="T:Microsoft.AspNet.Razor.CodeGenerators.Visitors.CSharpTagHelperAttributeValueVisitor">
            <summary>
            <see cref="T:Microsoft.AspNet.Razor.CodeGenerators.Visitors.CodeVisitor`1"/> that writes code for a non-<see langword="string"/> tag helper
            bound attribute value.
            </summary>
            <remarks>
            Since attribute value is not written out as HTML, does not emit instrumentation. Further this
            <see cref="T:Microsoft.AspNet.Razor.CodeGenerators.Visitors.CodeVisitor`1"/> writes identical code at design- and runtime.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNet.Razor.CodeGenerators.Visitors.CSharpTagHelperAttributeValueVisitor.#ctor(Microsoft.AspNet.Razor.CodeGenerators.CSharpCodeWriter,Microsoft.AspNet.Razor.CodeGenerators.CodeGeneratorContext,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNet.Razor.CodeGenerators.Visitors.CSharpTagHelperAttributeValueVisitor"/> class.
            </summary>
            <param name="writer">The <see cref="T:Microsoft.AspNet.Razor.CodeGenerators.CSharpCodeWriter"/> used to write code.</param>
            <param name="context">
            A <see cref="T:Microsoft.AspNet.Razor.CodeGenerators.CodeGeneratorContext"/> instance that contains information about the current code generation
            process.
            </param>
            <param name="attributeTypeName">
            Full name of the property <see cref="T:System.Type"/> for which this
            <see cref="T:Microsoft.AspNet.Razor.CodeGenerators.Visitors.CSharpTagHelperAttributeValueVisitor"/> is writing the value.
            </param>
        </member>
        <member name="M:Microsoft.AspNet.Razor.CodeGenerators.Visitors.CSharpTagHelperAttributeValueVisitor.Visit(Microsoft.AspNet.Razor.Chunks.ParentChunk)">
            <summary>
            Writes code for the given <paramref name="chunk"/>.
            </summary>
            <param name="chunk">The <see cref="T:Microsoft.AspNet.Razor.Chunks.ParentChunk"/> to render.</param>
            <remarks>
            Tracks code mappings for all children while writing.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNet.Razor.CodeGenerators.Visitors.CSharpTagHelperAttributeValueVisitor.Visit(Microsoft.AspNet.Razor.Chunks.ExpressionBlockChunk)">
            <summary>
            Writes code for the given <paramref name="chunk"/>.
            </summary>
            <param name="chunk">The <see cref="T:Microsoft.AspNet.Razor.Chunks.ExpressionBlockChunk"/> to render.</param>
        </member>
        <member name="M:Microsoft.AspNet.Razor.CodeGenerators.Visitors.CSharpTagHelperAttributeValueVisitor.Visit(Microsoft.AspNet.Razor.Chunks.ExpressionChunk)">
            <summary>
            Writes code for the given <paramref name="chunk"/>.
            </summary>
            <param name="chunk">The <see cref="T:Microsoft.AspNet.Razor.Chunks.ExpressionChunk"/> to render.</param>
        </member>
        <member name="M:Microsoft.AspNet.Razor.CodeGenerators.Visitors.CSharpTagHelperAttributeValueVisitor.Visit(Microsoft.AspNet.Razor.Chunks.LiteralChunk)">
            <summary>
            Writes code for the given <paramref name="chunk"/>.
            </summary>
            <param name="chunk">The <see cref="T:Microsoft.AspNet.Razor.Chunks.LiteralChunk"/> to render.</param>
        </member>
        <member name="M:Microsoft.AspNet.Razor.CodeGenerators.Visitors.CSharpTagHelperAttributeValueVisitor.Visit(Microsoft.AspNet.Razor.Chunks.SectionChunk)">
            <summary>
            Writes code for the given <paramref name="chunk"/>.
            </summary>
            <param name="chunk">The <see cref="T:Microsoft.AspNet.Razor.Chunks.SectionChunk"/> to render.</param>
            <remarks>
            Unconditionally adds a <see cref="T:Microsoft.AspNet.Razor.RazorError"/> to inform user of unexpected <c>@section</c> directive.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNet.Razor.CodeGenerators.Visitors.CSharpTagHelperAttributeValueVisitor.Visit(Microsoft.AspNet.Razor.Chunks.StatementChunk)">
            <summary>
            Writes code for the given <paramref name="chunk"/>.
            </summary>
            <param name="chunk">The <see cref="T:Microsoft.AspNet.Razor.Chunks.StatementChunk"/> to render.</param>
            <remarks>
            Unconditionally adds a <see cref="T:Microsoft.AspNet.Razor.RazorError"/> to inform user of unexpected code block.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNet.Razor.CodeGenerators.Visitors.CSharpTagHelperAttributeValueVisitor.Visit(Microsoft.AspNet.Razor.Chunks.TemplateChunk)">
            <summary>
            Writes code for the given <paramref name="chunk"/>.
            </summary>
            <param name="chunk">The <see cref="T:Microsoft.AspNet.Razor.Chunks.TemplateChunk"/> to render.</param>
            <remarks>
            Unconditionally adds a <see cref="T:Microsoft.AspNet.Razor.RazorError"/> to inform user of unexpected template e.g.
            <c>@&lt;p&gt;paragraph@&lt;/p&gt;</c>.
            </remarks>
        </member>
        <member name="T:Microsoft.AspNet.Razor.CodeGenerators.Visitors.CSharpTagHelperRunnerInitializationVisitor">
            <summary>
            The <see cref="T:Microsoft.AspNet.Razor.CodeGenerators.Visitors.CodeVisitor`1"/> that generates the code to initialize the TagHelperRunner.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.CodeGenerators.Visitors.CSharpTagHelperRunnerInitializationVisitor.#ctor(Microsoft.AspNet.Razor.CodeGenerators.CSharpCodeWriter,Microsoft.AspNet.Razor.CodeGenerators.CodeGeneratorContext)">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.AspNet.Razor.CodeGenerators.Visitors.CSharpTagHelperRunnerInitializationVisitor"/>.
            </summary>
            <param name="writer">The <see cref="T:Microsoft.AspNet.Razor.CodeGenerators.CSharpCodeWriter"/> used to generate code.</param>
            <param name="context">The <see cref="T:Microsoft.AspNet.Razor.CodeGenerators.CodeGeneratorContext"/>.</param>
        </member>
        <member name="M:Microsoft.AspNet.Razor.CodeGenerators.Visitors.CSharpTagHelperRunnerInitializationVisitor.Accept(Microsoft.AspNet.Razor.Chunks.Chunk)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNet.Razor.CodeGenerators.Visitors.CSharpTagHelperRunnerInitializationVisitor.Visit(Microsoft.AspNet.Razor.Chunks.TagHelperChunk)">
            <summary>
            Writes the TagHelperRunner initialization code to the Writer.
            </summary>
            <param name="chunk">The <see cref="T:Microsoft.AspNet.Razor.Chunks.TagHelperChunk"/>.</param>
        </member>
        <member name="M:Microsoft.AspNet.Razor.CodeGenerators.Visitors.CSharpUsingVisitor.Accept(Microsoft.AspNet.Razor.Chunks.Chunk)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNet.Razor.Parser.SyntaxTree.Span.Accept(Microsoft.AspNet.Razor.Parser.ParserVisitor)">
            <summary>
            Accepts the specified visitor
            </summary>
            <remarks>
            Calls the VisitSpan method on the specified visitor, passing in this
            </remarks>
        </member>
        <member name="M:Microsoft.AspNet.Razor.Parser.SyntaxTree.Span.EquivalentTo(Microsoft.AspNet.Razor.Parser.SyntaxTree.SyntaxTreeNode)">
            <summary>
            Checks that the specified span is equivalent to the other in that it has the same start point and content.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.Parser.SyntaxTree.SyntaxTreeNode.IsBlock">
            <summary>
            Returns true if this element is a block (to avoid casting)
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.Parser.SyntaxTree.SyntaxTreeNode.Length">
            <summary>
            The length of all the content contained in this node
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.Parser.SyntaxTree.SyntaxTreeNode.Start">
            <summary>
            The start point of this node
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.Parser.SyntaxTree.SyntaxTreeNode.Accept(Microsoft.AspNet.Razor.Parser.ParserVisitor)">
            <summary>
            Accepts a parser visitor, calling the appropriate visit method and passing in this instance
            </summary>
            <param name="visitor">The visitor to accept</param>
        </member>
        <member name="M:Microsoft.AspNet.Razor.Parser.SyntaxTree.SyntaxTreeNode.EquivalentTo(Microsoft.AspNet.Razor.Parser.SyntaxTree.SyntaxTreeNode)">
            <summary>
            Determines if the specified node is equivalent to this node
            </summary>
            <param name="node">The node to compare this node with</param>
            <returns>
            true if the provided node has all the same content and metadata, though the specific quantity and type of
            symbols may be different.
            </returns>
        </member>
        <member name="M:Microsoft.AspNet.Razor.Parser.SyntaxTree.SyntaxTreeNode.GetEquivalenceHash">
            <summary>
            Determines a hash code for the <see cref="T:Microsoft.AspNet.Razor.Parser.SyntaxTree.SyntaxTreeNode"/> using only information relevant in
            <see cref="M:Microsoft.AspNet.Razor.Parser.SyntaxTree.SyntaxTreeNode.EquivalentTo(Microsoft.AspNet.Razor.Parser.SyntaxTree.SyntaxTreeNode)"/> comparisons.
            </summary>
            <returns>
            A hash code for the <see cref="T:Microsoft.AspNet.Razor.Parser.SyntaxTree.SyntaxTreeNode"/> using only information relevant in
            <see cref="M:Microsoft.AspNet.Razor.Parser.SyntaxTree.SyntaxTreeNode.EquivalentTo(Microsoft.AspNet.Razor.Parser.SyntaxTree.SyntaxTreeNode)"/> comparisons.
            </returns>
        </member>
        <member name="M:Microsoft.AspNet.Razor.Parser.HtmlMarkupParser.ScanTagInDocumentContext">
            <summary>
            Reads the content of a tag (if present) in the MarkupDocument (or MarkupSection) context,
            where we don't care about maintaining a stack of tags.
            </summary>
        </member>
        <member name="T:Microsoft.AspNet.Razor.Parser.ISyntaxTreeRewriter">
            <summary>
            Contract for rewriting a syntax tree.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.Parser.ISyntaxTreeRewriter.Rewrite(Microsoft.AspNet.Razor.Parser.RewritingContext)">
            <summary>
            Rewrites the provided <paramref name="context"/>s <see cref= "P:Microsoft.AspNet.Razor.Parser.RewritingContext.SyntaxTree" />.
            </summary>
            <param name="context">Contains information on the rewriting of the syntax tree.</param>
            <remarks>
            To modify the syntax tree replace the <paramref name="context"/>s <see cref="P:Microsoft.AspNet.Razor.Parser.RewritingContext.SyntaxTree"/>.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNet.Razor.Parser.ParserContext.StartBlock(Microsoft.AspNet.Razor.Parser.SyntaxTree.BlockType)">
            <summary>
            Starts a block of the specified type
            </summary>
            <param name="blockType">The type of the block to start</param>
        </member>
        <member name="M:Microsoft.AspNet.Razor.Parser.ParserContext.StartBlock">
            <summary>
            Starts a block
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.Parser.ParserContext.EndBlock">
            <summary>
            Ends the current block
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.Parser.ParserContext.IsWithin(Microsoft.AspNet.Razor.Parser.SyntaxTree.BlockType)">
            <summary>
            Gets a boolean indicating if any of the ancestors of the current block is of the specified type
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.Parser.RazorParser.#ctor(Microsoft.AspNet.Razor.Parser.ParserBase,Microsoft.AspNet.Razor.Parser.ParserBase,Microsoft.AspNet.Razor.TagHelpers.ITagHelperDescriptorResolver)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNet.Razor.Parser.RazorParser"/>.
            </summary>
            <param name="codeParser">The <see cref="T:Microsoft.AspNet.Razor.Parser.ParserBase"/> used for parsing code content.</param>
            <param name="markupParser">The <see cref="T:Microsoft.AspNet.Razor.Parser.ParserBase"/> used for parsing markup content.</param>
            <param name="tagHelperDescriptorResolver">The <see cref="T:Microsoft.AspNet.Razor.TagHelpers.ITagHelperDescriptorResolver"/> used to resolve
            <see cref="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor"/>s.</param>
        </member>
        <member name="M:Microsoft.AspNet.Razor.Parser.RazorParser.#ctor(Microsoft.AspNet.Razor.Parser.RazorParser)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNet.Razor.Parser.RazorParser"/> from the specified <paramref name="parser" />.
            </summary>
            <param name="parser">The <see cref="T:Microsoft.AspNet.Razor.Parser.RazorParser"/> to copy values from.</param>
        </member>
        <member name="P:Microsoft.AspNet.Razor.Parser.RazorParser.TagHelperDescriptorResolver">
            <summary>
            Gets the <see cref="T:Microsoft.AspNet.Razor.TagHelpers.ITagHelperDescriptorResolver"/> used to resolve <see cref="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor"/>s.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.Parser.RazorParser.GetTagHelperDescriptors(Microsoft.AspNet.Razor.Parser.SyntaxTree.Block,Microsoft.AspNet.Razor.ErrorSink)">
            <summary>
            Returns a sequence of <see cref="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor"/>s for tag helpers that are registered in the
            specified <paramref name="documentRoot"/>.
            </summary>
            <param name="documentRoot">The <see cref="T:Microsoft.AspNet.Razor.Parser.SyntaxTree.Block"/> to scan for tag helper registrations in.</param>
            <param name="errorSink">Used to manage <see cref="T:Microsoft.AspNet.Razor.RazorError"/>s encountered during the Razor parsing
            phase.</param>
            <returns><see cref="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor"/>s that are applicable to the <paramref name="documentRoot"/>
            </returns>
        </member>
        <member name="T:Microsoft.AspNet.Razor.Parser.RewritingContext">
            <summary>
            Informational class for rewriting a syntax tree.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.Parser.RewritingContext.#ctor(Microsoft.AspNet.Razor.Parser.SyntaxTree.Block,Microsoft.AspNet.Razor.ErrorSink)">
            <summary>
            Instantiates a new <see cref="T:Microsoft.AspNet.Razor.Parser.RewritingContext"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.Parser.RewritingContext.SyntaxTree">
            <summary>
            The documents syntax tree.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.Parser.TokenizerBackedParser`3.PutBack(System.Collections.Generic.IEnumerable{`1})">
            <summary>
            Put the specified symbols back in the input stream. The provided list MUST be in the ORDER THE SYMBOLS WERE READ. The
            list WILL be reversed and the Putback(TSymbol) will be called on each item.
            </summary>
            <remarks>
            If a document contains symbols: a, b, c, d, e, f
            and AcceptWhile or AcceptUntil is used to collect until d
            the list returned by AcceptWhile/Until will contain: a, b, c IN THAT ORDER
            that is the correct format for providing to this method. The caller of this method would,
            in that case, want to put c, b and a back into the stream, so "a, b, c" is the CORRECT order
            </remarks>
        </member>
        <member name="T:Microsoft.AspNet.Razor.Parser.TagHelpers.TagHelperBlock">
            <summary>
            A <see cref="T:Microsoft.AspNet.Razor.Parser.SyntaxTree.Block"/> that reprents a special HTML element.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.Parser.TagHelpers.TagHelperBlock.#ctor(Microsoft.AspNet.Razor.Parser.TagHelpers.TagHelperBlockBuilder)">
            <summary>
            Instantiates a new instance of a <see cref="T:Microsoft.AspNet.Razor.Parser.TagHelpers.TagHelperBlock"/>.
            </summary>
            <param name="source">A <see cref="T:Microsoft.AspNet.Razor.Parser.TagHelpers.TagHelperBlockBuilder"/> used to construct a valid
            <see cref="T:Microsoft.AspNet.Razor.Parser.TagHelpers.TagHelperBlock"/>.</param>
        </member>
        <member name="P:Microsoft.AspNet.Razor.Parser.TagHelpers.TagHelperBlock.SourceStartTag">
            <summary>
            Gets the unrewritten source start tag.
            </summary>
            <remarks>This is used by design time to properly format <see cref="T:Microsoft.AspNet.Razor.Parser.TagHelpers.TagHelperBlock"/>s.</remarks>
        </member>
        <member name="P:Microsoft.AspNet.Razor.Parser.TagHelpers.TagHelperBlock.SourceEndTag">
            <summary>
            Gets the unrewritten source end tag.
            </summary>
            <remarks>This is used by design time to properly format <see cref="T:Microsoft.AspNet.Razor.Parser.TagHelpers.TagHelperBlock"/>s.</remarks>
        </member>
        <member name="P:Microsoft.AspNet.Razor.Parser.TagHelpers.TagHelperBlock.TagMode">
            <summary>
            Gets the HTML syntax of the element in the Razor source.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.Parser.TagHelpers.TagHelperBlock.Descriptors">
            <summary>
            <see cref="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor"/>s for the HTML element.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.Parser.TagHelpers.TagHelperBlock.Attributes">
            <summary>
            The HTML attributes.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.Parser.TagHelpers.TagHelperBlock.Start">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.AspNet.Razor.Parser.TagHelpers.TagHelperBlock.TagName">
            <summary>
            The HTML tag name.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.Parser.TagHelpers.TagHelperBlock.ToString">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNet.Razor.Parser.TagHelpers.TagHelperBlock.Equals(Microsoft.AspNet.Razor.Parser.TagHelpers.TagHelperBlock)">
            <summary>
            Determines whether two <see cref="T:Microsoft.AspNet.Razor.Parser.TagHelpers.TagHelperBlock"/>s are equal by comparing the <see cref="P:Microsoft.AspNet.Razor.Parser.TagHelpers.TagHelperBlock.TagName"/>,
            <see cref="P:Microsoft.AspNet.Razor.Parser.TagHelpers.TagHelperBlock.Attributes"/>, <see cref="P:Microsoft.AspNet.Razor.Parser.SyntaxTree.Block.Type"/>, <see cref="P:Microsoft.AspNet.Razor.Parser.SyntaxTree.Block.ChunkGenerator"/> and
            <see cref="P:Microsoft.AspNet.Razor.Parser.SyntaxTree.Block.Children"/>.
            </summary>
            <param name="other">The <see cref="T:Microsoft.AspNet.Razor.Parser.TagHelpers.TagHelperBlock"/> to check equality against.</param>
            <returns>
            <c>true</c> if the current <see cref="T:Microsoft.AspNet.Razor.Parser.TagHelpers.TagHelperBlock"/> is equivalent to the given
            <paramref name="other"/>, <c>false</c> otherwise.
            </returns>
        </member>
        <member name="M:Microsoft.AspNet.Razor.Parser.TagHelpers.TagHelperBlock.GetHashCode">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNet.Razor.Parser.TagHelpers.TagHelperBlockBuilder">
            <summary>
            A <see cref="T:Microsoft.AspNet.Razor.Parser.SyntaxTree.BlockBuilder"/> used to create <see cref="T:Microsoft.AspNet.Razor.Parser.TagHelpers.TagHelperBlock"/>s.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.Parser.TagHelpers.TagHelperBlockBuilder.#ctor(Microsoft.AspNet.Razor.Parser.TagHelpers.TagHelperBlock)">
            <summary>
            Instantiates a new <see cref="T:Microsoft.AspNet.Razor.Parser.TagHelpers.TagHelperBlockBuilder"/> instance based on the given
            <paramref name="original"/>.
            </summary>
            <param name="original">The original <see cref="T:Microsoft.AspNet.Razor.Parser.TagHelpers.TagHelperBlock"/> to copy data from.</param>
        </member>
        <member name="M:Microsoft.AspNet.Razor.Parser.TagHelpers.TagHelperBlockBuilder.#ctor(System.String,Microsoft.AspNet.Razor.Runtime.TagHelpers.TagMode,Microsoft.AspNet.Razor.SourceLocation,System.Collections.Generic.IList{System.Collections.Generic.KeyValuePair{System.String,Microsoft.AspNet.Razor.Parser.SyntaxTree.SyntaxTreeNode}},System.Collections.Generic.IEnumerable{Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor})">
            <summary>
            Instantiates a new instance of the <see cref="T:Microsoft.AspNet.Razor.Parser.TagHelpers.TagHelperBlockBuilder"/> class
            with the provided <paramref name="tagName"/> and derives its <see cref="P:Microsoft.AspNet.Razor.Parser.TagHelpers.TagHelperBlockBuilder.Attributes"/>
            and <see cref="P:Microsoft.AspNet.Razor.Parser.SyntaxTree.BlockBuilder.Type"/> from the <paramref name="startTag"/>.
            </summary>
            <param name="tagName">An HTML tag name.</param>
            <param name="tagMode">HTML syntax of the element in the Razor source.</param>
            <param name="start">Starting location of the <see cref="T:Microsoft.AspNet.Razor.Parser.TagHelpers.TagHelperBlock"/>.</param>
            <param name="attributes">Attributes of the <see cref="T:Microsoft.AspNet.Razor.Parser.TagHelpers.TagHelperBlock"/>.</param>
            <param name="descriptors">The <see cref="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor"/>s associated with the current HTML
            tag.</param>
        </member>
        <member name="P:Microsoft.AspNet.Razor.Parser.TagHelpers.TagHelperBlockBuilder.SourceStartTag">
            <summary>
            Gets or sets the unrewritten source start tag.
            </summary>
            <remarks>This is used by design time to properly format <see cref="T:Microsoft.AspNet.Razor.Parser.TagHelpers.TagHelperBlock"/>s.</remarks>
        </member>
        <member name="P:Microsoft.AspNet.Razor.Parser.TagHelpers.TagHelperBlockBuilder.SourceEndTag">
            <summary>
            Gets or sets the unrewritten source end tag.
            </summary>
            <remarks>This is used by design time to properly format <see cref="T:Microsoft.AspNet.Razor.Parser.TagHelpers.TagHelperBlock"/>s.</remarks>
        </member>
        <member name="P:Microsoft.AspNet.Razor.Parser.TagHelpers.TagHelperBlockBuilder.TagMode">
            <summary>
            Gets the HTML syntax of the element in the Razor source.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.Parser.TagHelpers.TagHelperBlockBuilder.Descriptors">
            <summary>
            <see cref="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor"/>s for the HTML element.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.Parser.TagHelpers.TagHelperBlockBuilder.Attributes">
            <summary>
            The HTML attributes.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.Parser.TagHelpers.TagHelperBlockBuilder.TagName">
            <summary>
            The HTML tag name.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.Parser.TagHelpers.TagHelperBlockBuilder.Build">
            <summary>
            Constructs a new <see cref="T:Microsoft.AspNet.Razor.Parser.TagHelpers.TagHelperBlock"/>.
            </summary>
            <returns>A <see cref="T:Microsoft.AspNet.Razor.Parser.TagHelpers.TagHelperBlock"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNet.Razor.Parser.TagHelpers.TagHelperBlockBuilder.Reset">
            <inheritdoc />
            <remarks>
            Sets the <see cref="P:Microsoft.AspNet.Razor.Parser.TagHelpers.TagHelperBlockBuilder.TagName"/> to <c>null</c> and clears the <see cref="P:Microsoft.AspNet.Razor.Parser.TagHelpers.TagHelperBlockBuilder.Attributes"/>.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNet.Razor.Parser.TagHelpers.TagHelperBlockBuilder.Start">
            <summary>
            The starting <see cref="T:Microsoft.AspNet.Razor.SourceLocation"/> of the tag helper.
            </summary>
        </member>
        <member name="T:Microsoft.AspNet.Razor.Parser.TagHelpers.TagHelperDirectiveSpanVisitor">
            <summary>
            A <see cref="T:Microsoft.AspNet.Razor.Parser.ParserVisitor"/> that generates <see cref="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor"/>s from
            tag helper chunk generators in a Razor document.
            </summary>
        </member>
        <member name="E:Microsoft.AspNet.Razor.Editor.BackgroundParser.ResultsReady">
            <summary>
            Fired on the main thread.
            </summary>
        </member>
        <member name="T:Microsoft.AspNet.Razor.Editor.EditorHints">
            <summary>
            Used within <see cref="F:SpanEditHandler.EditorHints"/>.
            </summary>
        </member>
        <member name="F:Microsoft.AspNet.Razor.Editor.EditorHints.None">
            <summary>
            The default (Markup or Code) editor behavior for Statement completion should be used.
            Editors can always use the default behavior, even if the span is labeled with a different CompletionType.
            </summary>
        </member>
        <member name="F:Microsoft.AspNet.Razor.Editor.EditorHints.VirtualPath">
            <summary>
            Indicates that Virtual Path completion should be used for this span if the editor supports it.
            Editors need not support this mode of completion, and will use the default (<see cref="F:EditorHints.None"/>) behavior
            if they do not support it.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.Editor.SpanEditHandler.EditorHints">
            <summary>
            Provides a set of hints to editors which may be manipulating the document in which this span is located.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.Editor.SpanEditHandler.IsEndInsertion(Microsoft.AspNet.Razor.Parser.SyntaxTree.Span,Microsoft.AspNet.Razor.Text.TextChange)">
            <summary>
            Returns true if the specified change is an insertion of text at the end of this span.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.Editor.SpanEditHandler.IsEndDeletion(Microsoft.AspNet.Razor.Parser.SyntaxTree.Span,Microsoft.AspNet.Razor.Text.TextChange)">
            <summary>
            Returns true if the specified change is an insertion of text at the end of this span.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.Editor.SpanEditHandler.IsEndReplace(Microsoft.AspNet.Razor.Parser.SyntaxTree.Span,Microsoft.AspNet.Razor.Text.TextChange)">
            <summary>
            Returns true if the specified change is a replacement of text at the end of this span.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.Editor.SpanEditHandler.GetOldText(Microsoft.AspNet.Razor.Parser.SyntaxTree.Span,Microsoft.AspNet.Razor.Text.TextChange)">
            <summary>
            Returns the old text referenced by the change.
            </summary>
            <remarks>
            If the content has already been updated by applying the change, this data will be _invalid_
            </remarks>
        </member>
        <member name="P:Microsoft.AspNet.Razor.CommonResources.Argument_Cannot_Be_Null_Or_Empty">
            <summary>
            Value cannot be null or an empty string.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.CommonResources.FormatArgument_Cannot_Be_Null_Or_Empty">
            <summary>
            Value cannot be null or an empty string.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.CommonResources.Argument_Must_Be_Between">
            <summary>
            Value must be between {0} and {1}.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.CommonResources.FormatArgument_Must_Be_Between(System.Object,System.Object)">
            <summary>
            Value must be between {0} and {1}.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.CommonResources.Argument_Must_Be_Enum_Member">
            <summary>
            Value must be a value from the "{0}" enumeration.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.CommonResources.FormatArgument_Must_Be_Enum_Member(System.Object)">
            <summary>
            Value must be a value from the "{0}" enumeration.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.CommonResources.Argument_Must_Be_GreaterThan">
            <summary>
            Value must be greater than {0}.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.CommonResources.FormatArgument_Must_Be_GreaterThan(System.Object)">
            <summary>
            Value must be greater than {0}.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.CommonResources.Argument_Must_Be_GreaterThanOrEqualTo">
            <summary>
            Value must be greater than or equal to {0}.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.CommonResources.FormatArgument_Must_Be_GreaterThanOrEqualTo(System.Object)">
            <summary>
            Value must be greater than or equal to {0}.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.CommonResources.Argument_Must_Be_LessThan">
            <summary>
            Value must be less than {0}.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.CommonResources.FormatArgument_Must_Be_LessThan(System.Object)">
            <summary>
            Value must be less than {0}.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.CommonResources.Argument_Must_Be_LessThanOrEqualTo">
            <summary>
            Value must be less than or equal to {0}.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.CommonResources.FormatArgument_Must_Be_LessThanOrEqualTo(System.Object)">
            <summary>
            Value must be less than or equal to {0}.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.CommonResources.Argument_Must_Be_Null_Or_Non_Empty">
            <summary>
            Value cannot be an empty string. It must either be null or a non-empty string.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.CommonResources.FormatArgument_Must_Be_Null_Or_Non_Empty">
            <summary>
            Value cannot be an empty string. It must either be null or a non-empty string.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.ActiveParser_Must_Be_Code_Or_Markup_Parser">
            <summary>
            The active parser must be the same as either the markup or code parser.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatActiveParser_Must_Be_Code_Or_Markup_Parser">
            <summary>
            The active parser must be the same as either the markup or code parser.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.BlockName_Code">
            <summary>
            code
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatBlockName_Code">
            <summary>
            code
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.BlockName_ExplicitExpression">
            <summary>
            explicit expression
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatBlockName_ExplicitExpression">
            <summary>
            explicit expression
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.CancelBacktrack_Must_Be_Called_Within_Lookahead">
            <summary>
            The "CancelBacktrack" method can be called only while in a look-ahead process started with the "BeginLookahead" method.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatCancelBacktrack_Must_Be_Called_Within_Lookahead">
            <summary>
            The "CancelBacktrack" method can be called only while in a look-ahead process started with the "BeginLookahead" method.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.EndBlock_Called_Without_Matching_StartBlock">
            <summary>
            "EndBlock" was called without a matching call to "StartBlock".
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatEndBlock_Called_Without_Matching_StartBlock">
            <summary>
            "EndBlock" was called without a matching call to "StartBlock".
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.ParseError_AtInCode_Must_Be_Followed_By_Colon_Paren_Or_Identifier_Start">
            <summary>
            The "@" character must be followed by a ":", "(", or a C# identifier.  If you intended to switch to markup, use an HTML start tag, for example:
            
            @if(isLoggedIn) {
                &lt;p&gt;Hello, @user!&lt;/p&gt;
            }
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatParseError_AtInCode_Must_Be_Followed_By_Colon_Paren_Or_Identifier_Start">
            <summary>
            The "@" character must be followed by a ":", "(", or a C# identifier.  If you intended to switch to markup, use an HTML start tag, for example:
            
            @if(isLoggedIn) {
                &lt;p&gt;Hello, @user!&lt;/p&gt;
            }
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.ParseError_BlockComment_Not_Terminated">
            <summary>
            End of file was reached before the end of the block comment.  All comments started with "/*" sequence must be terminated with a matching "*/" sequence.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatParseError_BlockComment_Not_Terminated">
            <summary>
            End of file was reached before the end of the block comment.  All comments started with "/*" sequence must be terminated with a matching "*/" sequence.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.ParseError_Expected_CloseBracket_Before_EOF">
            <summary>
            An opening "{0}" is missing the corresponding closing "{1}".
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatParseError_Expected_CloseBracket_Before_EOF(System.Object,System.Object)">
            <summary>
            An opening "{0}" is missing the corresponding closing "{1}".
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.ParseError_Expected_EndOfBlock_Before_EOF">
            <summary>
            The {0} block is missing a closing "{1}" character.  Make sure you have a matching "{1}" character for all the "{2}" characters within this block, and that none of the "{1}" characters are being interpreted as markup.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatParseError_Expected_EndOfBlock_Before_EOF(System.Object,System.Object,System.Object)">
            <summary>
            The {0} block is missing a closing "{1}" character.  Make sure you have a matching "{1}" character for all the "{2}" characters within this block, and that none of the "{1}" characters are being interpreted as markup.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.ParseError_Expected_X">
            <summary>
            Expected "{0}".
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatParseError_Expected_X(System.Object)">
            <summary>
            Expected "{0}".
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.ParseError_InlineMarkup_Blocks_Cannot_Be_Nested">
            <summary>
            Inline markup blocks (@&lt;p&gt;Content&lt;/p&gt;) cannot be nested.  Only one level of inline markup is allowed.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatParseError_InlineMarkup_Blocks_Cannot_Be_Nested">
            <summary>
            Inline markup blocks (@&lt;p&gt;Content&lt;/p&gt;) cannot be nested.  Only one level of inline markup is allowed.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.ParseError_MarkupBlock_Must_Start_With_Tag">
            <summary>
            Markup in a code block must start with a tag and all start tags must be matched with end tags.  Do not use unclosed tags like "&lt;br&gt;".  Instead use self-closing tags like "&lt;br/&gt;".
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatParseError_MarkupBlock_Must_Start_With_Tag">
            <summary>
            Markup in a code block must start with a tag and all start tags must be matched with end tags.  Do not use unclosed tags like "&lt;br&gt;".  Instead use self-closing tags like "&lt;br/&gt;".
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.ParseError_MissingEndTag">
            <summary>
            The "{0}" element was not closed.  All elements must be either self-closing or have a matching end tag.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatParseError_MissingEndTag(System.Object)">
            <summary>
            The "{0}" element was not closed.  All elements must be either self-closing or have a matching end tag.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.ParseError_MissingOpenBraceAfterSection">
            <summary>
            Sections cannot be empty.  The "@section" keyword must be followed by a block of markup surrounded by "{}".  For example:
            
            @section Sidebar {
               &lt;!-- Markup and text goes here --&gt;
            }
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatParseError_MissingOpenBraceAfterSection">
            <summary>
            Sections cannot be empty.  The "@section" keyword must be followed by a block of markup surrounded by "{}".  For example:
            
            @section Sidebar {
               &lt;!-- Markup and text goes here --&gt;
            }
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.ParseError_NamespaceImportAndTypeAlias_Cannot_Exist_Within_CodeBlock">
            <summary>
            Namespace imports and type aliases cannot be placed within code blocks.  They must immediately follow an "@" character in markup.  It is recommended that you put them at the top of the page, as in the following example:
            
            @using System.Drawing;
            @{
                // OK here to use types from System.Drawing in the page.
            }
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatParseError_NamespaceImportAndTypeAlias_Cannot_Exist_Within_CodeBlock">
            <summary>
            Namespace imports and type aliases cannot be placed within code blocks.  They must immediately follow an "@" character in markup.  It is recommended that you put them at the top of the page, as in the following example:
            
            @using System.Drawing;
            @{
                // OK here to use types from System.Drawing in the page.
            }
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.ParseError_SingleLine_ControlFlowStatements_Not_Allowed">
            <summary>
            Expected a "{0}" but found a "{1}".  Block statements must be enclosed in "{{" and "}}".  You cannot use single-statement control-flow statements in CSHTML pages. For example, the following is not allowed:
            
            @if(isLoggedIn)
                &lt;p&gt;Hello, @user&lt;/p&gt;
            
            Instead, wrap the contents of the block in "{{}}":
            
            @if(isLoggedIn) {{
                &lt;p&gt;Hello, @user&lt;/p&gt;
            }}
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatParseError_SingleLine_ControlFlowStatements_Not_Allowed(System.Object,System.Object)">
            <summary>
            Expected a "{0}" but found a "{1}".  Block statements must be enclosed in "{{" and "}}".  You cannot use single-statement control-flow statements in CSHTML pages. For example, the following is not allowed:
            
            @if(isLoggedIn)
                &lt;p&gt;Hello, @user&lt;/p&gt;
            
            Instead, wrap the contents of the block in "{{}}":
            
            @if(isLoggedIn) {{
                &lt;p&gt;Hello, @user&lt;/p&gt;
            }}
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.ParseError_UnexpectedEndTag">
            <summary>
            Encountered end tag "{0}" with no matching start tag.  Are your start/end tags properly balanced?
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatParseError_UnexpectedEndTag(System.Object)">
            <summary>
            Encountered end tag "{0}" with no matching start tag.  Are your start/end tags properly balanced?
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.ParseError_Unexpected_Character_At_Section_Name_Start">
            <summary>
            Unexpected {0} after section keyword.  Section names must start with an "_" or alphabetic character, and the remaining characters must be either "_" or alphanumeric.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatParseError_Unexpected_Character_At_Section_Name_Start(System.Object)">
            <summary>
            Unexpected {0} after section keyword.  Section names must start with an "_" or alphabetic character, and the remaining characters must be either "_" or alphanumeric.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.ParseError_Unexpected_Character_At_Start_Of_CodeBlock_CS">
            <summary>
            "{0}" is not valid at the start of a code block.  Only identifiers, keywords, comments, "(" and "{{" are valid.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatParseError_Unexpected_Character_At_Start_Of_CodeBlock_CS(System.Object)">
            <summary>
            "{0}" is not valid at the start of a code block.  Only identifiers, keywords, comments, "(" and "{{" are valid.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.ParseError_UnfinishedTag">
            <summary>
            End of file or an unexpected character was reached before the "{0}" tag could be parsed.  Elements inside markup blocks must be complete. They must either be self-closing ("&lt;br /&gt;") or have matching end tags ("&lt;p&gt;Hello&lt;/p&gt;").  If you intended to display a "&lt;" character, use the "&amp;lt;" HTML entity.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatParseError_UnfinishedTag(System.Object)">
            <summary>
            End of file or an unexpected character was reached before the "{0}" tag could be parsed.  Elements inside markup blocks must be complete. They must either be self-closing ("&lt;br /&gt;") or have matching end tags ("&lt;p&gt;Hello&lt;/p&gt;").  If you intended to display a "&lt;" character, use the "&amp;lt;" HTML entity.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.ParseError_Unterminated_String_Literal">
            <summary>
            Unterminated string literal.  Strings that start with a quotation mark (") must be terminated before the end of the line.  However, strings that start with @ and a quotation mark (@") can span multiple lines.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatParseError_Unterminated_String_Literal">
            <summary>
            Unterminated string literal.  Strings that start with a quotation mark (") must be terminated before the end of the line.  However, strings that start with @ and a quotation mark (@") can span multiple lines.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.SectionExample_CS">
            <summary>
            @section Header { ... }
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatSectionExample_CS">
            <summary>
            @section Header { ... }
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.ParseError_TextTagCannotContainAttributes">
            <summary>
            "&lt;text&gt;" and "&lt;/text&gt;" tags cannot contain attributes.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatParseError_TextTagCannotContainAttributes">
            <summary>
            "&lt;text&gt;" and "&lt;/text&gt;" tags cannot contain attributes.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.ParseError_Unexpected_WhiteSpace_At_Start_Of_CodeBlock_CS">
            <summary>
            A space or line break was encountered after the "@" character.  Only valid identifiers, keywords, comments, "(" and "{" are valid at the start of a code block and they must occur immediately following "@" with no space in between.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatParseError_Unexpected_WhiteSpace_At_Start_Of_CodeBlock_CS">
            <summary>
            A space or line break was encountered after the "@" character.  Only valid identifiers, keywords, comments, "(" and "{" are valid at the start of a code block and they must occur immediately following "@" with no space in between.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.ParseError_InheritsKeyword_Must_Be_Followed_By_TypeName">
            <summary>
            The 'inherits' keyword must be followed by a type name on the same line.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatParseError_InheritsKeyword_Must_Be_Followed_By_TypeName">
            <summary>
            The 'inherits' keyword must be followed by a type name on the same line.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.ParseError_OuterTagMissingName">
            <summary>
            Outer tag is missing a name. The first character of a markup block must be an HTML tag with a valid name.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatParseError_OuterTagMissingName">
            <summary>
            Outer tag is missing a name. The first character of a markup block must be an HTML tag with a valid name.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.ParseError_RazorComment_Not_Terminated">
            <summary>
            End of file was reached before the end of the block comment.  All comments that start with the "@*" sequence must be terminated with a matching "*@" sequence.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatParseError_RazorComment_Not_Terminated">
            <summary>
            End of file was reached before the end of the block comment.  All comments that start with the "@*" sequence must be terminated with a matching "*@" sequence.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.ErrorComponent_Character">
            <summary>
            "{0}" character
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatErrorComponent_Character(System.Object)">
            <summary>
            "{0}" character
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.ErrorComponent_EndOfFile">
            <summary>
            end of file
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatErrorComponent_EndOfFile">
            <summary>
            end of file
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.ErrorComponent_Whitespace">
            <summary>
            space or line break
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatErrorComponent_Whitespace">
            <summary>
            space or line break
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.ParseError_Unexpected_EndOfFile_At_Start_Of_CodeBlock">
            <summary>
            End-of-file was found after the "@" character.  "@" must be followed by a valid code block.  If you want to output an "@", escape it using the sequence: "@@"
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatParseError_Unexpected_EndOfFile_At_Start_Of_CodeBlock">
            <summary>
            End-of-file was found after the "@" character.  "@" must be followed by a valid code block.  If you want to output an "@", escape it using the sequence: "@@"
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.Structure_Member_CannotBeNull">
            <summary>
            The {0} property of the {1} structure cannot be null.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatStructure_Member_CannotBeNull(System.Object,System.Object)">
            <summary>
            The {0} property of the {1} structure cannot be null.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.Parser_Context_Not_Set">
            <summary>
            Parser was started with a null Context property.  The Context property must be set BEFORE calling any methods on the parser.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatParser_Context_Not_Set">
            <summary>
            Parser was started with a null Context property.  The Context property must be set BEFORE calling any methods on the parser.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.ParseError_ReservedWord">
            <summary>
            "{0}" is a reserved word and cannot be used in implicit expressions.  An explicit expression ("@()") must be used.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatParseError_ReservedWord(System.Object)">
            <summary>
            "{0}" is a reserved word and cannot be used in implicit expressions.  An explicit expression ("@()") must be used.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.Tokenizer_CannotResumeSymbolUnlessIsPrevious">
            <summary>
            Cannot resume this symbol. Only the symbol immediately preceding the current one can be resumed.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatTokenizer_CannotResumeSymbolUnlessIsPrevious">
            <summary>
            Cannot resume this symbol. Only the symbol immediately preceding the current one can be resumed.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.ParserContext_NoCurrentBlock">
            <summary>
            Cannot finish span, there is no current block. Call StartBlock at least once before finishing a span
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatParserContext_NoCurrentBlock">
            <summary>
            Cannot finish span, there is no current block. Call StartBlock at least once before finishing a span
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.ParserContext_CannotCompleteTree_OutstandingBlocks">
            <summary>
            Cannot complete the tree, there are still open blocks.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatParserContext_CannotCompleteTree_OutstandingBlocks">
            <summary>
            Cannot complete the tree, there are still open blocks.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.ParserContext_CannotCompleteTree_NoRootBlock">
            <summary>
            Cannot complete the tree, StartBlock must be called at least once.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatParserContext_CannotCompleteTree_NoRootBlock">
            <summary>
            Cannot complete the tree, StartBlock must be called at least once.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.ParserContext_ParseComplete">
            <summary>
            Cannot complete action, the parser has finished. Only CompleteParse can be called to extract the final parser results after the parser has finished
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatParserContext_ParseComplete">
            <summary>
            Cannot complete action, the parser has finished. Only CompleteParse can be called to extract the final parser results after the parser has finished
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.Block_Type_Not_Specified">
            <summary>
            Block cannot be built because a Type has not been specified in the BlockBuilder
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatBlock_Type_Not_Specified">
            <summary>
            Block cannot be built because a Type has not been specified in the BlockBuilder
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.CSharpSymbol_CharacterLiteral">
            <summary>
            &lt;&lt;character literal&gt;&gt;
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatCSharpSymbol_CharacterLiteral">
            <summary>
            &lt;&lt;character literal&gt;&gt;
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.CSharpSymbol_Comment">
            <summary>
            &lt;&lt;comment&gt;&gt;
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatCSharpSymbol_Comment">
            <summary>
            &lt;&lt;comment&gt;&gt;
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.CSharpSymbol_Identifier">
            <summary>
            &lt;&lt;identifier&gt;&gt;
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatCSharpSymbol_Identifier">
            <summary>
            &lt;&lt;identifier&gt;&gt;
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.CSharpSymbol_IntegerLiteral">
            <summary>
            &lt;&lt;integer literal&gt;&gt;
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatCSharpSymbol_IntegerLiteral">
            <summary>
            &lt;&lt;integer literal&gt;&gt;
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.CSharpSymbol_Keyword">
            <summary>
            &lt;&lt;keyword&gt;&gt;
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatCSharpSymbol_Keyword">
            <summary>
            &lt;&lt;keyword&gt;&gt;
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.CSharpSymbol_Newline">
            <summary>
            &lt;&lt;newline sequence&gt;&gt;
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatCSharpSymbol_Newline">
            <summary>
            &lt;&lt;newline sequence&gt;&gt;
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.CSharpSymbol_RealLiteral">
            <summary>
            &lt;&lt;real literal&gt;&gt;
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatCSharpSymbol_RealLiteral">
            <summary>
            &lt;&lt;real literal&gt;&gt;
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.CSharpSymbol_StringLiteral">
            <summary>
            &lt;&lt;string literal&gt;&gt;
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatCSharpSymbol_StringLiteral">
            <summary>
            &lt;&lt;string literal&gt;&gt;
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.CSharpSymbol_Whitespace">
            <summary>
            &lt;&lt;white space&gt;&gt;
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatCSharpSymbol_Whitespace">
            <summary>
            &lt;&lt;white space&gt;&gt;
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.Symbol_Unknown">
            <summary>
            &lt;&lt;unknown&gt;&gt;
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatSymbol_Unknown">
            <summary>
            &lt;&lt;unknown&gt;&gt;
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.TokenizerView_CannotPutBack">
            <summary>
            In order to put a symbol back, it must have been the symbol which ended at the current position. The specified symbol ends at {0}, but the current position is {1}
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatTokenizerView_CannotPutBack(System.Object,System.Object)">
            <summary>
            In order to put a symbol back, it must have been the symbol which ended at the current position. The specified symbol ends at {0}, but the current position is {1}
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.ParseError_Unexpected_Nested_CodeBlock">
            <summary>
            Unexpected "{" after "@" character. Once inside the body of a code block (@if {}, @{}, etc.) you do not need to use "@{" to switch to code.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatParseError_Unexpected_Nested_CodeBlock">
            <summary>
            Unexpected "{" after "@" character. Once inside the body of a code block (@if {}, @{}, etc.) you do not need to use "@{" to switch to code.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.ErrorComponent_Newline">
            <summary>
            line break
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatErrorComponent_Newline">
            <summary>
            line break
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.HtmlSymbol_NewLine">
            <summary>
            &lt;&lt;newline sequence&gt;&gt;
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatHtmlSymbol_NewLine">
            <summary>
            &lt;&lt;newline sequence&gt;&gt;
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.HtmlSymbol_RazorComment">
            <summary>
            &lt;&lt;razor comment&gt;&gt;
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatHtmlSymbol_RazorComment">
            <summary>
            &lt;&lt;razor comment&gt;&gt;
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.HtmlSymbol_Text">
            <summary>
            &lt;&lt;text&gt;&gt;
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatHtmlSymbol_Text">
            <summary>
            &lt;&lt;text&gt;&gt;
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.HtmlSymbol_WhiteSpace">
            <summary>
            &lt;&lt;white space&gt;&gt;
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatHtmlSymbol_WhiteSpace">
            <summary>
            &lt;&lt;white space&gt;&gt;
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.ParserIsNotAMarkupParser">
            <summary>
            The parser provided to the ParserContext was not a Markup Parser.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatParserIsNotAMarkupParser">
            <summary>
            The parser provided to the ParserContext was not a Markup Parser.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.Language_Does_Not_Support_RazorComment">
            <summary>
            Cannot use built-in RazorComment handler, language characteristics does not define the CommentStart, CommentStar and CommentBody known symbol types or parser does not override TokenizerBackedParser.OutputSpanBeforeRazorComment
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatLanguage_Does_Not_Support_RazorComment">
            <summary>
            Cannot use built-in RazorComment handler, language characteristics does not define the CommentStart, CommentStar and CommentBody known symbol types or parser does not override TokenizerBackedParser.OutputSpanBeforeRazorComment
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.Trace_BackgroundThreadShutdown">
            <summary>
            [BG][{0}] Shutdown
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatTrace_BackgroundThreadShutdown(System.Object)">
            <summary>
            [BG][{0}] Shutdown
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.Trace_BackgroundThreadStart">
            <summary>
            [BG][{0}] Startup
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatTrace_BackgroundThreadStart(System.Object)">
            <summary>
            [BG][{0}] Startup
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.Trace_ChangesArrived">
            <summary>
            [BG][{0}] {1} changes arrived
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatTrace_ChangesArrived(System.Object,System.Object)">
            <summary>
            [BG][{0}] {1} changes arrived
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.Trace_ChangesDiscarded">
            <summary>
            [BG][{0}] Discarded {1} changes
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatTrace_ChangesDiscarded(System.Object,System.Object)">
            <summary>
            [BG][{0}] Discarded {1} changes
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.Trace_CollectedDiscardedChanges">
            <summary>
            [BG][{0}] Collecting {1} discarded changes
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatTrace_CollectedDiscardedChanges(System.Object,System.Object)">
            <summary>
            [BG][{0}] Collecting {1} discarded changes
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.Trace_Disabled">
            <summary>
            Disabled
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatTrace_Disabled">
            <summary>
            Disabled
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.Trace_EditorProcessedChange">
            <summary>
            [P][{0}] {3} Change in {2} milliseconds: {1}
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatTrace_EditorProcessedChange(System.Object,System.Object,System.Object,System.Object)">
            <summary>
            [P][{0}] {3} Change in {2} milliseconds: {1}
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.Trace_EditorReceivedChange">
            <summary>
            [P][{0}] Received Change: {1}
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatTrace_EditorReceivedChange(System.Object,System.Object)">
            <summary>
            [P][{0}] Received Change: {1}
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.Trace_Enabled">
            <summary>
            Enabled
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatTrace_Enabled">
            <summary>
            Enabled
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.Trace_Format">
            <summary>
            [Razor] {0}
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatTrace_Format(System.Object)">
            <summary>
            [Razor] {0}
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.Trace_NoChangesArrived">
            <summary>
            [BG][{0}] no changes arrived?
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatTrace_NoChangesArrived(System.Object)">
            <summary>
            [BG][{0}] no changes arrived?
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.Trace_ParseComplete">
            <summary>
            [BG][{0}] Parse Complete in {1} milliseconds
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatTrace_ParseComplete(System.Object,System.Object)">
            <summary>
            [BG][{0}] Parse Complete in {1} milliseconds
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.Trace_QueuingParse">
            <summary>
            [M][{0}] Queuing Parse for: {1}
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatTrace_QueuingParse(System.Object,System.Object)">
            <summary>
            [M][{0}] Queuing Parse for: {1}
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.Trace_Startup">
            <summary>
            [Razor] Editor Tracing {0}
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatTrace_Startup(System.Object)">
            <summary>
            [Razor] Editor Tracing {0}
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.Trace_TreesCompared">
            <summary>
            [BG][{0}] Trees Compared in {1} milliseconds. Different = {2}
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatTrace_TreesCompared(System.Object,System.Object,System.Object)">
            <summary>
            [BG][{0}] Trees Compared in {1} milliseconds. Different = {2}
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.ParseError_Sections_Cannot_Be_Nested">
            <summary>
            Section blocks ("{0}") cannot be nested.  Only one level of section blocks are allowed.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatParseError_Sections_Cannot_Be_Nested(System.Object)">
            <summary>
            Section blocks ("{0}") cannot be nested.  Only one level of section blocks are allowed.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.TagHelpers_AttributesMustHaveAName">
            <summary>
            Tag Helper '{0}'s attributes must have names.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatTagHelpers_AttributesMustHaveAName(System.Object)">
            <summary>
            Tag Helper '{0}'s attributes must have names.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.TagHelpers_CannotHaveCSharpInTagDeclaration">
            <summary>
            The tag helper '{0}' must not have C# in the element's attribute declaration area.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatTagHelpers_CannotHaveCSharpInTagDeclaration(System.Object)">
            <summary>
            The tag helper '{0}' must not have C# in the element's attribute declaration area.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.ParseError_DirectiveMustHaveValue">
            <summary>
            Directive '{0}' must have a value.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatParseError_DirectiveMustHaveValue(System.Object)">
            <summary>
            Directive '{0}' must have a value.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.ParseError_DirectiveMustBeSurroundedByQuotes">
            <summary>
            Directive '{0}'s value must be surrounded in double quotes.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatParseError_DirectiveMustBeSurroundedByQuotes(System.Object)">
            <summary>
            Directive '{0}'s value must be surrounded in double quotes.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.TagHelpersParseTreeRewriter_FoundMalformedTagHelper">
            <summary>
            Found a malformed '{0}' tag helper. Tag helpers must have a start and end tag or be self closing.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatTagHelpersParseTreeRewriter_FoundMalformedTagHelper(System.Object)">
            <summary>
            Found a malformed '{0}' tag helper. Tag helpers must have a start and end tag or be self closing.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.TagHelpersParseTreeRewriter_MissingCloseAngle">
            <summary>
            Missing close angle for tag helper '{0}'.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatTagHelpersParseTreeRewriter_MissingCloseAngle(System.Object)">
            <summary>
            Missing close angle for tag helper '{0}'.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.TagHelperBlockRewriter_TagHelperAttributeListMustBeWellFormed">
            <summary>
            TagHelper attributes must be well-formed.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatTagHelperBlockRewriter_TagHelperAttributeListMustBeWellFormed">
            <summary>
            TagHelper attributes must be well-formed.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.TagHelperBlockRewriter_IndexerAttributeNameMustIncludeKey">
            <summary>
            The tag helper attribute '{0}' in element '{1}' is missing a key. The syntax is '&lt;{1} {0}{{ key }}="value"&gt;'.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatTagHelperBlockRewriter_IndexerAttributeNameMustIncludeKey(System.Object,System.Object)">
            <summary>
            The tag helper attribute '{0}' in element '{1}' is missing a key. The syntax is '&lt;{1} {0}{{ key }}="value"&gt;'.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.TagHelpers_AttributeExpressionRequired">
            <summary>
            Non-string tag helper attribute values must not be empty. Add an expression to this attribute value.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatTagHelpers_AttributeExpressionRequired">
            <summary>
            Non-string tag helper attribute values must not be empty. Add an expression to this attribute value.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.TagHelpers_CodeBlocks_NotSupported_InAttributes">
            <summary>
            Code blocks (e.g. @{{var variable = 23;}}) must not appear in non-string tag helper attribute values.
             Already in an expression (code) context. If necessary an explicit expression (e.g. @(@readonly)) may be used.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatTagHelpers_CodeBlocks_NotSupported_InAttributes">
            <summary>
            Code blocks (e.g. @{{var variable = 23;}}) must not appear in non-string tag helper attribute values.
             Already in an expression (code) context. If necessary an explicit expression (e.g. @(@readonly)) may be used.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.TagHelpers_Directives_NotSupported_InAttributes">
            <summary>
            @'{0}' directives must not appear in non-string tag helper attribute values.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatTagHelpers_Directives_NotSupported_InAttributes(System.Object)">
            <summary>
            @'{0}' directives must not appear in non-string tag helper attribute values.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.TagHelpers_InlineMarkupBlocks_NotSupported_InAttributes">
            <summary>
            Inline markup blocks (e.g. @&lt;p&gt;content&lt;/p&gt;) must not appear in non-string tag helper attribute values.
             Expected a '{0}' attribute value, not a string.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatTagHelpers_InlineMarkupBlocks_NotSupported_InAttributes(System.Object)">
            <summary>
            Inline markup blocks (e.g. @&lt;p&gt;content&lt;/p&gt;) must not appear in non-string tag helper attribute values.
             Expected a '{0}' attribute value, not a string.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.RewriterError_EmptyTagHelperBoundAttribute">
            <summary>
            Attribute '{0}' on tag helper element '{1}' requires a value. Tag helper bound attributes of type '{2}' cannot be empty or contain only whitespace.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatRewriterError_EmptyTagHelperBoundAttribute(System.Object,System.Object,System.Object)">
            <summary>
            Attribute '{0}' on tag helper element '{1}' requires a value. Tag helper bound attributes of type '{2}' cannot be empty or contain only whitespace.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.SourceLocationFilePathDoesNotMatch">
            <summary>
            Cannot perform '{1}' operations on '{0}' instances with different file paths.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatSourceLocationFilePathDoesNotMatch(System.Object,System.Object)">
            <summary>
            Cannot perform '{1}' operations on '{0}' instances with different file paths.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.TagHelperParseTreeRewriter_InconsistentTagStructure">
            <summary>
            Tag helpers '{0}' and '{1}' targeting element '{2}' must not expect different {3} values.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatTagHelperParseTreeRewriter_InconsistentTagStructure(System.Object,System.Object,System.Object,System.Object)">
            <summary>
            Tag helpers '{0}' and '{1}' targeting element '{2}' must not expect different {3} values.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.TagHelperParseTreeRewriter_EndTagTagHelperMustNotHaveAnEndTag">
            <summary>
            Found an end tag (&amp;lt;/{0}&amp;gt;) for tag helper '{1}' with tag structure that disallows an end tag ('{2}').
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatTagHelperParseTreeRewriter_EndTagTagHelperMustNotHaveAnEndTag(System.Object,System.Object,System.Object)">
            <summary>
            Found an end tag (&amp;lt;/{0}&amp;gt;) for tag helper '{1}' with tag structure that disallows an end tag ('{2}').
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.TagHelperParseTreeRewriter_CannotHaveNonTagContent">
            <summary>
            The parent &amp;lt;{0}&amp;gt; tag helper does not allow non-tag content. Only child tag helper(s) targeting tag name(s) '{1}' are allowed.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatTagHelperParseTreeRewriter_CannotHaveNonTagContent(System.Object,System.Object)">
            <summary>
            The parent &amp;lt;{0}&amp;gt; tag helper does not allow non-tag content. Only child tag helper(s) targeting tag name(s) '{1}' are allowed.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.RazorResources.TagHelperParseTreeRewriter_InvalidNestedTag">
            <summary>
            The &amp;lt;{0}&amp;gt; tag is not allowed by parent &amp;lt;{1}&amp;gt; tag helper. Only child tag helper(s) targeting tag name(s) '{2}' are allowed.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.RazorResources.FormatTagHelperParseTreeRewriter_InvalidNestedTag(System.Object,System.Object,System.Object)">
            <summary>
            The &amp;lt;{0}&amp;gt; tag is not allowed by parent &amp;lt;{1}&amp;gt; tag helper. Only child tag helper(s) targeting tag name(s) '{2}' are allowed.
            </summary>
        </member>
        <member name="T:Microsoft.AspNet.Razor.Runtime.TagHelpers.TagMode">
            <summary>
            The mode in which an element should render.
            </summary>
        </member>
        <member name="F:Microsoft.AspNet.Razor.Runtime.TagHelpers.TagMode.StartTagAndEndTag">
            <summary>
            Include both start and end tags.
            </summary>
        </member>
        <member name="F:Microsoft.AspNet.Razor.Runtime.TagHelpers.TagMode.SelfClosing">
            <summary>
            A self-closed tag.
            </summary>
        </member>
        <member name="F:Microsoft.AspNet.Razor.Runtime.TagHelpers.TagMode.StartTagOnly">
            <summary>
            Only a start tag.
            </summary>
        </member>
        <member name="T:Microsoft.AspNet.Razor.Runtime.TagHelpers.TagStructure">
            <summary>
            The structure the element should be written in.
            </summary>
        </member>
        <member name="F:Microsoft.AspNet.Razor.Runtime.TagHelpers.TagStructure.Unspecified">
            <summary>
            If no other tag helper applies to the same element and specifies a <see cref="T:Microsoft.AspNet.Razor.Runtime.TagHelpers.TagStructure"/>,
            <see cref="F:Microsoft.AspNet.Razor.Runtime.TagHelpers.TagStructure.NormalOrSelfClosing"/> will be used.
            </summary>
        </member>
        <member name="F:Microsoft.AspNet.Razor.Runtime.TagHelpers.TagStructure.NormalOrSelfClosing">
            <summary>
            Element can be written as &lt;my-tag-helper&gt;&lt;/my-tag-helper&gt; or &lt;my-tag-helper /&gt;.
            </summary>
        </member>
        <member name="F:Microsoft.AspNet.Razor.Runtime.TagHelpers.TagStructure.WithoutEndTag">
            <summary>
            Element can be written as &lt;my-tag-helper&gt; or &lt;my-tag-helper /&gt;.
            </summary>
            <remarks>Elements with a <see cref="F:Microsoft.AspNet.Razor.Runtime.TagHelpers.TagStructure.WithoutEndTag"/> structure will never have any content.</remarks>
        </member>
        <member name="T:Microsoft.AspNet.Razor.TagHelpers.ITagHelperDescriptorResolver">
            <summary>
            Contract used to resolve <see cref="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor"/>s.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.TagHelpers.ITagHelperDescriptorResolver.Resolve(Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptorResolutionContext)">
            <summary>
            Resolves <see cref="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor"/>s based on the given <paramref name="resolutionContext"/>.
            </summary>
            <param name="resolutionContext">
            <see cref="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptorResolutionContext"/> used to resolve descriptors for the Razor page.
            </param>
            <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1"/> of <see cref="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor"/>s based
            on the given <paramref name="resolutionContext"/>.</returns>
        </member>
        <member name="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperAttributeDescriptor">
            <summary>
            A metadata class describing a tag helper attribute.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.TagHelpers.TagHelperAttributeDescriptor.#ctor">
            <summary>
            Instantiates a new instance of the <see cref="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperAttributeDescriptor"/> class.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.TagHelpers.TagHelperAttributeDescriptor.IsIndexer">
            <summary>
            Gets an indication whether this <see cref="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperAttributeDescriptor"/> is used for dictionary indexer
            assignments.
            </summary>
            <value>
            If <c>true</c> this <see cref="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperAttributeDescriptor"/> should be associated with all HTML
            attributes that have names starting with <see cref="P:Microsoft.AspNet.Razor.TagHelpers.TagHelperAttributeDescriptor.Name"/>. Otherwise this
            <see cref="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperAttributeDescriptor"/> is used for property assignment and is only associated with an
            HTML attribute that has the exact <see cref="P:Microsoft.AspNet.Razor.TagHelpers.TagHelperAttributeDescriptor.Name"/>.
            </value>
            <remarks>
            HTML attribute names are matched case-insensitively, regardless of <see cref="P:Microsoft.AspNet.Razor.TagHelpers.TagHelperAttributeDescriptor.IsIndexer"/>.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNet.Razor.TagHelpers.TagHelperAttributeDescriptor.IsStringProperty">
            <summary>
            Gets an indication whether this property is of type <see cref="T:System.String"/> or, if <see cref="P:Microsoft.AspNet.Razor.TagHelpers.TagHelperAttributeDescriptor.IsIndexer"/> is
            <c>true</c>, whether the indexer's value is of type <see cref="T:System.String"/>.
            </summary>
            <value>
            If <c>true</c> the <see cref="P:Microsoft.AspNet.Razor.TagHelpers.TagHelperAttributeDescriptor.TypeName"/> is for <see cref="T:System.String"/>. This causes the Razor parser
            to allow empty values for HTML attributes matching this <see cref="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperAttributeDescriptor"/>. If
            <c>false</c> empty values for such matching attributes lead to errors.
            </value>
        </member>
        <member name="P:Microsoft.AspNet.Razor.TagHelpers.TagHelperAttributeDescriptor.Name">
            <summary>
            The HTML attribute name or, if <see cref="P:Microsoft.AspNet.Razor.TagHelpers.TagHelperAttributeDescriptor.IsIndexer"/> is <c>true</c>, the prefix for matching attribute
            names.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.TagHelpers.TagHelperAttributeDescriptor.PropertyName">
            <summary>
            The name of the CLR property that corresponds to the HTML attribute.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.TagHelpers.TagHelperAttributeDescriptor.TypeName">
            <summary>
            The full name of the named (see <see name="PropertyName"/>) property's <see cref="T:System.Type"/> or, if
            <see cref="P:Microsoft.AspNet.Razor.TagHelpers.TagHelperAttributeDescriptor.IsIndexer"/> is <c>true</c>, the full name of the indexer's value <see cref="T:System.Type"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.TagHelpers.TagHelperAttributeDescriptor.DesignTimeDescriptor">
            <summary>
            The <see cref="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperAttributeDesignTimeDescriptor"/> that contains design time information about
            this attribute.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.TagHelpers.TagHelperAttributeDescriptor.IsNameMatch(System.String)">
            <summary>
            Determines whether HTML attribute <paramref name="name"/> matches this
            <see cref="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperAttributeDescriptor"/>.
            </summary>
            <param name="name">Name of the HTML attribute to check.</param>
            <returns>
            <c>true</c> if this <see cref="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperAttributeDescriptor"/> matches <paramref name="name"/>.
            <c>false</c> otherwise.
            </returns>
        </member>
        <member name="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperAttributeDesignTimeDescriptor">
            <summary>
            A metadata class containing information about tag helper use.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.TagHelpers.TagHelperAttributeDesignTimeDescriptor.Summary">
            <summary>
            A summary of how to use a tag helper.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.TagHelpers.TagHelperAttributeDesignTimeDescriptor.Remarks">
            <summary>
            Remarks about how to use a tag helper.
            </summary>
        </member>
        <member name="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor">
            <summary>
            A metadata class describing a tag helper.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor.Prefix">
            <summary>
            Text used as a required prefix when matching HTML start and end tags in the Razor source to available
            tag helpers.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor.TagName">
            <summary>
            The tag name that the tag helper should target.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor.FullTagName">
            <summary>
            The full tag name that is required for the tag helper to target an HTML element.
            </summary>
            <remarks>This is equivalent to <see cref="P:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor.Prefix"/> and <see cref="P:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor.TagName"/> concatenated.</remarks>
        </member>
        <member name="P:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor.TypeName">
            <summary>
            The full name of the tag helper class.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor.AssemblyName">
            <summary>
            The name of the assembly containing the tag helper class.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor.Attributes">
            <summary>
            The list of attributes the tag helper expects.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor.RequiredAttributes">
            <summary>
            The list of required attribute names the tag helper expects to target an element.
            </summary>
            <remarks>
            <c>*</c> at the end of an attribute name acts as a prefix match.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor.AllowedChildren">
            <summary>
            Get the names of elements allowed as children. Tag helpers must target all such elements.
            </summary>
            <remarks><c>null</c> indicates all children are allowed.</remarks>
        </member>
        <member name="P:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor.TagStructure">
            <summary>
            The expected tag structure.
            </summary>
            <remarks>
            If <see cref="F:Microsoft.AspNet.Razor.Runtime.TagHelpers.TagStructure.Unspecified"/> and no other tag helpers applying to the same element specify
            their <see cref="P:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor.TagStructure"/> the <see cref="F:Microsoft.AspNet.Razor.Runtime.TagHelpers.TagStructure.NormalOrSelfClosing"/> behavior is used:
            <para>
            <code>
            &lt;my-tag-helper&gt;&lt;/my-tag-helper&gt;
            &lt;!-- OR --&gt;
            &lt;my-tag-helper /&gt;
            </code>
            Otherwise, if another tag helper applying to the same element does specify their behavior, that behavior
            is used.
            </para>
            <para>
            If <see cref="F:Microsoft.AspNet.Razor.Runtime.TagHelpers.TagStructure.WithoutEndTag"/> HTML elements can be written in the following formats:
            <code>
            &lt;my-tag-helper&gt;
            &lt;!-- OR --&gt;
            &lt;my-tag-helper /&gt;
            </code>
            </para>
            </remarks>
        </member>
        <member name="P:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor.DesignTimeDescriptor">
            <summary>
            The <see cref="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperDesignTimeDescriptor"/> that contains design time information about this
            tag helper.
            </summary>
        </member>
        <member name="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptorComparer">
            <summary>
            An <see cref="T:System.Collections.Generic.IEqualityComparer`1"/> used to check equality between
            two <see cref="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor"/>s.
            </summary>
        </member>
        <member name="F:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptorComparer.Default">
            <summary>
            A default instance of the <see cref="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptorComparer"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptorComparer.#ctor">
            <summary>
            Initializes a new <see cref="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptorComparer"/> instance.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptorComparer.Equals(Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor,Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor)">
            <inheritdoc />
            <remarks>
            Determines equality based on <see cref="P:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor.TypeName"/>,
            <see cref="P:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor.AssemblyName"/>, <see cref="P:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor.TagName"/>,
            <see cref="P:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor.RequiredAttributes"/>, <see cref="P:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor.AllowedChildren"/>,
            and <see cref="P:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor.TagStructure"/>.
            Ignores <see cref="P:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor.DesignTimeDescriptor"/> because it can be inferred directly from
            <see cref="P:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor.TypeName"/> and <see cref="P:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor.AssemblyName"/>.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptorComparer.GetHashCode(Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptorProvider">
            <summary>
            Enables retrieval of <see cref="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor"/>'s.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptorProvider.#ctor(System.Collections.Generic.IEnumerable{Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor})">
            <summary>
            Instantiates a new instance of the <see cref="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptorProvider"/>.
            </summary>
            <param name="descriptors">The descriptors that the <see cref="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptorProvider"/> will pull from.</param>
        </member>
        <member name="M:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptorProvider.GetDescriptors(System.String,System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Gets all tag helpers that match the given <paramref name="tagName"/>.
            </summary>
            <param name="tagName">The name of the HTML tag to match. Providing a '*' tag name
            retrieves catch-all <see cref="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor"/>s (descriptors that target every tag).</param>
            <param name="attributeNames">Attributes the HTML element must contain to match.</param>
            <returns><see cref="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor"/>s that apply to the given <paramref name="tagName"/>.
            Will return an empty <see cref="T:System.Linq.Enumerable" /> if no <see cref="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor"/>s are
            found.</returns>
        </member>
        <member name="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptorResolutionContext">
            <summary>
            Contains information needed to resolve <see cref="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor"/>s.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptorResolutionContext.#ctor(System.Collections.Generic.IEnumerable{Microsoft.AspNet.Razor.TagHelpers.TagHelperDirectiveDescriptor},Microsoft.AspNet.Razor.ErrorSink)">
            <summary>
            Instantiates a new instance of <see cref="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptorResolutionContext"/>.
            </summary>
            <param name="directiveDescriptors"><see cref="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperDirectiveDescriptor"/>s used to resolve
            <see cref="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor"/>s.</param>
            <param name="errorSink">Used to aggregate <see cref="T:Microsoft.AspNet.Razor.RazorError"/>s.</param>
        </member>
        <member name="P:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptorResolutionContext.DirectiveDescriptors">
            <summary>
            <see cref="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperDirectiveDescriptor"/>s used to resolve <see cref="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor"/>s.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptorResolutionContext.ErrorSink">
            <summary>
            Used to aggregate <see cref="T:Microsoft.AspNet.Razor.RazorError"/>s.
            </summary>
        </member>
        <member name="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperDesignTimeDescriptor">
            <summary>
            A metadata class containing design time information about a tag helper.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.TagHelpers.TagHelperDesignTimeDescriptor.Summary">
            <summary>
            A summary of how to use a tag helper.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.TagHelpers.TagHelperDesignTimeDescriptor.Remarks">
            <summary>
            Remarks about how to use a tag helper.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.TagHelpers.TagHelperDesignTimeDescriptor.OutputElementHint">
            <summary>
            The HTML element a tag helper may output.
            </summary>
            <remarks>
            In IDEs supporting IntelliSense, may override the HTML information provided at design time.
            </remarks>
        </member>
        <member name="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperDirectiveDescriptor">
            <summary>
            Contains information needed to resolve <see cref="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor"/>s.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.TagHelpers.TagHelperDirectiveDescriptor.DirectiveText">
            <summary>
            A <see cref="T:System.String"/> used to find tag helper <see cref="T:System.Type"/>s.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.TagHelpers.TagHelperDirectiveDescriptor.Location">
            <summary>
            The <see cref="T:Microsoft.AspNet.Razor.SourceLocation"/> of the directive.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.TagHelpers.TagHelperDirectiveDescriptor.DirectiveType">
            <summary>
            The <see cref="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperDirectiveType"/> of this directive.
            </summary>
        </member>
        <member name="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperDirectiveType">
            <summary>
            The type of tag helper directive.
            </summary>
        </member>
        <member name="F:Microsoft.AspNet.Razor.TagHelpers.TagHelperDirectiveType.AddTagHelper">
            <summary>
            An <c>@addTagHelper</c> directive.
            </summary>
        </member>
        <member name="F:Microsoft.AspNet.Razor.TagHelpers.TagHelperDirectiveType.RemoveTagHelper">
            <summary>
            A <c>@removeTagHelper</c> directive.
            </summary>
        </member>
        <member name="F:Microsoft.AspNet.Razor.TagHelpers.TagHelperDirectiveType.TagHelperPrefix">
            <summary>
            A <c>@tagHelperPrefix</c> directive.
            </summary>
        </member>
        <member name="T:Microsoft.AspNet.Razor.TagHelpers.TypeBasedTagHelperDescriptorComparer">
            <summary>
            An <see cref="T:System.Collections.Generic.IEqualityComparer`1"/> that checks equality between two
            <see cref="T:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor"/>s using only their <see cref="P:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor.AssemblyName"/>s and
            <see cref="P:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor.TypeName"/>s.
            </summary>
            <remarks>
            This class is intended for scenarios where Reflection-based information is all important i.e.
            <see cref="P:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor.RequiredAttributes"/>, <see cref="P:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor.TagName"/>, and related
            properties are not relevant.
            </remarks>
        </member>
        <member name="F:Microsoft.AspNet.Razor.TagHelpers.TypeBasedTagHelperDescriptorComparer.Default">
            <summary>
            A default instance of the <see cref="T:Microsoft.AspNet.Razor.TagHelpers.TypeBasedTagHelperDescriptorComparer"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.TagHelpers.TypeBasedTagHelperDescriptorComparer.Equals(Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor,Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor)">
            <inheritdoc />
            <remarks>
            Determines equality based on <see cref="P:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor.AssemblyName"/> and
            <see cref="P:Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor.TypeName"/>.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNet.Razor.TagHelpers.TypeBasedTagHelperDescriptorComparer.GetHashCode(Microsoft.AspNet.Razor.TagHelpers.TagHelperDescriptor)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNet.Razor.Text.TextBufferReader.BacktrackContext">
            <summary>
            Need a class for reference equality to support cancelling backtrack.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.Text.TextChange.#ctor(System.Int32,System.Int32,Microsoft.AspNet.Razor.Text.ITextBuffer,System.Int32,Microsoft.AspNet.Razor.Text.ITextBuffer)">
            <summary>
            Constructor for changes where the position hasn't moved (primarily for tests)
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Razor.Text.TextChange.OldText">
            <remark>
            Note: This property is not thread safe, and will move position on the textbuffer while being read.
            https://aspnetwebstack.codeplex.com/workitem/1317, tracks making this immutable and improving the access
            to ITextBuffer to be thread safe.
            </remark>
        </member>
        <member name="P:Microsoft.AspNet.Razor.Text.TextChange.NewText">
            <remark>
            Note: This property is not thread safe, and will move position on the textbuffer while being read.
            https://aspnetwebstack.codeplex.com/workitem/1317, tracks making this immutable and improving the access
            to ITextBuffer to be thread safe.
            </remark>
        </member>
        <member name="M:Microsoft.AspNet.Razor.Text.TextChange.ApplyChange(Microsoft.AspNet.Razor.Parser.SyntaxTree.Span)">
            <summary>
            Applies the text change to the content of the span and returns the new content.
            This method doesn't update the span content.
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Razor.Text.TextChange.Normalize">
            <summary>
            Removes a common prefix from the edit to turn IntelliSense replacements into insertions where possible
            </summary>
            <returns>A normalized text change</returns>
        </member>
        <member name="M:Microsoft.AspNet.Razor.Tokenizer.Symbols.SymbolExtensions.FirstHtmlSymbolAs(System.Collections.Generic.IEnumerable{Microsoft.AspNet.Razor.Tokenizer.Symbols.ISymbol},Microsoft.AspNet.Razor.Tokenizer.Symbols.HtmlSymbolType)">
            <summary>
            Converts the generic <see cref="T:System.Collections.Generic.IEnumerable`1"/> to a <see cref="T:System.Collections.Generic.IEnumerable`1"/> and
            finds the first <see cref="T:Microsoft.AspNet.Razor.Tokenizer.Symbols.HtmlSymbol"/> with type <paramref name="type"/>.
            </summary>
            <param name="symbols">The <see cref="T:System.Collections.Generic.IEnumerable`1"/> instance this method extends.</param>
            <param name="type">The <see cref="T:Microsoft.AspNet.Razor.Tokenizer.Symbols.HtmlSymbolType"/> to search for.</param>
            <returns>The first <see cref="T:Microsoft.AspNet.Razor.Tokenizer.Symbols.HtmlSymbol"/> of type <paramref name="type"/>.</returns>
        </member>
    </members>
</doc>
