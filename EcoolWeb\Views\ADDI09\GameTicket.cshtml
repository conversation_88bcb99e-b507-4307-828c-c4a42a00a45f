﻿
@model ECOOL_APP.com.ecool.Models.DTO.ADDI09EditOoneViewModel
@using ECOOL_APP.com.ecool.Models.DTO;

<link href="~/Content/css/EzCss.css" rel="stylesheet" />
@{
    ViewBag.Title = ViewBag.Panel_Title;
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
}
@Html.Partial("_Title_Secondary")
<div style="text-align:center">

    <h3>
        小朋友您的闖關卡號為
    </h3>
    <h3 style="color:blue">
        0248415393
    </h3>

    <img src="https://chart.googleapis.com/chart?chs=180x180&cht=qr&chl=0248415393&chld=L|4">

    <br />
    @*<a href='@Url.Action("GameTicket",new { IsRandom = Model.IsRandom, IsFix = Model.IsFix})' role="button" class="btn btn-sm btn-sys">
        產生下一個卡號QR Code
    </a>*@
</div>

<script type="text/javascript">
        window.history.forward(1);
</script>


