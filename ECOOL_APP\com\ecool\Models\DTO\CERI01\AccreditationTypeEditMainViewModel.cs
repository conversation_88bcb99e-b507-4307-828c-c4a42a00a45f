﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class AccreditationTypeEditMainViewModel
    {
        /// <summary>
        ///護照ID
        /// </summary>
        [DisplayName("護照ID")]
        [Key]
        public string TYPE_ID { get; set; }

        /// <summary>
        ///認證類別名稱
        /// </summary>
        [DisplayName("護照名稱")]
        [Required]
        public string TYPE_NAME { get; set; }

        [DisplayName("一個✓獲得的點數")]
        public short CASH { get; set; }
    }
}