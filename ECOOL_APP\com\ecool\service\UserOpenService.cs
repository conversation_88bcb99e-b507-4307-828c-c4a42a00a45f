﻿using ECOOL_APP.EF;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.com.ecool.service
{
    public class UserOpenService
    {
        public TeacherModalViewModel GetTeacherListData(TeacherModalViewModel model, ref ECOOL_DEVEntities db)
        {
            //// 排序 ...
            if (string.IsNullOrWhiteSpace(model.OrderByColumnName)) // 預設排序
            {
                model.OrderByColumnName = nameof(HRMT01.NAME);
                model.SortType = PageGlobal.SortType.ASC;
            }

            var Temp = db.HRMT01.Where(a => a.USER_STATUS == UserStaus.Enabled && a.USER_TYPE == UserType.Teacher);

            if (!string.IsNullOrWhiteSpace(model.SCHOOL_NO))
            {
                Temp = Temp.Where(a => a.SCHOOL_NO == model.SCHOOL_NO);
            }

            if (!string.IsNullOrWhiteSpace(model.SearchContent))
            {
                Temp = Temp.Where(a => a.NAME.Contains(model.SearchContent));
            }

            if (model.oTeachers?.Count() > 0)
            {
                var userKeys = model.oTeachers.Select(a => a.USER_KEY).ToList();

                Temp = Temp.Where(a => !userKeys.Contains(a.USER_KEY));
            }
            Temp = Temp.OrderBy(model.OrderByColumnName, model.SortType);

            model.Teachers = Temp.ToEzPagedList(model.Page, model.PageSize);
            model.Page = model.Teachers.PageNumber;

            return model;
        }

        public HRMT01 GetUserData(string USER_KEY, ref ECOOL_DEVEntities db)
        {
            using (var txn = Queries.GetNewReadUncommittedScope())
            {
                var Hr = (from a in db.HRMT01
                          where a.USER_KEY == USER_KEY
                          select a).FirstOrDefault();

                return Hr;
            }
        }
    }
}