﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using com.ecool.service;
using System.Web.Mvc;
using ECOOL_APP.com.ecool.Models.entity;
using ECOOL_APP.com.ecool.Models.DTO;
using ECOOL_APP;
using EcoolWeb.Models;
using ECOOL_APP.EF;

namespace EcoolWeb.Controllers
{
    public class MenuController : Controller
    {
        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();

        public PartialViewResult PermissionMenu()
        {
            UserProfile user = UserProfileHelper.Get();

            ViewBag.user = user;

            List<uZZT01> list = new List<uZZT01>();

            string SCHOOL_NO = string.Empty;
            string USER_NO = string.Empty;
            string SEX = string.Empty;
            string USER_TYPE = string.Empty;
            int? ROLE_TYPE = null;

            if (user != null)
            {
                SCHOOL_NO = user.SCHOOL_NO;
                USER_NO = user.USER_NO;
                ROLE_TYPE = user.ROLE_TYPE;
                USER_TYPE = user.USER_TYPE;
                SEX = user.SEX;
            }

            ViewBag.PlayerUrl = UserProfile.GetPlayerUrl(ref db, SCHOOL_NO, USER_NO, SEX, USER_TYPE);

            //user.SCHOOL_NO = "4142522";
            //user.USER_NO = "0000";

            list = PermissionService.Permission_GetMenuListQUERY(SCHOOL_NO, USER_NO, ROLE_TYPE);
            list = list.Where(a => (a.ENABLE ?? true) == true).ToList();
            //list = PermissionService.Permission_GetMenuListQUERY("4142522", "0000");

            //我的秘書
            var SECMenu = from a in list
                          where a.BRE_NO_PRE == "SEC"
                          select a;

            //加值應用
            var ADDMenu = from a in list
                          where a.BRE_NO_PRE == "ADD"
                          select a;
            //e酷幣市集
            var AWAMenu = from a in list
                          where a.BRE_NO_PRE == "AWA"
                          select a;
            //能力認證
            var CERMenu = from a in list
                          where a.BRE_NO_PRE == "CER"
                          select a;

            //能力認證
            var CARMenu = from a in list
                          where a.BRE_NO_PRE == "CAR"
                          select a;


            //統計與排行
            var SARMenu = from a in list
                          where a.BRE_NO_PRE == "SAR"
                          select a;

            //維運管理
            var ZZZMenu = from a in list
                          where a.BRE_NO_PRE == "ZZZ"
                          select a;

            //帳號管理
            var ACCMenu = from a in list
                          where a.BRE_NO_PRE == "ACC"
                          select a;

            //酷嗶嗶嗶
            var COOMenu = from a in list
                          where a.BRE_NO_PRE == "COO"
                          select a;

            //酷嗶嗶嗶
            var AWACMenu = from a in list
                          where a.BRE_NO_PRE == "AWAC"
                           select a;
            var model = new Menu
            {
                SEC = SECMenu,
                ADD = ADDMenu,
                AWA = AWAMenu,
                CER = CERMenu,
                CAR = CARMenu,
                SAR = SARMenu,
                ZZZ = ZZZMenu,
                ACC = ACCMenu,
                COO = COOMenu,
                AWAC = AWACMenu,

            };
            string[] USerArra = new string[] { "run1", "run2", "run3", "run4", "run5", "run6" };

            if (user != null && USerArra.Contains(user.USER_NO))
            {
                return PartialView("../Shared/_RunMenu", model);
            }
            else
            {
                return PartialView("../Shared/_PermissionMenu", model);
            }
        }

        public PartialViewResult ChildMonthMenu()
        {
            UserProfile user = UserProfileHelper.Get();
            var list = PermissionService.Permission_GetMenuListQUERY(user?.SCHOOL_NO, user?.USER_NO, user?.ROLE_TYPE);
            list = list.Where(a => (a.ENABLE ?? true) == true).ToList();
            //維運管理
            var ZZZMenu = from a in list
                          select a;

            var model = new Menu
            {
                ZZZ = ZZZMenu,
            };

            return PartialView(model);
        }
    }
}