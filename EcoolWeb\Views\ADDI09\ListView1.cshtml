﻿@{
    ViewBag.Title = ViewBag.Panel_Title;
    var tempUSER_NO = "";
    var temfromSouce = "";
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    bool AppMode = (string.IsNullOrWhiteSpace(UUID) == false);
    if (AppMode)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
}

@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")
@if (TempData["USER_NO"] != null && TempData["fromSOurce"] != null)

{
    tempUSER_NO = TempData["USER_NO"].ToString();
    temfromSouce = TempData["fromSOurce"].ToString();
}

<div id="showStoargeAns" class="alert alert-info" style="display:none;">
    恭喜您完成&nbsp;<span id="classShowText"></span>&nbsp;的加點，以下是明細。
</div>

@using (Html.BeginForm("ListView", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1", enctype = "multipart/form-data" }))
{
    @Html.AntiForgeryToken()
<div id="PageContent">
    @Html.Hidden("ThisSaveBATCH_CASH_ID", (string)ViewBag.ThisSaveBATCH_CASH_ID)
    @Html.Hidden("SOURCE_NO", (string)ViewBag.SOURCE_NO)
    @Html.Hidden("ListSchool", (string)ViewBag.ListSchool)
    @Html.Hidden("ListUSERNO", (string)ViewBag.ListUSERNO)
    @Html.Hidden("fromSOurce", temfromSouce)
    @Html.Action("_PageContent", (string)ViewBag.BRE_NO, new { SOURCE_NO = (string)ViewBag.SOURCE_NO, ThisSaveBATCH_CASH_ID = (string)ViewBag.ThisSaveBATCH_CASH_ID, SYS_TABLE_TYPE = (string)ViewBag.SYS_TABLE_TYPE, ADDT14_STYLE = (string)ViewBag.ADDT14_STYLE, fromSouce = temfromSouce })
</div>

}

@section css{
    <style>
        samp {
            font-size: 0.8em;
        }
    </style>
}
@section Scripts {
    <script language="JavaScript">
        var targetFormID = '#form1';
        window.onload = function () {
            var ThisSaveBATCH_CASH_ID = $('#ThisSaveBATCH_CASH_ID').val();
            var SOURCE_NO = $('#SOURCE_NO').val();
            if (ThisSaveBATCH_CASH_ID != '') {
                $('#Q_DIV').hide();
            }
            if ("@tempUSER_NO"!= "" && "@temfromSouce" != "")
            {
                console.log("@tempUSER_NO");
                $('#Search_SearchContents').val("@tempUSER_NO");
                $("#Search_SYS_TABLE_TYPE").val("@ViewBag.SYS_TABLE_TYPE");
                $("#Search_fromSOurce").val("@temfromSouce");
                funAjax();
            }
        }
        function PrintBooK() {
                  @*var data = {
                "Search.OrderByName": $('#Search_OrderByName').val(),
                "Search.SyntaxName": $('#Search_SyntaxName').val() ,
                "Search.Page": $('#Search_Page').val() ,
                "Search.SearchContents": $('#Search_SearchContents').val(),
                "Search.SYS_TABLE_TYPE": $('#Search_SYS_TABLE_TYPE :selected').val(),
                "Search.SYS_fromSOurce": "@temfromSouce",
                "ThisSaveBATCH_CASH_ID": $('#ThisSaveBATCH_CASH_ID').val(),
                "SOURCE_NO": $('#SOURCE_NO').val(),
                "ListSchool": $("#ListSchool").val(),
                "ListUSERNO": $("#ListUSERNO").val(),
                "fromSouce": "@temfromSouce",
            };
                $(targetFormID).attr('action', '@Url.Action("PrintQuery2", (string)ViewBag.BRE_NO))').attr('target','_blank');
            $(targetFormID).submit();*@
            var ThisSaveBATCH_CASH_ID = $('#ThisSaveBATCH_CASH_ID').val();
            form1.action = '@Html.Raw(@Url.Action("PrintQuery2", (string)ViewBag.BRE_NO))' + '?vBATCH_CASH_ID=' + ThisSaveBATCH_CASH_ID + '&vSCHOOL_NO=' + @user.SCHOOL_NO
            form1.submit();
            }
        //明細
        function onBtnLink(BATCH_CASH_ID, SCHOOL_NO, USER_NO, NUM) {
            form1.action = '@Html.Raw(@Url.Action("Details", (string)ViewBag.BRE_NO))' + '?vBATCH_CASH_ID=' + BATCH_CASH_ID + '&vSCHOOL_NO=' + SCHOOL_NO + '&vUSER_NO=' + USER_NO + '&vNUM=' + NUM
            form1.submit();
        }
        function ToExcel() {

             var ThisSaveBATCH_CASH_ID = $('#ThisSaveBATCH_CASH_ID').val();
            form1.action = '@Html.Raw(@Url.Action("PrintExcel", (string)ViewBag.BRE_NO))' + '?vBATCH_CASH_ID=' + ThisSaveBATCH_CASH_ID + '&vSCHOOL_NO=' + @user.SCHOOL_NO
            form1.submit();
          
         };
        //分頁
        function FunPageProc(pageno) {

            $('#Search_Page').val(pageno);
            funAjax()
        }

        //排序
        function FunSort(SortName) {

            OrderByName = $('#Search_OrderByName').val();
            SyntaxName = $('#Search_SyntaxName').val();

            if (OrderByName == SortName) {

                if (SyntaxName == "Desc") {
                    $('#Search_SyntaxName').val("ASC");
                }
                else {
                    $('#Search_SyntaxName').val("Desc");
                }
            }
            else {
                $('#Search_OrderByName').val(SortName);
                $('#Search_SyntaxName').val("Desc");
            }
            funAjax()
        }

        //查詢
        function funAjax() {

            var data = {
                "Search.OrderByName": $('#Search_OrderByName').val(),
                "Search.SyntaxName": $('#Search_SyntaxName').val() ,
                "Search.Page": $('#Search_Page').val() ,
                "Search.SearchContents": $('#Search_SearchContents').val(),
                "Search.SYS_TABLE_TYPE": 'ZZZI20',
                "Search.SYS_fromSOurce": "@temfromSouce",
                "ThisSaveBATCH_CASH_ID": $('#ThisSaveBATCH_CASH_ID').val(),
                "SOURCE_NO": $('#SOURCE_NO').val(),
                "ListSchool": $("#ListSchool").val(),
                "ListUSERNO": $("#ListUSERNO").val(),
                "fromSouce": "@temfromSouce",
            };

            $.ajax({
                url: '@Url.Action("_PageContent", (string)ViewBag.BRE_NO)',
                data: data,
                type: 'post',
                async: false,
                cache: false,
                dataType: 'html',
                success: function (data) {
                    $('#PageContent').html(data);
                }
            });
        }

        checkClassArrayStoarge();

        function checkClassArrayStoarge() {
            let temp_class_array = localStorage.getItem("temp_class_array");
            if (temp_class_array) {
                let displayString = "";
                displayString = JSON.parse(temp_class_array).join(', ');
                $("#showStoargeAns").show();
                $("#classShowText").text(displayString);

                localStorage.removeItem("temp_class_array");
            }
        }
    </script>
}