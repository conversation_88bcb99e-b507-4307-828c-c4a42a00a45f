﻿using ECOOL_APP.com.ecool.Models.entity;
using com.ecool.sqlConnection;
using ECOOL_APP.com.ecool.Models.DTO;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Data;
using System.Data.SqlClient;
using ECOOL_APP.EF;
using System.Web;

namespace ECOOL_APP.com.ecool.service
{
    public class ZZZI25Service
    {
        public string ErrorMsg;
        private int ErrorInt = 0;

        #region 取得uADDT19清單

        public List<uADDT19> GetListData(ZZZ25IndexViewModel Data, UserProfile user, int pageSize, ref int Count)
        {
            List<uADDT19> list_data = new List<uADDT19>();

            uADDT19 ReturnData = null;

            StringBuilder sb = new StringBuilder();
            DataTable dt;
            try
            {
                sb.Append("SELECT A.IMG_ID,A.IMG_FILE,<PERSON><PERSON>IMG_TITLE,<PERSON><PERSON>IMG_DATES,<PERSON><PERSON>IMG_DATEE,<PERSON><PERSON>IMG_LINK,<PERSON>.<PERSON>,<PERSON><PERSON>_<PERSON>ATE,A.CRE_PERSON ");
                sb.Append(", A.CHG_DATE, A.CHG_PERSON,A.IMG_TYPE,A.SCHOOL_NO ");
                sb.Append(" FROM ADDT19 A(NOLOCK)");
                sb.Append(" WHERE 1=1 ");
                sb.Append($"AND ISNULL(A.IMG_TYPE,{ADDT19.IMG_TYPEVal.HomeViewImage}) = {Data.IMG_TYPE} ");

                if (Data.Search.SearchContents != string.Empty && Data.Search.SearchContents != null)
                {
                    sb.Append(" AND (");
                    sb.AppendFormat("  A.IMG_TITLE LIKE '%{0}%' ", Data.Search.SearchContents);
                    sb.Append(" OR ");
                    sb.AppendFormat("  A.IMG_LINK LIKE '%{0}%' ", Data.Search.SearchContents);
                    sb.Append(" OR ");
                    sb.AppendFormat("  A.IMG_FILE LIKE '%{0}%' ", Data.Search.SearchContents);

                    DateTime dtDate;
                    if (DateTime.TryParse(Data.Search.SearchContents, out dtDate))
                    {
                        sb.Append(" OR ");
                        sb.AppendFormat("  A.IMG_DATES = '{0}' ", Data.Search.SearchContents.Trim());
                        sb.Append(" OR ");
                        sb.AppendFormat("  A.IMG_DATEE = '{0}' ", Data.Search.SearchContents.Trim());
                    }

                    sb.Append(" )");
                }

                if (Data.Search.STATUS != string.Empty && Data.Search.STATUS != null)
                {
                    sb.Append(" and A.STATUS = '" + Data.Search.STATUS + "' ");
                }

                string OrderBy = "A.IMG_DATES DESC";

                if (string.IsNullOrWhiteSpace(Data.Search.OrderByName) == false)
                {
                    OrderBy = "A." + Data.Search.OrderByName + " " + Data.Search.SyntaxName;
                }

                string ThisError = "";

                dt = new sqlConnection().executeQueryBSqlDataReaderListPage(Data.Search.Page, pageSize, sb.ToString(), sb.ToString(), OrderBy, ref Count, ref ThisError);
                if (dt != null)
                {
                    foreach (DataRow dr in dt.Rows)
                    {
                        ReturnData = new uADDT19();

                        ReturnData.IMG_ID = (dr["IMG_ID"] == DBNull.Value ? "" : (string)dr["IMG_ID"]);
                        ReturnData.IMG_FILE = (dr["IMG_FILE"] == DBNull.Value ? "" : (string)dr["IMG_FILE"]);
                        ReturnData.IMG_TITLE = (dr["IMG_TITLE"] == DBNull.Value ? "" : (string)dr["IMG_TITLE"]);
                        ReturnData.IMG_DATES = (dr["IMG_DATES"] == DBNull.Value ? (DateTime?)null : (DateTime)dr["IMG_DATES"]);
                        ReturnData.IMG_DATEE = (dr["IMG_DATEE"] == DBNull.Value ? (DateTime?)null : (DateTime)dr["IMG_DATEE"]);
                        ReturnData.IMG_LINK = (dr["IMG_LINK"] == DBNull.Value ? "" : (string)dr["IMG_LINK"]);
                        ReturnData.STATUS = (dr["STATUS"] == DBNull.Value ? "" : (string)dr["STATUS"]);
                        ReturnData.STATUS_NAME = ReturnData.STATUS == "" ? "" : uADDT19.GetStatusName(ReturnData.STATUS);
                        ReturnData.CRE_DATE = (dr["CRE_DATE"] == DBNull.Value ? (DateTime?)null : (DateTime)dr["CRE_DATE"]);
                        ReturnData.CRE_PERSON = (dr["CRE_PERSON"] == DBNull.Value ? "" : (string)dr["CRE_PERSON"]);
                        ReturnData.CHG_DATE = (dr["CHG_DATE"] == DBNull.Value ? (DateTime?)null : (DateTime)dr["CHG_DATE"]);
                        ReturnData.CHG_PERSON = (dr["CHG_PERSON"] == DBNull.Value ? "" : (string)dr["CHG_PERSON"]);
                        ReturnData.IMG_TYPE = dr["IMG_TYPE"] == DBNull.Value ? (byte)ADDT19.IMG_TYPEVal.HomeViewImage : (byte)dr["IMG_TYPE"];
                        ReturnData.SCHOOL_NO = dr["SCHOOL_NO"] == DBNull.Value ? "" : (string)dr["SCHOOL_NO"];
                        list_data.Add(ReturnData);
                    }
                }

                dt.Clear();
                dt.Dispose();
            }
            catch (Exception exception)
            {
                throw exception;
            }
            return list_data;
        }

        #endregion 取得uADDT19清單

        #region 取得.uADDT19 詳細資料

        public uADDT19 GetGetDetailsData(string IMG_ID)
        {
            uADDT19 ReturnData = new uADDT19();
            StringBuilder sb = new StringBuilder();
            DataTable dt;
            try
            {
                sb.Append("SELECT A.IMG_ID,A.IMG_FILE,A.IMG_TITLE,A.IMG_DATES,A.IMG_DATEE,A.IMG_LINK,A.STATUS,A.CRE_DATE,A.CRE_PERSON ");
                sb.Append(", A.CHG_DATE, A.CHG_PERSON,A.IMG_TYPE ");
                sb.Append(" FROM ADDT19 A(NOLOCK)");
                sb.Append(" WHERE 1=1 ");
                sb.AppendFormat(" AND A.IMG_ID= '{0}' ", IMG_ID);

                dt = new sqlConnection().executeQueryByDataTableList(sb.ToString());

                foreach (DataRow dr in dt.Rows)
                {
                    ReturnData.IMG_ID = (dr["IMG_ID"] == DBNull.Value ? "" : (string)dr["IMG_ID"]);
                    ReturnData.IMG_FILE = (dr["IMG_FILE"] == DBNull.Value ? "" : (string)dr["IMG_FILE"]);
                    ReturnData.IMG_TITLE = (dr["IMG_TITLE"] == DBNull.Value ? "" : (string)dr["IMG_TITLE"]);
                    ReturnData.IMG_DATES = (dr["IMG_DATES"] == DBNull.Value ? (DateTime?)null : (DateTime)dr["IMG_DATES"]);
                    ReturnData.IMG_DATEE = (dr["IMG_DATEE"] == DBNull.Value ? (DateTime?)null : (DateTime)dr["IMG_DATEE"]);
                    ReturnData.IMG_LINK = (dr["IMG_LINK"] == DBNull.Value ? "" : (string)dr["IMG_LINK"]);
                    ReturnData.STATUS = (dr["STATUS"] == DBNull.Value ? "" : (string)dr["STATUS"]);
                    ReturnData.STATUS_NAME = ReturnData.STATUS == "" ? "" : uADDT19.GetStatusName(ReturnData.STATUS);
                    ReturnData.CRE_DATE = (dr["CRE_DATE"] == DBNull.Value ? (DateTime?)null : (DateTime)dr["CRE_DATE"]);
                    ReturnData.CRE_PERSON = (dr["CRE_PERSON"] == DBNull.Value ? "" : (string)dr["CRE_PERSON"]);
                    ReturnData.CHG_DATE = (dr["CHG_DATE"] == DBNull.Value ? (DateTime?)null : (DateTime)dr["CHG_DATE"]);
                    ReturnData.CHG_PERSON = (dr["CHG_PERSON"] == DBNull.Value ? "" : (string)dr["CHG_PERSON"]);
                    ReturnData.IMG_TYPE = dr["IMG_TYPE"] == DBNull.Value ? (byte)ADDT19.IMG_TYPEVal.HomeViewImage : (byte)dr["IMG_TYPE"];
                }

                dt.Clear();
                dt.Dispose();
                sb.Clear();
            }
            catch (Exception exception)
            {
                throw exception;
            }

            return ReturnData;
        }

        #endregion 取得.uADDT19 詳細資料

        #region 取得.uADDT19 今天資料

        public List<uADDT19> GetGetNowDetailsData(byte? IMG_TYPE = 1)
        {
            List<uADDT19> ReturnDataList = new List<uADDT19>();

            StringBuilder sb = new StringBuilder();
            DataTable dt;
            try
            {
                sb.Append("SELECT A.IMG_ID,A.IMG_FILE,A.IMG_TITLE,A.IMG_DATES,A.IMG_DATEE,A.IMG_LINK,A.STATUS,A.CRE_DATE,A.CRE_PERSON ");
                sb.Append(", A.CHG_DATE, A.CHG_PERSON,A.IMG_TYPE , A.SCHOOL_NO");
                sb.Append(" FROM ADDT19 A(NOLOCK)");
                sb.Append(" WHERE 1=1 and A.STATUS='Enabled'");
                sb.AppendFormat(" and CONVERT(nvarchar(10),GETDATE(),111) BETWEEN A.IMG_DATES AND A.IMG_DATEE ");
                sb.Append($" AND ISNULL(A.IMG_TYPE,{ADDT19.IMG_TYPEVal.HomeViewImage}) = {IMG_TYPE} ");

                dt = new sqlConnection().executeQueryByDataTableList(sb.ToString());

                foreach (DataRow dr in dt.Rows)
                {
                    uADDT19 ReturnData = new uADDT19();
                    ReturnData.IMG_ID = (dr["IMG_ID"] == DBNull.Value ? "" : (string)dr["IMG_ID"]);
                    ReturnData.IMG_FILE = (dr["IMG_FILE"] == DBNull.Value ? "" : (string)dr["IMG_FILE"]);
                    ReturnData.IMG_TITLE = (dr["IMG_TITLE"] == DBNull.Value ? "" : (string)dr["IMG_TITLE"]);
                    ReturnData.IMG_DATES = (dr["IMG_DATES"] == DBNull.Value ? (DateTime?)null : (DateTime)dr["IMG_DATES"]);
                    ReturnData.IMG_DATEE = (dr["IMG_DATEE"] == DBNull.Value ? (DateTime?)null : (DateTime)dr["IMG_DATEE"]);
                    ReturnData.IMG_LINK = (dr["IMG_LINK"] == DBNull.Value ? "" : (string)dr["IMG_LINK"]);
                    ReturnData.STATUS = (dr["STATUS"] == DBNull.Value ? "" : (string)dr["STATUS"]);
                    ReturnData.STATUS_NAME = ReturnData.STATUS == "" ? "" : uADDT19.GetStatusName(ReturnData.STATUS);
                    ReturnData.CRE_DATE = (dr["CRE_DATE"] == DBNull.Value ? (DateTime?)null : (DateTime)dr["CRE_DATE"]);
                    ReturnData.CRE_PERSON = (dr["CRE_PERSON"] == DBNull.Value ? "" : (string)dr["CRE_PERSON"]);
                    ReturnData.CHG_DATE = (dr["CHG_DATE"] == DBNull.Value ? (DateTime?)null : (DateTime)dr["CHG_DATE"]);
                    ReturnData.CHG_PERSON = (dr["CHG_PERSON"] == DBNull.Value ? "" : (string)dr["CHG_PERSON"]);
                    ReturnData.IMG_TYPE = dr["IMG_TYPE"] == DBNull.Value ? (byte)ADDT19.IMG_TYPEVal.HomeViewImage : (byte)dr["IMG_TYPE"];
                    ReturnData.SCHOOL_NO = dr["SCHOOL_NO"] == DBNull.Value ? "" : (string)dr["SCHOOL_NO"];
                    ReturnDataList.Add(ReturnData);
                }

                dt.Clear();
                dt.Dispose();
                sb.Clear();
            }
            catch (Exception exception)
            {
                throw exception;
            }

            return ReturnDataList;
        }

        #endregion 取得.uADDT19 今天資料

        #region 新增ADDT19

        public void CreateDateADDT19(uADDT19 Date)
        {
            using (SqlConnection conn = new sqlConnection().getConnection4Query())
            {
                SqlCommand cmd = conn.CreateCommand();
                SqlTransaction transaction = conn.BeginTransaction();
                cmd.Transaction = transaction;
                try
                {
                    Date.IMG_ID = this.GetNewIMG_ID(cmd);

                    this.INSERT_NTO_ADDT19(conn, transaction, Date);

                    if (ErrorInt == 0)
                    {
                        transaction.Commit();
                    }
                    else
                    {
                        Date.IMG_ID = null;
                        transaction.Rollback();
                        ErrorMsg = "新增資料失敗;\r\n" + ErrorMsg;
                    }
                }
                catch (Exception ex)
                {
                    Date.IMG_ID = null;
                    transaction.Rollback();
                    ErrorMsg = "新增資料失敗;\r\n" + ex.Message;
                }
                finally
                {
                    if (cmd != null)
                    {
                        cmd.Dispose();
                    }

                    if (conn != null)
                    {
                        conn.Close();
                        conn.Dispose();
                    }
                }
            }
        }

        #endregion 新增ADDT19

        #region 修改 ADDT19

        public void UpDate(uADDT19 Date)
        {
            using (SqlConnection conn = new sqlConnection().getConnection4Query())
            {
                SqlCommand cmd = conn.CreateCommand();
                SqlTransaction transaction = conn.BeginTransaction();
                cmd.Transaction = transaction;
                try
                {
                    this.UPDATE_SET_ADDT19(conn, transaction, Date);

                    if (ErrorInt == 0)
                    {
                        transaction.Commit();
                    }
                    else
                    {
                        transaction.Rollback();
                        ErrorMsg = "修改資料失敗;\r\n" + ErrorMsg;
                    }
                }
                catch (Exception ex)
                {
                    transaction.Rollback();
                    ErrorMsg = "修改資料失敗;\r\n" + ex.Message;
                }
                finally
                {
                    if (cmd != null)
                    {
                        cmd.Dispose();
                    }

                    if (conn != null)
                    {
                        conn.Close();
                        conn.Dispose();
                    }
                }
            }
        }

        #endregion 修改 ADDT19

        #region 刪除 ADDT19

        public void DelAllDate(string IMG_ID)
        {
            using (SqlConnection conn = new sqlConnection().getConnection4Query())
            {
                SqlCommand cmd = conn.CreateCommand();
                SqlTransaction transaction = conn.BeginTransaction();
                cmd.Transaction = transaction;
                try
                {
                    this.DELETE_ADDT19(conn, transaction, IMG_ID);

                    if (ErrorInt == 0)
                    {
                        transaction.Commit();
                    }
                    else
                    {
                        transaction.Rollback();
                        ErrorMsg = "刪除資料處理;\r\n" + ErrorMsg;
                    }
                }
                catch (Exception ex)
                {
                    transaction.Rollback();
                    ErrorMsg = "刪除資料處理;\r\n" + ex.Message;
                }
                finally
                {
                    if (cmd != null)
                    {
                        cmd.Dispose();
                    }

                    if (conn != null)
                    {
                        conn.Close();
                        conn.Dispose();
                    }
                }
            }
        }

        #endregion 刪除 ADDT19

        #region 檢查日期是否重疊 ADDT19

        public bool CheckData(DateTime? IMG_DATES, DateTime? IMG_DATEE, string IMG_ID = "", byte? IMG_TYPE = 1)
        {
            bool ReturnData = false;

            if (IMG_DATES == null) return ReturnData;
            if (IMG_DATEE == null) return ReturnData;

            string StrIMG_DATES = IMG_DATES != null ? IMG_DATES.Value.ToString("yyyy/MM/dd") : "";
            string StrIMG_DATEE = IMG_DATEE != null ? IMG_DATEE.Value.ToString("yyyy/MM/dd") : "";

            StringBuilder sb = new StringBuilder();
            DataTable dt;
            sb.Append(" SELECT A.IMG_DATES,A.IMG_DATEE ");
            sb.Append(" FROM ADDT19 A(NOLOCK) ");
            sb.Append(" WHERE A.STATUS='Enabled' ");
            sb.AppendFormat($" and isnull(A.IMG_TYPE,{ADDT19.IMG_TYPEVal.HomeViewImage}) = {0} ", IMG_TYPE);

            if (IMG_ID != string.Empty && IMG_ID != null)
            {
                sb.AppendFormat(" and A.IMG_ID <> '{0}' ", IMG_ID);
            }

            sb.Append(" and ( ");
            sb.AppendFormat("A.IMG_DATES BETWEEN '{0}' AND '{1}'  ", StrIMG_DATES, StrIMG_DATEE);
            sb.AppendFormat(" OR A.IMG_DATEE BETWEEN '{0}' AND '{1}'", StrIMG_DATES, StrIMG_DATEE);
            sb.AppendFormat(" OR '{0}' BETWEEN A.IMG_DATES AND A.IMG_DATEE ", StrIMG_DATES);
            sb.AppendFormat(" OR '{0}' BETWEEN A.IMG_DATES AND A.IMG_DATEE ", StrIMG_DATEE);
            sb.Append(" ) ");
            dt = new sqlConnection().executeQueryByDataTableList(sb.ToString());
            if (dt.Rows.Count > 0)
            {
                ReturnData = true;
            }

            return ReturnData;
        }

        #endregion 檢查日期是否重疊 ADDT19

        #region INSERT INTO ADDT19
        
        private void INSERT_NTO_ADDT19(SqlConnection conn, SqlTransaction transaction, uADDT19 Date)
        {
            IDbCommand cmd = new SqlCommand(@"INSERT INTO ADDT19 ( IMG_ID,IMG_FILE,IMG_TITLE,IMG_DATES
            ,IMG_DATEE,IMG_LINK,STATUS,CRE_DATE,CRE_PERSON,CHG_DATE,CHG_PERSON,IMG_TYPE,SCHOOL_NO)
            VALUES (@IMG_ID,@IMG_FILE,@IMG_TITLE,@IMG_DATES,@IMG_DATEE,@IMG_LINK,@STATUS
            ,@CRE_DATE,@CRE_PERSON,@CHG_DATE,@CHG_PERSON,@IMG_TYPE,@SCHOOL_NO)");

            cmd.Connection = conn;
            cmd.Transaction = transaction;

            cmd.Parameters.Add(
                         (Date.IMG_ID == null)
                         ? new SqlParameter("@IMG_ID", DBNull.Value)
                         : new SqlParameter("@IMG_ID", Date.IMG_ID));

            cmd.Parameters.Add(
            (Date.IMG_FILE == null)
            ? new SqlParameter("@IMG_FILE", DBNull.Value)
            : new SqlParameter("@IMG_FILE", Date.IMG_FILE));

            cmd.Parameters.Add(
            (Date.IMG_TITLE == null)
            ? new SqlParameter("@IMG_TITLE", DBNull.Value)
            : new SqlParameter("@IMG_TITLE", Date.IMG_TITLE));

            cmd.Parameters.Add(
            (Date.IMG_DATES == null)
            ? new SqlParameter("@IMG_DATES", DBNull.Value)
            : new SqlParameter("@IMG_DATES", Date.IMG_DATES));

            cmd.Parameters.Add(
            (Date.IMG_DATEE == null)
            ? new SqlParameter("@IMG_DATEE", DBNull.Value)
            : new SqlParameter("@IMG_DATEE", Date.IMG_DATEE));

            cmd.Parameters.Add(
            (Date.IMG_LINK == null)
            ? new SqlParameter("@IMG_LINK", DBNull.Value)
            : new SqlParameter("@IMG_LINK", Date.IMG_LINK));

            cmd.Parameters.Add(
            (Date.STATUS == null)
            ? new SqlParameter("@STATUS", DBNull.Value)
            : new SqlParameter("@STATUS", Date.STATUS));

            cmd.Parameters.Add(
            (Date.CRE_DATE == null)
            ? new SqlParameter("@CRE_DATE", DBNull.Value)
            : new SqlParameter("@CRE_DATE", Date.CRE_DATE));

            cmd.Parameters.Add(
            (Date.CRE_PERSON == null)
            ? new SqlParameter("@CRE_PERSON", DBNull.Value)
            : new SqlParameter("@CRE_PERSON", Date.CRE_PERSON));

            cmd.Parameters.Add(
           (Date.SCHOOL_NO == null)

           ? new SqlParameter("@SCHOOL_NO", DBNull.Value)

           : new SqlParameter("@SCHOOL_NO", Date.SCHOOL_NO));

            cmd.Parameters.Add(
            (Date.CHG_DATE == null)
            ? new SqlParameter("@CHG_DATE", DBNull.Value)
            : new SqlParameter("@CHG_DATE", Date.CHG_DATE));

            cmd.Parameters.Add(
            (Date.CHG_PERSON == null)
            ? new SqlParameter("@CHG_PERSON", DBNull.Value)
            : new SqlParameter("@CHG_PERSON", Date.CHG_PERSON));

            cmd.Parameters.Add(
          (Date.IMG_TYPE == null)
          ? new SqlParameter("@IMG_TYPE", DBNull.Value)
          : new SqlParameter("@IMG_TYPE", Date.IMG_TYPE));

            try
            {
                cmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                ErrorInt = ErrorInt + 1;
                ErrorMsg = ErrorMsg + "INSERT_NTO_ADDT19 失敗;\r\n" + ex.Message;
            }
            finally
            {
                cmd.Dispose();
            }
        }

        #endregion INSERT INTO ADDT19

        #region UPDATE SET ADDT19

        private void UPDATE_SET_ADDT19(SqlConnection conn, SqlTransaction transaction, uADDT19 Item)
        {
            IDbCommand cmd = new SqlCommand(@" UPDATE ADDT19 set
            IMG_FILE=@IMG_FILE,IMG_TITLE=@IMG_TITLE,IMG_DATES=@IMG_DATES,IMG_DATEE=@IMG_DATEE
            ,IMG_LINK=@IMG_LINK,STATUS=@STATUS,CHG_DATE=@CHG_DATE,CHG_PERSON=@CHG_PERSON,SCHOOL_NO=@SCHOOL_NO
            WHERE IMG_ID=@IMG_ID
            ");

            cmd.Connection = conn;
            cmd.Transaction = transaction;

            cmd.Parameters.Add(
            (Item.IMG_ID == null)
            ? new SqlParameter("@IMG_ID", DBNull.Value)
            : new SqlParameter("@IMG_ID", Item.IMG_ID));

            cmd.Parameters.Add(
            (Item.IMG_FILE == null)
            ? new SqlParameter("@IMG_FILE", DBNull.Value)
            : new SqlParameter("@IMG_FILE", Item.IMG_FILE));

            cmd.Parameters.Add(
            (Item.IMG_TITLE == null)
            ? new SqlParameter("@IMG_TITLE", DBNull.Value)
            : new SqlParameter("@IMG_TITLE", Item.IMG_TITLE));

            cmd.Parameters.Add(
            (Item.IMG_DATES == null)
            ? new SqlParameter("@IMG_DATES", DBNull.Value)
            : new SqlParameter("@IMG_DATES", Item.IMG_DATES));

            cmd.Parameters.Add(
            (Item.IMG_DATEE == null)
            ? new SqlParameter("@IMG_DATEE", DBNull.Value)
            : new SqlParameter("@IMG_DATEE", Item.IMG_DATEE));

            cmd.Parameters.Add(
            (Item.SCHOOL_NO == null)
            ? new SqlParameter("@SCHOOL_NO", DBNull.Value)
            : new SqlParameter("@SCHOOL_NO", Item.SCHOOL_NO));

            cmd.Parameters.Add(
          (Item.IMG_LINK == null)
          ? new SqlParameter("@IMG_LINK", DBNull.Value)
          : new SqlParameter("@IMG_LINK", Item.IMG_LINK));

            cmd.Parameters.Add(
            (Item.STATUS == null)
            ? new SqlParameter("@STATUS", DBNull.Value)
            : new SqlParameter("@STATUS", Item.STATUS));

            cmd.Parameters.Add(
            (Item.CHG_DATE == null)
            ? new SqlParameter("@CHG_DATE", DBNull.Value)
            : new SqlParameter("@CHG_DATE", Item.CHG_DATE));

            cmd.Parameters.Add(
            (Item.CHG_PERSON == null)
            ? new SqlParameter("@CHG_PERSON", DBNull.Value)
            : new SqlParameter("@CHG_PERSON", Item.CHG_PERSON));

            try
            {
                cmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                ErrorInt = ErrorInt + 1;
                ErrorMsg = ErrorMsg + "UPDATE_SET_ADDT19 失敗;\r\n" + ex.Message;
            }
            finally
            {
                cmd.Dispose();
            }
        }

        #endregion UPDATE SET ADDT19

        #region DELETE ADDT19

        private void DELETE_ADDT19(SqlConnection conn, SqlTransaction transaction, string IMG_ID)
        {
            IDbCommand cmd = new SqlCommand(@" DELETE ADDT19 Where IMG_ID=@IMG_ID");
            cmd.Connection = conn;
            cmd.Transaction = transaction;

            cmd.Parameters.Add(
            (IMG_ID == null)
            ? new SqlParameter("@IMG_ID", DBNull.Value)
            : new SqlParameter("@IMG_ID", IMG_ID));

            try
            {
                cmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                ErrorInt = ErrorInt + 1;
                ErrorMsg = ErrorMsg + "DELETE_ADDT19 失敗;\r\n" + ex.Message;
            }
            finally
            {
                cmd.Dispose();
            }
        }

        #endregion DELETE ADDT19

        #region 取得新的ID

        /// <summary>
        /// 取得得新的ID
        /// </summary>
        /// <param name="cmd">SqlCommand</param>
        /// <returns></returns>
        private string GetNewIMG_ID(SqlCommand cmd)
        {
            try
            {
                string NUM = "001";
                //ID =  年 月 日+3 碼流水號
                string sSQL;

                sSQL = " SELECT RIGHT('00'+CONVERT(nvarchar(3),ISNULL(MAX(RIGHT(A.IMG_ID,3)),0)+1),3) AS NUM ";
                sSQL = sSQL + " FROM ADDT19 A ";
                sSQL = sSQL + "  WHERE 1=1 ";
                sSQL = sSQL + "  and CONVERT(varchar(10),A.CRE_DATE,112)='" + DateTime.Now.ToString("yyyyMMdd") + "'  ";

                cmd.CommandText = sSQL;
                var drDate_Header = cmd.ExecuteReader();

                if (drDate_Header.HasRows == true)
                {
                    while (drDate_Header.Read())
                    {
                        NUM = drDate_Header["NUM"].ToString();
                    }
                }

                drDate_Header.Close();

                string ID = DateTime.Now.ToString("yyyyMMdd") + NUM;

                return ID;
            }
            catch (Exception ex)
            {
                ErrorMsg = " 取得新的IMG_ID 失敗;\r\n" + ex.Message;
                return null;
            }
        }

        #endregion 取得新的ID

        /// <summary>
        /// 取得目徑
        /// </summary>
        /// <param name="sTtype">1.實際 2.虛擬</param>
        /// <returns></returns>
        public string GetSYSUrl(byte sTtype)
        {
            string ReturnImgUrl = string.Empty;

            string UploadImageRoot = System.Web.Configuration.WebConfigurationManager.AppSettings["UploadImageRoot"];
            if (UploadImageRoot == null) UploadImageRoot = @"~\Content\";

            if (sTtype == 1)
            {
                ReturnImgUrl = string.Format(@"{0}{1}IMG\", HttpContext.Current.Request.MapPath(UploadImageRoot), "ZZZI25");
            }
            else
            {
                ReturnImgUrl = string.Format(@"{0}{1}IMG\", UploadImageRoot, "ZZZI25");
            }

            return ReturnImgUrl;
        }
    }
}