/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Gyre-Termes/Marks/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.GyreTermesMathJax_Marks={directory:"Marks/Regular",family:"GyreTermesMathJax_Marks",testString:"\u00A0\u02DB\u02DD\u0305\u0309\u030F\u0311\u0323\u0326\u032C\u032D\u032E\u032F\u0330\u0331",32:[0,0,250,0,0],160:[0,0,250,0,0],731:[17,245,333,81,261],733:[676,-505,333,37,427],773:[632,-588,0,-416,-83],777:[704,-517,0,-348,-151],783:[711,-540,0,-510,-120],785:[692,-567,0,-425,-75],803:[-89,191,0,-301,-199],806:[-38,281,0,-319,-180],812:[-70,204,0,-421,-79],813:[-80,214,0,-421,-79],814:[-70,195,0,-425,-75],815:[-88,213,0,-425,-75],816:[-88,197,0,-417,-83],817:[-113,167,0,-405,-94],818:[-70,114,0,-416,-83],819:[-70,228,0,-416,-83],831:[746,-588,0,-416,-83],8192:[0,0,500,0,0],8193:[0,0,1000,0,0],8199:[0,0,500,0,0],8200:[0,0,250,0,0],8203:[0,0,0,0,0],8204:[0,0,0,0,0],8205:[0,0,0,0,0],8208:[257,-194,333,39,285],8210:[357,-305,660,80,580],8213:[276,-224,1160,80,1080],8215:[-70,228,493,80,413],8218:[102,141,333,79,218],8222:[102,141,444,45,416],8226:[400,-100,460,80,380],8239:[0,0,200,0,0],8240:[676,13,1000,14,986],8241:[676,13,1320,14,1306],8246:[780,-450,521,60,461],8247:[780,-450,721,60,661],8249:[411,-33,333,57,278],8250:[411,-33,333,45,266],8251:[514,14,564,18,546],8253:[736,8,444,68,414],8274:[662,0,500,28,472],8287:[0,0,222,0,0],8288:[0,0,0,0,0],8289:[702,202,1008,52,956],8290:[0,0,0,0,0],8291:[0,0,0,0,0],8292:[0,0,0,0,0],8400:[710,-600,0,-438,-62],8401:[710,-600,0,-438,-62],8402:[650,150,0,-272,-228],8403:[500,0,0,-276,-224],8404:[768,-599,0,-452,-48],8405:[768,-599,0,-452,-48],8406:[710,-534,0,-443,-57],8408:[400,-100,0,-400,-100],8411:[660,-560,0,-500,0],8412:[660,-560,0,-600,100],8413:[668,168,0,-668,168],8414:[650,150,0,-650,150],8415:[872,372,0,-872,372],8417:[710,-534,0,-479,-21],8420:[735,209,0,-795,295],8421:[650,150,0,-403,-97],8422:[650,150,0,-344,-156],8424:[-70,170,0,-500,0],8425:[726,-548,0,-438,-63],8426:[430,-70,0,-595,95],8427:[650,150,0,-479,-21],8428:[-150,260,0,-438,-62],8429:[-150,260,0,-438,-62],8430:[-84,260,0,-443,-57],8431:[-84,260,0,-443,-57],8432:[747,-509,0,-356,-143],11800:[503,241,444,30,376],12310:[668,168,430,80,350],12311:[668,168,430,80,350]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"GyreTermesMathJax_Marks"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Marks/Regular/Main.js"]);
