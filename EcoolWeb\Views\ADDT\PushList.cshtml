﻿@model EcoolWeb.Models.ADDT06QueryViewModel
@using ECOOL_APP.com.ecool.Models.entity;

@{
    ViewBag.Title = "閱讀認證-推薦清單";
    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    if (string.IsNullOrWhiteSpace(UUID) == false)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }

}

@functions
{
    int bookNameMaxStr = 10;
    string AdjustmentBookName(string bookName)
    {
        if (bookName == null)
            return "";
        if (bookName.Length > bookNameMaxStr)
        {
            return bookName.Substring(0, bookNameMaxStr) + "...";
        }

        return bookName;
    }
}

@Html.Partial("_Title_Secondary")

@{
    Html.RenderAction("_PageMenu", new { NowAction = "PushList" });
}

@using (Html.BeginForm("PushList", "ADDT", FormMethod.Post, new { id = "ADDT06", name = "form1" }))
{

    <div class="form-inline">
        <div class="form-inline" role="form">
            <div class="form-group">
                <label class="control-label">學號/姓名/文章標題</label>
            </div>
            <div class="form-group">
                @Html.EditorFor(m => m.whereKeyword, new { htmlAttributes = new { @class = "form-control input-sm" } })
                @Html.HiddenFor(m => m.OrdercColumn)
                @Html.HiddenFor(m => m.whereUserNo)
                @Html.HiddenFor(m => m.Page)
                @Html.HiddenFor(m => m.ListType)
                @Html.Hidden("ADDTList", Request["ADDTList"])
            </div>
            <div class="form-group">
                <label class="control-label">年級</label>
            </div>
            <div class="form-group">
                @Html.DropDownListFor(m => m.whereGrade, (IEnumerable<SelectListItem>)ViewBag.GradeItem, new { @class = "form-control input-sm", onchange = "FunPageProc(1)" })
            </div>
            <div class="form-group">
                <label class="control-label">班級</label>
            </div>
            <div class="form-group">
                @Html.DropDownListFor(m => m.whereCLASS_NO, (IEnumerable<SelectListItem>)ViewBag.ClassItems, new { @class = "form-control input-sm", onchange = "FunPageProc(1)" })
            </div>
            <input type="button" class="btn-yellow btn btn-sm" value="搜尋" onclick="FunPageProc(1)" />
            <input type="button" class="btn-yellow btn btn-sm" value="清除搜尋" onclick="todoClear();" />
        </div>
        <br />
        <div class="row">
            <div class="col-xs-12 text-right">
                <a class="btn btn-xs btn-pink @(Model.ListType== EcoolWeb.Models.ADDT06ListType.推薦清單文? "active":"" )" type="button"
                   href="@Url.Action("PushList","ADDT",new { ListType =0 })">推薦清單文</a>
                <a class="btn btn-xs btn-pink @(Model.ListType== EcoolWeb.Models.ADDT06ListType.推薦清單圖? "active":"" )" type="button"
                   href="@Url.Action("PushList","ADDT",new { ListType =1 })">推薦清單圖</a>
            </div>
        </div>
    </div>

    @section scripts{
        <script>
            var targetFormID = '#ADDT06';
            jQuery(document).ready(function ($) {
                if ("@Model.whereKeyword" == "推薦清單圖") {

                    $('#@Html.IdFor(m=>m.whereKeyword)').val("");
                    FunPageProc(1);
                }
            });
            function FunPageProc(page) {
                if ($(targetFormID).size() > 0) {
                    $('#Page').val(page)
                    $(targetFormID).submit();
                }
            };

            function doSort(SortCol) {
                $("#OrdercColumn").val(SortCol);
                FunPageProc(1)
            }
            function imagesRotateJS(ImgURL_Val, RotateAngle_Val, IDX_Val) {
                $(this).attr('disabled', 'disabled').html("Loading...資料驗証中…請勿其他動作");
        if (!ImgURL_Val) {
            return;
        }
        $.ajax({
            url: "@Url.Action("imagesRotate", "Comm")",     // url位置
            type: 'post',                   // post/get
        data: {
            ImgURL: ImgURL_Val
            , RotateAngle: RotateAngle_Val
        },     // data
        async: false,
        dataType: 'json',               // xml/json/script/html
        cache: false,                   // 是否允許快取
        success: function (data) {

           $('#'+IDX_Val).attr("src",data+'?'+new Date());

        },
        error: function (xhr, err) {
            alert("readyState: " + xhr.readyState + "\nstatus: " + xhr.status);
            alert("responseText: " + xhr.responseText);
        }
    });
  }
            function doSearch(ColName, whereValue) {
                $("#" + ColName).val(whereValue);
                FunPageProc(1)
            }
            function ShowColorbox(APPLY_NO,ListType)
              {

                $('#@Html.IdFor(m=>m.OrdercColumn)').val(APPLY_NO)
                console.log("ListType" + APPLY_NO);
                $('#@Html.IdFor(m=>m.whereKeyword)').val(ListType)
        $.ajax({
            type: 'POST',
            url: '@Url.Action("OneIndex", (string)ViewBag.BRE_NO)',
            data: $(targetFormID).serialize(),
            success: function (data) {
                $.colorbox({ html: data, width: "80%", height: "80%", opacity: 0.82 });
            }
        });
    }
            function todoClear() {
                $("#whereKeyword").val('');
                $("#whereGrade").val('');
                $("#whereCLASS_NO").val('');
                $("#OrdercColumn").val('');
                FunPageProc(1)
            }
        </script>
    }

    <img src="~/Content/img/web-Bar-09.png" style="width:100%" class="img-responsive App_hide" alt="Responsive image" />
    <div class="table-responsive">
        <div class="text-center">
            @if (Model.ListType == EcoolWeb.Models.ADDT06ListType.推薦清單圖)
            {
                List<Image_File> ImageModel = (List<Image_File>)ViewBag.ImageUrl;

                int i = 0;
                <div class="row">
                    @foreach (var item in Model.ADDT06List)
                    {
                        DateTime dateTime = new DateTime().AddSeconds(1);
                        <div class="col-md-3" style="height:290px;">
                            <div>
                                @if (  /* 已篩選只看圖片的(無文字) */
                                ImageModel[i].APPLY_NO == item.APPLY_NO
                                && !string.IsNullOrEmpty(ImageModel[i].ImageUrl)
                                )
                                {
                                    <img src="@(ImageModel[i].ImageUrl+"?"+dateTime)"id="@i" class="img-thumbnail img-responsive" style="margin:10px;height:150px;width:200px" href="@ImageModel[i].ImageUrl" onclick="ShowColorbox('@item.APPLY_NO', '@Model.ListType')" />
                                    <button onclick="imagesRotateJS('@ImageModel[i].ImageUrl','90','@i');"><i class="fa fa-repeat" aria-hidden="true"></i></button>
                                    <span>@Html.DisplayFor(modelItem => item.CRE_DATE, "ShortDateTime")</span><br />
                                    <span class="text-primary">姓名: @Html.DisplayFor(modelItem => item.NAME)</span> <br />
                                    <span class="text-success">書名: @AdjustmentBookName(item.BOOK_NAME)</span><br />
                                }
                                else
                                {
                                    <div class="text-danger pull-left" style="margin:10px;height:150px;width:200px;"></div>
                                    <span>@Html.DisplayFor(modelItem => item.CRE_DATE, "ShortDateTime")</span><br />
                                    <span class="text-primary">姓名: @Html.DisplayFor(modelItem => item.NAME)</span> <br />
                                    <span class="text-success">書名: @AdjustmentBookName(item.BOOK_NAME) </span><br />
                                }
                                <a href='@Url.Action("PushListDetail", "ADDT", new { APPLY_NO = item.APPLY_NO, ListType = Model.ListType })' class="btn btn-xs btn-default">
                                    檢視
                                </a>
                            </div>
                        </div>
                        i++;
                    }
                </div>
            }
            else
            {
                <table class="table-ecool table-92Per table-hover table-ecool-reader">
                    <thead>
                        <tr>
                            <th style="text-align: center;cursor:pointer;" onclick="doSort('CLASS_NO');">
                                班級
                                <img id="CLASS_NO" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                            </th>
                            <th style="text-align: center;cursor:pointer;" onclick="doSort('SEAT_NO');">
                                座號
                                <img id="SEAT_NO" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                            </th>
                            <th style="text-align: center;cursor:pointer;" onclick="doSort('SNAME');">
                                姓名
                                <img id="SNAME" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                            </th>
                            <th style="text-align: center;cursor:pointer;" onclick="doSort('BOOK_NAME');">
                                書名
                                <img id="BOOK_NAME" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                            </th>
                            <th style="text-align: center;cursor:pointer;" onclick="doSort('CRE_DATE');">
                                閱讀日期
                                <img id="CRE_DATE" src="@Url.Content("~/Content/img/sort-descending.gif")" style="width:10px" />
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var item in Model.ADDT06List)
                        {
                            <tr>
                                <td>
                                    @Html.DisplayFor(modelItem => item.CLASS_NO)
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.SEAT_NO)
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.SNAME)
                                </td>
                                <td class="text-left">
                                    <a href='@Url.Action("PushListDetail", "ADDT", new { APPLY_NO = item.APPLY_NO })' class="btn-table-link">
                                        @item.BOOK_NAME
                                    </a>
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.CRE_DATE, "ShortDateTime")
                                </td>
                            </tr>

                        }
                    </tbody>
                </table>
            }
        </div>
    </div>

    <div>
        @Html.Pager(Model.ADDT06List.PageSize, Model.ADDT06List.PageNumber, Model.ADDT06List.TotalItemCount).Options(o => o
              .DisplayTemplate("BootstrapPagination")
             .MaxNrOfPages(5)
             .SetPreviousPageText("上頁")
             .SetNextPageText("下頁")
         )
    </div>
    <br />
    <div style="height:15px"></div>
    <div class="row">
        <div class="text-center">
            @if (Request["ADDTList"] != null)
            {
                <a href='@Url.Action("ADDTList", "ADDT")' class="btn btn-default" role="button">
                    返回
                </a>
            }
        </div>
    </div>

}