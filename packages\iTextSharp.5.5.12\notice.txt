iTextSharp consists of several dlls.

The main iTextSharp release contains:
- itextsharp.dll: the core library
- itextsharp.xtra.dll: extra functionality (PDF 2!)
- itextsharp.pdfa.dll: PDF/A-related functionality
This project is hosted on http://sourceforge.net/projects/itextsharp/
You can find the latest release here:
http://sourceforge.net/projects/itextsharp/files/itextsharp/

In some cases, you'll need extra dlls.
These dlls are available here:
http://sourceforge.net/projects/itextsharp/files/extras/

For XML (and HTML) functionality, you need this dll:
- itextsharp.xmlworker.dll
This is available on http://sourceforge.net/projects/itextsharp/files/xmlworker/

Finally, we also have a Java tool that can help you debug PDFs:
- itext-rups-x.y.z.jar
This project is hosted on http://sourceforge.net/projects/itextrups/

iTextSharp is licensed as AGPL software.
AGPL is a free / open source software license.
This doesn't mean the software is gratis!
Buying a license is mandatory as soon as you develop commercial activities
distributing the iTextSharp software inside your product or deploying it on a network
without disclosing the source code of your own applications under the AGPL license.
These activities include: offering paid services to customers as an ASP,
serving PDFs on the fly in the cloud or in a web application,
shipping iTextSharp with a closed source product.

Contact sales for more info: http://itextpdf.com/sales