﻿using ECOOL_APP.com.ecool.Models.DTO;
using MvcPaging;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;

namespace ECOOL_APP.EF
{
    public class ZZZI34IndexViewModel
    {
        public string IsPostBack { get; set; }

        public ZZZI34SearchViewModel Search { get; set; }

        public bool? WhereMyWork { get; set; }

        public bool? WhereVerify { get; set; }
        public byte? ModeValue { get; set; }
        public string WhereFrom { get; set; }
        /// <summary>
        /// 一頁筆數 預設筆數
        /// </summary>
        public int PageSize { get; set; }

        /// <summary>
        /// 查詢結果
        /// </summary>
        public IPagedList<ZZZI34IndexListDataViewModel> ListData;

        public ZZZI34IndexViewModel()
        {
            PageSize = 9;
        }
    }
}