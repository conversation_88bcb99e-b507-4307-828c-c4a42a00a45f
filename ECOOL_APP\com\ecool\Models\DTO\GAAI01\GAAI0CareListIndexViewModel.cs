﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class GAAI0CareListIndexViewModel
    {
        public byte IsSearch { get; set; }

        public string DivId { get; set; }

        public string WhereSCHOOL_NO { get; set; }

        [DisplayName("通知單的預設文字")]
        public string ALARM_NOTICE { get; set; }

        /// <summary>
        /// 班級
        /// </summary>
        [DisplayName("班級")]
        public string WhereCLASS_NO { get; set; }

        public string WhereSYEARSEMESTER { get; set; }

        [DisplayName("學年度")]
        public byte? WhereSYEAR { get; set; }

        [DisplayName("學期")]
        public byte? WhereSEMESTER { get; set; }

        public ICollection<GAAI01CareFoDateClassViewModel> CareFoDateClass { get; set; }

        public ICollection<GAAI01CareFoUserViewModel> CareFoUser { get; set; }

        public List<GAAI01SearchWearDetailListViewModel> WearDetailList { get; set; }
    }
}