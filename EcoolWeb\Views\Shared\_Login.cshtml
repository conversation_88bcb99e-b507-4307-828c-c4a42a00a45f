﻿@model object

<style type="text/css">
    .modal {
        text-align: center;
        padding: 0 !important;
    }
    .modal:before {
        content: '';
        display: inline-block;
        height: 100%;
        vertical-align: middle;
        margin-right: -4px;
    }
    .modal-dialog {
        display: inline-block;
        text-align: left;
        vertical-align: middle;
    }

    .modal-content {
        -webkit-border-radius: 0px !important;
        -moz-border-radius: 0px !important;
        border-radius: 0px !important;
    }
    .selector {
              background-image:url('@Url.Content("~/Content/img/web_png-11body.png")');
    }
   .form-control-txtSCHOOL_NO{
        height:30px;
        line-height:30px;
        padding:6px;
    }

</style>

@*<link href="~/Content/css/bootstrap.css" rel="stylesheet" />*@
<style type="text/css">
    .classInputCode {
        display: none
    }
</style>
@{
    int iLockCount = (Session["LockCount"] != null) ? Convert.ToInt32(Session["LockCount"]) : 0;
    int iVlidatePasswordMaxCount = Convert.ToInt32(System.Web.Configuration.WebConfigurationManager.AppSettings["VlidatePasswordMaxCount"]);
}

<table border="0" cellpadding="0" cellspacing="0" align="center" class="table_Menu">
    <tr>
        <td>
            <img src='@Url.Content("~/Content/img/web_png-11head.png")' class="imgMenu" />
        </td>
    </tr>
    <tr>
        <td align="center" class="font_Menu selector">
            @using (Html.BeginForm("Login", "Home", FormMethod.Post, new { name = "loginform", defaultbutton = "Button1", defaultfocus = "TextBox1" }))
            {
                string SchoolNo = EcoolWeb.Models.UserProfileHelper.GetSchoolNo();
                @Html.Hidden("iLockCount", iLockCount)
                @Html.Hidden("iVlidatePasswordMaxCount", iVlidatePasswordMaxCount)
                @Html.Hidden("ChildMonthIndex", Model)
                @Html.AntiForgeryToken()
                <table>
                    @if (SchoolNo != null && Model == null)
                    {
                        @Html.Hidden("txtSCHOOL_NO", SchoolNo)
                    }
                    else
                    {

                        var SchoolList = EcoolWeb.Models.UserProfileHelper.GetSchoolLists();
                        var ZONE_IDs = SchoolList.GroupBy(a => new { a.ZONE_ID, a.CITY }).Select(a => new { a.Key.CITY, a.Key.ZONE_ID }).OrderByDescending(a => a.CITY).ThenBy(a => a.ZONE_ID).ToList();
                        <tr>
                            <td>
                                <div class="form-inline">
                                    <div class="form-group">

                                        <label class="lobinLabel">學 校</label>
                                        <select class="selectpicker show-menu-arrow" date-style="form-control-txtSCHOOL_NO" style="height:40px" data-size="auto" data-width="95px" data-h data-live-search="true" id="txtSCHOOL_NO" name="txtSCHOOL_NO"
                                                title="選擇學校...">

                                            @foreach (var ZONE_ID in ZONE_IDs)
                                            {

                                                var strlabel = !string.IsNullOrWhiteSpace(ZONE_ID.ZONE_ID) ? ZONE_ID.ZONE_ID : (ZONE_ID.CITY);

                                                <optgroup label="@strlabel">
                                                    @foreach (var item in SchoolList.Where(a => a.CITY == ZONE_ID.CITY && (a.ZONE_ID ?? "") == (ZONE_ID.ZONE_ID ?? "")))
                                                    {
                                                        <option data-tokens="@item.SHORT_NAME" value="@item.SCHOOL_NO" @(item.SCHOOL_NO == SchoolNo ? "selected" : "")>@item.SHORT_NAME</option>
                                                    }
                                                </optgroup>
                                            }
                                        </select>

                                        <script>
                                            $(document).ready(function () {
                                                $('#txtSCHOOL_NO').selectpicker();
                                            });
                                        </script>

                                        @*@Html.DropDownList("txtSCHOOL_NO", SchoolList.OrderBy(a => a.Text), "選擇學校...", new { @class = "input-sm LoginBox" })*@
                                    </div>
                                </div>
                            </td>
                        </tr>
                    }
                    <tr>
                        <td>
                            <div class="form-inline">
                                <div class="form-group">
                                    <label class="lobinLabel">帳 號 </label>
                                    @Html.TextBox("txtUSER_NO", "", new { @class = "input-xs LoginBox form-control" })
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr>

                        <td>
                            <div class="form-inline">
                                <div class="form-group">
                                    <label class="lobinLabel">密 碼 </label>
                                    @Html.Password("txtPASSWORD", "", new { @class = "input-xs LoginBox form-control" })
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr class="classInputCode">
                        <td>
                            <div class="form-inline">
                                <div class="form-group">
                                    <label class="lobinLabel">驗 證 </label>
                                    @Html.TextBox("txtInputCode", "", new { @class = "input-xs LoginBox", placeholder = "輸入驗證碼" })
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr class="classInputCode">
                        <td>
                            <div class="form-inline">
                                <div class="form-group">
                                    <label class="lobinLabel">　 　 </label>
                                    <img id="captcha" src="@Url.Action("VerificationCode","Home")" alt="驗證碼" />
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td style="white-space:nowrap" align="right">
                            <samp class="classInputCode">
                                <input type="button" class="btn btn-default btn-sm" value="重取驗證"
                                       onclick="ImgCatcha()" />
                            </samp>
                            <input type="button" class="btn btn-link btn-sm" style="color:lightgoldenrodyellow;" value="忘記密碼?" onclick="openForgetPasswordModal()" />
                            <input type="button" class="btn btn-default btn-sm " value="登入" onclick='Auth.AuthIdentity()' />
                        </td>
                    </tr>
                </table>
            }
        </td>
    </tr>
    <tr>
        <td>
            <img src='@Url.Content("~/Content/img/web_png-11footer.png")' class="imgMenu" />
        </td>
    </tr>
    <tr>
        <td>
            <a href='https://ecc.tp.edu.tw/EcoolWeb/SSO/SSOLoginPage'>
                <img src='@Url.Content("~/Content/images/oidc-v.png")' style="width:100%" />
            </a>
        </td>
    </tr>
    @*@if (Model == null)
        {

            <tr>
                <td>
                    <a href='https://sso.tp.edu.tw/oauth2/oauth/authorize?client_id=squ19oBtWQZaPUZ3079bvSK0zzoOHEgK&response_type=code&login_type=simple&redirect_uri=http://ecc.hhups.tp.edu.tw/EcoolWeb/COOC/SSOBACK'>
                        <img src='@Url.Content("~/Content/img/LogoCooc-2.png")' style="width:100%" />
                    </a>
                </td>
            </tr>
        }*@
</table>

<div id="divPartial">
    @Html.Partial("__ForgetPasswordModal")
</div>

<script type="text/javascript">
         // 權限控管物件
        var Auth = new AuthorizeClass();
        function AuthorizeClass() {
                //使用者名稱
                this.getUser = function () {
                    return $("#txtUSER_NO").val();
                }
                //使用者密碼
                this.getPassword = function () {
                    return $("#txtPASSWORD").val();
               }
                    //使用者學校No.
                this.getSchoolNo = function () {
                    return $("#txtSCHOOL_NO").val();
                }
                this.strMsg = "";
                this.validateLoginTextBox = function () {
                    strMsg = '';
                    if (this.getUser() == "") {
                        strMsg = '請輸入帳號';
                    }
                    if (this.getPassword() == "") {
                        strMsg = '請輸入密碼';
                    }
                    if (this.getSchoolNo() == "") {
                        strMsg = '請選擇學校';
                    }
                    if ($('.classInputCode').is(":visible") == true) {
                        if ($("#txtInputCode").val() == "") {
                            strMsg = '請輸入驗証碼';
                        }
                    }
                    console.log(strMsg);
                    if (strMsg != '') {
                        alert(strMsg);
                        return false;
                    }
                    return true;
                }
                this.AuthIdentity = function () {
                    if (this.validateLoginTextBox() == false) {
                        return;
                    }
                    $.ajax({
                        url: '@Url.Action("CheckUserStatus","Home")',
                        data: { SchoolNo: this.getSchoolNo(), UserNo: this.getUser(), sPwd: this.getPassword(), InputCode: $("#txtInputCode").val(), CheckCode: true },
                        type: 'post',
                        dataType: 'json',               // xml/json/script/html
                        cache: false,                   // 是否允許快取
                        success: function (data) {
                            var res = jQuery.parseJSON(data);
                            if (res.ReValue != '') {
                                strMsg += res.ReValue + '\r\n';

                                if (res.IsInputCode == 1) {
                                    $('.classInputCode').show()
                                }
                                alert(strMsg);
                                return;
                            }
                            else {
                                document.loginform.submit();
                            }
                        },
                         error: function (request, status, error) {
                            alert(request.responseText);
                        }
                  });
                }
            }

        $(function () {
            $("form input").keypress(function (e) {
                if ((e.which && e.which == 13) || (e.keyCode && e.keyCode == 13)) {
                    //$('button[type=submit] .default').click();
                    Auth.AuthIdentity();
                    return false;
                } else {
                    return true;
                }
            });
        });

        window.onload = function ()
        {
            if ($('#iLockCount').val() >= $('#iVlidatePasswordMaxCount').val())
            {
                $('.classInputCode').show()
            }
        }

        function ImgCatcha()
        {
            var Url = '@Url.Action("VerificationCode", "Home")' + '?r='+(new Date()).getTime()+''
            $('#captcha').attr('src', Url);
        }
</script>