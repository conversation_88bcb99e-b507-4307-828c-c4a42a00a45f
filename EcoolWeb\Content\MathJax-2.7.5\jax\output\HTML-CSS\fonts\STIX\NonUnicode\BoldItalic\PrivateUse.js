/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/NonUnicode/BoldItalic/PrivateUse.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXNonUnicode-bold-italic"],{57500:[775,235,776,40,765],57501:[775,235,759,44,779],57502:[775,235,658,44,771],57523:[703,205,556,-188,517],57846:[688,13,500,89,578],57847:[688,0,500,204,505],57848:[688,0,500,20,581],57849:[688,13,500,32,586],57850:[688,0,500,55,583],57851:[676,13,500,27,651],57852:[688,13,500,80,638],57853:[676,0,500,120,639],57854:[688,13,500,63,594],57855:[688,13,500,28,588],57856:[669,0,733,7,667],57857:[669,0,729,18,714],57859:[669,0,680,18,703],57860:[669,0,474,18,703],57861:[685,14,718,35,708],57862:[669,0,382,22,411],57863:[669,14,603,19,644],57864:[669,0,766,18,766],57865:[669,0,613,18,568],57866:[669,0,912,26,943],57867:[685,14,749,35,734],57868:[685,14,686,30,711],57869:[669,0,445,30,653],57870:[669,14,709,35,755],57871:[669,0,504,42,705],57872:[669,0,891,81,991],57873:[669,0,759,7,832],57874:[669,0,462,10,714],57875:[462,13,634,45,589],57876:[699,13,661,34,619],57877:[462,13,571,45,545],57879:[462,13,575,45,540],57880:[699,0,438,45,618],57881:[462,205,666,28,642],57882:[699,0,661,34,616],57885:[699,0,641,34,616],57886:[699,0,372,34,413],57887:[462,0,942,35,897],57888:[462,0,661,34,616],57889:[462,13,586,45,551],57890:[462,205,680,3,645],57891:[462,205,662,45,630],57892:[462,0,403,33,538],57893:[462,13,533,33,519],57894:[676,14,403,22,422],57895:[449,13,661,45,627],57896:[449,0,477,32,534],57897:[449,0,733,55,763],57898:[449,0,562,-12,589],57899:[449,205,584,-9,643],57900:[449,0,619,35,594],57927:[711,47,871,38,834],57928:[703,10,755,33,740],57929:[704,12,667,36,669],57930:[696,0,802,30,808],57931:[704,8,609,41,626],57932:[696,0,645,34,738],57933:[704,144,615,43,615],57934:[696,24,849,22,858],57935:[696,0,621,36,623],57936:[695,116,645,36,811],57937:[703,14,856,38,820],57938:[704,8,726,38,688],57939:[705,45,1186,38,1146],57940:[835,39,997,36,1098],57941:[707,10,772,43,782],57942:[696,0,645,36,731],57943:[704,145,778,43,737],57944:[697,13,869,36,831],57945:[705,7,667,36,699],57946:[783,0,547,33,747],57947:[700,14,787,33,936],57948:[711,31,652,36,706],57949:[711,34,956,36,1010],57950:[710,14,720,36,781],57951:[711,144,720,36,773],57952:[702,98,778,36,744],57956:[473,10,600,47,554],57960:[473,0,600,95,450],57964:[473,0,600,54,531],57968:[463,217,600,31,547],57972:[450,217,600,30,564],57976:[450,218,600,25,561],57980:[670,10,600,55,545],57984:[450,217,600,24,582],57988:[670,10,600,41,560],57992:[463,217,600,49,539],58005:[775,235,776,40,739],58007:[775,235,762,44,747],58009:[775,235,711,57,753],58011:[775,235,870,44,840],58013:[775,235,759,44,779],58015:[775,235,658,44,771],58017:[775,235,789,57,787],58019:[775,235,915,44,940],58021:[775,235,502,46,525],58023:[775,235,648,68,688],58025:[775,207,814,44,838],58027:[775,235,764,44,718],58029:[775,235,1044,44,1069],58031:[775,235,857,44,882],58033:[775,235,802,57,777],58035:[775,207,626,19,790],58037:[775,245,834,57,777],58039:[775,235,783,44,757],58041:[775,235,589,57,621],58043:[775,235,562,30,696],58045:[775,235,745,74,813],58047:[775,235,597,66,774],58049:[775,235,980,66,1131],58051:[775,235,803,34,819],58053:[775,235,569,25,706],58055:[775,235,720,42,701],58057:[775,235,630,46,595],58059:[775,235,585,57,564],58061:[775,235,511,33,506],58063:[775,235,646,31,638],58065:[775,235,512,44,516],58067:[775,235,654,-29,762],58069:[775,235,601,24,599],58071:[775,235,611,35,577],58073:[775,207,373,34,488],58075:[775,235,600,-29,763],58077:[775,235,622,35,660],58079:[775,207,381,30,484],58081:[775,235,873,35,838],58083:[775,235,611,35,581],58085:[775,235,571,46,548],58087:[775,235,636,-25,649],58089:[775,207,580,46,568],58091:[775,235,437,35,567],58093:[775,235,512,42,515],58095:[775,207,411,32,486],58097:[775,235,632,60,597],58099:[775,207,554,52,558],58101:[775,207,814,17,799],58103:[775,235,647,35,622],58105:[775,207,599,20,640],58107:[775,235,531,35,555],58157:[775,207,671,46,675],58159:[775,207,664,-65,706],58161:[775,207,588,-100,671],58163:[775,207,571,46,547],58165:[775,207,508,44,515],58167:[775,207,505,-54,629],58169:[775,207,579,20,583],58171:[775,207,615,46,602],58173:[775,207,355,29,483],58175:[775,207,594,35,656],58177:[775,207,598,18,642],58179:[775,207,697,-34,737],58181:[775,207,571,35,584],58183:[775,207,504,-54,629],58185:[775,235,500,32,506],58187:[775,207,652,1,772],58189:[775,207,636,27,652],58191:[775,207,504,23,514],58193:[775,207,595,46,641],58195:[775,207,474,20,521],58197:[775,207,582,20,584],58199:[775,207,726,1,772],58201:[775,207,622,-41,730],58203:[775,207,720,37,808],58205:[775,207,782,24,795],58207:[775,207,608,20,681],58209:[775,207,727,0,771],58211:[775,207,925,6,978],58215:[775,235,475,-35,509],58219:[775,235,525,-68,651],58223:[775,235,485,16,466],58227:[775,235,530,12,731],58229:[775,235,569,-50,592],58231:[775,207,571,46,547],58233:[775,207,601,46,579],58235:[775,207,525,46,543],58238:[775,235,792,-40,777],58240:[707,14,670,10,662],58242:[707,14,622,14,598],58244:[628,14,411,18,390],58246:[473,14,355,15,338],58248:[666,0,493,25,508],58249:[666,0,480,16,472],58309:[462,207,514,47,475],58310:[462,9,357,55,274]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/NonUnicode/BoldItalic/PrivateUse.js");
