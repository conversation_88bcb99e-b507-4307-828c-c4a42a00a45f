﻿@model ADDI12IndexListDataViewModel

@if (Model != null)
{

    <style type="text/css">
        .videoWrapperOuter {
            max-width: 1024px;
            margin-left: auto;
            margin-right: auto;
        }

        .videoWrapperInner {
            float: none;
            clear: both;
            width: 100%;
            position: relative;
            padding-bottom: 50%;
            padding-top: 25px;
            height: 0;
        }

            .videoWrapperInner iframe {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 95%;
            }
    </style>

    <div class="panel panel-Img" name="TOP" style="width:98%;text-align:center;margin: 0px auto">
        <div class="panel-heading text-center" style="height:25px">
            @Model.STAGE_NAME
        </div>
        <div class="panel-body" style="width:90%;text-align:center;margin: 0px auto">
            <div class="videoWrapperOuter">
                <div class="videoWrapperInner">
                    <iframe src="@(Model.YOUTUBE_URL)?rel=0&autoplay=1"
                            frameborder="0" allow="autoplay; encrypted-media" allowfullscreen></iframe>
                </div>
            </div>
        </div>
    </div>
    <Br />
}