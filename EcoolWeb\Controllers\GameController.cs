﻿using com.ecool.service;
using ECOOL_APP;
using ECOOL_APP.EF;
using EcoolWeb.CustomAttribute;
using EcoolWeb.Models;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Web;
using System.Web.Mvc;
using ECOOL_APP.com.ecool.util;
using System.Net;
using System.Text;
using MvcPaging;
using System.Transactions;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Web.Http.Cors;
using UAParser;
using System.Data;
using NPOI.SS.UserModel;
using Dapper;
using IsolationLevel = System.Transactions.IsolationLevel;
using ECOOL_APP.com.ecool.Models.DTO;

namespace EcoolWeb.Controllers
{
    [SessionExpire]
    public class GameController : Controller
    {
        /// <summary>
        /// 此Controller 對應的程式代碼 測試
        /// </summary>
        private static string Bre_NO = "Game";

        private static string Bre_Name = string.Empty;

        /// <summary>
        /// 會員相關資訊
        /// </summary>
        private UserProfile user;

        private string SCHOOL_NO = string.Empty;
        private byte? GAME_TYPE = null;

        private ECOOL_DEVEntities db = new ECOOL_DEVEntities();

        private GameService gameService = new GameService();

        [CheckPermissionSeeion(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult Index()
        {
            this.GetBreName();
            this.Shared("行動學習E酷幣市集");

            if (GAME_TYPE == (byte)ADDT26.GameType.有獎徵答)
            {
                return RedirectToAction("GameIndex");
            }

            return View();
        }
        [CheckPermissionSeeion(CheckACTION_ID = "Index1")] //檢查權限
        public ActionResult Index1()
        {
            this.GetBreName();
            this.Shared("行動學習E酷幣市集");

            if (GAME_TYPE == (byte)ADDT26.GameType.有獎徵答)
            {
                return RedirectToAction("GameIndex1");
            }

            return View();
        }

        //[CheckPermissionSeeion(CheckACTION_ID = "Index")] //檢查權限
        //public ActionResult PsIndex()
        //{
        //    this.GetBreName();
        //    this.Shared("行動學習E酷幣市集 ");
        //    return View();
        //}

        ///// <summary>
        /////
        ///// </summary>
        ///// <param name="NowAction"></param>
        ///// <returns></returns>
        //[CheckPermissionSeeion(CheckACTION_ID = "Index")] //檢查權限
        //public ActionResult _EGameMenu(string NowAction)
        //{
        //    this.GetBreName();
        //    this.Shared();
        //    ViewBag.NowAction = NowAction;
        //    return PartialView();
        //}

        /// <summary>
        /// Menu
        /// </summary>
        /// <param name="NowAction"></param>
        /// <returns></returns>
        [CheckPermissionSeeion(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult _ByOneGameMenu(string NowAction)
        {
            this.GetBreName();
            this.Shared();

            ViewBag.Permission = PermissionService.GetActionPermissionForBreNO(UserProfileHelper.GetBRE_NO(), user?.SCHOOL_NO, user?.USER_NO).Where(a => a.BoolUse).ToList();

            ViewBag.NowAction = NowAction;
            return PartialView();
        }
        [CheckPermissionSeeion(CheckACTION_ID = "Index")]
        public ActionResult _ByOneGameMenu1(string NowAction)
        {
            this.GetBreName();
            this.Shared();

            ViewBag.Permission = PermissionService.GetActionPermissionForBreNO(UserProfileHelper.GetBRE_NO(), user?.SCHOOL_NO, user?.USER_NO).Where(a => a.BoolUse).ToList();

            ViewBag.NowAction = NowAction;
            return PartialView();
        }
        /// <summary>
        /// 活動列表
        /// </summary>
        /// <returns></returns>

        [CheckPermissionSeeion(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult GameIndex()
        {
            this.GetBreName();
            this.Shared(Bre_Name + " - 列表");
            return View();
        }
        [CheckPermissionSeeion(CheckACTION_ID = "GameIndex1")] //檢查權限
        public ActionResult GameIndex1()
        {
            this.GetBreName();
            this.Shared(Bre_Name + " - 列表");
            return View();
        }
        /// <summary>
        /// 活動 列表 PartialView
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [CheckPermissionSeeion(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult _PageContent(GameIndexViewModel model)
        {
            this.GetBreName();
            this.Shared();
            if (model == null) model = new GameIndexViewModel();
            if (model.Search == null) model.Search = new GameSearchViewModel();

            if (string.IsNullOrWhiteSpace(model.Search.WhereSCHOOL_NO))
            {
                model.Search.WhereSCHOOL_NO = SCHOOL_NO;
            }

            ViewBag.Permission = PermissionService.GetActionPermissionForBreNO(UserProfileHelper.GetBRE_NO(), user?.SCHOOL_NO, user?.USER_NO).Where(a => a.BoolUse).ToList();

            model = gameService.GetListData(model, ref db, this.GAME_TYPE);

            return PartialView(model);
        }
        [CheckPermissionSeeion(CheckACTION_ID = "GameIndex1")] //檢查權限
        public ActionResult _PageContent1(GameIndexViewModel model)
        {
            this.GetBreName();
            this.Shared();
            if (model == null) model = new GameIndexViewModel();
            if (model.Search == null) model.Search = new GameSearchViewModel();

            if (string.IsNullOrWhiteSpace(model.Search.WhereSCHOOL_NO))
            {
                model.Search.WhereSCHOOL_NO = SCHOOL_NO;
            }

            ViewBag.Permission = PermissionService.GetActionPermissionForBreNO(UserProfileHelper.GetBRE_NO(), user?.SCHOOL_NO, user?.USER_NO).Where(a => a.BoolUse).ToList();

            model = gameService.GetListData1(model, ref db, (byte)ADDT26.GameType.贈品抽獎);
            
            return PartialView(model);
        }
        #region 活動編輯

        #region 一般模式

        /// <summary>
        /// 活動編輯 一般模式
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [CheckPermissionSeeion(CheckACTION_ID = "Edit")] //檢查權限
        public ActionResult Edit(GameEditViewModel model)
        {
            this.GetBreName();
            this.Shared(Bre_Name + " - 編輯設定");
            model.GAME_TYPE = (byte)ADDT26.GameType.一般;

            if (model == null) model = new GameEditViewModel();
            if (model.Search == null) model.Search = new GameSearchViewModel();

            if (!string.IsNullOrWhiteSpace(model.Search.WhereGAME_NO))
            {
                model = this.gameService.GetEditData(model, ref db);
            }

            return View(model);
        }
        /// <summary>
        /// 活動編輯 一般模式
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [CheckPermissionSeeion(CheckACTION_ID = "Edit1")] //檢查權限
        public ActionResult Edit1(GameEditViewModel model)
        {
            this.GetBreName();
            this.Shared("抽獎點數(獎品) - 編輯設定");
            model.GAME_TYPE = (byte)ADDT26.GameType.贈品抽獎;

            if (model == null) model = new GameEditViewModel();
            if (model.Search == null) model.Search = new GameSearchViewModel();

            if (!string.IsNullOrWhiteSpace(model.Search.WhereGAME_NO))
            {
                model = this.gameService.GetEditDataItemPrize(model, ref db);
            }
            List<SelectListItem> HRMT25Select = new List<SelectListItem>();
            SelectListGroup group1 = new SelectListGroup() { Name = "校內榮譽(不受每個月控管的總點數)" };

            //導師進來看到group2group3
            //行政人員不要看到group2
            SelectListGroup group2 = new SelectListGroup() { Name = "班級表現" };
            SelectListGroup group3 = new SelectListGroup() { Name = "特殊表現(不出現在E本書)" };
            //行政人員不要看到group2
            int NteacherCount = 0;
            int YteacheCount = 0;
            NteacherCount = db.HRMT25.Where(x => x.SCHOOL_NO == user.SCHOOL_NO && x.USER_NO == user.USER_NO && x.ROLE_ID == "8").Count();
            YteacheCount = db.HRMT01.Where(x => x.SCHOOL_NO == user.SCHOOL_NO && x.USER_NO == user.USER_NO && x.CLASS_NO != null).Count();
            int ADMINteacherCount = 0;
            ADMINteacherCount = db.HRMT25.Where(x => x.SCHOOL_NO == user.SCHOOL_NO && x.USER_NO == user.USER_NO && (x.ROLE_ID == "1" || x.ROLE_ID == "0" || x.ROLE_ID == "2" || x.ROLE_ID == "4")).Count();
            if (NteacherCount > 0 && ADMINteacherCount == 0)
            {

                List<SelectListItem> HRMT25TempSelect = new List<SelectListItem>();
                HRMT25TempSelect = new List<SelectListItem>()
                {
                    new SelectListItem() { Text = "學習表現", Value = "學習表現", Group = group1 },

                new SelectListItem() { Text = "校內競賽", Value = "校內競賽", Group = group1 },
                new SelectListItem() { Text = "品德表現", Value = "品德表現", Group = group1 },
                 new SelectListItem() { Text = "志工", Value = "志工", Group = group1 },
                new SelectListItem() { Text = "其他", Value = "其他", Group = group1 },
             new SelectListItem() { Text = "領導人", Value = "領導人", Group = group1 },

              new SelectListItem() { Text = "七個習慣代言人", Value = "七個習慣代言人", Group = group1}

                };
                HRMT25Select.AddRange(HRMT25TempSelect);

            }
            if (YteacheCount > 0 && ADMINteacherCount == 0)
            {
                List<SelectListItem> HRMT25TempSelect = new List<SelectListItem>();

                HRMT25TempSelect = new List<SelectListItem>()
                {
               
                new SelectListItem() { Text = "班級幫手和榮譽(每個月總點數有控管)", Value = "班級幫手和榮譽", Group = group2 },
                new SelectListItem() { Text = "班級特殊加扣點(每個月總點數有控管)", Value = "班級服務", Group = group2 }


                };
                HRMT25Select.AddRange(HRMT25TempSelect);
            }

            if (ADMINteacherCount > 0)
            {
                List<SelectListItem> HRMT25TempSelect = new List<SelectListItem>();
                HRMT25TempSelect = new List<SelectListItem>()
                {
                    new SelectListItem() { Text = "學習表現", Value = "學習表現", Group = group1 },

                new SelectListItem() { Text = "校內競賽", Value = "校內競賽", Group = group1 },
                new SelectListItem() { Text = "品德表現", Value = "品德表現", Group = group1 },
                 new SelectListItem() { Text = "志工", Value = "志工", Group = group1 },
                new SelectListItem() { Text = "其他", Value = "其他", Group = group1 },
             new SelectListItem() { Text = "領導人", Value = "領導人", Group = group1 },

              new SelectListItem() { Text = "七個習慣代言人", Value = "七個習慣代言人", Group = group1},
                new SelectListItem() { Text = "班級幹部(不受每個月控管的總點數)", Value = "班級幹部", Group = group2 },
                new SelectListItem() { Text = "班級幫手和榮譽(每個月總點數有控管)", Value = "班級幫手和榮譽", Group = group2 },
                new SelectListItem() { Text = "班級特殊加扣點(每個月總點數有控管)", Value = "班級服務", Group = group2 }


                };
                HRMT25Select.AddRange(HRMT25TempSelect);
            }
            SelectListItem listItem = new SelectListItem();
            listItem = new SelectListItem() { Text = "小獎勵(班級加點，受點數控管)", Value = "小獎勵(班級加點，受點數控管)", Group = group3 };
            HRMT25Select.Add(listItem);
            listItem = new SelectListItem() { Text = "小獎勵(學校加點，不受點數控管)", Value = "小獎勵(學校加點，不受點數控管)", Group = group3 };
            HRMT25Select.Add(listItem);
            ViewBag.QuickItems = HRMT25Select;
            return View(model);
        }
        /// <summary>
        /// 活動編輯 明細 一般模式
        /// </summary>
        /// <param name="Item"></param>
        /// <returns></returns>
        [CheckPermissionSeeion(CheckACTION_ID = "Edit")] //檢查權限
        public ActionResult _Details(GameEditDetailsViewModel Item)
        {
            return PartialView(Item);
        }

        /// <summary>
        /// 活動編輯 明細 兌換關卡 -有獎徵答模式
        /// </summary>
        /// <param name="Item"></param>
        /// <returns></returns>
        [CheckPermissionSeeion(CheckACTION_ID = "Edit")] //檢查權限
        public ActionResult _DetailsPrize(GameEditDetailsViewModel Item)
        {
            return PartialView(Item);
        }

        [CheckPermissionSeeion(CheckACTION_ID = "Edit")] //檢查權限
        public ActionResult _DetailsPrize2(GameEditDetailsViewModel Item)
        {
            return PartialView(Item);
        }
        [CheckPermissionSeeion(CheckACTION_ID = "Edit1")] //檢查權限
        public ActionResult _DetailsPrize3(GameEditDetailsViewModel Item)
        {
            return PartialView(Item);
        }
        /// <summary>
        /// 活動編輯 明細 兌換關卡 -有獎徵答模式
        /// </summary>
        /// <param name="Item"></param>
        /// <returns></returns>
        [CheckPermissionSeeion(CheckACTION_ID = "Edit")] //檢查權限
        public ActionResult _DetailsPrizeLottery(GameLotteryPrizeViewModel Item)
        {
            return PartialView(Item);
        }
        [CheckPermissionSeeion(CheckACTION_ID = "Edit1")] //檢查權限
        public ActionResult _DetailsPrizeLottery1(GameLotteryPrizeViewModel Item)
        {
            return PartialView(Item);
        }
        public ActionResult _AddPrizeExchangeItembtn()
        {
            return PartialView();
        }

        /// <summary>
        /// 活動編輯 明細 有獎徵答模式
        /// </summary>
        /// <param name="Item"></param>
        /// <returns></returns>
        [CheckPermissionSeeion(CheckACTION_ID = "Edit")] //檢查權限
        public ActionResult _DetailsQA(GameEditDetailsQAViewModel Item)
        {
            return PartialView(Item);
        }

        /// <summary>
        /// 活動編輯 明細 有獎徵答模式
        /// </summary>
        /// <param name="Item"></param>
        /// <returns></returns>
        [CheckPermissionSeeion(CheckACTION_ID = "Edit")] //檢查權限
        public ActionResult _AnsLeve(GameEditDetailsAnsLevelViewModel AnsLeve)
        {
            return PartialView(AnsLeve);
        }
        /// <summary>
        /// 活動編輯 Save 一般模式
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [CheckPermissionSeeion(CheckACTION_ID = "Edit1")] //檢查權限
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult EditSave1(GameEditViewModel model)
        {
              this.GetBreName();
            this.Shared(Bre_Name + " - 編輯設定");

            if (model == null) model = new GameEditViewModel();
            if (model.Search == null) model.Search = new GameSearchViewModel();

            string Message = string.Empty;

            this.CheckInput1(model);

            if (ModelState.IsValid == false)
            {
                Message = "警告!!輸入的內容有錯誤";
            }
            else
            {
                bool OK = this.gameService.SaveEditData1(model, user, ref db, ref Message);

                if (OK)
                {
                    ModelState.Clear();
                    model.Search.WhereGAME_NO = null;
                    TempData["StatusMessage"] = "儲存完成";
                    GameIndexViewModel viewModel = new GameIndexViewModel
                    {
                        Search = model.Search
                    };
                    this.GetBreName();
                    this.Shared();
                    return View("GameIndex1", viewModel);
                }
            }

            TempData["StatusMessage"] = Message;
#if DEBUG
            var errorsaa = ModelState
                        .Where(x => x.Value.Errors.Count > 0)
                        .Select(x => new { x.Key, x.Value.Errors })
                        .ToArray();
#endif

            return View("Edit1", model);
        }
        /// <summary>
        /// 活動編輯 Save 一般模式
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [CheckPermissionSeeion(CheckACTION_ID = "Edit")] //檢查權限
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult EditSave(GameEditViewModel model)
        {
            this.GetBreName();
            this.Shared(Bre_Name + " - 編輯設定");

            if (model == null) model = new GameEditViewModel();
            if (model.Search == null) model.Search = new GameSearchViewModel();

            string Message = string.Empty;

            this.CheckInput(model);

            if (ModelState.IsValid == false)
            {
                Message = "警告!!輸入的內容有錯誤";
            }
            else
            {
                bool OK = this.gameService.SaveEditData(model, user, ref db, ref Message);

                if (OK)
                {
                    ModelState.Clear();
                    model.Search.WhereGAME_NO = null;
                    TempData["StatusMessage"] = "儲存完成";
                    GameIndexViewModel viewModel = new GameIndexViewModel
                    {
                        Search = model.Search
                    };
                    this.GetBreName();
                    this.Shared();
                    return View("GameIndex", viewModel);
                }
            }

            TempData["StatusMessage"] = Message;

#if DEBUG
            var errorsaa = ModelState
                        .Where(x => x.Value.Errors.Count > 0)
                        .Select(x => new { x.Key, x.Value.Errors })
                        .ToArray();
#endif

            return View("Edit", model);
        }

        #endregion 一般模式

        public ActionResult AddSelect(GameAddSelectViewModel model)
        {
            this.GetBreName();
            this.Shared(Bre_Name + " - 編輯(選擇活動類行) ");

            if (this.GAME_TYPE == (byte)ADDT26.GameType.一般)
            {
                return RedirectToAction("Edit");
            }
            else if (this.GAME_TYPE == (byte)ADDT26.GameType.有獎徵答)
            {
                return RedirectToAction("EditQA");
            }
            else
            {
                return View("AddSelect", model);
            }
        }

        #region 有獎徵答模式

        public ActionResult EditQA(GameEditViewModel model)
        {
            this.GetBreName();
            this.Shared(Bre_Name + " - 編輯設定");

            if (model == null) model = new GameEditViewModel();
            if (model.Search == null) model.Search = new GameSearchViewModel();

            model.GAME_TYPE = (byte)ADDT26.GameType.有獎徵答;

            if (!string.IsNullOrWhiteSpace(model.Search.WhereGAME_NO))
            {
                model = this.gameService.GetEditData(model, ref db);
            }

            return View("Edit", model);
        }

        #endregion 有獎徵答模式

        #region 備援管理

        /// <summary>
        /// 備援
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [CheckPermissionSeeion(CheckACTION_ID = "Edit")] //檢查權限
        public ActionResult BackupLink(BackupLinkViewModel model)
        {
            this.GetBreName();
            this.Shared(Bre_Name + " - 編輯 ");
            if (model == null) model = new BackupLinkViewModel();
            if (model.Search == null) model.Search = new GameSearchViewModel();

            model = this.gameService.BackupLinkData(model, ref db);
            return View(model);
        }

        [CheckPermissionSeeion(CheckACTION_ID = "Edit")] //檢查權限
        public ActionResult _BackupInfo(BackupLinkInfoViewModel Item)
        {
            return PartialView(Item);
        }

        [CheckPermissionSeeion(CheckACTION_ID = "Index")] //檢查權限
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult BackupLinkSave(BackupLinkViewModel model)
        {
            this.GetBreName();
            this.Shared(Bre_Name + " - 編輯 ");
            if (model == null) model = new BackupLinkViewModel();
            if (model.Search == null) model.Search = new GameSearchViewModel();

            string Message = string.Empty;

            if (ModelState.IsValid == false)
            {
                Message = "警告!!輸入的內容有錯誤";
            }
            else
            {
                bool OK = this.gameService.SaveBackupLinkData(model, user, ref db, ref Message);

                if (OK)
                {
                    TempData["StatusMessage"] = "儲存完成";
                }
                else
                {
                    TempData["StatusMessage"] = Message;
                }
            }

#if DEBUG
            var errorsaa = ModelState
                        .Where(x => x.Value.Errors.Count > 0)
                        .Select(x => new { x.Key, x.Value.Errors })
                        .ToArray();
#endif

            return View("BackupLink", model);
        }

        #endregion 備援管理

        [CheckPermissionSeeion(CheckACTION_ID = "Edit")]
        public ActionResult Reward(GameRewardViewModel model)
        {
            this.GetBreName();
            this.Shared(string.Concat(GameController.Bre_Name, " - 編輯 "));
            if (model == null)
            {
                model = new GameRewardViewModel();
            }
            if (model.Search == null)
            {
                model.Search = new GameSearchViewModel();
            }
            model = this.gameService.GetRewardData(model, ref this.db);
            return base.View(model);
        }

        [CheckPermissionSeeion(CheckACTION_ID = "Edit")]
        public ActionResult _RewardInfo(GameRewardInfoViewModel Item)
        {
            return base.PartialView(Item);
        }

        public ActionResult RewardSave(GameRewardViewModel model)
        {
            this.GetBreName();
            this.Shared(string.Concat(GameController.Bre_Name, " - 編輯 "));
            if (model == null)
            {
                model = new GameRewardViewModel();
            }
            if (model.Search == null)
            {
                model.Search = new GameSearchViewModel();
            }
            string Message = string.Empty;
            if (!base.ModelState.IsValid)
            {
                Message = "警告!!輸入的內容有錯誤";
                base.TempData["StatusMessage"] = Message;
            }
            else if (!this.gameService.SaveRewardData(model, this.user, ref this.db, ref Message))
            {
                base.TempData["StatusMessage"] = string.Concat("警告!!輸入的內容有錯誤", Environment.NewLine, Message);
            }
            else
            {
                base.TempData["StatusMessage"] = "儲存完成";
            }
            return base.View("Reward", model);
        }

        /// <summary>
        /// 匯出快速入口
        /// </summary>
        /// <param name="GAME_NO"></param>
        /// <returns></returns>
        [CheckPermissionSeeion(CheckACTION_ID = "Edit")] //檢查權限
        public ActionResult ExportFastLink(string GAME_NO)
        {
            string Message = string.Empty;
            string FileNamePath = this.gameService.ExcelFastLinkData(GAME_NO, ref db, ref Message);

            if (!string.IsNullOrWhiteSpace(Message))
            {
                return RedirectToAction(ErrorHelper.ErrorVal.NotFileError, "Error", new { error = Message });
            }

            if (System.IO.File.Exists(FileNamePath))
            {
                return File(System.IO.File.ReadAllBytes(FileNamePath), "application/vnd.ms-excel", $"{Path.GetFileNameWithoutExtension(FileNamePath)}.xlsx");//輸出檔案給Client端
            }
            else
            {
                return RedirectToAction(ErrorHelper.ErrorVal.NotFileError, "Error");
            }
        }

        [CheckPermissionSeeion(CheckACTION_ID = "Edit")] //檢查權限
        public ActionResult QueryTeamSetView(string GAME_NO)
        {
            this.GetBreName();
            this.Shared(Bre_Name + " - 小組設定");

            string Message = string.Empty;
            var Game = gameService.GetIsGAME(GAME_NO, ref db, ref Message, null, false);

            if (!string.IsNullOrWhiteSpace(Message))
            {
                TempData["StatusMessage"] = Message;
            }

            return View(Game);
        }

        /// <summary>
        /// 比賽開始
        /// </summary>
        /// <param name="GAME_NO"></param>
        /// <returns></returns>
        public ActionResult TeamGameStart(string GAME_NO)
        {
            string Success = "true";
            string Message = string.Empty;

            try
            {
                Success = this.gameService.SaveTeamGameStart(GAME_NO, ref db, ref Message).ToString();
            }
            catch (Exception ex)
            {
                Success = "false";
                Message = ex.Message;
            }

            var data = "{\"Success\" : \"" + Success + "\"  , \"Error\" : \"" + Message + "\" }";

            return Json(@data, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// 比賽結束
        /// </summary>
        /// <param name="GAME_NO"></param>
        /// <returns></returns>
        public ActionResult TeamGameEnd(string GAME_NO)
        {
            string Success = "true";
            string Message = string.Empty;

            try
            {
                Success = this.gameService.SaveTeamGameEnd(GAME_NO, ref db, ref Message).ToString();
            }
            catch (Exception ex)
            {
                Success = "false";
                Message = ex.Message;
            }

            var data = "{\"Success\" : \"" + Success + "\"  , \"Error\" : \"" + Message + "\" }";

            return Json(@data, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// 刪除
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [CheckPermissionSeeion(CheckACTION_ID = "Edit")] //檢查權限
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult EditDel(GameEditViewModel model)
        {
            this.GetBreName();
            this.Shared(Bre_Name + " - 編輯 ");

            if (model == null) model = new GameEditViewModel();
            if (model.Search == null) model.Search = new GameSearchViewModel();

            if (string.IsNullOrEmpty(model.Search?.WhereGAME_NO))
            {
                return RedirectToAction("GameIndex");
            }

            string Message = string.Empty;

            if (ModelState.IsValid == false)
            {
                Message = "警告!!輸入的內容有錯誤";
            }
            else
            {
                bool OK = this.gameService.SaveEditDeleteData(model, user, ref db, ref Message);

                if (OK)
                {
                    ModelState.Clear();
                    model.Search.WhereGAME_NO = null;
                    TempData["StatusMessage"] = "刪除完成";
                    GameIndexViewModel viewModel = new GameIndexViewModel
                    {
                        Search = model.Search
                    };
                    this.GetBreName();
                    this.Shared();
                    return View("GameIndex", viewModel);
                }
            }

            TempData["StatusMessage"] = Message;

#if DEBUG
            var errorsaa = ModelState
                        .Where(x => x.Value.Errors.Count > 0)
                        .Select(x => new { x.Key, x.Value.Errors })
                        .ToArray();
#endif

            return View("Edit", model);
        }

        #endregion 活動編輯

        #region 前台

        /// <summary>
        /// 闖關地圖
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ActionResult PassMode(GamePassModexViewModel model)
        {
            if (model == null) model = new GamePassModexViewModel();

            UserProfile LoginUser = UserProfileHelper.Get();

            string Message = string.Empty;

            if (LoginUser != null)
            {
                if (PermissionService.GetPermission_Use_YN(UserProfileHelper.GetBRE_NO(), "Index", LoginUser.SCHOOL_NO, LoginUser.USER_NO) == SharedGlobal.Y)
                {
                    UserProfileHelper.SetGameNoCookieData(model?.GAME_NO, false);
                }

                LogHelper.AddLogToDB(LoginUser.SCHOOL_NO, null, System.Web.HttpContext.Current.Request.UserHostAddress, "HOME", "LoginOUT");
                this.Request.RequestContext.HttpContext.Session[CONSTANT.SESSION_USER_KEY] = null;
            }

            var Game = gameService.GetIsGAME(model?.GAME_NO, ref db, ref Message, UserProfileHelper.GetGameNoCookie(), true);
            if (!string.IsNullOrWhiteSpace(Message))
            {
                TempData["GameName"] = Game?.GAME_NAME;
                return RedirectToAction("ErrGame", new GameErrorViewModel { Error = Message });
            }
            model.GAME_NO = Game.GAME_NO;
           
            model = gameService.GetPassModeData(model, ref db);

            return View(model);
        }

        /// <summary>
        /// 闖關地圖
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ActionResult PassMode1(GamePassModexViewModel model)
        {
            if (model == null) model = new GamePassModexViewModel();

            UserProfile LoginUser = UserProfileHelper.Get();
            if (model.Main != null && model.Main.GAME_NO != "")
            {
                model.GAME_NO = model.Main.GAME_NO;

            }
        
            string Message = string.Empty;

            if (LoginUser != null)
            {
                if (PermissionService.GetPermission_Use_YN(UserProfileHelper.GetBRE_NO(), "GameIndex1", LoginUser.SCHOOL_NO, LoginUser.USER_NO) == SharedGlobal.Y)
                {
                    UserProfileHelper.SetGameNoCookieData(model?.GAME_NO, false);
                }


            }

            var Game = gameService.GetIsGAME(model?.GAME_NO, ref db, ref Message, UserProfileHelper.GetGameNoCookie(), true);
            if (!string.IsNullOrWhiteSpace(Message))
            {
                TempData["GameName"] = Game?.GAME_NAME;
                return RedirectToAction("ErrGame", new GameErrorViewModel { Error = Message });
            }
            model.GAME_NO = Game.GAME_NO;
            model = gameService.GetPassModeData(model, ref db);

            return View(model);
        }

        /// <summary>
        /// 闖關地圖
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ActionResult PassModeForUSER(GamePassModexViewModel model)
        {
            if (model == null) model = new GamePassModexViewModel();

            UserProfile LoginUser = UserProfileHelper.Get();
            if (model.Main != null && model.Main.GAME_NO != "")
            {
                model.GAME_NO = model.Main.GAME_NO;

            }

            string Message = string.Empty;

            if (LoginUser != null)
            {
                if (PermissionService.GetPermission_Use_YN(UserProfileHelper.GetBRE_NO(), "GameIndex1", LoginUser.SCHOOL_NO, LoginUser.USER_NO) == SharedGlobal.Y)
                {
                    UserProfileHelper.SetGameNoCookieData(model?.GAME_NO, false);
                }


            }

            var Game = gameService.GetIsGAME(model?.GAME_NO, ref db, ref Message, UserProfileHelper.GetGameNoCookie(), true);
            if (!string.IsNullOrWhiteSpace(Message))
            {
                TempData["GameName"] = Game?.GAME_NAME;
                return RedirectToAction("ErrGame", new GameErrorViewModel { Error = Message });
            }
            model.GAME_NO = Game.GAME_NO;
            model = gameService.GetPassModeData(model, ref db);

            return View(model);
        }
        /// <summary>
        /// 警告畫面
        /// </summary>
        /// <param name="Error"></param>
        /// <returns></returns>
        public ActionResult ErrGame(string Error)
        {
            return View("ErrGame", new GameErrorViewModel { Error = Error });
        }

        /// <summary>
        /// 報名畫面
        /// </summary>
        /// <returns></returns>
        public ActionResult ApplyView(GameLeveViewModel model)
        {
            string Message = string.Empty;

            var Game = gameService.GetIsGAME(model.GAME_NO, ref db, ref Message, UserProfileHelper.GetGameNoCookie(), true);
            if (!string.IsNullOrWhiteSpace(Message))
            {
                TempData["GameName"] = Game?.GAME_NAME;
                return RedirectToAction("ErrGame", new GameErrorViewModel { Error = Message });
            }
            model.GAME_NO = Game.GAME_NO;

            ModelState.Clear();
            model = gameService.GetLeveData(model, ref db);

            return View(model);
        }

        /// <summary>
        /// 報名資料處理
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult ApplySave(GameLeveViewModel model)
        {
            string Message = string.Empty;
            string LOG_ID = string.Empty;
            ADDT27 T27 = null;
            if (ModelState.IsValid == false)
            {
                Message = "警告!!輸入的內容有錯誤";
            }
            else
            {
                bool OK = this.gameService.SaveApplyData(model, ref db, ref LOG_ID, ref Message);

                ModelState.Clear();

                GamePassedViewViewModel viewModel = this.gameService.GetIsPassed(OK, model.GAME_NO, null, ADDT26_D.LevelType.Apply, Message, "ApplyView", LOG_ID, ref db);

                T27 = db.ADDT27.Where(a => a.GAME_USER_ID == model.GameUserID && a.GAME_NO == model.GAME_NO && a.STATUS == (sbyte)ADDT27.StatusVal.使用中).NoLock(x => x.FirstOrDefault());
                if (T27 != null)
                {
                    viewModel.CASH_AVAILABLE = (int)T27.CASH_AVAILABLE;
                    model.CASH_AVAILABLE = (int)T27.CASH_AVAILABLE;
                    viewModel.User = T27;
                    model.User = T27;
                }

                return View("PassedView", viewModel);
            }

            return View("ApplyView", model);
        }

        /// <summary>
        /// 結果畫面
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ActionResult PassedView(GamePassedViewViewModel model)
        {
            return View(model);
        }

        /// <summary>
        /// 點數查詢 刷卡機版  (前台)
        /// </summary>
        /// <returns></returns>
        public ActionResult CashQuery(GameCashQueryViewModel model)
        {
            string Message = string.Empty;

            var Game = gameService.GetIsGAME(model?.GAME_NO, ref db, ref Message, UserProfileHelper.GetGameNoCookie(), true);
            if (!string.IsNullOrWhiteSpace(Message))
            {
                TempData["GameName"] = Game?.GAME_NAME;
                return RedirectToAction("ErrGame", new GameErrorViewModel { Error = Message });
            }
            model.GAME_NO = Game.GAME_NO;

            if (!string.IsNullOrWhiteSpace(Game.GAME_IMG))
            {
                model.LEVEL_IMG_PATH = gameService.GetDirectorySysGamePath(Game.SCHOOL_NO, Game.GAME_NO, Game.GAME_IMG);
            }
            else
            {
                model.LEVEL_IMG_PATH = UrlCustomHelper.Url_Content(GameService.GetSetDirectoryGamePath() + "Game_df.jpg");
            }

            ModelState.Clear();
            return View(model);
        }

        /// <summary>
        /// 點數查詢結果  刷卡機版 (前台)
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult CashQueryDataList(GameCashQueryViewModel model)
        {
            string Message = string.Empty;

            model = this.gameService.CashQueryData(model, ref db, ref Message, true);

            TempData["StatusMessage"] = Message;

            return View(model);
        }

        /// <summary>
        /// 查詢中獎清單 手機/網頁版
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public ActionResult QueryUserDataList(GameLotteryListIndexViewModel model)
        {
            string Message = string.Empty;

            var Game = gameService.GetIsGAME(model?.GAME_NO, ref db, ref Message);
            if (!string.IsNullOrWhiteSpace(Message))
            {
                TempData["GameName"] = Game?.GAME_NAME;
                return RedirectToAction("ErrGame", new GameErrorViewModel { Error = Message });
            }

            model.GAME_NO = Game.GAME_NO;
            model.RECEIVE_DATE = Game.RECEIVE_DATE;
            model.GameInfo = Game;

            return View(model);
        }

        /// <summary>
        /// 查詢中獎清單 手機/網頁版 PartialView
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [AllowAnonymous]
        public ActionResult _PageQueryUserDataList(GameLotteryListIndexViewModel model)
        {
            string Message = string.Empty;
            if (model == null) model = new GameLotteryListIndexViewModel();
            if (model.Search == null) model.Search = new GameSearchViewModel();
            if (model.CashSearch == null) model.CashSearch = new BatchCashIntoSearchViewModel();

            var Game = gameService.GetIsGAME(model?.GAME_NO, ref db, ref Message);
            if (!string.IsNullOrWhiteSpace(Message))
            {
                TempData["GameName"] = Game?.GAME_NAME;
                return PartialView("ErrGame", new GameErrorViewModel { Error = Message });
            }

            try
            {
                model.GAME_NO = Game.GAME_NO;
                ModelState.Clear();

                if (string.IsNullOrWhiteSpace(model.Search.WhereGAME_NO))
                {
                    model.Search.WhereGAME_NO = model.GAME_NO;
                }

                if (string.IsNullOrWhiteSpace(model.CashSearch.WhereSCHOOL_NO))
                {
                    //model.CashSearch.WhereSCHOOL_NO = Game.SCHOOL_NO;
                }

                var SchoolNoSelectItem = BDMT01Service.GetSCHOOL_NO_ALL_Not_P(model.CashSearch.WhereSCHOOL_NO, null);
                ViewBag.SchoolNoSelectItem = SchoolNoSelectItem;

                model = this.gameService.GetLotteryDetailsListData(model, ref db);

                model.IsPostBack = SharedGlobal.Y;
            }
            catch (Exception ex)
            {
                return PartialView("ErrGame", new GameErrorViewModel { Error = ex.Message });
            }

            return PartialView(model);
        }

        /// <summary>
        /// 活動記錄 to Mail
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ActionResult GameMailData(GameCashQueryViewModel model)
        {
            string Success = "false";

            string Message = string.Empty;

            var Game = gameService.GetIsGAME(model?.GAME_NO, ref db, ref Message, UserProfileHelper.GetGameNoCookie(), true);
            model.GAME_NO = Game.GAME_NO;

            TempData["GameName"] = Game?.GAME_NAME;

            if (!string.IsNullOrWhiteSpace(Message))
            {
                var data = "{\"Success\" : \"" + Success + "\" , \"Error\" : \"" + Message + "\" }";

                return Json(@data, JsonRequestBehavior.AllowGet);
            }

            model = this.gameService.CashQueryData(model, ref db, ref Message, false);

            if (!string.IsNullOrWhiteSpace(Message))
            {
                var data = "{\"Success\" : \"" + Success + "\" , \"Error\" : \"" + Message + "\" }";

                return Json(@data, JsonRequestBehavior.AllowGet);
            }

            try
            {
                string SUBJECT = Game?.GAME_NAME + $"-{model.User.NAME}活動記錄";

                StringBuilder MailBodyHtml = new StringBuilder();
                string urlAddress = UrlCustomHelper.GetOwnWebUri() + $@"/EcoolWeb/{Bre_NO}/ShowMailCashQuery?GAME_NO={model.GAME_NO}&GameUserID={model.GameUserID}";

                MailBodyHtml.Append($"{model.User.NAME}的活動記錄，請點選以下連結來查看");
                MailBodyHtml.Append("<br/>");
                MailBodyHtml.AppendFormat(" <a href = '{0}' target = '_blank'>{0}</a>", urlAddress);
                MailBodyHtml.Append("<br/><br/><br/>");
                MailBodyHtml.Append("如果您有任何疑問或建議事項，歡迎您隨時 上站與我們聯繫。<br/>");
                MailBodyHtml.Append("<br/><br/><br/>");

                string htmlCode = GetWebContent(@urlAddress, ref Message);
                if (!string.IsNullOrWhiteSpace(Message))
                {
                    var dataA = "{\"Success\" : \"" + Success + "\" , \"Error\" : \"" + Message + "\" }";

                    return Json(@dataA, JsonRequestBehavior.AllowGet);
                }

                MailBodyHtml.Append(htmlCode);

                List<string> Mail = new List<string>();
                Mail.Add(model.E_MAIL);

                MailHelper MailHelper = new MailHelper();
                MailHelper.SendMailByGmail(Mail, SUBJECT, MailBodyHtml.ToString());

                Success = "true";

                var data = "{\"Success\" : \"" + Success + "\" , \"Error\" : \"" + Message + "\" }";

                return Json(@data, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                var data = "{\"Success\" : \"" + Success + "\" , \"Error\" : \"" + ex.Message + "\" }";

                return Json(@data, JsonRequestBehavior.AllowGet);
            }
        }

        /// <summary>
        /// 使用者 mail 裡的連結內容
        /// </summary>
        /// <param name="GAME_NO"></param>
        /// <param name="GameUserID"></param>
        /// <returns></returns>
        [HttpGet]
        public ActionResult ShowMailCashQuery(string GAME_NO, string GameUserID, string TEMP_USER_ID)
        {
            string Message = string.Empty;

            GameCashQueryViewModel model = new GameCashQueryViewModel();
            model.GAME_NO = GAME_NO;
            model.GameUserID = GameUserID;
            model.TEMP_USER_ID = TEMP_USER_ID;

            var Game = gameService.GetIsGAME(model?.GAME_NO, ref db, ref Message);

            TempData["GameName"] = Game?.GAME_NAME;

            if (!string.IsNullOrWhiteSpace(Message))
            {
                return RedirectToAction("ErrGame", new GameErrorViewModel { Error = Message });
            }

            model.GAME_NO = Game.GAME_NO;

            model = this.gameService.CashQueryData(model, ref db, ref Message, false);

            TempData["StatusMessage"] = Message;

            return PartialView(model);
        }

        /// <summary>
        /// 報名QRCode
        /// 產生 要使用者產生 自已QRCode 窗口 的QRCode 及 網址 , 掃這個 QRCode 可導到  GuestCreQRCode 頁面 ，使用者輸入個人資訊 ，產生自已的臨時卡 QRCode
        /// </summary>
        /// <returns></returns>
        public ActionResult CreQRCode(GameQRCodeToGuestViewModel model)
        {
            string Message = string.Empty;

            var Game = gameService.GetIsGAME(model?.GAME_NO, ref db, ref Message, UserProfileHelper.GetGameNoCookie(), true);
            model.GAME_NO = Game.GAME_NO;
            TempData["GameName"] = Game?.GAME_NAME;

            if (!string.IsNullOrWhiteSpace(Message))
            {
                return RedirectToAction("ErrGame", new GameErrorViewModel { Error = Message });
            }

            return View(model);
        }
        public ActionResult CreQRCodeForExchage(GameLeveViewModel model)
        {
            return View(model);
        }

        public ActionResult CreQRCodeGameForExchage(GameLeveViewModel model)
        {
            return View(model);
        }
        /// <summary>
        /// 報名QRCode
        /// 訪客 產生自已的 QRCode畫面 (產生自已的臨時卡 QRCode)
        /// </summary>
        /// <returns></returns>
        public ActionResult GuestCreQRCode(GameQRCodeToGuestViewModel model)
        {
            string Message = string.Empty;

            var Game = gameService.GetIsGAME(model?.GAME_NO, ref db, ref Message, null, false);
            model.GAME_NO = Game.GAME_NO;
            TempData["GameName"] = Game?.GAME_NAME;

            if (!string.IsNullOrWhiteSpace(Message))
            {
                return RedirectToAction("ErrGame", new GameErrorViewModel { Error = Message });
            }

            return View(model);
        }

        /// <summary>
        /// 產生 查詢機 入口 QRCode
        /// </summary>
        /// <returns></returns>
        public ActionResult QueryUniteCreQRCode()
        {
            return View();
        }

        /// <summary>
        /// 掃 QueryUniteCreQRCode 產生的 QRCode 進入此頁，自已轉向目前活動的網頁版中獎清單頁面 QueryUserDataList
        /// </summary>
        /// <returns></returns>
        public ActionResult ToQuery(string GAME_NO)
        {
            if (string.IsNullOrEmpty(GAME_NO))
            {
                var aADDT26 = gameService.GetNowGameNo();

                if (aADDT26 != null)
                {
                    return RedirectToAction("QueryUserDataList", new { aADDT26.GAME_NO });
                }
            }
            else
            {
                return RedirectToAction("QueryUserDataList", new { GAME_NO });
            }

            return View("ErrGame", new GameErrorViewModel { Error = "目前無任何活動進行中" });
        }

        /// <summary>
        /// 快速連結
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ActionResult FastLevel(GameFastLevelViewModel model)
        {
            if (model == null) model = new GameFastLevelViewModel();

            string Message = string.Empty;

            if (string.IsNullOrWhiteSpace(model.ActionName))
            {
                TempData["GameName"] = "錯誤";
                Message = "參數未完整";
                return RedirectToAction("ErrGame", new GameErrorViewModel { Error = Message });
            }

            var Game = gameService.GetIsGAME(model.GAME_NO, ref db, ref Message, UserProfileHelper.GetGameNoCookie(), false);

            if (!string.IsNullOrWhiteSpace(Message))
            {
                TempData["GameName"] = Game?.GAME_NAME;
                return RedirectToAction("ErrGame", new GameErrorViewModel { Error = Message });
            }

            UserProfileHelper.Set(model?.SCHOOL_NO);
            UserProfileHelper.SetGameNoCookieData(model?.GAME_NO, false);
            UserProfileHelper.UserSessionDispose();

            return View(model);
        }

        /// <summary>
        /// 快速連結
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ActionResult FastLevel1(GameFastLevelViewModel model)
        {
            if (model == null) model = new GameFastLevelViewModel();

            string Message = string.Empty;

            if (string.IsNullOrWhiteSpace(model.ActionName))
            {
                TempData["GameName"] = "錯誤";
                Message = "參數未完整";
                return RedirectToAction("ErrGame", new GameErrorViewModel { Error = Message });
            }

            var Game = gameService.GetIsGAME(model.GAME_NO, ref db, ref Message, UserProfileHelper.GetGameNoCookie(), false);

            if (!string.IsNullOrWhiteSpace(Message))
            {
                TempData["GameName"] = Game?.GAME_NAME;
                return RedirectToAction("ErrGame", new GameErrorViewModel { Error = Message });
            }

            UserProfileHelper.Set(model?.SCHOOL_NO);
            UserProfileHelper.SetGameNoCookieData(model?.GAME_NO, false);
            UserProfileHelper.UserSessionDispose();

            return View(model);
        }
        /// <summary>
        /// 小組闖關 狀態 QRCode
        /// </summary>
        /// <returns></returns>
        public ActionResult QueryTeamQRCode(string GAME_NO)
        {
            string Message = string.Empty;

            var Game = gameService.GetIsGAME(GAME_NO, ref db, ref Message, null, false);
            TempData["GameName"] = Game?.GAME_NAME;

            if (!string.IsNullOrWhiteSpace(Message))
            {
                return RedirectToAction("ErrGame", new GameErrorViewModel { Error = Message });
            }

            return View();
        }

        /// <summary>
        /// 小組闖關 狀態 QRCode
        /// </summary>
        /// <returns></returns>
        public ActionResult QueryTeamStatus(string GAME_NO, bool IsPartialView = false)
        {
            string Message = string.Empty;

            var Game = gameService.GetIsGAME(GAME_NO, ref db, ref Message, null, false);
            TempData["GameName"] = Game?.GAME_NAME;

            if (!string.IsNullOrWhiteSpace(Message))
            {
                TempData["StatusMessage"] = Message;
            }

            var model = this.gameService.GetQueryTeamStatus(GAME_NO, ref db);

            if (model.GameInfo.TEAM_GAME_DATES == null)
            {
                TempData["StatusMessage"] = "小組闖關活動未開始";
            }

            if (IsPartialView)
            {
                return PartialView(model);
            }
            else
            {
                return View(model);
            }
        }

        /// <summary>
        /// 關卡畫面
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        public ActionResult LevelView(GameLeveViewModel model)
        {
            string Message = string.Empty;
            ADDT26 Game = new ADDT26();
            if (model.IsQRCODE)
            {
                Game = gameService.GetIsGAME(model?.GAME_NO, ref db, ref Message,null, false);
            }
            else {
                Game = gameService.GetIsGAME(model?.GAME_NO, ref db, ref Message, UserProfileHelper.GetGameNoCookie(), true);
            }

            if (!string.IsNullOrWhiteSpace(Message))
            {
                TempData["GameName"] = Game?.GAME_NAME;
                return RedirectToAction("ErrGame", new GameErrorViewModel { Error = Message });
            }
            model.GAME_NO = Game.GAME_NO;
            ModelState.Clear();

            if (string.IsNullOrWhiteSpace(model.LEVEL_NO))
            {
                model.LEVEL_NO = UserProfileHelper.GetLevelNoCookie();
            }
            else
            {
                UserProfileHelper.SetLevelNoCookieData(model.LEVEL_NO);
            }

            model = gameService.GetLeveData(model, ref db);

            ViewBag.BackupLinkItem = gameService.GetBackupLinkItem(model.GAME_NO, ref db);

            return View(model);
        }
        [AllowAnonymous]
        [AllowCrossSite(Origin = "*")]
        public JsonResult DelADDT27(GameLeveViewModel model)
        {
            ADDT27 dDT27 = new ADDT27();
            dDT27 = db.ADDT27.Where(x => x.TEMP_USER_ID == model.User.TEMP_USER_ID).FirstOrDefault();
            db.ADDT27.Remove(dDT27);
            db.SaveChanges();
            return Json("OK");
        }
        public ActionResult CheckISGameOK() {
            return View();
        }
        /// <summary>
        /// 關卡畫面
        /// </summary>
        /// <returns></returns>
        [CheckPermissionSeeion(CheckACTION_ID = "GameIndex1")] //檢查權限
        public ActionResult LevelGameView1(GameLeveViewModel model)
        {
            string Message = string.Empty;
            this.Shared();
            UserProfile LoginUser = UserProfileHelper.Get();
            var Game = gameService.GetIsGAME(model?.GAME_NO, ref db, ref Message, model?.GAME_NO, true);
            if (!string.IsNullOrWhiteSpace(Message))
            {
                TempData["GameName"] = Game?.GAME_NAME;
                return RedirectToAction("ErrGame", new GameErrorViewModel { Error = Message });
            }
    
            ModelState.Clear();

            if (string.IsNullOrWhiteSpace(model.LEVEL_NO))
            {
                model.LEVEL_NO = UserProfileHelper.GetLevelNoCookie();
            }
            else
            {
                UserProfileHelper.SetLevelNoCookieData(model.LEVEL_NO);
            }

            model = gameService.GetLeveData(model, ref db);

            ViewBag.BackupLinkItem = gameService.GetBackupLinkItem(model.GAME_NO, ref db);
           
            return View(model);
        }

        /// <summary>
        /// 關卡畫面
        /// </summary>
        /// <returns></returns>
        [CheckPermissionSeeion(CheckACTION_ID = "GameIndex1")] //檢查權限
        public ActionResult LevelGameView2(GameLeveViewModel model)
        {
            string Message = string.Empty;
            this.Shared();
            UserProfile LoginUser = UserProfileHelper.Get();
            var Game = gameService.GetIsGAME(model?.GAME_NO, ref db, ref Message, model?.GAME_NO, true);
            if (!string.IsNullOrWhiteSpace(Message))
            {
                TempData["GameName"] = Game?.GAME_NAME;
                return RedirectToAction("ErrGame", new GameErrorViewModel { Error = Message });
            }

            ModelState.Clear();

            if (string.IsNullOrWhiteSpace(model.LEVEL_NO))
            {
                model.LEVEL_NO = UserProfileHelper.GetLevelNoCookie();
            }
            else
            {
                UserProfileHelper.SetLevelNoCookieData(model.LEVEL_NO);
            }

            model = gameService.GetLeveData(model, ref db);

            ViewBag.BackupLinkItem = gameService.GetBackupLinkItem(model.GAME_NO, ref db);

            return View(model);
        }
        /// <summary>
        /// 關卡畫面
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        public ActionResult LevelGameView(GameLeveViewModel model)
        {
            string Message = string.Empty;

            var Game = gameService.GetIsGAME(model?.GAME_NO, ref db, ref Message, UserProfileHelper.GetGameNoCookie(), true);
            if (!string.IsNullOrWhiteSpace(Message))
            {
                TempData["GameName"] = Game?.GAME_NAME;
                return RedirectToAction("ErrGame", new GameErrorViewModel { Error = Message });
            }
            model.GAME_NO = Game.GAME_NO;
            ModelState.Clear();

            if (string.IsNullOrWhiteSpace(model.LEVEL_NO))
            {
                model.LEVEL_NO = UserProfileHelper.GetLevelNoCookie();
            }
            else
            {
                UserProfileHelper.SetLevelNoCookieData(model.LEVEL_NO);
            }

            model = gameService.GetLeveData(model, ref db);

            ViewBag.BackupLinkItem = gameService.GetBackupLinkItem(model.GAME_NO, ref db);

            return View(model);
        }
        public bool CheckIsWinner(GameLeveViewModel model) {
            Boolean ISCanDelte = true;
            UserProfile user = UserProfileHelper.Get();
            string sSQL = @"   SELECT Case when b.PrizeName is null then a.PrizeName else b.PrizeName end as PrizeName,Case when b.Y_CASH is null then a.Y_CASH  else b.Y_CASH end as Y_CASH,b.CreaUserID,d.NAME,k.CLASS_NO,k.SEAT_NO,k.NAME,b.UpdateDate
                                 FROM LotteryWinner b left join LotteryPrize a on b.PrizeId=a.PrizeId
								 inner join ADDT27 d on b.CreaUserID=d.TEMP_USER_ID
								 inner join HRMT01 k on d.GAME_USER_ID=k.CARD_NO
                                where a.GAME_NO=@GAME_NO and k.SCHOOL_NO=@SCHOOL_NO  order by UpdateDate desc";
            var temp = db.Database.Connection.Query<GetWinnerItemViewModel>(sSQL, new
            {
                GAME_NO = model.GAME_NO,
                SCHOOL_NO = user.SCHOOL_NO
            }).ToList();
            model.WinnerItemDetails = new List<GetWinnerItemViewModel>();
            if (temp.Count() > 0)
            {
                ISCanDelte = false;
                model.WinnerItemDetails = (List<GetWinnerItemViewModel>)temp;

            }
            return ISCanDelte;
        }



        [CheckPermissionSeeion(CheckACTION_ID = "GameIndex1")]
        public ActionResult GetWinnerItemDetail(GameLeveViewModel model)

        {
            this.Shared();
            UserProfile user = UserProfileHelper.Get();
            List<GetWinnerItemViewModel> GetWinnerItems = new List<GetWinnerItemViewModel>();
            string sSQL = @"   SELECT Case when b.PrizeName is null then a.PrizeName else b.PrizeName end as PrizeName,Case when b.Y_CASH is null then a.Y_CASH  else b.Y_CASH end as Y_CASH,b.CreaUserID,d.NAME,k.CLASS_NO,k.SEAT_NO,k.NAME,b.UpdateDate
                                 FROM LotteryWinner b left join LotteryPrize a on b.PrizeId=a.PrizeId
								 inner join ADDT27 d on b.CreaUserID=d.TEMP_USER_ID
								 inner join HRMT01 k on d.GAME_USER_ID=k.CARD_NO
                                where a.GAME_NO=@GAME_NO and k.SCHOOL_NO=@SCHOOL_NO  order by UpdateDate ";
            var temp = db.Database.Connection.Query<GetWinnerItemViewModel>(sSQL, new
            {
                GAME_NO = model.GAME_NO,
                SCHOOL_NO = SCHOOL_NO
            }).ToList();
            model.WinnerItemDetails = new List<GetWinnerItemViewModel>();

            if (temp.Count() > 0) {

                model.WinnerItemDetails = (List<GetWinnerItemViewModel>)temp;
            }
          
            return PartialView(model);
        }
        [CheckPermissionSeeion(CheckACTION_ID = "GameIndex1")]
        public ActionResult GetWinnerItem(GameLeveViewModel model)

        {
            this.Shared();
            return View(model);



        }
        [AllowAnonymous]
        [AllowCrossSite(Origin = "*")]
        public JsonResult GetPricName(GameLeveViewModel model)
        {
            this.Shared();
            UserProfile user = UserProfileHelper.Get();
            GameLeveViewModel viewModel = this.gameService.GetLotteryPrize(model,user.SCHOOL_NO, ref db);

            return Json(viewModel);
        }
        public JsonResult FindPersonFromStudentNumberBase(ADDT27 UserInfo)
        {
            GamePassedViewViewModel viewModel = new GamePassedViewViewModel();
            string Message = "";
            bool IsOK = false;
            int HRMT01count = 0;
            HRMT01count = db.HRMT01.Where(x => x.CARD_NO == UserInfo.USER_NO && x.SCHOOL_NO == UserInfo.SCHOOL_NO && x.USER_STATUS == 1).Count();
            if (UserInfo.USER_NO != null)
            {

                if (HRMT01count > 0)
                {

                    IsOK = true;

                }
                else
                {


                    HRMT01count =
                                    db.HRMT01.Where(x => x.USER_NO == UserInfo.USER_NO && x.SCHOOL_NO == UserInfo.SCHOOL_NO && x.USER_STATUS == 1).Count();


                    if (HRMT01count > 0)
                    {

                        IsOK = true;

                    }
                    else {
                        List<string> CLASSNOLIST = new List<string>();
                        string CLASS_NONOW = "";
                        string SEAT_NONOW = "";
                        CLASSNOLIST = db.HRMT01.Where(x => x.SCHOOL_NO == UserInfo.SCHOOL_NO && x.USER_STATUS == UserStaus.Enabled &&x.CLASS_NO!= "" && x.CLASS_NO !=null).Select(x => x.CLASS_NO).ToList();
                        foreach (var item in CLASSNOLIST)
                        {
                            if (UserInfo.USER_NO.Contains(item) == true)
                            {

                                CLASS_NONOW = UserInfo.USER_NO.Substring(0, item.Length);

                                SEAT_NONOW = UserInfo.USER_NO.Substring(item.Length, UserInfo.USER_NO.Length-item.Length);
                                HRMT01count = db.HRMT01.Where(x => x.CLASS_NO == CLASS_NONOW && x.SCHOOL_NO == UserInfo.SCHOOL_NO && x.SEAT_NO == SEAT_NONOW && x.USER_STATUS == UserStaus.Enabled && x.USER_TYPE == UserType.Student).Count();
                                if (HRMT01count > 0)
                                {

                                    IsOK = true;
                                }
                            }
                        }


                    }
                }
            }
         

            viewModel.IsOK = IsOK;

            if (IsOK == false)
            {
                Message = "對應不到相應的數位學生證!";
                viewModel.Message = Message;
                TempData["StatusMessage"] = Message;
            }
            return Json(viewModel);
        }
        public JsonResult FindPersonBase(ADDT27 UserInfo)
        {
            GamePassedViewViewModel viewModel = new GamePassedViewViewModel();
            string Message = "";
            bool IsOK = false;
            int HRMT01count = 0;
            HRMT01count = db.HRMT01.Where(x => x.CARD_NO == UserInfo.USER_NO && x.SCHOOL_NO == UserInfo.SCHOOL_NO &&x.USER_STATUS==1).Count();
            if (HRMT01count > 0)
            {

                IsOK = true;

            }
          
       
            viewModel.IsOK = IsOK;
           
            if (IsOK == false) {
                Message = "對應不到相應的數位學生證!";
                viewModel.Message = Message;
                TempData["StatusMessage"] = Message;
            }
            return Json(viewModel);
        }
        public JsonResult GetLotteryPrice(GameLeveViewModel model)
        {

            GameLeveViewModel viewModel = this.gameService.CheckInfoCash(model.GameUserID, model.GAME_NO, model.Y_REPEAT);
            return Json(viewModel);
        }
        public JsonResult CheckInfodetail(GameLeveViewModel model)
        {
            string Message = string.Empty;

            model = gameService.CheckInfo(model, ref db, ref Message);
            return Json(model);
        }


        public JsonResult FindPersonForUSERNO(GameLeveViewModel model)
        {
            this.Shared();
            GamePassedViewViewModel viewModel = new GamePassedViewViewModel();
            UserProfile user = UserProfileHelper.Get();
            HRMT01 rMT01 = new HRMT01();
            AWAT01 UserCash = new AWAT01();
            string Message = string.Empty;
            rMT01 = db.HRMT01.Where(x => x.CARD_NO == model.GameUserID && x.SCHOOL_NO == user.SCHOOL_NO).FirstOrDefault();
            if (rMT01 == null) {


                rMT01 = db.HRMT01.Where(x => x.USER_NO == model.GameUserID && x.SCHOOL_NO == user.SCHOOL_NO).FirstOrDefault();
                if (rMT01 != null)
                {

                    model.GameUserID = rMT01.CARD_NO;
                }
                else {
                    List<string> CLASSNOLIST = new List<string>();
                    string CLASS_NONOW = "";
                    string SEAT_NONOW = "";
                    CLASSNOLIST = db.HRMT01.Where(x => x.SCHOOL_NO == user.SCHOOL_NO &&x.USER_STATUS == UserStaus.Enabled && x.CLASS_NO != "" && x.CLASS_NO != null).Select(x=>x.CLASS_NO).ToList();
                    string CARD_NO = "";

                    foreach (var item in CLASSNOLIST)
                    {
                        if (model.GameUserID.Contains(item) == true) {

                            CLASS_NONOW=model.GameUserID.Substring(0, item.Length);

                            SEAT_NONOW = model.GameUserID.Substring(item.Length, model.GameUserID.Length- item.Length);
                            rMT01 = db.HRMT01.Where(x => x.CLASS_NO == CLASS_NONOW && x.SCHOOL_NO == user.SCHOOL_NO && x.SEAT_NO == SEAT_NONOW && x.USER_STATUS == UserStaus.Enabled && x.USER_TYPE == UserType.Student).FirstOrDefault();
                            if (rMT01 != null)
                            {

                                CARD_NO = rMT01.CARD_NO;
                              
                            }
                        }
                    }
                }
            }
        
            if (rMT01 != null)
            {
                model.GameUserID = rMT01.CARD_NO;
                model.IsOK = true;
                viewModel.IsOK = true;
                UserCash = db.AWAT01.Where(y => y.SCHOOL_NO == rMT01.SCHOOL_NO && y.USER_NO == rMT01.USER_NO).FirstOrDefault();
                if (UserCash != null)
                {
                    int GameCASH = UserCash.CASH_AVAILABLE.Value;
                    if (UserCash.CASH_AVAILABLE.HasValue)
                    {
                        int CashIsOK = 0;
                        ADDT26 aDDT26Item = new ADDT26();
                        ADDT26_D dDT26_DItem = new ADDT26_D();
                        string GAMENOItem = "";
                        GAMENOItem = model.GAME_NO;
                        aDDT26Item = db.ADDT26.Where(x => x.GAME_NO == GAMENOItem && x.SCHOOL_NO == user.SCHOOL_NO).FirstOrDefault();
                        dDT26_DItem = db.ADDT26_D.Where(x => x.GAME_NO == GAMENOItem).FirstOrDefault();

                        CashIsOK = (int)dDT26_DItem.CASH + (int)UserCash.CASH_AVAILABLE;
                        if (UserCash.CASH_AVAILABLE < 0)
                        {
                            Message = $"餘額不足！{rMT01.SNAME}目前剩餘點數為{UserCash.CASH_AVAILABLE}點。";
                            viewModel.IsOK = false;
                            viewModel.Message = Message;
                            return Json(viewModel);
                        }
                        else
                        {
                            if (CashIsOK < 0)
                            {
                                Message = $"餘額不足！{rMT01.SNAME}目前剩餘點數為{UserCash.CASH_AVAILABLE}點。";
                                viewModel.IsOK = false;
                                viewModel.Message = Message;
                                return Json(viewModel);
                            }
                            else
                            {

                                int GameCASHCASH_Workhard = UserCash.CASH_AVAILABLE.Value;


                                string sSQL = @" SELECT  COUNT(*)  FROM LotteryPrize a inner join LotteryWinner b on a.PrizeId =b.PrizeId
                                   inner join ADDT27 c on b.CreaUserID= c.TEMP_USER_ID and a.GAME_NO=c.GAME_NO
                                      where a.GAME_NO=@GAME_NO  and c.GAME_USER_ID=@GameUserID ";
                                var temp = db.Database.Connection.Query<int>(sSQL, new
                                {
                                    GAME_NO = GAMENOItem,
                                    GameUserID = model.GameUserID
                                }).FirstOrDefault();

                                if ((dDT26_DItem.Y_REPEAT ?? false) == false && temp > 0)
                                {
                                    model.IsOK = false;
                                    Message = "您已經抽過此獎品囉！";
                                    viewModel.IsOK = false;
                                    viewModel.Message = Message;
                                    return Json(viewModel);
                                }
                            }


                        }
                    }
                }
            }
            else
            {
                model.IsOK = false;
                viewModel.IsOK = false;
                viewModel.Message = "找不帳號";
                model.Message = "找不帳號";
                TempData["StatusMessage"] = "找不帳號";
                return Json(viewModel);
            }
            model.IsOK = true;
            return Json(viewModel);
        }
        public JsonResult FindPerson(GameLeveViewModel model)
        {
            this.Shared();
            GamePassedViewViewModel viewModel = new GamePassedViewViewModel();
            UserProfile user = UserProfileHelper.Get();
            HRMT01 rMT01 = new HRMT01();
            AWAT01 UserCash = new AWAT01();
            string Message = string.Empty;
            rMT01 = db.HRMT01.Where(x => x.CARD_NO == model.GameUserID && x.SCHOOL_NO == user.SCHOOL_NO).FirstOrDefault();
            if (rMT01 != null)
            {
                model.IsOK = true;
                viewModel.IsOK = true;
                UserCash = db.AWAT01.Where(y => y.SCHOOL_NO == rMT01.SCHOOL_NO && y.USER_NO == rMT01.USER_NO).FirstOrDefault();
                if (UserCash != null)
                {
                    int GameCASH = UserCash.CASH_AVAILABLE.Value;
                    if (UserCash.CASH_AVAILABLE.HasValue)
                    {
                        int CashIsOK = 0;
                        ADDT26 aDDT26Item = new ADDT26();
                        ADDT26_D dDT26_DItem = new ADDT26_D();
                        string GAMENOItem = "";
                        GAMENOItem = model.GAME_NO;
                        aDDT26Item = db.ADDT26.Where(x => x.GAME_NO == GAMENOItem && x.SCHOOL_NO == user.SCHOOL_NO).FirstOrDefault();
                        dDT26_DItem = db.ADDT26_D.Where(x => x.GAME_NO == GAMENOItem).FirstOrDefault();

                        CashIsOK = (int)dDT26_DItem.CASH + (int)UserCash.CASH_AVAILABLE;
                        if (UserCash.CASH_AVAILABLE < 0)
                        {
                            Message = $"餘額不足！{rMT01.SNAME}目前剩餘點數為{UserCash.CASH_AVAILABLE}點。";
                            viewModel.IsOK = false;
                            viewModel.Message = Message;
                            return Json(viewModel);
                        }
                        else
                        {
                            if (CashIsOK < 0)
                            {
                                Message = $"餘額不足！{rMT01.SNAME}目前剩餘點數為{UserCash.CASH_AVAILABLE}點。";
                                viewModel.IsOK = false;
                                viewModel.Message = Message;
                                return Json(viewModel);
                            }
                            else
                            {

                                int GameCASHCASH_Workhard = UserCash.CASH_AVAILABLE.Value;


                                string sSQL = @" SELECT  COUNT(*)  FROM LotteryPrize a inner join LotteryWinner b on a.PrizeId =b.PrizeId
                                   inner join ADDT27 c on b.CreaUserID= c.TEMP_USER_ID and a.GAME_NO=c.GAME_NO
                                      where a.GAME_NO=@GAME_NO  and c.GAME_USER_ID=@GameUserID ";
                                var temp = db.Database.Connection.Query<int>(sSQL, new
                                {
                                    GAME_NO = GAMENOItem,
                                    GameUserID = model.GameUserID
                                }).FirstOrDefault();

                                if ((dDT26_DItem.Y_REPEAT ?? false) == false && temp > 0)
                                {
                                    model.IsOK = false;
                                    Message = "您已經抽過此獎品囉！";
                                    viewModel.IsOK = false;
                                    viewModel.Message = Message;
                                    return Json(viewModel);
                                }
                            }


                        }
                    }
                }
            }
            else {
                model.IsOK = false;
                viewModel.IsOK = false;
                viewModel.Message = "找不帳號";
                model.Message = "找不帳號";
                TempData["StatusMessage"] = "找不帳號";
                return Json(viewModel);
            }
            model.IsOK = true;
            return Json(viewModel);
        }
        public JsonResult LotteryPrizeLotterySave(GameLeveViewModel model)
        {

            List<Tuple<string, string, int>> valuesList = new List<Tuple<string, string, int>>();
            string Message = string.Empty;
            model = gameService.SaveLotteryPrize(model, ref Message, ref db);
            model = gameService.SaveLotteryPrizeCash(model, ref Message, ref db,ref valuesList);
            model = gameService.SaveLotteryPrizeCashInfo(model, ref Message,  ref db,Session.SessionID, ref valuesList);
            return Json(model);
        }
      public JsonResult LotteryPrizeSave(GameLeveViewModel model,ref string Message)
        {
            
            ADDT26 T26 = null;
            ADDT26_D T26_D = new ADDT26_D();
            ADDT27 T27 = new ADDT27();
            int LogCount = 0;
            T26 = db.ADDT26.Where(a => a.GAME_NO == model.GAME_NO).FirstOrDefault();
            T26_D = db.ADDT26_D.Where(X => X.GAME_NO == model.GAME_NO && X.LEVEL_NO == model.LEVEL_NO).FirstOrDefault();
            string TEMP_USER_ID = "";
    
            if (model.GameUserID == null ||model.User==null || model.User.TEMP_USER_ID == null) {
                if (!string.IsNullOrEmpty(model.GameUserID))
                {

                    var St = db.HRMT01.Where(a => a.CARD_NO == model.GameUserID).Count();

                    if (model.GameUserID.Length != 10)
                    {
                        Message = $"喔喔！刷卡失敗！<br><font style='font-size:20px'>您的 QR CODE 非本活動所用 ，卡號：{model.GameUserID},長度{model.GameUserID.Length} 碼。</font>";

                    }
                    else if (St >= 2)
                    {
                        Message = $"喔喔！刷卡失敗！<br><font style='font-size:20px'>您的卡片對應到多位來賓之資料，請聯絡系統人員，卡號：{model.GameUserID}。</font>";
                    }
                }
                else {
                    Message = $@"感應失敗找不到";

                }


                model.IsOK = false;
                return Json(model);
            }
            else { 
            TEMP_USER_ID = model.GameUserID.Trim();
            string Desc = string.Empty;
            T27 = db.ADDT27.Where(x => x.GAME_NO == model.GAME_NO && x.TEMP_USER_ID == model.User.TEMP_USER_ID).FirstOrDefault();
            

            if (T27?.GAME_USER_TYPE == UserType.Student)
            {
                Desc = $"<font  color='#FF0000'>{T27?.NAME}</font>同學";
            }
            else
            {
                Desc = $"<font color='#FF0000'>{T27?.NAME}</font>先生/女士";
            }
            if (T26_D.CASH <= 0)
            {
                Message = $@"<font color='red'>感應成功</font><br/>{Desc}<br/>本次扣<font color='#FF0000'>{ (-T26_D.CASH.Value).ToString()}</font>點，";
            }
            else
            {
                Message = $@"<font color='red'>感應成功</font><br/>{Desc}<br/>本次獲得<font color='#FF0000'>{ T26_D.CASH.Value.ToString()}</font>點，";
            }
                List<Tuple<string, string, int>> valuesList = new List<Tuple<string, string, int>>();
                model = gameService.SaveLotteryPrize(model, ref Message, ref db);
            model = gameService.SaveLotteryPrizeCash(model, ref Message, ref db,ref valuesList);
            model = gameService.SaveLotteryPrizeCashInfo(model, ref Message, ref db, Session.SessionID,ref valuesList);
                if (T27?.GAME_USER_TYPE == UserType.Student)
                {
                    var St = db.HRMT01.Where(a => a.CARD_NO == model.GameUserID && a.SCHOOL_NO == T27.SCHOOL_NO && a.USER_STATUS== 1).FirstOrDefault();
                    var AWAT= db.AWAT01.Where(x => x.SCHOOL_NO == T27.SCHOOL_NO && x.USER_NO == St.USER_NO).FirstOrDefault();

                    Message+= $@"，目前還剩餘<font color='#FF0000'>{ AWAT.CASH_AVAILABLE}</font>點。";
                }
                else {

                    var St = db.HRMT01.Where(a => a.CARD_NO == model.GameUserID && a.SCHOOL_NO == T27.SCHOOL_NO && a.USER_STATUS == 1).FirstOrDefault();
                    var AWAT08 = db.AWAT08.Where(x => x.SCHOOL_NO == T27.SCHOOL_NO && x.USER_NO == St.USER_NO).FirstOrDefault();
                    Message += $@"，目前還剩餘<font color='#FF0000'>{ AWAT08.CASH_AVAILABLE}</font>點。";
                }

            }
            //string Desc = string.Empty;
            //if (model.IsOK) { 
            //if (T27!=null&&T27.GAME_USER_TYPE == UserType.Student)
            //{
            //    Desc = $"{T27.NAME}同學";
            //}
            //else
            //{
            //    Desc = $"{T27.NAME}先生/女士";
            //}
            //if (T26_D.LEVEL_TYPE == ADDT26_D.LevelType.Pay || T26_D.LEVEL_TYPE == ADDT26_D.LevelType.ItemPrize)
            //{
            //    List<GameCashDetailsViewModel> GameCashDetailsViewModelItemInfo = new List<GameCashDetailsViewModel>();

            //    int CashINInfo = 0;
            //    CashINInfo = GameCashDetailsViewModelItemInfo.Sum(x => x.CASH_IN);



            //    if (T26_D.CASH <= 0)
            //    {
            //        Message = $@"<font color='red'>感應成功</font><br/>{Desc}<br/>本次支付{ T26_D.CASH.Value.ToString()}點，目前共有{(CashINInfo)}點";
            //    }
            //    else
            //    {
            //        Message = $@"<font color='red'>感應成功</font><br/>{Desc}<br/>本次獲得{ T26_D.CASH.Value.ToString()}點，目前共有{(CashINInfo)}點";
            //    }

            //    if (LogCount > 0)
            //    {
            //        Message = Message + @"<br/><div style='background-color:#87CEFA'>您已經通過本關，請繼續挑戰其他關卡喔！</div>";
            //    }
            //}
            //else if (T26_D.LEVEL_TYPE == ADDT26_D.LevelType.Test)
            //{
            //    Message = $@"<font color='red'>感應成功</font><br/>{Desc}<br/>已完成開卡，可以開始闖關了";
            //}
            //else if (T26_D.LEVEL_TYPE == ADDT26_D.LevelType.Prize)
            //{
            //    Message = $@"<font color='red'>感應成功</font><br/>{Desc}<br/>兌換成功";
            //}
            //model.IsOK = true;
            //}
            model.Message = Message;
            //ADDT26 T26 = new ADDT26();
            //ADDT26_D T26_D = new ADDT26_D();
            //ADDT27 T27 = new ADDT27();
            //T26_D = db.ADDT26_D.Where(X => X.GAME_NO == model.GAME_NO && X.LEVEL_NO == model.LEVEL_NO).FirstOrDefault();
            //T27 = db.ADDT27.Where(x => x.GAME_NO == model.GAME_NO && x.TEMP_USER_ID == model.User.TEMP_USER_ID).FirstOrDefault();
            //T26 = db.ADDT26.Where(X => X.GAME_NO == model.GAME_NO).FirstOrDefault();
            //string LogDesc = string.Empty;
            //string LOG_ID = string.Empty;
            //LogDesc = T26.GAME_NAME + T26_D.LEVEL_NAME;
            //CashHelper.AddCash(null, (int)(-1*T26_D.CASH), T27.SCHOOL_NO, T27.USER_NO, "ADDT26_D", T26_D.LEVEL_NO, LogDesc, true, ref db);
            //if (OK == false)
            //{
            //    TempData["StatusMessage"] = Message;
            //}

            return Json(model);
        }

        /// <summary>
        /// 關卡畫面交易處理
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [AllowCrossSite(Origin = "*")]
        public JsonResult LevelSave(GameLeveViewModel model)
        {
            string Message = string.Empty;
            string LOG_ID = string.Empty;

            if (ModelState.IsValid == false)
            {
                Message = "警告!!輸入的內容有錯誤";
                return Json(Message);
            }
            else
            {
                bool OK = this.gameService.SaveLevelData(model, ref db, ref LOG_ID, ref Message);

                ModelState.Clear();

                string LevelType = ADDT26_D.LevelType.Pay;
                if (string.IsNullOrEmpty(LOG_ID))
                {
                    LevelType = ADDT26_D.LevelType.Test;
                }

                GamePassedViewViewModel viewModel = this.gameService.GetIsPassed(OK, model.GAME_NO, model.LEVEL_NO, LevelType, Message, "LevelView", LOG_ID, ref db);

                return Json(viewModel);
            }
        }
        /// <summary>
        /// 新增獎品抽獎交易處理
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [AllowCrossSite(Origin = "*")]
        public JsonResult LotteryLevelSave(GameLeveViewModel model)
        {
            string Message = string.Empty;
            string LOG_ID = string.Empty;
            this.Shared();
            UserProfile user= UserProfileHelper.Get();
            AWAT01 UserCash = new AWAT01();
            HRMT01 hRMT = new HRMT01();
            GamePassedViewViewModel viewModel = new GamePassedViewViewModel();
            if (model != null && model.GameUserID != null)

            {
                hRMT = db.HRMT01.Where(x => x.SCHOOL_NO == user.SCHOOL_NO && x.CARD_NO == model.GameUserID).FirstOrDefault();
                UserCash = db.AWAT01.Where(y => y.SCHOOL_NO == hRMT.SCHOOL_NO && y.USER_NO == hRMT.USER_NO).FirstOrDefault();
                if (UserCash != null)
                {
                    int GameCASH = UserCash.CASH_AVAILABLE.Value;
                    if (UserCash.CASH_WORKHARD.HasValue)
                    {
                        int CashIsOK = 0;
                        ADDT26 aDDT26Item = new ADDT26();
                        ADDT26_D dDT26_DItem = new ADDT26_D();
                        string GAMENOItem = "";
                        GAMENOItem = model.GAME_NO;
                        aDDT26Item = db.ADDT26.Where(x => x.GAME_NO == GAMENOItem && x.SCHOOL_NO == user.SCHOOL_NO).FirstOrDefault();
                        dDT26_DItem = db.ADDT26_D.Where(x => x.GAME_NO == GAMENOItem).FirstOrDefault();

                        CashIsOK = (int)dDT26_DItem.CASH + (int)UserCash.CASH_AVAILABLE;
                        if (UserCash.CASH_AVAILABLE < 0)
                        {
                            Message = $"餘額不足！{hRMT.SNAME}目前剩餘點數為{UserCash.CASH_AVAILABLE}點。";
                            viewModel.IsOK = false;
                            viewModel.Message = Message;
                            return Json(viewModel);
                        }
                        else
                        {
                            if (CashIsOK < 0)
                            {
                                Message = $"餘額不足！{hRMT.SNAME}目前剩餘點數為{UserCash.CASH_AVAILABLE}點。";
                                viewModel.IsOK = false;
                                viewModel.Message = Message;
                                return Json(viewModel);
                            }
                            else
                            {

                                int GameCASHCASH_Workhard = UserCash.CASH_AVAILABLE.Value;
                            }


                        }
                    }
                }
            }
            if (ModelState.IsValid == false)
            {
                Message = "警告!!輸入的內容有錯誤";
                return Json(Message);
            }
            else
            {
                ADDT27 aDDT27s = new ADDT27();
                if (model.User != null && model.User.TEMP_USER_ID != null)
                {
                    aDDT27s = db.ADDT27.Where(x => x.TEMP_USER_ID == model.User.TEMP_USER_ID).FirstOrDefault();
                    string Desc = string.Empty;

                    if (aDDT27s?.GAME_USER_TYPE == UserType.Student)
                    {
                        Desc = $"<font  color='#FF0000'>{aDDT27s?.NAME}</font>同學";
                    }
                    else
                    {
                        Desc = $"<font  color='#FF0000'>{aDDT27s?.NAME}</font>先生/女士";
                    }
                    Message = Desc+",兌換成功";

                }
                else
                {
                    aDDT27s = this.gameService.SaveLotteryLevelData(model, user, UserCash, ref db, ref LOG_ID, ref Message);
                }

                    ModelState.Clear();
                bool OK = false;
                if (aDDT27s != null) { OK = true; }
                string LevelType = ADDT26_D.LevelType.Pay;
                if (string.IsNullOrEmpty(LOG_ID))
                {
                    LevelType = ADDT26_D.LevelType.Test;
                }

           viewModel = this.gameService.GetIsPassed(OK, model.GAME_NO, model.LEVEL_NO, LevelType, Message, "LevelView", LOG_ID, ref db);

                return Json(viewModel);
            }
        }

        [AllowAnonymous]
        [AllowCrossSite(Origin = "*")]
        public JsonResult LotteryLevelSaveInBackGround(List<GameLeveViewModel> models)
        {
            GamePassedViewViewModel viewModel = new GamePassedViewViewModel();
            string Message = string.Empty;
            string LOG_ID = string.Empty;
            int successCount = 0;
            this.Shared();
            UserProfile user = UserProfileHelper.Get();
            ADDT27 aDDT27s = new ADDT27();
            HRMT01 hRMT = new HRMT01();
            AWAT01 UserCash = new AWAT01();
            if (models!=null&&models.FirstOrDefault().GameUserID != null)

            {

                string GUID = "";
                GUID = models.FirstOrDefault().GameUserID;
                hRMT = db.HRMT01.Where(x => x.SCHOOL_NO == user.SCHOOL_NO && x.CARD_NO == GUID).FirstOrDefault();
                if (hRMT == null) {

                    hRMT = db.HRMT01.Where(x => x.SCHOOL_NO == user.SCHOOL_NO && x.USER_NO == GUID).FirstOrDefault();
                    if (hRMT == null)
                    {
                        List<string> CLASSNOLIST = new List<string>();
                        string CLASS_NONOW = "";
                        string SEAT_NONOW = "";
                        CLASSNOLIST = db.HRMT01.Where(x => x.SCHOOL_NO == user.SCHOOL_NO && x.USER_STATUS == UserStaus.Enabled && x.CLASS_NO != "" && x.CLASS_NO != null).Select(x => x.CLASS_NO).ToList();
                        foreach (var item in CLASSNOLIST)
                        {

                            if (GUID.Contains(item) == true)
                            {

                                CLASS_NONOW = GUID.Substring(0, item.Length);

                                SEAT_NONOW = GUID.Substring(item.Length, GUID.Length - item.Length);
                                hRMT = db.HRMT01.Where(x => x.CLASS_NO == CLASS_NONOW && x.SCHOOL_NO == user.SCHOOL_NO && x.SEAT_NO == SEAT_NONOW && x.USER_STATUS == UserStaus.Enabled && x.USER_TYPE == UserType.Student).FirstOrDefault();
                                
                            }

                        }
                        }
                }
                 UserCash = db.AWAT01.Where(y => y.SCHOOL_NO == hRMT.SCHOOL_NO && y.USER_NO == hRMT.USER_NO).FirstOrDefault();
                if (UserCash != null)
                {
                     int GameCASH = UserCash.CASH_AVAILABLE.Value;
                    if (UserCash.CASH_AVAILABLE.HasValue)
                    {
                        int CashIsOK = 0;
                        ADDT26 aDDT26Item = new ADDT26();
                        ADDT26_D dDT26_DItem = new ADDT26_D();
                        string GAMENOItem = "";
                        GAMENOItem = models.FirstOrDefault().GAME_NO;
                        aDDT26Item =db.ADDT26.Where(x => x.GAME_NO == GAMENOItem && x.SCHOOL_NO == user.SCHOOL_NO).FirstOrDefault();
                        dDT26_DItem = db.ADDT26_D.Where(x => x.GAME_NO == GAMENOItem).FirstOrDefault();

                        CashIsOK = (int)dDT26_DItem.CASH + (int)UserCash.CASH_AVAILABLE;
                        if (UserCash.CASH_AVAILABLE < 0)
                        {
                            Message = $"餘額不足！{hRMT.SNAME}目前剩餘點數為{UserCash.CASH_AVAILABLE}點。";
                            viewModel.IsOK = false;
                            viewModel.Message = Message;
                            return Json(viewModel);
                        }
                        else {
                            if (CashIsOK < 0)
                            {
                                Message = $"餘額不足！{hRMT.SNAME}目前剩餘點數為{UserCash.CASH_AVAILABLE}點。";
                                viewModel.IsOK = false;
                                viewModel.Message = Message;
                                return Json(viewModel);
                            }
                            else {

                                int GameCASHCASH_Workhard = UserCash.CASH_AVAILABLE.Value;
                            }
                 

                        }
                    }
                }
                
            }
            
               
                aDDT27s = this.gameService.SaveLotteryLevelData(models.FirstOrDefault(), user, UserCash, ref db, ref LOG_ID, ref Message);

                if (aDDT27s!=null) successCount++;
          
            viewModel.TEMP_USER_ID = aDDT27s?.TEMP_USER_ID;
            viewModel.IsOK = true;
            if (models.FirstOrDefault().User != null)
            {
                models.FirstOrDefault().User.TEMP_USER_ID= aDDT27s?.TEMP_USER_ID;

            }
            GameLeveViewModel modelItem = new GameLeveViewModel();
            foreach (var item in models) {
                if (item.LotteryPrize != null && item.LotteryPrize.PrizeId!=null) {
                    if (item.GameUserID == null) {
                        item.GameUserID =aDDT27s.GAME_USER_ID;
                    }
                    modelItem = item;
                }
            }
            modelItem.User.TEMP_USER_ID = aDDT27s?.TEMP_USER_ID;
            LotteryPrizeSave(modelItem, ref Message);

            viewModel.Message = Message;
            return Json(viewModel);
        }

        [AllowAnonymous]
        [AllowCrossSite(Origin = "*")]
        public JsonResult LevelSaveInBackGround(List<GameLeveViewModel> models)
        {
            string Message = string.Empty;
            string LOG_ID = string.Empty;
            int successCount = 0;
            foreach (var model in models)
            {
                bool OK = this.gameService.SaveLevelData(model, ref db, ref LOG_ID, ref Message);

                if (OK) successCount++;
            }

            return Json("done working background, success count:" + successCount);
        }

        /// <summary>
        /// 街頭藝人報名
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ActionResult BuskerAddView(GameBuskerAddViewModel model)
        {
            string Message = string.Empty;

            var Game = gameService.GetIsGAME(model?.GAME_NO, ref db, ref Message, UserProfileHelper.GetGameNoCookie(), true);
            if (!string.IsNullOrWhiteSpace(Message))
            {
                TempData["GameName"] = Game?.GAME_NAME;
                return RedirectToAction("ErrGame", new GameErrorViewModel { Error = Message });
            }
            model.GAME_NO = Game.GAME_NO;
            ModelState.Clear();
            // model = gameService.GetLeveData(model, ref db);

            return View(model);
        }

        /// <summary>
        ///  增加 表演人員 PartialView 畫面
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ActionResult _DetailsBusker(GameBuskerAddViewModel model)
        {
            ModelState.Clear();
            string Message = string.Empty;

            var NewModel = this.gameService.GetgameBuskerUser(model, ref db, ref Message);

            if (!string.IsNullOrWhiteSpace(Message))
            {
                var data = "{ \"Error\" : \"" + Message + "\" }";
                return Json(@data, JsonRequestBehavior.AllowGet);
            }

            return PartialView(NewModel);
        }

        /// <summary>
        /// 街頭藝人報名 存檔
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult BuskerAddSave(GameBuskerAddViewModel model)
        {
            string Message = string.Empty;

            if (ModelState.IsValid == false)
            {
                Message = "警告!!輸入的內容有錯誤";
            }
            else
            {
                string TITLE_SHOW_ID = string.Empty;
                bool OK = this.gameService.SaveBuskerData(model, ref db, ref Message, ref TITLE_SHOW_ID);

                ModelState.Clear();

                var viewModel = this.gameService.GetBuskerPassedData(OK, model.GAME_NO, TITLE_SHOW_ID, ref Message, ref db);

                return View("BuskerPassedView", viewModel);
            }

            return View("BuskerAddView", model);
        }

        /// <summary>
        /// 街頭藝人報名 結果畫面
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ActionResult BuskerPassedView(GameBuskerAddViewModel model)
        {
            return View(model);
        }

        /// <summary>
        ///取得目前 Live
        /// </summary>
        /// <param name="GAME_NO"></param>
        /// <returns></returns>
        public ActionResult GetLive_Stream(string GAME_NO)
        {
            string Message = string.Empty;

            var Game = gameService.GetIsGAME(GAME_NO, ref db, ref Message);
            if (!string.IsNullOrWhiteSpace(Message))
            {
                TempData["GameName"] = Game?.GAME_NAME;
                return RedirectToAction("ErrGame", new GameErrorViewModel { Error = Message });
            }

            string TITLE_SHOW_ID = this.gameService.GetLive_StreamByKey(GAME_NO, ref db);
            var data = "{ \"TITLE_SHOW_ID\" : \"" + TITLE_SHOW_ID + "\"}";

            return Json(@data, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// 按讚
        /// </summary>
        /// <param name="GAME_NO"></param>
        /// <param name="GameUserID"></param>
        /// <param name="TITLE_SHOW_ID"></param>
        /// <returns></returns>
        public ActionResult LikeSave(string GAME_NO, string GameUserID, string TITLE_SHOW_ID, bool UnApply)
        {
            string Success = "true";
            string Message = string.Empty;

            var Game = gameService.GetIsGAME(GAME_NO, ref db, ref Message);
            if (!string.IsNullOrWhiteSpace(Message))
            {
                TempData["GameName"] = Game?.GAME_NAME;
                return RedirectToAction("ErrGame", new GameErrorViewModel { Error = Message });
            }

            int LIKE_COUNT = this.gameService.SaveLikeData(GAME_NO, GameUserID, TITLE_SHOW_ID, UnApply, ref Message, ref db);

            if (!string.IsNullOrWhiteSpace(Message))
            {
                Success = "false";
            }

            var data = "{\"Success\" : \"" + Success + "\" , \"LIKE_COUNT\" : \"" + LIKE_COUNT.ToString() + "\" , \"Error\" : \"" + Message + "\" }";

            return Json(@data, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// 取得 按讚數
        /// </summary>
        /// <param name="TITLE_SHOW_ID"></param>
        /// <returns></returns>
        public ActionResult GetLike(string TITLE_SHOW_ID)
        {
            string Success = "true";
            string Message = string.Empty;
            int LIKE_COUNT = 0;

            try
            {
                LIKE_COUNT = this.gameService.GetLikeData(TITLE_SHOW_ID, ref db);
            }
            catch (Exception ex)
            {
                Success = "false";
                Message = ex.Message;
            }

            var data = "{\"Success\" : \"" + Success + "\" , \"LIKE_COUNT\" : \"" + LIKE_COUNT.ToString() + "\" , \"Error\" : \"" + Message + "\" }";

            return Json(@data, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// 直播節目
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ActionResult BuskerLikeView(GameBuskerLikeViewModel model)
        {
            string Message = string.Empty;
            var Game = new ADDT26();
            if (model.UnApply!=true) {

                Game = gameService.GetIsGAME(model?.GAME_NO, ref db, ref Message, UserProfileHelper.GetGameNoCookie(), true);
            }
            
            if (!string.IsNullOrWhiteSpace(Message))
            {
                TempData["GameName"] = Game?.GAME_NAME;
                return RedirectToAction("ErrGame", new GameErrorViewModel { Error = Message });
            }
            if (!model.UnApply)
            {

                model.GAME_NO = Game.GAME_NO;
            }
           
           
            ModelState.Clear();
            model = this.gameService.GetBuskerLikeData(model, ref Message, ref db);

            TempData["StatusMessage"] = Message;

            return View(model);
        }

        /// <summary>
        /// 來賓打賞 QR CODE 及網址
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ActionResult BuskerGuestLikeView(GameBuskerLikeViewModel model)
        {
            string Message = string.Empty;

            var Game = gameService.GetIsGAME(model?.GAME_NO, ref db, ref Message);
            if (!string.IsNullOrWhiteSpace(Message))
            {
                TempData["GameName"] = Game?.GAME_NAME;
                return RedirectToAction("ErrGame", new GameErrorViewModel { Error = Message });
            }
            model.GAME_NO = Game.GAME_NO;
            ModelState.Clear();
            model = this.gameService.GetBuskerLikeData(model, ref Message, ref db);

            TempData["StatusMessage"] = Message;

            return View(model);
        }

        /// <summary>
        /// WebContent
        /// </summary>
        /// <param name="Url"></param>
        /// <param name="Error"></param>
        /// <returns></returns>
        private string GetWebContent(string Url, ref string Error)
        {
            string strResult = "";

            try
            {
                HttpWebRequest request = (HttpWebRequest)WebRequest.Create(Url);
                //聲明一個HttpWebRequest請求

                request.Timeout = 30000;

                //設置連接逾時時間
                request.Headers.Set("Pragma", "no-cache");

                HttpWebResponse response = (HttpWebResponse)request.GetResponse();

                Stream streamReceive = response.GetResponseStream();

                StreamReader streamReader = new StreamReader(streamReceive);

                strResult = streamReader.ReadToEnd();
            }
            catch (Exception ex)
            {
                Error = ex.Message;
            }

            return strResult;
        }

        #endregion 前台

        #region 街頭藝人 後台

        /// <summary>
        /// 表演活動後台管理
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [CheckPermissionSeeion(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult BuskerManager(GameBuskerManagerViewModel model)
        {
            this.GetBreName();
            this.Shared(Bre_Name + " - 街頭藝人管理列表");
            if (string.IsNullOrEmpty(model.Search?.WhereGAME_NO))
            {
                return RedirectToAction("GameIndex");
            }
            return View(model);
        }

        /// <summary>
        /// 表演活動後台管理 列表 PartialView
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [CheckPermissionSeeion(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult _BuskerPageContent(GameBuskerManagerViewModel model)
        {
            this.GetBreName();
            this.Shared();
            if (model == null) model = new GameBuskerManagerViewModel();
            if (model.Search == null) model.Search = new GameSearchViewModel();

            if (string.IsNullOrWhiteSpace(model.Search.WhereSCHOOL_NO))
            {
                model.Search.WhereSCHOOL_NO = SCHOOL_NO;
            }

            string Message = string.Empty;

            gameService.SaveAllBuskerCashData(model.Search.WhereGAME_NO, user, ref Message, ref db);

            if (!string.IsNullOrWhiteSpace(Message))
            {
                return PartialView("ErrGame", new GameErrorViewModel { Error = Message });
            }

            model = gameService.GetBuskerManagerListData(model, ref db);

            return PartialView(model);
        }

        /// <summary>
        /// 街頭藝人 表演後台存檔
        /// </summary>
        /// <param name="GAME_NO"></param>
        /// <param name="TITLE_SHOW_ID"></param>
        /// <param name="TITLE_SHOW_NAME"></param>
        /// <param name="ORDER_BY"></param>
        /// <param name="IsLive"></param>
        /// <param name="IsDelete"></param>
        /// <returns></returns>
        public ActionResult BuskerManagerSave(string GAME_NO, string TITLE_SHOW_ID, string TITLE_SHOW_NAME, string ORDER_BY, bool IsLive, bool IsDelete, bool IsUnLive)
        {
            this.GetBreName();
            this.Shared();
            string Success = "true";
            string Message = string.Empty;

            var OK = this.gameService.SaveBuskerManagerData(GAME_NO, TITLE_SHOW_ID, TITLE_SHOW_NAME, ORDER_BY, IsLive, IsDelete, IsUnLive, ref Message, ref db);

            if (!string.IsNullOrWhiteSpace(Message))
            {
                Success = "false";
            }

            var data = "{\"Success\" : \"" + Success + "\", \"Error\" : \"" + Message + "\" }";

            return Json(@data, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// 給於此街頭藝人小組活動酷幣點數
        /// </summary>
        /// <param name="GAME_NO"></param>
        /// <param name="TITLE_SHOW_ID"></param>
        /// <param name="CASH"></param>
        /// <returns></returns>
        public ActionResult BuskerCashSave(string GAME_NO, string TITLE_SHOW_ID, short CASH)
        {
            this.GetBreName();
            this.Shared();
            string Success = "true";
            string Message = string.Empty;

            var OK = this.gameService.SaveBuskerCashData(GAME_NO, TITLE_SHOW_ID, CASH, user, ref Message, ref db);

            if (!string.IsNullOrWhiteSpace(Message))
            {
                Success = "false";
            }

            var data = "{\"Success\" : \"" + Success + "\", \"Error\" : \"" + Message + "\" }";

            return Json(@data, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// 街頭藝人管理編輯
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [CheckPermissionSeeion(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult BuskerEdit(GameBuskerEditViewModel model)
        {
            this.GetBreName();
            this.Shared(Bre_Name + " - 街頭藝人管理編輯");

            if (model == null) model = new GameBuskerEditViewModel();
            if (model.Search == null) model.Search = new GameSearchViewModel();

            if (string.IsNullOrEmpty(model.Search?.WhereGAME_NO))
            {
                return RedirectToAction("GameIndex");
            }

            if (!string.IsNullOrWhiteSpace(model.WhereTITLE_SHOW_ID))
            {
                model = this.gameService.GetBuskerEditData(model, ref db);
            }

            return View(model);
        }

        /// <summary>
        /// 街頭藝人管理編輯 Save
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [CheckPermissionSeeion(CheckACTION_ID = "Index")] //檢查權限
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult BuskerEditSave(GameBuskerEditViewModel model)
        {
            this.GetBreName();
            this.Shared(Bre_Name + " - 街頭藝人管理編輯");

            if (model == null) model = new GameBuskerEditViewModel();
            if (model.Search == null) model.Search = new GameSearchViewModel();

            if (string.IsNullOrEmpty(model.Search?.WhereGAME_NO))
            {
                return RedirectToAction("GameIndex");
            }

            string Message = string.Empty;

            if (ModelState.IsValid == false)
            {
                Message = "警告!!輸入的內容有錯誤";
            }
            else
            {
                bool OK = this.gameService.SaveBuskerEditData(model, user, ref db, ref Message);

                if (OK)
                {
                    ModelState.Clear();
                    model.Search.WhereGAME_NO = null;
                    TempData["StatusMessage"] = "儲存完成";
                    GameBuskerManagerViewModel viewModel = new GameBuskerManagerViewModel
                    {
                        Search = model.Search
                    };
                    this.GetBreName();
                    this.Shared();
                    return View("BuskerManager", viewModel);
                }
            }

            TempData["StatusMessage"] = Message;

#if DEBUG
            var errorsaa = ModelState
                        .Where(x => x.Value.Errors.Count > 0)
                        .Select(x => new { x.Key, x.Value.Errors })
                        .ToArray();
#endif

            return View("BuskerEdit", model);
        }

        /// <summary>
        /// 街頭藝人管理編輯  PartialView
        /// </summary>
        /// <param name="Item"></param>
        /// <returns></returns>
        [CheckPermissionSeeion(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult _BuskerEditDetails(GameBuskerEditDetailsViewModel Item)
        {
            this.GetBreName();
            this.Shared();
            return PartialView(Item);
        }

        /// <summary>
        /// 街頭藝人管理編輯 OpenPerson PartialView
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [CheckPermissionSeeion(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult _BuskerOpenPersonView(GameBuskerOpenPersonViewViewModel model)
        {
            this.GetBreName();
            this.Shared();

            model = this.gameService.GetBuskerOpenPersonData(model, ref db);
            return PartialView(model);
        }

        /// <summary>
        /// 街頭藝人管理編輯 新增人員 PartialView
        /// </summary>
        /// <param name="GAME_NO"></param>
        /// <param name="TEMP_USER_ID"></param>
        /// <returns></returns>
        [CheckPermissionSeeion(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult _BuskerOpenPersonAddData(string GAME_NO, string TEMP_USER_ID)
        {
            this.GetBreName();
            this.Shared();
            var model = this.gameService.GetBuskerAddPersonData(GAME_NO, TEMP_USER_ID, ref db);
            return PartialView("_BuskerEditDetails", model);
        }

        #endregion 街頭藝人 後台

        #region 抽獎

        /// <summary>
        /// 抽獎名單列表
        /// </summary>
        /// <returns></returns>
        [CheckPermissionSeeion(CheckACTION_ID = "Index,Lottery")] //檢查權限
        public ActionResult Lottery(GameLotteryViewModel model)
        {
            this.GetBreName();
            this.Shared(Bre_Name + " - 抽獎名單列表");
            if (string.IsNullOrEmpty(model.Search?.WhereGAME_NO))
            {
                return RedirectToAction("GameIndex");
            }
            return View(model);
        }

        /// <summary>
        /// 抽獎名單列表 PartialView
        /// </summary>
        /// <returns></returns>
        [CheckPermissionSeeion(CheckACTION_ID = "Index,Lottery")] //檢查權限
        public ActionResult _LotteryPartial(GameLotteryViewModel model)
        {
            this.GetBreName();
            this.Shared();
            if (model == null) model = new GameLotteryViewModel();
            if (model.Search == null) model.Search = new GameSearchViewModel();

            if (string.IsNullOrWhiteSpace(model.Search.WhereSCHOOL_NO))
            {
                model.Search.WhereSCHOOL_NO = SCHOOL_NO;
            }

            model = gameService.GetLotteryListData(model, ref db);

            return PartialView(model);
        }

        /// <summary>
        /// 抽獎作廢
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [CheckPermissionSeeion(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult LotteryDel(GameLotteryViewModel model)
        {
            this.GetBreName();
            this.Shared(Bre_Name + " - 抽獎名單列表");
            if (string.IsNullOrEmpty(model.Search?.WhereGAME_NO))
            {
                return RedirectToAction("GameIndex");
            }

            string Message = string.Empty;
            var OK = this.gameService.SaveLotteryDelData(model, user, ref db, ref Message);

            if (OK)
            {
                Message = "作廢成功";
            }

            if (!string.IsNullOrEmpty(Message))
            {
                TempData["StatusMessage"] = Message;
            }

            model.Search.WhereLOTTERY_NO = null;
            ModelState.Clear();
            var viewModel = new GameLotteryViewModel
            {
                Search = new GameSearchViewModel()
            };

            viewModel.Search = model.Search;
            return View("Lottery", viewModel);
        }

        /// <summary>
        /// 抽獎設定
        /// </summary>
        /// <returns></returns>
        [CheckPermissionSeeion(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult LotteryCreView(GameLotteryCreViewModel model)
        {
            this.GetBreName();
            this.Shared(Bre_Name + " - 抽獎設定");
            if (string.IsNullOrEmpty(model.Search?.WhereGAME_NO))
            {
                return RedirectToAction("GameIndex");
            }

            if (!string.IsNullOrWhiteSpace(model.Search.WhereLOTTERY_NO))
            {
                model.Main = this.gameService.GetAddt32Data(model, db);
            }

            if (model.Main?.ArrLEVEL?.Length > 0)
            {
                Array.Sort(model.Main?.ArrLEVEL, StringComparer.InvariantCulture);
                model.Main.LEVEL = string.Join(",", model.Main?.ArrLEVEL);
            }

            ViewBag.LevelItem = this.gameService.GetLevelItem(model.Search.WhereGAME_NO, model.Main?.LEVEL, ref db);
            ViewBag.LevelCountItem = this.gameService.GetLevelCountItem(model.Search.WhereGAME_NO, model.Main?.LEVEL_COUNT, ref db);
            return View(model);
        }

        /// <summary>
        /// 長官抽獎模式 及 中獎名單 =>等待長官按 「開始抽獎」=>[動畫]=>[得獎名單]
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [CheckPermissionSeeion(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult ChiefLottery(GameLotteryCreViewModel model)
        {
            this.GetBreName();
            this.Shared(Bre_Name + " - 新增抽獎中獎名單");
            if (string.IsNullOrEmpty(model.Search?.WhereGAME_NO))
            {
                return RedirectToAction("GameIndex");
            }

            if (!string.IsNullOrWhiteSpace(model.Search.WhereLOTTERY_NO))
            {
                if (model.LotteryType == (byte)ADDT32.LotteryTypeVal.進入開始抽獎畫面)
                {
                    model.Main = this.gameService.GetAddt32Data(model, db);
                }
            }

            if (model.Main?.ArrLEVEL?.Length > 0)
            {
                Array.Sort(model.Main?.ArrLEVEL, StringComparer.InvariantCulture);
                model.Main.LEVEL = string.Join(",", model.Main?.ArrLEVEL);
            }

            if (model.LotteryType == (byte)ADDT32.LotteryTypeVal.儲存及進入開始抽獎畫面 || model.LotteryType == (byte)ADDT32.LotteryTypeVal.進入開始抽獎畫面)
            {
                model = this.gameService.GetLotteryPeopleData(model, ref db);
            }

            string Message = string.Empty;
            string LOTTERY_NO = string.Empty;

            bool OK = this.gameService.SaveLotteryData(model, user, ref db, ref Message, out LOTTERY_NO);

            if (!string.IsNullOrEmpty(Message))
            {
                TempData["StatusMessage"] = Message;
                return View("LotteryCreView", model);
            }

            if (model.LotteryType == (byte)ADDT32.LotteryTypeVal.儲存及進入開始抽獎畫面 || model.LotteryType == (byte)ADDT32.LotteryTypeVal.進入開始抽獎畫面)
            {
                if (model.Search.WhereLOTTERY_NO != LOTTERY_NO)
                {
                    ModelState.Clear();
                    model.Search.WhereLOTTERY_NO = LOTTERY_NO;
                    model.Main = this.gameService.GetAddt32Data(model, db);
                }

                return View(model);
            }
            else
            {
                model.Search.WhereLOTTERY_NO = null;
                var viewModel = new GameLotteryViewModel
                {
                    Search = new GameSearchViewModel()
                };
                viewModel.Search = model.Search;
                return View("Lottery", viewModel);
            }
        }

        /// <summary>
        /// 新增抽獎中獎名單(舊)
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [CheckPermissionSeeion(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult LotteryShowPeopleView(GameLotteryCreViewModel model)
        {
            this.GetBreName();
            this.Shared(Bre_Name + " - 新增抽獎中獎名單");
            if (string.IsNullOrEmpty(model.Search?.WhereGAME_NO))
            {
                return RedirectToAction("GameIndex");
            }

            if (model.Main?.ArrLEVEL?.Length > 0)
            {
                Array.Sort(model.Main?.ArrLEVEL, StringComparer.InvariantCulture);
                model.Main.LEVEL = string.Join(",", model.Main?.ArrLEVEL);
            }

            model = this.gameService.GetLotteryPeopleData(model, ref db);
            return View(model);
        }

        /// <summary>
        /// 新增抽獎中獎名單 HiddenFor
        /// </summary>
        /// <param name="item"></param>
        /// <returns></returns>
        [CheckPermissionSeeion(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult _LotteryPeopleView(GameLotteryPeopleViewModel item)
        {
            this.GetBreName();
            this.Shared(Bre_Name + " - 新增抽獎中獎名單");
            return View(item);
        }

        /// <summary>
        /// 新增抽獎中獎名單 Save
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [CheckPermissionSeeion(CheckACTION_ID = "Index")] //檢查權限
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult LotterySave(GameLotteryCreViewModel model)
        {
            this.GetBreName();
            this.Shared(Bre_Name + " - 新增抽獎中獎名單");
            if (string.IsNullOrEmpty(model.Search?.WhereGAME_NO))
            {
                return RedirectToAction("GameIndex");
            }
            string Message = string.Empty;
            string LOTTERY_NO = string.Empty;

            if (ModelState.IsValid == false)
            {
                Message = "警告!!輸入的內容有錯誤";
            }
            else
            {
                bool OK = this.gameService.SaveLotteryData(model, user, ref db, ref Message, out LOTTERY_NO);

                if (OK)
                {
                    ModelState.Clear();
                    TempData["StatusMessage"] = "儲存完成";
                    GameLotteryViewModel viewModel = new GameLotteryViewModel
                    {
                        Search = model.Search
                    };
                    this.GetBreName();
                    this.Shared();
                    return View("Lottery", viewModel);
                }
            }

            TempData["StatusMessage"] = Message;

            return View("LotteryShowPeopleView", model);
        }

        /// <summary>
        /// 抽獎中獎名單明細
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [CheckPermissionSeeion(CheckACTION_ID = "Index,Lottery")] //檢查權限
        public ActionResult LotteryDetails(GameLotteryCreViewModel model)
        {
            this.GetBreName();
            this.Shared(Bre_Name + " - 抽獎中獎名單明細");
            if (string.IsNullOrEmpty(model.Search?.WhereGAME_NO))
            {
                return RedirectToAction("GameIndex");
            }
            model = this.gameService.GetLotteryDetailsData(model, ref db);

            if (model.Main?.ArrLEVEL?.Length > 0)
            {
                Array.Sort(model.Main?.ArrLEVEL, StringComparer.InvariantCulture);
                model.Main.LEVEL = string.Join(",", model.Main?.ArrLEVEL);
            }

            ViewBag.LevelItem = this.gameService.GetLevelItem(model.Search.WhereGAME_NO, model.Main?.LEVEL, ref db);

            return View(model);
        }

        /// <summary>
        /// 抽獎中獎名單 更新是否領獎
        /// </summary>
        /// <param name="LOTTERY_NO"></param>
        /// <param name="ITEM_NO"></param>
        /// <param name="Checked"></param>
        /// <returns></returns>
        [HttpPost]
        [CheckPermissionSeeion(CheckACTION_ID = "Index,Lottery", ResultType = "Json")] //檢查權限
        public JsonResult SaveUpdateReceiveAward(string LOTTERY_NO, string ITEM_NO, bool Checked)
        {
            bool Success = false;
            string Message = string.Empty;

            Success = this.gameService.SaveUpdateReceiveAwardData(LOTTERY_NO, ITEM_NO, Checked, ref db, ref Message);

            var data = "{ \"Success\" : \"" + Success.ToString() + "\" , \"Error\" : \"" + Message + "\" }";
            return Json(@data, JsonRequestBehavior.AllowGet);
        }

        #endregion 抽獎

        #region 人員管理

        /// <summary>
        /// 批次作業(選取功能)
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [CheckPermissionSeeion(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult BatchWork(GameBatchWorkIndexViewModel model)
        {
            this.GetBreName();
            this.Shared(Bre_Name + " - 批次作業(選取功能)");
            if (string.IsNullOrEmpty(model.Search?.WhereGAME_NO))
            {
                return RedirectToAction("GameIndex");
            }

            int ApplyCash = gameService.GetApplyCash(model.Search?.WhereGAME_NO, ref db);

            TempData["StatusMessage"] = $"提醒您，新增人員資料後會同時給予報名點數，並同時幫此參與人員報到成功，目前報到成功，將獲得點數{ApplyCash}點。請確定報名人員獲得點數是否正確。";

            ViewBag.BatchWorkTypeItem = this.gameService.GetBatchWorkTypeItem();
            return View(model);
        }

        /// <summary>
        /// 批次報名(有e酷幣帳號)
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [CheckPermissionSeeion(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult BatchApplyStudentView(BatchApplyStudentViewModel model)
        {
            this.GetBreName();
            this.Shared(Bre_Name + $" - { GameService.BatchWorkTypeVal.GetDesc(GameService.BatchWorkTypeVal.BatchApplyStudent)}");
            if (string.IsNullOrEmpty(model.Search?.WhereGAME_NO))
            {
                return RedirectToAction("GameIndex");
            }
            return View(model);
        }

        /// <summary>
        /// 批次報名(有e酷幣帳號)
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [CheckPermissionSeeion(CheckACTION_ID = "Index")] //檢查權限
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult BatchApplyStudentSave(BatchApplyStudentViewModel model)
        {
            this.GetBreName();
            this.Shared(Bre_Name + $" - { GameService.BatchWorkTypeVal.GetDesc(GameService.BatchWorkTypeVal.BatchApplyStudent)}");

            if (model == null) model = new BatchApplyStudentViewModel();
            if (model.Search == null) model.Search = new GameSearchViewModel();
            if (string.IsNullOrEmpty(model.Search?.WhereGAME_NO))
            {
                return RedirectToAction("GameIndex");
            }

            string Message = string.Empty;

            if (ModelState.IsValid == false)
            {
                Message = "警告!!輸入的內容有錯誤";
            }
            else
            {
                BatchApplyStudentResultViewModel viewModel = new BatchApplyStudentResultViewModel();
                bool OK = this.gameService.SaveBatchApplyStudent(model, user, ref db, ref Message, ref viewModel);

                if (OK)
                {
                    ModelState.Clear();
                    TempData["StatusMessage"] = "批次報名完成";
                    return View("BatchApplyStudentResult", viewModel);
                }
            }

            TempData["StatusMessage"] = Message;

#if DEBUG
            var errorsaa = ModelState
                        .Where(x => x.Value.Errors.Count > 0)
                        .Select(x => new { x.Key, x.Value.Errors })
                        .ToArray();
#endif

            return View("BatchApplyStudentView", model);
        }

        /// <summary>
        /// 批次報名(有e酷幣帳號)
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [CheckPermissionSeeion(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult BatchApplyStudentResult(BatchApplyStudentResultViewModel model)
        {
            this.GetBreName();
            this.Shared(Bre_Name + $" - { GameService.BatchWorkTypeVal.GetDesc(GameService.BatchWorkTypeVal.BatchApplyStudent)}");
            if (string.IsNullOrEmpty(model.Search?.WhereGAME_NO))
            {
                return RedirectToAction("GameIndex");
            }
            return View(model);
        }

        /// <summary>
        /// 單筆報名(卡片)
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [CheckPermissionSeeion(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult OneApplyCardView(OneApplyCardViewModel model)
        {
            this.GetBreName();
            this.Shared(Bre_Name + $" - 訪客新增(臨時卡)");

            if (string.IsNullOrEmpty(model.Search.WhereGAME_NO))
            {
                return RedirectToAction("Index");
            }
            return View(model);
        }

        /// <summary>
        /// 單筆報名(卡片) Save
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [CheckPermissionSeeion(CheckACTION_ID = "Index")] //檢查權限
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult OneApplyCardSave(OneApplyCardViewModel model)
        {
            this.GetBreName();
            this.Shared(Bre_Name + $" - 訪客新增(臨時卡)");
            if (model == null) model = new OneApplyCardViewModel();
            if (model.Search == null) model.Search = new GameSearchViewModel();

            if (string.IsNullOrEmpty(model.Search?.WhereGAME_NO))
            {
                return RedirectToAction("GameIndex");
            }

            string Message = string.Empty;

            if (ModelState.IsValid == false)
            {
                Message = "警告!!輸入的內容有錯誤\r\n";
            }
            else
            {
                BatchApplyStudentResultViewModel viewModel = new BatchApplyStudentResultViewModel();
                viewModel.BatchWorkTypeVal = GameService.BatchWorkTypeVal.AddApplyCard;
                bool OK = this.gameService.SaveOneApplyCard(model, user, ref db, ref Message, ref viewModel);

                ModelState.Clear();
                if (OK)
                {
                    TempData["StatusMessage"] = "新增及報名成功";
                    return View("BatchApplyCardResult", viewModel);
                }
                else
                {
                    TempData["StatusMessage"] = Message;

                    if (viewModel.NG_LIST?.Count > 0)
                    {
                        return View("BatchApplyCardResult", viewModel);
                    }
                    else
                    {
                        return View("BatchApplyCardView", model);
                    }
                }
            }

            TempData["StatusMessage"] = Message;

#if DEBUG
            var errorsaa = ModelState
                        .Where(x => x.Value.Errors.Count > 0)
                        .Select(x => new { x.Key, x.Value.Errors })
                        .ToArray();
#endif

            return View("OneApplyCardView", model);
        }

        /// <summary>
        /// 人員編輯列表
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ActionResult EditApplyCard(BatchCashIntoIndexViewModel model)
        {
            this.GetBreName();
            this.Shared(Bre_Name + $" -人員編輯列表");

            if (string.IsNullOrEmpty(model.Search?.WhereGAME_NO))
            {
                return RedirectToAction("GameIndex");
            }

            return View(model);
        }

        /// <summary>
        /// 人員編輯列表 PartialView
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [CheckPermissionSeeion(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult _PageEditApplyCard(BatchCashIntoIndexViewModel model)
        {
            this.GetBreName();
            this.Shared();
            model = GetPageEditApplyCard(model);
            return PartialView(model);
        }

        /// <summary>
        /// 人員編輯
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ActionResult EditPerson(GameEditPersonViewModel model)
        {
            this.GetBreName();
            this.Shared(Bre_Name + $" -人員編輯");

            if (string.IsNullOrEmpty(model.Search?.WhereGAME_NO))
            {
                return RedirectToAction("GameIndex");
            }

            model = this.gameService.GetEditPersonData(model, ref db);

            return View(model);
        }

        /// <summary>
        /// 人員編輯 Save
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [CheckPermissionSeeion(CheckACTION_ID = "Index")] //檢查權限
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult EditPersonSave(GameEditPersonViewModel model)
        {
            this.GetBreName();
            this.Shared(Bre_Name + " - 人員編輯 ");

            if (model == null) model = new GameEditPersonViewModel();
            if (model.Search == null) model.Search = new GameSearchViewModel();

            string Message = string.Empty;

            if (ModelState.IsValid == false)
            {
                Message = "警告!!輸入的內容有錯誤";
            }
            else
            {
                bool OK = this.gameService.SaveEditPersonData(model, user, ref db, ref Message);

                if (OK)
                {
                    ModelState.Clear();
                    model.Search.WhereTEMP_USER_ID = null;
                    TempData["StatusMessage"] = "儲存完成";
                    BatchCashIntoIndexViewModel viewModel = new BatchCashIntoIndexViewModel
                    {
                        Search = model.Search
                    };
                    this.GetBreName();
                    this.Shared();
                    return View("EditApplyCard", viewModel);
                }
            }

            TempData["StatusMessage"] = Message;

#if DEBUG
            var errorsaa = ModelState
                        .Where(x => x.Value.Errors.Count > 0)
                        .Select(x => new { x.Key, x.Value.Errors })
                        .ToArray();
#endif

            return View("EditPerson", model);
        }

        /// <summary>
        /// 人員編輯 刪除
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ActionResult DelPerson(GameEditPersonViewModel model)
        {
            this.GetBreName();
            this.Shared(Bre_Name + " - 人員編輯 ");

            if (model == null) model = new GameEditPersonViewModel();
            if (model.Search == null) model.Search = new GameSearchViewModel();

            string Message = string.Empty;

            if (ModelState.IsValid == false)
            {
                Message = "警告!!輸入的內容有錯誤";
            }
            else
            {
                bool OK = this.gameService.SaveDelPersonDeleteData(model, ref db, ref Message);

                if (OK)
                {
                    ModelState.Clear();
                    model.Search.WhereTEMP_USER_ID = null;
                    TempData["StatusMessage"] = "刪除完成";
                }
            }

            if (!string.IsNullOrWhiteSpace(Message))
            {
                TempData["StatusMessage"] = Message;

#if DEBUG
                var errorsaa = ModelState
                            .Where(x => x.Value.Errors.Count > 0)
                            .Select(x => new { x.Key, x.Value.Errors })
                            .ToArray();
#endif
            }

            BatchCashIntoIndexViewModel viewModel = new BatchCashIntoIndexViewModel
            {
                Search = model.Search,
                CashSearch = model.CashSearch,
            };
            this.GetBreName();
            this.Shared();
            viewModel = GetPageEditApplyCard(viewModel);
            return View("EditApplyCard", viewModel);
        }

        /// <summary>
        /// 人員編輯 停止使用
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ActionResult DisablePerson(GameEditPersonViewModel model)
        {
            this.GetBreName();
            this.Shared(Bre_Name + " - 人員編輯 ");

            if (model == null) model = new GameEditPersonViewModel();
            if (model.Search == null) model.Search = new GameSearchViewModel();

            string Message = string.Empty;

            if (ModelState.IsValid == false)
            {
                Message = "警告!!輸入的內容有錯誤";
            }
            else
            {
                bool OK = this.gameService.SaveDisablePersonData(model, ref db, ref Message);

                if (OK)
                {
                    ModelState.Clear();
                    model.Search.WhereTEMP_USER_ID = null;
                    TempData["StatusMessage"] = "停止使用完成";
                }
            }

            if (!string.IsNullOrWhiteSpace(Message))
            {
                TempData["StatusMessage"] = Message;

#if DEBUG
                var errorsaa = ModelState
                            .Where(x => x.Value.Errors.Count > 0)
                            .Select(x => new { x.Key, x.Value.Errors })
                            .ToArray();
#endif
            }

            BatchCashIntoIndexViewModel viewModel = new BatchCashIntoIndexViewModel
            {
                Search = model.Search,
                CashSearch = model.CashSearch,
            };
            this.GetBreName();
            this.Shared();
            viewModel = GetPageEditApplyCard(viewModel);
            return View("EditApplyCard", viewModel);
        }

        /// <summary>
        /// 人員編輯 啟用使用
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ActionResult EnablePerson(GameEditPersonViewModel model)
        {
            this.GetBreName();
            this.Shared(Bre_Name + " - 人員編輯 ");

            if (model == null) model = new GameEditPersonViewModel();
            if (model.Search == null) model.Search = new GameSearchViewModel();

            string Message = string.Empty;

            if (ModelState.IsValid == false)
            {
                Message = "警告!!輸入的內容有錯誤";
            }
            else
            {
                bool OK = this.gameService.SaveEnablePersonData(model, ref db, ref Message);

                if (OK)
                {
                    ModelState.Clear();
                    model.Search.WhereTEMP_USER_ID = null;
                    TempData["StatusMessage"] = "啟用使用完成";
                }
            }
            if (!string.IsNullOrWhiteSpace(Message))
            {
                TempData["StatusMessage"] = Message;

#if DEBUG
                var errorsaa = ModelState
                            .Where(x => x.Value.Errors.Count > 0)
                            .Select(x => new { x.Key, x.Value.Errors })
                            .ToArray();
#endif
            }

            BatchCashIntoIndexViewModel viewModel = new BatchCashIntoIndexViewModel
            {
                Search = model.Search,
                CashSearch = model.CashSearch,
            };
            this.GetBreName();
            this.Shared();
            viewModel = GetPageEditApplyCard(viewModel);
            return View("EditApplyCard", viewModel);
        }

        /// <summary>
        /// 批次報名(卡片)
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [CheckPermissionSeeion(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult BatchApplyCardView(BatchApplyCardViewModel model)
        {
            this.GetBreName();
            this.Shared(Bre_Name + $" - { GameService.BatchWorkTypeVal.GetDesc(GameService.BatchWorkTypeVal.BatchApplyCard)}");

            if (string.IsNullOrEmpty(model.Search.WhereGAME_NO))
            {
                return RedirectToAction("Index");
            }
            return View(model);
        }

        public ActionResult ExportTeacherExcel()
        {
            this.GetBreName();
            this.Shared();

            string sSQL = $@"Select a.NAME ,'老師' GAME_USER_TYPE_DESC,isnull(a.CARD_NO,'') CARD_NO,isnull(a.SEX,1) SEX,isnull(a.PHOTO,'') PHOTO,isnull(b.SHORT_NAME,'') SHORT_NAME
                             ,null GRADE,'' CLASS_NO,'' SEAT_NO
                             from HRMT01 a (nolock)
                             join BDMT01 b (nolock) on a.SCHOOL_NO=b.SCHOOL_NO
                             where a.SCHOOL_NO=@SCHOOL_NO and a.USER_TYPE='{UserType.Teacher}' and a.USER_STATUS<>{UserStaus.Invalid} and NAME not like '%運動%'";

            var Temp = db.Database.Connection.Query<BatchExportTeacherExcelModel>(sSQL
              , new
              {
                  SCHOOL_NO
              }).ToList();

            DataTable DataTableExcel = Temp.AsDataTable();

            NPOIHelper npoi = new NPOIHelper();
            string TempleteFileFullName = Server.MapPath(@"~/Content/ExcelSample/GameBatchApplyCardSample.xlsx");

            FileStream xlsfile = new FileStream(TempleteFileFullName, FileMode.Open, FileAccess.Read);
            IWorkbook aBook = WorkbookFactory.Create(xlsfile);

            npoi.ExportExcelFromTemplete(DataTableExcel, aBook, "stud_data", false, 2);

            string strTMPFile = System.Web.Configuration.WebConfigurationManager.AppSettings["FilePath"] + @"\TMP\" + DateTime.Now.Date.ToString("yyyyMMdd");

            if (Directory.Exists(strTMPFile) == false)
            {
                Directory.CreateDirectory(strTMPFile);
            }

            strTMPFile = strTMPFile + @"\GameTeacherExcel_" + Session.SessionID + ".xlsx";

            System.IO.FileStream FS = new FileStream(strTMPFile, FileMode.Create);
            aBook.Write(FS);
            FS.Close();

            return File(strTMPFile, "application/vnd.ms-excel", "GameTeacherExcel.xlsx");//輸出檔案給Client端
        }

        /// <summary>
        /// 批次報名(卡片)
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [CheckPermissionSeeion(CheckACTION_ID = "Index")] //檢查權限
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult BatchApplyCardSave(BatchApplyCardViewModel model)
        {
            this.GetBreName();
            this.Shared(Bre_Name + $" - { GameService.BatchWorkTypeVal.GetDesc(GameService.BatchWorkTypeVal.BatchApplyCard)}");
            if (model == null) model = new BatchApplyCardViewModel();
            if (model.Search == null) model.Search = new GameSearchViewModel();

            if (string.IsNullOrEmpty(model.Search?.WhereGAME_NO))
            {
                return RedirectToAction("GameIndex");
            }

            string Message = string.Empty;

            if (model.UploadBatchCardFile == null || model.UploadBatchCardFile?.ContentLength == 0)
            {
                Message = "請上傳報名EXCEL";
                ModelState.AddModelError(string.Empty, Message);
            }
            else if (model.UploadBatchCardFile != null)
            {
                Regex regexCode = new Regex(@".*\.(xls|xlsx)");

                string fileNameExtension = Path.GetExtension(model.UploadBatchCardFile.FileName);

                if (regexCode.IsMatch(fileNameExtension.ToLower()) == false)
                {
                    Message = "請上傳Excel格式為xls、xlsx";
                    ModelState.AddModelError(string.Empty, Message);
                }
            }

            if (ModelState.IsValid == false)
            {
                Message = "警告!!輸入的內容有錯誤\r\n";
            }
            else
            {
                BatchApplyStudentResultViewModel viewModel = new BatchApplyStudentResultViewModel();
                bool OK = this.gameService.SaveBatchApplyCard(model, user, ref db, ref Message, ref viewModel);
                ModelState.Clear();
                if (OK)
                {
                    TempData["StatusMessage"] = "匯入及報名成功";
                    return View("BatchApplyCardResult", viewModel);
                }
                else
                {
                    TempData["StatusMessage"] = Message;

                    if (viewModel != null && viewModel.NG_LIST?.Count > 0)
                    {
                        return View("BatchApplyCardResult", viewModel);
                    }
                    else
                    {
                        return View("BatchApplyCardView", model);
                    }
                }
            }

            TempData["StatusMessage"] = Message;

#if DEBUG
            var errorsaa = ModelState
                        .Where(x => x.Value.Errors.Count > 0)
                        .Select(x => new { x.Key, x.Value.Errors })
                        .ToArray();
#endif

            return View("BatchApplyCardView", model);
        }

        /// <summary>
        /// 批次報名(卡片)
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [CheckPermissionSeeion(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult BatchApplyCardResult(BatchApplyStudentResultViewModel model)
        {
            if (string.IsNullOrEmpty(model.Search?.WhereGAME_NO))
            {
                return RedirectToAction("GameIndex");
            }

            return View(model);
        }

        [HttpPost]
        public JsonResult GetSchoolName(string Prefix)
        {
            using (TransactionScope ts = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions() { IsolationLevel = IsolationLevel.ReadUncommitted }))
            {
                var SchoolNameList = (from N in db.BDMT01
                                      where N.SHORT_NAME.StartsWith(Prefix)
                                      select new { N.SHORT_NAME });

                return Json(SchoolNameList, JsonRequestBehavior.AllowGet);
            }
        }

        private BatchCashIntoIndexViewModel GetPageEditApplyCard(BatchCashIntoIndexViewModel model)
        {
            if (model == null) model = new BatchCashIntoIndexViewModel();
            if (model.Search == null) model.Search = new GameSearchViewModel();
            if (model.CashSearch == null) model.CashSearch = new BatchCashIntoSearchViewModel();

            //if (string.IsNullOrWhiteSpace(model.CashSearch.WhereSCHOOL_NO))
            //{
            //    model.CashSearch.WhereSCHOOL_NO = SCHOOL_NO;
            //}
            var SchoolNoSelectItem = BDMT01Service.GetSCHOOL_NO_ALL_Not_P(model.CashSearch.WhereSCHOOL_NO, null);
            ViewBag.SchoolNoSelectItem = SchoolNoSelectItem;

            var ClassItems = HRMT01.GetClassListData(model.CashSearch.WhereSCHOOL_NO, model.CashSearch.WhereGRADE?.ToString(), model.CashSearch.WhereCLASS_NO, ref db);
            ViewBag.ClassItems = ClassItems;

            model = this.gameService.GetBatchCashIntoData(model, ref db);
            return model;
        }

        #endregion 人員管理

        #region 餘額轉入

        /// <summary>
        /// 報名管理/餘額轉入
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [CheckPermissionSeeion(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult CashIntoView(BatchCashIntoIndexViewModel model)
        {
            this.GetBreName();
            this.Shared(Bre_Name + $" -餘額轉入");

            if (string.IsNullOrEmpty(model.Search?.WhereGAME_NO))
            {
                return RedirectToAction("GameIndex");
            }

            return View(model);
        }

        public ActionResult _PageCashIntoToExcel(BatchCashIntoIndexViewModel model)
        {
            this.GetBreName();
            this.Shared();

            if (model == null) model = new BatchCashIntoIndexViewModel();
            if (model.Search == null) model.Search = new GameSearchViewModel();
            if (model.CashSearch == null) model.CashSearch = new BatchCashIntoSearchViewModel();

            var SchoolNoSelectItem = BDMT01Service.GetSCHOOL_NO_ALL_Not_P(model.CashSearch.WhereSCHOOL_NO, null);
            ViewBag.SchoolNoSelectItem = SchoolNoSelectItem;

            var ClassItems = HRMT01.GetClassListData(model.CashSearch.WhereSCHOOL_NO, model.CashSearch.WhereGRADE?.ToString(), model.CashSearch.WhereCLASS_NO, ref db);
            ViewBag.ClassItems = ClassItems;

            var UserTupeItems = UserType.GetSelectItem(model.CashSearch.WhereGAME_USER_TYPE);
            ViewBag.UserTupeItems = UserTupeItems;
            BatchCashIntroToExcel model2 = new BatchCashIntroToExcel();
            model2 = this.gameService.GetBatchCashIntoDatatoExcel(model, ref db);
            return PartialView(model2);
        }

        /// <summary>
        /// 報名管理/餘額轉入 PartialView
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [CheckPermissionSeeion(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult _PageCashInto(BatchCashIntoIndexViewModel model)
        {
            this.GetBreName();
            this.Shared();

            if (model == null) model = new BatchCashIntoIndexViewModel();
            if (model.Search == null) model.Search = new GameSearchViewModel();
            if (model.CashSearch == null) model.CashSearch = new BatchCashIntoSearchViewModel();

            var SchoolNoSelectItem = BDMT01Service.GetSCHOOL_NO_ALL_Not_P(model.CashSearch.WhereSCHOOL_NO, null);
            ViewBag.SchoolNoSelectItem = SchoolNoSelectItem;

            var ClassItems = HRMT01.GetClassListData(model.CashSearch.WhereSCHOOL_NO, model.CashSearch.WhereGRADE?.ToString(), model.CashSearch.WhereCLASS_NO, ref db);
            ViewBag.ClassItems = ClassItems;

            var UserTupeItems = UserType.GetSelectItem(model.CashSearch.WhereGAME_USER_TYPE);
            ViewBag.UserTupeItems = UserTupeItems;

            model = this.gameService.GetBatchCashIntoData(model, ref db);

            return PartialView(model);
        }

        /// <summary>
        /// 餘額轉入 轉入 選取 Checkbox
        /// </summary>
        /// <param name="Item"></param>
        /// <returns></returns>
        [CheckPermissionSeeion(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult _CheckboxCashInto(BatchCashIntoCheckBoxViewModel Item)
        {
            return PartialView(Item);
        }

        /// <summary>
        /// 餘額轉入 刪除 資料
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [CheckPermissionSeeion(CheckACTION_ID = "Index")] //檢查權限
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult DelApplyData(BatchCashIntoIndexViewModel model)
        {
            this.GetBreName();
            this.Shared(Bre_Name + $" - 餘額轉入");
            if (model == null) model = new BatchCashIntoIndexViewModel();
            if (model.Search == null) model.Search = new GameSearchViewModel();
            if (model.CashSearch == null) model.CashSearch = new BatchCashIntoSearchViewModel();

            if (string.IsNullOrEmpty(model.Search?.WhereGAME_NO))
            {
                return RedirectToAction("GameIndex");
            }

            string Message = string.Empty;

            if (model.CheckBox.Where(a => a.Chk == true).Count() == 0)
            {
                Message = "無選取任何資料";
                ModelState.AddModelError(string.Empty, Message);
            }

            if (ModelState.IsValid == false)
            {
                Message = "警告!!輸入的內容有錯誤<br/>" + Message;
            }
            else
            {
                bool OK = this.gameService.SaveDelApplyData(model, ref db, ref Message);

                if (OK)
                {
                    ModelState.Clear();
                    TempData["StatusMessage"] = "選取的報名資料己刪除";

                    BatchCashIntoIndexViewModel viewModel = new BatchCashIntoIndexViewModel();
                    if (model.Search == null) model.Search = new GameSearchViewModel();
                    if (model.CashSearch == null) model.CashSearch = new BatchCashIntoSearchViewModel();
                    viewModel.Search = model.Search;
                    viewModel.CheckBox = null;
                    return View("CashIntoView", viewModel);
                }
            }

            TempData["StatusMessage"] = Message;

#if DEBUG
            var errorsaa = ModelState
                        .Where(x => x.Value.Errors.Count > 0)
                        .Select(x => new { x.Key, x.Value.Errors })
                        .ToArray();
#endif

            return View("CashIntoView", model);
        }

        /// <summary>
        /// 資料餘額 轉入處理
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [CheckPermissionSeeion(CheckACTION_ID = "Index")] //檢查權限
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult CashIntoData(BatchCashIntoIndexViewModel model)
        {
            this.GetBreName();
            this.Shared(Bre_Name + $" - 餘額轉入");

            if (model == null) model = new BatchCashIntoIndexViewModel();
            if (model.Search == null) model.Search = new GameSearchViewModel();
            if (model.CashSearch == null) model.CashSearch = new BatchCashIntoSearchViewModel();

            if (string.IsNullOrEmpty(model.Search?.WhereGAME_NO))
            {
                return RedirectToAction("GameIndex");
            }

            string Message = string.Empty;

            if (model.CheckBox.Where(a => a.Chk == true).Count() == 0)
            {
                Message = "無選取任何資料";
                ModelState.AddModelError(string.Empty, Message);
            }

            if (ModelState.IsValid == false)
            {
                Message = "警告!!輸入的內容有錯誤<br/>" + Message;
            }
            else
            {
                bool OK = this.gameService.SaveCashIntoData(model, user, ref db, ref Message);

                if (OK)
                {
                    ModelState.Clear();
                    TempData["StatusMessage"] = "選取的資料餘額己轉入";

                    BatchCashIntoIndexViewModel viewModel = new BatchCashIntoIndexViewModel();
                    if (model.Search == null) model.Search = new GameSearchViewModel();
                    if (model.CashSearch == null) model.CashSearch = new BatchCashIntoSearchViewModel();
                    viewModel.Search = model.Search;
                    viewModel.CheckBox = null;
                    return View("CashIntoView", viewModel);
                }
            }

            TempData["StatusMessage"] = Message;

#if DEBUG
            var errorsaa = ModelState
                        .Where(x => x.Value.Errors.Count > 0)
                        .Select(x => new { x.Key, x.Value.Errors })
                        .ToArray();
#endif

            return View("CashIntoView", model);
        }

        #endregion 餘額轉入

        #region 現況查詢(後台)

        /// <summary>
        /// 現況查詢(後台)
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>

        [CheckPermissionSeeion(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult ShowPersonCashLogInto(GamePersonIntoViewModel model)
        {
            this.GetBreName();
            this.Shared();
            if (model == null) model = new GamePersonIntoViewModel();

            model = this.gameService.GetPersonStatusInto(model, ref db);

            return View(model);
        }

        /// <summary>
        /// 查詢活動闖關紀錄 手機/網頁版
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public ActionResult QueryUserGameData(String GAME_NO, String TEMP_USER_ID)
        {
            string Message = string.Empty;
            string GUSER_NO = "";
            string GCARDNO = "";
            var Game = gameService.GetIsGAME(GAME_NO, ref db, ref Message);
            if (!string.IsNullOrWhiteSpace(Message))
            {
                TempData["GameName"] = Game?.GAME_NAME;
                return RedirectToAction("ErrGame", new GameErrorViewModel { Error = Message });
            }

            GameCashQueryViewModel model = new GameCashQueryViewModel();
            model.GAME_NO = GAME_NO;
            model.GameInfo = Game;
            ViewBag.TEMP_USER_ID = "";
            ViewBag.GUSER_NO = "";
            if (!string.IsNullOrWhiteSpace(TEMP_USER_ID))
            {
                model.User = db.ADDT27.Where(x => x.TEMP_USER_ID == TEMP_USER_ID).FirstOrDefault();
                ViewBag.TEMP_USER_ID = model.User.GAME_USER_ID;
                ViewBag.GUSER_NO = model.User.USER_NO;
            }
            var SchoolNoSelectItem = BDMT01Service.GetSCHOOL_NO_ALL_Not_P(null, null);
            ViewBag.SchoolNoSelectItem = SchoolNoSelectItem;

            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult QueryUserGameData(GameCashQueryViewModel model)
        {
            string Message = string.Empty;

            var Game = gameService.GetIsGAME(model.GAME_NO, ref db, ref Message);
            if (!string.IsNullOrWhiteSpace(Message))
            {
                TempData["GameName"] = Game?.GAME_NAME;
                return RedirectToAction("ErrGame", new GameErrorViewModel { Error = Message });
            }

            model.GAME_NO = model.GAME_NO;
            model.GameInfo = Game;

            var SchoolNoSelectItem = BDMT01Service.GetSCHOOL_NO_ALL_Not_P(model.CashSearch.WhereSCHOOL_NO, null);
            ViewBag.SchoolNoSelectItem = SchoolNoSelectItem;
            // 驗證
            if (string.IsNullOrWhiteSpace(model.GameUserID)
                && (model.CashSearch.WhereSCHOOL_NO == "ALL" || string.IsNullOrWhiteSpace(model.CashSearch.WhereUSER_NO)))
            {
                TempData["StatusMessage"] = "請輸入查詢的學校及學號或臨時卡卡號。";
                return View(model);
            }

            // 是用學校和學號查詢 => 得到卡號
            if (!(model.CashSearch.WhereSCHOOL_NO == "ALL" || string.IsNullOrWhiteSpace(model.CashSearch.WhereUSER_NO)))
            {
                String card_no = db.HRMT01.Where(h => h.SCHOOL_NO == model.CashSearch.WhereSCHOOL_NO && h.USER_NO == model.CashSearch.WhereUSER_NO)
                .FirstOrDefault()?.CARD_NO;
                // 驗證
                if (card_no == null)
                {
                    TempData["StatusMessage"] = "此學號查詢不到卡號。";
                    return View(model);
                }
                model.GameUserID = card_no;
            }
            ViewBag.TEMP_USER_ID = "";
            ViewBag.GUSER_NO = "";
            if (model.User != null && !string.IsNullOrWhiteSpace(model.User.TEMP_USER_ID))
            {
                model.User = db.ADDT27.Where(x => x.TEMP_USER_ID == model.User.TEMP_USER_ID).FirstOrDefault();
                ViewBag.TEMP_USER_ID = model.User.TEMP_USER_ID;
                ViewBag.GUSER_NO = model.User.USER_NO;
            }
            model = this.gameService.CashQueryData(model, ref db, ref Message, true);
            model.FromSourcePage = GameCashQueryViewModel.SourcePage.QueryUserGameData;

            if (Message != string.Empty)
            {
                TempData["StatusMessage"] = Message;
                return View(model);
            }

            return View("CashQueryDataList", model);
        }

        #endregion 現況查詢(後台)
        public void CheckInput1(GameEditViewModel model) {

            if (model.Details.Count == 0)
            {
                ModelState.AddModelError("DetailsError", "*未輸入獎品資料");
            }
            else {
                if (model.PrizeDetails.Count() == 0)
                {

                    ModelState.AddModelError("PrizeDetailsError", "*未輸入抽獎品資料");
                }
                else {

                    if (model.PrizeDetails.Where(x => x.Y_CASH == true && x.PrizeName.All(char.IsDigit) == false && x.LEVEL_NO=="1").Any())
                    {
                        ModelState.AddModelError("PrizeDetailsError", "*勾選為酷幣，輸入資料要為數字");

                    }
                    if (model.PrizeDetails.Where(x => x.Y_CASH == true && x.PrizeName.All(char.IsDigit) == false && x.LEVEL_NO == "2").Any())
                    {
                        ModelState.AddModelError("PrizeDetails1Error", "*勾選為酷幣，輸入資料要為數字");

                    }
                }
             


            }

        }
        #region 驗証

        /// <summary>
        /// 活動驗証
        /// </summary>
        public void CheckInput(GameEditViewModel model)
        {
            if (model.Details.Count == 0)
            {
                ModelState.AddModelError("DetailsError", "*未輸入關卡資料");
            }
            else
            {
                var IsApply_Data = model.Details.Where(a => a.IsApply == true);

                if (model.Details.Where(a => a.LEVEL_TYPE == ADDT26_D.LevelType.Apply && a.CASH == null).Any())
                {
                    ModelState.AddModelError("ApplyError", "*【報名關卡】請輸入酷幣點數");
                }

                if (model.GAME_TYPE == (byte)ADDT26.GameType.一般)
                {
                    var Grouphaving = model.Details.GroupBy(c => c.LEVEL_NAME).Where(grp => grp.Count() > 1).Select(grp => grp.Key);
                    if (Grouphaving.Count() > 0)
                    {
                        ModelState.AddModelError("DetailsError", "*【關卡名稱】不可重覆");
                    }

                    if (model.Details.Where(a => a.LEVEL_TYPE == ADDT26_D.LevelType.Pay).Count() == 0)
                    {
                        ModelState.AddModelError("DetailsError", "*未輸入關卡資料");
                    }
                    if (model.Details.Where(a => a.LEVEL_TYPE == ADDT26_D.LevelType.ItemPrize).Count() > 0)
                    {
                        if (model.PrizeDetails == null)
                        {
                            ModelState.AddModelError("PrizeDetailsError", "*未輸入抽獎品資料");
                        }
                    }
                }
                else
                {
                    if ((model.DetailsQA?.Count() ?? 0) == 0)
                    {
                        ModelState.AddModelError("DetailsQAError", "*未輸入題目資料");
                    }
                }
            }
        }

        #endregion 驗証

        #region 統計表 -一般活動

        [CheckPermissionSeeion(CheckACTION_ID = "Index")] //檢查權限
        /// <summary>
        /// 統計表
        /// </summary>
        /// <returns></returns>
        public ActionResult Statistics(GameStatisticViewModel model)
        {
            this.GetBreName();
            this.Shared();

            if (string.IsNullOrEmpty(model.Search?.WhereGAME_NO))
            {
                return RedirectToAction("GameIndex");
            }

            return View(model);
        }

        /// <summary>
        /// 統計表 關卡圖表
        /// </summary>
        /// <param name="GAME_NO"></param>
        /// <param name="IsStatistics"></param>
        /// <returns></returns>

        [CheckPermissionSeeion(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult _LevelPersonCount(string GAME_NO, bool IsStatistics = false)
        {
            this.GetBreName();
            this.Shared();
            var model = this.gameService.GetLevelPersonCountData(GAME_NO, IsStatistics, ref db);
            return PartialView(model);
        }

        /// <summary>
        /// 統計表 人員圖表
        /// </summary>
        /// <param name="GAME_NO"></param>
        /// <param name="Level_Count"></param>
        /// <param name="LEVEL_NO"></param>
        /// <returns></returns>
        public ActionResult LevelPersonView(string GAME_NO, double? Level_Count, string LEVEL_NO = null, string LEVEL_TYPE = null)
        {
            this.GetBreName();
            this.Shared();
            var model = this.gameService.GetLevelPersonDetailsData(GAME_NO, ref db, Level_Count, LEVEL_NO, LEVEL_TYPE);
            return PartialView(model);
        }

        #endregion 統計表 -一般活動

        #region 有獎徵答-統計分析

        [CheckPermissionSeeion(CheckACTION_ID = "Index")] //檢查權限
        /// <summary>
        /// 有獎徵答-統計分析
        /// </summary>
        /// <returns></returns>
        public ActionResult StatisticsAns(GameStatisticsAnsViewModel model)
        {
            this.GetBreName();
            this.Shared(Bre_Name + $" -統計分析");
            if (model == null) model = new GameStatisticsAnsViewModel();
            if (model.Search == null) model.Search = new GameSearchViewModel();

            if (string.IsNullOrEmpty(model.Search?.WhereGAME_NO))
            {
                return RedirectToAction("GameIndex");
            }

            model = this.gameService.GetStatisticsAnsData(model, ref db);
            return View(model);
        }

        /// <summary>
        /// 有獎徵答-統計分析 - 答此答案的人資訊
        /// </summary>
        /// <param name="GAME_NO"></param>
        /// <param name="GROUP_ID"></param>
        /// <param name="LEVEL_NO"></param>
        /// <returns></returns>
        public ActionResult TotalGraphDetails(string GAME_NO, string GROUP_ID, string LEVEL_NO, bool IsSeeGroupId)
        {
            this.GetBreName();
            this.Shared();

            var model = this.gameService.GetStatisticsAnsData(GAME_NO, GROUP_ID, LEVEL_NO, IsSeeGroupId, ref db);

            if (IsSeeGroupId == true && (model.GameInfo == null || model.AnsQInfo == null || model.AnsInfo == null))
            {
                return PartialView($@"~/Error/{ErrorHelper.ErrorVal.NotFindError}");
            }
            else if (IsSeeGroupId == false && (model.GameInfo == null))
            {
                return PartialView($@"~/Error/{ErrorHelper.ErrorVal.NotFindError}");
            }

            return PartialView(model);
        }

        #endregion 有獎徵答-統計分析

        #region 有獎徵答 - 成績一覽表

        public ActionResult ScoreLists(GameScoreListsViewModel model)
        {
            this.GetBreName();
            this.Shared(Bre_Name + $" -成績一覽表");

            if (string.IsNullOrEmpty(model.Search?.WhereGAME_NO))
            {
                return RedirectToAction("GameIndex");
            }

            return View(model);
        }

        public ActionResult _ScorePersons(GameScoreListsViewModel model)
        {
            this.GetBreName();
            this.Shared();

            if (model == null) model = new GameScoreListsViewModel();
            if (model.Search == null) model.Search = new GameSearchViewModel();

            if (model.SCORE_TYPE == null) model.SCORE_TYPE = GameScoreListsViewModel.ScoreTypeVal.AllScore;

            var SchoolNoSelectItem = BDMT01Service.GetSCHOOL_NO_ALL_Not_P(model.WhereSCHOOL_NO, null);
            ViewBag.SchoolNoSelectItem = SchoolNoSelectItem;

            var ClassItems = HRMT01.GetClassListData(model.WhereSCHOOL_NO, null, model.WhereCLASS_NO, ref db);
            ViewBag.ClassItems = ClassItems;

            ViewBag.SCORE_TYPEItem = GameScoreListsViewModel.ScoreTypeVal.GetSelectListItem(model.SCORE_TYPE);

            model = this.gameService.GetScorePersonsData(model, ref db);
            return PartialView(model);
        }

        #endregion 有獎徵答 - 成績一覽表

        public void Capture()
        {
            var stream = Request.InputStream;
            string dump;

            using (var reader = new StreamReader(stream))
                dump = reader.ReadToEnd();

            var path = Server.MapPath("~/test.jpg");
            System.IO.File.WriteAllBytes(path, String_To_Bytes2(dump));
        }

        [CheckPermissionSeeion(CheckACTION_ID = "Index")] //檢查權限
        public ActionResult GameWebStatus(string GAME_NO)
        {
            var model = this.gameService.GetBackupLinkData(GAME_NO, ref db);

            return View(model);
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }

        private byte[] String_To_Bytes2(string strInput)
        {
            int numBytes = (strInput.Length) / 2;
            byte[] bytes = new byte[numBytes];

            for (int x = 0; x < numBytes; ++x)
            {
                bytes[x] = Convert.ToByte(strInput.Substring(x * 2, 2), 16);
            }

            return bytes;
        }

        private ActionResult Goto(string actionName, string controllerName)
        {
            return RedirectToAction(actionName, controllerName);
        }

        private void GetBreName()
        {
            string ThisBreNo = UserProfileHelper.GetBRE_NO();

            if (string.IsNullOrWhiteSpace(ThisBreNo) || ThisBreNo.IndexOf("Game") == -1)
            {
                Goto(ErrorHelper.ErrorVal.SessionTimeOutError, "Error");
            }

            Bre_Name = BreadcrumbService.GetBRE_NAME_forSessionBRE_NO(ThisBreNo, Bre_NO);
        }

        private void Shared(string Panel_Title = "")
        {
            ViewBag.BRE_NO = Bre_NO;

            if (!string.IsNullOrWhiteSpace(Panel_Title))
            {
                ViewBag.Panel_Title = Panel_Title;
            }

            user = UserProfileHelper.Get();
            SCHOOL_NO = UserProfileHelper.GetSchoolNo();

            if (UserProfileHelper.GetBRE_NO() == "GameAns")
            {
                GAME_TYPE = (byte)ADDT26.GameType.有獎徵答;
            }
            else
            {
                GAME_TYPE = (byte)ADDT26.GameType.一般;
            }

            ViewBag.GAME_TYPE = GAME_TYPE;

            if (user != null)
            {
                if (user.SCHOOL_NO != null)
                {
                    SCHOOL_NO = user.SCHOOL_NO;
                }
            }
        }
    }
}