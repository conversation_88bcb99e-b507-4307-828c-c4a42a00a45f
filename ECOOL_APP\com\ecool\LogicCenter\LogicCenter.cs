﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Net;
using System.Reflection;
using System.Runtime.Serialization.Formatters.Binary;
using System.Text;
using System.Threading.Tasks;
using System.Web.Mvc;
using System.Xml.Linq;

namespace ECOOL_APP.com.ecool.LogicCenter
{
    #region LogicCenter

    public enum UserRoles
    {
        上線前設定管理 = 0,
        總管理者 = 1,
        活動管理者 = 2,
        各校管理者 = 4,
        教師 = 5,
        學生 = 6,
        行政人員 = 8,
        導師 = 9,
    }


    public enum StorageGridType
    {
        資料欄位,
        日期,
        是否達標,
        總次數,
        總計
    }
    public enum CellLetter
    {
        A = 0, B, C, D, E, F, G, H, I, J, K, L, M, N, O, P, Q, R, S, T, U, V, W, X, Y, Z
    }


    public enum Gender
    {
        女生 = 0,
        男生 = 1
    }

    public enum Hash64 {

        H64= 502

    }
    public enum BookType
    {
        科學類 = 1,
        史地類,
        文學類,
        藝術類,
        其他 = 99
    }

    public class LogicCenter
    {
        /// <summary>
        /// 取得列舉Discription內容
        /// </summary>
        /// <param name="value">enum</param>
        /// <returns></returns>
        public static string GetEnumDescription(Enum value)
        {
            FieldInfo fi = value.GetType().GetField(value.ToString());

            DescriptionAttribute[] attributes =
                (DescriptionAttribute[])fi.GetCustomAttributes(
                typeof(DescriptionAttribute),
                false);

            if (attributes != null &&
                attributes.Length > 0)
                return attributes[0].Description;
            else
                return value.ToString();
        }

        /// <summary>
        /// 取得列舉名稱
        /// </summary>
        /// <typeparam name="T">型別</typeparam>
        /// <param name="e"></param>
        /// <returns></returns>
        public static string GetEnumName<T>(T e)
        {
            if (!typeof(T).IsEnum) throw new TypeAccessException("不是Enum型別");
            return Enum.GetName(typeof(T), e);
        }

        /// <summary>
        /// 列舉轉SelectList
        /// </summary>
        /// <typeparam name="T">型別</typeparam>
        /// <returns></returns>
        public static IEnumerable<SelectListItem> GetEnumSelectList<T>()
        {
            return Enum.GetValues(typeof(T)).Cast<T>().Select(
                enu => new SelectListItem() { Text = Enum.GetName(typeof(T), enu), Value = Convert.ToInt32(enu).ToString() });
        }

        /// <summary>
        /// 集合(陣列)比較算法，比較兩集合是否相同。 
        /// 前置作業: 需先行排序]。
        /// </summary>
        /// <typeparam name="L">左元素型別</typeparam>
        /// <typeparam name="R">右元素型別</typeparam>
        /// <param name="lefts">左元素陣列</param>
        /// <param name="rights">右元素陣列</param>
        /// <param name="compare">比較公式</param>
        /// <returns></returns>
        public static bool IsTheSame<L, R>(IEnumerable<L> lefts, IEnumerable<R> rights, Func<L, R, bool> compare)
        {
            if (lefts.Count() != rights.Count())
                return false;

            int i = 0, j = 0;
            foreach (var l_item in lefts)
            {
                foreach (var r_item in rights)
                {
                    if (i == j)
                    {
                        if (!compare(l_item, r_item))
                            return false;

                        i = 0;
                        j++;
                        break;
                    }
                    i++;
                }
            }
            return true;
        }

        /// <summary>
        /// 讀取xml config檔(兩層) => 轉Dictionary
        /// </summary>
        /// <param name="path">完整路徑</param>
        /// <param name="descendantsParent">父節點</param>
        /// <param name="descendantsChild">子節點</param>
        /// <returns></returns>
        public static Dictionary<string, string> LoadXML(string path, string descendantsParent, string descendantsChild)
        {
            return XDocument.Load(path).Descendants(descendantsParent).
            Descendants(descendantsChild).ToDictionary(p => p.Attribute("key").Value,
            p => p.Attribute("value").Value);
        }

        /// <summary>
        /// 複製物件
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="obj"></param>
        /// <returns></returns>
        public static T DeepClone<T>(T obj)
        {
            using (var ms = new MemoryStream())
            {
                var formatter = new BinaryFormatter();
                formatter.Serialize(ms, obj);
                ms.Position = 0;

                return (T)formatter.Deserialize(ms);
            }
        }

        /// <summary>
        /// 檢查youtube影片格式是否正確
        /// </summary>
        /// <param name="youtube_url">youtube連結</param>
        /// <param name="error">錯誤訊息</param>
        /// <returns></returns>
        public static bool YoutubeUrlConvert(ref string youtube_url, out string error)
        {
            error = "";

            if (youtube_url.IndexOf("https://www.youtube.com/embed/") == -1 )
            {
                if (youtube_url.IndexOf("https://www.youtube.com/watch?v=") == -1)
                {
                    error = "輸入 Youtube 網址 不是有效的，請輸入https://www.youtube.com/embed/影片id";
                    return false;
                }
                else
                {
                    youtube_url = youtube_url.Replace("https://www.youtube.com/watch?v=", "https://www.youtube.com/embed/");
                }
            }

            Uri urlCheck = new Uri(youtube_url);
            WebRequest request = WebRequest.Create(urlCheck);
            request.Timeout = 15000;
            WebResponse response;
            try
            {
                response = request.GetResponse();
                return true;
            }
            catch (Exception)
            {
                error = $"輸入 Youtube 網址{youtube_url} 不是有效的";
                return false;
            }
        }
        /// <summary>
        /// 公式: 目前酷幣值 + 定存酷幣值 + 每月的借書量 x 100 + 跑步里程 x 20
        ///   </summary>
        /// <param name="cash">酷幣</param>
        /// <param name="saveCash">定存</param>
        /// <param name="monthBookBorrow">每月借書</param>
        /// <param name="runKMiles">跑步里程(km)</param>
        /// <returns></returns>
        public static int CaculateMyATM_point(int cash, int saveCash, int monthBookBorrow, double runKMiles) {
            // 等級分數計算公式: 後兩者最高2000point
            int point = (int)Math.Round(
                cash +
                saveCash +
                (monthBookBorrow * 100 >= 2000 ? 2000 : monthBookBorrow * 100) +
                (runKMiles * 20 >= 2000 ? 2000 : runKMiles * 20));
            return point;
        }

        /// <summary>
        ///  計算ATM 等級 
        /// </summary>
        /// <param name="point">酷幣點數</param>
        /// <returns></returns>
        public static int CaculateMyATM_Level(int point)
        {
            int[] levelPointRange = new int[] { 0, 1001, 1501, 2501, 3001, 4001, 4501 };
           
            int myLevel = 0;
            for(int i = 0; i <= levelPointRange.Length -1 ; i++)
            {
                if (point >= levelPointRange[i]) { myLevel++; }
                else { break; }
            }
            return myLevel;
        }
    }

    #endregion

    #region 擴充方法

    public static class StringExtension
    {
        /// <summary>
        /// 字串轉Enum型別
        /// </summary>
        /// <typeparam name="T">Enum型別</typeparam>
        /// <param name="value"></param>
        /// <returns></returns>
        public static T ToEnum<T>(this string value) where T : struct, IConvertible
        {
            if (!typeof(T).IsEnum)
            {
                throw new ArgumentException("T must be an enumerated type");
            }
            return (T)Enum.Parse(typeof(T), value, true);
        }
    }

    public static class ObjectExtension
    {
        /// <summary>
        /// 任意object轉任意型別
        /// </summary>
        /// <typeparam name="T">型別</typeparam>
        /// <param name="dataObj">物件Object</param>
        /// <returns></returns>
        public static T ToType<T>(this object dataObj)
        {
            try
            {
                var underlyingType = Nullable.GetUnderlyingType(typeof(T)) ?? typeof(T); // 堤防Nullable發生Errror
                return (dataObj == DBNull.Value ? default(T) : (T)Convert.ChangeType(dataObj, underlyingType));
            }
            catch (Exception)
            {
                return default(T);
            }
        }
    }

    #endregion

}
