﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class GameMeQALevelViewModel
    {
        /// <summary>
        ///活動id
        /// </summary>
        [DisplayName("活動id")]
        public string GAME_NO { get; set; }

        /// <summary>
        ///題目序號
        /// </summary>
        [DisplayName("題目序號")]
        public string GROUP_ID { get; set; }

        /// <summary>
        ///題目描述
        /// </summary>
        [DisplayName("題目描述")]
        public string G_SUBJECT { get; set; }

        /// <summary>
        ///題目排序
        /// </summary>
        [DisplayName("題目排序")]
        public int? G_ORDER_BY { get; set; }

        public string LEVEL_NO { get; set; }

        [DisplayName("選項內容")]
        public string LEVEL_NAME { get; set; }

        /// <summary>
        ///正確答案
        /// </summary>
        [DisplayName("正確答案")]
        public bool? TRUE_ANS { get; set; }

        /// <summary>
        /// 回饋字串
        /// </summary>
        public string RETURN_DESC { get; set; }

        /// <summary>
        /// 已作答
        /// </summary>
        public bool MeIsLevelPass { get; set; }

        /// <summary>
        /// 建議作答關卡
        /// </summary>
        public bool IsRecommendLevel { get; set; }
    }
}