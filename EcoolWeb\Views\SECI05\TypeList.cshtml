﻿@model SECI05TypeListIndexViewModel
@{
    ViewBag.Title = ViewBag.Panel_Title;
    Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
}
<link href="~/Content/css/EzCss.css" rel="stylesheet" />
<link href="~/Content/colorbox/example3/colorbox.css" rel="stylesheet" />
<script src="~/Content/colorbox/jquery.colorbox.js"></script>
<script src="~/Scripts/printThis/printThis.js"></script>


@using (Html.BeginForm("TypeList", (string)ViewBag.BRE_NO, FormMethod.Post, new { id = "form1", name = "form1" }))
{

    @Html.HiddenFor(model => model.OrdercColumn)
    @Html.HiddenFor(model => model.SyntaxName)
    @Html.HiddenFor(model => model.Page)
    @Html.HiddenFor(model => model.WhereSEYEAR)
    @Html.HiddenFor(model => model.WhereSCHOOL_NO)
    @Html.HiddenFor(model => model.WhereUSER_NO)
   

    <div style="padding-top:25px;width:95%;margin:0px auto;">
        @Html.Partial("_Title_Secondary")
        @Html.Partial("_Notice")


        <div class="form-inline">
            <div class="col-xs-12 text-right">
                <button type="button" class="btn btn-sm btn-sys" onclick="PrintBooK()">我要列印</button>
            </div>
        </div>

        <div style="height:10px"></div>

        <div id="tbData" class="row">
            <div class="col-sm-12 col-xs-12">
                <table class="table-ecool table-ecool-Bule-SEC">
                    <thead>
                        <tr>

                            <th>
                                <samp class="form-group" style="cursor:pointer;" onclick="FunSort('BK_GRP')">
                                    @Html.DisplayNameFor(m => m.ListData.First().BK_GRP)
                                </samp>
                            </th>
                            <th>
                                <samp class="form-group" style="cursor:pointer;" onclick="FunSort('TYPE_NAME')">
                                    @Html.DisplayNameFor(m => m.ListData.First().TYPE_NAME)
                                </samp>
                            </th>
                            <th>
                                <samp class="form-group" style="cursor:pointer;" onclick="FunSort('QTY')">
                                    @Html.DisplayNameFor(m => m.ListData.First().QTY)
                                </samp>
                            </th>
                            <th>
                                <samp class="form-group" style="cursor:pointer;" onclick="FunSort('RATE')">
                                    @Html.DisplayNameFor(m => m.ListData.First().RATE)
                                </samp>
                            </th>
                            <th>
                                詳細資料
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        @if (Model.ListData != null)
                        {
                            foreach (var item in Model.ListData)
                            {

                                if (!string.IsNullOrWhiteSpace(item.BK_GRP) || item.QTY > 0)
                                {
                                    <tr class="text-center">
                                        <td>

                                            @Html.DisplayFor(modelItem => item.BK_GRP)

                                        </td>
                                        <td>

                                            @Html.DisplayFor(modelItem => item.TYPE_NAME)

                                        </td>
                                        <td>

                                            @Html.DisplayFor(modelItem => item.QTY)

                                        </td>
                                        <td>

                                            @item.RATE.ToString("P")

                                        </td>
                                        <td>
                                            <button type="button" class="colorbox btn btn-link-ez btn-xs" href='@Url.Action("BorrowList", (string)ViewBag.BRE_NO,new { WhereSEYEAR =Model.WhereSEYEAR,WhereBK_GRP= item.OrderBy,WhereSCHOOL_NO=Model.WhereSCHOOL_NO,WhereUSER_NO=Model.WhereUSER_NO })'>明細</button>
                                        </td>
                                    </tr>
                                }



                            }

                        }
                        else
                        {
                            <tr class="text-center">
                                <td colspan="4">無任何資料</td>
                            </tr>
                        }

                    </tbody>
                </table>
            </div>
        </div>
    </div>
}
<script language="javascript">


    $(document).ready(function () {
        $(".colorbox").colorbox({ iframe: true, width: "80%", height: "80%", opacity: 0.82 });
    });

    function PrintBooK() {
        $('#tbData').printThis();
    }



    function FunSort(SortName) {
        if (form1.OrdercColumn.value == SortName) {
            if (form1.SyntaxName.value == "Desc") {
                form1.SyntaxName.value = "ASC"
            }
            else {
                form1.SyntaxName.value = "Desc"
            }
        }
        else {
            form1.OrdercColumn.value = SortName;
            form1.SyntaxName.value = "Desc";
        }
        form1.submit();
    }



</script>