﻿/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'find', 'ja', {
	find: '検索',
	findOptions: '検索オプション',
	findWhat: '検索する文字列:',
	matchCase: '大文字と小文字を区別する',
	matchCyclic: '末尾に逹したら先頭に戻る',
	matchWord: '単語単位で探す',
	notFoundMsg: '指定された文字列は見つかりませんでした。',
	replace: '置換',
	replaceAll: 'すべて置換',
	replaceSuccessMsg: '%1 個置換しました。',
	replaceWith: '置換後の文字列:',
	title: '検索と置換'
} );
