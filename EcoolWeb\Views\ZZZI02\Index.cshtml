﻿@model IEnumerable<ECOOL_APP.com.ecool.Models.DTO.ZZT10ViewModel>
@{
    ViewBag.Title = ViewBag.Panel_Title;
}
@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")



@using (Html.BeginForm("Update", (string)ViewBag.BRE_NO, FormMethod.Post, new { name = "form1" }))
{
    
    <div class="panel panel-ACC" name="top">
        <div class="panel-heading text-center">
            @Html.BarTitle()
        </div>
        <div class="panel-Group  text-center">
            角色清單
        </div>
        <div class="panel-body">
            <div class="form-horizontal">
                    <div class="form-group">
                       
                            @{

                                string BtnClass = string.Empty;

                                foreach (var item in ViewBag.HRMT24ListItems as List<ECOOL_APP.com.ecool.Models.entity.uHRMT24>)
                                {

                                    if (ViewBag.ROLE_ID == item.ROLE_ID)
                                    {
                                        BtnClass = "btn btn-default btn-block active";
                                    }
                                    else
                                    {
                                        BtnClass = "btn btn-default btn-block";
                                    }

                                    <div class="col-md-3">
                                        @Html.ActionLink(item.ROLE_NAME, "Index", new { controller = (string)ViewBag.BRE_NO, ROLE_ID = item.ROLE_ID }, new { @class = BtnClass })
                                    </div>
                                }
                            }
                       
                    </div>
            </div>
        </div>
    </div>

    <div class="panel panel-ACC">
        <div class="panel-Group  text-center">
            權限清單
        </div>
        <div>
            <table class="table-ecool table-hover table-ecool-ACC">
                <thead>
                    <tr>
                        <th style="text-align:left">功能名稱</th>
                        <th style="text-align:left">動作</th>
                    </tr>
                </thead>
                <tbody>
                    @{
                        int num = 0;

                        foreach (var item in Model)
                        {
                            if (item.BRE_TYPE == "3") // (1.全部、2.依角色、3.標題)
                            {
                                <tr class="Group">
                                    <td colspan="2">@Html.DisplayFor(modelItem => item.BRE_NAME)</td>
                                </tr>
                            }
                            else
                            {
                                <tr style="text-align:left">
                                    <td width="30%">@Html.DisplayFor(modelItem => item.BRE_NAME)</td>
                                    <td width="70%">
                                        @{
                                            foreach (var D_item in item.Details_List)
                                            {
                                                if (D_item.ACTION_ID != "")
                                                {
                                                    string CheckBoxName = D_item.BRE_NO + '_' + D_item.ACTION_ID;

                                                    //ACTION_TYPE	類別 ALL.全部(不卡權限) ,R 依角色限制權限

                                                    if (D_item.ACTION_TYPE == "ALL" || D_item.MY_Permission_Checked == false || (ViewBag.MY_RoleID == "0" && ViewBag.ROLE_ID == "0") || ViewBag.MY_RoleID == ViewBag.ROLE_ID)
                                                    {

                                                        string titleValue;

                                                        if (ViewBag.MY_RoleID == "0" && ViewBag.ROLE_ID == "0")
                                                        {
                                                            titleValue = "您是" + ViewBag.MaxROLEName + "，擁有最大權限，無需異動。";
                                                        }
                                                        else if (ViewBag.MY_RoleID == ViewBag.ROLE_ID)
                                                        {
                                                            titleValue = "您是這個角色人員，需要比你更高權限者，才能異動你的權限。";
                                                        }
                                                        else if (D_item.ACTION_TYPE == "ALL")
                                                        {
                                                            titleValue = "此功能不限制權限，全部的使用者可使用。";
                                                        }
                                                        else
                                                        {
                                                            titleValue = "您無權異動此功能。";
                                                        }


                                                        @Html.CheckBox(CheckBoxName, D_item.Checked, new { @id = CheckBoxName, @onclick = "DbSave('" + D_item.ROLE_ID + "','" + D_item.BRE_NO + "','" + D_item.ACTION_ID + "',this.checked,this.id)", @disabled = "disabled", @title = titleValue, @class = "initialism" });
                                                        <abbr class="initialism" title="@titleValue" style="color:#A9A9A9">
                                                            @Html.DisplayFor(modelItem => D_item.ACTION_NAME, new { @title = titleValue, @class = "initialism" })
                                                        </abbr>


                                                    }
                                                    else
                                                    {
                                                        @Html.CheckBox(CheckBoxName, D_item.Checked, new { @id = CheckBoxName, @class = "initialism", @onclick = "DbSave('" + D_item.ROLE_ID + "','" + D_item.BRE_NO + "','" + D_item.ACTION_ID + "',this.checked,this.id)" })                                       ;
                                                        @Html.DisplayFor(modelItem => D_item.ACTION_NAME) <article></article>

                                                    }

                                                    @Html.HiddenFor(modelItem => D_item.ACTION_ID);
                                                    @Html.HiddenFor(modelItem => D_item.ACTION_TYPE);
                                                    @Html.HiddenFor(modelItem => D_item.BRE_NO);
                                                    @Html.HiddenFor(modelItem => D_item.ROLE_ID);

                                                }
                                            }
                                        }
                                    </td>
                                </tr>
                         }
                        num++;
                      }
                    }
                </tbody>
            </table>
        </div>
    </div>

    <div  class="form-group text-center">
        <a class="btn btn-default" href="@Url.Action("Index", (string)ViewBag.BRE_NO)#top">Top</a>
    </div>


  




}
@section Scripts {
    <script language="JavaScript">
        function DbSave(ROLE_ID_Val, BRE_NO_Val, ACTION_ID_Val, Checked_Val, ID_VAL) {

            $.ajax({
                url: "@(Url.Action("Save", (string)ViewBag.BRE_NO))",     // url位置
                type: 'post',                   // post/get
                data: {
                    ROLE_ID: ROLE_ID_Val
                    , BRE_NO: BRE_NO_Val
                    , ACTION_ID: ACTION_ID_Val
                    , Checked: Checked_Val
                },     // data
                dataType: 'json',               // xml/json/script/html
                cache: false,                   // 是否允許快取
                success: function (data) {
                    var res = jQuery.parseJSON(data);

                    if (res.Success == 'false') {
                        alert(res.Error);

                        if (Checked_Val) {
                            $('#' + ID_VAL).prop("checked", false)
                        }
                        else {
                            $('#' + ID_VAL).prop("checked", true)
                        }
                    }
                },
                error: function (xhr, err) {
                    alert("readyState: " + xhr.readyState + "\nstatus: " + xhr.status);
                    alert("responseText: " + xhr.responseText);
                }

            });

        }
    </script>
}


