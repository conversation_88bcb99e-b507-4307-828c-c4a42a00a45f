﻿using System;
using System.Collections.Generic;
using System.Collections;

//using com.ecool.model.dao;

namespace com.ecool.service
{
    /// <summary>
    /// CommService
    /// </summary>
    public class CommService : ServiceBase
    {
        /// <summary>
        /// 產品查詢 Grid 統計總筆數
        /// </summary>
        /// <param name="TABLE_NAME">資料庫名稱</param>
        public int GetGirdTotalCount(string TABLE_NAME)
        {
            string sql = @"SELECT COUNT(*) FROM " + TABLE_NAME;
            //string sql = @" SELECT ISNULL(( select sum (spart.rows) from sys.partitions spart where spart.object_id=object_id('"+ TABLE_NAME+"') and spart.index_id < 2),0) AS [RowCount]"  ;
            
            try
            {
                return (int)new sqlConnection.sqlConnection().executeQueryByDataTableList(sql).Rows[0][0];
            }
            catch
            {
            }
            return -1;
        }

        /// <summary>
        /// 產品查詢 Grid 統計總筆數
        /// </summary>
        /// <param name="TABLE_NAME">資料庫名稱</param>
        public int GetGirdTotalCount(string TABLE_NAME,string AllSQLString)
        {
            string sql =  TABLE_NAME;
            //string sql = @" SELECT ISNULL(( select sum (spart.rows) from sys.partitions spart where spart.object_id=object_id('"+ TABLE_NAME+"') and spart.index_id < 2),0) AS [RowCount]"  ;

            try
            {
                return (int)new sqlConnection.sqlConnection().executeQueryByDataTableList(sql).Rows[0][0];
            }
            catch
            {
            }
            return -1;
        }

        public int GetTotalCount(string TABLE_NAME, string WhereString)
        {
            string sql = @"SELECT COUNT(*) FROM " + TABLE_NAME + " (NOLOCK) Where " + WhereString;

            try
            {
                return (int)new sqlConnection.sqlConnection().executeQueryByDataTableList(sql).Rows[0][0];
            }
            catch
            {
            }
            return -1;
        }




        /// <summary>
        /// 角色
        /// </summary>
        public List<Hashtable> USP_COMM_ROLE()
        {
            List<Hashtable> list_data = new List<Hashtable>();

            try
            {
                list_data = new sqlConnection.sqlConnection().executeQueryBySQLToHashtableList("SELECT ROLE_ID from HRMT24 ");
            }
            catch (Exception exception)
            {
                throw exception;
            }
            return list_data;
        }

        


    }
}
