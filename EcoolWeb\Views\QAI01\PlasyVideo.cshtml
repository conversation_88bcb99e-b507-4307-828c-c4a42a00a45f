﻿@model ECOOL_APP.EF.QAT01
@{
    ViewBag.Title = "酷課雲影片-觀看影片";
    ECOOL_APP.UserProfile user = EcoolWeb.Models.UserProfileHelper.Get();

    string UUID = EcoolWeb.Models.UserProfileHelper.GetUUID();
    if (string.IsNullOrWhiteSpace(UUID) == false)
    {
        Layout = "~/Views/Shared/_LayoutWebView.cshtml";
    }
    else
    {
        Layout = "~/Views/Shared/_LayoutEmpty.cshtml";
    }
}
<script src="https://cdn.plyr.io/1.8.8/plyr.js"></script>
<link rel="stylesheet" href="https://cdn.plyr.io/1.8.8/plyr.css">


<div id="DivView" style="max-height:95%; margin: 0px auto; ">
    @if (Path.GetExtension(Model.MEDIAS_CONTENTLINK).IndexOf("mp4") > 0)
    {
        <video poster="@Model.MEDIAS_CONTENTLINK" controls class="img-responsive">
            <source src="@Model.MEDIAS_CONTENTLINK" type="video/mp4">
            <object>
                <embed src="@Model.MEDIAS_CONTENTLINK" type="application/x-shockwave-flash" allowfullscreen="false" allowscriptaccess="always" />
            </object>
        </video>
         <script>plyr.setup();</script>
    }
    else if (Model.MEDIAS_CONTENTLINK.IndexOf("youtube") > 0)
    {


        string youtubeId = (Model.MEDIAS_CONTENTLINK.Substring(Model.MEDIAS_CONTENTLINK.LastIndexOf("=") + 1)).Trim();
        <div data-type="youtube" data-video-id="@youtubeId"></div>
        <script>plyr.setup();</script>
    }
</div>
