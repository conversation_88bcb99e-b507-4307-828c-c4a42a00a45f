/*
 *  /MathJax/jax/output/HTML-CSS/fonts/TeX/Math/Italic/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["MathJax_Math-italic"]={directory:"Math/Italic",family:"MathJax_Math",style:"italic",testString:"MathJax Math \u03A5",skew:{65:0.139,66:0.0833,67:0.0833,68:0.0556,69:0.0833,70:0.0833,71:0.0833,72:0.0556,73:0.111,74:0.167,75:0.0556,76:0.0278,77:0.0833,78:0.0833,79:0.0833,80:0.0833,81:0.0833,82:0.0833,83:0.0833,84:0.0833,85:0.0278,88:0.0833,90:0.0833,99:0.0556,100:0.167,101:0.0556,102:0.167,103:0.0278,104:-0.0278,108:0.0833,111:0.0556,112:0.0833,113:0.0833,114:0.0556,115:0.0556,116:0.0833,117:0.0278,118:0.0278,119:0.0833,120:0.0278,121:0.0556,122:0.0556,915:0.0833,916:0.167,920:0.0833,923:0.167,926:0.0833,928:0.0556,931:0.0833,933:0.0556,934:0.0833,936:0.0556,937:0.0833,945:0.0278,946:0.0833,948:0.0556,949:0.0833,950:0.0833,951:0.0556,952:0.0833,953:0.0556,956:0.0278,957:0.0278,958:0.111,959:0.0556,961:0.0833,962:0.0833,964:0.0278,965:0.0278,966:0.0833,967:0.0556,968:0.111,977:0.0833,981:0.0833,1009:0.0833,1013:0.0556},32:[0,0,250,0,0],47:[716,215,778,139,638],65:[716,0,750,35,726],66:[683,0,759,35,756],67:[705,22,715,50,760],68:[683,0,828,33,803],69:[680,0,738,31,764],70:[680,0,643,31,749],71:[705,22,786,50,760],72:[683,0,831,31,888],73:[683,0,440,26,504],74:[683,22,555,57,633],75:[683,0,849,31,889],76:[683,0,681,32,647],77:[683,0,970,35,1051],78:[683,0,803,31,888],79:[704,22,763,50,740],80:[683,0,642,33,751],81:[704,194,791,50,740],82:[683,21,759,33,755],83:[705,22,613,52,645],84:[677,0,584,21,704],85:[683,22,683,60,767],86:[683,22,583,52,769],87:[683,22,944,51,1048],88:[683,0,828,26,852],89:[683,-1,581,30,763],90:[683,0,683,58,723],97:[441,10,529,33,506],98:[694,11,429,40,422],99:[442,11,433,34,429],100:[694,10,520,33,523],101:[442,11,466,39,429],102:[705,205,490,55,550],103:[442,205,477,10,480],104:[694,11,576,48,555],105:[661,11,345,21,302],106:[661,204,412,-12,403],107:[694,11,521,48,503],108:[694,11,298,38,266],109:[442,11,878,21,857],110:[442,11,600,21,580],111:[441,11,485,34,476],112:[442,194,503,-39,497],113:[442,194,446,33,460],114:[442,11,451,21,430],115:[442,10,469,53,419],116:[626,11,361,19,330],117:[442,11,572,21,551],118:[443,11,485,21,467],119:[443,11,716,21,690],120:[442,11,572,35,522],121:[442,205,490,21,496],122:[442,11,465,35,468],160:[0,0,250,0,0],915:[680,-1,615,31,721],916:[716,0,833,48,788],920:[704,22,763,50,740],923:[716,0,694,35,670],926:[677,0,742,53,777],928:[680,0,831,31,887],931:[683,0,780,58,806],933:[705,0,583,28,700],934:[683,0,667,24,642],936:[683,0,612,21,692],937:[704,0,772,80,786],945:[442,11,640,34,603],946:[705,194,566,23,573],947:[441,216,518,11,543],948:[717,10,444,36,451],949:[452,22,466,27,428],950:[704,204,438,44,471],951:[442,216,497,21,503],952:[705,10,469,35,462],953:[442,10,354,48,332],954:[442,11,576,49,554],955:[694,12,583,47,556],956:[442,216,603,23,580],957:[442,2,494,45,530],958:[704,205,438,21,443],959:[441,11,485,34,476],960:[431,11,570,19,573],961:[442,216,517,23,510],962:[442,107,363,31,405],963:[431,11,571,31,572],964:[431,13,437,18,517],965:[443,10,540,21,523],966:[442,218,654,50,618],967:[442,204,626,25,600],968:[694,205,651,21,634],969:[443,11,622,15,604],977:[705,11,591,21,563],981:[694,205,596,43,579],982:[431,10,828,19,823],1009:[442,194,517,67,510],1013:[431,11,406,40,382]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"MathJax_Math-italic"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Math/Italic/Main.js"]);
