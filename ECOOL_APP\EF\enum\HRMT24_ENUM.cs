﻿using Dapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECOOL_APP.EF
{
    public class HRMT24_ENUM
    {
        /// <summary>
        /// 超級管理人員 ROLE_ID 0
        /// </summary>
        static public string SuperAdminROLE = "0";

        /// <summary>
        /// 可查自已學校所有資料LEVEL
        /// </summary>
        static public double QAdminLevel = 4.00;

        /// <summary>
        /// 可異動系統管理人員才可異動的資料
        /// </summary>
        static public double UpAdminLevel = 1.00;

        /// <summary>
        /// 異動他人權限  (學校管理人員以上權限)
        /// </summary>
        /// <param name="User"></param>
        /// <returns></returns>
        static public bool CheckQAdmin(UserProfile User)
        {
            if (User == null) return false;

            try
            {
                using (ECOOL_DEVEntities db = new ECOOL_DEVEntities())
                {
                    string sSQL = @"select top 1 b.ROLE_LEVEL
                from hrmt25 a (nolock)
                join hrmt24 b  (nolock) on a.ROLE_ID = b.ROLE_ID
                where 1 = 1
                and a.SCHOOL_NO = @SCHOOL_NO
                and a.USER_NO = @USER_NO
                order by b.ROLE_LEVEL ";
                    byte ROLE_LEVEL = (db.Database.Connection.Query<byte?>(sSQL
                  , new
                  {
                      SCHOOL_NO = User.SCHOOL_NO,
                      USER_NO = User.USER_NO,
                  }).FirstOrDefault() ?? byte.MaxValue);

                    var AdminList = SetAdminUpDate();

                    if (AdminList.Contains((ROLE_LEVEL)))
                    {
                        return true;
                    }
                    else
                    {
                        return false;
                    }
                }
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// 可查他校的角色類別
        /// </summary>
        static public List<byte> QOutSchoolList = SetQOutSchool();

        /// <summary>
        /// /可查他校的角色類別
        /// </summary>
        /// <param name="ROLE_TYPE"></param>
        /// <returns></returns>
        static public bool CheckQQutSchool(byte? ROLE_TYPE)
        {
            if (ROLE_TYPE == null) return false;

            if (QOutSchoolList.Contains((byte)ROLE_TYPE))
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 可查他校的角色類別
        /// </summary>
        /// <param name="User"></param>
        /// <returns></returns>
        static public bool CheckQQutSchool(UserProfile User)
        {
            if (User == null) return false;

            return CheckQQutSchool((byte)User.ROLE_TYPE);
        }

        /// <summary>
        /// 只能查本校的角色類別
        /// </summary>
        static public List<byte> QMeSchoolList = SetQMeSchool();

        /// <summary>
        /// 4. 各校管理者
        /// </summary>
        static public string ROLE_SCHOOL_ADMIN = "4";

        /// <summary>
        /// 9. 班導師
        /// </summary>
        static public string ROLE_CLASS_TEACH = "9";

        /// <summary>
        /// 12. 圖書館人員
        /// </summary>
        static public string ROLE_Library = "12";

        /// <summary>
        /// 13. 閱讀推動教師
        /// </summary>
        static public string ROLE_Read = "13";

        /// <summary>
        /// 16. 志工
        /// </summary>
        static public string Helper_User = "16";

        /// <summary>
        /// 角色類別 ROLE_TYPE
        /// </summary>
        static public class RoleTypeVal
        {
            /// <summary>
            /// Admin
            /// </summary>
            static public Byte AdminLevel = 1;

            /// <summary>
            /// 跨校級
            /// </summary>
            static public Byte ExceedLevel = 2;

            /// <summary>
            /// 校級
            /// </summary>
            static public Byte SchoolLevel = 3;

            /// <summary>
            /// 學生/家長
            /// </summary>
            static public Byte StudentLevel = 4;

            /// <summary>
            /// 訪客
            /// </summary>
            static public Byte VisitorsLevel = Byte.MaxValue;
        }

        /// <summary>
        /// 取得 角色類別名稱
        /// </summary>
        /// <param name="Val"></param>
        /// <returns></returns>
        static public string RoleTypeDesc(Byte? Val)
        {
            if (Val == RoleTypeVal.AdminLevel)
            {
                return "Admin";
            }
            else if (Val == RoleTypeVal.ExceedLevel)
            {
                return "跨校級";
            }
            else if (Val == RoleTypeVal.SchoolLevel)
            {
                return "校級";
            }
            else if (Val == RoleTypeVal.StudentLevel)
            {
                return "學生";
            }
            else
            {
                return "其他";
            }
        }

        /// <summary>
        /// 可查他校的角色類別
        /// </summary>
        /// <returns></returns>
        static private List<Byte> SetQOutSchool()
        {
            List<Byte> OutList = new List<byte>();
            OutList.Add(RoleTypeVal.AdminLevel);
            OutList.Add(RoleTypeVal.ExceedLevel);

            return OutList;
        }

        /// <summary>
        /// 只能查自已學校的角色類別
        /// </summary>
        /// <returns></returns>
        static private List<Byte> SetQMeSchool()
        {
            List<Byte> MeList = new List<byte>();
            MeList.Add(RoleTypeVal.SchoolLevel);
            MeList.Add(RoleTypeVal.StudentLevel);
            return MeList;
        }

        /// <summary>
        /// 異動他人資料權限設定 =>ROLE_LEVEL
        /// </summary>
        /// <returns></returns>
        static private List<Byte> SetAdminUpDate()
        {
            List<Byte> MeList = new List<byte>();
            MeList.Add(0);
            MeList.Add(1);
            MeList.Add(2);
            MeList.Add(4);
            return MeList;
        }

        /// <summary>
        /// 設定 閱讀推動教師和圖書館人員
        /// </summary>
        /// <returns></returns>
        static private List<string> SetOtherTeacherDate()
        {
            List<string> MeList = new List<string>();
            MeList.Add(ROLE_Library);
            MeList.Add(ROLE_Read);
            return MeList;
        }

        /// <summary>
        /// 判斷是否 閱讀推動教師和圖書館人員
        /// </summary>
        /// <param name="User"></param>
        /// <returns></returns>
        static public bool IsOtherTeacher(UserProfile User)
        {
            if (User == null) return false;

            using (ECOOL_DEVEntities db = new ECOOL_DEVEntities())
            {
                List<string> listOtherTeacher = SetOtherTeacherDate();

                var OK = db.HRMT25.Where(a => a.SCHOOL_NO == User.SCHOOL_NO && a.USER_NO == User.USER_NO && listOtherTeacher.Contains(a.ROLE_ID)).Any();
                return OK;
            }
        }

        /// <summary>
        /// 判斷是否 志工
        /// </summary>
        /// <param name="User"></param>
        /// <returns></returns>
        static public bool IshelperUser(UserProfile User)
        {
            if (User == null) return false;

            using (ECOOL_DEVEntities db = new ECOOL_DEVEntities())
            {
                var OK = db.HRMT25.Where(a => a.SCHOOL_NO == User.SCHOOL_NO && a.USER_NO == User.USER_NO && a.ROLE_ID == Helper_User).Any();
                return OK;
            }
        }
    }
}