﻿@model IPagedList<ECOOL_APP.com.ecool.Models.DTO.ZZZI14ViewModel>
<link href="~/Content/css/EzCss.css" rel="stylesheet" />
@{
    ViewBag.Title = ViewBag.Panel_Title;
}
@Html.Partial("_Title_Secondary")
@Html.Partial("_Notice")

@Html.ActionLink("新增", "Edit", new { controller = (string)ViewBag.BRE_NO }, new { @class = "btn btn-sm btn-sys" })
<br /><br />
<div class="panel panel-ACC">
    <div class="panel-heading  text-center">
        @Html.BarTitle()
    </div>
    <div class="table-responsive">
        <table class="table-ecool table-92Per table-hover table-ecool-ACC">
            <thead>
                <tr>
                    <th class="right">
                    </th>
                    <th>
                        @Html.DisplayNameFor(model => model.First().BRE_NO)
                    </th>
                    <th>
                        @Html.DisplayNameFor(model => model.First().BRE_NAME)
                    </th>
                    <th>
                        @Html.DisplayNameFor(model => model.First().CONTROLLER)
                    </th>
                    <th>
                        @Html.DisplayNameFor(model => model.First().ACTION_ID)
                    </th>
                    <th>
                        @Html.DisplayNameFor(model => model.First().ENABLE)
                    </th>
                </tr>
            </thead>
            <tbody>

                @{
                    string ClassVal;

                    foreach (var item in Model)
                    {
                        // (1.全部、2.依角色、3.標題)
                        ClassVal = item.BRE_TYPE == "3" ? "warning" : "selectable";

                        <tr class="@ClassVal">
                            <td class="right actions">
                                @Html.ActionLink("編輯", "Edit", new { controller = (string)ViewBag.BRE_NO, BRE_NO = item.BRE_NO }, new { @class = "btn btn-xs btn-Basic" })
                            </td>
                            <td>
                                @if (item.BRE_TYPE == "3")
                                {
                                    <b>
                                        @Html.DisplayFor(modelItem => item.BRE_NO)
                                    </b>
                                }
                                else
                                {
                                    @Html.DisplayFor(modelItem => item.BRE_NO)
                                }
                            </td>
                            <td>
                                @if (item.BRE_TYPE == "3")
                                {
                                    <b>
                                        @Html.DisplayFor(modelItem => item.BRE_NAME)
                                    </b>
                                }
                                else
                                {
                                    @Html.DisplayFor(modelItem => item.BRE_NAME)
                                }
                            </td>
                            <td align="center">
                                @Html.DisplayFor(modelItem => item.CONTROLLER)
                            </td>
                            <td align="center">
                                @Html.DisplayFor(modelItem => item.ACTION_ID)
                            </td>
                            <td align="center">
                                @Html.DisplayFor(modelItem => item.ENABLE)
                            </td>
                        </tr>
                    }
                }
            </tbody>
        </table>
    </div>
</div>

<div>
    @Html.Pager(Model.PageSize, Model.PageNumber, Model.TotalItemCount).Options(o => o
        .DisplayTemplate("BootstrapPagination")
             .MaxNrOfPages(5)
             .SetPreviousPageText("上頁")
             .SetNextPageText("下頁")
    )
</div>