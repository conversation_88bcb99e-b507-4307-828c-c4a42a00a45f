﻿
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.IO;
using System.Linq;
using System.Text;

namespace ECOOL_APP.EF
{
    public class AWAI01EditDataViewModel
    {
        /// <summary>
        ///獎品代號
        /// </summary>
        [DisplayName("獎品代號")]
        public int? AWARD_NO { get; set; }

        /// <summary>
        ///學校代碼
        /// </summary>
        [DisplayName("學校")]
        [Required(ErrorMessage = "此欄位必輸")]
        public string SCHOOL_NO { get; set; }

        /// <summary>
        ///獎品類型
        /// </summary>
        [DisplayName("獎品類型")]
        [Required(ErrorMessage = "此欄位必輸")]
        public string AWARD_TYPE { get; set; }

        /// <summary>
        ///獎品名稱
        /// </summary>
        [DisplayName("獎品名稱")]
        [Required(ErrorMessage = "此欄位必輸")]
        public string AWARD_NAME { get; set; }

        /// <summary>
        ///兌換點數
        /// </summary>
        [DisplayName("兌換點數")]
        [Required(ErrorMessage = "此欄位必輸")]
        public int? COST_CASH { get; set; }

        /// <summary>
        ///獎品庫存數
        /// </summary>
        [DisplayName("獎品數量")]
        [Required(ErrorMessage = "此欄位必輸")]
        public int? QTY_STORAGE { get; set; }

        /// <summary>
        ///獎品登記兌換數
        /// </summary>
        [DisplayName("獎品登記兌換數")]
        public int? QTY_TRANS { get; set; }

        /// <summary>
        ///兌換開始時間
        /// </summary>
        [DisplayName("開始兌換")]
        [Required(ErrorMessage = "此欄位必輸")]
        [DisplayFormat(DataFormatString = "{0:yyyy/MM/dd HH:mm}", ApplyFormatInEditMode = true)]
        public DateTime? SDATETIME { get; set; }

        /// <summary>
        ///截止段換時間
        /// </summary>
        [DisplayName("兌換期限")]
        [Required(ErrorMessage = "此欄位必輸")]
        [DisplayFormat(DataFormatString = "{0:yyyy/MM/dd HH:mm}", ApplyFormatInEditMode = true)]
        public DateTime? EDATETIME { get; set; }

        /// <summary>
        ///獎品描述
        /// </summary>
        [DisplayName("備註說明")]
        public string DESCRIPTION { get; set; }

        /// <summary>
        ///圖檔儲存路徑
        /// </summary>
        [DisplayName("圖檔儲存路徑")]
        public string IMG_FILE { get; set; }

        /// <summary>
        ///圖檔儲存路徑
        /// </summary>
        [DisplayName("圖檔儲存路徑")]
        public string IMG2_FILE { get; set; }

        /// <summary>
        /// YOUTUBE影片路徑
        /// </summary>
        [DisplayName("影片儲存路徑")]
        public string VIDEO_PATH { get; set; }

        /// <summary>
        ///獎品狀態
        /// </summary>
        [DisplayName("獎品狀態")]
        public string AWARD_STATUS { get; set; }

        /// <summary>
        ///熱門獎項
        /// </summary>
        [DisplayName("熱門獎項")]
        public string HOT_YN { get; set; }

        [DisplayName("兌換方式")]
        [Description("是否顯示於行動支付頁面")]
        /// <summary>
        /// Y: 限行動支付頁面  N:限一般獎品兌換  B:兩者皆可
        /// </summary>
        public string FULLSCREEN_YN { get; set; }

        [DisplayName("是否限制對象")]
        public string BUY_PERSON_YN { get; set; }
        /// <summary>
        ///限制可領取閱讀認證等級
        /// </summary>
        [DisplayName("閱讀認證")]
        public byte? READ_LEVEL { get; set; }

        /// <summary>
        ///限制可領取閱讀護照等級
        /// </summary>
        [DisplayName("閱讀護照")]
        public byte? PASSPORT_LEVEL { get; set; }

        /// <summary>
        ///限制數量(每一個可兌換的數量)
        /// </summary>
        [DisplayName("限制數量")]
        public int? QTY_LIMIT { get; set; }

        /// <summary>
        ///【備註說明】是否顯示於獎品清單中
        /// </summary>
        [DisplayName("【備註說明】是否顯示於獎品清單中")]
        public string SHOW_DESCRIPTION_YN { get; set; }

        /// <summary>
        ///是否通知
        /// </summary>
        [DisplayName("是否通知")]
        public string PUSH_YN { get; set; }

        /// <summary>
        ///起標價
        /// </summary>
        [DisplayName("起標價")]
        public int? BID_START_PRICE { get; set; }

        /// <summary>
        ///增加每單位的點數
        /// </summary>
        [DisplayName("每單位點數")]
        public int? BID_PER_UNIT_PRICE { get; set; }


        /// <summary>
        ///直購價
        /// </summary>
        [DisplayName("直購價")]
        public int? BID_BUY_PRICE { get; set; }

    }
}