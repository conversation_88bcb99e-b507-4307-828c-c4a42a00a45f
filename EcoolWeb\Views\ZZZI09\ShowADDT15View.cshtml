﻿@model ZZZI09ShowADDT15ViewViewModel

@if (TempData["StatusMessage"] != null)
{
    @Html.Partial("_Notice")
}
else
{
    <div class="Div-EZ-Pink" style="margin: 0 auto;">
        <div class="Details text-danger">
            <h4 style="margin-bottom: 0;"><label>&nbsp;校外榮譽</label></h4>
        </div>
    </div>
    if (Model != null && Model.ADDT15List != null && Model.ADDT15List.Count() > 0)
    {
        foreach (var item in Model.ADDT15List)
        {
            <div class="Div-EZ-Pink" style="margin: 0 auto;">
                <div class="Details">
                    <div class="row">
                        <div class="col-md-5 col-sm-6 dl-horizontal-EZ">
                            <samp class="dt" style="font-size:0.6cm;">
                                日期
                            </samp>
                            <samp class="dd" style="font-size:0.5cm;">
                                @Html.DisplayFor(model => item.CREATEDATE, "ShortDateTime")
                            </samp><br />
                            <samp class="dt" style="font-size:0.6cm;">
                                班級
                            </samp>
                            <samp class="dd" style="font-size:0.5cm;">
                                @Html.DisplayFor(model => item.CLASS_NO)
                            </samp><br />
                            <samp class="dt" style="font-size:0.6cm;">
                                姓名
                            </samp>
                            <samp class="dd" style="font-size:0.5cm;">
                                @Html.DisplayFor(model => item.SNAME)
                            </samp><br />
                                   <samp class="dt" style="font-size:0.6cm;">
                                       優良表現
                                   </samp>

                            <samp class="dd" style="font-size:0.5cm;">
                                @Html.DisplayFor(model => item.OAWARD_ITEM)
                            </samp><br />
                            <samp class="dt" style="font-size:0.6cm;">
                                成績
                            </samp>
                            <samp class="dd" style="font-size:0.5cm;">
                                @Html.DisplayFor(model => item.OAWARD_SCORE)
                            </samp>
                        </div>
                        <div class="col-md-5  col-sm-6 offset-md-4">

                            @if (!string.IsNullOrWhiteSpace(ViewBag.ImageUrl) && !string.IsNullOrWhiteSpace(item.IMG_FILE))
                            {
                                string ImgPath = new ECOOL_APP.FileHelper().GetDirectorySysPathImg(item.SCHOOL_NO, EcoolWeb.Controllers.ADDI07Controller.ImgPath, item.OAWARD_ID.ToString(), item.IMG_FILE);

                                //string ImgPath = ViewBag.ImageUrl + Path.Combine(item.OAWARD_ID.ToString(), item.IMG_FILE);

                                <img src="@ImgPath" href="@ImgPath" class="img-responsive " style="height:450px" />
                            }

                        </div>
                    </div>


                </div>

                <div style="height:1px;background-color:palevioletred" ;></div>
            </div>
        }
    }
    else
    {
            <div class="Div-EZ-Pink" style="margin: 0 auto;">
                <div class="Details">
                    暫時無紀錄
                </div>
            </div>
                }
                }
