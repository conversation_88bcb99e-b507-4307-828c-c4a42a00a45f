/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX-Web/Normal/BoldItalic/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXMathJax_Normal-bold-italic"]={directory:"Normal/BoldItalic",family:"STIXMathJax_Normal",weight:"bold",style:"italic",testString:"\u00A0\uD835\uDC68\uD835\uDC69\uD835\uDC6A\uD835\uDC6B\uD835\uDC6C\uD835\uDC6D\uD835\uDC6E\uD835\uDC6F\uD835\uDC70\uD835\uDC71\uD835\uDC72\uD835\uDC73\uD835\uDC74\uD835\uDC75",32:[0,0,250,0,0],160:[0,0,250,0,0],119912:[685,0,759,39,724],119913:[669,0,726,42,715],119914:[685,12,701,55,745],119915:[669,0,818,42,790],119916:[669,0,732,42,754],119917:[669,0,635,44,750],119918:[685,12,768,55,768],119919:[669,0,891,42,946],119920:[669,0,502,42,557],119921:[669,12,558,66,646],119922:[669,0,795,42,839],119923:[669,0,744,42,700],119924:[669,0,1016,42,1071],119925:[669,0,869,42,924],119926:[685,16,777,55,755],119927:[669,0,612,42,733],119928:[685,154,810,55,755],119929:[669,0,801,42,784],119930:[685,10,671,55,704],119931:[669,0,568,28,700],119932:[669,10,733,72,810],119933:[669,15,593,66,797],119934:[669,17,925,66,1129],119935:[669,0,808,28,830],119936:[669,0,549,39,725],119937:[669,0,797,66,830],119938:[462,10,581,44,548],119939:[685,8,509,50,487],119940:[462,10,477,44,460],119941:[685,14,595,44,589],119942:[462,10,498,44,459],119943:[685,207,572,44,632],119944:[462,203,527,22,527],119945:[685,10,576,50,543],119946:[620,9,357,55,300],119947:[620,207,431,-18,414],119948:[685,11,580,55,563],119949:[685,9,346,50,310],119950:[467,9,760,33,727],119951:[467,10,559,33,526],119952:[462,10,561,44,539],119953:[469,205,571,-33,554],119954:[462,205,526,44,532],119955:[467,0,441,33,424],119956:[462,11,474,55,419],119957:[592,10,351,44,318],119958:[463,10,535,33,502],119959:[473,14,554,52,539],119960:[473,14,814,52,799],119961:[462,8,587,33,543],119962:[462,205,519,35,522],119963:[462,19,531,35,499],120604:[685,0,759,39,724],120605:[669,0,726,42,715],120606:[669,0,634,42,749],120607:[685,0,632,32,589],120608:[669,0,732,42,754],120609:[669,0,797,66,830],120610:[669,0,891,42,946],120611:[685,16,783,55,755],120612:[669,0,502,42,557],120613:[669,0,795,42,839],120614:[685,0,759,39,724],120615:[669,0,1016,42,1071],120616:[669,0,869,42,924],120617:[669,0,718,57,757],120618:[685,16,777,55,755],120619:[669,0,887,39,942],120620:[669,0,612,42,733],120621:[685,16,783,55,755],120622:[669,0,759,64,787],120623:[669,0,568,28,700],120624:[685,0,641,31,784],120625:[669,0,827,28,799],120626:[669,0,808,28,830],120627:[685,0,694,30,781],120628:[685,0,826,57,815],120629:[669,16,632,43,600],120630:[461,12,624,44,630],120631:[685,205,555,28,583],120632:[462,203,490,44,503],120633:[685,8,538,44,538],120634:[462,10,495,28,451],120635:[685,203,472,44,522],120636:[462,205,517,33,511],120637:[685,11,566,44,555],120638:[462,9,318,55,274],120639:[462,0,560,55,577],120640:[685,16,570,55,537],120641:[449,205,636,33,603],120642:[459,10,523,55,534],120643:[685,203,476,28,487],120644:[462,10,561,44,539],120645:[449,13,579,39,590],120646:[462,205,595,33,562],120647:[462,203,480,39,508],120648:[449,10,592,44,603],120649:[449,7,469,33,502],120650:[462,10,552,33,535],120651:[462,205,706,55,667],120652:[462,204,621,33,676],120653:[462,205,701,33,756],120654:[462,10,687,22,665],120655:[686,10,559,44,559],120656:[461,10,481,44,481],120657:[698,13,607,33,584],120658:[462,15,607,-12,630],120659:[685,205,683,44,655],120660:[462,205,585,44,563],120661:[449,10,868,33,879]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"STIXMathJax_Normal-bold-italic"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Normal/BoldItalic/Main.js"]);
