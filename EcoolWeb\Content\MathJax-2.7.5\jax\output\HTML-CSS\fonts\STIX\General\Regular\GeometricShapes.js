/*
 *  /MathJax/jax/output/HTML-CSS/fonts/STIX/General/Regular/GeometricShapes.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.STIXGeneral,{9632:[662,158,910,45,865],9633:[662,158,910,45,865],9634:[662,158,910,45,865],9635:[662,158,910,45,865],9636:[662,158,910,45,865],9637:[662,158,910,45,865],9638:[662,158,910,45,865],9639:[662,158,910,45,865],9640:[662,158,910,45,865],9641:[662,158,910,45,865],9642:[460,-40,484,32,452],9643:[460,-40,484,32,452],9644:[469,11,1020,38,982],9645:[469,11,1020,38,982],9646:[724,220,560,40,520],9647:[724,220,560,40,520],9648:[514,11,1140,28,1112],9649:[514,11,1140,29,1111],9650:[811,127,1145,35,1110],9652:[553,-28,660,27,632],9653:[553,-28,660,27,632],9654:[790,285,1043,70,1008],9655:[791,284,1043,70,1008],9656:[556,49,660,80,605],9658:[555,50,930,65,885],9659:[555,50,930,65,885],9660:[811,127,1145,35,1110],9662:[477,48,660,27,632],9663:[477,48,660,27,632],9664:[790,285,1043,35,973],9665:[791,284,1043,70,1008],9666:[555,50,660,55,580],9668:[555,50,930,45,865],9669:[555,50,930,45,865],9670:[744,242,1064,39,1025],9671:[744,242,1064,39,1025],9672:[744,242,1064,39,1025],9673:[623,119,842,50,792],9674:[795,289,790,45,745],9675:[623,119,842,50,792],9676:[680,176,910,29,881],9677:[680,176,910,27,884],9678:[623,119,842,50,792],9679:[623,119,842,50,792],9680:[623,119,842,50,792],9681:[623,119,842,50,792],9682:[623,119,842,50,792],9683:[623,119,842,50,792],9684:[623,119,842,50,792],9685:[623,119,842,50,792],9686:[680,176,580,66,494],9687:[680,176,580,86,514],9688:[662,158,910,45,865],9689:[662,158,910,45,865],9690:[662,-252,910,45,865],9691:[252,158,910,45,865],9692:[680,-252,910,27,455],9693:[680,-252,910,455,884],9694:[252,176,910,455,884],9695:[252,176,910,26,455],9696:[680,-251,910,27,884],9697:[252,176,910,27,884],9698:[662,158,911,45,865],9699:[662,158,911,45,865],9700:[662,158,911,45,865],9701:[662,158,911,45,865],9702:[444,-59,523,70,455],9703:[662,157,910,45,865],9704:[662,157,910,45,865],9705:[662,157,910,45,865],9706:[662,157,910,45,865],9707:[662,157,910,45,865],9708:[811,127,1145,35,1110],9709:[811,127,1145,35,1110],9710:[811,127,1145,35,1110],9712:[662,158,910,45,865],9713:[662,158,910,45,865],9714:[662,158,910,45,865],9715:[662,158,910,45,865],9716:[623,119,842,50,792],9717:[623,119,842,50,792],9718:[623,119,842,50,792],9719:[623,119,842,50,792],9720:[662,158,911,45,865],9721:[662,158,911,45,865],9722:[662,158,911,45,865],9723:[580,76,746,45,701],9724:[580,76,746,45,701],9725:[513,12,601,38,563],9726:[514,11,601,38,563],9727:[662,158,911,45,865]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/Regular/GeometricShapes.js");
