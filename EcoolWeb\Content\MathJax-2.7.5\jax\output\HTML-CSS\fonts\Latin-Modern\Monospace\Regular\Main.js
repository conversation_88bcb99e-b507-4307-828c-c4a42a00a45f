/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Latin-Modern/Monospace/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.LatinModernMathJax_Monospace={directory:"Monospace/Regular",family:"LatinModernMathJax_Monospace",testString:"\u00A0\uD835\uDE70\uD835\uDE71\uD835\uDE72\uD835\uDE73\uD835\uDE74\uD835\uDE75\uD835\uDE76\uD835\uDE77\uD835\uDE78\uD835\uDE79\uD835\uDE7A\uD835\uDE7B\uD835\uDE7C\uD835\uDE7D",32:[0,0,525,0,0],160:[0,0,525,0,0],120432:[623,0,525,27,497],120433:[611,0,525,23,482],120434:[622,11,525,40,484],120435:[611,0,525,19,485],120436:[611,0,525,26,502],120437:[611,0,525,28,490],120438:[622,11,525,38,496],120439:[611,0,525,22,502],120440:[611,0,525,79,446],120441:[611,11,525,71,478],120442:[611,0,525,26,495],120443:[611,0,525,32,488],120444:[611,0,525,17,507],120445:[611,0,525,28,496],120446:[622,11,525,56,468],120447:[611,0,525,26,480],120448:[622,139,525,56,468],120449:[611,11,525,22,522],120450:[622,11,525,52,472],120451:[611,0,525,26,498],120452:[611,11,525,4,520],120453:[611,8,525,18,506],120454:[611,8,525,11,513],120455:[611,0,525,27,496],120456:[611,0,525,19,505],120457:[611,0,525,48,481],120458:[440,6,525,50,519],120459:[611,6,525,12,488],120460:[440,6,525,73,466],120461:[611,6,525,36,512],120462:[440,6,525,55,464],120463:[617,0,525,42,437],120464:[442,229,525,29,509],120465:[611,0,525,12,512],120466:[605,0,525,78,455],120467:[605,228,525,48,369],120468:[611,0,525,21,508],120469:[611,0,525,58,467],120470:[437,0,525,-4,516],120471:[437,0,525,12,512],120472:[440,6,525,57,467],120473:[437,222,525,12,488],120474:[437,222,525,40,537],120475:[437,0,525,32,487],120476:[440,6,525,72,459],120477:[554,6,525,25,449],120478:[431,6,525,12,512],120479:[431,4,525,24,500],120480:[431,4,525,16,508],120481:[431,0,525,27,496],120482:[431,228,525,26,500],120483:[431,0,525,33,475],120822:[622,11,525,50,474],120823:[622,0,525,105,442],120824:[622,0,525,52,472],120825:[622,11,525,44,480],120826:[623,0,525,29,495],120827:[611,11,525,52,472],120828:[622,11,525,53,471],120829:[627,11,525,44,480],120830:[622,11,525,44,480],120831:[622,11,525,53,471]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"LatinModernMathJax_Monospace"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Monospace/Regular/Main.js"]);
