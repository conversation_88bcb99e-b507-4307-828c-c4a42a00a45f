/*
 *  /MathJax/jax/output/HTML-CSS/fonts/TeX/Fraktur/Regular/PUA.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.MathJax_Fraktur,{58112:[683,32,497,75,430],58113:[616,30,498,35,432],58114:[680,215,333,29,339],58115:[679,224,329,28,318],58116:[471,214,503,52,449],58117:[686,20,333,26,315],58118:[577,21,334,29,347],58119:[475,22,501,10,514]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/Fraktur/Regular/PUA.js");
